<template>
  <div class="tax-mapping-container">
    <LMTable
      row-key="index"
      :data="tableData"
      :columns="columns"></LMTable>
  </div>
</template>

<script setup lang="ts">
  // 定义表格数据
  const tableData = ref([
    // 第一列数据
    {
      currentType: "数电票（增值税专用发票）",
      currentMapping: "专用发票",
      targetType: "数电票（增值税专用发票）",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "增值税专用发票",
      currentMapping: "专用发票",
      targetType: "增值税专用发票",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "数电票（普通发票）",
      currentMapping: "普通发票",
      targetType: "数电票（机动车销售统一发票）",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "增值税普通发票",
      currentMapping: "普通发票",
      targetType: "机动车销售统一发票",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "数电票（机动车销售统一发票）",
      currentMapping: "机动车发票",
      targetType: "道路通行费电子普通发票",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "数电票（二手车销售统一发票）",
      currentMapping: "机动车发票",
      targetType: "数电票（通行费发票）",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "机动车销售统一发票",
      currentMapping: "机动车发票",
      targetType: "数电票（航空运输电子客票行程单）",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "二手车销售统一发票",
      currentMapping: "机动车发票",
      targetType: "数电票（铁路电子客票）",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "道路通行费电子普通发票",
      currentMapping: "普通发票",
      targetType: "数电票（普通发票）",
      targetMapping: "其他（8b）",
    },
    {
      currentType: "数电票（通行费发票）",
      currentMapping: "普通发票",
      targetType: "增值税电子普通发票",
      targetMapping: "其他（8b）",
    },
    {
      currentType: "数电票（航空运输电子客票行程单）",
      currentMapping: "其他",
      targetType: "增值税普通发票",
      targetMapping: "其他（8b）",
    },
    {
      currentType: "数电票（铁路电子客票）",
      currentMapping: "其他",
      targetType: "税控机动车专用发票",
      targetMapping: "认证相符的增值税专用发票",
    },
    {
      currentType: "无票收入（未开具发票）",
      currentMapping: "其他",
      targetType: "",
      targetMapping: "",
    },
    {
      currentType: "纳税检查调整",
      currentMapping: "其他",
      targetType: "",
      targetMapping: "",
    },
    {
      currentType: "其他发票",
      currentMapping: "其他",
      targetType: "",
      targetMapping: "",
    },
  ])

  // 定义表格列
  const columns = ref([
    {
      label: "现有销项发票种类",
      prop: "currentType",
      minWidth: 200,
      align: "left",
      headerAlign: "center",
    },
    {
      label: "映射票种种类",
      prop: "currentMapping",
      minWidth: 150,
      align: "left",
      headerAlign: "center",
    },
    {
      label: "现有进项抵扣能抵扣发票种类",
      prop: "targetType",
      minWidth: 200,
      align: "left",
      headerAlign: "center",
    },
    {
      label: "映射票种种类",
      prop: "targetMapping",
      minWidth: 200,
      align: "left",
      headerAlign: "center",
    },
  ])
</script>

<style lang="scss" scoped>
  .tax-mapping-container {
    padding: 20px;

    :deep(.el-table) {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: bold;
      }

      .el-table__row {
        height: 40px;
      }
    }
  }
</style>
