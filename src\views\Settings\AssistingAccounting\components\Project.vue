<template>
    <searchView
        @handle-new="handleNew"
        @handle-search="btnSearch"
        @handle-clear="handleClear"
        @handle-export="handleExport"
        @handle-import="handleImport"
    />
    <div class="main-center">
        <Table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :page-is-show="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :scrollbar-show="true"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="90px" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccount-canedit']" class="link" @click="handleEdit(scope.row.aaeId)"> 编辑 </a>
                            <a v-permission="['assistingaccount-candelete']" class="link" @click="handleDelete(scope.row.aaeId)"> 删除 </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { deleteHandle, clearHanele, exportHandle, getModelApi, formatDate } from "../utils";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import searchView from "./SearchBox.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "SetAssisting";
interface ITableItem {
    department: string;
    owner: string;
    mobilePhone: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
interface IEditModelItem {
    asId: number;
    aaeId: number;
    department: string;
    owner: string;
    mobilePhone: string;
    startDate: string;
    endDate: string;
    note: string;
    departmentId: number;
    ownerId: number;
    aaNum: string;
    aaName: string;
    status: number;
    uscc: string;
}

const isErp = ref(window.isErp);

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const columns: IColumnProps[] = [
    { label: "项目编码", prop: "aaNum", minWidth: 120, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaNum") },
    { label: "项目名称", prop: "aaName", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaName") },
    { label: "助记码", prop: "aaAcronym", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaAcronym") },
    { label: "负责部门", prop: "department", minWidth: 90, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "department") },
    { label: "责任人", prop: "owner", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "owner") },
    { label: "手机", prop: "mobilePhone", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "mobilePhone") },
    { label: "开始日期", prop: "startDate", minWidth: 90, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "startDate") },
    { label: "结束日期", prop: "endDate", minWidth: 90, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "endDate") },
    { label: "备注", prop: "note", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "note") },
    { slot: "operator" },
];

if (isErp.value) {
    columns[7].label = "验收日期";
}

const loading = ref(false);
const tableData = ref<ITableItem[]>([]);
const emit = defineEmits(["handleNew", "handleEdit", "handleImport", "handleCancel"]);

const searchStr = ref("");
const btnSearch = (searchVal: string) => {
    searchStr.value = searchVal;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleSearch = (successBack?: Function) => {
    loading.value = true;
    const params = {
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        searchStr: searchStr.value,
    };
    request({ url: "/api/AssistingAccounting/PagingProjectList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                successBack && successBack();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleNew = () => {
    emit("handleNew", 10005);
};
const handleEdit = (aaeID: string | number) => {
    getModelApi(aaeID, 10005).then((res: IResponseModel<IEditModelItem>) => {
        if (res.state == 1000) {
            const data = res.data;
            const params = {
                aaNum: data.aaNum,
                aaName: data.aaName,
                aaeID: data.aaeId,
                department: isErp.value ? data.departmentId : data.department,
                owner: isErp.value ? data.ownerId : data.owner,
                mobilePhone: data.mobilePhone,
                startDate: data.startDate,
                endDate: data.endDate,
                note: data.note,
                status: data.status == 0,
            };
            emit("handleEdit", 10005, params);
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const isThisPageHasData = (type: "delete" | "clear") => {
    return function () {
        if (type === "delete") {
            if (paginationData.currentPage !== 1 && tableData.value.length === 1) {
                paginationData.currentPage--;
            } else {
                handleCancel();
            }
        } else {
            if (paginationData.currentPage !== 1) {
                paginationData.currentPage = 1;
            } else {
                paginationData.currentPage = 1;
                handleCancel();
            }
        }
    };
};
const handleCancel = () => emit("handleCancel");
const handleDelete = (aaeID: number) => deleteHandle(10005, aaeID, isThisPageHasData("delete"));
const handleClear = () => clearHanele(tableData.value.length, 10005, isThisPageHasData("clear"));
const handleExport = () => exportHandle(10005, paginationData.total);
const handleImport = () => emit("handleImport", 10005);

defineExpose({ handleSearch });

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});
</script>

<style lang="less" scoped></style>
