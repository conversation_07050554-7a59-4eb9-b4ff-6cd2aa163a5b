@--orenge: #fba285;
@--dark-orenge: #dd7350;
@--purple: #b69ce7;
@--dark-purple: #9471d6;
@--blue: #61b7f2;
@--dark-blue: #51a8e3;
.content {
    .title-info {
        margin: 0 auto;
        padding: 12px 0;
        width: 1100px;
        text-align: left;
        .link-content {
            display: inline;
            .link-icon {
                width: 16px;
                vertical-align: -4px;
            }
            span {
                font-size: var(--h5);
                line-height: 16px;
                color: var(--font-color);
                margin: 0 5px;
            }
            .link {
                font-size: var(--h5);
                line-height: 16px;
                position: relative;
                z-index: 10;
                .link-qrcode {
                    visibility: hidden;
                    opacity: 0;
                    transition: var(--transition-time);
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%, 0);
                    top: 16px;
                    padding-top: 5px;
                }
                &:hover {
                    .link-qrcode {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }
        .wxwork-choosespace-btn {
            width: 106px;
            height: 20px;
            background: #0d8fe6;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: var(--h5);
            color: var(--white);
            line-height: 17px;
            .icon {
                width: 16px;
                height: 14px;
                margin-right: 4px;
            }
        }
    }
    .main-content {
        width: 1100px;
        padding: 20px;
        margin: auto;
        background-color: var(--white);
        box-sizing: border-box;
        position: relative;
    }
}
.top-content {
    height: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > div {
        display: inline-block;
        position: relative;
        height: 98px;
        width: 248px;
        border-radius: 6px;
        cursor: pointer;
        border: 1px solid var(--border-color);
        background-color: transparent;
        &:hover {
            box-shadow: 0 32px 32px 0 rgba(234, 239, 243, 0.3), 0 16px 16px 0 rgba(215, 217, 219, 0.05),
                0 8px 8px 0 rgba(234, 239, 243, 0.05), 0 4px 4px 0 rgba(64, 97, 121, 0.05), 0 2px 2px 0 rgba(173, 187, 200, 0.19);
            .title-item-top {
                color: var(--white);
            }
            &.item-voucher {
                .title-item-top {
                    background-color: var(--main-color);
                }
            }
            &.item-trialbalance {
                .title-item-top {
                    background-color: @--orenge;
                }
            }
            &.item-balancesheet {
                .title-item-top {
                    background-color: @--purple;
                }
            }
            &.item-incomestatement,
            &.item-businessactivitystatement,
            &.item-surplusdistributionstatement,
            &.item-incomeandexpendituresheet,
            &.item-incomeanddistributionsheet {
                .title-item-top {
                    background-color: @--blue;
                }
            }
        }
        &.item-voucher {
            .title-item-bottom {
                background-color: var(--main-color);
            }
        }
        &.item-trialbalance {
            .title-item-bottom {
                background-color: @--orenge;
            }
        }
        &.item-balancesheet {
            .title-item-bottom {
                background-color: @--purple;
            }
        }
        &.item-incomestatement,
        &.item-businessactivitystatement,
        &.item-surplusdistributionstatement,
        &.item-incomeandexpendituresheet,
        &.item-incomeanddistributionsheet {
            .title-item-bottom {
                background-color: @--blue;
            }
        }
        .title-item-top {
            text-align: center;
            color: var(--font-color);
            font-size: var(--h2);
            line-height: 98px;
            transition: var(--transition-time);
            border-radius: 6px;
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
        }
        .title-item-bottom {
            position: absolute;
            bottom: -1px;
            left: -1px;
            right: -1px;
            height: 6px;
            border-radius: 0 0 6px 6px;
        }
    }
}
.line {
    height: 1px;
    background-color: var(--border-color);
}
.center-content {
    height: 405px;
    margin-top: 18px;
    text-align: left;
    display: flex;
    align-items: center;
    .center-content-left {
        display: inline-block;
        width: 518px;
        height: 403px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        .center-content-left-top {
            height: 41px;
        }
        .center-content-left-bottom {
            text-align: center;
            .cross {
                display: inline-block;
                position: relative;
                height: 118px;
                width: 118px;
                margin-top: 89px;
                border: 1px solid var(--main-color);
                border-radius: 8px;
                cursor: pointer;
                transition: var(--transition-time);
                &:hover {
                    background-color: var(--main-color);
                    .cross-c,
                    .cross-r {
                        background-color: var(--white);
                    }
                }
                .cross-c,
                .cross-r {
                    position: absolute;
                    background-color: var(--main-color);
                    border-radius: 8px;
                    transition: var(--transition-time);
                }
                .cross-c {
                    height: 72px;
                    width: 12px;
                    top: 24px;
                    left: 54px;
                }
                .cross-r {
                    height: 12px;
                    width: 72px;
                    top: 54px;
                    left: 24px;
                }
            }
            .cross-text {
                margin-top: 10px;
                color: var(--font-color);
                font-size: var(--h2);
                line-height: 25px;
            }
            .bottom-desc {
                margin-top: 85px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
            }
        }
    }
    .center-content-right {
        margin-left: 20px;
        display: inline-block;
        width: 518px;
        height: 403px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        .center-content-rigth-top {
            margin-top: 20px;
            padding: 0 18px 0 20px;
            span {
                display: inline-block;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 25px;
            }
            .center-content-rigth-top-title {
                font-size: 18px;
            }
            .center-content-rigth-top-date {
                float: right;
                line-height: 25px;
                * {
                    margin-top: 2.5px;
                    float: left;
                }
                .img {
                    height: 20px;
                    width: 20px;
                    display: inline-block;
                    background-repeat: no-repeat;
                }
                span {
                    margin: 2.5px 10px 0;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                }
                .goto-left-normal {
                    cursor: pointer;
                    background-image: url("@/assets/Default/zuobian.png");
                    &:hover {
                        background-image: url("@/assets/Default/zuobian_hover.png");
                    }
                }
                .goto-left-disable {
                    background-image: url("@/assets/Default/zuobian_disable.png");
                }
                .goto-right-normal {
                    cursor: pointer;
                    background-image: url("@/assets/Default/youbian.png");
                    &:hover {
                        background-image: url("@/assets/Default/youbian_hover.png");
                    }
                }
                .goto-right-disable {
                    background-image: url("@/assets/Default/youbian_disable.png");
                }
            }
        }
        .center-content-rigth-unit {
            padding-right: 24px;
            text-align: right;
            margin-top: 8px;
            color: #929292;
            font-size: var(--h5);
            line-height: 17px;
        }
        .center-content-center1 {
            height: 50px;
            padding: 0 20px;
            margin-top: 27px;
            margin-bottom: 44px;
            visibility: visible;
            opacity: 1;
            transition: var(--transition-time);
            .center-content-center-item {
                width: 20%;
                float: left;
                text-align: center;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                .center-content-center-item-bottom {
                    margin-top: 10px;
                }
            }
        }
        .center-content-center2 {
            height: 160px;
            position: relative;
            .center-content-center2-item {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                height: 50px;
                padding: 0 20px;
                margin-top: 60px;
                margin-bottom: 50px;
                visibility: visible;
                opacity: 1;
                transition: var(--transition-time);
                .center-content-center-item {
                    width: 20%;
                    float: left;
                    text-align: center;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    .important {
                        margin-top: -17px;
                        color: var(--font-color);
                        font-size: 26px;
                        line-height: 37px;
                    }
                    .center-content-center-item-bottom {
                        margin-top: 10px;
                    }
                }
            }
        }
        .center-content-buttons {
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            .button1,
            .button2 {
                display: inline-block;
                width: 100px;
                height: 30px;
                text-align: center;
                border: 1px solid var(--main-color);
                background-color: var(--white);
                cursor: pointer;
                color: var(--main-color);
                font-size: var(--h5);
                line-height: 30px;
                transition: var(--transition-time);
                &.hover {
                    color: var(--white);
                    background-color: var(--main-color);
                }
                &:hover {
                    color: var(--white);
                    background-color: var(--main-color);
                }
            }
            .button1 {
                border-radius: 4px 0 0 4px;
            }
            .button2 {
                border-radius: 0 4px 4px 0;
            }
        }
        &.folk {
            .center-content-center1 {
                .center-content-center-item {
                    width: 25%;
                    float: left;
                    text-align: center;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                }
            }
            .center-content-center2 {
                .center-content-center2-item {
                    .center-content-center-item {
                        width: 25%;
                        float: left;
                        text-align: center;
                        color: var(--font-color);
                        font-size: var(--font-size);
                        line-height: var(--line-height);
                    }
                }
            }
        }
    }
}
.bottom-ad-content {
    width: 100%;
    margin-top: 15px;
    text-align: center;
    img {
        width: 100%;
        cursor: pointer;
    }
}
