<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <a v-permission="['asyncfile-candelete']" class="button solid-button large-1" @click="deleteFiles">删除</a>
        </div>
        <div class="main-tool-right">
            <RefreshButton></RefreshButton>
        </div>
    </div>
    <div class="main-center">
        <Table
            empty-text="暂无数据"
            :data="divAsyncFileInfoDAata"
            :columns="divAsyncFileInfoColumns()"
            :rowClassName="judgeRowClassName"
            @selection-change="handleSelectionChange"
            min-height="400"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="137" align="left" header-align="center" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.progress === -1" class="error-row">
                            <a class="link-error" v-permission="['asyncfile-candelete']" @click="deleteFileItem(scope.row.fileName)">删除</a>
                            <span class="ml-10" v-permission="['asyncfile-canview']">
                                <el-popover
                                    placement="bottom-end"
                                    :width="200"
                                    trigger="hover"
                                    popper-class="tip-popover"
                                    :popper-style="popperStyle"
                                >
                                    <template #reference>
                                        <span style="color: #ff7500">
                                            <span>{{scope.row.fileSourceTypeName}}失败 </span>
                                            <img
                                                src="@/assets/Settings/warnning-orange.png"
                                                alt=""
                                                style="width: 15px; vertical-align: top; margin-top: 1px"
                                            />
                                        </span>
                                    </template>
                                    网络异常，请删除重新{{scope.row.fileSourceTypeName}}
                                </el-popover>
                            </span>
                        </span>
                        <span v-show="scope.row.progress === 100">
                            <a class="link" v-permission="['asyncfile-candelete']" @click="deleteFileItem(scope.row.fileName)">删除</a>
                            <a class="link" v-permission="['asyncfile-canview']" v-if="scope.row.expiredDate">
                                <el-popover
                                    placement="bottom-end"
                                    :width="300"
                                    trigger="hover"
                                    popper-class="tip-popover"
                                    :popper-style="popperStyle"
                                >
                                    <template #reference>
                                        <span>
                                            <span @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)">下载</span>
                                            <img
                                                src="@/assets/Settings/warnning-orange.png"
                                                alt=""
                                                style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                            />
                                        </span>
                                    </template>
                                    空间不足！已为您提供额外存储空间备份， <br />
                                    请于72小时内下载到本地，超时将自动删除
                                </el-popover>
                            </a>
                            <a
                                class="link"
                                v-permission="['asyncfile-canview']"
                                @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)"
                                v-else
                            >
                                <span>下载</span>
                            </a>
                            <a
                                class="link"
                                v-permission="['asyncfile-canview']"
                                v-show="/\.pdf$/.test(scope.row.fileName)"
                                @click="printitem(scope.row.fileName)"
                            >
                                <span>打印</span>
                            </a>
                        </span>
                        <span v-show="scope.row.progress !== 100">
                            <span v-show="scope.row.progress > 0" style="display: flex; align-items: center">
                                <span class="progress-bar">
                                    <span class="progress-solid-bar" :style="{ width: scope.row.progress + '%' }"></span>
                                </span>
                                <span>{{ scope.row.progress + "%" }}</span>
                            </span>
                            <span class="loading-operator" v-show="scope.row.progress === 0">
                                <span class="loading-icon"></span>
                                <span class="link" style="margin-left: 3px">数据加载中...</span>
                            </span>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { divAsyncFileInfoColumns, judgeRowClassName, popperStyle } from "../utils";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { globalPrint } from "@/util/url";

import type { ITableData } from "../types";

import Table from "@/components/Table/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getGlobalToken } from "@/util/baseInfo";

const setModule = "DivAsyncFile";
const props = defineProps<{
    tableData: ITableData[];
}>();
const divAsyncFileInfoDAata = computed(() => props.tableData);
const multipleSelection = ref<ITableData[]>([]);

const emit = defineEmits(["deleteFileItem", "downloadbackupitem", "batch-delete-success"]);
const handleSelectionChange = (val: any) => (multipleSelection.value = val);
const deleteFileItem = (fileName: string) => emit("deleteFileItem", fileName, "file");
const downloadbackupitem = (fileName: string, fileType: number) => emit("downloadbackupitem", fileName, fileType);

const deleteFiles = () => {
    const rows = multipleSelection.value;
    if (rows.length == 0) {
        ElNotify({ type: "warning", message: "请选择文件" });
        return;
    }
    const filenames = rows.map((item) => item.fileName);
    ElConfirm("确定删除吗？").then((r: boolean) => {
        if (r) {
            request({
                url: "/api/Backup/ProcessBatchdelete",
                method: "post",
                data: filenames,
            }).then((res: IResponseModel<string>) => {
                res.state == 1000 ? emit("batch-delete-success") : ElNotify({ type: "warning", message: res.msg || "删除失败" });
            });
        }
    });
};

const printitem = (filename: string) => {
    const url = window.jLmDiskHost + "/Services/Backup/PrintFile.ashx?filename=" + encodeURIComponent(filename)+ "&appasid=" + getGlobalToken() + "&r=" + Math.random();
    globalPrint(url);
};
</script>

<style lang="less" scoped>
@import "@/style/Settings/Backup.less";
</style>
