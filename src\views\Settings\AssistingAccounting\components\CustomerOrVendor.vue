<template>
    <searchView
        @handle-new="handleNew"
        @handle-search="btnSearch"
        @handle-clear="handleClear"
        @handle-export="handleExport"
        @handle-import="handleImport"
    />
    <div class="main-center">
        <Table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :page-is-show="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :scrollbar-show="true"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="90px" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccount-canedit']" class="link" @click="handleEdit(scope.row.aaeId)"> 编辑 </a>
                            <a v-permission="['assistingaccount-candelete']" class="link" @click="handleDelete(scope.row.aaeId)"> 删除 </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>
<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { deleteHandle, clearHanele, exportHandle, getModelApi } from "../utils";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";

import searchView from "./SearchBox.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AssitCusorVend";
interface ITableItem {
    type: string;
    address: string;
    contact: string;
    mobilePhone: string;
    taxNumber: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
interface IEditModelItem {
    asId: number;
    aaeId: number;
    type: string;
    adLevel0: number;
    adLevel1: number;
    adLevel2: number;
    adLevel3: number;
    address: string;
    contact: string;
    mobilePhone: string;
    taxNumber: string;
    note: string;
    aaNum: string;
    aaName: string;
    status: number;
    uscc: string;
}

const props = defineProps<{ aaType: number }>();
const name = computed(() => (props.aaType == 10001 ? "客户" : props.aaType == 10002 ? "供应商" : ""));

const columns: IColumnProps[] = [
    { label: name.value + "编码", prop: "aaNum", minWidth: 90, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaNum") },
    { label: name.value + "名称", prop: "aaName", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaName") },
    { label: "助记码", prop: "aaAcronym", minWidth: 90, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaAcronym") },
    { label: name.value + "类别", prop: "type", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "type") },
    { label: "经营地址", prop: "address", minWidth: 150, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "address") },
    { label: "联系人", prop: "contact", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "contact") },
    { label: "手机", prop: "mobilePhone", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "mobilePhone") },
    { label: "税号", prop: "taxNumber", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "taxNumber") },
    { label: "备注", prop: "note", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "note") },
    { slot: "operator" },
];

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const loading = ref(false);
const tableData = ref<ITableItem[]>([]);
const emit = defineEmits(["handleNew", "handleEdit", "handleImport", "handleCancel"]);

const searchStr = ref("");
const btnSearch = (searchVal: string) => {
    searchStr.value = searchVal;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleSearch = (successBack?: Function) => {
    loading.value = true;
    const params = {
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        searchStr: searchStr.value,
    };
    const urlPath = props.aaType == 10001 ? "PagingCustomerList?" : props.aaType == 10002 ? "PagingVendorList?" : "?";
    request({ url: "/api/AssistingAccounting/" + urlPath + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                successBack && successBack();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleNew = () => {
    emit("handleNew", props.aaType);
};
const handleEdit = (aaeID: number) => {
    getModelApi(aaeID, props.aaType).then((res: IResponseModel<IEditModelItem>) => {
        if (res.state == 1000) {
            const data = res.data;
            const params = {
                aaNum: data.aaNum,
                aaName: data.aaName,
                aaeID: data.aaeId,
                status: data.status == 0,
                adLevel0: data.adLevel0,
                adLevel1: data.adLevel1,
                adLevel2: data.adLevel2,
                adLevel3: data.adLevel3,
                address: data.address,
                contact: data.contact,
                mobilePhone: data.mobilePhone,
                note: data.note,
                taxNumber: data.taxNumber,
                type: data.type,
                USCC: data.uscc,
            };
            emit("handleEdit", props.aaType, params);
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const isThisPageHasData = (type: "delete" | "clear") => {
    return function () {
        if (type === "delete") {
            if (paginationData.currentPage !== 1 && tableData.value.length === 1) {
                paginationData.currentPage--;
            } else {
                handleCancel();
            }
        } else {
            if (paginationData.currentPage !== 1) {
                paginationData.currentPage = 1;
            } else {
                paginationData.currentPage = 1;
                handleCancel();
            }
        }
    };
};
const handleCancel = () => emit("handleCancel");
const handleDelete = (aaeID: number) => deleteHandle(props.aaType, aaeID, isThisPageHasData("delete"));
const handleClear = () => clearHanele(tableData.value.length, props.aaType, isThisPageHasData("clear"));
const handleExport = () => exportHandle(props.aaType, paginationData.total);
const handleImport = () => emit("handleImport", props.aaType);

defineExpose({ handleSearch });

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});
</script>
<style lang="less" scoped></style>
