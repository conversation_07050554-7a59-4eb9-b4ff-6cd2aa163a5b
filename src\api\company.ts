import { Jrequest } from "@/utils/service"
export const getCompanyListApi = (module: number, companyName: string) => {
  return Jrequest({
    url: "/api/Company/List",
    params: {
      module,
      companyName,
    },
  })
}
export const getCompanyDetailApi = (module: number, companyName: string) => {
  return Jrequest({
    url: "/api/Company",
    params: {
      module,
      companyName,
    },
  })
}

export interface ICompanyList {
  isFromDb: boolean
  companyInfos: ICompanyInfo[]
}

export interface ICompanyInfo {
  name: string
  creditCode: string
}
