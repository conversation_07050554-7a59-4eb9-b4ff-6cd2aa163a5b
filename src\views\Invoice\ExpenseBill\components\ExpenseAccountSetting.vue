<template>
    <div class="expense-account-setting-content">
        <div class="slot-title no-border">费用科目设置</div>
        <el-tabs v-model="category">
            <el-tab-pane label="费用类型" name="billTypes"> </el-tab-pane>
            <el-tab-pane label="结算方式" name="payMethods"> </el-tab-pane>
        </el-tabs>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div></div>
                <div class="main-tool-right">
                    <a v-permission="['expensebillsettings-canedit']" class="solid-button float-r" @click="showEditDialog('添加')"
                        >新建</a
                    >
                    <a class="button ml-10 float-r" @click="goBackAdd">返回</a>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :data="basicTypeList[category]"
                    :columns="columns"
                    max-height="calc(100vh - 268px)"
                    style="width: 100%"
                    v-loading="loading"
                    :tableName="setModule"
                >
                    <template #default>
                        <el-table-column 
                            label="默认结算方式" 
                            align="left" 
                            min-width="40"
                            prop="default"
                            :width="getColumnWidth(setModule, 'default')"
                        >
                            <template #default="scope">
                                <el-switch
                                    v-model="scope.row.isDefault"
                                    active-color="#33C759"
                                    inactive-color="#D6D6DA"
                                    :active-value="1"
                                    :inactive-value="0"
                                    :disabled="!checkPermission(['expensebillsettings-canedit']) || scope.row.isDefault"
                                    @change="changeDefault(scope.row)"
                                ></el-switch>
                            </template>
                        </el-table-column>
                    </template>
                    <template #operation>
                        <el-table-column label="操作" align="left" min-width="80" :resizable="false">
                            <template #default="scope">
                                <span v-if="category === 'billTypes' || ![1, 2].includes(scope.row.pmId)">
                                    <a
                                        class="link"
                                        @click.stop="showEditDialog('修改', scope.row)"
                                        v-if="checkPermission(['expensebillsettings-canedit'])"
                                    >
                                        修改
                                    </a>
                                    <a
                                        class="link"
                                        v-if="checkPermission(['expensebillsettings-candelete'])"
                                        @click.stop="showEditDialog('删除', scope.row)"
                                    >
                                        删除
                                    </a>
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
        <AddBillTypeOrPayMethodDialog
            ref="AddBillTypeOrPayMethodDialogRef"
            :billData="tableData"
            :editDialogType="editDialogType"
            @reloadBillTypeList="emits('reloadBillTypeList')"
            @reloadTableData="emits('reloadTableData')"
        ></AddBillTypeOrPayMethodDialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, type ComputedRef } from "vue";
import Table from "@/components/Table/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { checkPermission } from "@/util/permission";
import AddBillTypeOrPayMethodDialog from "./AddBillTypeOrPayMethodDialog.vue";
import type { BasicTypeList, IBillTypeItem, IPayMethodItem } from "../types";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "InvoiceExpenseSetting"
const props = defineProps<{
    basicTypeList: BasicTypeList;
}>();
const emits = defineEmits(["goMain", "reloadBillTypeList", "reloadTableData"]);
let category = ref<"billTypes" | "payMethods">("billTypes");
const AddBillTypeOrPayMethodDialogRef = ref<InstanceType<typeof AddBillTypeOrPayMethodDialog>>();
let columns: ComputedRef<IColumnProps[]> = computed(() => {
    return category.value === "billTypes"
        ? [
              { 
                label: "编码", 
                prop: "billTypeNo", 
                align: "left", 
                headerAlign: "left", 
                minWidth: 50,
                width: getColumnWidth(setModule, 'billTypeNo'),
            },
              { 
                label: "费用类型名称", 
                prop: "billTypeName", 
                align: "left", 
                headerAlign: "left", 
                minWidth: 80,
                width: getColumnWidth(setModule, 'billTypeName'),
            },
            {
                label: "对应费用科目",
                prop: "asubName",
                align: "left",
                headerAlign: "left",
                minWidth: 80,
                width: getColumnWidth(setModule, 'asubName'),
                formatter(row) {
                return row.asubCode + " " + row.asubName;
                  },
            },
            { slot: "operation" },
        ]
        : [
              { 
                label: "编码", 
                prop: "pmNo", 
                align: "left", 
                headerAlign: "left", 
                minWidth: 50,
                width: getColumnWidth(setModule, 'billTypeNo'), 
            },
            { 
                label: "结算方式", 
                prop: "pmName", 
                align: "left", 
                headerAlign: "left", 
                minWidth: 80,
                width: getColumnWidth(setModule, 'pmName'), 
            },
            {
                label: "对应会计科目",
                prop: "asubName",
                align: "left",
                headerAlign: "left",
                minWidth: 80,
                width: getColumnWidth(setModule, 'asubName'),
                formatter(row) {
                    return row.asubCode + " " + row.asubName;
                },
            },
            {
                label: "抵扣税额对应科目",
                prop: "taxAsubName",
                align: "left",
                headerAlign: "left",
                minWidth: 80,
                width: getColumnWidth(setModule, 'taxAsubName'),
                formatter(row) {
                    return row.taxAsubCode + " " + row.taxAsubName;
                },
            },
            { slot: "default" },
            { slot: "operation" },
        ];
});

let tableData = computed((): BasicTypeList => {
    return props.basicTypeList;
});
let loading = ref(false);
const editDialogType = ref("");
const showEditDialog = (type: string, row?: IPayMethodItem | IBillTypeItem) => {
    editDialogType.value = type;
    AddBillTypeOrPayMethodDialogRef.value?.initForm(row, category.value);
};
const changeDefault = (row: IPayMethodItem) => {
    request({
        url: "/api/ExpenseBill/SetDefaultPaymentMethod",
        method: "put",
        data: {
            pmid: row.pmId,
            isdefault: row.isDefault,
        },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            ElNotify({
                type: "success",
                message: "设置成功",
            });
            emits("reloadTableData");
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
        emits("reloadBillTypeList");
    });
};
const goBackAdd = () => {
    emits("goMain");
    category.value = "billTypes";
};
</script>

<style lang="less">
.expense-account-setting-content {
    width: 100%;
    .el-tabs {
        .el-tabs__nav-scroll {
            overflow: hidden;
            width: var(--content-width);
            margin: 0 auto;
        }
    }
    .main-content {
        width: var(--content-width);
        margin: 0 auto;

        .main-top {
            padding: 10px 20px;
        }
        .main-center {
            padding: 0 20px 20px;
        }
    }
}
</style>
