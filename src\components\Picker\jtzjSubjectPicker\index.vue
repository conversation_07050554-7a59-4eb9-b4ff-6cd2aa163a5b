<script lang="ts" setup>
import ToolTip from "@/components/Tooltip/index.vue";
import { ElMessage, ElPopover, ElSelect, ElTooltip } from "element-plus";
import SubjectDialog from "./asubDialog.vue";
import { computed, provide, watch, ref, nextTick } from "vue";
import { updateAsub<PERSON>ode<PERSON><PERSON>, popoverHandleClose<PERSON>ey } from "./symbols";
import { getAllAsubApi, type IAccountSubjectModel } from "@/api/accountSubject";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { AccountSubjectType } from "@/store/modules/accountSet";

const props = defineProps({
    modelValue: { type: String, required: true },
    isById: { type: Boolean, default: false },
    isToBody: { type: Boolean, default: false },
    diyWidth: { type: String, default: "298px" },
    isByNumberId: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    selectShow: { type: Boolean, default: true },
    highLight: { type: Boolean, default: false },
    asubImgRight: { type: String, default: "18px" },
    checkValid: { type: Boolean, default: false },
    dialogShow: { type: Boolean, default: false },
    btntxt: { type: String, default: "返回" },
    showTips: { type: Boolean, default: false },
    checkFA: { type: Boolean, default: false },
    isUnionSalary: { type: Boolean, default: false },
});
const emit = defineEmits<{
    (e: "update:modelValue", value: string): void;
    (e: "update:innerAsubName", value: string): void;
    (e: "inputKeyDown"): void;
}>();
const subjectData = ref<Array<IAccountSubjectModel>>([]);

const tabs = computed({
    get: () => {
        return subjectData.value.find((item: any) => {
            return item.asubId == props.modelValue;
        })?.asubType;
    },
    set: (val) => val
});
const innerAsubCode = computed({
    get() {
        return props.modelValue;
    },
    set(value: string) {
        emit("update:modelValue", value);
    },
});
const innerAsubName = ref("");

const asubId = ref(0);
const asubName = ref("");
watch(innerAsubCode, (value: string) => {
    for (let i = 0; i < subjectData.value.length; i++) {
        const item = subjectData.value[i];
        if (props.isById) {
            if (item.asubId + "" === value) {
                asubId.value = item.asubId - 0;
                asubName.value = getSelectLabel(item);
                return;
            }
        } else {
            if (item.asubCode === value) {
                asubName.value = getSelectLabel(item);
                asubId.value = item.asubId - 0;
                return;
            }
        }
    }
    asubName.value = "";
    asubId.value = 0;
});

function getAsubList(){
    getAllAsubApi()
    .then((res) => {
        res.state == 1000 ? (subjectData.value = res.data) : ElMessage.error(res.msg);
        if (props.isUnionSalary) {
            tabs.value = AccountSubjectType.Disbursement;
        } else {
            tabs.value = subjectData.value.find((item: any) => {
                return item.asubId == props.modelValue;
            })?.asubType;
        }
        innerAsubName.value =
            subjectData.value.find((item: any) => {
                return item.asubId == props.modelValue;
            })?.asubAAName || "";
    })
    .catch((error) => {
        console.log(error);
        ElMessage.error("出现错误，请刷新页面重试");
    });
}
getAsubList()

window.addEventListener("modifyaccountSubject", () => {
    if(useRouterArrayStoreHook().routerArray.find((item) => item.title.includes('员工信息'))) {
        getAsubList();
    }
});
function getSelectLabel(subject: IAccountSubjectModel) {
    return subject.asubAAName;
}

const popoverRef = ref<InstanceType<typeof ElPopover>>();
const handleClose = () => {
    popoverRef.value?.hide();
};
function showHandle() {
    const node = document.getElementById(props.modelValue);
    if (node) {
        setTimeout(() => {
            if (node) {
                nextTick(() => {
                    node.scrollIntoView({ block: "center" });
                });
            }
        }, 100);
    }
}
const asubRef = ref();
function showDialog() {
    //popover显示
    asubRef.value?.click();
}
provide(popoverHandleCloseKey, handleClose);
provide("subjectValue", props.modelValue);
provide("highLight", props.highLight);
provide("checkValid", props.checkValid);
provide("checkFA", props.checkFA);
provide(
    "tabs",
    props.isUnionSalary ? AccountSubjectType.Disbursement : subjectData.value.find((item: any) => {
        return item.asubId == props.modelValue;
    })?.asubType
);
provide("btntxt", props.btntxt);
function updateAsubCode(asubCode: string) {
    innerAsubCode.value = asubCode;
}

provide(updateAsubCodeKey, updateAsubCode);
watch(
    innerAsubCode,
    () => {
        innerAsubName.value =
            subjectData.value.find((item: any) => {
                return item.asubId == innerAsubCode.value;
            })?.asubAAName || "";
    },
    { immediate: true }
);
// <InstanceType<typeof ElInput>>
const showTipsInputRef = ref();
function focusInput() {
    showTipsInputRef.value?.focus();
}
function inputKeyDown(e: any) {
    if (e.keyCode === 13) {
        emit("inputKeyDown");
    }
}
defineExpose({
    asubName,
    asubId,
    focusInput,
});
</script>

<template>
    <template v-if="selectShow">
        <el-select
            :popper-class="'subject-picker'"
            v-model="innerAsubCode"
            :style="{ width: diyWidth }"
            :teleported="props.isToBody"
            :fit-input-width="true"
            :filterable="true"
            placeholder=" "
            placement="bottom"
            :default-first-option="true"
            suffix-icon=""
            :reserve-keyword="false"
            style="position: relative"
            :disabled="props.disabled"
        >
            <div style="max-height: 170px">
                <template v-if="isById">
                    <el-option v-for="item in subjectData" :key="item.asubId" :label="getSelectLabel(item)" :value="item.asubId">
                    </el-option>
                </template>
                <template v-else>
                    <el-option v-for="item in subjectData" :key="item.asubId" :label="getSelectLabel(item)" :value="item.asubId + ''">
                    </el-option>
                </template>
            </div>
        </el-select>
    </template>
    <template v-else>
        <ToolTip :isInput="true" v-if="showTips" :content="innerAsubName" :shieldDistance="20" placement='right'>
            <!-- <template #content>
                <span class="tooltip_content">{{ innerAsubName }}</span>
            </template> -->
            <el-input
                ref="showTipsInputRef"
                v-model="innerAsubName"
                :style="{ width: diyWidth }"
                :readonly="true"
                :disabled="props.disabled"
                @keydown="inputKeyDown"
                @click="dialogShow ? showDialog() : ''"
            ></el-input>
        </ToolTip>
        <el-input
            v-else
            v-model="innerAsubName"
            :style="{ width: diyWidth }"
            :readonly="true"
            :disabled="props.disabled"
            @click="dialogShow ? showDialog() : ''"
        ></el-input>
    </template>

    <el-popover
        placement="right"
        trigger="click"
        :width="372"
        :teleported="props.isToBody"
        ref="popoverRef"
        :disabled="props.disabled"
        @show="showHandle"
    >
        <template #reference>
            <div ref="asubRef" class="asub-img" :style="{ right: asubImgRight }" style="height: 22px"></div>
        </template>
        <SubjectDialog :tabs="tabs" :isUnionSalary="props.isUnionSalary"></SubjectDialog>
    </el-popover>
</template>

<style lang="less" scoped>
.tooltip_content {
    display: inline-block;
    max-width: 300px;
    text-align: left;
    white-space: normal;
}
.asub-img {
    position: absolute;
    right: 18px;
    top: 6px;
    bottom: 1px;
    width: 18px;
    background: url("@/assets/AccountBooks/book.png") no-repeat center #fff;
    cursor: pointer;
}

:deep(.el-scrollbar__bar) {
    &.is-vertical {
        display: block !important;
    }
}

:deep(.custom-tree-node) {
    .el-tooltip__trigger {
        display: inline-block;
        max-width: 309px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
:deep(.el-input__inner) {
    width: 100%;
    flex-grow: initial;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
