import { request } from "@/util/service";

export const getPeriodsApi = () => {
    return request({
        url: "/api/Period/ListWithStatus",
        method: "get",
    });
};

export const getCurrentPeriodApi = () => {
    return request({
        url: "/api/Period/GetCurrentPeriod",
        method: "post",
    });
};

export enum PeriodStatus {
    /** 没有凭证或期间未启用 */
    NoVoucher = 0,
    /** 期间有凭证 */
    HasVoucher = 1,
    /** 已完成结转损益 */
    ChangeOut = 2,
    /** 已结账 */
    CheckOut = 3,
}

export interface IPeriod {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
}