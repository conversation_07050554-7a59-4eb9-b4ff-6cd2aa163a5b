<template>
    <div class="content" :class="isErp ? 'erp-content' : ''">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">账户设置</div>
                    <el-tabs v-model="tabName">
                        <el-tab-pane label="现金" name="cashFlow">
                            <div class="main-top main-tool-bar space-between">
                                <template v-if="cashFlowShow">
                                    <div class="main-tool-left">
                                        <span class="mr-10">输入编码或名称：</span>
                                        <form @submit.prevent="submitForm">
                                            <el-input
                                                v-model="searchTextCash"
                                                placeholder=" "
                                                autocomplete="on"
                                                name="cdAccountCash"
                                                clearable
                                            />
                                            <button type="submit" class="solid-button ml-10">查询</button>
                                        </form>
                                    </div>
                                    <div class="main-tool-right">
                                        <a
                                            class="button"
                                            @click="importDialogDisplay = true"
                                            v-permission="['cdaccount-canedit']"
                                            v-show="!isErp"
                                            >导入</a
                                        >
                                        <a
                                            class="solid-button ml-10"
                                            @click="handleNew"
                                            v-permission="['cdaccount-canedit']"
                                            v-show="!isErp"
                                        >
                                            新增
                                        </a>
                                        <RefreshButton></RefreshButton>
                                    </div>
                                </template>
                            </div>
                            <div class="main-center">
                                <Table
                                    :data="tableDataCashFlow"
                                    :columns="columnsCashFlow"
                                    :loading="loading"
                                    :page-is-show="true"
                                    :layout="paginationData.layout"
                                    :page-sizes="paginationData.pageSizes"
                                    :page-size="paginationData.pageSize"
                                    :total="paginationData.total"
                                    :currentPage="paginationData.currentPage"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    @refresh="handleRerefresh"
                                    :scrollbar-show="true"
                                    :tableName="setModuleCash"
                                >
                                    <template #status>
                                        <el-table-column 
                                            label="启用状态" 
                                            min-width="100px" 
                                            align="center" 
                                            header-align="left"
                                            prop="status"
                                            :width="getColumnWidth(setModuleCash, 'status')"
                                        >
                                            <template #default="scope">
                                                <el-switch
                                                    v-model="scope.row.state"
                                                    :active-value="'0'"
                                                    :inactive-value="'1'"
                                                    :before-change="handleBeforeChangeStage"
                                                    @change="handleChangeState(scope.row, '1010')"
                                                >
                                                </el-switch>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #operation>
                                        <el-table-column label="操作" :min-width="160" align="left" header-align="left" :resizable="false">
                                            <template #default="scope">
                                                <a class="link" v-permission="['cdaccount-canedit']" @click="() => handleEdit(scope.row)">
                                                    编辑
                                                </a>
                                                <a
                                                    class="link"
                                                    v-permission="['cdaccount-candelete']"
                                                    @click="handleDelete(scope.row.ac_id, false)"
                                                >
                                                    删除
                                                </a>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </Table>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="银行存款" name="bank">
                            <div class="main-top main-tool-bar space-between">
                                <template v-if="cashBankShow">
                                    <div class="main-tool-left">
                                        <span class="mr-10">输入编码或名称：</span>
                                        <form @submit.prevent="submitForm">
                                            <el-input
                                                v-model="searchTextBank"
                                                placeholder=" "
                                                autocomplete="on"
                                                name="cdAccountBank"
                                                clearable
                                            />
                                            <button type="submit" class="solid-button ml-10">查询</button>
                                        </form>
                                    </div>
                                    <div class="main-tool-right">
                                        <a
                                            class="button"
                                            @click="globalWindowOpenPage('/Cashier/BankAccPreOpen', '预约开户')"
                                            v-permission="['bankpreopen-bankpreopen']"
                                            >预约开户</a
                                        >
                                        <a
                                            class="button ml-10"
                                            @click="importDialogDisplay = true"
                                            v-permission="['cdaccount-canedit']"
                                            v-show="!isErp"
                                            >导入</a
                                        >
                                        <a
                                            class="solid-button ml-10"
                                            @click="handleNew"
                                            v-permission="['cdaccount-canedit']"
                                            v-show="!isErp"
                                        >
                                            新增
                                        </a>
                                        <RefreshButton></RefreshButton>
                                    </div>
                                </template>
                            </div>
                            <div class="main-center">
                                <Table
                                    :data="tableDataCashBank"
                                    :columns="columnsCashBank"
                                    :loading="loading"
                                    :page-is-show="true"
                                    :layout="paginationBank.layout"
                                    :page-sizes="paginationBank.pageSizes"
                                    :page-size="paginationBank.pageSize"
                                    :total="paginationBank.total"
                                    :currentPage="paginationBank.currentPage"
                                    @size-change="handleSizeChangeBank"
                                    @current-change="handleCurrentChangeBank"
                                    @refresh="handleRerefreshBank"
                                    :scrollbar-show="true"
                                    :tableName="setModuleBank"
                                >
                                    <template #status>
                                        <el-table-column 
                                            label="启用状态" 
                                            min-width="100px" 
                                            align="center" 
                                            header-align="left"
                                            prop="status"
                                            :width="getColumnWidth(setModuleBank, 'status')"
                                        >
                                            <template #default="scope">
                                                <el-switch
                                                    v-model="scope.row.state"
                                                    :active-value="'0'"
                                                    :inactive-value="'1'"
                                                    :before-change="handleBeforeChangeStage"
                                                    @change="handleChangeState(scope.row, '1020')"
                                                >
                                                </el-switch>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #operation>
                                        <el-table-column label="操作" min-width="180px" align="left" header-align="left" :resizable="false">
                                            <template #default="scope">
                                                <a class="link" v-permission="['cdaccount-canedit']" @click="() => handleEdit(scope.row)">
                                                    编辑
                                                </a>
                                                <a
                                                    class="link"
                                                    v-permission="['cdaccount-candelete']"
                                                    @click="handleDelete(scope.row.ac_id, scope.row.authStatus)"
                                                >
                                                    删除
                                                </a>
                                                <a
                                                    class="link"
                                                    v-if="scope.row.authStatus"
                                                    v-permission="['cdaccount-canedit']"
                                                    @click="handleUnbind(scope.row.ac_id, scope.row.bankType)"
                                                >
                                                    解绑
                                                </a>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </Table>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ getTabName() === 1010 ? "现金账户" : "银行存款账户" }}</div>
                    <div class="slot-mini-content">
                        <AddForm
                            @cancel="handleCancel"
                            @save="handleSave"
                            ref="addFormRef"
                            :getTabName="getTabName"
                            :currencyList="currencyList"
                            :bankList="bankList"
                        />
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
    <ImportSingleFileDialog
        :importTitle="'账户导入'"
        v-model:import-show="importDialogDisplay"
        :importUrl="'/api/CDAccount/Import'"
        :downloadTemplate="handleDownloadTemplate"
        :uploadSuccess="uploadSuccess"
    ></ImportSingleFileDialog>
</template>

<script lang="ts">
export default {
    name: "CDAccount",
};
</script>
<script setup lang="ts">
import { ref, watch, nextTick, onMounted, onUnmounted, onActivated, computed} from "vue";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams, globalExport, globalWindowOpenPage } from "@/util/url";
import { ElAlert, ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { useRoute } from "vue-router";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableItem, ISearchParams, IBankList, IBankBack } from "./types";
import type { ICurrencyList } from "@/views/Cashier/components/types";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Table from "@/components/Table/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import AddForm from "./components/AddForm.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { checkPermission } from "@/util/permission";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useCurrencyStore } from "@/store/modules/currencyList";

const route = useRoute();
const { 
    paginationData, 
    handleCurrentChange, 
    handleSizeChange, 
    handleRerefresh 
} = usePagination("cash", true);
const {
    paginationData: paginationBank,
    handleCurrentChange: handleCurrentChangeBank,
    handleSizeChange: handleSizeChangeBank,
    handleRerefresh: handleRerefreshBank,
} = usePagination("deposite", true);

const tabName = ref("cashFlow");
const setModuleCash = "CAccount";
const setModuleBank = "DAccount";
const columnsCashFlow = ref<Array<IColumnProps>>([
    { label: "编码", prop: "ac_no", minWidth: 140, align: "left", headerAlign: "left", width: getColumnWidth(setModuleCash, 'ac_no') },
    { label: "账户名称", prop: "ac_name", minWidth: 220, align: "left", headerAlign: "left", width: getColumnWidth(setModuleCash, 'ac_name') },
    { label: "币别", prop: "currency_name", minWidth: 190, align: "left", headerAlign: "left", width: getColumnWidth(setModuleCash, 'currency_name') },
    { label: "会计科目", prop: "asub_name", minWidth: 270, align: "left", headerAlign: "left", width: getColumnWidth(setModuleCash, 'asub_name') },
    { slot: "status" },
    { slot: "operation" },
]);
const columnsCashBank = ref<Array<IColumnProps>>([
    { label: "编码", prop: "ac_no", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModuleBank, 'ac_no') },
    { label: "账户名称", prop: "ac_name", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModuleBank, 'ac_name') },
    {
        label: "银行",
        prop: "bank_id",
        minWidth: 180,
        width: getColumnWidth(setModuleBank, 'bank_id'),
        align: "left",
        headerAlign: "left",
        formatter(_r, _c, value) {
            return bankList.value.find((item) => item.id === value)?.name || "";
        },
    },
    { label: "银行账号", prop: "bank_account", minWidth: 205, align: "left", headerAlign: "left", width: getColumnWidth(setModuleBank, 'bank_account') },
    { label: "币别", prop: "currency_name", minWidth: 120, align: "left", headerAlign: "left", width: getColumnWidth(setModuleBank, 'currency_name') },
    { label: "会计科目", prop: "asub_name", minWidth: 215, align: "left", headerAlign: "left", width: getColumnWidth(setModuleBank, 'asub_name') },
    {
        label: "绑定银企互联",
        prop: "authStatus",
        minWidth: 130,
        width: getColumnWidth(setModuleBank, 'authStatus'),
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => {
            return value ? "已绑定" : "未绑定";
        },
    },
    {
        label: "绑定日期",
        prop: "bindDate",
        minWidth: 130,
        width: getColumnWidth(setModuleBank, 'bindDate'),
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => {
            return String(value).substring(0, String(value).indexOf(" ")).replace(new RegExp(/\//g), "-");
        },
    },
    {
        label: "到期日期",
        prop: "mat_date",
        minWidth: 130,
        width: getColumnWidth(setModuleBank, 'mat_date'),
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => {
            return value === "" ? "" : String(value).substring(0, String(value).indexOf(" ")).replace(new RegExp(/\//g), "-");
        },
    },
    { slot: "status" },
    { slot: "operation" },
]);
const slots = ["main", "add"];

const addFormRef = ref<InstanceType<typeof AddForm>>();
const importDialogDisplay = ref(false);

let type: string = "";
const currentSlot = ref("main");
const searchTextCash = ref("");
const searchTextBank = ref("");
const loading = ref(false);
const tableDataCashFlow = ref<ITableItem[]>([]);
const tableDataCashBank = ref<ITableItem[]>([]);
const currencyList = ref<ICurrencyList[]>([]);
const isErp = ref(window.isErp);

const getCdAccountListApi = (acType: number) => {
    const searchText = acType === 1010 ? searchTextCash.value : searchTextBank.value;
    const params: ISearchParams = { acType, acName: searchText, acAuth: true };
    params.pageIndex = acType === 1010 ? paginationData.currentPage : paginationBank.currentPage;
    params.pageSize = acType === 1010 ? paginationData.pageSize : paginationBank.pageSize;
    return request({ url: "/api/CDAccount/PagingList?" + getUrlSearchParams(params) });
};
const handleEdit = (row: ITableItem, init = false) => {
    type = "Edit";
    const params = {
        AC_NO: row.ac_no,
        AC_NAME: row.ac_name,
        BANK_ACCOUNT: row.bank_account,
        CURRENCY: Number(row.currency),
        ASUB: row.asub === "0" ? "" : row.asub,
        AC_ID: row.ac_id,
        bankDisabled: getTabName() === 1020 && row.authStatus,
        // 不确定是string还是number已经不确定是0还是空字符串
        bankid: ~~row.bank_id === 0 ? "" : row.bank_id.toString(),
    };
    Check(params.AC_ID, params, init);
    if (init) return;
    CheckSubjectHaveChildren(params.ASUB, params);
};
const Check = (acid: string, params: any, init = false) => {
    request({ url: "/api/CDAccount/Check?acId=" + acid, method: "post" }).then((res: any) => {
        if (res.state != 1000) return;
        const result = res.data;
        if (result == "USE") {
            addFormRef.value?.editDisabled(true);
        } else if (result == "TextOverflow") {
            ElNotify({ type: "warning", message: "亲，银行账户名称超过64个字，请修改后重新保存~ " });
        } else {
            addFormRef.value?.editDisabled(false);
        }
        if (init) {
            setTimeout(() => {
                addFormRef.value?.editSearchInfo(params);
                currentSlot.value = "add";
            }, 0);
        }
    });
};
const CheckSubjectHaveChildren = (asub: string, params: any) => {
    if (asub === "") {
        setTimeout(() => {
            addFormRef.value?.editSearchInfo(params);
            currentSlot.value = "add";
        }, 0);
        return;
    }
    request({ url: "/api/AccountSubject/CheckSubjectHaveChildren?parentAsubId=" + asub, method: "post" }).then((res: any) => {
        if (res.state == 1000 && res.data === "Failed") {
            ElNotify({ type: "warning", message: "亲，会计科目只能选择末级科目，请重新选择" });
            params.ASUB = "";
        }
        setTimeout(() => {
            addFormRef.value?.editSearchInfo(params);
            currentSlot.value = "add";
        }, 0);
    });
};
const handleDelete = (acId: string, authStatus: boolean | undefined) => {
    let confirmMsg = authStatus ? "该银行账户已绑定银企互联，删除账户后会自动解绑。请确认是否删除选中数据？" : "亲，确认要删除吗?";
    ElConfirm(confirmMsg).then((r: any) => {
        if (r) {
            request({ url: "/api/CDAccount?acId=" + acId, method: "delete" }).then((res: any) => {
                if (res.state == 1000) {
                    if (res.data == "Success") {
                        ElNotify({ type: "success", message: "删除成功" });
                        window.dispatchEvent(new CustomEvent("reloadCDAccount"));
                        const length = getTabName() === 1010 ? tableDataCashFlow.value.length : tableDataCashBank.value.length;
                        const currentPage = getTabName() === 1010 ? paginationData.currentPage : paginationBank.currentPage;
                        if (length > 1) {
                            getTableList();
                        } else {
                            currentPage === 1
                                ? getTableList()
                                : getTabName() === 1010
                                ? (paginationData.currentPage = currentPage - 1)
                                : (paginationBank.currentPage = currentPage - 1);
                        }
                    } else if (res.data == "USE") {
                        ElNotify({ type: "warning", message: "账户已被使用，无法删除哦！" });
                    } else {
                        ElNotify({ type: "warning", message: "删除失败了，请联系侧边栏客服" });
                    }
                } else {
                    ElNotify({ type: "warning", message: res.msg });
                }
            });
        }
    });
};
const handleNew = () => {
    type = "New";
    const total = tabName.value === "cashFlow" ? paginationData.total : paginationBank.total;
    const tableData = tabName.value === "cashFlow" ? tableDataCashFlow.value : tableDataCashBank.value;
    let ac_no = total + 1;
    let ac_no_str = ac_no.toString().padStart(3, "0");
    const arr = tableData.map(function (o) {
        return o.ac_no;
    });
    while (arr.indexOf(ac_no_str) > -1) {
        ac_no = ac_no + 1;
        ac_no_str = ac_no.toString().padStart(3, "0");
    }

    addFormRef.value?.resetForm();
    addFormRef.value?.editSearchInfo({ AC_NO: ac_no_str });
    currentSlot.value = "add";
};

const handleBeforeChangeStage = () => {
    if (!checkPermission(["cdaccount-canedit"])) {
        ElNotify({ type: "warning", message: "您没有启用/禁用账户的权限，请联系管理员" });
        return false;
    }
    return true;
};

const handleChangeState = (data: ITableItem, acType: string) => {
    let url = acType == "1010" ? "/api/CDAccount/ChangeCashState" : "/api/CDAccount/ChangeDepositState";
    url = `${url}?acId=${data.ac_id}&state=${data.state}`;
    request({ url: url, method: "post" }).then((res: IResponseModel<string>) => {
        if (res.state == 1000) {
            if (res.data == "Success") {
                ElNotify({
                    type: "success",
                    message: data.ac_no + "-" + data.ac_name + "账户" + (data.state === "0" ? "启用" : "禁用") + "成功",
                });
                window.dispatchEvent(new CustomEvent("reloadCDAccount"));
                getTableList();
            } else {
                ElNotify({ type: "warning", message: "操作失败了，请联系侧边栏客服" });
            }
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const handleUnbind = (acId: string, bankType: number) => {
    ElAlert({ message: "亲，确认要解绑吗? 解绑后将无法同步银企互联流水" }).then((r: any) => {
        if (r) {
            request({ url: `/api/CDAccount/Unbind?acId=${acId}&bankType=${bankType}`, method: "post" }).then(
                (res: IResponseModel<string>) => {
                    if (res.state == 1000) {
                        if (res.data == "Success") {
                            ElNotify({ type: "success", message: "解绑成功!" });
                            getTableList();
                        } else if (res.data === "NoAccount") {
                            ElNotify({ type: "warning", message: "亲，账号不存在，请刷新后重试!" });
                        } else {
                            ElNotify({ type: "warning", message: "解绑失败了，请联系侧边栏客服!" });
                        }
                    } else {
                        ElNotify({ type: "warning", message: res.msg });
                    }
                }
            );
        }
    });
};
const handleDownloadTemplate = () => {
    globalExport("/api/CDAccount/ExportImportTemplate");
};

const uploadSuccess = (res: IResponseModel<boolean>) => {
    if (res.state === 1000 && res.data) {
        ElNotify({
            type: "success",
            message: "导入成功！",
        });
        window.dispatchEvent(new CustomEvent("reloadCDAccount"));
        handleRefresh();
        importDialogDisplay.value = false;
    } else {
        ElNotify({
            type: "warning",
            message: res.msg,
        });
    }
};

const cashFlowShow = ref(true);
const cashBankShow = ref(true);
const submitForm = (event: Event) => {
    event.preventDefault();
    if (getTabName() === 1010) {
        cashFlowShow.value = false;
        nextTick().then(() => {
            cashFlowShow.value = true;
        });
    } else {
        cashBankShow.value = false;
        nextTick().then(() => {
            cashBankShow.value = true;
        });
    }
    // 在这里执行表单提交的逻辑
    getTableList();
};
const getTableList = () => {
    getTabName() === 1010 ? handleRefreshCash() : handleRefreshDesposit();
};
const handleRefreshCash = () => {
    loading.value = true;
    getCdAccountListApi(1010)
        .then((res: any) => {
            if (res.state == 1000) {
                tableDataCashFlow.value = res.data.rows;
                paginationData.total = res.data.total;
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleRefreshDesposit = () => {
    loading.value = true;
    getCdAccountListApi(1020)
        .then((res: any) => {
            if (res.state == 1000) {
                tableDataCashBank.value = res.data.rows;
                paginationBank.total = res.data.total;
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleRefresh = () => {
    handleRefreshCash();
    handleRefreshDesposit();
};
const handleCancel = () => {
    addFormRef.value?.resetForm();
    currentSlot.value = "main";
    type = "";
};
const handleSave = (rowItemData: any) => {
    const params = {
        acName: rowItemData.AC_NAME,
        bankAccount: rowItemData.BANK_ACCOUNT,
        acType: getTabName(),
        acNo: rowItemData.AC_NO,
        currency: rowItemData.CURRENCY,
        asub: rowItemData.ASUB,
        acId: rowItemData.AC_ID,
        bankid: ~~rowItemData.bankid,
    };
    const requestParams = {
        url: "/api/CDAccount",
        method: type === "New" ? "post" : "put",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: params,
    };
    const fcname = currencyList.value.find((item) => item.id === params.currency)?.name || "该币别";
    request(requestParams).then((res: any) => {
        if (res.state == 1000) {
            if (res.data.msg == "Success") {
                ElNotify({ type: "success", message: "保存成功！" });
                window.dispatchEvent(new CustomEvent("reloadCDAccount"));
                getTableList();
                handleCancel();
            } else if (res.data.msg == "NO") {
                ElNotify({ type: "warning", message: "亲，编码或会计科目重复了！" });
            } else if (res.data.msg.indexOf("NAME") != -1) {
                res.data.msg = res.data.msg.replace("NAME", "");
                if (res.data.msg == "1010") {
                    ElNotify({ type: "warning", message: "亲，现金账户有相同名称，请更换后重试！" });
                } else if (res.data.msg == "1020") {
                    ElNotify({ type: "warning", message: "亲，银行账户有相同名称，请更换后重试！" });
                }
            } else if (res.data.msg == "TextOverflow") {
                if (getTabName() === 1020) {
                    ElNotify({ type: "warning", message: "亲，银行账户名称超过64个字，请修改后重新保存~ " });
                } else {
                    ElNotify({ type: "warning", message: "亲，现金账户名称超过64个字，请修改后重新保存~ " });
                }
            } else if (res.data.msg == "AccountOverflow") {
                ElNotify({ type: "warning", message: "亲，银行账号超过64个字，请修改后重新保存~ " });
            } else if (res.data.msg === "CURRENCY") {
                ElNotify({ type: "warning", message: "亲，该会计科目不具有" + fcname + "核算" });
            } else {
                ElNotify({ type: "warning", message: "保存失败了，请联系侧边栏客服" });
            }
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const getCurrencyList = async () => {
    await useCurrencyStore().getCurrencyList();
    currencyList.value = [...useCurrencyStore().fcList];
};
function removeFieldsFromUrl() {
    const queryParams = JSON.parse(JSON.stringify(route.query));
    delete queryParams.editAcType;
    delete queryParams.editAcId;
    const modifiedUrl = route.path + "?" + getUrlSearchParams(queryParams);
    return modifiedUrl;
}
const handleEditFromRoute = () => {
    const editAcType = route.query.editAcType as undefined | "1010" | "1020";
    const editAcId = route.query.editAcId as undefined | string;
    canActive = true;
    if (!editAcType) return;
    tabName.value = editAcType === "1010" ? "cashFlow" : "bank";
    if (!editAcId) return;
    const editAcTypeNumber = Number(editAcType);
    const searchList = tabName.value === "cashFlow" ? tableDataCashFlow.value : tableDataCashBank.value;
    const editItem = searchList.find((item) => item.ac_id === editAcId) as ITableItem;
    if (!editItem) return;
    const row = editItem as ITableItem;
    const replacePath = removeFieldsFromUrl();
    useRouterArrayStoreHook().replaceCurrentRouter(replacePath);
    handleEdit(row, true);
};

const bankList = ref<Array<IBankList>>([]);
async function handleGetBankList() {
    await request({ url: "/api/CDAccount/Bank" }).then((res: IResponseModel<IBankBack>) => {
        if (res.state !== 1000) return;
        const list: Array<IBankList> = [];
        for (const key in res.data) {
            list.push({ id: key, name: res.data[key] });
        }
        bankList.value = list;
    });
}

const handleInit = async () => {
    loading.value = true;
    await handleGetBankList();
    getCurrencyList();
    // const cashNotFromStorage =
    //     localStorage.getItem("CDAccountCashListSize") === null || localStorage.getItem("CDAccountCashListSize") === "20";
    // const bankNotFromStorage =
    //     localStorage.getItem("CDAccountDepositListSize") === null || localStorage.getItem("CDAccountDepositListSize") === "20";
    // if (!cashNotFromStorage) paginationData.pageSize = Number(localStorage.getItem("CDAccountCashListSize"));
    // if (!bankNotFromStorage) paginationBank.pageSize = Number(localStorage.getItem("CDAccountDepositListSize"));
    Promise.all([getCdAccountListApi(1010), getCdAccountListApi(1020)])
        .then((res: any) => {
            if (res[0].state == 1000 && res[1].state == 1000) {
                tableDataCashFlow.value = res[0].data.rows;
                paginationData.total = res[0].data.total;
                tableDataCashBank.value = res[1].data.rows;
                paginationBank.total = res[1].data.total;
                handleEditFromRoute();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
// handleInit在watch之前执行，避免二次请求
handleInit();

watch(
    [
        () => paginationData.currentPage,
        () => paginationData.pageSize,
        () => paginationData.refreshFlag,
        () => paginationBank.currentPage,
        () => paginationBank.pageSize,
        () => paginationBank.refreshFlag,
    ],
    () => {
        getTableList();
    }
);

const getTabName = () => (tabName.value === "cashFlow" ? 1010 : 1020);
function reloadCurrency() {
    if (useRouterArrayStoreHook().routerArray.findIndex((item) => item.title === "账户设置") === -1) return;
    getCurrencyList();
}

function reloadCDAccount(e: any) {
    const event: CustomEvent = e;
    if (useRouterArrayStoreHook().routerArray.findIndex((item) => item.title === "账户设置") === -1) return;
    if (!event.detail?.journalType) return;
    if (event.detail.journalType === "1010") {
        handleRefreshCash();
    } else if (event.detail.journalType === "1020") {
        handleRefreshDesposit();
    }
}

let canActive = false;
onMounted(() => {
    window.addEventListener("reloadCurrency", reloadCurrency);
    window.addEventListener("reloadCDAccount", reloadCDAccount);
});

onUnmounted(() => {
    window.removeEventListener("reloadCurrency", reloadCurrency);
    window.removeEventListener("reloadCDAccount", reloadCDAccount);
    canActive = false;
});
onActivated(() => {
    if (!canActive) return;
    handleEditFromRoute();
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.content {
    .slot-content {
        .slot-mini-content {
            width: 1000px;
            margin-top: 32px;
            border: 1px solid var(--slot-title-color);
        }
    }
}
.main-top {
    box-sizing: border-box;
    overflow: hidden;
    padding: 16px 0;
    span.float-l.mr-10 {
        width: 112px;
        height: 30px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 30px;
    }
    :deep(.el-input) {
        width: 150px;
        height: 32px;
    }
}
.main-center {
    padding: 0;

    :deep(.el-table) {
        .el-table__body-wrapper {
            position: relative;
        }
        .el-scrollbar__view {
            min-height: 441px;
            .el-table__append-wrapper {
                position: absolute;
                bottom: 0;
                width: 100%;
            }
        }
    }
}

.import-content {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .download-content {
        margin-top: 30px;
        margin-left: 40px;
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
    }

    .import-content {
        margin-top: 20px;
        margin-left: 40px;
        margin-bottom: 30px;
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
        display: flex;
        align-items: center;
        flex-direction: row;
        .file-name {
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 20px;
            word-break: break-all;
            white-space: nowrap;
            cursor: pointer;
        }
    }

    .buttons {
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        padding: 10px;
    }
}
.main-content {
    :deep(.el-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__content {
            flex: 1;
        }
        .el-tab-pane {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
    }
}

.content {
    &.erp-content {
        .main-content {
            :deep(.el-tabs) {
                height: 100%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                .el-tabs__content {
                    flex: 1;
                    .el-tab-pane {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        .el-scrollbar__view {
                            min-height: 0px;
                        }
                    }
                }
            }
        }
    }
}
</style>
