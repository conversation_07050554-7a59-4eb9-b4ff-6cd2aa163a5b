<template>
  <div class="main-top space-between">
    <div class="main-tool-left">
      <div class="tool-item mr-10">
        <span>类型：</span>
        <LMSelect v-model="type">
          <LMOption
            v-for="item in TYPE_LIST"
            :value="item.value"
            :label="item.label"
            :key="item.value"></LMOption>
        </LMSelect>
      </div>
      <div
        class="tool-item mr-10"
        v-if="['deducSelect', 'nonDeducSelect'].includes(selType)">
        <span>勾选状态：</span>
        <LMSelect
          v-model="status"
          @change="getTableData">
          <LMOption
            v-for="item in STATUS_LIST"
            :value="item.value"
            :label="item.label"
            :key="item.value"></LMOption>
        </LMSelect>
      </div>
      <SearchInfoContainer>
        <template v-slot:title>{{ currentPeriodInfo }}</template>
        <div
          class="line-item first-item input"
          v-if="['nonDeductionRecords', 'deductionRecords'].includes(selType)">
          <div class="line-item-title">发票税款所属期</div>
          <div class="line-item-field">
            <el-date-picker
              v-model="searchInfo.invDateStart"
              type="month"
              :clearable="false"
              :editable="false"
              :teleported="false"
              :value-format="'YYYY-MM'"
              :format="'YYYY-MM'"
              style="width: 132px" />
            <span class="ml-10 mr-10">至</span>
            <el-date-picker
              v-model="searchInfo.invDateEnd"
              type="month"
              :clearable="false"
              :editable="false"
              :teleported="false"
              :value-format="'YYYY-MM'"
              :format="'YYYY-MM'"
              style="width: 132px" />
          </div>
        </div>
        <div
          class="line-item input"
          :class="['nonDeductionRecords', 'deductionRecords'].includes(selType) ? '' : 'first-item'">
          <div class="line-item-title">开票日期：</div>
          <div class="line-item-field">
            <el-date-picker
              v-model="searchInfo.invDateStart"
              type="month"
              :clearable="false"
              :editable="false"
              :teleported="false"
              :value-format="'YYYY-MM-DD'"
              :format="'YYYY-MM-DD'"
              style="width: 132px" />
            <span class="ml-10 mr-10">至</span>
            <el-date-picker
              v-model="searchInfo.invDateEnd"
              type="month"
              :clearable="false"
              :editable="false"
              :teleported="false"
              :value-format="'YYYY-MM-DD'"
              :format="'YYYY-MM-DD'"
              style="width: 132px" />
          </div>
        </div>
        <div class="line-item input">
          <div class="line-item-title">发票号码：</div>
          <div class="line-item-field">
            <el-input
              v-input.integer
              v-model="searchInfo.invocieNum"
              style="width: 132px"></el-input>
          </div>
        </div>
        <div class="line-item input">
          <div class="line-item-title">发票种类：</div>
          <div class="line-item-field">
            <el-select
              v-model="searchInfo.invocieType"
              :multiple="true"
              collapse-tags
              collapse-tags-tooltip>
              <el-option
                v-for="item in INVOICE_TYPE_LIST"
                :value="item.value"
                :label="item.label"
                :key="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="line-item input">
          <div class="line-item-title">发票状态：</div>
          <div class="line-item-field">
            <el-select
              v-model="searchInfo.invocieStatus"
              :multiple="true"
              collapse-tags
              collapse-tags-tooltip>
              <el-option
                v-for="item in INVOICE_STATUS_LIST"
                :value="item.value"
                :label="item.label"
                :key="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="line-item input">
          <div class="line-item-title">销方名称：</div>
          <div class="line-item-field">
            <el-input
              v-model="searchInfo.sellerName"
              style="width: 132px"></el-input>
          </div>
        </div>
        <div class="line-item input">
          <div class="line-item-title">发票风险等级：</div>
          <div class="line-item-field">
            <el-select
              v-model="searchInfo.riskLevel"
              :multiple="true"
              collapse-tags
              collapse-tags-tooltip>
              <el-option
                v-for="item in INVOICE_RISK_LEVEL_LIST"
                :value="item.value"
                :label="item.label"
                :key="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="buttons">
          <a
            class="button solid-button"
            @click="handleSearch">
            确定
          </a>
          <a
            class="button"
            @click="handleCancel">
            取消
          </a>
          <a
            class="button"
            @click="handleReset">
            重置
          </a>
        </div>
      </SearchInfoContainer>
    </div>
    <div class="main-tool-right">
      <el-popover
        placement="bottom"
        width="300">
        <template #reference>
          <a
            class="button mr-20"
            @click="collectInvoiceData">
            采集发票
          </a>
        </template>
        <div class="popover-content">
          <span class="time">{{ lastestTime }} 更新</span>
          <span
            class="link"
            @click="showUpdateHistory">
            查看记录
          </span>
        </div>
      </el-popover>
      <a
        class="button mr-20"
        style="width: 130px"
        v-if="selType === 'nonDeducSelect'"
        @click="batchSetNonDeductibleReason">
        批量设置不抵扣原因
      </a>
      <a
        class="button"
        @click="handleSubmitSelected">
        提交勾选
      </a>
    </div>
  </div>
  <div class="main-content-body">
    <LMTable
      ref="tableRef"
      rowKey="invoiceNumber"
      :data="tableData"
      :columns="columns"
      :pageIsShow="true"
      :page-sizes="paginationData.pageSizes"
      :page-size="paginationData.pageSize"
      :total="paginationData.total"
      :currentPage="paginationData.currentPage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @refresh="handleRefresh"
      @selection-change="
        (val: ITableData[]) => {
          selectData = val
        }
      ">
      >
      <template #nonDeductibleReason>
        <el-table-column label="不抵扣原因">
          <template #default="{ row }">
            <el-select v-model="row.nonDeductibleReason">
              <LMOption
                v-for="item in NONDEDUCTION_REASON_LIST"
                :value="item.value"
                :label="item.label"
                :key="item.value"></LMOption>
            </el-select>
          </template>
        </el-table-column>
      </template>
      <template #operation>
        <el-table-column label="操作">
          <template #default="{ row }">
            <span
              class="link"
              @click="emits('check', row)">
              查看
            </span>
          </template>
        </el-table-column>
      </template>
      <template
        #pageOther
        v-if="selectData.length">
        <span>
          已选 {{ selectData.length }} 条 有效税额抵扣金合计：{{ getTotalDeductibleAmount() }} 元
          <a
            class="link pl-20"
            @click="handleOpenPageSelectDialog">
            查看
          </a>
          <a
            class="link pl-20"
            @click="tableRef.TableComponents.getTable()?.clearSelection()">
            取消全部
          </a>
        </span>
      </template>
    </LMTable>
  </div>
  <Confirm
    title="采集发票数据"
    v-model:visible="collectInvoiceVisible"
    :onConfirm="gatherInvoice"
    :options="{
      confirmText: '立即获取',
    }">
    <div class="collect-invoice-confirm">
      <div class="confirm-content-title">
        {{ status === "0" ? "未勾选发票" : `已勾选${["deducSelect", "deductionRecords"].includes(selType) ? "抵扣" : "不抵扣"}发票` }}
      </div>
      <div
        class="confirm-content-field"
        v-if="status === '0'">
        <div class="line-item-title">开票日期：</div>
        <div class="line-item-field">
          <el-date-picker
            v-model="searchInfo.invDateStart"
            type="month"
            :clearable="false"
            :editable="false"
            :teleported="false"
            :value-format="'YYYY-MM-DD'"
            :format="'YYYY-MM-DD'"
            style="width: 132px" />
          <span class="ml-10 mr-10">至</span>
          <el-date-picker
            v-model="searchInfo.invDateEnd"
            type="month"
            :clearable="false"
            :editable="false"
            :teleported="false"
            :value-format="'YYYY-MM-DD'"
            :format="'YYYY-MM-DD'"
            style="width: 132px" />
        </div>
      </div>
      <div
        class="confirm-content-field"
        v-else>
        <div class="line-item-title">税款所属期</div>
        <div class="line-item-field">
          <el-input
            v-model="currentPeriod"
            disabled
            style="width: 132px"></el-input>
        </div>
      </div>
    </div>
  </Confirm>
  <Confirm
    title="批量设置不抵扣原因"
    v-model:visible="noDeductibleReasonVisible"
    :onConfirm="handleBatchSetNonDeductibleReason">
    <div class="batch-non-deductible-reason-confirm">
      <div class="confirm-content-title">不抵扣原因：</div>
      <div class="confirm-content-field">
        <el-select v-model="nonDeductionReason">
          <LMOption
            v-for="item in NONDEDUCTION_REASON_LIST"
            :value="item.value"
            :label="item.label"
            :key="item.value"></LMOption>
        </el-select>
      </div>
    </div>
  </Confirm>
  <Confirm
    title="已选记录"
    v-model:visible="selectRecordVisible"
    width="800">
    <div class="confirm-content-field">
      <LMTable
        ref="selectedTableRef"
        rowKey="invoiceNumber"
        :data="selectData"
        :columns="SELECT_COLUMNS"></LMTable>
    </div>
  </Confirm>
  <Confirm
    title="采集记录"
    v-model:visible="collectRecordVisible"
    width="700">
    <div class="confirm-content-field">
      <LMTable
        ref="collectTableRef"
        rowKey="invoiceNumber"
        :data="collectData"
        :columns="COLLECT_COLUMNS"
        :pageIsShow="true"
        :page-sizes="paginationHistoryData.pageSizes"
        :page-size="paginationHistoryData.pageSize"
        :total="paginationHistoryData.total"
        :currentPage="paginationHistoryData.currentPage"
        @size-change="handleHistorySizeChange"
        @current-change="handleHistoryCurrentChange"
        @refresh="handleHistoryRefresh"></LMTable>
    </div>
  </Confirm>
  <Confirm
    :visible="collectTipVisible"
    :options="{
      buttons: {
        confirm: '我知道了',
      },
      showCancel: false,
      message: '正在取票中，可能需要几分钟，您可以先关闭此弹窗，申报完成后会弹窗通知您~',
    }"></Confirm>
</template>

<script setup lang="ts">
  import { usePagination } from "@/hooks/usePagination"
  import {
    TYPE_LIST,
    STATUS_LIST,
    INVOICE_TYPE_LIST,
    INVOICE_RISK_LEVEL_LIST,
    NONDEDUCTION_REASON_LIST,
    INVOICE_STATUS_LIST,
    TABLE_COLUMNS,
    SELECT_COLUMNS,
    COLLECT_COLUMNS,
  } from "../constants"
  import { ElNotify } from "@/utils/notify"
  import { request, IResponseModel } from "@/utils/service"
  import { LatestParamsType, getLatestUpdateTime, checkUpdateStatus } from "@/api/taxManagement"
  import { formatYearMonthToChinese } from "@/utils/format"
  import { ElConfirm } from "@/utils/confirm"
  import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  import { EnumUpdateStatus } from "@/views/TaxManagement/constants"

  const SearchInfoContainer = defineAsyncComponent(() => import("@/components/SearchInfoContainer/index.vue"))
  const props = defineProps({
    selType: {
      type: String as PropType<"deducSelect" | "nonDeducSelect" | "deductionRecords" | "nonDeductionRecords">,
      default: "deducSelect",
    },
    invoiceTaxPeriod: {
      type: String,
      default: "",
    },
  })
  const emits = defineEmits(["check"])
  const taxPeriodStore = useTaxPeriodStore()
  const { currentPeriod } = storeToRefs(taxPeriodStore)
  const tableRef = ref()
  const selectedTableRef = ref()
  const type = ref("1")
  const status = ref(["deducSelect", "nonDeducSelect"].includes(props.selType) ? "0" : "1")
  const currentPeriodInfo = ref("")

  function getCurrentPeriodInfo() {
    if (searchInfo.invDateStart === searchInfo.invDateEnd) {
      return formatYearMonthToChinese(searchInfo.invDateStart)
    } else {
      return `${formatYearMonthToChinese(searchInfo.invDateStart)} 至 ${formatYearMonthToChinese(searchInfo.invDateEnd)}`
    }
  }

  function createInitialSearchInfo() {
    const base = {
      invDateStart: "",
      invDateEnd: "",
      invocieNum: "",
      invocieType: INVOICE_TYPE_LIST.map((item) => item.value),
      invocieStatus: INVOICE_STATUS_LIST.map((item) => item.value),
      sellerName: "",
      riskLevel: INVOICE_RISK_LEVEL_LIST.map((item) => item.value),
    }
    if (["deductionRecords", "nonDeductionRecords"].includes(props.selType)) {
      return {
        ...base,
        start: "",
        end: "",
      }
    } else {
      return {
        ...base,
        someFlag: "0",
      }
    }
  }

  const searchInfo = reactive(createInitialSearchInfo())
  currentPeriodInfo.value = getCurrentPeriodInfo()

  function handleSearch() {
    currentPeriodInfo.value = getCurrentPeriodInfo()
    getTableData()
  }

  function handleCancel() {
    //取消
  }

  function handleReset() {
    const initialSearchInfo = createInitialSearchInfo()
    Object.assign(searchInfo, initialSearchInfo)
  }
  const { paginationData, handleCurrentChange, handleSizeChange, handleRefresh } = usePagination()
  const {
    paginationData: paginationHistoryData,
    handleCurrentChange: handleHistoryCurrentChange,
    handleSizeChange: handleHistorySizeChange,
    handleRefresh: handleHistoryRefresh,
  } = usePagination()
  const columns = computed(() => {
    return ["deducSelect", "deductionRecords"].includes(props.selType)
      ? TABLE_COLUMNS.filter((item) => item.label !== "不抵扣原因")
      : TABLE_COLUMNS
  })

  const tableData = ref<ITableData[]>([])
  const selectData = ref<ITableData[]>([])
  interface ITableData {
    adValoremTaxTotals: number
    amount: number
    deductionTax: number
    invCode: string
    invDate: string
    invNum: string
    invType: string
    invoiceNumber: string
    electricInvoice: string
    invoiceRiskLevel: string
    invokeD_RPA: string | null
    invoiceStatus: string
    isRedLock: string
    isSelected: string
    name: string
    nonDeductible: string
    nonDeductibleReason: string
    selectedDatetime: string
    tax: number
  }
  function getTableData() {
    const params = {
      invoicePeriodId: props.invoiceTaxPeriod,
      category: type.value,
      isSelected: status.value === "1" ? (["deducSelect", "deductionRecords"].includes(props.selType) ? "1" : "2") : "0",
      invDateStart: searchInfo.invDateStart,
      invDateEnd: searchInfo.invDateEnd,
      pageIndex: paginationData.currentPage,
      pageSize: paginationData.pageSize,
      invNum: searchInfo.invocieNum,
      invoiceTypes: searchInfo.invocieType.join(","),
      invoiceStatus: searchInfo.invocieStatus.join(","),
      salesName: searchInfo.sellerName,
      invoiceRiskLevel: searchInfo.riskLevel.join(","),
    }
    request({
      url: "api/invoice-selection/selected-list",
      params,
    }).then((res: any) => {
      if (res.state === 1000) {
        tableData.value = res.data.data
        paginationData.total = res.data.total
      }
    })
  }
  const selectRecordVisible = ref(false)
  function getTotalDeductibleAmount() {
    return selectData.value.reduce((total, item) => {
      return total + (item.deductionTax || 0)
    }, 0)
  }
  function handleOpenPageSelectDialog() {
    selectRecordVisible.value = true
    nextTick(() => {
      selectedTableRef.value?.TableComponents?.toggleAllSelection()
    })
  }
  const lastestTime = ref("")
  const collectRecordVisible = ref(false)
  const collectData = ref([])
  function getLatest() {
    const type =
      status.value === "0"
        ? LatestParamsType.未勾选发票
        : ["deducSelect", "deductionRecords"].includes(props.selType)
          ? LatestParamsType.已勾选发票抵扣
          : LatestParamsType.已勾选发票不抵扣
    getLatestUpdateTime({ type }).then((res) => {
      if (res.state === 1000) {
        lastestTime.value = res.data
      }
    })
  }
  function showUpdateHistory() {
    const params = {
      "paging.pageIndex": paginationHistoryData.currentPage,
      "paging.pageSize": paginationHistoryData.pageSize,
    }
    request({
      url: "api/task/gather",
      params,
    }).then((res: any) => {
      if (res.state === 1000) {
        collectData.value = res.data.data
        paginationHistoryData.total = res.data.total
      }
    })
    collectRecordVisible.value = true
  }
  getLatest()
  const collectInvoiceVisible = ref(false)
  const collectInvoiceData = () => {
    // 采集发票数据的逻辑
    const type =
      status.value === "0"
        ? LatestParamsType.未勾选发票
        : ["deducSelect", "deductionRecords"].includes(props.selType)
          ? LatestParamsType.已勾选发票抵扣
          : LatestParamsType.已勾选发票不抵扣
    request({
      url: "/api/task/running?taskType=" + type,
    }).then((res: IResponseModel<boolean>) => {
      if (res.data) {
        showUpdateHistory()
        ElNotify({
          type: "warning",
          message: "发票正在采集中",
        })
      } else {
        collectInvoiceVisible.value = true
      }
    })
  }
  const collectTipVisible = ref(false)
  let countdownTimer: ReturnType<typeof setTimeout> | null = null
  let collectTimer: ReturnType<typeof setTimeout> | null = null
  const gatherInvoice = () => {
    let apiUrl =
      status.value === "0"
        ? `api/invoice/gather/unchecked`
        : `api/invoice/gather/checked/${["deducSelect", "deductionRecords"].includes(props.selType) ? "Deduction" : "NonDeduction"}`
    if (props.selType === "deductionRecords") {
      apiUrl = "api/invoice/gather/deduction"
    }
    const params =
      status.value === "0"
        ? { start: searchInfo.invDateStart, end: searchInfo.invDateEnd, periodId: "202505" }
        : ["deducSelect", "nonDeducSelect"].includes(props.selType)
          ? { invoicePeriodId: "202505" }
          : "start" in searchInfo && "end" in searchInfo
            ? { start: searchInfo.start, end: searchInfo.end }
            : {}
    request({
      url: apiUrl,
      params,
      method: "post",
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000) {
        collectTipVisible.value = true
        collectInvoiceVisible.value = false
        countdownTimer = setTimeout(() => {
          collectTipVisible.value = false
          clearTimeout(countdownTimer as ReturnType<typeof setTimeout>)
        }, 5000)
        collectTimer = setInterval(() => {
          checkUpdateStatus({ taskId: res.data }).then((res) => {
            if (res.state !== 1000) return
            switch (res.data) {
              case EnumUpdateStatus.失败:
                ElNotify({
                  type: "info",
                  message: "发票数据采集失败",
                })
                clearInterval(collectTimer as ReturnType<typeof setInterval>)
                break
              case EnumUpdateStatus.成功:
                ElNotify({
                  type: "success",
                  message: "发票数据采集成功",
                })
                getTableData()
                clearInterval(collectTimer as ReturnType<typeof setInterval>)
                break
            }
          })
        }, 5000)
      } else {
        ElNotify({
          type: "error",
          message: "发票数据采集失败",
        })
      }
    })
  }

  const handleSubmitSelected = () => {
    request({
      url: "/api/invoice-selection/status?invoicePeriodId=" + props.invoiceTaxPeriod,
    }).then((res: IResponseModel<number>) => {
      // 处理返回结果
      if (res.data !== 0) {
        ElConfirm({
          message: "当前税款所属期已完成统计，请撤销统计后再提交勾选",
          buttons: { confirm: "撤销统计" },
        })
        return
      }
      if (!selectData.value.length) {
        ElNotify({
          message: "请至少选择一条发票数据",
          type: "warning",
        })
        return
      }
      ElConfirm({
        title: "确认提交",
        message: `本次勾选发票：${selectData.value.length}份，金额合计：${getTotalDeductibleAmount()}元，有效抵扣税额合计：${getTotalDeductibleAmount()}元，是否确认提交？`,
        onConfirm: () => {
          request({
            url: "api/invoice-selection/save-deduction?invoicePeriodId=" + props.invoiceTaxPeriod,
            method: "post",
            data: selectData.value.map((item) => ({
              invoiceNumber: item.invoiceNumber,
              invokeD_RPA: item.invokeD_RPA,
            })),
          }).then((res: IResponseModel<boolean>) => {
            // 处理返回结果
            request({
              url: "/api/invoice-selection/invselected-taskstatus?invoicePeriodId=202505&taskId=" + res.data,
            }).then((res: IResponseModel<boolean>) => {})
          })
        },
      })
    })
  }
  const noDeductibleReasonVisible = ref(false)
  const nonDeductionReason = ref("")
  const batchSetNonDeductibleReason = () => {
    if (!tableRef.value?.TableComponents.getSelectionRows().length) {
      ElNotify({
        message: "请至少选择一条发票数据",
        type: "warning",
      })
      return
    }
    noDeductibleReasonVisible.value = true
  }
  const handleBatchSetNonDeductibleReason = () => {
    selectData.value.forEach((item) => {
      item.nonDeductibleReason = nonDeductionReason.value
    })
  }

  watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], getTableData)

  watch(
    () => props.invoiceTaxPeriod,
    () => {
      const year = parseInt(props.invoiceTaxPeriod.substring(0, 4))
      const month = props.invoiceTaxPeriod.substring(4, 6)
      const lastYear = year - 1
      searchInfo.invDateStart = `${lastYear}-${month}-01`
      searchInfo.invDateEnd = `${year}-${month}-${new Date(year, parseInt(month), 0).getDate()}`
      if ("start" in searchInfo) {
        searchInfo.start = `${lastYear}${month}`
        searchInfo.end = `${year}${month}`
      }
      handleSearch()
    },
  )
</script>

<style scoped lang="scss">
  .collect-invoice-confirm {
    padding: 20px 0;
    .confirm-content-field {
      line-height: 32px;
      display: flex;
      .line-item-title {
        width: 80px;
      }
      .line-item-field {
        display: flex;
        flex-direction: row;
      }
    }
  }
  .main-content-body {
    flex: 1;
    overflow: hidden;
  }
  .table {
    height: 100%;
  }
</style>
