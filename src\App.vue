<template>
  <div class="main-container">
    <!-- 左侧区域 -->
    <aside class="left-container">
      <div class="logo"></div>
      <div class="menu-container">
        <template
          v-for="(menuNode, index) in menuList"
          :key="index">
          <div
            class="menu-node-container"
            @mouseenter="menuFocus($event)"
            @mouseleave="menuBlur($event)"
            @click.prevent="menuClick(menuNode.node)">
            <div class="menu-node-content">
              <!-- <img
                class="menu-icon"
                :src="getIcon(menuNode.node.functionId, false)" /> -->
              <span>{{ menuNode.node.functionName }}</span>
            </div>
            <div class="children-more-icon"></div>
            <div
              class="children-menu-node-content"
              v-if="menuNode.children?.length">
              <a
                v-for="(childNode, index) in menuNode.children"
                :key="index"
                :href="childNode.node.url"
                @click.prevent="menuClick(childNode.node)">
                <span>{{ childNode.node.functionName }}</span>

                <!-- <span class="hot"></span> -->
              </a>
            </div>
          </div>
        </template>
      </div>
    </aside>

    <!-- 右侧区域 -->
    <main class="right-container">
      <!-- 顶部工具栏 -->
      <header class="top-container">
        <div class="top-left-container">
          <div class="company-info w-180 mr-10 ml-10">
            <el-select-v2
              v-model="currentAsId"
              :options="asListOptions"
              :filterable="true"
              placeholder=""
              :teleported="false"
              :scrollbar-always-on="true"
              @change="companyChange" />
          </div>
          <div class="credit-grade">
            纳税信用等级：
            <span>{{ taxCreditGrade }}级</span>
          </div>
        </div>

        <div class="top-right-container">
          <a
            class="mr-10 button"
            type="default"
            @click="goToAccounting">
            去记账
          </a>
          <div class="tax-period mr-10">
            <span>税款所属期：</span>
            <el-date-picker
              v-model="currentPeriod"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              placeholder="选择税款所属日期"
              :disabled-date="taxPeriodStore.disabledDate"
              class="date-picker" />
          </div>
        </div>
      </header>

      <!-- 标签页区域 -->
      <div class="tabs-container">
        <div
          class="tab-nav-btn tab-left-btn"
          @click="tabToLeft"></div>

        <el-scrollbar
          ref="tabScrollbarRef"
          @scroll="handleTabScroll"
          :height="36">
          <div
            ref="tabScrollbarInnerRef"
            class="tabs-content"
            @dragstart="handleDragStart"
            @dragenter="handleDragEnter"
            @dragend="handleDragEnd">
            <div
              v-for="(item, index) in routerArray"
              :key="item.path"
              class="tab-content"
              :class="{
                'is-alive': item.alive,
                'can-close': true,
                'can-refresh': item.alive,
              }"
              :draggable="true"
              @click="item.alive ? null : clickTabs(item)"
              @dblclick="index === 0 ? null : routerArrayStore.removeRouter(item.path)"
              @contextmenu="tabRightClick($event, item)">
              {{ item.title }}

              <div
                v-if="item.alive"
                class="tab-icon refresh-btn"
                @click.stop="handleRefreshTab(item.path)"></div>

              <div
                v-if="item.name !== 'TaxDeclaration'"
                class="tab-icon close-btn"
                @click.stop="closeBtnClick(item.path)"></div>
            </div>
          </div>
        </el-scrollbar>

        <div
          class="more-btn"
          @click="moreBtnClick"></div>

        <div
          class="tab-nav-btn tab-right-btn"
          @click="tabToRight"></div>
      </div>

      <!-- 主内容区 -->
      <div class="router-container">
        <router-view v-slot="{ Component }">
          <keep-alive :include="keepAliveComponents">
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </main>

    <!-- 右键菜单 -->
    <!-- <div
      v-show="!!currentRightClickTab"
      ref="tabRightMenuRef"
      class="context-menu">
      <div
        v-if="currentRightClickTab?.alive"
        class="menu-item"
        @click="handleRefreshTab(currentRightClickTab.path)">
        刷新当前页面
      </div>
      <div
        class="menu-item"
        @click="closeBtnClick(currentRightClickTab?.path || '')">
        关闭标签页
      </div>
    </div> -->
  </div>
  <div
    class="tab-right-menu"
    v-show="currentRightClickTab !== undefined"
    @click="$event.stopPropagation()"
    ref="tabRightMenuRef">
    <div
      class="menu-btn close-all"
      @click="rightMenuCloseAll()">
      关闭全部
    </div>
    <div
      class="menu-btn close-others"
      @click="rightMenuCloseOthers()">
      关闭其他页
    </div>
    <div
      class="menu-btn close-current"
      @click="rightMenuCloseCurrent()">
      关闭当前页
    </div>
    <div
      class="menu-btn refresh-current"
      @click="rightMenuRefreshCurrent()">
      刷新当前页
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { ElScrollbar } from "element-plus"
  import { useVisitedRoutesStore, type IRouterModel } from "@/store/modules/visitedRoutes"
  import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { globalWindowOpenPage, globalWindowOpen, getAppasid } from "./utils/url"
  import { request, IResponseModel } from "@/utils/service"
  import router from "@/router"
  import { useTaxBureau, TaskType, TaxBureauLoginType } from "@/hooks/useTaxBureau.ts"
  import { useUserInfoStore } from "./store/modules/userInfo"

  const { checkLoginState } = useTaxBureau()

  interface TabDragState {
    currentTab: HTMLElement | null
    enterTab: HTMLElement | null
    draggedIndex: number
  }
  const { getBasicInfo, getAccountSetList } = useBasicInfoStore()
  // 公司选择相关
  const currentAsId = ref("")
  let asListOptions = reactive<{ [key: string]: any }[]>([])
  getAccountSetList().then((res) => {
    asListOptions = res.map((item) => {
      return {
        label: item.asName,
        value: item.asId,
        appAsId: item.appAsId,
      }
    })
    if (!getAppasid()) {
      window.location.href = window.location.origin + "/TaxDeclaration?appasid=" + res[0].appAsId
    }
    getBasicInfo().then(async (res: { [key: string]: any }) => {
      currentAsId.value = res.asId
      taxCreditGrade.value = res.creditLevel
      checkLoginState(TaskType.TaxBureau, TaxBureauLoginType.Login)
    })
  })

  function companyChange(value: string | number) {
    const selectedCompany = asListOptions.find((item) => item.value === value)
    if (!selectedCompany) return
    window.location.href = window.location.origin + "/TaxDeclaration?appasid=" + selectedCompany.appAsId
  }
  // 纳税信用等级
  let taxCreditGrade = ref("")

  // 获取用户信息
  const { getUserInfo } = useUserInfoStore()
  getUserInfo()

  const taxPeriodStore = useTaxPeriodStore()
  const { currentPeriod, currentPeriodId } = storeToRefs(taxPeriodStore)

  function goToAccounting() {
    globalWindowOpen(window.jAccH5Url + "/Default/Default")
  }

  // 标签页状态管理
  const routerArrayStore = useVisitedRoutesStore()
  const routerArray = toRef(routerArrayStore, "routerArray")
  const keepAliveComponents = computed(() => routerArray.value.filter((item) => item.cache).map((item) => item.name))
  const currentRightClickTab = ref<IRouterModel>()

  //菜单
  interface IMenuModel {
    functionName: string
    url: string
  }
  const menuList = ref([
    {
      node: { functionName: "发票勾选", url: "" },
      children: [
        { node: { functionName: "抵扣勾选", url: "/InvoiceSelection/DeductionSelection" }, children: [] },
        { node: { functionName: "不抵扣勾选", url: "/InvoiceSelection/NonDeductionSelection" }, children: [] },
        { node: { functionName: "统计确认", url: "/InvoiceSelection/DeductionSelection?tabName=statConfirm" }, children: [] },
        { node: { functionName: "往期认证结果", url: "/InvoiceSelection/CertificationResults" }, children: [] },
      ],
    },
    {
      node: { functionName: "税种申报", url: "/TaxDeclaration" },
    },
    {
      node: { functionName: "税费种管理", url: "/TaxManagement", children: [] },
      children: [],
    },
    {
      node: { functionName: "报税设置", url: "/TaxReportSettings", children: [] },
    },
    {
      node: { functionName: "发票获取", url: "/InvoiceAccess" },
    },
  ])
  // request({ url: "/api/menu/list" }).then((res) => {
  //   if (res.state === 1000 && res.data) {
  //     menuList.value = res.data
  //   }
  // })
  const menuFocus = (event: any) => {
    const childrenMenuNodeContent = event.currentTarget.querySelector(".children-menu-node-content")
    if (!childrenMenuNodeContent) return
    const offset = childrenMenuNodeContent.getBoundingClientRect()
    const height = childrenMenuNodeContent.offsetHeight
    if (offset.top + height > window.innerHeight) {
      childrenMenuNodeContent.style.top = window.innerHeight - offset.top - height - 10 + "px"
    }
  }
  const menuBlur = (event: any) => {
    const childrenMenuNodeContent = event.currentTarget.querySelector(".children-menu-node-content")
    childrenMenuNodeContent && (childrenMenuNodeContent.style.top = 0)
  }
  const menuClick = (item: IMenuModel) => {
    if (item.url) {
      globalWindowOpenPage(item.url, item.functionName)
    }
  }

  // 滚动相关
  const tabScrollbarRef = ref<InstanceType<typeof ElScrollbar>>()
  const tabScrollbarInnerRef = ref<HTMLElement>()
  const tabScrollLeft = ref(0)

  // 拖拽状态
  const dragState = ref<TabDragState>({
    currentTab: null,
    enterTab: null,
    draggedIndex: -1,
  })

  // 标签页操作
  const clickTabs = async (item: IRouterModel) => {
    if (!item.alive) {
      await router.push(item.fullPath)
    }
  }

  const handleTabScroll = ({ scrollLeft }: { scrollLeft: number }) => {
    tabScrollLeft.value = scrollLeft
  }

  const tabToLeft = () => {
    if (!tabScrollbarRef.value) return
    const newScrollLeft = Math.max(0, tabScrollLeft.value - 100)
    tabScrollbarRef.value.setScrollLeft(newScrollLeft)
  }

  const tabToRight = () => {
    if (!tabScrollbarRef.value) return
    tabScrollbarRef.value.setScrollLeft(tabScrollLeft.value + 100)
  }

  // 拖拽相关
  const handleDragStart = (event: DragEvent) => {
    const target = event.target as HTMLElement
    if (!target.classList.contains("tab-content")) return

    dragState.value.currentTab = target
    dragState.value.draggedIndex = Array.from(tabScrollbarInnerRef.value?.children || []).indexOf(target)

    nextTick(() => target.classList.add("moving"))
  }

  const handleDragEnter = (event: DragEvent) => {
    event.preventDefault()
    if (!dragState.value.currentTab) return

    const target = event.target as HTMLElement
    if (!isValidDropTarget(target)) return

    handleTabSwap(target)
  }

  const handleDragEnd = () => {
    if (!dragState.value.currentTab) return

    dragState.value.currentTab.classList.remove("moving")
    updateTabsOrder()
    resetDragState()
  }

  const isValidDropTarget = (target: HTMLElement): boolean => {
    if (!target.classList.contains("tab-content")) return false
    if (target === dragState.value.currentTab) return false
    if (target === dragState.value.enterTab) return false

    const targetIndex = Array.from(tabScrollbarInnerRef.value?.children || []).indexOf(target)

    return !(targetIndex === 0 && dragState.value.draggedIndex !== 1)
  }

  const handleTabSwap = (target: HTMLElement) => {
    const container = tabScrollbarInnerRef.value
    if (!container || !dragState.value.currentTab) return

    if (target === container || !target.classList.contains("tab-content")) return

    const children = Array.from(container.children)
    const currentIndex = children.indexOf(dragState.value.currentTab)
    const targetIndex = children.indexOf(target)

    if (currentIndex < targetIndex) {
      container.insertBefore(dragState.value.currentTab, target.nextElementSibling)
    } else {
      container.insertBefore(dragState.value.currentTab, target)
    }

    dragState.value.enterTab = target
  }

  const updateTabsOrder = () => {
    nextTick(() => {
      const titles = Array.from(tabScrollbarInnerRef.value?.children || []).map((el) => (el as HTMLElement).innerText)

      const indexMap = new Map(titles.map((title, index) => [title, index]))

      routerArray.value.sort((a, b) => {
        const indexA = indexMap.get(a.title) ?? 0
        const indexB = indexMap.get(b.title) ?? 0
        return indexA - indexB
      })
    })
  }

  const resetDragState = () => {
    dragState.value = {
      currentTab: null,
      enterTab: null,
      draggedIndex: -1,
    }
  }

  // 标签页右键菜单
  const tabRightMenuRef = ref<HTMLElement>()

  const tabRightClick = (event: MouseEvent, tab: IRouterModel) => {
    event.preventDefault()
    event.stopPropagation()

    currentRightClickTab.value = tab
    const windowWidth = window.innerWidth

    nextTick(() => {
      const menuRef = tabRightMenuRef.value
      if (!menuRef) return

      const menuRect = menuRef.getBoundingClientRect()
      const left = menuRect.width + event.clientX > windowWidth ? event.clientX - menuRect.width : event.clientX

      menuRef.style.left = `${left}px`
      menuRef.style.top = `${event.clientY}px`
      menuRef.style.display = "block"
    })
  }

  const handleRefreshTab = (path: string) => {
    routerArrayStore.refreshRouter(path)
    currentRightClickTab.value = undefined
  }

  const closeBtnClick = async (path: string) => {
    const tab = routerArray.value.find((item) => item.path === path)
    if (!tab) return

    routerArrayStore.removeRouter(path)
    currentRightClickTab.value = undefined
  }

  const moreBtnClick = (event: MouseEvent) => {
    event.stopPropagation()
    currentRightClickTab.value = routerArray.value.find((item) => item.alive)

    const target = event.currentTarget as HTMLElement
    const targetRect = target.getBoundingClientRect()
    const menuRef = tabRightMenuRef.value
    if (!menuRef) return

    nextTick(() => {
      const menuRect = menuRef.getBoundingClientRect()
      const windowWidth = window.innerWidth

      const left =
        menuRect.width + (targetRect.left - 6) > windowWidth ? targetRect.left + target.offsetHeight - menuRect.width : targetRect.left - 6

      menuRef.style.left = `${left}px`
      menuRef.style.top = `${targetRect.top + target.offsetHeight + 11}px`
    })
  }

  const rightMenuCloseAll = () => {
    routerArrayStore.removeAllRouter()
    currentRightClickTab.value = undefined
  }
  const rightMenuCloseOthers = () => {
    routerArrayStore.removeOthersRouter(currentRightClickTab.value!.path)
    currentRightClickTab.value = undefined
  }
  const rightMenuCloseCurrent = () => {
    const path = currentRightClickTab.value!.path
    routerArrayStore.removeRouter(path)
    currentRightClickTab.value = undefined
  }
  const rightMenuRefreshCurrent = () => {
    routerArrayStore.refreshRouter(currentRightClickTab.value!.path)
    currentRightClickTab.value = undefined
  }

  onMounted(() => {
    document.addEventListener("click", () => {
      if (tabRightMenuRef.value) {
        tabRightMenuRef.value.style.display = "none"
      }
    })
  })

  onBeforeUnmount(() => {
    document.removeEventListener("click", () => {
      if (tabRightMenuRef.value) {
        tabRightMenuRef.value.style.display = "none"
      }
    })
  })
</script>

<style lang="scss">
  .main-container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
    background-color: #f6f6f6;
    .left-container {
      width: var(--menu-container-width);
      background-color: var(--menu-bg-color);
      .logo {
        height: 50px;
        background-repeat: no-repeat;
        background-size: 100%;
        background-image: url("");
      }
    }
    .menu-container {
      .menu-node-container {
        display: flex;
        align-items: stretch;
        margin-top: 1px;
        cursor: pointer;
        outline: none;
        text-decoration: none;
        position: relative;

        &::before {
          content: " ";
          align-self: stretch;
          width: 3px;
          background-color: transparent;
          position: absolute;
          height: 100%;
        }

        .menu-node-content {
          flex: 1;
          padding: 10px 12px;
          height: 28px;
          display: flex;
          align-items: center;

          .menu-icon {
            width: 14px;
            height: 14px;
            margin-left: 6px;
            margin-right: 6px;

            &.selected {
              display: none;
            }
          }

          span {
            font-size: 16px;
            line-height: 22px;
            color: var(--menu-font-color);
            font-weight: 500;
          }
        }

        .children-more-icon {
          align-self: center;
          display: none;
          width: 0;
          height: 0;
          border-top: 6px solid transparent;
          border-bottom: 6px solid transparent;
          border-right: 9px solid var(--white);
          margin-left: -9px;
        }

        .children-menu-node-content {
          width: 164px;
          position: absolute;
          top: 0;
          left: 100%;
          display: none;
          flex-direction: column;
          align-items: flex-start;
          background: var(--white);
          box-shadow: 2px 4px 15px 0px rgba(17, 31, 65, 0.2);
          z-index: 9999;
          cursor: default;
          padding: 8px 0 8px 2px;

          a {
            outline: none;
            text-decoration: none;
            text-align: left;
            color: #111;
            line-height: 20px;
            font-size: 14px;
            cursor: pointer;
            height: 40px;
            line-height: 40px;
            width: 100%;
            position: relative;
            padding-left: 28px;
            box-sizing: border-box;

            &:hover {
              color: var(--white);
              background-color: var(--main-color);
            }

            .hot {
              display: inline-block;
              width: 21px;
              height: 12px;
              background-image: url("@/assets/Icons/hot.png");
              background-repeat: no-repeat;
              background-size: 100% 100%;
              position: absolute;
              margin-left: 5px;
              margin-top: 5px;
            }
          }
        }
        &:hover {
          .menu-node-content {
            background: rgba(255, 255, 255, 0.15);
          }

          .children-menu-node-content {
            display: flex;
          }
        }
      }
    }
    .right-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      width: 0;
      overflow: auto;
      overflow-x: hidden;

      .top-container {
        height: 44px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        background-color: var(--white);
        .top-left-container,
        .top-right-container {
          display: flex;
          align-items: center;
        }
        .center-container {
          display: flex;
          justify-content: space-between;
          background-color: #fff;
          margin: 5px 4px 0;
          align-items: center;
        }

        .credit-grade {
          font-size: var(--h4);
          span {
            color: var(--light-blue);
          }
        }
      }
    }
  }
  .tabs-container {
    height: 36px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 5px 4px 0;
    background-color: var(--white);
    padding-left: 24px;
    padding-right: 24px;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    .tab-left-btn,
    .tab-right-btn {
      height: 20px;
      width: 20px;
      background-size: 100%;
      background-repeat: no-repeat;
      cursor: pointer;
      flex-shrink: 0;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    .tab-left-btn {
      left: 0;
    }
    .tab-right-btn {
      right: 0;
    }

    .tab-left-btn {
      margin-right: 4px;
      background-image: url("@/assets/Menu/left.png");

      &:hover {
        background-image: url("@/assets/Menu/left-hover.png");
      }
    }

    .tab-right-btn {
      margin-left: 4px;
      background-image: url("@/assets/Menu/right.png");

      &:hover {
        background-image: url("@/assets/Menu/right-hover.png");
      }
    }

    .more-btn {
      height: 20px;
      width: 20px;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: center;
      cursor: pointer;
      flex-shrink: 0;
      margin-left: 4px;
      background-image: url("@/assets/Menu/more.png");

      &:hover {
        background-image: url("@/assets/Menu/more-hover.png");
      }
    }

    .tabs-content {
      display: flex;
      align-items: center;
      height: 36px;

      .tab-content {
        display: flex;
        align-items: center;
        height: 28px;
        background-color: #f1f3f9;
        padding: 0 24px;
        margin-left: 4px;
        margin-right: 4px;
        font-size: 14px;
        font-weight: 400;
        color: #666666;
        line-height: 24px;
        cursor: pointer;
        border-radius: 1px;
        flex-shrink: 0;
        &.moving {
          transition: all 0.5s ease;
          background-color: transparent !important;
          color: transparent !important;
          border: 1px dashed #f1f3f9;
          .close-btn {
            visibility: hidden !important;
          }
          .refresh-btn {
            visibility: hidden !important;
          }
        }
        &.is-alive {
          background-color: var(--main-color);
          color: var(--white);
          &.can-refresh {
            padding: 0 10px 0 16px;

            .refresh-btn {
              display: block;
            }

            .close-btn {
              margin-left: 4px;
            }
          }
        }

        .close-btn {
          display: none;
          background-image: url("@/assets/Menu/close-btn.png");
          background-repeat: no-repeat;
          background-size: 100%;
          height: 17px;
          width: 17px;
          cursor: pointer;
          margin-left: 7px;
        }

        .refresh-btn {
          display: none;
          background-image: url("@/assets/Menu/tab-refresh-btn.png");
          background-repeat: no-repeat;
          background-size: 100%;
          height: 20px;
          width: 20px;
          cursor: pointer;
          margin-left: 4px;
        }

        &.can-close {
          padding: 0 10px 0 16px;

          .close-btn {
            display: block;
          }
        }

        &.alive {
          background-color: var(--main-color);
          color: var(--white);

          .close-btn {
            background-image: url("@/assets/Menu/close-btn-alive.png");
          }

          &.can-refresh {
            padding: 0 10px 0 16px;

            .refresh-btn {
              display: block;
            }

            .close-btn {
              margin-left: 4px;
            }
          }

          &.can-close {
            &.can-refresh {
              padding-right: 8px;
            }
          }
        }

        & + .tab-content {
          margin-left: 0;
        }
      }
    }

    :deep(.el-scrollbar__bar) {
      display: none !important;
    }
  }

  .router-container {
    position: relative;
    flex: 1;
    margin: var(--router-container-margin);
    background-color: var(--white);
    box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
    border-radius: 4px;
    overflow-x: hidden;
  }

  .tab-right-menu {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 132px;
    padding: 10px 0;
    z-index: 9999;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.85);
    box-shadow: 2px 4px 15px 0px rgba(17, 31, 65, 0.1);
    border: 1px solid #f2f2f2;
    backdrop-filter: blur(6px);

    .menu-btn {
      padding: 9px 0 8px 15px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: var(--font-color);
      line-height: 19px;
      cursor: pointer;

      &::before {
        content: " ";
        height: 17px;
        width: 17px;
        background-size: 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      &:hover {
        color: var(--main-color);
      }

      &.close-all {
        &::before {
          background-image: url("@/assets/Menu/right-menu-close-all-icon.png");
        }

        &:hover {
          &::before {
            background-image: url("@/assets/Menu/right-menu-close-all-hover-icon.png");
          }
        }
      }

      &.close-others {
        &::before {
          background-image: url("@/assets/Menu/right-menu-close-others-icon.png");
        }

        &:hover {
          &::before {
            background-image: url("@/assets/Menu/right-menu-close-others-hover-icon.png");
          }
        }
      }

      &.close-current {
        &::before {
          background-image: url("@/assets/Menu/right-menu-close-current-icon.png");
        }

        &:hover {
          &::before {
            background-image: url("@/assets/Menu/right-menu-close-current-hover-icon.png");
          }
        }
      }

      &.refresh-current {
        &::before {
          background-image: url("@/assets/Menu/right-menu-refresh-current-icon.png");
        }

        &:hover {
          &::before {
            background-image: url("@/assets/Menu/right-menu-refresh-current-hover-icon.png");
          }
        }
      }
    }
  }
</style>
