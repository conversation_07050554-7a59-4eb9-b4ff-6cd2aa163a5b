<script setup lang="ts">
import { ref, watch, computed, nextTick, toRef, watchEffect, onMounted,  onUnmounted, provide } from "vue";
import { RouterView, useRoute } from "vue-router";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import AccountSetExpire from "@/components/AccountSetExpire/index.vue";
import ChangeAACompanyDialog from "@/components/ChangeAACompanyDialog/index.vue";
import { request, type IResponseModel } from "./util/service";
import { getGlobalToken } from "./util/baseInfo";
import { EnterLockedAccountSetDialog } from "@/util/enterLockedAccountSet";
import { useAccountPeriodStore } from "./store/modules/accountPeriod";
import { PeriodStatus } from "./api/period";
import { useAccountSetStore, useAccountSetStoreHook } from "./store/modules/accountSet";
import { change2newasid } from "./views/Settings/AccountSets/utils";
import { isInWxWork } from "./util/wxwork";
import { isWithin24Hours } from "./util/date";
import {
    setTopLocationhref,
    globalWindowOpen,
    globalWindowOpenPage,
    appendAACompanyId,
    appendAppasid,
    logout,
    globalWindowOpenErp,
    getUrlSearchParams,
} from "./util/url";
import { useUserStore } from "./store/modules/user";
import { tryGoToPro, goToFree, getHasProAccountSet, buyPro } from "./util/proUtils";
import { useRouterArrayStoreHook, type IRouterModel } from "./store/modules/routerArray";
import { tryShowPayDialog } from "./util/proPayDialog";
import { useTrialStatusStore } from "./store/modules/trialStatus";
import { useDialogStore } from "./store/modules/fullDialog";
import TaxCalculator from "@/components/Dialog/TaxCalculator/index.vue";
import type { ElScrollbar } from "element-plus";
import { getLemonClient, isLemonClient } from "./util/lmClient";
import ProWorkBenchDialog from "@/components/ProWorkBenchDialog/index.vue";
import { getAccountList, type IAccountList, type ITopInfo } from "./api/getAccountList";
import LMCalculator from "@/components/LMCalculator/index.vue";
import { getCookie } from "./util/cookie";
import { gotoBuy, getServiceId } from "./util/proUtils";
import { initWxworkConfig } from "@/components/ProWorkBenchDialog/utils";
import WxworkPermissionAlertDialog from "@/views/Settings/Permissions/components/WxworkPermissionAlertDialog.vue";
import ToolTip from "@/components/Tooltip/index.vue";
import { useThirdPartInfoStoreHook, useThirdPartInfoStore } from "./store/modules/thirdpart";
import { showAddServiceDialog } from "./util/showAddService";
import { erpNotifyTypeEnum, erpNotify } from "./util/erpUtils";
import dayjs from "dayjs";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { menuClickKey } from "@/views/Default/types";
import { pageScrollKey } from "./symbols";
import router from "@/router";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { ElNotify } from "./util/notify";
import { BannerEnum } from "@/views/Default/types";
import { getRightsImg } from "@/components/ExpiredToBuyDialog/utils";

const isAlipayImportJournalRedirectRoute = window.location.pathname === "/Cashier/AlipayImportJournalRedirectRoute";

const fullScreen = toRef(useFullScreenStore(), "fullScreen");
const rightsList = [
    { name: "发票管理", order: 1 },
    { name: "一键取票", order: 2 },
    { name: "资金管理", order: 3 },
    { name: "银企互联", order: 4 },
    { name: "资产管理", order: 5 },
    { name: "工资管理", order: 6 },
    { name: "10G空间", order: 7 },
    { name: "查看更多", order: 8 },
];

// 先给个跳转列表，感觉这样安全一些...
const messageOpenPageList = [
    "/Default/PersonalInfo",
    "/MasterPages/AccountingEntry",
    "/MasterPages/MessageSearch",
    "/Settings/TransferPro",
    "/Pro/PayDialog",
];

const route = useRoute();
window.addEventListener("message", function (e) {
    // if (e.origin !== window.jHost) return;
    if (e.data.type === "reloadAccountSetList") {
        loadAccountSetList();
    }
    if (e.data.type === "setSelectedAccount") {
        currentAsId.value = e.data.nextAsId;
    }
    if (e.data.type === "setSelectedAccountWithoutChange") {
        setSelectedAccountWithoutChange(e.data.nextAsId);
    }
    if (e.data.type === "loadMenu") {
        loadMenu();
    }
    if (e.data.type === "refreshCurrent") {
        routerArrayStore.refreshRouter(route.path);
    }
    if (e.data.type === "proRemainingDays") {
        proRemainingDays.value = e.data.proRemainingDays;
    }
    if (e.data.type === "gotoWorkBench") {
        gotoWorkBench();
    }

    if (e.data.type === "globalWindowOpenPage") {
        let url = e.data.url;
        if (url.indexOf("?") >= 0) url = url.substring(0, url.indexOf("?"));
        if (messageOpenPageList.includes(url)) globalWindowOpenPage(e.data.url, e.data.name);
    }
    if (e.data.type === "ycOpen") {
        let url = e.data.path;
        const query = e.data.query || {};
        if (e.data.path === "/Cashier/Journal") {
            const page = query.Page;
            switch (page) {
                case "CD":
                    url = "/Cashier/CDJournal";
                    break;
                case "IE":
                    url = "/Cashier/IEJournal";
                    break;
                case "COMBINATION":
                    url = "/Cashier/CombineJournal";
                    break;
                default:
                    url = "/Cashier/AAJournal";
                    break;
            }
        }
        if (url.indexOf("?") < 0 && Object.keys(e.data.query).length) {
            url = url + "?" + getUrlSearchParams(e.data.query);
        }
        globalWindowOpenErp(url, e.data.name);
    }
    if (e.data.type === "ycClose") {
        rightMenuCloseCurrent(e.data.path);
    }
    if (e.data.type === "ycCloseAll") {
        router.push(appendAppasid('/MidPage?stay=true'))
        rightMenuCloseAll();
    }
    if (e.data.type === "ycRefresh") {
        rightMenuRefreshCurrent(e.data.path);
    }
    if (e.data.type === "ycCloseOthers") {
        rightMenuCloseOthers(e.data.path);
    }
    if (e.data.type === "ycChange") {
        router.push(appendAppasid('/MidPage?stay=true'))
    }
});
const isProSystem = ref(window.isProSystem);
const isBossSystem = ref(window.isBossSystem);
const isAccountingAgent = ref(window.isAccountingAgent);
const isWxwork = ref(isInWxWork());
const isInLemonClient = ref(isLemonClient());
const visible = ref(false);
const visibleDownUp = ref(true);
const clickDownNum = ref(0);
const visibleFullScreen = ref(true);
const visibleTopBar = ref(true);

const isAccountSet1 = computed(() => {
    return window.location.pathname === "/Settings/AccountSets1";
});

//点击收起按钮
const clickDown = () => {
    clickDownNum.value++;
    visibleDownUp.value = false;
    visibleTopBar.value = false;
    visible.value = false;
    const accountSetStore = useAccountSetStore();
    accountSetStore.getUserInfo().then(() => {
        const showVisible = useAccountSetStoreHook().userInfo?.userSn + "-" + getGlobalToken() + "-visibleClick";
        visible.value = true;
        if (!localStorage.getItem(showVisible)) {
            setTimeout(() => {
                visible.value = false;
                localStorage.setItem(showVisible, "2");
            }, 5000);
        } else {
            visible.value = false;
        }
    });
    // 改遮罩层top值
    document.documentElement.style.setProperty("--top-value", "53px");
};
//点击展开按钮
const clickUp = () => {
    visibleDownUp.value = true;
    visibleTopBar.value = true;
    visible.value = false;
    document.documentElement.style.setProperty("--top-value", "97px");
};
//点击全屏
const openFullScreen = () => {
    toggleFullscreen();
};
//退出全屏
const closeFullScreen = () => {
    toggleFullscreen();
};
//是否全屏切换方法
const toggleFullscreen = () => {
    let isFullscreen =
        !!(document as any).fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement;
    if (isFullscreen) {
        // 如果已经是全屏状态，则退出全屏
        exitFullscreen();
    } else {
        // 否则，进入全屏
        enterFullscreen();
    }
};
//进入全屏
const enterFullscreen = () => {
    const element: any = document.documentElement;
    if (element.requestFullscreen) {
        element.requestFullscreen();
        visibleFullScreen.value = false;
    } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
    } else if (element.webkitRequestFullScreen) {
        element.webkitRequestFullScreen();
    } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
    }
};
//退出全屏
const exitFullscreen = () => {
    if ((document as any).exitFullscreen) {
        (document as any).exitFullscreen();
        visibleFullScreen.value = true;
    } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
    } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
    } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
    }
};

//全屏状态下按esc或点击X退出全屏时将"退出全屏"改为"全屏"
const handleFullscreenChange = () => {
    visibleFullScreen.value = !document.fullscreenElement;
};
//按下F11键
const handleKeyDown = (event: any) => {
    if (event.key === "F11") {
        event.preventDefault();
        toggleFullscreen();
    }
};
const showFullscreenTime = () => {
    const accountSetStore = useAccountSetStore();
    accountSetStore.getUserInfo().then(() => {
        const showVisible = useAccountSetStoreHook().userInfo?.userSn + "-" + getGlobalToken() + "-visible";
        visible.value = true;
        if (!localStorage.getItem(showVisible) && !hideMenu.value) {
            setTimeout(() => {
                visible.value = false;
                localStorage.setItem(showVisible, "2");
            }, 5000);
        } else {
            visible.value = false;
        }
    });
};
const showFullscreen = ref(false);
const thirdPartInfoStore = useThirdPartInfoStoreHook();
// const isThirdPart = toRef(thirdPartInfoStore, "isThirdPart");
const isThirdPart = computed({
    get: () => {
        //vue-router在APP.vue只能获取到初始的route
        return thirdPartInfoStore.isThirdPart || window.location.href.includes("isthirdPart");
    },
    set: (val) => val,
});

//后续调整这个方法，没必要调用两次
async function isShowFullscreen() {
    !isAlipayImportJournalRedirectRoute &&
        (await useThirdPartInfoStore()
            .getThirdPartInfo()
            .then((res) => {
                isThirdPart.value = res.isThirdPart;
                request({
                    url: `${window.accountSrvHost}/api/QyWxHasDeptFollowUser.ashx?CurrentSystemType=1&systemType=${isProSystem.value ? 1002 : 1001}`,
                    method: "post",
                }) .then((res: any) => {
                    if (res.Data) {
                        isWxworkFollowUser.value = true;
                    }
                }).finally(() => {
                    countOpenTime();
                });
                if (!isWxwork.value && !isThirdPart.value) {
                    showFullscreen.value = true;
                    showFullscreenTime();
                } else {
                    showFullscreen.value = false;
                    showFullscreenTime();
                }
            }));
    return "加载第三方信息结束";
}
function raceFn() {
    return new Promise<any>((resolve) => setTimeout(() => resolve("获取第三方信息超时"), 1000));
}
function handleInit() {
    Promise.race([isShowFullscreen(), raceFn()]).then(() => {
        thirdParthasLoading.value = true;
        checkAndSetGray();
    });
}
handleInit();

const isErp = ref(window.isErp);
const thirdParthasLoading = ref(false);
const hideMenu = computed(() => {
    if (
        [
            "/AccountBooks/ExcessiveNumberOrRowsErrorPage",
            "/AccountBooks/ExistsAsyncFileProcessErrorPage",
            "/Client/ClientInit",
            "/410",
        ].indexOf(window.location.pathname) !== -1
    ) {
        return true;
    }
    if (window.isErp) {
        return true;
    }
    if (isThirdPart.value && !thirdPartInfoStore.isOEM) {
        return true;
    }
    return fullScreen.value;
});
const allowNoAppasid = computed(() => {
    if (
        !getGlobalToken() &&
        (window.location.pathname === "/Settings/AccountSets1" ||
            window.location.pathname === "/Settings/TransferPro" ||
            window.location.pathname === "/Pro/PayDialog" ||
            window.location.pathname === "/Default/PersonalInfo" ||
            window.location.pathname === "/MasterPages/MessageSearch" ||
            window.location.pathname === "/MasterPages/AccountingEntry" ||
            window.location.pathname === "/410" ||
            window.location.pathname === "/Cashier/AlipayImportJournalRedirectRoute")
    ) {
        return true;
    }
    return false;
});

const trialStatusStore = useTrialStatusStore();
const isTrial = computed(() => {
    return trialStatusStore.isTrial;
});
const isExpired = computed(() => {
    return trialStatusStore.isExpired;
});

const proExpiredTime = computed(() => {
    return trialStatusStore.proExpiredTime;
});
const remainingDays = computed(() => {
    return trialStatusStore.remainingDays;
});
const expiredTime = computed(() => {
    let now = dayjs();
    let specifiedDate = dayjs(trialStatusStore.expiredTime);
    let daysDiff = isExpired.value ? now.diff(specifiedDate, "day") : remainingDays.value;
    return daysDiff || '';
});
const proRemainingDays = ref(-1);

if (window.isErp) {
    document.body.attributes.setNamedItem(document.createAttribute("erp"));
}

interface IMenuModel {
    functionId: number;
    functionName: string;
    functionCode: string;
    url: string;
    parent: number;
    order: number;
    ftype: number;
    isBlank: number;
    icon: string;
    readOnly: boolean;
}

interface IMenuNode {
    node: IMenuModel;
    children: IMenuNode[];
}
const showAdContainer = ref(false);
const isShowGuidePace = ref(false);
const beginnersGuideBtnShow = computed(() => {
    return isShowGuidePace.value && isTrial.value && permission.value.split(",").indexOf("10001") >= 0 && !isThirdPart.value;
});
const beginnersGuidePaceRestSteps = ref(0);
function countZeroValues(obj: any) {
    let count = 0;
    for (let key in obj) {
        if (obj[key] === 0) {
            count++;
        }
    }
    return count;
}
const getNewbieGuidePace = () => {
    request({
        url: "/api/PaidConversion/NewbieGuidePace",
        method: "get",
    }).then(
        (
            res: IResponseModel<{
                isShowPace: boolean;
                paceItems: {
                    initStudy: number;
                    invoiceStudy: number;
                    cashierStudy: number;
                    assetsStudy: number;
                    voucherStudy: number;
                    diskStudy: number;
                };
            }>
        ) => {
            if (res.state === 1000) {
                beginnersGuidePaceRestSteps.value = countZeroValues(res.data.paceItems);
                isShowGuidePace.value = res.data.isShowPace;
            }else {
                isShowGuidePace.value = false;
            }
        }
    ).catch(() => {
        isShowGuidePace.value = false;
    });
};

const menuNodeList = ref<IMenuNode[]>([]);
const loadMenu = () => {
    // if(isAccountSet1.value) return;
    if (hideMenu.value) return;
    if (allowNoAppasid.value) {
        request({
            url: "/api/AccountSetMenuOnlyAuth",
            method: "get",
        }).then((res: IResponseModel<IMenuNode>) => {
            if (res.state === 1000) {
                menuNodeList.value = res.data.children;
                setScmMenu();
            }
        });
    } else {
        request({
            url: "/api/AccountSetMenu/GetCurrentMenu",
            method: "get",
        }).then((res: IResponseModel<IMenuNode>) => {
            if (res.state === 1000) {
                menuNodeList.value = res.data.children;
                setScmMenu();
            }
        });
    }
};
function setScmMenu() {
    const settings = menuNodeList.value.find((item) => item.node.functionId === ********);
    if (!settings) return;
    const scm = !!settings.children.find((item) => item.node.functionId === ********);
    localStorage.setItem("scmShow-" + getGlobalToken(), String(scm));
}
allowNoAppasid.value && !isAlipayImportJournalRedirectRoute && loadMenu();

const chiledrenMenuShow = toRef(useDialogStore(), "dialogCount");
const getIcon = (functionId: number, selected: boolean) => {
    return new URL("/Menu/" + functionId + (selected ? "-selected" : "") + ".png", import.meta.url).href;
};

const vipModuleCheck = (functionId: number) => {
    return [********, ********, ********, ********, ********, ********, ********, ********, ********].indexOf(functionId) !== -1;
};

const wxworkPermissionAlertDialogRef = ref<InstanceType<typeof WxworkPermissionAlertDialog>>();
const menuClick = (menuModel: IMenuModel) => {
    if (!hasAccountSet.value) return;
    if (isLock.value) {
        EnterLockedAccountSetDialog(asInfo.value!.asid, asInfo.value!.asname);
        return;
    }
    if (routerArray.value.length === 0) return;
    if (isInWxWork() && menuModel.functionName == "权限设置") {
        wxworkPermissionAlertDialogRef.value?.showPcWxworkPermissionAlertDialog();
        return false;
    }
    if (menuModel.url) {
        if (menuModel.functionId === ********) {
            globalWindowOpen(menuModel.url);
        } else {
            const href =
                menuModel.url.replace(/^https?:\/\/.+?(?=\/)/, "") +
                (menuModel.functionId === ********
                    ? "?hash=/upgrade/acc?remainingDays=" +
                      (remainingDays.value && isTrial.value && !isExpired.value ? remainingDays.value : -1)
                    : "");
            globalWindowOpenPage(href, menuModel.functionName);

            handleShowTabsTip();
        }
    }
};

function handleShowTabsTip() {
    setTimeout(
        () => {
            const refreshBtn = tabScrollbarInnerRef.value.querySelector(".tab-content.alive.can-refresh .refresh-btn");
            if (refreshBtn) {
                const showRerefshBtnTipKey = useAccountSetStoreHook().userInfo?.userSn + "-" + currentAsId.value + "-showRerefshBtnTip";
                if (!localStorage.getItem(showRerefshBtnTipKey)) {
                    const offset = refreshBtn.getBoundingClientRect();
                    tabRefreshBtnTipRef.value.style.top = offset.top + 6 + 20 + "px";
                    tabRefreshBtnTipRef.value.style.left = offset.left - 7 + "px";
                    tabRefreshBtnTipDisplay.value = true;
                    const removeTip = () => {
                        tabRefreshBtnTipDisplay.value = false;
                        document.removeEventListener("click", removeTip);
                        tabScrollbarInnerRef.value
                            .querySelector(".tab-content.alive.can-refresh .close-btn")
                            .removeEventListener("click", removeTip);
                    };
                    setTimeout(() => {
                        document.addEventListener("click", removeTip);
                        tabScrollbarInnerRef.value
                            .querySelector(".tab-content.alive.can-refresh .close-btn")
                            .addEventListener("click", removeTip);
                    }, 10);
                    localStorage.setItem(showRerefshBtnTipKey, "1");
                    setTimeout(() => {
                        removeTip();
                    }, 5000);
                }
            }
        },
        isThirdPart.value ? 1010 : 0
    );
}

provide(menuClickKey, handleShowTabsTip);

const menuFocus = (event: any) => {
    const childrenMenuNodeContent = event.currentTarget.querySelector(".children-menu-node-content");
    const offset = childrenMenuNodeContent.getBoundingClientRect();
    const height = childrenMenuNodeContent.offsetHeight;
    if (offset.top + height > window.innerHeight) {
        childrenMenuNodeContent.style.top = window.innerHeight - offset.top - height - 10 + "px";
    }
};
const menuBlur = (event: any) => {
    const childrenMenuNodeContent = event.currentTarget.querySelector(".children-menu-node-content");
    childrenMenuNodeContent && (childrenMenuNodeContent.style.top = 0);
};

const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const routerNameArray = computed(() => routerArray.value.filter((item) => item.cache).map((item) => item.name));
const routerArrayLength = computed(() => routerArray.value.length);
const iframeRouterArray = computed(() => routerArray.value.filter((item) => item.iframeComponet));
const currentRightClickTab = ref<IRouterModel>();
const tabRightMenuRef = ref();
const clickTabs = (item: IRouterModel) => {
    if (!hasAccountSet.value) return;
    if (isLock.value && item.title === "首页") {
        EnterLockedAccountSetDialog(asInfo.value!.asid, asInfo.value!.asname);
        return;
    } else {
        globalWindowOpenPage(item.fullPath, item.title);
    }
};
const tabRightClick = (event: any, routerModel: IRouterModel) => {
    event.stopPropagation();
    event.preventDefault();
    if (isLock.value) return;
    currentRightClickTab.value = routerModel;
    // routerModel.title !== "首页" && (currentRightClickTab.value = routerModel);
    const windowWidth = window.innerWidth;
    nextTick().then(() => {
        const offset = tabRightMenuRef.value?.getBoundingClientRect();
        if (offset!.width + event.clientX > windowWidth) {
            tabRightMenuRef.value.style.left = event.clientX - offset!.width + "px";
        } else {
            tabRightMenuRef.value.style.left = event.clientX + "px";
        }
        tabRightMenuRef.value.style.top = event.clientY + "px";
    });
};

const tabRefreshBtnTipRef = ref();
const tabRefreshBtnTipDisplay = ref(false);
const tabScrollbarRef = ref<InstanceType<typeof ElScrollbar>>();
const tabScrollbarInnerRef = ref();
const tabScrollLeft = ref(0);
const tabOnScroll = (params: { scrollLeft: number; scrollTop: number }) => {
    tabScrollLeft.value = params.scrollLeft;
};
const tabToLeft = () => {
    tabScrollbarRef.value?.setScrollLeft(tabScrollLeft.value - 100);
};
const tabToRight = () => {
    tabScrollbarRef.value?.setScrollLeft(tabScrollLeft.value + 100);
};
const closeBtnClick = (path: string) => {
    if (isLock.value && path === "/Settings/AccountSets") {
        EnterLockedAccountSetDialog(asInfo.value!.asid, asInfo.value!.asname);
        return;
    }
    routerArrayStore.removeRouter(path);
    currentRightClickTab.value = undefined;
};
const moreBtnClick = (event: any) => {
    event.stopPropagation();
    currentRightClickTab.value = routerArray.value.find((item) => item.alive);
    const windowWidth = window.innerWidth;
    nextTick().then(() => {
        const offset = tabRightMenuRef.value?.getBoundingClientRect();
        const moreOffset = event.currentTarget.getBoundingClientRect();
        if (offset!.width + (moreOffset.left - 6) > windowWidth) {
            tabRightMenuRef.value.style.left = moreOffset.left + event.currentTarget.offsetHeight - offset!.width + "px";
        } else {
            tabRightMenuRef.value.style.left = moreOffset.left - 6 + "px";
        }
        tabRightMenuRef.value.style.top = moreOffset.top + event.currentTarget.offsetHeight + 11 + "px";
    });
};
watch(
    () => routerArrayLength.value,
    (newValue, oldValue) => {
        if (newValue > oldValue) {
            nextTick(() => {
                tabScrollbarRef.value?.setScrollLeft(tabScrollbarInnerRef.value.offsetWidth);
            });
        }
    }
);
document.body.addEventListener("click", () => {
    currentRightClickTab.value = undefined;
});
const rightMenuCloseAll = () => {
    if (isLock.value) {
        if (routerArray.value.length > 1) {
            routerArray.value.forEach((item: any) => {
                if (!["Default", "AccountSets"].includes(item.name)) {
                    routerArrayStore.removeRouter(item.path);
                }
            });
            currentRightClickTab.value = routerArray.value.find((item: any) => item.name === "AccountSets");
            return;
        } else {
            EnterLockedAccountSetDialog(asInfo.value!.asid, asInfo.value!.asname);
        }
        return;
    }
    routerArrayStore.removeAllRouter();
    currentRightClickTab.value = undefined;
};
const rightMenuCloseOthers = (path: string) => {
    if (isLock.value) {
        if (routerArray.value.length > 1) {
            routerArray.value.forEach((item: any) => {
                if (!["Default", "AccountSets", currentRightClickTab.value!.name].includes(item.name)) {
                    routerArrayStore.removeRouter(item.path);
                }
            });
            return;
        } else {
            EnterLockedAccountSetDialog(asInfo.value!.asid, asInfo.value!.asname);
        }
        return;
    }
    routerArrayStore.removeOthersRouter(isErp.value && path ? path : currentRightClickTab.value!.path);
    currentRightClickTab.value = undefined;
};
const rightMenuCloseCurrent = (erppath?: string) => {
    if (!isErp.value && currentRightClickTab.value!.title === "首页") {
        currentRightClickTab.value = undefined;
        return;
    }
    if (isLock.value) {
        if (currentRightClickTab.value!.name !== "AccountSets") {
            routerArrayStore.removeRouter(currentRightClickTab.value!.path);
            currentRightClickTab.value = undefined;
        } else {
            EnterLockedAccountSetDialog(asInfo.value!.asid, asInfo.value!.asname);
        }
        return;
    }
    const path = isErp.value && erppath ? erppath : currentRightClickTab.value!.path;
    routerArrayStore.removeRouter(path);
    currentRightClickTab.value = undefined;
};
const rightMenuRefreshCurrent = (path?: string) => {
    routerArrayStore.refreshRouter(isErp.value && path ? path : currentRightClickTab.value!.path);
    currentRightClickTab.value = undefined;
};

const currentAsId = ref(0);
const asList = ref<Array<IAccountList>>([]);
const asListOptions = computed(() => {
    return asList.value.map((item) => {
        return {
            ...item,
            value: item.asid,
            label: item.asname,
        };
    });
});

function isQueryParamPresent(paramName: string): boolean {
    const searchParams = new URLSearchParams(window.location.search);
    return searchParams.has(paramName) && !!searchParams.get(paramName);
}
function routerMap(url: string) {
    if (isLock.value) {
        return appendAACompanyId(appendAppasid(window.location.origin + "/Settings/AccountSets")) + "&stay=true";
    } else {
        return appendAACompanyId(appendAppasid(url)) + "&stay=true";
    }
}
const isLock = ref(false);
const asInfo = ref<IAccountList>();
const asListLoading = ref(false);
const loadAccountSetList = () => {
    if (hideMenu.value) return;
    if (allowNoAppasid.value) return;
    asListLoading.value = true;
    getAccountList().then((res: IResponseModel<ITopInfo>) => {
        localStorage.removeItem("hasAccountSet");
        if (res.state === 1000) {
            asList.value = res.data.accountList;
        }
        const searchInfo = asList.value.find((item) => item.appAsId === getGlobalToken());
        if (!searchInfo) {
            if (!asList.value.length && window.location.pathname !== "/Settings/AccountSets1") {
                setTopLocationhref("/Settings/AccountSets1");
            } else {
                setTopLocationhref("/Default/Default?appasid=" + asList.value[0].appAsId);
            }
            return;
        }
        if (!allowNoAppasid.value) loadMenu();
        if (window.isProSystem || window.isErp) {
            loadAccountSetInner();
            return;
        }
        !window.isProSystem && asList.value.length && getNewbieGuidePace();
        // if(isAccountSet1.value) return;
        // 判断是否有可用账套
        request({
            url: "/api/AccountSetOnlyAuth/GetFirstValidAccountSet",
            method: "post",
        })
            .then((res: IResponseModel<any>) => {
                if (res.state === 1000) {
                    hasAccountSet.value = true;
                    firstValidAccountSet.value = res.data;
                } else {
                    hasAccountSet.value = false;
                }
            })
            .finally(() => {
                localStorage.setItem("hasAccountSet", `${hasAccountSet.value}`);
                loadAccountSetInner();
            });
    });
};
function handleReloadAccountSetList() {
    if (currentAsId.value !== 0) return;
    getAccountList().then((res: IResponseModel<ITopInfo>) => {
        if (res.state === 1000) {
            asList.value = res.data.accountList;
            if (asList.value.length > 0 && !asList.value.find((item) => item.asid === 0)) {
                nextTick().then(() => {
                    const placeholderDom = selectRef.value?.selectRef?.querySelector(".el-select-v2__placeholder");
                    placeholderDom && (placeholderDom.innerText = "请选择账套");
                });
            }
        }
    });
}
provide("reloadTopAccountSetList", handleReloadAccountSetList);

function loadAccountSetInner() {
    const accountSetStore = useAccountSetStore();
    accountSetStore
        .getAccountSet()
        .then(() => {
            if (isQueryParamPresent("virtualAsId")) {
                currentAsId.value = Number(new URLSearchParams(window.location.search).get("virtualAsId"));
                accountSetExpireRef.value?.showDialog(currentAsId.value);
            } else {
                currentAsId.value = accountSetStore.accountSet?.asId || 0;
                asInfo.value = asList.value.find((item) => item.asid === currentAsId.value);
                if (asInfo.value?.needLockPassword) {
                    isLock.value = true;
                    if (window.location.pathname !== "/Settings/AccountSets") {
                        setTimeout(() => {
                            globalWindowOpenPage("/Settings/AccountSets?stay=true", "账套");
                        }, 100);
                    }
                    EnterLockedAccountSetDialog(asInfo.value.asid, asInfo.value.asname);
                }
            }
        })
        .catch(() => {
            newAsid.value = asList.value[0].appAsId;
            if (!hasAccountSet.value && !isAccountSet1.value) {
                // asList.value 默认有一个不可用账套
                if (asList.value.length > 1) {
                    currentAsId.value = Number(new URLSearchParams(window.location.search).get("virtualAsId")) || asList.value[0].asid;
                    accountSetExpireRef.value?.showDialog(currentAsId.value);
                } else {
                    // change2newasid(asList.value[0].asid);
                    setTopLocationhref("/Settings/AccountSets1");
                }
            }
        })
        .finally(() => {
            asListLoading.value = false;
        });
}
loadAccountSetList();

const permission = ref("");
const accountSet = toRef(useAccountSetStoreHook(), "accountSet");
watchEffect(() => {
    permission.value = accountSet.value?.permission || "";
});

const newAccountSetHtml = `<li class="new-account-set">
        <a class="link">
            +点击新增账套
        </a>
    </li>`;

const computedNewAccountSetHtml = computed(() => {
    if (window.isAccountingAgent) return "";
    const hasPermission = permission.value.split(",").indexOf("10001") >= 0;
    if (window.isProSystem && !hasPermission) return "";
    return newAccountSetHtml;
});

function newAccountSet() {
    globalWindowOpenPage("/Settings/AccountSets", "帐套");
}

const accountSetExpireRef = ref<InstanceType<typeof AccountSetExpire>>();

// 是否有可用账套
const hasAccountSet = ref(true);
// 可用账套
const firstValidAccountSet = ref();
const accountSetExpireOnClose = () => {
    localStorage.removeItem("hasAccountSet");
    if (isLemonClient()) {
        getLemonClient().setSelectedAccount(Number(firstValidAccountSet.value?.asId));
    } else {
        setTopLocationhref("/Default/Default?appasid=" + firstValidAccountSet.value?.appAsId);
    }
};
const newAsid = ref("");
const accountSetExpireShowBuyDialog = () => {
    tryShowPayDialog(1, "", "开通专业版，立即解锁多用户协同。更多专业版功能如下：", "用户限制", function () {
        localStorage.removeItem("hasAccountSet");
        if (isLemonClient()) {
            getLemonClient().setSelectedAccount(Number(accountSet.value?.asId));
        } else {
            setTopLocationhref(`/Default/Default?appasid=${getGlobalToken() || newAsid.value}`);
        }
    });
};

const accountPeriodStore = useAccountPeriodStore();
const currentPeriodTxt = computed(() => {
    const currentPeriodStartDate = accountPeriodStore.periodList
        .filter((p) => p.status === PeriodStatus.HasVoucher || p.status === PeriodStatus.ChangeOut)
        .sort((p1, p2) => p2.pid - p1.pid)[0]?.startDate;
    if (currentPeriodStartDate) {
        const date = new Date(currentPeriodStartDate);
        return `${date.getFullYear()}年${date.getMonth() + 1}月`;
    } else {
        return "";
    }
});

let watchLock = false;
const setSelectedAccountWithoutChange = function (asid: number) {
    watchLock = true;
    nextTick(() => {
        currentAsId.value = asid;
        nextTick(() => {
            watchLock = false;
        });
    });
};

watch(
    () => currentAsId.value,
    (value, oldValue) => {
        // 当前无可使用账套，并且在创建账套页
        if (!hasAccountSet.value && isAccountSet1.value) {
            if (value !== 0) {
                const newAppasid = asListOptions.value.find((item) => item.asid === value)?.appAsId;
                if (newAppasid) {
                    setTopLocationhref("/Default/Default?appasid=" + newAppasid);
                }
            }
            return;
        }
        if (oldValue !== 0 && !watchLock) {
            const asInfo = asList.value.find((item) => item.asid === value);
            if (asInfo?.needLockPassword) {
                EnterLockedAccountSetDialog(asInfo.asid, asInfo.asname).then((r: boolean) => {
                    if (!r) {
                        setSelectedAccountWithoutChange(oldValue);
                    }
                });
            } else {
                change2newasid(value);
            }
        }
    }
);

const hasProAccountSet = ref(false);
getHasProAccountSet().then((r) => {
    hasProAccountSet.value = r;
});

const gotoWWW = () => {
    globalWindowOpen(window.wwwHost);
};

const gotoGuide = () => {
    globalWindowOpenPage("/BeginnersGuide", "新手指引");
};

const gotoHelpCenter = () => {
    globalWindowOpen("https://help.ningmengyun.com");
};

const gotoStudyCenter = () => {
    globalWindowOpen("https://www.nmkjxy.com/");
};
const isShowProWorkBench = ref(false);

const initProWorkBenchShow = () => {
    if (window.isAccountingAgent) {
        return false;
    }
    if (window.isErp) {
        return false;
    }
    if (isWxwork.value) {
        return false;
    }
    if (isProSystem.value) {
        return true;
    }

    return false;
};

const proWorkBenchCanShow = () => {
    isShowProWorkBench.value = initProWorkBenchShow();
    if (isWxwork.value) {
        initWxworkConfig(function () {
            WWOpenData.bindAll(document.querySelectorAll("ww-open-data"));
        });
    }
    if (isProSystem.value && isWxwork.value) {
        isShowProWorkBench.value = false;
        request({ url: window.eHost + "/wb/" + (isWxwork.value ? "wecom/" : "") + "plan/enterprise?serviceId=" + getServiceId() })
            .then((r: any) => {
                if (r.statusCode === true) {
                    window.isCurrentWxworkService = true;
                    isShowProWorkBench.value = true;
                    request({ url: window.eHost + "/wb/" + (isWxwork.value ? "wecom/" : "") + "plan/administrator" }).then((r: any) => {
                        if (r.statusCode === true) {
                            window.wxworkSuperAdminUserSn = r.data.administrator;
                            request({ url: "/api/WxWorkOnlyAuth/IsManager", method: "post" }).then((r: IResponseModel<boolean>) => {
                                let isManager = false;
                                if (
                                    (r.state === 1000 && r.data === true) ||
                                    window.wxworkSuperAdminUserSn == useAccountSetStoreHook().userInfo?.userSn
                                ) {
                                    isManager = true;
                                    proWorkBenchDialogRef.value?.showWxworkSelectProSurperManager();
                                    if (window.wxworkSuperAdminUserSn !== 0) {
                                        request({
                                            url: "/api/WxWorkOnlyAuth/GetUserId?userSn=" + window.wxworkSuperAdminUserSn,
                                            method: "post",
                                        }).then((r: IResponseModel<string>) => {
                                            if (r.state === 1000) {
                                                window.wxworkSuperAdminOpenId = r.data;
                                                proWorkBenchDialogRef.value?.setOpenid(r.data);
                                                proWorkBenchDialogRef.value?.hidePlaceholderShow();
                                                proWorkBenchDialogRef.value?.setSuperManagerContainer();
                                            }
                                        });
                                    }
                                }
                                if (
                                    window.location.href.indexOf("AccountSets1") !== -1 &&
                                    window.wxworkSuperAdminUserSn == 0 &&
                                    isManager &&
                                    window.isCurrentWxworkService
                                ) {
                                    proWorkBenchDialogRef.value?.showSelectProSurperManagerDialog(false);
                                }
                            });
                        }
                    });
                }
            })
            .catch(() => {
                isShowProWorkBench.value = false;
            });
    }
};

proWorkBenchCanShow();

const proWorkBenchDialogRef = ref<InstanceType<typeof ProWorkBenchDialog>>();

const gotoWorkBench = () => {
    if (isWxwork.value) {
        proWorkBenchDialogRef.value?.showWorkBench();
        return;
    }
    globalWindowOpen(window.wwwHost + "/Partial.aspx?p=w#/workbench");
};

// 分录大全
const gotoAccountingEntry = () => {
    globalWindowOpenPage("/MasterPages/AccountingEntry", "分录大全");
};
const gotoMessageSearch = () => {
    globalWindowOpenPage("/MasterPages/MessageSearch", "查询导航");
};

interface ICenterMessageModel {
    //状态
    succeed: boolean;
    //信息
    msg: string;
    //需要弹出的所有消息的数量 新增
    dispUnreadTotalCount: number;
    //所有未读消息的数量 不一定要弹出
    unreadTotalCount: number;
    //弹出的未读消息的信息 新增
    dispunreadList: Array<IAnnoucementModel>;
    //右上角未读消息的信息
    items: Array<IAnnoucementModel>;
    popup: IAnnoucementModel;
    dayMsg: number;
}

interface IAnnoucementModel {
    ancm_sn: string;
    flag: string;
    title: string;
    body: string;
    created_date: string;
    picture: string;
    link: string;
    modifytime: string;
    desc1: string;
    task_id: string;
    hasPICTURE: boolean;
}
const headImgUrl = ref(window.jApiHost + "/api/HeadImg");
const userStore = useUserStore();
const userName = computed(() => {
    return userStore.userName;
});
const gotoOrderList = (isCancel? : boolean) => {
    globalWindowOpen(window.wwwHost + "/OrderList?p=w#/workbench/orderTable"+(isCancel ? "?MessageError=1" : ""));
};
const gotoPersonalInfo = () => {
    globalWindowOpenPage("/Default/PersonalInfo", "个人设置");
};
const changeAACompanyDialogRef = ref<InstanceType<typeof ChangeAACompanyDialog>>();
const changeAACompany = () => {
    changeAACompanyDialogRef.value?.show();
};
const isWxworkFollowUser = ref(false);

const buyMore = () => {
    if (isAccountingAgent.value) {
        buyPro();
    } else {
        gotoBuy();
    }
};
const asideDisplay = ref(true);
const gotoTopBtnDisplay = ref(false);
const mainContainerRef = ref();
const mainContainerScroll = (e: any) => {
    gotoTopBtnDisplay.value = e.currentTarget.scrollTop > 0;
};
let setTableScrollToTop = () => {};
const setGotoTopBtnDisplay = (display: boolean, fn: () => void) => {
    // 避免滚动的时候重复赋值
    if (display === gotoTopBtnDisplay.value) return;
    gotoTopBtnDisplay.value = display;
    setTableScrollToTop = fn;
};
const handleScrollToTop = () => {
    mainContainerRef.value?.scrollTop && (mainContainerRef.value.scrollTop = 0);
    setTableScrollToTop();
};
provide(pageScrollKey, setGotoTopBtnDisplay);

const hotFunctionCodeList = ["bankandcompany", "bosspermissions", "invoice-fetch", "invoice-analysis"];

const showTaxCalculator = ref<boolean>(false);
const LMCalculatorRef = ref<InstanceType<typeof LMCalculator>>();
const gotoLMCalculator = () => {
    LMCalculatorRef.value?.show();
};

// 计算专业版打开次数
const openNumber = ref<number>(0);
const countOpenTime = () => {
    if (!isProSystem.value || allowNoAppasid.value || isWxworkFollowUser.value || isThirdPart.value) return;
    // 打开次数
    openNumber.value = JSON.parse(localStorage.getItem("prosystem-open-number") || "0");
    // 超过3次不弹
    if (openNumber.value >= 3) return;
    // 已存储日期
    const storageDay = localStorage.getItem("prosystem-open-date") || "";
    // 当前日期
    const currentDate = dayjs();
    // 第二天
    const nextDay = currentDate.add(1, "day").startOf("date");

    if (storageDay && !currentDate.isAfter(storageDay)) return;
    showAddServiceDialog();
    openNumber.value++;
    localStorage.setItem("prosystem-open-number", JSON.stringify(openNumber.value));
    localStorage.setItem("prosystem-open-date", nextDay.format("YYYY-MM-DD"));
};

const openid = ref("");
//编辑状态时 浏览器刷新弹窗
const handleEditRefresh = (e: any) => {
    if (isErp.value) return;
    const isEdit = routerArray.value.some((item: IRouterModel) => item.isEditting === true);
    if (isEdit) {
        e.preventDefault();
        e.returnValue = "当前网站有未保存的数据，确定要离开吗？";
    }
};
//多页签拖动
let currentDragTab: any = null;
const handleDragStart = (event: any) => {
    // event.preventDefault();  阻止默认行为 会导致不会触发 dragEnd
    if (event.target.classList && event.target.classList.contains("tab-content") && event.target.innerText !== "首页") {
        const clickedElement = event.target;
        currentDragTab = clickedElement;
        setTimeout(() => {
            clickedElement.classList.add("moving");
        }, 0);
    }
};
let enterDragTab: any = null;
let dragedIndex: any = null;
const handleDragEnter = (event: any) => {
    event.preventDefault();
    if (!currentDragTab) return;
    if (event.target === tabScrollbarInnerRef.value) {
        enterDragTab = null; //用于互换之后再进行互换回来
        return;
    }
    if (!event.target.classList.contains("tab-content")) return; //过滤不是标签页的元素
    if (enterDragTab === event.target || enterDragTab === event.target.parentNode) return;
    if (event.target === currentDragTab) return;
    enterDragTab = event.target;
    const children = [...tabScrollbarInnerRef.value.children];
    dragedIndex = children.indexOf(currentDragTab);
    const targetIndex = children.indexOf(event.target);
    //第二个标签页插入首页不做处理
    if (dragedIndex === 1 && targetIndex === 0) {
        return;
    }
    //第三个及以上标签页插入首页直接插入第二个标签页前面
    if ((targetIndex === 0 && dragedIndex !== 1) || dragedIndex < targetIndex) {
        // 向右插入
        tabScrollbarInnerRef.value.insertBefore(currentDragTab, event.target.nextElementSibling);
    } else {
        // 向左插入
        tabScrollbarInnerRef.value.insertBefore(currentDragTab, event.target);
    }
};
const handleDragEnd = () => {
    if (!currentDragTab) return;
    currentDragTab.classList.remove("moving");
    currentDragTab = null;
    enterDragTab = null;
    nextTick(() => {
        const innerTextList = [...tabScrollbarInnerRef.value.children].map((item: any) => item.innerText);
        const indexMap = new Map(innerTextList.map((title, index) => [title, index]));
        routerArray.value.sort((a, b) => {
            const indexA = indexMap.get(a.title) ?? 0;
            const indexB = indexMap.get(b.title) ?? 0;
            if (indexA < indexB) {
                return -1;
            } else if (indexA > indexB) {
                return 1;
            } else {
                return 0;
            }
        });
    });
};
//拖动侧边栏
const isDragging = ref(false);
let asideIsShowMaxTop = 0;
const styleObject = ref({
    top: "50%",
    transform: "translate(0, -50%)",
    WebkitTransform: "translate(0, -50%)",
    MozTransform: "translate(0, -50%)",
    OTransform: "translate(0, -50%)",
});
//拖动的时候防止触发子元素点击事件
const handleAsideClick = (e: any) => {
    if (isDragging.value) {
        e.stopPropagation();
    }
};
const handleAsideMouseDown = (e: any) => {
    if (asideDisplay.value) {
        asideIsShowMaxTop = window.innerHeight - e.currentTarget.offsetHeight;
    }
    const currentY = e.clientY;
    isDragging.value = false;
    const rect = e.currentTarget.getBoundingClientRect();
    const currentTargetTop = rect.top;
    const maxHeight = window.innerHeight - e.currentTarget.offsetHeight;
    const handleMouseMove = (e: any) => {
        const moveDistanceY = e.clientY - currentY;
        isDragging.value = Math.abs(moveDistanceY) <= 5 ? false : true;
        let moveY = currentTargetTop + moveDistanceY;
        if (moveY < 0) {
            moveY = 0;
        } else if (moveY > maxHeight) {
            moveY = maxHeight;
        }
        if (isDragging.value) {
            styleObject.value = {
                top: `${moveY}px`,
                transform: "none",
                WebkitTransform: "none",
                MozTransform: "none",
                OTransform: "none",
            };
        }
    };
    const handleMouseUp = () => {
        window.removeEventListener("mousemove", handleMouseMove);
        window.removeEventListener("mouseup", handleMouseUp);
    };
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);
};
const handleAsideHideOrShow = (flag: boolean) => {
    asideDisplay.value = flag;
    if (flag) {
        styleObject.value.top = parseFloat(styleObject.value.top) > asideIsShowMaxTop ? asideIsShowMaxTop + "px" : styleObject.value.top;
    }
};
//展开在底部，返回顶部出现的时候防止被覆盖
const backToTopIsShow = computed(() => {
    return gotoTopBtnDisplay.value && asideDisplay.value;
});
watch(backToTopIsShow, (newVal) => {
    if (!newVal) return;
    const asidEl = document.querySelector(".aside") as HTMLElement;
    nextTick(() => {
        const showAllmaxTop = window.innerHeight - asidEl.offsetHeight;
        styleObject.value.top = parseFloat(styleObject.value.top) > showAllmaxTop ? showAllmaxTop + "px" : styleObject.value.top;
    });
});
const reloadOrderNews = () => {
    getNewsList(1);
}
const checkAndSetGray = () => {
    if (window.isErp || isThirdPart.value || window.isAccountingAgent || isLemonClient()) {
        return;
    }
    if(!getGlobalToken()) {
        return;
    }
    if (getCookie('grayrelease')) {
        return;
    }

    request({
        url: "/api/AccountSet/IsGray",
        method: "post",
    }).then((res: IResponseModel<{ isGray: boolean, fullDeploymentTime: string }>) => {
        if (res.state === 1000 && res.data.isGray) {
            const data = res.data;
            const cookieName = 'grayrelease';
            const cookieValue = 'true';
            const domain = document.location.hostname.includes('.ningmengyun.com') ? '.ningmengyun.com' : document.location.hostname;
            const expirationDate = new Date(data.fullDeploymentTime).toUTCString();
            document.cookie = `${cookieName}=${cookieValue}; domain=${domain}; expires=${expirationDate}; path=/`;
            window.location.reload();
        }
    });
};



onMounted(async () => {
    //添加事件监听器
    // fullscreenchange事件来实现监听是否是全屏状态,会在全屏状态发生改变时触发,余下三个是为了兼容不同浏览器
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);
    window.addEventListener("refreshGuidePace", getNewbieGuidePace);
    window.addEventListener("reloadOrderNews",  reloadOrderNews);
    // 添加F11键按下事件监听器
    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("beforeunload", handleEditRefresh);
    isWxwork.value && (openid.value = getCookie("openUserId") || "");

    if(!isInLemonClient.value && !window.isErp){
        if(!isThirdPart.value){
            getNewsList(1);
            getNewsList(2);
            getAdNotice();
        }
    }

});
onUnmounted(() => {
    //移除事件监听器
    document.removeEventListener("fullscreenchange", handleFullscreenChange);
    document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
    document.removeEventListener("MSFullscreenChange", handleFullscreenChange);
    window.removeEventListener("refreshGuidePace", getNewbieGuidePace);
    window.removeEventListener("reloadOrderNews", reloadOrderNews)
    // 移除F11键按下事件监听器
    window.removeEventListener("keydown", handleKeyDown);
    window.removeEventListener("beforeunload", handleEditRefresh);

    localStorage.removeItem("hasAccountSet");
});
window.onload = function () {
    erpNotify(erpNotifyTypeEnum.erpLoading, { load: true });
};
const selectRef = ref();

const visibleSelect = ref(false);
const handleVisibleChange = (visible: boolean) => {
    visibleSelect.value = visible;
    currentRightClickTab.value = undefined;
    if (visible && computedNewAccountSetHtml.value !== "") {
        let popper = selectRef.value?.popperRef;
        if (!popper) return;
        if (popper.$el) popper = popper.$el;
        //避免重复添加
        if (!Array.from(popper.children).some((v: any) => v.className === "el-select-menu__list")) {
            const el = document.createElement("ul");
            el.className = "el-select-menu__list";
            el.innerHTML = computedNewAccountSetHtml.value;
            popper.appendChild(el);
            el.onclick = (event: Event) => {
                event.preventDefault();
                event.stopPropagation();
                newAccountSet();
                selectRef.value?.toggleMenu();
            };
            popper.querySelector(".el-virtual-scrollbar").addEventListener("click", (e: any) => {
                e.stopPropagation();
            });
        }
    }
};
// 消息中心
// 系统消息2，订单消息1
const currentNews = ref(Number(localStorage.getItem("AppViewNewsType")) || 2);
watch(currentNews, (newVal) => {
    localStorage.setItem("AppViewNewsType", newVal.toString());
});
const moreNewsListDialogVisible = ref(false);
const newsDetailtDialog = ref({
    visible: false,
    title: "",
    content: "",
    date: "",
});
interface INewsItem {
    ancmSn: number;
    body: string;
    createdDate: string;
    createdDateTimestamp: number;
    isUnread: boolean;
    link: string;
    linkType: number;
    msgType: number;
    pageCount: number;
    pageIndex: number;
    title: string;
}
const newsList = ref<Array<INewsItem | any>>([]);
// 未读是1，全部是2
const newsRange = ref(Number(localStorage.getItem("AppViewNewsRange")) || 2);
const switchNewsRangeListShow = ref(false);
const unreadNewsCount = ref(0);
const unreadNewsCount1 = ref(0);
const unreadNewsCount2 = ref(0);
// 待支付订单
const pendingPaymentOrderVisible = ref({ show: false, orderSn: 0, link: "" });
const allNewsList = ref<Array<INewsItem>>([]);
// 订单是1，系统是2
const allNewsList1 = ref<Array<INewsItem>>([]);
const allNewsList2 = ref<Array<INewsItem>>([]);
// apim的接口都用bearer token 获取用户登录信息
const headers = {
    Authorization: `Bearer ${getCookie("ningmengcookie")}`,
};
const getNewsList = (msgType?: number) => {
    if (!isAlipayImportJournalRedirectRoute) {
        if (window.isAccountingAgent) {
            request({
                url: "/api/Message/GetCenter",
                method: "post",
            }).then((res: IResponseModel<ICenterMessageModel>) => {
                if (res.state === 1000 && res.data.succeed) {
                    newsList.value = res.data.items;
                    unreadNewsCount.value = res.data.unreadTotalCount;
                }
            });
        } else {
            request({
                url: window.apimHost + "/api/Message/JzPcMsg",
                method: "post",
                headers,
                data: {
                    msgType: msgType || currentNews.value,
                    version: window.isProSystem ? "PRO" : "FREE",
                    isUnread: false,
                    pageIndex: 1,
                    pageCount: 1000,
                },
            }).then((res: any) => {
                if (res.code === 1000) {
                    allNewsList.value = res.data.data;
                    if((msgType || currentNews.value) === 1){
                        allNewsList1.value = res.data.data;
                        let orderSns = res.data.data
                            .filter((message: INewsItem) => message.title.includes("待支付订单") && isWithin24Hours(message.createdDateTimestamp))
                            .map((message: INewsItem) => {
                                let link = message.link;
                                let matchSn = link.match(/(\d+)$/);
                                return matchSn ? matchSn[1] : null;
                            });
                            unreadNewsCount1.value = res.data.data.filter((v:INewsItem)=>v.isUnread).length;
                        if (orderSns.length) {
                            request({
                                url: window.eHost + "/wb/order/pendingPaymentOrder",
                                method: "post",
                                data: {
                                    orderSns: [...new Set(orderSns)],
                                },
                            }).then((res2: IResponseModel<any>) => {
                                const orderSn = res2.data.orderSn;
                                if (orderSn === 0) {
                                    pendingPaymentOrderVisible.value.show = false;
                                } else {
                                    pendingPaymentOrderVisible.value.show = true;
                                    pendingPaymentOrderVisible.value.orderSn = orderSn;
                                    pendingPaymentOrderVisible.value.link = res.data.data?.find((message: any) =>
                                        new RegExp(orderSn).test(message.link)
                                    ).link;
                                }
                            });
                        }else {
                            pendingPaymentOrderVisible.value.show = false;
                        }
                    }else {
                        allNewsList2.value = res.data.data;
                        unreadNewsCount2.value = res.data.data.filter((v:INewsItem)=>v.isUnread).length;
                    }
                    newsList.value = newsRange.value === 1 ? res.data.data.filter((v:INewsItem)=>v.isUnread) : res.data.data;
                    unreadNewsCount.value = unreadNewsCount2.value + unreadNewsCount1.value;
                }else {
                    allNewsList.value = [];
                    newsList.value = [];
                    (msgType || currentNews.value) === 1 ? unreadNewsCount1.value = 0 : unreadNewsCount2.value = 0;
                    unreadNewsCount.value = unreadNewsCount2.value + unreadNewsCount1.value;
                }
            }).catch(() => {
                allNewsList.value = [];
                newsList.value = [];
                (msgType || currentNews.value) === 1 ? unreadNewsCount1.value = 0 : unreadNewsCount2.value = 0;
                unreadNewsCount.value = unreadNewsCount2.value + unreadNewsCount1.value;
            });
        }
    }
};

const gotoOrderListCheckup = (item: any) => {
    request({
        url: window.eHost + "/wb/order/pendingPaymentOrder",
        method: "get",
        params: {
            orderSn: item.link.match(/\d+$/)[0],
        },
    }).then((res: any) => {
        if (res.statusCode) {
            if(res.data.isPending && !res.data.isComplete && !res.data.isCancel){
                globalWindowOpen(item.link);
            } else {
                gotoOrderList(res.data.isCancel);
                pendingPaymentOrderVisible.value.show = false;
            }
        }
    }).catch(() => {
        gotoOrderList();
        pendingPaymentOrderVisible.value.show = false;
    });
};
const filterNewsByLastMonth = (news: INewsItem[]) => {
    let currentDate = new Date();
    let oneMonthAgo = new Date();
    oneMonthAgo.setMonth(currentDate.getMonth() - 1);
    let recentMessages = news.filter((message: INewsItem) => {
        let messageDate = new Date(message.createdDate);
        return messageDate >= oneMonthAgo && messageDate <= currentDate;
    });

    return recentMessages;
};


const readNewsChangeStatus = (item: any) => {
    moreNewsListDialogVisible.value = false;
    if (item.msgType === 1 && item.title.includes("待支付订单")) {
        gotoOrderListCheckup(item);
    } else {
        item.link
            ? globalWindowOpen(item.link)
            : (newsDetailtDialog.value = {
                  visible: true,
                  title: item.title,
                  content: item.body,
                  date: item.createdDate,
              });
    }
    request({
        url:
            window.apimHost +
            `/api/Message/JzPcMsg?msgType=${item.msgType}&ancmSn=${item.ancmSn}&createdDateTimeStamp=${item.createdDateTimestamp}`,
        method: "put",
        headers,
    }).finally(() => {
        if (item.isUnread) {
            item.msgType === 1 ? unreadNewsCount1.value-- : unreadNewsCount2.value--;
            unreadNewsCount.value--;
            item.isUnread = false;
            if(newsRange.value === 1){
                newsList.value = newsList.value.filter(v=>v.isUnread);
            }
        }
    });
};
const formatterNewsContent = (body: string) => {
    return body ? body.replace(/<[^>]+>/g, "") : "";
}
const newsListScrollBarRefTop = ref();
const newsListScrollBarRefDialog = ref();
const handleSwitchNewsRange = (value: number) => {
    allNewsList.value = currentNews.value === 1 ? allNewsList1.value : allNewsList2.value;
    newsRange.value = value;
    localStorage.setItem("AppViewNewsRange", String(value));
    switchNewsRangeListShow.value = false;
    newsList.value= value === 1?allNewsList.value.filter(v=>v.isUnread):allNewsList.value;
    moreNewsListDialogVisible.value ? newsListScrollBarRefDialog.value?.scrollTo(0, 0) : newsListScrollBarRefTop.value?.scrollTo(0, 0);
};
const changeNewsType = (msgType: number) => {
    currentNews.value = msgType;
    allNewsList.value = msgType === 1 ? allNewsList1.value : allNewsList2.value;
};
watch(allNewsList, (newVal) => {
    newsList.value = newsRange.value === 1 ? newVal.filter(v=>v.isUnread) : newVal;
},{deep: true});
const newsContainerRef = ref();
const getCurrentNewsList = () => {
    if(window.isAccountingAgent){
       return;
    }
    newsList.value = (currentNews.value === 1 ? allNewsList1.value : allNewsList2.value)
    .filter(v => newsRange.value === 1 ? v.isUnread : true);
};
const changeNewsListStatus = () => {
    request({
        url: window.apimHost + `/api/Message/JzPcMsg?msgType=${currentNews.value}&version=${window.isProSystem ? "PRO" : "FREE"}`,
        method: "put",
        headers,
    }).then((res: any) => {
        if (res.code === 1000 && res.data) {
            if(currentNews.value === 1){
                unreadNewsCount1.value = 0;
                allNewsList1.value = allNewsList1.value.map((v:INewsItem)=>({...v,isUnread:false}));
                allNewsList.value = allNewsList1.value;
            }else {
                unreadNewsCount2.value = 0;
                allNewsList2.value = allNewsList2.value.map((v:INewsItem)=>({...v,isUnread:false}));
                allNewsList.value = allNewsList2.value;
            }
            unreadNewsCount.value = unreadNewsCount1.value + unreadNewsCount2.value;
        }else {
            ElNotify({
                type:'warning',
                message:"出现错误，请刷新页面重试"
            })
        }
    }).catch(()=>{
        ElNotify({
            type:'warning',
            message:"出现错误，请刷新页面重试"
        })
    });
};
const formatUnreadCount = (count: number) => {
    return count > 99 ? "99+" : count;
};
// 广告位
const topAdImg = ref({ src: "", link: "" });
const getAdNotice = () => {
    if(localStorage.getItem("closeAdContainer") === dayjs().format('YYYY-MM-DD')){
        return;
    }
    let systemType: BannerEnum = BannerEnum.FreeTop;
    if (window.isAccountingAgent) {
        if (window.isProSystem) {
            systemType = BannerEnum.ProAgentTop;
        } else {
            systemType = BannerEnum.FreeAgentTop;
        }
    } else {
        if (window.isProSystem) {
            systemType = BannerEnum.ProTop;
        } else {
            systemType = BannerEnum.FreeTop;
        }
    }
    request({ url: window.apimHost + "/api/Banner/JzPc?adsType=2070&dispType=" + systemType }).then((r: any) => {
        if (r.code == 1000 && r.data) {
            showAdContainer.value = true;
            topAdImg.value.src = r.data.picOne;
            topAdImg.value.link = r.data.link;
        }else {
            showAdContainer.value = false;
        }
    }).catch(()=>{
        showAdContainer.value = false;
    });
};
watch(showAdContainer, (newVal) => {
    document.documentElement.style.setProperty('--top-value', newVal ? '137px' : '97px');
},{immediate: true});
const closeAdContainer = () => {
    showAdContainer.value = false;
    localStorage.setItem("closeAdContainer", dayjs().format('YYYY-MM-DD'));
};
function checkShowImportFree() {
    return menuNodeList.value
        .find((menuNode) => menuNode.node.functionId === ********)
        ?.children.find((menuNode) => menuNode.node.functionId === 10600012);
}
function checkShowRelateScm() {
    return menuNodeList.value.find((menuNode) => menuNode.node.functionId === ********);
}
function checkMenuShadowClass() {
    if (checkShowImportFree() && checkShowRelateScm()) {
        return "pro-scm";
    } else if (checkShowImportFree()) {
        return "pro";
    } else if (checkShowRelateScm()) {
        return "scm";
    }
    return "";
}
function checkMenuShadowHeight() {
    if (checkShowRelateScm()) {
        const menuNodeHeight = 48;
        return (menuNodeList.value.length - 1) * (menuNodeHeight + 1) + "px";
    } else if (checkShowImportFree()) {
        return "calc(100% - 48px)";
    }
    return "100%";
}
</script>

<template>
    <div class="main-container">
        <div class="left-container" v-show="!hideMenu && thirdParthasLoading">
            <div class="logo" :class="{ free: !isProSystem, pro: isProSystem }" v-show="!isInLemonClient"></div>
            <div class="menu-container" :class="checkMenuShadowClass()">
                <a
                    class="import-free-accountset"
                    :href="appendAACompanyId(appendAppasid('/Settings/TransferPro')) + '&stay=true'"
                    v-show="checkShowImportFree()"
                    @click.prevent="menuClick(checkShowImportFree()!.node)"
                    >导入免费版账套</a
                >
                <!-- <el-scrollbar> -->
                <template v-for="(menuNode, index) in menuNodeList" :key="index">
                    <a
                        v-if="!!menuNode.node.url"
                        :href="
                            appendAACompanyId(
                                appendAppasid(
                                    menuNode.node.url +
                                        (menuNode.node.functionId === ********
                                            ? '?hash=/upgrade/acc?remainingDays=' +
                                              (remainingDays && isTrial && !isExpired ? remainingDays : -1)
                                            : '')
                                )
                            ) + '&stay=true'
                        "
                        class="menu-node-container"
                        :class="{
                            selected:
                                (menuNode.node.url &&
                                    menuNode.node.url.toLowerCase().replace(/^https?:\/\/.+?(?=\/)/, '') !== '/' &&
                                    $route.fullPath
                                        .toLowerCase()
                                        .indexOf(menuNode.node.url.toLowerCase().replace(/^https?:\/\/.+?(?=\/)/, '')) === 0) ||
                                menuNode.children.some(
                                    (item) =>
                                        item.node.url &&
                                        $route.fullPath
                                            .toLowerCase()
                                            .indexOf(item.node.url.toLowerCase().replace(/^https?:\/\/.+?(?=\/)/, '')) === 0
                                ),
                            vip: menuNode.node.functionId === ********,
                        }"
                        @click.prevent="menuClick(menuNode.node)"
                    >
                        <div class="menu-node-content">
                            <template v-if="menuNode.node.functionId === ********">
                                <img src="./assets/Icons/scmMenu.png" alt="" style="max-width: 100%" />
                            </template>
                            <template v-else>
                                <img class="menu-icon" :src="getIcon(menuNode.node.functionId, false)" />
                                <img
                                    class="menu-icon selected"
                                    v-if="menuNode.node.functionId !== ********"
                                    :src="getIcon(menuNode.node.functionId, true)"
                                />
                                <span>{{ menuNode.node.functionName }}</span>
                                <img
                                    class="vip-icon"
                                    v-show="!isProSystem && !isThirdPart && vipModuleCheck(menuNode.node.functionId)"
                                    src="@/assets/Menu/vip-icon.png"
                                />
                            </template>
                        </div>
                    </a>
                    <div
                        v-else
                        class="menu-node-container"
                        :class="{
                            selected:
                                (menuNode.node.url &&
                                    $route.fullPath
                                        .toLowerCase()
                                        .indexOf(menuNode.node.url.toLowerCase().replace(/^https?:\/\/.+?(?=\/)/, '')) === 0) ||
                                menuNode.children.some(
                                    (item) =>
                                        item.node.url &&
                                        $route.fullPath
                                            .toLowerCase()
                                            .indexOf(item.node.url.toLowerCase().replace(/^https?:\/\/.+?(?=\/)/, '')) === 0
                                ) ||
                                (menuNode.node.functionId === ******** &&
                                    routerArray.find((item) => item.alive)?.path.startsWith('/Cashier')),
                            vip: menuNode.node.functionId === ********,
                        }"
                        @mouseenter="menuFocus($event)"
                        @mouseleave="menuBlur($event)"
                    >
                        <div class="menu-node-content">
                            <img class="menu-icon" :src="getIcon(menuNode.node.functionId, false)" />
                            <img
                                class="menu-icon selected"
                                v-if="menuNode.node.functionId !== ********"
                                :src="getIcon(menuNode.node.functionId, true)"
                            />
                            <span>{{ menuNode.node.functionName }}</span>
                            <img
                                class="vip-icon"
                                v-show="!isProSystem && !isThirdPart && vipModuleCheck(menuNode.node.functionId)"
                                src="@/assets/Menu/vip-icon.png"
                            />
                        </div>
                        <div class="children-more-icon"></div>
                        <div class="children-menu-node-content" v-show="!chiledrenMenuShow">
                            <a
                                v-for="(childNode, index) in menuNode.children"
                                :key="index"
                                :href="routerMap(childNode.node.url)"
                                @click.prevent="menuClick(childNode.node)"
                            >
                                <span>{{ childNode.node.functionName }}</span>
                                <img
                                    class="vip-icon"
                                    v-if="!isProSystem && !isThirdPart && vipModuleCheck(childNode.node.functionId)"
                                    src="@/assets/Menu/vip-icon.png"
                                />
                                <span
                                    class="hot"
                                    v-if="hotFunctionCodeList.findIndex((item) => item === childNode.node.functionCode) !== -1"
                                ></span>
                            </a>
                        </div>
                    </div>
                </template>
                <!-- </el-scrollbar> -->
                <div class="menu-shadow" v-show="allowNoAppasid" :style="{ height: checkMenuShadowHeight() }"></div>
            </div>
        </div>
        <div class="right-container">
            <div class="top-container" v-show="!hideMenu && !isInLemonClient && visibleTopBar && thirdParthasLoading">
                <div class="accountset-info">
                    <template v-if="(currentAsId === 0 && asListOptions.length === 0) || asListLoading">
                        <Select
                            v-model="currentAsId"
                            :teleported="false"
                            :filterable="true"
                            :bottom-html="computedNewAccountSetHtml"
                            @bottom-click="newAccountSet"
                        >
                            <Option :value="0" :label="asListLoading ? '账套加载中...' : '当前没有账套'"></Option>
                        </Select>
                    </template>
                    <template v-else>
                        <el-select-v2
                            ref="selectRef"
                            v-model="currentAsId"
                            :filterable="true"
                            class="select custom-select-v2"
                            :class="visibleSelect ? 'visibleSelect' : ''"
                            popper-class="select-down"
                            :options="asListOptions"
                            placeholder=""
                            :teleported="false"
                            :scrollbar-always-on="true"
                            :item-height="28"
                            @visible-change="handleVisibleChange"
                        >
                            <template #default="{ item }">
                                <ToolTip
                                    :content="item.label"
                                    :line-clamp="1"
                                    :teleported="true"
                                    placement="right-start"
                                    :offset="18"
                                    :maxWidth="245"
                                >
                                    <span class="locked-accountset" v-if="item.needLockPassword">{{ item.label }}</span>
                                    <template v-else>
                                        <span class="unlocked-accountset" v-if="item.locked">{{ item.label }}</span>
                                        <span v-else>{{ item.label }}</span>
                                    </template>
                                </ToolTip>
                            </template>
                        </el-select-v2>
                    </template>
                    <span class="period-info">{{ currentPeriodTxt }}</span>
                    <div class="pro-renew-info-container" v-show="proRemainingDays !== -1 && !isThirdPart">
                        您的专业版服务有效期还剩<span style="color: rgb(228, 74, 94)">{{ proRemainingDays }}天</span>！
                        <div class="renew-button" @click="gotoBuy()">立即续费</div>
                    </div>
                    <div class="trial-info-container" v-show="isTrial && !isExpired && remainingDays <= 7 && !isThirdPart">
                        <div class="trial-info">
                            {{ isExpired ? "推荐开通专业版，立享多项专属功能" : "您的专业版功能体验期限还剩 " + remainingDays + "天" }}
                        </div>
                        <div class="trial-icon" v-show="!isExpired">
                            <img src="@/assets/Menu/pro-notice.png" />
                        </div>
                        <span class="trial-txt"
                            >新创建账套，自动开通专业版专属功能体验期，包括资金、工资、发票、资产、多用户等。体验期结束后，账套自动变为免费版，可以长期使用。</span
                        >
                        <div class="trial-button" @click="buyPro()">{{ isExpired ? "了解更多" : "查看详情 >>" }}</div>
                    </div>
                </div>
                <div class="tool-bar">
                    <div v-show="beginnersGuideBtnShow" class="tool-bar-guide button solid-button hot" @click="gotoGuide()">
                        新手指引
                        <span>{{ beginnersGuidePaceRestSteps }}</span>
                    </div>
                    <div class="goto-pro ml-20" @click="tryGoToPro()" v-show="hasProAccountSet && !isThirdPart">前往财务专业版&nbsp;>></div>
                    <div class="tool-bar-menu" @click="goToFree()" v-show="isProSystem && !isThirdPart">前往财务免费版</div>
                    <div v-show="!isThirdPart" class="tool-bar-menu" @click=" gotoHelpCenter()">帮助中心</div>
                    <div v-show="!isWxwork && !isThirdPart" class="tool-bar-menu" @click=" gotoStudyCenter()">学习中心</div>

                    <div class="tool-bar-menu more">
                        更多
                        <div class="more-drop">
                            <ul>
                                <li v-if="!isWxwork && !isThirdPart">
                                    <div @click="gotoWWW()">官网首页</div>
                                </li>
                                <li>
                                    <div @click="gotoLMCalculator">计算器</div>
                                </li>
                                <li>
                                    <div @click="() => (showTaxCalculator = true)">个税计算器</div>
                                </li>
                                <li v-if="!isWxwork">
                                    <div @click="gotoMessageSearch">查询导航</div>
                                </li>
                                <li>
                                    <div @click="gotoAccountingEntry">分录大全</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="tool-bar-menu" v-if="isShowProWorkBench" @click="gotoWorkBench()">
                        {{ isInWxWork() ? "云财务工作台" : "企业工作台" }}
                    </div>
                    <div class="news-tip" v-show="!isThirdPart">
                        <div class="news-icon" @mouseenter="getCurrentNewsList()"></div>
                        <span class="news-count" v-show="unreadNewsCount > 0"></span>
                        <div class="news-container-box">
                            <div class="news-container" v-if="!isAccountingAgent" ref="newsContainerRef">
                                <div class="news-top">
                                    <div
                                        :class="['news-tab', { active: currentNews === 2 }]"
                                        @click="
                                            currentNews = 2;
                                            getNewsList();
                                        "
                                    >
                                        系统消息
                                        <span v-show="unreadNewsCount2 > 0">({{ formatUnreadCount(unreadNewsCount2) }})</span>
                                    </div>
                                    <div
                                        :class="['news-tab', { active: currentNews === 1 }]"
                                        @click="
                                            currentNews = 1;
                                            getNewsList();
                                        "
                                    >
                                        订单消息
                                        <span v-show="unreadNewsCount1 > 0">({{ formatUnreadCount(unreadNewsCount1) }})</span>
                                    </div>
                                    <div class="range-select-btn" @mouseleave="switchNewsRangeListShow = false">
                                        <span class="news-select-checked" @click="switchNewsRangeListShow = true">{{
                                            newsRange === 2 ? "全部" : "未读"
                                        }}<el-icon><ArrowDown /></el-icon></span>
                                        <div class="switch-news-range" v-show="switchNewsRangeListShow">
                                            <div class="item" @click="handleSwitchNewsRange(2)">全部</div>
                                            <div class="item" @click="handleSwitchNewsRange(1)">未读</div>
                                        </div>
                                    </div>
                                </div>
                                <el-scrollbar ref="newsListScrollBarRefTop" max-height="320" :always="false">
                                    <ul class="news-list" v-if="newsList.length">
                                        <li class="news-item" v-for="(item, index) in filterNewsByLastMonth(newsList)" :key="index">
                                            <div :class="{ 'not-read': item.isUnread }">
                                                <div class="news-item-top">
                                                    <span class="news-item-title"> {{ item.title }} </span>
                                                    <span class="news-item-date">{{ item.createdDate.substring(0, 10) }}</span>
                                                </div>
                                                <div class="news-item-content">
                                                    <a @click.prevent="readNewsChangeStatus(item)">{{ formatterNewsContent(item.body) }}</a>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </el-scrollbar>

                                <div class="news-bottom" v-if="newsList.length">
                                    <div
                                        class="button solid-button"
                                        v-if="newsList.some((v) => v.isUnread)"
                                        @click="changeNewsListStatus"
                                    >
                                        标记所有为已读
                                    </div>
                                    <div v-else></div>
                                    <div class="news-bottom-btn link" @click="moreNewsListDialogVisible = true">查看更多</div>
                                </div>
                                <div class="no-news" v-else>
                                    <img src="@/assets/App/xiaoxi-none.png" alt="" />
                                    暂无{{ newsRange === 2 ? "更多" : "未读" }}消息
                                </div>
                            </div>
                            <div class="news-container accounting-agent" v-else>
                                <div class="news-title">
                                    消息中心(<span>{{ unreadNewsCount }}</span
                                    >)
                                </div>
                                <ul v-show="unreadNewsCount > 0">
                                    <li v-for="(item, index) in newsList" :key="index">
                                        <a
                                            href="https://www.ningmengyun.com/CenterMessage.aspx"
                                            @click.prevent="globalWindowOpen('https://www.ningmengyun.com/CenterMessage.aspx')"
                                            >{{ item.created_date }}{{ item.title }}</a
                                        >
                                    </li>
                                </ul>
                                <div v-show="unreadNewsCount === 0" class="no-news">无未读消息</div>
                                <a
                                    class="more link"
                                    href="https://www.ningmengyun.com/CenterMessage.aspx"
                                    @click.prevent="globalWindowOpen('https://www.ningmengyun.com/CenterMessage.aspx')"
                                    >查看更多</a
                                >
                            </div>
                        </div>
                    </div>
                    <div class="user-info" :class="{ base: !isWxwork }">
                        <div class="head-container">
                            <img v-if="!isWxwork" class="head-img" :src="headImgUrl" />
                            <img v-else class="head-img" :src="getCookie('avatar')" />
                        </div>

                        <div :class="['user-info-container-box',{'is-third-part':isThirdPart}]">
                            <div class="user-info-container" v-if="isThirdPart">
                                <div class="log-out" @click="logout()">退出</div>
                                <div class="user-info-content">
                                    <img class="head-img-bigger" :src="headImgUrl" />
                                    <div class="infos">
                                        <ToolTip :content="userName">
                                            <div class="user-name">账号：{{ userName }}</div>
                                        </ToolTip>
                                        <div class="btns">
                                            <div class="btn" @click="gotoPersonalInfo">个人设置</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="user-info-container" v-else-if="!isAccountingAgent">
                                <div :class="['user-info-content', { ispro: isProSystem }]">
                                    <img class="head-img-bigger" :src="headImgUrl" />
                                    <img v-if="isProSystem" class="head-vip-icon" src="@/assets/App/vip-icon-45deg.png" alt="" />
                                    <div class="infos2">
                                        <div class="lemon-finance">{{ userName }}</div>
                                        <div v-if="!isProSystem && !isAccountSet1" style="cursor: pointer;" @click="buyPro()">
                                            <span class="version">免费版</span><span v-show="!hasProAccountSet">（您还没开通专业版，去看看）</span
                                            ><span class="arrow"
                                                ><el-icon v-show="!hasProAccountSet"><ArrowRight /></el-icon
                                            ></span>
                                        </div>
                                        <div v-else-if="isProSystem">
                                            <span class="pr-10">专业版至：{{ proExpiredTime }}</span
                                            ><span class="arrow"
                                                ><el-icon><ArrowRight /></el-icon
                                            ></span>
                                        </div>
                                    </div>
                                    <div v-if="isProSystem" class="vip-renew-btn" @click="gotoBuy()">续 费</div>
                                </div>
                                <div class="upgrade-vip-content" v-show="!isProSystem && !isAccountSet1">
                                    <div class="upgrade-tip">
                                        <div class="tip">
                                            <div class="tip-upgrade">升级专业版</div>
                                            <div class="tip-expired">
                                                专业版体验期&nbsp;<span v-if="!isExpired">仅剩{{ expiredTime }}天！</span
                                                ><span v-else>已过期{{ expiredTime }}天！</span>
                                            </div>
                                        </div>
                                        <div class="upgrade-vip-btn" @click="handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false })">
                                            立即升级
                                        </div>
                                    </div>
                                    <div class="upgrade-rights">
                                        <div class="rights-title">升级专业版 尊享更多权益</div>
                                        <div class="rights-list">
                                            <div class="rights-item" v-for="item in rightsList" :key="item.order" @click="buyPro()">
                                                <img :src="getRightsImg(item.order)" alt="" />
                                                <span>{{ item.name }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="settings-and-services">
                                    <div class="settings-title">设置与服务</div>
                                    <div class="setting-list">
                                        <div class="setting-item" @click="gotoPersonalInfo">
                                            <div class="item-name">
                                                <img src="@/assets/App/personal.png" />
                                                <span class="setting-name">个人设置</span>
                                            </div>

                                            <span class="arrow"
                                                ><el-icon><ArrowRight /></el-icon
                                            ></span>
                                        </div>
                                        <div class="setting-item" @click="gotoOrderList()" v-show="!isThirdPart">
                                            <div class="item-name">
                                                <img src="@/assets/App/order.png" />
                                                <span class="setting-name">我的订单</span>
                                            </div>
                                            <span class="arrow"
                                                ><el-icon><ArrowRight /></el-icon
                                            ></span>
                                        </div>
                                        <div class="setting-item" @click="logout()">
                                            <div class="item-name">
                                                <img src="@/assets/App/exit.png" />
                                                <span class="setting-name">退出登录</span>
                                            </div>
                                            <span class="arrow"
                                                ><el-icon><ArrowRight /></el-icon
                                            ></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="aa-user-info-container" v-else>
                                <div class="btn" v-show="isBossSystem" @click="changeAACompany()">切换代账公司</div>
                                <div class="btn" @click="logout()">退出</div>
                            </div>
                        </div>
                        <div class="wxwork-username" v-if="isWxwork">
                            <ww-open-data type="userName" :openid="openid"></ww-open-data>
                        </div>
                    </div>
                    <div v-if="!isThirdPart && pendingPaymentOrderVisible.show" class="pending-payment-order-poppver" @click="gotoOrderListCheckup(pendingPaymentOrderVisible)">您有一笔订单待支付！<el-icon @click.stop.prevent="pendingPaymentOrderVisible.show = false"><Close /></el-icon></div>
                </div>
            </div>
            <div class="ad-container" v-show="!fullScreen && !hideMenu && !isInLemonClient && !isThirdPart && thirdParthasLoading && showAdContainer">
                <img :src="topAdImg.src" alt="" @click="globalWindowOpen(topAdImg.link)">
                <el-icon @click="closeAdContainer()"><Close /></el-icon>
            </div>
            <div :class="{ 'center-container': !isErp }" v-show="!fullScreen && !hideMenu && thirdParthasLoading">
                <div
                    class="tabs-container"
                    v-show="!hideMenu && thirdParthasLoading"
                    :style="{ margin: '0', width: !isWxwork ? '93%' : '97%' }"
                >
                    <div class="tab-left-btn" @click="tabToLeft()"></div>
                    <el-scrollbar ref="tabScrollbarRef" @scroll="tabOnScroll" :height="0" :min-size="0">
                        <div
                            class="tabs-content"
                            ref="tabScrollbarInnerRef"
                            @dragstart="handleDragStart($event)"
                            @dragenter="handleDragEnter($event)"
                            @dragend="handleDragEnd"
                        >
                            <div
                                v-for="(item, index) in routerArray"
                                :key="item.name"
                                class="tab-content"
                                :class="{
                                    alive: item.alive,
                                    'can-close': index !== 0,
                                    'can-refresh': item.alive && item.name !== 'NewVoucher',
                                }"
                                @click="item.alive ? '' : clickTabs(item)"
                                @dblclick="index === 0 ? '' : routerArrayStore.removeRouter(item.path)"
                                @click.right="tabRightClick($event, item)"
                                :draggable="item.name !== 'Default'"
                            >
                                {{ item.title }}
                                <div
                                    class="refresh-btn"
                                    @click.stop="
                                        routerArrayStore.refreshRouter(item.path);
                                        currentRightClickTab = undefined;
                                    "
                                ></div>
                                <div class="close-btn" @click.stop="closeBtnClick(item.path)"></div>
                            </div>
                        </div>
                    </el-scrollbar>
                    <div class="more-btn" @click="moreBtnClick($event)"></div>
                    <div class="tab-right-btn" @click="tabToRight()"></div>
                </div>
                <div class="right-space" v-if="!isErp && !isInLemonClient" :style="{ width: showFullscreen ? '7%' : '3%' }">
                    <div>
                        <div class="down-up-btn" @click="clickDown" v-if="visibleDownUp">
                            <el-tooltip content='点击收起"顶部栏"' placement="bottom-end" effect="light" :visible="visible">
                                <img src="@/assets/App/up.png" class="img" @mouseenter="visible = true" @mouseleave="visible = false" />
                            </el-tooltip>
                        </div>
                        <div class="down-up-btn" @click="clickUp" v-if="!visibleDownUp">
                            <el-tooltip content='点击展开"顶部栏"' placement="bottom-end" effect="light" :visible="visible">
                                <img src="@/assets/App/down.png" class="img" @mouseenter="visible = true" @mouseleave="visible = false" />
                            </el-tooltip>
                        </div>
                    </div>
                    <div v-if="showFullscreen">
                        <div id="yourButtonId" class="qp-tcqp-btn" v-if="visibleFullScreen" @click="openFullScreen">
                            <img src="@/assets/App/fangda.png" class="img" />
                            <div class="qp-font qp-tcqp-font">全屏</div>
                        </div>
                        <div class="qp-tcqp-btn" v-if="!visibleFullScreen" @click="closeFullScreen">
                            <img src="@/assets/App/suoxiao.png" class="img" />
                            <div class="tcqp-font qp-tcqp-font">退出全屏</div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="router-container"
                :class="{ 'hide-menu': hideMenu, 'in-lemone-client': isInLemonClient }"
                @scroll="mainContainerScroll($event)"
                ref="mainContainerRef"
            >
                <router-view v-slot="{ Component }">
                    <keep-alive :include="routerNameArray">
                        <component :is="Component" />
                    </keep-alive>
                </router-view>
                <component
                    v-for="item in iframeRouterArray"
                    :key="item.path"
                    :is="item.iframeComponet"
                    v-show="$route.path === item.path"
                ></component>
            </div>
        </div>
    </div>
    <div
        class="aside"
        style="height: auto"
        :style="styleObject"
        v-show="!hideMenu && !isThirdPart && thirdParthasLoading"
        @mousedown.capture="handleAsideMouseDown($event)"
        @click.capture="handleAsideClick($event)"
    >
        <div class="item" title="升级专业版" v-show="!isProSystem && !isBossSystem && asideDisplay" @click="buyPro()">
            <img class="icon" src="@/assets/Aside/sjzyb.png" draggable="false" />
            <span class="txt">升级专业版</span>
        </div>
        <div class="item" title="专属客服" v-show="isProSystem && asideDisplay">
            <img class="icon" src="@/assets/Aside/zskf.png" draggable="false" />
            <span class="txt">专属客服</span>
            <div class="more">
                <img src="@/assets/Aside/pro-kfewm.png" v-show="!isAccountingAgent" draggable="false" />
                <img src="@/assets/Aside/aapro-kfewm.png" v-show="isAccountingAgent" draggable="false" />
            </div>
        </div>
        <div
            class="item"
            title="在线客服"
            v-show="!isProSystem && asideDisplay"
            @click="isWxworkFollowUser ? globalWindowOpen('https://ningmengyun.s2.udesk.cn/im_client/?web_plugin_id=31057') : ''"
        >
            <img class="icon" src="@/assets/Aside/zxkf.png" draggable="false" />
            <span class="txt">在线客服</span>
            <div class="more" v-show="!isWxworkFollowUser">
                <img src="@/assets/Aside/kfewm.png" v-show="!isAccountingAgent" draggable="false" />
                <img src="@/assets/Aside/aa-kfewm.png" v-show="isAccountingAgent" draggable="false" />
            </div>
        </div>
        <div
            class="item"
            title="免费导账"
            v-show="!isProSystem && asideDisplay"
            @click="isAccountingAgent ? '' : globalWindowOpen('https://ningmengyun.s2.udesk.cn/im_client/?web_plugin_id=31057')"
        >
            <img class="icon" src="@/assets/Aside/mfdz.png" draggable="false" />
            <span class="txt">免费导账</span>
            <div class="more">
                <img src="@/assets/Aside/dzewm.png" v-show="!isAccountingAgent" draggable="false" />
                <img src="@/assets/Aside/aa-dzewm.png" v-show="isAccountingAgent" draggable="false" />
            </div>
        </div>
        <div class="item" title="加购续费" v-show="isProSystem && !isWxwork && asideDisplay" @click="buyMore()">
            <img class="icon" src="@/assets/Aside/jgxf.png" draggable="false" />
            <span class="txt">加购续费</span>
        </div>
        <div class="item" title="下载APP" v-show="!isAccountingAgent && asideDisplay">
            <img class="icon" src="@/assets/Aside/xzapp.png" draggable="false" />
            <span class="txt">下载APP</span>
            <div class="more">
                <img src="@/assets/Aside/xzappewm.png" draggable="false" />
            </div>
        </div>
        <div class="item" title="关于我们" v-show="asideDisplay">
            <img class="icon" src="@/assets/Aside/gywm.png" draggable="false" />
            <span class="txt">关于我们</span>
            <div class="more">
                <img src="@/assets/Aside/gywmewm.png" v-show="!isAccountingAgent" draggable="false" />
                <img src="@/assets/Aside/aa-gywmewm.png" v-show="isAccountingAgent" draggable="false" />
            </div>
        </div>
        <div class="item" title="福利领取" v-show="!isProSystem && asideDisplay">
            <img class="icon" src="@/assets/Aside/fllq.png" draggable="false" />
            <span class="txt">福利领取</span>
            <div class="more" style="bottom: 0; top: initial; transform: none">
                <img style="width: 460px; height: 340px" src="https://j6.ningmengyun.com/fllq-pic.png" draggable="false" />
            </div>
        </div>
        <div
            class="item"
            style="overflow: hidden; justify-content: center"
            title="返回顶部"
            v-show="gotoTopBtnDisplay && asideDisplay"
            @click="handleScrollToTop"
        >
            <img class="icon" src="@/assets/Aside/fhdb.png" draggable="false" />
            <span class="txt">返回顶部</span>
        </div>
        <div class="item hide-aside" title="隐藏侧边" v-show="asideDisplay" @click="handleAsideHideOrShow(false)">
            <span class="txt">隐藏</span>
            <img src="@/assets/Aside/back_right.png" draggable="false" />
        </div>
        <div
            class="item"
            style="width: 22px; height: 130px; border-radius: 0; overflow: hidden"
            v-show="!asideDisplay"
            @click="handleAsideHideOrShow(true)"
        >
            <img style="width: 30px; height: 130px" src="@/assets/Aside/xscb.png" draggable="false" />
        </div>
    </div>
    <div class="tab-right-menu" v-show="currentRightClickTab !== undefined" @click="$event.stopPropagation()" ref="tabRightMenuRef">
        <div class="menu-btn close-all" @click="rightMenuCloseAll()">关闭全部</div>
        <div class="menu-btn close-others" @click="rightMenuCloseOthers()">关闭其他页</div>
        <div class="menu-btn close-current" @click="rightMenuCloseCurrent()">关闭当前页</div>
        <div class="menu-btn refresh-current" @click="rightMenuRefreshCurrent()">刷新当前页</div>
    </div>
    <AccountSetExpire
        ref="accountSetExpireRef"
        :hasAccountSet="hasAccountSet"
        :on-close="accountSetExpireOnClose"
        :show-buy-dialog="accountSetExpireShowBuyDialog"
        :newAsid="newAsid"
    ></AccountSetExpire>
    <TaxCalculator :showTaxCalculator="showTaxCalculator" @close="() => (showTaxCalculator = false)" />
    <template v-if="isWxwork && isProSystem">
        <ProWorkBenchDialog ref="proWorkBenchDialogRef"></ProWorkBenchDialog>
    </template>
    <LMCalculator ref="LMCalculatorRef"></LMCalculator>
    <ChangeAACompanyDialog ref="changeAACompanyDialogRef"></ChangeAACompanyDialog>
    <WxworkPermissionAlertDialog ref="wxworkPermissionAlertDialogRef"></WxworkPermissionAlertDialog>
    <div class="tab-refresh-btn-tip" ref="tabRefreshBtnTipRef" v-show="tabRefreshBtnTipDisplay">点击此处，刷新当前页面</div>
    <el-dialog v-model="moreNewsListDialogVisible" width="800" class="app-news-dialog">
        <div class="news-container">
            <div class="news-top">
                <div
                    :class="['news-tab', { active: currentNews === 2 }]"
                    @click="
                        changeNewsType(2);
                    "
                >
                    系统消息
                    <span v-show="unreadNewsCount2 > 0">({{ formatUnreadCount(unreadNewsCount2) }})</span>
                </div>
                <div
                    :class="['news-tab', { active: currentNews === 1 }]"
                    @click="
                        changeNewsType(1);
                    "
                >
                    订单消息
                    <span v-show="unreadNewsCount1 > 0">({{ formatUnreadCount(unreadNewsCount1) }})</span>
                </div>
                <div :class="['button', 'solid-button']" v-show="newsList.some((v) => v.isUnread)"  @click="changeNewsListStatus">
                    标记所有为已读
                </div>

                <div class="range-select-btn" @mouseleave="switchNewsRangeListShow = false">
                    <span class="news-select-checked" @click="switchNewsRangeListShow = true">{{ newsRange === 2 ? "全部" : "未读" }}<el-icon><ArrowDown /></el-icon></span>
                    <div class="switch-news-range" v-show="switchNewsRangeListShow">
                        <div class="item" @click="handleSwitchNewsRange(2)">全部</div>
                        <div class="item" @click="handleSwitchNewsRange(1)">未读</div>
                    </div>
                </div>
            </div>
            <div class="no-news" v-if="!newsList.length">
                <img src="@/assets/App/xiaoxi-none.png" alt="" />
                暂无{{ newsRange === 2 ? "更多" : "未读" }}消息
            </div>
            <el-scrollbar ref="newsListScrollBarRefDialog" v-else max-height="478" :always="false">
                <ul class="news-list">
                    <li class="news-item" v-for="(item, index) in newsList" :key="index">
                        <div :class="{ 'not-read': item.isUnread }">
                            <div class="news-item-title">
                                    {{ item.title }}
                            </div>
                            <div class="news-item-content">
                                    <a @click.prevent="readNewsChangeStatus(item)">{{ formatterNewsContent(item.body) }}</a>
                                <span class="news-item-date">{{ item.createdDate.substring(0, 10) }}</span>
                            </div>
                        </div>
                    </li>
                </ul>
            </el-scrollbar>
        </div>
    </el-dialog>
    <el-dialog
        v-model="newsDetailtDialog.visible"
        title="消息详情"
        width="650"
        class="app-news-dialog"
    >
        <div class="news-container">
            <div class="news-detail-top">
                <div class="news-detail-title">{{ newsDetailtDialog.title }}</div>
                <div class="news-detail-date">{{ newsDetailtDialog.date.substring(0,10) }}</div>
            </div>
            <el-scrollbar class="news-detail-container" max-height="300" :always="false">
                <div class="news-detail-content">
                    {{ newsDetailtDialog.content }}
                </div>
            </el-scrollbar>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click="newsDetailtDialog.visible = false"> 关闭</a>
        </div>
    </el-dialog>
</template>

<style lang="less">
#app {
    font-family: 微软雅黑;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    height: 100%;
}
.el-select-dropdown__option-item {
    .locked-accountset {
        background: url(@/assets/Settings/lock-icon-black.png) no-repeat;
        background-position: right 3px center;
        background-size: 13px 16px;
        padding-right: 19px;
        word-break: break-all;
    }
    .unlocked-accountset {
        background: url(@/assets/Settings/unlock-icon-black.png) no-repeat;
        background-position: right 3px center;
        background-size: 13px 16px;
        padding-right: 19px;
        word-break: break-all;
    }
    &:hover,
    &.hover {
        .locked-accountset {
            background-image: url(@/assets/Settings/lock-icon-white.png);
        }
        .unlocked-accountset {
            background-image: url(@/assets/Settings/unlock-icon-white.png);
        }
    }
}
.el-dialog.app-news-dialog {
    border-radius: 4px;
    .el-dialog__header .el-dialog__headerbtn {
        height: 46px;
    }
    .el-dialog__body {
        .news-container {
            width: 100%;
            background-color: var(--white);
            display: flex;
            flex-direction: column;
            align-items: stretch;
            font-size: 14px;
            border-radius: 4px;

            .news-top {
                height: 40px;
                padding: 4px 20px 2px;
                border-bottom: 1px solid var(--border-color);
                line-height: 40px;
                color: #767676;
                .news-tab {
                    float: left;
                    margin-right: 30px;
                    cursor: pointer;
                    padding-bottom: 1px;
                    &.active {
                        color: #333;
                        border-bottom: 2px solid var(--main-color);
                        span {
                            color: red;
                        }
                    }
                }
                .button {
                    float: left;
                    margin: 6px 0;
                    width: auto;
                    padding: 0 10px;
                }
                .range-select-btn {
                    float: right;
                    position: relative;
                    right: 32px;
                    cursor: pointer;

                    .news-select-checked {
                        .el-icon {
                            position: relative;
                            top: 2px;
                            left: 2px;
                        }
                    }
                    .switch-news-range {
                        position: absolute;
                        top: 37px;
                        right: 0;
                        width: 52px;
                        background-color: var(--white);
                        z-index: 9999;
                        line-height: 20px;
                        box-shadow: 0px 0px 7px -1px rgba(0, 0, 0, 0.25);
                        .item {
                            padding: 5px 10px;
                            cursor: pointer;
                            &:hover {
                                color: var(--main-color);
                            }
                        }
                    }
                }
            }
            .no-news {
                height: 478px;
                color: #9d9d9d;
                font-size: 14px;
                line-height: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                img {
                    width: 76px;
                    height: 73px;
                }
            }
            .news-detail-container {
                height:300px;
                text-align: left;
            }
            .el-scrollbar__wrap {
                padding: 0 30px 20px;
            }
            .news-list {
                list-style-type: none;
                padding: 0;
                margin: 0;
                min-height: 468px;

                .news-item {
                    color: #666;
                    font-size: 13px;
                    margin-top: 10px;
                    .not-read {
                        position: relative;
                        &::before {
                            content: " ";
                            display: inline-block;
                            width: 6px;
                            height: 6px;
                            background-color: var(--red);
                            border-radius: 100%;
                            position: absolute;
                            top: 13px;
                            left: -12px;
                        }
                    }

                    .tooltip-title {
                        height: 30px;
                    }

                    .news-item-title {
                        color: #333;
                        font-size: 14px;
                        width: 500px;
                        display: inline-block;
                        text-align: left;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: flex;
                        line-height: 30px;
                    }
                    .news-item-content {
                        display: flex;
                        justify-content: space-between;
                        width: 100%;
                        line-height: 20px;
                        text-align: left;
                        color: #767676;
                        cursor: pointer;
                        a {
                            width: 640px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            text-decoration: none;
                            color: #767676;
                        }
                    }
                }
            }
            .news-bottom {
                margin-top: 10px;
                height: 46px;
                padding: 0 20px;
                border-top: 1px solid var(--border-color);
                text-align: center;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .button {
                    width: auto;
                    padding: 0 10px;
                }
                &:hover {
                    color: var(--main-color);
                }
            }
            .news-detail-top {
                display: flex;
                justify-content: space-between;
                padding: 20px 20px 10px;
                .news-detail-title {
                    font-size: 16px;
                    color: #333;
                    font-weight: 600;
                }
                .news-detail-date {
                    color: #767676;
                }
            }
            .news-detail-content {
                padding: 10px 20px;
                font-size: 14px;
                color: #333;
                height: 300px;
                line-height: 24px;
                text-align: left;
            }
        }
        .btns {
            height: 46px;
            display: flex;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>

<style lang="less" scoped>
@import "@/style/Functions.less";

.main-container {
    height: 100%;
    width: 100%;
    min-width: 1440px;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    background-color: var(--background-color);

    .left-container {
        width: var(--menu-container-width);
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: stretch;

        .logo {
            height: 50px;
            background-repeat: no-repeat;
            background-size: 100%;

            &.pro {
                background-image: url("@/assets/Menu/pro-logo.png");
            }

            &.free {
                background-image: url("@/assets/Menu/free-logo.png");
            }
        }

        .menu-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: stretch;
            background-color: var(--menu-background-color);
            position: relative;

            .menu-node-container {
                display: flex;
                align-items: stretch;
                margin-top: 1px;
                cursor: pointer;
                outline: none;
                text-decoration: none;
                position: relative;

                &::before {
                    content: " ";
                    align-self: stretch;
                    width: 3px;
                    background-color: transparent;
                    position: absolute;
                    height: 100%;
                }

                .menu-node-content {
                    flex: 1;
                    padding: 10px 12px;
                    height: 28px;
                    display: flex;
                    align-items: center;

                    .menu-icon {
                        width: 14px;
                        height: 14px;
                        margin-left: 6px;
                        margin-right: 6px;

                        &.selected {
                            display: none;
                        }
                    }

                    span {
                        font-size: 16px;
                        line-height: 22px;
                        color: var(--menu-font-color);
                        font-weight: 500;
                    }

                    .vip-icon {
                        width: 14px;
                        height: 13px;
                        align-self: flex-start;
                        margin-top: 2px;
                        margin-left: 3px;
                    }
                }

                .children-more-icon {
                    align-self: center;
                    display: none;
                    width: 0;
                    height: 0;
                    border-top: 6px solid transparent;
                    border-bottom: 6px solid transparent;
                    border-right: 9px solid var(--white);
                    margin-left: -9px;
                }

                .children-menu-node-content {
                    width: 164px;
                    position: absolute;
                    top: 0;
                    left: 100%;
                    display: none;
                    flex-direction: column;
                    align-items: flex-start;
                    background: var(--white);
                    box-shadow: 2px 4px 15px 0px rgba(17, 31, 65, 0.2);
                    z-index: 9999;
                    cursor: default;
                    padding: 8px 0 8px 2px;

                    a {
                        outline: none;
                        text-decoration: none;
                        text-align: left;
                        color: #111;
                        line-height: 20px;
                        font-size: 14px;
                        cursor: pointer;
                        height: 40px;
                        line-height: 40px;
                        width: 100%;
                        position: relative;
                        padding-left: 28px;
                        box-sizing: border-box;

                        &:hover {
                            color: var(--white);
                            background-color: var(--main-color);
                        }

                        .hot {
                            display: inline-block;
                            width: 21px;
                            height: 12px;
                            background-image: url("@/assets/Icons/hot.png");
                            background-repeat: no-repeat;
                            background-size: 100% 100%;
                            position: absolute;
                            margin-left: 5px;
                            margin-top: 5px;
                        }
                        .vip-icon {
                            width: 20px;
                            height: 20px;
                            align-self: flex-start;
                            margin-top: 2px;
                            margin-left: 3px;
                        }
                    }
                }

                &.vip {
                    .menu-node-content {
                        .menu-icon {
                            width: 22px;
                            height: 22px;
                            margin-left: 2px;
                            margin-right: 2px;
                        }

                        span {
                            color: #f5d6b5;
                        }
                    }

                    &.selected,
                    &:hover {
                        .menu-node-content {
                            background-color: rgba(246, 217, 185, 0.18);

                            .menu-icon {
                                display: inline-block;
                            }

                            span {
                                color: #f5d6b5;
                            }
                        }
                    }
                }

                &.selected {
                    &::before {
                        background: linear-gradient(165deg, #5fe688 0%, #3fcb7e 100%);
                    }

                    .menu-node-content {
                        .menu-icon {
                            display: none;

                            &.selected {
                                display: inline-block;
                            }
                        }

                        span {
                            color: var(--menu-green);
                        }
                    }
                }

                &:hover {
                    .menu-node-content {
                        background: rgba(255, 255, 255, 0.15);
                    }

                    .children-more-icon {
                        display: inline-block;
                    }

                    .children-menu-node-content {
                        display: flex;
                    }
                }

                &.selected {
                    .menu-node-content {
                        background: #113c1e;
                    }
                }
            }

            .import-free-accountset {
                background-image: url("@/assets/Menu/import-free-menu-bg.png");
                width: 120px;
                height: 28px;
                background-size: 100%;
                background-repeat: no-repeat;
                align-self: center;
                font-size: var(--font-size);
                font-weight: 500;
                color: var(--white);
                line-height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                outline: none;
                text-decoration: none;
                margin: 10px 0;
            }

            &.pro {
                .menu-shadow {
                    top: 48px;
                }
            }

            &.pro-scm {
                .menu-shadow {
                    top: 48px;
                }
            }
        }

        .menu-shadow {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--menu-container-width);
            display: inline-block;
        }
    }

    .right-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        width: 0;

        .top-container {
            height: 44px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            background-color: var(--white);

            .accountset-info {
                display: flex;
                align-items: center;
                padding-left: 20px;
                :deep(.el-select-v2) {
                    width: 261px;
                    height: 37px;
                    &.visibleSelect {
                        .el-select-v2__placeholder {
                            color: var(--border-color);
                        }
                    }
                    .el-select-v2__popper {
                        z-index: 9999 !important;
                    }
                    .el-select-v2__placeholder {
                        text-align: left;
                    }
                }
                :deep(.el-select) {
                    width: 261px;
                    height: 37px;
                }

                .period-info {
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--font-color);
                    margin-left: 23px;
                    white-space: nowrap;
                }

                .trial-info-container {
                    display: flex;
                    align-items: center;
                    margin-left: 15px;
                    position: relative;

                    .trial-info {
                        background: #fffcf8;
                        padding: 4px 12px 4px 12px;
                        color: #5e4111;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 20px;
                    }

                    .trial-icon {
                        display: flex;

                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 10px;
                        }

                        &:hover {
                            & + .trial-txt {
                                display: block;
                            }
                        }
                    }

                    .trial-txt {
                        position: absolute;
                        top: 32px;
                        left: -215px;
                        z-index: 999;
                        width: 464px;
                        word-break: break-all;
                        background-color: white;
                        display: none;
                        border: 1px solid #f6f6f6;
                        padding: 5px;
                        color: #5e4111;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 20px;
                    }

                    .trial-button {
                        background: #fdf5e8;
                        font-weight: 500;
                        padding: 4px 12px 4px 12px;
                        cursor: pointer;
                        color: #5e4111;
                        font-size: 14px;
                        line-height: 20px;
                    }
                }

                .pro-renew-info-container {
                    display: flex;
                    align-items: center;
                    margin-left: 10px;
                    font-size: 13px;
                    line-height: 20px;
                    color: gray;

                    .renew-button {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 70px;
                        height: 24px;
                        line-height: 20px;
                        font-weight: 600;
                        color: #6c4b15;
                        background: linear-gradient(177deg, #fff7e8 0%, #f4dbb2 100%);
                        border-radius: 5px;
                        cursor: pointer;
                    }
                }

                :deep(.el-select),
                :deep(.el-select-v2) {
                    .new-account-set {
                        a.link {
                            display: inline-block;
                            height: 32px;
                            line-height: 32px;
                            width: 100%;
                        }
                    }
                }
            }

            .tool-bar {
                display: flex;
                align-items: center;
                padding-right: 30px;
                position: relative;
                .tool-bar-guide {
                    position: relative;

                    span {
                        position: absolute;
                        top: -6px;
                        right: -6px;
                        background-color: var(--red);
                        border-radius: 15px;
                        height: 15px;
                        width: 15px;
                        cursor: pointer;
                        color: var(--white);
                        font-size: var(--h5);
                        line-height: 14px;
                        display: flex;
                        justify-content: center;
                    }
                }
                .goto-free {
                    height: 30px;
                    line-height: 20px;
                    background-color: #00c256;
                    color: white;
                    font-size: var(--font-size);
                    border-radius: 15px;
                    padding: 0 15px;
                    cursor: pointer;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    white-space: nowrap;
                }

                .goto-pro {
                    height: 30px;
                    line-height: 20px;
                    background: linear-gradient(135deg, #fff6eb, #ffdbb8);
                    color: #5e4111;
                    font-size: var(--font-size);
                    font-weight: bolder;
                    border-radius: 15px;
                    padding: 0 15px;
                    cursor: pointer;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    white-space: nowrap;
                }

                .tool-bar-menu {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    cursor: pointer;
                    position: relative;
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                    margin-left: 20px;

                    &.hot {
                        &::after {
                            content: " ";
                            display: inline-block;
                            background-image: url("@/assets/App/hot.png");
                            background-repeat: no-repeat;
                            background-size: 100%;
                            position: absolute;
                            top: -9px;
                            right: -11px;
                            width: 14px;
                            height: 17px;
                        }
                    }

                    &.more {
                        &::after {
                            content: " ";
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            background-image: url("@/assets/App/xia.png");
                            background-repeat: no-repeat;
                            background-size: 100%;
                            margin-left: 6px;
                        }
                        .more-drop {
                            display: none;
                            position: absolute;
                            top: 18px;
                            left: -51px;
                            z-index: 9999;
                            padding-top: 15px;
                            width: 100px;
                        }
                        ul {
                            flex-direction: column;
                            align-items: stretch;
                            padding: 0;
                            margin: 0;
                            list-style: none;
                            box-sizing: border-box;
                            cursor: default;
                            border: 1px solid #eee;
                            box-shadow: 0px 30px 60px 0px rgba(0, 0, 0, 0.15);
                            border-radius: 4px;
                            cursor: pointer;
                            &::before {
                                content: "";
                                position: absolute;
                                top: 10px;
                                right: 18px;
                                width: 10px; 
                                height: 10px; 
                                transform: rotate(45deg); 
                                background-color: #fff;
                                border-left: 1px solid #eee;
                                border-top: 1px solid #eee;
                            }

                            li {
                                background-color: var(--white);
                                div {
                                    padding-left: 10px;
                                    cursor: pointer;
                                    color: var(--font-color);
                                    font-size: var(--h4);
                                    line-height: 30px;
                                    text-align: left;

                                    &:hover {
                                        color: var(--white);
                                        background-color: var(--main-color);
                                    }

                                    &:active {
                                        color: var(--white);
                                        background-color: var(--dark-main-color);
                                    }
                                }

                                &:first-child {
                                    padding-top: 8px;
                                    border-top-left-radius: 2px;
                                    border-top-right-radius: 2px;
                                }

                                &:last-child {
                                    border-bottom-left-radius: 2px;
                                    border-bottom-right-radius: 2px;
                                    padding-bottom: 8px;
                                }
                            }
                        }
                    }

                    &:hover {
                        color: var(--main-color);

                        &.more {
                            .more-drop {
                                display: block;
                                ul {
                                    display: flex;
                                }
                            }
                        }
                    }

                    &:active {
                        color: var(--dark-main-color);
                    }
                }

                .news-tip {
                    display: flex;
                    position: relative;
                    margin-left: 24px;

                    .news-icon {
                        width: 21px;
                        height: 21px;
                        cursor: pointer;
                        background: url(@/assets/App/xiaoxi-gray.png) no-repeat;
                        background-size: cover;

                    }

                    .news-count {
                        position: absolute;
                        top: -2px;
                        right: -2px;
                        background-color: var(--red);
                        border-radius: 6px;
                        height: 6px;
                        width: 6px;
                    }

                    .news-container-box {
                        display: none;
                        position: absolute;
                        top: 20px;
                        right: -90px;
                        padding-top: 14px;
                        z-index: 2999;

                        .news-container {
                            width: 328px;
                            box-shadow: 0px 30px 60px 0px rgba(0,0,0,0.15);
                            border: 1px solid #DDDDDD;
                            background-color: var(--white);
                            display: flex;
                            flex-direction: column;
                            align-items: stretch;
                            font-size: 14px;
                            border-radius: 4px;
                            .news-top {
                                height: 40px;
                                padding: 0 12px 0 18px;
                                border-bottom: 1px solid var(--border-color);
                                line-height: 40px;
                                color: #767676;
                                .news-tab {
                                    float: left;
                                    margin-right: 30px;
                                    cursor: pointer;

                                    &.active {
                                        color: #333;
                                        border-bottom: 2px solid var(--main-color);
                                        span {
                                            color: red;
                                        }
                                    }
                                }
                                .range-select-btn {
                                    float: right;
                                    position: relative;
                                    margin-right: 6px;
                                    cursor: pointer;

                                    .el-icon {
                                        position: relative;
                                        top: 2px;
                                        left: 2px;
                                    }
                                    .switch-news-range {
                                        position: absolute;
                                        top: 37px;
                                        right: 0;
                                        width: 52px;
                                        background-color: var(--white);
                                        z-index: 9999;
                                        line-height: 20px;
                                        box-shadow: 0px 0px 7px -1px rgba(0, 0, 0, 0.25);
                                        cursor: pointer;

                                        .item {
                                            padding: 5px 10px;
                                            cursor: pointer;
                                            &:hover {
                                                color: var(--main-color);
                                            }
                                        }
                                    }
                                }
                            }
                            .el-scrollbar {
                                position: unset;
                                :deep(.el-scrollbar__bar.is-vertical){
                                    top:56px !important;
                                }
                            }
                            .news-list {
                                list-style-type: none;
                                padding: 0 17px 0 28px;
                                margin: 0 0 10px 0;
                                .news-item {
                                    color: #666;
                                    font-size: 13px;
                                    margin: 20px 0;
                                    .not-read {
                                        position: relative;
                                        &::before {
                                            content: " ";
                                            display: inline-block;
                                            width: 6px;
                                            height: 6px;
                                            background-color: var(--red);
                                            border-radius: 100%;
                                            position: absolute;
                                            top: 8px;
                                            left: -12px;
                                        }
                                    }
                                    .news-item-top {
                                        display: flex;
                                        justify-content: space-between;
                                        line-height: 20px;
                                        margin-bottom: 5px;
                                        .span_wrap {
                                            height: 30px;
                                        }
                                        .tooltip-title {
                                            height: 30px;
                                        }

                                        .news-item-title {
                                            color: #333;
                                            font-size: 14px;
                                            width: 162px;
                                            display: inline-block;
                                            text-align: left;
                                            white-space: nowrap;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                        }
                                    }
                                    .news-item-content {
                                        display: -webkit-box;
                                        -webkit-box-orient: vertical;
                                        -webkit-line-clamp: 2;
                                        overflow: hidden;
                                        width: 100%;
                                        line-height: 18px;
                                        text-align: left;
                                        cursor: pointer;

                                        a {
                                            width: 288px;
                                            text-decoration: none;
                                            color: #767676;
                                        }
                                    }
                                }
                            }
                            .news-bottom {
                                height: 46px;
                                padding: 0 20px;
                                border-top: 1px solid var(--border-color);
                                text-align: center;
                                cursor: pointer;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                .button {
                                    width: auto;
                                    padding: 0 10px;
                                }
                                &:hover {
                                    color: var(--main-color);
                                }
                            }
                            .no-news {
                                height: 270px;
                                color: #9d9d9d;
                                font-size: 14px;
                                line-height: 50px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                flex-direction: column;
                                img {
                                    width: 76px;
                                    height: 73px;
                                }
                            }
                            &.accounting-agent {
                                padding: 15px;
                                .news-title {
                                    display: flex;
                                    align-items: center;
                                    color: var(--font-color);
                                    font-size: var(--h5);
                                    line-height: 16px;
                                    padding-bottom: 6px;
                                    margin-bottom: 6px;
                                    border-bottom: 2px solid var(--border-color);

                                    span {
                                        font-weight: bold;
                                        background-color: var(--white);
                                        color: var(--red);
                                    }
                                }

                                ul {
                                    list-style: none;
                                    display: flex;
                                    flex-direction: column;
                                    align-items: stretch;
                                    margin: 0;
                                    padding: 0;

                                    li {
                                        padding: 5px 0;
                                        border-bottom: 1px solid var(--border-color);
                                        display: flex;
                                        align-items: center;

                                        a {
                                            color: var(--font-color);
                                            font-size: var(--h5);
                                            line-height: 16px;
                                            outline: none;
                                            text-decoration: none;
                                            cursor: pointer;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                            white-space: nowrap;

                                            &:hover {
                                                color: var(--main-color);
                                            }

                                            &:active {
                                                color: var(--dark-main-color);
                                            }
                                        }
                                    }
                                }

                                .no-news {
                                    height: 50px;
                                    color: var(--font-color);
                                    font-size: var(--h5);
                                    line-height: 20px;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                }

                                .more {
                                    margin-top: 15px;
                                    align-self: flex-end;
                                }

                            }
                            
                        }
                    }

                    &:hover {
                        .news-icon {
                            background: url(@/assets/App/xiaoxi-green.png);
                            background-size: cover;
                        }
                        .news-container-box {
                            display: block;
                        }
                    }
                }

                .user-info {
                    display: flex;
                    align-items: center;
                    position: relative;
                    margin-left: 20px;
                    cursor: pointer;
                    .head-container {
                        width: 29px;
                        height: 40px;
                        position: relative;
                        top: 4px;
                        .head-img {
                            width: 29px;
                            height: 29px;
                            border-radius: 100%;
                        }
                    }
                   
                   
                    .user-info-container-box {
                        display: none;
                        position: absolute;
                        top: 29px;
                        right: -6px;
                        width: 375px;
                        padding-top: 10px;
                        cursor: initial;
                        z-index: 2999;
                    

                        .user-info-container {
                            box-sizing: border-box;
                            background: linear-gradient( 179deg, #FFFFFF 0%, #FFFEFB 100%);
                            box-shadow: 0px 30px 60px 0px rgba(0,0,0,0.15);
                            border: 1px solid #DDDDDD;
                            border-radius: 4px;
                            display: flex;
                            flex-direction: column;
                            align-items: stretch;

                            .log-out {
                                color: var(--font-color);
                                font-size: var(--h5);
                                line-height: 17px;
                                cursor: pointer;
                                align-self: flex-end;

                                &:hover {
                                    color: var(--main-color);
                                    text-decoration: underline;
                                }

                                &:active {
                                    color: var(--dark-main-color);
                                }
                            }

                            .user-info-content {
                                display: flex;
                                align-items: flex-start;
                                justify-content: flex-start;
                                padding: 10px 16px 0 20px;

                                .head-img-bigger {
                                    width: 48px;
                                    height: 48px;
                                    border-radius: 100%;
                                    z-index: 9;
                                }
                                .head-vip-icon {
                                    width: 32px;
                                    height: 32px;
                                    margin-left: -24px;
                                    margin-top: -20px;
                                }

                                .infos2 {
                                    margin-left: 10px;
                                    display: flex;
                                    flex-direction: column;
                                    text-align: left;
                                    color: #333;
                                    font-size: 12px;
                                    line-height: 24px;
                                    margin-top: 4px;
                                    flex: 1;
                                    .lemon-finance {
                                        font-size: 16px;
                                        font-weight: 600;
                                    }
                                    .version {
                                        font-weight: 600;
                                    }
                                    .arrow {
                                        color: #b7b7b7;
                                        i {
                                            position: relative;
                                            top: 2px;
                                        }
                                    }
                                    .user-name {
                                        color: var(--font-color);
                                        font-size: var(--font-size);
                                        line-height: var(--line-height);
                                        margin-top: 6px;
                                        margin-bottom: 4px;
                                        // display: -webkit-box;
                                        // -webkit-box-orient: vertical;
                                        // -webkit-line-clamp: 2;
                                        // overflow: hidden;
                                        // text-overflow: ellipsis;
                                        display: block;
                                        width: 150px;
                                        white-space: nowrap;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                    }
                                    .btns {
                                        display: flex;

                                        .btn {
                                            color: var(--font-color);
                                            font-size: var(--h5);
                                            line-height: 17px;
                                            cursor: pointer;

                                            & + .btn {
                                                margin-left: 18px;
                                            }

                                            &:hover {
                                                color: var(--main-color);
                                                text-decoration: underline;
                                            }

                                            &:active {
                                                color: var(--dark-main-color);
                                            }
                                        }
                                    }
                                }
                                &.ispro {
                                    background: linear-gradient(to bottom, #fffcf4, #fffefe);
                                    border-bottom: 1px solid #f7f7f6;
                                    padding: 26px 16px 16px 20px;
                                    .vip-renew-btn {
                                        width: 80px;
                                        height: 36px;
                                        border-radius: 18px;
                                        line-height: 36px;
                                        text-align: center;
                                        background: linear-gradient(160deg, #fffaf0 20%, #f8e4ce 70%);
                                        cursor: pointer;
                                        color: #5e4111;
                                        font-size: 14px;
                                        position: relative;
                                        top: 5px;
                                        font-weight: 600;
                                        margin-left: 5px;
                                    }
                                }
                            }
                            .upgrade-vip-content {
                                padding: 0 16px 0 20px;

                                .upgrade-tip {
                                    height: 80px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    margin-top: 10px;
                                    background: linear-gradient(to bottom, #fef9f1, #fbf2e6);
                                    border: 1px solid #fff4ea;
                                    padding: 16px;
                                    box-sizing: border-box;
                                    text-align: left;
                                    color: #5a3b34;
                                    font-size: 13px;
                                    line-height: 24px;

                                    .tip-upgrade {
                                        font-size: 16px;
                                        font-weight: 600;
                                    }
                                    .tip-expired {
                                        span {
                                            color: #ff5019;
                                            font-size: 14px;
                                            font-weight: bold;
                                        }
                                    }
                                    .upgrade-vip-btn {
                                        width: 120px;
                                        height: 38px;
                                        border-radius: 19px;
                                        line-height: 38px;
                                        color: #5a3b34;
                                        font-weight: 600;
                                        text-align: center;
                                        background: linear-gradient(170deg, #f8f1e4 24%, #eec7a2);
                                        cursor: pointer;
                                        font-size: 16px;
                                    }
                                }
                                .upgrade-rights {
                                    background-color: #fffaf5;
                                    .rights-title {
                                        padding-top: 8px;
                                        font-size: 15px;
                                        font-weight: 500;
                                        color: #333;
                                        text-align: center;
                                        line-height: 40px;
                                        font-weight: 600;

                                        &::before {
                                            content: " ";
                                            display: inline-block;
                                            width: 60px;
                                            height: 1px;
                                            background: #ffe3d1;
                                            margin-right: 10px;
                                            position: relative;
                                            top: -5px;
                                        }
                                        &::after {
                                            content: " ";
                                            display: inline-block;
                                            width: 60px;
                                            height: 1px;
                                            background: #ffe3d1;
                                            margin-left: 10px;
                                            position: relative;
                                            top: -5px;
                                        }
                                    }
                                    .rights-list {
                                        display: flex;
                                        flex-wrap: wrap;
                                        justify-content: space-around;
                                        padding: 0 10px 10px;
                                        .rights-item {
                                            width: 60px;
                                            padding: 8px 5px;
                                            text-align: center;
                                            display: flex;
                                            flex-direction: column;
                                            justify-content: center;
                                            align-items: center;
                                            font-size: 13px;
                                            color: #333;
                                            line-height: 24px;
                                            cursor: pointer;
                                            img {
                                                width: 50px;
                                                height: 50px;
                                                border-radius: 25px;
                                            }
                                        }
                                    }
                                }
                            }
                            .settings-and-services {
                                padding: 10px 26px 20px 30px;
                                margin-top: 10px;
                                font-size: 14px;
                                color: #333;
                                background-color: #fff;
                                .settings-title {
                                    font-size: 14px;
                                    line-height: 20px;
                                    text-align: left;
                                    margin-bottom: 5px;
                                    font-weight: 600;
                                }
                                .setting-list {
                                    .setting-item {
                                        height: 40px;
                                        line-height: 40px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: space-between;
                                        border-bottom: 1px solid #ececec;
                                        cursor: pointer;
                                        .item-name {
                                            display: flex;
                                            align-items: center;
                                            img {
                                                width: 20px;
                                                height: 20px;
                                                padding-right: 10px;
                                            }
                                        }
                                        .arrow {
                                            color: #b7b7b7;
                                        }
                                    }
                                }
                            }
                        }
                        &.is-third-part {
                            width: 260px;
                            .user-info-container {
                                height: 120px;
                                box-sizing: border-box;
                                border: 1px solid var(--border-color);
                                background-color: var(--white);
                                border-radius: 2px;
                                box-shadow: 0px 2px 2px 0px rgba(173, 187, 200, 0.19), 0px 4px 4px 0px rgba(64, 97, 121, 0.05),
                                    0px 8px 8px 0px rgba(234, 239, 243, 0.05), 0px 16px 16px 0px rgba(215, 217, 219, 0.05),
                                    0px 32px 32px 0px rgba(234, 239, 243, 0.3);
                                display: flex;
                                flex-direction: column;
                                align-items: stretch;
                                padding: 10px 16px 10px 20px;
                                .user-info-content {
                                    display: flex;
                                    align-items: flex-start;
                                    justify-content: flex-start;
                                    padding: 0;
                                    .head-img-bigger {
                                        width: 48px;
                                        height: 48px;
                                        border-radius: 100%;
                                    }

                                    .infos {
                                        margin-left: 20px;
                                        display: flex;
                                        flex-direction: column;

                                        .user-name {
                                            color: var(--font-color);
                                            font-size: var(--font-size);
                                            line-height: var(--line-height);
                                            margin-top: 6px;
                                            margin-bottom: 4px;
                                            // display: -webkit-box;
                                            // -webkit-box-orient: vertical;
                                            // -webkit-line-clamp: 2;
                                            // overflow: hidden;
                                            // text-overflow: ellipsis;
                                            display: block;
                                            width: 150px;
                                            white-space: nowrap;
                                            overflow: hidden;
                                            text-overflow: ellipsis;
                                        }
                                        .btns {
                                            display: flex;

                                            .btn {
                                                color: var(--font-color);
                                                font-size: var(--h5);
                                                line-height: 17px;
                                                cursor: pointer;

                                                & + .btn {
                                                    margin-left: 18px;
                                                }

                                                &:hover {
                                                    color: var(--main-color);
                                                    text-decoration: underline;
                                                }

                                                &:active {
                                                    color: var(--dark-main-color);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .aa-user-info-container {
                            background: white;
                            width: 120px;
                            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
                            border-radius: 2px;
                            position: absolute;
                            top: 15px;
                            right: -20px;

                            .btn {
                                height: 44px;
                                line-height: 44px;
                                text-align: center;
                                font-size: 14px;
                                font-weight: 400;
                                color: #0d0d0d;
                                cursor: pointer;

                                &:hover {
                                    background: rgba(80, 89, 233, 0.1);
                                }
                            }
                        }
                    }

                    &.base {
                        &::after {
                            content: " ";
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            background-image: url("@/assets/App/xia.png");
                            background-repeat: no-repeat;
                            background-size: 100%;
                            margin-left: 8px;
                        }

                        &:hover {
                            .user-info-container-box {
                                display: block;
                            }
                        }
                    }

                    .wxwork-username {
                        color: var(--font-color);
                        font-size: var(--font-size);
                        line-height: var(--line-height);
                        margin-left: 4px;
                        display: inline-block;
                    }
                }
                .pending-payment-order-poppver {
                        width: 210px;
                        height: 36px;
                        color: #fff;
                        font-weight: bold;
                        background: linear-gradient(170deg, #eea61f 0%, #fe4502 100%);
                        position: absolute;
                        top: 44px;
                        z-index: 9;
                        right: 36px;
                        font-size: 16px;
                        line-height: 36px;
                        border-radius: 5px;
                        text-align: center;
                        cursor: pointer;
                        &::before {
                            content: "";
                            position: absolute;
                            top: -14px;
                            right: 18px;
                            border: 8px solid transparent;
                            border-bottom-color: #F47F13;
                        }
                        & .el-icon {
                            position: relative;
                            top: 2px;
                            right: -10px;
                            width: 20px;
                        }
                    }
            }
        }
        .ad-container {
            height: 40px;
            position: relative;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            .el-icon {
                color: var(--white);
                font-size: 20px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 10px;
            }
        }
        .center-container {
            display: flex;
            justify-content: space-between;
            // width: 100%;
            background-color: #fff;
            margin: 5px 4px 0;
            align-items: center;

            .right-space {
                width: 7%;
                border-left: 1px solid #e6e6e6;
                height: 36px;
                align-items: center;
                display: flex;
                .img {
                    width: 17px;
                    height: 17px;
                }

                .down-up-btn {
                    width: 17px;
                    height: 36px;
                    padding-top: 19px;
                    margin: 0 7px;
                    cursor: pointer;
                }
                .qp-tcqp-btn {
                    display: flex;
                    height: 36px;
                    align-items: center;
                    cursor: pointer;
                    .qp-tcqp-font {
                        height: 17px;
                        font-size: 12px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        line-height: 17px;
                        margin-left: 2px;
                    }
                    .tcqp-font {
                        width: 48px;
                        color: #44b449;
                    }
                    .qp-font {
                        width: 24px;
                        color: #666;
                    }
                }
            }
        }
        .tabs-container {
            height: 36px;
            display: flex;
            flex-direction: row;
            align-items: center;
            margin: 5px 4px 0;
            background-color: var(--white);
            padding-left: 24px;
            padding-right: 24px;
            position: relative;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;

            .tab-left-btn,
            .tab-right-btn {
                height: 20px;
                width: 20px;
                background-size: 100%;
                background-repeat: no-repeat;
                cursor: pointer;
                flex-shrink: 0;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }
            .tab-left-btn {
                left: 0;
            }
            .tab-right-btn {
                right: 0;
            }

            .tab-left-btn {
                margin-right: 4px;
                background-image: url("@/assets/Menu/left.png");

                &:hover {
                    background-image: url("@/assets/Menu/left-hover.png");
                }
            }

            .tab-right-btn {
                margin-left: 4px;
                background-image: url("@/assets/Menu/right.png");

                &:hover {
                    background-image: url("@/assets/Menu/right-hover.png");
                }
            }

            .more-btn {
                height: 20px;
                width: 20px;
                background-size: 100%;
                background-repeat: no-repeat;
                background-position: center;
                cursor: pointer;
                flex-shrink: 0;
                margin-left: 4px;
                background-image: url("@/assets/Menu/more.png");

                &:hover {
                    background-image: url("@/assets/Menu/more-hover.png");
                }
            }

            .tabs-content {
                display: flex;
                align-items: center;
                height: 36px;

                .tab-content {
                    display: flex;
                    align-items: center;
                    height: 28px;
                    background-color: #f1f3f9;
                    padding: 0 24px;
                    margin-left: 4px;
                    margin-right: 4px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #666666;
                    line-height: 24px;
                    cursor: pointer;
                    border-radius: 1px;
                    flex-shrink: 0;
                    &.moving {
                        transition: all 0.5s ease;
                        background-color: transparent !important;
                        color: transparent !important;
                        border: 1px dashed #f1f3f9;
                        .close-btn {
                            visibility: hidden !important;
                        }
                        .refresh-btn {
                            visibility: hidden !important;
                        }
                    }

                    .close-btn {
                        display: none;
                        background-image: url("@/assets/Menu/close-btn.png");
                        background-repeat: no-repeat;
                        background-size: 100%;
                        height: 17px;
                        width: 17px;
                        cursor: pointer;
                        margin-left: 7px;
                    }

                    .refresh-btn {
                        display: none;
                        background-image: url("@/assets/Menu/tab-refresh-btn.png");
                        background-repeat: no-repeat;
                        background-size: 100%;
                        height: 20px;
                        width: 20px;
                        cursor: pointer;
                        margin-left: 4px;
                    }

                    &.can-close {
                        padding: 0 10px 0 16px;

                        .close-btn {
                            display: block;
                        }
                    }

                    &.alive {
                        background-color: var(--main-color);
                        color: var(--white);

                        .close-btn {
                            background-image: url("@/assets/Menu/close-btn-alive.png");
                        }

                        &.can-refresh {
                            padding: 0 10px 0 16px;

                            .refresh-btn {
                                display: block;
                            }

                            .close-btn {
                                margin-left: 4px;
                            }
                        }

                        &.can-close {
                            &.can-refresh {
                                padding-right: 8px;
                            }
                        }
                    }

                    & + .tab-content {
                        margin-left: 0;
                    }
                }
            }

            :deep(.el-scrollbar__bar) {
                display: none !important;
            }
        }

        .router-container {
            height: 0;
            flex: 1;
            overflow: auto;
            overflow-x: hidden;
            margin: var(--router-container-margin);
            background-color: var(--white);
            box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
            border-radius: 4px;
            position: relative;

            :deep(.el-overlay) {
                left: calc(140px + var(--router-container-margin));
                top: var(--top-value);
                right: var(--router-container-margin);
                bottom: var(--router-container-margin);
                height: auto;

                .el-overlay-dialog {
                    left: calc(140px + var(--router-container-margin));
                    top: var(--top-value);
                    right: var(--router-container-margin);
                    bottom: var(--router-container-margin);
                }
            }

            &.hide-menu {
                margin: 0;

                :deep(.el-overlay) {
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    position: fixed;

                    .el-overlay-dialog {
                        left: 0;
                        top: 0;
                        right: 0;
                        bottom: 0;
                    }
                }
            }

            &.in-lemone-client {
                :deep(.el-overlay) {
                    left: calc(140px + var(--router-container-margin));
                    top: calc(41px + var(--router-container-margin));
                    right: var(--router-container-margin);
                    bottom: var(--router-container-margin);

                    .el-overlay-dialog {
                        left: calc(140px + var(--router-container-margin));
                        top: calc(41px + var(--router-container-margin));
                        right: var(--router-container-margin);
                        bottom: var(--router-container-margin);
                    }
                }
            }
        }
    }
}

.aside {
    position: fixed;
    right: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 3000;

    .item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        position: relative;
        margin-top: 4px;
        width: 66px;
        height: 72px;
        box-sizing: border-box;
        border: 1px solid #ebecf9;
        box-shadow: 0px 2px 6px 0px rgba(0, 2, 33, 0.04);
        border-radius: 6px;
        background-color: var(--white);
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;

        & > img.icon {
            width: 32px;
            height: 32px;
            margin-top: 8px;
            margin-bottom: 4px;
        }

        .txt {
            font-size: 11px;
            color: var(--font-color);
            line-height: 18px;
        }

        .more {
            position: absolute;
            opacity: 0;
            visibility: hidden;
            -webkit-transition: all 0.2s ease-in;
            -moz-transition: all 0.2s ease-in;
            -o-transition: all 0.2s ease-in;
            transition: all 0.2s ease-in;
            top: 50%;
            right: 110%;
            -webkit-transform: translate(0, -50%);
            -moz-transform: translate(0, -50%);
            -o-transform: translate(0, -50%);
            transform: translate(0, -50%);
            display: flex;
            justify-content: center;
        }

        &:hover {
            .more {
                opacity: 1;
                visibility: visible;
            }
        }

        &.hide-aside {
            flex-direction: row;
            background-color: var(--main-color);
            height: 35px;
            border-radius: 0;
            overflow: hidden;
            border: none;
            box-shadow: none;
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
            align-items: center;
            justify-content: center;

            > img {
                margin-left: 4px;
                height: 15px;
                width: 15px;
            }

            .txt {
                color: var(--white);
            }
        }
    }
}

.tab-right-menu {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    width: 132px;
    padding: 10px 0;
    z-index: 9999;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.85);
    box-shadow: 2px 4px 15px 0px rgba(17, 31, 65, 0.1);
    border: 1px solid #f2f2f2;
    backdrop-filter: blur(6px);

    .menu-btn {
        padding: 9px 0 8px 15px;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 400;
        color: var(--font-color);
        line-height: 19px;
        cursor: pointer;

        &::before {
            content: " ";
            height: 17px;
            width: 17px;
            background-size: 100%;
            background-repeat: no-repeat;
            margin-right: 8px;
        }

        &:hover {
            color: var(--main-color);
        }

        &.close-all {
            &::before {
                background-image: url("@/assets/Menu/right-menu-close-all-icon.png");
            }

            &:hover {
                &::before {
                    background-image: url("@/assets/Menu/right-menu-close-all-hover-icon.png");
                }
            }
        }

        &.close-others {
            &::before {
                background-image: url("@/assets/Menu/right-menu-close-others-icon.png");
            }

            &:hover {
                &::before {
                    background-image: url("@/assets/Menu/right-menu-close-others-hover-icon.png");
                }
            }
        }

        &.close-current {
            &::before {
                background-image: url("@/assets/Menu/right-menu-close-current-icon.png");
            }

            &:hover {
                &::before {
                    background-image: url("@/assets/Menu/right-menu-close-current-hover-icon.png");
                }
            }
        }

        &.refresh-current {
            &::before {
                background-image: url("@/assets/Menu/right-menu-refresh-current-icon.png");
            }

            &:hover {
                &::before {
                    background-image: url("@/assets/Menu/right-menu-refresh-current-hover-icon.png");
                }
            }
        }
    }
}

.tab-refresh-btn-tip {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    height: 45px;
    width: 186px;
    padding: 10px 0 0 12px;
    background: rgba(0, 0, 0, 0.7);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    box-sizing: border-box;
    font-size: 14px;
    line-height: 22px;
    color: var(--white);
    font-weight: 500;
    text-align: left;

    &::before {
        content: " ";
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 6px solid rgba(0, 0, 0, 0.7);
        position: absolute;
        top: -6px;
        left: 12px;
    }
}

body[erp] {
    .main-container {
        min-width: 1420px;
    }
}
</style>
