import { appendAACompanyId } from "@/util/url";
import { ElConfirm } from "./confirm";
import { isInWxWork } from "./wxwork";
import { ElNotify } from "./notify";
import { tryShowPayDialog } from "./proPayDialog";
import { request, type IResponseModel } from "./service";
import { globalWindowOpen, globalWindowOpenPage, setTopLocationhref } from "./url";
import { getCookie } from "./cookie";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { useTrialStatusStoreHook } from "@/store/modules/trialStatus";
import { getGlobalToken, getServiceID } from "./baseInfo";
import { showExpiredDialog, showExpiredToBuyDialog } from "@/util/showExpiredDialog";
import dayjs from "dayjs";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

export const tryGoToPro = () => {
    if (window.isAccountingAgent) {
        accountingAgentVersionChange("专业版");
    } else {
        const url =
            window.eHost +
            "/wb/" +
            (isInWxWork() ? "wecom/" : "") +
            "redirect/info?appId=1060&virtualAppId=1062&productType=" +
            window.productType;
        request({
            url: url,
            method: "get",
        })
            .then((res: any) => {
                if (res.statusCode) {
                    if (res.data.serviceId == 0) {
                        if (isInWxWork()) {
                            request({
                                url:
                                    window.eHost +
                                    "/wb/" +
                                    (isInWxWork() ? "wecom/" : "") +
                                    "plan/subscription?productType=" +
                                    window.productType,
                                method: "get",
                            }).then((res: any) => {
                                if (res.statusCode) {
                                    ElConfirm("请联系管理员添加账套权限", true);
                                } else {
                                    tryShowPayDialog(1, "", "开通专业版，立即畅享发票、资金、关联进销存、工资、资产等多项专属功能");
                                }
                            });
                        } else {
                            tryShowPayDialog(1, "", "开通专业版，立即畅享发票、资金、关联进销存、工资、资产等多项专属功能");
                        }
                    } else {
                        setTopLocationhref(res.data.url + (res.data.url.includes('?') ? '&' : "?")+ "serviceID=" + res.data.serviceId);
                    }
                } else {
                    tryShowPayDialog(1, "", "开通专业版，立即畅享发票、资金、关联进销存、工资、资产等多项专属功能");
                }
            })
            .catch(() => {
                ElNotify({ message: "出现错误，请刷新页面重试或联系管理员", type: "warning" });
            });
    }
};

export const goToFree = () => {
    if (window.isAccountingAgent) {
        accountingAgentVersionChange("免费版");
    } else {
        setTopLocationhref(window.jFreeHost + "/App/Default.aspx");
    }
};

function accountingAgentVersionChange(versionTo: string) {
    request({
        url: "/api/AccountingAgent/GetExChangeAppasid?ran=" + Math.random(),
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                if (res.data) {
                    if (window.isProSystem) {
                        if(window.isBossSystem){
                            setTopLocationhref(appendAACompanyId(window.jAABossH5Host + "/Default/Default?appasid=" + res.data));
                        }else{
                            setTopLocationhref(appendAACompanyId(window.jAAH5Host + "/Default/Default?appasid=" + res.data));
                        }
                    } else {
                        if(window.isBossSystem){
                            setTopLocationhref(appendAACompanyId(window.jAAProBossH5Host + "/Default/Default?appasid=" + res.data));
                        }else{
                            setTopLocationhref(appendAACompanyId(window.jAAProH5Host + "/Default/Default?appasid=" + res.data));
                        }
                    }
                } else {
                    ElNotify({ message: "当前未存在" + versionTo + "账套；切换失败", type: "warning" });
                }
            } else {
                ElNotify({ message: "出现错误，请刷新页面重试或联系管理员", type: "warning" });
            }
        })
        .catch(() => {
            ElNotify({ message: "出现错误，请刷新页面重试或联系管理员", type: "warning" });
        });
}

export const getServiceId = () => {
    return window.isErp ? getCookie("ServiceId") : getServiceID() || getCookie("service-id");
};

export const getHasProAccountSet = async () => {
    let result = false;
    if (!window.isProSystem && !window.isErp) {
        await request({
            url:
                window.eHost +
                "/wb/" +
                (isInWxWork() ? "wecom/" : "") +
                "redirect/info?appId=1060&virtualAppId=1062&type=trial&productType=" +
                window.productType,
            method: "get",
        }).then((res: any) => {
            if (res.statusCode) {
                if (res.data.serviceId != 0) {
                    result = true;
                }
            }
        });
    }
    return result;
};

export const buyPro = () => {
    if (window.isAccountingAgent) {
        globalWindowOpen(window.aaHost + "/#/valueAddedServices");
    } else {
        const accountSet = useAccountSetStoreHook().accountSet;
        const trialStatusStore = useTrialStatusStoreHook();
        const hash =
            "/upgrade/acc" +
            ((accountSet == null || accountSet.asId == 0)
                ? ""
                : "?remainingDays=" +
                  (trialStatusStore.remainingDays && trialStatusStore.isTrial && !trialStatusStore.isExpired
                      ? trialStatusStore.remainingDays
                      : -1));
        globalWindowOpenPage("/Pro/PayDialog?appasid=" + getGlobalToken() + "&hash=" + hash, "升级专业版");
    }
};

export const gotoBuy = () => {
    globalWindowOpen(window.epHost + "/#/acc/tab?Id=" + getServiceId());
};
export enum ExpiredToBuyDialogEnum {
    normal = "1",
    generateVoucher = "2",
    relateVoucher = "3",
    invoiceRiskAnalysis = "4",
    taxDeclare = "5",
    ERecordSpaceNotEnough = "6",
    ERecordSpaceNone = "7",
    ERecordSpaceExpand = "8",
    ERecordSpaceBackupNotEnough = "9",
    notAdminEnter = "10",
    permissionEdit = "11",
    addFirstLevelSubject = "12",
    modifySubjectCodeLevel = "13",
    ERecordSpaceOverflow = "14",
    ERecordSpaceBackupOverflow = "15",
}
const message = {
    [ExpiredToBuyDialogEnum.normal]: "建议您升级至专业版，享受更多专业版功能权益！",
    [ExpiredToBuyDialogEnum.generateVoucher]: "生成凭证失败！升级专业版，立即解锁一键生成凭证功能",
    [ExpiredToBuyDialogEnum.relateVoucher]: "关联凭证失败！升级专业版，立即解锁关联凭证功能",
    [ExpiredToBuyDialogEnum.invoiceRiskAnalysis]: "发票风险分析报告生成失败！升级至专业版，立即解锁发票风险分析功能",
    [ExpiredToBuyDialogEnum.taxDeclare]: "申报失败！升级专业版，立即解锁个税申报功能",
    [ExpiredToBuyDialogEnum.ERecordSpaceNotEnough]: "会计电子档案剩余空间已不足20%！建议您升级至专业版，立即获取10G超大会计电子档案空间",
    [ExpiredToBuyDialogEnum.ERecordSpaceNone]: "空间不足，无法上传！请升级至专业版，立即获取10G超大会计电子档案空间",
    [ExpiredToBuyDialogEnum.ERecordSpaceExpand]: "升级至专业版，立即获取10G超大会计电子档案空间",
    [ExpiredToBuyDialogEnum.notAdminEnter]: "免费版仅限账套管理员一人使用，建议您升级至专业版，被授权的成员才能进入本账套",
    [ExpiredToBuyDialogEnum.permissionEdit]: "升级至专业版，立即解锁多用户协同",
    [ExpiredToBuyDialogEnum.addFirstLevelSubject]: "升级至专业版，立即解锁小企业会计准则新增一级科目功能。",
    [ExpiredToBuyDialogEnum.modifySubjectCodeLevel]: "科目级次调整失败！请升级至专业版，立即解锁科目级次支持10级调整功能。",
    [ExpiredToBuyDialogEnum.ERecordSpaceBackupNotEnough]:"会计电子档案剩余空间已不足20%，将影响您备份恢复！建议您升级至专业版，立即获取10G超大会计电子档案空间",
    [ExpiredToBuyDialogEnum.ERecordSpaceOverflow]: "会计电子档案空间不足！建议您升级至专业版，立即获取10G超大会计电子档案空间",
    [ExpiredToBuyDialogEnum.ERecordSpaceBackupOverflow]: "会计电子档案空间不足，将影响您备份恢复！建议您升级至专业版，立即获取10G超大会计电子档案空间",
};
/**
 * @param {msg} 提示信息
 * @return {}Object
 * @param {model} -模块名称 用于存储
 * @param {frequency} -存储频率
 * @param {showPay} -是否显示支付弹窗,传false则不显示
 * @param {needToStore} -是否需要存储，需要时传true
 * @param {needExpired} -是否需要判断过期，传false则不判断过期
 * @param {trialExpiredFn} -过期后的回调函数
 */
export const handleTrialExpired = (
    obj: { msg: ExpiredToBuyDialogEnum; model?: string; frequency?: string; showPay?: boolean; needToStore?: boolean; needExpired?: boolean },
    trialExpiredFn?: Function
) => {
    const isThirdPart = useThirdPartInfoStoreHook().isThirdPart;
    return new Promise<boolean>((resolve) => {
        const trialStatusStore = useTrialStatusStoreHook();
        if (trialStatusStore.isTrial && (trialStatusStore.isExpired || obj.needExpired === false) ) {
            if( isThirdPart ){
                resolve(false);
                return;
            }
            // 代账版记账弹旧版升级弹框
            const frequencyModel: { [key: string]: string } = {
                month: "YYYY-MM",
                day: "YYYY-MM-DD",
            };
            const storageKey = useAccountSetStoreHook().userInfo?.userSn + "-" + getGlobalToken() + "-trialExpired-" + obj.model;
            const storageValue = dayjs().format(frequencyModel[obj.frequency || "month"]);
            const storageIsExpired =
                localStorage.getItem(storageKey) !==
                storageValue;
            if (obj.showPay !== false && (obj.needToStore === undefined || (obj.needToStore && storageIsExpired))) {
                if (window.isAccountingAgent || isInWxWork()) {
                    if (obj.needExpired === false) {
                        tryShowPayDialog(1, "", message[obj.msg], ['6','7','8','9'].includes(obj.msg) ? "会计电子档案" : "");
                    } else {
                        showExpiredDialog();
                    }
                } else {
                    showExpiredToBuyDialog(obj.msg);
                }
                trialExpiredFn && trialExpiredFn();
                if (obj.needToStore) {
                    localStorage.setItem(storageKey, storageValue);
                }
                resolve(true);
            } else {
                resolve(false);
            }
        } else {
            resolve(false);
        }
    });
};

export enum ExpiredCheckModuleEnum {
    //资金
    Cashier = 100,
    // 发票
    Invoice = 200,
    //资产
    Assets = 300,
    //工资
    Salary = 400,
    //会计电子档案
    Disk = 500,
    //备份恢复
    BackUp = 600,
    //期末结转
    Checkout = 700,
}
export const handleExpiredCheckData = (module: ExpiredCheckModuleEnum) => {
    const isThirdPart = useThirdPartInfoStoreHook().isThirdPart;
    if( isThirdPart ){
        return;
    }
    const trialStatusStore = useTrialStatusStoreHook();
    if (trialStatusStore.isTrial && trialStatusStore.isExpired) {
        request({ url: "/api/PaidConversion/CheckIsPromptUpgradeVip", method: "get", params: { module } }).then(
            (res: IResponseModel<boolean>) => {
                handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, showPay: res.data });
            }
        );
    }
};
