<!-- 增值税月报 -->
<template>
  <div class="tab-content">
    <div class="tab-content-upper">
      <div class="upper-title"><span>本期应补（退）税费额（元）</span></div>
      <div class="upper-body">
        <div
          v-for="item in addTaxAmountList"
          :key="item.text">
          <img
            :src="item.icon"
            alt="" />
          <span>{{ item.text }}</span>
          <div class="tax-amount">¥{{ taxFixedInfo[item.field] }}</div>
        </div>
      </div>
    </div>
    <div class="tab-content-down-title"><span>要素信息填写（元）</span></div>
    <el-form
      :model="factorInfo"
      label-suffix="："
      label-position="top"
      style="overflow: hidden">
      <el-row :gutter="48">
        <el-col :span="6">
          <el-form-item label="销售额">
            <el-input
              class="edit"
              v-model="factorInfo.sale"
              :disabled="isShow"
              @click="inputClick('销售收入', salesRevenueColumns, getSalesVolume)"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="进项税额">
            <el-input
              class="edit"
              v-model="factorInfo.purchaseTax"
              :disabled="isShow"
              @click="inputClick('进项税额填报', inputTaxColumns, getInputTaxData)"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="税额抵減-本期实际抵减额合计">
            <el-input
              class="edit"
              v-model="factorInfo.taxCredit"
              :disabled="isShow"
              @click="inputClick('抵减税额填报', [], getTaxDeduction)"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="48">
        <el-col :span="6">
          <el-form-item label="销项税额">
            <el-input
              v-model="factorInfo.saleTax"
              disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="本期进项税额转出额">
            <el-input
              class="edit"
              v-model="factorInfo.transferOutTax"
              :disabled="isShow"
              @click="inputClick('进项税额转出额填报', taxTransferAmountColumns, getTaxTransferAmount)"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    class="custom-confirm dialogDrag"
    modal-class="modal-class"
    :width="800">
    <div
      class="dialog-body"
      v-dialogDrag>
      <div
        class="prompt"
        v-if="dialogTitle === '销售收入'">
        <el-icon><WarningFilled /></el-icon>
        <span>已自动为您归集发票数据并预填，若还有未开票收入，请在下方继续补充，或返回填写申报表。</span>
      </div>
      <LMTable
        v-if="dialogTitle !== '抵减税额填报'"
        row-key="id"
        :columns="tableColumns"
        :data="tableData"
        :span-method="dialogTitle === '销售收入' ? spanMethod : () => {}">
        <template #input="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              <checkable-input
                v-if="row.readonly.split(',')[slotColumn.columnIndex] == 0"
                v-model="row[slotColumn.prop]"
                :check-type="row[slotColumn.checkTypeField]"
                :row-data="row"
                :check-type-field="slotColumn.checkTypeField"></checkable-input>
              <span v-if="row.readonly.split(',')[slotColumn.columnIndex] == 2">--</span>
              <Popover
                v-else-if="row.note"
                placement="right"
                :content="row.note">
                <template #trigger>
                  <div class="equal-icon" />
                  <span style="float: right; line-height: 12px">{{ row[slotColumn.prop] }}</span>
                </template>
              </Popover>
            </template>
          </el-table-column>
        </template>
      </LMTable>
      <div
        class="native-table"
        v-else>
        <table
          cellpadding="10"
          cellspacing="0">
          <thead>
            <tr>
              <th>建筑服务预征缴纳税款</th>
              <th>金额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData[0]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in taxDeductionColumns"
                :key="innerIndex">
                <span v-if="!innerIndex || parseInt(item.readonly)">
                  {{ item[innerItem] }}
                </span>
                <checkable-input
                  v-else
                  v-model="item[innerItem]"></checkable-input>
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th>销售不动产预征缴纳税款</th>
              <th>金额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData[1]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in taxDeductionColumns"
                :key="innerIndex">
                <span v-if="!innerIndex || parseInt(item.readonly)">
                  {{ item[innerItem] }}
                </span>
                <checkable-input
                  v-else
                  v-model="item[innerItem]"></checkable-input>
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th>出租不动产预征缴纳税款</th>
              <th>金额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData[2]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in taxDeductionColumns"
                :key="innerIndex">
                <span v-if="!innerIndex || parseInt(item.readonly)">
                  {{ item[innerItem] }}
                </span>
                <checkable-input
                  v-else
                  v-model="item[innerItem]"></checkable-input>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <template #footer>
      <a
        class="button mr-10"
        @click="visible = false">
        取消
      </a>
      <a
        class="button solid-button"
        type="primary"
        @click="handleSubmit">
        确定
      </a>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  import { getAddTaxMonthData, saveAddTaxMonthData } from "@/api/taxDeclaration"
  // import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  // import { storeToRefs } from "pinia"

  const props = withDefaults(defineProps<{ currentTaxInfo: { [key: string]: any } }>(), {
    currentTaxInfo: () => ({}),
  })

  const isShow = computed(() => {
    return props.currentTaxInfo.status === 20
  })

  // const { currentPeriodId } = storeToRefs(useTaxPeriodStore())

  // 增值税月报快速填写tab内容
  const addTaxAmountList = [
    { icon: new URL("@/assets/TaxDeclaration/total.png", import.meta.url).href, text: "合计", field: "totalTax" },
    { icon: new URL("@/assets/TaxDeclaration/tax.png", import.meta.url).href, text: "增值税", field: "addedTax" },
    {
      icon: new URL("@/assets/TaxDeclaration/house.png", import.meta.url).href,
      text: "城市维护建设税",
      field: "ctyBuildTax",
    },
    {
      icon: new URL("@/assets/TaxDeclaration/book-green.png", import.meta.url).href,
      text: "教育费附加",
      field: "eduAddedTax",
    },
    {
      icon: new URL("@/assets/TaxDeclaration/add.png", import.meta.url).href,
      text: "地方教育附加",
      field: "localEduAddedTax",
    },
  ]
  const taxFixedInfo = ref<{ [key: string]: any }>({})
  const factorInfo = ref<{ [key: string]: any }>({})
  // 快速填写页面数据
  function getQuicklyFillInData() {
    getAddTaxMonthData("/fastInit").then((res: any) => {
      if (res.state === 1000) {
        taxFixedInfo.value = res.data.taxFixedInfo
        factorInfo.value = res.data.factorInfo
      }
    })
  }
  getQuicklyFillInData()

  let dialogTitle = ref<string>("")
  let tableColumns = ref<Array<{ [key: string]: any }>>([])
  let tableData = ref<Array<{ [key: string]: any }>>([])
  let visible = ref<boolean>(false)

  // 销售收入表头
  const salesRevenueColumns = [
    { prop: "title", label: "类型" },
    { prop: "thdTitle", label: "项目" },
    { prop: "notIssuedInvoiceSalesAmt", label: "销售额", slot: "input", align: "right", columnIndex: 0 },
    { prop: "notIssuedInvoiceTaxAmt", label: "销项税额", slot: "input", align: "right", columnIndex: 1 },
  ]
  // 销售收入弹框数据
  async function getSalesVolume() {
    const res = await getAddTaxMonthData("/fastSale")
    if (res.state === 1000) {
      tableData.value = res.data.statementDatas[0]
    }
  }

  // 进项税额表头
  const inputTaxColumns = [
    { prop: "title", label: "项目" },
    { prop: "count", label: "份数", slot: "input", align: "right", columnIndex: 0 },
    { prop: "amount", label: "金额", slot: "input", align: "right", columnIndex: 1 },
    { prop: "taxAmt", label: "税额", slot: "input", align: "right", columnIndex: 2 },
  ]
  async function getInputTaxData() {
    const res = await getAddTaxMonthData("/fastPurchase")
    if (res.state === 1000) {
      tableData.value = res.data.statementDatas[0]
    }
  }

  // 抵减税额
  const taxDeductionColumns = ["title", "amount"]
  async function getTaxDeduction() {
    const res = await getAddTaxMonthData("/fastTaxCredit")
    if (res.state === 1000) {
      tableData.value = res.data.statementDatas
    }
  }

  // 进项税额转出额
  const taxTransferAmountColumns = [
    { prop: "title", label: "项目" },
    { prop: "taxAmt", label: "税额", slot: "input", align: "right", columnIndex: 0 },
  ]
  async function getTaxTransferAmount() {
    const res = await getAddTaxMonthData("/fastTransferOut")
    if (res.state === 1000) {
      tableData.value = res.data.statementDatas[0]
    }
  }

  async function inputClick(title: string, columns: Array<{ [key: string]: any }>, fun: () => void) {
    if (isShow.value) return

    dialogTitle.value = title
    await fun()
    tableColumns.value = columns
    visible.value = true
  }

  // 根据表格数据合并单元格
  const spanMethod = ({ row, rowIndex, columnIndex }: { [key: string]: any }) => {
    if (columnIndex === 0) {
      // 合并相同类型的单元格
      if (rowIndex > 0 && row.title === tableData.value[rowIndex - 1].title) {
        return [0, 1]
      }
      let rowspan = 1
      for (let i = rowIndex + 1; i < tableData.value.length; i++) {
        if (tableData.value[i].title === row.title) {
          rowspan++
        } else {
          break
        }
      }

      return [rowspan, 1] // 不渲染当前单元格
    }
  }

  const submitMap: { [key: string]: any } = {
    销售收入: "/fastSaleSubmit",
    进项税额填报: "/fastPurchaseSubmit",
    抵减税额填报: "/fastTaxCreditSubmit",
    进项税额转出额填报: "/fastTransferOutSubmit",
  }

  // 确定按钮
  function handleSubmit() {
    const url = submitMap[dialogTitle.value]
    if (url) {
      saveAddTaxMonthData(url, tableData.value).then((res: any) => {
        if (res.state === 1000) {
          visible.value = false
          getQuicklyFillInData()
        }
      })
    }
  }
</script>
<style lang="scss" scoped>
  @use "@/style/TaxDeclaration/index.scss" as *;

  .tab-content {
    font-size: var(--h4);
    color: var(--dark-grey);

    .tab-content-upper {
      padding: 15px 20px;
      border: 1px solid #eaeaea;
      border-radius: 0px 0px 4px 4px;

      .upper-title {
        line-height: 20px;

        &::before {
          @include common-before;
        }

        span {
          margin-left: 4px;
        }
      }
      .upper-body {
        display: flex;
        justify-content: space-between;
        margin: 15px 0 20px 0;
        padding: 15px 16px;
        img {
          width: 20px;
          height: 20px;
          vertical-align: sub;
        }
        span {
          margin-left: 5px;
          font-size: var(--h3);
        }
        .tax-amount {
          margin-top: 11px;
          font-size: 26px;
        }
      }
    }
    .tab-content-down-title {
      margin: 20px 0;
      &::before {
        @include common-before;
      }
      span {
        margin-left: 4px;
      }
    }
    .edit::after {
      content: "";
      width: 14px;
      height: 14px;
      position: absolute;
      right: 7px;
      top: calc(50% - 7px);
      background-image: url("@/assets/TaxDeclaration/edit-pen.png");
      background-size: 100%;
    }
  }
  .dialog-body {
    padding: 32px !important;

    .prompt {
      margin-top: -16px;
      padding-bottom: 16px;
      font-size: var(--h5);
      color: var(--grey);

      .el-icon {
        color: var(--orange);
        font-size: var(--h4);
        vertical-align: text-bottom;
      }
      span {
        margin-left: 2px;
      }
    }

    .native-table table {
      // width: 100%;
      tr {
        th:first-child,
        td:first-child {
          text-align: left;
        }
        th:last-child,
        td:last-child {
          text-align: right;
        }
      }
    }
  }
</style>
