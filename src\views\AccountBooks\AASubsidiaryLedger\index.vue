<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                    <div class="main-content">
                        <div class="title">核算项目明细账</div>
                        <div class="main-top">
                            <div class="main-top-left">
                                <SearchInfoContainer ref="containerRef">
                                    <template v-slot:title>{{ currentPeriodInfo }}</template>
                                    <div class="line-item first-item input">
                                        <div class="line-item-title">会计期间：</div>
                                        <div class="line-item-field">
                                            <DatePicker
                                                v-model:startPid="searchInfo.startMonth"
                                                v-model:endPid="searchInfo.endMonth"
                                                :clearable="false"
                                                :editable="false"
                                                :dateType="'month'"
                                                :value-format="'YYYYMM'"
                                                :label-format="'YYYY年MM月'"
                                                :isPeriodList="true"
                                                @getActPid="getActPid"
                                            />
                                        </div>
                                    </div>
                                    <div class="line-item input">
                                        <div class="line-item-title">辅助项目：</div>
                                        <div class="line-item-field aatype-select">
                                            <SelectWithLotOfData
                                                v-model="auxiliaryItem"
                                                style="width: 298px"
                                                :teleported="false"
                                                :filterable="true"
                                                :clearable="true"
                                                :class="generateId"
                                                :placeholder="currentAuxiliary"
                                                :options="showRealyArray"
                                                :icon-clear-right="33"
                                                @change="handleAuxiliaryChange"
                                                @visible-change="handleVisibleChange"
                                                @input="handleInput"
                                                :remote="realyArray.length > 0"
                                                :filter-method="realyFilterMethod"
                                                :isSuffixIcon="true"
                                            />
                                        </div>
                                    </div>
                                    <div class="line-item input">
                                        <div class="line-item-title">科目：</div>
                                        <div class="line-item-field">
                                            <SubjectPicker
                                                ref="startAsubRef"
                                                @get-asub-id="handleAsubChange"
                                                asubImgRight="14px"
                                                v-model="searchInfo.sbj_id"
                                                :is-by-id="false"
                                                :showDisabled="true"
                                            >
                                            </SubjectPicker>
                                        </div>
                                    </div>
                                    <div class="line-item input">
                                        <div class="line-item-title">科目级别：</div>
                                        <div class="line-item-field">
                                            <el-input-number
                                                v-model="searchInfo.sbj_leval_s"
                                                :min="1"
                                                :max="maxCodelength"
                                                controls-position="right"
                                                style="width: 132px"
                                            ></el-input-number>
                                            <div class="ml-10 mr-10">至</div>
                                            <el-input-number
                                                v-model="searchInfo.sbj_leval_e"
                                                :min="1"
                                                :max="maxCodelength"
                                                controls-position="right"
                                                style="width: 132px"
                                            ></el-input-number>
                                        </div>
                                    </div>
                                    <div class="line-item input" v-show="fcIsShow">
                                        <div class="line-item-title">币别：</div>
                                        <div class="line-item-field fcid-select">
                                            <el-select 
                                                v-model="searchInfo.fcid" 
                                                :teleported="false"
                                                placeholder=" "
                                                :filterable="true"
                                                :filter-method="fcFilterMethod"
                                            >
                                                <el-option :label="item.label" :value="item.id" v-for="item in showfcList" :key="item.id"></el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class="line-item input">
                                        <div class="line-item-title">排序方式：</div>
                                        <div class="line-item-field">
                                            <el-radio-group v-model="searchInfo.sortColumn">
                                                <el-radio label="V_NUM">凭证号排序</el-radio>
                                                <el-radio label="V_DATE">凭证日期排序</el-radio>
                                            </el-radio-group>
                                        </div>
                                    </div>
                                    <div class="line-item single">
                                        <div class="line-item-title">
                                            <el-checkbox v-model="searchInfo.showsbj" label="显示科目"></el-checkbox>
                                        </div>
                                    </div>
                                    <div class="line-item single">
                                        <div class="line-item-title">
                                            <el-checkbox v-model="searchInfo.isBalanceZero" label="余额为0不显示"></el-checkbox>
                                        </div>
                                    </div>
                                    <div class="line-item single">
                                        <div class="line-item-title">
                                            <el-checkbox
                                                v-model="searchInfo.hiddenTotal"
                                                label="无发生额不显示本期合计、本年累计"
                                            ></el-checkbox>
                                        </div>
                                    </div>
                                    <div class="buttons">
                                        <a class="button solid-button" @click="handleSearch">确定</a>
                                        <a class="button" @click="handleClose">取消</a>
                                        <a class="button" @click="handleReset">重置</a>
                                    </div>
                                </SearchInfoContainer>
                                <div class="aatype">
                                    <span class="text ml-10 mr-10">辅助类别：</span>
                                    <Select 
                                        v-model="searchInfo.aa_type" 
                                        :fit-input-width="true" 
                                        :teleported="false" 
                                        @change="aatypeChange"
                                        :filterable="true"
                                        :filter-method="fzTypeFilterMethod"
                                    >
                                        <Option v-for="item in showfzTypeList" :label="item.label" :value="item.id" :key="item.id" />
                                    </Select>
                                </div>
                                <a class="button solid-button ml-10" @click="handleSearch">查询</a>
                                <ErpRefreshButton></ErpRefreshButton>
                            </div>
                            <div class="main-tool-right">
                                <el-checkbox class="mr-10" v-model="searchInfo.quantity" label="显示数量金额" @change="handleSearch"></el-checkbox>
                                <Dropdown class="mr-10 print-down-90" :width="116" btnTxt="打印当前数据" :downlistWidth="116" v-permission="['aasubsidiaryledger-canprint']">
                                    <li @click="handlePrintOpen(0, 0)">直接打印</li>
                                    <li @click="handlePrintOpen(0, 2)">打印设置</li>
                                </Dropdown>
                                <Dropdown class="mr-10 print-down-114" :width="144" btnTxt="打印所有核算明细" :downlistWidth="144" v-permission="['aasubsidiaryledger-canprint']">
                                    <li @click="handlePrintOpen(1, 0)">直接打印</li>
                                    <li @click="handlePrintOpen(1, 2)">打印设置</li>
                                </Dropdown>
                                <Dropdown 
                                    :btnTxt="'导出'" 
                                    class="mr-10"
                                    :downlistWidth="102" 
                                    v-permission="['aasubsidiaryledger-canexport']"
                                >
                                    <li @click="handleExport(0)">当前查询数据</li>
                                    <li @click="showExportDialog">批量导出</li>
                                </Dropdown>
                                <RefreshButton></RefreshButton>
                            </div>
                        </div>
                        <div :class="['main-center', { isErpCenter: isErp }]">
                            <div v-loading="loading" element-loading-text="正在加载数据..." class="main-left">
                                <div class="main-title">
                                    <Tooltip :content="searchInfo.faname" :maxWidth="1470">
                                        <div class="ellipsis">{{ searchInfo.faname}}</div>
                                    </Tooltip>
                                </div>
                                <AccountBooksTable
                                    :scrollbarShow="true"
                                    :data="tableData"
                                    :columns="columns"
                                    :loading="loading"
                                    :page-is-show="true"
                                    :empty-text="emptyText"
                                    :layout="paginationData.layout"
                                    :page-sizes="paginationData.pageSizes"
                                    :page-size="paginationData.pageSize"
                                    :total="paginationData.total"
                                    :currentPage="paginationData.currentPage"
                                    :tooltipOptions="{ effect: 'light', placement: 'bottom' }"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    @refresh="handleRerefresh"
                                    :tableName="setModule"
                                >
                                    <template #vgname>
                                        <el-table-column
                                            label="凭证字号"
                                            :min-width="searchInfo.quantity ? '70px' : '100px'"
                                            align="left"
                                            headerAlign="left"
                                            prop="vgname"
                                            :width="getColumnWidth(setModule, 'vgname')"
                                        >
                                            <template #default="scope">
                                                <span
                                                    v-show="scope.row.vg_name !== ''"
                                                    :class="checkPermission(['voucher']) ? 'link' : 'cursor-default'"
                                                    @click="checkPermission(['voucher']) ? ShowDetail(scope.row) : ''"
                                                >
                                                    {{ scope.row.vg_name.replace(/<[^<>]+>/g, "") }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </AccountBooksTable>
                            </div>
                            <div class="main-right drag-right">
                                <div class="assit-col-line"></div>
                                <div class="drag-line" v-tree-drag="flag" v-show="flag.closeFlag"></div>
                                <div class="tree-click">
                                    <span :class="['tree-img', {'disabled': !flag.closeFlag}]" @click="treeClick(0, flag)"></span>
                                    <span :class="['tree-img expand', {'disabled': !flag.expandFlag}]" @click="treeClick(1, flag)"></span>
                                </div>
                                <div class="main-right-title">快速切换</div>
                                <div class="select-content">
                                    <div class="search-box"></div>
                                    <div class="pd-10">
                                        <input
                                            type="text"
                                            autocomplete="off"
                                            :value="searchVal"
                                            @input="inputHandle"
                                            @focus="inputFocusHandle"
                                            @blur="inputBlurHandle"
                                            @keyup.enter="handleEnter"
                                        />
                                    </div>
                                    <div class="select-info" v-show="searchListShow">
                                        <recycle-scroller
                                            class="virtual-list"
                                            :buffer="1000"
                                            :prerender="100"
                                            style="height: 100%"
                                            :item-size="24"
                                            key-field="id"
                                            :items="searchList"
                                        >
                                            <template v-slot="{ item }">
                                                <Tooltip
                                                    :key="item.id"
                                                    :content="item.text"
                                                    :line-clamp="1"
                                                    :font-size="14"
                                                    :placement="'left'"
                                                    :teleported="true"
                                                    :dynamicWidth="true"
                                                >
                                                    <div class="select-info-inner" @click="searchClickHandle(item)">
                                                        {{ item.text }}
                                                    </div>
                                                </Tooltip>
                                            </template>
                                        </recycle-scroller>
                                        <div class="select-info-inner" v-show="searchList.length === 0" style="text-align: center"></div>
                                    </div>
                                </div>
                                <div class="tree">
                                    <el-scrollbar :always="true" :minSize="80" ref="scrollRef" @scroll="handleScroll">
                                        <el-tree
                                            ref="treeRef"
                                            highlight-current
                                            :data="treeData"
                                            :indent="21"
                                            :node-key="'treeId'"
                                            :default-expand-all="true"
                                            :expand-on-click-node="false"
                                            :empty-text="emptyText"
                                            :props="{ label: 'text', class: userDefinedClassName }"
                                            @node-click="nodeClick"
                                        >
                                        </el-tree>
                                    </el-scrollbar>
                                </div>
                            </div>
                        </div>
                    </div>
            </template>
            <template #add>
                <BooksVoucher
                    ref="booksVoucherRef"
                    :title="'查看凭证'"
                    :changedTimes="changedTimes"
                    @voucher-cancel="handleVoucherCancel"
                    @hasChanged="() => changedTimes++"
                ></BooksVoucher>
            </template>
        </ContentSlider>
        <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        title="核算项目明细账打印"
        allPrint="打印所有核算明细"
        :coverEdit="true"
        :printData="printInfo"
        :dir-disabled="searchInfo.quantity"
        :otherOptions="otherOptions"
        @continuePrint="handlePrintOpen(1, 3)"
        @currentPrint="handlePrintOpen(0, 3)"
        />
        <el-dialog v-model="exportDialogVisible" title="批量导出" center width="550" class="custom-confirm">
            <div class="export-content">
                <div class="export-main">
                    <div class="line-item mt-20 mb-20">
                        <span class="mr-20">导出类别：</span>
                        <Select
                            class="export-type-select"
                            v-model="exportInfo.aa_type"
                            filterable
                            :fitInputWidth="true"
                            :teleported="false"
                            :multiple="true"
                            :style="{ width: '400px' }"
                            :popperClass="'export-ype-dropdown'"
                            @change="onExportTypeChange"
                        >
                            <Option v-for="item in fzTypeList" :label="item.label" :value="item.id" :key="item.id"> </Option>
                        </Select>
                    </div>
                    <div class="line-item mt-20 mb-20">
                        <span class="mr-20">期间：</span>
                        <PeriodPicker
                            v-model:startPid="exportInfo.startPid"
                            v-model:endPid="exportInfo.endPid"
                            :style="{ width: '400px' }"
                        />
                    </div>
                </div>
                <div class="buttons a_nofloat">
                    <a class="button" @click="exportDialogVisible = false">取消</a>
                    <a class="button solid-button ml-20" @click="handleExport(1)">导出</a>
                </div>
            </div>
        </el-dialog>
        <ConfirmWithoutGlobal
            v-model="confirmWithoutGlobalVisible"
            :show-close="false"
            confirmButtonText="编辑原凭证"
            cancelButtonText="进入新页面"
            :cancel-click="reLoadCurrentPage"
        >
            <div style="text-align: left; margin: 0 -30px">
                您之前编辑的凭证还没有保存<br />点击'编辑原凭证'则可继续编辑原凭证<br />点击'进入新页面'则原凭证将不会保存并进入核算项目明细账页面
            </div>
        </ConfirmWithoutGlobal>
    </div>
</template>

<script lang="ts">
export default {
    name: "AASubsidiaryLedger",
};
</script>
<script setup lang="ts">
import { ref, provide, reactive, nextTick, watch, onMounted, onUnmounted, onActivated, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpen } from "@/util/url";
import { componentFinishKey } from "@/symbols";
import { usePagination } from "@/hooks/usePagination";
import { ChangeCulumns, userDefinedClassName } from "./utlis";
import { ElNotify } from "@/util/notify";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type {
    Itree,
    ISearchParams,
    ITableData,
    IResTableData,
    IAssistingAccount,
    ISelectList,
    IAsubCodeLength,
    ICurrency,
    ISearchInfo,
    IAssistingAccountType,
} from "./types";
import Dropdown from "@/components/Dropdown/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import BooksVoucher from "../components/BooksVoucher.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import { getCookie, setCookie } from "@/util/cookie";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import SelectWithLotOfData from "./components/SelectWithLotOfData.vue";
import { SelectWithLotOfDataOptions } from "./components/types";
import { checkPermission } from "@/util/permission";
import { getGlobalLodash } from "@/util/lodash";
import { ElTree } from "element-plus";
import RefreshButton from "@/components/RefreshButton/index.vue";
import ConfirmWithoutGlobal from "@/components/ConfirmWithoutGlobal/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { treeClick } from "@/views/AccountBooks/SubsidiaryLedger/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo, initStartOrEndMonth } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import usePrint from "@/hooks/usePrint";
import { getLocalStorage } from "@/util/localStorageOperate";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "AASubsidiary";
const realyArray = ref<Array<SelectWithLotOfDataOptions>>([]);
const _ = getGlobalLodash()
const periodStore = useAccountPeriodStore();
const isErp = ref(window.isErp);
const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const slots = ["main", "add"];
const currentSlot = ref("main");
const loading = ref(false);
const columns = ref<Array<IColumnProps>>([]);
const currentPeriodInfo = ref("");
// const periodInfo = ref("");
const fcIsShow = ref(false);
const nodeAAE_ID = ref("");
const nodeAAE_PID = ref("0");
let auxiliaryItem = ref("");
const period_s = ref(Number(periodStore.getPeriodRange().start));
const period_e = ref(Number(periodStore.getPeriodRange().end));
const searchInfo = reactive<ISearchInfo>({
    // 会计期间开始
    period_s: Number(periodStore.getPeriodRange().start),
    // 会计期间结束
    period_e: Number(periodStore.getPeriodRange().end),
    // 科目
    sbj_id: "",
    // 科目级别
    sbj_leval_s: 1,
    sbj_leval_e: 4,
    // 排序方式
    sortColumn: "V_NUM",
    // 显示科目
    showsbj: false,
    // 余额为0不显示
    isBalanceZero: false,
    // 无发生额不显示本期合计、本年累计
    hiddenTotal: false,
    // 辅助类别 初始值为空字符串
    aa_type: " ",
    // 显示数量金额
    quantity: false,
    // 币别
    fcid: -1,
    // 辅助项目
    aae_id: "",
    faname: "",
    aae_pid: "0",
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, period_s.value, period_e.value); 
const getActPid = (start: number, end: number) => {
    period_s.value = start;
    period_e.value = end;
}
function updateRouteFateMonth() {
    const result = initStartOrEndMonth(periodData.value, period_s.value, period_e.value);  
    searchInfo.startMonth = result.startMonth;  
    searchInfo.endMonth = result.endMonth;  
}

// searchInfo.sbj_id绑定的是科目编码，currentAsubId是对应科目id
let currentAsubId = ref();
const handleAsubChange = (asubId: number) => {
    currentAsubId.value = asubId;
};

// 页面展示
const fzTypeList = ref<ISelectList[]>([]);
const fcList = ref<ISelectList[]>([]);
const searchPeriodList = ref<ISelectList[]>([]);
const treeData = ref<Itree[]>([]);
const searchList = ref<Itree[]>([]);
const tableData = ref<ITableData[]>([]);

// ref 节点
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const treeRef = ref<InstanceType<typeof ElTree>>();
const booksVoucherRef = ref<InstanceType<typeof BooksVoucher>>();

// 搜索框
const searchVal = ref("");
const searchListShow = ref(false);

let firstSearch = true;
const inputHandleFn = (e: any) => {
    searchListShow.value = true;
    const text = e.target.value.trim();
    nextTick().then(() => {
        searchVal.value = text;
        searchList.value = treeData.value.filter((item: Itree) => item.text.indexOf(text) > -1);
        firstSearch = false;
    });
};

const inputHandle = _.debounce(inputHandleFn, 500);

const inputFocusHandle = () => {
    searchListShow.value = true;
    if (firstSearch) {
        searchList.value = treeData.value;
        firstSearch = false;
    }
};

const inputBlurHandle = () => {
    const timer = setTimeout(() => {
        searchListShow.value = false;
        clearTimeout(timer);
    }, 200);
};

function handleEnter() {
    if (searchList.value.length === 0) return;
    searchClickHandle(searchList.value[0]);
}

const searchClickHandle = (row: Itree) => {
    searchVal.value = row.text;
    nextTick().then(() => {
        treeRef.value?.setCurrentKey(row.treeId);
        inputBlurHandle();
        nodeClick(treeRef.value?.getCurrentNode());
        rightTreeScrollTo(row.treeId);
    });
};
watch(
    auxiliaryItem,
    (v) => {
        searchInfo.aae_id = v;
    },
    { immediate: true }
);

// 切换页面
let isLoading = false;
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    const timer = setTimeout(() => {
        if (isLoading) {
            clearTimeout(timer);
            return;
        }
        // 等于0时页面无数据，防止重复请求
        if (paginationData.currentPage !== 0) {
            loading.value = true;
            const data = treeRef.value?.getCurrentNode();
            const aae_pid =
                data!.attributes.type === "none"
                    ? treeData.value.find((item: Itree) => item.attributes.aaname == data!.attributes.aaname)!.id
                    : "0";
            const aae_id = data!.id;
            const params = {
                ...getBaseParams(),
                aae_id,
                aae_pid,
                sortColumn: searchInfo.sortColumn,
                page: paginationData.currentPage ? paginationData.currentPage : 1,
                rows: paginationData.pageSize,
            };

            request({
                url: "/api/AASubsidiaryLedger",
                params,
            })
                .then((res: IResponseModel<IResTableData>) => {
                    clearTimeout(timer);
                    if (res.state === 1000) {
                        tableData.value = res.data.rows;
                        paginationData.total = res.data.total;
                        if (res.data.total === 0) {
                            paginationData.currentPage = 1;
                        }
                        setInfos();
                        columns.value = ChangeCulumns(searchInfo.quantity, searchInfo.fcid);
                        loading.value = false;
                    }
                })
                .catch(() => {
                    loading.value = false;
                    clearTimeout(timer);
                })
                .finally(() => {
                    // 业财切换页面置顶
                    if (isErp.value) {
                        const mainCenter = document.querySelector(".main-center");
                        mainCenter?.scrollTo(0, 0);
                    }
                    loading.value = false;
                });
        }
    }, 50);
});

const handleSearch = () => {
    if (period_s.value > period_e.value) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return false;
    }
    periodStore.changePeriods(String(period_s.value), String(period_e.value));
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, period_s.value, period_e.value);
    searchVal.value = "";
    searchInfo.aae_id = auxiliaryItem.value;
    columns.value = ChangeCulumns(searchInfo.quantity, searchInfo.fcid);
    if (setParams(searchParams)) {
        if (paginationData.currentPage !== 1) {
            isLoading = true;
            paginationData.currentPage = 1;
            const timer = setTimeout(() => {
                isLoading = false;
                clearTimeout(timer);
            }, 100);
        }
        getTableData();
        handleClose();
    }
};

const handleClose = () => containerRef.value?.handleClose();

const handleReset = () => {
    searchInfo.sbj_id = "";
    searchInfo.aae_id = "";
    searchInfo.sbj_leval_s = 1;
    searchInfo.sbj_leval_e = maxCodelength.value;
    searchInfo.fcid = -1;
    currentAuxiliary.value = "";
    auxiliaryItem.value = "";
    searchInfo.sortColumn = "V_NUM";
    period_s.value = Number(periodStore.getPeriodRange().start);
    period_e.value = Number(periodStore.getPeriodRange().end);
    searchInfo.showsbj = false;
    searchInfo.isBalanceZero = false;
    searchInfo.hiddenTotal = false;
    searchRet.value = [];
};

function getPrintParams(ifpage: number) {
    return {
        ...getBaseParams(),
        aae_id:  ifpage === 1 ? auxiliaryItem.value : nodeAAE_ID.value,
        aae_pid:  ifpage === 1 ? "0" : nodeAAE_PID.value,
        sortColumn: searchInfo.sortColumn,
        fcid: searchInfo.fcid,
        ifpage,
    }
}
const extraInfo = {
    isBodySubjectShowLeaf: false,
    basepage: false,
    continuePrint: false,
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "aaSubsidiaryLedger",
    window.jAccountBooksHost + "/api/AASubsidiaryLedger/Print",
    extraInfo,
    true,
    true,
    printValidator
);

if(!getLocalStorage("aasubsidiaryLedgerPrint")){
    const imageDirection = parseInt(getCookie("ImageDirection") ?? 0);
    const hidePrintDate = getCookie("HidePrintDate") === "true";
    const printBasepage = getCookie("PrintBasepage") === "true";

    printInfo.value.direction = searchInfo.quantity ? 1 : imageDirection;
    printInfo.value.isShowPrintDate = hidePrintDate;
    printInfo.value.basepage = printBasepage;
}
otherOptions.value = [
    { key: "isBodySubjectShowLeaf", label: "表体科目名称只打印末级" },
    { key: "basepage", label: "打印封面" },
    ...otherOptions.value,
    { key: "continuePrint", label: "连续打印" },
];

function printValidator() {
    if (!isParams()) return false;
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可打印！", type: "warning" });
        printDialogVisible.value = false;
        return false;
    }
    return true;
}
const handlePrintOpen = (ifContinue: number, type:  0|2|3) => {
    if (type === 2) {
        handlePrint(type);
    } else {
        handlePrint(type,getPrintParams(ifContinue))
    }
};

//导出
const exportDialogVisible = ref<boolean>(false);
const exportInfo = reactive({
    aa_type: [Number(searchInfo.aa_type)],
    startPid: searchInfo.period_s,
    endPid: searchInfo.period_e,
});

const onExportTypeChange = () => {
    const ipt = document.querySelector(".export-type-select .el-select__input");
    nextTick(() => {
        (ipt as HTMLInputElement).value = "";
    });
};

// 批量导出弹窗
const showExportDialog = () => {
    exportInfo.aa_type = [Number(searchInfo.aa_type)];
    exportInfo.startPid = searchInfo.period_s;
    exportInfo.endPid = searchInfo.period_e;
    exportDialogVisible.value = true;
};

const handleExport = (ifpage: number) => {
    // if (!isParams()) return;
    if (ifpage === 0 && period_s.value > period_e.value) {
        return ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
    }
    if (ifpage === 1 && exportInfo.startPid > exportInfo.endPid) {
        return ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
    }
    if (searchInfo.sbj_leval_s > searchInfo.sbj_leval_e) {
        return ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
    }
    if (ifpage === 0 && paginationData.total === 0) {
        return ElNotify({ message: "没有数据可导出！", type: "warning" });
    }
    if (!exportInfo.aa_type.length) {
        ElNotify({
            message: "亲，请选择导出类别哦",
            type: "warning",
        });
        return false;
    }
    const params = {
        ...getBaseParams(),
        aae_id: ifpage === 1 ? auxiliaryItem.value : nodeAAE_ID.value,
        aae_pid: nodeAAE_PID.value,
        async: !isLemonClient(),
        ifpage,
        ran: Math.random(),
        fcid: searchInfo.fcid,
        aa_type: ifpage === 0 ? searchInfo.aa_type + "" : exportInfo.aa_type.join(","),
        sbj_leval_s: ifpage === 0 ? searchInfo.sbj_leval_s : exportInfo.startPid,
        sbj_leval_e: ifpage === 0 ? searchInfo.sbj_leval_e : exportInfo.endPid,
        sortColumn: searchInfo.sortColumn,
    };
    if (ifpage === 1) {
        params.period_e = exportInfo.endPid;
        params.period_s = exportInfo.startPid;
    }
    globalExport(window.jAccountBooksHost + "/api/AASubsidiaryLedger/Export?" + getUrlSearchParams(params));
};

const carryOverVoucherTime = ref(0)
provide("carryOverVoucherTime", carryOverVoucherTime);
const ShowDetail = (row: ITableData) => {
    carryOverVoucherTime.value = 1500;
    changedTimes.value = 0;
    currentSlot.value = "add";
    booksVoucherRef.value?.initVoucherData(row.p_id, row.v_id);
};

const searchParams: ISearchParams = {
    period_s: 0,
    period_e: 0,
    sbj_id: "",
    sbj_leval_s: 1,
    sbj_leval_e: 4,
    showsbj: 0,
    isBalanceZero: 0,
    hiddenTotal: 0,
    aae_id: 0,
    aae_pid: 0,
    aa_type: 10001,
    fcid: -1,
    direction: 0,
    ifpage: "",
    basepage: 0,
    faname: "",
    quantity: false,
    sortColumn: "V_NUM",
    page: paginationData.currentPage,
    rows: paginationData.pageSize,
};

const setParams = (params: ISearchParams) => {
    if (!isParams()) return;
    params.period_s = searchInfo.period_s;
    params.period_e = searchInfo.period_e;
    params.sbj_id = searchInfo.sbj_id ?? "";
    params.sbj_leval_s = searchInfo.sbj_leval_s;
    params.sbj_leval_e = searchInfo.sbj_leval_e;
    params.showsbj = searchInfo.showsbj ? 1 : 0;
    params.isBalanceZero = searchInfo.isBalanceZero ? 1 : 0;
    params.hiddenTotal = searchInfo.hiddenTotal ? 1 : 0;
    params.aae_id = searchInfo.aae_id;
    params.aae_pid = 0;
    params.aa_type = searchInfo.aa_type + "";
    params.fcid = searchInfo.fcid;
    params.direction = printInfo.value.direction;
    params.basepage = printInfo.value.basepage ? 1 : 0;
    params.faname = searchInfo.faname;
    params.quantity = searchInfo.quantity;
    params.sortColumn = searchInfo.sortColumn;
    params.page = paginationData.currentPage;
    params.rows = paginationData.pageSize;
    params.fcid = searchInfo.fcid;
    params.quantity = searchInfo.quantity ? 1 : 0;
    return true;
};

// 获取币别并判断币别是否展示
const InitCurrencyApi = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcListOptions];
};
// 获取辅助核算类型
const InitPeriodApi1 = () => {
    return request({
        url: "/api/AssistingAccountingType/List",
        method: "get",
    });
};
// 判断是否存在使用了外币核算的科目
const InitPeriodApi2 = () => {
    return request({
        url: "/api/AccountSubject/ExistsFc",
        method: "post",
    });
};
const setInfos = () => {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, period_s.value, period_e.value);
}

const isParams = () => {
    if (period_s.value > period_e.value) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return false;
    }
    if (searchInfo.sbj_leval_s > searchInfo.sbj_leval_e) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    return true;
};

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1)
        InitPeriod().then(() => {
            getTableData(true);
        });
});

// 初始化执行一次 让树的第一个节点选中并获取表格数据
const emptyText = ref(" ");

const formatterTreeData = (data: Itree[], mapTreeData: Itree[]) => {
    const newArr = data.map((item) => {
        if (item.attributes.type === "none") {
            const rootData = mapTreeData.find((mapItem) => mapItem.attributes.aanum === item.attributes.aanum);
            item.parentData = { aae_pid: rootData?.id || "0" };
            item.treeId = item.parentData.aae_pid + "_" + item.id;
        } else {
            item.parentData = { aae_pid: "0" };
            item.treeId = item.parentData.aae_pid + "_" + item.id;
        }
        if (item.children) formatterTreeData(item.children, mapTreeData);
        return item;
    });
    return newArr;
};

const InitPeriod = () => {
    return Promise.all([InitPeriodApi1(), InitPeriodApi2()]).then((res: IResponseModel<any>[]) => {
        InitCurrencyApi()
        fzTypeList.value = res[0].data.map((v: IAssistingAccountType) => {
            return { id: v.aatypeId, label: v.aatypeName };
        });
        fcIsShow.value = res[1].data;
        searchInfo.aa_type = route.query.AA_TYPE ? Number(route.query.AA_TYPE) : fzTypeList.value[0]?.id;
        searchInfo.fcid = -1;
        aatypeChange();
        return Promise.resolve();
    });
};

const getTreeData = () => {
    const params = {
        aa_type: searchInfo.aa_type + "",
        period_s: searchInfo.period_s,
        period_e: searchInfo.period_e,
        sbj_id: searchInfo.sbj_id.split(" ")[0] ?? "",
        fcid: searchInfo.fcid,
        sbj_leval_s: searchInfo.sbj_leval_s,
        sbj_leval_e: searchInfo.sbj_leval_e,
        aae_id: searchInfo.aae_id,
        showsbj: searchInfo.showsbj ? 1 : 0,
        isBalanceZero: searchInfo.isBalanceZero ? 1 : 0,
        hiddenTotal: searchInfo.hiddenTotal ? 1 : 0,
        quantity: searchInfo.quantity,
        sortColumn: searchInfo.sortColumn,
    };

    firstSearch = true;

    return request({
        url: "/api/AASubsidiaryLedger/Tree",
        method: "get",
        params,
    })
        .then((res: IResponseModel<string>) => {
            return Promise.resolve(res.data);
        })
        .catch(() => {
            return Promise.reject();
        });
};

const getTableData = (init = false) => {
    loading.value = true;
    if (init && route.query.period_s && route.query.period_e) {
        searchInfo.period_s = Number(route.query.period_s);
        searchInfo.period_e = Number(route.query.period_e);
        period_s.value = Number(route.query.period_s);
        period_e.value = Number(route.query.period_e);
    }
    getTreeData()
        .then((data: string) => {
            let tree_data: any[] = JSON.parse(data);
            if (tree_data.length === 0) {
                loading.value = false;
                treeData.value = [];
                tableData.value = [];
                emptyText.value = "暂无数据";
                paginationData.total = 0;
                setInfos();
                searchInfo.faname = fzTypeList.value.find((item) => item.id === searchInfo.aa_type)!.label;
            } else {
                treeData.value = formatterTreeData(tree_data, tree_data);
                searchList.value = treeData.value;
                nextTick().then(() => {
                    if (init && route.query.AAE_ID) {
                        const item = treeData.value.find((item: Itree) => item.id === route.query.AAE_ID);
                        if (item) {
                            treeRef.value?.setCurrentKey(item.treeId);
                            nodeClick(treeRef.value?.getCurrentNode());
                            rightTreeScrollTo(item.treeId);
                        }
                    } else {
                        treeRef.value?.setCurrentKey(treeData.value[0].treeId);
                        nodeClick(treeRef.value?.getCurrentNode());
                    }
                });
            }
        })
        .catch(() => {
            loading.value = false;
        });
};

function rightTreeScrollTo(treeId: any) {
    const targetNode = treeRef.value?.$el.querySelector(`[data-key="${treeId}"]`);
    if (targetNode) {
        targetNode.scrollIntoView();
    }
}

// 科目级别
const codeLengthStr = ref("");
const maxCodelength = ref<number>(4);
function getAsubCodeLength() {
    request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        codeLengthStr.value = res.data.codeLength.join("");
        const codeLengthList: number[] = res.data.codeLength;
        const codeLength = codeLengthList.length;
        maxCodelength.value = codeLength;
        searchParams.sbj_leval_e = codeLength;
        searchInfo.sbj_leval_e = codeLength;
    });
}
const aatypeChange = () => {
    currentAuxiliary.value = " ";
    auxiliaryItem.value = "";
    request({
        url: `/api/AssistingAccounting/ListByAAType?aaTypeId=${searchInfo.aa_type}&showAll=${true}`,
    }).then((res: IResponseModel<IAssistingAccount[]>) => {
        searchPeriodList.value = res.data.map((v: IAssistingAccount) => {
            return {
                id: v.aaeid,
                label: v.aanum + " " + v.aaname,
            };
        });
        realyArray.value = res.data.map((v) => new SelectWithLotOfDataOptions(v.aaeid + "", v.aanum + " " + v.aaname));
    });
};
let currentAuxiliary = ref(" ");
const handleAuxiliaryChange = (item: string) => {
    let current = searchPeriodList.value.find((v: ISelectList) => v.id + "" === item);
    currentAuxiliary.value = current!.label;
};
// 生成一个八位的随机数
const random = () => {
    return Math.floor(Math.random() * *********);
};
// 生成一个唯一的id
const generateId = `select-${random()}`;
const handleVisibleChange = (visible: boolean) => {
    let timer = setTimeout(() => {
        let selectFilterInput = document.querySelector(`.${generateId} .select-trigger .el-input__inner`) as HTMLInputElement;
        if (selectFilterInput) {
            selectFilterInput.value = auxiliaryItem.value
                ? (searchPeriodList.value.find((item) => item.id === Number(auxiliaryItem.value)) as any).label
                : "";
        }
        clearTimeout(timer);
    }, 0);
    if (visible) {
        showRealyArray.value = JSON.parse(JSON.stringify(realyArray.value));
    }
};

// 辅助项目
const searchRet = ref();

const handleInput = (value: string) => {
    // 搜索结果重置
    searchRet.value = [];
    if (value.trim()) {
        searchRet.value = showRealyArray.value.filter((item) => item.label.includes(value));
        searchRet.value.length > 0 && (auxiliaryItem.value = searchRet.value[0].value);
    } else {
        searchRet.value = [];
    }

    let selectFilterInput = document.querySelector(`.${generateId} .select-trigger .el-input__inner`) as HTMLInputElement;
    if (selectFilterInput) {
        if (!selectFilterInput.value || !auxiliaryItem.value) {
            selectFilterInput.placeholder = "";
            auxiliaryItem.value = "";
        }
    }
};

const nodeClick = (data: any) => {
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
        return;
    }
    const aae_pid =
        data.attributes.type === "none" ? treeData.value.find((item: Itree) => item.attributes.aaname == data.attributes.aaname)!.id : "0";
    const aae_id = data.id;
    searchInfo.faname =
        fzTypeList.value.find((item) => item.id === searchInfo.aa_type)!.label +
        "：" +
        data.attributes.aanum +
        data.attributes.aaname +
        (data.attributes.type === "none" ? "_" + data.attributes.asubname : "");
    nodeAAE_ID.value = aae_id;
    nodeAAE_PID.value = aae_pid;
    loading.value = true;
    const params = {
        ...getBaseParams(),
        aae_id,
        aae_pid,
        sortColumn: searchInfo.sortColumn,
        page: paginationData.currentPage ? paginationData.currentPage : 1,
        rows: paginationData.pageSize,
        ifpage: 0,
    };

    params.sbj_id = currentAsubId.value;
    setInfos();
    request({
        url: "/api/AASubsidiaryLedger",
        params,
    })
        .then((res: IResponseModel<IResTableData>) => {
            if (res.state === 1000) {
                columns.value = ChangeCulumns(searchInfo.quantity, searchInfo.fcid);
                tableData.value = res.data.rows;
                paginationData.total = res.data.total;
                loading.value = false;
            } else {
                loading.value = false;
            }
        })
        .catch(() => {
            loading.value = false;
        });
};

const getBaseParams = () => {
    return {
        isBalanceZero: searchInfo.isBalanceZero ? 1 : 0,
        hiddenTotal: searchInfo.hiddenTotal ? 1 : 0,
        aa_type: searchInfo.aa_type + "",
        // faname: searchInfo.faname,
        fcid: searchInfo.fcid,
        period_e: searchInfo.period_e,
        period_s: searchInfo.period_s,
        quantity: searchInfo.quantity,
        sbj_id: currentAsubId.value ?? "",
        sbj_leval_s: searchInfo.sbj_leval_s,
        sbj_leval_e: searchInfo.sbj_leval_e,
        showsbj: searchInfo.showsbj ? 1 : 0,
    };
};

const scrollRef = ref();
const casheScrollTop =  ref(0);
const handleScroll = (e: any) => {
    casheScrollTop.value = e.scrollTop;
}

// 切换回页面，数据刷新
const changedTimes = ref(0);
// 凭证弹窗
const confirmWithoutGlobalVisible = ref(false);

const reLoadCurrentPage = () => {
    searchInfo.aa_type = route.query.AA_TYPE ? Number(route.query.AA_TYPE) : fzTypeList.value[0]?.id;
    getTableData(true);
    currentSlot.value = "main";
    changedTimes.value = 0;
};

const routerArrayStore = useRouterArrayStoreHook();
let cacheRan = ""
onActivated(() => {
    scrollRef.value?.setScrollTop(casheScrollTop.value);
    const { AA_TYPE, AAE_ID, period_s, period_e } = route.query;
    let isRoute = AA_TYPE && AAE_ID && period_s && period_e;
    if (!isRoute) return;
    if (
        route.query.AA_TYPE !== searchInfo.aa_type ||
        route.query.AAE_ID !== searchInfo.aae_id ||
        route.query.period_s != searchInfo.period_s + "" ||
        route.query.period_e != searchInfo.period_e + ""
    ) {
        if(isErp.value) {
            if(route.query.ran && cacheRan === route.query.ran) return
        }
        if (currentSlot.value === "add" && changedTimes.value > 1) {
            confirmWithoutGlobalVisible.value = true;
        } else {
            searchInfo.period_s = Number(period_s);
            searchInfo.period_e = Number(period_e);
            searchInfo.aa_type = route.query.AA_TYPE ? Number(route.query.AA_TYPE) : fzTypeList.value[0]?.id;
            getTableData(true);
            currentSlot.value = "main";
        }
         cacheRan = route.query.ran as string ?? ""
    }
});

onMounted(() => {
    searchInfo.period_s = period_s.value;
    searchInfo.period_e = period_e.value;
    document.body.scrollTop = 0;
    columns.value = ChangeCulumns(searchInfo.quantity, searchInfo.fcid);
    getAsubCodeLength();
});

watch([period_s, period_e], ([_s, _e]) => {
    searchInfo.period_s = _s;
    searchInfo.period_e = _e;
    updateRouteFateMonth();
});

function handleVoucherCancel() {
    currentSlot.value = "main";
    nodeClick(treeRef.value?.getCurrentNode());
}

// 展开和收起右侧树标志
const flag = reactive({
    expandFlag: true,
    closeFlag: true
});

const showRealyArray = ref<Array<SelectWithLotOfDataOptions>>([]);
const showfzTypeList = ref<Array<ISelectList>>([]);
const showfcList = ref<ISelectList[]>([]);
watchEffect(() => { 
    showRealyArray.value = JSON.parse(JSON.stringify(realyArray.value));  
    showfzTypeList.value = JSON.parse(JSON.stringify(fzTypeList.value));  
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));
});
function realyFilterMethod(value: string) {
    showRealyArray.value = commonFilterMethod(value, realyArray.value, 'label');
}
function fzTypeFilterMethod(value: string) {
    showfzTypeList.value = commonFilterMethod(value, fzTypeList.value, 'label');
}
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";
@import "@/style/AccountBooks/SubsidiaryLedger.less";
@import "@/style/RightTreeExpand.less";

.fcid-select {
    .detail-el-select(132px);
}

.aatype-select {
    :deep(.el-select-v2) {
        .el-select-v2__wrapper {
            padding-right: 50px;
        }
    }
}
.main-content {
    min-width: 1100px;
    .main-top {
        justify-content: space-between;
        .main-top-left {
            display: flex;
            align-items: center;
            .aatype {
                .text {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                }

                text-align: left;
                .detail-el-select(122px);
            }
        }
        .main-tool-right {
            display: flex;
            align-items: center;
            :deep(.print-down-90.button.dropdown) {
                background-position: right 10px center;
            }
            :deep(.print-down-114.button.dropdown) {
                background-position: right 10px center;
            }
        }
    }
    .main-center {
        .main-left {
            height: 100%; 
            display: flex; 
            flex-direction: column;
            .table {
                flex: 1;
                min-height: 0;
            }
        }
        .main-right {
            position: relative;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            justify-content: start;
            .tree {
                flex: 1;
            }
        }
    }
}

// .line-item {
//     :deep(.el-input__inner) {
//         &::placeholder {
//             color: #333;
//         }
//     }
// }
& .tree-icon {
    display: inline-block;
    vertical-align: top;
    height: 21px;
    width: 21px;
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: center;
}
& .is-expanded {
    & .tree-icon.tree-folder.tree-folder-open {
        background-image: url(@/assets/icons/folder-open.png);
    }
}
& .tree-icon.tree-folder.tree-folder-open {
    background-image: url(@/assets/icons/folder.png);
}
& .tree-icon.tree-file {
    background-image: url(@/assets/icons/file.png);
    background-size: 16px 18px;
}
& .tree-title {
    color: var(--font-color);
    font-size: var(--h5);
    line-height: 21px;
    display: inline-block;
}

// 默认滚动条样式
.select-info {
    :deep(.vue-recycle-scroller) {
        &::-webkit-scrollbar {
            width: 6px;
            padding-right: 2px;
            background-color: #fff;
        }
        &::-webkit-scrollbar-thumb {
            width: 6px;
            border-radius: 3px;
            background: #d0d0d0;
            &:hover {
                background-color: #b1b1b1;
            }
        }
        .select-info-inner {
            -webkit-line-clamp: 1 !important;
            white-space: nowrap;
            display: block !important;
        }
    }
}
:deep(.el-select-v2__popper) {
    margin-top: -12px;
}
.export-content {
    text-align: center;

    .export-main {
        width: 510px;
        text-align: left;
        display: inline-block;

        .line-item {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 20px;
            padding-top: 10px;
            span {
                width: 70px;
                text-align: right;
            }
            :deep(.export-ype-dropdown) {
                border: 1px solid #dcdfe6;
                transform: translateX(-25px);
            }
            :deep(.el-checkbox) {
                font-weight: normal;
            }
            :deep(.el-select-dropdown) {
                &.is-multiple .el-select-dropdown__item.selected.hover {
                    background: var(--main-color);
                }
            }
        }
    }

    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}
.ellipsis{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
// 业财样式兼容
body[erp] {
    .content {
        .main-content {
            .main-top {
                border-bottom: 1px solid #ebebeb;
                margin-bottom: 20px;
            }
            .main-center {
                .main-left {
                    .table {
                        flex: 1;
                        min-height: 0;
                    }
                }
                .main-right {
                    justify-content: stretch;
                    .tree {
                        height: 100%;
                        :deep(.el-tree-node.tree-file) {
                            .el-tree-node__content {
                                .el-tree-node__label {
                                    background-image: url("@/assets/Icons/file-erp.png");
                                    background-size: contain;
                                }
                            }
                        }
                        :deep(.el-tree-node__content) {
                            height: 30px;
                        }
                    }
                }
            }
        }
    }
}
</style>
