<template>
    <div class="slot-content">
        <div class="slot-mini-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #step_three>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="3" />
                                <div class="step-edit">
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="开户省份：" prop="branchProvince">
                                                    <Select
                                                        v-model="companyToSPDBInfo.branchProvince"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectProvince"
                                                    >
                                                        <Option
                                                            v-for="item in props.provinceData"
                                                            :value="item.id"
                                                            :label="item.name"
                                                            :key="item.id"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="开户城市：" prop="branchCity">
                                                    <Select
                                                        v-model="companyToSPDBInfo.branchCity"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectCity"
                                                    >
                                                        <Option
                                                            v-for="item in cityList"
                                                            :value="item.name"
                                                            :label="item.name"
                                                            :key="item.id"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="选择网点：" :required="true" prop="branchName">
                                                    <Select
                                                        v-model="companyToSPDBInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in outletList"
                                                            :value="item.branchNo"
                                                            :label="item.branchName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToSPDBInfo.branchAddress || companyToSPDBInfo.branchTel">
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToSPDBInfo.branchAddress">
                                                        {{ companyToSPDBInfo.branchAddress }}
                                                    </div>
                                                    <div v-show="companyToSPDBInfo.branchTel">
                                                        网点电话：{{ companyToSPDBInfo.branchTel }}
                                                    </div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToSPDBInfo" label-position="right" label-width="190px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true">
                                                    <el-radio-group v-model="companyToSPDBInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToSPDBInfo.accountType === 1">
                                                <el-form-item label="基本户开户银行：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.openBankName"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="基本户账号：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.basicBankAcctNo"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToSPDBInfo.accountType === 1">
                                                <el-form-item label="基本户开户许可证核准号：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.openAcctLcnsId"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="一般存款账户类型：" :required="true">
                                                    <Select
                                                        v-model="companyToSPDBInfo.depositAcctType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                    >
                                                        <Option
                                                            v-for="item in SPDBacctTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="back">上一步</a>
                                    <a class="button ml-28 solid-button" @click="toStepFour">下一步</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #step_four>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="4" />
                                <div class="step-edit">
                                    <div class="block-title">公司信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="注册资金：" :required="true" prop="regCapital">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.regCapital"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="统一社会信用代码到期日：" :required="true" prop="unifiedNumberExpData">
                                                    <el-date-picker
                                                        v-model="companyToSPDBInfo.unifiedNumberExpData"
                                                        type="date"
                                                        placeholder="请选择日期"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 240px"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow addressRow">
                                                <el-form-item label="办公地址：" :required="true" prop="companyAddr">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.companyAddr"
                                                        style="width: 660px"
                                                        placeholder="请输入公司详细办公地址"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件类型：" :required="true" prop="legalRprsntIDType">
                                                    <Select
                                                        v-model="companyToSPDBInfo.legalRprsntIDType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        @change="legalRprsntIDTypeChange"
                                                    >
                                                        <Option
                                                            v-for="item in SPDBlegalRprsntIDTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="法人证件号码：" :required="true" prop="legalRprsntIDNo">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.legalRprsntIDNo"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                        @input="legalRprsntIDNoInput"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件到期日：" :required="true" prop="legalRprsntIDExpData">
                                                    <el-date-picker
                                                        v-model="companyToSPDBInfo.legalRprsntIDExpData"
                                                        type="date"
                                                        placeholder="请选择日期"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 240px"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">经办人信息</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="客户名称：" :required="true" prop="clientName">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.clientName"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="预留手机号：" :required="true" prop="defaultMobile">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.defaultMobile"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">预约时间</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="预约期望时间：" :required="true" prop="appointmentTime">
                                                    <el-date-picker
                                                        v-model="companyToSPDBInfo.appointmentTime"
                                                        type="date"
                                                        placeholder="请选择预约时间，具体以银行审核为准"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 330px"
                                                        :disabled-date="disabledDateStart"
                                                        @change="dateChange"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_three'">上一步</a>
                                    <a class="button ml-28 solid-button longer-button" @click="toCofirmInfo">立即预约开户</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #confirm>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">确认预约开户详情</div>
                            <div class="open-main-content">
                                <div class="step-edit">
                                    <div class="block-title">开户银行</div>
                                    <div class="block-main">
                                        <el-row class="isRow"><div class="openbank-line">开户银行：浦发银行</div> </el-row>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="开户省份：" prop="branchProvince">
                                                    <Select
                                                        v-model="companyToSPDBInfo.branchProvince"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectProvince"
                                                    >
                                                        <Option
                                                            v-for="item in props.provinceData"
                                                            :value="item.id"
                                                            :label="item.name"
                                                            :key="item.id"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="开户城市：" prop="branchCity">
                                                    <Select
                                                        v-model="companyToSPDBInfo.branchCity"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectCity"
                                                    >
                                                        <Option
                                                            v-for="item in cityList"
                                                            :value="item.name"
                                                            :label="item.name"
                                                            :key="item.id"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="选择网点：" :required="true" prop="branchName">
                                                    <Select
                                                        v-model="companyToSPDBInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in outletList"
                                                            :value="item.branchNo"
                                                            :label="item.branchName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToSPDBInfo.branchAddress || companyToSPDBInfo.branchTel">
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToSPDBInfo.branchAddress">
                                                        {{ companyToSPDBInfo.branchAddress }}
                                                    </div>
                                                    <div v-show="companyToSPDBInfo.branchTel">
                                                        网点电话：{{ companyToSPDBInfo.branchTel }}
                                                    </div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToSPDBInfo" label-position="right" label-width="190px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true">
                                                    <el-radio-group v-model="companyToSPDBInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToSPDBInfo.accountType === 1">
                                                <el-form-item label="基本户开户银行：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.openBankName"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="基本户账号：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.basicBankAcctNo"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToSPDBInfo.accountType === 1">
                                                <el-form-item label="基本户开户许可证核准号：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.openAcctLcnsId"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="一般存款账户类型：" :required="true">
                                                    <Select
                                                        v-model="companyToSPDBInfo.depositAcctType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                    >
                                                        <Option
                                                            v-for="item in SPDBacctTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">基本信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>系统会自动同步账套公司信息，请您检查是否准确</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="公司名称：" :required="true" prop="companyName">
                                                    <el-autocomplete
                                                        ref="asNameRef"
                                                        @blur="handleCompanyBlur"
                                                        v-model="companyToSPDBInfo.companyName"
                                                        :prop="[
                                                            {
                                                                required: true,
                                                                message: '亲，单位名称不能为空',
                                                                trigger: ['blur', 'change'],
                                                            },
                                                        ]"
                                                        :fetch-suggestions="querySearch"
                                                        :trigger-on-focus="false"
                                                        placeholder="请输入完整的单位名称"
                                                        style="width: 238px"
                                                        @select="selectName"
                                                    />
                                                </el-form-item>
                                                <el-form-item label="统一社会信用代码：" :required="true" prop="unifiedNumber">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.unifiedNumber"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>

                                            <el-row class="isRow">
                                                <el-form-item label="注册资金：" :required="true">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.regCapital"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="统一社会信用代码到期日：" :required="true" prop="unifiedNumberExpData">
                                                    <el-date-picker
                                                        v-model="companyToSPDBInfo.unifiedNumberExpData"
                                                        type="date"
                                                        placeholder="请选择日期"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 240px"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow addressRow">
                                                <el-form-item label="办公地址：" :required="true" prop="companyAddr">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.companyAddr"
                                                        style="width: 660px"
                                                        placeholder="请输入公司详细办公地址"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人姓名：" :required="true" prop="legalName">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.legalName"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                        @blur="handleNameBlur"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="法人手机号码：" :required="true" prop="legalMobile">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.legalMobile"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>

                                            <el-row class="isRow">
                                                <el-form-item label="法人证件类型：" :required="true" prop="legalRprsntIDType">
                                                    <Select
                                                        v-model="companyToSPDBInfo.legalRprsntIDType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        @change="legalRprsntIDTypeChange"
                                                    >
                                                        <Option
                                                            v-for="item in SPDBlegalRprsntIDTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="法人证件号码：" :required="true" prop="legalRprsntIDNo">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.legalRprsntIDNo"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                        @input="legalRprsntIDNoInput"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件到期日：" :required="true" prop="legalRprsntIDExpData">
                                                    <el-date-picker
                                                        v-model="companyToSPDBInfo.legalRprsntIDExpData"
                                                        type="date"
                                                        placeholder="请选择日期"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 240px"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">更多信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请补充如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="客户名称：" :required="true" prop="clientName">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.clientName"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="预留手机号：" :required="true" prop="defaultMobile">
                                                    <el-input
                                                        v-model="companyToSPDBInfo.defaultMobile"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">预约时间</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToSPDBInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="预约期望时间：" :required="true" prop="appointmentTime">
                                                    <el-date-picker
                                                        v-model="companyToSPDBInfo.appointmentTime"
                                                        type="date"
                                                        placeholder="请选择预约时间，具体以银行审核为准"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 330px"
                                                        :disabled-date="disabledDateStart"
                                                        @change="dateChange"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_four'">上一步</a>
                                    <a class="button ml-28 solid-button" @click="saveInfo">确认无误</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </ContentSlider>
        </div>
        <PreOpenResDialog v-model="openResultDialog" bank="浦发银行" @close="toMain" />
    </div>
</template>

<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import { ref, watch, reactive } from "vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import TopStep from "./TopStep.vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { type IArea, type IBaseCompanyInfo, SPDBlegalRprsntIDTypeList, SPDBacctTypeList, SPDBCompanyInfoModel } from "../types";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import PreOpenResDialog from "./PreOpenResDialog.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import {
    disabledDateStart,
    isBeforeToday,
    checkLegalPersonName,
    cancelConfirm,
    checkSPDBStepThree,
    checkSPDBStepFour,
    checkCompanyInfo,
} from "../utils";

const props = defineProps({
    provinceData: { type: Array as () => Array<IArea>, required: true },
    systemType: { type: Number, required: true },
});
const emit = defineEmits<{
    (
        e: "back",
        data: {
            companyName: string;
            legalPersonName: string;
            legalPhone: string;
            socialCreditCode: string;
            accountType: number;
            rightLegalPersonName: string;
        }
    ): void;
    (e: "success"): void;
}>();
const slots = ["step_three", "step_four", "confirm"];
const currentSlot = ref("step_three");
const asNameRef = ref();
const queryParams = reactive({
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
});
const asId = useAccountSetStore().accountSet?.asId;
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};
const rightLegalPersonName = ref("");
function selectName(item: any) {
    companyToSPDBInfo.value.companyName = item.value;
    companyToSPDBInfo.value.unifiedNumber = item.creditCode;
    companyToSPDBInfo.value.legalName = rightLegalPersonName.value = item.legalPersonName;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}
const handleCompanyBlur = (event: any) => {
    getCompanyDetailApi(1010, decodeURIComponent(event.target.value)).then((res: any) => {
        if (res.state === 1000) {
            rightLegalPersonName.value = res.data?.legalPersonName ?? "";
        }
    });
};
const dateChange = (date: any) => {
    if (isBeforeToday(date)) {
        companyToSPDBInfo.value.appointmentTime = "";
    }
};
const cityList = ref<any[]>([]);
const outletList = ref<ISPDBBranchItem[]>([]);

const getCityList = (proId: string) => {
    request({ url: `/api/City/CityList?proId=${proId}`, method: "get" }).then((res: IResponseModel<any[]>) => {
        if (res.state === 1000) {
            cityList.value = res.data;
        }
    });
};
const selectProvince = (val: string) => {
    companyToSPDBInfo.value.branchCity = "";
    companyToSPDBInfo.value.branchName = "";
    companyToSPDBInfo.value.branchNo = "";
    companyToSPDBInfo.value.branchAddress = "";
    companyToSPDBInfo.value.branchTel = "";
    outletList.value = [];
    getCityList(val);
};
const selectCity = (cityName: string) => {
    companyToSPDBInfo.value.branchName = "";
    companyToSPDBInfo.value.branchNo = "";
    companyToSPDBInfo.value.branchAddress = "";
    companyToSPDBInfo.value.branchTel = "";
    outletList.value = [];
    getSPDBBranch(cityName);
};
const selectOutlet = (val: string) => {
    const outlet = outletList.value.find((item: ISPDBBranchItem) => item.branchNo === val);
    if (outlet) {
        const { branchAddr, branchTel, branchName } = outlet;
        companyToSPDBInfo.value.branchAddress = branchAddr || "";
        companyToSPDBInfo.value.branchTel = branchTel || "";
        companyToSPDBInfo.value.branchName = branchName || "";
    }
};
//浦发银行网点信息
interface ISPDBBranchItem {
    branchNo: string; //网点号
    branchName: string; //网点名称
    branchAddr: string; //网点地址
    branchTel: string; //网点电话
    provinceName: string; //省份名称
    cityName: string; //城市名称
    bankName: string; //所属分行
    business: string; //营业时间
}
const getSPDBBranch = (cityName: string) => {
    const data = {
        branchName: "",
        cityName,
    };
    request({
        url: window.preOpenBankUrl + "/api/SPDBPreOpen/QueryBranch",
        method: "post",
        data,
    }).then((res: IResponseModel<ISPDBBranchItem[]>) => {
        if (res.state === 1000) {
            outletList.value = res.data;
        }
    });
};
const back = () => {
    cancelConfirm().then((r) => {
        if (r) {
            emit("back", {
                companyName: companyToSPDBInfo.value.companyName,
                socialCreditCode: companyToSPDBInfo.value.unifiedNumber,
                legalPersonName: companyToSPDBInfo.value.legalName,
                legalPhone: companyToSPDBInfo.value.legalMobile,
                rightLegalPersonName: rightLegalPersonName.value,
                accountType: companyToSPDBInfo.value.accountType,
            });
            saveCancelInfo();
        }
    });
};
const saveCancelInfo = () => {
    const data = {
        ...getSPDBBankParams(),
    } as any;
    request({
        url: window.preOpenBankUrl + "/api/SPDBPreOpen/Save",
        method: "post",
        data,
    });
};
//法人证件身份证 限制18位
const legalRprsntIDTypeChange = (value: string) => {
    if (value === "1") {
        companyToSPDBInfo.value.legalRprsntIDNo = companyToSPDBInfo.value.legalRprsntIDNo.slice(0, 18);
    }
};
const legalRprsntIDNoInput = (value: string) => {
    if (value.length > 18 && companyToSPDBInfo.value.legalRprsntIDType === "1") {
        companyToSPDBInfo.value.legalRprsntIDNo = value.slice(0, 18);
    }
};
const toStepFour = () => {
    if (!checkSPDBStepThree(companyToSPDBInfo.value)) return;
    currentSlot.value = "step_four";
};

//步骤三

//步骤四 补全公司以及申请人信息
const companyToSPDBInfo = ref(new SPDBCompanyInfoModel());

const operateInfoRules = ref({
    regCapital: [{ required: true, message: "请输入注册资金", trigger: "blur" }],
    unifiedNumberExpData: [{ required: true, message: "请选择统一社会信用代码到期日", trigger: "blur" }],
    companyAddr: [{ required: true, message: "请输入办公地址", trigger: "blur" }],
    legalRprsntIDType: [{ required: true, message: "请选择法人证件类型", trigger: "blur" }],
    legalRprsntIDNo: [{ required: true, message: "请输入法人证件号码", trigger: "blur" }],
    legalRprsntIDExpData: [{ required: true, message: "请选择法人证件到期日", trigger: "blur" }],
    clientName: [{ required: true, message: "请输入客户名称", trigger: "blur" }],
    defaultMobile: [{ required: true, message: "请输入预留手机号", trigger: "blur" }],
    accountType: [{ required: true, message: "请选择账户类型", trigger: "blur" }],
    companyName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
    unifiedNumber: [{ required: true, message: "请输入统一社会信用代码", trigger: "blur" }],
    legalName: [{ required: true, message: "请输入法人姓名", trigger: "blur" }],
    legalMobile: [{ required: true, message: "请输入法人手机号码", trigger: "blur" }],
    appointmentTime: [{ required: true, message: "请选择预约时间", trigger: "blur" }],
    branchProvince: [{ required: true, message: "请选择开户省份", trigger: "blur" }],
    branchCity: [{ required: true, message: "请选择开户城市", trigger: "blur" }],
    branchName: [{ required: true, message: "请选择选择网点", trigger: "blur" }],
});

const toCofirmInfo = () => {
    if (!checkSPDBStepFour(companyToSPDBInfo.value)) return;
    currentSlot.value = "confirm";
};

//确认预约
const isCanToNext = ref(true);
const handleNameBlur = () => {
    if (!checkLegalPersonName(companyToSPDBInfo.value.legalName, rightLegalPersonName.value)) {
        isCanToNext.value = false;
    }
    const timer = setTimeout(() => {
        isCanToNext.value = true;
        clearTimeout(timer);
    }, 200);
};
const openResultDialog = ref(false);
let canClickSaveInfo = true;
const saveInfo = () => {
    if (!isCanToNext.value) return; //法人姓名失焦校验失败防止提交校验2次提示
    if (!canClickSaveInfo) return;
    if (!checkAllInfo()) return;
    if (!checkLegalPersonName(companyToSPDBInfo.value.legalName, rightLegalPersonName.value)) return;
    canClickSaveInfo = false;
    const data = {
        ...getSPDBBankParams(),
    };
    request({
        url: window.preOpenBankUrl + "/api/SPDBPreOpen/Open",
        method: "post",
        data,
    })
        .then((res: any) => {
            if (res.state === 1000) {
                openResultDialog.value = true;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .finally(() => {
            canClickSaveInfo = true;
        });
};
const getSPDBBankParams = () => {
    const { accountType, depositAcctType, openBankName, basicBankAcctNo, openAcctLcnsId, ...params } = JSON.parse(
        JSON.stringify(companyToSPDBInfo.value)
    );
    return {
        id: "",
        asId: asId + "",
        system: props.systemType,
        content: {
            ...params,
            accountType,
            depositAcctType: accountType === 1 ? depositAcctType : "",
            openBankName: accountType === 1 ? openBankName : "",
            basicBankAcctNo: accountType === 1 ? basicBankAcctNo : "",
            openAcctLcnsId: accountType === 1 ? openAcctLcnsId : "",
        },
    };
};
const toMain = () => {
    currentSlot.value = "step_three";
    resetInfo();
    emit("success");
};

const initBaseCompanyInfo = (data: IBaseCompanyInfo) => {
    const {
        companyName,
        socialCreditCode,
        legalPersonName,
        legalPhone,
        accountType,
        rightLegalPersonName: rightName,
        taxProvinceName,
    } = data;
    companyToSPDBInfo.value.companyName = companyName;
    companyToSPDBInfo.value.unifiedNumber = socialCreditCode;
    companyToSPDBInfo.value.legalName = legalPersonName;
    rightLegalPersonName.value = rightName;
    companyToSPDBInfo.value.legalMobile = legalPhone;
    companyToSPDBInfo.value.accountType = accountType;
    if (taxProvinceName && companyToSPDBInfo.value.branchProvince === "") {
        const province = props.provinceData.find((item) => item.name === taxProvinceName);
        if (province) {
            companyToSPDBInfo.value.branchProvince = province.id;
            getCityList(province.id);
        }
    }
};
const initCancelSPDBInfo = (data: any) => {
    companyToSPDBInfo.value = Object.assign(companyToSPDBInfo.value, data);
    companyToSPDBInfo.value.branchProvince && getCityList(data.branchProvince);
    companyToSPDBInfo.value.branchCity && getSPDBBranch(data.branchCity);
    companyToSPDBInfo.value.branchNo && selectOutlet(data.branchNo);
};
watch(
    () => companyToSPDBInfo.value.accountType,
    () => {
        companyToSPDBInfo.value.acctType = companyToSPDBInfo.value.accountType === 0 ? "C" : "D";
    },
    { immediate: true }
);

const resetInfo = () => {
    companyToSPDBInfo.value = new SPDBCompanyInfoModel();
    rightLegalPersonName.value = "";
};


const checkAllInfo = () => {
    return (
        checkSPDBStepThree(companyToSPDBInfo.value) &&
        checkCompanyInfo(companyToSPDBInfo.value) &&
        checkSPDBStepFour(companyToSPDBInfo.value)
    );
};
defineExpose({
    initBaseCompanyInfo,
    initCancelSPDBInfo,
    resetInfo,
    saveCancelInfo,
});
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
