<template>
    <div class="slot-content align-center">
        <div class="slot-content-mini" :class="props.theme + ' open-body'">
            <div class="open-title">
                <div class="open-icon">
                    <img src="@/assets/Icons/nm-icon.png" alt="" class="lemon" />
                    <div class="open-ningmeng-text">柠檬云</div>
                    <div class="open-ningmeng-text2">×</div>
                    <img src="@/assets/Icons/icbc-icon.png" alt="" class="bank" v-if="props.theme === 'icbc'" />
                    <div v-if="props.theme === 'spdb'" class="spdb">
                        <img src="@/assets/Icons/spdb-icon.png" alt="" />
                        <div>浦发银行</div>
                    </div>
                    <img src="@/assets/Icons/pab-icon.png" alt="" class="pab" v-if="props.theme === 'pab'" />
                </div>
            </div>
            <div class="openTextTitle">{{ preBankInfo[0] }}</div>
            <div class="openTextContent">{{ preBankInfo[1] }}</div>
            <div class="openTextTip">{{ preBankInfo[2] }}</div>
            <div class="openButton" v-show="props.theme === 'icbc' && !isWxwork">
                <a @click="globalWindowOpen(icbcHref[0])" class="solid-button large orange"> 我要开户 </a>
                <!-- <a @click="globalWindowOpen(icbcHref[1])" class="button orange large ml-10"> 查看详情 </a> -->
            </div>
            <div class="openButton" v-show="props.theme === 'spdb' && !isWxwork">
                <a @click="globalWindowOpen(spdbHref[0])" class="solid-button large blue"> 我要开户 </a>
                <a @click="globalWindowOpen(spdbHref[1])" class="button blue large ml-10"> 查看详情 </a>
            </div>
            <div class="openButton" v-show="props.theme === 'pab' && !isWxwork">
                <a @click="globalWindowOpen(pabHref[0])" class="solid-button large orange"> 我要开户 </a>
                <a @click="globalWindowOpen(pabHref[1])" class="button orange large ml-10"> 查看详情 </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { watchEffect, ref } from "vue";
import { globalWindowOpen } from "@/util/url";
import { isInWxWork } from "@/util/wxwork";
const props = defineProps({
    theme: { type: String, required: true },
});
const icbcHref = [window.wwwHost + "/ICBCOpenAccount/OpenAccount.aspx", window.wwwHost + "/ICBCOpenAccount/Introduce.aspx"];
const spdbHref = [window.wwwHost + "/SPDBOpenAccount/OpenAccount.aspx", window.wwwHost + "/SPDBOpenAccount/Introduce.aspx"];
const pabHref = [window.wwwHost + "/PAOpenAccount/OpenAccount.aspx", window.wwwHost + "/PAOpenAccount/Introduce.aspx"];
const preBankInfo = ref<string[]>([]);
const isWxwork = ref(isInWxWork());
watchEffect(() => {
    if (props.theme === "icbc") {
        preBankInfo.value = [
            "专人服务极速开户",
            "在线自由选择工行开户网点预约办理",
            "(如有疑问可咨询柠檬云在线客服或95588)",
        ];
    } else if (props.theme === "spdb") {
        preBankInfo.value = ["专人服务极速开户", "在线预约开户，专人贴心服务，企业专享多重福利", "(如有疑问可咨询柠檬云在线客服或95528)"];
    } else if (props.theme === "pab") {
        preBankInfo.value = [
            "在线预约极速开户，尽享千元权益",
            "千元开户大礼包，账户实时生成，全程专人服务",
            "(如有疑问可咨询柠檬云在线客服或95511)",
        ];
    } else {
        preBankInfo.value = ["1", "2", "3"].map(String);
    }
});
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
</style>
