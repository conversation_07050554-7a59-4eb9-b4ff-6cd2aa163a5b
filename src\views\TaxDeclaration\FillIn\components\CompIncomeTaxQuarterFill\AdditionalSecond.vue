<template>
  <div class="declaration-form">
    <div class="form-title">{{ tabData.describe }}</div>
    <div class="form-info">
      <div class="info">
        <div
          v-for="(item, index) in formHeaderInfo"
          :key="index">
          <span class="label">{{ item.label }}：</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="native-table">
    <table>
      <tbody>
        <tr>
          <th>纳税人名称（盖章）：</th>
          <td></td>
          <th>纳税人识别号：</th>
          <td></td>
        </tr>
      </tbody>
    </table>
  </div>
  <!-- 技术成果投资入股企业所得税递延纳税备案表 -->
  <div class="tech-investment-table">
    <LMTable
      ref="techInvestmentTableRef"
      row-key="id"
      :data="techInvestmentData"
      :columns="techInvestmentColumns">
      <template #nameInput="{ slotColumn }">
        <el-table-column v-bind="slotColumn">
          <template #default="{ row }">
            <checkable-input
              v-if="!row.isLastRow"
              v-model="row[slotColumn.prop]"
              :row-data="row"></checkable-input>
          </template>
        </el-table-column>
      </template>
      <template #typeSelect="{ slotColumn }">
        <el-table-column v-bind="slotColumn">
          <template #default="{ row }">
            <el-select
              v-if="!row.isLastRow"
              filterable
              v-model="row[slotColumn.prop]"
              :row-data="row">
              <el-option
                v-for="item in techTypeList"
                :key="item.value"
                :value="item.value"
                :label="item.label" />
            </el-select>
          </template>
        </el-table-column>
      </template>
      <template #datePicker="{ slotColumn }">
        <el-table-column v-bind="slotColumn">
          <template #default="{ row }">
            <el-date-picker
              v-if="!row.isLastRow"
              v-model="row[slotColumn.prop]"
              type="date"
              placeholder="请选择日期"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD" />
          </template>
        </el-table-column>
      </template>
      <template #boolSelect="{ slotColumn }">
        <el-table-column v-bind="slotColumn">
          <template #default="{ row }">
            <el-select v-if="!row.isLastRow">
              <el-option
                v-for="(item, index) in [
                  { value: true, label: '是' },
                  { value: false, label: '否' },
                ]"
                :key="index"
                :value="item.value"
                :label="item.label" />
            </el-select>
          </template>
        </el-table-column>
      </template>
      <template #checkInput="{ slotColumn }">
        <el-table-column v-bind="slotColumn">
          <template #default="{ row }">
            <checkable-input
              v-if="!row.isLastRow"
              v-model="row[slotColumn.prop]"
              :row-data="row"></checkable-input>
          </template>
        </el-table-column>
      </template>
      <template #operation="{ slotColumn }">
        <el-table-column
          v-bind="slotColumn"
          align="center">
          <template #default="{ row }">
            <div
              v-if="!row.isFirstRow"
              class="operation-btns">
              <a
                class="link"
                v-if="!row.isLastRow"
                @click.prevent="handleAddTechRow()">
                增行
              </a>
              <a
                class="link"
                v-if="!row.isFirstRow && !row.isLastRow"
                @click.prevent="handleDeleteTechRow(row)">
                删除
              </a>
            </div>
          </template>
        </el-table-column>
      </template>
    </LMTable>
    <div class="footer">
      <div class="remark">
        <div class="mb-10">谨声明：本人知悉并保证本表填报内容及所附证明材料真实、完整，并承担因资料虚假而产生的法律和行政责任。</div>
        <div class="rightBotton">
          <div class="Seal mr-20">填表人:</div>
          <el-input
            class="mr-20"
            style="width: auto" />
          <div class="Seal mr-20">法定代表人签章:</div>
          <el-input
            class="mr-20"
            style="width: auto" />
          <span class="SealDate">2025 年 05 月 14 日</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { sumField } from "@/views/TaxDeclaration/utils"

  const props = defineProps({
    tabData: {
      type: Object,
      required: true,
    },
  })
  const formHeaderInfo = ref([
    { label: "税款所属期", value: "2024-10-01至2024-12-31" },
    { label: "填表日期", value: "2025-01-10" },
    { label: "金额单位", value: "元，至角分" },
  ])
  // 表格三 - 技术成果投资入股企业所得税递延纳税备案表相关
  // 表格列定义
  const techInvestmentColumns = ref([
    { prop: "rowNum", label: "行次", align: "center", width: "80", fixed: "left" },
    {
      label: "投资企业信息",
      children: [
        { label: "技术成果名称", children: [{ label: "1", prop: "techName", slot: "nameInput", width: "180" }] },
        { label: "技术成果类型", children: [{ label: "2", prop: "techType", width: "180", slot: "typeSelect" }] },
        { label: "技术成果编号", children: [{ label: "3", prop: "techCode", slot: "nameInput", width: "180" }] },
        { label: "公允价值", children: [{ label: "4", prop: "fairValue", slot: "checkInput", width: "180" }] },
        { label: "计税基础", children: [{ label: "5", prop: "taxBase", slot: "checkInput", width: "180" }] },
        { label: "取得股权时间", children: [{ label: "6", prop: "acquisitionTime", slot: "datePicker", width: "180" }] },
        { label: "递延所得", children: [{ label: "7=4-5", prop: "acquisitionRatio", width: "180" }] },
      ],
    },
    {
      label: "被投资企业信息",
      children: [
        { label: "企业名称", children: [{ label: "8", prop: "companyName", slot: "nameInput", width: "180" }] },
        { label: "纳税人识别号", children: [{ label: "9", prop: "companyCode", slot: "nameInput", width: "180" }] },
        { label: "主管税务机关", children: [{ label: "10", prop: "taxAuthority", slot: "nameInput", width: "180" }] },
        { label: "与投资方是否为关联企业", children: [{ label: "11", prop: "isRelated", slot: "boolSelect", width: "180" }] },
      ],
    },
    {
      label: "备注",
      prop: "remark",
    },
    { label: "操作", slot: "operation", width: "120", align: "center", fixed: "right" },
  ])

  // 技术成果类型选项
  const techTypeList = ref([
    { value: "1", label: "专利技术" },
    { value: "2", label: "非专利技术" },
    { value: "3", label: "软件著作权" },
    { value: "4", label: "集成电路布图设计" },
    { value: "5", label: "生物医药新品种" },
    { value: "6", label: "其他" },
  ])

  interface ITechInvestmentData {
    rowNum: string
    techName: string
    techType: string
    techCode: string
    fairValue: string
    taxBase: string
    acquisitionMethod: string
    acquisitionTime: string
    acquisitionRatio: string
    companyName: string
    companyCode: string
    taxAuthority: string
    isRelated: string
    isFirstRow: boolean
    isLastRow: boolean
  }
  // 表格数据
  const techInvestmentData = ref<ITechInvestmentData[]>([
    {
      rowNum: "1",
      techName: "",
      techType: "",
      techCode: "",
      fairValue: "0.00",
      taxBase: "0.00",
      acquisitionMethod: "",
      acquisitionTime: "",
      acquisitionRatio: "",
      companyName: "",
      companyCode: "",
      taxAuthority: "",
      isRelated: "",
      isFirstRow: false,
      isLastRow: false,
    },
    {
      rowNum: "2",
      techName: "",
      techType: "",
      techCode: "",
      fairValue: "0.00",
      taxBase: "0.00",
      acquisitionMethod: "",
      acquisitionTime: "",
      acquisitionRatio: "",
      companyName: "",
      companyCode: "",
      taxAuthority: "",
      isRelated: "",
      isFirstRow: false,
      isLastRow: false,
    },
    {
      rowNum: "合计",
      techName: "--",
      techType: "--",
      techCode: "--",
      fairValue: "0.00",
      taxBase: "0.00",
      acquisitionMethod: "--",
      acquisitionTime: "--",
      acquisitionRatio: "0.00",
      companyName: "--",
      companyCode: "--",
      taxAuthority: "--",
      isRelated: "--",
      isFirstRow: false,
      isLastRow: true,
    },
  ])

  // 需要累加的字段列表
  const fieldsToCalculateSum = ["fairValue", "taxBase", "acquisitionRatio"] as const

  // 技术成果表格的行操作方法
  const handleAddTechRow = () => {
    // 获取当前所有行数量（不包括合计行）
    const rowCount = techInvestmentData.value.length - 1

    // 创建新行
    const newRow = {
      rowNum: (rowCount + 1).toString(),
      techName: "",
      techType: "",
      techCode: "",
      fairValue: "0.00",
      taxBase: "0.00",
      acquisitionMethod: "",
      acquisitionTime: "",
      acquisitionRatio: "",
      companyName: "",
      companyCode: "",
      taxAuthority: "",
      isRelated: "",
      isFirstRow: false,
      isLastRow: false,
    }

    // 在合计行前插入新行
    techInvestmentData.value.splice(rowCount, 0, newRow)

    // 更新行号
    updateTechRowNumbers()
  }

  const handleDeleteTechRow = (row: any) => {
    const index = techInvestmentData.value.findIndex((item) => item.rowNum === row.rowNum)
    if (index !== -1) {
      techInvestmentData.value.splice(index, 1)

      // 更新行号
      updateTechRowNumbers()
    }
  }

  // 更新技术成果表格的行号
  const updateTechRowNumbers = () => {
    // 不包括最后一行（合计行）
    const dataRows = techInvestmentData.value.filter((row) => !row.isLastRow)

    dataRows.forEach((row, index) => {
      row.rowNum = (index + 1).toString()
    })

    // 更新合计行的数据
    updateTechTotals()
  }

  const techInvestmentDataWithoutTotal = computed(() => {
    return techInvestmentData.value.filter((row) => !row.isLastRow)
  })

  // 计算技术成果表格的合计值
  const updateTechTotals = () => {
    const lastRow = techInvestmentData.value[techInvestmentData.value.length - 1]
    if (lastRow && lastRow.isLastRow) {
      fieldsToCalculateSum.forEach((field) => {
        lastRow[field] = sumField(techInvestmentDataWithoutTotal.value, field)
      })
    }
  }

  // 监听技术成果表格数据变化，更新合计
  watch(
    techInvestmentDataWithoutTotal,
    () => {
      updateTechTotals()
    },
    { deep: true },
  )
</script>

<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
  @include colspan-width(10);
  .table3 {
    display: flex;
    flex-direction: column;

    .tech-investment-table {
      flex: 1;
      margin-top: 15px;
      display: flex;
      overflow: hidden;
      flex-direction: column;
      .footer {
        height: 100px;
        .remark {
          padding: 20px;
          .rightBotton {
            display: flex;
            justify-content: flex-end;
            align-items: center;
          }
        }
      }
    }
  }
</style>
