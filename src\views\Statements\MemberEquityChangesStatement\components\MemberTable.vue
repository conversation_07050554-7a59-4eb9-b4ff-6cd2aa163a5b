<template>
    <el-table
        :class="isErp ? 'erp-table' : ''"
        :data="props.tableData"
        border
        :empty-text="props.emptyText"
        fit
        stripe
        scrollbar-always-on
        highlight-current-row
        class="custom-table member-table"
        @header-dragend="headerDragend"
    >
        <el-table-column 
            label="项目" 
            min-width="83px" 
            align="left" 
            header-align="center" 
            prop="projectName"
            :width="getColumnWidth(setModule, 'projectName')"
        ></el-table-column>
        <el-table-column 
            label="股金" 
            min-width="158px" 
            align="left" 
            header-align="center"
            prop="shareCapitalName"
            :width="getColumnWidth(setModule, 'shareCapitalName')"
        >
            <template #default="scope">
                <div :key="scope.row.shareCapitalLineId" class="special-fund-name-tr">
                    <span
                        v-if="judgeShow(scope.row.shareCapitalName)"
                        class="firsttd special-fund-name-first-td"
                        :style="scope.row.shareCapitalName == '其中：' ? '' : 'border-right:1px solid #ccc;height:100%'"
                    >
                        <span>
                            {{ scope.row.shareCapitalName }}
                        </span>
                    </span>
                    <span
                        v-if="formulaShow(scope.row.shareCapitalName)"
                        class="special-fund-name-second-td"
                        :style="judgeShow(scope.row.shareCapitalName) ? 'width:48%' : 'width:100%'"
                    >
                        <TableAmountItem
                            :amount="scope.row.shareCapitalTotal"
                            :formula="scope.row.shareCapitalNote"
                            :line-number="scope.row.shareCapitalLineId"
                        ></TableAmountItem>
                    </span>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="专项基金" 
            min-width="158px" 
            align="left" 
            header-align="center"
            prop="specialFundName"
            :width="getColumnWidth(setModule, 'specialFundName')"
        >
            <template #default="scope">
                <div :key="scope.row.specialFundLineId" class="special-fund-name-tr">
                    <span
                        v-if="judgeShow(scope.row.specialFundName)"
                        class="firsttd special-fund-name-first-td"
                        :style="scope.row.specialFundName == '其中：' ? '' : 'border-right:1px solid #ccc;height:100%'"
                    >
                        <span class="" :class="scope.row.specialFundName == '国家财政直接补助' ? 'wrap' : ''">
                            {{ scope.row.specialFundName }}
                        </span>
                    </span>
                    <span
                        v-if="formulaShow(scope.row.specialFundName)"
                        class="special-fund-name-second-td"
                        :style="judgeShow(scope.row.specialFundName) ? 'width:48%' : 'width:100%'"
                    >
                        <TableAmountItem
                            :amount="scope.row.specialFundTotal"
                            :formula="scope.row.specialFundNote"
                            :line-number="scope.row.specialFundLineId"
                        ></TableAmountItem>
                    </span>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="资本公积" 
            min-width="158px" 
            align="left" 
            header-align="center"
            prop="capitalReserveName"
            :width="getColumnWidth(setModule, 'capitalReserveName')"
        >
            <template #default="scope">
                <div :key="scope.row.capitalReserveLineId" class="special-fund-name-tr">
                    <span
                        v-if="judgeShow(scope.row.capitalReserveName)"
                        class="firsttd special-fund-name-first-td"
                        :style="scope.row.capitalReserveName == '其中：' ? '' : 'border-right:1px solid #ccc;height:100%'"
                    >
                        <span>
                            {{ scope.row.capitalReserveName }}
                        </span>
                    </span>
                    <span
                        v-if="formulaShow(scope.row.capitalReserveName)"
                        class="special-fund-name-second-td"
                        :style="judgeShow(scope.row.capitalReserveName) ? 'width:48%' : 'width:100%'"
                    >
                        <TableAmountItem
                            :amount="scope.row.capitalReserveTotal"
                            :formula="scope.row.capitalReserveNote"
                            :line-number="scope.row.capitalReserveLineId"
                        ></TableAmountItem>
                    </span>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="盈余公积" 
            min-width="158px" 
            align="left" 
            header-align="center"
            prop="surplusReserveLineId"
            :width="getColumnWidth(setModule, 'surplusReserveLineId')"
        >
            <template #default="scope">
                <div :key="scope.row.surplusReserveLineId" class="special-fund-name-tr">
                    <span
                        v-if="judgeShow(scope.row.surplusReserveName)"
                        class="firsttd special-fund-name-first-td"
                        :style="scope.row.surplusReserveName == '其中：' ? '' : 'border-right:1px solid #ccc;height:100%'"
                    >
                        <span>
                            {{ scope.row.surplusReserveName }}
                        </span>
                    </span>
                    <span
                        v-if="formulaShow(scope.row.surplusReserveName)"
                        class="special-fund-name-second-td"
                        :style="judgeShow(scope.row.surplusReserveName) ? 'width:48%' : 'width:100%'"
                    >
                        <TableAmountItem
                            :amount="scope.row.surplusReserveTotal"
                            :formula="scope.row.surplusReserveNote"
                            :line-number="scope.row.surplusReserveLineId"
                        ></TableAmountItem>
                    </span>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="未分配盈余" 
            min-width="158px" 
            align="left" 
            header-align="center"
            prop="undistributedSurplusLineId"
            :width="getColumnWidth(setModule, 'undistributedSurplusLineId')"
        >
            <template #default="scope">
                <div :key="scope.row.undistributedSurplusLineId" class="special-fund-name-tr">
                    <span
                        v-if="judgeShow(scope.row.undistributedSurplusName)"
                        class="firsttd special-fund-name-first-td"
                        :style="scope.row.undistributedSurplusName == '其中：' ? '' : 'border-right:1px solid #ccc;height:100%'"
                    >
                        <span>
                            {{ scope.row.undistributedSurplusName }}
                        </span>
                    </span>
                    <span
                        v-if="formulaShow(scope.row.undistributedSurplusName)"
                        class="special-fund-name-second-td"
                        :style="judgeShow(scope.row.undistributedSurplusName) ? 'width:48%' : 'width:100%'"
                    >
                        <TableAmountItem
                            :amount="scope.row.undistributedSurplusTotal"
                            :formula="scope.row.undistributedSurplusNote"
                            :line-number="scope.row.undistributedSurplusLineId"
                        ></TableAmountItem>
                    </span>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="合计" 
            min-width="85px" 
            align="left" 
            header-align="center"
            :resizable="false"
        >
            <template #default="scope">
                <div class="special-fund-name-tr">
                    <TableAmountItem
                        v-if="!judgeAmountShow(scope.row)"
                        :amount="scope.row.totalAmount"
                        :formula="scope.row.totalNote"
                        :line-number="scope.row.surplusReserveLineId"
                    ></TableAmountItem>
                </div>
            </template>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "MemberSheet";
const isErp = ref(window.isErp);

const props = defineProps<{
    tableData: Array<any>;
    emptyText: string;
}>();

function judgeShow(property: any) {
    if (property && !["股金", "专项基金", "资本公积", "盈余公积", "未分配盈余"].includes(property)) {
        return true;
    }
    return false;
}

function formulaShow(property: any) {
    if (property && property != "其中：") {
        return true;
    }
    return false;
}
function judgeAmountShow(row: any) {
    return (
        row.shareCapitalName === "其中：" ||
        row.specialFundName === "其中：" ||
        row.capitalReserveName === "其中：" ||
        row.surplusReserveName === "其中：" ||
        row.undistributedSurplusName === "其中："
    );
}
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style scoped lang="less">
.special-fund-name-tr {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    .special-fund-name-first-td {
        width: 50%;
        display: flex;
        align-items: center;
        line-height: 18px;
        height: inherit;
        min-height: 37px;
        overflow: hidden;
        white-space: pre-wrap;
    }
    .special-fund-name-second-td {
        display: inline-block;
    }
}
td {
    display: block;
}
.member-table.el-table {
    :deep(.cell) {
        height: 100% !important;
    }
}
</style>
