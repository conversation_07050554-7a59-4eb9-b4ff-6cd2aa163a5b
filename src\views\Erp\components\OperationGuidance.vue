<template>
    <el-dialog v-model="display" center width="760px" title="业务凭证操作指引" class="custom-confirm" modal-class="modal-class">
        <div class="guidance-content">
            <div class="guidance-main">
                <div class="top mb-20">
                    <span class="guidance-title">业务生成凭证功能，可便捷选择业务单据，一键生成所需凭证</span>
                    <span class="link" @click="globalWindowOpen('https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=110134101')">
                        更多帮助
                    </span>
                </div>
                <div class="center">
                    <div class="item" @click="switchRouter(1)">
                        <div class="item-label">第一步</div>
                        <img src="@/assets/Erp/business-voucher-1.png" />
                        <div class="link-item link">科目关联设置</div>
                        <div class="default">让业务基础资料和财务会计科目关联对应，生成业务凭证时自动带出科目</div>
                    </div>
                    <div class="allow"></div>
                    <div class="item" @click="switchRouter(2)">
                        <div class="item-label">第二步</div>
                        <img src="@/assets/Erp/business-voucher-2.png" />
                        <div class="link-item link">业务凭证模板</div>
                        <div class="default">针对所有单据类型或业务场景配置凭证模板，生成业务凭证时自动按模板生成</div>
                    </div>
                    <div class="allow"></div>
                    <div class="item" @click="switchRouter(3)">
                        <div class="item-label">第三步</div>
                        <img src="@/assets/Erp/business-voucher-3.png" />
                        <div class="link-item link">业务生成凭证</div>
                        <div class="default">快速选择业务单据并配置凭证生成规则，一键生成业务凭证</div>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="display = false">我知道了</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import { globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";

const props = defineProps<{
    current: string;
}>();

const display = ref(false);
function checkShowGuidance() {
    const key = "businessVoucherGuidance-" + getGlobalToken();
    if (!localStorage.getItem(key)) {
        display.value = true;
        const timer = setTimeout(() => {
            localStorage.setItem(key, "1");
            clearTimeout(timer);
        }, 1000);
    }
}
function showGuidance() {
    display.value = true;
}
defineExpose({ checkShowGuidance, showGuidance });

const linkInfo = {
    1: {
        permission: "asubrelationsettings-canview",
        route: "/Erp/AsubRelationSettings1",
        title: "科目关联设置",
    },
    2: {
        permission: "businessvouchertemplate-canview",
        route: "/Erp/BusinessVoucherTemplate",
        title: "业务凭证模板",
    },
    3: {
        permission: "businessvoucher-canview",
        route: "/Erp/BusinessVoucher",
        title: "业务生成凭证",
    },
};
function switchRouter(type: 1 | 2 | 3) {
    const link = linkInfo[type];
    if ("/Erp/" + props.current === link.route) {
        display.value = false;
        return;
    }
    if (!checkPermission([link.permission])) {
        ElNotify({ type: "warning", message: "亲，您没有这个功能权限哦~" });
        return;
    }
    globalWindowOpenPage(link.route, link.title);
}
</script>

<style lang="less" scoped>
.guidance-content {
    font-size: 14px;
    text-align: center;
    line-height: 20px;
    .guidance-main {
        padding: 24px 44px;
        .top {
            position: relative;
            .link {
                position: absolute;
                right: 0;
                top: 0;
                padding-right: 20px;
                background: url("@/assets/Erp/right-erp.png") no-repeat;
                background-size: 16px 16px;
                background-position: right center;
            }
        }
    }
    .guidance-title {
        font-weight: 600;
    }
    .center {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .item {
            width: 200px;
            height: 248px;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 2px;
            &:hover {
                border-color: var(--main-color);
            }
            .item-label {
                margin-top: 10px;
                margin-bottom: 18px;
            }
            img {
                width: 104px;
                height: 90px;
            }
            .link-item {
                display: inline-block;
                margin-top: 13px;
                margin-bottom: 4px;
            }
            .default {
                margin: 0 15px;
                text-align: left;
            }
        }
        .allow {
            display: inline-block;
            width: 18px;
            height: 18px;
            background: url("@/assets/Erp/right.png") no-repeat center;
            background-size: 100% 100%;
        }
    }
}
</style>
