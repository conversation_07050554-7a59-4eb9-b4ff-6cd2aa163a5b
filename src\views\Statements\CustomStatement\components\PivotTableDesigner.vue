<template>
    <div class="pivot-table-designer-view">
        <div class="title">数据透视表</div>
        <div class="main-content" v-loading="loading" element-loading-text="数据加载中...">
            <div class="main-top main-tool-bar space-between split-line" style="box-sizing: border-box">
                <div class="main-tool-left">
                    <div class="form-title required">报表编码：</div>
                    <div class="form-field">
                        <el-input v-model="pivotModel.statementCode" @input="handleCodeInput"></el-input>
                        />
                    </div>
                    <div class="form-title required ml-10">报表名称：</div>
                    <div class="form-field">
                        <Tooltip :content="pivotModel.statementName" :isInput="true">
                            <el-input v-model="pivotModel.statementName" @input="handleNameInput" />
                        </Tooltip>
                    </div>
                    <div class="form-title ml-10" v-if="isErp">备注：</div>
                    <div class="form-field" v-if="isErp">
                        <Tooltip :content="pivotModel.note" :isInput="true">
                            <el-input v-model="pivotModel.note" />
                        </Tooltip>
                    </div>
                    <div class="help-tip ml-20">
                        <img src="@/assets/Icons/help-book.png" alt="" />
                        <a
                            class="link"
                            @click="
                                globalWindowOpen(
                                    isErp
                                        ? 'https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=110137111'
                                        : 'https://help.ningmengyun.com/#/jz/handle?subMenuId=110119110'
                                )
                            "
                            >点此查看帮助手册</a
                        >
                    </div>
                </div>
                <div class="main-tool-right">
                    <el-checkbox v-model="customSetPeriod" v-show="valueArr.length > 1" label="自定义设置取值金额期间"></el-checkbox>

                    <a class="solid-button button ml-10" @click="savePivotModel()">保存</a>
                    <a class="button ml-10" @click="cancelPivotModel()">取消</a>
                </div>
            </div>
            <div class="main-center pivot-table-container">
                <div class="pivot-left-content">
                    <el-scrollbar>
                        <div class="list-title">取值维度</div>
                        <el-scrollbar class="scrollbar-pivot-left">
                            <div class="dimensions-list">
                                <div class="list-item-parent" v-for="item in dimensionList" :key="item.id">
                                    <div
                                        draggable="true"
                                        :class="[
                                            'list-item draggable',
                                            {
                                                'move-btn': isDragging && currentDragValue === String(item.value),
                                            },
                                        ]"
                                        @dragstart="handleDragStart($event, item)"
                                        @dragend="movingType = ''"
                                    >
                                        <Tooltip :content="item.name" :maxWidth="80" :lineClamp="1">
                                            <span>{{ item.type === "asub" ? "会计" + item.name : "辅助核算-" + item.name }}</span>
                                        </Tooltip>
                                    </div>
                                </div>
                            </div>
                        </el-scrollbar>

                        <div class="list-title">取值金额</div>
                        <div class="money-list">
                            <!-- <el-scrollbar> -->
                            <div class="list-item-parent" v-for="item in valueList" :key="item.id">
                                <div
                                    draggable="true"
                                    :class="[
                                        'list-item draggable',
                                        {
                                            'move-btn': isDragging && currentDragValue === item.value,
                                        },
                                    ]"
                                    @dragstart="handleDragStart($event, item)"
                                    @dragend="movingType = ''"
                                >
                                    {{ item.name }}
                                </div>
                            </div>

                            <!-- </el-scrollbar> -->
                        </div>
                    </el-scrollbar>
                </div>
                <div class="pivot-right-content">
                    <div class="tip">
                        将<span class="highlight-orange">左侧</span>的<span class="highlight-orange">取值维度</span>、<span
                            class="highlight-orange"
                            >取值金额</span
                        >字段拖动到<span class="highlight-orange">行、列、值</span>中，以生成报表样式
                    </div>
                    <div class="right-content-top-setup">
                        <div class="row-line-set">
                            <div class="line-name">会计期间：</div>
                            <div class="line-list">
                                <PeriodSelector
                                    ref="periodSelectorRef"
                                    :disable="customSetPeriod"
                                    v-model="valuePeriod"
                                    @update:modelValue="updateValuePeriod"
                                ></PeriodSelector>
                            </div>
                        </div>
                        <div class="row-line-set" @drop="handleDragRow" @dragover.prevent>
                            <div class="line-name">行：</div>
                            <div :class="['line-list', { 'move-ing': movingType === 'dimensions' }]">
                                <DropCascadeDown
                                    v-for="(item, index) in rowArr"
                                    :key="item.value"
                                    :btnType="item.type"
                                    :btnTxt="item.name"
                                    :btnValue="item.value"
                                    :detailedQuantity="item.dropDownOPtions.selectList.list.length"
                                    :draggable="!dropCascadeShow"
                                    v-model="item.dropDownOPtions"
                                    @dragstart="handleDragStart($event, item, index)"
                                    @dragover.prevent
                                    @drop.stop="changeDragRow($event, index)"
                                    @openSelectDialog="(type, btnTxt, btnValue) => openSelectDetailDialog('行', type, btnTxt, btnValue)"
                                    @removeBtn="removeButton('row', item, index)"
                                    @dropCascadeShow="changeDropCascadeShow"
                                    @dragend="movingType = ''"
                                >
                                </DropCascadeDown>
                                <div>
                                    <el-tooltip
                                        v-if="valueArr.length > 1 && !isValueInColumn"
                                        :visible="valueBtnTipShow"
                                        effect="light"
                                        placement="top"
                                        content="所选择的取值金额可在行、列之间拖动改变金额展示方向哦~"
                                    >
                                        <a
                                            class="button figure-btn"
                                            draggable="true"
                                            v-show="valueArr.length > 1 && !isValueInColumn"
                                            @dragstart="handleDragStart($event, 'valueBtn')"
                                            @dragover.prevent
                                            @mouseenter="valueBtnTipShow = true"
                                            @mouseleave="valueBtnTipShow = false"
                                        >
                                            数值
                                        </a>
                                    </el-tooltip>
                                </div>
                            </div>
                        </div>
                        <div class="row-line-set" @drop="handleDragColumn" @dragover.prevent>
                            <div class="line-name">列：</div>
                            <div :class="['line-list', { 'move-ing': movingType === 'dimensions' }]">
                                <DropCascadeDown
                                    v-for="(item, index) in colArr"
                                    :btnTxt="item.name"
                                    :key="item.value"
                                    :btnType="item.type"
                                    :btnValue="item.value"
                                    :detailedQuantity="item.dropDownOPtions.selectList.list.length"
                                    :draggable="!dropCascadeShow"
                                    v-model="item.dropDownOPtions"
                                    @openSelectDialog="(type, btnTxt, btnValue) => openSelectDetailDialog('列', type, btnTxt, btnValue)"
                                    @removeBtn="removeButton('col', item, index)"
                                    @dragstart="handleDragStart($event, item, index)"
                                    @dragover.prevent
                                    @drop.stop="changeDragColumn($event, index)"
                                    @dropCascadeShow="changeDropCascadeShow"
                                    @dragend="movingType = ''"
                                >
                                </DropCascadeDown>
                                <div>
                                    <el-tooltip
                                        v-if="valueArr.length > 1 && isValueInColumn"
                                        :visible="valueBtnTipShow"
                                        effect="light"
                                        placement="top"
                                        content="所选择的取值金额可在行、列之间拖动改变金额展示方向哦~"
                                    >
                                        <a
                                            class="button figure-btn"
                                            draggable="true"
                                            v-show="valueArr.length > 1 && isValueInColumn"
                                            @dragstart="handleDragStart($event, 'valueBtn')"
                                            @dragover.prevent
                                            @mouseenter="valueBtnTipShow = true"
                                            @mouseleave="valueBtnTipShow = false"
                                        >
                                            数值
                                        </a>
                                    </el-tooltip>
                                </div>
                            </div>
                        </div>
                        <div class="row-line-set" @drop="handleDragValue" @dragover.prevent>
                            <div class="line-name">值：</div>
                            <div :class="['line-list', { 'move-ing': movingType === 'money' }]">
                                <DropCascadeDown
                                    v-for="(item, index) in valueArr"
                                    :btnTxt="item.name"
                                    :key="item.value"
                                    :btnType="item.type"
                                    :btnValue="item.value"
                                    :draggable="!dropCascadeShow"
                                    :dropIconShow="customSetPeriod"
                                    v-model="item.dropDownOPtions"
                                    @dropCascadeShow="changeDropCascadeShow"
                                    @removeBtn="removeButton('value', item, index)"
                                    @dragstart="handleDragStart($event, item, index)"
                                    @dragover.prevent
                                    @drop.stop="changeDragValue($event, index)"
                                    @updateValueOptions="updateValueOptions($event, item)"
                                    @dragend="movingType = ''"
                                >
                                </DropCascadeDown>
                            </div>
                        </div>
                    </div>
                    <div class="preview-pivot-table">
                        <PreviewOfPivotTable :tableData="tableData" :Columns="Columns.slice(0, 10)"></PreviewOfPivotTable>
                    </div>
                </div>
            </div>
        </div>
        <SelectAsubjectDialog
            v-if="selectAsubsDialogShow"
            v-model="selectAsubsDialogShow"
            :position="currentAsubPosition"
            :selectList="dimensionList[0].dropDownOPtions.selectList.list"
            @addAsubSure="addAsubDetail"
        ></SelectAsubjectDialog>
        <SelectAccountDialog
            v-if="selectAccountDialogShow"
            v-model="selectAccountDialogShow"
            :selectList="currentAccountSelectList"
            :accountType="accountType"
            :position="currentAccountPosition"
            @addAccountSure="addAccountDetail"
        ></SelectAccountDialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRef, watch, nextTick, defineAsyncComponent } from "vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import DropCascadeDown from "./DropCascadeDown.vue";
import PreviewOfPivotTable from "./PreviewOfPivotTable.vue";
import { ElNotify } from "@/util/notify";
const SelectAsubjectDialog = defineAsyncComponent(() => import("./SelectAsubjectDialog.vue"));
const SelectAccountDialog = defineAsyncComponent(() => import("./SelectAccountDialog.vue"));
import type { IColumnProps } from "@/components/Table/IColumnProps";
import cloneDeep from "lodash/cloneDeep";
import debounce from "lodash/debounce";
import { AsubTypeEnum } from "@/views/Settings/AccountSubject/types";
import { request, type IResponseModel } from "@/util/service";
import { useLoading } from "@/hooks/useLoading";
import type { ITableDataItem } from "@/views/Settings/AccountSubject/types";
import $bus from "@/bus";
import Tooltip from "@/components/Tooltip/index.vue";
import PeriodSelector from "./PeriodSelector.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModelWithChecked } from "@/api/accountSubject";
import { globalWindowOpen } from "@/util/url";
import type { IAssistingAccountWithChecked } from "@/api/assistingAccounting";

import type {
    IDimensionItem,
    IDropDownOPtions,
    IValueListItem,
    IPivotModel,
    IPivotTableDetail,
    IPivotTableDetailRowColumn,
    IPivotTableDetailValues,
    IValuePeriod,
} from "../types";
import { AasObj, ValueObj, IValueDropDownOPtionsClass, IDimensionDropDownOPtionsClass } from "../types";
import type { IAssistingAccountType, IAssistingAccount } from "@/api/assistingAccounting";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
const periodStore = useAccountPeriodStore();

const valueBtnTipShow = ref(false);
const aaTypeList = toRef(useAssistingAccountingStore(), "assistingAccountingTypeList");
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingListAll");
const accountSubjectStore = useAccountSubjectStore();
const accountSubject = toRef(accountSubjectStore, "accountSubjectList");

const selectAsubsDialogShow = ref(false);
const emits = defineEmits(["submitSuccess", "goMain"]);
const isErp = ref(window.isErp);
const selectAccountDialogShow = ref(false);
const fieldsEnum: { [key: string]: number } = {
    asubName: 1,
    asubCode: 2,
    asubType: 4,
    direction: 8,
    name: 1,
    code: 2,
};

let valuePeriod = ref<IValuePeriod>({
    valuePeriodType: 1,
    startValuePeriod: periodStore.period.endPid,
    endValuePeriod: periodStore.period.endPid,
});
let watchNoChange = ref(false);
let periodList = toRef(periodStore, "periodList");
const updateValuePeriod = (updateData: IValuePeriod) => {
    valuePeriod.value = updateData;
};
const updateValueOptions = (e: IValuePeriod, item: IValueListItem) => {
    item.dropDownOPtions = cloneDeep(e);
};
const dimensionList = ref<IDimensionItem[]>([
    {
        id: 1,
        type: "asub",
        name: "科目",
        value: 1,
        dropDownOPtions: reactive({
            selectList: {
                value: false,
                label: "选择科目",
                list: [],
            },
            fields: {
                label: "显示字段",
                children: [
                    {
                        value: "asubName",
                        label: "科目名称",
                        required: true,
                    },
                    {
                        value: "asubCode",
                        label: "科目编码",
                        required: false,
                    },
                    {
                        value: "asubType",
                        label: "科目类型",
                        required: false,
                    },
                    {
                        value: "direction",
                        label: "借贷方向",
                        required: false,
                    },
                ],
                checked: ["asubName"],
            },
            enable: {
                value: false,
                label: "启用筛选",
            },
        }),
    },
]);
const accountType = ref<{ aaname: string; aatype: number | string }>({
    aaname: "客户",
    aatype: 10001,
});
const customSetPeriod = ref(false);
watch(
    aaTypeList,
    () => {
        dimensionList.value = dimensionList.value.splice(0, 1);
        aaTypeList.value.forEach((v: IAssistingAccountType) => {
            if (v.aaTypeName !== "现金流") {
                dimensionList.value.push({
                    id: dimensionList.value.length + 1,
                    type: "auxi",
                    name: v.aaTypeName,
                    value: v.aaType,
                    dropDownOPtions: reactive({
                        selectList: {
                            value: false,
                            label: "选择" + v.aaTypeName,
                            list: [],
                        },
                        fields: {
                            label: "显示字段",
                            children: [
                                {
                                    value: "name",
                                    label: v.aaTypeName + "名称",
                                    required: true,
                                },
                                {
                                    value: "code",
                                    label: v.aaTypeName + "编码",
                                    required: false,
                                },
                            ],
                            checked: ["name"],
                        },
                        enable: {
                            value: false,
                            label: "启用筛选",
                        },
                    }),
                });
            }
        });
    },
    { immediate: true }
);
const valueListOrigin = ref<IValueListItem[]>([
    {
        id: 1,
        name: "期初余额",
        type: "money",
        value: "qcye",
        dropDownOPtions: reactive(new IValueDropDownOPtionsClass()),
    },
    {
        id: 2,
        name: "期末余额",
        type: "money",
        value: "qmye",
        dropDownOPtions: reactive(new IValueDropDownOPtionsClass()),
    },
    {
        id: 3,
        name: "发生额",
        type: "money",
        value: "fse",
        dropDownOPtions: reactive(new IValueDropDownOPtionsClass()),
    },
    {
        id: 4,
        name: "借方发生额",
        type: "money",
        value: "jffse",
        dropDownOPtions: reactive(new IValueDropDownOPtionsClass()),
    },
    {
        id: 5,
        name: "贷方发生额",
        type: "money",
        value: "dffse",
        dropDownOPtions: reactive(new IValueDropDownOPtionsClass()),
    },
]);
let pivotModel = reactive<IPivotModel>({
    statementId: 0,
    statementCode: "",
    statementName: "",
    note: "",
});
const periodSelectorRef = ref();
const valueList = ref(cloneDeep(valueListOrigin.value));
let rowArr = ref<IDimensionItem[]>([]);
let colArr = ref<IDimensionItem[]>([]);
let valueArr = ref<IValueListItem[]>([]);
const isValueInColumn = ref(true);
type DragItemType = IDimensionItem | IValueListItem | string;
let currentDragTarget = ref<DragItemType>();
const isDragging = ref(false);
const currentDragValue = ref<string>("1");
const movingType = ref<string>("");
function handleDragStart(event: DragEvent, value: DragItemType, index?: number) {
    currentDragTarget.value = value;
    if (typeof value !== "string") {
        currentDragValue.value = String(value.value);
        movingType.value = value.type === "money" ? "money" : "dimensions";
    }

    valueBtnTipShow.value && (valueBtnTipShow.value = false);
    isDragging.value = true;
    let timer = setTimeout(() => {
        isDragging.value = false;
        clearTimeout(timer);
    }, 0);
}

let currentAccountSelectList = ref<IAssistingAccount[] | ITableDataItem[] | []>([]);
let currentAccountPosition = ref("行");
let currentAsubPosition = ref("行");
function openSelectDetailDialog(position: "行" | "列", type?: string, btnTxt?: string, btnValue?: number) {
    if (type) {
        if (type === "asub") {
            selectAsubsDialogShow.value = true;
            currentAsubPosition.value = position;
        } else if (type === "auxi") {
            accountType.value.aaname = btnTxt as string;
            accountType.value.aatype = btnValue as number;
            nextTick(() => {
                currentAccountSelectList.value = dimensionList.value.find((v) => v.value === btnValue)?.dropDownOPtions.selectList.list as
                    | IAssistingAccount[]
                    | ITableDataItem[]
                    | [];
                selectAccountDialogShow.value = true;
                currentAccountPosition.value = position;
            });
        }
        return;
    }
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
        selectAsubsDialogShow.value = true;
        currentAsubPosition.value = position;
    } else if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "auxi") {
        accountType.value.aaname = (currentDragTarget.value as IDimensionItem | IValueListItem).name;
        accountType.value.aatype = (currentDragTarget.value as IDimensionItem | IValueListItem).value;
        nextTick(() => {
            selectAccountDialogShow.value = true;
            currentAccountSelectList.value = [];
            currentAccountPosition.value = position;
        });
    }
}
function handleDragRow(event: DragEvent) {
    event.preventDefault();
    if (currentDragTarget.value === "valueBtn") {
        isValueInColumn.value = false;
        return;
    }
    if (
        rowArr.value.length === 8 &&
        rowArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id) === -1
    ) {
        ElNotify({
            type: "warning",
            message: "行维度最多只能设置八个字段。",
        });
        return;
    }
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type !== "money") {
        let rowIndex = rowArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id);
        let colIndex = colArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id);
        if (rowIndex === -1 && colIndex === -1) {
            if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
                rowArr.value.unshift(currentDragTarget.value as any);
                changeAsubTypeRequired("row");
            } else {
                rowArr.value.push(currentDragTarget.value as any);
            }
            openSelectDetailDialog("行");
        } else if (rowIndex === -1 && colIndex > -1) {
            const element = colArr.value.splice(colIndex, 1)[0];
            if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
                rowArr.value.unshift(element);
                changeAsubTypeRequired("row");
            } else {
                rowArr.value.push(element);
            }
        }
    } else {
        ElNotify({
            type: "warning",
            message: "金额字段只能放在值上，不可拖动。",
        });
    }
}
function changeDragRow(event: DragEvent, index: number) {
    event.preventDefault();
    if (currentDragTarget.value === "valueBtn") {
        isValueInColumn.value = false;
        return;
    }
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type !== "money") {
        let rowIndex = rowArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id);
        let colIndex = colArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id);
        if (rowIndex > -1) {
            if ((currentDragTarget.value as IDimensionItem)?.type !== "asub") {
                const element = rowArr.value.splice(rowIndex, 1)[0];
                rowArr.value.splice(index, 0, element);
            }
        } else if (colIndex > -1) {
            if (rowArr.value.length === 8) {
                ElNotify({
                    type: "warning",
                    message: "行维度最多只能设置八个字段。",
                });
                return;
            }
            const element = colArr.value.splice(colIndex, 1)[0];
            rowArr.value.splice(element.type === "asub" ? 0 : index, 0, element);
        } else {
            if (rowArr.value.length === 8) {
                ElNotify({
                    type: "warning",
                    message: "行维度最多只能设置八个字段。",
                });
                return;
            }
            rowArr.value.splice(
                (currentDragTarget.value as IDimensionItem)?.type !== "asub" ? index : 0,
                0,
                currentDragTarget.value as IDimensionItem
            );
            openSelectDetailDialog("行");
        }
        if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
            changeAsubTypeRequired("row");
        }
    } else {
        ElNotify({
            type: "warning",
            message: "金额字段只能放在值上，不可拖动。",
        });
    }
}
function handleDragColumn(event: DragEvent) {
    event.preventDefault();
    if (currentDragTarget.value === "valueBtn") {
        isValueInColumn.value = true;
        return;
    }

    if (
        colArr.value.length === 3 &&
        colArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id) === -1
    ) {
        ElNotify({
            type: "warning",
            message: "列维度最多只能设置三个字段。",
        });
        return;
    }
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type !== "money") {
        let rowIndex = rowArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id);
        let colIndex = colArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem)?.id);
        if (rowIndex === -1 && colIndex === -1) {
            if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
                colArr.value.unshift(currentDragTarget.value as any);
                changeAsubTypeRequired("col");
            } else {
                colArr.value.push(currentDragTarget.value as any);
            }
            openSelectDetailDialog("列");
        } else if (rowIndex > -1 && colIndex === -1) {
            const element = rowArr.value.splice(rowIndex, 1)[0];
            if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
                colArr.value.unshift(element);
                changeAsubTypeRequired("col");
            } else {
                colArr.value.push(element);
            }
        }
    } else {
        ElNotify({
            type: "warning",
            message: "金额字段只能放在值上，不可拖动。",
        });
    }
}
function changeAsubTypeRequired(dimension: "row" | "col") {
    if (dimension === "col") {
        dimensionList.value[0].dropDownOPtions.fields.children[2].required = true;
        dimensionList.value[0].dropDownOPtions.fields.children[3].required = true;
        let checked = dimensionList.value[0].dropDownOPtions.fields.checked;
        let indexType = checked.indexOf("asubType");
        if (indexType > -1) {
            checked.splice(indexType, 1);
        }
        let indexDire = checked.indexOf("direction");
        if (indexDire > -1) {
            checked.splice(indexDire, 1);
        }
    } else {
        dimensionList.value[0].dropDownOPtions.fields.children[2].required = false;
        dimensionList.value[0].dropDownOPtions.fields.children[3].required = false;
    }
}
function changeDragColumn(event: DragEvent, index: number) {
    event.preventDefault();
    if (currentDragTarget.value === "valueBtn") {
        isValueInColumn.value = true;
        return;
    }
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type !== "money") {
        let rowIndex = rowArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem).id);
        let colIndex = colArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem).id);
        if (rowIndex > -1) {
            if (colArr.value.length === 3) {
                ElNotify({
                    type: "warning",
                    message: "列维度最多只能设置三个字段。",
                });
                return;
            }
            const element = rowArr.value.splice(rowIndex, 1)[0];
            colArr.value.splice(element.type === "asub" ? 0 : index, 0, element);
        } else if (colIndex > -1) {
            const element = colArr.value.splice(colIndex, 1)[0];
            colArr.value.splice(element.type === "asub" ? 0 : index, 0, element);
        } else {
            if (colArr.value.length === 3) {
                ElNotify({
                    type: "warning",
                    message: "列维度最多只能设置三个字段。",
                });
                return;
            }
            colArr.value.splice(
                (currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub" ? 0 : index,
                0,
                currentDragTarget.value as IDimensionItem
            );
            openSelectDetailDialog("列");
        }
        if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "asub") {
            changeAsubTypeRequired("col");
        }
    } else {
        ElNotify({
            type: "warning",
            message: "金额字段只能放在值上，不可拖动。",
        });
    }
}
function handleDragValue(event: DragEvent, index?: number) {
    event.preventDefault();
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "money") {
        let isExistTarget = valueArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem).id);
        if (isExistTarget === -1) {
            (currentDragTarget.value as IValueListItem).dropDownOPtions = valuePeriod.value;
            valueArr.value.push(currentDragTarget.value as IValueListItem);
        } else {
            const element = valueArr.value.splice(isExistTarget, 1)[0];
            valueArr.value.push(element);
        }
    } else {
        ElNotify({
            type: "warning",
            message: "维度字段不能放在值上哦！",
        });
    }
}
function changeDragValue(event: DragEvent, index: number) {
    event.preventDefault();
    if ((currentDragTarget.value as IDimensionItem | IValueListItem).type === "money") {
        let isExistIndex = valueArr.value.findIndex((v) => v.id === (currentDragTarget.value as IDimensionItem | IValueListItem).id);
        if (isExistIndex > -1) {
            const element = valueArr.value.splice(isExistIndex, 1)[0];
            valueArr.value.splice(index, 0, element);
        } else {
            valueArr.value.splice(index, 0, currentDragTarget.value as IValueListItem);
        }
    } else {
        ElNotify({
            type: "warning",
            message: "维度字段不能放在值上哦！",
        });
    }
}
function addAsubDetail(data: IAccountSubjectModelWithChecked[]) {
    dimensionList.value[0].dropDownOPtions.selectList.list = data;
}
function addAccountDetail(data: IAssistingAccount[], account: { aaname: string; aatype: number | string }) {
    (dimensionList.value.find((v) => v.value === account.aatype) as IDimensionItem).dropDownOPtions.selectList.list = data;
}
function cancelPivotModel() {
    emits("goMain");
    resetPivotModel();
}
function resetPivotModel() {
    watchNoChange.value = true;
    pivotModel.statementId = 0;
    pivotModel.statementName = "";
    pivotModel.statementCode = "";
    pivotModel.note = "";
    rowArr.value = [];
    colArr.value = [];
    valueArr.value = [];
    tableData.value = [];
    Columns.value = [];
    dimensionList.value.forEach((v) => {
        v.dropDownOPtions = new IDimensionDropDownOPtionsClass(v);
    });
    valueList.value = cloneDeep(valueListOrigin.value);
    customSetPeriod.value = false;
    valuePeriod.value = {
        valuePeriodType: 1,
        startValuePeriod: periodStore.period.endPid,
        endValuePeriod: periodStore.period.endPid,
    };
}
watch(
    valuePeriod,
    (v) => {
        periodSelectorRef.value.changeValuePeriod(v);
    },
    { deep: true }
);
function getStatementCode() {
    if (!pivotModel.statementCode) {
        request({
            url: "/api/CustomStatement/GetNextStatementCode",
            method: "post",
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                pivotModel.statementCode = res.data;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg || "获取编码失败",
                });
            }
        });
    }
}

function savePivotModel() {
    if (!pivotModel.statementCode) {
        ElNotify({
            type: "warning",
            message: "请填写报表编码！",
        });
        return false;
    }
    if (pivotModel.statementCode.length > 40) {
        ElNotify({
            type: "warning",
            message: "报表编码长度不能超过40个字符！",
        });
        return false;
    }
    if (!pivotModel.statementName) {
        ElNotify({
            type: "warning",
            message: "请填写报表名称！",
        });
        return false;
    }
    if (pivotModel.statementName.length > 60) {
        ElNotify({
            type: "warning",
            message: "报表名称长度不能超过60个字符！",
        });
        return false;
    }
    if (pivotModel.note && pivotModel.note.length > 255) {
        ElNotify({
            type: "warning",
            message: "备注长度不能超过255个字符！",
        });
        return false;
    }
    if (!rowArr.value.length && !colArr.value.length) {
        ElNotify({
            type: "warning",
            message: "请设置行或列字段。",
        });
        return false;
    } else {
        if (rowArr.value.length) {
            for (let i = 0; i < rowArr.value.length; i++) {
                let current = rowArr.value[i];
                if (!current.dropDownOPtions.selectList.list.length) {
                    ElNotify({
                        type: "warning",
                        message:
                            current.type === "asub"
                                ? `行维度科目明细不能为空，请选择科目。`
                                : `行维度${current.name}辅助核算项目不能为空，请选择${current.name}`,
                    });
                    return false;
                }
            }
        }
        if (colArr.value.length) {
            for (let i = 0; i < colArr.value.length; i++) {
                let current = colArr.value[i];
                if (!current.dropDownOPtions.selectList.list.length) {
                    ElNotify({
                        type: "warning",
                        message:
                            current.type === "asub"
                                ? `列维度科目明细不能为空，请选择科目。`
                                : `列维度${current.name}辅助核算项目不能为空，请选择${current.name}`,
                    });
                    return false;
                }
            }
        }
    }
    if (!valueArr.value.length) {
        ElNotify({
            type: "warning",
            message: "请设置金额字段。",
        });
        return false;
    }
    let optionList = interfaceFormat();
    let valueOptionList = optionList.values;
    if (
        customSetPeriod.value &&
        valueOptionList.every(
            (obj) =>
                obj.valuePeriodType === valueOptionList[0].valuePeriodType &&
                obj.startValuePeriod === valueOptionList[0].startValuePeriod &&
                obj.endValuePeriod === valueOptionList[0].endValuePeriod
        )
    ) {
        customSetPeriod.value = false;
        valuePeriod.value = {
            valuePeriodType: valueOptionList[0].valuePeriodType,
            startValuePeriod: valueOptionList[0].startValuePeriod,
            endValuePeriod: valueOptionList[0].endValuePeriod,
        };
    }
    let data = {
        statementId: pivotModel.statementId || 0,
        statementCode: pivotModel.statementCode,
        statementName: pivotModel.statementName,
        note: pivotModel.note,
        valueShow: valueArr.value.length < 2 ? 0 : isValueInColumn.value ? 2 : 1,
        subject: optionList.subject,
        aas: optionList.aas,
        values: valueOptionList,
        isCustomPeriod: customSetPeriod.value ? 1 : 0,
        ...valuePeriod.value,
    };

    useLoading().enterLoading("数据加载中...");
    request({
        url: "/api/CustomStatement/PivotTable",
        method: pivotModel.statementId ? "put" : "post",
        data,
    })
        .then((res: IResponseModel<boolean>) => {
            useLoading().quitLoading();
            if (res.data) {
                ElNotify({
                    type: "success",
                    message: "保存成功！",
                });
                emits("submitSuccess");
                resetPivotModel();
            } else {
                if (res.subState === 1) {
                    ElNotify({
                        type: "warning",
                        message: "报表编码重复，请重新输入。",
                    });
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg || "保存失败。",
                    });
                }
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({
                type: "warning",
                message: "保存失败，请刷新后重试~",
            });
        });
}

function interfaceFormat() {
    //1 科目，2 辅助核算，3 金额
    let place = {
        row: 1,
        col: 2,
        value: 3,
    };
    let subject = {};
    let aas = [];
    for (let i = 0; i < rowArr.value.length; i++) {
        let current = rowArr.value[i];
        if (current.id === 1) {
            subject = {
                optionId: 0,
                optionType: 1,
                optionPlace: place["row"],
                isQuery: current.dropDownOPtions.enable.value,
                showFields: current.dropDownOPtions.fields.checked.map((v) => fieldsEnum[v]),
                asubIds: current.dropDownOPtions.selectList.list.map((v: any) => Number(v.asubId)),
            };
        } else {
            let obj = new AasObj();
            obj.optionId = 0;
            obj.optionType = 2;
            obj.optionPlace = place["row"];
            obj.isQuery = current.dropDownOPtions.enable.value;
            obj.showFields = current.dropDownOPtions.fields.checked.map((v: string) => fieldsEnum[v]);
            obj.AAEIds = current.dropDownOPtions.selectList.list.map((v: any) => Number(v.aaeid));
            obj.AATypeId = Number(current.value);
            aas.push(obj);
        }
    }
    for (let i = 0; i < colArr.value.length; i++) {
        let current = colArr.value[i];
        if (current.id === 1) {
            subject = {
                optionId: 0,
                optionType: 1,
                optionPlace: place["col"],
                isQuery: current.dropDownOPtions.enable.value,
                showFields: current.dropDownOPtions.fields.checked.map((v) => fieldsEnum[v]),
                asubIds: current.dropDownOPtions.selectList.list.map((v: any) => Number(v.asubId)),
            };
        } else {
            let obj = new AasObj();
            obj.optionId = 0;
            obj.optionType = 2;
            obj.optionPlace = place["col"];
            obj.isQuery = current.dropDownOPtions.enable.value;
            obj.showFields = current.dropDownOPtions.fields.checked.map((v: string) => fieldsEnum[v]);
            obj.AAEIds = current.dropDownOPtions.selectList.list.map((v: any) => v.aaeid);
            obj.AATypeId = Number(current.value);
            aas.push(obj);
        }
    }
    let values = [];
    for (let i = 0; i < valueArr.value.length; i++) {
        let current = valueArr.value[i];
        let dropDownOPtions = current.dropDownOPtions;
        let obj = new ValueObj();
        obj.optionId = 0;
        obj.optionType = 3;
        obj.optionPlace = place["value"];
        obj.valueType = current.id;
        obj.valuePeriodType = dropDownOPtions.valuePeriodType;
        obj.startValuePeriod = dropDownOPtions.startValuePeriod;
        obj.endValuePeriod = dropDownOPtions.endValuePeriod;
        values.push(obj);
    }
    return { subject, aas, values };
}
function removeButton(direction: string, item: IDimensionItem | IValueListItem, index: number) {
    if (direction === "row") {
        item.dropDownOPtions = new IDimensionDropDownOPtionsClass(item as IDimensionItem);
        rowArr.value.splice(index, 1);
    } else if (direction === "col") {
        item.dropDownOPtions = new IDimensionDropDownOPtionsClass(item as IDimensionItem);
        colArr.value.splice(index, 1);
    } else {
        item.dropDownOPtions = cloneDeep(new IValueDropDownOPtionsClass());
        valueArr.value.splice(index, 1);
        if (valueArr.value.length === 1 && customSetPeriod.value) {
            customSetPeriod.value = false;
            periodSelectorRef.value.changeValuePeriod(valueArr.value[0].dropDownOPtions);
        }
    }
}
const Columns = ref<IColumnProps[]>([]);
const tableData = ref<any[]>([]);
const loading = ref(false);
function setInitialData(type: string) {
    watchNoChange.value = false;
    if (type === "edit") {
        loading.value = true;
    } else {
        getStatementCode();
    }
}
const auxiFieldsEnum: { [key: number]: string } = {
    1: "name",
    2: "code",
};
const asubFieldsEnum: { [key: number]: string } = {
    1: "asubName",
    2: "asubCode",
    4: "asubType",
    8: "direction",
};
let dropCascadeShow = ref(false);
function changeDropCascadeShow(show: boolean) {
    dropCascadeShow.value = show;
}
watch(customSetPeriod, (newVal) => {
    if (
        !newVal &&
        valueArr.value.some(
            (v) =>
                v.dropDownOPtions.valuePeriodType !== valuePeriod.value.valuePeriodType ||
                v.dropDownOPtions.startValuePeriod !== valuePeriod.value.startValuePeriod ||
                v.dropDownOPtions.endValuePeriod !== valuePeriod.value.endValuePeriod
        )
    ) {
        valueArr.value.forEach((v) => {
            v.dropDownOPtions = new IValueDropDownOPtionsClass(valuePeriod.value);
        });
    }
});
const debouncedReloadData = debounce(reloadData, 1000);
function reloadData() {
    let asubLocationR = rowArr.value.findIndex((v) => v.type === "asub");
    let asubLocationC = colArr.value.findIndex((v) => v.type === "asub");
    if (asubLocationR > 0) {
        moveElementToStart(rowArr.value, asubLocationR);
    } else if (asubLocationC > 0) {
        moveElementToStart(colArr.value, asubLocationC);
    }
    getPreviewTableData();
}
watch(
    [rowArr, colArr, valueArr, isValueInColumn, dropCascadeShow, valuePeriod],
    () => {
        if (selectAccountDialogShow.value || selectAsubsDialogShow.value || dropCascadeShow.value || watchNoChange.value) return;
        debouncedReloadData();
    },
    { immediate: true, deep: true }
);
$bus.on("setPivotTable", (data: unknown): void => {
    const pivotData = data as IPivotTableDetail;
    pivotModel.statementId = pivotData.statementId;
    pivotModel.statementCode = pivotData.statementCode;
    pivotModel.statementName = pivotData.statementName;
    pivotModel.note = pivotData.note;
    valuePeriod.value = {
        valuePeriodType: pivotData.valuePeriodType,
        startValuePeriod: pivotData.startValuePeriod,
        endValuePeriod: pivotData.endValuePeriod,
    };
    customSetPeriod.value = pivotData.isCustomPeriod;
    rowArr.value = conversionConditions(pivotData.rows);
    colArr.value = conversionConditions(pivotData.columns);
    valueArr.value = conversionConditions(pivotData.values);

    isValueInColumn.value = pivotData.valueShow !== 1;
    loading.value = false;
});
function conversionConditions(arr: IPivotTableDetailRowColumn[] | IPivotTableDetailValues[]) {
    let res: any[] = [];
    arr.forEach((v: IPivotTableDetailRowColumn | IPivotTableDetailValues) => {
        let target = {} as IDimensionItem | IValueListItem;
        if (v.optionType === 2) {
            let pivotItem = v as IPivotTableDetailRowColumn;
            target = dimensionList.value.find((w) => Number(w.value) === pivotItem.aatypeId) as IDimensionItem;
            target.type = "auxi";
            let dropDownOPtions = target.dropDownOPtions as IDropDownOPtions;
            dropDownOPtions.enable.value = pivotItem.isQuery;
            dropDownOPtions.fields.checked = pivotItem.showFields.map((e: number) => auxiFieldsEnum[e]);
            dropDownOPtions.selectList.list = assistingAccountingList.value.filter((a) =>
                pivotItem.aaeids?.includes(a.aaeid)
            ) as IAssistingAccountWithChecked[];
            if (dropDownOPtions.selectList.list[0].aaeid < 0) {
                dropDownOPtions.selectList.list.push(dropDownOPtions.selectList.list.shift() as IAssistingAccountWithChecked);
            }
        } else if (v.optionType === 1) {
            target = dimensionList.value.find((w) => w.value === 1) as IDimensionItem;
            target.type = "asub";
            let pivotItem = v as IPivotTableDetailRowColumn;
            let dropDownOPtions = target.dropDownOPtions as IDropDownOPtions;
            dropDownOPtions.enable.value = pivotItem.isQuery;
            dropDownOPtions.fields.checked = pivotItem.showFields.map((e: number) => asubFieldsEnum[e]);
            dropDownOPtions.selectList.list = accountSubject.value.filter((a) => pivotItem.asubIds?.includes(a.asubId));
            changeAsubTypeRequired(v.optionPlace === 1 ? "row" : "col");
        } else {
            let pivotItem = v as IPivotTableDetailValues;
            target = valueList.value.find((w) => w.id === pivotItem.valueType) as IValueListItem;
            target.type = "money";
            nextTick(() => {
                target.dropDownOPtions = new IValueDropDownOPtionsClass({
                    startValuePeriod: pivotItem.startValuePeriod,
                    endValuePeriod: pivotItem.endValuePeriod,
                    valuePeriodType: pivotItem.valuePeriodType,
                });
            });
        }
        res.push(target);
    });
    return res;
}
function calculateArrayProduct(arr: IDimensionItem[]) {
    if (!Array.isArray(arr) || arr.length === 0) {
        return 0;
    }

    let product = 1;
    for (let obj of arr) {
        if (!obj.dropDownOPtions.selectList.list || !obj.dropDownOPtions.selectList.list.length) {
            continue;
        }
        if (obj.dropDownOPtions.selectList.list && Array.isArray(obj.dropDownOPtions.selectList.list)) {
            product *= obj.dropDownOPtions.selectList.list.length;
        }
    }

    return product;
}
function moveElementToStart(arr: IDimensionItem[], index: number) {
    if (index < 0 || index >= arr.length) {
        return;
    }
    const element = arr.splice(index, 1)[0];
    arr.unshift(element);
}
function getPreviewTableData() {
    Columns.value = [];
    tableData.value = [];
    let colColumns = buildColumns(0);
    Columns.value = rowsGenerateColumns().concat(colColumns);
    buildRows(0, {});
    if (!isValueInColumn.value) {
        if (!colArr.value.length) {
            Columns.value.splice(rowsGenerateColumns().length + 1, 0, {
                label: " ",
                prop: "horizontalValue",
                minWidth: 140,
                align: "left",
                headerAlign: "center",
                resizable: false,
            });
        }
        if (!rowArr.value.length) {
            tableData.value = generateValuesInRowData();
        } else {
            let originData = cloneDeep(tableData.value);
            tableData.value = [];

            originData.forEach((f) => {
                generateValuesInRowData().forEach((s) => {
                    let newData = { ...f, ...s };
                    tableData.value.push(newData);
                });
            });
        }
    }
}

function rowsGenerateColumns(): IColumnProps[] {
    let rowsGenerateColumns: IColumnProps[] = [];
    rowArr.value.forEach((v, i) => {
        if (v.dropDownOPtions.selectList.list.length) {
            if (v.type === "asub") {
                let showMore = v.dropDownOPtions.fields.checked;
                rowsGenerateColumns.push({
                    label: "科目名称",
                    prop: "asubName",
                    minWidth: 80,
                    align: "left",
                    headerAlign: "center",
                    resizable: false,
                    className: "set-min-col-width",
                    formatter: (row, column) => {
                        let value = row[column.property];
                        return value ? (value.includes("^") ? value.substring(0, value.lastIndexOf("^")) : value) : "";
                    },
                });
                if (showMore.includes("asubCode")) {
                    rowsGenerateColumns.push({
                        label: "科目编码",
                        prop: "asubCode",
                        minWidth: 80,
                        align: "left",
                        headerAlign: "center",
                        resizable: false,
                        className: "set-min-col-width",
                    });
                }
                if (showMore.includes("asubType")) {
                    rowsGenerateColumns.push({
                        label: "科目类型",
                        prop: "asubType",
                        minWidth: 80,
                        align: "left",
                        headerAlign: "center",
                        resizable: false,
                        formatter: (row: any) => {
                            return AsubTypeEnum[row.asubType];
                        },
                        className: "set-min-col-width",
                    });
                }
                if (showMore.includes("direction")) {
                    rowsGenerateColumns.push({
                        label: "借贷方向",
                        prop: "direction",
                        minWidth: 80,
                        align: "left",
                        headerAlign: "center",
                        resizable: false,
                        formatter: (row: any) => {
                            if (row.direction == 1) {
                                return "借";
                            } else if (row.direction == 2) {
                                return "贷";
                            } else {
                                return "未知";
                            }
                        },
                        className: "set-min-col-width",
                    });
                }
            } else {
                let showMore = v.dropDownOPtions.fields.checked;

                rowsGenerateColumns.push({
                    label: v.name + "名称",
                    prop: "auxi" + "_" + v.value + "_" + "aaname",
                    minWidth: 80,
                    align: "left",
                    headerAlign: "center",
                    resizable: false,
                    formatter: (row, column) => {
                        let value = row[column.property];
                        return value ? (value.includes("^") ? value.substring(0, value.lastIndexOf("^")) : value) : "";
                    },
                    className: "set-min-col-width",
                });
                if (showMore.includes("code")) {
                    rowsGenerateColumns.push({
                        label: v.name + "编码",
                        prop: "auxi" + "_" + v.value + "_" + "aanum",
                        minWidth: 80,
                        align: "left",
                        headerAlign: "center",
                        resizable: false,
                        className: "set-min-col-width",
                    });
                }
            }
        }
    });
    // 数值在行上
    if (!isValueInColumn.value) {
        rowsGenerateColumns.push(
            {
                label: "取值类型",
                prop: "valueColValue",
                minWidth: 100,
                align: "left",
                headerAlign: "center",
                resizable: false,
            },
            {
                label: "日期",
                prop: "valueDate",
                minWidth: 100,
                align: "left",
                headerAlign: "center",
                resizable: false,
            }
        );
    } else {
        if (colArr.value.length && valueArr.value.length && !rowArr.value.length) {
            rowsGenerateColumns.push({
                label: "取值类型",
                prop: "valueColValue",
                minWidth: 100,
                align: "left",
                headerAlign: "center",
                resizable: false,
                children: [
                    {
                        label: "日期",
                        prop: "valueDate",
                        minWidth: 100,
                        align: "left",
                        headerAlign: "center",
                        resizable: false,
                    },
                ],
            });
        }
    }
    let availableData = colArr.value.filter((v) => v.dropDownOPtions.selectList.list.length);
    if (availableData.length) {
        let temp: IColumnProps = {};
        for (let i = availableData.length - 1; i >= 0; i--) {
            const col = colArr.value[i];
            let columnChild: IColumnProps = {
                label: col.name + "名称",
                prop: "",
                minWidth: 80,
                align: "left",
                headerAlign: "center",
                resizable: false,
                className: "set-min-col-width",
            };
            if (i !== availableData.length - 1) {
                columnChild.children = [temp];
                temp = columnChild;
            } else {
                columnChild.children = rowsGenerateColumns;
                temp = columnChild;
            }
        }
        return [temp];
    }

    return rowsGenerateColumns;
}
function buildRows(index: number, item: any) {
    let filterRowArr = rowArr.value.filter((v) => v.dropDownOPtions.selectList.list.length);
    if (index >= filterRowArr.length) {
        tableData.value.push(item);
        return;
    }
    const currentRow = filterRowArr[index];
    const selectList = currentRow.dropDownOPtions.selectList.list;
    let sliceLast = 100;
    if (index < filterRowArr.length - 1) {
        sliceLast = Math.ceil(100 / filterRowArr[index + 1].dropDownOPtions.selectList.list.length);
    } else {
        sliceLast = Math.min(100, selectList.length);
    }
    for (let i = 0; i < Math.min(sliceLast, selectList.length); i++) {
        let value = selectList[i];

        const newItem = { ...item };
        findKey(currentRow).forEach((key: string) => {
            if (findValue(key) === "aaname") {
                newItem[key] = (value as any)[findValue(key)] + "^" + (value as IAssistingAccount).aaeid;
            } else if (findValue(key) === "asubName") {
                newItem[key] = (value as any)[findValue(key)] + "^" + (value as any).asubId;
            } else {
                newItem[key] = (value as any)[findValue(key)];
            }
        });
        buildRows(index + 1, newItem);
    }
}

function findKey(v: IDimensionItem): string[] {
    if (v.type === "asub") {
        return v.dropDownOPtions.fields.checked;
    } else {
        let keyArr = ["auxi" + "_" + v.value + "_" + "aaname"];
        let showMore = v.dropDownOPtions.fields.checked;
        if (showMore.includes("code")) {
            keyArr.push("auxi" + "_" + v.value + "_" + "aanum");
        }
        if (showMore.includes("type")) {
            keyArr.push("auxi" + "_" + v.value + "_" + "aatype");
        }
        return keyArr;
    }
}
function findValue(v: string) {
    const parts = v.split("_");
    const lastPart = parts[parts.length - 1];
    return lastPart;
}
function buildColumns(index: number) {
    let filterColArr = colArr.value.filter((v) => v.dropDownOPtions.selectList.list.length);
    if (index >= filterColArr.length) {
        if (valueArr.value.length && isValueInColumn.value) {
            return buildColumnsAddValue();
        }
        return [];
    }
    const currentCol = filterColArr[index];
    const columns: IColumnProps[] = [];
    for (
        let i = 0;
        i <
        Math.min(
            currentCol.dropDownOPtions.selectList.list.length,
            filterColArr.length - 1 > index
                ? Math.ceil(20 / allColChildrenLengthSum(buildColumns(index + 1)))
                : Math.ceil(20 / (valueArr.value.length ? allColChildrenLengthSum(buildColumnsAddValue()) : 1))
        );
        i++
    ) {
        let item = currentCol.dropDownOPtions.selectList.list[i];
        let colLabel = "";
        if (currentCol.type === "asub") {
            colLabel = (item as ITableDataItem).asubName;
            let showMore = currentCol.dropDownOPtions.fields.checked;
            if (showMore.includes("asubCode")) {
                colLabel = (item as ITableDataItem).asubCode + " " + (item as ITableDataItem).asubName;
            }
        } else {
            colLabel = (item as IAssistingAccount).aaname;
            let showMore = currentCol.dropDownOPtions.fields.checked;
            if (showMore.includes("code")) {
                colLabel = (item as IAssistingAccount).aanum + " " + (item as IAssistingAccount).aaname;
            }
        }
        const newCol: IColumnProps = {
            label: colLabel,
            prop: "prop" + currentCol.type === "asub" ? (item as ITableDataItem).asubCode : (item as IAssistingAccount).aanum,
            minWidth: 140,
            align: "left",
            headerAlign: "center",
            resizable: false,
        };
        if (index < filterColArr.length - 1) {
            newCol.children = buildColumns(index + 1);
        } else {
            if (isValueInColumn.value) {
                // 数值在列上
                newCol.children = buildColumnsAddValue();
            } else {
                newCol.children = [{ label: " ", prop: " ", minWidth: 140, align: "left", headerAlign: "center" }];
            }
        }
        if (currentCol.type === filterColArr[0].type && currentCol.id === filterColArr[0].id) {
            if (allColChildrenLengthSum(columns) >= 20) {
                return columns;
            }
        }
        columns.push(newCol);
    }

    return columns;
}
function calculateChildrenLengthSum(obj: IColumnProps) {
    let sum = 1;

    if (obj.children && obj.children.length > 0) {
        sum = sum * obj.children.length * calculateChildrenLengthSum(obj.children[0] as IColumnProps);
    }

    return sum;
}

function buildColumnsAddValue() {
    let valueChildren: IColumnProps[] = [];
    valueArr.value.forEach((v: IValueListItem) => {
        let randomProp = Math.random();
        const valueCol: IColumnProps = {
            label: v.name,
            prop: String(v.value) + randomProp,
            minWidth: 120,
            align: "right",
            headerAlign: "center",
            resizable: false,
            children: [],
        };
        let dropDownOPtions = v.dropDownOPtions;
        if (dropDownOPtions.valuePeriodType === 3) {
            for (let i = dropDownOPtions.startValuePeriod; i <= dropDownOPtions.endValuePeriod; i++) {
                const yearCol: IColumnProps = {
                    label: i + "年",
                    prop: "year" + i + randomProp,
                    minWidth: 120,
                    align: "left",
                    headerAlign: "center",
                    resizable: false,
                };
                if (valueCol.children && valueCol.children.length < 20) {
                    (valueCol.children as IColumnProps[]).push(yearCol);
                } else {
                    break;
                }
            }
        } else if (dropDownOPtions.valuePeriodType === 1) {
            for (let i = 0; i < periodList.value.length; i++) {
                let current = periodList.value[i];
                if (current.pid >= dropDownOPtions.startValuePeriod && current.pid <= dropDownOPtions.endValuePeriod) {
                    const Col: IColumnProps = {
                        label: current.year + "年" + current.sn + "月",
                        prop: String(current.sn) + v.value + randomProp,
                        minWidth: 120,
                        align: "left",
                        headerAlign: "center",
                        resizable: false,
                    };
                    if (valueCol.children && valueCol.children.length < 20) {
                        (valueCol.children as IColumnProps[]).push(Col);
                    } else {
                        break;
                    }
                }
            }
        } else {
            for (let i = 0; i < periodList.value.length; i++) {
                let current = periodList.value[i];
                if (current.pid >= dropDownOPtions.startValuePeriod && current.pid <= dropDownOPtions.endValuePeriod) {
                    const quarter = Math.floor(current.sn % 3 === 0 ? current.sn / 3 : current.sn / 3 + 1);
                    const propQ = `${current.year}${quarter}${v.value}` + randomProp;

                    // 检查是否已经存在相同 prop 的列，避免推入重复数据
                    if (valueCol.children && valueCol.children.findIndex((v) => v.prop === propQ) < 0) {
                        const newCol: IColumnProps = {
                            label: `${current.year}年第${quarter}季度`,
                            prop: propQ,
                            minWidth: 120,
                            align: "left",
                            headerAlign: "center",
                            resizable: false,
                        };
                        if (valueCol.children && valueCol.children.length < 20) {
                            (valueCol.children as IColumnProps[]).push(newCol);
                        } else {
                            break;
                        }
                    }
                }
            }
        }

        if (allColChildrenLengthSum(valueChildren) >= 20) {
            return valueChildren;
        }
        valueChildren.push(valueCol);
    });

    return valueChildren;
}
function allColChildrenLengthSum(colArr: IColumnProps[]) {
    let sum = 0;
    colArr.forEach((v) => {
        sum += calculateChildrenLengthSum(v);
    });
    return sum;
}
function generateValuesInRowData() {
    let data: Array<{ [key: string]: any }> = [];

    valueArr.value.forEach((v) => {
        let newRow = {};
        let dropDownOPtions = v.dropDownOPtions;
        if (dropDownOPtions.valuePeriodType === 1) {
            for (let i = 0; i < periodList.value.length; i++) {
                let current = periodList.value[i];
                if (current.pid >= dropDownOPtions.startValuePeriod && current.pid <= dropDownOPtions.endValuePeriod) {
                    newRow = {
                        valueColValue: v.name,
                        valueDate: current.year + "年" + current.sn + "月",
                    };
                    data.push(newRow);
                }
            }
        } else if (dropDownOPtions.valuePeriodType === 3) {
            for (let i = dropDownOPtions.startValuePeriod; i <= dropDownOPtions.endValuePeriod; i++) {
                newRow = {
                    valueColValue: v.name,
                    valueDate: i + "年",
                };
                data.push(newRow);
            }
        } else {
            for (let i = 0; i < periodList.value.length; i++) {
                let current = periodList.value[i];
                if (current.pid >= dropDownOPtions.startValuePeriod && current.pid <= dropDownOPtions.endValuePeriod) {
                    newRow = {
                        valueColValue: v.name,
                        valueDate: current.year + "年第" + Math.floor(current.sn % 3 === 0 ? current.sn / 3 : current.sn / 3 + 1) + "季度",
                    };
                    if (
                        data.findIndex(
                            (v) =>
                                v.valueDate ===
                                current.year + "年第" + Math.floor(current.sn % 3 === 0 ? current.sn / 3 : current.sn / 3 + 1) + "季度"
                        ) < 0
                    ) {
                        data.push(newRow);
                    }
                }
            }
        }
    });
    return data;
}
const handleCodeInput = (value: string) => {
    pivotModel.statementCode = value.replace(/[^a-zA-Z\d]/g, "");
};
const handleNameInput = (value: string) => {
    pivotModel.statementName = value.replace(/[^a-zA-Z0-9_\-\s\u4e00-\u9fa5]/g, "");
};
defineExpose({
    setInitialData,
});
</script>

<style scoped lang="less">
@import "@/style/Constants.less";
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.pivot-table-designer-view {
    width: 100%;
    height: 100%;
    .main-content {
        display: flex;
        overflow: hidden;
        flex-direction: column;
        .main-top .main-tool-left {
            .form-title.required::before {
                content: "*";
                color: var(--el-color-danger);
                margin-right: 4px;
            }
            .form-field {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .help-tip {
                img {
                    width: 17px;
                    height: 17px;
                    margin-right: 2px;
                    position: relative;
                    top: 3px;
                }
            }
        }
        .pivot-table-container {
            display: flex;
            flex: 1 !important;
            overflow: hidden;
            .pivot-left-content {
                width: 200px;
                margin-right: 16px;
                border: 1px solid var(--el-border-color-light);
                .scrollbar-pivot-left {
                    position: relative;
                    right: 8px;
                }
                .list-title,
                .list-item-parent {
                    width: 100%;
                    height: 40px;

                    box-sizing: border-box;
                    text-decoration: none;
                    color: #333;
                    font-size: 14px;
                    white-space: nowrap;
                    padding: 0 32px;
                    display: block;
                    width: 100%;
                    line-height: 40px;
                    text-align: left;
                    border-bottom: 1px solid var(--el-border-color-light);
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    .list-item {
                        cursor: move;
                        width: auto;
                        display: inline-block;
                        width: 136px;
                        &:hover {
                            color: var(--main-color);
                        }
                        &.move-btn {
                            background-color: var(--main-color);
                            color: #fff;
                            width: 136px;
                            padding: 0 10px;
                            margin: 6px 0;
                            height: 28px;
                            text-align: center;
                            font-size: 12px;
                            white-space: nowrap;
                            line-height: 28px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            border-radius: 4px;
                        }
                        :deep(.span_wrap) {
                            max-width: 100%;
                            display: inline-block;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                }

                .list-title {
                    font-weight: 600;
                    background-color: var(--table-selected-color);
                }
                .dimensions-list {
                    max-height: calc(100vh / 2);
                }
            }
            .pivot-right-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                .tip {
                    text-align: left;
                    font-size: var(--h4);
                    color: var(--font-color);
                    line-height: 30px;
                }
                .right-content-top-setup {
                    border: 1px solid var(--border-color);
                    .row-line-set {
                        display: flex;
                        height: 40px;
                        line-height: 40px;
                        font-size: var(--h4);
                        color: var(--font-color);
                        border-bottom: 1px solid var(--border-color);
                        &:last-of-type {
                            border-bottom: none;
                        }
                        .line-name {
                            width: auto;
                            padding: 0 10px 0 15px;
                            font-size: var(--h4);
                            font-weight: 700;
                        }
                        .line-list {
                            display: flex;
                            align-items: center;
                            flex: 1;
                            height: 30px;
                            margin-top: 5px;
                            margin-right: 5px;
                            padding-left: 5px;
                            &.move-ing {
                                background-color: var(--table-selected-color);
                            }
                            .dropdown-button {
                                margin-right: 15px;
                            }
                            .el-button:hover {
                                background-color: var(--main-color);
                                color: #fff;
                            }
                            .figure-btn {
                                position: relative;
                                top: -2px;
                            }
                        }
                    }
                }
                .preview-pivot-table {
                    flex: 1;
                    margin-top: 10px;
                    border-bottom: 1px solid var(--el-border-color-light);
                    overflow: hidden;
                }
            }
        }
    }
}
</style>
