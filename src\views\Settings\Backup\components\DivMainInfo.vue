<template>
    <div class="main-top main-tool-bar">
        <a class="button solid-button mr-10" @click="beginBackup">开始备份</a>
        <a class="button solid-button large-1" @click="uploadLocalFile">上传本地备份</a>
        <span class="highlight-orange float-r"> 亲，备份的数据包是加密不能查看的，下载后即可上传，并且支持恢复数据包中的数据哦~ </span>
    </div>
    <div class="main-center">
        <Table 
            empty-text="暂无数据" 
            :data="divMainInfoData" 
            :columns="divMainInfoColumns()" 
            :rowClassName="judgeRowClassName"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="215" align="left" header-align="center" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.progress === -1" class="error-row">
                            <a class="link-error" v-permission="['backup-candelete']" @click="deleteBackupItem(scope.row.fileName, 'zip')">
                                删除
                            </a>
                            <span class="ml-10" v-permission="['backup-canview']">
                                <el-popover
                                    placement="bottom-end"
                                    :width="200"
                                    trigger="hover"
                                    popper-class="tip-popover"
                                    :popper-style="popperStyle"
                                >
                                    <template #reference>
                                        <span style="color: #ff7500">
                                            <span>备份失败 </span>
                                            <img
                                                src="@/assets/Settings/warnning-orange.png"
                                                alt=""
                                                style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                            />
                                        </span>
                                    </template>
                                    网络异常，请删除重新备份
                                </el-popover>
                            </span>
                        </span>
                        <span v-show="scope.row.progress == 100">
                            <a class="link" v-permission="['backup-canrestore']" @click="beginRestore(scope.row.fileName)"> 恢复 </a>
                            <a class="link" v-permission="['backup-candelete']" @click="deleteBackupItem(scope.row.fileName, 'zip')">
                                删除
                            </a>
                            <a class="link" v-permission="['backup-canview']" v-if="scope.row.expiredDate">
                                <el-popover
                                    placement="bottom-end"
                                    :width="300"
                                    trigger="hover"
                                    popper-class="tip-popover"
                                    :popper-style="popperStyle"
                                >
                                    <template #reference>
                                        <span>
                                            <a @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)"> 下载 </a>
                                            <img
                                                src="@/assets/Settings/warnning-orange.png"
                                                alt=""
                                                style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                            />
                                        </span>
                                    </template>
                                    空间不足！已为您提供额外存储空间备份， <br />
                                    请于72小时内下载到本地，超时将自动删除
                                </el-popover>
                            </a>
                            <a
                                class="link"
                                v-permission="['backup-canview']"
                                @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)"
                                v-else
                            >
                                <span>下载</span>
                            </a>
                        </span>
                        <span v-show="scope.row.progress !== 100">
                            <span v-show="scope.row.progress > 0" style="display: flex; align-items: center">
                                <span class="progress-bar">
                                    <span class="progress-solid-bar" :style="{ width: scope.row.progress + '%' }"></span>
                                </span>
                                <span>{{ scope.row.progress + "%" }}</span>
                            </span>
                            <span class="loading-operator" v-show="scope.row.progress === 0">
                                <span class="loading-icon"></span>
                                <span class="link" style="margin-left: 3px">数据加载中...</span>
                            </span>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
    <el-dialog title="上传本地备份文件" center width="440" v-model="uploadLocalFileShow" class="dialogDrag">
        <div class="common-dialog-content" v-dialogDrag>
            <div class="common-dialog-main">
                <div class="common-dialog-main-title" style="font-weight: normal">
                    <div style="display: flex; justify-content: center; align-items: center">
                        <label class="file-button">
                            <input @change="chooseFile($event)" type="file" />
                            <a class="link">选取文件</a>
                        </label>
                        <span class="file-box">{{ fileName }}</span>
                    </div>
                    <div class="highlight-blue" v-show="waitLoading">导入中，请稍等...</div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="uploadAjaxFile">上传</a>
                <a class="button ml-10" @click="uploadLocalFileShow = false">返回</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="备份提示" center width="440" v-model="processTipShow" class="dialogDrag">
        <div class="common-dialog-content" v-dialogDrag>
            <div class="common-dialog-main">
                <div class="common-dialog-main-title">
                    如果该账套有多名用户使用，为了确保备份数据的完整<br />
                    性，请在备份过程中停止操作，是否确定执行备份？
                </div>
            </div>
            <div class="buttons">
                <a @click="processZip" class="button solid-button">确定</a>
                <a @click="() => (processTipShow = false)" class="button ml-10">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { divMainInfoColumns, popperStyle, judgeRowClassName } from "../utils";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { getGlobalToken } from "@/util/baseInfo";

import type { ITableData, ICheckSpace, IProcessBack } from "../types";

import Table from "@/components/Table/index.vue";
import { useLoading } from "@/hooks/useLoading";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { checkCanProcessAndHasTemporaryFile } from "../utils";

const setModule = "DivMain";
const emit = defineEmits(["deleteBackupItem", "downloadbackupitem", "insertRow", "beginRestore"]);
const props = defineProps<{
    tableData: ITableData[];
    uploadFinallyDoing: (success: boolean) => void;
    showSpaceDialog: (usedSpace: number, totalSpace: number) => void;
    showNotEnoughDiskDialog: () => void;
    showBackupTipDialog: () => void;
}>();
const divMainInfoData = computed(() => props.tableData);
const uploadLocalFileShow = ref(false);
const fileName = ref("");
const file = ref<File | null>(null);
const waitLoading = ref(false);
const processTipShow = ref(false);
const fileType = "zip";

const deleteBackupItem = (fileName: string, type: string) => emit("deleteBackupItem", fileName, type);
const downloadbackupitem = (fileName: string, fileType: number) => emit("downloadbackupitem", fileName, fileType);
const beginBackup = () => {
    processTipShow.value = true;
};
const uploadLocalFile = () => (uploadLocalFileShow.value = true);
const chooseFile = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const selectedFile: File = (input.files as FileList)[0];
    if (!selectedFile) {
        fileName.value = "";
        file.value = null;
        return;
    }
    fileName.value = selectedFile.name;
    file.value = selectedFile;
};
let uploading = false;
const uploadAjaxFile = () => {
    if (uploading) {
        ElNotify({ type: "warning", message: "正在上传中，请稍后" });
        return;
    }
    if (!file.value) {
        ElNotify({ type: "warning", message: "请选择文件" });
        return;
    }
    const formData = new FormData();
    formData.append("myfile", file.value);
    uploading = true;
    waitLoading.value = true;
    request({ url: "/api/Backup/UploadFile", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            const uploadPostion =
                window.jLmDiskHost + "/services/backup/uploadfile.ashx?accesscode=" + res.data + "&appasid=" + getGlobalToken();
            request({
                url: uploadPostion,
                method: "post",
                headers: { "Content-Type": "multipart/form-data" },
                data: formData,
            })
                .then(() => {
                    uploading = false;
                    uploadLocalFileShow.value = false;
                    props.uploadFinallyDoing(true);
                })
                .catch(() => {
                    uploading = false;
                    props.uploadFinallyDoing(false);
                });
        } else {
            ElNotify({ type: "warning", message: "授权失败，请重新登录再次尝试" });
        }
    });
};
const processZip = () => {
    processTipShow.value = false;
    const confirmMsg = "您已有临时的备份数据包文件，继续备份数据包将自动删除之前的临时文件，是否继续？";
    const msg = window.isAccountingAgent
        ? "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
        : "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：";
    checkCanProcessAndHasTemporaryFile(
        fileType,
        confirmMsg,
        go,
        () => {
            request({ url: "/api/Backup/CheckSpace?fileType=" + fileType, method: "post" }).then((r: IResponseModel<ICheckSpace>) => {
                if (r.state === 1000) {
                    if (r.data.overflow) {
                        useLoading().quitLoading();
                        if (useThirdPartInfoStoreHook().isThirdPart) {
                            props.showSpaceDialog(r.data.state.usedSpace, r.data.state.totalSpace);
                        } else {
                            tryShowPayDialog(1, "backup-" + fileType, msg, "会计电子档案", go);
                            return;
                        }
                    }
                }
                go();
            });
        },
        props.showBackupTipDialog
    );
};
const go = () => {
    useLoading().enterLoading("备份进行中，请稍候...");
    const url = "/api/Backup/ProcessBackup?allowTemporaryFile=1&isProcess=1";
    request({ url, method: "post" })
        .then((res: IResponseModel<IProcessBack>) => {
            useLoading().quitLoading();
            if (res.state !== 1000) {
                if (res.subState == 4) {
                    if (useThirdPartInfoStoreHook().isThirdPart) {
                        props.showNotEnoughDiskDialog();
                    } else {
                        const msg = window.isAccountingAgent
                            ? "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
                            : "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：";
                        tryShowPayDialog(1, "backup-" + fileType, msg, "会计电子档案", () => {});
                    }
                } else {
                    if (res.msg) {
                        ElNotify({ type: "warning", message: res.msg });
                        return;
                    }
                    ElNotify({ type: "warning", message: "备份操作出错，请稍后重试" });
                    return;
                }
            } else {
                const row = res.data;
                emit("insertRow", row, "zip");
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "备份操作出错，请稍后重试" });
        });
};

const beginRestore = (fileName: string) => emit("beginRestore", fileName);

const changeImportState = (state: boolean) => {
    waitLoading.value = state;
};
defineExpose({ changeImportState });
</script>

<style lang="less" scoped>
@import "@/style/Settings/Backup.less";
</style>
