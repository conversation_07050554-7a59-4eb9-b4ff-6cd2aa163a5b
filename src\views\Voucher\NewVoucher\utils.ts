interface IQuickKeycode {
    save: () => void;
    saveAndAdd: () => void;
    newVoucher: () => void;
    nextVoucher: () => void;
    lastVoucher: () => void;
    openTempDialog: () => void;
    deleteVoucher: () => void;
    saveAsTemp: () => void;
    print: () => void;
}

export function handleCheckHasDialog(content?: HTMLElement) {
    let hasDialog = false;
    const _overlayList = content?.querySelectorAll(".el-overlay");
    if (!_overlayList || _overlayList.length === 0) return hasDialog;
    for (let i = 0; i < _overlayList.length; i++) {
        if (window.getComputedStyle(_overlayList[i] as HTMLElement).display !== "none") {
            hasDialog = true;
            break;
        }
    }
    return hasDialog;
}

export const setVoucherQuickKeycode = (porps: IQuickKeycode) => {
    document.onkeydown = function (e) {
        e = e || window.event;
        const kc = e.keyCode || e.charCode;
        if (kc == 83 && e.ctrlKey) {
            //ctrl + S 保存
            porps.save();
            return false;
        } else if (kc == 123) {
            //F12 保存并新增
            porps.saveAndAdd();
            return false;
        } else if (kc == 78 && e.altKey) {
            //alt+N 新增
            porps.newVoucher();
            return false;
        } else if (kc == 33) {
            //PgUp 上一张
            e.preventDefault();
            porps.lastVoucher();
            return false;
        } else if (kc == 34) {
            //PgDn 下一张
            e.preventDefault();
            porps.nextVoucher();
            return false;
        } else if (kc === 81 && e.ctrlKey) {
            //ctrl + q 调用模板选择框
            porps.openTempDialog();
            return false;
        } else if (kc === 46 && e.ctrlKey) {
            //ctrl + delete 删除
            porps.deleteVoucher();
            return false;
        } else if (kc === 69 && e.ctrlKey) {
            //ctrl + E 另存为模板
            porps.saveAsTemp();
            return false;
        } else if (kc === 80 && e.ctrlKey) {
            //ctrl + P 打印
            porps.print();
            return false;
        }
    };
};

export const deleteVoucherQuickKeycode = () => {
    document.onkeydown = null;
};
