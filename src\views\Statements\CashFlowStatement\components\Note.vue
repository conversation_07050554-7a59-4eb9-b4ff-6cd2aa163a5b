<script lang="ts" setup>
import { computed } from "vue";
import { hasNumberTitle } from "../utils";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";

const props = defineProps<{
    Amount: number|undefined; // Amount
    LineName: string|undefined; // LineName
    modelValue: string|undefined; // Remark
}>();

const emit = defineEmits<{
    (e: "update:modelValue", value: string): void;
}>();

const Remark = computed({
    get(): string  {
        return props.modelValue||'';
    },
    set(value: string) {

        emit("update:modelValue", value);
    },
});
</script>

<template>
    <template v-if="!hasNumberTitle(props.LineName)">
        <div>
            <el-popover placement="right" trigger="click" popper-class="tooltip-dialog-content" width="227">
                <template #reference>
                    <a class="link">备注</a>
                </template>
                <template #default>
                    <textarea
                        style="height: 120px; width: 200px; z-index: 2000000"
                        placeholder="请输入备注内容"
                        v-model="Remark"
                        maxlength="60"
                        :disabled="!useAccountSetStoreHook().permissions.includes('cashflowstatement-canedit')"
                    ></textarea>
                </template>
            </el-popover>
        </div>
    </template>
</template>

<style lang="less" scoped>
.tooltip-dialog-content {
    padding: 10px;
    width: 200px;
    height: 120px;
    box-sizing: border-box;
    text-align: left;
}
textarea {
    outline: none;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    color: var(--font-color);
    border-radius: 2px;
    line-height: 25px;
    background-color: White;
    font-size: 12px !important;
    border: none;
    resize: none;
}
</style>
