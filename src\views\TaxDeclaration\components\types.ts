import type { IColumnProps } from "@/components/Table/types"

// 数据类型
// tab页数据类型
export interface TabObj {
  id?: string | number
  isRequired?: boolean
  title: string
  describe?: string
  columns?: IColumnProps[]
  data?: Array<{ [key: string | number]: any }> | { [key: string | number]: any }
  spanMethod?: any
  headerCellStyle?: any
  func?: () => void
}
export interface ITabProps {
  tabList?: Array<TabObj>
  showCheck?: boolean
  showTotal?: boolean
  activeIndex?: number
  tabContent?: any // 暂定为any
}

// 可填写表格props
export interface IWritableTableProps {
  columns?: Array<IColumnProps>
  data?: Array<any>
  height?: number | string
  spanMethod?: any
}
