import { watch } from "vue";
import type { IPaginationData } from "./usePagination";

export const useDebugCurrentPage = (paginationData: IPaginationData, callBack: () => void, delay = 100) => {

    let canSearch = true;

    const debugCanChangeCurrentPage = () => {
        canSearch = false;
        const timer = setTimeout(() => {
            canSearch = true;
            clearTimeout(timer);
        }, delay);
    };

    watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
        const timer = setTimeout(() => {
            clearTimeout(timer);
            if (!canSearch) return;
            callBack();
        }, 10);
    });

    return debugCanChangeCurrentPage;
};
