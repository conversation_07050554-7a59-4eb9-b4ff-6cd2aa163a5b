<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:custno="autoBankForm.custno"
        v-model:acid="autoBankForm.acid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :uscc-number="props.usccNumber"
        @save-success="saveSuccessHandle"
        @confirm-bank="confirmBank"
    />
    <div class="slot-content align-center edit-temp-table" style="height: 350px" v-show="confirmTable">
        <div class="slot-content-mini edit-temp-table-content boc-bank">
            <div class="autoBankSelect">
                <div class="autoBankItem"><span style="font-weight: 600">第一步：</span>打开浏览器使用管理员U盾登录中行网上银行</div>
                <div class="autoBankItem" style="margin-bottom: 6px">
                    <span style="font-weight: 600">第二步：</span>首页点击进入【特色服务-开放银行管理-协议签署】
                </div>
                <div class="autoBankItem">
                    <div style="margin-left: 56px">选择协议签订开通</div>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第三步：</span>同意签署协议，法人身份验证通过则完成协议签署</div>
                <div class="autoBankItem"><span style="font-weight: 600">第四步：</span>点击立即授权，确定完成授权</div>
            </div>
            <div class="autoBankDescription">
                <div style="margin-left: 40px; padding-top: 20px; line-height: 16px">
                    <span style="font-weight: 500">中国银行网银地址：</span>
                    <span class="copy-file copy-key" data-target="boc-apply-url" @click="handleCopy">复制</span>
                </div>
                <p class="copy-key pub-key" data-target="boc-apply-url" @click="handleCopy">
                    https://netc1.igtb.bankofchina.com/#/login-page
                </p>
            </div>
            <div class="authButton" style="margin-bottom: 20px; margin-top: 50px">
                <a class="button back-button" attr-click="1" @click="handleBack">上一步</a>
                <a class="solid-button-large" attr-click="1" @click="JumpToBankAuthorizePage">立即授权</a>
            </div>
            <div class="autoBankToOpen" style="height: 16px">&nbsp;</div>
            <CommonBottom></CommonBottom>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watchEffect, onMounted, watch, nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { BankType } from "@/constants/bankKey";
import { GetBankLink } from "@/util/bankType";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams } from "@/util/url";
import { appendStyle, copyText, handleCheck, upgradeApi, getSuccessMsg } from "../utils";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, IBankHandleResult, CMBGradeResult } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import CommonBottom from "./CommonBottom.vue";
import { replaceAll } from "@/util/common";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = [
    "1、请确认公司名称和统一社会信用代码",
    "2、选择对应的银行账户，并确认银行账号和名称正确",
    "3、在电脑上插入“中国银行”U盾",
    "4、点击立即授权",
];

const props = defineProps({
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const currencyList = computed(() => props.currencyList);
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const impowerValue = ref("立即授权");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    mainaccno: "",
    custno: "",
});

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "申请中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};
let canNextAuthorized = true;
const authorizedText = ref("立即授权");
const handleAuthorizedLock = () => {
    canNextAuthorized = false;
    authorizedText.value = "申请中...";
};
const handleAuthorizedUnLock = () => {
    canNextAuthorized = true;
    authorizedText.value = "立即授权";
};
const confirmTable = ref(false);
const handleBack = () => {
    showMain.value = true;
    confirmTable.value = false;
    handleAuthorizedUnLock();
    handleConfirmUnLock();
};
const handleToConfirm = () => {
    showMain.value = false;
    confirmTable.value = true;
    handleConfirmUnLock();
};
const confirmBank = () => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    handleConfirmLock();
    let accountNumber = replaceAll(autoBankForm.mainaccno, " ", "");
    const custNumber = replaceAll(autoBankForm.custno, " ", "");
    if (custNumber !== "") {
        accountNumber += "_" + custNumber;
    }

    props.checkAuthorization(props.bankType, accountNumber, () => {
        request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
                handleConfirmUnLock();
                return;
            }
            handleQuery();
        });
    });
};

const handleQuery = () => {
    const { bankAccountName } =  getSuccessMsg(autoBankForm, bankAccountList.value);
    upgradeApi(props, autoBankForm, bankAccountName).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Query");
    });
};

const JumpToBankAuthorizePage = () => {
    if (!canNextAuthorized) {
        ElNotify({ type: "warning", message: "申请中，请稍后" });
        return;
    }
    handleAuthorizedLock();
    const { bankAccountName } =  getSuccessMsg(autoBankForm, bankAccountList.value);
    upgradeApi(props, autoBankForm, bankAccountName).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Apply");
    });
};

function handleResult(res: IResponseModel<CMBGradeResult>, type: string) {
    const { successMsg, acctNo, acname} =  getSuccessMsg(autoBankForm, bankAccountList.value);
    const confirmErrorMsg = `${acname} (${acctNo}) 签约还未成功，请再次确认账号和签约状态后点击立即授权，完成授权~`;
    type === "Query" ? handleConfirmUnLock() : handleAuthorizedUnLock();
    if (res.state !== 1000) {
        ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
        return;
    }
    const result = res.data;
    if (result.status === 1 || result.status === 2) {
        //已签约成功
        ElConfirm(appendStyle(successMsg), true, () => {}, "授权成功").then(() => {
            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
        });
        return;
    }
    if (result.status === 3 && type === "Query") { //签约中查询
        handleToConfirm();
        return;
    }
    if (result.status === 3 && type === "Apply") { //签约中申请
        ElConfirm(appendStyle(confirmErrorMsg), true, () => {}, "授权失败");
        return;
    } 
    if (result.status === 4) {
        let errorMsg = result.message;
        if (result.data && result.data.Msg) {
            errorMsg = result.data.Msg;
        }
        ElConfirm(errorMsg || "签约失败，请联系客服或者稍后再试~", true, () => {}, "授权失败");
        return;
    }
    if (result.status === 5 || result.status === 6) {
        //其他账套成功签约/其他账套签约中
        ElConfirm(result.message, true, () => {}, "授权失败");
        return;
    }
}

const bankHelpLink = ref("");
onMounted(() => {
    let res = GetBankLink(props.bankType);
    bankHelpLink.value = res;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});

watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);
const handleCopy = () => {
    copyText("https://netc1.igtb.bankofchina.com/#/login-page");
    ElNotify({ type: "success", message: "复制成功" });
};

defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
</style>
