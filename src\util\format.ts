import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { ElNotify } from "./notify";
import { getGlobalLodash } from "@/util/lodash";

export const formatMoney = (value: number | string | undefined, separator = true, showZero: boolean = false) => {
    if (value) {
        if (typeof value === "number" && !isNaN(value)) {
            if (separator) {
                return value.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
            } else {
                return value.toFixed(2);
            }
        } else if (typeof value === "string") {
            const numberValue = Number(value);
            if (!numberValue) {
                return "";
            } else {
                if (separator) {
                    return numberValue.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
                } else {
                    return numberValue.toFixed(2);
                }
            }
        }
    }
    if (showZero) {
        return "0.00";
    }
    return "";
};
export const formatQuantity = (value: number | string | undefined) => {
    if (value === "0" || value === "" || value === 0 || value === undefined || value === null) return "";
    if(window.isErp){
        return value.toString();
    }else{
        const decimalPlace = useAccountSetStoreHook().accountSet?.decimalPlace ?? 2;
        const num = parseFloat(value + "");
        if (isNaN(num)) {
            return value + ""; // 输入不是有效的数字字符串，直接返回原字符串
        }
        const roundedNum = Math.round(num * Math.pow(10, decimalPlace)) / Math.pow(10, decimalPlace); // 保留最多指定位数的小数，并四舍五入
        let formattedStr = roundedNum.toString();
        if (formattedStr.indexOf(".") !== -1) {
            // 处理小数位
            formattedStr = formattedStr.replace(new RegExp(`\\.?0{1,${decimalPlace}}$`), ""); // 去除小数位末尾的0
        }
        return formattedStr;
    }
};

export const formatMoneyWithZero = (value: number | string | undefined) => {
    if (value) {
        if (typeof value === "number" && !isNaN(value)) {
            return getGlobalLodash().round(value, 2).toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        } else if (typeof value === "string") {
            const numberValue = Number(value);
            return numberValue.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        }
    }
    return "0.00";
};

export const formatNumber = (value: number, decimalPlace?: number, defaultValue?: string) => {
    decimalPlace = decimalPlace || 2;
    defaultValue = defaultValue || "";
    if (!value) {
        return defaultValue;
    }
    return Number(value)
        .toFixed(decimalPlace)
        .replace(/(\.\d*?)([,0]+)$/g, "$1")
        .replace(/\.$/, "");
};

export const digitUppercase = (n: number) => {
    const fraction = ["角", "分"];
    const digit = ["零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"];
    const unit = [
        ["元", "万", "亿"],
        ["", "拾", "佰", "仟"],
    ];
    const head = n < 0 ? "负" : "";
    n = Math.abs(n);
    let s = "";
    for (let i = 0; i < fraction.length; i++) {
        if (i == 0) {
            s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, "");
        } else {
            s += (digit[Math.round(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, "");
        }
    }
    s = s || "整";
    n = Math.floor(n);
    for (let i = 0; i < unit[0].length && n > 0; i++) {
        let p = "";
        for (let j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
    }
    return (
        head +
        s
            .replace(/(零.)*零元/, "元")
            .replace(/(零.)+/g, "零")
            .replace(/^整$/, "零元整")
    );
};

export const sizeFormatter = (size: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB", "BB"];
    let i = 0;
    while (size >= 1024 && i < units.length - 1) {
        size /= 1024;
        i++;
    }
    const returnSize = size.toFixed(2) + " " + units[i];
    return returnSize === "0.00 B" ? "" : returnSize;
};

//制保留2位小数，如：2，会在2后面补上00.即2.00
export const toDecimal2 = (x: string) => {
    let f = parseFloat(x);
    if (isNaN(f)) {
        return "";
    }
    f = Math.round(Number(x) * 100) / 100;
    let s = f.toString();
    let rs = s.indexOf(".");
    if (rs < 0) {
        rs = s.length;
        s += ".";
    }
    while (s.length <= rs + 2) {
        s += "0";
    }
    return s;
};

export const toDecimal3 = (num: string | number, decimal?: number) => {
    num = Number(num);
    decimal = decimal || 2;
    return (Math.round(num * Math.pow(10, decimal)) / Math.pow(10, decimal)).toFixed(decimal);
};

export const formatNumberStr = (num: string) => {
    let fu = false;
    if (num.indexOf("-") === 0) {
        num = num.substring(1);
        fu = true;
    }
    const reg = /(\d*)\.?(\d*)/.exec(num);
    if (reg) {
        const integerPart = reg[1];
        const fractionalPart = reg[2];
        let integerResult = "";
        let fractionalResult = "";
        for (let i = integerPart.length - 1; i >= 0; i--) {
            if ((integerPart.length - i - 1) % 3 === 0 && integerResult) {
                integerResult = "," + integerResult;
            }
            integerResult = integerPart[i] + integerResult;
        }
        for (let i = 0; i < fractionalPart.length; i++) {
            if (i % 3 === 0 && fractionalResult) {
                fractionalResult = fractionalResult + ",";
            }
            fractionalResult = fractionalResult + fractionalPart[i];
        }
        let result = integerResult + (fractionalResult ? "." : "") + fractionalResult;
        if (fu) result = "-" + result;
        return result;
    }
};

export const isNumber = (n: string) => {
    const regu = "^[0-9]+$";
    const re = new RegExp(regu);
    if (n.search(re) != -1) {
        return true;
    } else {
        return false;
    }
};

export const isNull = (n: string | number) => {
    n = n + "";
    if (n == "") return !0;
    const t = new RegExp("^[ ]+$");
    return t.test(n);
};

export const FormatNumber = (numberStr: string) => {
    //if (numberStr == false)
    //    return "";
    let result: string = "";
    let haveDian = false;
    let lessThan0 = false;
    haveDian = numberStr.indexOf(".") != -1;
    lessThan0 = parseFloat(numberStr) < 0;

    if (lessThan0) {
        numberStr = numberStr.replace("-", "");
    }

    if (haveDian) {
        const vals = numberStr.split(".");
        const val = vals[0].replace(/,/g, "");

        const strs = val.split("");
        let result = "";
        let j = 0;
        for (let i = strs.length - 1; i > -1; i--) {
            result = strs[i] + result;
            if ((j + 1) % 3 == 0 && j + 1 != strs.length) {
                result = "," + result;
            }
            j++;
        }
        result += "." + vals[1];
    } else {
        const val = numberStr.replace(/,/g, "");
        const strs = val.split("");
        let result = "";
        let j = 0;
        for (let i = strs.length - 1; i > -1; i--) {
            result = strs[i] + result;
            if ((j + 1) % 3 == 0 && j + 1 != strs.length) {
                result = "," + result;
            }
            j++;
        }
    }

    if (lessThan0) {
        result = "-" + result;
    }

    return result;
};

export const truncateDecimal = (num: any, digits: any) => {
    if (num == undefined || num == null || num == 0.0) {
        return "";
    }
    let formated = false;
    if (num.toString().indexOf(",") != -1) {
        formated = true;
        num = num.replace(/,/g, "");
    }
    num = parseFloat(num).toFixed(8);
    // 如果小数位数本来就小于要求的位数，则直接返回原数
    if (num.toString().split(".")[1] && num.toString().split(".")[1].length < digits) {
        return formated ? FormatNumber(num.toString()) : num.toString();
    }
    let numStr = num.toString();
    const dotIndex = numStr.indexOf("."); // 获取小数点的位置
    let r;
    if (dotIndex !== -1) {
        // 如果存在小数点，则进行截断
        numStr = numStr.substring(0, dotIndex + 1 + digits); // 保留小数位数及小数点
        numStr = numStr.replace(/0+$/, ""); // 去除末尾不必要的0
        if (numStr[numStr.length - 1] === ".") {
            // 如果末尾是小数点，也要去掉
            numStr = numStr.substring(0, numStr.length - 1);
        }
        r = numStr;
    } else {
        r = num.toString(); // 如果不是小数，直接返回原数
    }
    return formated ? FormatNumber(r) : r;
};

export const truncateDecimalZero = (value: any) => {
    if (value == undefined || value == null || value == 0.0) {
        return "";
    }
    return value
        .toString()
        .replace(/(\.\d*?)([,0]+)$/g, "$1")
        .replace(/\.$/, "");
};

// 处理带有a标签返回值提取中间部分
export const handleAsubName = (name: string) => {
    const quantity = name.match(/</g);
    const length = quantity?.length || 0;
    if (length === 2 && name.indexOf("<a") >= 0) {
        return name.replace(/<[^<>]+>/g, "");
    } else if (length > 2 && name.indexOf("<a") >= 0) {
        const newStr = name.slice(1, -1);
        const end = newStr.lastIndexOf("<") + 1;
        const start = newStr.indexOf(">") + 2;
        return name.slice(start, end);
    } else {
        return name;
    }
};

// 获取月初
export function getMonthStart(date: Date) {
    return new Date(date.getFullYear(), date.getMonth(), 1).toLocaleDateString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
    });
}

// 获取月末
export function getMonthEnd(date: Date) {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).toLocaleDateString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
    });
}

//裁断保留小数不进行四舍五入
export const formatClipPointNum = (value: number | string | undefined) => {
    if (value === "0" || value === "" || value === 0 || value === undefined || value === null) return "";
    if (window.isErp){
        return value.toString();
    }else{
        const decimalPlace = useAccountSetStoreHook().accountSet?.decimalPlace ?? 2;
        let num = parseFloat(value + "");
        if (isNaN(num)) {
            return value + ""; // 输入不是有效的数字字符串，直接返回原字符串
        }
        if (num.toString().split(".")[1] && num.toString().split(".")[1].length > 8) {
            // 如果小数位数大于8位，先四舍五入到八位
            num = Math.round(num * Math.pow(10, 8)) / Math.pow(10, 8);
        }
        let formattedStr = num.toString();
        if (formattedStr.includes(".")) {
            const parts = formattedStr.split(".");
            if (decimalPlace) {
                formattedStr = parts[0] + "." + (parts[1] || "").substring(0, decimalPlace);
                formattedStr = formattedStr.replace(new RegExp(`\\.?0{1,${decimalPlace}}$`), ""); // 去除小数位末尾的0
            } else {
                formattedStr = parts[0];
            }
        }
        const numberValue = Number(formattedStr);
        return numberValue.toLocaleString(undefined, {
                    minimumFractionDigits: undefined,
                    maximumFractionDigits: 8,
                    useGrouping: false,
                });
        // return formattedStr;
    }
};

//账套的小数设置判断是否禁止显示气泡
export const showSpanPopover = (value: number | string | undefined) => {
    if (value === undefined || value === null || isNaN(Number(value))) return true;
    if (value.toString().split(".")[1] && value.toString().split(".")[1].length > 8) {
        value = Math.round(Number(value) * Math.pow(10, 8)) / Math.pow(10, 8);
    }
    // const parsedValue = Math.floor(Number(value)* Math.pow(10, decimalPlace))/Math.pow(10, decimalPlace)
    const parsedValue = formatClipPointNum(value);
    if (Math.abs(Number(parsedValue)) < Math.abs(Number(value))) {
        return false;
    } else {
        return true;
    }
};

//格式化关联凭证显示格式 日期+凭证字
export const formatVoucherTxt = (vDateStr: string | null | undefined, voucherGroupTxt: string | null | undefined) => {
    if (voucherGroupTxt && vDateStr) {
        return `${vDateStr} ${voucherGroupTxt}`;
    }
    return voucherGroupTxt
}

export function handleKeyDown(e: Event) {
    const event = e as KeyboardEvent;
    const allowedKeys = ["Backspace", "Delete", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown"];
    const isAllowed = allowedKeys.includes(event.key);
    if (!isAllowed && !/^[0-9]\d*$/g.test(event.key)) {
        event.preventDefault();
    }
}

export const useNumberInput = (proxy: any, type: "reactive" | "ref", attr = "") => {
    let lastValue = "";
    function handleInput(value: string) {
        if (!/^[0-9]\d*$/g.test(value) && value !== "") {
            ElNotify({ type: "warning", message: "亲，请输入数字！" });
            if (type === "reactive") {
                proxy[attr] = lastValue;
            } else {
                proxy.value = lastValue;
            }
            return;
        }
        lastValue = value;
    }
    return handleInput;
}