<template>
    <div class="slot-content align-center">
        <div class="slot-title">新增账套</div>
        <div class="slot-content-mini" style="border-radius: 0 0 12px 12px">
            <div class="error-msg" id="errorMsg">
                <span v-show="errorMsgShow">{{ errorMsg }}</span>
            </div>
            <!-- 新版 -->
            <div class="create-content-form">
                <div class="line-item1">
                    <div class="line-item-title"><span class="highlight-red">*</span>单位名称：</div>
                    <div class="line-item-field">
                        <el-autocomplete
                            ref="asNameRef"
                            v-model="createData.asName"
                            :prop="[{ required: true, message: '亲，单位名称不能为空', trigger: ['blur', 'change'] }]"
                            :fetch-suggestions="querySearch"
                            :trigger-on-focus="false"
                            class="inline-input w-50"
                            placeholder="请输入完整的单位名称"
                            style="width: 238px"
                            @select="selectName"
                        />
                    </div>
                </div>
                <div class="line-item1">
                    <div class="line-item-title"><span class="highlight-red">*</span>启用年月：</div>
                    <div class="line-item-field" id="startYear">
                        <div class="jqtransform">
                            <el-select v-model="createData.asStartYear" style="width: 115px" :teleported="false" :fit-input-width="true">
                                <el-option
                                    v-for="item in yearList"
                                    :value="item"
                                    :label="(item as number)+'年'"
                                    :key="(item as number)"
                                ></el-option>
                            </el-select>
                            <el-select
                                v-model="createData.asStartMonth"
                                style="width: 115px; padding: 0; margin-left: 10px"
                                :teleported="false"
                                :fit-input-width="true"
                            >
                                <el-option
                                    v-for="item in monthList"
                                    :value="item"
                                    :label="(item as number)+'月'"
                                    :key="(item as number)"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </div>
                <div class="line-item1">
                    <div class="line-item-title"><span class="highlight-red">*</span>会计准则：</div>
                    <div class="line-item-field">
                        <div class="jqtransform">
                            <el-select
                                v-model="createData.accountingStandard"
                                style="width: 238px"
                                :teleported="false"
                                :fit-input-width="true"
                                placement="bottom"
                            >
                                <el-option label="小企业会计准则" value="1" />
                                <el-option label="企业会计准则" value="2" />
                                <el-option label="民间非营利组织会计制度" value="3" />
                                <el-option label="农民专业合作社财务会计制度（2023年）" value="5" />
                                <el-option value="6" label="工会会计制度" />
                                <el-option value="7" label="农村集体经济组织会计制度" />
                            </el-select>
                            <el-popover
                                placement="right"
                                :width="210"
                                trigger="hover"
                                content="创建后年月无法修改，企业会计准则可点击账套管理-设置-切换准则调整报表样式。"
                            >
                                <template #reference>
                                    <div class="image-container"></div>
                                </template>
                            </el-popover>
                        </div>
                    </div>
                </div>
                <div class="line-item1">
                    <div class="line-item-title"><span class="highlight-red">*</span>增值税种类：</div>
                    <div class="line-item-field">
                        <el-radio-group v-model="createData.taxType">
                            <el-radio label="1" size="large">小规模纳税人</el-radio>
                            <el-radio label="2" size="large">一般纳税人</el-radio>
                        </el-radio-group>
                    </div>
                </div>
            </div>
            <div class="buttons" style="display: flex; justify-content: center">
                <a class="button solid-button" id="saveAS" @click="SubmitAccountSet">创建账套</a
                ><a class="button" id="cancel" tabindex="20" @click="cancelCreate">取消</a>
            </div>
            <div v-if="!isThirdPart" @click="gotoImportFromOther" class="report-container" id="dzReport"></div>
        </div>
        <el-dialog v-model="showAccountingStandardTip" title="会计准则说明" center width="440px" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div class="box-body" style="padding-top: 30px; padding-bottom: 30px">
                    <div class="body-message">
                        <div class="body-message-title" style="text-align: left">亲，请根据实际情况选择对应的会计准则。</div>
                        <div class="body-message-content">
                            {{
                                isProSystem
                                    ? "① 小企业会计准则是按照2013年版 《小企业会计准则》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。"
                                    : "① 小企业会计准则是按照2013年版 《小企业会计准则》设置，为了使报表取数准确，系统不允许增加一级科目，但可以增加二级、三级和四级科目。"
                            }}
                            <br />② 企业会计准则是按照2007年版 《企业会计准则》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />③
                            民间非营利组织会计制度是按照2005年版《民间非营利组织会计制度》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />④
                            农民专业合作社财务会计制度是按照2023年版《农民专业合作社会计制度》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />
                            {{
                                isAccountingAgent
                                    ? ""
                                    : "⑤ 需要使用《企业会计准则(2019年未执行新金融准则、新收入准则和新租赁准则)》或《企业会计准则(2019年已执行新金融准则、新收入准则和新租赁准则)》时，可以先选择《企业会计准则》新建账套，新建后到设置—账套—编辑页面去切换准则"
                            }}
                        </div>
                    </div>
                </div>
                <div class="box-footer button-ok" style="text-align: center; padding: 10px 0">
                    <a class="button solid-button" @click="() => (showAccountingStandardTip = false)">确定</a>
                </div>
            </div>
        </el-dialog>
        <ProOverFlowDialog
            v-model:proOverFlow="proOverFlowShow"
            :proOverFlowText="proOverFlowText"
            :centerText="centerText"
            @resetCenterText="centerText = false"
        />
    </div>
</template>

<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { setTopLocationhref } from "@/util/url";
import { onMounted, ref, reactive, watch } from "vue";
import type { ICreateAccountSetResult } from "../types";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import { getAACompanyId, getAACustomerId } from "@/util/baseInfo";
import { getCompanyList } from "@/util/getCompanyList";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { globalWindowOpenPage } from "@/util/url";
import { changeAS } from "../utils";
import { getServiceId } from "@/util/proUtils";
const props = defineProps({
    asId: {
        type: String,
        default: "",
    },
});
const emits = defineEmits(["cancelCreate"]);

const errorMsgShow = ref(false);
const errorMsg = ref("");
const asNameRef = ref();
let isProSystem = ref(window.isProSystem);
let isAccountingAgent = window.isAccountingAgent;
const showAccountingStandardTip = ref(false);
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const centerText = ref(false);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart)

const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
// let dataName = ''
// const nameList = ref()
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};

function asNameBlur() {
    asNameRef.value.activated = false;
    if (createData.asName.length == 0) {
        errorMsgShow.value = true;
        errorMsg.value = "亲，单位名称不能为空！";
        asNameRef.value.focus();
        return;
    }
    if (createData.asName.length < 5) {
        errorMsgShow.value = true;
        errorMsg.value = "亲，请输入完整的单位名称哦！";
        return;
    }
    if (createData.asName.length > 40) {
        errorMsgShow.value = true;
        errorMsg.value = "亲，单位名称最多40个字哦！";
        asNameRef.value.focus();
        return;
    }
    errorMsgShow.value = false;
    errorMsg.value = "";
}

function selectName(item: any) {
    createData.asName = item.value;
    createData.unifiedNumber = item.creditCode;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}

const yearList = ref<Number[]>([]);
const monthList = ref<Number[]>([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);

function getYearList() {
    let year = new Date().getFullYear();
    for (let i = 2000; i < year + 6; i++) {
        yearList.value.push(i);
    }
}

const createDataTemp = {
    asId: props.asId,
    asName: "",
    asStartYear: new Date().getFullYear(),
    asStartMonth: new Date().getMonth() + 1,
    unifiedNumber: "",
    accountingStandard: "1",
    isChangedStartDate: false,
    creditCode: "",
    aaCompanyId: 0,
    customerId: 0,
    scmAsId: 0,
    showScm: "1",
    taxType: "1",
}
let createData = reactive(Object.assign({}, createDataTemp));
function resetCreateData() {
    Object.assign(createData, createDataTemp);
}

function ValidateSelectedDateForEdit() {
    return ValidateSelectedDateInner(createData.asStartYear, createData.asStartMonth);
}

function ValidateSelectedDateInner(yearSelector: number, monthSelector: number) {
    let _msg = "";
    let _succeed = true;
    if (!yearSelector) {
        _msg = "亲，请选择账套启用年月";
        _succeed = false;
    } else {
        if (!monthSelector) {
            _msg = "亲，请选择账套启用年月";
            _succeed = false;
        }
    }
    return { result: _succeed, msg: _msg };
}

// 按钮状态
// 创建中
let creating = false;
// 是否可点击
let canClick = true;
//数量是否超限
let propOverFlowStatus = false;
let propOverFlowContent = "";
function SubmitAccountSet() {
    // 测试
    // createSuccessDialog.value = true;
    // return;
    if (propOverFlowStatus) {
        proOverFlowText.value = propOverFlowContent;
        proOverFlowShow.value = true;
        return;
    }
    if (!canClick) return;
    canClick = false;
    if (creating) {
        ElNotify({
            type: "warning",
            message: "账套正在添加中，请稍候",
        });
        canClick = true;
        return;
    }
    if (createData.asName === "") {
        ElNotify({
            type: "warning",
            message: "亲，单位名称不能为空！",
        });
        canClick = true;
        return;
    }
    if (createData.asName.match('[\\\\/*?:<>|~"]')) {
        ElNotify({
            type: "warning",
            message: '亲，单位名称不能包含\\/*?:<>|~"等特殊符号！',
        });
        canClick = true;
        return;
    }
    if (createData.asName.length < 5) {
        ElNotify({
            type: "warning",
            message: "亲，请输入完整的单位名称哦！至少5个字符~",
        });
        canClick = true;
        return;
    }
    if (createData.asName.length > 40) {
        ElNotify({
            type: "warning",
            message: "亲，单位名称最多40个字哦！",
        });
        canClick = true;
        return;
    }
    let _r = ValidateSelectedDateForEdit();
    if (!_r.result) {
        ElNotify({
            type: "warning",
            message: _r.msg,
        });
        canClick = true;
        return;
    }

    creating = true;
    const data = {
        accountingStandard: createData.accountingStandard,
        asId: createData.asId,
        asName: createData.asName,
        asStartMonth: createData.asStartMonth,
        asStartYear: createData.asStartYear,
        creditCode: createData.unifiedNumber, //重复字段
        isChangedStartDate: createData.isChangedStartDate,
        SubmitType: "New",
        AACompanyId: getAACompanyId(),
        CustomerId: getAACustomerId(),
        // 增值税种类 默认小规模
        taxType: createData.taxType,
        // 凭证审核 默认不审核
        checkNeeded: 0,
        // 资金模块  默认启用
        cashJournal: "1",
        // 关联进销存 默认启用
        showScm: "1",
        // 固定资产模块 默认启用
        fixedAsset: "1",
        // 统一社会信用代码 默认为空
        unifiedNumber: "",
        // 行业 默认空
        asIndustry: "",
    };
    SubmitAccountSetInner(data);
}

function SubmitAccountSetInner(data: any) {
    creating = true;
    let submitUrl = `/api/AccountSetOnlyAuth/V2?serviceid=${getServiceId()}`;
    request({
        url: submitUrl,
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    })
        .then((res: IResponseModel<ICreateAccountSetResult>) => {
            canClick = true;
            if (res.state == 1000) {
                ElNotify({
                    type: "success",
                    message: "亲，保存成功啦！",
                });
                localStorage.setItem("showCreateSuccessDialog", "true");
                if (isThirdPart.value) {
                    thirdPartNotify(thirtPartNotifyTypeEnum.accountSetCreateAccountSet, { asId: res.data.newAsid }).then(() => {
                        changeAS(res.data.newAsid);
                    });
                    return;
                }
                if (isLemonClient()) {
                    getLemonClient().setSelectedAccount(res.data.newAsid);
                    return;
                }
                setTopLocationhref(res.data.urlTarget);
                // setTopLocationhref(res.data.urlTarget);
                // 创建完返回账套列表页
                cancelCreate();
                return;
            } else if (res.state === 2000 && res.subState === 1 && res.msg === "Overflow") {
                ElNotify({
                    type: "warning",
                    message: "亲，您的应用创建账套的数量已经超过限额，请联系管理员!x！",
                });
            } else if (res.state === 2000 && res.subState === 8) {
                if(res.msg === "您的账套数量已超过已购买账套数量。"){
                    proOverFlowText.value = "您的账套数量已超过已购买账套数，建议您去增购~";
                    proOverFlowShow.value = true;
                }else{
                    ElConfirm(res.msg, true, () => {}, "提示", { confirmButtonText: "知道了", cancelButtonText: "" });
                }
            } else {
                if (res.msg !== null && res.msg !== undefined && res.msg !== "" && res.msg !== "Failed") {
                    if (res.msg === "亲，期初的借方累计或贷方累计存在数据，需要先删除才能修改为1月哦~") {
                        ElConfirm(res.msg);
                    } else if (res.msg === "您的账套数量已超过已购买账套数量。") {
                        proOverFlowText.value = "您的账套数量已超过已购买账套数，建议您去增购~";
                        centerText.value = true;
                        proOverFlowShow.value = true;
                    } else {
                        ElNotify({
                            type: "warning",
                            message: res.msg == "请使用管理员操作" ? "系统管理员才可以创建账套" : res.msg,
                        });
                    }
                } else {
                    ElNotify({
                        type: "error",
                        message: "亲，保存失败啦，请刷新重试！",
                    });
                }
            }
        })
        .catch((err: any) => {
            if (err.response?.status === 400) {
                //propOverFlow
                propOverFlowStatus = true;
                propOverFlowContent = err.response.data;
                proOverFlowText.value = err.response.data;
                thirdPartNotify(thirtPartNotifyTypeEnum.accountSetAccountSetOverflow).then(() => {
                    proOverFlowShow.value = true;
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "创建账套失败，请稍后重试！",
                });
            }
            // 新建失败才能继续点击
            canClick = true;
        })
        .finally(() => {
            creating = false;
        });
}

// 跳转到旧账导入
const gotoImportFromOther = () => {
    // 触发返回账套
    localStorage.setItem("isFromCreateAccountSet", "true");
    globalWindowOpenPage("/Settings/ImportFromOther", "旧账导入");
};
function showStartYearTip() {
    ElConfirm("有凭证、资产、资金、工资数据或存在结账期间，无法再修改账套启用年月。", true, () => {}, "账套启用年月说明");
}

function cancelCreate() {
    emits("cancelCreate");
    resetCreateData();
}
onMounted(() => {
    getYearList();
});

const divShowScm = ref(true);
watch(
    () => createData.accountingStandard,
    (val) => {
        // 企业会计准则，农村集体经济组织会计制度没有关联进销存
        if (val === "6" || val === "7") {
            divShowScm.value = false;
            createData.showScm = "0";
        } else {
            divShowScm.value = true;
            createData.showScm = "1";
        }
    }
);
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/Settings/CreatAccountSet.less";
:deep(.el-select-dropdown__list) {
    max-height: 200px;
}
:deep(.el-select-dropdown__item) {
    width: 50x;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    &.selected {
        background: #f6f6f6;
        color: var(--el-text-color-regular);
        // background: #44b449;
        // color: #fff;
    }
    &:hover {
        background: #44b449;
        color: #fff;
    }
}

.new-message-box {
    & .box-body {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        & .body-message {
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            width: 100%;
            & .body-message-title {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                white-space: normal;
                word-break: break-all;
            }
            & .body-message-content {
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 17px;
                margin-top: 10px;
                text-align: left;
                width: 100%;
                padding: 0;
                white-space: normal;
            }
        }
    }
}
</style>
