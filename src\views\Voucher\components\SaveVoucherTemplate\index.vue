<template>
    <el-dialog v-model="display" title="保存为模板" class="custom-confirm dialogDrag" center width="440px" @closed="closed">
        <div class="savevouchertemplate-container" v-dialogDrag>
            <div class="input-content">
                <div class="input-title">模板类型：</div>
                <div class="input-field">
                    <el-select 
                        v-model="vtType"
                        :filterable="true"
                        :filter-method="vtTypeFilterMethod"
                    >
                        <el-option 
                            v-for="vtType in showVtTypeList" 
                            :key="vtType.id" 
                            :value="vtType.id" 
                            :label="vtType.value"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="input-content">
                <div class="input-title">模板名称：</div>
                <div class="input-field">
                    <el-input v-model="vtName"></el-input>
                </div>
            </div>
            <div class="input-content">
                <div class="input-title">保存金额：</div>
                <div class="input-field">
                    <el-checkbox v-model="saveAmount" size="large" @change="changeSaveAmountStatus" />
                    <el-tooltip class="box-item" effect="dark" placement="top">
                        <img style="width:16px;height:16px" class="pl-10" src="@/assets/Scm/question.png" alt="" />
                        <template #content>
                            <span>1: 勾选时，会保存分录的金额;</span><br/>
                            <span>2: 不勾选时，则不存分录金额，会按分录金额<br/>存借贷方向;</span><br/>
                            <span>3: 若借贷金额=0，则按科目方向存借贷方向;</span>


                        </template>
                    </el-tooltip>
                </div>
            </div>

            <div class="buttons">
                <a class="button" @click="display = false">取消</a>
                <a class="button solid-button ml-10" @click="saveVoucherTemplate()">确定</a>
            </div>
        </div>
    </el-dialog>
</template>
<style lang="less" scoped>
@import "@/style/functions.less";

.savevouchertemplate-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .input-content {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 16px;

        .input-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            text-align: right;
        }
        .input-field {
            width: 180px;
            text-align: left;
            .detail-el-input(180px, 32px);
            .detail-el-select(180px, 32px);
        }
    }

    .buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        margin-top: 26px;
        border-top: 1px solid var(--border-color);
    }
}

body[erp] .custom-confirm .el-dialog__body .buttons {
    display: flex;
    justify-content: flex-end;
}
</style>
<script setup lang="ts">
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { onUnmounted } from "vue";
import { onMounted, ref, watchEffect } from "vue";
import { commonFilterMethod } from "@/components/Select/utils";

const emit = defineEmits<{
    (e: "saveVoucherTemplate", vtId: number, vtName: string, saveAmount:boolean): void;
    (e: "closed"): void;
}>();
const vtTypeList = ref(new Array<{ id: number; value: string }>());
const display = ref(false);
const vtType = ref(0);
const vtName = ref("");
const saveAmount = ref(localStorage.getItem("voucherTemplateSaveAmount") === "false" ? false : true);
const changeSaveAmountStatus = (v: boolean) => {
    localStorage.setItem("voucherTemplateSaveAmount", String(v));
};
function getVtTypeList() {
    request({
        url: "/api/VoucherTemplate/GetVtTypeList",
        method: "post",
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            for (let id in res.data) {
                vtTypeList.value.push({ id: parseInt(id), value: res.data[id] });
            }
        }
    });
}
getVtTypeList();

function saveVoucherTemplate() {
    if (!vtType.value) {
        ElNotify({
            message: "亲，请选择模板类型！",
            type: "warning",
        });
        return;
    }
    if (!vtName.value) {
        ElNotify({
            message: "亲，请输入模板名称！",
            type: "warning",
        });
        return;
    }
    if (vtName.value.length > 64) {
        ElNotify({
            message: "模板名称不能超过64个字！",
            type: "warning",
        });
        return;
    }
    emit("saveVoucherTemplate", vtType.value, vtName.value,saveAmount.value);
}

function showSaveVoucherTemplateDialog() {
    display.value = true;
    if (vtTypeList.value.length > 0) {
        vtType.value = vtTypeList.value[0].id || 0;
    }
    vtName.value = "";
}

function close() {
    display.value = false;
}

function closed() {
    emit("closed");
}

onMounted(() => {
    window.addEventListener("reloadVoucherTemplateType", getVtTypeList);
});

onUnmounted(() => {
    window.removeEventListener("reloadVoucherTemplateType", getVtTypeList);
});

defineExpose({
    showSaveVoucherTemplateDialog,
    close,
});

const showVtTypeList = ref<any[]>([]);
watchEffect(() => { 
    showVtTypeList.value = JSON.parse(JSON.stringify(vtTypeList.value));
});
function vtTypeFilterMethod(value: string) {
    showVtTypeList.value = commonFilterMethod(value, vtTypeList.value, 'value');
}
</script>
