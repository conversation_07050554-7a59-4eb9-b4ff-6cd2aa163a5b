import { reactive, ref } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import { getPeriodsApi } from "@/api/period";
import type { IResponseModel } from "@/util/service";
import type { IPeriod } from "@/api/period";
import { localStoragePeriod } from "@/util/period";
const { changePeriod, changePeriodRange, getCurrentPeriod, getPeriodRange } = localStoragePeriod();
export const useAccountPeriodStore = defineStore("accountPeriod", () => {
    const period = reactive({
        startPid: 0,
        endPid: 0,
    });
    const periodList = ref<IPeriod[]>([]);
    const getPeriods = () => {
        return new Promise<IPeriod[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getPeriodsApi()
                    .then((res: any) => {
                        const data = res as IResponseModel<IPeriod[]>;
                        if (data.state === 1000) {
                            periodList.value = data.data;
                            period.startPid = data.data[0].pid ?? 0;
                            const lastPosition = data.data.length - 1;
                            period.endPid = (data.data[lastPosition] as IPeriod).pid ?? 0;
                            if (!getCurrentPeriod()) {
                                changePeriods(period.endPid.toString());
                            }
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };
    const changePeriods = (startPid: string, endPid?: string) => {
        endPid ? changePeriodRange(startPid, endPid) : changePeriod(startPid);
    };

    return { period, periodList, getPeriods, changePeriods, getCurrentPeriod, getPeriodRange};
});

export function useAccountPeriodStoreHook() {
    return useAccountPeriodStore(store);
}
