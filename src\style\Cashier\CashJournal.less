@import "../Functions.less";

.add-content {
    .add-main {
        box-sizing: border-box;
        padding: 37px 70px;
        .add-input-item {
            margin-top: 20px;
            > span.label {
                text-align: right;
                width: 85px;
                display: inline-block;
                margin-right: 5px;
            }
            .detail-el-input(200px, 32px);
            &:first-child {
                margin-top: 0;
            }
        }
    }
    .buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        box-sizing: border-box;
        border-top: 1px solid var(--border-color);
    }
}
.my-data-picker {
    :deep(.el-date-editor--date) {
        height: 32px;
        width: 132px;
        box-sizing: border-box;
        .el-input__wrapper {
            padding: 0;
            padding-left: 8px;
            .el-input__inner {
                border: none;
            }
            .el-input__prefix {
                position: absolute;
                top: 2px;
                bottom: 2px;
                right: 3px;
                width: 18px;
                height: 86%;
                // background-color: #fff;
                padding-left: 2px;
            }
        }
    }
}

.handle {
    a {
        margin: 0px !important;
        margin-right: 10px !important;
        &:last-child {
            margin-right: 0 !important;
        }
    }
}
.paymethod-select {
    .detail-el-select(110px);
    :deep(.el-select) {
        input.el-input__inner {
            border: none;
        }
    }
}
.project-select {
    .detail-el-select(164px);
    :deep(.el-select) {
        input.el-input__inner {
            border: none;
        }
    }
}
.paymentMethod-select {
    .detail-el-select(157px);
    :deep(.el-select) {
        input.el-input__inner {
            border: none;
        }
    }
}
.content {
    .main-center {
        padding: 20px;
        padding-top: 0;
        :deep(.el-table__header) {
            color: var(--font-color);
            font-size: var(--table-title-font-size);
            line-height: var(--table-title-line-height);
            .el-table__cell {
                padding: 0;
            }
            .cell {
                padding: 0 4px;
            }
        }
        :deep(.el-table__body) {
            .el-table__row {
                height: 37px;
                .el-table__cell {
                    padding: 0;
                    position: relative;
                }
                .cell {
                    padding: 2px 4px;
                }
            }
        }
    }
}
.w-16 {
    width: 16px;
}

.cd-accountList {
    .detail-el-select(226px, 28px);
    :deep(.el-select-dropdown__item) {
        padding: 0;
        text-align: left;
    }

    :deep(.new-cd-account) {
        line-height: 34px;
        height: 34px;
        display: flex;
        justify-content: center;
        .link {
            font-size: 12px;
            color: var(--font-color);
            display: flex;
            align-items: center;
            &::before {
                width: 14px;
                height: 15px;
                content: "";
                margin-right: 3px;
                background-image: url("@/assets/Icons/add.png");
                background-size: 14px 15px;
            }
        }
        .link:hover {
            color: var(--link-color);
        }
    }
}
.line-item {
    :deep(.my-input-small) {
        width: 132px;
    }
    :deep(.my-input-normal) {
        width: 300px;
    }
    .item {
        width: 300px;
    }
    .line-item-field {
        & > input {
            .detail-original-input(132px, 32px);
        }
    }
}

.content {
    .slot-content {
        .slot-title {
            border-bottom: none;
        }
        .slot-mini-content {
            &.voucher-setting {
                flex: 1;
                overflow: hidden;
                :deep(.el-tabs) {
                    .el-tabs__nav-scroll {
                        width: 1000px;
                        margin: 0 auto;
                    }
                }
            }
        }
    }
}

:deep(.el-select) {
    .el-select__caret::before {
        background: none !important;
    }
}

.editVoucherShow-content {
    .content-main {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
    }
    &.erp {
        .content-main {
            border-bottom: none;
        }
        .buttons {
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            padding: 20px 10px;
            .button {
                margin: 0 10px;
            }
        }
    }
}
.line-item-field {
    :deep(.el-select) {
        .detail-placehoder-color(#999);
        width: 300px;
    }
    :deep(input[type="number"]) {
        .detail-spin-button();
    }
    .vgid-line {
        display: flex;
        .el-input {
            width: 84px;
        }
    }
}

.cd-accountList {
    :deep(.el-scrollbar) {
        .el-select-dropdown__wrap {
            margin-bottom: 33px;
        }
        .el-select-dropdown__list {
            .el-select-dropdown__item {
                &.hover {
                    background-color: var(--main-color);
                    color: var(--white);
                }
                &.all-option {
                    border-top: 1px solid var(--border-color);
                    display: flex;
                    align-items: center;
                    width: 100%;
                    justify-content: space-between;
                    position: absolute;
                    bottom: 0;
                    &.hover {
                        background-color: var(--white);
                        color: var(--font-color);
                    }
                    &:hover {
                        background-color: var(--white) !important;
                        color: var(--main-color) !important;
                    }
                    .all-option-switch {
                        display: flex;
                        align-items: center;
                        .el-switch {
                            height: 20px;
                            margin-right: 2px;
                            .el-switch__action {
                                top: 1px;
                            }
                        }
                    }
                }
            }
        }
    }
}

.fc-account-history-data-sync-content {
    .data-sync-main {
        .set-font;
        padding: 20px 30px;
        text-align: left;
        .data-sync-title {
            font-size: var(--h3);
            margin-bottom: 12px;
            line-height: 25px;
        }
        :deep(.data-sync-title) {
            .green {
                color: #6ed773;
            }
        }
        .data-sync-tip {
            font-size: var(--h5);
            color: #666666;
            line-height: 20px;
            display: flex;
            align-items: flex-start;
            img {
                width: 14px;
                margin-top: 5px;
                margin-right: 5px;
            }
        }
    }
    .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
        font-size: 0;
        .button {
            width: 120px;
        }
    }
}
.line-item-field {
    :deep(.select-checkbox) {
        .top-box {
            input {
                padding-right: 24px;
            }
            .bg-click {
                width: 14px;
                padding-right: 10px;
            }
        }
    }

    :deep(.el-select-v2) {
        width: 300px !important;
    }
}
.main-tool-left {
    .tip-title {
        white-space: normal;
        display: flex;
        height: 32px;
        word-break: keep-all;
        align-items: center;
    }
    :deep(.search-info-selector) {
        white-space: nowrap;
    }
}
:deep(.header-operate) {
    width: 100%;
}
:deep(.selected-table-content) {
    .el-table__header .cell {
        padding: 0 8px !important;
    }
    .el-table__body .el-table__row .cell {
        padding: 2px 8px !important;
    }
}
