import type { ModuleType } from "@/views/ERecord/utils";

export interface IFileInfo {
    fileId: number;
    fileName: string;
    fileSize: number | string;
    fileType: number;
    relativePath: string;
}

export interface IERecordTableInfo {
    fileId: number;
    fileName: string;
    fileSize: number;
    fileType: number;
    relativePath: string;
    fileCategory: number;
    createdDate: string;
    createdByName: string;
    checkState: number;
    relatedDocumentTypes: ModuleType[];
    voucherGroupStr: string;
    source: any[];
    vid: string;
}
export interface IMobileUploadData {
    Asid: number;
    Channel: string;
    FileCategory: number;
    FileId: number;
    FileName: string;
    FileSize: string;
    FileType: number;
    Path: string;
    PathUrl: string;
}

export interface IGetAttachFileListBack {
    result: boolean;
    parentId: number;
    data: Array<any>;
}
