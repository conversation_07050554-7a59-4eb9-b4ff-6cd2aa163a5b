<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef"></PaginationPeriodPicker>
                            <CheckOutTooltip v-if="periodIsCheckOut"></CheckOutTooltip>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <el-checkbox
                                v-model="searchInfo.classification"
                                label="显示上年数和本年数"
                                @change="handleSearch"
                            ></el-checkbox>
                            <span class="ml-16">
                                <Dropdown btnTxt="打印" class="w-100" :downlistWidth="100" v-permission="['incomeanddistrubutionsheet-canprint']">
                                    <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                    <li @click="handlePrint(1)">批量打印</li>
                                    <li @click="handlePrint(2)">打印设置</li>
                                </Dropdown>
                            </span>
                            <span class="ml-16">
                                <Dropdown btnTxt="导出" class="w-100" :downlistWidth="100" v-permission="['incomeanddistrubutionsheet-canexport']">
                                    <li @click="handleExport('single')">当前报表数据</li>
                                    <li @click="handleExport('batch')">批量导出</li>
                                </Dropdown>
                            </span>
                            <a
                                class="button solid-button ml-10"
                                v-if="checkPermission(['incomeanddistrubutionsheet-canedit']) && resetButtonShow"
                                @click="resetFormula"
                            >
                                重置公式
                            </a>
                            <a
                                class="button ml-16"
                                @click="handleShare"
                                v-if="!isHideBarcode && checkPermission(['incomeanddistrubutionsheet-canshare'])"
                            >
                                微信分享
                            </a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <el-table
                            :class="{ 'erp-table': isErp }"
                            :data="tableData"
                            border
                            fit
                            stripe
                            scrollbar-always-on
                            highlight-current-row
                            class="custom-table"
                            row-key="lineID"
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                            @header-dragend="headerDragend"
                        >
                            <el-table-column 
                                label="项目" 
                                :min-width="340" 
                                align="left" 
                                headerAlign="center"
                                prop="proName"
                                :width="getColumnWidth(setModule, 'proName')"
                            >
                                <template #default="scope">
                                    <div :class="'level' + (scope.row.showLevel + 1)" :title="scope.row.proName">
                                        <span>{{ scope.row.proName.trim() }}</span>
                                        <template v-if="assertShowEquationEdit(scope.row)">
                                            <div
                                                class="link"
                                                v-permission="['incomeanddistrubutionsheet-canedit']"
                                                @click="handleEditEquation(scope.row)"
                                            >
                                                编辑公式
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                            <template v-if="showLastYear">
                                <el-table-column 
                                    label="本年数" 
                                    :min-width="280" 
                                    align="right" 
                                    header-align="right"
                                    prop="yearAmount"
                                    :width="getColumnWidth(setModule, 'yearAmount')"
                                >
                                    <template #default="scope">
                                        <TableAmountItem
                                            :amount="scope.row.yearAmount"
                                            :formula="calcFormula(scope.row.note, 0)"
                                            :line-number="scope.row.lineNumber"
                                        ></TableAmountItem>
                                    </template>
                                </el-table-column>
                                <el-table-column 
                                    label="上年数" 
                                    :min-width="280" 
                                    align="right" 
                                    header-align="right"
                                    :resizable="false"
                                >
                                    <template #default="scope">
                                        <TableAmountItem
                                            :amount="scope.row.lastYearAmount"
                                            :formula="calcFormula(scope.row.note, 1)"
                                            :line-number="scope.row.lineNumber"
                                        ></TableAmountItem>
                                    </template>
                                </el-table-column>
                            </template>
                            <template v-else>
                                <el-table-column 
                                    label="本月数" 
                                    :min-width="280" 
                                    align="right" 
                                    header-align="right"
                                    prop="amount"
                                    :width="getColumnWidth(setModule, 'amount')"
                                >
                                    <template #default="scope">
                                        <TableAmountItem
                                            :amount="scope.row.amount"
                                            :formula="calcFormula(scope.row.note, 1)"
                                            :line-number="scope.row.lineNumber"
                                        ></TableAmountItem>
                                    </template>
                                </el-table-column>
                                <el-table-column 
                                    label="本年累计数" 
                                    :min-width="280" 
                                    align="right" 
                                    header-align="right"
                                    :resizable="false"
                                >
                                    <template #default="scope">
                                        <TableAmountItem
                                            :amount="scope.row.yearAmount"
                                            :formula="calcFormula(scope.row.note, 0)"
                                            :line-number="scope.row.lineNumber"
                                        ></TableAmountItem>
                                    </template>
                                </el-table-column>
                            </template>
                        </el-table>
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <EditEquation
                        ref="editRef"
                        :class="[{ 'edit-content': !isErp }, 'slot-mini-content']"
                        :statement-id="editData.statementId"
                        :line-id="editData.lineId"
                        :pid="editData.pid"
                        :title="editData.title"
                        month-title="本月数"
                        year-title="本年累计数"
                        :value-type-options="valueTypeOptions"
                        @handle-submit-success="handleEditSubmitSuccess"
                        @handle-cancel="handleEditCancel"
                    ></EditEquation>
                </div>
            </template>
        </ContentSlider>
    </div>
    <PrintOrExportDialog
        :show-dialog="showDialog"
        :type="dialogType"
        :pid="searchInfo.pid"
        :fromStatement="ReportTypeEnum.IncomeAndDistributionSheet"
        @close="handleDialogClose"
    />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="收益及收益分配表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "IncomeAndDistributionSheet",
};
</script>
<script setup lang="ts">
import { ref, computed, reactive, nextTick, onMounted, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { globalExport, getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { PeriodStatus } from "@/api/period";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import usePrint from "@/hooks/usePrint";
import { checkPermission } from "@/util/permission";
import { share } from "@/views/Statements/utils";

import type { IValueTypeOption } from "../types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "IncomeAndDist";
interface ITableData {
    statementID: number;
    lineID: number;
    lineType: number;
    partentID: number;
    proName: string;
    lineNumber: number;
    amount: number;
    yearAmount: number;
    lastYearAmount: number;
    note: string;
}

const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const editRef = ref<InstanceType<typeof EditEquation>>();

const accountSet = useAccountSetStore().accountSet;
const slots: Array<string> = ["main", "edit"];
const currentSlot = ref<"main" | "edit">("main");
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);

const periodStore = useAccountPeriodStore();
const searchInfo = reactive({
    pid: Number(periodStore.getCurrentPeriod()),
    classification: false,
});
const tableData = ref<Array<ITableData>>([]);
const loading = ref(false);
const emptyText = ref("");
const showLastYear = ref(false);
function toggleTableLabel() {
    showLastYear.value = searchInfo.classification;
}

function handleSearch() {
    const params = { pid: searchInfo.pid, isTax: searchInfo.classification };
    loading.value = true;
    emptyText.value = " ";
    request({
        url: "/api/IncomeDistributionSheet",
        method: "get",
        params: params,
    })
        .then((res: IResponseModel<Array<ITableData>>) => {
            loading.value = false;
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "获取数据失败，请稍后重试" });
                return;
            }
            toggleTableLabel();
            tableData.value = res.data;
            if (!tableData.value.length) {
                emptyText.value = "暂无数据";
            }
        })
        .catch(() => {
            loading.value = false;
            ElNotify({ type: "warning", message: "获取数据失败，请稍后重试" });
        });
}


function getDefaultParams() {
    return {
        pid: searchInfo.pid,
        isTax: searchInfo.classification,
    };
}
const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "incomeDistributionSheet",
    `/api/IncomeDistributionSheet/Print`,
    {},
    false,
    false,
);
function handleExport(type: "single" | "batch") {
    if (type === "single") {
        globalExport(`/api/IncomeDistributionSheet/Export?`+ getUrlSearchParams(getDefaultParams()));
    } else {
        dialogType.value = "export";
        showDialog.value = true;
    }
}
function handleDialogClose() {
    showDialog.value = false;
    dialogType.value = "export";
}
function handleShare() {
    request({
        url: `/api/IncomeDistributionSheet/Share?`+ getUrlSearchParams(getDefaultParams()),
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "分享失败，请联系客服或管理员～" });
                return;
            }
            const shareReportHost =
                window.accountSrvHost +
                "/api/WxPay/MakeQRCode.ashx?data=" +
                window.shareReportHost +
                "/ShareReport/" +
                res.data +
                "&CurrentSystemType=1";
            share(shareReportHost);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "分享失败，请联系客服或管理员～" });
        });
}

const reportStatementId = computed(() => accountSet!.accountingStandard * 1000 + ReportTypeEnum.IncomeAndDistributionSheet);
const editData = reactive({
    statementId: reportStatementId.value,
    lineId: 0,
    pid: 0,
    title: "",
});
function handleEditEquation(data: ITableData) {
    if (data.lineType === 1) {
        if (data.lineID === ********) return;
        editData.statementId = data.statementID;
        editData.lineId = data.lineID;
        editData.pid = searchInfo.pid;
        editData.title = data.proName;
        nextTick().then(() => {
            editRef.value?.init();
            currentSlot.value = "edit";
        });
    }
}
const valueTypeOptions: IValueTypeOption[] = [
    { value: "4", label: "发生额" },
    { value: "2", label: "借方发生额" },
    { value: "3", label: "贷方发生额" },
];
function handleEditSubmitSuccess() {
    currentSlot.value = "main";
    handleSearch();
    checkHasCustomEqutions();
}
function handleEditCancel() {
    currentSlot.value = "main";
}

const periodIsCheckOut = computed(() => periodRef.value?.periodStatus === PeriodStatus.CheckOut);
function assertShowEquationEdit(row: ITableData) {
    return !periodIsCheckOut.value && row.lineType === 1 && row.lineID !== ********;
}

function calcFormula(note: string, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = note?.split("|")[0] ?? "";
            break;
        case 1:
            formula = note?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}

const resetButtonShow = ref(false);
function checkHasCustomEqutions() {
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${reportStatementId.value}`,
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                resetButtonShow.value = res.data;
            } else {
                resetButtonShow.value = false;
            }
        })
        .catch(() => {
            resetButtonShow.value = false;
        });
}
function resetFormula() {
    ElConfirm(
        "确认删除此报表所有自定义公式？<div style='margin-top: 10px;'>重置公式仅影响未结账期间的报表数据</div>",
        false,
        () => {},
        "重置此报表公式"
    ).then((r: boolean) => {
        if (r) {
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${reportStatementId.value}`,
                method: "post",
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({ type: "success", message: "已经成功重置" });
                        resetButtonShow.value = res.data;
                        handleInit();
                    } else {
                        ElNotify({ type: "warning", message: res.msg || "重置失败，请稍后重试" });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "重置失败，请稍后重试" });
                });
        }
    });
}

function handleInit() {
    handleSearch();
    checkHasCustomEqutions();
}
onMounted(() => {
    handleInit();
});

watch(
    () => searchInfo.pid,
    () => {
        handleSearch();
    }
);
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.content {
    display: flex;
    justify-content: center;
    align-items: center;
    .main-content {
        width: 1000px !important;
    }
}
.edit-content {
    width: 1100px !important;
}
.main-tool-right {
    span.ml-16 {
        :deep(.w-100) {
            width: 98px;
        }
    }
}
</style>
