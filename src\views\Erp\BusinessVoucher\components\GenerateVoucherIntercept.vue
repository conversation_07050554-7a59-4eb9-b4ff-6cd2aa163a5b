<template>
    <el-dialog destroy-on-close v-model="display" title="提示" width="532px" class="custom-confirm" modal-class="modal-class">
        <div class="tip-content">
            <div class="tip-main">
                <div class="tip-label">
                    所选数据
                    <span class="highlight-red">包含发票数据</span>
                    ，系统推荐优先采用对应的业务单据来生成会计凭证。当发票和对业务单据的凭证模板相同时，使用发票生成凭证可能会
                    <span class="highlight-red">导致凭证重复</span>
                </div>
                <div class="tip-template mt-8">
                    <div class="row">例如：</div>
                    <div class="row" v-for="(item, index) in example" :key="index">
                        <div class="l">{{ item.l }}</div>
                        <div class="r">{{ item.r }}</div>
                    </div>
                </div>
                <div class="tip-info">
                    <div class="row">1.对应的业务单据是指：采购入库单、采购退货单、销售出库单、销售退货单、其他收入单、其他支出单</div>
                    <div class="row">
                        2.可到
                        <span :class="{ link: checkHasPermission() }" @click="linkToTemplate">业务凭证模板</span>
                        中确认单据和发票的模板是否重复
                    </div>
                    <div class="row">3.确认凭证模板不重复后，可选择继续生成凭证</div>
                </div>
                <div class="not-tip">
                    <el-checkbox v-model="checked" @change="handleSetNotTipAgain">不再提示</el-checkbox>
                </div>
            </div>
            <div class="buttons">
                <span class="button mr-20" @click="display = false">取消</span>
                <span class="button solid-button" @click="continueGenerate">继续生成凭证</span>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage } from "@/util/url";
const example = [
    { l: "采购入库模板", r: "进项发票模板" },
    { l: "借: 存货科目", r: "借: 存货科目" },
    { l: "借: 应交税费-应交增值税-进项税额", r: "借: 应交税费-应交增值税-进项税额" },
    { l: "贷: 应付账款科目", r: "贷: 应付账款科目" },
];
const display = ref(false);
const checked = ref(false);
const storageKey = "generateVoucherIntercept-notTip-" + getGlobalToken();
function handleSetNotTipAgain() {
    checked.value ? localStorage.setItem(storageKey, "1") : localStorage.removeItem(storageKey);
}
const emit = defineEmits<{
    (event: "continue-generate", params: any): void;
}>();
function continueGenerate() {
    display.value = false;
    emit("continue-generate", params);
}
function checkHasPermission() {
    return checkPermission(["businessvouchertemplate-canview"]);
}
function linkToTemplate() {
    if (!checkHasPermission()) return;
    globalWindowOpenPage("/Erp/BusinessVoucherTemplate", "业务凭证模板");
}
let params: any = null;
function showDialog(_params: any) {
    params = null;
    if (localStorage.getItem(storageKey) === "1") return;
    params = _params;
    display.value = true;
}
defineExpose({ showDialog });
</script>

<style lang="less" scoped>
.tip-content {
    text-align: left;
    .tip-main {
        padding: 15px 32px;
    }
    .tip-template {
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 2px;
        margin-bottom: 12px;
        .row {
            display: flex;
            justify-content: space-between;
            line-height: 22px;
            text-align: left;
            > div {
                flex: 1;
            }
        }
    }
    .tip-info {
        padding-left: 20px;
        font-size: 13px;
        color: #666666;
        line-height: 20px;
        background: url("@/assets/Icons/warn.png") no-repeat 0 4px;
        background-size: 14px;
    }
    .not-tip {
        margin-top: 12px;
    }
}
</style>
