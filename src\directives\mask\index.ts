import type { Directive, DirectiveBinding, App } from "vue"
import { createApp, h } from "vue"
import Mask from "@/components/Mask/index.vue"

// Define extended element type with our custom properties
interface ExtendedHTMLElement extends HTMLElement {
  _container: HTMLDivElement
  _app: App | null
  _isGlobal: boolean
}

function createVNode(el: ExtendedHTMLElement, binding: DirectiveBinding) {
  const app = createApp({
    render: () => h(Mask, { message: binding.value.message }),
  })
  el._app = app

  // Check if global is true to mount to body instead of target element
  if (binding.value.global) {
    document.body.appendChild(el._container)
    el._isGlobal = true
  } else {
    el.appendChild(el._container)
    el._isGlobal = false
  }
  app.mount(el._container)
}

export const mask: Directive = {
  beforeMount(el: ExtendedHTMLElement) {
    const container = document.createElement("div")
    el._container = container
    el._app = null
    el._isGlobal = false
  },
  mounted(el: ExtendedHTMLElement, binding: DirectiveBinding) {
    if (binding.value.visible) {
      createVNode(el, binding)
    }
  },
  updated(el: ExtendedHTMLElement, binding: DirectiveBinding) {
    if (binding.value.visible) {
      if (!el._app) {
        createVNode(el, binding)
      }
    } else {
      if (el._app) {
        el._app.unmount()
        el._app = null
        el._container.remove()
      }
    }
  },
  unmounted(el: ExtendedHTMLElement) {
    if (el._app) {
      el._app.unmount()
      el._app = null
    }
    if (el._container) {
      // Make sure to remove the container from the correct parent
      if (el._isGlobal && document.body.contains(el._container)) {
        document.body.removeChild(el._container)
      } else {
        el._container.remove()
      }
    }
  },
}
