import Big from "big.js";

export const formatInitialMoney = (value: number | string | undefined) => {
    if (value == 0 || !value) return "";
    if (value) {
        if (typeof value === "number" && !isNaN(value) && value !== 0) {
            return value.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        } else if (typeof value === "string") {
            if (value.includes(",")) {
                value = value.replace(/,/g, "");
            }
            return Big(value)
                .toFixed(2)
                .toString()
                .replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        }
    }
    return "";
};

export const trucateDecimalStrZero = (value: number | null | string) => {
    if (value == undefined || value == null || value == "") {
        return "";
    }
    value = value
        .toString()
        .replace(/(\.\d*?)([,0]+)$/g, "$1")
        .replace(/\.$/, "");
    if (value == "") {
        return "0";
    } else {
        return value;
    }
};
export const isZero = (value: any, precision: number) => {
    if (value == 0 || !value) {
        return true;
    } else {
        if (Number(parseFloat(value).toFixed(precision)) === 0) {
            return true;
        } else {
            return false;
        }
    }
};
export const amountFormat = (value: any) => {
    if (isZero(value, 2)) {
        return "";
    }

    return formatInitialBalanceMoney(value);
};
export const formatInitialBalanceMoney = (value: string | number, precision: number = 2) => {
    value = value.toString();
    if (value == "0" || !value) return "";
    if (value.includes(",")) {
        value = value.replace(/,/g, "");
    }
    const formattedValue = Big(value)
        .toFixed(precision)
        .replace(/(^|\s)\d+/g, (m: string) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','));
    return formattedValue;
};

//判断是否是数字或字母
export const isNumberOr_Letter = (s: string) => {
    const regu = "^[0-9a-zA-Z_]+$";
    const re = new RegExp(regu);
    if (re.test(s)) {
        return true;
    } else {
        return false;
    }
};
export const aaCheckQuote = (str: string) => {
    if (str.match("[\\\\:'\"\\n]")) {
        return true;
    }
    return false;
};

// 判断字符串是否正确数字
export const isStringNaN = (str: string) => {
    str = str.trim().replace(/,/g, "");
    return !/^(-)?\d+(\.\d+)?$/.test(str);
};

export const formatOnlyNum = (value: number | string | undefined) => {
    if (value) {
        if (typeof value === "number" && !isNaN(value)) {
            return value.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        } else if (typeof value === "string") {
            // const numberValue = isNaN(Number(value)) ? parseFloat(value.replace(/[^\d.]/g, "")) : Number(value);
            if (value == "0" || isStringNaN(value)) {
                return "";
            } else {
                return Big(value)
                    .toFixed(2)
                    .replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
            }
        }
    }
    return "";
};

function unformatvalue(value: string) {
    return parseFloat(value.replace(/[^0-9.-]+/g, ""));
}

function formatNumber(value: number, decimalPlaces: number) {
    if (decimalPlaces === 2) {
        return Math.round(value * 100) / 100;
    }
    return parseFloat(value.toFixed(decimalPlaces));
}

export const unformat = (value: string | number) => {
    if (value == "" || value == null) {
        return "0";
    }
    // const vf = unformatvalue(value + "");
    // formatNumber((vf + 1 - 1)+'', 2)
    // Math.round((parseFloat(value+'') + Number.EPSILON) * 100) / 100
    // Math.round(value * 100) / 100
    // value.replace(/[^0-9.-]+/g, "")
    // return  _.round(vf, 2);

    return new Big(String(value).replace(/[^0-9.-]+/g, ""));
};
export const unformat8 = (value: string | number) => {
    if (value == "" || value == null) {
        return 0;
    }
    const vf = unformatvalue(value + "");
    return formatNumber(vf + 1 - 1, 8);
};

const MaxLength = "10000000000000";
export const isRightNum = (v: string) => {
    // v = Math.abs(v);
    // return v > MaxLength;
    return new Big(v).abs().gt(new Big(MaxLength));
};

export const isUnBalance = (oldValue: string, newValue: string) => {
    if (oldValue == null && !isStringNaN(newValue)) {
        return true;
    }
    if (isStringNaN(oldValue) || isStringNaN(newValue)) {
        return false;
    } else {
        if (new Big(oldValue).round(2).toString() === new Big(newValue).round(2).toString()) {
            return false;
        } else {
            if (new Big(oldValue).abs().gt(new Big(MaxLength))) {
                return false;
            }
            return true;
        }
    }
};
export const isUnBalance8 = (newValue: number, oldValue: number) => {
    if (isNaN(oldValue) || isNaN(newValue)) {
        return false;
    } else {
        if (oldValue.toFixed(8) == newValue.toFixed(8)) {
            return false;
        } else {
            // ~~newValue.toFixed(8) > MaxLength
            if (new Big(newValue).abs().cmp(new Big(MaxLength)) >= 0) {
                return false;
            }
            return true;
        }
    }
};
// 解析格式化数字
export function parseFormattedNumber(value: any) {
    if (typeof value === "number") {
        return String(value);
    } else {
        if (value === "") {
            return "0";
        } else {
            // 去除千位分隔符
            value = value.includes(",") ? value.replace(/,/g, "") : value;
            // const numberString = value.replace(/,/g, "");
            // 解析成数字
            // const parsedNumber = parseFloat(numberString);

            // 检查解析后的结果是否为有效数字，如果是则返回解析后的数字，否则返回 NaN
            // return isNaN(parsedNumber) ? NaN : parsedNumber;
            return value;
        }
    }
}
// 数量格式化
export function quantityFormat(value: any) {
    if (isZero(value, 8)) {
        return "";
    }
    return trucateDecimalStrZero(formatInitialBalanceMoney(value, 8));
}

export const bigPlus = (val1: number | string, val2: number | string) => {
    return new Big(parseFormattedNumber(val1)).plus(new Big(parseFormattedNumber(val2))).toString();
};

export const bigMinus = (val1: number | string, val2: number | string) => {
    return new Big(parseFormattedNumber(val1)).minus(new Big(parseFormattedNumber(val2))).toString();
};

// 先加后减
export const bigPlusMinus = (val1: Big.Big | "0", val2: Big.Big | "0", val3: Big.Big | "0") => {
    return new Big(val1).plus(new Big(val2)).minus(new Big(val3)).toString();
};
