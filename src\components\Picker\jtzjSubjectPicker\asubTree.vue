<template>
    <el-scrollbar :height="300" class="scrollbar-container">
        <el-tree
            :class="isErp ? 'erp-tree' : ''"
            :data="data"
            :props="defaultProps"
            node-key="id"
            :default-expand-all="true"
            :expand-on-click-node="false"
            :highlight-current="true"
            :current-node-key="(sbj_id as string)"
            :empty-text="emptyText"
            @node-click="handleNodeClick"
        >
            <template #default="{ data }">
                <span class="custom-tree-node">
                    <span :class="data.children ? 'tree-icon tree-folder tree-folder-open' : 'tree-icon tree-file'"></span>
                    <span class="tree-title">{{ data.asubCode + ' ' + data.asubName }}</span>
                </span>
            </template>
        </el-tree>
    </el-scrollbar>
</template>

<script lang="ts" setup>
import type { IAccountSubjectModel } from "@/api/accountSubject.js";
import { request } from "@/util/service";
import { ElMessage, tagEmits } from "element-plus";
import { inject, onMounted, ref, nextTick, watchEffect } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { popoverHandleCloseKey, updateAsubCodeKey } from "./symbols";
import { ElNotify } from "@/util/notify";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { checkinvalidasubid } from "./utils";
const props = defineProps({
    asubType: { type: Number, required: true },
});

const isErp = ref(window.isErp)
const accountingStandard = ref(0);
accountingStandard.value = Number(useAccountSetStore().accountSet?.accountingStandard);
const data = ref(new Array<IAccountSubjectModel>());
const subjectValue = inject("subjectValue");
const checkFA = inject("checkFA") as boolean;
const sbj_id = ref(subjectValue);
const clickAsubWithtype = inject("clickAsubWithtype") as Function;
const updateAsubCode = inject(updateAsubCodeKey);
const popoverHandleClose = inject(popoverHandleCloseKey) as Function;
const checkValid = inject("checkValid") as boolean;

const accountSubjectStore = useAccountSubjectStore();
const emptyText = ref("");
const asubList = ref(new Array<{ asubType: number; asubList: Array<IAccountSubjectModel> }>());

function getTreeData(){
    let list = asubList.value.filter((i) => i.asubType === props.asubType)[0]?.asubList;
    data.value = list;
    emptyText.value = data.value.length === 0 ? "暂无数据" : "  ";
//     request({
//     url: `/api/SubsidiaryLedger/Tree?ASUB_TYPE=${props.asubType}&canUse=1&leafNode=1&ifls=1&isSalary=1`,
//     method: "get",
// })
//     .then((res: any) => {
//         if (res.state === 1000) {
//             data.value = JSON.parse(res.data);
//             emptyText.value = data.value.length === 0 ? "暂无数据" : "  ";
//         }
//     })
//     .catch((error) => {
//         console.log(error);
//         ElMessage.error("出现错误，请刷新页面重试");
//     });
}
   
window.addEventListener("modifyaccountSubject", () => {
    if(useRouterArrayStoreHook().routerArray.find((item) => item.title.includes('员工信息'))) {
        getTreeData();
    }
});
watchEffect(() => {
    const accountSubject = accountSubjectStore.accountSubjectList;
    asubList.value = [];
    asubList.value.push({ asubType: 0, asubList: accountSubject.filter((asub) => asub.status === 0 && asub.isLeafNode) });
    let asubGroup: any = {};
    accountSubject.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = new Array<IAccountSubjectModel>();
            asubList.value.push({ asubType: asub.asubType, asubList: asubGroup[asub.asubType] });
        }
        if (asub.status === 0 && asub.isLeafNode) {
            asubGroup[asub.asubType].push(asub);
        }
    });
});
onMounted(()=>{
    getTreeData();
})
interface ISubjectTree {
    id: string;
    text: string;
    attributes: {
        code: string;
    };
    children?: ISubjectTree[];
}

const defaultProps = {
    children: "children",
    label: "text",
};


async function handleNodeClick(item: any) {
    if (checkValid) {
        if (item.children) {
            ElNotify({
                type: "warning",
                message: '"亲！只能选择末级节点哦！"',
            });
            return;
        }
        let invaild = await checkinvalidasubid(item.asubId);
        if (invaild) {
            ElNotify({
                type: "warning",
                message: "亲！选择的科目不能含有辅助核算或数量核算或外币核算，请停止这些核算设置此科目",
            });
            return;
        }
    }
    if (checkFA) {
        if ((accountingStandard.value === 2 || accountingStandard.value === 1) && item.asubCode == "1602") {
            ElNotify({
                type: "warning",
                message: "亲！折旧费用科目不可以选择为累计折旧哦！",
            });
            return;
        } else if (accountingStandard.value === 3 && item.asubCode == "1502") {
            ElNotify({
                type: "warning",
                message: "亲！折旧费用科目不可以选择为累计折旧哦！",
            });
            return;
        }
        if(item.aatypes !== ''){
            clickAsubWithtype('折旧费用科目', item.asubName, item.aatypes,item.asubId,0);
        }
    }
    if (updateAsubCode !== undefined) {
        updateAsubCode(item.asubId);
    }
    if (popoverHandleClose !== undefined) {
        popoverHandleClose();
    }
}
onMounted(() => {
    if (sbj_id.value !== undefined) {
        const node = document.getElementById(sbj_id.value as string);
        if (node) {
            setTimeout(() => {
                if (node) {
                    nextTick(() => {
                        node.scrollIntoView({ block: "center" });
                    });
                }
            }, 100);
        }
    }
});
</script>

<style lang="less" scoped>
.span_wrap {
    -webkit-line-clamp: 1 !important;
}
.scrollbar-container {
    border: 1px solid var(--border-color);
    :deep(.el-tree) {
        min-width: 100%;
        overflow: visible;
        display: inline-block;
    }
    :deep(.el-scrollbar__bar) {
        right: 1px;
        bottom: 1px;
    }
}

:deep(.el-tree-node__content) {
    height: 21px;
    .el-icon {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 21px;
        height: 21px;
        padding: 0;
    }
    &:hover {
        background-color: var(--table-hover-color)
        // background-color: var(--main-color);
        // color: var(--white);
    }
    .custom-tree-node {
        display: flex;
        align-items: center;
    }
}
:deep(.el-tree-node) {
    &.is-current {
        .el-tree-node__content {
            background-color: #44b449;

            .tree-title {
                color: #fff;
            }

            & ~ .el-tree-node__children {
                .el-tree-node__content {
                    background-color: #fff;
                    color: var(--font-color);

                    .tree-title {
                        color: var(--font-color);
                    }
                }
            }
        }
    }
}
// :deep(.el-tree-node.is-current > .el-tree-node__content) {
//     background-color: var(--white);
//     color: var(--main-color);
// }
.erp-tree{
    :deep(.el-tree-node){
        padding:2px 0;
        .tree-title {
            font-size: 14px;
            }
        &.is-current {
        .el-tree-node__content {
            background-color: var(--erp-table-hover-color);
            .tree-title {
                color: #333333;
            }
        }
    }
    }
}

:deep(.el-popover.el-popper) {
    background-color: var(--white);
}
.tree_tooltip {
    max-width: 300px;
    display: inline-block;
    white-space: normal;
    text-align: left;
}
.tree-icon {
    display: inline-block;
    vertical-align: middle;
    height: 21px;
    width: 21px;
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: center;
}

.tree-icon.tree-folder.tree-folder-open {
    background-image: url(@/assets/icons/folder.png);
}
.tree-icon.tree-file {
    background-image: url(@/assets/Icons/file.png);
}
.tree-title {
    color: var(--font-color);
    font-size: var(--h5);
    line-height: 21px;
    display: inline-block;
    padding-right: 10px;
}
// 兼容业财样式
body[erp] {
    .tree-icon.tree-file {
        width: 21px;
        height: 21px;
        background: url(@/assets/icons/file-erp.png) no-repeat 0 0;
        background-size: 16px 18px;
    }
}
</style>
