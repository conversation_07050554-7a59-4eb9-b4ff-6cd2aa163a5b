<template>
    <div class="content">
        <content-slider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">发票</div>
                    <div class="main-content-detail">
                        <div class="main-top main-tool-bar">
                            <div class="main-tool-left">
                                <SearchInfoContainer ref="containerRef" class="invoice-search-container">
                                    <template v-slot:title>{{ currentPeriodInfo }} </template>
                                    <el-scrollbar max-height="480px" always class="search-scroll" @scroll="handleSearchScroll">
                                        <div class="line-item first-item input">
                                            <div class="line-item-title">开票日期:</div>
                                            <div class="line-item-field">
                                                <DatePicker
                                                    v-model:startPid="(searchInfo.startDate as string)"
                                                    v-model:endPid="(searchInfo.endDate as string)"
                                                    v-model:periodInfo="periodInfo"
                                                    @blur="forcedShutdown = false"
                                                    @focus="forcedShutdown = true"
                                                />
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">录入日期:</div>
                                            <div class="line-item-field">
                                                <DatePicker
                                                    v-model:startPid="(searchInfo.startEntryDate as string)"
                                                    v-model:endPid="(searchInfo.endEntryDate as string)"
                                                    v-model:periodInfo="periodInfo"
                                                    @blur="forcedShutdown = false"
                                                    @focus="forcedShutdown = true"
                                                />
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">凭证日期:</div>
                                            <div class="line-item-field">
                                                <DatePicker
                                                    v-model:startPid="(searchInfo.startVDate as string)"
                                                    v-model:endPid="(searchInfo.endVDate as string)"
                                                    v-model:periodInfo="periodInfo"
                                                    @blur="forcedShutdown = false"
                                                    @focus="forcedShutdown = true"
                                                />
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">凭证字号：</div>
                                            <div class="line-item-field">
                                                <div style="display: flex; padding-right: 5px;">
                                                    <el-select v-model="searchInfo.vgId" :teleported="false">
                                                        <el-option :value="0" label="请选择">请选择</el-option>
                                                        <el-option :value="1" label="全部">全部</el-option>
                                                        <el-option v-for="item in voucherGroup" :value="item.id" :key="item.id" :label="item.name"></el-option>
                                                    </el-select>
                                                    <el-input
                                                        clearable 
                                                        type="text" 
                                                        class="ml-10" 
                                                        v-model="searchInfo.startVNum" 
                                                        :disabled="!searchInfo.vgId"
                                                        @input="startVNumLimit"
                                                    ></el-input>
                                                    <span style="padding: 0 8px; line-height: 30px;">至</span>
                                                    <el-input
                                                        clearable 
                                                        type="text" 
                                                        v-model="searchInfo.endVNum" 
                                                        :disabled="!searchInfo.vgId"
                                                        @input="endVNumLimit"
                                                    ></el-input>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">发票号码:</div>
                                            <div class="line-item-field">
                                                <el-input type="text" style="width: 100%; padding-right: 5px;" v-model="searchInfo.number" @input="invoiceNumberLimit" clearable></el-input>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">{{ invoiceCategory === "10080" ? "供应商名称:" : "客户名称:" }}</div>
                                            <div class="line-item-field">
                                                <el-input type="text" style="width: 100%; padding-right: 5px;" v-model="searchInfo.name" clearable></el-input>
                                            </div>
                                        </div>
                                        <div class="line-item input" v-show="invoiceCategory === '10080'">
                                            <div class="line-item-title">是否认证：</div>
                                            <div class="line-item-field">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <Select 
                                                        :teleported="false" 
                                                        class="item" 
                                                        v-model="searchInfo.invoiceIssueDateStatus"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                    >
                                                        <el-option value="0" label="全部">全部</el-option>
                                                        <el-option value="1" label="是">是</el-option>
                                                        <el-option value="2" label="否">否</el-option>
                                                    </Select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">发票状态:</div>
                                            <div class="line-item-field">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <Select 
                                                        :teleported="false" 
                                                        class="item" 
                                                        v-model="searchInfo.invoiceStatus"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                        :filterable="true"
                                                        :filter-method="statusFilterMethod"
                                                    >
                                                        <el-option 
                                                            v-for="item in showStatusList" 
                                                            :value="String(item.value)" 
                                                            :label="item.name" 
                                                            :key="item.value"
                                                        >
                                                            {{item.name}}
                                                        </el-option>
                                                    </Select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input" v-if="!isErp">
                                            <div class="line-item-title">业务类型:</div>
                                            <div class="line-item-field">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <VirtualSelectCheckbox
                                                        :options="businessTypeOptions"
                                                        :use-el-icon="true"
                                                        :class="[{placeTop : invoiceTypeListDirect === 'top'}]"
                                                        width="100%"
                                                        v-model:selectedList="searchInfo.businessTypes"
                                                        v-model:forced-shutdown="forcedShutdown" 
                                                        class="item"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">发票种类:</div>
                                            <div class="line-item-field large">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <VirtualSelectCheckbox
                                                        :options="invoiceTypeListOptions"
                                                        :use-el-icon="true"
                                                        :class="[{placeTop : invoiceTypeListDirect === 'top'}]"
                                                        width="100%"
                                                        v-model:selectedList="searchInfo.invoiceTypes"
                                                        v-model:forced-shutdown="forcedShutdown" 
                                                        class="item"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input" v-show="invoiceCategory === '10080'">
                                            <div class="line-item-title">认证日期:</div>
                                            <div class="line-item-field">
                                                <DatePicker
                                                    v-model:startPid="(searchInfo.issueStartDate as string)"
                                                    v-model:endPid="(searchInfo.issueEndDate as string)"
                                                    v-model:periodInfo="periodInfo"
                                                    @blur="forcedShutdown = false"
                                                    @focus="forcedShutdown = true"
                                                />
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">是否生成凭证：</div>
                                            <div class="line-item-field">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <Select 
                                                        :teleported="false" 
                                                        class="item" 
                                                        v-model="searchInfo.invoiceVoucherStatus"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                    >
                                                        <el-option value="0" label="全部">全部</el-option>
                                                        <el-option value="1" label="是">是</el-option>
                                                        <el-option value="2" label="否">否</el-option>
                                                    </Select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input" v-if="(!isErp && isRelation) || (isErp && isFullVersionErp)">
                                            <div class="line-item-title">{{ isErp ? "是否关联单据" : "是否生成单据" }}：</div>
                                            <div class="line-item-field">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <Select 
                                                        :teleported="false" 
                                                        class="item" 
                                                        v-model="searchInfo.invoiceBillStatus"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                    >
                                                        <el-option value="0" label="全部">全部</el-option>
                                                        <el-option value="1" label="是">是</el-option>
                                                        <el-option value="2" label="否">否</el-option>
                                                    </Select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">发票来源:</div>
                                            <div class="line-item-field">
                                                <div style="width: 100%; padding-right: 5px;">
                                                    <Select 
                                                        :teleported="false" 
                                                        class="item" 
                                                        v-model="searchInfo.invoiceSource"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                        :filterable="true"
                                                        :filter-method="sourceFilterMethod"
                                                    >
                                                        <el-option 
                                                            v-for="item in showSourceList" 
                                                            :value="item.value" 
                                                            :label="item.label" 
                                                            :key="item.value"
                                                        > {{ item.label }} </el-option>
                                                    </Select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">税率:</div>
                                            <div class="line-item-field taxRate-int">
                                                <el-input style="width: 100%; padding-right: 5px;" v-model="searchInfo.taxRate" type="text" @input="limitTaxRate" clearable></el-input>
                                                <span class="taxRate-int-unit">%</span>
                                            </div>
                                        </div>
                                        <div class="line-item input">
                                            <div class="line-item-title">备注:</div>
                                            <div class="line-item-field">
                                                <el-input type="text" style="width: 100%; padding-right: 5px;" v-model="searchInfo.note" clearable></el-input>
                                            </div>
                                        </div>
                                    </el-scrollbar>
                                    <div class="buttons">
                                        <a class="button solid-button" @click="handleConfirm">确定</a>
                                        <a class="button" @click="handleClose">取消</a>
                                        <a class="button" @click="handleReset">重置</a>
                                    </div>
                                </SearchInfoContainer>
                                <div class="invoice-task-tip ml-10" v-if="finishedInvoiceTask !== null">
                                    {{ dayjs(finishedInvoiceTask.modifiedDate).format("YYYY-MM-DD HH:mm") }}更新
                                    <a @click="openFetchInvoiceTaskShow" class="link">查看记录</a>
                                </div>
                                <ErpRefreshButton></ErpRefreshButton>
                            </div>
                            <div class="main-tool-right">
                                <el-checkbox label="显示所有信息" v-model="salesShowAll" v-show="invoiceCategory === '10070'"></el-checkbox>
                                <el-checkbox
                                    label="显示所有信息"
                                    v-model="purchaseShowAll"
                                    v-show="invoiceCategory === '10080'"
                                ></el-checkbox>
                                <RightButton
                                    ref="rightButtonRef"
                                    :key="rightButtonKey"
                                    :addInvoiceOptions="invoiceTypeList"
                                    v-model:cate="invoiceCategory"
                                    :search-info="searchInfo"
                                    :filterParams="filterParams"
                                    :checked-table-data="checkedTableData"
                                    :businessSalesOptions="businessSalesOptions"
                                    :businessPurchaseOptions="businessPurchaseOptions"
                                    :hasInvoiceTask="hasInvoiceTask"
                                    :exportInvoiceIds="exportInvoiceIds"
                                    :isProjectAccountingEnabled="isProjectAccountingEnabled"
                                    :finishedInvoiceTaskTime="finishedInvoiceTaskTime"
                                    :checkCanSearch="checkCanSearch"
                                    :showAll="invoiceCategory === '10070' ? salesShowAll : purchaseShowAll"
                                    @checkedInvoiceType="handleInvoiceTypeChecked"
                                    @goMasterPlate="goMasterPlateView"
                                    @goGenerateVoucher="loadVoucherView"
                                    @optionsSuccess="optionSuccess"
                                    @showFetchInvoice="showFetchInvoice"
                                    @show-journal-settings="showJournalSettings"
                                    @show-success-import-invoice="showSuccessImportInvoice"
                                    @batch-update-project-dialog="BatchUpdateProjectDialog"
                                    @quickAddInvoiceSuccess="quickAddInvoiceSuccess"
                                    @openFetchInvoiceTaskShow="openFetchInvoiceTaskShow"
                                    @associateVoucher="associateVoucher"
                                    :sortOrder="sortOrder"
                                    :sortField="sortField"
                                    :priceFloat="priceFloat"
                                    :quantityFloat="quantityFloat"
                                    :scmProductTypeVal="scmProductType"
                                    :scmAsidVal="scmAsid"
                                >
                                </RightButton>
                            </div>
                        </div>
                        <div class="main-center">
                            <keep-alive>
                                <Table
                                    ref="invoiceTableRef"
                                    :loading="loading"
                                    :data="tableData"
                                    :columns="invoiceColumns"
                                    :page-is-show="true"
                                    row-key="invoiceId"
                                    :layout="paginationData.layout"
                                    :page-sizes="paginationData.pageSizes"
                                    :page-size="paginationData.pageSize"
                                    :total="paginationData.total"
                                    :scrollbarShow="true"
                                    :row-class-name="setCancelRowStyle"
                                    :selectable="setSelectable"
                                    :currentPage="invoiceCategory === '10070' ? saleCurrentPage : purCurrentPage"
                                    :highlight-current-row="false"
                                    @current-change="handleCurrentChange"
                                    @size-change="handleSizeChange"
                                    @selection-change="tableSelectionChange"
                                    @select-all="checkHasMoreSelect"
                                    @select="handleSingleCheckMoreSelect"
                                    @row-click="tableRowClick"
                                    @cell-click="tableCellClick"
                                    @refresh="handleRerefresh"
                                    @cell-drop="cellDrop"
                                    :tableName="setModule"
                                    @agin-column-drop="watchDrop"
                                    @scroll="handleScroll"
                                >
                                    <template #invoiceType>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'invoiceType')"
                                            :min-width="170"
                                            prop="invoiceType"
                                            align="left"
                                            header-align="left"
                                            :class-name="'type-column ' + getSlotClassName('invoiceType', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('invoiceType', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>发票种类</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('invoiceType', $event)"></div>
                                                        <TableHeaderFilter
                                                            :prop="'invoiceTypes'" 
                                                            :isSelect="true"
                                                            :selectedList="searchInfo.invoiceTypes"
                                                            :option="searchOptions.invoiceTypes"
                                                            :hasSelectList="filterSearchInfo.invoiceTypes"
                                                            :isFilter="isFilterMultile(filterSearchInfo.invoiceTypes, searchOptions.invoiceTypes)"
                                                            @filterSearch="filterSearch"
                                                        >
                                                        </TableHeaderFilter>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.invoiceTypeText }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #entryDateText>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'entryDateText')"
                                            :min-width="90"
                                            prop="entryDateText"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('entryDateText', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('entryDateText', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>录入日期</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('entryDate', $event)"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.entryDateText }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #invoiceDateText>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'invoiceDateText')"
                                            :min-width="90"
                                            prop="invoiceDateText"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('invoiceDateText', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('invoiceDateText', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>开票日期</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('invoiceDate', $event)"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.invoiceDateText }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #code>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'code')"
                                            :min-width="85"
                                            prop="code"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('code', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('code', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>发票代码</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('code', $event)"></div>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.code }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #number>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'number')"
                                            :min-width="85"
                                            prop="number"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('number', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('number', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>发票号码</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('number', $event)"></div>
                                                        <TableHeaderFilter
                                                            :prop="'number'"
                                                            :isFilter="!!filterSearchInfo.number"
                                                            :hasSearchVal="filterSearchInfo.number" 
                                                            @filterSearch="filterSearch"
                                                        ></TableHeaderFilter>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.number }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #invoiceStatus>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'invoiceStatus')"
                                            :min-width="112"
                                            prop="invoiceStatus"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('invoiceStatus', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('invoiceStatus', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>发票状态</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('status', $event)"></div>
                                                        <TableHeaderFilter
                                                            :prop="'invoiceStatus'" 
                                                            :isSelect="true"
                                                            :multiSelect="false"
                                                            :SSinglist="statusListOptions"
                                                            :hasSSingVal="Number(filterSearchInfo.invoiceStatus)"
                                                            :isFilter="Number(filterSearchInfo.invoiceStatus) > 0"
                                                            @filterSearch="filterSearch"
                                                        ></TableHeaderFilter>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.invoiceStatus }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #name>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'name')"
                                            :min-width="140"
                                            prop="name"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('name', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('name', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>{{ invoiceCategory === "10070" ? "客户名称" : "供应商名称"}}</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('name', $event)"></div>
                                                        <TableHeaderFilter
                                                            :prop="'name'"
                                                            :isFilter="!!filterSearchInfo.name"
                                                            :hasSearchVal="filterSearchInfo.name" 
                                                            @filterSearch="filterSearch"
                                                        ></TableHeaderFilter>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.name }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #projectName>
                                        <el-table-column 
                                            label="项目" 
                                            align="left"
                                            prop="projectName" 
                                            header-align="left" 
                                            :width="getColumnWidth(setModule, 'projectName')"
                                            :min-width="130"
                                            :class-name="getSlotClassName('projectName', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('projectName', invoiceColumns)"
                                        >
                                            <template #default="scope">
                                                <div
                                                    v-if="
                                                        scope.row.invoiceId &&
                                                        currentClickRowId === scope.row.invoiceId &&
                                                        currentClickProp === scope.column.label
                                                    "
                                                >
                                                    <Select
                                                        ref="projectItemRef"
                                                        class="project-item"
                                                        :suffix-icon="CaretBottom"
                                                        v-model="currentProjectId"
                                                        placeholder=""
                                                        :fit-input-width="true"
                                                        :filterable="true"
                                                        clearable
                                                        :IconClearRight="'20px'"
                                                        :bottom-html="checkPermission(['assistingaccount-canedit']) ? newCDAccountHtmlProject : ''"
                                                        @bottom-click="newProject"
                                                        @blur="handleBlur(scope.row)"
                                                        :filter-method="erpAssistingProjectFilterMethod"
                                                    >
                                                        <ElOption
                                                            v-for="item in showErpAssistingProjectList"
                                                            :key="item.aaeid"
                                                            :value="item.aaeid"
                                                            :label="item.aaname"
                                                        />
                                                    </Select>
                                                </div>
                                                <span v-else> {{ scope.row?.projectName }}</span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #businessTypeText>
                                        <el-table-column
                                            label="业务类型"
                                            prop="businessTypeText"
                                            align="left"
                                            header-align="left"
                                            :width="getColumnWidth(setModule, 'businessTypeText')"
                                            :min-width="130"
                                            :class-name="getSlotClassName('businessTypeText', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('businessTypeText', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>业务类型</span>
                                                    <div class="header-operate-rt">
                                                        <div class="header-caret" @click="getSortCommon('businessType', $event)"></div>
                                                        <TableHeaderFilter
                                                            :prop="'businessTypes'" 
                                                            :isSelect="true"
                                                            :selectedList="searchInfo.businessTypes"
                                                            :option="searchOptions.businessTypes"
                                                            :hasSelectList="filterSearchInfo.businessTypes"
                                                            :isFilter="isFilterMultile(filterSearchInfo.businessTypes, searchOptions.businessTypes)"
                                                            @filterSearch="filterSearch"
                                                        >
                                                        </TableHeaderFilter>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <div
                                                    v-if="
                                                        scope.row.invoiceId &&
                                                        currentClickRowId === scope.row.invoiceId &&
                                                        currentClickProp === scope.column.label
                                                    "
                                                    @mouseenter="mouseenterBusiness"
                                                >
                                                    <Select
                                                        ref="businessItemRef"
                                                        class="project-item"
                                                        :suffix-icon="CaretBottom"
                                                        v-model="currentBusinessId"
                                                        placeholder=""
                                                        :fit-input-width="true"
                                                        :filterable="true"
                                                        :bottom-html="newCDAccountHtmlProject"
                                                        @bottom-click="newBusiness"
                                                        @blur="handleBlurBusiness(scope.row)"
                                                        :filter-method="businessFilterMethod"
                                                    >
                                                        <ElOption
                                                            v-for="item in showBusinessOptions"
                                                            :key="item.value"
                                                            :label="item.label"
                                                            :value="item.value"
                                                        ></ElOption>
                                                    </Select>
                                                </div>
                                                <span v-else>
                                                    {{ scope.row?.businessTypeText }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #taxRate>
                                        <el-table-column 
                                            label="税率" 
                                            :width="getColumnWidth(setModule, 'taxRate')"
                                            :min-width="50" 
                                            prop="taxRate" 
                                            align="right" 
                                            header-align="right"
                                            :class-name="getSlotClassName('taxRate', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('taxRate', invoiceColumns)"
                                        >
                                            <template #default="scope">
                                                <el-tooltip
                                                    v-if="isMultipleTaxRates(scope.row.taxRate2)"
                                                    class="box-item"
                                                    effect="light"
                                                    content="该发票可能存在多种税率或差额征税情况"
                                                    placement="right-start"
                                                >
                                                    <span>{{ scope.row.taxRate }}</span>
                                                </el-tooltip>
                                                <span v-else>{{ scope.row.taxRate }}</span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #vidText>
                                        <el-table-column
                                            label="关联凭证"
                                            :width="getColumnWidth(setModule, 'vidText', 90)"
                                            :min-width="90"
                                            align="left"
                                            :class-name="'highlight-blue ' + getSlotClassName('vidText', invoiceColumns)"
                                            prop="vidText"
                                            :show-overflow-tooltip="false"
                                            :fixed="getSlotIsFreeze('vidText', invoiceColumns)"
                                        >
                                            <template #default="scope">
                                                <div class="vidtext">
                                                    <div
                                                        @click.stop.prevent="
                                                            canViewVoucher && scope.row.vid !== 0
                                                                ? jumpLookVoucher(scope.row.pid, scope.row.vid)
                                                                : ''
                                                        "
                                                    >
                                                        <span>{{ formatVoucherTxt(scope.row.pidText, scope.row.vidText) }}</span>
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #verifiedBills v-if="isErp && isFullVersionErp">
                                        <el-table-column
                                            label="关联单据"
                                            min-width="120"
                                            align="left"
                                            prop="verifiedBills"
                                            :show-overflow-tooltip="false"
                                        >
                                            <template #default="scope">
                                                <div v-if="scope.row.verifiedBills">
                                                    <template v-if="scope.row.groupBills.length <= 1">
                                                        <div class="link" v-if="checkBillPermission(scope.row.verifiedBills[0].billType)">
                                                            <span 
                                                                @click.stop.prevent="openErpScm(scope.row.groupBills[0])"
                                                            >
                                                                {{ getBillContent(scope.row.groupBills[0]) }}
                                                            </span>
                                                        </div>
                                                        <span v-else>{{ getBillContent(scope.row.groupBills[0]) }}</span>
                                                    </template>
                                                    <template v-else>
                                                        <el-tooltip effect="light" placement="bottom-start">
                                                            <template #content>
                                                                <div style="width: 150px">
                                                                    <div  
                                                                        v-for="billItem in scope.row.groupBills" 
                                                                        :key="billItem.billType"
                                                                    >
                                                                    <div class="link mt-10" v-if="checkBillPermission(billItem.billType)">
                                                                        <span 
                                                                            @click.stop.prevent="openErpScm(billItem)"
                                                                        >
                                                                        {{ getBillContent(billItem) }}
                                                                        </span>
                                                                    </div>
                                                                    <span v-else style="font-size: 14px;">{{ getBillContent(billItem) }}</span>
                                                                    </div>
                                                                </div>
                                                            </template>
                                                            <div>
                                                                <span class="lightText pr-5">{{ getBillContent(scope.row.groupBills[0]) }}</span>
                                                                <!-- <el-icon color="#333"><MoreFilled /></el-icon> -->
                                                            </div>
                                                        </el-tooltip>
                                                    </template>    
                                                </div>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #invoiceSource>
                                        <el-table-column
                                            label="发票来源"
                                            :width="getColumnWidth(setModule, 'invoiceSourceText')"
                                            :min-width="80"
                                            prop="invoiceSourceText"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('invoiceSource', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('invoiceSource', invoiceColumns)"
                                        >
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.invoiceSourceText }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #scmRelation v-if="!isErp">
                                        <el-table-column
                                            :label="invoiceCategory === '10070' ? '出库单' : '入库单'"
                                            :width="getColumnWidth(setModule, 'scmRelation')"
                                            :min-width="70"
                                            align="left"
                                            :class-name="'highlight-blue ' + getSlotClassName('scmRelation', invoiceColumns)"
                                            prop="scmRelation"
                                            :fixed="getSlotIsFreeze('scmRelation', invoiceColumns)"
                                        >
                                            <template #default="scope">
                                                <div class="vidtext" v-if="scope.row.billId !== 0">
                                                    <span class="link" @click="openScm(scope.row.billId, scope.row.billType)">查看</span>
                                                </div>
                                                <span v-else>&nbsp;</span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #note>
                                        <el-table-column
                                            label=""
                                            :width="getColumnWidth(setModule, 'note')"
                                            prop="note"
                                            align="left"
                                            header-align="left"
                                            :class-name="getSlotClassName('note', invoiceColumns)"
                                            :fixed="getSlotIsFreeze('note', invoiceColumns)"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>备注</span>
                                                    <div class="header-operate-rt">
                                                        <TableHeaderFilter
                                                            :prop="'note'"
                                                            :isFilter="!!filterSearchInfo.note"
                                                            :hasSearchVal="filterSearchInfo.note" 
                                                            @filterSearch="filterSearch"
                                                        ></TableHeaderFilter>
                                                    </div>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <span>
                                                    {{ scope.row.note }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #operation>
                                        <el-table-column
                                            prop="operation"
                                            :min-width="140"
                                            :show-overflow-tooltip="false"
                                            align="left"
                                            header-align="left"
                                            :fixed="getSlotIsFreeze('operation', invoiceColumns)"
                                            :resizable="false"
                                        >
                                            <template #header>
                                                <div class="header-operate">
                                                    <span>操作</span>
                                                    <div class="header-operate-rt">
                                                        <div class="set-icon" @click="openColSet"></div>
                                                    </div>
                                                </div>
                                            </template>     
                                            <template #default="scope">
                                                <div class="handle" v-if="scope.row.invoiceId !== 0">
                                                    <a
                                                        v-if="(scope.row.pid !== 0 && scope.row.vid !== 0) || scope.row.isOffseted"
                                                        class="link"
                                                        @click.stop.prevent="handleEditRow(scope.row, 'look')"
                                                        >查看</a
                                                    >
                                                    <a
                                                        v-else
                                                        v-permission="[
                                                            invoiceCategory === '10070'
                                                                ? 'invoice-output-canedit'
                                                                : 'invoice-input-canedit',
                                                        ]"
                                                        class="link"
                                                        @click.stop.prevent="handleEditRow(scope.row, 'edit')"
                                                        >编辑</a
                                                    >
                                                    <a
                                                        v-permission="[
                                                            invoiceCategory === '10070'
                                                                ? 'invoice-output-candelete'
                                                                : 'invoice-input-candelete',
                                                        ]"
                                                        class="link"
                                                        @click.stop.prevent="handleDeleteRow(scope.row)"
                                                        >删除</a
                                                    >
                                                    <a
                                                        class="link"
                                                        v-permission="[
                                                            invoiceCategory === '10070'
                                                                ? 'invoice-output-canview'
                                                                : 'invoice-input-canview',
                                                        ]"
                                                        @click.stop="getAttachFileList(scope.row)"
                                                        >附件{{
                                                            scope.row.attachFileCount === 0 ? "" : "(" + scope.row.attachFileCount + ")"
                                                        }}</a
                                                    >
                                                </div>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #pageOther v-if="!isErp && checkedTableData.length > 0">
                                        <span>
                                            已选 {{ checkedTableData.length }} 条
                                            <a class="link pl-20" @click="changeSelectedTableDialog">查看</a>
                                            <a class="link pl-20" @click="invoiceTableRef?.getTable()?.clearSelection()">取消全部</a>
                                        </span>
                                    </template>
                                </Table>
                            </keep-alive>
                        </div>
                    </div>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center invoice-add-view">
                    <AddInvoice
                        ref="editInvoiceView"
                        :key="addComponentKey"
                        :invoice-type="invoiceType"
                        :form-type="addOrEdit"
                        :edit-data="editOneData"
                        :isProjectAccountingEnabled="isProjectAccountingEnabled"
                        :wordCutList="wordCutList"
                        :quantityFloat="quantityFloat"
                        v-model:invoiceCategory="invoiceCategory"
                        @addInvoiceSuccess="addInvoiceSuccess"
                        @goBackMain="addBackMain"
                    >
                    </AddInvoice>
                </div>
            </template>
            <template #voucher>
                <div class="slot-content">
                    <GenerateVoucher
                        :title="'发票生成凭证'"
                        :documentTitle="'发票'"
                        :query-params="genVoucherQueryParameters"
                        :merge-error-function="mergeErrorFunction"
                        :error-table-columns="genVoucherErrorTableColumns"
                        @back="genVoucherSaveSuccess"
                        @voucher-changed="genVoucherChangedHandle"
                        @save-operate-log="saveVoucherGenerateLog"
                        ref="generateVoucherView"
                        @load-success="loadSuccess = true"
                    >
                        <template #toolbar>
                            <a class="solid-button" @click="saveGenVoucher()">保存</a>
                            <a class="button ml-10" @click="currentSlot = 'main'">取消</a>
                            <el-checkbox
                                class="ml10"
                                @change="mergeVoucher()"
                                label="凭证合并"
                                v-model="genVoucherQueryParameters.isMerge"
                            ></el-checkbox>
                            <SelectCheckbox
                                class="ml10"
                                ref="selectCheckboxRef"
                                inputPlaceholder="请选择凭证合并条件"
                                :useElIcon="true"
                                width="180px"
                                :options="options"
                                :show-all="false"
                                :select-all="false"
                                @update:selected-list="changeSelectedList"
                                v-show="genVoucherQueryParameters.isMerge"
                                v-model:selectedList="selectedList"
                                @check-box-show-change="checkBoxShowChange"
                            >
                            </SelectCheckbox>
                            <span class="ml10" style="font-size: 14px" v-show="genVoucherQueryParameters.isMerge">科目合并</span>
                            <SelectCheckbox
                                class="ml10"
                                :useElIcon="true"
                                width="180px"
                                :options="subjectOption"
                                :select-all="false"
                                @update:selected-list="changeSubjectSelectedList"
                                v-show="genVoucherQueryParameters.isMerge"
                                v-model:selectedList="subjectSelectedList"
                                ref="selectSubjectRef"
                                :place-holder="'请选择科目合并方式'"
                            >
                            </SelectCheckbox>

                            <el-checkbox
                                class="ml20"
                                label="智能匹配客户/供应商"
                                v-model="genVoucherQueryParameters.autoMatch"
                                @change="genVoucherCheckboxChanged(3)"
                            ></el-checkbox>
                            <BubbleTip :bubble-width="354" :bubble-top="16" class="ml-10">
                                <template #content>
                                    <div class="help-icon"></div>
                                </template>
                                <template #tips>
                                    <div>根据发票的业务类型对应的凭证模板</div>
                                    <div v-show="!isUnion && !isVillage">1、智能匹配六大往来（应收账款、其他应收款、预收账款、</div>
                                    <div v-show="!isUnion && !isVillage">应付账款、其他应付款和预付账款）的客户/供应商</div>
                                    <div v-show="isUnion">1、智能匹配往来科目（其他应收款、其他应付款）的客户/供应商</div>
                                    <div v-show="isVillage">1、智能匹配往来科目（应收款、应付款）的客户/供应商</div>
                                    <div>2、自动新增对应的往来单位</div>
                                </template>
                            </BubbleTip>
                            <el-checkbox
                                class="ml20"
                                label="智能匹配存货"
                                v-model="genVoucherQueryParameters.autoMatchStock"
                                @change="genVoucherCheckboxChanged(4)"
                            ></el-checkbox>
                            <div class="gear-icon ml-10" @click="openMatchStockDialog()"></div>
                            <BubbleTip :bubble-width="354" :bubble-top="16" class="ml-10">
                                <template #content>
                                    <div class="help-icon"></div>
                                </template>
                                <template #tips>
                                    <div>根据发票上的商品明细智能匹配存货</div>
                                    <div v-show="!isUnion && !isVillage">1、智能匹配原材料、库存商品、委托加工物资、周转材料、</div>
                                    <div v-show="!isUnion && !isVillage">主营/其他业务收入、主营/其他业务成本的存货</div>
                                    <div v-show="isUnion">1、智能匹配资产基金-库存物品、库存物品的存货</div>
                                    <div v-show="isVillage">1、智能匹配库存物资、销售收入、经营支出的存货</div>
                                    <div>2、自动新增对应的存货</div>
                                    <div>3、点击齿轮按钮，支持自定义匹配存货</div>
                                </template>
                            </BubbleTip>
                            <el-checkbox class="ml20"
                                label="AI智能生成凭证"
                                v-model="genVoucherQueryParameters.autoAIGenerate"
                                @change="genVoucherCheckboxChanged(5)"
                            ></el-checkbox>
                            <BubbleTip :bubble-width="354" :bubble-top="16" class="ml-10">
                                <template #content>
                                    <div class="help-icon"></div>
                                </template>
                                <template #tips>
                                    <div>勾选后，AI会自动根据发票信息生成凭证。您也可以取消勾选，继续使用发票业务类型对应的凭证模板生成凭证</div>
                                </template>
                            </BubbleTip>
                        </template>
                    </GenerateVoucher>
                </div>
            </template>
            <template #masterPlate>
                <div class="slot-content align-center" :style="isErp ? '' : 'height: 100%;'">
                    <MasterPlate
                        ref="masterPlateView"
                        :invoice-category="invoiceCategory"
                        @goBackAdd="masterPlateViewGoBackAdd"
                        @templateEditSuccess="getBusinessTypeOptions"
                        :autoAddName="autoAddName"
                    >
                    </MasterPlate>
                </div>
            </template>
            <template #checkVoucher>
                <div class="slot-content">
                    <CheckVoucher
                        ref="checkVoucherRef"
                        :to-voucher-about="toVoucherAbout"
                        :back="() => (currentSlot = 'main')"
                        :reload-data="genVoucherSaveSuccess"
                    ></CheckVoucher>
                </div>
            </template>
        </content-slider>
        <FetchInvoice
            v-model:fetchInvoiceShow="fetchInvoiceShow"
            :hasInvoiceTask="hasInvoiceTask"
            :invoiceTaskList="invoiceTaskList"
            @setCollectTaskId="setCollectTaskId"
            @getInvoiceTaskList="getInvoiceTaskList"
        ></FetchInvoice>
        <FetchInvoiceTask v-model:fetchInvoiceTaskShow="fetchInvoiceTaskShow" v-model:invoiceCategory="invoiceCategory"></FetchInvoiceTask>
        <LastFetchInvoiceTask
            v-model:lastFetchInvoiceTaskShow="lastFetchInvoiceTaskShow"
            v-model:invoiceCategory="invoiceCategory"
            :invoiceTaskList="invoiceTaskList"
            @handlebyDate="handlebyDate"
        ></LastFetchInvoiceTask>
    </div>
    <GenerateVoucherSettingDialog
        :settingType="voucherSettingsType"
        ref="generateVoucherSettingDialogRef"
        v-model="voucherSettingDialogShow"
    />
    <UploadFileDialog :readonly="readonly" ref="uploadFileDialogRef" @save="saveAttachFile" />
    <DialogAddProject
        ref="dialogAddProjectRef"
        v-model:add-project-show="addProjectShow"
        @save-project="handleProjectSave"
    ></DialogAddProject>
    <el-dialog title="提示" center v-model="voucherTip" width="440px" @close="handleTipCancel" class="dialogDrag">
        <div class="voucher-tip-box" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="box-main">
                <div style="font-size: 14px; color: #333333">发票数据已生成凭证，是否仍需修改？</div>
                <div class="mb-20 tip">
                    <img src="@/assets/Settings/tips.png" style="width: 14px; height: 14px; margin-right: 4px" />
                    <span style="color: #666666">修改会导致发票数据与凭证不一致~</span>
                </div>
                <div>
                    <el-checkbox v-model="notTip" @change="(val: any) => handleNoTipsChange(val)" label="不再提示" />
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleTipConfirm">确定</a>
                <a class="button ml-10" @click="handleTipCancel">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="提示" width="440px" center v-model="editVoucherShow" class="custom-confirm dialogDrag">
        <div class="editVoucherShow-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="content-main">
                <div style="text-align: left; margin-left: 5px">发票修改成功！</div>
                <div style="margin-top: 5px; text-align: left; margin-left: 5px">是否需手工修改已关联的凭证？</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleConfirmSuccess">确定</a>
                <a class="button ml-10" @click="handleComfirmCancal">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 关联凭证 -->
    <DialogAboutVoucher ref="dialogAboutVoucherRef" @about-vourcher="aboutVourcher"></DialogAboutVoucher>
    <!-- 取消关联凭证再次确认 -->
    <el-dialog v-model="cancelAboutVourchDialog" title="提示" center width="440" class="custom-confirm dialogDrag">
        <div class="certification-show-content" v-dialogDrag>
            <div class="certification-show-main">
                <span class="delete-tip">亲，确定要取消该发票与凭证的关联关系吗？</span>
            </div>
            <div class="buttons bt">
                <a class="button solid-button ml-10" @click="cancelAboutVourchFn">确定</a>
                <a class="button ml-10" @click="() => (cancelAboutVourchDialog = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 取消关联结果(关联进销存) -->
    <el-dialog v-model="cancelAboutVourchResult.showDialog" title="提示" center width="450" class="custom-confirm dialogDrag">
        <div v-dialogDrag>
            <div style="padding:30px">
                成功：{{cancelAboutVourchResult.successCount}}，跳过:{{cancelAboutVourchResult.skipCount}}（已结转/自动关联进销存的发票数据已跳过）
            </div>
            <div style="padding: 0 40px 10px;color: #999;line-height:18px;font-size:12px">
                <div>
                    <img src="@/assets/Icons/hint.png" style="height: 14px; width: 14px;vertical-align: top;margin-top:2px" />
                    <span>温馨提示：</span>
                </div>
                <div style="padding-left:10px">
                    <div>1、自动关联的进销存凭证，请删除凭证取消关联</div>
                    <div>2、发票生成单据后，再从发票模块生成凭证，凭证可以同时关联单据和发票附件哦~</div>
                </div>
            </div>
            <div class="buttons bt">
                <a class="button solid-button" @click="cancelAboutVourchResult.showDialog = false">知道了</a>
            </div>
        </div>
    </el-dialog>
    <DiffPageSelected
        ref="diffPageSelectedRef"
        row-key="invoiceId"
        :selected-data="checkedTableData"
        :columns="selectedColumns(invoiceCategory)"
        @row-click="tableRowClickSelected"
        @select="selectedTableSelectionChange"
        @select-all="viewSelectedTableAelectAll"
    />
    <DialogMatchStock ref="DialogMatchStockRef" :invoiceCategory="invoiceCategory" @save-stockt="loadGenVoucher(genVoucherInvoiceIds)"/>
    <!-- 列设置 -->
    <ColumnSet
        ref="columnSetRef" 
        v-model="columnSetShow"
        :data="invoiceColumnsSet"
        :allColumns="allColumns"
        :setModuleType="module"
        @saveColumnSet="saveColumnSet"
    >
    </ColumnSet>
</template>

<script lang="ts">
export default {
    name: "Invoice",
};
</script>
<script setup lang="ts">
import { 
    ref, 
    reactive, 
    watch, 
    computed, 
    nextTick, 
    onMounted, 
    onBeforeUnmount, 
    onUnmounted, 
    toRef, 
    onActivated,
    watchEffect,
    defineAsyncComponent
} from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import dayjs from "dayjs";
import { getGlobalLodash } from "@/util/lodash";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm, ElAlert} from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams, globalWindowOpenPage, tryClearCustomUrlParams } from "@/util/url";
import { formatVoucherTxt } from "@/util/format";
import { getGlobalToken } from "@/util/baseInfo";
import { getScmRelationInfo } from "@/api/scm";
import type { ScmRelationInfo } from "@/api/scm";
import { openScmTab } from "@/util/scm";
import { getFirstAndLastDayOfMonth } from "@/util/date";
import { useLoading } from "@/hooks/useLoading";
import { usePagination } from "@/hooks/usePagination";
import { useAccountSetStore } from "@/store/modules/accountset";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import {
    InvoiceQueryParameters,
    type IBatchGenerateVoucherModel,
    type InvoiceWithVoucherModel,
    type IGenVoucherNeedInsertAsub,
    type IInvocieGenVoucherResult,
    type IVoucherOperateLog,
    type GenVoucherParameters,
    type BaseDocumentModel,
    type InvoiceDocumentModel,
} from "@/components/GenerateVoucher/types";
import BubbleTip from "@/components/BubbleTip/index.vue";
import ElOption from "@/components/Option/index.vue";
import Select from "@/components/Select/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import GenerateVoucher from "@/components/GenerateVoucher/index.vue";
import RightButton from "./components/RightButton.vue";
import AddInvoice from "./components/AddInvoice.vue";
import MasterPlate from "./components/MasterPlate.vue";
import { 
    Columns, 
    checkStartEndDate, 
    isMultipleTaxRates, 
    genVoucherErrorTableColumns, 
    selectedColumns,
    checkBillPermission,
    urlRoute,
    urlRouteName,
    invoiceTypeListAll,
    invoiceTypeListAllPur,
} from "./utils";
import DialogMatchStock from "./components/DialogMatchStock.vue";
import type {
    ITableItem,
    IInvoiceTypeListItem,
    ISearchInfoType,
    IBusinessTypeItem,
    ITableDataRow,
    ISearchDate,
    ISearchParams,
    IPagingList,
    IInvoiceTaskResult,
    IInvoiceTaskValue,
    IImportSuccessData,
    IResoneQuickAddInvoice,
    IInvoiceAttachFileList,
    InvoiceLineSn,
    IFSearchItem,
    VerifiedBill,
    GroupedBill
} from "./types"; 
import FetchInvoice from "./components/FetchInvoice.vue";
import FetchInvoiceTask from "./components/FetchInvoiceTask.vue";
import LastFetchInvoiceTask from "./components/LastFetchInvoiceTask.vue";
import { getCookie, setCookie } from "@/util/cookie";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import GenerateVoucherSettingDialog from "@/components/Dialog/GenerateVoucherSetting/index.vue";
import type { Option } from "@/components/SelectCheckbox/types";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import router from "@/router";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { PeriodStatus } from "@/api/period";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { getDaysInMonth } from "../Voucher/VoucherList/utils";
import { checkPermission } from "@/util/permission";
import { CaretBottom } from "@element-plus/icons-vue";
import DialogAddProject from "./components/DialogAddProject.vue";
import { getNextAaNum } from "@/views/Settings/AssistingAccounting/utils";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { editConfirm } from "@/util/editConfirm";
import { tryUpdateWordCut, getWordCutList, type IWordCut, WordCutCategory, } from "@/util/wordCut";
import DialogAboutVoucher from "./components/DialogAboutVoucher.vue";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import ColumnSet from "@/components/ColumnSet/index.vue";
import type { IColItem } from "@/components/ColumnSet/utils";
import { 
    getSlotIsFreeze, 
    getSlotClassName, 
    getShowColumn, 
    getColumnWidth,
    alterArrayPos, 
    getColumnListApi,
} from "@/components/ColumnSet/utils";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import { isFilterMultile } from "@/components/TableHeaderFilter/utils";
import { erpCreateTab } from "@/util/erpUtils";
import { formatDate } from "@/util/date";
import DiffPageSelected from "@/components/Dialog/DiffPageSelected/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum, handleTrialExpired, ExpiredToBuyDialogEnum} from "@/util/proUtils";

const CheckVoucher = defineAsyncComponent(() => import("./components/CheckVoucher.vue"));
const accountSet = useAccountSetStoreHook().accountSet;
const accountStandard = accountSet!.accountingStandard;
const isUnion = ref(accountStandard === 6);
const isVillage = ref(accountStandard === 7);
const props = defineProps({
    invoiceCategory: {
        type: String,
        default: "10070"
    },
    isFetchInvoiceSales: {
        type: Boolean,
        default: false,
    },
    isFetchInvoicePurchase: {
        type: Boolean,
        default: false,
    },
});
const invoiceCategory = ref(props.invoiceCategory);
const _ = getGlobalLodash()
const generateVoucherSettingDialogRef = ref<InstanceType<typeof GenerateVoucherSettingDialog>>();
const voucherSettingDialogShow = ref(false);
const voucherSettingsType = computed(() => (invoiceCategory.value === "10070" ? "salesInvoice" : "purchaseInvoice"));
let module = computed(() => {
    return invoiceCategory.value === "10070" ? 400 : 500
});

const trialStatusStore = useTrialStatusStore();
const isErp = ref(window.isErp);
const invoiceTableRef = ref<InstanceType<typeof Table>>();
const rightButtonRef = ref();
const rightButtonKey = ref(0);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const periodStore = useAccountPeriodStore();
const currentPath = ref(route.path);
let minCheckOutDate = dayjs(new Date(periodStore.periodList.filter((p) => p.status < PeriodStatus.CheckOut)[0]?.startDate || "")).format(
    "YYYY-MM-DD"
);

let slots = ["main", "add", "masterPlate", "voucher", "checkVoucher"];
let currentSlot = ref("main");
let searchInfo = ref<ISearchInfoType>({
    startDate: "",
    endDate: "",
    startEntryDate: "",
    endEntryDate: "",
    number: (route.query.invoiceCode as string) ?? "",
    invoiceType: (route.query.invoiceType as string) ?? "0",
    invoiceVoucherStatus: "0",
    invoiceStatus: "0",
    invoiceSource: "0",
    name: "",
    invoiceIssueDateStatus: "0",
    issueStartDate: "",
    issueEndDate: "",
    businessTypes: [] as number[],
    startVDate: "",
    endVDate: "",
    vgId: 0,
    startVNum: "",
    endVNum: "",
    invoiceBillStatus: "0",
    taxRate: "",
    note: "",
   // 如果route.query.invoiceTypes已经是数组，直接使用；如果是单个值，转为数组；否则使用空数组
    invoiceTypes: Array.isArray(route.query.invoiceTypes)
        ? route.query.invoiceTypes.map(Number)
        : (route.query.invoiceTypes ? [Number(route.query.invoiceTypes)] : []) as number[],
});
const filterSearchInfo:IFSearchItem = reactive({
    name: "",
    note: "",
    number: "",
    invoiceTypes: [] as number[],
    businessTypes: [] as number[],
    invoiceStatus: "0",
});
const statusListOptions = ref([
    {value: 0, name: "全部"},
    {value: 1, name: "正常"},
    {value: 2, name: "作废"},
]);
const salesSearchInfo = ref<ISearchInfoType>(_.cloneDeep(searchInfo.value));
const purchaseSearchInfo = ref<ISearchInfoType>(_.cloneDeep(searchInfo.value));
const currentPeriodInfo = ref("");
function tryGetParamsFromRoute() {
    //针对发票号码，电子档案invNum，发票一览表invoiceCode，进销存单据未知，有待沟通统一
    const invalidArguments = ["", null, undefined];
    if (Array.isArray(route.query.invoiceCategory) || invalidArguments.some((arg) => arg === route.query.invoiceCategory)) return;
    invoiceCategory.value = route.query.invoiceCategory as string;
    const { vdate, vnum, vgname, invNum } = route.query;
    const routeParams = { startVDate: vdate, endVDate: vdate, startVNum: vnum, endVNum: vnum, number: invNum ?? '' };
    if (vgname) {
        const targetVgid = useVoucherGroupStore().voucherGroupList?.find((item) => item.name === route.query.vgname)?.id;
        Object.assign(routeParams, { vgId: targetVgid ?? 1 });
    }
    if (invoiceCategory.value === "10070") {
        Object.assign(salesSearchInfo.value, routeParams);
    } else if (invoiceCategory.value === "10080") {
        Object.assign(purchaseSearchInfo.value, routeParams);
    }
}

const periodInfo = ref("");
const showAll = computed(() => {
    return invoiceCategory.value === "10070" ? salesShowAll.value : purchaseShowAll.value;
});

const canViewVoucher = computed(() => {
    if (isErp.value) {
        return useAccountSetStoreHook().permissions.includes("businessvoucher-canedit");
    }
    return useAccountSetStoreHook().permissions.includes(
        invoiceCategory.value === "10070" ? "invoice-output-cancreatevoucher" : "invoice-input-cancreatevoucher"
    );
});

let salesShowAll = ref<boolean>(false);
let purchaseShowAll = ref<boolean>(false);
const invoiceColumns = ref<Array<IColumnProps>>([]);
const invoiceColumnsSet = ref<Array<IColumnProps>>([]);
let editInvoiceView = ref();
let addComponentKey = ref(0);
let masterPlateView = ref();
// add页传参
let addOrEdit = ref<string>("");
let invoiceType = reactive({ title: "", id: "" });

const generateVoucherView = ref<InstanceType<typeof GenerateVoucher>>();
const genVoucherQueryParameters = ref(new InvoiceQueryParameters());
const genVoucherChanged = ref(false);
const baseVoucherChanged = ref(false);
const businessSalesOptions = ref<IBusinessTypeItem[]>([]);
const businessPurchaseOptions = ref<IBusinessTypeItem[]>([]);
const businessTypeOptions = ref<Array<Option>>([])

function getBusinessTypeOptions(code?: number) {
    if (invoiceCategory.value === "10070") {
        request({
            url: `/api/InvoiceTemplate/SalesList`,
            method: "get",
        }).then((res: IResponseModel<ITableDataRow[]>) => {
            businessSalesOptions.value = res.data.map((item: any) => {
                return {
                    label: item.businessTypeModel.name,
                    value: String(item.businessTypeModel.id),
                } as IBusinessTypeItem;
            });
            businessTypeOptions.value = businessSalesOptions.value.map((v) => (
                { id: Number(v.value), name: v.label}
            ));
            searchInfo.value.businessTypes = businessTypeOptions.value.map((item) => item.id);
            if (code !== undefined && invoiceCategory.value === "10070") {
                filterItemId = res.data.filter((item: any) => item.businessTypeModel.templateId === Number(code))[0].businessTypeModel.id;
            }
        });
    } else {
        request({
            url: `/api/InvoiceTemplate/PurchaseList`,
            method: "get",
        }).then((res: IResponseModel<ITableDataRow[]>) => {
            businessPurchaseOptions.value = res.data.map((item: any) => {
                return {
                    label: item.businessTypeModel.name,
                    value: String(item.businessTypeModel.id),
                } as IBusinessTypeItem;
            });
            businessTypeOptions.value = businessPurchaseOptions.value.map((v) => (
                { id: Number(v.value), name: v.label}
            ));
            searchInfo.value.businessTypes = businessTypeOptions.value.map((item) => item.id);
            if (code !== undefined && invoiceCategory.value === "10080") {
                filterItemId = res.data.filter((item: any) => item.businessTypeModel.templateId === Number(code))[0].businessTypeModel.id;
            }
        });
    }
}
// 编辑页数据
let editOneData = reactive({});
let invoiceTypeList = computed((): Array<IInvoiceTypeListItem> => {
    return invoiceCategory.value === "10070"
        ? invoiceTypeListAll
        : invoiceTypeListAllPur
});
// 生成凭证页

const invoiceTypeListDirect = ref("top");
const handleSearchScroll = (scrollData: any) => {
    if (invoiceCategory.value === "10070") {
        if (scrollData.scrollTop > 120) {
            invoiceTypeListDirect.value = "bottom";
        } else {
            invoiceTypeListDirect.value = "top";
        }
    } else {
        if (scrollData.scrollTop > 180) {
            invoiceTypeListDirect.value = "bottom";
        } else {
            invoiceTypeListDirect.value = "top";
        }
    }
}
const forcedShutdown = ref(false);
const startVNumLimit = (e:string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.value.startVNum = val;
}
const endVNumLimit = (e:string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.value.endVNum = val;
}
const invoiceNumberLimit = (e:string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.value.number = val;
}
//发票查询条件增加凭证日期、凭证字号、是否生成单据、税率、备注
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = toRef(voucherGroupStore, "voucherGroupList");
watch(
    ()=>searchInfo.value.vgId,
    (val)=>{
        if(val === 0) {
            searchInfo.value.startVNum = "";
            searchInfo.value.endVNum = "";
        }
    }
)
const isRelation = ref(false); //是否关联进销存
const isEditting = computed(() => {
    return (
        (currentSlot.value === "add" && editInvoiceView.value.getAddSlotIsEditting()) ||
        currentSlot.value === "voucher" ||
        (currentSlot.value === "checkVoucher" && checkVoucherRef.value?.getEditedState() === true)
    );
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
const invoiceTypeListOptions = ref<Array<Option>>([]);
function initCheck() {
    const sourceList = invoiceCategory.value === "10070" ? invoiceTypeListAll : invoiceTypeListAllPur;  

    sourceList.forEach((v: IInvoiceTypeListItem) => {  
        if (v.IN_ID !== '0' && !route.query.invoiceTypes) {  
            searchInfo.value.invoiceTypes.push(Number(v.IN_ID));  
        }  
    }); 
    invoiceTypeListOptions.value = sourceList.map((v) => (
        { id: Number(v.IN_ID), name: v.IN_NAME, }
    ));
}
const searchOptions = ref();
watch(
    () => [invoiceTypeListOptions, businessTypeOptions],
    () => {
        searchOptions.value = {
            "invoiceTypes": invoiceTypeListOptions.value,
            "businessTypes": businessTypeOptions.value,
        }
    },
    {
        immediate: true,
        deep: true,
    }
)
const limitTaxRate = (e:string) => {
    let val = e.replace(/[^0-9.]/g, "");
    let decimalCount = val.split('.').length - 1; //出现小数点的次数,只能有一个小数点
    let decimalVal = 0; //保留小数位数只能一位
    if (decimalCount > 1) {
        val = val.slice(0, val.lastIndexOf('.'));
    }
    if(val.split('.').length > 1) {
        decimalVal = val.split('.')[1].length;
    }
    if (decimalVal > 1) {
        let index = val.split('.')[0].length + 2; //整数位+小数点.+一位小数
        searchInfo.value.taxRate = val.slice(0, index);
    } else {
        searchInfo.value.taxRate = val;
    }
}
const currentAddInvoiceSuccessType = ref("add");
function addInvoiceSuccess(invoiceDate: string, formType: string, data:any, flag?: boolean, ProjectIdChange?: boolean) {
    if(flag) {
        businessListAlter();
        return;
    }
    erpAssistingProjectList.value = data;
    currentSlot.value = "main";
    addOrEdit.value = "";
    currentAddInvoiceSuccessType.value = formType;
    if (formType === "add") {
        const [firstDay, lastDay] = getFirstAndLastDayOfMonth(invoiceDate);
        currentPeriodInfo.value = `${firstDay || " "}至${lastDay || " "}`;
        searchInfo.value.startDate = firstDay;
        searchInfo.value.endDate = lastDay;
        paginationData.currentPage = 1;
    }
    invoiceTableRef.value?.getTable()?.clearSelection();
    loadTableData().then(() => {        
        addComponentKey.value++;
        if(flag) {
            currentClickProp.value = "-1";
        }
        if(formType === "look" && ProjectIdChange) { //已凭证查看，项目值未修改，保存不用提示是否手动修改凭证弹窗
            editVoucherShow.value = true;
        }
    });
    (!isErp.value) && getBusinessTypeOptions();
}

function addBackMain(data: any) {
    erpAssistingProjectList.value = data;
    currentSlot.value = "main";
    addOrEdit.value = "";
    let timer = setTimeout(() => {
        addComponentKey.value++;
        clearTimeout(timer);
    }, 500);
    !isErp.value && getBusinessTypeOptions();
}
let searchStartDate = "";
let searchEndDate = "";
let restSalesStartDate = "";
let restSalesEndDate = "";
let restPurchaseStartDate = "";
let restPurchaseEndDate = "";
// 获取时间
async function GetSalesSearchDate() {
    const routerStartDate = route.query.searchStartDate as string;
    const routerEndDate = route.query.searchEndDate as string;
    if (routerStartDate && routerEndDate) {
        searchInfo.value.startDate = formatDate(routerStartDate);
        searchStartDate = formatDate(routerStartDate);
        searchInfo.value.endDate = formatDate(routerEndDate);
        searchEndDate = formatDate(routerEndDate);
    } else {
        await request({
            url: `/api/Invoice/Get${invoiceCategory.value === "10070" ? "Sales" : "Purchase"}SearchDate`,
            method: "post",
        }).then(async (res: IResponseModel<ISearchDate>) => {
            searchInfo.value.startDate = routerStartDate ? formatDate(routerStartDate) : res.data.startDate.substring(0, 10);
            searchInfo.value.endDate = routerEndDate ? formatDate(routerEndDate) : res.data.endDate.substring(0, 10);
            searchStartDate = routerStartDate ? formatDate(routerStartDate) : res.data.startDate.substring(0, 10);
            searchEndDate = routerEndDate ? formatDate(routerEndDate) : res.data.endDate.substring(0, 10);
        });
    }
    currentPeriodInfo.value = `${searchInfo.value.startDate || " "}至${searchInfo.value.endDate || " "}`;
    if (invoiceCategory.value === "10070") {
        restSalesStartDate = searchStartDate;
        restSalesEndDate = searchEndDate;
    } else {
        restPurchaseStartDate = searchStartDate;
        restPurchaseEndDate = searchEndDate;
    }
    await loadTableData();
}
let isProjectAccountingEnabled = ref(false);

// 表格

let loading = ref<boolean>(false);
let tableData = ref<Array<ITableItem>>([]);

let checkedTableData = ref<Array<ITableItem>>([]);
const setMoudleInvoiceSelect = "InvoiceSelectRecord";
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

function optionSuccess(optionType: number, count: number) {
    if (optionType === 2) {
        paginationData.total = paginationData.total - count;
        if (paginationData.currentPage > 1 && paginationData.currentPage * paginationData.pageSize >= paginationData.total) {
            paginationData.currentPage = paginationData.currentPage - 1;
        }
    }
    invoiceTableRef.value?.getTable()?.clearSelection();
    optionType === -1 ? GetSalesSearchDate() : loadTableData();
}
//识别导入pdf/ofd发票，查看返回导入成功的发票
const exportInvoiceIds = ref(false);
const saleSuccessInvoiceId = ref("");
const purSuccessInvoiceId = ref("");
function showSuccessImportInvoice(data: IImportSuccessData) {
    if (data) {
        restFilterInfo();
        searchInfo.value.startDate = data.startDate;
        searchInfo.value.endDate = data.endDate;
        if (invoiceCategory.value === "10070") {
            saleSuccessInvoiceId.value = data.invoiceIds.join(",");
        } else if (invoiceCategory.value === "10080") {
            purSuccessInvoiceId.value = data.invoiceIds.join(",");
        }
        currentPeriodInfo.value = `${searchInfo.value.startDate || " "}至${searchInfo.value.endDate || " "}`;
        loadTableData();
    }
}
function getQueryVal(name: string) {
    if (isKeyOfIFSearchItem(name)) {  
        const baseInfo = searchInfo.value[name] as string;  
        const filterInfo = filterSearchInfo[name] as string;
        return filterInfo ? `${baseInfo}${baseInfo ? '|-|': ''}${filterInfo}` : baseInfo;
    }
    return "";
}
function getCommonSelect(name: keyof IFSearchItem, option: Option[]) {  
    const list1 = searchInfo.value[name] as number[];  
    const list2 = filterSearchInfo[name] as number[]; 
    if (list2.length > 0) { 
        if (list1.length === option.length && list2.length === option.length) {  
            return "";  
        }  
        const set2 = new Set(list2);  
        const commonList = list1.filter(value => set2.has(value));    
        if (commonList.length) {  
            return commonList.join();  
        } else if (list1.length) {  
            return list1.join();  
        } else {  
            return list2.join();  
        }   
    } else {
        return list1.length === option.length ? "" : list1.join();
    } 
} 
function restFilterInfo() {
    filterSearchInfo.invoiceTypes = [];
    filterSearchInfo.businessTypes = [];
    filterSearchInfo.name = "";
    filterSearchInfo.note = "";
    filterSearchInfo.invoiceStatus = "0";
}
const filterParams = computed(() => {
    return {
        invoiceTypes: getCommonSelect("invoiceTypes", invoiceTypeListOptions.value),
        businessTypes: getCommonSelect("businessTypes", businessTypeOptions.value),
        name: getQueryVal("name"),
        note: getQueryVal("note"),
        number: getQueryVal("number"),
        invoiceStatus: searchInfo.value.invoiceStatus,
    }
});
let saleCurrentPage = 1;
let purCurrentPage = 1;
let hasLoading = false;
function loadTableData() {
    return new Promise((resolve, reject) => {
        loading.value = true;
        tableData.value = [];
        let params: ISearchParams = {
            name: getQueryVal("name"),
            startDate: searchInfo.value.startDate,
            endDate: searchInfo.value.endDate,
            startEntryDate: searchInfo.value.startEntryDate,
            endEntryDate: searchInfo.value.endEntryDate,
            number: getQueryVal("number"),
            invoiceVoucherStatus: searchInfo.value.invoiceVoucherStatus,
            invoiceStatus: searchInfo.value.invoiceStatus,
            invoiceSource: searchInfo.value.invoiceSource,
            invoiceType: searchInfo.value.invoiceType,
            businessTypes: getCommonSelect("businessTypes", businessTypeOptions.value),
            pageIndex: invoiceCategory.value === "10070" ? saleCurrentPage : purCurrentPage,
            pageSize: paginationData.pageSize,
            invoiceIds: invoiceCategory.value === "10070" ? saleSuccessInvoiceId.value : purSuccessInvoiceId.value,
            startVDate: searchInfo.value.startVDate,
            endVDate: searchInfo.value.endVDate,
            vgId: searchInfo.value.vgId + "",
            startVNum: searchInfo.value.startVNum,
            endVNum: searchInfo.value.endVNum,
            invoiceBillStatus: searchInfo.value.invoiceBillStatus,
            taxRate: searchInfo.value.taxRate,
            note: getQueryVal("note"),
            invoiceTypes: getCommonSelect("invoiceTypes", invoiceTypeListOptions.value),
            sortOrder: sortOrder.value,
            sortField: sortField.value,
        };
        if (invoiceCategory.value === "10080") {
            params.invoiceIssueDateStatus = searchInfo.value.invoiceIssueDateStatus;
            params.issueStartDate = searchInfo.value.issueStartDate;
            params.issueEndDate = searchInfo.value.issueEndDate;
        }
        if (!hasLoading) {
            tryClearCustomUrlParams(route);
            hasLoading = true;
        }
        request({
            url: `/api/Invoice/${invoiceCategory.value === "10070" ? "SalesPagingList" : "PurchasePagingList"}`,
            method: "get",
            params,
        })
            .then((res: IResponseModel<IPagingList>) => {
                loading.value = false;
                if (res.state === 1000) {
                    if (isErp.value) {
                        tableData.value = res.data.data.map((item)=>{
                            return {
                                ...item,
                                groupBills: item.verifiedBills ? handleBillData(item.verifiedBills) : [],
                            }
                        })
                    } else {
                        tableData.value = res.data.data;
                    }
                    if (isErp.value && !tableData.value[tableData.value.length - 1].name) {
                        tableData.value[tableData.value.length - 1].name = "合计";
                        tableData.value[tableData.value.length - 1].account = "";
                    }
                    tableData.value.forEach((item) => {
                        if (item.projectId < 0) {
                            item.projectId = 0;
                            item.projectName = "";
                        }
                    });
                    paginationData.total = res.data.count;
                    totalName();
                } else {
                    tableData.value = [];
                }
                params.invoiceIds ? (exportInvoiceIds.value = true) : (exportInvoiceIds.value = false);
                resolve(res);
            })
            .catch((err: any) => {
                loading.value = false;
                reject(err);
            });
    });
}

const handlebyDate = (startDate: string, endDate: string) => {
    handleReset();
    searchInfo.value.startDate = startDate;
    searchInfo.value.endDate = endDate;
    handleConfirm();
};

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
function checkCanSearch() {
    if (!searchInfo.value.startDate || !searchInfo.value.endDate) {
        ElNotify({
            type: "warning",
            message: `亲，开票${!searchInfo.value.startDate ? "起始" : "结束"}日期不能为空！`,
        });
        return false;
    }
    if (checkStartEndDate(searchInfo.value.startDate as string, searchInfo.value.endDate as string)) {
        ElNotify({
            type: "warning",
            message: "亲，开票起始日期不能大于开票结束日期",
        });
        return false;
    }
    if (!searchInfo.value.startEntryDate && searchInfo.value.endEntryDate) {
        ElNotify({
            type: "warning",
            message: "亲，录入起始日期不能为空！",
        });
        return false;
    }
    if (searchInfo.value.startEntryDate && !searchInfo.value.endEntryDate) {
        ElNotify({
            type: "warning",
            message: "亲，录入结束日期不能为空！",
        });
        return false;
    }
    if (checkStartEndDate(searchInfo.value.startEntryDate as string, searchInfo.value.endEntryDate as string)) {
        ElNotify({
            type: "warning",
            message: "亲，录入起始日期不能大于录入结束日期",
        });
        return false;
    }
    if (
        invoiceCategory.value === "10080" &&
        checkStartEndDate(searchInfo.value.issueStartDate as string, searchInfo.value.issueEndDate as string)
    ) {
        ElNotify({
            type: "warning",
            message: "亲，认证开始日期不能大于认证结束日期",
        });
        return false;
    }
    const startVDate = searchInfo.value.startVDate as string;
    const endVDate = searchInfo.value.endVDate as string;
    if (startVDate && endVDate &&checkStartEndDate(startVDate, endVDate)) {
        ElNotify({
            type: "warning",
            message: "亲，凭证起始日期不能大于凭证结束日期",
        });
        return false;
    }
    return true;
}
const handleConfirm = () => {
    if(!checkCanSearch()) return;
    invoiceCategory.value === "10070" ? (saleSuccessInvoiceId.value = "") : (purSuccessInvoiceId.value = "");
    paginationData.currentPage = 1;
    exportInvoiceIds.value = false;
    if(window.isErp) {
        invoiceTableRef.value?.getTable()?.clearSelection();  //重新搜索时之前选中的清空重选
    }
    restFilterInfo();
    loadTableData();
    handleClose();
    currentPeriodInfo.value = `${searchInfo.value.startDate || " "}至${searchInfo.value.endDate || " "}`;
};
const handleClose = () => containerRef.value?.handleClose();
const handleReset = () => {
    searchInfo.value.startDate = invoiceCategory.value === "10070" ? restSalesStartDate : restPurchaseStartDate;
    searchInfo.value.endDate = invoiceCategory.value === "10070" ? restSalesEndDate : restPurchaseEndDate;
    searchInfo.value.startEntryDate = "";
    searchInfo.value.endEntryDate = "";
    // (route.query.invoiceCode as string) ??
    searchInfo.value.number = "";
    searchInfo.value.invoiceType = (route.query.invoiceType as string) ?? "0";
    searchInfo.value.invoiceVoucherStatus = "0";
    searchInfo.value.name = "";
    searchInfo.value.issueStartDate = "";
    searchInfo.value.invoiceIssueDateStatus = "0";
    searchInfo.value.invoiceStatus = "0";
    searchInfo.value.invoiceSource = "0";
    searchInfo.value.issueEndDate = "";
    searchInfo.value.businessTypes = businessTypeOptions.value.map((item) => item.id);
    currentPeriodInfo.value = `${searchInfo.value.startDate || " "}至${searchInfo.value.endDate || " "}`;
    invoiceCategory.value === "10070" ? (saleSuccessInvoiceId.value = "") : (purSuccessInvoiceId.value = "");
    searchInfo.value.startVDate = "";
    searchInfo.value.endVDate = "";
    searchInfo.value.vgId = 0;
    searchInfo.value.taxRate = "";
    searchInfo.value.note = "";
    searchInfo.value.invoiceBillStatus = "0";
    initCheck();
};
const scmRelation = ref<ScmRelationInfo>();

const scmProductType = ref(0);
const scmAsid = ref(0);
const cstId = ref(0);
async function handleGetScmRelationInfo() {
    await getScmRelationInfo()
        .then((res) => {
            scmProductType.value = res.data.scmProductType;
            scmAsid.value = res.data.scmAsid;
            cstId.value = res.data.cstId;
            scmRelation.value = res.data;
            isRelation.value = res.data.isRelation;
            columnQuery();
            if (window.isErp) {
                getIsProjectAccountingEnabled({ scmProductType: scmProductType.value, scmAsid: scmAsid.value });
                getPriceAndUnitCount();
            }
        })
        .catch((err) => {
            console.log(err);
        });
}

function getIsProjectAccountingEnabled(data: any) {
    request({
        url: "/api/AsubRelation/IsProjectAccountingEnabled",
        method: "post",
        params: data,
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            isProjectAccountingEnabled.value = window.isErp && res.data;
        }
    });
}
const priceFloat = ref(0);
const quantityFloat = ref(0);
const isFullVersionErp = ref(true); //业财环境是否同时购买了记账和进销存服务
function getPriceAndUnitCount() {
    request({
        url: "/api/BaseDataForErp/GetErpSet", 
        method: "get",
    }).then((res: any) => {
        priceFloat.value = res.data.priceFloatWidth;
        quantityFloat.value = res.data.qtyFloatWidth;
        isFullVersionErp.value = res.data.isFullVersionErp;
    })
}

let checkedTableDataSale: ITableItem[] = [];
let checkedTableDataParch: ITableItem[] = [];

const maxLength = 600;
function tableSelectionChange(value: any) {
    checkedTableData.value = value.filter((v: ITableItem) => v.invoiceType !== 0 && v.invoiceCategory === Number(invoiceCategory.value));
    if (invoiceCategory.value === "10070") {
        checkedTableDataSale = checkedTableData.value;
    } else {
        checkedTableDataParch = checkedTableData.value;
    }
}
function checkHasMoreSelect() {
    if (checkedTableData.value.length > maxLength) {
        ElNotify({ type: "warning", message: `最多支持${maxLength}条哦！` });
        const moreData = checkedTableData.value.slice(maxLength);
        checkedTableData.value = checkedTableData.value.slice(0, maxLength);
        moreData.forEach((item) => {
            invoiceTableRef.value?.getTable()?.toggleRowSelection(item, false);
        });
    }
}
function handleSingleCheckMoreSelect() {
    nextTick().then(() => {
        checkHasMoreSelect();
    });
}

let SummaryText = "合计";
function totalName() {
    let isLastPage = paginationData.currentPage * paginationData.pageSize >= paginationData.total ? true : false;
    if (isErp.value) {
        if (showAll.value && isProjectAccountingEnabled.value) {
            if (!tableData.value[tableData.value.length - 1].projectName && isLastPage) {
                tableData.value[tableData.value.length - 1].projectName = SummaryText;
            }
            if (tableData.value[tableData.value.length - 1].name === SummaryText) {
                tableData.value[tableData.value.length - 1].name = "";
            }
        } else {
            if (tableData.value[tableData.value.length - 1].projectName === SummaryText) {
                tableData.value[tableData.value.length - 1].projectName = "";
            }
            if (!tableData.value[tableData.value.length - 1].name) {
                tableData.value[tableData.value.length - 1].name = SummaryText;
            }
        }
    } else {
        if (showAll.value) {
            if (!tableData.value[tableData.value.length - 1].projectName && isLastPage) {
                tableData.value[tableData.value.length - 1].projectName = SummaryText;
            }
            if (tableData.value[tableData.value.length - 1].businessTypeText === SummaryText) {
                tableData.value[tableData.value.length - 1].businessTypeText = "";
            }
        } else {
            if (tableData.value[tableData.value.length - 1].projectName === SummaryText) {
                tableData.value[tableData.value.length - 1].projectName = "";
            }
            if (!tableData.value[tableData.value.length - 1].businessTypeText) {
                tableData.value[tableData.value.length - 1].businessTypeText = SummaryText;
            }
        }
    }
}

watch(showAll, (v) => {
    totalName();
});
const salesPeriodInfo = ref("");
const purchasePeriodInfo = ref("");

const quickAddInvoiceFlag = ref(false);
watch(invoiceCategory, (newVal, oldVal) => {
    handleInvoiceCategoryChange(newVal);
});
function handleInvoiceCategoryChange(newVal: string) {
    rightButtonKey.value++;
    if (newVal === "10070") {
        purchaseSearchInfo.value = _.cloneDeep(searchInfo.value);
        searchInfo.value = _.cloneDeep(salesSearchInfo.value);
        purchasePeriodInfo.value = currentPeriodInfo.value;
        currentPeriodInfo.value = salesPeriodInfo.value;
        changeSearchParam();
    } else {
        salesSearchInfo.value = _.cloneDeep(searchInfo.value);
        searchInfo.value = _.cloneDeep(purchaseSearchInfo.value);
        salesPeriodInfo.value = currentPeriodInfo.value;
        currentPeriodInfo.value = purchasePeriodInfo.value;
        changeSearchParam();
    }
    if (searchInfo.value.startDate === "") {
        GetSalesSearchDate();
    } else {
        loadTableData();
    }
    columnQuery();
    initCheck();
    searchInfo.value.businessTypes = businessTypeOptions.value.map((item) => item.id);
    invoiceColumns.value = Columns(showAll.value, newVal, scmRelation.value?.isRelation || false, isProjectAccountingEnabled.value);
    checkedTableData.value = newVal === "10070" ? checkedTableDataSale : checkedTableDataParch;
}
//快速新增发票成功
const resultDate = ref();
function changeSearchParam() {
    if (quickAddInvoiceFlag.value) {
        changeSearchParamDate();
        quickAddInvoiceFlag.value = false;
    }
}
function changeSearchParamDate() {
    searchInfo.value.startDate = resultDate.value.startDate;
    searchInfo.value.endDate = resultDate.value.endDate;
    currentPeriodInfo.value = `${searchInfo.value.startDate || " "}至${searchInfo.value.endDate || " "}`;
}
function quickAddInvoiceSuccess(data: IResoneQuickAddInvoice) {
    if (data) {
        restFilterInfo();
        resultDate.value = getDateMonth(data.invoiceDateText);
        if (invoiceCategory.value === "10070") { //在销项
            if (String(data.invoiceCategory) === "10080") { //添加的进项的发票
                quickAddInvoiceFlag.value = true;
                globalWindowOpenPage("/Invoice/PurchaseInvoice?from=SalesInvoice&searchStartDate=" + resultDate.value.startDate + "&searchEndDate=" + resultDate.value.endDate, "进项发票");
            } else { //添加的销项的发票
                purchaseSearchInfo.value = _.cloneDeep(searchInfo.value);
                purchasePeriodInfo.value = currentPeriodInfo.value;
                changeSearchParamDate();
                loadTableData();
            }
        } else if (invoiceCategory.value === "10080") { //在进项
            if (String(data.invoiceCategory) === "10070") { //添加的销项的发票
                globalWindowOpenPage("/Invoice/SalesInvoice?from=PurchaseInvoice&searchStartDate=" + resultDate.value.startDate + "&searchEndDate=" + resultDate.value.endDate, "销项发票");
                quickAddInvoiceFlag.value = true;
            } else { //添加的进项的发票
                salesSearchInfo.value = _.cloneDeep(searchInfo.value);
                salesPeriodInfo.value = currentPeriodInfo.value;
                changeSearchParamDate();
                loadTableData();
            }
        }
    }
}
//获取日期所在的月份
function getDateMonth(date: string) {
    let givenDate = new Date(date);
    let year = givenDate.getFullYear();
    let month = givenDate.getMonth() + 1; // 月份是从 0 开始计数的，所以需要加 1
    let firstDayOfMonth = year + '-' + (month < 10 ? '0' : '') + month + '-01';
    let lastDayOfMonth = year + '-' + (month < 10 ? '0' : '') + month + '-' + new Date(year, month, 0).getDate();
    // 输出当前月份的起始日期和结束日期
    let result = {
        startDate: firstDayOfMonth,
        endDate: lastDayOfMonth
    }
    return result; 
}
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    if (invoiceCategory.value === "10070") {
        saleCurrentPage = paginationData.currentPage;
    } else {
        purCurrentPage = paginationData.currentPage;
    }
    loadTableData();
});
function goMasterPlateView() {
    masterPlateView.value?.initCategory(invoiceCategory.value);
    currentSlot.value = "masterPlate";
}
let timer: number = 0;
onBeforeUnmount(() => {
    unInvoiceTaskList();
});

const reInvoiceTaskList = () => {
    if (timer) return;
    timer = setInterval(() => {
        getInvoiceTaskList();
    }, window.invoiceTaskPolllingInterval*60*1000);
};

const unInvoiceTaskList = () => {
    timer && clearTimeout(timer);
    timer = 0;
};

const invoiceTaskList = ref<Array<IInvoiceTaskResult>>();
const getInvoiceTaskList = async () => {
    await request({
        url: `/api/TaxBureau/LastSummaryTask`,
    }).then((res: IResponseModel<Array<IInvoiceTaskResult>>) => {
        invoiceTaskList.value = res.data;
        hasInvoiceTask.value = invoiceTaskList.value && invoiceTaskList.value[0] && invoiceTaskList.value[0].status === 1;
        nextTick(() => {
            if (hasInvoiceTask.value) {
                reInvoiceTaskList();
            } else {
                unInvoiceTaskList();
            }
        });
        const taskId = getCollectTaskId();
        if (taskId && invoiceTaskList.value && invoiceTaskList.value[0] &&
                ((invoiceTaskList.value[0].invoiceTaskStatus === 2 && invoiceTaskList.value[0].fileTaskStatus > 0) ||
                (invoiceTaskList.value[0].invoiceTaskStatus > 2))
            ) {
            setCollectTaskId(0);
            fetchInvoiceShow.value = false;
            fetchInvoiceTaskShow.value = false;
            lastFetchInvoiceTaskShow.value = true;
            window.dispatchEvent(new CustomEvent("modifyAccountSet"));
        }
    });
};

const asId = useAccountSetStore().accountSet?.asId as number;
const setCollectTaskId = (val: number) => {
    setCookie("collectTaskId" + asId, val.toString(), "d14");
};
const getCollectTaskId = () => {
    const id = getCookie("collectTaskId" + asId);
    return id ? parseInt(id) : 0;
};

const hasInvoiceTask = ref(false);

const finishedInvoiceTask = computed(() => {
    if (!invoiceTaskList.value) return null;
    for (let i = 0; i < invoiceTaskList.value.length; i++) {
        if (invoiceTaskList.value[i].invoiceTaskStatus !== 1 ) return invoiceTaskList.value[i];
    }

    return null;
});
const finishedInvoiceTaskTime = computed(() => {
    if (!finishedInvoiceTask.value) return "";
    return finishedInvoiceTask.value.modifiedDate;
});

const fetchInvoiceShow = ref(false);

let preCheck = false;
const showFetchInvoice = () => {
    if (preCheck) return;
    if (hasInvoiceTask.value) {
        if((invoiceTaskList.value!)[0].fileTaskStatus === 1){
            ElNotify({ type: "warning", message: "取票仍在持续同步获取发票原件中，可通过取票记录查看获取情况，请获取发票原件任务结束后再执行一键取票。" });
            return;
        } else {
            fetchInvoiceShow.value = true;
            preCheck = false;
            return;
        }
    }

    preCheck = true;
    request({ url: "/api/TaxBureau/PreCheck", method: "get" })
        .then((res: IResponseModel<IInvoiceTaskValue>) => {
            if (res.state === 1000) {
                fetchInvoiceShow.value = true;
                return;
            } else if (res.state === 2000) {
                if (res.data.result === "running" && hasInvoiceTask.value) {
                    fetchInvoiceShow.value = true;
                    return;
                }
            }

            ElNotify({ type: "warning", message: res.msg });
            return;
        })
        .catch((err) => {
            console.log(err);
            ElNotify({ type: "warning", message: "取票预检查发生错误，请稍后重试。" });
        })
        .finally(() => {
            preCheck = false;
        });
};

const fetchInvoiceTaskShow = ref(false);
const lastFetchInvoiceTaskShow = ref(false);

const openFetchInvoiceTaskShow = () => {
    fetchInvoiceTaskShow.value = true
}

// 发票附件
const readonly = ref(false);
let isAttachFileRow: ITableItem | null = null;
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
const getAttachFileList = (row: ITableItem) => {
    readonly.value = row.isCheckOut;
    const params = { invoiceId: row.invoiceId };
    request({
        url: "/api/Invoice/GetAttachFileList?" + getUrlSearchParams(params),
        method: "post",
    }).then((res: IResponseModel<IInvoiceAttachFileList>) => {
        if (res.state === 1000 && res.data.result) {
            let list = res.data.data;
            list = list.map((item: any) => {
                item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                return item;
            });
            const dateMonth = row.entryDate.split("-").slice(0, 2);
            const startDate = dateMonth.join("-") + "-01";
            const day = getDaysInMonth(Number(dateMonth[0]), Number(dateMonth[1]));
            const endDate = dateMonth.join("-") + "-" + day;
            const _params = {
                eRecordSearchDate: { startDate, endDate },
                invoiceId: row.invoiceId,
                fileCategory: invoiceCategory.value === "10070" ? 10 : 11,
            };
            uploadFileDialogRef.value?.open(_params, list, res.data.parentId);
            isAttachFileRow = row;
        } else {
            ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
        }
    });
};

async function saveAttachFile(_params: any, newFileids: Number[], delFileids: Number[], fileList: any[]) {
    const newAttachFileCount = ~~isAttachFileRow!.attachFileCount + newFileids.length - delFileids.length;
    const needToast = await checkNeedToastWithBillAndVoucher(_params.invoiceId, delFileids.join(","));
    if (!needToast) {
        saveAttachFileFn(_params, newFileids, delFileids, fileList, newAttachFileCount);
        return;
    }
    showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
        saveAttachFileFn(_params, newFileids, delFileids, fileList, newAttachFileCount, batchDelte);
    });
}
function saveAttachFileFn(_params: any, newFileids: Number[], delFileids: Number[], fileList: any[], newAttachFileCount: number, batchDelte = false) {
    const params = {
        invoiceId: _params.invoiceId,
        newFileids: newFileids.join(","),
        delFileids: delFileids.join(","),
        isNeedSaveToVoucherForAdd: true,
        isNeedSaveToVoucherForDelete: batchDelte,
    };
    request({
        url: "/api/Invoice/SaveAttachFile?" + getUrlSearchParams(params),
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state == 1000 && res.data) {
                if (isAttachFileRow !== null) {
                    isAttachFileRow.attachFileCount = newAttachFileCount;
                }
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
        })
        .finally(() => {
            isAttachFileRow = null;
        });
}
async function checkNeedToastWithBillAndVoucher(invId: number, delFileids: string) {
    return await request({
        url: "/api/Invoice/GetNeedSaveToVoucherForSaveAttachFile",
        method: "post",
        params: { invId, delFileids },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000) return false;
        return res.data;
    }).catch(() => {
        return false;
    })
}

// 查看/编辑
let lookEditRow: ITableItem;
function handleEditRow(item: ITableItem, type: string) {
    addOrEdit.value = type;
    invoiceType.id = String(item.invoiceType);
    invoiceType.title = invoiceTypeListAll.find((item: IInvoiceTypeListItem) => item.IN_ID === invoiceType.id)?.IN_NAME as string;
    if (type === "look") {
        lookEditRow = JSON.parse(JSON.stringify(item));
    }
    currentSlot.value = "add";
    if (!window.isErp) {
        editInvoiceView.value?.initBusinessType(
            invoiceCategory.value,
            invoiceCategory.value === "10070" ? businessSalesOptions.value : businessPurchaseOptions.value
        );
    }
    editInvoiceView.value?.initEditMainForm(item);
}
// 新增
function handleInvoiceTypeChecked(val: IInvoiceTypeListItem) {
    currentSlot.value = "add";
    addOrEdit.value = "add";
    invoiceType.title = val.IN_NAME;
    invoiceType.id = val.IN_ID;
    editInvoiceView.value?.initPage(invoiceType.id);
    if (!window.isErp) {
        editInvoiceView.value?.initBusinessType(
            invoiceCategory.value,
            invoiceCategory.value === "10070" ? businessSalesOptions.value : businessPurchaseOptions.value
        );
    }
    editInvoiceView.value?.changeAddSlotInit(true);
}
function handleDeleteRow(item: ITableItem) {
    if (item.isOffseted) {
        ElNotify({
            type: "warning",
            message: "当前发票已核销，无法删除~",
        });
        return false;
    }
    if (item.pid !== 0 && item.vid !== 0) {
        ElNotify({
            type: "warning",
            message: "当前发票已生成凭证，请先删除凭证，再删除发票哦~",
        });
        return false;
    }
    if (item.billId !== 0) {
        let billType = invoiceCategory.value === "10070" ? "出库单" : "入库单";
        ElNotify({
            type: "warning",
            message: `当前发票已生成${billType}，请先删除${billType}，再删除发票哦~`,
        });
        return false;
    }
    ElConfirm("亲，确认要删除吗?").then((r: boolean) => {
        if (r) {
            let params = {
                invoiceId: item.invoiceId,
                invoiceType: item.invoiceType,
            };
            request({
                url: `/api/Invoice`,
                method: "delete",
                params,
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({
                            message: "删除成功",
                            type: "success",
                        });
                        paginationData.total--;
                        if (
                            paginationData.currentPage * paginationData.pageSize >= paginationData.total &&
                            paginationData.currentPage > 1
                        ) {
                            paginationData.currentPage--;
                        }
                        loadTableData();
                        invoiceTableRef.value?.getTable()?.toggleRowSelection(item, false);
                    } else {
                        ElNotify({
                            message: res.msg || "删除失败，请联系客服或管理员～",
                            type: "warning",
                        });
                    }
                })
                .catch((e) => {
                    throw e;
                });
        }
    });
}
// 进入发票生成凭证页
let isGenVoucher = false;
function loadVoucherView() {
    if (isGenVoucher) {
        ElNotify({
            type: "warning",
            message: "正在生成凭证，请稍后~",
        });
        return;
    }
    isGenVoucher = true;
    let available: any = [];
    let invoiceIds: number[] = [];

    let existHasVoucher = false;
    let existInvalid = false;
    let existMinDate = false;
    if (checkedTableData.value.length) {
        checkedTableData.value.forEach((v: ITableItem) => {
            if (v.vidText) {
                existHasVoucher = true;
            }
            if (v.isInvalid) {
                existInvalid = true;
            }

            if (v.invoiceDateText < minCheckOutDate) {
                existMinDate = true;
            }
        });
        available = checkedTableData.value.filter((v: ITableItem) => {
            return !v.vidText && v.invoiceStatus === "正常";
        });
    }

    if (!checkedTableData.value.length || !available.length) {
        isGenVoucher = false;
        ElNotify({
            type: "warning",
            message: `请选择未生成凭证且未作废的发票数据后生成凭证`,
        });
        return;
    }

    available.forEach((v: ITableItem) => {
        invoiceIds.push(v.invoiceId);
    });
    if (existMinDate && generateVoucherSettingDialogRef.value?.getVoucherSetting().voucherDate === 0) {
        ElConfirm(
            "亲，您当前凭证生成日期设置为开票日期且所选发票中存在开票日期月份已结账的情况，是否使用会计日期（左上角帐套名旁期间）生成凭证？"
        ).then((r: boolean) => {
            if (r) {
                isGenVoucher = false;
                showJournalSettings();
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else if (existInvalid && existHasVoucher) {
        ElConfirm("亲，已生成凭证的单据或已作废发票数据将会被跳过，是否继续生成凭证？").then((r: boolean) => {
            if (r) {
                loadGenParameters();
                loadGenVoucher(invoiceIds);
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else if (existInvalid) {
        ElConfirm("亲，已作废发票数据将会被跳过，是否继续生成凭证？").then((r: boolean) => {
            if (r) {
                loadGenParameters();
                loadGenVoucher(invoiceIds);
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else if (existHasVoucher) {
        ElConfirm("亲，已生成凭证的单据将会被跳过，是否继续生成凭证？").then((r: boolean) => {
            if (r) {
                loadGenParameters();
                loadGenVoucher(invoiceIds);
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else {
        loadGenParameters();
        loadGenVoucher(invoiceIds);
    }
}

function loadGenParameters() {
    // 从缓存中读取凭证合并的选项 
    if (window.localStorage.getItem("genVoucher-invoice")) {
        const jsonObject = JSON.parse(window.localStorage.getItem('genVoucher-invoice') as string);
        let parseObject = jsonObject as InvoiceQueryParameters;
        Object.setPrototypeOf(parseObject, InvoiceQueryParameters.prototype);
        
        if(selectedList.value.length === 0){
            if(parseObject.mergeDate){
                selectedList.value.push(1);
            }
            if(parseObject.mergeOpposite){
                selectedList.value.push(2);
            }
            if(parseObject.mergeInvoiceType){
                selectedList.value.push(4);
            }
            if(parseObject.mergeBusinessType){
                selectedList.value.push(8);
            }
            if(parseObject.isMerge){
                selectedList.value.push(0);
            }
        }
        
        if(subjectSelectedList.value.length === 0){
            if(parseObject.mergeCredit){
                subjectSelectedList.value.push(32);
            }
            if(parseObject.mergeDebit){
                subjectSelectedList.value.push(16);
            }
        }

        genVoucherQueryParameters.value = parseObject;
        
        if(jsonObject.autoAIGenerate === undefined) {
            genVoucherQueryParameters.value.autoMatch = false;
            genVoucherQueryParameters.value.autoMatchStock = false;
            genVoucherQueryParameters.value.autoAIGenerate = true;
        }
    } else {
        genVoucherQueryParameters.value.autoMatch = false;
        genVoucherQueryParameters.value.autoMatchStock = false;
        genVoucherQueryParameters.value.autoAIGenerate = true;
    }
}

let genVoucherInvoiceIds: number[] = [];
function loadGenVoucher(invoiceIds?: number[]) {
    if (invoiceIds) {
        genVoucherInvoiceIds = invoiceIds;
    }
    const parameters = {
        InvoiceIds: genVoucherInvoiceIds,
        AutoMatch: genVoucherQueryParameters.value.autoMatch,
        AutoMatchStock: genVoucherQueryParameters.value.autoMatchStock,
        autoAIGenerate: genVoucherQueryParameters.value.autoAIGenerate,
        VoucherSettings: generateVoucherSettingDialogRef.value?.getVoucherSetting(),
    };
    request({
        url: "/api/InvoiceVoucher/BatchGenerateVoucher",
        method: "post",
        data: parameters,
        headers: {
            "Content-Type": "application/json",
        },
    })
        .then((res: any) => {
            if (res.state !== 1000) {
                ElNotify({
                    type: "warning",
                    message: res.msg || "生成凭证失败，请稍后重试~",
                });
                isGenVoucher = false;
                return;
            }
            const data = res as IResponseModel<IBatchGenerateVoucherModel<InvoiceWithVoucherModel>>;
            genVoucherChanged.value = false;
            baseVoucherChanged.value = false;
            if (genVoucherQueryParameters.value.isMerge && !checkMergeVoucher(data.data)) {
                genVoucherQueryParameters.value.isMerge = false;
            }
            nextTick(() => {
                generateVoucherView.value?.loadDocumentList(data.data, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                currentSlot.value = "voucher";
                
                saveGenerateVoucherLog(data.data);
                const tiemr = setTimeout(() => {
                    isGenVoucher = false;
                    clearTimeout(tiemr);
                }, 1000);
            });
        })
        .catch(() => {
            isGenVoucher = false;
            loadSuccess.value = true;
            ElNotify({
                type: "warning",
                message: "生成凭证失败，请稍后重试~",
            });
            currentSlot.value = "voucher";
            const tiemr = setTimeout(() => {
                isGenVoucher = false;
                clearTimeout(tiemr);
            }, 1000);
        });
}
// 智能匹配存货弹框
const DialogMatchStockRef = ref<InstanceType<typeof DialogMatchStock>>();
const openMatchStockDialog = () => {
    DialogMatchStockRef.value?.getStockList(genVoucherInvoiceIds);
};
// 如果获取的数据里有错误数据，就不可以合并凭证
function checkMergeVoucher(data: IBatchGenerateVoucherModel<InvoiceWithVoucherModel>) {
    if (data) {
        for (let i = 0; i < data.documentWithVoucherList.length; i++) {
            if (data.documentWithVoucherList[i].document.errorInfo !== "") {
                ElNotify({ message: "亲，生成的凭证存在错误暂不支持合并，请依据错误提示修改凭证后再进行合并哦！", type: "warning" });
                return false;
            }
        }
    }
    return true;
}

function genVoucherChangedHandle() {
    genVoucherChanged.value = true;
}

function saveGenerateVoucherLog(model: IBatchGenerateVoucherModel<InvoiceWithVoucherModel>) {
    const data : IVoucherOperateLog = {
        operateType: 1010,
        setting: "",
        content: "",
    };

    for (let idx in model.documentWithVoucherList) {
        const documentWithVoucher = model.documentWithVoucherList[idx];
        if (data.content.length > 0) data.content += ",";
        data.content += documentWithVoucher.document.invoiceId;
    }

    saveVoucherGenerateLog(data);
}

function saveVoucherLog(parameters: GenVoucherParameters<BaseDocumentModel>) {
    const data : IVoucherOperateLog = {
        operateType: 1030,
        setting: "",
        content: "",
    };

    for (let idx in parameters.vouchers) {
        const voucherModel = parameters.vouchers[idx];
        data.content += 'invId: ' + voucherModel.documentList.map((d) => (d as InvoiceDocumentModel).invoiceId).join(",") + '\n';
        
        data.content += 'vgId: '+ voucherModel.voucher.vgId + '\n';
        for (let vIdx in voucherModel.voucher.voucherLines) {
            const voucherLine = voucherModel.voucher.voucherLines[vIdx];
            data.content += `${voucherLine.asubName} ${voucherLine.credit} ${voucherLine.debit}\n`; 
        }
        data.content += '\n';
    }

    saveVoucherGenerateLog(data);
}

function saveVoucherGenerateLog(data: IVoucherOperateLog) {
    data.unixTime = new Date().getTime();
    data.sourceType = 2010;
    data.setting = JSON.stringify(genVoucherQueryParameters.value);
    request({
        url: "/api/Intelligence/SaveVoucherGenerateLog",
        method: "post",
        data: data,
    })
}

function saveGenVoucherCheck() {
    const parameters = generateVoucherView.value?.getGenVoucherParameters();
    let flag = true;
    if (parameters) {
        for (let i = 0; i < parameters.temporaryAccountSubjectList.length; i++) {
            if (!flag) {
                return false;
            }
            if (parameters.temporaryAccountSubjectList[i].simpleAsubName.length > 254) {
                parameters.vouchers.forEach((v) => {
                    if (
                        v.voucher.voucherLines.filter(
                            (vl) => vl.asubCode === parameters.temporaryAccountSubjectList[i].asubCode && vl.asubId === 0
                        ).length > 0
                    ) {
                        {
                            ElNotify({
                                type: "warning",
                                message: "亲，自动生成的科目名称超过254个字，请修改后重试~",
                            });
                            flag = false;
                            return false;
                        }
                    }
                });
            }
        }
        for (let i = 0; i < parameters.temporaryAssitEntryList.length; i++) {
            if (!flag) {
                return false;
            }
            if (parameters.temporaryAssitEntryList[i].aaname.length > 256) {
                parameters.vouchers.forEach((v) => {
                    if (
                        v.voucher.voucherLines.filter(
                            (vl) =>
                                vl.assistingAccounting === 1 &&
                                vl.asubName.includes(
                                    "_" + parameters.temporaryAssitEntryList[i].aanum + " " + parameters.temporaryAssitEntryList[i].aaname
                                )
                        ).length > 0
                    ) {
                        let name: string =
                            parameters.temporaryAssitEntryList[i].aatype === 10001
                                ? "客户名称"
                                : parameters.temporaryAssitEntryList[i].aatype === 10006
                                ? "存货名称"
                                : "供应商名称";
                        ElNotify({
                            type: "warning",
                            message: `亲,自动生成的辅助核算${name}超过256个字,请修改后重试~`,
                        });
                        flag = false;
                        return false;
                    }
                });
            }
        }
        return flag;
    }
}

let isSaving = false;
async function saveGenVoucher() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) {
        ElNotify({ message: "正在保存，请稍后！", type: "warning" });
        return;
    }
    isSaving = true;
    let res = saveGenVoucherCheck();
    const parameters = generateVoucherView.value?.getGenVoucherParameters();
    if (parameters && res) {
        useLoading().enterLoading("努力加载中，请稍候...");
        request({
            url: "/api/InvoiceVoucher/FindNeedAutoInsertAccountSubject",
            method: "post",
            data: parameters,
        })
            .then((res: any) => {
                const data = res as IResponseModel<IGenVoucherNeedInsertAsub>;
                if (data.state === 1000) {
                    new Promise<void>((resolve, reject) => {
                        for (let i = 0; i < parameters.temporaryAccountSubjectList.length; i++) {
                            if (
                                parameters.temporaryAccountSubjectList[i].asubCode
                                    .toString()
                                    .indexOf(parameters.temporaryAccountSubjectList[i].parentAsubCode.toString()) !== 0
                            ) {
                                isSaving = false;
                                useLoading().quitLoading();
                                ElConfirm("您的科目编码长度不足，智能匹配失败，是否立即前往科目编码设置？").then((r) => {
                                    if (r) {
                                        globalWindowOpenPage("/Settings/AccountSubject", "科目");
                                    }
                                });
                                reject();
                                return;
                            }
                        }
                        if (data.data.autoInsertAsubList.length > 0) {
                            const parentAsubs = [];
                            const asubs = [];
                            for (let i = 0; i < data.data.autoInsertAsubList.length; i++) {
                                parentAsubs.push(data.data.autoInsertAsubList[i].parentAsubName);
                                asubs.push(data.data.autoInsertAsubList[i].asubName);
                            }
                            const msg =
                                '<div>' +
                                parentAsubs.join("、") +
                                "已有凭证，将新增同名下级科目"+ '<br>' +
                                asubs.join("、") +
                                "替代，您要继续吗？" +
                                "</div>";
                            useLoading().quitLoading();
                            ElAlert({ message: msg, leftJustifying: true }).then((r) => {
                                if (r) {
                                    useLoading().enterLoading("努力加载中，请稍候...");
                                    resolve();
                                } else {
                                    isSaving = false;
                                    reject();
                                }
                            });
                        } else {
                            resolve();
                        }
                    }).then(() => {
                        request({
                            url: "/api/InvoiceVoucher/SaveInvoiceVoucher",
                            method: "post",
                            data: parameters,
                            timeout: 0,
                        })
                            .then((res: IResponseModel<IInvocieGenVoucherResult>) => {
                                useLoading().quitLoading();
                                const data = res;
                                if (data.state === 1000) {
                                    generateVoucherView.value?.loadSaveResult(data.data);
                                    dispatchReloadAsubAmountEvent();
                                    useAccountSubjectStore().getAccountSubject();
                                    useAssistingAccountingStore().getAssistingAccounting();
                                    saveVoucherLog(parameters);
                                    const timer = setTimeout(() => {
                                        isSaving = false;
                                        clearTimeout(timer);
                                    }, 1000);
                                } else if (data.state === 2000) {
                                    isSaving = false;
                                    ElNotify({
                                        type: "warning",
                                        message: data.msg || "保存出错了，请刷新页面重试或联系客服处理！",
                                    });
                                } else if (data.state === 9999) {
                                    isSaving = false;
                                    ElNotify({
                                        type: "warning",
                                        message: "保存出错了，请刷新页面重试或联系客服处理！",
                                    });
                                } else {
                                    generateVoucherView.value?.loadSaveResult(data.data);
                                    const timer = setTimeout(() => {
                                        isSaving = false;
                                        clearTimeout(timer);
                                    }, 1000);
                                }
                            })
                            .catch((err: any) => {
                                if (err.code !== "ERR_NETWORK") {
                                    isSaving = false;
                                    useLoading().quitLoading();
                                    ElNotify({
                                        type: "warning",
                                        message: "保存出错了，请刷新页面重试或联系客服处理！",
                                    });
                                }
                            });
                    });
                } else {
                    isSaving = false;
                    useLoading().quitLoading();
                }
            })
            .catch(() => {
                isSaving = false;
                isSaving = false;
                useLoading().quitLoading();
            });
    } else {
        isSaving = false;
    }
}
function genVoucherSaveSuccess() {
    currentSlot.value = "main";
    invoiceTableRef.value?.getTable()?.clearSelection();
    loadTableData();
}

function setCancelRowStyle(data: { row: any; rowIndex: number }): string {
    if (data.row.invoiceStatus === "作废") {
        return "highlight-red";
    }
    if (checkedTableData.value.findIndex((item: ITableItem) => item.invoiceId === data.row.invoiceId) >= 0) {
        return "el-table__row--striped";
    }
    return "";
}

function setSelectable(row: any, rowIndex: number): boolean {
    if (row.invoiceId === 0) {
        return false;
    }
    return true;
}

// 跳转查看凭证
function jumpLookVoucher(pid: number, vid: number) {
    let fcode = invoiceCategory.value === "10070" ? "invoice-output-cancreatevoucher" : "invoice-input-cancreatevoucher";
    let from = invoiceCategory.value === "10070" ? "salesinvoice" : "purchaseinvoice";
    globalWindowOpenPage(`/Voucher/VoucherPage?pid=${pid}&vid=${vid}&fcode=${fcode}&from=${from}`, "查看凭证");
}

function tableRowClick(row: any, column: any, event: any) {
    if (column.label === "业务类型" || column.label === "项目") {
        return;
    }
    if (!setSelectable(row, row.index)) return;
    if (row.invoiceId === 0) return;
    let selected = checkedTableData.value.findIndex((item: any) => item.invoiceId === row.invoiceId) >= 0 ? true : false;
    invoiceTableRef.value?.getTable()?.toggleRowSelection(row, !selected);
    checkHasMoreSelect();
}

//列表修改
//业务类型
//项目编辑
const currentClickRowId = ref("");
const currentClickProp = ref("");
const currentProjectId = ref();
const voucherTip = ref(false);
const editVoucherShow = ref(false);
const projectItemRef = ref();
let saveCurrentRow: any;
let saveCurrentColumn: any;

// 凭证提示
const handleTipConfirm = () => {
    if (getCookie("tempHiddenInvoice-" + getGlobalToken()) == "true") {
        setCookie("hiddenInvoiceDialog-" + getGlobalToken(), "true", "d1");
    } else {
        setCookie("hiddenInvoiceDialog-" + getGlobalToken(), "false", "d1");
    }
    voucherTip.value = false;
    currentAssigin(saveCurrentRow, saveCurrentColumn);
};
const notTip = ref(false);
const handleTipCancel = () => {
    setCookie("tempHiddenInvoice-" + getGlobalToken(), "false", "s1");
    voucherTip.value = false;
    notTip.value = false;
};
// 不再提示
const handleNoTipsChange = (check: boolean) => {
    check
        ? setCookie("tempHiddenInvoice-" + getGlobalToken(), "true", "d1")
        : setCookie("tempHiddenInvoice-" + getGlobalToken(), "false", "s1");
};
function tableCellClick(row: any, column: any, event: any) {
    if(isSubmit.value) return;
    if (!checkPermission([invoiceCategory.value === "10070" ? "invoice-output-canedit" : "invoice-input-canedit"])) {
        return;
    }
    saveCurrentRow = row;
    saveCurrentColumn = column;
    confirmParams.pid = row.pid;
    confirmParams.vid = row.vid;
    if (!row.vid) {
        //未生成凭证
        currentAssigin(row, column);
    } else {
        //已生成凭证结账
        if (column.label === "业务类型") {
            ElNotify({
                type: "warning",
                message: "该发票已生成凭证，不支持修改业务类型！",
            });
        }
        if (column.label === "项目") {
            if (getCookie("hiddenInvoiceDialog-" + getGlobalToken()) == "true") {
                currentAssigin(row, column);
            } else {
                voucherTip.value = true;
            }
        }
    }
}
function currentAssigin(row: any, column: any) {
    currentClickRowId.value = row.invoiceId;
    currentClickProp.value = column.label;
    if (column.label === "业务类型") {
        currentBusinessId.value = String(row.businessType);
        nextTick(() => {
            businessItemRef.value.focus();
        });
    }
    if (column.label === "项目") {
        currentProjectId.value = row.projectId ? row.projectId : "";
        nextTick(() => {
            projectItemRef.value.focus();
        });
    }
}
const newCDAccountHtmlProject = `
<li style="text-align: center; height: 32px; line-height: 32px;">
    <a class='link' style="text-decoration: none;">
        +点击添加
    </a>
</li>`;
const masterPlateViewGoBackAdd = () => {
    currentSlot.value = "main";
    if (addBusinessFlag.value) {
        addBusinessFlag.value = false;
        currentClickRowId.value = saveCurrentRow.invoiceId;
        currentClickProp.value = saveCurrentColumn.label;
        currentBusinessId.value = filterItemId !== -1 ? String(filterItemId) : currentBusinessId.value;
        showBusinessOptions.value = JSON.parse(JSON.stringify(businessOptionsAll.value));
        let timer = setTimeout(() => {
            businessItemRef.value.focus();
            clearTimeout(timer);
        }, 1000);
    }
};
let filterItemId: number = -1;
const currentBusinessId = ref("");
const businessItemRef = ref();
const addBusinessFlag = ref(false); //从列表点击新增跳转的标志
const isSubmit = ref(false);
const newBusiness = () => {
    addBusinessFlag.value = true;
    currentClickProp.value = "-1";
    goMasterPlateView();
    let timer = setTimeout(() => {
        masterPlateView.value?.showVoucherTemplateNew();
        clearTimeout(timer);
    }, 1000);
};
//业务类型失焦保存提交
async function handleBlurBusiness(row: ITableItem) {
    filterItemId = -1;
    isSubmit.value = true;
    if (currentSlot.value === "main") {
        const params = {
            invoiceId: row.invoiceId,
            businessTypeId: Number(currentBusinessId.value),
            invoiceCategory: row.invoiceCategory,
        }
        request({
            url: `/api/Invoice/BusinessType?` + getUrlSearchParams(params),
            method: "post",
        }).then((res) => {
            loading.value = false;
                isSubmit.value = false;
                if (res.state === 1000) {
                    ElNotify({
                        message: `保存成功`,
                        type: "success",
                    });
                    currentClickProp.value = "-1";
                    let index = tableData.value.findIndex((v: ITableItem) => v.invoiceId === saveCurrentRow.invoiceId);
                    let list = [];
                    if (invoiceCategory.value === "10070") {
                        list = businessSalesOptions.value.filter((v: any) => v.value === currentBusinessId.value);
                    } else {
                        list = businessPurchaseOptions.value.filter((v: any) => v.value === currentBusinessId.value);
                    }
                    let Text = list.length > 0 ? list[0].label : "";
                    tableData.value[index].businessTypeText = Text;
                    tableData.value[index].businessType = Number(currentBusinessId.value);
                } else {
                    ElNotify({
                        message: res.msg || "保存失败了，请稍候重试或联系系统管理员",
                        type: "warning",
                    });
                }
            })
            .catch((err) => {
                currentClickProp.value = "-1";
                loading.value = false;
                isSubmit.value = false;
            });
    }
}
const mouseenterBusiness = ()=>{
    //获取气泡框元素
    const tooltip = document.querySelector('.table .el-popper') as HTMLElement;
    // 隐藏气泡框
    if(tooltip) {
        tooltip.style.display = 'none';
    }
}
function businessListAlter() {
    loading.value = false;
    currentClickProp.value = "-1";
    let Text = "";
    let index = tableData.value.findIndex((v: ITableItem) => v.invoiceId === saveCurrentRow.invoiceId);
    if (invoiceCategory.value === "10070") {
        Text = businessSalesOptions.value.filter((v: IBusinessTypeItem) => v.value === currentBusinessId.value)[0].label;
    }
    if (invoiceCategory.value === "10080") {
        Text = businessPurchaseOptions.value.filter((v: IBusinessTypeItem) => v.value === currentBusinessId.value)[0].label;
    }
    tableData.value[index].businessTypeText = Text;
    tableData.value[index].businessType = Number(currentBusinessId.value);
}
//项目
const erpAssistingProjectList = ref<any[]>([]);
const fetchProjectList = (showAll = true) => {
    return request({
        url: "/api/AssistingAccounting/ProjectList?showAll=" + showAll,
        method: "get",
    });
};
const handleProjectSave = (code: number) => {
    fetchProjectList().then((res) => {
        if (res.state === 1000) {
            erpAssistingProjectList.value = res.data.filter((v: any) => v.aaeid > 0);
            currentClickRowId.value = saveCurrentRow.invoiceId;
            currentClickProp.value = saveCurrentColumn.label;
            currentProjectId.value = code || currentProjectId.value;
            nextTick(() => {
                projectItemRef.value.focus();
            });
        }
    });
};
const getProjectData = () => {
    fetchProjectList().then((res) => {
        if (res.state === 1000) {
            erpAssistingProjectList.value = res.data.filter((v: any) => v.aaeid > 0);
        }
    });
};
const addProjectShow = ref(false);
const dialogAddProjectRef = ref();
const newProject = () => {
    addProjectShow.value = true;
    dialogAddProjectRef.value?.changeName(autoAddName.value);
    getNextAaNum(10005).then((res: any) => {
        if (res.state === 1000) {
            const code = res.data;
            dialogAddProjectRef.value?.changeCode(code);
        }
    });
};
//项目失焦保存提交
const handleBlur = (row: ITableItem) => {
    if (!addProjectShow.value) {
        handleSubmit(saveCurrentRow);
    }
};
const handleSubmit = (row: ITableItem) => {
    let params = {
        invoiceId: row.invoiceId,
        projectId: Number(currentProjectId.value),
    };
    loading.value = true;
    request({
        url: `/api/Invoice/Project?` + getUrlSearchParams(params),
        method: "put",
    })
        .then((res) => {
            loading.value = false;
            if (res.state === 1000) {
                ElNotify({
                    message: `保存成功`,
                    type: "success",
                });
                currentClickProp.value = "-1";
                if (row.vid) {
                    editVoucherShow.value = true;
                }
                let index = tableData.value.findIndex((v: ITableItem) => v.invoiceId === saveCurrentRow.invoiceId);
                let list = erpAssistingProjectList.value.filter((v: any) => v.aaeid === currentProjectId.value);
                let Text = list.length > 0 ? list[0].aaname : "";
                tableData.value[index].projectName = Text;
                tableData.value[index].projectId = Number(currentProjectId.value);
            } else {
                ElNotify({
                    message: res.msg || "保存失败了，请稍候重试或联系系统管理员",
                    type: "warning",
                });
            }
        })
        .catch((err) => {
            currentClickProp.value = "-1";
            loading.value = false;
        });
};
const checkVoucherRef = ref<InstanceType<typeof CheckVoucher>>();
const toVoucherAbout = () => {
    currentSlot.value = "checkVoucher";
};
const confirmParams = reactive({
    pid: "",
    vid: "",
});
const resetConfirmVoiceParams = () => {
    confirmParams.pid = "";
    confirmParams.vid = "";
    editVoucherShow.value = false;
};
const handleConfirmSuccess = () => {
    if (!checkPermission([invoiceCategory.value === "10070" ? "invoice-output-canedit" : "invoice-input-canedit"]) && !isErp.value) {
        ElNotify({ type: "warning", message: "修改已生成凭证的发票数据，需要删除凭证或拥有凭证编辑权限哦~" });
    } else {
        if (currentAddInvoiceSuccessType.value !== "look") {
            checkVoucherRef.value?.checkVoucher(Number(confirmParams.pid), Number(confirmParams.vid));
        } else {
            checkVoucherRef.value?.checkVoucher(Number(lookEditRow.pid), Number(lookEditRow.vid));
        }
    }
    resetConfirmVoiceParams();
};
const handleComfirmCancal = () => {
    resetConfirmVoiceParams();
};
//批量操作后，刷新列表取消勾选
const BatchUpdateProjectDialog = () => {
    loadTableData();
    invoiceTableRef.value?.getTable()?.clearSelection();
};

function openScm(billId: number, billType: number) {
    let openUrl = `/from_acc?asid=${
        scmRelation.value?.scmAsid
    }&id=${billId}&bill_type=${billType}&asname=${scmRelation.value?.scmAsName.replace(/'/g, "\\'")}`+(cstId.value ? `&serviceId=${cstId.value}` : '');
    openScmTab(openUrl, scmRelation.value?.scmProductType || 1020);
}

function openErpScm(billItem: GroupedBill) {
    let billIdList =  billItem.bills.map(item => item.billId);
    let billId = billIdList.join();
    let billType = billItem.billType;

    let url = `${urlRoute[billType]}?asid=${
        scmRelation.value?.scmAsid
    }&id=${billId}&bill_type=${billType}`+(cstId.value ? `&serviceId=${cstId.value}` : '');
    erpCreateTab(url, urlRouteName[billType]);
}
function getBillContent(billItem: GroupedBill) {
    const billNo = billItem.bills[0].billNo;  
    const baseContent = `${billItem.billName}：${billNo}`;   
    if (billItem.bills.length > 1) {
        return `${baseContent}...`; 
    }  
    return baseContent;  
}
function handleBillData(verifiedBills: VerifiedBill[]) {
    const groupedBills:GroupedBill[] = verifiedBills.reduce((acc:GroupedBill[], curr:VerifiedBill) => {   
        let group = acc.find(item => item.billType === curr.billType);   
        if (!group) {  
            group = {  
                billType: curr.billType,  
                billName: curr.billName,  
                bills: []  
            };  
            acc.push(group);  
        }   
        group.bills.push({  
            billId: curr.billId,  
            billNo: curr.billNo  
        });  
        
        return acc;  
    }, []); 
    return groupedBills;
}
function handleBillShow(verifiedBills: VerifiedBill[]) {
    const groupedBills = handleBillData(verifiedBills);
    return groupedBills.length <= 1;
}

let hasMounted = false;
onMounted(async () => {
    tryGetParamsFromRoute();
    getBusinessTypeOptions();
    updateWordcut();
    getInvoiceTaskList();
    getProjectData();
    await handleGetScmRelationInfo();
    handleInvoiceCategoryChange(invoiceCategory.value);
    hasMounted = true;
    window.addEventListener("InvoiceVoucherChange", loadTableData);

    if (router.currentRoute.value.query.toFetchInvoice == "1") {
        showFetchInvoice();
    }

    if (!isErp.value) {
        isProjectAccountingEnabled.value = true;
    }
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop + "" !== "undefined") {
        setTimeout(() => {
            delete (currentRouterModel as any).stop;
        });
    }
});
onUnmounted(() => {
    window.removeEventListener("InvoiceVoucherChange", loadTableData);
});
watch(
    ()  => isProjectAccountingEnabled.value,
    () => {
        columnQuery();
    }
)
watch(
    () => [props.isFetchInvoiceSales, props.isFetchInvoicePurchase],
    ([isSales, isPurchase]) => {
        if (isSales || isPurchase) {
            showFetchInvoice(); // 调用一键取票按钮
        } else {
            fetchInvoiceShow.value = false;
        }
    }
);
watch(
    ()=>fetchInvoiceShow.value,
    (val)=>{
        if(!val) {  //一键取票弹窗关闭
            routerReplace();
        }
    }
);
function routerReplace() {
    const currentQuery = { ...route.query };
    delete currentQuery.fetch;
    router.replace({ query: currentQuery });
}
// 已选弹框
const diffPageSelectedRef = ref<InstanceType<typeof DiffPageSelected>>();
const changeSelectedTableDialog = () => {
    diffPageSelectedRef.value?.handleOpen();
};

function tableRowClickSelected(row: any) {
    invoiceTableRef.value?.getTable()?.toggleRowSelection(row, false);
}
function selectedTableSelectionChange(checked:ITableItem[]) {
    const row = checkedTableData.value.find(element => !checked.includes(element));
    invoiceTableRef.value?.getTable()?.toggleRowSelection(row, false);
}
const viewSelectedTableAelectAll = () => {
    invoiceTableRef.value?.getTable()?.clearSelection();
};

// 凭证生成设置
const showJournalSettings = () => {
    if(isGenVoucher) return 
    voucherSettingDialogShow.value = true;
};

const loadSuccess = ref(true);
const options = computed(() => {
    const invoiceCategoryValue = invoiceCategory.value;
    return [
        {
            id: 0,
            name: "合并成一张凭证",
        },
        {
            id: 1,
            name: "按开票日期分开合并",
        },
        {
            id: 2,
            name: invoiceCategoryValue === "10070" ? "按客户分开合并" : "按供应商分开合并",
        },
        {
            id: 4,
            name: "按发票种类分开合并",
        },
        {
            id: 8,
            name: "按业务类型分开合并",
        },
    ];
});
const subjectOption = ref<Array<Option>>([
    {
        id: 16,
        name: "相同借方科目合并",
    },
    {
        id: 32,
        name: "相同贷方科目合并",
    },
]);
const selectedList = ref<number[]>([]);
const subjectSelectedList = ref<number[]>([]);
const backSelectedList = ref<number[]>([]);
const selectSubjectRef = ref();
const selectZero = ref(true);

function changeSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    if (selectZero.value && val.length > 1 && val[0] === 0) {
        val.splice(0, 1);
        selectZero.value = false;
    } else if (val.findIndex((z) => z === 0) !== -1) {
        selectZero.value = true;
        val.splice(0);
        val.push(0);
    }

    backSelectedList.value = _.cloneDeep(selectedList.value);
    selectedList.value = val;
    genVoucherQueryParameters.value.mergeDate = val.includes(1) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeOpposite = val.includes(2) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeInvoiceType = val.includes(4) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeBusinessType = val.includes(8) && genVoucherQueryParameters.value.isMerge;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(1, selectedList);
}

function mergeVoucher() {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if (genVoucherQueryParameters.value.isMerge) {
        if (!generateVoucherView.value?.checkMergeVoucher()) {
            genVoucherQueryParameters.value.isMerge = false;
            return;
        }
    }

    if (genVoucherQueryParameters.value.isMerge) {
        genVoucherQueryParameters.value.mergeOthers = true;
        if (selectedList.value.length === 0 && subjectSelectedList.value.length === 0) {
            selectedList.value.push(0);
            subjectSelectedList.value.push(16);
            subjectSelectedList.value.push(32);
            selectSubjectRef.value?.changeSelectAll(true);
            genVoucherQueryParameters.value.mergeCredit = true;
            genVoucherQueryParameters.value.mergeDebit = true;
        }

        loadSuccess.value = false;
        if (genVoucherChanged.value) {
            baseVoucherChanged.value = true;
            genVoucherChanged.value = false;
        }
        genVoucherCheckboxChanged(1, selectedList);
    } else {
        loadSuccess.value = false;
        genVoucherCheckboxChanged(0, selectedList);
    }
}

function checkBoxShowChange(val: boolean) {
    if (!val && selectedList.value.length === 0 && genVoucherQueryParameters.value.isMerge) {
        ElNotify({ message: "凭证合并默认需要勾选一个条件哦~", type: "warning" });
        genVoucherQueryParameters.value.isMerge = true;
        selectedList.value.push(0);
        selectZero.value = true;
        genVoucherCheckboxChanged(1, selectedList);
    }
}

function changeSubjectSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    backSelectedList.value = _.cloneDeep(subjectSelectedList.value);
    subjectSelectedList.value = val;
    genVoucherQueryParameters.value.mergeDebit = val.includes(16) && genVoucherQueryParameters.value.mergeOthers;
    genVoucherQueryParameters.value.mergeCredit = val.includes(32) && genVoucherQueryParameters.value.mergeOthers;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(2, subjectSelectedList);
}

function genVoucherCheckboxChanged(flag: number, selectedist?: any) {
    new Promise<boolean>((resolve) => {
        if (genVoucherChanged.value || (baseVoucherChanged.value && (flag === 3 || flag === 4))) {
            ElConfirm("系统可能不会保存您做的更改，确定要切换吗？").then((r) => {
                if (r) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(true);
        }
    }).then((r) => {
        if (r) {
            if (flag > 0 && selectedist !== undefined) {
                generateVoucherView.value?.loadDocumentList(undefined, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                genVoucherChanged.value = false;
                window.localStorage.setItem("genVoucher-invoice", JSON.stringify(genVoucherQueryParameters.value));
            } else {
                if (flag === 3 || flag === 4 || flag === 5) {
                    if (flag === 3 || flag === 4) {
                        if(genVoucherQueryParameters.value.autoMatch || genVoucherQueryParameters.value.autoMatchStock) {
                            genVoucherQueryParameters.value.autoAIGenerate = false;
                        }
                    } else if (flag === 5)  {
                        if(genVoucherQueryParameters.value.autoAIGenerate) {
                            genVoucherQueryParameters.value.autoMatch = false;
                            genVoucherQueryParameters.value.autoMatchStock = false;
                        }
                    }
                    window.localStorage.setItem("genVoucher-invoice", JSON.stringify(genVoucherQueryParameters.value));
                    loadGenVoucher();
                } else {
                    generateVoucherView.value?.loadDocumentList(undefined, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                    genVoucherChanged.value = false;
                    window.localStorage.setItem("genVoucher-invoice", JSON.stringify(genVoucherQueryParameters.value));
                }
            }
        } else if (selectedist != undefined) {
            selectedist.value = _.cloneDeep(backSelectedList.value);
            loadSuccess.value = true;
        } else if (flag === 3) {
            genVoucherQueryParameters.value.autoMatch = !genVoucherQueryParameters.value.autoMatch;
            if(genVoucherQueryParameters.value.autoMatch || genVoucherQueryParameters.value.autoMatchStock) {
                genVoucherQueryParameters.value.autoAIGenerate = false;
            }
            loadSuccess.value = true;
        } else if (flag === 4) {
            genVoucherQueryParameters.value.autoMatchStock = !genVoucherQueryParameters.value.autoMatchStock;
            if(genVoucherQueryParameters.value.autoMatch || genVoucherQueryParameters.value.autoMatchStock) {
                genVoucherQueryParameters.value.autoAIGenerate = false;
            }
            loadSuccess.value = true;
        } else if (flag === 5) {
            genVoucherQueryParameters.value.autoAIGenerate = !genVoucherQueryParameters.value.autoAIGenerate;
            if(genVoucherQueryParameters.value.autoAIGenerate) {
                genVoucherQueryParameters.value.autoMatch = false;
                genVoucherQueryParameters.value.autoMatchStock = false;
            }
            loadSuccess.value = true;
        } else {
            genVoucherQueryParameters.value.isMerge = !genVoucherQueryParameters.value.isMerge;
            loadSuccess.value = true;
        }
    });
}

function mergeErrorFunction() {
    genVoucherQueryParameters.value.mergeCredit = false;
    genVoucherQueryParameters.value.mergeDebit = false;
    subjectSelectedList.value = [];
}

const wordCutList = ref<IWordCut[]>([]);
function updateWordcut() {
    tryUpdateWordCut(WordCutCategory.Invoice).then((res: IResponseModel<number>) => {
        if (res.state === 1000) {
            getWordCutList(WordCutCategory.Invoice).then((res: IResponseModel<Array<IWordCut>>) => {
                if (res.state === 1000) {
                    wordCutList.value = res.data;
                    for (const wordCut of wordCutList.value) {
                        wordCut.matchKeys = wordCut.value3.replace(/，/g, ",").split(",");

                        const parties = wordCut.text1.split(",");
                        wordCut.matchParties = [];
                        wordCut.matchPartiesWithType = [];

                        for (const party of parties) {
                            if(party.indexOf("_") !== -1) {
                                wordCut.matchPartiesWithType.push(party);
                            } else {
                                wordCut.matchParties.push(party);
                            }
                        }
                        
                        wordCut.matchKeywords = wordCut.text2.split(",");
                    }
                }
            });
        }
    });
}

let cacheRouterQueryParams: any = null;
let firstInit = true;
onBeforeRouteLeave((to, from, next) => {
    firstInit = false;
    cacheRouterQueryParams = from.query;
    next();
});

const reLoadCurrentPage = () => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if (currentRouterModel.stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        currentRouterModel.stop = true;
    }
};
const isFromOtherPage = (page: string): boolean => {
    if (!cacheRouterQueryParams?.from && route.query.from === page) {
        return true;
    }
    if (
        cacheRouterQueryParams?.from === page &&
        route.query?.from === page &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }

    return false;
};
// 发票资金一览表判断是否已经打开弹窗，防止重复打开
let isShowDialog = false;
onActivated(async () => {
    if (!hasMounted) return;
    if (isFromOtherPage("FetchInvoice")) {
        GetSalesSearchDate();
        return;
    }
    if (isFromOtherPage("SalesInvoice") || isFromOtherPage("PurchaseInvoice")) {
        await GetSalesSearchDate();
        tryClearCustomUrlParams(route);
        return;
    }
    if (isFromOtherPage("voucherPage")) {
        invoiceTableRef.value?.getTable()?.clearSelection();
        loadTableData();
        return;
    }
    if ((isFromOtherPage("voucherList") || (isFromOtherPage("ERecord"))) && !firstInit) {
        if (isEditting.value) {
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
    }
    // 发票资金一览表明细跳转过来
    if (
        route.query.from &&
        (route.query.from as string).startsWith('/Invoice/CashierInvoiceInfo')
    ) {
        if(
            cacheRouterQueryParams.r &&
            cacheRouterQueryParams.r !== route.query.r &&
            isEditting.value &&
            !isShowDialog
        ){
            isShowDialog = true;
            editConfirm("otherEdit", () => {
                isShowDialog = false;
            }, () => {
                reLoadCurrentPage();
                isShowDialog = false;
            });
        }else if(currentSlot.value === "add" && !isEditting.value){
            reLoadCurrentPage();
        }else {
            if ((cacheRouterQueryParams.r && cacheRouterQueryParams.r !== route.query.r) || (!cacheRouterQueryParams.r && route.query.r)) {
                searchInfo.value.number = route.query.invoiceCode as string;
                await GetSalesSearchDate();
                tryClearCustomUrlParams(route);
            }
        }
    }

    if(!route.query.invoiceType) {
        return;
    } else {
        if (route.query.invoiceType === "purchase") {
            invoiceCategory.value = "10080";
        } else {
            invoiceCategory.value = "10070";
        }
        route.query.invoiceCode && (searchInfo.value.number = route.query.invoiceCode as string);
        await GetSalesSearchDate();
        tryClearCustomUrlParams(route);
    }
});

//关联凭证和取消关联
const dialogAboutVoucherRef = ref<InstanceType<typeof DialogAboutVoucher>>();
const cancelAboutVourchDialog = ref(false);
const associateVoucher = (type: number) => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: `请先选择发票数据！`,
        });
        return ;
    }
    if (type === 1) {
        comfirmAssociateVoucher();
    } else {
        cancelAssociateVoucher();
    }
}
function comfirmAssociateVoucher() {
    let invoiceLineList: Array<InvoiceLineSn> = [];
    let existMinDate = false;
    for (let i = 0; i < checkedTableData.value.length; i++) {
        const v: any = checkedTableData.value[i];
        invoiceLineList.push({
            invoiceId: v.invoiceId,
            invoiceDateText: v.invoiceDateText
        });
        if (v.invoiceDateText < minCheckOutDate) {
            existMinDate = true;
        }
    }
    let hasRecommend = true; //是否调用可推荐接口
    if (existMinDate && generateVoucherSettingDialogRef.value?.getVoucherSetting().voucherDate === 0) {
        hasRecommend = false;
    }
    let VoucherSettings = generateVoucherSettingDialogRef.value?.getVoucherSetting();
    invoiceLineList.sort((a, b) => dayjs(a.invoiceDateText).valueOf() - dayjs(b.invoiceDateText).valueOf());
    dialogAboutVoucherRef.value?.handleOpenDialog(invoiceLineList, invoiceCategory.value, hasRecommend, VoucherSettings);
}
const aboutVourcher = (successCount: number) => {
    if (successCount) {
        BatchUpdateProjectDialog();
        window.dispatchEvent(new CustomEvent("reloadVoucherList"));
    }
}
//取消关联凭证
function cancelAssociateVoucher() {
    let hasDataNum: number = 0; //包含未生成凭证或未关联凭证的发票数据
    for (let i = 0; i < checkedTableData.value.length; i++) {
        const v: any = checkedTableData.value[i];
        if (!v.vid) {
            hasDataNum += 1;
        }
    }
    if (hasDataNum) {
        ElNotify({ 
            type: "warning", 
            message: '未生成/未关联凭证的发票不可取消关联凭证！'
        });
        return;
    }
    cancelAboutVourchDialog.value = true;
}
let timerCancelVourch:any;
const cancelAboutVourchFn = () => {
    if (timerCancelVourch) {
        clearTimeout(timerCancelVourch);
        return;
    }
    cancelAboutVourchReq();
    timerCancelVourch = setTimeout(() => {
        timerCancelVourch = 0;
    }, 200); 
};
const cancelAboutVourchResult = reactive({
    showDialog: false,
    successCount: 0,
    skipCount: 0,
});
const cancelAboutVourchReq = () => {
    let invoiceIdsAbout: number[] = [];
    for (let i = 0; i < checkedTableData.value.length; i++) {
        const v: any = checkedTableData.value[i];
        invoiceIdsAbout.push(v.invoiceId);
    }
    request({ 
        url: "/api/InvoiceVoucher/BatchUnBindVoucher",
        method: "post",
        data: {
            invoiceIds: invoiceIdsAbout,
            isNeedSaveToVoucher: false,
        }
    }).then((res: IResponseModel<{successCount: number, skipCount: number}>) => {
        if (res.state !== 1000) return;
        if (res.data.successCount) {
            BatchUpdateProjectDialog();
            window.dispatchEvent(new CustomEvent("reloadVoucherList"));
        }
        // if(isRelation.value) {
            cancelAboutVourchResult.showDialog = true;
            cancelAboutVourchResult.successCount = res.data.successCount;
            cancelAboutVourchResult.skipCount = res.data.skipCount;
            return
        // }
        // let msg = `成功：${res.data.successCount}，跳过：${res.data.skipCount} （已结账的发票数据已跳过）`;
        // ElNotify({
        //     type: "success",
        //     message: msg,
        // });
    }).finally(()=>{
        cancelAboutVourchDialog.value = false;
    });
}

//列设置弹窗
const columnSetShow = ref(false);
const columnSetRef = ref<InstanceType<typeof ColumnSet>>();
const allColumns = ref<IColItem[]>([]);
function columnQuery() {
    invoiceColumnsSet.value = Columns(
        true,
        invoiceCategory.value,
        scmRelation.value?.isRelation || false,
        isProjectAccountingEnabled.value
    );
    if (!showAll.value && allColumns.value.length) {
        invoiceColumns.value = getShowColumn(allColumns.value, invoiceColumnsSet.value);
    } else {
        invoiceColumns.value = Columns(
            showAll.value,
            invoiceCategory.value,
            scmRelation.value?.isRelation || false,
            isProjectAccountingEnabled.value
        );
    }
    watchDrop(); //列拖拽
}
function getColumnSetList() {
    getColumnListApi(module.value).then((res) => {
        allColumns.value = res.data;
        columnQuery();
    }).catch(() => {
        allColumns.value = [];
    })
}
getColumnSetList();
watch(
    () => showAll.value,
    (v) => {
        columnQuery();
        invoiceTableRef.value?.getTable()?.clearSelection();
    },
    { immediate: true }
);
function openColSet() {
    columnSetShow.value = true;
    columnSetRef.value?.initData();
    nextTick(() => {
        columnSetRef.value?.rowDrop();
    })
}
function saveColumnSet(data: IColItem[]) {
    if (!showAll.value) {
        invoiceColumns.value = getShowColumn(data, invoiceColumnsSet.value);
    }
    watchDrop();
    getColumnSetList();
}
//头部列拖拽设置
const setModule = computed(() => (invoiceCategory.value === "10070" ? "SalesInvoice" : "PurchaseInvoice"));
function cellDrop(oldIndex: number, newIndex: number) {
    let index1 = allColumns.value.findIndex((v) =>v.columnName === invoiceColumns.value[oldIndex].label);
    let index2 = allColumns.value.findIndex((v) =>v.columnName === invoiceColumns.value[newIndex].label);
    allColumns.value = alterArrayPos(allColumns.value, index1, index2);
    invoiceColumns.value = alterArrayPos(invoiceColumns.value, oldIndex, newIndex);
    columnSetRef.value?.saveData(module.value, allColumns.value);
}
function watchDrop() {
    nextTick(() => {
        invoiceTableRef.value?.columnDrop(invoiceTableRef.value?.$el, invoiceColumns.value, showAll.value);
    })
}
function handleScroll() {
    invoiceTableRef.value?.$el.click();
}
//表头字段模糊搜索
function isKeyOfIFSearchItem(key: string): key is keyof IFSearchItem {  
    return [
        'name', 
        'note',
        'number',
        'invoiceTypes', 
        'businessTypes',
        'invoiceStatus'
    ].includes(key);  
}
function filterSearch(prop: string, data: any) {
    if (isKeyOfIFSearchItem(prop)) {  
        if (typeof data === "string" || typeof data === "number") {
            if (prop === "invoiceStatus") {
                searchInfo.value[prop] = data.toString();
            }
            (filterSearchInfo[prop] as string) = data.toString(); 
            loadTableData();  
        } else {
            if (data.length > 0) {
                const filteredData: number[] = data.filter((item: any): item is number => typeof item === 'number');  
                (filterSearchInfo[prop] as number[]) = filteredData;
                loadTableData();
            }
        }
    } 
}
//排序
const sortList:{  
    [key: string]: number;  
} = {
    default: 0,
    invoiceType: 1,
    entryDate: 2,
    invoiceDate: 3,
    code: 4,
    number: 5,
    status: 6,
    name: 7,
    businessType: 8,
}
let sortNum = ref(0);
let sortOrder = ref(-1); //默认； 0升序； 1降序
let sortField = ref(0); //申请排序的是哪个字段
function getSortCommon(name: string, event: any) {
    if (sortField.value !== 0 && sortField.value !== sortList[name]) {
        sortNum.value = 0;
        let sortItems = document.querySelectorAll(".header-caret");
        for(let i =0; i <sortItems.length; i++) {
            sortItems[i].classList.remove("ascending");
            sortItems[i].classList.remove("descending");
        }
    }
    
    sortNum.value++;
    sortField.value = sortList[name];
    if (sortNum.value === 1) {
        event.target.classList.remove("ascending");
        event.target.classList.add("descending");
        sortOrder.value = 1;
    } else if(sortNum.value === 2) {
        event.target.classList.remove("descending");
        event.target.classList.add("ascending");
        sortOrder.value = 0;
    } else {
        sortNum.value = 0;
        event.target.classList.remove("ascending");
        event.target.classList.remove("descending");
        sortOrder.value = -1;
        sortField.value = 0;
    }
    loadTableData();
}

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//下拉组件拼音首字母搜索
const sourceList = computed(() => {
    let list = [
        { value: "0", label: "全部" },        
        { value: "1", label: "移动端扫描" },      
        { value: "2", label: "手动新增" },      
        { value: "3", label: "手动导入" },     
        { value: "11", label: "快速新增" },     
        { value: "100", label: "一键取票" },     
        { value: "202", label: "查验工具导入" },     
        { value: "301", label: "导账工具导入" },     
        { value: "401", label: "云发票" },     
    ];
    if (isErp.value) {
        list.filter((item) => !["1", "301", "401"].includes(item.value));
    }
    return list;
});
const showStatusList = ref<Array<any>>([]);
const showSourceList = ref<Array<any>>([]);
const showErpAssistingProjectList = ref<any[]>();
const businessOptionsAll = computed(() => {
    return invoiceCategory.value === '10070' ? businessSalesOptions.value : businessPurchaseOptions.value;
});
const showBusinessOptions = ref<IBusinessTypeItem[]>([]);
watchEffect(() => { 
    showStatusList.value = JSON.parse(JSON.stringify(statusListOptions.value));
    showSourceList.value = JSON.parse(JSON.stringify(sourceList.value));
    showErpAssistingProjectList.value = JSON.parse(JSON.stringify(erpAssistingProjectList.value));
    showBusinessOptions.value = JSON.parse(JSON.stringify(businessOptionsAll.value));
});
function statusFilterMethod(value: string) {
    showStatusList.value = commonFilterMethod(value, statusListOptions.value, 'name');
}
function sourceFilterMethod(value: string) {
    showSourceList.value = commonFilterMethod(value, sourceList.value, 'label');
}
function erpAssistingProjectFilterMethod(value: string) {
    showErpAssistingProjectList.value = commonFilterMethod(value, erpAssistingProjectList.value, 'aaname');
    autoAddName.value = showErpAssistingProjectList.value.length === 0 ? value.trim() : ""; 
}
function businessFilterMethod(value: string) {
    showBusinessOptions.value = commonFilterMethod(value, businessOptionsAll.value, 'label');
    autoAddName.value = showBusinessOptions.value.length === 0 ? value.trim() : ""; 
}

</script>

<style lang="less" scoped>
@import url(@/style/SelfAdaption.less);

:deep(.el-select) {
    &.project-item {
        .el-input {
            .el-input__inner {
                border: none;
            }
        }
    }
}
:deep(.el-popper.is-light){
    max-width: 700px !important;
}
.taxRate-int {
    position: relative;
    :deep(.el-input) {
        input::-webkit-outer-spin-button, 
        input::-webkit-inner-spin-button {
            -webkit-appearance: none !important;
        }
        .el-input__inner {
            padding-right: 20px;
        }
        .el-input__wrapper{
            .el-input__suffix{
                margin-right:15px;
            }
        }
    }
    .taxRate-int-unit {
        position: absolute;
        right: 12px;
        top: 6px;
        font-size: 16px;
    }
}
.invoice-search-container {
    :deep(.search-info-container-popover) {
        > .downlist {
            padding-right: 8px;
        }
        .el-scrollbar.search-scroll {
            padding-right: 15px;
        }
    }
}
:deep(.select-checkbox) {
    &.invoicetype-list {
        .check-box {
            border-top: 1px solid var(--border-color);
            box-shadow: var(--el-box-shadow-light);
        }
        .bg-click {
            right: 4px;
        }
    }
    &.placeTop {
        .check-box {
            top: -1px;
            border-top: 1px solid var(--border-color);
            transform: translateY(-100%);
        } 
    }
}
.downlist {
    .line-item {
        .line-item-title {
            width: 110px;
        }

        .line-item-field {
            padding-left: 0;
            box-sizing: border-box;

            :deep(.el-select) {
                width: 100%;
            }

            &.large {
                :deep(.el-select) {
                    width: 100%;
                }
            }
        }
    }

    .buttons {
        text-align: left;
    }
}

.content {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .main-content {
        overflow: hidden;
        .main-top {
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .main-tool-right {
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .el-checkbox {
                    margin-right: 0;
                }
            }

            .main-tool-left {
                display: flex;
                align-items: center;
                justify-content: flex-start;
            }
        }

        .main-content-tabs {
            position: relative;
        }
        .invoice-task-tip {
            font-size: var(--font-size);
            line-height: var(--line-height);
        }

        .main-content-detail {
            display: flex;
            flex-direction: column;
            flex: 1;
            overflow: hidden;
        }

        .main-center {
            flex: 1;
            display: flex;
            width: 100%;
            box-sizing: border-box;

            :deep(.table) {
                display: flex;
                flex-direction: column;
                width: 100%;
                .el-table {
                    flex: 1;

                    .el-table__body-wrapper {
                        flex: 1;
                        display: flex;
                        flex-direction: column;

                        .el-scrollbar {
                            flex: 1;
                            display: flex;
                            flex-direction: column;

                            .el-scrollbar__wrap {
                                flex: 1;
                            }
                        }
                    }
                }
            }

            :deep(tbody .el-table__cell.highlight-blue) {
                .cell {
                    color: var(--link-color);
                }
            }

            :deep(.custom-table tbody tr.highlight-red) {
                td.type-column {
                    .cell {
                        color: #333;
                        width: 100%;
                        height: 100%;
                        padding-left: 0;

                        span {
                            width: calc(100% - 22px);
                            height: calc(100% - 4px);
                            display: inline-block;
                            background: url(@/assets/Invoice/invoice-invalid.svg) no-repeat;
                            line-height: 32px;
                            padding-left: 22px;
                            padding-top: 2px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
                td .cell {
                    color: var(--red);
                }
            }

            .image-cancel {
                width: 36px;
                height: auto;
                position: absolute;
                top: 0;
                left: 0;
            }

            .invoice-table-type {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .vidtext {
                span {
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
            .lightText {
                color: var(--link-color);
            }
        }
    }
}
.editVoucherShow-content {
    .content-main {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
    }
    &.erp {
        .content-main {
            border-bottom: none;
        }
        .buttons {
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            padding: 20px 10px;
            .button {
                margin: 0 10px;
            }
        }
    }
}
.voucher-tip-box {
    .box-main {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        .tip {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &.erp {
        .box-main {
            border: none;
            text-align: left;
            padding-bottom: 0;
            .tip {
                justify-content: flex-start;
            }
        }
        .buttons {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            padding: 20px 10px;
            border-top: 1px solid var(--border-color);
            margin-top: 20px;
            .button {
                margin: 0 10px;
            }
        }
    }
}
@media screen and (max-width: 1480px) {
    .invoice-task-tip {
        display: none;
        opacity: 0;
    }
}
.certification-show-content {
    .certification-form {
        padding: 45px 40px 20px;

        :deep(.el-date-editor.el-input) {
            width: 160px;
        }
    }

    .delete-tip {
        padding: 40px 70px;
        display: block;
        text-align: center;
    }

    .certification-res-tip {
        padding: 40px 70px 20px;
    }
}
.buttons.bt {
    border-top: 1px solid var(--border-color);
}

body[erp] {
    .content {
        .slot-content.invoice-add-view {
            height: 100vh;
            overflow: auto !important;
            .edit-content.invoice-edit-content {
                overflow: visible;
            }
        }
        :deep(.el-scrollbar__wrap) {
            position: relative;
            .el-table__empty-block {
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
            }
        }
    }
}
</style>
<style lang="less">
.selected-table-dialog {
    height: auto;
    .el-dialog__body {
        height: 515px;
    }
    .selected-table-content {
        height: 426px;
        .selected-table {
            height: 100%;
            padding: 20px;
            .el-table {
                height: 100%;
                .el-scrollbar__view {
                    height:100%;
                }
            }
        }
    }
}
</style>