<template>
    <div class="content">
        <div class="title">收支类别日记账</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <span>{{ mainLabel }}：</span>
                    <SelectV2 
                        v-model="searchInfo.mainItem" 
                        :options="showMainSelectList" 
                        :teleported="false" 
                        class="mr-10" 
                        :filterable="true"
                        :remote="true"
                        :filter-method="mainSelFilterMethod"
                        @visible-change="handleVisibleChangeMain"
                        :isSuffixIcon="true"
                    />
                    <span>{{ subLabel }}：</span>
                    <SelectV2 
                        v-model="searchInfo.subItem" 
                        :options="showSubSelectList" 
                        :teleported="false" 
                        class="mr-10" 
                        :filterable="true"
                        :remote="true"
                        :filter-method="subSelFilterMethod"
                        @visible-change="handleVisibleChangeSub"
                        :isSuffixIcon="true" 
                    />
                    <DatePicker 
                        :clearable="true" 
                        v-model:startPid="searchInfo.date_s" 
                        v-model:endPid="searchInfo.date_e" 
                        class="small" 
                    />
                    <span class="ml-10">账户：</span>
                    <CDAccountFilter
                        v-model:accountOptions="accountOptions"
                        :options="optionsList"
                        v-model:selectedList="selectedAccount"
                        :width="'170px'"
                        :maxWidth="138"
                        :showDisabledAccount="showDisabledAccount"
                        :isJump="true"
                    />
                    <span class="ml-10">币别：</span>
                    <CurrencyFilter v-model:fcid="searchInfo.fcid" :width="'100px'" :fcList="fcList"></CurrencyFilter>
                    <a class="button solid-button ml-10" @click="handleSearchClick">查询</a>
                </div>
                <div class="main-tool-right">
                    <el-checkbox class="mr-10" v-model="showAll" label="显示所有信息" @change="handleSearch" />
                    <Dropdown
                        btnTxt="打印列表"
                        class="print"
                        :width="isErp ? 96 : 84"
                        :downlistWidth="isErp ? 96 : 84">
                        <li @click="handlePrint(0,getPrintOrExportParams())">直接打印</li>
                        <li @click="printListSettings">打印设置</li>
                    </Dropdown>
                    <a class="button ml-10" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :loading="loading"
                    :use-normal-scroll="true"
                    :columns="columns"
                    :data="tableData"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.description, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column 
                            label="收入（借方）" 
                            :min-width="165" 
                            align="right" 
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column 
                            label="收入（借方）" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column 
                            label="支出（贷方）" 
                            :min-width="165" 
                            align="right" 
                            header-align="right"
                            prop="expenditure_standard"
                            :width="getColumnWidth(setModule, 'expenditure_standard')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.expenditure_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column 
                            label="支出（贷方）" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.expenditure) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditure')"
                                align="right"
                                header-align="right"
                                prop="expenditure"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.expenditure_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #lineSn>
                        <el-table-column label="日记账序号" min-width="155px" align="left" header-align="left">
                            <template #default="scope">
                                <span v-if="!scope.row.line_sn_name.startsWith('<a')">{{ scope.row.line_sn_name }}</span>
                                <span class="link" @click="toJournalPage(scope.row.line_sn_name,scope.row.created_date)" v-else>
                                    {{ getText(scope.row.line_sn_name) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <JouralPrint
    v-model:printDialogShow="printDialogVisible"
    title="日记账打印"
    :printData="printInfo"
    :dir-disabled="showAll"
    :otherOptions="otherOptions"
    @currentPrint="handlePrint(3,getPrintOrExportParams())" />
</template>

<script lang="ts">
export default {
    name: "CombineJournal",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, onActivated, onUnmounted, watchEffect, nextTick } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { request, type IResponseModel } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { ElNotify } from "@/util/notify";
import { setColumns, GetNumber } from "@/views/Cashier/CDJournal/utils";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { Option } from "@/components/SelectCheckbox/types";
import { getATagParams, getText, formatCombineUrlParams, getFcCode } from "@/views/Cashier/Report/utils";
import { CashAAType } from "../CashOrDepositJournal/types";
import { getGlobalLodash } from "@/util/lodash";
import usePrint from "@/hooks/usePrint";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IIETypeItem } from "@/views/Cashier/type";
import type { ICombineUrlParams, ISCurrcy } from "@/views/Cashier/Report/types";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import JouralPrint from "@/components/PrintDialog/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import CDAccountFilter from "@/views/Cashier/components/CDAccountFilter.vue";
import CurrencyFilter from "@/views/Cashier/components/CurrencyFilter.vue";
import { useCDAccountStore } from "@/store/modules/cdAccountList";
import { useCurrencyStore } from "@/store/modules/currencyList";
import { getSearchInfoCD } from "@/views/Cashier/Report/utils";

const setModule = "CombineJournal";
const isErp = ref(window.isErp)
class SelectList {
    constructor(label: string, value: string) {
        this.label = label;
        this.value = value;
    }
    label = "";
    value = "";
}
const _ = getGlobalLodash();
const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const cdAccountStore = useCDAccountStore();
const currencyStore = useCurrencyStore();

const mainSelectList = ref<Array<SelectList>>([]);
const subSelectList = ref<Array<SelectList>>([]);

const selectedAccount = ref<Array<number>>([]);
const columns = ref<Array<IColumnProps>>([]);
const mainLabel = ref("");
const subLabel = ref("");
const searchInfo = reactive({
    date_s: "",
    date_e: "",
    mainItem: "",
    subItem: "",
    cdAccIds: "",
    mainType: "",
    subType: "",
    fcid: -1,
});

const fcList = ref<ISCurrcy[]>([]);
const optionsList = ref<Array<IBankAccountItem>>([]);
const accountOptions = ref<Array<Option>>([]);
async function handleGetIETypeList(type: "main" | "sub") {
    await request({ url: "/api/IEType/List" }).then((res: IResponseModel<{ rows: Array<IIETypeItem> }>) => {
        if (res.state != 1000) return;
        const list = res.data.rows.filter((item: any) => item.haschild !== 1);
        const selectList = list.map((item: any) => new SelectList(item.value1 + " - " + item.value2, item.subkey));
        type === "main" ? (mainSelectList.value = selectList) : (subSelectList.value = selectList);
    });
}
async function getSelectList(aaType: string, type: "main" | "sub") {
    if (aaType === "IE_TYPE") {
        await handleGetIETypeList(type);
        return;
    }
    const params = { aaType, startDate: searchInfo.date_s, endDate: searchInfo.date_e };
    await request({ url: "/api/Journal/AATypeList?" + getUrlSearchParams(params), method: "post" }).then(
        (res: IResponseModel<Array<{ code: string; text: string }>>) => {
            if (res.state !== 1000) return;
            const list = res.data.filter((i) => i.code).map((i) => ({ value: i.code, label: i.text }));
            type === "main" ? (mainSelectList.value = list) : (subSelectList.value = list);
        }
    );
}
async function handleGetCDAccount() {
    optionsList.value = [...cdAccountStore.cdAccountList];
    fcList.value = [...currencyStore.fcListOptions];
    nextTick(() => {
        if (searchInfo.cdAccIds === "ALL") {
            selectedAccount.value = accountOptions.value.map((i) => i.id);
        } else {
            selectedAccount.value = searchInfo.cdAccIds.split(",").map((i) => Number(i));
        }
    });
}
function handleSetLable(type: "main" | "sub", specificType: number) {
    let typeLabel = "";
    switch (specificType) {
        case CashAAType.IEType:
            typeLabel = "收支类别";
            break;
        case CashAAType.Department:
            typeLabel = "部门";
            break;
        case CashAAType.Project:
            typeLabel = "项目";
            break;
        case CashAAType.Unit:
            typeLabel = "往来单位";
            break;
    }
    type === "main" ? (mainLabel.value = typeLabel) : (subLabel.value = typeLabel);
}
const showDisabledAccount = ref(false);
async function handleInit() {
    const { mian_item, sub_item, date_s, date_e, cd_ids, main_id, sub_id, fcid, showDisabled } = route.query as unknown as ICombineUrlParams;
    if (mian_item) searchInfo.mainType = mian_item;
    if (sub_item) searchInfo.subType = sub_item;
    if (main_id) searchInfo.mainItem = main_id;
    if (sub_id) searchInfo.subItem = decodeURIComponent(sub_id);
    if (date_s) searchInfo.date_s = date_s;
    if (date_e) searchInfo.date_e = date_e;
    if (cd_ids) searchInfo.cdAccIds = cd_ids;
    if (fcid) searchInfo.fcid = Number(fcid as string);
    if (showDisabled) showDisabledAccount.value = showDisabled === "true";
    handleSetLable("main", formatCombineUrlParams(mian_item));
    handleSetLable("sub", formatCombineUrlParams(sub_item));
    await getSelectList(searchInfo.mainType, "main");
    await getSelectList(searchInfo.subType, "sub");
    await handleGetCDAccount();
    await handleSearch();
    hasInit = true;
}

const showAll = ref(false);
const loading = ref(false);
const tableData = ref<any[]>([]);
const currentFcid = ref(-1);
async function handleSearch() {
    if (searchInfo.date_s === "" || searchInfo.date_e === "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    loading.value = true;
    await request({ url: "/api/Journal/PagingListForAA?" + getUrlSearchParams(getParams()) })
        .then((res: any) => {
            columns.value = setColumns(showAll.value, false, true, setModule, searchInfo.fcid > 1);
            if (res.state != 1000) return;
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            currentFcid.value = searchInfo.fcid;
        })
        .finally(() => (loading.value = false));
}
function handleSearchClick() {
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
}
const {
  printDialogVisible,
  printInfo,
  otherOptions,
  updataPritnInfo,
  handlePrint
} = usePrint("joural", "/api/Journal/PrintCombinationTable", {}, true, true ,canExportOrPrint);
const printListSettings = () => {
    printDialogVisible.value = true;
};
onActivated(() => {
    printInfo.value = updataPritnInfo()
});
function handleExport() {
    if (!canExportOrPrint()) return;
    const params = getPrintOrExportParams();
    globalExport("/api/Journal/ExportCombinationTable?" + getUrlSearchParams(params));
}
function toJournalPage(name: string,created_date:string) {
    const path = "/Cashier/IEJournal";
    const params = { ...getATagParams(name) };
    params.from = path;
    params.CREATED_DATE = created_date;
    globalWindowOpenPage("/Cashier/JournalPage?" + getUrlSearchParams(params), params.JOURNAL_TYPE === "INCOME" ? "收款凭据" : "付款凭据");
}
watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    if (!hasInit) return;
    handleSearch();
});

watch(selectedAccount, (val) => {
    searchInfo.cdAccIds = getSearchInfoCD(selectedAccount.value, optionsList.value);
});

watch(showAll, () => {
    //勾选显示所有，默认禁用，横向
    if(showAll.value) printInfo.value.direction = 1
});

function getParams() {
    return {
        aa_type: "COMBINATION",
        date_s: searchInfo.date_s,
        date_e: searchInfo.date_e,
        cdAccIds: searchInfo.cdAccIds,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        first_type: searchInfo.mainType,
        second_type: searchInfo.subType,
        first_value: searchInfo.mainItem,
        second_value: searchInfo.subItem,
        fcid: searchInfo.fcid,
        showDisableAccount: getShowDisabledAccount("cashReport"),
    };
}
function getPrintOrExportParams() {
    return {
        aa_type: "COMBINATION",
        aa_value: searchInfo.mainType,
        date_s: searchInfo.date_s,
        date_e: searchInfo.date_e,
        cd_acc_ids: searchInfo.cdAccIds,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        [searchInfo.mainType]: searchInfo.mainItem,
        [searchInfo.subType]: searchInfo.subItem,
        showAll: ~~showAll.value,
        fcid: searchInfo.fcid,
        showDisableAccount: getShowDisabledAccount("cashReport"),
    };
}
function canExportOrPrint() {
    if (tableData.value.length === 0 || tableData.value.length === 1) {
        ElNotify({ type: "warning", message: "报表数据为空" });
        return false;
    }
    if (!searchInfo.date_s || !searchInfo.date_e) {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    return true;
}

let routeQueryParams: any = null;
let hasInit = false;
onActivated(() => {
    const thisRouteQueryParams = route.query;
    const isEqualRouterParams = _.isEqual(thisRouteQueryParams, routeQueryParams) as boolean;
    if (!isEqualRouterParams) {
        hasInit = false;
        paginationData.currentPage = 1;
        handleInit();
    }
    routeQueryParams = thisRouteQueryParams;
});
onBeforeRouteLeave((to, from, next) => {
    routeQueryParams = from.query;
    next();
});
onUnmounted(() => {
    routeQueryParams = null;
});

const handleVisibleChangeMain = (visible: boolean) => {
    if (visible) {
        showMainSelectList.value = JSON.parse(JSON.stringify(mainSelectList.value));
    }
}
const handleVisibleChangeSub = (visible: boolean) => {
    if (visible) {
        showSubSelectList.value = JSON.parse(JSON.stringify(subSelectList.value));
    }
};

const showMainSelectList = ref<SelectList[]>([]);
const showSubSelectList = ref<SelectList[]>([]);
watchEffect(() => {
    showMainSelectList.value = JSON.parse(JSON.stringify(mainSelectList.value));
    showSubSelectList.value = JSON.parse(JSON.stringify(subSelectList.value));
});
function mainSelFilterMethod(value: string) {
    showMainSelectList.value = commonFilterMethod(value, mainSelectList.value, 'label');
}
function subSelFilterMethod(value: string) {
    showSubSelectList.value = commonFilterMethod(value, subSelectList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";
.main-content {
    .main-top {
        padding: 16px 20px;
        .main-tool-left {
            > span {
                flex-shrink: 0;
            }
            :deep(.el-select-v2) {
                width: 120px;
                height: 32px;
                .el-select-v2__placeholder {
                    text-align: left;
                    width: calc(100% - 42px);
                }
            }
            :deep(.picker) {
                .mr-10.ml-10 {
                    margin-left: 5px;
                    margin-right: 5px;
                }
            }
        }
        .button {
            width: 70px;
        }
    }
    .main-center {
        padding: 20px;
        padding-top: 0;
        :deep(.el-table) {
            .el-scrollbar__bar.is-horizontal {
                display: block !important;
            }
        }
    }
}
.small {
    .detail-el-date-picker(118px, 32px);
}
</style>
