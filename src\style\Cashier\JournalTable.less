@import "../Functions.less";
@import "./CashJournal.less";
:deep(.el-textarea__inner) {
    position: absolute;
    top: -15px;
    z-index: 99;
}
:deep(.span_wrap) {
    flex: 1;
}
.format-edit-item(@width, @fontSize) {
    .detail-el-input(@width, 32px);
    :deep(.el-input) {
        .el-input__wrapper {
            padding: 0 4px;
            input.el-input__inner {
                border: none;
                font-size: @fontSize;
                color: var(--font-color);
            }
        }
    }
}
.handle {
    .link {
        display: inline-block;
    }
    .link.disabled {
        color: gray;
        &:hover {
            text-decoration: underline;
        }
    }
}
.none {
    opacity: 0;
    display: none !important;
}
.edit-item.amount {
    :deep(.el-input) {
        .el-input__wrapper {
            padding: 0 4px;
        }
        .el-input__inner {
            text-align: right;
            &:focus {
                text-align: left;
            }
        }
    }
}
& :deep(.el-table) {
    .el-table__header {
        .cell {
            line-height: 36px;
        }
    }

    // 用于隐藏表格被禁用的选择框
    .el-table__row {
        td:first-child {
            .el-checkbox.is-disabled {
                display: none;
            }
        }
        &.edit-row {
            .el-table__cell {
                .cell.el-tooltip {
                    padding-right: 3px !important;
                    position: relative;
                    overflow: visible;
                    > span {
                        box-sizing: border-box;
                        padding-right: 1px;
                        margin-top: 1px;
                    }
                }
            }
        }
        &.init-row {
            .el-table__cell {
                .cell.el-tooltip {
                    position: relative;
                    padding-right: 3px !important;
                    .edit-item {
                        top: 2px;
                        left: 4px;
                    }
                }
            }
        }
        &.journal-row-selected .el-table__cell {
            background-color: var(--el-table-current-row-bg-color);
        }
    }

    .el-table__body {
        .el-table__cell {
            &.journal-all-choose {
                background-color: var(--table-hover-color);
            }
            &.select-box {
                .cell {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
            .cell {
                height: 36px;
                line-height: 30px;
                & > span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                & > span,
                & > a,
                & > .handle {
                    display: inline-block;
                    width: calc(100% - 1px);
                    height: 32px;
                    line-height: 32px;
                }
            }
            &.is-center {
                .cell {
                    justify-content: center;
                }
            }
        }
    }

    .el-table__footer {
        .el-table__cell {
            background-color: var(--white);
            border-bottom: none;
            border-top: 1px solid var(--border-color);
            padding: 0;
            height: 37px;
            .cell {
                padding: 0 4px;
                font-size: 12px;
            }
        }
    }
}

.edit-item {
    height: calc(100% - 4px);
    width: calc(100% - 7px);
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    .detail-placehoder-color(var(--font-color));
    .format-edit-item(100%, 12px);
    &.note {
        :deep(.el-textarea) {
            .detail-el-textarea-scroll-thumb();
        }
    }
    &.description {
        :deep(.el-textarea) {
            .detail-el-textarea-scroll-thumb();
        }
    }
    &.oppositeParty {
        :deep(.el-input__wrapper) {
            padding-right: 26px;
        }
        :deep(.el-textarea) {
            .detail-el-textarea-scroll-thumb();
        }
        :deep(.el-textarea__inner) {
            position: absolute;
            top: -30px;
            padding-right: 16px;
        }
        .icon {
            position: absolute;
            top: 50%;
            right: 2px;
            bottom: 4px;
            background: url("@/assets/Icons/book.png") no-repeat center;
            cursor: pointer;
            width: 26px;
            height: 26px;
            z-index: 100;
            transform: translateY(-50%);
        }
        :deep(.el-autocomplete) {
            width: 100%;
        }
    }
    & > :deep(.el-select) {
        width: 100%;
        height: 32px;
    }
    & :deep(.el-input) {
        &.is-focus {
            .el-input__wrapper {
                box-shadow: 0 0 0 1px var(--border-color) inset !important;
                &.is-focus {
                    box-shadow: 0 0 0 1px var(--border-color) inset !important;
                }
            }
        }
        .el-input__wrapper {
            &.is-focus {
                box-shadow: 0 0 0 1px var(--border-color) inset;
            }
        }
    }
    &.cd-date {
        .el-input__suffix {
            position: absolute;
            top: 0;
            right: -5px;
        }
    }
}

body[erp] {
    .edit-item {
        .format-edit-item(100%, 14px);
        :deep(.el-select) {
            .el-select__input {
                border: none;
            }
        }
    }
}

.el-select-dropdown__item {
    font-size: var(--table-body-font-size);
    &.hover {
        background-color: var(--main-color) !important;
        color: var(--white);
    }
}
.link {
    opacity: 0.9;
}
:deep(input[type="number"]) {
    .detail-spin-button();
}
:deep(.header-operate) {
    .filter-icon {
        top: -4px;
    }
}
:deep(.opposite-column) {
    position: static !important;
}
:deep(.el-autocomplete) {
    &.oppositePartyInput {
        width: 100%;
        .el-textarea__inner {
            padding-right: 24px;
        }
    }
}
:deep(.el-table) {
    .el-table-fixed-column--left,
    .el-table-fixed-column--right {
        z-index: 101;
    }
    .edit-row {
        .opposite-column.el-table-fixed-column--left {
            z-index: 102;
        }
    }
    &.el-table--border:before {
        z-index: 102;
    }
    .el-table__inner-wrapper::after {
        z-index: 102;
    }
    .el-scrollbar__bar.is-vertical {
        z-index: 102;
    }
}
