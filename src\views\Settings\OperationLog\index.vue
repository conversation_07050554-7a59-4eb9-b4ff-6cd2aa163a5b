<template>
    <div class="content" :class="isErp ? 'erp-content' : 'normal-content'">
        <div class="title">操作日志</div>
        <div class="main-content">
            <div class="main-top main-tool-bar">
                <div class="main-tool-left">
                <DatePicker
                    v-model:startPid="searchInfo.startPid"
                    v-model:endPid="searchInfo.endPid"
                    :disabled-date-start="disabledDateStart"
                    :disabled-date-end="disabledDateEnd"
                    v-model:periodInfo="periodInfo"
                />
                <span class="ml-10 mr-10">选择用户：</span>
                <div class="choose-user">
                    <Select v-model="searchInfo.userName" :teleported="false">
                        <Option v-for="item in userNameList" :key="item" :label="item" :value="item" />
                    </Select>
                </div>
                <div class="search-input">
                    <el-input class="ml-10" placeholder="请输入日志的关键字搜索" v-model="searchIext" clearable/>
                </div>
                <a class="button solid-button ml-10" @click="btnSearCh">查询</a>
                </div>
                <div class="main-tool-right">
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :columns="columns"
                    :data="tableData"
                    :loading="loading"
                    :page-is-show="true"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :scrollbar-show="true"
                    :total="paginationData.total"
                    :current-page="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #userId>
                        <el-table-column 
                            show-overflow-tooltip 
                            label="用户ID" 
                            min-width="130px" 
                            align="left" 
                            header-align="left"
                            prop="userId"
                            :width="getColumnWidth(setModule, 'userId')"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.userId }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #userName>
                        <el-table-column 
                            show-overflow-tooltip 
                            label="用户姓名" 
                            min-width="160px" 
                            align="left" 
                            header-align="left"
                            prop="userName"
                            :width="getColumnWidth(setModule, 'userName')"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.userName }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #logType>
                        <el-table-column 
                            show-overflow-tooltip 
                            label="日志类型" 
                            min-width="80px" 
                            align="left" 
                            header-align="left"
                            prop="logType"
                            :width="getColumnWidth(setModule, 'logType')"
                        >
                            <template #default="scope">
                                <span>{{ formatLog(scope.row.logType) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #module>
                        <el-table-column 
                            show-overflow-tooltip 
                            label="模块" 
                            min-width="200px" 
                            align="left" 
                            header-align="left"
                            prop="module"
                            :width="getColumnWidth(setModule, 'module')"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.mainmenuName + "/" + scope.row.menuName }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #log>
                        <el-table-column 
                            show-overflow-tooltip 
                            label="日志" 
                            min-width="280px" 
                            align="left" 
                            header-align="left"
                            prop="log"
                            :width="getColumnWidth(setModule, 'log')"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.log }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #createdDate>
                        <el-table-column show-overflow-tooltip label="操作时间" min-width="130px" align="left" header-align="left" :resizable="false">
                            <template #default="scope">
                                <span>{{ formatTime(scope.row.createdDate) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "OperationLog",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import { dayjs } from "element-plus";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import { getAccountList } from "@/api/getAccountList";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "OperationLog";
interface ITableItem {
    asId: number;
    sn: number;
    userSn: number;
    userId: string;
    userName: string;
    logType: number;
    mainmenu: number;
    menu: number;
    div: number;
    page: string;
    log: string;
    website: number;
    language: number;
    createdDate: string;
    mainmenuName: string;
    menuName: string;
    module?: string;
}

const isErp = ref(window.isErp);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const columns: IColumnProps[] = [
    { slot: "userId" },
    { slot: "userName" },
    { slot: "logType" },
    { slot: "module" },
    { slot: "log" },
    { slot: "createdDate" },
];

const searchInfo = reactive({
    userName: "",
    startPid: "",
    endPid: "",
    keyword: "",
});
const searchIext = ref("");

const periodInfo = ref("");
const tableData = ref<ITableItem[]>([]);
const loading = ref(false);
const userNameList = ref<string[]>([]);

const getUserNameList = () => request({ url: "/api/OperationLog/GetUsers", method: "post" });
let canSearch = true;
const handleSearch = () => {
    if (!canSearch) return;
    const params = {
        userName: searchInfo.userName,
        startTime: searchInfo.startPid,
        endTime: searchInfo.endPid,
        keyword: searchInfo.keyword,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    if (params.keyword.length > 64) {
        ElNotify({ type: "warning", message: "关键字长度不能超过64" });
        return;
    }
    const url = "/api/OperationLog/PagingList?" + getUrlSearchParams(params);
    loading.value = true;
    canSearch = false;
    request({ url })
        .then((res: any) => {
            loading.value = false;
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
        })
        .finally(() => {
            canSearch = true;
        });
};
const handleInit = () => {
    Promise.all([getAccountList(), getUserNameList()]).then((res: any) => {
        if (res[0].state == 1000) searchInfo.userName = res[0].data.userName;
        res[1].state == 1000 ? (userNameList.value = res[1].data) : ElNotify({ type: "warning", message: res.message });
        handleSearch();
    });
};

handleInit();

const formatLog = (value: number): string => {
    switch (value) {
        case 11:
            return "登录";
        case 12:
            return "访问";
        case 21:
            return "创建";
        case 22:
            return "读取";
        case 23:
            return "更新";
        case 24:
            return "删除";
        case 31:
        case 50:
        case 110:
            return "创建";
    }
    return "";
};
const formatTime = (value: string) => {
    return value.replace("T", " ").substring(0, 19);
};

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});

function disabledDateStart(time: Date) {
    let endDate = dayjs(searchInfo.endPid).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(searchInfo.startPid).valueOf();
    return time.getTime() < startDate;
}

const btnSearCh = () => {
    searchInfo.keyword = searchIext.value;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.main-top {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    height: 30px;
    font-size: var(--font-size);
    .search-input {
        .detail-el-input(200px, 30px);
    }
    .choose-user {
        .detail-el-select(200px, 32px);
    }
}
.content {
    width: 100%;
    &.normal-content {
        :deep(.el-table) {
            .el-scrollbar__view {
                min-height: 383px;
                .el-table__empty-block {
                    height: 383px !important;
                }
            }
        }
    }
    .main-content {
        width: 100%;
    }
}
.main-center {
    padding: 20px;
    padding-top: 0;
    :deep(.el-table) {
        .el-scrollbar__view {
            display: block !important;
        }
    }
    :deep(.pagination-num) {
        text-align: left;
    }
}
</style>
<style lang="less">
.table-overflow-popper {
    max-width: 300px;
    text-align: left;
}
</style>
