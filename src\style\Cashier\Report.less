.main-bottom {
    padding: 10px 20px 20px;
    color: var(--font-color);
    font-size: var(--font-size);
    line-height: 14px;
    text-align: left;
}
.main-center {
    margin-top: 1px;
    :deep(.el-table) {
        & > .el-popper {
            max-width: 300px;
            text-align: left;
        }
    }
}
.main-tool-left {
    & > span {
        height: 32px;
        font-size: var(--font-size);
        line-height: 32px;
    }
    :deep(.select-checkbox) {
        .top-box {
            input {
                line-height: var(--line-height) !important;
            }
        }
    }
}
.tabs-pane-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}
#pane-unit {
    .main-center.unit-content {
        max-height: calc(100vh - 296px);
        flex: initial;
    }
}
#pane-project,
#pane-department,
#pane-combine {
    .main-center {
        max-height: calc(100vh - 296px);
        flex: initial;
    }
}
body[erp] {
    #pane-project,
    #pane-department,
    #pane-combine {
        .main-center {
            max-height: calc(100vh - 196px);
            overflow: hidden;
            flex: initial;
            .el-table__body-wrapper {
                max-height: calc(100vh - 302px);
            }
        }
    }
    #pane-combine .multi-combine {
        overflow-y: auto;
        .main-center {
            max-height: unset;
        }
        .el-table__body-wrapper {
            max-height: unset;
        }
    }
}