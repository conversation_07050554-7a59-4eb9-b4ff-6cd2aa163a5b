<template>
    <div class="edit-content" style="width: 1000px; padding: 0px">
        <div class="title">{{ changeTitle }}</div>
        <div style="border: 1px solid var(--slot-title-color); margin-top: 32px">
            <table class="change-tb">
                <tr>
                    <td colspan="4" class="tb-hr">基本信息</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px; text-align: right">资产编号：</td>
                    <td id="ztxg_bh" class="tb-field" style="width: 326px">{{ props.changeData.fa_num }}</td>
                    <td class="tb-title" style="width: 84px">资产类别：</td>
                    <td id="ztxg_lb" class="tb-field" style="width: 416px">
                        <Tooltip :content="props.changeData.fa_type_name" :max-width="326">{{ props.changeData.fa_type_name }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">资产名称：</td>
                    <td id="ztxg_mc" class="tb-field">
                        <Tooltip :content="props.changeData.fa_name" :max-width="326">{{ props.changeData.fa_name }}</Tooltip>
                    </td>
                    <td class="tb-title">资产型号：</td>
                    <td id="ztxg_xh" class="tb-field">
                        <Tooltip :content="props.changeData.fa_model" :max-width="326">{{ props.changeData.fa_model }}</Tooltip>
                    </td>
                </tr>
            </table>
            <div class="line"></div>
            <table class="change-tb" cellspacing="0" cellpadding="0">
                <tr>
                    <td colspan="4" class="tb-hr">变更内容</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px">资产科目：</td>
                    <td class="tb-field">
                        <InputWithDot
                            placement="top-start"
                            :input-value="changeData.fa_asub_name"
                            :inputDisabled="true"
                            :iconVisible="changeData?.faAAEModel?.fa_aae !== ''"
                            @expandAssit="expandAssit('资产科目', changeData.fa_asub_name, changeData.faAAEModel.fa_aae, 0, true)"
                        ></InputWithDot>
                    </td>
                    <td class="tb-title" style="width: 154px">资产科目变更为：</td>
                    <td class="tb-field">
                        <div class="jqtransform">
                            <Select
                                v-model="faasub"
                                :teleported="false"
                                :input-text="faasubName"
                                :tooltipPlacement="'bottom'"
                                style="width: 218px"
                                :filterable="true"
                                :filter-method="faAsubFilterMethod"
                            >
                                <Option
                                    v-for="item in showFaAsubList"
                                    :value="item.value"
                                    :label="item.label"
                                    :key="item.value"
                                    @click="clickAsub('资产科目', item.label, item.type, item.value)"
                                >
                                </Option>
                            </Select>
                        </div>
                    </td>
                </tr>
                <tr v-if="!depreciationBlock">
                    <td class="tb-title">{{ changeData.fa_property === 0 ? "累计折旧科目：" : "累计摊销科目：" }}</td>
                    <td class="tb-field">
                        <InputWithDot
                            placement="top-start"
                            :input-value="changeData.depreciation_asub_name"
                            :iconVisible="changeData.faAAEModel?.depreciation_aae !== ''"
                            :inputDisabled="true"
                            @expandAssit="
                                expandAssit(
                                    changeData.fa_property === 0 ? '累计折旧科目' : '累计摊销科目',
                                    changeData.depreciation_asub_name,
                                    changeData.faAAEModel.depreciation_aae,
                                    0,
                                    true
                                )
                            "
                        ></InputWithDot>
                    </td>
                    <td class="tb-title">{{ changeData.fa_property === 0 ? "累计折旧科目" : "累计摊销科目" }}变更为：</td>
                    <td class="tb-field">
                        <div class="jqtransform">
                            <Select
                                v-model="deprecationAsub"
                                :teleported="false"
                                :input-text="deprecationAsubName"
                                :tooltipPlacement="'bottom'"
                                style="width: 218px"
                                :filterable="true"
                                :filter-method="deAsubFilterMethod"
                            >
                                <Option
                                    v-for="item in showDeprecationAsubList"
                                    :value="item.value"
                                    :label="item.label"
                                    :key="item.value"
                                    @click="
                                        clickAsub(
                                            changeData.fa_property === 0 ? '累计折旧科目' : '累计摊销科目',
                                            item.label,
                                            item.type,
                                            item.value
                                        )
                                    "
                                >
                                </Option>
                            </Select>
                        </div>
                    </td>
                </tr>
                <tr v-if="changeData.fa_property !== 2">
                    <td class="tb-title">资产处置科目：</td>
                    <td class="tb-field">
                        <InputWithDot
                            placement="top-start"
                            :input-value="changeData.disposal_asub_name"
                            :iconVisible="changeData.faAAEModel?.disposal_aae !== ''"
                            :inputDisabled="true"
                            @expandAssit="
                                expandAssit('资产处置科目', changeData.disposal_asub_name, changeData.faAAEModel.disposal_aae, 0, true)
                            "
                        ></InputWithDot>
                    </td>
                    <td class="tb-title">资产处置科目变更为：</td>
                    <td class="tb-field">
                        <div class="jqtransform">
                            <Select
                                v-model="disposalAsub"
                                :teleported="false"
                                :input-text="disposalAsubName"
                                :tooltipPlacement="'bottom'"
                                style="width: 218px"
                                :filterable="true"
                                :filter-method="diAsubFilterMethod"
                            >
                                <Option
                                    v-for="item in showDisposalAsubList"
                                    :value="item.value"
                                    :label="item.label"
                                    :key="item.value"
                                    @click="clickAsub('资产处置科目', item.label, item.type, item.value)"
                                >
                                </Option>
                            </Select>
                        </div>
                    </td>
                </tr>
                <tr class="impairmentProvisionBlock" v-show="impairmentProvisionBlock">
                    <td class="tb-title">减值准备科目：</td>
                    <td class="tb-field">
                        <InputWithDot
                            placement="top-start"
                            :input-value="changeData.impairment_provision_asub_name"
                            :iconVisible="changeData.faAAEModel?.impairment_provision_aae !== ''"
                            :inputDisabled="true"
                            @expandAssit="
                                expandAssit(
                                    '减值准备科目',
                                    changeData.impairment_provision_asub_name,
                                    changeData.faAAEModel.impairment_provision_aae,
                                    0,
                                    true
                                )
                            "
                        ></InputWithDot>
                    </td>
                    <td class="tb-title">减值准备科目变更为：</td>
                    <td class="tb-field">
                        <div class="jqtransform">
                            <Select
                                v-model="impairmentProvisionAsub"
                                :teleported="false"
                                :input-text="impairmentProvisionAsubName"
                                :tooltipPlacement="'bottom'"
                                style="width: 218px"
                                :filterable="true"
                                :filter-method="ipAsubFilterMethod"
                            >
                                <Option
                                    v-for="item in showIProvisionAsubList"
                                    :value="item.value"
                                    :label="item.label"
                                    :key="item.value"
                                    @click="clickAsub('减值准备科目', item.label, item.type, item.value)"
                                >
                                </Option>
                            </Select>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="buttons" style="margin-top: 22px; margin-bottom: 40px; text-align: center">
                <a class="button solid-button" style="margin: 0 5px" @click="savedata">保存</a>
                <a class="button" style="margin: 0 5px" @click="changeCancle">取消</a>
            </div>
        </div>
    </div>
    <AuxiliaryDialog
        v-model="auxiliaryDialogShow"
        :title="auxiliaryTitle"
        :asubName="auxiliaryAsubName"
        :aacode="auxiliaryCode"
        :aaAllowNull="auxiliaryAllowNull"
        :aaReadonly="auxiliaryReadonly"
        :departmentId="department"
        :aaId="auxiliaryId"
        @setAsubWithAAE="setAsubWithAAE"
    ></AuxiliaryDialog>
</template>

<script setup lang="ts">
import Tooltip from "@/components/Tooltip/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import InputWithDot from "./InputWithDot.vue";
import AuxiliaryDialog from "./AuxiliaryDialog.vue";
import type { IFAAsubDto, IAsubSelect } from "../types";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { watch, computed, ref, watchEffect } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { commonFilterMethod } from "@/components/Select/utils";

const _ = getGlobalLodash()

const props = defineProps({
    changeData: {
        type: Object,
        default: () => {
            return {};
        },
    },
    changeTitle: {
        type: String,
        default: " ",
    },
    pid: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(["changeCancle", "saveData"]);
const accountsetStore = useAccountSetStore();

const alterData = computed(() => {
    return _.cloneDeep(props.changeData);
});

const department = computed(() => {
    return props.changeData.faAmortizations?.map((item: any) => item.department_id);
});
const auxiliaryDialogShow = ref(false);
const auxiliaryTitle = ref("");
const auxiliaryAsubName = ref("");
const auxiliaryCode = ref<string[]>([]);
const auxiliaryAllowNull = ref<string[]>([]);
const auxiliaryReadonly = ref(false);
const auxiliaryId = ref();
const clickAsubId = ref();
function expandAssit(title: string, asubName: string, id: string, value: any, isReadonly?: boolean) {
    clickAsubId.value = value;
    auxiliaryDialogShow.value = true;
    auxiliaryTitle.value = title;
    auxiliaryAsubName.value = asubName;
    auxiliaryId.value = id.split(",");
    auxiliaryCode.value = id.split(",").map((item) => useAssistingAccountingStore().getAssistingAccountingModel(Number(item))!.aatype + "");
    let type = "";
    switch (auxiliaryTitle.value) {
        case "资产科目":
            type = faAsubList.value.find((item) => item.value == faasub.value)?.type || "";
            break;
        case "累计折旧科目":
        case "累计摊销科目":
            type = deprecationAsubList.value.find((item) => item.value == deprecationAsub.value)?.type || "";
            break;
        case "资产处置科目":
            type = disposalAsubList.value.find((item) => item.value == disposalAsub.value)?.type || "";
            break;
        case "资产减值准备科目":
            type = impairmentProvisionAsubList.value.find((item) => item.value == impairmentProvisionAsub.value)?.type || "";
            break;
    }
    auxiliaryAllowNull.value = type.includes(",")
        ? type.split(",").map((item) => {
              return item.split("_")[1];
          })
        : [type.split("_")[1]];
    auxiliaryReadonly.value = isReadonly ? true : false;
}

function clickAsub(title: string, name: string, type: string, value: any) {
    if (type) {
        auxiliaryId.value = [];
        clickAsubId.value = value;
        auxiliaryTitle.value = title;
        auxiliaryAsubName.value = name;
        auxiliaryCode.value = type.includes(",")
            ? type.split(",").map((item) => {
                  return item.split("_")[0];
              })
            : [type.split("_")[0]];
        auxiliaryAllowNull.value = type.includes(",")
            ? type.split(",").map((item) => {
                  return item.split("_")[1];
              })
            : [type.split("_")[1]];
        switch (auxiliaryTitle.value) {
            case "资产科目":
                auxiliaryId.value = alterData.value.faAAEModel.fa_aae.split(",");
                break;
            case "累计折旧科目":
            case "累计摊销科目":
                auxiliaryId.value = alterData.value.faAAEModel.depreciation_aae.split(",");
                break;
            case "资产处置科目":
                auxiliaryId.value = alterData.value.faAAEModel.disposal_aae.split(",");
                break;
            case "减值准备科目":
                auxiliaryId.value = alterData.value.faAAEModel.impairment_provision_aae.split(",");
                break;
        }
        auxiliaryDialogShow.value = true;
        auxiliaryReadonly.value = false;
    }
}

function setAsubWithAAE(params: any) {
    switch (auxiliaryTitle.value) {
        case "资产科目":
            alterData.value.faAAEModel.fa_aae = params.map((item: any) => item.id).join(",");
            faasub.value = clickAsubId.value;
            break;
        case "累计折旧科目":
        case "累计摊销科目":
            alterData.value.faAAEModel.depreciation_aae = params.map((item: any) => item.id).join(",");
            deprecationAsub.value = clickAsubId.value;
            break;
        case "资产处置科目":
            alterData.value.faAAEModel.disposal_aae = params.map((item: any) => item.id).join(",");
            disposalAsub.value = clickAsubId.value;
            break;
        case "减值准备科目":
            alterData.value.faAAEModel.impairment_provision_aae = params.map((item: any) => item.id).join(",");
            impairmentProvisionAsub.value = clickAsubId.value;
            break;
    }
}

const impairmentProvisionBlock = computed(() => {
    return (
        (accountsetStore.accountSet?.accountingStandard == 3 && props.changeData.fa_property == 0) ||
        (accountsetStore.accountSet?.accountingStandard == 2 && [0, 1].includes(props.changeData.fa_property))
    );
});
const depreciationBlock = computed(() => {
    return ([3, 4, 5].includes(accountsetStore.accountSet?.accountingStandard as number) && props.changeData.fa_property===1) || props.changeData.fa_property===2;
});
const faasub = ref();
const deprecationAsub = ref();
const disposalAsub = ref();
const impairmentProvisionAsub = ref();
const faasubName = computed(() => {
    return faAsubList.value.find((item) => item.value == faasub.value)?.label || "";
});
const deprecationAsubName = computed(() => {
    return deprecationAsubList.value?.find((item) => item.value == deprecationAsub.value)?.label || "";
});
const disposalAsubName = computed(() => {
    return disposalAsubList.value?.find((item) => item.value == disposalAsub.value)?.label || "";
});
const impairmentProvisionAsubName = computed(() => {
    return impairmentProvisionAsubList.value.find((item) => item.value == impairmentProvisionAsub.value)?.label || "";
});

//资产科目
const faAsubList = ref<IAsubSelect[]>([]);
function getFixedAssetsAsubList() {
    let asubUrl = "";
    switch (props.changeData.fa_property) {
        case 0:
            asubUrl = `/api/FixedAssets/GetFixedAssetsAsubList`;
            break;
        case 1:
            asubUrl = `/api/FixedAssets/GetAllLeafIntangibleAsubList`;
            break;
        case 2:
            asubUrl = `/api/FixedAssets/GetAllLeafLongTermDeferredExpensesAsubList`;
            break;
        case undefined:
            return;
    }
    request({
        url: asubUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        faAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
            prev.push({
                value: item.asubId,
                label: item.asubName,
                type: item.aatypes,
            });
            return prev;
        }, []);
    });
}

//累计折旧科目
const deprecationAsubList = ref<IAsubSelect[]>([]);
function getDeprecationAsubList() {
    let depreciationUrl = "";
    switch (props.changeData.fa_property) {
        case 0:
            depreciationUrl = `/api/FixedAssets/GetDeprecationAsubList`;
            break;
        case 1:
            depreciationUrl = `/api/FixedAssets/GetAllLeafAccumulatedAmortizationAsub`;
            break;
    }
    request({
        url: depreciationUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        deprecationAsubList.value = res.data?.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
            prev.push({
                value: item.asubId,
                label: item.asubName,
                type: item.aatypes,
            });
            return prev;
        }, []);
    });
}

//资产处置科目
const disposalAsubList = ref<IAsubSelect[]>([]);
function getDisposalAsubList() {
    let disposalUrl = "";
    switch (props.changeData.fa_property) {
        case 0:
            disposalUrl = `/api/FixedAssets/GetDisposalAsubList`;
            break;
        case 1:
            disposalUrl = `/api/FixedAssets/GetAllLeafIntangibleDisposalAsub`;
            break;
    }
    request({
        url: disposalUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        disposalAsubList.value = res.data?.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
            prev.push({
                value: item.asubId,
                label: item.asubName,
                type: item.aatypes,
            });
            return prev;
        }, []);
    });
}

//减值准备科目
const impairmentProvisionAsubList = ref<IAsubSelect[]>([]);
function getImpairmentProvisionAsubList() {
    let impairmentProvisionUrl = "";
    switch (props.changeData.fa_property) {
        case 0:
            impairmentProvisionUrl = `/api/FixedAssets/GetImpairmentProvisionAsubList`;
            break;
        case 1:
            impairmentProvisionUrl = `/api/FixedAssets/GetAllLeafIntangibleImpairmentAsub`;
            break;
    }
    request({
        url: impairmentProvisionUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        if (res.state !== 1000) {
            return;
        }
        if (res.state === 1000) {
            impairmentProvisionAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
                prev.push({
                    value: item.asubId,
                    label: item.asubName,
                    type: item.aatypes,
                });
                return prev;
            }, []);
        }
    });
}

function savedata() {
    const isChange =
        faasub.value == props.changeData.fa_asub &&
        deprecationAsub.value == props.changeData.depreciation_asub &&
        disposalAsub.value == props.changeData.disposal_asub &&
        (!impairmentProvisionBlock.value ||
            (impairmentProvisionBlock.value && impairmentProvisionAsub.value == props.changeData.impairment_provision_asub)) &&
        alterData.value.faAAEModel.fa_aae == props.changeData.faAAEModel.fa_aae &&
        alterData.value.faAAEModel.depreciation_aae == props.changeData.faAAEModel.depreciation_aae &&
        alterData.value.faAAEModel.disposal_aae == props.changeData.faAAEModel.disposal_aae &&
        alterData.value.faAAEModel.impairment_provision_aae == props.changeData.faAAEModel.impairment_provision_aae;
    if (isChange) {
        ElNotify({
            type: "warning",
            message: "亲，您的数据没有变动，不能保存哦",
        });
        return;
    }
    const data = {
        faId: props.changeData.fa_id,
        pId: props.pid,
        vId: 0,
        faAsub: faasub.value,
        depreciationAsub: deprecationAsub.value,
        disposalAsub: disposalAsub.value,
        impairmentProvisionAsub: impairmentProvisionAsub.value,
        asubAAERelation: {
            fa_aae: alterData.value.faAAEModel.fa_aae,
            depreciation_aae: alterData.value.faAAEModel.depreciation_aae,
            disposal_aae: alterData.value.faAAEModel.disposal_aae,
            impairment_provision_aae: alterData.value.faAAEModel.impairment_provision_aae,
        },
    };
    request({
        url: `/api/FAChange/AsubChange`,
        method: "post",
        data,
    }).then((res: any) => {
        emits("saveData", res, "kmtz");
    });
}
function changeCancle() {
    emits("changeCancle");
}

watch(
    () => props.changeData,
    () => {
        getFixedAssetsAsubList();
        getDeprecationAsubList();
        getDisposalAsubList();
        impairmentProvisionBlock.value && getImpairmentProvisionAsubList();
        faasub.value = props.changeData.fa_asub;
        deprecationAsub.value = props.changeData.depreciation_asub;
        disposalAsub.value = props.changeData.disposal_asub;
        impairmentProvisionAsub.value = props.changeData.impairment_provision_asub;
    },
    { immediate: true }
);

const showFaAsubList = ref<Array<IAsubSelect>>([]);
const showDeprecationAsubList = ref<Array<IAsubSelect>>([]);
const showDisposalAsubList = ref<Array<IAsubSelect>>([]);
const showIProvisionAsubList= ref<Array<IAsubSelect>>([]);
watchEffect(() => {
    showFaAsubList.value = JSON.parse(JSON.stringify(faAsubList.value));
    showDeprecationAsubList.value = deprecationAsubList.value && JSON.parse(JSON.stringify(deprecationAsubList.value));
    showDisposalAsubList.value = disposalAsubList.value && JSON.parse(JSON.stringify(disposalAsubList.value));
    showIProvisionAsubList.value = JSON.parse(JSON.stringify(impairmentProvisionAsubList.value));
});
function faAsubFilterMethod(value: string) {
    showFaAsubList.value = commonFilterMethod(value, faAsubList.value, 'label');
}
function deAsubFilterMethod(value: string) {
    showDeprecationAsubList.value = commonFilterMethod(value, deprecationAsubList.value, 'label');
}
function diAsubFilterMethod(value: string) {
    showDisposalAsubList.value = commonFilterMethod(value, disposalAsubList.value, 'label');
}
function ipAsubFilterMethod(value: string) {
    showIProvisionAsubList.value = commonFilterMethod(value, impairmentProvisionAsubList.value, 'label');
}
</script>
<style lang="less" scoped>
:deep(.assist-input) {
    position: relative;
    width: 185px;
}
</style>
