<template>
    <div class="step-box" :class="isErp ? 'erp-step-box' : ''">
        <div class="content-row mt-40 asName">
            <div class="content-left width-five">
                <span class="label-color">当前账套:</span>
            </div>
            <div class="content-right width-five">
                <span class="label-color accountSetName">{{ props.asname || "柠檬云示范账套" }}</span>
            </div>
        </div>
        <div class="content-row mt-20">
            <div class="content-left width-five">
                <span class="label-color">授权老板看账:</span>
            </div>
            <div class="content-right width-five">
                <input type="text" placeholder="请输入老板的手机号" v-model="phone" />
            </div>
        </div>
        <div class="content-row mt-40">
            <a class="button solid-button mr-10" @click="submitBossTel">保存</a>
            <a class="button" @click="BackIndex">取消</a>
        </div>
        <el-dialog class="custom-confirm dialogDrag" v-model="sendMsgShow" title="老板看账" width="440px" center @close="handleSwndMsgClose">
            <div class="addnewuser-permission-main" v-dialogDrag>
                <div class="txt">亲，您输入的手机还没有注册，确认需要新增吗？</div>
                <div class="txt">为保障您的数据安全，新注册用户需要验证手机。</div>
                <div class="input mt-20">
                    <input type="text" id="txtaddMobile" disabled v-model="phone" />
                    <a
                        @click="SendSMS"
                        class="button solid-button large-2 ml-10"
                        style="padding-top: 1px; padding-bottom: 1px; margin-left: 10px"
                    >
                        {{ buttonText }}
                    </a>
                </div>
                <div class="input mt-10">
                    <input id="txtaddConfirmCode" type="text" placeholder="请输入验证码" v-model="confirmCode" />
                </div>
            </div>
            <div class="buttons" :style="isErp?'':'border-top:1px solid var(--border-color)'">
                <a class="button solid-button" @click="confirmHandle">确定</a>
                <a class="button ml-10" @click="() => (sendMsgShow = false)">取消</a>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams } from "@/util/url";

const isErp = ref<boolean>(window.isErp);

const props = defineProps<{ asname: string; tableData: any[] }>();

const emit = defineEmits(["back-index", "isOldBossInner", "isNewBossInner"]);
const phone = ref("");
const confirmCode = ref("");
const buttonText = ref("发送验证码");
const sendMsgShow = ref(false);
let canSendSms = true;
let timer: any;
let senonds = 60;

const handleSwndMsgClose = () => {
    confirmCode.value = "";
};
const CheckBossLogin = (phone: string) => {
    request({ url: "/api/BossPermissions/CheckBossLogin?phone=" + phone, method: "post" }).then((res: IResponseModel<string>) => {
        res.state == 1000
            ? res.data === "Success"
                ? OldBossInner(phone)
                : NewBossInner(phone)
            : ElNotify({ type: "warning", message: "请求失败！" });
    });
};
const OldBossInner = (phoneNum: string) => {
    request({ url: "/api/BossPermissions/InitOldBoss?phone=" + phoneNum, method: "post" }).then((result: IResponseModel<string>) => {
        if (result.state !== 1000) {
            ElNotify({ type: "warning", message: "请求失败！" });
            return;
        }
        if (result.data === "Success") {
            ElConfirm("检测到该用户为老用户，已成功授权老板看账~", true).then(() => {
                phone.value = "";
                emit("isOldBossInner");
            });
        } else {
            ElNotify({ type: "warning", message: "请求失败！" });
        }
    });
};
const NewBossInner = (phone: string) => emit("isNewBossInner", phone);
const submitBossTel = () => {
    if (!checkPhone(phone.value)) {
        ElNotify({ type: "warning", message: "亲，您输入手机不正确！" });
        return;
    }
    if (isBossExist(phone.value)) {
        ElNotify({ type: "warning", message: "亲，此用户已经拥有此账套老板权限" });
        return;
    }
    request({ url: "/api/ConfirmCode/ValidateUserIdOrMobile?mobile=" + phone.value, method: "post" }).then(
        (res: IResponseModel<number>) => {
            if (res.state === 1000) {
                res.data > 0 ? CheckBossLogin(phone.value) : (sendMsgShow.value = true);
            }
        }
    );
};
const BackIndex = () => {
    phone.value = "";
    emit("back-index");
};
const editValue = (val: string) => (phone.value = val);
const SendSMS = () => {
    if (!canSendSms) {
        ElNotify({ type: "warning", message: "发送短信中，请稍后！" });
        return;
    }
    canSendSms = false;
    const params = {
        CurrentSystemType: 1,
        Phone: phone.value,
        stype: 13,
        appasid: getGlobalToken(),
    };
    request({
        url: window.accountSrvHost + `/Default/Services/SendSMSForConfirm.ashx?` + getUrlSearchParams(params),
        method: "get",
    }).then((res: any) => {
        if (res === "Success") {
            nextTick().then(() => (buttonText.value = "重新发送（60）"));
            timer = setInterval(MinusOneSecond, 1000);
        } else {
            ElNotify({ type: "warning", message: "亲，您操作太快了，请稍后再试！" });
            canSendSms = true;
        }
    });
};
const confirmHandle = () => {
    const createUserForPermissionUrl =
        window.accountSrvHost + "/Api/CreateUserForPermission.ashx?CurrentSystemType=1&NewType=MOBILE&UserId=" + phone.value;
    if (confirmCode.value.trim() === "") {
        ElNotify({ type: "warning", message: "请输入验证码" });
        return;
    }
    request({
        url: "/api/ConfirmCode/CheckCodeTime?mobile=" + phone.value + "&confirmCode=" + confirmCode.value,
        method: "post",
    }).then((r: IResponseModel<string>) => {
        if (r.state !== 1000) {
            ElNotify({ type: "warning", message: r.msg ?? "验证码错误" });
            return;
        }
        const res = r.data;
        if (res === "Success") {
            sendMsgShow.value = false;
            request({ url: createUserForPermissionUrl }).then((result: any) => {
                if (result == "Success") {
                    NewBossInner(phone.value);
                } else {
                    ElNotify({ type: "warning", message: "亲，新增用户失败，请重试~" });
                }
            });
        } else {
            ElNotify({ type: "warning", message: "验证码错误" });
        }
    });
};

defineExpose({ editValue });

onBeforeUnmount(() => clearInterval(timer));

//发送验证码
const MinusOneSecond = () => {
    if (canSendSms) return;
    if (senonds > 0) {
        senonds--;
        buttonText.value = "重新发送（" + senonds + "）";
    } else {
        buttonText.value = "重新发送";
        senonds = 60;
        canSendSms = true;
        timer = null;
    }
};
const checkPhone = (phoneNum: string) => {
    const filter = /^(0|86|17951)?1([3-9]|0)[0-9][0-9]{8}$/;
    return filter.test(phoneNum);
};
const isBossExist = (phone: string) => {
    const list = props.tableData as any[];
    for (let i = 0; i < list.length; i++) {
        if (list[i].mobile === phone) {
            return true;
        }
    }
    return false;
};
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.step-box {
    min-height: 500px;
    position: relative;
    border-top: 1px solid #cdcdcd;
    overflow: hidden;
    padding: 0;
    font-size: 14px;
    .content-row {
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 20px;
        .content-left {
            width: 490px;
            padding-right: 10px;
            span {
                float: right;
            }
        }
        .content-right {
            width: 480px;
            span,
            input {
                text-align: left;
                float: left;
            }
        }
        input {
            .detail-original-input(188px, 28px);
        }
    }
}
.addnewuser-permission-main {
    padding: 20px 60px;
    .txt {
        font-size: 13px;
    }
    .input {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        input {
            .detail-original-input(180px, 30px);
        }
    }
}
</style>
