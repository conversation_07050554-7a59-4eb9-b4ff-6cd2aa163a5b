<script lang="ts" setup>
import { getPeriodsApi, type IPeriod } from "@/api/period";
import { componentFinishKey } from "@/symbols";
import { ElNotify } from "@/util/notify";
import { ElSelect, ElOption } from "element-plus";
import { computed, inject, ref, watch } from "vue";
const componentFinish = inject(componentFinishKey);

const props = defineProps({
    startPid: { type: Number, required: true },
    endPid: { type: Number, required: true },
    periodInfo: String,
});

const emit = defineEmits<{
    (e: "update:startPid", id: number): void;
    (e: "update:endPid", id: number): void;
    (e: "update:periodInfo", info: string): void;
}>();

const innerStartPid = computed({
    get() {
        return props.startPid;
    },
    set(value: number) {
        emit("update:startPid", value);
    },
});

const innerEndPid = computed({
    get() {
        return props.endPid;
    },
    set(value: number) {
        emit("update:endPid", value);
    },
});

const periodData = ref(new Array<IPeriod>());
const periodInfoValue = computed(() => {
    if (innerStartPid.value === 0 || innerEndPid.value === 0) {
        return "----年--月";
    }
    if (innerStartPid.value === innerEndPid.value) {
        return getPeriodInfoByPID(innerStartPid.value);
    } else {
        return getPeriodInfoByPID(innerStartPid.value) + "—" + getPeriodInfoByPID(innerEndPid.value);
    }
});

// 监听 periodInfoValue 值的变化，有变动就向父组件更新
watch(periodInfoValue, (newValue) => {
    emit("update:periodInfo", newValue);
});

const loadPeriodData = async (_s?: number, _e?: number) => {
    await getPeriodsApi()
        .then((data: any) => {
            periodData.value = data.data.reverse();
            if (periodData.value.length > 0) {
                if (innerStartPid.value === 0) {
                    innerStartPid.value = periodData.value[0].pid;
                }
                if (innerEndPid.value === 0) {
                    innerEndPid.value = periodData.value[0].pid;
                }
                const this_s_index = periodData.value.findIndex((item) => item.pid === _s);
                const this_e_index = periodData.value.findIndex((item) => item.pid === _e);
                if (this_s_index === -1 && _s !== undefined) {
                    innerStartPid.value = periodData.value[0].pid;
                }
                if (this_e_index === -1 && _e !== undefined) {
                    innerEndPid.value = periodData.value[0].pid;
                }
            }
            if (componentFinish !== undefined) {
                componentFinish();
            }
        })
        .catch((error) => {
            console.log(error);
        });
};
loadPeriodData();

function getPeriodInfo(period: IPeriod) {
    return period.year + "年" + period.sn + "月";
}

function getPeriodInfoByPID(pid: number) {
    for (let i = 0; i < periodData.value.length; i++) {
        const item = periodData.value[i];
        if (item.pid === pid) {
            return getPeriodInfo(item);
        }
    }
    return "";
}

function getPeriodData() {
    return periodData.value;
}

function startPidChange(val: number) {
    if (val > innerEndPid.value) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦。",
            type: "warning",
        });
    }
}

function endPidChange(val: number) {
    if (val < innerStartPid.value) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
    }
}

function checkExchangePid() {
    if (innerStartPid.value > innerEndPid.value) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦, 系统已为您自动调整~",
            type: "warning",
        });
        const temp = innerStartPid.value;
        innerStartPid.value = innerEndPid.value;
        innerEndPid.value = temp;
    }
}

defineExpose({
    getPeriodData,
    loadPeriodData,
    checkExchangePid
});
</script>
<template>
    <div class="period-container">
        <el-select v-model="innerStartPid" style="width: 132px" :teleported="false" :fit-input-width="true"
            @change="startPidChange">
            <div style="max-height: 170px">
                <el-option v-for="item in periodData" :key="item.pid" :label="getPeriodInfo(item)" :value="item.pid" />
            </div>
        </el-select>
        <div class="period-separative">至</div>
        <el-select v-model="innerEndPid" style="width: 132px" :teleported="false" :fit-input-width="true"
            @change="endPidChange">
            <div style="max-height: 170px">
                <el-option v-for="item in periodData" :key="item.pid" :label="getPeriodInfo(item)" :value="item.pid" />
            </div>
        </el-select>
    </div>
</template>

<style lang="less" scoped>
@import (reference) "@/style/Common.less";

.period-container {
    display: flex;
    align-items: center;
    .detail-el-select(132px);

    .period-separative {
        .ml-10;
        .mr-10;
    }
}</style>
