import { ref, type Ref } from "vue";

interface Point {
    x: number;
    y: number;
}

interface BoxSelectionCallback {
    (data: { width: number; height: number; left: number; top: number }): void;
}

const useBoxSelection = (
    eventBox: HTMLElement,
    bigBox: HTMLElement,
    preventClassNameArr: string[] = [],
    callBack?: BoxSelectionCallback,
): { cleanUp: () => void; addEventListener: () => void } => {
    const isMouseDown: Ref<boolean> = ref(false);
    const startPoint: Ref<Point | null> = ref(null);
    const selectionBox: Ref<HTMLDivElement | null> = ref(null);
    const onMouseDown = (e: MouseEvent, preventClassNameArr: string[], bigBox: HTMLElement): void => {
        let flag = false;
        preventClassNameArr.forEach((className) => {
            if (e.target instanceof Element && e.target.className && e.target.className.includes(className)) {
                flag = true;
            }
        });
        if (!flag) return;
        e.preventDefault();
        selectionBox.value = null;
        isMouseDown.value = true;
        const rect = bigBox.getBoundingClientRect();
        const x = e.clientX - rect.left + bigBox.scrollLeft;
        const y = e.clientY - rect.top + bigBox.scrollTop;
        startPoint.value = { x, y };

        selectionBox.value = document.createElement("div");
        const box = selectionBox.value;
        box.style.border = "1px solid #1890ff";
        box.style.backgroundColor = "rgba(24,144,255,0.1)";
        box.style.position = "absolute";
        box.style.left = `${startPoint.value.x}px`;
        box.style.top = `${startPoint.value.y}px`;
        bigBox.appendChild(box);
    };

    const onMouseMove = (e: MouseEvent, bigBox: HTMLElement): void => {
        if (!isMouseDown.value || !startPoint.value) return;
        const rect = bigBox.getBoundingClientRect();
        const x = e.clientX - rect.left + bigBox.scrollLeft;
        const y = e.clientY - rect.top + bigBox.scrollTop;
        // const x = e.clientX;
        // const y = e.clientY;
        const width = x - startPoint.value.x;
        const height = y - startPoint.value.y;
        if (width === 0 && height === 0) return;
        const box = selectionBox.value;
        if (box) {
            box.style.width = `${Math.abs(width)}px`;
            box.style.height = `${Math.abs(height)}px`;
            box.style.left = `${width > 0 ? startPoint.value.x : x}px`;
            box.style.top = `${height > 0 ? startPoint.value.y : y}px`;
        }
    };

    const onMouseUp = (e: MouseEvent, bigBox: HTMLElement, fn?: BoxSelectionCallback): void => {
        isMouseDown.value = false;
        if (selectionBox.value) {
            const { width, height, left, top } = selectionBox.value.style;
            const widthNum = parseInt(width) || 0;
            const heightNum = parseInt(height) || 0;
            if (!heightNum || !widthNum) {
                bigBox.removeChild(selectionBox.value);
                selectionBox.value = null;
                return;
            }
            const leftNum = parseInt(left) || 0;
            const topNum = parseInt(top) || 0;
            bigBox.removeChild(selectionBox.value);
            selectionBox.value = null;
            fn && fn({ width: widthNum, height: heightNum, left: leftNum, top: topNum });
        }
    };
    const onMouseDownHandler = (e: MouseEvent) => {
        onMouseDown(e, preventClassNameArr, bigBox);
    };

    const onMouseMoveHandler = (e: MouseEvent) => {
        onMouseMove(e, bigBox);
    };

    const onMouseUpHandler = (e: MouseEvent) => {
        onMouseUp(e, bigBox, callBack);
    };

    const addEventListener = () => {
        eventBox.addEventListener("mousedown", onMouseDownHandler);
        eventBox.addEventListener("mousemove", onMouseMoveHandler);
        eventBox.addEventListener("mouseup", onMouseUpHandler);
        eventBox.addEventListener("mouseleave", onMouseUpHandler);
    };

    const cleanUp = () => {
        eventBox.removeEventListener("mousedown", onMouseDownHandler);
        eventBox.removeEventListener("mousemove", onMouseMoveHandler);
        eventBox.removeEventListener("mouseup", onMouseUpHandler);
        eventBox.removeEventListener("mouseleave", onMouseUpHandler);
    };

    return { cleanUp, addEventListener };
};

export default useBoxSelection;
