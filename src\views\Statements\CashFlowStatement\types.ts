export interface IChangeDto {
    // 现金流是否修改过
    isChange: boolean;
    // 是否有其他科目核算了现金流
    isBindOther: boolean;
}

// 现金流列表数据
export interface ICashFlowList {
    aaeid: number;
    aaname: string;
    aanum: string;
    aatype: number;
    asid: number;
    createdBy: number;
    createdDate: string;
    preName: string;
    status: number;
    uscc: string;
    value01: string;
}

// 现金流详情
export interface ICashFlowtDetails {
    rows: ICashFlowtDetailsRow[];
    total: number;
}

export interface ICashFlowtDetailsRow {
    V_Date: string;
    V_Name: string;
    Description: string;
    ASub_Name: string;
    Debit: string;
    Credit: string;
    OrderBy: number;
}

// 现金流报表数据
export interface ICashFlowState {
    tableData: ICashFlowList[];
    tableList: ICashFlowList[];
    tableShow: ICashFlowtDetailsRow[];
    total: number;
}

// 现金流量表报表底稿
export interface IAdjustFlowSheet {
    amount: number;
    asid: number;
    coumType: number;
    createdManually: number;
    entryType: number;
    expand: number;
    initalAmount: number;
    lineID: number;
    lineName: string;
    lineNumber: number;
    lineType: number;
    note: string;
    parentID: number;
    pid: number;
    priority: number;
    remark: string;
    statementID: number;
    children?: IAdjustFlowSheet[];
    fold?: number;
}

export interface IState {
    pid: number;
    isTax: boolean;
    calmethod: number;
}

export interface IDraftSearchInfo {
    pid: number;
    isTax: boolean;
    calmethod: string;
}
