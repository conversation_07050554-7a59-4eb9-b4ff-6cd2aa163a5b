import { request } from "@/util/service";
import sha1 from "js-sha1";
import { ElNotify } from "@/util/notify";

let isInitedWxworkConfig = false;
export function initWxworkConfig(callback?: Function) {
    if (isInitedWxworkConfig) {
        callback && callback();
        return;
    }
    request({ url: window.jmHost + "/api/QyWxJsapiTicket" }).then((r: any) => {
        const timestamp = Date.parse(new Date() + "");
        const nonceStr = Math.random().toString(36).substr(3, 10);
        const jsapi_ticket = r.jsapi_ticket;
        const currentUrl = window.location.href.split("#")[0];
        const needSignStr = "jsapi_ticket=" + jsapi_ticket + "&noncestr=" + nonceStr + "&timestamp=" + timestamp + "&url=" + currentUrl;
        const signature = sha1(needSignStr);

        wx.config({
            beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: r.appId, // 必填，企业微信的corpID
            timestamp: timestamp, // 必填，生成签名的时间戳
            nonceStr: nonceStr, // 必填，生成签名的随机串
            signature: signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
            jsApiList: ["checkJsApi", "scanQRCode"], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
        });

        request({ url: window.jmHost + "/api/QyWxAgentJsApiTicket" }).then((r: any) => {
            const timestamp = Date.parse(new Date() + "");
            const nonceStr = Math.random().toString(36).substr(3, 10);
            const jsapi_ticket = r.jsapi_ticket;
            const currentUrl = window.location.href.split("#")[0];
            const needSignStr = "jsapi_ticket=" + jsapi_ticket + "&noncestr=" + nonceStr + "&timestamp=" + timestamp + "&url=" + currentUrl;
            const signature = sha1(needSignStr);
            wx.agentConfig({
                corpid: r.appId, // 必填，企业微信的corpid，必须与当前登录的企业一致
                agentid: r.agentId, // 必填，企业微信的应用id （e.g. 1000247）
                timestamp: timestamp, // 必填，生成签名的时间戳
                nonceStr: nonceStr, // 必填，生成签名的随机串
                signature: signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
                jsApiList: ["selectEnterpriseContact", "selectPrivilegedContact"], //必填，传入需要使用的接口名称
                success: function (res: any) {
                    // 回调
                    console.log(res);
                    isInitedWxworkConfig = true;
                    callback && callback();
                },
                fail: function (res: any) {
                    console.log("fail", res);
                    if (res.errMsg.indexOf("function not exist") > -1) {
                        ElNotify({ type: "warning", message: "版本过低请升级" });
                    }
                },
            });
        });
    });
}
