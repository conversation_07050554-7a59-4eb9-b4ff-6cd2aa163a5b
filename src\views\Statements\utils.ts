import ElementPlus from "element-plus";
import { createApp } from "vue";
import ShareDialog from "@/views/Statements/components/ShareDialog/index.vue";
/** 公式格式化 */
export const noteFormatter = function (note: string, splitCount: number | undefined) {
    let rnote = "<div style='text-align:left;'>";
    if (splitCount === undefined) splitCount = 1;
    (function () {
        let counter = 0;
        for (let i = 0; i < note.length; i++) {
            if (note[i] == "+" || note[i] == "/" || (note[i] == "-" && note.substring(i - 1, i + 1) != "，-")) {
                if (counter === splitCount) {
                    rnote += "</div><div style='text-align:left;'>";
                    counter = 0;
                } else {
                    counter++;
                }
            }
            rnote += note[i];
        }
    })();
    rnote += "</div>";
    // 使用正则表达式匹配金额部分
    const regex = /(-?\d{1,3}(,\d{3})*(\.\d+)?)/g;
    // 使用替换函数将匹配到的数字用 <span> 标签包裹起来
    const newText = rnote.replace(regex, (match) => `<span style="display: inline-block;">${match}</span>`);
    return newText;
};

export const debounce = (fn: Function, delay: number = 3000, immediate: boolean = true) => {
    let timer: any = null;
    const delayTime = delay;
    if (timer) {
        clearTimeout(timer);
    }
    return function () {
        if (timer) {
            return;
        }
        if (immediate) {
            const bool = !timer;
            timer = setTimeout(() => (timer = null), delayTime);
            return bool && fn();
        }
    };
};

export const handleCellClick = (row: any, column: any, cell: HTMLTableCellElement, event: Event) => {
    // 设置当前行为选中行
    const spanElement = cell.querySelector('span.cell-value,span.highlight-red');
    if (spanElement && spanElement.textContent) {
        const numberValue = parseFloat(spanElement.textContent);
        if (numberValue) {
            // 对span元素进行选中操作
            const range = document.createRange();
            range.selectNodeContents(spanElement);
            const sel = window.getSelection();
            if (sel) {
              sel.removeAllRanges();
              sel.addRange(range);
            }
          }
    }
   
  };

type ICacheType =
    | "isShowBeginYearFirst"
    | "isShowBeginYearLast"
    | "isShowBusiness"
    | "isShowBudgetMonth"
    | "isShowBudgetYear"
    | "isShowSurplus"
    | "isShowIncome";

export function getSwitchState(type: ICacheType): boolean {
    const cacheValue = localStorage.getItem(type);
    if (cacheValue === null) return true;
    return JSON.parse(cacheValue);
}

export const share = (shareReportHost:string): Promise<boolean>=>{
    //前缀可以在这处理
    return new Promise<boolean>(() => {
        if(document.querySelector("#sheetShareDialog")) return
        const content = document.querySelector(".router-container .content")
        const contentBox = content;
        const props = {
            shareReportHost,
        };
        const capp = createApp(ShareDialog, props);
        const container = document.createElement("div");
        capp.use(ElementPlus);
        capp.mount(container);
        contentBox!.insertBefore(container, contentBox!.firstChild);
    })
}