<template>
  <div>
    <Popover
      v-if="rowData.formula.split(',')[columnInfo.index] == 1"
      placement="right"
      :use-slot-content="true">
      <template #trigger>
        <div class="equal-icon" />
        <span style="float: right; line-height: 12px">
          {{ formatValue }}
        </span>
      </template>
      <template #content>
        <div v-html="generateFormula(rowData, tabData, columnInfo)"></div>
      </template>
    </Popover>
    <template v-else>
      <span v-if="!Object.keys(columnInfo).includes('index')">
        {{ value }}
      </span>
      <template v-else>
        <checkable-input
          v-if="isInput"
          :box-id="`getObjFromList(${tabData.id}, 'ewblxh', '${rowData.ewblxh}').${rowData.prop}`"
          v-model="value"
          :table-validate-messages="tableValidateMessages"
          @blur="handleInputBlur"></checkable-input>
        <checkable-span
          v-else-if="isSpan"
          :box-id="`getObjFromList(${tabData.id}, 'ewblxh', '${rowData.ewblxh}').${rowData.prop}`"
          :value="value"
          :table-validate-messages="tableValidateMessages"></checkable-span>
        <span v-else>--</span>
      </template>
    </template>
  </div>
</template>
<script setup lang="ts">
  const value = defineModel<string | number>("modelValue")
  const props = withDefaults(
    defineProps<{
      rowData?: { [key: string]: any }
      tabData?: { [key: string]: any }
      columnInfo?: { [key: string]: any }
      customTable?: boolean
      tableValidateMessages?: any[]
    }>(),
    {
      rowData: () => ({}),
      tabData: () => ({}),
      columnInfo: () => ({}),
      customTable: true,
      tableValidateMessages: () => [],
    },
  )

  if (props.tabData.id === "ywbw.sbZzsFbZzsjmssbmxb") {
    console.log(props.rowData, "rowData")
  }
  const formatValue = computed(() => {
    if (value.value === null || value.value === "") {
      return ""
    }
    return String(parseFloat(props.rowData[props.columnInfo.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  })

  const isInput = computed(() => {
    return props.rowData.readonly.split(",")[props.columnInfo.index as number] == 0
  })

  const isSpan = computed(() => {
    return props.rowData.readonly.split(",")[props.columnInfo.index as number] == 1
  })

  const emits = defineEmits<{
    (e: "blur", value: number, rowData: { [key: string]: any }, tabData: { [key: string]: any }, prop: string): void
    (
      e: "formula",
      rowData: { [key: string]: any },
      tabData: { [key: string]: any },
      columnInfo: { [key: string]: any },
      callback: (result: string) => void,
    ): string
  }>()

  const handleInputBlur = (value: number, rowData: { [key: string]: any }, tabData: { [key: string]: any }, prop: string) => {
    emits("blur", value, rowData, tabData, prop)
  }

  // const generateFormula = computed(() => {
  //   return (rowData: { [key: string]: any }, tabData: { [key: string]: any }, columnInfo: { [key: string]: any }) => {
  //     let str = ""
  //     emits("formula", rowData, tabData, columnInfo, (result) => {
  //       str = result
  //     })
  //     return str
  //   }
  // })
  const generateFormula = (rowData: { [key: string]: any }, tabData: { [key: string]: any }, columnInfo: { [key: string]: any }) => {
    let str = ""
    emits("formula", rowData, tabData, columnInfo, (result) => {
      str = result
    })
    return str
  }
</script>
<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
</style>
