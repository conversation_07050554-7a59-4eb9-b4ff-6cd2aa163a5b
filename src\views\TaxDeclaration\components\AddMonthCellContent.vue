<template>
  <div>
    <Popover
      v-if="row.formula.split(',')[slotColumn.columnIndex] == 1"
      placement="right"
      :use-slot-content="true">
      <template #trigger>
        <div class="equal-icon" />
        <span style="float: right; line-height: 12px">
          {{ String(parseFloat(row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
        </span>
      </template>
      <template #content>
        <div v-html="generateFormula(row, tabData, slotColumn)"></div>
      </template>
    </Popover>
    <template v-else>
      <!-- <template v-if="!customTable"> -->
      <checkable-input
        v-if="isInput"
        v-model="value"
        @blur="handleInputBlur($event, row, tabData, slotColumn.prop)"></checkable-input>
      <span v-else-if="isNumber">
        {{ content }}
      </span>
      <span v-else>--</span>
      <!-- </template> -->
      <!-- <template v-else>
        <checkable-input
          v-if="slotColumn.columnIndex && row.readonly.split(',')[slotColumn.columnIndex] == 0"
          v-model="value"
          @blur="handleInputBlur($event, row, tabData, slotColumn.prop)"></checkable-input>
        <span v-else-if="!slotColumn.columnIndex || row.readonly.split(',')[slotColumn.columnIndex] == 1">
          {{
            !slotColumn.columnIndex
              ? row[slotColumn.prop]
              : String(parseFloat(row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
          }}
        </span>
        <span v-else>--</span>
      </template> -->
    </template>
  </div>
</template>
<script setup lang="ts">
  const value = defineModel<string | number>("modelValue")
  const props = withDefaults(
    defineProps<{
      row?: { [key: string]: any }
      tabData?: { [key: string]: any }
      slotColumn?: { [key: string]: any }
      customTable?: boolean
    }>(),
    {
      row: () => ({}),
      tabData: () => ({}),
      slotColumn: () => ({}),
      customTable: true,
    },
  )

  const isInput = computed(() => {
    return props.customTable
      ? props.slotColumn.columnIndex && props.row.readonly.split(",")[props.slotColumn.columnIndex] == 0
      : props.row.readonly.split(",")[props.slotColumn.columnIndex] == 0
  })
  const isNumber = computed(() => {
    return props.customTable
      ? !props.slotColumn.columnIndex || props.row.readonly.split(",")[props.slotColumn.columnIndex] == 1
      : props.row.readonly.split(",")[props.slotColumn.columnIndex] == 1
  })
  const content = computed(() => {
    return props.customTable
      ? !props.slotColumn.columnIndex || props.row[props.slotColumn.prop] === null
        ? props.row[props.slotColumn.prop]
        : String(parseFloat(props.row[props.slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
      : String(parseFloat(props.row[props.slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  })
  const emits = defineEmits<{
    (e: "blur", value: number, row: { [key: string]: any }, tabData: { [key: string]: any }, prop: string): void
    (
      e: "formula",
      row: { [key: string]: any },
      tabData: { [key: string]: any },
      slotColumn: { [key: string]: any },
      callback: (result: string) => void,
    ): string
  }>()

  const handleInputBlur = (value: number, row: { [key: string]: any }, tabData: { [key: string]: any }, prop: string) => {
    emits("blur", value, row, tabData, prop)
  }

  // const generateFormula = computed(() => {
  //   return (row: { [key: string]: any }, tabData: { [key: string]: any }, slotColumn: { [key: string]: any }) => {
  //     let str = ""
  //     emits("formula", row, tabData, slotColumn, (result) => {
  //       str = result
  //     })
  //     return str
  //   }
  // })
  const generateFormula = (row: { [key: string]: any }, tabData: { [key: string]: any }, slotColumn: { [key: string]: any }) => {
    let str = ""
    emits("formula", row, tabData, slotColumn, (result) => {
      str = result
    })
    return str
  }
</script>
<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
</style>
