export interface IAccountItem {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
    standard: string;
    fc_rate: string;
    label?: string;
}

export interface ITableItem {
    j_type: number;
    cd_date: string;
    cd_date_text: string;
    line_sn: number;
    description: string;
    cd_account: number;
    cd_account_name: string;
    line_sn_in: number;
    cd_account_in: number;
    cd_account_in_name: string;
    income: number;
    income_standard: number;
    expenditure: number;
    expenditure_standard: number;
    amount: number;
    amount_standard: number;
    note: string;
    flag: number;
    checked: string;
    p_id: number;
    v_id: number;
    v_num: string;
    v_num2: string;
    created_by: number;
    v_date: string;
    created_date: string;
    created_date_txt: string;
    modified_by: number;
    modified_date: string;
    vtId: number;
    vtName: string;
    fc_rate: number;
    standard_out: number;
    fc_code_out: string;
    standard_in: number;
    fc_code_in: string;
    expenditure_data: string;
    index?: number;
    new?: boolean;
    showDelete?: boolean;
    old_cd_date?: string;
    newDate?: string;
    fc_rate_in?: number;
    fc_rate_out?: number;
}

export interface ICurrency {
    asId: number;
    preName: string;
    id: number;
    code: string;
    name: string;
    rate: number;
    isBaseCurrency: boolean;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}
