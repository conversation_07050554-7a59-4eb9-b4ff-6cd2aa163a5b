<template>
    <el-dialog
        :title="title"
        :draggable="false"
        v-model="visible"
        destroy-on-close
        width="440"
        align-center
        center
        :close-on-click-modal="false"
        :show-close="showClose"
    >
        <div class="dialog-content">
            <div class="dialog-content-body">
                <div class="dialog-content-message" v-html="message"></div>
                <slot> </slot>
            </div>
            <div class="buttons" :class="isErp ? 'erp-buttons' : 'buttons'">
                <a class="button solid-button mr-20" @click="onConfirmClick">{{ confirmButtonText }}</a>
                <a class="button" @click="onCancelClick">{{ cancelButtonText }}</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";

const isErp = ref(window.isErp);

const props = withDefaults(
    defineProps<{
        modelValue: boolean;
        title?: string;
        showClose?: boolean;
        closeOnClickModal?: boolean;
        message?: string;
        confirmButtonText?: string;
        cancelButtonText?: string;
        confirmClick?: () => void;
        cancelClick?: () => void;
    }>(),
    {
        title: "提示",
        showClose: true,
        closeOnClickModal: false,
        message: "",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmClick: () => {},
        cancelClick: () => {},
    }
);

const emit = defineEmits(["update:modelValue"]);

const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
});

const onConfirmClick = () => {
    props.confirmClick();
    visible.value = false;
};

const onCancelClick = () => {
    props.cancelClick();
    visible.value = false;
};
</script>

<style lang="less" scoped>
@import (reference) "@/style/Common.less";
.dialog-content {
    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
    .erp-buttons {
        padding: 16px 20px 16px 0;
        text-align: right;
        height: 30px;
        & a {
            float: right;
            margin: 0 10px;
            min-width: 64px !important;
            font-size: 12px;
        }
    }
}
</style>
