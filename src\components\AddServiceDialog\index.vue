<template>
    <div ref="addServiceDialogMaskRef" v-show="showAddServiceDialog" id="showAddServiceDialogMask">
        <div class="dialog-box">
            <div class="dialog-box-top">
                <!-- <p>云财务专业版专属客服<br />全天候为您服务</p> -->
                <div class="qrcode-box">
                    <!-- 财务 -->
                    <img class="dialog-box-qrcode" src="@/assets/Common/caiwu-pro.png" v-show="!isAccountingAgent" />
                    <!-- 代账 -->
                    <img class="dialog-box-qrcode" src="@/assets/Common/daizhang-pro.png" v-show="isAccountingAgent" />
                    <!-- <p class="tips">微信扫一扫添加</p> -->
                </div>
            </div>
            <div class="close-btn" @click="handleClose"></div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";

const isAccountingAgent = ref(window.isAccountingAgent);

const props = withDefaults(
    defineProps<{
        visible: boolean;
    }>(),
    {
        visible: true,
    }
);

const showAddServiceDialog = ref(props.visible);
const addServiceDialogMaskRef = ref();

const handleClose = () => {
    showAddServiceDialog.value = false;
    // 移除容器
    document.body.removeChild(addServiceDialogMaskRef.value.parentNode);
};
</script>
<style lang="less">
#showAddServiceDialogMask {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 9999;
    .dialog-box {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 360px;
        height: 480px;
        .dialog-box-top {
            position: relative;
            width: 360px;
            height: 428px;
            padding-top: 30px;
            background: url("@/assets/app/service-dialog.jpg") no-repeat 0 0;
            background-size: contain;
            border-radius: 6px;
            box-sizing: border-box;
            p {
                padding: 0;
                font-weight: 500;
                font-size: 24px;
                color: #333333;
                line-height: 35px;
                text-align: center;
                font-style: normal;
                margin: 0;
                &.tips {
                    font-size: 20px;
                }
            }
            .qrcode-box {
                position: absolute;
                top: 95px;
                left: 50%;
                transform: translatex(-50%);
                width: 220px;
                margin: 20px auto;
                // border: 1px solid #e5e5e5;
                .dialog-box-qrcode {
                    display: block;
                    width: 220px;
                    margin: 0 auto;
                }
            }
        }
        .close-btn {
            width: 24px;
            height: 24px;
            margin: 24px auto 0;
            background: url("@/assets/Common/close-icon.png") no-repeat 0 0;
            background-size: contain;
        }
    }
}
</style>
