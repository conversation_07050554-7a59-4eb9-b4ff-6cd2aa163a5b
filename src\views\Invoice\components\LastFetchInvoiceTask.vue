<template>
    <el-dialog 
        v-model="lastFetchInvoiceTaskShow" 
        :title="isRobotError() ? '提示' : '一键取票记录'"
        @close="handleTrialExpired({ msg:ExpiredToBuyDialogEnum.normal, needExpired: false })" 
        center 
        :width="isRobotError() ? 440 : 860" 
        class="dialogDrag"
    >
        <div class="fetch-invoice-task-box" v-dialogDrag v-if="!isRobotError()">
            <div class="fetch-invoice-task-box-body">
                <div class="tips" v-if="invoiceTaskListData[0].invoiceTaskStatus === 2">
                    <img class="status-icon" src="@/assets/Invoice/pass.png" />
                    {{ invoiceCategoryText }}发票数据获取成功，系统会自动覆盖未生成凭证的发票数据，如该发票{{
                        invoiceCategory === "0" ? "数据" : ""
                    }}已生成凭证，则不会更新，请您仔细核对。数电发票的发票原件获取时间较长，正在持续同步获取中，您可通过取票记录查看获取情况。
                </div>
                <div class="tips" v-else>
                    <img class="status-icon" src="@/assets/Invoice/warning.png" />
                    {{ invoiceCategoryText }}发票数据获取失败，失败原因：{{
                        invoiceTaskListData[0].invoiceTaskRemark ? invoiceTaskListData[0].invoiceTaskRemark : invoiceTaskListData[0].remark
                    }}
                </div>
                <Table
                    :data="invoiceTaskListData"
                    :columns="columns"
                    :page-is-show="false"
                    :scrollbarShow="true"
                    :maxHeight="300"
                    :showOverflowTooltip="false"
                    :tableName="setModule"
                >
                    <template #invoiceStatus>
                        <el-table-column 
                            label="发票状态" 
                            min-width="50" 
                            align="left" 
                            header-align="left"
                            prop="invoiceStatus" 
                            :width="getColumnWidth(setModule, 'invoiceStatus')"
                        >
                            <template #default="scope">
                                <div>
                                    <span> {{ invoiceStatusEnum[scope.$index % 3] }}发票 </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceDate>
                        <el-table-column 
                            label="开票日期" 
                            min-width="120" 
                            align="left" 
                            header-align="left" 
                            prop="invoiceDate" 
                            :width="getColumnWidth(setModule, 'invoiceDate')"
                        >
                            <template #default="scope">
                                <div>
                                    <span>
                                        {{ parseDateText(scope.row.startDate) }} 至
                                        {{ parseDateText(scope.row.endDate) }}
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceCount>
                        <el-table-column 
                            label="份数" 
                            min-width="40" 
                            align="left" 
                            header-align="left" 
                            prop="invoiceCount" 
                            :width="getColumnWidth(setModule, 'invoiceCount')"
                        >
                            <template #default="scope">
                                <div>
                                    <span>{{
                                        invoiceCategory === "10070"
                                            ? scope.row.salesCount
                                            : invoiceCategory === "10080"
                                            ? scope.row.purchaseCount
                                            : scope.row.salesCount + scope.row.purchaseCount
                                    }}</span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceAmount>
                        <el-table-column 
                            label="不含税金额（元）" 
                            min-width="80" 
                            align="left" 
                            header-align="left" 
                            prop="invoiceAmount" 
                            :width="getColumnWidth(setModule, 'invoiceAmount')"
                        >
                            <template #default="scope">
                                <div>
                                    <span>{{ formatMoneyForInvoice(scope.row, "Amount") }} </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceTax>
                        <el-table-column 
                            label="发票税额（元）" 
                            min-width="80" 
                            align="left" 
                            header-align="left" 
                            prop="invoiceAmount" 
                            :width="getColumnWidth(setModule, 'invoiceTax')"
                        >
                            <template #default="scope">
                                <div>
                                    <span>{{ formatMoneyForInvoice(scope.row, "Tax") }} </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceTotal>
                        <el-table-column 
                            label="价税合计（元）"
                            min-width="80" 
                            align="left" 
                            header-align="left" 
                            :resizable="false"
                        >
                            <template #default="scope">
                                <div>
                                    <span>{{ formatMoneyForInvoice(scope.row, "Total") }} </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="fetch-invoice-task-box-footer buttons">
                <a @click="toSearch" class="button solid-button">去看看</a>
            </div>
        </div>
        <div  class="fetch-invoice-task-box" v-dialogDrag v-else>
            <div class="fetch-invoice-task-box-body">
                <div class="robot-tips">
                    <div class="robot-tips-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                            <defs data-reactroot=""></defs>
                            <g>
                                <path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z" fill="#f00"></path>
                            </g>
                        </svg>
                    </div>
                    <span>代理业务登录方式取票失败</span>
                </div>
                <div class="robot-code">
                    <img v-if="isPro" src="@/assets/Invoice/fetchInvoice_pro.png" />
                    <img v-else-if="isErp" src="@/assets/Invoice/fetchInvoice_erp.png" />
                    <img v-else src="@/assets/Invoice/fetchInvoice.png" />
                </div>
                <div>请使用微信扫一扫反馈客服处理</div>
            </div>
            <div class="fetch-invoice-task-box-footer buttons">
                <a @click="lastFetchInvoiceTaskShow = false" class="button solid-button">关闭</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import type { IInvoiceTaskDisplay, IInvoiceTaskResult } from "../types";
import Table from "@/components/Table/index.vue";
import dayjs from "dayjs";
import { convertDeductionTaskRemark, convertFileTaskRemark, convertInvoiceTaskRemark } from "../utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { formatMoney } from "@/util/format";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { robotLoginErrorList } from "@/views/Invoice/utils";

const setModule = "LastFetchInvoiceTask";
const columns = [
    { slot: "invoiceStatus" },
    { slot: "invoiceDate" },
    { slot: "invoiceCount" },
    { slot: "invoiceAmount" },
    { slot: "invoiceTax" },
    { slot: "invoiceTotal" },
];

const invoiceStatusEnum = ["正常", "作废", "红冲"];
const invoiceTaskStatusEnum = ["状态异常", "获取中", "获取成功", "获取失败", "获取超时"];

const props = defineProps({
    invoiceCategory: {
        type: String,
        required: true,
    },
    lastFetchInvoiceTaskShow: {
        type: Boolean,
        default: false,
    },
    invoiceTaskList: {
        type: Array<IInvoiceTaskResult>,
        default: () => [],
    },
});

const isErp = ref(window.isErp);
const isPro = ref(window.isProSystem);
function isRobotError() {
    if (
        props.invoiceTaskList && 
        props.invoiceTaskList[0] && 
        props.invoiceTaskList[0].taxLoginType === 101 && 
        (props.invoiceTaskList[0].invoiceTaskStatus !== 2 || 
        props.invoiceTaskList[0].deductionTaskStatus !== 2 ||
        props.invoiceTaskList[0].fileTaskStatus !== 2
        )
    ) {

        if (props.invoiceTaskList[0].invoiceTaskStatus !== 2 && props.invoiceTaskList[0].invoiceTaskRemark) {
            return robotLoginErrorList.some((item) => props.invoiceTaskList[0].invoiceTaskRemark.includes(item));
        } 
        if (props.invoiceTaskList[0].deductionTaskStatus !== 2 && props.invoiceTaskList[0].deductionTaskRemark) {
            return robotLoginErrorList.some((item) => props.invoiceTaskList[0].deductionTaskRemark.includes(item));
        } 
        if (props.invoiceTaskList[0].fileTaskStatus !== 2 && props.invoiceTaskList[0].fileTaskRemark) {
            return robotLoginErrorList.some((item) => props.invoiceTaskList[0].fileTaskRemark.includes(item));
        } 
        return robotLoginErrorList.some((item) => props.invoiceTaskList[0].remark.includes(item));
    }
    return false;
}

let invoiceCategory = computed(() => {
    return props.invoiceCategory;
});
let invoiceCategoryText = computed(() => {
    return invoiceCategory.value === "10070" ? "销项" : invoiceCategory.value === "10080" ? "进项" : "进销项";
});
const emit = defineEmits(["handlebyDate", "update:lastFetchInvoiceTaskShow"]);
const lastFetchInvoiceTaskShow = computed({
    get() {
        return props.lastFetchInvoiceTaskShow;
    },
    set(value) {
        emit("update:lastFetchInvoiceTaskShow", value);
    },
});

const invoiceTaskListData = ref<Array<IInvoiceTaskDisplay>>([]);
const parseDateText = (date: number) => {
    let dateText = date.toString();
    return dateText.substring(0, 4) + "-" + dateText.substring(4, 6) + "-" + dateText.substring(6, 8);
};

const toSearch = () => {
    const salesCount = invoiceTaskListData.value?.reduce((total, item) => total + item.salesCount, 0);
    const purchaseCount = invoiceTaskListData.value?.reduce((total, item) => total + item.purchaseCount, 0);
    emit("handlebyDate", startDate.value, endDate.value, salesCount, purchaseCount);
    lastFetchInvoiceTaskShow.value = false;
};
const formatMoneyForInvoice = (row: any, colprop: string) => {
    return formatMoney(
        invoiceCategory.value === "0"
            ? row["sales" + colprop] + row["purchase" + colprop]
            : row[invoiceCategory.value === "10070" ? "sales" + colprop : "purchase" + colprop], true, true
    );
};
onMounted(() => {});
watch(
    () => props.lastFetchInvoiceTaskShow,
    (newVal: boolean) => {
        if (newVal) {
            loadTableData();
        }
    }
);

const startDate = ref(dayjs().format("YYYY-MM-DD"));
const endDate = ref(dayjs().subtract(1, "month").format("YYYY-MM-DD"));

function loadTableData() {
    const tempList = props.invoiceTaskList;
    let list: Array<IInvoiceTaskDisplay> = [];
    const i = 0;
    startDate.value = parseDateText(tempList[i].startDate);
    endDate.value = parseDateText(tempList[i].endDate);

    list.push({
        asId: tempList[i].asId,
        createdBy: tempList[i].createdBy,
        createdByName: tempList[i].createdByName,
        createdDate: tempList[i].createdDate,
        startDate: tempList[i].startDate,
        endDate: tempList[i].endDate,
        modifiedDate: tempList[i].modifiedDate,
        status: tempList[i].status,
        remark: convertInvoiceTaskRemark(tempList[i].remark),
        invoiceTaskStatus: tempList[i].invoiceTaskStatus,
        invoiceTaskRemark: convertInvoiceTaskRemark(tempList[i].invoiceTaskRemark),
        deductionTaskStatus: tempList[i].deductionTaskStatus,
        deductionTaskRemark: convertDeductionTaskRemark(tempList[i].deductionTaskRemark),
        fileTaskStatus: tempList[i].fileTaskStatus,
        fileTaskRemark: convertFileTaskRemark(tempList[i].fileTaskRemark),
        purchaseTotal: tempList[i].purchaseNormalTotal,
        purchaseTax: tempList[i].purchaseNormalTax,
        purchaseAmount: tempList[i].purchaseNormalAmount,
        purchaseCount: tempList[i].purchaseNormalCount,
        salesTotal: tempList[i].salesNormalTotal,
        salesTax: tempList[i].salesNormalTax,
        salesAmount: tempList[i].salesNormalAmount,
        salesCount: tempList[i].salesNormalCount,
    });

    list.push({
        asId: tempList[i].asId,
        createdBy: tempList[i].createdBy,
        createdByName: tempList[i].createdByName,
        createdDate: tempList[i].createdDate,
        startDate: tempList[i].startDate,
        endDate: tempList[i].endDate,
        modifiedDate: tempList[i].modifiedDate,
        status: tempList[i].status,
        remark: convertInvoiceTaskRemark(tempList[i].remark),
        invoiceTaskStatus: tempList[i].invoiceTaskStatus,
        invoiceTaskRemark: convertInvoiceTaskRemark(tempList[i].invoiceTaskRemark),
        deductionTaskStatus: tempList[i].deductionTaskStatus,
        deductionTaskRemark: convertDeductionTaskRemark(tempList[i].deductionTaskRemark),
        fileTaskStatus: tempList[i].fileTaskStatus,
        fileTaskRemark: convertFileTaskRemark(tempList[i].fileTaskRemark),
        purchaseTotal: tempList[i].purchaseInvalidTotal,
        purchaseTax: tempList[i].purchaseInvalidTax,
        purchaseAmount: tempList[i].purchaseInvalidAmount,
        purchaseCount: tempList[i].purchaseInvalidCount,
        salesTotal: tempList[i].salesInvalidTotal,
        salesTax: tempList[i].salesInvalidTax,
        salesAmount: tempList[i].salesInvalidAmount,
        salesCount: tempList[i].salesInvalidCount,
    });

    list.push({
        asId: tempList[i].asId,
        createdBy: tempList[i].createdBy,
        createdByName: tempList[i].createdByName,
        createdDate: tempList[i].createdDate,
        startDate: tempList[i].startDate,
        endDate: tempList[i].endDate,
        modifiedDate: tempList[i].modifiedDate,
        status: tempList[i].status,
        remark: convertInvoiceTaskRemark(tempList[i].remark),
        invoiceTaskStatus: tempList[i].invoiceTaskStatus,
        invoiceTaskRemark: convertInvoiceTaskRemark(tempList[i].invoiceTaskRemark),
        deductionTaskStatus: tempList[i].deductionTaskStatus,
        deductionTaskRemark: convertDeductionTaskRemark(tempList[i].deductionTaskRemark),
        fileTaskStatus: tempList[i].fileTaskStatus,
        fileTaskRemark: convertFileTaskRemark(tempList[i].fileTaskRemark),
        purchaseTotal: tempList[i].purchaseRedTotal,
        purchaseTax: tempList[i].purchaseRedTax,
        purchaseAmount: tempList[i].purchaseRedAmount,
        purchaseCount: tempList[i].purchaseRedCount,
        salesTotal: tempList[i].salesRedTotal,
        salesTax: tempList[i].salesRedTax,
        salesAmount: tempList[i].salesRedAmount,
        salesCount: tempList[i].salesRedCount,
    });

    invoiceTaskListData.value = list;
}
</script>

<style lang="less" scoped>
.fetch-invoice-task-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;

    .fetch-invoice-task-box-body {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: stretch;
        // border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        text-align: center;
        min-height: 200px;
        padding: 20px;

        .tips {
            margin-bottom: 20px;
            text-align: left;

            .status-icon {
                margin-right: 2px;
                width: 16px;
                vertical-align: middle;
            }
        }
    }

    .fetch-invoice-task-box-footer {
        padding: 20px 0;
        text-align: center;
    }

    .robot-tips {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        .robot-tips-icon {
            margin-right: 5px;
            width: 18px;
            height: 18px;
        }
    }
    .robot-code {
        img {
            margin: 10px auto 5px;
            display: block;
            width: 200px;
            height: 200px;
        }
    }
}
</style>
