<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <div class="jqtransform float-l date-picker">
                <el-date-picker
                    v-model="startMonth"
                    type="month"
                    :disabled-date="disabledDateMonth"
                    :clearable="false"
                    :editable="false"
                    :teleported="false"
                    :value-format="'YYYYMM'"
                    :format="'YYYY年MM月'"
                    style="width: 130px"
                    @change="changeMonth"
                />
            </div>            
            <a
                class="link float-l ml-10"
                v-show="!isErp && !isHideBarcode"
                @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/commonPro?subMenuId=*********&answerId=630')"
                ><span class="help-icon mr-5"></span>查看差异说明</a
            >
        </div>
        <div class="main-tool-right">
            <a v-permission="['scmvoucher-canexport']" class="float-r button solid-button" @click="exportData">导出</a>
            <RefreshButton class="float-l"></RefreshButton>
        
        </div>
    </div>
    <div class="main-center">
        <Table 
            :data="tableData" 
            :columns="columns" 
            :scrollbarShow="true" 
            :loading="loading"
            :tableName="setModule"
        >
            <template #accountSystem>
                <el-table-column label="财务系统" align="left" header-align="center">
                    <el-table-column 
                        label="日期" 
                        prop="vdate" 
                        align="left" 
                        header-align="left"
                        min-width="51"
                        :width="getColumnWidth(setModule, 'vdate')"
                    > </el-table-column>
                    <el-table-column 
                        label="凭证号" 
                        min-width="51" 
                        align="left" 
                        header-align="left"
                        prop="voucherInfo"
                        :width="getColumnWidth(setModule, 'voucherInfo')"
                    >
                        <template #default="scope">
                            <template
                                v-if="
                                    !!scope.row.voucherInfo && !useAccountSetStoreHook().permissions.includes('scmvoucher-cancreatevoucher')
                                "
                            >
                                {{ scope.row.voucherInfo }}
                            </template>
                            <template v-else>
                                <a
                                    class="link"
                                    @click="
                                        globalWindowOpenPage(
                                            `/Voucher/VoucherPage?pid=${scope.row.pid}&vid=${scope.row.vid}&fcode=scmvoucher-cancreatevoucher`,
                                            '查看凭证'
                                        )
                                    "
                                    >{{ scope.row.voucherInfo }}</a
                                >
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column 
                        label="摘要" 
                        prop="description" 
                        align="left" 
                        header-align="left" 
                        min-width="125"
                        :width="getColumnWidth(setModule, 'description')"
                    > </el-table-column>
                    <el-table-column 
                        label="借方" 
                        prop="debitText" 
                        align="right" 
                        header-align="right" 
                        min-width="75"
                        :width="getColumnWidth(setModule, 'debitText')"
                    > </el-table-column>
                    <el-table-column 
                        label="贷方" 
                        prop="creditText" 
                        align="right" 
                        header-align="right" 
                        min-width="75"
                        :width="getColumnWidth(setModule, 'creditText')"
                    > </el-table-column>
                    <el-table-column 
                        label="余额" 
                        prop="accTotalText" 
                        align="right" 
                        header-align="right" 
                        min-width="75"
                        :width="getColumnWidth(setModule, 'accTotalText')"
                    > </el-table-column>
                </el-table-column>
            </template>
            <template #scmSystem>
                <el-table-column label="进销存系统" align="left" header-align="center">
                    <el-table-column 
                        label="进销存单据号" 
                        min-width="125" 
                        align="left" 
                        header-align="left"
                        prop="scmBillNo"
                        :width="getColumnWidth(setModule, 'scmBillNo')"
                    >
                        <template #default="scope">
                            <template v-if="scope.row.billType !== 0 && scope.row.billId !== 0">
                                <a
                                    class="link"
                                    @click="
                                        globalWindowOpen(
                                            `${getScmHost(props.scmProductType)}/#/from_acc?asid=${props.scmAsid}&bill_type=${
                                                scope.row.billType
                                            }&id=${scope.row.billId}&asname=${encodeURIComponent(props.scmAsName.replace(/'/g, '\\\''))}${props.cstId ? `&serviceId=${props.cstId}` : ''}`
                                        )
                                    "
                                    >{{ scope.row.billNo }}</a
                                >
                            </template>
                            <template v-else>
                                {{ scope.row.billNo }}
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column 
                        label="凭证号" 
                        min-width="51" 
                        align="left" 
                        header-align="left"
                        prop="billVoucherInfo"
                        :width="getColumnWidth(setModule, 'billVoucherInfo')"
                    >
                        <template #default="scope">
                            <template
                                v-if="
                                    !!scope.row.billVoucherInfo &&
                                    !useAccountSetStoreHook().permissions.includes('scmvoucher-cancreatevoucher')
                                "
                            >
                                {{ scope.row.billVoucherInfo }}
                            </template>
                            <template v-else>
                                <a
                                    class="link"
                                    @click="
                                        globalWindowOpenPage(
                                            `/Voucher/VoucherPage?pid=${scope.row.billPId}&vid=${scope.row.billVId}&fccode=scmvoucher-cancreatevoucher`,
                                            '查看凭证'
                                        )
                                    "
                                    >{{ scope.row.billVoucherInfo }}</a
                                >
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column 
                        label="入库" 
                        prop="warehouseText" 
                        align="right" 
                        header-align="rigth" 
                        min-width="75"
                        :width="getColumnWidth(setModule, 'warehouseText')"
                    > </el-table-column>
                    <el-table-column 
                        label="出库" 
                        prop="deliveryText" a
                        lign="right" 
                        header-align="rigth" 
                        min-width="75"
                        :width="getColumnWidth(setModule, 'deliveryText')"
                    > </el-table-column>
                    <el-table-column 
                        label="余额" 
                        prop="scmTotalText" 
                        align="right" 
                        header-align="right" 
                        min-width="75"
                        :width="getColumnWidth(setModule, 'scmTotalText')"
                    > </el-table-column>
                </el-table-column>
            </template>
            <template #differenceValue>
                <el-table-column 
                    label="差额" 
                    prop="totalText" 
                    align="right" 
                    header-align="right" 
                    min-width="80"
                    :width="getColumnWidth(setModule, 'totalText')"
                > </el-table-column>
            </template>
            <template #differenceInfo>
                <el-table-column header-align="center" min-width="130" :resizable="false">
                    <template #header>
                        <span>差异原因</span>
                        <img src="@/assets/Scm/question.png" style="width: 14px; margin-left: 3px; cursor: pointer" @click="openHelp" v-show="!isHideBarcode" />
                    </template>
                    <template #default="scope">
                        <div
                            style="
                                text-align: left;
                                height: 37px;
                                white-space: nowrap;
                                line-height: 37px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            "
                        >
                            {{ scope.row.differenceInfoText }}
                        </div>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { request } from "@/util/service";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { globalExport, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { getScmHost } from "@/util/scm";
import { ref, onMounted, watch, nextTick } from "vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { dayjs } from "element-plus";

const setModule = "ScmVoucherDiff";
const props = defineProps({
    scmAsid: {
        type: Number,
        default: 0,
    },
    scmProductType: {
        type: Number,
        default: 0,
    },
    scmAsName: {
        type: String,
        default: "",
    },
    cstId: {
        type: Number,
        default: 0,
    },
});

const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
// 加载状态
const loading = ref<boolean>(false);
const selPeriodRef = ref();
const lostFocus = () => {
    setTimeout(() => {
        selPeriodRef.value.blur();
    }, 0);
};

(window as any).globalWindowOpenPage = globalWindowOpenPage;
const period = ref("");
const startMonth = ref("");
const columns = ref<Array<IColumnProps>>([
    {
        slot: "accountSystem",
    },
    {
        slot: "scmSystem",
    },
    { slot: "differenceValue" },
    // { label: "差额", prop: "totalText", align: "right", headerAlign: "right", width: 80 },
    { slot: "differenceInfo" },
    // {
    //     label: '差异原因<img src="@/assets/Icons/help.png" style="width:14px; margin-left: 3px; cursor: pointer;" onclick="openHelp()" /> ',
    //     prop: "differenceInfoText",
    //     align: "left",
    //     headerAlign: "center",
    //     width: 130,
    //     formatter: function (value, row, index) {
    //         return "<div title='" + value + "' style='text-overflow:ellipsis; overflow: hidden;'>" + value + "</div>";
    //     },
    // },
]);

interface ISelect {
    value: number;
    label: string;
    time: string;
}
const periodList = ref<ISelect[]>([]);
function initPeriods() {
    return request({
        url: `/api/Period/ListWithStatus`,
    });
}
const tableData = ref([]);
function GetCheckScmDetailData() {
    loading.value = true;
    request({
        url: `/api/CheckScm/List?pid=${period.value}&scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
    })
        .then((res: any) => {
            if (res.state === 1000) {
                tableData.value = res.data;
            }
        })
        .finally(() => {
            loading.value = false;
        });
}

function openHelp() {
    globalWindowOpen("https://help.ningmengyun.com/#/jz/commonPro?subMenuId=*********&answerId=725");
}

// const getDateInfo = () => {
//     request({
//         url: "/MasterPages/Services/GetAccountList.ashx?r=" + Math.random() + "&appasid" + getGlobalToken(),
//         method: "get",
//     }).then((res: any) => {
//         period.value = res.CurrentYear + "年" + res.CurrentMonth + "月";
//     });
// };
// getDateInfo();

function exportData() {
    globalExport(`/api/CheckScm/Export?pid=${period.value}&scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`);
}

onMounted(() => {
    Promise.all([initPeriods()]).then((res: any) => {
        periodList.value = res[0].data
            .reduce((prev: ISelect[], item: any) => {
                prev.push({
                    value: item.pid,
                    label: item.year + "年" + item.sn + "月",
                    time: item.year + "" + String(item.sn).padStart(2, "0"),
                });
                return prev;
            }, [])
            .reverse();
        period.value = res[0].data.find((item: any) => item.isActive).pid;
        startMonth.value = periodList.value.find((item) => item.value === Number(period.value))?.time || "";
    });
});
function disabledDateMonth(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
function changeMonth() {
    period.value = periodList.value.find((item: any) => item.time === startMonth.value)?.value + "" || "";
}

watch(period, () => {
    GetCheckScmDetailData();
});
</script>

<style scoped lang="less">
.main-top {
    & .help-icon {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url(@/assets/Icons/help.png) no-repeat;
        vertical-align: top;
        margin-top: 2px;
    }
}
.date-picker {
    :deep(.el-date-editor) {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
        & .el-input__suffix-inner {
            position: absolute;
            right: 30px;
            top: 9px;
        }
    }
    :deep(.el-month-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
    :deep(.el-year-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
}
</style>
