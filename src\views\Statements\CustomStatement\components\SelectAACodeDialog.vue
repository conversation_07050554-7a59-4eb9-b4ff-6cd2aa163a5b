<template>
    <el-dialog
        v-model="selectAACodeDialog"
        title="选择辅助核算明细"
        destroy-on-close
        width="642px"
        top="200px"
        class="select-aacode-dialog custom-confirm dialogDrag"
    >
        <div class="select-aacode-dialog-container" v-dialogDrag>
            <div class="select-aacode-dialog-table">
                <Table
                    class="custom-table erp-table"
                    :columns="columns"
                    :show-overflow-tooltip="true"
                    :flexible="true"
                    :fit="false"
                    :data="AACodeTableList"
                >
                    <template #aaNum>
                        <el-table-column label="编码" :min-width="140" :show-overflow-tooltip="false" :resizable="false">
                            <template #default="scope">
                                <FilterCustomSelect
                                    :suffix-icon="CaretBottom"
                                    v-model="scope.row.aaeid"
                                    placeholder=" "
                                    :filterable="true"
                                    class="aacode-select"
                                    :popper-append-to-body="false"
                                    :fit-input-width="true"
                                    @focus="handleAAFocus(scope.$index)"
                                    :filter-method="handleAAFilter"
                                    @change="handleAAEidChange(scope.row.aaeid, scope.$index)"
                                >
                                    <ElOption
                                        v-for="item in scope.row.list || []"
                                        :key="item.aaeid"
                                        :label="item.aanum"
                                        :optionValue="item.aanum + '-' + item.aaname"
                                        :value="String(item.aaeid)"
                                    >
                                        <span>{{ item.aanum + "-" + item.aaname }}</span>
                                    </ElOption>
                                </FilterCustomSelect>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
        <div class="buttons erp-buttons">
            <a class="button solid-button mr-10" @click="selectAACodeConfirm">确定</a>
            <a class="button" @click="selectAACodeClear">清空</a>
            <a class="button" @click="selectAACodeDialog = false">取消</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import Table from "@/components/Table/index.vue";
import { CaretBottom } from "@element-plus/icons-vue";
import ElOption from "@/components/Option/index.vue";
import FilterCustomSelect from "@/views/Statements/components/EditEquation/FilterCustomSelect.vue";
import { getGlobalLodash } from "@/util/lodash";
import type { IAACodeTableItem, IAssistEntriesItem } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { pinyin } from "pinyin-pro";

const emits = defineEmits(["update:modelValue", "selectAACodeConfirm"]);
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
});
const _ = getGlobalLodash()
const selectAACodeDialog = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
const columns = ref<Array<IColumnProps>>([
    {
        label: "辅助核算类别",
        prop: "name",
        align: "left",
        headerAlign: "left",
        minWidth: 140,
        resizable: false,
        
    },
    { slot: "aaNum" },
    {
        label: "名称",
        prop: "aaname",
        align: "left",
        headerAlign: "left",
        minWidth: 320,
        resizable: false,
    },
]);
let AACodeTableList = ref<IAACodeTableItem[]>([]);
let AACodeTableListOrigin = ref<IAACodeTableItem[]>([]);
const initTable = (data: IAACodeTableItem[]) => {
    AACodeTableList.value = _.cloneDeep(data);
    AACodeTableListOrigin.value = _.cloneDeep(data);
};
const selectAACodeConfirm = () => {
    let res: Array<{ aatype: string; aaeid: string; aanum: string; aaname: string }> = AACodeTableList.value.map((v: IAACodeTableItem) => {
        return {
            aatype: v.aatype,
            aaeid: v.aaeid,
            aanum: v.list.find((n: IAssistEntriesItem) => n.aaeid === Number(v.aaeid))?.aanum || "",
            aaname: v.aaname,
        };
    });
    selectAACodeDialog.value = false;
    emits("selectAACodeConfirm", res);
};
const selectAACodeClear = () => {
    AACodeTableList.value.forEach((v: IAACodeTableItem) => {
        v.aaeid = "";
        v.aaname = "";
    });
};
const handleAAEidChange = (aaeid: string, index: number) => {
    AACodeTableList.value[index].aaname = AACodeTableList.value[index].list.find((v) => v.aaeid === Number(aaeid)).aaname;
    if (Number(aaeid) < 0) {
        AACodeTableList.value[index].aaeid = " ";
    }
};
let currentOptionIndex = ref(-1);
const handleAAFocus = (index: number) => {
    currentOptionIndex.value = index;
};
const handleAAFilter = (str: string) => {
    const lowerCaseValue = str.trim().toLowerCase();
    AACodeTableList.value[currentOptionIndex.value].list =
        AACodeTableListOrigin.value[currentOptionIndex.value].list.filter((v) => {
            const itemProp = v.aanum + '-' + v.aaname; 
            const pinyinFirst = pinyin(itemProp, {   
                pattern: "first",   
                toneType: "none",   
                type: "array"   
            }).join("").toLowerCase();
            const pinyinFull = pinyin(itemProp, {   
                toneType: "none",   
                type: "array"   
            }).join("").toLowerCase();

            return (
                v.aanum.includes(str) 
                || v.aaname.includes(str)
                || pinyinFirst.includes(lowerCaseValue) 
                || pinyinFull.includes(lowerCaseValue)
            )
        }) || [];
};
defineExpose({
    initTable,
});
</script>

<style scoped lang="less">
.select-aacode-dialog-container .select-aacode-dialog-table {
    margin: 12px 20px 16px;
    :deep(.aacode-select.el-select .el-input__wrapper) {
        .el-input__inner {
            border: none;
        }
        .el-input__suffix {
            height: 90%;
        }
    }
}
</style>
