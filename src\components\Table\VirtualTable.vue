<template>
    <div
        v-loading="loading"
        element-loading-text="正在加载数据..."
        :class="{
            table: true,
            'paging-show': pageIsShow,
            'paging-hide': !pageIsShow,
            'custom-table-normal': useNormalScroll,
            'custom-table': !useNormalScroll,
        }"
    >
        <el-table
            ref="tableRef"
            :size="size"
            :cell-class-name="cellClassName"
            :show-header="showHeader"
            :border="border"
            :data="renderData"
            :tree-props="treeProps"
            :fit="fit"
            :stripe="stripe"
            :max-height="maxHeight"
            :height="height"
            :row-key="onlyKey"
            :empty-text="customEmptyText"
            :highlight-current-row="highlightCurrentRow"
            :automatic-dropdown="false"
            :row-class-name="computeRowClass"
            :show-overflow-tooltip="showOverflowTooltip"
            :cell-style="cellStyle"
            :scrollbar-always-on="scrollbarShow"
            :header-row-class-name="computeHeaderRowClass"
            :header-cell-class-name="computeHeaderCellClass"
            @select="handleSelect"
            @selection-change="handleSelectionChange"
            @select-all="handleSelectAll"
            @cell-click="handleCellClick"
            @row-click="handleRowClick"
            @sort-change="handleSortChange"
            @cell-mouse-enter="cellMouseEnter"
            @cell-mouse-leave="cellMouseLeave"
            @row-dblclick="handleRowDblclick"
            :span-method="objectSpanMethod"
            :style="{ minHeight: minHeight ? minHeight : '0px' }"
            :tooltip-options="tooltipOptions"
            :show-summary="showSummary"
            :summary-method="summaryMethod"
            :class="!showHeader ? 'no-header' : ''"
            @header-dragend="headerDragend"
        >
            <template v-for="colItem in columns" :key="colItem.prop">
                <ColumnItem :column="colItem">
                    <template v-for="slotItem in slotsArr" #[slotItem]="{ slotColumn }" :key="slotItem">
                        <slot :name="slotItem" :slotColumn="slotColumn"></slot>
                    </template>
                    <template #selection="{ slotColumn }">
                        <el-table-column
                            :type="virtualTable ? 'default' : 'selection'"
                            :width="slotColumn.width ?? 40"
                            :header-align="slotColumn.headerAlign ?? 'center'"
                            :align="slotColumn.align ?? 'center'"
                            :selectable="selectable"
                            class-name="selection-col"
                            :reserve-selection="slotColumn.reserveSelection ?? false"
                            :resizable="false"
                        >
                            <template #header v-if="virtualTable">
                                <el-checkbox class="test" v-model="selectAllStatus" @change="handleSelectAllChange" />
                            </template>
                            <template #default="scope" v-if="virtualTable">
                                <el-checkbox
                                    v-if="selectable(scope.row, scope.$index)"
                                    v-model="scope.row[selectKey]"
                                    @click.stop
                                    @change="(check) => handleSelectChange(scope.row, check)"
                                />
                            </template>
                        </el-table-column>
                    </template>
                </ColumnItem>
            </template>
            <template #append>
                <slot name="append"></slot>
            </template>
        </el-table>
        <TablePagination
            v-if="pageIsShow"
            class="pagination"
            :size="size"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :layout="layout"
            :total="total"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
        >
        </TablePagination>
    </div>
</template>

<script setup lang="ts">
import { ref, useSlots, watch, onMounted, onActivated, onBeforeUnmount, computed, watchEffect } from "vue";
import ColumnItem from "./ColumnItem.vue";
import TablePagination from "./TablePagination.vue";

import type { IColumnProps } from "./IColumnProps";
import type { CellCls, CellStyle, ElTable } from "element-plus/es/components";
import { saveColWidth } from "@/components/ColumnSet/utils";

const props = withDefaults(
    defineProps<{
        loading?: boolean;
        data?: Array<any>;
        columns?: Array<IColumnProps>;
        pageIsShow?: boolean;
        pageSizes?: Array<number>;
        pageSize?: number;
        layout?: string;
        total?: number;
        border?: boolean;
        stripe?: boolean;
        fit?: boolean;
        size?: any;
        rowKey?: string;
        treeProps?: Object;
        currentPage?: number;
        maxHeight?: number | string;
        minHeight?: string;
        emptyText?: string;
        height?: number | string;
        hearderRowStyleName?: string;
        showOverflowTooltip?: boolean;
        showHeader?: boolean;
        cellClassName?: CellCls<any>;
        cellStyle?: CellStyle<any>; // string | Function
        rowClassName?: any; // string | Function
        objectSpanMethod?: any; // Function
        headerSelectNone?: boolean;
        scrollbarShow?: boolean;
        tooltipOptions?: any;
        tableData?: Array<any>;
        initialBalance?: boolean;
        highlightCurrentRow?: boolean;
        useNormalScroll?: boolean;
        selectable?: (row: any, index: number) => boolean;
        showSummary?: boolean;
        summaryMethod?: (params: { columns: any; data: any }) => any;
        virtualTable?: boolean;
        minVirtualScrollLines?: number;
        tableName?: string;
    }>(),
    {
        loading: false,
        data: () => [],
        columns: () => [],
        pageIsShow: false,
        currentPage: () => 1,
        pageSizes: () => [1, 2, 10, 20, 30],
        pageSize: () => 20,
        layout: () => "total, sizes, prev, pager, next, jumper",
        total: () => 0,
        border: true,
        stripe: true,
        fit: true,
        rowKey: "",
        size: "default",
        treeProps: () => ({}),
        hearderRowStyleName: "",
        showHeader: true,
        cellClassName: "",
        rowClassName: "",
        emptyText: " ",
        objectSpanMethod: () => {},
        headerSelectNone: false,
        scrollbarShow: true,
        tooltipOptions: () => ({ effect: "light", placement: "right-start", offset: -10 }),
        initialBalance: false,
        highlightCurrentRow: true,
        useNormalScroll: false,
        showOverflowTooltip: true,
        selectable: () => true,
        showSummary: false,
        summaryMethod: () => {},
        virtualTable: false,
        minVirtualScrollLines: 300,
        tableName: "",
    }
);

const slots = useSlots();
const slotsArr = Object.keys(slots);

const stripe = ref(props.stripe);
const selectData = ref<Array<any>>([]);

// Table instance
const tableRef = ref<InstanceType<typeof ElTable>>();
function getTable() {
    return tableRef.value;
}

// Selection
const clearSelection = () => {
    if (props.virtualTable) {
        mapData.value.forEach((item) => (item[selectKey] = false));
        selectAllStatus.value = false;
        selectData.value = [];
        emit("selection-change", []);
        updateView();
        return;
    } else {
        tableRef.value?.clearSelection();
    }
};
const toggleRowSelect = (row: number, selected: boolean) => {
    tableRef.value?.toggleRowSelection(props.data[row], selected);
};

// Scroll
const tableScrollTo = (x: number, y: number) => {
    tableRef.value?.scrollTo(x, y);
};
const setScrollLeft = (scrollLeft: number) => {
    tableRef.value?.setScrollLeft(scrollLeft);
};
const setScrollTop = (val: number) => {
    tableRef.value?.setScrollTop(val);
};

// Cell style
const computeRowClass = ({ row, rowIndex }: any) => {
    let isSelected = selectData.value && selectData.value.indexOf(row) > -1;
    if (isSelected) {
        stripe.value = false; //斑马纹和选中样式冲突
    }
    let propsClassName = "";
    if (typeof props.rowClassName == "function") {
        propsClassName = props.rowClassName({ row, rowIndex }) ?? "";
    } else {
        propsClassName = props.rowClassName;
    }
    return isSelected ? "row-selected" + " " + propsClassName : propsClassName;
};
function computeHeaderRowClass() {
    let result = "";
    if (props.hearderRowStyleName) {
        result = props.hearderRowStyleName;
    }
    if (props.headerSelectNone) {
        result += " header-select-none";
    }
    return result;
}
function computeHeaderCellClass(scope: { row: any; column: any }) {
    let result = "";
    if (scope.column.showOverflowTooltip) {
        result += "show_overflow_tooltip";
    }
    return result;
}

// Emit events
const emit = defineEmits<{
    (e: "select", selection: any, row: any): void;
    (e: "selection-change", value: any): void;
    (e: "select-all", value: any): void;
    (e: "row-click", row: any, column: any, event: any): void;
    (e: "row-dblclick", row: any, column: any, event: any): void;
    (e: "size-change", value: number): void;
    (e: "current-change", value: number): void;
    (e: "sort-change", column: any, prop: any, order: any): void;
    (e: "cell-click", row: any, column: any, cell: any, event: any): void;
    (e: "table-add-or-subtract", val: any): void;
    (e: "refresh"): void;
    (e: "cell-mouse-enter-qichu", index: number, top: number, left: number, cellHeight: number, tableRightDistance: number): void;
    (e: "cell-mouse-enter", row: any, column: any, cell: any, event: any): void;
    (e: "cell-mouse-leave"): void;
    (e: "handleSubtract", index: number, key?: any): Boolean;
    (e: "handleAdd", index: number): Boolean;
    (e: "scroll"): void;
}>();

// 正常表格用
const handleSelect = (selection: any, row: any) => {
    emit("select", selection, row);
};
const handleSelectionChange = (val: any) => {
    selectData.value = val;
    emit("selection-change", val);
};
const handleSelectAll = (val: any) => {
    emit("select-all", val);
};

// 虚拟表格用
const handleSelectChange = (row: any, val: any) => {
    row[selectKey] = !!val;
    const selection: any[] = [];
    let allCheck = true;
    for (let i = 0; i < mapData.value.length; i++) {
        if (mapData.value[i][selectKey]) {
            selection.push(mapData.value[i]);
        } else if (props.selectable(mapData.value[i], i)) {
            allCheck = false;
        }
    }
    selectAllStatus.value = allCheck;
    updateView();
    handleVirtualSelectionChange();
    emit("select", selection, row);
};
const handleVirtualSelectionChange = () => {
    const selection = mapData.value.filter((item) => item[selectKey]);
    selectData.value = selection;
    emit("selection-change", selection);
};
const selectAllStatus = ref(false);
const handleSelectAllChange = (val: any) => {
    const selection: any[] = [];
    for (let i = 0; i < mapData.value.length; i++) {
        mapData.value[i][selectKey] = !!val && props.selectable(mapData.value[i], i);
        if (mapData.value[i][selectKey]) {
            selection.push(mapData.value[i]);
        }
    }
    updateView();
    handleVirtualSelectionChange();
    if (!val) {
        emit("select-all", []);
        return;
    }
    emit("select-all", selection);
};
const handleSizeChange = (val: any) => {
    emit("size-change", val);
};
const handleCurrentChange = (val: any) => {
    emit("current-change", val);
};
const handleRerefresh = () => {
    emit("refresh");
};
const handleRowClick = (row: any, column: any, event: any) => {
    emit("row-click", row, column, event);
};
const handleRowDblclick = (row: any, column: any, event: any) => {
    emit("row-dblclick", row, column, event);
};
const handleSortChange = (column: any, prop: any, order: any) => {
    emit("sort-change", column, prop, order);
};
const handleCellClick = (row: any, column: any, cell: any, event: any) => {
    emit("cell-click", row, column, cell, event);
};
const cellMouseEnter = (row: any, column: number, cell: any, event: any) => {
    if (props.initialBalance) {
        let index = props.data.findIndex((v: any) => v["asubCode"] === row["asubCode"]);
        const { clientX = 0, clientY = 0 } = event;
        const cellHeight = cell.offsetHeight;
        const tableRect = tableRef.value?.$el.getBoundingClientRect();
        const tableRightDistance = tableRect.right - event.clientX;
        emit("cell-mouse-enter-qichu", index, clientY, clientX, cellHeight, tableRightDistance);
        return;
    }
    emit("cell-mouse-enter", row, column, cell, event);
};
const cellMouseLeave = () => {
    emit("cell-mouse-leave");
};

//双击双竖线
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    let elements = document.querySelectorAll(`.${column.id} .cell`);
    let max = elements[0].scrollWidth;
    elements.forEach((item) => {
        if (item.scrollWidth > max) {
            max = item.scrollWidth;
        }
    });
    if (newWidth === oldWidth && column.width < max) {
        column.width = max + 16; //单元格左右有8内边距
    }
    //列宽拖动保存浏览器
    if (props.tableName) {
        saveColWidth(props.tableName, column.width, column.property);
    }
};

const customEmptyText = ref(" ");
watch(
    () => props.emptyText,
    (val) => {
        if (val.trim()) {
            customEmptyText.value = val;
        } else {
            customEmptyText.value = " ";
        }
    },
    { immediate: true }
);
watch(
    () => props.loading,
    (val) => {
        if (!val && props.data.length === 0) {
            customEmptyText.value = "暂无数据";
        } else if (val && props.data.length === 0) {
            // 数据加载中...
            customEmptyText.value = " ";
        } else {
            customEmptyText.value = "  ";
        }
    }
);

const savedScrollPosition = ref(0);
const scrollTop = ref(0);
const saveScrollPosition = (left: number) => {
    savedScrollPosition.value = left;
};
watch(
    () => props.currentPage,
    () => {
        const routerContainer = document.querySelector(".router-container");
        routerContainer?.scrollTo(0, 0);
        tableScrollTo(savedScrollPosition.value, 0);
    }
);
const getScrollElement = () => {
    return (tableRef.value?.$refs.scrollBarRef as any)?.wrapRef as HTMLElement;
};
const handleScroll = (e: any) => {
    emit("scroll");
    saveScrollPosition(e.target.scrollLeft);
    scrollTop.value = e.target.scrollTop;
    if (!props.virtualTable || props.data.length < props.minVirtualScrollLines) {
        changeCacheScrollTop();
        calcBodyHeight();
        renderData.value = props.virtualTable ? mapData.value : props.data;
        return;
    }
    renderVirtualTable();
};
onMounted(() => {
    const scrollDom = getScrollElement();
    scrollDom && scrollDom.addEventListener("scroll", handleScroll);
    props.virtualTable && window.addEventListener("resize", updateView);
});
onBeforeUnmount(() => {
    const scrollDom = getScrollElement();
    scrollDom && scrollDom.removeEventListener("scroll", handleScroll);
    props.virtualTable && window.removeEventListener("resize", updateView);
});
let cacheScrollTop = 0;
onActivated(() => {
    setScrollLeft(savedScrollPosition.value);
    const scrollDom = getScrollElement();
    scrollDom && (scrollDom.scrollTop = cacheScrollTop);
});

// virtualTable
// 注意：不允许动态新增行删除行，因为是拿的索引作为唯一的 key，如果新增行删除行，请勿使用该功能
const onlyKey = computed(() => props.rowKey || (props.virtualTable ? "onlyKey" : ""));
const selectKey = "customSelect";
const mapData = computed(() =>
    props.data.map((item, index) => ({ ...item, [onlyKey.value]: props.rowKey ? item[props.rowKey] : index, [selectKey]: false }))
);
const sizes = ref<any>({});
const buffer = 500;
const top = ref(0);
const bottom = ref(0);
const start = ref(0);
const end = ref(0);
let timer: any = null;
const renderData = ref<Array<any>>([]);
const rowHeight = computed(() => (window.isErp ? 44 : 37));
const offsetMap = computed(() => {
    const res: any = {};
    let total = 0;
    for (let i = 0; i < props.data.length; i++) {
        const key = mapData.value[i][onlyKey.value];
        res[key] = total;
        const curSize = sizes.value[key];
        const size = typeof curSize === "number" ? curSize : rowHeight.value;
        total += size;
    }
    return res;
});
watchEffect(() => {
    if (!props.virtualTable) renderData.value = props.data;
});
const renderVirtualTable = (shouldUpdate = true) => {
    changeCacheScrollTop();
    if (!props.virtualTable || props.data.length < props.minVirtualScrollLines) {
        renderData.value = props.virtualTable ? mapData.value : props.data;
        calcBodyHeight();
        // calcPosition();
        return;
    }
    updateSizes();
    calcRenderData();
    calcPosition();
    shouldUpdate && updatePosition();
};
const updateSizes = () => {
    const tableBody = tableRef.value?.$refs.tableBody as HTMLElement;
    const rows = tableBody?.querySelectorAll(".el-table__row") as unknown as any[];
    if (rows) {
        Array.from(rows).forEach((row, index) => {
            const item = renderData.value[index];
            if (!item) return;
            const key = item[onlyKey.value];
            const offsetHeight = row.offsetHeight;
            if (sizes.value[key] !== offsetHeight) {
                sizes.value[key] = offsetHeight;
            }
        });
    }
};
const changeCacheScrollTop = () => {
    const scroller = getScrollElement();
    cacheScrollTop = scroller.scrollTop;
};
const calcRenderData = () => {
    const scroller = getScrollElement();
    const thisTop = scroller.scrollTop - buffer;
    const thisBottom = scroller.scrollTop + scroller.offsetHeight + buffer;

    let l = 0;
    let r = props.data.length - 1;
    let mid = 0;
    while (l <= r) {
        mid = Math.floor((l + r) / 2);
        const midVal = getOffsetTop(mid);
        if (midVal < thisTop) {
            const midNextVal = getOffsetTop(mid + 1);
            if (midNextVal > thisTop) break;
            l = mid + 1;
        } else {
            r = mid - 1;
        }
    }

    let thisStart = mid;
    let thisEnd = props.data.length - 1;
    for (let i = thisStart + 1; i < props.data.length; i++) {
        const offsetTop = getOffsetTop(i);
        if (offsetTop >= thisBottom) {
            thisEnd = i;
            break;
        }
    }

    if (thisStart % 2) {
        thisStart = thisStart - 1;
    }

    top.value = thisTop;
    bottom.value = thisBottom;
    start.value = thisStart;
    end.value = thisEnd;
    renderData.value = mapData.value.slice(thisStart, thisEnd + 1);
};
const calcBodyHeight = () => {
    const last = props.data.length - 1;
    const wrapHeight = getOffsetTop(last) + getSize(last);
    const el = tableRef.value?.$refs.bodyWrapper as any;
    if (!el) return;
    const virtualBody = (tableRef.value?.$refs.tableBody as HTMLElement).parentNode as HTMLElement;
    virtualBody && (virtualBody.style.height = wrapHeight + "px");
};
const calcPosition = () => {
    const el = tableRef.value?.$refs.bodyWrapper as any;
    if (!el) return;
    const offsetTop = getOffsetTop(start.value);
    const tableBody = tableRef.value?.$refs.tableBody as HTMLElement;
    tableBody && (tableBody.style.transform = `translateY(${offsetTop}px)`);
    calcBodyHeight();
};
const updatePosition = () => {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
        timer && clearTimeout(timer);
        renderVirtualTable(false);
    }, 100);
};
const getOffsetTop = (index: number) => {
    const item = mapData.value[index];
    if (item) return offsetMap.value[item[onlyKey.value]] || 0;
    return 0;
};
const getSize = (index: number) => {
    const item = renderData.value[index];
    if (item) {
        const key = item[onlyKey.value];
        return sizes.value[key] || rowHeight.value;
    }
    return rowHeight.value;
};
// virtualTable 使用的前提是在表格内部滚动
// 当外部数据变化的时候需要外部 nextTick 调用此方法，重新渲染表格数据
// 只有使用虚拟表格才需要调用该方法
// 当外部数据更新后，如需要清空选择，需要调用 clearSelection 方法，暂时没办法直接判断是否清空选择，因为 onlyKey 是基于 index 的...
// 当然还得清空一下表格数据，还是因为 index 作为 key 的原因...
// 完整示例如下：
// request().then((res) => {
//      tableData.value = [];
//      tableRef.value?.resetVirtualTableState();
//      nextTick().then(() => {
//          tableData.value = res.data;
//          tableRef.value?.updateView();
//      })
// })
const updateView = () => {
    renderVirtualTable();
};
const resetVirtualTableState = () => {
    clearSelection();
    setScrollTop(0);
    cacheScrollTop = 0;
    const tableBody = tableRef.value?.$refs.tableBody as HTMLElement;
    props.virtualTable && tableBody && (tableBody.style.transform = `translateY(${0}px)`);
};
// Expose
defineExpose({ getTable, toggleRowSelect, tableScrollTo, clearSelection, setScrollTop, scrollTop, updateView, resetVirtualTableState });
</script>

<style lang="less" scoped>
.table {
    position: relative;
    .pagination {
        text-align: right;
    }

    :deep(.el-table) {
        overflow: visible;

        .el-table__header {
            .header-select-none {
                th:first-child {
                    .cell {
                        cursor: default;

                        .el-checkbox {
                            display: none;
                        }
                    }
                }
            }
            .el-table__cell {
                &.show_overflow_tooltip {
                    .cell {
                        white-space: nowrap;
                    }
                }
            }
        }

        .el-popper.is-light,
        .el-popper.is-dark {
            max-width: 300px;
            text-align: left;
        }

        .cell.el-tooltip {
            min-width: 0px;
        }
        .el-table__header,
        .el-table__body {
            .el-table__row {
                &.el-table__row--striped {
                    background-color: var(--table-color);
                }

                &.row-selected {
                    background-color: var(--table-selected-color);
                }
            }
            .el-table-column--selection.el-table__cell {
                .cell {
                    padding: 0 4px;
                    min-width: 30px;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }

        .el-table-column--selection {
            text-align: center;
            .el-checkbox.is-disabled {
                display: none;
            }
        }

        &.el-table--border {
            &::before,
            &::after {
                top: 0;
            }
        }
        &.el-table--enable-row-hover .el-table__body tr.row-selected:hover > td.el-table__cell {
            background-color: var(--table-selected-hover-color);
        }

        .selection-col {
            .cell {
                display: flex;
                align-items: center;
            }
            .el-checkbox {
                height: unset;
            }
        }
    }

    &.paging-show {
        :deep(.el-table) {
            display: flex;
            flex-direction: column;
            .el-table__inner-wrapper {
                flex: 1;
                &::before {
                    height: 0;
                }
            }
            .el-table__body-wrapper {
                padding-bottom: 14px;
            }
            .el-scrollbar {
                position: static;
            }
        }
    }

    &.paging-hide {
        :deep(.el-table) {
            border-bottom: 1px solid var(--el-border-color-lighter);
        }
    }
}
body[erp] {
    .table {
        :deep(.header-select-none) {
            th:first-child {
                div.cell:after {
                    content: "";
                    display: none;
                }
            }
        }
    }
}
</style>
