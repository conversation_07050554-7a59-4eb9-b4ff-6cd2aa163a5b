.main-content {
    .main-tool-left {
        & .transfer-free-accountset {
            float: left;
            margin-top: 2px;
            margin-left: 16px;
            box-sizing: border-box;
            width: 120px;
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid var(--main-color);
            border-radius: 2px;
            font-size: var(--font-size);
            color: var(--main-color);
            line-height: 20px;
            font-weight: 500;
            cursor: pointer;
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
            &:active {
                background-color: var(--dark-main-color);
                border-color: var(--dark-main-color);
                color: var(--white);
            }
        }
        & .main-top-txt {
            float: left;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            margin-left: 10px;
        }
    }
    .main-tool-right {
        align-items: normal !important;
        & .input-group {
            width: 214px;
            height: 32px;
            position: relative;
            float: right;
            margin-right: 20px;
            & input[type="text"] {
                font-size: var(--font-size);
                width: 170px;
                height: 30px;
                padding: 0;
                padding-left: 10px;
                padding-right: 32px;
                outline: none;
                border-radius: 4px;
                border: 1px solid var(--weaker-font-color);
            }
            & .icon {
                background-image: url(@/assets/Settings/sousuo.png);
                background-repeat: no-repeat;
                height: 18px;
                width: 18px;
                top: 7px;
                right: 8px;
                cursor: pointer;
                position: absolute;
                margin: 0;
            }
        }
        & .table-icon {
            margin-right: 10px;
            margin-top: 6px;
            width: 20px;
            height: 20px;
            background-repeat: no-repeat;
            cursor: pointer;
            background-image: url(@/assets/Settings/kapianshi.png);
            &.selected {
                background-image: url(@/assets/Settings/kapianshilv.png);
            }
        }

        & .list-icon {
            margin-right: 10px;
            margin-top: 7px;
            width: 20px;
            height: 20px;
            background-repeat: no-repeat;
            cursor: pointer;
            background-image: url(@/assets/Settings/liebiao.png);
            &.selected {
                background-image: url(@/assets/Settings/liebiaolv.png);
            }
        }
        & .recyclebin-icon {
            float: right;
            margin-left: 7px;
            margin-right: 5px;
            margin-top: 2px;
            background-image: url(@/assets/Settings/recyclebin2.png);
            background-size: 29px 28px;
            background-repeat: no-repeat;
            padding-left: 28px;
            font-size: 16px;
            line-height: 28px;
            color: var(--font-color);
            cursor: pointer;
            &:hover {
                color: var(--main-color);
                background-image: url(@/assets/Settings/recyclebin2-hover.png);
            }
        }
    }
    & .main-center-loading {
        padding: 50px;
        text-align: left;
        height: 400px;
        font-size: 14px;
        color: #333333;
    }
    .main-center {
        text-align: left;
        min-height: 694px;
        width: 100%;
        box-sizing: border-box;
        & .account-item {
            margin-right: 20px;
            margin-bottom: 20px;
            width: calc((100% - 60px) / 4);
            height: 225px;
            border-radius: 4px;
            display: inline-block;
            vertical-align: top;
            border: 1px solid #e3e3e3;
            transition: var(--transition-time);
            cursor: pointer;
            box-sizing: border-box;
            position: relative;
            &:nth-child(4n + 4) {
                margin-right: 0;
            }
            &:hover {
                border-color: var(--main-color);
                box-shadow: 0 0 30px 0 rgba(179, 179, 179, 0.3), 0 0 20px 0 rgba(240, 240, 240, 0.3);
            }
            &.current {
                border-color: var(--main-color);
                & .account-item-title {
                    border-bottom: 1px solid var(--main-color);
                }
            }
            &.current .account-image {
                position: absolute;
                background: url(@/assets/Settings/current.png?v=**********) no-repeat;
                width: 59px;
                height: 59px;
                right: 0;
                bottom: 0;
            }
            & .account-item-title {
                height: 44px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 44px;
                border-bottom: 1px solid #e3e3e3;
                padding-left: 14px;
                padding-right: 48px;
                position: relative;
                & .account-item-title-txt {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: left;
                }
                & .account-item-settings {
                    background: url(@/assets/Settings/settings-icon.svg) no-repeat;
                    background-size: 100% 100%;
                    width: 20px;
                    height: 20px;
                    padding: 4px 0;
                    position: absolute;
                    right: 14px;
                    top: 8px;
                    &:hover {
                        & .account-item-settings-downlist {
                            opacity: 1;
                            visibility: visible;
                        }
                    }
                    & .account-item-settings-downlist {
                        transition: var(--transition-time);
                        visibility: hidden;
                        opacity: 0;
                        position: absolute;
                        top: 28px;
                        left: -32px;
                        padding: 6px 0;
                        box-shadow: 0px 1px 8px -2px rgba(0, 0, 0, 0.4);
                        background-color: var(--white);
                        width: 84px;
                        z-index: 1;
                        & * {
                            text-align: center;
                            padding: 6px 0px;
                            color: rgb(7, 21, 8);
                            font-size: var(--h5);
                            line-height: 17px;
                            cursor: pointer;
                            display: block;
                            &:hover {
                                background-color: var(--main-color);
                                color: var(--white);
                            }
                        }
                    }
                }
            }
            & .account-item-content {
                padding: 0 14px;
                text-align: left;
                & .account-item-company-name {
                    padding-left: 25px;
                    height: 20px;
                    color: #071508;
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    background-image: url(@/assets/Settings/company-icon.svg);
                    background-repeat: no-repeat;
                    background-size: 19px 18px;
                    background-position: left top;
                    margin-top: 15px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                & .account-item-marks {
                    margin-top: 10px;
                    & .account-item-mark {
                        display: inline-block;
                        vertical-align: top;
                        border: 1px solid #e6e6e6;
                        border-radius: 2px;
                        text-align: center;
                        padding: 0 4px;
                        height: 19px;
                        color: #000;
                        font-size: var(--h5);
                        line-height: 19px;
                    }
                }
                & .account-item-info-line {
                    color: #4f4f4f;
                    font-size: var(--h5);
                    line-height: 19px;
                    margin-top: 10px;
                }
            }
            & .account-item-shadow {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                text-align: center;
                background-color: rgba(0, 0, 0, 0.04);
                display: none;
                & .unlock-button-content {
                    margin-top: 24px;
                    display: inline-block;
                    & .unlock-button-img {
                        width: 52px;
                        height: 61px;
                        margin: 0 auto 0;
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                    }
                    & .unlock-button {
                        display: none;
                        background-color: var(--main-color);
                        height: 25px;
                        padding: 0 10px;
                        text-align: center;
                        color: var(--white);
                        font-size: var(--h5);
                        line-height: 25px;
                        font-weight: bold;
                        margin: 10px auto 0;
                    }
                }
            }
            &.unlocked {
                & .account-item-title {
                    visibility: hidden;
                }
                & .account-item-content {
                    filter: blur(2px);
                }
                & .account-item-shadow {
                    display: block;
                    & .account-item-title {
                        visibility: visible;
                    }
                    & .account-item-content {
                        filter: blur(2px);
                    }
                    & .unlock-button-content .unlock-button-img {
                        background-image: url("@/assets/Settings/unlock.png");
                    }
                }
            }
            &.locked {
                & .account-item-title {
                    visibility: hidden;
                }
                & .account-item-content {
                    filter: blur(2px);
                }
                & .account-item-shadow {
                    display: block;
                    & .account-item-title {
                        visibility: visible;
                    }
                    & .account-item-content {
                        filter: blur(2px);
                    }
                    & .unlock-button-content .unlock-button-img {
                        background-image: url("@/assets/Settings/lock.png");
                    }
                }
                &:hover {
                    .unlock-button-content {
                        .unlock-button-img {
                            background-image: url("@/assets/Settings/lock-green.png");
                        }

                        .unlock-button {
                            display: block;
                        }
                    }
                }
            }
        }
    }
}

.currentAs {
    background-image: url("@/assets/Settings/list-current.png");
    background-repeat: no-repeat;
    background-size: 27px;
    background-position: left top;
}
.list-lock-icon {
    width: 15px;
    height: 18px;
    vertical-align: top;
}
.delete-confirm-content {
    & .delete-confirm-main {
        padding: 30px 40px;
        & .txt {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            &.highlight-red {
                color: var(--red);
            }
            &.delete-font {
                font-size: var(--h2);
            }
        }
    }
    & .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
        & .delete-button {
            border-color: #d9d9d9;
            background-color: rgba(0, 0, 0, 0.04);
            color: var(--red);
        }
    }
}
.accountset-lock-dialog-content {
    padding-top: 20px;
    text-align: center;
    & table {
        margin: 0px auto;
        & td {
            height: 40px;
        }
        & td.tb-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            text-align: right;
        }
        & td.tb-field {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            max-width: 180px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    & .buttons {
        border-top: 1px solid var(--border-color);
        text-align: center;
        padding: 10px 0px;
        margin-top: 25px;
    }
}
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
    // width: var(--content-width) !important;
    width: 100% !important;
}