import dayjs from "dayjs";
export interface ITableItem {
    billId: number;
    billDate: string;
    billNo: string;
    billDesc: string;
    billTypeId: number | null;
    billTypeName: string;
    depId: number | null | "";
    depName: string;
    pmethodId: number | null;
    pmethodName: string;

    payeeId: number | string | null;
    payeeName: string;
    amount: string | number;
    tax: any;
    totalAmount: any;
    billSource: string;
    note: string;
    p_id: number | string;
    v_id: number;
    v_num: string;
    v_date: string;
    v_num2: string;
    rowType: number;
    originTax?: number;
    originTotalAmount?: number;
}

export interface IProjectOrDepartmentItem {
    asid: number;
    aatype: number;
    aaeid: number;
    aanum: string;
    aaname: string;
    value01: string;
    status: number;
    uscc: string;
    createdBy: number;
    createdDate: string;
    preName: string;
}

export interface IDepartmentItem {
    manager: string;
    mobilePhone: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}

// 分界线
export class expenseTypeForm {
    constructor(expenseTypeForm?: expenseTypeForm) {
        if (expenseTypeForm) {
            this.id = expenseTypeForm.id;
            this.no = expenseTypeForm.no;
            this.name = expenseTypeForm.name;
            this.asubId = expenseTypeForm.asubId;
            expenseTypeForm.taxAsubId && (this.taxAsubId = expenseTypeForm.taxAsubId);
        }
    }
    id: number = 0;
    no = "";
    name = "";
    asubId = "";
    taxAsubId? = "";
}
export class IRowItemEditInfo {
    billid: number = 0;
    billdate: string = "";
    billno: string = "";
    billdesc: string = "";
    billtypeid: number | null = null;
    depid: number = 0;
    pmethodid: number = 0;
    tax: string = "";
    totalamount: string = "";
    note: string = "";
}
export class ITableItemClass {
    constructor(billId?: number, defaultMathod?: number, billDate: string = dayjs(new Date()).format("YYYY-MM-DD"), rowType?: number,billNo?: string) {
        this.billId = billId || 0;
        this.pmethodId = defaultMathod || null;
        billDate && (this.billDate = billDate);
        this.rowType = rowType || 0;
        billNo && (this.billNo = billNo);
    }
    billId: number = 0;
    billDate: string = dayjs(new Date()).format("YYYY-MM-DD");
    billNo: string = "FY" + dayjs(new Date()).format("YYYYMMDDHHmmss") + "000001";
    billDesc: string = "";
    billTypeId: number | null = null;
    billTypeName: string = "";
    depId: number | null = null;
    depName: string = "";
    pmethodId: number | null = null;
    pmethodName: string = "";
    payeeId: number | null | string = null;
    payeeName: string = "";
    amount: string | number = "";
    tax: string | number = "";
    totalAmount: string | number = "";
    billSource: string = "";
    note: string = "";
    p_id: number | string = 0;
    v_id: number = 0;
    v_num: string = "";
    v_date: string = "";
    v_num2: string = "";
    rowType: number = 0;
    originTax?: number = 0;
    originTotalAmount?: number = 0;
}
export interface BasicTypeList {
    billTypes: Array<IBillTypeItem>;
    payMethods: Array<IPayMethodItem>;
}
export interface IBillTypeItem {
    asubId: number;
    asubName: string;
    billTypeId: number;
    billTypeName: string;
    billTypeNo: string;
}
export interface IPayMethodItem {
    asubId: number;
    asubName: string;
    isDefault: number;
    pmId: number;
    pmName: string;
    pmNo: string;
    taxAsubId: number;
    taxAsubName: string;
}
export interface IBillSearchInfo {
    date_s: string;
    date_e: string;
    vstatus: number;
    billtypeid: string;
    billdesc: string;
    billno: string;
    payeeid: string;
    paymethod: number;
    depid: string;
    note: string;
    payeeName: string;
    startVDate: string;
    endVDate: string;
    startVNum: string;
    endVNum: string;
    vgId: number;
    sourceType: number;
}
