import { getLemonClient, isLemonClient } from "./lmClient";

export const reloadAccountSetList = () => {
    if (isLemonClient()) {
        // 刷新账套和刷新菜单的动作是一起做的
        getLemonClient().loadTopAccounstInfo();
        getLemonClient().loadMenu();
    } else {
        window.postMessage({ type: "reloadAccountSetList" }, window.location.origin);
    }
};

export const setSelectedAccount = (asId: number) => {
    if (isLemonClient()) {
        getLemonClient().setSelectedAccount(asId);
    } else {
        window.postMessage({ type: "setSelectedAccount", nextAsId: asId }, window.location.origin);
    }
};

export const setSelectedAccountWithoutChange = (asId: number) => {
    window.postMessage({ type: "setSelectedAccountWithoutChange", nextAsId: asId }, window.location.origin);
};
