export interface IIncomeSheetQuarter {
    asid: number;
    expand: number;
    fold: number;
    lineID: number;
    lineNumber: number;
    lineType: number;
    monthTotal: number;
    note: string;
    proName: string;
    quater1: number;
    quater2: number;
    quater3: number;
    quater4: number;
    statementId: number;
    yearTotal: number;
    children?: IIncomeSheetQuarter[];
    indentation?: boolean;
}
