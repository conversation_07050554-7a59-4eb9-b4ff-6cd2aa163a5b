import { ref, watchEffect } from 'vue';  
import _ from 'lodash';
import { initStartOrEndMonth, type IPeriodData } from "@/components/DatePicker/utils"; 
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";

export function usePeriodData(searchInfo: any, start: any, end: any) {  
    const periodData = ref<IPeriodData[]>([]);  
    const periodStore = useAccountPeriodStore(); 

    watchEffect(() => {  
        const cachePeriodList = _.cloneDeep(periodStore.periodList);  
        const periodList = cachePeriodList.reverse();  
        periodData.value = periodList.map((item: any) => {  
            return {  
                year: item.year,  
                sn: item.sn,  
                pid: item.pid,  
                time: item.year + "" + String(item.sn).padStart(2, "0"),  
            };  
        });  

        const result = initStartOrEndMonth(periodData.value, start, end);  
        searchInfo.startMonth = result.startMonth;  
        searchInfo.endMonth = result.endMonth;  
    });  

    return {  
        periodData,  
    };  
} 