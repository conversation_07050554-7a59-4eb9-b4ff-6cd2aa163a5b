export interface ITableItem {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
    authStatus?: boolean;
    bank_id: string;
}

export interface ISearchParams {
    acType: number;
    acName: string;
    acAuth: boolean;
    pageIndex?: number;
    pageSize?: number;
}

export interface IBankBack {
    [key: string]: string;
}
export interface IBankList {
    id: string;
    name: string;
}
