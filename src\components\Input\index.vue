<template>
  <ToolTip
    :content="modelValue"
    :is-input="true">
    <el-input
      v-model="inputValue"
      v-bind="$attrs"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength"
      :show-word-limit="showWordLimit"
      :type="type"
      :formatter="formatter"
      @input="handleInput"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @clear="handleClear">
      <template
        v-if="$slots.prefix"
        #prefix>
        <slot name="prefix"></slot>
      </template>
      <template
        v-if="$slots.suffix"
        #suffix>
        <slot name="suffix"></slot>
      </template>
      <template
        v-if="$slots.prepend"
        #prepend>
        <slot name="prepend"></slot>
      </template>
      <template
        v-if="$slots.append"
        #append>
        <slot name="append"></slot>
      </template>
    </el-input>
  </ToolTip>
</template>

<script setup lang="ts">
  interface Props {
    modelValue?: string | number
    placeholder?: string
    disabled?: boolean
    maxlength?: number
    showWordLimit?: boolean
    type?: "text" | "textarea" | "number"
    formatter?: (value: string | number) => string
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: "",
    placeholder: "请输入",
    disabled: false,
    maxlength: undefined,
    showWordLimit: false,
    type: "text",
    formatter: (value: string | number) => value?.toString() || "",
  })

  const emit = defineEmits<{
    "update:modelValue": [value: string | number]
    input: [value: string | number, event: Event]
    change: [value: string | number]
    blur: [event: FocusEvent]
    focus: [event: FocusEvent]
    clear: []
  }>()

  const inputValue = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
  })

  const handleInput = (value: string | number, event: Event) => {
    emit("input", value, event)
  }

  const handleChange = (value: string | number) => {
    emit("change", value)
  }

  const handleBlur = (event: FocusEvent) => {
    emit("blur", event)
  }

  const handleFocus = (event: FocusEvent) => {
    emit("focus", event)
  }

  const handleClear = () => {
    emit("clear")
  }

  defineExpose({
    focus: () => {
      const input = document.querySelector(".el-input__inner") as HTMLInputElement
      input?.focus()
    },
    blur: () => {
      const input = document.querySelector(".el-input__inner") as HTMLInputElement
      input?.blur()
    },
    select: () => {
      const input = document.querySelector(".el-input__inner") as HTMLInputElement
      input?.select()
    },
  })
</script>

<style scoped lang="scss">
  .el-input {
    width: 100%;

    :deep(.el-input__inner) {
      height: 32px;
      line-height: 32px;
      text-overflow: ellipsis;
      white-space: nowrap;

      &::placeholder {
        color: var(--placeholder-color, #999);
      }
    }

    &.is-disabled {
      .el-input__inner {
        background-color: var(--disabled-bg-color, #f5f7fa);
        border-color: var(--disabled-border-color, #e4e7ed);
      }
    }
  }
</style>
