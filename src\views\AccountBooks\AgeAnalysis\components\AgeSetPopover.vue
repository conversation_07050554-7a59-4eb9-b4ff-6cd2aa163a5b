<template>
    <el-popover placement="bottom-start" :width="400" :visible="visible" :show-arrow="false" :offset="0">
        <template #reference>
            <a class="button ml-20" @mouseenter="handleMouseEnter" @mouseleave="closeAgeSetbox"> 账龄设置 </a>
        </template>
        <div class="ageset-box" @mouseenter="handleMouseEnter" @mouseleave="visible = false">
            <div v-loading="props.loading" element-loading-text="正在加载数据..." class="loading-box">
                <div class="table-box">
                    <el-table
                        class="ageset-table"
                        :data="newAgeSetList"
                        style="width: 260px"
                        border
                        :show-overflow-tooltip="true"
                        :tooltip-options="{ effect: 'light', placement: 'right-start', offset: -10 }"
                    >
                        <el-table-column property="title" label="标题" align="center" :resizable="false"> </el-table-column>
                        <el-table-column property="days" :min-width="80" :show-overflow-tooltip="false" label="天数" align="center" :resizable="false">
                            <template #default="scope">
                                <span
                                    :class="['days-text', { 'hover-change': scope.$index < newAgeSetList.length - 1 }]"
                                    @click="(e) => showInput(e, scope.$index)"
                                >
                                    {{ scope.row.days === 0 ? "..." : scope.row.days }}
                                </span>
                                <input
                                    class="days-input"
                                    type="number"
                                    style="display: none"
                                    min="1"
                                    :value="scope.row.days"
                                    @blur="(e:FocusEvent) => handleChangeDays(e, scope.$index)"
                                />
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="right-btns">
                        <a class="button" @click="addLine">增加行</a>
                        <a class="button" @click="deleteLine">删除行</a>
                    </div>
                </div>
                <div class="bottom-btns">
                    <a class="button solid-button" @click="handleChangeAgeSet">确定</a>
                    <a class="button" @click="handleCancel">取消</a>
                    <a class="button" @click="handleReset">重置</a>
                </div>
            </div>
        </div>
    </el-popover>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { ElNotify } from "@/util/notify";
import { initAgeSetList } from "../utils";
import type { IAgeSet } from "../types.ts";

const cloneDeep = getGlobalLodash().cloneDeep;

const props = withDefaults(
    defineProps<{
        ageSetList: IAgeSet[];
        defaultAgeSetList: IAgeSet[];
        loading: boolean;
    }>(),
    {
        ageSetList: () => [],
        defaultAgeSetList: () => [],
        loading: false,
    }
);

const emit = defineEmits<{
    (e: "change", list: IAgeSet[]): void;
}>();

const newAgeSetList = ref(initAgeSetList);

// 账龄弹窗
const visible = ref(false);
// 鼠标移开按钮
let needWaiting = false;
function handleMouseEnter() {
    needWaiting = true;
    visible.value = true;
}
// 关闭
function closeAgeSetbox() {
    needWaiting = false;
    setTimeout(() => {
        if (needWaiting) {
            needWaiting = false;
            return;
        }
        visible.value = false;
    }, 100);
}

// 手动修改行数据>
const showInput = (e: MouseEvent, index: number) => {
    // 最后一行不允许编辑
    if (index === newAgeSetList.value.length - 1) return;
    const target = e.target as HTMLElement;
    target.style.display = "none";
    const ipt = target.nextSibling as HTMLInputElement;
    ipt.style.display = "block";
    ipt.focus();
};

const handleChangeDays = (e: FocusEvent, index: number) => {
    const target = e.target as HTMLInputElement;
    const val = Math.floor(Number(target.value));
    target.style.display = "none";
    (target.previousSibling as HTMLElement).style.display = "inline-block";
    // 校验
    if (val < 1) {
        // 输入框值还原
        target.value = String(newAgeSetList.value[index].days);
        return;
    }
    if (!val) {
        // 输入框值还原
        target.value = String(newAgeSetList.value[index].days);
        return ElNotify({
            type: "warning",
            message: `天数仅支持整数`,
        });
    }
    if (val > 10800) {
        // 输入框值还原
        target.value = String(newAgeSetList.value[index].days);
        return ElNotify({
            type: "warning",
            message: `最大天数不能超过10800天！`,
        });
    }
    if (index > 0 && val <= newAgeSetList.value[index - 1].days && index !== newAgeSetList.value.length - 1) {
        // 输入框值还原
        target.value = String(newAgeSetList.value[index].days);
        return ElNotify({
            type: "warning",
            message: `天数不能小于或等于上一行`,
        });
    }
    if (index < newAgeSetList.value.length - 1 && val >= newAgeSetList.value[index + 1].days && newAgeSetList.value[index + 1].days !== 0) {
        // 输入框值还原
        target.value = String(newAgeSetList.value[index].days);
        return ElNotify({
            type: "warning",
            message: `天数不能大于或等于下一行`,
        });
    }

    // 赋值
    newAgeSetList.value[index].days = val;
    // 修改标题
    if (index === 0) {
        newAgeSetList.value[0].title = newAgeSetList.value[0].days + "天以内";
    } else {
        newAgeSetList.value[index].title = newAgeSetList.value[index].title.split("-")[0] + `-${val}天`;
    }
    // 第一项
    if (index === newAgeSetList.value.length - 2) {
        // 最后一项
        newAgeSetList.value[index + 1].title = newAgeSetList.value[index + 1].title.replace(/\d+/, String(val));
    } else {
        newAgeSetList.value[index + 1].title = val + "-" + newAgeSetList.value[index + 1].title.split("-")[1];
    }
};

// 增加行
const addLine = () => {
    const length = newAgeSetList.value.length;
    if (length === 10) {
        return ElNotify({
            type: "warning",
            message: `账龄设置最高不超过10行哦~`,
        });
    }
    const firstDay = newAgeSetList.value[length - 2].days;
    let lastDay = Number(newAgeSetList.value[length - 1].title.split("天")[0]) + 30;
    if (firstDay === 10800) {
        return ElNotify({
            type: "warning",
            message: `最大天数不能超过10800天！`,
        });
    }
    if (lastDay > 10800) {
        lastDay = 10800;
    }
    newAgeSetList.value[length - 1].title = `${firstDay}-${lastDay}天`;
    newAgeSetList.value[length - 1].days = lastDay;

    newAgeSetList.value.push({
        ageId: length + 1,
        title: `${lastDay}天以上`,
        days: 0,
    });
};
// 删除行
const deleteLine = () => {
    if (newAgeSetList.value.length === 2) {
        return ElNotify({
            type: "warning",
            message: `账龄设置不能低于2行哦~`,
        });
    }
    newAgeSetList.value.pop();
    // 数组长度发生了变化
    const newLength = newAgeSetList.value.length;
    newAgeSetList.value[newLength - 1].title = newAgeSetList.value[newLength - 1].title.split("-")[0] + "天以上";
    newAgeSetList.value[newLength - 1].days = 0;
};
// 取消
const handleCancel = () => {
    newAgeSetList.value = cloneDeep(props.ageSetList.length ? props.ageSetList : initAgeSetList);
    visible.value = false;
};
// 重置
const handleReset = () => {
    newAgeSetList.value = cloneDeep(props.defaultAgeSetList.length ? props.defaultAgeSetList : initAgeSetList);
};

// 确定修改
const handleChangeAgeSet = () => {
    emit("change", newAgeSetList.value);
    visible.value = false;
};

watch(
    () => props.ageSetList,
    (val) => {
        newAgeSetList.value = cloneDeep(val);
    }
);
</script>
<style lang="less" scoped>
.ageset-box {
    .table-box {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        .right-btns {
            display: flex;
            justify-content: space-around;
            flex-direction: column;
            height: 100px;
            .button {
                user-select: none;
            }
        }
        .days-text {
            display: inline-block;
            width: 100%;
            height: 35px;
            line-height: 35px;
            box-sizing: border-box;
            &.hover-change {
                &:hover {
                    width: 98%;
                    background-color: #fff;
                    border: 1px solid var(--main-color);
                    cursor: text;
                }
            }
        }
        .days-input {
            width: 96%;
            height: 35px;
            margin: 0 auto;
            border: 0;
            border: 1px solid var(--main-color);
            box-sizing: border-box;
        }
        :deep(.el-table) {
            &.ageset-table {
                .el-table__cell {
                    padding: 2px 0;
                    .cell {
                        padding: 0;
                    }
                }
            }
        }
    }

    .bottom-btns {
        display: flex;
        justify-content: space-around;
        margin-top: 20px;
        padding-right: 20px;
    }
}
</style>
