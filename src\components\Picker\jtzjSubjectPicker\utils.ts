import { request } from "@/util/service";

export async function checkinvalidasubid(asubid: string) {
    const asubValidCache: any[] = [];
    let invaild = true;
    let needRemoto = true;
    if (asubValidCache.length > 0) {
        (function () {
            for (let i = 0; i < asubValidCache.length; i++) {
                if (asubValidCache[i].id == asubid) {
                    invaild = asubValidCache[i].invaild;
                    needRemoto = false;
                    break;
                }
            }
        })();
        needRemoto = needRemoto ? true : needRemoto;
    }
    if (needRemoto) {
        const url = "/api/FixedAssets/IsValidCostAsub?asubid=" + asubid;
        await request({
            url,
            method: "post",
        })
            .then(async (res: any) => {
                invaild = !res.data;
                asubValidCache.push({ id: asubid, invaild: invaild });
            })
            .catch(() => {
                invaild = true;
            });
    }
    return invaild;
}