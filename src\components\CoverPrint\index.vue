<template>
  <div
    class="seniorPrint content"
    :class="{ erpSeniorPrint: isErp }">
    <div class="print-top">
      <div class="print-tool-left">
        <a
          class="solid-button mr-10"
          @click="save(getSettingId(), getSettingType())">
          保存
        </a>
        <a
          class="button mr-10"
          @click="reset">
          重置
        </a>
        <a
          class="button mr-10"
          @click="preview">
          预览
        </a>
        <a
          class="button mr-10"
          style="width: 120px"
          @click="syncDialogShow = true">
          同步到其他账套
        </a>
        <div
          class="video-guide mr-20"
          v-if="viewType === 'AccountBooks'"
          @click="helpManual">
          <img
            v-if="!isErp"
            src="@/assets/Icons/acc-help-book.png"
            alt="视频"
            style="height: 16px; margin-right: 5px" />
          <img
            v-if="isErp"
            src="@/assets/Icons/help-book.png"
            alt="视频"
            style="height: 16px; margin-right: 5px" />
          <a>帮助手册</a>
        </div>
        <div
          class="video-guide"
          v-else
          @click="helpVideo">
          <img
            v-if="!isErp"
            src="@/assets/Icons/video.png"
            alt="视频"
            style="height: 16px; margin-right: 5px" />
          <img
            v-if="isErp"
            src="@/assets/Icons/video-blue.png"
            alt="视频"
            style="height: 16px; margin-right: 5px" />
          <a>操作视频</a>
        </div>
      </div>
      <div class="zoom-btn">
        <el-select
          v-model="zoom"
          style="width: 100px; height: 28px; line-height: 28px"
          disabled>
          <el-option
            v-for="item in zoomList"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </div>
      <div class="print-tool-right">
        <div
          v-if="!isLemonClient()"
          :class="['screen-btn', fullScreen ? (isErp ? 'full-screen erp' : 'full-screen') : 'exit-full-screen']"
          @click="fullScreen = !fullScreen">
          <span>{{ !fullScreen ? "全屏" : "退出全屏" }}</span>
        </div>
      </div>
    </div>
    <div
      class="print-content"
      style="height: calc(100%-120px)">
      <div
        class="arrowLeftNav"
        v-show="leftNavShow"
        @click="() => (leftNavShow = !leftNavShow)"></div>
      <div
        class="expandLeftNav"
        v-show="!leftNavShow"
        @click="() => (leftNavShow = !leftNavShow)"></div>
      <div
        class="arrowRightNav"
        v-show="rightNavShow"
        @click="() => (rightNavShow = !rightNavShow)"></div>
      <div
        class="expandRightNav"
        v-show="!rightNavShow"
        @click="() => (rightNavShow = !rightNavShow)"></div>
      <div
        class="print-left"
        v-show="leftNavShow">
        <div
          class="page"
          style="padding: 10px 0 0 22px">
          纸张:{{ selectPagerSize.label }}
        </div>
        <div
          v-if="viewType === 'AccountBooks'"
          class="page"
          style="padding: 10px 0 0 22px">
          方向:
          <el-radio-group
            style="padding-left: 5px"
            v-model="printInfo.direction"
            @change="changeDirection()">
            <el-radio label="Z">纵向</el-radio>
            <el-radio label="H">横向</el-radio>
          </el-radio-group>
          <el-tooltip
            popper-class="el-option-tool-tip"
            effect="light"
            placement="right-start">
            <template #content>账簿封面支持分别按照横向和纵向配置两套模板，您只需要切换方向后配置模板并分别保存即可哦~</template>
            <img
              class="img-question"
              src="@/assets/Icons/question.png"
              style="height: 16px; margin-left: 3px" />
          </el-tooltip>
        </div>
        <div class="nav-tool">
          <div class="nav-name">工具</div>
          <div class="tool-list">
            <div
              class="tool text-icon"
              @dragend="navSetting('text', $event)"
              @dblclick="navSetting('text')"
              draggable="true">
              <i></i>
              <div class="tool-name">文字</div>
            </div>
            <div
              class="tool img-icon"
              @dragend="addImg($event)"
              @dblclick="addImg()"
              draggable="true">
              <i></i>
              <div class="tool-name">图片</div>
              <input
                type="file"
                @change="handleFileSelect"
                id="selfSealInput"
                multiple="multiple"
                accept="image/png, image/jpeg, image/jpg, image/bmp"
                style="display: none" />
            </div>
            <div
              class="tool square-icon"
              @dragend="navSetting('rectangle', $event)"
              @dblclick="navSetting('rectangle')"
              draggable="true">
              <i></i>
              <div class="tool-name">矩形</div>
            </div>
            <div
              class="tool line-icon"
              @dragend="navSetting('line', $event)"
              @dblclick="navSetting('line')"
              draggable="true">
              <i></i>
              <div class="tool-name">线条</div>
            </div>
          </div>
        </div>
      </div>
      <div class="print-center">
        <div
          class="main-drag-box"
          id="cover-node"
          v-loading="loading"
          element-loading-text="正在加载数据..."
          :style="{ ...getPagerWH }">
          <div
            class="main-drag-content"
            :style="{
              position: 'absolute',
              color: '#000',
              ...getPagerWH,
            }"
            :key="refreshId"
            @dragover="allowDrop">
            <Vue3DraggableResizable
              :class="{ active: item.active, unactive: !item.active, isErp: isErp }"
              v-for="item in coverData"
              :key="item.id"
              :initW="item.width"
              :initH="item.height"
              v-model:x="item.left"
              v-model:y="item.top"
              v-model:w="item.width"
              v-model:h="item.height"
              v-model:active="item.active"
              :minW="item.type === 'text' ? item.style.fontSize * 1.5 : 10"
              :minH="item.type === 'text' ? item.style.fontSize * 1.5 : 10"
              :handles="item.sticks"
              :draggable="!item.inputable"
              :resizable="!item.inputable"
              :parent="true"
              @activated="activeDrag($event, item, 'unTable')"
              @deactivated="deactivated($event, item)"
              @drag-start="mouduleDrag($event, 'drag-start', item)"
              @resize-start="mouduleDrag($event, 'resize-start', item)"
              @dragging="mouduleDrag($event, 'dragging', item)"
              @resizing="mouduleDrag($event, 'resizing', item)"
              @drag-end="mouduleDrag($event, 'drag-end', item)"
              @resize-end="mouduleDrag($event, 'resize-end', item)"
              @dblclick="caseOnDrop(item)"
              @mousedown.stop="
                bodyDown($event, item);
                selectStyle(item.style);
              ">
              <template v-if="item.type === 'text'">
                <div
                  class="drag-box"
                  v-if="!item.inputable"
                  @dblclick="dbClick(item)"
                  :style="{
                    width: item.width + 'px',
                    height: item.height + 'px',
                    boxSizing: 'border-box',
                    ...getStyle(item.style, item.height),
                    zIndex: 2,
                  }">
                  <span
                    v-if="item.label"
                    :style="{
                      width: item.style.fontStyle === 'italic' ? item.width - 3 + 'px' : item.width + 'px',
                      maxHeight: item.height + 'px',
                    }">
                    {{ item.label || "请输入文字" }}
                  </span>
                  <span
                    v-else
                    :style="{ width: item.width + 'px', color: '#666666', maxHeight: item.height + 'px' }">
                    {{ !isSavingPng ? "请输入文字" : "" }}
                  </span>
                </div>
                <el-input
                  v-else
                  :ref="
                    (el) => {
                      moudleInputRef[item.id] = el;
                    }
                  "
                  v-model="item.label"
                  type="textarea"
                  resize="none"
                  :style="{
                    width: item.width + 'px',
                    height: item.height + 'px',
                    textAlign: 'left',
                    boxSizing: 'border-box',
                    ...getStyle(item.style, item.height),
                  }"
                  :input-style="{
                    height: item.height + 'px',
                    minHeight: item.height + 'px',
                    padding: '0px 1px',
                    fontStyle: item.style.fontStyle,
                    fontWeight: item.style.fontWeight,
                  }"
                  @blur="blur(item)"
                  @change="addHistory" />
              </template>
              <template v-else-if="item.type === 'img'">
                <img
                  draggable="false"
                  v-if="item.dataSource"
                  :src="item.dataSource"
                  class="avatar" />
              </template>
              <template v-else-if="item.type === 'rectangle'">
                <div
                  :id="'rectangle-' + item.id"
                  :style="{
                    width: item.width + 'px',
                    height: item.height + 'px',
                    border: '1px solid #999',
                    boxSizing: 'border-box',
                    ...judgeBorder(item.id, item.left, item.top, item.width, item.height),
                  }"></div>
              </template>
              <template v-else-if="item.type === 'line'">
                <div
                  style="border-top: 1px solid rgb(6, 6, 6); margin: 0 auto; height: 1px; position: relative; top: 9px"
                  :style="{ borderTop: viewType === 'AccountBooks' ? '1px solid rgb(6, 6, 6)' : '1px solid rgb(153, 153, 153)' }"></div>
              </template>
            </Vue3DraggableResizable>
            <div
              class="line line-x"
              :class="{ isErp: isErp }"
              v-show="xline !== -1"
              :style="{ top: xline + 'px' }"></div>
            <div
              class="line line-y"
              :class="{ isErp: isErp }"
              v-show="yline !== -1"
              :style="{ left: yline + 'px' }"></div>
          </div>
          <!-- </DraggableContainer> -->
          <!-- 页边距限制角标封面不需要，自定义，不受设置影响 -->
          <!-- <div
                        class="top-left-subscript"
                        :style="{ top: `${mmToPx(printInfo.marginTop) - 24}px`, left: `${mmToPx(printInfo.marginLeft) - 24}px` }"
                    ></div>
                    <div
                        class="top-right-subscript"
                        :style="{ top: `${mmToPx(printInfo.marginTop) - 24}px`, right: `${mmToPx(printInfo.marginRight) - 24}px` }"
                    ></div>
                    <div
                        class="bottom-left-subscript"
                        :style="{ bottom: `${mmToPx(printInfo.marginBottom) - 24}px`, left: `${mmToPx(printInfo.marginLeft) - 24}px` }"
                    ></div>
                    <div
                        class="bottom-right-subscript"
                        :style="{ bottom: `${mmToPx(printInfo.marginBottom) - 24}px`, right: `${mmToPx(printInfo.marginRight) - 24}px` }"
                    ></div> -->
        </div>
      </div>
      <div
        class="print-right"
        v-show="rightNavShow">
        <div
          class="rich-text"
          style="height: 90px">
          <div class="rich-text-name">字体</div>
          <div class="typeFace">
            <div style="display: flex">
              <el-select
                v-model="selectFontType"
                :teleported="false"
                style="width: 100px"
                @change="changeBaseStyle('fontFamily', selectFontType)">
                <el-option
                  v-for="item in fontTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :style="{ fontFamily: item.value }" />
              </el-select>
              <el-select
                v-model="selectFontSize"
                :teleported="false"
                style="width: 60px"
                @change="changeBaseStyle('fontSize', selectFontSize)">
                <el-option
                  v-for="item in fontSizeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </div>
            <div style="margin-top: 9px">
              <span
                @click="changeBold(!isBold)"
                class="rich-text-btn"
                :class="{ 'is-active': isBold, 'is-grey': !barCanEdit }"
                style="font-weight: bold">
                B
              </span>
              <span
                @click="changeUnderline(!isUnderline)"
                class="rich-text-btn"
                :class="{ 'is-active': isUnderline, 'is-grey': !barCanEdit }"
                style="text-decoration: underline">
                U
              </span>
              <span
                @click="changeItalic(!isItalic)"
                class="rich-text-btn"
                :class="{ 'is-active': isItalic, 'is-grey': !barCanEdit }"
                style="font-style: italic">
                I
              </span>
            </div>
          </div>
        </div>
        <div
          class="rich-text"
          style="height: 68px">
          <div class="rich-text-name">文本对齐</div>
          <div class="rich-text-col">
            <div
              class="cell"
              :class="{ 'is-active': selectTextAlign == 'left', 'is-grey': !barCanEdit }"
              @click="changeTextAlign('left')">
              <img
                v-if="selectTextAlign === 'left' && !isErp"
                src="@/assets/Voucher/text-left-active.png" />
              <img
                v-else-if="selectTextAlign === 'left' && isErp"
                src="@/assets/Voucher/text-left-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-left.png" />
              <span>左对齐</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-active': selectTextAlign == 'center', 'is-grey': !barCanEdit }"
              @click="changeTextAlign('center')">
              <img
                v-if="selectTextAlign === 'center' && !isErp"
                src="@/assets/Voucher/text-center-active.png" />
              <img
                v-else-if="selectTextAlign === 'center' && isErp"
                src="@/assets/Voucher/text-center-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-center.png" />
              <span>水平居中</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-active': selectTextAlign == 'justify', 'is-grey': !barCanEdit }"
              @click="changeTextAlign('justify')">
              <img
                v-if="selectTextAlign === 'justify' && !isErp"
                src="@/assets/Voucher/text-justify-active.png" />
              <img
                v-else-if="selectTextAlign === 'justify' && isErp"
                src="@/assets/Voucher/text-justify-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-justify.png" />
              <span>两端对齐</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-active': selectTextAlign == 'right', 'is-grey': !barCanEdit }"
              @click="changeTextAlign('right')">
              <img
                v-if="selectTextAlign === 'right' && !isErp"
                src="@/assets/Voucher/text-right-active.png" />
              <img
                v-else-if="selectTextAlign === 'right' && isErp"
                src="@/assets/Voucher/text-right-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-right.png" />
              <span>右对齐</span>
            </div>
          </div>
        </div>
        <div
          class="rich-text"
          style="height: 60px">
          <div class="rich-text-name">文本超长</div>
          <div class="rich-text-col">
            <div
              class="cell"
              :class="{ 'is-active': selectWhiteSpace == 'Wrap', 'is-grey': !barCanEdit }"
              @click="changeWhiteSpace('Wrap')">
              <img
                v-if="selectWhiteSpace === 'Wrap' && !isErp"
                src="@/assets/Voucher/text-line-break-active.png" />
              <img
                v-else-if="selectWhiteSpace === 'Wrap' && isErp"
                src="@/assets/Voucher/text-line-break-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-line-break.png" />
              <span>文本折行</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-active': selectWhiteSpace == 'Truncation', 'is-grey': !barCanEdit }"
              @click="changeWhiteSpace('Truncation')">
              <img
                v-if="selectWhiteSpace === 'Truncation' && !isErp"
                src="@/assets/Voucher/text-cut-active.png" />
              <img
                v-else-if="selectWhiteSpace === 'Truncation' && isErp"
                src="@/assets/Voucher/text-cut-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-cut.png" />
              <span>截断</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-active': selectWhiteSpace == 'Reduce', 'is-grey': !barCanEdit }"
              @click="changeWhiteSpace('Reduce')">
              <img
                v-if="selectWhiteSpace === 'Reduce' && !isErp"
                src="@/assets/Voucher/text-scale-active.png" />
              <img
                v-else-if="selectWhiteSpace === 'Reduce' && isErp"
                src="@/assets/Voucher/text-scale-blue.png" />
              <img
                v-else
                src="@/assets/Voucher/text-scale.png" />
              <span>缩小</span>
            </div>
          </div>
        </div>
        <div
          class="rich-text"
          style="height: 110px">
          <div class="rich-text-name">控件对齐</div>
          <div class="rich-text-col">
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="changeBoxAlign('left')">
              <img src="@/assets/Voucher/cell-left.png" />
              <span>左对齐</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="changeBoxAlign('xCenter')">
              <img src="@/assets/Voucher/cell-center.png" />
              <span>水平居中</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="changeBoxAlign('right')">
              <img src="@/assets/Voucher/cell-right.png" />
              <span>右对齐</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="changeBoxAlign('top')">
              <img src="@/assets/Voucher/cell-top.png" />
              <span>顶对齐</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="changeBoxAlign('yCenter')">
              <img src="@/assets/Voucher/cell-center.png" />
              <span>垂直居中</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="changeBoxAlign('bottom')">
              <img src="@/assets/Voucher/cell-bottom.png" />
              <span>底对齐</span>
            </div>
          </div>
        </div>
        <div
          class="rich-text"
          style="height: 60px">
          <div class="rich-text-name">操作</div>
          <div class="rich-text-col">
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="historyIndex >= 0 ? preHistory() : ''">
              <img
                v-if="historyIndex >= 0"
                src="@/assets/Voucher/prevStep.png" />
              <img
                v-else
                src="@/assets/Voucher/pre-grey.png" />
              <span>上一步</span>
            </div>
            <div
              class="cell"
              :class="{ 'is-grey': !barCanEdit }"
              @click="historyArr.length !== historyIndex + 1 && historyArr.length != 0 ? nextHistory() : ''">
              <img
                v-if="historyArr.length !== historyIndex + 1 && historyArr.length != 0"
                src="@/assets/Voucher/nextStep.png" />
              <img
                v-else
                src="@/assets/Voucher/next-grey.png" />
              <span>下一步</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <SyncToOtherAccountSet
    v-model:syncDialogShow="syncDialogShow"
    :type="viewType"
    :syncNotice="syncNotice"
    @confirmSync="syncSettings" />
</template>

<script setup lang="ts">
  import SyncToOtherAccountSet from "./components/SyncToOtherAccountSet.vue";
  import type { IpagerSize, IStyle, IModuleData, ISavePrintData, ModuleType, IDragBoxOptions } from "./types";
  import { TextAlignmentType, VoucherPrintDataType, PrintSettingTypeEnum, PintSettingIdEnum } from "./types";
  import { isLemonClient } from "@/util/lmClient";
  import { useFullScreenStore } from "@/store/modules/fullScreen";
  import { printParams, getVoucherSeniorPrintInfo, type ISeniorPrintInfo } from "@/components/Voucher/utils";
  import { ElNotify } from "@/util/notify";
  import { ElAlert } from "@/util/confirm";
  import { globalWindowOpenPage, getUrlSearchParams, globalWindowOpen } from "@/util/url";
  import useBoxSelection from "@/hooks/useBoxSelection";
  import { useLoading } from "@/hooks/useLoading";
  import { useRefHistory } from "@vueuse/core";
  import { getKeyByValue, judgeOverFlow, pageSizeOptions, fontSizeList, canTextFitInDiv, zoomList } from "./utils";
  import { ref, toRef, computed, onMounted, onBeforeUnmount, onUnmounted, onActivated, onDeactivated, type Ref, nextTick } from "vue";
  import { request, type IResponseModel } from "@/util/service";
  import _ from "lodash";
  import { useRoute } from "vue-router";
  import { getGlobalToken } from "@/util/baseInfo";
  import { domToPng } from "modern-screenshot";
  import { useRouterArrayStoreHook } from "@/store/modules/routerArray";

  const syncDialogShow = ref(false);

  const routerArrayStore = useRouterArrayStoreHook();
  const route = useRoute();

  const viewType = route.path.split("/")[1] as keyof typeof PrintSettingTypeEnum;

  function getSettingType(isPreview: boolean = false) {
    if (viewType === "AccountBooks") {
      if (isPreview && printInfo.value.direction === "Z") {
        return PrintSettingTypeEnum.AccountBooksPreview;
      } else if (!isPreview && printInfo.value.direction === "Z") {
        return PrintSettingTypeEnum.AccountBooks;
      } else if (isPreview && printInfo.value.direction === "H") {
        return PrintSettingTypeEnum.AccountBookHorizontalBaseImagePreview;
      } else if (!isPreview && printInfo.value.direction === "H") {
        return PrintSettingTypeEnum.AccountBookHorizontalBaseImage;
      }
    }
    return isPreview ? PrintSettingTypeEnum.VoucherBasePreview : PrintSettingTypeEnum.VoucherBase;
  }

  function getSettingId(isPreview: boolean = false) {
    if (viewType === "AccountBooks") {
      return getSettingType(isPreview) + 1000;
    }
    return isPreview ? PintSettingIdEnum.VoucherBasePreview : PintSettingIdEnum.VoucherBase;
  }

  function getPrintType() {
    if (viewType === "AccountBooks" && printInfo.value.direction === "H") {
      return 9;
    }
    return printInfo.value.printType;
  }

  const syncNotice = computed(() => {
    if (viewType === "AccountBooks") {
      return [
        "1. 有对应账套明细账/总账/核算项目明细账打印权限时才可以进行同步",
        "2. 同步后对应账套的明细账/总账/核算项目明细账封面打印模板将更新",
      ];
    }
    return ["1. 有对应账套凭证打印权限时才可以进行同步", "2. 同步后对应账套的凭证高级打印模板将更新"];
  });
  // 同步设置
  function syncSettings(data: any) {
    loading.value = true;
    request({
      url:
        window.printHost +
        `/api/VoucherPrintSettings/Copy?settingType=${getSettingType()}&printType=${getPrintType()}` +
        (route.query.from ? "&from=" + route.query.from : ""),
      method: "post",
      data: data,
    })
      .then((res: any) => {
        if (res.state === 1000) {
          ElNotify({
            type: "success",
            message: `${viewType === "AccountBooks" ? "账簿" : ""}封面打印同步成功`,
          });
          syncDialogShow.value = false;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
  const isErp = ref(window.isErp);

  function helpManual() {
    let url = "";
    if (isErp.value) {
      url = "https://help.ningmengyun.com/#/yyc/HandleForYYC?subMenuId=*********";
    } else {
      url = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********";
    }
    globalWindowOpen(url);
  }
  function helpVideo() {
    let url = "";
    if (isErp.value) {
      url = "https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=*********";
    } else {
      url = "https://help.ningmengyun.com/#/jz/videoPlayer?qType=*********";
    }
    globalWindowOpen(url);
  }
  //打印模版数据
  const printInfo = ref({ ...getVoucherSeniorPrintInfo() });
  if (viewType === "AccountBooks") {
    printInfo.value = { printType: 6, direction: (route.query.direction as string) || "Z" };
  }
  const fullScreen = toRef(useFullScreenStore(), "fullScreen");

  const loading = ref(true);
  //工具map
  const typeSize = new Map([
    ["text", { width: 240, height: 30, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
    ["rectangle", { width: 100, height: 100, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
    ["img", { width: 150, height: 150, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
    ["line", { width: 200, height: 20, sticks: ["mr", "ml"] }],
  ]);
  // 监听shift是否按下
  const shiftKey = ref(false);
  const copyData = ref();
  const copyTime = ref(1);
  // 监听shift ctrl是否按下,并监听删除按钮
  function ctrlKeyChange() {
    document.onkeydown = function (e) {
      if (e.key === "Shift" && !e.altKey) {
        shiftKey.value = true;
      } else {
        shiftKey.value = false;
      }
      if (e.ctrlKey && e.key === "z") {
        // if (history.value.length > 2) {
        preHistory();
        return false;
        // }
      }
      if (e.ctrlKey && e.key === "y") {
        nextHistory();
        return false;
      }
      if (e.ctrlKey && e.key === "c") {
        const activeElement = document.activeElement;
        if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
          return;
        }
        copyTime.value = 1;
        copyData.value = _.cloneDeep(Array.from(activeEle.value));
        return false;
      }
      if (e.ctrlKey && e.key === "v") {
        const activeElement = document.activeElement;
        // 检查是否是 <input> 元素，且类型为 text 或 password
        if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
          return;
        }

        if (!copyData.value) return;
        copyData.value.forEach((item: any) => {
          addDragBox(
            item.type,
            {
              id: keyId.value++,
              top: item.top + 10 * copyTime.value,
              left: item.left + 10 * copyTime.value,
              height: item.height,
              width: item.width,
              positionType: item.positionType,
              label: item.label,
              content: item.text,
              dataSource: item.dataSource,
              style: { ...item.style },
            },
            false,
            false
          );
        });
        copyTime.value++;
        return false;
      }
      if (e.key === "Delete") {
        const lastBaxArr = coverData.value.filter((i) => !i.active);
        coverData.value = lastBaxArr;
        addHistory();
      }
      const target = e.target as HTMLElement;
      if (target && target.className !== "el-textarea__inner") {
        if (e.key === "ArrowUp" && Array.from(activeEle.value).length) {
          e.preventDefault();
          e.stopPropagation();
          activeEle.value.forEach((item: any) => {
            if (item.top > 1) item.top = item.top - 1;
          });
          addHistory();
        }
        if (e.key === "ArrowDown" && Array.from(activeEle.value).length) {
          e.preventDefault();
          e.stopPropagation();
          activeEle.value.forEach((item: any) => {
            if (item.top < getMainContentBoxDimension().height - 1) item.top = item.top + 1;
          });
          addHistory();
        }
        if (e.key === "ArrowLeft" && Array.from(activeEle.value).length) {
          e.preventDefault();
          e.stopPropagation();
          activeEle.value.forEach((item: any) => {
            if (item.left > 1) item.left = item.left - 1;
          });
          addHistory();
        }
        if (e.key === "ArrowRight" && Array.from(activeEle.value).length) {
          e.preventDefault();
          e.stopPropagation();
          activeEle.value.forEach((item: any) => {
            if (item.left < getMainContentBoxDimension().width - 1) item.left = item.left + 1;
          });
          addHistory();
        }
      }
    };
    document.onkeyup = function (e) {
      shiftKey.value = false;
    };
  }
  // mm转换为整数px
  function mmToPx(mm: number) {
    //1mm = 2.83pt 1pt = 1.33px 纸张给了pt只需要转化为pt
    return Math.round(mm * 2.83);
  }

  const moudleInputRef = ref<any>([]);
  //封面
  const coverData = ref<IModuleData[]>([]);
  const historyCoverData = ref<IModuleData[]>([]);
  // const { history, undo, redo, clear: historyClear } = useRefHistory(historyCoverData, { deep: true });

  //定义类型
  type CoordinateMaps = [Map<string, IModuleData[]>, Map<string, IModuleData[]>];
  const coverDataCoordinate: Ref<CoordinateMaps> = computed(() => {
    const rectangle = coverData.value.filter((i) => i.type === "rectangle");
    const coordinateY = new Map();
    const coordinateX = new Map();
    rectangle.forEach((item) => {
      if (!coordinateY.has(`${item.top}`)) {
        coordinateY.set(`${item.top}`, [item]);
      } else {
        const existingItems = coordinateY.get(`${item.top}`);
        existingItems.push(item); // 否则，将当前item添加到已有的数组中
        coordinateY.set(`${item.top}`, existingItems);
      }
      if (!coordinateY.has(`${item.top + item.height}`)) {
        coordinateY.set(`${item.top + item.height}`, [item]);
      } else {
        const existingItems = coordinateY.get(`${item.top + item.height}`);
        existingItems.push(item); // 否则，将当前item添加到已有的数组中
        coordinateY.set(`${item.top + item.height}`, existingItems);
      }
      if (!coordinateX.has(`${item.left}`)) {
        coordinateX.set(`${item.left}`, [item]);
      } else {
        const existingItems = coordinateX.get(`${item.left}`);
        existingItems.push(item); // 否则，将当前item添加到已有的数组中
        coordinateX.set(`${item.left}`, existingItems);
      }
      if (!coordinateX.has(`${item.left + item.width}`)) {
        coordinateX.set(`${item.left + item.width}`, [item]);
      } else {
        const existingItems = coordinateX.get(`${item.left + item.width}`);
        existingItems.push(item); // 否则，将当前item添加到已有的数组中
        coordinateX.set(`${item.left + item.width}`, existingItems);
      }
    });
    return [coordinateY, coordinateX];
  });

  function isIntersectOrTouch(
    A_x1: number,
    A_y1: number,
    A_width: number,
    A_height: number,
    B_x1: number,
    B_y1: number,
    B_width: number,
    B_height: number
  ) {
    if (A_x1 === B_x1 && A_y1 === B_y1 && A_width === B_width && A_height === B_height) {
      return false;
    }
    // 计算矩形A的右边界和底边界
    var A_x2 = A_x1 + A_width;
    var A_y2 = A_y1 + A_height;

    // 计算矩形B的右边界和底边界
    var B_x2 = B_x1 + B_width;
    var B_y2 = B_y1 + B_height;

    // 检查矩形A和B在x轴上是否有重叠或接触
    var xOverlapOrTouch = A_x1 <= B_x2 && A_x2 >= B_x1;

    // 检查矩形A和B在y轴上是否有重叠或接触
    var yOverlapOrTouch = A_y1 <= B_y2 && A_y2 >= B_y1;

    // 如果在x轴和y轴上都有重叠或接触，则两个矩形相交或接触
    return xOverlapOrTouch && yOverlapOrTouch;
  }

  interface IInterval {
    start: number;
    end: number;
    id: number[];
  }
  function judgeBorder(id: number, left: number, top: number, width: number, height: number) {
    const coverDataWithOutSelf = coverData.value.filter((i) => i.id !== id && i.type === "rectangle");
    let borderTop = null;
    let borderLeft = null;
    let borderBottom = null;
    let borderRight = null;
    if (coverDataCoordinate.value[0].has(`${top}`)) {
      const items =
        coverDataCoordinate.value[0]
          .get(`${top}`)
          ?.filter((i) => i.id !== id && isIntersectOrTouch(left, top, width, height, i.left, i.top, i.width, i.height)) || [];
      //形成x轴的区间
      items.sort((a, b) => a.left - b.left);

      const coordinateIntervals = items.reduce((intervals: IInterval[], current) => {
        // 如果区间数组为空，或者当前区间的起始点大于区间数组中最后一个区间的结束点，
        // 则直接将当前区间添加到区间数组中
        if (intervals.length === 0 || current.left > intervals[intervals.length - 1].end) {
          intervals.push({ start: current.left, end: current.left + current.width, id: [current.id] });
        } else {
          // 否则，如果当前区间与区间数组中最后一个区间相连或重叠，
          // 则合并这两个区间
          const lastInterval = intervals[intervals.length - 1];
          lastInterval.end = Math.max(lastInterval.end, current.left + current.width);
          lastInterval.id = lastInterval.id.concat(current.id);
        }
        return intervals;
      }, []);
      borderTop = coordinateIntervals.find((i) => {
        return (
          (i.start < left && i.end > left + width) ||
          (((i.start == left && i.end >= left + width) || (i.start <= left && i.end == left + width)) &&
            items.find((j) => j.left <= left && j.width >= width && j.top < top && j.top + j.height === top))
        );
      });
    }
    if (coverDataCoordinate.value[1].has(`${left}`)) {
      const items =
        coverDataCoordinate.value[1]
          .get(`${left}`)
          ?.filter((i) => i.id !== id && isIntersectOrTouch(left, top, width, height, i.left, i.top, i.width, i.height)) || [];
      //形成y轴的区间
      items.sort((a, b) => a.top - b.top);

      const coordinateIntervals = items.reduce((intervals: IInterval[], current) => {
        // 如果区间数组为空，或者当前区间的起始点大于区间数组中最后一个区间的结束点，
        // 则直接将当前区间添加到区间数组中
        if (intervals.length === 0 || current.top > intervals[intervals.length - 1].end) {
          intervals.push({ start: current.top, end: current.top + current.height, id: [current.id] });
        } else {
          // 否则，如果当前区间与区间数组中最后一个区间相连或重叠，
          // 则合并这两个区间
          const lastInterval = intervals[intervals.length - 1];
          lastInterval.end = Math.max(lastInterval.end, current.top + current.height);
          lastInterval.id = lastInterval.id.concat(current.id);
        }
        return intervals;
      }, []);

      borderLeft = coordinateIntervals.find((i) => {
        return (
          (i.start < top && i.end > top + height) ||
          (((i.start == top && i.end >= top + height) || (i.start <= top && i.end == top + height)) &&
            items.find((j) => j.top === top && j.height === height && j.left < left && j.left + j.width === left))
        );
      });
    }
    if (coverDataCoordinate.value[0].has(`${top + height}`)) {
      const items =
        coverDataCoordinate.value[0]
          .get(`${top + height}`)
          ?.filter((i) => i.id !== id && isIntersectOrTouch(left, top, width, height, i.left, i.top, i.width, i.height)) || [];
      //形成x轴的区间
      items.sort((a, b) => a.left - b.left);

      const coordinateIntervals = items.reduce((intervals: IInterval[], current) => {
        // 如果区间数组为空，或者当前区间的起始点大于区间数组中最后一个区间的结束点，
        // 则直接将当前区间添加到区间数组中
        if (intervals.length === 0 || current.left > intervals[intervals.length - 1].end) {
          intervals.push({ start: current.left, end: current.left + current.width, id: [current.id] });
        } else {
          // 否则，如果当前区间与区间数组中最后一个区间相连或重叠，
          // 则合并这两个区间
          const lastInterval = intervals[intervals.length - 1];
          lastInterval.end = Math.max(lastInterval.end, current.left + current.width);
          //合并id
          lastInterval.id = lastInterval.id.concat(current.id);
        }
        return intervals;
      }, []);
      const target = coordinateIntervals.find((i) => i.start <= left && i.end >= left + width);
      // console.log(target);
      if (target) {
        const topArr = target.id
          .map((i) =>
            coverDataWithOutSelf.find(
              (j) => j.id === i && j.top === top + height && j.left > left && j.width > width && j.left + j.width <= left + width
            )
          )
          .filter((item) => item !== undefined);

        if (topArr.length) {
          borderBottom = true;
        } else {
          borderBottom = false;
        }
      }
    }

    if (coverDataCoordinate.value[1].has(`${left + width}`)) {
      const items =
        coverDataCoordinate.value[1]
          .get(`${left + width}`)
          ?.filter((i) => i.id !== id && isIntersectOrTouch(left, top, width, height, i.left, i.top, i.width, i.height)) || [];
      //形成y轴的区间
      items.sort((a, b) => a.top - b.top);

      const coordinateIntervals = items.reduce((intervals: IInterval[], current) => {
        // 如果区间数组为空，或者当前区间的起始点大于区间数组中最后一个区间的结束点，
        // 则直接将当前区间添加到区间数组中
        if (intervals.length === 0 || current.top > intervals[intervals.length - 1].end) {
          intervals.push({ start: current.top, end: current.top + current.height, id: [current.id] });
        } else {
          // 否则，如果当前区间与区间数组中最后一个区间相连或重叠，
          // 则合并这两个区间
          const lastInterval = intervals[intervals.length - 1];
          lastInterval.end = Math.max(lastInterval.end, current.top + current.height);
          lastInterval.id = lastInterval.id.concat(current.id);
        }
        return intervals;
      }, []);
      const target = coordinateIntervals.find((i) => i.start < left && i.end > left + width);
      if (target) {
        const topArr = target.id
          .map((i) => coverDataWithOutSelf.find((j) => j.id === i && j.left === left + width && j.top <= top && j.height >= height))
          .filter((item) => item !== undefined);
        if (topArr.length) {
          borderRight = false;
        } else {
          borderRight = true;
        }
      }
      // borderRight = coordinateIntervals.find((i) => i.start <= top && i.end >= top + height);
    }

    let shouldHideBorderTop = borderTop;
    let shouldHideBorderLeft = borderLeft;
    let shouldHideBorderBottom = borderBottom;
    let shouldHideBorderRight = borderRight;
    return {
      borderTop: shouldHideBorderTop ? "none" : "1px solid #999",
      borderLeft: shouldHideBorderLeft ? "none" : "1px solid #999",
      borderBottom: shouldHideBorderBottom ? "none" : "1px solid #999",
      borderRight: shouldHideBorderRight ? "none" : "1px solid #999",
    };
  }

  interface IResult {
    module: IModuleData | null;
    index: number;
  }

  //纸张大小
  const pagerSizeList = ref(_.cloneDeep(pageSizeOptions));

  const leftNavShow = ref(true);
  const rightNavShow = ref(true);
  // 字体类型
  const selectFontType = ref("SimSun");
  const fontTypeList = ref([
    { label: "宋体", value: "SimSun", style: "SimSun" },
    { label: "楷体", value: "KaiTi", style: "KaiTi" },
    { label: "黑体", value: "SimHei", style: "SimHei" },
  ]);

  const selectStyleList = ref<IStyle[]>([]);

  // 字体大小列表
  const selectFontSize = ref(10);

  const barCanEdit = computed(() => {
    return selectStyleList.value.length > 0;
  });
  const isBold = ref(false);
  const isUnderline = ref(false);
  const isItalic = ref(false);
  const selectTextAlign = ref("");
  const selectWhiteSpace = ref("");
  const selectBoxAlign = ref(new Set());
  // 选择需要修改的样式组件
  function selectStyle(data: IStyle) {
    // 当按住shift时为多选
    if (shiftKey.value) {
      data.isSelected = true;
      selectStyleList.value.push(data);
    } else {
      selectStyleList.value.forEach((i) => (i.isSelected = false));
      data.isSelected = true;
      selectStyleList.value = [data];
    }
    selectFontType.value = data.fontFamily;
    selectFontSize.value = data.fontSize;
    isBold.value = data.fontWeight === "bold";
    isUnderline.value = data.textDecoration === "underline";
    isItalic.value = data.fontStyle === "italic";
    selectTextAlign.value = data.textAlign;
    if (data.shadowWhiteSpace) {
      selectWhiteSpace.value = data.shadowWhiteSpace;
    } else {
      selectWhiteSpace.value = data.whiteSpace;
    }
    rightNavShow.value = true;
  }

  // 改变字体样式
  function changeBaseStyle(type: string, val: boolean | string | number) {
    if (val === "Reduce") {
      const activeEle = coverData.value.filter((i) => i.active);
      if (activeEle.length) {
        activeEle.forEach((item: IModuleData) => {
          item.style.shadowFontSize = item.style.fontSize;
          item.style.shadowWhiteSpace = "Reduce";
          if (!canTextFitInDiv(item.label, item.style.fontSize, item.width, item.height)) {
            item.style.fontSize = 7;
            item.style.whiteSpace = "Wrap";
          }
        });
      }
    } else {
      selectStyleList.value.forEach((i) => (i[type] = val));
    }
    addHistory();
  }
  // 改变字体粗细
  function changeBold(type: boolean) {
    if (!barCanEdit.value) return;
    isBold.value = type;
    changeBaseStyle("fontWeight", type ? "bold" : "normal");
  }
  // 改变字体下划线
  function changeUnderline(type: boolean) {
    if (!barCanEdit.value) return;
    isUnderline.value = type;
    changeBaseStyle("textDecoration", type ? "underline" : "none");
  }
  // 改变字体斜体
  function changeItalic(type: boolean) {
    if (!barCanEdit.value) return;
    isItalic.value = type;
    changeBaseStyle("fontStyle", type ? "italic" : "normal");
  }
  // 改变文本对齐方式
  function changeTextAlign(type: string) {
    if (!barCanEdit.value) return;
    selectTextAlign.value = type;
    changeBaseStyle("textAlign", type);
  }
  // 改变文本超长处理方式
  function changeWhiteSpace(type: string) {
    if (!barCanEdit.value) return;
    selectWhiteSpace.value = type;
    changeBaseStyle("whiteSpace", type);
  }
  // 改变控件对齐方式
  function changeBoxAlign(type: string) {
    selectBoxAlign.value.add(type);
    const { width: mainWidth, height: mainHeight } = getMainContentBoxDimension();
    let maxLeft: number;
    let maxRight: number;
    let maxTop: number;
    let maxBottom: number;
    let isActiveBoxArr = [
      ...coverData.value.filter((i) => {
        if (i.active) {
          maxLeft = maxLeft === undefined || maxLeft >= i.left ? i.left : maxLeft;
          maxRight = maxRight === undefined || maxRight <= i.left + i.width ? i.left + i.width : maxRight;
          maxTop = maxTop === undefined || maxTop >= i.top ? i.top : maxTop;
          maxBottom = maxBottom === undefined || maxBottom <= i.top + i.height ? i.top + i.height : maxBottom;
        }
        return i.active;
      }),
    ];
    if (isActiveBoxArr.length === 0) return;
    const isOne = isActiveBoxArr.length === 1;
    switch (type) {
      case "left":
        isActiveBoxArr.forEach((box) => {
          box.left = isOne ? 0 : maxLeft;
        });
        break;
      case "right":
        isActiveBoxArr.forEach((box) => {
          box.left = isOne ? mainWidth - box.width : maxRight - box.width;
        });
        break;
      case "xCenter":
        isActiveBoxArr.forEach((item) => {
          let width = item.width;
          let left = maxLeft + Math.round((maxRight - maxLeft - width) / 2);
          item.left = isOne ? Math.round((mainWidth - width) / 2) : left;
        });
        break;
      case "top":
        isActiveBoxArr.forEach((box) => {
          box.top = isOne ? 0 : maxTop;
        });
        break;
      case "bottom":
        isActiveBoxArr.forEach((box) => {
          box.top = isOne ? mainHeight - box.height : maxBottom - box.height;
        });
        break;
      case "yCenter":
        isActiveBoxArr.forEach((item) => {
          let height = item.height;
          let top = maxTop + Math.round((maxBottom - maxTop - height) / 2);
          item.top = isOne ? Math.round((mainHeight - height) / 2) : top;
        });
        break;
      default:
        break;
    }
    addHistory();
  }

  //历史记录
  const historyArr = ref<IHistory[]>([]);
  const historyIndex = ref(-1);
  const ptLayout = ref();
  interface IHistory {
    data: IModuleData[];
    printInfo: ISeniorPrintInfo;
  }
  function addHistory() {
    const cloneSeniorModuleData = _.cloneDeep(coverData.value);
    const history = {
      data: cloneSeniorModuleData,
      printInfo: _.cloneDeep(printInfo.value),
    };

    historyArr.value.splice(historyIndex.value + 1, historyArr.value.length - historyIndex.value - 1, history);
    historyIndex.value++;
    historyCoverData.value = _.cloneDeep(historyArr.value[historyIndex.value].data);
  }
  function preHistory() {
    if (historyIndex.value < 0) return;
    historyIndex.value--;
    const clonePtLayout = _.cloneDeep(ptLayout.value);
    const historyJosn = historyIndex.value == -1 ? clonePtLayout : historyArr.value[historyIndex.value];
    coverData.value = _.cloneDeep(historyJosn.data);
    printInfo.value = _.cloneDeep(historyJosn.printInfo);
    refreshId.value++;
    // if (historyCoverData.value.length < 2) return;
    // undo();
    // coverData.value = _.cloneDeep(historyCoverData.value);
    // historyArr.value.push("1");
  }
  // const historyArr = ref<string[]>([]);
  function nextHistory() {
    if (historyArr.value.length === 0 || historyIndex.value === historyArr.value.length - 1) return;
    historyIndex.value++;
    const historyJosn = historyArr.value[historyIndex.value];
    coverData.value = _.cloneDeep(historyJosn.data);
    printInfo.value = _.cloneDeep(historyJosn.printInfo);
    refreshId.value++;
    // redo();
    // coverData.value = _.cloneDeep(historyCoverData.value);
    // historyArr.value.pop();
  }

  // 当前选择的纸张宽高
  const zoom = ref(1.3);
  const selectPagerSize: Ref<IpagerSize> = computed(() => {
    return pagerSizeList.value.find((item) => item.value === printInfo.value.printType)!;
  });
  const getPagerWH = computed(() => {
    const defaultVertical = [1, 2, 6].includes(printInfo.value.printType);
    const isVertical = printInfo.value.direction === "Z" && defaultVertical;
    return isVertical
      ? { width: `${selectPagerSize.value.width * zoom.value}px`, height: `${selectPagerSize.value.height * zoom.value}px` }
      : { width: `${selectPagerSize.value.height * zoom.value}px`, height: `${selectPagerSize.value.width * zoom.value}px` };
  });
  // 当前选择的纸张内容宽高
  const getPageContentWH = computed(() => {
    const defaultVertical = [1, 2, 6].includes(printInfo.value.printType);
    const isVertical = printInfo.value.direction === "Z" && defaultVertical;
    return isVertical
      ? {
          width: `${selectPagerSize.value.width * zoom.value}px`,
          height: `${selectPagerSize.value.height * zoom.value}px`,
        }
      : defaultVertical
      ? {
          width: `${selectPagerSize.value.height * zoom.value}px`,
          height: `${selectPagerSize.value.width * zoom.value}px`,
        }
      : {
          width: `${selectPagerSize.value.height * zoom.value}px`,
          height: `${selectPagerSize.value.width * zoom.value}px`,
        };
  });

  //生成样式对象
  function getStyle(item: IStyle, height = 30) {
    const style: { [key: string]: string } = {
      height: `${height}px`,
      display: "flex",
      alignItems: "center",
      fontFamily: fontTypeList.value.find((i) => i.value === item.fontFamily)!.style + " !important",
      fontSize: `${item.fontSize * zoom.value}px`,
      fontWeight: item.fontWeight,
      fontStyle: item.fontStyle,
      textDecoration: item.textDecoration,
      whiteSpace: item.whiteSpace == "Wrap" ? "normal" : "nowrap",
      wordBreak: item.whiteSpace == "Wrap" ? "break-all" : "normal",
      //是否有折行显示需要
      overflow: item.whiteSpace == "Wrap" ? "hidden" : "hidden",
      textAlign: item.textAlign,
      textAlignLast: item.textAlign,
      justifyContent: item.textAlign == "center" ? "center" : item.textAlign == "right" ? "flex-end" : "flex-start",
    };
    if (item.isSelected) {
      const color = isErp.value ? "rgba(61, 127, 255, 0.1)" : "rgba(148, 196, 148, 0.1)";
      style.backgroundColor = color;
    }
    return style;
  }

  function bodyDown(event: any, data: any) {
    if (!shiftKey.value) {
      deselect(event);
      if (!data.active) {
        coverData.value.forEach((data: any) => {
          data.active = false;
        });
        data.active = true;
      }
    } else {
      if (data.active) {
        activeEle.value.delete(data);
      } else {
        activeEle.value.add(data);
      }
      data.active = !data.active;
    }
  }
  const isDrag = ref(true);
  function allowDrop(event: DragEvent) {
    event.preventDefault();
  }
  const xline = ref(-1);
  const yline = ref(-1);
  const canLine = ref(false);
  let diff = 2; //吸附距离
  let startDrag = ref(0);
  function createLine(dragdata: any, lines: string[] = []) {
    const selfX: number[] = [];
    const selfY: number[] = [];
    coverData.value
      .filter((i) => i.type === "rectangle" && i.id !== dragdata.id)
      .forEach((item) => {
        //左辅助线
        if (lines.length === 0) {
          if (Math.abs(item.left - dragdata.left) <= diff) {
            selfY.push(item.left);
            // yline.value = item.left;
          }
          if (Math.abs(item.left + item.width - dragdata.left) <= diff) {
            selfY.push(item.left + item.width);
            // yline.value = item.left + item.width;
          }
          if (Math.abs(dragdata.left + dragdata.width - item.left) <= diff) {
            selfY.push(item.left);
            // xline.value = item.top;
          }
          if (item.top === dragdata.top) {
            selfX.push(item.top);
            // xline.value = item.top;
          }
          if (Math.abs(item.top + item.height - dragdata.top) <= diff) {
            selfX.push(item.top + item.height);
            // xline.value = item.top + item.height;
          }
          if (Math.abs(dragdata.top + dragdata.height - item.top) <= diff) {
            selfX.push(item.top);
            // xline.value = item.top;
          }
          return;
        }
        if (lines.includes("left")) {
          if (Math.abs(item.left - dragdata.left) <= diff) {
            selfY.push(item.left);
            // yline.value = item.left;
          }
          if (Math.abs(item.left + item.width - dragdata.left) <= diff) {
            selfY.push(item.left + item.width);
            // yline.value = item.left + item.width;
          }
          // if (Math.abs(dragdata.left + dragdata.width - item.left) <= diff) {
          //     selfY.push(item.left);
          //     // xline.value = item.top;
          // }
        }
        if (lines.includes("top")) {
          //上辅助线
          if (item.top === dragdata.top) {
            selfX.push(item.top);
            // xline.value = item.top;
          }
          if (Math.abs(item.top + item.height - dragdata.top) <= diff) {
            selfX.push(item.top + item.height);
            // xline.value = item.top + item.height;
          }
          if (Math.abs(dragdata.top + dragdata.height - item.top) <= diff) {
            selfX.push(item.top);
            // xline.value = item.top;
          }
        }
        if (lines.includes("right")) {
          //右辅助线
          if (Math.abs(item.left - dragdata.left - dragdata.width) <= diff) {
            selfY.push(item.left);
          }
          if (Math.abs(item.left + item.width - dragdata.left - dragdata.width) <= diff) {
            selfY.push(item.left + item.width);
          }
          // if (Math.abs(dragdata.left - item.left - item.width) <= diff) {
          //     console.log('item.left3',item.left);
          //     selfY.push(item.left);
          // }
        }
        if (lines.includes("bottom")) {
          //下辅助线
          if (Math.abs(item.top - dragdata.top - dragdata.height) <= diff) {
            selfX.push(item.top);
          }
          if (Math.abs(item.top + item.height - dragdata.top - dragdata.height) <= diff) {
            selfX.push(item.top + item.height);
          }
          // if (Math.abs(dragdata.top - item.top - item.height) <= diff) {
          //     selfX.push(item.top);
          // }
        }
      });
    // //吸附
    if (selfY.length > 0) {
      yline.value = Math.min(...selfY);
    } else {
      yline.value = -1;
    }
    if (selfX.length > 0) {
      xline.value = Math.min(...selfX);
    } else {
      xline.value = -1;
    }
    const { left, top } = absorb(dragdata.left, dragdata.top, dragdata);
    dragdata.left = left !== -1 ? left : dragdata.left;
    dragdata.top = top !== -1 ? top : dragdata.top;
  }
  function absorb(left: number, top: number, item: any) {
    let hasx = false;
    let hasy = false;
    if (canLine.value) {
      coverData.value
        .filter((i) => i.type === "rectangle" && i.id !== item.id)
        .forEach((item) => {
          if (Math.abs(item.left - left) <= diff) {
            hasx = true;
            left = item.left;
          }
          if (Math.abs(item.left + item.width - left) <= diff) {
            hasx = true;
            left = item.left + item.width;
          }
          if (Math.abs(item.top - top) <= diff) {
            hasy = true;
            top = item.top;
          }
          if (Math.abs(item.top + item.height - top) <= diff) {
            hasy = true;
            top = item.top + item.height;
          }
        });
    }
    return { left: hasx ? left : -1, top: hasy ? top : -1 };
  }
  let restoreDrag = { x: 0, y: 0, restoreId: -1 };
  let restoreResize = { x: 0, y: 0, width: 0, height: 0, restoreId: -1 };
  let dragStartTime = 0;
  let dragEndTime = 0;
  function mouduleDrag(event: any, type: string, item?: any) {
    if (type === "drag-start") {
      dragStartTime = new Date().getTime();
      restoreDrag.x = event.x;
      restoreDrag.y = event.y;
      restoreDrag.restoreId = item.id;
      canLine.value = true;
      startDrag.value = new Date().getTime();
    } else if (type === "resize-start") {
      restoreResize.x = event.x;
      restoreResize.y = event.y;
      restoreResize.width = event.w;
      restoreResize.height = event.h;
      restoreResize.restoreId = item.id;
    } else if (type === "dragging") {
      const { x, y } = event;
      if (activeEle.value.size) {
        const { mainWidth, mainHeight } = getPagerWhNumber();
        Array.from(activeEle.value)
          .filter((i: any) => i.id !== item.id)
          .forEach((data: any) => {
            const oldData = historyCoverData.value.find((i) => i.id === data.id)!;
            let clientX = oldData.left + x - restoreDrag.x;
            let clientY = oldData.top + y - restoreDrag.y;
            clientX = clientX < 0 ? 0 : clientX;
            clientY = clientY < 0 ? 0 : clientY;
            clientX = clientX > mainWidth ? mainWidth : clientX;
            clientY = clientY > mainHeight ? mainHeight : clientY;
            data.left = clientX;
            data.top = clientY;
          });
      }
      if (item && item.left < 0) {
        isDrag.value = false;
        item.left = 0;
        return false;
      }
      if (new Date().getTime() - startDrag.value < 100) return;
      xline.value = -1;
      yline.value = -1;
      if (Array.from(activeEle.value).length > 1) {
        return;
      }
      if (item.type === "rectangle") createLine(item);
    } else if (type === "resizing") {
      const line = [];
      if (event.w !== restoreResize.width) {
        if (event.x === restoreResize.x && event.y === restoreResize.y && event.h === restoreResize.height) {
          line.push("right");
        } else if (event.x !== restoreResize.x && event.y === restoreResize.y && event.h === restoreResize.height) {
          line.push("left");
        } else if (event.x === restoreResize.x && event.y !== restoreResize.y && event.h !== restoreResize.height) {
          line.push("right");
        } else if (event.x === restoreResize.x && event.y === restoreResize.y && event.h !== restoreResize.height) {
          line.push("right");
        } else if (event.x !== restoreResize.x && event.y !== restoreResize.y && event.h !== restoreResize.height) {
          line.push("left");
        } else if (event.x !== restoreResize.x && event.y === restoreResize.y && event.h !== restoreResize.height) {
          line.push("left");
        }
      }
      if (event.h !== restoreResize.height) {
        if (event.x === restoreResize.x && event.y === restoreResize.y && event.w === restoreResize.width) {
          line.push("bottom");
        } else if (event.x === restoreResize.x && event.y !== restoreResize.y && event.w === restoreResize.width) {
          line.push("top");
        } else if (event.x === restoreResize.x && event.y !== restoreResize.y && event.w !== restoreResize.width) {
          line.push("top");
        } else if (event.x === restoreResize.x && event.y === restoreResize.y && event.w !== restoreResize.width) {
          line.push("bottom");
        } else if (event.x !== restoreResize.x && event.y !== restoreResize.y && event.w !== restoreResize.width) {
          line.push("top");
        } else if (event.x !== restoreResize.x && event.y === restoreResize.y && event.w !== restoreResize.width) {
          line.push("bottom");
        }
      }
      if (item.type === "rectangle") createLine(item, line);
    } else if (type == "drag-end" || type == "resize-end") {
      dragEndTime = new Date().getTime();
      if (dragEndTime - dragStartTime < 100) {
        const targetItem = historyCoverData.value.find((i) => i.id === item.id);
        if (targetItem) {
          const { left, top } = targetItem;
          Object.assign(item, { left, top });
        }
        return false;
      } else {
        //吸附
        if (Array.from(activeEle.value).length > 1) {
          xline.value = -1;
          yline.value = -1;
          addHistory();
          return;
        }
        xline.value = -1;
        yline.value = -1;
        const { left, top } = absorb(item.left, item.top, item);
        item.left = left !== -1 ? left : item.left;
        item.top = top !== -1 ? top : item.top;
        canLine.value = false;
        addHistory();
      }
    }
  }

  const activeEle = ref(new Set());
  function activeDrag(event: any, item: any, type: string) {
    if (!shiftKey.value) {
      activeEle.value.clear();
    }
    if (type === "unTable") {
      setTimeout(() => {
        coverData.value.forEach((data: any) => {
          if (data.active) {
            activeEle.value.add(data);
          }
        });
      });
    } else {
      ///
    }
  }
  function deactivated(event: any, item: any) {
    item.inputable = false;
    if (shiftKey.value) {
      setTimeout(() => {
        item.style.isSelected = false;
        item.active = false;
      });
      activeEle.value.forEach((data: any) => {
        if (data.id !== item.id) {
          data.active = true;
        }
      });
    } else {
      item.active = false;
      item.style.isSelected = false;
      activeEle.value.delete(item);
    }
  }
  function dbClick(item: any) {
    item.inputable = true;
  }
  function blur(item: any) {
    item.inputable = false;
  }

  function caseOnDrop(item: any) {
    if (item.type !== "textNoLabel") {
      item.inputable = true;
      nextTick(() => {
        if (item.type === "text") {
          moudleInputRef.value[item.id].focus();
        }
      });
    }
  }

  function getMainBoxDimension() {
    const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    return {
      height: mainDragBox.offsetHeight,
      width: mainDragBox.offsetWidth,
    };
  }
  function getMainContentBoxDimension() {
    const mainDragBox = document.querySelector(".main-drag-content") as HTMLElement;
    return {
      height: mainDragBox.offsetHeight,
      width: mainDragBox.offsetWidth,
    };
  }
  function getPagerWhNumber() {
    let { width, height } = getPageContentWH.value;
    let mainWidth = 0;
    let mainHeight = 0;
    mainWidth = Number(width.replace("px", ""));
    mainHeight = Number(height.replace("px", ""));
    // if (printInfo.value.direction === "H") {
    //     [mainWidth, mainHeight] = [mainHeight, mainWidth];
    // }
    return { mainWidth, mainHeight };
  }
  function changeDirection() {
    refreshId.value++;
    get(false, false);
    // setDragSize();
  }
  //根据选择的纸张大小和方向改变拖拽盒子限制
  function setDragSize() {
    let width = Number(getPageContentWH.value.width.replace("px", ""));
    let height = Number(getPageContentWH.value.height.replace("px", ""));
    coverData.value.forEach((item) => {
      if (item.width > width) {
        item.width = width;
        item.left = 0;
      }
      if (item.height > height) {
        item.height = height;
        item.top = 0;
      }
      if (item.left + item.width > width) {
        item.left = width - item.width;
      }
      if (item.top + item.height > height) {
        item.top = height - item.height;
      }
    });
    addHistory();
  }
  // 返回一个默认样式
  function getBaseStyle(): IStyle {
    return {
      fontFamily: "SimSun",
      fontSize: 10,
      fontWeight: "normal",
      fontStyle: "normal",
      textDecoration: "none",
      whiteSpace: "nowrap",
      wordBreak: "normal",
      overflow: "hidden",
      textAlign: "left",
      justifyContent: "flex-start",
      isSelected: false,
    };
  }
  const keyId = ref(24);
  // 添加一个可拖拽盒子
  function addDragBox(
    type: ModuleType,
    { height, width, id, positionType, top, left, label, content, placeholder, style, columnName, dataSource }: IDragBoxOptions,
    needAddhistory = false,
    isAddByMoudle = false,
    isKeep = false
  ) {
    const { mainWidth, mainHeight } = getPagerWhNumber();
    width = width || typeSize.get(type)!.width;
    height = height || typeSize.get(type)!.height;
    if (isAddByMoudle) {
      // if (left > mainWidth - width - marginRight) {
      //     left = mainWidth - width - marginRight;
      // }
      // if (top > mainHeight - height - marginBottom) {
      //     top = mainHeight - height - marginBottom;
      // }
    } else if (!left && !top && coverData.value.length > 0) {
      let maxLeft = -Infinity;
      let maxTop = -Infinity;
      let headResult: IModuleData | null = null as IModuleData | null;
      coverData.value.forEach((obj: any) => {
        if (obj.top + obj.height > maxTop) {
          maxLeft = obj.left + obj.width;
          maxTop = obj.top + obj.height;
          headResult = obj;
        } else if (obj.top + obj.height === maxTop && obj.left + obj.width > maxLeft) {
          maxTop = obj.top + obj.height;
          headResult = obj;
        }
      });
      if (headResult !== null) {
        left = headResult.left + headResult.width + 10;
        if (left > mainWidth - width) {
          left = 0;
          top = headResult.top + headResult.height + 10;
        } else {
          top = headResult.top;
        }
        left = Math.min(left, mainWidth - width);
        top = Math.min(top, mainHeight - height);
      } else {
        left = 0;
        top = 0;
      }
    } else {
      left = Math.min(left, mainWidth - width);
      top = Math.min(top, mainHeight - height);
    }
    let sticks = typeSize.get(type)!.sticks;
    let dargBox = {
      active: true,
      height,
      text: content ?? "",
      label: label ?? "",
      inputable: type === "text" && dataSource === undefined && !isKeep,
      id: id ?? keyId.value++,
      dataSource: dataSource || "",
      positionType: 1,
      left: left,
      style: style || getBaseStyle(),
      top: top,
      type: type,
      sticks,
      width,
      index: 1,
    };
    coverData.value.push(dargBox as IModuleData);
    addHistory();
    nextTick(() => {
      if (type === "text" && typeof dargBox.dataSource === "string" && dargBox.dataSource === "" && !isKeep) {
        moudleInputRef.value[dargBox.id].focus();
      }
    });
  }
  function dblAddModule(type: ModuleType, e: any) {
    if (e.target.className === "main-drag-content") {
      navSetting(type, e);
    }
  }
  function navSetting(type: ModuleType, e?: any, dataSource?: ArrayBuffer | string) {
    if (!e) {
      addDragBox(type, { top: 0, left: 0, dataSource }, true);
      return;
    }
    if (!limitLocation(e)) return;
    let mainDragBox = document.querySelector(".main-drag-box") as Element;
    const rect = mainDragBox.getBoundingClientRect();
    // 计算鼠标相对于元素的位置
    const x = Math.round(e.clientX - rect.left);
    const y = Math.round(e.clientY - rect.top);
    const { width: mainWidth, height: mainHeight } = getMainBoxDimension();
    if (x < mainWidth && x > 0 && y < mainHeight && y > 0) {
      addDragBox(type, { top: y, left: x, dataSource }, true);
    }
  }

  function limitLocation(e: any) {
    let mainDragBox = document.querySelector(".main-drag-box") as Element;
    const rect = mainDragBox.getBoundingClientRect();
    // 计算鼠标相对于元素的位置
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const { width: mainWidth, height: mainHeight } = getMainBoxDimension();
    if (x > mainWidth || y > mainHeight || x < 0 || y < 0) {
      return false;
    }
    return true;
  }
  const addImgTarget = ref(null);
  function addImg(e?: any) {
    addImgTarget.value = e || null;
    if (e && !limitLocation(e)) return;
    const input = document.getElementById("selfSealInput");
    input!.click();
  }
  function handleFileSelect(event: any) {
    var file = event.target.files[0]; // 获取选择的文件
    if (!file) return;
    const acceptedImageTypes = ["image/jpeg", "image/png", "image/webp", "image/svg+xml", "image/bmp"];
    if (!acceptedImageTypes.includes(file.type)) {
      ElNotify({
        type: "warning",
        message: "请选择为图像类型的文件",
      });
      return;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      ElNotify({
        type: "warning",
        message: "上传图片大小不能超过 2MB!",
      });
      return;
    }
    if (file) {
      var reader = new FileReader(); // 创建FileReader对象
      reader.onload = function (e) {
        //ArrayBuffer Blob
        navSetting("img", addImgTarget.value, e.target!.result as ArrayBuffer);
        event.target.value = "";
      };

      reader.readAsDataURL(file); // 读取文件内容并触发onload事件
    }
  }
  // 框选功能
  function boxSelectCallback({ width, height, left, top }: { width: number; height: number; left: number; top: number }) {
    // 将dragBoxArr中控件位置在盒子内的控件选中
    coverData.value.forEach((item: any) => {
      if (item.left >= left && item.left <= left + width && item.top >= top && item.top <= top + height) {
        item.active = true;
        item.style.isSelected = true;
        selectStyleList.value.push(item.style);
      } else {
        item.active = false;
        item.style.isSelected = false;
      }
    });
    if (selectStyleList.value.length > 0) {
      const {
        fontFamily: newFontFamily,
        fontSize: newFontSize,
        fontWeight: newIsBold,
        textDecoration: newIsUnderline,
        fontStyle: newIsItalic,
        textAlign: newTextAlign,
        whiteSpace: newWhiteSpace,
      } = selectStyleList.value[0];

      selectFontType.value = newFontFamily;
      selectFontSize.value = newFontSize;
      isBold.value = newIsBold === "bold";
      isUnderline.value = newIsUnderline === "underline";
      isItalic.value = newIsItalic === "italic";
      selectTextAlign.value = newTextAlign;
      selectWhiteSpace.value = newWhiteSpace;
    }
  }

  function addEvents(events: Map<string, EventListenerOrEventListenerObject>, element: Element) {
    events.forEach((listener, eventName) => {
      element.addEventListener(eventName, listener);
    });
  }
  function deselect(e: any) {
    if ((e && e.target.className.indexOf("main-drag-box") > -1) || e.target.className.indexOf("main-drag-content") > -1) {
      activeEle.value.clear();
      coverData.value.forEach((data: any) => {
        data.active = false;
      });
      selectStyleList.value.forEach((i: any) => {
        i.isSelected = false;
      });
      selectStyleList.value = [];

      return;
    }
  }
  function get(useDefault = false, needTemp = true) {
    loading.value = true;
    let requestUrl = window.printHost + `/api/VoucherPrintSettings/ByType?settingType=${getSettingType()}&printType=${getPrintType()}`;
    if (useDefault) {
      requestUrl = window.printHost + `/api/VoucherPrintSettings/DefaultByType?settingType=${getSettingType()}&printType=${getPrintType()}`;
    }
    request({
      url: requestUrl,
      method: useDefault || viewType !== "AccountBooks" ? "GET" : "POST",
    }).then((res: any) => {
      if (res.state === 1000) {
        let maxId = -1;
        //排除封面图片
        coverData.value = [];
        //处理历史数据
        if (
          res.data.printData[res.data.printData.length - 1].width !== selectPagerSize.value.width &&
          res.data.printData[res.data.printData.length - 1].height !== selectPagerSize.value.height &&
          res.data.printData[res.data.printData.length - 1].width !== selectPagerSize.value.height &&
          res.data.printData[res.data.printData.length - 1].height !== selectPagerSize.value.width
        ) {
          zoom.value = 1.3;
        } else {
          zoom.value = 1;
        }
        nextTick(() => {
          coverData.value = res.data.printData
            .filter((i: ISavePrintData) => i.dataType !== VoucherPrintDataType.coverImg)
            .map((item: any) => {
              if (item.id > maxId) {
                maxId = item.id;
              }
              const { dataType, fontFamily, fontSize, isBold, isItalic, textAlign, justifyContent, overflow, isUnderLine, ...data } = item;
              data.style.isSelected = false;

              return {
                ...data,
                type: getKeyByValue(dataType),
                active: false,
                inputable: false,
              };
            });
          if (needTemp) {
            ptLayout.value = {
              data: _.cloneDeep(coverData.value),
              printInfo: _.cloneDeep(printInfo.value),
            };
            historyCoverData.value = _.cloneDeep(ptLayout.value.data);
          } else {
            addHistory();
          }

          keyId.value = ++maxId;
        });

        loading.value = false;
        if (firstLoading.value) {
          firstLoading.value = false;
          nextTick(() => {
            initEvents();
          });
        }
      }
    });
  }
  get();

  const isSavingPng = ref(false);
  function save(settingsId: number, settingType: number, callBack?: Function) {
    isSavingPng.value = true;
    coverData.value.forEach((item) => {
      item.active = false;
    });
    useLoading().enterLoading("努力保存中，请稍候...");
    const node = document.getElementById("cover-node") as HTMLElement;
    const topLeftSubscript = document.querySelector(".top-left-subscript") as HTMLElement;
    const topRightSubscript = document.querySelector(".top-right-subscript") as HTMLElement;
    const bottomLeftSubscript = document.querySelector(".bottom-left-subscript") as HTMLElement;
    const bottomRightSubscript = document.querySelector(".bottom-right-subscript") as HTMLElement;
    //判断是否有元素超出页面
    const { mainWidth, mainHeight } = getPagerWhNumber();
    const overflowData = coverData.value.find((item) => {
      if (item.left + item.width > mainWidth || item.top + item.height > mainHeight || item.left < 0 || item.top < 0) {
        return item;
      }
    });
    if (overflowData) {
      ElNotify({
        type: "warning",
        message: "有元素超出页面，请调整后再保存",
      });
      isSavingPng.value = false;
      useLoading().quitLoading();
      return;
    }
    domToPng(node, {
      scale: 4,
      width: node.offsetWidth,
      backgroundColor: "#ffffff",
      height: node.offsetHeight,
      filter: (el) => {
        if (el === topLeftSubscript || el === topRightSubscript || el === bottomLeftSubscript || el === bottomRightSubscript) {
          return false;
        } else {
          return true;
        }
      },
    }).then((dataUrl) => {
      isSavingPng.value = false;
      const printData: ISavePrintData[] = coverData.value.map((item) => {
        const { type, active, sticks, inputable, ...data } = item;
        return {
          ...data,
          dataType: VoucherPrintDataType[type],
          fontFamily: item.style.fontFamily,
          fontSize: item.style.fontSize,
          isBold: item.style.fontWeight === "bold",
          isItalic: item.style.fontStyle === "italic",
          textAlign: TextAlignmentType[item.style.textAlign],
          justifyContent: 0,
          overflow: judgeOverFlow(item.style.whiteSpace, item.style.shadowWhiteSpace),
          isUnderLine: item.style.textDecoration === "underline",
        };
      });
      printData.push({
        id: keyId.value++,
        label: "",
        text: "",
        dataSource: dataUrl,
        left: 0,
        top: 0,
        width: node.offsetWidth,
        height: node.offsetHeight,
        dataType: VoucherPrintDataType.coverImg,
        fontFamily: "",
        fontSize: 0,
        isBold: false,
        isItalic: false,
        textAlign: 0,
        justifyContent: 0,
        overflow: 0,
        positionType: 1,
        style: {
          fontFamily: "",
          fontSize: 0,
          fontWeight: "",
          fontStyle: "",
          textDecoration: "",
          whiteSpace: "",
          wordBreak: "",
          overflow: "",
          textAlign: "left",
          justifyContent: "",
          isSelected: false,
          shadowFontSize: 0,
          shadowWhiteSpace: "",
        },
        index: 1,
      });
      const data = {
        settingsId: settingsId,
        settingType: settingType,
        isPrintTitlePage: false,
        isPrintFileNum: false,
        isIncludeFile: false,
        includeManifest: false,
        isContinuePrint: false,
        pageType: getPrintType(),
        direction: printInfo.value.direction,
        marginTop: printInfo.value.marginTop,
        marginLeft: printInfo.value.marginLeft,
        tableStyle: {
          isMergeSubject: false,
          isShowLeaf: false,
          isMergeAAE: false,
          isHideAAECode: false,
          isHideAAECashflow: false,
          isShowFC: false,
          isShowQuantity: false,
          isLineHeightAdaptive: false,
          voucherLine: 5,
        },
        printData,
      };
      request({
        url: window.printHost + "/api/VoucherPrintSettings" + (route.query.from ? "?from=" + route.query.from : ""),
        method: "post",
        data,
      })
        .then((res: any) => {
          if (res.state === 1000) {
            if (callBack) {
              useLoading().quitLoading();
              callBack();
            } else {
              historyIndex.value = -1;
              historyArr.value = [];
              ptLayout.value = {
                data: _.cloneDeep(coverData.value),
                printInfo: _.cloneDeep(printInfo.value),
              };
              historyCoverData.value = _.cloneDeep(ptLayout.value.data);
              ElNotify({
                type: "success",
                message: "保存成功",
              });
            }
          } else {
            ElNotify({
              type: "error",
              message: "保存失败，请稍后重试或联系侧边栏客服哦~",
            });
          }
        })
        .finally(() => {
          useLoading().quitLoading();
        });
    });
    // const node = document.getElementById("cover-node") as HTMLElement; // 通过id获取dom
    // const setBox = {
    //     useCORS: true, //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
    //     allowTaint: true, //允许跨域
    //     scale: 2, //设置放大倍数
    //     dpi: 600, //设置分辨率
    //     backgroundColor: "#ffffff", //背景色
    //     width: node.offsetWidth, //设置canvas宽度
    //     height: node.offsetHeight, //设置canvas高度
    // };
    // html2canvas(node, setBox).then((canvas) => {
    //     // base64编码
    //     const dataUrl = canvas.toDataURL();
    //     const a = document.createElement("a"); // 生成一个a元素
    //     const event = new MouseEvent("click"); // 创建一个单击事件
    //     a.download = "xxxx"; // 设置图片名称没有设置则为默认
    //     a.href = dataUrl; // 将生成的URL设置为a.href属性
    //     a.dispatchEvent(event);

    // });
  }
  function reset() {
    ElAlert({
      message: "重置将清除所有的修改内容并恢复为系统默认，您确定要继续吗？",
      options: { confirmButtonText: "继续重置", cancelButtonText: "取消" },
    }).then((r: boolean) => {
      if (r) {
        historyIndex.value = -1;
        historyArr.value = [];
        get(true);
      }
    });
  }

  function preview() {
    save(getSettingId(true), getSettingType(true), () => {
      fullScreen.value = false;
      const url =
        window.printHost +
        `/api/Voucher/OnlyPrintBasePage?appasid=${getGlobalToken()}&isPreview=true` +
        "&" +
        getUrlSearchParams({ ...printParams(printInfo.value), pageType: getPrintType() }) +
        `&noCheckHttp&r=${Math.random()}` +
        (route.query.from ? "&from=" + route.query.from : "");
      const printUrl = encodeURIComponent(viewType === "AccountBooks" ? url + "&settingType=" + getSettingType(true) : url);
      globalWindowOpenPage("/PreviewPrint?appasid=" + getGlobalToken() + "&iframeSrc=" + printUrl, "封面预览");
    });
  }

  const firstLoading = ref(true);
  const dblDragContent = ref(0);
  const dblCoord = { x: 0, y: 0, timestamp: 0 };
  function resetDblValues(e?: any) {
    if (e) {
      dblCoord.x = e.clientX;
      dblCoord.y = e.clientY;
      dblCoord.timestamp = new Date().getTime();
      dblDragContent.value = 1;
      return;
    }
    dblDragContent.value = 0;
    dblCoord.x = 0;
    dblCoord.y = 0;
    dblCoord.timestamp = 0;
  }
  function docMouseDown(e: any) {
    const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    const printRight = document.querySelector(".print-right") as HTMLElement;
    if (e.target.className === "main-drag-content") {
      dblDragContent.value++;
      if (dblDragContent.value === 1) {
        dblCoord.x = e.clientX;
        dblCoord.y = e.clientY;
        dblCoord.timestamp = new Date().getTime();
      }
      if (
        dblDragContent.value === 2 &&
        e.clientX === dblCoord.x &&
        e.clientY === dblCoord.y &&
        new Date().getTime() - dblCoord.timestamp < 300
      ) {
        dblAddModule("text", e);
        resetDblValues();
      }
      if (
        dblDragContent.value === 2 &&
        (new Date().getTime() - dblCoord.timestamp >= 300 || e.clientX !== dblCoord.x || e.clientY !== dblCoord.y)
      ) {
        resetDblValues(e);
      }
    } else {
      resetDblValues();
    }
    if (e && mainDragBox.contains(e.target)) {
      if (e.target.className !== "el-input__inner") {
        // nonHotZone.value = false;
        // activeTableInput.value.forEach((id: any) => {
        //     tableContentList.value.find((i) => i.id === id)!.inputable = false;
        //     activeTableInput.value = [];
        // });
      }
      if (e.target.className === "main-drag-content" || e.target.className === "drag-box") {
        // seniorTableData.value.inputable = false;
        // seniorTableData.value.active = false;
      }
    } else {
      if (e && printRight.contains(e.target)) {
        activeEle.value.forEach((item: any) => {
          item.active = false;
        });
      } else {
        activeEle.value.clear();
      }
    }
  }
  function initEvents() {
    ctrlKeyChange();
    let domEvents = new Map<string, EventListenerOrEventListenerObject>([["mousedown", deselect]]);
    //加了加载，没有加载元素
    const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    const printCenter = document.querySelector(".print-center") as HTMLElement;
    const printRight = document.querySelector(".print-right") as HTMLElement;
    addEvents(domEvents, mainDragBox);
    const mainContainer = document.querySelector(".main-container") as HTMLElement;
    printRight.addEventListener("mousedown", (e: any) => {
      // if (!seniorTableData.value.inputable) {
      e.stopPropagation();
      // }
    });
    useBoxSelection(
      printCenter,
      mainDragBox,
      [
        "print-center",
        "main-drag-box",
        "main-drag-content",
        "top-left-subscript",
        "top-right-subscript",
        "bottom-left-subscript",
        "bottom-right-subscript",
      ],
      boxSelectCallback
    ).addEventListener();
  }
  const refreshId = ref(0);
  onMounted(() => {
    // let domEvents = new Map<string, EventListenerOrEventListenerObject>([["mousedown", deselect]]);
    // const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    // const printCenter = document.querySelector(".print-center") as HTMLElement;
    // addEvents(domEvents, mainDragBox);
    // const mainContainer = document.querySelector(".main-container") as HTMLElement;
    // document.addEventListener("mousedown", (e: any) => {
    //     console.log(e.target.className);
    //     if (e && mainDragBox.contains(e.target)) {
    //         console.log(e.target.className);
    //     } else {
    //         activeEle.value.forEach((item: any) => {
    //             item.active = true;
    //             // if (coverData.value.find((data) => data.keyId === item)) {
    //             //     coverData.value.find((data) => data.keyId === item)!.active = true;
    //             // }
    //         });
    //     }
    // });
    // useBoxSelection(printCenter, mainDragBox, ["print-center", "main-drag-box", "main-drag-content"], boxSelectCallback).addEventListener();
    // ctrlKeyChange();
    if (route.query.printType) {
      if (
        printInfo.value.printType !== Number(route.query.printType) ||
        printInfo.value.direction !== (route.query.direction as "H" | "Z")
      ) {
        printInfo.value.printType = Number(route.query.printType);
        printInfo.value.direction = route.query.direction as "H" | "Z";
        setDragSize();
      }
    }
    window.addEventListener("refreshPageSize", (e: any) => {
      if (viewType === "AccountBooks") {
        return;
      }
      if (e.detail?.printType !== printInfo.value.printType || e.detail?.direction !== printInfo.value.direction) {
        printInfo.value.printType = e.detail?.printType;
        printInfo.value.direction = e.detail?.direction;
        setDragSize();
      } else {
        printInfo.value.printType = e.detail?.printType;
        printInfo.value.direction = e.detail?.direction;
      }
    });
  });
  onBeforeUnmount(() => {
    const bigBox = document.querySelector(".main-drag-box") as HTMLElement;
    const printCenter = document.querySelector(".print-center") as HTMLElement;
    useBoxSelection(printCenter, bigBox, ["print-center", "main-drag-box", "main-drag-content"]).cleanUp();
  });

  let originClose = false;
  const validator: (path: string, operate: string) => boolean = (path, operate) => {
    let leaveText = operate === "remove" ? "离开" : "刷新";
    let cancelText = operate === "remove" ? "留下" : "取消";
    if (historyArr.value.length > 0) {
      ElAlert({
        message: `当前封面模板还没有保存，确定要${leaveText}吗`,
        options: { confirmButtonText: cancelText, cancelButtonText: leaveText },
        close: () => {
          originClose = true;
          return true;
        },
      }).then((r) => {
        if (!r && !originClose) {
          if (operate === "remove") {
            routerArrayStore.forceRemoveRouter(path);
          } else if (operate === "refresh") {
            get();
          }
        }
        originClose = false;
      });
      return false;
    } else {
      return true;
    }
  };

  const leaveValidator = routerArrayStore.registerOperateLeaveValidator(route.path, validator);

  onUnmounted(() => {
    leaveValidator.dispose();
  });
  onActivated(() => {
    //?????
    refreshId.value++;
    if (viewType === "AccountBooks") {
        if(route.query.direction !== printInfo.value.direction) {
          printInfo.value.direction = route.query.direction as "H" | "Z";
          changeDirection()
        }
      }
    ctrlKeyChange();
    document.addEventListener("mousedown", docMouseDown);
  });
  onDeactivated(() => {
    document.removeEventListener("mousedown", docMouseDown);
  });
</script>

<style lang="less" scoped>
  @import "@/style/Voucher/SeniorCoverPrint.less";
</style>
