<template>
    <div class="main-top main-tool-bar">
        <a v-permission="['assistingaccounting-category-canedit']" class="button solid-button large-2" @click="handleNew">
            自定义辅助核算
        </a>
    </div>
    <div class="main-center">
        <Table 
            :loading="loading" 
            :columns="columns" 
            :data="tableData" 
            :scrollbar-show="true"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="190px" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="isErp" class="mr-10">
                            <a v-if="getTypePession(scope.row.aaType)" class="link" @click="handleLook(scope.row.aaType)">
                                查看
                            </a>
                        </span>
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccounting-category-canedit']" class="link" @click="handleEdit(scope.row)">
                                编辑
                            </a>
                            <a
                                v-permission="['assistingaccounting-category-candelete']"
                                class="link"
                                @click="handleDelete(scope.row.aaType)"
                            >
                                删除
                            </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { checkPermission } from "@/util/permission";
import { erpCreateTab } from "@/util/erpUtils";

const setModule = "AssitCategory";
const isErp = ref(window.isErp);
interface ITableItem {
    rowNum: number;
    aaType: number;
    aaTypeName: string;
    option: boolean;
    column01: string;
    column02: string;
    column03: string;
    column04: string;
    column05: string;
    column06: string;
    column07: string;
    column08: string;
}

const columns: IColumnProps[] = [
    { label: "类别编号", minWidth: 290, prop: "rowNum", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "rowNum") },
    { label: "类别名称", minWidth: 500, prop: "aaTypeName", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaTypeName") },
    { slot: "operator" },
];

const props = defineProps<{ tableData: ITableItem[] }>();

const tableData = computed(() => props.tableData);
const loading = ref(false);

const emit = defineEmits(["load-data", "handleNew", "handleEdit", "switchAAType"]);

const handleNew = () => emit("handleNew", 10000);

const handleEdit = (row: ITableItem) => {
    let columnList: any[] = [];
    for (let key in row) {
        if (key.includes("column")) columnList.push(row[key as keyof ITableItem]);
    }
    columnList = columnList.filter((item) => item.length !== 0);
    const params = {
        AATypeId: row.aaType,
        AATypeName: row.aaTypeName,
        columnArr: columnList,
    };
    emit("handleEdit", 10000, params);
};
const handleDelete = (aaType: number) => {
    ElConfirm("亲，确认要删除吗?").then((r: boolean) => {
        if (r) {
            request({
                url: "/api/AssistingAccountingType?aaType=" + aaType,
                method: "delete",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state == 1000) {
                    if (res.data) {
                        ElNotify({ message: "亲，删除成功啦！", type: "success" });
                        emit("load-data", true);
                    } else {
                        if (res.msg === "存在核算项目，无法删除！") {
                            ElNotify({ message: "亲，删除失败啦，存在核算项目，无法删除！", type: "warning" });
                        } else {
                            ElNotify({ message: res.msg, type: "warning" });
                        }
                    }
                } else {
                    ElNotify({ message: res.msg, type: "warning" });
                }
            });
        }
    });
};
const getTypePession = (aaType: number) => {  
    const permissionMap: { [key: number]: string[] } = {  
        10001: ["Customers-查看"],  
        10002: ["Vendors-查看"],  
        10003: ["Employees-查看"],  
        10004: ["Department-查看"],  
        10006: ["ProductManager-查看"],  
    };  
    if ([10001, 10002, 10003, 10004, 10006].includes(aaType)) {
        return checkPermission(permissionMap[aaType]); 
    } else {
        return checkPermission(["assistingaccount-canview"]);
    } 
} 
const handleLook = (aaType: number) => {  
    const routeMap: { [key: number]: [string, string] } = {  
        10001: ["/Customers", "客户列表"],  
        10002: ["/Vendors", "供应商列表"],  
        10003: ["/Employees", "职员列表"],  
        10004: ["/department", "部门"],  
        10006: ["/Products", "商品列表"],  
    };  
    if ([10001, 10002, 10003, 10004, 10006].includes(aaType)) {
        const tabData = routeMap[aaType];
        erpCreateTab(tabData[0], tabData[1]);  
    } else {
        emit("switchAAType", aaType);  
    }  
} 
</script>
