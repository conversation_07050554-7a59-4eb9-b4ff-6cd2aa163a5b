<template>
  <el-tooltip
    :visible="visible"
    popper-class="el-option-tool-tip"
    effect="light"
    :content="content"
    :placement="tooltipPlacement"
    :hide-after="0">
    <el-select
      ref="selectRef"
      v-model="value"
      v-bind="selectBindings"
      @visible-change="handleVisibleChange"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @keyup="handleKeyUp"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @input="handleInput"
      @click="handleClick">
      <slot />
      <template
        #prefix
        v-if="clearable">
        <div
          v-show="iconFlag && clearable"
          class="icon-clear"
          :style="{ right: iconClearRight }"
          @click="handleClear">
          <el-icon>
            <Close />
          </el-icon>
        </div>
      </template>
    </el-select>
  </el-tooltip>
</template>

<script setup lang="ts">
  import type { SelectProps, SelectEmits } from "./types.ts"
  import { useSelect } from "./utils.ts"

  const props = withDefaults(defineProps<SelectProps>(), {
    teleported: false,
    placeholder: "",
    fitInputWidth: true,
    filterable: false,
    clearable: false,
    disabled: false,
    iconClearRight: "22px",
  })

  const emit = defineEmits<SelectEmits>()

  const {
    selectRef,
    visible,
    content,
    value,
    iconFlag,
    handleVisibleChange,
    handleInput,
    handleClick,
    handleChange,
    handleFocus,
    handleBlur,
    handleKeyUp,
  } = useSelect(props, emit)

  // Computed
  const selectBindings = computed(() => ({
    class: ["select", props.class],
    "popper-class": `${props.popperClass} select-down`,
    placement: "bottom",
    teleported: props.teleported,
    placeholder: props.placeholder,
    "fit-input-width": props.fitInputWidth,
    filterable: props.filterable,
    clearable: false,
    disabled: props.disabled,
    "allow-create": props.allowCreate,
    "automatic-dropdown": props.automaticDropdown,
    "popper-options": props.popperOptions,
    style: props.style,
    multiple: props.multiple,
    remote: props.remote,
    "filter-method": props.filterMethod,
  }))

  const handleClear = () => {
    value.value = ""
    iconFlag.value = false
    emit("clear")
  }

  const handleMouseEnter = () => {
    iconFlag.value = Boolean(value.value)
  }

  const handleMouseLeave = () => {
    iconFlag.value = false
  }

  defineExpose({
    focus: () => {
      setTimeout(() => selectRef.value?.focus(), 150)
    },
    blur: () => selectRef.value?.blur(),
    getSelect: () => selectRef.value,
  })
</script>

<style lang="scss" scoped>
  .select {
    width: 180px;
    height: 32px;
    :deep(.el-select__wrapper) {
      height: 100%;
      .el-select__selection {
        flex-wrap: nowrap;
      }
    }
    &.select-down {
      :deep(.el-input__suffix) {
        visibility: hidden;
      }

      :deep(.el-select-menu__list) {
        border-top: 1px solid var(--border-color);
        padding: 0;
        margin: 0;
        list-style: none;
        cursor: pointer;
      }
    }
  }

  .icon-clear {
    position: absolute;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
</style>
