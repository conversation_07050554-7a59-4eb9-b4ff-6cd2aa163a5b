<template>
    <el-tooltip
        :visible="visible"
        popper-class="el-option-tool-tip"
        effect="light"
        :content="content"
        :placement="tooltipPlacement"
        :hide-after="0"
        :virtual-ref="inputElement"
    >
        <el-select
            ref="selectRef"
            :popper-class="popperClass + ' select-down'"
            placement="bottom"
            v-model="value"
            :class="[props.class, 'select', props.useCustomClass ? 'custom-select' : '',showMoreFilled?'select-down':'']"
            :teleported="teleported"
            :placeholder="placeholder"
            :fit-input-width="fitInputWidth"
            :filterable="filterable"
            :clearable="false"
            :disabled="disabled"
            @visible-change="visibleChange"
            @change="handleChange"
            @focus="handleFocus"
            @blur="handleBlur"
            @keyup="handleKeyUp"
            :suffix-icon="suffixIcon"
            :allow-create="allowCreate"
            :automatic-dropdown="automaticDropdown"
            :no-data-text="noDataText"
            :no-match-text="noMatchText"
            :palacement="props.palacement"
            @mouseenter="enterInput"
            @mouseleave="leaveInput"
            @input="handleInput"
            @click="handleClick"
            :popper-options="popperOptions"
            :style="style"
            :default-first-option="defaultFirstOption"
            :multiple="props.multiple"
            :remote="remote"
            :filter-method="filterMethod"
            :remote-show-suffix="remoteShowSuffix"
        >
            <slot> </slot>
            <template #prefix v-if="clearable || showMoreFilled">
                <div class="icon_clear" @click="handleClearClick" v-show="iconFlag && clearable" :style="{ right: IconClearRight }">
                    <el-icon v-if="circleClose" color="#a8abb2"><CircleClose /></el-icon>
                    <el-icon v-else><Close /></el-icon>
                </div>
                <div class="more-filled" v-show="showMoreFilled" :style="{ right: IconClearRight }">
                    <el-icon><MoreFilled  @click="(e)=>expandAssit(e)"/></el-icon>
                </div>
            </template>
        </el-select>
    </el-tooltip>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from "vue";
const props = withDefaults(
    defineProps<{
        modelValue: any;
        teleported?: boolean;
        bottomHtml?: string;
        placeholder?: string;
        unfold?: boolean;
        fitInputWidth?: boolean;
        filterable?: boolean;
        allowCreate?: boolean;
        clearable?: boolean;
        automaticDropdown?: boolean;
        inputText?: string;
        noDataText?: string;
        noMatchText?: string;
        immediatelyBlur?: boolean;
        disabled?: boolean;
        palacement?: string;
        class?: string;
        suffixIcon?: string | object;
        popperClass?: string;
        IconClearRight?: string;
        popperOptions?: object;
        style?: any;
        defaultFirstOption?: boolean;
        useCustomClass?: boolean;
        multiple?: boolean;
        showMoreFilled?: string;
        tooltipPlacement?: EpPropMergeType<StringConstructor, Placement, unknown> | undefined;
        circleClose?: boolean;
        filterMethod?:Function;
        remote?: boolean;
        remoteShowSuffix?: boolean;
    }>(),
    {
        teleported: true,
        bottomHtml: "",
        placeholder: "",
        unfold: false,
        fitInputWidth: true,
        filterable: false,
        allowCreate: false,
        clearable: false,
        automaticDropdown: false,
        inputText: "",
        noDataText:"无数据",
        noMatchText: " ",
        immediatelyBlur: true,
        palacement: "bottom",
        class: "",
        suffixIcon: "ArrowDown",
        popperClass: "",
        IconClearRight: "18px",
        popperOptions: () => ({}),
        style: () => ({}),
        defaultFirstOption: false,
        useCustomClass: true,
        multiple: false,
        showMoreFilled:'',
        tooltipPlacement: "right",
        circleClose: false,
        filterMethod: ()=>{},
        remote: false,
        remoteShowSuffix: false,
    }
);

const emits = defineEmits([
    "update:modelValue",
    "bottomClick",
    "keyupEnter",
    "keydownEnter",
    "change",
    "focus",
    "blur",
    "visible-change",
    "keyup",
    "input",
    "click",
    "expandAssit",
    "clear",
]);

function expandAssit(e:any) {
    e.stopPropagation();
    emits("expandAssit");
}

const value = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});

const visible = ref(false);

const selectRef = ref();
const handleInput = (val: string) => {
    emits("input", val);
};
const handleClick = (event: Event) => {
    emits("click", event);
};
/**
 * 为element-ui的Select和Cascader添加弹层底部操作按钮
 * @param visible
 * @param refName  设定的ref名称
 * @param onClick  底部操作按钮点击监听
 */
const visibleChange = (visible: boolean) => {
    emits("visible-change", visible);
    // if (!visible && props.immediatelyBlur) {
    //     nextTick().then(() => {
    //         selectRef.value?.blur();
    //     });
    //     return;
    // }
    if (visible && props.bottomHtml !== "") {
        let popper = selectRef.value?.popperPaneRef;
        if (!popper) return;
        if (popper.$el) popper = popper.$el;
        //避免重复添加
        if (!Array.from(popper.children).some((v: any) => v.className === "el-select-menu__list")) {
            const el = document.createElement("ul");
            el.className = "el-select-menu__list";
            el.innerHTML = props.bottomHtml;
            popper.appendChild(el);
            el.onclick = (event: Event) => {
                event.preventDefault();
                event.stopPropagation();
                emits("bottomClick");
                selectRef.value?.blur();
            };
        }
    }
};

const handleChange = (value: any) => {
    emits("change", value);
};
const handleFocus = (event: FocusEvent) => {
    emits("focus", event);
};
const handleBlur = (event: FocusEvent) => {
    emits("blur", event);
};
const content = ref("");
const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        emits("keyupEnter");
        return;
    }
    emits("keyup", event);
};
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        // selectRef.value?.toggleMenu();
        emits("keydownEnter");
    }
};
const inputElement = ref(selectRef.value?.$refs?.reference?.input);
const checkOverflow = () => {
    const input = selectRef.value?.$refs?.reference?.input;
    let label = "";
    if (selectRef.value?.selected?.currentLabel) {
        content.value = selectRef.value?.selected?.currentLabel;
        label = selectRef.value?.selected?.currentLabel.trim();
    } else {
        content.value = "";
        label = "";
    }
    if (input.scrollWidth > input.clientWidth && label.trim() !== "") {
        visible.value = true;
    } else {
        visible.value = false;
    }
};
const hiddenOverflow = () => {
    visible.value = false;
};

onMounted(() => {
    const input = selectRef.value?.$refs?.reference?.input;
    if (input) {
        // checkOverflow();
        input.addEventListener("keydown", handleKeyDown);
        input.addEventListener("mouseenter", checkOverflow);
        input.addEventListener("mouseleave", hiddenOverflow);
        input.addEventListener("focus", () => {
            nextTick().then(() => {
                visible.value = false;
            });
        });
    }
    if (props.unfold) {
        const timer = setTimeout(() => {
            selectRef.value?.toggleMenu();
            clearInterval(timer);
        }, 300);
    }
});

const focus = () => {
    const timer = setTimeout(() => {
        selectRef.value?.focus();
        clearInterval(timer);
    }, 150);
};
const blur = () => {
    selectRef.value?.blur();
};
const getSelect = () => {
    return selectRef.value;
};
defineExpose({ focus, blur, getSelect });
const handleClearClick = () => {
    value.value = "";
    iconFlag.value = false;
    emits("clear");
};
const enterInput = () => {
    iconFlag.value = value.value ? true : false;
};
const leaveInput = () => {
    iconFlag.value = false;
};
const iconFlag = ref(false);
</script>
<style lang="less">
.custom-select {
    .el-input__inner {
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.select-down {
    // 点击添加之类的自定义按钮的格式
    .el-select-menu__list {
        border-top: solid 1px var(--border-color);
        padding: 0;
        list-style: none;
        margin: 0;
        cursor: pointer;
    }
}
.icon_clear {
    position: absolute;
    right: 18px;
    display: flex;
}
.more-filled{
    position:absolute;
    color:black;
    cursor: pointer;
}
.select.custom-select {
    .el-input__prefix {
        width: 0px;
        
    }
    .el-input__suffix {
        margin-left: 4px;
    }
}
.select.select-down{
    .el-input__suffix {
        visibility: hidden;
    }
}
</style>
