export interface ITableItem {
    bindingDate: string;
    department: number;
    departmentName: any;
    eName: string;
    eSn: number;
    mobilePhone: string;
    state: number;
}
export interface IEmlpoyeeTableData {
    ASUB_NAME: any;
    AS_ID: number;
    BANK: string;
    BANK_ACCOUNT: string;
    BIRTHDAY: string;
    DEPARTMENT: number;
    DEPARTMENT_NAME: string;
    EDUCATION: number;
    EMAIL: string;
    END_DATE: string;
    E_CODE: string;
    E_ID: string;
    E_NAME: string;
    E_SN: number;
    E_TYPE: number;
    GENDER: number;
    InsuranceSettings: any;
    MOBILE_PHONE: string;
    NOTE: string;
    POSITION: string;
    SALARY_ASUB: number;
    SALARY_END_DATE: string;
    SALARY_START_DATE: string;
    SS: number;
    START_DATE: string;
    STATUS: number;
    TAX_BASE: number;
    TITLE: string;
}
export interface IGetEmployeeTableListParams {
    page: number;
    rows: number;
    searchInfo?: string;
}
