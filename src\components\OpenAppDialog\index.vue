<script lang="ts" setup>
  import { nextTick, ref } from "vue"

  const props = withDefaults(
    defineProps<{
      confirm: Function
      cancel: Function
      close: Function
    }>(),
    {},
  )

  const visible = ref(true)
  // const isErp = ref(window.isErp)

  const onConfirmClick = () => {
    props.confirm()
  }

  const onCancelClick = () => {
    props.cancel()
    visible.value = false
  }

  const handleClose = () => {
    props.close()
    visible.value = false
  }
</script>

<template>
  <el-dialog
    id="openAppDialog"
    :title="'提示'"
    modal-class="modal-class"
    :draggable="false"
    v-model="visible"
    destroy-on-close
    :width="460"
    align-center
    center
    @close="handleClose"
    class="dialogDrag">
    <div
      class="dialog-content"
      v-dialogDrag>
      <div
        class="dialog-content-body"
        style="text-align: left">
        <div style="font-weight: 600">
          <img
            src="@/assets/Icons/warning.png"
            style="height: 16px; width: 16px; vertical-align: top; margin-top: 2px" />
          <span>使用一键登录跳转税局功能需安装【柠檬云税务助手】，现未检测到【柠檬云税务助手】，可能原因：</span>
        </div>
        <ul style="list-style-type: disc; padding-left: 20px">
          <li>未安装，请点击“下载”按钮安装后再点击一键登录税局</li>
          <li style="margin-top: 10px">
            若已安装，但未跳转至税局，请检查浏览器页面顶部是否有引导打开ningmengyun tax的弹窗，若有则点击“打开”按钮
          </li>
        </ul>
      </div>
      <div class="buttons">
        <a
          class="button mr-20"
          @click="onCancelClick">
          取消
        </a>
        <a
          class="button solid-button"
          @click="onConfirmClick">
          下载
        </a>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
  .dialog-content {
    .buttons {
      text-align: center;
      padding: 10px 0;
      border-top: 1px solid var(--border-color);
    }
    .erp-buttons {
      padding: 16px 20px 16px 0;
      text-align: right;
      height: 30px;
      & a {
        float: right;
        margin: 0 10px;
        min-width: 64px !important;
        font-size: 12px;
      }
    }
  }
</style>
