<template>
    <el-dialog v-model="printDialogShow" center width="560px" class="custom-confirm print-dialog dialogDrag" top="3vh">
        <template #header>
            <div class="print-header" :style="{ lineHeight: isErp ? '' : '52px' }">
                <div>
                    {{ isSeniorPrint ? "凭证高级打印" : "凭证打印" }}
                    <el-tooltip popper-class="print-tip" effect="light" placement="right" :teleported="false">
                        <template #content>
                            <span class="link" @click="helpGuide">点击查看帮助</span>
                        </template>
                        <img
                            class="img-question"
                            src="@/assets/Icons/question.png"
                            @click="helpGuide"
                            style="height: 16px; margin-left: 3px"
                        />
                    </el-tooltip>
                </div>
            </div>
        </template>
        <!-- :style="isSeniorPrint?'padding-left:135px':''" -->
        <div class="print-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <template v-if="isSeniorPrint">
                <div class="print-main" style="padding: 10px 32px 10px; height: 500px; overflow-y: auto">
                    <div class="print-switch">
                        <el-tooltip popper-class="print-tip" effect="light" content="" placement="right-start" :teleported="false">
                            <template #content>
                                <div class="print-tips">
                                    <div>1.普通打印简单易用无需自己设置</div>
                                    <div>2.支持普通打印和高级打随时切换</div>
                                </div>
                            </template>
                            <div>
                                <span class="switch-icon"></span>
                                <a @click="usePrint('default')" class="link"> 使用普通打印 </a>
                            </div>
                        </el-tooltip>
                    </div>
                    <div class="print-item" v-show="printVoucherCountTypeShow">
                        <div class="print-item-label">打印选项：</div>
                        <div class="print-item-field">
                            <el-radio-group v-model="PrintVoucherCountType">
                                <el-radio label="PrintAll">打印全部凭证</el-radio>
                                <el-radio label="PrintSelect">打印选中凭证</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="print-item">
                        <div class="print-item-label">打印纸张:</div>
                        <div class="print-item-field">
                            <el-select v-model="seniorPrintInfo.printType" :teleported="false" @change="showAdjustTips">
                                <el-option v-for="item in printTypeList" :value="item.value" :label="item.label" :key="item.value" />
                            </el-select>
                        </div>
                    </div>
                    <!-- <div class="print-item">
                    <div class="print-item-label">打印凭证行:</div>
                    <div class="print-item-field">
                        <el-select v-model="printInfo.voucherLineCount" :teleported="false">
                            <el-option :value="5" label="5" />
                            <el-option :value="6" label="6" />
                            <el-option :value="7" label="7" />
                            <el-option :value="8" label="8" />
                        </el-select>
                        <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start" :teleported="false">
                            <template #content>
                                <div style="cursor: pointer">
                                    若在<span class="print-tip" @click="seniorPrint('?collapse=5')">凭证打印设置-表体设置</span
                                    >中选择了行高自适应，则凭证行数以实际计算为准
                                </div>
                            </template>
                            <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px; margin-left: 3px" />
                        </el-tooltip>
                    </div>
                </div> -->
                    <div class="print-item">
                        <div class="print-item-label">打印方向:</div>
                        <div class="print-item-field">
                            <el-radio-group v-model="seniorPrintInfo.direction" :disabled="seniorDirectionDisabled">
                                <el-radio label="Z">纵向</el-radio>
                                <el-radio label="H">横向</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="print-item">
                        <div class="print-item-label">页边距:</div>
                        <div class="print-item-field" style="width: 230px">
                            <div class="flex-center" :class="{ isErp: isErp }" style="flex-wrap: wrap; justify-content: space-between">
                                <div class="page-margin">
                                    <span class="page-direction">上</span>
                                    <el-input-number
                                        v-model="seniorPrintInfo.marginTop"
                                        :min="0"
                                        :max="maxSeniorTBMargin - minSeniorTBBottomMargin"
                                        controls-position="right"
                                        @change="handleMarginTop(seniorPrintInfo.marginTop)"
                                        @keyup="handleMargin($event, 'top')"
                                    />
                                    mm
                                </div>
                                <div class="page-margin">
                                    <span class="page-direction">下</span>
                                    <el-input-number
                                        v-model="seniorPrintInfo.marginBottom"
                                        :disabled="true"
                                        :min="minSeniorTBBottomMargin"
                                        :max="maxSeniorTBMargin"
                                        controls-position="right"
                                    />
                                    mm
                                </div>
                                <div class="page-margin">
                                    <span class="page-direction">左</span>
                                    <el-input-number
                                        v-model="seniorPrintInfo.marginLeft"
                                        :min="0"
                                        :max="35"
                                        controls-position="right"
                                        @change="handleMarginLeft(seniorPrintInfo.marginLeft)"
                                        @keyup="handleMargin($event, 'left')"
                                    />
                                    mm
                                </div>
                                <div class="page-margin">
                                    <span class="page-direction">右</span>
                                    <el-input-number
                                        v-model="seniorPrintInfo.marginRight"
                                        :disabled="true"
                                        :min="0"
                                        :max="35"
                                        controls-position="right"
                                    />
                                    mm
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="print-item">
                        <div class="print-item-label">其他选项:</div>
                        <div class="print-item-field">
                            <el-checkbox v-model="seniorPrintInfo.isPrintFrontCover" label="同时打印封面" /><br />
                            <div class="line-item height18 margintop16">
                                <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right" :teleported="false">
                                    <template #content>
                                        <div>仅支持打印jpg、png、pdf格式文件，其他文件将跳过</div>
                                    </template>
                                    <template #default>
                                        <div class="line-item-field" style="width: 100px">
                                            <el-checkbox style="margin-right: 4px" v-model="seniorPrintInfo.printFile" label="打印附件" />
                                            <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px" />
                                        </div>
                                    </template>
                                </el-tooltip>
                            </div>
                            <div
                                class="line-item height18 input-size4"
                                v-show="seniorPrintInfo.printFile"
                                style="margin-top: 0px; padding-left: 22px"
                            >
                                <div class="line-item-field">
                                    <el-checkbox v-model="seniorPrintInfo.simultaneouslyPrintFileList" label="同时打印附件清单" /><br />
                                    <el-checkbox v-model="seniorPrintInfo.continuousFiles" label="附件连续打印" />
                                </div>
                            </div>
                            <el-checkbox v-model="seniorPrintInfo.isHideEmpty" label="空行不打印" /><br />
                            <el-checkbox v-model="seniorPrintInfo.isLineHeightAdaptive" label="表格行高自适应" /><br />
                            <el-checkbox v-model="seniorPrintInfo.isShowPrintDate" label="显示打印日期" @change="changeShowDate" />
                            <el-date-picker
                                style="width: 160px; height: 28px"
                                v-if="seniorPrintInfo.isShowPrintDate"
                                v-model="seniorPrintInfo.printDateText"
                                type="date"
                                placeholder=" "
                                value-format="YYYY-MM-DD"
                                size="small"
                                :clearable="false"
                            />
                            <br />
                            <el-checkbox v-model="seniorPrintInfo.isShowSummaryPrint" label="汇总打印" /><br />
                            <div class="sub-level" v-show="seniorPrintInfo.isShowSummaryPrint">
                                <span style="margin-right: 4px">将科目汇总到</span>
                                <el-select v-model="seniorPrintInfo.summaryAsubLevel" :teleported="false" style="width: 54px">
                                    <el-option :value="1" label="1" />
                                    <el-option :value="2" label="2" />
                                    <el-option :value="3" label="3" />
                                    <el-option :value="4" label="4" />
                                </el-select>
                                <span style="margin-left: 4px">级科目</span><br />
                                <el-checkbox v-model="seniorPrintInfo.isSummaryByDirection" label="按借贷方向分别汇总" /><br />
                                <el-checkbox v-model="seniorPrintInfo.isHideZero" label="汇总金额为零不显示" /><br />
                                <el-checkbox v-model="seniorPrintInfo.isShowAssitItem" label="显示核算项目" />
                            </div>

                            <div
                                class="link"
                                style="font-size: 14px; line-height: 22px; color: var(--link-color)"
                                @click="toSeniorPrint('')"
                            >
                                更多高级设置
                                <img class="img-question" src="@/assets/Voucher/forward.png" style="height: 11px; margin-left: 3px" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="buttons borderTop">
                    <a class="button" @click="cancel">取消</a>
                    <a class="button ml-10" style="width: 106px" @click="printFrontCover">单独打印封面</a>
                    <a class="button solid-button ml-10" @click="seniorPrint">打印</a>
                </div>
            </template>
            <template v-else>
                <div class="print-main">
                    <div class="print-main-title">
                        <div class="switch-btns">
                            <div
                                :class="settingType === 'base' ? 'selected switch-btn left-btn' : 'switch-btn left-btn'"
                                @click="() => (settingType = 'base')"
                            >
                                基础设置
                            </div>
                            <div
                                :class="settingType === 'advanced' ? 'selected switch-btn right-btn' : 'switch-btn right-btn'"
                                @click="() => (settingType = 'advanced')"
                            >
                                更多设置
                            </div>
                        </div>
                        <div class="print-switch">
                            <el-tooltip popper-class="print-tip" effect="light" content="" placement="right-start" :teleported="false">
                                <template #content>
                                    <div class="print-tips">
                                        <div>1.高级打印可灵活配置凭证打印模版</div>
                                        <div>2.支持普通打印和高级打印随时切换</div>
                                    </div>
                                </template>
                                <div>
                                    <span class="switch-icon"></span>
                                    <a @click="usePrint('senior')" class="link"> 使用高级打印 </a>
                                </div>
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="switch-content print-switch-settings print-base-settings" v-show="settingType === 'base'">
                        <div class="line-item height28" v-show="printVoucherCountTypeShow">
                            <div class="line-item-title height28">打印选项：</div>
                            <div class="line-item-field height28">
                                <el-radio-group v-model="PrintVoucherCountType">
                                    <el-radio label="PrintAll">打印全部凭证</el-radio>
                                    <el-radio label="PrintSelect">打印选中凭证</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="line-item height28">
                            <div class="line-item-title">选择打印纸张：</div>
                            <div class="line-item-field input-size1">
                                <el-select v-model="printInfo.printType" :teleported="false">
                                    <el-option :value="4" label="发票版 14*24cm（推荐）" />
                                    <el-option :value="1" label="A4两版（推荐）" />
                                    <el-option :value="2" label="A4三版" />
                                    <el-option :value="3" label="A4宽 12*21cm" />
                                    <el-option :value="7" label="A5" />
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item height18" style="margin-top: 10px" v-show="printLoacationShow">
                            <div class="line-item-title"></div>
                            <div class="line-item-field">
                                <el-radio-group v-model="printInfo.printLoacation" @change="changePrintLoacation">
                                    <el-radio label="Center">居中</el-radio>
                                    <el-radio label="Top">靠上</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="line-item height28">
                            <div class="line-item-title">打印版凭证行：</div>
                            <div class="line-item-field input-size2">
                                <el-select v-model="printInfo.voucherLineCount" :teleported="false" :disabled="voucherLineCountDisabled">
                                    <el-option :value="5" label="5" />
                                    <el-option :value="6" label="6" />
                                    <el-option :value="7" label="7" />
                                    <el-option :value="8" label="8" />
                                </el-select>
                                <span style="margin-left: 8px">行</span>
                            </div>
                        </div>
                        <div class="line-item height28">
                            <div class="line-item-title">打印字体：</div>
                            <div class="line-item-field input-size2">
                                <el-select v-model="printInfo.font" :teleported="false">
                                    <el-option :value="0" label="宋体" />
                                    <el-option :value="1" label="黑体" />
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item height28">
                            <div class="line-item-title">打印字体大小：</div>
                            <div class="line-item-field input-size2">
                                <el-select v-model="printInfo.voucherFontSize" :teleported="false">
                                    <el-option :value="8" label="8" />
                                    <el-option :value="9" label="9" />
                                    <el-option :value="10" label="10" />
                                    <el-option :value="11" label="11" />
                                    <el-option :value="12" label="12" />
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item height28">
                            <div class="line-item-title height28">打印方向：</div>
                            <div class="line-item-field">
                                <el-radio-group v-model="printInfo.direction" :disabled="directionDisabled">
                                    <el-radio label="Z">纵向</el-radio>
                                    <el-radio label="H">横向</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="line-item height28">
                            <div class="line-item-title">页面边距：</div>
                            <div class="line-item-field span-content">
                                <span>左</span>
                                <input type="text" v-model="printInfo.marginLeft" @keyup="handleMarginLeft(printInfo.marginLeft)" />
                                <span class="mr-20">毫米</span>
                                <span class="ml-20">右</span>
                                <input type="text" v-model="printInfo.marginRight" readonly disabled />
                                <span>毫米</span>
                            </div>
                        </div>
                        <div class="line-item height28" style="margin-top: 12px">
                            <div class="line-item-title"></div>
                            <div class="line-item-field span-content">
                                <span>上</span>
                                <input type="text" v-model="printInfo.marginTop" @keyup="handleMarginTop(printInfo.marginTop)" />
                                <span class="mr-20">毫米</span>
                                <span class="ml-20">下</span>
                                <input type="text" v-model="printInfo.marginBottom" readonly disabled />
                                <span>毫米</span>
                            </div>
                        </div>
                    </div>
                    <div class="switch-content print-switch-settings print-advanced-settings" v-show="settingType === 'advanced'">
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isPrintUserName" label="打印制单人姓名" />
                                <el-checkbox v-model="printInfo.isPrintApprovedName" label="打印审核人姓名" />
                                <el-checkbox v-model="printInfo.isPrintFrontCover" label="打印封面" />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field input-size3">
                                <el-checkbox label="打印主管姓名" v-model="printInfo.isPrintDirectorName" />
                                <input
                                    v-show="printInfo.isPrintDirectorName"
                                    type="text"
                                    v-model="printInfo.directorName"
                                    style="margin: -5px 0 -5px 16px"
                                />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field input-size3">
                                <el-checkbox v-model="printInfo.isPrintMakerName" label="打印记账姓名" />
                                <input
                                    v-show="printInfo.isPrintMakerName"
                                    type="text"
                                    v-model="printInfo.makerName"
                                    style="margin: -5px 0 -5px 16px"
                                />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field input-size3">
                                <el-checkbox label="打印出纳姓名" v-model="printInfo.isPrintCashierName" />
                                <input
                                    v-show="printInfo.isPrintCashierName"
                                    type="text"
                                    v-model="printInfo.cashierName"
                                    style="margin: -5px 0 -5px 16px"
                                />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isShowOriginalCoinAndRate" label="在摘要中打印显示原币、汇率" />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isShowQuantityAndUnitPrice" label="在摘要中打印显示数量、单价" />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isShowAsubCode" label="打印显示科目编码" />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isPrintFileNum" label="无附件时打印“0”" />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isShowSummaryPrint" label="汇总打印" />
                            </div>
                        </div>
                        <div class="line-item height18" v-show="printInfo.isShowSummaryPrint" style="margin-top: 10px; padding-left: 22px">
                            <div class="line-item-field input-size4">
                                <span style="margin-right: 8px">将科目汇总到</span>
                                <el-select v-model="printInfo.summaryAsubLevel" :teleported="false">
                                    <el-option :value="1" label="1" />
                                    <el-option :value="2" label="2" />
                                    <el-option :value="3" label="3" />
                                    <el-option :value="4" label="4" />
                                </el-select>
                                <span style="margin-left: 8px">级科目</span>
                            </div>
                        </div>
                        <div
                            class="line-item height18 input-size4"
                            v-show="printInfo.isShowSummaryPrint"
                            style="margin-top: 10px; padding-left: 22px"
                        >
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.isSummaryByDirection" label="按借贷方向分别汇总" />
                                <el-checkbox v-model="printInfo.isHideZero" label="汇总金额为零不显示" />
                                <el-checkbox v-model="printInfo.isShowAssitItem" label="显示核算项目" />
                            </div>
                        </div>
                        <div class="line-item height18 margintop16">
                            <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start" :teleported="false">
                                <template #content>
                                    <div>仅支持打印jpg、png、pdf格式文件，其他文件将跳过</div>
                                </template>
                                <template #default>
                                    <div class="line-item-field">
                                        <el-checkbox style="margin-right: 4px" v-model="printInfo.printFile" label="打印附件" />
                                        <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px" />
                                    </div>
                                </template>
                            </el-tooltip>
                        </div>
                        <div
                            class="line-item height18 input-size4"
                            v-show="printInfo.printFile"
                            style="margin-top: 10px; padding-left: 22px"
                        >
                            <div class="line-item-field">
                                <el-checkbox v-model="printInfo.simultaneouslyPrintFileList" label="同时打印附件清单" /><br />
                                <el-checkbox v-model="printInfo.continuousFiles" label="附件连续打印" />
                            </div>
                        </div>
                    </div>
                    <div class="txt">
                        <span class="highlight-red">提示：</span>
                        为了保证您的正常打印，请先下载安装
                        <a class="link" @click="globalWindowOpen('https://get.adobe.com/cn/reader/')"> Adobe PDF阅读器 </a>
                    </div>
                </div>
                <div class="buttons borderTop">
                    <a @click="() => (printDialogShow = false)" class="button ml-10">取消</a>
                    <a @click="confirmPrint" class="button solid-button ml-10">打印</a>
                </div>
            </template>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ElNotify } from "@/util/notify";
import { ElAlert } from "@/util/confirm";
import { getGlobalToken } from "@/util/baseInfo";
import { getFormatterDate } from "@/util/date";
import { request } from "@/util/service";
import { printTypeList, getMaxMargin } from "@/views/Voucher/utils";
import { globalWindowOpen, globalWindowOpenPage, globalPrint, getUrlSearchParams } from "@/util/url";
import {
    getVoucherPrintInfo,
    getVoucherSeniorPrintInfo,
    setVoucherPrintInfo,
    setVoucherSeniorPrintInfo,
    printParams,
} from "@/components/Voucher/utils";
import { ref, reactive, watch, computed, onMounted } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { isLemonClient } from "@/util/lmClient";

const _ = getGlobalLodash();
const isErp = ref(window.isErp);
const emit = defineEmits(["confirmPrint", "seniorPrint"]);
const printDialogShow = defineModel<boolean>("printDialogShow");
const props = defineProps({
    printVoucherCountTypeShow: {
        type: Boolean,
        default: true,
    },
});
const isSeniorPrint = ref(false);

function helpGuide() {
    if (isSeniorPrint.value) {
        let url = "";
        if (isErp.value) {
            url = "https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=130150111";
        } else {
            url = "https://help.ningmengyun.com/#/jz/videoPlayer?qType=130150161";
        }
        globalWindowOpen(url);
    } else {
        globalWindowOpen("https://help.ningmengyun.com/#/jz/videoPlayer?qType=130150130");
    }
}
let printInfo = reactive(getVoucherPrintInfo());
//涉及重新赋值
const seniorPrintInfo = ref({ ...getVoucherSeniorPrintInfo(), printDateText: getFormatterDate("", "-") });
const settingType = ref("base");
const PrintVoucherCountType = ref("PrintAll");

const directionDisabled = ref(true);
const seniorDirectionDisabled = ref(true);
const printLoacationShow = ref(false);
const voucherLineCountDisabled = ref(false);

function changeShowDate() {
    if (seniorPrintInfo.value.isShowPrintDate) {
        seniorPrintInfo.value.marginBottom = Math.max(seniorPrintInfo.value.marginBottom, minSeniorTBBottomMargin.value);
        seniorPrintInfo.value.marginTop = maxSeniorTBMargin.value - seniorPrintInfo.value.marginBottom;
        seniorPrintInfo.value.printDateText = getFormatterDate("", "-");
    }
}
const localPrintType = localStorage.getItem(getGlobalToken() + "-printType");
if (localPrintType === "senior") {
    isSeniorPrint.value = true;
}

const maxTBMargin = computed({
    get() {
        return getMaxMargin(printInfo.printType);
    },
    set(value: number) {
        return value;
    },
});
const maxSeniorTBMargin = computed({
    get() {
        return getMaxMargin(seniorPrintInfo.value.printType);
    },
    set(value: number) {
        return value;
    },
});
const minSeniorTBBottomMargin = computed(() => {
    if (seniorPrintInfo.value.isShowPrintDate || seniorPrintInfo.value.isShowPageNumber) {
        return seniorPrintInfo.value.printType === 2 || seniorPrintInfo.value.printType === 3 ? 8 : 12;
    }
    return 0;
});
if (isSeniorPrint.value) {
    seniorPrintInfo.value.marginBottom = maxSeniorTBMargin.value - seniorPrintInfo.value.marginTop;
    seniorPrintInfo.value.marginRight = 35 - seniorPrintInfo.value.marginLeft;
}
function showAdjustTips() {
    printDialogShow.value = false;
    ElAlert({
        message: `<div style='text-align:left'>修改纸张后会导致打印效果不佳<br>点击‘去调整’可前往‘凭证打印设置’中修改并调整好模板样式再来打印，如需打印封面，调整纸张也需要调整封面模板哦
</div>`,
        title: "提示",
        options: {
            confirmButtonText: "去调整",
            cancelButtonText: "取消",
        },
    }).then((r: boolean) => {
        seniorPrintInfo.value.printType = getVoucherSeniorPrintInfo().printType;
        if (r) {
            toSeniorPrint("?collapse=5");
        } else {
            printDialogShow.value = true;
        }
    });
}

function handleMargin(e: any, direction: string) {
    switch (direction) {
        case "top":
            handleMarginTop(e.target.value, e);
            break;
        case "left":
            handleMarginLeft(Number(e.target.value), e);
            break;
    }
}

function handleMarginTop(val: number, e?: any) {
    const marginTop = Number(val);
    let maxMargin = isSeniorPrint.value ? maxSeniorTBMargin.value : maxTBMargin.value;
    // let printType = isSeniorPrint.value ? seniorPrintInfo.value.printType : printInfo.printType;
    let top = Number(val);
    if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0 || Number(val) < 0) {
        val = 25;
        ElNotify({ type: "warning", message: "请输入正整数边距~" });
    }
    if (marginTop > maxMargin) {
        ElNotify({ type: "warning", message: "最高可设置" + maxMargin + "边距" });
        top = getMaxMargin(printInfo.printType, "top");
    }
    if (isSeniorPrint.value) {
        e && (e.target.value = Number(val));
        seniorPrintInfo.value.marginTop = top;
        seniorPrintInfo.value.marginBottom = maxMargin - seniorPrintInfo.value.marginTop;
    } else {
        printInfo.marginTop = top;
        printInfo.marginBottom = maxMargin - printInfo.marginTop;
    }
}

function handleMarginLeft(val: number, e?: any) {
    if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0 || Number(val) < 0) {
        val = 25;
        ElNotify({ type: "warning", message: "请输入正整数边距~" });
    }
    if (val > 35) {
        ElNotify({ type: "warning", message: "最高可设置35边距" });
        //为什么是25
        val = 25;
    }

    if (isSeniorPrint.value) {
        e && (e.target.value = Number(val));
        seniorPrintInfo.value.marginLeft = val;
        seniorPrintInfo.value.marginRight = 35 - val;
    } else {
        printInfo.marginLeft = val;
        printInfo.marginRight = 35 - val;
    }
}

function changePrintVersion(version: number) {
    //凭证高级打印id为11
    request({
        url: window.printHost + "/api/VoucherPrintSettings/ChangePrintVersion?settingId=1&version=" + version,
        method: "post",
    });
}
function usePrint(type: string) {
    if (type === "senior") {
        printDialogShow.value = false;
        seniorPrintInfo.value = getVoucherSeniorPrintInfo();
        const printType = localStorage.getItem(getGlobalToken() + "-printType");
        changePrintVersion(1);
        if (!printType) {
            localStorage.setItem(getGlobalToken() + "-printType", "senior");
            ElAlert({
                message: `<div style="text-align:left;margin:-10px -9px 0 -9px;"><div style="padding-bottom:12px">切换到高级打印模式后，凭证均按照高级打印中配置的模板样式进行打印，您也可以自由灵活的进行模板样式设置和调整</div>
            <div style="padding-bottom:12px">高级打印和普通打印属于两种打印模式，支持随时来回切换
              </div>是否继续切换到高级打印？</div>`,
                options: {
                    confirmButtonText: "继续切换",
                    cancelButtonText: "取消",
                },
            }).then((r: boolean) => {
                printDialogShow.value = true;
                if (r) {
                    globalWindowOpenPage("/Voucher/SeniorVoucherPrint?collapse=5", "凭证打印设置");
                    isSeniorPrint.value = true;
                }
            });
        } else {
            localStorage.setItem(getGlobalToken() + "-printType", "senior");
            printDialogShow.value = true;
            isSeniorPrint.value = true;
        }
    } else {
        isSeniorPrint.value = false;
        localStorage.setItem(getGlobalToken() + "-printType", "default");
        changePrintVersion(0);
    }
}

function toSeniorPrint(query: string) {
    globalWindowOpenPage("/Voucher/SeniorVoucherPrint" + query, "凭证打印设置");
}

function cancel() {
    printDialogShow.value = false;
}

function printFrontCover() {
    globalPrint(window.printHost + "/api/Voucher/OnlyPrintBasePage?" + getUrlSearchParams(printParams(seniorPrintInfo.value)));
}
function canPrint() {
    if (!printInfo.marginTop && printInfo.marginTop !== 0) {
        ElNotify({ type: "warning", message: "请输入上边距后进行打印" });
        return false;
    }
    if (isNaN(Number(printInfo.marginTop))) {
        ElNotify({ type: "warning", message: "上边距只能输入数字" });
        return false;
    }
    if (!printInfo.marginLeft && printInfo.marginLeft !== 0) {
        ElNotify({ type: "warning", message: "请输入左边距后进行打印" });
        return false;
    }
    if (isNaN(Number(printInfo.marginLeft))) {
        ElNotify({ type: "warning", message: "左边距只能输入数字" });
        return false;
    }
    return true;
}
function confirmPrint() {
    if (!canPrint()) return;
    setVoucherPrintInfo(printInfo);
    if (localStorage.getItem("updateTipShow") === "true") {
        const seniorPrintInfo = {
            ...printInfo,
            isShowPrintDate: false,
            isShowPageNumber: false,
            isShowSplitLine: true,
            isSplitPageByVNum: false,
            printDateText: "",
            isLineHeightAdaptive: false,
            isHideEmpty: false,
            currentVersion: 0,
        };
        setVoucherSeniorPrintInfo(seniorPrintInfo);
    }
    setTimeout(() => {
        emit("confirmPrint", getPrintInfo());
    }, 200);
}
function seniorPrint() {
    if (!canPrint()) return;
    setVoucherSeniorPrintInfo(seniorPrintInfo.value);
    window.dispatchEvent(new CustomEvent("refreshVoucherSettingPage", { detail: { printInfo: seniorPrintInfo.value } }));
    window.dispatchEvent(
        new CustomEvent("refreshPageSize", {
            detail: { printType: seniorPrintInfo.value.printType, direction: seniorPrintInfo.value.direction },
        })
    );
    setTimeout(() => {
        emit("seniorPrint", getPrintInfo());
    }, 200);
}
function changePrintLoacation() {
    if (printInfo.printLoacation === "Center") {
        printInfo.marginTop = 14;
        printInfo.marginBottom = 15;
    } else {
        printInfo.marginTop = 9;
        printInfo.marginBottom = 9;
    }
}
function changePrintType(type: string) {
    let direction = "";
    let dirDisabled = true;
    let marginTop = 0;
    let marginBottom = 0;
    const val = isSeniorPrint.value ? seniorPrintInfo.value.printType : printInfo.printType;
    if ([4, 3, 7, 8].findIndex((item) => item === val) > -1) {
        direction = "H";
        if (val === 4) {
            marginTop = 9;
            marginBottom = 12;
        } else if (val === 3) {
            marginTop = 9;
            marginBottom = 9;
        } else {
            marginTop = 14;
            marginBottom = 15;
        }
        dirDisabled = false;
    } else if ([1, 2].findIndex((item) => item === val) > -1) {
        direction = "Z";
        if (val === 2) {
            marginTop = 5;
            marginBottom = 8;
        } else if (val === 1) {
            marginTop = 14;
            marginBottom = 15;
            // changePrintLoacation();
        }
        dirDisabled = true;
    } else {
        // 6 A4正版
        direction = "Z";
        dirDisabled = true;
        marginTop = 14;
        marginBottom = 15;
    }
    if (isSeniorPrint.value && type === "senior") {
        seniorPrintInfo.value.direction = direction;
        seniorPrintInfo.value.marginTop = marginTop;
        seniorPrintInfo.value.marginBottom = marginBottom;
        seniorDirectionDisabled.value = dirDisabled;
    } else {
        printInfo.direction = direction;
        printInfo.marginTop = marginTop;
        printInfo.marginBottom = marginBottom;
        directionDisabled.value = dirDisabled;
        if (val === 2) {
            printInfo.voucherLineCount = 5;
            voucherLineCountDisabled.value = true;
        } else {
            voucherLineCountDisabled.value = false;
        }
    }
    if (val === 1) {
        printLoacationShow.value = true;
    } else {
        printLoacationShow.value = false;
    }
}

watch([() => printInfo.printType], () => {
    const storePrintInfoStr =  JSON.parse(localStorage.getItem("voucherPrintInfo")??'{}');
    if (Object.keys(storePrintInfoStr).length && storePrintInfoStr.printType !== printInfo.printType) {
        changePrintType("default");
    }else{
        directionDisabled.value = [4,3, 7, 8].findIndex((item) => item === printInfo.printType) === -1
        //Lmtips:这个依据bug影响,倒也合理，但是这个地方的逻辑不太好
        seniorDirectionDisabled.value = [4,3, 7, 8].findIndex((item) => item === seniorPrintInfo.value.printType) === -1
        printLoacationShow.value = printInfo.printType === 1;
        if (printInfo.printType === 2) {
            printInfo.voucherLineCount = 5;
            voucherLineCountDisabled.value = true;
        } else {
            voucherLineCountDisabled.value = false;
        }
    }
},{immediate:true});
watch([() => seniorPrintInfo.value.printType], () => {
    changePrintType("senior");
});

//后端返回的设置是0，与默认不符合
watch(
    () => seniorPrintInfo.value.isShowSummaryPrint,
    (val) => {
        if (val && seniorPrintInfo.value.summaryAsubLevel === 0) {
            seniorPrintInfo.value.summaryAsubLevel = 1;
        }
    },
    {
        immediate: true,
    }
);
const seniorChanged = ref(false);
watch(
    () => seniorPrintInfo,
    () => {
        seniorChanged.value = true;
    },
    { deep: true }
);

onMounted(() => {
    window.addEventListener("refreshVoucherSettingDialog", (e: any) => {
        seniorPrintInfo.value = _.cloneDeep(e.detail?.printInfo);
        seniorDirectionDisabled.value = [4, 3, 7, 8].findIndex((item) => item === seniorPrintInfo.value.printType) === -1;
    });
});
const getPrintType = () => {
    return PrintVoucherCountType.value;
};

const getPrintInfo = () => {
    if (isSeniorPrint.value) {
        if (seniorChanged.value) {
            seniorChanged.value = false;
            return { ...printParams(seniorPrintInfo.value), isSeniorPrint: true, hasChange: true };
        } else {
            return { ...printParams(seniorPrintInfo.value), isSeniorPrint: true, hasChange: false };
        }
    } else {
        const { printFile, simultaneouslyPrintFileList, continuousFiles, ...printParams } = printInfo;
        return {
            ...printParams,
            isIncludeFile: (printFile ? 1 : 0) + "_" + (simultaneouslyPrintFileList ? 1 : 0) + "_" + (continuousFiles ? 1 : 0),
            isSeniorPrint: false,
            async: !isLemonClient(),
        };
    }
};

defineExpose({ getPrintType, getPrintInfo });
</script>

<style scoped lang="less">
@import "@/style/Functions.less";
@import "@/style/Voucher/VoucherList.less";
.print-header {
    // line-height: 52px;/
    color: #333;
    font-size: 16px;
    .img-question {
        cursor: pointer;
    }
}

:deep(.print-voucher-tip) {
    width: 150px;
}

.print-content {
    .set-font(#333,14px);
    .print-switch {
        font-size: 0;
        margin-top: 5px;
        float: right;
        display: flex;
        align-items: center;

        .switch-icon {
            display: inline-block;
            height: 20px;
            width: 20px;
            background: url("@/assets/Voucher/switch.png") no-repeat;
            background-size: cover;
            vertical-align: top;
        }
    }
    .print-item {
        display: flex;
        align-items: center;
        padding-top: 8px;
        .print-item-label {
            line-height: 32px;
            width: 84px;
            padding-top: 0;
            align-self: flex-start; /* 使该元素在纵向上顶部对齐 */
        }
        .print-item-field {
            .sub-level {
                padding-left: 20px;
            }
            .time-input {
                .detail-original-input(152px, 28px);
            }
        }
    }
    .print-main {
        .detail-lm-default-scroll(8px);
        .switch-content {
            display: flex;
            flex-direction: column;
            height: 454px;
            width: 496px;

            .line-item {
                margin-top: 23px;
                font-size: 12px;
                display: flex;
                flex-direction: row;
            }
        }
        .txt {
            margin-top: 10px;
        }
    }
    &.erp {
        .buttons {
            display: flex !important;
            flex-direction: row;
            justify-content: flex-end;
        }
    }
}
.flex-center {
    display: flex;
    align-items: center;
    .page-margin {
        display: flex;
        font-size: 11px;
        line-height: 24px;
        margin-top: 10px;
        .page-direction {
            width: 24px;
            height: 24px;
            text-align: center;
            border: 1px solid #44b449;
            border-right: none;
            color: #666;
        }
        :deep(.el-input-number) {
            width: 48px;
            .el-input-number__decrease,
            .el-input-number__increase {
                height: 12px !important;
                width: 12px !important;
            }
            .el-input {
                height: 26px;
                .el-input__wrapper {
                    box-shadow: 0 0 0 0;
                    border: 1px solid #44b449;
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                    .el-input__inner {
                        text-align: center;
                        padding-right: 13px !important;
                    }
                }
            }
        }
        .operate {
            width: 18px;
            height: 24px;
            border: 1px solid #a3cfff;
            display: flex;
            flex-wrap: wrap;
            span {
                cursor: pointer;
                width: 100%;
                height: 12px;
                text-align: center;
                line-height: 12px;
            }
            .add-btn {
                .el-icon-caret-top:before {
                    content: "\E60C";
                }
            }
            .sub-btn {
                .el-icon-caret-bottom:before {
                    content: "\E60B";
                }
            }
        }
    }
    &.isErp {
        .page-direction {
            border: 1px solid #3d7fff;
            border-right: none;
        }
        :deep(.el-input-number) {
            .el-input {
                .el-input__wrapper {
                    border: 1px solid #3d7fff;
                }
            }
        }
        .operate {
            border: 1px solid #3d7fff;
        }
    }
}
</style>
