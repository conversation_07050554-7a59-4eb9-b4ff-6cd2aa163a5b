<template>
    <div class="main-content align-center">
        <div class="slot-mini-content">
            <div class="title">权限设置</div>
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <span class="asname">{{ asname }}</span>
                    <Suspense><Help /></Suspense>
                </div>
                <div class="main-tool-right">
                    <a v-permission="['permissions-canedit']" class="button solid-button large-3" @click="() => emit('adduser')">
                        新增成员
                    </a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <div class="tb">
                    <div class="tb-head">
                        <span class="tb-td tb-font">用户</span>
                        <span class="tb-td tb-font">手机号</span>
                        <span class="tb-td tb-font">权限</span>
                        <span class="tb-td tb-font">操作</span>
                        <div class="clear"></div>
                    </div>
                    <div class="tb-body">
                      <el-scrollbar :always="true" :maxHeight="'calc(100vh - 300px)'">
                        <div class="tb-tr" v-for="(item,index) in permissionList" :key="item.userSn">
                            <div class="tb-td tbody-font">
                                <Tooltip :content="item.userName" placement="top" effect="light">
                                   <div class="tbody-font left">{{ item.userName }}</div>
                                </Tooltip>
                            </div>
                            <div class="tb-td tbody-font">
                                {{ item.mobile }}
                            </div>
                            <div class="tb-td tbody-font">
                                <el-tooltip :content="item.permission" placement="top" effect="light" :visible="visible[index]">
                                    <div class="tbody-font" @mouseenter="handleMouseEnter(index)" @mouseleave="handleMouseLeave(index)">{{ item.permission }}</div>
                                </el-tooltip>
                            </div>
                            <div class="tb-td tbody-font">
                                <div v-if="item.option !== ''">
                                    <a class="link" v-if="item.option === 'transfer'" @click="handleTransfer(item)">移交</a>
                                    <span v-else>
                                        <a class="link mr-10" v-permission="['permissions-canedit']" @click="handleEdit(item)">编辑</a>
                                        <a class="link" v-permission="['permissions-candelete']" @click="handleDelete(item.userSn)">删除</a>
                                    </span>
                                </div>
                            </div>
                        </div>
                      </el-scrollbar>
                    </div>
                </div>
                <div class="tip" v-show="tipShow">
                    <img src="@/assets/Settings/warnning-yellow.png" alt="" />
                    <span> 免费版仅限账套管理员一人使用，如需多人使用您可以选择升级至专业版 </span>
                    <a class="link" @click="trialButtonOnclick">立即升级</a>
                </div>
            </div>
        </div>

        <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
        <el-dialog title="移交账套" width="440px" center v-model="transferShow">
            <div class="verify-phone-content">
                <div class="verify-phone-main">
                    <div id="note" class="txt txt-title">{{ transferTipText }}</div>
                    <div id="noteAsName" class="txt txt-title">{{ asname }}</div>
                    <div class="txt mt-8 highlight-red">1、移交账套后，您将不能再查看该账套！</div>
                    <div class="txt mt-8 highlight-red" v-permission="['permissions-canedit']">
                        2、如设置了备份转发邮箱，移交后会自动解除！
                    </div>
                    <div class="input mt-8">
                        <input type="text" v-show="!acceptMobileShow" v-model="txtMobile" disabled />
                        <input type="text" v-show="acceptMobileShow" v-model="acceptMobile" placeholder="请输入接收人的手机" />
                        <a @click="SendSMS" class="button per-button large-2 ml-6">{{ sendMsg }}</a>
                    </div>
                    <div class="input mt-10">
                        <input v-model="txtConfirmCode" type="text" placeholder="请输入验证码" />
                    </div>
                </div>
                <div class="buttons" :style="isErp ? '' : 'border-top:1px solid var(--border-color)'">
                    <a v-show="vMobileBtn" @click="validConfirmCode" class="button warn-button">确定</a>
                    <a v-show="transferBtn" @click="transferSubmit" class="button warn-button ml-10">移交</a>
                    <a @click="() => (transferShow = false)" class="button ml-10">取消</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { computed, ref,onMounted,watch } from "vue";

import Help from "./Help.vue";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams, setTopLocationhref } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { AppConfirmDialog } from "@/util/appConfirm";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import type { ITableItem, IAccountList } from "../types";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { getServiceId } from "@/util/proUtils";
const asid = useAccountSetStore().accountSet?.asId as number;
const trialStatusStore = useTrialStatusStore();
const isThirdPart = useThirdPartInfoStoreHook().isThirdPart;
const _ = getGlobalLodash()
const isErp = ref(window.isErp);
const props = defineProps<{
    asname: string;
    permissionList: ITableItem[];
    currentPermission: string;
    accountList: IAccountList[];
    trialButtonOnclick: Function;
}>();
const emit = defineEmits(["adduser", "load-data", "editPermission"]);

const trialButtonOnclick = () => {
    props.trialButtonOnclick();
};

const currentPermission = computed(() => props.currentPermission);
const asname = computed(() => props.asname);
const permissionList = computed(() => props.permissionList);
// const accountList = computed(() => props.accountList);
// const token = getGlobalToken();

const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const tipShow = ref(false);

const handleEdit = (row: ITableItem) => emit("editPermission", row);
const handleDelete = (userSn: number) => {
    if (currentPermission.value != "10001") {
        ElNotify({ type: "warning", message: "亲，您没有权限删除此权限！" });
        return;
    }
    ElConfirm("亲，确认要删除吗？").then((r: any) => {
        if (r) {
            let url = `/api/Permissions/V2?userSn=${userSn}&asId=${asid}&serviceid=${getServiceId()}`;
            request({ url, method: "delete" })
                .then((res: IResponseModel<string>) => {
                    if (res.state == 1000) {
                        ElNotify({ type: "success", message: "亲，删除成功啦！" });
                        emit("load-data");
                    } else {
                        ElNotify({ type: "warning", message: "亲，删除失败啦！" });
                    }
                })
                .catch((err: any) => {
                    if (err.response?.status === 400) {
                        proOverFlowText.value = err.response.data;
                        proOverFlowShow.value = true;
                    }
                });
            return;
        }
    });
};

// 移交相关操作及变量定义
const transferShow = ref(false);
const txtMobile = ref("");
const txtConfirmCode = ref("");
const acceptMobile = ref("");
const vMobileBtn = ref(true);
const transferBtn = ref(true);
const sendMsg = ref("发送验证码");
const acceptMobileShow = ref(false);
const transferTipText = ref("请验证移交人的手机，您即将移交的账套是：");
let canSend = true;
let timer: any = null;
let step = 0;
const handleTransfer = (item: ITableItem) => {
    AppConfirmDialog(1050).then((r: boolean) => {
        if (r) {
            step = 0;
            txtMobile.value = item.mobile;
            acceptMobile.value = "";
            vMobileBtn.value = true;
            transferBtn.value = false;
            acceptMobileShow.value = false;
            transferShow.value = true;
        }
    });
};
const SendSMS = () => {
    if (!canSend) return;
    if (acceptMobileShow.value && !CheckPhone(acceptMobile.value)) {
        ElNotify({ type: "warning", message: "亲，您输入的手机号码不正确" });
        return;
    }
    canSend = false;
    sendMsg.value = "正在发送...";
    const params = {
        CurrentSystemType: 1,
        Phone: step === 0 ? txtMobile.value : acceptMobile.value,
        stype: 13,
        appasid: getGlobalToken(),
    };
    request({
        url: window.accountSrvHost + "/Default/Services/SendSMSForConfirm.ashx?" + getUrlSearchParams(params),
        method: "get",
    })
        .catch(() => {
            ElNotify({ type: "warning", message: "亲，发送失败，请稍后再试！" });
        })
        .finally(() => {
            sendValidCode();
        });
};
const visible = ref<boolean[]>([]);
const initializeVisibleArray=()=> {
    visible.value = Array(permissionList.value.length).fill(false);

}
onMounted(() => {
    initializeVisibleArray()
});

//摘要气泡显示
const handleMouseEnter=(index:any)=> {
    if((permissionList.value)[index].permission.length>=15){
        visible.value[index]=true
    }
}
//摘要气泡隐藏
const handleMouseLeave=(index: number)=> {
    visible.value[index]=false
}
watch(
    () => permissionList.value.length,
    () => {
        initializeVisibleArray()
    }
);

const sendValidCode = (second?: number) => {
    if (!second) second = 60;
    if (second - 1 > 0) {
        sendMsg.value = "重新发送（" + second + "）";
        second--;
        timer = setTimeout(() => {
            sendValidCode(second);
        }, 1000);
    } else {
        sendMsg.value = "重新发送（" + 1 + "）";
        setTimeout(() => {
            sendMsg.value = "重新发送";
            canSend = true;
            clearTimeout(timer);
            timer = null;
            return;
        }, 1000);
    }
};

const validConfirmCode = () => {
    if (txtConfirmCode.value == "") {
        ElNotify({ type: "warning", message: "请输入验证码！" });
        return;
    }
    request({
        url: "/api/ConfirmCode/CheckCodeTime?mobile=" + txtMobile.value + "&confirmCode=" + txtConfirmCode.value,
        method: "post",
    }).then((r: any) => {
        if (r.state != 1000) {
            ElNotify({ type: "warning", message: r.message || "请求失败" });
            return;
        }
        const res = r.data;
        if (res === "Success") {
            clearTimeout(timer);
            txtConfirmCode.value = "";
            transferTipText.value = "请验证接收人的手机，他即将接收的账套是：";
            sendMsg.value = "发送验证码";
            acceptMobileShow.value = true;
            canSend = true;
            vMobileBtn.value = false;
            transferBtn.value = true;
            step = 1;
        } else if (res == "Expired") {
            ElNotify({ type: "warning", message: "验证码已过期！" });
        } else {
            ElNotify({ type: "warning", message: "请输入正确的验证码！" });
        }
    });
};
let isTransfer = false;
const transferSubmitFn = () => {
    if (isTransfer) {
        ElNotify({ type: "warning", message: "正在移交，请稍后..." });
        return;
    }
    isTransfer = true;
    if (txtConfirmCode.value == "") {
        ElNotify({ type: "warning", message: "请输入验证码！" });
        isTransfer = false;
        return;
    }
    const phoneNum = acceptMobile.value;
    if (!phoneNum) {
        ElNotify({ type: "warning", message: "亲，请输入用户手机号码！" });
        isTransfer = false;
        return;
    }
    if (!CheckPhone(phoneNum)) {
        ElNotify({ type: "warning", message: "亲，请输入正确的手机号码！" });
        isTransfer = false;
        return;
    }
    if (txtMobile.value == phoneNum) {
        ElNotify({ type: "warning", message: "亲，请勿输入相同号码！" });
        isTransfer = false;
        return;
    }
    request({ url: "/api/ConfirmCode/CheckCodeTime?mobile=" + phoneNum + "&confirmCode=" + txtConfirmCode.value, method: "post" })
        .then((r: IResponseModel<string>) => {
            if (r.state != 1000) {
                ElNotify({ type: "warning", message: r.msg || "亲，验证码验证失败，请稍后重试" });
                return;
            }
            const res = r.data;
            if (res === "Success") {
                transferShow.value = false;
                transferSuccessHandle();
            } else if (res == "Expired") {
                ElNotify({ type: "warning", message: "亲，验证码已过期！" });
                isTransfer = false;
            } else {
                ElNotify({ type: "warning", message: "请输入正确的验证码！" });
                isTransfer = false;
            }
        })
        .catch(() => {
            isTransfer = false;
            ElNotify({ type: "warning", message: "亲，验证码验证失败，请稍后重试" });
        });
};

const transferSubmit = _.debounce(transferSubmitFn, 500);
const transferSuccessHandle = () => {
    ElConfirm("亲，请再次确定是否移交该账套，移交成功后，接收人登录柠檬云即可查看该账套：" + asname.value).then((r: any) => {
        if (r) {
            const phoneNum = acceptMobile.value;
            let url = `/api/Permissions/Transfer?userIdOrMobile=${phoneNum}&permissions=${currentPermission.value}&asId=${asid}`;
            if (window.isProSystem) {
                url = window.eHost + "/wb/valveacc_vip_web" + url;
            }

            const requestParams = {
                url,
                method: "post",
            };
            request({ url: "/api/ConfirmCode/ValidateUserIdOrMobile?mobile=" + phoneNum, method: "post" })
                .then((res: IResponseModel<number>) => {
                    if (res.state !== 1000) {
                        ElNotify({ type: "warning", message: res.msg || "亲，验证用户失败，请稍后重试" });
                        isTransfer = false;
                        return;
                    }
                    if (res.data > 0) {
                        // 已经注册过的手机号
                        hasRegisterHandle(requestParams);
                    } else {
                        // 没有注册过的手机号
                        ElConfirm("亲，您输入的手机不存在，要新建用户吗？").then((r: any) => {
                            if (r) {
                                if (!phoneNum) {
                                    ElNotify({ type: "warning", message: "亲，请输入用户手机号码！" });
                                    isTransfer = false;
                                    return;
                                }
                                if (!CheckPhone(phoneNum)) {
                                    ElNotify({ type: "warning", message: "亲，请输入正确的手机号码！" });
                                    isTransfer = false;
                                    return;
                                }
                                const newUrl = window.accountSrvHost + "/Api/CreateUserForPermission.ashx?CurrentSystemType=1";
                                const newRequestParams = {
                                    url: newUrl,
                                    method: "post",
                                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                                    data: { NewType: "MOBILE", UserId: phoneNum },
                                };
                                noRegisterHandle(newRequestParams, requestParams);
                            } else {
                                isTransfer = false;
                            }
                        });
                    }
                })
                .catch(() => {
                    isTransfer = false;
                    ElNotify({ type: "warning", message: "亲，验证用户失败，请稍后重试" });
                });
        } else {
            isTransfer = false;
            transferShow.value = true;
        }
    });
};
const hasRegisterHandle = (requestParams: any) => {
    request(requestParams)
        .then((r: IResponseModel<string>) => {
            successTransferHandle(r);
        })
        .catch((err: any) => {
            isTransfer = false;
            if (err.response?.status === 400) {
                proOverFlowText.value = err.response.data;
                proOverFlowShow.value = true;
            } else {
                ElNotify({
                    type: "warning",
                    message: "移交账套失败，请稍后重试！",
                });
            }
        });
};

const noRegisterHandle = (newRequestParams: any, requestParams: any) => {
    request(newRequestParams)
        .then((res: IResponseModel<string>) => {
            if (res.state != 1000 || res.data !== "Success") {
                ElNotify({ type: "warning", message: res.msg || "移交账套失败，请稍后重试！" });
                isTransfer = false;
                return;
            }
            request(requestParams)
                .then((r: IResponseModel<string>) => {
                    successTransferHandle(r);
                })
                .catch(() => {
                    isTransfer = false;
                });
        })
        .catch(() => {
            isTransfer = false;
        });
};
const successTransferHandle = (r: IResponseModel<string>) => {
    isTransfer = false;
    if (r.state === 1000) {
        ElNotify({ type: "success", message: "亲，权限移交成功！" });
        transferShow.value = false;
        // !window.isThirdPart && !window.isErp && !isLemonClient() && $("#asToken", top.document).val("");
        setTimeout(function () {
            if (isLemonClient()) {
                getLemonClient().init();
            } else {
                setTopLocationhref(window.jHost + "/App/Default.aspx");
            }
        }, 2000);
    } else if (r.subState === 8) {
        ElNotify({ type: "warning", message: "亲，移交用户的应用创建账套的数量已经超过限额，请联系管理员！" });
    } else {
        ElNotify({ type: "warning", message: r.msg || "亲，权限移交失败！" });
    }
};
const CheckPhone = (phoneNum: string) => {
    const filter = /^(0|86|17951)?1([3-9]|0)[0-9][0-9]{8}$/;
    return filter.test(phoneNum);
};
const handleInit = () => {
    tipShow.value = trialStatusStore.isTrial && trialStatusStore.isExpired && !isThirdPart;
};
handleInit();

</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.main-content {
    height: 100%;
    .slot-mini-content {
        width: 1000px;
        position: relative;
        height: 100%;
        .main-top {
            padding: 24px 30px;
            .asname {
                display: inline-flex;
                padding-top: 4px;
                color: #929292;
                font-size: var(--h3);
                line-height: 22px;
                margin-right: 10px;
                margin-left: 2px;
            }
            a.button {
                display: inline-flex;
                margin-left: auto;
                padding-left: 42px;
                text-align: left;
                width: 74px;
                background-image: url("@/assets/Settings/adduser.png");
                background-repeat: no-repeat;
                background-position: 16px 6px;
            }
        }
        .tip {
            padding: 20px 30px;
            display: flex;
            align-items: center;
            font-size: 16px;
            color: #666666;
            line-height: 24px;
            a.link {
                font-size: 16px;
                margin-left: 16px;
            }
            img {
                height: 17px;
                width: 17px;
                margin-right: 5px;
            }
        }
        .main-center {
            padding: 0 30px 30px;
            padding-bottom: 0;
            overflow: hidden;
            .tb {
                width: 940px;
                // height: 100%;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                .tb-head {
                    height: 36px;
                    background: #f0f0f0;
                    padding-left: 30px;
                    .tb-font {
                        color: var(--font-color);
                        font-size: var(--h4);
                        line-height: 20px;
                        text-align: left;
                        padding: 8px 0;
                        font-weight: 600;
                    }
                    .tb-td {
                        width: 34%;
                        float: left;
                        height: 20px;
                        & + .tb-td {
                            width: 22%;
                            text-align: center;
                        }
                    }
                    .clear {
                        display: block;
                        clear: both;
                    }
                }
                .tb-body {
                    border-bottom: 1px solid #f0f0f0;
                }
                .tb-tr {
                    padding: 10px 0px;
                    border: 1px solid #f0f0f0;
                    border-top: none;
                    padding-left: 30px;
                    height: 20px;
                    &:last-child {
                        border-bottom: none;
                    }
                    .left{
                        text-align: left !important;
                    }
                    .tbody-font {
                        color: var(--font-color);
                        font-size: var(--h4);
                        line-height: 20px;
                        text-align: center;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        &.tb-td {
                            width: 34%;
                            float: left;
                            text-align: left;
                            height: 20px;
                            & + .tb-td {
                                width: 22%;
                                text-align: center;
                            }
                        }
                    }
                }
            }
        }
    }
}

.verify-phone-content {
    color: #404040;
    font-size: 12px;
    .verify-phone-main {
        padding: 20px 70px;
        .txt {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }
        .mt-8 {
            margin-top: 8px;
        }
        .input {
            input {
                .detail-original-input(180px, 30px);
            }
        }
        .per-button {
            color: var(--main-color);
            border: 1px solid var(--main-color);
            padding-top: 1px;
            padding-bottom: 1px;
            &:hover {
                color: var(--white);
            }
        }
        .ml-6 {
            margin-left: 6px;
        }
    }
}
</style>
