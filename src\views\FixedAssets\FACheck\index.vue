<template>
    <div class="content">
        <div class="title">核对总账</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :disabledDateStart="disabledDate"
                                    :disabledDateEnd="disabledDate"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.onlyUnBalance" label="只显示不平"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons" style="text-align: left; margin-top: 0">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <a class="button mr-10" v-permission="['checkledger-canprint']" @click="handlePrint">打印</a>
                    <a class="button" v-permission="['checkledger-canexport']" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <TreeTable
                    ref="treeTable"
                    :data="tableData"
                    :columns="columns"
                    :loading="loading"
                    :hearderRowStyleName="useGreenHeader ? 'green-header' : ''"
                    row-key="projectType"
                    other-field="projectTypeText"
                    first-label="项目"
                    view="FACheck"
                    firstProp="itemTypeText"
                    @go-to-detail="gotoDetail"
                    :style="isErp ? '' : 'border-top: 1px solid #dadada;border-bottom: 1px solid #dadada'"
                    :tableName="setModule"
                >
                    <!-- <template #itemTypeText>
                        <el-table-column label="项目" min-width="100" align="left" header-align="left">
                            <template #default="scope">
                                <template v-if="scope.row.itemTypeText === '差异'">
                                    <a class="link" @click="gotoDetail(scope.row)">{{ scope.row.itemTypeText }}</a>
                                </template>
                                <template v-else>
                                    {{ scope.row.itemTypeText }}
                                </template>
                            </template>
                        </el-table-column>
                    </template> -->
                    <template #initial>
                        <el-table-column 
                            label="期初余额" 
                            min-width="100" 
                            align="right" 
                            header-align="right"
                            prop="initial"
                            :width="getColumnWidth(setModule, 'initial')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.itemTypeText === '差异'">
                                    <a class="link"  v-permission="['checkledgerdedetail-canview']" @click="gotoDetail(scope.row)">{{ formatMoney(scope.row.initial) }}</a>
                                </template>
                                <template v-else>
                                    {{ formatMoney(scope.row.initial) }}
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #debit>
                        <el-table-column 
                            label="借方" 
                            min-width="100" 
                            align="right" 
                            header-align="right"
                            prop="debit"
                            :width="getColumnWidth(setModule, 'debit')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.itemTypeText === '差异'">
                                    <a class="link"  v-permission="['checkledgerdedetail-canview']" @click="gotoDetail(scope.row)">{{ formatMoney(scope.row.debit) }}</a>
                                </template>
                                <template v-else>
                                    {{ formatMoney(scope.row.debit) }}
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #credit>
                        <el-table-column 
                            label="贷方" 
                            min-width="100" 
                            align="right" 
                            header-align="right"
                            prop="credit"
                            :width="getColumnWidth(setModule, 'credit')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.itemTypeText === '差异'">
                                    <a class="link"  v-permission="['checkledgerdedetail-canview']" @click="gotoDetail(scope.row)">{{ formatMoney(scope.row.credit) }}</a>
                                </template>
                                <template v-else>
                                    {{ formatMoney(scope.row.credit) }}
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #total>
                        <el-table-column 
                            label="余额" 
                            min-width="100" 
                            align="right" 
                            header-align="right"
                            prop="total"
                            :resizable="false"
                            :width="getColumnWidth(setModule, 'total')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.itemTypeText === '差异'">
                                    <a class="link" v-permission="['checkledgerdedetail-canview']" @click="gotoDetail(scope.row)">{{ formatMoney(scope.row.total) }}</a>
                                </template>
                                <template v-else>
                                    {{ formatMoney(scope.row.total) }}
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </TreeTable>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "FACheck",
};
</script>
<script setup lang="ts">
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import TreeTable from "@/components/Table/TreeTable.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { ref, reactive, onMounted, onActivated } from "vue";
import { request } from "@/util/service";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IPeriod } from "./types";
import { formatMoney } from "@/util/format";
import { useRoute } from "vue-router";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { dayjs } from "element-plus";

const setModule = "FACheck";
const isErp = ref(window.isErp)
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const currentPeriodInfo = ref("");
const route = useRoute();
let treeTable = ref();

const initPid = ref(0);
const searchInfo = reactive({
    startPid: initPid.value,
    endPid: initPid.value,
    onlyUnBalance: false,
    startMonth: "",
    endMonth: "",
});
const useGreenHeader = ref(false);
const loading = ref(false);

function handleSearch() {
    searchInfo.startPid = periodList.value.find((item) => item.time === searchInfo.startMonth)?.pid || 0;
    searchInfo.endPid = periodList.value.find((item) => item.time === searchInfo.endMonth)?.pid || 0;
    if (searchInfo.endPid < searchInfo.startPid) {
        return ElNotify({
            message: "开始期间不能大于结束期间",
            type: "warning",
        });
    }
    getCurrentInfoTitle();
    getTableData();
    return containerRef.value?.handleClose();
}
function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.startPid = initPid.value;
    searchInfo.endPid = initPid.value;
    searchInfo.onlyUnBalance = false;
}
//打印
function handlePrint() {
    globalPrint(`/api/FACheck/Print?` + getUrlSearchParams(searchInfo));
}

function handleExport() {
    globalExport(`/api/FACheck/Export?` + getUrlSearchParams(searchInfo));
}

const columns = ref<Array<IColumnProps>>([
    // { slot: "itemTypeText" },
    { label: "项目", align: "left", headerAlign: "left", minWidth: 110, prop: "itemTypeText", width: getColumnWidth(setModule, 'itemTypeText') },
    { label: "名称", prop: "name", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'name') },
    { slot: "initial" },
    { slot: "debit" },
    { slot: "credit" },
    { slot: "total" },
]);

const gotoDetail = (row: any) => {
    globalWindowOpenPage(
        `/FixedAssets/FACheckDetail?startPid=${searchInfo.startPid}&endPid=${searchInfo.endPid}&projectType=${row.projectType}&r=${Math.random()}`,
        "资产差异明细"
    );
};

const periodList = ref<IPeriod[]>([]);
function getPeriodApi() {
    request({
        url: `/api/FAPeriod/List?dpcFlag=false`,
        method: "get",
    }).then((res: any) => {
        if (res.state == 1000) {
            periodList.value = res.data.map((item: any) => {  
                const year = item.periodInfo.slice(0, 4);
                const sn = item.periodInfo.indexOf("月") === 7 ? item.periodInfo.slice(5, 7) : item.periodInfo.slice(5, 6) ;
                return {  
                    pid: item.pid,
                    periodInfo: item.periodInfo,    
                    time: year + "" + sn.padStart(2, "0"),
                };  
            });
            getCurrentPeriod().then((r: any) => {
                if (res.state === 1000) {
                    initPid.value = r.data.pid;
                    searchInfo.startPid = r.data.pid;
                    searchInfo.endPid = r.data.pid;

                    if (route.query.pid && route.query.from === "checkout") {
                        const routePid = Number(route.query.pid);
                        if (periodList.value?.findIndex((item) => item.pid === routePid) === -1) {
                            searchInfo.startPid = periodList.value[0].pid;
                            searchInfo.endPid = periodList.value[0].pid;
                        } else {
                            searchInfo.startPid = routePid;
                            searchInfo.endPid = routePid;
                        }
                    }
                    getInitMonth();
                    getCurrentInfoTitle();
                    getTableData();
                }
            });
        }
    });
}

function getCurrentPeriod() {
    return request({
        url: `/api/FAPeriod/Current`,
    });
}

function disabledDate(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

//searchinfo标题
function getCurrentInfoTitle() {
    if (searchInfo.startPid === searchInfo.endPid) {
        currentPeriodInfo.value = periodList.value?.find((item) => item.pid === searchInfo.startPid)?.periodInfo || "";
    } else {
        currentPeriodInfo.value =
            (periodList.value?.find((item) => item.pid === searchInfo.startPid)?.periodInfo || "") +
            "—" +
            (periodList.value?.find((item) => item.pid === searchInfo.endPid)?.periodInfo || "");
    }
}

function getInitMonth() {
    searchInfo.startMonth = periodList.value.find((item) => item.pid === Number(searchInfo.startPid))?.time || "";
    searchInfo.endMonth = periodList.value.find((item) => item.pid === Number(searchInfo.endPid))?.time || "";
}

const tableData = ref<any[]>([]);
function getTableData() {
    loading.value = true;
    const params = {
        startPId: searchInfo.startPid,
        endPId: searchInfo.endPid,
        onlyUnBalance: searchInfo.onlyUnBalance,
    };
    request({
        url: `/api/FACheck/List?` + getUrlSearchParams(params),
    }).then((res: any) => {
        if (res.state == 1000) {
            tableData.value = res.data;
            treeTable.value?.initData(tableData.value);
        }
        loading.value = false;
    });
}
onMounted(() => {
    getPeriodApi();
});
onActivated(()=>{
    if(route.query.checkInitial === 'true'){
        searchInfo.startPid = periodList.value![0].pid;
        searchInfo.endPid = periodList.value![0].pid;
        getInitMonth();
        getTableData()
    }
})
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/FixedAssets/FixedMulti-tabs.less";

.main-center {
    :deep(.el-table__row.el-table__row--level-0) {
        --el-table-row-hover-bg-color: transparent; 
        img {
            cursor: pointer;
        }
    }
}
:deep(.el-table--enable-row-transition .el-table__body td.el-table__cell){
    transition:none !important;
}
body[erp]{
    .main-tool-left{
        :deep(.search-info-selector){
            background-color: var(--main-color);
            color:var(--white);
            background-image: url("@/assets/Icons/down-white.png");
        }
    }
}
</style>
