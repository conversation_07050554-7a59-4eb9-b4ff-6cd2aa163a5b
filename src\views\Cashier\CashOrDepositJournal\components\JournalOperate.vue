<template>
  <div class="button-content" :class="isErp ? 'erp' : ''">
    <div class="item" v-if="props.jType === '1020' && !isErp" v-show="bankAndCompanyShow">
      <a class="button" @click="autoBankJournal">银企互联</a>
    </div>
    <span class="ml-10"></span>
    <Dropdown
      btnTxt="打印列表"
      class="mr-10 print"
      :width="isErp ? 96 : 84"
      :downlistWidth="isErp ? 96 : 84"
      v-permission="props.jType === '1020' ? ['depositjournal-canprint'] : ['cashjournal-canprint']">
      <li @click="handleListPrint(0)">直接打印</li>
      <li @click="printListSettings">打印设置</li>
    </Dropdown>
    <Dropdown
      btnTxt="打印凭据"
      class="mr-10 print"
      :width="isErp ? 96 : 84"
      :downlistWidth="isErp ? 96 : 84"
      v-permission="props.jType === '1020' ? ['depositjournal-canprint'] : ['cashjournal-canprint']">
      <li @click="handleReceiptPrint(0)">直接打印</li>
      <li @click="printReceiptSettings">打印设置</li>
    </Dropdown>
    <Dropdown
      btnTxt="导入"
      :class="props.jType === '1020' ? 'mr-10 import depositjournal' : 'mr-10 import'"
      v-permission="props.jType === '1020' ? ['depositjournal-canimport'] : ['cashjournal-canimport']">
      <li v-if="props.jType === '1020'" @click="selectImportMethod('receipt')">银行回单(PDF)导入</li>
      <li v-if="props.jType === '1020'" @click="selectImportMethod('bank')">银行对账单(Excel)导入</li>
      <li @click="selectImportMethod('normal')">{{ props.jType === '1020' ? '模板导入' : '一键导入' }}</li>
      <li @click="selectImportMethod('alipay')" v-show="!isHideBarcode">支付宝导入</li>
      <li @click="selectImportMethod('wechat')">微信导入</li>
      <li @click="selectImportMethod('enterpriseWechat')">企微导入</li>
      <li @click="selectImportMethod('subsidiaryLedger')">明细账导入</li>
    </Dropdown>
    <Dropdown
      btnTxt="导出"
      class="mr-10 export"
      v-permission="props.jType === '1020' ? ['depositjournal-canexport'] : ['cashjournal-canexport']">
      <li @click="handleExport">当前查询数据</li>
      <li @click="handleExportAll">所有账户全部数据</li>
    </Dropdown>
    <Dropdown
      btnTxt="批量操作"
      class="operate mr-10"
      v-if="getEditPerssion() || checkPermission(['voucher-candelete'])">
      <li v-permission="props.jType === '1020' ? ['depositjournal-candelete'] : ['cashjournal-candelete']" @click="handleBatchDelete">
        批量删除
      </li>
      <li @click="openBatchUpdateDialog(CashAAType.Description)" v-if="getEditPerssion()">批量修改摘要</li>
      <li @click="handleBatchDeleteVoucher" v-if="checkPermission(['voucher-candelete']) && !isErp">删除关联凭证</li>
      <li @click="openBatchUpdateDialog(CashAAType.IEType)" v-if="getEditPerssion()">指定收支类别</li>
      <li @click="openBatchUpdateDialog(CashAAType.Unit)" v-if="getEditPerssion()">指定往来单位</li>
      <li @click="openBatchUpdateDialog(CashAAType.Project)" v-if="getEditPerssion()">指定项目</li>
      <li @click="openBatchUpdateDialog(CashAAType.Department)" v-if="getEditPerssion()">指定部门</li>
    </Dropdown>
        <a class="button mr-10" v-if="isErp && checkPermission(['businessvoucher-canview'])" @click="genertaeErpVoucher">生成凭证</a>
        <el-tooltip effect="light" content="本按钮只支持跳转业务生成凭证页面，单据的筛选和勾选不会带入" placement="top-start">
            <el-icon v-if="isErp && checkPermission(['businessvoucher-canview'])" class="el-icon-question" color="#3385ff"><QuestionFilled /></el-icon>
        </el-tooltip>
    <Dropdown btnTxt="关联凭证" class="mr-10" :downlistWidth="84" v-if="checkCanRelateVoucher">
      <li @click="handleRelateVoucher">关联凭证</li>
      <li @click="handleDisrelateVoucher">取消关联</li>
    </Dropdown>
    <div
      v-if="generateVoucherAllCan && !isErp"
      class="item"
      style="position: relative; display: flex; align-items: center; flex-wrap: nowrap"
      @mouseleave="() => (voucherFromJournalDownList = false)">
      <a class="button solid-button" style="float: left" @click="openVoucherFromJournal">生成凭证</a>
      <a class="button solid-button down-click" style="float: left" @mouseenter="() => (voucherFromJournalDownList = true)"></a>
      <div style="width: 106px; top: 28px" class="downlist" v-show="voucherFromJournalDownList">
        <li @click="showJournalSettings">生成凭证设置</li>
        <li @click="showJournalTemplate">凭证模板设置</li>
      </div>
    </div>
    <div
      v-else-if="canGenerateVoucher"
      class="item ml-10"
      v-show="!isErp"
      style="position: relative; display: flex; align-items: center; flex-wrap: nowrap"
      @mouseleave="() => (voucherFromJournalDownList = false)">
      <a class="button solid-button" style="float: left" @click="openVoucherFromJournal">生成凭证</a>
      <a class="button solid-button down-click" style="float: left" @mouseenter="() => (voucherFromJournalDownList = true)"></a>
      <div style="width: 106px; top: 28px" class="downlist" v-show="voucherFromJournalDownList">
        <li @click="showJournalSettings">生成凭证设置</li>
      </div>
    </div>
    <a class="button solid-button ml-10 large-1" v-if="canGenerateVoucherSetting" @click="showJournalTemplate">凭证模板设置</a>
  </div>
  <DialogBatchOperate
    ref="dialogBatchOperateRef"
    v-model:override="override"
    @saveIEType="batchSaveIEType"
    @saveUnit="batchSaveUnit"
    @saveAAType="batchSaveAAType"
    @saveDescription="batchSaveDescription" />
  <DialogImport
    ref="dialogImportRef"
    :jType="props.jType"
    :cdAccount="props.cdAccount"
    :cdAccountList="props.cdAccountList"
    @edit-date="handleEditDate"
    @change-acid="handleChangeAcid"
    @change-show-disabled="handleChangeShowDisabled"
    @import-success="handleImportSuccess" />
  <!-- 关联凭证 -->
  <DialogRelateVoucher
    ref="dialogRelateVoucherRef"
    :checkoutDate="checkoutDate"
    :cdAccount="cdAccount"
    :jType="jType"
    @search="handleSearch" />
  <JouralPrint
    v-model:printDialogShow="printListDialogVisible"
    title="日记账列表打印"
    :printData="printListInfo"
    :dir-disabled="props.getBaseParams().showAll"
    :otherOptions="listOtherOptions"
    @currentPrint="handleListPrint(3)" />
  <JouralPrint
    v-model:printDialogShow="printReceiptInfoDialogVisible"
    title="日记账凭据打印"
    :dirShow="false"
    :printData="printReceiptInfoInfo"
    :otherOptions="receiptOtherOptions"
    @currentPrint="handleReceiptPrint(3)">
    <template #custom>
      <div class="print-item">
        <div class="print-item-label">打印纸张：</div>
        <div class="print-item-field">
          <Select v-model="printReceiptInfoInfo.pageType">
            <Option label="A4两版" :value="1"></Option>
            <Option label="A4三版" :value="2"></Option>
          </Select>
        </div>
      </div>
      <div class="print-item font-size" v-if="isErp">
        <div class="print-item-label">打印字体：</div>
        <div class="print-item-field">
        <el-select v-model="printReceiptInfoInfo.font" :teleported="false">
          <el-option label="宋体" value="default"></el-option>
          <el-option label="黑体" value="black"></el-option>
          <el-option label="Arial" value="arial"></el-option>
        </el-select>
        </div>
        <span class="tip ml-10">针式打印机推荐使用Arial</span>
      </div>
    </template>
  </JouralPrint>

  <!-- 删除关联凭证 -->
  <DialogTip 
    ref="dialogDeleteVoucherRef"
    :tipTitle="'亲，确认要删除凭证吗？'"
    :tipInfo="'删除凭证后，凭证与资金、发票、资产等模块单据的关联关系也将删除'"
    @confirm="deleteVoucherConfrim"
  >
  </DialogTip>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onActivated, watchEffect } from "vue";
import {
  getUrlSearchParams,
  globalFormPost,
  globalWindowOpenPage,
  globalExport,
  globalPrint,
  globalDirectPrint,
  globalPostPrint,
} from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getCookie, setCookie } from "@/util/cookie";
import { checkPermission } from "@/util/permission";
import { getGlobalLodash } from "@/util/lodash";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { ElConfirm } from "@/util/confirm";
import { CashAAType } from "../types";
import { dayjs } from "element-plus";
import { getLocalStorage, setLocalStorage } from "@/util/localStorageOperate";
import usePrint from "@/hooks/usePrint";

import type { ICDAccountItem } from "@/views/Cashier/components/types";
import type { ITableItem, ImportType, IUnitUpdateParams, IPayMethod } from "../types";

import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import JouralPrint from "@/components/PrintDialog/index.vue";
import DialogBatchOperate from "./DialogBatchOperate.vue";
import DialogImport from "./DialogImport.vue";
import DialogRelateVoucher from "./DialogRelateVoucher.vue";
import DialogTip from "@/components/Dialog/DialogTip/index.vue";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { useLoading } from "@/hooks/useLoading";

const { cloneDeepWith } = getGlobalLodash();
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);

const props = defineProps<{
  cdAccount: string;
  jType: "1010" | "1020";
  cdAccountList: Array<ICDAccountItem>;
  selectionList: Array<ITableItem>;
  allCdAccountList: Array<ICDAccountItem>;
  checkoutDate: Date;
  payList: Array<IPayMethod>;
  getBaseParams: () => any;
  clearSelection: () => void;
}>();
const emit = defineEmits<{
  (event: "show-generate-voucher"): void;
  (event: "show-journal-template"): void;
  (event: "show-journal-settings"): void;
  (event: "search"): void;
  (event: "refresh-ietype"): void;
  (event: "import-success"): void;
  (event: "edit-date", _s: string, _e: string): void;
  (event: "change-acid", id: number | string): void;
  (event: "change-show-disabled", showDisabled: boolean): void;
}>();

function handleSearch() {
  emit("search");
  props.clearSelection();
}

// 打印
//日记账列表打印逻辑不一致，考虑不抽成，目前只看到这一个地方打印出入大
const {
  printDialogVisible: printListDialogVisible,
  printInfo: printListInfo,
  otherOptions: listOtherOptions,
  updataPritnInfo: updataPritnlistInfo,
} = usePrint("joural", "", {}, true);
onActivated(() => {
  printListInfo.value = updataPritnlistInfo()
  printReceiptInfoInfo.value = updataPritnReceiptInfo()
});

watchEffect(() => {
  printListInfo.value.direction = props.getBaseParams().showAll ? 1 : 0;
});
function handleListPrint(type: number) {
  const params = props.getBaseParams();
  // if (params.showAll) printListInfo.value.direction = 1;
  if (!isErp.value && !isHideBarcode.value) {
    const showAll = params.showAll;
    delete params.showAll;
    if (type === 0) {
      globalDirectPrint("/api/Journal/PrintCDTable", "post", { ...params, showAll, seniorModelJson: JSON.stringify(printListInfo.value) }, "打印中，请稍候...");
    } else {
      setLocalStorage("jouralPrint", JSON.stringify(printListInfo.value));
      globalPostPrint("/api/Journal/PrintCDTable", { ...params, showAll, seniorModelJson: JSON.stringify(printListInfo.value) });
    }
  } else {
    request({ url: "/api/Journal/PrintCDTable" , method:'post', data: { ...params, seniorModelJson: JSON.stringify(printListInfo.value) } })
      .then((res: IResponseModel<string>) => {
        if (res.state === 9999) {
          let msg = res.msg.replace("在位置 0 处没有任何行。", "账户不存在，请刷新页面重试！");
          ElNotify({ message: msg, type: "warning" });
          return;
        }
        if (type === 0) {
          globalDirectPrint("/api/Journal/PrintCDTable", "post", {
            ...params,
            seniorModelJson: JSON.stringify(printListInfo.value),
          },"打印中，请稍候...");
        } else {
          setLocalStorage("jouralPrint", JSON.stringify(printListInfo.value));
          globalPostPrint("/api/Journal/PrintCDTable", {
            ...params,
            seniorModelJson: JSON.stringify(printListInfo.value),
          });
        }
      })
      .catch(() => {
        ElNotify({ message: "打印失败，请重试", type: "warning" });
      });
  }
}
function printListSettings() {
  if (props.getBaseParams().showAll) printListInfo.value.direction = 1;
  printListDialogVisible.value = true;
}

const receiptExtraInfo = {
  isPrintCashierName: true,
  isPrintDepartment: false,
  isPrintProject: false,
  font: "default",
  isShowEndLine: true,
  pageType: 2,
  bottomTextList: ["会计：","#CASHIER", "经手人："],
};
function validateReceiptPrint() {
  if (props.selectionList.length === 0) {
    ElNotify({ message: "请选择凭据后打印！", type: "warning" });
    return false;
  }
  return true;
}
const {
  printDialogVisible: printReceiptInfoDialogVisible,
  printInfo: printReceiptInfoInfo,
  otherOptions: receiptOtherOptions,
  updataPritnInfo: updataPritnReceiptInfo,
} = usePrint("receipt", "", receiptExtraInfo, false, false);
receiptOtherOptions.value = [
  { key: "isPrintDepartment", label: "打印部门" },
  { key: "isPrintProject", label: "打印项目" },
  { key: "isShowEndLine", label: "打印分割线" },
  ...receiptOtherOptions.value,
];
if (!getLocalStorage("receiptPrint")) {
  let accountantIndex = printReceiptInfoInfo.value.bottomTextList.findIndex((item) => item === "会计：");
  if (accountantIndex !== -1) {
    if (getCookie("JournalIsPrintAccountantName") + "" === "true") {
      printReceiptInfoInfo.value.bottomTextList[accountantIndex] = "会计：" + decodeURI(getCookie("JournalAccountantName"));
      } else if(getCookie("JournalIsPrintAccountantName") + "" === "false"){
        printReceiptInfoInfo.value.bottomTextList[accountantIndex] = "";
      }
  }
  
  let cashierIndex = printReceiptInfoInfo.value.bottomTextList.findIndex((item) => item === "#CASHIER");
  if (cashierIndex !== -1) {
    if (getCookie("JournalIsPrintCashierName") + "" === "true") {
      printReceiptInfoInfo.value.bottomTextList[cashierIndex] = "#CASHIER";
    } else if(getCookie("JournalIsPrintCashierName") + "" === "false"){
      printReceiptInfoInfo.value.bottomTextList[cashierIndex] = "";
    }
  }

  printReceiptInfoInfo.value.isPrintCashierName = !(getCookie("JournalIsPrintCashierName") + "" === "false");
  let usedIndex = printReceiptInfoInfo.value.bottomTextList.findIndex((item) => item === "经手人：");
  if (usedIndex !== -1) {
    if (getCookie("JournalIsPrintUsedName") + "" === "true") {
      printReceiptInfoInfo.value.bottomTextList[usedIndex] = "经手人：" + decodeURI(getCookie("JournalUsedName"));
    } else if(getCookie("JournalIsPrintUsedName") + "" === "false"){
      printReceiptInfoInfo.value.bottomTextList[usedIndex] = "";
    }
  }
  printReceiptInfoInfo.value.isPrintDepartment = getCookie("JournalIsPrintDepartment") + "" === "true";
  printReceiptInfoInfo.value.isPrintProject = getCookie("JournalIsPrintProject") + "" === "true";
}

function printReceiptSettings() {
  printReceiptInfoDialogVisible.value = true;
}

function handleReceiptPrint(type: number) {
  if (!validateReceiptPrint()) return;
  const tArray = new Array();
  console.log(props.selectionList);
  var Date = props.selectionList[0].cd_date.split(" ")[0];
  var cdAccount = props.selectionList[0].cd_account;
  var created_dates = "";
  var i = 0;
  tArray[i] = new Object();

  const checkedItems = cloneDeepWith(props.selectionList);
  checkedItems.forEach((item: ITableItem) => {
    item.cd_date = item.cd_date.split(" ")[0];
    if (dayjs(Date).valueOf() != dayjs(item.cd_date).valueOf() || cdAccount != item.cd_account) {
      tArray[i].Date = Date;
      tArray[i].CdAccount = cdAccount;
      tArray[i].created_dates = created_dates;
      i++;
      tArray[i] = new Object();
      Date = item.cd_date;
      created_dates = item.created_date;
      cdAccount = item.cd_account;
    } else {
      if (created_dates == "") created_dates += item.created_date;
      else created_dates += "," + item.created_date;
    }
  });
  tArray[i].Date = Date;
  tArray[i].created_dates = created_dates;
  tArray[i].CdAccount = cdAccount;
  const params = {
    jtype: props.jType,
    cdaccount: props.cdAccount === "all" ? props.cdAccountList.map((item) => item.ac_id).join(",") : props.cdAccount,
    seniorModelJson: JSON.stringify(printReceiptInfoInfo.value),
    arrayLineSns: JSON.stringify(tArray),
  };
  const urlPath = props.jType === "1010" ? "/api/Journal/PrintCashBatch" : "/api/Journal/PrintDepositBatch";
  if (type === 0) {
    globalDirectPrint(urlPath, "post", params,"打印中，请稍候...");
  } else {
    setLocalStorage("receiptPrint", JSON.stringify(printReceiptInfoInfo.value));
    globalPostPrint(urlPath, params);
  }
}
// 导出
function handleExportAll() {
  const params = {
    ...props.getBaseParams(),
    cd_account: props.allCdAccountList.map((item) => item.ac_id).join(","),
    cd_accountdisplay: "全部账户",
    exportAll: "all",
  };
  params.vgId = "";
  params.startVNum = "";
  params.endVNum = "";
  params.startVDate = "";
  params.endVDate = "";
  if (!isErp.value && !isHideBarcode.value) {
    const showAll = params.showAll;
    delete params.showAll;
    globalFormPost("/api/Journal/ExportCDTable?showAll=" + showAll, params, "export");
  } else {
    request({ url: "/api/Journal/ExportCDTable?" + getUrlSearchParams(params) })
      .then((res: IResponseModel<string>) => {
        if (res.state === 9999) {
          let msg = res.msg.replace("在位置 0 处没有任何行。", "账户不存在，请刷新页面重试！");
          ElNotify({ message: msg, type: "warning" });
          return;
        }
        globalExport("/api/Journal/ExportCDTable?" + getUrlSearchParams(params));
      })
      .catch(() => {
        ElNotify({ message: "导出失败，请重试", type: "warning" });
      });
  }
}
function handleExport() {
  const params = props.getBaseParams();
  if (!params.date_s || !params.date_e) {
    ElNotify({ message: "起止日期不能为空", type: "warning" });
    return;
  }
  const showAll = params.showAll;
  const urlPath = props.jType === "1010" ? "/api/Journal/ExportCashTable?" : "/api/Journal/ExportDepositTable?";
  if (!isErp.value && !isHideBarcode.value) {
    delete params.showAll;
    globalFormPost(urlPath + "showAll=" + showAll, params, "export");
  } else {
    request({ url: urlPath + getUrlSearchParams(params) })
      .then((res: IResponseModel<string>) => {
        if (res.state === 9999) {
          let msg = res.msg.replace("在位置 0 处没有任何行。", "账户不存在，请刷新页面重试！");
          ElNotify({ message: msg, type: "warning" });
          return;
        }
        globalExport(urlPath + getUrlSearchParams(params));
      })
      .catch(() => {
        ElNotify({ message: "导出失败，请重试", type: "warning" });
      });
  }
}
// 批量操作
const override = ref(true);
const dialogBatchOperateRef = ref<InstanceType<typeof DialogBatchOperate>>();
function canNext() {
  if (props.selectionList.length === 0) {
    ElNotify({ message: "请先选择日记账数据！", type: "warning" });
    return false;
  }
  return true;
}
function handleBatchDelete() {
  if (!canNext()) return;
  for (let i = 0; i < props.selectionList.length; i++) {
    const item = props.selectionList[i];
    let jDate = new Date(item.cd_date.replace(/-/g, "/"));
    if (jDate < props.checkoutDate) {
      ElNotify({ type: "warning", message: "已结账期间资金数据不能删除！" });
      return;
    }
  }

  let jDate = new Date(props.selectionList[0].cd_date.replace(/-/g, "/"));
  if (jDate < props.checkoutDate) {
    ElNotify({ type: "warning", message: "已结账期间资金数据不能删除！" });
    return;
  }

  for (var i = 0; i < props.selectionList.length; i++) {
    if (props.selectionList[i].ie_type == "-1") {
      ElNotify({ type: "warning", message: "内部转账单据不能在日记账删除，请在内部转账删除！" });
      return;
    }
    if (props.selectionList[i].v_num !== "") {
      ElNotify({ type: "warning", message: "资金数据已生成凭证，请先删除凭证！" });
      return;
    }
    if (props.selectionList[i].erp_offset == "1") {
      ElNotify({ type: "warning", message: "资金数据已经关联单据，请先取消关联！" });
      return;
    }
  }

  ElConfirm("亲，确认要删除吗?").then((r: any) => {
    if (r) {
      const list = props.selectionList.map((item: ITableItem) => {
        return {
          cdAccount: item.cd_account,
          date: item.cd_date,
          created_date: item.created_date,
          line_sn: item.line_sn,
        };
      });
      request({
        url: "/api/Journal/" + (props.jType === "1010" ? "CashBatch" : "DepositBatch"),
        method: "delete",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { arrayLineSn: list },
      }).then((res: IResponseModel<string>) => {
        if (res.state == 1000) {
          ElNotify({ message: "删除成功", type: "success" });
          handleSearch();
        } else {
          ElNotify({ message: "删除失败", type: "warning" });
        }
      });
    }
  });
}
//删除关联凭证
const getEditPerssion = () => {
  return props.jType === '1020' ? checkPermission(['depositjournal-canedit']) : checkPermission(['cashjournal-canedit']);
}
const dialogDeleteVoucherRef = ref();
let arrayLineSn: ITableItem[] = []; // 生成凭证数据
function handleBatchDeleteVoucher() {
    arrayLineSn = []; // 生成凭证数据
    for (let i = 0; i < props.selectionList.length; i++) {
        if (props.selectionList[i].v_id !== "") {
            arrayLineSn.push(props.selectionList[i]);
        }
    }
    if (props.selectionList.length === 0 || arrayLineSn.length === 0) {
        ElNotify({ message: "请先选择已关联凭证的日记账数据！", type: "warning" });
        return;
    }
    dialogDeleteVoucherRef.value?.showDialog();
}
function deleteVoucherConfrim() {
    //直接调用凭证批量删除的接口
    const queryParamsList = arrayLineSn.map((item) => {
        return {
            vid: item.v_id,
            pid: item.p_id,
        };
    });
    useLoading().enterLoading("努力加载中，请稍后...");
    const url = props.jType === '1010' ? "/api/Voucher/CashJournalBatch" : "/api/Voucher/DepositJournalBatch";
    request({
        url,
        method: "delete",
        headers: { "content-type": "application/json" },
        data: JSON.stringify(queryParamsList),
    }).then((r: IResponseModel<any>) => {
        useLoading().quitLoading();
        if (r.state !== 1000) {
            ElNotify({ type: "warning", message: r.msg || "亲，删除失败啦！请联系侧边栏客服！" });
            return;
        }
        const result = r.data;
        if (result != undefined) {
            if (result.isOvertime === true) {
                ElNotify({ type: "warning", message: "数据太多啦，服务器太忙了，请10分钟后分批删除哦~" });
            } else {
                ElNotify({
                    type: "success",
                    message: `成功：${result.batchOperationResult.success}，跳过：${result.batchOperationResult.jump}（已经审核、已经结账的凭证，将会被跳过！）`,
                });
                dispatchReloadAsubAmountEvent();
                window.dispatchEvent(new CustomEvent("reloadVoucherList"));
                handleSearch();
            }
        } else {
            ElNotify({ type: "warning", message: "亲，删除失败啦！请联系侧边栏客服！" });
        }
    });
}

function openBatchUpdateDialog(type: CashAAType) {
  if (!canNext()) return;
  dialogBatchOperateRef.value?.handleOpenDialog(type);
}
function genertaeErpVoucher() {
    const billType = props.jType === "1010" ? "3010" : "3020";
    const params = { billType, searchStartDate: props.getBaseParams().date_s, searchEndDate: props.getBaseParams().date_e };
    globalWindowOpenPage("/Erp/BusinessVoucher?" + getUrlSearchParams(params), "业务生成凭证");
}
function batchSaveIEType(id: string) {
  const params = {
    arrayLineSn: [],
    flag: CashAAType.IEType,
    updateInfo: { ie_type: id },
  };
  handleBatchSave(params, "收支类别");
}
function batchSaveUnit(info: IUnitUpdateParams, aaType: number) {
    const params = {
        arrayLineSn: [],
        flag: aaType,
        updateInfo: {
            opposite_party: info.opposite_party,
            opposite_party_int: info.opposite_party_int,
        },
    };
    if (isErp.value) {
        handleBatchSave(params, "往来单位", info.departmentId);
    } else {
        handleBatchSave(params, "往来单位");
    }
}
function batchSaveAAType(id: string, aaType: CashAAType.Department | CashAAType.Project) {
  const params = {
    arrayLineSn: [],
    flag: aaType,
    updateInfo: { [aaType === CashAAType.Project ? "project" : "department"]: id },
  };
  const name = aaType === CashAAType.Project ? "项目" : "部门";
  handleBatchSave(params, name);
}
function batchSaveDescription(description: string) {
  const params = {
    arrayLineSn: [],
    flag: CashAAType.Description,
    updateInfo: { description },
  };
  handleBatchSave(params, "摘要");
}
function handleBatchSave(params: any, name: string, departmentId: string = "") {
    let arrayLineSn: ITableItem[] = [];
    for (let i = 0; i < props.selectionList.length; i++) {
        // 超出期间数据不能修改
        let jDate = new Date(props.selectionList[i].cd_date.replace(/-/g, "/"));
        if (jDate < props.checkoutDate) {
            continue;
        }
        // 内部转账数据不能修改
        if (props.selectionList[i].ie_type === "-1") {
            continue;
        }
        // 生成凭证数据不能修改
        if (props.selectionList[i].v_id !== "") {
            continue;
        }
        // 关联单据数据不能修改
        if (props.selectionList[i].erp_offset === "1") {
            continue;
        }
        // 判断收支类别
        if (params.flag == 10000) {
            const paymethodDisplay = props.payList.find((item) => item.subkey == params.updateInfo.ie_type)?.value2 || "";
            if (paymethodDisplay.startsWith("收")) {
                const { income, income_standard } = props.selectionList[i];
                if (!income || Number(income) === 0 || !income_standard || Number(income_standard) === 0) {
                    continue;
                }
            } else if (paymethodDisplay.startsWith("支")) {
                const { expenditure, expenditure_standard } = props.selectionList[i];
                if (!expenditure || Number(expenditure) === 0 || !expenditure_standard || Number(expenditure_standard) === 0) {
                    continue;
                }
            }
        }
        // 判断是否覆盖
        else if (!override.value) {
            if (params.flag == 10001 || params.flag == 10002 || params.flag == 10003) {
                if (props.selectionList[i].opposite_party != "") {
                    continue;
                }
            } else if (params.flag == CashAAType.Department) {
                if (props.selectionList[i].department != "") {
                    continue;
                }
            } else if (params.flag == CashAAType.Project) {
                if (props.selectionList[i].project != "") {
                    continue;
                }
            }
        }
        arrayLineSn.push(props.selectionList[i]);
    }
    params.arrayLineSn = arrayLineSn.map((item: ITableItem) => {
        return {
            cdAccount: item.cd_account,
            date: item.cd_date,
            created_date: item.created_date,
            line_sn: item.line_sn,
        };
    });

    if (params.arrayLineSn.length === 0) {
        if (isErp.value) {
            ElNotify({
                type: "warning",
                message: `成功：0，跳过：${props.selectionList.length}(已结账、已生成凭证、内部转账、已核销的数据已跳过)`,
            });
        } else {
            ElNotify({
                type: "success",
                message: `成功：0，跳过：${props.selectionList.length}(已结账、已生成凭证、内部转账的数据已跳过)`,
            });
        }

        return;
    }
    if (isErp.value && name === "往来单位") {
        handleBatchSaveDepart(arrayLineSn, departmentId);
    }

    let IENameItem = props.payList.filter((item) => item.subkey === params.updateInfo.ie_type);
    batchSaveApi(params)
        .then((res: IResponseModel<string>) => {
            if (res.state == 1000 && res.data === "Success") {
                if (window.isErp) {
                    ElNotify({
                        type: "success",
                        message: `成功：${params.arrayLineSn.length}，跳过：${
                            props.selectionList.length - params.arrayLineSn.length
                        }(已结账、已生成凭证、内部转账、已核销的数据已跳过)`,
                    });
                } else {
                    ElNotify({
                        type: "success",
                        message: `成功：${params.arrayLineSn.length}，跳过：${
                            props.selectionList.length - params.arrayLineSn.length
                        }(已结账、已生成凭证、内部转账的数据已跳过)`,
                    });
                }
                handleSearch();
            } else if (res.state == 9999 && res.msg == "NotLeafIEType") {
                ElConfirm(`收支类别"${IENameItem[0].value2.slice(2)}"已存在下级类别，请使用下级类别`, true).then((r) => {
                    if (r) {
                        dialogBatchOperateRef.value?.handleOpenDialog(CashAAType.IEType);
                        emit("refresh-ietype");
                    }
                });
            } else if (res.state == 9999 && res.msg == "DisableIEType") {
                ElConfirm(`收支类别已被禁用，请刷新页面重试！`, true).then((r) => {
                    if (r) {
                        dialogBatchOperateRef.value?.handleOpenDialog(CashAAType.IEType);
                        emit("refresh-ietype");
                    }
                });
            } else if (res.data === "Used") {
                ElNotify({ type: "warning", message: "资金数据已生成凭证，请先删除凭证！" });
            } else if (res.data === "OffSet") {
                ElNotify({ type: "warning", message: "资金数据已核销，不能修改！" });
            } else {
                ElNotify({ type: "warning", message: "更新失败了，请刷新页面重试" });
            }
        })
        .catch(() => {
            ElNotify({ message: "批量指定" + name + "失败", type: "warning" });
        });
}
function handleBatchSaveDepart(arrayLineSn: ITableItem[], departmentId: string) {
    let filterList: ITableItem[] = arrayLineSn.filter((item) => item.department_name === "");
    if (filterList.length > 0) {
        let searchList = filterList.map((item: ITableItem) => {
            return {
                cdAccount: item.cd_account,
                date: item.cd_date,
                created_date: item.created_date,
                line_sn: item.line_sn,
            };
        });
        const departParams = {
            arrayLineSn: searchList,
            flag: CashAAType.Department,
            updateInfo: { department: departmentId },
        };
        batchSaveApi(departParams);
    }
}
function batchSaveApi(data: any) {
    return request({
        url: "/api/Journal/" + (props.jType === "1010" ? "CashBatch" : "DepositBatch"),
        method: "put",
        data,
    });
}
// 关联凭证
interface IArrayLineSn {
  cdAccount: string;
  date: string;
  created_date: string;
  line_sn: string;
}
const checkCanRelateVoucher = computed(() => {
  const permission = props.jType === "1010" ? "cashjournal-canrelatevoucher" : "depositjournal-canrelatevoucher";
  return !isErp.value && checkPermission([permission]);
});
const dialogRelateVoucherRef = ref<InstanceType<typeof DialogRelateVoucher>>();
function handleRelateVoucher() {
  if (!canNext()) return;
  const lineSnList: Array<IArrayLineSn> = getLineSnListList(true);
  const parameters = {
    lineSnList,
    selectionList: props.selectionList,
    skip: props.selectionList.length - lineSnList.length,
  };
  lineSnList.sort((a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf());
  const dateInfo = lineSnList.length > 0 ? { _s: lineSnList[0].date, _e: lineSnList[lineSnList.length - 1].date } : { _s: "", _e: "" };
  dialogRelateVoucherRef.value?.handleOpenDialog(parameters, dateInfo);
}
function handleDisrelateVoucher() {
  if (!canNext()) return;
  ElConfirm("亲，确定要取消该日记账与凭证的关联关系吗？").then((r: boolean) => {
    if (r) {
      for (let i = 0; i < props.selectionList.length; i++) {
        if (props.selectionList[i].v_id === "") {
          ElNotify({ message: "未生成/未关联凭证的日记账不可取消关联凭证！", type: "warning" });
          return;
        }
      }
      const lineSnList: Array<IArrayLineSn> = getLineSnListList(false);
      if (lineSnList.length === 0) {
        ElNotify({
          type: "success",
          message: `成功：0，跳过：${props.selectionList.length}(已结账、内部转账的数据已跳过)`,
        });
        return;
      }
      const params = {
        isNeedSaveToVoucher: false,
      };
      const url = props.jType === "1010" ? "/api/Journal/CashUnbindVoucherBatch" : "/api/Journal/DepositUnbindVoucherBatch";
      request({ url, method: "post", data: lineSnList, params })
        .then((res: IResponseModel<string>) => {
          if (res.state !== 1000 || res.data !== "Success") {
            ElNotify({ message: "取消关联凭证失败", type: "warning" });
            return;
          }
          const successCount = lineSnList.length;
          const skipCount = props.selectionList.length - successCount;
          ElNotify({
            type: "success",
            message: `成功：${successCount}，跳过：${skipCount}(已结账、内部转账的数据已跳过)`,
          });
          window.dispatchEvent(new CustomEvent("reloadVoucherList"));
          handleSearch();
        })
        .catch(() => {
          ElNotify({ message: "取消关联凭证失败", type: "warning" });
        });
    }
  });
}
function getLineSnListList(jumpHasVoucher: boolean) {
  const lineSnList: Array<IArrayLineSn> = [];
  for (let i = 0; i < props.selectionList.length; i++) {
    // 已结账
    let jDate = new Date(props.selectionList[i].cd_date.replace(/-/g, "/"));
    if (jDate < props.checkoutDate) continue;
    // 已生成凭证
    if (jumpHasVoucher && props.selectionList[i].p_id !== "" && props.selectionList[i].v_id !== "") continue;
    // 内部转账
    if (props.selectionList[i].ie_type === "-1") continue;
    lineSnList.push({
      cdAccount: props.selectionList[i].cd_account,
      date: props.selectionList[i].cd_date,
      created_date: props.selectionList[i].created_date,
      line_sn: props.selectionList[i].line_sn,
    });
  }
  return lineSnList;
}
// 生成凭证相关
const voucherFromJournalDownList = ref(false);
let saveFlag = true;
function openVoucherFromJournal() {
  if (saveFlag) {
    saveFlag = false;
    emit("show-generate-voucher");
  }
  setTimeout(() => {
    saveFlag = true;
  }, 1000);
}
function showJournalTemplate() {
  emit("show-journal-template");
}
function showJournalSettings() {
  emit("show-journal-settings");
}
// 导入
const dialogImportRef = ref<InstanceType<typeof DialogImport>>();
function selectImportMethod(type: ImportType) {
  dialogImportRef.value?.selectImportMethod(type);
}
function handleEditDate(start: string, end: string) {
  emit("edit-date", start, end);
}
function handleChangeAcid(id: number | string) {
  emit("change-acid", id);
}
function handleChangeShowDisabled(showDisabled: boolean) {
  emit("change-show-disabled", showDisabled);
}
function handleImportSuccess() {
  emit("import-success");
}
// 银企互联
function autoBankJournal() {
  globalWindowOpenPage("/Cashier/BankAndCompany", "银企互联");
}
const canGenerateVoucher = ref(false);
const canGenerateVoucherSetting = ref(false);
const generateVoucherAllCan = ref(false);
const bankAndCompanyShow = ref(false);
function handleInit() {
  const basicAuth = props.jType === "1020" ? "depositjournal" : "cashjournal";
  if (checkPermission([basicAuth + "-cancreatevoucher"]) && checkPermission(["journalvouchertemplate-canview"])) {
    generateVoucherAllCan.value = true;
  } else if (checkPermission([basicAuth + "-cancreatevoucher"])) {
    canGenerateVoucher.value = true;
  } else if (checkPermission(["journalvouchertemplate-canview"])) {
    canGenerateVoucherSetting.value = true;
  }
  // printReceiptInfo.isPrintCashierName = !(getCookie("JournalIsPrintCashierName") + "" === "false");
  // printReceiptInfo.isPrintAccountantName = getCookie("JournalIsPrintAccountantName") + "" === "true";
  // printReceiptInfo.accountantName = decodeURI(getCookie("JournalAccountantName") ?? "");
  // printReceiptInfo.isPrintUsedName = getCookie("JournalIsPrintUsedName") + "" === "true";
  // printReceiptInfo.usedName = decodeURI(getCookie("JournalUsedName") ?? "");
  // printReceiptInfo.isPrintDepartment = getCookie("JournalIsPrintDepartment") + "" === "true";
  // printReceiptInfo.isPrintProject = getCookie("JournalIsPrintProject") + "" === "true";

  if (props.jType && !isErp.value) {
    request({ url: "/api/Journal/NeedBankEnterprise", method: "post" }).then((res: IResponseModel<boolean>) => {
      if (res.state != 1000) return;
      bankAndCompanyShow.value = res.data && checkPermission(["bankandcompany"]);
    });
  }
}
handleInit();

function getPrintParams() {
  return printReceiptInfoInfo.value;
}
defineExpose({ getPrintParams });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/JournalPrint.less";
.el-icon-question {
    margin-top: -10px;
    margin-left: -10px;
}
</style>
