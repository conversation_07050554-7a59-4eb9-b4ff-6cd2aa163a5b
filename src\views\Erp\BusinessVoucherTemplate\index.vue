<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <a class="button solid-button" v-permission="['businessvouchertemplate-canedit']" @click="oneTouchGenerate">
                                新建
                            </a>
                            <a
                                class="button ml-20"
                                v-show="showMethod === 'list'"
                                v-permission="['businessvouchertemplate-canimport']"
                                @click="handleImport"
                            >
                                导入
                            </a>
                            <a
                                class="button ml-20"
                                v-show="showMethod === 'list'"
                                v-permission="['businessvouchertemplate-canexport']"
                                @click="handleExport"
                            >
                                导出
                            </a>
                            <div class="button ml-20" @click="showGuidance">业务凭证操作指引</div>
                            <div class="warning-tip ml-20">针对所有单据类型或业务场景配置凭证模板，生成业务凭证时自动按模板生成</div>
                            <div class="help ml-10" @click="toHelp"><span class="vedio-icon"></span><span>帮助视频</span></div>
                            <ErpRefreshButton :reload="refreshCurrentPage"></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <a class="button" @click="switchShowMethod">{{ showMethodText }}</a>
                        </div>
                    </div>
                    <div v-loading="loading" element-loading-text="正在加载数据..." :class="{ hidden: newAddClass, 'main-center': true }">
                        <div class="main-center-left">
                            <el-scrollbar height="100%" :always="true" v-show="showMethod === 'module'">
                                <template v-for="moduleItem in groups" :key="moduleItem.id">
                                    <div class="module-item">
                                        <div class="module-title">{{ moduleItem.label }}</div>
                                        <div class="module-content">
                                            <template v-for="item in moduleItem.children" :key="item.id">
                                                <div class="main-template" @dblclick="editTemplate(item.id, item.isDefault)">
                                                    <div class="main-block">
                                                        <img class="defaultImg" src="@/assets/Erp/default.png" v-if="item.isDefault" />
                                                        <div class="main-block-title">
                                                            <Tooltip
                                                                :content="getDisPlayLabel(item)"
                                                                :dynamic-width="true"
                                                                :lineClamp="1"
                                                                placement="top"
                                                            >
                                                                <div class="main-block-info-line t">类型：{{ getDisPlayLabel(item) }}</div>
                                                            </Tooltip>
                                                            <div class="main-block-button">
                                                                <img
                                                                    src="@/assets/Erp/more.png"
                                                                    v-permission="[
                                                                        'businessvouchertemplate-canedit',
                                                                        'businessvouchertemplate-candelete',
                                                                    ]"
                                                                />
                                                                <div class="main-block-edit-expend">
                                                                    <div class="main-block-edit-content">
                                                                        <div
                                                                            v-permission="['businessvouchertemplate-canedit']"
                                                                            @click="editTemplate(item.id, item.isDefault)"
                                                                        >
                                                                            编辑
                                                                        </div>
                                                                        <div
                                                                            v-permission="['businessvouchertemplate-candelete']"
                                                                            @click="deleteTemplate(item.id, item.isDefault)"
                                                                        >
                                                                            删除
                                                                        </div>
                                                                        <div
                                                                            v-permission="['businessvouchertemplate-canedit']"
                                                                            @click="setDefault(item.id)"
                                                                        >
                                                                            设为默认
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="main-block-label">
                                                            <Tooltip
                                                                :content="item.code + item.name"
                                                                :max-width="200"
                                                                :font-size="20"
                                                                :lineClamp="1"
                                                                placement="top-start"
                                                            >
                                                                {{ item.code + " " + item.name }}
                                                            </Tooltip>
                                                        </div>
                                                        <div class="main-block-info">
                                                            <template v-for="project in item.lines" :key="project.description">
                                                                <div class="main-block-info-line">
                                                                    <Tooltip
                                                                        :content="project.asubInfo"
                                                                        :max-width="100"
                                                                        :lineClamp="3"
                                                                        placement="right"
                                                                    >
                                                                        <div class="main-block-info-line-left">
                                                                            {{ project.direction == 1 ? "借" : "贷" }}：{{
                                                                                project.asubInfo
                                                                            }}
                                                                        </div>
                                                                    </Tooltip>
                                                                    <div class="main-block-info-line-right">
                                                                        {{ project.amountCalcWayText }}
                                                                    </div>
                                                                </div>
                                                            </template>
                                                            <div class="main-block-note mt-10">备注：{{ item.note }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                                <div class="template-empty" v-show="hasLoading && displayTableTemplate.length === 0 && !loading">
                                    暂无数据
                                </div>
                            </el-scrollbar>
                            <Table
                                v-show="showMethod === 'list'"
                                class="mt-20"
                                emptyText="暂无数据"
                                :data="displayTableTemplate"
                                :columns="columns"
                                :tableName="setModule"
                                @selection-change="handleSelectionChange"
                            >
                                <template #code>
                                    <el-table-column
                                        label="模板编码"
                                        min-width="100px"
                                        :width="getColumnWidth(setModule, 'code')"
                                        align="left"
                                        header-align="left"
                                    >
                                        <template #default="{ row }: { row: IBusinessVoucherTemplate }">
                                            <span
                                                :class="{ link: checkPermission(['businessvouchertemplate-canedit']) }"
                                                @click="editTemplate(row.id, row.isDefault)"
                                            >
                                                {{ row.code }}
                                            </span>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #isDefault>
                                    <el-table-column
                                        label="是否默认"
                                        min-width="100px"
                                        :width="getColumnWidth(setModule, 'isDefault')"
                                        align="left"
                                        header-align="left"
                                    >
                                        <template #header>
                                            <div class="is-default-header">
                                                <span class="text">是否默认</span>
                                                <el-popover placement="right" :width="300" trigger="hover">
                                                    <template #default>
                                                        <div class="tip-content">
                                                            <div class="tip-line">
                                                                1.设置为默认模板后，业务凭证中对应单据类型凭证模板会默认这个模板
                                                            </div>
                                                            <div class="tipline">
                                                                2.每个单据类型只能有一个默认模板，当前模板设置为默认后，原默认模板会自动取消默认
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template #reference>
                                                        <span class="hover-icon"></span>
                                                    </template>
                                                </el-popover>
                                            </div>
                                        </template>
                                        <template #default="{ row }: { row: IBusinessVoucherTemplate }">
                                            <el-switch
                                                :disabled="!checkPermission(['businessvouchertemplate-canedit'])"
                                                v-model="row.isDefault"
                                                :before-change="() => handleBeforeChange(row)"
                                                @change="setDefault(row.id)"
                                            >
                                            </el-switch>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #operation>
                                    <el-table-column
                                        label="操作"
                                        min-width="100px"
                                        :width="getColumnWidth(setModule, 'operation')"
                                        align="left"
                                        fixed="right"
                                        header-align="left"
                                    >
                                        <template #default="{ row }: { row: IBusinessVoucherTemplate }">
                                            <span
                                                class="link"
                                                v-permission="['businessvouchertemplate-canedit']"
                                                @click="editTemplate(row.id, row.isDefault)"
                                            >
                                                编辑
                                            </span>
                                            <span
                                                class="link"
                                                v-permission="['businessvouchertemplate-candelete']"
                                                @click="deleteTemplate(row.id, row.isDefault)"
                                            >
                                                删除
                                            </span>
                                        </template>
                                    </el-table-column>
                                </template>
                            </Table>
                        </div>
                        <div class="main-center-right ml-20" :class="{ expend }">
                            <div :class="{ function: true, expend }" @click="expend = !expend"></div>
                            <el-scrollbar ref="scrollBarRef" @scroll="handleTreeScroll" :always="true">
                                <el-tree
                                    ref="moduleTreeRef"
                                    node-key="id"
                                    highlight-current
                                    default-expand-all
                                    class="el-tree-all"
                                    :data="moduleTree"
                                    :props="{ label: 'text', children: 'children', class: treeClass }"
                                    :expand-on-click-node="false"
                                    @node-click="nodeClickHandle"
                                >
                                    <template #default="{ data }">
                                        <span class="custom-tree-node">
                                            <span :class="data.children ? 'tree-icon tree-folder tree-folder-open' : 'tree-icon tree-file'">
                                            </span>
                                            <span class="tree-title">{{ data.text }}</span>
                                        </span>
                                    </template>
                                </el-tree>
                            </el-scrollbar>
                        </div>
                    </div>
                </div>
            </template>
            <template #add>
                <AddVoucherTemplate
                    ref="addVoucherTemplateRef"
                    :feature="feature"
                    :scmProductType="scmProductType"
                    :scmAsid="scmAsid"
                    :defaultAsubList="defaultAsubList"
                    @save-success="refreshTemplates"
                    @cancelTemplate="cancelTemplate"
                />
            </template>
        </ContentSlider>
        <OperationGuidance current="BusinessVoucherTemplate" ref="operationGuidanceRef" />
        <ImportSingleFileDialog
            dialogWidth="600px"
            :importTitle="'导入业务凭证模板'"
            v-model:import-show="importShow"
            :importUrl="'/api/VoucherTemplate/BatchImportByExportTemplate?excludeSameName=' + excludeSameName"
            allowFileType=""
            :uploadSuccess="uploadSuccess"
            :uploadCheck="uploadCheck"
            :isClearFile="false"
        >
            <template #download>
                <span>1.将需要导入的业务凭证模板进行导出</span>
            </template>
            <template #import-content>
                <span>2.选择文件导入</span>
            </template>
            <template #bottom-tips>
                <div class="import-tip mt-20 mb-10" style="text-align: center">
                    当前账套中不存在的字段、科目在模版中会默认为空，建议导入后检查下模板
                </div>
            </template>
        </ImportSingleFileDialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "BusinessVoucherTemplate",
};
</script>
<script setup lang="ts">
import { ref, onMounted, computed, nextTick, onActivated } from "vue";
import { type IResponseModel, request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElAlert, ElConfirm } from "@/util/confirm";
import { checkPermission } from "@/util/permission";
import { getUrlSearchParams, globalExport, globalWindowOpen } from "@/util/url";
import { DefaultAsubClass, type AsubRelationType } from "../AsubRelationSettings1/utils";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useScmInfoStore } from "@/store/modules/scm";
import { resetErpVoucherHistory } from "../utils";
import { useRoute } from "vue-router";

import {
    NewVoucherTemplate,
    EditVoucherTemplate,
    type IBusinessVoucherTemplate,
    type IModuleTree,
    type IEditVoucehrTemplateBack,
    type VoucherTemplate,
} from "./types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElTree, ElScrollbar } from "element-plus";

import ContentSlider from "@/components/ContentSlider/index.vue";
import AddVoucherTemplate from "./components/AddVoucherTemplate.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import Table from "@/components/Table/index.vue";
import OperationGuidance from "../components/OperationGuidance.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";

const loading = ref<boolean>(false);

const addVoucherTemplateRef = ref<InstanceType<typeof AddVoucherTemplate>>();
const slots = ["main", "add"];
const currentSlot = ref("main");
const scmProductType = ref(0);
const scmAsid = ref(0);
const feature = ref("");
const VoucherTemplates = ref<Array<IBusinessVoucherTemplate>>([]);
const newAddClass = ref(false);
const allTreeId = "0";
const needAssignBillType = [
    { parent: "其他入库单", children: "其他入库" },
    { parent: "其他出库单", children: "其他出库" },
    { parent: "其他支出", children: "其他费用" },
    { parent: "核销", children: "应收冲应付" },
];
const otherExpense = [544, 545, 546, 547, 548, 549]; // 其他支出
const offset = [514, 515, 516]; // 核销
function tryRepairVtInfo(vtType: number, businessType: number, template: VoucherTemplate) {
    if (businessType === 0) {
        if (otherExpense.includes(vtType)) {
            template.businessType = vtType;
            vtType = 544;
        } else if (offset.includes(vtType)) {
            template.businessType = vtType;
            vtType = 514;
        } else {
            template.businessType = businessType;
        }
    } else {
        template.businessType = businessType;
    }
    template.vtType = vtType;
}
function getCurrentVtInfo() {
    const currentNode = moduleTreeRef.value?.getCurrentNode() as IModuleTree;
    if (needAssignBillType.some((item) => item.parent === currentNode.text)) {
        const item = needAssignBillType.find((item) => item.parent === currentNode.text);
        const child = currentNode.children?.find((child) => child.text === item?.children);
        if (child && child.attributes) return { vtType: child.attributes.VtType, businessType: child.attributes.BusinessId };
    }
    // 在当前节点内部递归查找第一个最末级节点 也就是 children 为null的节点
    function search(data: IModuleTree): { vtType: number; businessType: number } {
        // 这两个条件是互斥的 如果是末级节点则一定有 attributes 一定没有 children
        if (!data.children && data.attributes) return { vtType: data.attributes.VtType, businessType: data.attributes.BusinessId };
        return search(data.children![0]);
    }
    return search(currentNode);
}
function oneTouchGenerate() {
    feature.value = "add";
    newAddClass.value = true;
    const template = new NewVoucherTemplate();
    const { vtType, businessType } = getCurrentVtInfo();
    tryRepairVtInfo(vtType, businessType, template);
    nextTick().then(() => {
        addVoucherTemplateRef.value?.initEditInfo(template);
        currentSlot.value = "add";
    });
}

function cancelTemplate() {
    currentSlot.value = "main";
    newAddClass.value = false;
}

async function refreshTemplates() {
    await getVoucherTemplate();
    let currentNode = moduleTreeRef.value?.getCurrentNode() as IModuleTree;
    !currentNode && moduleTreeRef.value?.setCurrentKey(allTreeId);
    currentNode = moduleTreeRef.value?.getCurrentNode() as IModuleTree;
    if (currentNode) {
        moduleTreeRef.value?.setCurrentKey(currentNode.id);
        nodeClickHandle(currentNode);
    }
}
async function refreshCurrentPage() {
    await getBusinessModuleTree(false);
    addVoucherTemplateRef.value?.refreshAsub();
    addVoucherTemplateRef.value?.getVtType();
    refreshTemplates();
}
async function getVoucherTemplate() {
    loading.value = true;
    await request({
        url: `/api/VoucherForErp/GetTemplates?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
        method: "post",
    })
        .then((res: IResponseModel<Array<IBusinessVoucherTemplate>>) => {
            if (res.state !== 1000) return;
            VoucherTemplates.value = res.data;
        })
        .finally(() => {
            loading.value = false;
            newAddClass.value = false;
        });
}

function editTemplate(id: number, isDefault: boolean) {
    if (!checkPermission(["businessvouchertemplate-canedit"])) return;
    newAddClass.value = true;
    request({ url: `/api/VoucherTemplate?vtId=${id}` }).then((res: IResponseModel<IEditVoucehrTemplateBack>) => {
        if (res.state !== 1000) return;
        feature.value = "edit";
        nextTick().then(() => {
            const template = new EditVoucherTemplate();
            const { vtId, vtType, businessType, vgId, note, voucherLines, vtName, vtCode } = res.data;
            Object.assign(template, { vtId, vtType, businessType, vgId, note, voucherLines, vtName, code: vtCode, isDefault });
            tryRepairVtInfo(vtType, businessType, template);
            addVoucherTemplateRef.value?.initEditInfo(template);
            currentSlot.value = "add";
        });
    });
}

function deleteTemplate(id: number, isDefault: boolean) {
    if (!checkPermission(["businessvouchertemplate-candelete"])) return;
    if (isDefault) {
        ElNotify({ type: "warning", message: "设置为默认的模板不允许删除" });
        return;
    }
    ElConfirm("亲，确认要删除吗？").then((r: boolean) => {
        if (!r) return;
        request({
            url: `/api/VoucherForErp/DeleteDefaultTemplate?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}&vtId=` + id,
            method: "post",
        }).then((res: IResponseModel<boolean>) => {
            if (res.state == 1000 && res.data) {
                ElNotify({ type: "success", message: "删除成功" });
                refreshTemplates();
            } else if (res.state == 2000) {
                ElNotify({ type: "warning", message: res.msg });
            } else {
                ElNotify({ type: "warning", message: "出现问题，请刷新页面重试或联系客服" });
            }
        });
    });
}

function handleBeforeChange(row: IBusinessVoucherTemplate) {
    if (row.isDefault) {
        ElNotify({
            type: "warning",
            message: "每个单据类型只允许设置一个默认模板，请直接将另一个模板设为默认",
        });
        return false;
    }
    return true;
}
function setDefault(id: number) {
    request({
        url: `/api/VoucherForErp/SetDefaultTemplate?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}&vtId=` + id,
        method: "post",
    }).then((res: any) => {
        if (res.state == 1000 && res.data) {
            ElNotify({ type: "success", message: "设置成功" });
            refreshTemplates();
        } else {
            ElNotify({ type: "warning", message: "出现问题，请刷新页面重试或联系客服" });
        }
    });
}

const defaultAsubList = ref<Record<AsubRelationType, string>>(new DefaultAsubClass());
async function getDefaultAsubRelation() {
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    await request({ url: "/api/AsubRelation/GetDefaultList?" + getUrlSearchParams(params), method: "post" }).then(
        (res: IResponseModel<Record<AsubRelationType, number>>) => {
            for (const keyName in res.data) {
                const key = keyName as unknown as AsubRelationType;
                if (defaultAsubList.value[key] !== undefined) {
                    defaultAsubList.value[key] = res.data[key].toString();
                }
            }
        }
    );
}

const operationGuidanceRef = ref<InstanceType<typeof OperationGuidance>>();
function showGuidance() {
    operationGuidanceRef.value?.showGuidance();
}
function toHelp() {
    globalWindowOpen("https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=130150112");
}
function tryRepairCustomDescription() {
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    request({ url: "/api/VoucherForErp/TemplateDescription?" + getUrlSearchParams(params), method: "put" });
}
const scmInfoStore = useScmInfoStore();
onMounted(async () => {
    await scmInfoStore.handleGetScmRelation().then(() => {
        scmAsid.value = scmInfoStore.scmAsid;
        scmProductType.value = scmInfoStore.scmProductType;
    });
    operationGuidanceRef.value?.checkShowGuidance();
    await resetErpVoucherHistory();
    await getVoucherTemplate();
    tryRepairCustomDescription();
    getBusinessModuleTree();
    getDefaultAsubRelation();
    // 取值方式
    addVoucherTemplateRef.value?.getVtTypeAmountCalcWayPairs();
    // 单据类型  业务类型
    addVoucherTemplateRef.value?.getVtType();
    // 关联科目
    addVoucherTemplateRef.value?.getAsubList();
    // 凭证摘要历史记录
    addVoucherTemplateRef.value?.handleGetDescHistory();
});

const moduleTree = ref<Array<IModuleTree>>([]);
const groups = ref<Array<GroupTemplate>>([]);
const moduleTreeRef = ref<InstanceType<typeof ElTree>>();
class GroupTemplate {
    constructor(tree: IModuleTree) {
        this.label = formatBillTypeText(tree.id);
        this.id = tree.id;
        this.children = getGroupTemps(getAllBillTypes(tree));
    }
    label: string;
    id: string;
    children: Array<IBusinessVoucherTemplate>;
}
function treeClass(data: any) {
    return data.id === allTreeId ? "is-root" : "";
}
function formatBillTypeText(billType: string) {
    let level = 0;
    let parentText = "";
    let returnText = "";
    let change = false;
    function search(data: Array<IModuleTree>, id: string): string {
        for (let i = 0; i < data.length; i++) {
            if (data[i].id === id) return data[i].text;
            if (!data[i].children) continue;
            level++;
            parentText = data[i].text;
            const result = search(data[i].children || [], id);
            if (result) {
                if (level === 3 && parentText) {
                    if (!change) {
                        change = true;
                        returnText = `${parentText}(${result})`;
                    }
                    return returnText;
                } else {
                    return result;
                }
            } else {
                level--;
            }
        }
        return "";
    }
    return search(moduleTree.value, billType);
}
function getGroupTemps(bills: Array<string>) {
    const list: Array<IBusinessVoucherTemplate> = [];
    bills.forEach((bill) => {
        const temps = VoucherTemplates.value.filter((item) => item.onlyType === bill);
        temps && list.push(...temps);
    });
    return list;
}
function getDisPlayLabel(item: IBusinessVoucherTemplate) {
    return item.parentVtTypeText ? `${item.parentVtTypeText}(${item.vtTypeText})` : item.vtTypeText;
}
function getAllBillTypes(data: IModuleTree) {
    const billTypes = [data.id];
    if (!data.children) return billTypes;
    data.children.forEach((item) => {
        if (item.children) {
            billTypes.push(...getAllBillTypes(item));
        } else if(item.id !== data.id) {
            billTypes.push(item.id);
        }
    });
    return billTypes;
}
const displayTableTemplate = computed(() => {
    const list: Array<IBusinessVoucherTemplate> = [];
    groups.value.forEach((group) => {
        list.push(...group.children);
    });
    return list;
});
const currentTreeId = ref("0");
function nodeClickHandle(data: IModuleTree) {
    const { id } = data;
    currentTreeId.value = id;
    groups.value = id === "0" ? data.children!.map((tree) => new GroupTemplate(tree)) : [new GroupTemplate(data)];
    nextTick(() => {
        const node = document.querySelector(`.el-tree-node[data-key="${id}"]`);
        node?.scrollIntoView({ block: 'center', behavior: 'smooth' });
    });
}
const hasLoading = ref(false);
const route = useRoute();
async function getBusinessModuleTree(defaultClick = true) {
    await request({
        url: `/api/VoucherForErp/GetTemplateModuleTree?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
        method: "post",
    })
        .then((res: IResponseModel<Array<IModuleTree>>) => {
            if (res.state == 1000) {
                moduleTree.value = [{ text: "全部", children: res.data, id: allTreeId, attributes: null }];
                nextTick().then(() => {
                    if (!defaultClick) return;
                    const templateType = Array.isArray(route.query.templateType) ? route.query.templateType[0] : route.query.templateType;
                    moduleTreeRef.value?.setCurrentKey(templateType || allTreeId);
                    const currentNode = moduleTreeRef.value?.getCurrentNode() as IModuleTree;
                    currentNode && nodeClickHandle(currentNode);
                });
            }
        })
        .finally(() => {
            hasLoading.value = true;
        });
}

const showMethod = ref<"module" | "list">("module");
const showMethodText = ref("按列表展示");
function switchShowMethod() {
    showMethod.value = showMethod.value === "module" ? "list" : "module";
    showMethodText.value = showMethod.value === "module" ? "按列表展示" : "按模块展示";
}
const setModule = "BusinessVoucherTemplate";
const columns: Array<IColumnProps> = [
    { slot: "selection", fixed: "left" },
    { slot: "code" },
    { label: "模板名称", prop: "name", minWidth: 150, width: getColumnWidth(setModule, "name") },
    {
        label: "凭证字",
        prop: "vgId",
        minWidth: 100,
        formatter: (row: IBusinessVoucherTemplate) => getVgName(row.vgId),
        width: getColumnWidth(setModule, "vgId"),
    },
    {
        label: "单据类型",
        prop: "vtTypeText",
        formatter(row: IBusinessVoucherTemplate) {
            return row.parentVtTypeText || row.vtTypeText;
        },
        minWidth: 150,
        width: getColumnWidth(setModule, "parentVtTypeText"),
    },
    {
        label: "业务类型",
        prop: "vtTypeText",
        formatter(row) {
            return row.parentVtTypeText ? row.vtTypeText : "";
        },
        minWidth: 150,
        width: getColumnWidth(setModule, "vtTypeText"),
    },
    { label: "备注", prop: "note", minWidth: 200, width: getColumnWidth(setModule, "note") },
    { slot: "isDefault" },
    { slot: "operation" },
];
const voucherGroupList = computed(() => useVoucherGroupStore().voucherGroupList);
function getVgName(vgId: number) {
    return voucherGroupList.value.find((item) => item.id === vgId)?.name || "";
}
const selections = ref<Array<IBusinessVoucherTemplate>>([]);
function handleSelectionChange(selection: Array<IBusinessVoucherTemplate>) {
    selections.value = selection;
}
function handleExport() {
    if (!selections.value.length) {
        ElNotify({ type: "warning", message: "请选择需要导出的模板" });
        return;
    }
    const vtIds = selections.value.map((item) => item.id);
    globalExport(`/api/VoucherTemplate/BatchExportVoucherTemplate?vtIds=${vtIds}`);
}
const importShow = ref(false);
function handleImport() {
    importShow.value = true;
}
async function uploadCheck(file: any) {
    excludeSameName.value = false;
    const fileExtension = file.name.split(".").pop().toLowerCase();
    if (fileExtension !== "zip") {
        ElNotify({ type: "warning", message: "您上传的文件格式不正确，请重新选择哦~" });
        return false;
    }
    const { canNext, confirmType } = await checkSameNameForImport(file);
    if (!canNext || confirmType === ImportType.Close) return false;
    excludeSameName.value = confirmType === ImportType.Skip;
    return true;
}
enum ImportType {
    Close = 1, // 弹窗关闭
    Continue = 2, // 继续导入
    Skip = 3, // 跳过并导入
}
async function handleImportConfirm() {
    let confirmType: ImportType = ImportType.Close;
    await ElAlert({
        message: `
            <div style="line-height: 25px">亲，存在同一单据类型凭证模板名称相同的情况，是否继续导入？</div>
            <div style="line-height: 25px">选择继续导入，则同名的模板也会导入</div>
            <div style="line-height: 25px">选择跳过并导入，则同名的模板不会导入，仅导入非同名的模板</div>
        `,
        dialogWidth: "550px",
        options: { confirmButtonText: "跳过并导入", cancelButtonText: "继续导入" },
        confirmHandle: () => {
            confirmType = ImportType.Skip;
        },
        cancelHandle: () => {
            confirmType = ImportType.Continue;
        },
    }).then((r) => {
        if (confirmType === ImportType.Close) {
            importShow.value = true;
        }
    });
    return confirmType;
}
const excludeSameName = ref(false);
async function checkSameNameForImport(file: any) {
    let canNext = false;
    let confirmType: ImportType = ImportType.Continue as ImportType;
    const formData = new FormData();
    formData.append("file", file);
    await request({
        url: "/api/VoucherTemplate/CheckSameNameForImport",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: formData,
    }).then(async (res: IResponseModel<boolean>) => {
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "导入失败" });
            return;
        }
        if (!res.data) {
            canNext = true;
            return;
        }
        // 有重名
        importShow.value = false;
        confirmType = await handleImportConfirm();
        canNext = confirmType !== ImportType.Close;
    });
    return { canNext, confirmType };
}
function uploadSuccess(res: IResponseModel<{ success: number; failed: number; skip: number; billTypeIds: Array<string> }>) {
    if (res.state !== 1000 || !res.data) {
        ElNotify({ type: "warning", message: res.msg || "导入失败" });
        return;
    }
    if (res.data.success || res.data.skip) {
        if (excludeSameName.value) {
            ElNotify({
                type: "success",
                message: `成功导入${res.data.success}个，失败${res.data.failed + res.data.skip}个！同一单据类型凭证模板名称相同的已跳过`,
            });
        } else {
            ElNotify({ type: "success", message: `成功导入${res.data.success}个！` });
        }
        importShow.value = false;
        const currentBills = getAllBillTypes(moduleTreeRef.value?.getCurrentNode() as IModuleTree);
        let treeId = moduleTreeRef.value?.getCurrentKey();
        if (res.data.billTypeIds && res.data.billTypeIds.length && res.data.billTypeIds.some((bill) => !currentBills.includes(bill))) {
            treeId = allTreeId;
        }
        moduleTreeRef.value?.setCurrentKey(treeId);
        refreshTemplates();
    } else {
        ElNotify({ type: "warning", message: "导入失败" });
    }
}
let cacheTop = 0;
function handleTreeScroll(event: { scrollLeft: number; scrollTop: number }) {
    cacheTop = event.scrollTop;
}
const scrollBarRef = ref<InstanceType<typeof ElScrollbar>>();
onActivated(() => {
    scrollBarRef.value?.setScrollTop(cacheTop);
    const { templateType } = route.query
    if(templateType && templateType !== currentTreeId.value) {
        moduleTreeRef.value?.setCurrentKey(templateType as string);
        const currentNode = moduleTreeRef.value?.getCurrentNode() as IModuleTree;
        currentNode && nodeClickHandle(currentNode);
    }
});
const expend = ref(false);
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/Erp/BusinessVoucherTemplate-index.less";
</style>
