import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

class ThirtPartNotifyType {
    constructor(typeName: string) {
        this.typeName = typeName;
    }
    typeName: string;
    toString() {
        return this.typeName;
    }
}

export const thirtPartNotifyTypeEnum = {
    accountSet1CreateAccountSet: new ThirtPartNotifyType("accountSet1-createAccountSet"),
    accountSet1AccountSetOverflow: new ThirtPartNotifyType("accountSet1-accountSetOverflow"),
    accountSetCreateAccountSet: new ThirtPartNotifyType("accountSet-createAccountSet"),
    accountSetUpgradeAccountSet: new ThirtPartNotifyType("accountSet-upgradeAccountSet"),
    accountSetCopyAccountSet: new ThirtPartNotifyType("accountSet-copyAccountSet"),
    accountSetDeleteAccountSet: new ThirtPartNotifyType("accountSet-deleteAccountSet"),
    accountSetModifyAccountSet: new ThirtPartNotifyType("accountSet-modifyAccountSet"),
    accountSetAccountSetOverflow: new ThirtPartNotifyType("accountSet-accountSetOverflow"),
    checkoutCheckout: new ThirtPartNotifyType("checkout-checkout"),
    checkoutReCheckout: new ThirtPartNotifyType("checkout-reCheckout"),
    globalAccountSetExpired: new ThirtPartNotifyType("global-accountSetExpired"),
    globalSwitchAccountSet: new ThirtPartNotifyType("global-switchAccountSet"),
    globalRecycledAccountSet: new ThirtPartNotifyType("global-recycledAccountSet"),
    globalNewProAccountSet: new ThirtPartNotifyType("global-newProAccountSet"),
    globalReloadAccountSetInfo: new ThirtPartNotifyType("global-reloadAccountSetInfo"),
    globalReloadMenu: new ThirtPartNotifyType("global-reloadMenu"),
    globalCloseWindow: new ThirtPartNotifyType("global-closeWindow"),
    globalPrint: new ThirtPartNotifyType("global-print"),
    globalDownload: new ThirtPartNotifyType("global-download"),
    globalOpenWindow: new ThirtPartNotifyType("global-openWindow"),
    globalHrefTo: new ThirtPartNotifyType("global-hrefTo"),
    reinitializeInitAccountSet: new ThirtPartNotifyType("reinitialize-initAccountSet"),
    transferProAccountSetOverflow: new ThirtPartNotifyType("transferPro-accountSetOverflow"),
    globalLogout: new ThirtPartNotifyType("global-logout"),

};

export const thirdPartNotify = function (thirtPartNotifyType: ThirtPartNotifyType, data?: any) {
    return new Promise<void>((resolve, reject) => {
        if (useThirdPartInfoStoreHook().isThirdPart) {
            const functionName = thirtPartNotifyType.toString() + new Date().getTime();
            const receiveMessage = function (event: any) {
                if (event.data.type === functionName) {
                    clearTimeout(timer);
                    window.removeEventListener("message", receiveMessage);
                    reject();
                }
            };
            window.addEventListener("message", receiveMessage);
            const timer = setTimeout(() => {
                window.removeEventListener("message", receiveMessage);
                resolve();
            }, 200);
            parent.postMessage({ type: thirtPartNotifyType.toString(), data: data || {}, functionName: functionName }, "*");
        } else {
            resolve();
        }
    });
};
