<script lang="ts">
export default {
    name: "VoucherGroup",
};
</script>
<script lang="ts" setup>
import type { IVoucherGroup } from "@/api/voucherGroup";
import ContentSlider from "@/components/ContentSlider/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import Table from "@/components/Table/index.vue";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { request } from "@/util/service";
import { ElLoading, type FormInstance, type FormRules } from "element-plus";
import { reactive, ref, watch, nextTick } from "vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "VoucherGroup";
const slots = ["main", "edit"];
const currentSlot = ref("main");
const voucherGroupStore = useVoucherGroupStore();

const tableData = ref<IVoucherGroup[]>([]);

const tableLoading = ref(false);
function handleSearch() {
    tableLoading.value = true;
    voucherGroupStore
        .getVoucherGroup()
        .then(() => {
            tableData.value = voucherGroupStore.voucherGroupList;
            tableLoading.value = false;
        })
        .catch((error) => {
            console.log(error);
        });
}
handleSearch();

const columns: IColumnProps[] = [
    { label: "凭证字", prop: "name", minWidth: 250, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "name") },
    { label: "打印标题", prop: "title", minWidth: 250, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "title") },
    {
        label: "是否默认",
        prop: "isDefault",
        minWidth: 250,
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => {
            return value ? "是" : "否";
        }, 
        width: getColumnWidth(setModule, "isDefault")
    },
    { slot: "operator" },
];

let editType = ref("");
let editVgId = 0;
function handleNew() {
    if (!ruleFormRef.value) {
        return;
    }
    defaultShow.value = true;

    if (ruleForm.isDefault) {
        ruleForm.isDefault = false;
    }
    ruleFormRef.value.resetFields();
    editType.value = "New";
    currentSlot.value = "edit";
    nextTick(() => {
        init = true;
    });
}
let defaultShow = ref(true);
function handleEdit(rowData: any) {
    if (!ruleFormRef.value) {
        return;
    }
    ruleFormRef.value.resetFields();
    editType.value = "Edit";
    editVgId = rowData.id;
    ruleForm.name = rowData.name;
    ruleForm.title = rowData.title;
    ruleForm.isDefault = rowData.isDefault;
    defaultShow.value = !rowData.isDefault;
    currentSlot.value = "edit";
    nextTick(() => {
        init = true;
    });
}

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
    name: "",
    title: "",
    isDefault: false,
});

const rules = reactive<FormRules>({
    name: [{ required: true, message: "凭证字不能为空！", trigger: "blur" }],
    title: [{ required: true, message: "打印标题不能为空！", trigger: "blur" }],
    isDefault: [{ required: true }],
});
const submitLoading = ref(false);
const handleSubmit = async () => {
    if (!ruleFormRef.value) {
        return;
    }
    await ruleFormRef.value.validate((valid) => {
        if (valid) {
            const data = { ...ruleForm };
            submitLoading.value = true;
            request({
                url: editType.value === "Edit" ? `/api/VoucherGroup?id=${editVgId}` : "/api/VoucherGroup",
                data,
                method: editType.value === "Edit" ? "put" : "post",
                headers: {
                    "Content-Type": "application/json",
                },
            })
                .then((res: any) => {
                    submitLoading.value = false;
                    if (res.state === 1000) {
                        ElNotify({
                            message: "亲，保存成功啦",
                            type: "success",
                        });
                        handleSearch();
                        currentSlot.value = "main";
                        backToMain()
                    } else {
                        ElNotify({
                            message: "亲，保存失败啦！" + res.msg,
                            type: "warning",
                        });
                    }
                })
                .catch((err) => {
                    submitLoading.value = false;
                    console.log(err);
                    ElNotify({
                        message: "亲，保存失败啦！",
                        type: "error",
                    });
                });
        }
    });
};
function handleDelete(row: IVoucherGroup) {
    let vgId = row.id;
    if (row.isDefault) {
        ElNotify({
            type: "warning",
            message: "默认凭证字不能被删除！",
        });
        return false;
    }
    ElConfirm("亲，确认要删除吗?").then((r: any) => {
        if (r) {
            request({
                url: "/api/VoucherGroup",
                params: { id: vgId },
                method: "delete",
            })
                .then((res: any) => {
                    if (res.state === 1000 && res.data) {
                        ElNotify({
                            message: "亲，删除成功啦！",
                            type: "success",
                        });
                        handleSearch();
                    } else {
                        ElNotify({
                            message: res.msg || res.message,
                            type: "warning",
                        });
                    }
                })
                .catch((err) => {
                    console.log(err);
                    ElNotify({
                        message: "亲，删除失败啦！",
                        type: "error",
                    });
                });
        }
    });
}
const isErp = ref(window.isErp);
const backToMain = () => {
    currentSlot.value = "main";
    resetInit()
};
let init = false;
const resetInit = () => {
    init = false;
    isEditting.value = false;
};
const isEditting = ref(false);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
watch(
    ruleForm,
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    {
        deep: true,
    }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
</script>

<template>
    <div class="content narrow-content">
        <div class="title">凭证字设置</div>
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <a v-permission="['vouchergroup-canedit']" class="solid-button" @click="handleNew">新增</a>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div v-if="isErp" class="divider-line"></div>
                    <div class="main-center">
                        <Table
                            :data="tableData"
                            :columns="columns"
                            :loading="tableLoading"
                            :max-height="isErp ? 'calc(100vh - 110px)' : 'calc(100vh - 220px)'"
                            :scrollbar-show="true"
                            :tableName="setModule"
                        >
                            <template #operator>
                                <el-table-column label="操作" min-width="228" align="left" header-align="left" :resizable="false">
                                    <template #default="scope">
                                        <a class="link" v-permission="['vouchergroup-canedit']" @click="handleEdit(scope.row)"> 编辑 </a>
                                        <a class="link" v-permission="['vouchergroup-candelete']" @click="handleDelete(scope.row)">
                                            删除
                                        </a>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center" v-loading="submitLoading">
                    <div class="slot-title">凭证字设置</div>

                    <div class="slot-mini-content">
                        <el-form
                            :model="ruleForm"
                            :rules="rules"
                            :inline-message="true"
                            ref="ruleFormRef"
                            label-width="100px"
                            label-position="right"
                            class="custom-form"
                        >
                            <el-form-item label="凭证字：" prop="name">
                                <el-input v-model="ruleForm.name" maxlength="4"></el-input>
                            </el-form-item>
                            <el-form-item label="显示标题：" prop="title">
                                <el-input v-model="ruleForm.title" maxlength="8"></el-input>
                            </el-form-item>
                            <el-form-item label="是否默认：" prop="isDefault" v-if="defaultShow">
                                <el-radio-group v-model="ruleForm.isDefault">
                                    <el-radio :label="true">是</el-radio>
                                    <el-radio :label="false">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-form>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSubmit">保存</a>
                            <a class="button ml-20" @click="backToMain">取消</a>
                        </div>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";

.content {
    .main-content {
        height: auto;
    }

    .slot-content {
        .slot-mini-content {
            width: 1000px;
            padding: 32px 0;
            display: flex;
            align-items: center;
            border: 1px solid var(--slot-title-color);
            margin: 32px 0;
            .buttons {
                padding-left: 35px;
            }
        }
    }

    .custom-form {
        .detail-el-form(238px, 32px);
    }
}
body[erp] .content {
    .main-content .main-center .table {
        height: auto;
    }
    .slot-content {
        .slot-mini-content {
            height: auto;
        }
    }
}
</style>
