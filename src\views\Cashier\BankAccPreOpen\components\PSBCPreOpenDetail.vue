<template>
    <div class="open-account detail-content">
        <div class="detail-back-button" @click="back">
            <img src="@/assets/Icons/back.png" alt="" />
            <span>返回</span>
        </div>
        <div class="slot-title">预约开户详情</div>
        <div class="open-main-content">
            <div class="step-edit">
                <div class="block-title">开户银行</div>
                <div class="block-main">
                    <el-row class="isRow"><div class="openbank-line">开户银行：邮储银行</div> </el-row>
                </div>
                <div class="line"></div>
                <div class="block-title">银行网点信息</div>
                <div class="block-main">
                    <el-form :model="PSBCDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="网点机构号：">
                                <div class="w-240">{{ PSBCDeltailInfo.branchNo }}</div>
                            </el-form-item>
                            <el-form-item label="网点名称：">
                                <div class="w-240">{{ PSBCDeltailInfo.branchName }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">基本信息</div>
                <div class="block-main">
                    <el-form :model="PSBCDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="公司名称：">
                                <div class="w-240">{{ PSBCDeltailInfo.companyName }}</div>
                            </el-form-item>
                            <el-form-item label="统一社会信用代码：">
                                <div class="w-240">{{ PSBCDeltailInfo.unifiedNumber }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人姓名：">
                                <div class="w-240">{{ PSBCDeltailInfo.legalName }}</div>
                            </el-form-item>
                            <el-form-item label="法人手机号：">
                                <div class="w-240">{{ PSBCDeltailInfo.legalMobile }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="企业证件类型：">
                                <div class="w-240">{{ getCertType(PSBCDeltailInfo.companyCertType) }}</div>
                            </el-form-item>
                            <el-form-item label="企业证件号码：">
                                <div class="w-240">{{ PSBCDeltailInfo.companyCertNo }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人证件类型：">
                                <div class="w-240">{{ getLegalCertType(PSBCDeltailInfo.legalCertType) }}</div>
                            </el-form-item>
                            <el-form-item label="法人证件号码：">
                                <div class="w-240">{{ PSBCDeltailInfo.legalCertNo }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">更多信息</div>
                <div class="block-main">
                    <el-form :model="PSBCDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="申请人手机号：">
                                <div class="w-240">{{ PSBCDeltailInfo.applicantMobile }}</div>
                            </el-form-item>
                            <el-form-item label="账户类型：">
                                <div class="w-240">{{ PSBCDeltailInfo.accountType === 0 ? "没有基本户" : "已有基本户" }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">预约时间</div>
                <div class="block-main">
                    <el-form :model="PSBCDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="预约时间：">
                                <div class="w-240">{{ PSBCDeltailInfo.appointmentTime }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import { PSBCLegalCertTypeList, PSBCCertTypeList, type IBankDetail, type IPSBCDetail } from "../types";

const props = defineProps({
    data: {
        type: Object as PropType<IBankDetail<IPSBCDetail>>,
        default: () => ({
            bankType: 0,
            openState: 0,
            id: "",
            content: {
                branchName: "",
                branchNo: "",
                applicantMobile: "",
                companyCertType: "",
                companyCertNo: "",
                legalCertType: "",
                legalCertNo: "",
                companyName: "",
                unifiedNumber: "",
                legalName: "",
                legalMobile: "",
                accountType: 0,
                appointmentTime: "",
            },
        }),
    },
});
const emit = defineEmits<{
    (e: "back"): void;
}>();

const PSBCDeltailInfo = computed(() => props.data.content);
const back = () => {
    emit("back");
};
const getLegalCertType = (type: string) => {
    const legalCertType = PSBCLegalCertTypeList.find((item) => item.value === type);
    return legalCertType?.label ?? "";
};
const getCertType = (type: string) => {
    const certType = PSBCCertTypeList.find((item) => item.value === type);
    return certType?.label ?? "";
};
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
