<template>
    <iframe v-if="isAccountingAgent || isWxwork" :src="iframeSrc" v-show="dialogType === 1 && display"></iframe>
    <ExpiredToBuyDialog v-else :heading="expiredToBuyDialogHeading" @expiredToBuyDialogClose="props.onclose()"></ExpiredToBuyDialog>

    <div class="pro-dialog" v-show="dialogDisplay">
        <el-dialog title="提示" v-model="dialogDisplay" @close="onClose" center width="480px">
            <div class="pro-dialog-container">
                <div class="txt">
                    <div>
                        您当前使用的是<span class="highlight-orange">免费版</span>，凭证、账簿、结账、报表等功能可<span
                            class="highlight-orange"
                            >长期</span
                        >
                    </div>
                    <div><span class="highlight-orange">免费使用！</span></div>
                </div>
                <div class="txt">
                    <div>资金、发票、工资、资产、关联进销存模块为<span class="highlight-orange">专业版</span>功能，</div>
                    <div>系统已为您自动开通<span class="highlight-orange">半个月的体验期</span>。到期后将自动转为免费</div>
                    <div>版，之前录入专业版模块中的数据仅供查询和导出。</div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="gotoDetails">查看功能详情</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<style lang="less" scoped>
iframe {
    position: fixed;
    top: -1px;
    left: -2px;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
}
.pro-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    :deep(.el-overlay-dialog) {
        display: flex;
        align-items: center;
        justify-content: center;
        & .el-dialog {
            margin: 0;
            transform: translateX(70px);
        }
    }
}
.pro-dialog-container {
    .txt {
        padding-left: 28px;
        margin-top: 24px;
        font-size: 15px;
        color: var(--font-color);
        line-height: 24px;

        & + .txt {
            margin-top: 12px;
        }

        &.highlight-orange {
            color: #ff7500;
        }
    }

    .buttons {
        margin-top: 27px;
        text-align: center;
        border-top: 1px solid #dadada;
        padding: 10px 0;

        .solid-button {
            width: 128px;
        }
    }
}
</style>
<script lang="ts" setup>
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { useDialogStore } from "@/store/modules/fullDialog";
import { getGlobalToken } from "@/util/baseInfo";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { globalWindowOpenPage, globalWindowOpen } from "@/util/url";
import { ref, watch, computed } from "vue";
import ExpiredToBuyDialog from "@/components/ExpiredToBuyDialog/index.vue";
import { ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { isInWxWork } from "@/util/wxwork";

const props = defineProps<{
    dialogType: 1 | 2;
    msg?: string;
    highlightmodule?: string;
    onclose: () => void;
}>();
const isAccountingAgent = ref(window.isAccountingAgent);
const isWxwork = ref(isInWxWork());
const display = ref(true);
const dialogDisplay = ref(props.dialogType === 2 && display.value);
const accountsetStore = useAccountSetStore();
const asId = accountsetStore.accountSet?.asId || 0;
const trialStatusStore = useTrialStatusStore();
const iframeSrc = ref(
    "/proPayDialog.html#/upgrade/accmodal?msg=" +
        props.msg +
        "&isAccountingAgent=" +
        (window.isAccountingAgent ? "true" : "false") +
        "&asid=" +
        asId +
        "&highlightmodule=" +
        (props.highlightmodule || "")
);
window.proDialogOnclose = () => {
    display.value = false;
    props.onclose();
};
let isGotoDetails = false;
const onClose = () => {
    if (!isGotoDetails) {
        props.onclose();
        // useDialogStore().dialogCount = false;
    }
};
const gotoDetails = () => {
    isGotoDetails = true;
    dialogDisplay.value = false;
    if (window.isAccountingAgent) {
        globalWindowOpen(window.aaHost + "/#/valueAddedServices");
    } else {
        const days = trialStatusStore.remainingDays;
        const hash = "/upgrade/acc?remainingDays=" + (days && trialStatusStore.isTrial && !trialStatusStore.isExpired ? days : -1);
        globalWindowOpenPage("/Pro/PayDialog?appasid=" + getGlobalToken() + "&hash=" + hash, "升级专业版");
    }
};
const expiredToBuyDialogHeading = computed(() => {
    if (props.msg?.includes("立即解锁多用户协同")) {
        return ExpiredToBuyDialogEnum.permissionEdit;
    } else if (props.msg?.includes("立即获取10G超大会计电子档案空间")) {
        return ExpiredToBuyDialogEnum.ERecordSpaceExpand;
    }
    return ExpiredToBuyDialogEnum.normal;
});
// watch(
//     dialogDisplay,
//     () => {
//         useDialogStore().dialogCount = dialogDisplay.value;
//     },
//     { immediate: true }
// );
</script>
