import { ref } from "vue";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import store from "@/store";
import { getVoucherGroupApi, type IVoucherGroup } from "@/api/voucherGroup";
import type { IResponseModel } from "@/util/service";

export const useVoucherGroupStore = defineStore("voucherGroup", () => {
    const voucherGroupList = ref<IVoucherGroup[]>([]);

    const defaultVgId = ref<number>(1010);

    const getVoucherGroup = () => {
        return new Promise<IVoucherGroup[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getVoucherGroupApi()
                    .then((res: any) => {
                        const data = res as IResponseModel<IVoucherGroup[]>;
                        if (data.state === 1000) {
                            voucherGroupList.value = data.data;
                            const defaultVoucherGroup = voucherGroupList.value.find((item) => item.isDefault);
                            defaultVgId.value = defaultVoucherGroup ? defaultVoucherGroup.id : 1010;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    return { voucherGroupList, defaultVgId, getVoucherGroup };
});

/** 在setup外使用 */
export function useVoucherGroupStoreHook() {
    return useVoucherGroupStore(store);
}
