<template>
    <VirtualSelectCheckbox
        :options="accountOptions"
        :useIcon="true"
        v-model:selectedList="selectedList"
        :bottom-switch="bottomSwitch"
        :bottom-switch-text="'显示禁用账户'"
        v-model:bottomSwitchValue="showDisabled"
        @change-switch-status="changeSwitchStatus"
        :width="width"
        :maxWidth="maxWidth"
        :cacheLastSelect="true"
    >
    </VirtualSelectCheckbox>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import { type IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { 
    calcCurrentCDAccountList, 
    setShowDisabledAccount, 
    getShowDisabledAccount,
    hasDisableAccount,
} from "@/views/Cashier/CDAccount/utils";

const props = withDefaults(
    defineProps<{
        options: IBankAccountItem[];
        accountOptions: Option[];
        selectedList: Array<number | string>;
        modulePage?: string;
        width?: string;
        maxWidth?: number;
        showDisabledAccount?: boolean; //跳转明细页路由传入的值
        isJump?: boolean; //属于跳转的明细页
    }>(),
    {
        modulePage: "cashReport",
        width: "200px",
        maxWidth: 0,
        showDisabledAccount: false,
        isJump: false,
    }
);

const bottomSwitch = computed(()=> {
    return hasDisableAccount(props.options);
});

const emit = defineEmits<{
    (event: "update:selectedList", args: Array<number | string>): void;
    (event: "update:accountOptions", args: Array<Option>): void;
    (event: "change-switch-status", args: boolean): void;
}>();

const selectedList = computed({
    get() {
        return props.selectedList;
    },
    set(value: Array<number | string>) {
        emit("update:selectedList", value);
    },
});
const accountOptions = computed({
    get() {
        return props.accountOptions;
    },
    set(value: Array<Option>) {
        emit("update:accountOptions", value);
    },
});
const showDisabled = ref(getShowDisabledAccount(props.modulePage));
watch(
    () => props.options,
    () => {
        handleInit();
    }
);
function handleInit() {
    if (props.isJump) {
        showDisabled.value = props.showDisabledAccount;
    } else {
        showDisabled.value = getShowDisabledAccount(props.modulePage);
    }
    getAccountOptions();
}
function changeSwitchStatus() {
    if (!props.isJump) {
        setShowDisabledAccount(props.modulePage, showDisabled.value);
    }
    getAccountOptions();
    emit("change-switch-status", showDisabled.value);
}
function getAccountOptions() {
    accountOptions.value = calcCurrentCDAccountList(props.options, showDisabled.value);
}
defineExpose({ 
    handleInit 
});
</script>
