import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useLoading } from "@/hooks/useLoading";
import { ElConfirm } from "@/util/confirm";
import { type IResponseModel, request } from "@/util/service";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const sizeFormatter = (size: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB", "BB"];
    let i = 0;
    while (size >= 1024 && i < units.length - 1) {
        size /= 1024;
        i++;
    }
    return size === 0 ? "" : size.toFixed(2) + " " + units[i];
};

export const judgeRowClassName = (scope: any) => {
    return scope.row.expiredDate ? "lack-space-tip" : "";
};

export const parseOptionsString = (optionsString: string) => {
    const regex = /value='(\d+)'( selected='selected')?>([\u4e00-\u9fa5\d]+)/g;
    const result: { value: number; label: string; selected: boolean }[] = [];
    let match;
    let selectedIndex = -1;
    while ((match = regex.exec(optionsString)) !== null) {
        const value = parseInt(match[1], 10);
        const label = match[3];
        const selected = match[2] !== undefined;
        result.push({ value, label, selected });
        if (selectedIndex < 0 && selected) {
            selectedIndex = result.length - 1;
        }
    }
    if (selectedIndex < 0 && result.length > 0) {
        selectedIndex = 0;
        result[0].selected = true;
    }
    return result;
};


export const divMainInfoColumns = ():IColumnProps[] => {
    const setModule = "DivMain";
    return [
        { label: "备份名称", prop: "fileName", minWidth: 432, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'fileName') },
        { label: "日期", prop: "dateTime", minWidth: 138, align: "center", headerAlign: "center", width: getColumnWidth(setModule, 'dateTime') },
        {
            label: "文件大小",
            prop: "fileSize",
            minWidth: 80,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return sizeFormatter(value);
            }, 
            width: getColumnWidth(setModule, 'fileSize')
        },
        { label: "操作人", prop: "creator", minWidth: 93, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'creator') },
        { slot: "operator" },
    ]
}
export const divExcelMainInfoOrPdfColumns = ():IColumnProps[] => {
    const setModule = "DivExcelOrPdf";
    return  [
        { label: "备份名称", prop: "fileName", minWidth: 460, align: "center", headerAlign: "center", width: getColumnWidth(setModule, 'fileName') },
        { label: "日期", prop: "dateTime", minWidth: 130, align: "center", headerAlign: "center", width: getColumnWidth(setModule, 'dateTime') },
        {
            label: "文件大小",
            prop: "fileSize",
            minWidth: 80,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return sizeFormatter(value);
            },
            width: getColumnWidth(setModule, 'fileSize')
        },
        { label: "操作人", prop: "creator", minWidth: 93, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'creator') },
        { slot: "operator" },
    ]
}

export const divAsyncFileInfoColumns = ():IColumnProps[] => {
    const setModule = "DivAsyncFile";
    return [
        { slot: "selection" },
        { label: "文件名称", prop: "fileName", minWidth: 280, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'fileName') },
        {
            label: "来源类型",
            prop: "fileSourceTypeName",
            minWidth: 80,
            align: "left",
            headerAlign: "center",
            width: getColumnWidth(setModule, 'fileSourceTypeName')
        },
        { label: "来源位置", prop: "fileSource", minWidth: 130, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'fileSource') },
        { label: "日期", prop: "dateTime", minWidth: 130, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'dateTime') },
        {
            label: "文件大小",
            prop: "fileSize",
            minWidth: 80,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return sizeFormatter(value);
            }, 
            width: getColumnWidth(setModule, 'fileSize')
        },
        { label: "操作人", prop: "creator", minWidth: 93, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'creator') },
        { slot: "operator" },
    ]
}
export const divAuditMainInfoColumhs = ():IColumnProps[] => {
    const setModule = "DivAudit";
    return [
        { label: "文件名称", prop: "fileName", minWidth: 460, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'fileName') },
        { label: "日期", prop: "dateTime", minWidth: 130, align: "center", headerAlign: "center", width: getColumnWidth(setModule, 'dateTime') },
        {
            label: "文件大小",
            prop: "fileSize",
            minWidth: 80,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return sizeFormatter(value);
            },
            width: getColumnWidth(setModule, 'fileSize')
        },
        { label: "操作人", prop: "creator", minWidth: 93, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'creator') },
        { slot: "operator" },
    ]
}

export const popperStyle = {
    fontSize: "14px",
    lineHeight: "22px",
    color: "rgba(0, 0, 0, 0.65)",
    padding: "12px",
    background: "#ffffff",
    boxShadow: " 0px 4px 16px 0px rgba(0, 0, 0, 0.1)",
    border: "1px solid #cccccc",
};

export const CheckMail = (mail: string) => {
    const filter = /^[\w.-]+@[\w-]+(\.[\w-]+)+$/;
    return filter.test(mail);
};

export const checkCanProcessAndHasTemporaryFile = (
    fileType: "pdf" | "excel" | "zip" | "file" | "audit" | "archive",
    confirmMsg: string,
    go: Function,
    successHandle: Function,
    notCanProcess: () => void
) => {
    const text = fileType === "archive" ? "归档中，请稍后..." : "备份进行中，请稍候...";
    useLoading().enterLoading(text);
    const checkCanProcessUrl = "/api/Backup/CheckCanProcess?fileType=" + fileType;
    request({ url: checkCanProcessUrl, method: "post" }).then((r: IResponseModel<boolean>) => {
        if (r.state == 1000) {
            if (r.data) {
                request({ url: "/api/Backup/HasTemporaryFile?fileType=" + fileType, method: "post" }).then((r: IResponseModel<boolean>) => {
                    if (r.state === 1000 && r.data) {
                        useLoading().quitLoading();
                        ElConfirm(confirmMsg).then((r: boolean) => {
                            if (r) {
                                fileType === "archive" && useLoading().enterLoading(text);
                                go();
                            }
                        });
                    } else {
                        fileType !== "archive" && useLoading().quitLoading();
                        successHandle();
                    }
                });
            } else {
                useLoading().quitLoading();
                notCanProcess();
            }
        }
    });
};
