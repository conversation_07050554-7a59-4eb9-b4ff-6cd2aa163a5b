import { request } from "@/util/service";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { getServiceId } from "@/util/proUtils";

export const getPromptMessageApi = () => {
    const accountsetStore = useAccountSetStoreHook();
    return request({
        url:
            window.eHost +
            "/wb/reminder/promptMessage?userSn=" +
            accountsetStore.userInfo?.userSn +
            "&serviceId=" +
            getServiceId() +
            "&channelId=1",
        method: "get",
    });
};
export interface IProInfo {
    startTime: string;
    subscriptionType: string;
    amount: number;
    needRemind: boolean;
    isExpired: boolean;
    expiredTime: string;
    remainingDays: number;
}
