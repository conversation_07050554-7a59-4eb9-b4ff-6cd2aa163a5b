import { createApp } from "vue";
import ElementPlus from "element-plus";
import Viewer from "viewerjs";

import type { IFileInfo, IERecordTableInfo, IMobileUploadData } from "./types";

import DialogDeleteAttachFile from "./components/DialogDeleteAttachFile.vue";
import { dialogDrag }  from "@/direcitves/dialogDrag/index";

interface IPathInfo {
    id: number;
    text: string;
    children: IPathInfo[];
}

export const formatFileCategory = function (i: number) {
    let _str = "";
    switch (i) {
        case 0:
            _str = "凭证附件";
            break;
        case 1:
            _str = "银行回单";
            break;
        case 2:
            _str = "电子发票";
            break;
        case 3:
            _str = "其它单据";
            break;
    }
    return _str;
};

export const formatCheckState = function (i: number) {
    let _str = "";
    switch (i) {
        case 0:
            _str = "非电子发票，无需查验";
            break;
        case 1:
            _str = "未查验";
            break;
        case 2:
            _str = "查验中";
            break;
        case 3:
            _str = "已查验";
            break;
        case 4:
            _str = "查验失败";
            break;
        case 5:
            _str = "已查验，发票号码重复";
            break;
        case 6:
            _str = "已查验，发票抬头与公司名称不符";
            break;
        case 7:
        case 8:
        case 9:
        case 10:
        case 11:
        case 12:
            _str = "查验失败";
            break;
        case 100:
            _str = "一键取票获取，无需查验";
            break;
    }

    return _str;
};

export const getIconClass = (type: number) => {
    switch (type) {
        case 1010:
            return "icon-word";
        case 1020:
            return "icon-excel";
        case 1030:
            return "icon-ppt";
        case 1040:
            return "icon-access";
        case 2010:
            return "icon-txt";
        case 2020:
            return "icon-pdf";
        case 2030:
            return "icon-zip";
        case 3010:
            return "icon-img";
        case 4010:
            return "icon-ofd";
        case 9010:
            return "icon-default";
        default:
            return "icon-default";
    }
};

export const mapToFileInfo = (file: any): IFileInfo => {
    return {
        fileId: Number(file.FileId),
        fileName: file.FileName,
        fileSize: file.FileSize,
        fileType: file.FileType,
        relativePath: file.FilePath.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, ""),
    };
};
export const mapToMobileFileInfo = (file: IMobileUploadData): any => {
    const fileSize = (Number(file.FileSize) / 1024).toFixed(2) + "KB";
    return {
        fileId: Number(file.FileId),
        fileName: file.FileName,
        fileSize: fileSize,
        fileType: file.FileType,
        relativePath: file.Path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, ""),
    };
};

export const mapToERecordTableInfo = (file: any): IERecordTableInfo => {
    return {
        fileId: Number(file.File_Id),
        fileName: file.File_Name,
        fileSize: file.File_Size,
        fileType: file.File_Type,
        relativePath: file.Path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, ""),
        fileCategory: file.File_Category,
        createdDate: file.Created_Date_Str,
        createdByName: file.Created_By_Name,
        checkState: file.Check_State,
        relatedDocumentTypes: file.RelatedDocumentTypes,
        voucherGroupStr: file.VoucherGroupStr || "",
        source: file.Source || [],
        vid: file.V_Id || "",
    };
};
export const fileInfoFromERecordTomain = (file: any): IFileInfo => {
    return {
        fileId: file.fileId,
        fileName: file.fileName,
        fileSize: file.fileSize,
        fileType: file.fileType,
        relativePath: file.relativePath,
    };
};
export function findPathById(list: IPathInfo[], id: number): string {
    for (const item of list) {
        if (item.id === id) {
            return item.text;
        }
        if (item.children && item.children.length > 0) {
            const foundPath = findPathById(item.children, id);
            if (foundPath) {
                return item.text + ">" + foundPath;
            }
        }
    }
    return "";
}

export const showDeleteBillOrVoucherConfirm = (type: "bill" | "voucher" = "bill"): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        const contentBox = document.querySelector(".router-container .content");
        const props = {
            type,
            onlyDelete: () => {
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
            batchDelete: () => {
                resolve(true);
                capp.unmount();
                contentBox!.removeChild(container);
            },
        };
        const capp = createApp(DialogDeleteAttachFile, props);
        const container = document.createElement("div");
        capp.use(ElementPlus);
        capp.directive('dialogDrag', dialogDrag);
        capp.mount(container);
        contentBox!.insertBefore(container, contentBox!.firstChild);
    });
};

export function getFileErrorTip(message: string) {
    const errNames = [
        "文件大小为0，不能上传",
        "文件名称含有非法字符",
        "文件名含有非法字符",
        "文件名称长度超过120个字符",
        "文件名长度超过120个字符",
    ];
    const index = errNames.findIndex((item) => message.endsWith(item));
    const tip = index === -1 ? "" : errNames[index];
    if (message.endsWith("长度超过120个字符")) {
        const names = message.split(".");
        names.pop();
        message = names.join(".").slice(0, 30) + "..." + tip;
    }
    return message;
}

export function previewImg(container: HTMLElement, index: number) {
    const photoViewer = new Viewer(container, {
        inline: false, // 是否启用 inline 模式
        fullscreen: true, // 播放时是否全屏
        title: false, // 是否显示当前图片的标题
        toolbar: {
            // 显示工具栏
            // 下面各种按钮1显示，0隐藏，可自定义按钮大小和点击事件
            zoomIn: 1, // 放大图片
            zoomOut: 1, //缩小图片
            oneToOne: 1, // 图片比例100%
            reset: 1, // 重置图片大小
            prev: 1, // 查看上一张图片
            play: 0, // 播放图片
            next: 1, // 查看下一张图片
            rotateLeft: 1, // 向左旋转图片
            rotateRight: 1, // 向右旋转图片
            flipHorizontal: 1, // 图片左右翻转
            flipVertical: 1, // 图片上下翻转
        },
        // 定义用于查看的图像的初始索引
        initialViewIndex: index,
        // 每次关闭查看时触发
        hide() {
            photoViewer.destroy();
        },
        // 每次关闭查看时触发，在hide之后
        hidden() {
            photoViewer.destroy();
        },
        // 每次查看时触发
        show() {
            photoViewer.full();
        },
    });
    photoViewer.show();
}