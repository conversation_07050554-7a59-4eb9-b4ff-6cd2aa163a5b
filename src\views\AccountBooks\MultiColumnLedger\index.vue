<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">{{ title ? title.split(" ")[1] + " " : "" }}多栏账</div>
                    <div class="main-content">
                        <div class="main-top main-tool-bar space-between split-line">
                            <div class="main-tool-left">
                                <SearchInfoContainer ref="containerRef">
                                    <template v-slot:title>{{ currentPeriodInfo }}</template>
                                    <div class="line-item first-item input">
                                        <div class="line-item-title">会计期间：</div>
                                        <div class="line-item-field">
                                            <DatePicker
                                                v-model:startPid="searchInfo.startMonth"
                                                v-model:endPid="searchInfo.endMonth"
                                                :clearable="false"
                                                :editable="false"
                                                :dateType="'month'"
                                                :value-format="'YYYYMM'"
                                                :label-format="'YYYY年MM月'"
                                                :isPeriodList="true"
                                                @getActPid="getActPid"
                                            />
                                        </div>
                                    </div>
                                    <div class="line-item input">
                                        <div class="line-item-title">会计科目：</div>
                                        <div class="line-item-field">
                                            <SubjectPicker
                                                ref="asubRef"
                                                v-model="asubCode"
                                                :is-by-id="true"
                                                asubImgRight="14px"
                                                :showDisabled="true"
                                            ></SubjectPicker>
                                        </div>
                                    </div>
                                    <div class="line-item input" v-show="assistTypeShow">
                                        <div class="line-item-title">核算项目类别</div>
                                        <div class="line-item-field fcid-select">
                                            <el-select
                                                :teleported="false"
                                                v-model="searchInfo.assistType"
                                                placeholder=" "
                                                @change="assistSubjectChange"
                                            >
                                                <el-option
                                                    v-for="item in assistTypeList"
                                                    :key="item.id"
                                                    :value="item.id"
                                                    :label="item.label"
                                                    >{{ item.label }}</el-option
                                                >
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class="line-item input" v-show="assistSubjectShow">
                                        <div class="line-item-title">核算项目：</div>
                                        <div class="line-item-field fcid-select">
                                            <el-select :teleported="false" v-model="searchInfo.assistSubject" placeholder=" ">
                                                <el-option label="全部" value="0"></el-option>
                                                <el-option
                                                    v-for="item in assistSubjectList"
                                                    :key="item.id"
                                                    :value="item.id"
                                                    :label="item.label"
                                                    >{{ item.label }}</el-option
                                                >
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class="line-item input" v-show="fcTypeShow">
                                        <div class="line-item-title">币别：</div>
                                        <div class="line-item-field fcid-select">
                                            <el-select v-model="searchInfo.fcid" :teleported="false" placeholder=" ">
                                                <el-option label="综合本位币" value="-1"> 综合本位币 </el-option>
                                                <el-option :label="item.label" :value="item.id" v-for="item in fcTypeList" :key="item.id">{{
                                                    item.label
                                                }}</el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class="line-item input" v-show="subjectShow">
                                        <div class="line-item-field" style="padding-left: 25px">
                                            <el-radio-group v-model="searchInfo.subjectShow">
                                                <el-radio label="1">只显示最明细科目</el-radio>
                                                <el-radio label="2">只显示下级科目</el-radio>
                                            </el-radio-group>
                                        </div>
                                    </div>
                                    <div class="line-item single">
                                        <div class="line-item-title">
                                            <el-checkbox v-model="searchInfo.showBalance" label="显示明细栏余额"></el-checkbox>
                                        </div>
                                    </div>
                                    <div class="line-item single" v-show="NOSHOW">
                                        <div class="line-item-title">
                                            <el-checkbox v-model="searchInfo.noShow" label="无发生额且余额为0不显示"></el-checkbox>
                                        </div>
                                    </div>
                                    <div class="buttons">
                                        <a class="button solid-button" @click="handleSearchSubmit">确定</a>
                                        <a class="button" @click="handleClose">取消</a>
                                        <a class="button" @click="handleReset">重置</a>
                                    </div>
                                </SearchInfoContainer>
                                <ErpRefreshButton></ErpRefreshButton>
                            </div>
                            <div class="main-tool-right">
                                <!-- <a class="button" v-permission="['multicolumnledger-canprint']" @click="printOpen">打印</a> -->
                                <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['multicolumnledger-canprint']">
                                    <li @click="handlePrint(0)">直接打印</li>
                                    <li @click="printDialogVisible = true">打印设置</li>
                                </Dropdown>
                                <a class="button ml-10" v-permission="['multicolumnledger-canexport']" @click="handleExport">导出</a>
                                <RefreshButton></RefreshButton>
                            </div>
                        </div>
                        <div v-loading="loading" element-loading-text="正在加载数据..." class="main-center">
                            <div class="main-title">
                                <div class="main-title-left">
                                    <Tooltip :content="title" :max-width="1580" :font-size="12">{{ "科目：" + title }}</Tooltip>
                                </div>
                            </div>
                            <AccountBooksTable
                                :data="tableData"
                                :columns="columns"
                                :loading="loading"
                                :page-is-show="true"
                                :empty-text="emptyText"
                                :layout="paginationData.layout"
                                :page-sizes="paginationData.pageSizes"
                                :page-size="paginationData.pageSize"
                                :total="paginationData.total"
                                :currentPage="paginationData.currentPage"
                                :scrollbar-show="true"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                @refresh="handleRerefresh"
                                :tableName="setModule"
                            >
                                <template #voucher>
                                    <el-table-column 
                                        label="凭证字号" 
                                        align="left" 
                                        header-align="center"
                                        prop="voucher"
                                        :width="getColumnWidth(setModule, 'voucher', 80)"
                                    >
                                        <template #default="scope">
                                            <a
                                                class="link"
                                                v-if="scope.row.vg_name"
                                                @click="jumpVoucherView(scope.row.p_id, scope.row.v_id)"
                                                >{{ scope.row.vg_name }}
                                            </a>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #debit>
                                    <el-table-column
                                        :label="debitColumn.label"
                                        :width="debitColumn.width"
                                        :min-width="debitColumn.minWidth"
                                        :align="debitColumn.align"
                                        :header-align="debitColumn.headerAlign"
                                    >
                                        <template v-if="debitColumn.children.length">
                                            <el-table-column
                                                v-for="item in debitColumn.children"
                                                :key="item.prop"
                                                :label="item.label"
                                                :align="item.align"
                                                :header-align="item.headerAlign"
                                                :prop="item.prop"
                                                :formatter="debitColumn.formatter"
                                                show-overflow-tooltip
                                                :min-width="item.minWidth"
                                                :width="item.width"
                                            >
                                                <template #header>
                                                    <el-tooltip
                                                        :disabled="isShowHeadTooltip"
                                                        :content="item.label"
                                                        placement="top"
                                                        effect="light"
                                                    >
                                                        <div class="head-tooltip" @mouseover="headMouseOver($event.target)">
                                                            {{ item.label }}
                                                        </div>
                                                    </el-tooltip>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template v-else></template>
                                    </el-table-column>
                                </template>
                            </AccountBooksTable>
                        </div>
                    </div>
                </div>
            </template>
            <template #voucher>
                <div>
                    <BooksVoucher ref="booksVoucherRef" :title="'查看凭证'" @voucher-cancel="handleVoucherCancel"></BooksVoucher>
                </div>
            </template>
        </ContentSlider>
        <!-- <el-dialog v-model="printDialogVisible" title="多栏账打印" center width="440" class="custom-confirm">
            <div class="print-content">
                <div class="print-main">
                    <div class="line-item mt-20 mb-20">
                        <span class="mr-20">图像方向：</span>
                        <el-radio-group v-model="printInfo.direction" disabled>
                            <el-radio label="1">横向</el-radio>
                            <el-radio label="0">纵向</el-radio>
                        </el-radio-group>
                    </div>
                    <div class="line-item pt-10 pb-10">
                        <el-checkbox v-model="printInfo.hidePrintDate" label="隐藏打印日期"></el-checkbox>
                    </div>
                    <div class="line-item mt-20 mb-20">
                        为了保证您的正常打印，请先下载安装
                        <a class="link" @click="globalWindowOpen('https://get.adobe.com/cn/reader/')">Adobe PDF阅读器</a>
                    </div>
                </div>
                <div class="buttons a_nofloat">
                    <a class="button" @click="printDialogVisible = false">取消</a>
                    <a class="button solid-button ml-20" @click="handlePrint">打印</a>
                </div>
            </div>
        </el-dialog> -->
    </div>
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        :title="'多栏账打印'"
        :printData="printInfo"
        :dir-disabled="true"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3)"
    />
</template>

<script lang="ts">
export default {
    name: "MultiColumnLedger",
};
</script>
<script setup lang="ts">
import { ref, reactive, provide, watch, onMounted, watchEffect } from "vue";
import { getUrlSearchParams, globalWindowOpen, globalPrint, globalExport,globalDirectPrint, globalPostPrint } from "@/util/url";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { setColumns } from "./utils";
import { usePagination } from "@/hooks/usePagination";
import ContentSlider from "@/components/ContentSlider/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { componentFinishKey } from "@/symbols";
import Dropdown from "@/components/Dropdown/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import BooksVoucher from "../components/BooksVoucher.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import Tooltip from "@/components/Tooltip/index.vue";
import { getGlobalToken } from "@/util/baseInfo";
import { ElConfirm } from "@/util/confirm";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { isLemonClient } from "@/util/lmClient";
import { getCookie, setCookie } from "@/util/cookie";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import usePrint from "@/hooks/usePrint";
import { setLocalStorage } from "@/util/localStorageOperate";

const setModule = "MultiColumnLedger";
const periodStore = useAccountPeriodStore();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const booksVoucherRef = ref<InstanceType<typeof BooksVoucher>>();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const asubRef = ref<InstanceType<typeof SubjectPicker>>();
const slots = ["main", "voucher"];
let currentSlot = ref("main");
const currentPeriodInfo = ref("");
// const periodInfo = ref("");
const title = ref("");
const _ = getGlobalLodash();
const searchInfo = reactive({
    startPid: Number(periodStore.getPeriodRange().start),
    endPid: Number(periodStore.getPeriodRange().end),
    subjectShow: "1", // 1 2
    showBalance: true, // 1 0
    assistType: "",
    assistSubject: "0",
    fcid: "-1",
    noShow: false, // 1 0
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, searchInfo.startPid, searchInfo.endPid); 
const getActPid = (start: number, end: number) => {
    searchInfo.startPid = start;
    searchInfo.endPid = end;
}

const asubCode = ref("");
const accountingStandard = Number(useAccountSetStore().accountSet?.accountingStandard);
const asubCodeDefault = () => {
    const cookieAsubCode = getCookie(`asubCode${getGlobalToken()}`);
    if (cookieAsubCode) {
        asubCode.value = cookieAsubCode;
        return;
    }
    switch (accountingStandard) {
        case 1:
            asubCode.value = "10112";
            break;
        case 2:
            asubCode.value = "10212";
            break;
        case 3:
            asubCode.value = "10099"; //民非科目：业务活动成本
            break;
        case 4:
            asubCode.value = "10058"; //旧农合社
            break;
        case 5:
            asubCode.value = "10080";
            break;
        case 6:
            asubCode.value = "10083";
            break;
        case 7:
            asubCode.value = "10045";
            break;
    }
};
const columns = ref<IColumnProps[]>([
    { label: "日期", prop: "V_DATE", minWidth: 100, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'V_DATE') },
    { slot: "voucher" },
    { label: "摘要", prop: "DESCRIPTION", minWidth: 100, align: "left", headerAlign: "center", width: getColumnWidth(setModule, 'DESCRIPTION') },
    { label: "借方", prop: "DEBIT", minWidth: 100, align: "right", headerAlign: "center", width: getColumnWidth(setModule, 'DEBIT') },
    { label: "贷方", prop: "CREDIT", minWidth: 100, align: "right", headerAlign: "center", width: getColumnWidth(setModule, 'CREDIT') },
    { label: "方向", prop: "DIRECTION", minWidth: 50, align: "center", headerAlign: "center", width: getColumnWidth(setModule, 'DIRECTION') },
    { label: "余额", prop: "TOTAL", width: 100, align: "right", headerAlign: "center", resizable: false },
]);
const debitColumn = ref<any>();
const tableData = ref<any[]>([]);
const loading = ref(false);
const subjectShow = ref(true);
const NOSHOW = ref(false);
const assistTypeShow = ref(false);
const assistSubjectShow = ref(false);
const fcTypeShow = ref(false);
const isAssist = ref(0);
interface ISelectOptions {
    id: string;
    label: string;
}
const assistTypeList = ref<ISelectOptions[]>([]);
const assistSubjectList = ref<ISelectOptions[]>([]);
const fcTypeList = ref<ISelectOptions[]>([]);

const { printDialogVisible, printInfo, otherOptions } = usePrint(
    "multiColumnLedger",
    window.jAccountBooksHost + "/api/MultiColumnLedger/Print",
    {},
    true
);
printInfo.value.direction = 1;

const getPrintParams = () => {
    return {
        isAssist: isAssist.value,
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        fcid: searchInfo.fcid,
        subject: asubCode.value,
        subjectShow: searchInfo.subjectShow,
        assistType: searchInfo.assistType,
        assistSubject: searchInfo.assistSubject,
        showBalance: searchInfo.showBalance ? 1 : 0,
        noShow: searchInfo.noShow ? 1 : 0,
        async: !isLemonClient(),
    };
};

const handlePrint = (type:0|3) => {
    if (!canSearch()) return;

    printDialogVisible.value = false;
    // 打印前进行校验
    let checkParams: any = { ...getPrintParams() };
    delete checkParams.appasid;
    request({
        url: "/api/MultiColumnLedger/CheckRows",
        method: "post",
        params: checkParams,
    }).then((res: any) => {
        if (res.data) {
            ElConfirm("亲，您选择的科目具有较多的下级科目，建议您导出到Excel后打印，直接打印将会有显示不全的问题，确定要继续吗？").then(
                (r: boolean) => {
                    if (r) {
                        print(type);
                    }
                }
            );
        } else {
            print(type);
        }
    });
};
function print(type:0|3) {
    const params = {
        ...getPrintParams(),
        seniorModelJson: JSON.stringify(printInfo.value),
    };
    if(type === 0){
        globalDirectPrint(window.jAccountBooksHost + "/api/MultiColumnLedger/DirectPrint?async=" + !isLemonClient(), "post", params,"打印中，请稍候...");
        return;
    }else{
        setLocalStorage('multiColumnLedgerPrint', JSON.stringify(printInfo.value));
        globalPostPrint(window.jAccountBooksHost + "/api/MultiColumnLedger/Print?async=" + !isLemonClient(), params);
    }
}
const handleExport = () => {
    if (!canSearch()) return;
    globalExport(window.jAccountBooksHost + "/api/MultiColumnLedger/Export?" + getUrlSearchParams({ ...getPrintParams() }));
};
const handleSearchSubmit = () => {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return false;
    }
    if (!asubCode.value) {
        ElNotify({
            type: "warning",
            message: "亲，请选择会计科目",
        });
        return false;
    }
    periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    paginationData.currentPage = 1;
    setCookie(`asubCode${getGlobalToken()}`, asubCode.value, "d30");
    handleSearch();
    handleClose();
};
const emptyText = ref("暂无数据");
const handleSearch = () => {
    if (!canSearch) return;
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
    const params = {
        isAssist: isAssist.value,
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        fcid: searchInfo.fcid,
        subject: asubCode.value,
        subjectShow: searchInfo.subjectShow,
        assistType: searchInfo.assistType,
        assistSubject: searchInfo.assistSubject,
        showBalance: searchInfo.showBalance ? 1 : 0,
        noShow: searchInfo.noShow ? 1 : 0,
        pageSize: paginationData.pageSize,
        pageNumber: paginationData.currentPage,
    };
    loading.value = true;
    emptyText.value = " ";
    request({
        url: "/api/MultiColumnLedger",
        method: "get",
        params,
    })
        .then((res: any) => {
            columns.value = setColumns(res.data);
            debitColumn.value = _.cloneDeep(columns.value[columns.value.length - 1]);
            columns.value.splice(columns.value.length - 1, 1, { slot: "debit" });
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            title.value = asubRef.value?.asubName as string;
            setInfos();
            if (res.data.rows.length === 0) {
                emptyText.value = "暂无数据";
            }
        })
        .catch((error) => console.log(error))
        .finally(() => (loading.value = false));
};
const handleClose = () => containerRef.value?.handleClose();
const handleReset = () => {
    asubCode.value = "";
    searchInfo.subjectShow = "1";
    searchInfo.showBalance = true;
    searchInfo.assistType = "";
    searchInfo.assistSubject = "";
    searchInfo.fcid = "-1";
    searchInfo.noShow = false;
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    assistTypeShow.value = false;
    assistSubjectShow.value = false;
    fcTypeShow.value = false;
    subjectShow.value = true;
    NOSHOW.value = false;
};

const handleVoucherCancel = () => {
    currentSlot.value = "main";
    handleSearch();
};

const setInfos = () => {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
};

const judgeAsubCode = (subject: string) => {
    request({
        url: `/api/MultiColumnLedger/CheckSubject?asubId=${subject}`,
        method: "post",
    }).then((res: any) => {
        if (res.data == "Failed") {
            // ElNotify({
            //     type: "warning",
            //     message: "您筛选的科目无法生成多栏式明细账，请选择有数据的非末级科目或涉及辅助核算的项目！",
            // });
            // asubCode.value = "";
            // return false;

            assistTypeShow.value = false;
            assistSubjectShow.value = false;
            searchInfo.assistType = "";
            searchInfo.assistSubject = "";
        } else {
            if (res.data == "1") {
                isAssist.value = 0;
                subjectShow.value = true;
                searchInfo.assistType = "";
                assistTypeShow.value = false;
                assistSubjectShow.value = false;
                searchInfo.noShow = false;
                NOSHOW.value = false;
            }
            if (res.data == "2") {
                subjectShow.value = false;
                request({
                    url: `/api/AssistingAccountingType/GetListByAsub?asubId=${subject}`,
                    method: "post",
                }).then((res: any) => {
                    if (res.state === 1000) {
                        isAssist.value = 1;
                        assistTypeList.value = res.data.map((item: any) => {
                            return { id: String(item.aatypeId), label: item.aatypeName };
                        });
                        searchInfo.assistType = assistTypeList.value[0].id;
                        assistTypeShow.value = true;
                        searchInfo.noShow = false;
                        NOSHOW.value = true;
                        getAssist(searchInfo.assistType);
                    }
                });
            }
            request({
                url: `/api/Currency/GetListByAsub?asubId=${subject}`,
                method: "post",
            }).then((res: any) => {
                if (res.data && res.data.length) {
                    fcTypeShow.value = true;
                    fcTypeList.value = res.data.map((item: any) => {
                        return { id: String(item.id), label: item.name };
                    });
                    searchInfo.fcid = "-1";
                } else {
                    fcTypeShow.value = false;
                    searchInfo.fcid = "-1";
                }
            });
        }
    });
};
const getAssistInterfaceName: any = {
    10001: "CustomerList", //客户
    10002: "VendorList", //  供应商
    10003: "EmployeeList", //职员
    10004: "DepartmentList", //部门
    10005: "ProjectList", //项目
    10006: "StockList", //存货
    10007: "CashFlowList", //现金流
};
const assistSubjectChange = (val: string) => {
    getAssist(val);
};
const getAssist = (AA_Type: string) => {
    let reqUrl = "";
    if (Object.keys(getAssistInterfaceName).includes(AA_Type)) {
        reqUrl = `/api/AssistingAccounting/${getAssistInterfaceName[AA_Type]}?showAll=${true}&onlyLeaf=${true}`;
    } else {
        reqUrl = `/api/AssistingAccounting/CustomList?aaTypeId=${AA_Type}&showAll=${true}`;
    }

    request({
        url: reqUrl,
        method: "get",
    }).then((res: any) => {
        if (res.state === 1000) {
            assistSubjectShow.value = true;

            assistSubjectList.value = res.data.map((item: any) => {
                return {
                    id: item.aaeid,
                    label: item.aaname,
                };
            });
            searchInfo.assistSubject = "0";
        }
    });
};

const canSearch = () => {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return false;
    }
    if (asubCode.value == "") {
        ElNotify({
            type: "warning",
            message: "亲，请选择会计科目",
        });
        return false;
    }
    return true;
};
const jumpVoucherView = (pid: number, vid: number) => {
    // 跳转到查看凭证
    booksVoucherRef.value?.initVoucherData(pid, vid);
    // checkVoucherRef.value?.loadVoucher(pid, vid);
    currentSlot.value = "voucher";
};
const isShowHeadTooltip = ref(true);
const headMouseOver = (target: any) => {
    if (target.scrollWidth > target.clientWidth) {
        isShowHeadTooltip.value = false;
    } else {
        isShowHeadTooltip.value = true;
    }
};
watch(asubCode, (val: string) => {
    if (val != "") judgeAsubCode(val);
});
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    handleSearch();
});
provide(componentFinishKey, () => {});
onMounted(() => {
    // periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    document.body.scrollTop = 0;
    asubCodeDefault();
    handleSearch();
});
</script>

<style lang="less" scoped>
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";

.main-content {
    background-color: #fff;
    .main-title {
        margin: 0;
        display: flex;
        .main-title-left {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1;
            text-align: left;
            :deep(.span_wrap) {
                white-space: nowrap !important;
                overflow: hidden;
                display: inline-block !important;
            }
        }
    }
    .main-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        .main-top-left {
            height: 32px;
        }
    }
    .main-center {
        display: flex;
        flex-direction: column;
        :deep(.table) {
            flex: 1;
            min-height: 0;
        }
    }
}
.print-content {
    .print-main {
        text-align: left;
        display: inline-block;

        .line-item {
            height: 20px;
            :deep(.el-checkbox) {
                font-weight: normal;
            }
        }
    }
}
.head-tooltip {
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
}
</style>
