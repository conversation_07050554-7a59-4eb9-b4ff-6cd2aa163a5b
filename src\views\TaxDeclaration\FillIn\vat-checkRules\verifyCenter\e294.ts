// @ts-nocheck
/* eslint-disable */
import i from "./24cc.js"
function r(t) {
  return (
    (function (t) {
      if (Array.isArray(t)) {
        return n(t)
      }
    })(t) ||
    (function (t) {
      if ((typeof Symbol != "undefined" && t[Symbol.iterator] != null) || t["@@iterator"] != null) {
        return Array.from(t)
      }
    })(t) ||
    (function (t, e) {
      if (t) {
        if (typeof t == "string") {
          return n(t, e)
        }
        var a = {}.toString.call(t).slice(8, -1)
        if (a === "Object" && t.constructor) {
          a = t.constructor.name
        }
        if (a === "Map" || a === "Set") {
          return Array.from(t)
        } else if (a === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)) {
          return n(t, e)
        } else {
          return undefined
        }
      }
    })(t) ||
    (function () {
      throw new TypeError(
        "Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
      )
    })()
  )
}
function n(t, e) {
  if (e == null || e > t.length) {
    e = t.length
  }
  for (var a = 0, i = Array(e); a < e; a++) {
    i[a] = t[a]
  }
  return i
}
function l(t, e) {
  var a = Object.keys(t)
  if (Object.getOwnPropertySymbols) {
    var i = Object.getOwnPropertySymbols(t)
    if (e) {
      i = i.filter(function (e) {
        return Object.getOwnPropertyDescriptor(t, e).enumerable
      })
    }
    a.push.apply(a, i)
  }
  return a
}
function o(t) {
  for (var e = 1; e < arguments.length; e++) {
    var a = arguments[e] ?? {}
    if (e % 2) {
      l(Object(a), true).forEach(function (e) {
        d(t, e, a[e])
      })
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(t, Object.getOwnPropertyDescriptors(a))
    } else {
      l(Object(a)).forEach(function (e) {
        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(a, e))
      })
    }
  }
  return t
}
function d(t, e, a) {
  if (
    (e = (function (t) {
      var e = (function (t, e) {
        if (s(t) != "object" || !t) {
          return t
        }
        var a = t[Symbol.toPrimitive]
        if (a !== undefined) {
          var i = a.call(t, e || "default")
          if (s(i) != "object") {
            return i
          }
          throw new TypeError("@@toPrimitive must return a primitive value.")
        }
        return (e === "string" ? String : Number)(t)
      })(t, "string")
      if (s(e) == "symbol") {
        return e
      } else {
        return e + ""
      }
    })(e)) in t
  ) {
    Object.defineProperty(t, e, {
      value: a,
      enumerable: true,
      configurable: true,
      writable: true,
    })
  } else {
    t[e] = a
  }
  return t
}
function s(t) {
  s =
    typeof Symbol == "function" && typeof Symbol.iterator == "symbol"
      ? function (t) {
          return typeof t
        }
      : function (t) {
          if (t && typeof Symbol == "function" && t.constructor === Symbol && t !== Symbol.prototype) {
            return "symbol"
          } else {
            return typeof t
          }
        }
  return s(t)
}
var c = /\d+/
var u = {}
function p(t, e) {
  for (var a = e.length - 1; a >= 0; a--) {
    var r = e[a]
    if (typeof r.functionBody == "string") {
      t[r.functionDm] = new Function(`return  ${r.functionBody}`).call()
    } else {
      if (typeof r.functionBody != "function") {
        throw new Error("无法解析函数")
      }
      t[r.functionDm] = r.functionBody
    }
  }
  t.round = function (t, e) {
    return Number(Object(i)(Number(t), e))
  }
  t.getObjFromList = function (t, e, a) {
    for (var i = 0; i < t.length; i++) {
      if (t[i][e] === a) {
        return t[i]
      }
    }
    return null
  }
  return t
}
function v(t, e) {
  u.content = t
  return new Function(
    `with(this){return ${(function (t) {
      return t.replace(/ /gi, "")
    })(e)}}`,
  ).call(t)
}
function b(t, e) {
  var a
  var i
  var r
  var n = e
  if ((a = e.match(/【(.*?)】/gi)) !== null && a !== undefined) {
    a.forEach(function (e) {
      try {
        var a = v(t, e.slice(1, -1))
        n = n.replace(e, `【${a}】`)
      } catch (t) {}
    })
  }
  if ((i = e.match(/\$\{(.*?)\}/gi)) !== null && i !== undefined) {
    i.forEach(function (e) {
      try {
        r = v(t, e.slice(2, -1))
        n = n.replace(e, r)
      } catch (t) {}
    })
  }
  return n
}
function f(t) {
  return t.split(/[\.\[\]]/).filter(function (t) {
    return t
  })
}
function g(t, e) {
  if (typeof (t = t || e) == "string") {
    t = JSON.parse(t)
  }
  return t
}
function h(t, e, a) {
  t = g(t, {})
  e = g(e, [])
  a = g(a, [])
  var i = e.slice()
  p(t, a)
  i.sort(function (t, e) {
    return t.messagePriority - e.messagePriority
  })
  i = JSON.parse(JSON.stringify(i))
  for (var n = 0; n < i.length; n++) {
    var l = i[n]
    var d = true
    if (Number(i[n].validateType) !== 3 && Number(i[n].isEnable) !== 0) {
      try {
        if (!v(t, l.preTable)) {
          throw "返回为假值跳过"
        }
      } catch (t) {
        l.passed = "pass"
        l.passReason = "preTable 未通过"
        continue
      }
      l.passResult = l.passResult == null || l.passResult
      try {
        if (s((d = v(t, l.ruleData))) !== "object" || !(d instanceof Array)) {
          d = [d]
        }
        var c = []
        var f = []
        var h = []
        for (var y = 0; y < d.length; y++) {
          if (d[y] !== undefined) {
            h.push(!!d[y] == l.passResult ? "pass" : "unpass")
            if (l.passed !== "unpass") {
              l.passed = h[y]
            }
            c.push(b(t, l.ruleMessage.replace(/\[\*\]/gi, "[" + y + "]")))
            f.push(b(t, l.ruleData.replace(/\[\*\]/gi, "[" + y + "]")))
          }
        }
        if (f.length) {
          var m
          var _ = f.map(function (t, e) {
            return o(
              o({}, l),
              {},
              {
                messageRules: t,
                ruleMessage: c[e],
                passed: h[e],
              },
            )
          })
          ;(m = i).splice.apply(m, [n, 1].concat(r(_)))
          n = n + f.length - 1
        }
      } catch (t) {
        l.passed = "unexecuted"
        // l.ruleMessage = t.toString();
        // l.messageRules = "";
        l.errorMessage = t.toString()
        if (u.customErrorLog) {
          u.customErrorLog({
            error: t,
            data: l,
          })
        }
      }
    } else {
      l.passed = "pass"
      l.passReason = "不提示 或者未启用 不校验 直接通过"
    }
  }
  return i
}
function y(t, e) {
  for (var a = t[e], i = [], r = 0; r < a.length; r++) {
    var n = a[r]
    if (~n.indexOf("customGet_")) {
      i = i.concat(y(t, n))
    } else {
      i.push(n)
    }
  }
  return i
}
function m(t, e, a) {
  var i = (function (t, e) {
    var a = {}
    for (var i = 0; i < e.length; i++) {
      var r = e[i]
      if (~r.functionDm.indexOf("customGet_")) {
        var n = []
        try {
          n =
            r.dependencies && r.dependencies.indexOf("function") == 0
              ? new Function(`return ${r.dependencies}`)().call(t)
              : JSON.parse(r.dependencies)
        } catch (t) {}
        a[r.functionDm] = n
      }
    }
    return a
  })(t, a)
  var r = /getObjFromList\([^)]+\)\.[a-zA-Z0-9_]+/g
  var n = /sumList\s*\([^)]+\)/g
  var l = new RegExp(`${"ywbwDto".replace(".", "\\.")}\\.[a-zA-Z0-9\\[\\]\\*_\\.]+`, "g")
  var o = /customGet_[a-zA-Z0-9_]+/g
  var d = {}
  for (var s = 0; s < e.length; s++) {
    var c = e[s]
    try {
      var u = c.replace("===", "@@@").split("@@@")
      var p = u[0]
      var v = u[1]
      p = p.trim()
      var b = []
      for (var f = ((a = v.match(o) || []), 0); f < a.length; f++) {
        var g = a[f]
        if (~g.indexOf("customGet_")) {
          b = b.concat(y(i, g))
        } else {
          b.push(g)
        }
      }
      b = b.concat(v.match(r) || [])
      v = v.replace(r, "1")
      var h = []
      for (var m = v.match(n) || [], _ = 0; _ < m.length; _++) {
        var w = m[_].trim()
          .replace(/^sumList\s*\((.+)\)$/, "$1")
          .split(",")
        h.push(`${w[0].trim()}[*].${w[1].trim().replace(/['"]/g, "").trim()}`)
      }
      b = b.concat(h)
      v = v.replace(n, "1")
      b = b.concat(v.match(l) || [])
      for (var x = 0; x < b.length; x++) {
        var S = b[x]
        if (S !== p) {
          d[S] ||= []
          if (!~d[S].indexOf(c)) {
            d[S].push(c)
          }
        }
      }
    } catch (t) {}
  }
  return d
}
function _(t, e) {
  var a = e.substring(0, e.indexOf(")") + 1)
  var i = t.getObjFromList
  var r = null
  t.getObjFromList = function (t, e, a) {
    for (var i = 0; i < t.length; i++) {
      if (t[i][e] === a) {
        return i
      }
    }
    return null
  }
  try {
    r = v(t, a)
  } catch (t) {}
  t.getObjFromList = i
  if (r != null) {
    return (
      e.substring(e.indexOf("(") + 1, e.indexOf(",")).trim() +
      "[" +
      r +
      "]" +
      e
        .substring(e.indexOf(")") + 1)
        .trim()
        .replace(/ /gi, "")
    )
  } else {
    return null
  }
}
function w(t) {
  if (!Array.isArray(t)) {
    return null
  }
  var e = []
  for (var a = 0; a < t.length; a++) {
    if (e.indexOf(t[a]) === -1) {
      e.push(t[a])
    }
  }
  return e
}
function x(t, e, a) {
  if (~a.indexOf("getObjFromList")) {
    var i = _(t, a)
    if (i) {
      return w((e[a] || []).concat(e[i] || []))
    }
  }
  return w(e[a] || [])
}
function S(t, e, a) {
  var i = e.slice()
  var r = u.custom
  u.custom = function (t, e) {
    var n = null
    var l = ""
    if (~e.indexOf("=")) {
      l = e.substring(0, e.indexOf("=")).trim()
      try {
        n = r.call(this, t, l)
      } catch (t) {}
    }
    var o = r.call(this, t, e)
    if (o != n && n != null && x(t, a, l).length) {
      i = w(x(t, a, l).concat(i))
    }
    return o
  }
  for (var n = null; (n = i.shift()); ) {
    try {
      u.custom(t, n.replace("===", "="))
    } catch (t) {}
  }
  u.custom = r
}
u.run = v
u.initFunc = p
u.custom = v
u.customTip = b
u.customByRules = h
u.validateAll = h
u.initPathValueAfter = function (t) {
  var e = null
  var a = f(t)
  var i = a.map(function (t) {
    return c.test(t)
  })
  for (var r = a.length - 1; r >= 0; r--) {
    var n = a[r]
    var l = i[r] ? [] : {}
    l[n] = e || ""
    e = l
  }
}
u.initPathValue = function (_param, t) {
  for (
    var e = f(arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ""),
      a = e.map(function (t) {
        return c.test(t)
      }),
      i = 0;
    i < e.length;
    i++
  ) {
    var r = e[i]
    var n = i + 1 >= a.length ? "" : a[i + 1] ? [] : {}
    if (t[r] != null) {
      n = t[r]
    } else {
      t[r] = n || ""
    }
    t = n
  }
}
u.setPathValue = function (e = {}, _param3, t) {
  for (var a = f(arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ""), i = 0; i < a.length - 1; i++) {
    var r = a[i]
    if (e[r] == null) {
      throw "obj 中不存在 " + r
    }
    e = e[r]
  }
  e[a[a.length - 1]] = t
}
u.getPathValue = function (t = {}) {
  for (var e = f(arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ""), a = 0; a <= e.length - 1; a++) {
    var i = e[a]
    if (t[i] == null) {
      throw "obj 中不存在 " + i
    }
    t = t[i]
  }
  return t
}
u.validateAllForMessage = function () {
  var t = h.apply(this, arguments)
  var e = {
    alert: [],
    confirm: [[], [], []],
  }
  for (var a = t.length - 1; a >= 0; a--) {
    var i = t[a]
    if (i.passed === "unpass") {
      if (Number(i.validateType) === 1) {
        e.alert.push(i)
      } else if (Number(i.validateType) === 2) {
        e.confirm[Number(i.validateType) - 1].push(i)
      }
    }
  }
  e.alert.sort(function (t, e) {
    return Number(t.messagePriority) - Number(e.messagePriority)
  })
  a = e.alert.length - 1
  for (; a >= 0; a--) {
    e.alert[a] = e.alert[a].ruleMessage
  }
  for (a = e.confirm.length - 1; a >= 0; a--) {
    for (var r = e.confirm[a].length - 1; r >= 0; r--) {
      e.confirm[a][r] = e.confirm[a][r].ruleMessage
    }
    if (e.confirm[a].length === 0) {
      e.confirm.splice(a, 1)
    }
  }
  return e
}
u.validateAllForMessageAndDeps = function () {
  var t = h.apply(this, arguments)
  var e = []
  var a = []
  for (var i = t.length - 1; i >= 0; i--) {
    var r = t[i]
    if (r.passed === "unpass") {
      if (Number(r.validateType) === 1) {
        e.push({
          type: "error",
          content: r.ruleMessage,
          messageRules: r.messageRules,
          messagePriority: r.messagePriority,
          preTables: r.preTables,
        })
      } else if (Number(r.validateType) === 2) {
        a.push({
          type: "warning",
          content: r.ruleMessage,
          messageRules: r.messageRules,
          messagePriority: r.messagePriority,
          preTables: r.preTables,
        })
      }
    }
  }
  e.sort(function (t, e) {
    return Number(t.messagePriority) - Number(e.messagePriority)
  })
  a.sort(function (t, e) {
    return Number(t.messagePriority) - Number(e.messagePriority)
  })
  return [].concat(e, a)
}
u.customErrorLog = function (t) {
  t.error
  t.data
}
u.calc = function (t, e, a, i) {
  t = g(t, {})
  e = g(e, [])
  a = g(a, [])
  i = g(i, [])
  p(t, a)

  var r = []
  for (var n = 0; n < e.length; n++) {
    var l = e[n]
    var o = false
    try {
      console.log(u.custom, "custom")

      o = u.custom(t, l.preTable)
      console.log(o, "oooooo")
    } catch (t) {}
    if (l.ruleCalc && o) {
      r.push(l.ruleData)
    }
  }
  var d = (function (t, e) {
    function a(t, a) {
      if (e[t]) {
        for (var i = 0; i < a.length; i++) {
          if (!~e[t].indexOf(a[i])) {
            e[t].push(a[i])
          }
        }
      } else {
        e[t] = a
      }
    }
    for (var i in e) {
      if (Object.hasOwnProperty.call(e, i)) {
        if (~i.indexOf("[*]")) {
          var r = []
          var n = i.substring(0, i.indexOf("[*]"))
          try {
            r = v(t, n) || []
          } catch (t) {}
          for (var l = 0; l < r.length; l++) {
            a(i.replace("[*]", JSON.stringify([l])).replace(/ /gi, ""), e[i])
          }
          continue
        }
        if (~i.indexOf("getObjFromList")) {
          var o = _(t, i)
          if (o) {
            a(o, e[i])
          }
          continue
        }
      }
    }
    return e
  })(t, m(t, r, a) || {})
  if (!i || i.length <= 0) {
    S(t, r, d)
  } else {
    ;(function (e) {
      for (var a = e.length - 1, i = ""; a >= 0; ) {
        if (~(i = e[a]).indexOf("[*]")) {
          var r = i.split("[*]")[0].trim()
          var n = 0
          try {
            n = v(t, r + ".length")
          } catch (t) {}
          var l = []
          for (var o = 0; o < n; o++) {
            l.push(i.replace("[*]", "[" + o + "]"))
          }
          e.splice.apply(e, [a, 1].concat(l))
        }
        a--
      }
    })(i)
    var s = {}
    for (var c = 0; c < i.length; c++) {
      s[i[c]] = v(t, i[c])
    }
    ;(function (t, e, a, i) {
      for (var r = 0; r < a.length; r++) {
        var n = a[r]
        var l = i[n]
        try {
          new Function("newValue", "with(this){return " + n + "=newValue }").call(t, l)
        } catch (t) {}
        S(t, x(t, e, n), e)
      }
    })(t, d, i, s)
  }
  console.log(r, "r")

  return JSON.stringify(t)
}
export default u
// global.engineJs = u;
