import { ref } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import { request, type IResponseModel } from "@/util/service";
import { useThirdPartInfoStoreHook } from "./thirdpart";
import { getPromptMessageApi, type IProInfo } from "@/api/proInfo";
import dayjs from "dayjs";

interface ITrialStatus {
    isTrial: boolean;
    isExpired: boolean;
    remainingDays: number;
    expiredTime: string;
}

export const useTrialStatusStore = defineStore("trialStatus", () => {
    const isTrial = ref(false);
    const isExpired = ref(false);
    const remainingDays = ref(-1);
    const expiredTime = ref("");
    const proExpiredTime = ref("");
    const proExpiredData = ref<IProInfo>({
        startTime: "",
        subscriptionType: "",
        amount: 1,
        needRemind: false,
        isExpired: false,
        expiredTime: "",
        remainingDays: 365, // 随便定的初始值为一年日期
    });

    const getTrialStatus = () => {
        return new Promise<ITrialStatus>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                if (window.isProSystem || window.isErp || useThirdPartInfoStoreHook().isThirdPart) {
                    resolve({
                        isTrial: false,
                        isExpired: false,
                        remainingDays: -1,
                        expiredTime: "",
                    });
                } else {
                    request({
                        url: `/api/Trial/GetAccountSetStatus`,
                        method: "post",
                    }).then((result: IResponseModel<ITrialStatus>) => {
                        if (result.state === 1000) {
                            if (result.data.isTrial) {
                                isTrial.value = result.data.isTrial;
                                isExpired.value = result.data.isExpired;
                                remainingDays.value = result.data.remainingDays;
                                expiredTime.value = result.data.expiredTime;
                            }
                            resolve(result.data);
                        }
                    });
                }
            }
        });
    };
    const getProStatus = () => {
        getPromptMessageApi().then((res: any) => {
            if (res.statusCode == 200) {
                proExpiredData.value = res.data;
                proExpiredTime.value = dayjs(proExpiredData.value.expiredTime).format("YYYY-MM-DD");
                if (proExpiredData.value.remainingDays < 0) {
                    window.location.href =  window.wwwHost + "/Partial.aspx?p=w#/workbench"
                }
            }
        });
    };

    return { isTrial, isExpired, remainingDays, expiredTime, getTrialStatus, getProStatus, proExpiredTime, proExpiredData };
});

export function useTrialStatusStoreHook() {
    return useTrialStatusStore(store);
}
