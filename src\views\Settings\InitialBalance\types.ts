export interface IStandardTabs {
    id: number;
    name: string;
    alias: string;
    active: Boolean;
}
export interface IAssistDetail {
    supplierName?: string;
    supplierCode?: string;
}

export interface IStrObject {
    [key: string]: IEntryPair[] | undefined;
}
export interface INumObject {
    [key: number]: IEntryPair[] | undefined;
}
export interface IEntryPair {
    id: number;
    name: string;
}
export interface IAssistTypeForDetail {
    type: number;
    name: string;
}

export interface ISupplierList {
    label: string;
    value: string;
}
export interface IconfirmAssitData {
    aaeid?: string;
    code: string;
    aaname?:string;
    name?: string;
    aatype?: number;
    aanum?: string;
    asubCode?: string;
    unfold?:boolean;
}

export interface IAssistingAccountingList {
    aaeid: number;
    aaname: string;
    aanum: string;
    aatype: number;
    asid: number;
    createdBy: number;
    createdDate: string;
    preName: string;
    status: number;
    uscc: string;
    value01: string;
}

export interface ISelection {
    aaeid: string;
    aaname: string;
    aanum: string;
    aatype: number;
    asid: number;
    createdBy: number;
    createdDate: string;
    preName: string;
    status: number;
    uscc: string;
    value01: string;
}

export interface IAssitInfo {
    aaeid: number;
    aaname: string;
    aanum: string;
    aatype: number;
    acromym: string;
    asid: number;
    createdBy: number;
    createdDate: string;
    displayOrder: number;
    jsonData: string;
    status: number;
    typeName: string;
    uscc: string;
}

export interface ITypeName {
    aaeid: number;
    aaname: string;
    aanum: string;
    aatype: number;
    acromym: string;
    asid: number;
    createdBy: number;
    createdDate: string;
    displayOrder: number;
    jsonData: string;
    status: number;
    typeName: string;
    uscc: string;
}

export interface IEditAssistLine {
    assistDetail:IEditAssistInfo[]
}

export interface IEditAssistInfo {
    aaeid: string;
    aaname: string;
    aanum: string;
    unfold: boolean;
    aatype: number;
}