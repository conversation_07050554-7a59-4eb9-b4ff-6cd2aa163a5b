<template>
    <Select 
        v-model="fcid" 
        :teleported="false" 
        :filterable="true" 
        :filter-method="fcFilterMethod"
        :style="{width: width}"
    >
        <Option 
            :label="item.label" 
            :value="item.id" 
            v-for="item in showfcList" 
            :key="item.id"
        ></Option>
    </Select>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from "vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
interface ISelect { 
    id: number; 
    label: string; 
    code: string;
}

const props = withDefaults(
    defineProps<{
        fcid: number;
        fcList: ISelect[];
        width?: string;
    }>(),
    {
        fcid: -1,
        fcList: () => [],
        width: "120px"
    }
);
const emit = defineEmits<{
    (event: "update:fcid", args: number): void;
}>();

const fcid = computed({
    get() {
        return props.fcid;
    },
    set(value: number) {
        emit("update:fcid", value);
    },
});
const showfcList = ref<ISelect[]>([]);
watchEffect(() => { 
    showfcList.value = JSON.parse(JSON.stringify(props.fcList));    
});
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, props.fcList, 'label');
}

</script>
