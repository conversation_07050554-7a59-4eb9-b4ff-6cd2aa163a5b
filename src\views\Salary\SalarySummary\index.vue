<template>
   <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">工资统计表</div>
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <SearchInfoContainer ref="containerRef">
                                <template v-slot:title>{{ currentPeriodInfo }}</template>
                                <div class="line-item first-item input">
                                    <div class="line-item-title">会计期间：</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.periodS"
                                            v-model:endPid="searchInfo.periodE"
                                            :clearable="false"
                                            :editable="false"
                                            :dateType="'month'"
                                            :value-format="'YYYYMM'"
                                            :label-format="'YYYY年MM月'"
                                            :disabledDateStart="disabledDate"
                                            :disabledDateEnd="disabledDate"
                                            @user-change="stopCloseSearchPopover"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">部门：</div>
                                    <div class="line-item-field">
                                        <div style="width: 300px;">
                                            <Select 
                                                :teleported="false" 
                                                v-model="searchInfo.department" 
                                                placeholder="请选择"
                                                class="item"
                                                @visible-change="handleVisibleChange"
                                                :filterable="true"
                                                :filter-method="departFilterMethod"
                                            >
                                                <Option
                                                    v-for="item in showDepartmentList"
                                                    :value="item.value"
                                                    :label="item.label"
                                                    :key="item.value"
                                                ></Option>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                                <div class="buttons" style="text-align: left">
                                    <a class="button solid-button" @click="handleSearch">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                        </div>
                        <div class="main-tool-right">
                            <div class="mr-20">
                                <el-checkbox
                                    v-model="searchInfo.showDetail"
                                    label="显示员工工资明细"
                                    @change="changeDetail"
                                ></el-checkbox>
                            </div>
                            <a class="button mr-10" v-permission="['salarystatistics-canprint']" @click="handleExport(0)"> 打印 </a>
                            <a class="button solid-button" v-permission="['salarystatistics-canexport']" @click="handleExport(1)">导出</a>
                        </div>
                    </div>
                    <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                        <Table
                            :class="searchInfo.showDetail ? 'erpfirstBorder' : ''"
                            ref="tableRef"
                            :data="tableData"
                            :columns="columns"
                            :pageIsShow="true"
                            :empty-text="emptyText"
                            :page-sizes="paginationData.pageSizes"
                            :page-size="paginationData.pageSize"
                            :total="paginationData.total"
                            :current-page="paginationData.currentPage"
                            :scrollbarShow="true"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            @refresh="handleRerefresh"
                            :object-span-method="objectSpanMethod"
                            :tableName="setModule"
                            @cell-mouse-enter="mouseEnter"
                            @cell-mouse-leave="mouseLeave"
                            :row-class-name="rowClassName"
                        >
                        </Table>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "SalarySummary",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed, watchEffect } from "vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Table from "@/components/Table/index.vue";
import { request } from "@/util/service";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { usePagination } from "@/hooks/usePagination";
import { setColumns} from "./utils";
import type { ITableItemDep, ITableItemEm, SpanMethodProps, ISelectItem, IPeriodItem } from "./types";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams, globalExport, globalPrint } from "@/util/url";
import dayjs from 'dayjs';
import DatePicker from "@/components/DatePicker/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "SalarySummary";
const slots = ["main"];
const currentSlot = ref("main");
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const currentPeriodInfo = ref("");
const searchInfo = reactive({
    periodS: "",
    periodE: "",
    department: "0",
    showDetail: false,
});
const periodList = ref<IPeriodItem[]>([]);
function getPeriodList() {
    return request({
        url: `/api/Salary/GetSearchMonthList`,
        method: "post",
    });
}
const periodInfo = computed(() => {
    if (!searchInfo.periodS || !searchInfo.periodE) {
        return "----年--月";
    }
    if (searchInfo.periodS === searchInfo.periodE) {
        return getPeriodInfoByPID(searchInfo.periodS);
    } else {
        return getPeriodInfoByPID(searchInfo.periodS) + "—" + getPeriodInfoByPID(searchInfo.periodE);
    }
});
function getPeriodInfoByPID(month: string) {
    return periodList.value.filter((item: ISelectItem)=> item.value === month)[0].label;
}

function handleClose() {
    containerRef.value?.handleClose();
}
//保存初始值用于重置
let saveStart = "";
let saveEnd = "";
function handleReset() {
    searchInfo.department = "0";
    searchInfo.periodS = saveStart;
    searchInfo.periodE = saveEnd;
}
const setInfos = () => (currentPeriodInfo.value = periodInfo.value);

function handleSearch() {
    if (Number(searchInfo.periodS) > Number(searchInfo.periodE)) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return;
    }
    setInfos();
    changeDetail();
    containerRef.value?.handleClose();
}

const handleVisibleChange = (visible: boolean) => {
    if (!visible) {
        stopCloseSearchPopover();
    }
};
function stopCloseSearchPopover() {
    containerRef.value?.stopCloseSearchPopover();
}

const departmentList = ref<ISelectItem[]>([{value: "0", label: "全部"}]);
function getDepartmentApi() {
    request({
        url: `/api/AssistingAccounting/DepartmentList?showAll=true&onlyLeaf=false`,
        method: "get",
    }).then((res: any) => {
        if (res.state == 1000) {
            let list = res.data.reduce((prev: ISelectItem[], item: any) => {
                if (item.aaname !== "未录入辅助核算") {
                    prev.push({
                        value: item.aaeid + "",
                        label: item.aaname,
                    });
                }
                return prev;
            }, []);
            departmentList.value = [...departmentList.value, ...list];
        }
    });
}

const emptyText = ref("");
const tableData = ref<ITableItemEm[] | ITableItemDep[]>([]);
const loading = ref(false);
const columns = ref<Array<IColumnProps>>();
let departmentCounts: {[key: string]: number} = {}; // 用于存储部门出现的次数
function getTableData() {
    loading.value = true;
    departmentCounts = {};
    let params = {
        startPId: Number(searchInfo.periodS),
        endPId: Number(searchInfo.periodE),
        department: Number(searchInfo.department),
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    }
    let url = searchInfo.showDetail ? "/api/Salary/StatisticalTableEmployee" : "/api/Salary/StatisticalTableDepartment"
    request({
        url: url,
        method: "get",
        params,
    }).then((res: any) => {
        if (res.state === 1000) {
            columns.value = setColumns(searchInfo.showDetail, columns.value);
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
            if (tableData.value.length === 0) {
                emptyText.value = "暂无数据";
            }
            tableData.value.forEach((item, index) => {
                if (item.departmentName !== "合计") {
                    departmentCounts[item.departmentName] = (departmentCounts[item.departmentName] || 0) + 1;
                }
            })
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    }).finally(() => {
        loading.value = false;
    })
}
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableData();
});
function changeDetail() {
    if (paginationData.currentPage > 1) {
        paginationData.currentPage = 1;
    } else {
        getTableData();
    }
}
const objectSpanMethod = ({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) => {
    if (searchInfo.showDetail === true) {
        if (row.departmentName === '合计') {
            if (columnIndex === 0) {
                return [1, 2]
            } else if(columnIndex === 1) {
                return [0, 0]
            }
        } else {
            let index = tableData.value.findIndex((item: ITableItemEm | ITableItemDep) => item.departmentName === row.departmentName);
            if (columnIndex === 0) {
                if (index === rowIndex) {
                    return {
                        rowspan: departmentCounts[row.departmentName],
                        colspan: 1
                    }
                } else {
                    return {
                        rowspan: 0,
                        colspan: 0
                    }
                }
            }
        }
    } else {
        return {
            rowspan: 1,
            colspan: 1
        }
    }
}
function handleExport(val: number) {
    let params = {
        startPId: Number(searchInfo.periodS),
        endPId: Number(searchInfo.periodE),
        department: Number(searchInfo.department),
        statisticalType: searchInfo.showDetail ? 1 : 0,
    }
    if (val > 0) {
        globalExport(`/api/Salary/StatisticalExport?` + getUrlSearchParams(params));
    } else {
        globalPrint(`/api/Salary/StatisticalPrint?` + getUrlSearchParams(params));
    }
}
const initSearchInfo = () => {
    return Promise.all([getPeriodList(), getDepartmentApi()]).then((res) => { 
        periodList.value = res[0].data?.reduce((prev: IPeriodItem[], item: any) => {
            prev.push({
                value: item.key,
                label: item.text,
                time: item.key.slice(0, 4) + "" + item.key.slice(4),
            });
            return prev;
        }, []);
        let currentDateYear = dayjs().year();
        const currentDateMonth = String(dayjs().month() + 1).padStart(2, "0");
        const dateString = currentDateYear + currentDateMonth;
        let firstList = periodList.value.filter((item: IPeriodItem) => Number(item.value.slice(0, 4)) === currentDateYear);
        firstList.sort((a,b)=> {
            return Number(b.value) - Number(a.value);
        });
        searchInfo.periodS =  firstList[firstList.length-1].value;
        searchInfo.periodE = dateString; 
        saveStart = firstList[firstList.length-1].value;
        saveEnd = dateString;
        setInfos(); 
        getTableData();
    });
};
function disabledDate(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
onMounted(() => {
    initSearchInfo();
});

const rowClassName = (data: { row: any; rowIndex: number }) => {
    if (hoverRow.value && data.row.departmentId === hoverRow.value?.departmentId) {
        return "hover";
    }
};
const hoverRow = ref();
const mouseEnter = (row: any, column: any, cell: HTMLTableCellElement, event: Event) => {
    hoverRow.value = row;
};

const mouseLeave = () => {
    hoverRow.value = undefined;
};

const showDepartmentList = ref<Array<ISelectItem>>([]);
watchEffect(() => { 
    showDepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));   
});
function departFilterMethod(value: string) {
    showDepartmentList.value = commonFilterMethod(value, departmentList.value, 'label');
}

</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";

.line-item-field {
    padding-left: 0;
    box-sizing: border-box;
    :deep(.el-select) {
        width: 100%;
    }
}
body[erp] {
    .erpfirstBorder.custom-table {
        :deep(tbody) {
            tr:not(:last-child) td.detailFirst {
                border-right: 1px solid var(--table-border-color);
            }
            tr.hover {
                td.el-table__cell {
                    background-color: var(--table-hover-color);
                }
            }
        }
    }
    :deep(.el-table) {
        thead tr th div.cell {
            overflow: initial;
            &:after {
                right: -1px;
            }
        }
    }
}
</style>
