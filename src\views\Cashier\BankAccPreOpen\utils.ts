import dayjs from 'dayjs';
import { ElMessage } from "element-plus";
import { ElAlert } from "@/util/confirm";
import type { PACompanyInfoModel, PSBCCompanyInfoModel, SPDBCompanyInfoModel, CMBCompanyInfoModel } from './types'
import { ElNotify } from "@/util/notify";

export const disabledDateStart = (time: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return time.getTime() < today.getTime();
};

export const isBeforeToday = (date: Date) => {
    //日期选择面板从月份点击会默认到这个月的第一天，虽然是不可选的
    return dayjs(date).isBefore(dayjs(), 'day');
}

export const checkLegalPersonName = (legalName: string, rightlegalName: string) => {
    if (rightlegalName && rightlegalName !== "-" && legalName !== rightlegalName) {
        ElMessage({
            message: "法人姓名填写不正确，请重新输入",
            type: "error",
            offset: 80,
        });
        return false;
    }

    return true;
};

export const cancelConfirm = () => {
    return ElAlert({
        message: "为了方便您的操作，系统已经自动保存了您填写的信息，下次进入时，系统会自动带出上次填写的内容，是否继续退出预约开户页面？",
        zIndex: 9000,
    })
}

const checkPhone = (phone: string) => {
    return /^1\d{10}$/.test(phone);
}

export const checkCompanyInfo = (data: SPDBCompanyInfoModel | PACompanyInfoModel | PSBCCompanyInfoModel | CMBCompanyInfoModel) => {
    const { companyName, unifiedNumber, legalName, legalMobile } = data;
    if (companyName.trim() === "") {
        return failCheckNotify("请输入公司名称");
    }
    if (unifiedNumber.trim() === "") {
        return failCheckNotify("请输入公司统一社会信用代码");
    }
    if (legalName.trim() === "") {
        return failCheckNotify("请输入法人姓名");
    }
    if (!checkPhone(legalMobile)) {
        return failCheckNotify("请输入法人手机号");
    }

    return true;
};
//平安银行校验
const failCheckNotify = (message: string, type: any = 'warning') => {
    ElNotify({
        type,
        message,
    });
    return false;
};
export const checkPAStepThree = (data: PACompanyInfoModel) => {
    const { branchCityNo, branchNo, accountType } = data;
    if (branchCityNo === "") {
        return failCheckNotify("请选择银行网点");
    }
    if (branchNo === "") {
        return failCheckNotify("请选择二级网点");
    }
    if (accountType !== 0 && accountType !== 1) {
        return failCheckNotify("请选择正确的账户类型");
    }
    return true;
}
export const checkPAStepFour = (data: PACompanyInfoModel) => {
    const { regProvCode, regCityCode, regAddr, bizProvCode, bizCityCode, bizAddr, appointmentTime } = data;
    if (regProvCode === "" || regCityCode === "" || regAddr.trim() === "") {
        return failCheckNotify("请填写注册地址");
    }
    if (bizProvCode === "" || bizCityCode === "" || bizAddr.trim() === "") {
        return failCheckNotify("请填写经营地址");
    }
    if (appointmentTime === "" || appointmentTime === null) {
        return failCheckNotify("请选择预约时间");
    }
    return true;
};



//浦发校验

export const checkSPDBStepThree = (data: SPDBCompanyInfoModel) => {
    const { branchProvince, branchCity, branchName, accountType, openBankName, basicBankAcctNo, openAcctLcnsId, depositAcctType } = data;
    if (branchProvince === "") {
        return failCheckNotify("请选择开户省份");
    }
    if (branchCity === "") {
        return failCheckNotify("请选择开户城市");
    }
    if (branchName === "") {
        return failCheckNotify("请选择网点");
    }
    if (accountType !== 1 && accountType !== 0) {
        return failCheckNotify("请选择账户类型");
    }
    if (accountType === 1) {
        if (openBankName.trim() === "") {
            return failCheckNotify("请输入基本户开户银行");

        }
        if (basicBankAcctNo.trim() === "") {
            return failCheckNotify("请输入基本户账号");

        }
        if (openAcctLcnsId.trim() === "") {
            return failCheckNotify("请输入基本户开户许可证核准号");

        }
        if (depositAcctType === "") {
            return failCheckNotify("请选择一般存款账户类型");
        }
    }

    return true;
}
export const checkSPDBStepFour = (data: SPDBCompanyInfoModel) => {
    const { regCapital, unifiedNumberExpData, companyAddr, legalRprsntIDType, legalRprsntIDNo, legalRprsntIDExpData, clientName, defaultMobile } = data;
    if (regCapital.trim() === "") {
        return failCheckNotify("请输入注册资金");

    }
    if (unifiedNumberExpData === "" || unifiedNumberExpData === null) {
        return failCheckNotify("请选择统一社会信用代码到期日");
    }
    if (companyAddr.trim() === "") {
        return failCheckNotify("请输入办公地址");

    }
    if (legalRprsntIDType === "") {
        return failCheckNotify("请选择法人证件类型");

    }
    if (legalRprsntIDNo.trim() === "") {
        return failCheckNotify("请输入法人证件号码");

    }
    if (legalRprsntIDExpData === "" || legalRprsntIDExpData === null) {
        return failCheckNotify("请选择法人证件到期日");

    }
    if (clientName.trim() === "") {
        return failCheckNotify("请输入客户名称");

    }
    if (!checkPhone(defaultMobile)) {
        return failCheckNotify("请输入正确的预留手机号");
    }
    return true;
}

//邮储校验

export const checkPSBCStepThree = (data: PSBCCompanyInfoModel) => {
    const { openProvince, openCity, openCounty, branchNo } = data;
    if (openProvince === "") {
        return failCheckNotify("请选择开户省份");
    }
    if (openCity === "") {
        return failCheckNotify("请选择开户城市");
    }
    if (openCounty === "") {
        return failCheckNotify("请选择开户所在区县");
    }
    if (branchNo === "") {
        return failCheckNotify("请选择网点");
    }
    return true;
}
export const checkPSBCStepFour = (data: PSBCCompanyInfoModel) => {
    const { companyCertType, companyCertNo, legalCertType, legalCertNo, applicantMobile, appointmentTime } = data;
    if (companyCertType === "") {
        return failCheckNotify("请选择企业证件类型");

    }
    if (companyCertNo.trim() === "") {
        return failCheckNotify("请输入企业证件号码");
    }
    if (legalCertType === "") {
        return failCheckNotify("请选择法人证件类型");

    }
    if (legalCertNo.trim() === "") {
        return failCheckNotify("请输入法人证件号码");

    }
    if (!checkPhone(applicantMobile)) {
        return failCheckNotify("请输入正确的申请人手机号码");

    }
    if (appointmentTime === "" || appointmentTime === null) {
        return failCheckNotify("请选择预约时间");

    }
    return true;
}

