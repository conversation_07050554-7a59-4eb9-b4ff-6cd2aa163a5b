<template>
    <div>
        <div class="div-header">
            <p class="h3">帮您智能生成一份老板都看得懂的报表</p>
            <p class="h4">智能财务分析报告，助您轻松和老板汇报</p>
            <a
                v-permission="['bosspermissions-canedit']"
                class="button solid-button w120"
                style="border-radius: 5px; margin-top: 44px"
                @click="quickAuth"
            >
                立即授权
            </a>
        </div>
        <div class="div-bottom">
            <div class="box" style="margin: auto 50px">
                <div class="dv-text">
                    <p><span class="text-emp">老板看账亮点：</span></p>
                    <p><span class="image-check">财务报告自动生成</span></p>
                    <p><span class="image-check">财务数据通俗易懂</span></p>
                    <p><span class="image-check">收入费用一目了然</span></p>
                </div>
                <img class="left-img" src="@/assets/Settings/0.png" alt="老板看账" />
            </div>
            <div class="box" style="margin: auto 50px auto 0">
                <div class="dv-text">
                    <p><span class="text-emp">【老板看账】公众号</span></p>
                    <p><span class="normal-text">微信扫码关注，并推送给老板</span></p>
                    <p><span class="normal-text">轻松向老板汇报企业经营情况~ </span></p>
                </div>
                <img src="@/assets/Settings/5.png" class="right-img" alt="老板看账" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { request } from "@/util/service";

const emit = defineEmits(["quick-auth"]);

const quickAuth = () => {
    request({ url: "/api/BossPermissions/InitBossPhone?phone=", method: "post" }).then((res: any) => {
        if (res.state == 1000) emit("quick-auth");
    });
};
</script>

<style lang="less" scoped>
.w120 {
    width: 120px !important;
}
.div-header {
    height: 226px;
    margin-top: 70px;
    overflow: hidden;
    box-sizing: border-box;
    p {
        color: rgb(51, 51, 51);
        text-align: center;
        width: 100%;
        font-weight: 500;
        &.h3 {
            height: 30px;
            margin: 24px 0;
            font-size: 24px;
            line-height: 30px;
        }
        &.h4 {
            height: 22px;
            font-size: 18px;
            line-height: 22px;
            margin: 0;
        }
    }
}
.div-bottom {
    height: 214px;
    margin-bottom: 50px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    div.box {
        display: flex;
        width: 425px;
        height: 160px;
        background-color: #fafafa;
        &:first-child {
            img {
                width: 166px;
                height: 138px;
                margin-top: 22px;
            }
        }
        &:last-child {
            img {
                width: 120px;
                height: 120px;
                margin-top: 20px;
            }
        }
        .dv-text {
            margin: 12px 50px;
            margin-bottom: 0;
            p {
                color: #999999;
                font-size: 14px;
                margin: 14px 0;
                text-align: start;
                span.text-emp {
                    color: #333333;
                    font-size: 16px;
                    font-weight: 600;
                }
                span.image-check {
                    background: url("@/assets/Settings/boss-checkbox.png") no-repeat left center;
                    background-size: 16px 16px;
                    line-height: 18px;
                    padding-left: 20px;
                    font-size: 14px;
                }
                span.normal-text {
                    line-height: 20px;
                }
            }
        }
    }
}
</style>
