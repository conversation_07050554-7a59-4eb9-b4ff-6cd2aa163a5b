import { TextAlignmentType, VoucherPrintDataType } from "./types";
import type { IModuleData, IStyle, ITableList, ModuleType } from "./types";
//可转为utils/common.ts
export function getKeyByValue(value: number): string {
    const enumEntries = Object.entries(VoucherPrintDataType);
    const foundEntry = enumEntries.find(([key, val]) => val === value);
    return foundEntry![0];
}

export function transformTableData(data: IModuleData[]) {
    data = data
        .filter((i) => i.type === "tableHeader" || i.type === "tableBody" || i.type === "tableTotal")
        .sort((a, b) => a.index - b.index);
    const tableListIds = new Set(data.map((i) => i.id));
    const list: any[] = [];
    tableListIds.forEach((id) => {
        const header = data.find((i) => i.id === id && i.type === "tableHeader")!;
        const body = data.find((i) => i.id === id && i.type === "tableBody")!;
        const footer = data.find((i) => i.id === id && i.type === "tableTotal") || { height: 0, style: {} };
        if (!header || !body) return;
        const { label, text, width, height: headerHeight, style } = header;
        const { dataSource, height: bodyHeight } = body;
        const { height: footerHeight } = footer;
        list.push({
            id,
            label,
            text,
            dataSource,
            width,
            headerHeight,
            bodyHeight,
            footerHeight,
            inputable: false,
            headerStyle: style,
            bodyStyle: body.style,
            footerStyle: footer?.style ?? {},
            isSelected: false,
            index: header.index,
        });
    });
    return list;
}

export function splitTableObject(obj: ITableList, index: number) {
    const createTablePart = (type: ModuleType, label: string, text: string, dataSource: string, height: number, style: IStyle) => ({
        id: obj.id,
        label,
        text,
        dataSource,
        width: obj.width,
        height: height,
        positionType: 3,
        dataType: VoucherPrintDataType[type],
        active: false,
        left: 0,
        top: 0,
        index: obj.index,
        fontFamily: style.fontFamily,
        fontSize: style.shadowWhiteSpace === "Reduce" ? style.shadowFontSize : style.fontSize,
        isBold: style.fontWeight === "bold",
        isItalic: style.fontStyle === "italic",
        textAlign: TextAlignmentType[style.textAlign],
        justifyContent: 0,
        overflow: judgeOverFlow(style.whiteSpace, style.shadowWhiteSpace),
        isUnderLine: style.textDecoration === "underline",
        style: { ...style },
    });
    const tableHeader = createTablePart("tableHeader", obj.label, obj.text, obj.dataSource, obj.headerHeight, obj.headerStyle);
    const tableBody = createTablePart("tableBody", obj.label, obj.text, obj.dataSource, obj.bodyHeight, obj.bodyStyle);
    let tableTotal;
    if (index === 0 && obj.id !== 12 && obj.id !== 13) {
        tableTotal = createTablePart("tableTotal", "合计：", "", "#TOTAL", obj.footerHeight, obj.footerStyle);
    } else if (obj.id === 12) {
        tableTotal = createTablePart("tableTotal", "", "", "#TOTAL_DEBIT", obj.footerHeight, obj.footerStyle);
    } else if (obj.id === 13) {
        tableTotal = createTablePart("tableTotal", "", "", "#TOTAL_CREDIT", obj.footerHeight, obj.footerStyle);
    }

    return tableTotal ? [tableHeader, tableBody, tableTotal] : [tableHeader, tableBody];
}

export const judgeOverFlow = (overFlow: string, shadowOverflow: string) => {
    if (shadowOverflow) {
        overFlow = shadowOverflow;
    }
    let overFlowType = 0;
    switch (overFlow) {
        case "Wrap":
            overFlowType = 0;
            break;
        case "Truncation":
            overFlowType = 1;
            break;
        case "Reduce":
            overFlowType = 2;
            break;
    }
    return overFlowType;
};

export const moduleList = [
    {
        moudleType: "Head",
        moudleName: "表头信息（上）",
        order: 2,
        seniorFieldList: [
            {
                fieldName: "凭证字打印标题",
                checked: false,
                fieldType: "Text",
                fieldLabel: "凭证字",
                dataSource: "#VoucherTitle",
                order: 0,
            },
            {
                fieldName: "账套名称",
                checked: false,
                fieldType: "Text",
                fieldLabel: "单位",
                dataSource: "#ASName",
                order: 1,
            },
            {
                fieldName: "凭证日期",
                checked: false,
                fieldType: "Text",
                fieldLabel: "日期",
                dataSource: "#VoucherDate",
                order: 2,
            },
            {
                fieldName: "凭证号",
                checked: false,
                fieldType: "Text",
                fieldLabel: "凭证号",
                dataSource: "#VoucherNum",
                order: 3,
            },
            {
                fieldName: "附单据数",
                checked: false,
                fieldType: "Text",
                fieldLabel: "附单据数",
                dataSource: "#FileNum",
                order: 4,
            },
        ],
    },
    {
        moudleType: "Body",
        moudleName: "表体信息（中）",
        order: 3,
        seniorFieldList: [
            {
                fieldName: "序号",
                checked: false,
                fieldType: "Text",
                fieldLabel: "序号",
                dataSource: "#INDEX",
                order: 5,
            },
            {
                fieldName: "摘要",
                checked: false,
                fieldType: "Text",
                fieldLabel: "摘要",
                dataSource: "#DESCRIPTION",
                order: 6,
            },
            {
                fieldName: "科目编码",
                checked: false,
                fieldType: "Text",
                fieldLabel: "科目编码",
                dataSource: "#ASUB_CODE",
                order: 7,
            },
            {
                fieldName: "科目名称",
                checked: false,
                fieldType: "Text",
                fieldLabel: "科目名称",
                dataSource: "#ASUB_NAME",
                order: 8,
            },
            {
                fieldName: "辅助核算",
                checked: false,
                fieldType: "Text",
                fieldLabel: "辅助核算",
                dataSource: "#AA_NAME",
                order: 9,
            },
            {
                fieldName: "数量核算",
                checked: false,
                fieldType: "Text",
                fieldLabel: "数量核算",
                dataSource: "#AA_QUANTITY",
                order: 10,
            },
            {
                fieldName: "外币核算",
                checked: false,
                fieldType: "Text",
                fieldLabel: "外币核算",
                dataSource: "#AA_FC",
                order: 11,
            },
            {
                fieldName: "借方金额",
                checked: false,
                fieldType: "Text",
                fieldLabel: "借方金额",
                dataSource: "#DEBIT",
                order: 12,
            },
            {
                fieldName: "贷方金额",
                checked: false,
                fieldType: "Text",
                fieldLabel: "贷方金额",
                dataSource: "#CREDIT",
                order: 13,
            },
        ],
    },
    {
        moudleType: "Foot",
        moudleName: "表尾信息（下）",
        order: 4,
        seniorFieldList: [
            {
                fieldName: "制单人",
                checked: false,
                fieldType: "Text",
                fieldLabel: "制单",
                dataSource: "#UserName",
                order: 14,
            },
            {
                fieldName: "审核人",
                checked: false,
                fieldType: "Text",
                fieldLabel: "审核",
                dataSource: "#ApprovedName",
                order: 15,
            },
            {
                fieldName: "备注",
                checked: false,
                fieldType: "Text",
                fieldLabel: "备注",
                dataSource: "#REMARK",
                order: 16,
            },
        ],
    },
];

export const pageSizeOptions = [
    {
        label: "发票版（14*24cm）",
        value: 4,
        width: 397,
        height: 680,
        marginTop: 14,
        marginBottom: 14,
        marginLeft: 25,
        marginRight: 25,
    },
    {
        label: "A4整版",
        value: 6,
        width: 595,
        height: 842,
        marginTop: 14,
        marginBottom: 14,
        marginLeft: 20,
        marginRight: 20,
    },
    {
        label: "A4两版（推荐）",
        value: 1,
        width: 595,
        height: 421,
        marginTop: 14,
        marginBottom: 14,
        marginLeft: 20,
        marginRight: 20,
    },
    {
        label: "A4三版",
        value: 2,
        width: 595,
        height: 281,
        marginTop: 14,
        marginBottom: 14,
        marginLeft: 20,
        marginRight: 20,
    },
    {
        label: "A4宽 12*21cm",
        value: 3,
        width: 340,
        height: 595,
        marginTop: 14,
        marginBottom: 14,
        marginLeft: 20,
        marginRight: 20,
    },
    {
        label: "A5",
        value: 7,
        width: 420,
        height: 595,
        marginTop: 10,
        marginBottom: 10,
        marginLeft: 14,
        marginRight: 14,
    },
    {
        label: "B5",
        value: 8,
        width: 498,
        height: 708,
        marginTop: 10,
        marginBottom: 10,
        marginLeft: 14,
        marginRight: 14,
    },
];

export const zoomList = [
    { label: "80%", value: 0.8 },
    { label: "100%", value: 1 },
    { label: "130%", value: 1.3 },
    { label: "150%", value: 1.5 },
    { label: "200%", value: 2 },
    { label: "300%", value: 3 },
];

export const fontSizeList = [
    { label: 8, value: 8 },
    { label: 9, value: 9 },
    { label: 10, value: 10 },
    { label: 11, value: 11 },
    { label: 12, value: 12 },
    { label: 13, value: 13 },
    { label: 14, value: 14 },
    { label: 15, value: 15 },
    { label: 16, value: 16 },
    { label: 17, value: 17 },
    { label: 18, value: 18 },
    { label: 19, value: 19 },
    { label: 20, value: 20 },
    { label: 21, value: 21 },
    { label: 22, value: 22 },
    { label: 23, value: 23 },
    { label: 24, value: 24 },
    { label: 25, value: 25 },
    { label: 26, value: 26 },
    { label: 28, value: 28 },
    { label: 36, value: 36 },
    { label: 42, value: 42 },
]

export const typeTransform = (type: string) => {
    let label = "";
    switch (type) {
        case "text":
            label = "文本框";
            break;
        case "img":
            label = "图片";
            break;
        case "rectangle":
            label = "矩形";
            break;
        case "line":
            label = "线条";
            break;
    }
    return label;
};

// 判断一段文本，用指定的字体大小，在指定的 一块固定大小的div里能不能显示的下
export const canTextFitInDiv = (text: string, fontSize: number, divWidth: number, divHeight: number) => {
    // 创建一个临时的span元素用于测量文本
    const tempSpan = document.createElement("span");
    tempSpan.style.width = `${divWidth}px`;
    tempSpan.style.position = "absolute";
    tempSpan.style.visibility = "hidden";
    tempSpan.style.whiteSpace = "warp"; // 换行
    tempSpan.style.fontSize = `${fontSize}px`;
    tempSpan.innerText = text;
    document.body.appendChild(tempSpan);

    // 获取文本的宽度
    const textHeight = tempSpan.offsetHeight;

    // 判断文本是否能在容器内显示
    const canFit = textHeight <= divHeight;

    // 移除临时的span元素
    document.body.removeChild(tempSpan);

    return canFit;
}
