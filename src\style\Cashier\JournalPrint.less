@import "../Functions.less";
ul,
li {
  list-style: none;
  padding: 0;
  margin: 0;
}
.button-content {
  display: flex;
  .dropdown-button {
    z-index: 103;
  }
  .item {
    position: relative;
    z-index: 103;
    &:hover {
      ul {
        display: block;
      }
    }
  }
  ul {
    z-index: 10;
    color: var(--font-color);
    background-color: var(--white);
    box-shadow: 0 0 4px var(--button-border-color);
    border-radius: 2px;
    position: absolute;
    left: 1px;
    display: none;
    li {
      height: 27px;
      line-height: 27px;
      cursor: pointer;
      font-size: 13px;
      text-align: left;
      padding: 0 12px;
      white-space: nowrap;
      &:hover {
        background-color: var(--main-color);
        color: var(--white);
      }
    }
  }
  .downlist {
    z-index: 10;
    color: var(--font-color);
    background-color: var(--white);
    box-shadow: 0 0 4px var(--button-border-color);
    border-radius: 2px;
    position: absolute;
    li {
      height: 27px;
      line-height: 27px;
      cursor: pointer;
      font-size: 13px;
      text-align: left;
      padding: 0 12px;
      white-space: nowrap;
      &:hover {
        background-color: var(--main-color);
        color: var(--white);
      }
    }
  }
  .down-click {
    width: 19px;
    margin-left: 1px;
    background: url("@/assets/Icons/down-white.png") no-repeat center;
    background-color: var(--main-color);
    & + ul {
      width: 106px !important;
    }
    &:hover {
      border-color: var(--light-main-color);
      background-color: var(--light-main-color);
    }
  }
  &.erp {
    :deep(.dropdown-button) {
      > .print {
        width: 84px;
      }
      > .import {
        width: 89px;
        &.depositjournal {
          width: 115px;
        }
      }
      & > .export {
        width: 128px;
      }
      & > .operate {
        width: 102px;
      }
    }
  }
}
.print-item {
  display: flex;
  align-items: center;
  padding-top: 8px;

  .print-item-label {
    line-height: 32px;
    width: 100px;
    padding-top: 0;
    align-self: flex-start;
    /* 使该元素在纵向上顶部对齐 */
    text-align: right;
    margin-right: 10px;
  }
  .tip {
    padding-left: 20px;
    line-height: 16px;
    background: url("@/assets/Icons/warn.png") no-repeat left center;
  }
  .print-item-field{
    :deep(.el-select){
        width: 200px;
    }
  }
}

