<template>
    <el-dialog v-model="printDialogShow" title="凭证打印" center width="560px" top="5vh" class="custom-confirm dialogDrag">
        <div class="print-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="print-main">
                <div class="print-main-title">
                    <div id="printSwitch" class="switch-btns">
                        <div
                            :class="settingType === 'base' ? 'selected switch-btn left-btn' : 'switch-btn left-btn'"
                            @click="() => (settingType = 'base')"
                        >
                            基础设置
                        </div>
                        <div
                            :class="settingType === 'advanced' ? 'selected switch-btn right-btn' : 'switch-btn right-btn'"
                            @click="() => (settingType = 'advanced')"
                        >
                            高级设置
                        </div>
                    </div>
                    <div class="help-tip">
                        <span class="help-icon" v-show="!isErp && !isHideBarcode"></span>
                        <a
                            @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=130150130')"
                            v-show="!isErp && !isHideBarcode"
                            class="link"
                        >
                            帮助
                        </a>
                    </div>
                </div>
                <div class="switch-content print-switch-settings print-base-settings" v-show="settingType === 'base'">
                    <div class="line-item height28" v-show="printVoucherCountTypeShow">
                        <div class="line-item-title height28">打印选项：</div>
                        <div class="line-item-field height28">
                            <el-radio-group v-model="PrintVoucherCountType">
                                <el-radio label="PrintAll">打印全部凭证</el-radio>
                                <el-radio label="PrintSelect">打印选中凭证</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="line-item height28">
                        <div class="line-item-title">选择打印纸张：</div>
                        <div class="line-item-field input-size1">
                            <el-select v-model="printInfo.printType" :teleported="false">
                                <el-option :value="4" label="发票版 14*24cm（推荐）" />
                                <el-option :value="1" label="A4两版（推荐）" />
                                <el-option :value="2" label="A4三版" />
                                <el-option :value="3" label="A4宽 12*21cm" />
                                <el-option :value="7" label="A5" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item height18" style="margin-top: 10px" v-show="printLoacationShow">
                        <div class="line-item-title"></div>
                        <div class="line-item-field">
                            <el-radio-group v-model="printInfo.printLoacation" @change="changePrintLoacation">
                                <el-radio label="Center">居中</el-radio>
                                <el-radio label="Top">靠上</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="line-item height28">
                        <div class="line-item-title">打印版凭证行：</div>
                        <div class="line-item-field input-size2">
                            <el-select v-model="printInfo.voucherLineCount" :teleported="false" :disabled="voucherLineCountDisabled">
                                <el-option :value="5" label="5" />
                                <el-option :value="6" label="6" />
                                <el-option :value="7" label="7" />
                                <el-option :value="8" label="8" />
                            </el-select>
                            <span style="margin-left: 8px">行</span>
                        </div>
                    </div>
                    <div class="line-item height28">
                        <div class="line-item-title">打印字体：</div>
                        <div class="line-item-field input-size2">
                            <el-select v-model="printInfo.font" :teleported="false">
                                <el-option :value="0" label="宋体" />
                                <el-option :value="1" label="黑体" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item height28">
                        <div class="line-item-title">打印字体大小：</div>
                        <div class="line-item-field input-size2">
                            <el-select v-model="printInfo.voucherFontSize" :teleported="false">
                                <el-option :value="8" label="8" />
                                <el-option :value="9" label="9" />
                                <el-option :value="10" label="10" />
                                <el-option :value="11" label="11" />
                                <el-option :value="12" label="12" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item height28">
                        <div class="line-item-title height28">打印方向：</div>
                        <div class="line-item-field">
                            <el-radio-group v-model="printInfo.direction" :disabled="directionDisabled">
                                <el-radio label="Z">纵向</el-radio>
                                <el-radio label="H">横向</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="line-item height28">
                        <div class="line-item-title">页面边距：</div>
                        <div class="line-item-field span-content">
                            <span>左</span>
                            <input type="text" v-model="printInfo.marginLeft" @keyup="handleMarginLeft(printInfo.marginLeft)" />
                            <span class="mr-20">毫米</span>
                            <span class="ml-20">右</span>
                            <input type="text" v-model="printInfo.marginRight" readonly disabled />
                            <span>毫米</span>
                        </div>
                    </div>
                    <div class="line-item height28" style="margin-top: 12px">
                        <div class="line-item-title"></div>
                        <div class="line-item-field span-content">
                            <span>上</span>
                            <input type="text" v-model="printInfo.marginTop" @keyup="handleMarginTop(printInfo.marginTop)" />
                            <span class="mr-20">毫米</span>
                            <span class="ml-20">下</span>
                            <input type="text" v-model="printInfo.marginBottom" readonly disabled />
                            <span>毫米</span>
                        </div>
                    </div>
                </div>
                <div class="switch-content print-switch-settings print-advanced-settings" v-show="settingType === 'advanced'">
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isPrintUserName" label="打印制单人姓名" />
                            <el-checkbox v-model="printInfo.isPrintApprovedName" label="打印审核人姓名" />
                            <el-checkbox v-model="printInfo.isPrintFrontCover" label="打印封面" />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field input-size3">
                            <el-checkbox label="打印主管姓名" v-model="printInfo.isPrintDirectorName" />
                            <input
                                v-show="printInfo.isPrintDirectorName"
                                type="text"
                                v-model="printInfo.directorName"
                                style="margin: -5px 0 -5px 16px"
                            />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field input-size3">
                            <el-checkbox v-model="printInfo.isPrintMakerName" label="打印记账姓名" />
                            <input
                                v-show="printInfo.isPrintMakerName"
                                type="text"
                                v-model="printInfo.makerName"
                                style="margin: -5px 0 -5px 16px"
                            />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field input-size3">
                            <el-checkbox label="打印出纳姓名" v-model="printInfo.isPrintCashierName" />
                            <input
                                v-show="printInfo.isPrintCashierName"
                                type="text"
                                v-model="printInfo.cashierName"
                                style="margin: -5px 0 -5px 16px"
                            />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isShowOriginalCoinAndRate" label="在摘要中打印显示原币、汇率" />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isShowQuantityAndUnitPrice" label="在摘要中打印显示数量、单价" />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isShowAsubCode" label="打印显示科目编码" />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isPrintFileNum" label="无附件时打印“0”" />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isShowSummaryPrint" label="汇总打印" />
                        </div>
                    </div>
                    <div class="line-item height18" v-show="printInfo.isShowSummaryPrint" style="margin-top: 10px; padding-left: 22px">
                        <div class="line-item-field input-size4">
                            <span style="margin-right: 8px">将科目汇总到</span>
                            <el-select v-model="printInfo.summaryAsubLevel" :teleported="false">
                                <el-option :value="1" label="1" />
                                <el-option :value="2" label="2" />
                                <el-option :value="3" label="3" />
                                <el-option :value="4" label="4" />
                            </el-select>
                            <span style="margin-left: 8px">级科目</span>
                            
                        </div>
                    </div>
                    <div
                        class="line-item height18 input-size4"
                        v-show="printInfo.isShowSummaryPrint"
                        style="margin-top: 10px; padding-left: 22px"
                    >
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.isSummaryByDirection" label="按借贷方向分别汇总" />
                            <el-checkbox v-model="printInfo.isHideZero" label="汇总金额为零不显示" />
                            <el-checkbox v-model="printInfo.isShowAssitItem" label="显示核算项目" />
                        </div>
                    </div>
                    <div class="line-item height18 margintop16">
                            <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start" :teleported="false">
                                <template #content>
                                    <div>
                                     仅支持打印jpg、png、pdf格式文件，其他文件将跳过
                                   </div>
                                 </template>
                                 <template #default>
                                    <div class="line-item-field">
                                    <el-checkbox  style="margin-right: 4px; " v-model="printInfo.printFile" label="打印附件" />
                                    <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px; " />
                                    </div>
                                </template>
                            </el-tooltip>
                    </div>
                    <div
                        class="line-item height18 input-size4"
                        v-show="printInfo.printFile"
                        style="margin-top: 10px; padding-left: 22px"
                    >
                        <div class="line-item-field">
                            <el-checkbox v-model="printInfo.simultaneouslyPrintFileList" label="同时打印附件清单" /><br>
                            <el-checkbox v-model="printInfo.continuousFiles" label="附件连续打印" />
                        </div>
                    </div>
                </div>
                <div class="txt">
                    <span class="highlight-red">提示：</span>
                    为了保证您的正常打印，请先下载安装
                    <a class="link" @click="globalWindowOpen('https://get.adobe.com/cn/reader/')"> Adobe PDF阅读器 </a>
                </div>
            </div>
            <div class="buttons">
                <a @click="() => (printDialogShow = false)" class="button ml-10">取消</a>
                <a @click="confirmPrint" class="button solid-button ml-10">打印</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { ElNotify } from "@/util/notify";
import { getVoucherPrintInfo, setVoucherPrintInfo } from "@/components/Voucher/utils";
import { globalWindowOpen } from "@/util/url";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

const props = defineProps({
    printDialogShow: {
        type: Boolean,
        default: false,
    },
    printVoucherCountTypeShow: {
        type: Boolean,
        default: true,
    },
});
const emit = defineEmits(["confirmPrint", "update:printDialogShow"]);
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const PrintVoucherCountType = ref("PrintAll");
const printDialogShow = computed({
    get: () => props.printDialogShow,
    set: (val) => emit("update:printDialogShow", val),
});
const printVoucherCountTypeShow = computed(() => props.printVoucherCountTypeShow);
const settingType = ref("base");
let printInfo = reactive(getVoucherPrintInfo());
const confirmPrint = () => {
    if (!canPrint()) return;
    setVoucherPrintInfo(printInfo);
    const { printFile, simultaneouslyPrintFileList, continuousFiles, ...printParams } = printInfo;
    setTimeout(() => {
        // emit("confirmPrint", printInfo);
        emit("confirmPrint", {...printParams,isIncludeFile: (printFile ? 1 : 0) + '_' + (simultaneouslyPrintFileList ? 1 : 0) + '_' + (continuousFiles ? 1 : 0) });
    }, 200);
};
const directionDisabled = ref(true);
const printLoacationShow = ref(false);
const voucherLineCountDisabled = ref(false);
const canPrint = () => {
    if (!printInfo.marginTop && printInfo.marginTop !== 0) {
        ElNotify({ type: "warning", message: "请输入上边距后进行打印" });
        return false;
    }
    if (isNaN(Number(printInfo.marginTop))) {
        ElNotify({ type: "warning", message: "上边距只能输入数字" });
        return false;
    }
    if (!printInfo.marginLeft && printInfo.marginLeft !== 0) {
        ElNotify({ type: "warning", message: "请输入左边距后进行打印" });
        return false;
    }
    if (isNaN(Number(printInfo.marginLeft))) {
        ElNotify({ type: "warning", message: "左边距只能输入数字" });
        return false;
    }
    return true;
};

const changePrintLoacation = () => {
    if (printInfo.printLoacation === "Center") {
        printInfo.marginTop = 14;
        printInfo.marginBottom = 15;
    } else {
        printInfo.marginTop = 9;
    }
};

// watch(
//     () => printInfo.isShowSummaryPrint,
//     (type) => {
//         if (!type) {
//             printInfo.summaryAsubLevel = 1;
//             printInfo.isSummaryByDirection = true;
//             printInfo.isHideZero = true;
//             printInfo.isShowAssitItem = true;
//         }
//     }
// );

watch(
    [() => printInfo.isPrintDirectorName, () => printInfo.isPrintMakerName, () => printInfo.isPrintCashierName],
    ([type1, type2, type3]) => {
        if (type1) printInfo.directorName = "";
        if (type2) printInfo.makerName = "";
        if (type3) printInfo.cashierName = "";
    }
);

watch(
    () => printInfo.printType,
    (val) => {
        if ([4, 3, 7].findIndex((item) => item === val) > -1) {
            printInfo.direction = "H";
            if (val === 4) {
                printInfo.marginTop = 9;
                printInfo.marginBottom = 12;
            } else if (val === 3) {
                printInfo.marginTop = 9;
                printInfo.marginBottom = 9;
            } else if (val === 7) {
                printInfo.marginTop = 14;
                printInfo.marginBottom = 15;
            }
            directionDisabled.value = false;
        } else {
            printInfo.direction = "Z";
            if (val === 2) {
                printInfo.marginTop = 5;
                printInfo.marginBottom = 8;
            } else if (val === 1) {
                printInfo.marginTop = 14;
                printInfo.marginBottom = 15;
                changePrintLoacation();
            }
            directionDisabled.value = true;
        }
        if (val === 1) {
            printLoacationShow.value = true;
        } else {
            printLoacationShow.value = false;
        }
        if (val === 2) {
            printInfo.voucherLineCount = 5;
            voucherLineCountDisabled.value = true;
        } else {
            voucherLineCountDisabled.value = false;
        }
    },
    {
        immediate: true,
    }
);

const handleMarginLeft = (val: any) => {
    if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0) {
        printInfo.marginLeft = 25;
        ElNotify({ type: "warning", message: "请输入整数边距~" });
    } else if (val + "" === "") {
        printInfo.marginRight = 33;
        return;
    } else {
        const marginLeft = Number(val);
        if (marginLeft > 35) {
            ElNotify({ type: "warning", message: "最高可设置35边距" });
            printInfo.marginLeft = 25;
        }
    }
    printInfo.marginRight = 35 - printInfo.marginLeft;
};

const handleMarginTop = (val: any) => {
    let maxHeight = 0;
    switch (printInfo.printType) {
        case 4:
            maxHeight = 21;
            break;
        case 1:
            maxHeight = printInfo.printLoacation === "Top" ? 18 : 29;
            break;
        case 2:
            maxHeight = 13;
            break;
        case 3:
            maxHeight = 18;
            break;
        case 7:
            maxHeight = 29;
            break;
    }
    if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0) {
        printInfo.marginTop = 9;
        ElNotify({ type: "warning", message: "请输入整数边距~" });
    } else if (val + "" === "") {
        return;
    } else {
        const marginTop = Number(val);
        if (marginTop > maxHeight) {
            ElNotify({ type: "warning", message: "最高可设置" + maxHeight + "边距" });
            switch (printInfo.printType) {
                case 4:
                    printInfo.marginTop = 9;
                    break;
                case 1:
                    printInfo.marginTop = 14;
                    break;
                case 2:
                    printInfo.marginTop = 5;
                    break;
                case 3:
                    printInfo.marginTop = 9;
                    break;
                case 7:
                    printInfo.marginTop = 14;
                    break;
            }
        }
    }
    printInfo.marginBottom = maxHeight - printInfo.marginTop;
};

const getPrintType = () => {
    return PrintVoucherCountType.value;
};
const getPrintInfo = () => {
    refreshPrintInfo();
    return printInfo;
};
const refreshPrintInfo = () => {
    printInfo = reactive(getVoucherPrintInfo());
};
defineExpose({ getPrintType, getPrintInfo });
</script>

<style lang="less" scoped>
@import "@/style/Voucher/VoucherList.less";

.print-content {
    .print-main-title {
        .help-tip {
            font-size: 0;
            margin-top: 5px;
            float: right;
            display: flex;
            align-items: center;

            .help-icon {
                display: inline-block;
                height: 16px;
                width: 16px;
                background: url("@/assets/Icons/help.png") no-repeat;
                vertical-align: top;
                margin-top: 1px;
                margin-right: 4px;
            }
        }
    }

    .print-main {
        .switch-content {
            display: flex;
            flex-direction: column;
            height: 460px;

            .line-item {
                margin-top: 24px;
                font-size: 12px;
                display: flex;
                flex-direction: row;
            }
        }
        .txt {
            margin-top: 16px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }

    .buttons {
        border-top: 1px solid var(--border-color);
    }

    &.erp {
        .buttons {
            display: flex !important;
            flex-direction: row;
            justify-content: flex-end;
        }
    }
}
</style>
