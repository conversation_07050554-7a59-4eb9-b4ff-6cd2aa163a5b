export class MenuFunction {
    functionId = 0;
    parentId = 0;
    functionNo = 0;
    functionCode = "";
    fullFunctionCode = "";
    functionName = "";
    level = 0;
    order = 0;
    isIndeterminate = false;
    children: MenuFunction[] = [];
}

export function processMenuFunctions(menuFunctions: MenuFunction[]): MenuFunction[] {
    const processedMenuFunctions: MenuFunction[] = [];
    for (const menuFunction of menuFunctions) {
        const processedMenuFunction: any = new MenuFunction();
        for (const key in menuFunction) {
            if (key === "children") {
                const processedChildren = processMenuFunctions(menuFunction.children);
                processedMenuFunction.children = processedChildren;
            } else {
                processedMenuFunction[key as keyof MenuFunction] = menuFunction[key as keyof MenuFunction] as any;
            }
        }
        processedMenuFunctions.push(processedMenuFunction);
    }
    return processedMenuFunctions;
}

export function swapMenuFunctions(menuFunctions: MenuFunction[]): MenuFunction[] {
    const processedMenuFunctions: MenuFunction[] = [];
    let function325: MenuFunction | undefined;
    let function327: MenuFunction | undefined;
    let function349: MenuFunction | undefined;

    for (const menuFunction of menuFunctions) {
        if (menuFunction.functionId === 325) {
            function325 = menuFunction;
        } else if (menuFunction.functionId === 327) {
            function327 = menuFunction;
        } else if (menuFunction.functionId === 349) {
            function349 = menuFunction;
        } else {
            processedMenuFunctions.push(menuFunction);
        }
    }

    if (function325 && function349) {
        const index325 = processedMenuFunctions.findIndex((menuFunction) => menuFunction === function325);
        const index349 = processedMenuFunctions.findIndex((menuFunction) => menuFunction === function349);
        if (index325 !== -1 && index349 !== -1) {
            processedMenuFunctions[index325] = function349;
            processedMenuFunctions[index349] = function325;
        }
    }

    if (function327 && function349) {
        const index327 = processedMenuFunctions.findIndex((menuFunction) => menuFunction === function327);
        const index349 = processedMenuFunctions.findIndex((menuFunction) => menuFunction === function349);
        if (index327 !== -1 && index349 !== -1) {
            processedMenuFunctions[index327] = function349;
            processedMenuFunctions[index349] = function327;
        }
    }

    return processedMenuFunctions;
}

export function findFunctionById(data: MenuFunction[], functionId: number): MenuFunction | null {
    for (const item of data) {
      if (item.functionId === functionId) {
        return item;
      }
      if (item.children.length > 0) {
        const foundItem = findFunctionById(item.children, functionId);
        if (foundItem !== null) {
          return foundItem;
        }
      }
    }
    return null;
  }
