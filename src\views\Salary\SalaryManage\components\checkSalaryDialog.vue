<template>
    <div style="overflow-y: auto;">
        <div id="checkSalesDiloag" class="check-sales-diloag" style="width: 1100px; margin: 0 auto">
            <div class="check-voucher-head">
                <div class="check-voucher-title" style="width: 226px; text-align: left">摘要</div>
                <div class="check-voucher-title" style="width: 446px; text-align: left">科目</div>
                <div class="check-voucher-title" style="width: 154px; text-align: right">借方金额</div>
                <div class="check-voucher-title" style="width: 154px; text-align: right">贷方金额</div>
            </div>
            <div id="checkVoucherList" class="check-voucher-list">
                <div id="v-i-1" class="voucher-list-item">
                    <div class="voucher-list-head">
                        <span class="voucher-date float-l" id="voucherdate">
                            日期：{{ checkSalaryVoucher[0]?.v_date.replace(/\//g, "-") }}
                        </span>
                        <span class="voucher-num float-l ml-20" id="vgname">
                            凭证字号: {{ checkSalaryVoucher[0]?.vg_name + checkSalaryVoucher[0]?.v_num }}
                        </span>
                        <template v-if="ShowVoucherCheckout">
                            <a class="link hover-display float-r ml-20" @click="ShowVoucherFromList(checkSalaryVtId, checkSalaryVtName)">
                                查看
                            </a>
                        </template>
                        <template v-else>
                            <a class="link hover-display float-r ml-20" v-permission="['voucher-candelete']" @click="deleteVoucherFromList(pid, vid)"> 删除 </a>
                            <a class="link hover-display float-r ml-20" @click="updateVoucherFromList(checkSalaryVtId, checkSalaryVtName)">
                                修改
                            </a>
                        </template>
                    </div>
                    <div class="voucher-list-lines">
                        <div class="voucher-list-line" v-for="(item, index) in checkSalaryVoucher" :key="index">
                            <ToolTip :content=item.description placement="right" effect="light" :dynamicWidth="true">
                                <div class="voucher-list-description" style="width: 226px">
                                {{ item.description }}
                                </div>
                            </ToolTip>
                            <div class="voucher-list-asubName" style="width: 446px">
                                {{ item.asub_name }}
                            </div>
                            <div class="voucher-list-debit" style="width: 154px; text-align: right">
                                {{ formatMoney(item.debit) }}
                            </div>
                            <div class="voucher-list-credit" style="width: 154px; text-align: right">
                                {{ formatMoney(item.credit) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="check-box-tool-bar">
                <a class="button solid-button" @click="backTo">返回</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { request } from "@/util/service";
import { formatMoney } from "@/util/format";
import ToolTip from "@/components/Tooltip/index.vue";
import type { ICheckSalaryVoucherItem } from "../types";
import { ElNotify } from "@/util/notify";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
const props = defineProps<{
    pid: string;
    vid: string;
    checkSalaryVtId: string;
    checkSalaryVtName: string;
    checkSalaryVoucher: ICheckSalaryVoucherItem[];
    ShowVoucherCheckout: boolean;
}>();
const emits = defineEmits(["backTo", "updateVoucherFromList"]);

function ShowVoucherFromList(vtid: string, vname: string) {
    emits("updateVoucherFromList", vtid, vname, true);
}

function deleteVoucherFromList(pid: string, vid: string) {
    request({
        url: `/api/SalaryVoucher/DeleteVoucher?pid=${pid}&vid=${vid}`,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data) {
                ElNotify({
                    type: "success",
                    message: "删除成功！",
                });
                // ChangeTabName("计提工资，发放工资");
                dispatchReloadAsubAmountEvent();
                backTo();
                // global_deletedVoucher(r.data.deleteParams);
            } else {
                ElNotify({
                    type: "error",
                    message: "删除失败，请稍后重试！",
                });
            }
        } else {
            ElNotify({
                type: "error",
                message: "删除失败，请稍后重试！",
            });
        }
    });
}
function updateVoucherFromList(vtid: string, vname: string) {
    emits("updateVoucherFromList", vtid, vname, true);
}
function backTo() {
    emits("backTo");
}
</script>

<style lang="less" scoped>
.check-sales-diloag {
    .check-voucher-head {
        display: flex;
        justify-content: space-between;
        background-color: #f0f7f0;
        width: 100%;
        height: 36px;
        padding: 0 20px;
        box-sizing: border-box;
        font-size: 12px;
        font-weight: bold;
        line-height: 36px;
        margin: 20px auto 0;
        text-align: left;
        border: 1px solid #E5E5E5;
        border-bottom: 0;
    }
    .check-voucher-list {
        font-size: 12px;
        border: 1px solid #E5E5E5;
        &:hover {
            border: 1px solid #44b449;
            .hover-display {
                display: inline-block !important;
            }
        }
        .voucher-list-item {
            .voucher-list-head {
                width: 100%;
                height: 40px;
                line-height: 40px;
                padding: 0 20px;
                box-sizing: border-box;
                border-bottom: 1px solid #E5E5E5;
                font-size: 12px;
                .voucher-date,
                .voucher-num {
                    color: #7f7f7f;
                }
                .float-r {
                    line-height: 36px;
                }
                .hover-display {
                    display: none;
                }
            }
            .voucher-list-lines {
                .voucher-list-line {
                    width: 100%;
                    padding: 0 20px;
                    box-sizing: border-box;
                    display: flex;
                    justify-content: space-between;
                    border-bottom: 1px solid #E5E5E5;
                    height: 33px;
                    line-height: 32px;
                    color: #333;
                    text-align: left;
                    &:hover {
                        background-color: #dff7df;
                    }
                    .voucher-list-description,
                    .voucher-list-asubName {
                        text-align: left;
                        overflow: hidden;
                        text-overflow: ellipsis; 
                        white-space: nowrap;
                    }
                }
            }
        }
    }
    .check-box-tool-bar {
        margin: 70px auto;
    }
}
body[erp] {
    .check-sales-diloag {
        .check-voucher-head {
            background-color: var(--erp-table-title-color);
            border: none;
        }
        .check-voucher-list {
            border: none;
            &:hover {
                border: none;
                .hover-display {
                    display: none !important;
                }
            }
            .voucher-list-item {
                margin-top: 10px;
                border: 1px solid #E5E5E5;
                &:hover {
                    border: 1px solid #3d7fff;
                    .voucher-list-head {
                        .hover-display {
                            display: inline-block !important;
                        }
                    }
                }
                .voucher-list-lines {
                    .voucher-list-line:hover {
                        background-color: var(--erp-table-title-color);
                    }
                    .voucher-list-line:last-child {
                        border-bottom: none;
                    }
                }
            }
        }
    }
}
</style>
