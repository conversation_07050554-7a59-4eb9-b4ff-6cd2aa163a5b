import type { IVoucherSetting, ISummaryCheckOption } from './type';
import { settingFixedTexts, settingSummaryChecks, settingTypes } from './type';

export const getSettingFixedTexts = (type: typeof settingTypes[number]) => settingFixedTexts[type];
export const getSettingSummaryChecks = (type: typeof settingTypes[number]) => settingSummaryChecks[type];
export const getBillSettingsType = (type: number) =>{
    switch (type) {
        case 1010:
            return 'WHSAll';
        case 1020:
            return 'SellAll';
        case 1030:
            return 'Receipt';
        case 1040:
            return 'Payment';
        case 1050:
            return 'OtherWHSAll';
        case 1060:
            return 'OtherWHSOutAll';
        case 1070:
            return 'CostAdjustment';
        case 1080:
            return 'CarryOverSellAll';
        case 1090:
            return 'OtherReceipt';
        case 1100:  
            return 'OtherPayment';
        case 1110:
            return 'Assembly';
        case 1120:
            return 'Dassembly';
        case 1130:
            return 'OffsetAll';
        case 1140:
            return 'AccountTransfer';
        default:
            return 'WHSAll';
    }
}

export const getBillSettingsTypeText = (type: typeof settingTypes[number]) =>{
    switch (type) {
        case 'WHSAll':
            return '采购单';
        case 'SellAll':
            return '销售单';
        case 'Receipt':
            return '收款单';
        case 'Payment':
            return '付款单';
        case 'OtherWHSAll':
            return '其他入库单';
        case 'OtherWHSOutAll':
            return '其他出库单';
        case 'CostAdjustment':
            return '成本调整单';
        case 'CarryOverSellAll':
            return '结转出库成本';
        case 'OtherReceipt':
            return '其他收入单';
        case 'OtherPayment':
            return '其他支出单';
        case 'Assembly':
            return '组装单';
        case 'Dassembly':
            return '拆卸单';
        case 'OffsetAll':
            return '核销单';
        case 'AccountTransfer':
            return '转账单';
        default:
            return '入库单';
    }
}

export const isScmSettingType = (type: typeof settingTypes[number]) => {
    return type === 'WHSAll' || type === 'SellAll' || type === 'Receipt' || type === 'Payment' || type === 'OtherWHSAll' || type === 'OtherWHSOutAll' || type === 'CostAdjustment' || type === 'CarryOverSellAll' || type === 'OtherReceipt' || type === 'OtherPayment' || type === 'Assembly' || type === 'Dassembly' || type === 'OffsetAll' || type === 'AccountTransfer';
}