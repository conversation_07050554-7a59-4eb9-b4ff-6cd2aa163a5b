<template>
    <el-dialog 
        v-model="columnSetShow" 
        title="列设置" 
        center 
        :width="550" 
        class="custom-confirm dialogDrag"
        @close="cancel"
        destroy-on-close
    >
        <div class="column-main" v-dialogDrag v-if="columnSetShow">
            <div class="column-set">
                <div class="column-set-lt">
                    <el-table
                        class="column-table"
                        :data="dataColumns"
                        border
                        :scrollbar-always-on="true"
                        :max-height="260"
                        ref="tableRef"
                        :row-key="'no'"
                        :current-row-key="currentRowKey"
                        :highlight-current-row="true"
                        @row-click="handleRowClick"
                        @header-dragend="headerDragend"
                    >
                        <el-table-column 
                            prop="no" 
                            label="序号" 
                            align="center" 
                            header-align="center"
                            :width="getColumnWidth(tableName, 'no')"
                            :min-width="50"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.no + 1 }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="columnName" 
                            label="列名称" 
                            align="left" 
                            header-align="left" 
                            :width="getColumnWidth(tableName, 'columnName')"
                            :min-width="100" 
                            class-name="className"
                        >
                            <template #default="scope">
                                <div class="drag">
                                    <div class="drag-icon"></div>
                                    <div :class="['text-overflow-ellipsis', {'require': scope.row.isRequire}]">{{ scope.row.columnName }}</div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="isShow" 
                            label="是否显示" 
                            align="center" 
                            header-align="center" 
                            :width="getColumnWidth(tableName, 'isShow')"
                            :min-width="70"
                            >
                            <template #default="scope">
                                <el-switch
                                    :disabled="scope.row.isRequire"
                                    v-model="scope.row.isShow"
                                    @change="changeShow(scope.row, $event)"
                                >
                                </el-switch>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="isFreeze" 
                            label="列冻结" 
                            align="center" 
                            header-align="center" 
                            :width="getColumnWidth(tableName, 'isFreeze')"
                            :min-width="70" 
                            :resizable="false"
                        >
                            <template #default="scope">
                                <el-switch
                                    v-model="scope.row.isFreeze"
                                    @change="changeFreeze(scope.row, $event)"
                                >
                                </el-switch>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="column-set-rt ml-20">
                    <div>
                        <el-button :icon="Upload" :disabled="preDisabled" @click="goStep(-1)">前移</el-button>
                    </div>
                    <div class="mt-10">
                        <el-button :icon="Download" :disabled="nextDisabled" @click="goStep(1)">后移</el-button>
                    </div>
                </div>
            </div>
            <div :class="[{'line': !isErp}]"></div>
            <div class="buttons">            
                <a class="button ml-10" @click="saveDefault">恢复默认</a>
                <a class="button ml-10" @click="cancel">取消</a>
                <a class="button solid-button ml-10" @click="save">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue";
import {
    Upload,
    Download
} from '@element-plus/icons-vue'
import { ElNotify } from "@/util/notify";
import type { IColItem, IDataItem } from "./utils";
import ColumnSetRequire from "./utils";
import { getColumnWidth, saveColWidth, alterArrayPos, getColumnListApi, savColumnListApi } from "./utils";
import Sortable from "sortablejs"
import { request, type IResponseModel } from "@/util/service";

const tableName = "ColumnSet";
const isErp = ref(window.isErp);
const props = withDefaults(
    defineProps<{
        data: Array<any>;
        allColumns: IColItem[];
        setModuleType: number;
        modelValue: boolean;
    }>(),{
        data: () => [],
        setModuleType: 0,
        allColumns: () => [],
        modelValue: false,
    }
);

const emit = defineEmits<{
    (e: "update:modelValue", val: boolean): void;
    (e: "saveColumnSet", data: any): void;
}>();
const columnSetShow = computed({
    get: () => props.modelValue,
    set: (val: boolean) => emit('update:modelValue', val)
});
function cancel() {
    columnSetShow.value = false;
    restData();
}
function restData() {
    preDisabled.value = true;
    nextDisabled.value = true;
    saveRow.value = null;
    currentRowKey.value = "";
    highlightCurrentRow.value = false;
}

const dataColumns = ref<IDataItem[]>([]);
const dataColumnsAll = ref<IColItem[]>([]);
function initData() {
    highlightCurrentRow.value = true;
    const matchedColumns:any[] = props.allColumns.filter(column =>  
        props.data.some(dataItem => dataItem.label === column.columnName)  
    ); 
    //中间某项不显示前后项设置了冻结，下次此项在原来位置显示不对，改到冻结后面
    // 排序将 isFreeze: true 的项放到前面
    // 增加索引以备排序(低版本浏览器isFreeze相同的，会不按原本顺序)  
    for (let i = 0; i < matchedColumns.length; i++) {  
        matchedColumns[i]["no"] = i;  
    } 
    matchedColumns.sort(function(a, b) {  
        if (a.isFreeze === b.isFreeze) {  
            return a.no - b.no;  
        }  
        return a.isFreeze ? -1 : 1;  
    }); 
    // 去除索引字段  
    for (let j = 0; j < matchedColumns.length; j++) {  
        delete matchedColumns[j].no;  
    } 
    dataColumnsAll.value = JSON.parse(JSON.stringify(props.allColumns));
    dataColumns.value = matchedColumns.map((item, index) => {
        return {
            no: index,
            columnField: item.columnField, 
            columnName: item.columnName, 
            isShow: item.isShow, 
            isFreeze: item.isFreeze, 
            isRequire: ColumnSetRequire[props.setModuleType].includes(item.columnName),
        }
    });
}

const preDisabled = ref(true);
const nextDisabled = ref(true);
const saveRow = ref();
const currentRowKey = ref("");
const highlightCurrentRow = ref(false);
const handleRowClick = (row: any, column: any, event: any) => {
    currentRowKey.value = row.no + "";
    saveRow.value = JSON.parse(JSON.stringify(row));
    getBtnStatus(row);
};
//按钮可否点击
const getBtnStatus = (row: any) => {
    if (row.no + 1 === 1) {
        preDisabled.value = true;
        nextDisabled.value = false;
    } else if (row.no + 1 === dataColumns.value.length) {
        preDisabled.value = false;
        nextDisabled.value = true;
    } else {
        preDisabled.value = false;
        nextDisabled.value = false;
    }
}
// 进行元素交换
function swapEle(array: any[], index1: number, index2: number) {    
    // const temp = array[index1];  
    // array[index1] = array[index2];  
    // array[index2] = temp;  
    [array[index1], array[index2]] = [array[index2], array[index1]];
} 
//更新所有数据项中列所在的位置
function updateAllColumns(step: number, columnName: string) {
    const index = dataColumnsAll.value.findIndex(item => item.columnName === columnName);  
    const targetIndex = index + step;  
    swapEle(dataColumnsAll.value, index, targetIndex);
}
//上移下移
const goStep = (step: number) => {
    let columnName = saveRow.value.columnName;
    let index = saveRow.value.no;
    saveRow.value.no = saveRow.value.no + step;
    currentRowKey.value = saveRow.value.no + "";
    dataColumns.value[index].no += step;
    dataColumns.value[(index + step)].no -= step;
    swapEle(dataColumns.value, index, index + step);
    getBtnStatus(saveRow.value);
    updateAllColumns(step, columnName);
}
//更新所有数据项冻结/显示状态
function updateStatus() {
    dataColumns.value.forEach(item => { 
        let index = dataColumnsAll.value.findIndex(v => v.columnName === item.columnName);
        dataColumnsAll.value[index].isFreeze = item.isFreeze;
        dataColumnsAll.value[index].isShow = item.isShow;
    })
}

//切换冻结
const changeFreeze = (row: any, event: any) => {
    let index = row.no;
    if (event) {
        if (index >= 0) {
            dataColumns.value.forEach(item => {  
                if (item.no <= index) {  
                    item.isFreeze = true; 
                    item.isShow = true;
                }  
            });  
        }
    } else {
        if (index < dataColumns.value.length - 1) {
            dataColumns.value.forEach(item => {  
                if (item.no >= index) {  
                    item.isFreeze = false; 
                }  
            }); 
        }
    }
    updateStatus();
}
//切换显示
const changeShow = (row: any, event: any) => {
    if (!event) {
        if (row.isFreeze) {
            row.isFreeze = false;
        }
    }
    updateStatus();
}
const tableRef = ref();
//行拖拽
function rowDrop() {
    const tbody = tableRef.value?.$el?.querySelector(".el-table__body-wrapper tbody") as HTMLElement;
    const wrap = tableRef.value?.$el?.querySelector(".el-scrollbar__wrap") as HTMLElement;
    Sortable.create(tbody, {
        disabled: false, //指示是否禁用了 sortable 功能
        animation: 150, //排序时的动画时长
        delay: 0, //可以开始拖动的延迟时间
        handle: ".className", //指定拖动手柄的选择器，只有该选择器元素被拖动时才能移动整个列表项
        direction: "vertical",
        dropBubble: true,
        scroll: wrap,   
        scrollSensitivity: 30, //具体边缘的位置
        scrollSpeed: 10, // 设置滚动速度为 
        bubbleScroll: true,
        onStart: (evt) => {
            
        },
        onEnd: (evt) => { 
            let oldIndex = evt.oldIndex as number;
            let newIndex = evt.newIndex as number;
            let index1 = dataColumnsAll.value.findIndex((v) =>v.columnName === dataColumns.value[oldIndex].columnName);
            let index2 = dataColumnsAll.value.findIndex((v) =>v.columnName === dataColumns.value[newIndex].columnName);
            dataColumns.value = alterArrayPos(dataColumns.value, oldIndex, newIndex);
            dataColumnsAll.value = alterArrayPos(dataColumnsAll.value, index1, index2);
            nextTick(() => {
                dataColumns.value.forEach((item, index) => {
                    item.no = index;
                })
                currentRowKey.value = dataColumns.value[newIndex].no + "";
                saveRow.value = JSON.parse(JSON.stringify(dataColumns.value[newIndex]));
                getBtnStatus(saveRow.value);
            })
        } 
    })
}
//恢复默认
const saveDefault = () => {
    getColumnListApi(props.setModuleType, true).then((res) => {
        saveData(props.setModuleType, res.data)
    })
}
//保存
const save = () => {
    for (let i=0; i<dataColumns.value.length - 1; i++) {
        if (!dataColumns.value[i].isFreeze && dataColumns.value[ i + 1].isFreeze) {
            ElNotify({
                type: "warning",
                message: "列冻结需要在非列冻结之前",
            });
            return;
        }
    }
    saveData(props.setModuleType, dataColumnsAll.value);
}
function saveData(module: number, data: IColItem[]) {
    savColumnListApi(module, data).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "列设置保存成功",
            });
            emit("saveColumnSet", data);
        } else {
            ElNotify({
                type: "warning",
                message: res.msg ?? "列设置保存失败",
            });
        }
    }).finally(()=>{
        cancel();
    })
}
//改变列宽
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    saveColWidth(tableName, newWidth, column.property);
}

defineExpose({
    initData,
    rowDrop,
    saveData
})
</script>

<style lang="less" scoped>
.column-set {
    margin: 30px;
    display: flex;
    align-items: center;
}
.column-set-lt {
    flex: 1;
    min-width: 0;
}
.column-set-btn {
    width: 80px;
    height: 36px;
}
:deep(.el-table) {
    &.column-table {
        overflow: visible;
        .el-table__header .el-table__cell {
            padding-top: 8px !important;
            padding-bottom: 8px !important;
        }
    }
    .cell {
        font-size: 12px;
        line-height: 20px;
        -webkit-font-smoothing: antialiased;
    }
    .className .cell {
        padding-left: 20px !important;
    }
    .require {
        &:before {
            content: "*";
            color: var(--red);
        }
    }
    tbody tr {
        height: 37px;
        .el-table__cell {
            padding: 0;
        }
    }
    tbody tr:hover {
        .drag-icon {
            display: block;
        }
        .className {
            cursor: move;
        }
    }
    .drag-icon {
        display: none;
        position: absolute;
        top: 7px;
        left: 0;
        width: 30px;
        height: 30px;
        &::before {
            position: absolute;
            left: 6px;
            top: 8px;
            content: "";
            width: 10px;
            height: 2px;
            border-top: 1px solid #aaa;
            border-bottom: 1px solid #aaa;
        }
        &:after {
            position: absolute;
            left: 6px;
            top: 12px;
            content: "";
            width: 10px;
            height: 2px;
            border-bottom: 1px solid #aaa;
        }
    }
}
body[erp] {
    .column-set-rt {
        .el-button {
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }
    .el-table {
        .el-table__header,
        .el-table__body {
            .el-table__row {
                &.el-table__row--striped {
                    background-color: var(--white);
                }
            }
        }
        .el-table__body tr.el-table__row--striped td.el-table__cell {
            background-color: var(--white);
        }
    }
}
</style>
