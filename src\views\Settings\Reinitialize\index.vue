<template>
    <div class="content">
        <div class="title">重新初始化</div>
        <div class="slot-title">重新初始化</div>
        <div class="main-content">
            <div class="slot-mini-content">
                <div class="main-title">重新初始化将彻底清空帐套中所有数据,并不可恢复,请慎重!!</div>
                <div class="main-center">
                    <div>1，系统将删除您新增的所有科目</div>
                    <div>2，系统将删除您录入的所有凭证</div>
                    <div>3，系统将删除您录入的所有初始化数据</div>
                </div>
                <div class="main-checkbox">
                    <label class="checkbox-button">
                        <input type="checkbox" v-model="understanded" />
                        <span class="checkbox-box">我已清楚了解将产生的后果</span>
                    </label>
                    <span class="highlight-red" v-show="tipShow">（请先确认并勾选“我已清楚了解将产生的后果”）</span>
                </div>
                <div class="buttons" style="text-align: left">
                    <a :class="understanded ? 'button' : 'button disabled'" ref="reinitializeRef" @click="reinitialize">重新初始化</a>
                </div>
            </div>
        </div>
    </div>
    <el-dialog center width="478" title="重新初始化" v-model="dialogShow" class="dialogDrag">
        <div class="ren-confirm-content" v-dialogDrag>
            <div class="ren-confirm-main">
                <div class="txt">
                    您将要重新初始化以下账套：<span>{{ AccountName }}</span>
                </div>
                <div class="txt mt-10 highlight-red">注意：重新初始化该账套后，所有的数据都不能再恢复！</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="ReinitializeConfirm">确认</a>
                <a class="button ml-10" @click="() => (dialogShow = false)">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "Reinitialize",
};
</script>
<script setup lang="ts">
import { ref, watch } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { AppConfirmDialog } from "@/util/appConfirm";
import { setTopLocationhref } from "@/util/url";
import { useLoading } from "@/hooks/useLoading";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { getServiceId } from "@/util/proUtils";

const currentAccountInfo = useAccountSetStore().accountSet;

const reinitializeRef = ref<HTMLElement>();

const AccountName = ref("");
const dialogShow = ref(false);
const understanded = ref(false);
const tipShow = ref(false);
let reinitializeConfirm = true;
const reinitialize = () => {
    if (!understanded.value) tipShow.value = true;
    const noCanNext = reinitializeRef.value?.classList.contains("disabled");
    if (noCanNext) return;
    if (!reinitializeConfirm) {
        ElNotify({ type: "warning", message: "正在初始化，请稍后..." });
        return;
    }
    AccountName.value = currentAccountInfo?.asName as string;
    dialogShow.value = true;
};
const ReinitializeConfirm = () => {
    dialogShow.value = false;
    AppConfirmDialog(1100).then((r: boolean) => {
        if (r) {
            handleReinitialize();
        }
    });
};
const handleReinitialize = () => {
    reinitializeConfirm = false;
    useLoading().enterLoading("努力加载中，请稍候...");
    request({ url: `/api/Reinitialize/ReInitialzeV2?serviceid=${getServiceId()}`, method: "post", data: null })
        .then((res: IResponseModel<string>) => {
            useLoading().quitLoading();
            if (res.state == 1000) {
                if (res.subState != 0) {
                    ElNotify({ type: "warning", message: res.msg || "重新初始化失败，请重试" });
                    return;
                }
                ElNotify({ type: "success", message: "账套初始化成功！" });
                thirdPartNotify(thirtPartNotifyTypeEnum.reinitializeInitAccountSet, { asId: currentAccountInfo?.asId }).then(() => {
                    setTimeout(function () {
                        if (isLemonClient()) {
                            getLemonClient().setSelectedAccount(Number(currentAccountInfo?.asId));
                        } else {
                            setTopLocationhref("/Default/Default?appasid=" + res.data);
                        }
                    }, 500);
                });
            } else {
                ElNotify({ type: "warning", message: res.msg ?? "重新初始化失败，请重试" });
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "重新初始化失败，请重试" });
        })
        .finally(() => {
            reinitializeConfirm = true;
        });
};

watch(understanded, (val) => {
    if (val) tipShow.value = false;
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.main-content {
    text-align: left;
    box-sizing: border-box;
    margin: 0 auto;
    .slot-mini-content {
        width: auto;
        margin: 0 auto;
        display: inline-flex;
        border: 1px solid var(--slot-title-color);
        padding: 0 200px;
        margin-top: 32px;
    }
    .main-title {
        padding-top: 32px;
        color: var(--red);
        font-size: var(--h2);
        line-height: 25px;
        font-weight: bold;
    }
    .main-center {
        margin: 20px 0;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 24px;
        padding: 0;
    }
    .main-checkbox {
        .highlight-red {
            font-size: var(--font-size);
        }
    }
    .buttons {
        margin-top: 40px;
        margin-left: 175px;
        padding-bottom: 60px;
        border-top: none;
        padding: 0 0 60px;
        .button {
            border-color: #d9d9d9;
            background-color: rgba(0, 0, 0, 0.04);
            color: var(--red);
            &:hover {
                border-color: var(--red);
                background-color: var(--red);
                color: var(--white);
            }
            &.disabled {
                border-color: #d9d9d9;
                background-color: rgba(0, 0, 0, 0.04);
                color: #aeaeae;
            }
        }
    }
}
.ren-confirm-content {
    .ren-confirm-main {
        padding: 30px 40px;
        .txt {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
