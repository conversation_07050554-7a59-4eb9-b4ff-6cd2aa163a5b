import { defineStore } from "pinia";
import { nextTick, ref } from "vue";
import store from "..";
import router from "@/router";
import { getGlobalToken } from "@/util/baseInfo";
import { globalWindowOpenPage,getUrlAllowParams,getUrlSearchParams } from "@/util/url";
import { erpCreateTab } from "@/util/erpUtils";

export interface IRouterModel {
    name: string;
    title: string;
    path: string;
    fullPath: string;
    alive: boolean;
    cache: boolean;
    iframeComponet?: any;
    scrollTop?: number;
    isEditting?: boolean;
    stop?: boolean;
}

export const routerArrayStore = defineStore("routerArray", () => {
    const routerArray = ref<Array<IRouterModel>>([]);
    const leaveValidatorArray = ref<Array<{ path: string; validator: () => boolean }>>([]);
    const operateLeaveValidatorArray = ref<Array<{ path: string; validator: (path: string,operate: string) => boolean }>>([]);

    const enterRouter = (routerModel: IRouterModel) => {
        if (routerModel.path === "/") return;
        const currentRouter = routerArray.value.find((item) => item.path.toLocaleLowerCase() === routerModel.path.toLocaleLowerCase());
        routerArray.value.forEach((item) => {
            item.alive = false;
        });
        if (!currentRouter) {
            if (routerModel.title.indexOf(".aspx") === -1) {
                routerModel.alive = true;
                routerArray.value.push(routerModel);
            }
        } else {
            currentRouter.fullPath = routerModel.fullPath;
            currentRouter.alive = true;
        }
    };

    const removeRouter = (path: string) => {
        if (!window.isErp) {
            if (!leaveValidate(path)) return;
            if (!OperateLeaveValidate(path, "remove")) return;
        }
        const index = routerArray.value.findIndex((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        if (index !== -1) {
            if (!window.isErp) {
                if (routerArray.value[index].alive) {
                    let nextRouter = routerArray.value[index + 1];
                    if (!nextRouter) {
                        nextRouter = routerArray.value[index - 1];
                    }
                    globalWindowOpenPage(nextRouter.fullPath, nextRouter.title);
                }
            }
            routerArray.value.splice(index, 1);
        }
    };
    const forceRemoveRouter = (path: string) => {
        const index = routerArray.value.findIndex((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        if (index !== -1) {
            if (routerArray.value[index].alive) {
                let nextRouter = routerArray.value[index + 1];
                if (!nextRouter) {
                    nextRouter = routerArray.value[index - 1];
                }
                globalWindowOpenPage(nextRouter.fullPath, nextRouter.title);
            }
            routerArray.value.splice(index, 1);
        }
    };

    const removeAllRouter = () => {
        for (let i = 1; i < routerArray.value.length; i++) {
            if (window.isErp) {
                routerArray.value.splice(i, 1);
                i--;
            } else {
                if (!leaveValidate(routerArray.value[i].path)) {
                    continue;
                } else if (!OperateLeaveValidate(routerArray.value[i].path, "remove")) {
                    continue;
                } else {
                    routerArray.value.splice(i, 1);
                    i--;
                }
            }
        }
        if (!routerArray.value[0].alive) {
            globalWindowOpenPage(routerArray.value[0].fullPath, routerArray.value[0].title);
        }
    };

    const removeOthersRouter = (path: string) => {
        const index = routerArray.value.findIndex((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        if (index === 0) {
            removeAllRouter();
            return;
        }
        const currentRouter = routerArray.value[index];
        if (currentRouter) {
            for (let i = 1; i < routerArray.value.length; i++) {
                if (window.isErp) {
                    if(routerArray.value[i] === currentRouter) continue;
                    routerArray.value.splice(i, 1);
                    i--;
                } else {
                    if (!leaveValidate(routerArray.value[i].path) || routerArray.value[i] === currentRouter) {
                        continue;
                    } else if (!OperateLeaveValidate(routerArray.value[i].path, "remove")) {
                        continue;
                    } else {
                        routerArray.value.splice(i, 1);
                        i--;
                    }
                }
            }
        }
        if (!currentRouter) {
            globalWindowOpenPage(
                routerArray.value[routerArray.value.length - 1].fullPath,
                routerArray.value[routerArray.value.length - 1].title
            );
        } else {
            if (!currentRouter.alive) {
                globalWindowOpenPage(currentRouter.fullPath, currentRouter.title);
            }
        }
    };

    const refreshRouter = (path: string) => {
        if (!window.isErp) {
            if (!leaveValidate(path)) return;
            if (!OperateLeaveValidate(path, "refresh")) return;
        }
        const index = routerArray.value.findIndex((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        const currentRouter = routerArray.value[index];
        currentRouter.cache = false;
        nextTick(() => {
            router.replace("/?appasid=" + getGlobalToken());
            setTimeout(() => {
                router.replace(currentRouter.fullPath);
                currentRouter.cache = true;
            });
        });
    };

    const replaceCurrentRouter = (fullPath: string) => {
        const currentRouter = routerArray.value.find((item) => item.alive);
        if (currentRouter) {
            currentRouter.fullPath = fullPath;
        }
        router.replace(fullPath);
    };

    const registerLeaveValidator = (path: string, validator: () => boolean): { dispose: () => void } => {
        const item = { path: path, validator: validator };
        leaveValidatorArray.value.push(item);
        return {
            dispose: () => {
                const index = leaveValidatorArray.value.findIndex((i) => i.path === item.path && i.validator === item.validator);
                if (index !== -1) {
                    leaveValidatorArray.value.splice(index, 1);
                }
            },
        };
    };

    const leaveValidate = (path: string) => {
        for (let i = 0; i < leaveValidatorArray.value.length; i++) {
            const item = leaveValidatorArray.value[i];
            if (item.path === path && !item.validator()) {
                return false;
            }
        }
        return true;
    };

    const registerOperateLeaveValidator = (path: string, validator: (path: string,operate: string,) => boolean): { dispose: () => void } => {
        const item = { path: path, validator: validator };
        operateLeaveValidatorArray.value.push(item);
        return {
            dispose: () => {
                const index = operateLeaveValidatorArray.value.findIndex((i) => i.path === item.path && i.validator === item.validator);
                if (index !== -1) {
                    operateLeaveValidatorArray.value.splice(index, 1);
                }
            },
        };
    };

    const OperateLeaveValidate = (path: string, operate: string) => {
        for (let i = 0; i < operateLeaveValidatorArray.value.length; i++) {
            const item = operateLeaveValidatorArray.value[i];
            if (item.path === path && !item.validator(path,operate)) return false;
        }
        return true;
    };

    const rememberScrollTop = (path: string) => {
        const index = routerArray.value.findIndex((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        if (index !== -1) {
            routerArray.value[index].scrollTop = document.querySelector(".router-container")!.scrollTop;
        }
    };

    const restoreScrollTop = () => {
        const index = routerArray.value.findIndex((item) => item.alive);
        if (index !== -1) {
            document.querySelector(".router-container")!.scrollTop = routerArray.value[index].scrollTop || 0;
        }
    };

    const changeRouterEditting = (path: string, isEditting: boolean) => {
        const currentRouter = routerArray.value.find((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        if (currentRouter) {
            currentRouter.isEditting = isEditting;
        }
    };

    const resetRouterQuery = (path: string) => {
        const currentRouter = routerArray.value.find((item) => item.path.toLocaleLowerCase() === path.toLocaleLowerCase());
        if (!currentRouter) return;
        const queryKeyValue = currentRouter.fullPath.split("?")[1] ?? "";
        const searchParams = new URLSearchParams();
        getUrlAllowParams().forEach((key) => {
            const value = new URLSearchParams(queryKeyValue).get(key);
            if (value) {
                searchParams.append(key, value);
            }
        });
        const newQuery = searchParams.toString();
        if (newQuery) {
            currentRouter.fullPath = currentRouter.path + "?" + newQuery;
        }
        if(window.isErp){
            erpCreateTab(currentRouter.path,currentRouter.title)
        }
    };
    return {
        routerArray,
        enterRouter,
        removeRouter,
        forceRemoveRouter,
        removeAllRouter,
        removeOthersRouter,
        refreshRouter,
        replaceCurrentRouter,
        registerLeaveValidator,
        leaveValidate,
        rememberScrollTop,
        restoreScrollTop,
        changeRouterEditting,
        resetRouterQuery,
        registerOperateLeaveValidator,
        OperateLeaveValidate,
    };
});

/** 在setup外使用 */
export function useRouterArrayStoreHook() {
    return routerArrayStore(store);
}
