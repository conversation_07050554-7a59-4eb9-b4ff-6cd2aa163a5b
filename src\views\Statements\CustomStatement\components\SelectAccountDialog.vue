<template>
    <div>
        <el-dialog
            v-model="batchAddAccountDialogShow"
            :title="'选择' + accountType.aaname"
            width="798px"
            class="batch-add-asubs-dialog custom-confirm dialogDrag"
            @close="cancelDialog"
        >
            <div class="dialog-content" v-dialogDrag>
                <div class="batch-add-asubs-dialog-container">
                    <div class="left-container">
                        <div class="left-top">
                            <SearchInfo
                                :width="240"
                                :height="30"
                                :placeholder="'输入编码或名称搜索'"
                                @change-value="search"
                                @search="search"
                            ></SearchInfo>
                        </div>
                        <div class="asub-table">
                            <VirtualTable
                                class="subject-table"
                                ref="accountingTableRef"
                                :data="tableData"
                                :columns="Columns"
                                :fit="true"
                                height="280"
                                row-key="aaeid"
                                :virtual-table="true"
                                empty-text="暂无数据"
                                :minVirtualScrollLines="100"
                                :scrollbar-show="Boolean(tableData.length)"
                                :highlight-current-row="false"
                                :show-overflow-tooltip="true"
                                @row-click="tableRowClick"
                                :tableName="setModule"
                            >
                                <template #selection>
                                    <el-table-column width="36px" align="center" props="isChecked" :resizable="false">
                                        <template #header>
                                            <el-checkbox v-model="selectAllChecked" @change="updateRightTableDataAll(selectAllChecked)" />
                                        </template>
                                        <template #default="scope">
                                            <el-checkbox
                                                v-model="scope.row.isChecked"
                                                @click.stop
                                                @change="updateRightTableData(scope.row, scope.row.isChecked)"
                                            />
                                        </template>
                                    </el-table-column>
                                </template>
                            </VirtualTable>
                        </div>
                    </div>
                    <div class="right-container">
                        <Table
                            class="subject-table-right"
                            :data="tableRightList"
                            :columns="rightColumn"
                            height="280"
                            :scrollbar-show="true"
                            :highlight-current-row="false"
                            :show-overflow-tooltip="false"
                            @scroll="handleRightTableScroll"
                        >
                            <template #checkedList>
                                <el-table-column
                                    :label="'已选(' + tableRightList.length + '个)'"
                                    width="auto"
                                    align="left"
                                    header-align="center"
                                    :resizable="false"
                                >
                                    <template #header>
                                        <div class="table-header">
                                            <span>{{ "已选(" + tableRightList.length + "个)" }}</span>
                                            <a class="link" @click="clearSelection">清空</a>
                                        </div>
                                    </template>
                                    <template #default="scope">
                                        <ToolTip
                                            :content="scope.row.aaname"
                                            :line-clamp="1"
                                            :teleported="true"
                                            placement="right"
                                            :offset="18"
                                            :fontSize="12"
                                            :maxWidth="163"
                                            popperClass="pivot-asub-popper"

                                        >
                                            <div>{{ scope.row.aaname }}</div>
                                        </ToolTip>
                                        <img
                                            src="@/assets/Statements/remove-btn.png"
                                            class="delete-btn"
                                            @click="removeCheckAsub(scope.row, scope.row.aaeid)"
                                        />
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
                <div class="buttons erp-buttons">
                    <a class="button solid-button mr-10" @click="accountAddSure">确定</a>
                    <a class="button" @click="cancelDialog">取消</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed, toRef, watchEffect, nextTick, onMounted } from "vue";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITabListItem } from "@/views/Settings/AccountSubject/types";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import SearchInfo from "@/components/SearchInfo/index.vue";
import type { IAssistingAccountWithChecked } from "@/api/assistingAccounting";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { ElNotify } from "@/util/notify";
import VirtualTable from "@/components/Table/VirtualTable.vue";
import { cloneDeep } from "lodash";
import ToolTip from "@/components/Tooltip/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "CustomSelAccountDialog";
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    accountType: {
        type: Object,
        required: true,
    },
    selectList: {
        type: Array<any>,
        default: () => [],
    },
    position: {
        type: String,
        required: true,
    },
});
const emits = defineEmits(["update:modelValue", "addAccountSure"]);
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingListAll");
const accountingTableRef = ref();

let searchInfo = ref("");

const batchAddAccountDialogShow = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});

const Columns = ref<IColumnProps[]>([
    { slot: "selection", width: 36, headerAlign: "center", align: "center" },
    { label: props.accountType.aaname + "编码", prop: "aanum", minWidth: 40, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'aanum') },
    { label: props.accountType.aaname + "名称", prop: "aaname", minWidth: 60, align: "left", headerAlign: "left", resizable: false },
]);
watchEffect(() => {
    Columns.value = [
        { slot: "selection", width: 36, headerAlign: "center", align: "center" },
        { label: props.accountType.aaname + "编码", prop: "aanum", minWidth: 40, align: "left", headerAlign: "left" , width: getColumnWidth(setModule, 'aanum') },
        { label: props.accountType.aaname + "名称", prop: "aaname", minWidth: 60, align: "left", headerAlign: "left", resizable: false },
    ];
});
const tableRightList = ref<IAssistingAccountWithChecked[]>([]);

const calcAllData = () => {
    let data = cloneDeep(assistingAccountingList.value);
    let list: IAssistingAccountWithChecked[] = [];
    data.forEach((item: IAssistingAccountWithChecked) => {
        if (item.aatype === props.accountType.aatype) {
            item.isChecked = props.selectList.some((v: any) => v.aaeid === item.aaeid);
            list.push(item);
        }
    });
    if (list.length && list[0].aaeid < 0) {
        list.push(list.shift() as IAssistingAccountWithChecked);
    }
    return list;
};
let allData = ref<IAssistingAccountWithChecked[]>(calcAllData());
let tableData = ref<IAssistingAccountWithChecked[]>(allData.value);
const rightColumn = ref([{ slot: "checkedList" }]);
watch(
    () => props.selectList,
    () => {        
        tableRightList.value = props.selectList;
    },
    { immediate: true }
);
const selectAllChecked = ref(false);
const tableRowClick = (row: IAssistingAccountWithChecked, column: any, event: any) => {
    let selected = tableRightList.value.some((item: IAssistingAccountWithChecked) => item.aaeid === row.aaeid);
    allData.value.find((item) => item.aaeid === row.aaeid)!.isChecked = !selected;
    tableRightList.value = allData.value.filter((v) => v.isChecked);
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
    accountingTableRef.value?.updateView();
};
function updateRightTableData(row: IAssistingAccountWithChecked, isChecked: boolean) {
    allData.value.find((item) => item.aaeid === row.aaeid)!.isChecked = isChecked;
    tableRightList.value = allData.value.filter((v) => v.isChecked);
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
}
function updateRightTableDataAll(allCheck: boolean) {
    tableData.value.forEach((v) => (v.isChecked = allCheck));
    accountingTableRef.value?.updateView();
    tableRightList.value = allData.value.filter((v) => v.isChecked);
}

const removeCheckAsub = (row: IAssistingAccountWithChecked, aaeid: number) => {
    tableRightList.value = tableRightList.value.filter((v: IAssistingAccountWithChecked) => v.aaeid !== aaeid);
    allData.value.find((item) => item.aaeid === row.aaeid)!.isChecked = false;
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
    accountingTableRef.value?.updateView();
};
const accountAddSure = () => {
    batchAddAccountDialogShow.value = false;
    if (!tableRightList.value.length) {
        ElNotify({
            type: "warning",
            message: `${props.position}维度${props.accountType.aaname}辅助核算项目不能为空，请选择${props.accountType.aaname}`,
        });
    }
    emits("addAccountSure", tableRightList.value, props.accountType);
    resetDialog();
};
const cancelDialog = () => {
    batchAddAccountDialogShow.value = false;
    if (!tableRightList.value.length && !props.selectList.length) {
        ElNotify({
            type: "warning",
            message: `${props.position}维度${props.accountType.aaname}辅助核算项目不能为空，请选择${props.accountType.aaname}`,
        });
    }
    resetDialog();
};
const resetDialog = () => {
    searchInfo.value = "";
    allData.value.forEach((v) => {
        v.isChecked = false;
    });
    tableRightList.value = [];
};
const clearSelection = () => {
    allData.value.forEach((v) => {
        v.isChecked = false;
    });
    selectAllChecked.value = false;
    tableRightList.value = [];
    accountingTableRef.value?.updateView();
};
const handleRightTableScroll = () => {
    let poppers = document.querySelectorAll(".el-popper.pivot-asub-popper");
    if(poppers.length > 0) {
        for (let i = 0; i < poppers.length; i++) {
            (poppers[i] as HTMLElement).style.display = "none";
        }
    }
 
};
const search = (data: string) => {
    searchInfo.value = data;
    updateTableView();
};
function updateTableView() {
    filterData();
    accountingTableRef.value?.resetVirtualTableState();
    nextTick(() => {
        accountingTableRef.value?.updateView();
    });
}
function filterData() {
    tableData.value = searchInfo.value.trim()
        ? allData.value.filter((v: IAssistingAccountWithChecked) => {
              return v.aanum.includes(searchInfo.value) || v.aaname.includes(searchInfo.value);
          })
        : allData.value;
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
}
watch(
    batchAddAccountDialogShow,
    () => {
        nextTick(() => {
            if (batchAddAccountDialogShow.value) {
                accountingTableRef.value?.updateView();
                selectAllChecked.value = tableData.value.length > 0 && tableData.value.length === tableRightList.value.length;
            }
        });
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
.batch-add-asubs-dialog {
    .batch-add-asubs-dialog-container {
        margin-bottom: 15px;
        .left-container {
            display: inline-block;
            vertical-align: top;
            margin-right: 16px;
            text-align: left;
            margin-top: 15px;
            width: 546px;
            .left-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .asub-tabs {
                font-size: 0;
                overflow: hidden;
                border-radius: 2px;
                display: inline-block;
                vertical-align: top;
                &.el-radio-group {
                    margin-bottom: 0 !important;
                }
            }
            .asub-table {
                margin-top: 8px;
                .table.subject-table {
                    border-bottom: none;
                    :deep(.el-table__empty-block) {
                        width: 100% !important;
                        min-height: 0;
                    }
                }
                :deep(.el-table__inner-wrapper) {
                    height: 278px !important;
                    border-bottom: 1px solid var(--el-border-color-lighter);
                }
            }
        }
        .right-container {
            display: inline-block;
            vertical-align: top;
            text-align: left;
            width: 204px;
            margin-top: 55px;
            height: 280px;
            // border: 1px solid var(--table-border-color);
            box-sizing: border-box;
            .subject-table-right {
                :deep(.el-table__empty-block) {
                    width: 100% !important;
                    min-height: 0;
                }
                :deep(.cell) {
                    display: flex;
                    align-items: center;
                    height:100%;
                    .span_wrap {
                        flex: 1;
                    }
                    .table-header {
                        display: flex;
                        justify-content: space-between;
                    }
                    & div {
                        display: inline-block;
                        width: 163px;
                        height: 100%;
                        line-height: 40px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        vertical-align: top;
                    }
                    .delete-btn {
                        background-size: 100%;
                        background-repeat: no-repeat;
                        width: 12px;
                        height: 12px;
                        float: right;
                        cursor: pointer;
                        text-align: center;
                        margin: 0 5px;

                    }
                }
            }
        }
    }
}
</style>
