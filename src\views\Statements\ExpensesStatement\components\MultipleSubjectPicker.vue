<template>
    <VirtualSelectCheckbox
        width="298px"
        connector=","
        ref="virtualSelectCheckboxRef"
        v-model:selectedList="selectedList"
        :useIcon="true"
        :options="subjectList"
        :custom-props="customProps"
        :useValueLabel="true"
        :customFilterMethod="filterMethodFn"
        :showAll="props.showAll"
        :selectWithBlur="true"
        :blurMethod="filterMethod"
        :filterByExpenses="props.filterByExpenses"
        :filterStartCode="filterStartCode"
        @check-box-show-change="handleCheckBoxShowChange"
        @searchListLengthChange="searchListLengthChange"
    >
        <template #icon>
            <el-popover 
                placement="right" 
                trigger="click" 
                :width="'372px'" 
                :teleported="false" 
                ref="popoverRef" 
                popper-class="subject-popover"
                @after-enter="showTree = true"
                @before-leave="showTree = false"
            >
                <template #reference>
                    <div class="asub-img" @click="iconClick($event)"></div>
                </template>
                <SubjectDialog ref="subjectDialogRef" :showTree="showTree" :show-disabled="true"></SubjectDialog>
            </el-popover>
        </template>
    </VirtualSelectCheckbox>
</template>

<script setup lang="ts">
import { ref, computed, toRef, nextTick, provide, inject, onMounted, onUnmounted } from "vue";
import { updateAsubCodeKey, popoverHandleCloseKey, stopPopoverCloseKey } from "@/components/Picker/SubjectPicker/symbols";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { ElPopover } from "element-plus";
import VirtualSelectCheckbox from "./VirtualSelectCheckbox.vue";
import SubjectDialog from "@/components/Picker/SubjectPicker/SubjectDialog.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";

// 会计准则
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;

const customProps = { id: "asubCode", name: "name" };
const virtualSelectCheckboxRef = ref<InstanceType<typeof VirtualSelectCheckbox>>();

const accountSubjectList = toRef(useAccountSubjectStore(), "accountSubjectList");

const filterStartCode = computed(() => (accountStandard === 2 ? "6" : "5"));

const subjectList = computed(() => {
    let initAccountSubjectList = accountSubjectList.value;
    if (props.filterByExpenses) {
        initAccountSubjectList = accountSubjectList.value.filter((item) => item.asubCode.startsWith(filterStartCode.value));
    }

    return initAccountSubjectList.map((item) => {
        return {
            id: item.asubId,
            name: item.asubCode + " " + item.asubName,
            asubCode: item.asubCode,
            asubName: item.asubName,
        };
    });
});

const emit = defineEmits<{
    (event: "update:selectedList", args: string[]): void;
    (event: "searchListLengthChange", show: boolean): void;
}>();

const props = withDefaults(
    defineProps<{
        selectedList: string[];
        width?: string;
        showAll?: boolean;
        placeHolder?: string;
        // 费用明细表过滤
        filterByExpenses?: boolean;
    }>(),
    {
        width: "200px",
        showAll: true,
        filterByExpenses: false,
    }
);

const showTree = ref(false);

const selectedList = computed({
    get() {
        return props.selectedList;
    },
    set(value: string[]) {
        emit("update:selectedList", value);
    },
});

function iconClick(e: Event) {
    e.stopPropagation();
    e.preventDefault();
    virtualSelectCheckboxRef.value?.closeCheckBox();
}

function updateAsubCode(asubCode: string) {
    if (asubCode) {
        const asubIndex = subjectList.value.findIndex((item) => item.asubCode === asubCode);
        if (asubIndex > -1) {
            selectedList.value.push(asubCode);
            virtualSelectCheckboxRef.value?.changeUpElement(true);
        }
    }
}
provide(updateAsubCodeKey, updateAsubCode as any);

// 多选过滤
const subjectDialogRef = ref();
// 过滤弹窗科目类型
const filterAsubType = computed(() => (accountStandard === 3 ? 9 : 6));

const popoverRef = ref<InstanceType<typeof ElPopover>>();
function handleClose() {
    setTimeout(() => {
        popoverRef.value?.hide();
        virtualSelectCheckboxRef.value?.closeCheckBox();
        if (stopPopoverClose) {
            stopPopoverClose();
        }
    });
}
provide(popoverHandleCloseKey, handleClose);
const stopPopoverClose = inject(stopPopoverCloseKey) as Function;

function handleCheckBoxShowChange(visible: boolean) {
    if (!visible) {
        nextTick().then(() => {
            stopPopoverClose && stopPopoverClose();
        });
    }
}

function filterMethodFn(query: string) {
    return filterMethod(query);
}
function filterMethod(query: string, fullListWithBlank = true) {
    if (!query.trim()) {
        return fullListWithBlank ? subjectList.value : [];
    }
    query = query.trim();
    query = query.replace(/，/g, ",");
    const returnList: any[] = [];
    if (query.includes(",")) {
        const queryList = query.split(",");
        queryList.forEach((item) => {
            if (!item.trim()) return;
            item = item.trim();
            if (!item.includes("-")) {
                const filterList = subjectList.value.filter((subItem: any, index) => {
                    if (fullListWithBlank) {
                        if (subItem.asubCode.includes(item) || subItem.asubName.includes(item)) {
                            subItem.index = index;
                            return subItem;
                        }
                    } else {
                        if (subItem.asubCode === item) {
                            subItem.index = index;
                            return subItem;
                        }
                    }
                });
                returnList.push(...filterList);
            } else {
                returnList.push(...filterFn(item));
            }
        });
    } else {
        if (query.includes("-")) {
            returnList.push(...filterFn(query));
        } else {
            const filterList = subjectList.value.filter((item: any, index) => {
                if (fullListWithBlank) {
                    if (item.asubCode.includes(query) || item.asubName.includes(query)) {
                        item.index = index;
                        return item;
                    }
                } else {
                    if (item.asubCode === query) {
                        item.index = index;
                        return item;
                    }
                }
            });
            returnList.push(...filterList);
        }
    }
    const uniqueList = Array.from(new Set(returnList));
    const sortList = uniqueList.sort((a, b) => a.index - b.index);
    return sortList;
}
function filterFn(query: string) {
    const splitIndex = query.indexOf("-");
    const startAsubCode = query.slice(0, splitIndex).trim();
    const endAsubCode = query.slice(splitIndex + 1).trim();
    let startAsubIndex = subjectList.value.findIndex((item) => item.asubCode === startAsubCode);
    let endAsubIndex = subjectList.value.findIndex((item) => item.asubCode === endAsubCode);
    if (startAsubCode === "" && endAsubIndex !== -1) {
        startAsubIndex = 0;
    }
    if (endAsubCode === "" && startAsubIndex !== -1) {
        endAsubIndex = subjectList.value.length - 1;
    }
    if (startAsubIndex === -1 || endAsubIndex === -1 || startAsubIndex > endAsubIndex) {
        return [];
    }
    const returnList = subjectList.value.slice(startAsubIndex, endAsubIndex + 1);
    returnList.forEach((item: any, index) => {
        item.index = startAsubIndex + index;
    });
    return returnList;
}

// 查询科目为空时
const searchListLengthChange = (show: boolean) => {
    emit("searchListLengthChange", show);
};

defineExpose({ handleClose });

onMounted(() => {
    if (props.filterByExpenses) {
        // 费用明细表筛选
        subjectDialogRef.value.filterTabsByAsubType(filterAsubType.value);
    }
});
onUnmounted(() => {
    if (props.filterByExpenses) {
        // 恢复全部
        subjectDialogRef.value?.setEditableTabs();
    }
});
</script>
<style lang="less" scoped>
.asub-img {
    position: absolute;
    right: 1px;
    top: 1px;
    bottom: 1px;
    width: 31px;
    background: url("@/assets/AccountBooks/book.png") no-repeat center #fff;
    cursor: pointer;
    z-index: 30;
}
</style>
