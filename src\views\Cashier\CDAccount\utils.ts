import { request } from "@/util/service";
import { Option } from "@/components/SelectCheckbox/types";
import { getGlobalToken } from "@/util/baseInfo";
export interface IBankAccountItem {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
    standard: string;
    fc_rate: string;
}
export interface IBankAccountLabelItem extends IBankAccountItem {
    label: string;
}

export interface ISelect { 
    id: number; 
    label: string; 
}

export const getCDAccountApi = (acType: number | string) => {
    return request({
        url: "/api/CDAccount/List?acType=" + acType,
        method: "get",
    });
};

export function calcCurrentCDAccountList(options: IBankAccountItem[], showDisabled: boolean = false) {
    const list: Array<IBankAccountItem> = showDisabled ? options : options.filter((item) => item.state === "0");
    return list.map((item) => {
        const suffix = item.state === "1" ? "（已禁用）" : "";
        return new Option(Number(item.ac_id), `${item.ac_name}${suffix}`);
    });
}

export function setShowDisabledAccount(modulePage: string, value: boolean) {
    localStorage.setItem(getGlobalToken() + modulePage + "-showDisabledAccount", value.toString());
}

export function getShowDisabledAccount(modulePage: string) {
    return localStorage.getItem(getGlobalToken() + modulePage +"-showDisabledAccount") === "true";
}

export function hasDisableAccount(options: IBankAccountItem[]) {
    return options.some((item) =>item.state === "1");
}
