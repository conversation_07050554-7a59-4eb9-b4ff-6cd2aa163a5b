<template>
    <el-dialog
        v-model="dialogShow"
        title="生成凭证规则"
        center
        width="820"
        :destroy-on-close="true"
        @close="handleCancel"
        class="dialogDrag"
    >
        <div class="voucher-template-box" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="vouher-template-detial">
                <div class="voucher-template-top">
                    <span class="txt float-l"> <span class="highlight-red">*</span><span>模板名称：</span></span>
                    <input ref="vtNameInputRef" type="text" class="float-l" autocomplete="off" v-model="templateInfo.vtName" />
                    <el-select 
                        class="float-r" 
                        v-model="templateInfo.vgId" 
                        :teleported="true"
                        :filterable="true"
                        :filter-method="voucherGroupFilterMethod"
                    >
                        <el-option :label="item.name" :value="item.id" v-for="item in showVoucherGroupList" :key="item.id" />
                    </el-select>
                    <span class="txt float-r">凭证字：</span>
                </div>
                <div
                    class="voucher-template-main"
                    @mouseenter="() => (hasAddSub = true)"
                    @mouseleave="() => (hasAddSub = false)"
                    ref="tableContentRef"
                >
                    <Table
                        ref="tableRef"
                        :columns="columns"
                        :data="editVoucherTableData"
                        empty-text="暂无数据"
                        cell-class-name="row-click-show-others"
                        :hasAddSub="hasAddSub"
                        add-sub-field="index"
                        :add-row-data="tableRowAddData"
                        :not-delete-row="4"
                        :customSubtract="true"
                        :custom-add="true"
                        :scrollbar-show="true"
                        :continuous-click="true"
                        height="186px"
                        @handleAdd="handleTableAddSub"
                        @handleSubtract="handleSubtract"
                        @row-click="handleRowclick"
                        :show-overflow-tooltip="true"
                        :tableName="setModule"
                    >
                        <template #description>
                            <el-table-column 
                                label="摘要" 
                                min-width="307px" 
                                align="left" 
                                header-align="left"
                                prop="description"
                                :width="getColumnWidth(setModule, 'description')"
                                :show-overflow-tooltip="false"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 100%">
                                        <div class="click-show-item" v-if="currentClickRowIndex === scope.row.index && editTableCanShow">
                                            <input
                                                ref="descriptionInputRef"
                                                type="text"
                                                maxlength='256'
                                                v-model="editTableInfo.description"
                                                @change="handleDescriptionChange"
                                                @focus="tableFocus"
                                                @input="limitInputLength(editTableInfo.description)"
                                            />
                                        </div>
                                        <template v-else>
                                            <ToolTip :content="scope.row.description" :line-clamp="1">
                                                {{ scope.row.description }}
                                            </ToolTip>
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #asubId>
                            <el-table-column 
                                label="会计科目" 
                                min-width="220px" 
                                align="left" 
                                header-align="left"
                                prop="asubId"
                                :width="getColumnWidth(setModule, 'asubId')"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 100%">
                                        <div class="click-show-item" v-if="currentClickRowIndex === scope.row.index && editTableCanShow">
                                            <Select
                                                ref="asubRef"
                                                v-model="editTableInfo.asubId"
                                                placeholder=""
                                                :fit-input-width="true"
                                                :filterable="true"
                                                :clearable="true"
                                                :automatic-dropdown="true"
                                                :IconClearRight="'26px'"
                                                :filterMethod="subjectListFilter"
                                                @change="handleSubjectChange"
                                                @focus="tableFocus"
                                            >
                                                <Option
                                                    v-for="item in newSubjectList"
                                                    :key="item.asubId"
                                                    :label="item.label"
                                                    :value="item.asubId"
                                                />
                                            </Select>
                                        </div>
                                        <template v-else>
                                            <ToolTip :content="scope.row.asubCode + scope.row.asubName" :line-clamp="1">
                                                {{ scope.row.asubCode + scope.row.asubName }}
                                            </ToolTip>
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #valueType>
                            <el-table-column 
                                label="取值" 
                                min-width="108px" 
                                align="left" 
                                header-align="left"
                                prop="valueType"
                                :width="getColumnWidth(setModule, 'valueType')"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 100%">
                                        <div class="click-show-item" v-if="currentClickRowIndex === scope.row.index && editTableCanShow">
                                            <Select
                                                ref="valueTypeRef"
                                                v-model="editTableInfo.valueType"
                                                placeholder=" "
                                                :fit-input-width="true"
                                                :immediately-blur="false"
                                                @change="handleValueTypeChange"
                                                @focus="tableFocus"
                                            >
                                                <Option value="1020" label="自动平衡" />
                                                <Option value="1010" label="按公式取值" />
                                            </Select>
                                        </div>
                                        <template v-else>
                                            <ToolTip
                                                :content="
                                                    scope.row.valueType === 1010
                                                        ? '按公式取值'
                                                        : scope.row.valueType === 1020
                                                        ? '自动平衡(' + scope.row.valueRate + '%)'
                                                        : ''
                                                "
                                                :line-clamp="1"
                                            >
                                                {{
                                                    scope.row.valueType === 1010
                                                        ? "按公式取值"
                                                        : scope.row.valueType === 1020
                                                        ? "自动平衡(" + scope.row.valueRate + "%)"
                                                        : ""
                                                }}
                                            </ToolTip>
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #directory>
                            <el-table-column label="方向" min-width="91px" align="left" header-align="left" :resizable="false">
                                <template #default="scope">
                                    <div style="width: 100%; height: 100%">
                                        <div class="click-show-item" v-if="currentClickRowIndex === scope.row.index && editTableCanShow">
                                            <Select
                                                ref="directoryRef"
                                                v-model="editTableInfo.directory"
                                                placeholder=" "
                                                :fit-input-width="true"
                                                :immediately-blur="false"
                                                @change="handleDirectoryChange"
                                                @focus="tableFocus"
                                            >
                                                <Option value="1" label="借" />
                                                <Option value="2" label="贷" />
                                            </Select>
                                        </div>
                                        <template v-else>
                                            {{ scope.row.directory === 1 ? "借" : scope.row.directory === 2 ? "贷" : "" }}
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
                <div class="balance-panel" v-show="activeValueType === 1020">
                    <div class="balance-panel-title">自动平衡{{ asubName }}</div>
                    <div class="balance-value-rate-container" :class="isErp ? 'erp' : ''">
                        <span>自动结转比例：</span>
                        <input type="number" v-model="valueRate" @blur="handleValueRateBlur" />
                        <span style="margin-left: 8px">%</span>
                    </div>
                    <div class="balance-panel-tips">
                        <div style="margin-left: -42px">
                            <span class="highlight-red">*</span>
                            <span>提示：1、生成凭证时，自动平衡将根据借贷方向自动平衡凭证数据</span>
                        </div>
                        <div style="margin-top: 10px">2、自动平衡的结转比例总和必须等于100%</div>
                    </div>
                </div>
                <div class="balance-panel" v-show="activeValueType === 1010">
                    <div class="balance-panel-title">按公式取值{{ asubName }}</div>
                    <div class="voucher-line-equation-top">
                        <span class="float-l txt">科目名称：</span>
                        <div class="asub-name float-l" ref="lemonSelectRef">
                            <Select
                                v-model="subjectAddInfo.asubId"
                                placeholder=" "
                                :clearable="true"
                                :filterable="true"
                                :IconClearRight="'26px'"
                                :filter-method="subjectFilterMethod"
                            >
                                <Option
                                    v-for="item in showSubjectList"
                                    :key="item.asubId"
                                    :value="item.asubId"
                                    :label="item.label"
                                ></Option>
                            </Select>
                        </div>
                        <span class="txt float-l" style="margin-left: 22px">运算符号：</span>
                        <div class="operator float-l">
                            <Select v-model="subjectAddInfo.operator">
                                <Option label="+" :value="1"></Option>
                                <Option label="-" :value="2"></Option>
                            </Select>
                        </div>
                        <span class="txt float-l" style="margin-left: 22px">取数规则：</span>
                        <div class="float-l balance">
                            <Select v-model="subjectAddInfo.valueType">
                                <Option label="期末余额" :value="1"></Option>
                            </Select>
                        </div>
                        <a class="button solid-button float-r" style="margin-top: 11px" @click="addSubject">添加</a>
                    </div>
                    <div class="voucher-line-equation-main">
                        <Table
                            :columns="columnsBottom"
                            :data="calculationData"
                            empty-text=""
                            :scrollbar-show="true"
                            @row-click="handleRowclickRateBottom"
                        >
                            <template #name>
                                <el-table-column label="科目名称" min-width="359px" align="left" header-align="left">
                                    <template #default="scope">
                                        <span>{{ scope.row.asubCode + scope.row.asubName }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #operator>
                                <el-table-column label="运算符号" min-width="88px" align="left" header-align="left">
                                    <template #default="scope">
                                        <span>{{ scope.row.operator === 1 ? "+" : "-" }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #valueRule>
                                <el-table-column label="取数规则" min-width="88px" align="left" header-align="left">
                                    <template #default="scope">
                                        <span>{{ scope.row.valueType === 1 ? "期末余额" : "" }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #valueRate>
                                <el-table-column label="取值比例" min-width="108px" align="left" header-align="left">
                                    <template #default="scope">
                                        <span>{{ (formatMoney(scope.row.valueRate) || "0.00") + "%" }}</span>
                                        <div class="edit-item" v-show="bottomRateCanEdit[scope.row.index]">
                                            <input
                                                type="text"
                                                :id="'bottomRateInput' + scope.row.index"
                                                :value="scope.row.valueRate"
                                                @focus="handleBottomRateFocus(scope.row.index)"
                                                @blur="handleBottonRateBlur($event, scope.row.index)"
                                                @keypress="handleKeyPress($event)"
                                                @change="handleValueRateChangeBottom($event)"
                                            />
                                            <span>%</span>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #handle>
                                <el-table-column label="操作" min-width="64px" align="left" header-align="left">
                                    <template #default="scope">
                                        <a class="link" @click="() => deleteSubject(scope.row.asubId)">删除</a>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
                <div class="custom-precheck-btns">
                    <a class="button mr-10" @click="handleCancel">取消</a>
                    <a class="button solid-button mr-10" @click="handleSave">保存</a>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick, toRef, watchEffect } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { formatMoney } from "@/util/format";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IVoucherTemplateLine, ICalculation } from "../tpyes";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import ToolTip from "@/components/Tooltip/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { pinyin } from "pinyin-pro";

const setModule = "CheckGenerateVoucherDialog";
interface IEditVoucherTableData extends IVoucherTemplateLine {
    index: number;
}

const isErp = ref(window.isErp);
const hasAddSub = ref(true);
const lemonSelectRef = ref<HTMLDivElement>();
const tableContentRef = ref<HTMLDivElement>();
const descriptionInputRef = ref<HTMLInputElement>();
const vtNameInputRef = ref<HTMLInputElement>();
const asubRef = ref<InstanceType<typeof Select>>();
const valueTypeRef = ref<InstanceType<typeof Select>>();
const directoryRef = ref<InstanceType<typeof Select>>();
const tableRef = ref<InstanceType<typeof Table>>();

const judgeEditShow = (e: Event) => {
    if (tableContentRef.value?.contains(e.target as HTMLElement) || lemonSelectRef.value?.contains(e.target as HTMLElement)) {
        editTableCanShow.value = true;
    } else {
        editTableCanShow.value = false;
    }
};

onMounted(() => {
    window.addEventListener("click", judgeEditShow);
    window.addEventListener("click", handleCheckRateCanEdit);
});
onUnmounted(() => {
    window.removeEventListener("click", judgeEditShow);
    window.removeEventListener("click", handleCheckRateCanEdit);
});
const columns = ref<Array<IColumnProps>>([{ slot: "description" }, { slot: "asubId" }, { slot: "valueType" }, { slot: "directory" }]);
const columnsBottom = ref<Array<IColumnProps>>([
    { slot: "name" },
    { slot: "operator" },
    { slot: "valueRule" },
    { slot: "valueRate" },
    { slot: "handle" },
]);

const props = defineProps<{
    dialogShow: boolean;
}>();

const dialogShow = computed({
    get: () => props.dialogShow,
    set: (val) => emit("update:dialogShow", val),
});

const emit = defineEmits(["update:dialogShow", "successSave"]);

const editVoucherTableData = ref<Array<IEditVoucherTableData>>([]);

const editTableCanShow = ref(true);
function handleRowclick(row: IEditVoucherTableData) {
    const [, column] = arguments;
    if (currentClickRowIndex.value === row.index) return;
    const list: IEditVoucherTableData[] = JSON.parse(JSON.stringify(editVoucherTableData.value)).reverse();
    const filterList = list.filter((item) => item.valueType !== 0);
    const item = filterList.find((item) => item.valueType === 1010);
    // const id = item?.calculations?.reverse().find((item) => item.asubId)?.asubId;
    // const asubId = id ? id + "" : "";

    editTableInfo.description = row.description;
    editTableInfo.asubId = row.asubId === 0 ? "" : row.asubId + "";
    editTableInfo.directory = row.directory ? row.directory + "" : "";
    editTableInfo.valueType = row.valueType ? row.valueType + "" : "";
    nextTick().then(() => {
        currentClickRowIndex.value = row.index;
        editTableCanShow.value = true;
        valueRate.value = row.valueRate ? row.valueRate + "" : "0";
        calculationData.value = row.calculations ?? [];
        bottomRateCanEdit.value = row.calculations?.map(() => false) ?? [];
        bottomRateBlurStatus.value = row.calculations?.map(() => true) ?? [];
        activeValueType.value = row.valueType ?? 0;
        asubName.value = row.asubName === "" ? "" : " — " + row.asubName;
        if (row.valueRate === 0 && row.valueType === 0) {
            activeValueType.value = 1010;
            asubName.value = item ? (item.asubName ? " - " + item.asubName : "") : "";
            // subjectAddInfo.asubId = asubId;
        }

        descriptionInputRef.value?.blur();
        asubRef.value?.blur();
        valueTypeRef.value?.blur();
        directoryRef.value?.blur();
        setTimeout(() => {
            const label = column?.label;
            if (label === "会计科目") {
                asubRef.value?.focus();
            } else if (label === "取值") {
                valueTypeRef.value?.focus();
            } else if (label === "方向") {
                directoryRef.value?.focus();
            } else if (label === "摘要") {
                descriptionInputRef.value?.focus();
            }
        }, 100);
    });
}
const setTableData = (val: any[]) => {
    editVoucherTableData.value = val.map((item, index) => {
        return {
            ...item,
            index,
        };
    });
};
function limitInputLength(val:string) {
        if(val.length===256){
            ElNotify({ type: "warning", message: `亲，摘要不能超过256个字哦~` });
        }
}
const handleDescriptionChange = (e: Event) => {
    const val = (e.target as HTMLInputElement).value;
    if (currentClickRowIndex.value === -1) return;
    editVoucherTableData.value[currentClickRowIndex.value].description = val;
    tableRef.value?.initEditData(editVoucherTableData.value);
};
const handleSubjectChange = (val: string) => {
    const subjectItem = findSubjectItem(val);
    const name = subjectItem.asubAAName;
    const id = subjectItem.asubId;
    const code = subjectItem.asubCode;
    editVoucherTableData.value.splice(currentClickRowIndex.value, 1, {
        ...editVoucherTableData.value[currentClickRowIndex.value],
        asubId: Number(id),
        asubCode: code,
        asubName: name,
    });
    tableRef.value?.initEditData(editVoucherTableData.value);
    asubName.value = name === "" ? "" : " — " + name;
};
const handleValueTypeChange = (val: string) => {
    editVoucherTableData.value.splice(currentClickRowIndex.value, 1, {
        ...editVoucherTableData.value[currentClickRowIndex.value],
        valueType: Number(val),
    });
    tableRef.value?.initEditData(editVoucherTableData.value);
    activeValueType.value = Number(val);
};
const handleDirectoryChange = (val: string) => {
    editVoucherTableData.value.splice(currentClickRowIndex.value, 1, {
        ...editVoucherTableData.value[currentClickRowIndex.value],
        directory: Number(val),
    });
    tableRef.value?.initEditData(editVoucherTableData.value);
};

const tableRowAddData = {
    veId: 0,
    description: "",
    asubId: 0,
    asubCode: "",
    asubName: "",
    aacode: "",
    amount: 0,
    qutAmount: 0,
    valueType: 0,
    valueRate: 0,
    directory: 0,
    calculations: [].slice(),
    show: false,
};
const handleTableAddSub = (data: any) => {
    editVoucherTableData.value.splice(data, 0, {
        ...JSON.parse(JSON.stringify(tableRowAddData)),
    });
    editVoucherTableData.value.forEach((item, index) => {
        item.index = index;
    });
    tableRef.value?.initEditData(editVoucherTableData.value);
    hasAddSub.value = true;
};

const handleSubtract = (index: number) => {
    resetEditTableInfo();
    currentClickRowIndex.value = -1;
    editTableCanShow.value = false;
    if (editVoucherTableData.value.length > 4) {
        editVoucherTableData.value.splice(index, 1);
    } else {
        editVoucherTableData.value.splice(index, 1, {
            ...JSON.parse(JSON.stringify(tableRowAddData)),
            index,
        });
    }
    editVoucherTableData.value.forEach((item, index) => {
        item.index = index;
    });
    hasAddSub.value = true;
};
const editTableInfo = reactive({
    description: "",
    asubId: "",
    valueType: "",
    directory: "",
});
const resetEditTableInfo = () => {
    editTableInfo.description = "";
    editTableInfo.asubId = "";
    editTableInfo.valueType = "";
    editTableInfo.directory = "";
};

const voucherGroupList = toRef(useVoucherGroupStore(), "voucherGroupList");
const asubName = ref("");
let editType: "new" | "edit" = "new";
const defaultVgId = useVoucherGroupStore()?.defaultVgId ?? 1010;
const templateInfo = reactive({
    vtId: 0,
    vtType: 0,
    vtName: "",
    vgId: defaultVgId,
    vgName: "记",
    state: false,
});
const resetTemplateInfo = () => {
    templateInfo.state = false;
    templateInfo.vgId = defaultVgId;
    templateInfo.vtId = 0;
    templateInfo.vtName = "";
    templateInfo.vtType = 0;
};
interface ITemplateInfo {
    vtId: number;
    vtType: number;
    vtName: string;
    vgId: number;
    state: boolean;
}
const changeTemplateInfo = (info: ITemplateInfo) => {
    templateInfo.state = info.state;
    templateInfo.vgId = info.vgId;
    templateInfo.vgName = voucherGroupList.value.find((item) => item.id === info.vgId)?.name ?? "记";
    templateInfo.vtId = info.vtId;
    templateInfo.vtName = info.vtName;
    templateInfo.vtType = info.vtType;
};

watch(
    () => templateInfo.vgId,
    (val) => {
        templateInfo.vgName = voucherGroupList.value.find((item) => item.id === val)?.name ?? "记";
    }
);

const handleCancel = () => {
    dialogShow.value = false;
    currentClickRowIndex.value = -1;
    resetTemplateInfo();
    resetSubjectAddInfo();
    resetEditTableInfo();
    editVoucherTableData.value = [];
    calculationData.value = [];
    activeValueType.value = 0;
    asubName.value = "";
    editType = "new";
};

const canSave = () => {
    const copyData: Array<IEditVoucherTableData> = JSON.parse(JSON.stringify(editVoucherTableData.value));
    const length = copyData.length;
    if (templateInfo.vtName.trim() === "") {
        ElNotify({ type: "warning", message: "请填写模板名称" });
        canSaveFlag = true;
        return false;
    }
    if (templateInfo.vtName.length > 10) {
        ElNotify({ type: "warning", message: "模板名称不能超过10个字" });
        canSaveFlag = true;
        return false;
    }
    let valueRateTotal = 0;
    let valueType1010Count = 0;
    let valueType1020Count = 0;
    for (let i = 0; i < length; i++) {
        const item = copyData[i];
        if (item.valueType === 1010) {
            item.valueRate = 0;
        }
        valueRateTotal += Number(item.valueRate) ?? 0;
        if (item.asubCode !== "" || item.description !== "" || item.valueType !== 0 || item.directory !== 0) {
            if (item.description === "") {
                ElNotify({ type: "warning", message: "第" + (i + 1) + "行，请输入摘要" });
                canSaveFlag = true;
                return false;
            }
            if (item.asubCode === "") {
                ElNotify({ type: "warning", message: "第" + (i + 1) + "行，请选择科目" });
                canSaveFlag = true;
                return false;
            }
            if (item.valueType === 0) {
                ElNotify({ type: "warning", message: "第" + (i + 1) + "行，请选择取值方式" });
                canSaveFlag = true;
                return false;
            }
            if (item.directory === 0) {
                ElNotify({ type: "warning", message: "第" + (i + 1) + "行，请选择方向" });
                canSaveFlag = true;
                return false;
            }
        }
        if (item.valueType === 1010 && item.calculations?.length === 0) {
            ElNotify({ type: "warning", message: "第" + (i + 1) + "行，请添加公式行" });
            canSaveFlag = true;
            return false;
        }
        if (item.valueType === 1010) {
            valueType1010Count++;
        } else if (item.valueType === 1020) {
            valueType1020Count++;
        }
    }
    if (valueType1010Count === 0) {
        ElNotify({ type: "warning", message: "至少包含一行按公式取值的凭证行" });
        canSaveFlag = true;
        return false;
    }
    if (valueType1020Count === 0) {
        ElNotify({ type: "warning", message: "至少包含一行自动平衡的凭证行" });
        canSaveFlag = true;
        return false;
    }
    if (Number(valueRateTotal) !== 100) {
        ElNotify({ type: "warning", message: "所有自动平衡的凭证行结转比例之和应该等于100" });
        canSaveFlag = true;
        return false;
    }
    return true;
};

// 防止多次保存
let canSaveFlag = true;
const handleSave = () => {
    //建立一个宏任务，保证那些input都先失焦之后再提交
    setTimeout(() => {
        // 这里应该用节流而不是防抖
        if (!canSaveFlag) return;
        canSaveFlag = false;
        handleSaveFuc();
    });
};

const handleSaveFuc = () => {
    if (!canSave()) return;
    const copyRequestParams: IEditVoucherTableData[] = JSON.parse(JSON.stringify(editVoucherTableData.value));
    copyRequestParams.forEach((item) => {
        if (item.valueType === 1010) {
            item.valueRate = 0;
        }
    });
    const params = {
        vtId: templateInfo.vtId,
        vtName: templateInfo.vtName,
        vtType: templateInfo.vtType,
        vgId: templateInfo.vgId,
        vgName: templateInfo.vgName,
        state: templateInfo.state,
        voucherTemplateLines: copyRequestParams,
    };
    const urlPath = editType === "new" ? "/CustomCarryOverTemplate" : "/CustomCarryOverTemplate?id=" + params.vtId;
    request({
        url: "/api/" + urlPath,
        method: editType === "edit" ? "put" : "post",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(params),
    })
        .then((res: any) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg ?? res.message ?? "保存失败" });
                return;
            }
            ElNotify({ type: "success", message: "保存成功" });
            emit("successSave");
        })
        .finally(() => {
            canSaveFlag = true;
        });
};

const currentClickRowIndex = ref(-1);
const accountSubjectListWithoutDisabled = toRef(useAccountSubjectStore(), "accountSubjectListWithoutDisabled"); // 科目列表
const subjectList = ref<IAccountSubjectModel[]>([]);

const findSubjectItem = (asubId: string) => {
    const item = subjectList.value.find((item) => Number(item.asubId) === Number(asubId));
    return item ?? { asubId: 0, asubCode: "", asubName: "", asubAAName: "" };
};
const defaultRowClick = () => {
    const item = editVoucherTableData.value[0];
    handleRowclick(item);
    nextTick().then(() => {
        editTableCanShow.value = false;
    });
};
const changeEditType = (type: "new" | "edit") => {
    editType = type;
    if (type === "new") {
        setTimeout(() => {
            vtNameInputRef.value?.focus();
        }, 100);
    }
};
defineExpose({ setTableData, changeEditType, changeTemplateInfo, defaultRowClick, handleCancel });

const activeValueType = ref(0);
const valueRate = ref("0");

const handleValueRateBlur = (e: Event) => {
    const value = (e.target as HTMLInputElement).value;
    const val: number = Number(value) + "" === "NaN" ? 0 : Number(Number(value).toFixed(2));
    if (val > 100) {
        ElNotify({ type: "warning", message: "自动结转比例不能超过100%哦~" });
        editVoucherTableData.value.splice(currentClickRowIndex.value, 1, {
            ...editVoucherTableData.value[currentClickRowIndex.value],
            valueRate: 0,
        });
        nextTick().then(() => {
            valueRate.value = "";
        });
        return;
    } else if (val < 0) {
        ElNotify({ type: "warning", message: "自动结转比例不能输入负数哦~" });
        editVoucherTableData.value.splice(currentClickRowIndex.value, 1, {
            ...editVoucherTableData.value[currentClickRowIndex.value],
            valueRate: 0,
        });
        nextTick().then(() => {
            valueRate.value = "";
        });
        return;
    }
    editVoucherTableData.value.splice(currentClickRowIndex.value, 1, {
        ...editVoucherTableData.value[currentClickRowIndex.value],
        valueRate: val,
    });
    tableRef.value?.initEditData(editVoucherTableData.value);
    nextTick().then(() => {
        valueRate.value = Number(value).toFixed(2);
    });
};
const calculationData = ref<ICalculation[]>([]);
const subjectAddInfo = reactive({
    asubId: "",
    operator: 1, // 运算符号
    valueType: 1, // 取数规则
    valueRate: 100,
    vcId: 0,
});
const resetSubjectAddInfo = () => {
    subjectAddInfo.asubId = "";
    subjectAddInfo.operator = 1;
    subjectAddInfo.valueType = 1;
    subjectAddInfo.valueRate = 100;
    subjectAddInfo.vcId = 0;
};
const addSubject = () => {
    if (subjectAddInfo.asubId === "" || Number(subjectAddInfo.asubId) === 0 || subjectAddInfo.asubId === "0") {
        ElNotify({ type: "warning", message: "请选择科目" });
        return;
    }
    if (calculationData.value.findIndex((item) => item.asubId + "" == subjectAddInfo.asubId) !== -1) {
        ElNotify({ type: "warning", message: "不能重复添加科目" });
        return;
    }
    const item = subjectList.value.find((item) => item.asubId + "" == subjectAddInfo.asubId) ?? {
        asubId: 0,
        asubCode: "",
        asubName: "",
        asubAAName: "",
    };
    const newRowData = {
        asubId: Number(item.asubId) ?? 0,
        asubCode: item.asubCode,
        asubName: item.asubName,
        operator: subjectAddInfo.operator,
        valueType: subjectAddInfo.valueType,
        valueRate: subjectAddInfo.valueRate,
        vcId: subjectAddInfo.vcId,
    };
    calculationData.value.push(newRowData);
    calculationData.value = calculationData.value.map((item, index) => {
        return {
            ...item,
            index: index,
        };
    });
    if (currentClickRowIndex.value === -1) return;
    const currentItem = editVoucherTableData.value.find((item) => item.index === currentClickRowIndex.value);
    if (currentItem) {
        currentItem.calculations = calculationData.value;
    }
    subjectAddInfo.asubId = "";
    tableRef.value?.initEditData(editVoucherTableData.value);
};
const bottomRateIndex = ref(-1);
const bottomRateCanEdit = ref<boolean[]>([]);
const bottomRateBlurStatus = ref<boolean[]>([]);
const handleKeyPress = (e: KeyboardEvent) => {
    const value = (e.target as HTMLInputElement).value;
    if ((e.keyCode < 48 && e.keyCode !== 46 && e.keyCode !== 45) || e.keyCode > 57) {
        e.returnValue = false;
    } else {
        if (value.includes(".") && e.keyCode === 46) {
            e.returnValue = false;
        } else if (value.includes("-") && e.keyCode === 45) {
            e.returnValue = false;
        }
    }
};
const handleValueRateChangeBottom = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    calculationData.value[bottomRateIndex.value].valueRate = Number(value);
};
const canHidden = ref(false);
const handleRowclickRateBottom = (row: ICalculation, column: any, event: any) => {
    if (event.target.className.includes("link")) return;
    bottomRateIndex.value = Number(row.index);
    nextTick().then(() => {
        bottomRateCanEdit.value[row.index!] = true;
        bottomRateBlurStatus.value[row.index!] = false;
        nextTick().then(() => {
            const input = document.getElementById("bottomRateInput" + row.index) as HTMLInputElement;
            if (input && event.target.parentNode.className.includes("edit-item")) input.focus();
        });
    });
};
const handleBottomRateFocus = (index: number) => {
    bottomRateBlurStatus.value[index] = true;
    nextTick().then(() => {
        canHidden.value = false;
    });
};
const handleBottonRateBlur = (event: Event, index: number) => {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    calculationData.value[bottomRateIndex.value].valueRate = Number(value) + "" === "NaN" ? 0 : Number(value);
    canHidden.value = true;
    bottomRateBlurStatus.value[index] = true;
    setTimeout(() => {
        if (canHidden.value) {
            if (bottomRateBlurStatus.value.every((item) => item)) {
                // bottomRateCanEdit.value = bottomRateCanEdit.value.map(() => false);
            }
        }
    }, 100);
};

function handleCheckRateCanEdit(evt: any) {
    if (
        !evt.target.className.includes("cell") &&
        !evt.target.parentNode.className?.includes("cell") &&
        !evt.target.parentNode.className?.includes("edit-item")
    ) {
        bottomRateCanEdit.value = bottomRateCanEdit.value.map(() => false);
    }
}

const deleteSubject = (asubId: number) => {
    if (currentClickRowIndex.value === -1) return;
    calculationData.value = calculationData.value?.filter((item) => item.asubId !== asubId);
    const currentItem: IEditVoucherTableData = JSON.parse(JSON.stringify(editVoucherTableData.value[currentClickRowIndex.value]));
    currentItem.calculations = calculationData.value;
    editVoucherTableData.value.splice(currentClickRowIndex.value, 1, currentItem);
    tableRef.value?.initEditData(editVoucherTableData.value);
};
const tableFocus = () => {
    if (currentClickRowIndex.value === -1) return;
    if (currentClickRowIndex.value === editVoucherTableData.value.length - 1) {
        editVoucherTableData.value.push({
            ...JSON.parse(JSON.stringify(tableRowAddData)),
            index: editVoucherTableData.value.length,
        });
    }
};
// 科目下拉框筛选函数
const newSubjectList = ref<Array<any>>([]);
const showVoucherGroupList = ref<Array<any>>([]);
const subjectListAll = ref<Array<any>>([]);
const showSubjectList = ref<Array<any>>([]);

watchEffect(() => {
    subjectList.value = accountSubjectListWithoutDisabled.value.map((item: any) => {
        return { ...item, asubId: item.asubId + "" };
    });
    subjectListAll.value = subjectList.value.map((item) => {
        return {
            ...item,
            label: item.asubCode + item.asubAAName,
        }
    });
    newSubjectList.value = JSON.parse(JSON.stringify(subjectListAll.value));

    showVoucherGroupList.value = JSON.parse(JSON.stringify(voucherGroupList.value));
    showSubjectList.value = JSON.parse(JSON.stringify(subjectListAll.value));
});
const subjectListFilter = (val: string) => {
    newSubjectList.value = commonFilterMethod(val, subjectListAll.value, 'label');
};
function voucherGroupFilterMethod(value: string) {
    showVoucherGroupList.value = commonFilterMethod(value, voucherGroupList.value, 'name');
}
function subjectFilterMethod(value: string) {
    showSubjectList.value = commonFilterMethod(value, subjectListAll.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.voucher-template-box {
    .vouher-template-detial {
        color: #404040;
        font-size: 12px;
        text-align: left;

        .voucher-template-top {
            padding: 14px 24px 10px;
            height: 30px;
            .detail-el-select(85px);
            :deep(.el-select) {
                .el-input__wrapper {
                    padding: 0 6px 0 8px;
                }
            }

            > input.float-l {
                .detail-original-input(180px, 30px);
            }

            .float-r {
                :deep(.el-input__wrapper) {
                    height: 28px;
                }
            }
            .txt {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                margin-right: 2px;
            }
        }

        .voucher-template-main {
            padding: 0 24px;
            padding-bottom: 12px;
            :deep(.row-click-show-others) {
                .cell {
                    width: 100%;
                    height: 100%;
                    line-height: 36px;
                    padding: 0;

                    > div {
                        padding: 0 4px;
                        box-sizing: border-box;
                        position: relative;

                        .click-show-item {
                            position: absolute;
                            top: 3px;
                            left: 4px;
                            width: calc(100% - 8px);
                            height: calc(100% - 6px);
                            display: flex;
                            align-items: center;

                            & > input {
                                .detail-original-input(100%, 100%);
                            }

                            .el-select {
                                width: 100%;
                                height: 100%;

                                .select-trigger {
                                    line-height: 28px;

                                    .el-input {
                                        height: 30px;
                                        .el-input__inner {
                                            border: none;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            :deep(.el-table) {
                .el-scrollbar__bar.is-vertical {
                    display: block !important;
                }
            }
        }

        .balance-panel {
            padding: 0 24px;
            .balance-panel-title {
                font-size: var(--h3);
                color: var(--font-color);
                line-height: 22px;
                font-weight: 500;
                padding: 12px;
                border-bottom: 1px solid #edefee;
                background-color: #f8f8f8;
                border-radius: 4px 4px 0 0;
                overflow-wrap: break-word;
                word-break: break-all;
            }

            .balance-value-rate-container {
                padding-right: 20px;
                padding-top: 40px;
                padding-bottom: 40px;
                text-align: center;
                font-size: 0;
                background-color: #f8f8f8;
                border-radius: 0 0 4px 4px;

                span {
                    font-size: var(--font-size);
                }

                > input {
                    .detail-original-input(140px, 28px);

                    .detail-spin-button();
                }

                &.erp {
                    background-color: var(--table-title-color);
                }
            }

            .balance-panel-tips {
                padding-left: 220px;
                padding-top: 38px;
                padding-bottom: 27px;
                color: #777777;
            }

            .voucher-line-equation-top {
                height: 50px;
                padding: 0 12px;
                background-color: #f8f8f8;

                > .asub-name {
                    height: 28px;
                    width: 165px;
                    margin-top: 11px;
                    position: relative;

                    :deep(.el-select) {
                        width: 100% !important;
                        height: 100% !important;
                    }

                    :deep(.asub-img) {
                        top: 5px;
                        right: 5px;
                    }

                    :deep(.el-tabs__content) {
                        min-height: 302px !important;
                    }
                }

                .txt {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 50px;
                    margin-right: 2px;
                }

                .operator {
                    .detail-el-select(75px, 28px);
                    margin-top: 11px;
                }

                .balance {
                    .detail-el-select(105px, 28px);
                    margin-top: 11px;
                }
            }

            .voucher-line-equation-main {
                padding: 10px;
                padding-top: 0;
                background-color: #f8f8f8;
                :deep(.el-table) {
                    .el-scrollbar__view {
                        height: 111px;
                        background-color: #f8f8f8;
                    }
                    .el-table__body {
                        .el-table__cell {
                            .cell {
                                box-sizing: border-box;
                                height: 100%;
                                position: relative;
                                line-height: 36px;
                                cursor: pointer;
                                .edit-item {
                                    position: absolute;
                                    width: calc(100% - 8px);
                                    height: calc(100% - 4px);
                                    top: 2px;
                                    left: 4px;
                                    display: flex;
                                    align-items: center;
                                    input {
                                        .detail-original-input(85px, 100%);
                                    }
                                    span {
                                        margin-left: 3px;
                                        flex: 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .custom-precheck-btns {
            padding: 10px 0;
            margin-top: 12px;
            text-align: center;
            border-top: 1px solid var(--border-color);
        }
    }
    &.erp {
        .vouher-template-detial {
            .balance-panel {
                .voucher-line-equation-main {
                    :deep(.el-table) {
                        border: none;
                        box-shadow: 0 0 1px gray;
                        .el-scrollbar__view {
                            background-color: var(--table-title-color);
                        }
                    }
                }
                .balance-panel-title,
                .voucher-line-equation-top,
                .voucher-line-equation-main {
                    background-color: var(--table-title-color);
                }
            }
        }

        .custom-precheck-btns {
            border-top: 1px solid var(--border-color);
            padding-right: 20px;
            text-align: right;
            margin-top: 0px;
        }
    }
}
</style>
<style lang="less">
// 兼容业财
body[erp] {
    .voucher-template-box
        .vouher-template-detial
        .voucher-template-main
        .row-click-show-others
        .cell
        > div
        .click-show-item
        .el-select
        .select-trigger
        .el-input {
        height: 37px;
    }
    .balance-panel {
        .voucher-line-equation-main {
            .el-table__body-wrapper {
                tbody tr {
                    background-color: var(--table-title-color);
                }
            }
        }
    }
}
</style>
