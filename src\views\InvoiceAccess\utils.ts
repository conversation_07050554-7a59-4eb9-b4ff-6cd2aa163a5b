// 格式化份数显示
export const formatCount = (count: number | string) => {
  if (count === undefined || count === null || count === "") {
    return "0" // 份数没值显示0
  }
  return count
}

// 格式化金额显示
export const formatAmount = (row: any) => {
  // 海关进口增值税缴款书特殊处理
  if (row.deductText === "海关进口增值税缴款书") {
    return "--" // 金额显示--
  }

  // 其他项目处理
  if (row.amount === undefined || row.amount === null || row.amount === "") {
    return "0.00" // 无值显示0.00
  }

  if (Number(row.amount) === 0) {
    return "0.00"
  }

  // 格式化金额
  return Number(row.amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

// 格式化税额显示
export const formatTax = (row: any) => {
  // 其他项目处理
  if (row.tax === undefined || row.tax === null || row.tax === "") {
    return "0.00" // 无值显示0.00
  }

  if (Number(row.tax) === 0) {
    return "0.00"
  }

  // 格式化税额
  return Number(row.tax).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}
