// 格式化份数显示
export const formatCount = (count: number | string) => {
  if (count === undefined || count === null || count === "") {
    return "0" // 份数没值显示0
  }
  return count
}

// 格式化金额显示
export const formatValue = (value: string | number) => {
  // 其他项目处理
  if (value === undefined || value === null || value === "") {
    return "0.00" // 无值显示0.00
  }

  if (Number(value) === 0) {
    return "0.00"
  }

  // 格式化金额
  return Number(value).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}

// 季度配置类型定义
interface QuarterConfig {
  quarter: number
  months: number[]
  startMonth: number
  endMonth: number
  endDay: number
  label?: string
}

// 季度配置：定义每个季度的月份范围和结束日期
const QUARTER_CONFIG: QuarterConfig[] = [
  { quarter: 1, months: [1, 2, 3], startMonth: 1, endMonth: 3, endDay: 31, label: "第一季度" },
  { quarter: 2, months: [4, 5, 6], startMonth: 4, endMonth: 6, endDay: 30, label: "第二季度" },
  { quarter: 3, months: [7, 8, 9], startMonth: 7, endMonth: 9, endDay: 30, label: "第三季度" },
  { quarter: 4, months: [10, 11, 12], startMonth: 10, endMonth: 12, endDay: 31, label: "第四季度" },
]

// 季度信息返回类型
export interface QuarterInfo {
  year: number
  quarter: number
  startDate: string
  endDate: string
  label: string
  config: QuarterConfig
}

// 解析 YYYY-MM 格式的日期字符串
const parseDateString = (dateString: string): { year: number; month: number } => {
  const match = dateString.match(/^(\d{4})-(\d{1,2})$/)
  if (!match) {
    throw new Error("日期格式必须为 YYYY-MM，例如：2024-03")
  }

  const year = parseInt(match[1], 10)
  const month = parseInt(match[2], 10)

  if (month < 1 || month > 12) {
    throw new Error("月份必须在1-12之间")
  }

  return { year, month }
}

// 根据月份获取季度配置
const getQuarterConfig = (month: number) => {
  if (month < 1 || month > 12) {
    throw new Error("月份必须在1-12之间")
  }

  return QUARTER_CONFIG.find((config) => config.months.includes(month))!
}

// 根据 YYYY-MM 格式返回对应的季度起始日
export const getQuarterStartDate = (dateString: string): string => {
  const { year, month } = parseDateString(dateString)
  const config = getQuarterConfig(month)

  // 格式化为 YYYY-MM-DD 格式
  const formattedMonth = config.startMonth.toString().padStart(2, "0")
  return `${year}-${formattedMonth}-01`
}

// 根据 YYYY-MM 格式返回对应的季度结束日
export const getQuarterEndDate = (dateString: string): string => {
  const { year, month } = parseDateString(dateString)
  const config = getQuarterConfig(month)

  // 格式化为 YYYY-MM-DD 格式
  const formattedMonth = config.endMonth.toString().padStart(2, "0")
  const formattedDay = config.endDay.toString().padStart(2, "0")
  return `${year}-${formattedMonth}-${formattedDay}`
}

// 根据 YYYY-MM 格式获取季度信息
export const getQuarterInfo = (dateString: string): QuarterInfo => {
  const { year, month } = parseDateString(dateString)
  const config = getQuarterConfig(month)

  return {
    year,
    quarter: config.quarter,
    startDate: getQuarterStartDate(dateString),
    endDate: getQuarterEndDate(dateString),
    label: `${year}年${config.label}`,
    config,
  }
}

// 根据日期字符串获取季度起始日（支持 YYYY-MM-DD 格式，转换为 YYYY-MM）
export const getQuarterStartDateFromString = (dateString: string): string => {
  const date = new Date(dateString)

  if (isNaN(date.getTime())) {
    throw new Error("无效的日期格式")
  }

  const year = date.getFullYear()
  const month = date.getMonth() + 1 // getMonth() 返回 0-11，需要 +1
  const formattedDateString = `${year}-${month.toString().padStart(2, "0")}`

  return getQuarterStartDate(formattedDateString)
}

// 根据日期字符串获取季度结束日（支持 YYYY-MM-DD 格式，转换为 YYYY-MM）
export const getQuarterEndDateFromString = (dateString: string): string => {
  const date = new Date(dateString)

  if (isNaN(date.getTime())) {
    throw new Error("无效的日期格式")
  }

  const year = date.getFullYear()
  const month = date.getMonth() + 1 // getMonth() 返回 0-11，需要 +1
  const formattedDateString = `${year}-${month.toString().padStart(2, "0")}`

  return getQuarterEndDate(formattedDateString)
}

// 根据日期字符串获取完整季度信息（支持 YYYY-MM-DD 格式，转换为 YYYY-MM）
export const getQuarterInfoFromString = (dateString: string): QuarterInfo => {
  const date = new Date(dateString)

  if (isNaN(date.getTime())) {
    throw new Error("无效的日期格式")
  }

  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const formattedDateString = `${year}-${month.toString().padStart(2, "0")}`

  return getQuarterInfo(formattedDateString)
}

// 获取所有季度配置
export const getAllQuarterConfigs = (): QuarterConfig[] => {
  return [...QUARTER_CONFIG]
}

// 根据季度号获取配置
export const getQuarterConfigByNumber = (quarter: number): QuarterConfig => {
  if (quarter < 1 || quarter > 4) {
    throw new Error("季度必须在1-4之间")
  }

  const config = QUARTER_CONFIG.find((config) => config.quarter === quarter)
  if (!config) {
    throw new Error(`未找到第${quarter}季度的配置`)
  }

  return config
}

// 判断两个日期是否在同一季度
export const isSameQuarter = (date1: string, date2: string): boolean => {
  try {
    const quarter1 = getQuarterInfoFromString(date1)
    const quarter2 = getQuarterInfoFromString(date2)

    return quarter1.year === quarter2.year && quarter1.quarter === quarter2.quarter
  } catch {
    return false
  }
}
