<template>
    <Table
        ref="AccountSetTable"
        :maxHeight="320"
        :data="tableData"
        :columns="tableColumns"
        :scrollbarShow="true"
        :page-is-show="false"
        :showOverflowTooltip="true"
    >
        <template #import_as_name>
            <el-table-column label="账套名称" width="510" align="left" headerAlign="left" :show-overflow-tooltip="false" :resizable="false">
                <template #default="scope">
                    {{ scope.row.import_as_name }}
                </template>
            </el-table-column>
        </template>
        <template #status>
            <el-table-column
                label="状态"
                min-width="210"
                align="left"
                headerAlign="center"
                :resizable="false"
                :show-overflow-tooltip="false"
            >
                <template #default="scope">
                    <div v-if="scope.row.status === 0" class="status-box">
                        <img src="@/assets/Settings/loading-icon.png" />
                        <!-- 正在导入... -->
                        在线导账中{{ getProgressRatio(scope.row.progress) }}...
                        {{ getProgress(scope.row.group_id, scope.row.import_as_id, scope.row.status) }}
                    </div>
                    <div v-if="scope.row.status === 1" class="status-box">
                        <img src="@/assets/Settings/correct-icon.png" />
                        导账成功
                        {{ gotoNewAccountSet(scope.row.as_id, scope.row.appAsId) }}
                    </div>
                    <div v-if="scope.row.status === 2" class="status-box">
                        <img src="@/assets/Settings/warnning-yellow.png" />
                        部分导入
                        <a
                            v-if="scope.row.error_msg !== ''"
                            class="link pop-trigger"
                            @click.prevent="showErrorMsg(scope.row)"
                        >
                            点击查看原因
                        </a>
                        {{ gotoNewAccountSet(scope.row.as_id, scope.row.appAsId) }}
                    </div>
                    <div v-if="scope.row.status === 3" class="status-box">
                        <img src="@/assets/Settings/warn-red.png" />
                        导账失败
                        <el-popover placement="right" :width="170" trigger="hover" :popper-class="'import-status-list-popper'">
                            <template #reference>
                                <a class="link pop-trigger">查看原因</a>
                            </template>
                            <div class="pop-content">
                                <!-- 导账失败：您的账套数量， 已超过已购买账套数量。 -->
                                {{ getErrorMsg(scope.row.error_message) }}
                                <div class="pop-tips" style="color: #fa7c27; margin-top: 10px">可以联系客服帮您看看哦~</div>
                            </div>
                        </el-popover>
                    </div>
                </template>
            </el-table-column>
        </template>
    </Table>
    <div class="tips">
        <img
            src="@/assets/Settings/tips.png"
            alt=""
            style="width: 14px; margin-right: 5px"
        />您如果不小心关闭页面，可以在菜单“设置-旧账导入”进入当前页面
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { setTopLocationhref } from "@/util/url";

import { getCookie } from "@/util/cookie";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAccountSetStore } from "@/store/modules/accountSet";
import type { ImportModeType, IImportListItem, IGetProgress, IErrorReasonParams } from "../types";
import { getErrorMsg } from "../utils";
const accountsetStore = useAccountSetStore();
const asId = accountsetStore.accountSet?.asId || 0;

const isAccountingAgent = window.isAccountingAgent;

const props = withDefaults(
    defineProps<{
        statusList: IImportListItem[];
    }>(),
    {
        statusList: () => [],
    }
);

const emits = defineEmits<{
    (e: "changeImportMode", val: ImportModeType, paramsData?: IErrorReasonParams): void;
    (e: "importSuccess", val: string): void;
}>();

const tableData = ref<IImportListItem[]>(props.statusList);
const tableColumns: IColumnProps[] = [
    {
        slot: "import_as_name",
    },
    {
        slot: "status",
    },
];

// 获取某一行进度
const waiting = ref(false);
const getProgress = (groupid: string, importasid: string, status: number) => {
    if (status !== 0 || waiting.value) return;
    waiting.value = true;
    request({
        url: `${window.importFromOther}/api/ImportFromYun/GetProgress?groupid=${groupid}&importasid=${importasid}`,
        method: "GET",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    })
        .then((res: IResponseModel<IGetProgress>) => {
            if (res.state === 1000) {
                const index = tableData.value.findIndex((item) => item.import_as_id === importasid && item.group_id === groupid);
                if (index !== -1) {
                    tableData.value[index] = res.data.data;
                }
            }
        })
        .finally(() => {
            // 20s查询一次
            setTimeout(() => (waiting.value = false), 20000);
        });
};

// 进度格式转换
const getProgressRatio = computed(() => (progress: string) => {
    if (progress.length > 0) {
        return JSON.parse(progress).ProgressRatio;
    }
});
// 查看错误信息
const showErrorMsg = (row: IImportListItem) => {
    const { appAsId, error_message, import_as_name} = row;    
    const errMsgData={
        appAsId,
        asName:import_as_name,
        errorMsgList:JSON.parse(error_message)
    }
    emits("changeImportMode", "statusError", errMsgData);
};

// 是否有导入成功的账套
const hasSuccess = computed(() => tableData.value.some((item) => item.status === 1));

// 跳转新账套
const gotoNewAccountSet = (as_id: number, appAsId: string) => {
    if (!isAccountingAgent || !as_id || !appAsId || asId !== as_id) return;
    setTopLocationhref(`/Default/Default?appasid=${appAsId}`);
};

watch(
    () => hasSuccess.value,
    (val) => {
        if (val) {
            const appAsId = tableData.value.find((item) => item.status === 1)?.appAsId || "";
            emits("importSuccess", appAsId);
        }
    }
);
</script>
<style lang="less" scoped>
.status-box {
    display: flex;
    align-items: center;
    img {
        width: 18px;
        margin: 5px;
    }
    .pop-trigger {
        margin-left: 5px;
    }
}

.tips {
    margin-top: 15px;
    display: flex;
    align-items: center;
    text-align: left;
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    color: #333;
}
</style>
