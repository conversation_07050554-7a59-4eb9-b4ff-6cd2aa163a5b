<template>
    <div class="content">
        <div class="title">财务概要信息表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef"></PaginationPeriodPicker>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['financialsummary-canprint']">
                        <li @click="handlePrint(0, { pid: searchInfo.pid })">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button ml-10" @click="handleExport" v-permission="['financialsummary-canexport']">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <FinancialTable
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :tableData="tableData"
                    :empty-text="emptyText"></FinancialTable>
            </div>
        </div>
    </div>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="财务概要信息打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3, { pid: searchInfo.pid })"></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "FinancialSummary",
};
</script>
<script setup lang="ts">
import Dropdown from "@/components/Dropdown/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import FinancialTable from "./components/FinancialTable.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { globalPrint, globalExport } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { reactive, ref, watch } from "vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import usePrint from "@/hooks/usePrint";
import { useRoute } from "vue-router";
import type { IFinancialItem } from "./types";
import RefreshButton from "@/components/RefreshButton/index.vue";

const route = useRoute();
const periodStore = useAccountPeriodStore();
const searchInfo = reactive({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    classification: false,
});
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
let loading = ref(false);
let emptyText = ref("");
function handleSearch() {
    loading.value = true;
    emptyText.value = " ";
    // 财务概要信息
    request({
        url: `/api/FinancialSummary`,
        params: { PId: searchInfo.pid },
        method: "get",
    })
        .then((res: IResponseModel<IFinancialItem[]>) => {
            loading.value = false;
            tableData.value = [];

            if (res.state === 1000) {
                tableData.value = res.data;
            } else {
                // res.massage
                console.log(res.msg);
            }
            if (!tableData.value.length) {
                emptyText.value = "暂无数据";
            }
        })
        .catch((error) => {
            tableData.value = [];
            console.log(error);
        });
}

//定义表格数据
const tableData = ref<IFinancialItem[]>([]);

//打印

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint("financialSummary", `/api/FinancialSummary/Print`, {}, false, false);
//导出
function handleExport() {
    globalExport(`/api/FinancialSummary/Export?PId=${searchInfo.pid}`);
}

watch(searchInfo, handleSearch, { immediate: true });
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
</style>
