import { useAccountPeriodStoreHook } from "@/store/modules/accountPeriod";
const periodStore = useAccountPeriodStoreHook();
import type { IPeriod } from "@/api/period";

export const columnRelation = [
    { dateType: 1, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 2, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 3, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 4, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 5, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 6, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 7, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 8, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 9, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 10, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 11, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 12, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 13, dataType: [1, 2, 3, 4, 5, 6, 7] },
    { dateType: 14, dataType: [5, 6, 7] },
    { dateType: 15, dataType: [5, 6, 7] },
    { dateType: 16, dataType: [5, 6, 7] },
    { dateType: 17, dataType: [5, 6, 7] },
    { dateType: 18, dataType: [5, 6, 7] },
    { dateType: 19, dataType: [5, 6, 7, 8] },
];

export const columnDateTypeEnum: { [key: number]: string } = {
    1: "1月",
    2: "2月",
    3: "3月",
    4: "4月",
    5: "5月",
    6: "6月",
    7: "7月",
    8: "8月",
    9: "9月",
    10: "10月",
    11: "11月",
    12: "12月",
    13: "本月",
    14: "一季度",
    15: "二季度",
    16: "三季度",
    17: "四季度",
    18: "本季度",
    19: "本年",
};

export const columnDataTypeEnum: { [key: number]: string } = {
    1: "期初余额",
    2: "期末余额",
    3: "借方余额",
    4: "贷方余额",
    5: "借方发生额",
    6: "贷方发生额",
    7: "损益实际发生额",
    8: "年初余额",
};
export const equationOperatorEnum = {
    addition: 1,
    subtraction: 2,
    multiplication: 3,
    division: 4,
};
export const equationValueTypeEnum = {
    accountSubject: 1,
    cell: 2,
    statement: 3,
};
export function convertToColumnName(i: number) {
    var str = "";
    while (i > 26) {
        str = String.fromCharCode((i % 26 || 26) + 64) + str;
        i = Math.trunc(i / 26) - (i % 26 === 0 ? 1 : 0);
    }
    str = String.fromCharCode(i + 64) + str;
    return str;
}
export function convertToNumber(columnName: string) {
    let str = columnName.toUpperCase();
    let number = 0;

    for (let i = 0; i < str.length; i++) {
        number = number * 26 + (str.charCodeAt(i) - 64);
    }
    return number;
}
export function splitCellName(str: string) {
    const matches = str.match(/^([A-Za-z]+)(\d+)$/);
    if (matches === null) {
        return null;
    }
    const columnNumber = convertToNumber(matches[1]); // 匹配到的字母部分
    const rowNumber = parseInt(matches[2], 10); // 匹配到的数字部分，转换为整数
    return {
        columnNumber,
        rowNumber,
    };
}
export const quarterEnum: { [key: number]: string } = {
    5: "本",
    1: "一",
    2: "二",
    3: "三",
    4: "四",
};
export function getPeriodList() {
    const periodsList = periodStore.periodList;

    const monPeriodList = periodsList
        .map((item: IPeriod) => ({
            ...item,
            label: `${item.year}年${item.sn}月`,
        }))
        .reverse();

    const quarterPeriodList = [];
    const yearList = new Set();

    for (let i = periodsList.length - 1; i >= 0; i--) {
        const item = periodsList[i];

        if (item.sn % 3 === 0 || i === periodsList.length - 1) {
            quarterPeriodList.push({
                ...item,
                label: `${item.year}年第${Math.floor(item.sn % 3 === 0 ? item.sn / 3 : item.sn / 3 + 1)}季度`,
            });
        }

        if (!yearList.has(item.year)) {
            yearList.add(item.year);
        }
    }

    const yearPeriodList = Array.from(yearList)
        .sort((a: any, b: any) => b - a)
        .map((year: any) => ({
            pid: year,
            label: `${year}年`,
        }));

    return { monPeriodList, quarterPeriodList, yearPeriodList };
}

export const dateTypeOption = [
    { value: 1, label: "1月" },
    { value: 2, label: "2月" },
    { value: 3, label: "3月" },
    { value: 4, label: "4月" },
    { value: 5, label: "5月" },
    { value: 6, label: "6月" },
    { value: 7, label: "7月" },
    { value: 8, label: "8月" },
    { value: 9, label: "9月" },
    { value: 10, label: "10月" },
    { value: 11, label: "11月" },
    { value: 12, label: "12月" },
    { value: 13, label: "本月" },
    { value: 14, label: "一季度" },
    { value: 15, label: "二季度" },
    { value: 16, label: "三季度" },
    { value: 17, label: "四季度" },
    { value: 18, label: "本季度" },
    { value: 19, label: "本年" },    
];
export const dataTypeOption = [
    {
        value: 1,
        label: "期初余额",
    },
    {
        value: 2,
        label: "期末余额",
    },
    {
        value: 3,
        label: "借方余额",
    },
    {
        value: 4,
        label: "贷方余额",
    },
    {
        value: 5,
        label: "借方发生额",
    },
    {
        value: 6,
        label: "贷方发生额",
    },
    {
        value: 7,
        label: "损益实际发生额",
    },
    {
        value: 8,
        label: "年初余额",
    },
];
