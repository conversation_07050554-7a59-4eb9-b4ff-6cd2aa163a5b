{
    "extends": "@vue/tsconfig/tsconfig.web.json",
    "include": [
        "env.d.ts",
        "src/**/*",
        "src/**/**/*",
        "src/*.vue",
        "src/**/*.vue",
        "src/**/*.ts",
        "public/*",
        "types/**/*.d.ts"
    ],
    "compilerOptions": {
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"]
        },
        "isolatedModules": true,
        "types": ["element-plus/global"],
        "ignoreDeprecations": "5.0",
        "forceConsistentCasingInFileNames": false,
        /** TS 严格模式 */
        "strict": true,
        "lib": ["esnext", "dom"]
    },
    "references": [
        {
            "path": "./tsconfig.config.json"
        }
    ]
}
