export interface ISearchParams {
    period_s: number;
    period_e: number;
    sbj_leval_s: number;
    sbj_leval_e: number;
    fcid: number;
    showNumber: number;
    showPeriod: number;
    showYear: number;
}

export interface IRows {
    asub_code: string;
    asub_name: string;
    credit: number;
    debit: number;
    initial_credit: number;
    initial_debit: number;
    month: number;
    total_credit: number;
    total_debit: number;
    year: number;
    year_credit: number;
    year_debit: number;
}
export interface ItableData {
    total: number;
    rows: Array<IRows>;
}
// export interface ICellStyleData {
//     column: number;
//     columnIndex: number;
//     row:IRows;
//     rowIndex:number
// }

export interface IFcList {
    asId: number;
    code: string;
    id: number;
    isBaseCurrency: boolean;
    name: string;
    preName: string;
    rate: number;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}
export interface IAsubCodeLength {
    asid:number;
    codeLength:Array<number>;
    firstAsubLength:number;
    firstCodeLength:number;
    forthAsubLength:number;
    forthCodeLength:number;
    preName:string;
    secondAsubLength:number;
    secondCodeLength:number;
    thirdAsubLength:number;
    thirdCodeLength:number;
}