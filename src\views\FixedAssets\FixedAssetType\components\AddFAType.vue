<template>
    <div
        class="slot-mini-content"
        style="width: 1000px"
        :style="isErp ? '' : 'margin-top: 32px; border: 1px solid var(--slot-title-color);'"
    >
        <div class="edit-div" :class="isErp ? 'erp' : ''">
            <div class="title" id="SpanEdit">{{ title === "new" ? "新增资产类别" : "编辑资产类别" }}</div>
            <div class="add-content">
                <el-form :model="form" ref="formRef" :rules="rules" :inline-message="true" label-width="auto" style="width: 1000px">
                    <div class="isRow">
                        <el-form-item class="left" label="资产类别编码：" prop="fa_type_num">
                            <el-input
                                ref="inputmRef1"
                                autocomplete="on"
                                style="width: 180px"
                                v-model="formData.fa_type_num"
                                class="input-item inputmRef1"
                                @blur="invaildfixedTypeNum"
                                @keydown.enter="nextFocus(2)"
                            ></el-input>
                        </el-form-item>
                        <el-form-item style="margin-left: 55px" label="资产类别名称：" prop="fa_type">
                            <Tooltip :content="formData.fa_type" :is-input="true" placement="right">
                                <el-input
                                ref="inputmRef2"
                                autocomplete="on"
                                style="width: 180px"
                                v-model="formData.fa_type"
                                class="input-item inputmRef2"
                                @input="checkFAType"
                                @keydown.enter="nextFocus(3)"
                            />
                            </Tooltip>
                        </el-form-item>
                    </div>
                    <div class="isRow">
                        <el-form-item class="left" label="资产属性：" prop="fa_property">
                            <div style="width: 180px">
                                <Select
                                    ref="inputmRef6"
                                    class="inputmRef6"
                                    :fitInputWidth="true"
                                    v-model="formData.fa_property"
                                    :teleported="true"
                                    @keydown.enter="nextFocus(7)"
                                    :filterable="true"
                                    :filter-method="faPropertyFilterMethod"
                                >
                                    <Option 
                                        v-for="item in showfaPropertyList" 
                                        :key="item.value" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </Select>
                            </div>
                        </el-form-item>
                        <el-form-item class="left" style="margin-left: 55px" :label="formData.fa_property === 0 ? '折旧方法：':'摊销方法：'" prop="depreciation_type">
                            <el-select
                                ref="inputmRef3"
                                style="width: 180px"
                                v-model="formData.depreciation_type"
                                class="inputmRef3"
                                @keydown.enter="nextFocus(4)"
                                :filterable="true"
                                :filter-method="depreciationTypeFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showDepreciationTypeList" 
                                    :label="item.label" 
                                    :value="Number(item.value)" 
                                    :key="item.value" 
                                />
                            </el-select>
                        </el-form-item>
                    </div>

                    <div class="isRow">
                        <el-form-item class="left" label="使用月份：" prop="monthes">
                            <el-input
                                ref="inputmRef4"
                                autocomplete="on"
                                style="width: 180px"
                                v-model="formData.monthes"
                                class="input-item inputmRef4"
                                @keydown.enter="nextFocus(5)"
                            />
                        </el-form-item>
                        <el-form-item class="left" style="margin-left: 55px" label="预计净残值率：" prop="netsalvage_rate">
                            <el-input
                                ref="inputmRef5"
                                autocomplete="on"
                                style="width: 180px"
                                v-model="formData.netsalvage_rate"
                                class="input-item inputmRef5"
                                @keydown.enter="nextFocus(6)"
                            >
                                <template #suffix> <span style="color: black">%</span></template>
                            </el-input>
                        </el-form-item>
                    </div>
                    <div class="isRow">
                        <el-form-item class="left" label="资产科目：" prop="fa_asub" :style="formData.fa_property === 2 || [3,4,5].includes(accountingStandard) ? 'width:656px':''">
                            <div style="width: 180px">
                                <Select
                                    ref="inputmRef6"
                                    class="inputmRef6"
                                    :fitInputWidth="true"
                                    v-model="formData.fa_asub"
                                    :teleported="true"
                                    @keydown.enter="nextFocus(7)"
                                    :filterable="true"
                                    :filter-method="faAsubFilterMethod"
                                >
                                    <Option 
                                        v-for="item in showfaAsubList" 
                                        :key="item.value" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </Select>
                            </div>
                        </el-form-item>
                        <el-form-item
                            style="margin-left: 55px"
                            v-show="formData.fa_property !== 2 && ![3,4,5].includes(accountingStandard)"
                            :label="formData.fa_property === 0 ? '累计折旧科目：' : '累计摊销科目：'"
                            prop="depreciation_asub"
                        >
                            <div style="width: 180px">
                                <Select
                                    ref="inputmRef7"
                                    class="inputmRef7"
                                    :fitInputWidth="true"
                                    v-model="formData.depreciation_asub"
                                    :teleported="true"
                                    @keydown.enter="nextFocus(8)"
                                    :filterable="true"
                                    :filter-method="deAsubFilterMethod"
                                >
                                    <Option 
                                        v-for="item in showdepreciationList" 
                                        :key="item.value" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </Select>
                            </div>
                        </el-form-item>
                    </div>
                    <div class="isRow">
                        <el-form-item label="备注：" prop="note">
                            <Tooltip :content="formData.note" :is-input="true" placement="right" :teleported='true'>
                            <el-input
                                ref="inputmRef8"
                                autocomplete="on"
                                v-model="formData.note"
                                class="input-item inputmRef8"
                                style="width: 535px"
                            />
                        </Tooltip>
                        </el-form-item>
                    </div>
                    <span style="font-size: 14px; color: var(--el-text-color-regular)"
                        >提示：资产类别中设置的科目，在新增卡片时会默认带到卡片上，带入后仍可以修改哦~</span
                    >
                </el-form>
            </div>
            <div class="buttons" style="margin-bottom: 40px; margin-top: 22px">
                <a class="button solid-button mr-10" @click="save(formData)">保存</a>
                <a class="button" @click="cancel">取消</a>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, watchEffect } from "vue";
import type { FormRules } from "element-plus";
import type { IForm, ISelectItem } from "../types";
import Tooltip from "@/components/Tooltip/index.vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { watch } from "vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { depreciationTypeList } from "@/views/FixedAssets/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const isErp = ref(window.isErp);
const formRef = ref<any>(null);
const props = defineProps<{
    form: IForm;
    title: string;
}>();
const emit = defineEmits(["saveData", "cancelData"]);
const formData = computed(() => {
    return props.form;
});
const accountingStandard = ref(0);
const accountsetStore = useAccountSetStore();
accountingStandard.value = Number(accountsetStore.accountSet?.accountingStandard);
const rules = reactive<FormRules>({
    fa_type_num: [{ required: true, message: "", trigger: "none" }],
    fa_type: [{ required: true, message: "", trigger: "none" }],
    fa_property: [{ required: true, message: "", trigger: "none" }],
    depreciation_type: [{ required: true, message: "", trigger: "none" }],
    monthes: [{ required: true, message: "", trigger: "none" }],
    netsalvage_rate: [{ required: true, message: "", trigger: "none" }],
    fa_asub: [{ required: true, message: "", trigger: "none" }],
    depreciation_asub: [{ required: true, message: "", trigger: "none" }],
});

function invaildfixedTypeNum() {
    if (formData.value.fa_type_num) {
        const reg = /^[0-9a-zA-Z]+$/;
        if (!reg.test(formData.value.fa_type_num)) {
            ElNotify({
                type: "warning",
                message: "编码只能为数字字母组合，请检查后重新尝试",
            });
        }
    }
}

function checkFAType(value: string) {
    if (value.length > 64) {
        ElNotify({
            type: "warning",
            message: "资产类别名称不能超过64个字符，请修改后重试哦~",
        });
    }
    formData.value.fa_type = value.slice(0, 64);
}
const save = (formData: IForm) => {
    emit("saveData", formData);
};

const cancel = () => {
    emit("cancelData");
};
const inputmRef1 = ref();
const inputmRef2 = ref();
const inputmRef3 = ref();
const inputmRef4 = ref();
const inputmRef5 = ref();
const inputmRef6 = ref();
const inputmRef7 = ref();
const inputmRef8 = ref();

const nextFocus = (val: number) => {
    if (val === 2) inputmRef2.value.focus();
    if (val === 3) inputmRef3.value.focus();
    if (val === 4) inputmRef4.value.focus();
    if (val === 5) inputmRef5.value.focus();
    if (val === 6) inputmRef6.value.focus();
    if (val === 7) inputmRef7.value.focus();
    if (val === 8) {
        inputmRef8.value.focus();
        return;
    }
    const inputE = document.querySelector(`.inputmRef${val} .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            nextFocus(val + 1);
        }
    });
};

const handleFocus = () => {
    inputmRef1.value?.focus();
};

//资产属性
const faPropertyList = computed(()=>{
    return [1,2,3,4,5].includes(accountingStandard.value) ? [
        { value: 0, label: "固定资产" },
        { value: 1, label: "无形资产" },
        { value: 2, label: "长期待摊费用" },
    ] : [
        { value: 0, label: "固定资产" },
    ]
})

//根据资产属性获取资产科目
function getFixedAssetsAsubListByProperty() {
    return request({
        url: `/api/FixedAssets/GetFixedAssetsAsubListByProperty`,
        method: "post",
        data: {
            property: formData.value.fa_property,
        },
    });
}

watch(
    () => formData.value.fa_property,
    () => {
        let apiEndpoints: Promise<IResponseModel<any>>[] = [];
        switch (formData.value.fa_property) {
            case 0:
            case 1:
                apiEndpoints = [getFixedAssetsAsubList(), getDeprecationAsubList()];
                break;
            case 2:
                apiEndpoints = [getFixedAssetsAsubList()];
                break;
        }
        Promise.all(apiEndpoints).then((res: any) => {
            faAsubList.value = res[0].data.reduce((prev: ISelectItem[], item: any) => {
                prev.push({
                    value: Number(item.asubId),
                    label: item.asubCode + " " + item.asubName,
                });
                return prev;
            }, []);
            res[1] && (depreciationList.value = res[1].data.reduce((prev: ISelectItem[], item: any) => {
                prev.push({
                    value: Number(item.asubId),
                    label: item.asubCode + " " + item.asubName,
                });
                return prev;
            }, []));
            formData.value.fa_asub = faAsubList.value[0].value;
            formData.value.depreciation_asub = depreciationList.value.length > 0 ? depreciationList.value[0].value : 0;
            if (props.title === "new") {

                switch (formData.value.fa_property) {
                    case 0:
                        formData.value.monthes = "";
                        formData.value.netsalvage_rate = "";
                        break;
                    case 1:
                        formData.value.monthes = "120";
                        formData.value.netsalvage_rate = "0";
                        break;
                    case 2:
                        formData.value.monthes = "36";
                        formData.value.netsalvage_rate = "0";
                        break;
                }
            }
        });
    },
    { immediate: true }
);

//获得资产科目getFixedAssetsAsubList
const faAsubList = ref<ISelectItem[]>([]);
function getFixedAssetsAsubList() {
    let asubUrl = "";
    switch (formData.value.fa_property) {
        case 0:
            asubUrl = `/api/FixedAssets/GetFixedAssetsAsubList`;
            break;
        case 1:
            asubUrl = `/api/FixedAssets/GetAllLeafIntangibleAsubList`;
            break;
        case 2:
            asubUrl = `/api/FixedAssets/GetAllLeafLongTermDeferredExpensesAsubList`;
            break;
    }
    return request({
        url: asubUrl,
        method: "post",
    });
}

//获取累计折旧科目
const depreciationList = ref<ISelectItem[]>([]);
function getDeprecationAsubList() {
    let depreciationUrl = "";
    switch (formData.value.fa_property) {
        case 0:
            depreciationUrl = `/api/FixedAssets/GetDeprecationAsubList`;
            break;
        case 1:
            depreciationUrl = `/api/FixedAssets/GetAllLeafAccumulatedAmortizationAsub`;
            break;
    }
    return request({
        url: depreciationUrl,
        method: "post",
    });
}

function initGetSubject() {
    Promise.all([getFixedAssetsAsubList(), getDeprecationAsubList()]).then((res: any) => {
        faAsubList.value = res[0].data.reduce((prev: ISelectItem[], item: any) => {
            prev.push({
                value: Number(item.asubId),
                label: item.asubCode + " " + item.asubName,
            });
            return prev;
        }, []);
        props.title === "new" && (formData.value.fa_asub = faAsubList.value[0].value);
        depreciationList.value = res[1].data.reduce((prev: ISelectItem[], item: any) => {
            prev.push({
                value: Number(item.asubId),
                label: item.asubCode + " " + item.asubName,
            });
            return prev;
        }, []);
        props.title === "new" && (formData.value.depreciation_asub = depreciationList.value[0].value);
    });
}
watch(()=>props.title,()=>{
    initGetSubject();
    window.addEventListener("modifyaccountSubject", () => {
    if(useRouterArrayStoreHook().routerArray.find((item) => item.title.includes('固定资产类别设置'))) {
        initGetSubject()
    }
    });
},{immediate:true})

const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
let init = false;
const changeInit = (val: boolean) => {
    nextTick(() => {
        init = val;
    });
};
const resetInit = () => {
    init = false;
    isEditting.value = false;
};
const isEditting = ref(false);
watch(
    formData,
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
defineExpose({
    handleFocus,
    changeInit,
    resetInit
});

watch(
    () => formData.value.fa_property,
    () => {
        depreciationTypeList[1].label = formData.value.fa_property === 0 ? "不折旧" : "不摊销";
    }
);
const showfaPropertyList = ref<Array<any>>([]);
const showDepreciationTypeList = ref<Array<any>>([]);
const showfaAsubList = ref<Array<ISelectItem>>([]);
const showdepreciationList = ref<Array<ISelectItem>>([]);
watchEffect(() => {  
    showDepreciationTypeList.value = JSON.parse(JSON.stringify(depreciationTypeList));  
    showfaPropertyList.value = JSON.parse(JSON.stringify(faPropertyList.value));
    showfaAsubList.value = JSON.parse(JSON.stringify(faAsubList.value));
    showdepreciationList.value = JSON.parse(JSON.stringify(depreciationList.value));
});
function faPropertyFilterMethod(value: string) {
    showfaPropertyList.value = commonFilterMethod(value, faPropertyList.value, 'label');
}
function depreciationTypeFilterMethod(value: string) {
    showDepreciationTypeList.value = commonFilterMethod(value, depreciationTypeList, 'label');
}
function faAsubFilterMethod(value: string) {
    showfaAsubList.value = commonFilterMethod(value, faAsubList.value, 'label');
}
function deAsubFilterMethod(value: string) {
    showdepreciationList.value = commonFilterMethod(value, depreciationList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.edit-div {
    width: 1000px;
    background-color: var(--white);
    .add-content {
        padding-top: 40px;
        padding-bottom: 12px;
        display: flex;
        justify-content: center;
        font-size: 12px;
        .isRow {
            width: 1000px;
            height: 40px;
            display: flex;
            justify-content: center;
        }
    }
    &.erp {
        border: 1px solid rgba(0, 0, 0, 0.1);
        margin-top: 36px;
        box-sizing: border-box;
    }
}

:deep(.el-select-dropdown__list) {
    max-height: 200px;
    overflow-y: auto;
}
:deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    & span {
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
</style>
