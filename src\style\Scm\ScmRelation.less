.content {
    .main-content {
        .slot-mini-content {
            width: 1100px;
            padding: 0px 70px 40px 50px;
            box-sizing: border-box;
            & .main-top {
                padding: 16px 0px;
                text-align: left;

                & .triangle {
                    display: inline-block;
                    width: 0px;
                    height: 0px;
                    border-top: 6px solid transparent;
                    border-bottom: 6px solid transparent;
                    border-left: 9px solid rgb(24, 144, 255);
                    margin-right: 5px;
                    margin-top: 4px;
                    vertical-align: top;
                }
            }
            & .main-table {
                & :deep(.state-block) {
                    text-align: center;
                    width: 54px;
                    height: 20px;
                    display: inline-block;
                    color: var(--white);
                    font-size: var(--h5);
                    line-height: var(--line-height);
                    &.relation {
                        color: #44b449;
                        & i {
                            width: 12px;
                            height: 12px;
                            background: url(@/assets/Scm/relation-state.png) no-repeat;
                            background-size: 12px;
                            display: inline-block;
                            vertical-align: -2px;
                            margin-right: 5px;
                        }
                    }
                    &.un-relation {
                        background-color: rgba(0, 0, 0, 0.45);
                    }
                }
            }
            .main-center {
                margin-bottom: 16px;
                padding: 0;
                .main-center-title {
                    text-align: left;
                    margin-top: 48px;
                    margin-bottom: 24px;
                    color: var(--black);
                    font-size: var(--h2);
                    line-height: 25px;
                }
                .main-center-block {
                    font-size: 0;
                    text-align: center;
                    display: flex;
                    justify-content: center;
                    & div {
                        display: inline-block;
                    }
                    & .info-block {
                        width: 180px;
                        text-align: center;
                        vertical-align: top;
                        & .info-pic {
                            height: 180px;
                            width: 164px;
                            border: 1px solid #f8f8f8;
                            border-radius: 8px;
                            text-align: center;
                            background-color: #f8f8f8;
                            cursor: pointer;
                            &:hover {
                                background-color: var(--white);
                                border-color: #44b449;
                            }
                            & .pic {
                                width: 56px;
                                height: 56px;
                                margin: 16px 54px 11px 54px;
                                display: block;
                            }
                            &.relation-pic .pic {
                                background: url(@/assets/Scm/relation-new.png) no-repeat;
                                background-size: 56px;
                            }
                            &.settings-pic .pic {
                                background: url(@/assets/Scm/settings-new.png) no-repeat;
                                background-size: 56px;
                            }
                            &.data-pic .pic {
                                background: url(@/assets/Scm/data-new.png) no-repeat;
                                background-size: 56px;
                            }
                            &.voucher-pic .pic {
                                background: url(@/assets/Scm/voucher-new.png) no-repeat;
                                background-size: 56px;
                            }
                            & .info-title {
                                color: #44b449;
                                font-size: 13px;
                                line-height: 18px;
                            }
                            & .info-word {
                                margin-top: 8px;
                                width: 125px;
                                text-align: left;
                                color: #999999;
                                font-size: var(--h5);
                                line-height: 18px;
                            }
                        }
                    }
                    & .connect-block {
                        width: 86px;
                        & span {
                            height: 36px;
                            display: block;
                            background: url(@/assets/Scm/connect-new.png) no-repeat;
                            margin-left: 37px;
                            margin-top: 80px;
                        }
                    }
                }
                // 进销存首页
                &.is-accounting-agent {
                    .main-center-block {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        padding: 48px 50px 40px 50px;
                        .as-block {
                            width: 224px;
                            height: 58px;
                            background-color: #f8f8f8;
                            display: inline-block;
                            padding: 16px 32px 24px 32px;
                            text-align: left;
                            border-radius: 4px;
                            line-height: 18px;
                            .as-title {
                                display: block;
                                font-size: 12px;
                                color: #999;
                            }
                            .as-info {
                                width: 100%;
                                font-size: 14px;
                                margin-top: 7px;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }
                        }
                        .relation {
                            width: 20px;
                            height: 8px;
                            margin: 0 18px;
                            background: url("@/assets/Scm/relation-aa.png") no-repeat 0 0;
                            background-size: 100% 100%;
                        }
                    }
                    .main-buttons {
                        margin-top: 56px;
                    }
                }
            }
        }
        .bottom-banner{
            width:1100px;
            height:200px;
            padding: 0px 70px 0px 50px;
            text-align:center;
            img {
                width: 980px;
                height: 100%;
                cursor: pointer;
                border-radius: 8px;
            }
        }
    }

    .slot-content {
        .slot-mini-content {
            padding: 40px 0 40px 0;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            width: 1100px;
            & .item-line {
                margin-bottom: 24px;
                display: flex;
                &.item-scm {
                    visibility: hidden;
                }
                &.item-center {
                    justify-content: center;
                }
                & .item-title {
                    width: 490px;
                    text-align: right;
                }
                & .item-input {
                    display: flex;
                    text-align: left;
                    & :deep(.el-radio) {
                        height: 20px;
                    }
                    & :deep(.el-input__inner) {
                        height: 30px;
                    }
                    :deep(.select-trigger) {
                        height: 30px;
                        .el-input {
                            height: 30px;
                        }
                    }
                }
            }
        }
    }
}

.item-option {
    display: flex;
    align-items: center;
    .vip-icon {
        width: 20px;
        height: 20px;
        align-self: flex-start;
    }
}
.relate-container {
    .relate-info {
        text-align: left;
        padding: 20px;
        div {
            margin-bottom: 12px;
        }
    }
}
