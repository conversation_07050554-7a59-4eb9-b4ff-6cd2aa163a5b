<template>
    <el-dialog v-model="dialogShow" :title="title + '设置'" center width="455" class="custom-confirm dialogDrag">
        <div class="asubWithAssist" v-dialogDrag>
            <div class="asubInfo">
                <div class="left-title">{{ title + "：" }}</div>
                <div class="right-info">
                    <ToolTip :content="asubName" :isInput="true">
                        <el-input :value="asubName" readonly disabled style="width: 160px" />
                    </ToolTip>
                </div>
            </div>
            <div class="assistLines" v-for="item in auxiliarySelectListAll" :key="item.name">
                <div class="left-title">{{ item.name }}：</div>
                <div class="right-info">
                    <Select
                        v-model="item.id"
                        style="width: 160px"
                        :disabled="aaReadonly"
                        :bottom-html="newAAHtml"
                        @bottom-click="quickAddAssist(item.type, item.name)"
                        @visible-change="handleVisibleChange($event, item.name)"
                        :filterable="true"
                        :filter-method="auxiliarySelectFilterMethod"
                    >
                        <Option 
                            v-for="option in item.showSelectList" 
                            :key="option.aaeid" 
                            :value="option.aaeid" 
                            :label="option.aaname"
                        ></Option>
                    </Select>
                </div>
            </div>
        </div>
        <div class="buttons" :style="isErp ? '' : 'border-top: 1px solid var(--border-color)'">
            <a class="button solid-button" @click="confirmaAssist">确定</a>
            <a class="button ml-10" @click="() => (dialogShow = false)">取消</a>
        </div>
    </el-dialog>
    <AddAssistingAccountingEntryDialog
        ref="addAssistingAccountingEntryDialogRef"
        :title="'新增' + aaAddTitle"
        :inputTitle="aaAddTitle"
        @save-success="successAddAssist"
    ></AddAssistingAccountingEntryDialog>
</template>

<script setup lang="ts">
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import ToolTip from "@/components/Tooltip/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getAssistingAccountByAATypeApi } from "@/api/assistingAccounting";
import { watch, watchEffect } from "vue";
import { computed, toRef, ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { isNumberOrLetter, aaCheckQuote } from "@/util/validator";
import { checkPermission } from "@/util/permission";
import {getDepartmentApi} from "@/api/assistingAccounting";
import { commonFilterMethod } from "@/components/Select/utils";
import { cloneDeep } from "lodash";

const props = defineProps({
    title: {
        type: String,
        default: "",
    },
    modelValue: {
        type: Boolean,
        default: false,
    },
    asubName: {
        type: String,
        default: "",
    },
    aacode: {
        type: Array<string>,
        default: [],
    },
    aaAllowNull: {
        type: Array<string>,
        default: [],
    },
    departmentId: {
        type: Array<number>,
        default: [],
    },
    aaReadonly: {
        type: Boolean,
        default: false,
    },
    aaId: {
        type: Array,
    },
});

const emit = defineEmits<{
    (e: "update:modelValue", val: boolean): void;
    (e: "setAsubWithAAE", data: IAuxiliarySelectItem[]): void;
}>();
const dialogShow = computed({
    get: () => props.modelValue,
    set: (val: boolean) => emit("update:modelValue", val),
});
const isErp = ref(window.isErp);
const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
let newAAHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
    <a class="link">
        +点击添加
    </a>
</div>`;
const quickAddVisible = ref(false);
const aaAddTitle = ref("");
const addType = ref("");
const quickAddForm = ref({
    addCode: "",
    addName: "",
});
function quickAddAssist(aaType: string, aaName: string) {
    if (!checkPermission(["assistingaccount-canedit"])) {
        ElNotify({
            type: "warning",
            message: "亲，你没有权限新增辅助核算哦！",
        });
        return;
    }
    addType.value = aaType;
    aaAddTitle.value = aaName;
    let name = autoAddName.value;
    request({
        url: `/api/AssistingAccounting/GetNewAANum?aaType=${aaType}&categoryId=0`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            quickAddForm.value.addCode = res.data + "";
            addAssistingAccountingEntryDialogRef.value?.showAADialog(Number(aaType), name);
            // quickAddVisible.value = true;
        }
    });
}

function successAddAssist(data: any) {
    const addassistObj = auxiliarySelectList.value.find((v) => v.type === addType.value) as IAuxiliarySelectItem;
    if (!isErp.value) {
        addassistObj.selectList.push({
            aanum: data.aaNum,
            aaname: data.aaName,
            aaeid: data.aaeId,
        });
    } else {
        let num = addassistObj.type === "10001" 
            ? data.custCode : addassistObj.type === "10002" 
            ? data.vendCode : addassistObj.type === "10003"
            ? data.employeeCode : addassistObj.type === "10004"
            ? data.departmentCode: addassistObj.type === "10005"
            ? data.aaNum : addassistObj.type === "10006"
            ? data.stockCode: data.aaNum ;

        let name = addassistObj.type === "10001" 
            ? data.custName : addassistObj.type === "10002" 
            ? data.vendName : addassistObj.type === "10003"
            ? data.employeeName : addassistObj.type === "10004"
            ? data.departmentName: addassistObj.type === "10005" 
            ? data.aaName : addassistObj.type === "10006"
            ? data.stockName: data.aaName;
            
        addassistObj.selectList.push({
            aanum: num,
            aaname: name,
            aaeid: data.aaeId,
        });
    }
    quickAddForm.value.addName = "";
    addassistObj.id = data.aaeId;
}
const aaTypeList = toRef(useAssistingAccountingStore(), "assistingAccountingTypeList");

function confirmaAssist() {
    if(props.aaReadonly) {
        dialogShow.value = false;
        return;
    }
    const isNull = auxiliarySelectList.value.some((v) => !v.id || v.id == ' ') || Object.keys(auxiliarySelectListAll.value).length === 0;
    if (isNull) {
        ElNotify({
            type: "warning",
            message: "亲，存在未选择的辅助核算项目!",
        });
        return;
    }
    emit("setAsubWithAAE", auxiliarySelectListAll.value);
    dialogShow.value = false;
}
const auxiliarySelectList = ref<IAuxiliarySelectItem[]>([]);
interface IAuxiliarySelectItem {
    type: string; //辅助核算类型
    id: string | number; //辅助核算id
    name: string; //辅助核算名称
    selectList: IAuxiliarySelectListItem[]; //辅助核算列表
}
interface IAuxiliarySelectListItem {
    aanum: string;
    aaname: string;
    aaeid?: number;
}

interface IAuxiliaryListItem {
    asid: number;
    aatype: number;
    aaeid: number;
    aanum: string;
    aaname: string;
    value01: string;
    status: number;
    uscc: string;
    createdBy: number;
    createdDate: string;
    preName: string;
}

watchEffect(async () => {
    if (dialogShow.value) {
        auxiliarySelectList.value = [];
        for (const [index, item] of props.aacode.entries()) {
            let assistObj = {} as IAuxiliarySelectItem;
            const aaType = aaTypeList.value!.find((v) => v.aaType === Number(item));
            assistObj.name = aaType ? aaType.aaTypeName : "";
            let list;
            await getAssistingAccountByAATypeApi(Number(item)).then(async (res: IResponseModel<IAuxiliaryListItem[]>) => {
                list = res.data;
                if (item === "10007") {
                    list = list.filter((v) => v.aaname !== "期初现金余额");
                }
                if (item == "10004") {
                    await getDepartmentApi(true).then((res: IResponseModel<IAuxiliaryListItem[]>) => {
                        list = res.data
                    });
                }
                assistObj.selectList = list.length
                    ? list.map((item: IAuxiliaryListItem) => {
                          return {
                              aanum: item.aanum,
                              aaname: item.aaname,
                              aaeid: item.aaeid,
                          };
                      })
                    : [];
                if (props.aaAllowNull[index] === "0")
                    assistObj.selectList = assistObj.selectList.filter((v) => v.aaname !== "未录入辅助核算");
                if (props.aaId && props.aaId[index] && assistObj.selectList.find(v=> v.aaeid == props.aaId![index])) {
                    assistObj.id =
                        Number(props.aaId[index]) < 0 && props.aaAllowNull[index] === "0"
                            ? assistObj.selectList[0]?.aaeid || ""
                            : Number(props.aaId[index]);
                } else {
                    assistObj.id = assistObj.selectList[0]?.aaeid || " ";
                }
                // 有待考证？
                // if (item === "10004") {
                //     assistObj.id = props.departmentId.length === 1 ? props.departmentId[0] : "";
                // }
                assistObj.type = item;
                auxiliarySelectList.value.push(assistObj);
            });
        }
    }
});

//搜索无数据时，传入新增弹窗内的字段
const filterName = ref("");
const autoAddName = ref("");
//模糊搜索
const auxiliarySelectListAll = ref<any[]>([]);
watch(
    () => auxiliarySelectList.value, 
    () => {
        auxiliarySelectListAll.value = auxiliarySelectList.value.map((v) => {
            return {
                ...v,
                showSelectList: cloneDeep(v.selectList),
            }
        });
    },
    { deep: true }
)
function auxiliarySelectFilterMethod(value: string) {
    let index = auxiliarySelectListAll.value.findIndex((v) => v.name === filterName.value);
    auxiliarySelectListAll.value[index].showSelectList = commonFilterMethod(value, auxiliarySelectListAll.value[index].selectList, 'aaname');
    autoAddName.value = auxiliarySelectListAll.value[index].showSelectList.length === 0 ? value.trim() : ""; 
}
function handleVisibleChange(visible: boolean, name: string) {
    filterName.value = visible ? name : "";
}
</script>
<style scoped lang="less">
:deep(.el-input__inner) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.asubWithAssist {
    padding: 20px 40px;
    line-height: 40px;
    .asubInfo,
    .assistLines {
        display: flex;
        .left-title {
            width: 126px;
            text-align: right;
        }
    }
}
.divAddAA {
    & table {
        margin: 19px auto 0;
        & tr td {
            height: 30px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            padding-bottom: 16px;
        }
    }
    & :deep(.el-input__wrapper) {
        height: 30px !important;
        padding-left: 10px;
    }
}
</style>
