import { getCookie } from "./cookie";
import { getServiceId } from "./proUtils";
import { request, type IResponseModel } from "./service";

export const isInWxWork = () => {
    return /wxwork/i.test(navigator.userAgent) || !!getCookie("wxworkUserId");
};

export const isWxWorkManager = () => {
    return isInWxWork() && !!getCookie("wxworkmanager");
};

let wxWorkServiceLock = false;
export const isWxworkService = async () => {
    if (!window.isProSystem) return false;
    if (window.isWxworkService === undefined && !wxWorkServiceLock) {
        wxWorkServiceLock = true;
        await request({
            url: window.eHost + "/wb/wecom?serviceId=" + getServiceId(),
            method: "get",
        }).then((res: any) => {
            if (res.statusCode) {
                window.isWxworkService = true;
            } else {
                window.isWxworkService = false;
            }
        });
    }
    return !!window.isWxworkService;
};

export const isCurrentWxworkService = async () => {
    if (!isInWxWork() || !window.isProSystem) return false;
    if (window.isCurrentWxworkService === undefined) {
        await request({
            url: window.eHost + "/wb/" + (isInWxWork() ? "wecom/" : "") + "plan/enterprise?serviceId=" + getServiceId(),
            method: "get",
        }).then((res: any) => {
            if (res.statusCode === true) {
                window.isCurrentWxworkService = true;
            } else {
                window.isCurrentWxworkService = false;
            }
        });
    }
    return !!window.isCurrentWxworkService;
};

export const wxworkSuperAdminUserSn = async () => {
    if (!isInWxWork() || !window.isProSystem || !(await isCurrentWxworkService())) return 0;
    if (window.wxworkSuperAdminUserSn === undefined) {
        await request({
            url: window.eHost + "/wb/" + (isInWxWork() ? "wecom/" : "") + "plan/administrator",
            method: "get",
        }).then((res: any) => {
            if (res.statusCode === true) {
                window.wxworkSuperAdminUserSn = res.data.administrator;
            }
        });
    }
    return window.wxworkSuperAdminUserSn || 0;
};

export const wxworkSuperAdminOpenId = async () => {
    if ((await wxworkSuperAdminUserSn()) === 0) return "";
    if (window.wxworkSuperAdminOpenId === undefined) {
        await request({
            url: "/api/WxWorkOnlyAuth/GetUserId",
            method: "post",
            params: {
                userSn: await wxworkSuperAdminUserSn(),
            },
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                window.wxworkSuperAdminOpenId = res.data;
            }
        });
    }
    return window.wxworkSuperAdminOpenId || "";
};

export const isVirtualAccount = async () => {
    if (!isInWxWork()) {
        return false;
    }
    return await request({
        url: "/api/WxWorkOnlyAuth/IsVirtualAccount",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            return res.data;
        } else {
            return false;
        }
    });
};

export const isDemoAccountSet = async () => {
    return await request({
        url: "/api/AccountSet/IsDemoAccountSet",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            return res.data;
        } else {
            return false;
        }
    });
};
