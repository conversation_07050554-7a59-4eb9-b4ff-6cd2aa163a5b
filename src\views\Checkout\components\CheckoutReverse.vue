<template>
    <div class="checkout-content" style="min-height: 500px">
        <div class="create-period-check-tips">
            <div class="tip-item">
                <span class="circle gray"></span>
                <span class="txt">已结转损益、已结账</span>
            </div>
        </div>
        <div class="tiles" :class="{ erp: isErp }">
            <template v-for="(yearItem, index) in props.checkoutListReverse" :key="index">
                <div class="tile-year">{{ Object.keys(yearItem)[0] }}</div>
                <div
                    v-for="item in yearItem[Object.keys(yearItem)[0]]"
                    :key="item.pid"
                    class="base-tile locked canclick"
                    @click="() => reverseCheckout(item.pid)"
                >
                    <div class="tile-head"></div>
                    <div class="tile-main">{{ item.sn }}</div>
                </div>
            </template>
            <div class="display-none-period" v-show="props.checkoutListReverse.length === 0">
                <span>亲，目前没有已结账的期间！</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from "vue";
import { ElConfirm } from "@/util/confirm";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";

import type { ICheckoutItem } from "../tpyes";
import { reloadPeriodInfo } from "@/util/url";
import { useLoading } from "@/hooks/useLoading";
import { checkPermission } from "@/util/permission";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { localStoragePeriod } from "@/util/period";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { reloadAccountSetList } from "@/util/accountset";

const isErp = ref(window.isErp);

const emit = defineEmits(["reverseSuccess"]);

const props = defineProps<{ checkoutListReverse: ICheckoutItem[] }>();

const reverseCheckout = (pid: number) => {
    if (!checkPermission(["checkout-canuncheck"])) {
        ElNotify({ type: "warning", message: "您没有财务反结账权限" });
        return;
    }
    ElConfirm("确定进行反结账 ?", false, () => {}, "反结账操作").then((r: any) => {
        if (r) {
            useLoading().enterLoading("正在进行反结账...");
            request({ url: "/api/Checkout/ReCheckout?pid=" + pid, method: "post" }).then((res: any) => {
                useLoading().quitLoading();
                if (res.state != 1000) {
                    ElNotify({ type: "warning", message: res.message ?? "请求失败" });
                    return;
                }
                if (res.data) {
                    ElNotify({ type: "success", message: "反结账成功" });
                    reloadAccountSetList();
                    window.dispatchEvent(new CustomEvent("peroidListChange"));
                    window.dispatchEvent(new CustomEvent("reCheckout"));
                    localStoragePeriod().reset();
                    useAccountPeriodStore().getPeriods();
                    setTimeout(function () {
                        reloadPeriodInfo();
                        emit("reverseSuccess");
                    }, 300);
                    thirdPartNotify(thirtPartNotifyTypeEnum.checkoutReCheckout, {
                        pid: pid,
                    }).then(() => {});
                }
            });
        }
    });
};
</script>

<style lang="less" scoped>
@import "@/style/Checkout/IndexComponents.less";
.display-none-period {
    display: block;
    color: #5d5d5d;
    font-size: var(--font-size);
    line-height: 20px;
    text-align: center;
    margin-top: 20px;
}
</style>
