<template>
    <el-tooltip
        :visible="visible"
        popper-class="el-option-tool-tip"
        effect="light"
        :content="content"
        :placement="placement"
        :hide-after="0"
    >
        <el-select-v2
            ref="selectRef"
            v-model="value"
            :filterable="filterable"
            :class="[props.class, 'select', 'goods-select-v2', visibleSelect ? 'visibleSelect' : '']"
            :popper-class="popperClass + ' select-down'"
            :options="options"
            :placeholder="placeholder"
            :style="style"
            :teleported="teleported"
            :scrollbar-always-on="scrollbarAlwaysOn"
            :item-height="28"
            :height="198"
            :props="prop"
            :reserve-keyword="reserveKeyword"
            :allow-create="allowCreate"
            @change="handleChange"
            @visible-change="visibleChange"
            @input="handleInput"
            @focus="handleFocus"
            @blur="handleBlur"
            @mouseenter="enterInput"
            @mouseleave="leaveInput"
            :remote="remote && options.length > 0"
            :filter-method="filterMethod"
        >
            <template #prefix>
                <div class="arrow">
                    <el-icon v-if="suffixIcon==='Caret'"><CaretBottom /></el-icon>
                    <el-icon v-else><ArrowDown /></el-icon>
                </div>
            </template>
            <template #default="{ item }">
                <el-tooltip
                    :key="item.value"
                    popper-class="el-option-tool-tip"
                    :visible="currentItem === item.value"
                    effect="light"
                    :content="prop.text?item[prop.text]:item[prop.label]"
                    placement="right"
                    :hide-after="0"
                >
                    <div 
                        class="textOverFlow" 
                        ref="optionRef" 
                        @mouseenter="showTooltip(item)" 
                        @mouseleave="currentItem=-1"
                    >
                        {{ prop.text?item[prop.text]:item[prop.label] }}
                    </div>
                </el-tooltip>
            </template>
        </el-select-v2>
    </el-tooltip>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, nextTick,onBeforeUnmount } from "vue";
import { CaretBottom } from "@element-plus/icons-vue";
import { ArrowDown } from "@element-plus/icons-vue";

interface IProps {
    label?: string;
    value?: string;
    options?: string;
    disabled?: string;
    text?: string;
}

const defaultProps = {
    label: "label",
    value: "value",
    options: "options",
    disabled: "disabled",
};

const selectRef = ref();

const props = withDefaults(
    defineProps<{
        options: Array<any>;
        modelValue: string | number;
        teleported?: boolean;
        class?: string;
        filterable?: boolean;
        placeholder?: string;
        scrollbarAlwaysOn?: boolean;
        style?: any;
        popperClass?: string;
        clearable?: boolean;
        iconClearRight?: number;
        allowCreate?: boolean;
        props?: IProps;
        reserveKeyword?: boolean;
        bottomHtml?: string;
        suffixIcon?: string;
        placement?: string;
        filterMethod?: Function;
        remote?: boolean;
    }>(),
    {
        teleported: true,
        filterable: false,
        class: "",
        placeholder: "",
        scrollbarAlwaysOn: true,
        style: "",
        popperClass: "selectV2",
        clearable: false,
        iconClearRight: 18,
        allowCreate: false,
        props: () => ({}),
        reserveKeyword: true,
        bottomHtml: "",
        suffixIcon: 'Caret',
        placement: "right",
        filterMethod: ()=>{},
        remote: false,
    }
);


const prop = Object.assign({}, defaultProps, props.props);

const emit = defineEmits([
    "update:modelValue",
    "change",
    "visible-change",
    "input",
    "focus",
    "blur",
    "keydownEnter",
    "bottomClick" 
]);

const value = computed<any>({
    get() {
        return props.modelValue ? props.modelValue : undefined;
    },
    set(value: string | number) {
        emit("update:modelValue", value === undefined ? "" : value);
    },
});

const currentItem = ref(-1);
const showTooltip = (item:any) => {
    const element = optionRef.value;
    const width = element.getBoundingClientRect().width;
    if (item.name.length * 12 > width) {
        currentItem.value = item.value;
    } else {
        currentItem.value = -1;
    }
}

const optionRef = ref();
const visible = ref(false);
const content = ref("");
function checkOverflow() {
    const wrapper = selectRef.value?.$el?.querySelector(".el-select-v2__wrapper");
    if (wrapper && wrapper.classList.contains("is-focused")) {
        visible.value = false;
        content.value = "";
        return;
    }
    const placeholder = selectRef.value?.$el?.querySelector(".el-select-v2__placeholder");
    content.value = placeholder ? placeholder.innerText || "" : "";
    visible.value = placeholder.scrollWidth > placeholder.clientWidth && content.value.trim() !== "";
}
function hiddenOverflow() {
    visible.value = false;
}
onMounted(() => {
    const input = selectRef.value?.$refs?.inputRef;
    const placeholder = selectRef.value?.$el?.querySelector(".el-select-v2__placeholder");
    if (input) {
        input.addEventListener("keydown", handleKeyDown);
    }
    if (placeholder) {
        placeholder.addEventListener("mouseenter", checkOverflow);
        placeholder.addEventListener("mouseleave", hiddenOverflow);
    }
});

onBeforeUnmount(() => {
    const input = selectRef.value?.$refs?.inputRef;
    const placeholder = selectRef.value?.$el?.querySelector(".el-select-v2__placeholder");
    if (input) {
        input.removeEventListener("keydown", handleKeyDown);
    }
    if (placeholder) {
        placeholder.removeEventListener("mouseenter", checkOverflow);
        placeholder.removeEventListener("mouseleave", hiddenOverflow);
    }
});

const handleChange = (value: string) => {
    emit("change", value);
};
const visibleSelect = ref(false);
const visibleChange = (visible: boolean) => {
    visibleSelect.value = visible;
    emit("visible-change", visible);
    if (visible && props.bottomHtml !== "") {
        let popper = selectRef.value?.popperRef;
        if (!popper) return;
        if (popper.$el) popper = popper.$el;
        //避免重复添加
        if (!Array.from(popper.children).some((v: any) => v.className === "el-select-menu__list")) {
            const el = document.createElement("ul");
            el.className = "el-select-menu__list";
            el.innerHTML = props.bottomHtml;
            popper.appendChild(el);
            el.onclick = (event: Event) => {
                event.preventDefault();
                event.stopPropagation();
                emit("bottomClick");
                selectRef.value?.$refs?.inputRef.blur();
            };
        }
    }
};
const handleInput = (event: InputEvent) => {
    emit("input", (event.target as HTMLInputElement)?.value || "");
};
const handleFocus = (event: FocusEvent) => {
    emit("focus", event);
};
const handleBlur = (event: FocusEvent) => {
    emit("blur", event);
};
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        // selectRef.value.popperRef.style.display = "none";
        emit("keydownEnter", event);
        nextTick().then(() => {
            value.value = props.modelValue;
            selectRef.value.popperRef.style.display = "none";
            selectRef.value.expanded = false;
            iconFlag.value = value.value ? true : false;
            // selectRef.value.$refs.inputRef.value = props.options.find((item) => item.value === props.modelValue)?.label || "";
        });
    }
};

// const handleClearClick = () => {
//     value.value = "";
//     iconFlag.value = false;
// };
const enterInput = () => {
    iconFlag.value = value.value ? true : false;
};
const leaveInput = () => {
    iconFlag.value = false;
};
const iconFlag = ref(false);



defineExpose({
    focus: () => {
        selectRef.value?.$refs?.inputRef.focus();
        selectRef.value.expanded = true;
    },
    blur: () => {
        selectRef.value?.$refs?.inputRef.blur();
        selectRef.value.popperRef.style.display = "none";
        selectRef.value.expanded = false;
    },
    clear: () => {
        value.value = "";
    },
    toggleMenu: () => {
        selectRef.value?.toggleMenu();
    },
});
</script>

<style lang="less">

.goods-select-v2 {
    .el-select-v2__wrapper {
        line-height: 30px;
        padding: 0 30px 0 0;
        > div {
            &:not(.el-select-v2__input-wrapper) {
                width: 14px;
                height: 30px;
                position: absolute;
                right: 1px;
            }
        }
        .arrow {
            color: #a8abb2;
        }
        .el-select-v2__input-wrapper {
            line-height: 28px;
            -webkit-margin-start: 6px;
            margin-inline-start: 6px;
            input[type="text"] {
                &.el-select-v2__combobox-input {
                    border: none;
                }   
            }
        }
        .el-select-v2__placeholder {
            -webkit-margin-start: 6px;
            margin-inline-start: 6px;
            width: calc(100% - 18px);
            font-size: 12px;
        }
    }
    .el-select-v2__wrapper.is-focused {
        .el-select-v2__placeholder {
            color: #999;
        }
        .arrow {
            transform: rotate(180deg);
        }
    }
    .el-select-v2__suffix {
        width: 0;
        .el-icon {
            display: none;
        }
    }
}
.selectV2 {
    .el-select-dropdown .el-select-dropdown__list .el-select-dropdown__option-item {
        .textOverFlow {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>

