<template>
    <div class="search-info-container">
        <div
            class="search-info-container-reference"
            :class="{ 'search-info-selector': isErp, 'larger-dropdown-button': !isErp }"
            @mouseenter="handleMouseEnter"
            @mouseleave="closeSearchContainer"
        >
            <slot name="title"></slot>
        </div>
        <div class="search-info-container-popover" v-show="popoverShow" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
            <div class="downlist" :class="isErp ? 'erp' : ''">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, provide } from "vue";
import { stopPopoverCloseKey } from "@/components/Picker/SubjectPicker/symbols";
const isErp = ref(window.isErp);
const popoverShow = ref(false);

function handleClose() {
    popoverShow.value = false;
}

const emits = defineEmits(["firstEnter"]);
function handleMouseLeave(event: MouseEvent) {
    needWaiting = false;
    // 存在软键盘的情况下，会触发mouseleave，但是event.relatedTarget为null
    if (event.relatedTarget === null) return;
    if (stop.value) return;
    popoverShow.value = false;
    stop.value = false;
}

let firstFLag = false;
let needWaiting = false;
function handleMouseEnter() {
    needWaiting = true;
    popoverShow.value = true;
    stop.value = true;
    window.requestAnimationFrame(() => {
        stop.value = false;
    });
    if (!firstFLag) {
        emits("firstEnter");
    }
    firstFLag = true;
}

function closeSearchContainer() {
    needWaiting = false;
    setTimeout(() => {
        if (needWaiting) {
            needWaiting = false;
            return;
        }
        popoverShow.value = false;
    }, 100);
}

const stop = ref(false);
function stopCloseSearchPopover() {
    stop.value = true;
    const timer = setTimeout(() => {
        stop.value = false;
        clearTimeout(timer);
    }, 500);
}
provide(stopPopoverCloseKey, stopCloseSearchPopover);

defineExpose({ handleClose, popoverShow, stopCloseSearchPopover });
</script>

<style lang="less" scoped>
@import (reference) "@/style/Common.less";
.larger-dropdown-button {
    border-radius: 2px;
    .base-button(28px, 124px);
    .solid(var(--main-color));
    width: auto;
    padding-left: 10px;
    padding-right: 30px;
    background-image: url("@/assets/Icons/down-white.png");
    background-repeat: no-repeat;
    background-position: right 8px center;
}

.search-info-selector {
    .set-border(var(--input-border-color));
    box-sizing: border-box;
    height: 28px;
    padding: 0 36px 0 10px;
    display: inline-flex;
    align-items: center;
    background-image: url("@/assets/Icons/dateicon2.png");
    background-position: right 10px center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
    font-size: var(--font-size);
    line-height: 22px;
    color: var(--font-color);
    white-space: nowrap;
    cursor: pointer;
}
.search-info-container {
    position: relative;
    height: 28px;

    .search-info-container-popover {
        width: 446px;
        position: absolute;
        z-index: 2000;
        top: 28px;
        background: var(--el-bg-color-overlay);
        border: 1px solid var(--el-border-color-light);
        border-radius: var(--el-popover-border-radius);
        .downlist {
            padding: 20px 15px 20px 0;
            background-color: var(--white);

            :deep(.line-item) {
                height: 20px;
                margin-top: 16px;
                display: flex;
                align-items: center;

                .line-item-title {
                    width: 88px;
                    padding-left: 25px;
                    text-align: left;
                    float: left;
                    .set-font;
                }

                .line-item-field {
                    width: 324px;
                    text-align: left;
                    float: left;
                    padding-left: 10px;
                    .set-font;
                    position: relative;
                    display: flex;
                    align-items: center;
                    .vgid-line {
                        & > .el-input {
                            width: 85px;
                        }
                    }

                    .textbox {
                        .float-l;
                    }
                }

                &.single {
                    .line-item-title {
                        width: auto;
                    }
                }

                &.input {
                    height: 32px;
                    margin-top: 10px;
                    line-height: 32px;
                }

                &.first-item {
                    margin-top: 0;
                }
            }

            :deep(.buttons) {
                display: flex;
                justify-content: flex-start;
                margin-top: 20px;
                padding-left: 25px;

                a {
                    margin-right: 10px;
                }
            }

            &.erp {
                :deep(.buttons) {
                    flex-direction: row-reverse;
                }
            }
        }
    }
}
</style>
