<template>
    <el-dialog center title="提示" width="450px" v-model="dialogShow">
        <div class="company-notice">
            <div class="all-line mt-10 full-text mb-10" :class="centerText?'centerText':''">
                {{ dialogText }}
            </div>
            <div class="all-line mb-20 mt-10 pt-10 border-top" v-show="!isWxWork">
                <a class="button mr-10" @click="cancleHandle">取消</a>
                <a class="button solid-button" @click="GoPurchase">立即增购</a>
            </div>
            <div class="all-line mb-20 mt-10 pt-10 border-top" v-show="isWxWork">
                <a class="button solid-button" @click="dialogShow = false">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ElNotify } from "@/util/notify";
import { globalWindowOpen } from "@/util/url";
import { isWxworkService, isInWxWork } from "@/util/wxwork";
import {  gotoBuy } from "@/util/proUtils";
import { onMounted } from "vue";

const props = defineProps({
    proOverFlow: {
        type: Boolean,
        default: false,
    },
    proOverFlowText: {
        type: String,
        default: "您的账套数量已超过已购买账套数，建议您去增购~",
    },
    centerText:{
        type: Boolean,
        default: false,
    }
});

const isWxWork = computed(() => isInWxWork() || !!window.isWxworkService);
const emit = defineEmits(["update:proOverFlow",'resetCenterText']);
const dialogShow = computed({
    get() {
        return props.proOverFlow && window.isProSystem;
    },
    set(value) {
        emit("update:proOverFlow", value);
    },
});

const dialogText = computed(() => {
    let msg = props.proOverFlowText;
    if (isWxWork.value) {
        msg = msg.replace("建议您去增购", "请联系管理员加购");
    }
    return msg;
});
const GoPurchase = () => {
    dialogShow.value = false;
    emit("resetCenterText");
    proBuyMore();
};
const cancleHandle = () => {
    dialogShow.value = false;
    emit("resetCenterText");
};
const proBuyMore = () => {
    if (isWxWork.value) {
        ElNotify({ type: "warning", message: "请联系管理员加购" });
        return;
    }
    
    gotoBuy();
};

onMounted(() => {
    isWxworkService();
});
</script>

<style lang="less" scoped>
.company-notice {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    align-content: center;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    img {
        width: 150px;
        height: 150px;
        margin-top: 12px;
        margin-bottom: 24px;
    }
    .btn {
        border: 0;
        background: #44b449;
        color: white;
        width: 70px;
        height: 30px;
    }
    .all-line {
        width: 100%;
        &.full-text {
            height: 70px;
            font-size: 15px;
            font-weight: normal;
            width: 300px;
            &.centerText{
                line-height: 80px;
                text-align: center;
                width:342px;
            }
        }
        &.border-top {
            border-top: 1px solid var(--border-color);
        }
    }
}
</style>
