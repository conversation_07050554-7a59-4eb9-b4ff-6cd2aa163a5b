// 表格相关类型
import { type TableProps } from "element-plus/es/components/table/src/table/defaults"
import type { RendererElement, RendererNode, VNode } from "vue"

export interface ITableProps extends TableProps<any> {
  loading?: boolean
  pageIsShow?: boolean
  minHeight?: string | number
  columns?: Array<IColumnProps>
  selectable?: (row: any, index: number) => boolean
  pageSizes?: Array<number>
  pageSize?: number
  layout?: string
  total?: number
  currentPage?: number
}

export interface IColumnProps {
  label?: string
  prop?: string
  width?: number | string
  minWidth?: number | string
  align?: "left" | "center" | "right"
  headerAlign?: "left" | "center" | "right"
  slot?: string
  type?: string
  fixed?: "left" | "right"
  useHtml?: boolean
  reserveSelection?: boolean
  children?: Array<IColumnProps>
  className?: string
  col?: number // 列数
  resizable?: boolean
  headerSort?: boolean // 表头排序
  alias?: string // 别名
  defaultNotSelectAll?: boolean // 默认全选
  selectable?: (row: any, index: number) => boolean // 可选择行的条件
  formatter?: (
    row: any,
    column: any,
    cellValue: any,
    index: number,
  ) =>
    | string
    | VNode<
        RendererNode,
        RendererElement,
        {
          [key: string]: any
        }
      >
  columnIndex?: string | number
}

export interface IPaginationProps {
  total?: number
  pageSizes?: Array<any>
  pageSize?: number
  currentPage?: number
  customPageStartEnd?: Array<number>
}

export interface TableEmits {
  (e: "select", selection: any, row: any): void
  (e: "selection-change", value: any): void
  (e: "select-all", value: any): void
  (e: "row-click", row: any, column: any, event: any): void
  (e: "row-dblclick", row: any, column: any, event: any): void
  (e: "current-change", value: number): void
  (e: "sort-change", column: any, prop: any, order: any): void
  (e: "cell-click", row: any, column: any, cell: any, event: any): void
  (e: "size-change", value: number): void
  (e: "refresh"): void
  (e: "cell-mouse-enter", row: any, column: any, cell: HTMLTableCellElement, event: Event): void
  (e: "cell-mouse-leave", row: any, column: any, cell: HTMLTableCellElement, event: Event): void
}

export interface PaginationEmits {
  (e: "current-change", payload: { pageNum: number; num: number }): void
  (e: "size-change", payload: { pageNum: number; num: number }): void
  (e: "refresh"): any
}
