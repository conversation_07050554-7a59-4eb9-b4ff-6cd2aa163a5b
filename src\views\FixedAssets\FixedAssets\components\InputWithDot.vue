<template>
    <div class="assist-input">
        <ToolTip :content="inputName" :isInput="true" :placement="placement">
            <el-input :value="inputName" placeholder="请输入内容" :style="{ width: inputWidth }" readonly :disabled="inputDisabled"></el-input>
            <el-icon v-if="props.iconVisible" class="more-filled"><MoreFilled @click="expandAssit" /></el-icon>
        </ToolTip>
    </div>
</template>

<script setup lang="ts">
import ToolTip from "@/components/Tooltip/index.vue";
import { computed, type PropType} from 'vue';

const props = defineProps({
    inputValue: {
        type: String,
        required: true,
    },
    inputWidth: {
        type: String,
        default: "160px",
    },
    iconVisible: {
        type: Boolean,
        default: true,
    },
    inputDisabled: {
        type: Boolean,
        default: false,
    },
    placement: {
        type: String as PropType<"top" | "top-start" | "top-end" | "bottom" | "bottom-start" | "bottom-end" | "left" | "left-start" | "left-end" | "right" | "right-start" | "right-end">,
        default: "top",
    },
});

const emit = defineEmits(["expandAssit"]);
const inputName = computed(() => {
    return props.inputValue;
});
function expandAssit() {
    emit("expandAssit");
}
</script>

<style scoped lang="less">
:deep(.el-input__inner) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
}
.assist-input{
    position:absolute;
    .more-filled{
        position: relative;
        height:30px;
        width:24px;
        left:-24px;
        top:3px;
        cursor: pointer;
    }
}
</style>
