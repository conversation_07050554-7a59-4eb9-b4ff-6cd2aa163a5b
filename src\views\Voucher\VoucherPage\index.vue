<template>
    <NewVoucher :pid="pid" :vid="vid" :fcode="fcode" :from="from" :isTemp="isTemp" ref="newVoucherRef"></NewVoucher>
    <ConfirmWithoutGlobal
        v-model="confirmWithoutGlobalVisible"
        :show-close="false"
        confirmButtonText="编辑原凭证"
        cancelButtonText="进入新凭证"
        :confirm-click="confirmHandle"
        :cancel-click="cancelHandle"
    >
        <div style="text-align: left; margin: 0 -30px">
            您之前编辑的凭证还没有保存<br />点击'编辑原凭证'则可继续编辑原凭证<br />点击'进入新凭证'则原凭证将不会保存并进入新凭证页面
        </div>
    </ConfirmWithoutGlobal>
</template>
<script lang="ts">
export default {
    name: "VoucherPage",
};
</script>
<script setup lang="ts">
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { getGlobalToken } from "@/util/baseInfo";
import NewVoucher from "@/views/Voucher/NewVoucher/index.vue";
import { onActivated } from "vue";
import { ref } from "vue";
import { useRoute } from "vue-router";
import ConfirmWithoutGlobal from "@/components/ConfirmWithoutGlobal/index.vue";

const confirmWithoutGlobalVisible = ref(false);

const route = useRoute();
const pid = ref(Number(route.query.pid) || 0);
const vid = ref(Number(route.query.vid) || 0);
const fcode = ref((route.query.fcode as string) || "");
const from = ref((route.query.from as string) || "");
const isTemp = ref(route.query.isTemp === "true");
const newVoucherRef = ref<InstanceType<typeof NewVoucher>>();
const routerArrayStore = useRouterArrayStoreHook();

let confirmHandle = () => {};

let cancelHandle = () => {};

onActivated(() => {
    const newPid = Number(route.query.pid) || 0;
    const newVid = Number(route.query.vid) || 0;
    const newFcode = (route.query.fcode as string) || "";
    const newFrom = (route.query.from as string) || "";
    const newIsTemp = route.query.isTemp === "true";
    if (pid.value !== newPid || vid.value !== newVid || fcode.value !== newFcode || from.value !== newFrom) {
        if (!newVoucherRef.value?.getEditedState()) {
            pid.value = newPid;
            vid.value = newVid;
            fcode.value = newFcode;
            from.value = newFrom;
            isTemp.value = newIsTemp;
        } else {
            confirmHandle = () => {
                routerArrayStore.replaceCurrentRouter(
                    "/Voucher/VoucherPage?appasid=" +
                        getGlobalToken() +
                        "&pid=" +
                        pid.value +
                        "&vid=" +
                        vid.value +
                        "&fcode=" +
                        fcode.value +
                        "&from=" +
                        from.value +
                        "&isTemp=" +
                        newIsTemp
                );
            };
            cancelHandle = () => {
                pid.value = newPid;
                vid.value = newVid;
                fcode.value = newFcode;
                from.value = newFrom;
                isTemp.value = newIsTemp;
            };
            confirmWithoutGlobalVisible.value = true;
        }
    }
});
</script>
