import { ref, type ComputedRef } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { getAllAsubApi, type IAccountSubjectModel, type IAccountSubjectModelWithPinyin } from "@/api/accountSubject";
import { getGlobalToken } from "@/util/baseInfo";
import type { IResponseModel } from "@/util/service";
import { pinyin } from "pinyin-pro";

export const useAccountSubjectStore = defineStore("accountSubject", () => {
    const accountSubjectList = ref<IAccountSubjectModel[]>([]);
    const accountSubjectListWithoutDisabled = ref<IAccountSubjectModel[]>([]);
    let _accountSubjectListWithPinyin: IAccountSubjectModelWithPinyin[];
    const accountSubjectListWithPinyin = (): IAccountSubjectModelWithPinyin[] => {
        if (!_accountSubjectListWithPinyin) {
            _accountSubjectListWithPinyin = [];
            accountSubjectList.value.forEach((item) => {
                _accountSubjectListWithPinyin.push({
                    ...item,
                    pinyinArr: pinyin(item.asubAAName, { pattern: "first", toneType: "none", type: "array" }),
                });
            });
        }
        return _accountSubjectListWithPinyin;
    };

    let _accountSubjectListWithoutDisabledWithPinyin: IAccountSubjectModelWithPinyin[];
    const accountSubjectListWithoutDisabledWithPinyin = (): IAccountSubjectModelWithPinyin[] => {
        if (!_accountSubjectListWithoutDisabledWithPinyin) {
            _accountSubjectListWithoutDisabledWithPinyin = [];
            accountSubjectListWithoutDisabled.value.forEach((item) => {
                _accountSubjectListWithoutDisabledWithPinyin!.push({
                    ...item,
                    pinyinArr: pinyin(item.asubAAName, { pattern: "first", toneType: "none", type: "array" }),
                });
            });
        }
        return _accountSubjectListWithoutDisabledWithPinyin;
    };

    const getAccountSubject = () => {
        return new Promise<IAccountSubjectModel[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getAllAsubApi(true)
                    .then((res: any) => {
                        const data = res as IResponseModel<IAccountSubjectModel[]>;
                        if (data.state === 1000) {
                            accountSubjectList.value = data.data;
                            accountSubjectListWithoutDisabled.value = data.data.filter((item) => item.status === 0);
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    return {
        accountSubjectList,
        accountSubjectListWithPinyin,
        accountSubjectListWithoutDisabled,
        accountSubjectListWithoutDisabledWithPinyin,
        getAccountSubject,
    };
});

/** 在 setup 外使用 */
export function useAccountSubjectStoreHook() {
    return useAccountSubjectStore(store);
}
