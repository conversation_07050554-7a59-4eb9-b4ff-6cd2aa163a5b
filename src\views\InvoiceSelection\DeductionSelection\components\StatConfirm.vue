<template>
  <div style="overflow: auto">
    <div class="main-top flex-end">
      <div class="main-tool-right">
        <template v-if="activeStep === 1">
          <a
            class="button mr-20"
            @click="print">
            打印
          </a>
          <a
            class="button mr-20"
            @click="revokeStatistics">
            撤销统计
          </a>
          <a
            class="button mr-20"
            style="display: flex; justify-content: center"
            @click="confirmStatistics">
            统计确认
            <component :is="renderTooltip('混开农产品专票会分别统计农产品和非农产品，出现总份数不一致情况')" />
          </a>
        </template>
        <template v-if="activeStep === 2">
          <a
            class="button mr-20"
            @click="toCertificationResults">
            往期认证结果
          </a>
          <a
            class="button mr-20"
            @click="print">
            打印
          </a>
          <a
            class="button mr-20"
            @click="downloadInvoiceDetails">
            下载发票明细
          </a>
          <a
            class="button mr-20"
            @click="revokeConfirmation">
            撤销确认
          </a>
        </template>
      </div>
    </div>
    <div class="main-content-body">
      <div
        class="confirm-steps"
        style="display: flex; justify-content: center">
        <el-steps
          style="min-width: 600px"
          :active="activeStep"
          finish-status="success">
          <el-step title="申请统计" />
          <el-step title="统计确认" />
          <el-step title="确认" />
        </el-steps>
      </div>
      <template v-if="activeStep === 0">
        <div
          class="steps1"
          style="display: flex; justify-content: center; align-items: center; flex-direction: column">
          <img
            src="@/assets/InvoiceSelection/applyStatistics.png"
            alt="Step 1" />
          <div class="steps1-tip">点击申请统计，生成当期抵扣发票汇总表</div>
          <div
            class="button solid-button mt-10"
            @click="applyStatistics">
            申请统计
          </div>
        </div>
      </template>
      <template v-if="[1, 2].includes(activeStep)">
        <div class="steps2">
          <div
            class="steps-block-title"
            style="display: flex">
            <span>增值税申报进行抵扣汇总</span>
            <component :is="renderTooltip('混开农产品专票会分别统计农产品和非农产品，出现总份数不一致情况')" />
          </div>
          <LMTable
            :data="vatDeductionResult"
            :columns="deductionSummaryColumns"
            rowKey="invoiceType"></LMTable>
          <div class="steps-block-title">抵扣类勾选统计结果</div>
          <LMTable
            :data="deductionSelectionResult"
            :columns="deductionStatisticsColumns"
            rowKey="invoiceType"></LMTable>
          <div class="steps-block-title">农产品加计扣除勾选统计结果</div>
          <LMTable
            :data="agriculturalDeductionResult"
            :columns="agriculturalDeductionColumns"
            rowKey="invoiceType"></LMTable>
          <template v-if="abnormalResult.length">
            <div class="steps-block-title">异常凭证转入统计表</div>
            <LMTable
              :data="abnormalResult"
              :columns="abnormalColumns"
              rowKey="invoiceType"></LMTable>
          </template>
        </div>
      </template>
    </div>
  </div>
  <Confirm
    title="选择打印内容"
    v-model:visible="printConfirmVisible"
    width="400"
    :onConfirm="handlePrintConfirm"
    :options="{
      buttons: {
        confirm: '打印',
      },
    }">
    <div class="confirm-content-field print-confirm-content">
      <el-checkbox-group v-model="printList">
        <el-checkbox
          label="增值税申报进行抵扣汇总"
          value="1"></el-checkbox>
        <el-checkbox
          label="抵扣类勾选统计结果"
          value="2"></el-checkbox>
        <el-checkbox
          label="农产品加计扣除勾选统计结果"
          value="3"></el-checkbox>
        <el-checkbox
          v-if="abnormalResult.length"
          label="异常凭证转入统计表"
          value="4"></el-checkbox>
      </el-checkbox-group>
    </div>
  </Confirm>
</template>

<script setup lang="ts">
  import { request, IResponseModel } from "@/utils/service"
  import { ElNotify } from "@/utils/notify"
  import { ElConfirm } from "@/utils/confirm"
  import { useTooltip } from "@/hooks/useTooltip"
  import { getLocalStorage, setLocalStorage } from "@/utils/localStorageOperate"
  import { globalWindowOpenPage } from "@/utils/url"

  const props = defineProps({
    invoiceTaxPeriod: {
      type: String,
      required: true,
    },
  })
  const { renderTooltip } = useTooltip()
  const activeStep = ref(0)
  const applyStatistics = () => {
    request({
      url: "/api/invoice-selection/statistics/1?invoicePeriodId=" + props.invoiceTaxPeriod,
      method: "POST",
    }).then((res: IResponseModel<number>) => {
      if (res.state === 1000) {
        activeStep.value = 1
      } else {
        ElNotify({
          message: res.msg ?? "申请统计失败，请联系客服",
          type: "error",
        })
      }
    })
  }

  const revokeStatistics = () => {
    ElConfirm({
      title: "撤销统计",
      message: "是否确定撤销统计？",
      showCancel: true,
    }).then((r: boolean) => {
      if (r) {
        request({
          url: "/api/invoice-selection/statistics/2?invoicePeriodId=" + props.invoiceTaxPeriod,
          method: "POST",
        }).then((res: IResponseModel<number>) => {
          if (res.state === 1000) {
            activeStep.value = 0
          } else {
            ElNotify({
              message: res.msg ?? "撤销统计失败，请联系客服",
              type: "error",
            })
          }
        })
      }
    })
  }

  const confirmStatistics = () => {
    ElConfirm({
      title: "统计确认提示",
      message: "确认后当前统计报表将作为申报的依据，请确认是否继续进行统计确认？",
    }).then((r: boolean) => {
      if (r) {
        request({
          url: "/api/invoice-selection/statistics/3?invoicePeriodId=" + props.invoiceTaxPeriod,
          method: "POST",
        }).then((res: IResponseModel<number>) => {
          if (res.state === 1000) {
            activeStep.value = 2
          } else {
            ElNotify({
              message: res.msg ?? "统计确认失败，请联系客服",
              type: "error",
            })
          }
        })
      }
    })
  }

  const deductionSummaryColumns = [
    {
      label: "进项抵扣类型",
      prop: "typeName",
      minWidth: 200,
    },
    {
      label: "发票份数",
      prop: "count",
      minWidth: 100,
    },
    {
      label: "金额",
      prop: "amount",
      minWidth: 100,
    },
    {
      label: "税额",
      prop: "tax",
      minWidth: 100,
    },
  ]
  const vatDeductionResult = ref([])

  const deductionStatisticsColumns = [
    {
      label: "发票类型",
      prop: "typeName",
      minWidth: 200,
    },
    {
      label: "合计份数",
      prop: "totalCount",
      minWidth: 100,
    },
    {
      label: "有效抵扣税额合计",
      prop: "deductionAmount",
      minWidth: 100,
    },
    {
      label: "出口转内销证明份数",
      prop: "exportToDomesticCount",
      minWidth: 100,
    },
    {
      label: "出口转内销证明有效抵扣税额合计",
      prop: "exportToDomesticTax",
      minWidth: 100,
    },
    {
      label: "其他发票份数",
      prop: "otherInvoiceCount",
      minWidth: 100,
    },
    {
      label: "其他发票有效抵扣税额合计",
      prop: "otherDeductionTax",
      minWidth: 100,
    },
  ]

  const deductionSelectionResult = ref([])

  const agriculturalDeductionColumns = [
    {
      label: "发票类型",
      prop: "typeName",
      minWidth: 200,
    },
    {
      label: "正数发票",
      minWidth: 100,
      children: [
        {
          label: "发票份数",
          prop: "count",
          minWidth: 100,
        },
        {
          label: "有效抵扣税额合计",
          prop: "deductionAmount",
          minWidth: 100,
        },
      ],
    },
    {
      label: "负数发票",
      minWidth: 100,
      children: [
        {
          label: "发票份数",
          prop: "negativeCount",
          minWidth: 100,
        },
        {
          label: "有效抵扣税额合计",
          prop: "negativeDeductionAmount",
          minWidth: 100,
        },
      ],
    },
  ]

  const abnormalColumns = [
    {
      label: "发票类型",
      prop: "typeName",
      minWidth: 200,
    },
    {
      label: "合计份数",
      prop: "totalCount",
      minWidth: 100,
    },
    {
      label: "有效抵扣税额合计",
      prop: "deductionAmount",
      minWidth: 100,
    },
  ]

  const agriculturalDeductionResult = ref([])
  const abnormalResult = ref([])

  const printConfirmVisible = ref(false)
  const printList = ref<string[]>(["1", "2", "3"])
  const print = () => {
    printConfirmVisible.value = true
    printList.value = JSON.parse(getLocalStorage("invoiceConfirmPrintList") || "[1, 2, 3]")
  }
  const handlePrintConfirm = () => {
    const selectedItems = printList.value
    if (selectedItems.length === 0) {
      ElNotify({
        message: "请至少选择一项打印内容",
        type: "warning",
      })
      return
    }
    setLocalStorage("invoiceConfirmPrintList", JSON.stringify(selectedItems))
    request({
      url: "/api/invoice-selection/statistics/print",
      method: "POST",
      data: {
        invoicePeriodId: props.invoiceTaxPeriod,
        printList: selectedItems,
      },
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000) {
        window.open(res.data, "_blank")
      } else {
        ElNotify({
          message: res.msg ?? "打印失败，请联系客服",
          type: "error",
        })
      }
    })
    printConfirmVisible.value = false
  }

  const toCertificationResults = () => {
    globalWindowOpenPage("/InvoiceSelection/CertificationResults", "往期认证结果")
  }

  const downloadInvoiceDetails = () => {
    //下载
  }
  const revokeConfirmation = () => {
    ElConfirm({
      title: "撤销确认",
      message: "是否确定撤销确认？",
      showCancel: true,
    }).then((r: boolean) => {
      if (r) {
        request({
          url: "/api/invoice-selection/statistics/4?invoicePeriodId=" + props.invoiceTaxPeriod,
          method: "POST",
        }).then((res: IResponseModel<number>) => {
          if (res.state === 1000) {
            activeStep.value = 1
          } else {
            ElNotify({
              message: res.msg ?? "撤销确认失败，请联系客服",
              type: "error",
            })
          }
        })
      }
    })
  }

  function getStatus() {
    request({
      url: "/api/invoice-selection/status?invoicePeriodId=" + props.invoiceTaxPeriod,
    }).then((res: IResponseModel<number>) => {
      if (res.state === 1000) {
        activeStep.value = res.data
      }
    })
  }

  watch(activeStep, (newStep) => {
    if (newStep === 1) {
      request({
        url: "/api/invoice-selection/statistics?invoicePeriodId=" + props.invoiceTaxPeriod,
      }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
          vatDeductionResult.value = res.data.vatDeductionResult || []
          deductionSelectionResult.value = res.data.deductionSelectionResult || []
          agriculturalDeductionResult.value = res.data.agriculturalDeductionResult || []
          abnormalResult.value = res.data.abnormalResult || []
        } else {
          ElNotify({
            message: res.msg ?? "获取统计数据失败，请联系客服",
            type: "error",
          })
        }
      })
    }
  })

  watch(
    () => props.invoiceTaxPeriod,
    () => {
      getStatus()
    },
    { immediate: true },
  )
</script>

<style lang="scss" scoped>
  .steps1 {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .steps2 {
    .steps-block-title {
      padding-top: 20px;
      padding-bottom: 20px;
    }
  }
  .print-confirm-content {
    padding: 0 76px;
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
    }
  }
</style>
