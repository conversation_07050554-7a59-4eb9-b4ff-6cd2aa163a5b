<template>
  <div style="overflow: auto">
    <div class="main-top flex-end">
      <div class="main-tool-right">
        <template v-if="activeStep === 1">
          <a class="button mr-10">打印</a>
          <a class="button mr-10">撤销统计</a>
          <a
            class="button mr-10"
            @click="activeStep = 2">
            统计确认
          </a>
        </template>
        <template v-if="activeStep === 2">
          <a class="button mr-10">往期认证结果</a>
          <a class="button mr-10">打印</a>
          <a class="button mr-10">下载发票明细</a>
          <a class="button mr-10">撤销确认</a>
        </template>
      </div>
    </div>
    <div class="main-content-body">
      <div
        class="confirm-steps"
        style="display: flex; justify-content: center">
        <el-steps
          style="min-width: 600px"
          :active="activeStep"
          finish-status="success">
          <el-step title="申请统计" />
          <el-step title="统计确认" />
          <el-step title="确认" />
        </el-steps>
      </div>
      <template v-if="activeStep === 0">
        <div
          class="steps1"
          style="display: flex; justify-content: center; align-items: center; flex-direction: column">
          <img
            src="@/assets/InvoiceSelection/applyStatistics.png"
            alt="Step 1" />
          <div class="steps1-tip">点击申请统计，生成当期抵扣发票汇总表</div>
          <div
            class="button solid-button mt-10"
            @click="applyStatistics">
            申请统计
          </div>
        </div>
      </template>
      <template v-if="[1, 2].includes(activeStep)">
        <div class="steps2">
          <div class="steps-block-title">增值税申报进行抵扣汇总</div>
          <LMTable
            :data="deductionSummaryData"
            :columns="deductionSummaryColumns"
            rowKey="invoiceType"></LMTable>
          <div class="steps-block-title">抵扣类勾选统计结果</div>
          <LMTable
            :data="deductionStatisticsData"
            :columns="deductionStatisticsColumns"
            rowKey="invoiceType"></LMTable>
          <div class="steps-block-title">农产品加计扣除勾选统计结果</div>
          <LMTable
            :data="deductionStatisticsData"
            :columns="agriculturalDeductionColumns"
            rowKey="invoiceType"></LMTable>
          <div class="steps-block-title">异常凭证转入统计表</div>
          <LMTable
            :data="deductionStatisticsData"
            :columns="deductionStatisticsColumns"
            rowKey="invoiceType"></LMTable>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  const activeStep = ref(0)
  const applyStatistics = () => {
    activeStep.value = 1
  }

  const deductionSummaryColumns = [
    {
      label: "进项抵扣类型",
      prop: "invoiceType",
      minWidth: 200,
    },
    {
      label: "发票份数",
      prop: "invoiceCount",
      minWidth: 100,
    },
    {
      label: "金额",
      prop: "invoiceAmount",
      minWidth: 100,
    },
    {
      label: "税额",
      prop: "taxAmount",
      minWidth: 100,
    },
  ]
  const deductionSummaryData = ref([
    {
      invoiceType: "本期认证相符的增值税专用发票",
      invoiceCount: 10,
      invoiceAmount: 1000,
      taxAmount: 170,
    },
    {
      invoiceType: "海关进口值税缴款书",
      invoiceCount: 5,
      invoiceAmount: 500,
      taxAmount: 85,
    },
  ])

  const deductionStatisticsColumns = [
    {
      label: "发票类型",
      prop: "invoiceType",
      minWidth: 200,
    },
    {
      label: "合计份数",
      prop: "invoiceCount",
      minWidth: 100,
    },
    {
      label: "有效抵扣税额合计",
      prop: "invoiceAmount",
      minWidth: 100,
    },
    {
      label: "出口转内销证明份数",
      prop: "taxAmount",
      minWidth: 100,
    },
    {
      label: "出口转内销证明有效抵扣税额合计",
      prop: "taxAmount",
      minWidth: 100,
    },
    {
      label: "合计份数",
      prop: "taxAmount",
      minWidth: 100,
    },
    {
      label: "其他发票有效抵扣税额合计",
      prop: "taxAmount",
      minWidth: 100,
    },
  ]

  const deductionStatisticsData = ref([
    {
      invoiceType: "增值税专用发票",
      invoiceCount: 10,
      invoiceAmount: 1000,
      taxAmount: 170,
    },
    {
      invoiceType: "收购发票",
      invoiceCount: 5,
      invoiceAmount: 500,
      taxAmount: 85,
    },
  ])

  const agriculturalDeductionColumns = [
    {
      label: "发票类型",
      prop: "invoiceType",
      minWidth: 200,
    },
    {
      label: "正数发票",
      minWidth: 100,
      children: [
        {
          label: "发票份数",
          prop: "invoiceCount",
          minWidth: 100,
        },
        {
          label: "有效抵扣税额合计",
          prop: "invoiceAmount",
          minWidth: 100,
        },
      ],
    },
    {
      label: "负数发票",
      minWidth: 100,
      children: [
        {
          label: "发票份数",
          prop: "invoiceCount",
          minWidth: 100,
        },
        {
          label: "有效抵扣税额合计",
          prop: "invoiceAmount",
          minWidth: 100,
        },
      ],
    },
  ]
</script>

<style lang="scss" scoped>
  .steps1 {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .steps2 {
    .steps-block-title {
      padding-top: 20px;
      padding-bottom: 20px;
    }
  }
</style>
