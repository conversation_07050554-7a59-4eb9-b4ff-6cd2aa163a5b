export const fileUtily = {
    unit: ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB", "BB"],
    sizeFormatter: function (size: number, fixed: string = "2") {
        let __i = 0;
        let __s = size;
        let __f = 2;
        if (parseInt(fixed) >= 0) {
            __f = parseInt(fixed);
        }
        while (__s / 1024 >= 1) {
            __i++;
            if (__s / 1024 >= 1024) {
                __s /= 1024;
                if (__i >= this.unit.length - 1) break;
            } else {
                __s /= 1024;
                break;
            }
        }
        if (__s == 0) {
            return "0KB";
        }
        return __s.toFixed(__f) + this.unit[__i];
    },
};

export function getLocalStorage(key: string) {
    return localStorage.getItem(key);
}
