<template>
    <div class="newUserDialog-shadow" v-if="newUserDialogShow">
        <div class="newUserDialog-container">
            <img src="@/assets/NewUserDialog/dialog.png" />
            <div class="close-btn" @click="newUserDialogShow = false"></div>
        </div>
    </div>
</template>
<style scoped lang="less">
.newUserDialog-shadow {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--shadow-color);
    z-index: 99999;
    display: flex;
    justify-content: center;
    align-items: center;

    .newUserDialog-container {
        position: relative;
        display: flex;

        .close-btn {
            width: 18px;
            height: 18px;
            background: url("@/assets/NewUserDialog/close.png") no-repeat;
            position: absolute;
            top: 20px;
            right: 20px;
            cursor: pointer;
        }
    }
}
</style>
<script setup lang="ts">
import { ref } from "vue";

const newUserDialogShow = ref(true);
</script>
