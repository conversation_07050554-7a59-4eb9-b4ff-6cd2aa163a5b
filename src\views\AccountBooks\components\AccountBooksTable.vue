<template>
    <Table
        ref="AccountTable"
        :data="props.data"
        :scrollbarShow="props.scrollbarShow"
        :columns="props.columns"
        :page-is-show="props.pageIsShow"
        :layout="props.layout"
        :page-sizes="props.pageSizes"
        :page-size="props.pageSize"
        :total="props.total"
        :currentPage="props.currentPage"
        :tooltipOptions="props.tooltipOptions"
        :min-height="props.minHeight"
        :empty-text="emptyText"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :tableName="tableName"
    >
        <template v-for="slotItem in slotsArr" #[slotItem]="{ slotColumn }" :key="slotItem">
            <slot :name="slotItem" :slotColumn="slotColumn"></slot>
        </template>
        <template #customizePrompt="{ slotColumn }">
            <el-table-column
                :prop="slotColumn.prop"
                :label="slotColumn.label"
                :formatter="slotColumn.formatter"
                :min-width="slotColumn.minWidth"
                :width="slotColumn.width"
                :align="slotColumn.align"
                :header-align="slotColumn.headerAlign"
                :fixed="slotColumn.fixed"
                :type="slotColumn.type"
                :show-overflow-tooltip="showOverflowTooltip"
                :resizable="slotColumn.resizable"
            >
                <template #default="scope">
                    <el-tooltip
                        popper-class="box-item"
                        effect="light"
                        placement="right"
                        :content="handleEightDigit(scope.row[slotColumn.prop ?? ''])"
                        :disabled="showSpanPopover(scope.row[slotColumn.prop ?? ''])"
                    >
                        <span class="box-item-text"  
                              :title="isErp ? formatClipPointNum(scope.row[slotColumn.prop ?? '']) : (handleEightDigit(scope.row[slotColumn.prop ?? '']) == formatClipPointNum(scope.row[slotColumn.prop ?? ''])?formatClipPointNum(scope.row[slotColumn.prop ?? '']):'')">
                            {{ formatClipPointNum(scope.row[slotColumn.prop ?? ""]) }}
                        </span>
                    </el-tooltip>
                </template>
            </el-table-column>
        </template>
    </Table>
</template>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useSlots, ref, watch, nextTick } from "vue";
import { formatClipPointNum } from "@/util/format";

const AccountTable = ref();
const isErp = ref(window.isErp)
const slots = useSlots();
const slotsArr = Object.keys(slots);
const handleEightDigit = (value: string | number) => {
    return value ? (Math.round(Number(value) * Math.pow(10, 8)) / Math.pow(10, 8)).toString() : "";
};

const props = withDefaults(
    defineProps<{
        loading?: boolean;
        data?: Array<any>;
        scrollbarShow?: boolean;
        columns?: Array<IColumnProps>;
        pageIsShow?: boolean;
        emptyText?: string;
        layout?: string;
        pageSizes?: Array<number>;
        pageSize?: number;
        total?: number;
        currentPage?: number;
        tooltipOptions?: any;
        minHeight?: string;
        tableName?: string;
    }>(),
    {
        loading: false,
        data: () => [],
        scrollbarShow: false,
        columns: () => [],
        pageIsShow: false,
        emptyText: "",
        layout: () => "total, sizes, prev, pager, next, jumper",
        pageSizes: () => [1, 2, 10, 20, 30],
        pageSize: () => 20,
        total: () => 0,
        currentPage: () => 1,
        tooltipOptions: () => ({ effect: "light", placement: "top" }),
        tableName: "",
    }
);

const emit = defineEmits<{
    (e: "size-change", value: number): void;
    (e: "current-change", value: number): void;
}>();

const accountBookRef = ref();

const showOverflowTooltip = ref(true);
const handleSizeChange = (val: any) => {
    emit("size-change", val);
};

const handleCurrentChange = (val: any) => {
    emit("current-change", val);
};
const cellMouseEnter = (disabled: boolean) => {
    const tablePopover = document.querySelector(".table .el-popper");
    if (!disabled && tablePopover) {
        (tablePopover as HTMLElement).style.display = "none";
    }
};
const showSpanPopover = (value: number | string | undefined) => {
    if (value === undefined || value === null || isNaN(Number(value))) {
        showOverflowTooltip.value = false;
        return true;
    }
    if (value.toString().split(".")[1] && value.toString().split(".")[1].length > 8) {
        value = Math.round(Number(value) * Math.pow(10, 8)) / Math.pow(10, 8);
    }
    // const parsedValue = Math.floor(Number(value)* Math.pow(10, decimalPlace))/Math.pow(10, decimalPlace)
    const parsedValue = formatClipPointNum(value);
    //
    if (Math.abs(Number(parsedValue)) < Math.abs(Number(value))) {
        if (isErp.value) {
            return true;
        } else {
            showOverflowTooltip.value = false;
            return false;
        }
    } else {
        return true;
    }
};
watch(
    () => props.data,
    () => {
        nextTick(() => {
            AccountTable.value?.tableScrollTo(0, 0);
        });
    }
);
</script>

<style lang="less" scoped>
.box-item-text {
    // display: block;
    // width: 100%;
    // height: 100%;
    // line-height: 36px;
    color: #333;
}
</style>
