<template>
    <div :class="['content', {'erp-content' : isErp}, {'template': tabName === 'checkout' && currentSlot === 'userDefinedTemplate'}, {'voucherAbout': currentSlot === 'voucherAbout'}]">
        <div class="title">期末结转</div>
        <el-tabs v-model="tabName">
            <el-tab-pane label="期末处理" name="checkout" lazy>
                <ContentSlider :slots="slots" :current-slot="currentSlot">
                    <template #main>
                        <div class="main-content">
                            <MainView
                                ref="mainviewRef"
                                :bulkBillingList="bulkBillingList"
                                :checkoutList="checkoutList"
                                :scmAsid="scmAsid"
                                :handleAddReverseList="handleAddReverseList"
                                @go-checkout="loadingToPreCheck"
                                @load-data="handleSearch"
                                @bulk-billing-failed="bulkBillingFailed"
                                @bulkBilling2preCheck="bulkBilling2preCheck"
                                @givePartMainMonth="givePartMainMonth"
                            />
                        </div>
                    </template>
                    <template #preCheck>
                        <div class="slot-content">
                            <PreCheck ref="preCheckRef" @back2main="() => (currentSlot = 'main')" />
                        </div>
                    </template>
                    <template #createPeriodCheck>
                        <div class="slot-content">
                            <CreatePeriodCheck
                                ref="createPeriodCheckRef"
                                :partMainMonth=partMainMonth
                                @create-period-check-last="() => (currentSlot = 'main')"
                                @create-period-check-next="createPeriodCheckNext"
                                @go-user-defined-template="goUserDefinedTemplate"
                                @go-look-vouchers="goLookVouchers"
                                @new-voucher="newVoucher"
                            />
                        </div>
                    </template>
                    <template #lookVouchers>
                        <div class="slot-content">
                            <LookVouchers
                                ref="lookVouchersRef"
                                :resetTotalMoney="resetTotalMoney"
                                @handle-cancel="lookVoucherClose"
                                @new-vouchers="newVoucherFromLookVouchers"
                                @delete-voucher="deleteCarryOver"
                                @edit-voucher="editVoucherFromLookVoucher"
                            />
                        </div>
                    </template>
                    <template #userDefinedTemplate>
                        <div class="slot-content">
                            <UserDefinedTemplate
                                ref="userDefinedTemplateRef"
                                @delete-template="goUserDefinedTemplate"
                                @back-to-create-period-check="backToCreatePeriodCheck"
                                @success-save="goUserDefinedTemplate"
                            />
                        </div>
                    </template>
                    <template #carryOverVoucher>
                        <div class="slot-content">
                            <CarryOverVoucher
                                ref="carryOverVoucherRef"
                                :pageChange="page2CarryOverVoucher"
                                :getVDate="getVDate"
                                :as-theme="asTheme"
                                :pid="currentPID"
                                @back="handleCarryOverVoucherBack"
                                @list-back="() => (currentSlot = 'carryOver')"
                                @save-success="handleCarryOverVoucherSaveSuccess"
                                @year-save-success="handleCarryOverVoucherYearSaveSuccess"
                                @year-not-save="carryOverIncomeChangeNext(false)"
                            />
                        </div>
                    </template>
                    <template #carryOver>
                        <div class="slot-content">
                            <CarryOver
                                ref="carryOverRef"
                                :asTheme="asTheme"
                                :resetTotalMoney="resetTotalMoney"
                                @carry-over-last="() => (currentSlot = 'createPeriodCheck')"
                                @edit-carry-over="editCarryOver"
                                @carry-over-next="carryOverNext"
                                @carry-over="handleCarryOver"
                                @carry-over-finish="carryOverFinish"
                                @open-start-bulkBilling="openStartBulkBilling"
                            />
                        </div>
                    </template>
                    <template #carryOverIncomeChange>
                        <div class="slot-content">
                            <CarryOverIncomeChange
                                ref="carryOverIncomeChangeRef"
                                :resetTotalMoney="resetTotalMoney"
                                @carry-over-last="handleCarryOverIncomeChangeBack"
                                @edit-carry-over="editCarryOverIncomeChange"
                                @carry-over-next="carryOverIncomeChangeNext"
                                @new-voucher="handleCarryOverVoucherNewVoucher"
                            />
                        </div>
                    </template>
                    <template #voucherAbout>
                        <div class="slot-content">
                            <VoucherAbout
                                :pid="currentPID"
                                :partMainMonth=partMainMonth
                                :toVoucherAbout="toVoucherAbout"
                                ref="voucherAboutRef"
                                @back-from-voucher="backFromVoucher"
                                @delete-voucher="deleteVoucherAbout"
                            />
                        </div>
                    </template>
                    <template #checkout>
                        <div class="slot-content">
                            <Checkout
                                ref="checkoutRef"
                                :pid="currentPID"
                                :as-theme="asTheme"
                                @checkout-last="checkoutLast"
                                @re-check="handleReCheck"
                                @go-checkout="goCheckout"
                                @back-to-main="backToMain"
                                v-loading="checkoutErploading"
                                :element-loading-text="isErp ? '正在结账中...' : ''"
                            />
                        </div>
                    </template>
                </ContentSlider>
            </el-tab-pane>
            <el-tab-pane label="反结账" name="checkoutReverse">
                <CheckoutReverse :checkout-list-reverse="checkoutListReverse" @reverse-success="reverseSuccess" />
            </el-tab-pane>
        </el-tabs>
        <el-dialog title="" center width="440px" v-model="bossPermissionsShow" :show-close="false" class="dialogDrag">
            <div id="CreateAccountOverFlow" title="" class="company-container" v-dialogDrag>
                <div class="company-notice">
                    <div class="all-line mt-20">
                        <p class="text-header">恭喜您结账成功！</p>
                        <p class="text-content">已为您智能生成一份老板能看懂的的报表</p>
                        <img src="@/assets/Icons/close.png" @click="() => (bossPermissionsShow = false)" class="close" alt="返回" />
                    </div>
                    <div class="all-line">
                        <img src="@/assets/Settings/0.png" alt="老板看账" />
                    </div>
                    <div class="all-line mb-20">
                        <a class="btn button solid-button normal-text" @click="closeModal">一键授权老板看账</a>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            title="请稍候"
            center
            width="300px"
            v-model="checkoutLoadShow"
            :show-close="false"
            class="checkout_progress"
            :draggable="false"
        >
            <div class="checkout_dialog">
                <div class="checkout_content">
                    <p>正在结账...</p>
                    <el-progress color="#84909c" :stroke-width="20" :percentage="percentage" :text-inside="true">
                        <span class="progress_content">小柠檬正在努力结账中...</span>
                        <span class="progress_contentblack">小柠檬正在努力结账中...</span>
                    </el-progress>
                </div>
            </div>
        </el-dialog>
    </div>
    <ConfirmWithoutGlobal
        v-model="confirmWithoutGlobalVisible"
        :show-close="false"
        confirmButtonText="编辑原凭证"
        cancelButtonText="进入新页面"
        :cancel-click="reLoadCurrentPage"
    >
        <div style="text-align: left; margin: 0 -30px">
            您之前编辑的凭证还没有保存<br />点击'编辑原凭证'则可继续编辑原凭证<br />点击'进入新页面'则原凭证将不会保存并进入期末结转页面
        </div>
    </ConfirmWithoutGlobal>
</template>

<script lang="ts">
export default {
    name: "Checkout",
};
</script>
<script setup lang="ts">
import { ref, onActivated, toRef, computed, watch, provide } from "vue";
import { useRoute, onBeforeRouteLeave, useRouter } from "vue-router";
import { getGlobalToken } from "@/util/baseInfo";
import { ElConfirm } from "@/util/confirm";
import { type IResponseModel, request } from "@/util/service";
import { formatPeriodData, handleSetOpPermission, PermissionModel, conbineincomeRows } from "./utils";
import { ElNotify } from "@/util/notify";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { getPeriodsApi, type IPeriod } from "@/api/period";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";

import type {
    ICheckoutItem,
    ICheckoutPageItem,
    ICheckResultItem,
    IVoucherLineTemplates,
    ITemplateVoucherLines,
    INewSalesCostInfoRow,
    ICarryOverBack,
    IPreCheckList,
} from "./tpyes";
import { VoucherEntryModel } from "@/components/Voucher/types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import CheckoutReverse from "./components/CheckoutReverse.vue";
import MainView from "./components/MainView.vue";
import CreatePeriodCheck from "./components/CreatePeriodCheck.vue";
import CarryOver from "./components/CarryOver.vue";
import Checkout from "./components/Checkout.vue";
import VoucherAbout from "./components/VoucherAbout.vue";
import UserDefinedTemplate from "./components/UserDefinedTemplate.vue";
import LookVouchers from "./components/LookVouchers.vue";
import CarryOverIncomeChange from "./components/CarryOverIncomeChange.vue";
import CarryOverVoucher from "./components/CarryOverVoucher.vue";
import PreCheck from "./components/PreCheck.vue";
import { nextTick } from "vue";
import { getUrlSearchParams, globalWindowOpenPage, reloadPeriodInfo } from "@/util/url";
import { isInWxWork } from "@/util/wxwork";
import { useLoading } from "@/hooks/useLoading";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { onMounted } from "vue";
import ConfirmWithoutGlobal from "@/components/ConfirmWithoutGlobal/index.vue";
import { onUnmounted } from "vue";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { getGlobalLodash } from "@/util/lodash";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { useScmInfoStore } from "@/store/modules/scm";
import { reloadAccountSetList } from "@/util/accountset";
const _ = getGlobalLodash()
const confirmWithoutGlobalVisible = ref(false);

interface ICheckoutPeriod extends IPeriod {
    greenLocked?: boolean;
    statusByGen?: number;
}

const createPeriodCheckRef = ref<InstanceType<typeof CreatePeriodCheck>>();
const userDefinedTemplateRef = ref<InstanceType<typeof UserDefinedTemplate>>();
const carryOverRef = ref<InstanceType<typeof CarryOver>>();
const checkoutRef = ref<InstanceType<typeof Checkout>>();
const voucherAboutRef = ref<InstanceType<typeof VoucherAbout>>();
const lookVouchersRef = ref<InstanceType<typeof LookVouchers>>();
const carryOverIncomeChangeRef = ref<InstanceType<typeof CarryOverIncomeChange>>();
const carryOverVoucherRef = ref<InstanceType<typeof CarryOverVoucher>>();
const preCheckRef = ref<InstanceType<typeof PreCheck>>();
const router = useRouter();

const isErp = ref(window.isErp);
const slots = [
    "main",
    "createPeriodCheck",
    "lookVouchers",
    "userDefinedTemplate",
    "carryOver",
    "carryOverIncomeChange",
    "carryOverVoucher",
    "voucherAbout",
    "checkout",
    "preCheck",
];
const currentSlot = ref("main");
const bossPermissionsShow = ref(false);
const tabName = ref("checkout");
const currentPID = ref(0);
const currentVID = ref(0);
const periodData = ref<IPeriod[]>([]);
const checkoutList = ref<ICheckoutItem[]>([]);
const checkoutListReverse = ref<ICheckoutItem[]>([]);
const bulkBillingList = ref<IPeriod[]>([]);
const voucherLineTemplates = ref<IVoucherLineTemplates>();
const partMainMonth=ref('')
const accountPeriodStore = useAccountPeriodStore();
const voucherGroupStore = useVoucherGroupStore();

const accStandard = ref(useAccountSetStore().accountSet?.accountingStandard as number);
const asTheme = accStandard.value % 3 === 0 ? "净资产" : "损益";
const asThemeInfo = accStandard.value === 3 ? "收入或费用" : accStandard.value === 6 ? "收入或支出" : "损益";
const mainviewRef = ref();
const handleAddReverseList = (item: ICheckoutPeriod) => {
    const year = item.year + "";
    const backupItem = JSON.parse(JSON.stringify(item));
    const thisYearObj = checkoutListReverse.value.find((i) => Object.keys(i)[0] === year);
    if (thisYearObj) {
        thisYearObj[year].unshift(backupItem);
    } else {
        checkoutListReverse.value.unshift({
            [year]: [backupItem],
        });
    }
};
const givePartMainMonth = (yearMonth:string) => {
    partMainMonth.value=yearMonth
}
const getVoucherLineTemplates = () => {
    request({ url: "/api/Checkout/GetVoucherLineTemplates", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            if (window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) !== null) {
                window.localStorage.removeItem("voucherLineTemplates-" + getGlobalToken());
            }
            window.localStorage.setItem("voucherLineTemplates-" + getGlobalToken(), JSON.stringify(res.data));
            voucherLineTemplates.value = res.data;
        }
    });
};
const getAsubCodeLength = () => {
    request({ url: "/api/AccountSubject/GetAsubCodeLength", method: "post" }).then((res: any) => {
        if (res.state === 1000) {
            const codeLength = res.data.codeLength;
            if (window.localStorage.getItem("codeLength-" + getGlobalToken()) !== null) {
                window.localStorage.removeItem("codeLength-" + getGlobalToken());
            }
            window.localStorage.setItem("codeLength-" + getGlobalToken(), JSON.stringify(codeLength));
        }
    });
};
const GetPeriodList = () => {
    return request({
        url: "/api/Checkout/GetPeriodList",
        method: "post",
    });
}
function mergeCommonList(periodRows: ICheckoutPeriod[]) {
    const minCanClickPeriodList = periodRows.filter((item) => item.status === 1);
    minCanClickPeriodList.map((item) => {
        item.greenLocked = true;
    });
    const minCanClickPid = Math.min(...minCanClickPeriodList.map((item) => item.pid));
    periodRows.forEach((item) => {
        if (item.status === 1) {
            item.greenLocked = true;
            if (item.pid === minCanClickPid) {
                item.greenLocked = false;
            }
        }
        if (item.status === 2 && item.statusByGen === 1) {
            item.greenLocked = true;
        }
    });
}
const scmInfoStore = useScmInfoStore();
const scmAsid = ref(0);
function handleGetScmInfo() {
    scmInfoStore.handleGetScmRelationInfo().then(() => {
        scmAsid.value = scmInfoStore.scmAsid;
    });
}
const handleInit = () => {
    if (window.localStorage.getItem("opPermission") !== null) window.localStorage.removeItem("opPermission");
    GetPeriodList().then((res) => {
        const periodRows: ICheckoutPeriod[] = res.data;
        mergeCommonList(periodRows);
        periodData.value = periodRows;
        checkoutList.value = formatPeriodData(periodData.value);
        checkoutListReverse.value = formatPeriodData(periodData.value.filter((item) => item.status === 3));
        bulkBillingList.value = periodData.value.filter((item) => item.status !== 3);
        handleSetOpPermission(periodRows);
        getVoucherLineTemplates();
        getAsubCodeLength();
        handleGetScmInfo();
    });
};
handleInit();

const handleSearch = async () => {
    await GetPeriodList().then((res: any) => {
        if (res.state == 1000) {
            if (window.localStorage.getItem("opPermission") !== null) window.localStorage.removeItem("opPermission");
            const periodRows: ICheckoutPeriod[] = res.data;
            mergeCommonList(periodRows);
            periodData.value = periodRows;
            checkoutList.value = formatPeriodData(periodData.value);
            checkoutListReverse.value = formatPeriodData(periodData.value.filter((item) => item.status === 3));
            bulkBillingList.value = periodData.value.filter((item) => item.status !== 3);
            handleSetOpPermission(periodRows);
        }
    });
};

// 批量结账失败
const bulkBillingFailed = (checkList: ICheckoutPageItem[], pid: number) => {
    checkoutRef.value?.setData(checkList, false, "");
    currentPID.value = pid;
    currentSlot.value = "checkout";
};
const bulkBilling2preCheck = (pid: number) => {
    currentPID.value = pid;
    getPreCheckHandlerApi(currentPID.value).then((res: any) => {
        if (res.state != 1000) return;
        const rdata: IPreCheckList[] = res.data;
        const isPassed = rdata.every((item) => item.isPassed);
        if (!isPassed) {
            preCheckRef.value?.handleInit(currentPID.value, rdata);
            currentSlot.value = "preCheck";
        }
    });
};
const loadingToPreCheck = (pid: number) => {
    goCreatePeriodCheck(pid, false, true);
};
const goCreatePeriodCheck = (PID: number, backPathIsLookVoucher = false, loading = false) => {
    if (window.localStorage.getItem("isLastMouth") !== null) {
        window.localStorage.removeItem("isLastMouth");
    }
    currentPID.value = PID;
    if (loading) {
        useLoading().enterLoading("数据加载中，请稍候...");
    }
    getPreCheckHandlerApi(currentPID.value).then((res: any) => {
        if (res.state != 1000) return;
        const rdata: IPreCheckList[] = res.data;
        const isPassed = rdata.every((item) => item.isPassed);
        if (!isPassed) {
            if (loading) {
                useLoading().quitLoading();
            }
            preCheckRef.value?.handleInit(currentPID.value, rdata);
            currentSlot.value = "preCheck";
            return;
        }
        getCheckPresetItemForCheckoutApi(currentPID.value).then((res: any) => {
            createPeriodCheckRef.value?.setCheckPresetItem(res.data, currentPID.value);
            window.localStorage.setItem("isLastMouth", JSON.stringify(res.data[0].isLastMouth));
            const needLoadingList: ICheckResultItem[] = res.data.filter((item: ICheckResultItem) => item.checktype === 99);
            if (needLoadingList.length === 0) {
                nextTick(() => {
                    if (loading) {
                        useLoading().quitLoading();
                    }
                    currentSlot.value = "createPeriodCheck";
                });
                return;
            }
            let count = 0;
            needLoadingList.forEach((item) =>
                getCustomVoucherTemplateRowsApi(currentPID.value, item.vtId)
                    .then((res: any) => {
                        count++;
                        createPeriodCheckRef.value?.setItemInfoFor99(res.data);
                        if (count === needLoadingList.length) {
                            if (loading) {
                                useLoading().quitLoading();
                            }
                            if (!backPathIsLookVoucher) {
                                currentSlot.value = "createPeriodCheck";
                            } else {
                                const editCheckoutLookVoucherInfo = JSON.parse(
                                    window.localStorage.getItem("editCheckoutLookVoucher-" + getGlobalToken()) || "{checktype: 0, vtId: 0}"
                                );
                                nextTick().then(() => {
                                    const createPageInfo = createPeriodCheckRef.value?.getCreatePageCardInfo();
                                    const createPageInfoItem = createPageInfo?.find(
                                        (item) =>
                                            item.checktype === editCheckoutLookVoucherInfo.checktype &&
                                            item.vtId === editCheckoutLookVoucherInfo.vtId
                                    );
                                    if (createPageInfoItem) {
                                        goLookVouchers(createPageInfoItem);
                                    }
                                });
                            }
                        }
                    })
                    .catch(() => {
                        if (loading) {
                            useLoading().quitLoading();
                        }
                        currentSlot.value = "createPeriodCheck";
                        return;
                    })
            );
        });
    });
};
const goUserDefinedTemplate = () => {
    request({ url: "/api/CustomCarryOverTemplate/List" }).then((res: any) => {
        if (res.state != 1000) return;
        userDefinedTemplateRef.value?.setData(res.data);
        currentSlot.value = "userDefinedTemplate";
    });
};
const backToCreatePeriodCheck = () => {
    goCreatePeriodCheck(currentPID.value);
};
const lookVoucherClose = () => {
    goCreatePeriodCheck(currentPID.value);
};
let lookVoucherCardInfo: ICheckResultItem | undefined;
const goLookVouchers = (row: ICheckResultItem) => {
    if (lookVoucherCardInfo) lookVoucherCardInfo = undefined;
    lookVoucherCardInfo = row;
    const list: any[] = [];
    list.push(row);
    lookVouchersRef.value?.setData(list);
    currentSlot.value = "lookVouchers";
};
interface IExtendInfoRow {
    assistingaccounting: string;
    assistsetting: string;
    asub_code: string;
    asub_id: string;
    asub_name: string;
    credit: string;
    debit: string;
    description: string;
    fc_amount: string;
    fc_id: number;
    fc_rate: string;
    foreigncurrency: number;
    measureunit: string;
    price: string;
    quantity: string;
    quantityaccounting: string;
}

const voucherLineInfoMapToVoucherEntryModel = (list: ITemplateVoucherLines[] | IExtendInfoRow[]) => {
    const voucherLines: VoucherEntryModel[] = [];
    for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new VoucherEntryModel();
        model.veId = 0;
        model.fcCode = "";
        model.asubType = 0;
        model.assistingAccounting = Number(item.assistingaccounting);
        model.asubCode = item.asub_code;
        model.asubId = Number(item.asub_id);
        model.asubName = item.asub_name;
        model.asubAAName = item.asub_name;
        model.debit = Number(item.debit.replace(/,/g, ""));
        model.credit = Number(item.credit.replace(/,/g, ""));
        model.description = item.description;
        model.fcAmount = Number(item.fc_amount.replace(/,/g, ""));
        model.fcId = item.fc_id;
        model.fcRate = Number(item.fc_rate.replace(/,/g, ""));
        model.foreigncurrency = item.foreigncurrency;
        model.measureUnit = item.measureunit;
        model.price = Number(item.price.replace(/,/g, ""));
        model.quantity = Number(item.quantity.replace(/,/g, ""));
        model.quantityAccounting = Number(item.quantityaccounting);
        model.aacode = item.assistsetting;
        model.isFaLine = false;
        voucherLines.push(model);
    }
    return voucherLines;
};
const carryOverSalesCostMapToVoucherEntryModel = (list: INewSalesCostInfoRow[]) => {
    const voucherLines: VoucherEntryModel[] = [];
    for (let i = 0; i < list.length; i++) {
        const item = list[i];
        const model = new VoucherEntryModel();
        model.veId = item.veId;
        model.fcCode = item.fcCode;
        model.asubType = item.asubType; //之前默认是0
        model.assistingAccounting = item.assistingAccounting;
        model.asubCode = item.asubCode;
        model.asubId = item.asubId;
        model.asubName = item.asubName;
        model.asubAAName = item.asubName;
        model.debit = item.debit;
        model.credit = item.credit;
        model.description = item.description;
        model.fcAmount = item.fcAmount;
        model.fcId = item.fcId;
        model.fcRate = item.fcRate;
        model.foreigncurrency = item.foreigncurrency;
        model.measureUnit = item.measureUnit;
        model.price = item.price;
        model.quantity = item.quantity;
        model.quantityAccounting = item.quantityAccounting;
        model.aacode = item.aacode;
        model.isFaLine = false;
        voucherLines.push(model);
    }
    return voucherLines;
};
const newVoucher = (data: ICheckResultItem) => {
    const defaultVgId = voucherGroupStore.defaultVgId;
    const vdate = getVDate();
    const thisData = JSON.parse(JSON.stringify(data));

    if (thisData.cardInfo.type === 2) {
        if (thisData.checktype === 0) {
            const newVoucherInfo: INewSalesCostInfoRow[] = thisData.newVoucherInfo;
            const voucherLines: VoucherEntryModel[] = carryOverSalesCostMapToVoucherEntryModel(newVoucherInfo);
            voucherAboutRef.value?.newSetData(voucherLines, defaultVgId, vdate, "createPeriodCheck", thisData.cardInfo.vtype);
            return;
        }
        const newVoucherInfo: ITemplateVoucherLines[] = thisData.newVoucherInfo;
        if (thisData.checktype === 20) {
            voucherAboutRef.value?.newSetData(
                newVoucherInfo as unknown as VoucherEntryModel[],
                defaultVgId,
                vdate,
                "createPeriodCheck",
                thisData.cardInfo.vtype
            );
            return;
        }
        if ([310,320, 321, 322].includes(thisData.checktype)) {
            voucherAboutRef.value?.newSetData(data.newVoucherInfo, defaultVgId, vdate, "createPeriodCheck", thisData.cardInfo.vtype, data.vtId, data.attachFiles);
            return;
        }
        const voucherLines: VoucherEntryModel[] = voucherLineInfoMapToVoucherEntryModel(newVoucherInfo);
        voucherAboutRef.value?.newSetData(voucherLines, defaultVgId, vdate, "createPeriodCheck", thisData.cardInfo.vtype);
    } else if (thisData.cardInfo.type === 3) {
        if ([310,320, 321, 322].includes(thisData.checktype)) {
            voucherAboutRef.value?.newSetData(data.newVoucherInfo, defaultVgId, vdate, "createPeriodCheck", thisData.cardInfo.vtype, data.vtId, data.attachFiles);
            return;
        }
        if (!thisData.extendInfo) return;
        const vtype = thisData.extendInfo.model.vtType;
        const voucherLines: VoucherEntryModel[] = voucherLineInfoMapToVoucherEntryModel(thisData.extendInfo.rows);
        voucherAboutRef.value?.newSetData(voucherLines, thisData.extendInfo.model.vgId, vdate, "createPeriodCheck", vtype ?? 0);
    }
};
const isNormalBack = ref(true);
const carryOverVoucherTime = ref(0);
provide("carryOverVoucherTime", carryOverVoucherTime);
watch(currentSlot, (val) => {
    if (val !== "carryOverVoucher" && val !== "carryOver") {
        carryOverVoucherTime.value = 0;
    }
    if (val === "createPeriodCheck") {
        window.addEventListener("changeSalaryMultiStatus", backToCreatePeriodCheck);
    }
});
const createPeriodCheckNext = () => {
    request({ url: "/api/CarryOver/GetCheckResultList?pid=" + currentPID.value, method: "post" }).then((res: any) => {
        if (res.state != 1000) return;
        const rdata = res.data;
        (function () {
            for (let i = 0; i < rdata.length; i++) {
                if (rdata[i].status == 0) {
                    carryOverVoucherTime.value = 1500;
                    carryOverVoucherRef.value?.reset();
                    if (accStandard.value === AccountStandard.FolkComapnyStandard) {
                        carryOverVoucherRef.value?.dealWithFolkCheckOutCreateVouchers(true, rdata);
                    } else if (accStandard.value === AccountStandard.UnionStandard) {
                        carryOverVoucherRef.value?.dealWithUnionCheckOutCreateVouchers(rdata);
                    } else {
                        carryOverVoucherRef.value?.dealWithCheckOutCreateVouchers(rdata);
                    }
                    currentSlot.value = "carryOverVoucher";
                    break;
                } else if (rdata[i].status == 1) {
                    carryOverRef.value?.setData(res.data);
                    carryOverRef.value?.setDealWithType("checkOutList");
                    currentSlot.value = "carryOver";
                    window.dispatchEvent(new CustomEvent("peroidListChange"));
                    break;
                } else {
                    const opPermission: PermissionModel[] = JSON.parse(localStorage.getItem("opPermission") ?? "") || [];
                    for (let i = 0; i < opPermission.length; i++) {
                        if (opPermission[i].pid == currentPID.value) {
                            if (opPermission[i].changeout && opPermission[i].checkout) {
                                const isLastMonth = JSON.parse(window.localStorage.getItem("isLastMouth") ?? "false");
                                if (isLastMonth && accStandard.value !== AccountStandard.FolkComapnyStandard) {
                                    isNormalBack.value = false;
                                    GetIncomeResult(false);
                                } else {
                                    ///期间不需要进行结转损益操作
                                    carryOverRef.value?.setDealWithType("noNeedDoNothing");
                                    StoreChangeOut(false, () => {
                                        currentSlot.value = "carryOver";
                                    });
                                }
                            } else {
                                if (opPermission[i].changeout) {
                                    carryOverRef.value?.setDealWithType("noNeedDoNothingIsNotFirstMonth");
                                    currentSlot.value = "carryOver";
                                } else {
                                    //无须操作
                                    ElNotify({ type: "warning", message: "此期间已经结转" + asTheme + "，请先将之前期间结账" });
                                }
                            }
                            break;
                        }
                    }
                }
            }
        })();
    });
};

const carryOverFinish = () => {
    StoreChangeOut(
        false,
        () => {
            handleInit();
            currentSlot.value = "main";
        },
        false
    );
};
const openStartBulkBilling = () => {
    currentSlot.value = "main";
    const year = partMainMonth.value.slice(0,4)
    const month = partMainMonth.value.slice(partMainMonth.value[4] === '0' ? 5 : 4);
    setTimeout(()=>{
        mainviewRef.value.startBulkBilling(Number(year),Number(month));
    },1000)
};
const handleReCheck = (baseBackPath: boolean, normalType: boolean) => {
    getLastCheckForCheckout(baseBackPath, normalType);
};
const getLastCheckForCheckout = (baseBackPath = true, normalType = true): Promise<boolean> => {
    const params: any = { pid: currentPID.value };
    if (window.isErp) params.scmAsid = scmAsid.value;
    return request({ url: "/api/Checkout/LastCheck?" + getUrlSearchParams(params), method: "post" })
        .then((res: any) => {
            if (res.state != 1000) return false;
            const path = !normalType ? "" : baseBackPath ? "carryOver" : "carryOverIncomeChange";
            checkoutRef.value?.setData(res.data, normalType, path);
            return true;
        })
        .catch(() => {
            return false;
        });
};

const carryOverIncomeChangeNext = (needCheckYear = true) => {
    const opPermission: PermissionModel[] = JSON.parse(localStorage.getItem("opPermission") ?? "") || [];
    //检查现有的期间权限,如果
    for (let i = 0; i < opPermission.length; i++) {
        if (opPermission[i].pid == currentPID.value) {
            //可以结账的月份既可以结账也可以不结账只结转损益
            if (opPermission[i].changeout && opPermission[i].checkout) {
                // 正常到最后一个页面
                checkAllChangeFinish(
                    function () {
                        Promise.all([StoreChangeOut(false), getLastCheckForCheckout(false)]).then(() => {
                            currentSlot.value = "checkout";
                            setTimeout(() => {
                                carryOverVoucherRef.value?.reset();
                            }, 500);
                        });
                    },
                    function () {
                        ElConfirm("您还有" + asThemeInfo + "没有结转完，请点击结转" + asTheme, true);
                    }, needCheckYear
                );
            } else {
                if (opPermission[i].changeout) {
                    checkAllChangeFinish(
                        function () {
                            StoreChangeOut(false).then((result: boolean) => {
                                if (result) {
                                    ElConfirm("本期已结转" + asTheme + `，由于上期还没有结账，无法进行下一步，请先将上期结账哦~<br/><div style='color:red;'>您可以点击确认进行批量结账操作<div/>`).then(
                                        (r: any) => {
                                            if (r) openStartBulkBilling();
                                            return;
                                        }
                                    );
                                }
                            });
                        },
                        function () {
                            ElConfirm("您还有" + asTheme + "没有结转完，请点击结转" + asTheme, true);
                        }, needCheckYear
                    );
                } else {
                    //无须操作
                }
            }
            break;
        }
    }
};
const carryOverNext = (dealWithType: "noNeedDoNothing" | "checkOutList") => {
    if (dealWithType === "noNeedDoNothing") {
        getLastCheckForCheckout().then((r: boolean) => {
            if (r) {
                currentSlot.value = "checkout";
            }
        });
        return;
    }
    const opPermission: PermissionModel[] = JSON.parse(localStorage.getItem("opPermission") ?? "") || [];
    //检查现有的期间权限,如果
    for (let i = 0; i < opPermission.length; i++) {
        if (opPermission[i].pid == currentPID.value) {
            //可以结账的月份既可以结账也可以不结账只结转损益
            if (opPermission[i].changeout && opPermission[i].checkout) {
                //结转未分配利润这个状态现在是通过结转销售成本和计提工资来赋值的，如果没有这两个卡片，则需要在有出现的卡片增加对该状态的赋值
                const isLastMonth: boolean = JSON.parse(window.localStorage.getItem("isLastMouth") ?? "false");
                if (isLastMonth && accStandard.value !== AccountStandard.FolkComapnyStandard) {
                    checkAllChangeFinish(
                        function () {
                            GetIncomeResult();
                        },
                        function () {
                            ElConfirm("您还有" + asThemeInfo + "没有结转完，请点击结转" + asTheme, true);
                        }
                    );
                } else {
                    // 正常到最后一个页面
                    checkAllChangeFinish(
                        function () {
                            Promise.all([StoreChangeOut(false), getLastCheckForCheckout()]).then(() => {
                                currentSlot.value = "checkout";
                            });
                        },
                        function () {
                            ElConfirm("您还有" + asThemeInfo + "没有结转完，请点击结转" + asTheme, true);
                        }
                    );
                }
            } else {
                if (opPermission[i].changeout) {
                    checkAllChangeFinish(
                        function () {
                            StoreChangeOut(false).then((result: boolean) => {
                                if (result) {
                                    ElConfirm("本期已结转" + asTheme + `，由于上期还没有结账，无法进行下一步，请先将上期结账哦~<br/><div style='color:red;'>您可以点击确认进行批量结账操作<div/>`).then(
                                        (r: any) => {
                                            if (r) openStartBulkBilling();
                                            return;
                                        }
                                    );
                                }
                            });
                        },
                        function () {
                            ElConfirm("您还有" + asTheme + "没有结转完，请点击结转" + asTheme, true);
                        }
                    );
                } else {
                    //无须操作
                }
            }
            break;
        }
    }
};
const checkAllChangeFinish = (successCallback: Function, failCallback: Function, needCheckYear = false) => {
    IsFinishApi()
        .then((res: any) => {
            if (res.state === 1000 && res.data) {
                if(needCheckYear) {
                    request({ url: "/api/CarryOver/IsYearEndProfitFinish?pid=" + currentPID.value, method: "post"})
                    .then((res: IResponseModel<boolean>) => {
                        if (res.state === 1000 ){
                            res.data ? successCallback() : GetIncomeResult();
                        } else {
                            ElNotify({
                                type: "warning",
                                message: res.msg || "亲，出错了，请刷新页面重试或联系客服",
                            });
                        }
                    }).catch(() => {
                        ElNotify({
                            type: "warning",
                            message: "亲，出错了，请刷新页面重试或联系客服",
                        });
                    });
                }else {
                    successCallback();
                }
            } else {
                failCallback();
            }
        })
        .catch(() => {
            failCallback();
        });
        
};
const StoreChangeOut = function (flag: boolean, callback?: Function, needTip = true): Promise<boolean> {
    const errorHanldle = () => {
        ElNotify({ type: "warning", message: "结转" + asTheme + "失败，请检查后重新提交" });
    };
    return storeChangeOutHandlerApi()
        .then((res: any) => {
            if (res.state != 1000) {
                errorHanldle();
                return false;
            }
            if (res.data === true) {
                if (flag) {
                    // displaySuccess("ChagneFinish");
                } else {
                    //  application.checkouProcess("close");
                    needTip && ElNotify({ type: "success", message: "结转" + asTheme + "已经完成" });
                    callback && callback();
                }
                return true;
            } else {
                // application.checkouProcess("close");
                errorHanldle();
                return false;
            }
        })
        .catch(() => {
            errorHanldle();
            return false;
        });
};
const checkoutLoadShow = ref(false);
const percentage = ref(0);
let checkoutTimer: any = null;
const checkoutLoading = () => {
    checkoutTimer = setInterval(() => {
        percentage.value = (percentage.value % 100) + 10;
    }, 500);
};
const checkoutErploading = ref(false);
const goCheckout = () => {
    if (isErp.value) {
        checkoutErploading.value = true;
    } else {
        checkoutLoadShow.value = true;
        checkoutLoading();
    }
    request({ url: "/api/Checkout/Checkout?pid=" + currentPID.value, method: "post" })
        .then(async (res: any) => {
            if (res.state == 1000 && res.data) {
                await handleSearch();
                reloadPeriodInfo();
                isErp.value && useAccountPeriodStore().getPeriods();
                if (isInWxWork() || window.isAccountingAgent || isErp.value) {
                    ElNotify({ type: "success", message: "结账成功" });
                    currentSlot.value = "main";
                    handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, model: "checkout", frequency: "month", needToStore: true });
                } else {
                    request({ url: "/api/BossPermissions/QueryBossFlag", method: "post" }).then((res: any) => {
                        if (res.state != 1000 || res.data === null) {
                            ElNotify({ type: "warning", message: "请求失败！" });
                            return;
                        }
                        res.data === "Success" ? ElNotify({ type: "success", message: "结账成功" }) : (bossPermissionsShow.value = true);
                        reloadAccountSetList();
                        currentSlot.value = "main";
                        handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, model: "checkout", frequency: "month", needToStore: true });
                    });
                }
                thirdPartNotify(thirtPartNotifyTypeEnum.checkoutCheckout, {
                    pid: currentPID.value,
                }).then(() => {});
            } else {
                ElNotify({ type: "warning", message: res.msg || "结账失败，请稍后再试" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "结账失败，请稍后再试" });
        })
        .finally(() => {
            clearInterval(checkoutTimer);
            window.dispatchEvent(new CustomEvent("peroidListChange"));
            checkoutErploading.value = false;
            checkoutLoadShow.value = false;
            percentage.value = 0;
        });
};
const editCarryOver = (VID: number) => {
    currentVID.value = VID;
    carryOverVoucherTime.value = 1500;
    voucherAboutRef.value?.setData(currentPID.value, VID);
};
const editCarryOverIncomeChange = (VID: number) => {
    currentVID.value = VID;
    voucherAboutRef.value?.setData(currentPID.value, VID, "carryOverIncomeChange");
};
const deleteCarryOver = (PID:number, VID: number, successCallback: Function) => {
    request({ url: "/api/Voucher?pId=" + PID + "&vId=" + VID }).then((res: any) => {
        if (res.state != 1000 || !res.data) {
            ElNotify({ type: "warning", message: "删除失败" });
            return;
        }
        if (res.data.approveStatus == 1) {
            ElNotify({ type: "warning", message: "亲！已审核凭证不能进行删除" });
            return;
        }
        ElConfirm("亲，确认要删除吗?").then((r: any) => {
            if (r) {
                request({ url: "/api/Voucher?pId=" + PID + "&vId=" + VID, method: "delete" }).then((res: any) => {
                    if (res.state != 1000 || !res.data) return;
                    resetTotalMoney();
                    ElNotify({ type: "success", message: "删除成功" });
                    successCallback();
                });
            }
        });
    });
};
const resetTotalMoney = () => {
    voucherAboutRef.value?.resetTotalMoney();
    carryOverVoucherRef.value?.resetTotalMoney();
};
const handleCarryOver = () => {
    checkAllChangeFinish(
        function () {
            const normal = accStandard.value !== AccountStandard.FolkComapnyStandard;
            const baseMsg =
                "<div style='text-align: left;'>本期所有的" + asTheme + "都已经结转完毕了，您可以点击下一步按钮继续进行结账操作。";
            const appendMsg =
                "</div><div style='text-align: left;color: var(--font-color);font-size: var(--h5);line-height: 17px;margin-top: 10px;text-align: left;width: 100%;padding: 0;white-space: normal;'>您确认您还需要继续结转" +
                asTheme +
                "吗？</div>";
            ElConfirm(normal ? baseMsg + appendMsg : baseMsg, !normal).then((r: any) => {
                // 新增凭证
                if (r) {
                    if (!normal) return;
                    const vdate = getVDate();
                    const defaultVgId = voucherGroupStore.defaultVgId;
                    voucherAboutRef.value?.newVoucher(vdate, defaultVgId, true, "第 2 步 结转" + asTheme, "carryOver");
                }
            });
        },
        function () {
            // 根据现有 voucherLines 创造凭证
            request({ url: "/api/CarryOver/GetCheckResultListExistsVoucher?pid=" + currentPID.value, method: "post" }).then((res: any) => {
                if (res.state != 1000) return;
                const rdata: ICarryOverBack[] = res.data;
                carryOverVoucherTime.value = 1500;
                for (let i = 0; i < rdata.length; i++) {
                    if (accStandard.value === AccountStandard.FolkComapnyStandard) {
                        if (rdata[i].status == 0) {
                            carryOverVoucherRef.value?.dealWithFolkCheckOutExistsVoucher(false, rdata);
                            currentSlot.value = "carryOverVoucher";
                            break;
                        }
                    } else if (accStandard.value === AccountStandard.UnionStandard) {
                        if (rdata[i].status == 0) {
                            carryOverVoucherRef.value?.dealWithUnionCheckOutCreateVouchers(rdata);
                            currentSlot.value = "carryOverVoucher";
                            break;
                        }
                    } else {
                        if (rdata[i].status == 0) {
                            const cr = conbineincomeRows(rdata);
                            carryOverVoucherRef.value?.initCreateChangeOutVoucher(false, cr);
                            currentSlot.value = "carryOverVoucher";
                            break;
                        }
                    }
                }
            });
        }
    );
};

const backToMain = () => {
    handleSearch().then(() => (currentSlot.value = "main"));
};
const editVoucherFromLookVoucher = (pid: number, vid: number) => {
    voucherAboutRef.value?.setData(pid, vid, "lookVouchers");
};
const GetIncomeResult = (normal = true) => {
    const urlPath = AccountStandard.UnionStandard === accStandard.value ? "GetIncomeResults" : "GetIncomeResult";
    request({ url: "/api/CarryOver/" + urlPath + "?pid=" + currentPID.value, method: "post" }).then((res: any) => {
        if (res.state === 1000) {
            const rdata: ICarryOverBack = res.data;
            if (AccountStandard.UnionStandard !== accStandard.value) {
                if (rdata.status === 0) {
                    carryOverVoucherRef.value?.reset();
                    carryOverVoucherRef.value?.createUnchangeVouchers(rdata.accountingSubjects);
                    currentSlot.value = "carryOverVoucher";
                } else if (rdata.status == 1) {
                    //检查未分配利润凭证
                    const list = [];
                    list.push(res.data);
                    carryOverIncomeChangeRef.value?.setData(list);
                    carryOverIncomeChangeRef.value?.setDealWithType("checkOutIncomeList", normal);
                    currentSlot.value = "carryOverIncomeChange";
                    setTimeout(() => {
                        carryOverVoucherRef.value?.reset();
                    }, 500);
                } else {
                    carryOverIncomeChangeRef.value?.setDealWithType("noNeedDoNothing", normal);
                    StoreChangeOut(false, () => {
                        currentSlot.value = "carryOverIncomeChange";
                    });
                }
            } else {
                const rdata: Array<ICarryOverBack> = res.data;
                for (let i = 0; i < rdata.length; i++) {
                    const item = rdata[i];
                    if (item.status === 0) {
                        carryOverVoucherRef.value?.reset();
                        carryOverVoucherRef.value?.createUnchangeVouchers(item.accountingSubjects);
                        currentSlot.value = "carryOverVoucher";
                        break;
                    } else if (item.status == 1) {
                        carryOverIncomeChangeRef.value?.setData(rdata);
                        carryOverIncomeChangeRef.value?.setDealWithType("checkOutIncomeList", normal);
                        currentSlot.value = "carryOverIncomeChange";
                        setTimeout(() => {
                            carryOverVoucherRef.value?.reset();
                        }, 500);
                        break;
                    } else {
                        carryOverIncomeChangeRef.value?.setDealWithType("noNeedDoNothing", normal);
                        StoreChangeOut(false, () => {
                            currentSlot.value = "carryOverIncomeChange";
                        });
                        break;
                    }
                }
            }
        }
    });
};
const backFromVoucher = (backPage: string, isFromCancel: boolean) => {
    if (backPage === "" || backPage === "carryOver") {
        if (isFromCancel) {
            currentSlot.value = "carryOver";
        } else {
            createPeriodCheckNext();
        }
        return;
    }
    if (backPage === "carryOverIncomeChange") {
        if (isFromCancel) {
            currentSlot.value = "carryOverIncomeChange";
        } else {
            GetIncomeResult(isNormalBack.value);
        }
        return;
    }
    if (backPage === "lookVouchers") {
        if (isFromCancel) {
            currentSlot.value = "lookVouchers";
        } else {
            goCreatePeriodCheck(currentPID.value, true);
        }
        return;
    }
    if (backPage === "createPeriodCheck") {
        if (isFromCancel) {
            currentSlot.value = "createPeriodCheck";
        } else {
            goCreatePeriodCheck(currentPID.value);
        }
        return;
    }
};
const deleteVoucherAbout = (vid: number, pid: number, backPage: string, callback: Function) => {
    if (backPage === "" || backPage === "carryOver") {
        const { listTotalIndex, index } = carryOverRef.value!.getCarryOverVoucherIndex(vid, pid);
        carryOverRef.value?.deleteCArryOver({ vid, pid }, listTotalIndex, index, callback as () => void);
        return;
    }
    if (backPage === "carryOverIncomeChange") {
        const { listTotalIndex } = carryOverIncomeChangeRef.value!.getCarryOverIncomeVoucherIndex(vid, pid);
        if (listTotalIndex !== -1) {
            carryOverIncomeChangeRef.value?.deleteCArryOver( vid, pid , listTotalIndex, callback as () => void);
        }
        return;
    }
    if (backPage === "lookVouchers") {
        const { listTotalIndex } = lookVouchersRef.value!.getLookVoucherIndex(vid, pid);
        if (listTotalIndex !== -1) {
            lookVouchersRef.value?.deleteVoucher(pid, vid, listTotalIndex as number, callback as () => void);
        }
        return;
    }
    //生成凭证无删除
    if (backPage === "createPeriodCheck") {
        return;
    }
};
const checkoutLast = (backPage: string) => {
    if (backPage === "" || backPage === "carryOver") {
        currentSlot.value = "carryOver";
        return;
    }
    if (backPage === "carryOverIncomeChange") {
        GetIncomeResult(isNormalBack.value);
        return;
    }
};
const handleCarryOverIncomeChangeBack = (normal = true) => {
    normal ? createPeriodCheckNext() : (currentSlot.value = "createPeriodCheck");
    isNormalBack.value = true;
};
const handleCarryOverVoucherBack = () => {
    goCreatePeriodCheck(currentPID.value);
};
const handleCarryOverVoucherSaveSuccess = () => {
    createPeriodCheckNext();
};
const handleCarryOverVoucherYearSaveSuccess = () => {
    GetIncomeResult(isNormalBack.value);
};
const page2CarryOverVoucher = () => {
    currentSlot.value = "carryOverVoucher";
};
const handleCarryOverVoucherNewVoucher = (vgId: number) => {
    const vdate = getVDate();
    const title = accStandard.value === AccountStandard.VillageCollectiveEconomyStandard ? "收益" : "利润";
    voucherAboutRef.value?.newVoucher(vdate, vgId, true, "第 2 步 结转" + asTheme + "-结转未分配" + title, "carryOverIncomeChange");
};
const newVoucherFromLookVouchers = (vgId = 1010) => {
    if (!lookVoucherCardInfo) return;
    const vdate = getVDate();
    const checktype = lookVoucherCardInfo?.checktype;
    let vType = 0;
    if (lookVoucherCardInfo?.cardInfo.type === 2) {
        vType = lookVoucherCardInfo?.cardInfo.vtype ?? 0;
    } else {
        if (lookVoucherCardInfo?.checktype === 99) {
            vType = lookVoucherCardInfo?.extendInfo.model.vtType ?? 0;
        } else {
            vType = lookVoucherCardInfo?.cardInfo.vtype ?? 0;
        }
    }

    let voucherLines: VoucherEntryModel[];
    if (checktype === 20) {
        lookVoucherCardInfo.suppleFcData &&
            lookVoucherCardInfo?.suppleFcData(lookVoucherCardInfo, currentPID.value, () => {
                voucherLines = lookVoucherCardInfo?.newVoucherInfo as VoucherEntryModel[];
                voucherAboutRef.value?.newVoucher(vdate, vgId, false, "", "lookVouchers", voucherLines, vType);
            });
        return;
    } else if (checktype === 0) {
        const newVoucherInfo: INewSalesCostInfoRow[] = lookVoucherCardInfo?.newVoucherInfo;
        voucherLines = carryOverSalesCostMapToVoucherEntryModel(newVoucherInfo);
        voucherAboutRef.value?.newVoucher(vdate, vgId, false, "", "lookVouchers", voucherLines, vType);
    } else {
        const newVoucherInfo: ITemplateVoucherLines[] =
            checktype === 99 ? lookVoucherCardInfo?.extendInfo.rows : lookVoucherCardInfo?.newVoucherInfo;
        newVoucherInfo.forEach((item) => {
            item.credit = "0";
            item.debit = "0";
            return item;
        });
        voucherLines = voucherLineInfoMapToVoucherEntryModel(newVoucherInfo);
        voucherAboutRef.value?.newVoucher(vdate, vgId, false, "", "lookVouchers", voucherLines, vType);
    }
};

const closeModal = () => {
    bossPermissionsShow.value = false;
    globalWindowOpenPage("/Settings/Bosspermission", "老板看账");
};
const getPreCheckHandlerApi = (pid: number) => {
    return request({ url: "/api/Checkout/PreCheck?pid=" + pid, method: "post" });
};
const getCheckPresetItemForCheckoutApi = (pid: number) => {
    return request({ url: "/api/Checkout/GetCheckResultList?pid=" + pid, method: "post" });
};
const getCustomVoucherTemplateRowsApi = (pId: number, vtId: number) => {
    return request({ url: "/api/CustomCarryOverTemplate/CalcData?id=" + vtId + "&pid=" + pId, method: "post" });
};
const IsFinishApi = () => {
    return request({ url: "/api/CarryOver/IsFinish?pid=" + currentPID.value, method: "post" });
};
const storeChangeOutHandlerApi = () => {
    return request({ url: "/api/CarryOver/CarryOver?pid=" + currentPID.value, method: "post" });
};
const getVDate = (): string => {
    const item = periodData.value.find((item) => item.pid === currentPID.value) as IPeriod;
    const day = getDaysInMonth(item.year, item.sn);
    const vdate = `${item.year}-${(item.sn + "").padStart(2, "0")}-${day}`;
    return vdate;
};
const toVoucherAbout = () => {
    carryOverVoucherTime.value = 1500;
    currentSlot.value = "voucherAbout";
};
const reverseSuccess = () => {
    handleInit();
    tabName.value = "checkout";
    currentSlot.value = "main";
};

const routeQueryParams = ref<any>(null);
const routerArrayStore = useRouterArrayStoreHook();
const route = useRoute();
const routerArray = toRef(routerArrayStore, "routerArray");
onBeforeRouteLeave((to, from, next) => {
    routeQueryParams.value = from.query;
    next();
});
const reLoadCurrentPage = () => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if (currentRouterModel.stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        currentRouterModel.stop = true;
    }
};
onMounted(() => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop + "" !== "undefined") {
        setTimeout(() => {
            delete (currentRouterModel as any).stop;
        });
    }
});
onUnmounted(() => {
    routeQueryParams.value = null;
    window.removeEventListener("changeSalaryMultiStatus", backToCreatePeriodCheck);
});
onActivated(() => {
    const thisRouteQueryParams = route.query;
    if (thisRouteQueryParams.from === "Statements") {
        tabName.value = "checkoutReverse";
        const currentQuery = { ...route.query };
        delete currentQuery.from;
        router.replace({ query: currentQuery });
        return;
    }
    const isEqualRouterParams = _.isEqual(thisRouteQueryParams, routeQueryParams.value);
    if (!isEqualRouterParams && routeQueryParams.value !== null) {
        if (
            (currentSlot.value === "carryOverVoucher" && carryOverVoucherRef.value?.voucherChanged()) ||
            (currentSlot.value === "voucherAbout" && voucherAboutRef.value?.voucherChanged())
        ) {
            confirmWithoutGlobalVisible.value = true;
        } else {
            reLoadCurrentPage();
        }
    }
});
const isEditting = computed(() => {
    return (
        (currentSlot.value === "carryOverVoucher" && carryOverVoucherRef.value!.voucherChanged()) ||
            (currentSlot.value === "voucherAbout" && voucherAboutRef.value!.voucherChanged())
    );
});
const currentPath = ref(route.path);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";

@media screen and (min-width: 1441px) {
    body[erp] {
        .content {
            .main-content {
                :deep(.checkout-content) {
                    .tiles {
                        .base-tile {
                            margin-left: 40px;
                        }
                    }
                }
            }
        }
    }
}

.content {
    height: auto;
    min-height: 100%;
    :deep(.el-tabs) {
        .el-tabs__header {
            margin-bottom: 0;
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: var(--white);
        }
        .el-tabs__content {
            overflow: initial;
        }
    }
    &.erp-content {
        .main-content,
        .slot-content {
            height: auto;
            overflow: initial;
            :deep(.stepTitle) {
                span:first-child {
                    font-weight: bold;
                }
            }
        }
    }
}
.template.content {
    :deep(.el-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .el-tabs__content {
            flex: 1;
            overflow: hidden;
        }
        .el-tab-pane {
            min-height: unset !important;
            height: 100%;
        }
    }
    #userDefinedTemplateContentSlider {
        height: 100%;
        overflow: hidden;
    }
}
.voucherAbout.content.erp-content {
    :deep(.el-tabs__header) {
        position: relative;
    }
}

.company-container {
    .company-notice {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        align-content: center;
        text-align: center;
        font-size: 16px;
        font-weight: 600;

        img {
            width: 260px;
            height: 200px;
            margin-top: 12px;
            margin-bottom: 24px;
        }

        .all-line {
            width: 100%;
        }

        .text-header {
            font-size: 20px;
            line-height: 28px;
        }

        .text-content {
            font-weight: 400;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 20px;
            width: 15px;
            height: 15px;
            cursor: pointer;
        }

        .btn {
            border: 0;
            background: #44b449;
            color: white;
            width: 160px;
            height: 32px;

            &.normal-text {
                font-size: 14px;
                line-height: 30px;
                font-weight: 400;
                white-space: nowrap;
            }
        }
    }
}
.checkout_dialog {
    padding: 10px;
    .checkout_content {
        padding: 10px;
        height: 40px;
        p {
            margin: 0;
            font-size: 12px;
            line-height: 16px;
            margin-bottom: 5px;
        }
        .progress_content {
            position: relative;
            z-index: 2;
        }
        .progress_contentblack {
            color: #404040;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }
    }
}
:deep(.checkout_progress) {
    .el-progress-bar {
        border: 1px solid #abafb8;
        .el-progress-bar__outer {
            border-radius: 0px;
            background-color: var(--el-dialog-bg-color) !important;
            .el-progress-bar__inner {
                border-radius: 0px;
                overflow: hidden;
                position: unset !important;
            }
        }
    }
    .el-progress-bar__innerText {
        white-space: nowrap;
        width: 260px;
        font-size: 12px !important;
        text-align: center;
        margin: 0;
    }
}
</style>
