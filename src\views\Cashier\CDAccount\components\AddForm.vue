<template>
    <div class="title">{{ props.getTabName() === 1010 ? "现金账户" : "银行存款账户" }}</div>
    <div class="form-content">
        <div class="form-component">
            <el-form :model="searchInfo" label-width="80px" ref="formRef">
                <el-form-item label="编码：" class="code">
                    <input v-model="searchInfo.AC_NO" placeholder=" " />
                </el-form-item>
                <el-form-item label="银行：" class="name" v-show="props.getTabName() === 1020">
                    <el-select
                        v-model="searchInfo.bankid"
                        placeholder="请选择银行"
                        :teleported="false"
                        :disabled="searchInfo.bankDisabled"
                        :filterable="true"
                        :filter-method="bankFilterMethod"
                    >
                        <el-option v-for="item in showbankList" :key="item.id" :value="item.id" :label="item.name" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账户名称：" class="name">
                    <input
                        v-model="searchInfo.AC_NAME"
                        :placeholder="props.getTabName() === 1010 ? '示例：现金' : '示例：中国工商银行深圳宝安支行'"
                        @input="
                            handleAAInput(LimitCharacterSize.Default, $event, '账户名称', 'name', changeSearchInfo, getErrorMsg('name'))
                        "
                        @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                    />
                </el-form-item>
                <el-form-item label="银行卡号：" v-show="props.getTabName() === 1020">
                    <input
                        v-model="searchInfo.BANK_ACCOUNT"
                        placeholder="请输入完整的银行卡号"
                        :disabled="searchInfo.bankDisabled"
                        @input="
                            handleAAInput(
                                LimitCharacterSize.Default,
                                $event,
                                '银行卡号',
                                'bank_account',
                                changeSearchInfo,
                                getErrorMsg('bankNo')
                            )
                        "
                        @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                    />
                </el-form-item>
                <el-form-item label="" class="currency-tip" v-show="searchInfo.bankDisabled">
                    <img src="@/assets/Icons/warn.png" alt="" />
                    <span class="tip">该账号已绑定银企互联，暂不支持修改</span>
                </el-form-item>
                <el-form-item label="币别：" class="currency" :class="{ disabled: currencyDisabled }">
                    <el-select 
                        v-model="searchInfo.CURRENCY" 
                        placeholder="请选择" 
                        :teleported="false" 
                        :disabled="currencyDisabled"
                        :filterable="true"
                        :filter-method="currencyFilterMethod"
                    >
                        <el-option v-for="item in showCurrencyList" :key="item.id" :value="item.id" :label="item.name" />
                    </el-select>
                </el-form-item>
                <el-form-item label="" class="currency-tip" v-if="currencyDisabled">
                    <img src="@/assets/Icons/warn.png" alt="" />
                    <span class="tip">该账户已被使用，暂不支持修改币别</span>
                </el-form-item>
                <el-form-item label="会计科目：" class="subject">
                    <SubjectPicker
                        ref="asubRef"
                        :is-by-id="true"
                        :isParentShow="false"
                        :is-expansion="true"
                        asubImgRight="2px"
                        v-model="searchInfo.ASUB"
                    ></SubjectPicker>
                </el-form-item>
            </el-form>
        </div>
        <div class="tip">说明：关联会计科目之后，资金数据自动和总账核对是否一致，同时也支持自动生成凭证。</div>
        <div class="buttons">
            <a class="solid-button mr-10" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">取消</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, nextTick, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import type { FormInstance } from "element-plus";
import type { ICurrencyList } from "@/views/Cashier/components/types";
import { useRoute } from "vue-router";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import { request } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import type { IBankList } from "../types";
import { commonFilterMethod } from "@/components/Select/utils";

const props = defineProps<{
    getTabName: Function;
    currencyList: ICurrencyList[];
    bankList: IBankList[];
}>();

const asubRef = ref<InstanceType<typeof SubjectPicker>>();
const formRef = ref<FormInstance>();
const searchInfo = reactive({
    AC_NO: "",
    AC_NAME: "",
    CURRENCY: 1,
    ASUB: "",
    BANK_ACCOUNT: "",
    AC_ID: "",
    bankDisabled: false,
    bankid: "",
});

const getErrorMsg = (type: "name" | "bankNo") => {
    if (type === "name") {
        const name = props.getTabName() === 1010 ? "现金账户" : "银行账户";
        return "亲，" + name + "名称超过64个字，请修改后重新保存~";
    } else if (type === "bankNo") {
        return "亲，银行账号超过64个字，请修改后重新保存~";
    }
};
const changeSearchInfo = (key: string, value: string) => {
    if (key === "bank_account") {
        searchInfo.BANK_ACCOUNT = value;
    } else if (key === "name") {
        searchInfo.AC_NAME = value;
    }
};

const emit = defineEmits(["cancel", "save"]);
const handleSave = () => {
    if (isJudgeng) return;
    const reg = /^[0-9a-zA-Z]{1,3}$/;
    if (searchInfo.AC_NO.trim() === "") {
        ElNotify({ type: "warning", message: "亲，账户编码不能为空" });
        return;
    }
    if (!reg.test(searchInfo.AC_NO.trim())) {
        ElNotify({ type: "warning", message: "亲，编码只能输入三位及以下的数字字母组合" });
        return;
    }
    if (searchInfo.AC_NAME.trim() === "") {
        ElNotify({ type: "warning", message: "亲，账户名称不能为空" });
        return;
    }
    if (props.getTabName() === 1020 && !searchInfo.bankid) {
        ElNotify({ type: "warning", message: "亲，请选择银行" });
        return;
    }
    emit("save", searchInfo);
};
const handleCancel = () => {
    emit("cancel");
};

let isJudgeng = false;
const CheckOnSubject = () => {
    isJudgeng = true;
    const params = {
        asub: searchInfo.ASUB,
        currency: searchInfo.CURRENCY,
        acId: searchInfo.AC_ID,
    };
    // 开启外币核算的科目的 foreigncurrency = 1
    const foreigncurrency = asubRef.value?.foreigncurrency;
    const currencyItem = props.currencyList.find((item) => item.id === searchInfo.CURRENCY);
    request({ url: "/api/CDAccount/CheckOnSubject?" + getUrlSearchParams(params), method: "post" })
        .then((res: any) => {
            const result = (res.data as string).toUpperCase();
            if (result == "FC") {
                if (currencyItem?.isBaseCurrency) {
                    if (foreigncurrency === 1) {
                        // 是本位币并且开启了外币核算的科目才会有外币核算
                        // 没有开启外币核算不进行提示
                        ElNotify({ type: "warning", message: "亲，该会计科目不具有" + currencyItem.name + "核算" });
                        searchInfo.ASUB = "";
                        return;
                    }
                } else {
                    // 不是本位币的情况下，那就确实不具有该币别核算
                    ElNotify({ type: "warning", message: "亲，该会计科目不具有" + currencyItem?.name + "核算" });
                    searchInfo.ASUB = "";
                    return;
                }
            }
            if (result == "USE") {
                ElNotify({ type: "warning", message: "亲，该科目已被选择" });
                searchInfo.ASUB = "";
                return;
            }
        })
        .finally(() => {
            isJudgeng = false;
        });
};

let changCount = 0;
watch(
    () => props.currencyList,
    (val) => {
        if (val.length) {
            if (changCount === 0) {
                changCount++;
                searchInfo.CURRENCY = val[0].id;
            }
        }
    }
);

const needJudge = ref(true);
watch(
    () => searchInfo.ASUB,
    (val) => {
        if (val !== "" && needJudge.value) {
            nextTick().then(() => {
                CheckOnSubject();
            });
        }
    }
);
watch(
    () => searchInfo.CURRENCY,
    () => {
        nextTick().then(() => {
            CheckOnSubject();
        });
    }
);

const editSearchInfo = (data: any) => {
    needJudge.value = false;
    Object.assign(searchInfo, data);
    setTimeout(() => {
        needJudge.value = true;
    }, 1000);
    nextTick(() => {
        init = true;
    });
};
const resetForm = () => {
    init = false;
    isEditting.value = false;
    searchInfo.AC_NO = "";
    searchInfo.AC_NAME = "";
    searchInfo.CURRENCY = 1;
    searchInfo.ASUB = "";
    searchInfo.BANK_ACCOUNT = "";
    searchInfo.AC_ID = "";
    currencyDisabled.value = false;
    needJudge.value = true;
    isJudgeng = false;
    searchInfo.bankDisabled = false;
    searchInfo.bankid = "";
};

const currencyDisabled = ref(false);
const editDisabled = (data: boolean) => (currencyDisabled.value = data);

defineExpose({ editSearchInfo, resetForm, editDisabled });

let init = false;
const isEditting = ref(false);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
watch(
    searchInfo,
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});

const showbankList = ref<Array<IBankList>>([]);
const showCurrencyList = ref<Array<ICurrencyList>>([]);
watchEffect(() => { 
    showbankList.value = JSON.parse(JSON.stringify(props.bankList));    
    showCurrencyList.value = JSON.parse(JSON.stringify(props.currencyList));    
});
function bankFilterMethod(value: string) {
    showbankList.value = commonFilterMethod(value, props.bankList, 'name');
}
function currencyFilterMethod(value: string) {
    showCurrencyList.value = commonFilterMethod(value, props.currencyList, 'name');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.form-content {
    margin: 0 auto;
    padding: 40px 0;
    .buttons {
        margin-top: 24px;
    }
    > .tip {
        padding-bottom: 16px;
        margin: 0 auto;
        font-size: 12px;
        color: red;
        width: 318px;
        text-align: left;
        box-sizing: border-box;
        padding-left: 30px;
        padding-right: 10px;
    }
    .form-component {
        width: 312px;
        margin: 0 auto;
        :deep(.el-form) {
            .el-form-item {
                .el-form-item__content {
                    & > input {
                        .detail-original-input(232px, 32px);
                    }
                }
                &.currency {
                    .detail-el-select(200px,32px);
                }
                &.code,
                &.name,
                &.currency {
                    .el-form-item__label {
                        &::before {
                            content: "*";
                            color: red;
                        }
                    }
                }
                &.currency-tip {
                    height: 16px;
                    .el-form-item__content {
                        display: flex;
                        align-items: center;
                        img {
                            width: 14px;
                            height: 14px;
                            margin-right: 5px;
                        }
                    }
                    span.tip {
                        color: gray;
                        font-size: 12px;
                        line-height: 16px;
                    }
                }
                &.subject {
                    .el-form-item__content {
                        .el-select-v2__placeholder {
                            text-align: left;
                        }
                    }
                }
            }
        }
    }
    .detail-el-form(232px, 32px);
}
.buttons {
    border-top: none;
}
.el-form-item {
    &.currency {
        .detail-el-select(232px,32px);
    }
}
</style>
