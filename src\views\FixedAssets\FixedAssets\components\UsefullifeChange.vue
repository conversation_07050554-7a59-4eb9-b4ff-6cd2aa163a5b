<template>
    <div class="edit-content" style="width: 1000px; padding: 0px">
        <div class="title">{{ props.changeTitle }}</div>
        <div style="border: 1px solid var(--slot-title-color);margin-top: 32px;">
            <table class="change-tb">
                <tr>
                    <td colspan="4" class="tb-hr">基本信息</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px; text-align: right">资产编号：</td>
                    <td id="ztxg_bh" class="tb-field" style="width: 326px">{{ props.changeData.fa_num }}</td>
                    <td class="tb-title" style="width: 84px">资产类别：</td>
                    <td id="ztxg_lb" class="tb-field" style="width: 416px">
                        <Tooltip :content="props.changeData.fa_type_name" :max-width="326" >{{ props.changeData.fa_type_name }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">资产名称：</td>
                    <td id="ztxg_mc" class="tb-field">
                        <Tooltip :content="props.changeData.fa_name" :max-width="326" >{{ props.changeData.fa_name }}</Tooltip>
                    </td>
                    <td class="tb-title">资产型号：</td>
                    <td id="ztxg_xh" class="tb-field">
                        <Tooltip :content="props.changeData.fa_model" :max-width="326" >{{ props.changeData.fa_model }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">所属部门：</td>
                    <td id="ztxg_bm" class="tb-field">
                        <Tooltip :content="props.changeData.department_name" :max-width="326" >{{ props.changeData.department_name }}</Tooltip>
                    </td>
                </tr>
            </table>
            <div class="line"></div>
            <table class="change-tb" cellspacing="0" cellpadding="0">
                <tr>
                    <td colspan="4" class="tb-hr">变更内容</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px">预计使用月份：</td>
                    <td id="synxtz_value1" class="tb-field" style="width: 326px">{{ changeData.usefullife }}</td>
                    <td class="tb-title" style="width: 84px"></td>
                    <td class="tb-field" style="width: 416px"></td>
                </tr>
                <tr>
                    <td class="tb-title">预计使用月份调整为：</td>
                    <td class="tb-field">
                        <el-input v-model="dataValue" style="width: 180px; height: 28px"></el-input>
                    </td>
                </tr>
            </table>
            <div class="buttons" style="margin-top: 22px; margin-bottom: 40px; text-align: center">
                <a class="button solid-button" style="margin: 0 5px" @click="savedata">保存</a>
                <a class="button" style="margin: 0 5px" @click="changeCancle">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { request } from "@/util/service";
import { ref } from "vue";
import { isInteger } from "../validator";
import { ElNotify } from "@/util/notify";
import  Tooltip  from "@/components/Tooltip/index.vue"
const props = defineProps({
    changeData: {
        type: Object,
        default: () => {
            return {};
        },
    },
    changeTitle: {
        type: String,
        default: "",
    },
    pid: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(["changeCancle", "saveData"]);

const dataValue = ref("");

function savedata() {
    let curentnum = dataValue.value.replace(/,/g, "");
    if (!isInteger(curentnum)) {
        ElNotify({
            type: "warning",
            message: "请输入正确的预计使用月份！",
        });
        return false;
    }
    if(props.changeData.usefullife === Number(curentnum)){
       ElNotify({
            type: "warning",
            message: "亲，您的数据没有变动，不能保存哦！",
        });
        return false;
    }
    request({
        url: `/api/FAChange/UsefullifeChange?faid=${props.changeData.fa_id}&pid=${props.pid}&value=${dataValue.value}&vid=0`,
        method: "post",
    }).then((res)=>{
        if (res.state === 1000) {
            emits("changeCancle",true);
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存失败！",
            });
        }
    }).catch(()=>{
        ElNotify({
            type: "error",
            message: "更新失败！",
        });
    })
}
function changeCancle() {
    emits("changeCancle");
}
</script>

<style scoped lang="less">
.tb-hr {
    padding: 20px 20px 15px 20px;
    color: var(--font-color);
    font-size: var(--h3);
    line-height: 22px;
    font-weight: bold;
    text-align: left;
}
.change-tb {
    & .tb-title {
        text-align: right;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
    }
    & .tb-field {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 14px;
        text-align: left;
    }
}
</style>
