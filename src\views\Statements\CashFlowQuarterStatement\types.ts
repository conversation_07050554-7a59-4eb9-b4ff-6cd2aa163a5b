export interface ICashFlowQuarter {
    amount: number;
    asid: number;
    coumType: number;
    entryType: number;
    expand: number;
    initalAmount: number;
    lineID: number;
    lineName: string;
    lineNumber: number;
    lineType: number;
    note: string;
    parentID: number;
    pid: number;
    priority: number;
    qutar1: number;
    qutar2: number;
    qutar3: number;
    qutar4: number;
    statementID: number;
    children?: ICashFlowQuarter[];
    fold?:number;
}
