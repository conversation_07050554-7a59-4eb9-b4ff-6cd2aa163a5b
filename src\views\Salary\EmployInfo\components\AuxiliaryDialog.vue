<template>
    <el-dialog v-model="dialogShow" :title="title + '设置'" center width="440" class="custom-confirm dialogDrag">
        <div class="asubWithAssist" v-dialogDrag>
            <div class="asubInfo">
                <div class="left-title">{{ title + ":" }}</div>
                <div class="right-info">
                    <ToolTip :content="asubName" :isInput="true">
                        <el-input :value="asubName" readonly disabled style="width: 160px" />
                    </ToolTip>
                </div>
            </div>
            <div class="assistLines" v-for="item in auxiliarySelectListAll" :key="item.name">
                <div class="left-title">{{ item.name }}:</div>
                <div class="right-info">
                    <Select
                        v-model="item.id"
                        style="width: 160px"
                        :disabled="aaReadonly"
                        :bottom-html="newAAHtml"
                        @bottom-click="quickAddAssist(item.type, item.name)"
                        @visible-change="handleVisibleChange($event, item.name)"
                        :filterable="true"
                        :filter-method="auxiliarySelectFilterMethod"
                    >
                        <Option 
                            v-for="option in item.showSelectList" 
                            :key="option.aaeid" 
                            :value="option.aaeid" 
                            :label="option.aaname"
                        ></Option>
                    </Select>
                </div>
            </div>
            <!-- <div class="ass-tip">注：部门从员工信息中自动带入，不可修改哦~</div> -->
        </div>
        <div class="buttons" style="border-top: 1px solid var(--border-color)">
            <a class="button solid-button" @click="confirmaAssist">确定</a>
            <a class="button ml-10" @click="cancelAssist">取消</a>
        </div>
    </el-dialog>
    <AccountingDialog
        ref="AccountingDialogRef"
        :inputTitle="aaAddTitle"
        @save-success="successAddAssist"
    ></AccountingDialog>
</template>

<script setup lang="ts">
import ToolTip from "@/components/Tooltip/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getAssistingAccountByAATypeApi } from "@/api/assistingAccounting";
import { computed, toRef, ref, watch, watchEffect } from "vue";
import type { IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import AccountingDialog from "./AccountingDialog.vue";
import { commonFilterMethod } from "@/components/Select/utils";
import { cloneDeep } from "lodash";

const props = defineProps({
    title: {
        type: String,
        default: "",
    },
    modelValue: {
        type: Boolean,
        default: false,
    },
    asubName: {
        type: String,
        default: "",
    },
    aacode: {
        type: Array<string>,
        default: [],
    },
    aaAllowNull: {
        type: Array<string>,
        default: [],
    },
    aaReadonly: {
        type: Boolean,
        default: false,
    },
    aaId: {
        type: Array<string>,
        default: [],
    },
    departId: {
        type: String,
        default: "",
    },
    employName: {
        type: String,
        default: "",
    }
});
const emits = defineEmits<{
    (e: "update:modelValue", val: boolean): void;
    (e: "setAsubAAE", data: IAuxiliarySelectItem[]): void;
}>();
const dialogShow = computed({
    get: () => props.modelValue,
    set: (val: boolean) => emits("update:modelValue", val),
});
const AccountingDialogRef = ref<InstanceType<typeof AccountingDialog>>();
let newAAHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
    <a class="link">
        +点击添加
    </a>
</div>`;
const aaAddTitle = ref("");
const addType = ref("");
function quickAddAssist(aaType: string, aaName: string) {
    if (!checkPermission(["assistingaccount-canedit"])) {
        ElNotify({
            type: "warning",
            message: "亲，你没有权限新增辅助核算哦！",
        });
        return;
    }
    addType.value = aaType;
    aaAddTitle.value = aaName;
    AccountingDialogRef.value?.showAADialog(Number(aaType), autoAddName.value);
}

function successAddAssist(data: any) {
    const addassistObj = auxiliarySelectList.value.find((v) => v.type === addType.value) as IAuxiliarySelectItem;
    addassistObj.selectList.push({
        aanum: data.aaNum,
        aaname: data.aaName,
        aaeid: data.aaeId,
    });
    addassistObj.id = data.aaeId;
}
const aaTypeList = toRef(useAssistingAccountingStore(), "assistingAccountingTypeList");

function confirmaAssist() {
    let flag = true;
    for (let i=0; i<auxiliarySelectListAll.value.length; i++) {
        if (!auxiliarySelectListAll.value[i].id || auxiliarySelectListAll.value[i].id === ' ') {
            flag = false;
            ElNotify({
                type: "warning",
                message: `亲，${auxiliarySelectListAll.value[i].name}辅助核算项目未选择!`,
            });
            break;
        }
    }
    if (flag) {
        // cancelAssist();
        emits("setAsubAAE", auxiliarySelectListAll.value);
        dialogShow.value = false;
    }
}
function cancelAssist() {
    // emits("setAsubAAE", auxiliarySelectList.value);
    dialogShow.value = false;
}
const auxiliarySelectList = ref<IAuxiliarySelectItem[]>([]);
interface IAuxiliarySelectItem {
    type: string; //辅助核算类型
    id: string | number; //辅助核算id
    name: string; //辅助核算名称
    selectList: IAuxiliarySelectListItem[]; //辅助核算列表
}
interface IAuxiliarySelectListItem {
    aanum: string;
    aaname: string;
    aaeid?: number;
}
interface IAuxiliaryListItem {
    asid: number;
    aatype: number;
    aaeid: number;
    aanum: string;
    aaname: string;
    value01: string;
    status: number;
    uscc: string;
    createdBy: number;
    createdDate: string;
    preName: string;
}
const aacodeList = computed(()=>{
    return JSON.parse(JSON.stringify(props.aacode));
})
async function getInitAcconting() {
    auxiliarySelectList.value = [];
    for (const [index, item] of aacodeList.value.entries()) {
        let assistObj = {} as IAuxiliarySelectItem;
        const aaType = aaTypeList.value!.find((v) => v.aaType === Number(item));
        assistObj.name = aaType ? aaType.aaTypeName : "";
        let list;
        await getAssistingAccountByAATypeApi(Number(item)).then(async (res: IResponseModel<IAuxiliaryListItem[]>) => {
            list = res.data;
            assistObj.selectList = list.length
                ? list.map((item: IAuxiliaryListItem) => {
                    return {
                        aanum: item.aanum,
                        aaname: item.aaname,
                        aaeid: item.aaeid,
                    };
                })
                : [];
            if (props.aaAllowNull[index] === "0") {
                assistObj.selectList = assistObj.selectList.filter((v) => v.aaname !== "未录入辅助核算");
            }
            if (props.aaId && props.aaId[index] && assistObj.selectList.find(v=> v.aaeid == Number(props.aaId[index]))) {
                assistObj.id =
                    Number(props.aaId[index]) < 0 && props.aaAllowNull[index] === "0"
                        ? assistObj.selectList[0]?.aaeid || ""
                        : Number(props.aaId[index]);
            } else {
                if (item === "10004") { //部门，员工信息中选择的部门自动带入
                    assistObj.id = props.departId ? Number(props.departId) : (assistObj.selectList[0]?.aaeid || " ");
                } else if(item === "10003" && props.employName) { //职员
                    let employ = assistObj.selectList.filter((v)=> v.aaname === props.employName);
                     if (employ && employ.length > 0) {
                        assistObj.id = employ[0].aaeid || "";
                    } else {
                        assistObj.id = assistObj.selectList[0]?.aaeid || " ";
                    }
                } else {
                    assistObj.id = assistObj.selectList[0]?.aaeid || " ";
                }
            }
            assistObj.type = item;
            auxiliarySelectList.value.push(assistObj);
        });
    }
    dialogShow.value = true;
}
defineExpose({
    getInitAcconting
});

//搜索无数据时，传入新增弹窗内的字段
const filterName = ref("");
const autoAddName = ref("");
//模糊搜索
const auxiliarySelectListAll = ref<any[]>([]);
watch(
    () => auxiliarySelectList.value, 
    () => {
        auxiliarySelectListAll.value = auxiliarySelectList.value.map((v) => {
            return {
                ...v,
                showSelectList: cloneDeep(v.selectList),
            }
        });
    },
    { deep: true }
)
function auxiliarySelectFilterMethod(value: string) {
    let index = auxiliarySelectListAll.value.findIndex((v) => v.name === filterName.value);
    auxiliarySelectListAll.value[index].showSelectList = commonFilterMethod(value, auxiliarySelectListAll.value[index].selectList, 'aaname');
    autoAddName.value = auxiliarySelectListAll.value[index].showSelectList.length === 0 ? value.trim() : ""; 
}
function handleVisibleChange(visible: boolean, name: string) {
    filterName.value = visible ? name : "";
}

</script>
<style scoped lang="less">
:deep(.el-input__inner) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.ass-tip {
    margin-bottom: -10px;
    padding-left: 20px;
    font-size: 12px;
    line-height: 30px;
}
.asubWithAssist {
    padding: 20px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    .asubInfo,
    .assistLines {
        display: flex;
        align-items: center;
        justify-content: center;
        .left-title {
            width: 120px;
            text-align: right;
        }
    }
    .assistLines {
        margin-top: 16px;
    }
    .right-info {
        margin-left: 10px;
    }
}
.divAddAA {
    table {
        margin: 19px auto 0;
        tr td {
            height: 30px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            padding-bottom: 16px;
        }
    }
    :deep(.el-input__wrapper) {
        height: 30px !important;
        padding-left: 10px;
    }
}
</style>
