<template>
    <div class="main-tool-right" @click="emits('operateClick')">
        <el-checkbox style="margin-right: 10px" label="显示全部" v-model="showAll" @change="changeShowAllStatus"></el-checkbox>
        <Dropdown btnTxt="导入" :downlistWidth="104" class="mr-10 import" v-permission="['expensebill-canimport']">
            <li v-for="(item, key) in importMethods" :key="key" @click="() => selectImportMethod(String(key))">
                {{ item.methods }}
            </li>
        </Dropdown>
        <Dropdown btnTxt="导出" :downlistWidth="104" class="mr-10 export" v-permission="['expensebill-canexport']">
            <li @click="exportHandle(false)">当前数据导出</li>
            <li @click="exportHandle(true)">全部导出</li>
        </Dropdown>
        <Dropdown btnTxt="批量操作" :downlistWidth="104" class="operate" v-permission="['expensebill-canedit']">
            <li v-for="(item, key) in batchOptions" :key="key" v-permission="[item.permission]" @click="openBatchUpdateDialog(String(key))">
                {{ item.option }}
            </li>
        </Dropdown>
        <div
            v-if="generateVoucherAllCan"
            class="item ml-10"
            style="position: relative; display: flex; align-items: center; flex-wrap: nowrap"
            @mouseleave="() => (voucherFromExpenseBillDownList = false)"
        >
            <a class="button solid-button" style="float: left" @click="openVoucherFromBill">生成凭证</a>
            <a class="button solid-button down-click" style="float: left" @mouseenter="() => (voucherFromExpenseBillDownList = true)"></a>
            <div style="width: 106px; top: 28px" class="downlist" v-show="voucherFromExpenseBillDownList">
                <li @click="emits('voucherSetting')">生成凭证设置</li>
                <li @click="expenseAccountSetting">费用科目设置</li>
            </div>
        </div>
        <div
            v-else-if="checkPermission(['expensebill-cancreatevoucher'])"
            class="item ml-10"
            style="position: relative; display: flex; align-items: center; flex-wrap: nowrap"
            @mouseleave="() => (voucherFromExpenseBillDownList = false)"
        >
            <a class="button solid-button" style="float: left" @click="openVoucherFromBill">生成凭证</a>
            <a class="button solid-button down-click" style="float: left" @mouseenter="() => (voucherFromExpenseBillDownList = true)"></a>
            <div style="width: 106px; top: 28px" class="downlist" v-show="voucherFromExpenseBillDownList">
                <li @click="emits('voucherSetting')">生成凭证设置</li>
            </div>
        </div>
        <a
            class="button solid-button ml-10 large-1"
            v-else-if="checkPermission(['expensebillsettings-canview'])"
            @click="expenseAccountSetting"
            >费用科目设置</a
        >

        <RefreshButton></RefreshButton>
    </div>
    <ImportSingleFileDialog
        class="expense-import-dialog"
        :importTitle="importMethods[importWay]?.methods"
        v-model:import-show="importShow"
        :importUrl="'/api/ExpenseBill/Import?impsrc=' + importMethods[importWay]?.dataParams.impsrc"
        :uploadSuccess="uploadSuccess"
        :need-loading="true"
    >
        <template #download>
            <div v-if="importWay === '1'">
                <span class="invoice-import-title child"
                    >1、点击下载导入模板，按照模板格式进行数据整理后再导入
                    <a class="link ml-20" @click="downloadTemplate()">下载模板</a></span
                >
            </div>
            <div v-if="importWay === '2'">
                <span class="invoice-import-title child"
                    >1、登录企业微信后台，选择应用管理找到“审批”应用 <br />2、审批应用下方找到报销或费用模块进入编辑下载对应
                    的报销单据或费用单据</span
                >
            </div>
            <div v-if="importWay === '3'">
                <span class="invoice-import-title child"
                    >1、登录钉钉，进入OA审批，点击右上角管理后台<br />
                    2、OA审批管理后台，选择日常报销、差旅报销和付款单 等表单导出，操作后<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;进入数据导出页面进行下载</span
                >
            </div>
        </template>
        <template #import-content>
            <span>{{ importWay === "1" ? 2 : 3 }}、选择文件导入</span>
        </template>
    </ImportSingleFileDialog>
    <el-dialog
        v-model="batchUpdateDialogVisible"
        :title="batchOptions[batchUpdateDialogType]?.dialogTitle || '提示'"
        center
        width="440"
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
    >
        <div class="certification-show-content" v-dialogDrag>
            <div class="certification-show-main">
                <span v-if="batchUpdateDialogType === '1'" class="delete-tip"> 亲，确认要删除吗？ </span>
                <el-form-item
                    v-else
                    :label="batchOptions[batchUpdateDialogType]?.labelName"
                    class="certification-form line-item"
                    :show-message="false"
                    prop="businessType"
                >
                    <Select
                        class="line-item-field businessTypeRef"
                        popper-class="business-type-list"
                        suffix-icon="CaretBottom"
                        v-model="batchOptions[batchUpdateDialogType].selected"
                        placeholder=" "
                        :teleported="false"
                        :filterable="true"
                        :no-match-text="'暂无数据'"
                        :filter-method="TypeOPayMethod"
                    >
                        <ElOption
                            v-for="item in showTypeOPayList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </ElOption>
                    </Select>
                </el-form-item>
            </div>
            <div class="buttons">
                <a class="button solid-button ml-10" @click="certificationSure">确定</a>
                <a class="button ml-10" @click="() => (batchUpdateDialogVisible = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <DialogBatchAAE
        ref="dialogAccountingRef"
        :aatype="batchUpdateDialogType === '3' ? CashAAType.Employee : CashAAType.Department"
        :name="batchUpdateDialogType === '3' ? '职员' : '部门'"
        @handle-sure="handleBatchEditDepartmentOrEmployee"
        @update:override="handleOverride"
    />
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, reactive, watchEffect, watch } from "vue";
import { checkPermission } from "@/util/permission";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { globalExport } from "@/util/url";
import Dropdown from "@/components/Dropdown/index.vue";
import ElOption from "@/components/Option/index.vue";
import Select from "@/components/Select/index.vue";
import DialogBatchAAE from "@/views/Cashier/CashOrDepositJournal/components/DialogBatchAAE.vue";
import { CashAAType } from "@/views/Cashier/CashOrDepositJournal/types";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";

const emits = defineEmits([
    "expenseAccountSetting",
    "reloadTableData",
    "operateClick",
    "goGenerateVoucher",
    "export",
    "importSuccess",
    "voucherSetting",
]);
import type { ITableItem, BasicTypeList, IBillTypeItem, IPayMethodItem } from "../types";

const props = defineProps<{
    basicTypeList: BasicTypeList;
    checkedTableData: ITableItem[];
}>();
const showAll = ref(localStorage.getItem("expensebillShowAll") === "true");
const changeShowAllStatus = (val: boolean) => {
    localStorage.setItem("expensebillShowAll", String(val));
    emits("reloadTableData");
};
const generateVoucherAllCan = ref(checkPermission(["expensebill-cancreatevoucher"]) && checkPermission(["expensebillsettings-canview"]));
const voucherFromExpenseBillDownList = ref(false);
const dialogAccountingRef = ref<InstanceType<typeof DialogBatchAAE>>();

const importMethods: { [key: string]: any } = {
    1: {
        methods: "通用模板导入",
        dataParams: {
            impsrc: 10,
        },
    },
    2: {
        methods: "企业微信导入",
        dataParams: {
            impsrc: 20,
        },
    },
    3: {
        methods: "钉钉导入",
        dataParams: {
            impsrc: 30,
        },
    },
};
const batchOptions = computed((): any => {
    return reactive({
        1: {
            option: "批量删除单据",
            permission: "expensebill-candelete",
            dialogTitle: "提示",
            updateType: 0,
        },
        2: {
            option: "指定费用类型",
            permission: "expensebill-canedit",
            dialogTitle: "批量指定费用类型",
            labelName: "选择费用类型：",
            selected: props?.basicTypeList["billTypes"][0].billTypeId || "",
            optionList: props?.basicTypeList["billTypes"].map((item: IBillTypeItem) => {
                return {
                    label: item.billTypeName,
                    value: item.billTypeId,
                };
            }),
            updateType: 10,
        },
        3: {
            option: "指定收款人",
            permission: "expensebill-canedit",
            dialogTitle: "批量指定收款人",
            updateType: 30,
        },
        4: {
            option: "指定部门",
            permission: "expensebill-canedit",
            dialogTitle: "批量指定部门",
            updateType: 40,
        },
        5: {
            option: "指定结算方式",
            permission: "expensebill-canedit",
            dialogTitle: "批量指定结算方式",
            labelName: "选择结算方式：",
            selected: props?.basicTypeList["payMethods"][0].pmId || "",
            optionList: props?.basicTypeList["payMethods"].map((item: IPayMethodItem) => {
                return {
                    label: item.pmName,
                    value: item.pmId,
                };
            }),
            updateType: 20,
        },
        // 6: {
        //     option: "批量修改备注",
        //     permission: "expensebill-canedit",
        //     dialogTitle: "批量修改备注",

        // },
    });
});
const importShow = ref(false);
const importWay = ref("0");
const selectImportMethod = (type: string) => {
    importShow.value = true;
    importWay.value = type;
};
const exportHandle = (all: boolean) => {
    emits("export", all);
};
const batchUpdateDialogVisible = ref(false);
const batchUpdateDialogType = ref("0");

const openBatchUpdateDialog = (type: string) => {
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: "请先选择单据！",
        });
        return;
    }
    if (props.checkedTableData.every((item) => item.v_id) && type === "1") {
        ElNotify({
            type: "warning",
            message: "单据数据已生成凭证，请先删除凭证！",
        });
        return;
    }
    batchUpdateDialogType.value = type;
    if (type === "3" || type === "4") {
        // 3.收款人/职员4.部门
        nextTick(() => {
            dialogAccountingRef.value?.handleInit();
        });
    } else {
        batchUpdateDialogVisible.value = true;
    }
};
const openVoucherFromBill = () => {
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: "请选择单据后生成凭证！",
        });
        return;
    }
    emits("goGenerateVoucher");
};
const expenseAccountSetting = () => {
    emits("expenseAccountSetting");
};
const downloadTemplate = () => globalExport("/api/ExpenseBill/ExportImportTemplate");
const uploadSuccess = (res: IResponseModel<{ endDate: string; error: string; startDate: string; status: boolean }>) => {
    if (res.state === 1000 && res.data.status) {
        ElNotify({
            type: "success",
            message: "导入成功！",
        });
        importShow.value = false;
        emits("importSuccess", res.data.startDate, res.data.endDate);
        handleExpiredCheckData(ExpiredCheckModuleEnum.Invoice);
    } else {
        ElNotify({
            type: "warning",
            message: res.msg || res.data.error,
        });
    }
};
const certificationSure = () => {
    let optionAbleIdList: ITableItem[] = [];
    let currentOption = batchOptions.value[batchUpdateDialogType.value];
    props.checkedTableData.forEach((item) => {
        if (!item.v_id) {
            if (!override.value && (currentOption.updateType === 30 || currentOption.updateType === 40)) {
                if (currentOption.updateType === 40 && !item.depId) {
                    optionAbleIdList.push(item);
                } else if (currentOption.updateType === 30 && !item.payeeId) {
                    optionAbleIdList.push(item);
                }
            } else {
                optionAbleIdList.push(item);
            }
        }
    });
    batchUpdateDialogVisible.value = false;
    if (!optionAbleIdList.length) {
        if (currentOption.updateType === 0) {
            ElNotify({
                type: "warning",
                message: `单据数据已生成凭证，请先删除凭证！`,
            });
        } else {
            ElNotify({
                type: "warning",
                message: `成功：${optionAbleIdList.length}，跳过：${
                    props.checkedTableData.length - optionAbleIdList.length
                } (已生成凭证的单据已跳过)`,
            });
        }
        return;
    }
    let params: {
        bills: ITableItem[];
        optype?: number;
        updid?: number;
        payeetype?: number | string;
    } =
        currentOption.updateType === 0
            ? { bills: optionAbleIdList }
            : {
                  bills: optionAbleIdList,
                  optype: currentOption.updateType,
                  updid: currentOption.selected,
                  payeetype: currentOption.updateType === 30 ? 10003 : "",
              };

    request({
        url: "/api/ExpenseBill/BatchOperate",
        method: currentOption.updateType === 0 ? "delete" : "put",
        data: params,
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            ElNotify({
                type: window.isErp ? "warning" : "success",
                message: `成功：${optionAbleIdList.length}，跳过：${
                    props.checkedTableData.length - optionAbleIdList.length
                }(已生成凭证的单据已跳过)`,
            });
            emits("reloadTableData");
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
};
const handleBatchEditDepartmentOrEmployee = (aaNum: string, aatype: number) => {
    if (!aaNum) {
        ElNotify({
            type: "warning",
            message: "请选择" + (aatype === 10003 ? "职员" : "部门"),
        });
        return;
    }
    batchOptions.value[batchUpdateDialogType.value].selected = aaNum;
    certificationSure();
};

const override = ref(true);
const handleOverride = (val: boolean) => {
    override.value = val;
};

defineExpose({
    showAll,
});

const showTypeOPayList = ref<any[]>([]);
watch(
    () =>batchUpdateDialogType.value,
    (val) => {
        if ( val === "2" || val === "5") {
            showTypeOPayList.value = JSON.parse(JSON.stringify(batchOptions.value[batchUpdateDialogType.value].optionList));
        } else {
            showTypeOPayList.value = [];
        }
    },
    { immediate: true }
);
const TypeOPayMethod = (value: string) => {
    showTypeOPayList.value = commonFilterMethod(value, batchOptions.value[batchUpdateDialogType.value].optionList, 'label');
}
</script>

<style scoped lang="less">
.main-tool-right {
    ul,
    li {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .item {
        position: relative;

        &:hover {
            ul {
                display: block;
            }
        }
    }

    ul {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        left: 1px;
        display: none;

        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;

            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }
    .downlist {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;

            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }

    .down-click {
        width: 19px;
        margin-left: 1px;
        background: url("@/assets/Icons/down-white.png") no-repeat center;
        background-color: var(--main-color);

        & + ul {
            width: 106px !important;
        }

        &:hover {
            border-color: var(--light-main-color);
            background-color: var(--light-main-color);
        }
    }
}
.custom-confirm {
    .certification-show-content {
        .certification-form {
            padding: 45px 40px 20px;

            :deep(.el-date-editor.el-input) {
                width: 160px;
            }
        }

        .delete-tip {
            padding: 40px 70px;
            display: block;
            text-align: center;
        }

        .certification-res-tip {
            padding: 40px 70px 20px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
