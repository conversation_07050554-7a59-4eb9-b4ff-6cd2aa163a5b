export const getScrollTop = () => {
    return document.querySelector(".router-container")?.scrollTop || 0;
};

export const setScrollTop = (scrollTop: number) => {
    document.querySelector(".router-container") && (document.querySelector(".router-container")!.scrollTop = scrollTop);
};

export const goToScrollTop = () => {
    setScrollTop(0);
};

export const getBrowserState = (): boolean => {
    const userAgent = navigator.userAgent;
    const edge = /Edge\/(\d+)\./.exec(userAgent);
    if (edge) {
        const version = Number(edge[1]);
        if (version >= 85) {
            return true;
        } else {
            return false;
        }
    }
    const chromeMs = /Chrome\/(\d+)\./.exec(userAgent);
    if (chromeMs) {
        const version = Number(chromeMs[1]);
        if (version >= 85) {
            return true;
        } else {
            return false;
        }
    }
    if (/Safari\//.test(userAgent)) {
        const version = Number(/Version\/(\d+)\./.exec(userAgent)![1] || 0);
        if (version >= 14) {
            return true;
        } else {
            return false;
        }
    }
    const firefox = /Firefox\/(\d+)\./.exec(userAgent);
    if (firefox) {
        const version = Number(firefox[1]);
        if (version >= 79) {
            return true;
        } else {
            return false;
        }
    }
    return false;
};

export const replaceAll = (str: string, searchValue: string, replaceValue: string) => {
    return str.replace(new RegExp(searchValue, "g"), replaceValue);
};
