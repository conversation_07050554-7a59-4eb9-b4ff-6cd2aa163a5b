<template>
    <div class="div-audit-first" v-show="noShowAuditTable">
        <div class="audit-first-title">审计无忧，一键提取</div>
        <div class="audit-line">
            <div class="audit-info">审计软件：</div>
            <div class="audit-content">
                <el-select 
                    v-model="auditSoftWare" 
                    :teleported="false"
                    :filterable="true"
                    :filter-method="auditSoftFilterMethod"
                >
                    <el-option v-for="item in showAuditSoftWareList" :key="item.value" :label="item.text" :value="item.value" />
                </el-select>
            </div>
        </div>
        <div class="audit-line">
            <div class="audit-info">审计期间：</div>
            <div class="audit-content small date-picker">
                <DatePicker
                    v-model:startPid="startMonth"
                    v-model:endPid="endMonth"
                    :clearable="false"
                    :editable="false"
                    :dateType="'month'"
                    :value-format="'YYYYMM'"
                    :label-format="'YYYY年MM月'"
                    :disabledDateStart="disabledDate"
                    :disabledDateEnd="disabledDate"
                    @userChange="userChange"
                />
            </div>
        </div>
        <div class="audit-first-button mt-40">
            <a class="button solid-button" @click="processAudit">一键提取</a>
        </div>
    </div>
    <div class="div-audit-last slot-content" v-show="!noShowAuditTable">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <a class="button solid-button large-1" @click="auditProcess">一键提取</a>
            </div>
            <div class="main-tool-right" v-if="onlyShowAuditFile">
                <span style="font-size: var(--font-size); transform: translateY(20px)">{{ diskState }}</span>
            </div>
        </div>
        <div class="main-center">
            <Table 
                empty-text="暂无数据" 
                :data="divAuditMainInfoData" 
                :columns="divAuditMainInfoColumhs()" 
                :rowClassName="judgeRowClassName"
                :tableName="setModule"
            >
                <template #operator>
                    <el-table-column label="操作" min-width="195" align="left" header-align="center" :resizable="false">
                        <template #default="scope">
                            <span v-show="scope.row.progress == 100">
                                <a class="link" v-permission="['backup-candelete']" @click="deleteBackupItem(scope.row.fileName, 'audit')">
                                    删除
                                </a>
                                <a
                                    class="link"
                                    v-permission="['backup-canview']"
                                    @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)"
                                    v-if="scope.row.expiredDate === ''"
                                >
                                    <span>下载</span>
                                </a>
                                <a class="link" v-permission="['backup-canview']" v-else>
                                    <el-popover
                                        placement="bottom-end"
                                        :width="300"
                                        trigger="hover"
                                        popper-class="tip-popover"
                                        :popper-style="popperStyle"
                                    >
                                        <template #reference>
                                            <span>
                                                <span @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)">下载</span>
                                                <img
                                                    src="@/assets/Settings/warnning-orange.png"
                                                    alt=""
                                                    style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                                />
                                            </span>
                                        </template>
                                        空间不足！已为您提供额外存储空间备份， <br />
                                        请于72小时内下载到本地，超时将自动删除
                                    </el-popover>
                                </a>
                            </span>
                            <span v-show="scope.row.progress !== 100">
                                <span v-show="scope.row.progress !== 0" style="display: flex; align-items: center">
                                    <span class="progress-bar">
                                        <span class="progress-solid-bar" :style="{ width: scope.row.progress + '%' }"></span>
                                    </span>
                                    <span>{{ scope.row.progress + "%" }}</span>
                                </span>
                                <span class="loading-operator" v-show="scope.row.progress === 0">
                                    <span class="loading-icon"></span>
                                    <span class="link" style="margin-left: 3px">数据加载中...</span>
                                </span>
                            </span>
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
    </div>
    <el-dialog title="审计取数" center width="420" v-model="auditProcessDialogShow" class="custom-confirm dialogDrag">
        <div class="common-dialog-content" v-dialogDrag>
            <div class="common-dialog-main process">
                <div class="audit-line">
                    <div class="audit-info">审计软件：</div>
                    <div class="audit-content">
                        <el-select 
                            v-model="auditSoftWare" 
                            :teleported="false"
                            :filterable="true"
                            :filter-method="auditSoftFilterMethod"
                        >
                            <el-option v-for="item in showAuditSoftWareList" :key="item.value" :label="item.text" :value="item.value" />
                        </el-select>
                    </div>
                </div>
                <div class="audit-line" style="margin-top: 16px">
                    <div class="audit-info">审计期间：</div>
                    <div class="audit-content small date-picker">
                        <DatePicker
                            v-model:startPid="startMonth"
                            v-model:endPid="endMonth"
                            :clearable="false"
                            :editable="false"
                            :dateType="'month'"
                            :value-format="'YYYYMM'"
                            :label-format="'YYYY年MM月'"
                            :disabledDateStart="disabledDate"
                            :disabledDateEnd="disabledDate"
                            @userChange="userChange"
                        />
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleProcessAudit">确定</a>
                <a class="button ml-10" @click="() => (auditProcessDialogShow = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="审计取数" center width="440" v-model="auditDialogShow" class="custom-confirm dialogDrag">
        <div class="common-dialog-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="common-dialog-main">
                <div class="audit-success-title">
                    <img src="@/assets/Settings/audit-success.png" />
                    <span>审计数据获取成功</span>
                </div>
                <div class="audit-success-info">提示：审计数据需要下载提供给审计使用</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="auditDownload">下载</a>
                <a class="button ml-10" @click="() => (auditDialogShow = false)">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watchEffect } from "vue";
import { divAuditMainInfoColumhs, judgeRowClassName, popperStyle } from "../utils";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { useLoading } from "@/hooks/useLoading";

import type { ITableData, IAuditSoftWare, ISearchPeriod, ICheckSpace, IProcessBack } from "../types";

import Table from "@/components/Table/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { commonFilterMethod } from "@/components/Select/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { dayjs } from "element-plus";
import { initStartOrEndMonth } from "@/components/DatePicker/utils";

const setModule = "DivAudit";
const isErp = ref(window.isErp);

const props = defineProps<{
    tableData: ITableData[];
    auditSoftWare: number;
    startPid: number;
    endPid: number;
    auditSoftWareList: IAuditSoftWare[];
    searchPeriodList: ISearchPeriod[];
    noShowAuditTable: boolean;
    showSpaceDialog: (usedSpace: number, totalSpace: number) => void;
    onlyShowAuditFile: boolean;
    diskState: string;
}>();
const emit = defineEmits([
    "insertRow",
    "deleteBackupItem",
    "downloadbackupitem",
    "update:noShowAuditTable",
    "update:auditSoftWare",
    "update:startPid",
    "update:endPid",
]);
const reversePList = ref<any[]>([]);
watchEffect(() => {
    const list = JSON.parse(JSON.stringify(props.searchPeriodList));
    reversePList.value = list.reverse().map((item: ISearchPeriod) => {
        return {
            ...item,
            time: item.year + "" + String(item.sn).padStart(2, "0"),
        };
    });
});

const divAuditMainInfoData = computed(() => props.tableData);
const noShowAuditTable = computed({
    get: () => props.noShowAuditTable,
    set: (val) => emit("update:noShowAuditTable", val),
});
const auditSoftWare = computed({
    get: () => props.auditSoftWare,
    set: (val) => emit("update:auditSoftWare", val),
});
const startPid = computed({
    get: () => props.startPid,
    set: (val) => emit("update:startPid", val),
});
const endPid = computed({
    get: () => props.endPid,
    set: (val) => emit("update:endPid", val),
});
const auditProcessDialogShow = ref(false);
const fileType = "audit";
const auditDialogShow = ref(false);

const startMonth = ref("");
const endMonth = ref("");
watchEffect(() => {
    let result = initStartOrEndMonth(reversePList.value, Number(startPid.value), Number(endPid.value));
    startMonth.value = result.startMonth;
    endMonth.value = result.endMonth;
});
function disabledDate(time: Date) {
    const start = reversePList.value[reversePList.value.length - 1]?.time ?? new Date();
    const end = reversePList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
function userChange() {
    startPid.value = reversePList.value.find((item) => item.time === startMonth.value)?.pid || 0;
    endPid.value = reversePList.value.find((item) => item.time === endMonth.value)?.pid || 0; 
}

const deleteBackupItem = (fileName: string, type: string) => emit("deleteBackupItem", fileName, type);
const downloadbackupitem = (fileName: string, fileType: number) => emit("downloadbackupitem", fileName, fileType);
const auditProcess = () => (auditProcessDialogShow.value = true);

const handleProcessAudit = () => {
    auditProcessDialogShow.value = false;
    processAudit();
};
const processAudit = () => {
    const startP = startPid.value;
    const endP = endPid.value;
    const confirmMsg = "您已有临时的审计文件，继续提取审计文件将自动删除之前的临时文件，是否继续？";
    if (startP > endP) {
        ElNotify({ type: "warning", message: "请选择正确的审计期间，开始期间不能晚于结束期间哦！" });
        return;
    }
    if (parseInt((startP - 1) / 12 + "", 10) != parseInt((endP - 1) / 12 + "", 10)) {
        ElNotify({ type: "warning", message: "请选择同一年度的审计期间哦~" });
        return;
    }

    const msg = window.isAccountingAgent
        ? "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
        : "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：";
    useLoading().enterLoading("账套备份中，请稍候...");
    request({ url: "/api/Backup/HasTemporaryFile?fileType=" + fileType, method: "post" }).then((r: IResponseModel<boolean>) => {
        if (r.state === 1000 && r.data) {
            useLoading().quitLoading();
            ElConfirm(confirmMsg).then((r: boolean) => {
                if (r) {
                    useLoading().enterLoading("账套备份中，请稍候...");
                    go();
                }
            });
        } else {
            request({ url: "/api/Backup/CheckSpace?fileType=" + fileType, method: "post" })
                .then((r: IResponseModel<ICheckSpace>) => {
                    if (r.state === 1000) {
                        if (r.data.overflow) {
                            if (useThirdPartInfoStoreHook().isThirdPart) {
                                props.showSpaceDialog(r.data.state.usedSpace, r.data.state.totalSpace);
                            } else {
                                useLoading().quitLoading();
                                tryShowPayDialog(1, "backup-" + fileType, msg, "会计电子档案", go);
                                return;
                            }
                        }
                    }
                    go();
                })
                .catch(() => {
                    useLoading().quitLoading();
                });
        }
    });
};
const go = () => {
    const software = auditSoftWare.value;
    const startP = startPid.value;
    const endP = endPid.value;
    const params = { software, startP, endP, allowTemporaryFile: 1 };
    const url = "/api/Backup/ProcessAudit?" + getUrlSearchParams(params);
    request({ url, method: "post" })
        .then((res: IResponseModel<IProcessBack>) => {
            useLoading().quitLoading();
            if (res.state !== 1000) {
                if (res.msg) {
                    ElNotify({ type: "warning", message: res.msg });
                    return;
                }
                ElNotify({ type: "warning", message: "备份操作出错，请稍后重试" });
                return;
            } else {
                const row = res.data;
                auditDialogShow.value = true;
                emit("insertRow", row, "audit");
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "提取审计文件操作出错，请稍后重试" });
        });
};
const auditDownload = () => {
    downloadbackupitem(divAuditMainInfoData.value[0].fileName, divAuditMainInfoData.value[0].fileType);
    auditDialogShow.value = false;
};

const showAuditSoftWareList = ref<Array<IAuditSoftWare>>([]);
watchEffect(() => { 
    showAuditSoftWareList.value = JSON.parse(JSON.stringify(props.auditSoftWareList));  
});
function auditSoftFilterMethod(value: string) {
    showAuditSoftWareList.value = commonFilterMethod(value, props.auditSoftWareList, 'text');
}
</script>

<style lang="less" scoped>
@import "@/style/Settings/Backup.less";
.audit-content.date-picker {
    :deep(.el-input) {
        width: 112px;
        .el-input__wrapper {
            width: 112px;
        }
    }
}
</style>
