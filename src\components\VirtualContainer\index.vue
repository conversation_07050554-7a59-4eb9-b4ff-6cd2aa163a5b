<template>
    <el-scrollbar :always="true" :height="height" @scroll="handleScroll">
        <div class="container" :style="{ top: listTop }">
            <div
                class="item"
                v-for="item in showData"
                :key="item[virtualKey]"
                :style="{ height: size + 'px' }"
                :class="currentKey === item[virtualKey] ? 'active' : ''"
                @click="nodeClick(item)"
            >
                <span class="item-box" style="display: inline-block">
                    <span class="item-indent" :style="{ width: indent + 'px', display: 'inline-block' }"></span>
                    <span class="item-content">{{ item[virtualLabel] }}</span>
                </span>
            </div>
        </div>
    </el-scrollbar>
</template>

<script setup lang="ts">
import { ref, toRefs, computed } from "vue";

import { ElScrollbar } from "element-plus";

interface IProp {
    label: string;
    value: string;
}

const props = withDefaults(
    defineProps<{
        data: any[];
        size?: number;
        shownumber?: number;
        indent?: number;
        height?: string;
        prop?: IProp;
    }>(),
    {
        size: 21,
        shownumber: 100,
        indent: 21,
        height: "auto",
        prop: () => ({
            label: "text",
            value: "id",
        }),
    }
);

const virtualKey = computed(() => props.prop.value);
const virtualLabel = computed(() => props.prop.label);

// 使用 toRefs 包裹 props，让解构获得的父组件传递的参数变为响应式的
const { data, size, shownumber } = toRefs(props);

let start = ref(0); // 要展示的数据的起始下标
let end = ref(shownumber.value); // 要展示的数据的结束下标

const showData = computed(() => data.value.slice(start.value, end.value)); // 最终筛选出的要展示的数据
// const containerHeight = computed(() => size.value * shownumber.value + "px"); // 容器的高度
// const barHeight = computed(() => size.value * items.value.length + "px"); // 撑开容器内容高度的元素的高度
const listTop = computed(() => start.value * size.value + "px"); // 列表向上滚动时要动态改变 top 值

// 容器的滚动事件需要防抖处理
let timer: any = null;
const handleScroll = (event: { scrollLeft: number; scrollTop: number }) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
        // 获取容器顶部滚动的尺寸
        const scrollTop = event.scrollTop ?? 0;

        // 计算卷去的数据条数，用计算的结果作为获取数据的起始和结束下标
        // 起始的下标就是卷去的数据条数，向下取整
        start.value = Math.floor(scrollTop / size.value);
        // 结束的下标就是起始的下标加上要展示的数据条数
        end.value = start.value + shownumber.value;
    }, 10);
};

const currentKey = ref("");

const setCurrentKey = (key: string) => {
    currentKey.value = key;
};

const getCurrentNode = () => {
    return data.value.find((item) => item[virtualKey.value] === currentKey.value);
};

defineExpose({
    setCurrentKey,
    getCurrentNode,
});

const emit = defineEmits(["nodeClick"]);

const nodeClick = (item: any) => {
    currentKey.value = item[virtualKey.value];
    emit("nodeClick", item);
};
</script>

<style lang="less" scoped>
.container {
    position: absolute;
    top: 0;
    min-width: 100%;
    display: inline-block;
    .item {
        font-size: var(--font-size);
        line-height: 21px;
        text-align: left;
        white-space: nowrap;
        cursor: pointer;

        &.active {
            background-color: var(--table-hover-color);
        }
    }
}

:deep(.el-scrollbar__view) {
    height: 100%;
    position: relative;
}
</style>
