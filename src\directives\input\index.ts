import type { Directive } from "vue"

interface ElementWithValue extends HTMLElement {
  value: string
}

function integer(el: ElementWithValue): void {
  el.value = el.value.replace(/[^\d]/g, "")
}

const map: { [key: string]: (el: ElementWithValue) => void } = { integer }

export const input: Directive = {
  mounted(el, binding) {
    // 使用实际的输入元素
    const inputEl = el.querySelector(".el-input__inner") || el.querySelector(".el-textarea__inner") || el

    let lock = false // 标记是否需要锁定输入框
    let isHandling = false // 标记是否正在处理
    let lastValue: string | null = null

    const handler = () => {
      if (lock) return
      if (isHandling) return
      if (inputEl.value === lastValue) return
      isHandling = true

      const modifiers = Object.keys(binding.modifiers)
      const newModifier = modifiers[0] || "integer"
      if (map[newModifier as keyof typeof map]) {
        map[newModifier](inputEl as ElementWithValue)
      }

      lastValue = inputEl.value
      Promise.resolve().then(() => {
        inputEl.dispatchEvent(new Event("input"))
      })
      isHandling = false
    }

    // 添加事件监听器
    inputEl.addEventListener("input", handler)
    inputEl.addEventListener("compositionstart", () => {
      lock = true
    })
    inputEl.addEventListener("compositionend", () => {
      lock = false
      inputEl.dispatchEvent(new Event("input"))
    })

    // 保存事件处理函数引用，用于之后的清理
    if (!el._handlers) el._handlers = {}
    el._handlers.inputHandler = {
      el: inputEl,
      handler,
    }
  },

  unmounted(el) {
    // 清理事件监听器
    if (el._handlers?.inputHandler) {
      const { el: inputEl, handler } = el._handlers.inputHandler
      inputEl.removeEventListener("input", handler)
      inputEl.removeEventListener("compositionstart", () => {})
      inputEl.removeEventListener("compositionend", () => {})
      delete el._handlers.inputHandler
    }
  },
}
