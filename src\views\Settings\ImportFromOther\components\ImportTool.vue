<template>
    <div class="import-tool">
        <div class="main-center-tool">
            <div class="main-center-client-right">
                <div class="right-title">柠檬云导账工具</div>
                <div class="right-text">
                    柠檬云导账工具可以打通用户的数据通道，节省用户迁移数据的人力投入，帮助用户快速体验财务软件的智能记账过程！
                </div>
                <div class="right-content">
                    <div class="content-1">
                        <div class="content-img1"></div>
                    </div>
                    <div class="content-2">
                        <div class="content-title">快速</div>
                        <div class="content-text">一键操作，快速迁移</div>
                    </div>
                    <div class="content-3">
                        <div class="content-img2"></div>
                    </div>
                    <div class="content-4">
                        <div class="content-title">易用</div>
                        <div class="content-text">操作界面简单易用</div>
                    </div>

                    <div class="content-1">
                        <div class="content-img3"></div>
                    </div>
                    <div class="content-2">
                        <div class="content-title">自动</div>
                        <div class="content-text">全流程自动化操作</div>
                    </div>
                    <div class="content-3">
                        <div class="content-img4"></div>
                    </div>
                    <div class="content-4">
                        <div class="content-title">高效</div>
                        <div class="content-text">快速轻松，提高效率</div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="props.softWareType === 1 && props.showImportToolTips" class="top-tips">
            <img src="@/assets/Settings/tips.png" alt="" style="width: 14px; margin-right: 5px" />
            您选择的“
            <span :title="props.softWareName">{{ softWareName }}</span>
            ”仅支持导账工具导账，请下载
            <a class="link" @click.prevent="() => emits('download')">导账工具</a>
            或扫码添加客服免费进行导账
        </div>
        <div class="buttons">
            <a @click.prevent="() => emits('download')" class="button solid-button mr-20" style="margin-top: -13px; text-decoration: none">
                立即下载
            </a>
            <a class="button return-btn" @click.prevent="() => emits('returnBack')">上一步</a>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { computed } from "vue";

const props = withDefaults(
    defineProps<{
        softWareName: string;
        softWareType: number;
        showImportToolTips: boolean;
    }>(),
    {
        softWareName: "",
        showImportToolTips: false,
    }
);
const emits = defineEmits<{
    (e: "download"): void;
    (e: "returnBack"): void;
}>();

const softWareName = computed(() => {
    return props.softWareName.length > 10 ? `${props.softWareName.substr(0, 10)}...` : props.softWareName;
});
</script>
<style lang="less" scoped>
.import-tool {
    position: relative;
    margin: 0 auto;
    .top-tips {
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 700px;
        height: 33px;
        padding: 0 12px;
        font-size: 14px;
        line-height: 33px;
        background-color: rgba(255, 191, 0, 0.02);
        border: 1px solid #ffbf00;
    }
    .main-center-tool {
        position: relative;
        width: 1000px;
        margin: 0 auto;
        height: 345px;
        padding-top: 50px;
        .main-center-client-right {
            width: 500px;
            height: 330px;
            margin: 0 auto;
            .right-title {
                font-size: 18px;
                font-weight: 500;
                color: rgba(51, 51, 51, 1);
                padding-top: 20px;
                text-align: left;
            }
            .right-text {
                text-align: left;
                width: 465px;
                height: 40px;
                font-size: 14px;
                font-weight: 400;
                color: rgba(93, 93, 93, 1);
                line-height: 25px;
                padding-top: 16px;
            }
            .right-content {
                height: 150px;
                text-align: left;
                > div {
                    height: 45px;
                    float: left;
                    margin-top: 40px;
                }
                .content-1,
                .content-3 {
                    width: 50px;
                    > div {
                        width: 41px;
                        height: 40px;
                        margin-top: 2px;
                        &.content-img1 {
                            box-shadow: 0px 1px 1px 0px rgba(253, 116, 0, 0.16);
                            background-image: url("@/assets/Settings/group1.png");
                        }
                        &.content-img2 {
                            box-shadow: 0px 1px 0px 0px rgba(1, 194, 87, 0.1);
                            background-image: url("@/assets/Settings/group2.png");
                        }
                        &.content-img3 {
                            box-shadow: 0px 1px 0px 0px rgba(24, 144, 255, 0.1);
                            background-image: url("@/assets/Settings/group3.png");
                        }
                        &.content-img4 {
                            box-shadow: 0px 1px 0px 0px rgba(250, 202, 27, 0.16);
                            background-image: url("@/assets/Settings/group4.png");
                        }
                    }
                }
                .content-2,
                .content-4 {
                    width: 200px;
                    .content-title {
                        font-size: 18px;
                        font-weight: 500;
                        color: rgba(51, 51, 51, 1);
                        line-height: 25px;
                        padding-left: 15px;
                    }
                    .content-text {
                        font-size: 14px;
                        font-weight: 400;
                        color: rgba(93, 93, 93, 1);
                        padding-left: 15px;
                    }
                }
            }
        }
    }
    .buttons {
        margin-top: 20px;
        .return-btn {
            border: 1px solid var(--main-color);
            color: var(--main-color);
            &:hover {
                color: #fff;
            }
        }
    }
}
</style>
