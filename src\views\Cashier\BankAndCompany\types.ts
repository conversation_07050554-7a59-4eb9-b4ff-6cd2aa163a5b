export interface IBankAccount {
    ac_id: string;
    ac_name: string;
    ac_no: string;
    ac_type: string;
    as_id: string;
    asub: string;
    asub_code: string;
    asub_name: string;
    bank_account: string;
    currency: string;
    currency_name: string;
    state: string;
}

export interface IBankHandleResult {
    code: number;
    message: string;
    data: IBankAuthorized;
}

export interface CMBGradeResult {
    status: number;
    message: string;
    data: CMBGradeResultData;
}
export interface CMBGradeResultData {
    Code: string;
    Msg: string;
    AesEncryptKey?: string;
    PubKey?: string;
    authUrl?: string;
}

export interface IBankAuthorized {
    authorized: number;
    errorMsg: string;
    pubKey: string;
    authUrl: string;
    priKey: string;
    aesKey: string;
    code: string;
    canApply: number;
    state: number;
    signStatus: string;
    signMessage: string;
}

export interface IAutoBankParams {
    accname: string;
    uscc: string;
    acid: string;
    mainaccno: string;
    enterprise_number?: string;
    uid?: string;
}