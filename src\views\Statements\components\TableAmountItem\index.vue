<script lang="ts" setup>
import { formatMoney } from "@/util/format";
import { noteFormatter } from "@/views/Statements/utils";
import { ref } from "vue";
import Popover from "@/components/Popover/index.vue";

const props = withDefaults(
    defineProps<{
        amount: number | string;
        formula: string;
        lineNumber: number;
        iconShow?: boolean;
        negativeHighlighted?: boolean;
        textAlign?: string;
        placement?: any;
        queryFormulas?: boolean;
        showLabel?: boolean;
    }>(),
    {
        amount: "",
        formula: "",
        lineNumber: 0,
        iconShow: true,
        negativeHighlighted: true,
        textAlign: "right",
        placement: "right",
        queryFormulas: false,
        showLabel: true,
    }
);
const oraginalValue = Number(props.amount);
let visible = ref(false);
const emit = defineEmits<{
    (e: "go-to-query-formulas"): void;
}>();
const goToQueryFormulas = () => {
    emit("go-to-query-formulas");
};
</script>

<template>
    <template v-if="oraginalValue != 0 || props.lineNumber > 0">
        <div v-if="!queryFormulas" :class="['note-popover-item', iconShow ? '' : 'icon-none-item']" @mouseleave="visible = false">
            <Popover v-if="iconShow" :title="showLabel ? '公式：' : ''" :content="noteFormatter(formula, 0)" :placement="placement">
                <template #trigger>
                    <div :class="['calc-icon', { 'icon-show': visible }]" @click="visible = true" @mouseleave="visible = false"></div>
                </template>
            </Popover>
            <span :class="[Number(props.amount) < 0 && negativeHighlighted ? 'highlight-red' : '', 'cell-value']">
                {{ String(amount).includes("%") ? amount : formatMoney(props.amount) }}
            </span>
        </div>
        <div v-else :class="['note-popover-item', iconShow ? '' : 'icon-none-item']" @mouseleave="visible = false">
            <Popover v-if="iconShow" :title="showLabel ? '公式：' : ''" :content="noteFormatter(formula, 0)" :placement="placement">
                <template #trigger>
                    <div :class="['calc-icon', { 'icon-show': visible }]" @click="visible = true" @mouseleave="visible = false"></div>
                </template>
            </Popover>
            <span
                :class="[Number(props.amount) < 0 && negativeHighlighted ? 'highlight-red' : '', 'cell-value', 'cell-link']"
                @click="goToQueryFormulas"
            >
                {{ String(amount).includes("%") ? amount : formatMoney(props.amount) }}
            </span>
        </div>
    </template>
</template>

<style lang="less" scoped>
@import (reference) "@/style/Common.less";
.tooltip-dialog-content {
    padding: 10px;
    width: 207px;
    .set-border;
    text-align: left;
    div {
        white-space: normal;
        .set-font(var(--font-color),var(--h5),18px);
    }
    &.hide-popover {
        display: none;
    }
}
.note-popover-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &.icon-none-item {
        justify-content: flex-end;
    }
}
span.cell-link {
    position: relative;
    cursor: pointer;
    &:hover {
        color: #3385ff !important;
    }
}
body[erp] {
    .main-center {
        tr.current-row {
            td {
                .cell {
                    .icon-show.calc-icon {
                        width: 12px;
                        height: 12px;
                        background-position: 0;
                        background-size: contain;
                        background-image: url("@/assets/Statements/equal-erp.png");
                    }
                }
            }
        }
    }
}
</style>
