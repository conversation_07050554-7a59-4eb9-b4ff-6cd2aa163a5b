<template>
    <div class="content">
        <div class="title">数据归档</div>
        <div class="main-content">
            <div class="main-top">
                <p>
                    已用空间 （<span>{{ usedSpace }}</span> / <span>{{ totalSpace }}</span
                    >）
                </p>
                <div>
                    <a v-permission="['backup-canbackup']" class="button solid-button large-1 mr-10" @click="processExcelAndPdfBackup">
                        归档
                    </a>
                    <a v-permission="['backup-candelete']" class="button solid-button large-1" @click="batchDelete">删除</a>
                </div>
            </div>
            <div class="divider-line"></div>
            <div class="main-center" id="backupGrid">
                <Table
                    empty-text="暂无数据"
                    :data="tableData"
                    :columns="columns"
                    :rowClassName="judgeRowClassName"
                    @selection-change="handleSelectionChange"
                    :scrollbar-show="true"
                    :tableName="setModule"
                >
                    <template #operator>
                        <el-table-column label="操作" min-width="120" align="left" header-align="center" :resizable="false">
                            <template #default="scope">
                                <span v-show="scope.row.progress == 100">
                                    <a class="link" v-permission="['backup-candelete']" @click="deleteBackupItem(scope.row.fileName)">
                                        删除
                                    </a>
                                    <a class="link" v-permission="['backup-canview']" v-if="scope.row.expiredDate">
                                        <el-popover placement="bottom-end" :width="300" trigger="hover" popper-class="tip-popover">
                                            <template #reference>
                                                <span>
                                                    <a @click="downloadbackupitem(scope.row.fileName)"> 限时下载 </a>
                                                    <img
                                                        src="@/assets/Settings/warnning-orange.png"
                                                        alt=""
                                                        style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                                    />
                                                </span>
                                            </template>
                                            空间不足！已为您提供额外存储空间备份， <br />
                                            请于72小时内下载到本地，超时将自动删除
                                        </el-popover>
                                    </a>
                                    <a
                                        class="link"
                                        v-permission="['backup-canview']"
                                        @click="downloadbackupitem(scope.row.fileName)"
                                        v-else
                                    >
                                        <span>下载</span>
                                    </a>
                                </span>
                                <span v-show="scope.row.progress !== 100">
                                    <span v-show="scope.row.progress !== 0" style="display: flex; align-items: center">
                                        <span class="progress-bar">
                                            <span class="progress-solid-bar" :style="{ width: scope.row.progress + '%' }"></span>
                                        </span>
                                        <span>{{ scope.row.progress + "%" }}</span>
                                    </span>
                                    <span class="loading-operator" v-show="scope.row.progress === 0">
                                        <span class="loading-icon"></span>
                                        <span class="link" style="margin-left: 3px">数据获取中...</span>
                                    </span>
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "BackUpPlus",
};
</script>
<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { onMounted, ref, watch } from "vue";
import { fileUtily, getLocalStorage } from "./utils";
import { getUrlSearchParams, globalExport } from "@/util/url";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IBackUpItem, IDiskStateBack, IGetFileRecord } from "./types";

import Table from "@/components/Table/index.vue";
import { ElConfirm } from "@/util/confirm";
import { useLoading } from "@/hooks/useLoading";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "BackUpPlus";
const columns: IColumnProps[] = [
    { slot: "selection", width: 36, headerAlign: "center", align: "center" },
    {
        label: "档案编码",
        prop: "fileCode",
        minWidth: 130,
        align: "left",
        headerAlign: "center",
        formatter: (row: any, column: any, value: string) => row.fileName.split("_")[1].replace(".zip", ""),
        width: getColumnWidth(setModule, "fileCode")
    },
    { 
        label: "档案名称", 
        prop: "fileName", 
        minWidth: 350, 
        align: "left", 
        headerAlign: 
        "center",
        width: getColumnWidth(setModule, "fileName") 
    },
    {
        label: "大小",
        prop: "fileSize",
        minWidth: 100,
        align: "left",
        headerAlign: "center",
        formatter: (row: any, column: any, value: number) => fileUtily.sizeFormatter(value),
        width: getColumnWidth(setModule, "fileSize")
    },
    {
        label: "归档日期",
        prop: "dateTime",
        minWidth: 118,
        align: "left",
        headerAlign: "center",
        formatter: (row: any, column: any, value: string) => value.substring(0, 10),
        width: getColumnWidth(setModule, "dateTime")
    },
    { label: "归档人", prop: "creator", minWidth: 93, align: "left", headerAlign: "center", width: getColumnWidth(setModule, "creator") },
    { slot: "operator" },
];

const selectionList = ref<Array<IBackUpItem>>([]);
const handleSelectionChange = (val: any) => {
    selectionList.value = val;
};

const processExcelAndPdfBackup = () => {
    const isProcess = tableData.value.some((item) => item.progress !== 100);
    if (isProcess) {
        ElNotify({ type: "warning", message: "存在备份中的文件" });
        return;
    }
    const params = {
        allowTemporaryFile: "1",
        isProcess: "1",
        balanceSheetIsClass: getLocalStorage("classificationSwitch"),
        cashFlowSheetIsClass: getLocalStorage("cashclassificationSwitch"),
    };
    useLoading().enterLoading("备份进行中，请稍候...");
    request({ url: "/api/Backup/ProcessExcelAndPdfBackup?" + getUrlSearchParams(params), method: "post" })
        .then((res: IResponseModel<IBackUpItem>) => {
            useLoading().quitLoading();
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "归档操作出错，请稍后重试" });
                return;
            }
            const row = res.data;
            tableData.value.unshift(row);
            getFileRecord(row.fileName, 0);
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "归档操作出错，请稍后重试" });
        });
};

const getFileRecord = (filename: string, index: number) => {
    request({ url: "/api/Backup/GetFileRecord?filename=" + filename, method: "post" }).then((res: IResponseModel<IGetFileRecord>) => {
        if (res.state === 1000) {
            filename = res.data.fileName;
            const row = tableData.value[0];
            const progress = res.data.progress;
            row.progress = progress;
            row.fileSize = res.data.fileSize;
            if (!filename) {
                ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
                tableData.value.splice(index, 1);
                return;
            }
            
            if (progress !== 100) {
                setTimeout(() => {
                    getFileRecord(filename, index);
                }, 5000);
            } else {
                getTableData();
                getDiskState();
                return;
            }
        }
    });
};

const batchDelete = () => {
    const checkedItems = selectionList.value;
    const deleteNames: Array<string> = [];
    if (checkedItems.length > 0) {
        for (var i = 0; i < checkedItems.length; i++) {
            if (checkedItems[i].progress == 100) {
                deleteNames.push(checkedItems[i].fileName);
            }
        }
    } else {
        ElNotify({ type: "warning", message: "请选择归档文件后删除" });
        return false;
    }
    if (deleteNames.length == 0) {
        ElNotify({ type: "warning", message: "选中的文件未完成备份" });
        return;
    }
    ElConfirm("是否删除选择的归档文件？").then((r: boolean) => {
        if (r) {
            const fileNameList = deleteNames;
            request({ url: "/api/Backup/ProcessBatchdelete", method: "post", data: fileNameList })
                .then((res: IResponseModel<string>) => {
                    if (res.state !== 1000) {
                        ElNotify({ type: "warning", message: res.msg || "删除失败" });
                        return;
                    }
                    ElNotify({ type: "success", message: "删除成功" });
                    getTableData();
                    getDiskState();
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "删除失败" });
                });
        }
    });
};

const usedSpace = ref("");
const totalSpace = ref("");

const getDiskState = () => {
    request({ url: "/api/Backup/GetDiskState", method: "post" }).then((res: IResponseModel<IDiskStateBack>) => {
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "请求失败" });
            return;
        }
        totalSpace.value = fileUtily.sizeFormatter(res.data.totalSpace, "2");
        usedSpace.value = fileUtily.sizeFormatter(res.data.usedSpace, "2");
    });
};

const tableData = ref<Array<IBackUpItem>>([]);
const getTableData = () => {
    request({ url: "/api/Backup/ExcelAndPdfList" }).then((res: IResponseModel<Array<IBackUpItem>>) => {
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "请求失败" });
            return;
        }
        tableData.value = res.data;
    });
};
onMounted(() => {
    getDiskState();
    getTableData();
});

watch(tableData, (val) => {
    val.forEach((item, index) => {
        if (item.progress != 100) {
            getFileRecord(item.fileName, index);
        }
    });
});

let canDelete = true;
function deleteBackupItem(filename: string) {
    if (!canDelete) return;
    ElConfirm("确定删除该归档文件？").then((r: boolean) => {
        if (r) {
            const fileNameList = [filename];
            canDelete = false;
            request({ url: "/api/Backup/ProcessBatchdelete", method: "post", data: fileNameList })
                .then((res: IResponseModel<string>) => {
                    canDelete = true;
                    if (res.state !== 1000) {
                        ElNotify({ type: "warning", message: res.msg || "删除失败" });
                        return;
                    }
                    ElNotify({ type: "success", message: "删除成功" });
                    const index = tableData.value.findIndex((item) => item.fileName === filename);
                    if (index > -1) tableData.value.splice(index, 1);
                    getDiskState();
                })
                .catch(() => {
                    canDelete = true;
                    ElNotify({ type: "warning", message: "删除失败" });
                });
        }
    });
}
function downloadbackupitem(fileName: string) {
    const url = "/api/Backup/DownloadFile?fileType=excelandpdf&fileName=" + encodeURIComponent(fileName);
    globalExport(url);
}
const judgeRowClassName = (scope: any) => {
    return scope.row.expiredDate ? "lack-space-tip" : "";
};
</script>

<style scoped lang="less">
body[erp] .content {
    width: 100%;
    .main-content {
        width: 100%;
        .main-top {
            padding: 10px;
            line-height: 60px;
            flex-direction: column;
            align-items: flex-start;
            p {
                line-height: 20px;
                font-size: var(--h5);
                color: #808080;
            }
        }
        .main-center {
            padding: 0px 10px 10px 10px;
            :deep(.el-table) {
                border: none;
                box-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
            }
        }
        // 分割线调整
        .main-top,
        .main-center {
            width: 1000px;
            margin: 0 auto;
        }
        .divider-line {
            width: 100%;
            height: 1px;
            background-color: var(--border-color);
            margin-bottom: 20px;
        }
    }

    .progress-bar {
        width: 120px;
        height: 9px;
        border-radius: 5px;
        background-color: #e4e4e4;
        margin-right: 4px;
        display: flex;
        align-items: center;
        .progress-solid-bar {
            height: 9px;
            border-radius: 5px;
            background-color: #3385ff;
        }
    }
}

span.loading-operator {
    display: flex;
    align-items: center;
    span.loading-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url("@/assets/Icons/loading.svg") no-repeat;
        background-size: 100% 100%;
        animation: spin 3s linear infinite;
    }
}
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
</style>
