<template>
    <div class="help-tip">
        <el-tooltip class="box-item" effect="dark" placement="bottom">
            <div style="margin-right: 2px">
                <span class="help-icon"></span>
                <a class="link" style="font-size: 12px">帮助</a>
            </div>
            <template #content>
                <span
                    >设置损益类科目为“辅助核算-{{aatype}}”，
                    <br />
                    在期初或者凭证上录入相关数据，系统
                    <br />
                    会自动核算{{aatype}}利润表。</span
                >
            </template>
        </el-tooltip>
    </div>
</template>

<script setup lang="ts">
    defineProps({
        aatype:{
            type:String,
            required:true
        }
    })
</script>

<style lang="less" scoped>
.help-tip {
    line-height: 28px;
    position: relative;
    &:hover {
        .help-tip-expend {
            display: block;
        }
    }
    .help-tip-expend {
        display: none;
        position: absolute;
        left: -70px;
        top: 26px;
        padding-top: 6px;
        width: 227px;
        text-align: center;
        transition: var(--transition-time);
        z-index: 10;
        cursor: pointer;
        .help-tip-content {
            text-align: left;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 10px;
            box-shadow: 0px 0px 20px 0px rgba(179, 179, 179, 0.3);
            color: var(--white);
            font-size: var(--h5);
            line-height: 17px;
        }
    }
    .help-icon {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url(@/assets/Icons/help.png) no-repeat;
        vertical-align: middle;
        margin-right: 2px;
        padding-right: 5px;
    }
}
</style>
