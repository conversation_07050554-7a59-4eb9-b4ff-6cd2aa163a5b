import type { IPeriodData } from "./types";
import { createApp } from "vue";
import ElementPlus from "element-plus";
import { dialogDrag }  from "@/direcitves/dialogDrag/index";
import OpenAppDialog from "./components/OpenAppDialog.vue";

export const changePeriodList = (list: IPeriodData[]) => {
    const reverseList = Array.from(list).reverse();
    const newList = reverseList.map((item) => {
        return {
            label: item.year + "年" + item.sn + "月",
            pid: item.pid,
        };
    });
    return newList;
};

interface Region {
    value: string;
    label: string;
    children?: Region[];
}

export const findAddressName = (code: string, regions: Region[]): string => {
    for (const region of regions) {
        if (region.value === code) {
            return region.label;
        } else if (region.children) {
            const result = findAddressName(code, region.children);
            if (result) {
                return result;
            }
        }
    }
    return "";
};

interface IYearOrQuarter {
    label: string;
    pid: number;
}
export const mapToQuartersAndYear = (data: IPeriodData[]): { years: IYearOrQuarter[]; quarters: IYearOrQuarter[] } => {
    const quarters: IYearOrQuarter[] = [];
    const years: IYearOrQuarter[] = [];
    for (let i = 0; i < data.length; i++) {
        const { pid, year, sn } = data[i];
        if (pid % 3 === 0) {
            const label = `${year}年第${Math.ceil(sn / 3)}季度`;
            const currentQuarter = { label, pid };
            quarters.push(currentQuarter);
        }
        if (pid % 12 === 0) {
            const label = `${year}年`;
            const currentYear = { label, pid };
            years.push(currentYear);
        }
    }
    quarters.reverse();
    years.reverse();
    
    return { years, quarters };
};

export const OpenApp = (): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if(document.querySelector("#openAppDialog")) return
        const content = document.querySelector(".router-container .content")
        const contentBox = content;
        const props = {
            confirm: () => {
                resolve(true);
                capp.unmount(); //注销
                contentBox!.removeChild(container); //点击后清除弹窗
            },
            close: () => {
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
            cancel: () => {
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
        };
        const capp = createApp(OpenAppDialog, props);
        const container = document.createElement("div");
        capp.use(ElementPlus);
        capp.directive('dialogDrag', dialogDrag);
        capp.mount(container);
        contentBox!.insertBefore(container, contentBox!.firstChild); //插入到body最前面，层级更高
    });
};
