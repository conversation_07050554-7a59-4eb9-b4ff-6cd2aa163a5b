import { request } from "@/util/service";

export function getUserPermissionApi() {
    return request({
        url: "/api/Permissions/GetUserFunctionState",
        method: "post",
    });
}
export function getUserInfoApi() {
    return request({
        url: "/api/User/UserInfo",
        method: "post",
    });
}
export interface IUserInfo {
    mobile: string;
    nickName: string;
    userId: string;
    userName: string;
    userSn: number;
}
