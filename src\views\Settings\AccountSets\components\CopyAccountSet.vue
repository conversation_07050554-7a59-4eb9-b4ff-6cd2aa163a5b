<template>
    <div class="slot-content">
        <div class="slot-title">复制账套</div>
        <div class="slot-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #main>
                    <div class="slot-content align-center">
                        <div class="create-content slot-content-mini">
                            <div class="error-msg" id="errorMsg">
                                <span v-show="errorMsgShow">{{ errorMsg }}</span>
                            </div>
                            <div class="line-item1" style="margin-top: 0">
                                <div class="line-item-left">
                                    <div class="line-item-field">
                                        <el-autocomplete
                                            ref="asNameRef"
                                            v-model="copyData.asName"
                                            :prop="[{ required: true, message: '亲，单位名称不能为空', trigger: ['blur', 'change'] }]"
                                            class="inline-input w-50"
                                            placeholder="请输入完整的单位名称"
                                            style="width: 238px"
                                            :fetch-suggestions="querySearch"
                                            :trigger-on-focus="false"
                                            @blur="asNameBlur"
                                            @select="selectName"
                                        />
                                    </div>
                                    <div class="line-item-title"><span class="highlight-red">*</span>单位名称：</div>
                                </div>
                                <div class="line-item-right">
                                    <div class="line-item-title">
                                        <img
                                            class="asStartYearTip"
                                            src="@/assets/Settings/warn.png"
                                            style="margin-right: 4px; vertical-align: top; margin-top: 10px; width: 12px"
                                            @click="showStartYearTip"
                                        /><span class="highlight-red">*</span>账套启用年月：
                                    </div>
                                    <div class="line-item-field">
                                        <div class="jqtransform">
                                            <el-select
                                                v-model="yearNumber"
                                                style="width: 78px; padding: 0"
                                                :teleported="false"
                                                :fit-input-width="true"
                                                :disabled="ddlAsStartDate"
                                            >
                                                <el-option v-for="item in yearList" :key="item" :value="item" :label="item"></el-option>
                                            </el-select>
                                            <span style="margin: 0 10px">年</span>
                                            <el-select
                                                v-model="monthNumber"
                                                style="width: 60px; padding: 0"
                                                :teleported="false"
                                                :fit-input-width="true"
                                                :disabled="ddlAsStartDate"
                                            >
                                                <el-option v-for="item in monthList" :key="item" :value="item" :label="item"></el-option>
                                            </el-select>
                                            <span style="margin: 0 10px">月</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="line-item1">
                                <div class="line-item-left">
                                    <div class="line-item-field">
                                        <el-input v-model="copyData.unifiedNumber" style="width: 238px"></el-input>
                                    </div>
                                    <div class="line-item-title">统一社会信用代码：</div>
                                </div>
                                <div class="line-item-right">
                                    <div class="line-item-title">
                                        <img
                                            class="accountingStandardTip"
                                            src="@/assets/Settings/warn.png"
                                            style="margin-right: 4px; vertical-align: top; margin-top: 10px; width: 12px"
                                            @click="showAccountingStandardTip"
                                        /><span class="highlight-red">*</span>会计准则：
                                    </div>
                                    <div class="line-item-field">
                                        <div class="jqtransform">
                                            <el-select
                                                v-model="copyData.accountingStandard"
                                                style="width: 238px"
                                                :teleported="false"
                                                :fit-input-width="true"
                                                :disabled="true"
                                            >
                                                <el-option label="小企业会计准则" :value="1" />
                                                <el-option label="企业会计准则" :value="2" />
                                                <el-option label="民间非营利组织会计制度" :value="3" />
                                                <el-option label="农民专业合作社财务会计制度" :value="4" />
                                                <el-option label="农民专业合作社财务会计制度（2023年）" :value="5" />
                                                <el-option label="工会会计制度" :value="6" />
                                                <el-option label="农村集体经济组织会计制度" :value="7" />
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="line-item1">
                                <div class="line-item-left">
                                    <div class="line-item-field">
                                        <div class="jqtransform">
                                            <el-select
                                                v-model="copyData.asIndustry"
                                                style="width: 238px"
                                                :teleported="false"
                                                :fit-input-width="true"
                                            >
                                                <el-option label="IT·通信·电子·互联网" :value="1010" />
                                                <el-option label="金融业" :value="1020" />
                                                <el-option label="房地产·建筑业" :value="1030" />
                                                <el-option label="商业服务" :value="1040" />
                                                <el-option label="贸易·批发·零售·租赁业" :value="1050" />
                                                <el-option label="文体教育·工艺美术" :value="1060" />
                                                <el-option label="生产·加工·制造" :value="1070" />
                                                <el-option label="交通·运输·物流·仓储" :value="1080" />
                                                <el-option label="服务业" :value="1090" />
                                                <el-option label="文化·传媒·娱乐·体育" :value="1100" />
                                                <el-option label="能源·矿产·环保" :value="1110" />
                                                <el-option label="政府·非盈利机构" :value="1120" />
                                                <el-option label="农·林·牧·渔·其他" :value="1130" />
                                                <el-option label="请选择" :value="0" />
                                            </el-select>
                                        </div>
                                    </div>
                                    <div class="line-item-title">行业：</div>
                                </div>
                                <div class="line-item-right" style="margin: 8px 0 -8px; line-height: 20px">
                                    <div class="line-item-title"><span class="highlight-red">*</span>资产模块：</div>
                                    <div class="line-item-field" style="margin-top: -5px">
                                        <el-radio-group v-model="copyData.fixedasset" :disabled="rdEnableFA">
                                            <el-radio :label="0" size="large">不启用</el-radio>
                                            <el-radio :label="1" size="large" style="margin-left: 58px"
                                                >启用
                                                {{ copyData.fixedasset ? copyData.periodDisplayText : "" }}
                                            </el-radio>
                                        </el-radio-group>
                                    </div>
                                </div>
                            </div>
                            <div class="line-item1">
                                <div class="line-item-left" style="margin: 7px 0 -7px; line-height: 20px">
                                    <div class="line-item-field" style="margin-top: -5px">
                                        <el-radio-group v-model="copyData.taxType">
                                            <el-radio :label="1" size="large">小规模纳税人</el-radio>
                                            <el-radio :label="2" size="large" style="margin-left: -4px">一般纳税人</el-radio>
                                        </el-radio-group>
                                    </div>
                                    <div class="line-item-title"><span class="highlight-red">*</span>增值税种类：</div>
                                </div>
                                <div class="line-item-right" style="margin: 7px 0 -7px; line-height: 20px">
                                    <div class="line-item-title"><span class="highlight-red">*</span>资金模块：</div>
                                    <div class="line-item-field" style="margin-top: -5px">
                                        <el-radio-group v-model="copyData.cashJournal">
                                            <el-radio :label="0" size="large">不启用</el-radio>
                                            <el-radio :label="1" size="large" style="margin-left: 58px">启用</el-radio>
                                        </el-radio-group>
                                    </div>
                                </div>
                            </div>
                            <div class="line-item2">
                                <div class="line-item-left">
                                    <div class="line-item-field" style="margin-top: -5px">
                                        <el-radio-group v-model="copyData.checkNeeded">
                                            <el-radio :label="1" size="large">审核</el-radio>
                                            <el-radio :label="0" size="large" style="margin-left: 58px">不审核</el-radio>
                                        </el-radio-group>
                                    </div>
                                    <div class="line-item-title"><span class="highlight-red">*</span>凭证审核：</div>
                                </div>
                                <div class="line-item-right divShowScm dingtalk-hidden" v-if="copyData.accountingStandard !== 6 && copyData.accountingStandard !== 7">
                                    <div class="line-item-title"><span class="highlight-red">*</span>关联进销存：</div>
                                    <div class="line-item-field" style="margin-top: -5px">
                                        <el-radio-group v-model="copyData.showScm">
                                            <el-radio :label="0" size="large">不启用</el-radio>
                                            <el-radio :label="1" size="large" style="margin-left: 58px">启用</el-radio>
                                        </el-radio-group>
                                    </div>
                                </div>
                            </div>
                            <div id="trCopyFiles" class="select-copy-content" style="display: block">
                                <a class="link" id="selectCopyContent" @click="toSelectCopy">复制内容选择 &gt;</a>
                            </div>
                            <div class="buttons" style="display: flex; justify-content: center">
                                <a class="button solid-button" id="saveAS" @click="excuteCopy">复制账套</a
                                ><a class="button" id="cancel" tabindex="20" @click="CancelCopyAccountSet">取消</a>
                            </div>
                            <div class="tips">
                                <div class="tips-title">温馨提示：</div>
                                <div class="tips-content">
                                    <div>1、如需复制账套设置如科目、期初等设置，可点击"复制内容选择"进行设置。</div>
                                    <div>2、红色星号表示必填项，其他为非必填项。</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #select>
                    <div class="slot-content align-center">
                        <div class="select-content-content slot-content-mini">
                            <div class="select-copy-content-container">
                                <div id="selectCopyContentTitle" class="select-copy-content-title">
                                    当前账套：{{ asName.slice(0, asName.length - 3) }}
                                </div>
                                <div
                                    class="select-copy-content-item-card"
                                    :class="{ selected: copyParams.copyContent === 1 }"
                                    @click="copyParams.copyContent = 1"
                                >
                                    <div class="card-circle"></div>
                                    <div class="card-content">
                                        <div class="card-title">
                                            <span class="txt">复制整个账套</span>
                                        </div>
                                        <div class="card-desc">
                                            <span class="txt">复制后的账套，所有数据将和当前账套完全一致。</span>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="select-copy-content-item-card"
                                    :class="{ selected: copyParams.copyContent === 2 }"
                                    @click="copyParams.copyContent = 2"
                                >
                                    <div class="card-circle"></div>
                                    <div class="card-content">
                                        <div class="card-title">
                                            <span class="txt">复制账套设置（包括科目设置、辅助核算、外币设置）</span>
                                        </div>
                                        <div class="card-desc">
                                            <img src="@/assets/Settings/dui.png" class="icon" />
                                            <span class="txt">复制科目</span>
                                            <img src="@/assets/Settings/cuo.png" class="icon" />
                                            <span class="txt">不复制凭证及其他</span>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="select-copy-content-item-card"
                                    :class="{ selected: copyParams.copyContent === 3 }"
                                    @click="copyParams.copyContent = 3"
                                >
                                    <div class="card-circle"></div>
                                    <div class="card-content">
                                        <div class="card-title">
                                            <span class="txt">复制账套设置以及</span>
                                            <div id="copyContentPeriod" class="jqtransform jqtransformdone">
                                                <el-select
                                                    v-model="copyParams.periodYear"
                                                    class="select-year"
                                                    placeholder="年"
                                                    size="small"
                                                >
                                                    <el-option
                                                        v-for="val in yearPeriod"
                                                        :key="val"
                                                        :value="val"
                                                        :label="val + '年'"
                                                    ></el-option>
                                                </el-select>
                                                <el-select
                                                    v-model="copyParams.periodMonth"
                                                    class="select-month"
                                                    placeholder="月"
                                                    size="small"
                                                >
                                                    <el-option
                                                        v-for="item in periods[copyParams.periodYear]"
                                                        :key="item.pid"
                                                        :value="item.sn"
                                                        :label="item.sn + '月'"
                                                    ></el-option>
                                                </el-select>
                                            </div>
                                            <span class="txt">的期初数据</span>
                                        </div>
                                        <div class="card-desc">
                                            <img src="@/assets/Settings/dui.png" class="icon" />
                                            <span class="txt">复制科目</span>
                                            <img src="@/assets/Settings/dui.png" class="icon" />
                                            <span class="txt">复制所选期间的期初</span>
                                            <img src="@/assets/Settings/cuo.png" class="icon" />
                                            <span class="txt">不复制凭证及其他</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="copy-files">
                                    <el-checkbox v-model="copyParams.checkCopyFiles" label="复制会计电子档案文件"></el-checkbox>
                                </div>
                                <div class="buttons">
                                    <a id="selectCopyContentConfirm" class="button solid-button" @click="confirmSelectCopy(true)">确定</a
                                    ><a id="selectCopyContentCancel" class="button ml-10" @click="confirmSelectCopy(false)">取消</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </ContentSlider>
        </div>
        <el-dialog v-model="accountingStandardTipVisible" title="会计准则说明" center width="440px" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div class="box-body" style="padding-top: 30px; padding-bottom: 30px">
                    <div class="body-message">
                        <div class="body-message-title" style="text-align: left">亲，请根据实际情况选择对应的会计准则。</div>
                        <div class="body-message-content">
                            {{
                                isProSystem
                                    ? "① 小企业会计准则是按照2013年版 《小企业会计准则》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。"
                                    : "① 小企业会计准则是按照2013年版 《小企业会计准则》设置，为了使报表取数准确，系统不允许增加一级科目，但可以增加二级、三级和四级科目。"
                            }}
                            <br />② 企业会计准则是按照2007年版 《企业会计准则》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />③
                            民间非营利组织会计制度是按照2005年版《民间非营利组织会计制度》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />④
                            农民专业合作社财务会计制度是按照2023年版《农民专业合作社会计制度》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />
                            {{
                                isAccountingAgent
                                    ? ""
                                    : "⑤ 需要使用《企业会计准则(2019年未执行新金融准则、新收入准则和新租赁准则)》或《企业会计准则(2019年已执行新金融准则、新收入准则和新租赁准则)》时，可以先选择《企业会计准则》新建账套，新建后到设置—账套—编辑页面去切换准则"
                            }}
                        </div>
                    </div>
                </div>
                <div class="box-footer button-ok" style="text-align: center; padding: 10px 0">
                    <a class="button solid-button" @click="() => (accountingStandardTipVisible = false)">确定</a>
                </div>
            </div>
        </el-dialog>
        <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow"  :proOverFlowText="proOverFlowText"/>
    </div>
</template>

<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import type { ICopyAccountSetResult, ICopyParams, IperiodList, Iperiod } from "../types";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { ref, watch, onMounted, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { reloadAccountSetList } from "@/util/accountset";
import { useLoading } from "@/hooks/useLoading";
import { getCompanyList } from "@/util/getCompanyList";
import type { ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyDetailApi } from "@/api/getCompanyList";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import type { IAccountSetInfo } from "@/api/accountSet";
import { getServiceId } from "@/util/proUtils";

const props = defineProps({
    copyAsId: {
        type: Number,
        default: 0,
    },
});
const errorMsgShow = ref(false);
const errorMsg = ref("");
const currentSlot = ref("main");
const slots = ["main", "select"];
const isProSystem = ref(window.isProSystem);
const isAccountingAgent = ref(window.isAccountingAgent);
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const asName = ref("");
const copyParams = ref<ICopyParams>({ copyContent: 1, periodYear: 2023, periodMonth: 1, checkCopyFiles: true });
const resParams = ref<ICopyParams>({ copyContent: 1, periodYear: 2023, periodMonth: 1, checkCopyFiles: true });
const accountingStandardTipVisible = ref(false);

const asNameRef = ref();
const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};

const emits = defineEmits(["selectCopyHandle", "cancelCopy", "update:copyData"]);
const copyData = ref({
    asId: *********,
    asName: "",
    asIndustry: 1030,
    taxType: 1,
    unifiedNumber: "911101056766374079",
    taxNumberS: "911101056766374079",
    accountingStandard: 1,
    subAccountingStandard: 0,
    checkNeeded: 0,
    fixedasset: 1,
    faStartPeroid: 4,
    faStartDate: null,
    faEndPeriod: null,
    faEndDate: null,
    cashJournal: 1,
    showScm: 1,
    asStartDate: "2023-04-01T00:00:00",
    adLevel0: null,
    adLevel1: null,
    adLevel2: null,
    adLevel3: null,
    asubLength: "4322",
    taxpayerName: " ",
    taxpayerPassword: "",
    taxadId: 0,
    createdBy: 290702,
    createdDate: "2023-04-21T15:27:10",
    modifiedBy: 290702,
    modifiedDate: "2023-05-25T22:21:10",
    usedBy: 290702,
    usedDate: "2023-05-26T09:47:05",
    deleteBy: null,
    deleteDate: null,
    destroyBy: null,
    destroyDate: null,
    isColdas: false,
    lockState: null,
    lockPassword: null,
    decimalPlace: null,
    periodDisplayText: "(2023-04)",
    isEditSartDate: true,
    usedSpace: 0,
    taxDeclareEmployeeName: "",
    taxDeclareEmployeePhone: "",
    taxDeclarePassword: "",
    isExpired: true,
});
const ddlAsStartDate = ref(true);
const rdEnableFA = ref(true);
const yearNumber = ref(new Date(copyData.value.asStartDate).getFullYear());
const monthNumber = ref(new Date(copyData.value.asStartDate).getMonth() + 1);
watchEffect(() => {
    yearNumber.value = new Date(copyData.value.asStartDate).getFullYear();
    monthNumber.value = new Date(copyData.value.asStartDate).getMonth() + 1;
});
const periods = ref<IperiodList>({});
const yearPeriod = ref([]);
const yearList = ref<number[]>([]);
const monthList = ref<number[]>([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
function getASYearList() {
    let year = new Date().getFullYear();
    for (let i = 2000; i < year + 6; i++) {
        yearList.value.push(i);
    }
}
function getYearList() {
    request({
        url: `/api/Period/ListWithStatusByAsid?asId=${props.copyAsId}`,
    }).then((res: IResponseModel<Iperiod[]>) => {
        periods.value = {};
        yearPeriod.value = res.data.reduce((prev: any, item: any) => {
            if (prev.indexOf(item.year) === -1) {
                prev.push(item.year);
            }
            return prev;
        }, []);
        res.data.forEach((item: Iperiod) => {
            if (periods.value[item.year]) {
                periods.value[item.year].unshift(item);
            } else {
                periods.value[item.year] = [item];
            }
        });
        for (let i in periods.value) {
            periods.value[i].sort((a: any, b: any) => {
                return a.pid - b.pid;
            });
        }
        copyParams.value.periodYear = res.data.find((item: any) => item.isActive)?.year as number;
        // copyParams.value.periodMonth = res.data.find((item: any) => item.isActive)?.sn as number;
        copyParams.value.periodMonth = periods.value[copyParams.value.periodYear][0]?.sn as number;
    });
}

function asNameBlur() {
    asNameRef.value.activated = false;
    if (copyData.value.asName.length == 0) {
        errorMsgShow.value = true;
        errorMsg.value = "亲，单位名称不能为空！";
        asNameRef.value.focus();
        return;
    }
    if (copyData.value.asName.length < 5) {
        errorMsgShow.value = true;
        errorMsg.value = "亲，请输入完整的单位名称哦！";
        return;
    }
    if (copyData.value.asName.length > 40) {
        errorMsgShow.value = true;
        errorMsg.value = "亲，单位名称最多40个字哦！";
        asNameRef.value.focus();
        return;
    }
    errorMsgShow.value = false;
    errorMsg.value = "";
}

function selectName(item: any) {
    copyData.value.asName = item.value;
    copyData.value.unifiedNumber = item.creditCode;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}

function showStartYearTip() {
    ElConfirm("有凭证、资产、资金、工资数据或存在结账期间，无法再修改账套启用年月。", true, () => {}, "账套启用年月说明");
}
function showAccountingStandardTip() {
    accountingStandardTipVisible.value = true;
}
function selectCopyHandle() {
    emits("selectCopyHandle", copyData.value.asName.replace("-副本", ""));
}
function CancelCopyAccountSet() {
    emits("cancelCopy");
}

function excuteCopy() {
    if (copyData.value.asName === "") {
        ElNotify({
            type: "warning",
            message: "亲，单位名称不能为空！",
        });
        return;
    }
    if (copyData.value.asName.match('[\\\\/*?:<>|"]')) {
        ElNotify({
            type: "warning",
            message: '亲，单位名称不能包含\\/*?:<>|"等特殊符号！',
        });
        return;
    }
    if (copyData.value.asName.length < 5) {
        ElNotify({
            type: "warning",
            message: "亲，请输入完整的单位名称哦！",
        });
        return;
    }
    if (copyData.value.asName.length > 40) {
        ElNotify({
            type: "warning",
            message: "亲，单位名称最多40个字哦！",
        });
        return;
    }
    useLoading().enterLoading("复制账套中，请稍候...");
    const data = {
        asId: copyData.value.asId,
        asName: copyData.value.asName,
        asIndustry: copyData.value.asIndustry,
        taxType: copyData.value.taxType,
        unifiedNumber: copyData.value.unifiedNumber,
        checkNeeded: copyData.value.checkNeeded ? true : false,
        showScm: copyData.value.showScm,
        copyFiles: resParams.value.checkCopyFiles ? 1 : 0,
        copyContent: resParams.value.copyContent,
        initPid: periods.value[copyParams.value.periodYear].find((item: any) => item.sn === copyParams.value.periodMonth)?.pid,
        asStartYear: yearNumber.value,
        asStartMonth: monthNumber.value,
        hasFixedAsset: copyData.value.fixedasset ? true : false,
        decimalPlace: copyData.value.decimalPlace,
    };
    let copyUrl = `/api/AccountSetOnlyAuth/CopyAccountSetV2?serviceid=${getServiceId()}`;


    request({
        url: copyUrl,
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    })
        .then((res: IResponseModel<ICopyAccountSetResult>) => {
            useLoading().quitLoading();
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "亲，复制成功了！",
                });
                thirdPartNotify(thirtPartNotifyTypeEnum.accountSetCopyAccountSet, { asId: res.data.newAsid }).then(() => {});
                reloadAccountSetList();
                CancelCopyAccountSet();
            } else if (res.state === 2000 && res.subState === 8) {
               if(res.msg === '您的账套数量已超过已购买账套数量。'){
                    proOverFlowText.value = "您的账套数量已超过已购买账套数，建议您去增购~";
                    proOverFlowShow.value = true;
               }else{
                 ElConfirm(res.msg, true, () => {}, "提示", { confirmButtonText: "知道了", cancelButtonText: "" });
               }
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((err: any) => {
            useLoading().quitLoading();
            if (err.response?.status === 400) {
                proOverFlowText.value = err.response.data;
                thirdPartNotify(thirtPartNotifyTypeEnum.accountSetAccountSetOverflow).then(() => {
                    proOverFlowShow.value = true;
                });
            }
        });
}

const raFixedAsset = ref(0);
function getTableData() {
    request({
        url: `/api/AccountSetOnlyAuth/Info?asId=${props.copyAsId}`,
    }).then((res: IResponseModel<any>) => {
        copyData.value = res.data;
        raFixedAsset.value = res.data.fixedasset;
        copyData.value.asName = res.data.asName + "-副本";
        asName.value = res.data.asName; //记录一下最初的账套名
        copyParams.value.copyContent = 1;
        (copyParams.value.checkCopyFiles = true),
            (resParams.value = { copyContent: 1, periodYear: 2023, periodMonth: 1, checkCopyFiles: true });
    });
}

function toSelectCopy() {
    currentSlot.value = "select";
}

function confirmSelectCopy(val: boolean) {
    currentSlot.value = "main";
    if (val) {
        resParams.value.checkCopyFiles = copyParams.value.checkCopyFiles;
        resParams.value.copyContent = copyParams.value.copyContent;
        resParams.value.periodYear = copyParams.value.periodYear;
        resParams.value.periodMonth = copyParams.value.periodMonth;
    } else {
        copyParams.value = {
            copyContent: 1,
            periodYear: new Date(copyData.value.asStartDate).getFullYear(),
            periodMonth: new Date(copyData.value.asStartDate).getMonth() + 1,
            checkCopyFiles: true,
        };
        getYearList();
    }
}

watch(
    props,
    () => {
        if (props.copyAsId > 0) {
            getASYearList();
            getTableData();
            getYearList();
        }
    },
    { immediate: true }
);

watch(
    () => copyParams.value.copyContent,
    (newValue) => {
        switch (newValue) {
            case 2:
            case 3:
                ddlAsStartDate.value = false;
                rdEnableFA.value = false;
                break;
            default:
                yearNumber.value = new Date(copyData.value.asStartDate).getFullYear();
                monthNumber.value = new Date(copyData.value.asStartDate).getMonth() + 1;
                ddlAsStartDate.value = true;
                rdEnableFA.value = true;
                copyData.value.fixedasset = raFixedAsset.value;
                break;
        }
    }
);
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/Settings/CopyAccountSet.less";
</style>
