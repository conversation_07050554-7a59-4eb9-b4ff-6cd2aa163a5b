<template>
    <!-- class="slot-content align-item" -->
    <div style="height: 100%;">
        <div class="recyclebin-content">
            <div class="main-top" style="justify-content: space-between;">
                <div>
                    <a class="button float-l" @click="back2AccountSet">返回</a>
                    <a class="button large-1 float-l ml-10" @click="clearRecycleBin">清空回收站</a>
                </div>
                <span class="tip">注意：回收站的账套会在用户删除90日后自动清除哦~</span>
                <div class="input-group">
                    <el-input type="text" v-model="searchInfo" autocomplete="off" @keyup.enter="getRecycleData" />
                    <div class="icon" @click="getRecycleData"></div>
                </div>
            </div>
            <div class="main-center">
                <Table 
                    :data="recycleData" 
                    :columns="recycleColumns" 
                    :loading="loading" 
                    :scrollbarShow="true"
                    :tableName="setModule"
                >
                    <template #operator>
                        <el-table-column label="操作" align="left" header-align="left" width="176" :resizable="false">
                            <template #default="scope">
                                <a
                                    v-if="scope.row.permissionFunctionCode.includes('accountset-canedit')"
                                    title="还原账套"
                                    class="link"
                                    @click="restore(scope.row.asId, scope.row.asName)"
                                    >还原账套</a
                                >
                                <a
                                    v-if="scope.row.permissionFunctionCode.includes('accountset-candelete')"
                                    title="彻底删除"
                                    class="link"
                                    @click="destory(scope.row.asId, scope.row.asName)"
                                    >彻底删除</a
                                >
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
        <el-dialog v-model="emptyDialog" title="清空回收站" width="440px" class="dialogDrag">
            <div class="body-message" v-dialogDrag>
                <div class="body-message-title">
                    确定要永久删除全部账套吗？<br /><span class="highlight-red">注意：彻底删除后，账套数据将不能再恢复！</span>
                </div>
                <div class="body-message-content content-none"></div>
            </div>
            <div class="buttons">
                <a class="button solid-button mr-10" @click="emptyHandle">确定</a
                ><a class="button" @click="() => (emptyDialog = false)">取消</a>
            </div>
        </el-dialog>
        <el-dialog v-model="deleteDialog" title="彻底删除" width="440px" class="dialogDrag">
            <div class="body-message" v-dialogDrag>
                <div class="body-message-title">
                    您将彻底删除账套：{{ deleteAsname }}<br /><span class="highlight-red">注意：永久删除后，账套数据将不能再恢复！</span>
                </div>
                <div class="body-message-content content-none"></div>
            </div>
            <div class="buttons">
                <a class="button solid-button mr-10" @click="absoluteDelete">确定</a
                ><a class="button" @click="() => (deleteDialog = false)">取消</a>
            </div>
        </el-dialog>
        <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
    </div>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { GetAccountStandardText } from "../utils";
import { AppConfirmDialog } from "@/util/appConfirm";
import { reloadAccountSetList } from "@/util/accountset";
import { ref } from "vue";
import { request } from "@/util/service";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import { useLoading } from "@/hooks/useLoading";
import { getServiceId } from "@/util/proUtils";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AccountSetRecycle";

const emits = defineEmits(["backMain", "getAccountSet"]);
const searchInfo = ref("");
const emptyDialog = ref(false);
const deleteDialog = ref(false);
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const deleteAsname = ref("");
const deleteAsid = ref(0);
const recycleColumns = ref<Array<IColumnProps>>([
    {
        label: "单位名称",
        prop: "asName",
        align: "left",
        headerAlign: "left",
        minWidth: 230,
        width: getColumnWidth(setModule, "asName"),
    },
    {
        label: "当前记账年月",
        prop: "asCurrentYear",
        align: "left",
        headerAlign: "left",
        minWidth: 93,
        formatter: function (row) {
            return row.asCurrentYear + "年" + (Number(row.asCurrentMonth) < 10 ? "0" + row.asCurrentMonth : row.asCurrentMonth) + "月";
        },
        width: getColumnWidth(setModule, "asCurrentYear"),
    },
    {
        label: "账套启用年月",
        prop: "asStartDate",
        align: "left",
        headerAlign: "left",
        minWidth: 93,
        formatter: function (row) {
            return row.asStartYear + "年" + (Number(row.asStartMonth) < 10 ? "0" + row.asStartMonth : row.asStartMonth) + "月";
        },
        width: getColumnWidth(setModule, "asStartDate"),
    },
    {
        label: "会计准则",
        prop: "accountingStandard",
        align: "left",
        headerAlign: "left",
        minWidth: 143,
        formatter: function (row) {
            return GetAccountStandardText(row.accountingStandard, row.subAccountingStandard);
        },
        width: getColumnWidth(setModule, "accountingStandard"),
    },
    {
        label: "操作人",
        prop: "asDeleteBy",
        align: "left",
        headerAlign: "left",
        minWidth: 144,
        width: getColumnWidth(setModule, "asDeleteBy"),
    },
    {
        label: "删除日期",
        prop: "asDeleteDate",
        align: "left",
        headerAlign: "left",
        minWidth: 77,
        formatter: function (row) {
            return (
                row.asDeleteYear + "-" + (row.asDeleteMonth < 10 ? "0" + row.asDeleteMonth : row.asDeleteMonth) + "-" + row.asDeleteDay + ""
            );
        },
        width: getColumnWidth(setModule, "asDeleteDate"),
    },
    { slot: "operator" },
]);

const recycleData = ref([]);
const loading = ref(false);
function getRecycleData() {
    loading.value = true;
    let url = `/api/AccountSetRecycleBin/List?searchInfo=${searchInfo.value}`;
    if (window.isProSystem) {
        url = window.eHost + "/wb/valveacc_vip_web" + url + "&serviceID=" + getServiceId();
    }
    request({
        url: url,
    }).then((res: any) => {
        loading.value = false;
        recycleData.value = res.data;
    })
}

function back2AccountSet() {
    searchInfo.value = "";
    recycleData.value = [];
    emits("backMain");
}

function clearRec() {
    useLoading().enterLoading("努力加载中，请稍候...");
    request({
        url: "/api/AccountSetRecycleBin/Clear",
        method: "post",
    }).then((res: any) => {
        useLoading().quitLoading();
        if (res.state === 1000) {
            getRecycleData();
            if (!window.isProSystem) {
                ElConfirm(
                    `亲，不是账套管理员身份的账套将被跳过！<br>成功：${res.data.successCount}，跳过：${res.data.faildCount}`,
                    true,
                    () => {},
                    "提示"
                );
            }
        } else {
            if (res.msg) {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "亲，删除失败啦，请联系侧边栏客服！",
                });
            }
        }
    });
}

function clearRecycleBin() {
    ElConfirm(
        `确定要永久删除全部账套吗？<br/><span class="highlight-red">注意：彻底删除后，账套数据将不能再恢复！</span>`,
        false,
        () => {},
        "清空回收站"
    ).then((r: Boolean) => {
        if (r) {
            if (window.isProSystem) {
                clearRec();
            } else {
                AppConfirmDialog(1130).then((bool: Boolean) => {
                    if (bool) {
                        clearRec();
                    }
                });
            }
        }
    });
}

function emptyHandle() {
    request({
        url: "/api/AccountSetRecycleBin/Clear",
        method: "post",
    }).then((res: any) => {
        if (res.state && res.data) {
            ElNotify({
                type: "success",
                message: "删除成功",
            });
            getRecycleData();
        }
    });
}

function restore(asid: number, asname: string) {
    let restoreUrl = `/api/AccountSetRecycleBin/RestoreV2?asId=${asid}&serviceid=${getServiceId()}`;
    
    function restoreAccountSet() {
        useLoading().enterLoading("还原账套中，请稍候...");
        request({
            url: restoreUrl,
            method: "post",
        })
            .then((res: any) => {
                if (res.state && res.data) {
                        ElNotify({
                            type: "success",
                            message: "还原成功",
                        });
                        reloadAccountSetList();
                        getRecycleData();
                        emits("getAccountSet");
                    }
                else if (!res.data && res.subState===8){
                    if(res.msg === "您的账套数量已超过已购买账套数量，建议您去增购") {
                        proOverFlowText.value = "您的账套数量已超过已购买账套数，建议您去增购~"
                    } else {
                        proOverFlowText.value = res.msg;
                    }
                    proOverFlowShow.value = true;
                }
            })
            .catch((err: any) => {
                if (err.response?.status === 400) {
                    proOverFlowText.value = err.response.data;
                    proOverFlowShow.value = true;
                } else {
                    ElNotify({
                        type: "warning",
                        message: "切换准则失败，请稍后重试！",
                    });
                }
            })
            .finally(() => {
                useLoading().quitLoading();
            });
    }
    ElConfirm(`您将还原账套：${asname}？`, false, () => {}, "还原账套").then((r: boolean) => {
        if (r) {
            AppConfirmDialog(1120).then((bool: Boolean) => {
                if (bool) {
                    restoreAccountSet();
                }
            });
        }
    });
}

function destory(asid: number, asname: string) {
    deleteAsid.value = asid;
    deleteAsname.value = asname;
    deleteDialog.value = true;
}

function absoluteDeleteRecyclebin() {
    request({
        url: "/api/AccountSetRecycleBin/Destroy?asId=" + deleteAsid.value,
        method: "post",
    }).then((res: any) => {
        if (res.state && res.data) {
            ElNotify({
                type: "success",
                message: "删除成功",
            });
            deleteDialog.value = false;
            getRecycleData();
        }
    });
}
function absoluteDelete() {
    deleteDialog.value = false;
    AppConfirmDialog(1110).then((r: Boolean) => {
        if (r) {
            absoluteDeleteRecyclebin();
        }
    });
}

defineExpose({
    getRecycleData,
});
</script>

<style scoped lang="less">
.recyclebin-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: white;
    .main-center {
        padding: 10px 20px;
        flex: 1;
        min-height: 0;
        :deep(.table) {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        :deep(.el-table) {
            border-bottom: none;
        }
    }
    & .main-top {
        height: 32px;
        padding: 10px 20px;
        border-bottom: 1px solid var(--border-color);
        text-align: center;
        & .button {
            float: left;
            margin-top: 2px;

            &.large-1 {
                width: 94px;
                background-position-x: 78px;
            }
        }

        & .tip {
            color: var(--orange);
            font-size: var(--font-size);
            line-height: 32px;
            text-align: center;
        }

        & .input-group {
            width: 214px;
            height: 32px;
            position: relative;
            float: right;
            & input[type="text"] {
                font-size: var(--font-size);
                width: 170px;
                height: 30px;
                padding: 0;
                padding-left: 10px;
                padding-right: 32px;
                outline: none;
                border-radius: 4px;
                border: 1px solid var(--weaker-font-color);
            }

            & .icon {
                background-image: url(@/assets/Settings/sousuo.png);
                background-repeat: no-repeat;
                height: 18px;
                width: 18px;
                top: 7px;
                right: 8px;
                cursor: pointer;
                position: absolute;
                margin: 0;
            }
        }
    }
}

.body-message {
    border-bottom: 1px solid var(--border-color);
    padding: 40px 70px;
    text-align: center;
    min-height: 42px;
}

:deep(.el-table) {
    border-bottom: 1px solid var(--border-color);
}

</style>
