<template>
    <el-dialog
        class="custom-confirm dialogDrag"
        :title="'添加' + aaDialogInfo.aaName"
        center
        v-model="aaDialogInfo.display"
        width="440px"
        :destroy-on-close="true"
        @closed="resetAADialogInfo"
    >
        <div class="add-info-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="add-info-line-item required">
                <div class="add-info-title">{{ aaDialogInfo.aaName + "编码：" }}</div>
                <div class="add-info-field">
                    <input
                        type="text"
                        v-model="aaDialogInfo.code"
                        @input="handleAAInput(LimitCharacterSize.Code, $event, aaDialogInfo.aaName + '编码', 'code', changeAAInfo)"
                        @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                    />
                </div>
            </div>
            <div class="add-info-line-item required">
                <div class="add-info-title">{{ aaDialogInfo.aaName + "名称：" }}</div>
                <div class="add-info-field">
                    <input
                        type="text"
                        v-model="aaDialogInfo.name"
                        @input="handleAAInput(LimitCharacterSize.Name, $event,  aaDialogInfo.aaName + '名称', 'name', changeAAInfo)"
                        @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                    />
                </div>
            </div>
            <div class="add-info-line-item">
                <div class="add-info-title">备注：</div>
                <div class="add-info-field">
                    <input
                        type="text"
                        v-model="aaDialogInfo.note"
                        @input="handleAAInput(LimitCharacterSize.Note, $event, '备注', 'note', changeAAInfo)"
                        @paste="handleAAPaste(LimitCharacterSize.Note, $event)"
                    />
                </div>
            </div>
            <div class="add-info-buttons">
                <a class="button solid-button" @click="handleAASave">保存</a>
                <a class="button ml-10" @click="closeAADialog">取消</a>
            </div>
        </div>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import { ElNotify } from "@/util/notify";
import { getNextAaNum } from "@/views/Settings/AssistingAccounting/utils";
import { request, type IResponseModel } from "@/util/service";
import {
    ProjectSaveEmptyParams,
    DepartmentSaveEmptyParams,
    CashAAType,
    EmployeeSaveEmptyParams,
} from "@/views/Cashier/CashOrDepositJournal/types";
const emit = defineEmits(["save", "close"]);
const props = defineProps({
    autoAddName: {
        type: String,
        default: "",
    },
});
const aaDialogInfo = reactive<{
    display: boolean;
    code: string;
    name: string;
    note: string;
    aaType: number;
    aaName: string;
}>({
    display: false,
    code: "",
    name: "",
    note: "",
    aaType: 10005,
    aaName: "项目",
});
const isErp = ref(false);
function changeAAInfo(key: string, val: string) {
    if (key === "code") {
        aaDialogInfo.code = val;
    } else if (key === "name") {
        aaDialogInfo.name = val;
    } else if (key === "note") {
        aaDialogInfo.note = val;
    }
}
function handleAASave() {
    if (!aaDialogInfo.name.trim()) {
        ElNotify({ type: "warning", message: aaDialogInfo.aaName + "名称不能为空" });
        return;
    }
    if (!aaDialogInfo.code.trim()) {
        ElNotify({ type: "warning", message: aaDialogInfo.aaName + "编码不能为空" });
        return;
    }
    createCheck(aaDialogInfo.aaType, 0, aaDialogInfo.code.trim(), aaDialogInfo.name.trim(), saveAAInfo);
}
const aaTypeCollection: { [key: number]: any } = {
    [CashAAType.Department]: {
        aaName: "部门",
        url: "Department",
    },
    [CashAAType.Project]: {
        aaName: "项目",
        url: "Project",
    },
    [CashAAType.Employee]: {
        aaName: "职员",
        url: "Employee",
    },
};
function saveAAInfo() {
    const emptyParams = {
        code: aaDialogInfo.code.trim(),
        name: aaDialogInfo.name.trim(),
        note: aaDialogInfo.note,
    };
    const params = transformParams(emptyParams, aaDialogInfo.aaType);

    request({
        url: `/api/AssistingAccounting/${aaTypeCollection[aaDialogInfo.aaType].url}`,
        method: "post",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(params),
    }).then((res: any) => {
        if (res.state == 1000) {
            aaDialogInfo.display = false;
            emit("save", aaDialogInfo.aaType, emptyParams.code, emptyParams.name);
            resetAADialogInfo();
        }
    });
}
function transformParams(params: { code: string; name: string; note: string }, aaType: number) {
    switch (aaType) {
        case CashAAType.Department:
            return new DepartmentSaveEmptyParams(params);
        case CashAAType.Project:
            return new ProjectSaveEmptyParams(params);
        case CashAAType.Employee:
            return new EmployeeSaveEmptyParams(params);
        default:
            return params;
    }
}
function resetAADialogInfo() {
    aaDialogInfo.code = "";
    aaDialogInfo.name = "";
    aaDialogInfo.note = "";
    aaDialogInfo.aaType = CashAAType.Project;
    aaDialogInfo.aaName = "项目";
}
function closeAADialog() {
    aaDialogInfo.display = false;
    emit("close");
}

async function showAADialog(aatype: CashAAType.Department | CashAAType.Project | CashAAType.Employee) {
    aaDialogInfo.display = true;
    aaDialogInfo.aaType = aatype;
    aaDialogInfo.aaName = aaTypeCollection[aatype].aaName;
    aaDialogInfo.name = props.autoAddName.slice(0, LimitCharacterSize.Name);
    if (props.autoAddName.length > LimitCharacterSize.Name) {
        ElNotify({ type: "warning", message: `亲，${aaDialogInfo.aaName}名称不能超过${LimitCharacterSize.Name}个字符!` });
    }
    await getNextAaNum(aatype).then((res: IResponseModel<string>) => {
        res.state === 1000 && (aaDialogInfo.code = res.data);
    });
    return;
}

defineExpose({ showAADialog });
</script>
<style lang="less" scoped>
@import "@/style/Functions.less";
.add-info-content {
    padding: 20px 0;
    text-align: center;
    .add-info-line-item {
        margin-right: 40px;
        &.required {
            .add-info-title:before {
                content: "*";
                color: var(--red);
            }
        }
        & + .add-info-line-item {
            margin-top: 16px;
        }
        .add-info-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            display: inline-block;
            vertical-align: middle;
            width: 120px;
            text-align: right;
        }
        .add-info-field {
            display: inline-block;
            vertical-align: middle;
            text-align: left;
            .detail-el-select(200px, 32px);
            & > input {
                .detail-original-input(200px, 32px);
            }
        }
        .filed-textarea {
            width: 198px;
            :deep(.el-textarea) {
                .detail-el-textarea-scroll-thumb();
            }
        }
    }
    .add-info-buttons {
        margin-top: 24px;
    }

    &.erp {
        padding-bottom: 0px;
        .add-info-buttons {
            height: 30px;
            display: block;
            box-sizing: content-box;
            border-top: 1px solid var(--border-color);
            text-align: right;
            padding: 16px 20px 16px 0;
            & a {
                text-align: center;
                display: block;
                float: right;
                width: 70px;
                min-width: 70px;
                height: 30px;
                border-radius: 2px;
                margin-left: 20px;
                line-height: 30px;
            }
            & button {
                box-sizing: border-box;
                padding: 0 12px;
                font-size: var(--font-size);
            }
        }
    }
}
</style>