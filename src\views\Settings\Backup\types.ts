export interface ITableData {
    createBy: number;
    creator: string;
    dateTime: string;
    expiredDate: any;
    fileName: string;
    fileSize: number;
    fileType: number;
    opertion: number;
    progress: number;
    fileSource?: any;
}
export interface IAuditSoftWare {
    text: string;
    value: number;
}
export interface ISearchPeriod {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
}

export interface IProcessRestoreResult {
    asid: string;
    createdTime: string;
}

export interface ICheckSpace {
    overflow: boolean;
    state: {
        totalSpace: number;
        lastTotalSpace: number;
        usedSpace: number;
        backUpUsedSpace: number;
        lemonDiskUsedSpace: number;
        endTime: string;
        startTime: string;
        spareDays: number;
        payYears: number;
        endTimeStr: string;
        endTimeStr2: string;
        state: number;
    };
}

export interface IProcessBack {
    fileName: string;
    fileType: number;
    dateTime: string;
    fileSize: number;
    creator: string;
    opertion: number;
    progress: number;
    expiredDate: string;
    createBy: number;
}
