import { VoucherSource } from "@/views/Voucher/components/VoucherListItem/types";
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage, getUrlSearchParams } from "@/util/url";
import dayjs from "dayjs";
import { formatDate } from "@/util/date";
import type { IVoucherModel, IVoucherSource } from "./types";

export const getDaysInMonth = (year: number, month: number): number => {
    // 判断是否为闰年
    const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
    // 计算每个月的天数
    const daysInMonth = [
        31, // January
        isLeapYear ? 29 : 28, // February
        31, // March
        30, // April
        31, // May
        30, // June
        31, // July
        31, // August
        30, // September
        31, // October
        30, // November
        31, // December
    ];
    return daysInMonth[month - 1];
};

export const formSubmit = (url: string, method: string, params: any) => {
    const form = document.createElement("form");
    form.method = method;
    form.action = url;
    for (const key in params) {
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = key;
        input.value = params[key];
        form.appendChild(input);
    }
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
};

export function getDatesMax(dates: string[]): string {
    let maxDate: Date | null = null;

    for (const dateStr of dates) {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
            if (maxDate === null || date > maxDate) {
                maxDate = date;
            }
        }
    }

    if (maxDate !== null) {
        const year = maxDate.getFullYear();
        const month = (maxDate.getMonth() + 1 + "").padStart(2, "0");
        const day = getDaysInMonth(year, maxDate.getMonth() + 1);
        return `${year}-${month}-${day}`;
    }

    return "";
}

// 输入[1, 2, 3, 5, 6, 8]  输出["1-3", "5-6", "8"]
export function formatNumberRange(arr: number[]): string[] {
    const result: string[] = [];

    let start = arr[0];
    let end = arr[0];

    for (let i = 1; i < arr.length; i++) {
        if (arr[i] === end + 1) {
            end = arr[i];
        } else {
            if (start === end) {
                result.push(start.toString());
            } else {
                result.push(`${start}-${end}`);
            }
            start = arr[i];
            end = arr[i];
        }
    }

    if (start === end) {
        result.push(start.toString());
    } else {
        result.push(`${start}-${end}`);
    }

    return result;
}

const moduleOrderMap = {
    [VoucherSource.Cashier]: window.isErp ? "资金管理" : "资金",
    [VoucherSource.Draft]: "资金",
    [VoucherSource.Invoice]: window.isErp ? "税务管理" : "发票",
    [VoucherSource.ExpenseBill]: "费用单据",
    [VoucherSource.Salary]: window.isErp ? "薪酬管理" : "工资",
    [VoucherSource.FixedAssets]: window.isErp ? "资产管理" : "资产",
    [VoucherSource.Scm]: "进销存",
    [VoucherSource.Purchase]: "采购管理",
    [VoucherSource.Sale]: "销售管理",
    [VoucherSource.Stock]: "库存管理",
    [VoucherSource.ReceivableAndPayable]: "应收应付",
};
function getModuleOrderMapArr(mapType: "key" | "value") {
    return mapType === "key" ? Object.keys(moduleOrderMap).map((item) => parseInt(item)) : Object.values(moduleOrderMap);
}
function sortModule(a: string | number, b: string | number, mapType: "key" | "value") {
    const aIndex = getModuleOrderMapArr(mapType).findIndex((item) => item === a);
    const bIndex = getModuleOrderMapArr(mapType).findIndex((item) => item === b);
    return aIndex - bIndex;
}
//账套未开启资产和进销存模块，把来源单据改为手工录入
export const formatSourceName = (sourceWay: number[]) => {
    const moduleSet = new Set<string>();
    for (let i = 0; i < sourceWay.length; i++) {
        const sourceWayDetail = sourceWay[i] as keyof typeof moduleOrderMap;
        moduleSet.add(moduleOrderMap[sourceWayDetail]);
    }
    const modules = Array.from(moduleSet);
    // modules.sort((a, b) => sortModule(a, b, "value"));
    return modules.join("、");
};
export function getModuleName(sourceWay: number) {
    const voucherSource = sourceWay as keyof typeof moduleOrderMap;
    return moduleOrderMap[voucherSource] || "";
}

//处理单据来源显示/默认显示最长显示2个单据号，日记账过长只显示1个，多余的用...代替
export const handleDocSource = (docSourceArr: any[]): string => {
    if (!docSourceArr || !docSourceArr.length) return "";
    const docSourceArrLength = docSourceArr.length;
    const maxLength = 30;
    if (docSourceArrLength < 2) return docSourceArr[0];
    const totalLength = docSourceArr.slice(0, 2).reduce((acc, cur) => acc + cur.length, 0);
    return totalLength > maxLength
        ? docSourceArr[0] + "..."
        : docSourceArrLength === 2
        ? docSourceArr.join("、")
        : docSourceArr.slice(0, 2).join("、") + "...";
};
export function checkHasModulePermission(sourceWay: number, module?: string) {
    let hasPermission = false;
    switch (sourceWay) {
        case VoucherSource.Cashier:
        case VoucherSource.Invoice:
            hasPermission = checkHasCashierOrInvoicePermission(module);
            break;
        case VoucherSource.FixedAssets:
            hasPermission = checkPermission(["fixedassets-card-canview"]);
            break;
        case VoucherSource.Salary:
            hasPermission = checkPermission(["salarymanage-canview"]);
            break;
        case VoucherSource.Scm:
            hasPermission = checkPermission(["scmvoucher-canview"]);
            break;
        case VoucherSource.Draft:
            hasPermission = checkPermission(["draft-canview"]);
            break;
        case VoucherSource.ExpenseBill:
            hasPermission = checkPermission(["expensebill-canview"]);
            break;
        default:
            break;
    }
    return hasPermission;
}
export function getBillPopContent(source: IVoucherSource, sourceWay: number) {
    const sourceArr: any[] = source[sourceWay];
    if (!sourceArr || !sourceArr.length) return "";
    switch (sourceWay) {
        case VoucherSource.FixedAssets:
            return handleFixedAssetsContent(sourceArr);
        case VoucherSource.Salary:
            return handleSalaryContent(sourceArr);
        case VoucherSource.Scm:
            return handleDocSource(sourceArr.filter((item) => item.billNum !== undefined).map((item: any) => item.billNum));
        case VoucherSource.Draft:
            return handleDocSource(sourceArr.map((item) => item.draftNo));
        case VoucherSource.ExpenseBill:
            return handleDocSource(sourceArr.map((item) => item.expenseBillNo));
        default:
            return "";
    }
}
// 资金 发票 存在子分类，资金 ===> 现金日记账、银行日记账、内部转账  发票 ===> 销项发票、进项发票  按照这些固定顺序显示
function getCashierBillInfo(originSource: IVoucherSource) {
    const source = originSource[VoucherSource.Cashier];
    const transfer: any[] = [];
    const cash: any[] = [];
    const deposit: any[] = [];
    for (let i = 0; i < source.length; i++) {
        const item = source[i];
        if (item.ieType === "-1") {
            transfer.push(item);
        } else if (item.jType === "1020") {
            deposit.push(item);
        } else {
            cash.push(item);
        }
    }
    const totalList = [cash, deposit, transfer].filter((item) => item.length > 0);
    return { totalList, cash, deposit, transfer };
}
function getCashierPopContent(originSource: IVoucherSource, jType: string) {
    const { cash, deposit } = getCashierBillInfo(originSource);
    if (jType === "1020") {
        return handleDocSource(deposit.map((item: any) => item.name));
    } else {
        return handleDocSource(cash.map((item: any) => item.name));
    }
}
function getInvoiceBillInfo(originSource: IVoucherSource) {
    const source = originSource[VoucherSource.Invoice];
    const input: any[] = [];
    const output: any[] = [];
    for (let i = 0; i < source.length; i++) {
        const item = source[i];
        if (item.invCategory === "10070") {
            output.push(item);
        } else {
            input.push(item);
        }
    }
    const totalList = [output, input].filter((item) => item.length > 0);
    return { totalList, input, output };
}
function getInvoicePopContent(originSource: IVoucherSource, invCategory: string) {
    const { input, output } = getInvoiceBillInfo(originSource);
    const invoiceNum: Array<string> = [];
    let has0InvNum = false;
    const list = invCategory === "10070" ? output : input;
    for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item.invNum === "") {
            if (!has0InvNum) {
                invoiceNum.push("未开具发票");
                has0InvNum = true;
            }
        } else {
            invoiceNum.push(item.invNum);
        }
    }
    return handleDocSource(invoiceNum);
}
export function isCashierOrInvoice(sourceWay: number) {
    return sourceWay === VoucherSource.Cashier || sourceWay === VoucherSource.Invoice;
}
function isErpBill(sourceWay: number) {
    if (!window.isErp) return false;
    if (!sourceWay) return false;
    if (sourceWay === VoucherSource.FixedAssets) return false;
    if (sourceWay === VoucherSource.Salary) return false;
    return true;
}
export function checkHasErpBill(sourceWay: Array<number>) {
    return sourceWay.some((item) => isErpBill(item));
}
export function getSourceDescription(sourceWay: number, source: any[]) {
    if (sourceWay === VoucherSource.Cashier) {
        return source[0].ieType === "-1" ? "内部转账：" : source[0].jType === "1020" ? "银行日记账：" : "现金日记账：";
    } else {
        return source[0].invCategory === "10070" ? "销项发票：" : "进项发票：";
    }
}
export function getErpBillInfo(sourceWay: Array<number>, source: IVoucherSource) {
    const erpBills: Array<{ billNo: string; billDate: string }> = [];
    for (let i = 0; i < sourceWay.length; i++) {
        const currentSourceWay = sourceWay[i];
        if (isErpBill(currentSourceWay)) {
            const sourceArr = source[currentSourceWay];
            for (let j = 0; j < sourceArr.length; j++) {
                const bill = sourceArr[j];
                if (currentSourceWay === VoucherSource.Cashier) {
                    const billNo = bill.ieType === "-1" ? "内部转账" : bill.name;
                    erpBills.push({ billNo, billDate: dayjs(bill.date).format("YYYY-MM-DD") });
                } else if (currentSourceWay === VoucherSource.Invoice) {
                    const billNo = bill.invNum === "" ? "未开具发票" : bill.invNum;
                    erpBills.push({ billNo, billDate: dayjs(bill.invDate).format("YYYY-MM-DD") });
                } else {
                    erpBills.push({ billNo: bill.billNum, billDate: dayjs(bill.billDate).format("YYYY-MM-DD") });
                }
            }
        }
    }
    const erpBillNoStr: Array<string> = [];
    const erpBillDateStr: Array<string> = [];
    let hasTransfer = false;
    let hasInvoice = false;
    for (let i = 0; i < erpBills.length; i++) {
        const billNo = erpBills[i].billNo;
        const billDate = erpBills[i].billDate;
        if (billNo === "内部转账") {
            erpBillDateStr.push(billDate);
            if (hasTransfer) continue;
            hasTransfer = true;
            erpBillNoStr.push(billNo);
        } else if (billNo === "未开具发票") {
            erpBillDateStr.push(billDate);
            if (hasInvoice) continue;
            hasInvoice = true;
            erpBillNoStr.push(billNo);
        } else {
            erpBillNoStr.push(billNo);
            erpBillDateStr.push(billDate);
        }
    }
    erpBillDateStr.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    const billNoStr = handleDocSource(erpBillNoStr);
    const searchStartDate = erpBillDateStr.length ? erpBillDateStr[0] : "";
    const searchEndDate = erpBillDateStr.length ? erpBillDateStr[erpBillDateStr.length - 1] : "";
    return { billNoStr, searchStartDate, searchEndDate };
}
export function getErpAccSourceWay(sourceWay: Array<number>) {
    if (!checkHasErpBill(sourceWay)) return sourceWay;
    return sourceWay.filter((item) => !isErpBill(item));
}
export function getSourceCategory(sourceWay: number, source: any[]) {
    return sourceWay === VoucherSource.Cashier ? (source[0].ieType === "-1" ? "-1" : "") || source[0].jType : source[0].invCategory;
}
export function getSourceContent(originSource: IVoucherSource, sourceWay: number, source: any[]) {
    if (sourceWay === VoucherSource.Cashier) {
        return source[0].ieType === "-1" ? "内部转账" : getCashierPopContent(originSource, source[0].jType);
    } else {
        return getInvoicePopContent(originSource, source[0].invCategory);
    }
}
export function getSourceBillInfo(sourceWay: number, source: IVoucherSource) {
    return sourceWay === VoucherSource.Cashier ? getCashierBillInfo(source) : getInvoiceBillInfo(source);
}
function getBillRouteParams(source: Array<any>) {
    const initBillType = source[0].billType;
    let billType = initBillType;
    for (let i = 1; i < source.length; i++) {
        if (source[i].billType !== source[0].billType) {
            billType = initBillType - (initBillType % 10);
            break;
        }
    }
    const dates = source.map((item) => item.billDate);
    dates.sort();
    const searchStartDate = dates[0];
    const searchEndDate = dates[dates.length - 1];
    return { billType, searchStartDate, searchEndDate };
}
// 点击跳转
export function handleRouteToBillPage(tableItem: IVoucherModel, sourceWay: number, module?: string) {
    const { source, vdate: _vdate, vnum, vgName: vgname } = tableItem;
    const vdate = dayjs(_vdate).format("YYYY-MM-DD");
    const urlParams = { from: "voucherList", r: Math.random() };
    if (!checkHasModulePermission(sourceWay, module)) return;
    if (sourceWay === VoucherSource.Cashier) {
        if (!module) return;
        const params = getCashierRouteInfo(source, sourceWay, module);
        const routeParams = { ...params, vdate, vnum, vgname, ...urlParams };
        const path = module === "-1" ? "/Cashier/Transfer?" : module === "1020" ? "/Cashier/DepositJournal?" : "/Cashier/CashJournal?";
        const name = module === "-1" ? "内部转账" : module === "1020" ? "银行日记账" : "现金日记账";
        globalWindowOpenPage(path + getUrlSearchParams(routeParams), name);
    } else if (sourceWay === VoucherSource.Invoice) {
        if (!module) return;
        const invoiceBills = source[sourceWay]?.filter((item: any) => item.invCategory === module);
        if (!invoiceBills || !invoiceBills.length) return;
        const { searchStartDate, searchEndDate } = getBillDateParams(invoiceBills, "invDate", true);
        const params = { vdate, vnum, vgname, searchStartDate, searchEndDate, invoiceCategory: module, ...urlParams };
        const path = module === "10080" ? "/Invoice/PurchaseInvoice?" : "/Invoice/SalesInvoice?";
        const name = module === "10080" ? "进项发票" : "销项发票";
        globalWindowOpenPage(path + getUrlSearchParams(params), name);
    } else if (sourceWay === VoucherSource.Draft) {
        const { searchStartDate, searchEndDate } = getBillDateParams(source[sourceWay], "draftCreatedDate");
        const params = { searchStartDate, searchEndDate, vdate, vnum, vgname, ...urlParams };
        globalWindowOpenPage("/Draft/Draft?" + getUrlSearchParams(params), "票据管理");
    } else if (sourceWay === VoucherSource.FixedAssets) {
        const sourceArr = source[sourceWay];
        if (!sourceArr || !sourceArr.length) return;
        const isChangeRecord = source[sourceWay].every((item: any) => item.faNum);
        //tabflag：2-变更记录  3-折旧
        const fixedAssetsParams = isChangeRecord
            ? { tabflag: 2, vnum, period: source[sourceWay][0].cpid, vdate, vgname }
            : { tabflag: 3, dpid: source[sourceWay][0].dpid ?? "" };
        Object.assign(urlParams, fixedAssetsParams);
        globalWindowOpenPage("/FixedAssets/FixedAssets?" + getUrlSearchParams(urlParams), "固定资产管理");
    } else if (sourceWay === VoucherSource.Salary) {
        const params = { dateKey: source[sourceWay][0].mid, ...urlParams };
        globalWindowOpenPage("/Salary/SalaryManage?" + getUrlSearchParams(params), "工资管理");
    } else if (sourceWay === VoucherSource.Scm) {
        const params = { ...getBillRouteParams(source[sourceWay]), vdate, vnum, vgname, ...urlParams };
        globalWindowOpenPage("/Scm/ScmVoucher?" + getUrlSearchParams(params), "进销存凭证");
    } else if (sourceWay === VoucherSource.ExpenseBill) {
        const { searchStartDate, searchEndDate } = getBillDateParams(source[sourceWay], "billDate");
        const params = { searchStartDate, searchEndDate, vdate, vnum, vgname, billNo: source[sourceWay][0].expenseBillNo, ...urlParams };
        globalWindowOpenPage("/Invoice/ExpenseBill?" + getUrlSearchParams(params), "费用单据");
    }
}
export function handleRouteToErpPage(tableItem: IVoucherModel) {
    if (!checkPermission(["businessvoucher-canview"])) return;
    const { sourceWay, source, vdate: _vdate, vnum, vgName: vgname } = tableItem;
    const { searchStartDate, searchEndDate } = getErpBillInfo(sourceWay, source);
    const vdate = dayjs(_vdate).format("YYYY-MM-DD");
    const params = { vdate, vnum, vgname, searchStartDate, searchEndDate, from: "voucherList" };
    globalWindowOpenPage("/Erp/BusinessVoucher?" + getUrlSearchParams(params), "业务凭证");
}
function getBillDateParams(list: any[], key: string, useDayjs = false) {
    function dateFn(date: any) {
        return useDayjs ? dayjs(date).format("YYYY-MM-DD") : formatDate(date);
    }
    const draftCreatedDateArr = list.map((item) => dateFn(item[key])).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    const searchStartDate = draftCreatedDateArr[0] ?? "";
    const searchEndDate = draftCreatedDateArr[draftCreatedDateArr.length - 1] ?? "";
    return { searchStartDate, searchEndDate };
}
function getCashierRouteInfo(source: IVoucherSource, sourceWay: number, module?: string) {
    const { cash, deposit, transfer } = getCashierBillInfo(source);
    const list = module === "-1" ? transfer : module === "1020" ? deposit : cash;
    const arrAcId = [...new Set(list.map((item: any) => item.acId))];
    const appendParams = module === "-1" ? {} : arrAcId.length > 1 ? { allAcc: "true" } : { cdAccount: arrAcId[0] };
    const sortedDates = list.map((item) => item.date).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
    const minDate = sortedDates[0] ?? "";
    const maxDate = sortedDates[sortedDates.length - 1] ?? "";
    return { searchStartDate: minDate, searchEndDate: maxDate, ...appendParams };
}
// format 单据来源总览
export function getBillContent(source: IVoucherSource, sourceWay: number[]) {
    const modules = sourceWay.slice().sort((a, b) => sortModule(a, b, "key"));
    const billCodeArr: string[] = [];
    for (let i = 0; i < modules.length; i++) {
        if (billCodeArr.length >= 2) break;
        const sourceWayDetail = modules[i];
        const sourceArr = source[sourceWayDetail];
        if (!sourceArr || !sourceArr.length) continue;
        switch (sourceWayDetail) {
            case VoucherSource.Purchase:
            case VoucherSource.Sale:
            case VoucherSource.Stock:
            case VoucherSource.ReceivableAndPayable:
                billCodeArr.push(...sourceArr.map((item: any) => item.billNum));
                break;
            case VoucherSource.Cashier:
                billCodeArr.push(...handleGetCashierBillArr(source));
                break;
            case VoucherSource.FixedAssets:
                billCodeArr.push(...handleGetAssetsBillArr(sourceArr));
                break;
            case VoucherSource.Invoice:
                billCodeArr.push(...handleGetInvoiceBillArr(source));
                break;
            case VoucherSource.Salary:
                billCodeArr.push(...sourceArr.map((item: any) => item.source));
                break;
            case VoucherSource.Scm:
                billCodeArr.push(...sourceArr.filter((item: any) => item.billNum !== undefined).map((item: any) => item.billNum));
                break;
            case VoucherSource.Draft:
                billCodeArr.push(...sourceArr.map((item: any) => item.draftNo));
                break;
            case VoucherSource.ExpenseBill:
                billCodeArr.push(...sourceArr.map((item: any) => item.expenseBillNo));
                break;
        }
    }
    return handleDocSource(billCodeArr);
}
function handleGetCashierBillArr(source: IVoucherSource) {
    const { cash, deposit, transfer } = getCashierBillInfo(source);
    const arr: string[] = [];
    if (cash.length > 0) {
        arr.push(...cash.map((item: any) => item.name));
    }
    if (deposit.length > 0) {
        arr.push(...deposit.map((item: any) => item.name));
    }
    if (transfer.length > 0) {
        arr.push("内部转账");
    }
    return arr;
}
function handleGetAssetsBillArr(source: any[]) {
    // 有 fnNum 为变更   有 fastart 为折旧记录
    const isChangeRecord = source.every((item: any) => item.faNum);
    // 变更记录会有 fnNum 折旧记录只有 dpid 和 dstart
    const arr = source.map((item: any) => (isChangeRecord ? item.faNum : item.dStart + "折旧"));
    return arr;
}
function handleGetInvoiceBillArr(source: IVoucherSource) {
    const { input, output } = getInvoiceBillInfo(source);
    const arr: string[] = [];
    function getInvoiceContentArr(source: any[]) {
        const invoiceNum: Array<string> = [];
        let has0InvNum = false;
        for (let i = 0; i < source.length; i++) {
            const item = source[i];
            if (item.invNum === "") {
                if (!has0InvNum) {
                    invoiceNum.push("未开具发票");
                    has0InvNum = true;
                }
            } else {
                invoiceNum.push(item.invNum);
            }
        }
        return invoiceNum;
    }
    if (output.length > 0) {
        arr.push(...getInvoiceContentArr(output));
    }
    if (input.length > 0) {
        arr.push(...getInvoiceContentArr(input));
    }
    return arr;
}
function checkHasCashierOrInvoicePermission(module?: string) {
    if (!module) return false;
    const moduleMap: any = {
        "-1": "transfer-canview",
        "1020": "depositjournal-canview",
        "1010": "cashjournal-canview",
        "10070": "invoice-output-canview",
        "10080": "invoice-input-canview",
    };
    const permission = moduleMap[module];
    if (!permission) return false;
    return checkPermission([permission]);
}
function handleFixedAssetsContent(source: any[]) {
    const arr = handleGetAssetsBillArr(source);
    const str = handleDocSource(arr);
    return str;
}
function handleSalaryContent(source: any[]) {
    const arr = source.map((item: any) => item.source);
    const str = handleDocSource(arr);
    return str;
}
