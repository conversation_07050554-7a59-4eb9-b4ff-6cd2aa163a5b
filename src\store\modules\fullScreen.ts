import { ref, watch } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { erpChangeFullScreen } from "@/util/erpUtils";
import { isLemonClient, getLemonClient } from "@/util/lmClient";
export const useFullScreenStore = defineStore("fullScreenState", () => {
    const fullScreen = ref<boolean>(false);
    const changeFullScreenStage = (state: boolean) => {
        if (fullScreen.value === state) return;
        fullScreen.value = state;
    };
    watch(fullScreen, (newVal) => {
        if (window.isErp) {
            erpChangeFullScreen(newVal ? "cover" : "uncover");
            return;
        }
        if (isLemonClient()) {
            getLemonClient().hideTopBar(newVal);
        }
    });
    return { fullScreen, changeFullScreenStage };
});

export function useLoadingStoreHook() {
    return useFullScreenStore(store);
}
