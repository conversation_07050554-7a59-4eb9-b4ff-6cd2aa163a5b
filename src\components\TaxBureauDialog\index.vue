<template>
  <el-dialog
    v-model="loginTaxBureauDialog"
    title="登录税局"
    center
    width="888"
    :before-close="cancelInvoiceBlock"
    modal-class="fetch-dialog-wrap"
    class="dialogDrag custom-confirm"
    align-center>
    <div
      class="fetch-invoice-box"
      v-dialogDrag>
      <div class="fetch-invoice-box-body">
        <div
          class="block tips"
          v-show="operationalIndex !== blockIndex.Code">
          <div class="tip-content">
            <i class="remark-warnning-yellow"></i>
            1.一键自动跳转登录电子税局，方便且高效
          </div>
          <div class="tip-content">
            2.
            <span v-show="!taxBureauInfo.taxBureauAuthorized">
              <span class="highlight-red">首次使用或税务信息填写不准确</span>
              ，均需要认证税局登录账号后，才能使用一键登录电子税局功能；
            </span>
            税务信息填写完成后，可前往
            <a
              class="link"
              @click="toAccountSet">
              {{ "【设置-账套-税务信息】" }}
            </a>
            中再次查看和修改
          </div>
          <div class="tip-content">
            3. 如您在使用过程中有任何疑问，请
            <a
              class="link"
              @click="toHelp">
              查看帮助文档
            </a>
            或联系
            <el-tooltip
              effect="light"
              placement="bottom">
              <template #content>
                <img
                  style="width: 200px"
                  src="" />
              </template>
              <a class="link">
                <img
                  style="width: 80px; vertical-align: middle"
                  src="" />
              </a>
            </el-tooltip>
          </div>
        </div>
        <div
          class="block tips"
          v-show="operationalIndex === blockIndex.Code">
          <div class="tip-content">
            <i class="remark-warnning-yellow"></i>
            1. 请注意查收手机号 {{ secretPersonPhone }} 的税局登录短信验证码，并在验证码有效期内提交确认，完成身份验证
          </div>
          <div class="tip-content">
            2.
            <span class="highlight-red">税局验证有效期仅{{ taxCodeTime }}分钟</span>
            ，请及时填写并提交；
          </div>
          <div class="tip-content">3. 如果验证码过期请点击“获取验证码”，重新获取验证码</div>
        </div>
        <div class="block block-flow">
          <div class="flow-node flow-node-tail">
            <i
              class="remark-ring"
              :class="{ 'remark-ring-active': operationalIndex >= blockIndex.CompanyInfo }"></i>
            <span class="flow-node-content">确认公司信息</span>
          </div>
          <div class="flow-node flow-node-tail">
            <i
              class="remark-ring"
              :class="{ 'remark-ring-active': operationalIndex >= blockIndex.TaxInfo }"></i>
            <span class="flow-node-content">填写税局登录信息</span>
          </div>
          <div class="flow-node">
            <i
              class="remark-ring"
              :class="{ 'remark-ring-active': operationalIndex >= blockIndex.Code }"></i>
            <span class="flow-node-content">输入税局验证码</span>
          </div>
        </div>
        <div
          class="block"
          v-show="operationalIndex === blockIndex.CompanyInfo">
          <div class="tax-info-col col-one">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              公司名称
            </div>
            <div class="tax-info-col-right">
              <ToolTip
                :content="taxBureauInfo.taxpayerName"
                placement="right"
                :offset="-328"
                :isInput="true">
                <el-autocomplete
                  :teleported="false"
                  :fit-input-width="true"
                  ref="asNameRef"
                  v-model="taxBureauInfo.taxpayerName"
                  :prop="[{ required: true, message: '亲，公司名称不能为空', trigger: ['blur', 'change'] }]"
                  :fetch-suggestions="querySearch"
                  class="inline-input w-50"
                  placeholder="请输入完整的公司名称"
                  style="width: 238px"
                  @select="selectName"
                  :class="{ 'tax-error-border': checkWarn.TaxpayerName }">
                  <template v-slot:default="{ item }">
                    <ToolTip
                      :content="item.value"
                      :line-clamp="2"
                      placement="right"
                      :maxWidth="400"
                      :teleported="true">
                      <div style="line-height: 18px">{{ item.value }}</div>
                    </ToolTip>
                  </template>
                </el-autocomplete>
              </ToolTip>
              <span
                v-show="checkWarn.TaxpayerName"
                class="error-message-right">
                请输入公司名称
              </span>
            </div>
          </div>
          <div class="tax-info-col col-one">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              统一社会信用代码
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxNumberS"
                style="width: 238px"
                placeholder="请输入统一社会信用代码"
                :class="{ 'tax-error-border': checkWarn.TaxNumber }" />
              <span
                v-show="checkWarn.TaxNumber"
                class="error-message">
                {{ taxNumberErrMsg }}
              </span>
            </div>
          </div>
          <div
            class="tax-info-col col-one"
            v-show="!taxBureauInfo.taxBureauAuthorized">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              增值税种类
            </div>
            <div class="tax-info-col-right">
              <el-radio-group v-model="taxBureauInfo.taxType">
                <el-radio
                  :label="1"
                  size="default">
                  小规模纳税人
                </el-radio>
                <el-radio
                  :label="2"
                  size="default">
                  一般纳税人
                </el-radio>
              </el-radio-group>
              <span
                v-show="checkWarn.TaxType"
                class="error-message-right">
                请选择增值税种类
              </span>
            </div>
          </div>
          <div class="tax-info-col col-one">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              报税地区
            </div>
            <div class="tax-info-col-right">
              <el-select
                v-model="taxBureauInfo.taxAreaId"
                @change="taxAreaChange"
                style="width: 238px"
                filterable
                teleported
                fit-input-width
                :class="{ 'tax-error-border': checkWarn.taxAreaId }">
                <el-option
                  v-for="item in showAreaList"
                  :value="item.taxAreaId"
                  :label="item.taxAreaName"
                  :key="item.taxAreaId"></el-option>
              </el-select>
              <span
                v-show="checkWarn.taxAreaId"
                class="error-message-right">
                请选择报税地区
              </span>
            </div>
          </div>
          <div class="tax-info-col col-one">
            <div class="tax-info-col-left"></div>
            <div class="tax-info-col-right">
              <label class="checkbox-button">
                <el-checkbox
                  v-model="taxBureauInfo.IsAgreement"
                  :true-label="1"
                  :false-label="0">
                  我已阅读并同意
                  <a
                    class="link"
                    @click="toServiceAgreement">
                    《柠檬云财务服务协议及隐私保护协议》
                  </a>
                </el-checkbox>
              </label>
              <span
                v-show="checkWarn.IsAgreement"
                class="error-message">
                请阅读并同意《柠檬云财务服务协议及隐私保护协议》!
              </span>
            </div>
          </div>
        </div>
        <div
          class="block"
          v-show="operationalIndex === blockIndex.TaxInfo">
          <div class="tax-info-col">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              登录方式
            </div>
            <div class="tax-info-col-right">
              <el-select
                v-model="taxBureauInfo.taxBureauLoginType"
                @change="loginTypeChange"
                style="width: 238px"
                :teleported="true"
                :fit-input-width="true"
                :class="{ 'tax-error-border': checkWarn.LoginType }">
                <el-option
                  v-for="item in taxBureauLoginListOption"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"></el-option>
              </el-select>
              <span
                v-show="checkWarn.LoginType"
                class="error-message">
                请选择登录方式
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('TaxAccount')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              登录账号
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxBureauAccount"
                style="width: 238px"
                placeholder="请输入税局登录账号"
                :class="{ 'tax-error-border': checkWarn.TaxAccount }" />
              <span
                v-show="checkWarn.TaxAccount"
                class="error-message">
                请输入登录账号
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('TaxPassword')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              登录密码
            </div>
            <div class="tax-info-col-right">
              <el-input
                type="password"
                style="width: 238px"
                :class="{ 'tax-error-border': checkWarn.TaxPassword }"
                placeholder="请输入税局登录密码"
                v-model="taxBureauInfo.taxpayerPassword"
                show-password />
              <span
                v-show="checkWarn.TaxPassword"
                class="error-message">
                请输入登录密码
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('PersonPositionType')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              身份类型
            </div>
            <div class="tax-info-col-right">
              <el-select
                v-model="taxBureauInfo.taxBureauPersonPositionType"
                style="width: 238px"
                :teleported="true"
                :fit-input-width="true"
                :class="{ 'tax-error-border': checkWarn.PersonPositionType }">
                <el-option
                  :value="0"
                  :label="'请选择办税人员身份类型'"
                  :key="0"></el-option>
                <el-option
                  :value="10"
                  :label="taxBureauLoginTable.PersonPositionTypeEnum[10].Description"
                  :key="10"></el-option>
                <el-option
                  :value="20"
                  :label="taxBureauLoginTable.PersonPositionTypeEnum[20].Description"
                  :key="20"></el-option>
                <el-option
                  :value="30"
                  :label="taxBureauLoginTable.PersonPositionTypeEnum[30].Description"
                  :key="30"></el-option>
                <el-option
                  :value="110"
                  :label="taxBureauLoginTable.PersonPositionTypeEnum[110].Description"
                  :key="110"></el-option>
              </el-select>
              <span
                v-show="checkWarn.PersonPositionType"
                class="error-message">
                请选择身份类型
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('AgentTaxNumber')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              代理机构税号
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxBureauAgentTaxNumber"
                style="width: 238px"
                placeholder="请输入代理机构统一社会信用代码"
                :class="{ 'tax-error-border': checkWarn.taxBureauAgentTaxNumber }" />
              <span
                v-show="checkWarn.taxBureauAgentTaxNumber"
                class="error-message">
                {{ AgentTaxNumberErrMsg }}
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('PersonIdentityNo')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              证件号码
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxBureauPersonIdentityNo"
                style="width: 238px"
                placeholder="请输入办税人员证件号码"
                :class="{ 'tax-error-border': checkWarn.PersonIdentityNo }" />
              <span
                v-show="checkWarn.PersonIdentityNo"
                class="error-message">
                请输入证件号码
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('PersonAccount')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              个人用户账户
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxBureauPersonAccount"
                style="width: 238px"
                placeholder="请输入身份证号码/手机号码/用户名"
                :class="{ 'tax-error-border': checkWarn.PersonAccount }" />
              <span
                v-show="checkWarn.PersonAccount"
                class="error-message">
                {{ personAccountErrMsg }}
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('PersonPassword')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              个人用户密码
            </div>
            <div class="tax-info-col-right">
              <el-input
                type="password"
                style="width: 238px"
                :class="{ 'tax-error-border': checkWarn.PersonPassword }"
                placeholder="请输入个人用户密码"
                v-model="taxBureauInfo.taxBureauPersonPassword"
                show-password />
              <span
                v-show="checkWarn.PersonPassword"
                class="error-message">
                请输入个人用户密码
              </span>
            </div>
          </div>
          <div
            class="tax-info-col"
            v-show="isTaxBureauNeed('PersonPhone')">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              办税人手机号
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxBureauPersonPhone"
                style="width: 238px"
                placeholder="请输入办税人手机号"
                :class="{ 'tax-error-border': checkWarn.PersonPhone }" />
              <span
                v-show="checkWarn.PersonPhone"
                class="error-message">
                {{ personPhoneErrMsg }}
              </span>
            </div>
          </div>
        </div>
        <div
          class="block"
          v-show="operationalIndex === blockIndex.Code">
          <div class="tax-info-col col-one">
            <div class="tax-info-col-left">
              <span class="tax-highlight-red">*</span>
              税局验证码
            </div>
            <div class="tax-info-col-right">
              <el-input
                v-model="taxBureauInfo.taxBureauCode"
                style="width: 238px"
                placeholder="请输入短信验证码"
                :class="{ 'tax-error-border': checkWarn.TaxBureauCode }" />
              <a
                class="button large-2 ml-10"
                style="width: 110px; height: 31px"
                :class="{ disabled: codeCountdown > 0 }"
                :disabled="codeCountdown > 0"
                @click="fetchCode()">
                {{ codeButtonText }}
              </a>
              <span
                v-show="checkWarn.TaxBureauCode"
                class="error-message">
                {{ codeErrMsg }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="fetch-invoice-box-footer buttons">
        <a
          @click="cancelInvoiceBlock"
          class="button mr-20"
          v-show="operationalIndex === blockIndex.CompanyInfo || operationalIndex === blockIndex.Code">
          取消
        </a>
        <a
          @click="preInvoiceBlock"
          class="button mr-20"
          v-show="operationalIndex === blockIndex.TaxInfo || operationalIndex === blockIndex.Code">
          上一步
        </a>
        <a
          @click="nextInvoiceBlock"
          class="button solid-button mr-20"
          v-show="operationalIndex === blockIndex.CompanyInfo || operationalIndex === blockIndex.TaxInfo">
          下一步
        </a>
        <a
          @click="loginTaxBureau"
          class="button solid-button mr-20"
          v-show="operationalIndex === blockIndex.Code">
          登录税局
        </a>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { ElNotify } from "@/utils/notify"
  import { globalWindowOpen, globalWindowOpenPage } from "@/utils/url"
  import { request, Jrequest, type IResponseModel } from "@/utils/service"
  import { loginErrorList, getTaxBureauLoginList, taxBureauLoginList } from "./utils"
  import { getCompanyDetailApi, type ICompanyInfo } from "@/api/company"
  import { getCompanyList } from "@/utils/company"
  import { type ILoginTaskValue } from "@/hooks/useTaxBureau"
  import TaxBureauLoginTableJson from "./loginTable.json"
  import ToolTip from "@/components/ToolTip/index.vue"
  import { useLoading } from "@/hooks/useLoading"

  const taxCodeTime = ref(window.taxCodeTime)

  const loginTaxBureauDialog = ref(true)
  const props = withDefaults(
    defineProps<{
      taskSource: number
      step: number
      successLogin: Function
      getInvoiceTaskList?: Function
      setCollectTaskId?: Function
    }>(),
    {},
  )

  const toAccountSet = () => {
    globalWindowOpenPage("/Settings/AccountSets?isFromTax=true", "账套")
  }

  const toHelp = () => {
    globalWindowOpen("https://help.ningmengyun.com/#/jz/handle?subMenuId=*********")
  }

  const toServiceAgreement = () => {
    globalWindowOpen(window.appHost + "/common/ServiceAgreements_jz.html")
  }
  interface ITaxArea {
    taxAreaId: number
    taxAreaName: string
  }
  const areaList = ref<ITaxArea[]>([])
  const getAreaList = () => {
    request({ url: "/api/common/area-list" }).then((res: IResponseModel<ITaxArea[]>) => {
      if (res.state === 1000) {
        let defaultList = [{ taxAreaId: 0, taxAreaName: "请选择报税地区" }]
        areaList.value = [...defaultList, ...res.data]
      }
    })
  }

  const asNameRef = ref()
  const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
  }
  const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams)
  }

  let selectTaxNumber: string = ""

  const selectName = (item: any) => {
    taxBureauInfo.value.taxpayerName = item.value
    taxBureauInfo.value.taxNumberS = item.creditCode
    selectTaxNumber = item.creditCode

    getCompanyDetailApi(1010, decodeURIComponent(item.value)).then((res: any) => {
      if (res.state === 1000 && res.data && res.data.taxAreaCode > 0) {
        taxBureauInfo.value.taxAreaId = res.data.taxAreaCode
        taxAreaChange()
      }
    })
  }

  const taxBureauInfo = ref()
  const getTaxBureauInfo = () => {
    useBasicInfoStore()
      .getBasicInfo()
      .then((res) => {
        taxBureauInfo.value = res.data
        taxBureauInfo.value.IsAgreement = taxBureauInfo.value.taxBureauAuthorized ? 1 : 0
      })
  }

  let secretPersonPhone = ref("")

  const blockIndex = {
    CompanyInfo: 1,
    TaxInfo: 2,
    Code: 3,
    FetchInvoice: 4,
    FetchInvoiceFail: 5,
  }
  const operationalIndex = ref(blockIndex.CompanyInfo)
  // const noBlockTitle = ref(false)
  // const changeOperationalIndex = (index: number) => {
  //   taxBureauInfo.value.IsAgreement = taxBureauInfo.value.taxBureauAuthorized ? 1 : 0
  //   operationalIndex.value = index
  //   noBlockTitle.value = index === blockIndex.Code
  //   if (index === blockIndex.Code) {
  //     fetchCode()
  //   }
  // }

  const codeCountdown = ref(0)
  const codeButtonText = ref("获取验证码")
  const startCountdown = () => {
    if (codeCountdown.value > 0) return
    codeCountdown.value = taxCodeTime.value * 60
    codeButtonText.value = `获取验证码(${codeCountdown.value})`

    const interval = setInterval(() => {
      codeCountdown.value--
      codeButtonText.value = `获取验证码(${codeCountdown.value})`

      if (codeCountdown.value <= 0) {
        clearInterval(interval)
        codeButtonText.value = "获取验证码"
      }
    }, 1000)
  }

  let fetchLock = false
  const fetchCode = () => {
    if (codeCountdown.value > 0) return
    if (fetchLock) return
    fetchLock = true
    useLoading().enterLoading("获取验证码中，请稍候...")

    const fetchCodeOnce = (retryCount = 0) => {
      let needLock = false
      Jrequest({
        url: `${retryCount === 0 ? "/api/TaxBureau/FetchCode" : "/api/TaxBureau/FetchCodeState"}?taskSource=${props.taskSource}`,
        method: "post",
      })
        .then((res: IResponseModel<ILoginTaskValue>) => {
          if (res.state === 1000) {
            if (res.data.result === "loginCode") {
              secretPersonPhone.value = res.data.info
              ElNotify({ type: "success", message: "获取验证码成功，请输入收到的短信验证码。", duration: 10000 })
              startCountdown()
              return
            } else if (res.data.result === "success") {
              //成功登陆
              props.successLogin()
              return
            }
          } else if (res.state === 2000) {
            if (res.data.result === "countLimit" || res.data.result === "noBusinessType") {
              loginTaxBureauDialog.value = false
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "running" || res.data.result === "frequent") {
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "authorizedError") {
              loginTaxBureauDialog.value = false
              getTaxBureauInfo()
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "needUpdate") {
              loginTaxBureauDialog.value = true
              taxBureauInfo.value.taxBureauAuthorized = false
              operationalIndex.value = blockIndex.TaxInfo
              ElNotify({ type: "warning", message: "输入的个人账号或密码错误，请重新输入！", duration: 10000 })
              return
            } else if (res.data.result === "waiting") {
              retryCount++
              if (retryCount > 60) {
                ElNotify({ type: "warning", message: "税局系统繁忙，获取验证码超时，请稍等1分钟后重试", duration: 10000 })
                return
              } else {
                needLock = true
                setTimeout(() => {
                  fetchCodeOnce(retryCount + 1)
                }, 5000)
                return
              }
            }
          }

          manageLoginErrorMsg(res.msg)
          return
        })
        .catch((err) => {
          console.log(err)
          ElNotify({ type: "warning", message: "获取验证码发生错误，请稍后重试。", duration: 10000 })
        })
        .finally(() => {
          if (!needLock) {
            fetchLock = false
            useLoading().quitLoading()
          }
        })
    }

    fetchCodeOnce()
  }

  let loginLock = false
  const taxBureauLogin = () => {
    if (loginLock) return
    loginLock = true

    useLoading().enterLoading("登录税局中，请稍候...")
    const data = {
      taxPayerName: taxBureauInfo.value.taxpayerName,
      taxPayerNumber: taxBureauInfo.value.taxNumberS,
      taxType: taxBureauInfo.value.taxType,
      taxArea: taxBureauInfo.value.taxAreaId,
      taxLoginType: taxBureauInfo.value.taxBureauLoginType,
      taxAccount: taxBureauInfo.value.taxBureauAccount,
      taxPayerPassword: taxBureauInfo.value.taxpayerPassword,
      personPositionType: taxBureauInfo.value.taxBureauPersonPositionType,
      personIdentityNo: taxBureauInfo.value.taxBureauPersonIdentityNo,
      personAccount: taxBureauInfo.value.taxBureauPersonAccount,
      personPassword: taxBureauInfo.value.taxBureauPersonPassword,
      personPhone: taxBureauInfo.value.taxBureauPersonPhone,
      agentTaxNumber: taxBureauInfo.value.taxBureauAgentTaxNumber,
    }

    const loginOnce = (retryCount = 0) => {
      let needLock = false
      //noBlockTitle 为true 说明是验证码登录,认证自然通过，后续会优化
      Jrequest({
        url: `${retryCount === 0 ? "/api/TaxBureau/Login" : "/api/TaxBureau/LoginState"}?isAuthorized=${
          taxBureauInfo.value.taxBureauAuthorized
        }&taskSource=${props.taskSource}`,
        data,
        method: "post",
      })
        .then((res: IResponseModel<ILoginTaskValue>) => {
          if (res.state === 1000) {
            // login 说明缓存或者登录不需要验证码
            if (res.data.result === "loginCode") {
              secretPersonPhone.value = res.data.info
              taxBureauInfo.value.taxBureauCode = ""
              operationalIndex.value = blockIndex.Code
              useLoading().quitLoading()
              startCountdown()
              taxBureauInfo.value.taxBureauAuthorized = true
              return
            } else if (res.data.taskId > 0 && res.data.result === "taskStart") {
              props.setCollectTaskId?.(res.data.taskId)
              operationalIndex.value = blockIndex.FetchInvoice
              props.getInvoiceTaskList?.()
              taxBureauInfo.value.taxBureauAuthorized = true
              return
            } else if (res.data.result === "success") {
              loginTaxBureauDialog.value = false
              props.successLogin()
              return
            }
          } else if (res.state === 2000) {
            if (res.data.result === "countLimit" || res.data.result === "noBusinessType") {
              loginTaxBureauDialog.value = false
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "running" || res.data.result === "frequent") {
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "authorizedError") {
              loginTaxBureauDialog.value = false
              getTaxBureauInfo()
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "needUpdate") {
              loginTaxBureauDialog.value = true
              taxBureauInfo.value.taxBureauAuthorized = false
              operationalIndex.value = blockIndex.TaxInfo
              ElNotify({ type: "warning", message: "输入的个人账号或密码错误，请重新输入！", duration: 10000 })
              return
            } else if (res.data.result === "waiting") {
              retryCount++
              if (retryCount > 60) {
                ElNotify({ type: "warning", message: "税局系统繁忙，登录超时，请稍等1分钟后重试", duration: 10000 })
                return
              } else {
                needLock = true
                setTimeout(() => {
                  loginOnce(retryCount + 1)
                }, 5000)
                return
              }
            }
          }

          manageLoginErrorMsg(res.msg)
          return
        })
        .catch((err) => {
          console.log(err)
          ElNotify({ type: "warning", message: "登录发生错误，请稍后重试。", duration: 10000 })
        })
        .finally(() => {
          if (!needLock) {
            loginLock = false
            useLoading().quitLoading()
          }
        })
    }

    loginOnce()
  }

  let submitLock = false
  const taxBureauSubmitCode = () => {
    if (submitLock) return
    submitLock = true
    useLoading().enterLoading("登录税局中，请稍候...")
    const data = {
      smsCode: taxBureauInfo.value.taxBureauCode,
    }
    checkWarn.value.TaxBureauCode = false

    const submitCodeOnce = (retryCount = 0) => {
      let needLock = false
      Jrequest({
        url: `${retryCount === 0 ? "/api/TaxBureau/SubmitCode" : "/api/TaxBureau/SubmitCodeState"}?taskSource=${props.taskSource}`,
        data,
        method: "post",
      })
        .then((res: IResponseModel<ILoginTaskValue>) => {
          if (res.state === 1000) {
            if (res.data.taskId > 0 && res.data.result === "taskStart") {
              props.setCollectTaskId?.(res.data.taskId)
              operationalIndex.value = blockIndex.FetchInvoice
              props.getInvoiceTaskList?.()
              return
            } else if (res.data.success) {
              props.successLogin()
              return
            }
          } else if (res.state === 2000) {
            if (res.data.result === "authorizedError") {
              loginTaxBureauDialog.value = false
              getTaxBureauInfo()
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "waiting") {
              retryCount++
              if (retryCount > 60) {
                ElNotify({ type: "warning", message: "税局系统繁忙，提交验证码超时，请稍等1分钟后重试", duration: 10000 })
                return
              } else {
                needLock = true
                setTimeout(() => {
                  submitCodeOnce(retryCount + 1)
                }, 5000)
                return
              }
            }
          }

          manageSubmitErrorMsg(res.msg)
          return
        })
        .catch((err) => {
          console.log(err)
          ElNotify({ type: "warning", message: "提交验证码发生错误，请稍后重试。", duration: 10000 })
        })
        .finally(() => {
          if (!needLock) {
            submitLock = false
            useLoading().quitLoading()
          }
        })
    }

    submitCodeOnce()
  }

  let loginErrorCount = 0
  const manageLoginErrorMsg = (msg: string) => {
    let ret = ""
    for (var key in loginErrorList) {
      if (msg.indexOf(key) >= 0) {
        ret = loginErrorList[key as keyof typeof loginErrorList]
        break
      }
    }
    if (ret.length === 0) ret = "税局登录失败，请稍后重试"
    if (loginErrorCount++ > 3) ret = msg.replace("请稍等1分钟后重试", "请联系在线客服")
    ElNotify({ type: "warning", message: ret, duration: 10000 })
    return
  }

  let submitErrorCount = 0
  const manageSubmitErrorMsg = (msg: string) => {
    if (msg.indexOf("验证码错误") >= 0) {
      codeErrMsg.value = msg
      checkWarn.value.TaxBureauCode = true
      return
    } else if (msg.indexOf("验证码超时") >= 0 || msg.indexOf("该任务暂不需要验证码") >= 0 || msg.indexOf("未获取到验证码，任务超时") >= 0) {
      codeCountdown.value = 0
      ElNotify({ type: "warning", message: "登录失败，验证码已失效，请重新获取", duration: 10000 })
      return
    } else {
      let ret = ""
      for (var key in loginErrorList) {
        if (msg.indexOf(key) >= 0) {
          ret = loginErrorList[key as keyof typeof loginErrorList]
          break
        }
      }

      if (ret.length === 0) ret = "税局系统繁忙，登录失败，请稍等1分钟后重试"
      if (submitErrorCount++ > 3) ret = msg.replace("请稍等1分钟后重试", "请联系在线客服")
      ElNotify({ type: "warning", message: ret, duration: 10000 })
      return
    }
  }

  const cancelInvoiceBlock = () => {
    loginTaxBureauDialog.value = false
  }

  const preInvoiceBlock = () => {
    switch (operationalIndex.value) {
      case blockIndex.CompanyInfo:
        break
      case blockIndex.TaxInfo:
        operationalIndex.value = blockIndex.CompanyInfo
        break
      case blockIndex.Code:
        if (taxBureauInfo.value.taxBureauAuthorized) {
          operationalIndex.value = blockIndex.CompanyInfo
        } else {
          operationalIndex.value = blockIndex.TaxInfo
        }
        break
      default:
        break
    }
  }

  const nextInvoiceBlock = () => {
    if (!checkInvoiceBlock()) return

    switch (operationalIndex.value) {
      case blockIndex.CompanyInfo:
        operationalIndex.value = blockIndex.TaxInfo
        break
      case blockIndex.TaxInfo:
        taxBureauLogin()
        break
      case blockIndex.Code:
        taxBureauSubmitCode()
        break
      default:
        break
    }
  }

  const loginTaxBureau = () => {
    if (!checkInvoiceBlock()) return
    taxBureauSubmitCode()
  }

  const getInitCheck = () => {
    return {
      TaxpayerName: false,
      TaxNumber: false,
      TaxType: false,
      taxAreaId: false,
      DateTime: false,
      IsAgreement: false,
      LoginType: false,
      TaxAccount: false,
      TaxPassword: false,
      PersonPositionType: false,
      PersonIdentityNo: false,
      PersonAccount: false,
      PersonPassword: false,
      PersonPhone: false,
      TaxBureauCode: false,
      taxBureauAgentTaxNumber: false,
    }
  }

  const checkWarn = ref(getInitCheck())

  const codeErrMsg = ref("验证码错误，请确认后重试")
  const personAccountErrMsg = ref("请输入身份证/手机号码/用户名")
  const personPhoneErrMsg = ref("请输入办税人手机号")
  const taxNumberErrMsg = ref("请输入统一社会信用代码")
  const AgentTaxNumberErrMsg = ref("请输入代理机构统一社会信用代码")
  const checkInvoiceBlock = (): boolean => {
    const temp = getInitCheck()
    var res = true
    if (operationalIndex.value === blockIndex.CompanyInfo) {
      if (!taxBureauInfo.value.taxBureauAuthorized) {
        if (!taxBureauInfo.value.taxpayerName || taxBureauInfo.value.taxpayerName.length === 0) {
          temp.TaxpayerName = true
          res = false
        }

        const taxNumberPattern = /^(([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10})|(\d{15})|(\d{20}))$/
        if (!taxBureauInfo.value.taxNumberS || taxBureauInfo.value.taxNumberS.length === 0) {
          taxNumberErrMsg.value = "请输入统一社会信用代码"
          temp.TaxNumber = true
          res = false
        } else if (!taxNumberPattern.test(taxBureauInfo.value.taxNumberS)) {
          taxNumberErrMsg.value = "统一社会信用代码格式不正确，请重新输入"
          temp.TaxNumber = true
          res = false
        } else if (selectTaxNumber.length !== 0 && selectTaxNumber != taxBureauInfo.value.taxNumberS) {
          taxNumberErrMsg.value = "请核对您填写的社会信用代码是否正确，如核对无误可继续点击下一步操作"
          temp.TaxNumber = true
          res = false
        }
        selectTaxNumber = ""

        if (!taxBureauInfo.value.taxType) {
          temp.TaxType = true
          res = false
        }

        if (!taxBureauInfo.value.taxAreaId) {
          temp.taxAreaId = true
          res = false
        }

        if (!taxBureauInfo.value.IsAgreement) {
          temp.IsAgreement = true
          res = false
        }
      }
    } else if (operationalIndex.value === blockIndex.TaxInfo) {
      if (!taxBureauInfo.value.taxBureauLoginType) {
        temp.LoginType = true
        res = false
      }
      if (isTaxBureauNeed("AgentTaxNumber")) {
        const taxNumberPattern = /^(([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10})|(\d{15})|(\d{20}))$/
        if (!taxBureauInfo.value.taxBureauAgentTaxNumber || taxBureauInfo.value.taxBureauAgentTaxNumber.length === 0) {
          AgentTaxNumberErrMsg.value = "请输入代理机构统一社会信用代码"
          temp.taxBureauAgentTaxNumber = true
          res = false
        } else if (!taxNumberPattern.test(taxBureauInfo.value.taxBureauAgentTaxNumber)) {
          AgentTaxNumberErrMsg.value = "代理机构统一社会信用代码格式不正确"
          temp.taxBureauAgentTaxNumber = true
          res = false
        }
      }

      if (isTaxBureauNeed("TaxAccount") && (!taxBureauInfo.value.taxBureauAccount || taxBureauInfo.value.taxBureauAccount.length === 0)) {
        temp.TaxAccount = true
        res = false
      }

      if (isTaxBureauNeed("TaxPassword") && (!taxBureauInfo.value.taxpayerPassword || taxBureauInfo.value.taxpayerPassword.length === 0)) {
        temp.TaxPassword = true
        res = false
      }

      if (isTaxBureauNeed("PersonPositionType") && !taxBureauInfo.value.taxBureauPersonPositionType) {
        temp.PersonPositionType = true
        res = false
      }

      if (
        isTaxBureauNeed("PersonIdentityNo") &&
        (!taxBureauInfo.value.taxBureauPersonIdentityNo || taxBureauInfo.value.taxBureauPersonIdentityNo.length === 0)
      ) {
        temp.PersonIdentityNo = true
        res = false
      }

      if (isTaxBureauNeed("PersonAccount")) {
        const pattern =
          /^(0|86|17951)?(10|13|14|15|16|17|18|19)[0-9]{9}$|^[1-9]\d{5}(19|20)\d{2}(0\d|1[0-2])([0-2]\d|3[0-1])\d{3}([0-9]|X)$|^[A-Za-z][A-Za-z0-9]{5,19}$/
        if (!taxBureauInfo.value.taxBureauPersonAccount || taxBureauInfo.value.taxBureauPersonAccount.length === 0) {
          personAccountErrMsg.value = "请输入身份证/手机号码/用户名"
          temp.PersonAccount = true
          res = false
        } else if (!pattern.test(taxBureauInfo.value.taxBureauPersonAccount)) {
          personAccountErrMsg.value = "身份证/手机号码/用户名格式不正确"
          temp.PersonAccount = true
          res = false
        }
      }

      if (
        isTaxBureauNeed("PersonPassword") &&
        (!taxBureauInfo.value.taxBureauPersonPassword || taxBureauInfo.value.taxBureauPersonPassword.length === 0)
      ) {
        temp.PersonPassword = true
        res = false
      }

      if (isTaxBureauNeed("PersonPhone")) {
        const pattern = /^(0|86|17951)?(10|13|14|15|16|17|18|19)[0-9]{9}$/
        if (!taxBureauInfo.value.taxBureauPersonPhone || taxBureauInfo.value.taxBureauPersonPhone.length === 0) {
          personPhoneErrMsg.value = "请输入办税人手机号"
          temp.PersonPhone = true
          res = false
        } else if (!pattern.test(taxBureauInfo.value.taxBureauPersonPhone)) {
          personPhoneErrMsg.value = "办税人手机号格式不正确"
          temp.PersonPhone = true
          res = false
        }
      }
    } else if (operationalIndex.value === blockIndex.Code) {
      const pattern = /^\d{6}$/
      if (!taxBureauInfo.value.taxBureauCode || taxBureauInfo.value.taxBureauCode.length === 0) {
        codeErrMsg.value = "请输入验证码"
        temp.TaxBureauCode = true
        res = false
      } else if (!pattern.test(taxBureauInfo.value.taxBureauCode)) {
        codeErrMsg.value = "验证码格式不正确，应为六位数字"
        temp.TaxBureauCode = true
        res = false
      }
    }
    checkWarn.value = temp
    return res
  }

  interface ILoginTable {
    [key: string]: any
  }
  const taxBureauLoginTable = TaxBureauLoginTableJson as ILoginTable
  let taxBureauAreaLoginTable: any = null

  const isTaxBureauNeed = (name: string) => {
    if (taxBureauAreaLoginTable === null) return false
    return !!taxBureauAreaLoginTable[name as keyof typeof taxBureauAreaLoginTable]
  }

  const taxAreaChange = () => {
    if (taxBureauInfo.value?.taxAreaId) {
      if (!invoiceAgentArea.value.includes(taxBureauInfo.value?.taxAreaId)) {
        taxBureauInfo.value.taxBureauLoginType = 9
      }
      const areaId = taxBureauInfo.value.taxAreaId.toString()
      const loginType = taxBureauInfo.value.taxBureauLoginType.toString()
      taxBureauAreaLoginTable = taxBureauLoginTable[areaId]?.[loginType] || null
      taxBureauInfo.value.taxBureauPersonPositionType = taxBureauInfo.value.taxBureauPersonPositionType ?? 0
    } else {
      taxBureauAreaLoginTable = null
    }
  }
  const loginTypeChange = () => {
    taxBureauAreaLoginTable = taxBureauLoginTable[taxBureauInfo.value.taxAreaId][taxBureauInfo.value.taxBureauLoginType]
  }

  onMounted(() => {
    getAreaList()
    taxBureauInfo.value = { ...useBasicInfoStore().basicInfo, isAgreement: useBasicInfoStore().basicInfo.taxBureauAuthorized ? 1 : 0 }
    getUseAgentTax()
  })
  watch(
    () => loginTaxBureauDialog.value,
    (newVal) => {
      if (newVal) {
        checkWarn.value = getInitCheck()
        operationalIndex.value = blockIndex.CompanyInfo
      }
    },
  )

  const showAreaList = ref<Array<ITaxArea>>([])
  watchEffect(() => {
    showAreaList.value = JSON.parse(JSON.stringify(areaList.value))
  })

  //代理业务登录
  const taxBureauLoginListOption = ref(taxBureauLoginList)
  const invoiceAgentArea = ref<number[]>([])
  watch(
    () => taxBureauInfo.value?.taxAreaId,
    (val) => {
      taxBureauLoginListOption.value = getTaxBureauLoginList(invoiceAgentArea.value, val)
    },
  )
  //配置支持代理登录地区
  const getUseAgentTaxApi = () => {
    return request({
      url: window.downLoadUrl + "Websites/InvoiceConfig/AgentBusinessAreaCode.json" + "?r=" + Math.random(),
    })
  }
  const getUseAgentTax = () => {
    getUseAgentTaxApi().then((res: any) => {
      invoiceAgentArea.value = res
      taxBureauLoginListOption.value = getTaxBureauLoginList(invoiceAgentArea.value, taxBureauInfo.value.taxAreaId)
      taxAreaChange()
    })
  }

  watch(
    () => props.step,
    (val) => {
      operationalIndex.value = val
      if (val === blockIndex.Code) {
        fetchCode()
      }
    },
    { immediate: true },
  )
</script>

<style lang="scss" scoped>
  .fetch-invoice-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;

    .fetch-invoice-box-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: stretch;
      text-align: center;
      min-height: 200px;
      padding: 20px 0;

      .block {
        display: flex;
        align-items: flex-start;
        position: relative;
        flex-wrap: wrap;
        min-height: 100px;
        margin-top: 10px;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;

        .tax-info-col {
          display: flex;
          width: 50%;
          min-height: 1px;
          padding: 10px 0;
          box-sizing: border-box;
          -moz-box-sizing: border-box;
          -webkit-box-sizing: border-box;

          .tax-info-col-left {
            display: block;
            text-align: right;
            width: 34%;
            margin-right: 1%;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            min-height: 32px;

            .tax-highlight-red {
              color: var(--red) !important;
              margin-right: 5px;
            }
          }

          .tax-info-col-right {
            position: relative;
            display: block;
            text-align: left;
            width: 64%;
            margin-left: 1%;
            font-size: var(--font-size);
            line-height: 32px;
            min-height: 32px;

            .tax-error-border {
              border: 1px solid var(--red);
            }

            .error-message {
              position: absolute;
              top: 28px;
              left: 10px;
              color: var(--red);
            }

            .error-message-right {
              position: relative;
              top: 0px;
              left: 10px;
              color: var(--red);
            }

            span {
              font-size: var(--font-size);
              line-height: 32px;
            }
          }

          &.col-one {
            width: 100%;
          }
        }
      }

      .tips {
        background-color: var(--background-color);
        display: flex;
        flex-direction: column;
        align-items: stretch;
        min-height: 100px;
        margin-top: 10px;
        width: 710px;
        margin: 0 calc((100% - 710px) / 2);
        padding: 10px 30px;
        border-radius: 6px;

        .tip-content {
          white-space: normal;
          text-align: left;
          color: var(--weaker-font-color);
          font-size: 12px;
          line-height: 24px;

          .link {
            margin: 0 3px;
            font-size: 12px;
          }
        }

        .service-info {
          display: none;
          position: absolute;
          right: 0px;
          top: -130px;
          z-index: 9999;
        }

        .remark-warnning-yellow {
          //background-image: url("@/assets/Settings/warnning-yellow.png");
          background-repeat: no-repeat;
          background-position: 0 0;
          background-size: 16px 16px;
          height: 16px;
          width: 16px;
          position: absolute;
          left: 10px;
          top: 14px;
        }
      }

      .block-flow {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: stretch;
        flex-wrap: wrap;
        min-height: 30px;
        margin-top: 10px;
        padding: 0 120px;
        counter-reset: index;

        .flow-node {
          flex-grow: 0;
        }

        .flow-node-tail {
          &:after {
            content: " ";
            margin: 0 10px;
            width: 30px;
            height: 0.5px;
            display: inline-block;
            background-color: var(--border-color);
            position: relative;
            bottom: 5px;
          }
        }

        .flow-node-content {
          font-size: var(--h4);
          font-weight: 600;
        }

        .remark-ring {
          background-color: var(--jz-table-title-color);
          color: var(--weaker-font-color);
          border-radius: 50%;
          font-size: 12px;
          line-height: 22px;
          vertical-align: middle;
          margin-right: 5px;
          display: inline-block;
          height: 22px;
          width: 22px;
          text-align: center;
          box-sizing: border-box;
          font-style: normal;
          position: relative;
          top: -2px;
          counter-increment: index;

          &.remark-ring-active {
            background-color: var(--light-main-color);
            color: var(--white);
          }

          &:after {
            content: counter(index);
          }
        }
      }
    }

    .fetch-invoice-box-footer {
      padding: 20px 0;
      text-align: center;
    }
  }

  :deep(.el-autocomplete-suggestion li) {
    margin: 12px 0;
  }
</style>
