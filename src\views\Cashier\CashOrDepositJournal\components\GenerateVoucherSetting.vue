<template>
    <div class="title">{{ jType === 1010 ? "现金日记账" : "银行日记账" }}</div>
    <el-tabs v-model="type">
        <el-tab-pane label="收入模板" name="income"></el-tab-pane>
        <el-tab-pane label="支出模板" name="expenditure"></el-tab-pane>
    </el-tabs>
    <div class="setting-wrap">
        <div class="setting-content">
            <div class="edit-content-top">
                <a class="button solid-button mr-10" v-permission="['journalvouchertemplate-canedit']" @click="handleNew">新建</a>
                <a class="button" @click="goBackSettings">返回</a>
            </div>
            <div class="edit-content-table">
                <div class="edit-setting-block" v-for="(item, index) in tempTableData" :key="index">
                    <div class="setting-block-top">
                        <span class="bold left">
                            <ToolTip :content="item.ietypeModel.name" :maxWidth="120">
                                <span class="tooltip-overflow">{{ item.ietypeModel.name }}</span>
                            </ToolTip>
                            <a class="link" v-show="item.voucherTemplateModel.hasErrorVoucherLines" @click="showVoucherTemplateEdit(item)">
                                <span class="warn-icon red temp-block-error"></span>
                                <span class="temp-logic-error">模板逻辑有误</span>
                            </a>
                        </span>
                        <a class="link" v-permission="['journalvouchertemplate-canedit']" @click="showVoucherTemplateEdit(item)">修改</a>
                        <a
                            class="link ml-10"
                            v-permission="['journalvouchertemplate-candelete']"
                            @click="handleDelete(item.ietypeModel.templateId)"
                            >删除</a
                        >
                    </div>
                    <div class="setting-block-lines">
                        <div class="setting-block-line" v-for="(line, i) in item.voucherTemplateModel.voucherLines" :key="i">
                            {{ line.debit ? "借：" : "贷：" }}{{ line.asubName }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-show="voucherDialogShow">
        <GenerateVoucherDialog
            :columns="columns"
            :pre-check="preCheck"
            :default-line="defaultLine"
            :firstRowNotShow="type === 'income'"
            :lastRowNotShow="type === 'expenditure'"
            :not-delete-row="2"
            not-delete-notify-text="凭证模板不能少于1行哦"
            v-model:voucher-dialog-is-show="voucherDialogShow"
            @voucherSave="handleVoucherSave"
            width="570px"
            ref="generateVoucherDialogRef"
            class="generate-voucher-dialog"
            :setModule="setModuleDialog"
        >
            <span class="txt">收支类别：</span>
            <div class="ie-type-select">
                <Select 
                    v-model="editIEType" 
                    :fit-input-width="true" 
                    :teleported="true"
                    :filterable="true"
                    :filter-method="ietypeFilterMethod"
                >
                    <Option v-for="item in filterIEType" :key="item.subkey" :label="item.value2" :value="item.subkey" />
                </Select>
            </div>
        </GenerateVoucherDialog>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import GenerateVoucherDialog from "@/components/dialog/GenerateVoucher/index.vue";
import type { IVoucherTemplateModel } from "@/components/dialog/GenerateVoucher/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IIETypeItem } from "@/views/Cashier/CashOrDepositJournal/types";
import { nextTick } from "vue";
import type { VoucherTemplateModel } from "@/api/voucherTemplate";
import { ElConfirm } from "@/util/confirm";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getUrlSearchParams } from "@/util/url";
import ToolTip from "@/components/ToolTip/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const setModuleDialog = "CashGenerateVoucherDialog"
const props = defineProps({
    ieTypeList: {
        type: Array<IIETypeItem>,
        required: true,
    },
    jType: {
        type: Number,
        required: true,
    },
});

const emit = defineEmits(["goBackSettings"]);

const accountSetStore = useAccountSetStore();
const accountingStandard = accountSetStore.accountSet?.accountingStandard as number;

const ieTypeListForSettings = computed(() => {
    let data;
    if (type.value === "income") {
        data = props.ieTypeList.filter((item) => item.value2.startsWith("收-") && item.haschild === 0);
    } else {
        data = props.ieTypeList.filter((item) => item.value2.startsWith("支-") && item.haschild === 0);
    }
    data.unshift({ subkey: "0", value1: "0", value2: "请选择" });
    return data;
});

const tempTableData = ref<ITableDataRow[]>([]);
const type = ref<"income" | "expenditure">("income");
const voucherDialogShow = ref<boolean>(false);
const columns = ref<Array<IColumnProps>>([{ slot: "asubId" }, { slot: "direction" }]);
const generateVoucherDialogRef = ref<InstanceType<typeof GenerateVoucherDialog>>();
const currentIEType = ref("0");
const editIEType = ref("0");

interface ITableDataRow {
    ietypeModel: IIETypeModel;
    voucherTemplateModel: VoucherTemplateModel;
}

interface IIETypeModel {
    asId: number;
    code: string;
    fulleName: string;
    id: number;
    ietype: number;
    name: string;
    preIETypeName: string;
    templateId: number;
}

const getDataList = (ietypeId?: number) => {
    request({
        url: `/api/IEType/GetListForJournalTemplate?type=${type.value}`,
        method: "post",
    }).then((res: any) => {
        const result = res as IResponseModel<any>;
        if (result.state === 1000) {
            const tableData: ITableDataRow[] = result.data[type.value];
            const filterTableData = tableData.map((item) => {
                item.voucherTemplateModel.voucherLines.map((voucherLine, index) => {
                    if (
                        (type.value === "income" && index == 0) ||
                        (type.value === "expenditure" && index == item.voucherTemplateModel.voucherLines.length - 1)
                    ) {
                        if (props.jType === 1010 && voucherLine.asubId === 10002) {
                            const asubName = accountingStandard === 3 ? "现金" : "库存现金";
                            voucherLine.asubName = voucherLine.asubName.indexOf("1002") === 0 ? "1001 " + asubName : "101 " + asubName;
                        }
                        if (props.jType === 1020 && voucherLine.asubId === 10001) {
                            voucherLine.asubName = voucherLine.asubName.indexOf("1001") === 0 ? "1002 银行存款" : "102 银行存款";
                        }
                    }
                    return voucherLine;
                });
                return item;
            });
            if (props.jType === 1010) {
                tempTableData.value = filterTableData;
                if (ietypeId) {
                    showVouchetTemplateNew(ietypeId);
                }
                return;
            }

            tempTableData.value = filterTableData;
            if (ietypeId) {
                showVouchetTemplateNew(ietypeId);
            }
        } else {
            ElNotify({
                type: "error",
                message: "获取数据失败，请刷新页面重试",
            });
        }
    });
};

const init = (ietype?: "income" | "expenditure", ietypeId?: number) => {
    if (ietype) {
        if (ietype === "income") {
            type.value = "income";
        } else if (ietype === "expenditure") {
            type.value = "expenditure";
        }
        getDataList(ietypeId);
        return;
    }

    if (type.value !== "income") {
        type.value = "income";
    } else {
        getDataList();
    }
};

const defaultLine = {
    asubId: "",
    direction: 1,
};

const handleNew = () => {
    showVouchetTemplateNew();
};

const showVouchetTemplateNew = (ietypeId?: number) => {
    currentIEType.value = "0";
    editIEType.value = "0";
    const data: IVoucherTemplateModel = {
        vgId: 1010,
        vtId: 0,
        voucherTemplateLines: [
            {
                asubId: "",
                direction: 1,
            },
            {
                asubId: "",
                direction: 2,
            },
        ],
    };
    dataLock(data);

    nextTick().then(() => {
        editIEType.value = ietypeId?.toString() || "0";
        generateVoucherDialogRef.value?.initEditForm(data, "new");
        voucherDialogShow.value = true;
        nextTick().then(() => {
            generateVoucherDialogRef.value?.defaultOpenAsubSelect();
        });
    });
};

const showVoucherTemplateEdit = (rowData: ITableDataRow) => {
    currentIEType.value = rowData.ietypeModel.id.toString();
    editIEType.value = rowData.ietypeModel.id.toString();
    const data: IVoucherTemplateModel = {
        vgId: rowData.voucherTemplateModel.vgId,
        vtId: rowData.voucherTemplateModel.vtId,
        voucherTemplateLines: rowData.voucherTemplateModel.voucherLines.map((i) => {
            return {
                asubId: i.asubId.toString(),
                direction: i.credit > 0 ? 2 : 1,
                logicEnum: i.logicEnum,
            };
        }),
    };
    if(rowData.voucherTemplateModel.voucherLines.length === 1 && type.value === "expenditure"){
        data.voucherTemplateLines.unshift({
            asubId: "",
            direction: 1,
        });
    }
    dataLock(data);
    // generateVoucherDialogRef.value?.initEditForm(data)

    nextTick().then(() => {
        generateVoucherDialogRef.value?.initEditForm(data);
        voucherDialogShow.value = true;
    });
};

const dataLock = (data: IVoucherTemplateModel) => {
    let asubId: number;
    if (props.jType === 1010) {
        asubId = 10001;
    } else {
        asubId = 10002;
    }
    const length = data.voucherTemplateLines.length;
    if (type.value === "income") {
        data.voucherTemplateLines[0].asubId = asubId.toString();
        data.voucherTemplateLines[0].lockAsub = true;
    } else {
        data.voucherTemplateLines[length - 1].asubId = asubId.toString();
        data.voucherTemplateLines[length - 1].lockAsub = true;
    }
};

const preCheck = () => {
    if (editIEType.value === "0") {
        ElNotify({
            type: "warning",
            message: "请选择收支类别",
        });
        return false;
    }
    //新增模板或者修改当前模板的收支类别时，判断是否已存在
    if (currentIEType.value === "0" || currentIEType.value !== editIEType.value) {
        const ieType = parseInt(editIEType.value);
        if (tempTableData.value.filter((i) => i.ietypeModel.id === ieType).length > 0) {
            ElNotify({
                type: "warning",
                message: "该收支类别已有模板，请对该模板进行编辑！",
            });
            return false;
        }
    }
    return true;
};

const handleVoucherSave = (templateData: IVoucherTemplateModel) => {
    if (templateData.voucherTemplateLines.filter((obj) => obj.logicEnum === 0).length > 1) {
        ElNotify({ type: "warning", message: "保存失败，请联系侧边栏客服" });
        return;
    }
    const params = generateVoucherDialogRef.value?.buildSaveData({ vtType: 300, vtName: "资金模板" });
    const queryParams: any = { ieTypeId: editIEType.value };
    if (params?.vtId) {
        queryParams.templateId = params.vtId;
    }
    request({
        url: `/api/CashierTemplate/${props.jType === 1010 ? "Cash" : "Deposit"}?` + getUrlSearchParams(queryParams),
        method: params?.vtId !== 0 ? "put" : "post",
        data: params,
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000 && res.data) {
                ElNotify({ type: "success", message: "保存成功" });
                voucherDialogShow.value = false;
                getDataList();
            } else {
                ElNotify({ type: "warning", message: res.msg || "出现异常，请稍后重试" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "出现异常，请稍后重试" });
        });
};

const handleDelete = (vtId: number) => {
    ElConfirm("亲，确认要删除吗").then((r) => {
        if (r) {
            const path = props.jType === 1010 ? "Cash" : "Deposit";
            request({ url: "/api/CashierTemplate/" + path + "?vtId=" + vtId, method: "delete" })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000 && res.data) {
                        ElNotify({ type: "success", message: "删除成功" });
                        getDataList();
                    } else {
                        ElNotify({ type: "warning", message: "出现异常，请稍后重试" });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "出现异常，请稍后重试" });
                });
        }
    });
};

const goBackSettings = () => {
    emit("goBackSettings");
};

watch(type, () => {
    getDataList();
});

defineExpose({ init });

const filterIEType = ref<Array<IIETypeItem>>([]);
watchEffect(() => { 
    filterIEType.value = JSON.parse(JSON.stringify(ieTypeListForSettings.value));  
});
function ietypeFilterMethod(value: string) {
    filterIEType.value = commonFilterMethod(value, ieTypeListForSettings.value, 'value2');
}
</script>

<style lang="less" scoped>
@import url(@/style/Common.less);
@import url(@/style/SelfAdaption.less);
.setting-wrap {
    flex: 1;
    overflow-y: auto;
}
.setting-content {
    width: 1000px;
    margin: 1px auto 0;
}

.edit-content-top {
    padding: 10px 20px;
    height: 30px;
    display: flex;
    justify-content: flex-end;
}

.edit-content-table {
    padding: 0px 20px 20px 20px;
    font-size: var(--h5);
    color: var(--font-color);

    .edit-setting-block {
        border: 1px solid var(--border-color);
        line-height: 17px;
        & + .edit-setting-block {
            margin-top: 10px;
        }
        .setting-block-top {
            display: flex;
            justify-content: flex-end;
            margin: 10px 20px 0px 20px;
            .left {
                margin-right: auto;
                display: flex;
                align-items: center;
                .link {
                    text-decoration: none;
                    display: flex;
                    align-items: center;
                    .warn-icon {
                        display: inline-block;
                        height: 16px;
                        width: 16px;
                        background: url("@/assets/Icons/warn-red.png") no-repeat;
                        background-size: contain;
                        vertical-align: top;
                        margin-left: 18px;
                    }
                    .temp-logic-error {
                        color: var(--red) !important;
                        margin-left: 6px;
                        font-weight: normal;
                    }
                }
            }

            .bold {
                font-weight: bold;
            }
            .link {
                font-size: var(--h5);
            }
            .tooltip-overflow {
                display: inline-block;
                max-width: 120px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .setting-block-lines {
            margin: 10px 40px 10px 40px;

            .setting-block-line {
                text-align: left;
            }
        }
    }
}
.generate-voucher-dialog {
    .ie-type-select {
        display: inline-block;
        .detail-el-select(160px);
    }
    .txt {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 30px;
        margin-right: 2px;
    }
}
</style>
