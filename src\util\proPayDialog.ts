import { createApp } from "vue";
import ProDialog from "@/components/ProPayDialog/index.vue";
import ElementPlus from "element-plus";
import { getCookie, setCookie } from "./cookie";
import { getGlobalToken } from "./baseInfo";
import { isDemoAccountSet, isInWxWork } from "./wxwork";
import { getLemonClient, isLemonClient } from "./lmClient";
import { useTrialStatusStoreHook } from "@/store/modules/trialStatus";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

export const tryShowPayDialog = (
    dialogType: 1 | 2,
    module: string,
    msg?: string,
    highlightmodule?: string,
    onclose?: () => void
): boolean => {
    if (!window.isProSystem && !window.isErp && !useThirdPartInfoStoreHook().isThirdPart && !window.isBossSystem) {
        const userSn = useAccountSetStoreHook().userInfo?.userSn;
        const appasid = getGlobalToken();
        const key = module + "-" + userSn + "-" + appasid + "pay-dialog";

        if (module && getCookie(key)) {
            onclose && onclose();
            return false;
        } else {
            const trialStatusStore = useTrialStatusStoreHook();
            const hasAccountSet = JSON.parse(localStorage.getItem('hasAccountSet') || 'true')

            // 有账套时判断是否试用期
            if (!trialStatusStore.isTrial && hasAccountSet) {
                onclose && onclose();
                return false;
            }

            if (dialogType == 2 && (trialStatusStore.isExpired || trialStatusStore.remainingDays <= 7)) {
                onclose && onclose();
                return false;
            }

            if (module) {
                //企业微信演示账套，每次都弹出
                if (isInWxWork()) {
                    isDemoAccountSet().then((value) => {
                        if (!value) {
                            setCookie(key, "1", "d60");
                        }
                    });
                } else {
                    setCookie(key, "1", "d60");
                }
            }
            // 客户端也走前端
            // if (isLemonClient()) {
            //     if (dialogType === 1) {
            //         getLemonClient().showBuyDialog(msg || "", onclose || (() => {}), highlightmodule || "");
            //         return true;
            //     }
            // }
            const props = {
                dialogType,
                msg,
                highlightmodule,
                onclose: () => {
                    capp.unmount();
                    document.body.removeChild(container);
                    onclose && onclose();
                },
            };
            const capp = createApp(ProDialog, props);
            const container = document.createElement("div");
            capp.use(ElementPlus);
            capp.mount(container);
            document.body.insertBefore(container, document.body.firstChild); //插入到body最前面，层级更高
            return true;
        }
    } else {
        onclose && onclose();
        return false;
    }
};
