<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">进销存凭证</div>
                    <el-tabs v-model="tabName" @tab-click="handleTabClick">
                        <el-tab-pane label="进销存单据列表" name="receipts">
                            <ReceiptsView
                                v-if="isLoaded.receipts"
                                :scmAsid="scmAsid"
                                :scmProductType="scmProductType"
                                :scmAsName="scmAsName"
                                :cstId="cstId"
                                @open-voucher-from-bill="openVoucherFromBill"
                                @show-journal-settings="showJournalSettings" @set-bill-root-type="setBillRootType"
                                ref="receiptsViewRef"
                            >
                            </ReceiptsView>
                        </el-tab-pane>
                        <el-tab-pane label="进销存凭证一览表" name="voucher">
                            <VoucherView
                                ref="voucherViewRef"
                                v-if="isLoaded.voucher"
                                :scmAsid="scmAsid"
                                :scmProductType="scmProductType"
                            ></VoucherView>
                        </el-tab-pane>
                        <el-tab-pane label="查看进销存差异" name="difference">
                            <DifferenceView
                                v-if="isLoaded.difference"
                                :scmAsid="scmAsid"
                                :scmProductType="scmProductType"
                                :scmAsName="scmAsName"
                                :cstId="cstId"
                            ></DifferenceView>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </template>
            <template #genVoucher>
                <GenerateVoucher
                    :title="'进销存凭证'"
                    :documentTitle="'进销存'"
                    :query-params="genVoucherQueryParameters"
                    :error-table-columns="genVoucherErrorTableColumns"
                    @back="genVoucherSaveSuccess"
                    @voucher-changed="genVoucherChangedHandle"
                    ref="generateVoucherRef"
                    :merge-error-function="mergeErrorFunction"
                    @load-success="loadSuccess = true"
                >
                    <template #toolbar>
                        <a class="solid-button" @click="genVoucher()">保存</a>
                        <a class="button ml-10" @click="currentSlot = 'main'">取消</a>
                        <el-checkbox class="ml10" label="凭证合并" @change="mergeVoucher()" v-model="genVoucherQueryParameters.isMerge"></el-checkbox>
                        <SelectCheckbox class="ml10" ref="selectCheckboxRef" inputPlaceholder="请选择凭证合并条件" :useElIcon="true" width="200px"
                            :options="options" :select-all="false" @update:selected-list="changeSelectedList"
                            v-show="genVoucherQueryParameters.isMerge" v-model:selectedList="selectedList" :show-all="false" @check-box-show-change="checkBoxShowChange" >
                        </SelectCheckbox>
                        <span class="ml10" style="font-size: 14px;" v-show="genVoucherQueryParameters.isMerge">科目合并</span>
                        <SelectCheckbox class="ml10" :useElIcon="true" width="180px" :options="subjectOption"
                            :select-all="false" @update:selected-list="changeSubjectSelectedList"
                            v-show="genVoucherQueryParameters.isMerge" v-model:selectedList="subjectSelectedList" ref="selectSubjectRef" :place-holder="'请选择科目合并方式'" >
                        </SelectCheckbox>

                        <el-checkbox class="ml20" label="智能匹配客户/供应商" v-model="genVoucherQueryParameters.autoMatch"
                            @change="genVoucherCheckboxChanged(3)"></el-checkbox>
                        <BubbleTip :bubble-width="340" :bubble-top="16" class="ml-10">
                            <template #content>
                                <div class="help-icon"></div>
                            </template>
                            <template #tips>
                                <div>根据进销存的单据类型对应的凭证模板</div>
                                <div>
                                    1、智能匹配六大往来（应收账款、其他应收款、预收账款、应付账款、其他应付款和预付账款）的客户/供应商
                                </div>
                                <div>2、自动新增对应的往来单位</div>
                            </template>
                        </BubbleTip>
                    </template>
                </GenerateVoucher>
            </template>
        </ContentSlider>
    </div>

    <GenerateVoucherSettingDialog :settingType="scmProductSettingsType" ref="generateVoucherSettingDialogRef"
        v-model="voucherSettingDialogShow" />
</template>

<script lang="ts">
export default {
    name: "ScmVoucher",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import ReceiptsView from "./components/Receipts.vue";
import VoucherView from "./components/Voucher.vue";
import DifferenceView from "./components/Difference.vue";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { useLoading } from "@/hooks/useLoading";
import { closeCurrentTab, getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { isLemonClient } from "@/util/lmClient";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { ref, reactive, computed } from "vue";
import { onMounted, watch , onActivated, toRef} from "vue";
import GenerateVoucher from "@/components/GenerateVoucher/index.vue";
import {
    BillDocumentModel,
    BillQueryParameters,
    BillWithVoucherModel,
    type IBatchGenerateVoucherModel,
    type IBillGenVoucherResult,
} from "@/components/GenerateVoucher/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import BubbleTip from "@/components/BubbleTip/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import GenerateVoucherSettingDialog from "@/components/Dialog/GenerateVoucherSetting/index.vue";
import { getBillSettingsType } from '@/components/Dialog/GenerateVoucherSetting/util'
import type { Option } from "@/components/SelectCheckbox/types";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { editConfirm } from "@/util/editConfirm";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const generateVoucherSettingDialogRef = ref<InstanceType<typeof GenerateVoucherSettingDialog>>();
const voucherSettingDialogShow = ref(false);
const _ = getGlobalLodash()
const accountSubjectStore = useAccountSubjectStore();

const tabName = ref("receipts");
const voucherViewRef = ref();

const isLoaded = reactive({
    receipts: true,
    voucher: false,
    difference: false,
});

const slots = ["main", "genVoucher", "voucher"];
const currentSlot = ref("main");
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const route = useRoute();
const currentPath = ref(route.path);
const scmAsid = ref(0);
const scmProductType = ref(0);
const scmAsName = ref("");
const cstId = ref(0);
const generateVoucherRef = ref<InstanceType<typeof GenerateVoucher>>();
const genVoucherQueryParameters = ref(new BillQueryParameters());
const genVoucherChanged = ref(false);
const baseVoucherChanged = ref(false);
const billRootType = ref("1010");
const scmProductSettingsType = computed(() => {
    return getBillSettingsType(Number(billRootType.value));
});
const setBillRootType = (type: string) => {
    billRootType.value = type;
};

const genVoucherErrorTableColumns = ref<IColumnProps[]>([
    {
        label: "单据编号",
        prop: "billNo",
        align: "left",
        headerAlign: "left",
        minWidth: 120,
    },
    {
        label: "单据类型",
        prop: "billTypeText",
        align: "left",
        headerAlign: "left",
        minWidth: 90,
    },
    {
        label: "金额",
        prop: "totalAmount",
        align: "right",
        headerAlign: "right",
        minWidth: 90,
        formatter: (row: BillDocumentModel): any => {
            return row.totalAmount.toFixed(2);
        },
    },
    {
        slot: "errorInfo",
    },
]);
const receiptsViewRef = ref<InstanceType<typeof ReceiptsView>>();

function handleTabClick(tab: any) {
    const { name } = tab.props;
    if (!(isLoaded as any)[name]) {
        (isLoaded as any)[name] = true;
    }

    if (name === "voucher") {
        voucherViewRef.value?.getBillAnalysisData();
    }
}

function checkIsRelation() {
    return request({
        url: `/api/ScmRelation`,
    });
}

function checkHasScmSettings() {
    request({
        url: `/api/ScmSettings/IsSet?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data === true) {
                //
            } else {
                ElConfirm("请先设置进销存核算参数，才能查看进销存凭证", true).then((r: Boolean) => {
                    if (useAccountSetStoreHook().permissions.includes("scmrelation-canedit")) {
                        // closeCurrentTab();
                        globalWindowOpenPage("/Scm/ScmSettings", "进销存核算参数");
                    } else {
                        ElNotify({
                            type: "error",
                            message: "您没有设置进销存核算参数权限！",
                        });
                        closeCurrentTab();
                        globalWindowOpenPage("/Default/Default", "首页");
                    }
                });
            }
        } else if (res.state === 2000) {
            ElNotify({
                type: "error",
                message: res.message,
            });
        } else {
            ElNotify({
                type: "warning",
                message: "出现错误，请稍候刷新页面重试",
            });
        }
    });
}

function initRelationTable() {
    request({
        url: `/api/ScmRelation/Info`,
    }).then((res: any) => {
        scmAsName.value = res.data.scmAsName;
        cstId.value = res.data.cstId;
    });
}

let isGenVoucher = false;
async function openVoucherFromBill(parameters: { billIds: string[]; rootBillType: string }) {
    if (isGenVoucher) {
        ElNotify({
            type: "warning",
            message: "正在生成凭证，请稍候",
        });
        return;
    }
    isGenVoucher = true;
    if (parameters.billIds.length === 0) {
        isGenVoucher = false;
        ElNotify({
            type: "warning",
            message: "请选择单据后生成凭证",
        });
        return;
    }

    // 从缓存中读取凭证合并的选项 
    const genVoucherCache = window.localStorage.getItem('genVoucher-scm');
    if(genVoucherCache  && selectedList.value.length === 0) {
        let parseObject = JSON.parse(genVoucherCache) as BillQueryParameters;
        Object.setPrototypeOf(parseObject, BillQueryParameters.prototype);
        if(parseObject.mergeDate){
            selectedList.value.push(1);
        }
        if(parseObject.mergeOpposite){
            selectedList.value.push(2);
        }
        if(parseObject.mergeBillType){
            selectedList.value.push(4);
        }
        if(parseObject.isMerge && selectedList.value.length === 0){
            selectedList.value.push(0);
        }
        if(parseObject.mergeCredit){
            subjectSelectedList.value.push(32);
        }
        if(parseObject.mergeDebit){
            subjectSelectedList.value.push(16);
        }
        genVoucherQueryParameters.value = parseObject;
    } else {
        genVoucherQueryParameters.value.autoMatch = true;
    }

    useLoading().enterLoading("努力加载中，请稍候...");
    getGenVoucher(parameters);
}
let genVoucherBillIds: string[] = [];
function getGenVoucher(parameters?: { billIds: string[]; rootBillType: string }) {
    if (parameters) {
        genVoucherBillIds = parameters.billIds;
        genVoucherQueryParameters.value.rootBillType = Number(parameters.rootBillType);
    }
    const data = {
        rootType: genVoucherQueryParameters.value.rootBillType,
        autoMatch: genVoucherQueryParameters.value.autoMatch,
        billIds: genVoucherBillIds,
        voucherSettings: generateVoucherSettingDialogRef.value?.getVoucherSetting(),
    };
    request({
        url: `/api/BillVoucher/BatchGenerateVoucher`,
        method: "post",
        data: data,
        headers: {
            scmProductType: scmProductType.value,
            scmAsid: scmAsid.value,
            "Content-Type": "application/json"
        },
    }).then((res: any) => {
        useLoading().quitLoading();
        if (res.state !== 1000) {
            isGenVoucher = false;
            loadSuccess.value = true;
            ElNotify({
                type: "warning",
                message: res.msg,
            });
            return;
        }
        const timer = setTimeout(() => {
            isGenVoucher = false;
            clearTimeout(timer);
        }, 1000);
        currentSlot.value = "genVoucher";
        const data = res as IResponseModel<IBatchGenerateVoucherModel<BillWithVoucherModel>>;
        data.data.documentWithVoucherList.forEach((item) => {
            item.voucherLines.forEach((line:any)=>{
                line.asubAAName = line.asubAAName===" "? "" : line.asubAAName;
            })
        });
        if(genVoucherQueryParameters.value.isMerge && !checkMergeVoucher(data.data)){
            genVoucherQueryParameters.value.isMerge = false;
        }    
        genVoucherChanged.value = false;
        baseVoucherChanged.value = false;
        generateVoucherRef.value?.loadDocumentList(data.data, generateVoucherSettingDialogRef.value?.getVoucherSetting());
    }).catch(() => {
        isGenVoucher = false;
        useLoading().quitLoading();
        ElNotify({
            type: "warning",
            message: "出现错误，请稍候刷新页面重试",
        });
    })
}
function genVoucherChangedHandle() {
    genVoucherChanged.value = true;
}

const genVoucher = _.debounce(saveGenVoucher, 500);

let isSaving = false;
async function saveGenVoucher() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) {
        ElNotify({ message: "正在保存，请稍后！", type: "warning" });
        return;
    }
    isSaving = true;
    const parameters = generateVoucherRef.value?.getGenVoucherParameters();
    if (parameters) {
        useLoading().enterLoading("努力加载中，请稍候...");
        request({
            url: "/api/BillVoucher/SaveBillVouchers",
            method: "post",
            data: parameters,
            headers: {
                scmProductType: scmProductType.value,
                scmAsid: scmAsid.value,
            },
        }).then((res: IResponseModel<IBillGenVoucherResult>) => {
            useLoading().quitLoading();
            const data = res;
            if (data.state === 1000) {
                dispatchReloadAsubAmountEvent();
                accountSubjectStore.getAccountSubject();
                useAssistingAccountingStore().getAssistingAccounting();
                generateVoucherRef.value?.loadSaveResult(data.data);
                receiptsViewRef.value?.clearSelection();
                const timer = setTimeout(() => {
                    isSaving = false;
                    clearTimeout(timer);
                }, 1000);
            }else{
                isSaving = false;
                ElNotify({
                    type: "error",
                    message: data.msg,
                });
            }
        }).catch((err: any) => {
            if (err.code !== "ERR_NETWORK") {
                isSaving = false;
                useLoading().quitLoading();
            }
        });
    }else{
        isSaving = false;
    }
}
function genVoucherSaveSuccess() {
    currentSlot.value = "main";
    receiptsViewRef.value?.getScmBillList();
}

// 生成凭证设置
const showJournalSettings = () => {
    voucherSettingDialogShow.value = true;
};

const loadSuccess = ref(true);
const options = ref<Array<Option>>([
    {
        id: 0,
        name: "合并成一张凭证",
    },
    {
        id: 1,
        name: "按单据日期分开合并",
    },
    {
        id: 2,
        name: "按客户/供应商分开合并",
    },
    {
        id: 4,
        name: "按单据类型分开合并",
    },
]);
const subjectOption = ref<Array<Option>>([
    {
        id: 16,
        name: "相同借方科目合并",
    },
    {
        id: 32,
        name: "相同贷方科目合并",
    },
]);
const selectedList = ref<number[]>([]);
const subjectSelectedList = ref<number[]>([]);
const backSelectedList = ref<number[]>([]);
const selectSubjectRef = ref();
const selectZero = ref(true);

function changeSelectedList(val: number[]) {
    if(!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    if(selectZero.value && val.length > 1 && val[0] === 0 ){
        val.splice(0, 1);
        selectZero.value = false;       
    } else if(val.findIndex(z=>z === 0) !== -1){
        selectZero.value = true;
        val.splice(0);
        val.push(0);
    }

    backSelectedList.value = _.cloneDeep(selectedList.value);
    selectedList.value = val;
    genVoucherQueryParameters.value.mergeDate = val.includes(1) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeOpposite = val.includes(2) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeBillType = val.includes(4) && genVoucherQueryParameters.value.isMerge;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(1, selectedList);
}

function mergeVoucher(){
    if(!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if(genVoucherQueryParameters.value.isMerge){
        if(!generateVoucherRef.value?.checkMergeVoucher()){
            genVoucherQueryParameters.value.isMerge = false;
            return;
        }
    }

    if(genVoucherQueryParameters.value.isMerge){
        genVoucherQueryParameters.value.mergeOthers = true;
        if(selectedList.value.length === 0 && subjectSelectedList.value.length === 0){
            selectedList.value.push(0);
            subjectSelectedList.value.push(16);
            subjectSelectedList.value.push(32);
            selectSubjectRef.value?.changeSelectAll(true);
            genVoucherQueryParameters.value.mergeCredit = true;
            genVoucherQueryParameters.value.mergeDebit = true;
        }
    }
    loadSuccess.value = false;
    if(genVoucherQueryParameters.value.isMerge){
        if(genVoucherChanged.value){
            baseVoucherChanged.value = true;
            genVoucherChanged.value = false;
        }

        genVoucherCheckboxChanged(1, selectedList);
    } else {
        genVoucherCheckboxChanged(0);
    }
}

// 如果获取的数据里有错误数据，就不可以合并凭证
const checkMergeVoucher = (data: IBatchGenerateVoucherModel<BillWithVoucherModel>) => {
    if (data) {
        for (let i = 0; i < data.documentWithVoucherList.length; i++) {
            if (data.documentWithVoucherList[i].document.errorInfo !== "") {
                ElNotify({ message: "亲，生成的凭证存在错误暂不支持合并，请依据错误提示修改凭证后再进行合并哦！", type: "warning" });
                return false;
            }
        }
    }
    return true;
};

function checkBoxShowChange(val: boolean) {
    if (!val && selectedList.value.length === 0 && genVoucherQueryParameters.value.isMerge) {
        ElNotify({ message: "凭证合并默认需要勾选一个条件哦~", type: "warning" });
        genVoucherQueryParameters.value.isMerge = true;
        selectedList.value.push(0);
        selectZero.value = true;
        genVoucherCheckboxChanged(1, selectedList);
    }
}

function changeSubjectSelectedList(val: number[]) {
    if(!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    backSelectedList.value = _.cloneDeep(subjectSelectedList.value);
    subjectSelectedList.value = val;
    genVoucherQueryParameters.value.mergeDebit = val.includes(16) && genVoucherQueryParameters.value.mergeOthers;
    genVoucherQueryParameters.value.mergeCredit = val.includes(32) && genVoucherQueryParameters.value.mergeOthers;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(2, subjectSelectedList);
}

function genVoucherCheckboxChanged(flag: number, selectedList?: any) {
    new Promise<boolean>((resolve) => {
        if (genVoucherChanged.value || baseVoucherChanged.value && flag === 3) {
            ElConfirm("系统可能不会保存您做的更改，确定要切换吗？").then((r) => {
                if (r) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(true);
        }
    }).then((r) => {
        if (r) {
            if (flag > 0 && selectedList!==undefined ) {
                genVoucherChanged.value = false;
                generateVoucherRef.value?.loadDocumentList(undefined, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                window.localStorage.setItem('genVoucher-scm', JSON.stringify(genVoucherQueryParameters.value));
            } else {
                if(flag === 3){
                    window.localStorage.setItem('genVoucher-scm', JSON.stringify(genVoucherQueryParameters.value));
                    getGenVoucher();
                } else {
                    generateVoucherRef.value?.loadDocumentList(undefined, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                    genVoucherChanged.value = false; 
                }
            }
        } else if(selectedList != undefined) {
            selectedList.value = _.cloneDeep(backSelectedList.value);
            loadSuccess.value = true;
        } else if(flag === 3) {
            genVoucherQueryParameters.value.autoMatch = !genVoucherQueryParameters.value.autoMatch;
            loadSuccess.value = true;
        } else {
            genVoucherQueryParameters.value.isMerge = !genVoucherQueryParameters.value.isMerge;
            loadSuccess.value = true;
        }
    });
}

function mergeErrorFunction() {
    genVoucherQueryParameters.value.mergeCredit = false;
    genVoucherQueryParameters.value.mergeDebit = false;
    subjectSelectedList.value = [];
}

const isEditting = computed(() => {
    return currentSlot.value === "genVoucher";
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
onMounted(() => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop + "" !== "undefined") {
        setTimeout(() => {
            delete (currentRouterModel as any).stop;
        });
    }
    Promise.all([checkIsRelation()]).then((res: any) => {
        if (res[0].data.isRelation) {
            scmAsid.value = res[0].data.scmAsid;
            scmProductType.value = res[0].data.scmProductType;
            checkHasScmSettings();
            initRelationTable();
        } else {
            if (!useAccountSetStoreHook().permissions.includes("scmrelation-canedit")) {
                ElConfirm("请先关联进销存账套，才能进行后续操作").then((r: Boolean) => {
                    if (r) {
                        closeCurrentTab();
                        globalWindowOpenPage("/Scm/ScmSettings", "进销存核算参数");
                    }
                });
            } else {
                ElConfirm("请联系账套管理员关联进销存账套，才能进行后续操作");
            }
        }
    });
});
let firstInit = true;
let cacheRouterQueryParams: any = null;
const isFromOtherPage = (page: "voucherList"): boolean => {
    if (!cacheRouterQueryParams?.from && route.query.from === page) {
        return true;
    }
    if (
        cacheRouterQueryParams?.from === page &&
        route.query?.from === page &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }

    return false;
};
onBeforeRouteLeave((to, from, next) => {
    firstInit = false;
    cacheRouterQueryParams = from.query;
    next();
});
const reLoadCurrentPage = () => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if (currentRouterModel.stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        currentRouterModel.stop = true;
    }
};

onActivated(() => {
    if(scmAsid.value) {
        checkHasScmSettings();
        initRelationTable();
    }
    if (isFromOtherPage("voucherList") && !firstInit) {
        if (isEditting.value) {
            // 正在编辑票据页面
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
    }
});
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";

:deep(.el-tabs) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .el-tabs__content {
        background-color: var(--white);
        flex: 1;
    }
    .el-tab-pane {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
}

:deep(.el-tabs__header) {
    background-color: white;
}
</style>
