import { ref } from "vue";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import store from "@/store";
import type { IResponseModel } from "@/util/service";
import { getCurrencyApi, type ICurrency } from "@/api/currency";

export const useCurrencyStore = defineStore("currency", () => {
    const fcList = ref<ICurrency[]>([]);
    const fcListOptions = ref<{ id: number; label: string; code: string}[]>([]);
    const isLoaded = ref(false); // 跟踪数据是否已加载

    const getCurrencyList = async () => {
        // 如果数据已经加载，直接返回
        if (isLoaded.value) {
            return fcList.value;
        }
        const globalToken = getGlobalToken();
        if (!globalToken) {
            throw new Error("Token 为空");
        }
        try {
            const res: IResponseModel<ICurrency[]> = await getCurrencyApi();
            if (res.state === 1000) {
                const defaultList = [{ id: -1, label: "综合本位币", code: "" }];
                fcListOptions.value = [
                    ...defaultList,
                    ...res.data.map(item => ({ id: item.id, label: item.name, code: item.code }))
                ];
                fcList.value = res.data;
                isLoaded.value = true; // 标记数据已加载
                return res.data; // 返回数据
            } else {
                throw new Error(res.msg); // 抛出错误
            }
        } catch (error) {
            throw new Error(error instanceof Error ? error.message : "请求失败"); // 处理错误
        }
    };

    // 重置加载状态
    const resetCurrencyList = () => {
        isLoaded.value = false; // 重置加载状态
        fcList.value = []; // 清空数据
        fcListOptions.value = []; // 清空选项
    };

    return { fcList, getCurrencyList, fcListOptions, resetCurrencyList  };
});

/** 在setup外使用 */
export function useCurrencyStoreHook() {
    return useCurrencyStore(store);
}
