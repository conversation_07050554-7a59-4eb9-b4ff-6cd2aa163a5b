import { VoucherEntryModel } from "@/components/Voucher/types";
import type { RequestCustomDesc } from "./utils";

export interface IBusinessVoucherTemplate {
    asId: number;
    id: number;
    code: string;
    name: string;
    vtType: number;
    vtTypeText: string;
    vgId: number;
    isDefault: boolean;
    note: string;
    lines: Line[];
    onlyType: string; // onlyType = vtType + "_" + customType
    customType: number;
    parentVtTypeText: string;
}

export interface Line {
    description: string;
    asubId: number;
    asubInfo: string;
    direction: number;
    amountCalcWay: number;
    amountCalcWayText: string;
}

class VoucherLine extends VoucherEntryModel {
    logicEnum = 0;
    valueType = 1010;
    customDescriptions: Array<RequestCustomDesc> | null = null;
}

export class VoucherTemplate {
    constructor() {
        this.voucherLines = new Array<VoucherLine>();
        this.voucherLines.push(new VoucherLine());
        this.voucherLines.push(new VoucherLine());
    }
    vtId = 0;
    vtName = "";
    vtType = 0;
    vgId = 1010;
    note = "";
    businessType = 0;
    code = "";
    isDefault = false;
    voucherLines: Array<VoucherLine>;
}

export class NewVoucherTemplate extends VoucherTemplate {}

export class EditVoucherTemplate extends VoucherTemplate {}

export interface IEditVoucehrTemplateBack {
    asId: number;
    businessType: number;
    businessTypeCode: string;
    businessTypeName: string;
    note: string;
    vgId: number;
    vgName: string;
    vtCode: string;
    vtId: number;
    vtName: string;
    vtType: number;
    voucherLines: Array<VoucherLine>;
}

export enum ValueType {
    Amount = 1010,
    NoTaxAmount = 1020,
    TaxAmount = 1030,
    TotalAmount = 1040,
    InAmount = 1050,
    OutAmount = 1060,
    AdjustAmount = 1070,
    WriteOffAmount = 1100,
    IncomeAmount = 1110,
    ExpenseAmount = 1120,
    DiscountAmount = 2010,
    DiscountedAmount = 2020,
    CustomerCost = 2030,
    AssemblyCost = 2040,
    DisassemblyCost = 2050,
}

export interface IAmountCalcItem {
    value: ValueType;
    text: string;
}

export interface IVtTypeAmountCalcWayPairs {
    [key: number]: Array<IAmountCalcItem>;
}

export interface IBusinessType {
    vtType: number;
    vtTypeName: string;
}
export interface IScmVoucherTemplateList {
    vtId: number;
    vtName: string;
    vtType: number;
    vtTypeName: string;
    vgId: number;
    voucherLines: any;
    businessTypes: null | Array<IBusinessType>;
}

export class BusinessVoucherTemplateLine {
    constructor(line: VoucherTemplateLine) {
        const { description, customDescriptions, asubId, asubName, direction, valueType } = line;
        const desc = customDescriptions.length ? "" : description || "";
        this.description = desc.replace(/\n/g, "").replace(/\\/g, "\\\\").replace(/\t/g, "").trim();
        this.customDescriptions = customDescriptions.map((desc) => {
            return {
                ...desc,
                description: desc.isCustom ? "" : desc.description,
            };
        });
        this.asubId = asubId || 0;
        this.asubName = asubName || "";
        this.debit = direction === 1 ? 1 : 0;
        this.credit = direction === 2 ? 1 : 0;
        this.valueType = valueType;
    }
    description = "";
    asubId = 0;
    asubName = "";
    debit = 0;
    credit = 0;
    quantityAccounting = 0;
    quantity = 0;
    price = 0;
    foreigncurrency = 0;
    fcId = 1;
    fcRate = 0;
    fcAmount = 0;
    assistingAccounting = 0;
    aacode = "";
    logicEnum = 0;
    valueType = 0;
    customDescriptions: Array<RequestCustomDesc> = [];
}

export class VoucherTemplateLine {
    constructor(index: number, direction: number, valueType: number) {
        this.index = index;
        this.direction = direction;
        this.valueType = valueType;
    }
    asubId = 0;
    asubName = "";
    description = "";
    direction = 1;
    index = 0;
    descriptionInput = false;
    asubIdSelect = false;
    valueType = 0;
    displayDescription = "";
    customDescriptions: Array<RequestCustomDesc> = [];
}

export enum AsubSelectorType {
    Relate = 1,
    System = 2,
}

interface ISelect {
    label: string;
    value: number;
}

export interface IBusinessSelect extends ISelect {
    businessTypes: Array<IBusinessType> | null;
}

export interface IAsubSelect extends ISelect {
    pinyinArr: Array<string>;
    type: AsubSelectorType;
    active: boolean;
    displayLabel: string;
}

export interface ICalSelect {
    text: string;
    value: number;
}
export interface ITemplateAsubList {
    code: number;
    name: string;
}

export interface ICheckBalanceResult {
    description: string;
    asubName: string;
    debit: number;
    credit: number;
}

export interface IModuleTree {
    id: string;
    text: string;
    children: null | Array<IModuleTree>;
    attributes: null | { VtType: number; BusinessId: number };
}
