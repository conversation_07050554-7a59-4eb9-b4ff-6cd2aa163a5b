export interface IJournalDate {
    searchStart: string;
    searchEnd: string;
    checkDate: string;
    startDate: string;
}

export interface IIETypeItem {
    // id
    subkey: string;
    // 编码
    value1: string;
    // 名称
    value2: string;
    // 关键词
    value3: string;
    // 父级id
    subsubkey: string;
    // 是否有子级；1有，0无
    haschild: number;
    //是否启用；0启用，1禁用
    num3: string;
}

export interface ICDAccountItem {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
    standard: string;
    fc_rate: string;
}
