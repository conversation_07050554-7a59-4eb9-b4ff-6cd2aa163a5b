import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoneyWithZero } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const setColumns = (showDetail: boolean, columns: Array<IColumnProps> | undefined) => {
    const setModule = "SalarySummary";
    columns = [
        { 
            label: "部门", 
            prop: "departmentName", 
            align: "center", 
            headerAlign: "center", 
            minWidth: 100, 
            width: getColumnWidth(setModule, 'departmentName'), 
            className: "detailFirst"
        },
        { 
            label: "人数", 
            prop: "emcount", 
            align: "center", 
            headerAlign: "center", 
            minWidth: 88,
            width: getColumnWidth(setModule, 'emcount')
        },
        { 
            label: "应发工资", 
            prop: "grossPay", 
            align: "right", 
            headerAlign: "right", 
            minWidth: 120,
            width: getColumnWidth(setModule, 'grossPay'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { 
            label: "代扣个人款项", 
            prop: "sspamount", 
            align: "right", 
            headerAlign: "right", 
            minWidth: 120,
            width: getColumnWidth(setModule, 'sspamount'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { 
            label: "公司承担款项", 
            prop: "sscamount", 
            align: "right", 
            headerAlign: "right", 
            minWidth: 120,
            width: getColumnWidth(setModule, 'sscamount'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { 
            label: "实发工资", 
            prop: "netSalary", 
            align: "right", 
            headerAlign: "right", 
            minWidth: 120,
            width: getColumnWidth(setModule, 'netSalary'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { 
            label: "员工成本", 
            prop: "totalCost", 
            align: "right",
            headerAlign: "right", 
            minWidth: 120,
            width: getColumnWidth(setModule, 'totalCost'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
    ];
    if (showDetail) {
        columns.splice(1, 1, {
            label: "姓名", 
            prop: "ename", 
            align: "center", 
            headerAlign: "center", 
            minWidth: 88,
            width: getColumnWidth(setModule, 'ename'),
        });
    }
    return columns;
};
