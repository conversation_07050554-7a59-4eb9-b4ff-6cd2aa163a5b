export interface IPeriodItem {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
}

export interface IAccountItem {
    AC_ID: string;
    AC_NAME: string;
}

export interface ITableItem {
    balance: number;
    cdDate: string;
    cdPid: number;
    cdTotal: number;
    cdVid: number;
    cdVoucherNo: any;
    credit: number;
    debit: number;
    description: string;
    expenditure: number;
    hideTotal: number;
    income: number;
    isTotalLine: boolean;
    lineSnName: any;
    pId: number;
    summary: string;
    total: number;
    vId: number;
    voucherDate: string;
    voucherNo: any;
}

export interface ICDAccountItem {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
}
