<template>
    <div class="open-account detail-content">
        <div class="detail-back-button" @click="back">
            <img src="@/assets/Icons/back.png" alt="" />
            <span>返回</span>
        </div>
        <div class="slot-title">预约开户详情</div>
        <div class="open-main-content">
            <div class="step-edit">
                <div class="block-title">开户银行</div>
                <div class="block-main">
                    <el-row class="isRow"><div class="openbank-line">开户银行：招商银行</div> </el-row>
                </div>
                <div class="line"></div>
                <div class="block-title">银行网点信息</div>
                <div class="block-main">
                    <el-form :model="CMBDetailInfo" label-position="right" label-width="150px">
                        <el-row class="isRow">
                            <el-form-item label="网点机构号：">
                                <div class="w-240">{{ CMBDetailInfo.branchNo }}</div>
                            </el-form-item>
                            <el-form-item label="网点名称：">
                                <div class="w-240">{{ CMBDetailInfo.branchName }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="网点地址：">
                                <div class="w-240">{{ CMBDetailInfo.branchAddr }}</div>
                            </el-form-item>
                            <el-form-item label="网点电话：">
                                <div class="w-240">{{ CMBDetailInfo.branchTel }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">业务信息</div>
                <div class="block-main">
                    <el-form :model="CMBDetailInfo" label-position="right" label-width="150px">
                        <el-row class="isRow">
                            <el-form-item label="账户类型：">
                                <div class="w-240">{{ CMBDetailInfo.accountType === 0 ? "没有基本户" : "已有基本户" }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="预约开户币别：">
                                <div class="w-240">{{ getActCcyType(CMBDetailInfo.actCcyTyp) }}</div>
                            </el-form-item>
                            <el-form-item label="预约开户账户类型：">
                                <div class="w-240">{{ getAplActType(CMBDetailInfo.aplActTyp) }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">基本信息</div>
                <div class="block-main">
                    <el-form :model="CMBDetailInfo" label-position="right" label-width="150px">
                        <el-row class="isRow">
                            <el-form-item label="公司名称：">
                                <div class="w-240">{{ CMBDetailInfo.companyName }}</div>
                            </el-form-item>
                            <el-form-item label="统一社会信用代码：">
                                <div class="w-240">{{ CMBDetailInfo.unifiedNumber }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="公司证件类型：">
                                <div class="w-240">{{ getCompanyCertType(CMBDetailInfo.companyCertType) }}</div>
                            </el-form-item>
                            <el-form-item label="公司证件号：">
                                <div class="w-240">{{ CMBDetailInfo.companyCertNo }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">法人信息</div>
                <div class="block-main">
                    <el-form :model="CMBDetailInfo" label-position="right" label-width="150px">
                        <el-row class="isRow">
                            <el-form-item label="是否法人亲自办理：">
                                <div class="w-240">{{ CMBDetailInfo.oprWhtrLgp === "Y" ? "是" : "否" }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人姓名：">
                                <div class="w-240">{{ CMBDetailInfo.legalName }}</div>
                            </el-form-item>
                            <el-form-item label="法人手机号：">
                                <div class="w-240">{{ CMBDetailInfo.legalMobile }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人证件类型：">
                                <div class="w-240">{{ getMbrDocType(getLegalInfo()?.mbrDocTyp || "") }}</div>
                            </el-form-item>
                            <el-form-item label="法人证件号：">
                                <div class="w-240">{{ getLegalInfo()?.mbrDocId || "" }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <template v-if="CMBDetailInfo.oprWhtrLgp !== 'Y'">
                    <div class="line"></div>
                    <div class="block-title">申请人信息</div>
                    <div class="block-main">
                        <el-form :model="CMBDetailInfo" label-position="right" label-width="150px">
                            <el-row class="isRow">
                                <el-form-item label="申请人姓名：">
                                    <div class="w-240">{{ CMBDetailInfo.applicantName }}</div>
                                </el-form-item>
                                <el-form-item label="申请人手机号：">
                                    <div class="w-240">{{ CMBDetailInfo.applicantMobile }}</div>
                                </el-form-item>
                            </el-row>
                            <el-row class="isRow">
                                <el-form-item label="申请人证件类型：">
                                    <div class="w-240">{{ getMbrDocType(getApplyInfo()?.mbrDocTyp || "") }}</div>
                                </el-form-item>
                                <el-form-item label="申请人证件号：">
                                    <div class="w-240">{{ getApplyInfo()?.mbrDocId || "" }}</div>
                                </el-form-item>
                            </el-row>
                        </el-form>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { CMBCompanyInfoModel, aplActTypes, companyCertTypes, mbrDocTyps, actCcyTypes } from "../types";

const props = defineProps<{
    data: CMBCompanyInfoModel | undefined;
}>();
const emit = defineEmits<{
    (e: "back"): void;
}>();

const CMBDetailInfo = computed(() => props.data || new CMBCompanyInfoModel());
function getMbrDocType(type: string) {
    return mbrDocTyps.find((item) => item.value === type)?.label || "";
}
function getAplActType(type: string) {
    return aplActTypes.find((item) => item.value === type)?.label || "";
}
function getCompanyCertType(type: string) {
    return companyCertTypes.find((item) => item.value === type)?.label || "";
}
function getActCcyType(type: string) {
    return actCcyTypes.find((item) => item.value === type)?.label || "";
}
function getLegalInfo() {
    return CMBDetailInfo.value.members.find((item) => item.mbrTyp === "1");
}
function getApplyInfo() {
    return CMBDetailInfo.value.members.find((item) => item.mbrTyp === "2");
}

function back() {
    emit("back");
}
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
.open-account .open-main-content .step-edit .block-main .isRow {
    text-align: left;
    padding-left: 150px;
    padding-right: 40px;
    .openbank-line {
        padding-left: 0;
    }
    :deep(.el-form-item__label) {
        justify-content: flex-start;
    }
}
</style>
