<template>
  <div class="form-controls-demo">
    <h2>表单控件示例</h2>

    <div class="form-container">
      <h3>CheckableSelect 组件</h3>

      <div class="demo-section">
        <h4>基本用法</h4>
        <el-form-item label="城市">
          <CheckableSelect
            v-model="form.city"
            :clearable="true"
            placeholder="请选择城市"
            :rule="{ required: true, message: '请选择城市' }">
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </CheckableSelect>
        </el-form-item>
      </div>

      <div class="demo-section">
        <h4>提示型校验 vs 强制型校验</h4>
        <div class="validation-types">
          <div class="validation-type">
            <h5>提示型校验 (type=1)</h5>
            <el-form-item label="选择数量">
              <CheckableSelect
                v-model="form.promptCount"
                placeholder="请选择数量"
                clearable
                :rule="{
                  validator: (value) => Number(value) <= 5,
                  message: '建议选择不超过5的数量',
                  validateType: 1,
                }">
                <el-option
                  v-for="i in 10"
                  :key="i"
                  :label="`${i}`"
                  :value="i" />
              </CheckableSelect>
            </el-form-item>
          </div>

          <div class="validation-type">
            <h5>强制型校验 (type=2)</h5>
            <el-form-item label="选择数量">
              <CheckableSelect
                clearable
                v-model="form.forceCount"
                placeholder="请选择数量"
                :rule="{
                  validator: (value) => Number(value) <= 3,
                  message: '必须选择不超过3的数量',
                  validateType: 2,
                }"
                show-error-message>
                <el-option
                  v-for="i in 10"
                  :key="i"
                  :label="`${i}`"
                  :value="i" />
              </CheckableSelect>
            </el-form-item>
          </div>
        </div>
      </div>

      <h3>CheckableDatePicker 组件</h3>

      <div class="demo-section">
        <h4>基本用法</h4>
        <el-form-item label="日期">
          <CheckableDatePicker
            v-model="form.date"
            type="date"
            placeholder="请选择日期"
            :rule="{ required: true, message: '请选择日期' }"></CheckableDatePicker>
        </el-form-item>
      </div>

      <div class="demo-section">
        <h4>提示型校验 vs 强制型校验</h4>
        <div class="validation-types">
          <div class="validation-type">
            <h5>提示型校验 (type=1)</h5>
            <el-form-item label="选择日期">
              <CheckableDatePicker
                v-model="form.promptDate"
                type="date"
                placeholder="请选择日期"
                :rule="{
                  required: true,
                  message: '建议选择今天或之后的日期',
                  validateType: 1,
                }"></CheckableDatePicker>
            </el-form-item>
          </div>

          <div class="validation-type">
            <h5>强制型校验 (type=2)</h5>
            <el-form-item label="选择日期">
              <CheckableDatePicker
                v-model="form.forceDate"
                type="date"
                placeholder="请选择日期"
                :rule="{
                  required: true,
                  message: '必须选择',
                  validateType: 2,
                }"
                show-error-message></CheckableDatePicker>
            </el-form-item>
          </div>
        </div>
      </div>

      <h3>CheckableInput 组件</h3>

      <div class="demo-section">
        <h4>基本用法</h4>
        <el-form-item label="姓名">
          <CheckableInput
            v-model="form.name"
            placeholder="请输入姓名"
            :rule="{ required: true, message: '请输入姓名www' }"></CheckableInput>
        </el-form-item>
      </div>

      <div class="form-actions">
        <el-button
          type="primary"
          @click="validateForm">
          验证表单
        </el-button>
        <el-button @click="resetForm">重置表单</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from "vue"
  import CheckableSelect from "./CheckableSelect.vue"
  import CheckableDatePicker from "./CheckableDatePicker.vue"
  import CheckableInput from "./CheckableInput.vue"
  import { ElNotify } from "@/utils/notify"
  import { ElButton, ElFormItem, ElOption } from "element-plus"

  // 表单数据
  const form = ref({
    city: "",
    hobbies: [],
    name: "",
    promptCount: 7, // 默认值超过5，触发提示型校验
    forceCount: 5, // 默认值超过3，触发强制型校验
    department: "hr",
    date: "",
    promptDate: "", // 用于提示型校验示例
    forceDate: new Date().toISOString().split("T")[0], // 默认为今天，用于强制型校验示例
  })

  // 城市选项
  const cityOptions = [
    { label: "北京", value: "beijing" },
    { label: "上海", value: "shanghai" },
    { label: "广州", value: "guangzhou" },
    { label: "深圳", value: "shenzhen" },
  ]

  // 行数据（包含校验结果）
  const rowData = ref({
    checkResult: {
      departmentCheck: false,
    },
    departmentCheck: 1, // 1: 提示型校验, 2: 强制型校验
  })

  // 验证表单
  const validateForm = () => {
    ElNotify({
      type: "success",
      message: "表单验证成功",
      title: "成功",
    })
    console.log("表单数据:", form.value)
  }

  // 重置表单
  const resetForm = () => {
    form.value = {
      city: "",
      hobbies: [],
      name: "",
      promptCount: 7,
      forceCount: 5,
      department: "hr",
      date: "",
      promptDate: "",
      forceDate: new Date().toISOString().split("T")[0], // 默认为今天
    }
    rowData.value.checkResult.departmentCheck = false
    rowData.value.departmentCheck = 1
  }
</script>

<style lang="scss" scoped>
  .form-controls-demo {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;

    h2 {
      text-align: center;
      margin-bottom: 30px;
    }

    h3 {
      margin-top: 20px;
      margin-bottom: 15px;
      font-size: 18px;
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }

    h4 {
      margin-top: 15px;
      margin-bottom: 10px;
      font-size: 16px;
      color: #333;
    }

    h5 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 14px;
      color: #333;
    }

    .form-container {
      background-color: #fff;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .demo-section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px dashed #eee;

      &:last-child {
        border-bottom: none;
      }
    }

    .form-actions {
      margin-top: 30px;
      display: flex;
      justify-content: center;
      gap: 20px;
    }

    .validation-types {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .validation-type {
        flex: 1;
        padding: 15px;
        border-radius: 4px;
        background-color: #f9f9f9;
      }
    }

    .actions {
      margin-top: 10px;
      display: flex;
      gap: 10px;
    }
  }
</style>
