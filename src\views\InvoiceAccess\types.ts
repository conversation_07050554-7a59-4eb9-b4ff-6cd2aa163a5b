export interface IOutputInvoiceItem {
  normalCount: number
  allCount: number
  invoiceCountText: string
  asId: number
  invoiceType: number
  invoiceTypeText: string
  amount: number
  tax: number
  invoiceTotal: number
}

export interface IInputInvoiceItem {
  invoiceCount: number
  asId: number
  invoiceType: number
  invoiceTypeText: string
  amount: number
  tax: number
  invoiceTotal: number
}

export interface IInvoiceTypeListItem {
  IN_ID: string
  IN_NAME: string
}
// category (integer, optional),
// categoryText (string, optional, read only),
// invoiceTypeText (string, optional),
// count_New (integer, optional),
// amount_New (number, optional),
// tax_New (number, optional),
// count_Old (integer, optional),
// amount_Old (number, optional),
// tax_Old (number, optional)
export interface IInvoiceDataItem {
  category: number
  categoryText: string
  invoiceTypeText: string
  count_New: number
  amount_New: number
  tax_New: number
  count_Old: number
  amount_Old: number
  tax_Old: number
  checkResult?: string // 核对结果
}
// 进项统计表
export interface IDeductionItem {
  deductText: string
  count: number
  amount: number
  tax: number
}
