<template>
  <slot
    :name="column.slot"
    :slotColumn="column"
    v-if="column.slot"></slot>
  <el-table-column
    v-else
    v-bind="newColumn">
    <template v-if="column.children?.length">
      <ColumnItem
        v-for="childrenItem of column.children"
        :column="childrenItem"
        :key="childrenItem.prop">
        <template
          v-for="slotItem in slotsArr"
          #[slotItem]="{ slotColumn }"
          :key="slotItem">
          <slot
            :name="slotItem"
            :slotColumn="slotColumn"></slot>
        </template>
      </ColumnItem>
    </template>
  </el-table-column>
</template>
<script lang="ts" setup>
  import type { IColumnProps } from "./types.ts"

  const props = defineProps({
    column: {
      type: Object as PropType<IColumnProps>,
      default: () => {},
    },
  })

  // 解决v-bind中column多了children属性，控制台报警告
  const newColumn = computed(() => {
    const column = { ...props.column }
    delete column.children
    return column
  })

  const slots = useSlots()
  const slotsArr = Object.keys(slots)
</script>
<style lang="scss" scoped></style>
