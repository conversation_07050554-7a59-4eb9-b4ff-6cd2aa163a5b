<script lang="ts" setup>
import { useSlots, type PropType, inject, computed, ref, watch } from "vue";
import type { FilterOrder, IColumnProps } from "./IColumnProps";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import { customFilterLimitKey, customFilterOptionKey, customFilterPropsKey } from "../TableHeaderFilter/utils";
import { tableHeaderCustomSort, type SortType } from "./utils";

const props = defineProps({
    column: {
        type: Object as PropType<IColumnProps>,
        default: () => {},
    },
});

const slots = useSlots();
const slotsArr = Object.keys(slots);

const emit = defineEmits<{
    (e: "sort", prop: string, sortType: SortType): void;
    (e: "filter-search", prop: string, value: any, filterOrder: FilterOrder): void;
}>();
function getSortCommon(prop = "", event: MouseEvent) {
    const sortType = tableHeaderCustomSort(event);
    if (sortType === false) return;
    emit("sort", prop, sortType);
}
const lastSelectedList = ref<Array<any>>([]);
function handleFilterSearch(prop: string, value: any) {
    let emitValue: any = value;
    const filterOrder = props.column.filterOrder || "";
    isFilter.value = true;
    if (filterOrder === "multiSelect") {
        lastSelectedList.value = JSON.parse(JSON.stringify(value));
        isFilter.value = value.length > 0;
        emitValue = value;
    } else if (filterOrder === "text") {
        isFilter.value = !!value;
    } else if (["code", "date", "number"].includes(filterOrder)) {
        isFilter.value = !!(value.s || value.e);
    }
    emit("filter-search", prop, emitValue, props.column.filterOrder || "");
}
const filterOptions = inject(customFilterOptionKey);
const options = computed<Array<any>>(() => {
    const prop = props.column?.alias || props.column?.prop || "";
    return filterOptions?.[prop] || [];
});
const customProps = inject(customFilterPropsKey);
const customProp = computed(() => {
    const prop = props.column?.alias || props.column?.prop || "";
    return customProps?.[prop] || undefined;
});
const isFilter = ref(false);
watch(
    options,
    () => {
        if (!customProp.value) return;
        lastSelectedList.value = props.column.defaultNotSelectAll ? [] : options.value.map((item) => item[customProp.value.id]);
    },
    { immediate: true }
);
const tableHeaderFilterRef = ref<InstanceType<typeof TableHeaderFilter>>();
function resetFilter() {
    if (!props.column.filterOrder) return;
    lastSelectedList.value = props.column.defaultNotSelectAll ? [] : options.value.map((item) => item[customProp.value.id]);
    isFilter.value = false;
    tableHeaderFilterRef.value?.resetFilter();
}
function setCustomFilter(prop: string, filterOrder: FilterOrder, value: any) {
    if (!filterOrder) return;
    if (prop !== props.column.prop) return;
    let filter = false;
    if (filterOrder === "multiSelect") {
        const select = value as Array<any>;
        filter = select.length > 0;
    } else if (filterOrder === "text") {
        const text = value as string;
        filter = !!text.trim();
    } else if (filterOrder === "date" || filterOrder === "number" || filterOrder === "code") {
        const info = value as { s: string; e: string };
        filter = !!(info.s.trim() || info.e.trim());
    }
    isFilter.value = filter;
    if (filterOrder === "multiSelect") {
        lastSelectedList.value = JSON.parse(JSON.stringify(value));
    }
    tableHeaderFilterRef.value?.setCustomFilter(filterOrder, value);
}
const customFilterLimit = inject(customFilterLimitKey);
const { disabledStartDate, disabledEndDate } = customFilterLimit || {};
defineExpose({ resetFilter, setCustomFilter });
</script>

<template>
    <slot :name="column.slot" :slotColumn="column" v-if="column.slot"></slot>
    <el-table-column
        :prop="column.prop"
        :show-overflow-tooltip="!column.children || !column.children.length"
        :label="column.label"
        v-if="!column.slot && column.label && !column.useHtml"
        :formatter="column.formatter"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align"
        :header-align="column.headerAlign"
        :fixed="column.fixed"
        :type="column.type"
        :class-name="column.className"
        :resizable="column.resizable"
    >
        <template v-if="column.filterOrder || column.headerSort" #header>
            <div class="header-operate">
                <span class="text">{{ column.label }}</span>
                <div class="more-operate">
                    <div class="header-caret" @click="getSortCommon(column.prop, $event)"></div>
                    <TableHeaderFilter
                        v-if="column.filterOrder"
                        ref="tableHeaderFilterRef"
                        :use-common-select="true"
                        :prop="column.prop"
                        :filter-order="column.filterOrder"
                        :option="options"
                        :custom-prop="customProp"
                        :has-select-list="lastSelectedList"
                        :clear-last-data="false"
                        :disabledStartDate="disabledStartDate"
                        :disabledEndDate="disabledEndDate"
                        :default-not-select-all="column.defaultNotSelectAll"
                        v-model:is-filter="isFilter"
                        @filter-search="handleFilterSearch"
                    ></TableHeaderFilter>
                </div>
            </div>
        </template>
        <template v-if="column.children && column.children.length > 0">
            <ColumnItem v-for="childrenItem of column.children" :column="childrenItem" :key="childrenItem.prop">
                <template v-for="slotItem in slotsArr" #[slotItem]="{ slotColumn }" :key="slotItem">
                    <slot :name="slotItem" :slotColumn="slotColumn"></slot>
                </template>
            </ColumnItem>
        </template>
    </el-table-column>
    <el-table-column
        :prop="column.prop"
        show-overflow-tooltip
        :label="column.label"
        v-if="!column.slot && column.label && column.useHtml"
        :formatter="column.formatter"
        :width="column.width"
        :min-width="column.minWidth"
        :align="column.align"
        :header-align="column.headerAlign"
        :class-name="column.className"
        :fixed="column.fixed"
        :resizable="column.resizable"
    >
        <template #default="scope">
            <div
                v-html="
                    column.formatter
                        ? column.formatter(scope.row, scope.column, scope.row[column.prop ?? ''], scope.$index)
                        : scope.row[column.prop ?? '']
                "
            ></div>
        </template>
    </el-table-column>
</template>

<style lang="less" scoped>
.header-operate {
    .text {
        flex: 1;
    }
    .more-operate {
        display: flex;
        align-items: center;
    }
}
</style>
