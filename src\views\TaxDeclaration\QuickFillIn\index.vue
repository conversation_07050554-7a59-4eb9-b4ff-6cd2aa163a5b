<!-- 快速填写 -->
<template>
  <div class="main-content">
    <div class="main-content-header">
      <div class="left-content">
        <span class="tax-period">
          税款所属期
          <span class="month">{{ parseInt(currentPeriod.slice(-2)) }}月</span>
          <span class="specific-date">{{ currentTaxInfo.periodStart }} 至 {{ currentTaxInfo.periodEnd }}</span>
        </span>
      </div>
      <div class="right-content">
        <a
          class="button mr-10"
          @click="checkBeforeExit">
          退出
        </a>
        <a class="button mr-10">重置</a>
        <a
          class="button mr-10"
          v-if="currentTaxInfo.declareTypeName !== '增值税及附加税费（一般纳税人适用）'"
          @click="handleSave">
          保存
        </a>
        <a
          class="button mr-10"
          @click="handleCheckTableShow">
          账表核对
          <el-tooltip
            effect="light"
            content="增值税申报表和财务财务数据核对">
            <img
              class="question-icon"
              src="@/assets/Icons/question.png"
              alt="" />
          </el-tooltip>
        </a>
        <a
          class="button solid-button mr-10"
          type="primary"
          @click="fillInReportClick">
          {{ currentTaxInfo.status === 20 ? "查看" : "填写" }}报表
        </a>
        <a class="button solid-button">申报</a>
      </div>
    </div>
    <category-tabs
      class="main-content-body"
      v-model="activeIndex"
      :tab-list="quicklyFillInTabList"
      :showCheck="false"
      :currentTab="currentTaxCategory"
      @tab-click="handleTabClick">
      <template #content>
        <component
          :is="currentTabComponent"
          :currentTaxInfo="currentTaxInfo"
          ref="currentComponentRef" />
      </template>
    </category-tabs>
  </div>
</template>
<script setup lang="ts">
  import CategoryTabs from "../components/CategoryTabs.vue"
  import AddTaxMonth from "./AddTaxMonth.vue"
  import CompIncomeTaxQuarter from "./CompIncomeTaxQuarter.vue"
  import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  import { storeToRefs } from "pinia"
  import { TabObj } from "../components/types"

  const currentSlot = defineModel<string>("current-slot")
  const currentTaxCategory = defineModel<string>("current-tax-category")

  const props = withDefaults(defineProps<{ currentTaxInfo: { [key: string]: any } }>(), {
    currentTaxInfo: () => ({}),
  })

  const taxPeriodStore = useTaxPeriodStore()
  const { currentPeriod } = storeToRefs(taxPeriodStore)

  // TODO 快速填写tab列表(这里数据是动态的，记得按需求处理)
  const quicklyFillInTabList: Array<TabObj> = [
    { title: "增值税及附加税费申报（一般纳税人适用）" },
    { title: "居民企业（查账征收）企业所得税月（季）度申报表" },
  ]
  const activeIndex = computed(() => {
    return quicklyFillInTabList.findIndex((item) => item.title === currentTaxCategory.value)
  })

  // 引用当前组件实例
  const currentComponentRef = ref<InstanceType<typeof CompIncomeTaxQuarter> | InstanceType<typeof AddTaxMonth> | null>(null)

  const currentTabComponent = computed(() => {
    switch (currentTaxCategory.value) {
      case "增值税及附加税费申报（一般纳税人适用）":
        return AddTaxMonth
      case "居民企业（查账征收）企业所得税月（季）度申报表":
        return CompIncomeTaxQuarter
      default:
        return AddTaxMonth
    }
  })

  // 保存数据
  function handleSave() {
    if (currentComponentRef.value && currentTaxCategory.value === "居民企业（查账征收）企业所得税月（季）度申报表") {
      const compRef = currentComponentRef.value as InstanceType<typeof CompIncomeTaxQuarter>
      if (compRef.saveData) {
        compRef.saveData()
      }
    }
  }

  // 退出前检查是否需要保存
  function checkBeforeExit() {
    if (currentComponentRef.value && currentTaxCategory.value === "居民企业（查账征收）企业所得税月（季）度申报表") {
      const compRef = currentComponentRef.value as InstanceType<typeof CompIncomeTaxQuarter>

      // 检查是否有未保存的修改
      if (compRef.checkDataModified) {
        const isCheckingModified = compRef.checkDataModified(() => {
          // 保存或不保存后的回调
          handleExit()
        })

        // 如果没有未保存的修改，checkDataModified会直接执行回调并返回false
        if (!isCheckingModified) {
          // 直接执行退出操作
          handleExit()
        }
        // 如果有未保存的修改，checkDataModified会显示对话框并返回true
        // 此时不执行退出操作，等待用户在对话框中选择
      } else {
        // 组件没有实现checkDataModified方法，直接退出
        handleExit()
      }
    } else {
      // 不是企业所得税组件，直接退出
      handleExit()
    }
  }

  function handleExit() {
    currentSlot.value = "main"
  }

  function handleTabClick(row: TabObj) {
    currentTaxCategory.value = row.title
  }

  // 点击填写报表
  function fillInReportClick() {
    currentSlot.value = "fillIn"
  }

  // 点击账表核对
  function handleCheckTableShow() {
    currentComponentRef.value?.showCheckDialog()
  }
</script>
<style lang="scss" scoped>
  .tax-period {
    font-size: var(--h3);
    .month {
      color: #fa6400;
    }
    .specific-date {
      margin-left: 42px;
      font-size: var(--h4);
    }
  }
  .question-icon {
    margin-left: 5px;
    margin-top: -9px;
    width: 12px;
    height: 12px;
  }
</style>
