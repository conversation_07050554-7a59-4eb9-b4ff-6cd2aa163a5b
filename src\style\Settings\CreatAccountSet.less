:deep(.el-radio.el-radio--large) {
    height: 32px;
}
#startYear {
    & :deep(.el-input__wrapper) {
        padding: 1px 6px !important;
    }
}
.create-content {
    width: var(--edit-content-width);
    background-color: var(--white);
    padding-top: 40px;
}
.slot-content-mini {
    width: 1000px;
    padding: 32px 0 0;
    border: 1px solid var(--slot-title-color);
    margin-top: 32px;
    background-color: var(--white);
}

.slot-content-mini,
.create-content {
    overflow: hidden;
    // z-index: 100;
    & .error-msg {
        height: 17px;
        // margin-top: 40px;
        padding-left: 204px;
        color: var(--red);
        font-size: var(--h5);
        line-height: 17px;
        text-align: left;
    }
    & .line-item1 {
        height: 32px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
        margin-top: 4px;
        margin-top: 16px;
        & .line-item-left {
            height: 32px;
            float: left;
            width: 50%;
            & .line-item-field {
                width: 296px;
                text-align: left;
                padding-left: 10px;
                float: right;
                position: relative;
            }
            & .line-item-title {
                float: right;
                text-align: right;
                line-height: 32px;
            }
        }
        & .line-item-right {
            height: 32px;
            float: left;
            width: 50%;
            & .line-item-title {
                width: 156px;
                float: left;
                text-align: right;
                line-height: 32px;
            }
            & .line-item-field {
                float: left;
                text-align: left;
                padding-left: 10px;
                position: relative;
            }
        }
    }
    & .line-item2 {
        height: 32px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
        margin-top: 20px;
        & .line-item-left {
            height: 20px;
            float: left;
            width: 50%;
            & .line-item-field {
                width: 296px;
                float: right;
                text-align: left;
                padding-left: 10px;
                position: relative;
            }
            & .line-item-title {
                float: right;
                text-align: right;
            }
        }
        & .line-item-right {
            height: 20px;
            float: left;
            width: 50%;
            & .line-item-title {
                width: 156px;
                float: left;
                text-align: right;
            }
            & .line-item-field {
                float: left;
                text-align: left;
                padding-left: 10px;
                position: relative;
            }
        }
    }
    & .buttons {
        margin-top: 40px;
        text-align: center;
        & a {
            margin: 0 5px;
        }
    }
    & .report-container {
        background: url(@/assets/Settings/account-set-import-bg2.png) no-repeat -5px 0;
        background-size: 1010px 100px;
        height: 96px;
        margin-top: 27px;
        & .report-container-title {
            float: left;
            margin-top: 25px;
            color: var(--white);
            font-size: 22px;
            line-height: 30px;
            font-weight: 500;
            margin-left: 50px;
        }
        & .report-container-sub-title {
            float: left;
            margin-top: 30px;
            color: var(--white);
            font-size: var(--font-size);
            line-height: 20px;
        }
        & .report-container-btn {
            float: right;
            margin-right: 60px;
            margin-top: 24px;
            border-radius: 16px;
            width: 102px;
            height: 32px;
            background: var(--white);
            cursor: pointer;
            text-align: center;
            color: #1890ff;
            font-size: var(--h5);
            line-height: 32px;
            font-weight: 600;
            position: relative;
            &:hover {
                & .link-qrcode {
                    visibility: visible;
                    opacity: 1;
                }
            }
            & .link-qrcode {
                visibility: hidden;
                opacity: 0;
                transition: 0.2s;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                bottom: 35px;
                padding-bottom: 5px;
                width: 182px;
            }
        }
    }
}

:deep(.el-scrollbar__bar.is-vertical) {
    display: block !important;
}
.create-content-form {
    width: 370px;
    margin: 0 auto;
    .line-item1 {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .line-item-title {
            width: 100px;
        }
    }
}
.image-container {
    width: 18px;
    height: 18px;
    cursor: pointer;
    display: inline-block;
    margin-left: 7px;
    background-image: url('@/assets/Settings/question.png');
    background-size: cover;
}

.image-container:hover{
    background-image: url('@/assets/Settings/question-green.png');
}