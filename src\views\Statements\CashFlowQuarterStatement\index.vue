<template>
    <div :class="['content', { 'narrow-content': !state.currentQuarter }]">
        <div class="title">现金流量表季报</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <PaginationPeriodPicker v-model="state.pid" :isQuarter="true" ref="periodRef"></PaginationPeriodPicker>
                    <div class="mr-10" id="cashFlowPicker"></div>
                    <div :class="['cash-flow-select', state.calmethod === 1 ? 'center' : '']">
                        <el-select
                            id="calmethod"
                            v-model="state.calmethod"
                            @change="calMethodChange"
                            :teleported="false"
                            class="jqtransform mr-10"
                            popper-class="calmethod"
                        >
                            <el-option label="公式法" selected :value="1" class="option">
                                <span>公式法</span>
                            </el-option>
                            <el-option label="辅助核算法" :value="2" class="option">
                                <span>辅助核算法</span>
                            </el-option>
                        </el-select>
                    </div>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <el-checkbox class="mr-10" v-model="state.currentQuarter" label="显示本年所有季度" @change="handleCurrentQuarter"></el-checkbox>
                    <el-checkbox
                        class="mr-10"
                        v-show="accountStandard !== 3"
                        v-model="state.isTax"
                        :label="accountStandard === 1 ? '显示上年累计金额' : '显示上年同期累计金额'"
                        @change="handleIsTax"
                    ></el-checkbox>
                    <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['cashflowquarterstatement-canprint']">
                        <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button mr-10" @click="handleExport" v-permission="['cashflowquarterstatement-canexport']">导出</a>
                    <a class="button" v-if="!isHideBarcode" @click="handleShare" v-permission="['cashflowquarterstatement-canshare']"
                        >微信分享</a
                    >
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div v-if="isErp" class="divider-line"></div>
            <div class="main-center">
                <el-table
                    :class="isErp ? 'erp-table' : ''"
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :data="tableData"
                    :empty-text="emptyText"
                    border
                    fit
                    stripe
                    scrollbar-always-on
                    highlight-current-row
                    class="custom-table"
                    row-key="lineID"
                    :row-class-name="setTitleRowStyle"
                    @header-dragend="headerDragend"
                >
                    <el-table-column 
                        label="项目" 
                        min-width="400px" 
                        align="left" 
                        headerAlign="center"
                        prop="lineName"
                        :width="getColumnWidth(setModule, 'lineName')"
                    >
                        <template #default="scope">
                            <div :class="assertNameClass(scope.row)" :title="scope.row.lineName">
                                {{ scope.row.lineName }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="行次"
                        min-width="60"
                        align="left"
                        header-align="left"
                        prop="lineNumber"
                        :formatter="rowNumberFormatter"
                        :width="getColumnWidth(setModule, 'lineNumber')"
                    >
                    </el-table-column>
                    <el-table-column 
                        label="本年累计金额" 
                        :min-width="state.currentQuarter ? 150 : 250" 
                        align="right" 
                        header-align="right"
                        prop="initalAmount"
                        :width="getColumnWidth(setModule, 'initalAmount')"
                    >
                        <template #default="scope">
                            <TableAmountItem
                                :amount="scope.row.initalAmount.toString()"
                                :formula="calcFormula(scope.row, 0)"
                                :line-number="scope.row.lineNumber"
                                :icon-show="state.calmethod === 1"
                            >
                            </TableAmountItem>
                        </template>
                    </el-table-column>
                    <template v-if="!state.currentQuarter">
                        <el-table-column
                            :label="!state.isTax ? '本季金额' : accountStandard === 1 ? '上年累计金额' : '上年同期累计金额'"
                            min-width="250"
                            align="right"
                            header-align="right"
                            :resizable="false"
                        >
                            <template #default="scope">
                                <!-- <template v-if="state.calmethod === 1 && !hasNumberTitle(scope.row.lineName)"> -->
                                <TableAmountItem
                                    :amount="scope.row.amount.toString()"
                                    :formula="calcFormula(scope.row, 1)"
                                    :line-number="scope.row.lineNumber"
                                    :icon-show="state.calmethod === 1"
                                ></TableAmountItem>
                                <!-- </template> -->
                                <!-- <template v-if="state.calmethod === 2">
                                    <TableAmountItem :amount="scope.row.amount.toString()" :formula="calcFormula(scope.row, 1)"
                                        :line-number="scope.row.lineNumber"></TableAmountItem>
                                </template> -->
                            </template>
                        </el-table-column>
                    </template>
                    <!-- 显示本年所有季度 -->
                    <template v-else>
                        <el-table-column
                            label="第一季度"
                            min-width="120"
                            align="right"
                            header-align="right"
                            :formatter="rowNumberFormatter"
                            prop="qutar1"
                            :width="getColumnWidth(setModule, 'qutar1')"
                        >
                            <template #default="scope">
                                <TableAmountItem
                                    :amount="scope.row.qutar1"
                                    :formula="calcFormula(scope.row, 1)"
                                    :line-number="scope.row.lineNumber"
                                    :icon-show="state.calmethod === 1"
                                ></TableAmountItem>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            label="第二季度" 
                            min-width="120" 
                            align="left" 
                            header-align="right" 
                            :formatter="rowNumberFormatter"
                            prop="qutar2"
                            :width="getColumnWidth(setModule, 'qutar2')"
                        >
                            <template #default="scope">
                                <TableAmountItem
                                    :amount="scope.row.qutar2"
                                    :formula="calcFormula(scope.row, 2)"
                                    :line-number="scope.row.lineNumber"
                                    :icon-show="state.calmethod === 1"
                                ></TableAmountItem>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            label="第三季度" 
                            min-width="120" 
                            align="left" 
                            header-align="right" 
                            :formatter="rowNumberFormatter"
                            prop="qutar3"
                            :width="getColumnWidth(setModule, 'qutar3')"
                        >
                            <template #default="scope">
                                <TableAmountItem
                                    :amount="scope.row.qutar3"
                                    :formula="calcFormula(scope.row, 3)"
                                    :line-number="scope.row.lineNumber"
                                    :icon-show="state.calmethod === 1"
                                ></TableAmountItem> </template
                        ></el-table-column>
                        <el-table-column 
                            label="第四季度" 
                            min-width="120" 
                            align="left" 
                            header-align="right" 
                            :formatter="rowNumberFormatter"
                            :resizable="false"
                        >
                            <template #default="scope">
                                <TableAmountItem
                                    :formula="calcFormula(scope.row, 4)"
                                    :amount="scope.row.qutar4"
                                    :line-number="scope.row.lineNumber"
                                    :icon-show="state.calmethod === 1"
                                ></TableAmountItem> </template
                        ></el-table-column>
                    </template>
                    <!-- 显示本年所有季度 -->
                </el-table>
            </div>
        </div>
    </div>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="现金流量表季报打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "CashFlowQuarterStatement",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { useRoute } from "vue-router";
import usePrint from "@/hooks/usePrint";
import { hasNumberTitle, calcFormula, rowNumberFormatter } from "@/views/Statements/CashFlowStatement/utils";
import { globalPrint, globalExport, getUrlSearchParams } from "@/util/url";
import { share } from "@/views/Statements/utils";
import Dropdown from "@/components/Dropdown/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { useAccountSetStore } from "@/store/modules/accountset";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import type { ICashFlowQuarter } from "./types";
import { onMounted, onUnmounted } from "vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "CashFlowQuarter";
const isErp = ref(window.isErp);

const periodStore = useAccountPeriodStore();
const accountStandard = useAccountSetStore().accountSet?.accountingStandard ?? -1;
const route = useRoute();
const calMethodChange = (v: number) => {
    localStorage.setItem("calmethod", String(v));
    window.dispatchEvent(new CustomEvent("cashFlowCalMethodChange", { detail: { calmethod: v } }));
};
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const state = reactive({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    isTax: false,
    calmethod: Number(localStorage.getItem("calmethod")) || 1,
    isClass: localStorage.getItem("cashclassificationSwitch") === "false" ? false : true,
    isQuarter: true,
    currentQuarter: false, //判断是否显示本年所有季度
});

const tableData = ref<ICashFlowQuarter[]>([]);
let loading = ref(false);
const emptyText = ref(" ");
const getTableList = () => {
    loading.value = true;
    request({
        url: `/api/CashFlowStatement/Quarter`,
        params: {
            PId: state.pid,
            IsTax: state.isTax,
            CalMethod: state.calmethod,
            IsClass: state.isClass,
            isFullYear: state.currentQuarter,
        },
        method: "get",
    })
        // IResponseModel<ICashFlowQuarter[]>
        .then((res: IResponseModel<ICashFlowQuarter[]>) => {
            loading.value = false;
            if (res.state === 1000) {
                tableData.value = [];
                let parent = null;
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children!.push(element);
                        continue;
                    }
                    tableData.value.push(element);
                    if (!tableData.value.length) {
                        emptyText.value = "暂无数据";
                    }
                }
            } else {
                // res.message
                console.log(res.msg);
            }
        })
        .catch((error) => {
            console.log(error);
        });
};
//控制显示本年所有季度为true时上年同期累计金额选择框为false
const handleCurrentQuarter = (val: any) => {
    if (val) {
        state.isTax = false;
    }
    getTableList();
};
//控制显示上年同期累计金额为true时显示本年所有季度选择框为false
const handleIsTax = (val: any) => {
    if (val) {
        state.currentQuarter = false;
    }
    getTableList();
};

const shareReportHost = ref("");
const isHideBarcode = useThirdPartInfoStoreHook().isHideBarcode;

watch([() => state.pid, () => state.calmethod, () => state.isClass], getTableList, { deep: true, immediate: true });

function getDefaultParams() {
    return {
        pid: state.pid,
        isTax: state.isTax,
        calMethod: state.calmethod,
        isClass: state.isClass,
        isFullYear: state.currentQuarter,
    };
}
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "cashFlowStatement",
    `/api/CashFlowStatement/PrintQuarter`,
    {},
    false,
    false,
);

const handleExport = () => {
    globalExport(
        `/api/CashFlowStatement/ExportQuarter?`+ getUrlSearchParams(getDefaultParams())
    );
};

const handleShare = () => {
    const year = periodRef.value?.periodSelectInfo.year.toString();
    const month = periodRef.value?.periodSelectInfo.month ?? 0;
    const quarter = Math.ceil(month / 3).toString();
    request({
        url: "/api/CashFlowStatement/ShareQuarter?" + getUrlSearchParams(getDefaultParams()),
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                shareReportHost.value =
                    window.accountSrvHost +
                    "/api/WxPay/MakeQRCode.ashx?data=" +
                    window.shareReportHost +
                    "/ShareReport/" +
                    res.data +
                    "&CurrentSystemType=1";
                share(shareReportHost.value);
            } else {
                console.log(res.msg);
            }
        })
        .catch((err) => {
            console.log(err);
        });
};

const assertNameClass = (row: ICashFlowQuarter) => {
    let className: string;
    if (row.expand == 1) {
        className = "level2";
    } else if (row.lineNumber == 0 || hasNumberTitle(row.lineName)) {
        className = "level1";
    } else {
        className = "level2";
    }
    return className;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle(data.row.lineName)) {
        return "highlight-title-row";
    }
}

const updateState = () => {
    state.pid = route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod());
    state.calmethod = Number(localStorage.getItem("calmethod")) || 1;
    state.isClass = localStorage.getItem("cashclassificationSwitch") === "false" ? false : true;
};
const updateIsClass = () => {
    state.isClass = localStorage.getItem("cashclassificationSwitch") === "false" ? false : true;
};
onMounted(() => {
    window.addEventListener("cashFlowCalMethodChange", updateState);
    window.addEventListener("cashclassificationSwitchChange", updateIsClass);
});

onUnmounted(() => {
    window.removeEventListener("cashFlowCalMethodChange", updateState);
    window.removeEventListener("cashclassificationSwitchChange", updateIsClass);
});

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/CashFlowStatement.less";
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";
@import "@/style/Statements/Statements.less";
</style>
