import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ref } from "vue";
import { formatMoney } from "@/util/format";
import type { InvoiceDocumentModel } from "@/components/GenerateVoucher/types";
import { formatVoucherTxt } from "@/util/format";
import { request } from "@/util/service";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { checkPermission } from "@/util/permission";
import type { IInvoiceTypeListItem } from "./types";
export const Columns = (
    showAllInfo: boolean,
    type: string,
    isScmRelation: boolean,
    isProjectAccountingEnabled: boolean,
    columns?: Array<IColumnProps> | undefined
) => {
    const setModule = type === "10070" ? "SalesInvoice" : "PurchaseInvoice";
    columns = [
        { slot: "selection", width: 36, headerAlign: "center", align: "center", reserveSelection: true, fixed: "left" },
        { slot: "invoiceType", label: "发票种类", className: "colCellMove" },
        { slot: "entryDateText", label: "录入日期", className: "colCellMove" },
        { slot: "invoiceDateText", label: "开票日期", className: "colCellMove" },
        { slot: "number", label: "发票号码", className: "colCellMove" },
        { slot: "name", label: type === "10070" ? "客户名称" : "供应商名称", className: "colCellMove"},
        { slot: "businessTypeText", label: "业务类型", align: "left", headerAlign: "left", className: "colCellMove" },
        { label: "合计金额", prop: "amountText", align: "right", headerAlign: "right", className: "colCellMove", width: getColumnWidth(setModule, 'amountText') },
        { slot: "taxRate", label: "税率", className: "colCellMove" },
        { label: "合计税额", prop: "taxText", align: "right", headerAlign: "right", className: "colCellMove", width: getColumnWidth(setModule, 'taxText') },
        { label: "价税合计", prop: "adValoremTaxTotals", align: "right", headerAlign: "right", className: "colCellMove", width: getColumnWidth(setModule, 'adValoremTaxTotals') },
        { slot: "vidText", label: "关联凭证", className: "colCellMove" },
        { slot: "invoiceSource", label: "发票来源", className: "colCellMove" },
        { slot: "operation", fixed: "right" },
    ];

    const allColumns: Array<IColumnProps> = [
        { slot: "selection", width: 36, headerAlign: "center", align: "center", reserveSelection: true, fixed: "left" },
        { slot: "invoiceType", label: "发票种类", className: "colCellMove" },
        { slot: "entryDateText", label: "录入日期", className: "colCellMove" },
        { slot: "invoiceDateText", label: "开票日期", className: "colCellMove" },
        { slot: "code", label: "发票代码", className: "colCellMove" },
        { slot: "number", label: "发票号码", className: "colCellMove" },
        { slot: "invoiceStatus", label: "发票状态", className: "colCellMove"},
        { slot: "name", label: type === "10070" ? "客户名称" : "供应商名称", className: "colCellMove"},
        { label: "纳税人识别号", prop: "taxpayerNumber", align: "left", headerAlign: "left", className: "colCellMove", width: getColumnWidth(setModule, 'taxpayerNumber'), minWidth: 130 },
        { label: "地址及电话", prop: "address", align: "left", headerAlign: "left", className: "colCellMove", width: getColumnWidth(setModule, 'address'), minWidth: 130 },
        { label: "开户行及账号", prop: "account", align: "left", headerAlign: "left", className: "colCellMove", width: getColumnWidth(setModule, 'account'), minWidth: 130 },
        { slot: "businessTypeText", label: "业务类型", align: "left", headerAlign: "left", className: "colCellMove" },
        { label: "合计金额", prop: "amountText", align: "right", headerAlign: "right", className: "colCellMove", width: getColumnWidth(setModule, 'amountText') },
        { slot: "taxRate", label: "税率", className: "colCellMove" },
        { label: "合计税额", prop: "taxText", align: "right", headerAlign: "right", className: "colCellMove", width: getColumnWidth(setModule, 'taxText') },
        { label: "价税合计", prop: "adValoremTaxTotals", align: "right", headerAlign: "right", className: "colCellMove", width: getColumnWidth(setModule, 'adValoremTaxTotals') },
        { label: "记账期间", prop: "pidText", align: "left", headerAlign: "left", className: "colCellMove", width: getColumnWidth(setModule, 'pidText') },
        { slot: "vidText", label: "关联凭证", className: "colCellMove" },
        { slot: "invoiceSource", label: "发票来源", className: "colCellMove" },
        { slot: "note", label: "备注", className: "colCellMove"},
        { slot: "operation", fixed: "right" },
    ];
    if (isScmRelation && !window.isErp) {
        allColumns.splice(allColumns.length - 2, 0, { slot: "scmRelation", label: type === "10070" ? "出库单" : "入库单", className: "colCellMove" });
        columns.splice(columns.length - 1, 0, { slot: "scmRelation", label: type === "10070" ? "出库单" : "入库单", className: "colCellMove" });
    }
    if (window.isErp) {
        columns.splice(6, 1);
        allColumns.splice(11, 1);
        columns.splice(11, 0, { slot: "verifiedBills", label: "关联单据", className: "colCellMove" });
        allColumns.splice(17, 0, { slot: "verifiedBills", label: "关联单据", className: "colCellMove" });
    }
    if (isProjectAccountingEnabled) {
        // columns.splice(6,0,{ label:"项目", prop: "projectName", align: "left", headerAlign: "left" });
        // allColumns.splice(8,0,{ label:"项目", prop: "projectName", align: "left", headerAlign: "left"});
        allColumns.splice(8, 0, { slot: "projectName", label:"项目", className: "colCellMove" });
    }
    if (type === "10080") {
        columns.splice(5, 0, {
            label: "认证日期",
            prop: "issueDateText",
            align: "left",
            headerAlign: "left",
            className: "colCellMove",
            width: getColumnWidth(setModule, 'issueDateText'),
            minWidth: 80,
            formatter: (row, column, value) => {
                if (value === "0001-01-01" || value === "0001-1-01") {
                    return "";
                }
                return value;
            },
        });
        allColumns.splice(7, 0, {
            label: "认证日期",
            prop: "issueDateText",
            align: "left",
            headerAlign: "left",
            className: "colCellMove",
            width: getColumnWidth(setModule, 'issueDateText'),
            minWidth: 80,
            formatter: (row, column, value) => {
                if (value === "0001-01-01" || value === "0001-1-01") {
                    return "";
                }
                return value;
            },
        });
    }
    return showAllInfo ? allColumns : columns;
};
export const selectedColumns = (type: string): IColumnProps[] => {
    const setModule = "InvoiceSelectRecord";
    return [
        { slot: "selection", width: 36, headerAlign: "center", align: "center", reserveSelection: true },
        { label: "发票种类", prop: "invoiceTypeText", align: "left", headerAlign: "left", minWidth: 140, width: getColumnWidth(setModule, 'invoiceTypeText') },
        { label: "开票日期", prop: "invoiceDateText", align: "left", headerAlign: "left", minWidth: 85, width: getColumnWidth(setModule, 'invoiceDateText') },
        { label: "发票号码", prop: "number", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'number') },
        { label: type === "10070" ? "客户名称" : "供应商名称", prop: "name", align: "left", headerAlign: "left", minWidth: 140, width: getColumnWidth(setModule, 'name') },
        { label: "业务类型", prop: "businessTypeText", align: "left", headerAlign: "left", minWidth: 80, width: getColumnWidth(setModule, 'businessTypeText') },
        { label: "合计金额", prop: "amountText", align: "right", headerAlign: "right", width: getColumnWidth(setModule, 'amountText') },
        { label: "税率", prop: "taxRate", align: "right", headerAlign: "right", width: getColumnWidth(setModule, 'taxRate') },
        { label: "合计税额", prop: "taxText", align: "right", headerAlign: "right", width: getColumnWidth(setModule, 'taxText') },
        { label: "价税合计", prop: "adValoremTaxTotals", align: "right", headerAlign: "right", width: getColumnWidth(setModule, 'adValoremTaxTotals') },
        {
            label: "关联凭证",
            prop: "adValoremTaxTotals",
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'adValoremTaxTotals'),
            formatter: (row, column, value) => {
                return formatVoucherTxt(row.pidText, row.vidText) || "";
            },
        },
        { label: "发票来源", prop: "invoiceSourceText", align: "left", headerAlign: "left", minWidth: 70, resizable: false },
    ];
};
export const addColumns = ref<Array<IColumnProps>>([
    { slot: "no" },
    { slot: "tradeName" },
    { slot: "specificationModel" },
    { slot: "unit" },
    { slot: "number" },
    { slot: "money" },
    { slot: "taxRate" },
    { slot: "taxAmount" },
    { slot: "taxationItems" },
    { slot: "taxMethod" },
]);
export const genVoucherErrorTableColumns = ref<IColumnProps[]>([
    {
        label: "发票类型",
        prop: "invoiceType",
        align: "left",
        headerAlign: "left",
        minWidth: 120,
    },
    {
        label: "发票号码",
        prop: "invoiceNum",
        align: "left",
        headerAlign: "left",
        minWidth: 110,
    },
    {
        label: "业务类型",
        prop: "businessType",
        align: "left",
        headerAlign: "left",
        minWidth: 90,
    },
    {
        label: "金额",
        prop: "amount",
        align: "left",
        headerAlign: "left",
        minWidth: 90,
        formatter: (row: InvoiceDocumentModel) => {
            return row.amount.toFixed(2);
        },
    },
    {
        label: "税额",
        prop: "tax",
        align: "left",
        headerAlign: "left",
        minWidth: 90,
        formatter: (row: InvoiceDocumentModel) => {
            return row.tax.toFixed(2);
        },
    },
    {
        slot: "errorInfo",
    },
]);
export const checkStartEndDate = (startDate: string, endDate: string) => {
    return new Date(startDate).getTime() > new Date(endDate).getTime();
};
export const isMultipleTaxRates = (taxRate: string) => {
    const restrictedRate = [0, 0.5, 1, 1.5, 3, 4, 5, 6, 9, 10, 11, 13, 16, 17];
    restrictedRate.forEach((item) => {
        restrictedRate.push(item - 0.01);
        restrictedRate.push(item + 0.01);
    });
    const regex = /(\d+(\.\d+)?)/;
    const match = taxRate.match(regex);

    if (match) {
        const number = parseFloat(match[0]);
        return restrictedRate.includes(number) ? false : true;
    }
    return false;
};
export const swapKeysAndValues = (obj: object) => {
    const swapped = Object.entries(obj).map(([key, value]) => [value, key]);
    return Object.fromEntries(swapped);
};
export function containInvalidLabel(str: string): boolean {
    const reg = /[~!@#$*&\\?\";=/\\\n]/;
    if (reg.test(str)) {
        return true;
    }
    return false;
}
export function containDangerousLabel(str: string) {
    const reg = /[<>=]/;
    if (reg.test(str)) {
        return true;
    }
    return false;
}
// 根据传入的InvoiceCategory和InvoiceType来判断调用哪个接口
export const interfaceRoute: any = {
    // 销项
    "10070": {
        // 未开具发票
        "1010": "SalesNotIssuedInvoice",
        // 增值税普通发票
        "1020": "SalesValueAddedTaxInvoice",
        // 增值税专用发票
        "1030": "SalesValueAddedTaxSpecialInvoice",
        // 税控机动车专用发票
        "1040": "SalesTCSIFMV",
        // 纳税检查调整
        "1050": "SalesTaxInspectionAdjustment",
        // 全电普通发票
        "1060": "SalesAllElectricInvoice",
        // 全电专用发票
        "1070": "SalesAllElectricSpecialInvoice",
        //机动车销售统一发票
        "1110": "SalesMotorVehicleSaleInvoice",
        //二手车销售统一发票
        "1090": "SalesUsedVehicleSaleInvoice",
    },
    // 进项
    "10080": {
        // 未开具发票
        "1010": "PurchaseNotIssuedInvoice",
        // 增值税普通发票
        "1020": "PurchaseValueAddedTaxInvoice",
        // 增值税专用发票
        "1030": "PurchaseValueAddedTaxSpecialInvoice",
        // 税控机动车专用发票
        "1040": "PurchaseTCSIFMV",
        // 纳税检查调整
        "1050": "PurchaseTaxInspectionAdjustment",
        // 全电普通发票
        "1060": "PurchaseAllElectricInvoice",
        // 全电专用发票
        "1070": "PurchaseAllElectricSpecialInvoice",
        //机动车销售统一发票
        "1110": "PurchaseMotorVehicleSaleInvoice",
        //二手车销售统一发票
        "1090": "PurchaseUsedVehicleSaleInvoice",
    },
};

export function convertInvoiceTaskRemark(remark: string) {
    let ret = "";
    if (remark.indexOf("取票下载任务失败") >= 0 || remark.indexOf("无法跳转至取票页") >= 0 || remark.indexOf("取票页面加载超时") >= 0) {
        ret = "税局系统繁忙，获取数据超时，请稍等1分钟后重试";
    } else if (
        remark.indexOf("登录已过期") >= 0 ||
        remark.indexOf("登录状态失效") >= 0 ||
        remark.indexOf("任务超时结束") >= 0 ||
        remark.indexOf("Cookie") >= 0
    ) {
        ret = "税局系统登录异常，请稍等1分钟后重试";
    } else if (remark.indexOf("税局查询接口异常") >= 0) {
        ret = "税局系统数据异常，请稍等1分钟后重试";
    } else if (remark.indexOf("数据量过多") >= 0) {
        ret = "税局数据异常，请联系在线客服";
    } else if (remark.indexOf("编码已达上限") >= 0) {
        ret = remark;
    } else if (remark.indexOf("纳税人识别号匹配失败") >= 0) {
        ret = "税局数据异常，请重新取票，执行取票时请勿登录税局";
    } else if (remark.indexOf("发票分析报告生成失败") >= 0) {
        ret = "发票分析报告生成失败，请联系侧边栏客服";
    } else if (remark.indexOf("保存客户异常") >= 0 || remark.indexOf("保存供应商异常") >= 0) {
        ret = "税局数据异常，保存客户、供应商异常，请联系在线客服";
    } else if (remark.indexOf("因税局调整，暂不支持大批量数据取票操作") >= 0) {
        ret = "因税局系统有调整，暂时无法为您完成取票操作。您可以从税局导出发票文件再进行导入，我们会尽快优化取票功能为您提供更好的体验！";
    } else if (remark.indexOf("该地区暂不支持") >= 0) {
        ret = "因电子税局系统发票数据获取异常，当前地区暂时无法获取发票数据，后续将会尽快恢复正常使用"
    }

    if (ret.length === 0) ret = "税局系统繁忙，获取数据异常，请稍等1分钟后重试";
    return ret;
}

export function convertDeductionTaskRemark(remark: string) {
    let ret = "";
    if (remark.indexOf("本功能仅提供给增值税一般纳税人") >= 0) {
        ret = "您非增值税一般纳税人，无法勾选认证";
    } else if (
        remark.indexOf("取票页加载失败") >= 0 ||
        remark.indexOf("Cookie") >= 0 ||
        remark.indexOf("取票失败") >= 0 ||
        remark.indexOf("登录状态失效") >= 0 ||
        remark.indexOf("抵扣勾选校验操作失败") >= 0 ||
        remark.indexOf("下载文件失败") >= 0 ||
        remark.indexOf("下载文件超时") >= 0 ||
        remark.indexOf("执行异常") >= 0 ||
        remark.indexOf("文件失败") >= 0 ||
        remark.indexOf("数据库操作失败") >= 0 ||
        remark.indexOf("数据异常") >= 0
    ) {
        ret = "税局系统勾选认证数据异常，请稍等1分钟后重试";
    } else if (remark.indexOf("税局内部错误") >= 0) {
        ret = "税局系统繁忙，请稍等1分钟后重试";
    } else if (remark.indexOf("纳税人识别号匹配失败") >= 0) {
        ret = "税局数据异常，请重新取票，执行取票时请勿登录税局";
    } else if (remark.indexOf("调用纳税人综合信息为空") >= 0 || remark.indexOf("未查询到纳税人档案") >= 0) {
        ret = "纳税人档案信息有误，请前往税局确认是否可正常执行勾选认证";
    } else if (remark.indexOf("该地区暂不支持") >= 0) {
        ret = "因电子税局系统发票勾选认证异常，当前地区暂时无法获取发票勾选日期，后续将会尽快恢复正常使用"
    }

    if (ret.length === 0) ret = "税局系统勾选认证数据异常，请稍等1分钟后重试";
    return ret;
}

export function convertFileTaskRemark(remark: string) {
    let ret = "";
    if (
        remark.indexOf("税局处理时间过长") >= 0 ||
        remark.indexOf("取票页加载失败") >= 0 ||
        remark.indexOf("附件下载任务处理结果异常") >= 0
    ) {
        ret = "电子税局系统繁忙，发票原件获取超时。建议稍等或税局闲时时段重试"
    } else if (
        remark.indexOf("下载文件失败") >= 0 ||
        remark.indexOf("下载文件超时") >= 0 ||
        remark.indexOf("执行异常") >= 0 ||
        remark.indexOf("税局") >= 0 ||
        remark.indexOf("Cookie") >= 0 ||
        remark.indexOf("登录状态失效") >= 0
    ) {
        ret = "税局系统发票原件数据下载异常，请稍等1分钟后重试";
    } else if (remark.indexOf("数据库操作失败") >= 0 || remark.indexOf("系统操作失败") >= 0 || remark.indexOf("数据异常") >= 0) {
        ret = "税局系统繁忙，请联系在线客服";
    } else if (remark.indexOf("纳税人识别号匹配失败") >= 0) {
        ret = "税局数据异常，请重新取票，执行取票时请勿登录税局";
    } else if (remark.indexOf("该地区暂不支持") >= 0) {
        ret = "因电子税局系统发票原件下载异常，当前地区暂时无法获取发票原件，后续将会尽快恢复正常使用"
    }

    if (ret.length === 0) ret = "税局发票原件数据下载异常，请稍等1分钟后重试";
    return ret;
}

export const InvoiceCategoryList = { 1: 10080, 2: 10070, 3: 0 };
export const CashierCategoryList = { 1: 10060, 2: 10050, 3: 0 };

const formatMonth = (str: string) => {
    const [year, month] = str.split("-");
    return `${year}年${month}月`;
};

export const getCurrentMonth = (startDate: string, endDate: string) => {
    if (startDate === endDate) {
        return `${formatMonth(startDate)}`;
    } else {
        return `${formatMonth(startDate)} 至 ${formatMonth(endDate)}`;
    }
};

export const getOppositePartyName = () =>
    request({
        url: `/api/CashierInvoiceTable/GetOppositePartyName`,
        method: "post",
    });

// 发票资金一览表
export const setCITColumns = (isShowNum: boolean, analysisConditions: "1" | "2" | "3") => {
    const setModule = "CashierInvoice";
    const columns: IColumnProps[] = [
        {
            prop: "opposite_party",
            label: "往来单位",
            minWidth: 300,
            width: getColumnWidth(setModule, 'opposite_party'),
            fixed: "left",
            align: "left",
            headerAlign: "center",
        },
        {
            label: "发票",
            headerAlign: "center",
            children: [
                ["2", "3"].includes(analysisConditions) && isShowNum
                    ? {
                          prop: "salesInvoiceNum",
                          label: "销项发票张数",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'salesInvoiceNum')
                      }
                    : {},
                ["2", "3"].includes(analysisConditions)
                    ? {
                          prop: "salesInvoiceAmount",
                          label: "销项发票金额（含税）",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'salesInvoiceAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["1", "3"].includes(analysisConditions) && isShowNum
                    ? {
                          prop: "purchaseInvoiceNum",
                          label: "进项发票张数",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'purchaseInvoiceNum')
                      }
                    : {},
                ["1", "3"].includes(analysisConditions)
                    ? {
                          prop: "purchaseInvoiceAmount",
                          label: "进项发票金额（含税）",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'purchaseInvoiceAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
            ],
        },
        {
            label: "资金",
            headerAlign: "center",
            children: [
                ["2", "3"].includes(analysisConditions) && isShowNum
                    ? {
                          prop: "incomeNum",
                          label: "收款笔数",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 80,
                          width: getColumnWidth(setModule, 'incomeNum')
                      }
                    : {},
                ["2", "3"].includes(analysisConditions)
                    ? {
                          prop: "incomeAmount",
                          label: "收款金额",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 80,
                          width: getColumnWidth(setModule, 'incomeAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["1", "3"].includes(analysisConditions) && isShowNum
                    ? {
                          prop: "expenditureNum",
                          label: "付款笔数",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 80,
                          width: getColumnWidth(setModule, 'expenditureNum')
                      }
                    : {},
                ["1", "3"].includes(analysisConditions)
                    ? {
                          prop: "expenditureAmount",
                          label: "付款金额",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 80,
                          width: getColumnWidth(setModule, 'expenditureAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
            ],
        },
        {
            label: "差异",
            headerAlign: "center",
            children: [
                ["2", "3"].includes(analysisConditions)
                    ? {
                          prop: "noSalesInvoiceAmount",
                          label: "未开发票金额（含税）",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'noSalesInvoiceAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["2", "3"].includes(analysisConditions)
                    ? {
                          prop: "noIncomeAmount",
                          label: "未收款金额",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'noIncomeAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["1", "3"].includes(analysisConditions)
                    ? {
                          prop: "noPurchaseeNum",
                          label: "未收发票金额（含税）",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'noPurchaseeNum'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["1", "3"].includes(analysisConditions)
                    ? {
                          prop: "noExpenditureAmount",
                          label: "未付款金额",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'noExpenditureAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
            ],
        },
        { slot: "operation" },
    ];
    return columns;
};

export const setCIIColumns = (analysisConditions: "1" | "2" | "3") => {
    const setModule = "CashierInvoiceInfo";
    const columns: IColumnProps[] = [
        {
            label: "发票",
            headerAlign: "center",
            children: [
                {
                    prop: "invoiceDate",
                    label: "日期",
                    headerAlign: "right",
                    align: "left",
                    width: getColumnWidth(setModule, 'invoiceDate'),
                },
                {
                    slot: "invoiceNumber",
                },
                ["2", "3"].includes(analysisConditions)
                    ? {
                          prop: "salesInvoiceAmount",
                          label: "销项发票金额（含税）",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'salesInvoiceAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["1", "3"].includes(analysisConditions)
                    ? {
                          prop: "purchaseInvoiceAmount",
                          label: "进项发票金额（含税）",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 140,
                          width: getColumnWidth(setModule, 'purchaseInvoiceAmount'),
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
            ],
        },
        {
            label: "资金",
            headerAlign: "center",
            children: [
                {
                    prop: "cashierDate",
                    label: "日期",
                    headerAlign: "right",
                    align: "left",
                    width: getColumnWidth(setModule, 'cashierDate')
                },
                {
                    slot: "line_sn_name",
                },
                {
                    prop: "ac_name",
                    label: "资金账户",
                    headerAlign: "right",
                    align: "left",
                    width: getColumnWidth(setModule, 'ac_name')
                },
                ["2", "3"].includes(analysisConditions)
                    ? {
                          prop: "incomeAmount",
                          label: "收款金额",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 80,
                          width: getColumnWidth(setModule, 'incomeAmount'),
                          resizable: false,
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
                ["1", "3"].includes(analysisConditions)
                    ? {
                          prop: "expenditureAmount",
                          label: "付款金额",
                          align: "right",
                          headerAlign: "right",
                          minWidth: 80,
                          width: getColumnWidth(setModule, 'expenditureAmount'),
                          resizable: false,
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                      }
                    : {},
            ],
        },
    ];
    return columns;
};
//关联单据类型查看权限判断
export function checkBillPermission(billType: number) {
    return (
        (billType == 1011 && checkPermission(["Warehousing-查看"])) ||
        (billType == 1012 && checkPermission(["WarehousingReturn-查看"])) ||
        (billType == 1021 && checkPermission(["SellDelivery-查看"])) ||
        (billType == 1022 && checkPermission(["SellReturn-查看"])) ||
        ([1050, 1051, 1052].includes(billType) && checkPermission(["OthersWarehousing-查看"])) ||
        ([1060, 1061, 1062].includes(billType) && checkPermission(["OthersWarehousingOut-查看"])) ||
        (billType == 1070 && checkPermission(["CostAdjustment-查看"]))||
        (billType == 1081 && checkPermission(["SellDelivery-查看"])) ||
        (billType == 1082 && checkPermission(["SellReturn-查看"])) ||     
        (billType == 1090 && checkPermission(["OtherReceipt-查看"])) ||
        (billType == 1100 && checkPermission(["OtherPayment-查看"])) ||
        (billType == 1110 && checkPermission(["Assembly-查看"])) ||
        (billType == 1120 && checkPermission(["Disassembly-查看"]))||  
        ([1130, 1131, 1132, 1133].includes(billType) && checkPermission(["Offset-查看"]))
    );
}
//生成单据导入权限判断
export function checkBillInportPermission(billType: number) {
    return (
        (billType == 1011 && checkPermission(["Warehousing-导入"])) ||
        (billType == 1012 && checkPermission(["WarehousingReturn-导入"])) ||
        (billType == 1021 && checkPermission(["SellDelivery-导入"])) ||
        (billType == 1022 && checkPermission(["SellReturn-导入"]))
    );
}
interface UrlRoute {  
    [key: number]: string;  
} 
export const urlRoute: UrlRoute = {
    1011: "/warehousingList",
    1012: "/warehousingReturnList",
    1021: "/sellDeliveryList",
    1022: "/sellReturnList",
    1100: "/otherPaymentList",
    1090: "/otherReceiptList"
}
export const urlRouteName: UrlRoute = {
    1011: "采购入库单列表",
    1012: "采购退货单列表",
    1021: "销售出库单列表",
    1022: "销售退货单列表",
    1100: "其他支出单列表",
    1090: "其他收入单列表"
}

export const loginErrorList = {
    输入的统一社会信用代码或纳税人识别号错误: "请确认报税地区或统一社会信用代码是否填写正确",
    登记企业信息不存在: "请确认报税地区或统一社会信用代码是否填写正确",
    未查询到您与该企业的关联关系信息: "未查询到您与该企业的关联关系信息，请确认录入的统一社会信用代码或个人账号是否正确",
    未查询到您与该代理企业的关联关系信息: "未查询到您与该代理企业的关联关系信息，请确认录入的代理机构税号或个人账号是否正确",
    该用户未注册: "该用户未注册，请在自然人业务入口进行用户注册",
    未查询到对应手机号: "您的手机号码为空，请您前往税局“找回手机号码”功能修改成正确的手机号码或扫码登录税局添加默认手机号码",
    手机号码为空: "您的手机号码为空，请您前往税局“找回手机号码”功能修改成正确的手机号码或扫码登录税局添加默认手机号码",
    账号已被锁定: "连续认证错误次数过多，您的账号已被锁定。建议您前往电子税局使用“忘记密码”修改密码后重新登录或等待次日零时自动解锁。",
    已连续4次输入个人用户密码错误: "已连续4次输入个人用户密码错误，您可以通过电子税局的”忘记密码“设置新密码。如果再次输入错误，您的账户会被锁定！",
    已连续3次输入个人用户密码错误: "已连续3次输入个人用户密码错误，您可以通过电子税局“忘记密码”设置新密码",
    已连续4次输入密码错误: "已连续4次输入个人用户密码错误，您可以通过电子税局的”忘记密码“设置新密码。如果再次输入错误，您的账户会被锁定！",
    已连续3次输入密码错误: "已连续3次输入个人用户密码错误，您可以通过电子税局“忘记密码”设置新密码",
    输入的个人账号或密码错误: "输入的个人账号或密码错误，请重新输入！",
    手机号码不存在: "输入的个人账号或密码错误，请重新输入！",
    短信发送失败: "税局系统短信验证码发送失败，请稍等1分钟后重试",
    发送验证码失败: "税局系统短信验证码发送失败，请稍等1分钟后重试",
    短信发送次数超限: "税局系统检测您申请验证码次数已超限制",
    短信发送申请过于频繁: "税局系统检测您验证码申请过于频繁，请稍等10分钟后重试",
    使用短信验证码过于频繁: "税局系统检测您使用短信验证码过于频繁，请稍等10分钟后重试",
    未获取到验证码: "税局系统短信验证码发送失败，请稍等1分钟后重试",
    拼图: "税局系统短信验证码发送失败，请稍等1分钟后重试",
    该地区暂不支持: "因电子税局系统升级维护中，当前地区一键取票功能暂时无法使用，后续将会尽快恢复正常使用",
    当前账号下已有公司正在执行任务: "系统检测到您的税局登录账号所关联的其他企业税号正在取票中，为避免取票失败，请等其他企业税号取票结束后再发起一键取票操作",
    登录任务已存在:"系统检测到您的税局登录账号所关联的其他企业税号正在登录中，为保障您的账号安全，请勿重复登录。如非本人操作，请及时修改密码！",
    
    身份错误: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    跳转登录成功页失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    请求认证服务失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    网络异常: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    网络错误: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    税务局网页加载超时: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    税局繁忙校验失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    登录页加载失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    登录成功但未获取到cookie: "税局系统繁忙，登录失败，请稍等1分钟后重试",
    服务异常: "登录失败，请联系在线客服",
    服务器内部错误: "登录失败，请联系在线客服",
    服务不可用: "登录失败，请联系在线客服",
    服务器异常: "登录失败，请联系在线客服",
    任务超时: "登录失败，请联系在线客服",
    未扫码授权: "登录失败，请先前往税局选择自然人业务登录个人账号，进入“账户中心-企业授权管理-待确认授权”查看是否存在待确认授权企业，若存在则请确认后再进行一键取票操作",
    人脸识别服务协议: "登录失败，税局检测您的账号未签订《人脸识别服务协议》，请您前往税局官网登录同意《人脸识别服务协议》后再执行一键取票功能",
    您登录的账号下无当前取票企业的办税权限: "登录失败，您登录的账号下无当前取票企业的办税权限，请前往电子税局登录确认是否为该企业的涉税服务人员。",
    全国电子税务局下发验证码失败: "税局系统短信验证码发送失败，请稍等1分钟后重试",
};
export const robotLoginErrorList = [
    "未获取到验证码",
    "拼图",
    "身份错误",
    "跳转登录成功页失败",
    "请求认证服务失败",
    "网络异常",
    "网络错误",
    "税务局网页加载超时",
    "税局繁忙校验失败",
    "登录页加载失败",
    "登录成功但未获取到cookie",
    "服务异常",
    "服务器内部错误",
    "服务不可用",
    "服务器异常",
    "任务超时",

    "会话已失效",
    "取票下载任务失败",
    "无法跳转至取票页",
    "取票页面加载超时",
    "登录已过期",
    "登录状态失效",
    "Cookie",
    "税局查询接口异常",
    "数据量过多",
    "保存客户异常",
    "保存供应商异常",

    "取票页加载失败",
    "取票失败",
    "抵扣勾选校验操作失败",
    "下载文件失败",
    "下载文件超时",
    "执行异常",
    "文件失败",
    "数据库操作失败",
    "数据异常",
    "税局内部错误",
    "纳税人识别号匹配失败",
    "系统操作失败",
];

// 若要变动枚举值，搜一下使用地方
export const invoiceTypeListAll: Array<IInvoiceTypeListItem> = [
    {
        IN_ID: "1070",
        IN_NAME: "全电发票（增值税专用发票）",
    },
    {
        IN_ID: "1060",
        IN_NAME: "全电发票（普通发票）",
    },
    {
        IN_ID: "1010",
        IN_NAME: "未开具发票",
    },
    {
        IN_ID: "1020",
        IN_NAME: "增值税普通发票",
    },
    {
        IN_ID: "1030",
        IN_NAME: "增值税专用发票",
    },
    {
        IN_ID: "1110",
        IN_NAME: "机动车销售统一发票",
    },
    {
        IN_ID: "1090",
        IN_NAME: "二手车销售统一发票",
    },
    {
        IN_ID: "1040",
        IN_NAME: "税控机动车专用发票",
    },
    {
        IN_ID: "1050",
        IN_NAME: "纳税检查调整",
    },
];
export const invoiceTypeListAllPur: Array<IInvoiceTypeListItem> = [
    {
        IN_ID: "1070",
        IN_NAME: "全电发票（增值税专用发票）",
    },
    {
        IN_ID: "1060",
        IN_NAME: "全电发票（普通发票）",
    },
    {
        IN_ID: "1030",
        IN_NAME: "增值税专用发票",
    },
    {
        IN_ID: "1020",
        IN_NAME: "增值税普通发票",
    },
    {
        IN_ID: "1110",
        IN_NAME: "机动车销售统一发票",
    },
    {
        IN_ID: "1090",
        IN_NAME: "二手车销售统一发票",
    },
    {
        IN_ID: "1040",
        IN_NAME: "税控机动车专用发票",
    },
];
