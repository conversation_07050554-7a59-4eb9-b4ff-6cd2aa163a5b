<template>
    <div ref="dataMaskRef" class="data-mask" :style="{ bottom: maskBottom + 'px', height: maskHeight > 0 ? maskHeight + 'px' : '100%' }">
        <div class="data-mask-content">
            <div ref="updateDialogRef" class="update-dialog" @click="handleTrialExpired({msg:ExpiredToBuyDialogEnum.normal});"></div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from "vue";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const props = withDefaults(
    defineProps<{
        // 显示行数
        showLines: number;
        // 是否有分页
        hasPage?: boolean;
        // 是否onMounted立即执行
        immediate?: boolean;
    }>(),
    {
        showLines: 5,
        hasPage: false,
        immediate: true,
    }
);

const maskBottom = computed(() => (props.hasPage ? 50 : 19));

const dataMaskRef = ref();
// 遮罩高度
const maskHeight = ref(0);
const getTableHeight = (element?: any) => {
    if (element) {
        setTimeout(() => {
            let tableInner = element.$refs.tableWrapper;
            let tableHead = element.$refs.tableHeader;
            let row = element.$el.querySelector(`.el-table__row:nth-child(${props.showLines + 1})`);
            maskHeight.value = tableInner.offsetHeight - row.offsetTop - tableHead.offsetHeight - 10;
            dataMaskRef.value.style.display = "block";
        }, 0);
    } else {
        let tableInner = document.querySelector(".el-table") as any;
        let tableHead = tableInner.querySelector("thead");
        let row = tableInner.querySelector(`.el-table__row:nth-child(${props.showLines + 1})`);
        // 滚动条高度设置10，分页器高度31
        maskHeight.value =
            tableInner.offsetHeight - row?.offsetTop - tableHead.offsetHeight - maskBottom.value + (props.hasPage ? 31 + 10 : 10);
        dataMaskRef.value.style.display = "block";
    }
};

const updateDialogRef = ref();
const bounce = () => {
    updateDialogRef.value.classList.add("shake");
    setTimeout(() => {
        updateDialogRef.value.classList.remove("shake");
    }, 1000);
};

defineExpose({
    bounce,
    getTableHeight,
});

onMounted(() => {
    if (props.immediate) {
        nextTick().then(()=>{
            getTableHeight();
        });
    }
});
</script>
<style lang="less" scoped>
@keyframes shake {
    0% {
        // top: 40px;
        // left: 50%;
    }
    25% {
        // top: 42px;
        // left: 48%;
        transform: translate(-30px, 2px);
    }
    50% {
        // top: 40px;
        // left: 50%;
    }
    75% {
        // top: 38px;
        // left: 52%;
        transform: translate(30px, -2px);
    }
    100% {
        // top: 40px;
        // left: 50%;
    }
}

.data-mask {
    display: none;
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0 10px;
    box-sizing: border-box;
    z-index: 9;
    .data-mask-content {
        position: reactive;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        margin: 0 auto;
        border-left: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);
        background-color: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(5px);
        display: flex;
        justify-content: center;
        align-items: center;
        .update-dialog {
            // position: absolute;
            // top: 40px;
            // left: 50%;
            // transform: translateX(-50%);
            // height: 300px;
            width: 400px;
            height: 100%;
            margin: 0 auto;
            background: url("@/assets/DataMask/data-mask-dialog.png") no-repeat center;
            background-size: contain;
            &.shake {
                animation: shake 0.5s 2 linear;
            }
        }
    }
}
</style>
