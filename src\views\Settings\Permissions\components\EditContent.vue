<template>
    <div class="slot-content align-center">
        <div class="slot-title">权限设置</div>
        <div class="slot-mini-content">
            <div class="title">权限设置</div>
            <div class="line-item accoutset">
                <span class="line-title">当前账套：</span>
                <span class="line-content" id="editAsName">{{ asname }}</span>
            </div>
            <div class="line-item line-mobile">
                <span class="highlight-red">*</span>
                <span class="line-title">手机用户：</span>
                <input type="text" id="txtUserId" :disabled="isDisabled" v-model="txtUserId" />
            </div>
            <div class="line-item no-top" ref="elRadioGroupRef">
                <el-radio-group v-model="PermissionType">
                    <el-radio :label="10001" v-show="!isProSystem">
                        <template #default>
                            <span class="line-title">账套管理员</span>
                            <span style="display: flex">
                                <span class="block line-note scm-and-tax-hidden">
                                    凭证 | 资金 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 账簿 | 报表 | 新增账套 | 删除账套 | 备份恢复 |
                                    重新初始化账套 | 关联进销存 | 关联云发票
                                </span>
                                <span class="block line-note scm-and-tax-show">
                                    凭证 | 资金 | 发票 | 工资 | 资产 | 期末结转 | 账簿 | 报表 | 新增账套 | 删除账套 | 备份恢复 |
                                    重新初始化账套
                                </span>
                            </span>
                        </template>
                    </el-radio>
                    <div class="radio-parent">
                        <el-radio :label="10005">
                            <template #default>
                                <span class="line-title">主管</span>
                                <span style="display: flex">
                                    <span class="block line-note scm-and-tax-hidden pro-hidden">
                                        凭证 | 资金 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 账簿 | 报表 | 新增账套
                                    </span>
                                    <span class="block line-note scm-and-tax-show pro-hidden">
                                        凭证 | 资金 | 发票 | 工资 | 资产 | 期末结转 | 账簿 | 报表 | 新增账套
                                    </span>
                                    <span class="block line-note scm-and-tax-hidden pro-show">
                                        凭证 | 资金 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 账簿 | 报表
                                    </span>
                                    <span class="block line-note scm-and-tax-show pro-show"
                                        >凭证 | 资金 | 发票 | 工资 | 资产 | 期末结转 | 账簿 | 报表
                                    </span>
                                </span>
                            </template>
                        </el-radio>
                        <a v-permission="['permissions-canedit']" class="link ml-10" @click.stop="findInitRoleInfo(10005)">编辑</a>
                    </div>
                    <div class="radio-parent">
                        <el-radio :label="10002">
                            <template #default>
                                <span class="line-title">制单人</span>
                                <span style="display: flex">
                                    <span class="block line-note scm-and-tax-hidden pro-hidden">
                                        凭证 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 查看资金、账簿和报表 | 新增账套
                                    </span>
                                    <span class="block line-note scm-and-tax-show pro-hidden">
                                        凭证 | 发票 | 工资 | 资产 | 期末结转 | 查看资金、账簿和报表 | 新增账套
                                    </span>
                                    <span class="block line-note scm-and-tax-hidden pro-show">
                                        凭证 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 查看资金、账簿和报表
                                    </span>
                                    <span class="block line-note scm-and-tax-show pro-show">
                                        凭证 | 发票 | 工资 | 资产 | 期末结转 | 查看资金、账簿和报表
                                    </span>
                                </span>
                            </template>
                        </el-radio>
                        <a v-permission="['permissions-canedit']" class="link ml-10" @click.stop="findInitRoleInfo(10002)">编辑</a>
                    </div>
                    <div class="radio-parent">
                        <el-radio :label="10006">
                            <template #default>
                                <span class="line-title">出纳</span>
                                <span style="display: flex">
                                    <span class="block line-note pro-hidden">资金 | 查看凭证、资产、账簿和报表 | 新增账套</span>
                                    <span class="block line-note pro-show">资金 | 查看凭证、资产、账簿和报表</span>
                                </span>
                            </template>
                        </el-radio>
                        <a v-permission="['permissions-canedit']" class="link ml-10" @click.stop="findInitRoleInfo(10006)">编辑</a>
                    </div>
                    <div class="radio-parent">
                        <el-radio :label="10003">
                            <template #default>
                                <span class="line-title">查看</span>
                                <span style="display: flex">
                                    <span class="block line-note pro-hidden">查看凭证、资金、资产、账簿和报表 | 新增账套</span>
                                    <span class="block line-note pro-show">查看凭证、资金、资产、账簿和报表</span>
                                </span>
                            </template>
                        </el-radio>
                        <a v-permission="['permissions-canedit']" class="link ml-10" @click.stop="findInitRoleInfo(10003)">编辑</a>
                    </div>
                    <div class="radio-user-defined" v-for="item in roleInfoList" :key="item.roleId">
                        <el-radio :label="item.roleId" class="user-defined">
                            <!-- <template #default>
                            <span class="line-title">{{ item.roleName }}</span>
                        </template> -->
                            {{ item.roleName }}
                        </el-radio>
                        <a
                            v-permission="['permissions-canedit']"
                            class="link ml-10"
                            @click.stop="() => editRoleInfo(item.roleId, item.roleName, item.functionsState)"
                        >
                            编辑
                        </a>
                        <a v-permission="['permissions-canedit']" class="link ml-10" @click.stop="() => deleteRoleInfo(item.roleId)"
                            >删除</a
                        >
                    </div>
                </el-radio-group>
            </div>
            <div class="line-item" id="customRoleInfos" style="display: none"></div>
            <div class="line-item customrole">
                <div v-permission="['permissions-canedit']" class="add-role-btn" @click="addRoleInfo">+ 新增角色</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleSave">保存</a>
                <a class="button ml-20" @click="handleCancel">取消</a>
            </div>
        </div>
        <el-dialog v-model="validPhoneShow" center width="440" title="新增权限" @closed="handleClosed" class="dialogDrag">
            <div class="validPhoneShow-box" v-dialogDrag>
                <div class="main">
                    <div class="txt">亲，您输入的手机还没有注册，确认需要新增吗？</div>
                    <div class="txt">为保障您的数据安全，新注册用户需要验证手机。</div>
                    <input type="text" class="mt-20" disabled style="display: inline-block" v-model="txtUserId" />
                    <a class="button solid-button large-2 ml-10" @click="addSendSMS">{{ sendMsg }}</a>
                    <input type="text" class="mt-10" placeholder="请输入验证码" v-model="confirmCode" />
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="validPhoneConfirm">确定</a>
                    <a class="button ml-10" @click="validPhoneCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="freePromptShow" center width="440" title="提示" class="dialogDrag">
            <div class="validPhoneShow-box" v-dialogDrag>
                <div class="main">
                    <div class="txt">免费版仅限账套管理员一人使用， 您需要升级专业版，被授权的成员才能进入本账套。</div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="trialButtonOnclick">立即升级</a>
                </div>
            </div>
        </el-dialog>
        <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
    </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { isInWxWork } from "@/util/wxwork";

import type { IRoleInfo, ITableItem } from "../types";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { getServiceId } from "@/util/proUtils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const isProSystem = window.isProSystem;

const elRadioGroupRef = ref<HTMLDivElement>();
const asid = useAccountSetStore().accountSet?.asId || 0;
const trialStatusStore = useTrialStatusStore();

const props = defineProps<{
    asname: string;
    roleInfoList: IRoleInfo[];
    tableData: ITableItem[];
    trialButtonOnclick: Function;
}>();

const trialButtonOnclick = () => {
    props.trialButtonOnclick();
};

const asname = computed(() => props.asname);
const roleInfoList = computed(() => props.roleInfoList.filter((item) => item.roleId > 10006));

const txtUserId = ref("");
const isDisabled = ref(false);
const PermissionType = ref(window.isProSystem ? 10005 : 10001);
const confirmCode = ref("");
const sendMsg = ref("发送验证码");
const validPhoneShow = ref(false);
const freePromptShow = ref(false);
let canSend = true;
let SubmitType: "New" | "Edit" = "New";
let timer: any = null;
const emit = defineEmits(["addRoleInfo", "editRoleInfo", "deleteRoleInfo", "handleBack", "handleTrial","formChanged"]);

const handleInit = () => {
    const accountSet = useAccountSetStore().accountSet;
    const accountStandard = accountSet?.accountingStandard;
    if (window.isProSystem) {
        elRadioGroupRef.value?.querySelectorAll(".pro-hidden").forEach((item: any) => {
            (item as HTMLSpanElement).style.display = "none";
        });
        PermissionType.value = 10005;
    } else {
        elRadioGroupRef.value?.querySelectorAll(".pro-show").forEach((item: any) => {
            (item as HTMLSpanElement).style.display = "none";
        });
    }
    if (accountStandard === 4 || accountStandard === 6 || accountStandard === 7) {
        elRadioGroupRef.value?.querySelectorAll(".scm-and-tax-hidden").forEach((item: any) => {
            (item as HTMLSpanElement).style.display = "none";
        });
    } else {
        elRadioGroupRef.value?.querySelectorAll(".scm-and-tax-show").forEach((item: any) => {
            (item as HTMLSpanElement).style.display = "none";
        });
    }
};

onMounted(handleInit);

const addRoleInfo = () => emit("addRoleInfo");
const editRoleInfo = (roleId: number, roleName: string, functionsState: number[]) => {
    emit("editRoleInfo", { roleId, roleName, functionsState, isDisabled: false });
};
const deleteRoleInfo = (roleId: number) => {
    request({ url: "/api/PermissionsRole/CheckUsed?roleId=" + roleId, method: "post" }).then((r: any) => {
        if (r.state === 1000 && r.data === true) {
            ElNotify({ type: "warning", message: "该角色下有用户，不能删除" });
            return;
        } else {
            ElConfirm("角色删除后不可恢复，确定要删除吗？").then((r: any) => {
                if (r) {
                    request({ url: "/api/PermissionsRole?roleId=" + roleId, method: "delete" }).then((r: any) => {
                        if (r.state === 1000 && r.data === true) {
                            ElNotify({ type: "success", message: "删除成功" });
                            emit("deleteRoleInfo", roleId);
                        } else {
                            ElNotify({ type: "error", message: r.msg || "删除失败" });
                        }
                    });
                }
            });
        }
    });
};
const findInitRoleInfo = (roleId: number) => {
    const roleInfo = props.roleInfoList.find((item) => item.roleId == roleId);
    if (roleInfo) {
        emit("editRoleInfo", { roleId, roleName: roleInfo.roleName, functionsState: roleInfo.functionsState, isDisabled: true });
    }
};
let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    isSaving = true;
    if (!txtUserId.value.trim()) {
        ElNotify({ type: "warning", message: "亲，请输入用户手机号码！" });
        isSaving = false;
        return;
    }
    if (!/^(0|86|17951)?1([3-9]|0)[0-9][0-9]{8}$/.test(txtUserId.value.trim())) {
        ElNotify({ type: "warning", message: "亲，请输入正确的手机号码！" });
        isSaving = false;
        return;
    }
    SaveProxy(txtUserId.value.trim(), PermissionType.value);
};
const SaveProxy = (userId: string, PermissionType: number) => {
    if (isInWxWork() && SubmitType == "New") {
        if (userId === "") {
            ElNotify({ type: "warning", message: "亲，请选择用户！" });
            isSaving = false;
            return;
        }
        request({ url: window.jmHost + "/Wxwork/CreateUser?userId=" + userId, method: "post" }).then((data: any) => {
            if (data.code == 0) {
                txtUserId.value = data.data;
                Save(userId, PermissionType);
            } else if (data.code != -1) {
                ElNotify({ type: "warning", message: data.msg });
                isSaving = false;
            } else {
                ElNotify({ type: "warning", message: "亲，新增失败啦！" });
                isSaving = false;
            }
        });
    } else {
        Save(userId, PermissionType);
    }
};
const CheckPhone = (phoneNum: string) => {
    const filter = /^(0|86|17951)?1([3-9]|0)[0-9][0-9]{8}$/;
    return filter.test(phoneNum);
};
var IsExist = function (userId: string) {
    for (let i = 0; i < props.tableData.length; i++) {
        const item = props.tableData[i];
        if (item.userId == userId || item.mobile == userId) {
            return true;
        }
    }
    return false;
};
const Save = (txtUserId: string, PermissionType: number) => {
    if (!txtUserId) {
        ElNotify({ type: "warning", message: "亲，请输入用户手机号码！" });
        isSaving = false;
        return;
    }
    if (!CheckPhone(txtUserId)) {
        ElNotify({ type: "warning", message: "亲，请输入正确的手机号码！" });
        isSaving = false;
        return;
    }
    if (SubmitType == "New") {
        if (IsExist(txtUserId)) {
            ElNotify({ type: "warning", message: "亲，此用户已经拥有此账套权限！" });
            isSaving = false;
            return;
        }
    }
    request({ url: "/api/ConfirmCode/ValidateUserIdOrMobile?mobile=" + txtUserId, method: "post" }).then((res: IResponseModel<number>) => {
        if (res.state === 1000 && res.data > 0) {
            let baseApiUrl = SubmitType === "New" ? "/api/Permissions/V2" : "/api/Permissions";
            let queryParams = `userIdOrMobile=${txtUserId}&permissions=${PermissionType}&asId=${asid}`;
            if (SubmitType === "New") {
                queryParams += `&serviceid=${getServiceId()}`;
            }
            let url = `${baseApiUrl}?${queryParams}`;
            request({
                url: url,
                method: SubmitType === "New" ? "post" : "put",
            })
                .then((r: IResponseModel<string>) => {
                    const type = SubmitType === "New" ? "新增" : "修改";
                    if (r.state === 1000) {
                        if (
                            !window.isProSystem &&
                            !window.isErp &&
                            !useThirdPartInfoStoreHook().isThirdPart &&
                            !window.isBossSystem &&
                            trialStatusStore.isTrial &&
                            trialStatusStore.isExpired
                        ) {
                            validPhoneShow.value = true;
                        } else {
                            ElNotify({ type: "success", message: "亲，" + type + "成功啦！" });
                        }
                        handleCancel();
                        SubmitType === "New" && handleTrialExpired({ msg: ExpiredToBuyDialogEnum.notAdminEnter });

                    } else {
                        if(r.msg.length > 0) {
                            if(r.msg === "您的用户数量已超过已购买用户数量，建议您去增购"){
                                proOverFlowText.value = r.msg;
                                proOverFlowShow.value = true;
                            }else{
                                ElNotify({ type: "warning", message: r.msg });
                            }
                        } else {
                            if (r.subState === 8) {
                                ElNotify({ type: "warning", message: "亲，" + type + "用户的应用创建账套的数量已经超过限额，请联系管理员！" });
                            } else {
                                ElNotify({ type: "warning", message: "亲，" + type + "失败啦！" });
                            }
                        }
                    }
                })
                .catch((err: any) => {
                    if (err.response?.status === 400) {
                        proOverFlowText.value = err.response.data;
                        proOverFlowShow.value = true;
                    } else {
                        ElNotify({ type: "warning", message: "亲，新增用户失败，请稍后重试！" });
                    }
                })
                .finally(() => {
                    isSaving = false;
                });
        } else {
            validPhoneShow.value = true;
        }
    });
};
const handleCancel = () => {
    emit("handleBack");
    isDisabled.value = false;
    PermissionType.value = window.isProSystem ? 10005 : 10001;
    txtUserId.value = "";
    confirmCode.value = "";
    validPhoneShow.value = false;
    clearTimeout(timer);
    timer = null;
    canSend = true;
    sendMsg.value = "发送验证码";
    isSaving = false;
};
const addSendSMS = () => {
    if (!canSend) return;
    canSend = false;
    sendMsg.value = "正在发送...";
    const params = {
        CurrentSystemType: 1,
        Phone: txtUserId.value,
        stype: 13,
        appasid: getGlobalToken(),
    };
    request({
        url: window.accountSrvHost + "/Default/Services/SendSMSForConfirm.ashx?" + getUrlSearchParams(params),
        method: "get",
    }).then((res: any) => {
        if (res === "Success") sendValidCode();
    });
};
const validPhoneCancel = () => {
    validPhoneShow.value = false;
    isSaving = false;
};
const handleClosed = () => {
    validPhoneShow.value = false;
    isSaving = false;
};
const validPhoneConfirm = () => {
    request({ url: "/api/ConfirmCode/CheckCodeTime?mobile=" + txtUserId.value + "&confirmCode=" + confirmCode.value, method: "post" }).then(
        (r: IResponseModel<string>) => {
            if (r.state !== 1000) {
                ElNotify({ type: "warning", message: r.msg || "请求失败" });
                return;
            }
            if (r.data === "Success") {
                validPhoneShow.value = false;
                request({
                    url:
                        window.accountSrvHost +
                        "/Api/CreateUserForPermission.ashx?CurrentSystemType=1&NewType=MOBILE&UserId=" +
                        txtUserId.value,
                }).then((result: any) => {
                    if (result === "Success") {
                        let baseApiUrl = SubmitType === "New" ? "/api/Permissions/V2" : "/api/Permissions";
                        let queryParams = `userIdOrMobile=${txtUserId.value}&permissions=${PermissionType.value}&asId=${asid}`;
                        if (SubmitType === "New") {
                            queryParams += `&serviceid=${getServiceId()}`;
                        }
                        let url = `${baseApiUrl}?${queryParams}`;
                        request({ url, method: SubmitType === "New" ? "post" : "put" })
                            .then((result: IResponseModel<string>) => {
                                if (r.state === 1000) {
                                    // Init();
                                    // Cancel();
                                    if (
                                        !window.isProSystem &&
                                        !window.isErp &&
                                        !useThirdPartInfoStoreHook().isThirdPart &&
                                        !window.isBossSystem &&
                                        trialStatusStore.isTrial &&
                                        trialStatusStore.isExpired
                                    ) {
                                        // $("#proDialog").dialog("open").dialog("center");
                                        // 专业版广告提示
                                    } else {
                                        ElNotify({ type: "success", message: "亲，新增成功啦！" });
                                    }
                                } else {
                                    if (r.subState === 8) {
                                        ElNotify({
                                            type: "warning",
                                            message: "亲，新增用户的应用创建账套的数量已经超过限额，请联系管理员！",
                                        });
                                    } else if (r.subState === 2) {
                                        ElNotify({
                                            type: "warning",
                                            message: "亲，新增用户时的数据存储过程出错，请联系管理员！",
                                        });
                                    } else {
                                        ElNotify({ type: "warning", message: "亲，新增失败，请重试~" });
                                    }
                                }
                            })
                            .catch((err: any) => {
                                if (err.response?.status === 400) {
                                    proOverFlowText.value = err.response.data;
                                    proOverFlowShow.value = true;
                                } else {
                                    ElNotify({
                                        type: "warning",
                                        message: "亲，新增用户失败，请稍后重试！",
                                    });
                                }
                            });
                    } else {
                        ElNotify({ type: "warning", message: "亲，新增用户失败，请重试~" });
                    }
                    validPhoneCancel();
                });
            } else if (r.data === "Expired") {
                ElNotify({ type: "warning", message: "亲，验证码已过期！" });
            } else {
                ElNotify({ type: "warning", message: "亲，验证码错误！" });
            }
        }
    );
};

const changeTelePhoneNumber = (val: string) => (txtUserId.value = val);
const changePermissionType = (val: number) => (PermissionType.value = val);
const changeDisabled = (val: boolean) => (isDisabled.value = val);
const changeSubmitType = (val: "New" | "Edit") => (SubmitType = val);

defineExpose({ changeTelePhoneNumber, changePermissionType, changeDisabled, changeSubmitType });

const sendValidCode = (second?: number) => {
    if (!second) second = 60;
    if (second - 1 > 0) {
        sendMsg.value = "重新发送（" + second + "）";
        second--;
        timer = setTimeout(() => {
            sendValidCode(second);
        }, 1000);
    } else {
        sendMsg.value = "重新发送（" + 1 + "）";
        setTimeout(() => {
            sendMsg.value = "发送验证码";
            canSend = true;
            clearTimeout(timer);
            timer = null;
            return;
        }, 1000);
    }
};
watch([txtUserId, PermissionType], () => {
    emit("formChanged");
});
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.slot-content {
    .slot-mini-content {
        overflow: hidden;
        width: 1000px;

        .line-item {
            padding-left: 296px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            text-align: left;
            margin-top: 10px;
            display: flex;
            align-items: center;
            .block {
                display: block;
            }
            .link {
                line-height: 17px;
                font-size: var(--h5);
                &.bottom {
                    align-self: flex-end;
                    line-height: 32px;
                }
            }
            .add-role-btn {
                width: 80px;
                height: 28px;
                border-radius: 2px;
                box-sizing: border-box;
                border: 1px solid var(--link-color);
                color: var(--link-color);
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: var(--h5);
                cursor: pointer;
                margin-left: 28px;
            }
            & + .line-item {
                margin-top: 20px;
            }
        }
        .line-title {
            color: var(--font-color);
            font-size: var(--h4);
            line-height: 20px;
            font-weight: 600;
        }
        .line-content {
            margin-left: 10px;
            color: var(--font-color);
            font-size: var(--h4);
            line-height: 20px;
        }
        .accoutset {
            margin-left: 27px;
            margin-top: 40px !important;
        }
        .line-mobile {
            margin-top: 30px !important;
            margin-left: 16px;
            #txtUserId {
                .detail-original-input(188px, 32px);
                margin-left: 8px;
            }
        }
        .line-wxwork-selectuser {
            margin-top: 30px !important;
            margin-left: 44px;
            .select-user-btn {
                box-sizing: border-box;
                border: 1px solid var(--border-color);
                width: 130px;
                height: 32px;
                text-align: center;
                font-size: 0;
                cursor: pointer;
                display: inline-block;
                margin-left: 8px;
                vertical-align: top;
                img {
                    width: 9px;
                    height: 9px;
                    vertical-align: top;
                    margin-top: 10.5px;
                    margin-right: 4px;
                }
                span {
                    font-size: var(--h5);
                    color: var(--weaker-font-color);
                    line-height: 30px;
                }
            }
            .selected-user-btn {
                box-sizing: border-box;
                border: 1px solid var(--border-color);
                width: 154px;
                height: 32px;
                font-size: 0;
                cursor: pointer;
                display: inline-block;
                margin-left: 8px;
                vertical-align: top;
                position: relative;
                .head-img {
                    height: 18px;
                    width: 18px;
                    border-radius: 50%;
                    margin-left: 8px;
                    vertical-align: top;
                    margin-top: 6px;
                    margin-right: 4px;
                }
                .clear-btn {
                    width: 20px;
                    height: 20px;
                    vertical-align: top;
                    position: absolute;
                    right: -25px;
                    top: 5px;
                    cursor: pointer;
                }
            }
        }
        .line-cst-selectuser {
            .detail-el-select(188px, 32px);
        }
        .line-note {
            color: #929292;
            font-size: var(--h5);
            line-height: 17px;
            max-width: 476px;
            height: auto;
            word-wrap: normal;
            text-overflow: clip;
            white-space: normal;
        }
        .buttons {
            border-top: none;
            margin-top: 30px;
            margin-bottom: 40px;
            text-align: center;
        }
        .line-item {
            &.no-top {
                margin-top: 0;
            }
            :deep(.el-radio-group) {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                .el-radio {
                    margin-right: 0px;
                    margin-top: 20px;
                    height: auto;
                    &.user-defined {
                        .el-radio__label {
                            flex-direction: row;
                            a.link {
                                line-height: 20px;
                            }
                        }
                    }
                    .el-radio__label {
                        display: inline-flex;
                        flex-direction: column;
                        justify-content: center;
                        line-height: 17px;
                    }
                }
            }
        }
        .radio-parent {
            display: flex;
            & > a {
                display: flex;
                align-items: flex-end;
            }
        }
        .radio-user-defined {
            display: flex;
            align-items: center;
            margin-top: 20px;
            :deep(.el-radio) {
                margin-top: 0 !important;
                display: flex;
                align-items: center !important;
                .el-radio__input,
                .el-radio__label {
                    margin-top: 0 !important;
                }
                .el-radio__label {
                    color: var(--font-color);
                    font-size: var(--h4);
                    line-height: 20px;
                    font-weight: 600;
                }
            }
        }
    }
}

.validPhoneShow-box {
    color: #404040;
    font-size: 12px;
    .main {
        padding: 20px 60px;
        .txt {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }
        > input {
            .detail-original-input(180px, 30px);
        }
    }
    .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }
}
</style>
