<template>
    <div class="content">
        <div class="title">折旧摊销明细表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <span class="tal">期间：</span>
                    <DatePicker
                        class="faSummary-pick"
                        v-model:startPid="searchInfo.startMonth"
                        v-model:endPid="searchInfo.endMonth"
                        :clearable="false"
                        :editable="false"
                        :dateType="'month'"
                        :value-format="'YYYYMM'"
                        :label-format="'YYYY年MM月'"
                        :disabledDateStart="disabledDate"
                        :disabledDateEnd="disabledDate"
                        @user-change="handleUserChange"
                    />
                    <span class="ml-10 tal">类别：</span>
                    <div style="width: 140px" class="category">
                        <Select
                            :teleported="false"
                            v-model="searchInfo.fa_type"
                            placeholder=" "
                            :fit-input-width="true"
                            :popper-append-to-body="false"
                            @visible-change="visibleChange"
                            :filterable="true"
                            :filter-method="faTypeFilterMethod"
                        >
                            <Option 
                                v-for="item in showfaTypeList" 
                                :value="item.value" 
                                :label="item.label" 
                                :key="item.value"
                            ></Option>
                        </Select>
                    </div>
                    <span class="ml-10 tal">资产属性：</span>
                    <div style="width: 140px" class="category">
                        <Select
                            :teleported="false"
                            v-model="searchInfo.fa_property"
                            placeholder=" "
                            :fit-input-width="true"
                            :popper-append-to-body="false"
                            :clearable="true"
                            :IconClearRight="'24px'"
                            :circleClose="true"
                            @visible-change="visibleChange"
                            :filterable="true"
                            :filter-method="faPropertyFilterMethod"
                        >
                            <Option 
                                v-for="item in showfaPropertyList" 
                                :value="item.value" 
                                :label="item.label" 
                                :key="item.value"
                            ></Option>
                        </Select>
                    </div>
                    <span class="ml-10 tal">编号：</span>
                    <Tooltip :content="searchInfo.fa_num" :isInput="true" placement="right">
                        <el-input
                            v-model="searchInfo.fa_num"
                            placeholder=""
                            clearable
                            :class="{ 'ellipsis-text': true }"
                            style="width: 100px"
                        ></el-input>
                    </Tooltip>
                    <span class="ml-10 tal departmentSelect">部门：</span>
                    <div style="width: 107px">
                        <Select 
                            :teleported="false" 
                            v-model="searchInfo.department" 
                            :fit-input-width="true"
                            :filterable="true"
                            :filter-method="departmentFilterMethod"
                        >
                            <Option 
                                v-for="item in showdepartmentList" 
                                :value="item.value" 
                                :label="item.label" 
                                :key="item.value"
                            ></Option>
                        </Select>
                    </div>
                    <div class="buttons" style="border: 0">
                        <a class="button solid-button ml-10" @click="handleSearch">查询</a>
                    </div>
                    <SearchInfo
                        class="ml-10"
                        v-if="isErp"
                        :width="150"
                        :height="30"
                        :placeholder="'输入编号/名称/金额等关键字搜索'"
                        @search="handleSearchInfo"
                    ></SearchInfo>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <a class="button mr-10" @click="handleExport" v-permission="['depreciationledger-canexport']">导出</a>
                    <a class="button" @click="handlePrint" v-permission="['depreciationledger-canprint']">打印</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :data="tableData"
                    :columns="columns"
                    :loading="loading"
                    :pageIsShow="true"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :current-page="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :scrollbar-show="true"
                    :tableName="setModule"
                ></Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "DepreciationLedger",
};
</script>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import SearchInfo from "@/components/SearchInfo/index.vue";
import Select from "@/components/Select/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import Option from "@/components/Option/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { reactive, ref, watch, onMounted, computed, nextTick, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { usePagination } from "@/hooks/usePagination";
import { formatMoney } from "@/util/format";
import { getUrlSearchParams, globalExport, globalPrint } from "@/util/url";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IPeriod, ISelectItem, IQueryParams } from "./types";
import { request } from "@/util/service";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { onUnmounted } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { faPropertyList } from "../utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { dayjs } from "element-plus";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "DepreciationLedger";
const _ = getGlobalLodash();

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const loading = ref(false);

const isErp = ref(window.isErp);
const searchInfo = reactive({
    searchInfo: "",
    startPid: "",
    endPid: "",
    fa_type: "",
    fa_property: "",
    fa_num: "",
    department: "0",
    startMonth: "",
    endMonth: "",
});
function handleUserChange() {
    const startItem = periodList.value.find((item) => item.time === searchInfo.startMonth);  
    const endItem = periodList.value.find((item) => item.time === searchInfo.endMonth);  
    searchInfo.startPid = startItem?.pid?.toString() || '0';  
    searchInfo.endPid = endItem?.pid?.toString() || '0';
}
function handleSearch() {
    if (Number(searchInfo.startPid) > Number(searchInfo.endPid)) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哦" });
        return;
    } else {
        document.body.scrollTop = 0;
        getTableData();
    }
}

function handleSearchInfo(search: string) {
    searchInfo.searchInfo = search;
    getTableData();
}

const columns = ref<Array<IColumnProps>>([
    { label: "会计期间", prop: "period", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "period") },
    { label: "资产编号", prop: "fa_num", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "fa_num") },
    { label: "资产类别", prop: "fa_type_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "fa_type_name") },
    {
        label: "资产属性",
        prop: "fa_property",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "fa_property"),
        formatter(row, column, cellValue) {
            if(row.fa_type_name === '合计') return '';
            if (cellValue === 0) {
                return "固定资产";
            } else if (cellValue === 1) {
                return "无形资产";
            } else if (cellValue === 2) {
                return "长期待摊费用";
            } else {
                return "";
            }
        },
    },
    { label: "资产名称", prop: "fa_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "fa_name") },
    { label: "规格型号", prop: "fa_model", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "fa_model") },
    {
        label: "使用部门",
        prop: "department_name",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "department_name"),
        formatter: (row, column, value) => {
            return row.fa_num ? value + "(" + _.round(row.ratio * 100) + "%" + ")" : value;
        },
    },

    {
        label: "资产原值",
        prop: "value",
        align: "right",
        headerAlign: "right",
        width: getColumnWidth(setModule, "value"),
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
    {
        label: "本期折旧",
        prop: "depreciation_value",
        align: "right",
        headerAlign: "right",
        width: getColumnWidth(setModule, "depreciation_value"),
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
    {
        label: "本年累计折旧",
        prop: "yeardeprecition",
        align: "right",
        headerAlign: "right",
        width: getColumnWidth(setModule, "yeardeprecition"),
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
    {
        label: "累计折旧",
        prop: "totaldeprecition",
        align: "right",
        headerAlign: "right",
        width: getColumnWidth(setModule, "totaldeprecition"),
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
    {
        label: "减值准备",
        prop: "impairment",
        align: "right",
        headerAlign: "right",
        width: getColumnWidth(setModule, "impairment"),
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
    {
        label: "资产净值",
        prop: "zcjz",
        align: "right",
        headerAlign: "right",
        resizable: false,
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
]);

//导出
function handleExport() {
    const params = {
        PeriodS: searchInfo.startPid,
        PeriodE: searchInfo.endPid,
        FaNum: searchInfo.fa_num,
        FaType: searchInfo.fa_type,
        Department: searchInfo.department,
        SearchInfo: "",
    };
    globalExport("/api/DepreciationLedger/Export?" + getUrlSearchParams(params));
}

function handlePrint() {
    const params = {
        PeriodS: searchInfo.startPid,
        PeriodE: searchInfo.endPid,
        FaNum: searchInfo.fa_num,
        FaType: searchInfo.fa_type,
        Department: searchInfo.department,
        SearchInfo: "",
    };
    globalPrint("/api/DepreciationLedger/Print?" + getUrlSearchParams(params));
}

const periodList = ref<IPeriod[]>([]);
function getPeriodApi() {
    return request({
        url: `/api/FAPeriod/List?dpcFlag=true`,
        method: "get",
    });
}

const faTypeList = ref<ISelectItem[]>([]);
function getFaTypeApi() {
    request({
        url: `/api/FixedAssetsType/List`,
        method: "get",
    }).then((res: any) => {
        if (res.state == 1000) {
            faTypeList.value = res.data.reduce((prev: ISelectItem[], item: any) => {
                prev.push({
                    value: item.typeId,
                    label: item.typeNum + "-" + item.typeName,
                });
                return prev;
            }, []);
        }
    });
}

const departmentList = ref<ISelectItem[]>([{
    value: "0",
    label: "请选择"
}]);
function getDepartmentApi() {
    request({
        url: `/api/AssistingAccounting/DepartmentList?showAll=true&onlyLeaf=false`,
        method: "get",
    }).then((res: any) => {
        if (res.state == 1000) {
            let list = res.data.reduce((prev: ISelectItem[], item: any) => {
                if (item.aaeid > 0) {
                    prev.push({
                        value: item.aaeid,
                        label: item.aaname,
                    });
                }
                return prev;
            }, []);
            departmentList.value = [...departmentList.value, ...list];
        }
    });
}

const tableData = ref<any[]>([]);
function getTableData() {
    const params = {
        PeriodS: searchInfo.startPid,
        PeriodE: searchInfo.endPid,
        FaType: searchInfo.fa_type,
        FaProperty: searchInfo.fa_property,
        Department: searchInfo.department,
        FaNum: searchInfo.fa_num,
        SearchInfo: searchInfo.searchInfo,
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
    };
    loading.value = true;
    requestTableData(params);
}
const visibleChange = async (visible: boolean) => {
    await nextTick();
    if (visible) {
        // 打开下拉框时，滚动到列表顶部
        const popper = document.querySelector(".category .el-select-dropdown .el-scrollbar__wrap");
        if (popper) {
            popper.scrollTop = 0;
        }
    }
};
const requestTableData = (params: IQueryParams) => {
    return request({
        url: `/api/DepreciationLedger/PagingList?` + getUrlSearchParams(params),
    })
        .then((res: any) => {
            if (res.state === 1000) {
                if (res.data.data.length === 0) {
                    if (res.data.count === 0) {
                        tableData.value = res.data.data;
                        paginationData.total = res.data.count;
                        ElNotify({
                            message: "亲，您本期没有计提折旧数据哦",
                            type: "warning",
                        });
                    } else {
                        paginationData.total = res.data.count;
                        paginationData.currentPage = Math.ceil((Number(res.data.count) - 1) / paginationData.pageSize);
                        params.PageIndex = paginationData.currentPage;
                        requestTableData(params);
                    }
                } else {
                    tableData.value = res.data.data;
                    paginationData.total = res.data.count;
                }
            } else {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        })
        .finally(() => (loading.value = false));
};

const initSearchInfo = () => {
    return Promise.all([getPeriodApi(), getFaTypeApi(), getDepartmentApi()]).then((res: any) => {
        if (res[0].state == 1000) {
            periodList.value = res[0].data.map((item: any) => {  
                const year = item.periodInfo.slice(0, 4);
                const sn = item.periodInfo.indexOf("月") === 7 ? item.periodInfo.slice(5, 7) : item.periodInfo.slice(5, 6) ;
                return {  
                    pid: item.pid, 
                    periodInfo: item.periodInfo, 
                    time: year + "" + sn.padStart(2, "0"),
                };  
            });
            let findItem = res[0].data.find((v: any) => v.isDefault);
            if (findItem) {
                searchInfo.startPid = findItem.pid;
                searchInfo.endPid = findItem.pid;
            }
            if (searchInfo.startPid == "0") {
                searchInfo.startPid = (periodList.value as IPeriod[])[0].pid + "";
                searchInfo.endPid = (periodList.value as IPeriod[])[0].pid + "";
            }
        }
        getInitMonth();
        getTableData();
    });
};

function getInitMonth() {
    searchInfo.startMonth = periodList.value.find((item) => item.pid === Number(searchInfo.startPid))?.time || "";
    searchInfo.endMonth = periodList.value.find((item) => item.pid === Number(searchInfo.endPid))?.time || "";
}

function disabledDate(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

onMounted(() => {
    initSearchInfo();
    window.addEventListener("depreciation", initSearchInfo);
});
onUnmounted(() => {
    window.removeEventListener("depreciation", initSearchInfo);
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], getTableData);

const showfaTypeList = ref<Array<ISelectItem>>([]);
const showfaPropertyList = ref<Array<any>>([]);
const showdepartmentList = ref<Array<ISelectItem>>([]);
watchEffect(() => {   
    showfaTypeList.value = JSON.parse(JSON.stringify(faTypeList.value));
    showfaPropertyList.value = JSON.parse(JSON.stringify(faPropertyList)); 
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value)); 
});
function faTypeFilterMethod(value: string) {
    showfaTypeList.value = commonFilterMethod(value, faTypeList.value, 'label');
}
function faPropertyFilterMethod(value: string) {
    showfaPropertyList.value = commonFilterMethod(value, faPropertyList, 'label');
}
function departmentFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value, 'label');
}
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/FixedAssets/FixedMulti-tabs.less"; 

.main-tool-left {
    :deep(.el-input__prefix-inner > :last-child) {
        // margin-right: 0px !important;
    }
    :deep(.el-select) {
        .el-input__wrapper {
            padding: 1px 5px;
        }
    }
}
.ellipsis-text :deep(.el-input__inner) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tal {
    text-align: left;
    flex-shrink: 0;
}

:deep(.el-select-dropdown__list) {
    max-height: 200px;
}
:deep(.picker) {
    &.faSummary-pick {
        .el-input {
            width: 122px;
            .el-input__wrapper {
                width: 122px;
            }
        }
    }
}

</style>
