import { request, type IResponseModel } from "./service";

export enum WordCutCategory {
    Journal = 1010,
    Invoice = 1020,
}

export interface IWordCut {
    asId: string;
    hasChild: boolean;
    updateBy: number;
    lastUpdateTime: string;
    mainKey: number;
    subKey: number;
    subsubkey: number;
    value1: string;
    value2: string;
    value3: string;
    text1: string;
    text2: string;
    text3: string;
    matchKeys: Array<string>;
    matchKeywords: Array<string>;
    matchParties: Array<string>;
    matchPartiesWithType: Array<string>;
    num3: number;
}

export const tryUpdateWordCut = (category: number): Promise<IResponseModel<number>> => {
    return request({
        url: "/api/Intelligence/TryUpdateWordCut?category=" + category,
        method: "post",
    });
};

export const getWordCutList = (category: number): Promise<IResponseModel<Array<IWordCut>>> => {
    return request({
        url: "/api/Intelligence/WordCutList?category=" + category,
        method: "get",
    });
};