import { ref, onUnmounted, watch } from "vue"
import { IInvoiceTaskValue } from "@/api/invoiceAccess"
import { useBasicInfoStore } from "@/store/modules/basicInfo"
import { ElNotify } from "@/utils/notify"
import { IResponseModel, Jrequest } from "@/utils/service"

export interface IInvoiceTaskResult {
  asId: number
  startDate: number
  endDate: number
  createdBy: number
  createdDate: string
  modifiedDate: string
  status: number
  remark: string

  invoiceTaskStatus: number
  invoiceTaskRemark: string
  purchaseInvalidTotal: number
  purchaseInvalidTax: number
  purchaseInvalidAmount: number
  purchaseInvalidCount: number
  purchaseNormalTotal: number
  purchaseNormalTax: number
  purchaseNormalAmount: number
  purchaseNormalCount: number
  purchaseRedTotal: number
  purchaseRedTax: number
  purchaseRedAmount: number
  purchaseRedCount: number
  salesInvalidTotal: number
  salesInvalidTax: number
  salesInvalidAmount: number
  salesInvalidCount: number
  salesNormalTotal: number
  salesNormalTax: number
  salesNormalAmount: number
  salesNormalCount: number
  salesRedTotal: number
  salesRedTax: number
  salesRedAmount: number
  salesRedCount: number

  deductionTaskStatus: number
  deductionTaskRemark: string
  deductionCount: number
  deductionAmount: number

  fileTaskStatus: number
  fileTaskRemark: string
  fileCount: number

  createdByName: string
  taxLoginType: number
}

export const useTaxBureauLogin = () => {
  const { basicInfo } = storeToRefs(useBasicInfoStore())
  const taskStartLoading = ref(false)
  const hasInvoiceTask = ref(false)
  const timer = ref<number | null>(null)
  const taskCompleted = ref(false)
  const preCheck = false
  const invoiceTaskList = ref<any[]>([]) // Updated to use ref with type any[]
  // 监听任务状态变化
  watch(hasInvoiceTask, (newVal, oldVal) => {
    // 当任务从有变为无时，表示任务完成
    if (oldVal && !newVal) {
      taskCompleted.value = true
    }
  })

  const taxBureauLogin = async (startData: string, endDate: string) => {
    taskCompleted.value = false

    const data = {
      taxPayerName: basicInfo.value.taxpayerName,
      taxPayerNumber: basicInfo.value.taxNumberS,
      taxType: basicInfo.value.taxType,
      taxArea: basicInfo.value.taxadId,
      taxLoginType: basicInfo.value.taxBureauLoginType,
      taxAccount: basicInfo.value.taxBureauAccount,
      taxPayerPassword: basicInfo.value.taxpayerPassword,
      personPositionType: basicInfo.value.taxBureauPersonPositionType,
      personIdentityNo: basicInfo.value.taxBureauPersonIdentityNo,
      personAccount: basicInfo.value.taxBureauPersonAccount,
      personPassword: basicInfo.value.taxBureauPersonPassword,
      personPhone: basicInfo.value.taxBureauPersonPhone,
      startDate: startData,
      endDate: endDate,
      agentTaxNumber: basicInfo.value.taxBureauAgentTaxNumber,
    }

    const loginOnce = (retryCount = 0) => {
      taskStartLoading.value = true

      Jrequest({
        url: `${retryCount === 0 ? "/api/TaxBureau/Login" : "/api/TaxBureau/LoginState"}?isAuthorized=${basicInfo.value.taxBureauAuthorized}&taskSource=105`,
        data,
        method: "post",
      })
        .then((res: IResponseModel<IInvoiceTaskValue>) => {
          if (res.state === 1000) {
            if (res.data.taskId > 0 && res.data.result === "taskStart") {
              getInvoiceTaskList()
              return
            }
          } else if (res.state === 2000) {
            if (res.data.result === "countLimit" || res.data.result === "noBusinessType") {
              taskStartLoading.value = false
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "running" || res.data.result === "frequent") {
              taskStartLoading.value = false
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "authorizedError") {
              taskStartLoading.value = false
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "needUpdate") {
              taskStartLoading.value = false
              ElNotify({ type: "warning", message: "输入的个人账号或密码错误，请重新输入！", duration: 10000 })
              return
            } else if (res.data.result === "loginFail") {
              taskStartLoading.value = false
              ElNotify({ type: "warning", message: res.msg, duration: 10000 })
              return
            } else if (res.data.result === "waiting") {
              if (retryCount > 60) {
                taskStartLoading.value = false
                ElNotify({ type: "warning", message: "税局系统繁忙，登录超时，请稍等1分钟后重试", duration: 10000 })
                return
              } else {
                setTimeout(() => {
                  loginOnce(retryCount + 1)
                }, 5000)
                return
              }
            } else if (res.data.result === "error") {
              taskStartLoading.value = false
              ElNotify({ type: "warning", message: "登录发生错误，请稍后重试。", duration: 10000 })
              return
            }
          }
          return
        })
        .catch((err) => {
          console.log(err)
          taskStartLoading.value = false

          ElNotify({ type: "warning", message: "登录发生错误，请稍后重试。", duration: 10000 })
        })
    }

    loginOnce()
  }

  const getInvoiceTaskList = async (preCheck = false) => {
    if (!preCheck) taskStartLoading.value = true

    await Jrequest({
      url: `/api/TaxBureau/LastSummaryTask`,
    }).then((res: IResponseModel<Array<IInvoiceTaskResult>>) => {
      invoiceTaskList.value = res.data
      hasInvoiceTask.value = invoiceTaskList.value && invoiceTaskList.value[0] && invoiceTaskList.value[0].status === 1
      if (hasInvoiceTask.value) {
        reInvoiceTaskList()
      } else {
        if (!preCheck) {
          ElNotify({
            type: "success",
            message: "取票成功,自动刷新列表数据和页面按钮",
          })
        }
        unInvoiceTaskList()
      }
    })
  }

  const reInvoiceTaskList = () => {
    if (timer.value) return
    timer.value = window.setInterval(() => {
      getInvoiceTaskList()
    }, 8 * 1000)
  }

  const unInvoiceTaskList = () => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
    taskStartLoading.value = false
  }

  onUnmounted(() => {
    unInvoiceTaskList()
  })

  return {
    taxBureauLogin,
    taskStartLoading,
    hasInvoiceTask,
    taskCompleted,
    getInvoiceTaskList,
    invoiceTaskList,
  }
}
