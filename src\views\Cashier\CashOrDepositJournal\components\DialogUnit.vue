<template>
    <el-dialog
        v-model="show"
        :title="props.canAdd ? '选择往来单位' : '批量指定往来单位'"
        center
        width="706"
        :destroy-on-close="true"
        class="custom-confirm dialogDrag"
        @closed="handleClosed"
    >
        <div class="unit-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="unit-main">
                <div class="tabs-header">
                    <span
                        v-for="item in tabsColumns"
                        :key="item.value"
                        @click="handleTabClick(item.value)"
                        :class="{ selected: aaType === item.value }"
                    >
                        {{ item.label }}
                    </span>
                </div>
                <div class="tabs-top">
                    <div class="left">
                        <el-input v-model="opTypeSearchValCustomer" v-show="aaType === 10001" />
                        <el-input v-model="opTypeSearchValVendor" v-show="aaType === 10002" />
                        <el-input v-model="opTypeSearchValEmployee" v-show="aaType === 10003" />
                        <a class="button solid-button ml-10" @click="btnSearch">查询</a>
                    </div>
                    <div class="right" v-if="props.canAdd">
                        <a v-if="hasPermission()" class="button solid-button" @click="handleAddAAInfo">新增</a>
                    </div>
                </div>
                <div class="tabs-center">
                    <Table
                        v-show="aaType === 10001"
                        :max-height="300"
                        :loading="loading"
                        :data="tableData"
                        :columns="customerColumns"
                        :page-is-show="true"
                        :layout="paginationDataCustomer.layout"
                        :page-sizes="paginationDataCustomer.pageSizes"
                        :page-size="paginationDataCustomer.pageSize"
                        :total="paginationDataCustomer.total"
                        :currentPage="paginationDataCustomer.currentPage"
                        :scrollbar-show="true"
                        @size-change="handleSizeChangeCustomer"
                        @current-change="handleCurrentChangeCustomer"
                        @refresh="handleRerefreshCustomer"
                        @row-click="handleRowClick"
                        :tableName="setModule"
                    />
                    <Table
                        v-show="aaType === 10002"
                        :max-height="300"
                        :loading="loading"
                        :data="tableData"
                        :columns="customerColumns"
                        :page-is-show="true"
                        :layout="paginationDataSupplier.layout"
                        :page-sizes="paginationDataSupplier.pageSizes"
                        :page-size="paginationDataSupplier.pageSize"
                        :total="paginationDataSupplier.total"
                        :currentPage="paginationDataSupplier.currentPage"
                        :scrollbar-show="true"
                        @size-change="handleSizeChangeSupplier"
                        @current-change="handleCurrentChangeSupplier"
                        @refresh="handleRerefreshSupplier"
                        @row-click="handleRowClick"
                        :tableName="setModule"
                    />
                    <Table
                        v-show="aaType === 10003"
                        :max-height="300"
                        :loading="loading"
                        :data="tableData"
                        :columns="customerColumns"
                        :page-is-show="true"
                        :layout="paginationDataEmployee.layout"
                        :page-sizes="paginationDataEmployee.pageSizes"
                        :page-size="paginationDataEmployee.pageSize"
                        :total="paginationDataEmployee.total"
                        :currentPage="paginationDataEmployee.currentPage"
                        :scrollbar-show="true"
                        @size-change="handleSizeChangeEmployee"
                        @current-change="handleCurrentChangeEmployee"
                        @refresh="handleRerefreshEmployee"
                        @row-click="handleRowClick"
                        :tableName="setModule"
                    />
                </div>
                <el-checkbox v-if="!props.canAdd" v-model="override" class="ml-20 mt-10" :class="isErp ? 'fw-600' : ''">
                    覆盖原有往来单位
                </el-checkbox>
                <el-checkbox
                    v-if="isErp && aaType !== 10003"
                    v-model="isShowAmount"
                    class="ml-20 mt-10 fw-600"
                    @change="changeIsShowAmount">
                    显示应收应付余额
                </el-checkbox>
            </div>
            <div class="buttons">
                <a class="button" v-if="props.canAdd" @click="handleRowClick(null)">清空</a>
                <a class="button" v-else @click="handleSure">确定</a>
                <a class="button ml-20" @click="handleCancel">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog
        v-if="props.canAdd"
        v-model="aaDialogInfo.display"
        :title="'新增' + aaDialogInfo.dialogName"
        center
        width="440"
        :destroy-on-close="true"
        @close="cancelAddAssistingAccount"
        class="dialogDrag"
    >
        <div class="add-content" v-dialogDrag>
            <div class="add-main">
                <div class="add-input-item">
                    <span class="label">{{ aaDialogInfo.dialogName }}编码：</span>
                    <input
                        type="text"
                        v-model="aaFormInfo.code"
                        @input="handleAAInput(LimitCharacterSize.Code, $event, aaDialogInfo.dialogName + '编码', 'code', changeFormData)"
                        @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                    />
                </div>
                <div class="add-input-item">
                    <span class="label">{{ aaDialogInfo.dialogName }}名称：</span>
                    <el-autocomplete
                        ref="oppositePartyRef"
                        v-model="aaFormInfo.name"
                        :fetch-suggestions="querySearch"
                        :prop="[{ required: true, trigger: ['change'] }]"
                        :trigger-on-focus="false"
                        placeholder=" "
                        class="oppositePartyInput"
                        :debounce="300"
                        :teleported="true"
                        :fit-input-width="true"
                        popper-class="opposite-autocomplete-popper"
                        @select="(val: any)=>{ aaFormInfo.name = val.value }"
                        @input="InputMaxLength"
                        @keypress="PressMaxLength"
                    >
                        <template #default="{ item }">
                            <div class="value">{{ item.value }}</div>
                        </template>
                    </el-autocomplete>
                </div>
                <div class="add-input-item">
                    <span class="label">备注：</span>
                    <input
                        type="text"
                        v-model="aaFormInfo.note"
                        @input="handleAAInput(LimitCharacterSize.Note, $event, '备注', 'note', changeFormData)"
                        @paste="handleAAPaste(LimitCharacterSize.Note, $event)"
                    />
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="confirmAddAssistingAccount">保存</a>
                <a class="button ml-20" @click="cancelAddAssistingAccount">取消</a>
            </div>
        </div>
    </el-dialog>
    <AddAssistingAccountingEntryDialog
        v-if="props.canAdd"
        ref="addAssistingAccountingEntryDialogRef"
        :title="aaTitle"
        @save-success="handleSearch"
    ></AddAssistingAccountingEntryDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { usePagination } from "@/hooks/usePagination";
import { getGlobalToken } from "@/util/baseInfo";
import { checkPermission } from "@/util/permission";
import { getCompanyList } from "@/util/getCompanyList";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IUnitUpdateParams } from "../types";
import type { ICompanyInfo } from "@/api/getCompanyList";

import Table from "@/components/Table/index.vue";
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import { isNumberOrLetter } from "@/util/validator";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "CashBatchUnit";
interface ITableItem {
    type: string;
    address: string;
    contact: string;
    mobilePhone: string;
    taxNumber: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
    departmentId?: string;
}

let customerColumns = ref<IColumnProps[]>([]);
const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
const isErp = ref(window.isErp);
const props = defineProps<{
    canAdd: boolean;
    trySyncCompany?: (aaList: Array<{ aaeId: number; aaName: string }>) => void;
}>();
const loading = ref(false);
const _override = ref(true);
const emit = defineEmits<{
    (event: "update:modelValue", value: boolean): void;
    (event: "handle-sure", value: IUnitUpdateParams | null, opType: number): void;
    (event: "update:override", value: boolean): void;
    (event: "closed"): void;
}>();
const show = ref(false);
function changeDialogShow(val: boolean) {
    show.value = val;
    isShowAmount.value = window.localStorage.getItem("isShowAmount-" + getGlobalToken()) === "true";
    customerColumns.value = setColumns();
}

const override = computed({
    get() {
        return _override.value;
    },
    set(val) {
        _override.value = val;
        emit("update:override", val);
    },
});

const {
    paginationData: paginationDataCustomer,
    handleCurrentChange: handleCurrentChangeCustomer,
    handleSizeChange: handleSizeChangeCustomer,
    handleRerefresh: handleRerefreshCustomer,
} = usePagination();
const {
    paginationData: paginationDataSupplier,
    handleCurrentChange: handleCurrentChangeSupplier,
    handleSizeChange: handleSizeChangeSupplier,
    handleRerefresh: handleRerefreshSupplier,
} = usePagination();
const {
    paginationData: paginationDataEmployee,
    handleCurrentChange: handleCurrentChangeEmployee,
    handleSizeChange: handleSizeChangeEmployee,
    handleRerefresh: handleRerefreshEmployee,
} = usePagination();
const tabsColumns = [
    { label: "客户", value: 10001 },
    { label: "供应商", value: 10002 },
    { label: "职员", value: 10003 },
];
const isColumnsChange = () => {
    return isErp.value && isShowAmount.value && (aaType.value !== 10003);
}
const setColumns = (): IColumnProps[] => {
    let list: IColumnProps[] = [
        { label: "客户编码", align: "left", headerAlign: "left", minWidth: 120, prop: "aaNum", width: getColumnWidth(setModule, "aaNum", isColumnsChange() ? 120: 160) },
        {
            label: "客户名称",
            align: "left",
            headerAlign: "left",
            minWidth: 205,
            prop: "aaName",
            width: getColumnWidth(setModule, "aaName", isColumnsChange() ? 205 : 234),
        },
        { label: "备注", align: "left", headerAlign: "left", minWidth: 175, prop: "note", resizable: false },
    ];

    if (isColumnsChange()) {
        list[0].width = 120;
        list[1].width = 205;
        list[2].width = 175;
        const columnMap:{[key: number]: string} = {
            10001: "应收款余额",
            10002: "应付款余额"
        };
        const propMap:{[key: number]: string}  = {
            10001: "aramount",
            10002: "apamount"
        };
        list.splice(2, 0, {
            label: columnMap[aaType.value],
            align: "right",
            headerAlign: "right",
            prop: propMap[aaType.value],
            width: getColumnWidth(setModule, propMap[aaType.value], 166),
        });
    }
    return list;
};

const tableData = ref<ITableItem[]>([]);
const opTypeSearchVal = ref("");
const opTypeSearchValCustomer = ref("");
const opTypeSearchValVendor = ref("");
const opTypeSearchValEmployee = ref("");
const aaType = ref(10001);
const dialogLabel = computed(() => (aaType.value === 10001 ? "客户" : aaType.value === 10002 ? "供应商" : "职员"));
let filterData: IUnitUpdateParams | null = null;
const departmentList = computed(() => useAssistingAccountingStore().departmentList);

function handleClosed() {
    aaType.value = 10001;
    opTypeSearchValCustomer.value = "";
    opTypeSearchValVendor.value = "";
    opTypeSearchValEmployee.value = "";
    emit("closed");
}
const handleTabClick = (type: number) => (aaType.value = type);
const handleRowClick = (row: any) => {
    const data: ITableItem = row;
    let departIndex = -1;
    if (data && data.departmentId) {
        departIndex = departmentList.value.findIndex((item) => item.aaeid === Number(data.departmentId));
    }
    filterData = row ? {
        opposite_party: data.aaName,
        opposite_party_int: data.aaeId,
        ...(isErp.value && { departmentId: departIndex > -1 ? data.departmentId : "" })
    } : null;
    if (props.canAdd || filterData === null) {
        emit("handle-sure", filterData, aaType.value);
        handleCancel();
    }
};
const handleSearch = () => {
    getTableList(aaType.value, opTypeSearchVal.value);
};

const btnSearch = () => {
    if (aaType.value === 10001) {
        opTypeSearchVal.value = opTypeSearchValCustomer.value.trim();
        if (paginationDataCustomer.currentPage !== 1) {
            paginationDataCustomer.currentPage = 1;
            return;
        }
    } else if (aaType.value === 10002) {
        opTypeSearchVal.value = opTypeSearchValVendor.value.trim();
        if (paginationDataSupplier.currentPage !== 1) {
            paginationDataSupplier.currentPage = 1;
            return;
        }
    } else if (aaType.value === 10003) {
        opTypeSearchVal.value = opTypeSearchValEmployee.value.trim();
        if (paginationDataEmployee.currentPage !== 1) {
            paginationDataEmployee.currentPage = 1;
            return;
        }
    }
    handleSearch();
};
const getTableList = async (aatype: number, searchstr = "") => {
    let pageIndex = 1;
    let pageSize = 20;
    let pathName = "";
    switch (aatype) {
        case 10001:
            pageIndex = paginationDataCustomer.currentPage;
            pageSize = paginationDataCustomer.pageSize;
            pathName = "PagingCustomerList";
            break;
        case 10002:
            pageIndex = paginationDataSupplier.currentPage;
            pageSize = paginationDataSupplier.pageSize;
            pathName = "PagingVendorList";
            break;
        case 10003:
            pageIndex = paginationDataEmployee.currentPage;
            pageSize = paginationDataEmployee.pageSize;
            pathName = "PagingEmployeeList";
            break;
    }
    const urlPath = pathName + "?pageIndex=" + pageIndex + "&pageSize=" + pageSize;
    let url = "/api/AssistingAccounting/" + urlPath + "&showAll=false&status=0&hidedisable=true";
    if (searchstr) url += "&searchStr=" + searchstr;
    loading.value = true;
    request({ url })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                props.trySyncCompany?.(tableData.value);
                const total = res.data.count;
                if (aaType.value === 10001) {
                    paginationDataCustomer.total = total;
                } else if (aaType.value === 10002) {
                    paginationDataSupplier.total = total;
                } else if (aaType.value === 10003) {
                    paginationDataEmployee.total = total;
                }
            }
        })
        .finally(() => (loading.value = false));
};

let hasInit = false;
const handleInit = async () => {
    if (window.localStorage.getItem("opposityPageSize-" + getGlobalToken())) {
        paginationDataCustomer.pageSize = Number(window.localStorage.getItem("opposityPageSize-" + getGlobalToken()));
        paginationDataSupplier.pageSize = Number(window.localStorage.getItem("opposityPageSize-" + getGlobalToken()));
        paginationDataEmployee.pageSize = Number(window.localStorage.getItem("opposityPageSize-" + getGlobalToken()));
    }
    await getTableList(10001);
    hasInit = true;
};
function checkDialogUnitDisplay() {
    return show.value;
}
defineExpose({ handleInit, changeDialogShow, checkDialogUnitDisplay });

const handleSure = () => {
    if (!filterData) {
        ElNotify({ type: "warning", message: "请选择往来单位" });
        return;
    }
    emit("handle-sure", filterData, aaType.value);
    handleCancel();
};
const handleCancel = () => {
    paginationDataCustomer.currentPage = 1;
    paginationDataSupplier.currentPage = 1;
    paginationDataEmployee.currentPage = 1;
    opTypeSearchVal.value = "";
    filterData = null;
    show.value = false;
    opTypeSearchValCustomer.value = "";
    opTypeSearchValVendor.value = "";
    opTypeSearchValEmployee.value = "";
};

const isShowAmount = ref(false);
watch(isShowAmount, () => {
    handleSetColumns();
});
watch(aaType, (val) => {
    opTypeSearchVal.value = "";
    handleSetColumns();
    getTableList(val);
});
function handleSetColumns() {
    const aaName = dialogLabel.value;
    customerColumns.value = setColumns();
    customerColumns.value = customerColumns.value.map((item, index) => {
        if (index === 0) item.label = aaName + "编码";
        if (index === 1) item.label = aaName + "名称";
        return item;
    });
}
const changeIsShowAmount = () => {
    window.localStorage.setItem("isShowAmount-" + getGlobalToken(), String(isShowAmount.value));
};

watch([() => paginationDataCustomer.currentPage, () => paginationDataCustomer.pageSize, () => paginationDataCustomer.refreshFlag], () => {
    if (!hasInit) return;
    handleSearch();
});
watch([() => paginationDataSupplier.currentPage, () => paginationDataSupplier.pageSize, () => paginationDataSupplier.refreshFlag], () => {
    if (!hasInit) return;
    handleSearch();
});
watch([() => paginationDataEmployee.currentPage, () => paginationDataEmployee.pageSize, () => paginationDataEmployee.refreshFlag], () => {
    if (!hasInit) return;
    handleSearch();
});
const pageSize = ref(20);
watch(
    () => paginationDataCustomer.pageSize,
    (val) => {
        pageSize.value = val;
    }
);
watch(
    () => paginationDataSupplier.pageSize,
    (val) => {
        pageSize.value = val;
    }
);
watch(
    () => paginationDataEmployee.pageSize,
    (val) => {
        pageSize.value = val;
    }
);
watch(pageSize, (val) => {
    window.localStorage.setItem("opposityPageSize-" + getGlobalToken(), val + "");
});

// 添加辅助核算
function hasPermission() {
    if (isErp.value) {
        if (aaType.value === 10001) {
            return checkPermission(["Customers-编辑"]);
        } else if (aaType.value === 10002) {
            return checkPermission(["Vendors-编辑"]);
        } else if (aaType.value === 10003) {
            return checkPermission(["Employees-编辑"]);
        }
    } else {
        return checkPermission(["assistingaccount-canedit"]);
    }
}
const aaDialogInfo = reactive({
    display: false,
    dialogName: "",
});
const aaFormInfo = reactive({
    code: "1",
    name: "",
    note: "",
});
const aaTitle = ref("新增客户");
const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
function handleAddAAInfo() {
    if (isErp.value) {
        aaTitle.value = "新增" + dialogLabel.value;
        addAssistingAccountingEntryDialogRef.value?.showAADialog(aaType.value);
    } else {
        aaDialogInfo.dialogName = dialogLabel.value;
        request({
            url: "/api/AssistingAccounting/GetNewAANum?aaType=" + aaType.value + "&categoryId=0",
            method: "post",
        }).then((res: any) => {
            if (res.state == 1000) {
                aaDialogInfo.dialogName = dialogLabel.value;
                aaFormInfo.code = res.data ?? "1";
                aaDialogInfo.display = true;
            }
        });
    }
}
function querySearch(queryString: string, cb: any) {
    if (!queryString.trim() || queryString.length > LimitCharacterSize.Name) return;
    getCompanyList(1060, queryString, cb, queryParams);
}
function changeFormData(key: string, val: string) {
    const keys = Object.keys(aaFormInfo);
    const addOpTypeFormCopy: any = aaFormInfo;
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            addOpTypeFormCopy[key] = val;
            return;
        }
    }
}
function InputMaxLength(value: string) {
    if (value.length > LimitCharacterSize.Name) {
        let timer = setTimeout(() => {
            aaFormInfo.name = value.slice(0, LimitCharacterSize.Name);
            clearTimeout(timer);
        }, 0);
        ElNotify({
            type: "warning",
            message: `亲，${aaDialogInfo.dialogName}名称不能超过${LimitCharacterSize.Name}个字符!`,
        });
    }
}
function PressMaxLength(e: any) {
    const { value, selectionStart, selectionEnd } = e.target as HTMLInputElement;
    if (value.length > LimitCharacterSize.Name - 1) {
        if (selectionStart === selectionEnd) {
            e.preventDefault();
            ElNotify({
                type: "warning",
                message: `亲，${aaDialogInfo.dialogName}名称不能超过${LimitCharacterSize.Name}个字符!`,
            });
        }
    }
}
function confirmAddAssistingAccount() {
    if (!aaFormInfo.code.trim()) {
        ElNotify({ type: "warning", message: `${aaDialogInfo.dialogName}编码不能为空！` });
        return;
    }
    if (!aaFormInfo.name.trim()) {
        ElNotify({ type: "warning", message: `${aaDialogInfo.dialogName}名称不能为空！` });
        return;
    }
    if (!isNumberOrLetter(aaFormInfo.code)) {
        ElNotify({ type: "warning", message: `${aaDialogInfo.dialogName}编码不是数字和字母组合，请修改后重试~` });
        return;
    }
    if (aaFormInfo.name.length > LimitCharacterSize.Name) {
        ElNotify({ type: "warning", message: aaDialogInfo.dialogName + "名称不能超过" + LimitCharacterSize.Name + "个字符！" });
        return;
    }
    let params = {};
    if (aaType.value == 10001 || aaType.value == 10002) {
        const entityParams = {
            type: "",
            adLevel0: "",
            adLevel1: "",
            adLevel2: "",
            adLevel3: "",
            address: "",
            contact: "",
            mobilePhone: "",
            taxNumber: "",
            note: aaFormInfo.note,
        };
        params = {
            aaNum: aaFormInfo.code,
            aaName: aaFormInfo.name,
            uscc: "",
            status: 0,
            hasEntity: true,
            ifvoucher: true,
            entity: entityParams,
        };
    } else if (aaType.value == 10003) {
        const entityParams = {
            gender: "",
            departmentId: "",
            departmentName: "",
            title: "",
            position: "",
            mobilePhone: "",
            birthday: "",
            startDate: "",
            endDate: "",
            note: aaFormInfo.note,
        };
        params = {
            entity: entityParams,
            aaNum: aaFormInfo.code,
            aaName: aaFormInfo.name,
            uscc: "",
            status: 0,
            hasEntity: true,
            ifvoucher: true,
        };
    }
    const urlPath = aaType.value == 10001 ? "Customer" : aaType.value == 10002 ? "Vendor" : "Employee";
    const requestParams = {
        url: "/api/AssistingAccounting/" + urlPath,
        method: "post",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(params),
    };
    const successHandle = () => {
        submitAddAssistingAccount(requestParams);
    };
    createCheck(aaType.value, 0, aaFormInfo.code, aaFormInfo.name, successHandle);
}
function submitAddAssistingAccount(requestParams: any) {
    request(requestParams).then((res: any) => {
        if (res.state == 1000) {
            ElNotify({ type: "success", message: "保存成功" });
            aaDialogInfo.display = false;
            handleSearch();
            aaFormInfo.code = "1";
            aaFormInfo.name = "";
            aaFormInfo.note = "";
            useAssistingAccountingStore().getAssistingAccounting();
        }
    });
}
function cancelAddAssistingAccount() {
    aaDialogInfo.display = false;
    aaFormInfo.code = "1";
    aaFormInfo.name = "";
    aaFormInfo.note = "";
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.unit-content {
    .tabs-header {
        box-sizing: border-box;
        padding-left: 20px;
        padding-top: 10px;
        span {
            margin-left: 20px;
            display: inline-block;
            height: 22px;
            box-sizing: border-box;
            cursor: pointer;
            &:first-child {
                margin-left: 0;
            }
            &.selected {
                color: var(--main-color);
                border-bottom: 2px solid var(--main-color);
            }
        }
    }
    .tabs-top {
        margin: 12px 20px 0px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > div {
            display: flex;
            align-items: center;
        }
        .left {
            .detail-el-input(180px, 30px);
            :deep(.el-input__wrapper) {
                padding: 0 8px;
                .el-input__inner {
                    border: none;
                }
            }
        }
    }
    .tabs-center {
        margin: 10px 20px 20px 20px;
        :deep(.el-table) {
            border: none;
            .el-scrollbar__view {
                height: 240px;
            }
        }
    }
    .buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        box-sizing: border-box;
        border-top: 1px solid var(--border-color);
        margin-top: 10px;
    }
    &.erp {
        .tabs-center {
            margin: 16px 20px 0px 20px;
            :deep(.el-table) {
                box-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
            }
        }
        .buttons {
            .button {
                color: var(--blue);
                border-color: var(--blue);
            }
        }
    }
}

.add-content {
    .add-main {
        box-sizing: border-box;
        padding: 37px 70px;
        .add-input-item {
            margin-top: 20px;
            > span.label {
                text-align: right;
                width: 85px;
                display: inline-block;
                margin-right: 5px;
            }
            .detail-el-input(200px, 32px);
            &:first-child {
                margin-top: 0;
            }
        }
    }
    .buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        box-sizing: border-box;
        border-top: 1px solid var(--border-color);
    }

    input {
        .detail-original-input(200px, 32px);
    }
}

.fw-600 {
    font-weight: 600;
}
</style>
