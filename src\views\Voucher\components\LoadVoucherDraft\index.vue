<template>
    <el-dialog v-model="display" title="选择草稿" center width="800px" class="custom-confirm dialogDrag">
        <div class="voucherdraft-container" v-dialogDrag>
            <div class="table-container" :class="{ erp : isErp }">
                <Table
                    :empty-text="paginationData.total > 0 ? '' : '暂无数据'"
                    :data="voucherDrafts"
                    :columns="columns"
                    :min-height="407 - 31 + 'px'"
                    :max-height="407 - 31 + 'px'"
                    :loading="loading"
                    @row-click="onSelect"
                    @row-dblclick="onDblSelect"
                    @cell-mouse-enter="mouseEnter"
                    @cell-mouse-leave="mouseLeave"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :object-span-method="objectSpanMethod"
                    :row-class-name="rowClassName"
                    ref="table"
                    :tableName="setModule"
                >
                    <template #vdate>
                        <el-table-column 
                            label="日期" 
                            min-width="115" 
                            align="left" 
                            header-align="left"
                            prop="vdate"
                            :show-overflow-tooltip="false"
                            :width="getColumnWidth(setModule, 'vdate')"
                        >
                            <template #default="scope">
                                <Tooltip :content="scope.row.vdate" :dynamicWidth="true" :teleported="true" :offset="2" placement="right">
                                    <div class="text-overflow-ellipsis">{{ scope.row.vdate }}</div>
                                </Tooltip>
                            </template>
                        </el-table-column>
                    </template>
                    <template #operator>
                        <el-table-column 
                            label="操作" 
                            min-width="85" 
                            align="left" 
                            header-align="center"
                            :resizable="false"
                        >
                            <template #default="scope">
                                <a class="link" @click="deleteVoucherDraft(scope.row)">删除</a>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="buttons">
                <a class="button" @click="display = false">取消</a>
                <a class="button solid-button ml-10" @click="selectVoucherDraft()">确定</a>
            </div>
        </div>
    </el-dialog>
</template>
<style lang="less" scoped>
.voucherdraft-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    .table-container {
        padding: 10px;

        :deep(.el-table--enable-row-hover .el-table__body tr) {
            &.hover,
            &.selected {
                td.el-table__cell {
                    background-color: var(--table-hover-color);
                }
            }
        }
        :deep(.el-table .el-table__empty-block) {
            min-height: 338px;
        }
        
        &.erp {
            :deep(.el-table .el-table__empty-block) {
                width: 100% !important;
                min-height: 330px;
            }
        }
    }
    .buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        border-top: 1px solid var(--border-color);
        padding: 10px;
    }
}

:deep(.el-scrollbar__bar) {
    &.is-vertical {
        display: block !important;
        opacity: 1;
    }
}
body[erp] .custom-confirm .el-dialog__body .buttons {
    display: flex;
    justify-content: flex-end;
}
</style>
<script setup lang="ts">
import { ref } from "vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IVoucherDraftModel } from "@/components/Voucher/types";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { watch } from "vue";
import type { PagingResponse } from "@/util/pagingResponse";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import Tooltip from "@/components/Tooltip/index.vue";

const setModule = "LoadVoucherDraft";
const isErp = ref(window.isErp);
const emit = defineEmits<{
    (e: "onSelect", pid: number, vid: number): void;
}>();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const display = ref(false);
const voucherDrafts = ref(new Array<IVoucherDraftModel>());
const columns = ref<IColumnProps[]>([
    { slot: "vdate" },
    {
        label: "凭证字",
        prop: "vgName",
        align: "left",
        headerAlign: "left",
        minWidth: 66,
        formatter: (row: IVoucherDraftModel) => {
            return row.vgName + "-" + row.vnum;
        },
        width: getColumnWidth(setModule, "vgName")
    },
    {
        label: "摘要",
        prop: "description",
        align: "left",
        headerAlign: "left",
        minWidth: 134,
        formatter: (row: IVoucherDraftModel) => {
            return row.voucherEntryModel.description;
        },
        width: getColumnWidth(setModule, "description")
    },
    {
        label: "科目",
        prop: "asubName",
        align: "left",
        headerAlign: "left",
        minWidth: 161,
        formatter: (row: IVoucherDraftModel) => {
            return row.voucherEntryModel.asubName;
        },
        width: getColumnWidth(setModule, "asubName")
    },
    {
        label: "借方金额",
        prop: "debit",
        align: "left",
        headerAlign: "left",
        minWidth: 107,
        formatter: (row: IVoucherDraftModel) => {
            return row.voucherEntryModel.debit.toFixed(2);
        },
        width: getColumnWidth(setModule, "debit")
    },
    {
        label: "贷方金额",
        prop: "credit",
        align: "left",
        headerAlign: "left",
        minWidth: 107,
        formatter: (row: IVoucherDraftModel) => {
            return row.voucherEntryModel.credit.toFixed(2);
        },
        width: getColumnWidth(setModule, "credit")
    },
    { slot: "operator" },
]);
const loading = ref(false);
const table = ref<InstanceType<typeof Table>>();
const hoverRow = ref<IVoucherDraftModel>();
const selectedRow = ref<IVoucherDraftModel>();
const onSelect = (val: IVoucherDraftModel) => {
    selectedRow.value = val;
};
const onDblSelect = (val: IVoucherDraftModel) => {
    onSelect(val);
    selectVoucherDraft();
};
const objectSpanMethod = (data: { row: IVoucherDraftModel; rowIndex: number; column: any; columnIndex: number }) => {
    if (data.columnIndex === 0 || data.columnIndex === 1 || data.columnIndex === 6) {
        if (data.rowIndex !== 0) {
            if (
                voucherDrafts.value[data.rowIndex - 1].pid === data.row.pid &&
                voucherDrafts.value[data.rowIndex - 1].vid === data.row.vid
            ) {
                return {
                    rowspan: 0,
                    colspan: 0,
                };
            }
        }
        let rowspan = 1;
        for (let i = data.rowIndex + 1; i <= voucherDrafts.value.length; i++) {
            if (
                i === voucherDrafts.value.length ||
                voucherDrafts.value[i].pid !== data.row.pid ||
                voucherDrafts.value[i].vid !== data.row.vid
            ) {
                return {
                    rowspan: rowspan,
                    colspan: 1,
                };
            } else {
                rowspan++;
            }
        }
    }
};

const rowClassName = (data: { row: IVoucherDraftModel; rowIndex: number }) => {
    if (selectedRow.value && data.row.pid === selectedRow.value?.pid && data.row.vid === selectedRow.value?.vid) {
        return "selected";
    }
    if (hoverRow.value && data.row.pid === hoverRow.value?.pid && data.row.vid === hoverRow.value?.vid) {
        return "hover";
    }
};

const mouseEnter = (row: IVoucherDraftModel) => {
    hoverRow.value = row;
};

const mouseLeave = () => {
    hoverRow.value = undefined;
};

function showVoucherDrafts() {
    display.value = true;
    loadVoucherDrafts();
}

function loadVoucherDrafts() {
    loading.value = true;
    request({
        url: `/api/VoucherDraft/PagingList?pageIndex=${paginationData.currentPage}&pageSize=${paginationData.pageSize}`,
        method: "get",
    }).then((res: IResponseModel<PagingResponse<IVoucherDraftModel>>) => {
        if (res.state === 1000) {
            voucherDrafts.value = res.data.data;
            paginationData.total = res.data.count;
            selectedRow.value = undefined;
        }
        loading.value = false;
    });
}

function deleteVoucherDraft(voucherDraft: IVoucherDraftModel) {
    ElConfirm("亲，确定要删除吗？").then((r) => {
        if (r) {
            request({
                url: `/api/VoucherDraft?pid=${voucherDraft.pid}&vid=${voucherDraft.vid}`,
                method: "delete",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000 && res.data) {
                    voucherDrafts.value = voucherDrafts.value.filter(
                        (item) => item.pid !== voucherDraft.pid || item.vid !== voucherDraft.vid
                    );
                    paginationData.total--;
                    if (voucherDraft.pid === selectedRow.value?.pid && voucherDraft.vid === selectedRow.value?.vid) {
                        selectedRow.value = undefined;
                    }
                    ElNotify({ message: "删除成功", type: "success" });
                }
            });
        }
    });
}

function selectVoucherDraft() {
    if (!selectedRow.value) {
        ElNotify({ message: "请选择草稿", type: "warning" });
        return;
    }
    emit("onSelect", selectedRow.value.pid, selectedRow.value.vid);
    display.value = false;
}

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    loadVoucherDrafts();
});

function close() {
    display.value = false;
}
defineExpose({
    showVoucherDrafts,
    close,
});
</script>
