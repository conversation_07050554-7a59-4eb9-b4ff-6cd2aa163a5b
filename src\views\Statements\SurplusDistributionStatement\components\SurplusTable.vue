<template>
    <div class="main-center">
        <el-table
            :class="isErp ? 'erp-table' : ''"
            v-if="!props.tableData[0]?.surplusLineID"
            :data="props.tableData"
            border
            fit
            stripe
            scrollbar-always-on
            :empty-text="emptyText"
            highlight-current-row
            class="custom-table"
            row-key="lineID"
            :row-class-name="setTitleRowStyle"
            ref="tableRef"
            @cell-dblclick="handleCellClick"
            @header-dragend="headerDragend"
        >
            <el-table-column 
                label="项目" 
                min-width="340" 
                align="left" 
                headerAlign="center"
                prop="proName"
                :width="getColumnWidth(setModule, 'proName')"
            >
                <template #default="scope">
                    <div>
                        <div :class="assertNameClass(scope.row)" :title="scope.row.proName">
                            <span>{{ scope.row.proName }}</span>
                            <template v-if="assertShowEquationEdit(scope.row)">
                                <div
                                    class="link"
                                    v-permission="['balancesheet-canedit']"
                                    @click="
                                        openEquationDialog(scope.row.lineType, scope.row.lineID, scope.row.statementID, scope.row.proName)
                                    "
                                >
                                    编辑公式
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column 
                label="行次" 
                min-width="60" 
                align="left" 
                header-align="left" 
                prop="lineNumber" 
                :formatter="rowNumberFormatter"
                :width="getColumnWidth(setModule, 'lineNumber')"
            >
            </el-table-column>
            <template v-if="isShowSurplus">
                <el-table-column 
                    label="本年金额" 
                    min-width="280" 
                    align="right" 
                    header-align="right"
                    prop="total"
                    :width="getColumnWidth(setModule, 'total')"
                >
                    <template #header>
                      <div class="table-header">
                        <el-icon><Switch @click="swapColumns"/></el-icon>
                        <span>本年金额</span>
                      </div>
                    </template>
                    <template #default="scope">
                        <TableAmountItem
                            :amount="scope.row.total + ''"
                            :formula="calcFormula(scope.row, 2)"
                            :line-number="scope.row.lineNumber"
                            :queryFormulas="isQueryFormulas(scope.row, 2)"
                            @go-to-query-formulas="goToQueryFormulas(scope.row, 2)"
                        ></TableAmountItem>
                    </template>
                </el-table-column>
                <el-table-column 
                    label="上年金额" 
                    min-width="280" 
                    align="right" 
                    header-align="right"
                    :resizable="false"
                >
                    <template #default="scope">
                        <TableAmountItem
                            :amount="scope.row.inital + ''"
                            :formula="calcFormula(scope.row, 3) ? calcFormula(scope.row, 3) : calcFormula(scope.row, 2)"
                            :line-number="scope.row.lineNumber"
                            :queryFormulas="isQueryFormulas(scope.row, 3)"
                            @go-to-query-formulas="goToQueryFormulas(scope.row, 3)"
                        ></TableAmountItem>
                    </template>
                </el-table-column>
            </template>
            <template v-else>
                <el-table-column l
                    abel="上年金额" 
                    min-width="280" 
                    align="right" 
                    header-align="right"
                    prop="inital"
                    :width="getColumnWidth(setModule, 'inital')"
                >
                    <template #default="scope">
                        <TableAmountItem
                            :amount="scope.row.inital + ''"
                            :formula="calcFormula(scope.row, 3) ? calcFormula(scope.row, 3) : calcFormula(scope.row, 2)"
                            :line-number="scope.row.lineNumber"
                            :queryFormulas="isQueryFormulas(scope.row, 3)"
                            @go-to-query-formulas="goToQueryFormulas(scope.row, 3)"
                        ></TableAmountItem>
                    </template>
                </el-table-column>
                <el-table-column 
                    label="本年金额" 
                    min-width="280" 
                    align="right" 
                    header-align="right"
                    :resizable="false"
                >
                    <template #header>
                      <div class="table-header">
                        <el-icon><Switch @click="swapColumns"/></el-icon>
                        <span>本年金额</span>
                      </div>
                    </template>
                    <template #default="scope">
                        <TableAmountItem
                            :amount="scope.row.total + ''"
                            :formula="calcFormula(scope.row, 2)"
                            :line-number="scope.row.lineNumber"
                            :queryFormulas="isQueryFormulas(scope.row, 2)"
                            @go-to-query-formulas="goToQueryFormulas(scope.row, 2)"
                        ></TableAmountItem>
                    </template>
                </el-table-column>
            </template>
        </el-table>
        <el-table
            :class="isErp ? 'erp-table' : ''"
            v-if="props.tableData[0]?.surplusLineID"
            :data="props.tableData"
            border
            fit
            stripe
            highlight-current-row
            class="custom-table"
            row-key="lineID"
            @cell-dblclick="handleCellClick"
            @header-dragend="headerDragend"
        >
            <el-table-column 
                label="项目" 
                align="left" 
                headerAlign="center"
                prop="surplusProName"
                :width="getColumnWidth(setModule, 'surplusProName', 300)"
            >
                <template #default="scope">
                    <div>
                        <div :class="assertNameClass(scope.row)" :title="scope.row.surplusProName">
                            <span>{{ scope.row.surplusProName }}</span>
                            <template v-if="assertShowEquationEdit(scope.row)">
                                <div
                                    class="link"
                                    v-permission="['balancesheet-canedit']"
                                    @click="
                                        openEquationDialog(
                                            scope.row.surplusLineType,
                                            scope.row.surplusLineID,
                                            scope.row.statementID,
                                            scope.row.surplusProName
                                        )
                                    "
                                >
                                    编辑公式
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="行次"
                align="left"
                header-align="left"
                prop="surplusLineNumber"
                :formatter="rowNumberFormatter"
                :width="getColumnWidth(setModule, 'surplusLineNumber', 60)"
            >
            </el-table-column>
            <el-table-column 
                label="金额" 
                min-width="60" 
                align="right" 
                header-align="right"
                prop="surplusTotal"
                :width="getColumnWidth(setModule, 'surplusTotal')"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.surplusTotal + ''"
                        :formula="calcFormula(scope.row, 0)"
                        :line-number="scope.row.surplusLineNumber"
                        :queryFormulas="isQueryFormulas(scope.row, 0)"
                        @go-to-query-formulas="goToQueryFormulas(scope.row, 0)"
                    ></TableAmountItem>
                </template>
            </el-table-column>
            <el-table-column 
                label="项目" 
                align="left" 
                headerAlign="center"
                prop="surplusDistributionProName"
                :width="getColumnWidth(setModule, 'surplusDistributionProName', 300)"
            >
                <template #default="scope">
                    <div>
                        <div :class="assertNameClass(scope.row)" :title="scope.row.surplusDistributionProName">
                            <span>{{ scope.row.surplusDistributionProName }}</span>
                            <template v-if="assertShowEquationEdit(scope.row)">
                                <div
                                    class="link"
                                    v-permission="['balancesheet-canedit']"
                                    @click="
                                        openEquationDialog(
                                            scope.row.surplusDistributionLineType,
                                            scope.row.surplusDistributionLineID,
                                            scope.row.statementID,
                                            scope.row.surplusDistributionProName
                                        )
                                    "
                                >
                                    编辑公式
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="行次"
                align="left"
                header-align="left"
                prop="surplusDistributionLineNumber"
                :formatter="rowNumberFormatter"
                :width="getColumnWidth(setModule, 'surplusDistributionLineNumber', 60)"
            >
            </el-table-column>
            <el-table-column 
                label="金额" 
                min-width="60" 
                align="right" 
                header-align="right"
                :resizable="false"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.surplusDistributionTotal + ''"
                        :formula="calcFormula(scope.row, 1)"
                        :line-number="scope.row.surplusDistributionLineNumber"
                        :queryFormulas="isQueryFormulas(scope.row, 1)"
                        @go-to-query-formulas="goToQueryFormulas(scope.row, 1)"
                    ></TableAmountItem>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
import { ref,onBeforeMount } from "vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import { PeriodStatus } from "@/api/period";
import { computed } from "vue";
import { globalWindowOpenPage, getUrlSearchParams } from "@/util/url";
import type { ITableLineIDItem } from "@/views/Statements/types";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { checkPermission } from "@/util/permission";
import { handleCellClick, getSwitchState } from "@/views/Statements/utils";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "SurplusSheet";
const props = defineProps<{
    tableData: Array<any>;
    periodRef: any;
    emptyText: string;
    searchInfo: any;
    lineIDList: any;
}>();
const emit = defineEmits(["changeSlot"]);

const isErp = ref(window.isErp);
const isShowSurplus = ref(getSwitchState('isShowSurplus'));
const colunmOrderChange=ref('0')
const tableRef=ref()
const swapColumns = () => {
    isShowSurplus.value=!isShowSurplus.value
    localStorage.setItem('isShowSurplus',JSON.stringify(isShowSurplus.value))
    colunmOrderChange.value=(isShowSurplus.value?'0':'1')
    emit('colunmOrderChange', colunmOrderChange.value);
    tableRef.value.doLayout();
};
onBeforeMount(()=>{
    colunmOrderChange.value=(isShowSurplus.value?'0':'1')
    emit('colunmOrderChange', colunmOrderChange.value);
})
//行次
function rowNumberFormatter(_row: any, _column: any, cellValue: any) {
    if (cellValue === 0) {
        return "";
    } else {
        return cellValue;
    }
}
//是否显示编辑公式
const periodIsCheckOut = computed(() => {
    return props.periodRef?.periodStatus === PeriodStatus.CheckOut;
});
function assertShowEquationEdit(row: any) {
    if ((row.partentID == 0 && row.lineNumber != 1) || row.lineID === 50350130) {
        return false;
    } else {
        return !periodIsCheckOut.value;
    }
}

//判定assertnameclass
function assertNameClass(row: any) {
    let className: string;
    if (row.partentID === 0 || row.surplusPartentID == 0) {
        className = "level1 ";
    } else {
        className = "level2";
    }
    return className;
}
function setTitleRowStyle(data:any) {
    if (data.row.partentID === 0 || data.row.surplusPartentID == 0) {
        return 'highlight-title-row';
    }
}

//跳转编辑
function openEquationDialog(lineType: number, lineId: number, statementID: number, title: string) {
    emit("changeSlot", { lineType: lineType, lineId: lineId, statementID: statementID, title: title });
}
//公式
function calcFormula(row: any, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.surplusNote?.split("|")[0] ?? "";
            break;
        case 1:
            formula = row.surplusDistributionNote?.split("|")[0] ?? "";
            break;
        case 2:
            formula = row.note?.split("|")[0] ?? "";
            break;
        case 3:
            formula = row.note?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}
const accountingStandard = Number(useAccountSetStore().accountSet?.accountingStandard);
const isQueryFormulas = (row: any, columnIndex: number) => {
    if (props.lineIDList && checkPermission(['generalledger-canview'])) {
        let result;
        if (accountingStandard === 4) { //旧农合
            result = columnIndex === 0 ? props.lineIDList[row.surplusLineID] : props.lineIDList[row.surplusDistributionLineID];
        } else { //新农合
            result = props.lineIDList[row.lineID];
        }
        return (result?.length > 0) ? !!result[0].asubname : false;      
    } else {
        return false;
    }
}
const goToQueryFormulas = (row: any, columnIndex: number) => {
    let time = props.searchInfo.pid;
    if (columnIndex==3 && props.searchInfo.pid > 12 ) { //去年
        let y = Math.floor( (props.searchInfo.pid-1) / 12 );
        time = y > 1 ? 12*y : 12;
    }
    let result;
    if (accountingStandard === 4) {
        result = columnIndex === 0 ? props.lineIDList[row.surplusLineID] : props.lineIDList[row.surplusDistributionLineID];
    } else {
        result = props.lineIDList[row.lineID];
    }
    let SubjectIDList = '&SubjectIDList=' + result.map((item:ITableLineIDItem) => item.asubid).join(','); 
    let params = {
        period_s: time,
        period_e: time,
    }
    if(result.length > 0) {
        globalWindowOpenPage("/AccountBooks/GeneralLedger?" + getUrlSearchParams(params) + SubjectIDList, '总账');
    }
}
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style scoped lang="less">
@import "@/style/Statements/Statements.less";

</style>
