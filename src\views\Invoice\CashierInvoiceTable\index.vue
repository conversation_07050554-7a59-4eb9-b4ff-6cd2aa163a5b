<template>
    <div class="content">
        <div class="main-content">
            <div class="title">发票资金一览表</div>
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">日期：</div>
                            <div class="line-item-field">
                                <el-date-picker
                                    v-model="searchInfo.StartDate"
                                    type="month"
                                    placeholder="选择月份"
                                    :teleported="false"
                                    :format="'YYYY年MM月'"
                                    value-format="YYYY-MM"
                                    style="width: 140px"
                                />
                                &nbsp; 至 &nbsp;
                                <el-date-picker
                                    v-model="searchInfo.EndDate"
                                    type="month"
                                    placeholder="选择月份"
                                    :teleported="false"
                                    :format="'YYYY年MM月'"
                                    value-format="YYYY-MM"
                                    style="width: 140px"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">往来单位：</div>
                            <div class="line-item-field">
                                <SelectV2
                                    style="width: 215px"
                                    v-model="searchInfo.Name"
                                    placeholder="请选择"
                                    :teleported="false"
                                    :fit-input-width="true"
                                    :filterable="true"
                                    :clearable="true"
                                    :options="showCompanyList"
                                    :isOuterTooltip="false"
                                    :toolTipOptions="{ dynamicWidth: true }"
                                    :remote="companyList.length > 0"
                                    :filter-method="companyMethod"
                                    @visible-change="companyVisibleChange"
                                    :isSuffixIcon="true"
                                ></SelectV2>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">分析条件：</div>
                            <div class="line-item-field">
                                <div style="display: flex; padding-right: 5px">
                                    <el-select v-model="searchInfo.analysisConditions" :teleported="false">
                                        <el-option value="2" label="销项发票+资金收款"></el-option>
                                        <el-option value="1" label="进项发票+资金付款"></el-option>
                                        <el-option value="3" label="进销项发票+资金收付款"></el-option>
                                    </el-select>
                                </div>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.IsShowNum" label="显示发票张数、资金笔数"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons mt-10">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                </div>
                <div class="main-tool-right">
                    <a v-if="checkPermission(['cashierinvoicetable-canexport'])" class="button" @click="handleExport">导出</a>
                </div>
            </div>
            <div :class="['main-center', { erp: isErp }]">
                <Table
                    ref="TableCom"
                    style="width: 100%"
                    :loading="loading"
                    :data="mainTableData"
                    :columns="columns"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :scrollbarShow="true"
                    :showOverflowTooltip="true"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #operation>
                        <el-table-column label="操作" width="120" align="left" header-align="center" fixed="right" :resizable="false">
                            <template #default="scope">
                                <a class="link" @click="showDetail(scope.row)">查看明细</a>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "CashierInvoiceTable",
};
</script>
<script setup lang="ts">
import { ref, reactive, onMounted, watch, onUnmounted, watchEffect } from "vue";
import dayjs from "dayjs";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import Table from "@/components/Table/index.vue";
import { usePagination } from "@/hooks/usePagination";
import SelectV2 from "@/components/SelectV2/index.vue";
import { globalWindowOpenPage, getUrlSearchParams, globalExport } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import type { ICashierInvoiceTableItem } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElNotify } from "@/util/notify";
import { InvoiceCategoryList, CashierCategoryList, getCurrentMonth, getOppositePartyName, setCITColumns } from "../utils";
import { checkPermission } from "@/util/permission";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "CashierInvoice";
const isErp = window.isErp;
const periodStore = useAccountPeriodStore();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const currentPeriodInfo = ref("");

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();

// 搜索参数
const searchInfo = reactive({
    StartDate: dayjs().format("YYYY-MM"),
    EndDate: dayjs().format("YYYY-MM"),
    // 往来单位名称
    Name: "",
    analysisConditions: localStorage.getItem("CITAnalysisConditions") || "2",
    IsShowNum: false,
});

const searchParams = reactive({
    Name: searchInfo.Name,
    StartDate: searchInfo.StartDate,
    EndDate: searchInfo.EndDate,
    IsShowNum: searchInfo.IsShowNum,
    analysisConditions: searchInfo.analysisConditions,
    InvoiceCategory: 0,
    CashierCategory: 0,
});

const loading = ref(false);
const TableCom = ref<InstanceType<typeof Table>>();

const mainTableData = ref<ICashierInvoiceTableItem[]>([]);
const columns = ref<IColumnProps[]>([]);

const handleClose = () => {
    containerRef.value?.handleClose();
};

const handleReset = () => {
    searchInfo.StartDate = dayjs().format("YYYY-MM");
    searchInfo.EndDate = dayjs().format("YYYY-MM");
    searchInfo.Name = "";
    searchInfo.analysisConditions = "2";
    searchInfo.IsShowNum = false;
};

const handleSearch = () => {
    if (!searchInfo.StartDate || !searchInfo.EndDate) {
        return ElNotify({
            type: "warning",
            message: "起始月份或结束月份不能为空",
        });
    }
    if (new Date(searchInfo.StartDate).getTime() > new Date(searchInfo.EndDate).getTime()) {
        return ElNotify({
            type: "warning",
            message: "起始月份不能大于结束月份",
        });
    }
    currentPeriodInfo.value = getCurrentMonth(searchInfo.StartDate, searchInfo.EndDate);
    searchParams.StartDate = searchInfo.StartDate;
    searchParams.EndDate = searchInfo.EndDate;
    searchParams.IsShowNum = searchInfo.IsShowNum;
    searchParams.analysisConditions = searchInfo.analysisConditions;
    searchParams.InvoiceCategory = InvoiceCategoryList[searchInfo.analysisConditions as "1" | "2" | "3"];
    searchParams.CashierCategory = CashierCategoryList[searchInfo.analysisConditions as "1" | "2" | "3"];
    localStorage.setItem("CITAnalysisConditions", searchParams.analysisConditions);
    if (searchParams.Name !== searchInfo.Name) {
        searchParams.Name = searchInfo.Name;
        if (paginationData.currentPage === 1) {
            loadData();
        } else {
            paginationData.currentPage = 1;
        }
    } else {
        loadData();
    }
    handleClose();
};

const loadData = () => {
    let params = {
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        Name: searchParams.Name,
        StartDate: searchParams.StartDate,
        EndDate: searchParams.EndDate,
        IsShowNum: searchParams.IsShowNum,
        InvoiceCategory: searchParams.InvoiceCategory,
        CashierCategory: searchParams.CashierCategory,
    };
    loading.value = true;
    request({
        url: `/api/CashierInvoiceTable/PagingList`,
        method: "get",
        params,
    })
        .then((res: IResponseModel<{ rows: ICashierInvoiceTableItem[]; total: number }>) => {
            mainTableData.value = res.data.rows;
            paginationData.total = res.data.total;
            columns.value = setCITColumns(searchParams.IsShowNum, searchInfo.analysisConditions as "1" | "2" | "3");
        })
        .finally(() => {
            loading.value = false;
        });
};

const companyList = ref<Array<{ value: string; label: string }>>([]);
const getCompanyList = () => {
    getOppositePartyName().then((res: IResponseModel<string[]>) => {
        companyList.value = res.data.map((item) => ({ value: item, label: item }));
    });
};

const showDetail = (row: ICashierInvoiceTableItem) => {
    globalWindowOpenPage(
        `/Invoice/CashierInvoiceInfo?${getUrlSearchParams(searchParams)}&opposite_party=${row.opposite_party}&r=${Math.random()}`,
        "发票资金一览表明细"
    );
};

const handleExport = () => {
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可导出！", type: "warning" });
        return;
    }
    let params = {
        Name: searchParams.Name,
        StartDate: searchParams.StartDate,
        EndDate: searchParams.EndDate,
        IsShowNum: searchParams.IsShowNum,
        InvoiceCategory: searchParams.InvoiceCategory,
        CashierCategory: searchParams.CashierCategory,
    };
    globalExport(`/api/CashierInvoiceTable/Export?${getUrlSearchParams(params)}`);
};

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    loadData();
});

onMounted(() => {
    window.addEventListener("reloadOppositeParty", getCompanyList);
    currentPeriodInfo.value = getCurrentMonth(searchInfo.StartDate, searchInfo.EndDate);
    getCompanyList();
    handleSearch();
});
onUnmounted(() => {
    window.removeEventListener("reloadOppositeParty", getCompanyList);
});

const showCompanyList = ref<any[]>([]);
watchEffect(() => {
    showCompanyList.value = JSON.parse(JSON.stringify(companyList.value));
});
const companyMethod = (value: string) => {
    showCompanyList.value = commonFilterMethod(value, companyList.value, 'label');
}
const companyVisibleChange = (visible: boolean) => {
    if (visible) {
        showCompanyList.value = JSON.parse(JSON.stringify(companyList.value));
    }
}
</script>

<style lang="less" scoped>
@import url(@/style/SelfAdaption.less);

.content {
    .main-content {
        .main-top {
            .main-tool-left {
                :deep(.el-select-dropdown.select-down) {
                    height: 160px;
                }
            }
        }
        :deep(.el-select-v2__placeholder) {
            text-align: left;
        }
        .main-center {
            position: relative;
            box-sizing: border-box;
            :deep(.el-table) {
                .el-popper.is-light {
                    max-width: 300px;
                    text-align: left;
                }
            }
        }
    }
}
</style>
