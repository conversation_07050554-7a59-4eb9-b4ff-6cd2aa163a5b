export interface IBackUpItem {
    fileName: string;
    fileType: number;
    dateTime: string;
    fileSize: number;
    creator: string;
    opertion: number;
    progress: number;
    expiredDate: string;
    createBy: number;
}

export interface IDiskStateBack {
    totalSpace: number;
    lastTotalSpace: number;
    usedSpace: number;
    backUpUsedSpace: number;
    lemonDiskUsedSpace: number;
    endTime: string;
    startTime: string;
    spareDays: number;
    payYears: number;
    endTimeStr: string;
    endTimeStr2: string;
    state: number;
}

export interface IGetFileRecord {
    asid: number;
    fileName: string;
    createDate: string;
    fileSize: number;
    creator: string;
    createBy: number;
    type: number;
    progress: number;
    expiredDate: string;
}