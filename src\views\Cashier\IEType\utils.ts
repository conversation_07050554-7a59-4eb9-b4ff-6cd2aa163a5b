import { getNextCode } from "@/util/code";
import { request } from "@/util/service";

export const getNextIECode = (ieType: string) => {
    return new Promise<string>((resolve) => {
        request({ url: "/api/IEType/MaxNumber?ieType=" + ieType, method: "post" }).then((res) => {
            if (res.state == 1000) {
                return window.isErp ? resolve(res.data) : resolve(getNextCode(res.data, 4));
            } else {
                return resolve("");
            }
        });
    });
};
