import { ElNotify } from "@/util/notify";
import { isNumber, isNull } from "@/util/format";
import type { AccountingStandard } from "@/api/accountSet";

function invaildfixedTypeNum(target:string) {
    const reg = /^[0-9a-zA-Z]+$/;
    if (!reg.test(target)) {
        //之前代码存在，但会重复弹出
        // ElNotify({
        //     type:'warning',
        //     message: "编码只能为数字字母组合，请检查后重新尝试",
        // })
        return true;
    }
    return false;
}

export const ValidateFixedAssetType = (data: any,accountingStandard:AccountingStandard) => {
    const { fa_type_num, fa_type, monthes, netsalvage_rate, fa_asub, depreciation_asub, note, fa_property } = data;
    if (isNull(fa_type_num)) {
        ElNotify({
            type:'warning',
            message: "请输入资产类别编码！",
        })
        return false;
    }
    if (invaildfixedTypeNum(fa_type_num)) {
        ElNotify({
            type:'warning',
            message: "编码只能为数字字母组成，请检查后重新尝试",
        })
        return false;
    }
    if (isNull(fa_type)) {
        ElNotify({
            type:'warning',
            message: "请输入资产类别名称！",
        })
        return false;
    }
    if (fa_type_num.length > 5) {
        ElNotify({
            type:'warning',
            message: "资产类别编码为不超过5位的字符或数字组合",
        })
        return false;
    }
    if (isNull(monthes)) {
        ElNotify({
            type:'warning',
            message: "请输入使用月份！",
        })
        return false;
    }
    if (!isNumber(monthes.toString())) {
        ElNotify({
            type:'warning',
            message: "使用月份错误，请输入正整数！",
        })
        return false;
    }
    if(monthes > **********){
        ElNotify({
            type:'warning',
            message: "月份不能超过**********,请重新输入有效使用月份",
        })
        return false;
    }
    if (isNull(netsalvage_rate)) {
        ElNotify({
            type:'warning',
            message: "请输入预计净残值率！",
        })
        return false;
    }
    if (!isNumber(netsalvage_rate.toString())) {
        ElNotify({
            type:'warning',
            message: "预计净残值率错误，请输入正整数！",
        })
        return false;
    }
    if(netsalvage_rate > **********){
        ElNotify({
            type:'warning',
            message: "净残值率不能超过**********,请重新输入有效净残值率",
        })
        return false;
    }
    if (!fa_asub) {
        ElNotify({
            type:'warning',
            message: "请选择资产科目！",
        })
        return false;
    }
    if (!depreciation_asub && fa_property !== 2 && ![3,4,5].includes(accountingStandard)) {
        ElNotify({
            type:'warning',
            message: fa_property === 0 ? "请选择累计折旧科目！" :"请选择累计摊销科目！",
        })
        return false;
    }
    
    if (note.length > 64) {
        ElNotify({
            type:'warning',
            message: "亲，备注最多只能录入64个字符！",
        })
        return false;
    }
    return true;
};
