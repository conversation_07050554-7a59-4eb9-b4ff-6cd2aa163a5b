import type { IAsubRelationType } from "../AsubRelationSettings/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";

// 商品
enum CommodityAsubRelationType {
    // 存货
    Stock = 1010,
    // 收入
    Income = 1020,
    // 成本
    Cost = 1030,
}
// 客户
enum CustomerAsubRelationType {
    // 应收账款
    Receivable = 2010,
    // 预收账款
    Prepaid = 2020,
    // 其他应收
    OtherReceivable = 2030,
}
// 供应商
enum VendorAsubRelationType {
    // 应付账款
    Payable = 3010,
    // 预付账款
    Advance = 3020,
    // 其他应付
    OtherPayable = 3030,
}
// 职员
enum EmployeeAsubRelationType {
    // 会计科目
    Accounting = 8010,
}
// 银行账户
enum BankAsubRelationType {
    // 会计科目
    Accounting = 6010,
}
// 收入类别
enum IncomeAsubRelationType {
    // 会计科目
    Accounting = 4010,
}
// 支出类别
enum ExpenseAsubRelationType {
    // 会计科目
    Accounting = 5010,
}
export type AsubRelationType =
    | CommodityAsubRelationType
    | CustomerAsubRelationType
    | VendorAsubRelationType
    | EmployeeAsubRelationType
    | BankAsubRelationType
    | IncomeAsubRelationType
    | ExpenseAsubRelationType;

export interface IAsubRelationInfo {
    type: string;
    typeCode: AsubRelationTypeCode;
    asubs: Array<string>;
    explain: Array<string>;
    operation: string;
    asubRelationType: Array<AsubRelationType>;
    requestType: IRequestType;
}

export type AsubRelationTypeCode = keyof IAsubRelationType;

enum IRequestType {
    Commodity = 1030,
    Customer = 1010,
    Vendor = 1020,
    Employee = 1060,
    Account = 1040,
    Income = 1051,
    Expense = 1052,
}

export class DefaultAsubClass {
    1010 = "";
    1020 = "";
    1030 = "";
    2010 = "";
    2020 = "";
    2030 = "";
    3010 = "";
    3020 = "";
    3030 = "";
    8010 = "";
    4010 = "";
    5010 = "";
    6010 = "";
}

export const asubRelationInfo: Array<IAsubRelationInfo> = [
    {
        type: "商品",
        typeCode: "commodity",
        asubs: ["存货科目", "收入科目", "成本科目"],
        explain: [
            "商品发生出入库业务时，用来体现商品的增加/减少的科目。例如库存商品、原材料",
            "销售商品或提供劳务时所实现的经济利益流入的科目。例如主营业务收入",
            "销售商品或提供劳务时发生的实际成本的科目。例如主营业务成本",
        ],
        operation: "按商品类别设置科目",
        asubRelationType: [CommodityAsubRelationType.Stock, CommodityAsubRelationType.Income, CommodityAsubRelationType.Cost],
        requestType: 1030,
    },
    {
        type: "客户",
        typeCode: "customer",
        asubs: ["应收账款科目", "预收账款科目", "其他应收科目"],
        explain: [
            "销售商品或提供劳务时应向购货单位或服务接受单位收取但还未收取的款项的科目。例如应收账款",
            "提前从客户处收取但尚未交付商品或服务的款项的科目。例如预收账款",
            "除主营业务外的其他各种应收及暂付款项的科目。例如其他应收账款",
        ],
        operation: "按客户类别设置科目",
        asubRelationType: [CustomerAsubRelationType.Receivable, CustomerAsubRelationType.Prepaid, CustomerAsubRelationType.OtherReceivable],
        requestType: 1010,
    },
    {
        type: "供应商",
        typeCode: "vendor",
        asubs: ["应付账款科目", "预付账款科目", "其他应付科目"],
        explain: [
            "购买商品或接受劳务时需向供应商支付的未结款项的科目。例如应付账款",
            "预先支付给供应商但尚未获得商品或服务的款项的科目。例如预付账款",
            "除主营业务外的其他各种应付及尚未偿还的债务的科目。例如其他应付账款",
        ],
        operation: "按供应商类别设置科目",
        asubRelationType: [VendorAsubRelationType.Payable, VendorAsubRelationType.Advance, VendorAsubRelationType.OtherPayable],
        requestType: 1020,
    },
    {
        type: "职员",
        typeCode: "employee",
        asubs: ["会计科目"],
        explain: ["记录和反映员工与公司之间财务往来的科目。例如其他应付账款、其他应收账款"],
        operation: "按职员设置科目",
        asubRelationType: [EmployeeAsubRelationType.Accounting],
        requestType: 1060,
    },
    {
        type: "银行账户",
        typeCode: "account",
        asubs: ["会计科目"],
        explain: ["记录和反映企业或个人在银行的存款情况的科目。例如银行存款、库存现金"],
        operation: "按不同账户设置科目",
        asubRelationType: [BankAsubRelationType.Accounting],
        requestType: 1040,
    },
    {
        type: "收入类别",
        typeCode: "income",
        asubs: ["会计科目"],
        explain: ["用于分类和记录企业在不同经济活动中所产生收入的科目。例如其他业务收入"],
        operation: "按收入类别设置科目",
        asubRelationType: [IncomeAsubRelationType.Accounting],
        requestType: 1051,
    },
    {
        type: "支出类别",
        typeCode: "expense",
        asubs: ["会计科目"],
        explain: ["用于分类和记录企业在不同经济活动中发生的各种成本和费用的科目。例如其他业务成本"],
        operation: "按支出类别设置科目",
        asubRelationType: [ExpenseAsubRelationType.Accounting],
        requestType: 1052,
    },
];

export const asubRelationColumns: Array<IColumnProps> = [
    { label: "类别", prop: "type", headerAlign: "center", align: "center", width: 98 },
    { slot: "asubs" },
    { slot: "explain" },
    { slot: "operation" },
];

export const asubRelationActiveTabKey = "active";
