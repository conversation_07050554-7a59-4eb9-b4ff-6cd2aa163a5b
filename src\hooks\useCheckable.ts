export const useCheckable = () => {
  // 校验类型
  const checkType = ref<string>("")
  // 校验结果
  const checkResult = ref<boolean>(true)
  // 校验提示信息
  const checkPrompt = ref<string>("")

  function ruleToCell(validateMessages: any, ruleToCellRelationships: any, id: string) {
    // console.log(validateMessages, "validateMessages")

    validateMessages.reverse().forEach((item: any) => {
      const rule = ruleToCellRelationships.find((rule: any) => rule.NmyRuleMessage === item.content)
      rule?.RelateCells.forEach((cellInfo: any) => {
        if (id === `getObjFromList(${cellInfo.PreTable}, ${cellInfo.Key}, ${cellInfo.Value}).${cellInfo.Col}`) {
          checkType.value = item.type
          checkResult.value = false
          checkPrompt.value = item.content
        }
      })
    })
  }

  return {
    checkType,
    checkResult,
    checkPrompt,
    ruleToCell,
  }
}
