import dayjs from "dayjs";
export function getFirstAndLastDayOfMonth(dateStr: string): [string, string] {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const firstDayOfMonth = new Date(year, month - 1, 1);
    const lastDayOfNextMonth = new Date(year, month, 0);
    const firstDayOfMonthStr = dayjs(firstDayOfMonth).format("YYYY-MM-DD");
    const lastDayOfMonthStr = dayjs(lastDayOfNextMonth).format("YYYY-MM-DD");
    return [firstDayOfMonthStr, lastDayOfMonthStr];
}

//支持的日期字符串格式包括但不限于以下几种(不是太离谱的应该都能支持）：
//  "2024-05-09T12:00:00Z" "2024-5-1" "2024/5-1" "2024/5**1"
//  "Fri, 09 May 2024 12:00:00 GMT"
// 自定义格式 "YYYY-MM-DD HH:mm:ss"
export function formatDate(dateStr: string) {
    if (dateStr) {
        const formattedDate = dayjs(dateStr).format("YYYY-MM-DD");
        return formattedDate.toString() === "Invalid Date" ? "" : formattedDate;
    }
    return "";
}

// 目前是针对当前时间转化为年月日或者加时间的格式，后续可传日期进行完善
export function getFormatterDate(val: any = '',seperator = "-", showDetails = false) {
    const date = val ? new Date(val) : new Date();
    const y = date.getFullYear();
    const m = String(date.getMonth()+1).padStart(2, "0");
    const d = String(date.getDate()).padStart(2, "0");
    const nowDate = y + seperator + m + seperator + d;
    if (showDetails) {
        const hh = String(date.getHours()).padStart(2, "0");
        const mm = String(date.getMinutes()).padStart(2, "0");
        const ss = String(date.getSeconds()).padStart(2, "0");
        return nowDate + ` ${hh}:${mm}:${ss}`;
    } else {
        return nowDate;
    }
}

// 根据时间戳判断是否在24小时内
export function isWithin24Hours(createdDateTimestamp: number) { 
    const currentTimestamp = new Date().getTime();
    const timeDifference = currentTimestamp - createdDateTimestamp;
    const twentyFourHoursInMilliseconds = 24 * 60 * 60 * 1000; // 24小时对应的毫秒数
    return timeDifference <= twentyFourHoursInMilliseconds;
}