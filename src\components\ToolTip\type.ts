export type TooltipPlacement =
  | "top"
  | "top-start"
  | "top-end"
  | "bottom"
  | "bottom-start"
  | "bottom-end"
  | "left"
  | "left-start"
  | "left-end"
  | "right"
  | "right-start"
  | "right-end"

export interface TooltipProps {
  content?: string
  maxWidth?: number
  fontSize?: number
  fontFamily?: string
  lineClamp?: number
  isInput?: boolean
  shieldDistance?: number
  teleported?: boolean
  placement?: TooltipPlacement
  offset?: number
  class?: string
  effect?: "dark" | "light"
  dynamicWidth?: boolean
  popperClass?: string
  appendTo?: HTMLElement
}
