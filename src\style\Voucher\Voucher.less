@import "../Functions.less";

@table-border-color: #c0c0c0;
@error-border-color: red;
@dragger-border-color: var(--main-color);
@dragger-enter-color: var(--main-color);
.container {
    position: relative;
    // height: 100vh;
    // min-height: var(--voucher-min-height);

    .voucher-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: flex-start;
        background-color: var(--white);
        padding: 20px 54px;
        box-sizing: border-box;
        height: 100%;

        .voucher-button-line {
            margin-left: -80px;
            margin-right: -80px;
        }

        .voucher-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size);
            line-height: 22px;
            color: var(--font-color);
            &.voucher-bottomBtn {
                order: 1;
                margin-top: 20px;
            }

            .toolbar-left {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .voucher-vgname {
                    :deep(.el-select) {
                        width: 80px;
                        height: 28px;
                        .el-input__wrapper {
                            padding: 0 4px;
                            .el-input__suffix {
                                margin-left: 0;
                                .el-select__caret.el-icon {
                                    margin-left: 0;
                                }
                            }
                        }
                    }
                }

                .voucher-num {
                    display: flex;
                    align-items: center;
                    margin-left: 12px;
                    .detail-el-input-number(60px, 28px);

                    span {
                        margin-left: 8px;
                    }
                }

                .voucher-date {
                    display: flex;
                    align-items: center;
                    margin-left: 18px;
                    .detail-el-date-picker(130px, 28px);
                }
            }

            .tips {
                color: #ff821c;
            }

            .toolbar-right {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .voucher-readonly-tip {
                    position: relative;
                    align-self: flex-start;

                    img {
                        width: 118px;
                        height: 57px;
                        position: absolute;
                        right: 0;
                        top: -20px;
                    }
                }

                .voucher-attachments {
                    display: flex;
                    align-items: center;
                    .detail-el-input-number(60px, 28px);

                    span {
                        &:first-child {
                            margin-right: 8px;
                        }

                        &:last-child {
                            margin-left: 8px;
                        }
                    }
                }

                .voucher-attachfiles {
                    margin-left: 18px;
                    line-height: 22px;
                }

                .voucher-note {
                    margin-left: 18px;
                    line-height: 22px;
                }

                .voucher-help {
                    width: 14px;
                    height: 14px;
                    margin-left: 18px;
                    cursor: pointer;
                    background-repeat: no-repeat;
                    background-size: 100%;
                    background-image: url("@/assets/Voucher/help.png");

                    &:hover {
                        background-image: url("@/assets/Voucher/help-hover.png");
                    }
                }

                .voucher-zoom-btn,
                .voucher-zoom-screen-btn {
                    margin-left: 18px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    font-size: var(--font-size);
                    color: var(--font-color);
                    line-height: 20px;
                    &::before {
                        content: " ";
                        width: 17px;
                        height: 17px;
                        background-repeat: no-repeat;
                        background-size: 100%;
                        margin-right: 7px;
                    }

                    &:hover {
                        color: var(--main-color);
                    }
                    &.full-screen {
                        color: var(--main-color);
                        &::before {
                            width: 20px;
                            height: 20px;
                            background-image: url("@/assets/Voucher/suoxiao.png");
                        }
                        &.erp {
                            &::before {
                                background-image: url("@/assets/Voucher/erp-suoxiao.png");
                            }
                        }
                    }
                    &.exit-full-screen {
                        color: var(--font-color);
                        &::before {
                            width: 19px;
                            height: 19px;
                            background-image: url("@/assets/Voucher/fangda.png");
                        }
                        span {
                            position: relative;
                            top: 1px;
                        }
                    }
                }
            }
        }

        .voucher-table {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            border-top: 1px solid @table-border-color;
            border-bottom: 1px solid @table-border-color;
            margin-top: 17px;
            max-height: calc(100% - 17px - 28px - 40px);
            box-sizing: border-box;
            position: relative;
            .voucherline-description,
            .voucherline-total,
            .voucherline-asub,
            .voucherline-amount {
                position: relative;
            }
            .adaptive-container {
                flex: 1;
                display: flex;
            }
            .voucherline-description,
            .voucherline-total {
                flex: 1;
                width: 0;
                min-width: 205px;
            }
            .voucherline-order-number {
                border-right: 1px solid @table-border-color;
                border-left: 1px solid @table-border-color;
                width: 32px;
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .voucherline-asub {
                flex: 1;
                width: 0;
                border-left: 1px solid @table-border-color;
                min-width: 366px;
            }

            .voucherline-amount {
                width: 219px;
                border-left: 1px solid @table-border-color;
                &.credit-amount {
                    border-right: 1px solid @table-border-color;
                }
            }

            .voucher-amount-lines {
                display: flex;
                align-items: stretch;

                span {
                    width: 19px;
                    border-right: 1px solid #ededed;
                    font-size: var(--h5);
                    color: var(--font-color);
                    line-height: 17px;
                    font-weight: 400;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    &:nth-child(3),
                    &:nth-child(6) {
                        border-right-color: #bde3f7;
                    }

                    &:nth-child(9) {
                        border-right-color: #f2cbca;
                    }

                    &:last-child {
                        border-right: none;
                    }
                }

                &.negative {
                    span {
                        color: var(--red);
                    }
                }
            }

            .voucher-title {
                display: flex;
                align-items: stretch;
                height: 59px;
                border-bottom: 1px solid @table-border-color;
                flex-shrink: 0;
                &.bottom-red {
                    border-bottom: 1px solid @error-border-color;
                }
                &.bottom-dragger {
                    border-bottom: 1px solid @dragger-border-color;
                }
                &.dragger-enter-border {
                    border-bottom: 2px solid @dragger-enter-color;
                }
                .voucherline-order-number,
                .voucherline-description,
                .voucherline-asub,
                .voucherline-amount {
                    font-size: var(--font-size);
                    color: var(--font-color);
                    line-height: 20px;
                    font-weight: 600;
                }
                .voucherline-order-number,
                .voucherline-description,
                .voucherline-asub {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .draggable-line {
                    width: 0;
                    height: var(--cell-height);
                    flex-shrink: 0;
                    border-left: 1px solid transparent;
                    margin-left: -1px;
                    position: relative;
                    z-index: 49;
                    cursor: col-resize;
                }
                .voucherline-amount {
                    display: flex;
                    flex-direction: column;
                    align-items: stretch;

                    .voucherline-amount-title {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 29px;
                        border-bottom: 1px solid @table-border-color;
                    }

                    .voucher-amount-lines {
                        flex: 1;
                    }
                }
            }

            .voucherlines-container {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                max-height: calc(100% - 60px - 70px);
            }

            .voucherline {
                min-height: 69px;
                max-height: 119px;
                display: flex;
                align-items: stretch;
                border-bottom: 1px solid transparent;
                position: relative;
                padding: 0 20px;
                margin: 0 -20px;

                &::after {
                    content: " ";
                    position: absolute;
                    bottom: -1px;
                    left: 20px;
                    right: 20px;
                    height: 1px;
                    background-color: @table-border-color;
                }
                &.error-last-row::after {
                    background-color: @error-border-color;
                }
                &.dragger-last-row::after {
                    background-color: @dragger-border-color;
                }
                &.dragger-enter-last-row::after {
                    height: 2px;
                    background-color: @dragger-enter-color;
                }

                .voucherline-display {
                    color: var(--font-color);
                    line-height: 20px;
                    font-size: 13px;
                    font-weight: bold;
                    padding-left: 5px;
                    word-break: break-all;
                    text-align: left;
                    max-height: 100%;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 5;
                }

                .voucherline-textarea {
                    z-index: 2;
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    display: none;

                    textarea {
                        color: var(--font-color);
                        line-height: 20px;
                        font-size: 13px;
                        font-weight: bold;
                        width: 100%;
                        height: 100%;
                        box-sizing: border-box;
                        resize: none;
                        border: 1px solid var(--main-color);
                        border-radius: 0;
                        padding: 0 0 0 5px;
                        outline: none;
                        vertical-align: top;
                    }
                }

                .voucherline-description,
                .voucherline-asub {
                    &.editing {
                        .voucherline-textarea {
                            display: block;
                        }
                    }
                }

                .voucherline-asub {
                    display: flex;
                    flex-direction: column;
                    align-items: stretch;

                    .voucherline-display {
                        min-height: 100%;
                        box-sizing: border-box;
                        word-break: break-all;
                    }

                    .voucherline-asub-flow {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-end;
                        position: absolute;
                        bottom: 20px;
                        right: 0;
                        z-index: 1;

                        .voucherline-asub-quantityaccounting,
                        .voucherline-asub-foreigncurrency {
                            align-self: flex-end;
                            display: flex;
                            align-items: center;

                            .voucherline-asub-fc {
                                display: flex;

                                select {
                                    height: 20px;
                                    width: 60px;
                                    outline: none;
                                    border: 1px solid var(--border-color);

                                    &:hover,
                                    &:focus {
                                        border-color: var(--main-color);
                                    }
                                }
                            }

                            .voucherline-asub-quantity,
                            .voucherline-asub-price,
                            .voucherline-asub-fcrate,
                            .voucherline-asub-fcamount {
                                display: flex;
                                align-items: center;
                                white-space: nowrap;

                                span {
                                    font-size: var(--h5);
                                    color: var(--font-color);
                                    line-height: 20px;
                                    font-weight: 400;
                                }

                                &.editing {
                                    .voucherline-asub-display {
                                        display: none;
                                    }

                                    .voucherline-asub-input {
                                        display: flex;
                                    }
                                }
                            }

                            .voucherline-asub-quantity,
                            .voucherline-asub-price {
                                .cost-accounting-icon {
                                    height: 14px;
                                    width: 14px;
                                    margin-left: 5px;
                                    cursor: pointer;
                                }
                            }
                        }

                        .voucherline-asub-input {
                            display: none;
                            .detail-input(56px, 20px);
                        }
                    }

                    .voucherline-asub-amount {
                        font-weight: bold;
                        line-height: 20px;
                        font-size: var(--h5);
                        color: var(--font-color);
                        padding-left: 5px;
                        text-align: left;
                        position: absolute;
                        bottom: 1px;
                        left: 1px;
                        right: 18px;
                        background-color: var(--white);

                        span {
                            font-size: 13px;
                            cursor: pointer;

                            &:hover {
                                text-decoration: underline;
                            }
                        }

                        &.negative {
                            span {
                                color: var(--red);
                            }
                        }
                    }

                    &.locked {
                        background-color: #f0f0f0;

                        .voucherline-asub-amount {
                            background-color: #f0f0f0;
                        }
                    }
                }

                .voucherline-amount {
                    display: flex;
                    align-items: stretch;

                    .voucher-amount-lines {
                        span {
                            font-family: "tahoma";
                            font-weight: bold;
                            font-size: 14px;
                        }
                    }

                    .voucherline-amount-input {
                        position: absolute;
                        top: 0;
                        left: 0;
                        bottom: 0;
                        right: 0;
                        display: none;

                        input[type="text"] {
                            color: var(--font-color);
                            font-weight: bold;
                            font-size: 16px;
                            width: 100%;
                            height: 100%;
                            box-sizing: border-box;
                            border: 1px solid transparent;
                            border-radius: 0;
                            padding-left: 5px;
                            outline: none;

                            &:focus {
                                border-color: var(--main-color);
                            }

                            &::-webkit-outer-spin-button,
                            &::-webkit-inner-spin-button {
                                -webkit-appearance: none;
                            }
                        }
                    }

                    &.editing {
                        .voucherline-amount-input {
                            display: flex;
                        }
                    }

                    &.locked {
                        background-color: #f0f0f0;
                    }
                }

                .voucherline-total {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    padding-left: 24px;
                    font-weight: 600;
                    font-size: var(--font-size);
                    color: var(--font-color);
                    line-height: 20px;
                }

                &.voucher-total-line {
                    border-bottom: none;
                    flex-shrink: 0;

                    &::after {
                        top: -1px;
                        bottom: auto;
                    }
                    .voucherline-total,
                    .voucherline-amount {
                        background-color: #fffbf7;
                    }
                    .voucherline-total {
                        border-left: 1px solid @table-border-color;
                    }
                }
                .voucherline-copy-btn,
                .voucherline-add-btn,
                .voucherline-del-btn {
                    width: 16px;
                    cursor: pointer;
                    background-size: 16px 16px;
                    background-repeat: no-repeat;
                    background-position: center;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    z-index: 998;
                    display: none;

                    &.erp {
                        background-size: 20px 20px;
                        width: 20px;
                    }
                }

                .voucherline-add-btn {
                    height: 35%;
                    top: 10%;
                    left: 0;
                    background-image: url("@/assets/Voucher/add-icon.png");

                    &.erp {
                        left: -4px;
                        background-image: url("@/assets/Voucher/erp-add-icon.png");
                    }
                }
                .voucherline-copy-btn {
                    left: 0;
                    height: 35%;
                    top: 56%;
                    background-image: url("@/assets/Voucher/copy-icon.png");
                    &.erp {
                        left: -3px;
                    }
                }
                .voucherline-del-btn {
                    right: 0;
                    background-image: url("@/assets/Voucher/del-icon.png");

                    &.erp {
                        right: -4px;
                        background-image: url("@/assets/Voucher/erp-del-icon.png");
                    }
                }

                &:hover {
                    .voucherline-add-btn,
                    .voucherline-copy-btn,
                    .voucherline-del-btn {
                        display: block;
                    }
                }
            }

            &.toolbar-hidden {
                max-height: calc(100% - 17px);
            }
            .error-border {
                .voucherline {
                    &::after {
                        background-color: @error-border-color;
                    }
                    .voucherline-order-number {
                        border-left: 1px solid @error-border-color;
                    }
                    .voucherline-amount.credit-amount {
                        border-right: 1px solid @error-border-color;
                    }
                }
            }
            .error-border-top {
                .voucherline {
                    &::after {
                        background-color: @error-border-color;
                    }
                }
            }
            .dragger-border {
                .voucherline {
                    &::after {
                        background-color: @dragger-border-color;
                    }
                    .voucherline-order-number {
                        border-left: 1px solid @dragger-border-color;
                    }
                    .voucherline-amount.credit-amount {
                        border-right: 1px solid @dragger-border-color;
                    }
                    cursor: move;
                }
            }
            .dragger-border-top {
                .voucherline {
                    &::after {
                        background-color: @dragger-border-color;
                    }
                }
            }
            .dragger-enter-border {
                .voucherline {
                    &::after {
                        height: 2px;
                        background-color: @dragger-enter-color;
                    }
                }
            }
            .move-line {
                cursor: move;
                background-color: rgba(238, 238, 238, 0.8);
                .voucherline-asub-amount {
                    background-color: rgba(238, 238, 238, 0.8) !important;
                }
                :deep(.tip-container) {
                    display: none;
                }
            }
            .assit-col-line {
                position: absolute;
                top: 0;
                left: 20px;
                bottom: 0;
                border-left: 1px dashed var(--table-border-color);
                display: none;
            }
        }

        .voucher-preparedby {
            margin-top: 20px;
            font-size: var(--font-size);
            color: var(--font-color);
            line-height: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .detail-el-input(100px, 28px);

            .voucher-preparedby-item {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                span.name {
                    min-width: 98px;
                    text-align: left;
                }
            }

            span.icon {
                width: 12px;
                height: 13px;
                background-repeat: no-repeat;
                background-size: 100%;
                background-image: url("@/assets/Voucher/edit-preparedby.png");
                cursor: pointer;
            }
        }

        &.zoom-out {
            .voucher-toolbar {
                min-width: 1051px;
                .toolbar-right {
                    .voucher-zoom-btn {
                        &::before {
                            background-image: url("@/assets/Voucher/zoom-in.png");
                        }

                        &:hover {
                            &::before {
                                background-image: url("@/assets/Voucher/zoom-in-hover.png");
                            }
                        }
                    }
                }
            }

            .voucher-table {
                min-width: 1051px;
            }

            .voucher-preparedby {
                min-width: 1051px;
            }
        }

        &.zoom-in {
            .voucher-toolbar {
                width: 1051px;
                align-self: center;

                .toolbar-right {
                    .voucher-zoom-btn {
                        &::before {
                            background-image: url("@/assets/Voucher/zoom-out.png");
                        }

                        &:hover {
                            &::before {
                                background-image: url("@/assets/Voucher/zoom-out-hover.png");
                            }
                        }
                    }
                }
            }

            .voucher-table {
                width: 1051px;
                align-self: center;
            }

            .voucher-preparedby {
                width: 1051px;
                align-self: center;
            }
        }
    }

    .voucherline-del-btn {
        right: -16px;
        background-image: url("@/assets/Voucher/del-icon.png");

        &.erp {
            background-image: url("@/assets/Voucher/erp-del-icon.png");
            width: 20px;
            background-size: 20px 20px;
            right: -20px;
        }
    }

    .deslist-selector-container {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        background-color: var(--white);
        border: 1px solid @table-border-color;
        max-height: 180px;
        overflow: auto;

        div {
            word-break: break-all;
            line-height: 22px;
            font-size: var(--h4);
            color: var(--font-color);
            cursor: pointer;
            padding: 0 10px;
            text-align: left;

            &.selected {
                background-color: var(--border-color);
            }

            &:hover {
                color: var(--white);
                background-color: var(--main-color);
            }

            &:active {
                background-color: var(--dark-main-color);
            }
        }
    }

    .asub-selector-container {
        position: absolute;
        z-index: 3;
        min-width: 368px;

        .asub-selector {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            background-color: var(--white);
            border: 1px solid @table-border-color;
            box-sizing: border-box;

            .asubtypes {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                padding: 11px 12px 8px;
                border-bottom: 1px solid @table-border-color;

                .asubtype {
                    font-size: var(--h4);
                    color: var(--font-color);
                    line-height: 17px;
                    cursor: pointer;
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .ai-suggestion{
                        // 和line-height一致
                        height: 17px;
                    }

                    &::after {
                        content: " ";
                        height: 2px;
                        width: 28px;
                        margin-top: 4px;
                        background-color: transparent;
                    }

                    & + .asubtype {
                        margin-left: 14px;
                    }

                    &.selected {
                        color: var(--main-color);

                        &::after {
                            background-color: var(--main-color);
                        }
                    }
                }
            }

            .asubs-content {
                max-height: 180px;
                overflow: auto;
                position: relative;

                div {
                    color: var(--font-color);
                    line-height: 28px;
                    font-size: var(--h4);
                    text-align: left;
                    padding: 0 10px;
                    cursor: pointer;
                    word-break: break-all;
                    &.selected {
                        background-color: var(--border-color);
                    }

                    &:hover {
                        color: var(--white);
                        background-color: var(--main-color);
                    }

                    &:active {
                        background-color: var(--dark-main-color);
                    }
                }
            }

            .add-btn {
                text-align: center;
                font-size: var(--font-size);
                line-height: 30px;
                height: 30px;
                border-top: 1px solid @table-border-color;
            }
        }

        .asub-aa-selector {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            background-color: var(--white);
            // width: 290px;
            border: 1px solid @table-border-color;
            padding: 10px;
            // margin-top: 12px;
            overflow: hidden;
            table {
                width: 100%;
                tr {
                    td {
                        height: 30px;
                        padding: 0;

                        &:first-child {
                            line-height: 20px;
                            font-size: var(--h4);
                            color: var(--font-color);
                            padding-right: 10px;
                            text-align: left;
                        }

                        &:last-child {
                            .detail-el-input(100%, 21px);
                            text-align: left;
                        }
                    }
                }
            }
        }
    }

    .aae-selector-container {
        position: absolute;
        z-index: 3;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        background-color: var(--white);
        border: 1px solid @table-border-color;
        width: 170px;

        .aaes {
            max-height: 180px;
            overflow: auto;
            position: relative;

            div {
                word-break: break-all;
                line-height: 22px;
                font-size: var(--h5);
                color: var(--font-color);
                cursor: pointer;
                padding: 0 10px;
                text-align: left;
                &.selected {
                    background-color: var(--border-color);
                }

                &:hover {
                    color: var(--white);
                    background-color: var(--main-color);
                }

                &:active {
                    background-color: var(--dark-main-color);
                }

                &.no-data {
                    color: var(--font-color);
                    background-color: var(--white);
                    text-align: center;
                    height: 30px;
                    line-height: 30px;
                }
            }
        }

        .add-btn {
            cursor: pointer;
            background-color: var(--main-color);
            font-size: var(--h5);
            line-height: 25px;
            color: var(--white);
            text-align: center;
            border-top: 1px solid @table-border-color;

            &:active {
                background-color: var(--dark-main-color);
            }
        }
    }

    .note-container {
        position: absolute;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        background-color: var(--white);
        border: 1px solid @table-border-color;
        width: 240px;
        height: 120px;

        textarea {
            outline: none;
            padding: 10px 10px 15px 10px;
            height: 100%;
            color: var(--font-color);
            font-size: var(--h5);
            line-height: var(--line-height);
            text-align: left;
            border: none;
            resize: none;
        }
    }

    .fa-confirm-container {
        position: relative;
        .close {
            position: absolute;
            top: -52px;
            right: 0;
            height: 52px;
            width: 52px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            .el-icon {
                color: #909399;
                font-size: 16px;
            }
            &:hover {
                .el-icon {
                    color: var(--main-color);
                }
            }
        }
        .fa-confirm-main {
            padding: 40px 70px;
            text-align: left;
            .txt {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                word-break: break-all;
                word-wrap: break-word;
            }
            &.reverse {
                padding: 20px 50px !important;
                .txt {
                    line-height: 20px;
                    font-size: 14px;
                    &.b {
                        font-size: 13px;
                        font-weight: 600;
                        background: url("@/assets/Icons/hint.png") no-repeat;
                        background-size: 14px 14px;
                        background-position: 0 3px;
                        padding-left: 20px;
                    }
                }
                .tip-content {
                    margin: 16px auto 0;
                    width: 388px;
                    height: 124px;
                    background-color: #fbfbfb;
                    padding: 19px 0 16px 45px;
                    box-sizing: border-box;
                    .txt {
                        position: relative;
                        line-height: 18px;
                        font-size: 13px;
                        color: #666666;
                        &::before {
                            width: 14px;
                            height: 17px;
                            content: " ";
                            display: inline-block;
                            position: absolute;
                            left: -20px;
                            top: 1px;
                            background-repeat: no-repeat;
                        }
                        &:nth-child(1) {
                            &::before {
                                background-image: url("@/assets/Icons/1.png");
                            }
                        }
                        &:nth-child(2) {
                            &::before {
                                background-image: url("@/assets/Icons/2.png");
                            }
                        }
                        &:nth-child(3) {
                            &::before {
                                background-image: url("@/assets/Icons/3.png");
                            }
                        }
                    }
                }
                .bug-link {
                    width: 388px;
                    margin: 8px auto 0;
                    text-align: right;
                    line-height: 17px;
                    font-size: 12px;
                    color: #666666;
                    .link {
                        font-size: 12px;
                    }
                }
            }
        }
        .buttons {
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid var(--border-color);
        }
    }

    .cost-accounting-settings-dialog {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 999;

        .dialog-shadow {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-color: transparent;
        }

        .cost-accounting-settings-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;

            .triangle-container {
                position: relative;
                height: 7px;
                align-self: stretch;

                .triangle-outer,
                .triangle-inner {
                    height: 0;
                    width: 0;
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    position: absolute;
                    left: 198px;
                }

                .triangle-outer {
                    border-bottom: 7px solid var(--border-color);
                    top: 0;
                }

                .triangle-inner {
                    border-bottom: 7px solid var(--white);
                    top: 1px;
                }
            }

            .cost-accounting-settings-content {
                border: 1px solid var(--border-color);
                box-shadow: 0px 2px 2px 0px rgba(173, 187, 200, 0.19);
                background-color: var(--white);
                width: 400px;

                .dialog-title {
                    font-weight: bold;
                    color: var(--font-color);
                    font-size: var(--h3);
                    line-height: var(--line-height);
                    padding: 20px 0;
                    margin-left: 50px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .link {
                        font-weight: 400;
                    }
                }

                .form-item {
                    display: flex;
                    align-items: center;
                    margin-left: 50px;

                    .input-title {
                        color: var(--font-color);
                        font-size: var(--font-size);
                        line-height: var(--line-height);
                    }

                    & + .form-item {
                        margin-top: 10px;
                    }
                }

                .buttons {
                    margin: 20px;
                    text-align: center;
                    padding: 0;
                    border: none;
                }
            }
        }
    }

    .amount-calculator-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;

        .calculator-example {
            margin-top: 30px;
            color: var(--font-color);
            font-size: 13px;
            line-height: 18px;
            padding: 0 20px;
        }

        .calculator-content {
            margin: 6px 0;
            padding: 0 20px;

            textarea {
                resize: none;
                outline: none;
                border: 1px solid var(--main-color);
                color: var(--font-color);
                font-size: 22px;
                line-height: 30px;
                padding: 14px;
                width: 100%;
                height: 109px;
                box-sizing: border-box;
                font-weight: bold;
            }
        }

        .calculat-result {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            flex-wrap: wrap;

            .tip {
                color: #fd7400;
                font-size: 13px;
                line-height: 18px;
                text-align: left;
                margin-right: auto;
            }

            .result-content {
                display: flex;
                color: var(--font-size);
                font-size: 13px;
                line-height: 33px;
                text-align: right;
                margin-left: auto;

                span {
                    color: var(--main-color);
                    line-height: 33px;
                    font-size: 24px;
                    font-weight: bold;
                }
            }
        }

        .buttons {
            margin-top: 30px;
            text-align: center;
            padding: 10px 0;
            border-top: 1px solid var(--border-color);
        }
    }

    .amount-zoomout-container {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 999;
        height: 60px;
        width: 220px;
        padding-right: 10px;
        border: 1px solid var(--border-color);
        background-color: var(--white);
        display: flex;
        align-items: center;
        justify-content: flex-end;

        span {
            line-height: 60px;
            font-size: 30px;
            color: var(--font-color);
            box-sizing: border-box;
            position: relative;

            &.highlight {
                animation: amountHighlight 0.2s ease-in-out;
            }

            &.twinkle {
                &::after {
                    content: "";
                    height: 60%;
                    position: absolute;
                    top: 20%;
                    right: 0;
                    background-color: #333;
                    animation: amountTwinkle 0.8s infinite;
                    animation-fill-mode: forwards;
                }
            }
        }

        &.twinkle-before {
            span {
                &:first-child {
                    &::before {
                        content: "";
                        height: 60%;
                        position: absolute;
                        top: 20%;
                        left: 0;
                        background-color: #333;
                        animation: amountTwinkle 0.8s infinite;
                        animation-fill-mode: forwards;
                    }
                }
            }
        }
    }

    .amount-tip-container {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 998;
        height: 60px;
        width: 250px;
        padding-right: 10px;
        border: 1px solid var(--border-color);
        background-color: var(--white);
        display: flex;
        align-items: center;
        line-height: 20px;
        font-size: var(--font-size);
        text-align: left;
        padding: 10px;
        box-sizing: border-box;
        transform: translateX(10%);
        span.allow {
            display: inline-block;
            width: 8px;
            height: 8px;
            transform: rotate(45deg);
            border-top: 1px solid var(--border-color);
            border-left: 1px solid var(--border-color);
            box-sizing: border-box;
            position: absolute;
            top: -5px;
            right: 0;
            bottom: 0;
            left: 40px;
            z-index: 999;
            background-color: var(--white);
        }
    }
}

.main-container .right-container .router-container .container {
    :deep(.el-overlay) {
        left: calc(140px + var(--router-container-margin));
        top: var(--top-value);
        right: var(--router-container-margin);
        bottom: var(--router-container-margin);
        position: fixed;
    }
}

body[erp] {
    .container {
        .voucher-container {
            .voucher-toolbar {
                .toolbar-right {
                    .voucher-help {
                        &:hover {
                            background-image: url("@/assets/Voucher/help-hover-erp.png");
                        }
                    }
                }
            }

            &.zoom-out {
                .voucher-toolbar {
                    .toolbar-right {
                        .voucher-zoom-btn {
                            &::before {
                                background-image: url("@/assets/Voucher/zoom-in.png");
                            }

                            &:hover {
                                &::before {
                                    background-image: url("@/assets/Voucher/zoom-in-hover-erp.png");
                                }
                            }
                        }
                    }
                }
            }

            &.zoom-in {
                .voucher-toolbar {
                    .toolbar-right {
                        .voucher-zoom-btn {
                            &::before {
                                background-image: url("@/assets/Voucher/zoom-out.png");
                            }

                            &:hover {
                                &::before {
                                    background-image: url("@/assets/Voucher/zoom-out-hover-erp.png");
                                }
                            }
                        }
                    }
                }
            }
        }
        .aae-selector-container {
            width: 188px;
            .aaes {
                div {
                    line-height: 28px;
                    font-size: var(--h4);
                }
            }
        }
    }
}

@keyframes amountHighlight {
    0% {
        font-size: 30pt;
        color: blue;
    }
    100% {
        font-size: 30px;
        color: var(--font-color);
    }
}

@keyframes amountTwinkle {
    0%,
    50% {
        width: 2px;
    }
    51%,
    100% {
        width: 0;
    }
}

:deep(.el-input__inner) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.asub {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 显示的行数 */
    padding: 0 !important;
}
.aatype-tr {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    .input-td {
        width: auto !important;
        height: auto !important;
        flex: 1;
    }
    :deep(.el-input) {
        width: 100% !important;
    }
}
.abstract-list {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    padding: 0 !important;
}
