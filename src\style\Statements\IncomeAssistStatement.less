@import "./Statements.less";
@import "../Functions.less";
@import "../SelfAdaption.less";
@import "../Common.less";
body {
    .downlist .setting-option {
        margin-left: 20px;
        .sheet {
            display: flex;
            .detail-el-select(132px);
            :deep(.el-select) {
                // .el-input__wrapper {
                //     padding: 0 8px;
                // }
                .el-input__suffix-inner > :first-child {
                    margin-left: 0;
                }
            }
            span {
                display: inline-block;
                width: 87px;
                line-height: 32px;
                text-align: left;
                &.select-label {
                    color: #333;
                    // font-weight: 500;
                    font-size: 14px;
                }
            }
            :deep(.el-checkbox) {
                font-weight: normal !important;
            }
            .accounting-item {
                position: relative;
                width: 298px;

                .chexk-box {
                    width: 100%;
                    border: 1px solid var(--border-color);
                    box-sizing: border-box;
                    background-color: var(--white);
                    max-height: 200px;
                    overflow: auto;
                    padding-left: 8px;
                    z-index: 100;
                    display: flex;
                    flex-direction: column;
                    position: absolute;
                    top: 32px;
                    left: 0;
                    border-top: none;
                }
                .top-box {
                    box-sizing: border-box;
                    border: 1px solid var(--border-color);
                    height: 32px;
                    padding-left: 8px;
                    padding-right: 20px;
                    display: flex;
                    align-items: center;
                    position: relative;
                    cursor: pointer;
                    input {
                        width: 298px;
                        height: 32px;
                        border: 1px solid var(--input-border-color);
                        border-left: none;
                        border-right: none;
                        outline: none;
                        padding-left: 10px;
                        padding-right: 0px !important;
                        background-color: #16b0a3;
                        color: var(--font-color);
                        font-size: var(--font-size);
                        line-height: 32px;
                        box-sizing: border-box;
                        border-radius: var(--input-border-radius);
                    }
                    .bg-click {
                        position: absolute;
                        right: 0;
                        top: 0;
                        background-image: url("@/assets/Icons/down-black.png");
                        background-position: right 5px top 10px;
                        background-repeat: no-repeat;
                        height: 100%;
                        width: 20px;
                        &.open {
                            background-image: url("@/assets/Icons/up-black.png");
                        }
                    }
                }
            }
        }
        .buttons {
            padding-left: 25px;
            text-align: center;
            .button {
                margin-right: 10px;
            }
        }
    }
}
.main-top {
    background-color: white;
    display: flex;
    justify-content: space-between;
    .main-tool-left {
        display: flex;
        align-items: center;
        :deep(.pagination-period-button) {
            position: relative;
        }
    }
    .main-tool-right {
        position: relative;
        a.button {
            width: 84px;
        }
        div.button {
            border-radius: 2px;
            height: 26px;
            width: 84px;
            line-height: 26px;
            font-size: 13px;
            display: inline-block;
            cursor: pointer;
            text-align: center;
            padding: 0;
            outline: none;
            transition: var(--transition-time);
            color: var(--font-color);
            border: 1px solid var(--button-border-color);
            background-color: var(--white);
            background-image: url("@/assets/Icons/down-black.png");
            background-position-x: 84px;
            background-position-y: center;
            background-repeat: no-repeat;
            &.large {
                width: 104px;
            }
        }
        &:hover {
            .show {
                visibility: visible;
            }
            div.button {
                background-color: var(--main-color);
                color: var(--white);
                background-image: url("@/assets/Icons/down-white.png");
            }
        }
        .show {
            display: flex;
            flex-direction: column;
            visibility: hidden;
            box-shadow: 0 0 4px var(--button-border-color);
            border-radius: 2px;
            position: absolute;
            width: 100%;
            top: 28px;
            left: 0;
            z-index: 10;
            span {
                color: var(--font-color);
                height: 27px;
                width: 100%;
                line-height: 27px;
                font-size: 13px;
                cursor: pointer;
                background-color: var(--white);
                &:hover {
                    // border-color: var(--table-hover-color);
                    background-color: var(--table-hover-color);
                    background-color: var(--main-color);
                    color: var(--white);
                }
            }
        }
    }
}
:deep(.el-table) {
    .el-table__body-wrapper{
        padding-bottom:14px;
    }
    .el-table__expand-icon {
        transform: rotate(90deg);
        margin-top: 3px;
        color: #fff;
        .el-icon {
            width: 1em;
            height: 1em;
            margin: 0 !important;
            border-radius: 50%;
            background-color: #2abe2a;
        }
        &.el-table__expand-icon--expanded {
            transform: rotate(-90deg) !important;
        }
    }
    .el-scrollbar {
        position: static;
    }
    .el-scrollbar__bar.is-horizontal {
        height: 10px;
        border-radius: 10px;
        display: block;
        position: absolute;
        z-index: 30;
        // position: relative;
        // top: 0;
    }
}
:deep(.main-center) {
    tr {
        td {
            .cell {
                //这里样式已经控制好了，禁用掉element的样式
                .el-table__indent {
                    display: none !important;
                }

                .el-table__placeholder {
                    display: none;
                }

                .el-table__expand-icon {
                    position: absolute;
                }

                .el-icon {
                    margin-top: 2px;
                }

                .level2 {
                    span {
                        white-space: normal;
                    }
                }

                // .level3.pl-60 {
                //     padding-left: 60px;
                // }
            }
        }
    }
}
