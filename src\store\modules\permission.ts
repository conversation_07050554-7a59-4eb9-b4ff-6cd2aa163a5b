import { ref } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import type { RouteRecordRaw } from "vue-router";
import { asyncRoutes, constantRoutes } from "@/router";

const hasPermission = (permissions: string[], route: RouteRecordRaw) => {
    if (route.meta && route.meta.permissions) {
        return permissions.some((permission) => {
            if (route.meta?.permissions !== undefined) {
                return route.meta.permissions.includes(permission);
            } else {
                return false;
            }
        });
    } else {
        return false;
    }
};

const filterAsyncRoutes = (routes: RouteRecordRaw[], permissions: string[]) => {
    const res: RouteRecordRaw[] = [];
    routes.forEach((route) => {
        const r = { ...route };
        if (hasPermission(permissions, r)) {
            if (r.children) {
                r.children = filterAsyncRoutes(r.children, permissions);
            }
            res.push(r);
        }
    });
    return res;
};

export const usePermissionStore = defineStore("permission", () => {
    const routes = ref<RouteRecordRaw[]>([]);
    const dynamicRoutes = ref<RouteRecordRaw[]>([]);

    const setRoutes = (permissions: string[]) => {
        const accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions);
        routes.value = constantRoutes.concat(accessedRoutes);
        dynamicRoutes.value = accessedRoutes;
    };

    return { routes, dynamicRoutes, setRoutes };
});

/** 在 setup 外使用 */
export function usePermissionStoreHook() {
    return usePermissionStore(store);
}
