<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef"></PaginationPeriodPicker>
                            <CheckOutTooltip v-if="periodIsCheckOut"></CheckOutTooltip>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <el-checkbox
                                v-model="searchInfo.classification"
                                label="显示上年数和本年数"
                                @change="handleSearch"
                            ></el-checkbox>
                            <span class="ml-16">
                                <Dropdown btnTxt="打印" class="w-100" :downlistWidth="100"  v-permission="[sheetType + '-canprint']">
                                    <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                    <li @click="handlePrint(1)">批量打印</li>
                                    <li @click="handlePrint(2)">打印设置</li>
                                </Dropdown>
                            </span>
                            <span class="ml-16">
                                <Dropdown btnTxt="导出" class="w-100" :downlistWidth="100" v-permission="[sheetType + '-canexport']">
                                    <li @click="handleExport('single')">当前报表数据</li>
                                    <li @click="handleExport('batch')">批量导出</li>
                                </Dropdown>
                            </span>
                            <a
                                class="button solid-button ml-16"
                                v-if="checkPermission([sheetType + '-canedit']) && resetButtonShow"
                                @click="resetFormula"
                            >
                                重置公式
                            </a>
                            <a
                                class="button ml-16"
                                @click="handleShare"
                                v-if="!isHideBarcode && checkPermission([sheetType + '-canshare'])"
                            >
                                微信分享
                            </a>
                            <div class="divNew" v-if="!isWxwork && !isAccountingAgent && !isProSystem"></div>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <el-table
                            :class="isErp ? 'erp-table' : ''"
                            border
                            fit
                            stripe
                            scrollbar-always-on
                            highlight-current-row
                            class="custom-table"
                            row-key="lineID"
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                            :empty-text="emptyText"
                            max-height="calc(100vh - 210px)"
                            :data="tableData"
                            ref="tableRef"
                            @cell-dblclick="handleCellClick"
                            @header-dragend="headerDragend"
                        >
                            <el-table-column 
                                label="项目" 
                                :min-width="340" 
                                align="left" 
                                headerAlign="center"
                                prop="proName"
                                :width="getColumnWidth(setModule, 'proName')"
                            >
                                <template #default="scope">
                                    <div :class="[assertNameClass(scope.row)]" :title="scope.row.proName">
                                        <span>{{ scope.row.proName }}</span>
                                        <template v-if="checkShowEditEquation(scope.row)">
                                            <div class="link" @click="openEquationDialog(scope.row)">编辑公式</div>
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                            <template v-if="props.sheetType === 'incomeandexpenditure'">
                                <template v-if="searchInfo.classification">
                                    <template v-if="isShowBudgetYear">
                                        <el-table-column 
                                            label="本年数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            prop="yearTotal"
                                            :width="getColumnWidth(setModule, 'yearTotal')"
                                        >
                                            <template #header>
                                                <div class="table-header">
                                                    <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                    <span>本年数</span>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.yearTotal"
                                                    :formula="calcFormula(scope.row, 1)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            label="上年数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            :resizable="false"
                                        >
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.lastYearTotal"
                                                    :formula="calcFormula(scope.row, 0)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template v-else>
                                        <el-table-column 
                                            label="上年数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            prop="yearTotal"
                                            :width="getColumnWidth(setModule, 'yearTotal')"
                                        >
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.lastYearTotal"
                                                    :formula="calcFormula(scope.row, 0)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            label="本年数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            :resizable="false"
                                        >
                                            <template #header>
                                                <div class="table-header">
                                                    <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                    <span>本年数</span>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.yearTotal"
                                                    :formula="calcFormula(scope.row, 1)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </template>
                                <template v-else>
                                    <template v-if="isShowBudgetMonth">
                                        <el-table-column 
                                            label="本月数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            prop="monthTotal"
                                            :width="getColumnWidth(setModule, 'monthTotal')"
                                        >
                                            <template #header>
                                                <div class="table-header">
                                                    <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                    <span>本月数</span>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.monthTotal"
                                                    :formula="calcFormula(scope.row, 0)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            label="本年累计数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            :resizable="false"
                                        >
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.yearTotal"
                                                    :formula="calcFormula(scope.row, 1)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template v-else>
                                        <el-table-column 
                                            label="本年累计数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            prop="yearTotal"
                                            :width="getColumnWidth(setModule, 'yearTotal')"
                                        >
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.yearTotal"
                                                    :formula="calcFormula(scope.row, 1)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                        <el-table-column 
                                            label="本月数" 
                                            :min-width="280" 
                                            align="right" 
                                            header-align="right"
                                            :resizable="false"
                                        >
                                            <template #header>
                                                <div class="table-header">
                                                    <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                    <span>本月数</span>
                                                </div>
                                            </template>
                                            <template #default="scope">
                                                <TableAmountItem
                                                    :showLabel="checkShowFormulaLabel(scope.row)"
                                                    :amount="scope.row.monthTotal"
                                                    :formula="calcFormula(scope.row, 0)"
                                                    :line-number="scope.row.lineNumber"
                                                ></TableAmountItem>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                <template v-if="searchInfo.classification">
                                    <el-table-column 
                                        label="本年数" 
                                        :min-width="280" 
                                        align="right" 
                                        header-align="right"
                                        prop="yearTotal"
                                        :width="getColumnWidth(setModule, 'yearTotal')"
                                    >
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.yearTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.lineNumber"
                                            ></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column 
                                        label="上年数" 
                                        :min-width="280" 
                                        align="right" 
                                        header-align="right"
                                        :resizable="false"
                                    >
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.lastYearTotal"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.lineNumber"
                                            ></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column 
                                        label="本月数" 
                                        :min-width="280" 
                                        align="right" 
                                        header-align="right"
                                        prop="monthTotal"
                                        :width="getColumnWidth(setModule, 'monthTotal')"
                                    >
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.monthTotal"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.lineNumber"
                                            ></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column 
                                        label="本年累计数" 
                                        :min-width="280" 
                                        align="right" 
                                        header-align="right"
                                        :resizable="false"
                                    >
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.yearTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.lineNumber"
                                            ></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                        </el-table>
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <EditEquation
                        ref="editRef"
                        :class="[{ 'edit-content': !isErp }, 'slot-mini-content']"
                        :statement-id="editData.statementId"
                        :line-id="editData.lineId"
                        :pid="editData.pid"
                        :title="editData.title"
                        month-title="本月数"
                        year-title="本年累计数"
                        :value-type-options="valueTypeOptions"
                        @handle-submit-success="handleEditSubmitSuccess"
                        @handle-cancel="handleEditCancel"
                    ></EditEquation>
                </div>
            </template>
        </ContentSlider>
    </div>
    <PrintOrExportDialog
        :show-dialog="showDialog"
        :type="dialogType"
        :pid="searchInfo.pid"
        :fromStatement="reportType"
        @close="handleDialogClose"
    />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        :title="sheetType === 'incomeandexpenditure' ? '收入支出表打印' : '成本费用表打印'"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    ></StatementsPrint>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, computed, onMounted, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { globalExport, globalPrint,getUrlSearchParams } from "@/util/url";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { isInWxWork } from "@/util/wxwork";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { PeriodStatus } from "@/api/period";
import { checkPermission } from "@/util/permission";
import { share } from "@/views/Statements/utils";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import usePrint from "@/hooks/usePrint";

import type { IValueTypeOption } from "../types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { handleCellClick, getSwitchState } from "@/views/Statements/utils";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const accountSet = useAccountSetStore().accountSet;
const props = defineProps<{ sheetType: "incomeandexpenditure" | "cost" }>();
const apiPath = computed(() => {
    return props.sheetType === "incomeandexpenditure" ? "/api/IncomeAndExpenditureStatement" : "/api/CostStatement";
});
const reportType = computed(() => {
    return props.sheetType === "incomeandexpenditure" ? ReportTypeEnum.IncomeAndExpenditureSheet : ReportTypeEnum.CostSheet;
});
const reportStatementId = computed(() => {
    return (accountSet?.accountingStandard as number) * 1000 + reportType.value;
});

const setModule = props.sheetType + "Sheet";

interface ITableData {
    asid: number;
    statementId: number;
    proName: string;
    expand: number;
    fold: number;
    lineNumber: number;
    monthTotal: number;
    yearTotal: number;
    lineID: number;
    lineType: number;
    note: string;
    lastYearTotal: number;
}

const isErp = ref(window.isErp);
const periodStore = useAccountPeriodStore();
const slots = ["main", "edit"];
const currentSlot = ref("main");
const searchInfo = reactive({
    pid: Number(periodStore.getCurrentPeriod()),
    classification: false,
});
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isWxwork = ref(isInWxWork());
const isAccountingAgent = ref(window.isAccountingAgent);
const isProSystem = ref(window.isProSystem);
const tableData = ref<Array<ITableData>>([]);
const loading = ref(false);
const emptyText = ref("");

const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();

const isShowBudgetYear = ref(getSwitchState("isShowBudgetYear"));
const isShowBudgetMonth = ref(getSwitchState("isShowBudgetMonth"));
const colunmOrderChange = ref("0");
const tableRef = ref();
const swapColumns = (num: number) => {
    if (props.sheetType === "incomeandexpenditure") {
        if (num === 1) {
            isShowBudgetYear.value = !isShowBudgetYear.value;
            localStorage.setItem("isShowBudgetYear", JSON.stringify(isShowBudgetYear.value));
            colunmOrderChange.value = isShowBudgetYear.value ? "0" : "1";
        } else if (num === 2) {
            isShowBudgetMonth.value = !isShowBudgetMonth.value;
            localStorage.setItem("isShowBudgetMonth", JSON.stringify(isShowBudgetMonth.value));
            colunmOrderChange.value = isShowBudgetMonth.value ? "0" : "1";
        }
        tableRef.value.doLayout();
    }
};
function handleSearch() {
    const params = { pid: searchInfo.pid, isTax: searchInfo.classification };
    loading.value = true;
    emptyText.value = " ";
    request({
        url: apiPath.value,
        method: "get",
        params: params,
    })
        .then((res: IResponseModel<Array<ITableData>>) => {
            loading.value = false;
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "获取数据失败，请稍后重试" });
                return;
            }
            tableData.value = res.data;
            if (searchInfo.classification) {
                colunmOrderChange.value = isShowBudgetYear.value ? "0" : "1";
            } else {
                colunmOrderChange.value = isShowBudgetMonth.value ? "0" : "1";
            }
            if (!tableData.value.length) {
                emptyText.value = "暂无数据";
            }
        })
        .catch(() => {
            loading.value = false;
            ElNotify({ type: "warning", message: "获取数据失败，请稍后重试" });
        });
}

function getDefaultParams() {
    return {
        pid: searchInfo.pid,
        isTax: searchInfo.classification,
        colunmOrderChange: colunmOrderChange.value,
    };
}

const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    props.sheetType === "incomeandexpenditure" ? "incomeAndExpenditureStatement" : "costStatement",
   apiPath.value + `/Print`,
   {},
    false,
    false,
);
function handleExport(type: "single" | "batch") {
    if (type === "single") {
        globalExport(
            apiPath.value + `/Export?` + getUrlSearchParams(getDefaultParams())
        );
    } else {
        dialogType.value = "export";
        showDialog.value = true;
    }
}
function handleDialogClose() {
    showDialog.value = false;
    dialogType.value = "export";
}
function handleShare() {
    request({
        url: apiPath.value + `/Share?isTax=${searchInfo.classification}&pid=${searchInfo.pid}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "分享失败，请联系客服或管理员～" });
                return;
            }
            const shareReportHost =
                window.accountSrvHost +
                "/api/WxPay/MakeQRCode.ashx?data=" +
                window.shareReportHost +
                "/ShareReport/" +
                res.data +
                "&CurrentSystemType=1";
            share(shareReportHost);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "分享失败，请联系客服或管理员～" });
        });
}
//跳转编辑
const editRef = ref<InstanceType<typeof EditEquation>>();
const editData = reactive({
    statementId: reportStatementId.value,
    lineId: 0,
    pid: 0,
    title: "",
});
const defaultValueTypeOptions: Array<IValueTypeOption> = [
    { value: "4", label: "发生额" },
    { value: "1", label: "余额" },
];
const moreValueTypeOptions: Array<IValueTypeOption> = [
    { value: "0", label: "期初余额" },
    { value: "4", label: "发生额" },
    { value: "1", label: "余额" },
];
const valueTypeOptions = ref(defaultValueTypeOptions);
function handleEditSubmitSuccess() {
    currentSlot.value = "main";
    handleSearch();
    checkHasCustomEqutions();
}
function handleEditCancel() {
    currentSlot.value = "main";
}
function hasNumberTitle(value: string, chinaNumber: string[]) {
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value?.indexOf(chinaNumber[i]) > -1) return true;
    }
    return false;
}
function assertNameClass(row: ITableData) {
    const chinaNumber = ["一、", "二、", "三、", "四、", "五、", "六、"];
    if (props.sheetType === "incomeandexpenditure") {
        return row.lineNumber == 0 || hasNumberTitle(row.proName, chinaNumber) ? "level1" : "level2";
    } else {
        return row.proName === "费用总计" ? "level1" : "level2";
    }
}
const initialList = [60430010, 60430020];
function openEquationDialog(row: ITableData) {
    const { lineID, statementId, proName } = row;
    editData.statementId = statementId;
    editData.lineId = lineID;
    editData.pid = searchInfo.pid;
    editData.title = proName;
    valueTypeOptions.value = initialList.includes(lineID) ? moreValueTypeOptions : defaultValueTypeOptions;
    nextTick().then(() => {
        editRef.value?.init();
        currentSlot.value = "edit";
    });
}
const resetButtonShow = ref(false);
function checkHasCustomEqutions() {
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${reportStatementId.value}`,
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                resetButtonShow.value = res.data;
            } else {
                resetButtonShow.value = false;
            }
        })
        .catch(() => {
            resetButtonShow.value = false;
        });
}
function resetFormula() {
    ElConfirm(
        "确认删除此报表所有自定义公式？<div style='margin-top: 10px;'>重置公式仅影响未结账期间的报表数据</div>",
        false,
        () => {},
        "重置此报表公式"
    ).then((r: boolean) => {
        if (r) {
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${reportStatementId.value}`,
                method: "post",
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({ type: "success", message: "已经成功重置" });
                        resetButtonShow.value = res.data;
                        handleInit();
                    } else {
                        ElNotify({ type: "warning", message: res.msg || "重置失败，请稍后重试" });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "重置失败，请稍后重试" });
                });
        }
    });
}
const periodIsCheckOut = computed(() => {
    return periodRef.value?.periodStatus === PeriodStatus.CheckOut;
});
function calcFormula(row: ITableData, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.note?.split("|")[0].replace(/期末/g, "年初") ?? "";
            break;
        case 1:
            formula = row.note?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}
const notShowList = [60430010, 60430020, 60430280, 60430290];
function checkShowEditEquation(row: ITableData) {
    let show = true;
    if (props.sheetType === "incomeandexpenditure" && !searchInfo.classification && notShowList.includes(row.lineID)) {
        show = false;
    }
    return !periodIsCheckOut.value && checkPermission([props.sheetType + "-canedit"]) && row.lineType === 1 && show;
}

const notShowFormulaLabelList = [60430000, 60430010, 60430020, 60430270, 60430280, 60430290];
function checkShowFormulaLabel(row: ITableData) {
    let show = true;
    if (props.sheetType === "incomeandexpenditure" && !searchInfo.classification && notShowFormulaLabelList.includes(row.lineID)) {
        show = false;
    }
    return show;
}

function handleInit() {
    handleSearch();
    checkHasCustomEqutions();
}
onMounted(() => {
    if (props.sheetType === "incomeandexpenditure") {
        if (searchInfo.classification) {
            colunmOrderChange.value = isShowBudgetYear.value ? "0" : "1";
        } else {
            colunmOrderChange.value = isShowBudgetMonth.value ? "0" : "1";
        }
    }
    handleInit();
});

watch(
    () => searchInfo.pid,
    () => {
        handleInit();
    }
);
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";

.edit-content {
    width: 1100px !important;
}
.content {
    display: flex;
    justify-content: center;
    align-items: center;
    .main-content {
        width: 1000px !important;
        .main-center {
            .el-table {
                height: auto; 
            }
        }
    }
}
.main-tool-right {
    span.ml-16 {
        :deep(.w-100) {
            width: 98px;
        }
    }
}
</style>
