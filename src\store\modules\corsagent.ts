import { computed, ref } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";

export const useCorsagentIframeStore = defineStore("corsagent", () => {
    const corsagentIframeHost = (window.isErp ? window.erpHost : window.jHost) + "/corsagent.html?";
    const funArr: Array<() => Promise<void>> = [];
    let canSend = true;
    const corsagentIframe = ref();

    const autoNotice = () => {
        if (funArr.length > 0) {
            if (canSend) {
                canSend = false;
                funArr[0]()
                    .then(() => {})
                    .catch(() => {})
                    .finally(() => {
                        canSend = true;
                        funArr.splice(0, 1);
                        funArr.length > 0 && autoNotice();
                    });
            }
        }
    };

    const pushNoticeFun = (fun: () => Promise<void>) => {
        if (corsagentIframe.value === undefined) {
            createCorsagentIframe();
        }
        funArr.push(fun);
        if (funArr.length === 1) {
            autoNotice();
        }
    };

    const pushNoticeUrl = (url: string) => {
        pushNoticeFun((): Promise<void> => {
            return new Promise((resolve, reject) => {
                corsagentIframe.value.onload = function () {
                    resolve();
                };
                corsagentIframe.value.onerror = function () {
                    reject();
                };
                corsagentIframe.value.src = getCorsagentIframeHost() + url + "&r=" + Math.random();
            });
        });
    };

    const createCorsagentIframe = () => {
        corsagentIframe.value = document.createElement("iframe");
        corsagentIframe.value.src = "";
        corsagentIframe.value.style.display = "none";
        document.body.appendChild(corsagentIframe.value);
    };

    const getCorsagentIframeHost = () => {
        return corsagentIframeHost + "appasid=" + getGlobalToken() + "&";
    };

    return { corsagentIframe, getCorsagentIframeHost, pushNoticeFun, pushNoticeUrl };
});

/** 在 setup 外使用 */
export function useCorsagentIframeStoreHook() {
    return useCorsagentIframeStore(store);
}
