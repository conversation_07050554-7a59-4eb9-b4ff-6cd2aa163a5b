<template>
  <el-dialog v-model="display" width="480" :draggable="false" :show-close="false">
    <template #header>
      <div class="my-header">
        <!-- 柠檬云财务标题 -->
         <div class="my-header-title">发现新功能</div>
        <img class="close" src="@/assets/AppConfirm/close.png" @click="closeDialog" />
      </div>
    </template>
    <div class="virtual-as-dialog-container">
      <el-scrollbar max-height="320" :always="true">
        <div class="tips" v-html="newFunctionBody">
        </div>
      </el-scrollbar>
      <div class="button-container">
        <el-button color="#43CE7F" class="button" round>
          <a @click="globalWindowOpen(newFunctionHref)" target="_blank">查看详情</a>
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<style lang="less" scoped>
.my-header {
  position: relative;
  width: 480px;
  height: 80px;
  color: #fff;
  background-color: #43CE7F;

  img {
    margin-top: 20px;
  }

  .close {
    position: absolute;
    right: 10px;
    top: 0;

    &:hover {
      cursor: pointer;
    }
  }
}
.my-header-title {
  font-weight: 400;
  font-size: 28px;
  color: #fff; 
  line-height: 38px; 
  padding-top: 18px;
  letter-spacing: 1px;
}

.virtual-as-dialog-container {
  .tips {
    font-weight: 500;
    font-size: 15px;
    color: #666666;
    line-height: 21px;
    padding: 16px 25px 0 35px;

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      font-weight: 400;
      font-size: 15px;
      color: #666666;
      line-height: 26px;
      text-align: left;
    }
    .tips-item + .tips-item {
      margin-top: 15px;
    }
  }

  .button-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .button {
      width: 200px;
      height: 40px;
      margin: 20px 0;
      color: #fff;
      background-color: #43CE7F;

      a {
        text-decoration: none;
        color: inherit;
        background-color: transparent;

      }
    }
  }

}
</style>
<script setup lang="ts">
import { ref } from "vue";
import { newFunctionHref, newFunctionBody } from "./utils";
import { globalWindowOpen } from "@/util/url";

const display = ref(true);
const closeDialog = () => {
  display.value = false;
};

</script>
