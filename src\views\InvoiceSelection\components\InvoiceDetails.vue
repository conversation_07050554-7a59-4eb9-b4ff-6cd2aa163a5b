<template>
  <div class="invoice-details">
    <div class="invoice-title">电子发票（增值税专用发票）</div>
    <div class="invoice-content">
      <div class="block">
        <div class="block-title">基本信息</div>
        <div class="info-form">
          <template
            v-for="(item, index) in displayFields"
            :key="index">
            <div
              class="form-item"
              v-if="invoiceData[item.field] !== undefined">
              <span class="label">{{ item.label }}：</span>
              <span class="value">
                <LMInput
                  v-model="invoiceData[item.field]"
                  :disabled="true"></LMInput>
              </span>
            </div>
          </template>
        </div>
      </div>
      <div class="block">
        <div class="block-title">发票明细</div>
        <div class="info-form">
          <div class="form-item">
            <span class="label">不含税金额：</span>
            <span class="value">
              <LMInput
                v-model="invoiceData.taxExclusiveAmount"
                :disabled="true"></LMInput>
            </span>
          </div>
          <div class="form-item">
            <span class="label">税额：</span>
            <span class="value">
              <LMInput
                v-model="invoiceData.taxAmount"
                :disabled="true"></LMInput>
            </span>
          </div>
          <div class="form-item">
            <span class="label">价税合计：</span>
            <span class="value">
              <LMInput
                v-model="invoiceData.totalAmount"
                :disabled="true"></LMInput>
            </span>
          </div>
        </div>
        <LMTable
          :data="invoiceData.items"
          :columns="columns"
          rowKey="name"></LMTable>
      </div>
    </div>
    <div class="buttons">
      <div
        class="button"
        @click="emits('back')">
        取消
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const emits = defineEmits(["back"])
  interface InvoiceField {
    label: string
    field: string
  }

  // 定义所有可能的字段
  const allFields: InvoiceField[] = [
    { label: "发票代码", field: "code" },
    { label: "发票号码", field: "number" },
    { label: "开票日期", field: "date" },
    { label: "校验码", field: "checkCode" },
    { label: "机器编号", field: "machineNo" },
    { label: "购买方名称", field: "buyerName" },
    { label: "购买方税号", field: "buyerTaxNo" },
    // ... 其他字段
  ]

  // 模拟发票数据
  interface InvoiceData {
    [key: string]: any
    code: string
    number: string
    date: string
    buyerName: string
    buyerTaxNo: string
    taxExclusiveAmount: number
    taxAmount: number
    totalAmount: number
    items: { name: string; spec: string; unit: string; quantity: number; price: number; amount: number }[]
  }

  const invoiceData = reactive<InvoiceData>({
    code: "*********",
    number: "12345678",
    date: "2024-04-10",
    buyerName: "测试公司",
    buyerTaxNo: "91110105MA00XXXXX11111111111",
    taxExclusiveAmount: 1000.6,
    taxAmount: 170.8,
    totalAmount: 1170.03,
    items: [
      { name: "商品1", spec: "规格1", unit: "个", quantity: 10, price: 100, amount: 10 },
      { name: "商品2", spec: "规格2", unit: "个", quantity: 5, price: 50, amount: 5 },
    ],
  })

  // 计算实际要显示的字段
  const displayFields = computed(() => {
    return allFields.filter((field) => invoiceData[field.field] !== undefined)
  })

  const columns = [
    { label: "序号", field: "index" },
    { label: "商品名称", prop: "name" },
    { label: "规格型号", prop: "spec" },
    { label: "单位", prop: "unit" },
    { label: "数量", prop: "quantity" },
    { label: "金额", prop: "price" },
    { label: "税率", prop: "amount" },
  ]
</script>

<style scoped lang="scss">
  .invoice-details {
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .invoice-title {
    padding: 16px 0;
    text-align: center;
    color: var(--font-color);
    font-size: var(--h4);
    line-height: 22px;
    font-weight: 700;
    width: 100%;
    border-bottom: 1px solid var(--button-border-color);
  }
  .invoice-content {
    width: var(--edit-content-width);
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    .block {
      padding: 20px;
    }
    .block-title {
      color: var(--font-color);
      font-size: var(--h3);
      line-height: 22px;
      text-align: left;
    }
  }
  .info-form {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 16px 24px;
    padding: 16px 0;

    .form-item {
      display: flex;
      align-items: center;
      min-height: 32px;

      .label {
        min-width: 90px;
        flex-shrink: 0;
        color: var(--label-color, #666);
        text-align: right;
        padding-right: 8px;
        font-size: 14px;
      }

      .value {
        flex: 1;
        color: var(--font-color);
        font-size: 14px;
        word-break: break-all;
      }
    }
  }
</style>
