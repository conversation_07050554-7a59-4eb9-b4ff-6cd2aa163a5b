<template>
    <div class="content">
        <content-slider :slots="['main', 'add', 'generateVoucher']" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">票据管理</div>
                    <el-tabs v-model="draftCategory" class="demo-tabs">
                        <el-tab-pane label="票据台账" name="1">
                            <div class="center-content">
                                <div class="main-top main-tool-bar space-between">
                                    <div class="main-tool-left">
                                        <SearchInfoContainer ref="containerRef" class="invoice-search-container">
                                            <template v-slot:title>{{ currentPeriodInfo }} </template>
                                            <div class="line-item first-item input">
                                                <div class="line-item-title">出票日:</div>
                                                <div class="line-item-field">
                                                    <DatePicker
                                                        v-model:startPid="searchInfo.startDate"
                                                        v-model:endPid="searchInfo.endDate"
                                                        :disabled-date-start="disabledDateStart"
                                                        :disabled-date-end="disabledDateEnd"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                    />
                                                </div>
                                            </div>
                                            <div v-if="!isErp">
                                                <div class="line-item input">
                                                    <div class="line-item-title">凭证日期:</div>
                                                    <div class="line-item-field">
                                                        <DatePicker
                                                            v-model:startPid="searchInfo.startVDate"
                                                            v-model:endPid="searchInfo.endVDate"
                                                            :disabled-date-start="disabledStartVDate"
                                                            :disabled-date-end="disabledEndVDate"
                                                            @blur="forcedShutdown = false"
                                                            @focus="forcedShutdown = true"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="line-item input">
                                                    <div class="line-item-title">凭证字号：</div>
                                                    <div class="line-item-field">
                                                        <div class="vgid-line">
                                                            <el-select
                                                                v-model="searchInfo.vgId"
                                                                :teleported="false"
                                                                style="width: 90px"
                                                                @change="handleVgIdChange"
                                                                @blur="forcedShutdown = false"
                                                                @focus="forcedShutdown = true"
                                                            >
                                                                <el-option :value="0" label="请选择">请选择</el-option>
                                                                <el-option :value="1" label="全部">全部</el-option>
                                                                <el-option
                                                                    v-for="item in voucherGroup"
                                                                    :value="item.id"
                                                                    :key="item.id"
                                                                    :label="item.name"
                                                                ></el-option>
                                                            </el-select>
                                                            <el-input
                                                                clearable
                                                                type="text"
                                                                class="ml-10"
                                                                v-model="searchInfo.startVNum"
                                                                :disabled="!searchInfo.vgId"
                                                                @input="startVNumLimit"
                                                            ></el-input>
                                                            <span style="padding: 0 8px; line-height: 30px">至</span>
                                                            <el-input
                                                                clearable
                                                                type="text"
                                                                v-model="searchInfo.endVNum"
                                                                :disabled="!searchInfo.vgId"
                                                                @input="endVNumLimit"
                                                            ></el-input>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="line-item input">
                                                <div class="line-item-title">票据种类:</div>
                                                <div class="line-item-field">
                                                    <el-select
                                                        :teleported="false"
                                                        class="item search-info-select"
                                                        placeholder=" "
                                                        v-model="searchInfo.draftType"
                                                        @blur="forcedShutdown = false"
                                                        @focus="forcedShutdown = true"
                                                        :filterable="true"
                                                        :filter-method="draftTypeFilterMethod"
                                                    >
                                                        <el-option
                                                            v-for="item in showDraftTypeOptions"
                                                            :key="item.value"
                                                            :value="item.value"
                                                            :label="item.name"
                                                        ></el-option>
                                                    </el-select>
                                                </div>
                                            </div>
                                            <div class="line-item input">
                                                <div class="line-item-title">票据状态:</div>
                                                <div class="line-item-field">
                                                    <VirtualSelectCheckbox
                                                        :options="draftStatusList"
                                                        :use-el-icon="true"
                                                        width="311px"
                                                        v-model:selectedList="searchInfo.draftStatus"
                                                        class="item"
                                                        v-model:forced-shutdown="forcedShutdown"
                                                    />
                                                    <!-- <el-select
                                                        :teleported="false"
                                                        class="item search-info-select"
                                                        placeholder=" "
                                                        v-model="searchInfo.draftStatus"
                                                        :suffix-icon="CaretBottom"
                                                    >
                                                        <el-option
                                                            v-for="item in draftStatusOptions"
                                                            :key="item.value"
                                                            :value="item.value"
                                                            :label="item.name"
                                                            >{{ item.name }}</el-option
                                                        >
                                                    </el-select> -->
                                                </div>
                                            </div>
                                            <div class="line-item input">
                                                <div class="line-item-title">票据号:</div>
                                                <div class="line-item-field">
                                                    <el-input type="text" v-model="searchInfo.draftNo" clearable></el-input>
                                                </div>
                                            </div>
                                            <div class="line-item input">
                                                <div class="line-item-title">出票人:</div>
                                                <div class="line-item-field">
                                                    <el-input type="text" v-model="searchInfo.drawerName" clearable></el-input>
                                                </div>
                                            </div>
                                            <div class="line-item input">
                                                <div class="line-item-title">承兑人:</div>
                                                <div class="line-item-field">
                                                    <el-input type="text" v-model="searchInfo.acceptorName" clearable></el-input>
                                                </div>
                                            </div>
                                            <div class="line-item input">
                                                <div class="line-item-title">票据金额:</div>
                                                <div class="line-item-field">
                                                    <el-input
                                                        v-model="searchInfo.minAmount"
                                                        @input="handleAmountInput($event, 'minAmount')"
                                                        clearable
                                                    ></el-input>
                                                    <span class="ml-10 mr-10 w-16">至</span>
                                                    <el-input
                                                        v-model="searchInfo.maxAmount"
                                                        @input="handleAmountInput($event, 'maxAmount')"
                                                        clearable
                                                    ></el-input>
                                                </div>
                                            </div>
                                            <div class="buttons">
                                                <a class="button solid-button" @click="handleSearchSubmit">确定</a>
                                                <a class="button" @click="handleClose">取消</a>
                                                <a class="button" @click="handleReset">重置</a>
                                            </div>
                                        </SearchInfoContainer>
                                    </div>
                                    <div class="main-tool-right right button-content">
                                        <a
                                            @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/handle?subMenuId=110113110')"
                                            class="mr-10 pr-10 how-draft"
                                            v-show="!isHideBarcode"
                                            >如何管理票据? &nbsp;
                                            <el-icon>
                                                <QuestionFilled />
                                            </el-icon>
                                        </a>
                                        <el-checkbox class="float-r pr-10" label="显示所有信息" v-model="showAll"></el-checkbox>

                                        <a v-permission="['draft-canedit']" class="button mr-10 float-r" @click="addNewDraft"> 新增 </a>
                                        <div class="right button-content">
                                            <Dropdown
                                                btnTxt="生成凭证"
                                                class="mr-10"
                                                :downlistWidth="102"
                                                v-permission="['draft-cancreatevoucher']"
                                            >
                                                <li @click="openVoucherFromDraft(1)">生成凭证</li>
                                                <li @click="openVoucherFromDraft(2)">生成收款凭证</li>
                                                <li @click="openVoucherFromDraft(3)">生成贴现凭证</li>
                                            </Dropdown>
                                            <Dropdown
                                                v-if="checkPermission(['draft-canimport']) && checkPermission(['draft-canexport'])"
                                                btnTxt="导入"
                                                class="mr-10"
                                                :downlistWidth="84"
                                            >
                                                <li v-permission="['draft-canimport']" @click="handleImport">导入</li>
                                                <li v-permission="['draft-canexport']" @click="handleExport">导出</li>
                                            </Dropdown>
                                            <a
                                                v-else-if="checkPermission(['draft-canimport'])"
                                                class="button float-r"
                                                @click="handleImport"
                                            >
                                                导入
                                            </a>
                                            <a
                                                v-else-if="checkPermission(['draft-canexport'])"
                                                class="button float-r"
                                                @click="handleExport"
                                            >
                                                导出
                                            </a>

                                            <a v-permission="['draft-candelete']" class="button float-r" @click="handleDelete"> 删除 </a>
                                        </div>
                                        <RefreshButton></RefreshButton>
                                    </div>
                                </div>
                                <div class="main-center">
                                    <div :class="['draft-data-table', tableData.length ? '' : 'none-data']">
                                        <Table
                                            ref="draftTableRef"
                                            :data="tableData"
                                            :columns="draftColumns"
                                            :loading="loading"
                                            :empty-text="emptyText"
                                            :page-is-show="true"
                                            :scrollbar-show="true"
                                            :show-overflow-tooltip="true"
                                            :layout="paginationData.layout"
                                            :page-sizes="paginationData.pageSizes"
                                            :page-size="paginationData.pageSize"
                                            :total="paginationData.total"
                                            :currentPage="paginationData.currentPage"
                                            :row-class-name="checkedRowClass"
                                            @size-change="handleSizeChange"
                                            @current-change="handleCurrentChange"
                                            @selection-change="tableSelectionChange"
                                            @cell-click="tableCellClick"
                                            @row-click="tableRowClick"
                                            @refresh="handleRerefresh"
                                            @cell-drop="cellDrop"
                                            :tableName="setModule"
                                            @scroll="handleScroll"
                                        >
                                            <template #draftType>
                                                <el-table-column
                                                    label="收支类别"
                                                    prop="ieType"
                                                    :min-width="98"
                                                    :width="getColumnWidth(setModule, 'ieType')"
                                                    align="left"
                                                    header-align="left"
                                                    :show-overflow-tooltip="true"
                                                    :class-name="getSlotClassName('draftType', draftColumns)"
                                                    :fixed="getSlotIsFreeze('draftType', draftColumns)"
                                                >
                                                    <template #default="scope">
                                                        <Select
                                                            ref="ieTypeRef"
                                                            v-if="
                                                                currentClickRowId === scope.row.id &&
                                                                currentClickProp === scope.column.property
                                                            "
                                                            v-model="IETypeName"
                                                            class="line-item-field"
                                                            :suffix-icon="CaretBottom"
                                                            placeholder=" "
                                                            :fit-input-width="true"
                                                            @change="changeIEType($event, scope.row)"
                                                            @blur="currentClickRowId = '-1'"
                                                            :filterable="true"
                                                            :filter-method="IETypeFilterMethod"
                                                        >
                                                            <el-option
                                                                v-for="item in showIETypeOPtions"
                                                                :label="item.value2"
                                                                :value="item.subkey"
                                                                :key="item.subkey"
                                                            ></el-option>
                                                        </Select>
                                                        <span v-else>{{ IETypeObject?.[scope.row.ieType] }}</span>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!-- 票据号 -->
                                            <template #draftNo>
                                                <el-table-column
                                                    label=""
                                                    :width="getColumnWidth(setModule, 'draftNo')"
                                                    align="left"
                                                    header-align="left"
                                                    prop="draftNo"
                                                    :class-name="getSlotClassName('draftNo', draftColumns)"
                                                    :fixed="getSlotIsFreeze('draftNo', draftColumns)"
                                                >
                                                    <template #header>
                                                        <div class="header-operate">
                                                            <span>票据号</span>
                                                            <div class="header-operate-rt">
                                                                <TableHeaderFilter
                                                                    :prop="'draftNo'"
                                                                    :isFilter="!!filterSearchInfo.draftNo && searchBoxHearder"
                                                                    :hasSearchVal="searchInfo.draftNo" 
                                                                    @filterSearch="filterSearch"
                                                                ></TableHeaderFilter>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template #default="scope">
                                                        <span>{{ scope.row.draftNo }}</span>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!-- 票据种类-->
                                            <template #draftTypeName>
                                                <el-table-column
                                                    label=""
                                                    :width="getColumnWidth(setModule, 'draftTypeName')"
                                                    align="left"
                                                    header-align="left"
                                                    prop="draftTypeName"
                                                    :class-name="getSlotClassName('draftTypeName', draftColumns)"
                                                    :fixed="getSlotIsFreeze('draftTypeName', draftColumns)"
                                                >
                                                    <template #header>
                                                        <div class="header-operate">
                                                            <span>票据种类</span>
                                                            <div class="header-operate-rt">
                                                                <TableHeaderFilter
                                                                    :prop="'draftType'" 
                                                                    :isSelect="true"
                                                                    :multiSelect="false"
                                                                    :SSinglist="draftTypeOptions"
                                                                    :hasSSingVal="searchInfo.draftType"
                                                                    :isFilter="filterSearchInfo.draftType > 0 && searchBoxHearder"
                                                                    @filterSearch="filterSearch"
                                                                ></TableHeaderFilter>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template #default="scope">
                                                        <span>{{ scope.row.draftTypeName }}</span>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!-- 出票人-->
                                            <template #drawerName>
                                                <el-table-column
                                                    label=""
                                                    :width="getColumnWidth(setModule, 'drawerName')"
                                                    align="left"
                                                    header-align="left"
                                                    prop="drawerName"
                                                    :class-name="getSlotClassName('drawerName', draftColumns)"
                                                    :fixed="getSlotIsFreeze('drawerName', draftColumns)"
                                                >
                                                    <template #header>
                                                        <div class="header-operate">
                                                            <span>出票人</span>
                                                            <div class="header-operate-rt">
                                                                <TableHeaderFilter
                                                                    :prop="'drawerName'" 
                                                                    :isFilter="!!filterSearchInfo.drawerName && searchBoxHearder"
                                                                    :hasSearchVal="searchInfo.drawerName" 
                                                                    @filterSearch="filterSearch"
                                                                ></TableHeaderFilter>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template #default="scope">
                                                        <span>{{ scope.row.drawerName }}</span>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!--承兑人-->
                                            <template #acceptorName>
                                                <el-table-column
                                                    label=""
                                                    :width="getColumnWidth(setModule, 'acceptorName')"
                                                    align="left"
                                                    header-align="left"
                                                    prop="acceptorName"
                                                    :class-name="getSlotClassName('acceptorName', draftColumns)"
                                                    :fixed="getSlotIsFreeze('acceptorName', draftColumns)"
                                                >
                                                    <template #header>
                                                        <div class="header-operate">
                                                            <span>承兑人</span>
                                                            <div class="header-operate-rt">
                                                                <TableHeaderFilter
                                                                    :prop="'acceptorName'" 
                                                                    :isFilter="!!filterSearchInfo.acceptorName && searchBoxHearder"
                                                                    :hasSearchVal="searchInfo.acceptorName" 
                                                                    @filterSearch="filterSearch"
                                                                ></TableHeaderFilter>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template #default="scope">
                                                        <span>{{ scope.row.acceptorName }}</span>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!--票据状态-->
                                            <template #draftStatusName>
                                                <el-table-column
                                                    label=""
                                                    :min-width="120"
                                                    :width="getColumnWidth(setModule, 'draftStatusName')"
                                                    align="left"
                                                    header-align="left"
                                                    prop="draftStatusName"
                                                    :class-name="getSlotClassName('draftStatusName', draftColumns)"
                                                    :fixed="getSlotIsFreeze('draftStatusName', draftColumns)"
                                                >
                                                    <template #header>
                                                        <div class="header-operate">
                                                            <span>票据状态</span>
                                                            <div class="header-operate-rt">
                                                                <TableHeaderFilter
                                                                    :prop="'draftStatus'"
                                                                    :isSelect="true"
                                                                    :selectedList="searchInfo.draftStatus"
                                                                    :option="searchOptions.draftStatus"
                                                                    :hasSelectList="filterSearchInfo.draftStatus"
                                                                    :isFilter="isFilterMultile(filterSearchInfo.draftStatus, searchOptions.draftStatus)"
                                                                    @filterSearch="filterSearch"
                                                                >
                                                                </TableHeaderFilter>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template #default="scope">
                                                        <span>{{ scope.row.draftStatusName }}</span>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!-- 票据凭证 -->
                                            <template #voucherGroup>
                                                <el-table-column
                                                    label="票据凭证"
                                                    :min-width="getColumnWidth(setModule, 'voucherGroup', 180)"
                                                    align="left"
                                                    header-align="left"
                                                    prop="voucherGroup"
                                                    :class-name="getSlotClassName('voucherGroup', draftColumns)"
                                                    :fixed="getSlotIsFreeze('voucherGroup', draftColumns)"
                                                >
                                                    <template #default="scope">
                                                        <div class="handle">
                                                            <a
                                                                v-if="
                                                                    scope.row.pId !== null &&
                                                                    scope.row.pId !== '' &&
                                                                    scope.row.vId !== null &&
                                                                    scope.row.vId !== '' &&
                                                                    checkPermission(['draft-cancreatevoucher'])
                                                                "
                                                                class="link"
                                                                v-permission="['draft-cancreatevoucher']"
                                                                @click="ViewCredentials(scope.row.pId, scope.row.vId)"
                                                                >{{ scope.row.voucherGroup }}</a
                                                            >
                                                            <span v-else>{{ scope.row.voucherGroup }}</span>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!-- 贴现凭证 -->
                                            <template #discountVoucherGroup>
                                                <el-table-column
                                                    label="贴现凭证"
                                                    :min-width="getColumnWidth(setModule, 'discountVoucherGroup', 180)"
                                                    align="left"
                                                    header-align="left"
                                                    prop="discountVoucherGroup"
                                                    :class-name="getSlotClassName('discountVoucherGroup', draftColumns)"
                                                    :fixed="getSlotIsFreeze('discountVoucherGroup', draftColumns)"
                                                >
                                                    <template #default="scope">
                                                        <div class="handle">
                                                            <a
                                                                v-if="
                                                                    scope.row.discountPId !== null &&
                                                                    scope.row.discountPId !== '' &&
                                                                    scope.row.discountVId !== null &&
                                                                    scope.row.discountVId !== '' &&
                                                                    checkPermission(['draft-cancreatevoucher'])
                                                                "
                                                                class="link"
                                                                v-permission="['draft-cancreatevoucher']"
                                                                @click="ViewCredentials(scope.row.discountPId, scope.row.discountVId)"
                                                                >{{ scope.row.discountVoucherGroup }}</a
                                                            >
                                                            <span v-else>{{ scope.row.discountVoucherGroup }}</span>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <!-- 收款凭证 -->
                                            <template #getMoneyVoucherGroup>
                                                <el-table-column
                                                    label="收款凭证"
                                                    :min-width="getColumnWidth(setModule, 'getMoneyVoucherGroup', 180)"
                                                    align="left"
                                                    header-align="left"
                                                    prop="getMoneyVoucherGroup"
                                                    :class-name="getSlotClassName('getMoneyVoucherGroup', draftColumns)"
                                                    :fixed="getSlotIsFreeze('getMoneyVoucherGroup', draftColumns)"
                                                >
                                                    <template #default="scope">
                                                        <div class="handle">
                                                            <a
                                                                v-if="
                                                                    scope.row.getMoneyPId !== null &&
                                                                    scope.row.getMoneyPId !== '' &&
                                                                    scope.row.getMoneyVId !== null &&
                                                                    scope.row.getMoneyVId !== '' &&
                                                                    checkPermission(['draft-cancreatevoucher'])
                                                                "
                                                                class="link"
                                                                v-permission="['draft-cancreatevoucher']"
                                                                @click="ViewCredentials(scope.row.getMoneyPId, scope.row.getMoneyVId)"
                                                                >{{ scope.row.getMoneyVoucherGroup }}</a
                                                            >
                                                            <span v-else>{{ scope.row.getMoneyVoucherGroup }}</span>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                            <template #operation>
                                                <el-table-column 
                                                    label="" 
                                                    width="200"
                                                    prop="operation"
                                                    align="left" 
                                                    header-align="left"
                                                    :fixed="getSlotIsFreeze('operation', draftColumns)"
                                                    :resizable="false"
                                                >
                                                    <template #header>
                                                        <div class="header-operate">
                                                            <span>操作</span>
                                                            <div class="header-operate-rt">
                                                                <div class="set-icon" @click="openColSet"></div>
                                                            </div>
                                                        </div>
                                                    </template> 
                                                    <template #default="scope">
                                                        <div class="handle">
                                                            <a
                                                                class="link"
                                                                v-permission="['draft-canedit']"
                                                                @click.stop="handleEditRow(scope.row)"
                                                                >编辑</a
                                                            >
                                                            <a
                                                                class="link"
                                                                v-permission="['draft-canreceipt']"
                                                                @click.stop="handlePayment(scope.row)"
                                                                >收款</a
                                                            ><a
                                                                v-permission="['draft-cantransfer']"
                                                                class="link"
                                                                @click.stop="handleTransference(scope.row)"
                                                                >转让</a
                                                            >
                                                            <a
                                                                class="link"
                                                                v-permission="['draft-candelete']"
                                                                @click.stop="handleDeleteRow(scope.row)"
                                                                >删除</a
                                                            >
                                                            <a
                                                                class="link"
                                                                v-permission="['draft-canedit']"
                                                                @click.stop="handleCheckAttachFile(scope.row)"
                                                            >
                                                                {{
                                                                    "附件" +
                                                                    (scope.row.attachsCount === 0 ? "" : "(" + scope.row.attachsCount + ")")
                                                                }}
                                                            </a>
                                                        </div>
                                                    </template>
                                                </el-table-column>
                                            </template>
                                        </Table>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                    <el-dialog v-model="transferenceDialog" title="票据转让" center width="440" class="dialogDrag">
                        <div class="transference-dialog" v-dialogDrag>
                            <div class="transference-form">
                                <div class="transference-form-item">
                                    <el-form-item label="受让方：" class="line-item">
                                        <el-input class="line-item-field" v-model="transferData.transferee" />
                                    </el-form-item>
                                    <el-form-item label="转让金额：" required class="line-item">
                                        <el-input class="line-item-field" v-model="transferData.amount" @input="handleTransAmount" />
                                    </el-form-item>
                                    <el-form-item label="转让日期：" class="line-item">
                                        <el-date-picker
                                            class="line-item-field line-item-date"
                                            v-model="transferData.transferDate"
                                            value-format="YYYY-MM-DD"
                                            type="date"
                                            clearable
                                            placeholder=""
                                        />
                                    </el-form-item>
                                </div>
                            </div>
                            <div class="transference-buttons">
                                <a class="button solid-button mr-10" @click="submitTransfer">确定</a>
                                <a class="button" @click="(transferenceDialog = false), resetTransferData()">取消</a>
                            </div>
                        </div>
                    </el-dialog>
                    <ImportSingleFileDialog
                        :importTitle="'导入票据信息'"
                        v-model:import-show="importShow"
                        :importUrl="'/api/Draft/Import'"
                        :uploadSuccess="uploadSuccess"
                    >
                        <template #download>
                            <div>1.请选择下面任意一种方式导入票据</div>
                            <div class="mt-10 ml-10">(1)在票据列表导出所需数据，确认后直接导入</div>
                            <div class="mt-10 ml-10">
                                (2)点击下载模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="DownloadExcelTemplate"
                                    >下载模板</a
                                >
                            </div>
                        </template>
                        <template #import-content>
                            <span>2.选择文件导入</span>
                        </template>
                    </ImportSingleFileDialog>
                </div>
            </template>
            <template #add>
                <add-draft
                    ref="addDraftView"
                    :form-type="addOrEdit"
                    :edit-draft-pic="editDraftPic"
                    @addDraftSuccess="handleEditDraftSuccess"
                    :draftStatusOptions="draftStatusOptions"
                    :draftTypeOptions="draftTypeOptions"
                ></add-draft>
            </template>
            <template #generateVoucher>
                <GenerateVoucher
                    :title="'票据生成凭证'"
                    :documentTitle="'票据'"
                    :query-params="genVoucherQueryParameters"
                    :error-table-columns="genVoucherErrorTableColumns"
                    :merge-error-function="() => (genVoucherQueryParameters.isMerge = false)"
                    @back="genVoucherSaveSuccess"
                    @voucher-changed="genVoucherChangedHandle"
                    ref="generateVoucherView"
                >
                    <template #toolbar>
                        <a class="solid-button" @click="saveGenVoucher()">保存</a>
                        <a class="button ml-10" @click="currentSlot = 'main'">取消</a>
                        <el-checkbox
                            class="ml10"
                            label="合并生成凭证"
                            v-model="genVoucherQueryParameters.isMerge"
                            @change="genVoucherCheckboxChanged('isMerge')"
                        ></el-checkbox>
                        <el-checkbox
                            class="ml10"
                            label="智能匹配出票人"
                            v-model="genVoucherQueryParameters.autoMatch"
                            @change="genVoucherCheckboxChanged('autoMatch')"
                        ></el-checkbox>
                        <BubbleTip :bubble-width="226" :bubble-top="16" class="ml-10">
                            <template #content>
                                <div class="help-icon"></div>
                            </template>
                            <template #tips>
                                <div>根据票据的收支类别生成对应的凭证</div>
                                <div>1、智能匹配出票人与客户</div>
                                <div>2、自动新增对应的客户</div>
                            </template>
                        </BubbleTip>
                    </template>
                </GenerateVoucher>
            </template>
        </content-slider>
        <UploadFileDialog :readonly="readonly" ref="uploadFileDialogRef" @save="saveAttachFile" />
        <!-- 列设置 -->
        <ColumnSet
            ref="columnSetRef" 
            v-model="columnSetShow"
            :data="draftColumnsSet"
            :allColumns="allColumns"
            :setModuleType="module"
            @saveColumnSet="saveColumnSet"
        >
        </ColumnSet>
    </div>
</template>

<script lang="ts">
export default {
    name: "Draft",
};
</script>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, onUnmounted, onActivated, toRef, nextTick, watchEffect } from "vue";
import { getUrlSearchParams, globalExport, globalWindowOpen, globalWindowOpenPage, tryClearCustomUrlParams } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import DatePicker from "@/components/DatePicker/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import AddDraft from "./components/AddDraft.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { Columns } from "./utils";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Table from "@/components/Table/index.vue";
import { QuestionFilled, CaretBottom } from "@element-plus/icons-vue";
import type { 
    ITableItem, 
    IDraftStatusOptions, 
    IETypeOption, 
    IJDStatusRes,
    IFSearchItem,
} from "./types";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { dayjs } from "element-plus";
import Select from "@/components/Select/index.vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { useLoading } from "@/hooks/useLoading";
import {
    DraftDocumentModel,
    DraftQueryParameters,
    DraftWithVoucherModel,
    type IBatchGenerateVoucherModel,
    type IDraftGenVoucherResult,
    type IGenVoucherNeedInsertAsub,
} from "@/components/GenerateVoucher/types";
import BubbleTip from "@/components/BubbleTip/index.vue";
import GenerateVoucher from "@/components/GenerateVoucher/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import Dropdown from "@/components/Dropdown/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { editConfirm } from "@/util/editConfirm";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { checkPermission } from "@/util/permission";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import type { IGetAttachFileListBack } from "@/components/UploadFileDialog/types";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import ColumnSet from "@/components/ColumnSet/index.vue";
import type { IColItem } from "@/components/ColumnSet/utils";
import { 
    getSlotIsFreeze, 
    getSlotClassName, 
    getShowColumn, 
    getColumnWidth,
    alterArrayPos,
    getColumnListApi,
} from "@/components/ColumnSet/utils";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import { isFilterMultile } from "@/components/TableHeaderFilter/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import type { IIETypeItem } from "@/views/Cashier/type";

import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
const forcedShutdown = ref(false);

const accountSubjectStore = useAccountSubjectStore();
const trialStatusStore = useTrialStatusStore();
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = voucherGroupStore.voucherGroupList;

let tableData = ref<Array<ITableItem>>([]);
let loading = ref<boolean>(false);
let emptyText = ref("");
let currentSlot = ref("main");
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
let addOrEdit = ref("");
let editDraftPic = ref("");
let transferenceDialog = ref(false);
let draftTableRef = ref<InstanceType<typeof Table>>();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
let searchInfo = ref({
    startDate: dayjs(new Date()).subtract(7, "day").format("YYYY-MM-DD"),
    endDate: dayjs(new Date()).format("YYYY-MM-DD"),
    startVDate: "",
    endVDate: "",
    vgId: 0,
    startVNum: "",
    endVNum: "",
    draftNo: "",
    drawerName: "",
    acceptorName: "",
    draftType: 0,
    draftStatus: [] as number[],
    minAmount: "",
    maxAmount: "",
});
const filterSearchInfo:IFSearchItem = reactive({
    draftNo: "",
    drawerName: "",
    acceptorName: "",
    draftStatus: [] as number[],
    draftType: 0,
});
let module = ref(300);

let showAll = ref<boolean>(false);
let importShow = ref<boolean>(false);
let draftColumns = ref<Array<IColumnProps>>([]);
let draftColumnsSet = ref<Array<IColumnProps>>([]);
let tableDataInquiry = ref();
let transferData = reactive({
    transferee: "",
    amount: "",
    transferDate: "",
    id: "",
});
let currentClickRowId = ref("");
let currentClickProp = ref("");
const currentPeriodInfo = ref("");
const routerArray = toRef(routerArrayStore, "routerArray");

function disabledDateStart(time: Date) {
    let endDate = dayjs(searchInfo.value.endDate).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(searchInfo.value.startDate).valueOf();
    return time.getTime() < startDate;
}
function disabledStartVDate(time: Date) {
    const endVDate = dayjs(searchInfo.value.endVDate).valueOf();
    return time.getTime() > endVDate;
}
function disabledEndVDate(time: Date) {
    let startVDate = dayjs(searchInfo.value.startVDate).valueOf();
    return time.getTime() < startVDate;
}

let draftCategory = ref("1");
let registrationRealNameStatus = ref(0);
let getStatusReason = ref("");
const generateVoucherView = ref<InstanceType<typeof GenerateVoucher>>();
const genVoucherQueryParameters = ref(new DraftQueryParameters());
const genVoucherChanged = ref(false);
const genVoucherErrorTableColumns = ref<IColumnProps[]>([
    {
        label: "票据类型",
        prop: "draftTypeName",
        align: "left",
        headerAlign: "left",
        width: 120,
    },
    {
        label: "票据号",
        prop: "draftNo",
        align: "left",
        headerAlign: "left",
        width: 110,
    },
    {
        label: "收支类别",
        prop: "ietypeName",
        align: "left",
        headerAlign: "left",
        width: 90,
    },
    {
        label: "金额",
        prop: "amount",
        align: "left",
        headerAlign: "left",
        width: 90,
        formatter: (row: DraftDocumentModel) => {
            return row.draftAmount.toFixed(2);
        },
    },
    {
        slot: "errorInfo",
    },
]);

watch(draftCategory, (newVal) => {
    if (newVal === "2") {
        getStatusReason.value = "changeTab";
        JDAccountStatus();
    }
    loadTableData();
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    loadTableData();
});
let draftStatusOptions = ref<Array<IDraftStatusOptions>>([]);
let draftTypeOptions = ref<Array<IDraftStatusOptions>>([]);
let IETypeOPtions = ref<IETypeOption[]>([]);
const draftStatusList = ref<Array<Option>>([]);
const getGraftStatusOptions = () => {
    request({
        url: `/api/Draft/GetStatusList`,
        method: "post",
    })
        .then((res: IResponseModel<IDraftStatusOptions[]>) => {
            if (res.state === 1000) {
                draftStatusOptions.value = res.data.map((item: IDraftStatusOptions) => {
                    return { name: item.name === "无状态" ? "全部" : item.name, value: item.value };
                });
                draftStatusList.value = draftStatusOptions.value.filter(v => v.name !== "全部").map((i) => new Option(Number(i.value), i.name));
                searchInfo.value.draftStatus = draftStatusList.value.map((item) => item.id);
            }
        })
        .catch((e) => {
            console.log(e);
        })
        .finally(() => {});
};
const searchOptions = ref();
watch(
    () => [draftStatusList],
    () => {
        searchOptions.value = {
            "draftStatus": draftStatusList.value,
        }
    },
    {
        immediate: true,
        deep: true,
    }
)
const getGraftTypeOptions = () => {
    request({
        url: `/api/Draft/GetDraftType`,
        method: "post",
    })
        .then((res: IResponseModel<IDraftStatusOptions[]>) => {
            if (res.state === 1000) {
                draftTypeOptions.value = res.data.map((item: IDraftStatusOptions) => {
                    return { name: item.name === "无类别" ? "全部" : item.name, value: item.value };
                });
                searchInfo.value.draftType = 0;
            }
        })
        .catch((e) => {
            console.log(e);
        })
        .finally(() => {});
};
const handleAmountInput = (e: any, key: string): void => {
    (searchInfo.value as any)[key] =
        e
            .replace(/[^-\d^.]+/g, "") // 第二步：把不是数字、不是小数点和不是负号的字符过滤掉
            .replace(/^-?(0+)(\d)/, "-$2") // 第三步：第一位可以是负号，0开头，0后面为数字，则过滤掉0，并保留负号
            .replace(/^\./, "0.") // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
            .match(/^-?\d*(\.?\d{0,2})/g)[0] || ""; // 第五步：最终匹配得到结果，以数字或负号开头，只有一个小数点，而且小数点后面只能有0到2位小数
};
let IETypeObject = ref<{ [key: number]: [value: string] }>({});
const IETypeName = ref("");
// 获取收支类别
const getIETypeList = () => {
    request({
        url: "/api/IEType/List",
        method: "get",
    }).then((res: IResponseModel<{ rows: Array<IIETypeItem> }>) => {
        let data: IIETypeItem[] = res.data.rows;
        IETypeOPtions.value = data.filter((item) => item.haschild !== 1 && Number(item.num3) !== 1)
            .map((item: IIETypeItem) => {
                return {
                    subkey: Number(item.subkey),
                    value2: item.value2,
                };
            });
        IETypeObject.value = data.reduce((acc: any, cur: any) => {
            acc[cur.subkey] = cur.value2;
            return acc;
        }, {});
    });
};

// 改变收支类别
function changeIEType(val: number, row: any) {
    if (IETypeObject.value) {
        IETypeName.value = IETypeObject.value[val] as unknown as string;
    }
    request({
        url: "/api/Draft/UpdateIEType",
        method: "post",
        params: { id: row.id, ieType: val },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            row.ieType = val;
            currentClickRowId.value = "-1";
            ElNotify({
                type: "success",
                message: res.data,
            });
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
}
const isErp = ref(window.isErp);
// 查看凭证
function ViewCredentials(pid: string, vid: string) {
    globalWindowOpenPage(`/Voucher/VoucherPage?pid=${pid}&vid=${vid}&fcode=draft-cancreatevoucher`, "查看凭证");
}
let checkedTableData = reactive<ITableItem[]>([]);
let init = true;
function getQueryVal(name: string) {
    if (isKeyOfIFSearchItem(name)) {  
        if (searchInfo.value[name]) {
            return searchInfo.value[name];
        }
    }
    return "";
}
function getCommonSelect(name: keyof IFSearchItem, option: Option[]) {  
    const list1 = searchInfo.value[name] as number[];  
    const list2 = filterSearchInfo[name] as number[]; 
    if (list2.length > 0) { 
        if (list1.length === option.length && list2.length === option.length) {  
            return "";  
        }  
        const set2 = new Set(list2);  
        const commonList = list1.filter(value => set2.has(value));    
        if (commonList.length) {  
            return commonList.join();  
        } else if (list1.length) {  
            return list1.join();  
        } else {  
            return list2.join();  
        }   
    } else {
        return list1.length === option.length ? "" : list1.join();
    } 
} 
function restFilterInfo() {
    filterSearchInfo.draftStatus = [];
    filterSearchInfo.draftNo = "";
    filterSearchInfo.drawerName = "";
    filterSearchInfo.drawerName = "";
    filterSearchInfo.draftType = 0;
}
const loadTableData = async () => {
    if (draftCategory.value === "2") {
        return;
    }
    if (init) {
        const { searchStartDate, searchEndDate, vdate = "", vnum = "", vgname = "", draftNo = "" } = route.query as any;
        const assignParams = {
            startVDate: vdate,
            endVDate: vdate,
            startVNum: vnum,
            endVNum: vnum,
            draftNo,
            vgId: 0,
            startDate: searchInfo.value.startDate,
            endDate: searchInfo.value.endDate,
        };
        if (vnum && vgname) {
            const targetVgid = voucherGroup.find((item) => item.name === vgname)?.id;
            assignParams.vgId = targetVgid ?? 1;
        }
        if (searchStartDate && searchEndDate) {
            assignParams.startDate = searchStartDate;
            assignParams.endDate = searchEndDate;
        }
        Object.assign(searchInfo.value, assignParams);
        const timer = setTimeout(() => {
            tryClearCustomUrlParams(route);
            clearTimeout(timer);
        });
    }
    init = false;
    const params = {
        startDate: searchInfo.value.startDate,
        endDate: searchInfo.value.endDate,
        draftType: searchInfo.value.draftType ? searchInfo.value.draftType : "",
        draftStatus: getCommonSelect("draftStatus",  draftStatusList.value),
        draftNo: getQueryVal("draftNo"),
        drawerName: getQueryVal("drawerName"),
        acceptorName: getQueryVal("acceptorName"),
        minAmount: searchInfo.value.minAmount ? Number(searchInfo.value.minAmount) : "",
        maxAmount: searchInfo.value.maxAmount ? Number(searchInfo.value.maxAmount) : "",
        showDetail: 1,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        startVDate: searchInfo.value.startVDate,
        endVDate: searchInfo.value.endVDate,
        vgId: searchInfo.value.vgId,
        startVNum: searchInfo.value.startVNum,
        endVNum: searchInfo.value.endVNum,
    };

    loading.value = true;
    emptyText.value = " ";
    await request({
        url: "/api/Draft/PagingList?" + getUrlSearchParams(params),
        method: "get",
    })
        .then((res: any) => {
            loading.value = false;
            let data: ITableItem[] = res.data.data;
            if (res.state === 1000) {
                tableData.value = data;
                emptyText.value = data.length ? "" : "暂无数据";
                paginationData.total = res.data.count;
            }
        })
        .catch((e) => {
            loading.value = false;
            console.log(e);
        });
};
const handleClose = () => containerRef.value?.handleClose();
const handleReset = () => {
    searchInfo.value.startDate = dayjs(new Date()).subtract(7, "day").format("YYYY-MM-DD");
    searchInfo.value.endDate = dayjs(new Date()).format("YYYY-MM-DD");
    searchInfo.value.draftNo = "";
    searchInfo.value.drawerName = "";
    searchInfo.value.acceptorName = "";
    searchInfo.value.draftType = 0;
    searchInfo.value.draftStatus = draftStatusList.value.map((item) => item.id);
    searchInfo.value.minAmount = "";
    searchInfo.value.maxAmount = "";
    searchInfo.value.startVDate = "";
    searchInfo.value.endVDate = "";
    searchInfo.value.vgId = 0;
    searchInfo.value.startVNum = "";
    searchInfo.value.endVNum = "";
    restFilterInfo();
};
const searchBoxHearder = ref(false);
const handleSearchSubmit = () => {
    if (!searchInfo.value.startDate || !searchInfo.value.endDate) {
        ElNotify({ type: "warning", message: "亲，起止日期不能为空!" });
        return;
    }
    searchBoxHearder.value = false;
    filterSearchInfo.draftStatus = [];
    handleClose();
    loadTableData();
    currentPeriodInfo.value = (searchInfo.value.startDate || " ") + " 至 " + (searchInfo.value.endDate || " ");
};
function addNewDraft() {
    currentSlot.value = "add";
    addDraftView.value.changeAddInit(true);
    addOrEdit.value = "add";
    let dom = document.querySelector(".line-item-field-memo .el-textarea__inner") as HTMLTextAreaElement;
    if (dom?.style.height) {
        dom.style.height = "auto";
    }
}
let addDraftView = ref();
function handleEditRow(item: ITableItem) {
    if (item.isCreateVoucher) {
        ElConfirm("亲，票据已生成凭证，无法编辑。", true);
        return false;
    }
    if (item.isGotMoney || (item.draftStatus !== 1 && item.draftStatus !== 0)) {
        ElConfirm("请检查票据状态是否正确。", true);
    }
    // 获取票据图片
    request({
        url: `/api/Draft/GetPic?id=${item.id}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                editDraftPic.value = res.data;
                addDraftView.value?.editInit(item, editDraftPic.value);
            }
        })
        .catch((e) => {
            console.log(e);
        });

    addOrEdit.value = "edit";
    currentSlot.value = "add";
}

const readonly = ref(false);
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
function handleCheckAttachFile(row: ITableItem) {
    request({ url: "/api/Draft/GetAttachFileList", method: "post", params: { id: row.id } }).then(
        (res: IResponseModel<IGetAttachFileListBack>) => {
            if (res.state === 1000 && res.data.result) {
                const list = res.data.data.map((item: any) => {
                    item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                    return item;
                });
                const dateMonth = row.draftCreateDateStr.split("-").slice(0, 2);
                const startDate = dateMonth.join("-") + "-01";
                const day = getDaysInMonth(Number(dateMonth[0]), Number(dateMonth[1]));
                const endDate = dateMonth.join("-") + "-" + day;
                const _params = {
                    eRecordSearchDate: { startDate, endDate },
                    id: row.id,
                };
                uploadFileDialogRef.value?.open(_params, list, res.data.parentId);
            } else {
                ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
            }
        }
    );
}
async function saveAttachFile(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
    const row = tableData.value.find((item) => item.id === _params.id);
    if (delFileids.length === 0 || !row?.vId) {
        saveAttachFileFn(_params.id, newFileids, delFileids, fileList);
        return;
    }
    const needToast = await checkNeedToastWithBillAndVoucher(_params.id, delFileids.join(","));
    if (needToast) {
        showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
            saveAttachFileFn(_params.id, newFileids, delFileids, fileList, batchDelte);
        });
    } else {
        saveAttachFileFn(_params.id, newFileids, delFileids, fileList);
    }
}
function saveAttachFileFn(id: string, newFileids: number[], delFileids: number[], fileList: any[], isNeedSaveToVoucherForDelete = false) {
    const params = {
        id,
        newFileids: newFileids.join(","),
        delFileids: delFileids.join(","),
        isNeedSaveToVoucherForAdd: true,
        isNeedSaveToVoucherForDelete,
    };
    const newAttachCount = fileList.length;
    const newAttachFiles = fileList.map((item) => item.fileId).join(",");
    request({ url: "/api/Draft/SaveAttachFile", method: "post", params }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            const row = tableData.value.find((item) => item.id === id);
            if (row) {
                row.attachsCount = newAttachCount;
                row.attachFiles = newAttachFiles;
            }
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
}
async function checkNeedToastWithBillAndVoucher(id: string, attachFiles: string) {
    return await request({
        url: "/api/Draft/GetNeedSaveToVoucher",
        method: "post",
        params: { id, attachFiles },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000) return false;
            return res.data;
        })
        .catch(() => {
            return false;
        });
}

function handleEditDraftSuccess() {
    currentSlot.value = "main";
    loadTableData();
}
function handlePayment(item: ITableItem) {
    if (item.isGotMoney) {
        ElConfirm("亲，票据已收款，无法重复操作。", true);
        return false;
    }
    if (item.isTransfered) {
        ElConfirm("亲，票据已转让，无法收款。", true);
        return false;
    }
    if (item.draftStatus == 2) {
        ElConfirm("亲，票据状态为背书待签收，无法收款。", true);
        return false;
    }
    if (item.draftStatus == 3) {
        ElConfirm("亲，票据状态为背书已签收，无法收款。", true);
        return false;
    }
    if (item.draftStatus == 4) {
        ElConfirm("亲，票据状态为票据已结清，无法收款。", true);
        return false;
    }
    if (item.draftStatus == 5) {
        ElConfirm("亲，票据状态为买断式贴现已签收，无法收款。", true);
        return false;
    }
    request({
        url: `/api/Draft/GetMoney?id=${item.id}`,
        method: "put",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "收款成功",
                });
                loadTableData();
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((e) => {
            throw e;
        });
}
function handleTransAmount(val: string) {
    if (isNaN(parseFloat(val))) {
        transferData.amount = "";
        return;
    }
    transferData.amount = ("" + val)
        .replace(/[^\d^.]+/g, "")
        .replace(/^0+(\d)/, "$1")
        .replace(/^\./, "0.")
        .match(/^\d*(\.?\d{0,2})/g)?.[0] as string;
}
function handleTransference(item: ITableItem) {
    if (item.canTransfer === 0) {
        ElConfirm("亲，票据不允许转让。", true);
        return false;
    }
    if (item.isGotMoney) {
        ElConfirm("亲，票据已收款，无法转让。", true);
        return false;
    }
    if (item.isTransfered) {
        ElConfirm("亲，票据已转让，无法重复操作。", true);
        return false;
    }
    if (item.draftStatus == 2) {
        ElConfirm("亲，票据状态为背书待签收，无法转让。", true);
        return false;
    }
    if (item.draftStatus == 3) {
        ElConfirm("亲，票据状态为背书已签收，无法转让。", true);
        return false;
    }
    if (item.draftStatus == 4) {
        ElConfirm("亲，票据状态为票据已结清，无法转让。", true);
        return false;
    }
    transferenceDialog.value = true;
    transferData.id = item.id;
}
function submitTransfer() {
    if (!transferData.amount) {
        ElNotify({
            message: "亲，请填写转让金额。",
            type: "warning",
        });
        return false;
    }

    let params = {
        id: transferData.id,
        transferFee: transferData.transferee,
        amount: transferData.amount,
        transferDate: transferData.transferDate || dayjs(new Date()).format("YYYY-MM-DD"),
    };
    request({
        url: "/api/Draft/TransferDraft",
        method: "put",
        params,
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "转让成功",
                });
                loadTableData();

                transferenceDialog.value = false;
                resetTransferData();
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((e) => {
            throw e;
        });
}
function resetTransferData() {
    transferData.id = "";
    transferData.transferee = "";
    transferData.amount = "";
    transferData.transferDate = "";
}
function handleDeleteRow(item: ITableItem) {
    if (item.isCreateVoucher) {
        ElNotify({
            type: "warning",
            message: "亲，票据已生成凭证，无法删除。",
        });
        return false;
    }
    ElConfirm("确定删除票据数据吗？").then((r: boolean) => {
        if (r) {
            request({
                url: `/api/Draft?id=${item.id}`,
                method: "delete",
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({
                            message: "删除成功",
                            type: "success",
                        });
                        loadTableData();
                    } else {
                        ElNotify({
                            message: res.msg,
                            type: "warning",
                        });
                    }
                })
                .catch((e) => {
                    throw e;
                });
        }
    });
}
const tableSelectionChange = (value: any) => {
    checkedTableData = value;
};
function tableRowClick(row: any, column: any, event: any) {
    let selected = checkedTableData.findIndex((item: ITableItem) => item.id === row.id) >= 0 ? true : false;
    draftTableRef.value?.getTable()?.toggleRowSelection(row, !selected);
}
function checkedRowClass(data: { row: any; rowIndex: number }): string {
    if (checkedTableData.findIndex((item: ITableItem) => item.id === data.row.id) >= 0) {
        return "current-row el-table__row--striped";
    }

    return "";
}
const tableCellClick = (row: any, column: any, cell: any, event: any) => {
    currentClickRowId.value = row.id;
    currentClickProp.value = column.property;
    IETypeName.value = IETypeObject.value[row.ieType] as unknown as string;
};
function handleDelete() {
    if (!checkedTableData.length) {
        ElConfirm("请选中需要删除的票据！", true);
        return false;
    } else if (checkedTableData.find((v: ITableItem) => v.isCreateVoucher)) {
        ElConfirm("亲，选中的数据中存在已生成凭证的票据，无法删除！", true);
        return false;
    }
    ElConfirm("确定删除当前选中的票据数据吗？").then((r: boolean) => {
        if (r) {
            let ids: string[] = [];
            checkedTableData.forEach((v: ITableItem) => {
                ids.push(v.id);
            });
            request({
                url: `/api/Draft/Batch`,
                data: ids,
                method: "delete",
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({
                            message: "删除成功",
                            type: "success",
                        });
                        loadTableData();
                    } else {
                        ElNotify({
                            message: res.msg || "删除失败",
                            type: "warning",
                        });
                    }
                })
                .catch((e) => {
                    throw e;
                });
        }
    });
}
let cVoucherType = ref(0);
let isGenVoucher = false;
function openVoucherFromDraft(createVoucherType: number) {
    if (isGenVoucher) {
        ElNotify({
            type: "warning",
            message: "正在生成凭证，请稍后",
        });
        return false;
    }
    isGenVoucher = true;
    cVoucherType.value = createVoucherType;
    var existHasVoucherDraft = false;
    var statusErrorDraft = false;
    var noIETypeDraft = false;
    //移除可能选中的全选按钮和已生成凭证数据
    for (var j = checkedTableData.length - 1; j >= 0; j--) {
        if (checkedTableData[j] === undefined) {
            uncheckRow(checkedTableData[j]);
        } else if (createVoucherType == 1 && checkedTableData[j].isCreateDraftVoucher) {
            existHasVoucherDraft = true;
            uncheckRow(checkedTableData[j]);
        } else if (createVoucherType == 2 && checkedTableData[j].isCreateGetMoneyVoucher) {
            existHasVoucherDraft = true;
            uncheckRow(checkedTableData[j]);
        } else if (createVoucherType == 3 && checkedTableData[j].isCreateDiscountVoucher) {
            existHasVoucherDraft = true;
            uncheckRow(checkedTableData[j]);
        } else if (createVoucherType == 1 && checkedTableData[j].draftStatus != 1) {
            //票据凭证 状态不为[提示收票已签收]
            statusErrorDraft = true;
            uncheckRow(checkedTableData[j]);
        } else if (createVoucherType == 2 && checkedTableData[j].draftStatus != 4) {
            //收款凭证 状态不为[票据已结清]
            statusErrorDraft = true;
            uncheckRow(checkedTableData[j]);
        } else if (checkedTableData[j].ieType == null) {
            noIETypeDraft = true;
            uncheckRow(checkedTableData[j]);
        }
    }
    if (existHasVoucherDraft || statusErrorDraft || noIETypeDraft) {
        ElConfirm("亲，已生成凭证、票据状态不符、没有设置收支类别的票据将会被跳过，是否继续生成凭证？").then((r: Boolean) => {
            if (r) {
                createDraftAndVoucherInfo(checkedTableData);
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else {
        createDraftAndVoucherInfo(checkedTableData);
    }
}
let genVoucherDraftIds = ref<string[]>([]);
let loadDialogShow = ref(false);
function createDraftAndVoucherInfo(checkItems: ITableItem[]) {
    genVoucherDraftIds.value = [];
    if (checkItems.length > 0) {
        // global_process("start");
        checkItems.forEach((item) => {
            genVoucherDraftIds.value.push(item.id);
        });
        // global_process("start");
        loadDialogShow.value = true;
        getDraftAndVoucherInfo();
    } else {
        isGenVoucher = false;
        ElNotify({
            type: "warning",
            message: "请选择票据后生成凭证",
        });
        return false;
    }
}

function getDraftAndVoucherInfo() {
    if (loadDialogShow.value) {
        useLoading().enterLoading("努力加载中，请稍候...");
    }
    request({
        url: `/api/DraftVoucher/GetVouchersFromDraft?ids=${genVoucherDraftIds.value?.join(",")}&autoMatch=${
            genVoucherQueryParameters.value.autoMatch
        }&voucherType=${cVoucherType.value}`,
        method: "post",
    })
        .then((res: any) => {
            if (loadDialogShow.value) {
                useLoading().quitLoading();
            }
            loadDialogShow.value = false;

            const data = res as IResponseModel<IBatchGenerateVoucherModel<DraftWithVoucherModel>>;

            if (data.data) {
                const timer = setTimeout(() => {
                    isGenVoucher = false;
                    clearTimeout(timer);
                }, 1000);
                currentSlot.value = "generateVoucher";
                genVoucherChanged.value = false;
                generateVoucherView.value?.loadDocumentList(data.data);
            } else {
                isGenVoucher = false;
                ElNotify({
                    type: "warning",
                    message: res.msg || "出现错误，请您刷新页面重试或联系系统管理员",
                });
            }
        })
        .catch((e: any) => {
            isGenVoucher = false;
            useLoading().quitLoading();
            ElNotify({
                type: "warning",
                message: "请稍后再试～",
            });
        });
}
function genVoucherChangedHandle() {
    genVoucherChanged.value = true;
}

let isSaving = false;
async function saveGenVoucher() {
    let isTrilExpired = await handleTrialExpired({msg: ExpiredToBuyDialogEnum.generateVoucher});
    if(isTrilExpired){
        return;
    }
    if (isSaving) {
        ElNotify({ message: "正在保存，请稍后！", type: "warning" });
        return;
    }
    isSaving = true;
    const parameters = generateVoucherView.value?.getGenVoucherParameters();
    if (parameters) {
        useLoading().enterLoading("努力加载中，请稍后...");
        request({
            url: "/api/DraftVoucher/FindNeedAutoInsertAccountSubject",
            method: "post",
            data: parameters,
        })
            .then((res: any) => {
                const data = res as IResponseModel<IGenVoucherNeedInsertAsub>;
                if (data.state === 1000) {
                    new Promise<void>((resolve, reject) => {
                        for (let i = 0; i < parameters.temporaryAccountSubjectList.length; i++) {
                            if (
                                parameters.temporaryAccountSubjectList[i].asubCode
                                    .toString()
                                    .indexOf(parameters.temporaryAccountSubjectList[i].parentAsubCode.toString()) !== 0
                            ) {
                                isSaving = false;
                                useLoading().quitLoading();
                                ElConfirm("您的科目编码长度不足，智能匹配失败，是否立即前往科目编码设置？").then((r) => {
                                    if (r) {
                                        globalWindowOpenPage("/Settings/AccountSubject", "科目");
                                    }
                                });
                                reject();
                                return;
                            }
                        }
                        if (data.data.autoInsertAsubList.length > 0) {
                            const parentAsubs = [];
                            const asubs = [];
                            for (let i = 0; i < data.data.autoInsertAsubList.length; i++) {
                                parentAsubs.push(data.data.autoInsertAsubList[i].parentAsubName);
                                asubs.push(data.data.autoInsertAsubList[i].asubName);
                            }
                            const msg =
                                '<div>' +
                                parentAsubs.join("、") +
                                "已有凭证，将新增同名下级科目"+ '<br>' +
                                asubs.join("、") +
                                "替代，您要继续吗？" +
                                "</div>";
                            useLoading().quitLoading();
                            ElAlert({ message: msg, leftJustifying: true }).then((r) => {
                                if (r) {
                                    useLoading().enterLoading("努力加载中，请稍后...");
                                    resolve();
                                } else {
                                    isSaving = false;
                                    reject();
                                }
                            });
                        } else {
                            resolve();
                        }
                    }).then(() => {
                        request({
                            url: "/api/DraftVoucher/Submit",
                            method: "post",
                            data: { ...parameters, voucherType: cVoucherType.value },
                        })
                            .then((res: IResponseModel<IDraftGenVoucherResult>) => {
                                useLoading().quitLoading();
                                const data = res;
                                if (data.state === 1000) {
                                    dispatchReloadAsubAmountEvent();
                                    generateVoucherView.value?.loadSaveResult(data.data);
                                    accountSubjectStore.getAccountSubject();
                                    useAssistingAccountingStore().getAssistingAccounting();
                                    const timer = setTimeout(() => {
                                        isSaving = false;
                                        clearTimeout(timer);
                                    }, 1000);
                                } else {
                                    ElNotify({ message: data.msg || "出现错误，请稍后重试！", type: "warning" });
                                    isSaving = false;
                                }
                            })
                            .catch((err: any) => {
                                if (err.code !== "ERR_NETWORK") {
                                    isSaving = false;
                                    useLoading().quitLoading();
                                }
                            });
                    });
                } else {
                    isSaving = false;
                    useLoading().quitLoading();
                }
            })
            .catch(() => {
                isSaving = false;
                useLoading().quitLoading();
            });
    } else {
        isSaving = false;
    }
}
function genVoucherSaveSuccess() {
    currentSlot.value = "main";
    loadTableData();
}
function genVoucherCheckboxChanged(type: "isMerge" | "autoMatch") {
    new Promise<boolean>((resolve) => {
        if (genVoucherChanged.value) {
            ElConfirm("系统可能不会保存您做的更改，确定要切换吗？").then((r) => {
                if (r) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(true);
        }
    }).then((r) => {
        if (r) {
            if (type === "isMerge") {
                generateVoucherView.value?.loadDocumentList();
            } else {
                getDraftAndVoucherInfo();
            }
        } else {
            genVoucherQueryParameters.value[type] = !genVoucherQueryParameters.value[type];
        }
    });
}
function uncheckRow(row: ITableItem) {
    draftTableRef.value!.getTable()?.toggleRowSelection(row, false);
}
function DownloadExcelTemplate() {
    globalExport("/api/Draft/ExportTemplate");
}
const handleImport = () => {
    importShow.value = true;
};
const handleExport = () => {
    if (!searchInfo.value.startDate || !searchInfo.value.endDate) {
        ElNotify({ type: "warning", message: "亲，起止日期不能为空!" });
        return;
    }
    const params1 = {
        startDate: searchInfo.value.startDate,
        endDate: searchInfo.value.endDate,
        startVDate: searchInfo.value.startVDate,
        endVDate: searchInfo.value.endVDate,
        vgId: searchInfo.value.vgId,
        startVNum: searchInfo.value.startVNum,
        endVNum: searchInfo.value.endVNum,
        draftType: searchInfo.value.draftType ? searchInfo.value.draftType : "",
        draftStatus: getCommonSelect("draftStatus",  draftStatusList.value),
        draftNo: getQueryVal("draftNo"),
        drawerName: getQueryVal("drawerName"),
        acceptorName: getQueryVal("acceptorName"),
        minAmount: searchInfo.value.minAmount,
        maxAmount: searchInfo.value.maxAmount,
        showDetail: "",
    };
    globalExport("/api/Draft/Export?" + getUrlSearchParams(params1));
};

const uploadSuccess = (res: IResponseModel<string>) => {
    if (res.state === 1000) {
        ElNotify({
            type: "success",
            message: "导入成功！",
        });
        importShow.value = false;
        loadTableData();
    } else {
        ElNotify({
            type: "warning",
            message: res.msg,
        });
    }
};

// 获取实名注册状态
function JDAccountStatus() {
    request({
        url: "/api/JDOperate/GetStatus",
        method: "post",
    })
        .then((res: IResponseModel<IJDStatusRes>) => {
            if (res.state === 1000) {
                if (res.data.regStatus === 0 || res.data.regStatus === 1 || res.data.regStatus === 3) {
                    // ElConfirm('请先前往京东票据平台实名认证，完成后即可签约', true);

                    //未注册成功
                    registrationRealNameStatus.value = 1;
                } else if (res.data.regStatus === 2) {
                    if (res.data.realnameStatus == 0 || res.data.realnameStatus == 1 || res.data.realnameStatus == 3) {
                        //注册成功 未实名
                        registrationRealNameStatus.value = 2;
                    } else if (res.data.realnameStatus == 2) {
                        //注册 实名成功
                        registrationRealNameStatus.value = 3;
                    }
                } else {
                    registrationRealNameStatus.value = 0;
                }
                if (getStatusReason.value === "changeTab") {
                    if (registrationRealNameStatus.value == 2 || registrationRealNameStatus.value == 3) {
                        //询价列表
                        GetQueryPriceList();
                    } else if (registrationRealNameStatus.value == 1) {
                        JDRegister();
                    } else {
                        // ElNotify({ type: "warning", message: "查询京东签约状态失败，请稍后重新询价" });
                    }
                }
            }
        })
        .catch((e) => {
            throw e;
        });

    // return registrationRealNameStatus.value;
}

//注册
function JDRegister() {
    request({
        url: "/api/JDOperate/Register",
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000 && res.data.code === 0) {
                draftCategory.value = "1";
                let result = res.data.data;
                formJumpJDView(result);
                ElConfirm("先前往京东票据平台注册，完成后即可询价", true).then((res: any) => {});
            }
        })
        .catch((e) => {
            throw e;
        });
}
function formJumpJDView(result: any) {
    //打开京东实名页面
    let form: any = document.getElementById("registerForm");
    form.action = result.url;
    let encrypt: any = document.getElementById("encrypt");
    encrypt.value = result.encrypt;
    let sign: any = document.getElementById("gw-sign");
    sign.value = result.sign;
    let signType: any = document.getElementById("gw-sign-type");

    signType.value = result.signType;
    let envKey: any = document.getElementById("jrgw-env-key");

    envKey.value = result.envKey;
    let encryptType: any = document.getElementById("gw-encrypt-type");

    encryptType.value = result.encryptType;
    let time: any = document.getElementById("jrgw-request-time");
    time.value = result.time;
    let userId: any = document.getElementById("jrgw-enterprise-user-id");
    userId.value = result.userId;
    let userIdType: any = document.getElementById("jrgw-user-id-type");
    userIdType.value = result.userIdType;
    form.submit();
}
// 询价
let priceDetails = ref<any[]>([]);
function GetQueryPriceList() {
    priceDetails.value = [];
    let data = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    loading.value = true;
    request({
        url: "/api/Draft/GetQueryPricePagingList",
        method: "post",
        data,
        headers: { "Content-Type": "application/json" },
    })
        .then((res: any) => {
            loading.value = false;

            if (res.state === 1000) {
                tableDataInquiry.value = res.data.data;
                paginationData.total = res.data.count;
                //逐条询价
                if (res.data.data.length) {
                    res.data.data.forEach((item: any, index: number) => {
                        request({
                            url: "/api/Draft/QueryPrice",
                            method: "post",
                            params: { id: item.id, index },
                        }).then((res: any) => {
                            if (res.state === 1000) {
                                //询价成功 显示最高的价格
                                if (res.data != null && res.data.list.length > 0) {
                                    var maxPrice = res.data.list[0];
                                    tableDataInquiry.value[res.data.index].discountRate = maxPrice.discountRate;
                                    tableDataInquiry.value[res.data.index].discountAmount = maxPrice.discountAmount;
                                    tableDataInquiry.value[res.data.index].discountBank = maxPrice.discountBank;
                                    tableDataInquiry.value[res.data.index].discountBankCode = maxPrice.discountBank;

                                    var detailList = { index: res.data.index, list: res.data.list };
                                    priceDetails.value.push(detailList);
                                } else {
                                    //Notify("提示", "未查询到贴现信息。");
                                }
                            } else if (res.state === 2000) {
                                //未注册实名
                                ElConfirm("前往京东票据平台完成注册即可自动询价。", true).then(() => JDRegister());
                            } else {
                                //异常
                                ElNotify({ type: "warning", message: res.msg });
                            }
                        });
                    });
                }
            }
        })
        .catch((e) => {
            loading.value = false;
        });
}

const isEditting = computed(() => {
    return addDraftView.value?.getEditStatus() || currentSlot.value === "generateVoucher";
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
let cacheRouterQueryParams: any = null;
let firstInit = true;
onBeforeRouteLeave((to, from, next) => {
    firstInit = false;
    cacheRouterQueryParams = from.query;
    next();
});
const startVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.value.startVNum = val;
};
const endVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.value.endVNum = val;
};
const handleVgIdChange = (val: number) => {
    if (val === 0) {
        searchInfo.value.startVNum = "";
        searchInfo.value.endVNum = "";
    }
};
const reLoadCurrentPage = () => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if (currentRouterModel.stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        currentRouterModel.stop = true;
    }
};
const isFromOtherPage = (page: "voucherList" | "ERecord"): boolean => {
    if (!cacheRouterQueryParams?.from && route.query.from === page) {
        return true;
    }
    if (
        cacheRouterQueryParams?.from === page &&
        route.query?.from === page &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }

    return false;
};
let hasMounted = false;
onActivated(() => {
    if (!hasMounted) return;
    if ((isFromOtherPage("voucherList") || isFromOtherPage("ERecord")) && !firstInit) {
        if (isEditting.value) {
            // 正在编辑票据页面
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
    }
});
onMounted(async () => {
    await loadTableData();
    hasMounted = true;
    getIETypeList();
    getGraftStatusOptions();
    getGraftTypeOptions();
    currentPeriodInfo.value = (searchInfo.value.startDate || " ") + " 至 " + (searchInfo.value.endDate || " ");
    window.addEventListener("reloadIETypeList", getIETypeList);
    window.addEventListener("DraftVoucherChange", loadTableData);
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop + "" !== "undefined") {
        setTimeout(() => {
            delete (currentRouterModel as any).stop;
        });
    }
});

onUnmounted(() => {
    window.removeEventListener("reloadIETypeList", getIETypeList);
    window.removeEventListener("DraftVoucherChange", loadTableData);
});

//列设置弹窗
const columnSetShow = ref(false);
const columnSetRef = ref<InstanceType<typeof ColumnSet>>();
const allColumns = ref<IColItem[]>([]);
function getColumnSetList() {
    getColumnListApi(module.value).then((res) => {
        allColumns.value = res.data;
        if (!showAll.value) {
            draftColumns.value = getShowColumn(allColumns.value, draftColumnsSet.value);
            watchDrop();
        }
    }).catch((err) => {
        draftColumns.value = Columns(showAll.value);
    })
}
function openColSet() {
    columnSetShow.value = true;
    columnSetRef.value?.initData();
    nextTick(() => {
        columnSetRef.value?.rowDrop();
    })
}
function saveColumnSet(data: IColItem[]) {
    if (!showAll.value) {
        draftColumns.value = getShowColumn(data, draftColumnsSet.value);
    }
    watchDrop();
    getColumnSetList();
}
//头部列拖拽设置
const setModule = ref("Draft");
function cellDrop(oldIndex: number, newIndex: number) {
    let index1 = allColumns.value.findIndex((v) =>v.columnName === draftColumns.value[oldIndex].label);
    let index2 = allColumns.value.findIndex((v) =>v.columnName === draftColumns.value[newIndex].label);
    allColumns.value = alterArrayPos(allColumns.value, index1, index2);
    draftColumns.value = alterArrayPos(draftColumns.value, oldIndex, newIndex);
    columnSetRef.value?.saveData(module.value, allColumns.value);
}
watch(
    ()=> showAll.value,
    (newVal: boolean) => {
        if (newVal) {
            draftColumns.value = Columns(newVal);
            watchDrop();
        } else {
            draftColumnsSet.value = Columns(true);
            getColumnSetList();
        }
    },
    { immediate: true }
)
function watchDrop() {
    nextTick(() => {
        draftTableRef.value?.columnDrop(draftTableRef.value?.$el, draftColumns.value, showAll.value);
    })
}
const ieTypeRef = ref();
function handleScroll() {
    draftTableRef.value?.$el.click();
    ieTypeRef.value?.blur();
}
//表头字段模糊搜索
function isKeyOfIFSearchItem(key: string): key is keyof IFSearchItem {  
    return [
        'draftNo', 
        'drawerName', 
        'acceptorName',
        'draftStatus',
        'draftType'
    ].includes(key);  
}
function filterSearch(prop: string, data: any) {
    if (isKeyOfIFSearchItem(prop)) { 
        if (typeof data === "string" || typeof data === "number") {
            if (typeof filterSearchInfo[prop] === "string") {
                (filterSearchInfo[prop] as string) = data.toString();
                (searchInfo.value[prop] as string) = data.toString();
            }
            if (typeof filterSearchInfo[prop] === "number") {
                (filterSearchInfo[prop] as number) = Number(data);
                (searchInfo.value[prop] as number) = Number(data);
            }
            searchBoxHearder.value = true;
            loadTableData(); 
        } else {
            if (data.length > 0) {
                const filteredData: number[] = data.filter((item: any): item is number => typeof item === 'number');  
                (filterSearchInfo[prop] as number[]) = filteredData;
                loadTableData();
            }
        }
    } 
}

const showDraftTypeOptions = ref<Array<IDraftStatusOptions>>([]);
const showIETypeOPtions = ref<Array<IETypeOption>>([]);
watchEffect(() => {
    showDraftTypeOptions.value = JSON.parse(JSON.stringify(draftTypeOptions.value));
    showIETypeOPtions.value = JSON.parse(JSON.stringify(IETypeOPtions.value));
});
function draftTypeFilterMethod(value: string) {
    showDraftTypeOptions.value = commonFilterMethod(value, draftTypeOptions.value, 'name');
}
function IETypeFilterMethod(value: string) {
    showIETypeOPtions.value = commonFilterMethod(value, IETypeOPtions.value, 'value2');
}
</script>

<style lang="less" scoped>
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";

:deep(.el-select.search-info-select) {
    width: 100% !important;
}
body[erp] .content .main-content .main-center .table {
    height: calc(100vh - 140px);
}

.main-content {
    height: 100%;
    background-color: var(--white);
    :deep(.el-tab-pane) {
        height: 100%;
    }
    .center-content {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    :deep(.el-tabs.demo-tabs) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .el-tabs__content {
            flex: 1;
        }
    }
    .main-top {
        display: flex;
        justify-content: space-between;
        padding: 16px 20px;
        .button-content {
            display: flex;
            align-items: center;

            .how-draft {
                color: #1890ff;
                text-decoration: none;
                line-height: 32px;
                cursor: pointer;

                .el-icon {
                    vertical-align: middle;
                    padding-bottom: 2px;
                }
            }

            .item {
                position: relative;

                &:hover {
                    ul {
                        display: block;
                    }
                }
            }

            ul {
                z-index: 10;
                color: var(--font-color);
                background-color: var(--white);
                box-shadow: 0 0 4px var(--button-border-color);
                border-radius: 2px;
                position: absolute;
                left: 1px;
                padding: 0;
                margin: 0;

                li {
                    height: 27px;
                    line-height: 27px;
                    cursor: pointer;
                    font-size: 13px;
                    list-style: none;
                    margin: 0;
                    box-shadow: none;
                    text-align: left;
                    padding: 0 12px;
                    white-space: nowrap;
                    &:hover {
                        background-color: var(--main-color);
                        color: var(--white);
                    }
                }
            }

            .down-click {
                width: 19px;
                margin-left: 1px;
                background: url("@/assets/Icons/down-white.png") no-repeat center;
                background-color: var(--main-color);

                &:hover {
                    border-color: var(--light-main-color);
                    background-color: var(--light-main-color);

                    & + ul {
                        display: block;
                    }
                }
            }
        }

        .solid-button {
            text-decoration: none;
        }
    }

    .main-center {
        padding: 0 20px 20px;
        .draft-data-table {
            height: 100%;
            &.none-data {
                :deep(.el-table) {
                    .el-table__inner-wrapper {
                        // overflow-x: auto;
                        // .el-table__header-wrapper {
                        //     overflow: unset;
                        // }
                        .el-table__body-wrapper {
                            overflow: hidden;
                        }
                    }
                }
            }
            :deep(.el-table--default .cell) {
                .el-select {
                    width: 100%;
                }

                .el-input__inner {
                    border: none;
                    padding: 1px 3px 1px 5px;
                }

                .el-input__suffix-inner > :first-child {
                    margin: 0;
                }

                .table-plus-icon {
                    display: block;
                    margin: auto;
                }
            }
        }
    }
}

.transference-dialog,
.importShow-content {
    .transference-form,
    .importShow-main {
        padding: 20px 20px 20px 30px;
        border-bottom: 1px solid #dadada;
        .transference-form-item {
            width: 60%;
            margin: 0 auto;
        }
        :deep(.el-form-item__label) {
            width: 100px;
            text-align: right;
        }

        :deep(.line-item-field, .line-item-date) {
            width: 160px !important;
            .el-input {
                width: 100%;
                .el-input__wrapper {
                    .el-input__prefix {
                        position: absolute;
                        top: 0;
                        right: 0;
                    }
                }
            }
        }
        .import-import {
            display: flex;
            .import-file {
                padding-right: 30px;
            }
            .upload-file-name {
                display: inline-block;
                width: 240px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }
    }

    .transference-buttons,
    .importShow-buttons {
        text-align: center;
        margin-bottom: 10px;
        margin-top: 10px;
    }

    overflow: hidden;
}

div.line-item-field {
    .detail-el-select(100%);
}

.draft-data-table2 {
    :deep(.el-table) {
        .expand-col {
            .cell {
                .el-table__expand-icon {
                    .el-icon {
                        display: none !important;
                    }
                    background-image: url(/src/assets/Icons/data-grid.png) !important;
                    background-position: 35px 2px;
                    width: 20px;
                    height: 20px;
                    margin: 0 auto;
                    &.el-table__expand-icon--expanded {
                        transform: rotate(0deg) !important;

                        background-position: 19px 2px;
                    }
                }
            }
        }
    }
}
</style>
