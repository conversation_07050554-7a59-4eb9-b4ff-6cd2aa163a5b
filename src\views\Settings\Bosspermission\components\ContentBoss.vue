<template>
    <div class="slot-mini-content">
        <div class="main-top main-tool-bar space-between" :class="isErp ? 'erp-main-top' : ''">
            <div class="main-tool-left">
                <span>{{ props.asname }}</span>
            </div>
            <div class="main-tool-right">
                <a v-permission="['bosspermissions-canedit']" @click="quickAuth" class="button solid-button large-3">
                    <img src="@/assets/Icons/adduser.png" class="adduserImg" alt="adduser.png" />
                    <span class="float-l">新增老板</span>
                </a>
            </div>
        </div>
        <div class="main-center">
            <Table :columns="columns" :data="tableData" :border="false">
                <template #operator>
                    <el-table-column label="操作" align="center" header-align="center" min-width="225px">
                        <template #default="scope">
                            <a class="link" v-permission="['bosspermissions-candelete']" @click="handleDelete(scope.row)"> 删除 </a>
                        </template>
                    </el-table-column>
                </template>
                <template #blank>
                    <el-table-column label="" align="left" header-align="left" min-width="138px" :resizable="false"> </el-table-column>
                </template>
            </Table>
            <div class="tips-block">
                <div class="tips-title">老板如何看账</div>
                <div class="tips-info">关注微信公众号【老板看账】查看公司经营状况</div>
                <img class="boss-qrcode" src="@/assets/Settings/boss-qrcode.png" />
                <a class="button solid-button large-4" :href="imgSrc" download="boss.png">
                    <img class="bossdownloadImg" src="@/assets/Settings/boss-download.png" />
                    <span>下载二维码</span>
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import Table from "@/components/Table/index.vue";
import imgSrc from "@/assets/Settings/boss-qrcode-download.png";

interface ITableItem {
    userSn: number;
    userId: string;
    userName: string;
    mobile: string;
}

const props = defineProps<{ asname: string; tableData: ITableItem[] }>();

const emit = defineEmits(["add-boss", "load-data"]);

const isErp = ref<boolean>(window.isErp);

const tableData = computed(() => props.tableData);
const columns: IColumnProps[] = [
    { label: "用户", prop: "userName", align: "left", headerAlign: "left", minWidth: 235 },
    { label: "手机号", prop: "mobile", align: "center", headerAlign: "center", minWidth: 340 },
    { slot: "operator" },
    { slot: "blank" },
];
const handleDelete = (row: ITableItem) => {
    const phone = row.mobile;
    const userSn = row.userSn;
    ElConfirm("亲，确认要删除吗?").then((r: any) => {
        if (r) {
            request({ url: "/api/BossPermissions?phone=" + phone + "&userSn=" + userSn, method: "delete" }).then((result: any) => {
                if (result.state == 1000 && result.data === "Success") {
                    ElNotify({ type: "success", message: "亲，删除成功啦！" });
                    emit("load-data");
                } else {
                    ElNotify({ type: "warning", message: "亲，删除失败啦！" });
                }
            });
        }
    });
};
const quickAuth = () => emit("add-boss");
</script>

<style lang="less" scoped>
.content {
    .slot-mini-content {
        background-color: #fff;
        .main-tool-bar {
            padding: 48px 30px 20px;
            .main-tool-left {
                margin-left: 2px;
                margin-right: 10px;
                height: 22px;
                color: #929292;
                font-size: var(--h3);
                line-height: 22px;
            }

            .main-tool-right {
                a {
                    display: flex;
                    width: 116px;
                    margin-left: auto;
                    img {
                        width: 18px;
                        height: 14px;
                        margin: 6px 8px 0 16px;
                    }
                    span {
                        color: #fff;
                        font-size: 13px;
                    }
                }
            }
        }
        .main-center {
            padding: 0 30px;
            .tips-block {
                margin: 70px 216px 80px 215px;
                padding-bottom: 36px;
                background-color: #f8f8f8;
                .tips-title {
                    padding-top: 36px;
                    color: var(--font-color);
                    font-size: var(--h2);
                    line-height: 25px;
                    font-weight: 500;
                }
                .tips-info {
                    margin-top: 10px;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                }
                .boss-qrcode {
                    width: 124px;
                    display: block;
                    margin: 25px auto 16px auto;
                }
                a {
                    width: 126px;
                    text-decoration: none;
                    img {
                        width: 16px;
                        vertical-align: text-bottom;
                    }
                }
            }
            :deep(.el-table) {
                border: 1px solid #f0f0f0;
                box-sizing: border-box;
                .el-table__inner-wrapper {
                    &::before {
                        height: 0px;
                    }
                }
                tr.el-table__row,
                .el-table__header {
                    .el-table__cell {
                        border: none;
                        &:first-child {
                            .cell {
                                padding-left: 30px;
                            }
                        }
                    }
                }
                .el-table__body,
                .el-table__header {
                    .cell {
                        height: 100%;
                        font-size: var(--h4);
                        a.link {
                            font-size: var(--h4);
                        }
                    }
                }
                .el-table__header {
                    .cell {
                        font-weight: 600;
                    }
                }
                .el-table__body {
                    .cell {
                        line-height: 37px;
                    }
                }
            }
        }
    }
}
// 兼容业财样式
body[erp] {
    .content {
        .main-content {
            .erp-main-top {
                .main-tool-right {
                    .button {
                        .adduserImg {
                            margin-left: 6px;
                        }
                    }
                }
            }
            .main-center {
                .table {
                    height: auto;
                }
            }
        }
    }
}
</style>
