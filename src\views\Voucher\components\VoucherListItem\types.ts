import type { IVoucherModel } from "@/views/Voucher/VoucherList/types";

export interface IVoucherListItem extends IVoucherModel {
    checked?: boolean;
    rid?: number;
}
export enum VoucherSource {
    Input = 1010, // 用户录入
    Import = 1020, // 用户导入
    Api = 1030, // 接口插入
    Mobile = 1040, // 手机端录入
    AutoCreate = 1050, // 自动生成
    Cashier = 1051, // 资金
    FixedAssets = 1052, // 固定资产
    Invoice = 1053, // 发票
    Salary = 1054, // 工资
    Scm = 1055, // 进销存
    Draft = 1056, // 票据
    CarryOver = 1057, // 期末结转
    ExpenseBill = 1058, // 费用单据
    Purchase = 1059, // 采购管理
    Sale = 1060, // 销售管理
    Stock = 1061, // 库存管理
    ReceivableAndPayable = 1062, // 应收应付
}
