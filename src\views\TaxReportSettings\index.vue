<template>
  <div class="content">
    <div class="main-content">
      <div class="main-content-header">
        <div class="title"><span>报税设置</span></div>
      </div>
      <div class="main-content-body">
        <settings-form v-model="basicInfo"></settings-form>
        <a
          class="button solid-button"
          @click="handleSave">
          保存
        </a>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import SettingsForm from "./components/SettingsForm.vue"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { storeToRefs } from "pinia"
  import { request } from "@/utils/service"
  import { ElNotify } from "@/utils/notify"

  defineOptions({
    name: "TaxReportSettings",
  })

  const basicInfoStore = useBasicInfoStore()
  const { basicInfo } = storeToRefs(basicInfoStore)

  function handleSave() {
    request({
      method: "PUT",
      url: "/api/basic/TaxSettings",
      data: basicInfo.value,
    }).then((res) => {
      if (res.state === 1000 && res.data) {
        ElNotify({ message: "保存成功", type: "success" })
      } else {
        ElNotify({ message: "保存失败", type: "error" })
      }
      basicInfoStore.getBasicInfo()
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    .title {
      &::before {
        @include common-before;
      }
      span {
        margin-left: 4px;
        font-size: var(--h4);
        color: var(--dark-grey);
      }
    }

    .main-content-body {
      padding: 28px;
    }
  }
</style>
