<template>
    <div>
        <div class="title">{{ vgname }}</div>
        <div class="check-voucher-center" :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in' }">
            <div class="check-voucher-center-center" style="box-sizing: border-box">
                <Voucher
                    v-model:query-params="voucherQueryParams"
                    @zoom="zoomCallback"
                    @load-success="voucherLoadSuccess"
                    @voucher-changed="voucherChanged"
                    ref="voucher"
                    :showSwitchBtn="showSwitch"
                    :showCancel="true"
                    @back="back"
                    :edited="edited"
                    @save="saveVoucher"
                    @delete-voucher="deleteVoucher"
                    :hiddenLine="true"
                    :switchInfo="switchInfo"
                    @preVoucher="loadVoucherSwitchInfo(1)"
                    @nextVoucher="loadVoucherSwitchInfo(2)"
                ></Voucher>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import {
    EditVoucherQueryParams,
    NewVoucherQueryParams,
    VoucherSwitchInfoModel,
    VoucherSaveParams,
    VoucherSaveModel,
    type VoucherGroupModel,
    VoucherQueryParams,
} from "@/components/Voucher/types";
import Voucher from "@/components/Voucher/index.vue";
import { computed, toRef } from "vue";
import { getUrlSearchParams } from "@/util/url";
import { useFullScreenStore } from "@/store/modules/fullScreen";

const emit = defineEmits(["load-data", "handleBack", "hasChanged"]);

const props = withDefaults(
    defineProps<{
        changeCurrentSlot: Function;
        sortColumn?: string;
        showSwitch?: boolean;
        ps?: number;
        pe?: number;
        sbgId?: number;
        readonly?: boolean;
    }>(),
    {
        showSwitch: false,
        readonly: false,
    }
);

const voucherQueryParams = ref<VoucherQueryParams>();
const switchInfo = ref(new VoucherSwitchInfoModel());
const voucher = ref<InstanceType<typeof Voucher>>();

const voucherGroupList = ref(new Array<VoucherGroupModel>());
const vgname = computed(() => {
    let vg = voucherGroupList.value.find((item) => item.id === voucher.value?.getVoucherModel()?.vgId);
    if (vg) {
        return vg.title;
    } else {
        return "查看凭证";
    }
});
const zoomState = ref<"in" | "out">("in");
const zoomCallback = ref((_zoomState: "in" | "out"): void => {
    zoomState.value = _zoomState;
});

let inited = false;
function voucherLoadSuccess() {
    if (!inited) {
        inited = true;
    }
    props.showSwitch && loadVoucherSwitchInfo(0);
    edited.value = false;
}
const edited = ref(false);
// 改变次数，第一次进入会触发一次
let changedTimes = 0;
function voucherChanged() {
    changedTimes++;
    if (changedTimes > 1) {
        emit("hasChanged");
    }
    if (!inited) return;
    edited.value = true;
}

let loading = false;
function isValidNumber(value:any) {
    return typeof value === 'number' && !isNaN(value);
}
function loadVoucherSwitchInfo(operation: 0 | 1 | 2) {
    if (!props.showSwitch) return;
    if ((operation === 1 && switchInfo.value.hasPrev === false) || (operation === 2 && switchInfo.value.hasNext === false)) {
        return;
    }
    voucher.value.warningRowIndex = -1;
    if (loading) {
        ElNotify({ message: "正在切换，请稍后点击~", type: "warning" });
        return;
    }
    loading = true;
    let voucherModel = voucher.value?.getVoucherModel();
    const params = {
        isPrev: operation === 1,
        isNext: operation === 2,
        pid: voucherModel?.pid ?? "",
        periodS: props.ps,
        periodE: props.pe,
        vid: voucherModel?.vid ?? "",
        isFirst: true,
        id: "",
        sbjInfo: isValidNumber(props.sbgId) ? props.sbgId : "",
        sortColumn: props.sortColumn ?? "",
    };
    request({
        url: "/api/SubsidiaryLedger/SwitchInfo?" + getUrlSearchParams(params),
        method: "get",
    }).then((res: IResponseModel<VoucherSwitchInfoModel>) => {
        loading = false;
        if (res.state === 1000) {
            if (operation !== 0) {
                voucherQueryParams.value = new EditVoucherQueryParams(res.data.pid, res.data.vid);
            }
            switchInfo.value = res.data;
        }
    });
}

const loadVoucher = (pid: number, vid: number) => {
    inited = false;
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
    props.changeCurrentSlot();
};

let isSaving = false;
const saveVoucher = () => {
    //去除进度条，防止凭证弹出弹窗被遮挡
    if (isSaving) return;
    isSaving = true;
    voucher.value?.saveVoucher(
        new VoucherSaveParams(1010, (res: IResponseModel<VoucherSaveModel>) => {
            isSaving = false;
            if (res.state === 1000) {
                const voucherModel = voucher.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({
                        message: "亲，保存成功啦！",
                        type: "success",
                    });
                }
                emit("load-data");
                edited.value = false;
                emit("handleBack");
                useFullScreenStore().changeFullScreenStage(false);
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
        })
    );
};

const deleteVoucher = () => {
    const pId = voucher.value?.getVoucherModel().pid as number;
    const vId = voucher.value?.getVoucherModel().vid as number;
    ElConfirm("确定要删除吗").then((r: boolean) => {
        if (r) {
            request({
                url: "/api/Voucher?pId=" + pId + "&vId=" + vId,
                method: "delete",
            }).then((res: any) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: "删除失败" });
                    return;
                }
                ElNotify({ type: "success", message: "删除成功" });
                emit("load-data");
                edited.value = false;
                emit("handleBack");
                useFullScreenStore().changeFullScreenStage(false);
            });
        }
    });
};

const back = () => {
    voucher.value?.removeEventListener();
    edited.value = false;
    emit("handleBack");
    useFullScreenStore().changeFullScreenStage(false);
};

request({
    url: "/api/VoucherGroup/List",
    method: "get",
}).then((res: IResponseModel<Array<VoucherGroupModel>>) => {
    if (res.state === 1000) {
        voucherGroupList.value = res.data;
    }
});

const loadAAE = () => {
    voucher.value?.loadAAE();
};
function getEditedState() {
    return edited.value;
}

defineExpose({ loadVoucher, loadAAE ,getEditedState });
</script>

<style lang="less" scoped>
.check-voucher-center {
    margin: 0 auto;
    // overflow: hidden;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // height: calc(var(--voucher-min-height) + 50px);
    // height: ~"max(calc(var(--voucher-min-height) + 50px), calc(100vh - var(--content-padding-bottom) - var(--title-height)))";
    .check-voucher-center-top {
        padding: 10px 20px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        align-self: center;
        flex-shrink: 0;
        .right {
            display: flex;
            align-items: center;
        }
    }

    .check-voucher-center-center {
        // height: 0;
        // flex: 1;
    }

    &.zoom-in {
        .check-voucher-center-top {
            width: 1050px;
        }
    }

    &.zoom-out {
        .check-voucher-center-top {
            align-self: stretch;
            padding: 10px 54px;
        }
    }
}
.prev-btn,
.next-btn {
    height: 17px;
    cursor: pointer;
    display: flex;
    align-items: center;
    .btn-text {
        padding: 0 10px;
        line-height: 16px;
    }

    .btn-icon {
        width: 17px;
        height: 17px;
        background-image: url(/src/assets/Voucher/prev.png);
        background-repeat: no-repeat;
        background-size: 100%;
    }
}

.prev-btn {
    margin-left: 24px;
    .btn-icon {
        background-image: url("@/assets/voucher/prev.png");
    }

    &:hover {
        color: var(--main-color);
        .btn-icon {
            background-image: url("@/assets/voucher/prev-hover.png");
        }
    }

    &.disabled {
        color: #999;
        .btn-icon {
            background-image: url("@/assets/voucher/prev-disabled.png");
        }
    }
}

.next-btn {
    margin-left: 20px;
    .btn-icon {
        background-image: url("@/assets/voucher/next.png");
    }
    .btn-text {
        padding-right: 0;
    }

    &:hover {
        color: var(--main-color);
        .btn-icon {
            background-image: url("@/assets/voucher/next-hover.png");
        }
    }

    &.disabled {
        color: #999;
        .btn-icon {
            background-image: url("@/assets/voucher/next-disabled.png");
        }
    }
}
body[erp] {
    .check-voucher-center {
        min-width: 1240px;
        .check-voucher-center-top {
            padding: 10px 54px 17px;
        }
    }

    .next-btn {
        &:hover {
            .btn-icon {
                background-image: url("@/assets/voucher/next-hover-erp.png");
            }
        }
    }

    .prev-btn {
        &:hover {
            .btn-icon {
                background-image: url("@/assets/voucher/prev-hover-erp.png");
            }
        }
    }
}
</style>
