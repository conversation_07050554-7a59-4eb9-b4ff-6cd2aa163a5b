<template>
    <el-option
        ref="optionRef"
        :label="label"
        :value="value"
        class="show-more-item"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
    >
    </el-option>
</template>

<script setup lang="ts">
import { ElOption } from "element-plus";

import { ref } from "vue";
defineProps<{
    label: string;
    value: any;
}>();

const emit = defineEmits<{
    (event: "mouse-enter", style: DOMRect): void;
    (event: "mouse-leave"): void;
}>();

const optionRef = ref<InstanceType<typeof ElOption>>();

function handleMouseEnter(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target) return;
    const style = target.getBoundingClientRect();
    emit("mouse-enter", style);
}
function handleMouseLeave() {
    emit("mouse-leave");
}
</script>
