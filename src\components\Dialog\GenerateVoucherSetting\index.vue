<template>
    <el-dialog 
        :title="title" 
        center 
        v-model="voucherSettingShow" 
        :width="width" 
        @close="handleClose" 
        modal-class="modal-class"
        class="dialogDrag"
    >
        <div class="settings-main" v-dialogDrag>
            <div class="settings-content" :class="voucherSetting.voucherSummary !== 1 ? 'lower' : ''">
                <div class="settings-row">
                    <div class="settings-label">凭证生成日期</div>
                    <div class="settings-input">
                        <el-radio-group v-model="voucherSetting.voucherDate">
                            <el-radio :label="0">{{dateText}}</el-radio>
                            <el-radio :label="1">会计日期</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="settings-row">
                    <div class="settings-label">凭证摘要设置</div>
                    <div class="settings-input">
                        <el-radio-group v-model="voucherSetting.voucherSummary">
                            <el-radio :label="0">系统摘要</el-radio>
                            <el-radio :label="1">自定义摘要</el-radio>
                            <el-radio :label="2" v-if="isScmSetting">单据备注</el-radio>
                        </el-radio-group>
                    </div>
                    <div class="settings-check" v-show="voucherSetting.voucherSummary === 1">
                        <div style="width: 100%;">
                            <el-checkbox v-model="summaryCheckOptions[0].checked" label="固定文本" @change="changeSel($event, summaryCheckOptions[0])">
                            <span class="el-checkbox__label">固定文本</span>
                                <el-input @input="lengthCheck" style="width:80px;" v-model="voucherSetting.fixedText"></el-input>
                            </el-checkbox>                        
                        </div>
                        <el-checkbox :key="index" v-for="(item, index) in summaryCheckOptions.slice(1, summaryCheckOptions.length)"
                            v-model="item.checked" :label="item.label" @change="changeSel($event, item)"></el-checkbox>

                    </div>
                    <div class="mt-20" v-show="voucherSetting.voucherSummary === 1">
                        <div class="settings-notice" >
                            <img src="@/assets/Cashier/ietype_edit_note.png" alt="" class="warnIcon">
                            <span>勾选字段或固定文本，系统将按照您勾选的顺序组成摘要信息</span>
                        </div>
                        <div class="settings-notice">
                            <span v-if="summarySelOptions.length === 0">示例：勾选字段及顺序为摘要、备注、固定文本，摘要为：摘要内容_备注内容_固定文本内容</span>
                            <span v-else>
                                凭证摘要生成预览：<span v-for="(item, index) in summarySelOptions" :key="item.value">
                                    {{ item.label !== '固定文本' ? item.label : voucherSetting.fixedText }}{{ index === summarySelOptions.length-1 ? "" : "_" }}
                                </span>
                            </span>
                        </div>
                    </div>
                    <!-- <div class="settings-notice" v-show="voucherSetting.voucherSummary === 1">
                        例子：选择固定文本+摘要+备注，摘要为：{{voucherSetting.fixedText}}_摘要内容_备注内容
                    </div> -->
                </div>
            </div>
            <div class="settings-button">
                <a class="button solid-button" @click="saveSettings">保存</a>
                <a class="button ml-10" @click="cancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { type IVoucherSetting, type ISummaryCheckOption ,settingTypes } from './type'
import { getSettingFixedTexts, getSettingSummaryChecks, getBillSettingsTypeText } from './util'
import { ElNotify } from '@/util/notify';
import { getScmRelationInfo, type ScmRelationInfo } from '@/api/scm';
import { request, type IResponseModel } from "@/util/service";
import { getGlobalLodash } from "@/util/lodash";
import { useRoute } from "vue-router";
const _ = getGlobalLodash()
const route = useRoute();
const isScmSetting = ref(false);
if(route.fullPath.indexOf('Scm') > -1){
    isScmSetting.value = true;
}
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    settingType: {
        type: String as () => typeof settingTypes[number],
        default: settingTypes[0],
    },
    defaultSummary: {
        type: Number,
        default: 0
    }
})
const emit = defineEmits<{
    (e: "update:modelValue", val: boolean): void;
}>();
const voucherSettingShow = computed({
    get: () => props.modelValue,
    set: (val: boolean) => emit('update:modelValue', val)
});
const title = computed({
    get: () => {
        let result = "生成凭证设置";
        if(isScmSetting.value){
            result = getBillSettingsTypeText(props.settingType)+ result;
        } 
        return result;
    },
    set: (val: string) => val
});
const summaryCheckOptions = ref<Array<ISummaryCheckOption>>([]);
const summarySelOptions = ref<Array<ISummaryCheckOption>>([]);
const summaryType = ref(-1);
const voucherSetting = ref<IVoucherSetting>(loadSettings());

const changeSel = (val: boolean, item:ISummaryCheckOption) => {
    if (val) {
        summarySelOptions.value.push(item);
    } else {
        let index = summarySelOptions.value.findIndex((v) => v.label === item.label);
        summarySelOptions.value.splice(index, 1);
    }
}

const dateText = ref('银行流水日期');
const width = ref('640px');
initSettings();

function cancel() {
    voucherSettingShow.value = false;
}

function saveSettings() {
    voucherSetting.value.combineSummary = [];
    voucherSetting.value.fixedText = voucherSetting.value.fixedText.trim();
    if (summaryCheckOptions.value[0].checked) {
        if (voucherSetting.value.fixedText == '' || voucherSetting.value.fixedText == null) {
            ElNotify({
                type: 'warning',
                message: '自定义摘要固定文本为空，请输入后重新保存哦~'
            });
            return;
        }
        if(voucherSetting.value.fixedText.length > 30){
            ElNotify({
                type: 'warning',
                message: '自定义摘要固定文本长度不能超过30个字符哦~'
            });
            return;
        }
    } else {
        if(voucherSetting.value.fixedText.length > 30){
            ElNotify({
                type: 'warning',
                message: '自定义摘要固定文本长度不能超过30个字符哦~'
            });
            return;
        }
    }
    // for遍历summarySelOptions，将选中的值value存于combineSummary
    summarySelOptions.value.forEach((item) => {
        if (item.checked) {
            voucherSetting.value.combineSummary.push(item.value);
        }
    });

    if (voucherSetting.value.voucherSummary === 1 && voucherSetting.value.combineSummary.length == 0) {
        ElNotify({
            type: 'warning',
            message: '自定义摘要至少要勾选一个条件哦~'
        });
        return;
    }
    window.localStorage.setItem(props.settingType, JSON.stringify(voucherSetting.value));
    ElNotify({
        type: 'success',
        message: '保存成功~'
    });
    voucherSettingShow.value = false;
}

const lengthCheckFlag = ref(false);
function lengthCheck(){
    if(voucherSetting.value.fixedText.length > 30){
        voucherSetting.value.fixedText = voucherSetting.value.fixedText.substring(0, 30);
        if(!lengthCheckFlag.value){
            ElNotify({
                type: 'warning',
                message: '自定义摘要固定文本长度不能超过30个字符哦~'
            });
        }
        lengthCheckFlag.value = true;
    } else{
        lengthCheckFlag.value = false;    
    }
}

function loadSettings() {
    summarySelOptions.value = [];
    if (window.localStorage.getItem(props.settingType)) {
        let localSettings = JSON.parse(window.localStorage.getItem(props.settingType) as string) as IVoucherSetting;
        //浏览器缓存数据combineSummary字段为数字，将其变成数字list
        if (typeof localSettings.combineSummary === "number") {
            let combineSummary:number = localSettings.combineSummary;
            localSettings.combineSummary = [];
            // 过滤出小于等于目标值的项并
            const validValues = summaryCheckOptions.value.filter(item => item.value <= combineSummary).map(item => item.value);
            for (const value of validValues.reverse()) { // 反向遍历以保持顺序
                if (combineSummary >= value) {
                    localSettings.combineSummary.unshift(value);
                    combineSummary -= value;
                }
            }
        }

        localSettings.combineSummary.forEach((v) => {
            summaryCheckOptions.value.forEach((item) => {
                if (v == item.value) {
                    item.checked = true;
                    summarySelOptions.value.push(item);
                }
            })
        });
        return localSettings;
    }

    let result = {
        voucherDate: 0,
        voucherSummary: props.defaultSummary,
        fixedText: getSettingFixedTexts(props.settingType),
    } as IVoucherSetting;
    if(props.defaultSummary === 1){
        let combineSummary:number[] = [];
        summaryCheckOptions.value.forEach((item) => {
            if (item.checked) {
                combineSummary.push(item.value);
                summarySelOptions.value.push(item);
            }
        });
        result.combineSummary = combineSummary;
    }
    if(isScmSetting.value){
        // 避免多次请求
        if(summaryType.value !== -1){
            result.voucherSummary = summaryType.value;
            return result;
        }
        getScmRelationInfo().then((relation: IResponseModel<ScmRelationInfo>) => {
            if (relation.state === 1000) {
                return request({
                    url: `/api/ScmSettings?scmProductType=${relation.data.scmProductType}&scmAsid=${relation.data.scmAsid}`,
                }).then((res: any) => {
                    if (res.state === 1000) {
                        if(res.data.settings.DescMatchRule === 1020){
                            summaryType.value = 2;
                        } else {
                            summaryType.value = 0;
                        }
                    } else {
                        ElNotify({
                            type: "error",
                            message: "出现错误，请稍候刷新页面重试",
                        });
                    }

                    result.voucherSummary = summaryType.value;
                    return result;
                });            
            }
        });        
    }

    return result;
}

function initSettings() {
    summaryCheckOptions.value = _.cloneDeep(getSettingSummaryChecks(props.settingType));
    switch (props.settingType) {
        case 'cashJournal':
        case 'depositJournal':
        case 'transferJournal':
            dateText.value = '日记账日期';
            voucherSetting.value.voucherDate = 0;         
            break;
        case 'salesInvoice':
        case 'purchaseInvoice':
            dateText.value = '开票日期';
            voucherSetting.value.voucherDate = 1;
            width.value = '680px';            
            break;
        default:
            dateText.value = '单据日期';
    }
}

const getVoucherSetting = ()=>{
    return loadSettings();
}

watch(()=> props.settingType, (val) => {
    if (val) {
        initSettings();
        voucherSetting.value = loadSettings();
    }
});

watch(()=> voucherSettingShow.value, (val) => {
    if (val) {
        voucherSetting.value = loadSettings();
    }
});

function handleClose(){
    voucherSettingShow.value = false;
    voucherSetting.value = loadSettings();
}

defineExpose({
    getVoucherSetting
})
</script>

<style lang="less" scoped>
.settings-content {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
    min-height: 210px;
    border-bottom: 1px solid var(--border-color);

    .settings-row {
        padding: 10px 40px 0px;
        // width: 100%;
        font-weight: 400;

        .settings-label {
            display: inline-flex;
        }

        .settings-input {
            margin-left: 40px;
            display: inline-flex;

            .el-radio-group {
                width: 250px;

                .el-radio {
                    width: 100px;
                }
            }

            :deep(.el-radio-group){
                width: 400px;
            }
        }

        .settings-check {
            margin-top: 10px;

            :deep(.el-input__wrapper) {
                box-shadow: 0 0 0 0px;

                .el-input__inner {
                    width: 240px;
                    border-bottom: 1px solid var(--border-color);
                    margin-left: 10px;
                }
            }
        }

        .settings-notice {
            margin-top: 10px;
            font-size: 12px;
            color: var(--text-color-light);
            position: relative;
        }
    }

    &.lower {
        min-height: 120px;
    }
    .warnIcon {
        width: 14px;
        height: 14px;
        position: absolute;
        left: -18px;
    }
}

.settings-button {
    padding: 20px;
    text-align: center;
}
</style>