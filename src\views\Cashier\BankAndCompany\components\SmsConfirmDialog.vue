<template>
    <el-dialog center width="540px" title="提示" @close="handleClose" class="dialogDrag">
        <div class="sms-confirm-content" v-dialogDrag>
            <div class="acctInfo">{{ props.accountInfo }}</div>
            <div class="messageInfo">{{ props.message }}</div>
            <div class="formInfo">
                <el-form>
                    <el-form-item label="管理员手机号" label-width="120px">
                        <Select v-model="phone" placeholder="请选择手机号" v-if="props.phoneList.length > 1" :teleported="true">
                            <el-option v-for="item in props.phoneList" :key="item" :label="item" :value="item"> </el-option>
                        </Select>
                        <el-input placeholder="请输入手机号" v-model="phone" :disabled="true" v-else></el-input>
                    </el-form-item>
                    <el-form-item label="验证码" label-width="120px">
                        <el-input placeholder="请输入验证码" v-model="smsCode"></el-input>
                        <a class="solid-button button mr-10 pl10 pr10" @click="sendSMSForBankConfirm">{{ sendText }}</a>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="buttons">
            <a :class="{ 'solid-button': true, disabled: !canAuthorize }" @click="authorize">立即授权</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { request } from "@/util/service";
import { ref, watchEffect, onUpdated } from "vue";
import { ElNotify } from "@/util/notify";
import Select from "@/components/Select/index.vue";
const props = defineProps<{
    phoneList: Array<string>;
    message: string;
    accountInfo: string;
    successHandle: Function | undefined;
}>();
const emit = defineEmits<{
    (e: "confirm-success"): void;
}>();

const phone = ref("");

const secondsToGo = ref(0);
const hasSend = ref(false);
const canAuthorize = ref(false);
const sendText = ref("发送验证码");
const smsCode = ref("");

onUpdated(() => {
    phone.value = props.phoneList[0] ?? "";
});

watchEffect(() => {
    if (!hasSend.value) {
        sendText.value = "发送验证码";
        return;
    }
    if (secondsToGo.value === 0) {
        sendText.value = "重新发送";
        return;
    }
    sendText.value = `${secondsToGo.value}秒后重新发送`;
    return;
});

function sendSMSForBankConfirm() {
    if (secondsToGo.value !== 0) {
        return;
    }

    request({
        url:
            window.accountSrvHost +
            `/Default/Services/SendSMSForBankConfirm.ashx?CurrentSystemType=1&stype=15&BankNameAndNo=${props.accountInfo}&Phone=${phone.value}`,
        method: "get",
    }).then((res: any) => {
        if (res === "Success") {
            canAuthorize.value = true;
            hasSend.value = true;
            secondsToGo.value = 60;
            countDown();
        } else {
            ElNotify({ type: "warning", message: "发送短信失败，请稍后重试..." });
        }
    });
}

function countDown() {
    if (secondsToGo.value === 0) {
        return;
    }
    secondsToGo.value--;
    setTimeout(countDown, 1000);
}

function authorize() {
    if (!canAuthorize.value) {
        return;
    }
    if (smsCode.value === "") {
        ElNotify({ type: "warning", message: "请输入验证码" });
        return;
    }

    request({
        url: `/api/ConfirmCode/CheckCodeTime?confirmCode=${smsCode.value}&mobile=${phone.value}`,
        method: "post",
    }).then((res) => {
        if (res.data === "Success") {
            emit("confirm-success");
            if (props.successHandle) {
                props.successHandle();
            }
        } else if (res.data === "Expired") {
            ElNotify({ type: "warning", message: "验证码已过期" });
        } else {
            ElNotify({ type: "warning", message: "验证码错误" });
        }
    });
}

function handleClose() {
    smsCode.value = "";
}
</script>
<style lang="less" scoped>
.sms-confirm-content {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    padding: 0 50px;

    .acctInfo {
        font-size: 16px;
        font-weight: 400;
        margin-top: 20px;
    }

    .messageInfo {
        margin-top: 20px;
        margin-bottom: 20px;
        font-weight: 400;
    }

    .formInfo {
        .el-input {
            width: 160px;
        }

        .el-select {
            width: 160px;
        }

        .button {
            display: inline-flex;
            margin-left: 20px;
            align-items: center;
            justify-content: center;
            line-height: 30px;
            width: 100px;
        }
    }
}

.buttons {
    height: 54px;
}

.disabled {
    cursor: not-allowed;
}
</style>
