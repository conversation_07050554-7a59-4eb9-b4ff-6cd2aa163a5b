<template>
    <el-dialog 
        :title="title" 
        center 
        v-model="display" 
        :width="width" 
        class="dialogDrag"
        @closed="handleClosed"
    >
        <div class="dialog-tip-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="box-main">
                <div :class="['tip-title', {'large': isLargeTipTitle, 'left': tipTitleLeft}]" v-html="tipTitle"></div>
                <div class="tip" v-if="tipInfo || hasTipSlot">
                    <img v-if="tipIcon" src="@/assets/Settings/tips.png" alt="" />
                    <span :class="['tip-info', {'left': tipInfoLeft}]">{{ tipInfo }}</span>
                    <slot name="tipSlot"></slot>
                </div>
                <slot></slot>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleConfirm">{{ confirmText }}</a>
                <a class="button ml-10" @click="handleCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, useSlots, computed } from "vue";

const props = defineProps({
    width: {
        type: Number,
        default: 440,
    },
    title: { //弹窗标题
        type: String,
        default: "提示",
    },
    tipTitle: { //主要提示
        type: String,
        default: "",
    },
    tipInfo: { //次要提示
        type: String,
        default: "",
    },
    tipIcon: { //是否有提示图标
        type: Boolean,
        default: true,
    },
    isLargeTipTitle: { 
        type: Boolean,
        default: false,
    },
    tipInfoLeft: {
        type: Boolean,
        default: false,
    },
    tipTitleLeft: {
        type: Boolean,
        default: false,
    },
    confirmText: { //确定按钮文字
        type: String,
        default: "确定",
    },
});
const emit = defineEmits<{
    (event: "closed"): void;
    (event: "confirm"): void;
    (event: "cancel"): void;
}>();

const slots = useSlots()
const hasTipSlot = computed(() => !!slots.tipSlot)
const isErp = ref(window.isErp);

const display = ref(false);
function showDialog() {
    display.value = true;
}
function handleClosed() {
    emit("closed");
}
function handleCancel() {
    handleCloseDialog();
    emit("cancel");
}
function handleCloseDialog() {
    display.value = false;
}
function handleConfirm() {
    handleCloseDialog();
    emit("confirm");
}
defineExpose({ showDialog });
</script>

<style lang="less" scoped>
.dialog-tip-content {
    .box-main {
        border-bottom: 1px solid var(--border-color);
        padding: 30px 40px;
        text-align: center;
        min-height: 42px;
        .tip-title {
            font-size: 14px; 
            color: #333;
            &.large {
                font-size: 16px;
            }
            &.left {
                text-align: left;
            }
        }
        .tip {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }
        .tip-info {
            font-size: 14px;
            color: #666;
            &.left {
                text-align: left;
            }
        }
        img {
            display: block;
            width: 14px; 
            height: 14px; 
            margin-right: 2px;
            position: relative;
            top: 3px;
        } 
    }
    &.erp {
        .box-main {
            border: none;
            padding-bottom: 0;
        }
        .buttons {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            padding: 20px 10px;
            border-top: 1px solid var(--border-color);
            margin-top: 20px;
            .button {
                margin: 0 10px;
            }
        }
    }
}
</style>
