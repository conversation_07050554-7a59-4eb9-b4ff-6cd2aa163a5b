<script lang="ts" setup>
import { getPeriodsApi, PeriodStatus, type IPeriod } from "@/api/period";
import { computed, reactive, ref, onMounted, onUnmounted} from "vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
const periodStore = useAccountPeriodStore();
const props = defineProps({
    modelValue: { type: Number, required: true },
    tableShow: { type: Boolean, default: false },
    parentPeriodInfo: { type: String, default: "" },
    isQuarter: { type: Boolean, default: false },
});

const emit = defineEmits(["update:modelValue", "onchangeMouse", "getQuarterPeriodsList", "selectedPidChange"]);
const selectedPid = computed({
    get() {
        return props.modelValue;
    },
    set(value: number) {
        emit("update:modelValue", value);
    },
});

const minPid = ref(0);
const maxPid = ref(0);
const periodSelectInfo = reactive({
    year: 0,
    month: 0,
});
const periodYearSn = computed(() => {
    return periodSelectInfo.year + "-" + periodSelectInfo.month;
});
const periodStatus = ref<PeriodStatus>(0);
const hiddenDropDown = ref(false);
const periodCurrentYear = ref(0);

function handleMouseEnter() {
    periodCurrentYear.value = periodSelectInfo.year;
    hiddenDropDown.value = false;
    emit("onchangeMouse", hiddenDropDown.value);
}

const periodInfo = computed(() => {
    if (periodSelectInfo.year === 0 || periodSelectInfo.month === 0) {
        return "--未选择--";
    } else {
        if (props.isQuarter) {
            return (
                periodSelectInfo.year +
                "年第" +
                Math.floor(periodSelectInfo.month % 3 === 0 ? periodSelectInfo.month / 3 : periodSelectInfo.month / 3 + 1) +
                "季度"
            );
        }
        return periodSelectInfo.year + "年" + periodSelectInfo.month + "月";
    }
});

function handleYearSelect(year: number) {
    periodCurrentYear.value = year;
}

function handleSelect(pid: number) {
    // 先确定期间没有问题，再向上更新
    if (setPeriodInfo(pid)) {
        selectedPid.value = pid;
        //每次更新期间后隐藏下拉框，需要重新进入才展开
        hiddenDropDown.value = true;
        periodStore.changePeriods(String(pid));
        selectedPid.value = Number(periodStore.getCurrentPeriod());
    }
}

function setPeriodInfo(pid: number) {
    for (let i = 0; i < periodsGroup.value.length; i++) {
        const periods = periodsGroup.value[i];
        for (let j = 0; j < periods.length; j++) {
            const item = periods[j];
            if (item.pid === pid) {
                periodSelectInfo.year = item.year;
                periodSelectInfo.month = item.sn;
                periodStatus.value = item.status;
                if (!localStorage.getItem("cashclassificationSwitch")) {
                    localStorage.setItem("cashclassificationSwitch", (periodSelectInfo.year as number) >= 2018 ? "true" : "false");
                }
                return true;
            }
        }
    }
    return false;
}
let quarterPeriodsListSort = ref<IPeriod[]>([]);
function getQuarterPeriodsList() {
    let quarterPeriodsList: IPeriod[] = [];

    periodsGroup.value.forEach((v) => {
        quarterPeriodsList = quarterPeriodsList.concat(v);
    });
    quarterPeriodsListSort.value = quarterPeriodsList.sort((a, b) => a.pid - b.pid);
    selectedPid.value = quarterPeriodsListSort.value.find((v) => v.pid >= Number(periodStore.getCurrentPeriod()))?.pid as number;
    emit("getQuarterPeriodsList", quarterPeriodsListSort.value);
    maxPid.value = quarterPeriodsListSort.value[quarterPeriodsListSort.value.length - 1].pid;
    minPid.value = quarterPeriodsListSort.value[0].pid;
}

function handleLeft() {
    if (selectedPid.value > minPid.value) {
        if (props.isQuarter) {
            let selectedPidIndex = quarterPeriodsListSort.value.findIndex((v: IPeriod) => v.pid === selectedPid.value);
            let leftPid = (selectedPidIndex > -1 && quarterPeriodsListSort.value[selectedPidIndex - 1].pid) || selectedPid.value;
            handleSelect(leftPid);
        } else {
            handleSelect(selectedPid.value - 1);
        }
    }
    emit("selectedPidChange");
}

function handleRight() {
    if (selectedPid.value < maxPid.value) {
        if (props.isQuarter) {
            let selectedPidIndex = quarterPeriodsListSort.value.findIndex((v: IPeriod) => v.pid === selectedPid.value);
            let rightPid = (selectedPidIndex > -1 && quarterPeriodsListSort.value[selectedPidIndex + 1].pid) || selectedPid.value;
            handleSelect(rightPid);
        } else {
            handleSelect(selectedPid.value + 1);
        }
    }
    emit("selectedPidChange");
}
const periodsGroup = ref(new Array<Array<IPeriod>>());
function getPeriodsList() {
    getPeriodsApi()
    .then((res: any) => {
        periodsGroup.value = [];
        let data = res.data;
        let year = 0;
        // 按年份倒序排序，每一年内部为正序排序
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (i === 0) {
                minPid.value = item.pid;
            }
            if (item.year !== year) {
                year = item.year;
                periodsGroup.value.unshift(new Array<IPeriod>());
            }
            periodsGroup.value[0].push(item);
            if (i === data.length - 1) {
                maxPid.value = item.pid;
                if (selectedPid.value === 0) {
                    handleSelect(item.pid);
                } else {
                    setPeriodInfo(selectedPid.value);
                }
            }
        }

        // 季度显示第几季度
        if (props.isQuarter) {
            let quarterGroup: any = [];
            periodsGroup.value.forEach((item: any) => {
                if (item[item.length - 1].sn % 3 === 0) {
                    let child = item.filter((v: any) => v.sn % 3 === 0);
                    quarterGroup.push(child);
                } else {
                    let child = item.filter((v: any, index: number) => v.sn % 3 === 0 || index === item.length - 1);
                    quarterGroup.push(child);
                }
            });
            periodsGroup.value = quarterGroup;
            getQuarterPeriodsList();
        }

        if (!selectedPid.value) {
            selectedPid.value = periodsGroup.value[0][0].pid;
        }
    })
    .catch((error) => {
        console.log(error);
    });
}
defineExpose({
    periodYearSn,
    periodStatus,
    periodSelectInfo,
});

onMounted(() => {
    getPeriodsList();
    window.addEventListener("peroidListChange", getPeriodsList)
});

onUnmounted(() => {
    window.removeEventListener("peroidListChange", getPeriodsList)
})
</script>

<template>
    <div class="pagination-period-button" @mouseenter="handleMouseEnter">
        <div
            class="pagination-period-icon pagination-period-icon-left"
            :class="selectedPid === minPid ? 'pagination-period-icon-disabled' : ''"
            @click="handleLeft"
        ></div>
        <div class="pagination-period-txt">{{ parentPeriodInfo ? parentPeriodInfo : periodInfo }}</div>
        <div
            class="pagination-period-icon pagination-period-icon-right"
            :class="selectedPid === maxPid ? 'pagination-period-icon-disabled' : ''"
            @click="handleRight"
        ></div>

        <template v-if="props.tableShow">
            <slot></slot>
        </template>
        <template v-else>
            <div class="pagination-period-panel" :style="hiddenDropDown ? 'display:none' : ''">
                <div class="pagination-period-dropdownlist">
                    <div
                        class="pagination-period-dropdownlist-item"
                        :class="periods[0].year === periodCurrentYear ? 'pagination-period-dropdownlist-item-selected' : ''"
                        v-for="periods in periodsGroup"
                        :key="periods[0].year"
                        @click="handleYearSelect(periods[0].year)"
                    >
                        <div class="pagination-period-dropdownlist-year">{{ periods[0].year }}年</div>
                        <div class="pagination-period-dropdownlist-months">
                            <template v-if="isQuarter">
                                <div
                                    class="pagination-period-dropdownlist-month"
                                    :class="period.pid === selectedPid ? 'pagination-period-dropdownlist-month-selected' : ''"
                                    v-for="period in periods"
                                    :key="period.sn"
                                    @click="handleSelect(period.pid)"
                                >
                                    第{{ Math.floor(period.sn % 3 === 0 ? period.sn / 3 : period.sn / 3 + 1) }}季度
                                </div>
                            </template>
                            <template v-else>
                                <div
                                    class="pagination-period-dropdownlist-month"
                                    :class="period.pid === selectedPid ? 'pagination-period-dropdownlist-month-selected' : ''"
                                    v-for="period in periods"
                                    :key="period.sn"
                                    @click="handleSelect(period.pid)"
                                >
                                    {{ period.sn }}月
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<style lang="less" scoped>
@import (reference) "@/style/Common.less";

.pagination-period-button {
    width: 150px;
    height: 28px;
    background-color: var(--main-color);
    position: relative;
    border-radius: 2px;
    display: flex;
    justify-content: space-between;

    .pagination-period-icon {
        width: 26px;
        height: 28px;
        cursor: pointer;
        background-position: center;
        background-repeat: no-repeat;

        &.pagination-period-icon-left {
            background-image: url("@/assets/Icons/left-white.png");

            &.pagination-period-icon-disabled {
                background-image: url("@/assets/Icons/left-green-disabled.png");
            }
        }

        &.pagination-period-icon-right {
            background-image: url("@/assets/Icons/right-white.png");

            &.pagination-period-icon-disabled {
                background-image: url("@/assets/Icons/right-green-disabled.png");
            }
        }
    }

    .pagination-period-txt {
        .set-font(var(--white), var(--font-size), 28px);
        cursor: pointer;
    }

    &:hover {
        .pagination-period-panel {
            display: block;
        }
    }

    .pagination-period-panel {
        position: absolute;
        top: 28px;
        .set-border;
        width: 148px;
        height: 360px;
        background-color: #f0f0f0;
        z-index: 10;
        display: none;

        .pagination-period-dropdownlist {
            width: 65px;
            height: 100%;
            overflow: hidden;
            overflow-y: auto;

            .pagination-period-dropdownlist-item {
                height: 30px;

                .pagination-period-dropdownlist-year {
                    text-align: center;
                    .set-border;
                    border-left: none;
                    border-top: none;
                    .set-font(var(--font-color), var(--h5), 29px);
                    .set-transition;
                    cursor: pointer;
                    position: relative;
                    z-index: 11;

                    &:hover {
                        background-color: var(--table-hover-color);
                    }
                }

                .pagination-period-dropdownlist-months {
                    visibility: hidden;
                    opacity: 0;
                    position: absolute;
                    top: 0px;
                    right: -1px;
                    bottom: -1px;
                    left: 64px;
                    .set-font;
                    .set-transition;
                    .set-border;
                    border-top: none;
                    cursor: initial;
                    background-color: var(--white);

                    .pagination-period-dropdownlist-month {
                        text-align: center;
                        .set-font(var(--font-color), var(--font-size), 30px);
                        .set-transition;
                        cursor: pointer;

                        &:hover {
                            background-color: var(--table-hover-color);
                        }

                        &.pagination-period-dropdownlist-month-selected {
                            color: var(--white);
                            background-color: var(--main-color);
                        }
                    }
                }

                &.pagination-period-dropdownlist-item-selected {
                    .pagination-period-dropdownlist-year {
                        color: var(--main-color);
                        border-right-color: var(--white);
                        background-color: var(--white);
                    }

                    .pagination-period-dropdownlist-months {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }

            .pagination-period-dropdownlist-line {
                position: absolute;
                top: 0;
                left: 64px;
                bottom: 0;
                width: 1px;
                background-color: var(--border-color);
            }
        }
    }
}

.pagination-table {
    position: absolute;
    top: 28px;
    width: 446px;
    .set-border;
    background-color: white;
    z-index: 11;
    display: none;
}
</style>
