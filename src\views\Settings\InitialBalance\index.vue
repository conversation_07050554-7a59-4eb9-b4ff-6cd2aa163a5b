<template>
    <div class="content">
        <div class="title">期初设置</div>
        <div class="main-content">
            <el-tabs v-model="tabName" @tab-click="handleTabClick">
                <el-tab-pane v-for="(item, index) in tabs" :label="item.name" :name="item.alias" :key="index"> </el-tab-pane>
            </el-tabs>
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <SearchInfo
                        :width="280"
                        :height="30"
                        :placeholder="'输入编码/名称/金额等关键字搜索'"
                        @search="handleSearchInfo"
                    ></SearchInfo>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <div class="fc-content" v-if="isShowFC">
                        <span>币别：</span>
                        <div class="jqtransform mr-20 jqtransformdone">
                            <el-select 
                                ref="fcSelect" 
                                style="width: 100px" 
                                v-model="currentFcValue" 
                                :filterable="true" 
                                placeholder=" "
                                :filter-method="taxTypeFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showfcOptions" 
                                    :key="item.id" 
                                    :label="item.name" 
                                    :value="item.id"
                                ></el-option>
                            </el-select>
                        </div>
                        <span id="fc">汇率：{{ currentRate }}</span>
                    </div>
                    <a
                        class="help mr-20"
                        v-show="!isErp && !isHideBarcode"
                        @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/commonPro?subMenuId=*********&answerId=395')"
                    >
                        <img src="@/assets/Settings/help.png" />
                        <span @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/commonPro?subMenuId=*********&answerId=395')"
                            >帮助</span
                        >
                    </a>
                    <el-checkbox v-if="isShowQut" v-model="showQuantity" label="隐藏数量"></el-checkbox>
                    <a class="button ml-10" @click="exportExcel" v-permission="['initialbalance1-canexport']">导出</a>
                    <a class="button ml-10" @click="importHandle" v-permission="['initialbalance1-canimport']">导入</a>
                    <a
                        id="btnCalcBalance"
                        class="button solid-button ml-10"
                        @click="calcBalanceHandle"
                        v-show="!isShowFC || (isShowFC && currentFcValue === 0)"
                        >试算平衡</a
                    >
                    <a
                        class="button ml-10"
                        @click="calcInitialReconciliationHandle"
                        v-show="isErp && (!isShowFC || (isShowFC && currentFcValue === 0))"
                        >期初对账</a
                    >
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <TableInitialBalance
                    ref="tableInitialBalanceRef"
                    :isFCView="isFCView"
                    :isShowFC="isShowFC"
                    :isShowQut="isShowQut"
                    :inputShowFc="inputShowFc"
                    :isJan="isJan"
                    :isShowQuantity="showQuantity"
                    :currentFcValue="currentFcValue"
                    :currentRate="currentRate"
                    :tabName="tabName"
                    :isEditable="isEditable"
                    :fcid="fcid"
                    :data="tableData"
                    :loading="loading"
                    :tabsType="tabsType"
                    @getTableData="getConditionData"
                ></TableInitialBalance>
            </div>
        </div>
    </div>
    <ImportSingleFileDialog
        v-model:import-show="importDialogVisible"
        v-model:importErrorShow="importErrorShow"
        v-model:importErrorMessage="importErrorMessage"
        :importTitle="'导入期初'"
        :importUrl="'/api/InitialBalance/Import'"
        :uploadSuccess="uploadSuccess"
        :need-loading="true"
    >
        <template #top-tips>
            <div style="margin-left: 40px; margin-top: 20px" v-show="!isErp && !isHideBarcode">
                <a @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=130270110')" class="link"
                    >不会操作？点此观看视频
                </a>
            </div>
        </template>
        <template #download>
            <div>1.请选择下面任意一种方式导入期初</div>
            <div class="mt-10 ml-10">(1)在科目期初列表导出所需数据，确认后直接导入</div>
            <div class="mt-10 ml-10">
                (2)点击下载模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="getExcelTemplate">下载模板</a>
            </div>
        </template>
        <template #import-content>
            <span>2.选择文件导入</span>
        </template>
    </ImportSingleFileDialog>
    <el-dialog v-model="calcBalanceDialogVisible" :class="[isErp ? 'custom-confirm' : '', 'dialogDrag']" title="期初试算结果" center width="1048">
        <div
            id="checkBanlace"
            title=""
            style="padding: 5px; width: 1038px; height: 335px"
            class="panel-body panel-body-noborder window-body"
            v-dialogDrag
        >
            <div class="pop">
                <div class="pop-heading">
                    <span>期初余额</span>
                </div>
                <div class="pop-body" :class="isErp ? 'erp-pop-body' : ''">
                    <ul>
                        <div class="bg ibg" id="ibg"></div>
                        <li
                            class="ibd"
                            :style="{
                                top: Number(calcBalanceInfo.initial) < 0 ? '68px' : Number(calcBalanceInfo.initial) > 0 ? '106px' : '80px',
                            }"
                        >
                            {{ calcBalanceInfo.initalDebit }}
                        </li>
                        <li class="ibt" style="top: 120px">{{ calcBalanceInfo.initial.replace(/-/g, "") }}</li>
                        <li
                            class="ibc"
                            :style="{
                                top: Number(calcBalanceInfo.initial) < 0 ? '85px' : Number(calcBalanceInfo.initial) > 0 ? '35px' : '60px',
                            }"
                        >
                            {{ calcBalanceInfo.initalCredit }}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="pop" style="border-left: 1px solid #e4e4e4">
                <div class="pop-heading">
                    <span>累计发生</span>
                </div>
                <div class="pop-body" :class="isErp ? 'erp-pop-body' : ''">
                    <ul>
                        <div class="bg abg" id="abg"></div>
                        <li
                            class="ahd"
                            :style="{
                                top:
                                    Number(calcBalanceInfo.accumulateTotal) < 0
                                        ? '106px'
                                        : Number(calcBalanceInfo.accumulateTotal) > 0
                                        ? '68px'
                                        : '80px',
                            }"
                        >
                            {{ calcBalanceInfo.debitTotal }}
                        </li>
                        <li class="aht" style="top: 125px">{{ calcBalanceInfo.accumulateTotal.replace(/-/g, "") }}</li>
                        <li
                            class="ahc"
                            :style="{
                                top:
                                    Number(calcBalanceInfo.accumulateTotal) < 0
                                        ? '35px'
                                        : Number(calcBalanceInfo.accumulateTotal) > 0
                                        ? '85px'
                                        : '60px',
                            }"
                        >
                            {{ calcBalanceInfo.creditTotal }}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="pop" style="border-left: 1px solid #e4e4e4">
                <div class="pop-heading">
                    <span>
                        资产负债表期初
                        <span v-if="needClassification" style="font-size: 12px; color: #f00"
                            >(当前{{ classificationSwitch ? "已" : "未" }}开启重分类)</span
                        >
                    </span>
                </div>
                <div class="pop-body" :class="isErp ? 'erp-pop-body' : ''">
                    <ul>
                        <div class="bg2 bag" id="bag"></div>
                        <li
                            class="bad"
                            :style="{
                                top:
                                    Number(calcBalanceInfo.balancesheet) < 0
                                        ? '68px'
                                        : Number(calcBalanceInfo.balancesheet) > 0
                                        ? '106px'
                                        : '80px',
                            }"
                        >
                            {{ calcBalanceInfo.totalDebit }}
                        </li>
                        <li class="bat" style="top: 125px">{{ calcBalanceInfo.balancesheet.replace(/-/g, "") }}</li>
                        <li
                            class="bac"
                            :style="{
                                top:
                                    Number(calcBalanceInfo.balancesheet) < 0
                                        ? '90px'
                                        : Number(calcBalanceInfo.balancesheet) > 0
                                        ? '35px'
                                        : '60px',
                            }"
                        >
                            {{ calcBalanceInfo.totalCredit }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </el-dialog>
    <div v-dialogDrag v-if="calcInitialReconciliationDialogVisible">
        <el-dialog
            v-model="calcInitialReconciliationDialogVisible"
            :class="isErp ? 'custom-confirm' : ''"
            title="期初对账"
            center
            width="958"
            :draggable="false"
        >
            <div class="reconciliation-content">
                <el-table :data="initialReconciliationData" :span-method="objectSpanMethod" border>
                    <el-table-column label="类别" align="center" prop="type"></el-table-column>
                    <el-table-column label="财务" align="center" prop="accValue" :width="190"></el-table-column>
                    <el-table-column label="业务" align="center" prop="businessValue" :width="190"></el-table-column>
                    <el-table-column label="差额" align="center" prop="balance" :width="190"></el-table-column>
                    <el-table-column label="状态" align="center" :width="190">
                        <template #default="scope">
                            <div v-if="scope.row.info === ''">平</div>
                            <div v-else class="unbalance">
                                不平
                                <div class="unbalance-block">
                                    <el-popover placement="bottom" :width="200" trigger="hover" :content="scope.row.info">
                                        <template #reference><img src="@/assets/Icons/warn-red.png" /></template>
                                    </el-popover>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "InitialBalance1",
};
</script>
<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed, onMounted, provide, watchEffect } from "vue";
import TableInitialBalance from "./components/TableInitialBalance.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import SearchInfo from "@/components/SearchInfo/index.vue";
import type { IStandardTabs } from "./types";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { useLoading } from "@/hooks/useLoading";
import { globalExport, globalWindowOpenPage, globalWindowOpen } from "@/util/url";
import { truncateDecimalZero } from "@/util/format";
import { isZero, trucateDecimalStrZero, formatInitialBalanceMoney, amountFormat } from "./utils";
import type { TableColumnCtx } from "element-plus";
import { getScmRelation } from "@/api/scm";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { isLemonClient } from "@/util/lmClient";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { commonFilterMethod } from "@/components/Select/utils";

const tableInitialBalanceRef = ref<InstanceType<typeof TableInitialBalance>>();

const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const tabName = ref("asset");
const tabs = ref<IStandardTabs[]>();
const tabsType = ref<number | undefined>();
const fcSelect = ref();
const accountSetStore = useAccountSetStore();
const accountSet = accountSetStore.accountSet;
switch (accountSet?.accountingStandard as number) {
    case AccountStandard.LittleCompanyStandard:
    case AccountStandard.FarmerCooperativeStandard:
    case AccountStandard.FarmerCooperativeStandard2023:
    case AccountStandard.VillageCollectiveEconomyStandard:
        tabs.value = [
            { id: 1, name: "资产", alias: "asset", active: true },
            { id: 2, name: "负债", alias: "debit", active: false },
            { id: 4, name: "权益", alias: "owner", active: false },
            { id: 5, name: "成本", alias: "cost", active: false },
            { id: 6, name: "损益", alias: "accurd", active: false },
        ];
        break;
    case AccountStandard.CompanyStandard:
        tabs.value = [
            { id: 1, name: "资产", alias: "asset", active: true },
            { id: 2, name: "负债", alias: "debit", active: false },
            { id: 3, name: "共同", alias: "comm", active: false },
            { id: 4, name: "权益", alias: "owner", active: false },
            { id: 5, name: "成本", alias: "cost", active: false },
            { id: 6, name: "损益", alias: "accurd", active: false },
        ];
        break;
    case AccountStandard.FolkComapnyStandard:
        tabs.value = [
            { id: 1, name: "资产", alias: "asset", active: true },
            { id: 2, name: "负债", alias: "debit", active: false },
            { id: 7, name: "净资产", alias: "netAssets", active: false },
            { id: 8, name: "收入", alias: "revenue", active: false },
            { id: 9, name: "费用", alias: "expenses", active: false },
        ];
        break;
    case AccountStandard.UnionStandard:
        tabs.value = [
            { id: 1, name: "资产", alias: "asset", active: true },
            { id: 2, name: "负债", alias: "debit", active: false },
            { id: 7, name: "净资产", alias: "netAssets", active: false },
            { id: 8, name: "收入", alias: "revenue", active: false },
            { id: 10, name: "支出", alias: "disbursement", active: false },
        ];
        break;
}
provide("tabs", tabs.value);
function handleTabClick(tab: any) {
    const { name } = tab.props;
    tabName.value = name;
    if (isFCView.value) {
        currentFcValue.value = 1;
    }
    getConditionData();
}

interface IFcOptions {
    id: number;
    name: string;
    rate: number;
}
const currentFcValue = ref<number>();
const isFCView = ref(false);
const isShowFC = ref(true);
const inputShowFc = computed(() => {
    return currentFcValue.value != 0 && currentFcValue.value != 1;
});
// watch(inputShowFc, (newValue) => {
//     isShowFC.value = newValue;
// });
let curentFcValueComputed = computed({
    get() {
        return currentFcValue.value;
    },
    set(val) {
        currentFcValue.value = val;
        for (var i = 0; i < fcOptions.value.length; i++) {
            if (fcOptions.value[i].id == val) {
                let toFixedValue = truncateDecimalZero(fcOptions.value[i].rate.toFixed(8));
                currentRate.value = toFixedValue == 1 ? 1 : toFixedValue;
                break;
            }
        }
        // if ((val !== undefined) && !self.isFirstLoad()) {
        //     self.loadConditionData(self.currentTab, v);
        // }
    },
});
const fcOptions = ref<Array<IFcOptions>>([]);
const fcid = ref();
const currentRate = ref(1);
const isShowQut = ref(false);
const isJan = ref(false);
const showQuantity = ref(false);
const IsEditImport = ref(false);
const importDialogVisible = ref(false);
const calcBalanceDialogVisible = ref(false);
const calcBalanceInfo = reactive({
    creditTotal: "0.00",
    balancesheet: "0.00",
    debitTotal: "0.00",
    initalCredit: "0.00",
    initial: "0.00",
    initalDebit: "0.00",
    totalCredit: "0.00",
    accumulateTotal: "0.00",
    totalDebit: "0.00",
});
const isEditable = ref(false);

const loading = ref<boolean>(false);

const tableData = ref<Object[]>([]);
function getInitdata() {
    tabsType.value = tabs.value?.find((item) => item.alias === tabName.value)?.id;
    loading.value = true;
    // useLoading().enterLoading("努力加载中，请稍候...");
    request({
        url: `/api/InitialBalance/List?type=${tabsType.value}&searchInfo=${searchInfo.value}${
            fcid.value ? `&id=${currentFcValue.value}` : ""
        }`,
    })
        .then((res: any) => {
            // useLoading().quitLoading();
            res.data.items.forEach((item: any) => {
                item.initial = amountFormat(item.initial);
                item.debit = amountFormat(item.debit);
                item.credit = amountFormat(item.credit);
                item.initialFC = amountFormat(item.initialFC);
                item.debitFC = amountFormat(item.debitFC);
                item.creditFC = amountFormat(item.creditFC);
                item.initialQut = quantityFormat(item.initialQut);
                item.debitQut = quantityFormat(item.debitQut);
                item.creditQut = quantityFormat(item.creditQut);
                item.totalQut = quantityFormat(item.totalQut);
            });
            tableData.value.length = 0;
            tableInitialBalanceRef.value?.resetVirtualTableState();
            nextTick().then(() => {
                tableData.value = res.data.items;
                tableInitialBalanceRef.value?.updateTableView();
                // isJan.value = res.data.isJanuary;
                // isFCView.value = res.data.isFCView;
                // fcOptions.value = res.data.fcitems;
                // isShowQut.value = tableData.value.some((item: any) => item.quantity !== 0);
                IsEditImport.value = res.data.isEditImport;
                isEditable.value = res.data.isEditable;
                isFCView.value = res.data.isFCView;
                isShowFC.value = false;
                if (isFCView.value) {
                    isShowFC.value = true;
                    fcOptions.value = [];
                    if (fcid.value === undefined) {
                        fcOptions.value.push({ id: 0, name: "综合本位币", rate: 1 });
                        for (let f = 0; f < res.data.fcitems.length; f++) {
                            fcOptions.value.push(res.data.fcitems[f]);
                        }
                        // $("#fc-select").select2();
                        currentFcValue.value = 1;
                    }
                }
                curentFcValueComputed.value = 1;
                isShowQut.value = tableData.value.some((item: any) => item.quantity !== 0);
            });
        })
        .finally(() => {
            loading.value = false;
        });
}

function quantityFormat(value: any) {
    if (isZero(value, 8)) {
        return "";
    }
    return trucateDecimalStrZero(formatInitialBalanceMoney(value, 8));
}
function getConditionData(height?: number) {
    useLoading().enterLoading("努力加载中，请稍候...");
    tabsType.value = tabs.value?.find((item) => item.alias === tabName.value)?.id;
    loading.value = true;
    request({
        url: `/api/InitialBalance/List?type=${tabsType.value}&id=${currentFcValue.value}&searchInfo=${searchInfo.value}`,
    })
        .then((res: any) => {
            useLoading().quitLoading();
            res.data.items.forEach((item: any) => {
                item.initial = amountFormat(item.initial);
                item.debit = amountFormat(item.debit);
                item.credit = amountFormat(item.credit);
                item.initialFC = amountFormat(item.initialFC);
                item.debitFC = amountFormat(item.debitFC);
                item.creditFC = amountFormat(item.creditFC);
                item.initialQut = quantityFormat(item.initialQut);
                item.debitQut = quantityFormat(item.debitQut);
                item.creditQut = quantityFormat(item.creditQut);
                item.totalQut = quantityFormat(item.totalQut);
            });
            tableData.value.length = 0;
            tableInitialBalanceRef.value?.resetVirtualTableState();
            nextTick().then(() => {
                tableData.value = res.data.items;
                tableInitialBalanceRef.value?.updateTableView();
                isJan.value = res.data.isJanuary;
                isFCView.value = res.data.isFCView;
                isShowQut.value = tableData.value.some((item: any) => item.quantity > 0);
                IsEditImport.value = res.data.isEditImport;
                isEditable.value = res.data.isEditable;
                for (let i = 0; i < res.data.fcitems.length; i++) {
                    // var r = new IBRowModal(rdata.Items[i], rdata.IsJanuary);
                    // r.AssistingAccounting > 0 ? self[cTab].isAssit = true : "";
                    // r.Quantity > 0 ? self[cTab].isQuantity(true) : "";
                    if (fcid.value == 0) {
                        isEditable.value = false;
                    }
                    // r.IsEditable = r.IsEditable && window.functionState['initialbalance1-canedit'] !== 0;
                    // self[cTab].rows.push(r);
                }
                setTimeout(() => {
                    height && (tableInitialBalanceRef.value?.setTableScrollTop(height));
                });
            })
        })
        .finally(() => {
            loading.value = false;
        });
}

const searchInfo = ref("");
function handleSearchInfo(val: string) {
    searchInfo.value = val;
    getConditionData();
}

watch(currentFcValue, () => {
    isShowFC.value && (currentRate.value = (fcOptions.value.find((item: any) => item.id == currentFcValue.value) as IFcOptions).rate);
    getConditionData();
});

function exportExcel() {
    if (isFCView.value) {
        globalExport(`/api/InitialBalance/ExportFc?filename=财务初始余额表`);
    } else {
        globalExport(`/api/InitialBalance/Export?filename=财务初始余额表`);
    }
}
function importHandle() {
    if (!IsEditImport.value) {
        ElConfirm("亲，账套已存在结账期间，请先反结账再处理~").then((r: Boolean) => {
            if (r) {
                globalWindowOpenPage("/Checkout/Checkout?from=initialbalance&r=" + Math.random(), "期末结转");
            }
        });
    } else {
        importDialogVisible.value = true;
    }
}

const accountsetStore = useAccountSetStore();
function getExcelTemplate() {
    let url = "";
    if (isShowFC.value) {
        url = `/api/InitialBalance/ExportFcTemplate?filename=财务初始余额表`;
    } else {
        url = `/api/InitialBalance/ExportTemplate?filename=财务初始余额表`;
    }
    globalExport(url);
}

const importErrorShow = ref(false);
const importErrorMessage = ref("");
const uploadSuccess = (res: IResponseModel<any>) => {
    if (res.data === "Y") {
        importDialogVisible.value = false;
        ElNotify({
            type: "success",
            message: "导入成功",
        });
        setTimeout(function () {
            if (isErp.value || isLemonClient()) {
                useRouterArrayStoreHook().refreshRouter('/Settings/InitialBalance1')
            } else {
                getInitdata();
            }
        }, 0);
    } else if (res.data === "nofile") {
        ElNotify({
            type: "warning",
            message: "您还未选择上传文件！",
        });
    } else if (res.data === "error") {
        ElNotify({
            type: "warning",
            message: "您上传的文件格式不正确，请上传excel文件！",
        });
    } else if (res.data === "") {
        importErrorMessage.value = res.msg;
        importErrorShow.value = true;
    }
};

// 是否开启重分类参数
const classificationSwitch = ref(JSON.parse(window.localStorage.getItem("classificationSwitch") || "false"));
const needClassification = computed(() => [1, 2, 3].includes(accountsetStore.accountSet!.accountingStandard));

const isClassification = computed(() => {
    if (needClassification.value) {
        return classificationSwitch.value;
    } else if ([4, 5].includes(accountsetStore.accountSet!.accountingStandard)) {
        return true;
    } else {
        return false;
    }
});

function calcBalanceHandle() {
    classificationSwitch.value = JSON.parse(window.localStorage.getItem("classificationSwitch") || "false");
    request({
        url: `/api/InitialBalance/GetCalcBalance`,
        method: "post",
        params: {
            isClassification: isClassification.value,
        },
    }).then((res: any) => {
        calcBalanceDialogVisible.value = true;
        calcBalanceInfo.initalCredit = res.data.initalCredit; //期初余额 贷方
        calcBalanceInfo.initalDebit = res.data.initalDebit; //期初余额 借方
        //期初余额 借方-贷方
        calcBalanceInfo.initial = (
            parseFloat(calcBalanceInfo.initalDebit.replace(/,/g, "")) - parseFloat(calcBalanceInfo.initalCredit.replace(/,/g, ""))
        ).toFixed(2);
        calcBalanceInfo.creditTotal = res.data.creditTotal; //累计发生 贷方
        calcBalanceInfo.debitTotal = res.data.debitTotal; //累计发生 借方
        //累计发生 贷方-借方
        calcBalanceInfo.accumulateTotal = (
            parseFloat(calcBalanceInfo.creditTotal.replace(/,/g, "")) - parseFloat(calcBalanceInfo.debitTotal.replace(/,/g, ""))
        ).toFixed(2);
        calcBalanceInfo.totalCredit = res.data.totalCredit; //资产负债表期初 负债和所有者权益
        calcBalanceInfo.totalDebit = res.data.totalDebit; //资产负债表期初 资产
        //balancesheet=资产-负债和所有者权益
        calcBalanceInfo.balancesheet = (
            parseFloat(calcBalanceInfo.totalDebit.replace(/,/g, "")) - parseFloat(calcBalanceInfo.totalCredit.replace(/,/g, ""))
        ).toFixed(2);
        judgeBalance();
    });
}

function judgeBalance() {
    nextTick().then(() => {
        if (accountsetStore.accountSet?.accountingStandard === 3) {
            (document.getElementById("bag") as HTMLElement).classList.remove("bg2");
            (document.getElementById("bag") as HTMLElement).classList.add("bg3");
        }
        //期初余额试算 initial=借方-贷方
        if (calcBalanceInfo.initalCredit == calcBalanceInfo.initalDebit) {
            (document.getElementById("ibg") as HTMLElement).classList.remove("credit", "debit");
            (document.getElementById("ibg") as HTMLElement).classList.add("balance");
        } else if (Number(calcBalanceInfo.initial) < 0) {
            (document.getElementById("ibg") as HTMLElement).classList.remove("balance", "debit");
            (document.getElementById("ibg") as HTMLElement).classList.add("credit");
        } else {
            (document.getElementById("ibg") as HTMLElement).classList.remove("credit", "balance");
            (document.getElementById("ibg") as HTMLElement).classList.add("debit");
        }
        //累积发生试算 accumulateTotal=贷方-借方
        if (calcBalanceInfo.creditTotal == calcBalanceInfo.debitTotal) {
            (document.getElementById("abg") as HTMLElement).classList.remove("credit", "debit");
            (document.getElementById("abg") as HTMLElement).classList.add("balance");
        } else if (Number(calcBalanceInfo.accumulateTotal) > 0) {
            (document.getElementById("abg") as HTMLElement).classList.remove("balance", "debit");
            (document.getElementById("abg") as HTMLElement).classList.add("credit");
        } else {
            (document.getElementById("abg") as HTMLElement).classList.remove("credit", "balance");
            (document.getElementById("abg") as HTMLElement).classList.add("debit");
        }
        //资产负债表期初试算 //balancesheet=资产-负债和所有者权益 民间
        if (calcBalanceInfo.totalCredit == calcBalanceInfo.totalDebit) {
            if (accountsetStore.accountSet?.accountingStandard === 3) {
                (document.getElementById("bag") as HTMLElement).classList.remove("credit2", "debit2");
                (document.getElementById("bag") as HTMLElement).classList.add("balance2");
            } else {
                (document.getElementById("bag") as HTMLElement).classList.remove("credit1", "debit1");
                (document.getElementById("bag") as HTMLElement).classList.add("balance1");
            }
        } else if (Number(calcBalanceInfo.balancesheet) < 0) {
            if (accountsetStore.accountSet?.accountingStandard === 3) {
                (document.getElementById("bag") as HTMLElement).classList.remove("balance2", "debit2");
                (document.getElementById("bag") as HTMLElement).classList.add("credit2");
            } else {
                (document.getElementById("bag") as HTMLElement).classList.remove("balance1", "debit1");
                (document.getElementById("bag") as HTMLElement).classList.add("credit1");
            }
        } else {
            if (accountsetStore.accountSet?.accountingStandard === 3) {
                (document.getElementById("bag") as HTMLElement).classList.remove("balance2", "credit2");
                (document.getElementById("bag") as HTMLElement).classList.add("debit2");
            } else {
                (document.getElementById("bag") as HTMLElement).classList.remove("credit1", "balance1");
                (document.getElementById("bag") as HTMLElement).classList.add("debit1");
            }
        }
    });
}

const calcInitialReconciliationDialogVisible = ref(false);
interface IInitialReconciliationRequestData {
    //财务应收账款
    accCustomerAccountsReceivable: number;
    //进销存应收账款
    scmCustomerAccountsReceivable: number;
    //应收账款差额
    customerAccountsReceivableBalance: number;
    //财务预收账款
    accCustomerAdvancesReceivable: number;
    //进销存预收账款
    scmCustomerAdvancesReceivable: number;
    //预收账款差额
    customerAdvancesReceivableBalance: number;

    //财务应付账款
    accVendorAccountSpayable: number;
    //进销存应付账款
    scmVendorAccountSpayable: number;
    //应付账款差额
    vendorAccountSpayableBalance: number;
    //财务预付账款
    accVendorPrepayments: number;
    //进销存预付账款
    scmVendorPrepayments: number;
    //预付账款差额
    vendorPrepaymentsBalance: number;

    //财务库存商品
    accCommodityStock: number;
    //进销存库存商品
    scmCommodityStock: number;
    //库存商品差额
    commodityStockBalance: number;

    //科目库存现金
    asubCash: number;
    //日记账库存现金
    journalCash: number;
    //库存现金差额
    cashBalance: number;
    //科目银行存款
    asubBank: number;
    //日记账银行存款
    journalBank: number;
    //银行存款差额
    bankBalance: number;

    //科目资产
    asubFixedAsset: number;
    //资产中的资产
    fixFixedAsset: number;
    //资产差额
    fixedAssetBalance: number;
    //科目累计折旧
    asubDepreciation: number;
    //资产累计折旧
    fixDepreciation: number;
    //累计折旧差额
    depreciationBalance: number;
}
const calcInitialReconciliationInfo = reactive({
    //财务应收账款
    accCustomerAccountsReceivable: "0.00",
    //进销存应收账款
    scmCustomerAccountsReceivable: "0.00",
    //应收账款差额
    customerAccountsReceivableBalance: "0.00",
    //财务预收账款
    accCustomerAdvancesReceivable: "0.00",
    //进销存预收账款
    scmCustomerAdvancesReceivable: "0.00",
    //预收账款差额
    customerAdvancesReceivableBalance: "0.00",
    //客户状态
    customerInfo: "",

    //财务应付账款
    accVendorAccountSpayable: "0.00",
    //进销存应付账款
    scmVendorAccountSpayable: "0.00",
    //应付账款差额
    vendorAccountSpayableBalance: "0.00",
    //财务预付账款
    accVendorPrepayments: "0.00",
    //进销存预付账款
    scmVendorPrepayments: "0.00",
    //预付账款差额
    vendorPrepaymentsBalance: "0.00",
    //供应商状态
    vendorInfo: "",

    //财务库存商品
    accCommodityStock: "0.00",
    //进销存库存商品
    scmCommodityStock: "0.00",
    //库存商品差额
    commodityStockBalance: "0.00",
    //商品状态
    commodityInfo: "",

    //科目库存现金
    asubCash: "0.00",
    //日记账库存现金
    journalCash: "0.00",
    //库存现金差额
    cashBalance: "0.00",
    //科目银行存款
    asubBank: "0.00",
    //日记账银行存款
    journalBank: "0.00",
    //银行存款差额
    bankBalance: "0.00",
    //资金状态
    journalInfo: "",

    //科目资产
    asubFixedAsset: "0.00",
    //资产中的资产
    fixFixedAsset: "0.00",
    //资产差额
    fixedAssetBalance: "0.00",
    //科目累计折旧
    asubDepreciation: "0.00",
    //资产累计折旧
    fixDepreciation: "0.00",
    //累计折旧差额
    depreciationBalance: "0.00",
    //资产状态
    fixedAssetInfo: "",
});
interface IInitialReconciliationRow {
    type: string;
    accValue: string;
    businessValue: string;
    balance: string;
    info: string;
}
const initialReconciliationData = ref<Array<IInitialReconciliationRow>>([]);
interface SpanMethodProps {
    row: IInitialReconciliationRow;
    column: TableColumnCtx<IInitialReconciliationRow>;
    rowIndex: number;
    columnIndex: number;
}
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: SpanMethodProps) => {
    if (columnIndex === 0 || columnIndex === 4) {
        if (rowIndex === 0 || rowIndex === 4 || rowIndex === 10 || rowIndex === 14) {
            return {
                rowspan: 4,
                colspan: 1,
            };
        } else if (rowIndex === 8) {
            return {
                rowspan: 2,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    } else if (columnIndex === 3) {
        if (rowIndex % 2 === 0) {
            return {
                rowspan: 2,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
};

function calcInitialReconciliationHandle() {
    getScmRelation().then((res) => {
        if (res.state === 1000 && res.data.isRelation) {
            request({
                url:
                    "/api/InitialBalanceForErp/GetReconciliation?scmProductType=" +
                    res.data.scmProductType +
                    "&scmAsid=" +
                    res.data.scmAsid,
                method: "post",
            }).then((res: IResponseModel<IInitialReconciliationRequestData>) => {
                if (res.state === 1000) {
                    setInitialReconciliationInfo(res.data);
                    calcInitialReconciliationDialogVisible.value = true;
                } else {
                    ElNotify({ type: "error", message: "获取失败，请稍候重试" });
                }
            });
        } else {
            ElNotify({ type: "error", message: "不存在进销存关联" });
            return;
        }
    });
}

function setInitialReconciliationInfo(data: IInitialReconciliationRequestData) {
    calcInitialReconciliationInfo.accCustomerAccountsReceivable = formatInitialBalanceMoney(data.accCustomerAccountsReceivable);
    calcInitialReconciliationInfo.scmCustomerAccountsReceivable = formatInitialBalanceMoney(data.scmCustomerAccountsReceivable);
    calcInitialReconciliationInfo.customerAccountsReceivableBalance = formatInitialBalanceMoney(data.customerAccountsReceivableBalance);
    calcInitialReconciliationInfo.accCustomerAdvancesReceivable = formatInitialBalanceMoney(data.accCustomerAdvancesReceivable);
    calcInitialReconciliationInfo.scmCustomerAdvancesReceivable = formatInitialBalanceMoney(data.scmCustomerAdvancesReceivable);
    calcInitialReconciliationInfo.customerAdvancesReceivableBalance = formatInitialBalanceMoney(data.customerAdvancesReceivableBalance);
    if (data.customerAccountsReceivableBalance === 0 && data.customerAdvancesReceivableBalance === 0) {
        calcInitialReconciliationInfo.customerInfo = "";
    } else {
        let title = "";
        if (data.customerAccountsReceivableBalance !== 0) {
            title += "应收账款和期初应收账款相差金额" + formatInitialBalanceMoney(data.customerAccountsReceivableBalance) + "，";
        }
        if (data.customerAdvancesReceivableBalance != 0) {
            title += "预收账款和期初预收账款相差金额" + formatInitialBalanceMoney(data.customerAdvancesReceivableBalance) + "，";
        }
        title += "请前往设置-科目期初余额和设置-客户期初余额表查看数据核对";
        calcInitialReconciliationInfo.customerInfo = title;
    }

    calcInitialReconciliationInfo.accVendorAccountSpayable = formatInitialBalanceMoney(data.accVendorAccountSpayable);
    calcInitialReconciliationInfo.scmVendorAccountSpayable = formatInitialBalanceMoney(data.scmVendorAccountSpayable);
    calcInitialReconciliationInfo.vendorAccountSpayableBalance = formatInitialBalanceMoney(data.vendorAccountSpayableBalance);
    calcInitialReconciliationInfo.accVendorPrepayments = formatInitialBalanceMoney(data.accVendorPrepayments);
    calcInitialReconciliationInfo.scmVendorPrepayments = formatInitialBalanceMoney(data.scmVendorPrepayments);
    calcInitialReconciliationInfo.vendorPrepaymentsBalance = formatInitialBalanceMoney(data.vendorPrepaymentsBalance);
    if (data.vendorAccountSpayableBalance === 0 && data.vendorPrepaymentsBalance === 0) {
        calcInitialReconciliationInfo.vendorInfo = "";
    } else {
        let title = "";
        if (data.vendorAccountSpayableBalance !== 0) {
            title += "应付账款和期初应付账款相差金额" + formatInitialBalanceMoney(data.vendorAccountSpayableBalance) + "，";
        }
        if (data.vendorPrepaymentsBalance !== 0) {
            title += "预付账款和期初预付账款相差金额" + formatInitialBalanceMoney(data.vendorPrepaymentsBalance) + "，";
        }
        title += "请前往设置-科目期初余额和设置-供应商期初余额表查看数据核对";
        calcInitialReconciliationInfo.vendorInfo = title;
    }

    calcInitialReconciliationInfo.accCommodityStock = formatInitialBalanceMoney(data.accCommodityStock);
    calcInitialReconciliationInfo.scmCommodityStock = formatInitialBalanceMoney(data.scmCommodityStock);
    calcInitialReconciliationInfo.commodityStockBalance = formatInitialBalanceMoney(data.commodityStockBalance);
    if (data.commodityStockBalance === 0) {
        calcInitialReconciliationInfo.commodityInfo = "";
    } else {
        let title = "库存商品和期初库存商品相差金额" + formatInitialBalanceMoney(data.commodityStockBalance) + "，";
        title += "请前往设置-科目期初余额和设置-商品期初余额表查看数据核对";
        calcInitialReconciliationInfo.commodityInfo = title;
    }

    calcInitialReconciliationInfo.asubCash = formatInitialBalanceMoney(data.asubCash);
    calcInitialReconciliationInfo.journalCash = formatInitialBalanceMoney(data.journalCash);
    calcInitialReconciliationInfo.cashBalance = formatInitialBalanceMoney(data.cashBalance);
    calcInitialReconciliationInfo.asubBank = formatInitialBalanceMoney(data.asubBank);
    calcInitialReconciliationInfo.journalBank = formatInitialBalanceMoney(data.journalBank);
    calcInitialReconciliationInfo.bankBalance = formatInitialBalanceMoney(data.bankBalance);
    if (data.cashBalance === 0 && data.bankBalance === 0) {
        calcInitialReconciliationInfo.journalInfo = "";
    } else {
        let title = "";
        if (data.cashBalance !== 0) {
            title += "库存现金和现金日记账相差金额" + formatInitialBalanceMoney(data.cashBalance) + "，";
        }
        if (data.bankBalance !== 0) {
            title += "银行存款和银行日记账相差金额" + formatInitialBalanceMoney(data.bankBalance) + "，";
        }
        title += "请前往资金管理-银行日记账和设置-科目期初余额查看数据核对";
        calcInitialReconciliationInfo.journalInfo = title;
    }

    calcInitialReconciliationInfo.asubFixedAsset = formatInitialBalanceMoney(data.asubFixedAsset);
    calcInitialReconciliationInfo.fixFixedAsset = formatInitialBalanceMoney(data.fixFixedAsset);
    calcInitialReconciliationInfo.fixedAssetBalance = formatInitialBalanceMoney(data.fixedAssetBalance);
    calcInitialReconciliationInfo.asubDepreciation = formatInitialBalanceMoney(data.asubDepreciation);
    calcInitialReconciliationInfo.fixDepreciation = formatInitialBalanceMoney(data.fixDepreciation);
    calcInitialReconciliationInfo.depreciationBalance = formatInitialBalanceMoney(data.depreciationBalance);
    if (data.fixedAssetBalance === 0 && data.depreciationBalance === 0) {
        calcInitialReconciliationInfo.fixedAssetInfo = "";
    } else {
        let title = "";
        if (data.fixedAssetBalance !== 0) {
            title += "财务资产和业务资产相差金额" + formatInitialBalanceMoney(data.fixedAssetBalance) + "，";
        }
        if (data.depreciationBalance !== 0) {
            title += "财务累计折旧和业务累计折旧相差金额" + formatInitialBalanceMoney(data.depreciationBalance) + "，";
        }
        title += "请前往资产管理-资产初始化和设置-科目期初余额查看数据核对";
        calcInitialReconciliationInfo.fixedAssetInfo = title;
    }
    setTableData();
}
function setTableData() {
    initialReconciliationData.value = [
        {
            type: "客户",
            accValue: "应收账款",
            businessValue: "期初应收账款",
            balance: calcInitialReconciliationInfo.customerAccountsReceivableBalance,
            info: calcInitialReconciliationInfo.customerInfo,
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.accCustomerAccountsReceivable,
            businessValue: calcInitialReconciliationInfo.scmCustomerAccountsReceivable,
            balance: "",
            info: "",
        },
        {
            type: "",
            accValue: "预收账款",
            businessValue: "期初预收账款",
            balance: calcInitialReconciliationInfo.customerAdvancesReceivableBalance,
            info: "",
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.accCustomerAdvancesReceivable,
            businessValue: calcInitialReconciliationInfo.scmCustomerAdvancesReceivable,
            balance: "",
            info: "",
        },
        {
            type: "供应商",
            accValue: "应付账款",
            businessValue: "期初应付账款",
            balance: calcInitialReconciliationInfo.vendorAccountSpayableBalance,
            info: calcInitialReconciliationInfo.vendorInfo,
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.accVendorAccountSpayable,
            businessValue: calcInitialReconciliationInfo.scmVendorAccountSpayable,
            balance: "",
            info: "",
        },
        {
            type: "",
            accValue: "预付账款",
            businessValue: "期初预付账款",
            balance: calcInitialReconciliationInfo.vendorPrepaymentsBalance,
            info: "",
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.accVendorPrepayments,
            businessValue: calcInitialReconciliationInfo.scmVendorPrepayments,
            balance: "",
            info: "",
        },
        {
            type: "商品",
            accValue: "库存商品",
            businessValue: "期初库存商品",
            balance: calcInitialReconciliationInfo.commodityStockBalance,
            info: calcInitialReconciliationInfo.commodityInfo,
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.accCommodityStock,
            businessValue: calcInitialReconciliationInfo.scmCommodityStock,
            balance: "",
            info: "",
        },
        {
            type: "资金",
            accValue: "库存现金",
            businessValue: "现金日记账",
            balance: calcInitialReconciliationInfo.cashBalance,
            info: calcInitialReconciliationInfo.journalInfo,
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.asubCash,
            businessValue: calcInitialReconciliationInfo.journalCash,
            balance: "",
            info: "",
        },
        { type: "", accValue: "银行存款", businessValue: "银行日记账", balance: calcInitialReconciliationInfo.bankBalance, info: "" },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.asubBank,
            businessValue: calcInitialReconciliationInfo.journalBank,
            balance: "",
            info: "",
        },
        {
            type: "资产",
            accValue: "资产",
            businessValue: "资产",
            balance: calcInitialReconciliationInfo.fixedAssetBalance,
            info: calcInitialReconciliationInfo.fixedAssetInfo,
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.asubFixedAsset,
            businessValue: calcInitialReconciliationInfo.fixFixedAsset,
            balance: "",
            info: "",
        },
        {
            type: "",
            accValue: "累计折旧",
            businessValue: "累计折旧",
            balance: calcInitialReconciliationInfo.depreciationBalance,
            info: "",
        },
        {
            type: "",
            accValue: calcInitialReconciliationInfo.asubDepreciation,
            businessValue: calcInitialReconciliationInfo.fixDepreciation,
            balance: "",
            info: "",
        },
    ];
}

onMounted(() => {
    getInitdata();
    window.addEventListener("modifyaccountSubject", () => {
        if (useRouterArrayStoreHook().routerArray.find((item) => item.title.includes("期初"))) {
            getInitdata();
        }
    });
    classificationSwitch.value = JSON.parse(window.localStorage.getItem("classificationSwitch") || "false");
});

watch(fcid, () => {
    currentRate.value = (fcOptions.value.find((item: any) => item.id == fcid.value) as IFcOptions).rate;
    getInitdata();
});

watch(currentFcValue, () => {
    nextTick().then(() => {
        fcSelect.value?.blur();
    });
});

const showfcOptions = ref<Array<IFcOptions>>([]);
watchEffect(() => {
    showfcOptions.value = JSON.parse(JSON.stringify(fcOptions.value));
});
function taxTypeFilterMethod(value: string) {
    showfcOptions.value = commonFilterMethod(value, fcOptions.value, 'name');
}
   
</script>

<style scoped lang="less">
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";
@import "@/style/Settings/InitialBalance.less";
.reconciliation-content {
    padding: 22px 32px;
    :deep(.el-table--enable-row-hover) {
        .el-table__body tr:hover > td.el-table__cell {
            background-color: var(--white);
        }
    }
    :deep(.cell) {
        .unbalance {
            color: var(--red);

            .unbalance-block {
                display: inline-block;
                cursor: pointer;
                position: absolute;

                img {
                    width: 14px;
                    margin-top: 5px;
                    margin-left: 2px;
                }
            }
        }
    }
}
body[erp] {
    .main-content {
        :deep(.custom-confirm) {
            .el-dialog__body {
                .buttons {
                    display: flex;
                    justify-content: flex-end;
                    a {
                        float: initial;
                    }
                }
            }
        }
    }
}
.content {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main-content {
        flex: 1;
        overflow: hidden;
        :deep(.container) {
            height: 100%;
            overflow: hidden;
            .table {
                height: 100%;
                .el-table {
                    height: 100%;
                }
            }
        }
    }
}

</style>
