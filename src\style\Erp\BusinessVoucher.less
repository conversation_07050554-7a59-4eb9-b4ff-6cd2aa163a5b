@import "../SelfAdaption.less";
.main-content {
    .main-top {
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        .input-group {
            width: 276px;
            height: 28px;
            position: relative;
            margin-right: 20px;
            input[type="text"] {
                font-size: var(--font-size);
                width: 276px;
                height: 28px;
                padding: 0;
                padding-left: 10px;
                padding-right: 32px;
                outline: none;
                border-radius: 4px;
                box-shadow: 0 0 0 1px #dcdfe6 inset;
                box-sizing: border-box;
                border: none;
            }
            .icon {
                background-image: url(@/assets/Settings/sousuo.png);
                background-repeat: no-repeat;
                height: 18px;
                width: 18px;
                top: 7px;
                right: 8px;
                cursor: pointer;
                position: absolute;
                margin: 0;
                background-size: 15px 15px;
            }
            &:hover {
                input[type="text"] {
                    box-shadow: 0 0 0 1px var(--main-color) inset;
                }
            }
        }
    }
    .main-center-options {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 20px 10px 20px;
        position: relative;
        flex-wrap: wrap;
        &.more {
            padding-bottom: 0;
        }
        .main-center-option {
            display: flex;
            flex-direction: row;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 22px;
            align-items: center;
            margin-top: 10px;
            & + .main-center-option {
                margin-left: 10px;
            }
            :deep(.empty .el-input__inner) {
                color: #929292;
            }
            :deep(.el-select.desc) {
                .el-input__wrapper {
                    padding-right: 5px;
                    padding-left: 5px;
                }
                .el-select-dropdown__item {
                    line-height: 18px;
                }
                .el-icon {
                    margin-left: 0;
                }
            }
        }
        .main-center-button {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }
        .more-search {
            position: absolute;
            right: 20px;
            top: 12px;
            .button.solid-button {
                padding-right: 32px;
            }
            :deep(.el-icon) {
                position: absolute;
                right: 8px;
                top: 6px;
            }
        }
        .search-button-container {
            display: flex;
            align-items: center;
            &.entry {
                // 更多查询占位 100px  外加 10px 的间距
                padding-right: 110px;
            }
        }
    }
    .main-center-options-more {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 10px;
        position: relative;
        margin-top: 10px;
        padding: 0 20px;
        .more-search-option {
            display: flex;
            align-items: center;
            height: 32px;
            margin-top: 10px;
            margin-right: 20px;
            font-size: var(--font-size);
            text-align: left;
            :deep(.el-input__suffix-inner) {
                right: 5px;
            }
            :deep(.check-box) {
                overflow: visible;
            }
        }
        .search-2 {
            margin-top: 12px;
        }
        &::before {
            content: "";
            display: block;
            width: 100%;
            height: 1px;
            background: var(--border-color);
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    :deep(.el-date-editor) {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
        & .el-input__suffix-inner {
            position: absolute;
            right: 30px;
            top: 9px;
        }
    }
}
.voucher-result {
    .result-info {
        color: var(--font-color);
        font-size: var(--h3);
        line-height: 24px;
        margin: 20px 48px;
        .success-content,
        .err-content {
            .l {
                text-indent: 20px;
            }
        }
        .success-content {
            margin-bottom: 30px;
        }
        .err-content .t .link {
            margin-left: 50px;
        }
    }
    .result-button {
        border-top: 1px solid #e9e9e9;
        padding: 20px;
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
    }
}

.warning-tip {
    font-size: 13px;
    display: flex;
    align-items: center;
    .delete {
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        background: url("@/assets/Icons/delete-orange.png") no-repeat center;
        background-size: 100%;
        cursor: pointer;
    }
}

.switch-button {
    display: inline-block;
    padding: 0 10px 0 32px;
    cursor: pointer;
    line-height: 28px;
    font-size: 14px;
    padding-left: 30px !important;
    background: url("@/assets/Erp/switch_h.png") no-repeat 5px 4px;
    background-size: 20px 20px;
    &:hover {
        background-image: url("@/assets/Erp/switch_h_blue.png");
    }
}

.err-month-content {
    .main {
        padding: 20px 40px;
        line-height: 28px;
        > div {
            padding-left: 20px;
        }
        .e-l {
            margin-bottom: 5px;
        }
        .e-r.warning {
            background: url("@/assets/Icons/warn.png") no-repeat;
            background-position-x: 4px;
            background-position-y: 6px;
        }
    }
}
.buttonContent {
    position: relative;
}
.hover-icon {
    position: absolute;
    right: -14px;
    top: -10px;
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url(@/assets/Icons/question.png) no-repeat center;
    background-size: 100%;
    margin-left: 5px;
    cursor: pointer;
    &:hover {
        background-image: url(@/assets/Icons/question-erp.png);
    }
}

.buttonContent {
    display: inline-flex;
    align-items: center;
}
.help {
    display: flex;
    align-items: center;
    cursor: pointer;
}