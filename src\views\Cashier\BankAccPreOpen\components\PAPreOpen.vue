<template>
    <div class="slot-content">
        <div class="slot-mini-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #step_three>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="3" />
                                <div class="step-edit">
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPAInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="bankOutletsInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="选择银行网点：" :required="true" prop="branchCityNo">
                                                    <Select
                                                        v-model="companyToPAInfo.branchCityNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectPrimaryOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in primaryOutletList"
                                                            :value="item.branchNo"
                                                            :label="item.branchName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="二级网点：" :required="true">
                                                    <Select
                                                        v-model="companyToPAInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectSecondaryOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in secondaryOutletList"
                                                            :value="item.branchNo"
                                                            :label="item.branchName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToPAInfo.branchAddr || companyToPAInfo.branchTel">
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToPAInfo.branchAddr">
                                                        {{ companyToPAInfo.branchAddr }}
                                                    </div>
                                                    <div v-show="companyToPAInfo.branchTel">网点电话：{{ companyToPAInfo.branchTel }}</div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToPAInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true" prop="accountType">
                                                    <el-radio-group v-model="companyToPAInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="back">上一步</a>
                                    <a class="button ml-28 solid-button" @click="toStepFour">下一步</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #step_four>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="4" />
                                <div class="step-edit">
                                    <div class="block-title">公司信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToPAInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow addressRow">
                                                <el-form-item label="注册地址：" :required="true">
                                                    <Select
                                                        v-model="companyToPAInfo.regProvCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectRegProvince"
                                                    >
                                                        <Option
                                                            v-for="item in provinceList"
                                                            :value="item.primaryCode"
                                                            :label="item.primaryName"
                                                            :key="item.primaryCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <Select
                                                        v-model="companyToPAInfo.regCityCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectRegCity"
                                                    >
                                                        <Option
                                                            v-for="item in regCityList"
                                                            :value="item.secondCode"
                                                            :label="item.secondaryListName"
                                                            :key="item.secondCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <el-input v-model="companyToPAInfo.regAddr" style="width: 344px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow addressRow">
                                                <el-form-item label="经营地址：" :required="true">
                                                    <Select
                                                        v-model="companyToPAInfo.bizProvCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectBizProvince"
                                                    >
                                                        <Option
                                                            v-for="item in provinceList"
                                                            :value="item.primaryCode"
                                                            :label="item.primaryName"
                                                            :key="item.primaryCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <Select
                                                        v-model="companyToPAInfo.bizCityCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectBizCity"
                                                    >
                                                        <Option
                                                            v-for="item in bizCityList"
                                                            :value="item.secondCode"
                                                            :label="item.secondaryListName"
                                                            :key="item.secondCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <el-input v-model="companyToPAInfo.bizAddr" style="width: 344px"></el-input>
                                                    <el-checkbox
                                                        v-model="isSameAddress"
                                                        label="与注册地址一致"
                                                        @change="handleSameAddress"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">预约时间</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPAInfo"
                                            label-position="right"
                                            label-width="191px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="预约期望时间：" :required="true" prop="appointmentTime">
                                                    <el-date-picker
                                                        v-model="companyToPAInfo.appointmentTime"
                                                        type="date"
                                                        placeholder="请选择预约时间，具体以银行审核为准"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 330px"
                                                        :disabled-date="disabledDateStart"
                                                        @change="dateChange"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_three'">上一步</a>
                                    <a class="button ml-28 solid-button longer-button" @click="toCofirmInfo">立即预约开户</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #confirm>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">确认预约开户详情</div>
                            <div class="open-main-content">
                                <div class="step-edit">
                                    <div class="block-title">开户银行</div>
                                    <div class="block-main">
                                        <el-row class="isRow"><div class="openbank-line">开户银行：平安银行</div> </el-row>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPAInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="bankOutletsInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="选择银行网点：" :required="true" prop="branchCityNo">
                                                    <Select
                                                        v-model="companyToPAInfo.branchCityNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectPrimaryOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in primaryOutletList"
                                                            :value="item.branchNo"
                                                            :label="item.branchName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="二级网点：" :required="true">
                                                    <Select
                                                        v-model="companyToPAInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectSecondaryOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in secondaryOutletList"
                                                            :value="item.branchNo"
                                                            :label="item.branchName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToPAInfo.branchAddr || companyToPAInfo.branchTel">
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToPAInfo.branchAddr">
                                                        {{ companyToPAInfo.branchAddr }}
                                                    </div>
                                                    <div v-show="companyToPAInfo.branchTel">网点电话：{{ companyToPAInfo.branchTel }}</div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToPAInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true" prop="accountType">
                                                    <el-radio-group v-model="companyToPAInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">基本信息</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPAInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="公司名称：" :required="true" prop="companyName">
                                                    <el-autocomplete
                                                        ref="asNameRef"
                                                        @blur="handleCompanyBlur"
                                                        v-model="companyToPAInfo.companyName"
                                                        :prop="[
                                                            {
                                                                required: true,
                                                                message: '亲，单位名称不能为空',
                                                                trigger: ['blur', 'change'],
                                                            },
                                                        ]"
                                                        :fetch-suggestions="querySearch"
                                                        :trigger-on-focus="false"
                                                        placeholder="请输入完整的单位名称"
                                                        style="width: 240px"
                                                        @select="selectName"
                                                    />
                                                </el-form-item>
                                                <el-form-item label="统一社会信用代码：" :required="true" prop="unifiedNumber">
                                                    <el-input
                                                        v-model="companyToPAInfo.unifiedNumber"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人姓名：" :required="true" prop="legalName">
                                                    <el-input
                                                        v-model="companyToPAInfo.legalName"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                        @blur="handleNameBlur"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="法人手机号码：" :required="true" prop="legalMobile">
                                                    <el-input
                                                        v-model="companyToPAInfo.legalMobile"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">更多信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请补充如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPAInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow addressRow">
                                                <el-form-item label="注册地址：" :required="true">
                                                    <Select
                                                        v-model="companyToPAInfo.regProvCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectRegProvince"
                                                    >
                                                        <Option
                                                            v-for="item in provinceList"
                                                            :value="item.primaryCode"
                                                            :label="item.primaryName"
                                                            :key="item.primaryCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <Select
                                                        v-model="companyToPAInfo.regCityCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectRegCity"
                                                    >
                                                        <Option
                                                            v-for="item in regCityList"
                                                            :value="item.secondCode"
                                                            :label="item.secondaryListName"
                                                            :key="item.secondCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <el-input v-model="companyToPAInfo.regAddr" style="width: 344px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow addressRow">
                                                <el-form-item label="经营地址：" :required="true">
                                                    <Select
                                                        v-model="companyToPAInfo.bizProvCode"
                                                        :teleported="false"
                                                        style="width: 120px"
                                                        @change="selectBizProvince"
                                                    >
                                                        <Option
                                                            v-for="item in provinceList"
                                                            :value="item.primaryCode"
                                                            :label="item.primaryName"
                                                            :key="item.primaryCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <Select v-model="companyToPAInfo.bizCityCode" :teleported="false" style="width: 120px">
                                                        <Option
                                                            v-for="item in bizCityList"
                                                            :value="item.secondCode"
                                                            :label="item.secondaryListName"
                                                            :key="item.secondCode"
                                                            @change="selectBizCity"
                                                        >
                                                        </Option>
                                                    </Select>
                                                    <el-input v-model="companyToPAInfo.bizAddr" style="width: 344px"></el-input>
                                                    <el-checkbox
                                                        v-model="isSameAddress"
                                                        label="与注册地址一致"
                                                        @change="handleSameAddress"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">预约时间</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请补充如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPAInfo"
                                            label-position="right"
                                            label-width="191px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="预约期望时间：" :required="true" prop="appointmentTime">
                                                    <el-date-picker
                                                        v-model="companyToPAInfo.appointmentTime"
                                                        type="date"
                                                        placeholder="请选择预约时间，具体以银行审核为准"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 330px"
                                                        :disabled-date="disabledDateStart"
                                                        @change="dateChange"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_four'">上一步</a>
                                    <a class="button ml-28 solid-button" @click="saveInfo">确认无误</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </ContentSlider>
        </div>
        <PreOpenResDialog v-model="openResultDialog" bank="平安银行" @close="toMain" />
    </div>
</template>

<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import { ref, watch, reactive } from "vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import TopStep from "./TopStep.vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { type IBaseCompanyInfo, PACompanyInfoModel } from "../types";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import PreOpenResDialog from "./PreOpenResDialog.vue";
import {
    disabledDateStart,
    isBeforeToday,
    checkLegalPersonName,
    cancelConfirm,
    checkPAStepThree,
    checkPAStepFour,
    checkCompanyInfo,
} from "../utils";

const props = defineProps({
    systemType: { type: Number, required: true },
});
const emit = defineEmits<{
    (
        e: "back",
        data: {
            companyName: string;
            socialCreditCode: string;
            legalPersonName: string;
            legalPhone: string;
            rightLegalPersonName: string;
            accountType: number;
        }
    ): void;
    (e: "success"): void;
}>();
const asId = useAccountSetStore().accountSet?.asId;
const slots = ["step_three", "step_four", "confirm"];
const currentSlot = ref("step_three");
const asNameRef = ref();
const queryParams = reactive({
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
});
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};
const rightLegalPersonName = ref("");
function selectName(item: any) {
    companyToPAInfo.value.companyName = item.value;
    companyToPAInfo.value.unifiedNumber = item.creditCode;
    companyToPAInfo.value.legalName = rightLegalPersonName.value = item.legalPersonName;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}
const handleCompanyBlur = (event: any) => {
    getCompanyDetailApi(1010, decodeURIComponent(event.target.value)).then((res: any) => {
        if (res.state === 1000) {
            rightLegalPersonName.value = res.data?.legalPersonName ?? "";
        }
    });
};
const regCityList = ref<IPACityModel[]>([]); //注册城市列表
const bizCityList = ref<IPACityModel[]>([]); //经营城市列表
const primaryOutletList = ref<IPABranchItem[]>([]);
const secondaryOutletList = ref<IPABranchItem[]>([]);
const provinceList = ref<IPAAreaItem[]>([]); //注册省份列表

const dateChange = (date: any) => {
    if (isBeforeToday(date)) {
        companyToPAInfo.value.appointmentTime = "";
    }
};
const bankOutletsInfoRules = ref({
    province: [{ required: true, message: "请选择开户省份", trigger: "blur" }],
    city: [{ required: true, message: "请选择开户城市", trigger: "blur" }],
    outletName: [{ required: true, message: "请选择选择网点", trigger: "blur" }],
});

const selectRegProvince = (val: string) => {
    companyToPAInfo.value.regCityCode = "";
    companyToPAInfo.value.regCityName = "";
    const regProvinceItem = provinceList.value.find((item: IPAAreaItem) => item.primaryCode === val);
    companyToPAInfo.value.regProvName = regProvinceItem?.primaryName || "";
    regCityList.value = regProvinceItem?.secondaryList || [];
};
const selectRegCity = (val: string) => {
    const regCityItem = regCityList.value.find((item: IPACityModel) => item.secondCode === val);
    companyToPAInfo.value.regCityName = regCityItem?.secondaryListName || "";
};
const selectBizProvince = (val: string) => {
    companyToPAInfo.value.bizCityCode = "";
    companyToPAInfo.value.bizCityName = "";
    const bizProvinceItem = provinceList.value.find((item: IPAAreaItem) => item.primaryCode === val);
    companyToPAInfo.value.bizProvName = bizProvinceItem?.primaryName || "";
    bizCityList.value = bizProvinceItem?.secondaryList || [];
};
const selectBizCity = (val: string) => {
    const bizCityItem = bizCityList.value.find((item: IPACityModel) => item.secondCode === val);
    companyToPAInfo.value.bizCityName = bizCityItem?.secondaryListName || "";
};

const selectPrimaryOutlet = (val: string) => {
    const primaryItem = primaryOutletList.value.find((item: IPABranchItem) => item.branchNo === val);
    companyToPAInfo.value.branchCityNo = primaryItem?.branchNo || "";
    companyToPAInfo.value.branchNo = "";
    companyToPAInfo.value.branchAddr = "";
    companyToPAInfo.value.branchTel = "";
    companyToPAInfo.value.branchName = "";
    getPABranch(val);
};
const selectSecondaryOutlet = (val: string) => {
    const outletItem = secondaryOutletList.value.find((item: IPABranchItem) => item.branchNo === val);
    companyToPAInfo.value.branchAddr = outletItem?.branchAddr || "";
    companyToPAInfo.value.branchTel = outletItem?.branchTel || "";
    companyToPAInfo.value.branchName = outletItem?.branchName || "";
};
//平安银行网点信息
interface IPACityModel {
    secondCode: string; //城市编码
    secondaryListName: string; //城市名称
}
interface IPAAreaItem {
    primaryCode: string; //省编码
    primaryName: string; //省名称
    secondaryList: IPACityModel[]; //城市列表
}
interface IPABranchItem {
    branchNo: string; //机构号
    branchName: string; //机构名称
    branchTel: string; //机构电话
    branchAddr: string; //机构地址
}

const getPABranch = (branchNo: string = "") => {
    request({
        url: window.preOpenBankUrl + `/api/PAPreOpen/QueryBranch?branchNo=${branchNo}`,
        method: "get",
    }).then((res: IResponseModel<IPABranchItem[]>) => {
        if (res.state === 1000) {
            if (branchNo === "") {
                primaryOutletList.value = res.data;
            } else {
                secondaryOutletList.value = res.data;
            }
        }
    });
};

const back = () => {
    cancelConfirm().then((r) => {
        if (r) {
            emit("back", {
                companyName: companyToPAInfo.value.companyName,
                socialCreditCode: companyToPAInfo.value.unifiedNumber,
                legalPersonName: companyToPAInfo.value.legalName,
                legalPhone: companyToPAInfo.value.legalMobile,
                rightLegalPersonName: rightLegalPersonName.value,
                accountType: companyToPAInfo.value.accountType,
            });
            saveCancelInfo();
        }
    });
};
const saveCancelInfo = () => {
    const data = {
        ...getPABankParams(),
    } as any;
    data.content.branchCityNo = companyToPAInfo.value.branchCityNo;
    request({
        url: window.preOpenBankUrl + `/api/PAPreOpen/Save`,
        method: "post",
        data,
    });
};
const toStepFour = () => {
    if (!checkPAStepThree(companyToPAInfo.value)) return;
    currentSlot.value = "step_four";
};

//步骤三

//步骤四 补全公司以及申请人信息
const companyToPAInfo = ref(new PACompanyInfoModel());

const isSameAddress = ref(false);
const operateInfoRules = ref({
    appointmentTime: [{ required: true, message: "请选择预约期望时间", trigger: "blur" }],
    companyName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
    unifiedNumber: [{ required: true, message: "请输入统一社会信用代码", trigger: "blur" }],
    legalName: [{ required: true, message: "请输入法人姓名", trigger: "blur" }],
    legalMobile: [{ required: true, message: "请输入法人手机号码", trigger: "blur" }],
});
//查询地区
const queryPAArea = (primaryCode: string = "") => {
    request({
        url: window.preOpenBankUrl + `/api/PAPreOpen/QueryArea?primaryCode=${primaryCode}`,
        method: "get",
    }).then((res: IResponseModel<IPAAreaItem[]>) => {
        if (res.state === 1000) {
            provinceList.value = res.data;
            if (companyToPAInfo.value.regProvCode) {
                const regProvinceItem = provinceList.value.find(
                    (item: IPAAreaItem) => item.primaryCode === companyToPAInfo.value.regProvCode
                );
                regCityList.value = regProvinceItem?.secondaryList || [];
            }
            if (companyToPAInfo.value.bizProvCode) {
                const bizProvinceItem = provinceList.value.find(
                    (item: IPAAreaItem) => item.primaryCode === companyToPAInfo.value.bizProvCode
                );
                bizCityList.value = bizProvinceItem?.secondaryList || [];
            }
        }
    });
};
const handleSameAddress = (val: any) => {
    if (val) {
        companyToPAInfo.value.bizProvCode = companyToPAInfo.value.regProvCode;
        companyToPAInfo.value.bizProvName = companyToPAInfo.value.regProvName;
        companyToPAInfo.value.bizAddr = companyToPAInfo.value.regAddr;
        bizCityList.value = regCityList.value;
        companyToPAInfo.value.bizCityCode = companyToPAInfo.value.regCityCode;
        companyToPAInfo.value.bizCityName = companyToPAInfo.value.regCityName;
    }
};

watch(
    [
        () => companyToPAInfo.value.regProvCode,
        () => companyToPAInfo.value.regCityCode,
        () => companyToPAInfo.value.regAddr,
        () => companyToPAInfo.value.bizProvCode,
        () => companyToPAInfo.value.bizCityCode,
        () => companyToPAInfo.value.bizAddr,
    ],
    () => {
        const { regProvCode, regCityCode, regAddr, bizProvCode, bizCityCode, bizAddr } = companyToPAInfo.value;
        const sameflag = regProvCode === bizProvCode && regCityCode === bizCityCode && regAddr === bizAddr;
        isSameAddress.value =
            !(regProvCode === "" && regCityCode === "" && regAddr === "" && bizProvCode === "" && bizCityCode === "" && bizAddr === "") &&
            sameflag;
    }
);
const toCofirmInfo = () => {
    if (!checkPAStepFour(companyToPAInfo.value)) return;
    currentSlot.value = "confirm";
};

//确认预约信息
const isCanToNext = ref(true);
const handleNameBlur = () => {
    if (!checkLegalPersonName(companyToPAInfo.value.legalName, rightLegalPersonName.value)) {
        isCanToNext.value = false;
    }
    const timer = setTimeout(() => {
        isCanToNext.value = true;
        clearTimeout(timer);
    }, 200);
};
const openResultDialog = ref(false);
let canClickSaveInfo = true;
const saveInfo = () => {
    if (!isCanToNext.value) return; //法人姓名失焦校验失败防止提交校验2次提示
    if (!canClickSaveInfo) return;
    if (!checkAllInfo()) return;
    if (!checkLegalPersonName(companyToPAInfo.value.legalName, rightLegalPersonName.value)) return;
    canClickSaveInfo = false;
    const data = {
        ...getPABankParams(),
    };
    request({
        url: window.preOpenBankUrl + "/api/PAPreOpen/Open",
        method: "post",
        data,
    })
        .then((res: any) => {
            if (res.state === 1000) {
                openResultDialog.value = true;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .finally(() => {
            canClickSaveInfo = true;
        });
};

const toMain = () => {
    currentSlot.value = "step_three";
    resetInfo();
    emit("success");
};
const getPABankParams = () => {
    const content = JSON.parse(JSON.stringify(companyToPAInfo.value));
    return {
        id: "",
        asId: asId + "",
        system: props.systemType,
        content,
    };
};

const initBaseCompanyInfo = (data: IBaseCompanyInfo) => {
    const { companyName, socialCreditCode, legalPersonName, legalPhone, accountType, rightLegalPersonName: rightName } = data;
    companyToPAInfo.value.companyName = companyName;
    companyToPAInfo.value.unifiedNumber = socialCreditCode;
    companyToPAInfo.value.legalName = legalPersonName;
    rightLegalPersonName.value = rightName;
    companyToPAInfo.value.legalMobile = legalPhone;
    companyToPAInfo.value.accountType = accountType;
};

const initCancelPAInfo = (data: any) => {
    companyToPAInfo.value = Object.assign(companyToPAInfo.value, data);
    companyToPAInfo.value.branchCityNo && getPABranch(companyToPAInfo.value.branchCityNo);
};

const resetInfo = () => {
    companyToPAInfo.value = new PACompanyInfoModel();
    rightLegalPersonName.value = "";
};

const checkAllInfo = () => {
    return checkPAStepThree(companyToPAInfo.value) && checkCompanyInfo(companyToPAInfo.value) && checkPAStepFour(companyToPAInfo.value);
};

defineExpose({
    getPABranch,
    queryPAArea,
    initBaseCompanyInfo,
    resetInfo,
    initCancelPAInfo,
    saveCancelInfo,
});
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
