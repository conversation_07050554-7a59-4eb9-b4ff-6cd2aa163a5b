<template>
    <el-dialog
        v-model="batchAddAsubsDialogShow"
        title="选择科目"
        width="798px"
        class="batch-add-asubs-dialog custom-confirm dialogDrag"
        @close="cancelDialog"
    >
        <div class="dialog-content" v-dialogDrag>
            <div class="batch-add-asubs-dialog-container">
                <div class="main-top">
                    <SearchInfo
                        :width="220"
                        :height="30"
                        @change-value="search"
                        :placeholder="'输入编码或名称搜索'"
                        @search="search"
                    ></SearchInfo>
                    <span class="ml-10">科目类别：</span>
                    <el-select 
                        v-model="activeName" 
                        style="width: 100px" 
                        @change="updateTableView"
                        :filterable="true"
                        :filter-method="tabListFilterMethod"
                    >
                        <el-option 
                            v-for="item in showTabList" 
                            :label="item.label" 
                            :value="item.asubType" 
                            :key="item.name"
                        ></el-option>
                    </el-select>
                    <el-checkbox class="ml-10" v-model="checkChildren" label="同步选择下级科目"></el-checkbox>
                </div>
                <div class="left-container">
                    <div class="asub-table">
                        <VirtualTable
                            class="subject-table"
                            ref="accountSubjectTableRef"
                            :data="tableData"
                            :columns="Columns"
                            :fit="true"
                            height="280"
                            row-key="asubId"
                            :virtual-table="true"
                            :minVirtualScrollLines="100"
                            :scrollbar-show="true"
                            :highlight-current-row="false"
                            :show-overflow-tooltip="true"
                            @row-click="tableRowClick"
                            :tableName="setModule"
                        >
                            <template #selection>
                                <el-table-column width="36px" align="center" props="isChecked" :resizable="false">
                                    <template #header>
                                        <el-checkbox v-model="selectAllChecked" @change="updateRightTableDataAll(selectAllChecked)" />
                                    </template>
                                    <template #default="scope">
                                        <el-checkbox
                                            v-model="scope.row.isChecked"
                                            @click.stop
                                            @change="updateRightTableData(scope.row, scope.row.isChecked)"
                                        />
                                    </template>
                                </el-table-column>
                            </template>
                        </VirtualTable>
                    </div>
                </div>
                <div class="right-container">
                    <Table
                        class="subject-table-right"
                        :data="tableRightList"
                        :columns="rightColumn"
                        height="280"
                        :scrollbar-show="true"
                        :highlight-current-row="false"
                        @scroll="handleRightTableScroll"
                    >
                        <template #checkedList>
                            <el-table-column
                                :label="'已选择的科目(' + tableRightList.length + '个)'"
                                width="auto"
                                align="left"
                                header-align="center"
                                :show-overflow-tooltip="false"
                                :resizable="false"
                            >
                                <template #header>
                                    <div class="table-header">
                                        <span>{{ "已选(" + tableRightList.length + "个)" }}</span>
                                        <a class="link" @click="clearSelection">清空</a>
                                    </div>
                                </template>
                                <template #default="scope">
                                    <ToolTip
                                        :content="scope.row.asubCode + ' ' + scope.row.asubName"
                                        :line-clamp="1"
                                        :teleported="true"
                                        placement="right"
                                        :offset="18"
                                        :fontSize="12"
                                        :maxWidth="163"
                                        popperClass="pivot-asub-popper"
                                    >
                                        <div>{{ scope.row.asubCode }}&nbsp;{{ scope.row.asubName }}</div>
                                    </ToolTip>
                                    <img
                                        src="@/assets/Statements/remove-btn.png"
                                        class="delete-btn"
                                        @click="removeCheckAsub(scope.row, scope.row.asubId)"
                                    />
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="buttons erp-buttons">
                <a class="button solid-button mr-10" @click="asubAddSure">确定</a>
                <a class="button" @click="cancelDialog">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed, toRef, watchEffect, nextTick, onMounted } from "vue";
import Table from "@/components/Table/index.vue";
import VirtualTable from "@/components/Table/VirtualTable.vue";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITabListItem } from "../types";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import SearchInfo from "@/components/SearchInfo/index.vue";
import { cloneDeep } from "lodash";
import type { IAccountSubjectModelWithChecked } from "@/api/accountSubject";
import { ElNotify } from "@/util/notify";
import { AsubTypeEnum } from "@/views/Settings/AccountSubject/types";
import { getAccountSubjectTabList, asubTypeCode } from "@/views/Settings/AccountSubject/utils";
import ToolTip from "@/components/Tooltip/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "CustomSelSubjectDialog";
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    selectList: {
        type: Array<any>,
        default: () => [],
    },
    position: {
        type: String,
        required: true,
    },
});
const emits = defineEmits(["update:modelValue", "addAsubSure"]);
const accountSubjectStore = useAccountSubjectStore();
const accountSubject = toRef(accountSubjectStore, "accountSubjectList");
let searchInfo = ref("");
const batchAddAsubsDialogShow = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});

const tabList = computed<ITabListItem[]>(() => {
    let arr = getAccountSubjectTabList().map((item) => {
        return {
            label: item.label,
            name: item.name,
            asubType: (asubTypeCode as any)[item.label + "类"],
        };
    });
    arr.unshift({
        label: "全部",
        name: "all",
        asubType: 0,
    });
    return arr;
});
const Columns = ref<IColumnProps[]>([
    { slot: "selection", width: 36, headerAlign: "center", align: "center" },
    { label: "科目编码", prop: "asubCode", minWidth: 45, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'asubCode') },
    { label: "科目名称", prop: "asubName", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'asubName') },
    {
        label: "科目类别",
        prop: "asubType",
        minWidth: 45,
        align: "left",
        headerAlign: "left",
        formatter: (row: any) => {
            return AsubTypeEnum[row.asubType];
        }, 
        width: getColumnWidth(setModule, 'asubType')
    },
    {
        label: "余额方向",
        prop: "direction",
        align: "left",
        minWidth: 40,
        headerAlign: "left",
        formatter: (row: any) => {
            if (row.direction == 1) {
                return "借";
            } else if (row.direction == 2) {
                return "贷";
            } else {
                return "未知";
            }
        }, width: getColumnWidth(setModule, 'direction')
    },
    {
        label: "辅助核算",
        prop: "aatypeNames",
        align: "left",
        minWidth: 45,
        headerAlign: "left",
        resizable: false,
    },
]);

const activeName = ref(0);
const checkChildren = ref(false);
const rightColumn = ref([{ slot: "checkedList" }]);
const accountSubjectTableRef = ref();
const tableRightList = ref<IAccountSubjectModelWithChecked[]>(props.selectList);
let allData = ref<IAccountSubjectModelWithChecked[]>(
    cloneDeep(accountSubject.value.map((item) => ({ ...item, isChecked: props.selectList.some((v: any) => v.asubId === item.asubId) })))
);
let tableData = ref<IAccountSubjectModelWithChecked[]>(allData.value);

watch(
    () => props.selectList,
    () => {
        tableRightList.value = props.selectList;
    },
    { immediate: true }
);

function updateRightTableData(row: IAccountSubjectModelWithChecked, isChecked: boolean) {
    allData.value.find((item) => item.asubId === row.asubId)!.isChecked = isChecked;
    checkChildren.value && isChecked && findChild(row);
    tableRightList.value = allData.value.filter((v) => v.isChecked);
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
}
const selectAllChecked = ref(false);
function updateRightTableDataAll(allCheck: boolean) {
    tableData.value.forEach((v) => (v.isChecked = allCheck));
    accountSubjectTableRef.value?.updateView();
    tableRightList.value = allData.value.filter((v) => v.isChecked);
}

const tableRowClick = (row: IAccountSubjectModelWithChecked) => {
    let selected = tableRightList.value.some((item: IAccountSubjectModelWithChecked) => item.asubId === row.asubId);
    // 上级选中下级自动选中，下级取消，上级不需要取消
    checkChildren.value && !selected && findChild(row);
    allData.value.find((item) => item.asubId === row.asubId)!.isChecked = !selected;
    tableRightList.value = allData.value.filter((v) => v.isChecked);
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
    accountSubjectTableRef.value?.updateView();
};
function findChild(row: IAccountSubjectModelWithChecked) {
    allData.value.forEach((item: IAccountSubjectModelWithChecked, index: number) => {
        if (item.asubCode.startsWith(row.asubCode.trim())) {
            item.isChecked = true;
        }
    });
    nextTick(() => {
        accountSubjectTableRef.value?.updateView();
    });
}
function findParent(cParentId: number) {
    if (cParentId === 0) return;
    allData.value.forEach((item: IAccountSubjectModelWithChecked) => {
        if (item.asubId === cParentId) {
            item.isChecked = false;
            findParent(item.parentId);
        }
    });
    nextTick(() => {
        accountSubjectTableRef.value?.updateView();
    });
}

const removeCheckAsub = (row: IAccountSubjectModelWithChecked, asubId: number) => {
    allData.value.find((item) => item.asubId === row.asubId)!.isChecked = false;
    tableRightList.value = tableRightList.value.filter((v: IAccountSubjectModelWithChecked) => v.asubId !== asubId);
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
    accountSubjectTableRef.value?.updateView();
};
const asubAddSure = () => {
    batchAddAsubsDialogShow.value = false;
    if (!tableRightList.value.length) {
        ElNotify({
            type: "warning",
            message: `${props.position}维度科目明细不能为空，请选择科目。`,
        });
    }
    emits("addAsubSure", tableRightList.value);
    resetDialog();
};
const cancelDialog = () => {
    batchAddAsubsDialogShow.value = false;
    if (!tableRightList.value.length && !props.selectList.length) {
        ElNotify({
            type: "warning",
            message: `${props.position}维度科目明细不能为空，请选择科目。`,
        });
    }
    resetDialog();
};
const resetDialog = () => {
    activeName.value = 0;
    searchInfo.value = "";
    allData.value.forEach((v) => {
        v.isChecked = false;
    });
    tableRightList.value = [];
};
const clearSelection = () => {
    allData.value.forEach((v) => {
        v.isChecked = false;
    });
    tableRightList.value = [];
    selectAllChecked.value = false;
    accountSubjectTableRef.value?.updateView();
};
const handleRightTableScroll = () => {
    let poppers = document.querySelectorAll(".el-popper.pivot-asub-popper");
    if (poppers.length > 0) {
        for (let i = 0; i < poppers.length; i++) {
            (poppers[i] as HTMLElement).style.display = "none";
        }
    }
};
const search = (data: string) => {
    searchInfo.value = data;
    updateTableView();
};
function filterData() {
    if (activeName.value === 0 && !searchInfo.value.trim()) {
        tableData.value = allData.value;
    } else {
        tableData.value = allData.value.filter((v: IAccountSubjectModelWithChecked) => {
            return (
                (activeName.value !== 0 ? v.asubType === activeName.value : true) &&
                (searchInfo.value ? v.asubAAName.includes(searchInfo.value) || v.asubCode.includes(searchInfo.value) : true)
            );
        });
    }
    selectAllChecked.value = tableData.value.every((v) => v.isChecked);
    accountSubjectTableRef.value?.resetVirtualTableState();
    accountSubjectTableRef.value?.updateView();
}
function updateTableView() {
    filterData();
    nextTick(() => {
        accountSubjectTableRef.value?.updateView();
    });
}
watch(
    batchAddAsubsDialogShow,
    () => {
        nextTick(() => {
            if (batchAddAsubsDialogShow.value) {
                accountSubjectTableRef.value?.updateView();
                selectAllChecked.value = tableData.value.length === tableRightList.value.length;
            }
        });
    },
    { immediate: true }
);

const showTabList = ref<ITabListItem[]>([]);
watchEffect(() => { 
    showTabList.value = JSON.parse(JSON.stringify(tabList.value));
});
function tabListFilterMethod(value: string) {
    showTabList.value = commonFilterMethod(value, tabList.value, 'label');
}
</script>

<style lang="less" scoped>
.batch-add-asubs-dialog {
    .batch-add-asubs-dialog-container {
        .main-top {
            padding-left: 15px;
        }
        margin-bottom: 15px;
        .left-container {
            display: inline-block;
            vertical-align: top;
            margin-right: 16px;
            text-align: left;
            width: 546px;
            .left-top {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .asub-tabs {
                font-size: 0;
                overflow: hidden;
                border-radius: 2px;
                display: inline-block;
                vertical-align: top;
                &.el-radio-group {
                    margin-bottom: 0 !important;
                }
            }
            .asub-table {
                .table.subject-table {
                    border-bottom: none;
                }
                :deep(.el-table__inner-wrapper) {
                    height: 278px !important;
                    border-bottom: 1px solid var(--el-border-color-lighter);
                }
            }
        }
        .right-container {
            display: inline-block;
            vertical-align: top;
            text-align: left;
            width: 204px;
            height: 280px;
            // border: 1px solid var(--table-border-color);
            box-sizing: border-box;
            .subject-table-right {
                :deep(.el-table__empty-block) {
                    width: 100% !important;
                    min-height: 0;
                }
                :deep(.cell) {
                    display: flex;
                    align-items: center;
                    height: 100%;
                    .span_wrap {
                        flex: 1;
                    }
                    .table-header {
                        display: flex;
                        justify-content: space-between;
                    }
                    & div {
                        width: 163px;
                        height: 100%;
                        line-height: 40px;
                        display: inline-block;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        vertical-align: top;
                    }
                    .delete-btn {
                        background-size: 100%;
                        background-repeat: no-repeat;
                        width: 12px;
                        height: 12px;
                        float: right;
                        cursor: pointer;
                        margin: 0 5px;
                        text-align: center;
                    }
                }
            }
        }
    }
}
</style>
