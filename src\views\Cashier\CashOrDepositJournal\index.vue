<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">{{ journalType === "1010" ? "现金" : "银行" }}日记账</div>
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <div class="cd-accountList mr-10">
                                <Select
                                    v-model="cdAccount"
                                    noMatchText="无数据"
                                    :teleported="false"
                                    :filterable="true"
                                    :bottom-html="newCDAccountHtml"
                                    @bottom-click="newCdAccount"
                                    :filter-method="AccountMethod"
                                >
                                    <ElOption
                                        v-for="item in showAccountList"
                                        :key="item.ac_id"
                                        :value="item.ac_id"
                                        :label="item.label"
                                        :className="item.ac_id === 'all' ? 'all-option' : ''"
                                    >
                                        <template v-if="item.ac_id === 'all'">
                                            <div>全部账户</div>
                                            <div class="all-option-switch">
                                                <span @click.stop="">
                                                    <el-switch v-model="showDisabled" :active-value="true" :inactive-value="false"></el-switch>
                                                </span>
                                                显示禁用账户
                                            </div>
                                        </template>
                                    </ElOption>
                                </Select>
                            </div>
                            <span class="tip-title" v-show="isErp">日期：</span>
                            <SearchInfoContainer ref="containerRef" @mouseenter="handleMouseEnter">
                                <template v-slot:title>{{ currentPeriodInfo }}</template>
                                <div class="line-item first-item input">
                                    <div class="line-item-title">日期：</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.startPid"
                                            v-model:endPid="searchInfo.endPid"
                                            v-model:periodInfo="periodInfo"
                                            :clearable="true"
                                            :disabled-date-start="disabledDateStart"
                                            :disabled-date-end="disabledDateEnd"
                                            @user-change="handleUserChangeDate"
                                            @blur="forcedShutdown = false"
                                            @focus="forcedShutdown = true"
                                        />
                                    </div>
                                </div>
                                <template v-if="!isErp">
                                    <div class="line-item input">
                                        <div class="line-item-title">凭证日期:</div>
                                        <div class="line-item-field">
                                            <DatePicker
                                                v-model:startPid="searchInfo.startVDate"
                                                v-model:endPid="searchInfo.endVDate"
                                                :disabled-date-start="disabledStartVDate"
                                                :disabled-date-end="disabledEndVDate"
                                                @blur="forcedShutdown = false"
                                                @focus="forcedShutdown = true"
                                            />
                                        </div>
                                    </div>
                                    <div class="line-item input">
                                        <div class="line-item-title">凭证字号：</div>
                                        <div class="line-item-field">
                                            <div class="vgid-line">
                                                <el-select
                                                    v-model="searchInfo.vgId"
                                                    :teleported="false"
                                                    style="width: 90px"
                                                    @change="handleVgIdChange"
                                                    @blur="forcedShutdown = false"
                                                    @focus="forcedShutdown = true"
                                                >
                                                    <el-option :value="0" label="请选择">请选择</el-option>
                                                    <el-option :value="1" label="全部">全部</el-option>
                                                    <el-option
                                                        v-for="item in voucherGroup"
                                                        :value="item.id"
                                                        :key="item.id"
                                                        :label="item.name"
                                                    ></el-option>
                                                </el-select>
                                                <el-input
                                                    clearable
                                                    type="text"
                                                    class="ml-10"
                                                    v-model="searchInfo.startVNum"
                                                    :disabled="!searchInfo.vgId"
                                                    @input="startVNumLimit"
                                                ></el-input>
                                                <span style="padding: 0 8px; line-height: 30px">至</span>
                                                <el-input
                                                    clearable
                                                    type="text"
                                                    v-model="searchInfo.endVNum"
                                                    :disabled="!searchInfo.vgId"
                                                    @input="endVNumLimit"
                                                ></el-input>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <div class="line-item input">
                                    <div class="line-item-title">收支类别：</div>
                                    <div class="line-item-field">
                                        <VirtualSelectCheckbox
                                            :options="ieTypeOptions"
                                            :use-el-icon="true"
                                            width="300px"
                                            v-model:selectedList="searchInfo.IE_TYPE"
                                            class="item"
                                            v-model:forced-shutdown="forcedShutdown"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">结算方式：</div>
                                    <div class="line-item-field">
                                        <VirtualSelectCheckbox
                                            :options="paymentMethodListOptions"
                                            :use-el-icon="true"
                                            width="300px"
                                            v-model:selectedList="searchInfo.PAYMENT_METHOD"
                                            class="item"
                                            v-model:forced-shutdown="forcedShutdown"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">生成凭证：</div>
                                    <div class="line-item-field">
                                        <Select 
                                            v-model="searchInfo.HasVoucher" 
                                            class="item" 
                                            :teleported="false" 
                                            :fit-input-width="true"
                                            @blur="forcedShutdown = false"
                                            @focus="forcedShutdown = true"
                                        >
                                            <ElOption value="-1" label="全部">全部</ElOption>
                                            <ElOption value="1" label="是">是</ElOption>
                                            <ElOption value="0" label="否">否</ElOption>
                                        </Select>
                                    </div>
                                </div>
                                <div class="line-item input" v-show="cdAccount === 'all'">
                                    <div class="line-item-title">币别：</div>
                                    <div class="line-item-field">
                                        <Select
                                            v-model="searchInfo.currencyType"
                                            class="item"
                                            :teleported="false"
                                            :fit-input-width="true"
                                            :filterable="true"
                                            @blur="forcedShutdown = false"
                                            @focus="forcedShutdown = true"
                                        >
                                            <ElOption value="0" label="全部"></ElOption>
                                            <ElOption v-for="item in currencyList" :key="item.id" :value="item.id" :label="item.name" />
                                        </Select>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">往来单位：</div>
                                    <div class="line-item-field">
                                        <SelectV2
                                            v-model="searchInfo.OPPOSITE_PARTY"
                                            ref="oppoSitePartyRef"
                                            class="item"
                                            placeholder="请选择或输入"
                                            :teleported="false"
                                            :fit-input-width="true"
                                            :filterable="true"
                                            :clearable="true"
                                            :allow-create="allowCreate"
                                            :props="oppositePartyProps"
                                            :options="companyListOptions"
                                            :remote="companyList.length > 0"
                                            :filter-method="oppositeFilter"
                                            @visible-change="oppositeVisibleChange"
                                            :isSuffixIcon="true"
                                        ></SelectV2>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">票据号：</div>
                                    <div class="line-item-field">
                                        <el-input class="my-input-normal" v-model="searchInfo.RECEIPT_NO" clearable />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">项目：</div>
                                    <div class="line-item-field">
                                        <VirtualSelectCheckbox
                                            :options="projectListOptions"
                                            :use-el-icon="true"
                                            width="300px"
                                            v-model:selectedList="searchInfo.PROJECT"
                                            class="item"
                                            v-model:forced-shutdown="forcedShutdown"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">部门：</div>
                                    <div class="line-item-field">
                                        <VirtualSelectCheckbox
                                            :options="departmentListOptions"
                                            :use-el-icon="true"
                                            width="300px"
                                            v-model:selectedList="searchInfo.DEPARTMENT"
                                            class="item"
                                            v-model:forced-shutdown="forcedShutdown"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">摘要：</div>
                                    <div class="line-item-field">
                                        <el-input class="my-input-normal" v-model="searchInfo.DESCRIPTION" clearable />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">备注：</div>
                                    <div class="line-item-field">
                                        <el-input class="my-input-normal" v-model="searchInfo.NOTE" clearable />
                                    </div>
                                </div>
                                <div class="line-item input" v-if="isErp">
                                    <div class="line-item-title">交易流水：</div>
                                    <div class="line-item-field">
                                        <el-input class="my-input-normal" v-model="searchInfo.TRAN_NO" clearable />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">金额：</div>
                                    <div class="line-item-field">
                                        <el-input
                                            clearable
                                            type="number"
                                            title=""
                                            class="my-input-small"
                                            v-model="searchInfo.Amount_s"
                                            v-decimal-limit
                                            @change="(val: string) => (searchInfo.Amount_s = val)"
                                        />
                                        <span class="ml-10 mr-10 w-16">至</span>
                                        <el-input
                                            clearable
                                            type="number"
                                            title=""
                                            class="my-input-small"
                                            v-model="searchInfo.Amount_e"
                                            v-decimal-limit
                                            @change="(val: string) => (searchInfo.Amount_e = val)"
                                        />
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button solid-button" @click="handleConfirmSearch(true)">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                        </div>
                        <div class="main-tool-right">
                            <el-checkbox style="margin-right: 10px" label="显示全部" v-model="showAll"></el-checkbox>
                            <JournalOperate
                                ref="journalOperateRef"
                                :jType="journalType"
                                :selection-list="selectionList"
                                :cdAccount="cdAccount"
                                :cdAccountList="cdAccountList"
                                :allCdAccountList="allCdAccountList"
                                :checkoutDate="checkoutDate"
                                :payList="payList"
                                :getBaseParams="getBaseParams"
                                :clearSelection="clearSelection"
                                @show-generate-voucher="handleToGenerateVoucher"
                                @show-journal-template="handleToJournalTemplate"
                                @show-journal-settings="handleToJournalSettings"
                                @search="handleSearch"
                                @refresh-ietype="handleGetIETypeList"
                                @edit-date="handleEditDate"
                                @change-acid="handleChangeAcid"
                                @change-show-disabled="handleChangeShowDisabled"
                                @import-success="handleImportSuccess"
                            />
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <JournalTable
                            ref="journalTableRef"
                            v-model:loading="loading"
                            v-model:is-edit="isEdit"
                            :table-data="tableData"
                            :show-all-list="showAllList"
                            :use-fc="useFc"
                            :hidden-amount-fc="hiddenAmountFc"
                            :pay-list="payList"
                            :project-list="filterProjectList"
                            :disabledProjectList="disabledProjectList"
                            :department-list="departmentList"
                            :payment-method-list="paymentMethodList"
                            :cdAccount="cdAccount"
                            :cd-account-list="cdAccountList"
                            :startDate="startDate"
                            :checkDate="checkDate"
                            :journalType="journalType"
                            :initAmountCanEdit="initAmountCanEdit"
                            :accountListLength="accountListLength"
                            :allAccountListLength="allAccountListLength"
                            :getSelectionList="getSelectionList"
                            :wordCutList="wordCutList"
                            :trySyncCompany="trySyncCompany"
                            @selection-change="handleSelectionChange"
                            @submit-edit="handleSubmit"
                            @delete-row="deleteRow"
                            @print-row="printRow"
                            @insert-new-row="insertNewRow"
                            @copy-row="copyRow"
                            @load-data="handleLoadData"
                            @change-row-date="handleChangeDate"
                            @save-ietype="handleNewIETypeSuccess"
                            @save-project="handleSaveProject"
                            @save-department="handleSaveDepartment"
                            @save-pay-method="handleSavePayMethod"
                            @receipt-row="receiptRow"
                            :searchInfo="searchInfo"
                            :searchOptions="searchOptions"
                            :filterSearchInfo="filterSearchInfo"
                            @filterSearch="filterSearch"
                            @move-drop="moveDrop"
                        />
                    </div>
                </div>
            </template>
            <template #voucher>
                <div class="slot-content">
                    <JournalGenerateVoucher
                        :check-date="checkDate"
                        :open-gen-voucher="openGenVoucher"
                        :cancel-gen-voucher="handleBackToJournal"
                        :gen-voucher-save-success="genVoucherSaveSuccess"
                        @no-template="genVoucherNoTemplate"
                        ref="journalGenerateVoucherRef"
                    ></JournalGenerateVoucher>
                </div>
            </template>
            <template #setting>
                <div class="slot-content align-center" :style="isErp ? '' : 'height: 100%;'">
                    <div class="slot-title">{{ journalType === "1010" ? "现金日记账" : "银行日记账" }}</div>
                    <div class="slot-mini-content voucher-setting">
                        <GenerateVoucherSetting
                            ref="generateVoucherSettingRef"
                            :ie-type-list="IETypeList"
                            :j-type="Number(journalType)"
                            @goBackSettings="handleBackToJournal"
                        ></GenerateVoucherSetting>
                    </div>
                </div>
            </template>
            <template #checkVoucher>
                <div class="slot-content">
                    <CheckVoucher
                        ref="checkVoucherRef"
                        :to-voucher-about="toVoucherAbout"
                        :back="() => (currentSlot = 'main')"
                        :reloadData="genVoucherSaveSuccess"
                    />
                </div>
            </template>
        </ContentSlider>
    </div>
    <el-dialog title="提示" width="440px" center v-model="editVoucherShow" class="custom-confirm dialogDrag" @closed="resetConfirmParams">
        <div class="editVoucherShow-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="content-main">
                <div style="text-align: left; margin-left: 5px">日记账修改成功！</div>
                <div style="margin-top: 5px; text-align: left; margin-left: 5px">是否需手工修改已关联的凭证？</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleConfirmSuccess">确定</a>
                <a class="button ml-10" @click="handleComfirmCancal">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="提示" width="600px" center v-model="fcAccountHistoryDataSync" :show-close="false" class="custom-confirm dialogDrag">
        <div class="fc-account-history-data-sync-content" v-dialogDrag>
            <div class="data-sync-main">
                <div class="data-sync-title">
                    当前账户 <span v-html="syncTitle"></span> 请您确认历史日记账数据金额是否是
                    {{
                        syncFcName
                    }}？确认完成后系统会根据币别设置的汇率自动计算本币或原币，同时支持外币核算生成记账凭证功能。如存在多个外币账户，请切换账户逐一确认。
                </div>
                <div class="data-sync-tip">
                    <img src="@/assets/Icons/warn.png" />
                    <div>注意：{{ accountSetBackupTips() }}</div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="syncFcData(0)">录入的是{{ syncFcName }}</a>
                <a class="button ml-20" @click="syncFcData(1)">录入的是人民币</a>
            </div>
        </div>
    </el-dialog>
    <AddAccountDialog
        ref="addAccountFormRef"
        v-model="addAccountDialogShow"
        :j_type="Number(journalType)"
        :currencyList="currencyList"
        :hasIconTip="true"
        @save-success="handleNewAccountSuccess"
        :modalClass="'modal-class'"
    />
    <UploadFileDialog :readonly="readonly" ref="uploadFileDialogRef" @save="saveReceipt" />
    <GenerateVoucherSettingDialog
        :settingType="voucherSettingsType"
        ref="generateVoucherSettingDialogRef"
        v-model="voucherSettingDialogShow"
        :width="680"
    />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick, onUnmounted, toRef, computed, onActivated, provide, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalWindowOpenPage, globalPrint, globalPostPrint, tryClearCustomUrlParams } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { tryUpdateWordCut, getWordCutList, type IWordCut, WordCutCategory } from "@/util/wordCut";
import { checkPermission } from "@/util/permission";
import { FullScreenLoading } from "@/util/fullScreenLoading";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { getCookie, setCookie } from "@/util/cookie";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import { erpCreateTab } from "@/util/erpUtils";
import { getGlobalToken } from "@/util/baseInfo";
import { cloneDeep } from "lodash";
import { ElLoading, dayjs } from "element-plus";
import { accountSetBackupTips } from "@/util/showTips";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { editConfirm } from "@/util/editConfirm";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import { AttachFileCategory } from "@/views/ERecord/types";
import {
    formatDate,
    initRowData,
    splitATag,
    checkFCAccountExistsHistoryData,
    syncFCAccountHistoryData,
    isNumericString,
    formatRate,
    extractLineSN,
    ieTypeKey,
    payMethodKey,
    checkCanSave,
    getReplaceRow,
    isMoreFc,
    getLineSnAndPerfix,
    getCombineSubmitParams,
    getDifferenceAmount,
    getLineSnNameWithAnchorTag,
} from "./utils";

import { Option } from "@/components/SelectCheckbox/types";
import { CashIEType, CashAAType, selectKey } from "./types";
import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { 
    IPaymentMethodItem, 
    IPayMethod, 
    ITableItem, 
    IRowItemEditInfo, 
    IJournalBack, 
    IDifferenceInfo, 
    ISearchInfo,
    IFSearchItem,
} from "./types";
import type { ICDAccountItem } from "@/views/Cashier/components/types";
import type { IIETypeItem } from "@/views/Cashier/type";
import type { IJournalDate } from "@/views/Cashier/type";
import type { IVoucherSetting } from "@/components/Dialog/GenerateVoucherSetting/type";
import type { IAssistingAccount } from "@/api/assistingAccounting";

import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import JournalOperate from "./components/JournalOperate.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import GenerateVoucherSetting from "./components/GenerateVoucherSetting.vue";
import Select from "@/components/Select/index.vue";
import AddAccountDialog from "@/views/Cashier/components/AddAccountDialog.vue";
import JournalGenerateVoucher from "./components/JournalGenerateVoucher.vue";
import JournalTable from "./components/JournalTable.vue";
import CheckVoucher from "./components/CheckVoucher.vue";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import ElOption from "@/components/Option/index.vue";
import GenerateVoucherSettingDialog from "@/components/Dialog/GenerateVoucherSetting/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";
import { getCDAccountApi } from "@/views/Cashier/CDAccount/utils";
import { useCurrencyStore } from "@/store/modules/currencyList";

const { isEqual } = getGlobalLodash();
const props = defineProps<{ journalType: "1010" | "1020" }>();
const journalType = ref<"1010" | "1020">(props.journalType);
const voucherSettingsType = computed(() => (journalType.value === "1010" ? "cashJournal" : "depositJournal"));
const forcedShutdown = ref(false);

const isErp = ref(window.isErp);
const route = useRoute();
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = computed(() => voucherGroupStore.voucherGroupList);

const oppositePartyProps = {
    value: "value",
    label: "text",
};

const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const journalTableRef = ref<InstanceType<typeof JournalTable>>();
const journalOperateRef = ref<InstanceType<typeof JournalOperate>>();
const addAccountFormRef = ref<InstanceType<typeof AddAccountDialog>>();
const generateVoucherSettingRef = ref<InstanceType<typeof GenerateVoucherSetting>>();
const journalGenerateVoucherRef = ref<InstanceType<typeof JournalGenerateVoucher>>();
const checkVoucherRef = ref<InstanceType<typeof CheckVoucher>>();
const generateVoucherSettingDialogRef = ref<InstanceType<typeof GenerateVoucherSettingDialog>>();
const oppoSitePartyRef = ref<InstanceType<typeof SelectV2>>();

const slots = ["main", "checkVoucher", "voucher", "setting"];
const currentSlot = ref("main");
const searchStart = ref("");
const searchEnd = ref("");
const checkDate = ref("");
const checkoutDate = ref(new Date());
const startDate = ref("");
const cdAccount = ref("");
const cdAccountDisplay = ref("");
const showAll = ref(false);
const showAllList = ref(false);
const searchInfo:ISearchInfo = reactive({
    startPid: "",
    endPid: "",
    startVDate: "",
    endVDate: "",
    vgId: 0,
    startVNum: "",
    endVNum: "",
    IE_TYPE: [] as number[],
    // PAYMENT_METHOD: "",
    PAYMENT_METHOD: [] as number[],
    HasVoucher: "-1",
    OPPOSITE_PARTY: "",
    RECEIPT_NO: "",
    PROJECT: [] as number[],
    // DEPARTMENT: "",
    DEPARTMENT: [] as number[],
    DESCRIPTION: "",
    NOTE: "",
    Amount_s: "",
    Amount_e: "",
    checkDate: "",
    startDate: "",
    currencyType: "0",
    TRAN_NO: "",
});
const filterSearchInfo:IFSearchItem = reactive({
    DESCRIPTION: "",
    NOTE: "",
    RECEIPT_NO: "",
    IE_TYPE: [] as number[],
    PAYMENT_METHOD: [] as number[],
    OPPOSITE_PARTY: "",
    PROJECT: [] as number[],
    DEPARTMENT: [] as number[],
});
function handleMouseEnter() {
    const ietypeSelectRef = journalTableRef.value?.$refs.ietypeSelectRef;
    if (ietypeSelectRef) {
        (ietypeSelectRef as any).blur();
    }
}
function startVNumLimit(e: string) {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.startVNum = val;
}
function endVNumLimit(e: string) {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.endVNum = val;
}
function handleVgIdChange(val: number) {
    if (val === 0) {
        searchInfo.startVNum = "";
        searchInfo.endVNum = "";
    }
}

interface ISelectValue {
    value: string;
    text: string;
}

const companyListOptions = ref<Array<ISelectValue>>([]);
const projectListOptions = ref<Array<Option>>([]);
const departmentListOptions = ref<Array<Option>>([]);
const companyList = ref<Array<ISelectValue>>([]);
const companyListCompare = ref<Array<ISelectValue>>([]); //弹窗选择往来单位时获取OPPOSITE_PARTY_INT
const projectList = ref<Array<IAssistingAccount>>([]);
const filterProjectList = computed(() => projectList.value.filter((item) => item.status === 0));
const disabledProjectList = computed(() => projectList.value.filter((item) => item.status === 1));
const departmentList = toRef(useAssistingAccountingStore(), "departmentList");
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingList");
const preAssistingAccountingList = ref<IAssistingAccount[]>([]); 
function handleInitAssistingAccounting(val: Array<IAssistingAccount>) {
    preAssistingAccountingList.value = JSON.parse(JSON.stringify(val));

    const project = val.filter((item) => item.aatype === CashAAType.Project && item.aaeid > 0);
    const company: Array<{ value: string; text: string }> = [];
    for (let i = 0; i < val.length; i++) {
        if ([10001, 10002, 10003].includes(val[i].aatype)) {
            company.push({ value: val[i].aaeid.toString(), text: val[i].aaname });
        }
    }
    companyListCompare.value = company;

    projectList.value = project;
    projectListOptions.value = projectList.value.map((i) => new Option(Number(i.aaeid), i.aaname));
    searchInfo.PROJECT = projectListOptions.value.map((item) => item.id);

    departmentListOptions.value = departmentList.value.map((i) => new Option(Number(i.aaeid), i.aaname));
    searchInfo.DEPARTMENT = departmentListOptions.value.map((item) => item.id);
}
function trySyncCompany(aaList: Array<{ aaeId: number, aaName: string }>) {
    for (let i = 0; i < aaList.length; i ++) {
        const aaeItem = aaList[i];
        const index = companyListCompare.value.findIndex((item) => item.value === aaeItem.aaeId.toString() && item.text === aaeItem.aaName);
        if (index === -1) {
            companyListCompare.value.push({ value: aaeItem.aaeId.toString(), text: aaeItem.aaName })
        }
    }
}
watch(assistingAccountingList, (val) => { //生成凭证时会触发，只有值内容变了才调用
    if (val.length > 0 && !isEqual(preAssistingAccountingList.value, val)) {
        handleInitAssistingAccounting(val);
    }
});

async function getCompany() {
    await request({ 
        url: "/api/Journal/OppositePartyNames" 
    }).then((res: IResponseModel<Array<any>>) => {
        if (res.state !== 1000) return;
        companyList.value = res.data.map((item) => {
            return {
                value: item,
                text: item,
            }
        });
        companyListOptions.value = JSON.parse(JSON.stringify(companyList.value));
    });
}

const allowCreate = ref(true);
function oppositeFilter(val: string) {
    companyListOptions.value = commonFilterMethod(val, companyList.value, 'text');
}
const oppositeVisibleChange = (visible: boolean) => {
    if (visible) {
        companyListOptions.value = JSON.parse(JSON.stringify(companyList.value));
    }
}
// 银企互联同步业财流水
const syncBusinessFinancialFlow = () => {
    const key = useAccountSetStore().userInfo?.userSn + "-" + getGlobalToken() + "-syncBusinessFinancialFlow";
    if (window.isErp && localStorage.getItem(key) !== dayjs().format("YYYY-MM-DD")) {
        request({ url: "/api/AccMessage?type=1", method: "post" }).then((res: IResponseModel<string[]>) => {
            if (res.state === 1000 && res.data.length) {
                ElNotify({
                    message: res.data[0],
                    type: "warning",
                });
                localStorage.setItem(key, dayjs().format("YYYY-MM-DD"));
            }
        });
    }
};
syncBusinessFinancialFlow();
// 初始化数据
const allCdAccountList = ref<Array<ICDAccountItem>>([]);
const cdAccountList = ref<Array<ICDAccountItem>>([]);
async function handleGetAccountList() {
    await getCDAccountApi(journalType.value).then((res: IResponseModel<Array<ICDAccountItem>>) => {
        if (res.state !== 1000) return;
        allCdAccountList.value = res.data;
        cdAccountList.value = calcCurrentCDAccountList();
    });
}
const allAccountListLength = ref(1);
const accountListLength = ref(1);
function calcCurrentCDAccountList() {
    let list: Array<ICDAccountItem> = [];
    list = showDisabled.value ? allCdAccountList.value : allCdAccountList.value.filter((item) => item.state === "0");
    list.forEach((item) => {
        let result = item.state === "1" ? "（已禁用）" : "";
        item.label = item.ac_no + ' - ' + item.ac_name + result;
    })
    accountListLength.value = list.length;
    allAccountListLength.value = allCdAccountList.value.length;
    return list;
}
function checkHasAccount() {
    if (cdAccountList.value.length === 0) {
        ElConfirm("亲，您尚未设置账户，请设置账户后再进行记账操作", true).then(() => {
            if (isErp.value) {
                erpCreateTab("/Accounts", "银行账户");
            } else {
                globalWindowOpenPage("/Cashier/CDAccount", "账户设置");
            }
        });
        return false;
    }
    return true;
}
function showAllAccount() {
    if (!showDisabled.value) {
        showDisabled.value = true;
        autoShowDisabled = true;
        cdAccountList.value = calcCurrentCDAccountList();
    }
}
function checkNeedChangeAccount() {
    if (route.query.allAcc === "true") {
        cdAccount.value = "all";
        showAllAccount();
        return checkHasAccount();
    } else {
        let cdAccountData = cdAccountList.value[0];
        if (route.query.cdAccount) {
            const item = cdAccountList.value.find((item) => item.ac_id === route.query.cdAccount);
            if (item) {
                cdAccountData = item;
            } else {
                const allListItem = allCdAccountList.value.find((item) => item.ac_id === route.query.cdAccount);
                if (allListItem) {
                    cdAccountData = allListItem;
                    showAllAccount();
                }
            }
        }
        if (!checkHasAccount()) return false;
        cdAccount.value = cdAccountData.ac_id;
        useFc.value = cdAccountData.standard === "1";
        cdAccountDisplay.value = cdAccountData.ac_no + " - " + cdAccountData.ac_name;
        return true;
    }
}
const allIEType = ref<Array<IIETypeItem>>([]);
const IETypeList = ref<Array<IIETypeItem>>([]);
const payList = ref<Array<IPayMethod>>([]);
const ieTypeOptions = ref<Array<Option>>([]);
provide(ieTypeKey, () => allIEType.value);
provide(payMethodKey, () => payList.value);
async function handleGetIETypeList() {
    await request({ url: "/api/IEType/List" }).then((res: IResponseModel<{ rows: Array<IIETypeItem> }>) => {
        if (res.state !== 1000) return;
        allIEType.value = res.data.rows;
        IETypeList.value = res.data.rows.filter((item: any) => !item.haschild);
        payList.value = convertToPayList(IETypeList.value);
        const optionsList = IETypeList.value.map((i) => new Option(Number(i.subkey), i.value2));
        optionsList.push(new Option(-1, "内部转账"));
        ieTypeOptions.value = optionsList;
        searchInfo.IE_TYPE = ieTypeOptions.value.map((item) => item.id);
    });
}
function convertToPayList(list: Array<IIETypeItem>) {
    return list.filter((item) => Number(item.num3) !== 1).map((i) => {
        i.value3 = i?.value3?.replace(/，/g, ",");
        return {
            subkey: i.subkey,
            value1: i.value1,
            value2: i.value2,
            matchKeys: i.value3.split(","),
        };
    });
}
const paymentMethodList = ref<Array<IPaymentMethodItem>>([]);
const paymentMethodListOptions = ref<Array<Option>>([]);
async function handleGetPaymentList() {
    await request({ url: "/api/Payment/List" }).then((res: IResponseModel<{ rows: Array<IPaymentMethodItem> }>) => {
        if (res.state !== 1000) return;
        paymentMethodList.value = res.data.rows;
        paymentMethodListOptions.value = res.data.rows.map((i) => new Option(Number(i.id), i.value));
        searchInfo.PAYMENT_METHOD = paymentMethodListOptions.value.map((item) => item.id);
    });
}
const searchOptions = ref();
watch(
    () => [ieTypeOptions, paymentMethodListOptions, projectListOptions, departmentListOptions, companyList],
    () => {
        searchOptions.value = {
            "IE_TYPE": ieTypeOptions.value,
            "PAYMENT_METHOD": paymentMethodListOptions.value,
            "PROJECT": projectListOptions.value,
            "OPPOSITE_PARTY": companyList.value,
            "DEPARTMENT": departmentListOptions.value,
        }
    },
    {
        immediate: true,
        deep: true,
    }
)

const currencyList = ref<Array<ICurrencyList>>([]);
async function handleGetCurrencyList() {
    await useCurrencyStore().getCurrencyList();
    currencyList.value = [...useCurrencyStore().fcList];
}

// 获取列表数据
const currentPeriodInfo = ref("");
const periodInfo = ref("");
function setInfos() {
    currentPeriodInfo.value = periodInfo.value;
}
let hasLoading = false;
const initAmountCanEdit = ref(false);
const loading = ref(false);
const tableData = ref<Array<ITableItem>>([]);
function checkInitAmountCanEdit() {
    if (tableData.value[0].description === "初始化余额") {
        initAmountCanEdit.value = formatDate(tableData.value[1].cd_date) >= checkDate.value && searchInfo.startPid >= checkDate.value;
    } else {
        initAmountCanEdit.value = true;
    }
}
function getBaseParams() {
    let project = getCommonSelect('PROJECT', projectListOptions.value);
    let department = getCommonSelect('DEPARTMENT', departmentListOptions.value);
    let item = companyList.value.filter((item) => item.value === searchInfo.OPPOSITE_PARTY);
    let opposite = searchInfo.OPPOSITE_PARTY;
    if (item && item[0]) {
        opposite = item[0].text;
    }
    return {
        showAll: showAll.value ? 1 : 0,
        j_type: journalType.value,
        cd_account: cdAccount.value === "all" ? cdAccountList.value.map((item) => item.ac_id).join(",") : cdAccount.value,
        cd_accountdisplay: cdAccountDisplay.value,
        date_s: searchInfo.startPid,
        date_e: searchInfo.endPid,
        startVDate: searchInfo.startVDate ?? "",
        endVDate: searchInfo.endVDate ?? "",
        vgId: searchInfo.vgId,
        startVNum: searchInfo.startVNum,
        endVNum: searchInfo.endVNum,
        ie_type: getCommonSelect('IE_TYPE', ieTypeOptions.value),
        payment_method: getCommonSelect('PAYMENT_METHOD', paymentMethodListOptions.value),
        opposite_party: filterSearchInfo.OPPOSITE_PARTY ? filterSearchInfo.OPPOSITE_PARTY : opposite,
        receipt_no: getQueryVal('RECEIPT_NO'),
        description: getQueryVal('DESCRIPTION'),
        note: getQueryVal('NOTE'),
        amount_s: Number(searchInfo.Amount_s) + "" === "NaN" ? "" : searchInfo.Amount_s,
        amount_e: Number(searchInfo.Amount_e) + "" === "NaN" ? "" : searchInfo.Amount_e,
        project: getStringSelect(project, projectListOptions.value),
        department: getStringSelect(department, departmentListOptions.value),
        hasVoucher: searchInfo.HasVoucher,
        currencyType: cdAccount.value === "all" ? searchInfo.currencyType : "",
        tran_no: searchInfo.TRAN_NO,
    };
}
function getQueryVal(name: string) {
    if (isKeyOfIFSearchItem(name)) {  
        const baseInfo = searchInfo[name];  
        const filterInfo = filterSearchInfo[name];
        return filterInfo ? `${baseInfo}${baseInfo ? '|-|': ''}${filterInfo}` : baseInfo;
    }
    return "";
}
function getCommonSelect(name: keyof IFSearchItem, option: Option[]) {  
    const list1 = searchInfo[name] as number[];  
    const list2 = filterSearchInfo[name] as number[]; 
    if (list2.length > 0) { 
        if (list1.length === option.length && list2.length === option.length) {  
            return "";  
        }  
        const set2 = new Set(list2);  
        const commonList = list1.filter(value => set2.has(value));    
        if (commonList.length) {  
            return commonList.join();  
        } else if (list1.length) {  
            return list1.join();  
        } else {  
            return list2.join();  
        }   
    } else {
        return list1.length === option.length ? "" : list1.join();
    } 
}
function getStringSelect(val: string, options: Option[]): string {  
    const optionMap = new Map<number, string>();  
    options.forEach(option => optionMap.set(option.id, option.name));  
 
    return val.split(",")  
        .map(item => optionMap.get(Number(item)))  
        .filter(name => name !== undefined)  
        .join(",");
} 

function setUrlParams() {
    const { startDate = "", searchStartDate = "", searchEndDate = "", vdate = "", vnum = "" } = route.query as any;
    startDate && (searchInfo.startPid = startDate);
    if (searchStartDate && searchEndDate) {
        searchStart.value = searchStartDate;
        searchEnd.value = searchEndDate;
        searchInfo.startPid = searchStartDate;
        searchInfo.endPid = searchEndDate;
    }
    const assignParams = {
        startVDate: vdate,
        endVDate: vdate,
        startVNum: vnum,
        endVNum: vnum,
        vgId: vnum ? 1 : 0,
    };
    if (vdate || vnum) {
        Object.assign(searchInfo, assignParams);
    }
}
function handleLoadTableData(position?: string) {
    let params = { ...getBaseParams() };
    let lineSnParams: any = {};
    if (!hasLoading) {
        setUrlParams();
        const { tran_no = "" } = route.query;
        if (tran_no) {
            searchInfo.TRAN_NO = tran_no as string;
            cdAccount.value = "all";
        }
        const { lineSn = "" } = route.query;
        lineSn && (lineSnParams.line_sn = lineSn);
        params = { ...getBaseParams(), ...lineSnParams };
        hasLoading = true;
        tryClearCustomUrlParams(route);
    }
    const queryParams = {
        LimitScm: "",
        pageIndex: journalTableRef.value?.getPaginationData().currentPage,
        pageSize: journalTableRef.value?.getPaginationData().pageSize,
    };
    if (params.date_s === "" || params.date_s + "" === "null" || params.date_e === "" || params.date_e + "" === "null") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    if (new Date(params.date_s) > new Date(params.date_e)) {
        ElNotify({ type: "warning", message: "开始日期不能晚于结束日期哦~" });
        return;
    }
    if (Number(params.amount_s) > Number(params.amount_e) && params.amount_e !== "" && params.amount_e + "" !== "null") {
        ElNotify({ type: "warning", message: "起始金额不能小于结束金额呦！" });
        return;
    }
    if (Number(params.startVNum) > Number(params.endVNum) && params.endVNum !== "" && params.endVNum + "" !== "null") {
        ElNotify({ type: "warning", message: "起始凭证号不能小于结束凭证号呦！" });
        return;
    }
    loading.value = true;
    handleClose();
    setInfos();
    request({ url: "/api/Journal/PagingList?" + getUrlSearchParams(queryParams), method: "post", data: params })
        .then((res: any) => {
            journalTableRef.value?.resetRowInfo();
            if (res.state !== 1000) {
                tableData.value = [];
                journalTableRef.value?.setColumns();
                journalTableRef.value?.setPaginationData({ total: 0 });
                return;
            }
            tableData.value = res.data.rows.map((item: ITableItem, index: number) => {
                item.index = index;
                item.showAll = item.cd_date !== "" && item.ie_type !== "";
                item.onlyShowDelete = false;
                item.changeDate = false;
                item.defaultClickItem = false;
                item.changeDataPut = false;
                return item;
            });
            journalTableRef.value?.setColumns();
            journalTableRef.value?.setPaginationData({ total: res.data.total });
            checkInitAmountCanEdit();
            if (tableData.value.findIndex((item) => item.description === "合计" && item.ie_type === "" && item.cd_date === "") !== -1) {
                const item = tableData.value[tableData.value.length - 2];
                // 此时已结账  应该不能默认点击
                if (item.description !== "") return;
                item.defaultClickItem = true;
                item.canMatchIEType = true;
                if (position === "description") {
                    journalTableRef.value?.handleRowClick(item, "description");
                } else {
                    journalTableRef.value?.handleRowClick(item);
                }
            }
            nextTick(() => {
                journalTableRef.value?.setScrollTop(canchScrollTop.value);
                canchScrollTop.value = 0;
            });
        })
        .catch(() => {
            journalTableRef.value?.resetRowInfo();
        })
        .finally(() => {
            setInfos();
            loading.value = false;
            hasLoading = false;
            window.localStorage.setItem("journal-pageSize-" + getGlobalToken(), queryParams.pageSize + "");
        });
}
function handleSearch() {
   handleLoadTableData();
}
function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.startPid = searchStart.value;
    searchInfo.endPid = searchEnd.value;
    resetParamsWithoutDateInfo();
}
function resetParamsWithoutDateInfo() {
    searchInfo.startVDate = "";
    searchInfo.endVDate = "";
    searchInfo.vgId = 0;
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
    userChangeDate.value = false;
    userChangeDateAndSearch.value = false;
    searchInfo.IE_TYPE = ieTypeOptions.value.map((item) => item.id);
    searchInfo.PAYMENT_METHOD = paymentMethodListOptions.value.map((item) => item.id);
    searchInfo.HasVoucher = "-1";
    searchInfo.OPPOSITE_PARTY = "";
    searchInfo.RECEIPT_NO = "";
    searchInfo.PROJECT = projectListOptions.value.map((item) => item.id);
    searchInfo.DEPARTMENT = departmentListOptions.value.map((item) => item.id);
    searchInfo.DESCRIPTION = "";
    searchInfo.NOTE = "";
    searchInfo.Amount_s = "";
    searchInfo.Amount_e = "";
    searchInfo.TRAN_NO = "";
}
function restFilterInfo() {
    filterSearchInfo.DEPARTMENT = [];
    filterSearchInfo.IE_TYPE = [];
    filterSearchInfo.PAYMENT_METHOD = [];
    filterSearchInfo.OPPOSITE_PARTY = "";
    filterSearchInfo.PROJECT = [];
    filterSearchInfo.DESCRIPTION = "";
    filterSearchInfo.NOTE = "";
    filterSearchInfo.RECEIPT_NO = "";
}
function handleConfirmSearch(flag: boolean = false) {
    if (flag) {
        restFilterInfo();
    }
    if (journalTableRef.value?.getPaginationData().currentPage !== 1) {
        userChangeDate.value && (userChangeDateAndSearch.value = true);
        journalTableRef.value?.setPaginationData({ currentPage: 1 });
        return;
    }
    handleLoadData();
}
function handleLoadData() {
    userChangeDate.value && (userChangeDateAndSearch.value = true);
    handleSearch();
}
function handleImportSuccess() {
    clearSelection();
    resetParamsWithoutDateInfo();
    journalTableRef.value?.debugCanChangeCurrentPage();
    journalTableRef.value?.setPaginationData({ currentPage: 1 });
    handleLoadTableData("description");
    handleExpiredCheckData(ExpiredCheckModuleEnum.Cashier);
}
//用户是否修改过时间
const userChangeDate = ref(false);
//用户是否修改过时间且经过查询
const userChangeDateAndSearch = ref(false);
function handleUserChangeDate() {
    userChangeDate.value = true;
}
//是否隐藏余额的原币和汇率
const hiddenAmountFc = ref(false);
const fcAccountHistoryDataSync = ref(false);
const syncTitle = ref("");
const syncFcName = ref("");
const useFc = ref(false);
//账户变动时重新查询数据，涉及到账户变动以及是否显示账户禁用切换情况下导致的账户变动
async function handleSearchAccountChange(val: string) {
    let cdAccounts: Array<string>;
    if (val === "all") {
        cdAccountDisplay.value = "全部账户";
        useFc.value = cdAccountList.value.filter((item) => item.standard === "1").length > 0;
        const set = [...new Set(cdAccountList.value.map((item) => item.currency))];
        hiddenAmountFc.value = set.length > 1;
        cdAccounts = cdAccountList.value.map((item) => item.ac_id);
        debugShowAll();
        showAll.value = useFc.value;
    } else {
        const item = cdAccountList.value.find((item) => item.ac_id === val) as ICDAccountItem;
        cdAccountDisplay.value = item.ac_no + " - " + item.ac_name;
        useFc.value = item.standard === "1";
        hiddenAmountFc.value = false;
        cdAccounts = [item.ac_id];
    }
    if (val !== "all" && !userChangeDateAndSearch.value) {
        userChangeDate.value = false;
        await request({ url: "/api/Journal/GetCDJournalDate?cdAccount=" + val + "&jType=" + journalType.value, method: "post" }).then(
            (res: IResponseModel<IJournalDate>) => {
                searchStart.value = res.data.searchStart;
                searchEnd.value = res.data.searchEnd;
                searchInfo.startPid = res.data.searchStart;
                searchInfo.endPid = res.data.searchEnd;
                checkDate.value = res.data.checkDate;
                checkoutDate.value = new Date(res.data.checkDate.replace(/-/g, "/"));
                startDate.value = res.data.startDate;
                if (route.query.searchStartDate && route.query.searchEndDate) {
                    searchStart.value = route.query.searchStartDate as string;
                    searchEnd.value = route.query.searchEndDate as string;
                    searchInfo.startPid = route.query.searchStartDate as string;
                    searchInfo.endPid = route.query.searchEndDate as string;
                }
            }
        );
    }
    if (useFc.value) {
        checkFCAccountExistsHistoryData(journalType.value === "1010" ? "Cash" : "Deposit", cdAccounts).then((res) => {
            if (res.state === 1000) {
                if (res.data.exists) {
                    let title = "";
                    for (let index = 0; index < res.data.data.length; index++) {
                        const element = res.data.data[index];
                        title +=
                            '<span class="green"> ' +
                            element.accountName +
                            " </span>" +
                            '的币别为 <span class="green"> ' +
                            element.fcName +
                            " </span>，";
                    }
                    syncTitle.value = title;
                    if (res.data.data.length === 1) {
                        syncFcName.value = res.data.data[0].fcName;
                    } else {
                        syncFcName.value = "外币";
                    }
                    fcAccountHistoryDataSync.value = true;
                }
            } else {
                ElNotify({ type: "warning", message: "加载失败，请刷新页面重试" });
            }
        });
    }
    const cachePageSize = window.localStorage.getItem("journal-pageSize-" + getGlobalToken());
    if (cachePageSize && cachePageSize !== journalTableRef.value?.getPaginationData().pageSize.toString()) {
        nextTick().then(() => {
            journalTableRef.value?.setPaginationData({ pageSize: Number(cachePageSize) || 20 });
        });
        return;
    }
    handleSearch();
}
function syncFcData(type: 0 | 1) {
    fcAccountHistoryDataSync.value = false;
    const fullScreenLoading = FullScreenLoading();
    let cdAccountArray: Array<string>;
    if (cdAccount.value === "all") {
        cdAccountArray = cdAccountList.value.map((item) => item.ac_id);
    } else {
        cdAccountArray = [cdAccount.value];
    }
    syncFCAccountHistoryData(journalType.value === "1010" ? "Cash" : "Deposit", cdAccountArray, type).then((res) => {
        fullScreenLoading.close();
        if (res.state === 1000 && res.data) {
            ElNotify({ type: "success", message: "数据同步成功！" });
        } else {
            ElNotify({ type: "warning", message: "数据同步失败，请重试！" });
        }
        if (showAll.value) {
            handleSearch();
        } else {
            showAll.value = true;
        }
    });
}
function handleChangeAcid(acid: number | string) {
    userChangeDateAndSearch.value = true;
    cdAccount.value = acid.toString();
}
let showAllLock = false;
function debugShowAll() {
    showAllLock = true;
    const timer = setTimeout(() => {
        showAllLock = false;
        clearTimeout(timer);
    }, 10);
}
watch(showAll, (val) => {
    showAllList.value = val;
    if (showAllLock) return;
    handleSearch();
});
function clearSelection() {
    journalTableRef.value?.handleClearSelection();
}
watch(cdAccount, (val) => {
    journalTableRef.value?.debugCanChangeCurrentPage();
    journalTableRef.value?.setPaginationData({ currentPage: 1 });
    val !== "all" && clearSelection();
    handleSearchAccountChange(val);
});

// 选中数组
const selectionList = ref<Array<ITableItem>>([]);
function handleSelectionChange(val: Array<ITableItem>) {
    selectionList.value = val;
}
function getSelectionList() {
    return selectionList.value;
}

// 单元格操作
function changeCurrentRow(row: ITableItem, type: "post" | "put", params: IJournalBack, requestParams: any, endIndex: number) {
    const oldRow = cloneDeep(row);
    row.j_type = params.j_type;
    row.cd_account = params.cd_account;
    row.line_sn = params.line_sn;
    row.cd_date = formatDate(params.cd_date);
    row.description = params.desription;
    row.ie_type = params.ie_type;
    row.ie_type_name = (params.isIncome ? "收-" : "支-") + params.ie_typename;
    row.income = params.income + "";
    row.expenditure = params.expenditure + "";
    row.ac_no = params.cd_no;
    row.ac_name = params.cd_accountname;
    row.opposite_party = params.oppositePartyText;
    row.opposite_party_uscc = params.opposite_party_uscc;
    row.payment_method = params.payment_method;
    row.payment_method_name = params.paymentMethodText;
    row.receipt_no = params.receipt_no;
    row.note = params.note;
    row.created_date = params.createdDate;
    row.income_standard = params.income_standard + "";
    row.expenditure_standard = params.expenditure_standard + "";
    const [perfix] = getLineSnAndPerfix(oldRow.line_sn_name);
    let needAppendLineSnLength = 0;
    for (let index = 0; index < endIndex; index++) {
        const item = tableData.value[index];
        const [itemPerfix] = getLineSnAndPerfix(item.line_sn_name);
        if (!item.line_sn_name.startsWith("<a class='link'") && itemPerfix === perfix) {
            needAppendLineSnLength++;
        }
    }
    if (checkPermission(["voucher-canview"])) {
        row.line_sn_name = getLineSnNameWithAnchorTag(
            perfix + "-" + (~~params.line_sn + needAppendLineSnLength),
            params.cd_account,
            params.cd_date,
            params.line_sn,
            params.isIncome
        );
    }
    row.ac_no = params.cd_no;
    row.ac_name = params.cd_accountname;
    row.tran_no = params.createdDate.replace(/[^\d]/g, "");
    row.project = requestParams.project + "";
    row.department = requestParams.department + "";
    row.project_name = projectList.value.find((item) => item.aaeid === Number(requestParams.project))?.aaname || "";
    row.department_name = departmentList.value.find((item) => item.aaeid === Number(requestParams.department))?.aaname || "";
    row.opposite_party_no = requestParams.opposite_party_no;
    row.opposite_party_int = requestParams.opposite_party_int;
    row.opposite_party_bank = requestParams.opposite_party_bank;

    row.showAll = true;
    row.onlyShowDelete = false;
    row.changeDate = false;
    row.oldCdDate = undefined;
    row.oldCDAccount = undefined;
    row.defaultClickItem = false;
    row.isCopyData = false;
    row.isInsertData = false;
    if (params.isIncome) {
        row.ie_type_name = "收-" + params.ie_typename;
        row.income_rate = params.fc_rate + "";
        row.expenditure_rate = "";
        row.expenditure = "";
        row.expenditure_standard = "";
    } else {
        row.ie_type_name = "支-" + params.ie_typename;
        row.income_rate = "";
        row.expenditure_rate = params.fc_rate + "";
        row.income = "";
        row.income_standard = "";
    }

    type === "post" && (row.receiptCount = "0");

    return needAppendLineSnLength;
}
function changeLineSnWithTag(lineSnName: string, lineSn: string | number, equal = true) {
    const [perfix] = getLineSnAndPerfix(lineSnName);
    const showLineSn = equal ? lineSn : ~~lineSn + 1;
    const newLineSnName = perfix + "-" + showLineSn;
    lineSnName = lineSnName.replace(/LINE_SN=[^&]*/, `LINE_SN=${lineSn}`);
    const lineSnNameWithoutTag = lineSnName.replace(/<[^<>]+>/g, "");
    lineSnName = lineSnName.replace(lineSnNameWithoutTag, newLineSnName);
    return lineSnName;
}
function changeRowRateAndLastRowAmountInfo(afterList: Array<ITableItem>, totalIndex: number, difference: IDifferenceInfo) {
    const {
        differentAmount,
        differentAmountStandard,
        differentIncome,
        differentIncomeStandard,
        differentExpenditure,
        differentExpenditureStandard,
    } = difference;

    afterList.forEach((item: ITableItem) => {
        item.amount_standard = Number(item.amount_standard) + Number(differentAmountStandard) + "";
        if (cdAccount.value !== "all" || (cdAccount.value === "all" && !isMoreFc(cdAccountList.value))) {
            item.amount = Number(item.amount) + Number(differentAmount) + "";
            item.amount_rate = formatRate(Number(item.amount_standard) / Number(item.amount));
        }
    });

    if (totalIndex !== -1) {
        const lastItem = tableData.value[totalIndex];
        lastItem.amount_standard = Number(lastItem.amount_standard) + Number(differentAmountStandard) + "";
        if (cdAccount.value !== "all" || (cdAccount.value === "all" && !isMoreFc(cdAccountList.value))) {
            lastItem.amount = Number(lastItem.amount) + Number(differentAmount) + "";
            lastItem.amount_rate = formatRate(Number(lastItem.amount_standard) / Number(lastItem.amount));
            lastItem.income = Number(lastItem.income) + Number(differentIncome) + "";
            lastItem.income_standard = Number(lastItem.income_standard) + Number(differentIncomeStandard) + "";
            lastItem.expenditure = Number(lastItem.expenditure) + Number(differentExpenditure) + "";
            lastItem.expenditure_standard = Number(lastItem.expenditure_standard) + Number(differentExpenditureStandard) + "";
        } else if (cdAccount.value === "all" && isMoreFc(cdAccountList.value) && showAll.value) {
            lastItem.income_standard = Number(lastItem.income_standard) + Number(differentIncomeStandard) + "";
            lastItem.expenditure_standard = Number(lastItem.expenditure_standard) + Number(differentExpenditureStandard) + "";
        }
    }
}
function appendACName() {
    const length = tableData.value.length;
    const lastItem = tableData.value[length - 1];
    if (lastItem.description === "合计" && lastItem.ie_type === "" && lastItem.cd_date === "") {
        const beforeLastClickItem = tableData.value[length - 2];
        const accountItem = allCdAccountList.value.find((item) => item.ac_id === beforeLastClickItem.cd_account) as ICDAccountItem;
        if (!beforeLastClickItem.ac_name && beforeLastClickItem.ac_no) {
            beforeLastClickItem.ac_name = accountItem.ac_name;
        }
    }
    tableData.value.forEach((item, index) => {
        item.index = index;
    });
}
function checkRowIsTotal(index: number) {
    const item = tableData.value[index];
    return item?.description === "合计" && item?.ie_type === "" && item?.cd_date === "";
}
function changeData(type: "post" | "put", params: IJournalBack, index: number, searchParams: any) {
    const row = tableData.value[index];
    const oldRow = cloneDeep(row);
    if (oldRow.changeDate) {
        handleLoadTableData("description");
        return;
    }
    // 更新当前行
    const needAppendLineSnLength = changeCurrentRow(row, type, params, searchParams, index);
    // 更新后续行的 rate 以及最后一行的金额信息
    const totalIndex = tableData.value.findIndex((item) => item.description === "合计" && !item.cd_date);
    const afterList = totalIndex === -1 ? tableData.value.slice(index) : tableData.value.slice(index, totalIndex);
    const difference = getDifferenceAmount(params, oldRow, type);
    changeRowRateAndLastRowAmountInfo(afterList, totalIndex, difference);
    // 更新后续行的其他信息
    journalTableRef.value?.resetRowInfo();
    journalTableRef.value?.updateSelections();
    if (type === "put") {
        afterList.forEach((item: ITableItem, index: number) => {
            const isSameDate = formatDate(oldRow.cd_date) === formatDate(params.cd_date);
            if (item.cd_account === oldRow.cd_account && oldRow.cd_account !== params.cd_account && index !== 0 && isSameDate) {
                item.line_sn = ~~item.line_sn - 1 + "";
                const [perfix] = getLineSnAndPerfix(item.line_sn_name);
                const newLineSnName = perfix + "-" + item.line_sn;
                item.line_sn_name = item.line_sn_name.startsWith("<a class='link'")
                    ? changeLineSnWithTag(item.line_sn_name, item.line_sn)
                    : newLineSnName;
            }
        });
        return;
    }
    if (type === "post") {
        const [perfix] = getLineSnAndPerfix(oldRow.line_sn_name);
        if (!oldRow.isInsertData && !oldRow.isCopyData) {
            const lastPosition = tableData.value.length - 1;
            const lineSn = ~~oldRow.line_sn + 1;
            tableData.value.splice(lastPosition, 0, {
                ...oldRow,
                cd_date: formatDate(params.cd_date),
                index: oldRow.index + 1,
                line_sn: lineSn.toString(),
                line_sn_name: perfix + "-" + (lineSn + needAppendLineSnLength),
                amount: row.amount,
                amount_standard: row.amount_standard,
                canMatchIEType: true,
            });
            appendACName();
            journalTableRef.value?.setPaginationData({ total: journalTableRef.value?.getPaginationData().total + 1 });
            journalTableRef.value?.handleRowClick(tableData.value[lastPosition], "description");
        } else if (oldRow.isInsertData) {
            let insertLength = 0;
            for (let i = 0; i < tableData.value.length; i++) {
                if (tableData.value[i].isInsertData) {
                    insertLength++;
                }
            }
            tableData.value.splice(index + 1, 0, {
                ...oldRow,
                cd_date: formatDate(params.cd_date),
                amount: row.amount,
                amount_standard: row.amount_standard,
                isInsertData: true,
                canMatchIEType: true,
            });
            const insertIndex = tableData.value.findIndex((item) => item.index === oldRow.index);
            let initLineSn = ~~oldRow.line_sn + 1 + "";
            for (let i = insertIndex + 1; i < tableData.value.length - 1; i++) {
                const item = tableData.value[i];
                item.defaultClickItem = i === insertIndex + 1;
                const [itemPerifx, itemLineSn] = getLineSnAndPerfix(item.line_sn_name);
                if (itemPerifx === perfix) {
                    if (item.line_sn_name.startsWith("<a class='link'")) {
                        item.line_sn = ~~extractLineSN(item.line_sn_name) + 1 + "";
                        const lineSnName = changeLineSnWithTag(item.line_sn_name, item.line_sn, false);
                        const [itemPerifx, itemLineSn] = getLineSnAndPerfix(lineSnName);
                        const realLineSnName = itemPerifx + "-" + itemLineSn;
                        const newNameLineSn = ~~itemLineSn + insertLength;
                        item.line_sn_name = lineSnName.replace(realLineSnName, itemPerifx + "-" + newNameLineSn);
                        // 所有的非 a 标签的数据的 line_sn 应该都是上一个 a 标签的 line_sn + 1
                        initLineSn = ~~item.line_sn + 1 + "";
                    } else {
                        item.line_sn_name = itemPerifx + "-" + (~~itemLineSn + 1);
                        item.line_sn = initLineSn;
                        item.onlyShowDelete = true;
                        item.showAll = false;
                        checkRowIsTotal(i + 1) && (item.onlyShowDelete = false);
                    }
                } else {
                    break;
                }
            }
            appendACName();
            const item = tableData.value.find((item) => item.defaultClickItem);
            item && journalTableRef.value?.handleRowClick(item, "description");
        } else if (oldRow.isCopyData) {
            const copyIndex = tableData.value.findIndex((item) => item.index === oldRow.index);
            for (let i = copyIndex + 1; i < tableData.value.length - 1; i++) {
                const item = tableData.value[i];
                const [itemPerifx] = getLineSnAndPerfix(item.line_sn_name);
                if (itemPerifx === perfix) {
                    if (item.line_sn_name.startsWith("<a class='link'")) {
                        item.line_sn = ~~extractLineSN(item.line_sn_name) + 1 + "";
                        item.line_sn_name = changeLineSnWithTag(item.line_sn_name, item.line_sn);
                    } else {
                        item.line_sn = ~~item.line_sn + 1 + "";
                    }
                } else {
                    break;
                }
            }
        }
        tableData.value.forEach((item) => {
            item.isCopyData = false;
        });
    }
}
const isEdit = ref(false);
let isRequesting = false;
function handleInitTableState(replaceData?: any) {
    if (replaceData !== undefined) {
        journalTableRef.value?.changeEditInfo(replaceData);
        journalTableRef.value?.setUseLastRow(true);
    }
    isEdit.value = false;
    journalTableRef.value?.resetAllowBlur();
}
function handleSubmit(oldRow: ITableItem, rowItemSearchInfo: IRowItemEditInfo) {
    if (isRequesting) return;
    const incomeRate = Number(rowItemSearchInfo.INCOME_RATE) || 0;
    const expenditureRate = Number(rowItemSearchInfo.EXPENDITURE_RATE) || 0;
    const OppositeIndex = companyList.value.findIndex((item) => item.text === rowItemSearchInfo.OPPOSITE_PARTY);
    const aaeItem = companyListCompare.value.find(
        (item) => item.value === rowItemSearchInfo.OPPOSITE_PARTY_INT && item.text === rowItemSearchInfo.OPPOSITE_PARTY
    );
    const oppositePartyInt = aaeItem?.value || (isErp.value ? rowItemSearchInfo.OPPOSITE_PARTY_INT || "" : "");
    const params = getCombineSubmitParams(oldRow, rowItemSearchInfo);
    params.as_id = useAccountSetStore().accountSet!.asId;
    params.j_type = journalType.value;
    params.fc_rate = incomeRate !== 0 ? incomeRate : expenditureRate;
    params.opposite_party_int = oppositePartyInt;
    if (isErp.value && !params.opposite_party_int) {
        params.opposite_party = "";
    }
    if (!checkCanSave(params, () => journalTableRef.value?.setUseLastRow(true))) {
        handleInitTableState();
        return;
    }
    if (rowItemSearchInfo.INCOME && !isNumericString(rowItemSearchInfo.INCOME)) {
        const replaceData = { INCOME: "" };
        handleInitTableState(replaceData);
        return;
    } else if (rowItemSearchInfo.EXPENDITURE && !isNumericString(rowItemSearchInfo.EXPENDITURE)) {
        const replaceData = { EXPENDITURE: "" };
        handleInitTableState(replaceData);
        return;
    }
    const IE_TYPE_NAME = payList.value.find((item) => item.subkey === rowItemSearchInfo.IE_TYPE)?.value2 || "";
    const incomeFail = IE_TYPE_NAME.startsWith("收") && rowItemSearchInfo.EXPENDITURE !== "";
    const expenditureFail = IE_TYPE_NAME.startsWith("支") && rowItemSearchInfo.INCOME !== "";
    if (incomeFail || expenditureFail) {
        ElNotify({ type: "warning", message: "收支类别和金额方向不匹配，不能保存哦" });
        const replaceAmount = { INCOME: "", EXPENDITURE: "" };
        handleInitTableState(replaceAmount);
        return;
    }
    if (IE_TYPE_NAME.startsWith("收")) {
        rowItemSearchInfo.EXPENDITURE = "";
        rowItemSearchInfo.EXPENDITURE_STANDARD = "";
    } else if(IE_TYPE_NAME.startsWith("支")) {
        rowItemSearchInfo.INCOME = "";
        rowItemSearchInfo.INCOME_STANDARD = "";
    }
    let method: "post" | "put" = "post";
    let appendParams = "";
    if ((!oldRow.onlyShowDelete && !!oldRow.showAll) || oldRow.changeDataPut) {
        method = "put";
        const oldParams = getUrlSearchParams({
            oldCdDate: oldRow.changeDate ? oldRow.oldCdDate : oldRow.cd_date,
            oldCdAccount: oldRow.changeDate ? oldRow.oldCDAccount : oldRow.cd_account,
        });
        appendParams = "?" + oldParams;
    } else if (oldRow.isCopyData) {
        appendParams = "?isCopy=true";
    }
    isRequesting = true;
    let loadingInstance: any = null;
    isErp.value ? (loading.value = true) : (loadingInstance = ElLoading.service({ background: "transparent" }));
    const url = (journalType.value === "1020" ? "/api/Journal/Deposit" : "/api/Journal/Cash") + appendParams;
    request({ url, method, headers: { "Content-Type": "application/x-www-form-urlencoded" }, data: params })
        .then((res: IResponseModel<IJournalBack>) => {
            isErp.value ? (loading.value = false) : loadingInstance?.close();
            const result = res.msg;
            if (result === "Success" || result === "") {
                ElNotify({ type: "success", message: "保存成功" });
                changeData(method, res.data, oldRow.index, params);
                method === "post" && handleExpiredCheckData(ExpiredCheckModuleEnum.Cashier);
                return;
            } else if (result.indexOf("Used") !== -1) {
                confirmParams.pid = params.p_id;
                confirmParams.vid = params.v_id;
                changeData(method, res.data, oldRow.index, params);
                editVoucherShow.value = true;
                return;
            } else if (result.indexOf("IncomeStandardLarge") !== -1) {
                ElNotify({ type: "warning", message: "收入本币金额不能大于亿位！" });
            } else if (result.indexOf("ExpenditureStandardLarge") !== -1) {
                ElNotify({ type: "warning", message: "支出本币金额不能大于亿位！" });
            } else if (result.indexOf("IncomeLarge") !== -1) {
                ElNotify({ type: "warning", message: "收入原币金额不能大于亿位！" });
            } else if (result.indexOf("ExpenditureLarge") !== -1) {
                ElNotify({ type: "warning", message: "支出原币金额不能大于亿位！" });
            }else if (result.indexOf("ErrorIEType") !== -1) {
                ElNotify({ type: "warning", message: "收支类别不存在，请刷新页面重试！" });
            } else if (result == "OffSet") {
                ElNotify({ type: "warning", message: "资金数据已核销，不能修改！" });
            } else if (result.indexOf("ErrorDate") !== -1) {
                ElNotify({ type: "warning", message: "日记账最晚只能录入当前日期五年内数据！" });
            } else if (result.indexOf("ErrorDepartment") !== -1) {
                ElNotify({ type: "warning", message: "部门不存在，请刷新页面重试！" });
            } else if (result.indexOf("ErrorProject") !== -1) {
                ElNotify({ type: "warning", message: "项目不存在，请刷新页面重试！" });
            } else if (result.indexOf("ErrorCdAccount") !== -1) {
                ElNotify({ type: "warning", message: "账户不存在，请刷新页面重试！" });
            } else if (result == "Transfer") {
                ElNotify({ type: "warning", message: "内部转账单据不能在日记账修改，请在内部转账修改！" });
            } else if (result == "DisableIEType") {
                ElNotify({ type: "warning", message: "收支类别名称已被禁用" });
                journalTableRef.value?.resetRowInfo();
            } else if (result == "NotLeafIEType" && res.state == 9999) {
                const ieTypeItem = allIEType.value.find((item) => item.subkey === params.ie_type);
                const ieTypeName = ieTypeItem ? ieTypeItem.value2.slice(2) : "";
                ElConfirm(`收支类别"${ieTypeName}"已存在下级类别，请使用下级类别`, true).then((r: boolean) => {
                    r && handleGetIETypeList();
                });
            } else {
                ElNotify({ type: "warning", message: "保存失败，请刷新页面重试！" });
            }
        })
        .catch(() => {
            isErp.value ? (loading.value = false) : loadingInstance?.close();
            ElNotify({ type: "warning", message: "保存失败，请刷新页面重试！" });
        })
        .finally(() => {
            if (OppositeIndex < 0) { //往来单位手动输入变化，列表变化了，往来单位查询列表更新
                getCompany();
            }
            handleInitTableState();
            changeInitLineSnName = true;
            isRequesting = false;
        });
}
let changeInitLineSnName = true;
let initLineSnName = "";
function insertNewRow(row: ITableItem) {
    // 保存初始日记账数据
    const thisInitLineSnName = changeInitLineSnName ? row.line_sn_name.replace(/<[^<>]+>/g, "") : initLineSnName;
    initLineSnName = thisInitLineSnName.replace(/<[^<>]+>/g, "");
    changeInitLineSnName = false;
    const cdAccountData =
        cdAccount.value === "all"
            ? cdAccountList.value[0]
            : (cdAccountList.value.find((item) => item.ac_id == cdAccount.value) as ICDAccountItem);
    let newAmount = "0.00";
    let newAmountStandard = "0.00";
    const newAmountRate = row.amount_rate;
    const isShowAmount = cdAccount.value !== "all" || (cdAccount.value === "all" && !isMoreFc(cdAccountList.value));
    if (Number(row.income) === 0) {
        newAmount = isShowAmount ? Number(row.amount) + Number(row.expenditure) + "" : "";
        newAmountStandard = Number(row.amount_standard) + Number(row.expenditure_standard) + "";
    } else {
        newAmount = isShowAmount ? Number(row.amount) - Number(row.income) + "" : "";
        newAmountStandard = Number(row.amount_standard) - Number(row.income_standard) + "";
    }
    const isAllColumns = useFc.value && showAll.value;
    const newRow: ITableItem = {
        ...initRowData,
        cd_date: formatDate(row.cd_date),
        fc_name: cdAccountData.currency_name,
        index: row.index as number,
        erp_offset: "",
        showAll: false,
        onlyShowDelete: true,
        line_sn_name: thisInitLineSnName,
        cd_account: cdAccountData.ac_id,
        ac_no: row.ac_no,
        ac_name: row.ac_name,
        changeDate: false,
        income_rate: cdAccountData.fc_rate,
        expenditure_rate: cdAccountData.fc_rate,
        line_sn: row.line_sn,
        isInsertData: true,
        defaultClickItem: true,
        amount: isAllColumns ? "" : newAmount,
        amount_standard: newAmountStandard,
        amount_rate: newAmountRate,
    };
    insertDataToTable(row, newRow, "insert");
}
function copyRow(row: ITableItem) {
    if (isRequesting) return;
    if (tableData.value.findIndex((item) => !!item.isCopyData) !== -1) return;
    const thisInitLineSnName = changeInitLineSnName ? row.line_sn_name.replace(/<[^<>]+>/g, "") : initLineSnName;
    initLineSnName = thisInitLineSnName.replace(/<[^<>]+>/g, "");
    changeInitLineSnName = false;
    const newRow: ITableItem = {
        ...row,
        cd_date: formatDate(row.cd_date),
        p_id: "",
        v_id: "",
        v_num: "",
        v_num2: "",
        showAll: false,
        onlyShowDelete: true,
        erp_offset: "",
        amount_rate: "",
        line_sn: (Number(row.line_sn) + 1).toString(),
        line_sn_name: thisInitLineSnName,
        changeDate: false,
        isCopyData: true,
        defaultClickItem: true,
    };
    insertDataToTable(row, newRow, "copy");
}
function changeSameLineSnName(
    targetIndex: number,
    lineSnName: string,
    type: "insert" | "copy" | "delete",
    insertOrDeleteFunc: () => void,
    newRow?: ITableItem
) {
    const [perfix, lineSn] = getLineSnAndPerfix(lineSnName);
    const sameLineSnPerfixList: Array<ITableItem> = [];
    for (let i = targetIndex; i < tableData.value.length; i++) {
        const item = tableData.value[i];
        const [itemPerifx, itemLineSn] = getLineSnAndPerfix(item.line_sn_name);
        if (itemPerifx === perfix) {
            (type === "insert" || type === "copy" || lineSn !== itemLineSn) && sameLineSnPerfixList.push(item);
        } else {
            break;
        }
    }
    type === "copy" && newRow && sameLineSnPerfixList.unshift(newRow);
    const diff = type === "delete" ? -1 : 1;
    sameLineSnPerfixList.forEach((item) => {
        const [, lineSn] = getLineSnAndPerfix(item.line_sn_name);
        const lineSnStr = perfix + "-" + (~~lineSn + diff);
        if (item.line_sn_name.startsWith("<")) {
            const tempList = splitATag(item.line_sn_name);
            tempList[1] = lineSnStr;
            item.line_sn_name = tempList.join("");
        } else {
            item.line_sn_name = lineSnStr;
        }
    });
    insertOrDeleteFunc();
    tableData.value = tableData.value.map((item, index) => {
        item.index = index;
        return item;
    });
    journalTableRef.value?.setPaginationData({ total: journalTableRef.value?.getPaginationData().total + diff });
    journalTableRef.value?.resetRowInfo();
}
function insertDataToTable(oldRow: ITableItem, newRow: ITableItem, type: "insert" | "copy") {
    newRow[selectKey] = false;
    let targetIndex = oldRow.index;
    type === "copy" && (targetIndex = targetIndex + 1);
    function insertOrDeleteFunc() {
        tableData.value.splice(targetIndex, 0, newRow);
    }
    changeSameLineSnName(targetIndex, oldRow.line_sn_name, type, insertOrDeleteFunc, newRow);
    const newItem = tableData.value[targetIndex];
    journalTableRef.value?.handleRowClick(newItem, type === "insert" ? "description" : "amount");
}
function deleteRow(row: ITableItem) {
    if (row.showAll && !row.onlyShowDelete) {
        if (row.v_num2 !== "") {
            ElNotify({ message: "资金数据已生成凭证，请先删除凭证！", type: "warning" });
            return;
        }
        ElConfirm("亲，确认要删除吗?").then((r: boolean) => r && confirmDeleteRow(row));
        return;
    }
    if (row.changeDate) {
        tableData.value.splice(row.index, 1);
        handleSearch();
        return;
    }
    if (!row.onlyShowDelete) return;
    const targetIndex = row.index;
    function insertOrDeleteFunc() {
        tableData.value.splice(targetIndex, 1);
    }
    changeSameLineSnName(targetIndex + 1, row.line_sn_name, "delete", insertOrDeleteFunc);
    const clickRow = tableData.value.find((item) => item.defaultClickItem);
    if (clickRow) journalTableRef.value?.handleRowClick(clickRow, "description");
}
function confirmDeleteRow(row: ITableItem) {
    const params = {
        cdAccount: row.cd_account,
        cdDate: row.cd_date,
        createdDate: row.created_date,
        lineSnName: row.line_sn_name.replace(/<[^<>]+>/g, ""),
    };
    const BasePath = journalType.value === "1020" ? "/api/Journal/Deposit" : "/api/Journal/Cash";
    request({ url: BasePath + "?" + getUrlSearchParams(params), method: "delete" })
        .then((res: IResponseModel<string>) => {
            if (res.state == 1000) {
                if (res.data == "Success") {
                    ElNotify({ type: "success", message: "删除成功" });
                    handleSearch();
                    journalTableRef.value?.toggleSelection2False(row);
                }
            } else {
                if (res.data == "Used") {
                    ElNotify({ type: "warning", message: "该条数据已生成凭证，不能删除！" });
                } else if (res.data == "OffSet") {
                    ElNotify({ type: "warning", message: "该条数据已核销，不能删除！" });
                } else {
                    ElNotify({ type: "warning", message: "删除失败了，请刷新页面重试" });
                }
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "删除失败了，请刷新页面重试" });
        });
}
function printRow(row: ITableItem) {
    const printParams = journalOperateRef.value?.getPrintParams();
    const params = {
        cdAccount: row.cd_account,
        lineSn: row.line_sn,
        cdDate: row.cd_date,
        createdDate: row.created_date,
        seniorModelJson: JSON.stringify(printParams),
    };
    const BasePath = journalType.value === "1020" ? "/api/Journal/PrintDeposit" : "/api/Journal/PrintCash";
    globalPostPrint(BasePath, params);
}
const readonly = ref(false);
let isReceiptingRow: ITableItem | null = null;
let receiptRowIndex = -1;
function receiptRow(row: ITableItem) {
    const params = { createdDate: row.created_date };
    const url = props.journalType === "1020" ? "/api/Journal/GetReceiptList" : "/api/Journal/GetTurnoverList";
    request({ url, method: "post", params }).then((res: IResponseModel<any>) => {
        if (res.state === 1000 && res.data.result) {
            let list = res.data.data;
            list = list.map((item: any) => {
                item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                return item;
            });
            const startDate = searchInfo.startPid.split("-").slice(0, 2).join("-") + "-01";
            const endDateList = searchInfo.endPid.split("-").slice(0, 2);
            const day = getDaysInMonth(Number(endDateList[0]), Number(endDateList[1]));
            const endDate = searchInfo.endPid.split("-").slice(0, 2).join("-") + "-" + day;
            const _params = {
                eRecordSearchDate: { startDate, endDate },
                created_date: row.created_date,
                fileCategory: props.journalType === "1020" ? AttachFileCategory.Receipt : AttachFileCategory.Other,
                permission: props.journalType === "1020" ? ["depositjournal-canedit"] : ["cashjournal-canedit"],
            };
            readonly.value = formatDate(row.cd_date) < checkDate.value;
            uploadFileDialogRef.value?.open(_params, list, res.data.parentId);
            isReceiptingRow = cloneDeep(row);
            receiptRowIndex = row.index;
        } else {
            ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
        }
    });
}
function saveReceipt(_params: any, newFileids: Array<Number>, delFileids: Array<Number>, fileList: Array<any>) {
    const row = isReceiptingRow as ITableItem;
    const newReceiptCount = ~~row.receiptCount + newFileids.length - delFileids.length;
    const params: ISaveAttachmentParams = {
        _params,
        newFileids,
        delFileids,
        fileList,
        receiptState: null,
        newReceiptCount,
    };
    if (props.journalType === "1020" && row.showFromBankReceiptMessage === "1" && ~~row.receiptCount === 0 && newReceiptCount > 0) {
        params.receiptState = 2;
    }
    checkAttachSaveFn(params);
}
let isNeedSaveToVoucherForDelete = false;
interface ISaveAttachmentParams {
    _params: any;
    newFileids: Array<Number>;
    delFileids: Array<Number>;
    fileList: Array<any>;
    receiptState: 2 | null;
    newReceiptCount: number;
}
async function checkAttachSaveFn(saveParams: ISaveAttachmentParams) {
    const { _params, delFileids } = saveParams;
    const item = tableData.value[receiptRowIndex];
    if (delFileids.length === 0 || item.v_id === "") {
        saveReceiptFn(saveParams);
        return;
    }
    const needToast = await checkNeedToastWithBillAndVoucher(_params.created_date, delFileids.join(","));
    if (!needToast) {
        saveReceiptFn(saveParams);
        return;
    }
    showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
        isNeedSaveToVoucherForDelete = batchDelte;
        saveReceiptFn(saveParams);
    });
}
function saveReceiptFn(saveParams: ISaveAttachmentParams) {
    const { _params, newFileids, delFileids, fileList, receiptState, newReceiptCount } = saveParams;
    const params = {
        createdDate: _params.created_date,
        newFileids: newFileids.join(","),
        delFileids: delFileids.join(","),
        receiptState: receiptState,
        isNeedSaveToVoucherForAdd: true,
        isNeedSaveToVoucherForDelete,
    };
    const path = journalType.value === "1020" ? "/api/Journal/SaveReceipt?" : "/api/Journal/SaveTurnover?";
    request({ url: path + getUrlSearchParams(params), method: "post" })
        .then((res: IResponseModel<boolean>) => {
            if (res.state == 1000 && res.data) {
                if (receiptRowIndex >= 0) {
                    const item = tableData.value[receiptRowIndex];
                    if (item) {
                        item.receiptCount = newReceiptCount.toString();
                        receiptState !== null && (item.showFromBankReceiptMessage = "0");
                        const failReceiptCount = fileList.length - newReceiptCount;
                        if (fileList.length > newReceiptCount) {
                            ElNotify({
                                type: "warning",
                                message: `添加成功${newReceiptCount}个，失败${failReceiptCount}个。存在未查验通过的电子发票`,
                            });
                        }
                    }
                }
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
        })
        .finally(() => {
            receiptRowIndex = -1;
            isReceiptingRow = null;
            isNeedSaveToVoucherForDelete = false;
        });
}
async function checkNeedToastWithBillAndVoucher(createdDate: string, delFileids: string) {
    const url =
        journalType.value === "1020"
            ? "/api/Journal/GetNeedSaveToVoucherForSaveReceipt"
            : "/api/Journal/GetNeedSaveToVoucherForSaveTurnover";
    return await request({ url, method: "post", params: { createdDate, delFileids } })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000) return false;
            return res.data;
        })
        .catch(() => {
            return false;
        });
}

// 日期相关
function handleChangeDate(startDate: string, endDate: string, row: ITableItem) {
    handleEditDate(startDate, endDate);
    handleChangeRowDate(row);
}
function handleChangeRowDate(row: ITableItem, total?: number) {
    loading.value = true;
    setInfos();
    if (journalTableRef.value?.getPaginationData().currentPage !== 1) {
        journalTableRef.value?.debugCanChangeCurrentPage();
        journalTableRef.value?.setPaginationData({ currentPage: 1 });
    }
    if (total !== undefined) {
        const currentPage = Math.ceil(total / (journalTableRef.value?.getPaginationData().pageSize || 20));
        journalTableRef.value?.debugCanChangeCurrentPage();
        journalTableRef.value?.setPaginationData({ currentPage });
    }
    const queryParams = {
        LimitScm: "",
        pageIndex: journalTableRef.value?.getPaginationData().currentPage,
        pageSize: journalTableRef.value?.getPaginationData().pageSize,
    };
    const params = { ...getBaseParams() };
    request({ url: "/api/Journal/PagingList?" + getUrlSearchParams(queryParams), method: "post", data: params })
        .then((res: IResponseModel<{ total: number; rows: Array<ITableItem> }>) => {
            journalTableRef.value?.resetRowInfo();
            if (res.state !== 1000 || res.data.rows.length === 0) {
                tableData.value = [];
                journalTableRef.value?.setColumns();
                journalTableRef.value?.setPaginationData({ total: 0 });
                loading.value = false;
                return;
            }
            const data = res.data.rows;
            if (data[data.length - 1].cd_date !== "") {
                handleChangeRowDate(row, res.data.total);
                return;
            }
            checkInitAmountCanEdit();
            tableData.value = data.map((item: ITableItem, index: number) => {
                item.index = index;
                item.showAll = item.cd_date !== "" && item.ie_type !== "";
                item.onlyShowDelete = false;
                item.changeDate = false;
                item.defaultClickItem = false;
                item.changeDataPut = false;
                return item;
            });
            const newItem = tableData.value[tableData.value.length - 2];
            newItem.changeDate = true;
            row.changeDataPut = !row.onlyShowDelete && !!row.showAll;
            request({
                url: "/api/Journal/GetNumber?cdAccount=" + newItem.cd_account + "&date_e=" + newItem.cd_date,
                method: "post",
            })
                .then((res: any) => {
                    if (res.state !== 1000) return;
                    const result = res.data as number;
                    const replaceRow = getReplaceRow(row, result, newItem.index, newItem.cd_date, journalType.value === "1010" ? "1" : "2");
                    tableData.value.splice(-2, 1, replaceRow);
                    journalTableRef.value?.handleRowClick(tableData.value[tableData.value.length - 2], "description");
                })
                .finally(() => {
                    loading.value = false;
                });
        })
        .catch(() => {
            journalTableRef.value?.resetRowInfo();
            loading.value = false;
        })
        .finally(() => {
            setInfos();
        });
}
function handleEditDate(startDate: string, endDate: string) {
    searchInfo.startPid = startDate;
    searchInfo.endPid = endDate;
}
function disabledDateStart(time: Date) {
    const now = new Date();
    const year = now.getFullYear() + 5;
    const month = now.getMonth();
    const day = now.getDate();
    const maxDate = new Date(year, month, day);
    const accountStartDate = useAccountSetStore()?.accountSet!.asStartDate;
    const asStartDate = dayjs(accountStartDate).valueOf();
    return time.getTime() < asStartDate || time.getTime() > dayjs(maxDate).valueOf();
}
function disabledDateEnd(time: Date) {
    const now = new Date();
    const year = now.getFullYear() + 5;
    const month = now.getMonth();
    const day = now.getDate();
    const maxDate = new Date(year, month, day);
    return time.getTime() < dayjs(searchInfo.startPid).valueOf() || time.getTime() > dayjs(maxDate).valueOf();
}
function disabledStartVDate(time: Date) {
    const endVDate = dayjs(searchInfo.endVDate).valueOf();
    return time.getTime() > endVDate;
}
function disabledEndVDate(time: Date) {
    let startVDate = dayjs(searchInfo.startVDate).valueOf();
    return time.getTime() < startVDate;
}

// 修改了已生成凭证的日记账
const editVoucherShow = ref(false);
const confirmParams = reactive({
    pid: "",
    vid: "",
});
function resetConfirmParams() {
    confirmParams.pid = "";
    confirmParams.vid = "";
}
function handleConfirmSuccess() {
    const fcode = journalType.value === "1020" ? "depositjournal-cancreatevoucher" : "cashjournal-cancreatevoucher";
    if (!checkPermission([fcode]) && !isErp.value) {
        ElNotify({ type: "warning", message: "修改已生成凭证的日记账数据，需要删除凭证或拥有凭证编辑权限哦~" });
    } else {
        checkVoucherRef.value?.checkVoucher(Number(confirmParams.pid), Number(confirmParams.vid));
        editVoucherShow.value = false;
    }
}
function handleComfirmCancal() {
    editVoucherShow.value = false;
    ElNotify({ type: "success", message: "保存成功" });
    journalTableRef.value?.resetRowInfo();
    handleSearch();
}

// 生成凭证相关
const generateVoucherSetting = ref<IVoucherSetting>();
function handleToGenerateVoucher() {
    generateVoucherSetting.value = generateVoucherSettingDialogRef.value?.getVoucherSetting();
    journalGenerateVoucherRef.value?.setVoucherSettings(generateVoucherSetting.value ?? ({} as IVoucherSetting));
    journalGenerateVoucherRef.value?.genVoucherFromJournal(selectionList.value.slice());
}
function openGenVoucher() {
    currentSlot.value = "voucher";
}
function handleBackToJournal() {
    currentSlot.value = "main";
}
function genVoucherSaveSuccess() {
    handleBackToJournal();
    handleSearch();
    clearSelection();
}
function handleToJournalTemplate() {
    generateVoucherSettingRef.value?.init();
    currentSlot.value = "setting";
}
const voucherSettingDialogShow = ref(false);
function handleToJournalSettings() {
    voucherSettingDialogShow.value = true;
}
function toVoucherAbout() {
    currentSlot.value = "checkVoucher";
}
function genVoucherNoTemplate(ietype: number, ietypeId: number) {
    const type = ietype === 10050 ? "income" : "expenditure";
    generateVoucherSettingRef.value?.init(type, ietypeId);
    currentSlot.value = "setting";
}

// 左上角账户操作
const showDisabled = ref(getCookie("showDisabled") === "true");
let autoShowDisabled = false; // 为了联查自动打开的显示停用账户不保存在 cookie 里面
watch(showDisabled, (val) => {
    if (!autoShowDisabled) {
        setCookie("showDisabled", val.toString(), "d30");
        cdAccountList.value = calcCurrentCDAccountList();
        cdAccount.value === "all" && handleSearchAccountChange(cdAccount.value);
    }
    autoShowDisabled && (autoShowDisabled = false);
});
function handleChangeShowDisabled(value: boolean) {
    showDisabled.value !== value && (showDisabled.value = value);
}
const newCDAccountHtml = `<li class="new-cd-account">
        <a class="link">
            新增账户
        </a>
    </li>`;
const addAccountDialogShow = ref(false);
function newCdAccount() {
    let ac_name = autoAddName.value;
    if (window.isErp) {
        request({
            url: "/api/CDAccount/GetErpNewAccountCode",
            method: "post",
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                addAccountFormRef.value?.resetForm();
                addAccountFormRef.value?.editSearchInfo({ AC_NO: res.data }, ac_name);
                addAccountDialogShow.value = true;
            }
        });
    } else {
        const list = allCdAccountList.value;
        const total = list.length;
        let ac_no = total + 1;
        let ac_no_str = ac_no.toString().padStart(3, "0");
        const arr = list.map(function (o: any) {
            return o.ac_no;
        });
        while (arr.indexOf(ac_no_str) > -1) {
            ac_no = ac_no + 1;
            ac_no_str = ac_no.toString().padStart(3, "0");
        }
        addAccountFormRef.value?.resetForm();
        addAccountFormRef.value?.editSearchInfo({ AC_NO: ac_no_str }, ac_name);
        addAccountDialogShow.value = true;
    }
}

// 新增成功  重新获取数据
async function handleNewAccountSuccess(param: string) {
    addAccountDialogShow.value = false;
    window.dispatchEvent(new CustomEvent("reloadCDAccount", { detail: { journalType: journalType.value } }));
    await handleGetAccountList();
    const item = cdAccountList.value.find((item) => (isErp.value ? item.ac_id : item.ac_no) == param) as ICDAccountItem;
    cdAccount.value = item.ac_id;
    cdAccountDisplay.value = item.ac_no + " - " + item.ac_name;
}
async function handleNewIETypeSuccess(type: CashIEType, code: string, name: string) {
    await handleGetIETypeList();
    const value2 = (type === CashIEType.Income ? "收-" : "支-") + name;
    const value1 = code;
    const item = IETypeList.value.find((item) => item.value1 == value1 && item.value2 == value2) as IIETypeItem;
    journalTableRef.value?.changeEditInfo({ IE_TYPE: item.subkey });
}
function handleSaveProject(code: string, name: string) {
    handleSaveProjectOrDepartment(CashAAType.Project, code, name);
}
function handleSaveDepartment(code: string, name: string) {
    handleSaveProjectOrDepartment(CashAAType.Department, code, name);
}
function handleSaveProjectOrDepartment(aatype: CashAAType.Project | CashAAType.Department, code: string, name: string) {
    if (aatype === CashAAType.Department) {
        useAssistingAccountingStore().getAssistingAccounting();
        useAssistingAccountingStore()
            .getDepartment()
            .then(() => {
                const list = departmentList.value;
                const item = list.find((item) => item.aanum === code && item.aaname === name);
                journalTableRef.value?.changeEditInfo({ DEPARTMENT: item?.aaeid + "" || "" });
            });
        return;
    }
    useAssistingAccountingStore()
        .getAssistingAccounting()
        .then(() => {
            const list = projectList.value;
            const item = list.find((item) => item.aanum === code && item.aaname === name);
            journalTableRef.value?.changeEditInfo({ PROJECT: item?.aaeid + "" || "" });
        });
}
async function handleSavePayMethod(name: string) {
    await handleGetPaymentList();
    const payMethod = paymentMethodList.value.find((item) => item.value === name);
    const payMethodId = payMethod ? payMethod.id : "";
    journalTableRef.value?.changeEditInfo({ PAYMENT_METHOD: payMethodId });
}

// 多页签优化2.0
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const currentPath = ref(route.path);
watch(currentSlot, (val) => {
    const flag = checkIsEdited() || (val === "checkVoucher" && checkVoucherRef.value?.getEditedState());
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        (currentRouterModel as any).isEditting = flag;
    }
});
function checkIsEdited() {
    return currentSlot.value === "voucher" && journalGenerateVoucherRef.value?.checkIsEdited();
}
const isEditting = computed(() => {
    return (
        checkIsEdited() ||
        (currentSlot.value === "checkVoucher" && checkVoucherRef.value?.getEditedState() === true) ||
        (journalTableRef.value?.newJournalIsEdit as boolean)
    );
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});

const wordCutList = ref<Array<IWordCut>>([]);
function updateWordcut() {
    tryUpdateWordCut(WordCutCategory.Journal).then((res: IResponseModel<number>) => {
        if (res.state === 1000) {
            getWordCutList(WordCutCategory.Journal).then((res: IResponseModel<Array<IWordCut>>) => {
                if (res.state === 1000) {
                    //剔除已禁用的收支类别
                    wordCutList.value = res.data.filter((item) => Number(item.num3) !== 1);
                    for (const wordCut of wordCutList.value) {
                        wordCut.matchKeys = wordCut.value3.replace(/，/g, ",").split(",");
                        wordCut.matchParties = wordCut.text1.split(",");
                        wordCut.matchKeywords = wordCut.text2.split(",");
                    }
                }
            });
        }
    });
}

// 重新加载（多用于其他页签操作）
let cacheRouterQueryParams: any = null;
let firstInit = true;
function reLoadCurrentPage() {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if ((currentRouterModel as any).stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        (currentRouterModel as any).stop = true;
    }
}
async function reloadIETypeList(e: any) {
    const event: CustomEvent = e;
    if (props.journalType === event.detail.journalType) return;
    await handleGetIETypeList();
}
async function reloadCDAccount(e: any) {
    const event: CustomEvent = e;
    if (props.journalType === event.detail?.journalType) return;
    await handleGetAccountList();
    const item = cdAccountList.value.find((item) => item.ac_id === cdAccount.value);
    if (item) {
        cdAccountDisplay.value = item.ac_no + " - " + item.ac_name;
        if (item.ac_id === cdAccount.value) {
            useFc.value = item.standard === "1";
            handleSearch();
        }
    }
}
function isFromOtherPage(page: "voucherPage" | "CDCheckDetail" | "voucherList" | "ERecord") {
    if (!cacheRouterQueryParams?.from && route.query.from === page) {
        return true;
    }
    if (
        cacheRouterQueryParams?.from === page &&
        route.query?.from === page &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }
    return false;
}
async function handleInit() {
    handleInitAssistingAccounting(assistingAccountingList.value);
    await handleGetAccountList();
    await getCompany();
    if (!checkNeedChangeAccount()) return;
    updateWordcut();
    handleGetIETypeList();
    handleGetPaymentList();
    handleGetCurrencyList();
}
function reloadCheckoutDate() {
    if (cdAccount.value === "all") return;
    request({ url: "/api/Journal/GetCDJournalDate?cdAccount=" + cdAccount.value + "&jType=" + journalType.value, method: "post" }).then(
        (res: IResponseModel<IJournalDate>) => {
            checkDate.value = res.data.checkDate;
            checkoutDate.value = new Date(res.data.checkDate.replace(/-/g, "/"));
        }
    );
}

let hasMounted = false;
onMounted(async () => {
    await handleInit();
    hasMounted = true;
    window.addEventListener("reloadIETypeList", reloadIETypeList);
    window.addEventListener("reloadIETypeList", updateWordcut);
    window.addEventListener("reloadCurrency", handleGetCurrencyList);
    window.addEventListener("reloadCDAccount", reloadCDAccount);
    window.addEventListener("reCheckout", reloadCheckoutDate);
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop !== undefined) {
        const timer = setTimeout(() => {
            delete (currentRouterModel as any).stop;
            clearTimeout(timer);
        });
    }
});
onActivated(() => {
    if (!hasMounted) return;
    if (isFromOtherPage("voucherPage")) {
        if (!searchInfo.startPid || !searchInfo.endPid) return;
        handleSearch();
        return;
    }
    if (isFromOtherPage("CDCheckDetail") || isFromOtherPage("voucherList") || (isFromOtherPage("ERecord") && !firstInit)) {
        if (currentSlot.value === "checkVoucher" && checkVoucherRef.value?.getEditedState()) {
            editConfirm("voucherEdit", () => {}, reLoadCurrentPage, "日记账");
        } else if (isEditting.value) {
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
        return;
    }
    if (isErp.value 
        && route.query.searchStartDate 
        && cacheRouterQueryParams && 
        cacheRouterQueryParams.searchStartDate !== route.query.searchStartDate 
        && !firstInit
    ) {
        handleSearch();
    }
});
onUnmounted(() => {
    cacheRouterQueryParams = null;
    window.removeEventListener("reloadIETypeList", reloadIETypeList);
    window.removeEventListener("reloadIETypeList", updateWordcut);
    window.removeEventListener("reloadCurrency", handleGetCurrencyList);
    window.removeEventListener("reloadCDAccount", reloadCDAccount);
    window.removeEventListener("reCheckout", reloadCheckoutDate);
});
onBeforeRouteLeave((to, from, next) => {
    cacheRouterQueryParams = from.query;
    firstInit = false;
    next();
});
//表头字段模糊搜索
function isKeyOfIFSearchItem(key: string): key is keyof IFSearchItem {  
    return [
        'DESCRIPTION', 
        'NOTE', 
        'RECEIPT_NO', 
        'IE_TYPE',
        'PAYMENT_METHOD', 
        'OPPOSITE_PARTY', 
        'PROJECT', 
        'DEPARTMENT',
    ].includes(key);  
}
function filterSearch(prop: string, data: any) {
    if (isKeyOfIFSearchItem(prop)) {
        if (typeof data === "string" || typeof data === "number") {
            if (typeof filterSearchInfo[prop] === "string") {
                (filterSearchInfo[prop] as string) = data.toString();
            }
            if (typeof filterSearchInfo[prop] === "number") {
                (filterSearchInfo[prop] as number) = Number(data);
            } 
            if (prop === "OPPOSITE_PARTY") {
                searchInfo.OPPOSITE_PARTY = "";
            }
            handleConfirmSearch();
        } else {
            if (data.length > 0) {
                const filteredData: number[] = data.filter((item: any) => typeof item === 'number');  
                (filterSearchInfo[prop] as number[]) = filteredData;
                handleConfirmSearch();
            }
        } 
    } 
}
//拼音首字母筛选
//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
const accountListAll = ref<any[]>([]);
const showAccountList = ref<any[]>([]);
watchEffect(() => {
    accountListAll.value = JSON.parse(JSON.stringify(cdAccountList.value));
    accountListAll.value.push({ ac_id: "all", label: "全部账户" });
    showAccountList.value = JSON.parse(JSON.stringify(accountListAll.value));
});
const AccountMethod = (value: string) => {
    showAccountList.value = commonFilterMethod(value, accountListAll.value, 'label');
    autoAddName.value = showAccountList.value.length === 0 ? value.trim() : ""; 
}
//流水拖动位置
let canchScrollTop = ref(0);
function moveDrop(oldIndex: number, newIndex: number, scrollTop: number, evt: any, originScrollTop: number) {
    if (oldIndex === newIndex) {
        return;
    }
    const newRow = tableData.value[newIndex];
    //初始行/空行/合计行
    if ((newRow.income ==="" && newRow.expenditure ==="") || newRow.cd_date === "") {
        journalTableRef.value?.setScrollTop(originScrollTop);
        return;
    }
    const row = tableData.value[oldIndex];

    const preIndex = newIndex > oldIndex ? newIndex : newIndex - 1;
    const nextIndex = newIndex > oldIndex ? newIndex + 1 : newIndex;
    const preRow = preIndex >= 0 ? tableData.value[preIndex] : null;
    const nextRow = nextIndex <= (tableData.value.length - 1) ? tableData.value[nextIndex] : null;
    //相同流水日期行流水拖动
    const preDate = preRow && (formatDate(preRow.cd_date) === formatDate(row.cd_date)) ? preRow.created_date : "";
    const nextDate = nextRow && (formatDate(nextRow.cd_date) === formatDate(row.cd_date)) ? nextRow.created_date : "";
    //不同日期标志
    const preFlag = preRow && (formatDate(preRow.cd_date) === formatDate(row.cd_date));
    const nextFlag = nextRow && (formatDate(nextRow.cd_date) === formatDate(row.cd_date)); 
    if (!preFlag && !nextFlag) {
        journalTableRef.value?.setScrollTop(originScrollTop);
        ElNotify({
            type: "warning",
            message: "日记账流水只能在相同日期之间进行调整排序哦~",
        });
        return;
    }

    const params = {
        j_type: Number(journalType.value),
        cd_account: Number(row.cd_account),
        cd_date: row.cd_date,
        line_sn: Number(row.line_sn),
        created_date: row.created_date, //创建流水的日期
        move_previous_line_sn: preRow && preDate ? Number(preRow.line_sn) : 0,
        move_previous_created_date: preDate,
        move_next_line_sn: nextRow && nextDate ? Number(nextRow.line_sn) : 0,
        move_next_created_date: nextDate,
    };
    const url = journalType.value === "1020" ? "/api/Journal/DepositMove" : "/api/Journal/CashMove";
    loading.value = true;
    canchScrollTop.value = scrollTop;
    request({ 
        url, 
        method: "post", 
        data: params 
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({ type: "success", message: "移动成功" });
            window.dispatchEvent(new CustomEvent("reloadVoucherList"));
            tableData.value.length = 0;
            nextTick(() => {
                handleSearch();
            });
        } else {
            loading.value = false;
            // restOriginDom();
            let errorMsg = res.msg;
            if (res.msg === "期间已结账，无法移动") {
                errorMsg = "当前流水所属期间已结账，不可以进行排序调整哦";
            }
            ElNotify({ 
                type: "warning", 
                message: errorMsg, 
            });
        }
    });
    function restOriginDom() {
        const tagName = evt.item.tagName;
        const items = evt.from.getElementsByTagName(tagName);
        if (oldIndex > newIndex) {
            evt.from.insertBefore(evt.item, items[oldIndex + 1]);
        } else {
            evt.from.insertBefore(evt.item, items[oldIndex]);
        }
        journalTableRef.value?.setScrollTop(originScrollTop);
    }
}

</script>

<style lang="less" scoped>
@import "@/style/Cashier/CashJournal.less";
@import "@/style/SelfAdaption.less";
</style>
