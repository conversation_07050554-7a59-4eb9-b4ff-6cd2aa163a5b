<template>
  <div class="datagrid-pager pagination">
    <table
      cellspacing="0"
      cellpadding="0"
      border="0">
      <tbody>
        <tr>
          <td>
            <select
              class="pagination-page-list"
              v-model="num"
              @change="changeSize($event)">
              <option
                v-for="item in props.pageSizes"
                :key="item"
                :value="item">
                {{ item }}
              </option>
            </select>
          </td>
          <td>
            <div class="pagination-btn-separator"></div>
          </td>
          <td>
            <a
              href="javascript:void(0)"
              class="l-btn l-btn-small l-btn-plain"
              :class="pageNum > 1 ? '' : 'l-btn-disabled'"
              group=""
              id=""
              @click="clickFirst">
              <span class="l-btn-left l-btn-icon-left">
                <span class="l-btn-text l-btn-empty">&nbsp;</span>
                <span
                  ref="firstRef"
                  class="l-btn-icon pagination-first">
                  &nbsp;
                </span>
              </span>
            </a>
          </td>
          <td>
            <a
              href="javascript:void(0)"
              class="l-btn l-btn-small l-btn-plain"
              :class="pageNum > 1 ? '' : 'l-btn-disabled'"
              group=""
              id=""
              @click="clickPrev">
              <span class="l-btn-left l-btn-icon-left">
                <span class="l-btn-text l-btn-empty">&nbsp;</span>
                <span
                  ref="prevRef"
                  class="l-btn-icon pagination-prev">
                  &nbsp;
                </span>
              </span>
            </a>
          </td>
          <td>
            <div class="pagination-btn-separator"></div>
          </td>
          <td>
            <span style="padding-left: 6px">第</span>
          </td>
          <td>
            <input
              class="pagination-num"
              type="text"
              size="2"
              v-model="pageNumMid"
              @keyup.enter="jumpPage" />
          </td>
          <td>
            <span style="text-align: left; white-space: nowrap; line-height: 18px; padding-right: 6px">共{{ totalPage }}页</span>
          </td>
          <td>
            <div class="pagination-btn-separator"></div>
          </td>
          <td>
            <a
              href="javascript:void(0)"
              class="l-btn l-btn-small l-btn-plain"
              :class="pageNum < totalPage ? '' : 'l-btn-disabled'"
              @click="clickNext">
              <span class="l-btn-left l-btn-icon-left">
                <span class="l-btn-text l-btn-empty">&nbsp;</span>
                <span
                  ref="nextRef"
                  class="l-btn-icon pagination-next">
                  &nbsp;
                </span>
              </span>
            </a>
          </td>
          <td>
            <a
              href="javascript:void(0)"
              class="l-btn l-btn-small l-btn-plain"
              :class="pageNum < totalPage ? '' : 'l-btn-disabled'"
              @click="clickLast">
              <span class="l-btn-left l-btn-icon-left">
                <span class="l-btn-text l-btn-empty">&nbsp;</span>
                <span
                  prev="lastRef"
                  class="l-btn-icon pagination-last">
                  &nbsp;
                </span>
              </span>
            </a>
          </td>
          <td>
            <div class="pagination-btn-separator"></div>
          </td>
          <td>
            <a
              href="javascript:void(0)"
              class="l-btn l-btn-small l-btn-plain"
              @click="clickLoad">
              <span class="l-btn-left l-btn-icon-left">
                <span class="l-btn-text l-btn-empty">&nbsp;</span>
                <span class="l-btn-icon pagination-load">&nbsp;</span>
              </span>
            </a>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="pagination-info">
      显示 {{ showPageStartIndex }} 到 {{ showPageEndIndex }}，共 {{ props.total }} 条记录
      <span class="other-info"><slot name="pageOther"></slot></span>
    </div>
    <div style="clear: both"></div>
  </div>
</template>

<script setup lang="ts">
  import type { IPaginationProps, PaginationEmits } from "./types"
  const props = withDefaults(defineProps<IPaginationProps>(), {
    total: 0,
    pageSizes: () => [],
    pageSize: 0,
    currentPage: 0,
    customPageStartEnd: () => [],
  })
  //接收事件
  const emit = defineEmits<PaginationEmits>()
  //当前页
  const pageInject: number = inject("currentPage", props.currentPage) as number

  const pageNumMid = ref<number>(props.currentPage ?? pageInject)
  const pageNum = ref<number>(props.currentPage)
  //num为每页条数
  const num = ref<number>(props.pageSize)
  //显示开始数
  const startPage = computed(() => {
    if ((pageNum.value as number) >= 1 || pageNum.value <= totalPage.value) return num.value * pageNum.value - (num.value - 1)
    else return 0
  })
  const endPage = computed(() => {
    return num.value * pageNum.value
  })
  const totalPage = ref(Math.ceil(props.total / num.value))

  const showPageStartIndex = computed(() => {
    if (props.customPageStartEnd && props.customPageStartEnd.length === 2) {
      return props.customPageStartEnd[0]
    } else {
      return (props.total as number) ? startPage.value : 0
    }
  })

  const showPageEndIndex = computed(() => {
    if (props.customPageStartEnd && props.customPageStartEnd.length === 2) {
      return props.customPageStartEnd[1]
    } else {
      return endPage.value < props.total ? endPage.value : props.total
    }
  })

  watch(
    () => props.currentPage,
    (val) => {
      pageNum.value = val
      pageNumMid.value = val
    },
  )

  //每页条数改变
  const changeSize = (ev: Event) => {
    num.value = Number((ev.target as HTMLInputElement).value)
    if (pageNum.value > Math.ceil(props.total / num.value)) {
      pageNum.value = Math.ceil(props.total / num.value) ? Math.ceil(props.total / num.value) : 1
      pageNumMid.value = Math.ceil(props.total / num.value) ? Math.ceil(props.total / num.value) : 1
    }
    emit("size-change", { pageNum: pageNum.value, num: num.value })
  }

  //跳转到某页
  const jumpPage = () => {
    if ((pageNumMid.value as number) < 1 || pageNumMid.value > totalPage.value) {
      pageNumMid.value = 1
      pageNum.value = 1
    }
    if ((pageNumMid.value as number) > Math.ceil((props.total as number) / (num.value as number))) {
      pageNumMid.value = Math.ceil((props.total as number) / (num.value as number))
      pageNum.value = Math.ceil((props.total as number) / (num.value as number))
    }
    pageNum.value = pageNumMid.value
    emit("current-change", { pageNum: pageNum.value, num: num.value })
  }

  //点击第一页
  const clickFirst = () => {
    if (pageNum.value > 1) {
      pageNum.value = 1
      emit("current-change", { pageNum: pageNum.value, num: num.value })
    }
  }

  //点击上一页
  const clickPrev = () => {
    if (pageNum.value > 1) {
      pageNum.value = Number(pageNum.value) - 1
      pageNumMid.value = Number(pageNumMid.value) - 1
      emit("current-change", { pageNum: pageNum.value, num: num.value })
    }
  }

  //点击下一页
  const clickNext = () => {
    if (pageNum.value < totalPage.value) {
      if ((pageNum.value as number) < Math.ceil((props.total as number) / (num.value as number))) {
        pageNum.value = Number(pageNum.value) + 1
        pageNumMid.value = Number(pageNumMid.value) + 1
      }
      emit("current-change", { pageNum: pageNum.value, num: num.value })
    }
  }

  //点击最后一页
  const clickLast = () => {
    if (pageNum.value < totalPage.value) {
      pageNum.value = Math.ceil((props.total as number) / (num.value as number))
      emit("current-change", { pageNum: pageNum.value, num: num.value })
    }
  }

  //点击刷新
  const clickLoad = () => {
    pageNumMid.value = pageNum.value ? pageNum.value : 1
    emit("refresh")
  }

  watch(
    [() => props.total, () => props.pageSize],
    () => {
      num.value = props.pageSize
      totalPage.value = Math.ceil(props.total / num.value)
      pageNumMid.value = totalPage.value ? pageNum.value : 1
    },
    { immediate: true },
  )
</script>

<style lang="scss" scoped>
  .datagrid-pager {
    background-color: var(--white);
    display: block;
    margin: 0;
    border: 1px solid var(--border-color);
    border-top: none;
    &.pagination-info {
      color: var(--font-color);
      font-size: var(--h5);
      line-height: 30px;
    }
    table tr td .pagination-page-list {
      border: 1px solid var(--border-color);
      height: 18px;
      width: 54px;
      font-size: var(--h5);
      outline: none;
    }
  }
  .pagination table {
    float: right;
    height: 30px;
    width: 317.5px;
    font-size: 12px;

    td {
      height: 30px;
    }
  }
  .pagination-info {
    float: left;
    margin: 0 6px 0 0;
    padding: 0;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    .other-info {
      padding-left: 20px;
    }
  }

  .pagination-page-list {
    margin: 0px 6px;
    padding: 1px;
  }
  .pagination-num {
    border: 1px solid var(--border-color);
    height: 16px;
    width: 30px;
    padding: 0 2px;
    text-align: center;
    margin: 0 6px;
    outline: none;
  }
  .pagination td {
    border: 0;
  }
  .pagination .pagination-first {
    background-image: url("@/assets/Icons/first-disabled.png");
  }
  .pagination-prev {
    background-image: url("@/assets/Icons/prev-disabled.png");
  }
  .l-btn-icon {
    display: block;
    width: 26px;
    height: 26px;
    margin: 0 auto;
    position: relative;
    background-position: center;
    background-repeat: no-repeat;
    left: 0;
  }
  input[type="text"] {
    padding: 1px 2px;
  }
  .l-btn {
    .pagination-first {
      background-image: url("@/assets/Icons/first.png");
      &:hover {
        background-image: url("@/assets/Icons/first-hover.png");
      }
    }
    .pagination-first:hover {
      background-image: url("@/assets/Icons/first-hover.png");
    }
    .pagination-prev {
      background-image: url("@/assets/Icons/prev.png");
      &:hover {
        background-image: url("@/assets/Icons/prev-hover.png");
      }
    }
    .pagination-prev:hover {
      background-image: url("@/assets/Icons/prev-hover.png");
    }
    .pagination-next {
      background-image: url("@/assets/Icons/next.png");
      &:hover {
        background-image: url("@/assets/Icons/next-hover.png");
      }
    }
    .pagination-next:hover {
      background-image: url("@/assets/Icons/next-hover.png");
    }
    .pagination-last {
      background-image: url("@/assets/Icons/last.png");
      &:hover {
        background-image: url("@/assets/Icons/last-hover.png");
      }
    }
    .pagination-last:hover {
      background-image: url("@/assets/Icons/last-hover.png");
    }

    &.l-btn-disabled {
      .pagination-first {
        background-image: url("@/assets/Icons/first-disabled.png");
      }
      .pagination-prev {
        background-image: url("@/assets/Icons/prev-disabled.png");
      }
      & .pagination-next {
        background-image: url("@/assets/Icons/next-disabled.png");
      }
      .pagination-last {
        background-image: url("@/assets/Icons/last-disabled.png");
      }
    }
  }

  .pagination-load {
    background-image: url("@/assets/Icons/load.png");
    &:hover {
      background-image: url("@/assets/Icons/load-hover.png");
    }
  }
  .pagination-load:hover {
    background-image: url("@/assets/Icons/load-hover.png");
  }
  .l-btn-empty {
    display: none;
  }
  a {
    text-decoration: none;
  }
</style>
