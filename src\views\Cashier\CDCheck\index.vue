<template>
    <div class="content">
        <div class="title">核对总账</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <div class="period-container">
                                    <DatePicker
                                        v-model:startPid="searchInfo.startMonth"
                                        v-model:endPid="searchInfo.endMonth"
                                        :clearable="false"
                                        :editable="false"
                                        :dateType="'month'"
                                        :value-format="'YYYYMM'"
                                        :label-format="'YYYY年MM月'"
                                        :disabledDateStart="disabledDate"
                                        :disabledDateEnd="disabledDate"
                                        @user-change="stopCloseSearchPopover"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">账户：</div>
                            <div class="line-item-field">
                                <Select 
                                    v-model="AC_ID" 
                                    :teleported="false" 
                                    :fit-input-width="true" 
                                    :filterable="true"
                                    :filter-method="accountFilterMethod"
                                >
                                    <Option v-for="item in showAccountList" :key="item.AC_ID" :label="item.AC_NAME" :value="item.AC_ID" />
                                </Select>
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field">
                                <Select 
                                    v-model="fc_code" 
                                    :teleported="false" 
                                    :fit-input-width="true" 
                                    :filterable="true"
                                    :filter-method="fcFilterMethod"
                                >
                                    <Option v-for="item in showFcList" :key="item.fc_id" :label="item.fc_name" :value="item.fc_id" />
                                </Select>
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.SHOW_TYPE" label="只显示不平"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <div class="tool-tips" v-show="tipShow">
                        <img class="tool-tips-img" src="@/assets/Cashier/zhuyi.png" alt="" />
                        <div class="tool-tips-txt">现金和银行账户没有关联科目，无法展示核对总账数据哦~</div>
                        <a @click="toCDAccount" class="tool-tips-link">去关联科目>></a>
                    </div>
                </div>
                <div class="main-tool-right">
                    <a class="button mr-10" v-permission="['cdcheck-canprint']" @click="handlePrint"> 打印 </a>
                    <a class="button" v-permission="['cdcheck-canexport']" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center" :class="isErp ? 'erp-content' : ''">
                <div class="main-center-title">
                    <span>账户：{{ accountTitle }}</span>
                </div>
                <TreeTable
                    ref="treeTable"
                    row-key="asub"
                    other-field="ac_name"
                    first-label="项目"
                    view="CDCheck"
                    firstProp="name"
                    :data="tableData"
                    :columns="columns"
                    :loading="loading"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :stripe="false"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    @go-to-detail="goToDetailFromFirstCol"
                    :tableName="setModule"
                >
                    <template #initial>
                        <el-table-column 
                            label="期初余额" 
                            :min-width="220" 
                            align="right" 
                            header-align="right"
                            prop="initial"
                            :width="getColumnWidth(setModule, 'initial')"
                        >
                            <template #default="scope">
                                <a
                                    v-if="scope.row.name === '差异' && checkPermission(['cdcheckdetail-canview'])"
                                    class="link"
                                    @click="goToDetail(scope.row.asub)"
                                >
                                    {{ formatMoney(scope.row.initial) }}
                                </a>
                                <span v-else>{{ formatMoney(scope.row.initial) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column 
                            label="借方(收入)" 
                            :min-width="130" 
                            align="right" 
                            header-align="right"
                            prop="income"
                            :width="getColumnWidth(setModule, 'income')"
                        >
                            <template #default="scope">
                                <a
                                    v-if="scope.row.name === '差异' && checkPermission(['cdcheckdetail-canview'])"
                                    class="link"
                                    @click="goToDetail(scope.row.asub)"
                                >
                                    {{ formatMoney(scope.row.income_sum) }}
                                </a>
                                <span v-else>{{ formatMoney(scope.row.income_sum) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #credit>
                        <el-table-column 
                            label="贷方(支出)" 
                            :min-width="130" 
                            align="right" 
                            header-align="right"
                            prop="credit"
                            :width="getColumnWidth(setModule, 'credit')"
                        >
                            <template #default="scope">
                                <a
                                    v-if="scope.row.name === '差异' && checkPermission(['cdcheckdetail-canview'])"
                                    class="link"
                                    @click="goToDetail(scope.row.asub)"
                                >
                                    {{ formatMoney(scope.row.expenditure_sum) }}
                                </a>
                                <span v-else>{{ formatMoney(scope.row.expenditure_sum) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #total>
                        <el-table-column label="余额" :min-width="150" align="right" header-align="right" :resizable="false">
                            <template #default="scope">
                                <a
                                    v-if="scope.row.name === '差异' && checkPermission(['cdcheckdetail-canview'])"
                                    class="link"
                                    @click="goToDetail(scope.row.asub)"
                                >
                                    {{ formatMoney(scope.row.total) }}
                                </a>
                                <span v-else>{{ formatMoney(scope.row.total) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                </TreeTable>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "CDCheck",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, watchEffect } from "vue";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney } from "@/util/format";
import TreeTable from "@/components/Table/TreeTable.vue";
import { ElNotify } from "@/util/notify";
import { useRoute } from "vue-router";
import { checkPermission } from "@/util/permission";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IOptionItem, IAccountItem, ITableItem, IPeriodResponseData, IIPeriodData } from "./types";

import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { initStartOrEndMonth, getCurrentPeriodInfo, type IPeriodData } from "@/components/DatePicker/utils";
import { dayjs } from "element-plus";
import { commonFilterMethod } from "@/components/Select/utils";

const isErp = ref(window.isErp);
const route = useRoute();
let treeTable = ref();
const setModule = "CDCheck";
const columns = ref<Array<IColumnProps>>([
    { label: "项目", align: "left", headerAlign: "left", minWidth: 110, prop: "name", width: getColumnWidth(setModule, 'name') },
    { label: "名称", align: "left", headerAlign: "left", minWidth: 120, prop: "asub_name", width: getColumnWidth(setModule, 'asub_name') },
    { label: "币别", align: "left", headerAlign: "left", minWidth: 120, prop: "fc_name", width: getColumnWidth(setModule, 'fc_name') },
    { slot: "initial" },
    { slot: "income" },
    { slot: "credit" },
    { slot: "total" },
]);

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();

function stopCloseSearchPopover() {
    containerRef.value?.stopCloseSearchPopover();
}
const currentPeriodInfo = ref("");
const accountTitle = ref("所有");
const loading = ref(false);
const accountList = ref<IAccountItem[]>([]);
const fcList = ref<IOptionItem[]>([]);
const tableData = ref<ITableItem[]>([]);
const searchInfo = reactive({
    startPeriod: "",
    endPeriod: "",
    SHOW_TYPE: false,
    startMonth: "",
    endMonth: "",
});
const AC_ID = ref("0");
const fc_code = ref(0);

const getFcList = () => {
    const url = "/api/CDAccount/GetAccountFcInfo?acId=" + AC_ID.value;
    request({ url, method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            fcList.value = res.data;
            if (AC_ID.value == "0") {
                fcList.value.unshift({
                    fc_code: "",
                    fc_id: 0,
                    fc_name: "所有币别",
                })
                fc_code.value = 0;
            } else {
                fc_code.value = fcList.value[0].fc_id;
            }
        }
    });
};
const getAccountList = () => {
    request({
        url: "/api/CDAccount/Table",
    }).then((res: any) => {
        if (res.state == 1000) {
            accountList.value = JSON.parse(res.data);
            accountList.value.unshift({
                AC_ID: "0",
                AC_NAME: "所有"
            });
        }
    });
};
const tipShow = ref(false);
const judgeShow = () => {
    request({ url: "/api/CDAccount/ExistsAsub", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            tipShow.value = !res.data;
        }
    });
};
const handleInit = () => {
    getFcList();
    getAccountList();
    judgeShow();
};

handleInit();

// 获取年月拼接
const getYearMonth = (pid: string) => originPeriodData.value.find((item) => item.pid === Number(pid))?.key;
// const getYearMonth = (pid: string) => {
//     const date = periodData.value.find((item) => item.pid === Number(pid));
//     return `${date?.year}/${date?.sn}/1 0:00:00`
// };

const getParams = () => {
    return {
        ac_id: AC_ID.value,
        fc_code: fc_code.value,
        period_s: getYearMonth(searchInfo.startPeriod),
        period_e: getYearMonth(searchInfo.endPeriod),
        show_type: searchInfo.SHOW_TYPE ? 1 : 0,
        rows: paginationData.pageSize,
        page: paginationData.currentPage,
    };
};
// const tableDataGroup = ref<any[]>([]);
const handleSearch = () => { 
    searchInfo.startPeriod = periodData.value.find((item) => item.time === searchInfo.startMonth)?.pid + "";
    searchInfo.endPeriod = periodData.value.find((item) => item.time === searchInfo.endMonth)?.pid + "";  
    if (Number(searchInfo.startPeriod) > Number(searchInfo.endPeriod)) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哦" });
        return;
    }
    handleClose();
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, Number(searchInfo.startPeriod), Number(searchInfo.endPeriod) );
    accountTitle.value = AC_ID.value === "0" ? "所有" : (accountList.value.find((item) => item.AC_ID === AC_ID.value)?.AC_NAME as string);
    const params = getParams();

    loading.value = true;
    request({ url: "/api/CDCheck/PagingList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.rows;
                treeTable.value?.initData(tableData.value);
                paginationData.total = res.data.total;
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleClose = () => containerRef.value?.handleClose();
const handleReset = () => {
    AC_ID.value = "0";
    fc_code.value = 0;
};
const handlePrint = () => globalPrint("/api/CDCheck/Print?" + getUrlSearchParams(getParams()));
const handleExport = () => globalExport("/api/CDCheck/Export?" + getUrlSearchParams(getParams()));
const goToDetailFromFirstCol = (row: any) => {
    goToDetail(row.asub);
};
const goToDetail = (asub: string) => {
    const params = {
        startPid: searchInfo.startPeriod,
        endPid: searchInfo.endPeriod,
        asubId: asub,
        fc_code: fc_code.value,
        show_type: searchInfo.SHOW_TYPE ? 1 : 0,
        r: Math.random(),
    };
    globalWindowOpenPage("/Cashier/CDCheckDetail?" + getUrlSearchParams(params), "资金差异明细");
};
const toCDAccount = () => {
    if (window.isErp) {
        globalWindowOpenPage("/Erp/AsubRelationSettings", "科目关联设置");
    } else {
        globalWindowOpenPage("/Cashier/CDAccount", "账户设置");
    }
};

watch(AC_ID, getFcList);

function disabledDate(time: Date) {
    const start = periodData.value[periodData.value.length - 1]?.time ?? new Date();
    const end = periodData.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

//获取期间信息
const periodData = ref<Array<IPeriodData>>([]);
const originPeriodData = ref<Array<IIPeriodData>>([]);
request({
    url: "/api/CDCheck/GetPeriodList",
    method: "post",
}).then((res: IResponseModel<Array<IPeriodResponseData>>) => {
    if (res.state === 1000) {
        const initPid = Number(res.data[0].sn);
        const list = res.data.map((item, index) => {
            return {
                ...item,
                pid: initPid + index,
            };
        });
        originPeriodData.value = JSON.parse(JSON.stringify(list));
        periodData.value = list.reverse().map((item) => {
            return {
                year: item.year,
                sn: item.sn,
                pid: item.pid,
                time: item.year + "" + String(item.sn).padStart(2, "0"),
            };
        });
        const currentPeriod = list.find((item) => item.isActived);
        if (currentPeriod) {
            searchInfo.startPeriod = currentPeriod.pid + "";
            searchInfo.endPeriod = currentPeriod.pid + "";
        } else {
            searchInfo.startPeriod = periodData.value[0].pid + "";
            searchInfo.endPeriod = periodData.value[0].pid + "";
        }
        if (route.query.P_ID) {
            searchInfo.startPeriod = route.query.P_ID as string;
            searchInfo.endPeriod = route.query.P_ID as string;
        }
        let result = initStartOrEndMonth(periodData.value, Number(searchInfo.startPeriod), Number(searchInfo.endPeriod));
        searchInfo.startMonth = result.startMonth;
        searchInfo.endMonth = result.endMonth;
        handleSearch();
    }
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    handleSearch();
});

const showAccountList = ref<Array<IAccountItem>>([]);
const showFcList = ref<Array<IOptionItem>>([]);
watchEffect(() => { 
    showAccountList.value = JSON.parse(JSON.stringify(accountList.value));
    showFcList.value = JSON.parse(JSON.stringify(fcList.value));  
});
function accountFilterMethod(value: string) {
    showAccountList.value = commonFilterMethod(value, accountList.value, 'AC_NAME');
}
function fcFilterMethod(value: string) {
    showFcList.value = commonFilterMethod(value, fcList.value, 'fc_name');
}
</script>

<style lang="less" scoped>
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";
.content {
    .main-content {
        background-color: var(--white);
        :deep(.el-table) {
            .el-table__row.table-row {
                font-weight: bold;
            }
        }
        .main-center {
            display: flex;
            flex-direction: column;
            &.erp-content {
                padding-bottom: 0;
                margin-bottom: 20px;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                :deep(.tree-table) {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    .el-table {
                        flex: 1;
                    }
                }
                & .main-center-title {
                    border-bottom: 0px;
                }
            }
        }
        & .main-center-title {
            border: 1px solid var(--border-color);
            padding-left: 10px;
            text-align: left;
            height: 36px;
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 36px;
        }
    }
}
div.line-item-field {
    .detail-el-select(132px);
    .period-container {
        display: flex;
        align-items: center;

        .period-separative {
            margin-left: 10px;
            margin-right: 10px;
        }
    }
}
.main-top {
    .left {
        display: flex;
        align-items: center;
    }
}
.tool-tips {
    font-family: 微软雅黑 !important;
    color: Gray;
    font-size: 12px !important;
    margin-left: 40px;
    height: 24px;
    display: flex;
    align-items: center;
    .tool-tips-img {
        vertical-align: top;
    }
    .tool-tips-txt {
        vertical-align: top;
        color: #999999;
        font-weight: bold;
        margin-left: 5px;
    }
    .tool-tips-link {
        vertical-align: top;
        color: var(--blue);
        font-weight: bold;
        margin-left: 5px;
        cursor: pointer;
        text-decoration: none;
    }
}
.line-item {
    height: 32px !important;
}
:deep(.el-table--enable-row-transition .el-table__body td.el-table__cell) {
    transition: none !important;
}
</style>
