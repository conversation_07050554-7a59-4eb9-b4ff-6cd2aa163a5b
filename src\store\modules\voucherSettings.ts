import { ref } from "vue";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import store from "@/store";
import { getVoucherSettingsApi, getVoucherPrintSettingsApi, type IVoucherSettings } from "@/api/voucherSettings";
import { request, type IResponseModel } from "@/util/service";
import { getFormatterDate } from "@/util/date";
import { type IPrintInfo, copyPrintInfo, printInfo, setVoucherSeniorPrintInfo } from "@/components/Voucher/utils";
import { globalWindowOpenPage } from "@/util/url";
import { ElAlert } from "@/util/confirm";

export const useVoucherSettingsStore = defineStore("voucherSettings", () => {
    const voucherSettings = ref<IVoucherSettings>();
    const cacheCheckSeniorSettings  = ref(false);
    const checkSeniorBool = ref(true)

    const getVoucherSettings = () => {
        return new Promise<IVoucherSettings>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getVoucherSettingsApi()
                    .then((res: any) => {
                        const data = res as IResponseModel<IVoucherSettings>;
                        if (data.state === 1000) {
                            voucherSettings.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });

                let seniorPrintInfo = {
                    ...printInfo,
                    isShowPrintDate: false,
                    isShowPageNumber: false,
                    isShowSplitLine: true,
                    isSplitPageByVNum: false,
                    printDateText: "",
                    isLineHeightAdaptive: false,
                    isHideEmpty: false,
                    currentVersion: 0,
                };
                getVoucherPrintSettingsApi().then((res: any) => {
                    if (res.state === 1000 && res.data.settingsId !== 0) {
                        seniorPrintInfo = { ...seniorPrintInfo, ...res.data };
                        seniorPrintInfo.printDateText = getFormatterDate("", "-");
                        seniorPrintInfo.printType = Number(res.data.pageType);
                        seniorPrintInfo.isPrintFrontCover = res.data.isPrintTitlePage;
                        seniorPrintInfo.printFile = res.data.isIncludeFile;
                        seniorPrintInfo.simultaneouslyPrintFileList = res.data.includeManifest;
                        seniorPrintInfo.continuousFiles = res.data.isContinuePrint;
                        seniorPrintInfo.isShowAssitItem = res.data.isShowSummaryAssist;
                        const { currentVersion, ...innerSeniorPrintInfo } = seniorPrintInfo;
                        localStorage.setItem("updateTipShow", "false");
                        setVoucherSeniorPrintInfo(innerSeniorPrintInfo, currentVersion, 1);
                    } else {
                        const storePrintInfoStr = localStorage.getItem("voucherPrintInfo");
                        if (storePrintInfoStr) {
                            const storePrintInfo = JSON.parse(storePrintInfoStr) as IPrintInfo;
                            const copyInfo = copyPrintInfo(storePrintInfo, printInfo);
                            seniorPrintInfo = {
                                ...copyInfo,
                                isShowPrintDate: false,
                                isShowPageNumber: false,
                                isShowSplitLine: true,
                                isSplitPageByVNum: false,
                                printDateText: "",
                                isLineHeightAdaptive: false,
                                isHideEmpty: false,
                                currentVersion: 0,
                            };
                        }
                        localStorage.setItem("updateTipShow", "true");
                        const { currentVersion, ...innerSeniorPrintInfo } = seniorPrintInfo;
                        setVoucherSeniorPrintInfo(innerSeniorPrintInfo, currentVersion, 0);
                    }
                });
            }
        });
    };
    
    const checkSeniorSettings = (printType:number|string) => {
        const showAlertAndOpenPage = () => {
            ElAlert({
                message: "高级打印模板出现错误，请重置模板",
                options: { confirmButtonText: "重置模板", cancelButtonText: "取消" },
            }).then((r) => {
                if (r) {
                    globalWindowOpenPage("/Voucher/SeniorVoucherPrint?collapse=5&isReset=true", "凭证打印设置");
                }
            });
        };
        
        if (cacheCheckSeniorSettings.value) {
            if (!checkSeniorBool.value) {
                showAlertAndOpenPage();
            }
            return Promise.resolve(checkSeniorBool.value);
        }
    
        return new Promise<boolean>((resolve) => {
            request({
                url: window.printHost + "/api/Voucher/CheckSeniorSettings",
                params: { printType },
                method: "post"
            }).then((res: IResponseModel<boolean>) => {
                cacheCheckSeniorSettings.value = true;
                checkSeniorBool.value = res.data;
                if (!res.data) {
                    showAlertAndOpenPage();
                }
                resolve(res.data);
            });
        });
    };
    

    return { voucherSettings, getVoucherSettings, checkSeniorSettings, cacheCheckSeniorSettings };
});

/** 在setup外使用 */
export function useVoucherSettingsStoreHook() {
    return useVoucherSettingsStore(store);
}
