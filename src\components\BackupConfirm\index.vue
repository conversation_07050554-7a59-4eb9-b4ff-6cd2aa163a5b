<template>
    <Confirm :hideCancel="true" :options="options" :confirm="confirm" :cancel="cancel" :close="close">
        账套数据较多，系统正在帮您自动备份，可到
        <span @click="toOtherPage" :class="{ link: checkHasPermission() }">{{ getBackupText() }}</span>
        中查看备份记录，备份成功后再来操作哦~
    </Confirm>
</template>

<script setup lang="ts">
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage } from "@/util/url";
import Confirm from "../Confirm/index.vue";

const options = {
    confirmButtonText: "知道了",
    cancelButtonText: "",
};
function confirm() {}
function cancel() {}
function close() {}
function getBackupText() {
    if (window.isAccountingAgent) {
        return "【电子档案-备份与恢复】";
    } else if (window.isErp) {
        return "【系统设置-备份与恢复】";
    } else {
        return "【设置-备份恢复】";
    }
}
function checkHasPermission() {
    if (window.isAccountingAgent) {
        return false;
    } else {
        return checkPermission(["backup-canview"]);
    }
}
function toOtherPage() {
    if (!checkHasPermission()) return;
    const path = window.isErp ? "/backup" : "/Settings/Backup";
    const title = window.isErp ? "备份与恢复" : "备份恢复";
    globalWindowOpenPage(path, title);
}
</script>

<style lang="less" scoped></style>
