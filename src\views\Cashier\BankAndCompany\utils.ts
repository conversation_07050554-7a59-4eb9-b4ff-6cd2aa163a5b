import { BankType } from "@/constants/bankKey";
import { ElNotify } from "@/util/notify";
import type { IAutoBankParams, IBankAccount } from "./types";
import { replaceAll } from "@/util/common";
import { request, type IResponseModel } from "@/util/service";
export const copyText = (text: string): void => {
    const copyTextarea = document.createElement("textarea"); // 创建一个 textarea 元素
    copyTextarea.value = text; // 将需要复制的文本赋值给 textarea 元素
    document.body.appendChild(copyTextarea); // 将 textarea 元素添加到文档中
    copyTextarea.select(); // 选择需要复制的文本
    document.execCommand("copy"); // 执行复制命令
    document.body.removeChild(copyTextarea); // 删除 textarea 元素
};

export const handleCheck = (autoBankForm: IAutoBankParams, bankType: BankType) => {
    const bankNo = replaceAll(autoBankForm.mainaccno, " ", "");
    const reg = /^-?[\da-zA-Z]+$/;
    if (autoBankForm.accname.trim() === "") {
        ElNotify({ type: "warning", message: "请设置公司名称" });
        return false;
    }
    if (autoBankForm.uscc.trim() === "") {
        ElNotify({ type: "warning", message: "请设置统一社会信用代码" });
        return false;
    }
    const taxNumberPattern = /^(([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10})|([0-9X]{15})|([0-9X]{17})|([0-9X]{20}))$/;
    if (!taxNumberPattern.test(autoBankForm.uscc)) {
        ElNotify({ type: "warning", message: "亲，统一社会信用代码格式有误，请认真检查！" });
        return false;
    }
    if (bankType === BankType.CMB) {
        if (autoBankForm.enterprise_number?.trim() === "") {
            ElNotify({ type: "warning", message: "请设置企业编号" });
            return;
        }
        if (autoBankForm.uid?.trim() === "") {
            ElNotify({ type: "warning", message: "请设置用户编号" });
            return;
        }
        if (!reg.test(autoBankForm.uid || "")) {
            ElNotify({ type: "warning", message: "用户编号只能输入数字字母，请检查" });
            return;
        }
    }
    if (autoBankForm.acid.trim() === "") {
        ElNotify({ type: "warning", message: "请选择银行账户" });
        return false;
    }
    if (bankNo === "") {
        ElNotify({ type: "warning", message: "请设置银行账号" });
        return false;
    }
    if (!reg.test(bankNo)) {
        const msg = bankType === BankType.CGB ? "银行账号只能输入数字，请检查" : "银行账号只能输入数字字母，请检查";
        ElNotify({ type: "warning", message: msg });
        return false;
    }
    return true;
};

export const appendStyle = (msg: string) => {
    return "<div style='text-align: left;text-align-last: center;white-space: normal;word-break: break-all;'>" + msg + "</div>";
};

export enum WeBankStatus {
    // 未签约
    None = 0,
    // 待审批
    UnApproved = 1,
    // 签约处理中
    SignProcessing = 2,
    // 已签约
    Signed = 3,
    // 已拒绝
    Refused = 4,
    // 已失败
    Failed = 5,
    // 解约处理中
    CancelProcessing = 6,
    // 已解约
    Cancelled = 7,
    // 已过期
    Expired = 8,
    // 失败
    BusinessFail = 9,
    // 全提示
    AllTips = 10,
}
//目前招商和民生银行签约（后续其他银行都将调用它）
export const upgradeApi = (props: any, autoBankForm: any, bankAccountName = "") => {
    const params = {
        bankType: props.bankType,
        acId: autoBankForm.acid,
        companyName: autoBankForm.accname,
        unifiedNumber: autoBankForm.uscc,
        bankAccountNo: replaceAll(autoBankForm.mainaccno, " ", ""),
        ...getAttachJson(props.bankType, autoBankForm, bankAccountName),
    };

    const url = "/api/BankSign/Sign";
    return request({
        url,
        data: params,
        method: "post",
    });
};
// 获取附加参数
const getAttachJson = (bankType: BankType, autoBankForm: any, bankAccountName: string) => {
    switch (bankType) {
        case BankType.CMB: // 招行
            return {
                attachJson: JSON.stringify({
                    enterpriseNumber: autoBankForm.enterprise_number,
                    uid: autoBankForm.uid,
                })
            };
        case BankType.BOC: // 中行
            return {
                attachJson: JSON.stringify({
                    customeNumber: replaceAll(autoBankForm.custno, " ", ""),
                    bankAccountName: bankAccountName,
                })
            };
        default:
            return {};
    }
};
export const getSuccessMsg = (autoBankForm: any, bankAccountList: IBankAccount[]) => {
    const acctNo = replaceAll(autoBankForm.mainaccno, " ", "");
    const bankItem = bankAccountList.find((item) => item.ac_id == autoBankForm.acid) as IBankAccount;
    const acname = bankItem.ac_no + "-" + bankItem.ac_name;
    const successMsg =
        "本银行账户“" + acname + "”账号“" + acctNo + "”已成功开通银企互联功能，签约后的每一笔款项都会实时自动录入该银行日记账哦~";
    return {
        acctNo,
        acname,
        successMsg,
        bankAccountName: bankItem.ac_name,
    };
}
