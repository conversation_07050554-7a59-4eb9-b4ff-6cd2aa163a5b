import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAssistingAccountingStoreHook } from "@/store/modules/assistingAccouting";
import { formatMoney } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AssistCombineState";
export enum TrialBalanceOptionsEnum {
    initialDebit = 1,
    initialCredit = 2,
    debit = 3,
    credit = 4,
    totalDebit = 5,
    totalCredit = 6,
    yearDebit = 7,
    yearCredit = 8,
    changeout = 9,
}

export const trialBalanceOptions = [
    { id: TrialBalanceOptionsEnum.initialDebit, name: "期初余额借方", slot: "initialDebit" },
    { id: TrialBalanceOptionsEnum.initialCredit, name: "期初余额贷方", slot: "initialCredit" },
    { id: TrialBalanceOptionsEnum.debit, name: "本期发生借方", slot: "debit" },
    { id: TrialBalanceOptionsEnum.credit, name: "本期发生贷方", slot: "credit" },
    { id: TrialBalanceOptionsEnum.totalDebit, name: "期末余额借方", slot: "totalDebit" },
    { id: TrialBalanceOptionsEnum.totalCredit, name: "期末余额贷方", slot: "totalCredit" },
    { id: TrialBalanceOptionsEnum.yearDebit, name: "本年累计借方", slot: "yearDebit" },
    { id: TrialBalanceOptionsEnum.yearCredit, name: "本年累计贷方", slot: "yearCredit" },
    { id: TrialBalanceOptionsEnum.changeout, name: "损益发生额", slot: "changeout" },
];

export function getCloumns(
    showSubject: boolean,
    customTralBalanceOptions: number[],
    fcid: number,
    mainAAType: number,
    subAAType: number,
    subjects: Array<string>
) {
    const assistingAccoutingTypeList = useAssistingAccountingStoreHook().assistingAccountingTypeList;
    const columns: Array<IColumnProps> = new Array().slice();

    const mainAATypeName = assistingAccoutingTypeList.find((item) => item.aaType === mainAAType)?.aaTypeName || "";
    const subAATypeName = assistingAccoutingTypeList.find((item) => item.aaType === subAAType)?.aaTypeName || "";
    const aatype_list: Array<IColumnProps> = [
        { prop: "firstAACode", label: mainAATypeName + "编码", align: "left", headerAlign: "left", minWidth: 80, width: getColumnWidth(setModule, "firstAACode") },
        { prop: "firstAAName", label: mainAATypeName + "名称", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, "firstAAName") },
        { prop: "secondAACode", label: subAATypeName + "编码", align: "left", headerAlign: "left", minWidth: 80, width: getColumnWidth(setModule, "secondAACode") },
        { prop: "secondAAName", label: subAATypeName + "名称", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, "secondAAName") },
    ];
    columns.push(...aatype_list);

    fcid > 1 && columns.push({ prop: "fcCode", label: "币别", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, "fcCode") });
    if (!showSubject) {
        const initialSlotName = getColumnsSlot("initial", customTralBalanceOptions);
        const currentSlotName = getColumnsSlot("current", customTralBalanceOptions);
        const totalSlotName = getColumnsSlot("total", customTralBalanceOptions);
        const yearSlotName = getColumnsSlot("year", customTralBalanceOptions);
        const rows: Array<string> = [initialSlotName, currentSlotName, totalSlotName, yearSlotName];

        rows.forEach((slot) => {
            slot && (fcid > 1 ? columns.push({ slot: slot + "_fc" }) : columns.push({ slot }));
        });

        const chooseChangeout = customTralBalanceOptions.includes(TrialBalanceOptionsEnum.changeout);
        const resetColumns = modifyColumns(columns.slice(), 0, fcid > 1, chooseChangeout);

        return resetColumns;
    } else {
        const baseColumns: Array<IColumnProps> = [];
        trialBalanceOptions.forEach((item) => {
            if (customTralBalanceOptions.includes(item.id)) {
                baseColumns.push({
                    slot: item.slot,
                    label: item.name,
                });
            }
        });

        subjects.forEach((subject, index) => {
            const subjectColumnChildren: Array<IColumnProps> = baseColumns.map((item: any) => {
                return {
                    label: item.label,
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    prop: item.slot,
                    formatter(row) {
                        return formatMoney(row.items[index][item.slot]);
                    }, 
                    width: getColumnWidth(setModule, item.slot)
                };
            });

            const subjectColumnMoreFcChildren: Array<IColumnProps> = baseColumns.map((item: any) => {
                return {
                    label: item.label,
                    align: "center",
                    headerAlign: "center",
                    minWidth: 120,
                    children: [
                        {
                            label: "原币",
                            align: "right",
                            headerAlign: "right",
                            minWidth: 120,
                            formatter(row) {
                                return formatMoney(row.items[index][item.slot + "Fc"]);
                            },
                            prop: item.slot,
                            width: getColumnWidth(setModule, item.slot)
                        },
                        {
                            label: "本位币",
                            align: "right",
                            headerAlign: "right",
                            minWidth: 120,
                            formatter(row) {
                                return formatMoney(row.items[index][item.slot]);
                            },
                            prop: item.slot,
                            width: getColumnWidth(setModule, item.slot)
                        },
                    ],
                };
            });
            columns.push({
                label: subject,
                align: "center",
                headerAlign: "center",
                children: fcid > 1 ? subjectColumnMoreFcChildren.slice() : subjectColumnChildren.slice(),
            });
        });

        return columns;
    }
}

function getColumnsSlot(type: "initial" | "current" | "total" | "year", customTralBalanceOptions: number[]) {
    let slotName = "";
    if (type === "initial") {
        if (isInCustomOptions(TrialBalanceOptionsEnum.initialDebit) && isInCustomOptions(TrialBalanceOptionsEnum.initialCredit)) {
            slotName = "initialAount";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.initialDebit)) {
            slotName = "initialDebit";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.initialCredit)) {
            slotName = "initialCredit";
        }
    } else if (type === "current") {
        if (isInCustomOptions(TrialBalanceOptionsEnum.debit) && isInCustomOptions(TrialBalanceOptionsEnum.credit)) {
            slotName = "amount";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.debit)) {
            slotName = "debit";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.credit)) {
            slotName = "credit";
        }
    } else if (type === "total") {
        if (isInCustomOptions(TrialBalanceOptionsEnum.totalDebit) && isInCustomOptions(TrialBalanceOptionsEnum.totalCredit)) {
            slotName = "totalAmount";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.totalDebit)) {
            slotName = "totalDebit";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.totalCredit)) {
            slotName = "totalCredit";
        }
    } else if (type === "year") {
        if (isInCustomOptions(TrialBalanceOptionsEnum.yearDebit) && isInCustomOptions(TrialBalanceOptionsEnum.yearCredit)) {
            slotName = "yearAmount";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.yearDebit)) {
            slotName = "yearDebit";
        } else if (isInCustomOptions(TrialBalanceOptionsEnum.yearCredit)) {
            slotName = "yearCredit";
        }
    }

    return slotName;

    function isInCustomOptions(id: TrialBalanceOptionsEnum) {
        return customTralBalanceOptions.includes(id);
    }
}

function modifyColumns(columns: Array<IColumnProps>, columnIndex: number, showFc: boolean, chooseChangeout: boolean) {
    if (!showFc) {
        columns.forEach((item: any, index) => {
            const trialBalanceIndex = trialBalanceOptions.findIndex((option) => option.slot === item.slot);
            if (trialBalanceIndex !== -1) {
                const trialBalanceItem = trialBalanceOptions[trialBalanceIndex];
                columns.splice(index, 1, {
                    label: trialBalanceItem.name,
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter(row) {
                        return formatMoney(row.items[columnIndex][trialBalanceItem.slot]);
                    },
                    prop: trialBalanceItem.slot,
                    width: getColumnWidth(setModule, trialBalanceItem.slot)
                });
            }
        });

        if (chooseChangeout) {
            columns.push({
                prop: "items",
                label: "损益发生额",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                formatter(row) {
                    return formatMoney(row.items[columnIndex].changeout);
                },
                width: getColumnWidth(setModule, "items"),
            });
        }
    } else {
        const initialAmountIndex = columns.findIndex((item) => item.slot === "initialAount_fc");
        if (initialAmountIndex > -1) {
            columns.splice(initialAmountIndex, 1, { slot: "initialDebit_fc" }, { slot: "initialCredit_fc" });
        }
        const amountIndex = columns.findIndex((item) => item.slot === "amount_fc");
        if (amountIndex > -1) {
            columns.splice(amountIndex, 1, { slot: "debit_fc" }, { slot: "credit_fc" });
        }
        const totalAmountIndex = columns.findIndex((item) => item.slot === "totalAmount_fc");
        if (totalAmountIndex > -1) {
            columns.splice(totalAmountIndex, 1, { slot: "totalDebit_fc" }, { slot: "totalCredit_fc" });
        }
        const yearAmountIndex = columns.findIndex((item) => item.slot === "yearAmount_fc");
        if (yearAmountIndex > -1) {
            columns.splice(yearAmountIndex, 1, { slot: "yearDebit_fc" }, { slot: "yearCredit_fc" });
        }

        columns.forEach((item: any, index) => {
            const trialBalanceIndex = trialBalanceOptions.findIndex((option) => option.slot + "_fc" === item.slot);
            if (trialBalanceIndex !== -1) {
                const trialBalanceItem = trialBalanceOptions[trialBalanceIndex];
                columns.splice(index, 1, {
                    label: trialBalanceItem.name,
                    align: "center",
                    headerAlign: "center",
                    minWidth: 120,
                    children: [
                        {
                            label: "原币",
                            align: "right",
                            headerAlign: "right",
                            minWidth: 120,
                            formatter(row) {
                                return formatMoney(row.items[columnIndex][trialBalanceItem.slot + "Fc"]);
                            },
                            prop: trialBalanceItem.slot,
                            width: getColumnWidth(setModule, trialBalanceItem.slot),
                        },
                        {
                            label: "本位币",
                            align: "right",
                            headerAlign: "right",
                            minWidth: 120,
                            formatter(row) {
                                return formatMoney(row.items[columnIndex][trialBalanceItem.slot]);
                            },
                            prop: trialBalanceItem.slot,
                            width: getColumnWidth(setModule, trialBalanceItem.slot),
                        },
                    ],
                });
            }
        });

        if (chooseChangeout) {
            columns.push({
                prop: "items",
                label: "损益发生额",
                align: "center",
                headerAlign: "center",
                minWidth: 120,
                children: [
                    {
                        prop: "changeoutFc",
                        label: "原币",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 120,
                        formatter(row) {
                            return formatMoney(row.items[columnIndex].changeoutFc);
                        },
                        width: getColumnWidth(setModule, "changeoutFc"),
                    },
                    {
                        prop: "changeout",
                        label: "本位币",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 120,
                        formatter(row) {
                            return formatMoney(row.items[columnIndex].changeout);
                        },
                        width: getColumnWidth(setModule, "changeout"),
                    },
                ],
            });
        }
    }

    return columns;
}
