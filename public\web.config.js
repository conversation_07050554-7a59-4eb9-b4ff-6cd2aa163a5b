(function (w) {
    w.config = {
        jLocalUrl: "https://j300test1.ningmengyun.com",
        jLocalH5Url: "http://localhost:5173",
        jLocalApiUrl: "https://japitest.ningmengyun.com",
        // jLocalApiUrl: "http://localhost:44335",
        jLocalAccountBooksUrl: "https://asyncfileprocesstest.ningmengyun.com",
        jLocalLmDiskUrl: "https://lemondisktest.ningmengyun.com:4446",
        jmLocalUrl: "https://jmtest.ningmengyun.com",
        jLocalTaxUrl: "https://jtaxapitest.ningmengyun.com",
        localShareReportUrl: "http://mtest1.ningmengyun.com",

        jUrl: "https://j300test1.ningmengyun.com",
        jH5Url: "https://jh5test.ningmengyun.com",
        jApiUrl: "https://japitest.ningmengyun.com",
        jAccountBooksUrl: "https://asyncfileprocesstest.ningmengyun.com",
        jLmDiskUrl: "https://lemondisktest.ningmengyun.com:4446",
        jmUrl: "https://jmtest.ningmengyun.com",
        jTaxUrl: "https://jtaxapitest.ningmengyun.com",
        freeShareReportUrl: "http://mtest1.ningmengyun.com",
        // freeImportFromOther: "https://daozhangapi.ningmengyun.com",
        freeImportFromOther: "https://accimportertest2.ningmengyun.com",
        jPrintUrl: "https://jprinttest.ningmengyun.com",
        jReceiptImportUrl: "https://receiptimporttest.ningmengyun.com:4446",

        jAAUrl: "https://jaatest.ningmengyun.com:4446",
        jAAH5Url: "https://jaah5test.ningmengyun.com",
        jAAApiUrl: "https://jaaapitest.ningmengyun.com",
        jAAAccountBooksUrl: "https://aaasyncfileprocesstest.ningmengyun.com",
        jAALmDiskUrl: "https://djlemondisktest.ningmengyun.com:4446",
        jmAAUrl: "https://jmaatest.ningmengyun.com",
        jAATaxUrl: "https://jaataxapitest.ningmengyun.com",
        aaShareReportUrl: "http://mtest1.ningmengyun.com",
        aaFreeImportFromOther: "https://aaimportertest2.ningmengyun.com/",
        jAAprintUrl: "https://jaaprinttest.ningmengyun.com",
        jAAReceiptImportUrl: "https://aareceiptimporttest.ningmengyun.com:4446",

        jAABossUrl: "https://jaabosstest.ningmengyun.com:4446",
        jAABossH5Url: "https://jaabossh5test.ningmengyun.com",
        jAABossApiUrl: "https://jaabossapitest.ningmengyun.com",
        jAABossAccountBooksUrl: "https://aaasyncfileprocesstest.ningmengyun.com",
        jmAABossUrl: "https://jmaabosstest.ningmengyun.com",

        jProUrl: "https://jprotest1.ningmengyun.com",
        jProH5Url: "https://jproh5test.ningmengyun.com",
        jProApiUrl: "https://jproapitest.ningmengyun.com",
        jProAccountBooksUrl: "https://proasyncfileprocesstest.ningmengyun.com",
        jProLmDiskUrl: "https://prolemondisktest.ningmengyun.com:4446",
        jmProUrl: "https://jmprotest.ningmengyun.com",
        jProTaxUrl: "https://jprotaxapitest.ningmengyun.com",
        proShareReportUrl: "http://mtest1.ningmengyun.com",
        // proImportFromOther: "https://prodaozhangapi.ningmengyun.com",
        proImportFromOther: "https://proimportertest2.ningmengyun.com",
        jProPrintUrl: "https://jproprinttest.ningmengyun.com",
        jProReceiptImportUrl: "https://proreceiptimporttest.ningmengyun.com:4446",

        jAAProUrl: "https://jaaprotest.ningmengyun.com:4446",
        jAAProH5Url: "https://jaaproh5test.ningmengyun.com",
        jAAProApiUrl: "https://jaaproapitest.ningmengyun.com",
        jAAProAccountBooksUrl: "https://aaproasyncfileprocesstest.ningmengyun.com",
        jAAProLmDiskUrl: "https://aaprolemondisktest.ningmengyun.com:4446",
        jmAAProUrl: "https://jmaaprotest.ningmengyun.com",
        jAAProTaxUrl: "https://jaaprotaxapitest.ningmengyun.com",
        aaProShareReportUrl: "http://mtest1.ningmengyun.com",
        aaProImportFromOther: "https://aaproimportertest2.ningmengyun.com/",
        jAAProPrintUrl: "https://jaaproprinttest.ningmengyun.com",
        jAAProReceiptImportUrl: "https://aaproreceiptimporttest.ningmengyun.com:4446",

        jAAProBossUrl: "https://jaaprobosstest.ningmengyun.com:4446",
        jAAProBossH5Url: "https://jaaprobossh5test.ningmengyun.com",
        jAAProBossApiUrl: "https://jaaprobossapitest.ningmengyun.com",
        jAAProBossAccountBooksUrl: "https://aaproasyncfileprocesstest.ningmengyun.com",
        jmAABossProUrl: "https://jmaaprobosstest.ningmengyun.com",

        jErpUrl: "https://j600test.ningmengyun.com:4446",
        jErpH5Url: "https://jerph5test.ningmengyun.com",
        jErpApiUrl: "https://erpjapitest.ningmengyun.com",
        jErpAccountBooksUrl: "https://erpasyncfileprocesstest.ningmengyun.com",
        jErpLmDiskUrl: "https://erplemondisktest.ningmengyun.com:4446",
        jmErpUrl: "https://erpjmtest.ningmengyun.com",
        jErpTaxUrl: "https://jerptaxapitest.ningmengyun.com",
        erpShareReportUrl: "http://mtest1.ningmengyun.com",
        jErpPrintUrl: "https://jerpprinttest.ningmengyun.com",
        jErpReceiptImportUrl: "https://erpreceiptimporttest.ningmengyun.com:4446",

        accountSrvUrl: "https://apitest.ningmengyun.com",
        aaUrl: "https://aatest2.ningmengyun.com",
        eUrl: "https://etest.ningmengyun.com",
        epUrl: "https://eptest.ningmengyun.com",
        scmUrl: "https://scmtest3.ningmengyun.com",
        scmProUrl: "https://scmprotest.ningmengyun.com",
        apimUrl: "https://apimtest.ningmengyun.com",
        wwwUrl: "https://wwwtest2.ningmengyun.com",
        erpUrl: "https://erptest6.ningmengyun.com:4443",
        salaryUrl: "https://gztest.nmygzt.cn",
        paBankUrl:
            "https://my-uat1.orangebank.com.cn:499/#/p/ebank-login?redirectUrl=https%3A%2F%2Fmy-st1.orangebank.com.cn%2Fcorporbank%2FbankLogin.do&appId=91021",
        spdbBankUrl: "https://ebanksent.spdb.com.cn/msent-web-login/proLogin.do",
        cmbBankUrl: "http://mobiletest.cmburl.cn/CmbBank_FB/UI/Login/FBPOPLogin.aspx",
        psbcLoginUrl: "https://corpebank.psbc.com",
        preOpenBankUrl: "https://preopenbanktest.ningmengyun.com",
        fpUrl: "https://einvoicetest.ningmengyun.com",
        fpApiUrl: "https://kpapitest.ningmengyun.com",
        authUrl: "https://authtest.ningmengyun.com:4443/v2",
        invoiceConfigUrl: "https://download.ningmengyun.com/Websites/InvoiceConfig/AgentBusinessAreaCode.json",
        erpApiUrl: "https://erpapitest2.ningmengyun.com",
        downLoadUrl: "https://downloadtest.ningmengyun.com/",
        maxRowNumber: 200000,
        wxworkSuitId: "ww9f4de00f09818521",
        wxGzhAppId: "wxe7e07d950bf68191",
        maxSubjectNumber: 1500,
        isStopAIFunction: false,
        invoiceTaskPolllingInterval: 1,
        FetchInoiceCodeTime: 4,
    };
})(window);
