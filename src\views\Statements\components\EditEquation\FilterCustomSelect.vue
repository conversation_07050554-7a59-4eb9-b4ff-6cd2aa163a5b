<template>
    <el-tooltip
        :visible="visible"
        popper-class="el-option-tool-tip"
        effect="light"
        :content="content"
        placement="right"
        :hide-after="0"
        :virtual-ref="inputElement"
    >
        <el-select
            ref="selectRef"
            :popper-class="popperClass + ' select-down'"
            placement="bottom"
            v-model="value"
            :class="[props.class,'select', 'custom-select']"
            :teleported="teleported"
            :placeholder="placeholder"
            :fit-input-width="fitInputWidth"
            :filterable="filterable"
            :clearable="false"
            :disabled="disabled"
            @visible-change="visibleChange"
            @change="handleChange"
            @focus="handleFocus"
            @blur="handleBlur"
            @keyup="handleKeyUp"
            :suffix-icon="suffixIcon"
            :allow-create="allowCreate"
            :automatic-dropdown="automaticDropdown"
            :no-match-text="noMatchText"
            :palacement="props.palacement"
            @mouseenter="enterInput"
            @mouseleave="leaveInput"
            @input="handleInput"
            :filter-method="filterMethod"

        >
            <slot> </slot>
            <template #prefix v-if="clearable">
                <div class="icon_clear" @click="handleClearClick" v-show="iconFlag" :style="{right:IconClearRight}">
                    <el-icon color="#a8abb2"><CircleClose /></el-icon>
                </div>
            </template>
        </el-select>
    </el-tooltip>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";

const props = withDefaults(
    defineProps<{
        modelValue: any;
        teleported?: boolean;
        bottomHtml?: string;
        placeholder?: string;
        unfold?: boolean;
        fitInputWidth?: boolean;
        filterable?: boolean;
        allowCreate?: boolean;
        clearable?: boolean;
        automaticDropdown?: boolean;
        inputText?: string;
        noMatchText?: string;
        immediatelyBlur?: boolean;
        disabled?: boolean;
        palacement?: string;
        class?: string;
        suffixIcon?: string | object;
        popperClass?: string;
        IconClearRight?:string;
        filterMethod?:Function;
    }>(),
    {
        teleported: true,
        bottomHtml: "",
        placeholder: "",
        unfold: false,
        fitInputWidth: true,
        filterable: false,
        allowCreate: false,
        clearable: false,
        automaticDropdown: false,
        inputText: "",
        noMatchText: " ",
        immediatelyBlur: true,
        palacement: "bottom",
        class: "",
        suffixIcon: "ArrowDown",
        popperClass: "",
        IconClearRight:"18px",
        filterMethod:()=>{}
    }
);

const emits = defineEmits(["update:modelValue", "bottomClick", "keyupEnter", "change", "focus", "blur", "visible-change", "keyup","input"]);

const value = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});

const visible = ref(false);

const selectRef = ref();
const handleInput=(val:string)=>{
    emits("input", val);
}
/**
 * 为element-ui的Select和Cascader添加弹层底部操作按钮
 * @param visible
 * @param refName  设定的ref名称
 * @param onClick  底部操作按钮点击监听
 */

const visibleChange = (visible: boolean) => {
    emits("visible-change", visible);
    if (!visible && props.immediatelyBlur) {
        nextTick().then(() => {
            selectRef.value?.blur();
        });
        return;
    }
    if (visible && props.bottomHtml !== "") {
        let popper = selectRef.value?.popperPaneRef;
        if (!popper) return;
        if (popper.$el) popper = popper.$el;
        //避免重复添加
        if (!Array.from(popper.children).some((v: any) => v.className === "el-select-menu__list")) {
            const el = document.createElement("ul");
            el.className = "el-select-menu__list";
            el.innerHTML = props.bottomHtml;
            popper.appendChild(el);
            el.onclick = () => {
                selectRef.value?.blur();
                emits("bottomClick");
            };
        }
    }
};

const handleChange = (value: any) => {
    emits("change", value);
};
const handleFocus = (event: FocusEvent) => {
    emits("focus", event);
};
const handleBlur = (event: FocusEvent) => {
    emits("blur", event);
};
const content = ref("");
const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        emits("keyupEnter");
        return;
    }
    emits("keyup", event);
};
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        selectRef.value?.toggleMenu();
    }
};
const inputElement = ref(selectRef.value?.$refs?.reference?.input);
const checkOverflow = () => {
    const input = selectRef.value?.$refs?.reference?.input;
    let label = "";
    if (selectRef.value?.selected?.currentLabel) {
        content.value = selectRef.value?.selected?.currentLabel;
        label = selectRef.value?.selected?.currentLabel.trim();
    } else {
        content.value = "";
        label = "";
    }
    if (input.scrollWidth > input.clientWidth && label.trim() !== "") {
        //解决日记账手支类别页面放大input.scrollWidth增加1从而出现气泡的问题
       if (Number(input.scrollWidth) - 1 === Number(input.clientWidth)){
            visible.value = false;
       } else {
            visible.value = true;
       }
    } else {
        visible.value = false;
    }
};
const hiddenOverflow = () => {
    visible.value = false;
};

onMounted(() => {
    const input = selectRef.value?.$refs?.reference?.input;
    if (input) {
        checkOverflow();
        input.addEventListener("keydown", handleKeyDown);
        input.addEventListener("mouseenter", checkOverflow);
        input.addEventListener("mouseleave", hiddenOverflow);
        input.addEventListener("focus", () => {
            nextTick().then(() => {
                visible.value = false;
            });
        });
    }
    if (props.unfold) {
        const timer = setTimeout(() => {
            selectRef.value?.toggleMenu();
            clearInterval(timer);
        }, 300);
    }
});

const focus = () => {
    const timer = setTimeout(() => {
        selectRef.value?.focus();
        clearInterval(timer);
    }, 150);
};
const blur = () => {
    selectRef.value?.blur();
};
const getSelect = () => {
    return selectRef.value;
};
defineExpose({ focus, blur, getSelect });
const handleClearClick = () => {
    value.value = "";
    iconFlag.value = false;
};
const enterInput = () => {
    iconFlag.value = value.value ? true : false;
};
const leaveInput = () => {
    iconFlag.value = false;
};
const iconFlag = ref(false);
</script>
<style lang="less">
.custom-select {
    .el-input__inner {
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.select-down {
    // 点击添加之类的自定义按钮的格式
    .el-select-menu__list {
        border-top: solid 1px var(--border-color);
        padding: 0;
        list-style: none;
        margin: 0;
    }
}
.icon_clear {
    position: absolute;
    right: 18px;
    display: flex;
}
.select.custom-select {
    .el-input__prefix {
        width: 0px;
    }
    .el-input__suffix{
        margin-left:4px;
    }
}

</style>
