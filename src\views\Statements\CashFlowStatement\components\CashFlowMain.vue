<template>
    <el-table
        style="width: 1100px"
        :class="isErp ? 'erp-table custom-table-normal' : ''"
        v-loading="loading"
        element-loading-text="正在加载数据..."
        :data="tableData"
        :empty-text="emptyText"
        border
        fit
        stripe
        scrollbar-always-on
        highlight-current-row
        row-key="lineID"
        :row-class-name="setTitleRowStyle"
        @header-dragend="headerDragend"
    >
        <el-table-column 
            label="项目" 
            min-width="400px" 
            align="left" 
            headerAlign="center"
            prop="lineName"
            :width="getColumnWidth(setModule, 'lineName')"
        >
            <template #default="scope">
                <div :class="assertNameClass(scope.row)" :title="scope.row.lineName">
                    {{ scope.row.lineName }}
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="行次" 
            min-width="60" 
            align="left" 
            header-align="left" 
            prop="lineNumber" 
            :formatter="rowNumberFormatter"
            :width="getColumnWidth(setModule, 'lineNumber')"
        >
        </el-table-column>
        <el-table-column 
            label="本年累计金额" 
            min-width="250" 
            align="right" 
            header-align="right"
            prop="initalAmount"
            :width="getColumnWidth(setModule, 'initalAmount')"
        >
            <template #default="scope">
                <TableAmountItem
                    :amount="scope.row.initalAmount.toString()"
                    :formula="calcFormula(scope.row, 1)"
                    :line-number="scope.row.lineNumber"
                    :icon-show="state.calmethod === 1"
                ></TableAmountItem>
            </template>
        </el-table-column>
        <el-table-column
            :label="!state.isTax ? '本期金额' : accountStandard === 1 ? '上年累计金额' : '上年同期累计金额'"
            min-width="250"
            align="right"
            header-align="right"
            :resizable="false"
        >
            <template #default="scope">
                <TableAmountItem
                    :amount="scope.row.amount.toString()"
                    :formula="calcFormula(scope.row, 0)"
                    :line-number="scope.row.lineNumber"
                    :icon-show="state.calmethod === 1"
                ></TableAmountItem>
            </template>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { hasNumberTitle, calcFormula, rowNumberFormatter } from "../utils";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import { useAccountSetStore } from "@/store/modules/accountset";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useRoute } from "vue-router";
import type { IState, IAdjustFlowSheet } from "../types";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "CashFlowMain";
const isErp = ref(window.isErp);

const accountStandard = useAccountSetStore().accountSet?.accountingStandard;
let initAmountOfTotal = ref(0);
const props = defineProps<{ state: IState; isclass: boolean }>();
const emits = defineEmits(["prompt"]);
const route = useRoute();
const periodStore = useAccountPeriodStore();
const searchInfo = reactive<any>({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    isTax: false,
    calmethod: Number(localStorage.getItem("calmethod")),
});
const isclass = computed(() => props.isclass);
const tableData = ref<IAdjustFlowSheet[]>([]);
let loading = ref(false);
let emptyText = ref(" ");
// 勾选显示上年累计后的不平状态需要跟随没有勾选之前的
let lastUnEven = false;
const getTableList = () => {
    loading.value = true;
    request({
        url: `/api/CashFlowStatement`,
        params: { PId: searchInfo.pid, IsTax: searchInfo.isTax, CalMethod: searchInfo.calmethod, IsClass: isclass.value },
        method: "get",
    })
        .then((res: IResponseModel<IAdjustFlowSheet[]>) => {
            loading.value = false;
            if (res.state === 1000) {
                tableData.value = [];
                let parent = null;
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children!.push(element);
                        continue;
                    }
                    tableData.value.push(element);
                }

                let aModel = tableData.value[tableData.value.length - 1];
                if (aModel) {
                    if (searchInfo.calmethod === 1) {
                        if (!searchInfo.isTax) lastUnEven = aModel.amount !== aModel.initalAmount;
                        // 民非不再校验
                        checkNeedShowTip(accountStandard !== 3 && lastUnEven);
                    } else {
                        lastUnEven = false;
                        initAmountOfTotal.value = aModel.initalAmount;
                        getCashFlowSubTotal();
                    }
                }
                if (!tableData.value.length) {
                    emptyText.value = "暂无数据";
                }
            } else {
                console.log(res.msg);
            }
        })
        .catch((error) => {
            tableData.value = [];
            emptyText.value = "暂无数据";
            console.log(error);
        });
};
let tips = ref("");
// 获取现金科目的期末余额
const getCashFlowSubTotal = () => {
    request({
        url: `/api/CashFlowStatement/GetCashFlowSubTotal?pId=${searchInfo.pid}`,
        method: "post",
    }).then((res: IResponseModel<number>) => {
        let total = res.data;
        checkNeedShowTip(initAmountOfTotal.value !== total);
    });
};
function checkNeedShowTip(uneven: boolean) {
    const append = searchInfo.calmethod === 1 ? "本期金额与本年累计金额不平" : "不平"
    if (uneven) {
        if (accountStandard === 3) {
            tips.value = `现金及现金等价物净增加额` ;
        } else if (accountStandard === 2) {
            tips.value = `期末现金及现金等价物余额` ;
        } else if (accountStandard === 1) {
            tips.value = "期末现金余额" ;
        }
        tips.value = (searchInfo.calmethod === 1 ? `"${tips.value}"` : tips.value) + append;
    } else {
        tips.value = "";
    }
    emits("prompt", tips.value);
}
watch([searchInfo, isclass], getTableList, { immediate: true });

watch(props, (val: any) => {
    for (let key in val.state) {
        searchInfo[key] = val.state[key];
    }
});

const assertNameClass = (row: IAdjustFlowSheet) => {
    let className: string;
    if (row.expand == 1) {
        className = "level2";
    } else if (row.lineNumber == 0 || hasNumberTitle(row.lineName)) {
        className = "level1";
    } else {
        className = "level2";
    }
    return className;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle(data.row.lineName)) {
        return "highlight-title-row";
    }
}
defineExpose({ getTableList });

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/CashFlowStatement.less";
@import "@/style/Statements/Statements.less";
</style>
