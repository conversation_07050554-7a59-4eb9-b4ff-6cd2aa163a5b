import { ElNotification, type NotificationParams, type MessageParams } from "element-plus"

export const ElNotify = (options?: NotificationParams | undefined | MessageParams) => {
  ElNotification?.closeAll()
  const configDefault: NotificationParams = {
    title: "提示",
    duration: 3000,
    position: "bottom-right",
    showClose: true,
    dangerouslyUseHTMLString: true,
  }
  return ElNotification(Object.assign(configDefault, options))
}
