<template>
    <el-dialog
        v-model="visible"
        @close="() => emits('close')"
        title="个人所得税计算器"
        draggable
        append-to-body
        width="710px"
        class="tax-calculator"
    >
        <div class="dialog-content">
            <!-- 收入类型 -->
            <div class="line" style="margin-bottom: 30px">
                <div class="line-left">收入类型：</div>
                <div class="line-right">
                    <el-select v-model="incomeType" fit-input-width>
                        <el-option v-for="item in incomeTypeList" :key="item.type" :value="item.type" :label="item.label" />
                    </el-select>
                </div>
            </div>
            <div v-if="incomeType === '14'" class="line">
                <el-radio-group v-model="yearOrMonth" style="margin-left: 40px">
                    <el-radio :label="1">按月计算</el-radio>
                    <el-radio :label="12">按年计算</el-radio>
                </el-radio-group>
            </div>
            <!-- 税前收入 -->
            <div v-if="!['3', '12'].includes(incomeType)" class="line">
                <div class="line-left">税前收入：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="beforeTax"
                        @input="(val) => handleInput(val, 'beforeTax')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 税后收入 -->
            <div v-if="incomeType === '12'" class="line">
                <div class="line-left">税后收入：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="beforeTax"
                        @input="(val) => handleInput(val, 'beforeTax')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 各项社会保险费 -->
            <div v-if="['12', '13', '14'].includes(incomeType)" class="line">
                <div class="line-left">各项社会保险费：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="socialSecurity"
                        @input="(val) => handleInput(val, 'socialSecurity')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 个人所得税免征额 -->
            <div v-if="['14', '12', '13'].includes(incomeType)" class="line">
                <div class="line-left">个人所得税免征额：</div>
                <div class="line-right">{{ dismiss }}元</div>
            </div>
            <!-- 综合所得 14 -->
            <div v-if="incomeType === '14'">
                <div class="line">
                    <div class="line-left">
                        <el-checkbox v-model="generalOptions.childrenEducation">子女教育：</el-checkbox>
                    </div>
                    <div v-show="generalOptions.childrenEducation" class="line-right">
                        <el-input v-model="generalCost.childrenEducation" disabled />&nbsp;元&nbsp;
                        <el-input
                            v-model="generalOptions.childrenEducationNum"
                            @input="(val) => formatCount(val, 'childrenEducationNum')"
                            style="width: 30px"
                        />&nbsp;个孩子&nbsp;
                        <el-select v-model="generalOptions.childrenEducationSelect" style="width: 180px" fit-input-width>
                            <el-option :value="1" :label="'单独承担100%扣除'" />
                            <el-option :value="0.5" :label="'夫妻双方50%扣除'" />
                        </el-select>
                    </div>
                </div>
                <div class="line">
                    <div class="line-left">
                        <el-checkbox v-model="generalOptions.adultEducation">继续教育：</el-checkbox>
                    </div>
                    <div v-show="generalOptions.adultEducation" class="line-right">
                        <el-input v-model="generalCost.adultEducation" disabled />&nbsp;元&nbsp;
                        <el-radio-group v-model="generalOptions.adultEducationSelect">
                            <el-radio label="400.00">学历教育</el-radio>
                            <el-radio label="300.00">职业资格教育</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="line">
                    <div class="line-left">
                        <el-checkbox v-model="generalOptions.medicalTreatment">大病医疗：</el-checkbox>
                    </div>
                    <div v-show="generalOptions.medicalTreatment" class="line-right">
                        <el-input
                            class="tax-calculator-ipt"
                            v-model="generalCost.medicalTreatment"
                            placeholder="0.00"
                            @input="(val) => handleInput(val, 'medicalTreatment')"
                        />&nbsp;元&nbsp;
                    </div>
                </div>
                <div class="line">
                    <div class="line-left">
                        <el-checkbox v-model="generalOptions.houseRent">居住扣除：</el-checkbox>
                    </div>
                    <div v-show="generalOptions.houseRent" class="line-right">
                        <el-input v-if="generalOptions.houseRentType === 'loans'" v-model="generalCost.houseRent" disabled />
                        <el-select
                            v-if="generalOptions.houseRentType === 'rent'"
                            v-model="generalOptions.houseRentSelect"
                            class="select120"
                            style="width: 120px"
                            fit-input-width
                        >
                            <el-option :value="yearOrMonth === 1 ? '800.00' :'9600.00'" :label="yearOrMonth === 1 ? '800.00' :'9600.00'" />
                            <el-option :value="yearOrMonth === 1 ? '1100.00' :'13200.00'" :label="yearOrMonth === 1 ? '1100.00' :'13200.00'" />
                            <el-option :value="yearOrMonth === 1 ? '1500.00' :'18000.00'" :label="yearOrMonth === 1 ? '1500.00' :'18000.00'" />
                        </el-select>
                        &nbsp;元&nbsp;
                        <el-radio-group v-model="generalOptions.houseRentType">
                            <el-radio label="loans">首房贷款</el-radio>
                            <el-radio label="rent">住房租金</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="line">
                    <div class="line-left">
                        <el-checkbox v-model="generalOptions.oldSupport">赡养老人：</el-checkbox>
                    </div>
                    <div v-show="generalOptions.oldSupport" class="line-right">
                        <el-input v-model="generalCost.oldSupport" disabled />&nbsp;元&nbsp;
                        <el-radio-group v-model="generalOptions.oldSupportSelect">
                            <el-radio label="only">独生子女</el-radio>
                            <el-radio label="more">非独生子女</el-radio>
                        </el-radio-group>
                        <span v-show="generalOptions.oldSupportSelect === 'more'">
                            <el-input
                                v-model="generalOptions.oldSupportNum"
                                @input="(val) => formatCount(val, 'oldSupportNum')"
                                @change="handleOldSupportNum"
                                style="width: 30px"
                            />&nbsp;人分摊&nbsp;</span
                        >
                    </div>
                </div>
                <div class="line">
                    <div class="line-left">
                        <el-checkbox v-model="generalOptions.babySupport">3岁以下婴<br />幼儿照护：</el-checkbox>
                    </div>
                    <div v-show="generalOptions.babySupport" class="line-right">
                        <el-input v-model="generalCost.babySupport" disabled />&nbsp;元&nbsp;
                        <el-input
                            v-model="generalOptions.babySupportNum"
                            @input="(val) => formatCount(val, 'babySupportNum')"
                            style="width: 30px"
                        />&nbsp;个孩子&nbsp;
                        <el-select v-model="generalOptions.babySupportSelect" style="width: 180px" fit-input-width>
                            <el-option :value="1" :label="'单独承担100%扣除'" />
                            <el-option :value="0.5" :label="'夫妻双方50%扣除'" />
                        </el-select>
                    </div>
                </div>
            </div>

            <!-- 经营所得 3 -->
            <div v-if="incomeType === '3'" class="line">
                <div class="line-left">收入总额：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="operatingIncome.totalIncome"
                        @input="(val) => handleInput(val, 'totalIncome')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 经营所得3与财产租赁所得8 -->
            <div v-if="['3', '8'].includes(incomeType)" class="line">
                <div class="line-left">扣除支出：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="disbursement"
                        @input="(val) => handleInput(val, 'disbursement')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 纳税年度经营期 -->
            <div v-if="incomeType === '3'" class="line">
                <div class="line-left">纳税年度经营期：</div>
                <div class="line-right">
                    <el-select
                        v-model="operatingIncome.operationPeriod"
                        fit-input-width
                        class="select120"
                        style="width: 120px"
                        placeholder=" "
                    >
                        <el-option v-for="item in 12" :key="item" :value="item" :label="item" />
                    </el-select>
                </div>
            </div>
            <!-- 月减除费用 -->
            <div v-if="incomeType === '3'" class="line">
                <div class="line-left">月减除费用：</div>
                <div class="line-right">{{ operatingIncome.monthlyDeduction }}&nbsp;元</div>
            </div>
            <!-- 修缮费用 -->
            <div v-if="['8'].includes(incomeType)" class="line">
                <div class="line-left">修缮费用：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="repairCost"
                        @input="(val) => handleInput(val, 'repairCost')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <p v-if="['8'].includes(incomeType)" class="tips">（每次800元为限，一次扣除不完，结转下次扣除）</p>

            <!-- 财产原值  -->
            <div v-if="['9'].includes(incomeType)" class="line">
                <div class="line-left">财产原值：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="originalValue"
                        @input="(val) => handleInput(val, 'originalValue')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 合理费用  -->
            <div v-if="['9'].includes(incomeType)" class="line">
                <div class="line-left">合理费用：</div>
                <div class="line-right">
                    <el-input
                        class="tax-calculator-ipt"
                        v-model.trim="reasonableExpense"
                        @input="(val) => handleInput(val, 'reasonableExpense')"
                        @blur="onInputBlur"
                        placeholder="0.00"
                    />&nbsp;元
                </div>
            </div>
            <!-- 按钮 -->
            <div class="all-line mb-20 mt-10 pt-10 border-top">
                <a class="button solid-button" @click="handleCount">计算</a>
                <a class="button mr-10" @click="() => handleReset()">重置</a>
            </div>
            <!-- 计算结果 -->
            <div class="line">
                <div class="line-left">计算结果：</div>
                <div class="line-right"></div>
            </div>
            <!-- 应纳税所得额 -->
            <div v-if="['8', '9', '10'].includes(incomeType)" class="line">
                <div class="line-left">应纳税所得额：</div>
                <div class="line-right">{{ totalNum || "0.00" }}&nbsp;元；</div>
            </div>
            <!-- 适用税率 -->
            <div v-if="['8', '10'].includes(incomeType)" class="line">
                <div class="line-left">适用税率：</div>
                <!-- taxRate -->
                <div class="line-right">
                    <el-select v-model="taxRate" fit-input-width class="select120" style="width: 120px">
                        <el-option :value="20" :label="'20%'" />
                        <el-option :value="10" :label="'10%'" />
                        <el-option v-if="incomeType === '10'" :value="5" :label="'5%'" />
                    </el-select>
                </div>
            </div>
            <div v-if="['9'].includes(incomeType)" class="line">
                <div class="line-left">适用税率：</div>
                <!-- taxRate -->
                <div class="line-right">{{ taxRate || 0 }}&nbsp;%；</div>
            </div>
            <!-- 提示 -->
            <p v-if="incomeType === '8'" class="red-tips">
                优惠政策：对于个人按市场价格出租的居民住房取得的财产租赁收入，自2001年1月1日起暂按10%的税率征收
            </p>
            <p v-if="incomeType === '10'" class="red-tips">
                优惠政策：对于个人取得的股票，持有期限在1个月以内（含1个月）的，其股息红利所得全额计入应纳税所得额（税负为20%）；持股期限在1个月以上至1年（含1年）的，暂减按50%计入应纳税所得额（税负为10%）；持股期限超过1年的，暂减按25%计入应纳税所得额（税负为5%）。
            </p>
            <!-- 应交税款 -->
            <div v-if="!['9'].includes(incomeType)" class="line">
                <div class="line-left">应交税款：</div>
                <div class="line-right">{{ totalTax || "0.00" }}&nbsp;元；</div>
            </div>
            <!-- 税前收入 -->
            <div v-if="incomeType === '12'" class="line">
                <div class="line-left">税前收入：</div>
                <div class="line-right">{{ afterTax || "0.00" }}元；</div>
            </div>
            <!-- 税后收入 -->
            <div v-if="!['3', '9', '12'].includes(incomeType)" class="line">
                <div class="line-left">税后收入：</div>
                <div class="line-right">{{ afterTax || "0.00" }}&nbsp;元；</div>
            </div>
            <!-- 综合 -->
            <div v-if="['14', '13', '12', '3', '11'].includes(incomeType)" class="line">
                <div class="line-left">应纳税所得额：</div>
                <div class="line-right">
                    <i> {{ totalNum || "0.00" }} </i>&nbsp;元；
                    <span>
                        适用税率：
                        <i>
                            {{ taxRate || 0 }}
                        </i>
                        %；
                    </span>
                    <span v-if="incomeType !== '11'">
                        速算扣除数：
                        <i>
                            {{ speedDeduction || "0.00" }}
                        </i>
                        &nbsp;元；
                    </span>
                </div>
            </div>
            <div v-if="['5', '6', '7'].includes(incomeType)" class="line">
                <div class="line-left">减除费用：</div>
                <div class="line-right">
                    <i> {{ deduction || "0.00" }} </i>&nbsp;元；
                    <span>
                        应纳税所得额：
                        <i> {{ totalNum || "0.00" }} </i>&nbsp;元；
                    </span>
                    <span v-if="['5', '7'].includes(incomeType)">
                        适用税率：
                        <i>
                            {{ taxRate || 0 }}
                        </i>
                        %；
                    </span>
                    <span v-if="['6'].includes(incomeType)">
                        实际税率：
                        <i>
                            {{ realRate || "0" }}
                        </i>
                        %；
                    </span>
                    <span v-if="['5'].includes(incomeType)">
                        速算扣除数：
                        <i>
                            {{ speedDeduction || "0.00" }}
                        </i>
                        &nbsp;元；
                    </span>
                </div>
            </div>
            <div v-if="['9'].includes(incomeType)" class="line">
                <div class="line-left">应交税款：</div>
                <div class="line-right">
                    <span>
                        <i> {{ totalTax || "0.00" }} </i>&nbsp;元；
                    </span>
                    <span>
                        税后收入：
                        <i> {{ afterTax || "0.00" }} </i>&nbsp;元；
                    </span>
                </div>
            </div>
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import { ElConfirm } from "@/util/confirm";
import { nextTick } from "vue";

const props = withDefaults(defineProps<{ showTaxCalculator: boolean }>(), {
    showTaxCalculator: false,
});

const emits = defineEmits<{
    (e: "close"): void;
}>();

const visible = ref<boolean>(false);
// 参数
// 收入类型
const incomeType = ref("14");
const incomeTypeList = [
    {
        type: "14",
        label: "综合所得",
    },
    {
        type: "13",
        label: "工资、薪金所得",
    },
    {
        type: "12",
        label: "工资、薪金所得（税后）",
    },
    {
        type: "3",
        label: "经营所得",
    },
    {
        type: "5",
        label: "劳务报酬所得",
    },
    {
        type: "6",
        label: "稿酬所得",
    },
    {
        type: "7",
        label: "特许权使用费所得",
    },
    {
        type: "8",
        label: "财产租赁所得",
    },
    {
        type: "9",
        label: "财产转让所得",
    },
    {
        type: "10",
        label: "利息，股息，红利所得",
    },
    {
        type: "11",
        label: "偶然所得",
    },
];
// 按年或按月计算  按月计算1  按年计算12
const yearOrMonth = ref(1);
// 税前收入
const beforeTax = ref("");
// 税后收入
const afterTax = ref("");
// 扣除支出
const disbursement = ref("");
// 修缮费用
const repairCost = ref("");
// 财产原值
const originalValue = ref("");
// 合理费用
const reasonableExpense = ref("");
// 各项社会保险费
const socialSecurity = ref("");
// 个税免征点
const dismiss = ref("5000.00");

// 计算结果
// 应交税款
const totalTax = ref("0.00");
//应纳税所得额
const totalNum = ref("0.00");
// 适用税率
const taxRate = ref();
// 实际税率
const realRate = ref("0");
// 速算扣除额
const speedDeduction = ref("0.00");
// 减除费用
const deduction = ref("0.00");

// 综合所得选项
const generalOptions = reactive({
    // 子女教育
    childrenEducation: false,
    childrenEducationNum: 1,
    childrenEducationSelect: 1,
    // 继续教育
    adultEducation: false,
    adultEducationSelect: "400.00",
    // 大病医疗
    medicalTreatment: false,
    // 居住扣除
    houseRent: false,
    houseRentType: "loans",
    houseRentSelect: "1500.00",
    // 赡养老人
    oldSupport: false,
    oldSupportSelect: "only",
    oldSupportNum: 2,
    // 婴幼儿照护
    babySupport: false,
    babySupportNum: 1,
    babySupportSelect: 1,
});

// 综合所得费用
const generalCost = reactive({
    // 子女教育
    childrenEducation: "0.00",
    // 继续教育
    adultEducation: "0.00",
    // 大病医疗
    medicalTreatment: "",
    // 居住扣除
    houseRent: "0.00",
    // 赡养老人
    oldSupport: "0.00",
    // 婴幼儿照护
    babySupport: "0.00",
});

// 经营所得
const operatingIncome = reactive({
    // 收入总额
    totalIncome: "",
    // 纳税年度的经营期
    operationPeriod: "",
    // 月减除费用
    monthlyDeduction: "5000.00",
});

// 格式化数字
const onInputBlur = (e: FocusEvent) => {
    (e.target as HTMLInputElement).value = Number((e.target as HTMLInputElement).value).toFixed(2);
};

// 格式化
const handleInput = (val: string, type: string) => {
    val = val.replace(/。/g, ".");
    val = val.replace(/[^0-9.]/g, "");
    // 整数部分最多 九位数  小数部分最多两位
    if (val.indexOf(".") !== -1) {
        const numArr = val.split(".");
        val = `${numArr[0].slice(0, 9)}.${numArr[1].slice(0, 2)}`;
    } else {
        val = val.slice(0, 9);
    }
    switch (type) {
        case "beforeTax":
            beforeTax.value = val.trim();
            break;
        case "afterTax":
            afterTax.value = val;
            break;
        case "socialSecurity":
            socialSecurity.value = val;
            break;
        case "disbursement":
            disbursement.value = val;
            break;
        case "totalIncome":
            operatingIncome.totalIncome = val;
            break;
        case "repairCost":
            if (Number(val) > 800) {
                val = "800.00";
            }
            repairCost.value = val;
            break;
        case "originalValue":
            originalValue.value = val;
            break;
        case "reasonableExpense":
            reasonableExpense.value = val;
            break;
        case "medicalTreatment":
            generalCost.medicalTreatment = val;
            break;
        default:
            console.log(val);
    }
};

// 格式化人数
const formatCount = (val: string, type: string) => {
    val = val.replace(/\D/g, "");
    switch (type) {
        case "childrenEducationNum":
            if (Number(val) > 9) val = "1";
            generalOptions.childrenEducationNum = Number(val);
            break;
        case "oldSupportNum":
            if (val !== "" && (Number(val) < 2 || Number(val) > 9)) val = "2";
            generalOptions.oldSupportNum = Number(val);
            break;
        case "babySupportNum":
            if (Number(val) > 9) val = "1";
            generalOptions.babySupportNum = Number(val);
            break;
    }
};

// 非独生子女人数
const handleOldSupportNum = () => {
    if (generalOptions.oldSupportNum === 0) {
        generalOptions.oldSupportNum = 2;
    }
};
// 计算方法
//获取应交税款
function getTaxPayable(type: string) {
    switch (type) {
        case "1":
        case "2":
        case "3":
        case "4":
        case "5":
        case "12":
        case "13":
        case "14":
            return (getTaxableIncome(type)! * getRate(type)!) / 100 - getFastReduce(type)!;
        case "6":
        case "7":
        case "8":
        case "9":
        case "10":
        case "11":
            return (getTaxableIncome(type)! * getRate(type)!) / 100;
        default:
            return 0;
    }
}
//获取应纳税所得额
function getTaxableIncome(type: string) {
    var result;
    switch (type) {
        case "13":
            result = Number(beforeTax.value) - Number(socialSecurity.value) - Number(dismiss.value);
            break;
        case "14":
            result =
                Number(beforeTax.value) -
                Number(socialSecurity.value) -
                Number(dismiss.value) -
                Number(generalCost.childrenEducation) -
                Number(generalCost.adultEducation) -
                Number(generalCost.medicalTreatment) -
                Number(generalCost.houseRent) -
                Number(generalCost.oldSupport) -
                Number(generalCost.babySupport);
            break;
        case "3":
            result =
                Number(operatingIncome.totalIncome) -
                Number(disbursement.value) -
                Number(operatingIncome.monthlyDeduction) * (Number(operatingIncome.operationPeriod) || 1);
            break;
        case "5":
        case "6":
        case "7":
            result = Number(beforeTax.value) <= 4000 ? Number(beforeTax.value) - 800 : (Number(beforeTax.value) * 80) / 100;
            break;
        case "8":
            result =
                Number(beforeTax.value) <= 4000
                    ? Number(beforeTax.value) - Number(disbursement.value) - Number(repairCost.value) - 800
                    : ((Number(beforeTax.value) - Number(disbursement.value) - Number(repairCost.value)) * 80) / 100;
            break;
        case "9":
            result = Number(beforeTax.value) - Number(originalValue.value) - Number(reasonableExpense.value);
            break;
        case "10":
        case "11":
            result = Number(beforeTax.value);
            break;
        case "12":
            result = (Number(beforeTax.value) - Number(dismiss.value) - getFastReduce(type)!) / (1 - getRate(type)! / 100);
            break;
        default:
            result = 0;
    }
    result = result > 0 ? result : 0;
    // totalNum.value = result.toFixed(2);
    return result;
}
//获取适用税率
function getRate(type: string): any {
    var temp = 0;
    switch (type) {
        case "3":
            temp = getTaxableIncome(type)!;
            if (temp <= 30000) {
                return 5;
            } else if (temp > 30000 && temp <= 90000) {
                return 10;
            } else if (temp > 90000 && temp <= 300000) {
                return 20;
            } else if (temp > 300000 && temp <= 500000) {
                return 30;
            } else if (temp > 500000) {
                return 35;
            }
            break;
        case "5":
            temp = getTaxableIncome(type)!;
            if (temp > 0 && temp <= 20000) {
                return 20;
            } else if (temp > 20000 && temp <= 50000) {
                return 30;
            } else if (temp > 50000) {
                return 40;
            } else return 0;
        case "6":
            return 14;
        case "7":
        case "9":
        case "11":
            return 20;
        case "8":
        case "10":
            return Number(taxRate.value);
        case "12":
            temp = Number(beforeTax.value) - Number(dismiss.value);
            if (temp <= 2910) {
                return 3;
            } else if (temp > 2910 && temp <= 11010) {
                return 10;
            } else if (temp > 11010 && temp <= 21410) {
                return 20;
            } else if (temp > 21410 && temp <= 28910) {
                return 25;
            } else if (temp > 28910 && temp <= 42910) {
                return 30;
            } else if (temp > 42910 && temp <= 59160) {
                return 35;
            } else if (temp > 59160) {
                return 45;
            }
            break;
        case "13":
        case "14":
            temp = getTaxableIncome(type)!;
            if (yearOrMonth.value === 1) {
                if (temp <= 3000) {
                    return 3;
                } else if (temp > 3000 && temp <= 12000) {
                    return 10;
                } else if (temp > 12000 && temp <= 25000) {
                    return 20;
                } else if (temp > 25000 && temp <= 35000) {
                    return 25;
                } else if (temp > 35000 && temp <= 55000) {
                    return 30;
                } else if (temp > 55000 && temp <= 80000) {
                    return 35;
                } else if (temp > 80000) {
                    return 45;
                }
            } else {
                if (temp <= 36000) {
                    return 3;
                } else if (temp > 36000 && temp <= 144000) {
                    return 10;
                } else if (temp > 144000 && temp <= 300000) {
                    return 20;
                } else if (temp > 300000 && temp <= 420000) {
                    return 25;
                } else if (temp > 420000 && temp <= 660000) {
                    return 30;
                } else if (temp > 660000 && temp <= 9600000) {
                    return 35;
                } else if (temp > 9600000) {
                    return 45;
                }
            }
            break;
        default:
            return 0;
    }
}
//获取速算扣除数
function getFastReduce(type: string) {
    var temp = 0;
    switch (type) {
        case "3":
            temp = getTaxableIncome(type)!;
            if (temp <= 30000) {
                return 0;
            } else if (temp > 30000 && temp <= 90000) {
                return 1500;
            } else if (temp > 90000 && temp <= 300000) {
                return 10500;
            } else if (temp > 300000 && temp <= 500000) {
                return 40500;
            } else if (temp > 500000) {
                return 65500;
            }
            break;
        case "4":
            temp = getTaxableIncome(type)!;
            if (temp > 0 && temp <= 15000) {
                return 0;
            } else if (temp > 15000 && temp <= 30000) {
                return 750;
            } else if (temp > 30000 && temp <= 60000) {
                return 3750;
            } else if (temp > 60000 && temp <= 100000) {
                return 9750;
            } else if (temp > 100000) {
                return 14750;
            } else {
                return 0;
            }
        case "5":
            temp = getTaxableIncome(type)!;
            if (temp > 0 && temp <= 20000) {
                return 0;
            } else if (temp > 20000 && temp <= 50000) {
                return 2000;
            } else if (temp > 50000) {
                return 7000;
            } else {
                return 0;
            }
        case "12":
            temp = Number(beforeTax.value) - Number(dismiss.value);
            if (temp <= 2910) {
                return 0;
            } else if (temp > 2910 && temp <= 11010) {
                return 210;
            } else if (temp > 11010 && temp <= 21410) {
                return 1410;
            } else if (temp > 21410 && temp <= 28910) {
                return 2660;
            } else if (temp > 28910 && temp <= 42910) {
                return 4410;
            } else if (temp > 42910 && temp <= 59160) {
                return 7160;
            } else if (temp > 59160) {
                return 15160;
            }
            break;
        case "13":
        case "14":
            temp = getTaxableIncome(type)!;
            if (yearOrMonth.value === 1) {
                if (temp <= 3000) {
                    return 0;
                } else if (temp > 3000 && temp <= 12000) {
                    return 210;
                } else if (temp > 12000 && temp <= 25000) {
                    return 1410;
                } else if (temp > 25000 && temp <= 35000) {
                    return 2660;
                } else if (temp > 35000 && temp <= 55000) {
                    return 4410;
                } else if (temp > 55000 && temp <= 80000) {
                    return 7160;
                } else if (temp > 80000) {
                    return 15160;
                }
            } else {
                if (temp <= 36000) {
                    return 0;
                } else if (temp > 36000 && temp <= 144000) {
                    return 2520;
                } else if (temp > 144000 && temp <= 300000) {
                    return 16920;
                } else if (temp > 300000 && temp <= 420000) {
                    return 31920;
                } else if (temp > 420000 && temp <= 660000) {
                    return 52920;
                } else if (temp > 660000 && temp <= 9600000) {
                    return 85920;
                } else if (temp > 9600000) {
                    return 181920;
                }
            }
            break;
        default:
            return 0;
    }
}
//获取税后收入
function getAfterTaxIncome(type: string) {
    switch (type) {
        case "13":
        case "14":
            return Number(beforeTax.value) - Number(socialSecurity.value) - getTaxPayable(type)!;
        case "5":
        case "6":
        case "7":
        case "8":
        case "9":
        case "10":
        case "11":
            return Number(beforeTax.value) - getTaxPayable(type)!;
        case "12":
            return Number(beforeTax.value) + Number(socialSecurity.value) + getTaxPayable(type)!;
        default:
            return 0;
    }
}

// 计算结果
const handleCount = () => {
    // 滚动到底部
    if (incomeType.value === "14") {
        const dialogContent = document.querySelector(".dialog-content");
        dialogContent!.scrollTop = dialogContent!.scrollHeight;
    }

    // 应缴纳所得额
    totalNum.value = getTaxableIncome(incomeType.value).toFixed(2);
    if (Number(totalNum.value) <= 0) {
        handleReset();
        return ElConfirm("亲，您无需缴纳个人所得税~", true);
    }
    // 适用税率
    taxRate.value = getRate(incomeType.value)!;
    // 速算扣除
    speedDeduction.value = getFastReduce(incomeType.value)!.toFixed(2);
    // 应交税款
    totalTax.value = getTaxPayable(incomeType.value).toFixed(2);

    // 税后收入
    afterTax.value = getAfterTaxIncome(incomeType.value)!.toFixed(2);
    // 减除费用
    if (["5", "6", "7"].includes(incomeType.value)) {
        var num = Number(beforeTax.value) <= 4000 ? 800 : (Number(beforeTax.value) * 20) / 100;
        deduction.value = num.toFixed(2);
    }
    realRate.value = getRate(incomeType.value);
};

// 重置
const handleReset = () => {
    // 滚动到顶部
    if (incomeType.value === "14") {
        const dialogContent = document.querySelector(".dialog-content");
        dialogContent!.scrollTop = 0;
    }

    // 按年或按月计算
    yearOrMonth.value = 1;
    // 税前收入
    beforeTax.value = "";
    // 税后收入
    afterTax.value = "";
    // 扣除支出
    disbursement.value = "";
    // 修缮费用
    repairCost.value = "";
    // 财产原值
    originalValue.value = "";
    // 合理费用
    reasonableExpense.value = "";
    // 各项社会保险费
    socialSecurity.value = "";
    // 个税免征点
    // dismiss.value = "5000.00";
    // 计算结果
    // 应交税款
    totalTax.value = "0.00";
    //应纳税所得额
    totalNum.value = "0.00";
    // 适用税率
    if (["8", "10"].includes(incomeType.value)) {
        taxRate.value = 20;
    } else {
        taxRate.value = 0;
    }
    // 实际税率
    realRate.value = "0";
    // 速算扣除额
    speedDeduction.value = "0.00";
    // 减除费用
    deduction.value = "0.00";

    // 综合所得选项
    // 子女教育
    generalOptions.childrenEducation = false;
    generalOptions.childrenEducationNum = 1;
    generalOptions.childrenEducationSelect = 1;
    // 继续教育
    generalOptions.adultEducation = false;
    generalOptions.adultEducationSelect = "400.00";
    // 大病医疗
    generalOptions.medicalTreatment = false;
    // 居住扣除
    generalOptions.houseRent = false;
    generalOptions.houseRentType = "loans";
    generalOptions.houseRentSelect = "1500.00";
    // 赡养老人
    generalOptions.oldSupport = false;
    generalOptions.oldSupportSelect = "only";
    generalOptions.oldSupportNum = 2;
    // 婴幼儿照护
    generalOptions.babySupport = false;
    generalOptions.babySupportNum = 1;
    generalOptions.babySupportSelect = 1;
    // 综合所得费用
    generalCost;
    // 子女教育
    generalCost.childrenEducation = "0.00";
    // 继续教育
    generalCost.adultEducation = "0.00";
    // 大病医疗
    generalCost.medicalTreatment = "";
    // 居住扣除
    generalCost.houseRent = "0.00";
    // 赡养老人
    generalCost.oldSupport = "0.00";
    // 婴幼儿照护
    generalCost.babySupport = "0.00";

    // 经营所得
    // 收入总额
    operatingIncome.totalIncome = "";
    // 纳税年度经营期
    operatingIncome.operationPeriod = "";
    // 月减除费用
    operatingIncome.monthlyDeduction = "5000.00";

    // 重置DOM
    const ipts = document.querySelectorAll(".tax-calculator-ipt .el-input__inner");
    for (let i in ipts) {
        if (typeof (ipts[i] as HTMLInputElement).value === "string") {
            (ipts[i] as HTMLInputElement).value = "";
        }
    }
};
// 切换收入类型
watch(
    () => incomeType.value,
    () => {
        handleReset();
    }
);

// 个税免征额
watch(
    () => yearOrMonth.value,
    (val) => {
        // handleReset();
        if (val === 1) {
            dismiss.value = "5000.00";
        } else {
            dismiss.value = "60000.00";
        }
        setChildEducationCost();
        setContinueEducationCost();
        setResidentCost();
        setSupportCost();
        setCareForInfantsUnder3YearsOldCost();
    }
);

// 子女教育
function setChildEducationCost() {
    // 子女教育单月扣除为2000
    if (!generalOptions.childrenEducation) {
        generalCost.childrenEducation = "0.00";
    } else {
        generalCost.childrenEducation = (
            2000 *
            generalOptions.childrenEducationNum *
            generalOptions.childrenEducationSelect *
            yearOrMonth.value
        ).toFixed(2);
    }
}
watch(
    [() => generalOptions.childrenEducation, () => generalOptions.childrenEducationNum, () => generalOptions.childrenEducationSelect],
    () => {
        setChildEducationCost();
    }
);

// 继续教育
function setContinueEducationCost() {
    // 继续教育单月扣除为400
    if (!generalOptions.adultEducation) {
        generalCost.adultEducation = "0.00";
    } else {
        generalCost.adultEducation = (Number(generalOptions.adultEducationSelect) * yearOrMonth.value).toFixed(2);
    }
}
watch([() => generalOptions.adultEducation, () => generalOptions.adultEducationSelect], () => {
    setContinueEducationCost();
});

// 大病医疗
watch(
    () => generalOptions.medicalTreatment,
    () => {
        if (!generalOptions.medicalTreatment) {
            generalCost.medicalTreatment = "0.00";
        }
    }
);

// 居住扣除
function setResidentCost() {
    if (!generalOptions.houseRent) {
        generalCost.houseRent = "0.00";
    } else {
        if (generalOptions.houseRentType === "loans") {
            generalCost.houseRent = (Number("1000.00") * yearOrMonth.value).toFixed(2);
        } else {
            generalCost.houseRent = (Number(generalOptions.houseRentSelect) * yearOrMonth.value).toFixed(2);
        }
    }
}
watch([() => generalOptions.houseRent, () => generalOptions.houseRentType, () => generalOptions.houseRentSelect], () => {
    setResidentCost();
});

// 赡养老人
function setSupportCost() {
    if (!generalOptions.oldSupport) {
        generalCost.oldSupport = "0.00";
    } else {
        if (generalOptions.oldSupportSelect === "only") {
            generalCost.oldSupport = (Number("3000.00") * yearOrMonth.value).toFixed(2);
        } else {
            if (generalOptions.oldSupportNum < 2) {
                generalCost.oldSupport = ((3000 * yearOrMonth.value) / 2).toFixed(2);
            } else {
                generalCost.oldSupport = ((3000 * yearOrMonth.value) / generalOptions.oldSupportNum).toFixed(2);
            }
        }
    }
}
watch([() => generalOptions.oldSupport, () => generalOptions.oldSupportSelect, () => generalOptions.oldSupportNum], () => {
    setSupportCost();
});

// 婴幼儿照护
function setCareForInfantsUnder3YearsOldCost() {
    if (!generalOptions.babySupport) {
        generalCost.babySupport = "0.00";
    } else {
        generalCost.babySupport = (2000 * generalOptions.babySupportNum * generalOptions.babySupportSelect * yearOrMonth.value).toFixed(2);
    }
}
watch([() => generalOptions.babySupport, () => generalOptions.babySupportNum, () => generalOptions.babySupportSelect], () => {
    setCareForInfantsUnder3YearsOldCost();
});

watch(
    () => props.showTaxCalculator,
    (val) => {
        visible.value = val;
        if (val) {
            incomeType.value = "14";
            handleReset();
        }
    }
);
</script>
<style lang="less" scoped>
.tax-calculator {
    // .el-dialog__header {
    //     height: 30px;
    //     background: #f4f3f8;
    //     .el-dialog__title {
    //         line-height: 30px;
    //         font-weight: 400;
    //     }
    //     .el-dialog__headerbtn {
    //         width: 30px;
    //         height: 30px;
    //         .el-dialog__close {
    //             width: 2em;
    //             height: 2em;
    //         }
    //     }
    // }
    .dialog-content {
        max-height: 500px;
        padding: 10px 20px;
        overflow-y: auto;
        .line {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            width: 650px;
            height: 30px;
            margin: 0 auto 10px;
            background: #fff;
            .line-left {
                width: 130px;
                text-align: right;
                margin-right: 10px;
            }
            .line-right {
                flex: 1;
                i {
                    font-style: normal;
                    color: var(--main-color);
                }
            }
            :deep(.el-input) {
                width: 120px;
                margin-right: 5px;
            }
            :deep(.el-select) {
                .el-input {
                    width: 220px;
                }
                &.select120 {
                    .el-input {
                        width: 120px;
                    }
                }
            }
            :deep(.el-radio) {
                margin-right: 10px;
            }
        }
        .all-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 200px;
            margin: 0 auto;
        }
        .result-line {
            padding-left: 50px;
            i {
                font-style: normal;
                color: var(--main-color);
            }
        }
        .tips {
            text-align: left;
            padding-left: 100px;
        }
        .red-tips {
            color: #f00;
            width: 500px;
            margin: 0 auto;
        }
    }
}
</style>
