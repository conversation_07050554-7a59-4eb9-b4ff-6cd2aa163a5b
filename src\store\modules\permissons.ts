import { defineStore } from "pinia"
import { ref } from "vue"
import { Jrequest } from "@/utils/service"
import { ElNotify } from "@/utils/notify"

export type PermissionKey = string
export type PermissionValue = 0 | 1

// 记录是否正在加载权限
let isLoadingPermissions = false
// 记录请求权限的Promise
let permissionsPromise: Promise<boolean> | null = null

export const usePermissonsStore = defineStore("permissons", () => {
  // 使用Partial类型声明，允许部分属性或空对象
  const permissions = ref<Partial<Record<PermissionKey, PermissionValue>>>({})

  // 判断权限对象是否为空
  const isPermissionsEmpty = () => {
    return Object.keys(permissions.value).length === 0
  }

  // 从API获取权限数据
  const fetchPermissions = async () => {
    // 如果已经在请求中，直接返回之前的Promise
    if (isLoadingPermissions && permissionsPromise) {
      return permissionsPromise
    }

    isLoadingPermissions = true
    permissionsPromise = new Promise<boolean>((resolve) => {
      Jrequest({
        url: "/api/Permissions/GetUserFunctionState",
        method: "post",
      })
        .then((res) => {
          // 处理成功的响应
          if (res.data) {
            permissions.value = res.data
            resolve(true)
          } else {
            resolve(false)
          }
        })
        .catch((error) => {
          // 处理错误的响应
          ElNotify({
            type: "error",
            message: "获取权限失败，请稍后重试:" + error,
          })
        })
        .finally(() => {
          // 请求完成后重置状态
          isLoadingPermissions = false
        })
    })

    return permissionsPromise
  }

  // 检查是否有特定权限，如果权限为空则自动获取
  const hasPermission = async (key: PermissionKey): Promise<boolean> => {
    // 如果权限对象为空，先获取权限
    if (isPermissionsEmpty()) {
      const success = await fetchPermissions()
      if (!success) {
        return false
      }
    }
    return !!permissions.value[key]
  }

  // 同步检查权限，不会触发API请求，用于已知权限已加载的情况
  const checkPermission = (key: PermissionKey): boolean => {
    return !!permissions.value[key]
  }

  // 更新单个权限
  const updatePermission = (key: PermissionKey, value: PermissionValue) => {
    permissions.value[key] = value
  }

  // 批量更新权限
  const updatePermissions = (newPermissions: Partial<Record<PermissionKey, PermissionValue>>) => {
    permissions.value = { ...newPermissions }
  }

  // 重置所有权限
  const resetPermissions = () => {
    permissions.value = {}
  }

  return {
    permissions,
    hasPermission,
    checkPermission,
    updatePermission,
    updatePermissions,
    resetPermissions,
    fetchPermissions,
    isPermissionsEmpty,
  }
})

export const usePermissonsStoreHook = () => {
  return usePermissonsStore()
}
