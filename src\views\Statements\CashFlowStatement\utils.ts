
import { formatMoney } from "@/util/format";
// 判断 level1
export const hasNumberTitle = (value: string='') => {
    const chinaNumber = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"];
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.indexOf(chinaNumber[i] + "、") > -1) return true;
    }
    return false;
};

// 拆开字符串
export const calcFormula = (row: any, columnIndex: number) => {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.note?.split("|")[0] ?? "";
            break;
        case 1:
            formula = row.note?.split("|")[1] ?? "";
            break;
        case 2:
            formula = row.note?.split("|")[2] ?? "";
            break;
        case 3:
            formula = row.note?.split("|")[3] ?? "";
            break;
        case 4:
            formula = row.note?.split("|")[4] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
};

// 替换行次
export const rowNumberFormatter = (_row: any, _column: any, LineNumber: any) => (LineNumber === 0 ? "" : LineNumber);

// 合计
export const NoCalculateIconFormatter = (oraginalValue: number) => {
    const moneyDisplay = formatMoney(oraginalValue);
    let activeValueString: string;
    if (oraginalValue > 0) {
        activeValueString = "<span>" + moneyDisplay + "</span>";
    } else if (oraginalValue === 0) {
        activeValueString = "";
    } else {
        activeValueString = "<span class='highlight-red'>" + moneyDisplay + "</span>";
    }
    return activeValueString;
};
