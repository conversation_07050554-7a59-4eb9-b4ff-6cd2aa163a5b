<template>
        <div class="main-content">
            <div class="vat-top">
                <div class="vat-top-lt">
                    <span class="vat-title">税款所属期：</span>
                    <el-date-picker
                        style="width: 120px"
                        :teleported="false"
                        v-model="taxDate"
                        type="month"
                        placeholder="请选择税款所属期"
                        :editable="false"
                        :clearable="false"
                        value-format="YYYY-MM-DD"
                        :disabled-date="disabledTaxDate"
                    ></el-date-picker>
                    <a class="button solid-button ml-20" @click="getCalculate">查询</a>
                </div>
                <div class="vat-top-rt">
                    <a class="button solid-button ml-20" v-permission="['invoice-fetch-canedit']" v-show="!isThrid" @click="goToFetch">同步认证状态</a> 
                    <el-tooltip effect="light" placement="right">
                        <template #content>
                            <div style="width: 230px">通过一键取票同步更新进项发票在电子税局的勾选认证状态和日期</div>
                        </template>
                        <i
                            v-permission="['invoice-fetch-canedit']"
                            v-show="!isThrid"
                            class="mark-question-icon"
                        ></i>
                    </el-tooltip>
                </div>
            </div>
            <div class="main-center">
                <div :class="['vat-table', {'erp': isErp}]">
                    <div class="vat-table-row head">
                        <div class="vat-table-col">项目</div>
                        <div class="vat-table-col">金额</div>
                    </div>
                    <div class="vat-table-body">
                        <div class="vat-table-row">
                            <div class="vat-table-col">销项税额</div>
                            <div class="vat-table-col">
                                <div class="vat-table-gs" @mouseenter="enterCol(1)" @mouseleave="leaveCol">
                                    <div class="calc-icon" v-show="visibleCount === 1" @click="clickIcon(1)"></div>
                                    <div class="vat-tooltip" v-show="TooltipCount === 1">{{ `取值：开票日期为${taxDate.slice(0, 4)}年${taxDate.slice(5, 7)}月的销项发票税额合计数` }}</div>
                                </div>
                                <div class="vat-table-val" @mouseenter="enterCol(1)" @mouseleave="leaveCol">{{ formatMoney(calculationRes.salesTax, true, true) }}</div>
                            </div>
                        </div>
                        <div class="vat-table-row">
                            <div class="vat-table-col">进项税额（本期认证）</div>
                            <div class="vat-table-col">
                                <div class="vat-table-gs" @mouseenter="enterCol(2)" @mouseleave="leaveCol">
                                    <div class="calc-icon" v-show="visibleCount === 2" @click="clickIcon(2)"></div>
                                    <div class="vat-tooltip" v-show="TooltipCount === 2">
                                        {{ `取值：认证日期为${calculationRes.issueStartDate}至${calculationRes.issueEndDate}的已认证进项发票税额合计数` }}
                                    </div>
                                </div>
                                <div class="vat-table-val" @mouseenter="enterCol(2)" @mouseleave="leaveCol">
                                    {{ formatMoney(calculationRes.purchaseTax, true, true) }}
                                </div>
                            </div>
                        </div>
                        <div class="vat-table-row">
                            <div class="vat-table-col">期初进项留抵税额</div>
                            <div class="vat-table-col">
                                <div class="vat-table-gs" @mouseenter="enterCol(3)" @mouseleave="leaveCol">
                                    <div class="calc-icon" v-show="visibleCount === 3" @click="clickIcon(3)"></div>
                                    <div class="vat-tooltip" v-show="TooltipCount === 3">
                                        {{ !calculationRes.isCustomTaxRetained ?  "取值：上一期已认证进项税额+期初进项留抵税额-销项税额；（计算结果为负时，取值为0）" : "取值：手动录入" }}
                                        <span class="vat-tooltip-link" v-if="calculationRes.isCustomTaxRetained" @click="restFormauls">按公式重置</span>
                                    </div>
                                    <div class="edit-icon" v-show="visibleCount === 4" @click="getInputBox" @mouseenter="enterCol(4)">
                                        <el-icon><EditPen /></el-icon>
                                    </div>
                                </div>
                                <div v-if="!showInput" 
                                    class="vat-table-val val-link" 
                                    @mouseenter="enterCol(3)" 
                                    @mouseleave="leaveCol" 
                                    @click="changeEditIcon"
                                >
                                    {{ formatMoney(calculationRes.taxRetained, true, true) }}
                                </div>  
                                <div v-if="showInput" class="vat-table-input">
                                    <el-input 
                                        v-model="taxRetained" 
                                        ref="cellInputRef"
                                        @input="limitInput" 
                                        @blur="handleInputBlur" 
                                        @keydown.enter="handleInputBlur"
                                    ></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="vat-center">
                    <div id="vat-left" class="tax-balance">
                        <div :class="[
                            'vat-value', 
                            calculationRes.salesTax > (calculationRes.purchaseTax + calculationRes.taxRetained) ? 'vat-sales-right': 
                            calculationRes.balanceTax === 0 ? 'vat-sales-equal' : 'vat-sales-left' ]"
                        >
                            <span>{{ calculationRes.salesTaxText }}</span>
                            <div class="vat-info">(本期销项)</div>
                        </div>
                        <div :class="[
                            'vat-value', 
                            calculationRes.salesTax > (calculationRes.purchaseTax + calculationRes.taxRetained) ? 'vat-balance-right': 
                            calculationRes.balanceTax === 0 ? 'vat-balance-equal' : 'vat-balance-left']"
                        >
                            <span>{{ calculationRes.balanceTaxText }}</span>
                        </div>
                        <span :class="[
                            'vat-value', 
                            calculationRes.salesTax > (calculationRes.purchaseTax + calculationRes.taxRetained) ? 'vat-purchase-right': 
                            calculationRes.balanceTax === 0 ? 'vat-purchase-equal' : 'vat-purchase-left']"
                        >
                        <span>{{ formatMoney(calculationRes.purchaseTax + calculationRes.taxRetained, true, true) }}</span>
                        <div class="vat-info">(本期进项+期初留抵)</div>
                    </span>
                        <div :class="[
                            'vat-bg',
                            calculationRes.salesTax > (calculationRes.purchaseTax + calculationRes.taxRetained) ? 'tax-right':
                            calculationRes.salesTax === (calculationRes.purchaseTax + calculationRes.taxRetained) ?'tax-equal':'tax-left']"
                        ></div>
                    </div>
                    <div id="vat-right">
                        <div class="vat-title vat-desc-line">增值税测算公式：</div>
                        <div class="vat-desc-line">预计应交增值税&nbsp;=&nbsp;销项税额&nbsp;-&nbsp;进项税额(本期认证)&nbsp;-&nbsp;期初进项留抵税额</div>
                        <div class="statement vat-desc-line">
                            =&nbsp;<span id="spanSales">{{ calculationRes.salesTaxText }}</span
                            >&nbsp;-&nbsp;<span id="spanPurchase">{{ calculationRes.purchaseTaxText }}</span
                            >&nbsp;-&nbsp;<span id="spanRetain">{{ calculationRes.taxRetainedText }}</span>
                        </div>
                        <div class="statement vat-desc-line">
                            =&nbsp;<span id="spanBalance">{{ calculationRes.salesTax >= (calculationRes.purchaseTax + calculationRes.taxRetained) ? calculationRes.balanceTaxText : '-'+calculationRes.balanceTaxText }}</span>
                        </div>
                        <div id="vatSales" v-if="calculationRes.salesTax > (calculationRes.purchaseTax + calculationRes.taxRetained)" class="vat-block vat-desc-line">
                            <div class="small">
                                *预计本期应交增值税为<span id="spanTax">{{ calculationRes?.balanceTaxText }}</span>元。
                            </div>
                        </div>
                        <div id="vatPurcahse" v-if="calculationRes.salesTax < (calculationRes.purchaseTax + calculationRes.taxRetained)" class="vat-block vat-desc-line">
                            <div class="small">*本期抵扣的进项税+期初进项留抵税额大于销项税，无需缴纳增值税。</div>
                            <div class="highlight-red mt-10">预警提示：增值税税负率&lt;0，请重点留意！</div>
                        </div>
                        <div id="vatBalance" class="vat-block vat-desc-line" v-if="calculationRes.balanceTax === 0">
                            <div class="small">*本期抵扣的进项税等于销项税，无需缴纳增值税。</div>
                        </div>
                    </div>
                </div>
                <div class="line-chart">
                    <div class="line-chart-top">
                        <div class="line-chart-title">应交增值税趋势图</div>
                        <div class="line-chart-sel">
                            <el-select 
                                v-model="taxYear" 
                                style="width: 100px" 
                                :teleported="false" 
                                :fit-input-width="true"
                                @change="getCalculateTrend"
                            >
                                <el-option
                                    v-for="item in periodYear"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </div>
                    </div>
                    <div id="line-echarts" style="width: 900px; height: 300px;"></div>
                </div>
            </div>
        </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { request,type IResponseModel } from "@/util/service";
import {ElNotify} from "@/util/notify";
import type { ICalculationRes2} from "../types";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { globalWindowOpenPage } from "@/util/url";
import { dayjs, type ElInput } from "element-plus";
import { formatMoney } from "@/util/format";

import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import {
  TooltipComponent,
  GridComponent,
  // 数据集组件
  DatasetComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
  LegendComponent
} from 'echarts/components';
// 注册必须的组件
echarts.use([
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);

const isThrid = ref(useThirdPartInfoStoreHook().isThirdPart);
const isErp = ref(window.isErp);
const taxDate = ref("");
taxDate.value = dayjs().format("YYYY-MM-DD");
const startTaxDate = ref("");

const visibleCount = ref(0); //1销项；2进项；3留抵；4编辑
const TooltipCount = ref(0);

const changeEditIcon = () => {
    visibleCount.value = 4;
    TooltipCount.value = 0;
}
const leaveCol = () => {
    visibleCount.value = 0;
    TooltipCount.value = 0;
};
const enterCol = (type: number) => {
    if (visibleCount.value === 4 && type === 3) return;
    visibleCount.value = type;
}
const clickIcon = (type: number) => {
    TooltipCount.value = type;
}
//期初进项留抵税额
let calculationRes = ref<ICalculationRes2>({
    salesTax: 0,
    purchaseTax: 0,
    taxRetained: 0,
    balanceTax: 0,
    salesTaxText: '0',
    purchaseTaxText: '0',
    taxRetainedText: '0',
    balanceTaxText: '0',
    issueEndDate: "",
    issueStartDate: "",
    isCustomTaxRetained: false,
});
const taxRetained = ref(String(calculationRes.value.taxRetained));
const showInput = ref(false);
const getInputBox = ()=> {
    showInput.value = true;
    nextTick().then(() => {
        taxRetained.value = String(calculationRes.value.taxRetained);
        cellInputRef.value?.select();
    });
}
const limitInput = (val: string) => {
    val = val.replace(/。/g, ".");
    const dotIndex = val.indexOf(".");
    if (dotIndex !== -1) {
        // 如果已经存在小数点，去除后面的小数点
        val = val.slice(0, dotIndex + 1) + val.slice(dotIndex).replace(/\./g, "");
        const decimalPart = val.slice(dotIndex + 1);
        if (decimalPart.length > 2) {
            val = val.slice(0, dotIndex + 3);
        }
    }
    if (val.length >= 18) {
        val = val.slice(0, 1) + "." + val.slice(1);
    }
    taxRetained.value = val.replace(/[^0-9.]/g, "");
}
const cellInputRef = ref<InstanceType<typeof ElInput>>();
const timer = ref(0);
const handleInputBlur = () => {
    if (timer.value) return;
    blurSave();
    timer.value = setTimeout(() => {
        timer.value = 0;
    }, 200); 
};

function blurSave() {
    const value = taxRetained.value.toString().trim();
    if (isNaN(Number(value))) {
        ElNotify({ type: "warning", message: "请输入数字金额" });
        return;
    }
    request({ 
        url: "/api/VATCalculation/ModifyTaxRetained",
        method: "post",
        params: {
            taxPeriod: taxDate.value,
            taxRetained: Number(value) ? Number(value) : 0,
        }
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            ElNotify({ type: "success", message: "保存成功" });
            getCalculate();
            getCalculateTrend();
        } else {
            ElNotify({ type: "warning", message: "保存失败" });
        }
    }).finally(()=>{
        showInput.value = false;
    });
}

function disabledTaxDate(time: Date){
    let disStartTaxDate = dayjs(startTaxDate.value).valueOf();
    return time.getTime() < disStartTaxDate || time.getTime() > Date.now();
}

const goToFetch = () => {
    if (isErp.value) {
        globalWindowOpenPage("/Invoice/PurchaseInvoice", '进项发票');
    }
    globalWindowOpenPage("/Invoice/PurchaseInvoice?fetch=true", '进项发票'); 
}
// 获取日期区间数据
function getSearchDate() {
    request({
        url: "/api/VATCalculation/GetSearchDate2",
        method: "post",
    }).then((res: IResponseModel<{startDate: string, defaultDate: string, year: number}>) => {
        if (res.state === 1000) {
            taxDate.value = res.data.defaultDate;
            startTaxDate.value = res.data.startDate;
            getYearList(res.data.year);
            getCalculate();
            getCalculateTrend();
        }
    });
}
function getCalculate() {
    if(!taxDate.value) {
        ElNotify({
            type: "warning",
            message: `增值税测算税款所属期不能为空！`,
        });
        return;
    }
    request({
        url: "/api/VATCalculation/Calculate2",
        params: {
            taxPeriod: taxDate.value,
        },
        method: "post",
    }).then((res: IResponseModel<ICalculationRes2>) => {
        if (res.state === 1000) {
            calculationRes.value = res.data;
        }
    });
}
const restFormauls = () => {
    request({
        url: "/api/VATCalculation/ResetTaxRetained",
        params: {
            taxPeriod: taxDate.value,
        },
        method: "post",
    }).then((res: IResponseModel<boolean>)=>{
        if (res.state === 1000 && res.data) {
            getCalculate();
            ElNotify({
                type: "success",
                message: "按公式重新计算成功",
            });
            getCalculateTrend();
        }
    })
}
//趋势图
interface IYearItem {
    value: number;
    label: string;
}
const taxYear = ref(new Date().getFullYear());
const periodYear = ref<Array<IYearItem>>([]);
function getYearList(year: number) {
    const currentYear = new Date().getFullYear();
    while (year <= currentYear) {
        periodYear.value.unshift({
            value: year,
            label: year +"年"
        });
        year++;
    }
}
let xData: string[] = [];
let lineData: number[] = [];
function echart1() {
    let div = document.getElementById('line-echarts') as HTMLElement;
    let myChart = echarts.init(div);
    let dataLength = 0;
    let maxVal = Math.max(...lineData);
    if (maxVal > 1000000) {
        dataLength = 2; 
    } else if(maxVal > 1000) {
        dataLength = 1;
    }
    let option = {
        grid: {
            left: dataLength === 2 ? '8%' : dataLength === 1 ? '6%' : '4%', // 调整左边距
            right: '0%', // 调整右边距
            top: '10%', // 可以调整上边距
            bottom: '15%', // 可以调整下边距
        },
        tooltip: {
            trigger: 'axis', // 设置触发条件为坐标轴
            axisPointer: {
                type:"shadow",
            },
            textStyle: {
                align: "left"
            }
        },
        xAxis: {
            type: 'category',
            data: xData
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                data: lineData,
                type: 'line'
            }
        ]
    };
    option && myChart.setOption(option);
}
interface ITrendItem {
    month: string;
    vatTax: number;
}
const trendData = ref<Array<ITrendItem>>([]);
function getCalculateTrend() {
    xData = [];
    lineData = [];
    request({
        url: "/api/VATCalculation/VatTrend",
        params: {
            year: taxYear.value,
        },
        method: "post",
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            trendData.value = res.data.vatTrendList;
            trendData.value.forEach((item: ITrendItem) => {
                xData.push(item.month);
                lineData.push(item.vatTax);
            });
            echart1();
        }
    });
}
defineExpose({
    getSearchDate,
})
</script>

<style lang="less" scoped>
@import url(@/style/SelfAdaption.less);

* {
    box-sizing: border-box;
}
:deep(.el-date-editor) {
    .el-input__prefix {
        position: absolute;
        right: 0;
    }
    .el-input__suffix-inner {
        position: absolute;
        right: 30px;
        top: 9px;
    }
}

.main-content {
    background-color: #fff;
    width: 900px;
    margin: auto;
    .vat-title {
        font-size: 14px;
        color: #000;
    }
    .vat-top {
        padding: 30px 0;
        text-align: left;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        :deep(.el-date-picker) {
            .el-month-table,
            .el-year-table {
                td.disabled.today .cell {
                    color: #a8abb2 !important;
                }
                td.current:not(.disabled) .cell {
                    background-color: var(--main-color);
                    color: var(--white) !important;
                }
            }
        }
    }
    .vat-top-rt {
        display: flex;
    }
    .mark-question-icon {
        background-image: url("@/assets/Settings/question.png");
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 16px 16px;
        font-size: 16px;
        margin-left: 3px;
        margin-top: -3px;
        height: 16px;
        width: 16px;
        cursor: pointer;
        display: block;
    }
    .vat-table {
        border: 1px solid var(--border-color);
        .vat-table-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            font-size: 14px;
            &.head {
                background-color: #eee;
                font-weight: 600;
            }
        }
        .vat-table-body {
            .vat-table-row:last-child {
                border-bottom: none;
            }
            .vat-table-col:last-child {
                padding: 0;
            }
        }
        .vat-table-col {
            position: relative;
            padding: 0 10px;
            width: 50%;
            min-height: 36px;
            border-right: 1px solid var(--border-color);
            line-height: 36px;
            text-align: left;
            &:last-child {
                border-right: none;
                text-align: right;
            }
        }
        .calc-icon {
            width: 24px;
            height: 24px;
            background: url(@/assets/Statements/equal.png) no-repeat center center;
            background-size: 12px 12px;
            cursor: pointer;
        }
        &.erp {
            .calc-icon {
                background: url(@/assets/Statements/equal-erp.png) no-repeat center center;
                background-size: 12px 12px;
            }
        }
        .edit-icon {
            width: 24px;
            line-height: 28px;
            color: var(--link-color);
            cursor: pointer;
            .el-icon {
                position: relative;
                left: -4px;
            }
        }
        .vat-table-val {
            padding-right: 10px;
            width: 100%;
            text-align: right;
            &.vat-link {
                cursor: pointer;
            }
        }
        .vat-table-input {
            padding: 0 10px;
            :deep(.el-input) {
                top: -1px;
            }
        }
        .vat-table-gs {
            position: absolute;
            top: 6px;
            left: 4px;
            width: 24px;
            height: 24px;
            .vat-tooltip {
                position: absolute;
                right: -2px;
                top: 50%;
                transform: translate(100%, -50%);
                padding: 10px 8px;
                width: 310px;
                word-break: break-all;
                background-color: var(--white);
                border: 1px solid #e4e7ed;
                filter: drop-shadow(0px 0px 12px rgba(0, 0, 0, 0.12));
                font-size: 12px;
                line-height: 20px;
                text-align: left;
                border-radius: 4px;
                &:before {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    content: "";
                    width: 10px;
                    height: 10px;
                    background-color: var(--white);
                    transform: translate(-50%, -50%) rotate(45deg);
                    border: 1px solid #e4e7ed;
                    border-right-color: transparent;
                    border-top-color: transparent;
                    z-index: -1;
                }
            }
            .vat-tooltip-link {
                padding-left: 120px;
                cursor: pointer;
                color: var(--link-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
            }
        }
    }
    .vat-center {
        padding-top: 50px;
        overflow: hidden;
        height: 300px;
        display: flex;
        border: 1px solid var(--border-color);
        border-top: none;

        .vat-info {
            margin-top: 2px;
            font-size: 12px;
            white-space: nowrap;
        }

        #vat-left {
            left: 90px;
            width: 450px;
            position: relative;
            .vat-bg {
                position: absolute;
                left: 0px;
                top: 0px;
                width: 264px;
                height: 207px;
            }

            .vat-bg.tax-right {
                background: url("@/assets/Invoice/right-scales.png") no-repeat center #fff;
            }
            .vat-bg.tax-equal {
                background: url("@/assets/Invoice/balance-scales.png") no-repeat center #fff;
            }
            .vat-bg.tax-left {
                background: url("@/assets/Invoice/left-scales.png") no-repeat center #fff;
            }
            .vat-value {
                display: inline-block;
                position: absolute;
                width: 100px;
                text-align: center;
                height: 20px;
                text-align: center;
                &.vat-sales-right {
                    top: 178px;
                    left: -10px;
                    z-index: 99;
                }
                &.vat-balance-right {
                    top: 18px;
                    left: 78px;
                    z-index: 99;
                    transform: rotate(-15deg);
                }
                &.vat-purchase-right {
                    top: 128px;
                    left: 180px;
                    z-index: 99;
                }
                &.vat-sales-equal {
                    top: 156px;
                    left: -15px;
                    z-index: 99;
                }
                &.vat-balance-equal {
                    top: 18px;
                    left: 78px;
                    z-index: 99;
                }
                &.vat-purchase-equal {
                    top: 154px;
                    left: 184px;
                    z-index: 99;
                }
                &.vat-sales-left {
                    top: 130px;
                    left: -15px;
                    z-index: 99;
                }
                &.vat-balance-left {
                    top: 18px;
                    left: 90px;
                    z-index: 99;
                    transform: rotate(15deg);
                }
                &.vat-purchase-left {
                    top: 178px;
                    left: 184px;
                    z-index: 99;
                }
            }
        }
        #vat-right {
            width: 450px;
            padding-left: 10px;
            margin-top: 40px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 26px;
            vertical-align: top;
            line-height: 32px;
            font-size: 14px;
            .vat-title {
                font-weight: 600;
            }
            .vat-desc-line {
                text-align: left;
                &.statement {
                    padding-left: 102px;
                }
            }
            .vat-block {
                color: #929292;
            }
        }
    }
}
.line-chart {
    margin-top: 30px;
    .line-chart-top {
        display: flex;
        justify-content: space-between;
    }
    .line-chart-title {
        font-weight: 600;
        font-size: 14px;
    }
}
body[erp] {
    .main-content {
        height:auto !important;
    }
}
</style>

