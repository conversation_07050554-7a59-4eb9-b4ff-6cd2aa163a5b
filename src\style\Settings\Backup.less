
@import "../Functions.less";
.content {
    .main-content {
        color: #404040;
        font-size: 12px;
        position: relative;
        flex-direction: column-reverse;
        padding-bottom: 10px;
        box-sizing: border-box;
        :deep(.el-tabs) {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            .el-tabs__header {
                margin-bottom: 0;
            }
            .el-tabs__item{
                padding:24px 14px;
            }
            .el-tabs__content {
                flex: 1;
            }
            .el-tab-pane {
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
        :deep(.el-table) {
            .lack-space-tip {
                background-color: #fafafa;
                & > .el-table__cell {
                    &:first-child {
                        > .cell {
                            &::before {
                                content: " ";
                                background-image: url("@/assets/Settings/temporary.svg");
                                background-repeat: no-repeat;
                                background-position: 0 0;
                                background-size: 30px 30px;
                                width: 30px;
                                height: 30px;
                                position: absolute;
                                left: 0;
                                top: 0;
                            }
                        }
                    }
                }
                .cell {
                    color: rgba(21, 21, 21, 0.45);
                }
            }
            .el-scrollbar__bar {
                display: block !important;
            }
        }

        .float-r {
            margin-left: auto;
        }

        .main-tool-bar {
            line-height: 28px;
        }
        .progress-bar {
            width: 120px;
            height: 9px;
            border-radius: 5px;
            background-color: #e4e4e4;
            margin-right: 4px;
            display: flex;
            align-items: center;
            .progress-solid-bar {
                height: 9px;
                border-radius: 5px;
                background-color: #3385ff;
            }
        }
        .tips {
            padding-left: 20px;
            padding-right: 10px;
            padding-top: 20px;
            text-align: left;
            .tip-title {
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 17px;
                font-weight: bold;
            }
            .tip-content {
                div {
                    margin-top: 14px;
                    font-size: 0;
                    img {
                        vertical-align: top;
                    }
                    span {
                        color: var(--font-color);
                        font-size: var(--h5);
                        line-height: 17px;
                        vertical-align: top;
                        &.highlight-gray1 {
                            color: #a1a1a1;
                        }
                        &.highlight-green {
                            color: var(--main-color);
                        }
                        &.highlight-gray2 {
                            color: #979797;
                        }
                    }
                }
            }
        }
        .div-audit-first {
            margin-top: 40px;
            height: 394px;
            background: url("@/assets/Settings/audit-bg.png") no-repeat;
            background-size: contain;
            background-position-x: right;
            padding-left: 60px;
            text-align: left;
            position: relative;
            border: 1px solid var(--el-border-color-light);
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
            .audit-first-title {
                padding-top: 90px;
                color: var(--font-color);
                font-size: 20px;
                line-height: 25px;
                font-weight: 500;
                margin-bottom: 35px;
            }
            .audit-line {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 16px;
                display: flex;
                align-items: center;
                & + .audit-line {
                    margin-top: 24px;
                }
                .audit-content {
                    height: 30px;
                    width: 260px;
                    .detail-el-select(100%, 100%);
                    span {
                        margin: 0 auto;
                    }
                    &.small {
                        display: flex;
                        justify-content: space-evenly;
                        align-items: center;
                        .detail-el-select(120px, 100%);
                    }
                }
            }
            .audit-first-button {
                .button {
                    width: 128px;
                    height: 36px;
                    border-radius: 4px;
                    line-height: 36px;
                    font-weight: 600;
                    font-size: 14px;
                    border-color: #3385ff;
                    background-color: #3385ff;
                }
            }
        }
    }
}

.common-dialog-content {
    color: #404040;
    font-size: 12px;
    .common-dialog-main {
        padding: 47px 63px 47px 65px;
        min-height: 42px;
        .audit-success-title {
            font-size: 0;
            font-weight: 500;
            img {
                width: 13px;
                margin-right: 9px;
                vertical-align: middle;
            }
            span {
                color: var(--font-color);
                font-size: 14px;
                line-height: 14px;
                vertical-align: middle;
            }
        }
        .audit-success-info {
            margin-top: 16px;
            color: #777777;
            font-size: 14px;
            line-height: 16px;
            font-weight: 400;
        }
        &.process {
            padding: 28px 44px 29px 44px;
            .audit-line {
                color: var(--font-color);
                font-size: 12px;
                line-height: 16px;
                font-weight: 400;
                height: 30px;
                display: flex;
                align-items: center;
                .audit-info {
                    flex: 1;
                }
                .audit-content {
                    width: 260px;
                    .detail-el-select(100%, 30px);
                    &.small {
                        display: flex;
                        justify-content: space-evenly;
                        align-items: center;
                        .detail-el-select(120px, 30px);
                    }
                }
            }
        }
    }
    .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }
}
.common-dialog-content {
    .common-dialog-main {
        padding: 40px;
        text-align: center;
        .common-dialog-main-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            font-weight: normal;
            text-align: center;
            .file-box {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                margin-left: 20px;
                max-width: 160px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: top;
            }
        }
    }
    &.erp {
        .common-dialog-main {
            text-align: left;
        }
    }
}
.restore-dialog-content {
    .restore-dialog-main {
        padding: 24px 16px 32px;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        .link-scm-desc {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            margin-bottom: 8px;
            align-self: center;
        }
        .restore-desc {
            background: #fff6f5;
            padding: 12px 0 12px 12px;
            font-size: 12px;
            color: #777777;
            line-height: 16px;
            margin-bottom: 24px;
        }
        .radio-btns {
            margin-left: 12px;
            :deep(.el-radio-group) {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
            }
        }
    }
}
.tab-right-content {
    display: flex;
    position: absolute;
    top: 5px;
    right: 0;
    z-index: 1;
    align-items: center;
    height: 40px;
    padding-right: 10px;
    .disk-state {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        margin-right: auto;
    }
    .autoprocess-content {
        width: auto;
        padding-right: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .autoprocess-content-checkbox {
            margin: auto;
            .el-checkbox__label {
                padding-left: 2px;
            }
        }
    }
    .update-btn {
        display: inline-block;
        min-width: 86px;
        height: 26px;
        background: url("@/assets/ERecord/upgrade-expand.png") no-repeat;
        background-size: cover;
        cursor: pointer;
        margin: 0 4px;
    }
}
.firstShow-content {
    background: var(--shadow-color);
    text-align: center;
    vertical-align: middle;
    position: relative;
    background: #fff;
    text-align: center;
    display: inline-block;
    padding-top: 19px;
    padding-bottom: 19px;
    .firstShow-close {
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
    }
    .firstShow-text {
        margin-top: 10px;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
        padding: 0 53px;
    }
    .firstShow-confirm {
        margin-top: 20px;
        text-align: center;
        background: #3385ff;
        width: 102px;
        height: 32px;
        line-height: 32px;
        display: inline-block;
        font-size: 12px;
        color: var(--white);
        cursor: pointer;
    }
}
.setauto-process-content {
    color: #404040;
    font-size: 12px;
    .setauto-process-main {
        padding: 20px;
        .choose-item {
            padding-top: 10px;
            height: 30px;
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 30px;
            .span-label {
                width: 98px;
                text-align: right;
            }
            .detail-el-select(100px, 30px);
            > input {
                .detail-original-input(180px, 30px);
            }
            .weaker-font {
                color: var(--weaker-font-color);
                text-decoration: underline;
                margin-right: 5px;
                vertical-align: middle;
            }
            > img {
                cursor: pointer;
                vertical-align: middle;
            }
        }
    }
}
.company-notice {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    .all-line {
        width: 100%;
        &.full-text {
            height: 70px;
            font-size: 15px;
            font-weight: normal;
            width: 300px;
        }
        &.border-top {
            border-top: 1px solid var(--border-color);
        }
        p {
            display: block;
            margin-block-start: 1em;
            margin-block-end: 1em;
            margin-inline-start: 0px;
            margin-inline-end: 0px;
        }
        img {
            width: 150px;
            height: 150px;
            margin-top: 12px;
            margin-bottom: 24px;
        }
        .btn {
            border: 0;
            background: #44b449;
            color: white;
            width: 70px;
            height: 30px;
        }
    }
}
span.loading-operator {
    display: flex;
    align-items: center;
    span.loading-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url("@/assets/Icons/loading.svg") no-repeat;
        background-size: 100% 100%;
        animation: spin 3s linear infinite;
    }
}
.error-row {
    .link-error {
        color: #ff7500;
        &:hover {
            cursor: pointer;
            color: var(--link-color);
            text-decoration: underline;
        }
    }
}
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
