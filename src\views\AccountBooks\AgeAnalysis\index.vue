<template>
    <div class="content">
        <div class="main-content">
            <div class="title">账龄分析表</div>
            <div class="main-content">
                <div class="main-top main-tool-bar space-between split-line">
                    <div class="main-tool-left">
                        <SearchInfoContainer ref="containerRef">
                            <template v-slot:title>{{ currentPeriodInfo }}</template>
                            <div class="line-item first-item input">
                                <div class="line-item-title">会计期间：</div>
                                <div class="line-item-field">
                                    <DatePicker
                                        v-model:startPid="searchInfo.startMonth"
                                        v-model:endPid="searchInfo.endMonth"
                                        :clearable="false"
                                        :editable="false"
                                        :dateType="'month'"
                                        :value-format="'YYYYMM'"
                                        :label-format="'YYYY年MM月'"
                                        :isPeriodList="true"
                                        @getActPid="getActPid"
                                    />
                                </div>
                            </div>
                            <div class="line-item input">
                                <div class="line-item-title">会计科目：</div>
                                <div class="line-item-field">
                                    <AgeAnalysisSubjectPicker v-model:sbjCode="searchInfo.sbjCode" :subjectList="subjectList" />
                                </div>
                            </div>
                            <div class="line-item single">
                                <div class="line-item-title">
                                    <el-checkbox v-model="searchInfo.isBalanceZero" label="余额为0不显示"></el-checkbox>
                                </div>
                            </div>
                            <div class="buttons">
                                <a class="button solid-button" @click="handleSearchSubmit">确定</a>
                                <a class="button" @click="handleClose">取消</a>
                                <a class="button" @click="handleReset">重置</a>
                            </div>
                        </SearchInfoContainer>
                        <AgeSetPopover
                            :ageSetList="ageSetList"
                            :defaultAgeSetList="defaultAgeSetList"
                            :loading="ageSetDialogLoading"
                            @change="handleChangeAgeSet"
                        />
                        <ErpRefreshButton></ErpRefreshButton>
                    </div>
                    <div class="main-tool-right">
                        <el-checkbox class="mr-10" label="显示比例" v-model="searchInfo.isShowPercent"></el-checkbox>
                        <!-- <a v-permission="['ageanalysis-canprint']" class="button ml-10" @click="printDialogVisible = true">打印</a> -->
                        <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['ageanalysis-canprint']">
                            <li @click="handlePrint(0,getParams())">直接打印</li>
                            <li @click="handlePrint(2)">打印设置</li>
                        </Dropdown>
                        <a v-permission="['ageanalysis-canexport']" class="button" @click="handleExport">导出</a>
                        <RefreshButton></RefreshButton>
                    </div>
                </div>
                <div
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :class="['main-center', { erp: isErp }, { showDataMask: showDataMask }]"
                >
                    <el-table
                        ref="AgeAnalysisTableRef"
                        border
                        stripe
                        show-overflow-tooltip
                        :class="['ageAnalysisTable', { 'erp-table': isErp }]"
                        :data="tableData"
                        :scrollbar-always-on="true"
                        :tooltip-options="{ effect: 'light', placement: 'right-start', offset: -10 }"
                        @header-dragend="headerDragend"

                    >
                        <el-table-column 
                            fixed label="编码" 
                            prop="num" 
                            align="left" 
                            header-align="center" 
                            :width="getColumnWidth(setModule, 'num')"
                        ></el-table-column>
                        <el-table-column 
                            fixed label="名称" 
                            align="left" 
                            header-align="center"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <a v-if="canLink(scope.row.params)" class="link" @click="showDetail(scope.row.params)">
                                    {{ handleAsubName(scope.row.name) }}
                                </a>
                                <span class="grey-text" v-else>{{ handleAsubName(scope.row.name) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            label="期初余额" 
                            prop="initial" 
                            align="left" 
                            header-align="center"
                            :width="getColumnWidth(setModule, 'initial')"
                        ></el-table-column>
                        <el-table-column 
                            label="借方" 
                            prop="debit" 
                            align="left" 
                            header-align="center"
                            :width="getColumnWidth(setModule, 'debit')"
                        ></el-table-column>
                        <el-table-column 
                            label="贷方" 
                            prop="credit" 
                            align="left" 
                            header-align="center"
                            :width="getColumnWidth(setModule, 'credit')"
                        ></el-table-column>
                        <el-table-column 
                            label="期末余额" 
                            prop="total" 
                            align="left" 
                            header-align="center"
                            :width="getColumnWidth(setModule, 'total')"
                        >
                            <template v-if="!searchInfo.isShowPercent" #default="scope">
                                {{ scope.row.total }}
                            </template>
                            <el-table-column v-if="searchInfo.isShowPercent" show-overflow-tooltip label="金额">
                                <template #default="scope">
                                    {{ scope.row.total }}
                                </template>
                            </el-table-column>
                            <el-table-column v-if="searchInfo.isShowPercent" show-overflow-tooltip label="比例">
                                <template #default="scope"> {{ scope.row.totalPercent }}</template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column
                            v-for="(item, index) in ageSetList"
                            :key="item.ageId"
                            :label="item.title"
                            align="left"
                            header-align="center"
                            :prop="item.ageId"
                            :width="getColumnWidth(setModule, item.ageId.toString())"
                            :resizable="!(index === ageSetList.length - 1)"
                        >
                            <template v-if="!searchInfo.isShowPercent" #default="scope">
                                {{ scope.row.ages[index].amount }}
                            </template>
                            <el-table-column v-if="searchInfo.isShowPercent" show-overflow-tooltip label="金额">
                                <template #default="scope">
                                    {{ scope.row.ages[index].amount }}
                                </template>
                            </el-table-column>
                            <el-table-column v-if="searchInfo.isShowPercent" show-overflow-tooltip label="比例">
                                <template #default="scope"> {{ scope.row.ages[index].percent }}</template>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                    <TablePagination
                        class="pagination"
                        :size="'default'"
                        :layout="'total, sizes, prev, pager, next, jumper'"
                        :page-sizes="paginationData.pageSizes"
                        :page-size="paginationData.pageSize"
                        :total="paginationData.total"
                        :current-page="paginationData.currentPage"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        @refresh="handleRerefresh"
                    >
                    </TablePagination>
                    <DataMask v-if="showDataMask" ref="dataMaskRef" :showLines="5" :hasPage="true" />
                </div>
            </div>
        </div>
    </div>
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        title="账龄分析表打印"
        :printData="printInfo"
        :dir-disabled="true"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getParams())"
    />
</template>
<script lang="ts">
export default {
    name: "AgeAnalysis",
};
</script>
<script lang="ts" setup>
import { ref, reactive, watch, onMounted, computed, nextTick, watchEffect } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { usePagination } from "@/hooks/usePagination";
import Dropdown from "@/components/Dropdown/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import AgeSetPopover from "./components/AgeSetPopover.vue";
import AgeAnalysisSubjectPicker from "./components/AgeAnalysisSubjectPicker.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { initAgeSetList, headerDragend } from "./utils";
import { globalWindowOpenPage, globalWindowOpen, getUrlSearchParams, globalPrint, globalExport } from "@/util/url";
import { handleAsubName } from "@/util/format";
import TablePagination from "@/components/Table/TablePagination.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAccountSetStore } from "@/store/modules/accountSet";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { checkPermission } from "@/util/permission";
import type { IAgeSet, IRows, IAgeSetTableData } from "./types";
import type { ElTable } from "element-plus";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import DataMask from "@/components/DataMask/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import usePrint from "@/hooks/usePrint";
import { isLemonClient } from "@/util/lmClient";

const setModule = "AgeAnalysis";
const _ = getGlobalLodash()
// 会计准则
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;
// 全部科目列表
const accountSubjectStore = useAccountSubjectStore();

const isErp = ref(window.isErp);
const periodStore = useAccountPeriodStore();

// 当前期间
const currentPeriodInfo = ref("");
// const periodInfo = ref("");

// 是否过期
const trialStatusStore = useTrialStatusStore();
const isExpired = computed(() => {
    return trialStatusStore.isExpired;
});
// 是否显示遮罩
const showDataMask = computed(() => isExpired.value && tableData.value.length > 5);

const dataMaskRef = ref<InstanceType<typeof DataMask>>();

const subjectList = ref<IAccountSubjectModel[]>([]);
// 设置默认科目下拉选项
const setInitSubjectOptions = () => {
    subjectList.value = accountSubjectStore.accountSubjectList.filter(
        (item) =>
            item.asubAAName === "应收账款" ||
            item.asubAAName === "应付账款" ||
            item.asubAAName === "应收票据" ||
            item.asubAAName === "应付票据" ||
            item.asubAAName === "其他应收款" ||
            item.asubAAName === "其他应付款" ||
            item.asubAAName === "应收款" ||
            item.asubAAName === "应付款"
    );
    setInitSubject();
};
// 根据账套准则设置默认科目
const setInitSubject = () => {
    switch (accountStandard) {
        case 1:
        case 2:
        case 3:
            // 民非
            searchInfo.sbjCode = subjectList.value.find((item) => item.asubAAName === "应收账款")!.asubCode;
            break;
        case 4:
        case 5:
        case 7:
            // 农合 新农合 农村
            searchInfo.sbjCode = subjectList.value.find((item) => item.asubAAName === "应收款")!.asubCode;
            break;
        case 6:
            // 工会
            searchInfo.sbjCode = subjectList.value.find((item) => item.asubAAName === "其他应收款")!.asubCode;
            break;
        default:
            searchInfo.sbjCode = subjectList.value[0].asubCode;
    }
};

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const loading = ref(false);
// 搜索参数
let searchInfo = reactive({
    periodS: Number(periodStore.getPeriodRange().start),
    periodE: Number(periodStore.getPeriodRange().end),
    // 会计科目
    sbjCode: "",
    // 余额为0不显示 0-不启用 1-启用
    isBalanceZero: true,
    // 展示比例 0-不展示 1-展示
    isShowPercent: true,
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, searchInfo.periodS, searchInfo.periodE);  
const getActPid = (start: number, end: number) => {
    searchInfo.periodS = start;
    searchInfo.periodE = end;
}

// 过滤
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const handleClose = () => containerRef.value?.handleClose();
const handleReset = () => {
    // 重置参数
    searchInfo.periodS = Number(periodStore.getPeriodRange().start);
    searchInfo.periodE = Number(periodStore.getPeriodRange().end);
    searchInfo.isBalanceZero = true;
    setInitSubject();
};

const getParams = () => {
    return {
        periodS: searchInfo.periodS,
        periodE: searchInfo.periodE,
        sbjCode: searchInfo.sbjCode,
        isBalanceZero: searchInfo.isBalanceZero ? 1 : 0,
        isShowPercent: searchInfo.isShowPercent ? 1 : 0,
    };
};

// 是否能搜索
const canSearch = () => {
    // 免费版过期数据遮罩
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    if (searchInfo.periodS > searchInfo.periodE) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return false;
    }
    if (searchInfo.sbjCode == "") {
        ElNotify({
            type: "warning",
            message: "亲，请选择会计科目",
        });
        return false;
    }
    return true;
};

// 表格数据
const tableData = ref<IRows[]>([]);

const handleSearchSubmit = () => {
    if (!canSearch()) return;
    paginationData.currentPage = 1;
    handleSearch();
};

// 刷新表格布局
const AgeAnalysisTableRef = ref<InstanceType<typeof ElTable>>();
const handleSearch = () => {
    setInfos();
    periodStore.changePeriods(String(searchInfo.periodS), String(searchInfo.periodE));
    loading.value = true;
    const params = {
        ...getParams(),
        rows: paginationData.pageSize,
        page: paginationData.currentPage,
    };
    request({
        url: `/api/AgeAnalysis`,
        method: "get",
        params,
    })
        .then((res: IResponseModel<IAgeSetTableData>) => {
            if (res.state === 1000 && res.data) {
                paginationData.total = res.data.total;
                tableData.value = res.data.rows;
                // 只有合计时，不显示
                if (tableData.value.length === 1) {
                    tableData.value = [];
                }
                // 重排表格布局
                // AgeAnalysisTableRef.value?.doLayout();
                // AgeAnalysisTableRef.value?.scrollTo(0, 0);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
            nextTick(() => {
                if (showDataMask.value) {
                    dataMaskRef.value?.getTableHeight();
                }
            });
        });
};

// 账龄设置
const ageSetList = ref<IAgeSet[]>([]);
const defaultAgeSetList = ref<IAgeSet[]>([]);
// 弹窗加载状态
const ageSetDialogLoading = ref(false);
// 获取账龄设置
const getAgeSet = () => {
    ageSetDialogLoading.value = true;
    request({
        url: `/api/AgeAnalysis/GetAgeSet`,
        method: "get",
    })
        .then((res: IResponseModel<IAgeSet[]>) => {
            if (res.state === 1000 && res.data) {
                ageSetList.value = res.data;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
                ageSetList.value = [];
            }
            // 数组为空时给两行初始值
            if (ageSetList.value.length === 0) {
                ageSetList.value = _.cloneDeep(initAgeSetList);
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            // 数组为空时给两行初始值
            if (ageSetList.value.length === 0) {
                ageSetList.value = _.cloneDeep(initAgeSetList);
            }
            ageSetDialogLoading.value = false;
        });
};
// 获取默认账龄
const getDefaultAgeSet = () => {
    ageSetDialogLoading.value = true;
    request({
        url: `/api/AgeAnalysis/GetDefaultAgeSet`,
        method: "get",
    })
        .then((res: IResponseModel<IAgeSet[]>) => {
            if (res.state === 1000 && res.data) {
                defaultAgeSetList.value = res.data;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
                defaultAgeSetList.value = [];
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            // 数组为空时给两行初始值
            if (defaultAgeSetList.value.length === 0) {
                defaultAgeSetList.value = _.cloneDeep(initAgeSetList);
            }
            ageSetDialogLoading.value = false;
        });
};
// 账龄修改确定
const handleChangeAgeSet = (AgeSets: IAgeSet[]) => {
    ageSetDialogLoading.value = true;
    const data = {
        AgeSets,
    };
    request({
        url: `/api/AgeAnalysis/SaveAgeSet`,
        method: "post",
        data,
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000 && res.data) {
                ElNotify({
                    type: "success",
                    message: "修改成功",
                });
                getAgeSet();
                handleSearch();
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            ageSetDialogLoading.value = false;
        });
};

// 点击名称跳转
const showDetail = (params: string) => {
    if (params.includes("ASUB_ID")) {
        globalWindowOpenPage(`/AccountBooks/SubsidiaryLedger?${params}&ran=${Math.random()}`, "明细账");
    } else {
        globalWindowOpenPage(`/AccountBooks/AASubsidiaryLedger?${params}&ran=${Math.random()}`, "核算项目明细账");
    }
    //获取气泡框元素
    const tooltip = document.querySelector(".ageAnalysisTable .el-popper") as HTMLElement;
    // 隐藏气泡框
    if (tooltip) {
        tooltip.style.display = "none";
    }
};

// 打印导出
const extraInfo = {
    isHideSubjectCode:false,
}
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "ageAnalysis",
    window.jAccountBooksHost + "/api/AgeAnalysis/Print",
    extraInfo,
    true,
    true,
    printValidator
);
otherOptions.value = [
    { key: "isHideSubjectCode", label: "表体不打印编码" },
    ...otherOptions.value,
];
printInfo.value.direction = 1;

function printValidator() {
    if (!canSearch()) return false;
    if (tableData.value.length === 0) {
        ElNotify({
            message: `亲，当前没有数据可打印哦`,
            type: "warning",
        });
        return false;
    }
    return true;
}

const handleExport = () => {
    if (!canSearch()) return;
    if (tableData.value.length === 0) {
        ElNotify({
            message: `亲，当前没有数据可导出哦`,
            type: "warning",
        });
        return false;
    }
    const params = {
        ...getParams(),
        async: true,
    };
    globalExport(window.jAccountBooksHost + "/api/AgeAnalysis/Export?" + getUrlSearchParams(params));
};

const setInfos = () => {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.periodS, searchInfo.periodE);
}

// 判断是否能跳转
const canLink = computed(() => (params: string) => {
    // 无权限参数为空
    if (params.trim() === "") return false;
    if (
        (params.includes("ASUB_ID") && checkPermission(["subsidiaryledger"])) ||
        (!params.includes("ASUB_ID") && checkPermission(["aasubsidiaryledger"]))
    ) {
        return true;
    } else {
        return false;
    }
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    // localStorage.setItem("AgeAnalysisSize", paginationData.pageSize + "");
    if (!canSearch()) return;
    handleSearch();
});

watch(
    () => searchInfo.isShowPercent,
    () => {
        localStorage.setItem("AgeAnalysisShowPercent", JSON.stringify(searchInfo.isShowPercent));
    }
);

onMounted(async () => {
    searchInfo.periodS = Number(periodStore.getPeriodRange().start);
    searchInfo.periodE = Number(periodStore.getPeriodRange().end);
    // paginationData.pageSize = Number(window.localStorage.getItem("AgeAnalysisSize") || 20);
    searchInfo.isShowPercent = JSON.parse(localStorage.getItem("AgeAnalysisShowPercent") || "true");
    document.body.scrollTop = 0;
    // 设置默认科目
    await setInitSubjectOptions();
    // 获取默认账龄
    getDefaultAgeSet();
    getAgeSet();
    // 搜索
    handleSearch();
});
</script>
<style lang="less" scoped>
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";

.content {
    width: 100%;
    .main-content {
        width: 100%;
        .main-center {
            display: flex;
            flex-direction: column;
            :deep(.el-table) {
                .el-popper.is-light {
                    max-width: 300px;
                    text-align: left;
                }
            }
            &.showDataMask {
                :deep(.el-table) {
                    .el-scrollbar__view {
                        min-height: 0;
                        height: 535px;
                        overflow: hidden;
                    }
                    // 底部滚动条样式
                    .el-scrollbar__bar {
                        &.is-horizontal {
                            height: 8px;
                            bottom: 1px;
                            background-color: #fff;
                            .el-scrollbar__thumb {
                                &:hover {
                                    filter: brightness(1.2);
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
            .select-info-inner {
                font-size: 12px;
            }
        }
        .subject-tips {
            color: #c5c5c5;
            text-align: left;
            padding-left: 120px;
            margin-top: 5px;
        }
    }
}

:deep(.erp-table.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
    background-color: #fff !important;
}
// 气泡框相关
:deep(.el-table) {
    .cell.el-tooltip {
        min-width: 0;
    }
}
</style>
