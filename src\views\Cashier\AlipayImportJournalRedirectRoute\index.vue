<template>
    <div></div>
</template>
<script lang="ts">
export default {
    name: "AlipayImportJournalRedirectRoute",
};
</script>
<script setup lang="ts">
import { getCookie } from "@/util/cookie";
import { setTopLocationhref } from "@/util/url";
import { AliPaySystem } from "../CashOrDepositJournal/utils";

const handleInit = () => {
    const fromType = getCookie("journalImportFromType");
    const appasid = getCookie("alipayImportAuthAppasid");
    let url = "/Cashier/AlipayImportJournal" + location.search + "&appasid=" + appasid;
    if (fromType === AliPaySystem.AgentFree) {
        const AACompanyId = getCookie("AACompanyId");
        url = window.jAAH5Host + url + "&AACompanyId=" + AACompanyId;
    } else if (fromType === AliPaySystem.AgentPro) {
        const AACompanyId = getCookie("AACompanyId");
        url = window.jAAProH5Host + url + "&AACompanyId=" + AACompanyId;
    } else if (fromType === AliPaySystem.Erp) {
        url = window.erpHost + "/#" + url;
    } else if (fromType === AliPaySystem.Pro) {
        url = window.jProH5Host + url;
    }
    setTopLocationhref(url, true);
};

handleInit();
</script>

<style scoped></style>
