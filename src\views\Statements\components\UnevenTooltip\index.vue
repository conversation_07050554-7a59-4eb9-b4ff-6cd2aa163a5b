<template>
    <el-tooltip 
        placement="bottom" 
        :offset="-4"
        effect="light" 
        :visible="visible" 
        :teleported="false" 
        class="customPover"
    >
        <template #content>
            <div class="popover_content" style="text-align: left;" v-if="!isHtmlContent">{{ content }}</div>
            <div class="popover_content" style="text-align: left;" v-else @mouseenter="checkOverflow" @mouseleave="handleMouseLeave">
                不平原因：损益类科目还有余额，请点击<span class="link" style="font-size: 12px;" @click="globalWindowOpenPage('/Checkout/Checkout', '期末结转')">期末结转</span>继续结转损益
            </div>
        </template>
        <div
            class="span_wrap"
            title=""
            ref="tooltipRef"
            :style="{ '-webkit-line-clamp': props.lineClamp }"
            @mouseenter="checkOverflow"
            @mouseleave="handleMouseLeave">
            <slot> </slot>
        </div>
    </el-tooltip>
</template>

<script setup lang="ts">
import { ref, type Ref } from "vue";
import { globalWindowOpenPage } from "@/util/url";

const props = withDefaults(
    defineProps<{
        content: string;
        maxWidth?: number;
        fontSize?: number;
        lineClamp?: number;
        dynamicWidth?: boolean;
        isHtmlContent?: boolean;
    }>(),
    {
        content: "",
        maxWidth: 300,
        fontSize: 14,
        lineClamp: 2,
        dynamicWidth: false,
        isHtmlContent: false,
    }
);
const visible = ref(false);
const tooltipRef: Ref<HTMLElement | null> = ref(null);
const checkOverflow = (event: Event) => {
    // 创建一个新的 span 元素
    let clone = document.createElement("span");
    // 将原来的 span 元素的文本复制到新的 span 元素
    clone.textContent = props.content;
    // 设置新的 span 元素的样式
    clone.style.display = "inline";
    clone.style.width = "auto";
    clone.style.visibility = "hidden";
    clone.style.whiteSpace = "nowrap"; // 确保文本不会换行
    clone.style.fontSize = props.fontSize + "px";

    // 将新的 span 元素添加到 DOM
    document.body.appendChild(clone);
    // 获取新的 span 元素的宽度
    let width = clone.getBoundingClientRect().width;

    const dom = event.target as HTMLElement;
    const domWidth = (dom?.getBoundingClientRect().width || props.maxWidth);
    const maxWidth = props.dynamicWidth ? domWidth : props.maxWidth;
    // 从 DOM 中移除新的 span 元素
    document.body.removeChild(clone);
    if (width > maxWidth) {
        visible.value = true;
    } else {
        visible.value = false;
    }
};
const handleMouseLeave = () => {
    visible.value = false;
};
</script>

<style lang="less">
.popover_content {
    max-width: 300px;
    white-space: normal;
    color: black;
    overflow-wrap: break-word;
    word-break: break-all;
}

.span_wrap {
    display: -webkit-box;
    max-width: 100%;
    //会遮挡输入框
    // overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    /* 显示省略号 */
    -webkit-box-orient: vertical;
}
.customPover {
    max-width: 300px;
    text-align: left;
}
</style>
