export const loginErrorList = {
  输入的统一社会信用代码或纳税人识别号错误: "请确认报税地区或统一社会信用代码是否填写正确",
  登记企业信息不存在: "请确认报税地区或统一社会信用代码是否填写正确",
  未查询到您与该企业的关联关系信息: "未查询到您与该企业的关联关系信息，请确认录入的统一社会信用代码或个人账号是否正确",
  未查询到您与该代理企业的关联关系信息: "未查询到您与该代理企业的关联关系信息，请确认录入的代理机构税号或个人账号是否正确",
  该用户未注册: "该用户未注册，请在自然人业务入口进行用户注册",
  未查询到对应手机号: "您的手机号码为空，请您前往税局“找回手机号码”功能修改成正确的手机号码或扫码登录税局添加默认手机号码",
  手机号码为空: "您的手机号码为空，请您前往税局“找回手机号码”功能修改成正确的手机号码或扫码登录税局添加默认手机号码",
  账号已被锁定: "连续认证错误次数过多，您的账号已被锁定。建议您前往电子税局使用“忘记密码”修改密码后重新登录或等待次日零时自动解锁。",
  已连续4次输入个人用户密码错误:
    "已连续4次输入个人用户密码错误，您可以通过电子税局的”忘记密码“设置新密码。如果再次输入错误，您的账户会被锁定！",
  已连续3次输入个人用户密码错误: "已连续3次输入个人用户密码错误，您可以通过电子税局“忘记密码”设置新密码",
  已连续4次输入密码错误: "已连续4次输入个人用户密码错误，您可以通过电子税局的”忘记密码“设置新密码。如果再次输入错误，您的账户会被锁定！",
  已连续3次输入密码错误: "已连续3次输入个人用户密码错误，您可以通过电子税局“忘记密码”设置新密码",
  输入的个人账号或密码错误: "输入的个人账号或密码错误，请重新输入！",
  手机号码不存在: "输入的个人账号或密码错误，请重新输入！",
  短信发送失败: "税局系统短信验证码发送失败，请稍等1分钟后重试",
  发送验证码失败: "税局系统短信验证码发送失败，请稍等1分钟后重试",
  短信发送次数超限: "税局系统检测您申请验证码次数已超限制",
  短信发送申请过于频繁: "税局系统检测您验证码申请过于频繁，请稍等10分钟后重试",
  使用短信验证码过于频繁: "税局系统检测您使用短信验证码过于频繁，请稍等10分钟后重试",
  未获取到验证码: "税局系统短信验证码发送失败，请稍等1分钟后重试",
  拼图: "税局系统短信验证码发送失败，请稍等1分钟后重试",
  该地区暂不支持: "因电子税局系统升级维护中，当前地区一键取票功能暂时无法使用，后续将会尽快恢复正常使用",
  当前账号下已有公司正在执行任务:
    "系统检测到您的税局登录账号所关联的其他企业税号正在取票中，为避免取票失败，请等其他企业税号取票结束后再发起一键取票操作",
  登录任务已存在:
    "系统检测到您的税局登录账号所关联的其他企业税号正在登录中，为保障您的账号安全，请勿重复登录。如非本人操作，请及时修改密码！",

  身份错误: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  跳转登录成功页失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  请求认证服务失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  网络异常: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  网络错误: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  税务局网页加载超时: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  税局繁忙校验失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  登录页加载失败: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  登录成功但未获取到cookie: "税局系统繁忙，登录失败，请稍等1分钟后重试",
  服务异常: "登录失败，请联系在线客服",
  服务器内部错误: "登录失败，请联系在线客服",
  服务不可用: "登录失败，请联系在线客服",
  服务器异常: "登录失败，请联系在线客服",
  任务超时: "登录失败，请联系在线客服",
  未扫码授权:
    "登录失败，请先前往税局选择自然人业务登录个人账号，进入“账户中心-企业授权管理-待确认授权”查看是否存在待确认授权企业，若存在则请确认后再进行一键取票操作",
  人脸识别服务协议:
    "登录失败，税局检测您的账号未签订《人脸识别服务协议》，请您前往税局官网登录同意《人脸识别服务协议》后再执行一键取票功能",
  您登录的账号下无当前取票企业的办税权限:
    "登录失败，您登录的账号下无当前取票企业的办税权限，请前往电子税局登录确认是否为该企业的涉税服务人员。",
  全国电子税务局下发验证码失败: "税局系统短信验证码发送失败，请稍等1分钟后重试",
}

export const taxBureauLoginList = [
  { value: 9, label: "企业新版登录" },
  { value: 101, label: "代理业务登录" },
]

export const getTaxBureauLoginList = (agentArea: number[], taxadId: number) => {
  let result = []
  if (taxadId && agentArea && agentArea.includes(taxadId)) {
    result = JSON.parse(JSON.stringify(taxBureauLoginList))
  } else {
    result = taxBureauLoginList.slice(0, 1)
  }
  return result
}
