import { request, type IResponseModel } from "@/util/service";
import { getCookie } from "@/util/cookie";
import { useEisInfoStoreHook } from "@/store/modules/eis";
//获取账套与在云发票中的关联信息
export const getEisRelationInfoApi = (asId: string | number) => {
  const accProductType = useEisInfoStoreHook().currentProductType;
  return request({
    url: window.fpApiHost + "/api/app/system-relation/acc-relation/" + asId + "?accProductType=" + accProductType,
    method: "get",
    headers: {
      authorization: `Bearer ${getCookie("ningmengcookie")}`,
    },
  });
};
export const getEisCompanyApi = () => {
  return request({
    url: window.fpApiHost + "/api/app/system-relation/company-list-for-acc",
    method: "get",
    headers: {
      authorization: `Bearer ${getCookie("ningmengcookie")}`,
    },
  });
};
//获取当前用户是否有云发票开票权限
export const checkInvoicePermissionApi = () => {
  const { eisRelationData } = useEisInfoStoreHook();
  return request({
    url: window.fpApiHost + "/api/app/company-user/has-invoice-issue-permission?eiscid=" + eisRelationData!.companyId,
    method: "get",
    headers: {
      authorization: `Bearer ${getCookie("ningmengcookie")}`,
    },
  });
};
//检查当前用户是否云发票企业系统管理员
export const checkInvoiceIsAdminApi = () => {
  const { eisRelationData } = useEisInfoStoreHook();
  return request({
    url: window.fpApiHost + "/api/app/company-user/is-administrator?eiscid=" + eisRelationData!.companyId,
    method: "get",
    headers: {
      authorization: `Bearer ${getCookie("ningmengcookie")}`,
    },
  });
};
//保存云发票关联系统
export const saveEisRelationApi = (data: any, companyId: string) => {
  return request({
    url: window.fpApiHost + "/api/app/system-relation/relation?eiscid=" + companyId,
    method: "post",
    data,
    headers: {
      authorization: `Bearer ${getCookie("ningmengcookie")}`,
    },
  });
};
//取消云发票关联系统
export const cancelEisRelationApi = (data: any) => {
  return request({
    url: window.fpApiHost + "/api/app/system-relation/delete-relation?eiscid=" + data.eiscid,
    method: "post",
    data,
    headers: {
      authorization: `Bearer ${getCookie("ningmengcookie")}`,
    },
  });
};
