<template>
    <el-dialog
        modal-class="modal-class"
        v-model="display"
        center
        width="822"
        title="关联凭证"
        @close="preResetDialogInfo"
        @closed="resetDialogInfo"
        class="dialogDrag"
    >
        <div class="about-voucehr-content" v-dialogDrag>
            <div class="about-voucehr-main">
                <div class="voucehr-main-top mb-10">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">
                                <el-select :teleported="false" v-model="selectorType">
                                    <el-option label="会计期间：" value="0" />
                                    <el-option label="凭证日期：" value="1" />
                                </el-select>
                            </div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="startMonth"
                                    v-model:endPid="endMonth"
                                    v-show="selectorType === '0'"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :disabledDateStart="disabledDateMonth"
                                    :disabledDateEnd="disabledDateMonth"
                                />
                                <DatePicker
                                    v-model:startPid="startDate"
                                    v-model:endPid="endDate"
                                    v-model:periodInfo="periodInfoDate"
                                    v-show="selectorType === '1'"
                                    :clearable="true"
                                    :disabled-date-start="disabledDate"
                                    :disabled-date-end="disabledDate"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证字：</div>
                            <div class="line-item-field small">
                                <Select 
                                    :teleported="false" 
                                    v-model="searchInfo.vgId"
                                    :filterable="true"
                                    :filter-method="voucherGroupFilterMethod"
                                >
                                    <ElOption 
                                        v-for="item in showVoucherGroupList" 
                                        :key="item.id" 
                                        :value="item.id + ''" 
                                        :label="item.title" 
                                    />
                                </Select>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">制单人：</div>
                            <div class="line-item-field small">
                                <Select 
                                    :teleported="false" 
                                    v-model="searchInfo.prepareBy"
                                    :filterable="true"
                                    :filter-method="userNameFilterMethod"
                                    :remote="true"
                                    :remoteShowSuffix="true"
                                >
                                    <ElOption 
                                        v-for="(item, index) in showUserNameList" 
                                        :key="index" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </Select>
                            </div>
                        </div>
                        <div class="line-item input" v-show="checkNeeded">
                            <div class="line-item-title">是否审核：</div>
                            <div class="line-item-field small">
                                <el-select :teleported="false" v-model="searchInfo.approvalStatus">
                                    <el-option label="全部" value="-1" />
                                    <el-option label="已审核" value="2" />
                                    <el-option label="未审核" value="1" />
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">摘要：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.description" style="width: 298px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">备注：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.note" style="width: 298px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目：</div>
                            <div class="line-item-field subject">
                                <SubjectPicker
                                    v-model="searchInfo.asubCode"
                                    :need-asub-name="false"
                                    :show-disabled="true"
                                    asubImgRight="16px"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">金额：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.startAmount" style="width: 132px" clearable />
                                <span class="mr-10 ml-10">至</span>
                                <el-input v-model="searchInfo.endAmount" style="width: 132px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证号：</div>
                            <div class="line-item-field">
                                <el-input
                                    v-model="searchInfo.startVNum"
                                    style="width: 132px"
                                    clearable
                                    @keydown="handleKeyDown"
                                    @input="vNumSInput"
                                />
                                <span class="mr-10 ml-10">至</span>
                                <el-input
                                    v-model="searchInfo.endVNum"
                                    style="width: 132px"
                                    clearable
                                    @keydown="handleKeyDown"
                                    @input="vNumEInput"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">排序依据：</div>
                            <div class="line-item-field">
                                <el-radio-group v-model="searchInfo.sortColumn">
                                    <el-radio label="0">凭证号排序</el-radio>
                                    <el-radio label="1">凭证日期排序</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">排序方式：</div>
                            <div class="line-item-field">
                                <el-radio-group v-model="searchInfo.sortType">
                                    <el-radio label="0">升序</el-radio>
                                    <el-radio label="1">降序</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="buttons left">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                </div>
                <div class="voucehr-main-center">
                    <div class="table-title">
                        <div class="title-cell-item date">凭证日期</div>
                        <div class="title-cell-item num">凭证字号</div>
                        <div class="title-cell-item desc">摘要</div>
                        <div class="title-cell-item asub">科目</div>
                        <div class="title-cell-item debit">借方金额</div>
                        <div class="title-cell-item credit">贷方金额</div>
                        <div class="title-cell-item create">制单人</div>
                        <div class="title-cell-item check">审核人</div>
                        <div class="title-cell-item operate">操作</div>
                    </div>
                    <el-scrollbar height="225px" ref="scrollBarRef" :always="true" @scroll="handleScroll" v-loading="loading">
                        <template v-for="(item, index) in tableData" :key="index">
                            <div
                                v-if="!useVirtualScroll || (index > startIndex - pageSize && index < startIndex + pageSize * 2)"
                                class="table-item"
                                :class="{ suggest: item.suggest }"
                                :style="{ height: item.height + 'px' }"
                            >
                                <div class="table-cell-item pl-5 date">
                                    <Tooltip
                                        :fontSize="12"
                                        :content="formatDate(item.vdate)"
                                        :dynamicWidth="true"
                                        :lineClamp="1"
                                        placement="right-start"
                                    >
                                        <div class="text-overflow">
                                            {{ formatDate(item.vdate) }}
                                        </div>
                                    </Tooltip>
                                </div>
                                <div class="table-cell-item pl-5 num">
                                    <Tooltip
                                        :fontSize="12"
                                        :content="item.vgName + '-' + item.vnum"
                                        :dynamicWidth="true"
                                        :lineClamp="1"
                                        placement="right-start"
                                    >
                                        <div class="text-overflow">
                                            {{ item.vgName + "-" + item.vnum }}
                                        </div>
                                    </Tooltip>
                                </div>
                                <div class="table-cell-item desc" :class="{ expend: item.expend }">
                                    <div
                                        class="pl-5"
                                        v-show="item.expend || lineIndex < 2"
                                        v-for="(voucherLine, lineIndex) in item.voucherLines"
                                        :key="lineIndex"
                                    >
                                        <Tooltip
                                            :fontSize="12"
                                            :content="voucherLine.description"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ voucherLine.description }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="item.expend" class="pl-5">
                                        <Tooltip
                                            :fontSize="12"
                                            :content="'合计：' + digitUppercase(calcTotalMoney(item.voucherLines, 'debit'))"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ "合计：" + digitUppercase(calcTotalMoney(item.voucherLines, "debit")) }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                </div>
                                <div class="table-cell-item asub" :class="{ expend: item.expend }">
                                    <div
                                        class="pl-5"
                                        v-show="item.expend || lineIndex < 2"
                                        v-for="(voucherLine, lineIndex) in item.voucherLines"
                                        :key="lineIndex"
                                    >
                                        <Tooltip
                                            :fontSize="12"
                                            :content="voucherLine.asubName"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ voucherLine.asubName }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="item.expend" class="pl-5"></div>
                                </div>
                                <div class="table-cell-item debit" :class="{ expend: item.expend }">
                                    <div
                                        class="pl-5"
                                        v-show="item.expend || lineIndex < 2"
                                        v-for="(voucherLine, lineIndex) in item.voucherLines"
                                        :key="lineIndex"
                                    >
                                        <Tooltip
                                            :fontSize="12"
                                            :content="formatMoney(voucherLine.debit)"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ formatMoney(voucherLine.debit) }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="item.expend" class="pl-5">
                                        <Tooltip
                                            :fontSize="12"
                                            :content="formatMoney(calcTotalMoney(item.voucherLines, 'debit'))"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ formatMoney(calcTotalMoney(item.voucherLines, "debit")) }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                </div>
                                <div class="table-cell-item credit" :class="{ expend: item.expend }">
                                    <div
                                        class="pl-5"
                                        v-show="item.expend || lineIndex < 2"
                                        v-for="(voucherLine, lineIndex) in item.voucherLines"
                                        :key="lineIndex"
                                    >
                                        <Tooltip
                                            :fontSize="12"
                                            :content="formatMoney(voucherLine.credit)"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ formatMoney(voucherLine.credit) }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="item.expend" class="pl-5">
                                        <Tooltip
                                            :fontSize="12"
                                            :content="formatMoney(calcTotalMoney(item.voucherLines, 'credit'))"
                                            :dynamicWidth="true"
                                            :lineClamp="1"
                                            placement="right-start"
                                        >
                                            <div class="text-overflow">
                                                {{ formatMoney(calcTotalMoney(item.voucherLines, "credit")) }}
                                            </div>
                                        </Tooltip>
                                    </div>
                                </div>
                                <div class="table-cell-item pl-5 create">
                                    <Tooltip
                                        :fontSize="12"
                                        :content="item.preparedBy"
                                        :dynamicWidth="true"
                                        :lineClamp="1"
                                        placement="right-start"
                                    >
                                        <div class="text-overflow">
                                            {{ item.preparedBy }}
                                        </div>
                                    </Tooltip>
                                </div>
                                <div class="table-cell-item pl-5 check">
                                    <Tooltip
                                        :fontSize="12"
                                        :content="item.approvedBy"
                                        :dynamicWidth="true"
                                        :lineClamp="1"
                                        placement="right-start"
                                    >
                                        <div class="text-overflow">
                                            {{ item.approvedBy }}
                                        </div>
                                    </Tooltip>
                                </div>
                                <div class="table-cell-item pl-5 operate">
                                    <span class="link" @click="handleExpend(item)">{{ item.expend ? "收起" : "展开" }}</span>
                                    <span class="link ml-10" @click="handleRelateVoucher(item)">关联</span>
                                </div>
                            </div>
                            <div v-else class="table-item virtual" :style="{ height: item.height + 'px' }"></div>
                        </template>
                    </el-scrollbar>
                    <div class="table-tip mt-10">单据关联凭证后，单据附件将自动添加到凭证附件中</div>
                </div>
            </div>
            <div class="buttons bt">
                <a class="button" @click="display = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, toRef, reactive, computed, onBeforeMount, onActivated, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney, digitUppercase } from "@/util/format";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { dayjs } from "element-plus";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { handleKeyDown, useNumberInput } from "@/util/format";
import { PeriodStatus } from "@/api/period";

import type { IVoucherModel, IVoucherLine } from "@/views/Voucher/VoucherList/types";
import type { IParameters } from "../types";
import type { IPeriod } from "@/api/period";
import type { ElScrollbar } from "element-plus";

import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import ElOption from "@/components/Option/index.vue";
import Select from "@/components/Select/index.vue";
import { initStartOrEndMonth, getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

interface IVoucherItem extends IVoucherModel {
    expend: boolean;
    suggest: boolean;
    height: number;
}

const props = defineProps<{
    jType: "1010" | "1020";
    checkoutDate: Date;
    cdAccount: string;
}>();
const emit = defineEmits<{
    (event: "search"): void;
}>();

const periodList = toRef(useAccountPeriodStore(), "periodList");
const _periodList = computed(() => {
    const list: Array<any> = [];
    for (let i = periodList.value.length - 1; i >= 0; i--) {
        if (periodList.value[i].status !== PeriodStatus.CheckOut) {
            list.push({ ...periodList.value[i], time: periodList.value[i].year + "" + String(periodList.value[i].sn).padStart(2, "0") });
        } else {
            break;
        }
    }
    return list;
});
const userNameList = ref<Array<any>>([]);
const voucherGroupList = toRef(useVoucherGroupStore(), "voucherGroupList");
const checkNeeded = computed(() => useAccountSetStore().accountSet!.checkNeeded === 1);
const selectorType = ref<"0" | "1">("0");
const startPid = ref(0);
const endPid = ref(0);
const startMonth = ref("");
const endMonth = ref("");
const startDate = ref("");
const endDate = ref("");
const periodInfoDate = ref("");
const currentPeriodInfo = ref("");
// const periodInfo = computed(() => {
//     if (startPid.value === 0 || startPid.value === 0) {
//         return "----年--月";
//     }
//     if (startPid.value === endPid.value) {
//         return getPeriodInfoByPID(startPid.value);
//     } else {
//         return getPeriodInfoByPID(startPid.value) + "—" + getPeriodInfoByPID(endPid.value);
//     }
// });
const searchInfo = reactive({
    vgId: "",
    prepareBy: "",
    approvalStatus: "-1",
    description: "",
    note: "",
    asubCode: "",
    startAmount: "",
    endAmount: "",
    startVNum: "",
    endVNum: "",
    sortColumn: "0",
    sortType: "0",
});

const vNumSInput = useNumberInput(searchInfo, "reactive", "startVNum");
const vNumEInput = useNumberInput(searchInfo, "reactive", "endVNum");

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const loading = ref(false);
function checkCanSearch() {
    startPid.value = _periodList.value.find((item) => item.time === startMonth.value)?.pid || 0;
    endPid.value = _periodList.value.find((item) => item.time === endMonth.value)?.pid || 0;
    if (selectorType.value === "0") {
        if (startPid.value === 0 || endPid.value === 0) {
            ElNotify({ type: "warning", message: "亲，开始期间和结束期间不能为空哦" });
            return false;
        }
        if (startPid.value > endPid.value) {
            ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哦" });
            return false;
        }
    } else {
        if (dayjs(startDate.value).valueOf() > dayjs(endDate.value).valueOf()) {
            ElNotify({ type: "warning", message: "亲，开始日期不能晚于结束日期哦" });
            return false;
        }
        if (!startDate.value || !endDate.value) {
            ElNotify({ type: "warning", message: "亲，起止日期不能为空！" });
            return false;
        }
    }
    return true;
}
async function handleSearch() {
    if (!checkCanSearch()) return;
    handleClose();
    setCurrentPeriodInfo();
    await handleGetVoucherData([]);
}
async function handleGetVoucherData(arrayLineSn: Array<any>) {
    const queryParams = {
        ...searchInfo,
        onlyTempVoucher: false,
        searchInfo: "",
        selectorType: selectorType.value,
        startPId: startPid.value,
        endPId: endPid.value,
        startDate: startDate.value,
        endDate: endDate.value,
    };
    const params = { j_type: props.jType, arrayLineSn };
    loading.value = true;
    await request({ url: "/api/JournalVoucher/GetVoucherList?" + getUrlSearchParams(queryParams), method: "post", data: params })
        .then((res: IResponseModel<{ count: number; data: Array<IVoucherModel>; isRecommend: boolean }>) => {
            loading.value = false;
            if (res.state !== 1000) return;
            useVirtualScroll.value = res.data.count > minVirtualLength;
            tableData.value = res.data.data.map((item: IVoucherModel, index: number) => {
                const voucher = { ...item, expend: false, suggest: false, height: calcTableItemHeight(item.voucherLines.length, false) };
                res.data.isRecommend && index === 0 && (voucher.suggest = true);
                return voucher;
            });
        })
        .catch(() => {
            loading.value = false;
        });
}
function handleExpend(item: IVoucherItem) {
    item.expend = !item.expend;
    item.height = calcTableItemHeight(item.voucherLines.length, item.expend);
}
function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.vgId = "";
    searchInfo.prepareBy = "";
    searchInfo.approvalStatus = "-1";
    searchInfo.description = "";
    searchInfo.note = "";
    searchInfo.asubCode = "";
    searchInfo.startAmount = "";
    searchInfo.endAmount = "";
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
    searchInfo.sortColumn = "0";
    searchInfo.sortType = "0";
}
let isSaving = false;
async function handleRelateVoucher(item: IVoucherItem) {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.relateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) return;
    const params = {
        pid: item.pid,
        vid: item.vid,
        arrayLineSn: journalParams!.lineSnList,
        isNeedSaveToVoucher: true,
    };
    isSaving = true;
    const skip = journalParams!.skip || 0;
    if (params.arrayLineSn.length === 0) {
        ElNotify({
            type: "success",
            message: `成功：0，跳过：${skip}(已结账、已生成凭证、内部转账的数据已跳过)`,
        });
        display.value = false;
        isSaving = false;
        return;
    }
    const path = props.jType === "1010" ? "/api/Journal/CashBindVoucherBatch" : "/api/Journal/DepositBindVoucherBatch";
    request({ url: path, method: "post", data: params })
        .then((res: IResponseModel<string>) => {
            isSaving = false;
            if (res.state === 1000 && res.data === "Success") {
                const success = params.arrayLineSn.length;
                ElNotify({
                    type: "success",
                    message: `成功：${success}，跳过：${skip}(已结账、已生成凭证、内部转账的数据已跳过)`,
                });
                display.value = false;
                window.dispatchEvent(new CustomEvent("reloadVoucherList"));
                emit("search");
            } else {
                ElNotify({ type: "warning", message: "关联凭证失败，请刷新页面后重试" });
            }
        })
        .catch(() => {
            isSaving = false;
            ElNotify({ type: "warning", message: "关联凭证失败，请刷新页面后重试" });
        });
}

// function getPeriodInfoByPID(pid: number) {
//     for (let i = 0; i < periodList.value.length; i++) {
//         const item = periodList.value[i];
//         if (item.pid === pid) {
//             return getPeriodInfo(item);
//         }
//     }
//     return "";
// }
// function getPeriodInfo(period: IPeriod) {
//     return period.year + "年" + period.sn + "月";
// }
function setCurrentPeriodInfo() {
    currentPeriodInfo.value = selectorType.value === "0" ? getCurrentPeriodInfo(_periodList.value, startPid.value, endPid.value) : periodInfoDate.value;
}
function formatDate(date: string) {
    const dateArr = date.split(" ")[0].split("/");
    return dateArr.join("-");
}
function disabledDate(time: Date) {
    const now = new Date();
    const accountStartDate = dayjs(props.checkoutDate).valueOf();
    now.setFullYear(now.getFullYear() + 5);
    return time.getTime() < accountStartDate || time.getTime() > dayjs(now).valueOf();
}
function disabledDateMonth(time: Date) {
    const start = _periodList.value[_periodList.value.length - 1]?.time ?? new Date();
    const end = _periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
function calcTotalMoney(voucherLines: Array<IVoucherLine>, type: "debit" | "credit") {
    const result: number = voucherLines.reduce((prev, cur) => {
        prev += cur[type] - 0;
        return prev;
    }, 0);
    return result;
}
const defaultLineHeight = 37;
function calcTableItemHeight(length: number, expend: boolean) {
    if (!expend) return defaultLineHeight * 2;
    return defaultLineHeight * (length + 1);
}
const scrollBarRef = ref<InstanceType<typeof ElScrollbar>>();
function resetDialogInfo() {
    tableData.value.length = 0;
    useVirtualScroll.value = false;
    journalParams = null;
    handleReset();
    cacheScrollTop = 0;
}
function preResetDialogInfo() {
    scrollBarRef.value?.setScrollTop(0);
}
async function handleGetUserNameList() {
    userNameList.value = [{ value: "", label: "全部" }];
    await request({ url: "/api/Voucher/GetVoucherPreparedBys", method: "post" }).then((res: IResponseModel<Array<string>>) => {
        if (res.state !== 1000) return;
        let list = res.data.map((item) => {
            return {
                value: item,
                label: item,
            }
        });
        userNameList.value = [...userNameList.value, ...list];
    });
}
onBeforeMount(() => {
    handleGetUserNameList();
});

let journalParams: IParameters | null = null;
const display = ref(false);
const tableData = ref<Array<IVoucherItem>>([]);
const useVirtualScroll = ref(false);
const minVirtualLength = 100;
const startIndex = ref(0);
const pageSize = computed(() => 10);
let cacheScrollTop = 0;
function handleScroll(event: { scrollLeft: number; scrollTop: number }) {
    const { scrollTop } = event;
    cacheScrollTop = scrollTop;
    let height = 0;
    for (let i = 0; i < tableData.value.length; i++) {
        const currentHeight = tableData.value[i].height + 1;
        if (height <= scrollTop && height + currentHeight > scrollTop) {
            startIndex.value = i;
            break;
        }
        height += currentHeight;
    }
}
onActivated(() => {
    cacheScrollTop && scrollBarRef.value?.setScrollTop(cacheScrollTop);
});

let doing = false;
async function handleOpenDialog(parameters: IParameters, dateInfo: { _s: string; _e: string }) {
    if (doing) return;
    doing = true;
    const _s = dateInfo._s || _periodList.value[0].year + _periodList.value[0].sn.toString().padStart(2, "0") + "01";
    const _e = dateInfo._e || _periodList.value[0].year + _periodList.value[0].sn.toString().padStart(2, "0") + "01";
    const s = dayjs(_s).format("YYYY-MM-DD");
    const e = dayjs(_e).format("YYYY-MM-DD");
    if (s > e) {
        doing = false;
        ElNotify({ type: "warning", message: "开始日期不能大于结束日期" });
        return;
    }
    startDate.value = s;
    endDate.value = e;
    const startPeriod = periodList.value.find((item) => item.year + item.sn.toString().padStart(2, "0") === dayjs(_s).format("YYYYMM"));
    const endPeriod = periodList.value.find((item) => item.year + item.sn.toString().padStart(2, "0") === dayjs(_e).format("YYYYMM"));
    const activePid = periodList.value.find((item) => item.isActive)!.pid;
    if (!startPeriod) {
        startPid.value = activePid;
        endPid.value = activePid;
    } else {
        startPid.value = startPeriod.pid;
        endPid.value = endPeriod ? endPeriod.pid : activePid;
    }
    let result = initStartOrEndMonth(_periodList.value, Number(startPid.value), Number(endPid.value));
    startMonth.value = result.startMonth;
    endMonth.value = result.endMonth;
    journalParams = parameters;
    await handleGetVoucherData(journalParams!.lineSnList);
    setCurrentPeriodInfo();
    doing = false;
    display.value = true;
}
defineExpose({ handleOpenDialog });

const voucherGroupListAll = ref<Array<any>>([]);
const showVoucherGroupList = ref<Array<any>>([]);
const showUserNameList = ref<Array<any>>([]);
watchEffect(() => {
    voucherGroupListAll.value = JSON.parse(JSON.stringify(voucherGroupList.value));
    voucherGroupListAll.value.unshift({ id: "", title: "全部" });
    showVoucherGroupList.value = JSON.parse(JSON.stringify(voucherGroupListAll.value));
    showUserNameList.value = JSON.parse(JSON.stringify(userNameList.value));
});
function voucherGroupFilterMethod(value: string) {
    showVoucherGroupList.value = commonFilterMethod(value, voucherGroupListAll.value, 'title');
}
function userNameFilterMethod(value: string) {
    showUserNameList.value = commonFilterMethod(value, userNameList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Cashier/JournalAboutVoucher.less";
</style>
