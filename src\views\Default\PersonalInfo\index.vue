<template>
    <div class="content">
        <div class="title">个人设置</div>
        <el-tabs v-model="activeName" @tab-click="handleTabClick">
            <el-tab-pane label="个人资料" name="main"></el-tab-pane>
            <el-tab-pane label="修改密码" name="password"></el-tab-pane>
            <el-tab-pane label="修改手机" name="phone"></el-tab-pane>
            <el-tab-pane label="微信绑定" name="wechat" v-if="!isHideBarcode"></el-tab-pane>
        </el-tabs>
        <div class="main-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #main>
                    <div class="slot-content">
                        <el-form :model="mainInfo" label-width="399px">
                            <el-form-item label="邮箱：">
                                <el-input v-model="mainInfo.mail" style="width: 230px"></el-input>
                                <span class="ml-10 newemail-tip">亲，邮箱格式不正确哦，请修正！</span>
                            </el-form-item>
                            <el-form-item label="姓名：">
                                <el-input v-model="mainInfo.name" style="width: 230px" @blur="handleBlur('newusername', $event)"></el-input>
                                <span class="ml-10 newusername-tip">亲，用户姓名不能为空哦！</span>
                            </el-form-item>
                        </el-form>
                        <div class="slot-content-footer">
                            <div class="footer-tip">* 当您录凭证或者审核凭证时，此姓名即为您的制单人姓名、审核人姓名。</div>
                            <div class="buttons">
                                <a class="button solid-button" @click="saveMainInfo">确定</a>
                            </div>
                        </div>
                    </div>
                </template>
                <template #password>
                    <div class="slot-content">
                        <el-form :model="passwordInfo" label-width="399px">
                            <el-form-item label="原密码：">
                                <el-input
                                    v-model="passwordInfo.oldPassword"
                                    style="width: 230px"
                                    @blur="handleBlur('oldpwd', $event)"
                                    type="password"
                                ></el-input>
                                <span class="ml-10 oldpwd-tip">亲，原密码不能为空哦！</span>
                                <span class="forgetpwd-tip ml-10" @click="handleForgetpwd" v-show="!isThirdPart">忘记密码？</span>
                            </el-form-item>
                            <el-form-item label="新密码：">
                                <el-input
                                    v-model="passwordInfo.newPassword"
                                    style="width: 230px"
                                    @blur="handleBlur('newpwd', $event)"
                                    type="password"
                                ></el-input>
                                <span class="ml-10 newpwd-tip">亲，新密码不能为空哦！</span>
                            </el-form-item>
                            <el-form-item label="重复密码：">
                                <el-input
                                    v-model="passwordInfo.repeatPassword"
                                    style="width: 230px"
                                    @blur="handleBlur('repeatpwd', $event)"
                                    type="password"
                                ></el-input>
                                <span class="ml-10 repeatpwd-tip">亲，重复密码与新密码不同哦！</span>
                            </el-form-item>
                        </el-form>
                        <div class="slot-content-footer">
                            <div class="buttons">
                                <a class="button solid-button" @click="saveNewPassword">确定</a>
                            </div>
                        </div>
                    </div></template
                >
                <template #phone>
                    <div class="slot-content">
                        <el-form :model="phoneInfo" label-width="399px">
                            <el-form-item label="新手机：">
                                <el-input
                                    v-model="phoneInfo.newphone"
                                    style="width: 114px"
                                    @blur="handleBlur('newphone', $event)"
                                ></el-input>
                                <a class="solid-button ml-10 code-button" @click="validateForRegister">{{ sendCodeAText }}</a>
                                <span class="ml-10 newphone-tip">亲，用户手机不能为空哦！</span>
                            </el-form-item>
                            <el-form-item label="验证码：">
                                <el-input v-model="phoneInfo.code" style="width: 230px"></el-input>
                            </el-form-item>
                        </el-form>
                        <div class="slot-content-footer">
                            <div class="buttons">
                                <a class="button solid-button" @click="saveRegisterPhone">确定</a>
                            </div>
                            <div class="phonefooter-tip">
                                <div class="phonefooter-title">提示：</div>
                                <div class="phonefooter-txt">
                                    <!-- <div>
                                        1、如果您只需要移交账套数据，建议您使用账套移交功能<span
                                            class="link ml-10"
                                            @click="redirectToPermission"
                                            >点此移交</span
                                        >
                                    </div> -->
                                    <div>
                                        修改手机号后，您将使用新手机号登录使用柠檬云系统，原手机号将无法查看所有柠檬云系统的历史数据（包括已购买的课程），请谨慎操作！
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div></template
                >
                <template #wechat>
                    <div class="slot-content">
                        <div class="wechat">
                            <div class="wechat-content">
                                <div class="wechat-line">绑定微信账号，可以验证身份，使用更加安全！</div>
                                <div class="wechat-line wechat-bind">
                                    <span>当前状态：</span>
                                    <span class="barcode-state" :class="wechatBindShow ? 'binded' : ''">{{
                                        wechatBindShow ? "已绑定" : "未绑定"
                                    }}</span>
                                    <span></span>
                                </div>
                            </div>
                            <div class="slot-content-footer">
                                <div class="unbind" :style="{ display: wechatBindShow ? 'none' : 'block' }">
                                    <div class="barcode-img" v-loading="codeLoading">
                                        <img class="qrcodeimg" :src="qrCodeBindImg" />
                                        <div class="expired-tip" :style="{ display: codeBindExpired ? '' : 'none' }">
                                            <span class="expired-tip-content">二维码过期</span>
                                            <a class="expired-tip-refresh">点击刷新</a>
                                        </div>
                                    </div>
                                    <div class="barcode-bottom">
                                        <div>微信扫一扫</div>
                                        <div>登录更快更安全</div>
                                    </div>
                                </div>
                                <div class="binded" :style="{ display: wechatBindShow ? 'block' : 'none' }">
                                    <div class="barcode-img">
                                        <!-- <img class="qrcodeimg" src="@/assets/Default/header-img.png" /> -->
                                        <img class="qrcodeimg" :src="headImgUrl" />
                                    </div>
                                    <div class="buttons">
                                        <a class="button ml-10" @click="changeWechatBind">修改绑定</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </ContentSlider>
        </div>
    </div>
    <el-dialog v-model="testPhoneShow" title="验证用户手机" width="440" center @close="handleClose" class="dialogDrag">
        <div class="test-content" v-dialogDrag>
            <div class="test-main">
                <div class="test-line">
                    <div class="line-title">手机：</div>
                    <div class="line-content">
                        <span>{{ mainInfo.mobile }}</span>
                        <span class="forgetpwd-tip ml-10" @click="handleForgetpwd" v-show="!isThirdPart">忘记密码？</span>
                    </div>
                </div>
                <div class="test-line">
                    <div class="line-title">密码：</div>
                    <div class="line-content">
                        <el-input v-model="testUserPwd" style="width: 180px" placeholder="请输入密码" class="masked-input"></el-input>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="verifyPassword">验证</a>
                <a class="button ml-10" @click="testPhoneShow = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="checkNameDialog" title="修改姓名" width="440" center class="dialogDrag">
        <div class="name-check-content" v-dialogDrag>
            <div class="name-check-main">
                <div class="checkmain-content">您修改了姓名，请确认是从您的下一张凭证开始生效，还是您的全部凭证同步生效？</div>
                <div class="checkmain-tip">提示：您的制单人姓名、审核人姓名将同时生效。</div>
            </div>
            <div class="buttons">
                <a class="button solid-button large-2" @click="changeMainInfo('next')">下一张凭证生效</a>
                <a class="button ml-10 solid-button large-2" @click="changeMainInfo('all')">全部凭证生效</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="wechatConfirmShow" title="微信验证" width="440" center @close="cancelWechatConfirm" class="dialogDrag">
        <div class="wechatDialog-content" v-dialogDrag>
            <span>您的账号已绑定微信，修改绑定需要进行微信验证</span>
            <div class="wechatverify-img" v-loading="codeLoading">
                <img class="verifyimg" :src="qrCodeConfirmImg" />
                <div class="verify-expired-tip" :style="{ display: confirmCodeExpired ? '' : 'none' }">
                    <span class="verify-tip-content">二维码过期</span>
                    <a class="verify-tip-refresh" @click="refreshConfirmCode">请点击刷新</a>
                </div>
            </div>
            <span>请用已绑定微信扫描以上二维码进行验证</span>
            <span>若此微信号已无法使用，请点击<span class="forgetpwd-tip" @click="confirmWechatByPhone">短信验证</span></span>
        </div>
    </el-dialog>
    <el-dialog v-model="wechatChangeShow" title="微信验证" width="440" center @close="cancelWechatConfirm" class="dialogDrag">
        <div class="wechatDialog-content" v-dialogDrag>
            <span>请用新的微信扫描以下二维码进行微信验证</span>
            <div class="wechatverify-img" v-loading="codeLoading">
                <img class="verifyimg" :src="qrCodeChangeImg" />
                <div class="verify-expired-tip" :style="{ display: changeCodeExpired ? '' : 'none' }">
                    <span class="verify-tip-content">二维码过期</span>
                    <a class="verify-tip-refresh" @click="refreshChangeCode">请点击刷新</a>
                </div>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="wechatSMSConfirmShow" title="手机验证" width="440" center @close="cancelWechatConfirm" class="dialogDrag">
        <div class="smsDialog-content" v-dialogDrag>
            <el-form label-width="164px">
                <el-form-item label="手机：">
                    <span>{{ mainInfo.mobile }}</span>
                </el-form-item>
                <el-form-item label="验证码：">
                    <el-input v-model="confirmSMSCode" style="width: 110px" placeholder="请输入验证码"></el-input>
                    <a class="solid-button ml-10 code-button" @click="sendWeChatConfirmSMS">{{ wechatConfirmSMSAText }}</a>
                </el-form-item>
            </el-form>
            <div class="buttons">
                <a class="button solid-button" @click="validateWeChatConfirm">确定</a>
                <a class="button ml-10" @click="wechatSMSConfirmShow = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "PersonalInfo",
};
</script>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import { useUserStore } from "@/store/modules/user";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { globalWindowOpen, globalWindowOpenPage, getUrlSearchParams, loadTopUserName } from "@/util/url";
import { AppConfirmDialog } from "@/util/appConfirm";
import { isLemonClient } from "@/util/lmClient";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

const activeName = ref("main");
const slots = ["main", "password", "phone", "wechat"];
const currentSlot = ref("main");
const uerStore = useUserStore(); //用来右上角名称
const isThirdPart = useThirdPartInfoStoreHook().isThirdPart;
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);

//点击tab
const handleTabClick = (tab: any) => {
    if (tab.paneName === "phone") {
        testPhoneShow.value = true;
    } else if (tab.paneName === "wechat" && !wechatBindShow.value) {
        currentSlot.value = "wechat";
        getQrCodeBind();
    } else if (tab.paneName === "password") {
        AppConfirmDialog(1030).then(() => {
            currentSlot.value = "password";
        });
    } else {
        currentSlot.value = tab.paneName;
    }
};

//修改保存个人资料
const EMPTY_MAIL_MESSAGE = "亲，邮箱格式不能为空哦！";
const ERROR_MAIL_MESSAGE = "亲，邮箱格式不正确哦，请修正！";
const EMPTY_NAME_MESSAGE = "亲，用户姓名不能为空哦！";
const ERROR_NAME_MESSAGE = "亲，用户姓名不能含有单引号哦！";
const oldUserInfo = reactive({
    oldMail: "",
    oldName: "",
});
const mainInfo = reactive({
    mail: "",
    name: "",
    mobile: "",
});
const checkNameDialog = ref(false);
const saveMainInfo = () => {
    if (!checkMainInfo()) return;
    if (oldUserInfo.oldName !== mainInfo.name) {
        checkNameDialog.value = true;
    } else {
        changeMainInfo();
    }
};
const changeMainInfo = (type?: string) => {
    checkNameDialog.value = false;
    const isChangeId = oldUserInfo.oldMail === mainInfo.mail ? 0 : 1;
    const PreparedType = type === "all" ? 1 : 0;
    const params = {
        CurrentSystemType: 1,
        UserName: mainInfo.name,
        UserId: mainInfo.mail,
        EditType: "Info",
        ChangeID: isChangeId,
        PreparedType,
    };
    request({
        url: window.accountSrvHost + "/Default/Services/SubmitForPersonalInfo.ashx?" + getUrlSearchParams(params),
        method: "get",
    })
        .then((res: any) => {
            if (res === "Success") {
                if (params.PreparedType === 1) {
                    updateVoucherName(params.UserName);
                } else {
                    loadTopUserName();
                    getUserInfo();
                    ElNotify({
                        message: "亲，保存成功啦！",
                        type: "success",
                    });
                }
            } else {
                ElNotify({
                    message: res,
                    type: "warning",
                });
            }
        })
        .catch((err: any) => {
            console.log(err);
        });
};
const updateVoucherName = (name: string) => {
    request({
        url: `/api/PersonalInfo/UpdateVoucherPreparedBy?userName=${name}`,
        method: "post",
    })
        .then((res: any) => {
            if (res.data === true) {
                uerStore.getUserName();
                getUserInfo();
                ElNotify({
                    message: "亲，保存成功啦！",
                    type: "success",
                });
            } else {
                ElNotify({
                    message: res.msg,
                    type: "success",
                });
            }
        })
        .catch((err: any) => {
            console.log(err);
        });
};

const checkMainInfo = () => {
    let flag = true;
    const regex = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    const mailTip = document.querySelector(`.newemail-tip`) as unknown as HTMLElement;
    const nameTip = document.querySelector(`.newusername-tip`) as unknown as HTMLElement;
    if (mainInfo.mail === "") {
        mailTip.innerText = EMPTY_MAIL_MESSAGE;
        mailTip.style.display = "block";
        flag = false;
    } else if (!regex.test(mainInfo.mail)) {
        mailTip.innerText = ERROR_MAIL_MESSAGE;
        mailTip.style.display = "block";
        flag = false;
    } else {
        mailTip.style.display = "none";
    }
    if (mainInfo.name === "") {
        nameTip.innerText = EMPTY_NAME_MESSAGE;
        nameTip.style.display = "block";
        flag = false;
    } else if (mainInfo.name.indexOf("'") >= 0) {
        nameTip.innerText = ERROR_NAME_MESSAGE;
        nameTip.style.display = "block";
        flag = false;
    } else {
        nameTip.style.display = "none";
    }
    return flag;
};

//修改密码
const passwordInfo = reactive({
    oldPassword: "",
    newPassword: "",
    repeatPassword: "",
});
const EMPTY_PASSWORD_MESSAGE = "亲，新密码不能为空哦！";
const SHORT_PASSWORD_MESSAGE = "亲，新密码长度不能小于6哦！";
const SAME_PASSWORD_MESSAGE = "亲，新密码不能与原密码一致哦！";
const handleForgetpwd = () => {
    globalWindowOpen("https://www.ningmengyun.com/default.aspx?rp=fpd");
};
const saveNewPassword = () => {
    if (checkPassword()) {
        const params = {
            CurrentSystemType: 1,
            CurrentPassword: passwordInfo.oldPassword,
            NewPassword: passwordInfo.newPassword,
        };
        request({
            url: window.accountSrvHost + "/api/SubmitForNewPassword.ashx?" + getUrlSearchParams(params),
            method: "get",
        })
            .then((res: any) => {
                if (res.msg === "Success") {
                    ElNotify({
                        message: "保存成功！",
                        type: "success",
                    });
                } else {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            })
            .catch((err: any) => {
                console.log(err);
            });
    }
};
const checkPassword = () => {
    let flag = true;
    if (passwordInfo.oldPassword === passwordInfo.newPassword) {
        const samepwdtip = document.querySelector(`.newpwd-tip`) as unknown as HTMLElement;
        samepwdtip.innerText = SAME_PASSWORD_MESSAGE;
        samepwdtip.style.display = "block";
        flag = false;
    }
    if (passwordInfo.newPassword !== passwordInfo.repeatPassword) {
        const repeatpwdTip = document.querySelector(`.repeatpwd-tip`) as unknown as HTMLElement;
        repeatpwdTip.style.display = "block";
        flag = false;
    }
    if (
        passwordInfo.newPassword.length < 6 ||
        passwordInfo.newPassword === "" ||
        passwordInfo.repeatPassword === "" ||
        passwordInfo.oldPassword === ""
    ) {
        flag = false;
    }
    return flag;
};

const handleBlur = (type: string, event: FocusEvent) => {
    const target = event.target as HTMLInputElement;
    const tip = document.querySelector(`.${type}-tip`) as unknown as HTMLElement;
    if (type === "repeatpwd") {
        tip.style.display = passwordInfo.repeatPassword === passwordInfo.newPassword ? "none" : "block";
        return;
    }
    if (type === "newphone" && target.value === "") {
        tip.innerText = EMPTY_PHONE_MESSAGE;
        tip.style.display = "block";
        return;
    }
    if (target.value === "") {
        tip.innerText = type === "newpwd" ? EMPTY_PASSWORD_MESSAGE : tip.innerText;
        tip.style.display = "block";
        return;
    }
    if (target.value.length < 6 && type === "newpwd") {
        tip.innerText = SHORT_PASSWORD_MESSAGE;
        tip.style.display = "block";
        return;
    }
    tip.style.display = "none";
};

//验证用户手机和密码
const testUserPwd = ref("");
const confirmUserPwd = ref("");
const testPhoneShow = ref(false);
const testResult = ref(false);
const handleClose = () => {
    //每次都要验证，取消验证就回到个人资料
    testUserPwd.value = "";
    if (!testResult.value) {
        currentSlot.value = "main";
        activeName.value = "main";
    } else {
        currentSlot.value = "phone";
        testResult.value = false;
    }
};
const verifyPassword = () => {
    request({
        url: `/api/User/VerifyPassword?code=${encodeURIComponent(testUserPwd.value)}&mobile=${mainInfo.mobile}`,
        method: "post",
    })
        .then((res: any) => {
            testResult.value = res.data;
            if (res.data) {
                confirmUserPwd.value = testUserPwd.value;
                testPhoneShow.value = false;
            } else {
                confirmUserPwd.value = ""
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        })
        .catch((err: any) => {
            console.log(err);
        });
};

//修改手机注册校验
const EMPTY_PHONE_MESSAGE = "亲，用户手机不能为空哦！";
const EMPTY_PHONESMS_MESSAGE = "亲，验证码不能为空哦！";
const sendCodeAText = ref("发送验证码");
let codeWaitTime = 60;
let codeTimer: number;
const phoneInfo = reactive({
    newphone: "",
    code: "",
});

const clickFlag = ref(true);
const codeInfo = ref({});
const validateForRegister = () => {
    if (!CheckPhone()) return;
    if (!clickFlag.value) return;
    clickFlag.value = false;
    sendCodeAText.value = "正在发送...";
    clearInterval(codeTimer);
    codeWaitTime = 60;
    sendSMS().then((res: any) => {
        if (res.Success) {
            codeInfo.value = res.Data;
            codeTimer = setInterval(() => {
                codeWaitTime--;
                sendCodeAText.value = `重新发送（${codeWaitTime}）`;
                if (codeWaitTime === 0) {
                    sendCodeAText.value = "重新发送";
                    clearInterval(codeTimer);
                    clickFlag.value = true;
                }
            }, 1000);
        } else if (res.ErrorMsg.indexOf("已被注册") > -1) {
            ElNotify({
                message: res.ErrorMsg,
                type: "warning",
            });
            sendCodeAText.value = "发送验证码";
            clickFlag.value = true;
        } else {
            sendCodeAText.value = "发送验证码";
            clickFlag.value = true;
        }
    });
};
const sendSMS = () => {
    // const params = {
    //     CurrentSystemType: 1,
    //     Phone: phoneInfo.newphone,
    //     stype: 3,
    //     updatePhone: true,
    // };
    const params = {
        clientType: isLemonClient()?1040:1010,
        mobile: phoneInfo.newphone,
    };
    return request({
        url: window.authHost + "/api/ChangeMobile?" + getUrlSearchParams(params),
        method: "get",
    });
};

const saveRegisterPhone = () => {
    if (!CheckPhone() || !emptySMS()) return;
    // const params = {
    //     CurrentSystemType: 1,
    //     Mobile: phoneInfo.newphone,
    //     ConfirmCode: phoneInfo.code,
    //     EditType: "Mobile",
    // };
    const data = {
        clientType: isLemonClient() ? 1040 : 1010,
        permissionCheckType: 1, //权限校验方式：1.密码 2.验证码
        password: confirmUserPwd.value, //密码验证时必需传
        newCode: {
            //新手机号、验证码、签名和时间戳
            Mobile: phoneInfo.newphone,
            Code: phoneInfo.code,
            ...codeInfo.value,
        },
    };
    request({
        url: window.authHost + "/api/ChangeMobile",
        method: "put",
        data,
    }).then((res: any) => {
        if (res.Success) {
            getUserInfo();
            ElNotify({
                message: "亲，保存成功啦！",
                type: "success",
            });
        } else {
            ElNotify({
                message: res.ErrorMsg || "亲，保存失败啦！请核对手机号与验证码是否正确",
                type: "warning",
            });
        }
    });
};
const CheckPhone = () => {
    const regex = /^(0|86|17951)?1([3-9]|0)[0-9][0-9]{8}$/;
    if (!regex.test(phoneInfo.newphone)) {
        ElNotify({
            message: "亲，您输入的手机号码不正确！",
            type: "warning",
        });
        return false;
    }
    if (phoneInfo.newphone === mainInfo.mobile) {
        ElNotify({
            message: "亲，请勿输入相同号码",
            type: "warning",
        });
        return false;
    }
    return true;
};
const emptySMS = () => {
    if (phoneInfo.code === "") {
        const tip = document.querySelector(`.newphone-tip`) as unknown as HTMLElement;
        tip.innerText = EMPTY_PHONESMS_MESSAGE;
        tip.style.display = "block";
        return false;
    }
    return true;
};
const redirectToPermission = () => {
    globalWindowOpenPage("/Settings/Permissions", "权限设置");
};

//微信绑定
const codeLoading = ref(false);
const wechatBindShow = ref(true);

const codeBindExpired = ref(true);
const qrCodeBindImg = ref("");
let lflag_bind = 0;
let bindTimer: number;
const getQrCodeBind = () => {
    clearTimeout(bindTimer);
    codeBindExpired.value = false;
    codeLoading.value = true;
    getCodeToken()
        .then((res: any) => {
            if (res.State) {
                getcodeImg(res.Data.SceneId).then((res: any) => {
                    codeBindExpired.value = false;
                    const blob = new Blob([res], { type: "image/jpeg" });
                    const url = window.URL.createObjectURL(blob);
                    qrCodeBindImg.value = url;
                });
                longPollingBind(res.Data.SceneId, ++lflag_bind);
            } else {
                ElNotify({
                    message: res.Message,
                    type: "warning",
                });
            }
        })
        .finally(() => {
            codeLoading.value = false;
        });
};

const longPollingBind = (sceneId: any, thisFlag: number) => {
    if (!sceneId || sceneId == "") return;
    if (thisFlag < lflag_bind) return;
    return request({
        url: window.accountSrvHost + `/Api/WechatBindScaned.ashx?CurrentSystemType=1&SceneId=${sceneId}`,
        method: "get",
    }).then((res: any) => {
        if (res.State) {
            if (res.Data) {
                //用户已扫码
                ElConfirm("亲，绑定微信成功", true, () => {}, "绑定微信号信息").then(() => {
                    window.location.reload();
                });
            } else {
                ElConfirm("亲，绑定微信失败，请稍后重新尝试", true, () => {}, "绑定微信号警告");
            }
        } else {
            if (res.Message === "true") {
                bindTimer = setTimeout(() => {
                    longPollingBind(sceneId, thisFlag);
                }, 1000);
            } else if (res.Message === "time_out") {
                if (thisFlag >= lflag_bind) {
                    codeBindExpired.value = true;
                }
            } else {
                getQrCodeBind();
                ElConfirm(res.Message, true, () => {}, "绑定微信号警告");
            }
        }
    });
};

const changeWechatBind = () => {
    wechatConfirmShow.value = true;
    getQrCodeConfirm();
};
const getCodeToken = () => {
    return request({
        url: window.accountSrvHost + "/api/WxQrToken.ashx?CurrentSystemType=1",
        method: "get",
    });
};
const getcodeImg = (sceneId: any) => {
    return request({
        url: window.accountSrvHost + `/api/QrcodeProxy.ashx?sceneid=${sceneId}`,
        method: "get",
        responseType: "blob",
    });
};
//微信验证
const confirmCodeExpired = ref(true);
const wechatConfirmShow = ref(false);
const qrCodeConfirmImg = ref("");
let lflag_confirm = 0;
let confirmTimer: number;
let confirmByWechat = true;
const refreshConfirmCode = () => {
    getQrCodeConfirm();
};
const getQrCodeConfirm = () => {
    confirmCodeExpired.value = false;
    codeLoading.value = true;
    confirmByWechat = true;
    getCodeToken()
        .then((res: any) => {
            if (res.State) {
                getcodeImg(res.Data.SceneId).then((res: any) => {
                    confirmCodeExpired.value = false;
                    const blob = new Blob([res], { type: "image/jpeg" });
                    const url = window.URL.createObjectURL(blob);
                    qrCodeConfirmImg.value = url;
                });
                longPollingConfirm(res.Data.SceneId, ++lflag_confirm);
            } else {
                ElNotify({
                    message: res.Message,
                    type: "warning",
                });
            }
        })
        .finally(() => {
            codeLoading.value = false;
        });
};

const longPollingConfirm = (sceneId: any, thisFlag: number) => {
    if (!sceneId || sceneId == "") return;
    if (thisFlag < lflag_confirm) return;
    if (!confirmByWechat) return;
    return request({
        url: window.accountSrvHost + `/Api/WechatBindScaned.ashx?CurrentSystemType=1&SceneId=${sceneId}`,
        method: "get",
    }).then((res: any) => {
        if (res.State) {
            if (res.Message == "") {
                //扫码成功关闭弹窗打开新的绑定新微信弹窗
                clearTimeout(confirmTimer);
                wechatConfirmShow.value = false;
                wechatChangeShow.value = true;
                getQrCodeChange();
            } else {
                ElConfirm(res.Message, true, () => {}, "绑定微信号警告");
            }
        } else {
            if (res.Message === "true") {
                confirmTimer = setTimeout(() => {
                    longPollingConfirm(sceneId, thisFlag);
                }, 1000);
            } else if (res.Message === "time_out") {
                if (thisFlag >= lflag_confirm) {
                    confirmCodeExpired.value = true;
                } else {
                    getQrCodeConfirm();
                    ElConfirm(res.Message, true, () => {}, "绑定微信号警告");
                }
            }
        }
    });
};
//微信短信验证
const wechatSMSConfirmShow = ref(false);
const confirmSMSCode = ref("");
const confirmWechatByPhone = () => {
    wechatConfirmShow.value = false;
    wechatSMSConfirmShow.value = true;
    sendWeChatConfirmSMS();
};
let canSendSms = true;
const wechatConfirmSMSAText = ref("发送验证码");
let confirmSMSTimer: number;
const sendWeChatConfirmSMS = () => {
    if (!canSendSms) return;
    wechatConfirmSMSAText.value = "正在发送...";
    canSendSms = false;
    codeWaitTime = 60;
    const params = {
        CurrentSystemType: 1,
        Phone: mainInfo.mobile,
        stype: 8,
    };
    request({
        url: window.accountSrvHost + "/Default/Services/SendSMSForConfirm.ashx?" + getUrlSearchParams(params),
        method: "get",
    }).then((res: any) => {
        if (res === "Success") {
            confirmSMSTimer = setInterval(() => {
                codeWaitTime--;
                wechatConfirmSMSAText.value = `重新发送（${codeWaitTime}）`;
                if (codeWaitTime === 0) {
                    wechatConfirmSMSAText.value = "重新发送";
                    clearInterval(confirmSMSTimer);
                    canSendSms = true;
                }
            }, 1000);
        } else {
            canSendSms = true;
            wechatConfirmSMSAText.value = "发送验证码";
        }
    });
};
const validateWeChatConfirm = () => {
    if (confirmSMSCode.value === "") {
        ElNotify({
            message: "亲，请输入验证码！",
            type: "warning",
        });
        return;
    }
    const params = {
        CurrentSystemType: 1,
        Phone: mainInfo.mobile,
        Verific: confirmSMSCode.value,
        type: "Verific",
    };
    request({
        url: window.accountSrvHost + "/Api/WechatBindProcess.ashx?" + getUrlSearchParams(params),
        method: "get",
    }).then((res: any) => {
        if (res === "Success") {
            wechatSMSConfirmShow.value = false;
            wechatChangeShow.value = true;
            getQrCodeChange();
        } else {
            ElNotify({
                message: res,
                type: "warning",
            });
        }
    });
};

const cancelWechatConfirm = () => {
    clearTimeout(confirmTimer);
    clearTimeout(changeTimer);
    confirmSMSCode.value = "";
};

//绑定新微信
const wechatChangeShow = ref(false);
const qrCodeChangeImg = ref("");
const changeCodeExpired = ref(false);
let lflag_change = 0;
let changeTimer: number;
const refreshChangeCode = () => {
    getQrCodeChange();
};
const getQrCodeChange = () => {
    changeCodeExpired.value = false;
    codeLoading.value = true;
    getCodeToken()
        .then((res: any) => {
            if (res.State) {
                getcodeImg(res.Data.SceneId).then((res: any) => {
                    changeCodeExpired.value = false;
                    const blob = new Blob([res], { type: "image/jpeg" });
                    const url = window.URL.createObjectURL(blob);
                    qrCodeChangeImg.value = url;
                });
                longPollingChange(res.Data.SceneId, ++lflag_change);
            } else {
                ElNotify({
                    message: res.Message,
                    type: "warning",
                });
            }
        })
        .finally(() => {
            codeLoading.value = false;
        });
};
const longPollingChange = (sceneId: any, thisFlag: number) => {
    if (!sceneId || sceneId == "") return;
    if (thisFlag < lflag_change) return;
    return request({
        url: window.accountSrvHost + `/Api/WechatChangeScaned.ashx?CurrentSystemType=1&SceneId=${sceneId}`,
        method: "get",
    }).then((res: any) => {
        if (res.State) {
            if (res.Data) {
                wechatChangeShow.value = false;
                ElConfirm("亲，绑定微信成功", true, () => {}, "绑定微信号信息").then(() => {
                    window.location.reload();
                });
            } else {
                ElConfirm("亲，绑定微信失败，请稍后重新尝试", true, () => {}, "绑定微信号警告");
            }
        } else {
            if (res.Message === "true") {
                changeTimer = setTimeout(() => {
                    longPollingChange(sceneId, thisFlag);
                }, 1000);
            } else if (res.Message === "time_out") {
                if (thisFlag >= lflag_change) {
                    changeCodeExpired.value = true;
                }
            } else {
                getQrCodeChange();
                ElConfirm(res.Message, true, () => {}, "绑定微信号警告");
            }
        }
    });
};

const checkIsBind = () => {
    const params = {
        CurrentSystemType: 1,
        type: "isBind",
    };
    request({
        url: window.accountSrvHost + "/Api/WechatBindProcess.ashx?" + getUrlSearchParams(params),
        method: "get",
    }).then((res: any) => {
        if (res === "Binded") {
            wechatBindShow.value = true;
        } else {
            wechatBindShow.value = false;
        }
    });
};
const getUserInfo = () => {
    request({
        url: `/api/PersonalInfo/GetCurrentUser`,
        method: "post",
    }).then((res: any) => {
        oldUserInfo.oldName = res.data.userName;
        oldUserInfo.oldMail = res.data.userId;
        mainInfo.mail = res.data.userId;
        mainInfo.name = res.data.userName;
        mainInfo.mobile = res.data.mobile;
    });
};
const headImgUrl = ref("");
const getHeadImg = () => {
    request({
        url: "/api/HeadImg",
        method: "get",
        responseType: "blob",
    }).then((res: any) => {
        const blob = new Blob([res], { type: "image/jpeg" });
        const url = window.URL.createObjectURL(blob);
        headImgUrl.value = url;
    });
};
onMounted(() => {
    getUserInfo();
    checkIsBind();
    getHeadImg();
});
onBeforeUnmount(() => {
    clearInterval(codeTimer);
    clearTimeout(bindTimer);
    clearTimeout(confirmTimer);
    clearInterval(confirmSMSTimer);
    clearTimeout(changeTimer);
});
</script>

<style style lang="less" scoped>
.masked-input {
    :deep(.el-input__inner){
        -webkit-text-security: disc; /* Chrome, Safari, Opera */
        text-security: disc; /* Firefox, IE */
    }
}
:deep(.el-tabs__nav-scroll) {
    display: flex;
    justify-content: center;
}
:deep(.el-tabs) {
    &.el-tabs--top {
        padding-top: 40px;
    }
    .el-tabs__item {
        font-size: var(--h3);
        margin-right: 60px;
    }
    .el-tabs__item:last-child {
        margin-right: 0;
    }
}
.code-button {
    width: 106px;
    height: 32px;
    line-height: 32px;
}
.test-content {
    .test-main {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .test-line {
            margin-top: 20px;
            width: 230px;
            display: flex;
            align-items: center;
            .line-title {
                width: 48px;
            }
        }
        padding-bottom: 34px;
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}

:deep(.el-form) {
    .el-form-item__label::before {
        display: none;
    }
    .el-form-item__error {
        left: 100%;
        margin-left: 10px;
        white-space: nowrap;
        top: 50%;
        transform: translateY(-50%);
        line-height: 32px;
        font-size: var(--font-size);
    }
}

.content {
    .main-content {
        padding-top: 30px;
        .slot-content {
            .newusername-tip,
            .newemail-tip,
            .oldpwd-tip,
            .newpwd-tip,
            .repeatpwd-tip,
            .newphone-tip {
                color: var(--red);
                display: none;
            }
            .slot-content-footer {
                width: 1000px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .footer-tip {
                    color: var(--red);
                    font-size: var(--h5);
                    line-height: 17px;
                    text-align: center;
                }
                .phonefooter-tip {
                    margin: 22px 0px 35px;
                    width: 448px;
                    height: 40x;
                    padding: 30px 41px 36px 41px;
                    background-color: #f8f8f8;
                    display: flex;
                    .phonefooter-title {
                        width: 36px;
                        color: #fd5055;
                        font-size: var(--h5);
                        margin-right: 6px;
                    }
                    .phonefooter-txt {
                        text-align: left;
                        flex: 1;
                        color: #5d5d5d;
                        font-size: var(--h5);
                        line-height: 20px;
                        .link {
                            font-size: var(--h5);
                            text-decoration: underline;
                        }
                    }
                }
            }
        }
    }
}
.forgetpwd-tip {
    outline: none;
    cursor: pointer;
    text-decoration: none;
    color: var(--link-color);
    font-size: var(--font-size);
    line-height: var(--line-height);
}
.name-check-content {
    .name-check-main {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 36px;
        padding-bottom: 16px;
        .checkmain-content {
            text-align: center;
            font-size: var(--font-size);
            width: 296px;
        }
        .checkmain-tip {
            color: red;
            margin-top: 10px;
            font-size: 12px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.wechat {
    width: 1000px;
    .wechat-content {
        display: flex;
        flex-direction: column;
        text-align: left;
        .wechat-line {
            padding-left: 331px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            margin-top: 10px;
            .barcode-state {
                color: var(--red);
                background-repeat: no-repeat;
                background-position-y: center;
                background-position-x: 50px;
                background-image: url("@/assets/Default/warn-red.png");
                padding-right: 24px;
                display: inline-block;
            }
            .binded {
                color: var(--link-color);
                background-image: url("@/assets/Default/warn-ok.png");
            }
        }
    }
    .slot-content-footer {
        .barcode-img {
            margin: 30px auto 8px;
            border: 1px solid var(--border-color);
            width: 190px;
            height: 190px;
            position: relative;
            .qrcodeimg {
                width: 100%;
            }
            .expired-tip {
                background-color: rgba(255, 255, 255, 0.9);
                width: 190px;
                height: 190px;
                position: absolute;
                left: 50%;
                top: 50%;
                margin-left: -95px;
                margin-top: -95px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                .expired-tip-refresh {
                    cursor: pointer;
                    color: #06c05f;
                    font-size: 16px;
                }
                .expired-tip-content {
                    font-size: 18px;
                    color: #5b5b5b;
                    font-weight: 500;
                }
            }
        }
    }
}
.wechatDialog-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0px;
    .wechatverify-img {
        margin: 10px 0px;
        border: 1px solid var(--border-color);
        width: 140px;
        height: 140px;
        position: relative;
        .verifyimg {
            width: 100%;
        }
        .verify-expired-tip {
            background-color: rgba(255, 255, 255);
            width: 140px;
            height: 140px;
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -70px;
            margin-top: -70px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .verify-tip-refresh {
                cursor: pointer;
                color: var(--dark-link-color);
                font-size: var(--font-size);
                margin-top: 10px;
            }
            .verify-tip-content {
                font-size: var(--font-size);
            }
        }
    }
}
.smsDialog-content {
    padding-top: 20px;
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
