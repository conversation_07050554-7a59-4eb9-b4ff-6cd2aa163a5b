<template>
    <div class="edit-content" style="width: 1000px; padding: 0px;">
        <div class="title">{{ props.changeTitle }}</div>
        <div class="" style="border: 1px solid var(--slot-title-color);margin-top: 32px;">
            <table class="change-tb">
                <tr>
                    <td colspan="4" class="tb-hr">基本信息</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px; text-align: right">资产编号：</td>
                    <td id="ztxg_bh" class="tb-field">{{ props.changeData.fa_num }}</td>
                    <td class="tb-title" style="width: 84px">资产类别：</td>
                    <td id="ztxg_lb" class="tb-field">
                        <Tooltip :content="props.changeData.fa_type_name" :max-width="326">{{ props.changeData.fa_type_name }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">资产名称：</td>
                    <td id="ztxg_mc" class="tb-field">
                        <Tooltip :content="props.changeData.fa_name" :max-width="326">{{ props.changeData.fa_name }}</Tooltip>
                    </td>
                    <td class="tb-title">资产型号：</td>
                    <td id="ztxg_xh" class="tb-field">
                        <Tooltip :content="props.changeData.fa_model" :max-width="326">{{ props.changeData.fa_model }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">所属部门：</td>
                    <td id="ztxg_bm" class="tb-field">
                        <Tooltip :content="props.changeData.department_name" :max-width="326">{{
                            props.changeData.department_name
                        }}</Tooltip>
                    </td>
                </tr>
            </table>
            <div class="line"></div>
            <table class="change-tb" cellspacing="0" cellpadding="0">
                <tr>
                    <td colspan="4" class="tb-hr">变更内容</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px">资产原值：</td>
                    <td id="ztxg_value1" class="tb-field" style="width: 326px">{{ props.changeData.value?.toFixed(2) }}</td>
                    <td class="tb-title" style="width: 84px"></td>
                    <td class="tb-field" style="width: 416px"></td>
                </tr>
                <tr>
                    <td class="tb-title">资产原值调整为：</td>
                    <td class="tb-field">
                        <div class="jqtransform">
                            <el-input v-model="dataValue" style="width: 180px; height: 28px"></el-input>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="buttons" style="margin-top: 22px; margin-bottom: 40px; text-align: center">
                <a class="button solid-button" style="margin: 0 5px" @click="savedata">保存</a>
                <a class="button" style="margin: 0 5px" @click="changeCancle">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { ref } from "vue";
import { ElConfirm } from "@/util/confirm";
import { isDecimal } from "../validator";
import { ElNotify } from "@/util/notify";
import Tooltip from "@/components/Tooltip/index.vue";
const props = defineProps({
    changeData: {
        type: Object,
        default: () => {
            return {};
        },
    },
    changeTitle: {
        type: String,
        default: "",
    },
    pid: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(["changeCancle", "goToVoucher", "getFixedAssetsList"]);
const dataValue = ref("");

function savedata() {
    let curentnum = dataValue.value.replace(/,/g, "");
    if (!isDecimal(curentnum)) {
        ElNotify({
            type: "warning",
            message: "请输入正确的原值！",
        });
        return false;
    }
    if(props.changeData.value === Number(curentnum)){
       ElNotify({
            type: "warning",
            message: "亲，您的数据没有变动，不能保存哦！",
        });
        return false;
    }
    request({
        url: `/api/FAChange/ValueChange?faid=${props.changeData.fa_id}&pid=${props.pid}&value=${dataValue.value}&vid=0`,
        method: "post",
    })
        .then((res: IResponseModel<number>) => {
            if(res.state !== 1000){
                ElNotify({
                    type: "warning",
                    message: res.msg||'保存失败！',
                });
                return false;
            }
            ElConfirm("您的变更已保存，需要立即生成凭证吗？").then((r: Boolean) => {
                if (r) {
                    getVoucher(res.data);
                } else {
                    emits("changeCancle",true);
                }
            });
        })
        .finally(() => {
            emits("getFixedAssetsList");
        });
}
//资产变更生成凭证
function getVoucher(changeId: number) {
    request({
        url: `/api/FAVoucher/GenerateValueChangeVoucher?faid=${props.changeData.fa_id}&changeId=${changeId}&value=${
            Number(dataValue.value) - Number(props.changeData.value)
        }`,
        method: "post",
    }).then((res: IResponseModel<object>) => {
        emits("goToVoucher", res.data, "原值调整凭证", null, null, changeId);
    });
}
function changeCancle() {
    emits("changeCancle");
}
</script>
<style lang="less" scoped>
</style>