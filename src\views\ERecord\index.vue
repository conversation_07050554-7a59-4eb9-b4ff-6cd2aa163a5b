<template>
    <div class="content">
        <div class="title">会计电子档案</div>
        <el-tabs v-model="activeName">
            <el-tab-pane label="电子档案列表" name="list">
                <div class="main-content">
                    <new-dir-dialog ref="dirDialog" v-model="newDirDialogShow" :fid="fid" @reloadTableData="loadFileTableData">
                    </new-dir-dialog>
                    <div class="file-center main-top" v-show="fid >= 0">
                        <MainTopBox
                            ref="mainTopBoxRef"
                            v-model:startDate="startDate"
                            v-model:endDate="endDate"
                            v-model:isNewName="isNewName"
                            :fid="fid"
                            :file-path="filePath"
                            :selected-list="fileMultipleSelection"
                            :tree-list="treeList.filter((item) => item.id >= 0)"
                            :restSpace="restSpace"
                            @get-table-list="getTableListFu"
                            @reload-table-data="loadFileTableData"
                            @handle-search="handleSearch"
                            @reload-search-date="reloadSearchDate"
                            :mini-date="miniDate"
                            :max-date="maxDate"
                            :filterSearchInfo="filterSearchInfo"
                        />
                    </div>
                    <div class="main-top" :class="fid === -2 ? 'backup-center' : 'audit-center'" v-show="fid === -2 || fid === -3">
                        <a
                            class="button solid-button c-icon-text delete yes"
                            title="点击删除"
                            @click="deleteBackupOrAudit(fid === -2 ? 'backup' : 'audit')"
                            v-show="!checkDeleteDisabled"
                        >
                            删除
                        </a>
                        <a class="button solid-button disabled c-icon-text delete no" title="选中文件后删除" v-show="checkDeleteDisabled">
                            删除
                        </a>
                    </div>

                    <div :class="['main-center', 'body-main', { isErpCenter: isErp }]">
                        <div class="body-left">
                            <div class="file-center" v-show="fid >= 0">
                                <FileCenterTable
                                    v-loading="loading"
                                    element-loading-text="正在加载数据..."
                                    :isNewName="isNewName"
                                    :table-data="fileTableData"
                                    :emptyText="emptyText"
                                    :page-is-show="true"
                                    :layout="paginationData.layout"
                                    :page-sizes="paginationData.pageSizes"
                                    :page-size="paginationData.pageSize"
                                    :total="paginationData.total"
                                    :currentPage="
                                        searchInfoType === 0 ? InfoOfDirCurrent : searchInfoType === 1 ? tableListCurrent : searchCurrent
                                    "
                                    @selection-change="fileHandleSelectionChange"
                                    @refresh-file="handleRefreshClick"
                                    @get-table-list="handleSearch"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    @refresh="handleRerefresh"
                                    :searchOptions="searchOptions"
                                    :headerMulSeletVal="headerMulSeletVal"
                                    :filterSearchInfo="filterSearchInfo"
                                    @filterSearch="filterSearch"
                                />
                            </div>
                            <div :class="fid === -2 ? 'backup-center' : 'audit-center'" v-show="fid === -2 || fid === -3">
                                <Table
                                    :data="fid === -2 ? collectionList : auditCollectionList"
                                    :emptyText="emptyText"
                                    :columns="backupOrAuditColumns"
                                    @selection-change="otherHandleSelectionChange"
                                    :height="'100%'"
                                    :tableName="setModuleBackup"
                                >
                                    <template #name>
                                        <el-table-column 
                                            :min-width="670" 
                                            align="left" 
                                            header-align="left" 
                                            label="名称"
                                            prop="name"
                                            :width="getColumnWidth(setModuleBackup, 'name')"
                                        >
                                            <template #default="scope">
                                                <span
                                                    class="link"
                                                    title="点击下载"
                                                    @click="downLoadCollectionOrAuditCollectionItem(scope.row)"
                                                >
                                                    {{ scope.row.FILE_NAME }}
                                                </span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                    <template #date>
                                        <el-table-column
                                            :min-width="200"
                                            align="left"
                                            header-align="left"
                                            prop="CREATED_DATE_STR"
                                            label="上传日期"
                                            :width="getColumnWidth(setModuleBackup, 'CREATED_DATE_STR')"
                                        />
                                    </template>
                                    <template #size>
                                        <el-table-column min-width="100px" align="left" header-align="left" label="大小" :resizable="false">
                                            <template #default="scope">
                                                <span>{{ sizeFormatter(scope.row.FILE_SIZE) }}</span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </Table>
                            </div>
                        </div>
                        <div class="body-right drag-right">
                            <div class="assit-col-line"></div>
                            <div class="drag-line" v-tree-drag="flag" v-show="flag.closeFlag"></div>
                            <div class="tree-click">
                                <span :class="['tree-img', {'disabled': !flag.closeFlag}]" @click="treeClick(0, flag)"></span>
                                <span :class="['tree-img expand', {'disabled': !flag.expandFlag}]" @click="treeClick(1, flag)"></span>
                            </div>
                            <div class="tree-view">
                                <div class="tree" v-loading="loading" element-loading-text="正在加载数据..." target="tree">
                                    <el-scrollbar ref="treeScrollbarRef" :always="true" :minSize="80">
                                        <div class="space-state">
                                            <div>
                                                已用空间(
                                                <span class="disk-used-space">{{ sizeFormatter(usedSpace) || 0 }}</span>
                                                /
                                                <span class="disk-total-space">{{ sizeFormatter(totalSpace) }}</span>
                                                )
                                            </div>
                                            <div
                                                class="update-btn"
                                                v-show="isTrial && !isThirdPart"
                                                @click="handleTrialExpired({ msg: ExpiredToBuyDialogEnum.ERecordSpaceExpand, needExpired: false })"
                                            ></div>
                                        </div>
                                        <el-tree
                                            :data="treeList"
                                            ref="treeRef"
                                            :indent="21"
                                            node-key="id"
                                            empty-text=" "
                                            :default-expand-all="true"
                                            :expand-on-click-node="false"
                                            :props="{ label: 'text', class: customNodeClass }"
                                            :highlight-current="true"
                                            @node-click="handleNodeClick"
                                        >
                                            <template #default="{ node, data }">
                                                <span class="file-name el-tree-node__label">{{ node.label }}</span>
                                                <span
                                                    v-permission="['lemondisk-canedit']"
                                                    v-show="node.label !== '数据备份' && node.label !== '审计文件'"
                                                    class="edit-more"
                                                >
                                                    <el-dropdown placement="bottom-end" :show-timeout="300">
                                                        <span class="el-dropdown-link" @click.stop.prevent="">
                                                            <el-icon><MoreFilled /></el-icon>
                                                        </span>
                                                        <template #dropdown>
                                                            <el-dropdown-menu>
                                                                <el-dropdown-item
                                                                    v-if="!onlyCanEditTreeList.includes(node.label)"
                                                                    @click="handleEditClick($event, 2, node, data)"
                                                                >
                                                                    <el-icon style="color: rgb(123, 176, 225)"> <Edit /> </el-icon
                                                                    >编辑</el-dropdown-item
                                                                >
                                                                <el-dropdown-item
                                                                    v-if="!onlyCanEditTreeList.includes(node.label)"
                                                                    @click="handleEditClick($event, 3, node, data)"
                                                                    ><el-icon style="color: rgb(239, 79, 79)"> <Delete /> </el-icon
                                                                    >删除</el-dropdown-item
                                                                >
                                                                <el-dropdown-item @click="handleEditClick($event, 1, node, data)"
                                                                    ><el-icon style="color: rgb(0, 145, 255)"> <Plus /> </el-icon
                                                                    >新增</el-dropdown-item
                                                                >
                                                            </el-dropdown-menu>
                                                        </template>
                                                    </el-dropdown>
                                                </span>
                                            </template>
                                        </el-tree>
                                    </el-scrollbar>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="电子档案归档" name="file" v-if="checkPermission(['lemondiskarchive-canview'])">
                <div class="main-content file">
                    <div class="main-top">
                        <a class="button solid-button" @click="handleProcess">归档</a>
                        <a class="button ml-20" v-permission="['lemondiskarchive-candelete']" @click="handleBatchDelete">删除</a>
                    </div>
                    <div class="main-center">
                        <Table
                            class="file-table"
                            :data="fileData"
                            :columns="fileColumns"
                            :showOverflowTooltip="true"
                            :scrollbar-show="true"
                            :selectable="selectable"
                            :rowClassName="judgeRowClassName"
                            @selection-change="fileSelectionChange"
                            :tableName="setModule"
                        >
                            <template #Name>
                                <el-table-column 
                                    label="档案名称"
                                    :min-width="250" 
                                    :width="getColumnWidth(setModule, 'name')"
                                    align="left" 
                                    header-align="left"
                                    prop="name"
                                >
                                    <template #default="scope">
                                        <span>{{ scope.row.fileName}}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #operation>
                                <el-table-column min-width="150px" align="left" header-align="left" label="操作" :resizable="false">
                                    <template #default="{ row }">
                                        <template v-if="row.progress === 100">
                                            <span class="link" v-permission="['lemondiskarchive-candelete']" @click="handleDelete(row)">
                                                删除
                                            </span>
                                            <span class="link" v-permission="['lemondiskarchive-candownload']" v-if="row.expiredDate">
                                                <el-popover
                                                    placement="bottom-end"
                                                    :width="300"
                                                    trigger="hover"
                                                    popper-class="tip-popover"
                                                    :popper-style="popperStyle"
                                                >
                                                    <template #reference>
                                                        <span>
                                                            <span @click="handleDownload(row)">下载</span>
                                                            <img
                                                                src="@/assets/Settings/warnning-orange.png"
                                                                alt=""
                                                                style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                                            />
                                                        </span>
                                                    </template>
                                                    空间不足！已为您提供额外存储空间备份， <br />
                                                    请于72小时内下载到本地，超时将自动删除
                                                </el-popover>
                                            </span>
                                            <span
                                                v-else
                                                class="link"
                                                v-permission="['lemondiskarchive-candownload']"
                                                @click="handleDownload(row)"
                                            >
                                                下载
                                            </span>
                                        </template>
                                        <template v-else>
                                            <span class="progress-container" v-show="row.progress !== 0">
                                                <span class="progress-bar">
                                                    <span class="progress-solid-bar" :style="{ width: row.progress + '%' }"></span>
                                                </span>
                                                <span>{{ row.progress + "%" }}</span>
                                            </span>
                                            <span class="loading-operator" v-show="row.progress === 0">
                                                <span class="loading-icon"></span>
                                                <span class="link" style="margin-left: 3px">数据加载中...</span>
                                            </span>
                                        </template>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #empty>
                                <div class="empty">
                                    您还没有归档数据，请点击
                                    <span class="blue" @click="handleProcess">“归档”</span>
                                    按钮进行归档操作哦~
                                </div>
                            </template>
                        </Table>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts">
export default {
    name: "ERecord",
};
</script>
<script setup lang="ts">
import { ref, watch, onMounted, computed, onActivated, onDeactivated, reactive, nextTick } from "vue";
import { type IResponseModel, request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getGlobalToken } from "@/util/baseInfo";
import { sizeFormatter } from "@/util/format";
import { getUrlSearchParams, globalExport, tryClearCustomUrlParams } from "@/util/url";
import { ElAlert, ElConfirm } from "@/util/confirm";
import { getUploadDate, getFilePath } from "./utils";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { checkPermission } from "@/util/permission";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { 
    ISearchData, 
    ITree, 
    IBackupOrAuditItem, 
    IFileTableItem, 
    IhandleSearchParams, 
    IProcessItem,
    IFSearchItem 
} from "./types";
import { Edit, Plus, Delete } from "@element-plus/icons-vue";
import { ElTree } from "element-plus";
import Table from "@/components/Table/index.vue";
import MainTopBox from "./components/MainTopBox.vue";
import NewDirDialog from "./components/NewDirDialog.vue";
import FileCenterTable from "./components/FileCenterTable.vue";
import { usePagination } from "@/hooks/usePagination";
import { useRoute } from "vue-router";
import { getGlobalLodash } from "@/util/lodash";
import { judgeRowClassName, popperStyle } from "@/views/Settings/Backup/utils";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { checkCanProcessAndHasTemporaryFile } from "@/views/Settings/Backup/utils";
import { useLoading } from "@/hooks/useLoading";
import type { ICheckSpace } from "../Settings/Backup/types";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { treeClick } from "@/views/AccountBooks/SubsidiaryLedger/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { useTrialStatusStore } from "@/store/modules/trialStatus";

const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const newDirDialogShow = ref(false);
const dirDialog = ref();
const setModuleBackup = "ERecordBackup"
const backupOrAuditColumns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 30, headerAlign: "center", align: "center" },
    { slot: "name" },
    { slot: "date" },
    { slot: "size" },
]);
const trialStatusStore = useTrialStatusStore();
const isTrial = computed(() => {
    return trialStatusStore.isTrial;
});
// 文件名增加凭证号
const isNewName = ref(false);

const mainTopBoxRef = ref();
const treeRef = ref<InstanceType<typeof ElTree>>();
const isErp = ref(window.isErp);
const fid = ref(0);
const checkDeleteDisabled = computed(() => {
    return fid.value === -2 ? backupMultipleSelection.value.length === 0 : auditMultipleSelection.value.length === 0;
});
const filePath = ref("所有文件");
const usedSpace = ref(0);
const totalSpace = ref(0);
const startDate = ref(getUploadDate().START_DATE);
const endDate = ref(getUploadDate().END_DATE);
const miniDate = ref("");
const maxDate = ref(getUploadDate().END_DATE);
const treeList = ref<ITree[]>([]);
const collectionList = ref<IBackupOrAuditItem[]>([]);
const auditCollectionList = ref<IBackupOrAuditItem[]>([]);
const fileMultipleSelection = ref<IFileTableItem[]>([]);
const backupMultipleSelection = ref<IBackupOrAuditItem[]>([]);
const auditMultipleSelection = ref<IBackupOrAuditItem[]>([]);
const fileTableData = ref<IFileTableItem[]>([]);
const onlyCanEditTreeList = ["所有文件", "凭证附件", "回单", "数据备份", "发票", "现金流水", "票据", "工资", "资产"];
const handleNodeClick = (treeData: ITree, node: any) => {
    restFilterInfo();
    fid.value = treeData.id;
    filePath.value = getFilePath(node, []).reverse().join(">");
    if (fid.value >= 0) {
        loadFileTableData();
    } else if (fid.value === -2) {
        getCollection();
    } else if (fid.value === -3) {
        getAuditCollection();
    }
};
const treeScrollbarRef = ref();

const customNodeClass = (data: any, node: any): any => {
    let className = "";
    if (data.id === fid.value) {
        className += "current-highlight";
    }

    if (!data.children || !data.children.length) {
        className += " " + "no-children";
    }
    return className;
};
let loading = ref(false);
let emptyText = ref("");
const searchInfoType = ref(0); //0调用（GetInfoOfDir） 1调用左边多项搜索(getTableList)  2调用右边关键字搜索(handleSearch)
const tableListParam = ref();
let tableListCurrent = 1; //searchInfoType为1
let searchCurrent = 1; //searchInfoType为2
let InfoOfDirCurrent = 1; //searchInfoType为0
const getTableListFu = (params: ISearchData, headerFlag: boolean) => {
    tableListParam.value = JSON.parse(JSON.stringify(params));
    searchInfoType.value = 1;
    if (!headerFlag) {
        restFilterInfo();
    }
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
    } else {
        getTableList(params);
    }
};
const getTableList = (params: ISearchData) => {
    loading.value = true;
    emptyText.value = "";
    searchInfoType.value = 1;
    request({
        url: window.jLmDiskHost + "/Services/Doc/FilterDoc.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: {
            ...params,
            pageIndex: tableListCurrent,
            pageSize: paginationData.pageSize,
        },
    })
        .then((data: any) => {
            if (data.Succeed) {
                // fileController.refreshFileList(data.Result, data.Result.Permission);
                fileTableData.value = data.Result;
                paginationData.total = data.TotalCount;
                mainTopBoxRef.value?.setInfos();
                loading.value = false;
            } else {
                ElNotify({ type: "warning", message: data.Message });
                loading.value = false;
            }

            if (!fileTableData.value.length) {
                emptyText.value = "暂无文件";
            }
        })
        .catch(() => {
            loading.value = false;
            fileTableData.value = [];
            paginationData.total = 0;
        })
        .finally(() => {
            mainTopBoxRef.value?.cancel();
            loading.value = false;
        });
};
const wordSearchParam = ref();
const handleSearch = (searchData: IhandleSearchParams) => {
    searchInfoType.value = 2;
    if (!searchData.word) {
        if (wordSearchParam.value !== undefined && wordSearchParam.value.word) {
            if (paginationData.currentPage === 1) {
                getInfoOfDir();
            } else {
                paginationData.currentPage = 1;
                searchInfoType.value = 0;
            }
        }
        wordSearchParam.value = undefined;
        return;
    }
    if (
        tableListCurrent !== 1 ||
        InfoOfDirCurrent !== 1 ||
        (wordSearchParam.value !== undefined && searchData.word !== wordSearchParam.value.word)
    ) {
        wordSearchParam.value = JSON.parse(JSON.stringify(searchData));
        paginationData.currentPage = 1;
        return;
    }
    wordSearchParam.value = JSON.parse(JSON.stringify(searchData));
    const params = {
        word: searchData.word,
        fid: fid.value,
        appasid: getGlobalToken(),
        date: searchData.date,
        fileType: searchData.fileType,
        pageIndex: searchCurrent,
        pageSize: paginationData.pageSize,
    };
    request({
        url: window.jLmDiskHost + "/Services/Doc/FilterDoc.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: params,
    }).then((data: any) => {
        if (data.Succeed) {
            fileTableData.value = data.Result;
            paginationData.total = data.TotalCount;
        } else {
            ElNotify({ type: "warning", message: data.Message });
        }
    });
};
const restSpace = ref(0);
const getDiskState = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetDiskState.ashx?r=" + Math.random(),
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    })
        .then((data: any) => {
            if (data.Succeed && data.Result) {
                const disk = data.Result.Disk;
                usedSpace.value = disk.UsedSpace;
                totalSpace.value = disk.LastTotalSpace > disk.TotalSpace ? disk.LastTotalSpace : disk.TotalSpace;
                restSpace.value = totalSpace.value - usedSpace.value;
                if (restSpace.value < 2 * 1024 * 1024) {
                    handleTrialExpired({
                        msg:
                            restSpace.value <= 0
                                ? ExpiredToBuyDialogEnum.ERecordSpaceOverflow
                                : ExpiredToBuyDialogEnum.ERecordSpaceNotEnough,
                        model: "ERecordSpaceNotEnough",
                        frequency: "day",
                        needToStore: true,
                        needExpired: false,
                    });
                }
            } else {
                ElNotify({ type: "warning", message: data.Message });
            }
        })
        .catch(() => {
            loading.value = false;
        });
};
const saveFid = ref(0);
const getInfoOfDir = () => {
    if (route.query.catalog && decodeURIComponent(route.query.catalog as string) === "数据备份") {
        fid.value = -2;
    }
    if (paginationData.currentPage > 1 && saveFid.value !== fid.value) {
        paginationData.currentPage = 1;
    }
    loading.value = true;
    searchInfoType.value = 0;
    saveFid.value = fid.value;
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetInfoOfDir.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: {
            startDate: startDate.value,
            endDate: endDate.value,
            fid: fid.value,
            appasid: getGlobalToken(),
            pageIndex: InfoOfDirCurrent,
            pageSize: paginationData.pageSize,
        },
    })
        .then((data: any) => {
            loading.value = false;
            if (data.Succeed) {
                fileTableData.value = data.Result.Files;
                treeList.value = data.Result.dirs;
                paginationData.total = data.TotalCount;
            }
            if (!fileTableData.value.length) {
                emptyText.value = "暂无文件";
            }
        })
        .finally(() => {
            tryClearCustomUrlParams(route);
        });
};
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    if (searchInfoType.value === 2) {
        searchCurrent = paginationData.currentPage;
        tableListCurrent = 1;
        InfoOfDirCurrent = 1;
        handleSearch(wordSearchParam.value);
    } else if (searchInfoType.value === 1) {
        tableListCurrent = paginationData.currentPage;
        searchCurrent = 1;
        InfoOfDirCurrent = 1;
        getTableList(tableListParam.value);
    } else {
        InfoOfDirCurrent = paginationData.currentPage;
        tableListCurrent = 1;
        searchCurrent = 1;
        getInfoOfDir();
    }
});
const loadFileTableData = () => {
    if (newDirDialogShow.value) {
        newDirDialogShow.value = false;
    }
    getDiskState();
    getInfoOfDir();
};
const getCollection = () => {
    request({
        url: window.jLmDiskHost + "/Services/Backup/Collection.ashx?r=" + Math.random(),
        method: "get",
    })
        .then((data: any) => {
            data.Succeed && data.Result ? (collectionList.value = data.Result) : ElNotify({ type: "warning", message: data.Message });
            if (!collectionList.value.length) {
                emptyText.value = "暂无文件";
            }
        })
        .finally(() => {
            tryClearCustomUrlParams(route);
        });
};
const getAuditCollection = () => {
    request({
        url: window.jLmDiskHost + "/Services/Backup/AuditCollection.ashx?r=" + Math.random(),
        method: "get",
    }).then((data: any) => {
        data.Succeed && data.Result ? (auditCollectionList.value = data.Result) : ElNotify({ type: "warning", message: data.Message });
        if (!auditCollectionList.value.length) {
            emptyText.value = "暂无文件";
        }
    });
};
const downLoadCollectionOrAuditCollectionItem = (row: IBackupOrAuditItem) => {
    if (isErp.value && fid.value === -2) {
        let Id = row.Id || "";
        downLoadBackupItemErp(Id)
    } else {
        globalExport(window.jLmDiskHost + `/Services/Backup/Download.ashx?name=${encodeURIComponent(row.FILE_NAME)}&appasid=${getGlobalToken()}`);
    }
};
const downLoadBackupItemErp = (Id: string) => {
    request({
        url: window.erpApiUrl + "/jxc_api/Backup/DownloadErp/" + Id,
        method: "post",
    }).then((res: any) => {
        if (res.StatusCode == 200) {
            globalExport(res.Data);
        } else {
            ElNotify({ 
                type: "warning", 
                message: "文件下载失败" 
            });
        }
    });
}
const deleteBackupOrAudit = (type: "backup" | "audit") => {
    const reallyRequestUrl = type === "backup" ? "DeleteBackup" : "DeleteAudit";
    ElConfirm(`确定删除${type === "backup" ? "备份" : "文件"}？`).then((r: any) => {
        let deleteTableList = type === "backup" ? backupMultipleSelection.value : auditMultipleSelection.value;
        const names = deleteTableList.map((item) => encodeURIComponent(item.FILE_NAME)).join("|");
        if (r) {
            request({
                url: window.jLmDiskHost + "/Services/Doc/" + reallyRequestUrl + ".ashx?r=" + Math.random(),
                method: "post",
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                data: { names },
            }).then((data: any) => {
                type === "backup" ? getCollection() : getAuditCollection();
                getDiskState();
                data.Succeed
                    ? ElNotify({ type: "success", message: `${type === "backup" ? "备份" : "审计"}文件删除成功` })
                    : ElNotify({
                          type: "warning",
                          message: data.Message || `${type === "backup" ? "备份" : "审计"}文件删除出现错误`,
                      });
            });
        }
    });
};
const fileHandleSelectionChange = (val: any) => (fileMultipleSelection.value = val);
const auditHandleSelectionChange = (val: any) => (auditMultipleSelection.value = val);
const backupHandleSelectionChange = (val: any) => (backupMultipleSelection.value = val);
const otherHandleSelectionChange = (val: any) => {
    fid.value === -2 ? backupHandleSelectionChange(val) : auditHandleSelectionChange(val);
};
// 新增1，编辑2，删除3
// 新增，编辑弹框显示
const handleEditClick = (e: Event, type: number, node: any, data: any) => {
    fid.value = data.id;
    e.preventDefault();
    e.stopPropagation();
    if (type === 1) {
        //新增
        newDirDialogShow.value = true;
        dirDialog.value?.editDataInit("", type);
    } else if (type === 2) {
        newDirDialogShow.value = true;

        dirDialog.value?.editDataInit(data.text, type);
    } else {
        ElConfirm("是否删除“" + data.text + "”文件夹及其下级文件夹？").then((r: boolean) => {
            if (r) {
                request({
                    url: window.jLmDiskHost + "/Services/Doc/DeleteDoc.ashx",
                    method: "post",
                    headers: { "Content-Type": "application/x-www-form-urlencoded" },
                    data: { fids: fid.value, appasid: getGlobalToken() },
                })
                    .then((data: any) => {
                        if (data.Succeed) {
                            ElNotify({ type: "success", message: `文件夹删除成功` });
                            fid.value = 0;
                            loadFileTableData();
                            window.dispatchEvent(new CustomEvent("reloadERecordDirList"));
                        } else {
                            ElNotify({ type: "warning", message: data.Message });
                        }
                    })
                    .catch(() => {
                        ElNotify({ type: "warning", message: "文件夹创建时发生异常" });
                    });
            }
        });
    }
};
// 刷新检验状态
const handleRefreshClick = (id: number) => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fid.value, appasid: getGlobalToken(), submitType: "Refresh", fid: id },
    }).then((data: any) => {
        if (data.Succeed) {
            ElNotify({ type: "success", message: `状态刷新成功` });
            loadFileTableData();
        } else {
            ElNotify({ type: "warning", message: data.Message });
        }
    });
};

const reloadSearchDate = () => {
    startDate.value = getUploadDate().START_DATE;
    endDate.value = getUploadDate().END_DATE;
};

const hasMounted = ref(false);
const searchOptions = ref();
const headerMulSeletVal = ref();
onMounted(() => {
    miniDate.value = useAccountSetStore().accountSet?.asStartDate || getUploadDate().START_DATE;
    isNewName.value = JSON.parse(localStorage.getItem("isNewName") || "false");
    loadFileTableData();
    handleGetFileData();
    hasMounted.value = true;
    nextTick(() => {
        searchOptions.value = mainTopBoxRef.value?.searchOptions;
        headerMulSeletVal.value = mainTopBoxRef.value?.headerMulSeletVal;
    })
});
onActivated(() => {
    if (route.query.catalog && decodeURIComponent(route.query.catalog as string) === "数据备份") {
        fid.value = -2;
        getCollection();
    } else if (hasMounted.value) {
        return;
    } else {
        if (searchInfoType.value === 2) {
            handleSearch(wordSearchParam.value);
        } else if (searchInfoType.value === 1) {
            getTableList(tableListParam.value);
        } else {
            getInfoOfDir();
        }
    }
});
onDeactivated(() => {
    hasMounted.value = false;
});
const activeName = ref<"list" | "file">("list");
const fileData = ref<Array<IProcessItem>>([]);
const hasProcess = computed(() => fileData.value.length > 0 && fileData.value.some((item) => item.progress !== 100));
const setModule = "ERcordFileSummary"
const fileColumns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 30, headerAlign: "center", align: "center" },
    {
        label: "档案编码",
        prop: "fileName",
        minWidth: 150,
        width: getColumnWidth(setModule, 'fileName'),
        align: "left",
        headerAlign: "left",
        formatter(_row, _column, value) {
            const match = value.match(/_(\d+)\.zip$/);
            if (!match || !match[1]) return "";
            return match[1];
        },
    },
    { slot: "Name"},
    {
        label: "文件大小",
        prop: "fileSize",
        minWidth: 100,
        width: getColumnWidth(setModule, 'fileSize'),
        align: "left",
        headerAlign: "left",
        formatter(_row, _column, value) {
            return sizeFormatter(value);
        },
    },
    { label: "归档日期", prop: "dateTime", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'dateTime') },
    { label: "归档人", prop: "creator", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'creator') },
    { slot: "operation" },
]);
const { debounce, cloneDeep } = getGlobalLodash();
const selectFileList = ref<Array<IProcessItem>>([]);
function fileSelectionChange(list: Array<IProcessItem>) {
    selectFileList.value = cloneDeep(list);
}
function handleProcessFn() {
    if (!checkPermission(["lemondiskarchive-canarchive"])) {
        ElNotify({ type: "warning", message: "亲，您没有这个功能权限哦~" });
        return;
    }
    if (hasProcess.value) {
        ElNotify({ type: "warning", message: "您有正在备份的归档文件，请稍后再试" });
        return;
    }
    const confirmMsg = "您已有临时的备份归档文件，继续备份归档将自动删除之前的临时文件，是否继续？";

    checkCanProcessAndHasTemporaryFile("archive", confirmMsg, beginBackup, checkSpace, notCanProcess);
}
function beginBackup() {
    const balanceSheetIsClass = window.localStorage.getItem("classificationSwitch") || "false";
    const cashFlowSheetIsClass = window.localStorage.getItem("cashclassificationSwitch") || "false";
    const params = {
        allowTemporaryFile: 1,
        isProcess: 1,
        balanceSheetIsClass,
        cashFlowSheetIsClass,
    };
    request({ url: "/api/Backup/BackupArchivePackage?" + getUrlSearchParams(params), method: "post" })
        .then((res: IResponseModel<IProcessItem>) => {
            if (res.state !== 1000 || !res.data) {
                useLoading().quitLoading();
                ElNotify({ type: "warning", message: res.msg || "归档失败，请稍后重试" });
                return;
            }
            fileData.value.unshift(res.data);
            checkInStack(res.data);
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "归档失败，请稍后重试" });
        });
}
const isThirdPart = computed(() => { return useThirdPartInfoStoreHook().isThirdPart });
function checkSpace() {
    request({ url: "/api/Backup/CheckSpace?fileType=archive", method: "post" }).then((r: IResponseModel<ICheckSpace>) => {
        if (r.state === 1000) {
            if (r.data.overflow) {
                if (!isThirdPart.value) {
                    const msg = window.isAccountingAgent
                        ? "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
                        : "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：";
                    tryShowPayDialog(1, "backup-archive", msg, "会计电子档案", beginBackup);
                    return;
                }
            }
        }
        beginBackup();
    });
}
function notCanProcess() {
    ElNotify({ type: "warning", message: "亲，存在正在归档的任务，请稍后重试哦~" });
}
const handleProcess = debounce(handleProcessFn);
let canDelete = true;
function handleBatchDelete() {
    if (!canDelete) return;
    if (selectFileList.value.length === 0) {
        ElNotify({ type: "warning", message: "请选择要删除的归档文件" });
        return;
    }
    ElAlert({ message: "是否删除选择的归档文件？" }).then((r: boolean) => {
        canDelete = true;
        if (!r) return;
        const data = selectFileList.value.map((item) => item.fileName);
        request({ url: "/api/Backup/ProcessBatchDeleteArchiveFile", method: "post", data })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: res.msg || "删除失败" });
                    return;
                }
                ElNotify({ type: "success", message: "删除成功" });
                handleGetFileData();
            })
            .finally(() => {
                canDelete = false;
            });
    });
}
function handleDelete(row: IProcessItem) {
    if (!canDelete) return;
    ElAlert({ message: "确定删除该档案文件吗？" }).then((r: boolean) => {
        if (!r) return;
        canDelete = false;
        request({ url: "/api/Backup/ProcessDeleteArchive?fileName=" + encodeURIComponent(row.fileName), method: "post" })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: res.msg || "删除失败" });
                    return;
                }
                ElNotify({ type: "success", message: "删除成功" });
                const index = fileData.value.findIndex((item) => item.fileName === row.fileName);
                fileData.value.splice(index, 1);
            })
            .finally(() => {
                canDelete = true;
            });
    });
}
function handleDownload(row: IProcessItem) {
    globalExport("/api/Backup/DownloadArchive?fileType=archive&fileName=" + encodeURIComponent(row.fileName));
}
function handleGetFileData() {
    if (!checkPermission(["lemondiskarchive-canview"])) return;
    request({ url: "/api/Backup/ArchiveList" }).then((res: IResponseModel<Array<IProcessItem>>) => {
        if (res.state !== 1000 || !res.data) {
            ElNotify({ type: "warning", message: res.msg || "获取归档文件失败" });
            return;
        }
        fileData.value = res.data;
        const inStack = fileData.value.find((item) => item.progress !== 100);
        inStack && checkInStack(inStack);
    });
}
function checkInStack(file: IProcessItem) {
    const timer = setTimeout(() => {
        request({ url: "/api/Backup/GetArchiveFileRecord?filename=" + encodeURIComponent(file.fileName), method: "post" })
            .then((res: IResponseModel<IProcessItem>) => {
                if (res.state !== 1000 || !res.data) {
                    useLoading().quitLoading();
                    ElNotify({ type: "warning", message: res.msg || "获取归档文件失败" });
                    const index = fileData.value.findIndex((item) => item.fileName === file.fileName);
                    index !== -1 && fileData.value.splice(index, 1);
                    return;
                }
                const row = fileData.value.find((item) => item.fileName === res.data.fileName);
                if (!row) return;
                row.fileSize = res.data.fileSize;
                row.progress = res.data.progress;
                tryClearTempFile(row);
                if (res.data.progress === 100) {
                    getDiskState();
                    useLoading().quitLoading();
                    return;
                }
                checkInStack(row);
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "获取归档文件失败" });
            })
            .finally(() => {
                clearTimeout(timer);
            });
    }, 5000);
}
function tryClearTempFile(data: IProcessItem) {
    if (data.progress == 100 && data.expiredDate) {
        for (let i = fileData.value.length - 1; i > 0; i--) {
            if (fileData.value[i].expiredDate) {
                fileData.value.splice(i, 1);
            }
        }
    }
}
function selectable(row: IProcessItem) {
    return row.progress === 100;
}

//表头字段模糊搜索
function isKeyOfIFSearchItem(key: string): key is keyof IFSearchItem {  
    return [
        'uploadPerson', 
        'uploadType', 
        'fileState',
        'documentType',
        'voucherState',
        'checkStates',
        'fileCategory',
        'matters',
        'fileName',
    ].includes(key);  
}
const filterSearchInfo:IFSearchItem = reactive({
    uploadPerson: "",
    uploadType: "",
    fileState: "-1",
    documentType: "",  
    voucherState: "-1",
    checkStates: [] as number[],
    fileCategory: "",
    matters: "",
    fileName: "",
});
function restFilterInfo() {
    filterSearchInfo.checkStates = [];
    filterSearchInfo.fileCategory = "";
    filterSearchInfo.uploadPerson = "";
    filterSearchInfo.uploadType = "";
    filterSearchInfo.fileState = "-1";
    filterSearchInfo.documentType = "";
    filterSearchInfo.voucherState = "-1";
    filterSearchInfo.matters = "";
    filterSearchInfo.fileName = "";
}
function filterSearch(prop: string, data: any) {
    if (isKeyOfIFSearchItem(prop)) { 
        if (typeof data === "string" || typeof data === "number") {
            (filterSearchInfo[prop] as string) = data.toString();  
            nextTick(() => {
                mainTopBoxRef.value?.executeFilter(true);
            })
        } else {
            if (Array.isArray(data) && data.length > 0) {
                const filteredData: number[] = data.filter((item): item is number => typeof item === 'number');  
                (filterSearchInfo[prop] as number[]) = filteredData;
                nextTick(() => {
                    mainTopBoxRef.value?.executeFilter(true);
                })
            }
        }
    } 
}
// 展开和收起右侧树标志
const flag = reactive({
    expandFlag: true,
    closeFlag: true
});
</script>

<style lang="less" scoped>
@import "@/style/ERecord/ERecord.less";
@import "@/style/SelfAdaption.less";
@import "@/style/RightTreeExpand.less";

@media (max-width: 1400px) {
    :deep(.larger-dropdown-button) {
        flex-shrink: 0;
        padding-left: 5px;
        padding-right: 25px;
    }
}
.content {
    width: 100% !important;
    height: 100% !important;
    :deep(.el-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__content {
            flex: 1;
            .el-tab-pane {
                height: 100%;
                .el-table__body .el-table__row .el-table__cell:nth-child(2) > .cell {
                    padding-left: 16px;
                }
                .lack-space-tip {
                    background-color: #fafafa;
                    & > .el-table__cell {
                        > .cell {
                            color: rgba(21, 21, 21, 0.45);
                        }
                        &:nth-child(2) {
                            > .cell {
                                &::before {
                                    content: " ";
                                    background-image: url("@/assets/Settings/temporary.svg");
                                    background-repeat: no-repeat;
                                    background-position: 0 0;
                                    background-size: 30px 30px;
                                    width: 30px;
                                    height: 30px;
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .main-content {
        height: 100%;
        .main-center.body-main {
            box-sizing: border-box;
            flex: 1;
            min-height: 0;
            height: auto;
            .body-right {
                position: relative;
                margin-left: 10px;
            }
            &.hidden-tree {
                display: flex;
                .body-left {
                    flex: 1;
                }
                .body-right {
                    width: 0;
                    border: 0;
                    margin-right: 10px;
                    .space-state {
                        display: none;
                    }
                }
            }
        }
        &.file {
            .main-center {
                flex: 1;
                overflow: hidden;
            }
        }
    }
}
.file-center {
    .table.paging-show .el-table .el-table__body-wrapper {
        padding-bottom: 0;
    }
}
.main-center {
    .file-table {
        width: 100%;
        display: flex;
        flex-direction: column;
        :deep(.el-table) {
            flex: 1;
            .el-scrollbar__view {
                height: 100%;
            }
        }
    }
}
.progress-container {
    display: flex;
    align-items: center;
    .progress-bar {
        width: 120px;
        height: 9px;
        border-radius: 5px;
        background-color: #e4e4e4;
        margin-right: 4px;
        display: flex;
        align-items: center;
        .progress-solid-bar {
            height: 9px;
            border-radius: 5px;
            background-color: #3385ff;
        }
    }
}
.loading-operator {
    display: flex;
    align-items: center;
    span.loading-icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url("@/assets/Icons/loading.svg") no-repeat;
        background-size: 100% 100%;
        animation: spin 3s linear infinite;
    }
}
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.empty {
    .blue {
        color: var(--link-color);
        cursor: pointer;
    }
}
body[erp] .content .main-content {
    height: 100%;
    :deep(.el-table.el-table--scrollable-x) {
        .el-table__header,
        .el-table__body {
            .el-table__row {
                &:hover {
                    background-color: var(--table-selected-color);
                }
            }
        }
        .el-table__body tr.el-table__row--striped td.el-table__cell {
            background-color: inherit;
        }
    }
}
</style>
