<template>
    <div class="tabs-pane-content">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <DatePicker
                    v-model:start-pid="searchInfo.startPid"
                    v-model:end-pid="searchInfo.endPid"
                    :clearable="true"
                    :disabled-date-start="disabledDateStart"
                    :disabled-date-end="disabledDateEnd"
                />
                <span class="ml-20">账户：</span>
                <CDAccountFilter
                    ref="CDAccountFilterRef"
                    v-model:accountOptions="accountOptions"
                    :options="options"
                    v-model:selectedList="selectedList"
                    :maxWidth="168"
                    @change-switch-status="changeSwitchStatus"
                />
                <span class="ml-20" v-if="fcList.length > 2">币别：</span>
                <CurrencyFilter
                    v-if="fcList.length > 2"
                    v-model:fcid="searchInfo.fcid" 
                    :fcList="fcList"
                ></CurrencyFilter>
                <a class="solid-button ml-10" @click="customSearch">查询</a>
            </div>
            <div class="main-tool-right">
                <a class="button mr-10" v-permission="['report-canprint']" @click="handlePrintOrExport('print')">打印</a>
                <a class="button mr-10" v-permission="['report-canexport']" @click="handlePrintOrExport('export')">导出</a>
                <a class="button" v-if="subscribeShow" @click="subscribe">报表订阅推送</a>
                <RefreshButton></RefreshButton>
            </div>
        </div>
        <div class="main-center">
            <Table
                :loading="loading"
                :columns="columns"
                :data="tableData"
                :page-is-show="true"
                :layout="paginationData.layout"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :currentPage="paginationData.currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @refresh="handleRerefresh"
                :show-overflow-tooltip="true"
                :tableName="setModule"
            >
                <template #name>
                    <el-table-column 
                        :label="type === 'PROJECT' ? '项目' : '部门'" 
                        min-width="178px" 
                        align="left" 
                        header-align="left"
                        prop="name"
                        :width="getColumnWidth(setModule, 'name')"
                    >
                        <template #default="scope">
                            <span v-if="!scope.row.name.startsWith('<a')">{{ scope.row.name }}</span>
                            <span class="link" @click="goToDetail(scope.row.name)" v-else>
                                {{ scope.row.name.replace(/<[^<>]+>/g, "") }}
                            </span>
                        </template>
                    </el-table-column>
                </template>
                <template #fcName>
                    <el-table-column 
                        label="币别" 
                        min-width="80" 
                        align="left" 
                        header-align="left"
                        prop="fc_name"
                        :width="getColumnWidth(setModule, 'fc_name')"
                    >
                        <template #default="scope">
                            <span>{{ getFcCode(scope.row.code, currentFcid, fcList) }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #income>
                    <el-table-column
                        label="收入总额"
                        :min-width="216"
                        align="right"
                        header-align="right"
                        prop="income_standard"
                        :width="getColumnWidth(setModule, 'income_standard')"
                    >
                        <template #default="scope">
                            <span>{{ formatMoney(scope.row.income_standard) }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #incomeFc>
                    <el-table-column label="收入总额" header-align="center">
                        <el-table-column
                            label="原币"
                            :min-width="90"
                            :width="getColumnWidth(setModule, 'incomeFc')"
                            align="right"
                            header-align="right"
                            prop="incomeFc"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="本币"
                            :min-width="90"
                            :width="getColumnWidth(setModule, 'income')"
                            align="right"
                            header-align="right"
                            prop="income"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
                <template #expenditure>
                    <el-table-column
                        label="支出总额"
                        :min-width="216"
                        align="right"
                        header-align="right"
                        prop="expend_standard"
                        :width="getColumnWidth(setModule, 'expend_standard')"
                    >
                        <template #default="scope">
                            <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #expenditureFc>
                    <el-table-column label="支出总额" header-align="center">
                        <el-table-column
                            label="原币"
                            :min-width="90"
                            :width="getColumnWidth(setModule, 'expendFc')"
                            align="right"
                            header-align="right"
                            prop="expendFc"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                            label="本币"
                            :min-width="90"
                            :width="getColumnWidth(setModule, 'expend')"
                            align="right"
                            header-align="right"
                            prop="expend"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </template>
            </Table>
        </div>
        <div class="main-bottom">
            <div style="color: var(--red)">提示：</div>
            <div class="mt-10">
                <span>内部转账，不计入{{ type === "PROJECT" ? "项目" : "部门" }}</span>
                <a v-permission="['transfer-canview']" class="link ml-10" @click="redirectToTransfer">点击查看</a>
            </div>
        </div>
    </div>
    <SubscribePushDialog ref="SubscribePushRef"></SubscribePushDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, nextTick } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getUrlSearchParams, globalWindowOpenPage, globalExport, globalPrint } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney } from "@/util/format";
import { checkReportTableEmpty, getATagParams, getSearchInfoCD, getFcCode } from "../utils";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

import type { IAAJournalTable, IDateInfo, IAAJournalBack, ISCurrcy } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { Option } from "@/components/SelectCheckbox/types";
import { dayjs } from "element-plus";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import SubscribePushDialog from "./SubscribePushDialog.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import CDAccountFilter from "@/views/Cashier/components/CDAccountFilter.vue";
import CurrencyFilter from "@/views/Cashier/components/CurrencyFilter.vue";

const accountSetStore = useAccountSetStore();

const props = defineProps<{ 
    options: IBankAccountItem[]; 
    dateInfo: IDateInfo; 
    type: "PROJECT" | "DEPARTMENT";
    fcList: ISCurrcy[];
}>();
const { 
    paginationData, 
    handleCurrentChange, 
    handleSizeChange, 
    handleRerefresh 
} = usePagination(props.type === "PROJECT" ? "Project" : "Department", true);

const emit = defineEmits<{
    (event: "change-switch-status", args: boolean): void;
}>();
const changeSwitchStatus = (val: boolean) => {
    emit("change-switch-status", val);
}

const subscribeShow = computed(() => {
    return !window.isErp && !window.isAccountingAgent && !window.isBossSystem && !useThirdPartInfoStoreHook().isThirdPart
});
const SubscribePushRef = ref();
function subscribe() {
    SubscribePushRef.value.initSubscribe();
}
const dateInfo = computed(() => props.dateInfo);
const accountOptions = ref<Option[]>([]);
const setModule = props.type === "PROJECT" ? "CashReportProject" : "CashReportDepartment";
const columns = ref<Array<IColumnProps>>([]);
const calcColumns = () => {
    const list:IColumnProps[] = [
        { 
            label: "编码", 
            prop: "aaNum", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 106,
            width: getColumnWidth(setModule, "aaNum") 
        },
        { slot: "name" },
        { slot: "income" },
        { slot: "expenditure" },
        { 
            label: "收入笔数", 
            prop: "incomeCount", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 135,
            width: getColumnWidth(setModule, "incomeCount")
        },
        { 
            label: "支出笔数", 
            prop: "expendCount", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 135,
            resizable: false 
        },
    ];
    if (searchInfo.fcid > 1) {
        list.splice(2, 0, { slot: "fcName" });
        list.forEach((item) => {
            if (item.slot === "income" || item.slot === "expenditure") {
                item.slot += "Fc";
            }
        });
    }
    columns.value = list;
}
const selectedList = ref<number[]>([]);
const tableData = ref<IAAJournalTable[]>([]);
const searchInfo = reactive({
    startPid: "",
    endPid: "",
    CD_ACC_IDS: "ALL",
    fcid: -1,
});
watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});

calcColumns();
const redirectToTransfer = () => globalWindowOpenPage("/Cashier/Transfer", "内部转账");
const loading = ref(false);
const currentFcid = ref(-1);
const getTableList = () => {
    const params = getRequestParams();
    loading.value = true;
    request({ url: "/api/CashierReport/PagingListForAA?" + getUrlSearchParams(params) })
        .then((res: IResponseModel<IAAJournalBack>) => {
            if (res.state == 1000) {
                const list = res.data.rows;
                paginationData.total = res.data.total;
                const pushTotal =
                    (paginationData.currentPage - 1) * paginationData.pageSize <= res.data.total &&
                    paginationData.currentPage * paginationData.pageSize >= res.data.total;
                if (res.data.totalRows && pushTotal) {
                    const totalRow = res.data.totalRows[0];
                    list.push(totalRow);
                }
                tableData.value = list;
            }
        })
        .finally(() => {
            loading.value = false;
            currentFcid.value = searchInfo.fcid;
        });
};
const customSearch = () => {
    calcColumns();
    paginationData.currentPage === 1 ? getTableList() : (paginationData.currentPage = 1);
};
const handlePrintOrExport = (type: "print" | "export") => {
    const length = tableData.value.length - 1;
    if (!checkReportTableEmpty(length, 1)) return;
    const params = getRequestParams();
    const urlPath = type === "print" ? "PrintForAA?" : "ExportForAA?";
    const url = "/api/CashierReport/" + urlPath + getUrlSearchParams(params);
    type === "print" ? globalPrint(url) : globalExport(url);
};
const isErp = ref(window.isErp);
const goToDetail = (name: string) => {
    const params = { 
        ...getATagParams(name),
        fcid: searchInfo.fcid,
        showDisabled: getShowDisabledAccount("cashReport"),
        ran: Math.random() 
    };
    const basePath = isErp.value ? "Journal" : props.type === "PROJECT" ? "ProjectJournal" : "DepartmentJournal";
    const title = props.type === "PROJECT" ? "项目日记账" : "部门日记账";
    globalWindowOpenPage("/Cashier/" + basePath + "?" + getUrlSearchParams(params), title);
};
const CDAccountFilterRef = ref();
const handleInit = () => {
    const { start, end } = dateInfo.value;
    searchInfo.startPid = start;
    searchInfo.endPid = end;
    nextTick(() => {
        CDAccountFilterRef.value?.handleInit();
        selectedList.value = accountOptions.value.map((i) => i.id);
        searchInfo.CD_ACC_IDS = getSearchInfoCD(selectedList.value, props.options);
        getTableList();
    });
};
defineExpose({ handleInit });

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    getTableList();
});

watch(selectedList, () => {
    searchInfo.CD_ACC_IDS = getSearchInfoCD(selectedList.value, props.options);
});

const getRequestParams = () => {
    return {
        tabType: props.type,
        startDate: searchInfo.startPid,
        endDate: searchInfo.endPid,
        cdAccIds: searchInfo.CD_ACC_IDS,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
        showDisableAccount: getShowDisabledAccount("cashReport"),
        fcid: searchInfo.fcid,
    };
};
function disabledDateStart(time: Date) {
    const endDate = dayjs(searchInfo.endPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    return time.getTime() > endDate || time.getTime() < asStartDate;
}
function disabledDateEnd(time: Date) {
    const startDate = dayjs(searchInfo.startPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    const minTime = Math.max(startDate, asStartDate);
    return time.getTime() < minTime;
}
</script>

<style scoped lang="less">
@import "@/style/Cashier/Report.less";
</style>
