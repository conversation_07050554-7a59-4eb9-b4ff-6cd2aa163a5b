<template>
    <div class="step-box">
        <div class="content-row mt-100">
            <div class="content-left width-six qr-left">
                <div>&nbsp;</div>
                <div class="tx-left">
                    <span class="label-bold">关注微信公众号【老板看账】</span>
                    <span class="label-color mt-10" style="display: block">微信扫码关注</span>
                </div>
            </div>
            <div class="content-right width-four mt-20">
                <img src="@/assets/Settings/boss-qrcode.png" class="boss-qrcode" alt="boss.png" />
            </div>
        </div>
        <div class="content-row tx-center bottom-operate">
            <a class="button mr-20" @click="reduceStage">上一步</a>
            <a class="button solid-button" @click="upgradeStage">已关注，下一步</a>
        </div>
    </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["reduce-stage", "upgrade-stage"]);
const reduceStage = () => emit("reduce-stage", 0);
const upgradeStage = () => emit("upgrade-stage", 1);
</script>

<style lang="less" scoped>
.step-box {
    min-height: 500px;
    position: relative;
    border-top: 1px solid #cdcdcd;
    overflow: hidden;
    padding: 0;
    font-size: 14px;
    .content-row {
        display: flex;
        overflow: hidden;
        &.bottom-operate {
            width: 100%;
            position: absolute;
            bottom: 50px;
            display: flex;
            justify-content: center;
            a {
                width: 150px;
                &.long {
                    padding: 0 15px;
                    width: auto;
                }
            }
        }
        .content-left {
            width: 610px;
            height: 54px;
            padding-right: 10px;
            margin-top: 80px;
            display: flex;
            div {
                flex: 1;
                &.tx-left {
                    height: 54px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    span {
                        text-align: start;
                        &.label-bold {
                            color: rgb(51, 51, 51);
                            font-size: 18px;
                            font-family: PingFangSC-Medium;
                            font-weight: bolder;
                        }
                    }
                }
            }
        }
        .content-right {
            width: 360px;
            height: 169px;
            display: flex;
            align-items: center;
        }
    }
}
</style>
