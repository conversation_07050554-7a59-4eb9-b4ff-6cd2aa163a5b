@import "../Functions.less";
.content {
    box-sizing: border-box;
    display: flex;
    height: 100%;
    flex-direction: column;
}
.main-content{
    height: 100%;
    flex:1;
}
.intro-container {
    flex-direction: column;
    align-items: center;
    display: flex;
    .intor-title {
        font-size: 24px;
        font-weight: 500;
        color: var(--font-color);
        line-height: 33px;
        margin-top: 69px;
    }
    .intor-sub-title {
        font-size: 20px;
        color: var(--font-color);
        line-height: 28px;
        margin-top: 16px;
    }
    .button {
        width: 160px !important;
        height: 36px;
        line-height: 36px;
        margin-top: 28px;
    }
    .intor-content-card {
        width: 560px;
        height: 248px;
        display: flex;
        justify-content: space-between;
        background-color: #f9fafc;
        margin-top: 80px;
        .card-left {
            margin-left: 60px;
            display: flex;
            flex-direction: column;
            .card-title {
                font-size: 20px;
                font-weight: 500;
                color: var(--font-color);
                line-height: 28px;
                margin-top: 40px;
                text-align: left;
            }
            .points-container {
                margin-top: 16px;
                .point-content {
                    display: flex;
                    align-items: center;
                    .point-icon {
                        width: 20px;
                        height: 20px;
                        margin-right: 8px;
                    }
                    span {
                        font-size: 16px;
                        color: #666666;
                        line-height: 22px;
                    }
                }
            }
        }
        .card-right {
            .card-pic {
                margin-top: 24px;
                margin-right: 24px;
                width: 260px;
                height: 200px;
            }
        }
    }
}
.step-toolbar-container {
    border-bottom: 1px solid var(--title-split-line);
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 6px;
    height: 71px;
    box-sizing: border-box;
    .step-split-line {
        background-color: #e6e9ed;
        width: 60px;
        height: 1px;
        margin-left: 12px;
        margin-right: 22px;
    }
    .step-tab {
        display: flex;
        align-items: center;
        .step-icon {
            height: 36px;
            width: 36px;
            margin-right: 10px;
            display: inline-block;
        }
        .step-title {
            font-size: var(--font-size);
            line-height: 22px;
            color: #666666;
        }
    }
}
.step-content-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-bottom: 40px;
    height: 100%;
    box-sizing: border-box;
    .step-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        .download-qr-code-btn {
            box-sizing: border-box;
            width: 127px;
            height: 30px;
            border: 1px solid #06c05f;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-right: 4px;
            cursor: pointer;
            outline: none;
            text-decoration: none;
        }
        &.step-one {
            .step-one-card {
                margin-top: 40px;
                width: 630px;
                height: 300px;
                background-color: #f9fafc;
                display: flex;
                justify-content: space-between;
                .card-left {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-left: 65px;
                    .card-title {
                        font-size: 16px;
                        line-height: 22px;
                        color: var(--font-size);
                        font-weight: 500;
                        margin-top: 29px;
                    }
                    .card-sub-title {
                        font-size: 16px;
                        line-height: 22px;
                        color: var(--font-size);
                        margin-top: 12px;
                    }
                    .qr-code {
                        width: 154px;
                        height: 154px;
                        margin-top: 12px;
                    }
                }
                .card-right {
                    .card-pic {
                        margin-top: 63px;
                        margin-right: 24px;
                        width: 260px;
                        height: 200px;
                    }
                }
            }
            .step-one-btns {
                margin-top: 34px;
                .button {
                    width: 140px;
                    height: 36px;
                    line-height: 34px;
                    border-color: var(--main-color);
                    color: var(--main-color);
                    &.solid-button {
                        line-height: 36px;
                        margin-left: 28px;
                        color: var(--white);
                    }
                }
            }
        }
        &.step-two {
            .step-two-card {
                margin-top: 40px;
                width: 630px;
                height: 320px;
                background-color: #f9fafc;
                display: flex;
                justify-content: space-between;
                .card-left {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    margin-left: 57px;
                    .card-title {
                        font-size: 16px;
                        line-height: 22px;
                        color: var(--font-size);
                        font-weight: 500;
                        margin-top: 24px;
                    }
                    .card-sub-title {
                        font-size: 16px;
                        line-height: 22px;
                        color: var(--font-size);
                        margin-top: 12px;
                    }
                    .qr-code {
                        width: 154px;
                        height: 154px;
                        margin-top: 12px;
                    }
                    .download-qr-code-btn {
                        margin-top: 8px;
                        .btn-icon {
                            width: 20px;
                            height: 20px;
                            margin-right: 4px;
                        }
                        span {
                            color: #06c05f;
                            line-height: 24px;
                            font-size: 14px;
                        }
                    }
                }
                .card-right {
                    .card-pic {
                        margin-top: 96px;
                        margin-right: 24px;
                        width: 260px;
                        height: 200px;
                    }
                }
            }
            .step-two-btns {
                margin-top: 40px;
                .button {
                    width: 140px;
                    height: 36px;
                    line-height: 34px;
                    border-color: var(--main-color);
                    color: var(--main-color);
                    &.solid-button {
                        line-height: 36px;
                        margin-left: 28px;
                        color: var(--white);
                    }
                }
            }
        }
        &.step-three {
            align-self: center;
            justify-content: center;
            flex-direction: row;
            align-items: stretch;
            height: 100%;
            .content-left {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                margin-top: 48px;
                width: 720px;
                .table-tool-bar {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    .tool-bar-left {
                        margin-top: 2px;
                        font-size: 16px;
                        line-height: 22px;
                        color: var(--black);
                    }
                    .tool-bar-right {
                        display: flex;
                        align-items: center;
                        .button.solid-button {
                            width: 72px;
                            height: 28px;
                            line-height: 28px;
                        }
                    }
                }
                .table-container {
                    margin-top: 16px;

                    :deep(.el-input__wrapper) {
                        padding: 0 1px;

                        .el-input__inner{
                            border-right: 0px;
                        }

                        .el-input__suffix{
                            .el-input__suffix-inner{
                                padding-right: 3px;
                            }
                        }
                    }
                    :deep(.el-table__empty-block) {
                        min-height: 0;
                    }
                    &.hidden-table-body {
                        :deep(.el-table__body-wrapper) {
                           display: none;
                        }
                    }
                }
            }
            .content-right {
                margin-left: 46px;
                display: flex;
                flex-direction: column;
                align-items: center;
                .qr-code {
                    width: 130px;
                    height: 130px;
                    margin-top: 36px;
                }
                .qr-code-desc {
                    font-size: 16px;
                    font-weight: 500;
                    color: var(--font-size);
                    line-height: 22px;
                    margin-top: 12px;
                    text-align: center;
                }
                .download-qr-code-btn {
                    margin-top: 12px;
                    .btn-icon {
                        width: 20px;
                        height: 20px;
                        margin-right: 4px;
                    }
                    span {
                        color: #06c05f;
                        line-height: 24px;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
.add-employee-container {
    padding-top: 20px;
    .buttons {
        padding: 10px;
        border-top: 1px solid var(--border-color);
        text-align: center;
        font-size: 0;
        margin-top: 20px;
    }
    .input-line {
        display: flex;
        align-items: center;
        padding: 0 32px;
        & + .input-line {
            margin-top: 20px;
        }
        .input-title {
            width: 100px;
            text-align: right;
            font-size: 14px;
            line-height: 30px;
            color: var(--font-color);
            margin-right: 10px;
        }
        .input-field {
            height: 30px;
            width: 330px;
            .detail-el-select(100%, 100%);
            > input {
                .detail-original-input(100%, 100%);
            }
        }
    }
}

.import-tip {
    font-size: 12px;
    color: #999999;
    line-height: 22px;
    margin-top: 12px;
    margin: 10px 0 20px 40px;
}

.select-employee-container {
    color: #404040;
    font-size: 12px;
    padding-top: 20px;
    .select-employee-toolbar {
        padding: 0 32px;
        display: flex;
        justify-content: flex-end;
        .search-controller {
            position: relative;
            > input {
                .detail-original-input(240px, 28px);
            }
            .search-submit {
                position: absolute;
                top: 6px;
                right: 8px;
                height: 16px;
                width: 15px;
                background: url("@/assets/MiniProgramBinding/search-icon.png") no-repeat;
                background-size: 15px 16px;
                cursor: pointer;
            }
        }
    }
    .select-employee-table {
        margin-top: 20px;
        padding: 0 32px;
        height: 316px;
        overflow: hidden;
        :deep(.el-table) {
            .cell {
                padding: 0 4px;
            }
        }
    }
    .buttons {
        padding: 10px;
        border-top: 1px solid var(--border-color);
        text-align: center;
        font-size: 0;
        margin-top: 20px;
    }
}

:deep(.el-table__row){
    .el-table__cell{
        padding: 5px 0;
    }
}