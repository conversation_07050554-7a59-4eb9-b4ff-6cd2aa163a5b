<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div style="height: 100%;">
                    <div class="title">模板列表</div>
                    <div class="main-content" ref="templateListRef">
                        <div :class="['main-top', 'main-tool-bar', 'space-between', { 'ml-10': !isErp }]">
                            <div class="main-tool-left">
                                <a v-permission="['vouchertemplate-canedit']" class="solid-button" @click="New">新建</a>
                                <a v-permission="['vouchertemplate-canimport']" class="button ml-10" @click="() => (importShow = true)"
                                    >导入</a
                                >
                                <a v-permission="['vouchertemplate-canexport']" class="button ml-10" @click="handleExport">导出</a>
                                <ErpRefreshButton></ErpRefreshButton>
                            </div>

                            <div class="main-tool-right">
                                <RefreshButton></RefreshButton>
                            </div>
                        </div>
                        <div class="split-line"></div>
                        <div class="voucher-head">
                            <div class="voucher-title" style="width: 226px">摘要</div>
                            <div class="voucher-title" style="width: 400px">科目</div>
                            <div class="voucher-title" style="width: 104px">借贷方向</div>
                            <div class="voucher-title" style="width: 104px; text-align: right">借方金额</div>
                            <div class="voucher-title" style="width: 104px; text-align: right">贷方金额</div>
                        </div>
                        <!-- <TablePagination
                            class="pagination"
                            :total="paginationData.total"
                            :current-page="paginationData.currentPage"
                            :page-size="paginationData.pageSize"
                            :page-sizes="paginationData.pageSizes"
                            @current-change="handleCurrentChange"
                            @size-change="handleSizeChange"
                            @refresh="handleRerefresh"
                            v-show="!isErp"
                        /> -->

                        <div class="voucher-list" v-loading="loading" element-loading-text="正在加载数据...">
                            <div v-if="emptyShow" class="none-data-text">暂无数据</div>
                            <el-scrollbar ref="scrollRef" :always="true" v-else @scroll="handleScroll">
                                <div :class="[{'virtual-list' : tableData.length > hasVirtualCount}]">
                                    <div
                                        class="voucher-list-item"
                                        v-for="rowItem in (tableData.length > hasVirtualCount ? virtuallyData : tableData)"
                                        :key="rowItem.vtId"
                                        @dblclick="editVoucherItem(rowItem)"
                                    >
                                        <div class="voucher-list-head">
                                            <span class="voucher-date float-l">模板名称：{{ rowItem.vtName }}</span>
                                            <span class="voucher-num float-l ml-20">模板类型：{{ rowItem.vtTypeName }}</span>
                                            <a
                                                v-permission="['vouchertemplate-canedit']"
                                                class="link hover-display float-l ml-20"
                                                @click="() => editVoucherItem(rowItem)"
                                                >修改</a
                                            >
                                            <a
                                                v-permission="['vouchertemplate-candelete']"
                                                class="link hover-display float-l ml-20"
                                                @click="() => DeleteVoucherItem(rowItem.vtId)"
                                                >删除</a
                                            >
                                        </div>
                                        <div class="voucher-list-lines">
                                            <div class="voucher-list-line" v-for="(item, index) in rowItem.voucherLines" :key="index">
                                                <div class="voucher-list-description" style="width: 226px">
                                                    <span> {{ item.description }}</span>
                                                    <span v-show="item.fcId && item.fcRate"
                                                        >&nbsp;({{ findCurrencyCode(item.fcId) }}{{ item.fcAmount ? item.fcAmount : "" }},&nbsp;汇率
                                                        <span :title="item.fcRate != formatClipPointNum(item.fcRate) ? item.fcRate : ''">{{
                                                            formatClipPointNum(item.fcRate)
                                                        }}</span>
                                                        )</span
                                                    >
                                                    <span v-show="item.quantity || item.price"
                                                        >&nbsp;(数量
                                                        <span
                                                            v-show="item.quantity"
                                                            :title="item.quantity != formatClipPointNum(item.quantity) ? item.quantity : ''"
                                                            >{{ formatClipPointNum(item.quantity) }}</span
                                                        >{{ item.measureUnit }},&nbsp;单价
                                                        <span
                                                            :title="item.price != formatClipPointNum(item.price) ? item.price : ''"
                                                            v-show="item.price"
                                                            >{{ formatClipPointNum(item.price) }}</span
                                                        >元)
                                                    </span>
                                                </div>
                                                <div class="voucher-list-asubName" style="width: 400px">{{ item.SubjectName }}</div>
                                                <div class="voucher-list-direction" style="width: 104px">
                                                    {{ item.direction === 1 ? "借" : item.direction === 2 ? "贷" : "" }}
                                                </div>
                                                <div class="voucher-list-debit" style="width: 104px; text-align: right">
                                                    {{ formatMoney(item.debit) }}
                                                </div>
                                                <div class="voucher-list-credit" style="width: 104px; text-align: right">
                                                    {{ formatMoney(item.credit) }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>               
                            </el-scrollbar>
                        </div>
                        <TablePagination
                            class="pagination"
                            :total="paginationData.total"
                            :current-page="paginationData.currentPage"
                            :page-size="paginationData.pageSize"
                            :page-sizes="paginationData.pageSizes"
                            @current-change="handleCurrentChange"
                            @size-change="handleSizeChange"
                            @refresh="handleRerefresh"
                        />
                    </div>
                </div>
            </template>
            <template #edit>
                <EditTemplate ref="editTemplateRef" @back-to-main="backToMain"> </EditTemplate>
            </template>
        </ContentSlider>
    </div>
    <ImportSingleFileDialog
        :importTitle="'导入凭证模板'"
        v-model:import-show="importShow"
        :importUrl="'/api/VoucherTemplate/Import'"
        :uploadSuccess="uploadSuccess"
        width="480px"
    >
        <template #download>
            <div>1.请选择下面任意一种方式导入凭证模板</div>
            <div class="mt-10 ml-10">(1)在凭证模板列表导出所需数据，确认后直接导入</div>
            <div class="mt-10 ml-10">
                (2)点击下载模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="downloadTemplate">下载模板</a>
            </div>
        </template>
        <template #import-content>
            <span>2.选择文件导入</span>
        </template>
    </ImportSingleFileDialog>
</template>

<script lang="ts">
export default {
    name: "VoucherTemplate",
};
</script>
<script setup lang="ts">
import { ref, watch, computed, onActivated, inject, onDeactivated, onMounted, onBeforeUnmount } from "vue";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney, digitUppercase, formatClipPointNum } from "@/util/format";
import { globalExport } from "@/util/url";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";

import TablePagination from "@/components/Table/TablePagination.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import EditTemplate from "./components/EditTemplate.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { getCurrencyApi } from "@/api/currency";
import type { ICurrency } from "@/api/currency";
import { getGlobalLodash } from "@/util/lodash";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { pageScrollKey } from "@/symbols";
import { getOffsetTop, getSize, getSEPos } from "@/components/Table/VirtualScroll";

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const isErp = ref(window.isErp);
const editTemplateRef = ref<InstanceType<typeof EditTemplate>>();
const slots = ["main", "edit"];
const currentSlot = ref("main");
const importShow = ref(false);
const tableData = ref<any[]>([]);
const loading = ref(false);
const templateListRef = ref();
const emptyShow = ref(false);
const _ = getGlobalLodash();
const pageScroll = inject(pageScrollKey);

const onlyKey = "onlyKey";
const selectKey = "SelectKey";
const mapData = ref<any[]>([]);

//虚拟滚动
const hasVirtualCount = 300;
const rowHeight = 105; //39 + 33*2
const virtuallyData = ref<any[]>([]);
function findScrollDom() {
    return scrollRef.value?.$el.querySelector(".el-scrollbar__wrap") as HTMLElement;
}
const sizes = ref<any>({});
const buffer = 500;
const start = ref(0);
const end = ref(0);
let timer: any = null;
const offsetMap = computed(() => {
    const res: any = {};
    let total = 0;
    for (let i = 0; i < tableData.value.length; i++) {
        const key = mapData.value[i][onlyKey];
        res[key] = total;
        const curSize = sizes.value[key];
        const size = typeof curSize === "number" ? curSize : rowHeight;
        total += size;
    }
    return res;
});
const renderVirtualTable = (shouldUpdate = true) => {
    updateSizes();
    calcRenderData();
    calcPosition();
    shouldUpdate && updatePosition();
};
const updateSizes = () => {
    const rows = scrollRef.value?.$el.querySelectorAll(".voucher-list-item") as unknown as any[];
    if (rows) {
        Array.from(rows).forEach((row, index) => {
            const item = virtuallyData.value[index];
            if (!item) return;
            const key = item[onlyKey];
            const offsetHeight = row.offsetHeight;
            if (sizes.value[key] !== offsetHeight) {
                sizes.value[key] = offsetHeight;
            }
        });
    }
};
const calcRenderData = () => {
    const scroller = findScrollDom();
    const thisTop = scroller.scrollTop - buffer;
    const thisBottom = scroller.scrollTop + scroller.offsetHeight + buffer;

    const result = getSEPos(thisTop, thisBottom, tableData.value, mapData.value, offsetMap.value, onlyKey);

    start.value = result.thisStart;
    end.value = result.thisEnd;
    virtuallyData.value = mapData.value.slice(result.thisStart, result.thisEnd + 1);
};
const calcBodyHeight = () => {
    const last = tableData.value.length - 1;
    const wrapHeight = getOffsetTop(last, mapData.value, offsetMap.value, onlyKey) + getSize(last, virtuallyData.value, sizes.value, onlyKey, rowHeight);
    const virtualBody = scrollRef.value?.$el.querySelector(".el-scrollbar__view") as HTMLElement;
    virtualBody && (virtualBody.style.height = wrapHeight + "px");
};
const calcPosition = () => {
    const offsetTop = getOffsetTop(start.value, mapData.value, offsetMap.value, onlyKey);
    const listBody = scrollRef.value?.$el.querySelector(".virtual-list") as HTMLElement;
    listBody && (listBody.style.transform = `translateY(${offsetTop}px)`);
    calcBodyHeight();
};
const updatePosition = () => {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
        timer && clearTimeout(timer);
        renderVirtualTable(false);
    }, 100);
};
// const getOffsetTop = (index: number) => {
//     const item = mapData.value[index];
//     if (item) return offsetMap.value[item[onlyKey]] || 0;
//     return 0;
// };
// const getSize = (index: number) => {
//     const item = virtuallyData.value[index];
//     if (item) {
//         const key = item[onlyKey];
//         return sizes.value[key] || rowHeight;
//     }
//     return rowHeight;
// };
const updateView = () => {
    renderVirtualTable();
};
const scrollTop = ref(0);
function handleScroll(e: any) {
    scrollTop.value = e.scrollTop;
    (tableData.value.length > hasVirtualCount) && updateView();
}
watch(
    () => tableData.value, 
    () => {
        mapData.value = tableData.value.map((item, index) => ({ 
            ...item, 
            [onlyKey]: index, 
            [selectKey]: false 
        }));
        updateView();
    }
)
function setScrollTop(top = 0) {
    scrollRef.value?.setScrollTop(top);
}
onActivated(() => {
    setScrollTop(scrollTop.value);
    pageScroll && pageScroll(scrollTop.value > 0, () => {
        scrollTop.value > 0 && setScrollTop(0);
    });
});
onDeactivated(() => {
    pageScroll && pageScroll(false, () => {});
});
onMounted(() => {
    window.addEventListener("resize", updateView);
});
onBeforeUnmount(() => {
    window.removeEventListener("resize", updateView);
});

const handleSearch = () => {
    loading.value = true;
    request({
        url: "/api/voucherTemplate/PagingList",
        method: "get",
        params: { PageIndex: paginationData.currentPage, PageSize: paginationData.pageSize },
    }).then((res: any) => {
        loading.value = false;
        paginationData.total = res.data.count;
        // localStorage.setItem("VoucherTemplateListSize", paginationData.pageSize.toString());

        const firstChangeList = res.data.data.map((item: any) => {
            item.voucherLines = item.voucherLines.map((line: any) => {
                line.SubjectName = line.asubCode + " " + line.asubName;
                return line;
            });
            return item;
        });
        const secondChangeList = firstChangeList.map((item: any) => {
            const VoucherList = item.voucherLines;
            const debitTotal = getSumData(VoucherList, "debit");
            const creditTotal = getSumData(VoucherList, "credit");
            item.voucherLines = [
                ...VoucherList,
                {
                    VEID: 0,
                    description: "合计",
                    SubjectName: digitUppercase(creditTotal),
                    debit: debitTotal,
                    credit: creditTotal,
                },
            ];
            return item;
        });
        tableData.value = secondChangeList;
        emptyShow.value = tableData.value.length === 0;
        _.debounce(() => {
            templateListRef.value?.scrollTo(0, 0);
        }, 200)();
    });
};

const DeleteVoucherItem = (vtid: number) => {
    ElConfirm("亲，确定要删除吗？").then((r: any) => {
        if (r) {
            if (tableData.value.length === 1) {
                paginationData.currentPage = paginationData.currentPage - 1;
            }
            request({
                url: "/api/voucherTemplate",
                method: "delete",
                params: { vtId: vtid },
            }).then((result: any) => {
                if (result.state === 1000) {
                    handleSearch();
                    ElNotify({ type: "success", message: "亲，删除成功啦！" });
                } else {
                    ElNotify({ type: "warning", message: "亲，删除失败啦！请联系侧边栏客服！" });
                }
            });
        }
    });
};
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    handleSearch();
    setScrollTop(0);
});
const downloadTemplate = () => {
    globalExport("/api/VoucherTemplate/ExportImport");
};
let currencyList: Array<ICurrency> = [];
getCurrencyApi().then((res) => {
    if (res.state === 1000) {
        currencyList = res.data;
    }
});
const findCurrencyCode = (currencyId: number) => {
    return currencyList.find((item) => item.id === currencyId)?.code;
};

const uploadSuccess = (res: IResponseModel<any>) => {
    if (res.state === 1000) {
        if (res.data.failed) {
            ElConfirm(
                `成功：${res.data.succeed}，失败：${res.data.failed}（科目为非明细科目或已停用、辅助核算不匹配、摘要超过256个字的凭证模板，将会被跳过！）`,
                true,
                () => {},
                "提示",
                undefined,
                true
            );
        } else {
            ElNotify({ type: "success", message: "导入成功！" });
        }
        importShow.value = false;
        handleSearch();
    } else {
        ElNotify({ type: "warning", message: "亲，请使用正确的导入模板导入！" });
    }
};

const handleExport = () => {
    globalExport("/api/VoucherTemplate/Export");
};

const New = () => {
    currentSlot.value = "edit";

    editTemplateRef.value?.initSubmitInfo("add", {});
};
const listScrollTop = ref(0);
const editVoucherItem = (rowItem: any) => {
    listScrollTop.value = (document.querySelector(".router-container") as HTMLElement).scrollTop;
    currentSlot.value = "edit";
    editTemplateRef.value?.initSubmitInfo("edit", rowItem);
    (document.querySelector(".router-container") as HTMLElement).scrollTop = 0;
};

const backToMain = () => {
    currentSlot.value = "main";
    handleSearch();
    setTimeout(() => {
        (document.querySelector(".router-container") as HTMLElement).scrollTop = listScrollTop.value;
    }, 1000);
};

handleSearch();

watch(paginationData, (newValue, oldValue) => {
    if (
        newValue.currentPage !== oldValue.currentPage ||
        newValue.pageSize !== oldValue.pageSize ||
        newValue.pageSize !== oldValue.pageSize
    ) {
        handleSearch();
    }
});
const getSumData = (row: any[], key: string): number => {
    return row.reduce((total, item) => {
        return total + item[key];
    }, 0);
};
const scrollRef = ref();
</script>

<style lang="less" scoped>
@import "@/style/Settings/VoucherTemplate.less";
@import "@/style/SelfAdaption.less";

.template-table-list {
    :deep(.custom-table tbody tr td .cell input[type="text"]) {
        border: none !important;
    }
}
.pagination.datagrid-pager {
    border: none;
}
.voucher-list-line:nth-child(even) {
    background-color: #f8f8f8;
}
.voucher-list-head {
    background-color: var(--table-title-color);
}
</style>
