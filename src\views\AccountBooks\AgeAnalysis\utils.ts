import { saveColWidth } from "@/components/ColumnSet/utils";
export const initAgeSetList = [
    {
        ageId: 1,
        title: "30天以内",
        days: 30,
    },
    {
        ageId: 2,
        title: "30-60天",
        days: 60,
    },
    {
        ageId: 3,
        title: "60-90天",
        days: 90,
    },
    {
        ageId: 4,
        title: "90-180天",
        days: 180,
    },
    {
        ageId: 5,
        title: "180-360天",
        days: 360,
    },
    {
        ageId: 6,
        title: "360-1080天",
        days: 1080,
    },
    {
        ageId: 7,
        title: "1080天以上",
        days: 0,
    },
];

export const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    const elements = document.querySelectorAll(`.${column.id} .cell`);
    let max = elements[0].scrollWidth;
    elements.forEach((item) => {
        if (item.scrollWidth > max) {
            max = item.scrollWidth;
        }
    });
    if (newWidth === oldWidth && column.width < max) {
        column.width = max + 16; //单元格左右有8内边距
    }
    //列宽拖动保存浏览器
    saveColWidth("AgeAnalysis", column.width, column.property);
};
