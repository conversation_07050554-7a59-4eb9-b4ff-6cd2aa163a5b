<template>
  <el-dialog
    v-model="dialogVisible"
    class="dialogDrag"
    :title="title"
    modal-class="modal-class custom-confirm"
    width="80%">
    <div
      v-dialog-drag
      class="dialog-content">
      <LMTable
        :max-height="400"
        ref="tableRef"
        :data="tableData"
        :columns="tableColumns"
        :header-cell-style="headerCellStyle"
        :span-method="spanMethod"
        row-key="code"
        border>
        <template #selection>
          <el-table-column
            type="selection"
            width="55"
            align="center"
            label="选择"
            :selectable="(row: TableItem) => !(parseFloat(row.amount) > 0)"></el-table-column>
        </template>
        <template #amount>
          <el-table-column
            prop="amount"
            label="本年累计金额"
            width="180"
            align="center">
            <template #default="{ row }">
              <checkable-input
                @change="() => handleInputChange(row)"
                v-model="row.amount"></checkable-input>
            </template>
          </el-table-column>
        </template>
      </LMTable>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmSelection">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, nextTick, watch } from "vue"
  import type { IColumnProps } from "@/components/Table/types.ts"
  import { he } from "element-plus/es/locales.mjs"
  import { max } from "lodash-es"

  // 表格数据项类型定义
  export interface TableItem {
    index: number
    code: string
    name: string
    amount: string
    selected?: boolean
    type?: string
    category?: string
    hasExtraInput?: boolean
  }

  // 跨行方法参数类型
  interface SpanMethodProps {
    rowIndex: number
    columnIndex: number
  }

  // 表头样式参数类型
  interface HeaderCellStyleProps {
    row: any
    column: any
    rowIndex: number
    columnIndex: number
  }

  const props = defineProps<{
    modelValue: boolean
    title: string
    data: TableItem[]
    selectedItems: TableItem[]
    showTypeColumn?: boolean
    showCategoryColumn?: boolean
    showSpanMethod?: boolean
  }>()

  const emit = defineEmits<{
    (e: "update:modelValue", value: boolean): void
    (e: "confirm", items: TableItem[], totalAmount: number): void
  }>()

  // 表格数据
  const tableData = ref<TableItem[]>([])

  // 表格列配置
  const tableColumns = computed(() => {
    const baseColumns: IColumnProps[] = [
      { slot: "selection", width: 55 },
      { prop: "index", label: "序号", width: 60, align: "center" },
      { prop: "code", label: "代码", width: 100, align: "center" },
    ]

    // 根据配置动态添加列
    if (props.showTypeColumn) {
      baseColumns.push({ prop: "type", label: "类型" })
    }

    if (props.showCategoryColumn) {
      baseColumns.push({ prop: "category", label: "减免项目类别" })
    }

    baseColumns.push({ prop: "name", label: "优惠事项" }, { slot: "amount" })

    return baseColumns
  })

  // 对话框可见性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit("update:modelValue", value),
  })

  // 表格引用
  const tableRef = ref<any>(null)

  // 更新表格选择状态
  const updateSelection = () => {
    if (!tableRef.value) return

    nextTick(() => {
      // 先清除所有选择
      tableRef.value.TableComponents?.clearSelection?.()

      // 根据选中状态设置行选择
      tableData.value.forEach((item) => {
        const isSelected = props.selectedItems.some((selectedItem) => selectedItem.code === item.code)
        if (isSelected || parseFloat(item.amount) > 0) {
          item.selected = true
          tableRef.value.TableComponents?.toggleRowSelection?.(item, true)
        } else {
          item.selected = false
        }
      })
    })
  }

  // 监听props.data变化，更新本地tableData
  watch(
    () => props.data,
    (newData) => {
      tableData.value = JSON.parse(JSON.stringify(newData))
      updateSelection()
    },
    { deep: true, immediate: true },
  )

  // 监听对话框打开状态，更新选择
  watch(
    () => dialogVisible.value,
    (newValue) => {
      if (newValue) {
        updateSelection()
      }
    },
    { immediate: true },
  )

  // 处理输入框变化
  const handleInputChange = (row: TableItem) => {
    const amount = parseFloat(row.amount || "0")

    // 如果金额大于0，自动勾选
    if (amount > 0) {
      row.selected = true
      nextTick(() => {
        tableRef.value.TableComponents?.toggleRowSelection?.(row, true)
      })
    }
  }

  // 表头样式处理
  const headerCellStyle = ({ row, column, rowIndex, columnIndex }: HeaderCellStyleProps) => {
    if (columnIndex === 4 && rowIndex === 0) {
      column.colSpan = 2
    }
    if (columnIndex === 3 && rowIndex === 0) {
      return { display: "none" }
    }
  }

  // 跨行处理方法（仅在免税收入、减计收入、加计扣除弹窗中使用）
  const spanMethod = ({ rowIndex, columnIndex }: SpanMethodProps) => {
    if (!props.showSpanMethod) {
      return { rowspan: 1, colspan: 1 }
    }

    // 根据实际业务逻辑处理单元格合并
    if (columnIndex === 3) {
      // 这里需要根据实际数据结构修改
      const exemptLength = tableData.value.filter((item) => item.type === "免税收入").length
      const reducedLength = tableData.value.filter((item) => item.type === "减计收入").length
      const additionLength = tableData.value.filter((item) => item.type === "加计扣除").length

      if (rowIndex === 0) {
        return { rowspan: exemptLength, colspan: 1 }
      } else if (rowIndex === exemptLength) {
        return { rowspan: reducedLength, colspan: 1 }
      } else if (rowIndex === reducedLength + exemptLength) {
        return { rowspan: additionLength, colspan: 1 }
      }
      return { rowspan: 0, colspan: 1 }
    }
    return { rowspan: 1, colspan: 1 }
  }

  // 确认选择
  const confirmSelection = () => {
    if (!tableRef.value) return

    // 获取选中行
    const selectedRows = tableRef.value.TableComponents?.getSelectionRows?.() || []

    // 计算总金额
    let totalAmount = 0
    selectedRows.forEach((item: TableItem) => {
      totalAmount += parseFloat(item.amount || "0")
    })

    // 发送确认事件
    emit("confirm", selectedRows as TableItem[], totalAmount)

    // 关闭对话框
    dialogVisible.value = false
  }
</script>

<style scoped>
  .dialog-footer {
    margin-top: 16px;
    text-align: right;
  }
</style>
