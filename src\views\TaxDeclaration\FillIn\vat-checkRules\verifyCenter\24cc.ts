// @ts-nocheck
/* eslint-disable */
function i(t) {
  i =
    typeof Symbol == 'function' && typeof Symbol.iterator == 'symbol'
      ? function (t) {
          return typeof t;
        }
      : function (t) {
          if (
            t &&
            typeof Symbol == 'function' &&
            t.constructor === Symbol &&
            t !== Symbol.prototype
          ) {
            return 'symbol';
          } else {
            return typeof t;
          }
        };
  return i(t);
}
var r;
var n;
var l = 9000000000000000;
var o = 1000000000;
var d = '0123456789abcdef';
var s =
  '2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058';
var c =
  '3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789';
var u = {
  precision: 20,
  rounding: 4,
  modulo: 1,
  toExpNeg: -7,
  toExpPos: 21,
  minE: -l,
  maxE: l,
  crypto: false,
};
var p = true;
var v = '[DecimalError] ';
var b = v + 'Invalid argument: ';
var f = v + 'Precision limit exceeded';
var g = v + 'crypto unavailable';
var h = '[object Decimal]';
var y = Math.floor;
var m = Math.pow;
var _ = /^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i;
var w = /^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i;
var x = /^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i;
var S = /^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;
var k = 10000000;
var O = 7;
var C = s.length - 1;
var j = c.length - 1;
var E = {
  toStringTag: h,
};
function A(t) {
  var e;
  var a;
  var i;
  var r = t.length - 1;
  var n = '';
  var l = t[0];
  if (r > 0) {
    n += l;
    e = 1;
    for (; e < r; e++) {
      i = t[e] + '';
      if ((a = O - i.length)) {
        n += z(a);
      }
      n += i;
    }
    l = t[e];
    if ((a = O - (i = l + '').length)) {
      n += z(a);
    }
  } else if (l === 0) {
    return '0';
  }
  while (l % 10 == 0) {
    l /= 10;
  }
  return n + l;
}
function T(t, e, a) {
  if (t !== ~~t || t < e || t > a) {
    throw Error(b + t);
  }
}
function P(t, e, a, i) {
  var r;
  var n;
  var l;
  var o;
  for (n = t[0]; n >= 10; n /= 10) {
    --e;
  }
  if (--e < 0) {
    e += O;
    r = 0;
  } else {
    r = Math.ceil((e + 1) / O);
    e %= O;
  }
  n = m(10, O - e);
  o = t[r] % n | 0;
  if (i == null) {
    if (e < 3) {
      if (e == 0) {
        o = (o / 100) | 0;
      } else if (e == 1) {
        o = (o / 10) | 0;
      }
      l =
        (a < 4 && o == 99999) || (a > 3 && o == 49999) || o == 50000 || o == 0;
    } else {
      l =
        (((a < 4 && o + 1 == n) || (a > 3 && o + 1 == n / 2)) &&
          ((t[r + 1] / n / 100) | 0) == m(10, e - 2) - 1) ||
        ((o == n / 2 || o == 0) && ((t[r + 1] / n / 100) | 0) == 0);
    }
  } else if (e < 4) {
    if (e == 0) {
      o = (o / 1000) | 0;
    } else if (e == 1) {
      o = (o / 100) | 0;
    } else if (e == 2) {
      o = (o / 10) | 0;
    }
    l = ((i || a < 4) && o == 9999) || (!i && a > 3 && o == 4999);
  } else {
    l =
      (((i || a < 4) && o + 1 == n) || (!i && a > 3 && o + 1 == n / 2)) &&
      ((t[r + 1] / n / 1000) | 0) == m(10, e - 3) - 1;
  }
  return l;
}
function D(t, e, a) {
  var i;
  var r;
  var n = [0];
  for (var l = 0, o = t.length; l < o; ) {
    for (r = n.length; r--; ) {
      n[r] *= e;
    }
    n[0] += d.indexOf(t.charAt(l++));
    i = 0;
    for (; i < n.length; i++) {
      if (n[i] > a - 1) {
        if (n[i + 1] === undefined) {
          n[i + 1] = 0;
        }
        n[i + 1] += (n[i] / a) | 0;
        n[i] %= a;
      }
    }
  }
  return n.reverse();
}
E.absoluteValue = E.abs = function () {
  var t = new this.constructor(this);
  if (t.s < 0) {
    t.s = 1;
  }
  return R(t);
};
E.ceil = function () {
  return R(new this.constructor(this), this.e + 1, 2);
};
E.clampedTo = E.clamp = function (t, e) {
  var a = this;
  var i = a.constructor;
  t = new i(t);
  e = new i(e);
  if (!t.s || !e.s) {
    return new i(NaN);
  }
  if (t.gt(e)) {
    throw Error(b + e);
  }
  if (a.cmp(t) < 0) {
    return t;
  } else if (a.cmp(e) > 0) {
    return e;
  } else {
    return new i(a);
  }
};
E.comparedTo = E.cmp = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n = this;
  var l = n.d;
  var o = (t = new n.constructor(t)).d;
  var d = n.s;
  var s = t.s;
  if (!l || !o) {
    if (d && s) {
      if (d !== s) {
        return d;
      } else if (l === o) {
        return 0;
      } else if (!l ^ (d < 0)) {
        return 1;
      } else {
        return -1;
      }
    } else {
      return NaN;
    }
  }
  if (!l[0] || !o[0]) {
    if (l[0]) {
      return d;
    } else if (o[0]) {
      return -s;
    } else {
      return 0;
    }
  }
  if (d !== s) {
    return d;
  }
  if (n.e !== t.e) {
    if ((n.e > t.e) ^ (d < 0)) {
      return 1;
    } else {
      return -1;
    }
  }
  e = 0;
  a = (i = l.length) < (r = o.length) ? i : r;
  for (; e < a; ++e) {
    if (l[e] !== o[e]) {
      if ((l[e] > o[e]) ^ (d < 0)) {
        return 1;
      } else {
        return -1;
      }
    }
  }
  if (i === r) {
    return 0;
  } else if ((i > r) ^ (d < 0)) {
    return 1;
  } else {
    return -1;
  }
};
E.cosine = E.cos = function () {
  var t;
  var e;
  var a = this;
  var i = a.constructor;
  if (a.d) {
    if (a.d[0]) {
      t = i.precision;
      e = i.rounding;
      i.precision = t + Math.max(a.e, a.sd()) + O;
      i.rounding = 1;
      a = (function (t, e) {
        var a;
        var i;
        var r;
        if (e.isZero()) {
          return e;
        }
        if ((i = e.d.length) < 32) {
          r = (1 / W(4, (a = Math.ceil(i / 3)))).toString();
        } else {
          a = 16;
          r = '2.3283064365386962890625e-10';
        }
        t.precision += a;
        e = K(t, 1, e.times(r), new t(1));
        for (var n = a; n--; ) {
          var l = e.times(e);
          e = l.times(l).minus(l).times(8).plus(1);
        }
        t.precision -= a;
        return e;
      })(i, Q(i, a));
      i.precision = t;
      i.rounding = e;
      return R(n == 2 || n == 3 ? a.neg() : a, t, e, true);
    } else {
      return new i(1);
    }
  } else {
    return new i(NaN);
  }
};
E.cubeRoot = E.cbrt = function () {
  var t;
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c = this;
  var u = c.constructor;
  if (!c.isFinite() || c.isZero()) {
    return new u(c);
  }
  p = false;
  if ((n = c.s * m(c.s * c, 1 / 3)) && Math.abs(n) != Infinity) {
    i = new u(n.toString());
  } else {
    a = A(c.d);
    if ((n = ((t = c.e) - a.length + 1) % 3)) {
      a += n == 1 || n == -2 ? '0' : '00';
    }
    n = m(a, 1 / 3);
    t = y((t + 1) / 3) - (t % 3 == (t < 0 ? -1 : 2));
    if (n == Infinity) {
      a = '5e' + t;
    } else {
      a = (a = n.toExponential()).slice(0, a.indexOf('e') + 1) + t;
    }
    (i = new u(a)).s = c.s;
  }
  l = (t = u.precision) + 3;
  while (true) {
    s = (d = (o = i).times(o).times(o)).plus(c);
    i = I(s.plus(c).times(o), s.plus(d), l + 2, 1);
    if (A(o.d).slice(0, l) === (a = A(i.d)).slice(0, l)) {
      if ((a = a.slice(l - 3, l + 1)) != '9999' && (r || a != '4999')) {
        if (!+a || (!+a.slice(1) && a.charAt(0) == '5')) {
          R(i, t + 1, 1);
          e = !i.times(i).times(i).eq(c);
        }
        break;
      }
      if (!r && (R(o, t + 1, 0), o.times(o).times(o).eq(c))) {
        i = o;
        break;
      }
      l += 4;
      r = 1;
    }
  }
  p = true;
  return R(i, t, u.rounding, e);
};
E.decimalPlaces = E.dp = function () {
  var t;
  var e = this.d;
  var a = NaN;
  if (e) {
    a = ((t = e.length - 1) - y(this.e / O)) * O;
    if ((t = e[t])) {
      for (; t % 10 == 0; t /= 10) {
        a--;
      }
    }
    if (a < 0) {
      a = 0;
    }
  }
  return a;
};
E.dividedBy = E.div = function (t) {
  return I(this, new this.constructor(t));
};
E.dividedToIntegerBy = E.divToInt = function (t) {
  var e = this.constructor;
  return R(I(this, new e(t), 0, 1, 1), e.precision, e.rounding);
};
E.equals = E.eq = function (t) {
  return this.cmp(t) === 0;
};
E.floor = function () {
  return R(new this.constructor(this), this.e + 1, 3);
};
E.greaterThan = E.gt = function (t) {
  return this.cmp(t) > 0;
};
E.greaterThanOrEqualTo = E.gte = function (t) {
  var e = this.cmp(t);
  return e == 1 || e === 0;
};
E.hyperbolicCosine = E.cosh = function () {
  var t;
  var e;
  var a;
  var i;
  var r;
  var n = this;
  var l = n.constructor;
  var o = new l(1);
  if (!n.isFinite()) {
    return new l(n.s ? Infinity : NaN);
  }
  if (n.isZero()) {
    return o;
  }
  a = l.precision;
  i = l.rounding;
  l.precision = a + Math.max(n.e, n.sd()) + 4;
  l.rounding = 1;
  if ((r = n.d.length) < 32) {
    e = (1 / W(4, (t = Math.ceil(r / 3)))).toString();
  } else {
    t = 16;
    e = '2.3283064365386962890625e-10';
  }
  n = K(l, 1, n.times(e), new l(1), true);
  var d;
  for (var s = t, c = new l(8); s--; ) {
    d = n.times(n);
    n = o.minus(d.times(c.minus(d.times(c))));
  }
  return R(n, (l.precision = a), (l.rounding = i), true);
};
E.hyperbolicSine = E.sinh = function () {
  var t;
  var e;
  var a;
  var i;
  var r = this;
  var n = r.constructor;
  if (!r.isFinite() || r.isZero()) {
    return new n(r);
  }
  e = n.precision;
  a = n.rounding;
  n.precision = e + Math.max(r.e, r.sd()) + 4;
  n.rounding = 1;
  if ((i = r.d.length) < 3) {
    r = K(n, 2, r, r, true);
  } else {
    t = (t = Math.sqrt(i) * 1.4) > 16 ? 16 : t | 0;
    r = K(n, 2, (r = r.times(1 / W(5, t))), r, true);
    var l;
    var o = new n(5);
    var d = new n(16);
    var s = new n(20);
    for (; t--; ) {
      l = r.times(r);
      r = r.times(o.plus(l.times(d.times(l).plus(s))));
    }
  }
  n.precision = e;
  n.rounding = a;
  return R(r, e, a, true);
};
E.hyperbolicTangent = E.tanh = function () {
  var t;
  var e;
  var a = this;
  var i = a.constructor;
  if (a.isFinite()) {
    if (a.isZero()) {
      return new i(a);
    } else {
      t = i.precision;
      e = i.rounding;
      i.precision = t + 7;
      i.rounding = 1;
      return I(a.sinh(), a.cosh(), (i.precision = t), (i.rounding = e));
    }
  } else {
    return new i(a.s);
  }
};
E.inverseCosine = E.acos = function () {
  var t;
  var e = this;
  var a = e.constructor;
  var i = e.abs().cmp(1);
  var r = a.precision;
  var n = a.rounding;
  if (i !== -1) {
    if (i === 0) {
      if (e.isNeg()) {
        return $(a, r, n);
      } else {
        return new a(0);
      }
    } else {
      return new a(NaN);
    }
  } else if (e.isZero()) {
    return $(a, r + 4, n).times(0.5);
  } else {
    a.precision = r + 6;
    a.rounding = 1;
    e = e.asin();
    t = $(a, r + 4, n).times(0.5);
    a.precision = r;
    a.rounding = n;
    return t.minus(e);
  }
};
E.inverseHyperbolicCosine = E.acosh = function () {
  var t;
  var e;
  var a = this;
  var i = a.constructor;
  if (a.lte(1)) {
    return new i(a.eq(1) ? 0 : NaN);
  } else if (a.isFinite()) {
    t = i.precision;
    e = i.rounding;
    i.precision = t + Math.max(Math.abs(a.e), a.sd()) + 4;
    i.rounding = 1;
    p = false;
    a = a.times(a).minus(1).sqrt().plus(a);
    p = true;
    i.precision = t;
    i.rounding = e;
    return a.ln();
  } else {
    return new i(a);
  }
};
E.inverseHyperbolicSine = E.asinh = function () {
  var t;
  var e;
  var a = this;
  var i = a.constructor;
  if (!a.isFinite() || a.isZero()) {
    return new i(a);
  } else {
    t = i.precision;
    e = i.rounding;
    i.precision = t + Math.max(Math.abs(a.e), a.sd()) * 2 + 6;
    i.rounding = 1;
    p = false;
    a = a.times(a).plus(1).sqrt().plus(a);
    p = true;
    i.precision = t;
    i.rounding = e;
    return a.ln();
  }
};
E.inverseHyperbolicTangent = E.atanh = function () {
  var t;
  var e;
  var a;
  var i;
  var r = this;
  var n = r.constructor;
  if (r.isFinite()) {
    if (r.e >= 0) {
      return new n(r.abs().eq(1) ? r.s / 0 : r.isZero() ? r : NaN);
    } else {
      t = n.precision;
      e = n.rounding;
      i = r.sd();
      if (Math.max(i, t) < -r.e * 2 - 1) {
        return R(new n(r), t, e, true);
      } else {
        n.precision = a = i - r.e;
        r = I(r.plus(1), new n(1).minus(r), a + t, 1);
        n.precision = t + 4;
        n.rounding = 1;
        r = r.ln();
        n.precision = t;
        n.rounding = e;
        return r.times(0.5);
      }
    }
  } else {
    return new n(NaN);
  }
};
E.inverseSine = E.asin = function () {
  var t;
  var e;
  var a;
  var i;
  var r = this;
  var n = r.constructor;
  if (r.isZero()) {
    return new n(r);
  } else {
    e = r.abs().cmp(1);
    a = n.precision;
    i = n.rounding;
    if (e !== -1) {
      if (e === 0) {
        (t = $(n, a + 4, i).times(0.5)).s = r.s;
        return t;
      } else {
        return new n(NaN);
      }
    } else {
      n.precision = a + 6;
      n.rounding = 1;
      r = r.div(new n(1).minus(r.times(r)).sqrt().plus(1)).atan();
      n.precision = a;
      n.rounding = i;
      return r.times(2);
    }
  }
};
E.inverseTangent = E.atan = function () {
  var t;
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s = this;
  var c = s.constructor;
  var u = c.precision;
  var v = c.rounding;
  if (s.isFinite()) {
    if (s.isZero()) {
      return new c(s);
    }
    if (s.abs().eq(1) && u + 4 <= j) {
      (l = $(c, u + 4, v).times(0.25)).s = s.s;
      return l;
    }
  } else {
    if (!s.s) {
      return new c(NaN);
    }
    if (u + 4 <= j) {
      (l = $(c, u + 4, v).times(0.5)).s = s.s;
      return l;
    }
  }
  c.precision = o = u + 10;
  c.rounding = 1;
  t = a = Math.min(28, (o / O + 2) | 0);
  for (; t; --t) {
    s = s.div(s.times(s).plus(1).sqrt().plus(1));
  }
  p = false;
  e = Math.ceil(o / O);
  i = 1;
  d = s.times(s);
  l = new c(s);
  r = s;
  while (t !== -1) {
    r = r.times(d);
    n = l.minus(r.div((i += 2)));
    r = r.times(d);
    if ((l = n.plus(r.div((i += 2)))).d[e] !== undefined) {
      for (t = e; l.d[t] === n.d[t] && t--; );
    }
  }
  if (a) {
    l = l.times(2 << (a - 1));
  }
  p = true;
  return R(l, (c.precision = u), (c.rounding = v), true);
};
E.isFinite = function () {
  return !!this.d;
};
E.isInteger = E.isInt = function () {
  return !!this.d && y(this.e / O) > this.d.length - 2;
};
E.isNaN = function () {
  return !this.s;
};
E.isNegative = E.isNeg = function () {
  return this.s < 0;
};
E.isPositive = E.isPos = function () {
  return this.s > 0;
};
E.isZero = function () {
  return !!this.d && this.d[0] === 0;
};
E.lessThan = E.lt = function (t) {
  return this.cmp(t) < 0;
};
E.lessThanOrEqualTo = E.lte = function (t) {
  return this.cmp(t) < 1;
};
E.logarithm = E.log = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s = this;
  var c = s.constructor;
  var u = c.precision;
  var v = c.rounding;
  if (t == null) {
    t = new c(10);
    e = true;
  } else {
    a = (t = new c(t)).d;
    if (t.s < 0 || !a || !a[0] || t.eq(1)) {
      return new c(NaN);
    }
    e = t.eq(10);
  }
  a = s.d;
  if (s.s < 0 || !a || !a[0] || s.eq(1)) {
    return new c(a && !a[0] ? -Infinity : s.s != 1 ? NaN : a ? 0 : Infinity);
  }
  if (e) {
    if (a.length > 1) {
      n = true;
    } else {
      for (r = a[0]; r % 10 == 0; ) {
        r /= 10;
      }
      n = r !== 1;
    }
  }
  p = false;
  l = q(s, (o = u + 5));
  i = e ? F(c, o + 10) : q(t, o);
  if (P((d = I(l, i, o, 1)).d, (r = u), v)) {
    do {
      l = q(s, (o += 10));
      i = e ? F(c, o + 10) : q(t, o);
      d = I(l, i, o, 1);
      if (!n) {
        if (+A(d.d).slice(r + 1, r + 15) + 1 == 100000000000000) {
          d = R(d, u + 1, 0);
        }
        break;
      }
    } while (P(d.d, (r += 10), v));
  }
  p = true;
  return R(d, u, v);
};
E.minus = E.sub = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c;
  var u;
  var v;
  var b = this;
  var f = b.constructor;
  t = new f(t);
  if (!b.d || !t.d) {
    if (b.s && t.s) {
      if (b.d) {
        t.s = -t.s;
      } else {
        t = new f(t.d || b.s !== t.s ? b : NaN);
      }
    } else {
      t = new f(NaN);
    }
    return t;
  }
  if (b.s != t.s) {
    t.s = -t.s;
    return b.plus(t);
  }
  s = b.d;
  v = t.d;
  o = f.precision;
  d = f.rounding;
  if (!s[0] || !v[0]) {
    if (v[0]) {
      t.s = -t.s;
    } else {
      if (!s[0]) {
        return new f(d === 3 ? -0 : 0);
      }
      t = new f(b);
    }
    if (p) {
      return R(t, o, d);
    } else {
      return t;
    }
  }
  a = y(t.e / O);
  c = y(b.e / O);
  s = s.slice();
  if ((n = c - a)) {
    if ((u = n < 0)) {
      e = s;
      n = -n;
      l = v.length;
    } else {
      e = v;
      a = c;
      l = s.length;
    }
    if (n > (i = Math.max(Math.ceil(o / O), l) + 2)) {
      n = i;
      e.length = 1;
    }
    e.reverse();
    i = n;
    while (i--) {
      e.push(0);
    }
    e.reverse();
  } else {
    if ((u = (i = s.length) < (l = v.length))) {
      l = i;
    }
    i = 0;
    for (; i < l; i++) {
      if (s[i] != v[i]) {
        u = s[i] < v[i];
        break;
      }
    }
    n = 0;
  }
  if (u) {
    e = s;
    s = v;
    v = e;
    t.s = -t.s;
  }
  l = s.length;
  i = v.length - l;
  for (; i > 0; --i) {
    s[l++] = 0;
  }
  for (i = v.length; i > n; ) {
    if (s[--i] < v[i]) {
      for (r = i; r && s[--r] === 0; ) {
        s[r] = k - 1;
      }
      --s[r];
      s[i] += k;
    }
    s[i] -= v[i];
  }
  while (s[--l] === 0) {
    s.pop();
  }
  for (; s[0] === 0; s.shift()) {
    --a;
  }
  if (s[0]) {
    t.d = s;
    t.e = N(s, a);
    if (p) {
      return R(t, o, d);
    } else {
      return t;
    }
  } else {
    return new f(d === 3 ? -0 : 0);
  }
};
E.modulo = E.mod = function (t) {
  var e;
  var a = this;
  var i = a.constructor;
  t = new i(t);
  if (!a.d || !t.s || (t.d && !t.d[0])) {
    return new i(NaN);
  } else if (!t.d || (a.d && !a.d[0])) {
    return R(new i(a), i.precision, i.rounding);
  } else {
    p = false;
    if (i.modulo == 9) {
      (e = I(a, t.abs(), 0, 3, 1)).s *= t.s;
    } else {
      e = I(a, t, 0, i.modulo, 1);
    }
    e = e.times(t);
    p = true;
    return a.minus(e);
  }
};
E.naturalExponential = E.exp = function () {
  return H(this);
};
E.naturalLogarithm = E.ln = function () {
  return q(this);
};
E.negated = E.neg = function () {
  var t = new this.constructor(this);
  t.s = -t.s;
  return R(t);
};
E.plus = E.add = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c;
  var u = this;
  var v = u.constructor;
  t = new v(t);
  if (!u.d || !t.d) {
    if (u.s && t.s) {
      if (!u.d) {
        t = new v(t.d || u.s === t.s ? u : NaN);
      }
    } else {
      t = new v(NaN);
    }
    return t;
  }
  if (u.s != t.s) {
    t.s = -t.s;
    return u.minus(t);
  }
  s = u.d;
  c = t.d;
  o = v.precision;
  d = v.rounding;
  if (!s[0] || !c[0]) {
    if (!c[0]) {
      t = new v(u);
    }
    if (p) {
      return R(t, o, d);
    } else {
      return t;
    }
  }
  n = y(u.e / O);
  i = y(t.e / O);
  s = s.slice();
  if ((r = n - i)) {
    if (r < 0) {
      a = s;
      r = -r;
      l = c.length;
    } else {
      a = c;
      i = n;
      l = s.length;
    }
    if (r > (l = (n = Math.ceil(o / O)) > l ? n + 1 : l + 1)) {
      r = l;
      a.length = 1;
    }
    a.reverse();
    while (r--) {
      a.push(0);
    }
    a.reverse();
  }
  if ((l = s.length) - (r = c.length) < 0) {
    r = l;
    a = c;
    c = s;
    s = a;
  }
  e = 0;
  while (r) {
    e = ((s[--r] = s[r] + c[r] + e) / k) | 0;
    s[r] %= k;
  }
  if (e) {
    s.unshift(e);
    ++i;
  }
  l = s.length;
  while (s[--l] == 0) {
    s.pop();
  }
  t.d = s;
  t.e = N(s, i);
  if (p) {
    return R(t, o, d);
  } else {
    return t;
  }
};
E.precision = E.sd = function (t) {
  var e;
  var a = this;
  if (t !== undefined && t !== !!t && t !== 1 && t !== 0) {
    throw Error(b + t);
  }
  if (a.d) {
    e = M(a.d);
    if (t && a.e + 1 > e) {
      e = a.e + 1;
    }
  } else {
    e = NaN;
  }
  return e;
};
E.round = function () {
  var t = this;
  var e = t.constructor;
  return R(new e(t), t.e + 1, e.rounding);
};
E.sine = E.sin = function () {
  var t;
  var e;
  var a = this;
  var i = a.constructor;
  if (a.isFinite()) {
    if (a.isZero()) {
      return new i(a);
    } else {
      t = i.precision;
      e = i.rounding;
      i.precision = t + Math.max(a.e, a.sd()) + O;
      i.rounding = 1;
      a = (function (t, e) {
        var a;
        var i = e.d.length;
        if (i < 3) {
          if (e.isZero()) {
            return e;
          } else {
            return K(t, 2, e, e);
          }
        }
        a = Math.sqrt(i) * 1.4;
        a = a > 16 ? 16 : a | 0;
        e = e.times(1 / W(5, a));
        e = K(t, 2, e, e);
        var r;
        var n = new t(5);
        var l = new t(16);
        var o = new t(20);
        while (a--) {
          r = e.times(e);
          e = e.times(n.plus(r.times(l.times(r).minus(o))));
        }
        return e;
      })(i, Q(i, a));
      i.precision = t;
      i.rounding = e;
      return R(n > 2 ? a.neg() : a, t, e, true);
    }
  } else {
    return new i(NaN);
  }
};
E.squareRoot = E.sqrt = function () {
  var t;
  var e;
  var a;
  var i;
  var r;
  var n;
  var l = this;
  var o = l.d;
  var d = l.e;
  var s = l.s;
  var c = l.constructor;
  if (s !== 1 || !o || !o[0]) {
    return new c(!s || (s < 0 && (!o || o[0])) ? NaN : o ? l : Infinity);
  }
  p = false;
  if ((s = Math.sqrt(+l)) == 0 || s == Infinity) {
    if (((e = A(o)).length + d) % 2 == 0) {
      e += '0';
    }
    s = Math.sqrt(e);
    d = y((d + 1) / 2) - (d < 0 || d % 2);
    if (s == Infinity) {
      e = '5e' + d;
    } else {
      e = (e = s.toExponential()).slice(0, e.indexOf('e') + 1) + d;
    }
    i = new c(e);
  } else {
    i = new c(s.toString());
  }
  a = (d = c.precision) + 3;
  while (true) {
    i = (n = i).plus(I(l, n, a + 2, 1)).times(0.5);
    if (A(n.d).slice(0, a) === (e = A(i.d)).slice(0, a)) {
      if ((e = e.slice(a - 3, a + 1)) != '9999' && (r || e != '4999')) {
        if (!+e || (!+e.slice(1) && e.charAt(0) == '5')) {
          R(i, d + 1, 1);
          t = !i.times(i).eq(l);
        }
        break;
      }
      if (!r && (R(n, d + 1, 0), n.times(n).eq(l))) {
        i = n;
        break;
      }
      a += 4;
      r = 1;
    }
  }
  p = true;
  return R(i, d, c.rounding, t);
};
E.tangent = E.tan = function () {
  var t;
  var e;
  var a = this;
  var i = a.constructor;
  if (a.isFinite()) {
    if (a.isZero()) {
      return new i(a);
    } else {
      t = i.precision;
      e = i.rounding;
      i.precision = t + 10;
      i.rounding = 1;
      (a = a.sin()).s = 1;
      a = I(a, new i(1).minus(a.times(a)).sqrt(), t + 10, 0);
      i.precision = t;
      i.rounding = e;
      return R(n == 2 || n == 4 ? a.neg() : a, t, e, true);
    }
  } else {
    return new i(NaN);
  }
};
E.times = E.mul = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c = this;
  var u = c.constructor;
  var v = c.d;
  var b = (t = new u(t)).d;
  t.s *= c.s;
  if (!v || !v[0] || !b || !b[0]) {
    return new u(
      !t.s || (v && !v[0] && !b) || (b && !b[0] && !v)
        ? NaN
        : v && b
        ? t.s * 0
        : t.s / 0,
    );
  }
  a = y(c.e / O) + y(t.e / O);
  if ((d = v.length) < (s = b.length)) {
    n = v;
    v = b;
    b = n;
    l = d;
    d = s;
    s = l;
  }
  n = [];
  i = l = d + s;
  while (i--) {
    n.push(0);
  }
  for (i = s; --i >= 0; ) {
    e = 0;
    r = d + i;
    while (r > i) {
      o = n[r] + b[i] * v[r - i - 1] + e;
      n[r--] = o % k | 0;
      e = (o / k) | 0;
    }
    n[r] = (n[r] + e) % k | 0;
  }
  while (!n[--l]) {
    n.pop();
  }
  if (e) {
    ++a;
  } else {
    n.shift();
  }
  t.d = n;
  t.e = N(n, a);
  if (p) {
    return R(t, u.precision, u.rounding);
  } else {
    return t;
  }
};
E.toBinary = function (t, e) {
  return Y(this, 2, t, e);
};
E.toDecimalPlaces = E.toDP = function (t, e) {
  var a = this;
  var i = a.constructor;
  a = new i(a);
  if (t === undefined) {
    return a;
  } else {
    T(t, 0, o);
    if (e === undefined) {
      e = i.rounding;
    } else {
      T(e, 0, 8);
    }
    return R(a, t + a.e + 1, e);
  }
};
E.toExponential = function (t, e) {
  var a;
  var i = this;
  var r = i.constructor;
  if (t === undefined) {
    a = L(i, true);
  } else {
    T(t, 0, o);
    if (e === undefined) {
      e = r.rounding;
    } else {
      T(e, 0, 8);
    }
    a = L((i = R(new r(i), t + 1, e)), true, t + 1);
  }
  if (i.isNeg() && !i.isZero()) {
    return '-' + a;
  } else {
    return a;
  }
};
E.toFixed = function (t, e) {
  var a;
  var i;
  var r = this;
  var n = r.constructor;
  if (t === undefined) {
    a = L(r);
  } else {
    T(t, 0, o);
    if (e === undefined) {
      e = n.rounding;
    } else {
      T(e, 0, 8);
    }
    a = L((i = R(new n(r), t + r.e + 1, e)), false, t + i.e + 1);
  }
  if (r.isNeg() && !r.isZero()) {
    return '-' + a;
  } else {
    return a;
  }
};
E.toFraction = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c;
  var u;
  var v;
  var f = this;
  var g = f.d;
  var h = f.constructor;
  if (!g) {
    return new h(f);
  }
  s = a = new h(1);
  i = d = new h(0);
  l = (n = (e = new h(i)).e = M(g) - f.e - 1) % O;
  e.d[0] = m(10, l < 0 ? O + l : l);
  if (t == null) {
    t = n > 0 ? e : s;
  } else {
    if (!(o = new h(t)).isInt() || o.lt(s)) {
      throw Error(b + o);
    }
    t = o.gt(e) ? (n > 0 ? e : s) : o;
  }
  p = false;
  o = new h(A(g));
  c = h.precision;
  h.precision = n = g.length * O * 2;
  while (((u = I(o, e, 0, 1, 1)), (r = a.plus(u.times(i))).cmp(t) != 1)) {
    a = i;
    i = r;
    r = s;
    s = d.plus(u.times(r));
    d = r;
    r = e;
    e = o.minus(u.times(r));
    o = r;
  }
  r = I(t.minus(a), i, 0, 1, 1);
  d = d.plus(r.times(s));
  a = a.plus(r.times(i));
  d.s = s.s = f.s;
  v =
    I(s, i, n, 1).minus(f).abs().cmp(I(d, a, n, 1).minus(f).abs()) < 1
      ? [s, i]
      : [d, a];
  h.precision = c;
  p = true;
  return v;
};
E.toHexadecimal = E.toHex = function (t, e) {
  return Y(this, 16, t, e);
};
E.toNearest = function (t, e) {
  var a = this;
  var i = a.constructor;
  a = new i(a);
  if (t == null) {
    if (!a.d) {
      return a;
    }
    t = new i(1);
    e = i.rounding;
  } else {
    t = new i(t);
    if (e === undefined) {
      e = i.rounding;
    } else {
      T(e, 0, 8);
    }
    if (!a.d) {
      if (t.s) {
        return a;
      } else {
        return t;
      }
    }
    if (!t.d) {
      t.s &&= a.s;
      return t;
    }
  }
  if (t.d[0]) {
    p = false;
    a = I(a, t, 0, e, 1).times(t);
    p = true;
    R(a);
  } else {
    t.s = a.s;
    a = t;
  }
  return a;
};
E.toNumber = function () {
  return +this;
};
E.toOctal = function (t, e) {
  return Y(this, 8, t, e);
};
E.toPower = E.pow = function (t) {
  var e;
  var a;
  var i;
  var r;
  var n;
  var l;
  var o = this;
  var d = o.constructor;
  var s = +(t = new d(t));
  if (!o.d || !t.d || !o.d[0] || !t.d[0]) {
    return new d(m(+o, s));
  }
  if ((o = new d(o)).eq(1)) {
    return o;
  }
  i = d.precision;
  n = d.rounding;
  if (t.eq(1)) {
    return R(o, i, n);
  }
  if (
    (e = y(t.e / O)) >= t.d.length - 1 &&
    (a = s < 0 ? -s : s) <= 9007199254740991
  ) {
    r = B(d, o, a, i);
    if (t.s < 0) {
      return new d(1).div(r);
    } else {
      return R(r, i, n);
    }
  }
  if ((l = o.s) < 0) {
    if (e < t.d.length - 1) {
      return new d(NaN);
    }
    if ((t.d[e] & 1) == 0) {
      l = 1;
    }
    if (o.e == 0 && o.d[0] == 1 && o.d.length == 1) {
      o.s = l;
      return o;
    }
  }
  if (
    (e =
      (a = m(+o, s)) != 0 && isFinite(a)
        ? new d(a + '').e
        : y(s * (Math.log('0.' + A(o.d)) / Math.LN10 + o.e + 1))) >
      d.maxE + 1 ||
    e < d.minE - 1
  ) {
    return new d(e > 0 ? l / 0 : 0);
  } else {
    p = false;
    d.rounding = o.s = 1;
    a = Math.min(12, (e + '').length);
    if ((r = H(t.times(q(o, i + a)), i)).d && P((r = R(r, i + 5, 1)).d, i, n)) {
      e = i + 10;
      if (
        +A((r = R(H(t.times(q(o, e + a)), e), e + 5, 1)).d).slice(
          i + 1,
          i + 15,
        ) +
          1 ==
        100000000000000
      ) {
        r = R(r, i + 1, 0);
      }
    }
    r.s = l;
    p = true;
    d.rounding = n;
    return R(r, i, n);
  }
};
E.toPrecision = function (t, e) {
  var a;
  var i = this;
  var r = i.constructor;
  if (t === undefined) {
    a = L(i, i.e <= r.toExpNeg || i.e >= r.toExpPos);
  } else {
    T(t, 1, o);
    if (e === undefined) {
      e = r.rounding;
    } else {
      T(e, 0, 8);
    }
    a = L((i = R(new r(i), t, e)), t <= i.e || i.e <= r.toExpNeg, t);
  }
  if (i.isNeg() && !i.isZero()) {
    return '-' + a;
  } else {
    return a;
  }
};
E.toSignificantDigits = E.toSD = function (t, e) {
  var a = this.constructor;
  if (t === undefined) {
    t = a.precision;
    e = a.rounding;
  } else {
    T(t, 1, o);
    if (e === undefined) {
      e = a.rounding;
    } else {
      T(e, 0, 8);
    }
  }
  return R(new a(this), t, e);
};
E.toString = function () {
  var t = this;
  var e = t.constructor;
  var a = L(t, t.e <= e.toExpNeg || t.e >= e.toExpPos);
  if (t.isNeg() && !t.isZero()) {
    return '-' + a;
  } else {
    return a;
  }
};
E.truncated = E.trunc = function () {
  return R(new this.constructor(this), this.e + 1, 1);
};
E.valueOf = E.toJSON = function () {
  var t = this;
  var e = t.constructor;
  var a = L(t, t.e <= e.toExpNeg || t.e >= e.toExpPos);
  if (t.isNeg()) {
    return '-' + a;
  } else {
    return a;
  }
};
var I = (function () {
  function t(t, e, a) {
    var i;
    var r = 0;
    var n = t.length;
    for (t = t.slice(); n--; ) {
      i = t[n] * e + r;
      t[n] = i % a | 0;
      r = (i / a) | 0;
    }
    if (r) {
      t.unshift(r);
    }
    return t;
  }
  function e(t, e, a, i) {
    var r;
    var n;
    if (a != i) {
      n = a > i ? 1 : -1;
    } else {
      for (r = n = 0; r < a; r++) {
        if (t[r] != e[r]) {
          n = t[r] > e[r] ? 1 : -1;
          break;
        }
      }
    }
    return n;
  }
  function a(t, e, a, i) {
    var r = 0;
    for (; a--; ) {
      t[a] -= r;
      r = t[a] < e[a] ? 1 : 0;
      t[a] = r * i + t[a] - e[a];
    }
    while (!t[0] && t.length > 1) {
      t.shift();
    }
  }
  return function (i, n, l, o, d, s) {
    var c;
    var u;
    var p;
    var v;
    var b;
    var f;
    var g;
    var h;
    var m;
    var _;
    var w;
    var x;
    var S;
    var C;
    var j;
    var E;
    var A;
    var T;
    var P;
    var D;
    var I = i.constructor;
    var L = i.s == n.s ? 1 : -1;
    var N = i.d;
    var F = n.d;
    if (!N || !N[0] || !F || !F[0]) {
      return new I(
        i.s && n.s && (N ? !F || N[0] != F[0] : F)
          ? (N && N[0] == 0) || !F
            ? L * 0
            : L / 0
          : NaN,
      );
    }
    if (s) {
      b = 1;
      u = i.e - n.e;
    } else {
      s = k;
      b = O;
      u = y(i.e / b) - y(n.e / b);
    }
    P = F.length;
    A = N.length;
    _ = (m = new I(L)).d = [];
    p = 0;
    for (; F[p] == (N[p] || 0); p++);
    if (F[p] > (N[p] || 0)) {
      u--;
    }
    if (l == null) {
      C = l = I.precision;
      o = I.rounding;
    } else {
      C = d ? l + (i.e - n.e) + 1 : l;
    }
    if (C < 0) {
      _.push(1);
      f = true;
    } else {
      C = (C / b + 2) | 0;
      p = 0;
      if (P == 1) {
        v = 0;
        F = F[0];
        C++;
        for (; (p < A || v) && C--; p++) {
          j = v * s + (N[p] || 0);
          _[p] = (j / F) | 0;
          v = j % F | 0;
        }
        f = v || p < A;
      } else {
        if ((v = (s / (F[0] + 1)) | 0) > 1) {
          F = t(F, v, s);
          N = t(N, v, s);
          P = F.length;
          A = N.length;
        }
        E = P;
        x = (w = N.slice(0, P)).length;
        while (x < P) {
          w[x++] = 0;
        }
        (D = F.slice()).unshift(0);
        T = F[0];
        if (F[1] >= s / 2) {
          ++T;
        }
        do {
          v = 0;
          if ((c = e(F, w, P, x)) < 0) {
            S = w[0];
            if (P != x) {
              S = S * s + (w[1] || 0);
            }
            if ((v = (S / T) | 0) > 1) {
              if (v >= s) {
                v = s - 1;
              }
              if (
                (c = e((g = t(F, v, s)), w, (h = g.length), (x = w.length))) ==
                1
              ) {
                v--;
                a(g, P < h ? D : F, h, s);
              }
            } else {
              if (v == 0) {
                c = v = 1;
              }
              g = F.slice();
            }
            if ((h = g.length) < x) {
              g.unshift(0);
            }
            a(w, g, x, s);
            if (c == -1 && (c = e(F, w, P, (x = w.length))) < 1) {
              v++;
              a(w, P < x ? D : F, x, s);
            }
            x = w.length;
          } else if (c === 0) {
            v++;
            w = [0];
          }
          _[p++] = v;
          if (c && w[0]) {
            w[x++] = N[E] || 0;
          } else {
            w = [N[E]];
            x = 1;
          }
        } while ((E++ < A || w[0] !== undefined) && C--);
        f = w[0] !== undefined;
      }
      if (!_[0]) {
        _.shift();
      }
    }
    if (b == 1) {
      m.e = u;
      r = f;
    } else {
      p = 1;
      v = _[0];
      for (; v >= 10; v /= 10) {
        p++;
      }
      m.e = p + u * b - 1;
      R(m, d ? l + m.e + 1 : l, o, f);
    }
    return m;
  };
})();
function R(t, e, a, i) {
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c;
  var u;
  var v;
  var b = t.constructor;
  t: if (e != null) {
    if (!(u = t.d)) {
      return t;
    }
    r = 1;
    o = u[0];
    for (; o >= 10; o /= 10) {
      r++;
    }
    if ((n = e - r) < 0) {
      n += O;
      l = e;
      d = ((c = u[(v = 0)]) / m(10, r - l - 1)) % 10 | 0;
    } else if ((v = Math.ceil((n + 1) / O)) >= (o = u.length)) {
      if (!i) {
        break t;
      }
      while (o++ <= v) {
        u.push(0);
      }
      c = d = 0;
      r = 1;
      l = (n %= O) - O + 1;
    } else {
      c = o = u[v];
      r = 1;
      for (; o >= 10; o /= 10) {
        r++;
      }
      d = (l = (n %= O) - O + r) < 0 ? 0 : (c / m(10, r - l - 1)) % 10 | 0;
    }
    i =
      i ||
      e < 0 ||
      u[v + 1] !== undefined ||
      (l < 0 ? c : c % m(10, r - l - 1));
    s =
      a < 4
        ? (d || i) && (a == 0 || a == (t.s < 0 ? 3 : 2))
        : d > 5 ||
          (d == 5 &&
            (a == 4 ||
              i ||
              (a == 6 &&
                (n > 0 ? (l > 0 ? c / m(10, r - l) : 0) : u[v - 1]) % 10 & 1) ||
              a == (t.s < 0 ? 8 : 7)));
    if (e < 1 || !u[0]) {
      u.length = 0;
      if (s) {
        e -= t.e + 1;
        u[0] = m(10, (O - (e % O)) % O);
        t.e = -e || 0;
      } else {
        u[0] = t.e = 0;
      }
      return t;
    }
    if (n == 0) {
      u.length = v;
      o = 1;
      v--;
    } else {
      u.length = v + 1;
      o = m(10, O - n);
      u[v] = l > 0 ? ((c / m(10, r - l)) % m(10, l) | 0) * o : 0;
    }
    if (s) {
      while (true) {
        if (v == 0) {
          n = 1;
          l = u[0];
          for (; l >= 10; l /= 10) {
            n++;
          }
          l = u[0] += o;
          o = 1;
          for (; l >= 10; l /= 10) {
            o++;
          }
          if (n != o) {
            t.e++;
            if (u[0] == k) {
              u[0] = 1;
            }
          }
          break;
        }
        u[v] += o;
        if (u[v] != k) {
          break;
        }
        u[v--] = 0;
        o = 1;
      }
    }
    for (n = u.length; u[--n] === 0; ) {
      u.pop();
    }
  }
  if (p) {
    if (t.e > b.maxE) {
      t.d = null;
      t.e = NaN;
    } else if (t.e < b.minE) {
      t.e = 0;
      t.d = [0];
    }
  }
  return t;
}
function L(t, e, a) {
  if (!t.isFinite()) {
    return G(t);
  }
  var i;
  var r = t.e;
  var n = A(t.d);
  var l = n.length;
  if (e) {
    if (a && (i = a - l) > 0) {
      n = n.charAt(0) + '.' + n.slice(1) + z(i);
    } else if (l > 1) {
      n = n.charAt(0) + '.' + n.slice(1);
    }
    n = n + (t.e < 0 ? 'e' : 'e+') + t.e;
  } else if (r < 0) {
    n = '0.' + z(-r - 1) + n;
    if (a && (i = a - l) > 0) {
      n += z(i);
    }
  } else if (r >= l) {
    n += z(r + 1 - l);
    if (a && (i = a - r - 1) > 0) {
      n = n + '.' + z(i);
    }
  } else {
    if ((i = r + 1) < l) {
      n = n.slice(0, i) + '.' + n.slice(i);
    }
    if (a && (i = a - l) > 0) {
      if (r + 1 === l) {
        n += '.';
      }
      n += z(i);
    }
  }
  return n;
}
function N(t, e) {
  var a = t[0];
  for (e *= O; a >= 10; a /= 10) {
    e++;
  }
  return e;
}
function F(t, e, a) {
  if (e > C) {
    p = true;
    if (a) {
      t.precision = a;
    }
    throw Error(f);
  }
  return R(new t(s), e, 1, true);
}
function $(t, e, a) {
  if (e > j) {
    throw Error(f);
  }
  return R(new t(c), e, a, true);
}
function M(t) {
  var e = t.length - 1;
  var a = e * O + 1;
  if ((e = t[e])) {
    for (; e % 10 == 0; e /= 10) {
      a--;
    }
    for (e = t[0]; e >= 10; e /= 10) {
      a++;
    }
  }
  return a;
}
function z(t) {
  var e = '';
  for (; t--; ) {
    e += '0';
  }
  return e;
}
function B(t, e, a, i) {
  var r;
  var n = new t(1);
  var l = Math.ceil(i / O + 4);
  for (p = false; ; ) {
    if (a % 2 && X((n = n.times(e)).d, l)) {
      r = true;
    }
    if ((a = y(a / 2)) === 0) {
      a = n.d.length - 1;
      if (r && n.d[a] === 0) {
        ++n.d[a];
      }
      break;
    }
    X((e = e.times(e)).d, l);
  }
  p = true;
  return n;
}
function V(t) {
  return t.d[t.d.length - 1] & 1;
}
function U(t, e, a) {
  var i;
  var r = new t(e[0]);
  for (var n = 0; ++n < e.length; ) {
    if (!(i = new t(e[n])).s) {
      r = i;
      break;
    }
    if (r[a](i)) {
      r = i;
    }
  }
  return r;
}
function H(t, e) {
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s = 0;
  var c = 0;
  var u = 0;
  var v = t.constructor;
  var b = v.rounding;
  var f = v.precision;
  if (!t.d || !t.d[0] || t.e > 17) {
    return new v(
      t.d
        ? t.d[0]
          ? t.s < 0
            ? 0
            : Infinity
          : 1
        : t.s
        ? t.s < 0
          ? 0
          : t
        : NaN,
    );
  }
  if (e == null) {
    p = false;
    d = f;
  } else {
    d = e;
  }
  o = new v(0.03125);
  while (t.e > -2) {
    t = t.times(o);
    u += 5;
  }
  d += i = ((Math.log(m(2, u)) / Math.LN10) * 2 + 5) | 0;
  a = n = l = new v(1);
  v.precision = d;
  while (true) {
    n = R(n.times(t), d, 1);
    a = a.times(++c);
    if (A((o = l.plus(I(n, a, d, 1))).d).slice(0, d) === A(l.d).slice(0, d)) {
      for (r = u; r--; ) {
        l = R(l.times(l), d, 1);
      }
      if (e != null) {
        v.precision = f;
        return l;
      }
      if (!(s < 3) || !P(l.d, d - i, b, s)) {
        return R(l, (v.precision = f), b, (p = true));
      }
      v.precision = d += 10;
      a = n = o = new v(1);
      c = 0;
      s++;
    }
    l = o;
  }
}
function q(t, e) {
  var a;
  var i;
  var r;
  var n;
  var l;
  var o;
  var d;
  var s;
  var c;
  var u;
  var v;
  var b = 1;
  var f = t;
  var g = f.d;
  var h = f.constructor;
  var y = h.rounding;
  var m = h.precision;
  if (f.s < 0 || !g || !g[0] || (!f.e && g[0] == 1 && g.length == 1)) {
    return new h(g && !g[0] ? -Infinity : f.s != 1 ? NaN : g ? 0 : f);
  }
  if (e == null) {
    p = false;
    c = m;
  } else {
    c = e;
  }
  h.precision = c += 10;
  i = (a = A(g)).charAt(0);
  if (!(Math.abs((n = f.e)) < 1500000000000000)) {
    s = F(h, c + 2, m).times(n + '');
    f = q(new h(i + '.' + a.slice(1)), c - 10).plus(s);
    h.precision = m;
    if (e == null) {
      return R(f, m, y, (p = true));
    } else {
      return f;
    }
  }
  while ((i < 7 && i != 1) || (i == 1 && a.charAt(1) > 3)) {
    i = (a = A((f = f.times(t)).d)).charAt(0);
    b++;
  }
  n = f.e;
  if (i > 1) {
    f = new h('0.' + a);
    n++;
  } else {
    f = new h(i + '.' + a.slice(1));
  }
  u = f;
  d = l = f = I(f.minus(1), f.plus(1), c, 1);
  v = R(f.times(f), c, 1);
  r = 3;
  while (true) {
    l = R(l.times(v), c, 1);
    if (
      A((s = d.plus(I(l, new h(r), c, 1))).d).slice(0, c) === A(d.d).slice(0, c)
    ) {
      d = d.times(2);
      if (n !== 0) {
        d = d.plus(F(h, c + 2, m).times(n + ''));
      }
      d = I(d, new h(b), c, 1);
      if (e != null) {
        h.precision = m;
        return d;
      }
      if (!P(d.d, c - 10, y, o)) {
        return R(d, (h.precision = m), y, (p = true));
      }
      h.precision = c += 10;
      s = l = f = I(u.minus(1), u.plus(1), c, 1);
      v = R(f.times(f), c, 1);
      r = o = 1;
    }
    d = s;
    r += 2;
  }
}
function G(t) {
  return String((t.s * t.s) / 0);
}
function J(t, e) {
  var a;
  var i;
  var r;
  if ((a = e.indexOf('.')) > -1) {
    e = e.replace('.', '');
  }
  if ((i = e.search(/e/i)) > 0) {
    if (a < 0) {
      a = i;
    }
    a += +e.slice(i + 1);
    e = e.substring(0, i);
  } else if (a < 0) {
    a = e.length;
  }
  i = 0;
  for (; e.charCodeAt(i) === 48; i++);
  for (r = e.length; e.charCodeAt(r - 1) === 48; --r);
  if ((e = e.slice(i, r))) {
    r -= i;
    t.e = a = a - i - 1;
    t.d = [];
    i = (a + 1) % O;
    if (a < 0) {
      i += O;
    }
    if (i < r) {
      if (i) {
        t.d.push(+e.slice(0, i));
      }
      r -= O;
      while (i < r) {
        t.d.push(+e.slice(i, (i += O)));
      }
      e = e.slice(i);
      i = O - e.length;
    } else {
      i -= r;
    }
    while (i--) {
      e += '0';
    }
    t.d.push(+e);
    if (p) {
      if (t.e > t.constructor.maxE) {
        t.d = null;
        t.e = NaN;
      } else if (t.e < t.constructor.minE) {
        t.e = 0;
        t.d = [0];
      }
    }
  } else {
    t.e = 0;
    t.d = [0];
  }
  return t;
}
function K(t, e, a, i, r) {
  var n;
  var l;
  var o;
  var d;
  var s = t.precision;
  var c = Math.ceil(s / O);
  p = false;
  d = a.times(a);
  o = new t(i);
  while (true) {
    l = I(o.times(d), new t(e++ * e++), s, 1);
    o = r ? i.plus(l) : i.minus(l);
    i = I(l.times(d), new t(e++ * e++), s, 1);
    if ((l = o.plus(i)).d[c] !== undefined) {
      for (n = c; l.d[n] === o.d[n] && n--; );
      if (n == -1) {
        break;
      }
    }
    n = o;
    o = i;
    i = l;
    l = n;
  }
  p = true;
  l.d.length = c + 1;
  return l;
}
function W(t, e) {
  var a = t;
  for (; --e; ) {
    a *= t;
  }
  return a;
}
function Q(t, e) {
  var a;
  var i = e.s < 0;
  var r = $(t, t.precision, 1);
  var l = r.times(0.5);
  if ((e = e.abs()).lte(l)) {
    n = i ? 4 : 1;
    return e;
  }
  if ((a = e.divToInt(r)).isZero()) {
    n = i ? 3 : 2;
  } else {
    if ((e = e.minus(a.times(r))).lte(l)) {
      n = V(a) ? (i ? 2 : 3) : i ? 4 : 1;
      return e;
    }
    n = V(a) ? (i ? 1 : 4) : i ? 3 : 2;
  }
  return e.minus(r).abs();
}
function Y(t, e, a, i) {
  var n;
  var l;
  var s;
  var c;
  var u;
  var p;
  var v;
  var b;
  var f;
  var g = t.constructor;
  var h = a !== undefined;
  if (h) {
    T(a, 1, o);
    if (i === undefined) {
      i = g.rounding;
    } else {
      T(i, 0, 8);
    }
  } else {
    a = g.precision;
    i = g.rounding;
  }
  if (t.isFinite()) {
    if (h) {
      n = 2;
      if (e == 16) {
        a = a * 4 - 3;
      } else if (e == 8) {
        a = a * 3 - 2;
      }
    } else {
      n = e;
    }
    if ((s = (v = L(t)).indexOf('.')) >= 0) {
      v = v.replace('.', '');
      (f = new g(1)).e = v.length - s;
      f.d = D(L(f), 10, n);
      f.e = f.d.length;
    }
    l = u = (b = D(v, 10, n)).length;
    while (b[--u] == 0) {
      b.pop();
    }
    if (b[0]) {
      if (s < 0) {
        l--;
      } else {
        (t = new g(t)).d = b;
        t.e = l;
        b = (t = I(t, f, a, i, 0, n)).d;
        l = t.e;
        p = r;
      }
      s = b[a];
      c = n / 2;
      p = p || b[a + 1] !== undefined;
      p =
        i < 4
          ? (s !== undefined || p) && (i === 0 || i === (t.s < 0 ? 3 : 2))
          : s > c ||
            (s === c &&
              (i === 4 ||
                p ||
                (i === 6 && b[a - 1] & 1) ||
                i === (t.s < 0 ? 8 : 7)));
      b.length = a;
      if (p) {
        while (++b[--a] > n - 1) {
          b[a] = 0;
          if (!a) {
            ++l;
            b.unshift(1);
          }
        }
      }
      for (u = b.length; !b[u - 1]; --u);
      s = 0;
      v = '';
      for (; s < u; s++) {
        v += d.charAt(b[s]);
      }
      if (h) {
        if (u > 1) {
          if (e == 16 || e == 8) {
            s = e == 16 ? 4 : 3;
            --u;
            for (; u % s; u++) {
              v += '0';
            }
            for (u = (b = D(v, n, e)).length; !b[u - 1]; --u);
            s = 1;
            v = '1.';
            for (; s < u; s++) {
              v += d.charAt(b[s]);
            }
          } else {
            v = v.charAt(0) + '.' + v.slice(1);
          }
        }
        v = v + (l < 0 ? 'p' : 'p+') + l;
      } else if (l < 0) {
        while (++l) {
          v = '0' + v;
        }
        v = '0.' + v;
      } else if (++l > u) {
        for (l -= u; l--; ) {
          v += '0';
        }
      } else if (l < u) {
        v = v.slice(0, l) + '.' + v.slice(l);
      }
    } else {
      v = h ? '0p+0' : '0';
    }
    v = (e == 16 ? '0x' : e == 2 ? '0b' : e == 8 ? '0o' : '') + v;
  } else {
    v = G(t);
  }
  if (t.s < 0) {
    return '-' + v;
  } else {
    return v;
  }
}
function X(t, e) {
  if (t.length > e) {
    t.length = e;
    return true;
  }
}
function Z(t) {
  return new this(t).abs();
}
function tt(t) {
  return new this(t).acos();
}
function et(t) {
  return new this(t).acosh();
}
function at(t, e) {
  return new this(t).plus(e);
}
function it(t) {
  return new this(t).asin();
}
function rt(t) {
  return new this(t).asinh();
}
function nt(t) {
  return new this(t).atan();
}
function lt(t) {
  return new this(t).atanh();
}
function ot(t, e) {
  t = new this(t);
  e = new this(e);
  var a;
  var i = this.precision;
  var r = this.rounding;
  var n = i + 4;
  if (t.s && e.s) {
    if (t.d || e.d) {
      if (!e.d || t.isZero()) {
        (a = e.s < 0 ? $(this, i, r) : new this(0)).s = t.s;
      } else if (!t.d || e.isZero()) {
        (a = $(this, n, 1).times(0.5)).s = t.s;
      } else if (e.s < 0) {
        this.precision = n;
        this.rounding = 1;
        a = this.atan(I(t, e, n, 1));
        e = $(this, n, 1);
        this.precision = i;
        this.rounding = r;
        a = t.s < 0 ? a.minus(e) : a.plus(e);
      } else {
        a = this.atan(I(t, e, n, 1));
      }
    } else {
      (a = $(this, n, 1).times(e.s > 0 ? 0.25 : 0.75)).s = t.s;
    }
  } else {
    a = new this(NaN);
  }
  return a;
}
function dt(t) {
  return new this(t).cbrt();
}
function st(t) {
  return R((t = new this(t)), t.e + 1, 2);
}
function ct(t, e, a) {
  return new this(t).clamp(e, a);
}
function ut(t) {
  if (!t || i(t) !== 'object') {
    throw Error(v + 'Object expected');
  }
  var e;
  var a;
  var r;
  var n = t.defaults === true;
  var d = [
    'precision',
    1,
    o,
    'rounding',
    0,
    8,
    'toExpNeg',
    -l,
    0,
    'toExpPos',
    0,
    l,
    'maxE',
    0,
    l,
    'minE',
    -l,
    0,
    'modulo',
    0,
    9,
  ];
  for (e = 0; e < d.length; e += 3) {
    a = d[e];
    if (n) {
      this[a] = u[a];
    }
    if ((r = t[a]) !== undefined) {
      if (y(r) !== r || !(r >= d[e + 1]) || !(r <= d[e + 2])) {
        throw Error(b + a + ': ' + r);
      }
      this[a] = r;
    }
  }
  a = 'crypto';
  if (n) {
    this[a] = u[a];
  }
  if ((r = t[a]) !== undefined) {
    if (r !== true && r !== false && r !== 0 && r !== 1) {
      throw Error(b + a + ': ' + r);
    }
    if (r) {
      if (
        typeof crypto == 'undefined' ||
        !crypto ||
        (!crypto.getRandomValues && !crypto.randomBytes)
      ) {
        throw Error(g);
      }
      this[a] = true;
    } else {
      this[a] = false;
    }
  }
  return this;
}
function pt(t) {
  return new this(t).cos();
}
function vt(t) {
  return new this(t).cosh();
}
function bt(t, e) {
  return new this(t).div(e);
}
function ft(t) {
  return new this(t).exp();
}
function gt(t) {
  return R((t = new this(t)), t.e + 1, 3);
}
function ht() {
  var t;
  var e;
  var a = new this(0);
  p = false;
  t = 0;
  while (t < arguments.length) {
    if ((e = new this(arguments[t++])).d) {
      if (a.d) {
        a = a.plus(e.times(e));
      }
    } else {
      if (e.s) {
        p = true;
        return new this(Infinity);
      }
      a = e;
    }
  }
  p = true;
  return a.sqrt();
}
function yt(t) {
  return t instanceof Mt || (t && t.toStringTag === h) || false;
}
function mt(t) {
  return new this(t).ln();
}
function _t(t, e) {
  return new this(t).log(e);
}
function wt(t) {
  return new this(t).log(2);
}
function xt(t) {
  return new this(t).log(10);
}
function St() {
  return U(this, arguments, 'lt');
}
function kt() {
  return U(this, arguments, 'gt');
}
function Ot(t, e) {
  return new this(t).mod(e);
}
function Ct(t, e) {
  return new this(t).mul(e);
}
function jt(t, e) {
  return new this(t).pow(e);
}
function Et(t) {
  var e;
  var a;
  var i;
  var r;
  var n = 0;
  var l = new this(1);
  var d = [];
  if (t === undefined) {
    t = this.precision;
  } else {
    T(t, 1, o);
  }
  i = Math.ceil(t / O);
  if (this.crypto) {
    if (crypto.getRandomValues) {
      for (e = crypto.getRandomValues(new Uint32Array(i)); n < i; ) {
        if ((r = e[n]) >= 4290000000) {
          e[n] = crypto.getRandomValues(new Uint32Array(1))[0];
        } else {
          d[n++] = r % 10000000;
        }
      }
    } else {
      if (!crypto.randomBytes) {
        throw Error(g);
      }
      for (e = crypto.randomBytes((i *= 4)); n < i; ) {
        if (
          (r =
            e[n] +
            (e[n + 1] << 8) +
            (e[n + 2] << 16) +
            ((e[n + 3] & 127) << 24)) >= 2140000000
        ) {
          crypto.randomBytes(4).copy(e, n);
        } else {
          d.push(r % 10000000);
          n += 4;
        }
      }
      n = i / 4;
    }
  } else {
    while (n < i) {
      d[n++] = (Math.random() * 10000000) | 0;
    }
  }
  i = d[--n];
  t %= O;
  if (i && t) {
    r = m(10, O - t);
    d[n] = ((i / r) | 0) * r;
  }
  for (; d[n] === 0; n--) {
    d.pop();
  }
  if (n < 0) {
    a = 0;
    d = [0];
  } else {
    for (a = -1; d[0] === 0; a -= O) {
      d.shift();
    }
    i = 1;
    r = d[0];
    for (; r >= 10; r /= 10) {
      i++;
    }
    if (i < O) {
      a -= O - i;
    }
  }
  l.e = a;
  l.d = d;
  return l;
}
function At(t) {
  return R((t = new this(t)), t.e + 1, this.rounding);
}
function Tt(t) {
  if ((t = new this(t)).d) {
    if (t.d[0]) {
      return t.s;
    } else {
      return t.s * 0;
    }
  } else {
    return t.s || NaN;
  }
}
function Pt(t) {
  return new this(t).sin();
}
function Dt(t) {
  return new this(t).sinh();
}
function It(t) {
  return new this(t).sqrt();
}
function Rt(t, e) {
  return new this(t).sub(e);
}
function Lt() {
  var t = 0;
  var e = arguments;
  var a = new this(e[t]);
  for (p = false; a.s && ++t < e.length; ) {
    a = a.plus(e[t]);
  }
  p = true;
  return R(a, this.precision, this.rounding);
}
function Nt(t) {
  return new this(t).tan();
}
function Ft(t) {
  return new this(t).tanh();
}
function $t(t) {
  return R((t = new this(t)), t.e + 1, 1);
}
E[Symbol.for('nodejs.util.inspect.custom')] = E.toString;
E[Symbol.toStringTag] = 'Decimal';
var Mt = (E.constructor = (function t(e) {
  var a;
  var r;
  var n;
  function l(t) {
    var e;
    var a;
    var r;
    var n = this;
    if (!(n instanceof l)) {
      return new l(t);
    }
    n.constructor = l;
    if (yt(t)) {
      n.s = t.s;
      if (p) {
        if (!t.d || t.e > l.maxE) {
          n.e = NaN;
          n.d = null;
        } else if (t.e < l.minE) {
          n.e = 0;
          n.d = [0];
        } else {
          n.e = t.e;
          n.d = t.d.slice();
        }
      } else {
        n.e = t.e;
        n.d = t.d ? t.d.slice() : t.d;
      }
      return;
    }
    if ((r = i(t)) === 'number') {
      if (t === 0) {
        n.s = 1 / t < 0 ? -1 : 1;
        n.e = 0;
        n.d = [0];
        return;
      }
      if (t < 0) {
        t = -t;
        n.s = -1;
      } else {
        n.s = 1;
      }
      if (t === ~~t && t < 10000000) {
        e = 0;
        a = t;
        for (; a >= 10; a /= 10) {
          e++;
        }
        if (p) {
          if (e > l.maxE) {
            n.e = NaN;
            n.d = null;
          } else if (e < l.minE) {
            n.e = 0;
            n.d = [0];
          } else {
            n.e = e;
            n.d = [t];
          }
        } else {
          n.e = e;
          n.d = [t];
        }
        return;
      }
      if (t * 0 != 0) {
        if (!t) {
          n.s = NaN;
        }
        n.e = NaN;
        n.d = null;
        return;
      } else {
        return J(n, t.toString());
      }
    }
    if (r !== 'string') {
      throw Error(b + t);
    }
    if ((a = t.charCodeAt(0)) === 45) {
      t = t.slice(1);
      n.s = -1;
    } else {
      if (a === 43) {
        t = t.slice(1);
      }
      n.s = 1;
    }
    if (S.test(t)) {
      return J(n, t);
    } else {
      return (function (t, e) {
        var a;
        var i;
        var r;
        var n;
        var l;
        var o;
        var d;
        var s;
        var c;
        if (e.indexOf('_') > -1) {
          e = e.replace(/(\d)_(?=\d)/g, '$1');
          if (S.test(e)) {
            return J(t, e);
          }
        } else if (e === 'Infinity' || e === 'NaN') {
          if (!+e) {
            t.s = NaN;
          }
          t.e = NaN;
          t.d = null;
          return t;
        }
        if (w.test(e)) {
          a = 16;
          e = e.toLowerCase();
        } else if (_.test(e)) {
          a = 2;
        } else {
          if (!x.test(e)) {
            throw Error(b + e);
          }
          a = 8;
        }
        if ((n = e.search(/p/i)) > 0) {
          d = +e.slice(n + 1);
          e = e.substring(2, n);
        } else {
          e = e.slice(2);
        }
        l = (n = e.indexOf('.')) >= 0;
        i = t.constructor;
        if (l) {
          n = (o = (e = e.replace('.', '')).length) - n;
          r = B(i, new i(a), n, n * 2);
        }
        n = c = (s = D(e, a, k)).length - 1;
        for (; s[n] === 0; --n) {
          s.pop();
        }
        if (n < 0) {
          return new i(t.s * 0);
        } else {
          t.e = N(s, c);
          t.d = s;
          p = false;
          if (l) {
            t = I(t, r, o * 4);
          }
          if (d) {
            t = t.times(Math.abs(d) < 54 ? m(2, d) : Mt.pow(2, d));
          }
          p = true;
          return t;
        }
      })(n, t);
    }
  }
  l.prototype = E;
  l.ROUND_UP = 0;
  l.ROUND_DOWN = 1;
  l.ROUND_CEIL = 2;
  l.ROUND_FLOOR = 3;
  l.ROUND_HALF_UP = 4;
  l.ROUND_HALF_DOWN = 5;
  l.ROUND_HALF_EVEN = 6;
  l.ROUND_HALF_CEIL = 7;
  l.ROUND_HALF_FLOOR = 8;
  l.EUCLID = 9;
  l.config = l.set = ut;
  l.clone = t;
  l.isDecimal = yt;
  l.abs = Z;
  l.acos = tt;
  l.acosh = et;
  l.add = at;
  l.asin = it;
  l.asinh = rt;
  l.atan = nt;
  l.atanh = lt;
  l.atan2 = ot;
  l.cbrt = dt;
  l.ceil = st;
  l.clamp = ct;
  l.cos = pt;
  l.cosh = vt;
  l.div = bt;
  l.exp = ft;
  l.floor = gt;
  l.hypot = ht;
  l.ln = mt;
  l.log = _t;
  l.log10 = xt;
  l.log2 = wt;
  l.max = St;
  l.min = kt;
  l.mod = Ot;
  l.mul = Ct;
  l.pow = jt;
  l.random = Et;
  l.round = At;
  l.sign = Tt;
  l.sin = Pt;
  l.sinh = Dt;
  l.sqrt = It;
  l.sub = Rt;
  l.sum = Lt;
  l.tan = Nt;
  l.tanh = Ft;
  l.trunc = $t;
  if (e === undefined) {
    e = {};
  }
  if (e && e.defaults !== true) {
    n = [
      'precision',
      'rounding',
      'toExpNeg',
      'toExpPos',
      'maxE',
      'minE',
      'modulo',
      'crypto',
    ];
    a = 0;
    while (a < n.length) {
      if (!e.hasOwnProperty((r = n[a++]))) {
        e[r] = this[r];
      }
    }
  }
  l.config(e);
  return l;
})(u));
s = new Mt(s);
c = new Mt(c);
function zt(t) {
  var e = t.toString().split(/[eE]/);
  var a = (e[0].split('.')[1] || '').length - +(e[1] || 0);
  if (a > 0) {
    return a;
  } else {
    return 0;
  }
}
function Bt(t, e, a) {
  var i = t;
  var r = i.indexOf('.');
  if (r < 0 && a > 0) {
    i += '.';
    for (var n = 0; n < a; n++) {
      i += '0';
    }
  } else {
    r = i.length - r;
    for (var l = 0; l < a - r + 1; l++) {
      i += '0';
    }
  }
  if (e < 0 && Number(i) !== 0) {
    if (Number(a) > 0) {
      return `-${i}`;
    } else {
      return -i;
    }
  } else {
    return i;
  }
}
function Vt(t) {
  var e = t;
  var a = `${e}`.match(/(\d)(?:\.(\d*))?e([+-]\d+)/);
  if (a) {
    var i = a[1] + (a[2] || '');
    if (Number(a[3]) > 0) {
      for (var r = 0; r < Number(a[3]) - (a[2] || '').length; r++) {
        i += '0';
      }
    } else {
      var n = '0.';
      for (var l = 0; l < -Number(a[3]) - 1; l++) {
        n += '0';
      }
      i = n + i;
    }
    e = i;
  }
  return e.toString();
}
function Ut(t) {
  return t.toString().replace('.', '').replace(/^0+/, '').length;
}
var Ht = function t(e, a, i) {
  var r = 16;
  if (
    Number(e.toPrecision(r).toString().replace('.', '').replace(/^0+/, '')) >
    Number.MAX_SAFE_INTEGER
  ) {
    r = 15;
  }
  var n = a || r;
  if (Ut(e) < n) {
    return e;
  }
  var l = parseFloat(e.toPrecision(n));
  if (Ut(l) === n && i + 6 < zt(l)) {
    return t(l, n - 1, i);
  } else {
    return l;
  }
};
function qt(t, e) {
  var a;
  var i;
  var r = zt((a = t < 0 ? -t : t));
  if (r === e && e > 0) {
    return Bt(Vt(a.toString()), t, e);
  }
  i = r < e && e > 0 ? a : Ht(a, undefined, e);
  i = Vt(i);
  var n = '';
  if ((`${i}`.split('.')[1] || '').length < e) {
    n = new Array(e + 1 - (`${i}`.split('.')[1] || '').length).join('0');
  }
  var l = `${
    `${i}`.split('.')[0] + ((`${i}`.split('.')[1] || '').substr(0, e) + n)
  }.${n ? '' : (`${i}`.split('.')[1] || '').substr(e)}`;
  var o = '';
  var d = new Mt(Number(`0.${l.split('.')[1]}` || false)).plus(new Mt(0.5));
  o =
    d >= 1
      ? Vt(new Mt(Number(l)).plus(new Mt(0.5)))
      : `${l.split('.')[0]}.${d.toString().split('.')[1]}`;
  if (zt(o) > 1) {
    o = Ht(Number(o), undefined, 1);
  }
  n = '';
  if (`${o}`.split('.')[0].length - 1 < e) {
    n = new Array(e + 1 - `${o}`.split('.')[0].length).join('0');
  }
  l = `${
    n ? '0' : `${o}`.split('.')[0].substr(0, `${o}`.split('.')[0].length - e)
  }.${n}${`${o}`
    .split('.')[0]
    .substr(n ? 0 : `${o}`.split('.')[0].length - e)}`;
  i = Vt(Number(l)).toString();
  return Bt(i, t, e);
}
export default qt;
// window.precisionFix = qt;
