@import "../Functions.less";
@import "../SelfAdaption.less";
.main-ecord-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .e-cord-top {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main-tool-left {
            display: flex;
            align-items: center;
            white-space: nowrap;
            @media screen and (max-width: 1100px) {
                .button {
                    width: auto;
                    padding: 0 6px;
                    white-space: nowrap;
                    &.dropdown {
                        min-width: 80px;
                        background-position-x: 96%;
                        .span {
                            display: inline-block;
                            text-align: center;
                        }
                        .downlist-buttons {
                            min-width: 80px;
                        }
                    }
                }
                .downlist {
                    left: 0;
                    width: 100%;
                    .downlist-buttons {
                        width: 100%;

                        div {
                            text-align: center;
                            padding: 0;
                            width: 100%;
                        }
                    }
                }
                .ml-10 {
                    margin-left: 5px;
                }
            }
            @media screen and (min-width: 1100px) {
                .button {
                    min-width: 66px;
                    max-width: 84px;
                    box-sizing: border-box;
                    height: 28px;
                    padding: 0 4px;
                    white-space: nowrap;
                    &.dropdown {
                        background-position-x: 96%;
                        .span {
                            display: inline-block;
                            text-align: center;
                        }
                        .downlist-buttons {
                            min-width: 66px;
                        }
                    }
                }
                .downlist {
                    left: 0;
                    width: 100%;
                    .downlist-buttons {
                        width: 100%;

                        div {
                            text-align: center;
                            padding: 0;
                            width: 100%;
                        }
                    }
                }
            }
            &.buttons-contract {
                .button {
                    width: auto;
                    padding: 0 6px;

                    &.dropdown {
                        min-width: 66px;
                        background-position-x: 96%;
                        .span {
                            display: inline-block;
                            text-align: center;
                        }
                        .downlist-buttons {
                            min-width: 80px;
                        }
                    }
                }
                .downlist {
                    left: 0;
                    width: 100%;
                    .downlist-buttons {
                        width: 100%;

                        div {
                            text-align: center;
                            padding: 0;
                            width: 100%;
                        }
                    }
                }
                .ml-10 {
                    margin-left: 5px;
                }
            }
        }
        .main-tool-right {
            display: flex;
            align-items: center;
            @media screen and (max-width: 1100px) {
                .search-controller .search-text {
                    width: 124px;
                    margin-left: 6px;
                }
            }
        }
    }
}
.downlist .line-item {
    &.input {
        height: auto;
    }
    &.long-input {
        height: auto !important;
        .line-item-field {
            .detail-el-select(298px);

            > input {
                .detail-original-input(298px,32px);
            }
            :deep(.el-select) {
                .el-input__wrapper {
                    height: auto;
                }
            }
            :deep(.el-popper.multiple-status-popper) {
                .el-select-dropdown__item.selected {
                    background-color: #f5f5f5;
                    color: #333;
                }
            }
        }
    }

    .line-item-title {
        width: 70px;
        padding-left: 25px;
        text-align: left;
        float: left;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
    }
    .line-item-field {
        text-align: left;
        float: left;
        padding-left: 10px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
        .detail-el-select(132px, 32px);
    }
}
.search-controller {
    // position: relative;
    .search-text {
        height: 28px;
        width: 230px;
        outline: none;
        font-size: 13px;
        line-height: 26px;
        box-sizing: border-box;
        color: var(--font-color);
        border-radius: 2px;
        padding-left: 5px;
        :deep(.el-input__wrapper) {
            padding-right: 28px;
        }
    }
    .search-submit {
        position: absolute;
        top: 6px;
        right: 8px;
        height: 16px;
        width: 15px;
        background: url("@/assets/Icons/search-icon.png") no-repeat;
        background-size: 15px 16px;
        cursor: pointer;
    }
}
.upload-content {
    color: #404040;
    font-size: 12px;
    .content-body {
        padding: 0 20px;
        .upload-path-section {
            padding: 10px 0;
            height: 28px;
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 28px;
            .upload-path {
                height: 26px;
                width: 478px;
                border: 1px solid var(--border-color);
                line-height: 26px;
                padding-left: 10px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                display: block;
            }
            .file-button {
                height: 28px;
            }
        }
        .upload-files {
            height: 301px;
            border: 1px solid var(--border-color);
            overflow: auto;
            .upload-files-item {
                min-height: 40px;
                display: flex;
                align-items: center;
                .file-icon {
                    width: 28px;
                    height: 28px;
                    margin-left: 15px;                                        
                    &.icon-word {
                        background: url("@/assets/ERecord/word_new.png") no-repeat;
                    }                
                    &.icon-excel {
                        background: url("@/assets/ERecord/excel_new.png") no-repeat;
                    }                
                    &.icon-pdf {
                        background: url("@/assets/ERecord/pdf_new.png") no-repeat;
                    }                
                    &.icon-ppt {
                        background: url("@/assets/ERecord/ppt.png") no-repeat center / 80%;
                    }                
                    &.icon-txt {
                        background: url("@/assets/ERecord/txt.png") no-repeat center / 80%;
                    }                
                    &.icon-zip {
                        background: url("@/assets/ERecord/zip.png") no-repeat center / 80%;
                    }                
                    &.icon-img {
                        background: url("@/assets/ERecord/jpg_new.png") no-repeat;
                    }                
                    &.icon-default {
                        background: url("@/assets/ERecord/default.png") no-repeat center / 88%;
                    }                
                    &.icon-ofd_file {
                        background: url("@/assets/ERecord/ofd_file.png") no-repeat center / 80%;
                    }
                }
                &:hover {
                    background-color: var(--table-hover-color);
                }
                .upload-file-name {
                    min-width: 0;
                    flex: 1;
                    margin-left: 7px;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    word-break: break-all;
                }
                .upload-file-delete {
                    cursor: pointer;
                    height: 20px;
                    width: 20px;
                    background: url("@/assets/ERecord/trash.png") no-repeat;
                    margin-right: 20px;
                    &:hover {
                        background-image: url("@/assets/ERecord/trash-green.png");
                    }
                }
            }
        }
    }
    .content-footer {
        padding: 10px 0;
        text-align: center;
    }
}
.mobile-upload-content{
    padding-top:33px;
    display: flex;
    flex-direction:column;
    justify-content: center;
    align-items: center;
    .mobile-content-footer{
        font-size: 18px;
        font-weight: 600;
        margin:12px 0px 36px
       
    }
    .upload-code{        
        border: 1px solid var(--border-color);
        width: 190px;
        height: 190px;
        position: relative;
        .qrcodeimg{
            width: 100%;
        }
        .expired-tip{
            .qrcode-refresh-icon{
                width: 36px;
                height: 36px;
                margin-bottom: 12px;
            }
            background-color: rgba(255, 255, 255, 0.9);
            width: 190px;
            height: 190px;
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -95px;
            margin-top: -95px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .expired-tip-refresh{
                cursor: pointer;
                color: #06C05F;
                font-size: 16px;
            }
            .expired-tip-content{
                font-size: 18px;
                color: #5B5B5B;
                font-weight:500;
            }
        }
    }
}
.reject-dialog-content {
    color: #404040;
    font-size: 12px;
    .newdir-main {
        padding: 40px 0;
        min-height: 42px;
        .d-text {
            width: 360px;
            font-size: 15px;
            margin: 0 auto;
            line-height: 28px;
        }
        .d-notice {
            width: 360px;
            font-size: 15px;
            margin: 0 auto;
            line-height: 28px;
            --font-color: red;
            background: url("@/assets/ERecord/warn-red.png") no-repeat left;
            margin-top: 10px;
            span {
                padding-left: 20px;
            }
        }
    }
    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
        &.no-border {
            border-top: 0px;
        }
    }
}
.relate-voucher-content {
    color: #404040;
    font-size: 12px;
    .buttons {
        text-align: center;
        margin-top: 24px;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
    .form-main {
        display: flex;
        flex-direction: column;
        align-items: center;
        .form-item {
            padding-top: 16px;
            height: 32px;
            .form-label {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                text-align: right;
                width: 70px;
            }
            .form-content {
                .detail-el-select(180px, 32px);
                > input {
                    .detail-original-input(230px, 32px);
                }
            }
        }
    }
}
.movefile-tcontent {
    color: #404040;
    font-size: 12px;
    .movefile-main {
        padding: 40px 0;
        min-height: 42px;
        margin: 0 auto;
        width: 80%;
        .movefile-title {
            display: inline-block;
            vertical-align: top;
            margin-right: 10px;
            margin-left: 10px;
        }
        .movefile-content {
            display: inline-block;
            border: 1px solid var(--border-color);
            overflow-x: auto;
            width: 260px;
            :deep(.tree) {
                display: inline-block;
                width: auto;
                // margin-bottom: 10px;
                min-width: 260px;
                .el-tree-node__content {
                    overflow: visible;

                    border-bottom: 1px solid var(--border-color);
                }
                .el-tree-node__children {
                    overflow: visible;
                    .el-tree-node__content {
                        border-bottom: none;
                    }
                }
                .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
                    background-color: #f7f8fa;
                    &:hover {
                        background-color: var(--main-color);
                    }
                }
                .el-tree-node {
                    .el-tree-node__content:hover {
                        background-color: var(--main-color);
                        & > .el-tree-node__label {
                            color: #fff;
                        }
                    }
                }
            }
            &::-webkit-scrollbar {
                width: 8px;
                height: 8px;
                background-color: transparent;
            }
            &:hover::-webkit-scrollbar-thumb {
                border-radius: 6px;
                -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
                background-color: rgba(0, 0, 0, 0.4);
            }
        }
    }
    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}

// 给园园测试
// .button {
//     border: 1px solid #cccccc;
//     &.solid-button {
//         border: none;
//     }
// }
body[erp] {
    @media screen and (max-width: 1100px) {
        .ml-10 {
            margin-left: 6px;
        }
    }
    .main-ecord-top {
        .e-cord-top {
            width: 100%;
            @media screen and (min-width: 1100px) {
                .button {
                    min-width: 82px;
                    max-width: 94px;
                    box-sizing: border-box;
                    padding: 0 6px;
                    white-space: nowrap;
                    &.dropdown {
                        background-position-x: 96%;
                        .span {
                            display: inline-block;
                            text-align: center;
                        }
                        .downlist-buttons {
                            min-width: 66px;
                        }
                    }
                }
            }
            .downlist {
                left: 0;
                min-width: 100%;
                width: auto;

                left: 0 !important;
                .downlist-buttons {
                    width: 100%;

                    div {
                        text-align: center;
                        padding: 0;
                        width: 100%;
                        padding: 0 2px;
                    }
                }
            }
            .search-text {
                margin-left: 0;
            }
        }
    }
    .movefile-tcontent {
        .movefile-content {
            :deep(.tree) {
                .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
                    background-color: #f7f8fa;
                    &:hover {
                        background-color: var(--table-title-color);
                    }
                }
                .el-tree-node {
                    .el-tree-node__content:hover {
                        background-color: var(--table-title-color);
                        & > .el-tree-node__label {
                            color: #333;
                        }
                    }
                }
            }
        }
    }
}
