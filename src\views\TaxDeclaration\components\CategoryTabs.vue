<!-- tab组件 -->
<template>
  <div class="tabs">
    <div class="tabs-left">
      <ul>
        <li
          v-for="(item, index) in tabList"
          :key="index"
          :class="{ active: activeIndex === index }"
          @click="handleClick(item, index)">
          <el-tooltip
            :content="item.title"
            :open-delay="200"
            placement="right"
            effect="light"
            :disabled="tooltipDisabled">
            <div
              class="tabs-title"
              @mouseenter="tooltipMouseEnter($event)">
              <div
                class="title"
                :class="{ 'tabs-title-required': item.isRequired }">
                <span>
                  {{ item.title }}
                </span>
              </div>

              <div
                style="width: 40px"
                v-if="activeIndex === index && showCheck">
                <div class="check force-check">{{ 1 }}</div>
                <div class="check prompt-check">{{ 2 }}</div>
              </div>
            </div>
          </el-tooltip>

          <el-tooltip
            :content="item.describe"
            effect="light"
            placement="right"
            :disabled="tooltipDisabled">
            <div
              class="tabs-describe"
              @mouseenter="tooltipMouseEnter($event)">
              <span>{{ item.describe }}</span>
            </div>
          </el-tooltip>
        </li>
      </ul>

      <div class="left-end">
        <div
          class="total"
          v-if="showTotal">
          <span>
            合计
            <Popover
              style="display: inline-block"
              placement="right"
              content="合计=增值税+城市维护建设税+教育费附加+地方教育附加">
              <template #trigger>
                <img src="@/assets/Icons/question.png" />
              </template>
            </Popover>
            ：
          </span>
          <p>{{ 11111 }}</p>
        </div>
        <div v-if="showCheck">
          <div class="check force-check">{{ 1 }}</div>
          <div class="check prompt-check">{{ 2 }}</div>
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
    <div
      class="tabs-right"
      @scroll="handleScroll($event, tabData)">
      <slot
        name="content"
        :active-index="activeIndex"
        :tab-data="tabData"></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
  import type { ITabProps, TabObj } from "./types"
  const activeIndex = defineModel("modelValue", { type: Number, default: 0 })
  const props = withDefaults(defineProps<ITabProps>(), {
    tabList: () => [],
    showCheck: true,
    showTotal: false,
    activeIndex: 0,
  })

  // // 当前活跃的tab页，默认为第一个
  // let activeIndex = ref(0)
  // 当前活跃的tab页数据
  let tabData = ref<TabObj>(props.tabList[activeIndex.value])

  const emit = defineEmits<{
    (e: "tab-click", row: TabObj, index: number): void
    (e: "scroll", event: Event, tabData: { [key: string]: any }): void
  }>()

  // li点击事件
  function handleClick(row: TabObj, index: number) {
    activeIndex.value = index
    tabData.value = row
    emit("tab-click", row, index)
  }

  function handleScroll(e: Event, tabData: { [key: string]: any }) {
    emit("scroll", e, tabData)
  }

  let tooltipDisabled = ref<boolean>(true)
  function tooltipMouseEnter(e: MouseEvent) {
    const target = e.target as HTMLElement
    const span = target.querySelector("span")
    tooltipDisabled.value = !(target.offsetWidth < span!.offsetWidth)
  }
</script>
<style lang="scss" scoped>
  .tabs {
    display: flex;

    .tabs-left {
      width: 170px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      // height: calc(100vh - 270px);
      height: 85%;
      border-radius: 4px;
      border: 1px solid #eaeaea;
      overflow-y: auto;

      ul {
        margin: 0 0 0 4px;
        padding: 0;
        list-style: none;

        li {
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 10px 12px;
          cursor: pointer;

          .tabs-title {
            display: flex;
            justify-content: space-between;
            // width: 100%;
            font-size: 13px;
            font-weight: 500;
            line-height: 22px;

            .title {
              flex: 1;
              display: inline-block;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }

          .tabs-title-required::before {
            content: "*";
            color: red;
          }

          .tabs-describe {
            font-size: var(--h5);
            font-weight: 400;
            color: #666;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .active {
        border-left: 4px solid var(--main-color);
        margin-left: -4px;
        background-color: #f5fcf8;

        .tabs-title {
          color: var(--main-color);
        }
      }

      .left-end {
        width: 100%;
        margin-bottom: 12px;
        text-align: right;

        .total {
          padding: 8px 4px;
          margin-bottom: 12px;
          font-size: var(--h5);
          font-weight: 400;
          background-color: #f8fffa;

          img {
            width: 12px;
            height: 12px;
            vertical-align: text-top;
          }

          p {
            margin: 0;
            line-height: 22px;
            font-size: var(--h4);
            font-weight: 500;
          }
        }
      }

      .check {
        display: inline-block;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        vertical-align: middle;
        text-align: center;
        line-height: 15px;
        font-size: 10px;
        color: var(--white);

        &.force-check {
          background-color: #f6404e;
        }

        &.prompt-check {
          margin-left: 10px;
          background-color: #ffbb00;
        }
      }

      .el-icon {
        margin: 0 7px;
        width: 14px;
        height: 14px;
        color: #969799;
        vertical-align: middle;
        cursor: pointer;
      }
    }

    .tabs-right {
      flex: 7;
      margin-left: 12px;
      height: auto;
      // height: calc(100vh - 180px);
      // overflow: hidden;
      overflow-y: auto;
      // transform: translate3d(0, 0, 0);
    }
  }
</style>
