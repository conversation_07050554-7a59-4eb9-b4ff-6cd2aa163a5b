import { fileURLToPath, URL } from "node:url"
import autoImport from "unplugin-auto-import/vite"
import compression from "vite-plugin-compression"
import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import legacy from "@vitejs/plugin-legacy"

// import dns from 'dns';
// dns.setDefaultResultOrder('verbatim');
export default defineConfig({
  plugins: [
    autoImport({
      imports: ["vue", "vue-router", "pinia"],
      eslintrc: {
        enabled: true,
        filepath: "./.eslintrc-auto-import.json",
        globalsPropValue: true,
      },
      dts: "./auto-imports.d.ts",
    }),
    compression({
      threshold: 1024 * 500, // 体积大于 threshold 才会被压缩,单位 b
      ext: ".gz", // 压缩文件格式
      deleteOriginFile: false, // 是否删除源文件
    }),
    vue(),
    legacy({
      targets: ["defaults", "ie >= 11", "chrome >= 49", "chrome 86"], //需要兼容的目标列表，可以设置多个
      additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
      renderLegacyChunks: true,
      polyfills: [
        "es.symbol",
        "es.array.filter",
        "es.promise",
        "es.promise.finally",
        "es/map",
        "es/set",
        "es.array.for-each",
        "es.object.define-properties",
        "es.object.define-property",
        "es.object.get-own-property-descriptor",
        "es.object.get-own-property-descriptors",
        "es.object.keys",
        "es.object.to-string",
        "web.dom-collections.for-each",
        "esnext.global-this",
        "esnext.string.match-all",
      ],
    }),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        charset: false,
        api: "modern-compiler",
        // 全局引入变量和 mixin
        additionalData: `
          @use "@/style/constants.scss" as *;
          @use "@/style/functions.scss" as *;
        `,
      },
    },
  },
  optimizeDeps: {
    exclude: ["pinyin-pro", "axios", "js-sha1"],
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  build: {
    target: "es2015",
    cssTarget: "chrome49",
    chunkSizeWarningLimit: 1500,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return id.toString().split("node_modules/")[1].split("/")[0].toString()
          }
        },
      },
    },
  },
  server: {
    host: "localhost",
    port: 5173,
    hmr: true,
    open: true,
  },
})
