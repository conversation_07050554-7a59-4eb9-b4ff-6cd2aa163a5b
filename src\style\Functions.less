@import "constants.less";

//编辑页面默认输入框，能兼容select2
.detail-input(@width, @height) {
    input[type="text"],
    input[type="password"],
    input[type="number"],
    input[type="text"] {
        width: @width;
        height: @height;
        .set-border(var(--input-border-color));
        outline: none;
        padding: 0;
        padding-left: 10px;
        padding-right: 10px;
        .set-font(var(--font-color), var(--font-size), @height);
        box-sizing: border-box;
        border-radius: var(--input-border-radius);

        &::-webkit-input-placeholder {
            color: var(--input-placeholder-color);
        }

        &.dropdown {
            background-repeat: no-repeat;
            background-position: right 5px top (@height - 12px) / 2;
            background-image: url("/Image/Settings/down-black.png");

            &:focus {
                background-image: url("/Image/Settings/up-black.png");
            }
        }

        &:hover {
            border-color: var(--input-border-hover-color);
        }

        &:focus {
            border-color: var(--input-border-focus-color);
            outline: 1px var(--input-border-outline-color);
        }
    }

    select {
        width: @width !important;
    }
}

.detail-el-form(@width, @height) {
    :deep(.el-form-item) {
        .el-form-item__error {
            color: var(--red);
            font-size: var(--font-size);
            line-height: @height;
            position: absolute;
            left: 100%;
            white-space: nowrap;
        }
    }

    :deep(.el-form-item__label) {
        display: inline-flex;
        justify-content: flex-end;
        align-items: flex-start;
        flex: 0 0 auto;
        font-size: var(--el-form-label-font-size);
        color: var(--el-text-color-regular);
        height: 32px;
        line-height: 32px;
        padding: 0;
        box-sizing: border-box;
    }

    .detail-el-input(@width, @height);
}

.detail-el-input(@width, @height) {
    :deep(.el-input) {
        width: @width;

        .el-input__wrapper {
            padding: 0 8px;
            width: @width;
            box-sizing: border-box;

            input.el-input__inner {
                width: 100%;
                height: @height;
                border: none;
                .set-font(var(--font-color), var(--font-size), @height);
            }
        }
    }
}

.detail-el-input-number(@width, @height) {
    :deep(.el-input-number) {
        width: @width;
    }

    .detail-el-input(@width, @height);
}

.detail-el-date-picker(@width, @height) {
    .detail-el-input(@width, @height);

    :deep(.el-input) {
        height: @height;
    }

    :deep(.el-input__wrapper) {
        .el-input__suffix,
        .el-input__prefix {
            position: absolute;
            top: 0;
            right: 0;
        }
        .el-input__prefix {
            .el-input__prefix-inner > :last-child {
                margin-right: 4px;
            }
        }
        .el-input__suffix {
            right: 18px;
        }
    }

    :deep(.el-popper) {
        .el-month-table td.current,
        .el-year-table td.current {
            border: none !important;
            border-radius: 0px !important;
            &:focus {
                outline: none !important;
            }
        }

        .el-month-table td.current:not(.disabled) .cell,
        .el-year-table td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
}

.detail-el-table-item() {
    :deep(.el-table__cell) {
        padding: 7px 0px;

        .cell {
            font-size: 12px;
            padding: 0px 4px;
            font-weight: bold;
        }
    }

    :deep(.el-table__row) {
        .cell {
            font-weight: normal;
        }
    }
}

.detail-el-select(@width, @height: 32px) {
    :deep(.el-select) {
        width: @width;

        .el-input__wrapper {
            height: @height;
        }
        .el-select-dropdown__wrap {
            width: @width;
        }
    }
}
.detail-el-autocomplete(@width, @height: 32px) {
    :deep(.el-autocomplete) {
        width: @width;
        height: @height;
    }
    :deep(.el-autocomplete__popper) {
        box-sizing: border-box !important;
        border: none;
        margin-top: -6px;
        .el-autocomplete-suggestion {
            width: @width;
            .el-autocomplete-suggestion__wrap {
                padding: 0;
                .el-autocomplete-suggestion__list {
                    li {
                        font-size: 12px;
                        padding: 4px 6px 4px 8px;
                        line-height: 20px;
                        height: auto;
                        white-space: break-spaces;
                        &:hover {
                            background-color: var(--main-color) !important;
                            color: var(--white) !important;
                            font-weight: normal;
                        }
                    }
                }
            }
            .el-scrollbar__bar.is-vertical {
                width: 8px;
                right: 0;
                --el-scrollbar-opacity: 0.7;
                --el-scrollbar-bg-color: #3c3f43;
            }
        }
        .el-popper__arrow {
            display: none !important;
        }
    }
}
.detail-original-input(@width, @height) {
    width: @width !important;
    height: @height !important;
    border: 1px solid var(--input-border-color);
    outline: none;
    padding: 0;
    padding-left: 10px;
    padding-right: 10px;
    color: var(--font-color);
    font-size: var(--font-size);
    line-height: @height;
    box-sizing: border-box;
    border-radius: var(--input-border-radius);
    outline-style: none !important;

    &:focus {
        border-color: var(--input-border-focus-color);
        outline: 1px var(--input-border-outline-color);
    }
}

.detail-el-tree() {
    :deep(.el-tree) {
        display: inline-block;
        .el-tree-node {
            text-align: start;
            &.is-focusable,
            &.is-focusable.no-children {
                > .el-tree-node__content {
                    > .el-tree-node__label {
                        background-image: url("@/assets/Icons/folder.png");
                    }
                }
            }
            &[aria-expanded~="true"] {
                > .el-tree-node__content {
                    > .el-tree-node__label {
                        background-image: url("@/assets/Icons/folder-open.png");
                    }
                }
            }
            &.tree-file {
                > .el-tree-node__content {
                    > .el-tree-node__label {
                        background-image: url("@/assets/Icons/file.png") !important;
                    }
                }
            }
            .el-tree-node__content {
                height: 21px;
                overflow: visible;
                .el-icon {
                    display: inline-flex;
                    justify-content: center;
                    align-items: center;
                    width: 21px;
                    height: 21px;
                    padding: 0;
                }
                .el-tree-node__label {
                    display: inline-block;
                    padding-left: 23px;
                    background-repeat: no-repeat;
                    background-position-y: center;
                    background-position-x: 2px;
                    color: var(--font-color);
                    font-size: var(--h5);
                    line-height: 21px;
                    text-align: left;
                }
            }
            .el-tree-node__children {
                overflow: visible;
            }
        }
    }
}

.detail-placehoder-color(@color) {
    /* WebKit browsers (Safari, Chrome) */
    input::-webkit-input-placeholder {
        color: @color;
    }

    /* Mozilla Firefox */
    input::-moz-placeholder {
        color: @color;
        opacity: 1;
    }

    /* Internet Explorer 10+ */
    input:-ms-input-placeholder {
        color: @color;
    }

    /* Microsoft Edge */
    input::-ms-input-placeholder {
        color: @color;
    }
}

.detail-spin-button {
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
    }
    -moz-appearance: textfield;
    &:focus,
    &:hover {
        -moz-appearance: number-input;
    }
}

.detail-el-textarea-scroll-thumb() {
    .el-textarea__inner {
        // box-shadow: none;
        // border: 1px solid var(--el-border-color);
        box-shadow: 0 0 0 1px var(--el-border-color) inset;
        &:hover {
            // box-shadow: none;
            // border: 1px solid var(--el-input-hover-border-color);
            box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
        }
        &:focus {
            // box-shadow: none;
            // border: 1px solid var(--main-color);
            box-shadow: 0 0 0 1px var(--main-color) inset;
        }
        &::-webkit-scrollbar-thumb {
            background-color: #c3c3c3;
            border-radius: 8px;
        }
        &::-webkit-scrollbar {
            background-color: white;
            width: 6px;
        }
        &::-webkit-scrollbar-track {
            background-color: white;
        }
        &::-webkit-scrollbar-corner {
            background-color: white;
        }
        &::-webkit-scrollbar-track-piece {
            background-color: white;
            width: 6px;
        }
    }
}

.detail-lm-default-scroll(@width){
    //hover宽度不生效问题
    &::-webkit-scrollbar {
        width: @width;
        height: @width;
        padding-right: 2px;
        background-color: #fff;
        &:hover{
            width: 10px;
        }
    }
    &::-webkit-scrollbar-thumb {
        width: @width;
        height: @width;
        border-radius: 3px;
        background: #d0d0d0;
        transition: width 0.3s; 
        &:hover {
            width: 10px;
            background-color: #b1b1b1;
        }
    }
}

//字体
.set-font(@color: var(--font-color), @font-size: var(--font-size), @line-height: var(--line-height)) {
    color: @color;
    font-size: @font-size;
    line-height: @line-height;
}

//动画
.set-transition(@time: var(--transition-time)) {
    transition: @time;
}

//边框
.set-border(@color: var(--border-color)) {
    border: 1px solid @color;
}

//空心按钮
.hollow(@color, @border-color) {
    color: @color;
    .set-border(@border-color);
    background-color: var(--white);
}

//实心按钮
.solid(@color) {
    color: var(--white);
    border-color: @color;
    background-color: @color;
}

//hover和active效果
.hover(@color) {
    &:hover {
        .solid(@color);
    }
}

.hover-font(@color) {
    &:hover {
        color: @color;
    }
}

.active(@color) {
    &:active {
        .solid(@color);
    }
}

.active-font(@color) {
    &:active {
        color: @color;
    }
}

//按钮
.base-button(@height, @width) {
    height: @height;
    width: @width;
    line-height: @height;
    font-size: 13px;
    display: inline-block;
    cursor: pointer;
    text-align: center;
    padding: 0;
    outline: none;
    .set-transition;
}

.dropdown-button(@height, @width) {
    border-radius: 2px;
    .base-button(@height, @width);
    .solid(var(--main-color));
    background-image: url("/src/assets/Icons/down-white.png");
    background-position-x: @width - 18px;
    background-position-y: center;
    background-repeat: no-repeat;
    position: relative;

    .downlist {
        display: block;
        position: absolute;
        top: @height;
        left: 0;
        visibility: hidden;
        opacity: 0;
        .set-transition;
        z-index: 10;
        cursor: default;
    }

    &:hover {
        //background-image: url('/Image/Settings/up-white.png');
        .downlist {
            visibility: visible;
            opacity: 1;
        }
    }
}

//分割线
.line(@color: var(--border-color)) {
    height: 1px;
    background-color: @color;
}

.asubName-textarea {
    :deep(.el-textarea__inner) {
        z-index: 1000;
    }
}
