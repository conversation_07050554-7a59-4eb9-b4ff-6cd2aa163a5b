import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { FormatNumber,truncateDecimal,truncateDecimalZero } from "@/util/format";
const accountsetStore = useAccountSetStoreHook();

export const strInArray = (elem: string, arr: string[], i?: number) => {
    let len;
    if (arr) {
        // if ( indexOf ) {
        // 	return indexOf.call( arr, elem, i );
        // }
        len = arr.length;
        i = i ? (i < 0 ? Math.max(0, len + i) : i) : 0;

        for (; i < len; i++) {
            // Skip accessing in sparse arrays
            if (i in arr && (arr[i].toLowerCase()) === elem) {
                return i;
            }
        }
    }
    return -1;
}

export const getShowData = (value: any) => {
    const decimal = accountsetStore.accountSet?.decimalPlace;
    if (parseFloat(value) == 0.0) {
        return "";
    }
    let formated = false;
    if (value.toString().indexOf(",") != -1) {
        formated = true;
        value = value.replace(/,/g, "");
    }
   const truncatedData = truncateDecimal(value, decimal);

    if (truncatedData == "") {
        return truncatedData;
    }
    if (parseFloat(value) == parseFloat(truncatedData)) {
        return formated ? FormatNumber(truncatedData) : truncatedData;
    }
    return (
        "<span onmouseover='showRealData(\"" +
        (formated ? FormatNumber(truncateDecimalZero(parseFloat(value).toFixed(8))) : truncateDecimalZero(parseFloat(value).toFixed(8))) +
        "\")' onmouseleave='hideRealData()'>" +
        (formated ? FormatNumber(truncatedData) : truncatedData) +
        "</span>"
    );
}
