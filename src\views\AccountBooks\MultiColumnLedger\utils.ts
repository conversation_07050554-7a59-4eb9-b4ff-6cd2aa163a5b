import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "MultiColumnLedger";
export const convertOptionStrToArray = (optionStr: string) => {
    const regex = /<option value='(\d+)'>([^<]+)<\/option>/g;
    const options = [];
    let match;
    while ((match = regex.exec(optionStr))) {
        const id = match[1]; // 将 id 转换为字符串
        const label = match[2];
        options.push({ id, label });
    }
    return options;
};

interface ITableDataBack {
    columns: any[];
    columnsTwo: any[];
    rows: any[];
    total: number;
}

export const setColumns = (res: ITableDataBack) => {
    const columns = res.columns.map((item: any) => {
        let columnsItem: IColumnProps;
        if (item.title == "凭证字号") {
            columnsItem = { slot: "voucher" };
        } else {
            columnsItem = {
                label: item.title,
                prop: item.field.toLowerCase(),
                minWidth: item.width,
                width: item.title === "余额" ? item.width : getColumnWidth(setModule, item.field.toLowerCase()),
                align: item.align,
                headerAlign: "center",
            };
            if (item.formatter == "getShortData") {
                columnsItem.formatter = (row, column, value) => {
                    return getShortData(value);
                };
            } else if (item.formatter == "formatMoney") {
                columnsItem.formatter = (row, column, value) => {
                    return formatMoney(value);
                };
            }
        }
        return columnsItem;
    });
    const columnsTwo = res.columnsTwo.map((item: any) => {
        const columnsItem: IColumnProps = {
            label: item.title,
            prop: item.field.toLowerCase(),
            minWidth: item.width,
            width: getColumnWidth(setModule, item.field.toLowerCase()),
            align: item.align,
            headerAlign: "center",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
        };
        return columnsItem;
    });
    columns[columns.length - 1].children = columnsTwo;

    return columns;
};

export const getShortData = (data: string) => data.split("T")[0] || "";

export const parseString = (input: string) => {
    const regex = /ShowDetail\((\d+),(\d+)\)'>(.+)<\/a>/;
    const match = input.match(regex) as any[];
    return {
        params1: parseInt(match[1]),
        params2: parseInt(match[2]),
        title: match[3],
    };
};
