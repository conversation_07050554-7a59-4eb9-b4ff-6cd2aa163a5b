<template>
    <content-slider :slots="['add', 'masterPlate']" :current-slot="currentSlot">
        <template #add>
            <div class="slot-content align-center invoice-edit-content" style="width: 100%">
                <div class="slot-title">
                    {{ invoiceType.title }}
                </div>

                <div class="edit-content">
                    <div class="title">
                        {{ invoiceType.title }}
                    </div>
                    <el-form ref="invoiceFormRef" inline :model="invoiceForm" class="invoice-form">
                        <div class="main-block">
                            <div class="main-title">基本信息</div>
                            <div class="main-block-center">
                                <div :class="['form-item-list', 'list-large', { listElectric: ['1060', '1070'].includes(invoiceType.id) }]">
                                    <el-form-item
                                        label="开票日期:"
                                        :show-message="false"
                                        :status-icon="false"
                                        :class="['line-item required']"
                                    >
                                        <el-date-picker
                                            :class="['line-item-field', 'line-item-date', 'invoice-date']"
                                            ref="invoiceDateRef"
                                            v-model="invoiceForm.invoiceDate"
                                            value-format="YYYY-MM-DD"
                                            type="date"
                                            clearable
                                            placeholder=""
                                            :disabled="formType === 'look'"
                                            @focus="handleDateFocus($event, 'invoiceDate')"
                                            @blur="handleDateInput($event, 'invoiceDate')"
                                            @keydown.enter="invoiceDateRefFocus"
                                        />
                                    </el-form-item>
                                    <el-form-item
                                        v-if="!['1010', '1050', '1060', '1070'].includes(invoiceType.id)"
                                        label="发票代码:"
                                        :show-message="false"
                                        prop="code"
                                        class="line-item required"
                                    >
                                        <Tooltip :content="invoiceForm.code" :isInput="true">
                                            <el-input
                                                ref="codeRef"
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="invoiceForm.code"
                                                @keydown.enter="numberRef.focus()"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="!['1010', '1050'].includes(invoiceType.id)"
                                        :label="['1060', '1070'].includes(invoiceType.id) ? '全电发票号码:' : '发票号码:'"
                                        :show-message="false"
                                        prop="number"
                                        class="line-item required"
                                    >
                                        <Tooltip :content="invoiceForm.number" :isInput="true">
                                            <el-input
                                                ref="numberRef"
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="invoiceForm.number"
                                                @keydown.enter="numberFocus"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="!['1010', '1050'].includes(invoiceType.id)"
                                        label="是否作废:"
                                        :show-message="false"
                                        prop="isInvalid"
                                        class="line-item required"
                                    >
                                        <div class="invoice-add-select">
                                            <el-select
                                                ref="isInvalidRef"
                                                :disabled="formType === 'look'"
                                                class="line-item-field line-item-select isInvalidRef"
                                                :suffix-icon="CaretBottom"
                                                v-model="invoiceForm.isInvalid"
                                                placeholder=""
                                                :fit-input-width="true"
                                                :teleported="false"
                                                @keydown.enter="isInvalidFocus"
                                            >
                                                <el-option label="正常" :value="0" />
                                                <el-option label="作废" :value="1" />
                                            </el-select>
                                        </div>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="!['1050'].includes(invoiceType.id)"
                                        :label="invoiceCategory === '10070' ? '客户名称:' : '供应商名称:'"
                                        :show-message="false"
                                        prop="name"
                                        :class="['line-item', { required: !['1010'].includes(invoiceType.id) }]"
                                    >
                                        <span class="autocomplete-input">
                                            <Select
                                                v-if="isErp"
                                                ref="customVendorSelectRef"
                                                :disabled="formType === 'look'"
                                                class="line-item-field customVendorSelectRef"
                                                :suffix-icon="CaretBottom"
                                                v-model="invoiceForm.erpNameId"
                                                placeholder=" "
                                                :teleported="false"
                                                :filterable="true"
                                                :bottom-html="hasPermissionCustomerOrVenor() ? (invoiceCategory === '10070' ? newCDAccountHtml : newCDAccountHtmlG) : ''"
                                                @bottom-click="newCdAccount"
                                                @change="handleErpNameCheck"
                                                @blur="inputBlur"
                                                @visible-change="handleCustomerOrVendorVisibleChange"
                                                :filter-method="erpAssistingCustomFilterMethod"
                                            >
                                                <ElOption
                                                    v-for="item in showErpAssistingCustomList"
                                                    :key="item.aaeid"
                                                    :label="item.aanum + ' ' + item.aaname"
                                                    :value="item.aaeid.toString()"
                                                ></ElOption>
                                            </Select>
                                            <div v-else class="invoice-move-top">
                                                <el-autocomplete
                                                    v-model="invoiceForm.name"
                                                    :prop="[{ required: true, trigger: ['change'] }]"
                                                    :fetch-suggestions="querySearch"
                                                    :trigger-on-focus="false"
                                                    placeholder=" "
                                                    ref="nameRef"
                                                    v-if="nameTextareaShow"
                                                    type="textarea"
                                                    :autosize="{minRows: 1, maxRows: 3.5 }"
                                                    resize="none"
                                                    :fit-input-width="true"
                                                    :teleported="false"
                                                    :debounce="500"
                                                    class="line-item-field"
                                                    :disabled="formType === 'look'"
                                                    maxlength="256"
                                                    @focus="inputTypeFocus()"
                                                    @blur="inputBlur"
                                                    @select="handleSelect"
                                                    @keydown.enter="taxpayerNumberRef.focus()"
                                                >
                                                    <template #default="{ item }">
                                                        <div class="value">{{ item.value }}</div>
                                                    </template>
                                                </el-autocomplete>
                                                <Tooltip :content="invoiceForm.name" :isInput="true" v-else>
                                                <el-autocomplete
                                                    v-model="invoiceForm.name"
                                                    :prop="[{ required: true, trigger: ['change'] }]"
                                                    :fetch-suggestions="querySearch"
                                                    :trigger-on-focus="false"
                                                    placeholder=" "
                                                    ref="nameRef" 
                                                    :fit-input-width="true"
                                                    :teleported="false"
                                                    :debounce="500"
                                                    class="line-item-field"
                                                    :disabled="formType === 'look'"
                                                    @focus="inputTypeFocus('name')"
                                                    @blur="inputBlur"
                                                    @select="handleSelect"
                                                    @keydown.enter="taxpayerNumberRef.focus()"
                                                >
                                                <template #default="{ item }">
                                                    <div class="value">{{ item.value }}</div>                                                        
                                                </template>
                                            </el-autocomplete>
                                            </Tooltip>
                                        </div>
                                        </span>
                                    </el-form-item>
                                    <el-form-item v-if="!['1050'].includes(invoiceType.id)" label="统一社会信用代码:" class="line-item">
                                        <Tooltip :content="invoiceForm.taxpayerNumber" :isInput="true">
                                            <el-input
                                                ref="taxpayerNumberRef"
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="invoiceForm.taxpayerNumber"
                                                @keydown.enter="
                                                    !['1050', '1010'].includes(invoiceType.id)
                                                        ? addressRef.focus()
                                                        : !['1050', '1010'].includes(invoiceType.id)
                                                        ? accountRef.focus()
                                                        : invoiceType.id === '1040'
                                                        ? vinRef.focus()
                                                        : isProjectAccountingEnabled
                                                        ? projectAccountingFocus()
                                                        : noteRef.focus()
                                                "
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item v-if="!['1050', '1010'].includes(invoiceType.id)" label="地址及电话:" class="line-item">
                                        <Tooltip :content="invoiceForm.address" :isInput="true">
                                            <el-input
                                                ref="addressRef"
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="invoiceForm.address"
                                                @keydown.enter="
                                                    !['1050', '1010'].includes(invoiceType.id)
                                                        ? accountRef.focus()
                                                        : invoiceType.id === '1040'
                                                        ? vinRef.focus()
                                                        : isProjectAccountingEnabled
                                                        ? projectAccountingFocus()
                                                        : noteRef.focus()
                                                "
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item v-if="!['1050', '1010'].includes(invoiceType.id)" label="开户行及账号:" class="line-item">
                                        <Tooltip :content="invoiceForm.account" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="invoiceForm.account"
                                                ref="accountRef"
                                                @keydown.enter="
                                                    invoiceType.id === '1110' || invoiceType.id === '1090'
                                                        ? vehicleTypeRef.focus()
                                                        : invoiceType.id === '1040'
                                                        ? vinRef.focus()
                                                        : invoiceCategory === '10080'
                                                        ? issueDateRef.focus()
                                                        : isProjectAccountingEnabled
                                                        ? projectAccountingFocus()
                                                        : noteRef.focus()
                                                "
                                            />
                                        </Tooltip>
                                    </el-form-item>

                                    <el-form-item
                                        v-if="invoiceType.id === '1110' || invoiceType.id === '1090'"
                                        label="车辆类型:"
                                        class="line-item"
                                    >
                                        <Tooltip :content="vehicleType" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="vehicleType"
                                                ref="vehicleTypeRef"
                                                @keydown.enter="brandModelRef.focus()"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="invoiceType.id === '1110' || invoiceType.id === '1090'"
                                        label="厂牌型号:"
                                        class="line-item"
                                    >
                                        <Tooltip :content="brandModel" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="brandModel"
                                                ref="brandModelRef"
                                                @keydown.enter="vinRef.focus()"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="invoiceType.id === '1040' || invoiceType.id === '1110' || invoiceType.id === '1090'"
                                        label="车架号:"
                                        class="line-item"
                                    >
                                        <Tooltip :content="vin" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="vin"
                                                ref="vinRef"
                                                @keydown.enter="
                                                    invoiceType.id === '1110'
                                                        ? engineNUMRef.focus()
                                                        : invoiceType.id === '1090'
                                                        ? vehicleLicenceRef.focus()
                                                        : machineNumbersRef.focus()
                                                "
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item v-if="invoiceType.id === '1090'" label="车牌照号:" class="line-item">
                                        <Tooltip :content="vehicleLicence" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="vehicleLicence"
                                                ref="vehicleLicenceRef"
                                                @keydown.enter="issueDateRef.focus()"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item v-if="invoiceType.id === '1040'" label="机器编号:" class="line-item">
                                        <Tooltip :content="machineNumbers" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="machineNumbers"
                                                ref="machineNumbersRef"
                                                @keydown.enter="competentTaxAuthoritiesCodeRef.focus()"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item v-if="invoiceType.id === '1110'" label="发动机号码:" class="line-item">
                                        <Tooltip :content="engineNUM" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="engineNUM"
                                                ref="engineNUMRef"
                                                @keydown.enter="competentTaxAuthoritiesCodeRef.focus()"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="invoiceType.id === '1040' || invoiceType.id === '1110'"
                                        label="主管税务机关代码:"
                                        class="line-item"
                                    >
                                        <Tooltip :content="competentTaxAuthoritiesCode" :isInput="true">
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class="line-item-field"
                                                v-model="competentTaxAuthoritiesCode"
                                                ref="competentTaxAuthoritiesCodeRef"
                                                @keydown.enter="
                                                    invoiceCategory === '10080'
                                                        ? issueDateRef.focus()
                                                        : isProjectAccountingEnabled
                                                        ? projectAccountingFocus()
                                                        : noteRef.focus()
                                                "
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        v-if="invoiceCategory === '10080'"
                                        label="认证日期:"
                                        :status-icon="false"
                                        class="line-item"
                                    >
                                        <el-date-picker
                                            class="line-item-field line-item-date issue-date"
                                            v-model="invoiceForm.issueDate"
                                            value-format="YYYY-MM-DD"
                                            clearable
                                            ref="issueDateRef"
                                            @keydown.enter="isProjectAccountingEnabled ? projectAccountingFocus() : noteRef.focus()"
                                            @focus="handleDateFocus($event, 'issueDate')"
                                            @blur="handleDateInput($event, 'issueDate')"
                                        />
                                    </el-form-item>
                                    <el-form-item label="录入日期:" class="line-item">
                                        <el-date-picker
                                            :disabled="formType === 'look' || invoiceForm.invoiceSource === 100"
                                            class="line-item-field line-item-date"
                                            v-model="invoiceForm.entryDate"
                                            value-format="YYYY-MM-DD"
                                            type="date"
                                            :clearable="false"
                                            placeholder=""
                                        />
                                    </el-form-item>
                                    <el-form-item v-if="isShowEditProject" label="项目:" class="line-item">
                                        <span class="project-select">
                                            <Select
                                                ref="customNameSelectRef"
                                                class="line-item-field customNameSelectRef"
                                                :suffix-icon="CaretBottom"
                                                v-model="invoiceForm.projectId"
                                                placeholder="请选择"
                                                clearable
                                                :IconClearRight="'20px'"
                                                :filterable="true"
                                                :teleported="false"
                                                :bottom-html="checkPermission(['assistingaccount-canedit']) ? newCDAccountHtmlProject : ''"
                                                @bottom-click="newProject"
                                                :filter-method="erpAssistingProjectFilterMethod"
                                            >
                                                <ElOption
                                                    v-for="item in showErpAssistingProjectList"
                                                    :key="item.aaeid"
                                                    :label="item.aaname"
                                                    :value="item.aaeid"
                                                ></ElOption>
                                            </Select>
                                        </span>
                                    </el-form-item>
                                    <el-form-item label="备注:" class="line-item line-item-last invoice-move-top">
                                        <el-input
                                            :disabled="formType === 'look'"
                                            class='line-item-field'
                                            v-model="invoiceForm.note"
                                            v-if="textareaShow"
                                            ref="noteRef"
                                            :autosize="{minRows: 1, maxRows: 3.5 }"
                                            type="textarea"
                                            maxlength='2048'
                                            resize="none"
                                            @keydown.enter="
                                                ['1010', '1050'].includes(invoiceType.id)
                                                    ? tradeAmountRef[0].focus()
                                                    : tradeNameRef[0].focus()
                                            "
                                            @blur="handleBlur"
                                            @input="inputLength(invoiceForm.note,'备注')"
                                            @focus="inputTypeFocus()"
                                        />
                                        <div :class="(invoiceForm.note).length>256?'note-tooltip-width':''" v-else>
                                            <Tooltip :content="invoiceForm.note" :isInput="true" placement="right" >
                                            <el-input
                                                :disabled="formType === 'look'"
                                                class='line-item-field'
                                                v-model="invoiceForm.note"
                                                ref="noteRef"
                                                @focus="inputTypeFocus('note')"
                                                resize="none"
                                                @keydown.enter="
                                                    ['1010', '1050'].includes(invoiceType.id)
                                                        ? tradeAmountRef[0].focus()
                                                        : tradeNameRef[0].focus()
                                                "
                                            />
                                        </Tooltip>
                                        </div>
                                    </el-form-item>
                                </div>
                            </div>
                        </div>
                        <div class="main-block" v-show="!isErp">
                            <div class="main-title">业务类型</div>
                            <div class="main-block-center">
                                <div class="second-form">
                                    <el-form-item label="模板:" required :show-message="false" prop="businessType" class="line-item">
                                        <div class="invoice-add-select">
                                            <Select
                                                :title="businessTypeOptions.find((v) => v.value === invoiceForm.businessType)?.label"
                                                :disabled="formType === 'look'"
                                                class="line-item-field line-item-select addBusinessTypeRef"
                                                :suffix-icon="CaretBottom"
                                                v-model="invoiceForm.businessType"
                                                placeholder=" "
                                                :teleported="false"
                                                :filterable="true"
                                                :no-match-text="'暂无数据'"
                                                @change="businessTypeChange"
                                                :filter-method="businessTypeFilterMethod"
                                            >
                                                <ElOption
                                                    v-for="item in showBusinessTypeOptions"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></ElOption>
                                            </Select>
                                        </div>
                                    </el-form-item>
                                    <span
                                        v-permission="['invoicevouchertemplate-canview']"
                                        type="primary"
                                        class="set-template-button link"
                                        @click="goToMasterPlate"
                                        >设置生成凭证模板</span
                                    >
                                </div>
                            </div>
                        </div>
                        <div class="main-block" style="padding-bottom: 0">
                            <div class="main-title">发票数据</div>
                            <div class="main-block-center">
                                <div class="third-form" v-if="!['1050', '1010', '1040'].includes(invoiceType.id)">
                                    <el-form-item
                                        label="是否录入发票明细:"
                                        required
                                        :show-message="false"
                                        prop="isEnterDetail"
                                        class="line-item large"
                                    >
                                        <el-radio-group
                                            class="line-item-field"
                                            v-model="isEnterDetail"
                                            :disabled="formType === 'look'"
                                            @change="changeIsEnterDetail"
                                        >
                                            <el-radio :label="true" :value="true" @click="marginTop = false">是</el-radio>
                                            <el-radio :label="false" :value="false" @click="marginTop = true">否</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                    <el-form-item label="不含税金额:" :show-message="false" prop="amount" class="line-item required">
                                        <Tooltip :content="invoiceForm.amount" :isInput="true">
                                            <el-input
                                                :disabled="isEnterDetail || formType === 'look'"
                                                :class="[modifyFocus ? 'modify-focus' : '', 'line-item-field invoice-text-right']"
                                                v-model="invoiceForm.amount"
                                                @input="calTaxTotal($event, 'amount')"
                                                @change="handleValueFormat($event, 'amount')"
                                                type="number"
                                                @mousewheel.native.prevent
                                                @DOMMouseScroll.native.prevent
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        label="税额:"
                                        label-width="100px"
                                        :show-message="false"
                                        prop="tax"
                                        class="line-item required"
                                    >
                                        <Tooltip :content="invoiceForm.tax" :isInput="true">
                                            <el-input
                                                :disabled="isEnterDetail || formType === 'look'"
                                                :class="[modifyFocus ? 'modify-focus' : '', 'line-item-field invoice-text-right']"
                                                v-model="invoiceForm.tax"
                                                @input="calTaxTotal($event, 'tax')"
                                                @change="handleValueFormat($event, 'tax')"
                                                type="number"
                                                @mousewheel.native.prevent
                                                @DOMMouseScroll.native.prevent
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                    <el-form-item
                                        label="价税合计:"
                                        :show-message="false"
                                        prop="adValoremTaxTotals"
                                        class="line-item required"
                                    >
                                        <Tooltip :content="invoiceForm.adValoremTaxTotals" :isInput="true">
                                            <el-input
                                                disabled
                                                :class="[modifyFocus ? 'modify-focus' : '', 'line-item-field invoice-text-right']"
                                                v-model="invoiceForm.adValoremTaxTotals"
                                            />
                                        </Tooltip>
                                    </el-form-item>
                                </div>
                                <div
                                    v-loading="loading"
                                    element-loading-text="正在加载数据..."
                                    class="main-table-block"
                                    v-if="isEnterDetail"
                                    @mouseleave="mouseEnterTable = false"
                                    @mouseenter="mouseEnterTable = true"
                                >
                                    <Table
                                        :highlight-current-row="false"
                                        ref="invoiceEditTable"
                                        :hasAddSub="
                                            !['1010', '1040', '1050', '1110'].includes(invoiceType.id) &&
                                            formType !== 'look' &&
                                            mouseEnterTable
                                        "
                                        add-sub-field="sn"
                                        :continuous-click="true"
                                        :add-row-data="tableRowAddData"
                                        :columns="
                                            ['1010', '1050'].includes(invoiceType.id)
                                                ? addColumns.slice(5)
                                                : ['1040', '1110'].includes(invoiceType.id)
                                                ? addColumns.slice(1)
                                                : addColumns
                                        "
                                        :data="
                                            !['1010', '1040', '1050', '1110'].includes(invoiceType.id) ? tableData : tableData.slice(0, 1)
                                        "
                                        :notDeleteRow="5"
                                        :customAdd="true"
                                        :customSubtract="true"
                                        :scrollbar-show="true"
                                        :height="isErp ? 266 : 222"
                                        :show-overflow-tooltip="false"
                                        v-model:tableData="tableData"
                                        :page-is-show="true"
                                        :layout="paginationData.layout"
                                        :currentPage="paginationData.currentPage"
                                        :page-sizes="[20, 50, 100]"
                                        :page-size="paginationData.pageSize"
                                        :total="paginationData.total"
                                        @current-change="handleCurrentChange"
                                        @size-change="handleSizeChange"
                                        @refresh="handleRerefresh"
                                        @handle-add="handleAdd"
                                        @handle-subtract="handleSubtract"
                                        @row-click="handleRowClick"
                                        :tableName="setModule"
                                        :isAddInvoiceTop="isAddInvoiceTop"
                                    >
                                        <template #no>
                                            <el-table-column 
                                                label="序号" 
                                                prop="no" 
                                                min-width="40"
                                                :width="getColumnWidth(setModule, 'no')"
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #header>
                                                    <span>序号</span>
                                                </template>
                                                <template #default="scope">
                                                    <div :class="['sn-no', { 'sn-disabled': formType === 'look' }]">
                                                        {{ handleInvoiceSn(scope.$index + 1) }}
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #tradeName>
                                            <el-table-column
                                                label="商品名称"
                                                prop="TradeName"
                                                min-width="120"
                                                :width="getColumnWidth(setModule, 'TradeName')"
                                                align="left"
                                                header-align="left"
                                            >
                                                <template #header>
                                                    <span class="table-required-label">商品名称</span>
                                                </template>
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            v-if="saveIndex !== scope.$index"
                                                            :title="tableData[scope.$index].name"
                                                            v-model="tableData[scope.$index].name"
                                                            :disabled="formType === 'look'"
                                                            :suffix-icon="CaretBottom"
                                                            :ref="
                                                                (el) => {
                                                                    tradeNameRef[scope.$index] = el;
                                                                }
                                                            "
                                                            @input="limitInputLength($event, scope.$index, 'name', 256, '商品名称')"
                                                            @focus="CurrentFocusGoods(scope.$index)"
                                                        />
                                                        <div v-else>
                                                            <GoodSelect
                                                                :ref="
                                                                    (el) => {
                                                                        tradeNameRef[scope.$index] = el;
                                                                    }
                                                                "
                                                                :class="'tradeNameRef' + scope.$index"
                                                                v-model="tableData[scope.$index].name"
                                                                placeholder=" "                                                        
                                                                placement="bottom-end"
                                                                filterable
                                                                :fit-input-width="true"
                                                                :bottom-html="hasPermissionGoods() ? newCDAccountHtmlGoods : ''"
                                                                :options="showGoodsTypeOptions"
                                                                :props="GoodsProp"
                                                                @bottom-click="newGoodStock(scope.$index)"
                                                                @change="handleGoodsNameChange(tableData[scope.$index].name, scope.$index)"
                                                                @focus="handleRowClickGoods(scope.$index)"
                                                                @blur="GoodsBlur"
                                                                @visible-change="visibleChange"
                                                                :remote="true"
                                                                :filter-method="GoodsTypeFilterMethod"
                                                            >
                                                            </GoodSelect>
                                                        </div>
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #specificationModel>
                                            <el-table-column 
                                                label="规格型号" 
                                                min-width="90"
                                                prop="specificationModel"
                                                :width="getColumnWidth(setModule, 'specificationModel')" 
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            :ref="
                                                                (el) => {
                                                                    tradeModelRef[scope.$index] = el;
                                                                }
                                                            "
                                                            :title="tableData[scope.$index].model"
                                                            v-model="tableData[scope.$index].model"
                                                            :disabled="formType === 'look'"
                                                            @keydown.enter="tradeUnitRef[scope.$index].focus()"
                                                            @input="limitInputLength($event, scope.$index, 'model', 256, '规格型号')"
                                                        />
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #unit>
                                            <el-table-column 
                                                label="单位" 
                                                min-width="100"
                                                prop="unit" 
                                                :width="getColumnWidth(setModule, 'unit')" 
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            :ref="
                                                                (el) => {
                                                                    tradeUnitRef[scope.$index] = el;
                                                                }
                                                            "
                                                            :title="tableData[scope.$index].unit"
                                                            v-model="tableData[scope.$index].unit"
                                                            :disabled="formType === 'look'"
                                                            @keydown.enter="tradeQualityRef[scope.$index].focus()"
                                                            @input="limitInputLength($event, scope.$index, 'unit', 64, '单位')"
                                                        />
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #number>
                                            <el-table-column 
                                                label="数量" 
                                                min-width="82" 
                                                prop="number"
                                                :width="getColumnWidth(setModule, 'number')" 
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #header>
                                                    <span :class="['1040'].includes(invoiceType.id) ? 'table-required-label' : ''"
                                                        >数量</span
                                                    >
                                                </template>
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            class="invoice-text-right"
                                                            :ref="
                                                                (el) => {
                                                                    tradeQualityRef[scope.$index] = el;
                                                                }
                                                            "
                                                            :title="tableData[scope.$index].quality"
                                                            v-model="tableData[scope.$index].quality"
                                                            @input="handleQualityInput($event, scope.$index)"
                                                            @change="handleQualityEnter($event, scope.$index)"
                                                            :disabled="formType === 'look'"
                                                            :formatter="(val:any)=>val ? val: ''"
                                                            @keydown.enter="tradeAmountRef[scope.$index].focus()"
                                                        />
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #money>
                                            <el-table-column 
                                                label="金额" 
                                                min-width="90"
                                                :width="getColumnWidth(setModule, 'amount')" 
                                                prop="amount" 
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #header>
                                                    <span class="table-required-label">金额</span>
                                                </template>
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            class="invoice-text-right"
                                                            :ref="
                                                                (el) => {
                                                                    tradeAmountRef[scope.$index] = el;
                                                                }
                                                            "
                                                            :title="tableData[scope.$index].amount"
                                                            v-model="tableData[scope.$index].amount"
                                                            :disabled="formType === 'look'"
                                                            @input="handleAmountInput($event, scope.$index)"
                                                            @change="handleAmountEnter($event, scope.$index)"
                                                            @keydown.enter="tradeTaxRateRef[scope.$index].focus()"
                                                        />
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #taxRate>
                                            <el-table-column 
                                                label="税率(%)" 
                                                min-width="85" 
                                                :width="getColumnWidth(setModule, 'taxRate')" 
                                                prop="taxRate" 
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            :ref="
                                                                (el) => {
                                                                    tradeTaxRateRef[scope.$index] = el;
                                                                }
                                                            "
                                                            :disabled="formType === 'look'"
                                                            style="width: calc(100% - 25px)"
                                                            class="line-item-field invoice-add-taxRate-input"
                                                            v-model="tableData[scope.$index].taxRate"
                                                            maxlength="3"
                                                            @input="
                                                                handleTaxRateInput(
                                                                    $event,
                                                                    scope.$index,
                                                                    String(tableData[scope.$index].amount)
                                                                )
                                                            "
                                                            @blur="taxRateHistory = []"
                                                            @focus="taxRateFocus($event, scope.$index)"
                                                            @keydown.enter="tradeTaxRef[scope.$index].focus()"
                                                        ></el-input>
                                                        <div class="tax-rate-select" style="position: relative">
                                                            <el-select
                                                                style="width: 25px"
                                                                ref="invoiceSelectRef"
                                                                :disabled="formType === 'look'"
                                                                class="line-item-field invoice-add-taxRate-select"
                                                                v-model="taxRateSelect"
                                                                placeholder=" "
                                                                :popper-append-to-body="false"
                                                                :suffix-icon="CaretBottom"
                                                                placement="bottom-end"
                                                                :fit-input-width="false"
                                                                :popper-class="taxRateClass"
                                                                @change="taxtRateChange($event, scope.$index,
                                                                    String(tableData[scope.$index].amount))"
                                                                @focus="taxRateSelectFocus($event, scope.row)"
                                                            >
                                                                <el-option
                                                                    v-for="(item, key) in taxRateOptions"
                                                                    :key="item.value"
                                                                    :label="item.label"
                                                                    :value="item.value"
                                                                />
                                                            </el-select>
                                                        </div>
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #taxAmount>
                                            <el-table-column 
                                                label="税额" 
                                                min-width="100" 
                                                prop="tax"
                                                :width="getColumnWidth(setModule, 'tax')" 
                                                align="left" 
                                                header-align="left"
                                            >
                                                <template #header>
                                                    <span class="table-required-label">税额</span>
                                                </template>
                                                <template #default="scope">
                                                    <div class="invoice-table-cell-input edit-item">
                                                        <el-input
                                                            class="invoice-text-right"
                                                            :ref="
                                                                (el) => {
                                                                    tradeTaxRef[scope.$index] = el;
                                                                }
                                                            "
                                                            :title="tableData[scope.$index].tax"
                                                            v-model="tableData[scope.$index].tax"
                                                            @input="handleTaxInput($event, scope.$index)"
                                                            @change="handleTaxEnter($event, scope.$index)"
                                                            :disabled="formType === 'look'"
                                                            @keydown.enter="
                                                                !['1040'].includes(invoiceType.id)
                                                                    ? tradeProjectOptionsFocus(scope.$index)
                                                                    : tradeCountingMethodFocus(scope.$index)
                                                            "
                                                        />
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #taxationItems>
                                            <el-table-column
                                                label="征税项目"
                                                min-width="95"
                                                :width="getColumnWidth(setModule, 'taxProject')" 
                                                prop="taxProject"
                                                align="left"
                                                header-align="left"
                                            >
                                                <template #header>
                                                    <span :class="!['1040'].includes(invoiceType.id) ? 'table-required-label' : ''"
                                                        >征税项目</span
                                                    >
                                                </template>
                                                <template #default="scope">
                                                    <div v-if="invoiceType.id !== '1040'" class="invoice-table-cell-input edit-item">
                                                        <el-select
                                                            :disabled="formType === 'look'"
                                                            :suffix-icon="CaretBottom"
                                                            :class="['line-item-field', 'tradeProjectOptionsRef' + scope.$index]"
                                                            :title="(taxProjectOptions as any)?.find((v:IEntriesSelectItem)=>v.value===tableData[scope.$index].taxProject)?.label"
                                                            :fit-input-width="true"
                                                            v-model="tableData[scope.$index].taxProject"
                                                            @focus="handleRowClick(scope.row)"
                                                            :ref="
                                                                (el) => {
                                                                    tradeProjectOptionsRef[scope.$index] = el;
                                                                }
                                                            "
                                                            @keydown.enter="tradeCountingMethodFocus(scope.$index)"
                                                            :filterable="true"
                                                            :filter-method="taxProjectFilterMethod"
                                                        >
                                                            <el-option
                                                                v-for="item in showTaxProjectOptions"
                                                                :key="item.value"
                                                                :label="item.label"
                                                                :value="item.value"
                                                            />
                                                        </el-select>
                                                    </div>
                                                    <div v-else>
                                                        {{ (taxProjectOptions as any)[0].label }}
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #taxMethod>
                                            <el-table-column
                                                label="计税方法"
                                                min-width="135"
                                                prop="taxCountingMethod"
                                                align="left"
                                                header-align="left"
                                                :resizeable="false"
                                            >
                                                <template #header>
                                                    <span :class="!['1050'].includes(invoiceType.id) ? 'table-required-label' : ''"
                                                        >计税方法</span
                                                    >
                                                </template>
                                                <template #default="scope">
                                                    <div v-if="invoiceType.id !== '1050'" class="invoice-table-cell-input edit-item">
                                                        <el-select
                                                            :disabled="formType === 'look'"
                                                            :suffix-icon="CaretBottom"
                                                            :class="['line-item-field', 'tradeCountingMethodRef' + scope.$index]"
                                                            :title="(taxMethodOptions as any)?.find((v:IEntriesSelectItem)=>v.value===tableData[scope.$index].taxCountingMethod)?.label"
                                                            :fit-input-width="true"
                                                            v-model="tableData[scope.$index].taxCountingMethod"
                                                            @focus="handleRowClick(scope.row)"
                                                            :ref="
                                                                (el) => {
                                                                    tradeCountingMethodRef[scope.$index] = el;
                                                                }
                                                            "
                                                            @keydown.enter="
                                                                tableData.length > scope.$index + 1
                                                                    ? tradeNameRef[scope.$index + 1].focus()
                                                                    : ''
                                                            "
                                                            :filterable="true"
                                                            :filter-method="taxMethodFilterMethod"
                                                        >
                                                            <el-option
                                                                v-for="item in showTaxMethodOptions"
                                                                :key="item.value"
                                                                :label="item.label"
                                                                :value="item.value"
                                                            />
                                                        </el-select>
                                                    </div>
                                                    <div v-else>
                                                        {{ (taxMethodOptions as any)[0].label }}
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                    </Table>
                                </div>
                                <div class="main-block-item">
                                    <div class="upload-title">附件</div>
                                    <div v-if="invoiceForm.attachFileCount === 0" class="upload-item" @click.stop="getAttachFileList()">
                                        <div class="upload-item-title"><img src="@/assets/Invoice/upload.png" />添加文件</div>
                                        <div class="upload-item-tip">单个文件不超过 100M</div>
                                    </div>
                                    <div v-else>
                                        <a class="link" @click.stop="getAttachFileList()">查看附件({{ invoiceForm.attachFileCount }})</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="main-button" :style="marginTop ? { 'margin-top': '20px' } : { 'margin-top': '0' }">
                            <span>
                                <a class="button solid-button mr-10" @click="saveSubmit">保存</a>
                                <a class="button" @click="EditCancel">取消</a>
                            </span>
                            <!-- <a v-else class="button" @click="EditCancel">返回</a> -->
                        </div>
                    </el-form>

                    <AddAssistingAccountingEntryDialog
                        ref="addAssistingAccountingEntryDialogRef"
                        :width="isAddProJect ? 400 : 700"
                        :title="isAddProJect ? '添加辅助核算项目' : invoiceCategory === '10070' ? '新增客户' : '新增供应商'"
                        @save-success="handleAddAssistingSuccess"
                    ></AddAssistingAccountingEntryDialog>
                    <DialogAddStockt
                        ref="dialogAddStocktRef"
                        v-model:add-stockt-show="addStocktShow"
                        @save-stockt="normalSaveStockt"
                    ></DialogAddStockt>
                    <AddAssistingAccountingEntryDialog :title="'添加商品'" ref="AddErpStockDialogRef" @save-success="erpSaveStockt">
                    </AddAssistingAccountingEntryDialog>
                    <DialogAddProject
                        ref="dialogAddProjectRef"
                        v-model:add-project-show="addProjectShow"
                        @save-project="getProjectList"
                    ></DialogAddProject>
                </div>
            </div>
        </template>
        <template #masterPlate>
            <div class="slot-content align-center" style="width: 100%">
                <MasterPlate
                    ref="masterPlateView"
                    :invoice-category="invoiceCategory"
                    @goBackAdd="() => (currentSlot = 'add')"
                    @templateEditSuccess="getBusinessTypeOptions"
                ></MasterPlate>
            </div>
        </template>
    </content-slider>
    <UploadFileDialog :readonly="readonly" ref="uploadFileDialogRef" @save="saveAttachFile" />
    <el-dialog title="提示" center v-model="voucherTip" width="440px" @close="handleTipCancel" class="dialogDrag">
        <div class="voucher-tip-box" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="box-main">
                <div style="font-size: 14px; color: #333333">发票数据已生成凭证，是否仍需修改？</div>
                <div class="mb-20 tip">
                    <img src="@/assets/Settings/tips.png" style="width: 14px; height: 14px; margin-right: 4px" />
                    <span style="color: #666666">修改会导致发票数据与凭证不一致~</span>
                </div>
                <div>
                    <el-checkbox v-model="notTip" @change="(val) => handleNoTipsChange(val)" label="不再提示" />
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleTipConfirm">确定</a>
                <a class="button ml-10" @click="handleTipCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, watchEffect, provide, nextTick } from "vue";
import dayjs from "dayjs";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { debounce } from "@/views/Statements/utils";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useAccountSetStore } from "@/store/modules/accountset";
import { CaretBottom } from "@element-plus/icons-vue";
import { usePagination } from "@/hooks/usePagination";
import ContentSlider from "@/components/ContentSlider/index.vue";
import Table from "@/components/Table/index.vue";
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import ElOption from "@/components/Option/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import Select from "@/components/Select/index.vue";
import { toDecimal2 } from "@/util/format";
import MasterPlate from "./MasterPlate.vue";
import { addColumns, containInvalidLabel, containDangerousLabel, interfaceRoute } from "../utils";
import type { IAssistingAccount } from "@/api/assistingAccounting";
import type { FormInstance } from "element-plus";
import { getCompanyList } from "@/util/getCompanyList";
import DialogAddStockt from "./DialogAddStockt.vue";
import DialogAddProject from "./DialogAddProject.vue";
import { getNextAaNum } from "@/views/Settings/AssistingAccounting/utils";
import { getCookie, setCookie } from "@/util/cookie";
import { getUrlSearchParams } from "@/util/url";
import GoodSelect from "./GoodSelect.vue";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { checkPermission } from "@/util/permission";
import { textareaBottom } from "@/views/FixedAssets/FixedAssets/utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";
import type {
    IAddTableEnterItem,
    IAddInvoiceForm,
    IEditOneData,
    ITableItem,
    IEditOneDataEntries,
    IEntriesSelectItem,
    IBusinessTypeItem,
    ITableDataRow,
    IGetTaxConfigurationInfo,
    IInvoiceStockItem,
    IResoneProjectItem,
    IInvoiceAttachFileList,
    IInvoiceModel
} from "../types";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import type { IFileInfo } from "@/components/UploadFileDialog/types";
import { onMounted} from "vue";
import type { IWordCut } from "@/util/wordCut";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = ref("addInvoice");
const { paginationData } = usePagination();
const marginTop = ref<boolean>(false);
const codeRef = ref();
const numberRef = ref();
const taxpayerNumberRef = ref();
const accountRef = ref();
const vinRef = ref();
const machineNumbersRef = ref();
const competentTaxAuthoritiesCodeRef = ref();
const addressRef = ref();
const nameRef = ref();
const noteRef = ref();
const tradeModelRef = ref<any>([]);
const tradeTaxRef = ref<any>([]);
const tradeNameRef = ref<any>([]);
const tradeAmountRef = ref<any>([]);
const tradeTaxRateRef = ref<any>([]);
const tradeUnitRef = ref<any>([]);
const tradeQualityRef = ref<any>([]);
const tradeCountingMethodRef = ref<any>([]);
const tradeProjectOptionsRef = ref<any>([]);
const customNameSelectRef = ref<InstanceType<typeof Select>>();
const issueDateRef = ref();
const textareaShow=ref(false)
const nameTextareaShow=ref(false)
const handleCurrentChange = (value: any) => {
    paginationData.currentPage = value.pageNum;
};
paginationData.pageSize = 20;
/** 改变页面大小 */
const handleSizeChange = (value: any) => {
    paginationData.pageSize = value.num;
    paginationData.currentPage = value.pageNum;
};

const invoiceSelectRef = ref();
const invoiceDateRef = ref();
//   "taxType": 1,小规模纳税人；2，一般纳税人
const taxType = useAccountSetStore().accountSet?.taxType;
const props = defineProps({
    invoiceType: {
        type: Object,
        required: true,
    },
    invoiceCategory: {
        type: String,
        required: true,
    },
    editData: {
        type: Object,
        defined: () => {},
    },
    formType: {
        type: String,
        required: true,
    },
    isProjectAccountingEnabled: {
        type: Boolean,
        default: false,
    },
    wordCutList: {
        type: Array<IWordCut>,
        default: [],
    },
    quantityFloat: {
        type: Number,
        default: 0,
    },
});
const isErp = ref(window.isErp);
let invoiceCate = computed(() => {
    return props.invoiceCategory;
});
let erpAssistingCustomList = ref<IAssistingAccount[]>([]);
let erpAssistingCustomListAll = ref<IAssistingAccount[]>([]);
let masterPlateView = ref();
const invoiceEditTable = ref<InstanceType<typeof Table>>();
const emit = defineEmits(["addInvoiceSuccess", "enterTemplatePage", "goBackMain"]);

const tableRowAddData = ref<IEditOneDataEntries>({
    sn: 1,
    name: "",
    model: "",
    unit: "",
    quality: "",
    amount: "",
    taxRate: taxType === 1 ? 3 : 13,
    tax: "",
    taxProject: 1010,
    taxCountingMethod: 1010,
});
let businessTypeOptions = ref<Array<IBusinessTypeItem>>([]);
const mouseEnterTable = ref();
let taxRateOptions = ref();
let taxProjectOptions = ref<Array<IEntriesSelectItem>>([]);
let taxMethodOptions = ref<Array<IEntriesSelectItem>>([]);
// 税控机动车专用发票
let vin = ref();
let machineNumbers = ref();
let competentTaxAuthoritiesCode = ref();
let currentSlot = ref<string>("add");
let taxRateClass = computed(() => {
    return ["1050", "1010"].includes(props.invoiceType.id)
        ? "invoice-add-taxRate-select-popper large"
        : "invoice-add-taxRate-select-popper";
});
// 字数限制在256内
const handleBlur=()=>{
    textareaShow.value=false
    const inputMaxLength=2048 //最大长度限制
    if(invoiceForm.note.length>inputMaxLength){
        ElNotify({ type: "warning", message: "亲，备注最多输入2048个字！" });
        invoiceForm.note=invoiceForm.note.slice(0,inputMaxLength)
    }
}
const inputTypeFocus = (val?:string) => {
    textareaBottom(invoiceFormRef);
    switch (val) {
        case 'note':
            textareaShow.value = true;
            break;
        case 'name':
            nameTextareaShow.value = true;
            break;
    }
    
    nextTick(()=>{
        if(val){
            getTextareaFocus(val)
        }
    })
};
const getTextareaFocus = (val:string) => {
switch (val) {
    case 'note':
        noteRef.value.focus();
        break;
    case 'name':
        nameRef.value.focus();
        break;
    }  
};

//客户名称或供应商名称
const inputBlur = () => {
    nameTextareaShow.value=false
    const inputMaxLength=256 //最大长度限制
    if(invoiceForm.name.length===inputMaxLength){
        ElNotify({ type: "warning", message: `亲，${props.invoiceCategory === "10070" ? "客户" : "供应商"}名称不能超过256个字哦~` });
    }
    
    // 摘要、备注有内容且业务类型为空时，通过摘要、备注对业务类型进行匹配
    if (invoiceForm.name !== "" && !isSelectBusinessType) {
        const wordCutList = props.wordCutList;
        let matchIndex = -1;
        for (let i = 0; i < wordCutList.length; i++) {
            const element = wordCutList[i];
            if (element.hasChild || element.mainKey.toString() !== props.invoiceCategory) continue;

            for (let j = 0; j < element.matchPartiesWithType.length; j++) {
                const matchPartyWithType = element.matchPartiesWithType[j];
                if(matchPartyWithType === '') continue;

                if (matchPartyWithType === props.invoiceType.id + "_" + invoiceForm.name.trim()) {
                    matchIndex = element.subKey;
                    break;
                }
            }

            if(matchIndex > 0) break;
        }

        if (matchIndex <= 0) {
            for (let i = 0; i < wordCutList.length; i++) {
                const element = wordCutList[i];
                if (element.hasChild || element.mainKey.toString() !== props.invoiceCategory) continue;

                for (let j = 0; j < element.matchParties.length; j++) {
                    const matchParty = element.matchParties[j];
                    if(matchParty === '') continue;

                    if (matchParty === invoiceForm.name.trim()) {
                        matchIndex = element.subKey;
                        break;
                    }
                }

                if(matchIndex > 0) break;
            }
        }

        if (matchIndex > 0) {
            isSelectBusinessTypeByParty = true;
            invoiceForm.businessType = matchIndex.toString();
        }
    }
}

//客户手动选择业务类型
const businessTypeChange = () => {
    isSelectBusinessType = true;
}


// 获取下拉框选项
getDropOptions();
// 表单
const isEnterDetail = ref(true);
let invoiceFormRef = ref<FormInstance>();
let invoiceForm = reactive<IAddInvoiceForm>({
    invoiceId: "0",
    erpNameId: "",
    projectId: null,
    invoiceType: props.invoiceType.id,
    entryDate: String(dayjs(new Date()).format("YYYY-MM-DD")),
    invoiceDate: "",
    code: "",
    number: "",
    isInvalid: 0,
    name: "",
    taxpayerNumber: "",
    address: "",
    account: "",
    note: "",
    businessType: "1",
    invoiceEntries: [],
    amount: "",
    tax: "",
    adValoremTaxTotals: "",
    issueDate: " ",
    attachFileCount: 0,
    attachFiles: "",
    isCheckOut: false,
    invoiceSource: 2,
});

let isSelectBusinessType = false; // 用户是否手动选择过业务类型
let isSelectBusinessTypeByParty = false; // 是否根据往来单位选择过业务类型

function initBusinessType(cate: string, list: IBusinessTypeItem[]) {
    if (isErp.value) return;
    businessTypeOptions.value = list;
    if (props.invoiceType?.title === "增值税普通发票" || props.invoiceType?.title === "全电发票（普通发票）") {
        if (Object.values(list).find((item) => item.label === "普票税金入成本")) {
            for (let i = 0; i < Object.values(list).length; i++) {
                if (Object.values(list)[i].label === "普票税金入成本") {
                    invoiceForm.businessType = Object.values(list)[i].value;
                }
            }
        } else {
            invoiceForm.businessType = list[0].value;
        }
    } else {
        invoiceForm.businessType = list[0].value;
    }
}
function inputLength(val: string,name: string) {
    if(val.length===2048){
        if(name==='备注'){
            ElNotify({ type: "warning", message: "亲，"+name+"不能超过2048个字哦~" });
        }
    }
}
function getBusinessTypeOptions() {
    request({
        url: `/api/InvoiceTemplate/${invoiceCate.value === "10070" ? "Sales" : "Purchase"}List`,
        method: "get",
    }).then((res: IResponseModel<ITableDataRow[]>) => {
        businessTypeOptions.value = res.data.map((item: any) => {
            return {
                label: item.businessTypeModel.name,
                value: String(item.businessTypeModel.id),
            };
        });
    });
}
//聚焦
function invoiceDateRefFocus() {
    if (!["1010", "1050", "1060", "1070"].includes(props.invoiceType.id)) {
        codeRef.value.focus();
    } else if (!["1010", "1050"].includes(props.invoiceType.id)) {
        numberRef.value.focus();
    } else if (!["1050"].includes(props.invoiceType.id)) {
        if (isErp.value) {
            customVendorSelectRef.value.focus();
            customVendorSelectRefFocus();
        } else {
            nameRef.value.focus();
        }
    } else {
        props.isProjectAccountingEnabled ? projectAccountingFocus() : noteRef.value.focus();
    }
}
const isInvalidRef = ref();
function numberFocus() {
    if (!["1010", "1050"].includes(props.invoiceType.id)) {
        isInvalidRef.value.focus();
        isInvalidRef.value.dropMenuVisible = true;
        const inputE = document.querySelector(`.isInvalidRef .el-input__inner`);
        inputE?.addEventListener("keydown", (e: any) => {
            if (e.keyCode === 13) {
                isInvalidFocus();
            }
        });
    } else {
        nameRef.value.focus();
    }
}
const customVendorSelectRef = ref();
function isInvalidFocus() {
    if (!["1050"].includes(props.invoiceType.id)) {
        if (isErp.value) {
            customVendorSelectRef.value.focus();
            customVendorSelectRefFocus();
        } else {
            nameRef.value.focus();
        }
    }
}
function projectAccountingFocus() {
    customNameSelectRef.value?.focus();
    (customNameSelectRef.value as any).getSelect().dropMenuVisible = true;
    const inputE = document.querySelector(`.customNameSelectRef .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            noteRef.value.focus();
            // invoiceCate.value === "10070" ? noteRef.value.focus() : issueDateRef.value.focus();
        }
    });
}
function tradeProjectOptionsFocus(index: number) {
    tradeProjectOptionsRef.value[index].focus();
    tradeProjectOptionsRef.value[index].dropMenuVisible = true;
    const inputE = document.querySelector(`.tradeProjectOptionsRef${index} .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            tradeCountingMethodFocus(index);
        }
    });
}
function tradeCountingMethodFocus(index: number) {
    tradeCountingMethodRef.value[index].focus();
    tradeCountingMethodRef.value[index].dropMenuVisible = true;
    const inputE = document.querySelector(`.tradeCountingMethodRef${index} .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            tableData.value.length > index + 1 ? tradeNameRef.value[index + 1].focus() : "";
        }
    });
}
function customVendorSelectRefFocus() {
    const inputE = document.querySelector(`.customVendorSelectRef .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            customVendorSelectRef.value.blur();
            taxpayerNumberRef.value.focus();
        }
    });
}

const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
const querySearch = (queryString: string, cb: any) => {
    if (queryString.length > 50) {
      cb([]);
    }else{
        nextTick(()=>{
            const autocompleteHeight=document.querySelector('.autocomplete-input .el-autocomplete__popper') as HTMLElement;
            const textareaHeight=document.querySelector('.autocomplete-input .el-textarea__inner') as HTMLElement;
            if(textareaHeight&&autocompleteHeight){
                const height=textareaHeight.style.height
                switch (height) {
                    case '31px':
                        autocompleteHeight.style.marginTop = '-6px';
                        break;
                    case '52px':
                        autocompleteHeight.style.marginTop = '8px';
                        break;
                    case '73px':
                        autocompleteHeight.style.marginTop = '29px';
                        break;
                }
            }
        })
        
      getCompanyList(1070, queryString, cb, queryParams);
    }
};


const isAddProJect = ref(false);
const newCdAccount = (c?: number) => {
    customNameSelectRef.value?.getSelect()?.blur();
    let code = c ? c : invoiceCate.value === "10070" ? 10001 : 10002;
    isAddProJect.value = Boolean(c);
    let name = autoAddName.value;
    let timer = setTimeout(() => {
        addAssistingAccountingEntryDialogRef.value?.showAADialog(code, name);
        clearTimeout(timer);
    }, 0);
};

function handleSelect(val: any) {
    invoiceForm.name = val.value;
    invoiceForm.taxpayerNumber = val.creditCode;
    getCompanyDetailApi(1070, decodeURIComponent(val.value));
}

//机动车/二手车销售统一发票
const vehicleType = ref("");
const vehicleTypeRef = ref();
const brandModel = ref("");
const brandModelRef = ref();
const engineNUM = ref("");
const engineNUMRef = ref();
const vehicleLicence = ref("");
const vehicleLicenceRef = ref();

const defaultInitial = ref<Array<IEditOneDataEntries>>([
    {
        sn: 1,
        name: "",
        model: "",
        unit: "",
        quality: "",
        amount: "",
        taxRate: taxType === 1 ? 3 : 13,
        tax: "",
        taxProject: 1010,
        taxCountingMethod: 1010,
    },
    {
        sn: 2,
        name: "",
        model: "",
        unit: "",
        quality: "",
        amount: "",
        taxRate: taxType === 1 ? 3 : 13,
        tax: "",
        taxProject: 1010,
        taxCountingMethod: 1010,
    },
    {
        sn: 3,
        name: "",
        model: "",
        unit: "",
        quality: "",
        amount: "",
        taxRate: taxType === 1 ? 3 : 13,
        tax: "",
        taxProject: 1010,
        taxCountingMethod: 1010,
    },
    {
        sn: 4,
        name: "",
        model: "",
        unit: "",
        quality: "",
        amount: "",
        taxRate: taxType === 1 ? 3 : 13,
        tax: "",
        taxProject: 1010,
        taxCountingMethod: 1010,
    },
    {
        sn: 5,
        name: "",
        model: "",
        unit: "",
        quality: "",
        amount: "",
        taxRate: taxType === 1 ? 3 : 13,
        tax: "",
        taxProject: 1010,
        taxCountingMethod: 1010,
    },
]);
let tableAllData = ref<Array<IEditOneDataEntries>>(defaultInitial.value.slice());

let loading = ref(false);
let tableData = ref<Array<IEditOneDataEntries>>(defaultInitial.value.slice());
const enterWay = ref(false); //true编辑，false新增
const beforeProjectId = ref();
const isAddInvoiceTop = ref(false); //发票新增明细滚动置顶

const handleRerefresh = () => {
    isAddInvoiceTop.value = true;
    getInvoiceEntriesData();
};
async function initEditMainForm(data: ITableItem, flag?: boolean) {
    if (isErp.value) {
        await getErpAssistingAccounting();
    }
    isShowEditProjectErp();
    getProjectList();
    invoiceForm.invoiceId = data.invoiceId;
    invoiceForm.invoiceType = data.invoiceType;
    invoiceForm.entryDate = data.entryDate;
    invoiceForm.invoiceDate = data.invoiceDate || "";
    invoiceForm.code = data.code || "";
    invoiceForm.number = data.number || "";
    invoiceForm.isInvalid = data.isInvalid;
    invoiceForm.name = data.name || "";
    invoiceForm.taxpayerNumber = data.taxpayerNumber?.toString() || "";
    invoiceForm.address = data.address || "";
    invoiceForm.account = data.account || "";
    invoiceForm.note = data.note || "";
    invoiceForm.businessType = String(data.businessType);
    invoiceForm.invoiceEntries = [];
    invoiceForm.amount = Number(data.amount).toFixed(2);
    invoiceForm.tax = Number(data.tax).toFixed(2);
    invoiceForm.adValoremTaxTotals = calTaxTotal(data.amount);
    invoiceForm.issueDate = data.issueDateText?.includes("0001-01-01") ? "" : data.issueDateText;
    invoiceForm.attachFileCount = data.attachFileCount;
    invoiceForm.isCheckOut = data.isCheckOut;
    invoiceForm.invoiceSource = data.invoiceSource;
    if (isErp.value) {
        invoiceForm.erpNameId = data.erpNameId;
    }
    invoiceForm.projectId = data.projectId ? data.projectId : null;
    beforeProjectId.value = invoiceForm.projectId;
    loading.value = true;
    enterWay.value = true;
    isSelectBusinessType = props.formType === "add" ? false : true;
    isSelectBusinessTypeByParty = props.formType === "add" ? false : true;
    isNeedSaveToVoucherForDelete = false;

    request({
        url: `/api/Invoice`,
        method: "get",
        params: {
            invoiceType: data.invoiceType,
            invoiceId: data.invoiceId,
        },
    })
        .then((res: IResponseModel<IInvoiceModel>) => {
            if (res.state === 1000) {
                invoiceForm.isCheckOut = res.data.isCheckOut;
                let editOneData = res.data.invoiceData;
                initEditForm(editOneData, flag);
                loading.value = false;
                tableAllData.value = res.data.invoiceData.invoiceEntries.map((item: IEditOneDataEntries) => {
                    return {
                        ...item,
                        amount: Number(item.amount).toFixed(2),
                        tax: Number(item.tax).toFixed(2),
                        taxRate: item.taxRate === -101 ? "不征税" : item.taxRate === -100 ? "免税" : item.taxRate,
                        quality: isErp.value ? Number(item.quality).toFixed(props.quantityFloat) : item.quality
                    };
                });
                paginationData.total = tableAllData.value.length;
                getInvoiceEntriesData();
                changeAddSlotInit(true);
            }
        })
        .catch((e) => {
            console.log(e);
        });
}
function getInvoiceEntriesData() {
    tableData.value = [];
    tableData.value = tableAllData.value.slice(
        (paginationData.currentPage - 1) * paginationData.pageSize,
        paginationData.currentPage * paginationData.pageSize
    );
    // if (tableAllData.value.length < 5 && isErp.value) {
    //     for (let i = tableAllData.value.length; i < 5; i++) {
    //         tableAllData.value[i] = { ...tableRowAddData.value, sn: i + 1 };
    //     }
    //     paginationData.total = 5;
    //     paginationData.currentPage = 1;
    //     tableData.value = tableAllData.value;
    // }
    invoiceEditTable.value?.initEditData(tableAllData.value);
    isAddInvoiceTop.value = false;
}
function handleCustomerOrVendorVisibleChange() {
    if (!invoiceForm.erpNameId) return;
    if (!erpAssistingCustomList.value?.find((v) => v.aaeid.toString() === invoiceForm.erpNameId)) {
        const item = erpAssistingCustomListAll.value?.find((v) => v.aaeid.toString() === invoiceForm.erpNameId);
        if (item) {
            const el = customVendorSelectRef.value?.getSelect()?.$el?.querySelector(".el-input__inner") as HTMLInputElement;
            if (el) {
                nextTick().then(() => {
                    el.value = item.aanum + " " + item.aaname;
                });
            }
        }
    }
}
function initEditForm(editRowData: IEditOneData, flag?: boolean) {
    invoiceForm.invoiceId = editRowData.invoiceId;
    invoiceForm.invoiceType = editRowData.invoiceType;
    invoiceForm.entryDate = editRowData.entryDate;
    invoiceForm.invoiceDate = editRowData.invoiceDate;
    invoiceForm.code = editRowData.code || "";
    invoiceForm.number = editRowData.number;
    invoiceForm.isInvalid = editRowData.isInvalid;
    invoiceForm.name = editRowData.name;
    if (isErp.value) {
        invoiceForm.erpNameId = editRowData.erpNameId;
        handleCustomerOrVendorVisibleChange();
    }
    invoiceForm.projectId = editRowData.projectId ? editRowData.projectId : null;
    invoiceForm.taxpayerNumber = editRowData.taxpayerNumber;
    invoiceForm.address = editRowData.address;
    invoiceForm.account = editRowData.account;
    invoiceForm.note = editRowData.note;
    if (!flag) {
        invoiceForm.businessType = String(editRowData.businessType);
    }
    invoiceForm.invoiceEntries = [];
    invoiceForm.amount = Number(editRowData.amount).toFixed(2);
    invoiceForm.tax = Number(editRowData.tax).toFixed(2);
    invoiceForm.adValoremTaxTotals = calTaxTotal(editRowData.amount);
    invoiceForm.issueDate = editRowData.issueDateText?.includes("0001-01-01") ? "" : editRowData.issueDateText;
    vin.value = editRowData.vin ? editRowData.vin : "";
    machineNumbers.value = editRowData.machineNumbers ? editRowData.machineNumbers : "";
    competentTaxAuthoritiesCode.value = editRowData.competentTaxAuthoritiesCode ? editRowData.competentTaxAuthoritiesCode : "";
    vehicleType.value = editRowData.vehicleType ? editRowData.vehicleType : "";
    brandModel.value = editRowData.brandModel ? editRowData.brandModel : "";
    engineNUM.value = editRowData.engineNUM ? editRowData.engineNUM : "";
    vehicleLicence.value = editRowData.vehicleLicence ? editRowData.vehicleLicence : "";
    isEnterDetail.value = editRowData.invoiceEntries.length > 0 ? true : false;
    invoiceForm.attachFiles = editRowData.attachFiles;
    invoiceForm.attachFileCount = editRowData.attachFiles ? editRowData.attachFiles.split(",").length : 0;
    attachParentId.value = 0;
}
watch([() => paginationData.currentPage, () => paginationData.pageSize], (v) => {
    isAddInvoiceTop.value = true;
    getInvoiceEntriesData();
});
const newCDAccountHtml = ref(`<li class="new-cd-account">
          <a class='add-name-btn'>
              +新增客户
          </a>
      </li>`);
const newCDAccountHtmlG = ref(`<li class="new-cd-account">
          <a class='add-name-btn'>
              +新增供应商
          </a>
      </li>`);
//业财客户供应商新增是否权限
const hasPermissionCustomerOrVenor = () => {
    if (invoiceCate.value === "10070") {
        return checkPermission(['Customers-编辑']); 
    } else {
        return checkPermission(['Vendors-编辑']);
    }
}
//商品存货是否有新增的权限
const hasPermissionGoods = () => {
    if (isErp.value) {
        return checkPermission(['ProductManager-编辑']);
    } else {
        return checkPermission(['assistingaccount-canedit']);
    }
}
async function getErpAssistingAccounting(data?: any) {
    const path = invoiceCate.value === "10070" ? "CustomerList" : "VendorList";
    request({
        url: `/api/AssistingAccounting/${path}?showAll=true`,
        method: "get",
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            let allList: IAssistingAccount[] = [];
            let list: IAssistingAccount[] = [];
            for (let i = 0; i < res.data.length; i++) {
                const item = res.data[i];
                if (item.aaeid > 0) {
                    allList.push(item);
                    if (item.status === 0) {
                        list.push(item);
                    }
                }
            }
            erpAssistingCustomList.value = list;
            erpAssistingCustomListAll.value = allList;
            if (data) {
                handleAddAssistingSuccessNext(data);
            }
        }
    });
    
}
function handleErpNameCheck(aaeid: string) {
    let target = erpAssistingCustomList.value?.find((v) => v.aaeid.toString() === aaeid) as IAssistingAccount;
    invoiceForm.name = target.aaname;
    getErpNameCode(aaeid);
}
async function getErpNameCode(aaeid: string) {
    const res = await getAssistingAccountingCode(aaeid);
    if(res.state === 1000 && res.data && res.data.taxNumber) {
        invoiceForm.taxpayerNumber = res.data.taxNumber;
        return;
    }
    const r = await getCompanyDetailApi(1070, decodeURIComponent(invoiceForm.name));
    if (r.state === 1000 && r.data && r.data.creditCode) {
        invoiceForm.taxpayerNumber = r.data.creditCode;
        return;
    }
    invoiceForm.taxpayerNumber = "";
}
function getAssistingAccountingCode(aaeid: string) {
    const path = invoiceCate.value === "10070" ? "CustomerModel" : "VendorModel";
    return request({ 
        url: "/api/AssistingAccounting/" + path,
        method: "get", 
        params: { aaeId: aaeid },
    });
}
const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
const handleAddAssistingSuccess = (data: any) => {
    useAssistingAccountingStore()
        .getAssistingAccounting()
        .then(async () => {
            await getErpAssistingAccounting(data);
        });
};
//序号
const handleInvoiceSn = (no: number) => {
    return paginationData.currentPage === 1 ? no : (paginationData.currentPage - 1) * paginationData.pageSize + no;
};
//发票明细商品名称
const newCDAccountHtmlGoods = `<li style='display: flex; justify-content: center'>
      <a style='font-size: 12px;color: var(--main-color); line-height: 34px;'>
          +点击添加
      </a>
  </li>`;
const GoodsTypeOptions = ref<Array<IInvoiceStockItem>>([]);
const GoodsProp = {
    value: "value",
    label: "name",
};
function escapeRegExp(aanum: string) {
    return aanum.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
const GoodsListData = () => {
    return new Promise((resolve) => {
        request({
        url: "/api/AssistingAccounting/ListWithAcronym",
    }) .then((res:any) => {
        let list: IInvoiceStockItem[] = [];
        for (let i = 0; i < res.data.length; i++) {
            const item = res.data[i];
            if (item.aatype === 10006 && item.aaeid > 0 && item.status === 0) {
                const pushItem = {
                    name: item.aaname.replace(new RegExp(`^${escapeRegExp(item.aanum)} `), ""),
                    value: item.aaeid,
                    num: item.aanum,
                    model: item.model ? item.model.stockModel : '',
                    unit: item.model ? item.model.unit : '',
                };
                list.push(pushItem);
            }
        }
        GoodsTypeOptions.value = JSON.parse(JSON.stringify(list));
        resolve(true);
    });
    });
};
const dialogAddStocktRef = ref();
const addStocktShow = ref(false);
const AddErpStockDialogRef = ref();
const CurrentFocusGoods = (index: number) => {
    saveIndex.value = index;
    nextTick(() => {
        tradeNameRef.value[index].focus();
    });
};
const handleGoodsNameChange = (val: string, index: number) => {
    tradeNameRef.value[index].focus();
    let item = GoodsTypeOptions.value.find((item: IInvoiceStockItem) => item.value === Number(val));
    if (item) {
        tableData.value[index].name = item.name;
        tableData.value[index].model = item.model;
        tableData.value[index].unit = item.unit;
        tradeNameChange();
        saveIndex.value = -1;
        // let params = {
        //     showAll: false,
        //     pageIndex: 1,
        //     pageSize: 20,
        //     searchStr: "",
        // };
        // params.searchStr = item.name;
        // request({
        //     url: "/api/AssistingAccounting/PagingStockList?" + getUrlSearchParams(params),
        //     method: "get",
        // }).then((res) => {
        //     if (res.state === 1000) {
        //         let result = res.data.data.find((item:IResoneStockItem) => item.aaeId === Number(val));
        //         console.log(val,result)
        //         if (result) {
        //             tableData.value[index].name = result.aaName;
        //             tableData.value[index].model = result.stockModel;
        //             tableData.value[index].unit = result.unit;
        //             tradeNameChange();
        //         }
        //         saveIndex.value = -1;
        //     }
        // });
    }
};
const saveIndex = ref(-1);
const handleRowClickGoods = (index: number) => {
    // saveIndex.value = index;
    handleRowClick(tableData.value[index]);
    // const inputE = document.querySelector(`.tradeNameRef${index} .el-input__inner`);
    const inputE = document.querySelector(`.tradeNameRef${index} .el-select-v2__combobox-input`);
    inputE?.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            tradeModelRef.value[index].focus();
        }
    });
};
const GoodsBlur = (event: FocusEvent) => {
    const element =(event.target as HTMLInputElement);
    const oldName = tableData.value[saveIndex.value].name;
    tableData.value[saveIndex.value].name = element.value ? element.value : tableData.value[saveIndex.value].name;
    let result = GoodsTypeOptions.value.filter((item: IInvoiceStockItem) => item.name === tableData.value[saveIndex.value].name);
    if (result.length === 0) {
        tableData.value[saveIndex.value].model = "";
        tableData.value[saveIndex.value].unit = "";
    }

    if(oldName !== tableData.value[saveIndex.value].name) {
        tradeNameChange();
    }
};

const tradeNameChange = () => {
    // 摘要、备注有内容且业务类型为空时，通过摘要、备注对业务类型进行匹配
    if (tableData.value[saveIndex.value].name !== "" && !isSelectBusinessType && !isSelectBusinessTypeByParty) {
        const wordCutList = props.wordCutList;
        let matchIndex = -1;
        for (let i = 0; i < wordCutList.length; i++) {
            const element = wordCutList[i];
            if (element.hasChild || element.mainKey.toString() !== props.invoiceCategory) continue;

            for (let j = 0; j < element.matchKeywords.length; j++) {
                const matchKeyword = element.matchKeywords[j];
                if(matchKeyword === '') continue;

                if (matchKeyword === tableData.value[saveIndex.value].name.trim()) {
                    matchIndex = element.subKey;
                    break;
                }
            }

            if(matchIndex > 0) break;
        }

        if (matchIndex > 0) {
            invoiceForm.businessType = matchIndex.toString();
        }
    }
}

const visibleChange = (visible: boolean) => {
    if(!visible) {
       if(typeof tableData.value[saveIndex.value].name === "string") {
            saveIndex.value = -1;
       }
    } else {
        showGoodsTypeOptions.value = JSON.parse(JSON.stringify(GoodsTypeOptions.value));
    }
}
let currentIndex = ref(-1);
const newGoodStock = (index: number) => {
    currentIndex.value = index;
    tradeNameRef.value[index].blur();
    autoAddName.value = showGoodsTypeOptions.value.length === 0 ? tableData.value[index].name : "";
    if (!isErp.value) {
        addStocktShow.value = true;
        dialogAddStocktRef.value?.changeCode(autoAddName.value);
    } else {
        AddErpStockDialogRef.value?.showAADialog(10006, autoAddName.value);
    }
};
const normalSaveStockt = async (data: string) => {
    if (data) {
        let result = await GoodsListData();
        if (result) {
            handleGoodsNameChange(data, currentIndex.value);
        }
    }
};
const erpSaveStockt = async (data: any) => {
    if (data) {
        let result = await GoodsListData();
        if (result) {
            handleGoodsNameChange(data.aaeId, currentIndex.value);
        }
    }
};
function handleAddAssistingSuccessNext(data: any) {
    if (data) {
        if (isAddProJect.value) {
            invoiceForm.projectId = data.aaeId;
            return;
        }
        invoiceForm.erpNameId = data.aaeId.toString();
        handleErpNameCheck(data.aaeId.toString());
    }
}
// 项目
const newCDAccountHtmlProject = `<li class="new-cd-account">
          <a class='new-cd-project'>
              +点击添加
          </a>
      </li>`;
const addProjectShow = ref(false);
const dialogAddProjectRef = ref();
const erpAssistingProjectList = ref<Array<IResoneProjectItem>>([]);
const newProject = () => {
    addProjectShow.value = true;
    dialogAddProjectRef.value?.changeName(autoAddName.value);
    getNextAaNum(10005).then((res: any) => {
        if (res.state === 1000) {
            const code = res.data;
            dialogAddProjectRef.value?.changeCode(code);
        }
    });
};
function getProjectList(code?: number) {
    request({
        url: "/api/AssistingAccounting/ProjectList?showAll=true",
        method: "get",
    }).then((res) => {
        if (res.state === 1000) {
            erpAssistingProjectList.value = res.data.filter((v: IResoneProjectItem) => v.aaeid > 0);
            invoiceForm.projectId = code ? code : invoiceForm.projectId;
        }
    });
}
const isShowEditProject = ref(true);
function isShowEditProjectErp() {
    if (isErp.value) {
        isShowEditProject.value = props.isProjectAccountingEnabled;
    }
}
onMounted(() => {
    getProjectList();
    GoodsListData();
});
function initPage(id: string, isReset: boolean = true) {
    isShowEditProjectErp();
    isReset && resetField();
    if (isErp.value) {
        getErpAssistingAccounting();
    }
    paginationData.total = ["1010", "1040", "1050", "1110"].includes(id) ? 1 : 5;
    paginationData.pageSize = 20;
    tableAllData.value = defaultInitial.value.slice();
    tableData.value = tableAllData.value;
}
// 初始化表单数据

function resetField() {
    invoiceFormRef.value?.resetFields();
    invoiceForm.invoiceId = "0";
    invoiceForm.erpNameId = "";
    invoiceForm.projectId = null;
    invoiceForm.invoiceType = props.invoiceType.id;
    invoiceForm.entryDate = String(dayjs(new Date()).format("YYYY-MM-DD"));
    invoiceForm.invoiceDate = "";
    invoiceForm.code = "";
    invoiceForm.number = "";
    invoiceForm.isInvalid = 0;
    invoiceForm.name = "";
    invoiceForm.taxpayerNumber = "";
    invoiceForm.address = "";
    invoiceForm.account = "";
    invoiceForm.note = "";
    invoiceForm.businessType = "1";
    invoiceForm.invoiceEntries = [];
    invoiceForm.amount = "";
    invoiceForm.tax = "";
    invoiceForm.adValoremTaxTotals = "";
    invoiceForm.issueDate = "";
    invoiceForm.invoiceSource = 2;
    tableAllData.value = defaultInitial.value.slice();
    tableData.value = defaultInitial.value.slice();
    paginationData.total = 5;
    paginationData.currentPage = 1;
    provide("currentPage", 1);
    // 税控机动车专用发票
    vin.value = "";
    machineNumbers.value = "";
    competentTaxAuthoritiesCode.value = "";
    //机动车二手车
    vehicleType.value = "";
    brandModel.value = "";
    engineNUM.value = "";
    vehicleLicence.value = "";
    isEnterDetail.value = true;
    
    isSelectBusinessType = false;
    isSelectBusinessTypeByParty = false;
}
provide("currentPage", 1);
//编辑状态
let init = false;
const addSlotIsEditting = ref(false);
const getAddSlotIsEditting = () => {
    return addSlotIsEditting.value;
};
const changeAddSlotInit = (val: boolean) => {
    nextTick(() => {
        init = val;
    });
};
watch(
    invoiceForm,
    () => {
        if (!init) return;
        addSlotIsEditting.value = true;
    },
    { deep: true }
);
defineExpose({
    initEditForm,
    initBusinessType,
    resetField,
    initEditMainForm,
    initPage,
    debounceSaveSubmit,
    getAddSlotIsEditting,
    changeAddSlotInit,
});
function handleDateInput(e: FocusEvent, props: string) {
    const regex = /^(\d{4})-(\d{0,2})-(\d{0,2})(?!\d)/;
    (e.target as any).value = regex.test((e.target as any).value) ? (e.target as any).value : "";
    (invoiceForm as any)[props] = (e.target as any).value;
}
function handleDateFocus(e: FocusEvent, props: string) {
    let propsClass: { [key: string]: string } = {
        invoiceDate: "invoice-date",
        issueDate: "issue-date",
    };
    let dateInput = document.querySelector(`.${propsClass[props]} .el-input__inner`);
    dateInput?.addEventListener("input", (event: Event) => {
        let input = (event.target as any).value;
        (dateInput as any).value = formatDateString(input);
        if ((dateInput as any).value.length > 10) {
            (dateInput as any).value = (dateInput as any).value.slice(0, 10);
        }
    });
}
function formatDateString(input: string) {
    const regex = /^(\d{4})-?(\d{0,2})-?(\d{0,2})(?!\d)/;
    const formatted = input.replace(regex, (match: any, year: string, month: string, day: string) => {
        if (month) {
            month = Number(month) > 12 ? "0" + month.substring(0, 1) : month;
        }
        if (day) {
            day = Number(day) > 31 ? "0" + day.substring(0, 1) : day;
        }
        return month && day ? `${year}-${month}-${day}` : month ? `${year}-${month}` : year;
    });
    return formatted;
}
let savetaxProjectOptions: IEntriesSelectItem[] = [];
function getDropOptions() {
    request({
        url: "/api/Invoice/GetTaxConfigurationInfo",
        method: "post",
    })
        .then((res: IResponseModel<IGetTaxConfigurationInfo>) => {
            let taxRateInfo = res.data.taxRateInfo;
            let arr1: IEntriesSelectItem[] = [];
            let arr2: IEntriesSelectItem[] = [];
            let arr3: IEntriesSelectItem[] = [];

            for (let key in taxRateInfo) {
                arr1.push({ value: Number(key), label: taxRateInfo[key] });
            }
            taxRateOptions.value = arr1.sort((a, b) => b.value - a.value);

            for (let key in res.data.taxProjectInfo) {
                arr2.push({ value: Number(key), label: res.data.taxProjectInfo[key] });
            }
            for (let key in res.data.taxCountingMethodInfo) {
                arr3.push({
                    value: Number(key),
                    label: res.data.taxCountingMethodInfo[key],
                });
            }
            taxProjectOptions.value = arr2;
            taxMethodOptions.value = arr3;
            savetaxProjectOptions = JSON.parse(JSON.stringify(arr2));
            tableData.value.forEach((v) => {
                v.taxProject = (taxProjectOptions.value as any)[0].value;
                v.taxCountingMethod = (taxMethodOptions.value as any)[0].value;
            });
        })
        .catch((e) => {
            console.log(e);
        });
}
watch(
    () => props.invoiceType.id,
    (val) => {
        if (val === "1110") {
            taxProjectOptions.value = savetaxProjectOptions.slice(0, 1);
        }
    }
);
let modifyFocus = ref(false);
let isEnterAmount = ref("");
let isEntertax = ref("");
function changeIsEnterDetail(v: any) {
    if (v) {
        let flag = tableAllData.value.findIndex((x) => x.amount !== "" || x.tax !== "");

        if (((invoiceForm.amount && invoiceForm.amount !== "0.00") || (invoiceForm.tax && invoiceForm.tax !== "0.00")) && flag === -1) {
            isEnterDetail.value = false;
            ElConfirm("亲，您已录入发票数据，确定要清除，重新录入发票明细吗？").then((r: boolean) => {
                if (r) {
                    invoiceForm.adValoremTaxTotals = "";
                    invoiceForm.amount = "";
                    invoiceForm.tax = "";
                    isEnterDetail.value = true;
                    modifyFocus.value = true;
                    initPage(props.invoiceType.id, false);
                } else {
                    isEnterDetail.value = false;
                }
            });
        } else {
            //手动改变未保存，则变原来的值
            invoiceForm.amount = isEnterAmount.value;
            invoiceForm.tax = isEntertax.value;
        }
    } else {
        //保存录入发票明细为true状态时，不含税金额，税额）
        isEnterAmount.value = invoiceForm.amount;
        isEntertax.value = invoiceForm.tax;
    }
}
function limitInputLength(val: string, index: number, prop: string, len: number, label: string) {
    if (val.length > len) {
        ElNotify({
            type: "warning",
            message: `亲，${label}不能超过${len}个字哦~`,
        });
        (tableData.value as any)[index][prop] = val.slice(0, len);
    } else {
        (tableData.value as any)[index][prop] = val;
    }
}
let taxRateHistory: string[] = [tableRowAddData.value.taxRate.toString()];
// 税率变，税额变
function handleTaxRateInput(e: any, index: number, amount: string): void {
    let val = e.replace(/[^\d]/g, "");
    if (e > 100) {
        tableData.value[index].taxRate = val.slice(0, 2);
    } else {
        tableData.value[index].taxRate = val;
    }
    handleAmountInput(amount, index);
    calculateDetailedTotal();
}
const taxRateSelect = ref(-1);
function taxtRateChange(e: any, index: number, amount: string) {
    if (taxRateSelect.value === -101) {
        tableData.value[index].taxRate = "不征税";
    } else if(taxRateSelect.value === -100) {
        tableData.value[index].taxRate = "免税";
    } else {
        tableData.value[index].taxRate = e;
    }
    handleAmountInput(amount, index);
}
function taxRateSelectFocus(e: any, row: IEditOneDataEntries) {
    handleRowClick(row);
    if (row.taxRate === "不征税") {
        taxRateSelect.value = -101;
    } else if(row.taxRate === "免税") {
        taxRateSelect.value = -100;
    } else {
        taxRateSelect.value = Number(row.taxRate);
    }
}
function taxRateFocus(e: any, index: number) {
    if (tableData.value[index].taxRate) {
        let inputElement = tradeTaxRateRef.value[index].$refs.input;
        // 获取光标聚焦的位置
        inputElement.selectionStart = 0;
        inputElement.selectionEnd = String(tableData.value[index].taxRate).length;
    }
}

function calculateDetailedTotal() {
    let amount: number = 0,
        tax: number = 0;
    tableAllData.value.forEach((v) => {
        amount += Number(Number(v.amount).toFixed(2));
        tax += Number(Number(v.tax).toFixed(2));
    });
    if (tax === -0.0) {
        tax = 0;
    }
    invoiceForm.amount = amount.toFixed(2);
    invoiceForm.tax = tax.toFixed(2);

    invoiceForm.adValoremTaxTotals = amount + tax ? String((amount + tax).toFixed(2)) : "";
}
let taxAmountHistory: string[] = [];
// 金额变，税额变
function handleAmountInput(e: any, index: number): void {
    taxAmountHistory.push(e);
    if (e === "" || e === "-") {
        tableData.value[index].amount = e;
        tableData.value[index].tax = "";
        e === "" && calculateDetailedTotal();
        return;
    }
    if ((isNaN(parseFloat(e)) || !isFinite(e)) && e !== "-") {
        taxAmountHistory.pop();
        tableData.value[index].amount = taxAmountHistory[taxAmountHistory.length - 1];
        return;
    }
    if (Math.abs(Number(e)) >= 1000000000) {
        taxAmountHistory.pop();
        tableData.value[index].amount = taxAmountHistory[taxAmountHistory.length - 1];
    }
    tableData.value[index].amount = String(tableData.value[index].amount).replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
    if (["不征税", "免税"].includes(tableData.value[index].taxRate.toString())) {
        tableData.value[index].tax = "0.00";
    } else {
        tableData.value[index].tax = tableData.value[index].taxRate
        ? toDecimal2(String(Number((Number(tableData.value[index].amount) * Number(tableData.value[index].taxRate)) / 100)))
        : "0.00";
    }
    calculateDetailedTotal();
}
function handleAmountEnter(e: any, index: number) {
    taxAmountHistory = [];
    tableData.value[index].amount = toDecimal2(String(tableData.value[index].amount));
    calculateDetailedTotal();
}
// 数量
let qualityHistory: string[] = [];
function formateQuality(val: any) {
    if (val) {
        if (val.includes(".") && val.split(".")[1].length > props.quantityFloat) {
            return Number(val).toFixed(props.quantityFloat);
        }
        return val;
    }
    return "";
}
function handleQualityInput(e: any, index: number): void {
    qualityHistory.push(e);

    if (e === "" || e === "-") {
        tableData.value[index].quality = e;
        return;
    }
    if ((isNaN(parseFloat(e)) || !isFinite(e)) && e !== "-") {
        qualityHistory.pop();
        tableData.value[index].quality = qualityHistory[qualityHistory.length - 1];
        return;
    }
    
    const maxFloat = isErp.value ? props.quantityFloat : 8;
    if (Math.abs(Number(e)) > 1000000000 || (e.includes(".") && e.split(".")[1].length > maxFloat)) {
        qualityHistory.pop();
        tableData.value[index].quality = qualityHistory[qualityHistory.length - 1];
    }
}
function handleQualityEnter(e: any, index: number): void {
    qualityHistory = [];

    if (e[e.length - 1] === ".") {
        tableData.value[index].quality = e.slice(0, -1);
    } else if (e === "-") {
        tableData.value[index].quality = "";
    }
}
//不含税金额
let calTaxHistory: string[] = [];
function calTaxTotal(e: any, prop?: any): string | undefined {
    calTaxHistory.push(e);

    if (Math.abs(Number(e)) > 1000000000) {
        calTaxHistory.pop();
        prop &&
            (prop === "amount"
                ? (invoiceForm.amount = calTaxHistory[calTaxHistory.length - 1])
                : (invoiceForm.tax = calTaxHistory[calTaxHistory.length - 1]));
    }
    if (prop === "tax") {
        if (Math.abs(Number(e)) > Math.abs(Number(invoiceForm.amount))) {
            calTaxHistory.pop();
            invoiceForm.tax = calTaxHistory[calTaxHistory.length - 1];
        }
    }

    invoiceForm.tax = invoiceForm.tax.replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
    invoiceForm.amount = invoiceForm.amount.replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
    invoiceForm.adValoremTaxTotals = String((Number(invoiceForm.amount) + Number(invoiceForm.tax)).toFixed(2));

    return invoiceForm.adValoremTaxTotals;
}

// 税额变
let history: string[] = [];
function handleTaxInput(e: any, index: number): void {
    history.push(e);
    if (e === "" || e === "-") {
        tableData.value[index].tax = e;
        e === "" && calculateDetailedTotal();
        return;
    }
    if ((isNaN(parseFloat(e)) || !isFinite(e) || !tableData.value[index].amount) && e !== "-") {
        history.pop();
        tableData.value[index].tax = history[history.length - 1];
        return;
    } else if (Math.abs(Number(e)) > Math.abs(Number(tableData.value[index].amount))) {
        history.pop();
        tableData.value[index].tax = history[history.length - 1];
    } else {
        tableData.value[index].tax = String(tableData.value[index].tax).replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
    }
    calculateDetailedTotal();
}
function handleTaxEnter(e: any, index: number) {
    history = [];
    tableData.value[index].tax = toDecimal2(String(tableData.value[index].tax));
    invoiceForm.tax = toDecimal2(invoiceForm.tax);
}
function handleValueFormat(value: string, prop: string) {
    if (prop === "tax") {
        invoiceForm.tax = toDecimal2(invoiceForm.tax);
    } else {
        invoiceForm.amount = toDecimal2(invoiceForm.amount);
    }
}
function handleAdd(sn: number) {
    let newRow = { ...tableRowAddData.value, sn: tableAllData.value.length + 1 };
    let addIndex = tableAllData.value.findIndex((v: IEditOneDataEntries) => v.sn === sn);

    tableAllData.value.splice(addIndex + 1, 0, newRow);
    tableAllData.value.forEach((item: IEditOneDataEntries, index: number) => {
        tableAllData.value[index]["sn"] = index + 1;
    });
    paginationData.total += 1;
    getInvoiceEntriesData();
}
function handleSubtract(index: number, sn: number) {
    if (tableAllData.value.length === 1) {
        tableAllData.value[0] = {
            sn: 1,
            name: "",
            model: "",
            unit: "",
            quality: "",
            amount: "",
            taxRate: taxType === 1 ? 3 : 13,
            tax: "",
            taxProject: 1010,
            taxCountingMethod: 1010,
        };
        tableData.value = tableAllData.value;
        calculateDetailedTotal();
        return;
    }
    let deleteIndex = tableAllData.value.findIndex((v: IEditOneDataEntries) => v.sn === sn);

    tableAllData.value.splice(deleteIndex, 1);
    tableAllData.value.forEach((item: IEditOneDataEntries, index: number) => {
        item.sn = index + 1;
    });
    paginationData.total -= 1;
    calculateDetailedTotal();
    getInvoiceEntriesData();
    if (!tableData.value.length) {
        paginationData.currentPage -= 1;
    }
}
function handleRowClick(row: IEditOneDataEntries) {
    if (["1010", "1040", "1050"].includes(props.invoiceType.id)) {
        return false;
    }
    if (props.formType === "look") {
        return;
    }
    if (row.sn === tableAllData.value.length) {
        tableAllData.value.push({ ...tableRowAddData.value, sn: row.sn + 1 });
        paginationData.total += 1;
        getInvoiceEntriesData();
    }
}
function enterTemplatePage() {
    emit("enterTemplatePage");
}

// 发票附件
const readonly = ref(false);
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
const attachFiles = ref<Array<IFileInfo>>();
const attachParentId = ref(0);

const getAttachFileList = () => {
    readonly.value = invoiceForm.isCheckOut;
    if (attachParentId.value === 0) {
        const params = { invoiceId: invoiceForm.invoiceId };
        request({
            url: "/api/Invoice/GetAttachFileList?" + getUrlSearchParams(params),
            method: "post",
        }).then((res: IResponseModel<IInvoiceAttachFileList>) => {
            if (res.state === 1000 && res.data.result) {
                let list = res.data.data;
                list = list.map((item: any) => {
                    item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                    return item;
                });
                attachFiles.value = list;
                attachParentId.value = res.data.parentId;
                openFileDialog(attachFiles.value ?? [], attachParentId.value);
            } else {
                ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
            }
        });
    } else {
        openFileDialog(attachFiles.value ?? [], attachParentId.value);
    }
};

const openFileDialog = (list: IFileInfo[], fileId: number) => {
    const dateMonth = invoiceForm.entryDate.split("-").slice(0, 2);
    const startDate = dateMonth.join("-") + "-01";
    const day = getDaysInMonth(Number(dateMonth[0]), Number(dateMonth[1]));
    const endDate = dateMonth.join("-") + "-" + day;
    const _params = {
        eRecordSearchDate: { startDate, endDate },
        invoiceId: invoiceForm.invoiceId,
        fileCategory: props.invoiceCategory === "10070" ? 10 : 11,
    };
    uploadFileDialogRef.value?.open(_params, list, fileId);
};

let isNeedSaveToVoucherForDelete = false;
const saveAttachFile = async (_params: any, newFileids: Number[], delFileids: Number[], fileList: any[]) => {
    attachFiles.value = fileList.map((f) => {
        return {
            fileId: f.fileId,
            fileName: f.fileName,
            fileSize: f.fileSize,
            fileType: f.fileType,
            relativePath: f.relativePath,
        };
    });
    invoiceForm.attachFileCount = fileList.length;
    invoiceForm.attachFiles = fileList.map((item: any) => item.fileId).join(",");
    if (props.formType === "look") {
        const { invoiceId } = _params;
        const needToast = await checkNeedToastWithBillAndVoucher(invoiceId, delFileids.join(","));
        if (needToast) {
            showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
                isNeedSaveToVoucherForDelete = batchDelte;
            });
        }
    }
};

async function checkNeedToastWithBillAndVoucher(invId: number, attachFiles: string) {
    return await request({
        url: "/api/Invoice/GetNeedSaveToVoucherForLookUpdate",
        method: "post",
        params: { invId, attachFiles },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000) return false;
        return res.data;
    }).catch(() => {
        return false;
    })
}

function submitCheck() {
    if (!invoiceForm.invoiceDate) {
        ElNotify({
            message: "开票日期不能为空",
            type: "warning",
        });
        return false;
    }
    if (!["1010", "1060", "1070", "1050"].includes(props.invoiceType.id)) {
        if (!invoiceForm.code) {
            ElNotify({
                message: "发票代码不能为空",
                type: "warning",
            });
            return false;
        } else if (invoiceForm.code.trim().length > 12) {
            ElNotify({
                message: "请输入不超过12位的发票代码",
                type: "warning",
            });
            return false;
        } else if (!/^\d+$/.test(invoiceForm.code.trim())) {
            ElNotify({
                message: "发票代码有误，请修改！",
                type: "warning",
            });
            return false;
        }
    }
    if (!["1010", "1050"].includes(props.invoiceType.id)) {
        let length = ["1070", "1060"].includes(props.invoiceType.id) ? 20 : 8;
        if (!invoiceForm.number) {
            ElNotify({
                message: "发票号码不能为空",
                type: "warning",
            });
            return false;
        } else if (!/^\d+$/.test(invoiceForm.number.trim())) {
            ElNotify({
                message: "发票号码有误，请修改！",
                type: "warning",
            });
            return false;
        } else if (invoiceForm.number.trim().length > length) {
            ElNotify({
                message: "请输入不超过" + length + "位的发票号码",
                type: "warning",
            });
            return false;
        }
    }
    if (!["1050", "1010"].includes(props.invoiceType.id) && !invoiceForm.name) {
        ElNotify({
            message: `发票${props.invoiceCategory === "10070" ? "客户" : "供应商"}名称不能为空`,
            type: "warning",
        });
        return false;
    }
    if (!invoiceForm.entryDate) {
        ElNotify({
            message: "录入日期不能为空",
            type: "warning",
        });
        return false;
    }
    // if (containInvalidLabel(invoiceForm.note)) {
    //     ElNotify({
    //         message: `备注不能带有特殊字符`,
    //         type: "warning",
    //     });
    //     return false;
    // }
    if (containInvalidLabel(invoiceForm.taxpayerNumber)) {
        ElNotify({
            message: `统一社会信用代码不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(invoiceForm.address)) {
        ElNotify({
            message: `地址及电话不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(invoiceForm.account)) {
        ElNotify({
            message: `开户行及账号不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(vin.value)) {
        ElNotify({
            message: `车架号不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(machineNumbers.value)) {
        ElNotify({
            message: `机器编号不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(competentTaxAuthoritiesCode.value)) {
        ElNotify({
            message: `主管税务机关代码不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(vehicleType.value)) {
        ElNotify({
            message: `车辆类型不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(brandModel.value)) {
        ElNotify({
            message: `厂牌型号不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(engineNUM.value)) {
        ElNotify({
            message: `发动机号不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    if (containInvalidLabel(vehicleLicence.value)) {
        ElNotify({
            message: `车牌照号不能带有特殊字符`,
            type: "warning",
        });
        return false;
    }
    let flag = true;
    if (isEnterDetail.value) {
        if (["1010", "1050"].includes(props.invoiceType.id)) {
            if (!tableData.value[0].amount || tableData.value[0].amount === "0.00") {
                ElNotify({
                    message: `发票金额数据不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
            if (!tableData.value[0].tax) {
                ElNotify({
                    message: `发票税额数据不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
        }
        if (["1040"].includes(props.invoiceType.id)) {
            if (!tableData.value[0].name) {
                ElNotify({
                    message: `发票商品名称不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
            if (!tableData.value[0].quality) {
                ElNotify({
                    message: `发票数量不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
            if (!tableData.value[0].amount) {
                ElNotify({
                    message: `发票金额不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
            if (!tableData.value[0].tax) {
                ElNotify({
                    message: `发票税额不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
        }

        let inputRow: any | [] = tableAllData.value.filter((v, i) => {
            return v.name || v.amount;
        });

        if (!inputRow.length) {
            ElNotify({
                message: `请录入发票行数据`,
                type: "warning",
            });
            return false;
        }
        const pageSize = paginationData.pageSize;
        inputRow.forEach((v: any,) => {
            if (!v.amount || v.amount === "0.00") {
                ElNotify({
                    message: `第${Math.ceil(v.sn / pageSize)}页第${v.sn % pageSize === 0 ? pageSize : v.sn % pageSize}行发票数据，金额不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
            if (!["1010", "1050"].includes(props.invoiceType.id)) {
                if (!v.name) {
                    ElNotify({
                        message: `第${Math.ceil(v.sn / pageSize)}页第${v.sn % pageSize === 0 ? pageSize : v.sn % pageSize}行发票数据，商品名称不能为空`,
                        type: "warning",
                    });
                    flag = false;
                    return false;
                }
                if (containDangerousLabel(v.name)) {
                    ElNotify({
                        message: `第${Math.ceil(v.sn / pageSize)}页第${v.sn % pageSize === 0 ? pageSize : v.sn % pageSize}行发票数据，商品名称不能包含特殊字符`,
                        type: "warning",
                    });
                    flag = false;
                    return false;
                }
            }

            if (["1040"].includes(props.invoiceType.id) && !v.quality) {
                ElNotify({
                    message: `发票数量不能为空`,
                    type: "warning",
                });

                flag = false;
                return false;
            }
        });
        if (!flag) {
            return false;
        }
    }

    if (!isEnterDetail.value && (!invoiceForm.amount || invoiceForm.amount === "0.00")) {
        ElNotify({
            message: "发票不含税金额不能为空",
            type: "warning",
        });
        return false;
    }
    if (!isEnterDetail.value && !invoiceForm.tax) {
        ElNotify({
            message: "发票税额不能为空",
            type: "warning",
        });
        return false;
    }

    return true;
}
function debounceSaveSubmit(flag?: boolean) {
    if (submitCheck()) {
        let tableEnterArr: Array<IAddTableEnterItem> = isEnterDetail.value
            ? tableAllData.value
                  .filter((v) => v.amount)
                  .map((v) => {
                      return {
                          message: "",
                          name: v.name,
                          model: v.model,
                          unit: v.unit,
                          quality: v.quality,
                          amount: v.amount,
                          taxRate: v.taxRate === "不征税" ? -101 : v.taxRate === "免税" ? -100 : v.taxRate,
                          tax: v.tax,
                          taxProject: v.taxProject,
                          taxCountingMethod: v.taxCountingMethod,
                          code: v.code,
                      };
                  })
            : [];
        invoiceForm.invoiceEntries = tableEnterArr;

        invoiceForm.invoiceType = props.formType === "edit" ? invoiceForm.invoiceType : props.invoiceType.id;
        if (props.invoiceType.id === "1040") {
            invoiceForm.vin = vin.value;
            invoiceForm.machineNumbers = machineNumbers.value;
            invoiceForm.competentTaxAuthoritiesCode = competentTaxAuthoritiesCode.value;
        }
        if (props.invoiceType.id === "1110") {
            invoiceForm.vehicleType = vehicleType.value;
            invoiceForm.brandModel = brandModel.value;
            invoiceForm.vin = vin.value;
            invoiceForm.engineNUM = engineNUM.value;
            invoiceForm.competentTaxAuthoritiesCode = competentTaxAuthoritiesCode.value;
        }
        if (props.invoiceType.id === "1090") {
            invoiceForm.vin = vin.value;
            invoiceForm.machineNumbers = machineNumbers.value;
            invoiceForm.competentTaxAuthoritiesCode = competentTaxAuthoritiesCode.value;
            invoiceForm.vehicleType = vehicleType.value;
            invoiceForm.brandModel = brandModel.value;
            invoiceForm.vehicleLicence = vehicleLicence.value;
        }
        let postBody = { ...invoiceForm };
        delete postBody.adValoremTaxTotals;
        if (props.formType === "edit") {
            delete postBody.invoiceType;
        }

        request({
            url:
                props.formType === "add"
                    ? `/api/Invoice/${interfaceRoute[props.invoiceCategory][props.invoiceType.id]}`
                    : `/api/Invoice/${interfaceRoute[props.invoiceCategory][props.invoiceType.id]}?invoiceId=${invoiceForm.invoiceId}`,
            method: props.formType === "add" ? "post" : "put",
            data: JSON.stringify(postBody),
            headers: {
                "Content-Type": "application/json",
            },
        })
            .then((res: IResponseModel<boolean>) => {
                if (res.state === 1000 && res.data) {
                    emit("addInvoiceSuccess", invoiceForm.invoiceDate, props.formType, erpAssistingProjectList.value, flag);
                    ElNotify({
                        message: `保存成功`,
                        type: "success",
                    });
                    if(props.formType === "add") {
                        handleExpiredCheckData(ExpiredCheckModuleEnum.Invoice);
                    }
                    window.dispatchEvent(new CustomEvent("reloadOppositeParty"));
                } else {
                    ElNotify({
                        message: res.msg || "保存失败了，请稍候重试或联系系统管理员",
                        type: "warning",
                    });
                }
            })
            .catch((e) => {
                ElNotify({
                    message: "保存失败了，请稍候重试或联系系统管理员",
                    type: "warning",
                });
            });
    }
}
const saveSubmit = () => {
    if (props.formType === "look") {
        if (getCookie("EditHiddenInvoiceDialog-" + getGlobalToken()) == "true" || beforeProjectId.value === invoiceForm.projectId) {
            lookUpdate();
        } else {
            voucherTip.value = true;
        }
    } else {
        debounce(debounceSaveSubmit, 500)();
    }
};
const voucherTip = ref(false);
// 凭证提示
const handleTipConfirm = () => {
    if (getCookie("EditHiddenInvoice-" + getGlobalToken()) == "true") {
        setCookie("EditHiddenInvoiceDialog-" + getGlobalToken(), "true", "d1");
    } else {
        setCookie("EditHiddenInvoiceDialog-" + getGlobalToken(), "false", "d1");
    }
    voucherTip.value = false;
    lookUpdate();
};
const notTip = ref(false);
const handleTipCancel = () => {
    setCookie("EditHiddenInvoice-" + getGlobalToken(), "false", "s1");
    voucherTip.value = false;
    notTip.value = false;
};
// 不再提示
const handleNoTipsChange = (check: boolean) => {
    check
        ? setCookie("EditHiddenInvoice-" + getGlobalToken(), "true", "d1")
        : setCookie("EditHiddenInvoice-" + getGlobalToken(), "false", "s1");
};
const lookUpdate = () => {
    let params = {
        invoiceId: 0,
        projectId: 0,
        issueDate: "",
        attachFiles: "",
        isNeedSaveToVoucherForAdd: true,
        isNeedSaveToVoucherForDelete,
    };
    params.invoiceId = Number(invoiceForm.invoiceId);
    params.projectId = Number(invoiceForm.projectId);
    params.issueDate = invoiceForm.issueDate ? invoiceForm.issueDate : "";
    params.attachFiles = invoiceForm.attachFiles ? invoiceForm.attachFiles : "";
    request({
        url: `/api/Invoice/LookUpdate?` + getUrlSearchParams(params),
        method: "put",
    })
        .then((res) => {
            if (res.state === 1000) {
                let ProjectIdChange = true;
                if (beforeProjectId.value === invoiceForm.projectId) {
                    //已凭证查看，项目值未修改，保存后不用提示修改凭证
                    ProjectIdChange = false;
                }
                emit("addInvoiceSuccess", invoiceForm.invoiceDate, props.formType, erpAssistingProjectList.value, false, ProjectIdChange);
                ElNotify({
                    message: `保存成功`,
                    type: "success",
                });
            } else {
                ElNotify({
                    message: res.msg || "保存失败了，请稍候重试或联系系统管理员",
                    type: "warning",
                });
            }
        })
        .catch((e) => {
            ElNotify({
                message: "保存失败了，请稍候重试或联系系统管理员",
                type: "warning",
            });
        });
};

function goToMasterPlate() {
    currentSlot.value = "masterPlate";
    masterPlateView.value?.initCategory(invoiceCate.value);
}
function EditCancel() {
    init = false;
    addSlotIsEditting.value = false;
    emit("goBackMain", erpAssistingProjectList.value);
}

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//模糊搜索
const showErpAssistingCustomListAll = ref<any[]>([]);
const showErpAssistingCustomList = ref<any[]>([]);
const showErpAssistingProjectList = ref<IResoneProjectItem[]>([]);
const showBusinessTypeOptions = ref<IBusinessTypeItem[]>([]);
const showTaxProjectOptions = ref<IEntriesSelectItem[]>([]);
const showTaxMethodOptions = ref<IEntriesSelectItem[]>([]);
const showGoodsTypeOptions = ref<Array<IInvoiceStockItem>>([]);
watchEffect(() => { 
    showErpAssistingCustomListAll.value = erpAssistingCustomList.value?.map((item) => {
        return {
            ...item,
            label: item.aanum + ' ' + item.aaname,
        }
    });
    showErpAssistingCustomList.value = JSON.parse(JSON.stringify(showErpAssistingCustomListAll.value));
});
watchEffect(() => {
    showErpAssistingProjectList.value = JSON.parse(JSON.stringify(erpAssistingProjectList.value));
    showBusinessTypeOptions.value = JSON.parse(JSON.stringify(businessTypeOptions.value));
    showTaxProjectOptions.value = JSON.parse(JSON.stringify(taxProjectOptions.value));
    showTaxMethodOptions.value = JSON.parse(JSON.stringify(taxMethodOptions.value));
    showGoodsTypeOptions.value = JSON.parse(JSON.stringify(GoodsTypeOptions.value));
});
function erpAssistingCustomFilterMethod(value: string) {
    showErpAssistingCustomList.value = commonFilterMethod(value, showErpAssistingCustomListAll.value, 'label');
    autoAddName.value = showErpAssistingCustomList.value.length === 0 ? value.trim() : ""; 
}
function erpAssistingProjectFilterMethod(value: string) {
    showErpAssistingProjectList.value = commonFilterMethod(value, erpAssistingProjectList.value, 'aaname');
    autoAddName.value = showErpAssistingProjectList.value.length === 0 ? value.trim() : ""; 
}
function businessTypeFilterMethod(value: string) {
    showBusinessTypeOptions.value = commonFilterMethod(value, businessTypeOptions.value, 'label');
}
function taxProjectFilterMethod(value: string) {
    showTaxProjectOptions.value = commonFilterMethod(value, taxProjectOptions.value, 'label');
}
function taxMethodFilterMethod(value: string) {
    showTaxMethodOptions.value = commonFilterMethod(value, taxMethodOptions.value, 'label');
}
function GoodsTypeFilterMethod(value: string) {
    showGoodsTypeOptions.value = commonFilterMethod(value, GoodsTypeOptions.value, 'name');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

:deep(.el-date-editor) {
    .el-input__prefix {
        position: absolute;
        right: 0;
    }
    .el-input__suffix-inner {
        position: absolute;
        right: 30px;
        top: 9px;
    }
}
.invoice-move-top{
    :deep(.el-textarea__inner){
        position: absolute;
        top: -32px;
        z-index: 1000;
    }
}
.note-tooltip-width{
    :deep(.popover_content){
        max-width: 700px !important;
        width: 700px !important;
   }
}
.main-block {
    padding: 20px;

    .main-title {
        color: var(--font-color);
        font-size: var(--h3);
        line-height: 22px;
        text-align: left;
    }

    .main-block-item {
        display: flex;
        margin: 10px 10px;
        text-align: left;

        .upload-title {
            height: 100%;
            width: 50px;
            font-weight: bold;
            font-size: var(--font-size);
        }

        .upload-item {
            height: 70px;
            width: 240px;
            border: 1px solid var(--border-color);
            cursor: pointer;

            .upload-item-title {
                margin: 10px 0 0 70px;
                img {
                    height: 14px;
                    width: 14px;
                    margin-right: 8px;
                }
            }
            .upload-item-tip {
                margin: 10px 0 0 60px;
                font-size: 12px;
                color: var(--menu-font-color);
            }
        }
    }

    .main-block-center {
        margin: 10px 0px 0px 0px;
        font-size: 0;
        padding: 0;
        .invoice-text-right {
            :deep(.el-input__inner) {
                text-align: right;
            }
        }
        .set-template-button {
            display: inline-block;
            line-height: 32px;
            color: var(--link-color);
            margin-left: 50px;
            width: 30%;
            text-align: left;
            margin: 10px 0 0 5%;
            width: auto;
        }

        .line-item {
            height: 32px;
            width: 29%;
            margin: 10px 0 0;
            display: flex;
            padding: 0 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &.large {
                width: 35%;
            }

            :deep(.el-form-item__label) {
                text-align: right;
                float: left;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                line-height: 32px;
                font-family: 微软雅黑 !important;
                font-size: 14px;
                width: 130px;
            }

            &.required {
                :deep(.el-form-item__label::before) {
                    content: "*";
                    color: var(--el-color-danger);
                    margin-right: 4px;
                }
            }
            :deep(.el-form-item__content) {
                display: inline-block;
                flex: 1;
                .line-item-field {
                    width: 160px;
                    &.line-item-select {
                        .el-input {
                            width: 100%;
                        }
                    }
                    &.el-autocomplete .el-input {
                        width: 100%;
                    }
                }

                .line-item-field.large {
                    width: 210px;
                }

                // 取消input的上下箭头
                input::-webkit-inner-spin-button {
                    -webkit-appearance: none !important;
                }

                input::-webkit-outer-spin-button {
                    -webkit-appearance: none !important;
                }
            }
        }

        .form-item-list {
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            position: relative;
            left: -30px;
        }
        .form-item-list.list-large {
            .line-item:nth-child(3n + 2) {
                width: 35%;
                :deep(.el-form-item__content) {
                    .line-item-field {
                        width: 210px;
                    }
                }
            }
        }
        .form-item-list.list-large.listElectric {
            .line-item:nth-child(3n + 1) {
                width: 35%;
                :deep(.el-form-item__content) {
                    .line-item-field {
                        width: 210px;
                    }
                }
            }
            .line-item:nth-child(3n + 2) {
                width: 29%;
                :deep(.el-form-item__content) {
                    .line-item-field {
                        width: 100%;
                    }
                }
            }
        }

        .form-item-list.list-large {
            .line-item:nth-child(3n + 2) {
                width: 35%;
                :deep(.el-form-item__content) {
                    .line-item-field {
                        width: 210px;
                    }
                }
            }
        }
        .form-item-list.list-large.listElectric {
            .line-item:nth-child(3n + 1) {
                width: 35%;
                :deep(.el-form-item__content) {
                    .line-item-field {
                        width: 210px;
                    }
                }
            }
            .line-item:nth-child(3n + 2) {
                width: 29%;
                :deep(.el-form-item__content) {
                    .line-item-field {
                        width: 100%;
                    }
                }
            }
        }

        .second-form {
            display: flex;
            justify-content: flex-start;
            position: relative;
            left: -30px;
        }

        .third-form {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .line-item {
                padding: 0;
                :deep(.el-form-item__label) {
                    width: 100px;
                    width: 100px;
                }
                .el-form-item__content {
                    width: auto;

                    .line-item-field {
                        width: 114px;
                        :deep(.el-input__wrapper) {
                            padding: 1px 4px;
                        }
                    }
                }
            }

            .line-item.large {
                :deep(.el-form-item__label) {
                    width: auto;
                }

                .line-item-field {
                    width: 200px;

                    :deep(.el-radio) {
                        margin-right: 10px;
                        display: inline-block;
                    }
                }
            }

            // 取消input的上下箭头
            input::-webkit-inner-spin-button {
                -webkit-appearance: none !important;
            }

            input::-webkit-outer-spin-button {
                -webkit-appearance: none !important;
            }
        }

        .main-table-block {
            margin-top: 20px;
            padding: 0 10px;
            position: relative;
            :deep(.el-table) {
                .el-table__inner-wrapper {
                    border-bottom: 1px solid var(--el-table-border-color);
                }

                .el-table__body-wrapper {
                    padding-bottom: 0px;
                }
            }
            :deep(.el-table--default .cell) {
                padding: 0 4px;

                .el-select {
                    width: 100%;
                }

                .el-input__inner {
                    border: none;
                    padding: 1px 3px 1px 5px;
                }

                .el-input__suffix-inner > :first-child {
                    margin: 0;
                }

                // 取消input的上下箭头
                input::-webkit-inner-spin-button {
                    -webkit-appearance: none !important;
                }

                input::-webkit-outer-spin-button {
                    -webkit-appearance: none !important;
                }
            }

            .table-required-label::before {
                content: "*";
                color: var(--el-color-danger);
                margin-right: 4px;
            }
            .invoice-table-cell-input {
                .el-input {
                    width: 100%;
                }
                :deep(.el-input__wrapper) {
                    padding: 0 2px;
                }
                :deep(.el-popper) {
                    display: none;
                }
            }
            .invoice-add-taxRate-input {
                :deep(.el-input__wrapper) {
                    padding: 0 4px;
                    .el-input__inner {
                        padding: 0;
                    }
                }
            }
            .tax-rate-select {
                display: inline-block;
                // .detail-el-select(30px);
            }
            .invoice-add-taxRate-select {
                :deep(.select-trigger) {
                    .el-input {
                        &.is-focus {
                            .el-input__wrapper {
                                box-shadow: none;
                            }
                        }
                        .el-input__wrapper {
                            padding: 0;
                            &.is-focus {
                                box-shadow: none;
                            }
                            .el-input__inner {
                                padding: 0;
                            }
                            .el-input__suffix {
                                padding: 0 10px;
                            }
                        }
                    }
                }
            }
            :deep(.el-select) {
                .el-input {
                    width: 100% !important;
                    .el-input__wrapper {
                        width: 100% !important;
                    }
                }
            }
            :deep(.datagrid-pager) {
                margin-top: 10px;
                border: none;
            }
        }
    }
}
.line-item:nth-child(3n + 1) .project-select {
    :deep(.el-select__popper .el-select-dropdown .el-select-dropdown__list) {
        width: 180px;
    }
}
.line-item:nth-child(3n + 3) .project-select {
    :deep(.el-select__popper .el-select-dropdown .el-select-dropdown__list) {
        width: 180px;
    }
}
.listElectric {
    .line-item:nth-child(3n + 1) .project-select {
        :deep(.el-select__popper .el-select-dropdown .el-select-dropdown__list) {
            width: 226px;
        }
    }
}
.sn-no {
    line-height: 30px;
    padding-left: 5px;
}
.sn-disabled {
    background-color: var(--el-disabled-bg-color);
    color: var(--el-disabled-text-color);
    border: 1px solid var(--border-color);
    border-radius: 2px;
}
.autocomplete-input,
.project-select {
    display: inline-block;
    width: 100%;
    .detail-el-autocomplete(200px);
    .detail-el-select(226px, 28px);
    :deep(.el-select-dropdown__item) {
        text-align: left;
        width: calc(100% - 16px) !important;
    }

    :deep(.new-cd-account) {
        line-height: 34px;
        height: 34px;
        display: flex;
        justify-content: center;
        cursor: pointer;
        .add-name-btn {
            font-size: 12px;
            color: var(--main-color);
            display: flex;
            align-items: center;
            // &::before {
            //     width: 14px;
            //     height: 15px;
            //     content: "";
            //     margin-right: 3px;
            //     background-image: url("@/assets/Icons/add.png");
            //     background-size: 14px 15px;
            // }
        }
        .new-cd-project {
            font-size: 12px;
            color: var(--main-color);
            display: flex;
            align-items: center;
        }
    }
}
.main-button {
    padding: 10px;
    text-align: center;

    .save-button {
        color: var(--white);
        border-color: var(--main-color);
        background-color: var(--main-color);
    }
}
:deep(.el-input .el-input__inner) {
    font-size: var(--el-form-label-font-size);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.main-block + .main-block {
    border-top: 1px solid var(--border-color);
}
.main-block {
    .main-block-center {
        .invoice-text-right {
            :deep(.el-input__inner) {
                text-align: right;
            }
        }
    }
}
:deep(.el-select) {
    &.addBusinessTypeRef {
        .el-select-dropdown {
            max-height: 200px;
            overflow: hidden;
        }
    }
}
.voucher-tip-box {
    .box-main {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        .tip {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &.erp {
        .box-main {
            border: none;
            text-align: left;
            padding-bottom: 0;
            .tip {
                justify-content: flex-start;
            }
        }
        .buttons {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            padding: 20px 10px;
            border-top: 1px solid var(--border-color);
            margin-top: 20px;
            .button {
                margin: 0 10px;
            }
        }
    }
}
.invoice-add-view {
    #masterPlateContentSlider {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
    }
}
body[erp] {
    .content {
        .slot-content.invoice-edit-content {
            overflow: visible;
            .edit-content {
                height: auto;
                overflow: visible;
            }
        }
    }
    .main-block {
        .main-block-center {
            :deep(.el-table) {
                .el-table__inner-wrapper {
                    border-bottom: none;
                }
            }
        }
    }
}
</style>
