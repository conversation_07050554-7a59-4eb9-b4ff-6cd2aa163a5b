<template>
    <searchView
        @handle-new="handleNew"
        @handle-search="btnSearch"
        @handle-clear="handleClear"
        @handle-export="handleExport"
        @handle-import="handleImport"
    />
    <div class="main-center">
        <Table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :page-is-show="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :scrollbar-show="true"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="90px" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccount-canedit']" class="link" @click="handleEdit(scope.row.aaeId)"> 编辑 </a>
                            <a v-permission="['assistingaccount-candelete']" class="link" @click="handleDelete(scope.row.aaeId)"> 删除 </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { deleteHandle, clearHanele, exportHandle, getModelApi, formatDate } from "../utils";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import searchView from "./SearchBox.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AssitStock";
interface ITableItem {
    stockModel: string;
    stockType: string;
    unit: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
interface IEditModelItem {
    asId: number;
    aaeId: number;
    stockModel: string;
    stockType: string;
    unit: string;
    startDate: string;
    endDate: string;
    note: string;
    aaNum: string;
    aaName: string;
    status: number;
    uscc: string;
}

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const columns: IColumnProps[] = [
    { label: "存货编码", prop: "aaNum", minWidth: 120, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaNum") },
    { label: "存货名称", prop: "aaName", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaName") },
    { label: "助记码", prop: "aaAcronym", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaAcronym") },
    { label: "规格型号", prop: "stockModel", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "stockModel") },
    { label: "存货类别", prop: "stockType", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "stockType") },
    { label: "计量单位", prop: "unit", minWidth: 70, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "unit") },
    { label: "启用日期", prop: "startDate", minWidth: 100, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "startDate") },
    { label: "停用日期", prop: "endDate", minWidth: 100, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "endDate") },
    { label: "备注", prop: "note", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "note") },
    { slot: "operator" },
];

const loading = ref(false);
const tableData = ref<ITableItem[]>([]);
const emit = defineEmits(["handleNew", "handleEdit", "handleImport", "handleCancel"]);

const searchStr = ref("");
const btnSearch = (searchVal: string) => {
    searchStr.value = searchVal;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleSearch = (successBack?: Function) => {
    loading.value = true;
    const params = {
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        searchStr: searchStr.value,
    };
    request({ url: "/api/AssistingAccounting/PagingStockList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                successBack && successBack();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleNew = () => {
    emit("handleNew", 10006);
};
const handleEdit = (aaeID: string | number) => {
    getModelApi(aaeID, 10006).then((res: IResponseModel<IEditModelItem>) => {
        if (res.state == 1000) {
            const data = res.data;
            const params = {
                aaNum: data.aaNum,
                aaName: data.aaName,
                aaeID: data.aaeId,
                stockModel: data.stockModel,
                stockType: data.stockType,
                unit: data.unit,
                startDate: data.startDate,
                endDate: data.endDate,
                note: data.note,
                status: data.status == 0,
            };
            emit("handleEdit", 10006, params);
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const isThisPageHasData = (type: "delete" | "clear") => {
    return function () {
        if (type === "delete") {
            if (paginationData.currentPage !== 1 && tableData.value.length === 1) {
                paginationData.currentPage--;
            } else {
                handleCancel();
            }
        } else {
            if (paginationData.currentPage !== 1) {
                paginationData.currentPage = 1;
            } else {
                paginationData.currentPage = 1;
                handleCancel();
            }
        }
    };
};
const handleCancel = () => emit("handleCancel");
const handleDelete = (aaeID: number) => deleteHandle(10006, aaeID, isThisPageHasData("delete"));
const handleClear = () => clearHanele(tableData.value.length, 10006, isThisPageHasData("clear"));
const handleExport = () => exportHandle(10006, paginationData.total);
const handleImport = () => emit("handleImport", 10006);

defineExpose({ handleSearch });

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});
</script>

<style lang="less" scoped></style>
