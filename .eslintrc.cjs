// require("@rushstack/eslint-patch/modern-module-resolution");

module.exports = {
  env: {
    node: true, // 添加 node 环境
    browser: true, // 如果需要浏览器环境
    es2021: true, // 支持现代 ES 特性
  },
  extends: ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/eslint-config-typescript", "plugin:prettier/recommended"],
  parser: "vue-eslint-parser", //解析.vue文件<template>中的内容
  parserOptions: {
    ecmaVersion: "latest",
    parser: "@typescript-eslint/parser", //解析.vue文件中<script>标签中的代码。
  },
  rules: {
    "vue/multi-word-component-names": "off",
    "no-debugger": "off",
    "no-const-assign": "error", //禁止修改const声明的变量
    "no-eq-null": "warn", //警告对null使用==或!=运算符
    "no-redeclare": "error", //禁止重复声明变量
    "no-unneeded-ternary": "error", //禁止不必要的嵌套例如 const isYes = answer === 1 ? true : false;
    "prettier/prettier": ["error"], // 添加这行来解决换行符问题
  },
}
