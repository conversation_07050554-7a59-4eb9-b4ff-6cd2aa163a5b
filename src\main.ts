import { createApp } from "vue";
import App from "./App.vue";
import store from "./store";
import router from "./router";
import "@/router/permission";
import { debounce, cloneDeep, round, isEqual, cloneDeepWith, isNumber, throttle, isString, intersection } from "lodash";
import { loadProvide } from "./provide";
import { loadDirectives } from "./direcitves";
import "@/style/Common.less";
import { ElDialog } from "element-plus";
ElDialog.props.closeOnClickModal = false;
ElDialog.props.closeOnPressEscape = false;
ElDialog.props. destroyOnClose = {
    type: Boolean,
    default: true,
};
import { ElSelect } from "element-plus";
ElSelect.props.fitInputWidth = true;

import { ElScrollbar } from "element-plus";
ElScrollbar.props.minSize = {
    type: Number,
    default: 100,
};

import ElementPlus from "element-plus";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import "element-plus/dist/index.css";
import "@/style/ScrollBarThumb.less";

import zhCn from "element-plus/dist/locale/zh-cn.mjs";

// 图片预览工具
import "viewerjs/dist/viewer.css";

// 虚拟列表
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import { RecycleScroller } from "vue-virtual-scroller";
import Vue3DraggableResizable from "vue3-draggable-resizable";
import { DraggableContainer } from "vue3-draggable-resizable";
import "vue3-draggable-resizable/dist/Vue3DraggableResizable.css";
import { initAsubTreeKey, popoverHandleCloseKey, stopPopoverCloseKey, updateAsubCodeKey } from "./components/Picker/SubjectPicker/symbols";
import { customFilterOptionKey, customFilterPropsKey, customFilterLimitKey } from "./components/TableHeaderFilter/utils";
import { componentFinishKey } from "@/symbols";

const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}
app.config.errorHandler = (err) => {
    console.log(err);
};

app.provide("global", {});
app.provide(initAsubTreeKey, undefined);
app.provide(updateAsubCodeKey, undefined);
app.provide(popoverHandleCloseKey, undefined);
app.provide(stopPopoverCloseKey, undefined);
app.provide(customFilterOptionKey, undefined);
app.provide(customFilterPropsKey, undefined);
app.provide(customFilterLimitKey, undefined);
app.provide("carryOverVoucherTime", undefined);
app.provide(componentFinishKey, undefined);

loadProvide(app);
loadDirectives(app);

app.component("RecycleScroller", RecycleScroller);
app.component("Vue3DraggableResizable", Vue3DraggableResizable);
app.component("DraggableContainer", DraggableContainer);
app.config.globalProperties.$lodash = {
    debounce,
    cloneDeep,
    round,
    isEqual,
    cloneDeepWith,
    isNumber,
    throttle,
    isString,
    intersection,
};
app.use(store)
    .use(ElementPlus, {
        locale: zhCn,
    })
    .use(router)
    .mount("#app");
