import App from "./App.vue"
import router from "./router"
import ElementPlus from "element-plus"
import "element-plus/dist/index.css"
import * as ElementPlusIconsVue from "@element-plus/icons-vue"
import zhCn from "element-plus/dist/locale/zh-cn.mjs" //国际化
import { createPinia } from "pinia"
import LemonUI from "@/components/common.ts"
import directives from "./directives/index"
import "@/style/common.scss"

const app = createApp(App)
const pinia = createPinia() //实例化pinia

//全局注册图标组件
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(LemonUI).use(directives).use(ElementPlus, { locale: zhCn }).use(router).use(pinia).mount("#app")
