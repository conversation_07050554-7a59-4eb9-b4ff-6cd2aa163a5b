<template>
    <div class="checkout-finish-boxs">
        <div class="stepTitle">
            <span>{{ normalType ? "第 3 步 结账" : "第 1 步 结账" }}</span>
        </div>
        <div class="context" ref="contextRef">
            <div class="left">
                <div class="context-title">影响结账检查</div>
                <div class="context-box">
                    <div class="context-item" v-show="normalType">
                        <span>期末检查：</span>
                        <img src="@/assets/Checkout/pass.png" alt="" />
                        <span class="info">已完成</span>
                    </div>
                    <div class="context-item" v-show="normalType">
                        <span>{{ "结转" + props.asTheme + "：" }}</span>
                        <img src="@/assets/Checkout/pass.png" alt="" />
                        <span class="info">已完成</span>
                    </div>
                    <div class="context-item" v-for="(tipItem, index) in pageData.filter((item) => !item.onlyMessage)" :key="index">
                        <span>{{ tipItem.project + "：" }} </span>
                        <img v-if="tipItem.isPaased" src="@/assets/Checkout/pass.png" alt="" />
                        <img v-else src="@/assets/Checkout/warn.png" alt="" />
                        <span class="info" v-html="formatMsg(tipItem.msg)"></span>
                    </div>
                </div>
            </div>
            <div class="right" style="margin-left: 150px" v-show="rightTipShow">
                <div class="context-title">仅提示类检查</div>
                <div class="context-box">
                    <div class="context-item" v-for="(tipItem, index) in pageData.filter((item) => item.onlyMessage)" :key="index">
                        <span>{{ tipItem.project + "：" }} </span>
                        <span class="info" v-html="formatMsg(tipItem.msg)"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="check-box-tool-bar">
            <a class="button mr-10" @click="checkoutLast">上一步</a>
            <a class="button mr-10" @click="handleReCheck">重新检查</a>
            <a :class="judgeCanCheckout() ? 'button solid-button' : 'button solid-button disabled'" @click="goCheckout">结账</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onUpdated, onUnmounted, computed, toRef } from "vue";
import { request } from "@/util/service";
import { checkPermission } from "@/util/permission";
import type { ICheckoutPageItem } from "../tpyes";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { globalWindowOpenPage } from "@/util/url";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";

const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const accountSet = useAccountSetStore().accountSet;

const rightTipShow = ref(false);

const handleInit = () => {
    const cashJournal = accountSet?.cashJournal;
    const fixedasset = accountSet?.fixedasset;
    if (cashJournal !== 1 && fixedasset !== 1) {
        rightTipShow.value = false;
    } else {
        rightTipShow.value = true;
    }
};
handleInit();

const props = defineProps<{
    pid: number;
    asTheme: string;
}>();
const pid = computed(() => props.pid);

const contextRef = ref<HTMLDivElement>();
const pageData = ref<ICheckoutPageItem[]>([]);

const normalType = ref(true);
let backPage = "";

const emit = defineEmits(["goCheckout", "checkoutLast", "reCheck", "backToMain"]);

const goCheckout = () => {
    if (!judgeCanCheckout()) return;
    emit("goCheckout");
};

const checkoutLast = () => {
    normalType.value ? emit("checkoutLast", backPage) : emit("backToMain");
    backPage = "";
    normalType.value = true;
};

const setData = (loadingData: any[], type = true, backPath?: string) => {
    pageData.value = loadingData;
    normalType.value = type;
    if (backPath !== undefined) backPage = backPath;
};
defineExpose({ setData });

const judgeCanCheckout = () => {
    return pageData.value.every((item) => item.isPaased);
};

function splitString(input: string): string[] {
    const regex = /(<a[^>]+>[^<]+<\/a>)/g;
    const matches = input.match(regex);
    const result: string[] = [];

    if (matches) {
        const startIndex = input.indexOf(matches[0]);
        if (startIndex > 0) {
            result.push(input.substring(0, startIndex));
        }

        for (const match of matches) {
            result.push(match);
        }
    }

    return result;
}

const isErp = ref(window.isErp);
function replaceEventName(input: string): string {
    const regex = /onclick='Notify\("提示", "您没有此权限"\)'/g;
    const updatedString = input.replace(regex, "onclick='notHavePermission()'");
    return updatedString;
}

function extractAnchorInfo(input: string): [string, string] {
    const regex = /<a[^>]*>(.*?)<\/a>/;
    const match = input.match(regex);

    if (match) {
        const anchorTag = match[0];
        const textRegex = />(.*?)<\/a>/;
        const hrefRegex = /href=['"](.*?)['"]/;
        const textMatch = anchorTag.match(textRegex);
        const hrefMatch = anchorTag.match(hrefRegex);

        if (textMatch && hrefMatch) {
            const text = textMatch[1];
            const href = hrefMatch[1];
            return [text, href];
        }
    }

    return ["", ""];
}

const formatMsg = (msg: string) => {
    let backupMsg = msg.replace(/.aspx/g, "");
    if (backupMsg.startsWith("存在未审核凭证") || backupMsg.startsWith("存在断号凭证")) {
        const msgArr = splitString(backupMsg);
        if (msgArr[1].includes("您没有此权限")) {
            if (backupMsg.startsWith("存在未审核凭证")) {
                msgArr[1] = "<a class='link' href='javascript:void(0)' id='CheckVoucherNotHasPermission'>查看</a>";
            } else {
                msgArr[1] = "<a class='link' href='javascript:void(0)' id='SortVoucherNotHasPermission'>查看</a>";
            }
        } else {
            msgArr[1] = msgArr[1].replace("'>", "&from=checkout&r=" + Math.random() + "'>");
            if (isErp.value) {
                const index = msgArr[1].indexOf("<a class='link'");
                const tip = msgArr[1].substring(0, index);
                const [title, href] = splitString4Erp(msgArr[1]);
                msgArr[1] = `${tip}<a class="link" href="javascript:void(0)" onclick="createTab4Erp('${href}', '${title}')">查看</a>`;
            } else {
                const [text, href] = extractAnchorInfo(msgArr[1]);
                msgArr[1] = `<a class="link" href="javascript:void(0)" onclick="createTab4Erp('${href}', '')">${text}</a>`;
            }
        }
        backupMsg = msgArr.join("");
        return backupMsg;
    } else {
        if (backupMsg.includes("您没有此权限")) {
            backupMsg = replaceEventName(backupMsg);
            return backupMsg;
        } else {
            backupMsg = backupMsg.replace("'>", "&from=checkout&r=" + Math.random() + "'>");
        }
        let replaceMsg = backupMsg.replace(/.aspx/g, "");
        if (replaceMsg.startsWith("已完成") || replaceMsg.startsWith("未启用")) {
            return replaceMsg;
        } else {
            const index = replaceMsg.indexOf("<a class='link'");
            const tip = replaceMsg.substring(0, index);
            const [, href] = isErp.value ? splitString4Erp(replaceMsg) : extractAnchorInfo(replaceMsg);
            const showTip = tip === "报表不平，检查报表" ? "点击处理" : "点击查看";
            return `${tip}<a class="link" href="javascript:void(0)" onclick="createTab4Erp('${href}', '')">${showTip}</a>`;
        }
    }
};
function splitString4Erp(input: string): string[] {
    const regex = /<a class='link' href='([^']+)'>.*?<\/a>/;
    const matches = input.match(regex);

    if (matches && matches.length === 2) {
        const [, href] = matches;
        const hashIndex = href.indexOf("#");
        const atSignIndex = href.indexOf("@");

        if (hashIndex !== -1 && atSignIndex !== -1 && atSignIndex > hashIndex) {
            const reportName = href.substring(hashIndex + 1, atSignIndex);
            const reportUrl = href.substring(atSignIndex + 1);
            return [reportName, reportUrl];
        }
    }

    return [];
}
const createTab4Erp = (href: string, title: string) => {
    globalWindowOpenPage(href, title);
    if (!isErp.value) {
        if (!href.startsWith("/Voucher/VoucherList") && !href.startsWith("/FixedAssets/FixedAssets")) {
            setTimeout(() => {
                const currentRouterModel = routerArray.value.find((item) => item.alive);
                if (currentRouterModel) {
                    routerArrayStore.refreshRouter(currentRouterModel!.path);
                }
            });
        }
    }
};

const handleReCheck = () => {
    emit("reCheck", backPage !== "carryOverIncomeChange", normalType.value);
};

const handleCheckVoucherNotHasPermission = () => {
    ElNotify({ type: "warning", message: "您没有此权限" });
};
const handleSortVoucherNotHasPermission = () => {
    ElNotify({ type: "warning", message: "您没有此权限" });
};
const handleApproveAllVoucher = () => {
    if (!checkPermission(["voucher-cancheck"])) {
        ElNotify({ type: "warning", message: "您没有此权限" });
        return;
    }
    request({ url: "/api/Voucher/ApproveAllVouchers?pId=" + pid.value, method: "post" }).then((res: any) => {
        if (res.state === 1000 && res.data === true) {
            ElNotify({ type: "success", message: "亲，审核成功啦!" });
            handleReCheck();
        } else {
            ElNotify({ type: "warning", message: "亲，审核失败了，请联系侧边栏客服！" });
        }
    });
};
const handleSortVoucherNum = () => {
    if (!checkPermission(["voucher-canclean"])) {
        ElNotify({ type: "warning", message: "您没有此权限" });
        return;
    }
    request({ url: "/api/Voucher/SortVoucher?pId=" + pid.value + "&vgId=0&sortType=0", method: "post" }).then((res: any) => {
        if (res.state === 1000 && res.data === true) {
            ElNotify({ type: "success", message: "亲，整理成功啦!" });
            handleReCheck();
        } else {
            ElNotify({ type: "warning", message: "亲，整理失败了，请联系侧边栏客服！" });
        }
    });
};
onUpdated(() => {
    document.getElementById("ApproveAllVoucher")?.addEventListener("click", handleApproveAllVoucher);
    document.getElementById("SortVoucherNum")?.addEventListener("click", handleSortVoucherNum);
    document.getElementById("CheckVoucherNotHasPermission")?.addEventListener("click", handleCheckVoucherNotHasPermission);
    document.getElementById("SortVoucherNotHasPermission")?.addEventListener("click", handleSortVoucherNotHasPermission);
    (window as any).notHavePermission = handleCheckVoucherNotHasPermission;
    (window as any).createTab4Erp = createTab4Erp;
});
onUnmounted(() => {
    document.getElementById("ApproveAllVoucher")?.removeEventListener("click", handleApproveAllVoucher);
    document.getElementById("SortVoucherNum")?.removeEventListener("click", handleSortVoucherNum);
    document.getElementById("CheckVoucherNotHasPermission")?.removeEventListener("click", handleCheckVoucherNotHasPermission);
    document.getElementById("SortVoucherNotHasPermission")?.removeEventListener("click", handleSortVoucherNotHasPermission);
    (window as any).notHavePermission = null;
    (window as any).createTab4Erp = null;
});
</script>

<style lang="less" scoped>
.checkout-finish-boxs {
    width: 1000px;
    box-sizing: border-box;
    text-align: left;
    color: #404040;
    font-size: 12px;
    margin: 0 auto;
    .stepTitle {
        padding-top: 60px;
        padding-left: 30px;
        color: var(--font-color);
        font-size: 24px;
        line-height: 30px;
    }
    .check-box-tool-bar {
        margin-top: 20px;
        text-align: center;
        margin-bottom: 40px;
    }
    .context {
        margin-top: 40px;
        padding-left: 30px;
        height: 286px;
        display: flex;
        font-weight: 500;
        .context-title {
            color: var(--font-color);
            font-size: var(--h2);
            line-height: 22px;
        }
        .context-box {
            .context-item {
                margin-top: 16px;
                color: var(--font-color);
                font-size: var(--h3);
                line-height: 22px;
                & > img {
                    width: 14px;
                    vertical-align: -3px;
                    margin-right: 4px;
                }
                & > span.info {
                    font-weight: 400;
                    color: var(--font-color);
                    font-size: 14px;
                    line-height: 22px;
                    :deep(a) {
                        margin-left: 20px;
                    }
                }
            }
        }
    }
}
</style>
