<template>
    <div id="divImport" class="edit-content upload-content" style="width: 1000px">
        <div class="title">批量导入员工信息</div>
        <div class="txt first-item">
            <div>1.请选择下面任意一种方式导入员工信息</div>
            <div class="mt-10 ml-10">(1)在员工信息列表导出所需数据，确认后直接导入</div>
            <div class="mt-10 ml-10">(2)点击下载模板，按照模板格式进行数据整理再导入</div>
        </div>
        <div class="txt mt-10">
            <a class="link ml-10" @click="downloadfile">下载模板</a>
        </div>
        <div class="txt mt-20">2.选择文件导入</div>
        <div class="txt mt-10">
            <label class="file-button">
                <input type="file" accept=".xls,.xlsx" @change="onFileSelected" ref="selectedFileRef" /><a class="link ml-10">选取文件</a
                ><span class="file-box" style="display: inline-block">{{ fileName }}</span>
            </label>
            <span id="wait_loading" class="ml-20" style="display: none">请稍等...</span>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click="SubmitEmployInfo">导入</a>
            <a class="button ml-10" @click="divCancel(false)">取消</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { request } from "@/util/service";
import { globalExport } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { useLoading } from "@/hooks/useLoading";
import { ref } from "vue";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";
const emits = defineEmits(["cancelImport"]);

function downloadfile() {
    globalExport(`/api/Employee/ExportTemplate`);
}

const fileName = ref("");
const selectedFile = ref();
const selectedFileRef = ref();
const onFileSelected = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const file: File = (input.files as FileList)[0];
    if (!file) {
        fileName.value = "";
        selectedFile.value = null;
        return;
    }
    fileName.value = file.name;
    selectedFile.value = file;
};

function divCancel(getData: boolean) {
    fileName.value = "";
    selectedFile.value = null;
    if (selectedFileRef.value) selectedFileRef.value!.value = "";
    emits("cancelImport", getData);
}

function SubmitEmployInfo() {
    if (!selectedFile.value) {
        ElNotify({
            type: "warning",
            message: "请选择文件",
        });
        return;
    }
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    useLoading().enterLoading("努力加载中，请稍候...");
    request({
        url: `/api/Employee/Import`,
        method: "post",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    }).then((res: any) => {
        useLoading().quitLoading();
        if (res.state === 1000 && res.data) {
            ElNotify({
                message: res.msg,
                type: "success",
            });
            divCancel(true);
            handleExpiredCheckData(ExpiredCheckModuleEnum.Salary);
            window.dispatchEvent(new CustomEvent('reloadAccountSubjectTree'));
        } else if (res.state === 2000) {
            ElNotify({
                message: res.msg,
                type: "warning",
            });
        }else{
            ElNotify({
                message: res.msg,
                type: "warning",
            });
        }
    });
}
</script>

<style scoped lang="less">
// @import "@/style/Common.less";
.upload-content {
    // position: absolute;
    // top: 0;
    width: 1000px;
    background-color: #ffffff;
    margin: 0 auto;
    & .txt {
        text-align: left;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        padding-left: 282px;
        &.first-item {
            margin-top: 60px;
        }
    }
    & .file-button {
        height: 20px;
        position: relative;
        display: inline-block;
        overflow: hidden;
    }
    & .file-box {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        margin-left: 20px;
        max-width: 160px;
        display: inline;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: top;
    }
    & .buttons {
        padding: 20px 0 60px;
        text-align: center;
    }
}
</style>
