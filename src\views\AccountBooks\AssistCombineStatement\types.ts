export interface ISearchBack {
    subjects: Array<string>;
    rows: Array<ITableData>;
    count: number;
}

export interface ITableData {
    firstAACode: string;
    firstAAName: string;
    secondAACode: string;
    secondAAName: string;
    fcName: string;
    fcCode: string;
    items: Array<Item>;
}

export interface Item {
    initialCredit: number;
    initialDebit: number;
    credit: number;
    debit: number;
    totalCredit: number;
    totalDebit: number;
    yearCredit: number;
    yearDebit: number;
    changeout: number;
    initialCreditFc: number;
    initialDebitFc: number;
    creditFc: number;
    debitFc: number;
    totalCreditFc: number;
    totalDebitFc: number;
    yearCreditFc: number;
    yearDebitFc: number;
    changeoutFc: number;
}
