export interface ILoadVoucherParams {
    pid: number;
    vid: number;
    voucherListQueryParams?: IVoucherListQueryParams;
}
export interface IVoucherListQueryParams {
    description?: string;
    asubId?: number;
    asubCode?: string;
    startAmount?: number;
    endAmount?: number;
    selectorType?: number;
    startPId?: number;
    endPId?: number;
    startDate?: string;
    endDate?: string;
    vid?: number;
    approvalStatus?: number;
    vgId?: number;
    prepareBy?: string;
    startVNum?: number;
    endVNum?: number;
    sortColumn?: number;
    sortType?: number;
    note?: string;
    searchInfo?: string;
    onlyTempVoucher?: boolean;
}
