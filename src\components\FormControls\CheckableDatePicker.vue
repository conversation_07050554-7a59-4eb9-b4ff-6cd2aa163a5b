<template>
  <div
    class="checkable-date-picker"
    :class="{ 'is-invalid': !isValid && showError }">
    <el-date-picker
      v-model="innerValue"
      v-bind="$attrs"
      :class="borderClass"
      @change="handleChange"
      @blur="handleBlur"
      :prefix-icon="renderPrefix"
      @focus="handleFocus">
      <!-- 错误提示工具提示 -->
    </el-date-picker>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, h, shallowRef } from "vue"
  import { WarningFilled, Calendar } from "@element-plus/icons-vue"
  import { ElTooltip } from "element-plus"
  import { useValidate, type Rule } from "./useValidate"

  // 定义props
  const props = withDefaults(
    defineProps<{
      // 校验相关
      rule?: Rule // 只支持单个规则对象
      validateTrigger?: string // 全局默认的校验触发时机
    }>(),
    {
      validateTrigger: "blur",
    },
  )

  // 定义事件
  const emits = defineEmits<{
    (e: "update:modelValue", value: any): void
    (e: "change", value: any): void
    (e: "blur", value: any): void
    (e: "focus", value: any): void
  }>()

  // 使用v-model
  const modelValue = defineModel<any>()

  // 使用验证钩子
  const { innerValue, isValid, errorMessage, showError, borderClass, handleChange, handleBlur, handleFocus, validate } = useValidate(
    props,
    emits,
    modelValue,
  )

  // // 使用render函数渲染前缀图标和错误提示
  const renderPrefix = shallowRef({
    render() {
      // 如果校验不通过且显示错误，则显示警告图标和错误提示
      if (!isValid.value && showError.value) {
        return h(
          ElTooltip,
          {
            content: errorMessage.value,
            placement: "right",
            effect: "light",
          },
          {
            default: () => h(WarningFilled),
          },
        )
      }
      // 如果校验通过或不显示错误，则返回默认的日期图标
      return h(Calendar)
    },
  })
  // 使用计算属性返回h函数创建的虚拟DOM

  // 暴露方法
  defineExpose({
    validate,
  })

  // 挂载时校验
  onMounted(() => {
    validate()
  })
</script>

<style lang="scss" scoped>
  .checkable-date-picker {
    width: 100%;
    position: relative;

    .error-message {
      font-size: 12px;
      margin-top: 4px;

      &.warning-text {
        color: var(--el-color-warning);
      }

      &.error-text {
        color: var(--el-color-danger);
      }
    }

    .error-tooltip {
      position: absolute;
      left: 36px;
      top: 0;
      background-color: white;
      padding: 8px 12px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      white-space: nowrap;
      z-index: 9999;
      font-size: 12px;
      display: none;

      &.warning {
        border: 1px solid var(--el-color-warning);
        color: var(--el-color-warning);
      }

      &.error {
        border: 1px solid var(--el-color-danger);
        color: var(--el-color-danger);
      }

      &:before {
        content: "";
        position: absolute;
        left: -5px;
        top: 8px;
        width: 8px;
        height: 8px;
        background-color: white;
        transform: rotate(45deg);
        border-left: 1px solid;
        border-bottom: 1px solid;
        border-color: inherit;
      }
    }

    :deep(.el-input__prefix:hover) + .error-tooltip {
      display: block;
    }

    .common-border {
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-border-color) inset;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }

    :deep(.prompt-border .el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-color-warning) inset;

      .el-input__prefix {
        color: var(--el-color-warning);
      }
    }

    :deep(.force-border .el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--el-color-danger) inset;

      .el-input__prefix {
        color: var(--el-color-danger);
      }
    }

    :deep(.el-date-editor) {
      width: 100%;
    }

    :deep(.el-input__prefix) {
      font-size: 16px;
      display: flex;
      align-items: center;
    }
  }
</style>
