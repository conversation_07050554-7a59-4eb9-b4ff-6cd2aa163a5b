<template>
    <div class="custom-statement-home-page main-content">
        <div class="title">自定义报表</div>
        <div class="main-top main-tool-bar space-between split-line">
            <div class="main-tool-left" v-if="isErp">
                <a v-permission="['customstatement-canedit']" class="button solid-button" @click="addStatement('goAdd')">新增</a>
                <a v-permission="['customstatement-canedit']" class="button ml-10 solid-button" @click="emits('goPivotTable')"
                    >数据透视表</a
                >
                <a v-permission="['customstatement-canedit']" class="button ml-10" @click="copyStatement()">复制</a>
                <a v-permission="['customstatement-canshare']" class="button ml-10" @click="showShareDialog()">分享到账套</a>
                <a v-permission="['customstatement-canexport']" class="button ml-10" @click="exportTemplate()">导出模板</a>
                <a v-permission="['customstatement-canexport']" class="button ml-10" @click="exportModel()">导出报表</a>
                <a v-permission="['customstatement-candelete']" class="button ml-10" @click="batchDeleteStatementModel()">批量删除</a>
                <ErpRefreshButton></ErpRefreshButton>
            </div>
            <div class="main-tool-left" v-else>
                <a v-permission="['customstatement-canedit']" class="button solid-button" @click="addStatement('goPivotTable')">新增</a>
                <a v-permission="['customstatement-canedit']" class="button ml-10" @click="copyStatement()">复制</a>
                <a v-permission="['customstatement-candelete']" class="button ml-10" @click="batchDeleteStatementModel()">批量删除</a>
            </div>
            <div class="main-tool-right" v-if="!isErp">
                <RefreshButton></RefreshButton>
            </div>
        </div>
        <div class="main-center">
            <Table
                ref="customStatementTableRef"
                class="erp-table custom-statement-table-main custom-table"
                :columns="columns"
                v-loading="loading"
                :data="tableData"
                :empty-text="emptyText"
                :page-is-show="true"
                :layout="paginationData.layout"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :scrollbarShow="Boolean(tableData.length)"
                :currentPage="paginationData.currentPage"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                @selection-change="tableSelectionChange"
                @refresh="handleRerefresh"
                @row-click="tableRowClick"
                :tableName="setModule"
            >
                <template #statementCode>
                    <el-table-column 
                        label="报表编码" 
                        min-width="100"
                        prop="statementCode"
                        :width="getColumnWidth(setModule, 'statementCode')"
                    >
                        <template #default="scope">
                            <a
                                class="link"
                                @click.stop.prevent="editStatementModel(scope.row.statementId, scope.row.customStatementType, false)"
                            >
                                {{ scope.row.statementCode }}</a
                            >
                        </template>
                    </el-table-column>
                </template>
                <template #statementName>
                    <el-table-column 
                        label="报表名称" 
                        min-width="100"
                        prop="statementName"
                        :width="getColumnWidth(setModule, 'statementName')"
                    >
                        <template #default="scope">
                            <a
                                class="link"
                                @click.stop.prevent="editStatementModel(scope.row.statementId, scope.row.customStatementType, false)"
                            >
                                {{ scope.row.statementName }}</a
                            >
                        </template>
                    </el-table-column>
                </template>
                <template #operation>
                    <el-table-column label="操作" min-width="120" :resizable="false">
                        <template #default="scope">
                            <a
                                v-permission="['customstatement-canedit']"
                                class="link"
                                @click.stop.prevent="editStatementModel(scope.row.statementId, scope.row.customStatementType, true)"
                            >
                                编辑</a
                            >
                            <a
                                v-permission="['customstatement-candelete']"
                                class="link"
                                @click.stop.prevent="deleteStatementModel(scope.row.statementId, scope.row.customStatementType)"
                            >
                                删除</a
                            >
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
        <el-dialog v-model="shareDialogShow" title="请选择分享到的账套（可多选）" center width="446" class="custom-confirm dialogDrag">
            <div class="share-dialog-container" v-dialogDrag>
                <div class="search-container">
                    <input type="text" id="shareSearchInfo" placeholder="请输入关键字" v-model="accountSetSearch" autocomplete="off" />
                    <a class="button solid-button">搜索</a>
                </div>
                <div id="shareAccountSetListContainer" class="accountsets">
                    <el-checkbox-group v-model="accountChecked">
                        <el-checkbox class="accountset" v-for="item in accountSetList" :key="item.asid" :label="item.asid"
                            >{{ item.asname }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="shareStatementModelConfirm()">确定</a>
                    <a
                        class="button"
                        @click="
                            shareDialogShow = false;
                            accountSetSearch = '';
                        "
                        >取消</a
                    >
                </div>
            </div>
        </el-dialog>
        <IntroductionPivotDialog v-if="!isErp" v-model="introductionPivotDialogShow" @goPivotTable="emits('goPivotTable')">
        </IntroductionPivotDialog>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { request, type IResponseModel } from "@/util/service";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { usePagination } from "@/hooks/usePagination";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import $bus from "@/bus";
import { getLocalStorage } from "@/util/localStorageOperate";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { globalExport } from "@/util/url";
import type { ICustomStatementListItem, ICustomStatementDetail, IAccountSetListItem, IPivotTableDetail } from "../types";
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
import { useLoading } from "@/hooks/useLoading";
import { useAccountSetStore } from "@/store/modules/accountSet";
import RefreshButton from "@/components/RefreshButton/index.vue";
import IntroductionPivotDialog from "./IntroductionPivotDialog.vue";
import { getGlobalToken } from "@/util/baseInfo";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "CusStateHomePage";
const accountsetStore = useAccountSetStore();
const currentAccountSet = useAccountSetStore().accountSet;

const customStatementTableRef = ref();
const isErp = ref(window.isErp);

const emits = defineEmits(["goAdd", "editStatementModel", "goPivotTable"]);
const tableData = ref<ICustomStatementListItem[]>([]);
const columns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 55, headerAlign: "center", align: "center", reserveSelection: true },
    { slot: "statementCode" },
    { slot: "statementName" },
    {
        label: "报表类型",
        prop: "customStatementType",
        minWidth: 80,
        align: "left",
        headerAlign: "left",
        formatter: (row: any, column: any, cellValue: any, index: number) => {
            return cellValue === 1 ? "自定义报表" : "数据透视表";
        },
        width: getColumnWidth(setModule, "customStatementType")
    },
    {
        label: "报表期间",
        prop: "periodStr",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "periodStr")
    },
    {
        label: "备注",
        prop: "note",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "note")
    },
    {
        label: "创建人",
        prop: "createdBy",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "createdBy")
    },
    {
        label: "创建时间",
        prop: "createdDate",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "createdDate")
    },
    {
        label: "修改人",
        prop: "modifiedBy",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "modifiedBy")
    },
    {
        label: "修改时间",
        prop: "modifiedDate",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "modifiedDate")
    },
    { slot: "operation" },
]);
let loading = ref(false);
let emptyText = ref(" ");
const getTableList = () => {
    loading.value = true;
    customStatementTableRef.value?.getTable()?.clearSelection();
    checkedTableData.value = [];
    request({
        url: `/api/CustomStatement/PagingList?PageIndex=${paginationData.currentPage}&PageSize=${paginationData.pageSize}`,
        method: "get",
    })
        .then((res: any) => {
            loading.value = false;
            if (res.state === 1000) {
                tableData.value = res.data.data.length ? (res.data.data as ICustomStatementListItem[]) : [];
                paginationData.total = res.data.count;
            } else {
                tableData.value = [];
                ElNotify({
                    type: "warning",
                    message: res.msg || "获取数据失败",
                });
            }
            if (!tableData.value.length) {
                paginationData.total = 0;
                emptyText.value = "暂无数据";
            }
        })
        .catch((error) => {
            loading.value = false;
            tableData.value = [];
            paginationData.total = 0;
            emptyText.value = "暂无数据";
        });
};
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], getTableList);
let checkedTableData = ref<ICustomStatementListItem[]>([]);
const tableSelectionChange = (value: ICustomStatementListItem[]) => {
    checkedTableData.value = value;
};

const addStatement = (emit: "goPivotTable" | "goAdd") => {
    if (paginationData.total >= 99) {
        let statementType = emit === "goPivotTable" ? "数据透视表" : "自定义报表";
        ElNotify({
            type: "warning",
            message: `${statementType}个数最大99张，请删除其他${statementType}后再新增。`,
        });
        return;
    }
    emits(emit);
};
const copyStatement = () => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "请选择要复制的数据",
        });
        return false;
    }
    if (paginationData.total >= 99 || checkedTableData.value.length + paginationData.total > 99) {
        let statementType = isErp.value ? "自定义报表" : "数据透视表";
        ElNotify({
            type: "warning",
            message: `${statementType}个数最大99张，请删除其他${statementType}后再复制。`,
        });
        return;
    }
    let { statementIds, tableIds } = filterStatements();

    useLoading().enterLoading("努力加载中...");
    request({
        url: "/api/CustomStatement/CopyStatementModel",
        params: { statementIds: statementIds.join(","), tableIds: tableIds.join(",") },
        method: "post",
    }).then((res: IResponseModel<number>) => {
        useLoading().quitLoading();
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "报表复制成功",
            });
            getTableList();
            statementIds.length && $bus.emit("reloadCustomStatementList");
        }
    });
};
const exportTemplate = () => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "请选择要导出的报表",
        });
        return false;
    }
    let customStatements = checkedTableData.value.filter((v) => v.customStatementType === 1);
    if (!customStatements.length) {
        checkedTableData.value = [];
        customStatementTableRef.value?.getTable()?.clearSelection();

        ElNotify({
            type: "warning",
            message: "数据透视表暂不支持导出模板",
        });
        return false;
    }

    customStatements.forEach((n, i) => {
        setTimeout(() => {
            globalExport(`/api/CustomStatement/ExportTemplate?statementId=${n.statementId}`);
        }, 2000 * i);
    });
    if (customStatements.length < checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "数据透视表暂不支持导出模板,已跳过",
        });
    }
};
const exportModel = () => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "请选择要导出的报表",
        });
        return false;
    }
    let customStatements = checkedTableData.value.filter((v) => v.customStatementType === 1);
    if (!customStatements.length) {
        checkedTableData.value = [];
        customStatementTableRef.value?.getTable()?.clearSelection();

        ElNotify({
            type: "warning",
            message: "数据透视表暂不支持导出",
        });
        return false;
    }
    customStatements.forEach((n, i) => {
        setTimeout(() => {
            globalExport(
                `/api/CustomStatement/Export?statementId=${n.statementId}&classification=${
                    getLocalStorage("classificationSwitch") || false
                }&pid=${n.pid}`
            );
        }, 2000 * i);
    });
    if (customStatements.length < checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "数据透视表暂不支持导出,已跳过",
        });
    }
};
const shareDialogShow = ref(false);
const accountSetList = ref<IAccountSetListItem[]>([]);
let accountSetListAll: IAccountSetListItem[] = [];
const showShareDialog = () => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "请选择要分享的报表",
        });
        return false;
    }
    shareDialogShow.value = true;
    accountChecked.value = [];
    useLoading().enterLoading("正在加载数据...");
    request({
        url: "/api/CustomStatement/GetCanShareAccountSetList",
        method: "post",
    }).then((res: IResponseModel<IAccountSetListItem[]>) => {
        useLoading().quitLoading();
        if (res.state === 1000) {
            let data = res.data.filter(
                (v: IAccountSetListItem) =>
                    Number(v.accountStandard) === currentAccountSet?.accountingStandard &&
                    Number(v.subAccountStandard) === currentAccountSet?.subAccountingStandard
            );
            accountSetList.value = data;
            accountSetListAll = data;
        }
    });
};
const introductionPivotDialogShow = ref(
    !(localStorage.getItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-pivot-intro-dialog") === "false")
);
onMounted(() => {
    if (!isErp.value) {
        columns.value.splice(3, 3);
    }
});
let accountSetSearch = ref("");
watch(accountSetSearch, (search) => {
    accountSetList.value = search ? accountSetListAll.filter((n) => n.asname.indexOf(search) !== -1) : accountSetListAll;
    accountChecked.value = [];
});
const accountChecked = ref([]);
const shareStatementModelConfirm = () => {
    if (!accountChecked.value.length) {
        ElNotify({
            type: "warning",
            message: "请选择要分享的账套",
        });
        return false;
    }
    let { statementIds, tableIds } = filterStatements();

    useLoading().enterLoading("正在加载数据...");
    request({
        url: `/api/CustomStatement/ShareStatementModel?asIds=${accountChecked.value.join(",")}&statementIds=${statementIds.join(
            ","
        )}&tableIds=${tableIds.join(",")}`,
        method: "post",
    })
        .then((res: IResponseModel<number>) => {
            useLoading().quitLoading();
            if (res.state === 1000) {
                shareDialogShow.value = false;
                accountSetSearch.value = "";
                if (res.data) {
                    ElNotify({
                        type: "success",
                        message: "报表分享成功",
                    });
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg || "分享失败",
                    });
                }
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({
                type: "warning",
                message: "亲，分享失败,请联系管理员~",
            });
        });
};
const batchDeleteStatementModel = () => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "请选择要删除的数据",
        });
        return false;
    }
    let { statementIds, tableIds } = filterStatements();
    ElConfirm("是否确定删除所选报表？").then((r: boolean) => {
        if (r) {
            useLoading().enterLoading("努力加载中...");
            request({
                url: "/api/CustomStatement",
                params: { statementIds: statementIds.join(","), tableIds: tableIds.join(",") },
                method: "delete",
            })
                .then((res: IResponseModel<boolean>) => {
                    useLoading().quitLoading();
                    if (res.data) {
                        ElNotify({
                            type: "success",
                            message: "删除成功",
                        });
                        if (paginationData.currentPage !== 1) {
                            paginationData.currentPage = 1;
                        } else {
                            getTableList();
                        }
                        statementIds.length && $bus.emit("reloadCustomStatementList");
                    }
                })
                .catch(() => {
                    useLoading().quitLoading();
                    ElNotify({
                        type: "warning",
                        message: "亲，删除失败,请联系管理员~",
                    });
                });
        }
    });
};
getTableList();
const editStatementModel = (statementId: number, statementType: number, editable: boolean) => {
    if (statementType === 2 && !editable) {
        $bus.emit("enterPivotView", statementId);
        emits("editStatementModel", statementId, statementType, editable);
        return;
    }
    useLoading().enterLoading("正在加载数据...");

    request({
        url:
            statementType === 2
                ? `/api/CustomStatement/PivotTable?statementId=${statementId}`
                : editable
                ? `/api/CustomStatement?statementId=${statementId}`
                : `/api/CustomStatement/WithData?statementId=${statementId}&pId=&classification=${
                      getLocalStorage("classificationSwitch") || false
                  }`,
        method: "get",
    })
        .then((res: IResponseModel<ICustomStatementDetail | IPivotTableDetail>) => {
            useLoading().quitLoading();
            if (res.state === 1000) {
                statementType === 1 ? $bus.emit("setStatementModel", res.data) : $bus.emit("setPivotTable", res.data);
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({
                type: "warning",
                message: "亲，获取失败,请联系管理员~",
            });
        });
    emits("editStatementModel", statementId, statementType, editable);
};
const deleteStatementModel = (statementId: number, statementType: number) => {
    let goPrePage = tableData.value.length === 1 && paginationData.currentPage > 1;
    ElConfirm("确定删除吗？").then((r) => {
        if (r) {
            request({
                url: `/api/CustomStatement?statementIds=${statementType === 1 ? statementId : ""}&tableIds=${
                    statementType === 2 ? statementId : ""
                }`,
                method: "delete",
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({
                            type: "success",
                            message: "删除成功",
                        });
                        if (goPrePage) {
                            paginationData.currentPage -= 1;
                        } else {
                            getTableList();
                        }
                        statementType === 1 && $bus.emit("reloadCustomStatementList");
                    } else {
                        ElNotify({
                            type: "warning",
                            message: res.msg || "删除失败",
                        });
                    }
                })
                .catch(() => {
                    ElNotify({
                        type: "warning",
                        message: "亲，删除失败,请联系管理员~",
                    });
                });
        }
    });
};
const filterStatements = () => {
    let statementIds: number[] = [];
    let tableIds: number[] = [];

    checkedTableData.value.forEach((v) => {
        if (v.customStatementType === 1) {
            statementIds.push(v.statementId);
        } else if (v.customStatementType === 2) {
            tableIds.push(v.statementId);
        }
    });

    statementIds.sort((a, b) => a - b);
    tableIds.sort((a, b) => a - b);
    return { statementIds, tableIds };
};
const tableRowClick = (row: ICustomStatementListItem) => {
    let selected = checkedTableData.value.findIndex((item: ICustomStatementListItem) => item.statementId === row.statementId) >= 0;
    customStatementTableRef.value?.getTable()?.toggleRowSelection(row, !selected);
};
defineExpose({ getTableList });
</script>

<style lang="less" scoped>
.custom-statement-home-page {
    .custom-statement-table-main {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table {
            flex: 1;
        }
        :deep(.el-table__empty-block) {
            height: calc(100vh - 200px) !important;
            width: calc(100% - 4px) !important;
            min-height: 0;
        }
    }
    .share-dialog-container {
        .search-container {
            font-size: 0;
            padding: 15px 0 8px;
            text-align: center;
            input[type="text"] {
                width: 338px;
                height: 28px;
                border: 1px solid var(--input-border-color);
                outline: none;
                padding: 0;
                padding-left: 10px;
                padding-right: 10px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 28px;
                box-sizing: border-box;
                border-radius: var(--input-border-radius);
            }
            .button {
                width: 56px;
                margin-left: 4px;
            }
        }
        .accountsets {
            margin: 0 24px 20px;
            background-color: #f8f8f8;
            overflow: auto;
            padding: 16px 0;
            height: 186px;
            .accountset {
                padding-left: 16px;
                font-size: var(--h5);
                line-height: 16px;
                color: var(--font-color);
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
                box-sizing: border-box;
                margin: 0;
            }
        }
    }
}
</style>
