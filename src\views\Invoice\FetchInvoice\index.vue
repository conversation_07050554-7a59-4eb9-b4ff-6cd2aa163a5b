<template>
    <div class="content">
        <div class="custom-title">
            <img v-if="isErp" src="@/assets/Icons/corner-marker-left-erp.png" alt="" />
            <img v-else src="@/assets/Icons/corner-marker-left.png" alt="" />
            <h2>一键取票</h2>
            <img v-if="isErp" src="@/assets/Icons/corner-marker-right-erp.png" alt="" />
            <img v-else src="@/assets/Icons/corner-marker-right.png" alt="" />
        </div>
        <p class="description">只需一次维护，即可直接获取进销项发票数据、数电发票电子文件，实现发票智能化</p>
        <div class="introduce">
            <div class="introduce-img">
                <img v-if="isErp" src="@/assets/Invoice/fetch-invoice-image-erp.png" alt="" />
                <img v-else src="@/assets/Invoice/fetch-invoice-image.png" alt="" />
            </div>
            <div class="introduce-content">
                <div class="introduce-item" v-for="item in introduceList" :key="item.index">
                    <div class="item-index">
                        <span>{{ item.index }}</span>
                    </div>
                    <div>
                        <h2 class="item-title">{{ item.title }}</h2>
                        <p class="item-desc">{{ item.desc }}</p>
                    </div>
                </div>
                <div @click="showFetchInvoice()">
                    <el-button class="button solid-button ticket-button" type="primary" :loading="hasInvoiceTask">
                        {{ hasInvoiceTask ? "正在取票中 ..." : "立即取票" }}
                    </el-button>
                </div>

                <div class="invoice-task-tip ml-10" v-if="finishedInvoiceTask !== null">
                    {{ dayjs(finishedInvoiceTask.modifiedDate).format("YYYY-MM-DD HH:mm") }}更新
                    <a @click="openFetchInvoiceTaskShow" class="link pl-10">查看记录</a>
                </div>
            </div>
        </div>
        <FetchInvoice
            v-model:fetchInvoiceShow="fetchInvoiceShow"
            ref="fetchInvoiceRef"
            :hasInvoiceTask="hasInvoiceTask"
            :invoiceTaskList="invoiceTaskList"
            @setCollectTaskId="setCollectTaskId"
            @getInvoiceTaskList="getInvoiceTaskList"
        ></FetchInvoice>
        <FetchInvoiceTask v-model:fetchInvoiceTaskShow="fetchInvoiceTaskShow" v-model:invoiceCategory="invoiceCategory"></FetchInvoiceTask>
        <LastFetchInvoiceTask
            v-model:lastFetchInvoiceTaskShow="lastFetchInvoiceTaskShow"
            v-model:invoiceCategory="invoiceCategory"
            :invoiceTaskList="invoiceTaskList"
            @handlebyDate="handleToInvoiceView"
        ></LastFetchInvoiceTask>
    </div>
</template>
<script lang="ts">
export default {
    name: "FetchInvoice",
};
</script>
<script lang="ts" setup>
import FetchInvoice from "../components/FetchInvoice.vue";
import FetchInvoiceTask from "../components/FetchInvoiceTask.vue";
import LastFetchInvoiceTask from "../components/LastFetchInvoiceTask.vue";
import { request, type IResponseModel } from "@/util/service";
import { ref, nextTick, computed, onActivated } from "vue";
import { getCookie, setCookie } from "@/util/cookie";
import dayjs from "dayjs";
import type { IInvoiceTaskResult, IInvoiceTaskValue } from "../types";
import router from "@/router";
import { useAccountSetStore } from "@/store/modules/accountset";
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
const isErp = ref(window.isErp);
const asId = useAccountSetStore().accountSet?.asId as number;
const invoiceCategory = ref("0");
const introduceList = ref([
    {
        index: "1",
        title: "智能获取发票数据",
        desc: "一键获取进销项发票，轻松获取1万+发票",
    },
    {
        index: "2",
        title: "数电发票电子文件采集",
        desc: "智能获取数电发票电子文件，自动关联发票数据",
    },
    {
        index: "3",
        title: "数据安全保障",
        desc: "单向式获取发票数据，数据安全有保障",
    },
]);
const fetchInvoiceShow = ref(false);
const fetchInvoiceRef = ref();

const fetchInvoiceTaskShow = ref(false);
const lastFetchInvoiceTaskShow = ref(false);
let timer: number = 0;

const reInvoiceTaskList = () => {
    if (timer) return;
    timer = setInterval(() => {
        getInvoiceTaskList();
    }, window.invoiceTaskPolllingInterval*60*1000);
};
const unInvoiceTaskList = () => {
    timer && clearTimeout(timer);
    timer = 0;
};
const getCollectTaskId = () => {
    const id = getCookie("collectTotalTaskId" + asId);
    return id ? parseInt(id) : 0;
};

const openFetchInvoiceTaskShow = () => {
    fetchInvoiceTaskShow.value = true;
};
const hasInvoiceTask = ref(false);
const setCollectTaskId = (val: number) => {
    setCookie("collectTotalTaskId" + asId, val.toString(), "d14");
};
let preCheck = false;
const showFetchInvoice = () => {
    if (preCheck) return;
    if (hasInvoiceTask.value) {
        if (invoiceTaskList.value![0].fileTaskStatus === 1) {
            ElNotify({
                type: "warning",
                message: "取票仍在持续同步获取发票原件中，可通过取票记录查看获取情况，请获取发票原件任务结束后再执行一键取票。",
            });
            return;
        } else {
            fetchInvoiceShow.value = true;
            preCheck = false;
            return;
        }
    }

    preCheck = true;
    request({ url: "/api/TaxBureau/PreCheck", method: "get" })
        .then((res: IResponseModel<IInvoiceTaskValue>) => {
            if (res.state === 1000) {
                fetchInvoiceShow.value = true;
                return;
            } else if (res.state === 2000) {
                if (res.data.result === "running" && hasInvoiceTask.value) {
                    fetchInvoiceShow.value = true;
                    return;
                }
            }
            ElNotify({ type: "warning", message: res.msg });
            return;
        })
        .catch((err) => {
            console.log(err);
            ElNotify({ type: "warning", message: "取票预检查发生错误，请稍后重试。" });
        })
        .finally(() => {
            preCheck = false;
        });
};
const invoiceTaskList = ref<Array<IInvoiceTaskResult>>();
let firstGetData = true;
const getInvoiceTaskList = async () => {
    await request({
        url: `/api/TaxBureau/LastSummaryTask`,
    }).then((res: IResponseModel<Array<IInvoiceTaskResult>>) => {
        invoiceTaskList.value = res.data;
        hasInvoiceTask.value = invoiceTaskList.value && invoiceTaskList.value[0] && invoiceTaskList.value[0].status === 1;
        nextTick(() => {
            if (hasInvoiceTask.value) {
                firstGetData = false;
                reInvoiceTaskList();
            } else {
                unInvoiceTaskList();
            }
        });
        const taskId = getCollectTaskId();
        console.log("path", router.currentRoute.value.path);
        if (
            taskId &&
            router.currentRoute.value.path.toLocaleLowerCase() === "/invoice/fetchinvoice" &&
            invoiceTaskList.value &&
            invoiceTaskList.value[0] &&
            ((invoiceTaskList.value[0].invoiceTaskStatus === 2 && invoiceTaskList.value[0].fileTaskStatus > 0) ||
                invoiceTaskList.value[0].invoiceTaskStatus > 2)
        ) {
            console.log("taskId",invoiceTaskList.value[0]);
            setCollectTaskId(0);
            fetchInvoiceShow.value = false;
            fetchInvoiceTaskShow.value = false;
            lastFetchInvoiceTaskShow.value = true;
            window.dispatchEvent(new CustomEvent("modifyAccountSet"));
            firstGetData = false;
        } else if (invoiceTaskList.value.length > 0 && !hasInvoiceTask.value && firstGetData) {
            showFetchInvoice();
            firstGetData = false;
        }
    });
};

const finishedInvoiceTask = computed(() => {
    if (!invoiceTaskList.value) return null;
    for (let i = 0; i < invoiceTaskList.value.length; i++) {
        if (invoiceTaskList.value[i].invoiceTaskStatus !== 1) return invoiceTaskList.value[i];
    }
    return null;
});
onActivated(() => {
    getInvoiceTaskList();
});
getInvoiceTaskList();
const handleToInvoiceView = (startDate: string, endDate: string, salesCount: number, purchaseCount: number) => {
    const canOutput = checkPermission(["invoice-output-canview"]);
    const canInput = checkPermission(["invoice-input-canview"]);
    function goSalseInvoice() {
        globalWindowOpenPage("/Invoice/SalesInvoice?from=FetchInvoice&searchStartDate=" + startDate + "&searchEndDate=" + endDate, "销项发票");
    }
    function goPurchaseInvoice() {
        globalWindowOpenPage("/Invoice/PurchaseInvoice?from=FetchInvoice&searchStartDate=" + startDate + "&searchEndDate=" + endDate, "进项发票");
    }
    if (canOutput && canInput) {
        salesCount || !purchaseCount ? goSalseInvoice() : goPurchaseInvoice();
    } else if (canOutput) {
        goSalseInvoice();
    } else if (canInput) {
        goPurchaseInvoice();
    } else {
        ElNotify({
            message: "您暂无进销项发票查看权限，请联系管理员添加",
            type: "warning",
        });
    }
    lastFetchInvoiceTaskShow.value = false;
};
</script>
<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.content {
    display: inline-block;
    background-color: #f9fffd;
    min-height: 100%;
    height: auto;
    .custom-title {
        margin-top: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
            width: 28px;
            height: 28px;
            padding: 0 20px;
        }
        h2 {
            font-size: 28px;
        }
    }
    .description {
        font-size: 20px;
        margin: 5px 0 15px;
    }
    .introduce {
        display: flex;
        justify-content: center;
        padding: 40px;
        .introduce-img {
            width: 405px;
            height: 336px;
            margin-right: 65px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .introduce-content {
            text-align: left;
            position: relative;
            top: -20px;
            .introduce-item {
                text-align: left;
                display: flex;
                .item-index {
                    font-weight: bold;
                    height: 79px;
                    padding-right: 10px;
                    span {
                        display: inline-block;
                        width: 24px;
                        height: 24px;
                        background-color: var(--main-color);
                        color: #f9fffd;
                        border-radius: 12px;
                        font-size: 14px;
                        text-align: center;
                        line-height: 24px;
                        margin-top: 12px;
                    }
                }
                .item-title {
                    font-size: 20px;
                    font-weight: bold;
                    height: 28px;
                    margin: 10px 0;
                }
                .item-desc {
                    font-size: 18px;
                    height: 38px;
                    margin: 0;
                }
            }
            .ticket-button {
                display: flex;
                align-items: center;
                width: 165px;
                height: 40px;
                line-height: 40px;
                font-size: 18px;
                margin-top: 20px;
                margin-left: 34px;
                background-color: var(--main-color);
                color: #fff;
                border-radius: 4px;
            }
            .invoice-task-tip {
                margin-top: 20px;
                margin-left: 34px;
            }
        }
    }
}
body[erp] {
    .content {
        background-color: #fff;
    }
}
</style>
