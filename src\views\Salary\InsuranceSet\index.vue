<template>
    <div class="content narrow-content" :style="isErp ? 'overflow-y:visible;' : ''">
        <div class="title">五险一金设置</div>
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content" :style="isErp ? 'overflow:visible;' : ''">
                    <div style="padding: 10px 0" class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <a class="button solid-button" @click="addInsuranceSet" v-permission="['insuranceset-canedit']">新增</a>
                        </div>
                        <div class="main-tool-right">
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div v-if="isErp" class="divider-line"></div>
                    <!-- :class="isErp?'insuranceSet-center':''" -->
                    <div class="main-center" :class="isErp ? 'insuranceSet-center' : ''">
                        <Table 
                            :data="tableData" 
                            :columns="columns" 
                            :scrollbarShow="true"
                            :max-height="isErp ? 'calc(100vh - 90px)' : 'calc(100vh - 170px)'"
                            :tableName="setModule"
                        >
                            <template #paymentMode>
                                <el-table-column 
                                    label="缴纳方式" 
                                    min-width="110" 
                                    align="left" 
                                    header-align="left"
                                    prop="paymentMode"
                                    :width="getColumnWidth(setModule, 'itemName')"
                                >
                                    <template #default="scope">
                                        <span>{{ paymentModeList[scope.row.paymentMode] }}</span> 
                                    </template>
                                </el-table-column>
                            </template>
                            <template #companyPercent>
                                <el-table-column 
                                    label="公司缴纳比例" 
                                    min-width="165" 
                                    align="left" 
                                    header-align="left"
                                    prop="companyPercent"
                                    :width="getColumnWidth(setModule, 'companyPercent')"
                                >
                                    <template #default="scope">
                                        <span>{{ formatDataInsurance(scope.row, scope.row.companyPercent, 0) }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #personPercent>
                                <el-table-column 
                                    label="个人缴纳比例" 
                                    min-width="165" 
                                    align="left" 
                                    header-align="left"
                                    prop="personPercent"
                                    :width="getColumnWidth(setModule, 'personPercent')"
                                >
                                    <template #default="scope">
                                        <span>{{ formatDataInsurance(scope.row, scope.row.personPercent, 0) }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #companyAmount>
                                <el-table-column 
                                    label="公司缴纳金额" 
                                    min-width="165" 
                                    align="left" 
                                    header-align="left"
                                    prop="companyAmount"
                                    :width="getColumnWidth(setModule, 'companyAmount')"
                                >
                                    <template #default="scope">
                                        <span>{{ formatDataInsurance(scope.row, scope.row.companyAmount, 1) }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #personAmount>
                                <el-table-column 
                                    label="个人缴纳金额" 
                                    min-width="165" 
                                    align="left" 
                                    header-align="left"
                                    prop="personAmount"
                                    :width="getColumnWidth(setModule, 'personAmount')"
                                >
                                    <template #default="scope">
                                        <span>{{ formatDataInsurance(scope.row, scope.row.personAmount, 1) }}</span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #operation>
                                <el-table-column label="操作" min-width="110" align="left" header-align="left" :resizable="false">
                                    <template #default="scope">
                                        <a
                                            class="link"
                                            @click="
                                                EditHandle(
                                                    scope.row.itemId,
                                                    scope.row.itemName,
                                                    scope.row.paymentMode,
                                                    scope.row.companyPercent,
                                                    scope.row.personPercent,
                                                    scope.row.companyAmount,
                                                    scope.row.personAmount,
                                                    scope.row.isNewItem
                                                )
                                            "
                                            v-permission="['insuranceset-canedit']"
                                        >
                                            编辑
                                        </a>
                                        <a
                                            class="link"
                                            v-if="scope.row.isNewItem"
                                            @click="deleteHandle(scope.row.itemId)"
                                            v-permission="['insuranceset-candelete']"
                                        >
                                            删除
                                        </a>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
            </template>
            <template #add>
                <div>
                    <div class="slot-title">五险一金设置</div>

                    <div class="edit-content" style="width: 1000px" :style="isErp ? 'height:auto' : ''">
                        <div class="insurance-kind" v-if="type === 'edit'">{{ addData.itemName }}</div>
                        <div class="line-item">
                            <div class="line-item-title" style="margin-left: 50px"><span class="highlight-red">*</span>缴纳项目：</div>
                            <div class="line-item-field project">
                                <el-input
                                    ref="project"
                                    v-model="addData.itemName"
                                    placeholder="养老保险"
                                    style="width: 138px; cursor: default"
                                    :disabled="readonly"
                                    @input="formatProject"
                                    @keyup.enter="focusNext('project')"
                                ></el-input>
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title" style="margin-left: 50px"><span class="highlight-red">*</span>缴纳方式：</div>
                            <div class="line-item-field" style="color: black">
                                <el-select v-model="addData.paymentMode" :teleported="false" style="width: 138px; " @change="handleChangePay">
                                    <el-option :value="0" label="按比例缴纳">按比例缴纳</el-option>
                                    <el-option :value="1" label="按金额缴纳">按金额缴纳</el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item" v-if="addData.paymentMode === 0">
                            <div class="line-item-title" style="margin-left: 50px"><span class="highlight-red">*</span>公司缴纳比例：</div>
                            <div class="line-item-field" style="color: black">
                                <el-input
                                    ref="companyPercent"
                                    v-model="addData.companyPercent"
                                    placeholder="20"
                                    style="width: 138px"
                                    @input="formatPercentOrAmount($event, 'company', 0)"
                                    @keyup.enter="focusNext('companyPercent')"
                                ></el-input>
                                &nbsp;%
                            </div>
                        </div>
                        <div class="line-item" v-if="addData.paymentMode === 0">
                            <div class="line-item-title" style="margin-left: 50px"><span class="highlight-red">*</span>个人缴纳比例：</div>
                            <div class="line-item-field" style="color: black">
                                <el-input
                                    ref="personPercent"
                                    v-model="addData.personPercent"
                                    placeholder="20"
                                    style="width: 138px"
                                    @input="formatPercentOrAmount($event, 'person', 0)"
                                    @keyup.enter="focusNext('personPercent')"
                                ></el-input>
                                &nbsp;%
                            </div>
                        </div>
                        <div class="line-item" v-if="addData.paymentMode === 1">
                            <div class="line-item-title" style="margin-left: 50px"><span class="highlight-red">*</span>公司缴纳金额：</div>
                            <div class="line-item-field" style="color: black">
                                <el-input
                                    ref="companyAmount"
                                    v-model="addData.companyAmount"
                                    placeholder="0"
                                    style="width: 138px"
                                    @input="formatPercentOrAmount($event, 'company', 1)"
                                    @keyup.enter="focusNext('companyAmount')"
                                ></el-input>
                            </div>
                        </div>
                        <div class="line-item" v-if="addData.paymentMode === 1">
                            <div class="line-item-title" style="margin-left: 50px"><span class="highlight-red">*</span>个人缴纳金额：</div>
                            <div class="line-item-field" style="color: black">
                                <el-input
                                    ref="personAmount"
                                    v-model="addData.personAmount"
                                    placeholder="0"
                                    style="width: 138px"
                                    @input="formatPercentOrAmount($event, 'person', 1)"
                                    @keyup.enter="focusNext('personAmount')"
                                ></el-input>
                            </div>
                        </div>
                        <div class="buttons" style="border: 0">
                            <a class="button solid-button" @click="SubmitInsuranceSet">保存</a>
                            <a class="button ml-10" @click="BackToMain">取消</a>
                        </div>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "InsuranceSet",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import Table from "@/components/Table/index.vue";
import { request, type IResponseModel } from "@/util/service";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { nextTick, onMounted, watch } from "vue";
import { ref, reactive, watchEffect } from "vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "InsuranceSet";
const isErp = ref(window.isErp);
const slots = ["main", "add"];
const currentSlot = ref("main");
const addData = reactive({
    itemName: "",
    companyPercent: "",
    personPercent: "",
    paymentMode: 0,
    companyAmount: "",
    personAmount: "",
});
const project = ref();
const companyPercent = ref();
const personPercent = ref();
const companyAmount = ref();
const personAmount = ref();
const columns = ref<Array<IColumnProps>>([
    { label: "缴纳项目", prop: "itemName", align: "left", headerAlign: "left", minWidth: 165, width: getColumnWidth(setModule, 'itemName') },
    { slot: "paymentMode" },
    { slot: "companyPercent" },
    { slot: "personPercent" },
    { slot: "companyAmount" },
    {   slot: "personAmount",},
    { slot: "operation" },
]);
const paymentModeList:{[key: number]: string} = {
    0: "按比例缴纳",
    1: "按金额缴纳",
}

function formatDataInsurance(row: IList, value: string | number, way: number) {
    let result = "";
    if (!way) { //比例
        result = !row.paymentMode ? (value ? (value + "%") : ((row.itemName === "工伤保险" || row.itemName === "生育保险") ? "" : "0%")) : "--";
    } else { //金额
        result = !row.paymentMode ? "--" : (value ? String(value) : ((row.itemName === "工伤保险" || row.itemName === "生育保险") ? "" : "0"));
    }
    return result;
}

const readonly = ref(false);
const type = ref("add");

const companyPercentTemp = ref<string>("");
const personPercentTemp = ref<string>("");
const companyAmountTemp = ref<string>("");
const personAmountTemp = ref<string>("");

function focusNext(refName: string) {
    switch (refName) {
        case "project":
            (companyPercent as any).value.focus();
            break;
        case "companyPercent":
            (personPercent as any).value.focus();
            break;
        case "personPercent":
            (project as any).value.focus();
            break;
        case "companyAmount":
            (personAmount as any).value.focus();
            break;
        case "personAmount":
            (project as any).value.focus();
            break;
    }
}

function formatProject(value: string) {
    value = value.slice(0, 10);
    addData.itemName = value;
}

function formatPercentOrAmount(value: string, type: string, mode: number) {
    value = value.replace(/。/g, ".");
    const dotIndex = value.indexOf(".");
    const decimalLength = mode === 0 ? 3 : 2; //比例小数点3位，金额小数点2位
    if (dotIndex !== -1) {
        // 如果已经存在小数点，去除后面的小数点
        value = value.slice(0, dotIndex + 1) + value.slice(dotIndex).replace(/\./g, "");
        const decimalPart = value.slice(dotIndex + 1);
        if (decimalPart.length > decimalLength) {
            value = value.slice(0, dotIndex + decimalLength + 1);
        }
    }
    if (value.length >= 18) {
        value = value.slice(0, 1) + "." + value.slice(1);
    }
    if (mode === 0) {
        if (type === "company") {
            addData.companyPercent = value.replace(/[^0-9.]/g, "");
        } else {
            addData.personPercent = value.replace(/[^0-9.]/g, "");
        }
    } else {
        if (type === "company") {
            addData.companyAmount = value.replace(/[^0-9.]/g, "");
        } else {
            addData.personAmount = value.replace(/[^0-9.]/g, "");
        }
    }
}

function addInsuranceSet() {
    if (tableData.value.length >= 20) {
        ElNotify({
            type: "warning",
            message: "五险一金缴纳项目最多20项",
        });
        return;
    }
    currentSlot.value = "add";
    type.value = "add";
    addData.itemName = "";
    addData.paymentMode = 0;
    addData.companyPercent = "";
    addData.personPercent = "";
    addData.companyAmount = "";
    addData.personAmount = "";
    readonly.value = false;
    nextTick(() => {
        isInit = true;
    });
}

const itemId = ref("");
function EditHandle(
    ItemId: string, 
    ItemName: string, 
    ItemWay: number,
    CompanyPercent: string, 
    PersonPercent: string, 
    CompanyAmount: string, 
    PersonAmount: string, 
    bool: boolean
) {
    currentSlot.value = "add";
    type.value = "edit";
    itemId.value = ItemId;
    addData.itemName = ItemName;
    addData.paymentMode = ItemWay || 0;
    addData.companyPercent = CompanyPercent;
    addData.personPercent = PersonPercent;
    addData.companyAmount = CompanyAmount;
    addData.personAmount = PersonAmount;
    companyPercentTemp.value = CompanyPercent;
    personPercentTemp.value = PersonPercent;
    companyAmountTemp.value = CompanyAmount;
    personAmountTemp.value = PersonAmount;
    readonly.value = !bool;
    nextTick(() => {
        isInit = true;
    });
}

function deleteHandle(ItemId: string) {
    ElConfirm("亲，确认要删除吗？").then((r: boolean) => {
        if (r) {
            request({
                url: `/api/InsuranceSettings?itemId=${ItemId}`,
                method: "delete",
            }).then((res: any) => {
                if (res.state === 1000) {
                    ElNotify({
                        type: "success",
                        message: "删除成功",
                    });
                    window.dispatchEvent(new CustomEvent("updateInsuranceSet"));
                    getTableData();
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
}
interface IList {
    asId: number;
    itemId: number;
    itemName: string;
    itemWay: number;
    companyPercent: number;
    personPercent: number;
    companyAmount: number;
    personAmount: number;
    insuranceDataStateStr: string;
    insuranceDataState: number;
    insuranceTypeStr: string;
    insuranceType: number;
    isNewItem: boolean;
    paymentMode: number;
}
const tableData = ref<Array<IList>>([]);
function getTableData() {
    request({
        url: `/api/InsuranceSettings/List`,
    })
        .then((res: IResponseModel<Array<IList>>) => {
            tableData.value = res.data;
        })
        .catch((error) => {
            console.log(error);
        });
}

function handleChangePay(val: number) {
    if (val === 0) {
        addData.companyAmount = "";
        addData.personAmount = "";
    } else {
        addData.companyPercent = "";
        addData.personPercent = "";
    }
}

let isSubmit = false;
function SubmitInsuranceSet() {
    if (isSubmit) {
        return;
    }
    if (type.value == "add") {
        if (addData.itemName.trim() === "") {
            ElNotify({
                type: "warning",
                message: "亲，缴纳项目不能为空。",
            });
            return false;
        }
        if (addData.paymentMode === 0) { //按比例
            if (addData.companyPercent === "") {
                ElNotify({
                    type: "warning",
                    message: "亲，公司缴纳比例不能为空。",
                });
                return false;
            } else if (addData.personPercent === "") {
                ElNotify({
                    type: "warning",
                    message: "亲，个人缴纳比例不能为空。",
                });
                return false;
            }
        } else {  //按金额
            if (addData.companyAmount === "") {
                ElNotify({
                    type: "warning",
                    message: "亲，公司缴纳金额不能为空。",
                });
                return false;
            } else if (addData.personAmount === "") {
                ElNotify({
                    type: "warning",
                    message: "亲，个人缴纳金额不能为空。",
                });
                return false;
            }
        }
        isSubmit = true;
        request({
            url: `/api/InsuranceSettings`,
            method: "post",
            data: addData,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            },
        }).then((res: any) => {
            isSubmit = false;
            if (res.data) {
                currentSlot.value = "main";
                ElNotify({
                    type: "success",
                    message: "亲，保存成功。",
                });
                window.dispatchEvent(new CustomEvent("updateInsuranceSet"));
                getTableData();
            } else {
                ElNotify({
                    type: "warning",
                    message: "亲，保存失败。缴纳项目名称重复。",
                });
            }
        });
    } else {
        if (addData.paymentMode === 0) {
            if (addData.companyPercent === "" && addData.companyPercent !== companyPercentTemp.value) {
                ElNotify({
                    type: "error",
                    message: "亲，公司缴纳比例不能为空。",
                });
                return false;
            } else if (addData.personPercent === "" && addData.personPercent !== personPercentTemp.value) {
                ElNotify({
                    type: "error",
                    message: "亲，个人缴纳比例不能为空。",
                });
                return false;
            }
        } else {
            if (addData.companyAmount === "" && addData.companyAmount !== companyAmountTemp.value) {
                ElNotify({
                    type: "error",
                    message: "亲，公司缴纳金额不能为空。",
                });
                return false;
            } else if (addData.personAmount === "" && addData.personAmount !== personAmountTemp.value) {
                ElNotify({
                    type: "error",
                    message: "亲，个人缴纳金额不能为空。",
                });
                return false;
            }
        }
        const data = {
            itemName: addData.itemName,
            companyPercent: addData.companyPercent,
            personPercent: addData.personPercent,
            paymentMode: addData.paymentMode,
            companyAmount: addData.companyAmount,
            personAmount: addData.personAmount,
            isNewItem: true,
        };
        isSubmit = true;
        request({
            url: `/api/InsuranceSettings?itemId=${itemId.value}`,
            method: "put",
            data,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            },
        }).then((res: any) => {
            if (res.data) {
                BackToMain()
                isSubmit = false;
                ElNotify({
                    type: "success",
                    message: "亲，保存成功。",
                });
                window.dispatchEvent(new CustomEvent("updateInsuranceSet"));
                getTableData();
            } else {
                ElNotify({
                    type: "warning",
                    message: "亲，保存失败。缴纳项目名称重复。",
                });
            }
        });
    }
}
onMounted(() => {
    getTableData();
});
const BackToMain = () => {
    currentSlot.value = "main";
    isEditting.value = false;
    isInit = false;
};
let isInit = false;
const isEditting = ref(false);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
// addData
watch(
    addData,
    () => {
        if (!isInit) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
.main-tool-left {
    text-align: left;
    padding-left: 10px;
}
.content {
    margin: 0 auto;
    // 页面调窄
    .main-top,
    .custom-table {
        width: 1100px;
        margin: 0 auto;
    }
    .main-tool-left {
        padding-left: 0;
    }
}
.edit-content {
    padding: 32px 0;
    text-align: center;
    margin: 0 auto;
    background-color: var(--white);
    border: 1px solid var(--slot-title-color);
    margin-top: 32px;
    & .line-item {
        height: 32px;
        margin-top: 16px;
        & .line-item-title {
            height: 32px;
            display: block;
            margin-right: 10px;
            width: 426px;
            float: left;
            text-align: right;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
        }
        & .line-item-field {
            height: 32px;
            float: left;
            display: block;
            line-height: 32px;
            &.project {
                :deep(.el-input__inner) {
                    cursor: default;
                }
            }
        }
    }
    & .buttons {
        margin-top: 40px;
    }
}
.table.paging-hide {
    :deep(.el-table) {
        border-bottom: none;
        flex: initial;
        .el-table__body-wrapper {
            border-bottom: 1px solid var(--table-border-color);
            tr:last-child td {
                border-bottom: none;
            }
        }
        .el-table__border-left-patch {
            width: 0;
        }
        .el-table__inner-wrapper:before {
            height: 0;
        }
    }
}
.insurance-kind {
    margin-bottom: 20px;
    color: #333;
    line-height: 22px;
    font-size: var(--h2);
    padding: 20px 40px 0;
    text-align: center;
    font-weight: 600;
}
.line-item {
    :deep(.el-select-dropdown__item) {
        text-align: left;
    }
}
body[erp] {
    .content {
        .insuranceSet-center {
            .table {
                :deep(.el-table) {
                    flex: initial;
                    height: auto;
                    border: none;
                    border-top: 1px solid var(--table-border-color);
                    &.el-table--border:before, 
                    &.el-table--border:after {
                        width: 1px;
                    }
                }
            }
        }
    }
}
</style>
