<template>
    <div class="accounting-entry">
        <div class="title">分录大全</div>
        <div class="content">
            <a class="block" target="_blank" href="https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/小企业会计准则分录大全.pdf">
                <div class="big">小</div>
                <div class="info">
                    <div class="info-title">小企业会计准则</div>
                    <div class="detail">分录大全</div>
                </div>
            </a>
            <a class="block" target="_blank" href="https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/企业会计准则分录大全.pdf">
                <div class="big">企</div>
                <div class="info">
                    <div class="info-title">企业会计准则</div>
                    <div class="detail">分录大全</div>
                </div>
            </a>
            <a class="block" target="_blank" href="https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/民非分录大全.pdf">
                <div class="big">民</div>
                <div class="info">
                    <div class="info-title">民非会计制度</div>
                    <div class="detail">分录大全</div>
                </div>
            </a>
            <a class="block" target="_blank" href="https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/农民专业合作社财务会计制度会计分录.pdf">
                <div class="big">农</div>
                <div class="info">
                    <div class="info-title">农民合作社会计制度</div>
                    <div class="detail">分录大全</div>
                </div>
            </a>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "AccountingEntry",
};
</script>

<script setup lang="ts"></script>

<style lang="less" scoped>
.accounting-entry {
    .title {
        padding: 30px 0 20px;
        text-align: center;
        color: var(--font-color);
        font-size: var(--h2);
        line-height: 25px;
        font-weight: bold;
        background-color: var(--background-color);
    }
    .content {
        width: 888px;
        background-color: white;
        position: relative;
        margin: auto;
        min-height: 448px;
        padding: 48px 51px 0 66px;
        overflow: hidden;
        text-align: left;
        .block {
            width: 250px;
            height: 42px;
            border: 1px solid #d0d0d0;
            border-radius: 2px;
            margin-right: 14px;
            margin-bottom: 16px;
            padding: 17px 0 17px 24px;
            cursor: pointer;
            display: inline-block;
            text-decoration: none;
            color: #333333;
            &:hover {
                -moz-box-shadow: 0 0 10px 0 #dadada;
                -webkit-box-shadow: 0 0 10px 0 #dadada;
                box-shadow: 0 0 10px 0 #dadada;
            }
            div {
                display: inline-block;
            }
            .big {
                background-color: #6ab1fb;
                width: 42px;
                height: 42px;
                font-size: 26px;
                color: white;
                line-height: 37px;
                vertical-align: top;
                text-align: center;
            }
            .info {
                width: 192px;
                margin-left: 8px;
                .info-title {
                    font-size: 16px;
                    height: 22px;
                    margin-top: -2px;
                    width: 192px;
                }
                .detail {
                    font-size: 13px;
                    height: 18px;
                    margin-top: 4px;
                }
            }
        }
    }
}
</style>
