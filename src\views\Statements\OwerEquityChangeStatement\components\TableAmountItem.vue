<script lang="ts" setup>
import { computed, nextTick, ref } from "vue";
import { formatMoney } from "@/util/format";
import { ElNotify } from "@/util/notify";
import { noteFormatter } from "@/views/Statements/utils";

import type { ElInput, ElPopover } from "element-plus";

import Popover from "@/components/Popover/index.vue";

const props = withDefaults(
    defineProps<{
        amount: number | string;
        formula: string;
        lineNumber: number;
        isEditing: boolean;
        showEditPen?: boolean;
        canInput?: boolean;
        isRequest?: boolean;
        showReset?: boolean;
        canEditByInput?: boolean;
        showMask?: () => boolean;
        hasTip?: boolean;
        hideTip?: () => void;
        canEditEquation?: boolean;
        isInput: boolean;
    }>(),
    {
        amount: "",
        formula: "",
        lineNumber: 0,
        showEditPen: false,
        canInput: false,
        isRequest: false,
        showReset: false,
        canEditByInput: false,
        hasTip: false,
        canEditEquation: true,
    }
);
const oraginalValue = Number(props.amount);
let visible = ref(false);
const close = () => {
    popoverRef.value?.hide();
};
const emit = defineEmits<{
    (e: "to-edit-equation"): void;
    (e: "save-amount", amount: string, resetHandle: () => void): void;
    (e: "reset", close: () => void): void;
    (e: "update:isInput", isInput: boolean): void;
}>();

const toEditEquation = () => {
    emit("to-edit-equation");
};
const isDefaultLayout = ref(true);
const isInput = computed({
    get() {
        return props.isInput;
    },
    set(value: boolean) {
        emit("update:isInput", value);
    },
});
const showInput = () => {
    if (props.showMask && !props.showMask()) return;
    if (props.isRequest) return;
    if (isInput.value) return;
    isInput.value = true;
    cellValue.value = formatMoney(props.amount).replace(/,/g, "");
    isDefaultLayout.value = false;
    nextTick().then(() => {
        cellInputRef.value?.select();
    });
};
const cellValue = ref("");
const cellInputRef = ref<InstanceType<typeof ElInput>>();
const handleAmountBlur = () => {
    if (props.isRequest) return;
    const value = cellValue.value.trim();
    if (!value) {
        ElNotify({ type: "success", message: "保存成功" });
        cellValue.value = "";
        isDefaultLayout.value = true;
        isInput.value = false;
        return;
    }
    if (isNaN(Number(value))) {
        ElNotify({ type: "warning", message: "请输入数字金额" });
        return;
    }
    emit("save-amount", value, resetEditInfo);
};
const resetEditInfo = () => {
    isDefaultLayout.value = true;
    cellValue.value = "";
    isInput.value = false;
};
const resetEquation = () => {
    emit("reset", close);
};
const handleHideTip = () => {
    props.hideTip && props.hideTip();
};
const popoverRef = ref<InstanceType<typeof ElPopover>>();
function preventArrowKeyEvent(event: any) {
    if (event.key === "Enter") {
        cellInputRef.value?.blur();
        return;
    }
    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        event.preventDefault();
    }
}
function handleWheel(e: WheelEvent) {
    e.preventDefault();
    e.stopPropagation();
}
</script>

<template>
    <template v-if="oraginalValue != 0 || props.lineNumber > 0">
        <div :class="{ 'note-popover-item': true, reverse: true }" @mouseleave="visible = false">
            <template v-if="!isDefaultLayout">
                <el-input
                    v-decimal-limit
                    type="number"
                    title=""
                    class="custom-input"
                    v-model="cellValue"
                    ref="cellInputRef"
                    @blur="handleAmountBlur"
                    @change="(val) => (cellValue = val)"
                    @wheel="handleWheel"
                    @keydown="preventArrowKeyEvent"
                />
            </template>
            <template v-else>
                <span
                    :class="{
                        'highlight-red': Number(props.amount) < 0,
                        'cell-value': true,
                        'can-input': props.canInput && !showEditPen && canEditByInput && !props.isEditing,
                    }"
                >
                    <template v-if="props.hasTip">
                        <span
                            class="link custom-input-icon"
                            v-if="props.canInput && !showEditPen && canEditByInput && !props.isEditing"
                            @click="showInput"
                        >
                            <el-icon><EditPen /></el-icon>
                        </span>
                    </template>
                    <template v-else>
                        <el-popover
                            v-if="props.canInput && !showEditPen && canEditByInput && !props.isEditing"
                            content="点击编辑支持手工修改哦~ "
                            placement="bottom"
                            trigger="hover"
                            @after-leave="handleHideTip"
                        >
                            <template #reference>
                                <span class="link custom-input-icon" @click="showInput">
                                    <el-icon><EditPen /></el-icon>
                                </span>
                            </template>
                        </el-popover>
                    </template>
                    <span class="amount">{{ String(amount).includes("%") ? amount : formatMoney(props.amount) }}</span>
                </span>
                <Popover
                    v-show="!showEditPen"
                    :title="'公式：'"
                    :content="noteFormatter(formula, 0)"
                    :placement="'right'"
                    :show-other-title="showReset"
                    :resetHandle="resetEquation"
                >
                    <template #trigger>
                        <div :class="['calc-icon', { 'icon-show': visible }]" @click="visible = true" @mouseleave="visible = false"></div>
                    </template>
                </Popover>
                <span class="link edit-equation-icon" v-show="showEditPen && canEditByInput" @click="toEditEquation">
                    <el-icon><EditPen /></el-icon>
                </span>
            </template>
        </div>
    </template>
</template>

<style lang="less" scoped>
@import (reference) "@/style/Functions.less";
.tooltip-dialog-content {
    padding: 10px;
    width: 207px;
    .set-border;
    text-align: left;
    div {
        white-space: normal;
        .set-font(var(--font-color),var(--h5),18px);
    }
    &.hide-popover {
        display: none;
    }
}
.note-popover-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &.icon-none-item {
        justify-content: flex-end;
    }
    &.reverse {
        flex-direction: row-reverse;
    }
    :deep(input[type="number"]) {
        .detail-spin-button();
    }
}
span.cell-link {
    position: relative;
    cursor: pointer;
    &:hover {
        color: #3385ff !important;
    }
}
span.can-input {
    min-height: var(--table-body-line-height);
    width: calc(100% - 18px);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;

    .custom-input-icon {
        width: 0;
        height: 18px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        position: absolute;
        left: -18px;
        top: 0;
        overflow: hidden;
    }

    span.amount {
        cursor: pointer;
        flex: 1;
    }

    &:hover {
        & + :deep(.new-popover) {
            display: none;
            pointer-events: none;
            width: 0;
        }

        .custom-input-icon {
            width: 18px;
        }
    }
}
.space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .link {
        font-size: 12px;
    }
}
body[erp] {
    .main-center {
        tr.current-row {
            td {
                .cell {
                    .icon-show.calc-icon {
                        width: 12px;
                        height: 12px;
                        background-position: 0;
                        background-size: contain;
                        background-image: url("@/assets/Statements/equal-erp.png");
                    }
                }
            }
        }
    }
}
</style>
