<template>
    <div class="content narrow-content">
        <div class="title">{{ currentSlot === "draft" ? "现金流量表报表底稿" : "现金流量表" }}</div>
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between" :class="{ uneven: tips }">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="state.pid" ref="periodRef"></PaginationPeriodPicker>
                            <div class="mr-10" id="cashFlowPicker"></div>
                            <div :class="['cash-flow-select', state.calmethod === 1 ? 'center' : '']">
                                <el-select
                                    id="calmethod"
                                    v-model="state.calmethod"
                                    :teleported="false"
                                    class="jqtransform mr-10"
                                    popper-class="calmethod"
                                    @change="calMethodChange"
                                >
                                    <el-option label="公式法" :value="1" class="option">
                                        <span>公式法</span>
                                    </el-option>
                                    <el-option label="辅助核算法" :value="2" class="option">
                                        <span>辅助核算法</span>
                                    </el-option>
                                </el-select>
                            </div>
                            <Help :accountStandard="accountStandard" />
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <el-checkbox
                                v-show="accountStandard !== 3"
                                v-model="state.isTax"
                                :label="accountStandard === 1 ? '显示上年累计金额' : '显示上年同期累计金额'"
                            ></el-checkbox>
                            <a class="button ml-10" v-if="initialDisplay" @click="handleOpenInitialDialog">期初</a>
                            <a class="button ml-10 mr-10" v-show="state.calmethod === 1" @click="handleDraft">报表底稿</a>
                            <a class="button ml-10 mr-10" v-show="state.calmethod === 2" @click="handleDetail">报表明细</a>
                            <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['cashflowstatement-canprint']">
                                <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                <li @click="handlePrint(1)">批量打印</li>
                                <li @click="handlePrint(2)">打印设置</li>
                            </Dropdown>
                            <Dropdown :btnTxt="'导出'" class="mr-10" :downlistWidth="102" v-permission="['cashflowstatement-canexport']">
                                <li @click="handleExport(0)">当前报表数据</li>
                                <li @click="handleExport(1)">批量导出</li>
                            </Dropdown>
                            <a class="button" v-if="!isHideBarcode" @click="handleShare" v-permission="['cashflowstatement-canshare']">
                                微信分享
                            </a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div v-show="tips" class="uneven-prompt">
                        <span class="highlight-red uneven-prompt-text">{{ tips }}</span>
                        <el-popover placement="right-start" :width="300" trigger="hover">
                            <div class="uneven-prompt-content">
                                <div class="row">不平原因：</div>
                                <div class="row">科目期初填写的本年累计金额与现金流量表期初填写数据不一致</div>
                            </div>
                            <template #reference>
                                <span v-show="state.calmethod === 1 && !isLoading" class="help-icon"></span>
                            </template>
                        </el-popover>
                    </div>
                    <div v-if="isErp" class="divider-line"></div>
                    <div class="main-center">
                        <CashFlowMain ref="cashFlowMain" :state="state" :isclass="isclass" @prompt="showTips"></CashFlowMain>
                    </div>
                </div>
            </template>
            <template #draft>
                <div class="slot-content align-center">
                    <CashFlowDraft
                        ref="draftRef"
                        :state="state"
                        :periodIsCheckOut="periodIsCheckOut"
                        v-model="isclass"
                        @draft-save="draftSaveHandle"
                        @draft-cancel="handleBack"
                    />
                </div>
            </template>
            <template #detail>
                <div class="slot-content align-center">
                    <CashFlowDetail @handle-back="handleBack" :pid="state.pid" />
                </div>
            </template>
        </ContentSlider>
    </div>
    <!-- 打印导出弹窗 -->
    <PrintOrExportDialog :show-dialog="showDialog" :type="dialogType" :pid="state.pid" :fromStatement="3" @close="handleDialogClose" />
    <InitialDialog ref="initialDialogRef" @loadTableData="handleBack" />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="现金流量表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "CashFlowStatement",
};
</script>
<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { useRoute } from "vue-router";
import usePrint from "@/hooks/usePrint";
import { globalPrint, globalExport, getUrlSearchParams } from "@/util/url";
import CashFlowDetail from "./components/CashFlowDetail.vue";
import CashFlowMain from "./components/CashFlowMain.vue";
import CashFlowDraft from "./components/CashFlowDraft.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import Help from "./components/Help.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { useAccountSetStore } from "@/store/modules/accountset";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import type { IChangeDto, IState } from "./types";
import { onMounted } from "vue";
import { onUnmounted } from "vue";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import Dropdown from "@/components/Dropdown/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import InitialDialog from "./components/InitialDialog.vue";
import { checkPermission } from "@/util/permission";
import { PeriodStatus } from "@/api/period";
import { share } from "@/views/Statements/utils";

const initialDialogRef = ref<InstanceType<typeof InitialDialog>>();
const periodStore = useAccountPeriodStore();
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;
const route = useRoute();
const isLoading = ref(false);
const calMethodChange = (v: number) => {
    localStorage.setItem("calmethod", String(v));
    window.dispatchEvent(new CustomEvent("cashFlowCalMethodChange", { detail: { calmethod: v } }));
    // 切换计算方法就取消勾选显示上年同期累计
    state.isTax = false;
    isLoading.value = true;
};
const isErp = ref(window.isErp);
const state = reactive<IState>({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    isTax: false,
    calmethod: Number(localStorage.getItem("calmethod") || 1),
});
const isclass = ref(localStorage.getItem("cashclassificationSwitch") === "false" ? false : true);
const slots = ["main", "draft", "detail"];
const currentSlot = ref("main");
const shareReportHost = ref("");
const isHideBarcode = useThirdPartInfoStoreHook().isHideBarcode;
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const periodIsCheckOut = computed(() => {
    return periodRef.value?.periodStatus === PeriodStatus.CheckOut;
});

const cashFlowMain = ref<InstanceType<typeof CashFlowMain>>();
const draftRef = ref<InstanceType<typeof CashFlowDraft>>();
const draftSaveHandle = () => {
    currentSlot.value = "main";
    cashFlowMain.value?.getTableList();
};

const handleBack = () => {
    currentSlot.value = "main";
    cashFlowMain.value?.getTableList();
};

const handleDraft = () => {
    currentSlot.value = "draft";
    draftRef.value?.getTableList();
};

const handleDetail = () => {
    currentSlot.value = "detail";
};
let tips = ref("");
const showTips = (v: string) => {
    tips.value = v;
    isLoading.value = false;
};

// 打印导出
const handleDialogClose = () => {
    showDialog.value = false;
};

function getDefaultParams() {
    return {
        calMethod: state.calmethod,
        isClass: isclass.value,
        isTax: state.isTax,
        pId: state.pid,
    };
}
const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "cashFlowStatement",
    `/api/CashFlowStatement/Print`,
    {},
    false,
    false,
    
);
// 导出
const handleExport = (exportType: number) => {
    if (exportType === 0) {
        globalExport(
            `/api/CashFlowStatement/Export?` + getUrlSearchParams(getDefaultParams()),
        );
    } else {
        // 批量导出
        dialogType.value = "export";
        showDialog.value = true;
    }
};

const handleShare = () => {
    request({
        url: `/api/CashFlowStatement/Share?` + getUrlSearchParams(getDefaultParams()),
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            shareReportHost.value =
                window.accountSrvHost +
                "/api/WxPay/MakeQRCode.ashx?data=" +
                window.shareReportHost +
                "/ShareReport/" +
                res.data +
                "&CurrentSystemType=1";
            share(shareReportHost.value);
        } else {
            console.log(res.msg);
        }
    });
};
let returnFlag = ref(0);
// 现金流核算状态
const cashFlowAssistAccountChange = () => {
    request({
        url: `/api/CashFlowStatement/IsCashFlowAssistAccountChange`,
        method: "post",
    }).then((res: IResponseModel<IChangeDto>) => {
        returnFlag.value = res.data.isBindOther || res.data.isChange ? 1 : 0;
    });
};
// 是否存在绑定现金流的科目
const isBindAssistAccount = () => {
    request({
        url: `/api/CashFlowStatement/HasBindingCashFlowAsub`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {});
};
isBindAssistAccount();
cashFlowAssistAccountChange();

const updateState = () => {
    state.pid = route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod());
    state.calmethod = Number(localStorage.getItem("calmethod")) || 1;
    isclass.value = localStorage.getItem("cashclassificationSwitch") === "false" ? false : true;
};
onMounted(() => {
    window.addEventListener("cashFlowCalMethodChange", updateState);
});

onUnmounted(() => {
    window.removeEventListener("cashFlowCalMethodChange", updateState);
});

const initialDisplay = computed(() => {
    const startMonth = new Date(useAccountSetStore().accountSet!.asStartDate).getMonth();
    return startMonth > 0 && checkPermission(["cashflowinitial-canview"]);
});
function handleOpenInitialDialog() {
    initialDialogRef.value?.openDialog();
}
</script>

<style lang="less" scoped>
@import "@/style/Statements/CashFlowStatement.less";
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";
.main-top {
    .main-tool-right {
        .button {
            @media (max-width: 1380px) {
                padding: 0 2px;
            }
        }
    }
}
.uneven-prompt {
    width: 1100px;
    margin: 0 auto;
    margin-bottom: 5px;
    text-align: left;
    .uneven-prompt-text {
        position: relative;
        top: 1px;
        display: inline-block;
        text-align: left;
        font-size: 14px;
    }
    .help-icon {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url(@/assets/Icons/help.png) no-repeat;
        vertical-align: top;
        margin-left: 3px;
        margin-top: 5px;
    }
}
.uneven-prompt-content {
    .row {
        line-height: 20px;
    }
}
.content .main-content .main-top.uneven {
    padding-bottom: 0 !important;
}
</style>
