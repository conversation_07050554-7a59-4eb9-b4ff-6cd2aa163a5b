export interface IPeriod {
    asid: number;
    endDate: string;
    fastatus: number;
    isActive: false;
    pid: number;
    sn: number;
    startDate: string;
    status: number;
    year: number;
}

export interface ITableItem {
    asid: number;
    expand: number;
    finiteMonthTotal: number;
    finiteYearTotal: number;
    Fold: number;
    lineID: number;
    lineNumber: number;
    monthTotal: number;
    nonFiniteMonthTotal: number;
    nonFiniteYearTotal: number;
    nonNote: string;
    note: string;
    ParentID: number;
    proName: string;
    statementId: number;
    yearTotal: number;
    lineType: number;
    children?: ITableItem[];
    fold?: number;
}
