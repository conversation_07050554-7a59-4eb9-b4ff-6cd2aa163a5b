<template>
    <div class="content">
        <div class="title">纳税统计表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <PaginationPeriodPicker v-model="pid" ref="periodRef"></PaginationPeriodPicker>
                </div>
                <div class="main-tool-right">
                    <a class="button ml-10" @click="handleExport" v-permission="['taxreport-canexport']">导出</a>
                    <a class="button ml-20" @click="handlePrint" v-permission="['taxreport-canprint']">打印</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <el-table
                    :class="isErp ? 'erp-table' : ''"
                    :data="tableData"
                    border
                    fit
                    stripe
                    :scrollbar-always-on="true"
                    :empty-text="emptyText"
                    highlight-current-row
                    class="custom-table"
                    v-loading="loading"  element-loading-text="正在加载数据..."
                    row-key="LineID"
                    @header-dragend="headerDragend"
                >
                    <el-table-column 
                        label="项目" 
                        min-width="360px" 
                        align="left" 
                        header-align="center"
                        prop="lineName"
                        :width="getColumnWidth(setModule, 'lineName')"
                    >
                        <template #default="scope">
                            <div :class="assertNameClass(scope.row)" :title="scope.row.lineName">
                                <span>{{ scope.row.lineName }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="lineNumber"
                        label="行次"
                        min-width="78px"
                        align="left"
                        header-align="left"
                        :formatter="rowNumberFormatter"
                        :width="getColumnWidth(setModule, 'lineNumber')"
                    />
                    <el-table-column label="本年累计金额" header-align="center">
                        <el-table-column 
                            label="金额" 
                            min-width="135px" 
                            align="right" 
                            header-align="right"
                            prop="initalAmount"
                            :width="getColumnWidth(setModule, 'initalAmount')"
                        >
                            <template #default="scope">
                                <TableAmountItem
                                    :amount="scope.row.initalAmount"
                                    :formula="calcFormula(scope.row, 0)"
                                    :line-number="scope.row.lineNumber"
                                ></TableAmountItem>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="initalAmountBefore" 
                            label="同比变化" 
                            min-width="135px" 
                            align="right" 
                            header-align="right"
                            :width="getColumnWidth(setModule, 'initalAmountBefore')"
                        ></el-table-column>
                    </el-table-column>
                    <el-table-column label="本期金额" header-align="center">
                        <el-table-column 
                            label="金额" 
                            min-width="135px" 
                            align="right" 
                            header-align="right"
                            prop="amount"
                            :width="getColumnWidth(setModule, 'amount')"
                        >
                            <template #default="scope">
                                <TableAmountItem
                                    :amount="scope.row.amount"
                                    :formula="calcFormula(scope.row, 1)"
                                    :line-number="scope.row.lineNumber"
                                ></TableAmountItem>
                            </template>
                        </el-table-column>
                        <el-table-column 
                            prop="amountBefore" 
                            label="同比变化" 
                            min-width="135px" 
                            align="right" 
                            header-align="right"
                            :resizable="false"
                        ></el-table-column>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "TaxReport",
};
</script>
<script setup lang="ts">
import { ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { globalExport, globalPrint } from "@/util/url";
import { useRoute } from "vue-router";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import type { ITableItem } from "./types";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "TaxReport";
const isErp = ref(window.isErp);

const periodStore = useAccountPeriodStore();

const route = useRoute();
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();

const pid = ref(periodStore.period.endPid);
const loading = ref(false);
const emptyText=ref(" ");
const tableData = ref<ITableItem[]>([]);

const handleExport = () => globalExport("/api/TaxReport/Export?pid=" + pid.value);
const handlePrint = () => globalPrint("/api/TaxReport/Print?pid=" + pid.value);
let loadingCount = 0;
const handleSearch = () => {
    if (loadingCount === 0 && route.query.pid) {
        pid.value = Number(route.query.pid);
    }
    loadingCount++;
    loading.value = true;
    // 获取纳税统计表
    request({ url: "/api/TaxReport?pid=" + pid.value })
        .then((res: IResponseModel<ITableItem[]>) => {
            if (res.state != 1000) {
                ElNotify({ type: "warning", message: res.msg || "请求失败" });
                return;
            }
            tableData.value = res.data;
            if(!tableData.value.length){
                emptyText.value="暂无数据"
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
watch(pid, handleSearch, { immediate: true });

function rowNumberFormatter(_row: any, _column: any, cellValue: any) {
    return cellValue === 0 ? "" : cellValue;
}
function calcFormula(row: ITableItem, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.note?.split("|")[0] ?? "";
            break;
        case 1:
            formula = row.note?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}
function assertNameClass(row: ITableItem) {
    return row.lineName.startsWith("（") ? "level1" : "level2";
}
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
</style>
