<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <SearchInfoContainer ref="containerRef">
                <template v-slot:title>{{ currentPeriodInfo }}</template>
                <div class="line-item first-item input">
                    <div class="line-item-title">进销存日期：</div>
                    <div class="line-item-field">
                        <el-date-picker
                            v-model="searchInfo.startDate"
                            type="date"
                            style="width: 132px"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateStart"
                        />
                        <span class="ml-10 mr-10 float-l">至</span>
                        <el-date-picker
                            v-model="searchInfo.endDate"
                            type="date"
                            style="width: 132px"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateStart"
                        />
                    </div>
                </div>
                <div v-if="!isErp">
                    <div class="line-item input">
                        <div class="line-item-title">凭证日期：</div>
                        <div class="line-item-field">
                            <DatePicker
                                v-model:startPid="searchInfo.startVDate"
                                v-model:endPid="searchInfo.endVDate"
                                :disabled-date-start="disabledStartVDate"
                                :disabled-date-end="disabledEndVDate"
                            />
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">凭证字号：</div>
                        <div class="line-item-field">
                            <div class="vgid-line">
                                <el-select v-model="searchInfo.vgId" :teleported="false" style="width: 90px" @change="handleVgIdChange">
                                    <el-option :value="0" label="请选择">请选择</el-option>
                                    <el-option :value="1" label="全部">全部</el-option>
                                    <el-option v-for="item in voucherGroup" :value="item.id" :key="item.id" :label="item.name"></el-option>
                                </el-select>
                                <el-input
                                    clearable
                                    type="text"
                                    class="ml-10"
                                    v-model="searchInfo.startVNum"
                                    :disabled="!searchInfo.vgId"
                                    @input="startVNumLimit"
                                ></el-input>
                                <span style="padding: 0 8px; line-height: 30px">至</span>
                                <el-input
                                    clearable
                                    type="text"
                                    v-model="searchInfo.endVNum"
                                    :disabled="!searchInfo.vgId"
                                    @input="endVNumLimit"
                                ></el-input>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="line-item input long-input">
                    <div class="line-item-title">制单人：</div>
                    <div class="line-item-field">
                        <el-select 
                            v-model="searchInfo.preparedBy" 
                            :teleported="false" 
                            style="width: 298px"
                            :filterable="true"
                            :filter-method="preparedFilterMethod"
                        >
                            <el-option 
                                :label="item.label" 
                                :value="item.value" 
                                v-for="item in showPreparedByList" 
                                :key="item.value"
                            > </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="line-item input long-input">
                    <div class="line-item-title">关联凭证状态：</div>
                    <div class="line-item-field">
                        <el-select v-model="searchInfo.relationVoucherState" :teleported="false" style="width: 298px">
                            <el-option value="0" label="全部"></el-option>
                            <el-option value="1010" label="关联"></el-option>
                            <el-option value="1020" label="未关联"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="line-item input long-input">
                    <div class="line-item-title">单据编号：</div>
                    <div class="line-item-field">
                        <el-input v-model="searchInfo.billNo" style="width: 298px" clearable></el-input>
                    </div>
                </div>
                <div class="line-item input long-input">
                    <div class="line-item-title">客户名称：</div>
                    <div class="line-item-field">
                        <el-input v-model="searchInfo.customerName" style="width: 298px" clearable></el-input>
                    </div>
                </div>
                <div class="line-item input long-input">
                    <div class="line-item-title">供应商名称：</div>
                    <div class="line-item-field">
                        <el-input v-model="searchInfo.vendorName" style="width: 298px" clearable></el-input>
                    </div>
                </div>
                <div class="line-item single">
                    <div class="line-item-title">
                        <el-checkbox v-model="searchInfo.showZeroBill" label="显示金额为0单据"></el-checkbox>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="handleSearch">确定</a>
                    <a class="button" @click="handleClose">取消</a>
                    <a class="button" @click="handleReset">重置</a>
                </div>
            </SearchInfoContainer>
        </div>
        <div class="main-tool-right">
            <a v-permission="['scmvoucher-candelete']" class="float-r button large-1 mr-10" @click="batchDeleteVoucher">批量删除凭证</a>

            <!-- <a v-permission="['scmvoucher-cancreatevoucher']" class="float-r button solid-button mr-10" @click="openVoucherFromBill">生成凭证</a>             -->
            <Dropdown :btnTxt="'导出'" class="mr-10" :downlistWidth="96" v-permission="['scmvoucher-canexport']">
                <li @click="handleExport(0)">当前查询数据</li>
                <li @click="handleExport(1)">所有单据数据</li>
            </Dropdown>
            <div
                v-permission="['scmvoucher-cancreatevoucher']"
                class="item ml-10"
                style="position: relative"
                @mouseleave="() => (receiptTemplateDownList = false)"
            >
                <a class="button solid-button" style="float: left" @click="genVoucher">生成凭证</a>
                <a class="button solid-button down-click" style="float: left" @mouseenter="() => (receiptTemplateDownList = true)"></a>
                <div style="width: 106px; top: 28px" class="downlist" v-show="receiptTemplateDownList">
                    <li @click="showJournalSettings">生成凭证设置</li>
                </div>
            </div>
            <RefreshButton></RefreshButton>
        </div>
    </div>
    <div :class="['main-center', { isErpCenter: isErp }]">
        <div class="main-left">
            <Table
                :class="pageSelections.length > 0 ? '' : 'empty-selection-table'"
                ref="receiptTableRef"
                rowKey="customBillId"
                :scrollbarShow="true"
                :data="tableData"
                :columns="columns"
                :fit="true"
                :loading="loading"
                :pageIsShow="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :current-page="paginationData.currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @select="singleSelectionCheck"
                @select-all="checkClearMoreSelections"
                @selection-change="handleSelectionChange"
                @row-click="selectToggle"
                @refresh="handleRerefresh"
                :tableName="setModule"
            >
                <template #billNo>
                    <el-table-column
                        label="单据编号"
                        prop="billNo"
                        :width="getColumnWidth(setModule, 'billNo', 155)"
                        align="left"
                        header-align="left"
                    >
                        <template #default="scope">
                            <template v-if="scope.row.billType !== 0 && scope.row.billId !== 0">
                                <a
                                    class="link"
                                    @click.stop="
                                        globalWindowOpen(
                                            `${getScmHost(props.scmProductType)}/#/from_acc?asid=${props.scmAsid}&bill_type=${
                                                scope.row.billType
                                            }&id=${scope.row.billId}&asname=${encodeURIComponent(props.scmAsName.replace(/'/g, '\\\''))}${
                                                props.cstId ? `&serviceId=${props.cstId}` : ''
                                            }`
                                        )
                                    "
                                    >{{ scope.row.billNo }}</a
                                >
                            </template>
                            <template v-else> </template>
                        </template>
                    </el-table-column>
                </template>
                <template #voucherInfo>
                    <el-table-column label="凭证字号" align="left" header-align="left" :resizable="false">
                        <template #default="scope">
                            <template
                                v-if="
                                    !!scope.row.voucherInfo && !useAccountSetStoreHook().permissions.includes('scmvoucher-cancreatevoucher')
                                "
                            >
                                {{ scope.row.voucherInfo }}
                            </template>
                            <template v-else>
                                <a
                                    class="link"
                                    @click.stop="
                                        globalWindowOpenPage(
                                            `/Voucher/VoucherPage?pid=${scope.row.pid}&vid=${scope.row.vid}&fcode=scmvoucher-cancreatevoucher`,
                                            '查看凭证'
                                        )
                                    "
                                    >{{ scope.row.voucherInfo }}</a
                                >
                            </template>
                        </template>
                    </el-table-column>
                </template>
                <template #pageOther v-if="!isErp && pageSelections.length > 0">
                    <span>
                        已选 {{ pageSelections.length }} 条
                        <a class="link pl-20" @click="handleOpenPageSelectDialog">查看</a>
                        <a class="link pl-20" @click="receiptTableRef?.clearSelection()">取消全部</a>
                    </span>
                </template>
            </Table>
        </div>
        <div class="main-right drag-right">
            <div class="assit-col-line"></div>
            <div class="drag-line" v-tree-drag="flag" v-show="flag.closeFlag"></div>
            <div class="tree-click">
                <span :class="['tree-img', {'disabled': !flag.closeFlag}]" @click="treeClick(0, flag)"></span>
                <span :class="['tree-img expand', {'disabled': !flag.expandFlag}]" @click="treeClick(1, flag)"></span>
            </div>
            <div class="tree-line"></div>
            <div class="tree-content">
                <div class="main-right-title">快速切换</div>
                <div class="tree-list">
                    <el-scrollbar :always="true" :minSize="80">
                        <el-tree
                            :data="treeData"
                            node-key="id"
                            ref="treeRef"
                            default-expand-all
                            :props="{ label: 'text', children: 'children' }"
                            :expand-on-click-node="false"
                            :highlight-current="true"
                            @node-click="handleNodeClick"
                        >
                            <template #default="{ data }">
                                <span class="custom-tree-node">
                                    <span :class="data.children ? 'tree-icon tree-folder tree-folder-open' : 'tree-icon tree-file'"></span>
                                    <span class="tree-title">{{ data.text }}</span>
                                </span>
                            </template>
                        </el-tree>
                    </el-scrollbar>
                </div>
            </div>
        </div>
    </div>
    <DiffPageSelected
        ref="diffPageSelectedRef"
        rowKey="customBillId"
        :selectedData="pageSelections"
        :columns="dialogColumns"
        @row-click="handlePageRowClick"
        @select="handlePageSelect"
        @select-all="handlePageSelectAll"
    />
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import { usePagination } from "@/hooks/usePagination";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { getUrlSearchParams, globalWindowOpen, globalWindowOpenPage, tryClearCustomUrlParams } from "@/util/url";
import { showExpiredDialog } from "@/util/showExpiredDialog";
import { toDecimal3, formatNumberStr } from "@/util/format";
import { getScmHost } from "@/util/scm";
import { firstDayOfMonth, lastDayOfMonth, findTreeIdById } from "../utils";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { type IResponseModel, request } from "@/util/service";
import { ref, reactive, watch, nextTick, onMounted, onUnmounted, computed, watchEffect } from "vue";
import { dayjs } from "element-plus";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { globalExport } from "@/util/url";
import { getGlobalLodash } from "@/util/lodash";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import DatePicker from "@/components/DatePicker/index.vue";
import { useRoute } from "vue-router";
import DiffPageSelected from "@/components/Dialog/DiffPageSelected/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useDebugCurrentPage } from "@/hooks/useDebugCurrentPage";
import { cloneDeep } from "lodash";
import { treeClick } from "@/views/AccountBooks/SubsidiaryLedger/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "ScmVoucherReceipt";
const props = defineProps({
    scmAsid: {
        type: Number,
        default: 0,
    },
    scmAsName: {
        type: String,
        default: "",
    },
    scmProductType: {
        type: Number,
        default: 0,
    },
    cstId: {
        type: Number,
        default: 0,
    },
});
const emit = defineEmits(["openVoucherFromBill", "showJournalSettings", "setBillRootType"]);

const isErp = ref(window.isErp);
const _ = getGlobalLodash();

const diffPageSelectedRef = ref<InstanceType<typeof DiffPageSelected>>();
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = voucherGroupStore.voucherGroupList;
const route = useRoute();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const treeRef = ref();
const currentPeriodInfo = ref("");
const loading = ref(false);
const searchInfo = reactive({
    startDate: firstDayOfMonth(),
    endDate: lastDayOfMonth(),
    startVDate: "",
    endVDate: "",
    vgId: 0,
    startVNum: "",
    endVNum: "",
    preparedBy: "0",
    relationVoucherState: "0",
    billNo: "",
    customerName: "",
    vendorName: "",
    showZeroBill: true,
});
const receiptTemplateDownList = ref(false);

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const debugCanChangeCurrentPage = useDebugCurrentPage(paginationData, getScmBillList);
const columns = ref<Array<IColumnProps>>([]);
const dialogColumns = ref<Array<IColumnProps>>([]);
function handlePageRowClick(row: ICustomBillItem) {
    receiptTableRef.value?.getTable()?.toggleRowSelection(row, false);
}
function handlePageSelect(select: Array<ICustomBillItem>) {
    const row = selectedRows.value.find((row) => !select.includes(row));
    if (!row) return;
    const selection = receiptTableRef
        .value!.getTable()
        .store.states.selection.value.find((item: ICustomBillItem) => item.customBillId === row.customBillId);
    if (!selection) return;
    receiptTableRef.value!.getTable().toggleRowSelection(selection, false);
}
function handlePageSelectAll() {
    pageSelections.value.forEach((row) => {
        const selection = receiptTableRef
            .value!.getTable()
            .store.states.selection.value.find((item: ICustomBillItem) => item.customBillId === row.customBillId);
        if (!selection) return;
        receiptTableRef.value!.getTable().toggleRowSelection(selection, false);
    });
}
function handleOpenPageSelectDialog() {
    dialogColumns.value = cloneDeep(columns.value.slice(0, columns.value.length - 1));
    const newColumn: IColumnProps = {
        label: "单据编号",
        prop: "billNo",
        align: "left",
        headerAlign: "left",
        minWidth: 155,
        width: getColumnWidth(setModule, "billNo", 155),
    };
    const index = dialogColumns.value.findIndex((item) => item.slot === "billNo");
    if (index !== -1) {
        dialogColumns.value.splice(index, 1, newColumn);
    }
    dialogColumns.value[dialogColumns.value.length - 1].resizable = false;
    diffPageSelectedRef.value?.handleOpen();
}

function setColumns(typeid: string) {
    if (typeid == "1070" || typeid == "1140") {
        columns.value = [
            { slot: "selection", width: 30, headerAlign: "center", align: "center", reserveSelection: true },
            {
                label: "日期",
                prop: "billDateText",
                align: "left",
                headerAlign: "left",
                minWidth: 125,
                width: getColumnWidth(setModule, "billDateText"),
            },
            { slot: "billNo" },
            {
                label: "单据类型",
                prop: "billTypeText",
                align: "left",
                headerAlign: "left",
                minWidth: 111,
                width: getColumnWidth(setModule, "billTypeText"),
            },
            {
                label: "单据金额",
                prop: "totalAmount",
                align: "right",
                headerAlign: "right",
                minWidth: 152,
                formatter: function (value) {
                    return formatNumberStr(toDecimal3(value.totalAmount)) as string;
                },
                width: getColumnWidth(setModule, "totalAmount"),
            },
            {
                label: "制单人",
                prop: "preparedBy",
                align: "left",
                headerAlign: "left",
                minWidth: 101,
                width: getColumnWidth(setModule, "preparedBy"),
            },
            { slot: "voucherInfo" },
        ];
    } else {
        columns.value = [
            { slot: "selection", width: 30, headerAlign: "center", align: "center", reserveSelection: true },
            {
                label: "日期",
                prop: "billDateText",
                align: "left",
                headerAlign: "left",
                minWidth: 99,
                width: getColumnWidth(setModule, "billDateText"),
            },
            { slot: "billNo" },
            {
                label: "单据类型",
                prop: "billTypeText",
                align: "left",
                headerAlign: "left",
                minWidth: 88,
                width: getColumnWidth(setModule, "billTypeText"),
            },
            {
                label: "单据金额",
                prop: "totalAmount",
                align: "right",
                headerAlign: "right",
                minWidth: 115,
                formatter: function (value) {
                    return formatNumberStr(toDecimal3(value.totalAmount)) as string;
                },
                width: getColumnWidth(setModule, "totalAmount"),
            },
            {
                label: "制单人",
                prop: "preparedBy",
                align: "left",
                headerAlign: "left",
                minWidth: 74,
                width: getColumnWidth(setModule, "preparedBy"),
            },
            { slot: "voucherInfo" },
        ];
        if (typeid === "1110" || typeid === "1120") {
            columns.value.splice(
                4,
                0,
                {
                    label: "组合件",
                    prop: "assemblyName",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, "assemblyName"),
                },
                {
                    label: "数量",
                    prop: "quantity",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 60,
                    width: getColumnWidth(setModule, "quantity"),
                }
            );
        } else {
            let nameTitle = "客户/供应商";
            if (typeid === "1020" || typeid === "1030" || typeid === "1060" || typeid === "1080" || typeid === "1090") {
                nameTitle = "客户";
            } else if (typeid === "1010" || typeid === "1040" || typeid === "1050") {
                nameTitle = "供应商";
            } else if (typeid === "1100") {
                nameTitle = "往来单位";
            }

            columns.value.splice(4, 0, {
                label: nameTitle,
                prop: "name",
                align: "left",
                headerAlign: "left",
                minWidth: 148,
                width: getColumnWidth(setModule, "name"),
            });
            //暂时隐藏结算金额列
        }
    }
}
const accountSet = useAccountSetStore().accountSet;
const accountStartDate = accountSet?.asStartDate ?? "";
const trialStatusStore = useTrialStatusStore();

function disabledDateStart(time: Date) {
    const asStartDate = dayjs(accountStartDate).valueOf();
    return time.getTime() < asStartDate;
}
function disabledStartVDate(time: Date) {
    const asStartDate = dayjs(accountStartDate).valueOf();
    const endVDate = dayjs(searchInfo.endVDate).valueOf();
    return time.getTime() < asStartDate || time.getTime() > endVDate;
}
function disabledEndVDate(time: Date) {
    let startVDate = dayjs(searchInfo.startVDate).valueOf();
    return time.getTime() < startVDate;
}
const startVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.startVNum = val;
};
const endVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.endVNum = val;
};
const handleVgIdChange = (val: number) => {
    if (val === 0) {
        searchInfo.startVNum = "";
        searchInfo.endVNum = "";
    }
};
const treeData = ref<any[]>([]);
const billRootType = ref("1010");
const billType = ref("1010");
interface IBillNode {
    id: string;
    text: string;
    children?: IBillNode[];
}
let init = true;
function getBillTypeTree() {
    request({
        url: `/api/ScmBill/GetBillType?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            for (var key in res.data.rootTypes) {
                let billNode = {} as IBillNode;
                billNode.id = key;
                billNode.text = res.data.rootTypes[key];
                var childrenTypes = res.data.nestTypes[key];
                if (Object.keys(childrenTypes).length > 1) {
                    var children = [];
                    for (var childrenKey in childrenTypes) {
                        if (childrenKey != key) {
                            var childrenNode = {} as IBillNode;
                            childrenNode.id = childrenKey;
                            childrenNode.text = childrenTypes[childrenKey];
                            children.push(childrenNode);
                        }
                    }
                    billNode.children = children;
                }
                treeData.value.push(billNode);
            }
            if (route.query.from === "voucherList" && init) {
                const { vnum, vdate, billType, vgname, searchStartDate, searchEndDate } = route.query;
                searchInfo.startDate = searchStartDate as string;
                searchInfo.endDate = searchEndDate as string;
                if (vnum && vgname) {
                    const targetVgid = voucherGroup.find((item) => item.name === vgname)?.id;
                    searchInfo.vgId = targetVgid ?? 1;
                    searchInfo.startVNum = vnum as string;
                    searchInfo.endVNum = vnum as string;
                }
                if (vdate) {
                    searchInfo.startVDate = vdate as string;
                    searchInfo.endVDate = vdate as string;
                }
                if (billType) {
                    const rightId = findTreeIdById(treeData.value, billType as string);
                    const treeId = rightId !== "" ? rightId : treeData.value[0].id;
                    nextTick(() => {
                        treeRef.value?.setCurrentKey(treeId);
                        handleNodeClick(treeRef.value?.getCurrentNode());
                        tryClearCustomUrlParams(route);
                    });
                }
            } else {
                nextTick().then(() => {
                    treeRef.value?.setCurrentKey(billType.value);
                    handleNodeClick(treeRef.value?.getCurrentNode());
                });
            }
            currentPeriodInfo.value = searchInfo.startDate + " 至 " + searchInfo.endDate;
            init = false;
        }
    });
}

let back: Array<any> = [];
function handleNodeClick(data: any) {
    if (loading.value) {
        treeRef.value?.setCurrentKey(null);
        back = treeData.value.slice();
        treeData.value.length = 0;
        nextTick().then(() => {
            treeData.value = back.slice();
            nextTick().then(() => {
                treeRef.value?.setCurrentKey(billType.value);
                back.length = 0;
            });
        });
        return;
    }
    const index = treeData.value.findIndex((item: any) => item.id === data.id);
    if (index === -1) {
        const childIndex = treeData.value.findIndex((item: any) => {
            if (item.children) {
                return item.children.includes(data);
            }
        });
        billRootType.value = treeData.value[childIndex].id;
        // billType.value = treeData.value[childIndex].id;
    } else {
        billRootType.value = treeData.value[index].id;
        // billType.value = treeData.value[index].id;
    }
    emit("setBillRootType", billRootType.value);
    billType.value = data.id;
    tryClearSelections();
    setColumns(billRootType.value);
    debugCanChangeCurrentPage();
    paginationData.currentPage = 1;
    getScmBillList();
}

const preparedByList = ref<any[]>([]);
function getScmPreparedBy() {
    request({
        url: `/api/ScmPreparedBy/Pairs?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
    }).then((res: any) => {
        preparedByList.value = Object.entries(res.data).map(([key, value]) => ({  
            value: key,  
            label: value  
        }));
    });
}

interface IBillItem {
    name: string;
    payeeType: number;
    settlementAmount: number;
    billId: number;
    billDate: Date;
    billDateText: Date;
    billNo: string;
    billRootType: number;
    billType: number;
    billTypeText: string;
    totalAmount: number;
    preparedBy: string;
    pid: number;
    vid: number;
    invoiceId: number;
    voucherInfo: string;
    note: string;
    isCheckOut: boolean;
    isApportioned: boolean;
    vtId: number;
    vtName: string;
    vstatus: number;
}
interface ICustomBillItem extends IBillItem {
    customBillId: string;
}

const tableData = ref<Array<ICustomBillItem>>([]);
function getScmBillList() {
    loading.value = true;
    const params = {
        ...searchInfo,
        billType: billType.value,
        billRootType: billRootType.value,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    request({
        url: `/api/ScmBill/PagingList?` + getUrlSearchParams(params),
    })
        .then((res: IResponseModel<{ data: Array<IBillItem>; count: number }>) => {
            if (res.state === 1000) {
                tableData.value = res.data.data.map((item) => ({ ...item, customBillId: item.billType + "_" + item.billId }));
                paginationData.total = res.data.count;
            } else {
                ElNotify({
                    type: "error",
                    message: res.msg || "出现错误，请稍候刷新页面重试",
                });
            }
        })
        .finally(() => {
            loading.value = false;
        });
}

function handleSearch() {
    if (searchInfo.startDate == "" || searchInfo.startDate == null) {
        ElNotify({
            type: "warning",
            message: "进销存起始日期不能为空",
        });
        return false;
    }
    if (searchInfo.endDate == "" || searchInfo.endDate == null) {
        ElNotify({
            type: "warning",
            message: "进销存结束日期不能为空",
        });
        return false;
    }
    if (searchInfo.startDate > searchInfo.endDate) {
        ElNotify({
            type: "warning",
            message: "进销存起始日期不能大于结束日期",
        });
        return false;
    }
    if (billType.value == null) {
        ElNotify({
            type: "warning",
            message: "请选择单据类型",
        });
        return false;
    }
    currentPeriodInfo.value = searchInfo.startDate + " 至 " + searchInfo.endDate;

    getScmBillList();
    containerRef.value?.handleClose();
}

function handleClose() {
    containerRef.value?.handleClose();
}

function handleReset() {
    searchInfo.startDate = firstDayOfMonth();
    searchInfo.endDate = lastDayOfMonth();
    searchInfo.preparedBy = "0";
    searchInfo.relationVoucherState = "0";
    searchInfo.billNo = "";
    searchInfo.customerName = "";
    searchInfo.vendorName = "";
    searchInfo.showZeroBill = false;
    searchInfo.startVDate = "";
    searchInfo.endVDate = "";
    searchInfo.vgId = 0;
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
}

const voucherInfos = ref<string[]>([]);
const voucherIds = ref<number[]>([]);
const voucherPids = ref<number[]>([]);
const billIds = ref<number[]>([]);
const selectedRows = ref<ICustomBillItem[]>([]);
const maxSelection = 600;
function handleSelectionChange(rows: ICustomBillItem[]) {
    voucherInfos.value = [];
    voucherIds.value = [];
    voucherPids.value = [];
    billIds.value = [];
    selectedRows.value = rows;
    for (let i in selectedRows.value) {
        if (rows[i].pid !== 0 && rows[i].vid !== 0) {
            var key = rows[i].pid + "," + rows[i].vid;
            if (voucherInfos.value.indexOf(key) === -1) {
                voucherIds.value.push(rows[i].vid);
                voucherPids.value.push(rows[i].pid);
                voucherInfos.value.push(key);
            }
        }
        if (!billIds.value.includes(rows[i].billId)) {
            billIds.value.push(rows[i].billId);
        }
    }
}
// select-all 先于 selection-change 触发
// select 晚于 selection-change 触发
function checkClearMoreSelections() {
    if (pageSelections.value.length <= maxSelection) return;
    ElNotify({ type: "warning", message: `最多支持${maxSelection}条哦！` });
    const totalIndex = selectedRows.value.findIndex((item) => item.customBillId === pageSelections.value[maxSelection - 1].customBillId);
    const moreData = selectedRows.value.slice(totalIndex + 1);
    moreData.forEach((item) => {
        receiptTableRef.value?.getTable()?.toggleRowSelection(item, false);
    });
    selectedRows.value = selectedRows.value.slice(0, totalIndex + 1);
}
function singleSelectionCheck(val: Array<ICustomBillItem>) {
    if (val.length <= selectedRows.value.length) return;
    const row = val.find((item) => !selectedRows.value.find((row) => row.customBillId === item.customBillId));
    if (!row) return;
    if (pageSelections.value.length <= maxSelection - 1) return;
    ElNotify({ type: "warning", message: `最多支持${maxSelection}条哦！` });
    const cacheRow = receiptTableRef.value
        ?.getTable()
        .store.states.selection.value.find((item: ICustomBillItem) => item.customBillId === row.customBillId);
    if (!cacheRow) return;
    nextTick().then(() => {
        receiptTableRef.value?.getTable().toggleRowSelection(cacheRow, false);
    });
}
function pageSelectionFilter(row: ICustomBillItem) {
    return (
        row.billType === ~~billType.value ||
        (~~billType.value % 10 === 0 && row.billType.toString().slice(0, 3) === billType.value.toString().slice(0, 3))
    );
}
function getPageSelections() {
    return selectedRows.value.filter(pageSelectionFilter);
}
function tryClearSelections() {
    const currentNode = treeRef.value?.getCurrentNode();
    if (currentNode?.children && currentNode?.children?.length > 0 && pageSelections.value.length > maxSelection) {
        ElNotify({ type: "warning", message: `超过${maxSelection}条，为确保流程顺畅已自动清空所有记录，请重新选择哦!` });
        const selections = receiptTableRef.value?.getTable().store.states.selection.value.filter(pageSelectionFilter);
        selections.forEach((item: ICustomBillItem) => {
            receiptTableRef.value?.getTable()?.toggleRowSelection(item, false);
        });
    }
}
const pageSelections = computed(() => getPageSelections());

const receiptTableRef = ref();
function selectToggle(row: ICustomBillItem) {
    let selected =
        receiptTableRef
            .value!.getTable()
            .store.states.selection.value.findIndex((item: ICustomBillItem) => item.customBillId === row.customBillId) >= 0;
    receiptTableRef.value?.getTable().toggleRowSelection(row, !selected);
    checkClearMoreSelections();
}

function batchDeleteVoucher() {
    if (voucherIds.value.length == 0) {
        ElNotify({
            type: "warning",
            message: "请选择凭证数据后删除",
        });
        return false;
    }
    ElConfirm("亲，确认要删除凭证吗？").then((r: Boolean) => {
        if (r) {
            // const data = {
            //     vid: voucherIds.value,
            //     pid: voucherPids.value,
            // };
            const data = voucherIds.value.reduce((prev: any, item: any, index: number) => {
                prev.push({ pid: voucherPids.value[index], vid: item });
                return prev;
            }, []);
            request({
                url: `/api/Voucher/BatchForScm`,
                method: "delete",
                data,
            }).then((r: any) => {
                if (r.state != 1000) return;
                const result = r.data;
                if (result != undefined) {
                    if (result.IsOvertime === true) {
                        ElNotify({ type: "warning", message: "数据太多啦，服务器太忙了，请10分钟后分批删除哦~" });
                    } else {
                        ElConfirm(
                            "成功：" +
                                result.batchOperationResult.success +
                                "，跳过：" +
                                result.batchOperationResult.jump +
                                "（已经审核、已经结账的凭证，将会被跳过！）",
                            true
                        );
                        getScmBillList();
                        receiptTableRef.value?.clearSelection();
                    }
                } else {
                    ElNotify({ type: "warning", message: "亲，删除失败啦！请联系侧边栏客服！" });
                }
            });
        }
    });
}

const genVoucher = _.debounce(openVoucherFromBill, 500);

let genFlag = true;
function openVoucherFromBill() {
    if (!genFlag) return;
    genFlag = false;
    let hasVoucherBillCount = 0;
    let hasUnCheckOutBill = false;
    billIds.value = [];
    for (let i in pageSelections.value) {
        if (pageSelections.value[i].billId !== 0) {
            if (
                billRootType.value === "1060" ||
                billRootType.value === "1080" ||
                billRootType.value === "1110" ||
                billRootType.value === "1120"
            ) {
                if (pageSelections.value[i].isCheckOut === false) {
                    hasUnCheckOutBill = true;
                    break;
                }
            }
            if (pageSelections.value[i].pid !== 0 && pageSelections.value[i].vid !== 0) {
                hasVoucherBillCount++;
            } else {
                billIds.value.push(pageSelections.value[i].billId);
            }
        }
    }
    if (hasUnCheckOutBill) {
        ElConfirm("选择的进销存单据还有未结账的，不支持生成凭证，是否立即前往进销存进行结账处理？").then((r: boolean) => {
            if (r) {
                globalWindowOpen(
                    getScmHost(props.scmProductType) +
                        "/#/from_acc?checkout&asid=" +
                        props.scmAsid +
                        "&asname=" +
                        encodeURIComponent(props.scmAsName.replace(/'/g, "\\'")) +
                        (props.cstId ? `&serviceId=${props.cstId}` : "")
                );
            }
        });
    } else {
        if (hasVoucherBillCount > 0) {
            ElConfirm("亲，已经生成凭证的单据将会被跳过，是否继续生成凭证？").then((r: boolean) => {
                if (r) {
                    if (billIds.value.length === 0) {
                        ElConfirm("成功：0，跳过：" + hasVoucherBillCount + "（已经生成凭证的单据，将会被跳过！）");
                    } else {
                        emit("openVoucherFromBill", { billIds: billIds.value, rootBillType: billRootType.value });
                    }
                } else {
                    return false;
                }
            });
        } else {
            emit("openVoucherFromBill", { billIds: billIds.value, rootBillType: billRootType.value });
        }
    }
    genFlag = true;
}

const showJournalSettings = () => {
    emit("showJournalSettings");
};
// 导出模块
const handleExport = (exportType: number) => {
    if (!searchInfo.startDate) {
        ElNotify({
            type: "warning",
            message: "进销存起始日期不能为空",
        });
        return false;
    }
    if (!searchInfo.endDate) {
        ElNotify({
            type: "warning",
            message: "进销存结束日期不能为空",
        });
        return false;
    }
    if (searchInfo.startDate > searchInfo.endDate) {
        ElNotify({
            type: "warning",
            message: "进销存起始日期不能大于结束日期",
        });
        return false;
    }
    const params = {
        ...searchInfo,
        billType: billType.value,
        billRootType: billRootType.value,
        pageSize: paginationData.pageSize,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
        ifpage: exportType, // 0 当前查询导出   1 导出全部
    };
    globalExport(`/api/ScmBill/export?${getUrlSearchParams(params)}`);
};
watch([() => searchInfo.startVDate, () => searchInfo.endVDate], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startVDate = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endVDate = "";
    }
});
watch([() => searchInfo.startDate, () => searchInfo.endDate], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startDate = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endDate = "";
    }
});

watch([() => props.scmAsName], () => {
    getBillTypeTree();
    getScmPreparedBy();
});
function clearSelection() {
    receiptTableRef.value?.clearSelection();
}
defineExpose({
    getScmBillList,
    clearSelection,
});
onMounted(() => {
    window.addEventListener("ScmVoucherVoucherChange", getScmBillList);
});
onUnmounted(() => {
    window.removeEventListener("ScmVoucherVoucherChange", getScmBillList);
});
// 展开和收起右侧树标志
const flag = reactive({
    expandFlag: true,
    closeFlag: true
});
const showPreparedByList = ref<Array<any>>([]);
watchEffect(() => {
    showPreparedByList.value = JSON.parse(JSON.stringify(preparedByList.value));
});
function preparedFilterMethod(value: string) {
    showPreparedByList.value = commonFilterMethod(value, preparedByList.value, 'label');
}
</script>

<style scoped lang="less">
@import "@/style/RightTreeExpand.less";

.content {
    .main-content {
        .main-center {
            display: flex;
            justify-content: space-between;
            flex-wrap: nowrap;

            .main-left {
                margin-right: 15px;
                height: 100%;
                min-width: 0;
                flex: 1;
                :deep(.empty-selection-table) {
                    .el-table__header-wrapper {
                        .is-indeterminate {
                            .el-checkbox__inner {
                                background-color: var(--el-checkbox-bg-color);
                                border: var(--el-checkbox-input-border);
                            }
                        }
                    }
                }
            }

            .main-right {
                position: relative;
                margin-left: 10px;
                font-size: 0px;
                // height: 100%;
                .main-right-title {
                    color: var(--font-color);
                    font-size: var(--h5);
                    font-weight: bold;
                    line-height: 36px;
                    padding-left: 10px;
                    text-align: left;
                }
                & .tree-line {
                    width: 1px;
                    margin-left: -1px;
                    vertical-align: middle;
                    display: inline-block;
                }

                & .tree-content {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    .tree-list {
                        flex: 1;
                        min-height: 0;
                    }

                    & :deep(.el-tree-node__content) {
                        height: 21px;
                    }

                    & .tree-icon {
                        display: inline-block;
                        vertical-align: top;
                        height: 21px;
                        width: 21px;
                        background-repeat: no-repeat;
                        background-position-y: center;
                        background-position-x: center;
                    }

                    & .is-expanded {
                        & .tree-icon.tree-folder.tree-folder-open {
                            background-image: url(@/assets/Icons/folder-open.png);
                        }
                    }

                    & .tree-icon.tree-folder.tree-folder-open {
                        background-image: url(@/assets/Icons/folder.png);
                    }

                    & .tree-icon.tree-file {
                        background-image: url(@/assets/Icons/file.png);
                        background-size: 16px 18px;
                    }

                    & .tree-title {
                        color: var(--font-color);
                        font-size: var(--h5);
                        line-height: 21px;
                        display: inline-block;
                    }
                }
            }
            &.hidden-tree {
                & .main-right {
                    & .tree-content {
                        display: none;
                    }
                }
            }
            .tree-list {
                :deep(.el-tree-node__content) {
                    padding-right: 10px;
                }
                :deep(.el-tree) {
                    display: inline-block;
                    min-width: 100%;
                }
            }
        }
    }
    .main-right {
        border: 1px solid var(--border-color);
        width: 233px;
    }
}

.line-item-title {
    width: 105px !important;
    padding-left: 14px !important;
    text-align: right !important;
}

.downlist {
    .line-item-field {
        :deep(.el-date-editor) {
            & .el-input__prefix {
                position: absolute;
                right: 0;
            }

            & .el-input__suffix-inner {
                position: absolute;
                right: 30px;
                top: 9px;
            }
        }
        .picker {
            :deep(.el-date-editor) {
                & .el-input__prefix {
                    .el-input__prefix-inner {
                        & > :last-child {
                            margin-right: 8px;
                        }
                    }
                }
                & .el-input__suffix-inner {
                    right: 10px;
                }
            }
        }
    }
}

.main-tool-right {
    display: flex;

    .item {
        position: relative;

        &:hover {
            ul {
                display: block;
            }
        }
    }

    ul {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        left: 1px;
        display: none;

        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;

            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }
    .downlist {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;
            margin: 0;
            list-style: none;

            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }

    .down-click {
        width: 19px;
        margin-left: 1px;
        background: url("@/assets/Icons/down-white.png") no-repeat center;
        background-color: var(--main-color);

        & + ul {
            width: 106px !important;
        }

        &:hover {
            border-color: var(--light-main-color);
            background-color: var(--light-main-color);

            // & + ul {
            //     display: block;
            // }
        }
    }
}
</style>
