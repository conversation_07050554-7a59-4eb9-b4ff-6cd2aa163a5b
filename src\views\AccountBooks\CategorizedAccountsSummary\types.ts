export interface ISearchParams {
    period_s: number;
    period_e: number;
    sbj_leval_s: number;
    sbj_leval_e: number;
    vg_id: number | string;
    vnumStart: string;
    vnumEnd: string;
    fcid: number;
    showNumber: number;
}

export interface IRows {
    asub_code: string;
    asub_id: number;
    asub_name: string;
    credit: number;
    credit_qut: number;
    debit: number;
    debit_qut: number;
    measureunit: string;
}

export interface ITableData {
    total: number;
    rows: Array<IRows>;
    attachCount: number;
    voucherCount: number;
}
export interface IFcList {
    asId: number;
    code: string;
    id: number;
    isBaseCurrency: boolean;
    name: string;
    preName: string;
    rate: number;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}
export interface IAsubCodeLength {
    asid:number;
    codeLength:Array<number>;
    firstAsubLength:number;
    firstCodeLength:number;
    forthAsubLength:number;
    forthCodeLength:number;
    preName:string;
    secondAsubLength:number;
    secondCodeLength:number;
    thirdAsubLength:number;
    thirdCodeLength:number;
}