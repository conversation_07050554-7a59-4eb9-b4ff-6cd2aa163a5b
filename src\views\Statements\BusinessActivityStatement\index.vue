<template>
    <div class="content">
        <div class="title">业务活动表</div>
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef" />
                            <CheckOutTooltip v-if="periodIsCheckOut"></CheckOutTooltip>
                        </div>
                        <div class="main-tool-right">
                            <Dropdown
                                :btnTxt="'打印'"
                                :downlistWidth="102"
                                v-permission="['businessactivitystatement-canprint']">
                                <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                <li @click="handlePrint(1)">批量打印</li>
                                <li @click="handlePrint(2)">打印设置</li>
                            </Dropdown>
                            <div class="button large-1 dropdown ml-16" v-permission="['businessactivitystatement-canexport']">
                                <div style="width: 100%; text-align: center">导出</div>
                                <div class="downlist" style="width: 100%">
                                    <div class="downlist-buttons large">
                                        <div @click="handleExport(0)">当前报表数据</div>
                                        <div @click="handleExport(1)">批量导出</div>
                                    </div>
                                </div>
                            </div>
                            <a
                                v-permission="['businessactivitystatement-canshare']"
                                class="button ml-10"
                                @click="handleShare"
                                v-show="!isHideBarcode"
                                >微信分享</a
                            >
                            <a
                                v-permission="['businessactivitystatement-canedit']"
                                class="button solid-button ml-10"
                                @click="handleReset"
                                v-if="isShow"
                                >重置公式</a
                            >
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <el-table
                            :class="isErp ? 'erp-table' : ''"
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                            :data="tableData"
                            border
                            fit
                            stripe
                            scrollbar-always-on
                            :empty-text="emptyText"
                            highlight-current-row
                            class="custom-table"
                            row-key="lineID"
                            :row-class-name="setTitleRowStyle"
                            ref="tableRef"
                            @cell-dblclick="handleCellClick"
                            @header-dragend="headerDragend">
                            <el-table-column
                                label="项目"
                                min-width="392"
                                header-align="center"
                                prop="proName"
                                :width="getColumnWidth(setModule, 'proName')">
                                <template #default="scope">
                                    <div :class="assertNameClass(scope.row)" :title="scope.row.proName">
                                        <span>{{ scope.row.proName }}</span>
                                        <template v-if="status !== 3 && isShowEditFormula(scope.row)">
                                            <div
                                                class="link"
                                                v-permission="['businessactivitystatement-canedit']"
                                                @click="
                                                    openEquationDialog(
                                                        scope.row.lineType,
                                                        scope.row.lineID,
                                                        scope.row.statementId,
                                                        scope.row.proName
                                                    )
                                                ">
                                                编辑公式
                                            </div>
                                        </template>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="lineNumber"
                                label="行次"
                                min-width="71"
                                :formatter="rowNumberFormatter"
                                :width="getColumnWidth(setModule, 'lineNumber')" />
                            <template v-if="isShowBusiness">
                                <el-table-column label="本月数" header-align="center">
                                    <template #header>
                                        <div class="table-header">
                                            <el-icon><Switch @click="swapColumns" /></el-icon>
                                            <span>本月数</span>
                                        </div>
                                    </template>
                                    <el-table-column
                                        label="非限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="nonNote"
                                        :width="getColumnWidth(setModule, 'nonNote')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.nonFiniteMonthTotal + ''"
                                                :formula="calcFormula(scope.row.nonNote, 0)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.nonNote, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '非限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="note"
                                        :width="getColumnWidth(setModule, 'note')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.finiteMonthTotal + ''"
                                                :formula="calcFormula(scope.row.note, 0)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.note, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="合计"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="monthTotal"
                                        :width="getColumnWidth(setModule, 'monthTotal')">
                                        <template #default="scope">
                                            <span
                                                :class="scope.row.monthTotal < 0 ? 'highlight-red' : ''"
                                                v-show="scope.row.monthTotal !== 0"
                                                >{{ formatMoney(scope.row.monthTotal) }}</span
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table-column>
                                <el-table-column label="本年数" header-align="center">
                                    <el-table-column
                                        label="非限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="nonFiniteYearTotal"
                                        :width="getColumnWidth(setModule, 'nonFiniteYearTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.nonFiniteYearTotal + ''"
                                                :formula="calcFormula(scope.row.nonNote, 1)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.nonNote, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '非限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="finiteYearTotal"
                                        :width="getColumnWidth(setModule, 'finiteYearTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.finiteYearTotal + ''"
                                                :formula="calcFormula(scope.row.note, 1)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.note, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="合计"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="yearTotal"
                                        :width="getColumnWidth(setModule, 'yearTotal')"
                                        :resizable="false">
                                        <template #default="scope">
                                            <span
                                                :class="scope.row.yearTotal < 0 ? 'highlight-red' : ''"
                                                v-show="scope.row.yearTotal !== 0"
                                                >{{ formatMoney(scope.row.yearTotal) }}</span
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table-column>
                            </template>
                            <template v-else>
                                <el-table-column label="本年数" header-align="center">
                                    <el-table-column
                                        label="非限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="nonFiniteYearTotal"
                                        :width="getColumnWidth(setModule, 'nonFiniteYearTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.nonFiniteYearTotal + ''"
                                                :formula="calcFormula(scope.row.nonNote, 1)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.nonNote, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '非限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="finiteYearTotal"
                                        :width="getColumnWidth(setModule, 'finiteYearTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.finiteYearTotal + ''"
                                                :formula="calcFormula(scope.row.note, 1)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.note, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="合计"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="yearTotal"
                                        :width="getColumnWidth(setModule, 'yearTotal')">
                                        <template #default="scope">
                                            <span
                                                :class="scope.row.yearTotal < 0 ? 'highlight-red' : ''"
                                                v-show="scope.row.yearTotal !== 0"
                                                >{{ formatMoney(scope.row.yearTotal) }}</span
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table-column>
                                <el-table-column label="本月数" header-align="center">
                                    <template #header>
                                        <div class="table-header">
                                            <el-icon><Switch @click="swapColumns" /></el-icon>
                                            <span>本月数</span>
                                        </div>
                                    </template>
                                    <el-table-column
                                        label="非限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="nonFiniteMonthTotal"
                                        :width="getColumnWidth(setModule, 'nonFiniteMonthTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.nonFiniteMonthTotal + ''"
                                                :formula="calcFormula(scope.row.nonNote, 0)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.nonNote, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '非限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="限定性"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        prop="finiteMonthTotal"
                                        :width="getColumnWidth(setModule, 'finiteMonthTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.finiteMonthTotal + ''"
                                                :formula="calcFormula(scope.row.note, 0)"
                                                :line-number="scope.row.lineNumber"
                                                :icon-show="Boolean(calcFormula(scope.row.note, 1))"
                                                :queryFormulas="isQueryFormulas(scope.row)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, '限定性')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="合计"
                                        min-width="122"
                                        header-align="right"
                                        align="right"
                                        rop="monthTotal"
                                        :width="getColumnWidth(setModule, 'monthTotal')"
                                        :resizable="false">
                                        <template #default="scope">
                                            <span
                                                :class="scope.row.monthTotal < 0 ? 'highlight-red' : ''"
                                                v-show="scope.row.monthTotal !== 0"
                                                >{{ formatMoney(scope.row.monthTotal) }}</span
                                            >
                                        </template>
                                    </el-table-column>
                                </el-table-column>
                            </template>
                        </el-table>
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <el-tabs v-model="actived">
                        <el-tab-pane label="限定性" name="limit"> </el-tab-pane>
                        <el-tab-pane label="非限定性" name="nonLimit"> </el-tab-pane>
                    </el-tabs>
                    <div v-show="actived === 'limit'">
                        <EditEquation
                            ref="editRef"
                            :statement-id="editData.statementId"
                            :line-id="editData.lineId"
                            :pid="editData.pid"
                            :title="editData.title"
                            :classify="true"
                            class="edit-content"
                            columnType="1020"
                            month-title="本月累计"
                            year-title="本年累计"
                            :value-type-options="valueTypeOptions"
                            @handle-submit-success="handleEditSubmitSuccess"
                            @handle-cancel="handleEditCancel">
                        </EditEquation>
                    </div>
                    <div v-show="actived === 'nonLimit'">
                        <EditEquation
                            ref="nonEditRef"
                            :statement-id="editData.statementId"
                            :line-id="editData.lineId"
                            :pid="editData.pid"
                            :title="editData.title"
                            month-title="本月累计"
                            year-title="本年累计"
                            :classify="true"
                            class="edit-content"
                            columnType="1010"
                            :value-type-options="valueTypeOptions"
                            @handle-submit-success="handleEditSubmitSuccess"
                            @handle-cancel="handleEditCancel">
                        </EditEquation>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
    <!-- 打印导出弹窗 -->
    <PrintOrExportDialog :show-dialog="showDialog" :type="dialogType" :pid="searchInfo.pid" :fromStatement="9" @close="handleDialogClose" />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="业务活动表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "BusinessActivityStatement",
};
</script>
<script setup lang="ts">
import { reactive, ref, watch, nextTick, onMounted, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { useRoute } from "vue-router";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { hasNumberTitle, rowNumberFormatter } from "@/views/Statements/CashFlowStatement/utils";
import { share } from "@/views/Statements/utils";
import { globalPrint, globalExport, getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import Dropdown from "@/components/Dropdown/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import { formatMoney } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import type { IPeriod, ITableItem } from "./types";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import type { ITableLineIDItem } from "@/views/Statements/types";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import usePrint from "@/hooks/usePrint";
import { checkPermission } from "@/util/permission";
import { handleCellClick, getSwitchState } from "@/views/Statements/utils";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import { PeriodStatus } from "@/api/period";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "BusActSheet";
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);

const periodStore = useAccountPeriodStore();
const editRef = ref<InstanceType<typeof EditEquation>>();
const nonEditRef = ref<InstanceType<typeof EditEquation>>();
const actived = ref("limit");
const shareReportHost = ref("");
const reportStatementId = Number(useAccountSetStore().accountSet?.accountingStandard) * 1000 + ReportTypeEnum["BusinessActivitySheet"];

const valueTypeOptions = [{ value: "4", label: "发生额" }];

const route = useRoute();
const searchInfo = reactive({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
});
const status = ref();

const slots = ["main", "edit"];
const currentSlot = ref("main");
const loading = ref(false);
const emptyText = ref("");
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const periodIsCheckOut = computed(() => {
    return periodRef.value?.periodStatus === PeriodStatus.CheckOut;
});

const isShow = ref(false); // 重置公式
const tableData = ref<ITableItem[]>([]);

// 打印导出
const handleDialogClose = () => {
    showDialog.value = false;
};
const isShowBusiness = ref(getSwitchState("isShowBusiness"));
const tableRef = ref();
const colunmOrderChange = ref("0");
const swapColumns = () => {
    isShowBusiness.value = !isShowBusiness.value;
    localStorage.setItem("isShowBusiness", JSON.stringify(isShowBusiness.value));
    colunmOrderChange.value = isShowBusiness.value ? "0" : "1";
    tableRef.value.doLayout();
};

function getDefaultParams() {
    return {
        pid: searchInfo.pid,
        colunmOrderChange: colunmOrderChange.value,
    };
}
const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "businessActivityStatement",
    `/api/BusinessActivityStatement/Print`,
    {},
    false,
    false,
);
const handleExport = (exportType: number) => {
    if (exportType === 0) {
        globalExport(`/api/BusinessActivityStatement/Export?` + getUrlSearchParams(getDefaultParams()));
    } else {
        // 批量导出
        dialogType.value = "export";
        showDialog.value = true;
    }
};
onMounted(() => {
    colunmOrderChange.value = isShowBusiness.value ? "0" : "1";
});

const handleShare = () => {
    request({
        url: `/api/BusinessActivityStatement/Share?PId=${searchInfo.pid}`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            shareReportHost.value =
                window.accountSrvHost +
                "/api/WxPay/MakeQRCode.ashx?data=" +
                window.shareReportHost +
                "/ShareReport/" +
                res.data +
                "&CurrentSystemType=1";
            share(shareReportHost.value);
        } else {
            ElNotify({
                type: "error",
                message: "业务活动表分享错误",
            });
        }
    });
};

const hasReset = () => {
    // 检查是否有自定义公式
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${reportStatementId}`,

        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                isShow.value = res.data;
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
};
// 获取当前账套指定期间的状态,结账状态不允许编辑公式
const checkAccPeriodStatus = () => {
    request({
        url: `/api/Period?pid=${searchInfo.pid}`,
        method: "get",
    }).then((res: IResponseModel<IPeriod>) => {
        if (res.state === 1000) {
            status.value = res.data.status;
        }
    });
};
// const hasTwoLevelTitle = (value: string) => {
//     const chinaNumber = ["（一）", "（二）", "（三）", "（四）"];
//     for (let i = 0; i < chinaNumber.length; i++) {
//         if (value.indexOf(chinaNumber[i]) > -1) return true;
//     }
//     return false;
// };
// 业务活动表只有民非组织有
const handleSearch = () => {
    checkAccPeriodStatus();
    loading.value = true;
    emptyText.value = " ";
    tableData.value = [];
    // 获取业务活动表
    request({
        url: `/api/BusinessActivityStatement?PId=${searchInfo.pid}`,
        method: "get",
    })
        .then((res: IResponseModel<ITableItem[]>) => {
            loading.value = false;
            if (res.state === 1000) {
                let parent = null;
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children!.push(element);
                        continue;
                    }
                    tableData.value.push(element);
                }
                hasReset();
            } else {
                ElNotify({
                    type: "error",
                    message: res.msg,
                });
            }
            if (!tableData.value.length) {
                emptyText.value = "暂无数据";
            }
        })
        .catch(() => {
            ElNotify({
                type: "warning",
                message: "获取期间信息出错，请联系侧边栏客服",
            });
        });
};
const lineIDList = ref();
function searchLineId() {
    request({
        url: `/api/BusinessActivityStatement/Formulas?PId=${searchInfo.pid}`,
        method: "get",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            lineIDList.value = res.data;
        }
    });
}
const isQueryFormulas = (row: ITableItem) => {
    if (lineIDList.value && checkPermission(["generalledger-canview"])) {
        let result = lineIDList.value[row.lineID];
        return result?.length > 0 ? !!result[0].asubname : false;
    } else {
        return false;
    }
};
const goToQueryFormulas = (row: ITableItem, val: string) => {
    let time = searchInfo.pid;
    let result = [];
    if (val === "非限定性") {
        result = lineIDList.value[row.lineID].filter((item: ITableLineIDItem) => item.columnType == 1010);
    } else {
        result = lineIDList.value[row.lineID].filter((item: ITableLineIDItem) => item.columnType == 1020);
    }
    let SubjectIDList = "&SubjectIDList=" + result.map((item: ITableLineIDItem) => item.asubid).join(",");
    let params = {
        period_s: time,
        period_e: time,
    };
    if (result.length > 0) {
        globalWindowOpenPage("/AccountBooks/GeneralLedger?" + getUrlSearchParams(params) + SubjectIDList, "总账");
    }
};

const handleReset = () => {
    ElConfirm(
        "确认删除此报表所有自定义公式？<div style='margin-top: 10px;'>重置公式仅影响未结账期间的报表数据</div>",
        false,
        () => {},
        "重置此报表公式"
    ).then((r: boolean) => {
        if (r) {
            // 重置公式
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${reportStatementId}`,
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    ElNotify({
                        type: "success",
                        message: "已经成功重置",
                    });
                    handleSearch();
                    checkAccPeriodStatus();
                    hasReset();
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
};

watch(
    () => searchInfo.pid,
    () => {
        handleSearch();
        searchLineId();
    },
    { immediate: true }
);

const calcFormula = (note: string, columnIndex: number) => note.split("|")[columnIndex] || "";

const assertNameClass = (row: ITableItem) => {
    let className: string;
    if (hasNumberTitle(row.proName)) {
        if (row.lineType === 0 || row.lineType === 3) {
            className = "level1";
        } else {
            className = "level2";
        }
    } else {
        className = "level2";
    }
    return className;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle(data.row.proName)) {
        return "highlight-title-row";
    }
}
const editData = reactive({
    statementId: 0,
    lineId: 0,
    pid: 0,
    title: "",
});

const openEquationDialog = (lineType: number, lineId: number, statementId: number, title: string) => {
    if (lineType === 1) {
        editData.statementId = statementId;
        editData.lineId = lineId;
        editData.pid = searchInfo.pid;
        editData.title = title;
        nextTick().then(() => {
            editRef.value?.init();
            nonEditRef.value?.init();
            currentSlot.value = "edit";
        });
    }
};

const handleEditSubmitSuccess = () => {
    handleSearch();
    searchLineId();
    currentSlot.value = "main";
};

const handleEditCancel = () => {
    currentSlot.value = "main";
};

// 判断展示编辑公式
const isShowEditFormula = (row: ITableItem) => !hasNumberTitle(row.proName) && !row.proName.includes("合计");

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/BusinessActivityStatement.less";
@import "@/style/Statements/Statements.less";

.edit-content,
.el-tabs {
    width: 1100px !important;
    background-color: #fff;
}
:deep(.table-header) {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
