<template>
    <div class="content">
        <content-slider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">费用单据</div>
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left" @click="handlePreventEditTableSubmit()">
                            <SearchInfoContainer ref="containerRef">
                                <template v-slot:title>{{ currentPeriodInfo }} </template>
                                <div class="line-item first-item input">
                                    <div class="line-item-title">单据日期:</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.date_s"
                                            v-model:endPid="searchInfo.date_e"
                                            :clearable="true"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">凭证日期:</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.startVDate"
                                            v-model:endPid="searchInfo.endVDate"
                                            :disabled-date-start="disabledStartVDate"
                                            :disabled-date-end="disabledEndVDate"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">凭证字号：</div>
                                    <div class="line-item-field">
                                        <div class="vgid-line">
                                            <el-select
                                                v-model="searchInfo.vgId"
                                                :teleported="false"
                                                style="width: 90px"
                                                @change="handleVgIdChange"
                                            >
                                                <el-option :value="0" label="请选择">请选择</el-option>
                                                <el-option :value="1" label="全部">全部</el-option>
                                                <el-option
                                                    v-for="item in voucherGroup"
                                                    :value="item.id"
                                                    :key="item.id"
                                                    :label="item.name"
                                                ></el-option>
                                            </el-select>
                                            <el-input
                                                clearable
                                                type="text"
                                                class="ml-10"
                                                v-model="searchInfo.startVNum"
                                                :disabled="!searchInfo.vgId"
                                                @input="startVNumLimit"
                                            ></el-input>
                                            <span style="padding: 0 8px; line-height: 30px">至</span>
                                            <el-input
                                                clearable
                                                type="text"
                                                v-model="searchInfo.endVNum"
                                                :disabled="!searchInfo.vgId"
                                                @input="endVNumLimit"
                                            ></el-input>
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">生成凭证：</div>
                                    <div class="line-item-field">
                                        <div>
                                            <el-select v-model="searchInfo.vstatus" :teleported="false">
                                                <el-option :value="0" label="全部">全部</el-option>
                                                <el-option :value="10" label="是">是</el-option>
                                                <el-option :value="99" label="否">否</el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">费用类型：</div>
                                    <div class="line-item-field">
                                        <VirtualSelectCheckbox
                                            :options="billTypeListOPtions"
                                            :use-el-icon="true"
                                            width="300px"
                                            v-model:selectedList="billTypesSelected"
                                            class="item"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">费用事由:</div>
                                    <div class="line-item-field">
                                        <el-input type="text" v-model="searchInfo.billdesc" clearable></el-input>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">单据编号:</div>
                                    <div class="line-item-field">
                                        <el-input type="text" v-model="searchInfo.billno" clearable></el-input>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">收款人：</div>
                                    <div class="line-item-field">
                                        <SelectV2
                                            v-model="searchInfo.payeeid"
                                            ref="payeeRef"
                                            class="item"
                                            placeholder="请选择或输入"
                                            :teleported="false"
                                            :fit-input-width="true"
                                            :filterable="true"
                                            :clearable="true"
                                            :props="aaProps"
                                            :options="showPayeeList"
                                            :toolTipOptions="{ dynamicWidth: true }"
                                            :remote="payeeList.length > 0"
                                            :filter-method="PayeeMethod"
                                            @visible-change="PayeeVisibleChange"
                                            :isSuffixIcon="true"
                                        ></SelectV2>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">结算方式:</div>
                                    <div class="line-item-field">
                                        <div>
                                            <Select
                                                :teleported="false"
                                                class="item"
                                                :filterable="true"
                                                v-model="searchInfo.paymethod"
                                                @visible-change="handleSelectVisibleChange"
                                                :filter-method="PayMethodFilterMethod"
                                            >
                                                <ElOption
                                                    v-for="item in showPayMethods"
                                                    :key="item.pmId"
                                                    :label="item.pmName"
                                                    :value="item.pmId"
                                                ></ElOption>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">部门：</div>
                                    <div class="line-item-field">
                                        <SelectV2
                                            v-model="searchInfo.depid"
                                            ref="departmentRef"
                                            class="item"
                                            placeholder="请选择或输入"
                                            :teleported="false"
                                            :fit-input-width="true"
                                            :filterable="true"
                                            :clearable="true"
                                            :props="aaProps"
                                            :options="showdepartmentList"
                                            :toolTipOptions="{ dynamicWidth: true }"
                                            @visible-change="handleSelectVisibleChange"
                                            :remote="departmentList.length > 0"
                                            :filter-method="departFilterMethod"
                                            :isSuffixIcon="true"
                                        ></SelectV2>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">单据来源：</div>
                                    <div class="line-item-field">
                                        <div>
                                            <el-select 
                                                v-model="searchInfo.sourceType" 
                                                :teleported="false"
                                                :filterable="true"
                                                :filter-method="sourceTypeFilterMethod"
                                            >
                                                <el-option v-for="item in showSourceTypeList" :value="item.value" :label="item.label" :key="item.value">{{ item.label }}</el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">备注:</div>
                                    <div class="line-item-field">
                                        <el-input type="text" v-model="searchInfo.note" clearable></el-input>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button solid-button" @click="handleConfirm">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                        </div>
                        <ExpenseOperate
                            ref="ExpenseOperateRef"
                            :basicTypeList="basicTypeList"
                            :checkedTableData="checkedTableData"
                            @operateClick="handlePreventEditTableSubmit()"
                            @expenseAccountSetting="expenseAccountSetting"
                            @reloadTableData="getTableData"
                            @importSuccess="handleImportSuccess"
                            @goGenerateVoucher="loadVoucherView"
                            @export="handleExportExcel"
                            @voucherSetting="voucherSettingDialogShow = true"
                        ></ExpenseOperate>
                    </div>
                    <div class="main-center">
                        <expense-bill-table
                            ref="expenseTableRef"
                            v-model:loading="loading"
                            :show-all="showAll"
                            :basicTypeList="basicTypeList"
                            :checkDate="expenseBillDate.checkDate"
                            :searchEnd="searchInfo.date_e"
                            :searchStart="searchInfo.date_s"
                            @reloadBillTypeList="handleTableAddBillType"
                            @handle-selection-change="handleSelectionChange"
                            @change-date="handleChangeDate"
                            @reloadTableData="getTableData"
                        >
                        </expense-bill-table>
                    </div>
                </div>
            </template>
            <template #expenseAccountSetting>
                <ExpenseAccountSetting
                    ref="accountSettingRef"
                    :basicTypeList="basicTypeList"
                    @reloadBillTypeList="getBillTypeList"
                    @goMain="currentSlot = 'main'"
                    @reloadTableData="getTableData"
                ></ExpenseAccountSetting>
            </template>
            <template #voucher>
                <div class="slot-content">
                    <ExpenseGenerateVoucher
                        :open-gen-voucher="openGenVoucher"
                        :cancel-gen-voucher="() => (currentSlot = 'main')"
                        :gen-voucher-save-success="genVoucherSaveSuccess"
                        ref="ExpenseGenerateVoucherRef"
                        @voucherSetting="voucherSettingDialogShow = true"
                    >
                    </ExpenseGenerateVoucher>
                </div>
            </template>
        </content-slider>
        <GenerateVoucherSettingDialog
            settingType="ExpenseBill"
            ref="generateVoucherSettingDialogRef"
            :defaultSummary="1"
            v-model="voucherSettingDialogShow"
        />
    </div>
</template>
<script lang="ts">
export default {
    name: "ExpenseBill",
};
</script>
<script lang="ts" setup>
import { ref, reactive, computed, onMounted, toRef, onUnmounted, onActivated, watch, watchEffect } from "vue";
import dayjs from "dayjs";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";

import ElOption from "@/components/Option/index.vue";
import Select from "@/components/Select/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import ExpenseGenerateVoucher from "@/views/Invoice/ExpenseBill/components/ExpenseGenerateVoucher.vue";
import ExpenseOperate from "@/views/Invoice/ExpenseBill/components/ExpenseOperate.vue";
import type { ITableItem, BasicTypeList, IBillSearchInfo } from "./types";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import SelectV2 from "@/components/SelectV2/index.vue";
import ExpenseBillTable from "@/views/Invoice/ExpenseBill/components/ExpenseBillTable.vue";
import ExpenseAccountSetting from "@/views/Invoice/ExpenseBill/components/ExpenseAccountSetting.vue";
import type { IVoucherSetting } from "@/components/Dialog/GenerateVoucherSetting/type";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
// import VirtualSelectCheckbox from "@/views/AccountBooks/AssistCombineStatement/components/VirtualSelectCheckbox.vue";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import GenerateVoucherSettingDialog from "@/components/Dialog/GenerateVoucherSetting/index.vue";
import { editConfirm } from "@/util/editConfirm";
import { ElAlert } from "@/util/confirm";
import { getAACompanyId, getGlobalToken } from "@/util/baseInfo";
import { sourceTypeList } from "./utils";
import { commonFilterMethod } from "@/components/Select/utils";

const voucherSettingDialogShow = ref(false);
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = computed(() => voucherGroupStore.voucherGroupList);
const departmentList = toRef(useAssistingAccountingStore(), "departmentList");
let payeeList = computed(() =>
    useAssistingAccountingStore()
        .getAssistingAccountingByTypeModel(10003)
        .filter((item) => item.aaeid > 0)
);
const aaProps = {
    value: "aaeid",
    label: "aaname",
};

const ExpenseOperateRef = ref<InstanceType<typeof ExpenseOperate>>();
const showAll = computed(() => {
    return ExpenseOperateRef.value?.showAll || false;
});
let slots = ["main", "masterPlate", "voucher", "checkVoucher", "expenseAccountSetting"];
let currentSlot = ref("main");
const searchInfo = reactive<IBillSearchInfo>({
    date_s: dayjs().startOf("month").format("YYYY-MM-DD"),
    date_e: dayjs().endOf("month").format("YYYY-MM-DD"),
    vstatus: 0,
    billtypeid: "", //费用类型id
    billdesc: "", // 费用事由
    billno: "", // 费用单据编号
    payeeid: "",
    payeeName: "",
    paymethod: 0,
    sourceType: 0,
    depid: "",
    note: "",
    startVDate: "",
    endVDate: "",
    startVNum: "",
    endVNum: "",
    vgId: 0,
});
const currentPeriodInfo = ref(`${searchInfo.date_s || " "}至${searchInfo.date_e || " "}`);

const loading = ref(false);
const tableData = ref<ITableItem[]>([]);
const expenseTableRef = ref<InstanceType<typeof ExpenseBillTable>>();
let hasLoading = false;
const route = useRoute();
let expenseBillDate = reactive({ checkDate: "", searchEnd: "", searchStart: "", startDate: "" });
let lastBillSelectData = { searchEnd: "", searchStart: "" };
const getTableData = (row?: ITableItem) => {
    if (!hasLoading) {
        if (route.query.searchStartDate) {
            searchInfo.date_s = route.query.searchStartDate as string;
        }
        if (route.query.searchEndDate) {
            searchInfo.date_e = route.query.searchEndDate as string;
        }

        if (route.query.vdate) {
            searchInfo.startVDate = route.query.vdate as string;
            searchInfo.endVDate = route.query.vdate as string;
        }
        if (route.query.vnum) {
            searchInfo.vgId = 1;
            searchInfo.startVNum = route.query.vnum as string;
            searchInfo.endVNum = route.query.vnum as string;
        }
        hasLoading = true;
    }
    if (searchInfo.date_s === "" || searchInfo.date_s + "" === "null" || searchInfo.date_e === "" || searchInfo.date_e + "" === "null") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        searchInfo.date_s = lastBillSelectData.searchStart;
        searchInfo.date_e = lastBillSelectData.searchEnd;
        currentPeriodInfo.value = `${searchInfo.date_s || " "}至${searchInfo.date_e || " "}`;
        return;
    }
    if (new Date(searchInfo.date_s) > new Date(searchInfo.date_e)) {
        ElNotify({ type: "warning", message: "开始日期不能晚于结束日期哦~" });
        searchInfo.date_s = lastBillSelectData.searchStart;
        searchInfo.date_e = lastBillSelectData.searchEnd;
        currentPeriodInfo.value = `${searchInfo.date_s || " "}至${searchInfo.date_e || " "}`;
        return;
    }
    if (Number(searchInfo.startVNum) > Number(searchInfo.endVNum) && searchInfo.endVNum !== "" && searchInfo.endVNum + "" !== "null") {
        ElNotify({ type: "warning", message: "起始凭证号不能小于结束凭证号呦！" });
        return;
    }
    loading.value = true;
    currentPeriodInfo.value = `${searchInfo.date_s || " "}至${searchInfo.date_e || " "}`;
    lastBillSelectData = { searchEnd: searchInfo.date_e, searchStart: searchInfo.date_s };
    let payeeInputNotZhiyuan = searchInfo.payeeid && payeeList.value.findIndex((item) => item.aaeid === Number(searchInfo.payeeid)) === -1;
    if (payeeInputNotZhiyuan) {
        searchInfo.payeeName = searchInfo.payeeid;
        searchInfo.payeeid = "";
    } else {
        searchInfo.payeeName = "";
    }
    searchInfo.billtypeid =
        billTypesSelected.value.length === basicTypeList.value.billTypes.length ? "" : billTypesSelected.value.join(",");
    request({
        url: "/api/ExpenseBill/PagingList",
        method: "get",
        params: {
            ...searchInfo,
            showall: showAll.value,
            pageIndex: expenseTableRef.value?.paginationData.currentPage,
            pageSize: expenseTableRef.value?.paginationData.pageSize,
        },
    })
        .then((res: IResponseModel<{ rows: ITableItem[]; total: number }>) => {
            if (res.state === 1000) {
                tableData.value = res.data.rows;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg || "获取失败",
                });
                tableData.value = [];
            }
            loading.value = false;
            expenseTableRef.value?.initTableData(tableData.value, res.data.total, row);
            if (payeeInputNotZhiyuan) {
                searchInfo.payeeid = searchInfo.payeeName;
            }
            let urlParams: any = {
                appasid: getGlobalToken(),
            };
            const queryParams = route.query;
            if ("stay" in queryParams) {
                urlParams.stay = queryParams["stay"];
            }
            if (window.isAccountingAgent) {
                urlParams.AACompanyId = getAACompanyId();
            }
            if (route.query.searchStartDate) {
                const replacePath = "/Invoice/ExpenseBill?" + getUrlSearchParams(urlParams);
                useRouterArrayStoreHook().replaceCurrentRouter(replacePath);
            }
        })
        .catch(() => {
            loading.value = false;
        });
};
const handleImportSuccess = (startDate: string, endDate: string) => {
    handleReset();
    searchInfo.date_s = startDate;
    searchInfo.date_e = endDate;
    getTableData();
};

const handlePreventEditTableSubmit = () => {
    expenseTableRef.value?.preventEditTableSubmit();
};

const reloadTableDataFn = () => {
    getTableData();
};
const getSearchDate = () => {
    request({
        url: "/api/ExpenseBill/GetExpenseBillDate",
        method: "get",
    })
        .then((res: IResponseModel<{ checkDate: string; searchEnd: string; searchStart: string; startDate: string }>) => {
            if (res.state === 1000) {
                expenseBillDate = res.data;
                if (!route.query.searchStartDate) {
                    searchInfo.date_s = res.data.searchStart;
                    searchInfo.date_e = res.data.searchEnd;
                }
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
            getTableData();
        })
        .catch(() => {
            ElNotify({
                type: "warning",
                message: "请求失败",
            });
        });
};
onMounted(() => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop !== undefined) {
        const timer = setTimeout(() => {
            delete (currentRouterModel as any).stop;
            clearTimeout(timer);
        });
    }
    getBillTypeList();
    getSearchDate();
    window.addEventListener("ExpenseBillVoucherChange", reloadTableDataFn);
});
const handleTableAddBillType = async (cate: string, no: string, name: string) => {
    await getBillTypeList();
    let targetId = 1;
    if (cate === "billTypes") {
        targetId = basicTypeList.value.billTypes.find((item) => item.billTypeNo === no && item.billTypeName === name)?.billTypeId || 1;
    } else {
        targetId = basicTypeList.value.payMethods.find((item) => item.pmNo === no && item.pmName === name)?.pmId || 1;
    }
    expenseTableRef.value?.setcurrentBillTypeValue(cate, targetId);
};
// 重新加载（多用于其他页签操作）
let cacheRouterQueryParams: any = null;
let firstInit = true;
function reLoadCurrentPage() {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if ((currentRouterModel as any).stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        (currentRouterModel as any).stop = true;
    }
}
function isFromOtherPage(page: "voucherPage" | "voucherList") {
    if (!cacheRouterQueryParams?.from && route.query.from === page) {
        return true;
    }
    if (
        cacheRouterQueryParams?.from === page &&
        route.query?.from === page &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }
    return false;
}
// 多页签优化2.0
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const currentPath = ref(route.path);
watch(currentSlot, (val) => {
    const flag = val === "voucher";
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        (currentRouterModel as any).isEditting = flag;
    }
});
const isEditting = computed(() => {
    return expenseTableRef.value?.newBillIsEdit as boolean;
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
onActivated(() => {
    if (isFromOtherPage("voucherPage")) {
        if (!searchInfo.date_s || !searchInfo.date_e) return;
        getTableData();
        return;
    }
    if (isFromOtherPage("voucherList") && !firstInit) {
        if (isEditting.value) {
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
    }
});
onBeforeRouteLeave((to, from, next) => {
    cacheRouterQueryParams = from.query;
    firstInit = false;
    next();
});
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();

const basicTypeList = ref<BasicTypeList>({
    billTypes: [],
    payMethods: [],
});
const billTypeListOPtions = ref<Array<Option>>([]);
const billTypesSelected = ref<number[]>([]);
const getBillTypeList = async () => {
    await request({
        url: "/api/ExpenseBill/GetBasicTypeList",
        method: "get",
    })
        .then((res: IResponseModel<BasicTypeList>) => {
            if (res.state === 1000) {
                basicTypeList.value = res.data;
                billTypeListOPtions.value = basicTypeList.value.billTypes.map((i) => new Option(Number(i.billTypeId), i.billTypeName));
                billTypesSelected.value = basicTypeList.value.billTypes.map((item) => item.billTypeId);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "warning",
                message: "请求失败",
            });
        });
};
let checkedTableData = ref<ITableItem[]>([]);
const handleSelectionChange = (selection: ITableItem[]) => {
    checkedTableData.value = selection;
};
function handleChangeDate(startDate: string, endDate: string, row: ITableItem) {
    searchInfo.date_s = startDate;
    searchInfo.date_e = endDate;
    getTableData(row);
}

const handleConfirm = () => {
    if (searchInfo.date_s === "" || searchInfo.date_s + "" === "null" || searchInfo.date_e === "" || searchInfo.date_e + "" === "null") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    expenseTableRef.value?.setpaginationData();
    handleClose();
    getTableData();
};
const handleClose = () => containerRef.value?.handleClose();
const handleReset = () => {
    searchInfo.date_s = expenseBillDate.searchStart;
    searchInfo.date_e = expenseBillDate.searchEnd;
    searchInfo.endVDate = "";
    searchInfo.startVDate = "";
    searchInfo.vstatus = 0;
    billTypesSelected.value = basicTypeList.value.billTypes.map((item) => item.billTypeId);
    searchInfo.billtypeid = "";
    searchInfo.billdesc = "";
    searchInfo.billno = "";
    searchInfo.payeeid = "";
    searchInfo.payeeName = "";
    searchInfo.paymethod = 0;
    searchInfo.depid = "";
    searchInfo.sourceType = 0;
    searchInfo.note = "";
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
    searchInfo.vgId = 0;
    searchInfo.vstatus = 0;
    searchInfo.sourceType = 0;
};
const handleSelectVisibleChange = (visible: boolean) => {
    if (!visible) {
        containerRef.value?.stopCloseSearchPopover();
        showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
    }
};

function disabledStartVDate(time: Date) {
    const endVDate = dayjs(searchInfo.endVDate).valueOf();
    return time.getTime() > endVDate;
}
function disabledEndVDate(time: Date) {
    let startVDate = dayjs(searchInfo.startVDate).valueOf();
    return time.getTime() < startVDate;
}
function handleVgIdChange(val: number) {
    if (val === 0) {
        searchInfo.startVNum = "";
        searchInfo.endVNum = "";
    }
}
function startVNumLimit(e: string) {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.startVNum = val;
}
function endVNumLimit(e: string) {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.endVNum = val;
}
// 费用科目设置
const accountSettingRef = ref<InstanceType<typeof ExpenseAccountSetting>>();
const expenseAccountSetting = () => {
    currentSlot.value = "expenseAccountSetting";
};
const handleExportExcel = (all: boolean) => {
    if (searchInfo.date_s === "" || searchInfo.date_s + "" === "null" || searchInfo.date_e === "" || searchInfo.date_e + "" === "null") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    let params = { ...searchInfo, showall: showAll.value, exportall: all ? "all" : "" };
    globalExport("/api/ExpenseBill/Export?" + getUrlSearchParams(params));
};
// 生成凭证
const generateVoucherSettingDialogRef = ref<InstanceType<typeof GenerateVoucherSettingDialog>>();
const ExpenseGenerateVoucherRef = ref<InstanceType<typeof ExpenseGenerateVoucher>>();
function loadVoucherView() {
    let voucherSetting = generateVoucherSettingDialogRef.value?.getVoucherSetting() ?? ({} as IVoucherSetting);
    if (checkedTableData.value.find((v) => v.billDate < expenseBillDate.checkDate) && voucherSetting.voucherDate === 0) {
        ElAlert({
            message: "亲，您选择的费用单据日期月份所在的会计期间已结账，是否将凭证生成日期设置的单据日期更改为会计日期后继续生成凭证？",
        }).then((r: boolean) => {
            if (r) {
                ExpenseGenerateVoucherRef.value?.setVoucherSettings({ ...voucherSetting, voucherDate: 1 });
                ExpenseGenerateVoucherRef.value?.genVoucherFromExpenseBill(checkedTableData.value.slice());
            }
        });
    } else {
        ExpenseGenerateVoucherRef.value?.setVoucherSettings(voucherSetting);
        ExpenseGenerateVoucherRef.value?.genVoucherFromExpenseBill(checkedTableData.value.slice());
    }
}
function openGenVoucher() {
    currentSlot.value = "voucher";
}
const genVoucherSaveSuccess = () => {
    currentSlot.value = "main";
    getTableData();
};
onUnmounted(() => {
    cacheRouterQueryParams = null;

    window.removeEventListener("ExpenseBillVoucherChange", reloadTableDataFn);
});

const showPayeeList = ref<any[]>([]);
const payMethodsAll = ref<any[]>([]);
const showPayMethods = ref<any[]>([]);
const showdepartmentList = ref<any[]>([]);
const showSourceTypeList = ref<any[]>([]);
watchEffect(() => {
    showPayeeList.value = JSON.parse(JSON.stringify(payeeList.value));
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
    showSourceTypeList.value = JSON.parse(JSON.stringify(sourceTypeList));
});
watchEffect(() => {
    payMethodsAll.value = JSON.parse(JSON.stringify(basicTypeList.value.payMethods));
    payMethodsAll.value.unshift({ pmId: 0, pmName: "全部" });
    showPayMethods.value = JSON.parse(JSON.stringify(payMethodsAll.value));
});
const PayeeMethod = (value: string) => {
    showPayeeList.value = commonFilterMethod(value, payeeList.value, 'aaname');
}
const PayeeVisibleChange = (visible: boolean) => {
    if (visible) {
        showPayeeList.value = JSON.parse(JSON.stringify(payeeList.value));
    }
}
function PayMethodFilterMethod(value: string) {
    showPayMethods.value = commonFilterMethod(value, payMethodsAll.value , 'pmName');
}
function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value , 'aaname');
}
function sourceTypeFilterMethod(value: string) {
    showSourceTypeList.value = commonFilterMethod(value, sourceTypeList , 'label');
}
</script>
<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";
.content {
    .line-item-field {
        :deep(.el-select) {
            .detail-placehoder-color(#999);
            width: 300px;
        }
        .el-input,
        :deep(.el-select-v2) {
            width: 300px;
        }
    }
    :deep(.expense-import-dialog) {
        .import-dialog {
            .import-content {
                margin-top: 10px;
                margin-bottom: 10px;
            }
            .invoice-import-title {
                line-height: 30px;
                position: relative;
                bottom: -1px;
            }
        }
    }
}
</style>
