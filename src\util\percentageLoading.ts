import { createApp } from "vue";
import PercentageLoading from "@/components/PercentageLoading/index.vue";
import ElementPlus from "element-plus";

export const percentageLoading = ():Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        const contentBox = window.isErp ? document.querySelector(".router-container .content") : document.body ;
        const props = {
            close: () => {
                if (close) close();
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
            confirm: () => {
                resolve(true);
                capp.unmount(); //注销
                contentBox!.removeChild(container); //点击后清除弹窗
            },
            cancel: () => {
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
        };
        const capp = createApp(PercentageLoading, props);
        const container = document.createElement("div");
        capp.use(ElementPlus);
        capp.mount(container);
        contentBox!.insertBefore(container, contentBox!.firstChild); //插入到body最前面，层级更高
    });
}