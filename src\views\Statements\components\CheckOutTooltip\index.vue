<template>
    <div :class="['checkout', {'ml-10': !isDraft}]">
        <div class="checkout-flag">已结账</div> 
        <el-tooltip
            width="200"
            placement="right"
            effect="light"
            :teleported="false"
        >
            <template #content>
                <div class="checkout-txt">
                    <span v-if="!isDraft">已结账期间不支持编辑报表公式，请先<span class="link" @click="goToCheck">反结账</span>再编辑公式；或选择最新期间编辑公式</span>
                    <span v-else>已结账期间不支持编辑报表底稿，请先<span class="link" @click="goToCheck">反结账</span>再编辑</span>
                </div>
            </template>
            <img
                class="question-icon"
                src="@/assets/Settings/question.png"
            />
        </el-tooltip>
    </div>
</template>

<script setup lang="ts">
import { globalWindowOpenPage } from "@/util/url";
const props = defineProps({
    isDraft: {
        type: Boolean,
        default: false,
    },
});
const goToCheck = () => {
    globalWindowOpenPage('/Checkout/Checkout?from=Statements', '期末结转')
}

</script>

<style lang="less">
.checkout {
    display: flex;
    .checkout-flag {
        background-color: rgba(0, 0, 0, .08);
        font-size: 12px;
        height: 20px;
        line-height: 20px;
        padding: 0 5px;
        border-radius: 4px;
    }
    .checkout-txt {
        width: 300px;
        font-size: 14px;
        word-break: break-all;
        text-align: left;
    }
}
.question-icon {
    height: 18px; 
    cursor: pointer; 
    margin-left: 7px
}
</style>
