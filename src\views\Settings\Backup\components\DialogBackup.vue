<template>
    <el-dialog center width="453" v-model="backupingDialogShow" :show-close="false" class="settings-backup-tip-dialog dialogDrag">
        <div class="backuping-dialog" v-dialogDrag>
            <div class="backuping-dialog-content">
                <div style="font-size: 16px; line-height: 22px; color: #fff; font-weight: bold; margin-top: 20px">
                    柠檬云财务APP实时通知功能
                </div>
                <div
                    style="
                        font-size: 14px;
                        line-height: 20px;
                        color: #e4423c;
                        font-weight: bold;
                        margin-top: 71px;
                        margin-left: 213px;
                        text-align: left;
                    "
                >
                    归档完成后自动接收消息
                </div>
                <div
                    style="
                        font-size: 13px;
                        line-height: 18px;
                        color: #272727;
                        font-weight: bold;
                        margin-top: 21px;
                        text-align: left;
                        padding-left: 200px;
                    "
                >
                    账套备份提醒
                </div>
                <div style="font-size: 12px; line-height: 17px; color: #626262; margin-top: 4px; text-align: left; padding-left: 200px">
                    <div>您已成功备份账套：xxxxxxxxxxx</div>
                </div>
                <div style="font-size: 12px; line-height: 17px; color: #272727; text-align: right; padding-right: 44px; margin-top: 53px">
                    微信扫码下载
                </div>
            </div>
            <img class="backuping-dialog-close" @click="() => (backupingDialogShow = false)" src="@/assets/Settings/close2.png" />
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
const backupingDialogShow = ref(false);
function showBackupingDialog() {
    backupingDialogShow.value = true;
}
defineExpose({ showBackupingDialog });
</script>

<style lang="less" scoped>
.backuping-dialog {
    position: relative;
    .backuping-dialog-content {
        display: inline-block;
        vertical-align: middle;
        width: 453px;
        height: 390px;
        background: url("@/assets/Settings/backuping.png") no-repeat;
        background-size: 100% 100%;
        text-align: center;
        transform: translateY(-5px);
    }
    .backuping-dialog-close {
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        width: 25px;
        position: absolute;
        top: -25px;
        right: -25px;
    }
}
</style>
