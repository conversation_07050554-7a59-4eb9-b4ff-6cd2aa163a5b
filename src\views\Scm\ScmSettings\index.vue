<template>
    <div class="content" v-if="settingDirectionShow">
        <div class="title">进销存核算参数</div>
        <div class="main-content align-center">
            <div class="slot-mini-content">
                <div class="main-block width-block">
                    <div class="block-title">基本设置</div>
                    <div class="block-main">
                        <el-form :model="settings" label-width="240px" label-position="right">
                            <el-row>
                                <el-form-item label="生成的进销存凭证是否允许修改：">
                                    <el-radio-group v-model="settings.VoucherAllowChangeKey">
                                        <el-radio label="1">允许</el-radio>
                                        <el-radio label="0">不允许</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="生成凭证后的进销存单据不允许修改">
                                    <el-popover placement="right" :width="300" trigger="hover">
                                        <template #reference>
                                            <img src="@/assets/Settings/question.png" style="height: 18px; cursor: pointer; margin-left: 7px" />
                                        </template>
                                        <template #default>
                                            为确保财务数据的准确性、一致性、可追溯性,不可修改已生凭证的进销存单据喔
                                        </template>
                                    </el-popover>
                                </el-form-item>
                            </el-row>
                        </el-form>
                    </div>
                </div>
                <div class="main-block width-block">
                    <div class="block-title">默认匹配规则</div>
                    <div class="block-main">
                        <el-form :model="settings" label-position="right">
                            <el-row>
                                <el-form-item label="客户：" label-width="125px">
                                    <el-radio-group v-model="settings.CustomerMatchRule">
                                        <el-radio label="1010">名称一致</el-radio>
                                        <el-radio label="1020">编码一致</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="商品：" label-width="125px">
                                    <el-radio-group v-model="settings.CommodityMatchRule">
                                        <el-radio label="1010">名称一致</el-radio>
                                        <el-radio label="1020">编码一致</el-radio>
                                        <el-radio label="1030">名称+规格一致</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="供应商：" label-width="125px">
                                    <el-radio-group v-model="settings.VendorMatchRule">
                                        <el-radio label="1010">名称一致</el-radio>
                                        <el-radio label="1020">编码一致</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="无法匹配时是否自动添加到财务软件：" label-width="240px">
                                    <el-radio-group v-model="settings.AutoSaveToAccKey">
                                        <el-radio label="1">自动添加</el-radio>
                                        <el-radio label="0">不添加</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-row>
                            <!-- <el-row>
                                <el-form-item label="摘要：" label-width="125px" text-align="left">
                                    <el-radio-group v-model="settings.DescMatchRule">
                                        <el-radio label="1010">系统默认</el-radio>
                                        <el-radio label="1020">进销存单据备注</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-row> -->
                        </el-form>
                    </div>
                </div>
                <div class="main-block" v-loading="loading"  element-loading-text="正在加载数据..." :element-loading-background="'#fff'">
                    <div class="block-title">默认对应的会计科目</div>
                    <div class="block-main">
                        <el-form label-position="right">
                            <el-row>
                                <el-form-item label="应收账款：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.AccountsReceivableAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="库存盘亏：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.InventoryLossAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="应付账款：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.AccountsPayableAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="库存盘盈：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.InventorySurplusAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="库存商品：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.CommodityStockAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="其他入库对方科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.WarehousingOppositeAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="主营业务收入：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.MainBusinessIncomeAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="其他出库对方科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.OutboundOppositeAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        :asubImgRight="'5px'"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="主营业务成本：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.MainBusinessExpenditureAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="成本调整单对方科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.CostAdjOrderOppositeAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="进项税：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.IncomeTaxAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="组装费用来源科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.AssemblyCostAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="销项税：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.ExpenditureTaxAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="拆卸费用来源科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.DemolitionCostAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="采购优惠/付款折扣：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.PurchaseDiscountAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="客户承担费用科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.CustomerCostAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                            <el-row>
                                <el-form-item label="销售优惠/收款折扣：" label-width="135px">
                                    <SubjectPicker
                                        v-model="settings.SalesDiscountAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                                <el-form-item label="卖家承担费用科目：" label-width="170px">
                                    <SubjectPicker
                                        v-model="settings.SellerCostAsub"
                                        :is-by-numberId="true"
                                        :isParentShow="true"
                                        @subjectCountChange="subjectCountChange"
                                    ></SubjectPicker>
                                </el-form-item>
                            </el-row>
                        </el-form>
                    </div>
                </div>
                <div class="button-block">
                    <a class="button solid-button" @click="sumbitSettings">保存</a>
                    <a class="button ml-10" @click="cancelSettings">取消</a>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "ScmSettings",
};
</script>
<script setup lang="ts">
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { ElNotify } from "@/util/notify";
import { ref, onMounted, nextTick,watch, provide } from "vue";
import router from "@/router";
import { closeCurrentTab, globalWindowOpenPage } from "@/util/url";
import { isLemonClient } from "@/util/lmClient";
import { getGlobalLodash } from "@/util/lodash";
import { initAsubTreeKey } from "@/components/Picker/SubjectPicker/symbols";
import { type ISubjectTree ,getAsubTree, expansionTreeList, formatData } from "@/components/Picker/SubjectPicker/util";

const settings = ref();
// const isLoading = ref(true);
const isDefault = ref(false);
const scmAsid = ref(0);
const scmProductType = ref(0);
const settingDirectionShow = ref(false);
const loading = ref(false);
const _ = getGlobalLodash()
const asubTreeData = ref<Array<ISubjectTree>>([]);
provide(initAsubTreeKey, asubTreeData);

getAsubTree(1)
.then((res: any) => {
        if (res.state === 1000) {
            if (res.data === "") {
                return;
            }
            asubTreeData.value = formatData(JSON.parse(res.data));
        }
    })
    .catch((error) => {
        console.log(error);
    });

function checkRelation() {
    return request({
        url: `/api/ScmRelation`,
    });
}

const subjectCount = ref<number>(0);
function subjectCountChange() {
    subjectCount.value++;
}
watch(subjectCount,()=>{
    if(loading.value && subjectCount.value === 18){
        loading.value = false;
    }
})
function getScmSettings() {
    return request({
        url: `/api/ScmSettings?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
    }).then((res: any) => {
        if (res.state === 1000) {
            isDefault.value = res.data.isDefault;
            settings.value = res.data.settings;
            settings.value.System = scmProductType.value
            for (let i in settings.value) {
                if (settings.value[i] === 0 && i !== "VoucherAllowChangeKey" && i !== "BillAllowChangeKey" && i !== "AutoSaveToAccKey") {
                    settings.value[i] = "";
                }
                if (typeof settings.value[i] === "number") {
                    settings.value[i] = settings.value[i].toString();
                }
            }
            settingDirectionShow.value = true;
        } else if (res.state === 2000) {
            ElNotify({
                type: "error",
                message: res.msg,
            });
        } else {
            ElNotify({
                type: "error",
                message: "出现错误，请稍候刷新页面重试",
            });
        }
    });
}
function sumbitSettings() {
    let parmas = _.cloneDeep(settings.value);
    for (let i in parmas) {
        if (parmas[i] === "" && i !== "VoucherAllowChangeKey" && i !== "BillAllowChangeKey" && i !== "AutoSaveToAccKey") {
            parmas[i] = 0;
        }
    }
    request({
        url: `/api/ScmSettings?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
        method: isDefault.value ? "post" : "put",
        data: JSON.stringify(parmas),
        headers: {
            "Content-Type": "application/json",
        },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data === true) {
            isDefault.value = false;
            cancelSettings();
        } else if (res.state === 2000) {
            ElNotify({
                type: "error",
                message: res.msg,
            });
        } else {
            ElNotify({
                type: "error",
                message: "保存失败，请刷新页面后重试",
            });
        }
    });
}
function cancelSettings() {
    closeCurrentTab();
    globalWindowOpenPage("/Scm/ScmRelation", "关联进销存");
}

onMounted(async () => {
    loading.value = true;
    // const checkData = await checkRelation();
    // scmAsid.value = checkData.data.scmAsid;
    // scmProductType.value = checkData.data.scmProductType;
    // await getScmSettings();

    Promise.all([checkRelation()]).then((res: any) => {
        scmAsid.value = res[0].data.scmAsid;
        scmProductType.value = res[0].data.scmProductType;
        getScmSettings();

    });
});

</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
.content {
    .main-content {
        .slot-mini-content {
            width: 1100px;
            .main-block {
                margin-bottom: 5px;
                overflow: hidden;
                & + .main-block {
                    border-top: 1px solid var(--border-color);
                }
                & .block-title {
                    text-align: left;
                    color: var(--font-color);
                    font-size: var(--h3);
                    line-height: 22px;
                    margin-top: 24px;
                    margin-left: 20px;
                    margin-bottom: 16px;
                    font-weight: 600;
                }
                & :deep(.el-row) {
                    & .el-form-item {
                        margin: 0 0px 16px 30px;
                        & .el-form-item__label {
                            padding: 0px;
                        }
                    }
                }
                &.width-block {
                    & :deep(.el-row) {
                        & .el-form-item {
                            width: 490px;
                            margin: 0 0px 16px 30px;
                            & .el-form-item__label {
                                padding: 0px;
                            }
                        }
                    }
                }
            }
            .button-block {
                text-align: center;
                padding: 35px 0px 24px 0px;
            }
            :deep(.el-select-dropdown__item) {
                padding: 6px 6px 6px 8px;
                text-align: left;
            }
            :deep(.asub-img.el-tooltip__trigger) {
                right: 5px !important;
            }
        }
    }
}
</style>
