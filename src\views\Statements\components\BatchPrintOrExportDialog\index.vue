<template>
  <el-dialog v-model="visible" :title="title" center width="590" class="custom-confirm dialogDrag" @closed="handleClose">
    <div class="export-content" v-dialogDrag>
      <div class="export-main">
        <div class="line-item mt-20 mb-20">
          <span class="mr-20">{{ props.type === "print" ? "打印报表：" : "导出报表" }}</span>
          <div class="line-item-right">
            <el-checkbox :checked="dialogParams.statementList.has(1)" @change="onStatementListChange(1)">资产负债表</el-checkbox>
            <el-checkbox
              v-if="[1, 2].includes(accountStandard)"
              :checked="dialogParams.statementList.has(2)"
              @change="onStatementListChange(2)">
              利润表
            </el-checkbox>
            <el-checkbox
              v-if="[3].includes(accountStandard)"
              :checked="dialogParams.statementList.has(9)"
              @change="onStatementListChange(9)">
              业务活动表
            </el-checkbox>
            <el-checkbox
              v-if="[1, 2, 3].includes(accountStandard)"
              :checked="dialogParams.statementList.has(3)"
              @change="onStatementListChange(3)">
              现金流量表
            </el-checkbox>
            <el-checkbox
              v-if="[4, 5].includes(accountStandard)"
              :checked="dialogParams.statementList.has(35)"
              @change="onStatementListChange(35)">
              盈余及盈余分配表
            </el-checkbox>
            <el-checkbox
              v-if="[4, 5].includes(accountStandard)"
              :checked="dialogParams.statementList.has(36)"
              @change="onStatementListChange(36)">
              成员权益变动表
            </el-checkbox>
            <template v-if="[6].includes(accountStandard)">
              <el-checkbox :checked="dialogParams.statementList.has(43)" @change="onStatementListChange(43)">收入支出表</el-checkbox>
              <el-checkbox :checked="dialogParams.statementList.has(44)" @change="onStatementListChange(44)">成本费用表</el-checkbox>
            </template>
            <el-checkbox v-if="7 === accountStandard" :checked="dialogParams.statementList.has(45)" @change="onStatementListChange(45)">
              收益及收益分配表
            </el-checkbox>
          </div>
        </div>
        <div class="line-item mt-20 mb-20">
          <span class="mr-20">期间：</span>
          <div class="line-item-right">
            <PeriodPicker v-model:startPid="dialogParams.startPid" v-model:endPid="dialogParams.endPid" />
          </div>
        </div>
        <div v-if="props.type === 'export'" class="line-item mt-20 mb-20">
          <span class="mr-20">文件类型：</span>
          <div class="line-item-right">
            <el-checkbox v-model="dialogParams.Excel">Excel</el-checkbox>
            <el-checkbox v-model="dialogParams.Pdf">PDF</el-checkbox>
          </div>
        </div>
      </div>
      <div class="buttons a_nofloat">
        <a class="button solid-button mr-20" @click="handleSubmit">{{ dialogType }}</a>
        <a class="button" @click="handleClose">取消</a>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed } from "vue";
import { ElNotify } from "@/util/notify";
import { globalPostPrint, downloadFile } from "@/util/url";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getSwitchState } from "@/views/Statements/utils";
import usePrint, { type PrintInfoType } from "@/hooks/usePrint";

const props = withDefaults(
  defineProps<{
    showDialog: boolean;
    type: "print" | "export";
    // 来自哪个报表  资产负债表  1  利润表  2  现金流量表  3  业务活动表  9  盈余及盈余分配表  35  成员权益变动表  36
    // 收入支出表  43  成本费用表  44  收益及收益分配表  45
    fromStatement: 1 | 2 | 3 | 9 | 35 | 36 | 43 | 44 | 45;
    pid: number;
  }>(),
  {
    showDialog: false,
    type: "print",
    fromStatement: 1,
    pid: -1,
  }
);

//默认打印参数
const { printInfo } = usePrint("", "");
enum printStoreEnum {
  "statement1" = "balanceSheetPrint",
  "statement2" = "incomeSheetPrint",
  "statement3" = "cashFlowStatementPrint",
  "statement9" = "businessActivityStatementPrint",
  "statement35" = "surplusSheetPrint",
  "statement36" = "memberEquitySheetPrint",
  "statement43" = "incomeAndExpenditureStatementPrint",
  "statement44" = "costStatementPrint",
  "statement45" = "incomeDistributionSheetPrint",
}

const emits = defineEmits<{
  (e: "close"): void;
}>();

// 会计准则
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;

const visible = ref(false);
const title = ref("报表批量打印");
const dialogType = computed(() => (props.type === "print" ? "打印" : "导出"));

// 不同会计准则报表列表
let initStatementList = [1, 2, 3];
switch (accountStandard) {
  case 3:
    // 民非  FolkComapnyStandard
    initStatementList = JSON.parse(localStorage.getItem("folkComapnyStandardStatementList") || "[1, 9, 3]");
    break;
  case 4:
  case 5:
    // 农合 FarmerCooperativeStandard  FarmerCooperativeStandard2023
    initStatementList = JSON.parse(localStorage.getItem("farmerCooperativeStandardStatementList") || "[1, 35, 36]");
    break;
  case 6:
    // 工会
    initStatementList = JSON.parse(localStorage.getItem("unionStandardStatementList") || "[1, 43, 44]");
    break;
  case 7:
    // 农村
    initStatementList = JSON.parse(localStorage.getItem("villageCollectiveEconomyStandardStatementList") || "[1, 45]");
    break;
  default:
    // 小企业、企业  LittleCompanyStandard  CompanyStandard
    initStatementList = JSON.parse(localStorage.getItem("companyStandardStatementList") || "[1, 2, 3]");
}

// 批量导出弹窗数据
const dialogParams = reactive({
  startPid: props.pid,
  endPid: props.pid,
  statementList: new Set(initStatementList),
  Excel: JSON.parse(localStorage.getItem("statementExportFileTypeExcel") || "true"),
  Pdf: JSON.parse(localStorage.getItem("statementExportFileTypePdf") || "true"),
});
// 选择报表
const onStatementListChange = (select: number) => {
  if (dialogParams.statementList.has(select)) {
    dialogParams.statementList.delete(select);
  } else {
    dialogParams.statementList.add(select);
  }
};
const handleSubmit = () => {
  if (dialogParams.startPid > dialogParams.endPid) {
    ElNotify({
      message: "亲，开始期间不能大于结束期间哦",
      type: "warning",
    });
    return false;
  }
  if (!Array.from(dialogParams.statementList).length) {
    ElNotify({
      message: `亲，请选择报表进行${dialogType.value}`,
      type: "warning",
    });
    return false;
  }
  // 打印导出公共参数
  // 报表列表
  const statementList = [...dialogParams.statementList].sort((a, b) => a - b);
  const SelectStatement = statementList.join(",");
  function getColumnState() {
    let array = SelectStatement.split(",");
    for (let i = 0; i < array.length; i++) {
      switch (array[i]) {
        case "1": {
          const isShowBeginYearFirst = getSwitchState("isShowBeginYearFirst");
          const isShowBeginYearLast = getSwitchState("isShowBeginYearLast");
          array[i] = array[i] + ":" + (isShowBeginYearFirst ? "0" : "1") + "," + (isShowBeginYearLast === true ? "0" : "1");
          break;
        }
        case "2": {
          array[i] = array[i] + ":" + (getSwitchState("isShowIncome") ? "0" : "1");
          break;
        }
        case "9": {
          array[i] = array[i] + ":" + (getSwitchState("isShowBusiness") ? "0" : "1");
          break;
        }
        case "43": {
          array[i] = array[i] + ":" + (getSwitchState("isShowBudgetMonth") ? "0" : "1");
          break;
        }
        case "35": {
          array[i] = array[i] + ":" + (getSwitchState("isShowSurplus") ? "0" : "1");
          break;
        }
      }
    }
    return array.join("|");
  }

  // 存储上次勾选选项
  switch (accountStandard) {
    case 3:
      // 民非  FolkComapnyStandard
      localStorage.setItem("folkComapnyStandardStatementList", JSON.stringify(statementList));
      break;
    case 4:
    case 5:
      // 农合 FarmerCooperativeStandard  FarmerCooperativeStandard2023
      localStorage.setItem("farmerCooperativeStandardStatementList", JSON.stringify(statementList));
      break;
    case 6:
      // 工会
      localStorage.setItem("unionStandardStatementList", JSON.stringify(statementList));
      break;
    case 7:
      // 农村
      localStorage.setItem("villageCollectiveEconomyStandardStatementList", JSON.stringify(statementList));
      break;
    default:
      // 小企业、企业  LittleCompanyStandard  CompanyStandard
      localStorage.setItem("companyStandardStatementList", JSON.stringify(statementList));
  }
  // 文件类型存储
  localStorage.setItem("statementExportFileTypeExcel", JSON.stringify(dialogParams.Excel));
  localStorage.setItem("statementExportFileTypePdf", JSON.stringify(dialogParams.Pdf));
  const seniorModel: Array<{ statementType: number } & PrintInfoType> = [];
  dialogParams.statementList.forEach((item) => {
    seniorModel.push({
      statementType: item,
      ...JSON.parse(
        localStorage.getItem(printStoreEnum[`statement${item}` as keyof typeof printStoreEnum]) ??
          JSON.stringify(
            usePrint(printStoreEnum[`statement${item}` as keyof typeof printStoreEnum].replace("Print", ""), "").printInfo.value
          )
      ),
    });
  });

  // 参数
  const params = {
    // 来自哪个报表
    FromStatement: props.fromStatement,
    // 资产负债表是否重分类
    IsBalanceClass: [6, 7].includes(accountStandard) ? false : JSON.parse(localStorage.getItem("classificationSwitch") || "false"),
    // // 现金流量表是否重分类
    IsCashFlowClass: JSON.parse(localStorage.getItem("cashclassificationSwitch") || "false"),
    SelectStatement,
    CalMethod: Number(JSON.parse(localStorage.getItem("calmethod") || "1")), //1：公式法，2：辅助核算法
    StartPId: dialogParams.startPid,
    EndPId: dialogParams.endPid,
    Excel: props.type === "print" ? null : dialogParams.Excel,
    Pdf: props.type === "print" ? null : dialogParams.Pdf,
    colunmOrderChange: getColumnState(),
  };

  if (props.type === "print") {
    //批量打印
    globalPostPrint(`/api/Statements/Print`,{...params,seniorModelJson: JSON.stringify(seniorModel)});
  } else {
    // 批量导出
    if (!dialogParams.Excel && !dialogParams.Pdf) {
      ElNotify({
        message: "亲，请选择导出文件类型",
        type: "warning",
      });
      return false;
    }
    if (params.Pdf && !params.Excel) {
      // pdf格式 新窗口打开
      globalPostPrint(`/api/Statements/Export`, { ...params, seniorModelJson: JSON.stringify(seniorModel) });
    } else {
      downloadFile(`/api/Statements/Export`,'post',{...params, seniorModelJson: JSON.stringify(seniorModel)});
    }
  }
};

const handleClose = () => {
  visible.value = false;
  emits("close");
};

// 重置
const handleReset = () => {
  dialogParams.startPid = props.pid;
  dialogParams.endPid = props.pid;
  dialogParams.statementList = new Set(initStatementList);
  dialogParams.Excel = true;
  dialogParams.Pdf = true;
};

watch(
  () => props.type,
  () => {
    title.value = props.type === "print" ? "报表批量打印" : "报表批量导出";
  }
);

watch(
  () => props.showDialog,
  (val) => {
    if (val) {
      // handleReset();
    }
    visible.value = val;
  }
);
</script>
<style lang="less" scoped>
.export-content {
  text-align: center;

  .export-main {
    width: 550px;
    text-align: left;
    display: inline-block;

    .line-item {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 30px;
      padding-top: 10px;

      span {
        width: 70px;
        text-align: right;
      }

      .line-item-right {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 300px;
        height: 36px;
      }

      :deep(.el-checkbox) {
        font-weight: normal;
        margin: 0 5px;
      }
    }
  }

  .buttons {
    text-align: center;
    padding: 10px 0;
    border-top: 1px solid var(--border-color);
  }
}
</style>
