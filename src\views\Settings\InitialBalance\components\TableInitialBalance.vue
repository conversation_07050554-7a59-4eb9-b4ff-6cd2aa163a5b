<template>
  <div
    class="container"
    :class="isErp ? 'erp-container' : ''">
    <!-- gridTr -->
    <Table
      ref="initialBalanceTable"
      v-if="!isShowQut && !(isFCView && currentFcValue != 1)"
      :data="tableData"
      :columns="columns"
      :empty-text="emptyText"
      :initial-balance="true"
      :show-overflow-tooltip="false"
      :scrollbarShow="true"
      :virtualTable="openVirtualTableFlag"
      :minVirtualScrollLines="minVirtualScrollLines"
      @cell-mouse-enter-qichu="cellMouseEnter"
      @cell-mouse-leave="cellMouseLeave"
      :tableName="setModule">
      <template #code>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目编码"
          align="left"
          header-align="left"
          min-width="180"
          prop="code"
          :width="getColumnWidth(setModule, 'code')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubCode }}</span>
          </template>
        </el-table-column>
      </template>
      <template #name>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目名称"
          align="left"
          header-align="left"
          min-width="240"
          prop="name"
          :width="getColumnWidth(setModule, 'name')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubName }}</span>
            <el-popover
              v-if="canAddAssit(scope.row.asubId, scope.row.assistingAccounting, scope.row.asubType)"
              placement="right"
              title='点击 "+"'
              :width="150"
              trigger="hover"
              content="添加辅助明细"
              popper-class="initial-balance-popover"
              :popper-style="
                isErp
                  ? { color: '#333333', backgroundColor: '#fff', borderRadius: '5px' }
                  : { color: '#fff', backgroundColor: '#44b449', borderRadius: '5px' }
              ">
              <template #reference>
                <span
                  class="add-icon"
                  id="add_assit_10004"
                  @mouseenter="displayMessage"
                  @click="editAssistDetailHandle(scope.row.asubId, scope.row.asubCode, scope.row.asubName, scope.row)">
                  +
                </span>
              </template>
            </el-popover>
            <span
              style="cursor: pointer; margin-left: 10px"
              v-if="canDelete(scope.row.asubId, scope.row.assistingAccounting)"
              @click="deleteAssit(scope.row.asubId, scope.row.parentId, scope.row)">
              x
            </span>
          </template>
        </el-table-column>
      </template>
      <template #direction>
        <el-table-column
          label="方向"
          align="left"
          header-align="left"
          min-width="57"
          prop="direction"
          :width="getColumnWidth(setModule, 'direction')">
          <template #default="scope">
            <span>{{ scope.row.direction == "1" ? "借" : "贷" }}</span>
          </template>
        </el-table-column>
      </template>
      <template #initial>
        <el-table-column
          label="期初余额"
          align="right"
          header-align="right"
          min-width="120"
          prop="initial"
          :width="getColumnWidth(setModule, 'initial')">
          <template #default="scope">
            <input
              class="native-input"
              v-model.lazy="scope.row.initial"
              v-if="
                isEditable &&
                props.tabName !== 'accurd' &&
                props.tabName !== 'revenue' &&
                props.tabName !== 'expenses' &&
                scope.row.nonEditableType !== 1 &&
                scope.row.isEditable &&
                checkPermission(['initialbalance1-canedit'])
              "
              :ref="
                (el) => {
                  initialRef[scope.row.asubId] = el;
                }
              "
              @keydown.enter="handleEnter(scope.row.asubId)"
              @focus="nativeFocus"
              @blur="nativeBlur"
              @change="nativeChangeValue(scope.row.initial, scope.row.asubId, 'initial')"
              @input="nativeInput" />
            <span v-else>{{ formatInitialMoney(scope.row.initial) }}</span>
          </template>
        </el-table-column>
      </template>
      <template #debit>
        <el-table-column
          label="借方累计"
          align="right"
          header-align="right"
          min-width="120"
          prop="debit"
          :width="getColumnWidth(setModule, 'debit')">
          <template #default="scope">
            <input
              class="native-input"
              v-model.lazy="scope.row.debit"
              v-if="
                isEditable &&
                !props.isJan &&
                scope.row.nonEditableType !== 1 &&
                scope.row.isEditable &&
                checkPermission(['initialbalance1-canedit'])
              "
              @keydown.enter="handleEnter(scope.row.asubId)"
              @focus="nativeFocus"
              @blur="nativeBlur"
              @change="nativeChangeValue(scope.row.debit, scope.row.asubId, 'debit')"
              @input="nativeInput" />
            <span v-else>{{ formatInitialMoney(scope.row.debit) }}</span>
          </template>
        </el-table-column>
      </template>
      <template #credit>
        <el-table-column
          label="贷方累计"
          align="right"
          header-align="right"
          min-width="120"
          prop="credit"
          :width="getColumnWidth(setModule, 'credit')">
          <template #default="scope">
            <input
              class="native-input"
              v-model.lazy="scope.row.credit"
              v-if="
                isEditable &&
                !props.isJan &&
                scope.row.nonEditableType !== 1 &&
                scope.row.isEditable &&
                checkPermission(['initialbalance1-canedit'])
              "
              @keydown.enter="handleEnter(scope.row.asubId)"
              @focus="nativeFocus"
              @blur="nativeBlur"
              @change="nativeChangeValue(scope.row.credit, scope.row.asubId, 'credit')"
              @input="nativeInput" />
            <span v-else>{{ formatInitialMoney(scope.row.credit) }}</span>
          </template>
        </el-table-column>
      </template>
      <template #total>
        <el-table-column
          label="年初余额"
          align="right"
          header-align="right"
          min-width="120px"
          :resizable="false">
          <template #header>
            <div>
              年初余额
              <img
                class="img-question"
                src="@/assets/Icons/question.png"
                @click="() => (totalTipDialogVisible = true)"
                style="height: 16px; margin-left: 3px" />
            </div>
          </template>
          <template #default="scope">
            <span>{{ formatInitialMoney(scope.row.total) }}</span>
          </template>
        </el-table-column>
      </template>
    </Table>
    <!-- gridTrFc -->
    <Table
      ref="initialBalanceTable"
      v-if="!isShowQut && isFCView && currentFcValue != 1"
      :data="tableData"
      :columns="columns"
      :empty-text="emptyText"
      :initial-balance="true"
      :show-overflow-tooltip="false"
      :scrollbarShow="true"
      :virtualTable="openVirtualTableFlag"
      :minVirtualScrollLines="minVirtualScrollLines"
      @cell-mouse-enter-qichu="cellMouseEnter"
      @cell-mouse-leave="cellMouseLeave"
      :tableName="setModule">
      <template #code>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目编码"
          align="left"
          header-align="left"
          min-width="200"
          prop="code"
          :width="getColumnWidth(setModule, 'code')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubCode }}</span>
          </template>
        </el-table-column>
      </template>
      <template #name>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目名称"
          align="left"
          header-align="left"
          min-width="300"
          prop="name"
          :width="getColumnWidth(setModule, 'name')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubName }}</span>
            <el-popover
              v-if="canAddAssit(scope.row.asubId, scope.row.assistingAccounting, scope.row.asubType)"
              placement="right"
              title='点击 "+"'
              :width="150"
              trigger="hover"
              content="添加辅助明细"
              popper-class="initial-balance-popover"
              :popper-style="
                isErp
                  ? { color: '#333333', backgroundColor: '#fff', borderRadius: '5px' }
                  : { color: '#fff', backgroundColor: '#44b449', borderRadius: '5px' }
              ">
              <template #reference>
                <span
                  class="add-icon"
                  id="add_assit_10004"
                  @mouseenter="displayMessage"
                  @click="editAssistDetailHandle(scope.row.asubId, scope.row.asubCode, scope.row.asubName, scope.row)">
                  +
                </span>
              </template>
            </el-popover>
            <span
              style="cursor: pointer; margin-left: 10px"
              v-if="canDelete(scope.row.asubId, scope.row.assistingAccounting)"
              @click="deleteAssit(scope.row.asubId, scope.row.parentId, scope.row)">
              x
            </span>
          </template>
        </el-table-column>
      </template>
      <template #direction>
        <el-table-column
          label="方向"
          align="left"
          header-align="left"
          min-width="57"
          prop="direction"
          :width="getColumnWidth(setModule, 'direction')">
          <template #default="scope">
            <span>{{ scope.row.direction == "1" ? "借" : "贷" }}</span>
          </template>
        </el-table-column>
      </template>
      <template #initial>
        <el-table-column
          label="期初余额"
          header-align="center">
          <el-table-column
            label="原币"
            align="right"
            header-align="right"
            prop="initialFC"
            :width="getColumnWidth(setModule, 'initialFC')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initialFC"
                v-if="
                  isEditable &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                :ref="
                  (el) => {
                    initialRef[scope.row.asubId] = el;
                  }
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initialFC, scope.row.asubId, 'initialFC')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.initialFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            prop="initialBW"
            :width="getColumnWidth(setModule, 'initialBW')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initial"
                v-if="
                  isEditable &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initial, scope.row.asubId, 'initial')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.initial) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #debit>
        <el-table-column
          label="借方累计"
          header-align="center">
          <el-table-column
            label="原币"
            align="right"
            header-align="right"
            prop="debitFC"
            :width="getColumnWidth(setModule, 'debitFC')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debitFC"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debitFC, scope.row.asubId, 'debitFC')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.debitFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            prop="debitBW"
            :width="getColumnWidth(setModule, 'debitBW')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debit"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debit, scope.row.asubId, 'debit')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.debit) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #credit>
        <el-table-column
          label="贷方累计"
          header-align="center">
          <el-table-column
            label="原币"
            align="right"
            header-align="right"
            prop="creditFC"
            :width="getColumnWidth(setModule, 'creditFC')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.creditFC"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.creditFC, scope.row.asubId, 'creditFC')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.creditFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            prop="creditBW"
            :width="getColumnWidth(setModule, 'creditBW')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.credit"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.credit, scope.row.asubId, 'credit')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.credit) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #total>
        <el-table-column header-align="center">
          <template #header>
            <div>
              年初余额
              <img
                class="img-question"
                src="@/assets/Icons/question.png"
                @click="() => (totalTipDialogVisible = true)"
                style="height: 16px; margin-left: 3px" />
            </div>
          </template>
          <el-table-column
            label="原币"
            align="right"
            header-align="right"
            prop="totalFC"
            :width="getColumnWidth(setModule, 'totalFC')">
            <template #default="scope">
              <span>{{ formatInitialMoney(scope.row.totalFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            :resizable="false">
            <template #default="scope">
              <span>{{ formatInitialMoney(scope.row.total) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </Table>
    <Table
      ref="initialBalanceTable"
      v-if="!isFCView && isShowQut"
      :data="tableData"
      :columns="columns"
      :empty-text="emptyText"
      :initial-balance="true"
      :show-overflow-tooltip="false"
      :scrollbarShow="true"
      :virtualTable="openVirtualTableFlag"
      :minVirtualScrollLines="minVirtualScrollLines"
      @cell-mouse-enter-qichu="cellMouseEnter"
      @cell-mouse-leave="cellMouseLeave"
      :tableName="setModule">
      <template #code>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目编码"
          align="left"
          header-align="left"
          min-width="200"
          prop="code"
          :width="getColumnWidth(setModule, 'code')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>

            <span>{{ scope.row.asubCode }}</span>
          </template>
        </el-table-column>
      </template>
      <template #name>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目名称"
          align="left"
          header-align="left"
          min-width="300"
          prop="name"
          :width="getColumnWidth(setModule, 'name')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubName }}</span>
            <el-popover
              v-if="canAddAssit(scope.row.asubId, scope.row.assistingAccounting, scope.row.asubType)"
              placement="right"
              title='点击 "+"'
              :width="150"
              trigger="hover"
              content="添加辅助明细"
              popper-class="initial-balance-popover"
              :popper-style="
                isErp
                  ? { color: '#333333', backgroundColor: '#fff', borderRadius: '5px' }
                  : { color: '#fff', backgroundColor: '#44b449', borderRadius: '5px' }
              ">
              <template #reference>
                <span
                  class="add-icon"
                  id="add_assit_10004"
                  @mouseenter="displayMessage"
                  @click="editAssistDetailHandle(scope.row.asubId, scope.row.asubCode, scope.row.asubName, scope.row)">
                  +
                </span>
              </template>
            </el-popover>
            <span
              style="cursor: pointer; margin-left: 10px"
              v-if="canDelete(scope.row.asubId, scope.row.assistingAccounting)"
              @click="deleteAssit(scope.row.asubId, scope.row.parentId, scope.row)">
              x
            </span>
          </template>
        </el-table-column>
      </template>
      <template #direction>
        <el-table-column
          label="方向"
          align="left"
          header-align="left"
          min-width="57"
          prop="direction"
          :width="getColumnWidth(setModule, 'direction')">
          <template #default="scope">
            <span>{{ scope.row.direction == "1" ? "借" : "贷" }}</span>
          </template>
        </el-table-column>
      </template>
      <template #initial>
        <el-table-column
          label="期初余额"
          header-align="center">
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="initialQut"
            :width="getColumnWidth(setModule, 'initialQut')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initialQut"
                v-if="
                  isEditable &&
                  scope.row.quantity &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initialQut, scope.row.asubId, 'initialQut')" />
              <span v-else>{{ scope.row.initialQut === 0 ? "" : scope.row.initialQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="金额"
            align="right"
            header-align="right"
            prop="initialAmount"
            :width="getColumnWidth(setModule, 'initialAmount')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initial"
                v-if="
                  isEditable &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                :ref="
                  (el) => {
                    initialRef[scope.row.asubId] = el;
                  }
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initial, scope.row.asubId, 'initial')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.initial) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #debit>
        <el-table-column
          label="借方累计"
          header-align="center">
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="debitQut"
            :width="getColumnWidth(setModule, 'debitQut')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debitQut"
                v-if="
                  isEditable &&
                  scope.row.quantity &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debitQut, scope.row.asubId, 'debitQut')" />
              <span v-else>{{ scope.row.debitQut === 0 ? "" : scope.row.debitQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="金额"
            align="right"
            header-align="right"
            prop="debitAmount"
            :width="getColumnWidth(setModule, 'debitAmount')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debit"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debit, scope.row.asubId, 'debit')" />
              <span v-else>{{ formatInitialMoney(scope.row.debit) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #credit>
        <el-table-column
          label="贷方累计"
          header-align="center">
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="creditQut"
            :width="getColumnWidth(setModule, 'creditQut')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.creditQut"
                v-if="
                  isEditable &&
                  scope.row.quantity &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.creditQut, scope.row.asubId, 'creditQut')" />
              <span v-else>{{ scope.row.creditQut === 0 ? "" : scope.row.creditQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="金额"
            align="right"
            header-align="right"
            prop="creditAmount"
            :width="getColumnWidth(setModule, 'creditAmount')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.credit"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.credit, scope.row.asubId, 'credit')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.credit) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #total>
        <el-table-column header-align="center">
          <template #header>
            <div>
              年初余额
              <img
                class="img-question"
                src="@/assets/Icons/question.png"
                @click="() => (totalTipDialogVisible = true)"
                style="height: 16px; margin-left: 3px" />
            </div>
          </template>
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="totalQut"
            :width="getColumnWidth(setModule, 'totalQut')">
            <template #default="scope">
              <span>{{ scope.row.totalQut === 0 ? "" : scope.row.totalQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="金额"
            align="right"
            header-align="right"
            :resizable="false">
            <template #default="scope">
              <span>{{ formatInitialMoney(scope.row.total) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </Table>
    <Table
      ref="initialBalanceTable"
      v-if="isFCView && isShowQut"
      :data="tableData"
      :columns="columns"
      :empty-text="emptyText"
      :initial-balance="true"
      :show-overflow-tooltip="false"
      :scrollbarShow="true"
      :virtualTable="openVirtualTableFlag"
      :minVirtualScrollLines="minVirtualScrollLines"
      @cell-mouse-enter-qichu="cellMouseEnter"
      @cell-mouse-leave="cellMouseLeave"
      :tableName="setModule">
      <template #code>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目编码"
          align="left"
          header-align="left"
          min-width="200"
          prop="code"
          :width="getColumnWidth(setModule, 'code')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubCode }}</span>
          </template>
        </el-table-column>
      </template>
      <template #name>
        <el-table-column
          label="&nbsp;&nbsp;&nbsp;&nbsp;科目名称"
          align="left"
          header-align="left"
          min-width="230"
          prop="name"
          :width="getColumnWidth(setModule, 'name')">
          <template #default="scope">
            <span v-html="'&nbsp;'.repeat(scope.row.asubLevel * 4)"></span>
            <span>{{ scope.row.asubName }}</span>
            <el-popover
              v-if="canAddAssit(scope.row.asubId, scope.row.assistingAccounting, scope.row.asubType)"
              placement="right"
              title='点击 "+"'
              :width="150"
              trigger="hover"
              content="添加辅助明细"
              popper-class="initial-balance-popover"
              :popper-style="
                isErp
                  ? { color: '#333333', backgroundColor: '#fff', borderRadius: '5px' }
                  : { color: '#fff', backgroundColor: '#44b449', borderRadius: '5px' }
              ">
              <template #reference>
                <span
                  class="add-icon"
                  id="add_assit_10004"
                  @mouseenter="displayMessage"
                  @click="editAssistDetailHandle(scope.row.asubId, scope.row.asubCode, scope.row.asubName, scope.row)">
                  +
                </span>
              </template>
            </el-popover>
            <span
              style="cursor: pointer; margin-left: 10px"
              v-if="canDelete(scope.row.asubId, scope.row.assistingAccounting)"
              @click="deleteAssit(scope.row.asubId, scope.row.parentId, scope.row)">
              x
            </span>
          </template>
        </el-table-column>
      </template>
      <template #direction>
        <el-table-column
          label="方向"
          align="left"
          header-align="left"
          min-width="57">
          <template #default="scope">
            <span>{{ scope.row.direction == "1" ? "借" : "贷" }}</span>
          </template>
        </el-table-column>
      </template>
      <template #initial>
        <el-table-column
          label="期初余额"
          header-align="center">
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="initialQut"
            :width="getColumnWidth(setModule, 'initialQut')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initialQut"
                v-if="
                  isEditable &&
                  scope.row.quantity &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initialQut, scope.row.asubId, 'initialQut')" />
              <span v-else>{{ scope.row.initialQut === 0 ? "" : scope.row.initialQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="inputShowFc"
            label="原币"
            align="right"
            header-align="right"
            prop="initialFC"
            :width="getColumnWidth(setModule, 'initialFC')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initialFC"
                v-if="
                  isEditable &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                :ref="
                  (el) => {
                    initialRef[scope.row.asubId] = el;
                  }
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initialFC, scope.row.asubId, 'initialFC')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.initialFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            prop="initialBW"
            :width="getColumnWidth(setModule, 'initialBW')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.initial"
                v-if="
                  isEditable &&
                  props.tabName !== 'accurd' &&
                  props.tabName !== 'revenue' &&
                  props.tabName !== 'expenses' &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.initial, scope.row.asubId, 'initial')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.initial) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #debit>
        <el-table-column
          label="借方累计"
          header-align="center">
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="debitQut"
            :width="getColumnWidth(setModule, 'debitQut')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debitQut"
                v-if="
                  isEditable &&
                  scope.row.quantity &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debitQut, scope.row.asubId, 'debitQut')" />
              <span v-else>{{ scope.row.debitQut === 0 ? "" : scope.row.debitQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="inputShowFc"
            label="原币"
            align="right"
            header-align="right"
            prop="debitFC"
            :width="getColumnWidth(setModule, 'debitFC')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debitFC"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debitFC, scope.row.asubId, 'debitFC')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.debitFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            prop="debitBW"
            :width="getColumnWidth(setModule, 'debitBW')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.debit"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.debit, scope.row.asubId, 'debit')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.debit) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #credit>
        <el-table-column
          label="贷方累计"
          header-align="center">
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="creditQut"
            :width="getColumnWidth(setModule, 'creditQut')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.creditQut"
                v-if="
                  isEditable &&
                  scope.row.quantity &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.creditQut, scope.row.asubId, 'creditQut')" />
              <span v-else>{{ scope.row.creditQut === 0 ? "" : scope.row.creditQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="inputShowFc"
            label="原币"
            align="right"
            header-align="right"
            prop="creditFC"
            :width="getColumnWidth(setModule, 'creditFC')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.creditFC"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.creditFC, scope.row.asubId, 'creditFC')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.creditFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            prop="creditBW"
            :width="getColumnWidth(setModule, 'creditBW')">
            <template #default="scope">
              <input
                class="native-input"
                v-model.lazy="scope.row.credit"
                v-if="
                  isEditable &&
                  !props.isJan &&
                  scope.row.nonEditableType !== 1 &&
                  scope.row.isEditable &&
                  checkPermission(['initialbalance1-canedit'])
                "
                @keydown.enter="handleEnter(scope.row.asubId)"
                @focus="nativeFocus"
                @blur="nativeBlur"
                @change="nativeChangeValue(scope.row.credit, scope.row.asubId, 'credit')"
                @input="nativeInput" />
              <span v-else>{{ formatInitialMoney(scope.row.credit) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
      <template #total>
        <el-table-column header-align="center">
          <template #header>
            <div>
              年初余额
              <img
                class="img-question"
                src="@/assets/Icons/question.png"
                @click="() => (totalTipDialogVisible = true)"
                style="height: 16px; margin-left: 3px" />
            </div>
          </template>
          <el-table-column
            v-if="!props.isShowQuantity"
            label="数量"
            align="right"
            header-align="right"
            prop="totalQut"
            :width="getColumnWidth(setModule, 'totalQut')">
            <template #default="scope">
              <span>{{ scope.row.totalQut === 0 ? "" : scope.row.totalQut }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="inputShowFc"
            label="原币"
            align="right"
            header-align="right"
            prop="totalFC"
            :width="getColumnWidth(setModule, 'totalFC')">
            <template #default="scope">
              <span>{{ formatInitialMoney(scope.row.totalFC) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="本位币"
            align="right"
            header-align="right"
            :resizable="false">
            <template #default="scope">
              <span>{{ formatInitialMoney(scope.row.total) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </template>
    </Table>
    <p
      v-if="tipNonEditableMessage1"
      class="cell-dialog"
      :style="{ top: cellTop, left: cellLeft }">
      非末级科目，不需要录入期初
    </p>
    <p
      v-if="tipNonEditableMessage2"
      class="cell-dialog"
      :style="{ top: cellTop, left: cellLeft }">
      该科目已启用辅助核算，点击科目名称旁边的加号添加辅助核算后录入期初
    </p>
    <p
      v-if="tipNonEditableMessage3"
      class="cell-dialog"
      :style="{ top: cellTop, left: cellLeft }">
      该账套有资产数据且已折旧，如需修改资产科目期初，可取消所有期间折旧（或到设置-账套-编辑中清空资产历史数据）
    </p>
    <p
      v-if="tipNonEditableMessage4"
      class="cell-dialog"
      :style="{ top: cellTop, left: cellLeft }">
      该账套有资产数据且已折旧，如需修改资产科目期初，可取消所有期间折旧
    </p>
  </div>
  <el-dialog
    v-model="totalTipDialogVisible"
    title="年初余额"
    center
    width="440"
    class="dialogDrag">
    <div
      class="box-body"
      style="border-bottom: 1px solid var(--border-color)"
      v-dialogDrag>
      <div class="body-message">
        <div class="body-message-title">
          <div style="text-align: left">
            <div style="margin: 5px -20px">年初余额不用手工录入</div>
            <div style="margin: 5px -20px">1.如果是年初启用的账套，年初余额就是期初余额</div>
            <div style="margin: 5px -20px">
              2.如果是年中启用的账套，年初余额由系统倒算出来，倒算公式如下：
              <br />
              借方科目：年初数 = 期初数 + 本年累计贷方 - 本年累计借方
              <br />
              贷方科目：年初数 = 期初数 + 本年累计借方 - 本年累计贷方
            </div>
          </div>
        </div>
        <div class="body-message-content content-none"></div>
      </div>
    </div>
    <div
      class="box-footer button-ok"
      :class="isErp ? 'erp-box-footer' : ''">
      <a
        class="button solid-button"
        @click="() => (totalTipDialogVisible = false)">
        确定
      </a>
    </div>
  </el-dialog>
  <el-dialog
    v-model="editAssistDetailDialog"
    title="增加明细"
    class="custom-confirm dialogDrag"
    center
    width="658"
    :destroy-on-close="true">
    <div
      class="add-assist-detail-content"
      v-dialogDrag>
      <div class="add-assist-detail-top">
        <div class="add-assist-detail-top-info">
          <Tooltip
            :content="asubName"
            :max-width="264"
            :font-size="16"
            :line-clamp="1">
            科目：{{ asubName }}
          </Tooltip>
        </div>
        <div style="font-size: 0">
          <a
            class="button solid-button ml-10 mr-10"
            @click="submitAssistDetail">
            保存
          </a>
          <a
            v-if="haveEditDialogPermission"
            class="button mr-10"
            id="btnAssistBatchAdd"
            @click="batchAddAssit">
            批量新增
          </a>
          <a
            v-if="haveDeleteDialogPermission"
            class="button"
            @click="deleteRows">
            删除
          </a>
        </div>
      </div>
      <div class="add-assist-detail-main">
        <div
          class="add-line"
          @mouseenter="addSubShow = true"
          @mouseleave="addSubShow = false"
          :style="{ top: addSubTop + 'px' }"
          v-if="addSubShow"
          @click="handleTableInsert">
          <div
            class="add-line-icon"
            :class="{ erp: isErp }"></div>
        </div>
        <div
          class="subtract-line"
          @mouseenter="addSubShow = true"
          @mouseleave="addSubShow = false"
          :style="{ top: addSubTop + 'px' }"
          @click="handleTableSubtract"
          v-if="addSubShow">
          <div
            class="subtract-line-icon"
            :class="{ erp: isErp }"></div>
        </div>
        <el-table
          ref="assistDetailRef"
          :data="assistDetailTable"
          border
          scrollbar-always-on
          @selection-change="assistDetailSelection"
          @row-click="clickAssistName"
          @cell-mouse-enter="addSubCellMouseEnter">
          <el-table-column
            type="selection"
            width="28" />
          <el-table-column
            label="序号"
            type="index"
            :width="basicValue" />
          <el-table-column
            v-for="(item, index) in assitTypesForDetail"
            :key="item.type"
            :label="item.name"
            :width="2 * basicValue">
            <template #default="scope">
              <div
                class="supplierList"
                v-if="assistDetailTable[scope.$index].assistDetail[0].unfold && haveEditDialogPermission">
                <Select
                  ref="assistDetailSelect"
                  style="width: 100%"
                  placeholder="请选择"
                  v-model="assistDetailTable[scope.$index].assistDetail[index].aaeid"
                  :fitInputWidth="true"
                  :teleported="false"
                  :filterable="true"
                  :clearable="true"
                  :bottom-html="item.type !== 10007 ? newAAHtml : ''"
                  :unfold="index == 0 && scope.$index === assistDetailTable.length - 2"
                  @change="handleAssistChange(scope.$index, index, item.type)"
                  @visible-change="handleVisibleChange(scope.$index, index)"
                  @update:model-value="(val) => changeAssistValue(val, item.type, index, scope.$index)"
                  @bottom-click="clickAddDepartment(item.type, item.name, scope.$index, index, scope.row)"
                  @keydown.delete="assistDetailTable[scope.$index].assistDetail[index].aaeid = ''"
                  :filter-method="(query: string) => assistFilterMethod(query, item.type)">
                  <Option
                    v-for="op in showAssistByAsubId[item.type]"
                    :key="op.label"
                    :value="op.value"
                    :label="op.label" />
                </Select>
              </div>
              <p
                class="assist-item"
                :title="assistDetailTable[scope.$index].assistDetail[index]?.aaname"
                v-else>
                {{ assistDetailTable[scope.$index].assistDetail[index]?.aaname }}
              </p>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    v-model="editAssitDialog"
    title="增加明细"
    center
    width="688"
    class="custom-confirm dialogDrag">
    <div
      class="edit-assit-content"
      v-dialogDrag>
      <div class="edit-assit-main">
        <div class="input mt-10 mb-10">
          <span>科目：</span>
          <!-- <span>{{ asubNameForAssistCombo }}</span> -->
          <Tooltip
            :content="asubName"
            :max-width="240"
            :font-size="16"
            :line-clamp="1">
            <input
              id="asubNameForAssistCombo"
              type="text"
              disabled
              value="材料采购"
              class="asubNameForAssistCombo" />
          </Tooltip>
        </div>
        <div
          id="ComboTitle"
          class="txt"
          style="">
          设置辅助核算组合方式：
        </div>
        <el-tabs
          v-model="assistActiveName"
          @tab-click="handleAssisTabClick">
          <el-tab-pane
            v-for="item in assitTypesForDetail"
            :key="item.type"
            :label="item.name"
            :name="item.name"></el-tab-pane>
        </el-tabs>
        <Table
          ref="TableCom"
          :data="editAssitData"
          :columns="editAssitColumns"
          :empty-text="assistEmptyText"
          :scrollbarShow="true"
          @selection-change="assistSelection"
          @row-click="selectToggle"
          style="height: 251px; overflow: auto; padding-top: 10px"></Table>

        <div
          id="assitEmptyInfo"
          class="assit-empty-info"></div>
      </div>
      <div
        class="buttons"
        style="text-align: center; padding: 10px 0; border-top: 1px solid var(--border-color)">
        <a
          class="button ml-10"
          id="assitCancelBtn"
          @click="() => (editAssitDialog = false)">
          取消
        </a>
        <a
          class="button solid-button ml-10"
          id="assitOkBtn"
          @click="editAssitHandle">
          确定
        </a>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    v-model="confirmAssistDialog"
    title="确认辅助核算组合项"
    center
    width="658"
    class="custom-confirm dialogDrag">
    <div
      class="confirm-assit-combo-content"
      v-dialogDrag>
      <div
        class="confirm-assit-combo-main"
        style="min-height: 420px; padding: 10px">
        <el-table
          :data="confirmAssitDataList"
          border
          :scrollbar-always-on="true">
          <el-table-column
            prop="code"
            label="编码"
            width="520"
            :resizable="false">
            <template #default="scope">
              <p
                class="assist-item"
                :title="scope.row.code">
                {{ scope.row.code }}
              </p>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="116"
            align="left"
            header-align="left"
            :resizable="false">
            <template #default="scope">
              <a
                class="link"
                @click="removeRow(scope.row.code)">
                删除
              </a>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div
        class="buttons"
        style="text-align: center; border-top: 1px solid var(--border-color)">
        <a
          @click="CloseConfirmAssistCombo"
          class="button ml-10">
          取消
        </a>
        <a
          @click="SubmitAssistCombo"
          class="button solid-button">
          确定
        </a>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    v-model="supplierAddVisible"
    :title="'新增' + aaAddTitle"
    center
    width="438"
    class="custom-confirm dialogDrag">
    <div
      class="divAddAA"
      v-dialogDrag>
      <table
        cellpadding="0"
        cellspacing="0">
        <tbody>
          <tr>
            <td id="tdAddNumInfo">{{ aaAddTitle + "编号：" }}</td>
            <td>
              <el-input
                v-model="supplierAddForm.supplierCode"
                style="width: 180px"></el-input>
            </td>
          </tr>
          <tr>
            <td id="tdAddNameInfo">{{ aaAddTitle + "名称：" }}</td>
            <td>
              <el-input
                v-model="supplierAddForm.supplierName"
                style="width: 180px"
                @input="limitname"></el-input>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div
      class="buttons"
      style="text-align: center; border-top: 1px solid var(--border-color); margin-top: 11px">
      <a
        @click="CancelAAItem"
        class="button ml-10">
        取消
      </a>
      <a
        class="button solid-button ml-10"
        @click="saveAssistAccount">
        保存
      </a>
    </div>
  </el-dialog>
  <AddAssistingAccountingEntryDialog
    :title="AddAssistingAccountingTitle"
    ref="addAssistingAccountingEntryDialogRef"
    @save-success="erpAAESaveSuccess"></AddAssistingAccountingEntryDialog>
</template>

<script setup lang="ts">
  import { ref, computed, inject, nextTick, onMounted, watch, watchEffect } from "vue";
  import Big from "big.js";
  import Table from "@/components/Table/VirtualTable.vue";
  import Select from "@/components/Select/index.vue";
  import Tooltip from "@/components/Tooltip/index.vue";
  import Option from "@/components/Option/index.vue";
  import type { IColumnProps } from "@/components/Table/IColumnProps";
  import type {
    IStandardTabs,
    IAssistDetail,
    IStrObject,
    INumObject,
    IEntryPair,
    IAssistTypeForDetail,
    ISupplierList,
    IconfirmAssitData,
    IAssistingAccountingList,
    ISelection,
    ITypeName,
    IEditAssistInfo,
    IEditAssistLine,
  } from "../types";
  import { useAccountSetStoreHook } from "@/store/modules/accountSet";
  import {
    formatInitialMoney,
    isNumberOr_Letter,
    aaCheckQuote,
    formatOnlyNum,
    unformat,
    unformat8,
    isRightNum,
    isUnBalance,
    isUnBalance8,
    quantityFormat,
    amountFormat,
    parseFormattedNumber,
    isStringNaN,
    bigPlus,
    bigMinus,
    bigPlusMinus,
  } from "../utils";
  import { request, type IResponseModel } from "@/util/service";
  import { ElNotify } from "@/util/notify";
  import { ElConfirm } from "@/util/confirm";
  import { getGlobalLodash } from "@/util/lodash";
  import { useLoading } from "@/hooks/useLoading";
  import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
  import { checkPermission } from "@/util/permission";
  import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
  import { getColumnWidth } from "@/components/ColumnSet/utils";
  import { commonFilterMethod } from "@/components/Select/utils";

  const setModule = "InitialBalance";
  const _ = getGlobalLodash();

  const openVirtualTableFlag = true;
  const minVirtualScrollLines = 2000;
  const initialBalanceTable = ref<InstanceType<typeof Table>>();
  const isErp = ref(window.isErp);
  const tabs = inject("tabs");
  const props = defineProps<{
    isFCView: boolean;
    isShowFC: boolean;
    isShowQut: boolean;
    inputShowFc: boolean;
    isJan: boolean;
    isEditable: boolean;
    currentFcValue: number | undefined;
    currentRate: number;
    fcid: any;
    tabName: string;
    data: any;
    loading: boolean;
    isShowQuantity?: boolean;
    tabsType?: number;
  }>();
  const isShowQut = ref<boolean>(props.isShowQut);
  const isFCView = ref<boolean>(props.isFCView);
  const currentFcValue = ref<number | undefined>(props.currentFcValue);
  const assistEmptyText = ref<string>(" ");
  const updateTableView = () => {
    nextTick().then(() => {
      initialBalanceTable.value?.updateView();
    });
  };
  watch(
    [() => props.isShowQuantity, () => props.isShowQut, () => props.isShowQut, () => props.isFCView, () => props.currentFcValue],
    () => {
      isShowQut.value = props.isShowQut;
      if (props.isShowQut) {
        isShowQut.value = !props.isShowQuantity;
      }
      isFCView.value = props.isFCView;
      currentFcValue.value = props.currentFcValue;
      updateTableView();
    },
    { immediate: true }
  );

  const emits = defineEmits<{
    // (e: "update:tableData", info: any): void;
    (e: "getTableData", height: number): void;
  }>();
  const emptyText = ref(" ");
  const asubNameForAssistCombo = ref("");
  const addSubShow = ref<boolean>(false);
  const addSubTop = ref<number>(0);
  const assitTypesForDetail = ref<IAssistTypeForDetail[]>([]);
  const assistActiveName = ref<string>("");
  const supplierSeletorShow = ref<boolean>(false);
  const supplierAddVisible = ref<boolean>(false);
  const supplierAddForm = ref({
    supplierCode: "",
    supplierName: "",
  });
  interface IPrevCodeData {
    [key: number]: string;
  }
  let prevCodeData: IPrevCodeData = {};
  const tableData = ref(props.data);

  let tableDataMid = _.cloneDeep(tableData.value);
  watch(
    () => props.data,
    (data) => {
      tableData.value = data;
      tableDataMid = _.cloneDeep(tableData.value);
      updateTableView();
    },
    { immediate: true }
  );
  watch(
    [() => props.data, () => props.loading],
    () => {
      if (props.data.length > 0 && !props.loading) {
        emptyText.value = "";
      } else if (props.data.length == 0 && !props.loading) {
        emptyText.value = "暂无数据";
      }
    },
    { immediate: true }
  );
  const totalTipDialogVisible = ref<boolean>(false);
  const editAssistDetailDialog = ref<boolean>(false);
  const editAssitDialog = ref<boolean>(false);
  const confirmAssistDialog = ref<boolean>(false);
  const columns = ref<Array<IColumnProps>>([
    { slot: "code" },
    { slot: "name" },
    { slot: "direction" },
    { slot: "initial" },
    { slot: "debit" },
    { slot: "credit" },
    { slot: "total" },
  ]);

//   const supplierList = ref<Array<ISupplierList>>([]);
  const asubId = ref<string>("");
  const asubCode = ref<string>("");
  const asubName = ref<string>("");
  const assistDetailIndex = ref<string[]>([]);
  const editAssitData = ref<Array<ITypeName>>([]);
  const editAssitColumns = ref<Array<IColumnProps>>([
    { slot: "selection" },
    { prop: "aanum", label: "编码", minWidth: 150, align: "left", headerAlign: "left" },
    { prop: "aaname", label: "名称", minWidth: 440, align: "left", headerAlign: "left", resizable: false },
  ]);
  const confirmAssitData = ref<Array<IconfirmAssitData>>([]);
  const confirmAssitDataList = ref<Array<IconfirmAssitData>>([]);

  const tipNonEditableMessage1 = ref<boolean>();
  const tipNonEditableMessage2 = ref<boolean>();
  const tipNonEditableMessage3 = ref<boolean>();
  const tipNonEditableMessage4 = ref<boolean>();
  function displayMessage() {
    tipNonEditableMessage1.value = false;
    tipNonEditableMessage2.value = false;
    tipNonEditableMessage3.value = false;
    tipNonEditableMessage4.value = false;
  }
  let newAAHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
    <a class="link">
        +点击添加
    </a>
</div>`;
  let hasAccountPermission = useAccountSetStoreHook().permissions.includes("assistingaccount-canedit");
  if (!hasAccountPermission) newAAHtml = "";

  const TableCom = ref<any>();

  const assistDetailRef = ref();

  const cellTop = ref<string>("0");
  const cellLeft = ref<string>("0");
  const tableRightPosition = ref<number>(0);
  const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();

  onMounted(() => {
    let contentWidth = document.querySelector(".content")?.clientWidth || 0;
    let tableWidth = document.querySelector(".main-content")?.clientWidth || 0;
    tableRightPosition.value = (contentWidth + tableWidth) / 2;
  });
  // 单元格鼠标移入事件
  const cellMouseEnter = (index: number, top: number, left: number) => {
    tipNonEditableMessage1.value = false;
    tipNonEditableMessage2.value = false;
    tipNonEditableMessage3.value = false;
    tipNonEditableMessage4.value = false;
    cellTop.value = top + "px";
    if (!tableData.value[index].isEditable) {
      if (tableData.value[index].nonEditableType === 1) {
        tipNonEditableMessage1.value = true;
      } else if (tableData.value[index].nonEditableType === 2) {
        tipNonEditableMessage2.value = true;
      } else if (tableData.value[index].nonEditableType === 3) {
        if (window.isErp) {
          tipNonEditableMessage4.value = true;
        } else {
          tipNonEditableMessage3.value = true;
        }
      }
    }
    nextTick(() => {
      // 获取提示信息元素
      const tipMessage = document.querySelector(".cell-dialog");
      // 获取表格容器元素
      const tableContainer = initialBalanceTable.value?.$el.querySelector(".el-table__body-wrapper");
      // 获取表格容器的宽度和左偏移量
      const tableContainerWidth = tableContainer.offsetWidth;
      const tableContainerLeft = tableContainer.getBoundingClientRect().left;
      const width = (tipMessage as HTMLElement)?.offsetWidth || 0;
      // 计算气泡显示位置
      if (left + width + 12 > tableContainerWidth + tableContainerLeft) {
        // tipMessage.style.top = `${location.y}px`;
        cellLeft.value = `${left - width}px`;
      } else {
        // tipMessage.style.top = `${location.y}px`;
        cellLeft.value = `${left + 2}px`;
      }
    });
  };
  const cellMouseLeave = () => {
    tipNonEditableMessage1.value = false;
    tipNonEditableMessage2.value = false;
    tipNonEditableMessage3.value = false;
    tipNonEditableMessage4.value = false;
  };
  let currentIndex: number;
  const addSubCellMouseEnter = (row: any, column: number, cell: any, event: any) => {
    const tableOffset = assistDetailRef.value?.$el.getBoundingClientRect();
    const rowOffset = event.currentTarget.getBoundingClientRect();
    const offsetY = rowOffset.top - tableOffset.top;
    // 当前行高度
    const cellHeight = event.currentTarget.offsetHeight;
    // 当前数据在数组中的位置index,
    currentIndex = assistDetailTable.value.indexOf(row);
    let inputindex = assistDetailTable.value.findIndex((item) => item.assistDetail[0].unfold === true);
    // 增加和删除按钮
    addSubTop.value = offsetY + 98 + cellHeight / 2;
    addSubShow.value = true;
  };

  // 插入新的行
  const handleTableInsert = () => {
    // 关闭其他行
    assistDetailTable.value.forEach((item: any, index) => {
      item.assistDetail.forEach((project: any) => {
        // if(project.unfold){
        //     project.unfold = false;
        //     // assistDetailTable
        // }
        project.unfold = false;
      });
    });

    assistDetailTable.value.splice(currentIndex, 0, {
      assistDetail: new Array(assitTypesForDetail.value.length).fill(null).map(() => ({
        aaeid: "",
        aaname: "",
        unfold: false,
        aanum: "",
        aatype: 0,
      })),
    });

    addSubShow.value = false;
  };

  const haveEditDialogPermission = computed(() => {
    return checkPermission(["initialbalance1-canedit"]);
  });
  const haveDeleteDialogPermission = computed(() => {
    return checkPermission(["initialbalance1-candelete"]);
  });

  const handleTableAdd = () => {
    if (haveEditDialogPermission.value) {
      assistDetailTable.value.push({
        assistDetail: new Array(assitTypesForDetail.value.length).fill(null).map(() => ({
          aaeid: "",
          aaname: "",
          unfold: false,
          aanum: "",
          aatype: 0,
        })),
      });
    }
  };

  // // 清除空白行
  const clarAssistDetailTable = () => {
    for (let i in assistDetailTable.value) {
      if (assistDetailTable.value[i].assistDetail.every((item) => item.aaeid === "")) {
        assistDetailTable.value.splice(~~i, 1);
      }
    }
  };

  const handleTableSubtract = () => {
    //如果删除了最后一行，手动补充一行
    if (currentIndex === assistDetailTable.value.length - 1) {
      assistDetailTable.value.splice(currentIndex, 1);
      assistDetailTable.value.splice(currentIndex, 1);
      assistDetailTable.value.push({
        assistDetail: new Array(assitTypesForDetail.value.length).fill(null).map(() => ({
          aaeid: "",
          aaname: "",
          unfold: false,
          aanum: "",
          aatype: 0,
        })),
      });
    } else {
      assistDetailTable.value.splice(currentIndex, 1);
    }
    addSubShow.value = false;
  };

  function getTotalValue(selfv: any, field: string, updatevalue: string) {
    let value: number | string = 0;
    if (selfv.direction == 2) {
      switch (field) {
        case "initial":
          value = bigPlusMinus(unformat(updatevalue), unformat(selfv.debit), unformat(selfv.credit));
          break;
        case "credit":
          value = bigPlusMinus(unformat(selfv.initial), unformat(selfv.debit), unformat(updatevalue));
          break;
        case "debit":
          value = bigPlusMinus(unformat(selfv.initial), unformat(updatevalue), unformat(selfv.credit));
          break;
        case "initialQut":
          value = unformat8(updatevalue) - unformat8(selfv.creditQut) + unformat8(selfv.debitQut);
          break;
        case "creditQut":
          value = unformat8(selfv.initialQut) - unformat8(updatevalue) + unformat8(selfv.debitQut);
          break;
        case "debitQut":
          value = unformat8(selfv.initialQut) - unformat8(selfv.creditQut) + unformat8(updatevalue);
          break;
        case "initialFC":
          value = bigPlusMinus(unformat(updatevalue), unformat(selfv.debitFC), unformat(selfv.creditFC));
          break;
        case "creditFC":
          value = bigPlusMinus(unformat(selfv.initialFC), unformat(selfv.debitFC), unformat(updatevalue));
          break;
        case "debitFC":
          value = bigPlusMinus(unformat(selfv.initialFC), unformat(updatevalue), unformat(selfv.creditFC));
          break;
      }
    } else {
      switch (field) {
        case "initial":
          value = bigPlusMinus(unformat(updatevalue), unformat(selfv.credit), unformat(selfv.debit));
          break;
        case "credit":
          value = bigPlusMinus(unformat(selfv.initial), unformat(updatevalue), unformat(selfv.debit));
          break;
        case "debit":
          value = bigPlusMinus(unformat(selfv.initial), unformat(selfv.credit), unformat(updatevalue));
          break;
        case "initialQut":
          value = unformat8(updatevalue) + unformat8(selfv.creditQut) - unformat8(selfv.debitQut);
          break;
        case "creditQut":
          value = unformat8(selfv.initialQut) + unformat8(updatevalue) - unformat8(selfv.debitQut);
          break;
        case "debitQut":
          value = unformat8(selfv.initialQut) + unformat8(selfv.creditQut) - unformat8(updatevalue);
          break;
        case "initialFC":
          value = bigPlusMinus(unformat(updatevalue), unformat(selfv.creditFC), unformat(selfv.debitFC));
          break;
        case "creditFC":
          value = bigPlusMinus(unformat(selfv.initialFC), unformat(updatevalue), unformat(selfv.debitFC));
          break;
        case "debitFC":
          value = bigPlusMinus(unformat(selfv.initialFC), unformat(selfv.creditFC), unformat(updatevalue));
          break;
      }
    }
    return value;
  }

  function refreshParent(ibr: any, errcallback?: Function) {
    let subValue = {};
    let allParents: any[] = [];
    allParents = getAllParents(allParents, subValue, ibr) as any[];
    for (let i = 0; i < allParents.length; i++) {
      let onvalue = allParents[i];
      onvalue.oldValue.isNewFcRow = false;
      onvalue.oldValue.creditQut = onvalue.newValue.creditQut;
      onvalue.oldValue.debitQut = onvalue.newValue.debitQut;
      onvalue.oldValue.initialQut = onvalue.newValue.initialQut;
      onvalue.oldValue.totalQut = onvalue.newValue.totalQut;
      onvalue.oldValue.initialFC = onvalue.newValue.initialFC;
      onvalue.oldValue.debitFC = onvalue.newValue.debitFC;
      onvalue.oldValue.creditFC = onvalue.newValue.creditFC;
      onvalue.oldValue.totalFC = onvalue.newValue.totalFC;
      onvalue.oldValue.credit = onvalue.newValue.credit;
      onvalue.oldValue.debit = onvalue.newValue.debit;
      onvalue.oldValue.initial = onvalue.newValue.initial;
      onvalue.oldValue.total = onvalue.newValue.total;
    }
  }

  function getAllParents(parents: any[], subvalues: any, ibr: any) {
    if (ibr.parentId == 0) {
      return parents;
    } else {
      let ibrmy;
      let updateIbrow: any = {};
      if (parents.length == 0) {
        let values = getAllChildValue(ibr.parentId, props.tabName, true);
        if (values.r) {
          ibrmy = getMyself(ibr.parentId);
          subvalues.direction = ibrmy.direction;
          // var updateIbrow = new InitialBalanceRowModel(ibrmy);
          updateIbrow.fcid = props.isShowFC ? props.currentFcValue : 1;
          updateIbrow.creditQut = quantityFormat(values.cq);
          subvalues.creditQut = quantityFormat(bigMinus(values.cq, ibrmy.creditQut));
          updateIbrow.debitQut = quantityFormat(values.dq);
          subvalues.debitQut = quantityFormat(bigMinus(values.dq, ibrmy.debitQut));
          updateIbrow.initialQut = quantityFormat(values.iq);
          subvalues.initialQut = quantityFormat(bigMinus(values.iq, ibrmy.initialQut));
          updateIbrow.totalQut = quantityFormat(values.tq);
          subvalues.totalQut = quantityFormat(bigMinus(values.tq, ibrmy.totalQut));
          updateIbrow.initialFC = amountFormat(values.ifc);
          subvalues.initialFC = amountFormat(bigMinus(values.ifc, ibrmy.initialFC));
          updateIbrow.debitFC = amountFormat(values.dfc);
          subvalues.debitFC = amountFormat(bigMinus(values.dfc, ibrmy.debitFC));
          updateIbrow.creditFC = amountFormat(values.cfc);
          subvalues.creditFC = amountFormat(bigMinus(values.cfc, ibrmy.creditFC));
          updateIbrow.totalFC = amountFormat(values.tfc);
          subvalues.totalFC = amountFormat(bigMinus(values.tfc, ibrmy.totalFC));
          updateIbrow.credit = amountFormat(values.c);
          subvalues.credit = amountFormat(bigMinus(values.c, ibrmy.credit));
          updateIbrow.debit = amountFormat(values.d);
          subvalues.debit = amountFormat(bigMinus(values.d, ibrmy.debit));
          updateIbrow.initial = amountFormat(values.i);
          subvalues.initial = amountFormat(bigMinus(values.i, ibrmy.initial));
          updateIbrow.total = amountFormat(values.t);
          subvalues.total = amountFormat(bigMinus(values.t, ibrmy.total));
          parents.push({ newValue: updateIbrow, oldValue: ibrmy });
          getAllParents(parents, subvalues, ibrmy);
        } else {
          return parents;
        }
      } else {
        ibrmy = getMyself(ibr.parentId);
        //   var updateIbrow = new InitialBalanceRowModel(ibrmy);
        updateIbrow.fcid = props.isShowFC ? props.currentFcValue : 1;
        updateIbrow.creditQut = quantityFormat(bigPlus(ibrmy.creditQut, subvalues.creditQut));
        updateIbrow.debitQut = quantityFormat(bigPlus(ibrmy.debitQut, subvalues.debitQut));
        updateIbrow.debitFC = amountFormat(bigPlus(ibrmy.debitFC, subvalues.debitFC));
        updateIbrow.creditFC = amountFormat(bigPlus(ibrmy.creditFC, subvalues.creditFC));
        updateIbrow.credit = amountFormat(bigPlus(ibrmy.credit, subvalues.credit));
        updateIbrow.debit = amountFormat(bigPlus(ibrmy.debit, subvalues.debit));
        if (ibrmy.direction === subvalues.direction) {
          updateIbrow.initialQut = quantityFormat(bigPlus(ibrmy.initialQut, subvalues.initialQut));
          updateIbrow.totalQut = quantityFormat(bigPlus(ibrmy.totalQut, subvalues.totalQut));
          updateIbrow.initialFC = amountFormat(bigPlus(ibrmy.initialFC, subvalues.initialFC));
          updateIbrow.totalFC = bigPlus(ibrmy.totalFC, subvalues.totalFC);
          updateIbrow.initial = amountFormat(bigPlus(ibrmy.initial, subvalues.initial));
          updateIbrow.total = bigPlus(ibrmy.total, subvalues.total);
        } else {
          updateIbrow.initialQut = quantityFormat(bigMinus(ibrmy.initialQut, subvalues.initialQut));
          updateIbrow.totalQut = quantityFormat(bigMinus(ibrmy.totalQut, subvalues.totalQut));
          updateIbrow.initialFC = amountFormat(bigMinus(ibrmy.initialFC, subvalues.initialFC));
          updateIbrow.totalFC = amountFormat(bigMinus(ibrmy.totalFC, subvalues.totalFC));
          updateIbrow.initial = amountFormat(bigMinus(ibrmy.initial, subvalues.initial));
          updateIbrow.total = amountFormat(bigMinus(ibrmy.total, subvalues.total));
        }
        parents.push({ newValue: updateIbrow, oldValue: ibrmy });
        getAllParents(parents, subvalues, ibrmy);
      }
      return parents;
    }
  }
  function saveChageValueToFCDatabase(data: any, isNewFc: boolean, callback: Function, errCallback: Function) {
    //var _self = this;
    //if (_self.islock) {
    //    errCallback();
    //    return;
    //}
    //   this.islock = true;
    //   var type = isNewFc ? InitialMethodType.AddFC : InitialMethodType.UpdateFC;
    request({
      url: "/api/InitialBalance/GenerlLedgeFC",
      method: isNewFc ? "post" : "put",
      data,
    }).then((res: any) => {
      if (res.data) {
        callback();
        tableDataMid = _.cloneDeep(tableData.value);
        updateTableView();
      } else {
        if (typeof errCallback == "function") {
          errCallback();
        }
        if (!res.data) {
          if (res.msg == "HasInvoicing") {
            ElNotify({
              type: "warning",
              message: "资产已结账，不能修改期初数据！",
            });
            return;
          }
          if (res.msg == "AssistingError") {
            ElNotify({
              type: "warning",
              message: "科目信息已经变更，请刷新页面后再输入",
            });
            return;
          }
          ElNotify({
            type: "warning",
            message: res.msg,
          });
          return;
        } else {
          ElNotify({
            type: "warning",
            message: "保存失败了！,请刷新页面后重试",
          });
        }
      }
    });
  }
  function saveChageValue2FCAADatabase(data: any, isNewFc: boolean, callback: Function, errCallback: Function) {
    //var _self = this;
    //if (_self.islock) {
    //    errCallback();
    //    return;
    //}
    //     var InitialMethodType = {
    //     AddFC: 9,
    //     UpdateFC: 10,
    //     AddFCAA: 11,
    //     UpdateFCAA: 12,
    //     DeleteAssit: 13,
    // }
    request({
      url: isNewFc ? "/api/InitialBalance/GenerlLedgeFC" : "/api/InitialBalance/GenerlLedgeAafc",
      method: isNewFc ? "post" : "put",
      data,
    }).then((res: any) => {
      if (res.data) {
        callback();
        tableDataMid = _.cloneDeep(tableData.value);
        updateTableView();
      } else {
        if (typeof errCallback == "function") {
          errCallback();
        }
        if (!res.data) {
          ElNotify({
            type: "warning",
            message: res.msg,
          });
        } else {
          ElNotify({
            type: "warning",
            message: "保存失败了！,请刷新页面后重试",
          });
        }
      }
    });
  }
  //更新损益类总帐值
  function saveProlostValue2Database(data: any, v: any, callback: Function, rollback: Function, isFcValue: boolean) {
    if (isAssit(data.asubId)) {
      let ids = data.asubId.split("-");
      let subData: any = {};
      subData.AsubId = ids[1];
      subData.AAcode = ids.splice(2).join("-");
      subData.Fcid = props.isShowFC ? props.currentFcValue : 1;
      subData.Credit = isFcValue ? new Big(v).times(new Big(props.currentRate)).toString() : v;
      subData.LastCredit = data.credit == null ? 0 : data.credit;
      subData.Debit = subData.Credit;
      subData.LastDebit = data.debit == null ? 0 : data.debit;
      if (isFcValue) {
        subData.CreditFC = v;
        subData.LastCreditFC = data.creditFC;
        subData.DebitFC = v;
        subData.LastDebitFC = data.debitFC;
      }
      saveChageValue2FCAADatabase(subData, false, callback, rollback);
    } else {
      let submitData: any = {};
      submitData.AsubId = data.asubId;
      submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
      submitData.Credit = isFcValue ? new Big(v).times(new Big(props.currentRate)).toString() : v;

      submitData.LastCredit = data.credit == null ? 0 : data.credit;
      submitData.Debit = submitData.Credit;
      submitData.LastDebit = data.debit == null ? 0 : data.debit;
      if (isFcValue) {
        submitData.CreditFC = v;
        submitData.LastCreditFC = data.creditFC;
        submitData.DebitFC = v;
        submitData.LastDebitFC = data.debitFC;
      }
      saveChageValueToFCDatabase(submitData, data.isNewFcRow, callback, rollback);
    }
  }

  const initialRef = ref<any>([]);

  const nativeFocus = (e: any) => {
    e.target.classList.add("is-focus");
  };
  const nativeBlur = (e: any) => {
    e.target.classList.remove("is-focus");
  };
  const handleEnter = (id: string) => {
    for (let i = Number(id) + 1, len = initialRef.value.length; i <= len; i++) {
      if (i === len) {
        ElNotify({
          type: "warning",
          message: "亲，已经到底了！",
        });
        break;
      }
      if (initialRef.value[i] && initialRef.value[i].focus) {
        initialRef.value[i].focus();
        break;
      }
    }
  };

  const nativeInput = (e: Event) => {
    let value = (e!.target as HTMLInputElement).value;

    if (
      (value.includes(".") && value.split(".")[0].replace(/,/g, "").length > 13) ||
      (!value.includes(".") && value.replace(/,/g, "").length > 13)
    ) {
      ElNotify({
        message: "金额不能超出13位",
        type: "warning",
      });
    }
  };

  const nativeChangeValue = (value: string, id: string, type: string) => {
    changeValue(value, id, type);
  };
  const changeValue = (value: string, id: string, type: string) => {
    const data: any = tableDataMid.find((item: any) => item.asubId === id);
    let oldVal: any = data[type];
    // 当前输入行
    const changeDate = tableData.value.find((item: any) => item.asubId == id);
    // !oldVal ||
    if (oldVal == "0" && (value === "0" || !formatOnlyNum(value))) {
      ElConfirm("期初数据为0，不需要输入哦！", true);
      changeDate[type] = "";
      return;
    }
    if (isStringNaN(value) && value.trim() !== "") {
      changeDate[type] = "";
      return;
    }
    if (value.trim() === "") {
      value = "0";
    }
    let differenceValue = Number(value) - Number(oldVal);
    let requestData;
    let v: any;
    let old: any;
    let success: Function;
    let successFC: Function;
    let rollback: Function;
    let err: Function;
    switch (type) {
      case "initial":
        //存在编辑其他类，但是数据为此不可编辑大类情况
        if ([6, 8, 9].includes(changeDate.asubType)) return;
        v = unformat(value).toString();
        if (isRightNum(v)) {
          changeDate.initial = formatInitialMoney(oldVal);
          return;
        }
        success = function () {
          changeDate.initial = amountFormat(v);
          changeDate.total = getTotalValue(changeDate, type, v);
          refreshParent(changeDate, function () {});
        };
        successFC = function () {
          changeDate.isNewFcRow = false;
          success();
        };
        rollback = function () {
          changeDate.initial = oldVal;
        };
        if (isUnBalance(v, parseFormattedNumber(oldVal))) {
          let ids = changeDate.asubId.split("-");
          // var submitData = new IBRowSubmitData
          let submitData: any = {};
          submitData.AsubId = changeDate.asubId;
          submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
          submitData.Initial = v;
          submitData.LastInitial = data.initial == null || data.intial == "" ? 0 : data.initial;
          submitData.Total = getTotalValue(changeDate, type, v);
          submitData.LastTotal = data.total == null ? 0 : data.total;
          if (submitData.Fcid == 1) {
            submitData.InitialFC = v;
            submitData.LastInitialFC = data.initial == null || data.intial == "" ? 0 : data.initial;
            submitData.TotalFC = submitData.Total;
            submitData.LastTotalFC = data.total;
          }
          if ((ids.length === 1 && changeDate.rowType == 2) || (changeDate.rowType == 0 && submitData.Fcid > 0)) {
            saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, successFC, rollback);
          }
          if (ids.length >= 3 || changeDate.rowType == 3 || (changeDate.rowType == 1 && submitData.Fcid > 0)) {
            submitData.AsubId = ids[1];
            submitData.AAcode = ids.splice(2).join("-");
            saveChageValue2FCAADatabase(submitData, false, successFC, rollback);
          }
        } else {
          changeDate.initial = amountFormat(v);
          // self.showTip(v);
          // self.Initial.notifySubscribers(v);
        }
        break;
      case "initialFC":
        v = unformat(value).toString();
        // var oldif = self.InitialFC();
        // var oldi = self.Initial();
        // var oldt = self.Total();
        if (isRightNum(v)) {
          changeDate.initialFC = formatInitialMoney(oldVal);
          return;
        }
        if (isRightNum(new Big(v).times(new Big(props.currentRate)).toString())) {
          changeDate.initialFC = formatInitialMoney(oldVal);
          return;
        }
        success = function () {
          changeDate.isNewFcRow = false;
          changeDate.initialFC = amountFormat(v);
          changeDate.initial = formatOnlyNum(new Big(v).times(new Big(props.currentRate)).toString());
          changeDate.totalFC = getTotalValue(changeDate, type, v);
          changeDate.total = getTotalValue(changeDate, "initial", new Big(v).times(new Big(props.currentRate)).toString());
          // self.InitialFC(v);
          // self.Initial(v * tvm.fccurrentRate());
          // self.TotalFC(self.getTotalValue("InitialFC", v));
          // self.Total(self.getTotalValue("Initial", v * tvm.fccurrentRate()));

          refreshParent(changeDate, function () {
            // self.InitialFCTxt(oldif);
            // self.InitialTxt(oldi);
          });
        };
        rollback = function () {
          changeDate.initialFC = oldVal;
        };

        if (isUnBalance(parseFormattedNumber(oldVal), v)) {
          let submitData: any = {};
          submitData.AsubId = changeDate.asubId;
          submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
          submitData.InitialFC = v;
          submitData.LastInitialFC = data.initialFC == null ? 0 : data.initialFC;
          submitData.Initial = new Big(v).times(new Big(props.currentRate)).toString();
          submitData.LastInitial = data.initial == null ? 0 : data.initial;
          submitData.Total = getTotalValue(changeDate, "initial", submitData.Initial);
          submitData.LastTotal = data.total == null ? 0 : data.total;
          submitData.TotalFC = getTotalValue(changeDate, "initialFC", v);
          submitData.LastTotalFC = data.totalFC == null ? 0 : data.totalFC;

          let ids = changeDate.asubId.split("-");
          if (ids.length >= 3 || changeDate.rowType == 3 || (changeDate.rowType == 0 && submitData.Fcid > 0)) {
            submitData.AsubId = ids[1];
            submitData.AAcode = ids.splice(2).join("-");
            saveChageValue2FCAADatabase(submitData, false, success, rollback);
          }
          if ((ids.length === 1 && changeDate.rowType == 2) || (changeDate.rowType == 0 && submitData.Fcid > 0)) {
            saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, success, rollback);
          }
        } else {
          changeDate.initialFC = amountFormat(v);
          // self.showTip(v);
          // rollback();
        }
        break;
      case "initialQut":
        v = unformat8(value);
        // var oldiq = self.InitialQut();
        if (isRightNum(v.toString())) {
          changeDate.InitialQut = oldVal;
          return;
        }
        success = function () {
          changeDate.isNewFcRow = false;
          changeDate.initialQut = quantityFormat(v);
          changeDate.totalQut = quantityFormat(getTotalValue(changeDate, type, v));
          // self.IsNewFcRow(false);
          // self.InitialQut(v);
          // self.TotalQut(self.getTotalValue("InitialQut", v));
          refreshParent(changeDate, function () {
            // self.InitialQutTxt(oldiq);
          });
        };
        err = function () {
          changeDate.InitialQut = oldVal;
        };

        if (isUnBalance8(v, Number(parseFormattedNumber(oldVal)))) {
          let ids = changeDate.asubId.split("-");
          let submitData: any = {};
          submitData.AsubId = changeDate.asubId;
          submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
          submitData.InitialQut = v;
          submitData.LastInitialQut = data.initialQut == null ? 0 : data.initialQut;
          submitData.TotalQut = getTotalValue(changeDate, type, v);
          submitData.LastTotalQut = data.totalQut == null ? 0 : data.totalQut;
          if ((ids.length === 1 && changeDate.rowType == 2) || (changeDate.rowType == 0 && submitData.Fcid > 0)) {
            saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, success, err);
          }
          if (ids.length >= 3 || changeDate.rowType == 3 || (changeDate.rowType == 0 && submitData.Fcid > 0)) {
            submitData.AsubId = ids[1];
            submitData.AAcode = ids.splice(2).join("-");
            saveChageValue2FCAADatabase(submitData, false, success, err);
          }
        } else {
          // self.showTip(v);
          // self.InitialQut.notifySubscribers(v);
        }
        break;
      case "credit":
        v = unformat(value).toString();
        // var oldc = self.Credit();
        if (isRightNum(v)) {
          changeDate.credit = formatInitialMoney(oldVal);
          return;
        }
        success = function () {
          changeDate.credit = amountFormat(v);
          changeDate.total = getTotalValue(changeDate, type, v);
          refreshParent(changeDate, function () {
            // self.CreditTxt(oldc);
          });
          // self.Credit(v);
          // self.Total(self.getTotalValue("Credit", v));
          // self.refreshParent(self, function () {
          //     self.CreditTxt(oldc);
          // });
        };
        successPv = function () {
          changeDate.debit = formatOnlyNum(v);
          success();
        };
        successFC = function () {
          changeDate.isNewFcRow = false;
          success();
        };
        rollback = function () {
          changeDate.credit = oldVal;
        };

        if (isUnBalance(v, parseFormattedNumber(oldVal))) {
          if (changeDate.asubType == 6 || changeDate.asubType == 8 || changeDate.asubType == 9) {
            saveProlostValue2Database(changeDate, v, successPv, rollback, false);
          } else {
            var ids = changeDate.asubId.split("-");
            var submitData: any = {};
            submitData.AsubId = changeDate.asubId;
            submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
            submitData.Credit = v;
            if (submitData.Fcid > 1) {
              submitData.CreditFC = changeDate.creditFC == null ? 0 : changeDate.creditFC;
              submitData.LastCreditFC = data.creditFC == null ? 0 : data.creditFC;
            } else {
              submitData.CreditFC = v;
              submitData.LastCreditFC = data.credit;
            }
            submitData.LastCredit = data.credit == null ? 0 : data.credit;
            submitData.Total = getTotalValue(changeDate, type, v);
            submitData.LastTotal = data.total == null ? 0 : data.total;

            if ((ids.length === 1 && changeDate.rowType == 2) || (submitData.Fcid > 0 && changeDate.rowType == 0)) {
              saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, successFC, rollback);
            }
            if (ids.length >= 3 || changeDate.rowType == 3 || (submitData.Fcid > 0 && changeDate.rowType == 1)) {
              submitData.AAcode = ids.splice(2).join("-");
              submitData.AsubId = ids[1];
              saveChageValue2FCAADatabase(submitData, false, successFC, rollback);
            }
          }
        } else {
          changeDate.credit = amountFormat(v);
          // self.showTip(v);
          // self.Credit.notifySubscribers(self.Credit());
        }
        break;
      case "creditFC":
        v = unformat(value).toString();
        // var oldcf = self.CreditFC();
        // var oldc = self.Credit();
        if (isRightNum(v)) {
          changeDate.creditFC = formatInitialMoney(oldVal);
          return;
        }
        if (isRightNum(new Big(v).times(new Big(props.currentRate)).toString())) {
          changeDate.creditFC = formatInitialMoney(oldVal);
          return;
        }
        success = function () {
          changeDate.credit = amountFormat(new Big(v).times(new Big(props.currentRate)).toString());
          changeDate.total = getTotalValue(changeDate, "credit", new Big(v).times(new Big(props.currentRate)).toString());
          // self.Credit(v * tvm.fccurrentRate());
          // self.Total(self.getTotalValue("Credit", v * tvm.fccurrentRate()));
          refreshParent(changeDate, function () {
            // self.CreditFCTxt(oldcf);
            // self.CreditTxt(oldc);
          });
        };

        successPv = function () {
          changeDate.isNewFcRow = false;
          changeDate.debitFC = formatOnlyNum(v);
          changeDate.debit = formatOnlyNum(new Big(v).times(new Big(props.currentRate)).toString());
          // self.IsNewFcRow(false);
          // self.DebitFC(v);
          // self.Debit(v * tvm.fccurrentRate());
          successFC();
        };

        successFC = function () {
          changeDate.isNewFcRow = false;
          changeDate.creditFC = formatOnlyNum(v);
          changeDate.totalFC = getTotalValue(changeDate, type, v);
          // self.CreditFC(v);
          // self.TotalFC(self.getTotalValue("CreditFC", v));
          success();
        };

        rollback = function () {
          changeDate.creditFC = oldVal;
        };

        if (isUnBalance(v, parseFormattedNumber(oldVal))) {
          if (changeDate.asubType == 6 || changeDate.asubType == 8 || changeDate.asubType == 9) {
            saveProlostValue2Database(changeDate, v, successPv, rollback, true);
          } else {
            let submitData: any = {};
            submitData.AsubId = changeDate.asubId;
            submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
            submitData.CreditFC = v;
            submitData.LastCreditFC = data.creditFC == null ? 0 : data.creditFC;
            submitData.Credit = new Big(v).times(new Big(props.currentRate)).toString();
            submitData.LastCredit = data.credit == null ? 0 : data.credit;
            submitData.Total = getTotalValue(changeDate, "credit", submitData.Credit);
            submitData.LastTotal = data.total == null ? 0 : data.total;
            submitData.TotalFC = getTotalValue(changeDate, "creditFC", v);
            submitData.LastTotalFC = data.totalFC == null ? 0 : data.totalFC;
            let ids = changeDate.asubId.toString().split("-");
            if (ids.length >= 3 || changeDate.rowType == 3) {
              submitData.AAcode = ids.splice(2).join("-");
              submitData.AsubId = ids[1];
              saveChageValue2FCAADatabase(submitData, false, successFC, rollback);
            }
            if (ids.length === 1 && changeDate.rowType == 2) {
              saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, successFC, rollback);
            }
          }
        } else {
          changeDate.creditFC = formatOnlyNum(v);
          // self.showTip(v);
          // self.CreditFC.notifySubscribers(self.CreditFC());
        }
        break;
      case "creditQut":
        v = unformat8(value);
        // var oldq = self.DebitQut();
        if (isRightNum(v)) {
          changeDate.creditQut = oldVal;
          return;
        }
        success = function () {
          changeDate.isNewFcRow = false;
          changeDate.creditQut = quantityFormat(v);
          changeDate.totalQut = quantityFormat(getTotalValue(changeDate, type, v));
          refreshParent(changeDate, function () {
            // self.CreditQutTxt(oldq);
          });
        };
        successFC = function () {
          changeDate.isNewFcRow = false;
          success();
        };
        err = function () {
          changeDate.creditQut = oldVal;
        };

        if (isUnBalance8(v, Number(parseFormattedNumber(oldVal)))) {
          let ids = changeDate.asubId.split("-");
          let submitData: any = {};
          submitData.AsubId = changeDate.asubId;
          submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
          submitData.CreditQut = v;
          submitData.LastCreditQut = data.creditQut == null ? 0 : data.creditQut;
          submitData.TotalQut = getTotalValue(changeDate, type, v);
          submitData.LastTotalQut = data.totalQut == null ? 0 : data.totalQut;
          if ((ids.length === 1 && changeDate.rowType == 2) || (submitData.Fcid > 0 && changeDate.rowType == 0)) {
            saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, successFC, err);
          }
          if (ids.length >= 3 || changeDate.rowType == 3 || (submitData.Fcid > 0 && changeDate.rowType == 1)) {
            submitData.AsubId = ids[1];
            submitData.AAcode = ids.splice(2).join("-");
            saveChageValue2FCAADatabase(submitData, false, successFC, err);
          }
        } else {
          // self.showTip(v);
          // self.CreditQut.notifySubscribers(self.CreditQut());
        }
        break;
      case "debit":
        v = unformat(value).toString();
        // var oldd = self.Debit();
        if (isRightNum(v)) {
          changeDate.debit = formatInitialMoney(oldVal);
          return;
        }
        success = function () {
          // self.Debit(v);
          changeDate.debit = amountFormat(v);
          changeDate.total = getTotalValue(changeDate, type, v);
          // self.Total(self.getTotalValue("Debit", v));
          refreshParent(changeDate, function () {
            // self.DebitTxt(oldd);
          });
          // self.refreshParent(self, function () {
          //     self.DebitTxt(oldd);
          // });
        };
        var successPv = function () {
          changeDate.credit = formatOnlyNum(v);
          // self.Credit(v);
          success();
        };
        successFC = function () {
          changeDate.isNewFcRow = false;
          success();
        };
        rollback = function () {
          changeDate.debit = oldVal;
          // self.Debit.notifySubscribers(self.Debit());
        };
        if (isUnBalance(v, parseFormattedNumber(oldVal))) {
          if (changeDate.asubType == 6 || changeDate.asubType == 8 || changeDate.asubType == 9) {
            saveProlostValue2Database(changeDate, v, successPv, rollback, false);
          } else {
            let ids = changeDate.asubId.split("-");
            let submitData: any = {};
            submitData.AsubId = changeDate.asubId;
            submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
            submitData.Debit = v;
            submitData.LastDebit = changeDate.debit == null ? 0 : changeDate.debit;
            if (submitData.Fcid > 1) {
              submitData.DebitFC = changeDate.debitFC == null ? 0 : changeDate.debitFC;
              submitData.LastDebitFC = data.debitFC == null ? 0 : data.debitFC;
            } else {
              submitData.DebitFC = v;
              submitData.LastDebitFC = data.debit;
            }
            submitData.Total = getTotalValue(changeDate, type, v);
            submitData.LastTotal = data.total;
            if ((ids.length === 1 && changeDate.rowType == 2) || (submitData.Fcid > 0 && changeDate.rowType == 0)) {
              saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, successFC, rollback);
            }
            if (ids.length >= 3 || changeDate.rowType == 3 || (submitData.Fcid > 0 && changeDate.rowType == 1)) {
              submitData.AsubId = ids[1];
              submitData.AAcode = ids.splice(2).join("-");
              saveChageValue2FCAADatabase(submitData, false, successFC, rollback);
            }
          }
        } else {
          changeDate.debit = amountFormat(v);
          // self.showTip(v);
          // rollback();
        }
        break;
      case "debitFC":
        v = unformat(value).toString();
        // var olddf = self.DebitFC();
        // var oldd = self.Debit();
        if (isRightNum(v)) {
          changeDate.debitFC = formatInitialMoney(oldVal);
          return;
        }
        if (isRightNum(new Big(v).times(new Big(props.currentRate)).toString())) {
          changeDate.debitFC = formatInitialMoney(oldVal);
          return;
        }
        successPv = function () {
          changeDate.creditFC = amountFormat(v);
          changeDate.credit = formatOnlyNum(new Big(v).times(new Big(props.currentRate)).toString());
          // self.CreditFC(v);
          // self.Credit(v * tvm.fccurrentRate());
          success();
        };

        success = function () {
          changeDate.isNewFcRow = false;
          changeDate.debitFC = formatOnlyNum(v);
          changeDate.debit = formatOnlyNum(new Big(v).times(new Big(props.currentRate)).toString());

          changeDate.totalFC = getTotalValue(changeDate, type, v);
          changeDate.total = getTotalValue(changeDate, "debit", new Big(v).times(new Big(props.currentRate)).toString());
          // self.IsNewFcRow(false);
          // self.DebitFC(v);
          // self.Debit(v * tvm.fccurrentRate());
          // self.TotalFC(self.getTotalValue("DebitFC", v));
          // self.Total(self.getTotalValue("Debit", v * tvm.fccurrentRate()));
          refreshParent(changeDate, function () {
            // self.DebitFCTxt(olddf);
            // self.DebitTxt(oldd);
          });
        };
        rollback = function () {
          changeDate.debitFC = oldVal;
        };
        if (isUnBalance(v, parseFormattedNumber(oldVal))) {
          if (changeDate.asubType == 6 || changeDate.asubType == 8 || changeDate.asubType == 9) {
            saveProlostValue2Database(changeDate, v, successPv, rollback, true);
          } else {
            let submitData: any = {};
            submitData.AsubId = changeDate.asubId;
            submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
            submitData.DebitFC = v;
            submitData.LastDebitFC = data.debitFC == null ? 0 : data.debitFC;
            submitData.Debit = new Big(v).times(new Big(props.currentRate)).toString();
            submitData.LastDebit = data.debit == null ? 0 : data.debit;
            submitData.Total = getTotalValue(changeDate, "debit", new Big(v).times(new Big(props.currentRate)).toString());
            submitData.LastTotal = data.total;
            submitData.TotalFC = getTotalValue(changeDate, "debitFC", v);
            submitData.LastTotalFC = data.totalFC;

            let ids = changeDate.asubId.split("-");
            if (ids.length >= 3 || changeDate.rowType == 3) {
              submitData.AsubId = ids[1];
              submitData.AAcode = ids.splice(2).join("-");
              saveChageValue2FCAADatabase(submitData, false, success, rollback);
            }
            if (ids.length === 1 && changeDate.rowType == 2) {
              saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, success, rollback);
            }
          }
        } else {
          changeDate.debitFC = formatOnlyNum(v);
          // self.showTip(v);
          // self.DebitFC.notifySubscribers(self.DebitFC());
        }
        break;
      case "debitQut":
        v = unformat8(value);
        // var olddq = self.DebitQut();
        if (isRightNum(v)) {
          changeDate.debitQut = oldVal;
          return;
        }
        success = function () {
          changeDate.debitQut = quantityFormat(v);
          changeDate.totalQut = quantityFormat(getTotalValue(changeDate, type, v));
          refreshParent(changeDate, function () {
            // self.DebitQutTxt(olddq);
          });
        };
        successFC = function () {
          changeDate.isNewFcRow = false;
          success();
        };
        err = function () {
          changeDate.debitQut = oldVal;
        };
        if (isUnBalance8(v, Number(parseFormattedNumber(oldVal)))) {
          let ids = changeDate.asubId.split("-");
          let submitData: any = {};
          submitData.AsubId = changeDate.asubId;
          submitData.Fcid = props.isShowFC ? props.currentFcValue : 1;
          submitData.DebitQut = v;
          submitData.LastDebitQut = data.debitQut == null ? 0 : data.debitQut;
          submitData.TotalQut = getTotalValue(changeDate, type, v);
          submitData.LastTotalQut = data.totalQut == null ? 0 : data.totalQut;
          if ((ids.length === 1 && changeDate.rowType == 2) || (changeDate.rowType == 0 && submitData.Fcid > 0)) {
            saveChageValueToFCDatabase(submitData, changeDate.isNewFcRow, successFC, err);
          }
          if (ids.length >= 3 || changeDate.rowType == 3 || (changeDate.rowType == 1 && submitData.Fcid > 0)) {
            submitData.AsubId = ids[1];
            submitData.AAcode = ids.splice(2).join("-");
            saveChageValue2FCAADatabase(submitData, false, successFC, err);
          }
        } else {
          // self.showTip(v);
          // self.DebitQut.notifySubscribers(v);
        }
        break;
    }
  };

  let clickColIndex = 0;
  let clickRowIndex = 0;
  function changeAssistValue(val: string, aaType: number, col: number, row: number) {
    // supplierList.value = assistByAsubId.value[aaType];
    const foundItem = assistData.value.find((item2: ITypeName) => item2.aaeid === Number(val));
    const changeFoundItem = assistByAsubId.value[aaType].find((item) => item.value === val);
    assistDetailTable.value[row].assistDetail[col].aaname = foundItem?.aaname || changeFoundItem?.label || "";
    assistDetailTable.value[row].assistDetail[col].aanum = foundItem?.aanum || "";
  }

  const clickAAType = ref<number>();
  const aaAddTitle = ref<string>("");
  const basicValue = ref<number>(0);

  // 选择某行
  const assistDetailSelect = ref();
  function clickAssistName(row: any, column?: any) {
    if (column && column.type === "selection") return;

    // 关闭其他行
    assistDetailTable.value.forEach((item: any) => {
      item.assistDetail.forEach((project: any) => {
        project.unfold = false;
      });
    });

    let index = assistDetailTable.value.indexOf(row);
    if (index === assistDetailTable.value.length - 1) {
      handleTableAdd();
    }
    const clickItem = assistDetailTable.value[index].assistDetail[0];
    clickItem.unfold = true;
    nextTick().then(() => {
      assistDetailTable.value[index].assistDetail.forEach((item, index) => {
        if (item.aatype !== 0 && !assistByAsubId.value[item.aatype]?.some((assist) => assist.value === item.aaeid)) {
          const selectRef = assistDetailSelect.value[index]?.getSelect();
          const el = selectRef?.$el;
          if (el) {
            const input = el.querySelector("input") as HTMLInputElement;
            input && (input.value = item.aaname);
          }
        }
      });
    });
  }
  function handleAssistChange(rowIndex: number, index: number, type: number) {
    const { aatype } = assistDetailTable.value[rowIndex].assistDetail[index];
    type !== aatype && (assistDetailTable.value[rowIndex].assistDetail[index].aatype = type);
  }
  function handleVisibleChange(rowIndex: number, index: number) {
    const { aaeid, aatype, aaname } = assistDetailTable.value[rowIndex].assistDetail[index];
    const selectRef = assistDetailSelect.value[index]?.getSelect();
    const el = selectRef?.$el;
    if (el && aatype !== 0 && !assistByAsubId.value[aatype]?.some((assist) => assist.value === aaeid)) {
      const input = el.querySelector("input") as HTMLInputElement;
      nextTick().then(() => {
        input.value = aaname;
        input.placeholder = aaname;
      });
    }
  }
  const AddAssistingAccountingTitle = ref("添加辅助核算项目");
  function clickAddDepartment(aaType: number, aaName: string, rowIndex: number, colIndex: number, row: any) {
    clickAAType.value = aaType;
    !assistByAsubId.value[aaType] && (assistByAsubId.value[aaType] = []);
    // supplierList.value = assistByAsubId.value[aaType];
    clickRowIndex = rowIndex;
    clickColIndex = colIndex;

    if (window.isErp && (aaType === 10001 || aaType === 10002 || aaType === 10003 || aaType === 10004 || aaType === 10006)) {
      addAssistingAccountingEntryDialogRef.value?.showAADialog(aaType, autoAddName.value);
      addAssistingAccountingEntryDialogRef.value?.setDialogWidth(aaType === 10001 || aaType === 10002 || aaType === 10006 ? 740 : 440);
      switch (aaType) {
        case 10001:
          AddAssistingAccountingTitle.value = "新增客户";
          break;
        case 10002:
          AddAssistingAccountingTitle.value = "新增供应商";
          break;
        case 10003:
          AddAssistingAccountingTitle.value = "新增职员";
          break;
        case 10004:
          AddAssistingAccountingTitle.value = "新增部门";
          break;
        case 10006:
          AddAssistingAccountingTitle.value = "新增商品";
          break;
        default:
          AddAssistingAccountingTitle.value = "添加辅助核算项目";
          break;
      }
    } else {
      aaAddTitle.value = aaName;
      supplierSeletorShow.value = true;
      limitname(autoAddName.value);
      request({
        url: `/api/AssistingAccounting/GetNewAANum?aaType=${aaType}&categoryId=0`,
        method: "post",
      }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
          supplierAddForm.value.supplierCode = res.data + "";
          supplierAddVisible.value = true;
          clickAssistName(row);
        }
      });
    }
  }

  function limitname(value: string) {
    if (value.length > 256) {
      ElNotify({
        type: "warning",
        message: "亲，" + aaAddTitle.value + "不能超过256个字符！",
      });
    }
    value = value.slice(0, 256);
    supplierAddForm.value.supplierName = value;
  }
  // 新增部门保存
  function saveAssistAccount() {
    if (supplierAddForm.value.supplierCode === "") {
      ElNotify({
        type: "warning",
        message: "请输入编码！",
      });
      return false;
    }
    if (!isNumberOr_Letter(supplierAddForm.value.supplierCode) || supplierAddForm.value.supplierCode.length > 18) {
      ElNotify({
        type: "warning",
        message: "编码为不超过5位的字符或数字组合！",
      });
      return false;
    }
    if (supplierAddForm.value.supplierName === "") {
      ElNotify({
        type: "warning",
        message: "请输入名称！",
      });
      return false;
    }
    if (supplierAddForm.value.supplierName !== "") {
      if (aaCheckQuote(supplierAddForm.value.supplierName)) {
        ElNotify({
          type: "warning",
          message: "亲,你输入的不能包含\\:'\"等特殊字符串！",
        });
        return false;
      }
    }
    // if (supplierAddForm.supplierName !== "") {
    //     if (supplierAddForm.supplierName.length > 64) {
    //         ElNotify({
    //             type: "warning",
    //             message: aaAddTitle.value+"不能超过64个字符，请修改后重试哦~",
    //         });
    //         return false;
    //     }
    // }
    const data = {
      aaeId: 0,
      AaNum: supplierAddForm.value.supplierCode,
      AaName: supplierAddForm.value.supplierName,
    };
    request({
      url: `/api/AssistingAccounting/Check?aaType=${clickAAType.value}`,
      data,
      method: "post",
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000 && res.data === "N") {
        saveAs();
      } else if (res.data == "Name") {
        ElConfirm("亲，此名称已存在，是否继续保存？").then((r: boolean) => {
          if (r) saveAs();
        });
      } else {
        ElNotify({
          type: "warning",
          message: "部门编号重复，请重新编号！",
        });
      }
    });
  }
  function saveAs() {
    let data = {
      aaType: clickAAType.value,
      aaNum: supplierAddForm.value.supplierCode,
      aaName: supplierAddForm.value.supplierName,
    };
    request({
      url: `/api/AssistingAccounting/QuickCreate`,
      method: "post",
      data,
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000 && res.data) {
        if (clickAAType.value !== undefined) {
          !assistByAsubId.value[clickAAType.value] && (assistByAsubId.value[clickAAType.value] = []);
          assistByAsubId.value[clickAAType.value].push({
          label: supplierAddForm.value.supplierName,
          value: res.data,
          })
        }
        assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].aaname = supplierAddForm.value.supplierName;
        assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].aanum = supplierAddForm.value.supplierCode;
        assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].unfold = false;
        nextTick(() => {
          assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].aaeid = res.data;
        });
        supplierAddVisible.value = false;
        ElNotify({
          type: "success",
          message: "亲，保存成功啦！",
        });
      } else {
        ElNotify({
          type: "error",
          message: "亲，保存失败啦！",
        });
      }
    });
  }

  function erpAAESaveSuccess(data: any) {
    let code = "";
    let name = "";
    let id = 0;
    switch (clickAAType.value) {
      case 10001:
        code = data.custCode;
        id = data.aaeId;
        name = data.custName;
        break;
      case 10002:
        code = data.vendCode;
        id = data.aaeId;
        name = data.vendName;
        break;
      case 10003:
        code = data.employeeCode;
        id = data.aaeId;
        name = data.employeeName;
        break;
      case 10004:
        code = data.departmentCode;
        id = data.aaeId;
        name = data.departmentName;
        break;
      case 10006:
        code = data.stockCode;
        id = data.aaeId;
        name = data.stockName;
        break;
    }
    //业财迁移去掉父级
    getTypeName(asubId.value);
    if (clickAAType.value !== undefined) {
        !assistByAsubId.value[clickAAType.value] && (assistByAsubId.value[clickAAType.value] = []);
        assistByAsubId.value[clickAAType.value].push({
            label: name,
            value: String(id),
        })
    }
    assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].aaname = name;
    assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].aanum = code;
    assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].unfold = false;
    assistDetailTable.value[clickRowIndex].assistDetail[clickColIndex].aaeid = id + "";
  }

  watch(supplierAddVisible, (newVal) => {
    if (!newVal) {
      supplierAddForm.value.supplierCode = "";
      supplierAddForm.value.supplierName = "";
    }
  });
  function CancelAAItem() {
    supplierAddVisible.value = false;
    supplierAddForm.value.supplierCode = "";
    supplierAddForm.value.supplierName = "";
  }

  function isAssit(asubid: string) {
    return asubid.split("-").length > 1;
  }

  function isLeaf(id: string, asubType: number) {
    let tab = "";
    for (let i = 0; i < (tabs as IStandardTabs[]).length; i++) {
      if ((tabs as IStandardTabs[])[i].id == asubType) {
        tab = (tabs as IStandardTabs[])[i].alias;
        break;
      }
    }
    return !getAllChildValue(id, tab, false).r;
  }

  function getAllChildValue(parentId: string, tab: string, hasAssit?: Boolean) {
    if (typeof hasAssit === "undefined") {
      hasAssit = true;
    }
    let values = { r: false, c: "0", d: "0", i: "0", t: "0", cq: "0", dq: "0", iq: "0", tq: "0", ifc: "0", dfc: "0", cfc: "0", tfc: "0" };
    if (parentId != "0") {
      for (let i = 0; i < tableData.value.length; i++) {
        let ibr: any = tableData.value[i];
        if (ibr.parentId == parentId) {
          if (hasAssit) {
            const direction = (getMyself(ibr.parentId) as any).direction;
            values.r = true;
            values.c = bigPlus(values.c, ibr.credit);
            values.d = bigPlus(values.d, ibr.debit);
            values.cq = bigPlus(values.cq, ibr.creditQut);
            values.dq = bigPlus(values.dq, ibr.debitQut);
            values.dfc = bigPlus(values.dfc, ibr.debitFC);
            values.cfc = bigPlus(values.cfc, ibr.creditFC);
            if (direction === ibr.direction) {
              values.i = bigPlus(values.i, ibr.initial);
              values.t = bigPlus(values.t, ibr.total);
              values.iq = bigPlus(values.iq, ibr.initialQut);
              values.tq = bigPlus(values.tq, ibr.totalQut);
              values.ifc = bigPlus(values.ifc, ibr.initialFC);
              values.tfc = bigPlus(values.tfc, ibr.totalFC);
            } else {
              values.i = bigMinus(values.i, ibr.initial);
              values.t = bigMinus(values.t, ibr.total);
              values.iq = bigMinus(values.iq, ibr.initialQut);
              values.tq = bigMinus(values.tq, ibr.totalQut);
              values.ifc = bigMinus(values.ifc, ibr.initialFC);
              values.tfc = bigMinus(values.tfc, ibr.totalFC);
            }
          } else {
            if (ibr.asubId.split("-").length > 1) {
              values.r = false;
              return values;
            } else {
              values.r = true;
              return values;
            }
          }
        }
      }
    }
    return values;
  }

  function getMyself(asubid: string) {
    for (let i = 0; i < tableData.value.length; i++) {
      if ((tableData.value[i] as any).asubId == asubid) {
        return tableData.value[i];
      }
    }
  }

  function canAddAssit(asubid: string, AssistingAccounting: number, asubType: number): boolean {
    if (
      !useAccountSetStoreHook().permissions.includes("initialbalance1-canedit") &&
      !useAccountSetStoreHook().permissions.includes("initialbalance1-candelete")
    ) {
      return false;
    }
    if (!props.isEditable) {
      return false;
    }
    if (isAssit(asubid)) {
      return false;
    } else {
      if (AssistingAccounting) {
        if (isLeaf(asubid, asubType)) {
          return judgeAssistCanEdit(asubid);
          // return true;
        }
        return false;
      } else {
        return false;
      }
    }
  }

  function judgeAssistCanEdit(asubid: string): boolean {
    // 如果是固定资产科目，需要校验资产是否已经折旧了。折旧不能再增加辅助核算期初
    // 辅助核算行只有一行，这一行不能编辑，它也就不能去删除
    let currentAsub = tableData.value.find((item: any) => item.asubId === asubid);
    return currentAsub.nonEditableType !== 3;
  }

  const assistByAsubId = ref<Array<Array<{ label: string; value: string }>>>([]);
  function getTypeName(asubId: string) {
    request({
      url: `/api/AssistingAccounting/GetAssitInfoByAsubId?asubId=` + asubId,
      method: "POST",
    }).then((res: IResponseModel<ITypeName[]>) => {
      assistByAsubId.value = [];
      res.data.forEach((item: ITypeName) => {
        if (isErp.value) {
          if (!assistByAsubId.value[Number(item.aatype)]) {
            assistByAsubId.value[Number(item.aatype)] = [];
          }
        } else {
          if (!assistByAsubId.value[Number(item.aatype)] && item.aaeid !== -1) {
            assistByAsubId.value[Number(item.aatype)] = [];
          }
        }

        if (item.status === 0) {
          assistByAsubId.value[Number(item.aatype)].push({
            label: item.aaname,
            value: String(item.aaeid),
          });
        }
      });
    //   clickAAType.value && (supplierList.value = assistByAsubId.value[clickAAType.value]);
    });
  }

  function canDelete(asubid: string, AssistingAccounting: number): boolean {
    if (!useAccountSetStoreHook().permissions.includes("initialbalance1-candelete")) {
      return false;
    }
    if (!props.isEditable) {
      return false;
    }
    if (AssistingAccounting && isAssit(asubid)) {
      return judgeAssistCanEdit(asubid);
      // return true;
    } else {
      return false;
    }
  }

  //所有可用数据，存储AA_NUM用于排序
  let aaEntryForSort: IStrObject = {};
  //所有可用数据
  let aaEntryPairs: INumObject = {};
  //所有数据
  let aaAllEntryPairs: INumObject = {};

  // 暂存选中的表格数据
  const editAssitDataTemp = ref<any>({});

  const assistDetailName = ref<(string | boolean)[][]>([]);
  const assistDetailNameTemp = ref<(string | boolean)[][]>([]);
  const assistDetailNumTemp = ref<string[][]>([]);
  const assistDetailTable = ref<IEditAssistLine[]>([]);
  const selectRefs = ref<any[]>([]);

  function getName(array: any, item: any, name: string = ""): string {
    if (item.parentId) {
      const parent = array.find((element: any) => element.asubId === item.parentId + "");
      name = "-" + item.asubName + name;
      return getName(array, parent, name);
    } else {
      name = item.asubName + name;
    }
    return name;
  }

  let reloadAssistDetailHandle: () => void;
  watchEffect(() => {
    if (!editAssistDetailDialog.value) return;
    useAssistingAccountingStore().assistingAccountingList;
    reloadAssistDetailHandle && reloadAssistDetailHandle();
  });
  // 辅助核算明细
  async function editAssistDetailHandle(AsubId: string, AsubCode: string, AsubName: string, row: any) {
    reloadAssistDetailHandle = () => {};
    await request({
      url: `/api/AssistingAccounting/GetAssitInfoByAsubId?asubId=${AsubId}`,
      method: "post",
    }).then((res: IResponseModel<ITypeName[]>) => {
      assitTypesForDetail.value = [];
      aaEntryForSort = {};
      aaEntryPairs = {};
      aaAllEntryPairs = {};
      for (let i = 0; i < res.data.length; i++) {
        let entry = res.data[i];
        if (!Object.keys(aaEntryPairs).includes(entry.aatype + "")) {
          assitTypesForDetail.value.push({ type: entry.aatype, name: entry.typeName });
          aaEntryForSort[entry.aatype] = [];
          aaEntryPairs[entry.aatype] = [];
          aaAllEntryPairs[entry.aatype] = [];
        }
        if (entry.status === 0) {
          aaEntryPairs[entry.aatype]!.push({ id: entry.aaeid, name: entry.aaname });
          aaEntryForSort[entry.aatype]!.push({ id: entry.aaeid, name: entry.aanum });
        }
        aaAllEntryPairs[entry.aatype]!.push({ id: entry.aaeid, name: entry.aaname });
        prevCodeData[entry.aatype] = entry.aanum;
      }

      assitTypesForDetail.value
        .sort((a, b) => {
          return a.type - b.type;
        })
        .forEach((item) => {
          editAssitDataTemp.value[item.type] = [];
        });
      assistData.value = res.data;
      editAssitData.value = assistData.value.reduce((prev: any, item: any) => {
        if (item.aatype === assitTypesForDetail.value[0].type) {
          prev.push(item);
        }
        return prev;
      }, []);
    });
    asubId.value = AsubId;
    asubCode.value = AsubCode;
    asubName.value = getName(props.data, row, "");
    getTypeName(AsubId);
    // 外币有问题？
    request({
      url: `/api/AssistingAccounting/GetAsubInitialAssitData?asubId=${AsubId}&fcId=${props.currentFcValue}`,
      method: "post",
    }).then((res: IResponseModel<Array<string>>) => {
      if (res.state === 1000) {
        assistDetailTable.value = res.data.map((item: string) => {
          return {
            assistDetail: item.split(",").map((item1: string) => {
              const foundItem = assistData.value.find((item2: ITypeName) => item2.aaeid === Number(item1));
              return {
                aaeid: item1,
                aaname: foundItem?.aaname || "",
                aanum: "some number", // 这里需要你根据实际情况提供一个字符串值
                unfold: false, // 这里需要你根据实际情况提供一个布尔值
                aatype: foundItem?.aatype || 0,
              } as IEditAssistInfo;
            }),
          };
        });
        assistDetailTable.value.push({
          assistDetail: new Array(assitTypesForDetail.value.length).fill(null).map(() => ({
            aaeid: "",
            aaname: "",
            unfold: true,
            aanum: "",
            aatype: 0,
          })),
        });
        handleTableAdd();
        basicValue.value = 580 / (assitTypesForDetail.value.length * 2 + 1);
        editAssistDetailDialog.value = true;
        setTimeout(() => {
          reloadAssistDetailHandle = () => {
            getTypeName(AsubId);
          };
        });
        clickRowIndex = assistDetailName.value.length - 2;
        clickColIndex = 0;
        // supplierList.value = assistByAsubId.value[assitTypesForDetail.value[0].type];
        supplierSeletorShow.value = true;
      } else {
        ElNotify({
          message: "出现异常，请刷新页面重试或联系客服",
          type: "warning",
        });
      }
    });
  }

  const deleteClickFlag = ref<boolean>(true);
  function deleteAssit(AsubId: string, ParentId: string, row: any) {
    // 去除前缀
    if (!deleteClickFlag.value) return;
    deleteClickFlag.value = false;
    let data = AsubId.replace(/^[a-zA-Z]+-\d+-/, "");
    request({
      url: `/api/InitialBalance/GenerlLedgeAafc?fullasubId=${AsubId}&asubId=${ParentId}&aafcid=${props.currentFcValue}`,
      method: "delete",
      data: data.split(","),
    })
      .then((res: IResponseModel<string>) => {
        if (res.state === 1000 && res.data) {
          ElNotify({
            type: "success",
            message: "删除成功！",
          });
          const deleteData = row;
          deleteData.initial = 0;
          deleteData.initialQut = 0;
          deleteData.initialFC = 0;
          deleteData.total = 0;
          deleteData.totalQut = 0;
          deleteData.totalFC = 0;
          deleteData.debit = 0;
          deleteData.debitQut = 0;
          deleteData.debitFC = 0;
          deleteData.credit = 0;
          deleteData.creditQut = 0;
          deleteData.creditFC = 0;
          refreshParent(deleteData);
          tableData.value.splice(
            tableData.value.findIndex((item: any) => item.asubId === AsubId),
            1
          );
          updateTableView();
        } else {
          ElNotify({
            type: "error",
            message: "删除失败！",
          });
        }
      })
      .finally(() => {
        deleteClickFlag.value = true;
      });
  }

  // 增加明细保存
  function submitAssistDetail() {
    let hasMap: Map<string, number> = new Map();
    for (let i = 0; i < assistDetailTable.value.length; i++) {
      let tempCodes: string[] = [];
      if (assistDetailTable.value[i].assistDetail.every((item) => item.aaeid === "")) {
        // 值全为空时
        continue;
      } else if (assistDetailTable.value[i].assistDetail.every((item) => item.aaeid !== "")) {
        // 全部输入完整时
        for (let j = 0; j < assistDetailTable.value[i].assistDetail.length; j++) {
          const assistDetailItem = assistDetailTable.value[i].assistDetail[j];
          const { aaeid, aatype, aaname } = assistDetailItem;
          tempCodes.push(aaeid);
          if (aatype !== 0 && assistByAsubId.value[aatype] && !assistByAsubId.value[aatype].some((item) => item.value === aaeid)) {
            return ElNotify({
              type: "warning",
              message: `第${i + 1}行，"${aaname}"已被删除或禁用，请重新选择`,
            });
          }
        }
      } else {
        // 有值，且未输入完整时
        if (assistDetailTable.value[i].assistDetail.some((item) => item.aaeid)) {
          return ElNotify({
            type: "warning",
            message: "第" + (i + 1) + "行，存在未选择的辅助核算项目！",
          });
        }
      }

      let tempStr = tempCodes.join(",");
      if (hasMap.has(tempStr)) {
        ElNotify({
          type: "warning",
          message: `第${i + 1}行与第${hasMap.get(tempStr)}行重复，请重新设置！`,
        });
        return false;
      } else {
        hasMap.set(tempStr, i + 1);
      }
    }
    useLoading().enterLoading("努力加载中，请稍候...");
    let aaCodes: string[] = [...hasMap.keys()];
    let assistDetaiScrollTop = initialBalanceTable.value?.$el.querySelector(".el-scrollbar__wrap").scrollTop || 0;
    request({
      url: `/api/InitialBalance/BatchAAGenerlLedgeFC?asubId=${Number(asubId.value)}&fcId=${currentFcValue.value}`,
      method: "POST",
      data: {
        asubId: Number(asubId.value),
        fcId: currentFcValue.value,
        aaCodes,
      },
    })
      .then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
          ElNotify({
            message: "保存成功",
            type: "success",
          });
          editAssistDetailDialog.value = false;
          emits("getTableData", assistDetaiScrollTop);
        } else {
          ElNotify({
            message: res.msg || "保存失败了！请刷新页面后重试或联系客服",
            type: "error",
          });
        }
      })
      .finally(() => {
        useLoading().quitLoading();
      });
  }

  let selectedIndexes: number[] = [];
  function assistDetailSelection(selection: any) {
    assistDetailIndex.value = selection;
    selectedIndexes = selection.map((item: any) => assistDetailTable.value.indexOf(item));
  }

  const assistData = ref<Array<ITypeName>>([]);
  // 批量新增
  const newAsubName = ref<string>("");
  function batchAddAssit() {
    editAssitDataTemp.value = {};
    assistActiveName.value = assitTypesForDetail.value[0].name;
    editAssitDialog.value = true;
    nextTick().then(() => {
      const asubNameArr = asubName.value.split("-");
      newAsubName.value = asubNameArr[asubNameArr.length - 1];

      asubNameForAssistCombo.value = newAsubName.value;
      (document.getElementById("asubNameForAssistCombo") as HTMLInputElement).value = newAsubName.value;
    });
  }

  function deleteRows() {
    if (selectedIndexes.length == 0) {
      ElNotify({
        type: "warning",
        message: "请先勾选数据",
      });
      return false;
    }
    useLoading().enterLoading("努力加载中，请稍候...");
    //反着删
    for (let i = selectedIndexes.length - 1; i >= 0; i--) {
      //如果最后一行被删除，则手动补充一行
      if (i === selectedIndexes.length - 1) {
        let lastIndex = assistDetailTable.value.length - 1;
        if (selectedIndexes[i] === lastIndex) {
          handleTableAdd();
        }
      }
      assistDetailTable.value.splice(selectedIndexes[i], 1);
    }
    useLoading().quitLoading();
  }

  function assistSelection(selection: any) {
    confirmAssitData.value = selection.reduce((prev: Array<IconfirmAssitData>, item: ISelection) => {
      prev.push({
        aaeid: String(item.aaeid),
        code: asubName.value + "_" + item.aaname,
        name: item.aaname,
        aatype: item.aatype,
        aanum: item.aanum,
      });
      return prev;
    }, []);
  }

  function selectToggle(row: any, column: any, event: any) {
    let selected = confirmAssitData.value.findIndex((item: any) => item.aaeid === String(row.aaeid)) >= 0 ? true : false;
    TableCom.value?.getTable().toggleRowSelection(row, !selected);
  }
  // 增加明细二级弹窗数据确定
  let confirmAssitDataTemp: IconfirmAssitData[][] = [];
  function editAssitHandle() {
    confirmAssitDataList.value = [];
    confirmAssitDataTemp = [];
    let ChoiceNumber = 0;

    // 修改最后一项
    // 最后修改的tab
    const assitDataType: number = assitTypesForDetail.value.find((item) => item.name === assistActiveName.value)!.type;
    if (confirmAssitData.value.length > 0) {
      // 有勾选就赋值
      editAssitDataTemp.value[assitDataType] = _.cloneDeep(confirmAssitData.value);
    } else {
      // 没勾选任何项就设置空数组
      editAssitDataTemp.value[assitDataType] = [];
    }
    // 报错弹窗
    for (let i of assitTypesForDetail.value) {
      if (!editAssitDataTemp.value[i.type] || editAssitDataTemp.value[i.type].length === 0) {
        ElNotify({
          type: "warning",
          message: `亲，${i.name}辅助核算不能为空`,
        });
        return false;
      }
      ChoiceNumber = ChoiceNumber == 0 ? editAssitDataTemp.value[i.type].length : ChoiceNumber * editAssitDataTemp.value[i.type].length;
      if (ChoiceNumber > 1000) {
        let msg = `您勾选的辅助核算项，组合后的总数量已超过1000条限制，<span style='color:#ef8d5d'>请减少部分类别勾选数量或分批添加</span>（组合数为各类别数相乘，例如：客户数*部门数*项目数）`;
        if(window.isErp){
            msg = '您勾选的辅助核算项，组合后的总数量已超过1000条限制，请减少部分类别勾选数量或分批添加（组合数为各类别数相乘，例如：客户数*部门数*项目数）'
        }
        ElNotify({
          type: "warning",
          message: msg,
        });
        return false;
      }
    }

    // 递归函数，用于生成排列组合
    function generateCombination(currentIndex: number, currentCombination: any) {
      // 当前索引大于数组长度时，表示已经遍历完所有数组
      if (currentIndex >= assitTypesForDetail.value.length) {
        confirmAssitDataTemp.push(currentCombination.slice()); // 将当前组合添加到结果数组中
        return;
      }
      // 遍历当前数组中的每个元素
      const item = editAssitDataTemp.value[assitTypesForDetail.value[currentIndex].type];
      for (let i = 0; i < item.length; i++) {
        // 将当前元素添加到当前组合中
        currentCombination.push({
          aaname: item[i].name,
          aaeid: item[i].aaeid,
          aanum: item[i].aanum,
          unfold: false,
          aatype: item[i].aatype || 0,
        });
        // 递归生成下一个数组的组合
        generateCombination(currentIndex + 1, _.cloneDeep(currentCombination));
        // 回溯，将当前元素从当前组合中移除
        currentCombination.pop();
      }
    }
    // 从第一个数组开始生成组合
    generateCombination(0, []);

    for (let index in confirmAssitDataTemp) {
      let nameTemp: string = newAsubName.value;
      let asubCodeTemp: string = asubCode.value;
      let numTemp: string[] = [];
      assistDetailNameTemp.value[index] = [false];
      for (let item of confirmAssitDataTemp[index]) {
        nameTemp += "_" + item.aaname;
        asubCodeTemp += "_" + item.aanum;
        numTemp.push(item.aaeid as string);
        assistDetailNameTemp.value[index].push(item.code);
      }
      assistDetailNumTemp.value.push(numTemp);
      confirmAssitDataList.value.push({ code: nameTemp, asubCode: asubCodeTemp });
    }

    editAssitDialog.value = false;
    confirmAssistDialog.value = true;
  }

  // 辅助核算确认删除
  function removeRow(code: string) {
    const index = confirmAssitDataList.value.findIndex((item: IconfirmAssitData) => item.code == code);
    confirmAssitDataList.value.splice(index, 1);
    confirmAssitDataTemp.splice(index, 1);
    assistDetailNumTemp.value.splice(index, 1);
  }

  // 增加明细二级弹窗取消
  function CloseConfirmAssistCombo() {
    confirmAssistDialog.value = false;
    editAssitDialog.value = true;
    // assistActiveName.value = assitTypesForDetail.value[0].name;

    // 切换
    handleAssisTabClick(assitTypesForDetail.value[0]);
    assistDetailNameTemp.value = [];
    assistDetailNumTemp.value = [];
    confirmAssitData.value = [];
    confirmAssitDataList.value = [];
  }

  function SubmitAssistCombo() {
    if (confirmAssitDataList.value.length === 0) {
      ElNotify({
        type: "warning",
        message: "没有选择任何辅助核算组合项",
      });
      return false;
    }

    // 去重
    const newSet = new Set();
    for (let item of assistDetailTable.value) {
      // if()
      const temp = item.assistDetail.reduce((prev, curr) => {
        return prev + "," + curr.aaeid;
      }, item.assistDetail[0].aaeid);
      if (newSet.has(temp)) {
        continue;
      } else {
        newSet.add(temp);
      }
    }

    for (let item of confirmAssitDataTemp) {
      const setTemp = item.reduce((prev, curr) => {
        return prev + "," + curr.aaeid;
      }, item[0].aaeid);
      if (newSet.has(setTemp)) {
        continue;
      } else {
        assistDetailTable.value.push({
          assistDetail: item as IEditAssistInfo[],
        });
      }
    }
    clarAssistDetailTable();
    handleTableAdd();
    // 数据重置
    confirmAssitDataTemp = [];
    confirmAssistDialog.value = false;
    assistActiveName.value = assitTypesForDetail.value[0].name;
    editAssitDataTemp.value = {};
    editAssitData.value = [];
    assistDetailNameTemp.value = [];
    confirmAssitDataList.value = [];
    newSet.clear();
  }

  // 切换tab
  function handleAssisTabClick(tab: any) {
    const { name } = tab.props;
    const type: number = (assitTypesForDetail.value as IAssistTypeForDetail[]).find((item: { name: string }) => item.name === name)!.type;
    editAssitData.value = assistData.value.reduce((prev: Array<ITypeName>, item: ITypeName) => {
      if (item.aatype === type) {
        if (item.aatype === 10004) {
          assistByAsubId.value[item.aatype].findIndex((item1) => item1.value === String(item.aaeid)) > -1 && prev.push(item);
        } else {
          prev.push(item);
        }
      }
      return prev;
    }, []);
    assistEmptyText.value = editAssitData.value.length === 0 ? "暂无数据" : "";
    const assitDataType: number = assitTypesForDetail.value.find((item) => item.name === assistActiveName.value)!.type;
    // 如果已选择项就赋值
    if (confirmAssitData.value.length > 0) {
      editAssitDataTemp.value[assitDataType] = _.cloneDeep(confirmAssitData.value);
    } else {
      editAssitDataTemp.value[assitDataType] = [];
    }
    confirmAssitData.value = [];
    for (let i = 0; i < editAssitData.value.length; i++) {
      if (!editAssitDataTemp.value[editAssitData.value[i].aatype] || editAssitDataTemp.value[editAssitData.value[i].aatype].length === 0)
        break;
      editAssitDataTemp.value[editAssitData.value[i].aatype].forEach((temp: { aaeid: number }) => {
        if (editAssitData.value[i].aaeid === Number(temp.aaeid)) {
          nextTick(() => {
            TableCom.value.toggleRowSelect(i, true);
          });
        }
      });
    }
  }
  const resetVirtualTableState = () => {
    initialBalanceTable.value?.resetVirtualTableState();
  };
  const setTableScrollTop = (scrollTop: number) => {
    const scrollDom = initialBalanceTable.value?.$el.querySelector(".el-scrollbar__wrap");
    scrollDom && (scrollDom.scrollTop = scrollTop);
  };

  defineExpose({ resetVirtualTableState, setTableScrollTop, updateTableView });

  const autoAddName = ref("");
  const showAssistByAsubId = ref<any[]>([]);
  watchEffect(() => {
    showAssistByAsubId.value = _.cloneDeep(assistByAsubId.value);
  });
  function assistFilterMethod(value: string, aatype: number) {
    showAssistByAsubId.value[aatype] = assistByAsubId.value[aatype] ? commonFilterMethod(value, assistByAsubId.value[aatype], "label") : [];
    autoAddName.value = showAssistByAsubId.value[aatype].length === 0 ? value.trim() : "";
  }
</script>

<style scoped lang="less">
  @import "@/style/Settings/InitialBalanceTable.less";

  :deep(.span_wrap) {
    -webkit-line-clamp: 1;
  }

  .container {
    &.erp-container {
      height: 100%;
    }

    .cell-dialog {
      position: fixed;
      padding: 10px;
      font-size: 12px !important;
      font-weight: 500;
      margin: 0;
      border: 1px solid #dadada;
      border-radius: 3px;
      text-align: left;
      line-height: 12px;
      background-color: #fff;
      z-index: 99;
      // min-width: 156px;
      // max-width: 500px;
    }

    .native-input {
      display: block;
      width: 100%;
      border: 1px solid var(--el-border-color);
      height: 30px;
      line-height: 30px;
      border-radius: 2px;
      box-sizing: border-box;
      text-align: right;
      outline: none;
      appearance: none;

      &:hover {
        border: 1px solid var(--el-border-color-hover);
      }

      &.is-focus {
        border: 1px solid var(--el-color-primary);
      }
    }

    :deep(.el-table__body) {
      .el-table__row .el-table__cell:last-child {
        .cell {
          padding-right: 9px !important;
        }
      }
    }
  }

  .add-assist-detail-main {
    :deep(.el-table) {
      box-sizing: content-box;

      thead th {
        border-right: 1px solid transparent;
        border-bottom: 1px solid var(--table-border-color);

        div.cell {
          position: relative;
          padding: 0 8px;

          &:after {
            content: "";
            height: 16px;
            width: 1px;
            background-color: #d0dae8;
            display: block;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }

      tbody td {
        border-right: 1px solid transparent;
        border-bottom: 1px solid var(--table-border-color);
      }

      .el-table__cell {
        height: 40px;
        padding: 2px 0;
      }

      .el-scrollbar__view {
        height: 298px;
      }

      .cell {
        overflow: visible !important;
      }
    }

    .add-line {
      position: absolute;
      left: 5px;
      z-index: 9999;
    }

    .add-line-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      background: url("@/assets/Voucher/add-icon.png") no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;

      &.erp {
        background-image: url("@/assets/Voucher/erp-add-icon.png");
      }
    }

    .subtract-line {
      position: absolute;
      // top: 49px;
      right: 5px;
      z-index: 9999;
    }

    .subtract-line-icon {
      width: 16px;
      height: 16px;
      background: url(@/assets/Voucher/del-icon.png) no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;

      &.erp {
        background-image: url("@/assets/Voucher/erp-del-icon.png");
      }
    }
  }

  .confirm-assit-combo-content {
    :deep(.el-scrollbar__view) {
      height: 400px;
    }
  }

  :deep(.el-table .el-table__cell) {
    z-index: auto;
  }

  .divAddAA {
    & table {
      margin: 19px auto 0;

      & tr td {
        height: 30px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        padding-bottom: 16px;
      }
    }

    // & :deep(.el-form-item__label) {
    //     padding-right: 0px !important;
    // }
    & :deep(.el-input__wrapper) {
      height: 30px !important;
      padding-left: 10px;
    }

    // & :deep(.el-form-item__content) {
    //     display: block !important;
    //     width: 180px;
    // }
  }

  .assist-item {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 5px 0;
    padding-left: 4px;
  }

  .add-icon {
    display: inline-block;
    width: 40px;
    height: 20px;
    font-size: 18px;
    cursor: pointer;
    padding-left: 10px;
    transform: translateY(2px);
  }

  body[erp] {
    .erp-container {
      :deep(.el-table) {
        tr th:last-child .cell {
          padding-right: 8px !important;
        }

        tr td:last-child .cell {
          padding-right: 8px !important;
        }

        tr td .cell .add-icon {
          color: var(--main-color);
          font-size: 26px;
          vertical-align: text-bottom;
          transform: translateY(0px);
          line-height: 19px;
        }
      }

      :deep(.el-scrollbar__wrap) {
        position: relative;

        .el-table__empty-block {
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
        }
      }
    }

    .add-assist-detail-content {
      .add-assist-detail-main {
        :deep(.el-input__wrapper) {
          margin-right: 0;
        }
      }
    }

    .edit-assit-main {
      :deep(.el-tabs__active-bar) {
        width: 28px !important;
      }
    }
  }

  .erp-box-footer {
    text-align: right !important;

    .button.solid-button {
      margin-right: 20px;
    }
  }
</style>
<style lang="less">
  .add-assist-detail-content {
    .el-table--border::before {
      width: 0;
    }
  }
</style>
