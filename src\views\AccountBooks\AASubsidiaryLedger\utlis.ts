import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney, formatQuantity } from "@/util/format";
import dayjs from 'dayjs';
import { getColumnWidth } from "@/components/ColumnSet/utils";

// 设置表格列
export const ChangeCulumns = (quantity: boolean, fcid: string | number) => {
    const setModule = "AASubsidiary";
    let mycolumns: IColumnProps[];
    if (quantity) {
        if (Number(fcid) > 0) {
            mycolumns = [
                {
                    label: "日期",
                    prop: "v_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    formatter:(row, column, value)=>{
                       return dayjs(value).format("YYYY-MM-DD");
                    },
                    width: getColumnWidth(setModule, 'v_date')
                },
                { slot: "vgname" },
                {
                    label: "科目",
                    prop: "asub_names",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 130,
                    width: getColumnWidth(setModule, 'asub_names')
                },
                {
                    label: "摘要",
                    prop: "description",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 135,
                    width: getColumnWidth(setModule, 'description')
                },
                {
                    label: "币别",
                    prop: "fc_code",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 50,
                    width: getColumnWidth(setModule, 'fc_code')
                },
                {
                    label: "借方发生额",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "debit_qut",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'debit_qut')
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价（原币）",
                            prop: "debit_price",
                            minWidth: 110,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'debit_price')
                        },
                        {
                            label: "金额（原币）",
                            prop: "debit_fc",
                            minWidth: 100,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'debit_fc')
                        },
                        {
                            label: "金额（本位币）",
                            prop: "debit",
                            minWidth: 100,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'debit')
                        },
                    ],
                },
                {
                    label: "贷方发生额",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "credit_qut",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'credit_qut')
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价（原币）",
                            prop: "credit_price",
                            minWidth: 110,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'credit_price')
                        },
                        {
                            label: "金额（原币）",
                            prop: "credit_fc",
                            minWidth: 100,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit_fc')
                        },
                        {
                            label: "金额（本位币）",
                            prop: "credit",
                            minWidth: 100,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit')
                        },
                    ],
                },
                {
                    label: "余额",
                    headerAlign: "center",
                    children: [
                        { 
                            label: "方向", 
                            prop: "direction", 
                            minWidth: 50, 
                            align: "left", 
                            headerAlign: "left",
                            width: getColumnWidth(setModule, 'direction') 
                        },
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "total_qut",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'total_qut') 
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价（原币）",
                            prop: "total_price",
                            minWidth: 110,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'total_price') 
                        },
                        {
                            label: "金额（原币）",
                            prop: "total_fc",
                            minWidth: 110,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'total_fc') 
                        },
                        {
                            label: "金额（本位币）",
                            prop: "total",
                            minWidth: 110,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            resizable: false,
                        },
                    ],
                },
            ];
        } else {
            mycolumns = [
                {
                    label: "日期",
                    prop: "v_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    formatter:(row, column, value)=>{
                        return dayjs(value).format("YYYY-MM-DD");
                    },
                    width: getColumnWidth(setModule, 'v_date') 
                },
                { slot: "vgname" },
                {
                    label: "科目",
                    prop: "asub_names",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 130,
                    width: getColumnWidth(setModule, 'asub_names') 
                },
                {
                    label: "摘要",
                    prop: "description",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 135,
                    width: getColumnWidth(setModule, 'description') 
                },
                {
                    label: "借方发生额",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "debit_qut",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'debit_qut') 
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "debit_price",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'debit_price') 
                        },
                        {
                            label: "金额",
                            prop: "debit",
                            minWidth: 100,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'debit')
                        },
                    ],
                },
                {
                    label: "贷方发生额",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "credit_qut",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'credit_qut')
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "credit_price",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'credit_price')
                        },
                        {
                            label: "金额",
                            prop: "credit",
                            minWidth: 100,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit')
                        },
                    ],
                },
                {
                    label: "余额",
                    headerAlign: "center",
                    children: [
                        { 
                            label: "方向", 
                            prop: "direction", 
                            minWidth: 50, 
                            align: "left", 
                            headerAlign: "left",
                            width: getColumnWidth(setModule, 'direction') 
                        },
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "total_qut",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'total_qut')
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "total_price",
                            minWidth: 80,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'total_price')
                        },
                        {
                            label: "金额",
                            prop: "total",
                            minWidth: 110,
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            resizable: false,
                        },
                    ],
                },
            ];
        }
    } else {
        if (Number(fcid) > 0) {
            mycolumns = [
                {
                    label: "日期",
                    prop: "v_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    formatter:(row, column, value)=>{
                        return dayjs(value).format("YYYY-MM-DD");
                    },
                    width: getColumnWidth(setModule, 'v_date')
                },
                { slot: "vgname" },
                {
                    label: "科目",
                    prop: "asub_names",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 130,
                    width: getColumnWidth(setModule, 'asub_names')
                },
                {
                    label: "摘要",
                    prop: "description",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 135,
                    width: getColumnWidth(setModule, 'description')
                },
                { 
                    label: "币别", 
                    prop: "fc_code", 
                    align: "left", 
                    headerAlign: "left", 
                    minWidth: 50,
                    width: getColumnWidth(setModule, 'fc_code') 
                },
                {
                    label: "借方（原币）",
                    prop: "debit_fc",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 105,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit_fc') 
                },
                {
                    label: "借方（本位币）",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 105,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit') 
                },
                {
                    label: "贷方（原币）",
                    prop: "credit_fc",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 105,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit_fc') 
                },
                {
                    label: "贷方（本位币）",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 105,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit')
                },
                { 
                    label: "方向", 
                    prop: "direction", 
                    minWidth: 90, 
                    align: "left", 
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'direction') 
                },
                {
                    label: "余额（原币）",
                    prop: "total_fc",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'total_fc')
                },
                {
                    label: "余额（本位币）",
                    prop: "total",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ];
        } else {
            mycolumns = [
                {
                    label: "日期",
                    prop: "v_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    formatter:(row, column, value)=>{
                        return dayjs(value).format("YYYY-MM-DD");
                    },
                    width: getColumnWidth(setModule, 'v_date')
                },
                { slot: "vgname" },
                {
                    label: "科目",
                    prop: "asub_names",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 130,
                    width: getColumnWidth(setModule, 'asub_names')
                },
                {
                    label: "摘要",
                    prop: "description",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 135,
                    width: getColumnWidth(setModule, 'description')
                },
                {
                    label: "借方",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 105,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit')
                },
                {
                    label: "贷方",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 105,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit')
                },
                { 
                    label: "方向", 
                    prop: "direction", 
                    minWidth: 90, 
                    align: "left", 
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'direction') 
                },
                {
                    label: "余额",
                    prop: "total",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false
                },
            ];
        }
    }
    return mycolumns;
};

export const userDefinedClassName = (data: any) => (data.children ? "" : "tree-file");