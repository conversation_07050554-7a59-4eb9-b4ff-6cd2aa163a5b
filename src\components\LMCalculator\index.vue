<template>
    <el-dialog v-model="showCalDialog" 
                class="cal-dialog" 
                width="490px" 
                :show-close="false" 
                :modal="false" 
                :draggable="true"
                >
        <template #header>
            <div class="cal-header">
                <div class="cal-title">计算器</div>
                <div class="cal-close" @click="handleClose"></div>
            </div>
        </template>
        <div class="cal-wrapper">
            <div class="cal-body">
                <div class="calBox">
                    <div class="screen">
                        <!-- <div class="cal-test">{{ test }}</div> -->
                        <input v-model="inputVal" type="text" id="text" />
                    </div>
                    <div class="button-wrap">
                        <ul class="list-button">
                            <li class="copy" @click="handleCopy">复制</li>
                            <li class="backspace" @click="handleBackspace">退格</li>
                            <li class="clear" @click="handClear">清屏</li>
                            <li class="" @click="handleReverse">+/-</li>
                            <li class="" @click="handleDao">1/x</li>
                            <li class="num" @click="handleInput('7')">7</li>
                            <li class="num" @click="handleInput('8')">8</li>
                            <li class="num" @click="handleInput('9')">9</li>
                            <li class="" @click="handleCaltor('/')">/</li>
                            <li class="" @click="handlePercent">%</li>
                            <li class="num" @click="handleInput('4')">4</li>
                            <li class="num" @click="handleInput('5')">5</li>
                            <li class="num" @click="handleInput('6')">6</li>
                            <li class="" @click="handleCaltor('*')">*</li>
                            <li class="" @click="handleSqrt">√</li>
                        </ul>
                        <div class="list-collaspe">
                            <div class="list-collaspe-lt">
                                <ul class="list-button">
                                    <li class="num" @click="handleInput('1')">1</li>
                                    <li class="num" @click="handleInput('2')">2</li>
                                    <li class="num" @click="handleInput('3')">3</li>
                                    <li class="" @click="handleCaltor('-')">-</li>
                                    <li class="num zero" @click="handleInput('0')">0</li>
                                    <li class="num" @click="handleInput('.')">.</li>
                                    <li class="" @click="handleCaltor('+')">+</li>
                                </ul>
                            </div>
                            <div class="list-collaspe-rt">
                                <ul class="list-button">
                                    <li class="deng" @click="handleDeng">=</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getGlobalLodash } from "@/util/lodash";

const _ = getGlobalLodash()
const showCalDialog = ref(false);
//关闭
const inputVal = ref("0");
const handleClose = () => {
    showCalDialog.value =false;
    document.removeEventListener('keydown', handleKeyDown);
    handClear();
}
//复制
const handleCopy = () => {
    const oText = document.getElementById('text') as HTMLInputElement;
    oText.select();
    document.execCommand('copy');
    alert("复制成功！");
}
//退格
const handleBackspace = () => {
    if (inputVal.value.length > 1) {
        inputVal.value = inputVal.value.slice(0, inputVal.value.length - 1);
        test.value = test.value.slice(0, inputVal.value.length - 1);
    } else if(inputVal.value.length === 1 ){
        inputVal.value="0";
    }
}
//清屏
const handClear = () => {
    result.value = 0;
    inputVal.value = "0";
    test.value = "";
    flag = false;
}
//数字输入
let flag = false;
const handleInput = (val: string = "") => {
    if( ['+', '-', '*', '/'].some(item => test.value.includes(item)) && flag === false) {
        inputVal.value = "";
        flag = true;
    }
    if (inputVal.value==='0') {
        if(val==='.') {
            inputVal.value += val;
        } else {
            inputVal.value = val;
        }
    } else if(inputVal.value === '除数不能为0') {
        inputVal.value = val;
    } else if(inputVal.value.indexOf('e')!==-1) {
        inputVal.value = val;
        test.value = '';
    } else if(inputVal.value==='0.'){
        if(val==='.') {
            return;
        } else {
            inputVal.value += val;
        }
    } else if( test.value.length > 0 && result.value!==0 ){ 
        result.value = 0;
        inputVal.value = val;
    } else {
        inputVal.value += val;
    }
}
//正负
const handleReverse = () => {
    if (inputVal.value!=='0') {
        if (inputVal.value[0]==='-') {
            inputVal.value = inputVal.value.slice(1, inputVal.value.length);
        } else {
            inputVal.value = '-' + inputVal.value;
        }
    }
}
//倒数
const handleDao = () => {
    if (inputVal.value === '0') {
        inputVal.value = '除数不能为0';
    } else if(inputVal.value === '0.'){
        inputVal.value = '除数不能为0';
    } else {
        inputVal.value = String(1 / Number(inputVal.value));
    }
}
//开方
const handleSqrt = () => {
    inputVal.value = String(Math.sqrt(Number(inputVal.value)));
}
//百分数
const handlePercent = () => {
    inputVal.value = String(Number(inputVal.value)/100);
}
//加-减-乘-除-取余
const test = ref("");
const result = ref(0);
const handleCaltor = (val:string) => {
    if(['+', '-', '*', '/'].some(item => test.value.includes(item))) { // 检查字符串中是否包含特定字符
        let operatorIndex = test.value.slice(1).search(/[+\-*/]/); // 查找第一个运算符的索引
        if (operatorIndex > -1) {
            calData(operatorIndex);
        }
        test.value = String(result.value) + val;
        inputVal.value = String(result.value);
    } else if(result.value===0) {
        test.value = inputVal.value + val;
    } else {
        if(result.value!==0) {
            test.value = '';
        }
        test.value += inputVal.value + val;
    }
}
function calData(operatorIndex: number) {
    let num1 = Number(test.value.slice(0, operatorIndex+1));
    let num2 = Number(inputVal.value);
    if(['+', '-', '*', '/', '%'].some(item => inputVal.value.includes(item))){
        const matchResult = inputVal.value.match(/[+-/*](\d+)/);
        if (matchResult) {
            num2 = Number(matchResult[1]);
        }
    }
    let digitNum1 = (num1.toString().split('.')[1] || '').length;
    let digitNum2 = (num2.toString().split('.')[1] || '').length;    
    switch (test.value[operatorIndex+1]) {
        case '+':
            result.value = _.round(num1 + num2, Math.max(digitNum1, digitNum2));
            // result.value = (num1*10 + num2*10) / 10; //处理0.1+0.2不精确
            break;
        case '-':
            result.value = num1 - num2;
            break;
        case '*':
            result.value = num1 * num2;
            break;
        case '/':
            result.value = num1 / num2;
            break;
        default:
            break;
    }       
}
const handleDeng = () => {
    if(['+', '-', '*', '/', '%'].some(item => test.value.includes(item))) { // 检查字符串中是否包含特定字符
        let operatorIndex = test.value.slice(1).search(/[+\-*/%]/); // 查找第一个运算符的索引
        if (operatorIndex > -1) {
            calData(operatorIndex);
        }
        if(result.value ===0 ) {
            test.value = '';
        } else {
            test.value = String(result.value);
        }
        inputVal.value = String(result.value);
    } else if(test.value.length===0){
        test.value = inputVal.value;
    }
}

function handleKeyDown(event: KeyboardEvent) {
    if(['+','-','*','/'].includes(event.key)) {
        handleCaltor(event.key);
    }
}

function show() {
    showCalDialog.value = true;
    document.addEventListener('keydown', handleKeyDown);
}

defineExpose({
    show,
});
</script>

<style scoped lang="less">
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.cal-header {
    width: 486px;
    background-color: rgb(244, 243, 248);
    position: relative;
    height: 30px;
    & .cal-title {
        color: var(--font-color);
        font-size: var(--h3);
        text-align: center;
        line-height: 30px;
    }
    & .cal-close {
        width: 20px;
        height: 20px;
        background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAQhJREFUKBVlkklShEAQRalaeAtvhlPbrS6AC+iuQw8ALLQVh7BPptcwKN+DBrXNiCSH/3OoDELTNHdZluUhhOuiKN7x/0nbtkcppVuAbeSTExyibwCLfbY5MTlyo51R4uTniYlnU5G+ObEd5yYI2qXv+0dcJ/aAS/OSzUnGrsqy7IYCwambBLQ3hwxkCi54n8XZXGBQ1/UppkMtyuz8m2xuAHSUGOMBhLkJKyVW/RrR8TuDdF/BfZgeKKyPcb3zqqpeseOEfbJrQL7E+li36DjMiQVBB/AFBf+5hiCYhfdihE7KI8F6IlOw9HSSFS6zIXe1a+SktWfzd/hAjyE8Y/+IRTRcwPtEt9+IoqSRmjKbxQAAAABJRU5ErkJggg==');
        background-repeat: no-repeat;
        background-position: center center;
        position: absolute;
        top: 50%;
        right: 10px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        cursor: pointer;
    } 
}
.cal-wrapper {
    width: 486px;
    & .cal-body {
        height: 397px;
    } 
}
.calBox {
    width: 460px;
    padding-bottom: 10px;
    padding-top: 15px;
    background: #fff;
    margin: 0 auto;
    overflow: hidden;
}
.screen {
    margin: 0 auto;
    position: relative;
    width: 430px;
    height: 72px;
    border-radius: 3px;
    background: #FCFDEB;
    padding: 0 10px;
    box-shadow:inset 1px 1px 6px #808080;
}
.screen input {
    width: 395px;
    margin-top: 12px;
    border: none;
    outline:none;
    background-color:#FCFDEB;
    text-align: right;
    font-family: "微软雅黑";
    font-size: 40px;
}
.cal-test {
    position: absolute;
    right: 18px;
    top: 2px;
    font-size: 12px;
    color: #999;
}
.button-wrap {
    margin: 0 auto;
    width: 430px;
}
.list-button {
    display: flex;
    flex-wrap: wrap;
}
li {
    list-style: none;
    width: 76px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    font-family: "微软雅黑";
    border: 1px solid #e8e8e8;
    border-radius: 3px;
    margin: 12px 12px 0px 0;
    cursor: pointer;
    font-size: 20px;
    color: #6a6a6a;
    &:nth-child(5n + 5) {
        margin-right: 0;
    }
    &:hover {
        background-color: #63bf5c;
        border: 1px solid #63bf5c;
        color: #fff;
    }
    &.zero {
        width: 162px;
        margin-right: 12px;
    }
    &.deng {
        margin-right: 0;
        height: 102px;
        line-height: 102px;
    }
}
.copy,
.backspace,
.clear {
    font-size: 16px;
}
.list-collaspe {
    display: flex;
    &-rt {
        flex: 1;
    }
}
        
</style>
<style lang="less">
.cal-dialog.el-dialog {
    border: 2px solid rgb(227, 227, 227);
    .el-dialog__header {
        border-bottom: none;
    }
}
</style>