export interface IAsubItem {
    itemId: number;
    itemType: number;
    itemName: string;
}
export interface IAsubItemString {
    itemId: string;
    itemType: number;
    itemName: string;
    // 是否为经营活动产生的现金流量
    isBusinessAsub: boolean;
}
export interface IAsubTypeList {
    label: string;
    value: number;
}
interface BaseTableData {
    asubId: number;
    asubCode: string;
    asubName: string;
    mainInItemName: string;
    mainOutItemName: string;
    subItemName: string;
}
interface ISearchTableData extends BaseTableData {
    mainInItemId: number;
    mainOutItemId: number;
    subItemId: number;
}
export interface ITableData extends ISearchTableData {
    index: number;
}
export interface IMapTableData extends BaseTableData {
    index: number;
    mainInItemId: string;
    mainOutItemId: string;
    subItemId: string;
}
export interface ISearchBack {
    data: Array<ISearchTableData>;
    count: number;
}
