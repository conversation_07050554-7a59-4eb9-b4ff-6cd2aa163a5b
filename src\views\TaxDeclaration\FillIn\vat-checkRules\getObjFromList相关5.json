[{"RuleXh": "773c474ea8c048e782ea466f0dac6fe4", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "msxse"}], "NmyRuleName": "4-11校验 （附表三全表非0再判断，全表为0通过）", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第4行第11列“9%税率的服务、不动产和无形资产（10%税率也填这栏）--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第2行第1列“9%税率的项目（10%税率也填这栏）--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "32524dd45a5f43348d51f582ef6ee2b1", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三5_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第5行第5列“5%征收率的项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "ff0fca787c92439caba47828762644a2", "RelateTable": "001,002,016", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzdxse"}], "NmyRuleName": "016_xsjelj_校验", "NmyRuleMessage": "《加油站月销售油品汇总表》“销售金额/通过加油机_本月数”+“销售金额/不通过加油机_本月数”须小于等于《增值税及附加税费申报表附列资料（一）》第1行的第1列+第3列+第5列+第7列。"}, {"RuleXh": "ff7907ed4185450b9a1a4f5c52608ee6", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}], "NmyRuleName": "11_3校验2", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》主表第11栏3列 = 《增值税及附加税费申报表附列资料（一）（本期销售情况明细）》第10列第6行+第14列第7行。"}, {"RuleXh": "0cf45e4c5d824d4a8f715fb1c36b0253", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfNsjctzxse"}], "NmyRuleName": "主表5_3校验", "NmyRuleMessage": "主表第5行（二）按简易办法计税销售额“即征即退项目”列“本月数”需大于等于第6行其中：纳税检查调整的销售额"}, {"RuleXh": "0bd1020aa32b450fab996f982aa3686f", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "主表19_3校验2", "NmyRuleMessage": "主表19行应纳税额“即征即退项目”列“本月数”需等于第11栏“销项税额”“即征即退项目”列“本月数”-第18栏“实际抵扣税额”“即征即退项目”列“本月数”-附表四第7行第5列的“本期实际抵减额”"}, {"RuleXh": "41884124c9b847bcb82e6a36e97290e2", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "je"}], "NmyRuleName": "金额：0 ≤ 第10行 ≤ 第1+4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第10栏“（四）本期用于抵扣的旅客运输服务扣税凭证”金额必须大于等于0且不能大于第一栏“（一）认证相符的税控增值税专用发票”金额与第四栏“（二）其他扣税凭证”金额之和，请检查！"}, {"RuleXh": "748795b646a549a99229395181eeb2af", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "syslNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sqldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ydks<PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "fcyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjnqjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmwjseQjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqybtse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jzjtsjtse"}], "NmyRuleName": "主表3列为0校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第3列即征即退本月数任一项填有非0.00的数据时，请确定您具有退税资格并认真核对！"}, {"RuleXh": "74a6813aac594c278bf6b928d462c09f", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'37'", "Col": "je"}], "NmyRuleName": "附表2-8a-2", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第8a行加计扣除农产品进项税额 金额不得填写"}, {"RuleXh": "459e13e3838946d5aa1f484d32937459", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三1_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第1行第5列“13%税率的项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "fd53662905c04bc1915e048744c31ba4", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "syslNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "syslNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "syslNsjcybjse"}], "NmyRuleName": "16_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第16行2列“按适用税率计算的纳税检查应补缴税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "fc796528e4514351a8d90cb6d85555a2", "RelateTable": "001,002,015", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzdxse"}], "NmyRuleName": "015_sumList_yljje_校验", "NmyRuleMessage": "《加油站月份加油信息明细表》“月累计金额/合计”须小于等于《增值税及附加税费申报表附列资料（一）》第1行的第1列+第3列+第5列+第7列。"}, {"RuleXh": "c7913259242a46d7add39cd1cd16e9e5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "nsjctzdxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kchXxynse"}], "NmyRuleName": "附表1-13b校验", "NmyRuleMessage": "该纳税人非铁路运输企业，《增值税及附加税费申报表附列资料（一）》不得填写13b行的相关栏次！"}, {"RuleXh": "0af6e918b56149e3897185241959206d", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三8_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第8行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "0a1f50c2e47c4741af72b78ab559012c", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "asysljsxse"}], "NmyRuleName": "主表中第2栏和第3栏和1栏（即征即退）提示校验", "NmyRuleMessage": "《增 值 税 及 附 加 税 费 申 报 表》本表第2栏次的\"其中：应税货物销售额_即征即退项目\"和第3栏次的\"应税劳务销售额_即征即退项目\"本月数都为0，但第1栏次的“（一）按适用税率计税销售额_即征即退项目”本月数不为0,您确认继续提交吗？"}, {"RuleXh": "0797f38532e9430a8c42b5d3815fc1dc", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "je"}], "NmyRuleName": "金额：0 ≤ 第9行 ≤ 第1+4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第9栏\"（三） 本期用于购建不动产的扣税凭证\"金额必须大于等于0且不能大于第一栏\"（一）认证相符的税控增值税专用发票\"金额与第四栏\"（二）其他扣税凭证\"金额之和，请检查！"}, {"RuleXh": "0819e920e71c41a987f202082108a2cd", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqfse"}], "NmyRuleName": "031_1_2_校验", "NmyRuleMessage": "您本期未发生税控设备服务费，不可填写《增值税及附加税费申报表附列资料（四）》第1行本期发生额"}, {"RuleXh": "3e10431428124e0eafe24d7bc929dff9", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqydj<PERSON>"}], "NmyRuleName": "3_4校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第3行第4列“建筑服务预征缴纳税款--本期实际抵减税额”必须小于等于第3行第3列“建筑服务预征缴纳税款--本期应抵减税额”。"}, {"RuleXh": "3c976bb552a346d687279b296081199e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "hjXxynse"}], "NmyRuleName": "6-10校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第6行“一般计税方法计税-即征即退货物及加工修理修配劳务”第10列“合计-销项(应纳)税额”"}, {"RuleXh": "71b6fd7000504ca88afc8cfdfbbc1c7e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}], "NmyRuleName": "11_1校验2", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第11栏1列 = 《增值税及附加税费申报表附列资料（一）（本期销售情况明细）》（第10列第1+3行之和-10列第6行）+第14列（第2+4+5行）之和-14列第7行。"}, {"RuleXh": "71bf6d175db74ce1886f9152ac7f7c87", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "yslwxse"}], "NmyRuleName": "3_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第3行4列“应税劳务销售额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "6f67c5d8a3544ae284a34d72e3c110c8", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "je"}], "NmyRuleName": "10_2校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》附表二第10栏“（四）本期用于抵扣的旅客运输服务扣税凭证”金额必须大于等于0！"}, {"RuleXh": "a2d73475a7334c178a7b584811cef755", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjns<PERSON><PERSON><PERSON>"}], "NmyRuleName": "25_1校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第25栏“期初未缴税额”【round ( Number(getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').qcwjse) + Number(getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').qcwjse) , 2 )】，第30栏【本期缴纳上期应纳税额】数值为0，请核实是否填写有误，以免申报错误。"}, {"RuleXh": "d594b312ff37493e8e249bea2cd5df11", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "sjdkse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "sjdkse"}], "NmyRuleName": "18_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第18行4列“实际抵扣税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "488295c83f0d4aecbed44411deb77585", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sqldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sqldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtytse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmldse"}], "NmyRuleName": "本月数第1列和3列第12、13、14、15、20行校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》您为逾期未认定一般纳税人，本月数第1列和3列第12、13、14、15、20行必须=0。"}, {"RuleXh": "477b773447bc45d784bbe1b0211787c4", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "6-5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第6行第5列“3%征收率的项目”--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第12行第12列“3%征收率的服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "47f20c180479472f9a0e6f834a979b25", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "msxse"}], "NmyRuleName": "17-11校验（附表三全表非0再判断，全表为0通过）", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第17行第11列“免抵退税：服务、不动产和无形资产--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第7行第1列“免抵退税的项目--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "9a61f1f361f8419d8f3643249071be76", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三7_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第7行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "d0b3bcf8f0904e70908af3d7809f5468", "RelateTable": "001,003,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "se"}], "NmyRuleName": "您本期填报的《附列资料（二）》进项税额合计为XX元，适用加计抵减率为XX%,但《附列资料（四）》加计抵减本期发生额为XX元，可能未足额计提享受加计抵减政策，请再次核对是否已准确填报《附列资料（四）》加计抵减栏次（允许1元尾差）", "NmyRuleMessage": "您本期填报的《附列资料（二）》进项税额合计为【getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','12').se】元，适用加计抵减率为【fzxx.csxx.jcdlSyjjdjl * 100】%,但《附列资料（四）》加计抵减本期发生额为【getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqfse + getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqfse】元，可能未足额计提享受加计抵减政策，请再次核对是否已准确填报《附列资料（四）》加计抵减栏次"}, {"RuleXh": "4cd6e64aa61347a7be42476c50117af6", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三7_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第7行第5列“免抵退税的项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "77a2dea03e7046b1a3c16739a1612f60", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "se"}], "NmyRuleName": "9_3校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》附表二第9栏“（三） 本期用于购建不动产的扣税凭证”税额必须≥0，请检查！"}, {"RuleXh": "20ff4156cd9c4c749de6c78ea3d663ac", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "msxse"}], "NmyRuleName": "19-11校验（附表三全表非0再判断，全表为0通过）", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第19行第11列“免税：服务、不动产和无形资产--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第8行第1列“免税的项目--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "ab9397b7812d4d51b10c157e4d2a3fd7", "RelateTable": "001,002,015", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzXxynse"}], "NmyRuleName": "015_sumList_yynzzse_校验", "NmyRuleMessage": "《加油站月份加油信息明细表》“月应纳增值税额/合计”须小于等于《增值税及附加税费申报表附列资料（一）》第1行的第2列+第4列+第6列+第8列"}, {"RuleXh": "a8b328e47d1744aeba386468b7ed0104", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'37'", "Col": "fs"}], "NmyRuleName": "附表2-8a-1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第8a行加计扣除农产品进项税额 份数不得填写"}, {"RuleXh": "dc2f3be88dbc422590b14ccab2c0fbe2", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "ajybfjsxse"}], "NmyRuleName": "5_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第5行2列“（二）按简易办法计税销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "a271894b08ee4b659ae8b246aef94a88", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三3_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第3行第5列“6%税率的项目（不含金融商品转让）”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "9ee3ea89a1a74c5ea9f11f0f96243415", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三5_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第5行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "53050a307d1e4ba99e2a4d620aeb5b2b", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "23_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第23行2列“应纳税额减征额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "e063d80e64cc4e47a3ed99b460b4e34f", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三6_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第6行第5列“3%征收率的项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "1a5915a42e444876b1e1bb41239761a9", "RelateTable": "001,002,081", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "mzzzsxmxse"}], "NmyRuleName": "8_1校验", "NmyRuleMessage": "《增值税减免税申报明细表》免税项目第1列【免征增值税项目销售额】合计值【getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').mzzzsxmxse】应等于《附列资料一》第9列\"销售额\"的第18栏\"四、免税-货物及加工修理修配劳务\"【getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','18').xse】+第19栏\"四、免税-服务、不动产和无形资产\"【getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','19').xse】。"}, {"RuleXh": "5169a78e3cb646879946652fa501f998", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "nsjctzdxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kchXxynse"}], "NmyRuleName": "附表1-13c校验", "NmyRuleMessage": "该纳税人非跨省合资铁路企业不得填写《增值税及附加税费申报表附列资料（一）》13c行的相关栏次！"}, {"RuleXh": "d966b4c3393e46e59821f13dd546e97a", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "asysljsxse"}], "NmyRuleName": "主表中第2栏和第3栏和1栏（一般企业）提示校验", "NmyRuleMessage": "《增 值 税 及 附 加 税 费 申 报 表》本表第2栏次的\"其中：应税货物销售额_一般项目\"和第3栏次的\"应税劳务销售额_一般项目\"本月数都为0，但第1栏次的“（一）按适用税率计税销售额_一般项目”本月数不为0,您确认继续提交吗？"}, {"RuleXh": "d87d70a14ce54023af9a1bcc71018cf9", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "8-5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第8行第5列“免税的项目”--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第19行第12列“免税：服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "a40368f282e549d98448edecfea44bc9", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "ynse"}], "NmyRuleName": "19_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第19行4列“应纳税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "d79d76d08d22483ea5025a37927d6bf3", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "yshwxse"}], "NmyRuleName": "2_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第2行第4列“其中：应税货物销售额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "19df96cd19fa468b8a93ae858c2bdea3", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "se"}], "NmyRuleName": "税额：0 ≤ 第9行 ≤ 第1+4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第9栏“（三） 本期用于购建不动产的扣税凭证”税额必须大于等于0且不能大于第一栏“（一）认证相符的税控增值税专用发票”税额与第四栏“（二）其他扣税凭证”税额之和，请检查！"}, {"RuleXh": "1976afcc80ee41b7872358d7a8f96b00", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqydj<PERSON>"}], "NmyRuleName": "1_4 校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第1行第4列“增值税税控系统专用设备费及技术维护费--本期实际抵减税额”必须小于等于第1行第3列“增值税税控系统专用设备费及技术维护费--本期应抵减税额”。"}, {"RuleXh": "4f0746caa7aa42e78c896cd680d54297", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "fs"}], "NmyRuleName": "份数：0≤第10行≤第1行+第4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第10栏“（四）本期用于抵扣的旅客运输服务扣税凭证”份数必须大于等于0且不能大于第一栏“（一）认证相符的税控增值税专用发票”份数与第四栏“（二）其他扣税凭证”份数之和，请检查！"}, {"RuleXh": "1707f392c0874f79a660bc1b036eb8e6", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "msxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "msxse"}], "NmyRuleName": "8_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第8行2列“（四）免税销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "171705cc87d247948d0444bb79565b1e", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxse"}], "NmyRuleName": "2-11校验 (附表三全表非0才校验，全零则通过)", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第2行第11列“13%税率的服务、不动产和无形资产--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第1行第1列“13%税率的项目（16%税率也填这栏）--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "4e5162bef5fe493386c7c36e7aff6d16", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'24'", "Col": "se"}], "NmyRuleName": "附表2-24-3", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第24行（一）认证相符的增值税专用发票 税额不得填写"}, {"RuleXh": "28fee5de424e4e0390694e0a6ed2d97d", "RelateTable": "001,002,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "se"}], "NmyRuleName": "16_1校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第16栏\"按适用税率计算的纳税检查应补缴税额\"第1列必须≤《增值税及附加税费申报表附列资料（一）（本期销售情况明细）》第8列第(1+2+3+4+5)栏 + 《增值税及附加税费申报表附列资料（二）（本期进项税额明细）》第19栏。"}, {"RuleXh": "29db5c9654b14ecf8ac00332c9251c0c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "kchXxynse"}], "NmyRuleName": "附表1-17_14校验", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）第17行“服务、不动产和无形资产”第14列“销项(应纳)税额”不得有值"}, {"RuleXh": "28b0510742fe4f259d6338123c281d08", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}], "NmyRuleName": "本期填写的一般项目进项税额与附表四声明数值一般项目进项税额不一致，请重新核对后提交", "NmyRuleMessage": "本期填写的一般项目进项税额与附表四声明数值一般项目进项税额不一致，请重新核对后提交"}, {"RuleXh": "e6ab1beaaf144c13b88a3eeec478de9e", "RelateTable": "001,081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "001_23_13_校验", "NmyRuleMessage": "《增值税纳税申报表（一般纳税人适用）》表第23行1、3列“应纳税额减征额”合计值应等于《增值税减免税明细表》减税项目第4列”本期实际抵减税额”合计值。"}, {"RuleXh": "7e21c30b48d848d3b0242990b89bc466", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jxse"}], "NmyRuleName": "12_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第12行4列“进项税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "b23d3050986b44da913bd9d343fa908a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "xse"}], "NmyRuleName": "14_9校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第14行“简易计税方法计税-即征即退货物及加工修理修配劳务”第9列“合计-销售额”"}, {"RuleXh": "b0d46ad43f1c48d79b849a3395bc0e9c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "xse"}], "NmyRuleName": "7-9校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第7行“一般计税方法计税-即征即退服务、不动产和无形资产”第9列“合计-销售额”"}, {"RuleXh": "b152f4d4e2d444de9e1d4f12f61f8d60", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfYnse"}], "NmyRuleName": "（19+21）栏＞0&&第23栏必须≤第(19+21)校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》主表第23栏第3列“应纳税额减征额--即征即退--本月数”＞0时，第23栏第3列“应纳税额减征额--即征即退--本月数”必须≤主表第第3列19栏“应纳税额”+主表第21栏“简易征收办法计算的应纳税额”。"}, {"RuleXh": "b111171c76f5431f8ca4c4f98fab5233", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "je"}], "NmyRuleName": "9_2校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》附表二第9栏“（三） 本期用于购建不动产的扣税凭证”金额必须≥0，请检查！"}, {"RuleXh": "e3acd554c0af46038450ba335d05c858", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}], "NmyRuleName": "本期填写的即征即退项目进项税额与附表四声明数值即征即退项目进项税额不一致，请重新核对后提交", "NmyRuleMessage": "本期填写的即征即退项目进项税额与附表四声明数值即征即退项目进项税额不一致，请重新核对后提交"}, {"RuleXh": "e3aa71112f024c8ba063e656dcdfd0e5", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jxsezc"}], "NmyRuleName": "14_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第14行4列“进项税额转出--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "e30db282d5fc42dfb02a9bad08018190", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "xse"}], "NmyRuleName": "5_1公式", "NmyRuleMessage": "主表第5行（二）按简易办法计税销售额“一般项目”列“本月数”需大于等于《附列资料（一）》第9列第8至13b行之和-第9列第14、15行之和"}, {"RuleXh": "26585657be6a47bdb29b0ed3d5efeb61", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}], "NmyRuleName": "7-14校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第7行“一般计税方法计税-即征即退服务、不动产和无形资产”第14列“扣除后-销项(应纳)税额”"}, {"RuleXh": "f8c3b0b583bb49829970459a46291752", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三6_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第6行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "2682438377c544c2b433aa6c0dda5a91", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mdtytse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtytse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mdtytse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mdtytse"}], "NmyRuleName": "15_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第15行2列“免、抵、退应退税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "7b2ce1b985dc4988881852b71d28ea9c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "xse"}], "NmyRuleName": "6-9校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第6行“一般计税方法计税-即征即退货物及加工修理修配劳务”第9列“合计-销售额”"}, {"RuleXh": "e1a305c3690e4148b19f49402371839c", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqydj<PERSON>"}], "NmyRuleName": "4_4校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第4行第4列“销售不动产预征缴纳税款--本期实际抵减税额”必须小于等于第4行第3列“销售不动产预征缴纳税款--本期应抵减税额”。"}, {"RuleXh": "e098cff177054df8a616a3fcd5f904b5", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "7-5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第7行第5列“免抵退税的项目”--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第17行第12列“免抵退税：服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "7a2e90c5d37949f09689920d569434cb", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jybfYnse"}], "NmyRuleName": "21_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第21行2列“简易计税办法计算的应纳税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "7a141f486db044a687e2fdff1781197c", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mshwxse"}], "NmyRuleName": "9_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第9行2列“其中：免税货物销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "dfa32cbe4dd24a888b77c7ce54542a65", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "2-5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第2行第5列“9%税率的项目--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第4行第12列“9%税率的服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "217260da0e82425a9561b0b5fff6cc8e", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "msxse"}], "NmyRuleName": "5-11校验（附表三全表非0再判断，全表为0通过）", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第5行第11列“6%税率--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第3行第1列“6%税率的项目（不含金融商品转让）--本期服务、不动产和无形资产价税合计额（免税销售额）”加上《增值税及附加税费申报表附列资料（三）》第4行第1列“6%税率的金融商品转让项目--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "2152040891b54c74b7b75a9e1a8e54b9", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mslwxse"}], "NmyRuleName": "10_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第10行2列“免税劳务销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "21cd2eb6ac2a434f99df02de52865ed6", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "hjXxynse"}], "NmyRuleName": "14-10校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第14行“简易计税方法计税-即征即退货物及加工修理修配劳务”第10列“合计-销项(应纳)税额”"}, {"RuleXh": "fe2e608f94cb42789ce7b36f968729af", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三2_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第2行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "56c5f2a0cb7440f99f5e29aae039392b", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "xxse"}], "NmyRuleName": "11_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第11行4列“销项税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "55df80897345430b8f30b7775901902e", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jybfNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jybfNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jybfNsjcybjse"}], "NmyRuleName": "22_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第22行2列“按简易计税办法计算的纳税检查应补缴税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "862d3cb1d0ec4d02aabb504f9ae0cbed", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "ynse"}], "NmyRuleName": "19_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第19行2列“实际抵扣税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "ebcf607c24a54800a1149b536bd5b104", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "yslwxse"}], "NmyRuleName": "3_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第3行第2列“应税劳务销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "b78fa269eba04596b30ffc9d513e4722", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "fs"}], "NmyRuleName": "附表2-11-1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第11行（五）外贸企业进项税额抵扣证明 份数不得填写"}, {"RuleXh": "eb769dc9820948fa9853fbe0f90a580b", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "syslNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "syslNsjctzxse"}], "NmyRuleName": "4_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第4行2列“纳税检查调整的销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "8bb375514d24453f9a9a44e99b1ba219", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三3_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第3行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "8550c5a6a07f443883e52ba8d9f17b3e", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "se"}], "NmyRuleName": "税额：0 ≤ 第10行 ≤ 第1+4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第10栏“（四）本期用于抵扣的旅客运输服务扣税凭证”税额必须大于等于0且不能大于第一栏“（一）认证相符的税控增值税专用发票”税额与第四栏“（二）其他扣税凭证”税额之和，请检查！"}, {"RuleXh": "b724d2f4c0ee402babc939901de6694f", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "xse"}], "NmyRuleName": "5_3校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第5栏第3列需大于等于《增值税及附加税费申报表附列资料（一）（本期销售情况明细）》第9列第(14+15)行之和。"}, {"RuleXh": "82f7d6e3397e4ec79633a86cc5fc24dd", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "hjXxynse"}], "NmyRuleName": "7-10校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第7行“一般计税方法计税-即征即退服务、不动产和无形资产”第10列“合计-销项(应纳)税额”"}, {"RuleXh": "e99f3214d9014c928b62f4edadd4d438", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "ajybfjsxse"}], "NmyRuleName": "5_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第5行4列“（二）按简易办法计税销售额---即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "e99246e388a94ec281ae27184b5ab0a9", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "se"}], "NmyRuleName": "12_1_3校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》12栏1列“一般项目--本月数”+12栏第3列“即征即退--本月数应等于《增值税及附加税费申报表附列资料（二）（本期进项税额明细）》第12栏3列税额。"}, {"RuleXh": "b3a1084a3b274715ab1748052ac392df", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "xxse"}], "NmyRuleName": "11_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第11行2列“销项税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "b426fbf6935e4119a4fbce03814b8f51", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jybfYnse"}], "NmyRuleName": "21_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第21行4列“简易计税办法计算的应纳税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "e6faf68131804e889a7a4db4db023295", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "msxse"}], "NmyRuleName": "9b-11校验（附表三全表非0再判断，全表为0通过）", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9b行第11列“5%征收率的服务、不动产和无形资产--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第5行第1列“5%征收率的项目--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "8f5295844a0844e5af3b94c53aa65999", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "15-12校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第15行“简易计税方法计税-即征即退服务、不动产和无形资产”第12列“服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "8dd31435e21d440e849b22adfb242dd2", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "hjXxynse"}], "NmyRuleName": "15-10校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第15行“简易计税方法计税-即征即退服务、不动产和无形资产”第10列“合计-销项(应纳)税额”"}, {"RuleXh": "c0db07862f394c2093c6dac0bcacfbd2", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mdtbfckxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtbfckxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mdtbfckxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mdtbfckxse"}], "NmyRuleName": "7_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第7行2列“（二）按简易办法计税销售额--一般项目--本年累计””应等于“本月数 + 上期累计”。"}, {"RuleXh": "025d2a7ad0e5469e889d8d4838eb087e", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sqldse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "sqldse"}], "NmyRuleName": "13_3校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第13行3列“上期留抵税额--即征即退项目--本月数”=核定（即征即退期初留抵税额本月数）。"}, {"RuleXh": "024d0758a8b040899114009e2eae80e9", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxsezc"}], "NmyRuleName": "14_1加14_3校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第14行1列“进项税额转出--一般项目--本月数”+第14行3列““进项税额转出--即征即退项目--本月数”合计=《增值税及附加税费申报表附列资料（二）（本期进项税额明细）》第13栏\"税额\""}, {"RuleXh": "59cf0a069e6648bc8faba589bf795dbc", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'24'", "Col": "fs"}], "NmyRuleName": "附表2-24-1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第24行（一）认证相符的增值税专用发票 份数不得填写"}, {"RuleXh": "59a6a8213dd843f19c04554f1b641d70", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sqldse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "sqldse"}], "NmyRuleName": "13_1校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第13行1列“上期留抵税额--一般项目--本月数”=核定（一般货物服务期初留抵税额）。"}, {"RuleXh": "5a01c351da784c6689d182e28ab3dea7", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqzce"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqzce"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "填写加计抵减_校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》您当前没有一般纳税人资格，按照政策，不能填写加计抵减栏次。"}, {"RuleXh": "8bb263f59db94f35bc6ef6680638ba43", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "msxse"}], "NmyRuleName": "12-11校验（附表三全表非0再判断，全表为0通过）", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第12行第11列“3%征收率的服务、不动产和无形资产--价税合计”必须等于《增值税及附加税费申报表附列资料（三）》第6行第1列“3%征收率的项目--本期服务、不动产和无形资产价税合计额（免税销售额）”"}, {"RuleXh": "8bc8535a9cbb4a4ebc6bf32ece02ba74", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqydj<PERSON>"}], "NmyRuleName": "5_4校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第5行第4列“出租不动产预征缴纳税款--本期实际抵减税额”必须小于等于第5行第3列“出租不动产预征缴纳税款--本期应抵减税额”。"}, {"RuleXh": "58b6217824b540c6ade548f1c1d9976f", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfNsjctzxse"}], "NmyRuleName": "主表5_1", "NmyRuleMessage": "主表第5行（二）按简易办法计税销售额“一般项目”列“本月数”需大于等于第6行其中：纳税检查调整的销售额"}, {"RuleXh": "58d0560f6432458d89172f6324a0bf51", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "asysljsxse"}], "NmyRuleName": "1-2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第1行2列“（一）按适用税率计税销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "8adf964a51574e98ac90baadf1d66804", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "1-5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第1行第5列“13%税率的项目--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第2行第12列“13%税率的服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "bbe5355038de465ab5b268cf79a420f1", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "asysljsxse"}], "NmyRuleName": "1_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第1行第4列“（一）按适用税率计税销售额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "f192d703cbd843f9beedcf420f348117", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "se"}], "NmyRuleName": "10_3校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》附表二第10栏“（四）本期用于抵扣的旅客运输服务扣税凭证”税额必须大于等于0！"}, {"RuleXh": "e11cbe39905a4fa89e69236052160fb8", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三1_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第1行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "308012910ec14a28a8f02927940d479b", "RelateTable": "001,003,032", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "se"}], "NmyRuleName": "附表二的第7栏“ 代扣代缴税收缴款凭证”的“税额”列是否与“代扣代缴税收通用缴款书抵扣清单”中的税额列合计值相等", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）（本期进项税额明细）》第7行“ 代扣代缴税收缴款凭证”的”税额”【getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','7').se】应等于《代扣代缴税收通用缴款书抵扣清单》中的税额合计【round(sumList(ywbw.sbZzsYbnsrFbDkdjjksdk,'se'), 2)】，请核实。"}, {"RuleXh": "30950fd86ccf4c5f84460c8f1547c4db", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqydj<PERSON>"}], "NmyRuleName": "2_4 校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第2行第4列“分支机构预征缴纳税款--本期实际抵减税额”必须小于等于第2行第3列“分支机构预征缴纳税款 --本期应抵减税额”。"}, {"RuleXh": "63bb478503df4cdfa3b593cbf8929977", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'36'", "Col": "je"}], "NmyRuleName": "附表2-36-2", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第36行 代扣代缴税收缴款凭证 金额 不得填写"}, {"RuleXh": "679ff22cf20c4bdeb9ecf5e86a67dea5", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三4_5校验_1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第4行第5列必须小于等于0，且必须小于等于第4列。"}, {"RuleXh": "95a4988c5ab346bd8bb9ac76e7a0980c", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'36'", "Col": "fs"}], "NmyRuleName": "附表2-36-1", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第36行 代扣代缴税收缴款凭证 份数 不得填写"}, {"RuleXh": "93b338ad866740758928401222647a9f", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "yshwxse"}], "NmyRuleName": "2_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第2行第2列“（一）按适用税率计税销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "5ff41e19298e46a681351a6ed238ae0b", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "fs"}], "NmyRuleName": "份数：0≤第9行≤第1行+第4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第9栏“（三） 本期用于购建不动产的扣税凭证”份数必须大于等于0且不能大于第一栏“（一）认证相符的税控增值税专用发票”份数与第四栏“（二）其他扣税凭证”份数之和，请检查！"}, {"RuleXh": "92db2a01a854476aafce68089106e83c", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'24'", "Col": "je"}], "NmyRuleName": "附表2-24-2", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第24行（一）认证相符的增值税专用发票 金额不得填写"}, {"RuleXh": "c3cc7e7d590b4e1eabd3b3e16978adee", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'32'", "Col": "je"}], "NmyRuleName": "附表2-32-2", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第32行 代扣代缴税收缴款凭证 金额 不得填写"}, {"RuleXh": "f99b2b41d4c145d4998e78787025fa11", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "se"}], "NmyRuleName": "税额：第9+10行 ≤ 第1+4行校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第9+10栏税额合计必须小于等于第1+4栏税额合计！"}, {"RuleXh": "f94d39d1bc7c48e0b5471a3c12dbe089", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "5-5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第5行第5列“5%征收率的项目”--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第9b行第12列“5%征收率的服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "f78e56452eaa448abfea8e099e367093", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "xse"}], "NmyRuleName": "15-9校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第15行“简易计税方法计税-即征即退服务、不动产和无形资产”第9列“合计-销售额”"}, {"RuleXh": "f839c71ef0254ae89af9ad67d6d3920b", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "je"}], "NmyRuleName": "附表2-7-2", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第7行代扣代缴税收缴款凭证 金额不得填写"}, {"RuleXh": "0523b82e65294ca9a0e77b6856a14bf9", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "je"}], "NmyRuleName": "附表2-11-2", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第11行（五）外贸企业进项税额抵扣证明 金额不得填写"}, {"RuleXh": "3a13344c4e5344b7a28bb42496a1b9de", "RelateTable": "001,003,052", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "se"}], "NmyRuleName": "6_3校验", "NmyRuleMessage": "《增值税纳税申报表附列资料（二）（本期进项税额明细）》的第6栏的“农产品收购发票或者销售发票”的税额应等于《农产品核定扣除增值税进项税额计算表（汇总表）》的“当期允许抵扣农产品增值税进项税额（元）”的合计值。"}, {"RuleXh": "3ac1cc5e780543a6b1cea0a01fc4f620", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "kchXxynse"}], "NmyRuleName": "15-14校验", "NmyRuleMessage": "该纳税人未进行“增值税即征即退项目备案”不允许填写《增值税及附加税费申报表附列资料（一）》第15行“简易计税方法计税-即征即退服务、不动产和无形资产”第14列“扣除后-销项(应纳)税额”"}, {"RuleXh": "6e1b9c99475e4097a1dd981d1d1f3bbb", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三4_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第4行第5列“6%税率的金融商品转让项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值"}, {"RuleXh": "f3f1e6b9c33942a7bd2e3b8db2388e12", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jybfNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jybfNsjctzxse"}], "NmyRuleName": "6_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第6行2列“（二）按简易办法计税销售额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "474450e5d3374470b055812435b1a58e", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三8_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第8行第5列“免抵退税的项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "68cd1904eb71491c99c7692ede863393", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchHsmsxse"}], "NmyRuleName": "12_13跟14校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第[12]行14列数值不等于按公式自动计算的值，请核实"}, {"RuleXh": "679d00fee287407594a779c2d94cb449", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jxse"}], "NmyRuleName": "12_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第12行2列“进项税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "999709d1f0f444879f3701df1218f950", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfYnse"}], "NmyRuleName": "（19+21）栏＞0&&第23栏必须≤第(19+21)校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》主表第23栏第1列“应纳税额减征额--一般项目--本月数”＞0时，第23栏第1列“应纳税额减征额--一般项目--本月数”必须≤主表第第1列19栏“应纳税额”+主表第21栏“简易征收办法计算的应纳税额”。"}, {"RuleXh": "32a0f70e7e424af386612c37c42958ab", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "主表19_1校验2", "NmyRuleMessage": "主表19行应纳税额“一般项目”列“本月数”需等于第11栏“销项税额”“一般项目”列“本月数”-第18栏“实际抵扣税额”“一般项目”列“本月数”-附表四第6行第5列的“本期实际抵减额”"}, {"RuleXh": "67686550907b47deabcfa2a41aed0234", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jybfNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jybfNsjctzxse"}], "NmyRuleName": "6_4校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第6行2列“（二）按简易办法计税销售额--即征即退项目--本年累计””应等于“本月数 + 上期累计”。"}, {"RuleXh": "f326064a81f44a748cda3181fc321b58", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jxsezc"}], "NmyRuleName": "14_2校验", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第14行2列“进项税额转出--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "f22247f941c041f3ac85587526411b3d", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "se"}], "NmyRuleName": "003_10_*_校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第10栏“税额”与“金额”需同时为0或同时大于0，请检查。"}, {"RuleXh": "32db8187fc05494f9bd119ea3564df41", "RelateTable": "006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三2_5校验", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第2行第5列“9%税率的项目”--本期实际扣除金额(第五列)填写数值不能大于第一列或第四列的值 "}, {"RuleXh": "769b12264e3e485bb9c9e3d6cdd7b3fe", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqfse"}], "NmyRuleName": "4-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第4行第4列“6%税率的金融商品转让项目”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第4行第2列“6%税率的金融商品转让项目”--期初余额加上增值税及附加税费申报表附列资料（三）第4行第3列“6%税率的金融商品转让项目”--本期发生额"}, {"RuleXh": "775c60782eca45299f6825b9161b4c74", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sqldse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "sqldse"}], "NmyRuleName": "13_3公式", "NmyRuleMessage": "自动计算，当逾期=Y,主表13行3列=0，只读，否则，等于核定WSXXS.[JZJTQCLD]，核定BMDBZ=Y,可修改；"}, {"RuleXh": "ce3cc134c2754becbde077174485fc0e", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "syslNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "syslNsjctzxse"}], "NmyRuleName": "4_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第4行4列“纳税检查调整的销售额--即征即退项目--本年累计”应等于“本月数 + 上期累计”"}, {"RuleXh": "ce071400931644e2af214660ba6e6757", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "fs"}], "NmyRuleName": "003_6_1_公式", "NmyRuleMessage": ""}, {"RuleXh": "75f61ef6072d411084ffadc2b95ad018", "RelateTable": "001,301", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtsecjs"}], "NmyRuleName": "39_1公式", "NmyRuleMessage": "《增 值 税 及 附 加 税 费 申 报 表》第39行1列“城市维护建设税本期应补（退）税额”应等于《增值税及附加税费申报表附列资料（五）》对应城市维护建设税的第14列“本期应补（退）税（费）额”。"}, {"RuleXh": "cd6d451bdbc4410f8c22e46f5136e3d9", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "yshwxse"}], "NmyRuleName": "2_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "ccb6603eafb4451ab3baefe9516721c0", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_3'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_3'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "11_2公式", "NmyRuleMessage": "简易计税方法计税--3%征收率的货物及加工修理修配劳务--\t开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "cce70c98aac244a3814540e68378ed3d", "RelateTable": "001,002,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "se"}], "NmyRuleName": "16_1公式", "NmyRuleMessage": "自动计算，主表16行1列=《附表一》第8列第(1+2+3+4+5)栏 + 《附表二》第19栏 ，可修改"}, {"RuleXh": "feb45628184f4c4a991865d08a60a86f", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yskje"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yzl"}], "NmyRuleName": "《电信企业分支机构增值税汇总纳税信息传递单》的预缴税额' 4=（1+2）*3", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》的预缴税额' 4=（1+2）*3需按表样公式计算"}, {"RuleXh": "426e28333b604fb1ba39bbecf23cb48d", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}], "NmyRuleName": "8-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第8行第6列“免税的项目--期末余额”必须等于增值税及附加税费申报表附列资料（三）第8行第4列“免税的项目--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第8行第5列“免税的项目--本期实际扣除金额”"}, {"RuleXh": "42f62586d6f24e7fb71a86e895c7882b", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "yslwxse"}], "NmyRuleName": "3_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "0cb1d67237b747a1b65ebfebf9d8a067", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmwjseQjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqyjse"}], "NmyRuleName": "33_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第33行3列“其中：欠缴税额（≥0）--即征即退项目--本月数”=本表本列第(25+26-27)栏，若计算为负数，则等于0。"}, {"RuleXh": "42c16eb10c004f6997ec88e810b1c5dc", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "12_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第12行第13列“3%征收率的服务、不动产和无形资产--含税(免税)销售额”必须等于第12行第11列“3%征收率的服务、不动产和无形资产--价税合计”-第12行第12列“3%征收率的服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "4296aec1ddfc4935bb476e27353aaff7", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "13b_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13b行第13列“预征率%--含税(免税)销售额”必须等于第13b行第11列“3预征率%--价税合计”-第13b行第12列“预征率%--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "418c25dbd09744788640e8627fa405d7", "RelateTable": "001,064", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "yzl"}], "NmyRuleName": "《航空运输企业试点地区分支机构传递单》已缴纳增值税情况 有形动产租赁服务 预缴税额 公式计算", "NmyRuleMessage": "《航空运输企业试点地区分支机构传递单》已缴纳增值税情况 有形动产租赁服务 预缴税额计算"}, {"RuleXh": "41fe2b0ef55241d9b2fdbf312a1ef5e5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '21'", "Col": "yzl"}], "NmyRuleName": "13c_4公式", "NmyRuleMessage": "自动计算，不可修改"}, {"RuleXh": "0c1470bc016241c6a6225f1a70909d8f", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqyjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqyjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqyjse"}], "NmyRuleName": "27_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第27行2列“本期已缴税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "41df416d00df446ab503622578d26694", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqydj<PERSON>"}], "NmyRuleName": "减税3列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第3列\"减税项目--本期应抵减税额--合计\"应等于本表第3列\"减税项目--本期应抵减税额“明细合计。"}, {"RuleXh": "74d41d70dfcf4197bf4593e8e1d77dd5", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjdjse"}], "NmyRuleName": "4_5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第4行第5列“税额抵减情况--期末余额”应等于第4行第3列“本期应抵减税额”-第4行第4列“本期实际抵减税额”。"}, {"RuleXh": "405ea77b724d4c54ad2a9a703e78250a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "17_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第17行第13列“服务、不动产和无形资产--含税(免税)销售额”必须等于第17行第11列“服务、不动产和无形资产--价税合计”-第17行第12列“服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "74342c81c34e4dc6974790ea9ccfe730", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "hjXxynse"}], "NmyRuleName": "13a_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13a行第11列“预征率%--价税合计”必须等于第13a行第9列“预征率%--销售额”+第13a行第10列“预征率%--销项(应纳)税额”之和"}, {"RuleXh": "750ccdfdac964a049640096929a28232", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_9'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "4-1公式", "NmyRuleMessage": "fw_9_zp_je:fw_9_zp_je 取值可修改"}, {"RuleXh": "73a5754d5bf04095a03580f407911353", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqybtsecjs"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtsecjs"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqybtsecjs"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqybtsecjs"}], "NmyRuleName": "39_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第39行2列“城市维护建设税本期应补（退）税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "3f7e536a57674926bfb29e6da286f36c", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}], "NmyRuleName": "4-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第4行第6列“6%税率的金融商品转让项目--期末余额”必须等于增值税及附加税费申报表附列资料（三）第4行第4列“6%税率的金融商品转让项目--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第4行第5列“6%税率的金融商品转让项目--本期实际扣除金额”"}, {"RuleXh": "3fd54de9dd224b568791e32e39634475", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ydks<PERSON><PERSON>"}], "NmyRuleName": "18_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第18行3列“实际抵扣税额--即征即退项目--本月数”应等于本表本列（第17和第11行）孰小值。"}, {"RuleXh": "72a3651512fa4a1ebb74fd6f520ecbcc", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "qcye"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_JZFWYZJNSK'", "Col": "qcye"}], "NmyRuleName": "3_1公式", "NmyRuleMessage": "取核定可修改。"}, {"RuleXh": "fe825149538d4ad388b7331e68ac3c97", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "je"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'DDKJXSE_QCYRZXFDWSBDK'", "Col": "je"}], "NmyRuleName": "25_2公式", "NmyRuleMessage": "取核定，可修改"}, {"RuleXh": "fe6e370458de4138b305ff14a2650ab6", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqfse"}], "NmyRuleName": "8-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第8行第4列“免税的项目”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第8行第2列“免税的项目”--期初余额加上增值税及附加税费申报表附列资料（三）第8行第3列“免税的项目”--本期发生额"}, {"RuleXh": "c9fac286b8774b0986cf9c1661ab4b87", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "je"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'SBDKDJXSE_HGJKZZSZYJKS'", "Col": "je"}], "NmyRuleName": "5_2公式", "NmyRuleMessage": "自动取数，可修改"}, {"RuleXh": "ca31af2e34a4477398583e26311de828", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "xse"}], "NmyRuleName": "1_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第1行3列“（一）按适用税率计税销售额--即征即退项目--本月数”应等于《增值税及附加税费申报表附列资料（一）》第9列“合计--销售额”第9列（6+7）行。"}, {"RuleXh": "c911c8c6e38c4366a604e02f63db899b", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "wkjfpXse"}], "NmyRuleName": "19_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第19行第9列“服务、不动产和无形资产--销售额”必须等于第19行第(1+3+5+7)列之和"}, {"RuleXh": "fda224421c314b988f52d95c6f00e64d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "wkjfpXse"}], "NmyRuleName": "17_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第17行第9列必须等于第17行第(1+3+5+7)列之和"}, {"RuleXh": "c90d81315d264add93f6e364dddc1145", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "23_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第23行4列“应纳税额减征额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "fcd46b2d336d44dcbafdc47de2de7fcb", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_3'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_3'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjqtfpXse"}], "NmyRuleName": "11_2公式", "NmyRuleMessage": "简易计税方法计税--3%征收率的货物及加工修理修配劳务--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "c7e712329d9441b8a13468c04910c059", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}], "NmyRuleName": "12_1公式", "NmyRuleMessage": "自动计算，主表第12行1列=《附表二》第12行“税额” - 主表第12行第3列，可修改"}, {"RuleXh": "c7ddb21ed8e441308bcfcd0b179e09e1", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "se"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'DDKJXSE_QCYRZXFDWSBDK'", "Col": "se"}], "NmyRuleName": "25_3公式", "NmyRuleMessage": "取核定，可修改"}, {"RuleXh": "c8f25e566aa648d3a90e0b10eafe7c30", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ydks<PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sjdkse"}], "NmyRuleName": "20_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第20行1列“期末留抵税额--一般项目--本月数”=本表本列第（17-18）行。"}, {"RuleXh": "c8b59251ac1940b9ac7433dee7cb9442", "RelateTable": "001,003,052", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "se"}], "NmyRuleName": "附表二6-3公式", "NmyRuleMessage": ""}, {"RuleXh": "fc16a8c339fd4dbc92b79cf09bfee678", "RelateTable": "001,301", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtsedfjyfj"}], "NmyRuleName": "41_1公式", "NmyRuleMessage": "《增 值 税 及 附 加 税 费 申 报 表》第41行1列“地方教育附加本期应补（退）费额”应等于《增值税及附加税费申报表附列资料（五）》对应地方教育附加的第14列“本期应补（退）税（费）额”。"}, {"RuleXh": "c7b10484e7ce4ffb91a2f19f56feb720", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'38'", "Col": "se"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'JXSEZCE_YCPZZCJXSE'", "Col": "se"}], "NmyRuleName": "23栏规则", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》取核定AYCPZZCSJ23 ,可修改。"}, {"RuleXh": "fb6914f56dd74626a47cacdcd5baea7c", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mshwxse"}], "NmyRuleName": "9_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "fbe60a958bdc49faa23d805e4e10fb57", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "xse"}], "NmyRuleName": "8_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第8行1列“（四）免税销售额--一般项目--本月数”应等于《附表一》第9列（18+19）行。"}, {"RuleXh": "fbca8bcf12034346bdea1e6928192859", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "se"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'SBDKDJXSE_HGJKZZSZYJKS'", "Col": "se"}], "NmyRuleName": "5_3公式", "NmyRuleMessage": "自动取数，可修改"}, {"RuleXh": "09c8dc7ca78c49c3b7df26aa33d0aeb4", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_3'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_3'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "12_2公式", "NmyRuleMessage": "简易计税方法计税--3%征收率的服务、不动产和无形资产--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "086f77bb57ac4c77b4a4a7fea8bb027a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzXxynse"}], "NmyRuleName": "1_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第1行第10列“13%税率的货物及加工修理修配劳务 （16%税率也填这栏）--销项(应纳)税额”必须等于第1行第(2+4+6+8)列之和。"}, {"RuleXh": "08ffe3fff1f04068809eafdeba8afd1f", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "15_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第15行第13列“预征率%--含税(免税)销售额”必须等于第15行第11列“3预征率%--价税合计”-第15行第12列“预征率%--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "08c16c59daa849a3ac37cf2fd6353452", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "qcwjse"}], "NmyRuleName": "25_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第25行1列“期初未缴税额（多缴为负数）--一般项目--本月数”=核定（期初未缴本月数--一般计税）。"}, {"RuleXh": "724a86c7fe2d43c28e4eab9cac7a0698", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "sysl"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 查补税额 列计算", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“其他应税服务”--查补税额7=5*6需按表样公式计算"}, {"RuleXh": "3dc612fbe6a54d0da660133d46258c03", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "xse"}], "NmyRuleName": "17_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第17行第11列“服务、不动产和无形资产--价税合计”必须等于第17行第9列“服务、不动产和无形资产--销售额”+第17行第10列“服务、不动产和无形资产--销项(应纳)税额”之和"}, {"RuleXh": "72186cc9d3ed428a87159ba5ba00ad36", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "syslNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "syslNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "syslNsjcybjse"}], "NmyRuleName": "16_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "3cbd547052844f47b0d05a0d358a2f9d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "hjXxynse"}], "NmyRuleName": "2_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第2行第11列“13%税率的服务、不动产和无形资产 （16%税率也填这栏）--价税合计”必须等于第2行第9列“13%税率的服务、不动产和无形资产 （16%税率也填这栏）--销售额”+第2行第10列“13%税率的服务、不动产和无形资产 （16%税率也填这栏）--销项(应纳)税额”之和。"}, {"RuleXh": "3c6e81806b7a457abf791ead532b896d", "RelateTable": "001,064", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "xse"}], "NmyRuleName": "《航空运输企业试点地区分支机构传递单》的已缴纳增值税情况 计算公式", "NmyRuleMessage": "航空运输企业试点地区分支机构传递单》的已缴纳增值税情况 销售额 小计"}, {"RuleXh": "71536b7970f54d14811b464b6c6a2d12", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_13'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_13'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXse"}], "NmyRuleName": "1_4公式", "NmyRuleMessage": "一般计税方法计税--13%税率的货物及加工修理修配劳务--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "3d10e1cc08d74a31a8ec1be18ed5bd0e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_3'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_3'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjqtfpXse"}], "NmyRuleName": "12_4公式", "NmyRuleMessage": "简易计税方法计税--3%征收率的服务、不动产和无形资产--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "3d2098d8732d4bf1a8831fc8aa081a1f", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '20'", "Col": "yzl"}], "NmyRuleName": "13b-4公式", "NmyRuleMessage": "自动计算不提示"}, {"RuleXh": "70aa647309e94f5380c3befc1c0a895e", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "qcye"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_XSBDCYZJNSK'", "Col": "qcye"}], "NmyRuleName": "4_1公式", "NmyRuleMessage": "取核定可修改。"}, {"RuleXh": "70c643bf27894af8a1d6e5d1de02eb2e", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "jshj"}], "NmyRuleName": "6-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第6行第1列“3%征收率的项目”--本期服务、不动产和无形资产价税合计额（免税销售额）必须等于《增值税及附加税费申报表附列资料（一）》第12行第11列“3%征收率的服务、不动产和无形资产”--价税合计"}, {"RuleXh": "3b46990fd8764a01ac495b42ef9fd3d2", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'MSHWJJGXLXPLW'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "18-1 公式", "NmyRuleMessage": "hw_ms_zp_je:hw_ms_zp_je 取值 可修改"}, {"RuleXh": "7018a23dfd7649c7b9ee82bc8eba7956", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSL_4'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "", "NmyRuleMessage": "hw_4_zp_je:hw_4_zp_je 取值 可修改"}, {"RuleXh": "6faf35bdced34babb938e650bbada20a", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqybtse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "fcyjse"}], "NmyRuleName": "34_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第34行3列“本期应补(退)税额--即征即退项目--本月数”=本表本列第(24-28-29)行。"}, {"RuleXh": "4cbc9dd1f9ec4f52823ebff7fe49c419", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmwjcbse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjcbse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqrkcbse"}], "NmyRuleName": "38_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第38行1列“期末未缴查补税额--一般项目--本月数”=本表本列第(16+22+36-37)行。"}, {"RuleXh": "1629ec35bdb844b0839fa9fc56265114", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqrkcbse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqrkcbse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqrkcbse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqrkcbse"}], "NmyRuleName": "37_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第37行2列“本期入库查补税额--一般项目--本年累计”应等于“本月数+上期累计”。"}, {"RuleXh": "4c923923815349728c40f29dff240c87", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "ssckkjzyjkstse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ssckkjzyjkstse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "ssckkjzyjkstse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "ssckkjzyjkstse"}], "NmyRuleName": "26_2公式", "NmyRuleMessage": "《增值税纳税申报表（一般纳税人适用）》第26行2列“实收出口开具专用缴款书退税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "a3b86f05d44f4f53b8552c12ac4b9271", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_5'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_5'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjqtfpXse"}], "NmyRuleName": "9b_4公式", "NmyRuleMessage": "简易计税方法计税--5%征收率的服务、不动产和无形资产--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "a3a8c63c19c54cf1bb1ddb8fadb5ac4f", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "wkjfpXse"}], "NmyRuleName": "5_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "4bc542b774b74ee4bf9bdbdac37bc9d2", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第19行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第19行12列应等于《增值税及附加税费申报表附列资料（三）》第8行5列"}, {"RuleXh": "4c624edb9e2a472c9742d82e01b7d35a", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'37'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "se"}], "NmyRuleName": "4_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第4行第3列必须等于第5+6+7+8a+10b栏之和。"}, {"RuleXh": "4b6ccf029f8e4204879ec89856eaff27", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "je"}], "NmyRuleName": "4_2公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第4行第2列必须等于第5+6+7+8a+9b栏之和。"}, {"RuleXh": "d675ce366a714e099b14e90010c1d1ee", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "se"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'JXSEZCE_SQLDSETS'", "Col": "se"}], "NmyRuleName": "22_1公式", "NmyRuleMessage": "取核定，可修改"}, {"RuleXh": "a2b43f6d702644e2a0c532702be93b05", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'29'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'30'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'31'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'32'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'33'", "Col": "fs"}], "NmyRuleName": "29_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第29行第1列“（二）其他扣税凭证--份数”必须等于第30行第1列+第31行第一列+第32行第1列+第33行第1列之和"}, {"RuleXh": "d601f846aa184d20ae1f4e8d45318ade", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jxse"}], "NmyRuleName": "12_4公式", "NmyRuleMessage": "自动计算，当逾期=Y,主表12行4列=0,只读，否则，等于本月数 + 核定LSXXS.[50],可修改；"}, {"RuleXh": "a184e55db1f84fb9ba24b51d91407900", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "hjXxynse"}], "NmyRuleName": "13c_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13c行第11列“预征率%--价税合计”必须等于第13c行第9列“预征率%--销售额”+第13c行第10列“预征率%--销项(应纳)税额”之和"}, {"RuleXh": "d4d01cd89dc54d959e9f045d6dcd622a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kchHsmsxse"}], "NmyRuleName": "4-14公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）第4行“9%税率的服务、不动产和无形资产”第12列“服务、不动产和无形资产扣除项目本期实际扣除金额”等于0时，增值税及附加税费申报表附列资料（一）第4行“9%税率的服务、不动产和无形资产”第14列“销项(应纳)税额”等于增值税及附加税费申报表附列资料（一）第2行“13%税率的服务、不动产和无形资产”第10列“价税合计”。否则，销项(应纳)税额等于含税(免税)销售额除以100%加税率乘以税率"}, {"RuleXh": "d43e9c02d3504e86bb72407fac9fb8c2", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "wkjfpXxynse"}], "NmyRuleName": "13a_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13a行第10列“预征率%--销项(应纳)税额”必须等于第13a行第(2+4+6+8)列之和"}, {"RuleXh": "d3e98e1c5a4b4469ba3c41eb676774eb", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjcbse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "qcwjcbse"}], "NmyRuleName": "36_1公式", "NmyRuleMessage": "第36行1列“期初未缴查补税额--一般项目--本月数”=核定WSXXS.[QCWJCBSEBYS]，可修改。"}, {"RuleXh": "d39c19ed5c0a4a94a99577982b7dab76", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ssckkjzyjkstse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqyjse"}], "NmyRuleName": "32_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第32行1列“期末未缴税额（多缴为负数）--一般项目--本月数”=本表本列第(24+25+26-27)行。"}, {"RuleXh": "15933e202ceb42c59033f19cc9d5924e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'JYJSFFJSQBZSXM_ZSL'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'JYJSFFJSQBZSXM_ZSL'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "8_2公式", "NmyRuleMessage": "简易计税方法计税--6%征收率--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "15f9efc48428437b89364c4211283382", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynsehj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "ynsehj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "ynsehj"}], "NmyRuleName": "24_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第24行2列“应纳税额合计--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "157b8dbb4baa4c28a363c0e293dd60f5", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'8'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "cbxse"}], "NmyRuleName": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏 取值规则", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏需等于 征税项目小计值"}, {"RuleXh": "154d128696b24cb39e33fe9a111e6941", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqyjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "bqyjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "bqyjse"}], "NmyRuleName": "27_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第27行4列“本期已缴税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "4a3ce97ed35c4d02a4acb0b2057d2a68", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}], "NmyRuleName": "11_1公式", "NmyRuleMessage": "主表第11行销项税额“一般项目”列“本月数”需等于“《附列资料（一）》（第10列第1、3行之和-第10列第6行）+（第14列第2、4、5行之和-第14列第7行）”"}, {"RuleXh": "4a3c1aa5daab4e2fa0a87ba31ec4c10f", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "2_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第2行第13列““13%税率的服务、不动产和无形资产 （16%税率也填这栏）--含税(免税)销售额”必须等于第2行11列“13%税率的服务、不动产和无形资产 （16%税率也填这栏）--价税合计”+第2行12列“13%税率的服务、不动产和无形资产 （16%税率也填这栏）--服务、不动产和无形资产扣除项目本期实际扣除金额”之和"}, {"RuleXh": "1328bb27afd94266950dc4cb4eea8af1", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "13a_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13a行第13列“预征率%--含税(免税)销售额”必须等于第13a行第11列“3预征率%--价税合计”-第13a行第12列“预征率%--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "489ed7cdb76c47ba91d91ba1d6eb5572", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqfse"}], "NmyRuleName": "减税2列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第2列\"减税项目--本期发生额--合计\"应等于本表第2列\"减税项目--本期发生额“明细合计。"}, {"RuleXh": "129cdddb46cb4ddf9b31b602a0e1f53e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'YBJSFFJSQBZSXM_SL'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "5-1 公式", "NmyRuleMessage": "fw_6_zp_je:fw_6_zp_je 取值 可修改"}, {"RuleXh": "493d2af65cb441ea8f104b58ec790243", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '4'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三4_5公式", "NmyRuleMessage": ""}, {"RuleXh": "12fcbbe7365e442194cb802b3ed461b0", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzdxse"}], "NmyRuleName": "4_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第4行第9列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--销售额”必须等于第4行第(1+3+5+7)列之和"}, {"RuleXh": "12ce8e2d0c024d059e34139c7336f49b", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'JYJSFFJSQBZSXM_ZSL'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'JYJSFFJSQBZSXM_ZSL'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjqtfpXse"}], "NmyRuleName": "8_4公式", "NmyRuleMessage": "简易计税方法计税--6%征收率--开具其他发票--销项(应纳)税额,自动计算，可修改"}, {"RuleXh": "12d485d482284cfa8b1d95cdb3610361", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jzjtsjtse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jzjtsjtse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jzjtsjtse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jzjtsjtse"}], "NmyRuleName": "35_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第35行4列“本期应补(退)税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "477ed8acce994b40ba97ccebbb5e61bd", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}], "NmyRuleName": "6-6", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第6行第6列“3%征收率的项目--期末余额”必须等于增值税及附加税费申报表附列资料（三）第6行第4列“3%征收率的项目--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第6行第5列“3%征收率的项目--本期实际扣除金额”"}, {"RuleXh": "4758766cbba8472081131adcc6a153ce", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "jshj"}], "NmyRuleName": "2-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第2行第1列“9%税率的项目--本期服务、不动产和无形资产价税合计额（免税销售额）”必须等于《增值税及附加税费申报表附列资料（一）》第4行第11列“9%税率的服务、不动产和无形资（10%税率也填这栏）--价税合计”"}, {"RuleXh": "47e7b067c2be4a0ab027894dd2f855b0", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "bqjnqjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjnqjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "bqjnqjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "bqjnqjse"}], "NmyRuleName": "31_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第31行4列“④本期缴纳欠缴税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "d28b671f2dd24f1abfcf8bc79cc3a8b3", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "sysl"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 查补税额 列计算", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“基础电信服务”--查补税额7=5*6需按表样公式计算"}, {"RuleXh": "d2ff81d9560943f9ab35575910272479", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jybfNsjcybjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jybfNsjcybjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jybfNsjcybjse"}], "NmyRuleName": "22_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "d1119d318c36408c8d4d03d8ee7d1ebd", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_13'", "Col": "kjqtfpXse"}], "NmyRuleName": "2_3公式", "NmyRuleMessage": "取fw_13_pp_je:fw_13_pp_je，可修改"}, {"RuleXh": "cf97d30c5b654711b486631b8fab1830", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "wkjfpXxynse"}], "NmyRuleName": "12_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第12行第10列“3%征收率的服务、不动产和无形资产--销项(应纳)税额”必须等于第11行第(2+4+6+8)列之和"}, {"RuleXh": "d04b2b341317421bbc69b08f11605a48", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第4行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第4行12列应等于《增值税及附加税费申报表附列资料（三）》第2行第5列。"}, {"RuleXh": "11edc54c35734079be75a7826468a641", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}], "NmyRuleName": "7-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第7行第4列“免抵退税的项目 ”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第7行第2列“免抵退税的项目”--期初余额加上增值税及附加税费申报表附列资料（三）第7行第3列“免抵退税的项目 ”--本期发生额"}, {"RuleXh": "10f64c65490d430a929a0e82ce3fd588", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'YBJSFFJSQBZSXM_SL'", "Col": "kjqtfpXse"}], "NmyRuleName": "5_3公式", "NmyRuleMessage": "取fw_6_pp_je:fw_6_pp_je，可修改"}, {"RuleXh": "10b4daa592ac4c618bcfabbd636879ef", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qcye"}], "NmyRuleName": "减税1列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第1列\"减税项目--期初余额--合计\"应等于本表第1列\"减税项目--期初余额“明细合计。"}, {"RuleXh": "11254163746c4f53891d8ab48c4438d2", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_5'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_5'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "9b_2公式", "NmyRuleMessage": "简易计税方法计税--5%征收率的服务、不动产和无形资产--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "0fb4f1be55324e6d8567ed76ca5dfeae", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '20'", "Col": "yzl"}], "NmyRuleName": "13b-2公式", "NmyRuleMessage": "自动计算，不提示"}, {"RuleXh": "7940db0b43134faabc8fcfd9a93a05f2", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jybfNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jybfNsjctzxse"}], "NmyRuleName": "6_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "4585620061424776bd0ce39244287b4c", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "xxse"}], "NmyRuleName": "11_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "7878c7094f264670bc1599f591758496", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "wkjfpXse"}], "NmyRuleName": "13b_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13b行第9列“预征率%--销售额”必须等于第13b行第(1+3+5+7)列之和"}, {"RuleXh": "0e730cb496dd44eeb95667ce46e0d00a", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "jshj"}], "NmyRuleName": "5-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第5行第1列“5%征收率的项目 ”--本期服务、不动产和无形资产价税合计额（免税销售额）必须等于《增值税及附加税费申报表附列资料（一）》第9b行11列“5%征收率的服务、不动产和无形资产”--价税合计"}, {"RuleXh": "78b9b78bb842413fa4e7ee320f176abd", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}], "NmyRuleName": "免税2列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第2列\"免税项目--免税销售额扣除项目本期实际扣除金额--合计\"应等于本表第2列\"免税项目--免税销售额扣除项目本期实际扣除金额\"明细合计。"}, {"RuleXh": "7893ac81a0ec47fca8ce88d7d326a274", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mslwxse"}], "NmyRuleName": "10_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "77858fec271742a292be1651d5885812", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '20'", "Col": "yzl"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '20'", "Col": "yzl"}], "NmyRuleName": "13b-14公式", "NmyRuleMessage": "自动取值不提示"}, {"RuleXh": "4406111ba9f642bdba31bf8ab118bc66", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzdxse"}], "NmyRuleName": "3_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第3行第9列“9%税率的货物及加工修理修配劳务 （10%税率也填这栏）--销售额”必须等于第3行第(1+3+5+7)列之和。"}, {"RuleXh": "78244a5e289940189e00d539e9552619", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_5'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_5'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjqtfpXse"}], "NmyRuleName": "9a_2公式", "NmyRuleMessage": "简易计税方法计税--5%征收率的货物及加工修理修配劳务--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "780c8c6c523745dfa9c48400a8e7334f", "RelateTable": "001,003,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqzce"}], "NmyRuleName": "附表四第6行第3列“本期调减额”_公式", "NmyRuleMessage": ""}, {"RuleXh": "20851185b5344b66a5ee13fd4353794a", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第9b行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9b行12列应等于《增值税及附加税费申报表附列资料（三）》第5行5列"}, {"RuleXh": "1f8b2cca976944bea32b98a3b6cffe09", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_9'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_9'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjqtfpXse"}], "NmyRuleName": "4_4公式", "NmyRuleMessage": "一般计税方法计税--9%税率的服务、不动产和无形资产--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "1f5c0f30c11f41c1b9ace837202dd165", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "7_13公式", "NmyRuleMessage": "第7行第13列必须等于第7行第(11-12)列之和"}, {"RuleXh": "abc9866735384859a4bc6c2327faa17e", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}], "NmyRuleName": "8_2公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第8行2列“加计抵减情况--本期发生额合计”应等本表本列明细之和。"}, {"RuleXh": "aba43ffbc30143498ab7109d074ca8a3", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_3'", "Col": "kjqtfpXse"}], "NmyRuleName": "12_3公式", "NmyRuleMessage": "取fw_3_pp_je:fw_3_pp_je，可修改"}, {"RuleXh": "ab5ad70a75e845cc9212a6704c30944d", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kchmsxse"}], "NmyRuleName": "免税3列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第3列\"免税项目--扣除后免税销售额--合计\"应等于本表第3列\"免税项目--扣除后免税销售额\"明细合计。"}, {"RuleXh": "ddb92c566bc94c949c103e0076a80fd7", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'35'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "fs"}], "NmyRuleName": "35_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第35行第1列“本期认证相符的税控增值税专用发票--份数”必须等于第2行第1列“其中：本期认证相符且本期申报抵扣--份数”+第26行第1列“本期认证相符且本期未申报抵扣--份数”之和"}, {"RuleXh": "aa846410891c4d3592692bec922bad74", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "je"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'SBDKDJXSE_BQRZXFQBQSBDK'", "Col": "je"}], "NmyRuleName": "2_2公式", "NmyRuleMessage": "自动取数，可修改"}, {"RuleXh": "aa80721172c243e18181caa1b8e3a611", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "19_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第19行3列“应纳税额--即征即退项目--本月数”应等于本表本列（11-18)栏 - 《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第7行第5列\"本期实际抵减额\"。"}, {"RuleXh": "aa8daadb2d56469e91c12c17ecce1b41", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXse"}], "NmyRuleName": "1_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "dd6bd7f0250043bdaa992a8b2b47cb96", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "jshj"}], "NmyRuleName": "7-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第7行第1列“免抵退税的项目”--本期服务、不动产和无形资产价税合计额（免税销售额）必须等于增值税及附加税费申报表附列资料（一）》第17行第11列“服务、不动产和无形资产”--价税合计"}, {"RuleXh": "dd62875a693b46069c83c342495010b6", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "xxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "xxse"}], "NmyRuleName": "11_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "aa0008b230e24410be797eac42f52514", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "fs"}], "NmyRuleName": "12_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第12行1列“当期申报抵扣进项税额合计--份数”必须等于第1行第1列“（一）认证相符的税控增值税专用发票--份数”+第4行第1列“（二）其他扣税凭证--份数”之和"}, {"RuleXh": "2a927daac31844a88420916f2c7b7413", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'JJDJQK_JZJTXMJJDJEJS'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}], "NmyRuleName": "附表四 7-2公式", "NmyRuleMessage": null}, {"RuleXh": "a8d12e852cb6452dac7596b373dc6410", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qcwjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "qcwjse"}], "NmyRuleName": "25_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第25行3列“期初未缴税额（多缴为负数）--即征即退项目--本月数”=核定（期初未缴本月数--即征即退）。"}, {"RuleXh": "dcaa9ea23b64412d901c515b2961e0bb", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "wkjfpXxynse"}], "NmyRuleName": "9b_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9b行第10列“5%征收率的服务、不动产和无形资产--销项(应纳)税额”必须等于第9b行第(2+4+6+8)列之和"}, {"RuleXh": "dc8355e7c0e34a3ea92e7994894a4b10", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzXxynse"}], "NmyRuleName": "4_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第4行第10列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--销项(应纳)税额”必须等于第4行第(2+4+6+8)列之和"}, {"RuleXh": "dc4da6d8399843deaa7823dbbfe43069", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "24_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第24行1列“应纳税额合计--一般项目--本月数””=本表此列第(19+21-23)行。"}, {"RuleXh": "a9147017ce954892adf9b4d8777ebcfc", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'16'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'16'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'16'", "Col": "wkjfpXse"}], "NmyRuleName": "16_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第16行第9列必须等于第16行第(1+3+5+7)列之和"}, {"RuleXh": "db9ca344dba646e1a888a65f90e926b5", "RelateTable": "001,073", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqybtse"}], "NmyRuleName": "应补（退）税额", "NmyRuleMessage": "《汇总纳税企业增值税分配表》应补（退）税额应等于主表第34栏第1列\"一般项目\"+第3列\"即征即退项目\"的\"本月数\"之和。"}, {"RuleXh": "da98cebf0b784f2c802335974b886409", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jybfNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jybfNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jybfNsjctzxse"}], "NmyRuleName": "6_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "53366bd43ad64e8cbad5d0aae6badc93", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqfse"}], "NmyRuleName": "3-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第3行第4列“6%税率的项目（不含金融商品转让）”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第3行第2列“6%税率的项目（不含金融商品转让）”--期初余额加上增值税及附加税费申报表附列资料（三）第3行第3列“6%税率的项目（不含金融商品转让）”--本期发生额"}, {"RuleXh": "1bfe31a41fce4216b9bb2b35e96e4be6", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jxsezc"}], "NmyRuleName": "14_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "529af152c49043398e67aec79d4d4149", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "wkjfpXse"}], "NmyRuleName": "9a_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "5314bc1eda8e4dd2b9d87bfebaa41c48", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第17行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第17行12列应等于《增值税及附加税费申报表附列资料（三）》第7行5列"}, {"RuleXh": "52e86c4ddea94f7fb695b72d23773849", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjdjse"}], "NmyRuleName": "1_5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第1行第5列“税额抵减情况--期末余额”应等于第1行第3列“本期应抵减税额”-第1行第4列“本期实际抵减税额”。"}, {"RuleXh": "52d9eb48105a4bc8993e14a929e18d67", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "msxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "msxse"}], "NmyRuleName": "8_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "51c9c1b48e8a4b598db50220c3ec77d3", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "wkjfpXse"}], "NmyRuleName": "9a_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9a行第9列“5%征收率的货物及加工修理修配劳务--销售额”必须等于第8行第(1+3+5+7)列之和"}, {"RuleXh": "1b8b5f3859d44e48b021c0f63523d1eb", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_9'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_9'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjqtfpXse"}], "NmyRuleName": "3_4公式", "NmyRuleMessage": "一般计税方法计税--9%税率的货物及加工修理修配劳务--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "1b96daa603e144efa83aaf8eb306d3b3", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'MDTSFW_BDCHWXZC'", "Col": "kjqtfpXse"}], "NmyRuleName": "17_3公式", "NmyRuleMessage": "取fw_ckts_pp_je:fw_ckts_pp_je，可修改"}, {"RuleXh": "51e6ff68561244328e1a8d3c4ee05a7a", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mdtytse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtytse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mdtytse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mdtytse"}], "NmyRuleName": "15_2公式", "NmyRuleMessage": "自动计算，当逾期=Y,第15行2列=0.只读，否则等于本月数 + 核定LSXXS.[15],可修改；"}, {"RuleXh": "50ed2870f9634f42a3c25cd443b985e6", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "fs"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'SBDKDJXSE_BQRZXFQBQSBDK'", "Col": "fs"}], "NmyRuleName": "2_1公式", "NmyRuleMessage": "自动取数，可修改"}, {"RuleXh": "518820163ed643f78085cb585d0b9cd8", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_3'", "Col": "kjqtfpXse"}], "NmyRuleName": "11_3公式", "NmyRuleMessage": "取hw_3_pp_je:hw_3_pp_je，可修改"}, {"RuleXh": "a7ebe8b0d6b642f2a2b422a31b41d864", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "hjXxynse"}], "NmyRuleName": "5_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第5行第10列“6%税率--价税合计”必须等于第5行第9列“6%税率--销售额”+第5行第10列“6%税率--销项(应纳)税额”之和"}, {"RuleXh": "509adf19dfd440f5b522c3f20c3f6cb0", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "wkjfpXxynse"}], "NmyRuleName": "11_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第11行第10列“3%征收率的货物及加工修理修配劳务--销项(应纳)税额”必须等于第11行第(2+4+6+8)列之和"}, {"RuleXh": "d9ff30b0b027402387c575432cfe0ac8", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "wkjfpXse"}], "NmyRuleName": "10_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第10行第9列“4%征收率--销售额”必须等于第10行第(1+3+5+7)列之和"}, {"RuleXh": "da58fb6817fd40bcae6a7ec4332e81c4", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "qcwjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "qcwjse"}], "NmyRuleName": "25_4公式", "NmyRuleMessage": "《增值税纳税申报表（一般纳税人适用）》第25行4列“期初未缴税额（多缴为负数）--即征即退项目--本年累计=核定（期初未缴本年累计--即征即退）。"}, {"RuleXh": "a450b588c7d34345b09089ac35f886f3", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "syslNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "syslNsjctzxse"}], "NmyRuleName": "4_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "d8d9a0fa957240e8b939147413084d42", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'MSHWJJGXLXPLW'", "Col": "kjskzzszyfpXxynse"}], "NmyRuleName": "18_2公式", "NmyRuleMessage": "取hw_ms_zp_se:hw_ms_zp_se，可修改"}, {"RuleXh": "d77763bcea1441d8a008d02c6d49f9e4", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '8'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三8_5公式", "NmyRuleMessage": ""}, {"RuleXh": "194d372dd5a044d28a6d628aab3e9f2b", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqfse"}], "NmyRuleName": "5-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第5行第4列“5%征收率的项目 ”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第5行第2列“5%征收率的项目 ”--期初余额加上增值税及附加税费申报表附列资料（三）第5行第3列“5%征收率的项目 ”--本期发生额"}, {"RuleXh": "199881673b9e426798256a13302ba3ae", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "19_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第19行第13列“服务、不动产和无形资产--含税(免税)销售额”必须等于第19行第11列“服务、不动产和无形资产--价税合计”-第19行第12列“服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "1931b29af4524cf79fa5d23e465801d9", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "kchXxynse"}], "NmyRuleName": "15_14公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）当第15行第12列=0时，本行14列=第10列值；"}, {"RuleXh": "4eb18e224480419fb85c14f596f9b8e0", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "msxse"}], "NmyRuleName": "3-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第3行第3列“6%税率的项目（不含金融商品转让）”应等于《《增值税及附加税费申报表附列资料（一）》第5行第11列》减去《增值税及附加税费申报表附列资料（三）》第4行第1列“6%税率的金融商品转让项目”"}, {"RuleXh": "2a42c395737e43ebafe18646b39622d8", "RelateTable": "001,064", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "yjse"}], "NmyRuleName": "《航空运输企业试点地区分支机构传递单》的已缴纳增值税情况 计算公式", "NmyRuleMessage": "航空运输企业试点地区分支机构传递单》的已缴纳增值税情况 预缴税额 小计"}, {"RuleXh": "2a462c6df46e462e983fc9d5f5a0f364", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'27'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "se"}], "NmyRuleName": "27_3公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "2926afcceb5a4eeca9cea264e0f483dd", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'16'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'MDTSHWJJGXLXPLW'", "Col": "kjqtfpXse"}], "NmyRuleName": "16_3公式", "NmyRuleMessage": "取hw_ckts_pp_je:hw_ckts_pp_je，可修改"}, {"RuleXh": "28282c66ca5b45e88ef9bf31e46014a8", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '3'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三3_5公式", "NmyRuleMessage": ""}, {"RuleXh": "27e44036b9cb4e1e93a74792abf50a0e", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "se"}], "NmyRuleName": "14_1公式", "NmyRuleMessage": "自动计算，逾期时，主表第14行1列=0，只读，否则等于《附表二》第13栏\"税额\" ，可修改"}, {"RuleXh": "281596572bec499c8d77ffc3b0ca085d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_9'", "Col": "kjqtfpXse"}], "NmyRuleName": "4_3公式", "NmyRuleMessage": "取fw_9_pp_je:fw_9_pp_je，可修改"}, {"RuleXh": "28139392d582447f97a48f117a0b5e4a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "hjXxynse"}], "NmyRuleName": "13b_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13b行第11列“预征率%--价税合计”必须等于第13b行第9列“预征率%--销售额”+第13b行第10列“预征率%--销项(应纳)税额”之和"}, {"RuleXh": "b3565619fa1d45ea961afd649a2f89db", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "yslwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "yslwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "yslwxse"}], "NmyRuleName": "3_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "b30dc9f59e1d4d1596840aa33fe3618a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "wkjfpXxynse"}], "NmyRuleName": "13c_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13c行第10列“预征率%--销项(应纳)税额”必须等于第13c行第(2+4+6+8)列之和"}, {"RuleXh": "7f0c9b7fed0348ea8a40e3d39625d103", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '13'", "Col": "yzl"}], "NmyRuleName": "13a-6公式", "NmyRuleMessage": "自动计算不提示"}, {"RuleXh": "b1eb0466f18e42c9bdd3fa524484c294", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "wkjfpXxynse"}], "NmyRuleName": "9a_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9a行第10列“5%征收率的货物及加工修理修配劳务--销项(应纳)税额”必须等于第9a行第(2+4+6+8)列之和"}, {"RuleXh": "7e58670b76e14501b276a6eb007e0f83", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjdjse"}], "NmyRuleName": "减税4列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第4列\"减税项目--本期实际抵减税额--合计\"应等于本表第4列\"减税项目--本期实际抵减税额“明细合计。"}, {"RuleXh": "b19d5086aa2849958c30965c7f9099b8", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtbfckxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'16'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "xse"}], "NmyRuleName": "7_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第7行1列“（三）免、抵、退办法出口销售额--一般项目--本月数”应等于《附表一》第9列（16+17）行。"}, {"RuleXh": "b24dd3b77a1243e082283af8514c83b5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_13'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_13'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjqtfpXse"}], "NmyRuleName": "2_4公式", "NmyRuleMessage": "一般计税方法计税--13%税率的服务、不动产和无形资产--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "e64fe8e4e94648188ae01f4c397f27e6", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第5行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第5行12列应等于《增值税及附加税费申报表附列资料（三）》第3行5列+4行第5列。"}, {"RuleXh": "7ea77139c621479a9bb9c9b372e4294a", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqfse"}], "NmyRuleName": "3_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第3行第3列“税额抵减情况--本期应抵减税额”应等于第3行第1列“期初余额”+第3行第2列“本期发生额”。"}, {"RuleXh": "7e73b786e26e4e8285900fb473f631a5", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "ajybfjsxse"}], "NmyRuleName": "5_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "e63e1ad6500a40289f6668ec28238990", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "fs"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'SBDKDJXSE_HGJKZZSZYJKS'", "Col": "fs"}], "NmyRuleName": "5_1公式", "NmyRuleMessage": "自动取数，可修改"}, {"RuleXh": "b0fcf54c722b4d5bb048a7f1502cc8ec", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "asysljsxse"}], "NmyRuleName": "1_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "b0f26d36c5a4405e887970f92ae5ec8c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "hjXxynse"}], "NmyRuleName": "4_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第4行第11列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--价税合计”必须等于第4行第9列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--销售额”+第4行第10列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--销项(应纳)税额”之和"}, {"RuleXh": "7d836dba32b24d078965a8fff1f757b4", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "qcwjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "qcwjse"}], "NmyRuleName": "25_2公式", "NmyRuleMessage": "《增值税纳税申报表（一般纳税人适用）》第25行2列“期初未缴税额（多缴为负数）--一般项目--本年累计”=核定（期初未缴本年累计--一般计税）。"}, {"RuleXh": "e4ffbbe0cc784dfe983938b4be8993bc", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ydks<PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}], "NmyRuleName": "20_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第20行3列“期末留抵税额--即征即退项目--本月数”=本表本列第（17-18）行。"}, {"RuleXh": "b0e9916c41814ec68a6d24aee5d7f30a", "RelateTable": "001,003,301", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "se"}], "NmyRuleName": "第8行“当期新增可用于扣除的留抵退税额”", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（五）》第8行“当期新增可用于扣除的留抵退税额”应等于《增值税及附加税费申报表附列资料（二）》第22栏税额。"}, {"RuleXh": "e557ea5b207c4c33b89924b18981a5f4", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "wkjfpXse"}], "NmyRuleName": "9b_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "e55ec5b972964890b6a947d217f0d88e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "hjXxynse"}], "NmyRuleName": "7_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第7行第11列“即征即退服务、不动产和无形资产--价税合计”必须等于第7行第9列“即征即退服务、不动产和无形资产--销售额”+第7行第10列“即征即退服务、不动产和无形资产--销项(应纳)税额”之和"}, {"RuleXh": "e4278ec44f74450ebc564fb93a9accdb", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "syslNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "syslNsjctzxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "syslNsjctzxse"}], "NmyRuleName": "4_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "e407fd032c3c4e0688921a315a5e307d", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "qcwjcbse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "qcwjcbse"}], "NmyRuleName": "36_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第36行2列“期初未缴查补税额--一般项目--本年累计”应等于“上期累计”。"}, {"RuleXh": "e3d4160077a84854b627d1cc213952aa", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kchHsmsxse"}], "NmyRuleName": "9b-14公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）第9b行“5%征收率的货物及加工修理修配劳务”第12列“服务、不动产和无形资产扣除项目本期实际扣除金额”等于0时，增值税及附加税费申报表附列资料（一）第9b行“5%征收率的货物及加工修理修配劳务”第14列“销项(应纳)税额”等于增值税及附加税费申报表附列资料（一）第9b行“5%征收率的货物及加工修理修配劳务”第10列“价税合计”。否则，销项(应纳)税额等于含税(免税)销售额除以100%加税率乘以税率"}, {"RuleXh": "e2a8b3d78cc34beab829ffbe678f827d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_5'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "9a-1公式", "NmyRuleMessage": "hw_5_zp_je:hw_5_zp_je 取值 可修改"}, {"RuleXh": "e2b301655e174bb7a07c593abd7eb9ad", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_13'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "1-1公式", "NmyRuleMessage": "取hw_13_zp_je:hw_13_zp_je值 可修改"}, {"RuleXh": "2605e60266634d67bb533e89809a7a1b", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSL_4'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSL_4'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjqtfpXse"}], "NmyRuleName": "10_4公式", "NmyRuleMessage": "简易计税方法计税--4%征收率--开具其他发票--开具其他发票，自动计算，可修改"}, {"RuleXh": "25ed166c77f84173a9f1940cc6a502a4", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "kchXxynse"}], "NmyRuleName": "21_1公式", "NmyRuleMessage": "主表第21行简易计税办法计算的应纳税额“一般项目”列“本月数”需等于“《附列资料（一）》（第10列第8、9a、10、11行之和-第10列第14行）+（第14列第9b、12、13a、13b行之和-第14列第15行）”"}, {"RuleXh": "233cbebd642841308ed17899b09f8a38", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "wkjfpXse"}], "NmyRuleName": "2_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "af501bfd087641018794360dae75a1d6", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "fcyjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "fcyjse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjdjse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjdjse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjdjse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjdjse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjdjse"}], "NmyRuleName": "28_1公式", "NmyRuleMessage": "自动计算，非即征即退企业(核定WSXXS.[JZJTBZ]=N）时，主表第28行1列= 《附表四》第4列第(2+3+4+5)栏，可编辑，即征即退企业(核定WSXXS.[JZJTBZ]=Y）时，默认0.00，可修改"}, {"RuleXh": "7c7757098a2347c3992ff33d9e9bedc7", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "yshwxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "yshwxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "yshwxse"}], "NmyRuleName": "2_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "af0ad8a19f8648469eece1af9cfc32a3", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "wkjfpXse"}], "NmyRuleName": "12_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "ada1bc5d8fe848a0b31e98c2d3fb3c9a", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "je"}], "NmyRuleName": "12_2公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第12行2列“当期申报抵扣进项税额合计--金额”必须等于第1行第2列“（一）认证相符的税控增值税专用发票--金额”+第4行第2列“（二）其他扣税凭证--金额”之和"}, {"RuleXh": "7a5dcf68e8f4489ca9ae756f8e6b6fb0", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_3'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "", "NmyRuleMessage": "hw_3_zp_je:hw_3_zp_je 取值 可修改"}, {"RuleXh": "7ec0dec655514b7696210fe19b694b3f", "RelateTable": "077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'5'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'6'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'7'", "Col": "xse"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 表 已缴纳增值税情况 模块 销售额 1 列 免税项目（小计）公式 ", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“销售额 1”列“免税项目（小计）”行需要等于 电信服务 + 其他应税服务"}, {"RuleXh": "7a4e58459077495fa95a59ebc339dcbd", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '20'", "Col": "yzl"}], "NmyRuleName": "13b-6公式", "NmyRuleMessage": "自动计算不提示"}, {"RuleXh": "e27a12f9e48e489a9a28b1a3b7b98125", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjdjse"}], "NmyRuleName": "5_5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第5行第5列“税额抵减情况--期末余额”应等于第5行第3列“本期应抵减税额”-第5行第4列“本期实际抵减税额”。"}, {"RuleXh": "e22bbf47887d4f34883b5da8591256e9", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "fs"}], "NmyRuleName": "4_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第4行第1列必须等于第5+6+7+8a+8b栏之和。"}, {"RuleXh": "e248c29437314f388025ae9e455b93ec", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}], "NmyRuleName": "主表11_3公式", "NmyRuleMessage": "主表第11行销项税额本行“即征即退项目”列“本月数”需等于“《附列资料（一）》第10列第6行+第14列第7行”"}, {"RuleXh": "7a8e9bd1b4594439b0d9755569f5f0d3", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzdxse"}], "NmyRuleName": "2_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第2行第9列“13%税率的服务、不动产和无形资产 （16%税率也填这栏）--销售额”必须等于第2行第(1+3+5+7)列之和"}, {"RuleXh": "e0a595be79484cb29e0b4fd4373f4c43", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "wkjfpXse"}], "NmyRuleName": "9b_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9b行第9列“5%征收率的货物及加工修理修配劳务--销售额”必须等于第9b行第(1+3+5+7)列之和"}, {"RuleXh": "e115c6c0ff9541b998cbf69478eb8f9d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "wkjfpXxynse"}], "NmyRuleName": "8_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第8行第10列“6%税率--销项(应纳)税额”必须等于第8行第(2+4+6+8)列之和"}, {"RuleXh": "ad50dc29497a43c59da6727abc8462a9", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "wkjfpXse"}], "NmyRuleName": "13c_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13c行第9列“预征率%--销售额”必须等于第13c行第(1+3+5+7)列之和"}, {"RuleXh": "e10ade56f5754ba49693a5899b00b18b", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'29'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'30'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'31'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'32'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'33'", "Col": "se"}], "NmyRuleName": "29_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第29行第3列“（二）其他扣税凭证--税额”必须等于第30行第3列+第31行第3列+第32行第3列+第33行第3列之和"}, {"RuleXh": "79ea3efaea1e4c1196929c49ced0c3bd", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqfse"}], "NmyRuleName": "1_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第1行第3列“税额抵减情况--本期应抵减税额”应等于第1行第1列“期初余额”+第1行第2列“本期发生额”。"}, {"RuleXh": "e07b60176c8445ff8300dd27187ada7e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'JYJSFFJSQBZSXM_ZSL'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "8-1公式", "NmyRuleMessage": "hw_6_zp_je:hw_6_zp_je 取值 可修改"}, {"RuleXh": "e060b91effe34a00862273f6fddab357", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '21'", "Col": "yzl"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '21'", "Col": "yzl"}], "NmyRuleName": "13c-14公式", "NmyRuleMessage": "自动取值，不提示"}, {"RuleXh": "df9e2f2556624e8f9eaa24ea433fe867", "RelateTable": "001,301", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtsejyfj"}], "NmyRuleName": "40_1公式", "NmyRuleMessage": "《增 值 税 及 附 加 税 费 申 报 表》第40行1列“教育费附加本期应补（退）费额”应等于《增值税及附加税费申报表附列资料（五）》对应教育费附加的第14列“本期应补（退）税（费）额”。"}, {"RuleXh": "2266b7cd5d614c8a8902ce7771fdbdc5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '21'", "Col": "yzl"}], "NmyRuleName": "13c_2公式", "NmyRuleMessage": "自动计算，不可修改"}, {"RuleXh": "22b8e21f93484ef898962d97a4aa6abc", "RelateTable": "001,081", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjdjse"}], "NmyRuleName": "23_1公式", "NmyRuleMessage": ""}, {"RuleXh": "22abde7dcdb04ec493f9f31f694775ca", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'MSHWJJGXLXPLW'", "Col": "kjqtfpXse"}], "NmyRuleName": "18_3公式", "NmyRuleMessage": "取hw_ms_pp_je:hw_ms_pp_je，可修改"}, {"RuleXh": "21b566ca15cd4486b99da596504106ef", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'27'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "je"}], "NmyRuleName": "27_2公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "55bcaef578414923af8491bc89c8f03d", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "cbxse"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 表 已缴纳增值税情况 模块 查补销售额 1 列 征税项目（小计）公式", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“查补销售额 1”列“征税项目（小计）”行需要等于 基础电信服务 + 增值电信服务 + 其他应税服务"}, {"RuleXh": "ba952f8a2b9646fc9b846d45e1fd420a", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "qmwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmwjse"}], "NmyRuleName": "32_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第32行2列“期末未缴税额（多缴为负数）--一般项目--本年累计”应等于“本月数 ”。"}, {"RuleXh": "886c9852c60b4c3d8413eced06d595fd", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "5_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第5行第13列“6%税率--含税(免税)销售额”必须等于第5行第11列“6%税率--价税合计”-第5行第12列“6%税率--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "5548cea7305b4384a7a9f94b587008b9", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmwjseQjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ssckkjzyjkstse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ssckkjzyjkstse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqyjse"}], "NmyRuleName": "", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第33行1列“其中：欠缴税额（≥0）--一般项目--本月数”=本表本列第(25+26-27)栏，若计算为负数，则等于0。"}, {"RuleXh": "878bc04cc7624b0ca177f25eb7593d4d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "wkjfpXse"}], "NmyRuleName": "3_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "53a468d2665f4074bdbdb696d3dc85de", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'YBJSFFJSQBZSXM_SL'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'YBJSFFJSQBZSXM_SL'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "5_2公式", "NmyRuleMessage": "6%税率--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "b9c5e71ddadf4a85837f3c8b486c7f8b", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "se"}], "NmyRuleName": "1_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第1行第3列“（一）认证相符的税控增值税专用发票--税额”必须等于第2行第3列“其中：本期认证相符且本期申报抵扣--税额”+第3行第3列“前期认证相符且本期申报抵扣--税额”之和"}, {"RuleXh": "eee05f33f1134f7184e1129a3c1cbf7d", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'8'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yjse"}], "NmyRuleName": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏取值规则", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏需等于 征税项目"}, {"RuleXh": "ba697f2ea18c449da4df33764e4701d5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "hjXxynse"}], "NmyRuleName": "15_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第15行第11列“即征即退服务、不动产和无形资产--价税合计”必须等于第15行第9列“即征即退服务、不动产和无形资产--销售额”+第15行第10列“即征即退服务、不动产和无形资产--销项(应纳)税额”之和"}, {"RuleXh": "549b09e491f44ec288ad5326bb721237", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "fcyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ckkjzyjksyjse"}], "NmyRuleName": "34_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第34行1列“本期应补(退)税额--一般项目--本月数”=本表本列第(24-28-29)行。"}, {"RuleXh": "5410c3d114424df790e4aee1c1bfccbb", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "ajybfjsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "ajybfjsxse"}], "NmyRuleName": "5_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "ee3fa848c3794568ba4d9bcee5382c88", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'JYJSFFJSQBZSXM_ZSL'", "Col": "kjqtfpXse"}], "NmyRuleName": "8_3公式", "NmyRuleMessage": "取fw_6_pp_je:fw_6_pp_je，可修改"}, {"RuleXh": "edf2685701284ac9b120300f79dd3d70", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "qcye"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_CZBDCYZJNSK'", "Col": "qcye"}], "NmyRuleName": "5_1公式", "NmyRuleMessage": "取核定可修改。"}, {"RuleXh": "b91eb5fb5cba4ac9be65e1340975e939", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '7'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三7_5公式", "NmyRuleMessage": ""}, {"RuleXh": "eea5e084a33d44f1a9ce9f2b5d5b949a", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "xse"}], "NmyRuleName": "5_1公式", "NmyRuleMessage": "主表第5行（二）按简易办法计税销售额“一般项目”列“本月数”等于《附列资料（一）》第9列第8至13b行之和-第9列第14、15行之和"}, {"RuleXh": "b9559c11e0c24b6cbf32e59240d1b7ae", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_13'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "2-1公式", "NmyRuleMessage": "fw_13_zp_je:fw_13_zp_je 取值 可修改"}, {"RuleXh": "ed3ed7d8d3494a758c96fdde4be9ac49", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qcwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqyjse"}], "NmyRuleName": "32_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第32行3列“期末未缴税额（多缴为负数）--即征即退项目--本月数”应等于本表本列第(24+25+26-27)行。"}, {"RuleXh": "859f4880034746b4a859d5e00dd9a110", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqzce"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqzce"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqzce"}], "NmyRuleName": "8_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第8行3列“加计抵减情况--本期调减额合计”应等本表本列明细之和。"}, {"RuleXh": "ec2fe56b849145acb42f5f2f7a9bc3cb", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "24_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第24行3列“应纳税额合计--即征即退项目--本月数”=本表本列第(19+21-23)行。"}, {"RuleXh": "ec3452bfb5534b85877f4b5952ccc3fa", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "se"}], "NmyRuleName": "12_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第12行3列“当期申报抵扣进项税额合计--税额”必须等于第1行第3列“（一）认证相符的税控增值税专用发票--税额”+第4行第3列“（二）其他扣税凭证--税额”+第11行第3列“（五）外贸企业进项税额抵扣证明--税额”之和"}, {"RuleXh": "eb71f0a88a4a47e897174db24cc06bf2", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "mzzzsxmxse"}, {"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "mzzzsxmxse"}], "NmyRuleName": "免税1列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第1列\"免税项目--免征增值税项目销售额--合计\"应等于本表第1列\"免税项目--免征增值税项目销售额\"明细合计。"}, {"RuleXh": "85324dea5f8b47c2b2405142d757ffd4", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqybtsejyfj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtsejyfj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqybtsejyfj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqybtsejyfj"}], "NmyRuleName": "40_2", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第40行2列“教育费附加本期应补（退）费额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "b745f96afad14661ae189f08213f4d8a", "RelateTable": "001,031,081", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqfse"}], "NmyRuleName": "1_2本期发生额公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）》可编辑，默认取减免税申报表明细表减税项目第2列（当减税代码|事项代码为0001129914|SXA031900185时才带过来）"}, {"RuleXh": "b72b84a991e440b2ac24b68c2df3d0ee", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}], "NmyRuleName": "5-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第5行第6列“5%征收率的项目--期末余额”必须等于增值税及附加税费申报表附列资料（三）第5行第4列“5%征收率的项目--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第5行第5列“5%征收率的项目--本期实际扣除金额”"}, {"RuleXh": "84b1f42fbf6743a090f9160e847399bf", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'35'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "je"}], "NmyRuleName": "35_2公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第35行第2列“本期认证相符的税控增值税专用发票--金额”必须等于第2行第2列“其中：本期认证相符且本期申报抵扣--金额”+第26行第2列“本期认证相符且本期未申报抵扣--金额”之和"}, {"RuleXh": "b6c56acbb8c64388a4d82fcf7e35ffe4", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "je"}], "NmyRuleName": "003_6_2_公式", "NmyRuleMessage": ""}, {"RuleXh": "b6f310d78a8d4ffcb36808cac833ccd6", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "wkjfpXse"}], "NmyRuleName": "8_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "b6eccf297bb54f3993c339ee315d5e39", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'8'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'5'", "Col": "xse"}], "NmyRuleName": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏计算公式", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏需等于 征税项目小计值加免税项目小计值"}, {"RuleXh": "e9fac12839df4d23b0584044281f5c07", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjdjse"}], "NmyRuleName": "2_5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第2行第5列“税额抵减情况--期末余额”应等于第2行第3列“本期应抵减税额”-第2行第4列“本期实际抵减税额”。"}, {"RuleXh": "ea50c5618cee482b892fbc533e81f9dd", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第12行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第12行12列应等于《增值税及附加税费申报表附列资料（三）》第6行5列"}, {"RuleXh": "82f1cb8759e74e37be6c7dd88c1a39a9", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kchHsmsxse"}], "NmyRuleName": "5-15公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）第5行“6%税率”第12列“服务、不动产和无形资产扣除项目本期实际扣除金额”等于0时，增值税及附加税费申报表附列资料（一）第5行“6%税率”第14列“销项(应纳)税额”等于增值税及附加税费申报表附列资料（一）第5行“6%税率”第10列“价税合计”。否则，销项(应纳)税额等于含税(免税)销售额除以100%加税率乘以税率"}, {"RuleXh": "ea1da034d3194428b7f7043aa7421579", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_5'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "9b-1公式", "NmyRuleMessage": "fw_5_zp_je:fw_5_zp_je 取值 可修改"}, {"RuleXh": "ea1824bc305b4295949820b05d8ac378", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "fcyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ckkjzyjksyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqjnqjse"}], "NmyRuleName": "27_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第27行1列“本期已缴税额--一般项目--本月数”=本表本列(28+29+30+31)行。"}, {"RuleXh": "ea22dc1a3c8348db9eb7e2889546bcf7", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxsedyjxse"}], "NmyRuleName": "免税4列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第4列\"免税项目--免税销售额对应的进项税额--合计\"应等于本表第4列\"免税项目--免税销售额对应的进项税额\"明细合计。"}, {"RuleXh": "82f056b957714c32b727927e04de19a3", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjctzxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzdxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzdxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzdxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzdxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzdxse"}], "NmyRuleName": "4_1公式", "NmyRuleMessage": "主表第4行纳税检查调整的销售额“一般项目”列“本月数”需等于《附列资料（一）》第7列第1至5行之和"}, {"RuleXh": "b5024017ebac442ba63214dc28bc8d79", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "nsjctzdxse"}], "NmyRuleName": "4_8公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "e901e09f483e481c9d05e88db54985a2", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqjns<PERSON><PERSON><PERSON>"}], "NmyRuleName": "30_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第30行2列“③本期缴纳上期应纳税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "b47c6d012b2c44e2b656bb9467c4a4f2", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "4_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第4行第13列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--含税(免税)销售额”必须等于第4行11列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--价税合计”-第4行第12列“9%税率的服务、不动产和无形资产 （10%税率也填这栏）--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "e8b738d9cbbd4b1798ad1b9bf5f53a24", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'6'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "xse"}], "NmyRuleName": "1_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第1行1列“（一）按适用税率计税销售额--一般项目--本月数”应等于《增值税及附加税费申报表附列资料（一）》第9列“合计--销售额”（1+2+3+4****-7）行。"}, {"RuleXh": "e84784fe06b4427db9a28690087f6c97", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'YBJSFFJSQBZSXM_SL'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'YBJSFFJSQBZSXM_SL'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjqtfpXse"}], "NmyRuleName": "5 _4公式", "NmyRuleMessage": "一般计税方法计税--6%税率--开具其他发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "e6dcc23b99dc44eaa1e614257ece7285", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_9'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "3-1公式", "NmyRuleMessage": "hw_9_zp_je:hw_9_zp_je 取值可修改"}, {"RuleXh": "5f025052d4844a5695def95f1cb44aee", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'8'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "cbse"}], "NmyRuleName": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏取值规则", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏需等于 征税项目小计值"}, {"RuleXh": "90d4405723b145b0901715e24c5a4c68", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "wkjfpXse"}], "NmyRuleName": "10_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "c1a1c63e1b10490bbf32dc59f670222f", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ydks<PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}], "NmyRuleName": "18_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第18行1列“实际抵扣税额--一般项目--本月数”应等于本表本列（第17和第11行）孰小值。"}, {"RuleXh": "5ac8d73860764ae4951b633c2ee35c3f", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "19_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第19行1列“应纳税额--一般项目--本月数”应等于本表本列（11-18)栏 - 《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第6行第5列\"本期实际抵减额\"。"}, {"RuleXh": "c22d14e025c0444eb67c4212d391b369", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kchHsmsxse"}], "NmyRuleName": "2-14公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）第2行“13%税率的服务、不动产和无形资产”第12列“服务、不动产和无形资产扣除项目本期实际扣除金额”等于0时，增值税及附加税费申报表附列资料（一）第2行“13%税率的服务、不动产和无形资产”第14列“销项(应纳)税额”等于增值税及附加税费申报表附列资料（一）第2行“13%税率的服务、不动产和无形资产”第10列“价税合计”。"}, {"RuleXh": "8ef611b4cd28447dab9dc1dc28e5bf38", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'MSFW_BDCHWXZC'", "Col": "kjqtfpXse"}], "NmyRuleName": "19_3公式", "NmyRuleMessage": "取fw_ms_pp_je:fw_ms_pp_je，可修改"}, {"RuleXh": "5b2eb6537d1546aab78334751f94de90", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qcye"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_ZZSSKXTZYSBFJJSWHF'", "Col": "qcye"}], "NmyRuleName": "1_1公式", "NmyRuleMessage": "自动计算可修改。"}, {"RuleXh": "c102fc0f7501454f99d77153019d53c5", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}], "NmyRuleName": "3-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第3行第6列“6%税率的项目（不含金融商品转让）--期末余额”必须等于增值税及附加税费申报表附列资料（三）第3行第4列“6%税率的项目（不含金融商品转让）--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第3行第5列“6%税率的项目（不含金融商品转让）--本期实际扣除金额”"}, {"RuleXh": "8e1c5ae3f0d14e3eb0e44b7e1955b680", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}], "NmyRuleName": "2-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第2行第6列“9%税率的项目（10%税率也填这栏）--期末余额”必须等于增值税及附加税费申报表附列资料（三）第2行第4列“9%税率的项目（10%税率也填这栏）--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第2行第5列“9%税率的项目（10%税率也填这栏）--本期实际扣除金额”"}, {"RuleXh": "c13025e6ddb6491f8aa5c590a1dff408", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "fs"}], "NmyRuleName": "1_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第1行第1列“份数”必须等于第2行第1列“其中：本期认证相符且本期申报抵扣”+第3行第1列“前期认证相符且本期申报抵扣”之和。"}, {"RuleXh": "8dfc9d7d9f7649399ea4f2951bbc92e6", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqybtsedfjyfj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqybtsedfjyfj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqybtsedfjyfj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqybtsedfjyfj"}], "NmyRuleName": "41_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第41行2列“地方教育附加本期应补（退）费额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "c06238b7fe804711820976ce64735863", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '21'", "Col": "yzl"}], "NmyRuleName": "13c_6公式", "NmyRuleMessage": "自动计算，不可修改"}, {"RuleXh": "8d7dbb24a0f041ed99773dab3e555973", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "wkjfpXse"}], "NmyRuleName": "4_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "8d3a7c827afa4930a7952411231db7b5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kchHsmsxse"}], "NmyRuleName": "12-14公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）第12行“3%征收率的服务、不动产和无形资产”第12列“服务、不动产和无形资产扣除项目本期实际扣除金额”等于0时，增值税及附加税费申报表附列资料（一）第12行“3%征收率的服务、不动产和无形资产”第14列“销项(应纳)税额”等于增值税及附加税费申报表附列资料（一）第12行“3%征收率的服务、不动产和无形资产”第10列“价税合计”。否则，销项(应纳)税额等于含税(免税)销售额除以100%加税率乘以税率"}, {"RuleXh": "bf4cf1b61fdd4e9d96b5b852410c782c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_9'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_9'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "3_2公式", "NmyRuleMessage": "9%税率的货物及加工修理修配劳务--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "bf5f9845a71f4160848b425e1efde083", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "mdtbfckxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtbfckxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "mdtbfckxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mdtbfckxse"}], "NmyRuleName": "7_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "0234f5f9584249e49af27af4905b7701", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jxsezc"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jxsezc"}], "NmyRuleName": "14_4公式", "NmyRuleMessage": "自动计算，当逾期=Y,第14行4列=0,只读，否则等于本月数 + 核定LSXXS.[52],可修改；"}, {"RuleXh": "01f9dbe83f6e4627a77106d20343598c", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "6_6公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》加计抵减项目第6行第6列“加计抵减情况--期末余额”应等于本表第6行4列-第6行5列。"}, {"RuleXh": "016396fb00f544bbb0ec90e0e16ce149", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '1'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三1_5公式", "NmyRuleMessage": ""}, {"RuleXh": "5ab51814fc8b4fd6877b8455e19085ab", "RelateTable": "001,031,081", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjdjse"}], "NmyRuleName": "1_4本期实际抵减税额公式", "NmyRuleMessage": "可编辑，默认取减免税申报表明细表减税项目第4列（当减税代码|事项代码 为 0001129914|SXA031900185时才带过来"}, {"RuleXh": "5a2ec5bb81d74c17b467833c9a7d8af1", "RelateTable": "001,064", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yzl"}], "NmyRuleName": "《航空运输企业试点地区分支机构传递单》已缴纳增值税情况 交通运输服务 预缴税额 公式计算", "NmyRuleMessage": "《航空运输企业试点地区分支机构传递单》已缴纳增值税情况 交通运输服务 预缴税额计算"}, {"RuleXh": "593984ef2be04d508e4077a2f2748189", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'8'", "Col": "yskje"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "yskje"}], "NmyRuleName": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏 规则", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》（已缴纳增值税情况）的合计栏需等于 征税项目小计值"}, {"RuleXh": "8ca4072fc30a43469198d8032f2b2abe", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "ynse"}], "NmyRuleName": "19_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "5919351b2f7442c09fc98001cf081421", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "bqjnqjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "bqjnqjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "bqjnqjse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "bqjnqjse"}], "NmyRuleName": "31_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第31行2列“④本期缴纳欠缴税额--一般项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "8c5fdf7d7bf44a40870ae2e5dbde60e6", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jxse"}], "NmyRuleName": "12_2公式", "NmyRuleMessage": "自动计算，当逾期=Y,主表12行2列=0,只读，否则，等于本月数 + 核定LSXXS.[12],可修改；"}, {"RuleXh": "8cf91921dd564f0994d45ee2906eebf7", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}], "NmyRuleName": "7_5公式", "NmyRuleMessage": "自动计算，《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》加计抵减项目第7栏第4列＜0时，第7栏第5列=0。第7栏第4列≥0时，第5列=min(第4列，主表第3列第(11-18)行)。"}, {"RuleXh": "596c92e8ef444f94ad05c2bbf86556f5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "hjXxynse"}], "NmyRuleName": "12_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第12行第11列“3%征收率的服务、不动产和无形资产--价税合计”必须等于第12第9列“3%征收率的服务、不动产和无形资产--销售额”+第12行第10列“3%征收率的服务、不动产和无形资产--销项(应纳)税额”之和"}, {"RuleXh": "bef43d5574e44762b0877814f6b3e835", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_9'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_9'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'4'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "4_2公式", "NmyRuleMessage": "9%税率的服务、不动产和无形资产--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "8c4235cbe9974d269269bebe874cb273", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtytse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "mdtytse"}], "NmyRuleName": "15_1公式", "NmyRuleMessage": "自动计算，当逾期=Y,主表15行1列=0,只读，否则等于核定WSXXS.[MDTYTSEBYS]，可修改"}, {"RuleXh": "8b63efbdca144c0fb698472cea99d23f", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qcye"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'JJDJQK_YBXMJJDJEJS'", "Col": "qcye"}], "NmyRuleName": "031_6_1_公式", "NmyRuleMessage": ""}, {"RuleXh": "8b55fd37dc784907ad135b05c58168ff", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzdxse"}], "NmyRuleName": "1_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第1行第9列“13%税率的货物及加工修理修配劳务 （16%税率也填这栏）--销售额”必须等于第1行第(1+3+5+7)列之和"}, {"RuleXh": "57c0c127664e48e5ad23d7a80daf8d48", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "xxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sjdkse"}], "NmyRuleName": "6_5公式", "NmyRuleMessage": "自动计算，《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》加计抵减项目第6栏第4列＜0时，第6栏第5列=0。第6栏第4列≥0时，第6栏第5列=min(第6栏第4列，主表第1列第(11-18)行)。"}, {"RuleXh": "8b5cd16529f44fd7874c65d600614340", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_3'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "12-1公式", "NmyRuleMessage": "fw_3_zp_je:fw_3_zp_je 取值可修改"}, {"RuleXh": "8a694bb89714427784e905e27038ffb2", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzXxynse"}], "NmyRuleName": "2_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第2行第10列“13%税率的服务、不动产和无形资产--销项(应纳)税额”必须等于第2行第(2+4+6+8)列之和。"}, {"RuleXh": "bd84a3452bd2456892af1b6d9e59d77b", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "ynsehj"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynsehj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "ynsehj"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "ynsehj"}], "NmyRuleName": "24_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第23行4列“应纳税额减征额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "bda835333cea471a95d84ad1441f27ae", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "qmwjcbse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "qmwjcbse"}], "NmyRuleName": "38_2公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第38行2列“期末未缴查补税额--一般项目--本年累计”应等于“本月数”。"}, {"RuleXh": "89c32953eee440f280149e9ae1db4355", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_5'", "Col": "kjqtfpXse"}], "NmyRuleName": "9a_3公式", "NmyRuleMessage": "取hw_5_pp_je:hw_5_pp_je，可修改"}, {"RuleXh": "f164303715bf4dd484e8ecfe51a312d2", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "sjdkse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sjdkse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "sjdkse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "sjdkse"}], "NmyRuleName": "18_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "f1df40b37a1e4070ad27e67c448b8b4e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "wkjfpXxynse"}], "NmyRuleName": "10_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第10行第10列“4%征收率--销项(应纳)税额”必须等于第10行第(2+4+6+8)列之和"}, {"RuleXh": "efa915755fe541279e56d0b9b3db378c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_9'", "Col": "kjqtfpXse"}], "NmyRuleName": "3_3公式", "NmyRuleMessage": "取hw_9_pp_je:hw_9_pp_je，可修改"}, {"RuleXh": "2ffa079a867a4a63bab9d0d2c0c77b5a", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "jybfYnse"}], "NmyRuleName": "21_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "648747d7c69641699c8f2e5a4bf792f7", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_13'", "Col": "kjqtfpXse"}], "NmyRuleName": "1_3公式", "NmyRuleMessage": "取hw_13_pp_je:hw_13_pp_je,可修改"}, {"RuleXh": "9782367ca0504a3b99e779104e255b70", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqfse"}], "NmyRuleName": "5_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第5行第3列“税额抵减情况--本期应抵减税额”应等于第5行第1列“期初余额”+第5行第2列“本期发生额”。"}, {"RuleXh": "9682c4dd9f6342ee9f3333ac9be37bda", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "wkjfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzdxse"}], "NmyRuleName": "5_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第5行第9列“6%税率--销售额”必须等于第4行第(1+3+5+7)列之和"}, {"RuleXh": "2e65cbc53a8342e9a47f874eb3b8e62a", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjkcje"}], "NmyRuleName": "7-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第7行第6列“免抵退税的项目--期末余额”必须等于增值税及附加税费申报表附列资料（三）第7行第4列“免抵退税的项目--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第7行第5列“免抵退税的项目--本期实际扣除金额”"}, {"RuleXh": "2d7e593c0da44b0ca019298104562137", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDFW_BDCHWXZC_5'", "Col": "kjqtfpXse"}], "NmyRuleName": "9b_3公式", "NmyRuleMessage": "取fw_5_pp_je:fw_5_pp_je，可修改"}, {"RuleXh": "6285328d0d554647b745d144a88e640d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "wkjfpXse"}], "NmyRuleName": "11_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第11行第9列“3%征收率的货物及加工修理修配劳务--销售额”必须等于第11行第(1+3+5+7)列之和"}, {"RuleXh": "628d6bcc613945ab8760d2911f5a4007", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qmye"}], "NmyRuleName": "减税5列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第5列\"减税项目--期末余额--合计\"应等于本表第5列\"减税项目--期末余额“明细合计。"}, {"RuleXh": "626a02ddc4534807885645e6479b74de", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "wkjfpXse"}], "NmyRuleName": "18_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第18行第9列“货物及加工修理修配劳务--销售额”必须等于第18行第(1+3+5+7)列之和"}, {"RuleXh": "62847204a48b484b9ca602405acbc039", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "23_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "624f88b337184b798e66d105b9de2b1d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_13'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDHWJJGXLXPLW_13'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "1_2公式。", "NmyRuleMessage": "13%税率的货物及加工修理修配劳务--开具增值税专用发票--销项(应纳)税额,自动计算，可修改"}, {"RuleXh": "2cddec433439437dbf5bcf3ad3c584b5", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzdxse"}], "NmyRuleName": "5_8公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "2c1a564ab14343aebd9e1bf688e49ea8", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'29'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'30'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'31'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'33'", "Col": "je"}], "NmyRuleName": "29_2公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第29行第2列“（二）其他扣税凭证--金额”必须等于第30行第2列“其中：海关进口增值税专用缴款书--金额”+第31行第2列“农产品收购发票或者销售发票--金额”+第33行第2列“其他--金额”之和"}, {"RuleXh": "61cd214d7b944cc9af6bb8144e163c76", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ydks<PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "sqldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jxsezc"}], "NmyRuleName": "17_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第17行3列“应抵扣税额合计--即征即退项目--本月数”应等于本表本列（12+13-14）行。"}, {"RuleXh": "93e3c5008eec4e639f4335c84e973618", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqsjkcje"}], "NmyRuleName": "3-5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第3行第5列“6%税率的项目（不含金融商品转让）--本期实际扣除金额”加《增值税及附加税费申报表附列资料（三）》第4行第5列“6%税率的金融商品转让项目--本期实际扣除金额”必须等于《增值税及附加税费申报表附列资料（一）》第5行第12列“6%税率--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "2b9003b9b7564d498a662d7ef720fff6", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqfse"}], "NmyRuleName": "2-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第2行第4列“9%税率的项目（10%税率也填这栏)”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第2行第2列“9%税率的项目（10%税率也填这栏)”--期初余额加上增值税及附加税费申报表附列资料（三）第2行第3列“9%税率的项目（10%税率也填这栏)”--本期发生额"}, {"RuleXh": "2b84d486af1d40aeaa0ac012091250d7", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjqtfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSL_4'", "Col": "kjqtfpXse"}], "NmyRuleName": "10_3公式", "NmyRuleMessage": "取hw_4_pp_je:hw_4_pp_je，可修改"}, {"RuleXh": "2b659e9a945f462da5862c1ab790930d", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "qmwjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "qmwjse"}], "NmyRuleName": "32_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第32行4列“期末未缴税额（多缴为负数）--即征即退项目--本年累计”应等于“本月数 ”。"}, {"RuleXh": "93359dddc6b9462fb47746c5e1cdd306", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqzce"}], "NmyRuleName": "7_4公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》加计抵减项目第7行第4列“加计抵减情况--本期可抵减额”应等于第7行第1列+第7行第2列-第7行第3列。"}, {"RuleXh": "931df96e77ef416daca1f9c55ccba542", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "cbse"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 表 已缴纳增值税情况 模块 查补税额 1 列 征税项目（小计）公式", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“查补税额 1”列“征税项目（小计）”行需要等于 基础电信服务 + 增值电信服务 + 其他应税服务"}, {"RuleXh": "5ff8dd3a95024f55b25e58dcea64826e", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "jybfYnse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "jybfYnse"}], "NmyRuleName": "21_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "c5bb018f4fd7426a9ce9824424c3bb73", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "ydks<PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sqldse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxsezc"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "mdtytse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "syslNsjcybjse"}], "NmyRuleName": "17_1公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第17行1列“应抵扣税额合计--一般项目--本月数”应等于本表本列（12+13-14-15+16）行。"}, {"RuleXh": "5fc2c4c24e434b99843e24e0cc20ae57", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "je"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "je"}], "NmyRuleName": "1_2公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第1行第2列“（一）认证相符的税控增值税专用发票--金额”必须等于第2行第2列“其中：本期认证相符且本期申报抵扣--金额”+第3行第2列“前期认证相符且本期申报抵扣--金额”之和"}, {"RuleXh": "c60951df51c94720ab281013b856cc57", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'1'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'2'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'4'", "Col": "xse"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 表 已缴纳增值税情况 模块 销售额 1 列 征税项目（小计）公式 ", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“销售额 1”列“征税项目（小计）”行需要等于 基础电信服务 + 增值电信服务 + 其他应税服务"}, {"RuleXh": "919465570deb49adafbf1d7bc6d10063", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqsjjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "8_5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第8行5列“加计抵减情况--本期实际抵减额合计”=本表本列明细之和。"}, {"RuleXh": "91a71c16cf28404facc62ec881d6d3d4", "RelateTable": "001,077", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "cbse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "cbxse"}, {"PreTable": "ywbw.sbZzsYbnsrFbDxqy.sbZzsYbnsrFbDxqyYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "sysl"}], "NmyRuleName": "电信企业分支机构增值税汇总纳税信息传递单 查补税额 列计算", "NmyRuleMessage": "《电信企业分支机构增值税汇总纳税信息传递单》“增值电信服务”--查补税额7=5*6需按表样公式计算"}, {"RuleXh": "916ea42c76d2471a9e709598ee5c2069", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '2'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'2'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三2_5公式", "NmyRuleMessage": ""}, {"RuleXh": "91c6bc47a3a34442b0ea17b9bd1e1d54", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "hjXxynse"}], "NmyRuleName": "9b_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9b行第11列“5%征收率的服务、不动产和无形资产--价税合计”必须等于第9b行第9列“5%征收率的服务、不动产和无形资产--销售额”+第9b行第10列“5%征收率的服务、不动产和无形资产--销项(应纳)税额”之和"}, {"RuleXh": "fb114037277f4badb854360fe1a5a1e8", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}], "NmyRuleName": "1-6公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第1行第6列“13%税率的项目（16%税率也填这栏）--期末余额”必须等于增值税及附加税费申报表附列资料（三）第1行第4列“13%税率的项目（16%税率也填这栏）--本期应扣除金额”减去增值税及附加税费申报表附列资料（三）第1行第5列“13%税率的项目（16%税率也填这栏）--本期实际扣除金额”"}, {"RuleXh": "fa992feff1724ae1b5ecfac4e355b046", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '13'", "Col": "yzl"}], "NmyRuleName": "13a-4公式", "NmyRuleMessage": "自动计算不提示"}, {"RuleXh": "fa78f58750714795bf5c0f9feb68b6af", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "jshj"}], "NmyRuleName": "1-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第1行第1列“13%税率的项目 --本期服务、不动产和无形资产价税合计额（免税销售额）”必须等于《增值税及附加税费申报表附列资料（一）》第2行第11列“13%税率的服务、不动产和无形资产（16%税率也填这栏）--价税合计”"}, {"RuleXh": "f9b6f1dd3c954a3eaed5a6a7b1144b1a", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqfse"}], "NmyRuleName": "4_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第4行第3列“税额抵减情况--本期应抵减税额”应等于第4行第1列“期初余额”+第4行第2列“本期发生额”。"}, {"RuleXh": "f9a1cacf36244157bd7caf886d0d530c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzdxse"}], "NmyRuleName": "3_8公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "f88454c7690a4288973093b92e422435", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "13c_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13c行第13列“预征率%--含税(免税)销售额”必须等于第13c行第11列“3预征率%--价税合计”-第13c行第12列“预征率%--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "f878af9ba18f4e1599a05e99a3873731", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'5'", "Col": "nsjctzXxynse"}], "NmyRuleName": "5_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第5行第10列“6%税率--销项(应纳)税额”必须等于第4行第(2+4+6+8)列之和"}, {"RuleXh": "f926887a9c0d445388a366e4aa3d9023", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqsjkcje"}], "NmyRuleName": "附表1第2行12列", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第2行12列应等于《增值税及附加税费申报表附列资料（三）》第1行第5列。"}, {"RuleXh": "f8208e84ac4849b18f15bd0d4d7a9af0", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ajybfjsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "xse"}], "NmyRuleName": "5_3公式", "NmyRuleMessage": "主表第5行（二）按简易办法计税销售额“即征即退项目”列“本月数”需大于等于《附列资料（一）》第9列第14、15行之和"}, {"RuleXh": "0689deb89667404c997b4ec8907747bb", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "wkjfpXse"}], "NmyRuleName": "13a_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13a行第9列“预征率%--销售额”必须等于第13a行第(1+3+5+7)列之和"}, {"RuleXh": "06817136c4254ad9849c734c0e9c29ba", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'1'", "Col": "nsjctzdxse"}], "NmyRuleName": "1_8公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "06a823a2a9ab45138d413ea17cf8d174", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqsjdjse"}], "NmyRuleName": "3_5公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第3行第5列“税额抵减情况--期末余额”应等于第3行第3列“本期应抵减税额”-第3行第4列“本期实际抵减税额”。"}, {"RuleXh": "3ae76eceeae94a028198ee4f9c68749f", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '5'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'5'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三5_5公式", "NmyRuleMessage": ""}, {"RuleXh": "0635c208cd2e403080974994a9e94ab7", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "sqldse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "sqldse"}], "NmyRuleName": "13_1公式", "NmyRuleMessage": "自动计算，当逾期=Y,主表13行1列=0,只读，否则，等于核定WSXXS.[YBHWFWQCLD]，,可修改；"}, {"RuleXh": "3adb8b4bc53c4cdfba86fc9e50c33a6f", "RelateTable": "081", "RelateCells": [{"PreTable": "ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "mse"}], "NmyRuleName": "免税5列求和公式", "NmyRuleMessage": "《增值税减免税申报明细表》合计行第5列\"免税项目--免税额--合计\"应等于本表第5列\"免税项目--免税额\"明细合计。"}, {"RuleXh": "6e59b0fb57944bb4a0f15e0cdec11c6d", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'27'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "fs"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'3'", "Col": "fs"}], "NmyRuleName": "27_1公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "045e96d1bbcf4782a7dba064abf348d0", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqzce"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqzce"}], "NmyRuleName": "031_7_3_公式", "NmyRuleMessage": ""}, {"RuleXh": "03b1e9491e994fbf80c344a51a384eb1", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "jybfYnse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "kchXxynse"}], "NmyRuleName": "21_3公式", "NmyRuleMessage": "主表第21行简易计税办法计算的应纳税额“即征即退项目”列“本月数”需等于《附列资料（一）》第10列第14行+第14列第15行"}, {"RuleXh": "0356cf56b2304eb680d7ddf7d527f68d", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_13'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'SLDFW_BDCHWXZC_13'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "2_2公式", "NmyRuleMessage": "13%税率的服务、不动产和无形资产--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "6dd7cf858fa748a49924fd22c0f63e45", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "se"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'SBDKDJXSE_BQRZXFQBQSBDK'", "Col": "se"}], "NmyRuleName": "2_3公式", "NmyRuleMessage": "自动取数，可修改"}, {"RuleXh": "03cc466ec82b4bb5a412b871c77f8dd9", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'8'", "Col": "wkjfpXse"}], "NmyRuleName": "8_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第8行第9列“6%税率--销售额”必须等于第8行第(1+3+5+7)列之和"}, {"RuleXh": "384d12e7d801465a985296d0bda04243", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "ysfwkcxmbqsjkcje"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'7'", "Col": "kchXxynse"}], "NmyRuleName": "7_14公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（一）当第7行第12列=0时，本行14列=第10列值；"}, {"RuleXh": "38fea39b3c904ba18fe4e5814042d542", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "kjqtfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'12'", "Col": "wkjfpXse"}], "NmyRuleName": "12_9公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第12行第9列“3%征收率的服务、不动产和无形资产--销售额”必须等于第12行第(1+3+5+7)列之和"}, {"RuleXh": "9e4ceaf5e01f4248b09a4b2604b15b9a", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'4'", "Col": "bqfse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_XSBDCYZJNSK'", "Col": "bqfse"}], "NmyRuleName": "4_2公式期初", "NmyRuleMessage": "增值税及附加税费申报表附列资料（四）第4行2列取WSXXS.[FB4XSBDCYJBQFSE]，可编辑"}, {"RuleXh": "9f0306548fbb452da51e2ec858cba134", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "bqjns<PERSON><PERSON><PERSON>"}], "NmyRuleName": "30_4公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第30行4列“③本期缴纳上期应纳税额--即征即退项目--本年累计”应等于“本月数 + 上期累计”。"}, {"RuleXh": "6ccb7c8c2ba3484b86106ddde7b2b1b7", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}], "NmyRuleName": "6-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第6行第4列“3%征收率的项目 ”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第6行第2列“3%征收率的项目”--期初余额加上增值税及附加税费申报表附列资料（三）第6行第3列“3%征收率的项目 ”--本期发生额"}, {"RuleXh": "9d682c6d74964474855f824f5e3e9b36", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'8'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qmye"}], "NmyRuleName": "8_6公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第8行第6列“加计抵减情况--期末余额合计=本表本列明细之和。"}, {"RuleXh": "6bde218674ec449c97f9a1441b3897ea", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'3'", "Col": "bqfse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_JZFWYZJNSK'", "Col": "bqfse"}], "NmyRuleName": "3_2公式期初", "NmyRuleMessage": "增值税及附加税费申报表附列资料（四）第3行2列取WSXXS.[FB4JZFWYJBQFSE]，可编辑"}, {"RuleXh": "6b561a2f2de048ddb7621b4363815754", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'11'", "Col": "wkjfpXse"}], "NmyRuleName": "11_6公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "6afc649fdee146709d01ebb2ae3d87f4", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "nsjctzdxse"}], "NmyRuleName": "2_8公式", "NmyRuleMessage": "自动计算，可修改"}, {"RuleXh": "9cf2dded381641b19b4839b2f16f073f", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kchXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '13'", "Col": "yzl"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '13'", "Col": "yzl"}], "NmyRuleName": "13a-14公式", "NmyRuleMessage": "自动取值，不提示"}, {"RuleXh": "9c29822f4cd34a26a469e7baaf732070", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'25'", "Col": "fs"}, {"PreTable": "ytxx.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhlbm'", "Value": "'DDKJXSE_QCYRZXFDWSBDK'", "Col": "fs"}], "NmyRuleName": "25_1公式", "NmyRuleMessage": "取核定，可修改"}, {"RuleXh": "9c378b3323dc4e788855da1d75fa2887", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'1'", "Col": "bqfse"}], "NmyRuleName": "1-4公式", "NmyRuleMessage": "增值税及附加税费申报表附列资料（三）第1行第4列“13%税率的项目（16%税率也填这栏）”--本期应扣除金额必须等于增值税及附加税费申报表附列资料（三）第1行第2列“13%税率的项目（16%税率也填这栏）”--期初余额加上增值税及附加税费申报表附列资料（三）第1行第3列“13%税率的项目（16%税率也填这栏）”--本期发生额"}, {"RuleXh": "f614c357790847f385427f2f0ca070d1", "RelateTable": "001,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqsjkcje"}, {"PreTable": "ytxx.sbZzsYbnsrFb3Ysfwkcxm", "Key": " 'ewbhxh'", "Value": " '6'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'6'", "Col": "b<PERSON><PERSON><PERSON><PERSON>"}], "NmyRuleName": "附表三6_5公式", "NmyRuleMessage": ""}, {"RuleXh": "f609bece94b8464492ec9258f72bbe26", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "kchHsmsxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "ysfwkcxmbqsjkcje"}], "NmyRuleName": "9b_13公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第9b行第13列“5%征收率的服务、不动产和无形资产--含税(免税)销售额”必须等于第9b行第11列“5%征收率的服务、不动产和无形资产--价税合计”-第9b行第12列“5%征收率的服务、不动产和无形资产--服务、不动产和无形资产扣除项目本期实际扣除金额”"}, {"RuleXh": "f4c00b96377b46bab1bdf04e4195c3a1", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'8'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqkjjdkjxse"}], "NmyRuleName": "8_4公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第8行4列“加计抵减情况--本期可抵减额合计”=本表本列明细之和。"}, {"RuleXh": "f4a8438de15e40f2bd2390fa6ecf6198", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqydj<PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'2'", "Col": "bqfse"}], "NmyRuleName": "2_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》抵减项目第2行第3列“税额抵减情况--本期应抵减税额”应等于第2行第1列“期初余额”+第2行第2列“本期发生额”。"}, {"RuleXh": "f3c72ee5b817448aa24746d48fd0c385", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "wkjfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "nsjctzXxynse"}], "NmyRuleName": "3_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第3行第10列“9%税率的货物及加工修理修配劳务 （10%税率也填这栏）--销项(应纳)税额”必须等于第3行第(2+4+6+8)列之和。"}, {"RuleXh": "34cbed55262d49d98e7e0273858e2726", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "fcyjse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjns<PERSON><PERSON><PERSON>"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "bqjnqjse"}], "NmyRuleName": "27_3公式", "NmyRuleMessage": "《增 值 税 纳 税 申 报 表（一般纳税人适用）》第27行3列“本期已缴税额--即征即退项目-本月数”=本表本列(28+29+30+31)行。"}, {"RuleXh": "6a17f37c5741474498fa0a1619efed71", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSL_4'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSL_4'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'10'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "10_2公式", "NmyRuleMessage": "简易计税方法计税--4%征收率--开具增值税专用发票--销项(应纳)税额，自动计算，可修改"}, {"RuleXh": "9b6fd14f3500481ba7850268d04f440f", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'8'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qcye"}], "NmyRuleName": "8_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》第8行1列“加计抵减情况--期初余额合计”应等本表本列明细之和。"}, {"RuleXh": "9b81f3adb582457fbd77f43b837e087c", "RelateTable": "001,002,006", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb3Ysfwkcxm", "Key": "'ewbhxh'", "Value": "'8'", "Col": "msxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "jshj"}], "NmyRuleName": "8-1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（三）》第8行第1列“免税的项目”--本期服务、不动产和无形资产价税合计额（免税销售额）必须等于增值税及附加税费申报表附列资料（一）》第19行第11列“服务、不动产和无形资产”--价税合计"}, {"RuleXh": "692a07e24c1a4cf2ada04d4b840cf28e", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_5'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ytxx.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhlbm'", "Value": "'ZSLDHWJJGXLXPLW_5'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'9'", "Col": "kjskzzszyfpXse"}], "NmyRuleName": "9a_2公式", "NmyRuleMessage": "简易计税方法计税--5%征收率的货物及加工修理修配劳务--开具增值税专用发票--销项(应纳)税额,自动计算，可修改"}, {"RuleXh": "9b695eaa1b49406db961c0c7775e3373", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'2'", "Col": "asysljsxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BNLJ'", "Col": "asysljsxse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'YBHWJLW_BYS'", "Col": "asysljsxse"}], "NmyRuleName": "1_2公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "34495bcab5cd40ecbaef44ca82057d38", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "kjskzzszyfpXse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": " 'ewbhxh'", "Value": " '13'", "Col": "yzl"}], "NmyRuleName": "13a-2公式", "NmyRuleMessage": "自动计算，不提示"}, {"RuleXh": "7cb1c8270d6446aa90ca0c4589557098", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'JJDJQK_YBXMJJDJEJS'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'1'", "Col": "jxse"}], "NmyRuleName": "附表四 6-2公式", "NmyRuleMessage": null}, {"RuleXh": "9a01ec4afbd742969733be5fd4647beb", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'5'", "Col": "bqfse"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'SEDJQK_CZBDCYZJNSK'", "Col": "bqfse"}], "NmyRuleName": "5_2公式期初", "NmyRuleMessage": "增值税及附加税费申报表附列资料（四）第5行2列取WSXXS.[FB4CZBDCYJBQFSE]，可编辑"}, {"RuleXh": "33be815e519c4c1294346d6357e12ec4", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qmye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "bqsjjjdkjxse"}], "NmyRuleName": "7_6公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》加计抵减项目第7行第6列“加计抵减情况--期末余额”应等于本表第7行4列-第7行5列。"}, {"RuleXh": "3303c7b8087042b6953f485daeda0171", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'13'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'14'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'15'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'16'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'17'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'18'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'21'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'22'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'23'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'38'", "Col": "se"}], "NmyRuleName": "13_1公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第13行1列“本期进项税转出额--税额”必须等于第14行第1列+第15行第1列+第16行第1列+第17行第1列+第18行第1列+第19行第1列+第20行第1列+第21行第1列+第22行第1列+第23a行+第23b行第1列之和"}, {"RuleXh": "68243c4e0cd7444cb47a28cc49b63d61", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqkjjdkjxse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "qcye"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqfse"}, {"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'6'", "Col": "bqzce"}], "NmyRuleName": "6_4公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（四）（税额抵减情况表）》加计抵减项目第6行第4列“加计抵减情况--本期可抵减额”应等于第6行第1列+第6行第2列-第6行第3列。"}, {"RuleXh": "680c869bb09d4bd9be9b3ca2ac65572c", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "hjXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjskzzszyfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "kjqtfpXxynse"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'20'", "Col": "wkjfpXxynse"}], "NmyRuleName": "13b_10公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第13b行第10列“预征率%--销项(应纳)税额”必须等于第13b行第(2+4+6+8)列之和"}, {"RuleXh": "67f0c5fce1c94fc097eeb2175ec5e598", "RelateTable": "001,003", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'35'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'2'", "Col": "se"}, {"PreTable": "ywbw.sbZzsYbnsrFb2Bqjxsemx", "Key": "'ewbhxh'", "Value": "'26'", "Col": "se"}], "NmyRuleName": "35_3公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（二）》第35行第3列“本期认证相符的税控增值税专用发票--税额”必须等于第2行第3列“其中：本期认证相符且本期申报抵扣--税额”+第26行第3列“本期认证相符且本期未申报抵扣--税额”之和"}, {"RuleXh": "66e44554e9604f6cbde7eba8fcd14688", "RelateTable": "001", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'4'", "Col": "ynse"}, {"PreTable": "ywbw.sbZzsYbnsr", "Key": "'ewblxh'", "Value": "'3'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BNLJ'", "Col": "ynse"}, {"PreTable": "ytxx.sbZzsYbnsr", "Key": "'ewbhlbm'", "Value": "'JZJTHWJLW_BYS'", "Col": "ynse"}], "NmyRuleName": "19_4公式", "NmyRuleMessage": "自动计算，核定BMDBZ=Y,可修改；"}, {"RuleXh": "98ea3cccd86741af8123716cc05f6038", "RelateTable": "001,064", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "yjse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "xse"}, {"PreTable": "ywbw.sbZzsYbnsrFbHkys.sbZzsYbnsrFbHkysYjn", "Key": "'ewbhxh'", "Value": "'3'", "Col": "yzl"}], "NmyRuleName": "《航空运输企业试点地区分支机构传递单》已缴纳增值税情况 其他《应税服务范围注释》所列业务 预缴税额 公式计算", "NmyRuleMessage": "《航空运输企业试点地区分支机构传递单》已缴纳增值税情况 其他《应税服务范围注释》所列业务 预缴税额计算"}, {"RuleXh": "6714c6c50b5544a9a52916184c08de73", "RelateTable": "001,031", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhxh'", "Value": "'7'", "Col": "qcye"}, {"PreTable": "ytxx.sbZzsYbnsrFb4Sedjqk", "Key": "'ewbhlbm'", "Value": "'JJDJQK_JZJTXMJJDJEJS'", "Col": "qcye"}], "NmyRuleName": "7_1公式", "NmyRuleMessage": "取核定可修改。"}, {"RuleXh": "66fa68dd72424041824fe72c332b4d64", "RelateTable": "001,002", "RelateCells": [{"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "jshj"}, {"PreTable": "ywbw.sbZzsYbnsrFb1Bqxsqkmx", "Key": "'ewbhxh'", "Value": "'19'", "Col": "xse"}], "NmyRuleName": "19_11公式", "NmyRuleMessage": "《增值税及附加税费申报表附列资料（一）》第19行第11列“服务、不动产和无形资产--价税合计”必须等于第19行第9列“服务、不动产和无形资产--销售额”+第19行第10列“服务、不动产和无形资产--销项(应纳)税额”之和"}]