<template>
    <div class="user-defined-template-box" :class="isErp ? 'erp' : ''">
        <div class="custom-precheck-tool-bar">
            <a class="button solid-button" @click="handleNew">新增</a>
            <a class="button ml-20" @click="backToCreatePeriodCheck">返回</a>
        </div>
        <div class="custom-precheck-title">
            <div style="width: 318px">摘要</div>
            <div style="width: 325px">会计科目</div>
            <div style="width: 180px">取值</div>
            <div style="width: 137px">方向</div>
        </div>
        <el-scrollbar :always="true" max-height="calc(100% - 94px)">
            <div class="voucher-template-items">
                <div class="voucher-template-item" v-for="(templateItem, index) in tableData" :key="index">
                    <div class="voucher-template-item-top">
                        <div class="voucher-template-item-title-bar"></div>
                        <div class="voucher-template-item-title">{{ templateItem.vtName }}</div>
                        <div class="voucher-template-state-btn">
                            <el-switch
                                @change="(type) => switchChange(type, templateItem.vtId)"
                                v-model="templateItem.state"
                                style="--el-switch-on-color: var(--main-color); --el-switch-off-color: #edefee; margin-top: 2px"
                            />
                        </div>
                        <div class="voucher-template-item-btns">
                            <a class="link" @click="() => handleEdit(templateItem.vtId)">修改</a>
                            <a class="link" @click="() => deleteTemplate(templateItem.vtId)">删除</a>
                        </div>
                    </div>
                    <div class="voucher-template-item-line"></div>
                    <div class="voucher-template-item-main">
                        <div class="voucher-template-voucherline" v-for="item in templateItem.voucherTemplateLines" :key="item.asubId">
                            <ToolTip
                            placement="right"
                            effect="light"
                            :line-clamp="1"
                            :maxWidth="350"
                            popper-class="checkout-user-defined-template-tooltip"
                            :content="item.description"
                            >
                            <div class="description" style="width: 300px;padding-left:20px">
                                {{ item.description }}
                            </div>
                            </ToolTip>
                            <ToolTip
                                placement="right"
                                effect="light"
                                :line-clamp="1"
                                :maxWidth="350"
                                popper-class="checkout-user-defined-template-tooltip"
                                :content="item.asubCode + item.asubName"
                            >
                                <div class="asub-code" ref="itemRefs" style="width: 305px;padding-left:20px">
                                {{ item.asubCode + item.asubName }}
                                </div>
                            </ToolTip>
                            <div style="width: 160px">{{ item.valueType == 1010 ? "按公式取值" : "自动平衡(" + item.valueRate + "%)" }}</div>
                            <div style="width: 117px">{{ item.directory == 1 ? "借" : "贷" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </el-scrollbar>
    </div>
    <CustomPrecheckDetialDialog
        ref="customPrecheckDetialDialogRef"
        v-model:dialog-show="show"
        @success-save="handleCancel"
    ></CustomPrecheckDetialDialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import ToolTip from "@/components/Tooltip/index.vue";


import type { IVoucherTemplateModel } from "../tpyes";

import CustomPrecheckDetialDialog from "./CustomPrecheckDetialDialog.vue";

const isErp = ref(window.isErp);
const itemRefs = ref([]);

const customPrecheckDetialDialogRef = ref<InstanceType<typeof CustomPrecheckDetialDialog>>();

const defaultVgId = useVoucherGroupStore().defaultVgId ?? 1010;


const show = ref(false);
const handleNew = () => {
    const templateInfo = {
        vtId: 0,
        vtName: "",
        vtType: 0,
        vgId: defaultVgId,
        state: false,
    };
    const tableData = [
        {
            veId: 0,
            description: "",
            asubId: 0,
            asubCode: "",
            asubName: "",
            aacode: "",
            amount: 0,
            qutAmount: 0,
            valueType: 1010,
            valueRate: 0,
            directory: 1,
            calculations: [],
            show: true,
            index: 0,
        },
        {
            veId: 0,
            description: "",
            asubId: 0,
            asubCode: "",
            asubName: "",
            aacode: "",
            amount: 0,
            qutAmount: 0,
            valueType: 1020,
            valueRate: 100,
            directory: 2,
            calculations: [],
            show: true,
            index: 1,
        },
        {
            veId: 0,
            description: "",
            asubId: 0,
            asubCode: "",
            asubName: "",
            aacode: "",
            amount: 0,
            qutAmount: 0,
            valueType: 0,
            valueRate: 0,
            directory: 0,
            calculations: [],
            show: false,
            index: 2,
        },
        {
            veId: 0,
            description: "",
            asubId: 0,
            asubCode: "",
            asubName: "",
            aacode: "",
            amount: 0,
            qutAmount: 0,
            valueType: 0,
            valueRate: 0,
            directory: 0,
            calculations: [],
            show: false,
            index: 3,
        },
    ];
    customPrecheckDetialDialogRef.value?.changeTemplateInfo(templateInfo);
    customPrecheckDetialDialogRef.value?.setTableData(tableData);
    customPrecheckDetialDialogRef.value?.changeEditType("new");
    nextTick().then(() => {
        show.value = true;
    });
};
const handleEdit = (vtId: number) => {
    request({ url: "/api/CustomCarryOverTemplate?id=" + vtId }).then((res: IResponseModel<IVoucherTemplateModel>) => {
        if (res.state != 1000) {
            ElNotify({ type: "warning", message: res.msg ?? "请求失败" });
            return;
        }
        const templateInfo = {
            vtId: res.data.vtId,
            vtName: res.data.vtName,
            vtType: res.data.vtType,
            vgId: res.data.vgId,
            state: res.data.state,
        };
        const voucherTemplateLines = res.data.voucherTemplateLines;
        voucherTemplateLines.forEach((item) => {
            if (!item.calculations) {
                item.calculations = [];
            }
            if (item.calculations.length > 0) {
                item.calculations.forEach((calculationItem, index) => {
                    calculationItem.index = index;
                });
            }
        });
        customPrecheckDetialDialogRef.value?.changeTemplateInfo(templateInfo);
        customPrecheckDetialDialogRef.value?.setTableData(voucherTemplateLines);
        customPrecheckDetialDialogRef.value?.defaultRowClick();
        customPrecheckDetialDialogRef.value?.changeEditType("edit");
        nextTick().then(() => {
            show.value = true;
        });
    });
};
const handleCancel = () => {
    emit("successSave");
    customPrecheckDetialDialogRef.value?.handleCancel();
};

const emit = defineEmits(["deleteTemplate", "backToCreatePeriodCheck", "successSave"]);

const tableData = ref<any[]>([]);
const setData = (loadingData: any[]) => {
    tableData.value = loadingData;
};
defineExpose({ setData });

const switchChange = (state: any, vtId: number) => {
    request({ url: "/api/CustomCarryOverTemplate/State?id=" + vtId + "&state=" + state, method: "put" }).then((res: any) => {
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "请求失败" });
            const item = tableData.value.find((item) => item.vtId === vtId);
            if (item) {
                item.state = !state;
            }
        }
    });
};
const deleteTemplate = (vtId: number) => {
    ElConfirm("亲，确认要删除吗?").then((r: any) => {
        if (r) {
            request({ url: "/api/CustomCarryOverTemplate?id=" + vtId, method: "delete" }).then((res: any) => {
                if (res.state != 1000 || res.data !== true) {
                    ElNotify({ type: "warning", message: res.msg ?? "删除失败" });
                    return;
                }
                ElNotify({ type: "success", message: "删除成功" });
                emit("deleteTemplate");
            });
        }
    });
};
const backToCreatePeriodCheck = () => {
    emit("backToCreatePeriodCheck");
};
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.asub-code,.description {
    overflow: hidden;
    text-overflow: ellipsis; 
    white-space: nowrap;
}
.user-defined-template-box {
    height: 100%;
    padding: 0 20px;
    width: 1000px;
    box-sizing: border-box;
    margin: 0 auto;
    text-align: left;
    color: #404040;
    font-size: 12px;
    .custom-precheck-tool-bar {
        padding: 16px 0 10px;
    }
    .custom-precheck-title {
        & > div {
            line-height: 35px;
            font-size: var(--font-size);
            color: var(--font-color);
            font-weight: bold;
            display: inline-block;
            vertical-align: top;
            box-sizing: border-box;
            padding-left: 20px;
            background-color: #f8f8f8;
            border: 1px solid #edefee;
        }
    }
    .voucher-template-items {
        margin-top: 10px;
        .voucher-template-item {
            border: 1px solid #edefee;
            margin-top: 8px;
            &:first-child {
                margin-top: 0;
            }
            .voucher-template-item-top {
                height: 37px;
                padding-left: 12px;
                padding-right: 8px;
                .voucher-template-item-title-bar {
                    width: 3px;
                    height: 12px;
                    background: var(--main-color);
                    border-radius: 2px;
                    margin-top: 12px;
                    float: left;
                }
                .voucher-template-item-title {
                    line-height: 37px;
                    font-size: var(--font-size);
                    color: var(--font-color);
                    float: left;
                    margin-left: 5px;
                    font-weight: 500;
                }
                .voucher-template-state-btn {
                    float: right;
                    cursor: pointer;
                }
                .voucher-template-item-btns {
                    float: right;
                    height: 37px;
                    a.link {
                        float: left;
                        font-size: var(--h5);
                        margin-top: 8px;
                        margin-right: 24px;
                    }
                }
            }
            .voucher-template-item-line {
                height: 1px;
                background: #edefee;
                opacity: 0.7;
            }
            .voucher-template-item-main {
                .voucher-template-voucherline {
                    display: flex;
                    align-items: center;
                    & > div {
                        color: var(--font-color);
                        font-size: var(--font-size);
                        line-height: 31px;
                        vertical-align: top;
                        padding-left: 20px;
                        box-sizing: border-box;
                    }
                }
            }
        }
    }

    &.erp {
        .custom-precheck-title {
            margin-bottom: 0;
            & > div {
                background-color: var(--table-title-color);
            }
        }
        & + :deep(.el-overlay) {
            .el-overlay-dialog {
                .el-dialog__header {
                    height: 64px;
                    padding-top: 0;
                    border-bottom: 1px solid var(--border-color);
                    line-height: 64px;
                    .el-dialog__title {
                        font-weight: 600;
                        font-size: var(--h3);
                    }
                }
            }
        }
    }
}
.voucher-template-box {
    .vouher-template-detial {
        padding: 0 24px;
        color: #404040;
        font-size: 12px;
        text-align: left;
        .voucher-template-top {
            padding: 14px 0 10px;
            height: 30px;
            .detail-el-select(85px);
            > input.float-l {
                .detail-original-input(180px, 30px);
            }
            .txt {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                margin-right: 2px;
            }
        }
        .voucher-template-main {
            margin-bottom: 12px;
            :deep(.row-click-show-others) {
                .cell {
                    width: 100%;
                    height: 100%;
                    line-height: 36px;
                    padding: 0;
                    > div {
                        padding: 0 12px;
                        box-sizing: border-box;
                        position: relative;
                        .click-show-item {
                            position: absolute;
                            top: 3px;
                            left: 4px;
                            width: calc(100% - 8px);
                            height: calc(100% - 6px);
                            display: flex;
                            align-items: center;
                            & > input {
                                .detail-original-input(100%, 100%);
                            }
                            .el-select {
                                width: 100%;
                                height: 100%;
                                .select-trigger {
                                    line-height: 28px;
                                    .el-input {
                                        height: 30px;
                                        .el-input__inner {
                                            border: none;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .balance-panel {
            .balance-panel-title {
                font-size: var(--h3);
                color: var(--font-color);
                line-height: 22px;
                font-weight: 500;
                padding: 12px;
                border-bottom: 1px solid #edefee;
                background-color: #f8f8f8;
                border-radius: 4px 4px 0 0;
            }
            .balance-value-rate-container {
                padding-right: 20px;
                padding-top: 40px;
                padding-bottom: 40px;
                text-align: center;
                font-size: 0;
                background-color: #f8f8f8;
                border-radius: 0 0 4px 4px;
                span {
                    font-size: var(--font-size);
                }
                > input {
                    .detail-original-input(140px, 28px);
                    &::-webkit-outer-spin-button,
                    &::-webkit-inner-spin-button {
                        -webkit-appearance: none !important;
                    }
                    -moz-appearance: textfield;
                    &:focus,
                    &:hover {
                        -moz-appearance: number-input;
                    }
                }
            }
            .balance-panel-tips {
                padding-left: 220px;
                padding-top: 32px;
                padding-bottom: 20px;
                color: #777777;
            }
            .voucher-line-equation-top {
                height: 50px;
                padding: 0 12px;
                background-color: #f8f8f8;
                > .asub-name {
                    height: 28px;
                    width: 165px;
                    margin-top: 11px;
                    position: relative;
                    :deep(.el-select) {
                        width: 100% !important;
                        height: 100% !important;
                    }
                    :deep(.asub-img) {
                        top: 5px;
                        right: 5px;
                    }
                    :deep(.el-tabs__content) {
                        min-height: 302px !important;
                    }
                }
                .txt {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 50px;
                    margin-right: 2px;
                }
                .operator {
                    .detail-el-select(75px, 28px);
                    margin-top: 11px;
                }
                .balance {
                    .detail-el-select(105px, 28px);
                    margin-top: 11px;
                }
            }
            .voucher-line-equation-main {
                padding: 10px;
                padding-top: 0;
                background-color: #f8f8f8;
            }
        }
        .custom-precheck-btns {
            padding: 10px 0;
            margin-top: 12px;
            text-align: center;
            text-align: center;
        }
    }
}
</style>
<style lang="less">
.checkout-user-defined-template-tooltip {
    max-width: 300px;
}
.user-defined-template-box {

}
</style>
