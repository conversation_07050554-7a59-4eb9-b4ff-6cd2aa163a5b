import { ElConfirm } from "@/util/confirm";

export const editConfirm = (type: "voucherEdit" | "otherEdit" | "coverVoucher", ConfirmCallBack: Function = () => { }, cancelCallBack: Function = () => { }, pageName: string = "新") => {
    let msg = "";
    let confirmButtonText = "";
    let cancelButtonText = "";
    let closeOnly = false;
    let showClose = false;
    if (type === "voucherEdit") {
        msg = `您之前编辑的凭证还没有保存<br />点击'编辑原凭证'则可继续编辑原凭证<br />点击'进入新页面'则原凭证将不会保存并进入${pageName}页面`;
        confirmButtonText = "编辑原凭证";
        cancelButtonText = "进入新页面";
    } else if (type === "coverVoucher") {
        msg = `<div style="margin-left:${window.isErp ? '-30px' : '-10px'};width:400px">当前凭证已有数据，您是想要新增一个凭证还是覆盖原有数据？<br /><br />点击'新增凭证'则原凭证将不会保存并进入新凭证页面<br />点击'覆盖原数据'则直接覆盖原凭证数据</div>`;
        confirmButtonText = "新增凭证";
        cancelButtonText = "覆盖原数据";
        showClose = true;
    } else {
        msg = "您当前页面正在编辑还没有保存<br />点击'编辑原数据'则可留在原编辑页面<br />点击'查看新数据'则原数据将不会保存并进入新页面";
        confirmButtonText = "编辑原数据";
        cancelButtonText = "查看新数据";
    }
    return ElConfirm(
        msg,
        false,
        () => { closeOnly = true; }, // 点击关闭按钮不执行cancelCallBack
        "提示",
        {
            confirmButtonText,
            cancelButtonText,
        },
        true,
        9000,
        false,
        showClose,
        false,
        true
    ).then((r) => {
        if (r) {
            ConfirmCallBack();
        } else if (!closeOnly) {
            cancelCallBack();
        }
    });

}