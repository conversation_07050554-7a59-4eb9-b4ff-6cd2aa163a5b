<!-- 财报申报内容 -->
<template>
  <category-tabs
    class="main-content-body"
    :tab-list="tabList"
    @tab-click="handleTabClick">
    <template #content="{ activeIndex, tabData }">
      <div class="declaration-form">
        <div class="form-title">{{ tabData?.title || "资产负债表" }}</div>
        <div class="form-subtitle">（{{ describe }}）</div>
        <div class="form-info">
          <div class="info">
            <div
              v-for="(item, index) in financialFormList"
              :key="index">
              <span class="label">{{ item.label }}：</span>
              <span class="value">{{ item.value }}</span>
            </div>
          </div>
          <span>
            {{ `${basicInfo.taxAccountStandard === 1 ? "会小企" : "会企"}0${activeIndex + 1}表` }} 单位：{{ 10000000000000000000 }}元
          </span>
        </div>
      </div>
      <LMTable
        row-key="id"
        :show-overflow-tooltip="false"
        :data="tabData.data"
        :columns="tabData.columns">
        <template #lineName="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              <span :style="{ 'white-space': 'pre-wrap', 'font-weight': row.isBold ? 600 : 400 }">{{ row.lineName }}</span>
            </template>
          </el-table-column>
        </template>
        <template #lineNumber="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              {{ row.lineNumber || "" }}
            </template>
          </el-table-column>
        </template>
        <template #before="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="scope">
              <checkable-input
                v-if="scope.row.lineType === 1"
                v-model="scope.row[slotColumn.prop]"
                @change="handleInputDataChange"
                @blur="handleInputBlur($event, scope.row, tabData, slotColumn.prop, 'lineId')"
                @focus="handleInputFocus"></checkable-input>
              <Popover
                v-else-if="scope.row.lineType === 3"
                placement="right"
                :use-slot-content="true">
                <template #trigger>
                  <div class="equal-icon" />
                  <span style="float: right; line-height: 12px">
                    {{ String(parseFloat(scope.row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                  </span>
                </template>
                <template #content>
                  <div v-html="generateFormula(scope.row, tabData, slotColumn, 'lineId', 'lineName')"></div>
                </template>
              </Popover>
              <span v-else>
                {{ [0, 2].includes(scope.row.lineType) ? "" : scope.row[slotColumn.prop] }}
              </span>
            </template>
          </el-table-column>
        </template>
        <template #debitLineName="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              <span :style="{ 'white-space': 'pre-wrap', 'font-weight': row.isBold ? 600 : 400 }">{{ row.debitLineName }}</span>
            </template>
          </el-table-column>
        </template>
        <template #debitLineNumber="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              {{ row.debitLineNumber || "" }}
            </template>
          </el-table-column>
        </template>
        <template #after="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="scope">
              <checkable-input
                v-if="scope.row.debitLineType === 1"
                v-model="scope.row[slotColumn.prop]"
                @change="handleInputDataChange"
                @blur="handleInputBlur($event, scope.row, tabData, slotColumn.prop, 'debitLineId')"
                @focus="handleInputFocus"></checkable-input>
              <Popover
                v-else-if="scope.row.debitLineType === 3"
                placement="right"
                :use-slot-content="true">
                <template #trigger>
                  <div class="equal-icon" />
                  <span style="float: right; line-height: 12px">
                    {{ String(parseFloat(scope.row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                  </span>
                </template>
                <template #content>
                  <div v-html="generateFormula(scope.row, tabData, slotColumn, 'debitLineId', 'debitLineName')"></div>
                </template>
              </Popover>
              <span v-else>{{ [0, 2].includes(scope.row.debitLineType) ? "" : scope.row[slotColumn.prop] }}</span>
            </template>
          </el-table-column>
        </template>
      </LMTable>
    </template>
  </category-tabs>
</template>
<script setup lang="ts">
  import CategoryTabs from "../components/CategoryTabs.vue"
  import type { TabObj } from "../components/types"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { storeToRefs } from "pinia"
  import { getFinancialReportData, saveFinancialReportData, getFinancialReportFormula } from "@/api/taxDeclaration"
  import { ElNotify } from "@/utils/notify"
  const props = withDefaults(defineProps<{ currentTaxInfo?: { [key: string]: any } }>(), {
    currentTaxInfo: () => ({}),
  })

  const tabList = ref<Array<TabObj>>([
    { title: "资产负债表", isRequired: true, columns: [], data: [] },
    { title: "利润表", isRequired: true, columns: [], data: [] },
    { title: "现金流量表", columns: [], data: [] },
  ])

  const submissionPeriodMap: { [key: string]: any } = {
    年报: {
      // 数组中第一项为资产负债表，第二项为利润表，第三项为现金流量表
      dataUrlList: ["/api/BalanceSheet/year", "/api/IncomeSheet/year", "/api/CashFlowStatement/year"], // 请求表格数据
      saveUrlList: ["/api/BalanceSheet/save-year", "/api/IncomeSheet/save-year", "/api/CashFlowStatement/save-year"], // 保存表格数据
    },
    季报: {
      dataUrlList: ["/api/BalanceSheet/quarter", "/api/IncomeSheet/quarter", "/api/CashFlowStatement/quarter"],
      saveUrlList: ["/api/BalanceSheet/save-quarter", "/api/IncomeSheet/save-quarter", "/api/CashFlowStatement/save-quarter"],
    },
    月报: {
      dataUrlList: ["/api/BalanceSheet", "/api/IncomeSheet", "/api/CashFlowStatement"],
      saveUrlList: ["/api/BalanceSheet/save", "/api/IncomeSheet/save", "/api/CashFlowStatement/save"],
    },
  }
  let submissionPeriod = computed(() => {
    return props.currentTaxInfo.declareTypeName.split("_")[1]
  })

  const basicInfoStore = useBasicInfoStore()
  const { basicInfo } = storeToRefs(basicInfoStore)

  interface FormProps {
    prop?: string
    label?: string
    value?: any
  }
  // 财报右侧类似form表单内容
  const financialFormList: Array<FormProps> = [
    { prop: "1", label: "纳税人识别号", value: basicInfo.value.taxNumberS },
    { prop: "1", label: "税款所属期起止", value: `${props.currentTaxInfo.periodStart}--${props.currentTaxInfo.periodEnd}` },
    { prop: "1", label: "纳税人名称", value: basicInfo.value.taxpayerName },
    { prop: "1", label: "资产负债表日", value: props.currentTaxInfo.periodEnd },
  ]

  // 资产负债表格表头(企业会计准则)
  const balanceSheetColumns = [
    { label: "资产", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "期末余额", prop: "totalAmount", slot: "before" },
    { label: "上年年末余额", prop: "initialAmount", slot: "before" },
    { label: "负债和所有者权益（或股东权益）", prop: "debitLineName", slot: "debitLineName" },
    { label: "行次", prop: "debitLineNumber", slot: "debitLineNumber", width: 50 },
    { label: "期末余额", prop: "debitTotalAmount", slot: "after" },
    { label: "上年年末余额", prop: "debitInitialAmount", slot: "after" },
  ]
  // 小企业会计准则 月、季、年报 资产负债表
  const littleBalanceSheetColumns = [
    { label: "资产", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "期末余额", prop: "totalAmount", slot: "before" },
    { label: "年初余额", prop: "initialAmount", slot: "before" },
    { label: "负债和所有者权益", prop: "debitLineName", slot: "debitLineName" },
    { label: "行次", prop: "debitLineNumber", slot: "debitLineNumber", width: 50 },
    { label: "期末余额", prop: "debitTotalAmount", slot: "after" },
    { label: "年初余额", prop: "debitInitialAmount", slot: "after" },
  ]

  // 利润表表头
  const incomeSheetColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本期余额", prop: "monthTotal", slot: "before" },
    { label: "上期金额", prop: "yearTotal", slot: "before" },
  ]
  // 小企业会计准则 年报 利润表
  const littleIncomeSheetYearColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本年累计金额", prop: "monthTotal", slot: "before" },
    { label: "上年金额", prop: "yearTotal", slot: "before" },
  ]
  // 小企业会计准则 季报 利润表
  const littleIncomeSheetQuarterColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本期金额", prop: "monthTotal", slot: "before" },
    { label: "本年累计金额", prop: "yearTotal", slot: "before" },
  ]
  // 小企业会计准则 月报 利润表
  const littleIncomeSheetMonthColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本月金额", prop: "monthTotal", slot: "before" },
    { label: "本年累计金额", prop: "yearTotal", slot: "before" },
  ]

  // 现金流量表
  const cashFlowSheetColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本期余额", prop: "monthTotal", slot: "before" },
    { label: "上期金额", prop: "yearTotal", slot: "before" },
  ]
  // 小企业会计准则 年报 现金流量表
  const littleCashFlowSheetYearColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本年累计金额", prop: "monthTotal", slot: "before" },
    { label: "上年金额", prop: "yearTotal", slot: "before" },
  ]
  // 小企业会计准则 季报 现金流量表
  const littleCashFlowSheetQuarterColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本期金额", prop: "monthTotal", slot: "before" },
    { label: "本年累计金额", prop: "yearTotal", slot: "before" },
  ]
  // 小企业会计准则 月报 现金流量表
  const littleCashFlowSheetMonthColumns = [
    { label: "项目", prop: "lineName", slot: "lineName" },
    { label: "行次", prop: "lineNumber", slot: "lineNumber", width: 50 },
    { label: "本月金额", prop: "monthTotal", slot: "before" },
    { label: "本年累计金额", prop: "yearTotal", slot: "before" },
  ]
  const describe = ref<string>("")
  // 处理使用哪个表头
  function initTabList() {
    // 这一步判断是年报还是月报还是季报
    if (basicInfo.value.taxAccountStandard === 1) {
      // 小企业会计准则
      tabList.value[0].columns = littleBalanceSheetColumns
      switch (submissionPeriod.value) {
        case "年报":
          tabList.value[1].columns = littleIncomeSheetYearColumns
          tabList.value[2].columns = littleCashFlowSheetYearColumns
          break
        case "季报":
          tabList.value[1].columns = littleIncomeSheetQuarterColumns
          tabList.value[2].columns = littleCashFlowSheetQuarterColumns
          break
        case "月报":
          tabList.value[1].columns = littleIncomeSheetMonthColumns
          tabList.value[2].columns = littleCashFlowSheetMonthColumns
          break
        default:
          break
      }
    }
    switch (basicInfo.value.taxAccountStandard) {
      case 1: // 小企业会计准则
        describe.value = "适用执行小企业会计准则的企业"
        break
      case 2: // 企业会计准则
        switch (props.currentTaxInfo.subAccountingStandard) {
          case 1: // 未执行
            describe.value = "适用未执行新金融准则、新收入准则和新租赁准则的一般企业"
            break
          case 2: // 已执行
            describe.value = "适用已执行新金融准则、新收入准则和新租赁准则的一般企业"
            break
          default:
            break
        }
        // 企业会计准则不区分年季月
        tabList.value[0].columns = balanceSheetColumns
        tabList.value[1].columns = incomeSheetColumns
        tabList.value[2].columns = cashFlowSheetColumns
        break
      default:
        break
    }
  }
  initTabList()

  // 获取表格数据
  function getTableData() {
    const urlList = submissionPeriodMap[submissionPeriod.value].dataUrlList
    getFinancialReportData(urlList).then((res) => {
      if (res.every((item) => item.state === 1000)) {
        res.forEach((item, index) => {
          tabList.value[index].data = item.data
        })
      }
    })
  }
  getTableData()

  // 所有表格公式
  const tableFormulaList: { [key: string]: any } = ref({
    资产负债表: {
      url: "/api/BalanceSheet/formulas",
      formulaData: {},
    },
    利润表: {
      url: "/api/IncomeSheet/formulas",
      formulaData: {},
    },
    现金流量表: {
      url: "/api/CashFlowStatement/formulas",
      formulaData: {},
    },
  })
  function handleTabClick(row: TabObj) {
    const currentSheetInfo = tableFormulaList.value[row.title]
    if (!Object.keys(currentSheetInfo.formulaData).length) {
      getFinancialReportFormula(currentSheetInfo.url).then((res) => {
        if (res.state === 1000) {
          currentSheetInfo.formulaData = res.data
        }
      })
    }
  }
  handleTabClick(tabList.value[0])

  // 生成表格公式提示
  const generateFormula = computed(() => {
    return (
      row: { [key: string]: any },
      currentTabInfo: { [key: string]: any },
      columnInfo: { [key: string]: any },
      lineIdField: string,
      rowNameField: string,
    ) => {
      const formulaInfo = tableFormulaList.value[currentTabInfo.title].formulaData
      let formulaList: { [key: string]: any } = []
      for (let key in formulaInfo) {
        if (row[lineIdField] == key) {
          formulaList = formulaInfo[key]
          break
        }
      }
      let str = "<span>公式：<span><br/>"
      formulaList.forEach((item: any, index: number) => {
        const calcRow = currentTabInfo.data.find((rowItem: any) => rowItem[lineIdField] === item.asubId)
        const operate = item.operator === 1 ? "+" : "-"
        str += `<span style="white-space: pre-wrap">${index !== 0 ? `${operate}` : ""}${calcRow[rowNameField].trim()}(${columnInfo.label},  ${String(parseFloat(calcRow[columnInfo.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")})<span><br/>`
      })

      return str
    }
  })

  // 财报表格数据是否保存
  let isSave = ref(true)
  // 输入框值改变
  function handleInputDataChange() {
    isSave.value = false
  }

  // 输入框失去焦点
  function handleInputBlur(
    value: number,
    row: { [key: string]: any },
    currentTabInfo: { [key: string]: any },
    formulaField: string,
    lineIdField: string,
  ) {
    const inputFormula = findLineAndFormula(currentTabInfo, row, lineIdField)
    if (inputFormula.formulaLineId) {
      const inputFormulaResult = formulaCalculate(currentTabInfo, inputFormula, formulaField, value, lineIdField)
      handleInputBlur(
        inputFormulaResult.value,
        currentTabInfo.data[inputFormulaResult.formulaIndex],
        currentTabInfo,
        formulaField,
        lineIdField,
      )
    }
  }
  // 合计行公式计算
  function findLineAndFormula(currentTabInfo: { [key: string]: any }, row: { [key: string]: any }, lineIdField: string) {
    const formulaData = tableFormulaList.value[currentTabInfo.title].formulaData
    let formulaLineId: string = "",
      formula: { [key: string]: any } = {}
    // 根据lineId判断要进行的公式计算
    for (let key in formulaData) {
      const currentTabFormula = formulaData[key]
      const rowFormula = currentTabFormula.find((item: any) => item.asubId === row[lineIdField])
      if (rowFormula) {
        formulaLineId = key
        formula = rowFormula
        break
      }
    }

    return { formulaLineId, formula }
  }
  // 合计行公式计算
  /**
   *
   * @param currentTabInfo 当前活跃的tab页信息
   * @param formulaInfo 公式信息，包含合计行id+合计行所有公式
   * @param formulaField 要进行公式计算的单元格字段
   * @param value 输入框值
   * @param lineIdField 单元格对应行id字段
   */
  function formulaCalculate(
    currentTabInfo: { [key: string]: any },
    formulaInfo: { [key: string]: any },
    formulaField: string,
    value: number,
    lineIdField: string,
  ) {
    // 合计行的index
    const formulaIndex = currentTabInfo.data.findIndex((item: any) => item[lineIdField] == formulaInfo.formulaLineId)

    const operate = formulaInfo.formula!.operator === 1 ? "+" : "-"
    const oldValOperate = operate === "+" ? "-" : "+"

    let str = `${currentTabInfo.data[formulaIndex][formulaField]} ${oldValOperate} ${inputOldVal.value} ${operate} ${value}`

    console.log(str, "str")

    // 保留此次合计行旧值用于下一次计算
    inputOldVal.value = currentTabInfo.data[formulaIndex][formulaField]
    console.log(eval(str))

    currentTabInfo.data[formulaIndex][formulaField] = parseFloat(eval(str).toFixed(2))

    return { formulaIndex, value: currentTabInfo.data[formulaIndex][formulaField] }
  }

  // 要计算的单元格旧值
  let inputOldVal = ref()
  function handleInputFocus(value: number) {
    inputOldVal.value = value
  }

  function save() {
    const urlList = submissionPeriodMap[submissionPeriod.value].saveUrlList
    saveFinancialReportData(
      urlList,
      tabList.value.map((item) => item.data),
    ).then((res) => {
      if (res.every((item) => item.state === 1000)) {
        getTableData()
        isSave.value = true
        ElNotify({
          type: "success",
          message: "保存成功",
        })
      }
    })
  }

  defineExpose({ isSave, save })
</script>
<style lang="scss" scoped>
  @use "@/style/TaxDeclaration/index.scss" as *;

  .declaration-form {
    .form-title {
      margin-bottom: 4px;
      font-size: var(--h3);
      text-align: center;
    }

    .form-subtitle {
      margin-bottom: 14px;
      font-weight: 400;
      font-size: var(--h4);
      color: var(--dark-grey);
      text-align: center;
    }

    .form-info {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .info {
        flex: 3;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        div {
          width: 50%;
          box-sizing: border-box;
          &:nth-child(2n) {
            padding-left: 80px;
          }

          span {
            line-height: 20px;
            font-size: var(--h4);
          }
        }
      }
      span {
        flex: 1;
        align-self: flex-end;
        line-height: 32px;
        font-size: 14px;
        text-align: right;
      }
    }
  }
  // :deep(.el-table) {
  //   overflow: visible !important;
  //   .el-table__header-wrapper {
  //     z-index: 3;
  //     position: sticky;
  //     border-top: 1px var(--el-border-color) var(--el-border-style);
  //     top: 0;
  //   }
  // }
</style>
