<script lang="ts" setup>
import { AccountStandard, useAccountSetStore, AccountSubjectType } from "@/store/modules/accountSet";
import { defineAsyncComponent, inject, ref, computed } from "vue";
import { popoverHandleCloseKey } from "./symbols";

const props = defineProps({
    isById: { type: Boolean, default: false },
    isByNumberId: { type: Boolean, default: false },
    isExpansion: { type: Boolean, default: false },
    showTree: { type: Boolean, default: false },
    showDisabled: { type: Boolean, default: false },
    exposeIdAndName: { type: Boolean, default: false },
    subjextPickerBackText: { type: String, default: "返回" },
});
const isById = computed(() => {
    return props.isById;
});
const isExpansion = computed(() => {
    return props.isExpansion;
});
const popoverHandleClose = inject(popoverHandleCloseKey);

const accountSetStore = useAccountSetStore();
const accountingStandard = accountSetStore.accountSet?.accountingStandard as number;

let tabIndex = 0;
const editableTabs = ref<any[]>([]);

const modelVal = ref(1);
const setEditableTabs = () => {
    switch (accountingStandard) {
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            addTree(AccountSubjectType.Asset, "资产");
            addTree(AccountSubjectType.Debit, "负债");
            addTree(AccountSubjectType.Owe, "权益");
            addTree(AccountSubjectType.Cost, "成本");
            addTree(AccountSubjectType.Income, "损益");
            break;
        case AccountStandard.CompanyStandard:
            addTree(AccountSubjectType.Asset, "资产");
            addTree(AccountSubjectType.Debit, "负债");
            addTree(AccountSubjectType.Common, "共同");
            addTree(AccountSubjectType.Owe, "权益");
            addTree(AccountSubjectType.Cost, "成本");
            addTree(AccountSubjectType.Income, "损益");
            break;
        case AccountStandard.FolkComapnyStandard:
            addTree(AccountSubjectType.Asset, "资产");
            addTree(AccountSubjectType.Debit, "负债");
            addTree(AccountSubjectType.NetWorth, "净资产");
            addTree(AccountSubjectType.Revenue, "收入");
            addTree(AccountSubjectType.Expenses, "费用");
            break;
        case AccountStandard.UnionStandard:
            addTree(AccountSubjectType.Asset, "资产");
            addTree(AccountSubjectType.Debit, "负债");
            addTree(AccountSubjectType.NetWorth, "净资产");
            addTree(AccountSubjectType.Revenue, "收入");
            addTree(AccountSubjectType.Disbursement, "支出");
            break;
    }
    modelVal.value = 1;
};
setEditableTabs();

function addTree(type: number, title: string) {
    const newTabName = ++tabIndex;
    editableTabs.value.push({
        title: title,
        name: newTabName,
        asubType: type,
    });
}

const filterTabsByAsubType = (asubType: number) => {
    editableTabs.value = editableTabs.value.filter((item, index) => {
        if (item.asubType === asubType) {
            modelVal.value = index + 1;
            return item;
        }
    });
};

function handleReturnClick() {
    if (popoverHandleClose !== undefined) {
        popoverHandleClose();
    }
}

const SubjectTreeComponent = defineAsyncComponent(() => import("./SubjectTree.vue"));

const isErp = ref(window.isErp);

defineExpose({
    setEditableTabs,
    filterTabsByAsubType,
});
</script>

<template>
    <div class="subject-dialog-container" :class="isErp ? 'erp' : ''">
        <div class="subject-dialog-title">选择科目</div>
        <el-tabs :model-value="modelVal">
            <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name" :lazy="true">
                <SubjectTreeComponent
                    :is="SubjectTreeComponent"
                    :expose-id-and-name="exposeIdAndName"
                    :tabName="item.name"
                    :asub-type="item.asubType"
                    :isExpansion="isExpansion"
                    :is-by-id="isById"
                    :is-by-numberId="isByNumberId"
                    :showTree="showTree"
                    :showDisabled="showDisabled"
                ></SubjectTreeComponent>
            </el-tab-pane>
        </el-tabs>
        <div class="buttons">
            <a class="button solid-button" @click="handleReturnClick">{{ subjextPickerBackText }}</a>
        </div>
    </div>
</template>

<style lang="less" scoped>
.subject-dialog-container {
    .subject-dialog-title {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        font-weight: bold;
        margin-bottom: 10px;
        text-align: center;
    }

    .buttons {
        margin-top: 15px;
        padding-left: 20px;
    }

    &.erp {
        :deep(.el-tabs) {
            .el-tabs__header {
                .el-tabs__nav-scroll {
                    display: flex !important;
                    .el-tabs__item {
                        padding: 0;
                        & + .el-tabs__item {
                            margin-left: 18px;
                        }
                    }
                }
            }
        }
        .buttons {
            text-align: right;
        }
    }
}
</style>
