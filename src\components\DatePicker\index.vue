<template>
    <div class="picker">
        <el-date-picker
            v-model="innerStartPid"
            :type="dateType"
            :disabled-date="isPeriodList ? disabledDate: disabledDateStart"
            :clearable="clearable"
            :editable="editable"
            :teleported="false"
            :value-format="valueFormat"
            :format="labelFormat"
            @focus="() => emit('focus')"
            @blur="() => emit('blur')"
            @change="handleChangeStart"
            @visible-change="handleVisibleChange"
        />
        <span class="ml-10 mr-10">至</span>
        <el-date-picker
            v-model="innerEndPid"
            :type="dateType"
            :disabled-date="isPeriodList ? disabledDate: disabledDateEnd"
            :clearable="clearable"
            :editable="editable"
            :teleported="false"
            :value-format="valueFormat"
            :format="labelFormat"
            @focus="() => emit('focus')"
            @blur="() => emit('blur')"
            @change="handleChangeEnd"
            @visible-change="handleVisibleChange"
        />
    </div>
</template>

<script setup lang="ts">
import { watch, computed, inject, ref } from "vue";

import { stopPopoverCloseKey } from "@/components/Picker/SubjectPicker/symbols";
import { ElNotify } from "@/util/notify";
import { getPeriodsApi } from "@/api/period";
import { componentFinishKey } from "@/symbols";
import { dayjs } from "element-plus";

const componentFinish = inject(componentFinishKey);
const stopPopoverClose = inject(stopPopoverCloseKey) as Function;

const handleVisibleChange = (visible: boolean) => {
    if (!visible) {
        if (stopPopoverClose) stopPopoverClose();
    }
};

const props = defineProps({
    startPid: { type: String, required: true },
    endPid: { type: String, required: true },
    periodInfo: String,
    disabledDateStart: {
        type: Function,
        default: () => {},
    },
    disabledDateEnd: {
        type: Function,
        default: () => {},
    },
    clearable: {
        type: Boolean,
        default: true,
    },
    editable: {
        type: Boolean,
        default: true,
    },
    dateType: {
        type: String,
        default: "date",
    },
    valueFormat: {
        type: String,
        default: "YYYY-MM-DD", //绑定值得格式
    },
    labelFormat: {
        type: String,
        default: "YYYY-MM-DD", //输入框中展示格式
    },
    isPeriodList: {  //组件本身获取数据，使用不可取范围
        type: Boolean,
        default: false,
    }
});
const emit = defineEmits<{
    (e: "update:startPid", date: string): void;
    (e: "update:endPid", date: string): void;
    (e: "update:periodInfo", info: string): void;
    (e: "userChange"): void;
    (e: "focus"): void;
    (e: "blur"): void;
    (e: "getActPid", start: number, end: number): void;
}>();

const innerStartPid = computed({
    get() {
        return props.startPid || "";
    },
    set(value: string) {
        emit("update:startPid", value || "");
    },
});

const innerEndPid = computed({
    get() {
        return props.endPid;
    },
    set(value: string) {
        emit("update:endPid", value);
    },
});

const handleChangeStart = (val: string) => {
    if (val > innerEndPid.value && props.dateType === "month") {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
    }
    if (props.isPeriodList) {
        handleActPid();
    }
    emit('userChange');
}
const handleChangeEnd = (val: string) => {
    if (val < innerStartPid.value && props.dateType === "month") {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
    }
    if (props.isPeriodList) {
        handleActPid();
    }
    emit('userChange');
}
function handleActPid() {
    actStartPid.value = periodData.value.find((item) => item.time === innerStartPid.value)?.pid ?? 0;
    actEndPid.value = periodData.value.find((item) => item.time === innerEndPid.value)?.pid ?? 0;
    emit("getActPid", actStartPid.value, actEndPid.value);
}

const periodData = ref<any[]>([]);
const actStartPid = ref(0);
const actEndPid = ref(0);
const loadPeriodData = async (_s?: number, _e?: number) => {
    await getPeriodsApi()
        .then((data: any) => {
            periodData.value = data.data.reverse().map((item: any) => {
                return {
                    ...item,
                    time: item.year + "" + String(item.sn).padStart(2, "0"),
                }
            });
            if (periodData.value.length > 0) {
                if (actStartPid.value === 0) {
                    actStartPid.value = periodData.value[0].pid;
                }
                if (actEndPid.value === 0) {
                    actEndPid.value = periodData.value[0].pid;
                }
                const this_s_index = periodData.value.findIndex((item) => item.pid === _s);
                const this_e_index = periodData.value.findIndex((item) => item.pid === _e);
                if (this_s_index === -1 && _s !== undefined) {
                    actStartPid.value = periodData.value[0].pid;
                }
                if (this_e_index === -1 && _e !== undefined) {
                    actEndPid.value = periodData.value[0].pid;
                }
            }
            if (componentFinish !== undefined) {
                componentFinish();
            }
        })
        .catch((error) => {
            console.log(error);
        });
};
function disabledDate(time: Date) {
    const start = periodData.value[periodData.value.length - 1]?.time ?? new Date();
    const end = periodData.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
if (props.isPeriodList) {
    loadPeriodData();
}

const periodInfoValue = computed(() => {
    if (innerStartPid.value === "" || innerEndPid.value === "") return "";
    return innerStartPid.value + " 至 " + innerEndPid.value;
});
watch(periodInfoValue, (newValue) => emit("update:periodInfo", newValue), { immediate: true });

function checkExchangePid() {
    if (innerStartPid.value > innerEndPid.value) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦, 系统已为您自动调整~",
            type: "warning",
        });
        const temp = innerStartPid.value;
        innerStartPid.value = innerEndPid.value;
        innerEndPid.value = temp;
    }
}

defineExpose({
    loadPeriodData,
    checkExchangePid,
});
</script>

<style lang="less" scoped>
@import url("@/style/Functions.less");
.picker {
    > span {
        line-height: 32px;
        color: var(--font-color);
        font-size: var(--font-size);
        width: 16px;
        display: inline-block;
        text-align: center;
    }
    .detail-el-date-picker(132px, 32px);
    display: flex;
    :deep(.el-date-editor) {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
        & .el-input__suffix-inner {
            position: absolute;
            right: 2px;
            top: 9px;
        }
    }
    :deep(.el-popper) {
        max-width: fit-content;
    }
    :deep(.el-month-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
    }
    :deep(.el-year-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
    }
}
</style>
