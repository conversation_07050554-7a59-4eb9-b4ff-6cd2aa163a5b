export interface IAgeSet {
    ageId: number;
    title: string;
    days: number;
}

export interface IAges {
    ageId: number;
    title: string;
    amount: string;
    percent: string;
}
export interface IRows {
    num: string;
    name: string;
    params: string;
    initial: string;
    debit: string;
    credit: string;
    total: string;
    totalPercent: string;
    ages: IAges[];
}
export interface IAgeSetTableData {
    total: number;
    rows: IRows[];
}
