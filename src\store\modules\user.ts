import { defineStore } from "pinia";
import store from "@/store";
import { ref } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import { request, type IResponseModel } from "@/util/service";
import { closeWindow, logout } from "@/util/url";
import { ElAlert } from "@/util/confirm";
import { isInWxWork } from "@/util/wxwork";
import { getLemonClient, isLemonClient } from "@/util/lmClient";

export const useUserStore = defineStore("user", () => {
    const userName = ref<string>("");

    const getUserName = () => {
        return new Promise<string>((resolve, reject) => {
            const globalToken = getGlobalToken();
            const isErpOrAccountingAgent = window.isErp || window.isAccountingAgent;
            const url = isErpOrAccountingAgent ? "/api/User/NameWithAccountSet" : "/api/User/Name";
            if (globalToken === "" && isErpOrAccountingAgent) {
                reject("token为空");
            } else {
                request({
                    url,
                    method: "get",
                })
                    .then((res: any) => {
                        const data = res as IResponseModel<string>;
                        if (data.state === 1000) {
                            userName.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    const logoutDialog = () => {
        ElAlert({
            message: "您的登录状态已过期，请您重新登录。",
            hideCancel: true,
            zIndex: 1000,
            closeOnClickModal: true,
            isGlobal: true,
            isInTabsContent: false,
        }).then(() => {
            if (isLemonClient()) {
                getLemonClient().init();
            } else if (isInWxWork()) {
                closeWindow();
            } else {
                logout();
            }
        });
    };

    return { userName, getUserName, logoutDialog };
});

/** 在setup外使用 */
export function useUserStoreHook() {
    return useUserStore(store);
}
