<template>
    <div class="subject-dialog-container">
        <div class="subject-dialog-title">选择科目</div>
        <el-tabs :model-value="tabsNumber">
            <el-tab-pane v-for="item in editableTabs" :key="item.name" :label="item.title" :name="item.name" :lazy="true">
                <component :is="SubjectTreeComponent" :asub-type="item.asubType"></component>
            </el-tab-pane>
        </el-tabs>
        <div class="buttons">
            <a class="button solid-button" @click="handleReturnClick">{{ btntxt }}</a>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { AccountStandard, useAccountSetStore, AccountSubjectType } from "@/store/modules/accountSet";
import { defineAsyncComponent, inject, ref, watch } from "vue";
import { popoverHandleCloseKey } from "./symbols";

const popoverHandleClose = inject(popoverHandleCloseKey);
const props = defineProps({
    tabs: {
        type: Number,
        default: 1,
    },
    isUnionSalary: {
        type: Boolean,
        default: false,
    },
});
const accountSetStore = useAccountSetStore();
const btntxt = ref("");
btntxt.value = inject("btntxt") as string;
let tabIndex = 0;
const editableTabs = ref<any[]>([]);
const tabsNumber = ref(1);

const accountingStandard = accountSetStore.accountSet?.accountingStandard as number;
switch (accountingStandard) {
    case AccountStandard.FolkComapnyStandard:
        addTree(AccountSubjectType.Asset, "资产");
        addTree(AccountSubjectType.Expenses, "费用");
        break;
    case AccountStandard.LittleCompanyStandard:
    case AccountStandard.CompanyStandard:
    case AccountStandard.FarmerCooperativeStandard:
    case AccountStandard.FarmerCooperativeStandard2023:
    case AccountStandard.VillageCollectiveEconomyStandard:
        addTree(AccountSubjectType.Asset, "资产");
        addTree(AccountSubjectType.Cost, "成本");
        addTree(AccountSubjectType.Income, "损益");
        break;
    case AccountStandard.UnionStandard:
        if (props.isUnionSalary) {
            addTree(AccountSubjectType.Disbursement, "支出");
        } else {
            addTree(AccountSubjectType.Asset, "资产");
            addTree(AccountSubjectType.NetWorth, "净资产");
        }
        break;
}

function addTree(type: number, title: string) {
    const newTabName = ++tabIndex;
    editableTabs.value.push({
        title: title,
        name: newTabName,
        asubType: type,
    });
}

function handleReturnClick() {
    if (popoverHandleClose !== undefined) {
        popoverHandleClose();
    }
}
let highLight = ref<boolean>();
highLight.value = inject("highLight") as boolean;
watch(props, () => {
    tabsNumber.value = highLight.value
        ? editableTabs.value.find((item: any) => {
              return item.asubType == props.tabs;
          }).name
        : 1;
}, { immediate: true});
tabsNumber.value = highLight.value ? (inject("tabs") as number) : 1;
const SubjectTreeComponent = defineAsyncComponent(() => import("./asubTree.vue"));
</script>

<style lang="less" scoped>
.subject-dialog-container {
    .subject-dialog-title {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        font-weight: bold;
        margin-bottom: 10px;
        text-align: center;
    }

    .buttons {
        margin-top: 15px;
        padding-left: 20px;
    }
}
:deep(.el-tabs__item.is-top:last-child) {
    padding-right: 20px !important;
}
</style>
