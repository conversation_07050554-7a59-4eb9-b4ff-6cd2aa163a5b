<template>
    <div class="content">
        <div class="title">总账对账差异明细</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <span>科目：{{ subject(info.projectType) }}</span>
                    <span class="pl-10">期间：{{ getCurrentInfoTitle() }} </span>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <a class="button mr-10" v-permission="['checkledgerdedetail-canprint']" @click="handlePrint">打印</a>
                    <a class="button mr-10" v-permission="['checkledgerdedetail-canexport']" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <Table 
                    :data="tableData" 
                    :columns="columns" 
                    :scrollbar-show="true"
                    :tableName="setModule"
                >
                    <template #voucher>
                        <el-table-column label="凭证" align="center" header-align="center">
                            <el-table-column 
                                label="日期" 
                                align="left" 
                                header-align="left" 
                                prop="voucherDate" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'voucherDate')"
                            > </el-table-column>
                            <el-table-column 
                                label="凭证号" 
                                align="left" 
                                header-align="left" 
                                prop="voucherNo" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'voucherNo')"
                            >
                                <template #default="scope">
                                        <a
                                            class="link"
                                            v-if="GetVoucherText(scope.row.pid, scope.row.vid)"
                                            @click="
                                                globalWindowOpenPage(
                                                    `/Voucher/VoucherPage?pid=${scope.row.pid}&vid=${scope.row.vid}`,
                                                    '记账凭证'
                                                )
                                            "
                                            >{{ scope.row.voucherNo }}</a
                                        >
                                    <template v-else>{{ scope.row.voucherNo }}</template>
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="摘要" 
                                align="left" 
                                header-align="left" 
                                prop="description" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'description')"
                            >
                                <template #default="scope">
                                    <!-- <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"> -->
                                        {{ scope.row.description }}
                                        <!-- </span> -->
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="借方" 
                                align="right" 
                                header-align="right" 
                                prop="debit" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'debit')"
                            >
                                <template #default="scope">
                                    {{ formatMoney(scope.row.debit) }}
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="贷方" 
                                align="right" 
                                header-align="right" 
                                prop="credit" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'credit')"
                            >
                                <template #default="scope">
                                    {{ formatMoney(scope.row.credit) }}
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="余额" 
                                align="right" 
                                header-align="right" 
                                prop="total" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'total')"
                            >
                                <template #default="scope">
                                    {{ formatMoney(scope.row.total) }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #fixedAsset>
                        <el-table-column label="资产卡片" align="center" header-align="center">
                            <el-table-column 
                                label="变动期间" 
                                align="left" 
                                header-align="left" 
                                prop="fixedAssetDate" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'fixedAssetDate')"
                            > </el-table-column>
                            <el-table-column 
                                label="资产编号" 
                                align="left" 
                                header-align="left" 
                                prop="fanum" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'fanum')"
                            >
                                <template #default="scope">
                                        <temlpate v-if="!isBossSystem">
                                            <a
                                                class="link"
                                                @click="
                                                    globalWindowOpenPage(
                                                        `/FixedAssets/FixedAssets?from=FACheckDetail&period=${scope.row.fpid}&tabflag=2&r=${Math.random()}`,
                                                        '资产管理'
                                                    )
                                                "
                                                >{{ scope.row.fanum }}</a
                                            >
                                        </temlpate>
                                        <template v-else>
                                            {{ scope.row.fanum }}
                                        </template>
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="变动类型" 
                                align="left" 
                                header-align="left" 
                                prop="changeTypeText" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'changeTypeText')"
                            > </el-table-column>
                            <el-table-column 
                                label="凭证号" 
                                align="left" 
                                header-align="left" 
                                prop="fvoucherNo" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'fvoucherNo')"
                            >
                                <template #default="scope">
                                        <a
                                            class="link"
                                            v-if="GetVoucherText(scope.row.fvpid, scope.row.fvvid)"
                                            @click="
                                                globalWindowOpenPage(
                                                    `/Voucher/VoucherPage?pid=${scope.row.fvpid}&vid=${scope.row.fvvid}`,
                                                    '查看凭证'
                                                )
                                            "
                                            >{{ scope.row.fvoucherNo }}</a
                                        >
                                    <template v-else>{{ scope.row.fvoucherNo }}</template>
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="增加" 
                                align="right" 
                                header-align="right" 
                                prop="increase" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'increase')"
                            >
                                <template #default="scope">
                                    {{ scope.row.increase ? formatMoney(scope.row.increase) : "" }}
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="减少" 
                                align="right" 
                                header-align="right" 
                                prop="decrease" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'decrease')"
                            >
                                <template #default="scope">
                                    {{ scope.row.decrease ? formatMoney(scope.row.decrease) : "" }}
                                </template>
                            </el-table-column>
                            <el-table-column 
                                label="余额" 
                                align="right" 
                                header-align="right" 
                                prop="fatotal" 
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'fatotal')"
                            >
                                <template #default="scope">
                                    {{ scope.row.fatotal ? formatMoney(scope.row.fatotal) : "" }}
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "FACheckDetail",
};
</script>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { request } from "@/util/service";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney } from "@/util/format";
import { checkPermission } from "@/util/permission";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { ref, onActivated,onMounted,onUnmounted } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "FACheckDetail";
const accountSetStore = useAccountSetStore();
const accountingStandard = accountSetStore.accountSet!.accountingStandard as number;
const isErp = ref(window.isErp);
const route = useRoute();
interface Info {
    startPid: string;
    endPid: string;
    projectType: string;
}
const info = ref<Info>({
    startPid: route.query.startPid as string,
    endPid: route.query.endPid as string,
    projectType: route.query.projectType as string,
});

(window as any).globalWindowOpenPage = globalWindowOpenPage;
const isBossSystem = ref(window.isBossSystem);
const columns = ref<Array<IColumnProps>>([
    //
    {
        slot: "voucher",
    },
    {
        slot: "fixedAsset",
    },
    {
        label: "差额",
        prop: "balance",
        align: "right",
        headerAlign: "right",
        resizable: false,
        formatter: (row, column, value) => {
            return formatMoney(value);
        },
    },
]);
const subject = (projectType: string) => {
    switch (Number(projectType)) {
        case 1010:
            return "固定资产";
        case 1020:
            return "累计折旧";
        case 1030:
            return "资产减值准备";
        case 2010:
            return "无形资产";
        case 2020:
            return "累计摊销";
        case 2030:
            return "无形资产减值准备";
        case 3010:
            return accountingStandard === 3 ? '待摊费用' : "长期待摊费用";
        default:
            return "";
    }
};
const GetVoucherText = (pid: number, vid: number) => checkPermission(["voucher-canedit"]) && pid != 0 && vid != 0;
function handlePrint() {
    globalPrint(`/api/FACheckDetail/Print?` + getUrlSearchParams(info.value));
}
function handleExport() {
    globalExport(`/api/FACheckDetail/Export?` + getUrlSearchParams(info.value));
}
interface IPeriod {
    pid: number;
    periodInfo: string;
}

const periodList = ref<IPeriod[]>();
function getPeriodApi() {
    return request({
        url: `/api/FAPeriod/List?dpcFlag=false`,
        method: "get",
    });
}

function getCurrentInfoTitle() {
    let currentPeriodInfo: string = "";
    if (info.value.startPid === info.value.endPid) {
        currentPeriodInfo = periodList.value?.find((item) => item.pid === Number(info.value.endPid))?.periodInfo || "";
    } else {
        currentPeriodInfo =
            (periodList.value?.find((item) => item.pid === Number(info.value.startPid))?.periodInfo || "") +
            "——" +
            (periodList.value?.find((item) => item.pid === Number(info.value.endPid))?.periodInfo || "");
    }
    return currentPeriodInfo;
}

const tableData = ref<any[]>([]);
function getTableData() {
    request({
        url: `/api/FACheckDetail/List?` + getUrlSearchParams(info.value),
    }).then((res: any) => {
        tableData.value = res.data;
    });
}
onMounted(() => {
    Promise.all([getPeriodApi()]).then((res: any) => {
        if (res[0].state == 1000) {
            periodList.value = res[0].data.reduce((prev: IPeriod[], item: any) => {
                prev.push({
                    pid: item.pid,
                    periodInfo: item.periodInfo,
                });
                return prev;
            }, []);
        }
        getTableData();
    });
});
let random = "";
onBeforeRouteLeave(() => {
    random = route.query.r as string;
});

onActivated(() => {
    if (route.query.r !== random) {
        random = route.query.r as string;
        info.value.startPid = route.query.startPid as string
        info.value.endPid = route.query.endPid as string
        info.value.projectType = route.query.projectType as string
        getTableData();
    }
});

onUnmounted(() => {
    random = "";
});
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/FixedAssets/FixedMulti-tabs.less";

:deep(.el-popper) {
    max-width: 300px;
    text-align: left;
}
</style>
