<template>
    <div class="add-content">
        <el-form :model="formList" label-width="222px" class="formRef" ref="formRef">
            <div class="isRow">
                <el-form-item label="部门编码：">
                    <div class="row">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Code, $event, '部门编码', 'aaNum', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                            v-model="formList.aaNum"
                            class="default"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="部门名称：">
                    <div class="row">
                        <el-input
                            v-model="formList.aaName"
                            class="default input-ellipsis"
                            @blur="inputTypeBlur('aaNameInputRef')"
                            v-if="aaNameTextareaShow"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            @input="limitInputLength(formList.aaName,'部门名称')"
                            @focus="inputTypeFocus()"
                            resize="none"
                            ref="aaNameInputRef"
                        />
                        <Tooltip :content="formList.aaName" :isInput="true" placement="right" v-else>
                            <input
                                @focus="inputTypeFocus(1)"
                                ref="aaNameInputRef"
                                type="text"
                                v-model="formList.aaName"
                                class="default input-ellipsis"
                            />
                        </Tooltip>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="负责人：">
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.manager" :is-input="true" placement="right">
                            <input
                                type="text"
                                @input="handleAAInput(LimitCharacterSize.Default, $event, '负责人', 'manager', changeFormListData)"
                                @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                                v-model="formList.aaEntity.manager"
                                class="default input-ellipsis"
                            />
                        </Tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="手机：">
                    <div class="row">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机', 'mobilePhone', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Phone, $event)"
                            v-model="formList.aaEntity.mobilePhone"
                            class="default"
                        />
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="成立日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.aaEntity.startDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateStart"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="撤销日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.aaEntity.endDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateEnd"
                        />
                    </div>
                </el-form-item>
            </div>
            <el-form-item label="备注：">
                <div class="max">
                    <el-input
                        v-model="formList.aaEntity.note"
                        class="big input-ellipsis"
                        @blur="inputTypeBlur('noteInputRef')"
                        v-if="noteTextareaShow"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        type="textarea"
                        maxlength="1024"
                        @input="limitInputLength(formList.aaEntity.note,'备注')"
                        @focus="inputTypeFocus()"
                        resize="none"
                        ref="aaNameInputRef"
                    />
                    <div v-else class='note-tooltip-width'>
                        <Tooltip :content="formList.aaEntity.note" :isInput="true" placement="bottom">
                            <input
                                @focus="inputTypeFocus(2)"
                                ref="aaNameInputRef"
                                type="text"
                                v-model="formList.aaEntity.note"
                                class="big input-ellipsis"
                            />
                        </Tooltip>
                    </div> 
                </div>
            </el-form-item>
            <el-form-item label="是否启用：" class="status">
                <el-checkbox label="启用" v-model="formList.aaEntity.status" @change="handleStatusChange" />
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">返回</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, watch, nextTick } from "vue";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste, getNextAaNum, textareaBottom } from "../utils";
import Tooltip from "@/components/Tooltip/index.vue";
import { dayjs } from "element-plus";
import { ValidataDepartment } from "../validator";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
const formList = reactive<any>({
    aaNum: "1",
    aaName: "",
    aaeID: "",
    USCC: "",
    hasEntity: true,
    aaEntity: {
        manager: "",
        mobilePhone: "",
        startDate: "",
        endDate: "",
        note: "",
        status: true,
    },
});

const changeFormListData = (key: string, val: string) => {
    const keys = Object.keys(formList);
    const aaEntityKeys = Object.keys(formList.aaEntity);
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            formList[key] = val;
            return;
        }
    }
    for (let i = 0; i < aaEntityKeys.length; i++) {
        if (aaEntityKeys[i] === key) {
            formList.aaEntity[key] = val;
            break;
        }
    }
};

const formRef = ref<any>(null);
const emit = defineEmits(["formCancel", "formChanged", "formCancelEdit"]);
let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => (EditType = val);
let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    const aaeID = EditType === "Edit" ? formList.aaeID : 0;
    const aaNum = formList.aaNum;
    const aaName = formList.aaName;
    createCheck(10004, aaeID, aaNum, aaName, Save);
};
const aaNameTextareaShow=ref(false)
const noteTextareaShow=ref(false)
const aaNameInputRef=ref()
const inputTypeBlur = (value:string) => {
    switch (value) {
        case 'aaNameInputRef':
            aaNameTextareaShow.value = false;
            break;
        case 'noteInputRef':
            noteTextareaShow.value = false;
            break;
    }
};
const inputTypeFocus = (num?:number) => {
    textareaBottom(formRef)
    switch (num) {
        case 1:
            aaNameTextareaShow.value = true;
            break;
        case 2:
            noteTextareaShow.value = true;
            break;
    }
    nextTick(()=>{
        if(num){
            getTextareaFocus(num)
        }
    })
};
const getTextareaFocus = (num:number) => {
    switch (num) {
        case 1:
        case 2:
            aaNameInputRef.value.focus();
            break;
    }
};
function limitInputLength(val: string,label: string) {
    switch (label) {
        case '备注':
            if (val.length === 1024) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过1024个字哦~` });
            }
            break;
        case '部门名称':
            if (val.length === 256) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
            }
            break;
    }
}
const Save = () => {
    const entityParams = {
        manager: formList.aaEntity.manager,
        mobilePhone: formList.aaEntity.mobilePhone,
        startDate: formList.aaEntity.startDate,
        endDate: formList.aaEntity.endDate,
        note: formList.aaEntity.note,
    };
    const params = {
        entity: entityParams,
        aaNum: formList.aaNum,
        aaName: formList.aaName,
        uscc: formList.USCC,
        status: formList.aaEntity.status ? 0 : 1,
        hasEntity: formList.hasEntity,
        ifvoucher: true,
    };
    const urlPath = EditType === "Edit" ? "Department?aaeid=" + formList.aaeID : "Department";
    if (ValidataDepartment(entityParams, params.aaNum, params.aaName)) {
        isSaving = true;
        request({
            url: "/api/AssistingAccounting/" + urlPath,
            method: EditType === "New" ? "post" : "put",
            headers: { "Content-Type": "application/json" },
            data: JSON.stringify(params),
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000 || "Failed" === res.data) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    isSaving = false;
                    return;
                }
                ElNotify({ type: "success", message: "保存成功" });
                useAssistingAccountingStore().getAssistingAccounting();
                useAssistingAccountingStore().getDepartment();
                if (EditType === "New") {
                    getNextAaNum(10004)
                        .then((res: IResponseModel<string>) => {
                            resetForm();
                            formList.aaNum = res.data;
                            emit("formCancelEdit");
                        })
                        .finally(() => {
                            isSaving = false;
                        });
                } else {
                    handleCancel();
                    isSaving = false;
                }
                window.dispatchEvent(new CustomEvent("updateAssistingAccounting"));
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
                isSaving = false;
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};
watch(
    formList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
const resetForm = () => {
    const initParams = {
        aaNum: "1",
        aaName: "",
        aaeID: "",
        USCC: "",
        hasEntity: true,
        aaEntity: {
            manager: "",
            mobilePhone: "",
            startDate: "",
            endDate: "",
            note: "",
            status: true,
        },
    };
    editForm(initParams);
};
const editForm = (data: any) => {
    Object.keys(data).forEach((key) => {
        if (Object.keys(formList).includes(key)) {
            formList[key] = data[key];
        } else if (Object.keys(formList.aaEntity).includes(key)) {
            formList.aaEntity[key] = data[key];
        }
    });
};
defineExpose({ resetForm, editForm, changeType });

function disabledDateStart(time: Date) {
    let endDate = dayjs(formList.aaEntity.endDate).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(formList.aaEntity.startDate).valueOf();
    return time.getTime() < startDate;
}
const handleStatusChange = (check: any) => {
    if (!check) {
        ElConfirm("亲，辅助核算项目停用后不能再在凭证中使用哦，是否确认停用？").then((r: boolean) => {
            formList.aaEntity.status = !r;
        });
    }
};
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
:deep(.el-textarea__inner){
    z-index: 1000;
}
.add-content {
    :deep(.el-form) {
        .isRow {
            &:first-child {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
        input {
            &.default {
                .detail-original-input(188px, 32px);
            }
            &.middle {
                .detail-original-input(288px, 32px);
            }
            &.big {
                .detail-original-input(100%, 32px);
            }
        }
    }
}
</style>
