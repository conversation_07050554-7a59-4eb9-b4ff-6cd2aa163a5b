<template>
    <div class="search-input-box">
        <input v-model="searchVal" placeholder="输入编码或名称" @keyup.enter="btsearch" />
        <div class="search-btn" @click="btsearch"></div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
const searchVal = ref("");
const issearch = ref(0);
const emit = defineEmits(["searchData"]);
const btsearch = () => {
    issearch.value = 1;
    emit("searchData", { fanum: searchVal.value, IsSearch: issearch.value });
};
</script>

<style lang="less" scoped>
.search-input-box {
    input {
        width: 160px;
        height: 30px;
        border: 1px solid var(--input-border-color);
        outline: none;
        padding: 0;
        padding-left: 10px;
        padding-right: 10px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 30px;
        box-sizing: border-box;
        border-radius: var(--input-border-radius);

        &:focus {
            border-color: var(--input-border-focus-color);
        }
    }
    .search-btn {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background-size: 15px 16px;
        width: 25px;
        cursor: pointer;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url("@/assets/Icons/sousuo.png");
    }
}
</style>
