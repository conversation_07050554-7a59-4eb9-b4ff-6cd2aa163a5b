// 增值税月报、季报公共方法
import { ReportTypeEnum } from "./vat-checkRules/enums"
import { VatCheckRulesItem } from "./vat-checkRules/interface"
import engineJs from "./vat-checkRules/verifyCenter/e294"
import functionsGeneral from "./vat-checkRules/verifyCenter/function_20220101_20991231.json" // 增值税月报函数
import allRulesGeneral from "./vat-checkRules/verifyCenter/v1_20220101_20991231.json" // 增值税月报规则+公式
import functionsSmallScale from "./vat-checkRules/verifyCenter/function_20210601_29991231.json" // 增值税季报函数
import allRulesSmallScale from "./vat-checkRules/verifyCenter/v1_20210601_29991231.json" // 增值税季报规则+公式

/**
 * 增值税表单规则校验
 * @param {ReportTypeEnum} reportType 业务类型，只支持增值税
 * @param rootInfo 查询信息
 * @returns {VatCheckRulesItem[]} 校验不通过信息
 */
export function vatCheckRules(reportType: ReportTypeEnum, rootInfo: RootInfo): VatCheckRulesItem[] {
  if (reportType === ReportTypeEnum.GeneralVatMonthly) {
    // 一般纳税人
    return checkRules(rootInfo, allRulesGeneral, functionsGeneral)
  } else if (reportType === ReportTypeEnum.SmallScaleVatQuarter) {
    // 小规模纳税人
    return checkRules(rootInfo, allRulesSmallScale, functionsSmallScale)
  }
  return []
}

/**
 * 获取增值税校验中心文件版本名
 * @param {ReportTypeEnum} reportType 业务类型，只支持增值税
 * @returns {{ [key: string]: string;}} 文件版本名
 */
export function getVerifyCenterVersionMap(reportType: ReportTypeEnum): {
  [key: string]: string
} {
  if (reportType === ReportTypeEnum.GeneralVatMonthly) {
    // 一般纳税人
    return {
      ruleVersion: "v1_20220101_20991231.json",
      functionVersion: "function_20220101_20991231.json",
      formVersion: "form_20220101_20991231.json",
    }
  } else if (reportType === ReportTypeEnum.SmallScaleVatQuarter) {
    // 小规模纳税人
    return {
      ruleVersion: "v1_20210601_29991231.json",
      functionVersion: "function_20210601_29991231.json",
      formVersion: "form_20210601_29991231.json",
    }
  }
  return {}
}

// 增值税-税务局表单引擎校验方法
function checkRules(rootInfo: RootInfo, allRules: { rules: object[]; formulas: object[] }, functions: object[]) {
  // 自定义错误日志，部分错误为正常情况发生，不影响结果
  // engineJs.customErrorLog = function ({ error, data }) {
  //   console.log('customErrorLog: ', error);
  //   console.log('customErrorDataLog: ', data);
  // };
  console.log(rootInfo, "rootInfo")
  ;(window as any).engineJs = engineJs // 税务局是挂载在window上，在配置工具方法中全局访问

  const ruleContext = {
    ywbw: rootInfo.ywbw || rootInfo.ytxx,
    ytxx: rootInfo.ytxx,
    fzxx: rootInfo.fzxx,
    sbsx: rootInfo.sbsx,
    nsrxx: rootInfo.nsrxx,
  }
  // allRules.formulas为自动计算逻辑，部分也作为校验逻辑
  const rules = allRules.rules.concat(allRules.formulas)
  const validAllMessage = (engineJs as any).validateAllForMessageAndDeps(ruleContext, rules, functions)
  validAllMessage.forEach((item: any) => {
    // 移除点此采集类似的a标签
    item.content = removeAnchorTags(item.content)
  })

  console.log("validAllMessage: ", validAllMessage)

  return validAllMessage
}

function removeAnchorTags(html: string) {
  return html.replace(/，?<a[^>]*>.*?<\/a>/g, "")
}

export interface RootInfo {
  ywbw: object
  nsrxx: object
  sbsx: object
  ytxx: object
  fzxx: object
}

// TODO: 大致是调用engineJs.calc方法，具体参数待定
// 增值税-税务局表单引擎计算公式方法
function calcRules(rootInfo: RootInfo, allRules: { rules: object[]; formulas: object[] }, functions: object[]) {
  // 自定义错误日志，部分错误为正常情况发生，不影响结果
  // engineJs.customErrorLog = function ({ error, data }) {
  //   console.log('customErrorLog: ', error);
  //   console.log('customErrorDataLog: ', data);
  // };

  ;(global as any).engineJs = engineJs // 税务局是挂载在window上，在配置工具方法中全局访问

  const ruleContext = {
    ywbw: rootInfo.ytxx, // 此处应该是当前正在填写的表单数据，
    ytxx: rootInfo.ytxx, // 初始表单数据，用于一些计算逻辑
    fzxx: rootInfo.fzxx,
    sbsx: rootInfo.sbsx,
    nsrxx: rootInfo.nsrxx,
  }
  // allRules.formulas为自动计算逻辑，部分也作为校验逻辑
  const rules = allRules.rules.concat(allRules.formulas)
  const result = (engineJs as any).calc(
    ruleContext,
    rules,
    functions,
    [], // 未知
  )
  console.log("calc result: ", result)
}
