@import "../SelfAdaption.less";

.content {
    :deep(.el-tabs) {
        display: flex;
        flex-direction: column;
        height: 100%;
        .el-tabs__content {
            flex: 1;
            display: flex;
            flex-direction: column;
            .el-tab-pane {
                height: 100%;
                display: flex;
                flex-direction: column;
                min-height: unset !important;
            }
        }
    }
    .main-content.part-voucher {
        :deep(.el-tabs) {
            display: block;
            .el-tabs__content {
                overflow: initial;
            }
        }
    }
}
.main-center {
    :deep(.el-table__empty-text) {
        line-height: 390px;
    }
}

.print-item {
    display: flex;
    align-items: center;
    padding-top: 8px;
    .print-item-label {
        line-height: 32px;
        width: 100px;
        padding-top: 0;
        align-self: flex-start; 
        text-align: right;
    }
}

:deep(.el-input__inner) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.main-tool-right {
    .importlarge {
        width: 113px;
    }
    .declare-img {
        margin-right: -34px;
        margin-left: 18px;
        animation: rotating 2s linear infinite;
    }
    .declaring {
        width: 76px;
        text-align: right;
        padding-right: 18px;
    }
}

.downlist + ul {
    & li {
        white-space: nowrap;
    }
}
:deep(.downlist-buttons) {
    div {
        padding: 0 !important;
        text-align: center !important;
    }
}
.none {
    display: none;
}

.format-edit-item(@width) {
    // .detail-el-input(@width, 22px);
    .el-input__inner {
        border: none;
    }
    .el-input__wrapper {
        padding: 0 4px;
    }
}
.edit-item {
    .format-edit-item(100%);
    :deep(input[type="text"]) {
        border: none !important;
    }
}

#warntext {
    .warn-icon {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url("@/assets/Icons/warn.png") no-repeat;
        vertical-align: top;
        margin-top: 2px;
    }
}
.declare-info-card {
    height: 55px;
    background: #f8f8f8;
    border-radius: 4px;
    padding-top: 12px;
    padding-left: 24px;
    .info-title {
        font-size: var(--el-dialog-content-font-size);
        line-height: 20px;
        font-weight: 500;
        color: var(--el-text-color-regular);
    }
    .info-subtitle {
        font-size: 12px;
        color: #777777;
    }
}
.radioFlex {
    display: flex;
    align-items: center;
}
.small {
    font-size: 12px;
    color: var(--red);
    margin-bottom: 30px;
}

.back-btn {
    display: flex;
    align-items: center;
    padding: 20px 30px 0;
    .btn-icon {
        width: 12px;
        height: 12px;
        margin-right: 4px;
    }
    .btn {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        cursor: pointer;
    }
}
.waiting-content {
    padding: 60px 40px;
    display: flex;
    justify-content: center;

    .waiting-left {
        display: flex;
        flex-direction: column;

        .left-title {
            display: flex;
            align-items: center;
            margin-top: 35px;
            .left-title-loading-img {
                animation: rotating 2s linear infinite;
                margin-right: 10px;
                width: 24px;
                height: 24px;
            }
            .waitingText {
                font-size: var(--h2);
                font-weight: 600;
                color: var(--font-color);
                line-height: 25px;
                margin-left: 8px;
            }
        }

        .left-subtitle {
            margin-top: 40px;
            font-size: var(--font-size);
            color: var(--font-color);
            line-height: 20px;
        }
    }
    .waiting-line {
        margin: 0 50px;
        border: 1px dashed var(--border-color);
    }
    .waiting-right {
        margin-right: 50px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qrcode-img {
            width: 170px;
            height: 170px;
        }

        .qrcode-desc {
            font-size: 16px;
            color: var(--font-color);
            line-height: 20px;
            width: 100%;
            box-sizing: border-box;
            padding-left: 10px;
            text-align: center;
            letter-spacing: 8px;
        }
    }
}
.checkoutDia {
    padding: 40px 70px;
    text-align: center;
    min-height: 42px;
    line-height: 42px;
}

.txt {
    font-size: 14px;
}
.templateNameIpt {
    width: 160px;
    height: 32px;
    box-sizing: border-box;
    margin-right: 10px;
}

//像是初期处理溢出的样式代码--感觉已经被覆盖了-无用
// :deep(.el-select-dropdown__item) {
//     width: 100%;
//     height: auto;
//     font-size: var(--el-font-size-base);
//     padding: 6px 6px 6px 8px;
//     line-height: 16px;
//     position: relative;
//     word-wrap: break-word;
//     white-space: normal;
//     color: var(--el-text-color-regular);
//     box-sizing: border-box;
//     cursor: pointer;
//     text-align: left;
//     & span {
//         display: -webkit-box;
//         -webkit-line-clamp: 2; /* 设置最多显示2行 */
//         -webkit-box-orient: vertical;
//         overflow: hidden;
//     }
// }
:deep(.el-table__cell) {
    .cell.el-tooltip {
        min-width: 0px !important;
    }
}

:deep(.custom-table tbody tr td .cell) {
    white-space: nowrap;
    & span {
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
.divImport {
    & .help-icon {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url(@/assets/Icons/help.png) no-repeat;
        vertical-align: top;
        margin-top: 2px;
    }
    & .template-import-download {
        margin: 30px 40px 0;
    }
    & .template-import-import {
        margin: 20px 40px 30px;
    }
    & .buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.item-boxs-context {
    // margin-left: -26px;
    text-align: left;
    margin-left: -26px;
    & :deep(.item-box) {
        width: 218px;
        height: 153px;
        text-align: center;
        border: 1px solid #dadada;
        display: inline-block;
        margin-left: 26px;
        margin-bottom: 20px;
        & .item-box-title {
            color: #333333;
            font-size: 16px;
            line-height: 36px;
            border-bottom: 1px solid #dadada;
            text-align: center;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        & .money {
            height: 25px;
            margin-top: 20px;
            margin-bottom: 20px;
            text-align: center;
            & .currecy.has-currency {
                color: var(--main-color);
                text-decoration: underline;
                cursor: pointer;
            }
        }
        & .check-btn {
            height: 30px;
            width: 96px;
            text-align: center;
            color: var(--white);
            font-size: var(--font-size);
            line-height: 30px;
            border-radius: 2px;
            margin: 0 auto 20px;
            cursor: pointer;
            &.create-voucher-btn {
                background-color: var(--main-color);
            }
            &.check-voucher-btn {
                background-color: #389bff;
            }
            &.disabled {
                background-color: #bbb !important;
                pointer-events: none;
            }
        }
    }
}
.erp-container {
    .item-boxs-context {
        :deep(.item-box) {
            & .check-btn {
                &.check-voucher-btn {
                    background-color: #58c979;
                }
            }
            & .custom-check-voucher-btn {
                background-color: #58c979 !important;
            }
        }
    }
}

.import-method-container {
    & .import-method-content {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 50px 0 66px;
        & .mr-20 {
            margin-right: 20px;
        }
    }
    & .dialog-buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

:deep(.el-tabs__nav) {
    padding-left: 10px;
}

:deep(.el-tabs__content) {
    position: static;
    .el-tab-pane {
        position: relative;
    }
}

.emperror-container {
    margin: 20px 20px 0px 20px;
    text-align: left;
    & .emperror-message {
        font-weight: 500;
        line-height: 20px;
        color: var(--font-color);
        font-size: var(--font-size);
    }
    & .emperror-name {
        width: 324px;
        min-height: 40px;
        border: 1px solid #edefee;
        margin-top: 8px;
        padding-left: 16px;
        padding-top: 6px;
        overflow: auto;
        & .name-content {
            margin-top: 10px;
            font-size: 0;
            & img {
                width: 12px;
                height: 13px;
            }
            & span {
                font-size: var(--font-size);
                color: var(--font-color);
                line-height: 20px;
                margin-left: 10px;
            }
        }
    }
    & .dialog-buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.taxinfo-check {
    text-align: left;
    border-bottom: 1px solid var(--border-color);

    .taxinfo-check-container {
        width: 420px;
        height: 350px;
        border: 1px solid #edefee;
        margin: 20px;

        .check-item {
            position: relative;
            height: 38px;
            line-height: 38px;
            font-size: var(--font-size);
            color: var(--font-color);
            padding-left: 50px;
            border-bottom: 1px solid #edefee;
            &:last-child {
                border-bottom: 0;
            }
            .check-waite {
                font-size: var(--font-size);
                color: #999999;
                float: right;
                margin-right: 20px;
                line-height: 38px;
            }

            .img-item {
                display: none;
                position: absolute;
                top: 7px;
                left: 20px;
                width: 24px;
                height: 24px;
                // float: left;
                // margin-top: 7px;
            }

            .icon-item {
                position: absolute;
                top: 15px;
                left: 27px;
                width: 10px;
                height: 10px;
                // margin-right: 16px;
                // margin-left: -17px;
            }

            .item-waite {
                display: none;
                width: 90px;
                height: 38px;
                float: right;
                margin-right: -80px;
            }
            &.check-item-active {
                .img-item {
                    display: block;
                    animation: rotating 0.5s linear infinite;
                }
                .item-waite {
                    display: block;
                    animation: finger infinite 2s;
                }
            }
        }
    }

    .dialog-buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

@keyframes finger {
    0% {
        transform: translate(-300px);
    }
    100% {
        transform: translate(0px);
    }
}

#partMainContentSlider {
    &.part-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        .main-center {
            overflow-y: auto;
        }
    }
}

.part-voucher-container {
    .buttons {
        flex-shrink: 0;
    }
}
.cell-header {
    position: relative;
    z-index: 10;
    padding-left: 10px;
    box-sizing: border-box;
    span {
        display: block;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
:deep(.el-table__header-wrapper) {
    &:hover {
        .icon-equal {
            display: block;
        }
    }
}
.icon-equal {
    position: absolute;
    left: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 16px;
    background: url("@/assets/Statements/equal.png") no-repeat 0 center;
    background-size: 12px 12px;
    display: none;
}
.date-picker {
    :deep(.el-date-editor) {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
        & .el-input__suffix-inner {
            position: absolute;
            right: 30px;
            top: 9px;
        }
    }
    :deep(.el-month-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
    :deep(.el-year-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
}
body[erp] {
    .icon-equal {
        background: url("@/assets/Statements/equal-erp.png") 0 / contain no-repeat;
        width: 12px;
        height: 12px;
    }
    .content {
        .main-content.part-voucher {
            height: auto;
        }
        :deep(.el-tabs .el-tabs__nav-scroll) {
            padding-top: 14px;
        }
        :deep(.el-tabs) {
            display: flex;
            flex-direction: column;
            height: 100%;
            overflow: hidden;
            .el-tabs__content {
                flex: 1;
                display: flex;
                flex-direction: column;
                .el-tab-pane {
                    min-height: 0 !important;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    overflow-y: auto;
                }
            }
        }
    }
    .erp-container {
        .custom-table {
            overflow: hidden;
        }
        :deep(.el-table) {
            &.el-table--border {
                &:before,
                &:after {
                    background-color: var(--table-border-color);
                }
            }
            .el-table__inner-wrapper {
                &:after {
                    background-color: var(--table-border-color);
                }
            }
            .el-table__border-left-patch {
                width: 0;
            }
            .el-table__header {
                th.el-table__cell {
                    &.is-leaf {
                        border-bottom-color: var(--table-border-color);
                    }
                }
            }
        }
        :deep(.money) {
            .currecy {
                text-decoration: none !important;
                font-size: 18px;
            }
        }
    }
    .slot-content.part-voucher-container {
        overflow-x: hidden !important;
        height: auto;
        .voucher-content {
            margin-bottom: 110px;
        }
    }
}
