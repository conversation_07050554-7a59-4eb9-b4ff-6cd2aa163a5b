export interface IAsubCodeLength {
    asid: number;
    codeLength: Array<number>;
    firstAsubLength: number;
    firstCodeLength: number;
    forthAsubLength: number;
    forthCodeLength: number;
    preName: string;
    secondAsubLength: number;
    secondCodeLength: number;
    thirdAsubLength: number;
    thirdCodeLength: number;
}

export interface ITableData {
    asubId: number;
    parentId: number;
    code: string;
    name: string;
    asubLevel: number;
    isRoot: boolean;
    hasChild: boolean;
    children?: ITableData[];
    expensePeriods: number[];
}

export interface IExpensesStatement {
    titles: { title: string }[];
    subjectExpenses: ITableData[];
    total: ITableData;
}

export interface ISearchInfo {
    PeriodStart: number;
    PeriodEnd: number;
    SubjectLevelStart: number;
    SubjectLevelEnd: number;
    SubjectIds: string[];
    /** 显示辅助核算 */
    IsAssist: boolean;
}
