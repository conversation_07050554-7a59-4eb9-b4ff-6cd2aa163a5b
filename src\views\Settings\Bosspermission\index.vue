<template>
    <div class="content" :class="isErp ? 'erp-content' : ''">
        <div class="title">老板看账</div>
        <div class="main-content">
            <Quickauth
                style="width: 1000px; margin: 0 auto"
                :asname="asname"
                @quick-auth="quickAuth"
                v-show="showDefault === 'quickauth'"
            />
            <Projress style="width: 1000px; margin: 0 auto" ref="projressRef" v-show="showDefault.includes('step')" />
            <div class="divider-line" v-if="showDefault !== 'quickauth' && showDefault !== 'contentBoss'"></div>
            <Step0
                ref="step0Ref"
                :asname="asname"
                :tableData="tableData"
                @back-index="backIndex"
                @is-old-boss-inner="isOldBossInner"
                @is-new-boss-inner="isNewBossInner"
                v-show="showDefault === 'step0'"
            />
            <Step1 v-show="showDefault === 'step1'" @reduce-stage="reduceStage" @upgrade-stage="upgradeStage" />
            <Step2 v-show="showDefault === 'step2'" @reduce-stage="reduceStage" @upgrade-stage="upgradeStage" />
            <Step3 v-show="showDefault === 'step3'" @reduce-stage="reduceStage" @complete-stage="completeStage" />
            <ContentBoss
                style="width: 1000px; margin: 0 auto"
                :tableData="tableData"
                :asname="asname"
                @add-boss="addBoss"
                @load-data="getTableList"
                v-show="showDefault === 'contentBoss'"
            />
        </div>
    </div>
    <el-dialog class="custom-confirm dialogDrag" title="提示" width="440" center v-model="completeStageTipShow">
        <div class="complete-stage-tip-content" v-dialogDrag>
            <div class="tip">老板还未绑定账号，请绑定后再进行下一步操作！</div>
            <div class="buttons">
                <a class="button solid-button" @click="() => (completeStageTipShow = false)">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "Bosspermission",
};
</script>
<script setup lang="ts">
import { ref, nextTick, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";

import Quickauth from "./components/Quickauth.vue";
import Projress from "./components/Projress.vue";
import Step0 from "./components/Step0.vue";
import Step1 from "./components/Step1.vue";
import Step2 from "./components/Step2.vue";
import Step3 from "./components/Step3.vue";
import ContentBoss from "./components/ContentBoss.vue";

const accountset = useAccountSetStore().accountSet;

const isErp = window.isErp;

const showDefault = ref("");
const step0Ref = ref<InstanceType<typeof Step0>>();
const projressRef = ref<InstanceType<typeof Projress>>();
const tableData = ref<any[]>([]);
const total = ref(0);
const asname = ref("");
const backContent = ref<"quickauth" | "contentBoss">("quickauth");
const completeStageTipShow = ref(false);
let phone = "";
let canComplete = true;

const quickAuth = () => {
    showDefault.value = "step0";
    nextTick().then(() => projressRef.value?.setSelected(0));
};
const isOldBossInner = () => getTableList();
const isNewBossInner = (phoneNum: string) => {
    phone = phoneNum;
    upgradeStage(0);
};
const upgradeStage = (val: number) => {
    request({ url: "/api/BossPermissions/UpgradeStage?phone=" + phone + "&paraStage=" + (val + 1), method: "post" }).then((res: any) => {
        if (res.state != 1000) {
            ElNotify({ type: "warning", message: "请求失败！" });
            return;
        }
        if (res.data == "Success") {
            showDefault.value = "step" + (val + 1);
            nextTick().then(() => projressRef.value?.setSelected((val + 1) * 2));
        } else if (res.data == "NotAuthorized") {
            ElNotify({ type: "warning", message: "此手机号已被其他用户授权！" });
        } else {
            ElNotify({ type: "warning", message: "请求失败！" });
        }
    });
};
const backIndex = () => {
    phone = backContent.value === "quickauth" ? "--" : "";
    reduceStage(-1);
};
const reduceStage = (val: number) => {
    request({ url: "/api/BossPermissions/ReduceStage?phone=" + phone + "&paraStage=" + val, method: "post" }).then((res: any) => {
        if (res.state != 1000) {
            ElNotify({ type: "warning", message: "请求失败！" });
            return;
        }
        if (res.data == "Success") {
            nextTick().then(() => projressRef.value?.setSelected(val * 2));
            if (val >= 0) {
                showDefault.value = "step" + val;
                if (val === 0) {
                    nextTick().then(() => step0Ref.value?.editValue(phone));
                }
            } else {
                showDefault.value = total.value > 0 ? "contentBoss" : "quickauth";
            }
        } else if (res.data == "NotAuthorized") {
            ElNotify({ type: "warning", message: "此手机号已被其他用户授权！" });
        } else {
            ElNotify({ type: "warning", message: "请求失败！" });
        }
    });
};
const completeStage = () => {
    if (!canComplete) {
        ElNotify({ type: "warning", message: "提交中，请稍后再试！" });
        return;
    }
    canComplete = false;
    request({ url: "/api/BossPermissions/CheckBossLogin?phone=" + phone, method: "post" })
        .then((res: IResponseModel<string>) => {
            if (res.state != 1000) {
                ElNotify({ type: "warning", message: "请求失败！" });
                return;
            }
            if (res.data === "Success") {
                request({ url: "/api/BossPermissions/NewBoss?phone=" + phone, method: "post" }).then((result: any) => {
                    if (result.state != 1000) {
                        ElNotify({ type: "warning", message: "请求失败！" });
                        return;
                    }
                    result.data == "Success" ? getTableList() : ElNotify({ type: "warning", message: "请求失败！" });
                });
            } else {
                completeStageTipShow.value = true;
            }
        })
        .finally(() => {
            canComplete = true;
        });
};
const addBoss = () => {
    request({ url: "/api/BossPermissions/InitBossPhone?phone=", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            showDefault.value = "step0";
            nextTick().then(() => projressRef.value?.setSelected(0));
        }
    });
};
const getTableListApi = () => {
    return request({ url: "/api/BossPermissions/List" });
};
const getTableList = () => {
    getTableListApi().then((res: any) => {
        if (res.state == 1000) {
            tableData.value = res.data;
            total.value = res.data.length || 0;
        }
    });
};

const handleInitBossInfo = () => {
    request({ url: "/api/BossPermissions/CurrentInfo" }).then((res: any) => {
        if (res.state != 1000) return;
        if (res.data.stage == "4") {
            if (res.data.hasBoss === "0") {
                showDefault.value = "quickauth";
                return;
            } else {
                return;
            }
        }
        phone = res.data.lastPhone;
        const val = res.data.stage;
        nextTick().then(() => projressRef.value?.setSelected(val * 2));
        if (val >= 0) {
            showDefault.value = "step" + val;
            if (val === 0) {
                nextTick().then(() => step0Ref.value?.editValue(phone));
            }
        } else {
            showDefault.value = total.value > 0 ? "contentBoss" : "quickauth";
        }
    });
};
const handleInit = () => {
    asname.value = accountset?.asName ?? "";
    Promise.all([getTableListApi()]).then((res: any) => {
        if (res[0].state === 1000) {
            tableData.value = res[0].data;
            total.value = res[0].data.length || 0;
            handleInitBossInfo();
        }
    });
};

handleInit();

watch(total, (val) => {
    showDefault.value = val === 0 ? "quickauth" : "contentBoss";
    backContent.value = val === 0 ? "quickauth" : "contentBoss";
});
</script>

<style lang="less" scoped>
.content {
    width: 100%;
    .main-content {
        width: 100%;
        .divider-line {
            width: 100%;
            height: 1px;
            background-color: #cdcdcd;
        }
        .step-box {
            width: 1000px;
            margin: 0 auto;
            border-top: 0;
        }
    }
}
.complete-stage-tip-content {
    .tip {
        padding: 50px 60px;
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
