<template>
    <div class="slot-content">
        <div class="title">{{ title }}</div>
        <div class="slot-mini-content">
            <content-slider :slots="slots" :currentSlot="currentSlot">
                <template #generate>
                    <div class="slot-mini-content generate-container">
                        <div class="main-top main-tool-bar">
                            <slot name="toolbar"></slot>
                        </div>
                        <div
                            class="main-view"
                            ref="scrollContainerRef"
                            @scroll="onScroll"
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                        >
                            <div class="main-center" :style="{ height: scrollBarHeight + 'px' }">
                                <div class="document-content">
                                    <div class="document-title">{{ documentTitle }}</div>
                                    <div class="document-list">
                                        <div
                                            class="document-item"
                                            v-for="(item, index) in visibleDocumentList"
                                            :key="index"
                                            :style="{
                                                marginBottom: getDocumentMarginBottom(item) + 'px',
                                                transform: `translate3d(0,${scrollTopD}px,0)`,
                                            }"
                                        >
                                            <div class="document-item-title">
                                                <ToolTip :content="item.title" :is-input="false" effect="dark" :lineClamp="1" :placement="index === 0 ? 'bottom' : 'top'">
                                                    {{ item.title }}
                                                </ToolTip>
                                                <el-tooltip
                                                    class="box-item"
                                                    effect="dark"
                                                    :raw-content="true"
                                                    :content="item.document.warnInfo"
                                                    placement="bottom"
                                                    popper-class="max-width-300"
                                                >
                                                    <div v-show="item.document.warnInfo && !item.document.errorInfo" class="warn-icon">
                                                        提示
                                                    </div>
                                                </el-tooltip>
                                                <el-tooltip
                                                    class="box-item"
                                                    effect="dark"
                                                    :raw-content="true"
                                                    :content="item.document.errorInfo"
                                                    placement="bottom"
                                                    popper-class="max-width-300"
                                                >
                                                    <div v-show="item.document.errorInfo" class="error-icon">错误提示</div>
                                                </el-tooltip>
                                            </div>
                                            <div class="document-item-content">
                                                <div v-for="(content, index) in item.contentList" :key="index">
                                                    <ToolTip :teleported="true" :fontSize="12" :lineClamp="1" :content="content.replace('摘要：', '')" placement="right" effect="light">
                                                        <div style="margin: 0">{{ content }}</div>
                                                    </ToolTip>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="voucher-content">
                                    <div class="voucher-title">凭证预览</div>
                                    <div class="voucher-list">
                                        <div
                                            class="voucher-item"
                                            v-for="(voucher, index) in visibleVoucherList"
                                            :key="index"
                                            :style="{
                                                marginBottom: getVoucherMarginBottom(voucher) + 'px',
                                                transform: `translate3d(0,${scrollTopV}px,0)`,
                                            }"
                                        >
                                        <div class="voucher-item-title">
                                                <div class="voucher-info">
                                                    <span>日期：{{ voucher.vdate }}</span><span class="ml-20">凭证字号：{{
                                                    voucher.vgName }}-{{ voucher.vnum }}</span><span class="ml-20">附件：{{
        voucher.attachments }}</span>
                                            </div>
                                                <div class="voucher-operators">
                                                    <a class="link" @click="editVoucher(voucher)">修改</a>
                                                    <!-- v-show="!queryParams.isMerge" -->
                                                    <a class="link ml-10"
                                                    @click="deleteVoucher(voucher)">删除</a>
                                                </div>
                                            </div>
                                            <div class="voucher-item-content">
                                                <div class="voucherline" v-for="(voucherline, index) in voucher.voucherLines"
                                                :key="index">
                                                    <div style="width: 350px;">
                                                        <ToolTip :teleported="true" :font-size="12" :line-clamp="1" :content=getDescription(voucherline) placement="right" effect="light" :maxWidth='340'>
                                                            <div class="voucherline-description">
                                                                {{ getDescription(voucherline)}}
                                                            </div>
                                                        </ToolTip>
                                                    </div>
                                                <div style="width: 350px;">
                                                    <ToolTip :content=voucherline.asubName :teleported="true" placement="right" effect="light" :maxWidth='400'>
                                                    <div class="voucherline-asubname">{{ voucherline.asubName }}</div>
                                                        </ToolTip>
                                                    </div>
                                                    <div class="voucherline-debit">{{ formatMoney(voucherline.debit) }}</div>
                                                    <div class="voucherline-credit">{{ formatMoney(voucherline.credit)
                                                }}</div>
                                            </div>
                                            <div class="voucherline">
                                                <div class="voucherline-description">合计</div>
                                                <div class="voucherline-asubname">{{ digitUppercase(getTotal(voucher,
                                                    "credit")) }}</div>
                                                <div class="voucherline-debit">{{ formatMoney(getTotal(voucher, "debit"))
                                                }}</div>
                                                <div class="voucherline-credit">{{ formatMoney(getTotal(voucher, "credit"))
                                                }}</div>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #voucherView>
                    <div class="slot-mini-content voucher-container">
                        <Voucher
                            v-model:query-params="voucherQueryParams"
                            ref="voucher"
                            @zoom="voucherZoomChange"
                            :edited="isEdited"
                            @save="saveVoucher"
                            :showCancel="true"
                            cancelTxt="取消"
                            @back="cancelEditVoucher"
                            :isGenerateVoucherPage="true"
                        ></Voucher>
                    </div>
                </template>
                <template #resultView>
                    <div class="slot-mini-content result-container">
                        <div class="main-top main-tool-bar space-between">
                            <a class="button solid-button" @click="back()">返回</a>
                            <div class="result-message" v-show="(genVoucherResult?.errorCount || 0) > 0">
                                共生成{{ genVoucherResult?.count || 0 }}张凭证，{{
                                    (genVoucherResult?.count || 0) - (genVoucherResult?.errorCount || 0)
                                }}张成功，
                                <span class="highlight-red">{{ genVoucherResult?.errorCount || 0 }}张失败</span>
                            </div>
                            <span class="assit"></span>
                        </div>
                        <div class="success-message" v-show="(genVoucherResult?.errorCount || 0) === 0">
                            <span class="success-icon"></span>共生成{{ genVoucherResult?.count || 0 }}张凭证，{{
                                (genVoucherResult?.count || 0) - (genVoucherResult?.errorCount || 0)
                            }}张成功，{{ genVoucherResult?.errorCount || 0 }}张失败
                        </div>
                        <div class="error-table" v-show="(genVoucherResult?.errorCount || 0) > 0">
                            <Table :data="genVoucherResult?.errorList" :columns="errorTableColumns">
                                <template #errorInfo>
                                    <el-table-column label="失败原因" min-width="470" align="left" header-align="left" :resizable="false">
                                        <template #default="scope">
                                            <span class="error-icon"></span><span v-html="scope.row.errorInfo"></span>
                                        </template>
                                    </el-table-column>
                                </template>
                            </Table>
                        </div>
                    </div>
                </template>
            </content-slider>
        </div>
    </div>
</template>
<style lang="less" scoped>
@import "@/style/SelfAdaption.less";

.content {
    .slot-mini-content {
        width: 100%;
        min-width: 1110px;
        .main-view {
            height: calc(100vh - 161px);
            overflow: auto;
        }
    }

    .generate-container {
        .main-tool-bar {
            :deep(.el-checkbox) {
                margin-left: 0px;
                margin-right: 0;
            }

            :deep(.ml27) {
                margin-left: 27px;
            }

            :deep(.ml10) {
                margin-left: 10px;

                input {
                    border-radius: 5px;
                }
            }

            :deep(.ml20) {
                margin-left: 20px;

                input {
                    border-radius: 5px;
                }
            }

            :deep(.help-icon) {
                background-image: url("@/assets/Icons/help.png");
                background-size: 100%;
                background-repeat: no-repeat;
                height: 16px;
                width: 16px;
            }
            :deep(.gear-icon) {
                background-image: url("@/assets/Icons/gear.png");
                background-size: 100%;
                background-repeat: no-repeat;
                height: 18px;
                width: 18px;
                cursor: pointer;
            }
        }

        .main-center {
            display: flex;

            .document-content {
                width: 334px;
                display: flex;
                flex-direction: column;
                align-items: stretch;

                .document-title {
                    height: 39px;
                    line-height: 39px;
                    color: var(--font-color);
                    font-size: var(--h5);
                    font-weight: 600;
                    background-color: #c6f2c8;
                    padding-left: 10px;
                    text-align: left;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                }

                .document-list {
                    display: flex;
                    flex-direction: column;
                    align-items: stretch;
                    margin-top: 10px;

                    .document-item {
                        display: flex;
                        flex-direction: column;
                        border: 1px solid var(--border-color);

                        .document-item-title {
                            background-color: var(--table-title-color);
                            height: 37px;
                            line-height: 37px;
                            font-size: var(--h5);
                            color: var(--font-color);
                            border-bottom: 1px solid var(--border-color);
                            padding-left: 20px;
                            text-align: left;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            :deep(.span_wrap) {
                                min-width: calc(100% - 94px); 
                                display: inline-block;
                                text-overflow: ellipsis;
                                overflow: hidden;
                                white-space: nowrap;
                                margin-right: 10px;
                            }
                            :deep(.popover_content) {
                                word-break: break-all;
                                color: #fff;
                            }
                            :deep(.icon-container) {
                                width: 130px;
                            }
                            .warn-icon,
                            .error-icon {
                                display: flex;
                                align-items: center;
                                font-size: var(--h5);
                                line-height: 20px;
                                width: auto;
                                white-space: nowrap;
                                padding-right: 20px;
                                &::before {
                                    content: " ";
                                    background-image: url("@/assets/Icons/warn.png");
                                    background-repeat: no-repeat;
                                    background-size: 100%;
                                    width: 16px;
                                    height: 16px;
                                    margin-right: 2px;
                                }
                            }

                            .warn-icon {
                                color: var(--yellow);

                                &::before {
                                    background-image: url("@/assets/Icons/warn.png");
                                }
                            }

                            .error-icon {
                                color: var(--red);

                                &::before {
                                    background-image: url("@/assets/Icons/warn-red.png");
                                }
                            }
                        }

                        .document-item-content {
                            padding: 0 20px 8px 20px;
                            display: flex;
                            flex-direction: column;
                            align-items: stretch;

                            div {
                                line-height: 17px;
                                font-size: var(--h5);
                                color: var(--font-color);
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                                text-align: left;
                                margin-top: 7px;
                            }
                        }
                    }
                }
            }

            .voucher-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: stretch;
                width: calc(100% - 334px);

                .voucher-title {
                    height: 39px;
                    line-height: 39px;
                    color: var(--font-color);
                    font-size: var(--h5);
                    font-weight: 600;
                    background-color: #c6f2c8;
                    padding-left: 20px;
                    text-align: left;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                }

                .voucher-list {
                    display: flex;
                    flex-direction: column;
                    align-items: stretch;
                    margin-top: 10px;
                    margin-left: 10px;

                    .voucher-item {
                        display: flex;
                        flex-direction: column;
                        border: 1px solid var(--border-color);

                        .voucher-item-title {
                            background-color: var(--table-title-color);
                            height: 37px;
                            border-bottom: 1px solid var(--border-color);
                            padding-left: 10px;
                            display: flex;
                            justify-content: flex-start;
                            align-items: center;

                            .voucher-info {
                                line-height: 37px;
                                font-size: var(--h5);
                                color: var(--font-color);
                            }

                            .voucher-operators {
                                display: flex;
                                align-items: center;
                                padding-left: 20px;
                                display: none;

                                .link {
                                    font-size: var(--h5);
                                }
                            }
                        }

                        .voucher-item-content {
                            display: flex;
                            flex-direction: column;
                            align-items: stretch;
                            text-align: left;

                            .voucherline {
                                height: 34px;
                                line-height: 34px;
                                font-size: var(--h5);
                                color: var(--font-color);
                                display: flex;

                                & + .voucherline {
                                    border-top: 1px solid var(--border-color);
                                }

                                .voucherline-description,
                                .voucherline-asubname,
                                .voucherline-debit,
                                .voucherline-credit {
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                    margin-left: 10px;
                                }

                                .voucherline-description {
                                    width: 340px;
                                }

                                .voucherline-asubname {
                                    width: 340px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                }

                                .voucherline-debit,
                                .voucherline-credit {
                                    width: 91px;
                                }
                            }
                        }

                        &:hover {
                            .voucher-item-title {
                                .voucher-operators {
                                    display: flex;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .voucher-container {
        // height: calc(var(--voucher-min-height) + 49px);
        // height: ~"max(calc(var(--voucher-min-height) + 49px), calc(100vh - var(--content-padding-bottom) - var(--title-height)))";

        .main-tool-bar {
            border-bottom: 1px solid var(--border-color);
            padding-left: 0;
            padding-right: 0;
            .operate {
                box-sizing: border-box;
                text-align: left;
            }
            &.zoom-out {
                .operate {
                    width: 100%;
                    padding-left: 54px;
                    padding-right: 54px;
                }
            }
            &.zoom-in {
                .operate {
                    width: 1050px;
                    margin: 0 auto;
                }
            }
        }

        .voucher-content {
            // height: 0;
            // flex: 1;
        }
    }

    .result-container {
        .main-tool-bar {
            .button {
                margin-right: -86px;
            }

            .result-message {
                line-height: 28px;
                color: var(--font-color);
                font-size: var(--h3);
            }
        }

        .success-message {
            padding: 72px 0 120px;
            font-size: var(--h3);
            color: var(--font-color);
            line-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;

            .success-icon {
                background-image: url("@/assets/Icons/success.png");
                background-size: 100%;
                background-repeat: no-repeat;
                height: 20px;
                width: 20px;
                margin-right: 8px;
            }
        }

        .error-table {
            padding: 0 10px 10px;

            .error-icon {
                display: inline-block;
                background-image: url("@/assets/Icons/warn-red.png");
                background-size: 100%;
                background-repeat: no-repeat;
                height: 16px;
                width: 16px;
                vertical-align: top;
                margin-right: 8px;
            }
        }
    }
}
</style>
<script lang="ts" setup>
import { ref, watchEffect, computed, reactive, provide, onActivated, onDeactivated } from "vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import {
    InvoiceGenVoucherParameters,
    type BaseDocumentModel,
    type DocumentWithVoucherModel,
    type IBatchGenerateVoucherModel,
    type IDocumentModel,
    type InvoiceWithVoucherModel,
    GenVoucherWithDocumentModel,
    GenVoucherModel,
    InvoiceDocumentModel,
    GenVoucherParameters,
    type BaseQueryParameters,
    InvoiceQueryParameters,
    type IGenVoucherResult,
    type IInvocieGenVoucherResult,
    JournalQueryParameters,
    type IJournalGenVoucherResult,
    JournalGenVoucherParameters,
    JournalDocumentModel,
    JournalWithVoucherModel,
    TransferQueryParameters,
    TransferWithVoucherModel,
    type ITransferGenVoucherResult,
    TransferGenVoucherParameters,
    TransferDocumentModel,
    DraftQueryParameters,
    type IDraftGenVoucherResult,
    DraftGenVoucherParameters,
    DraftDocumentModel,
    DraftWithVoucherModel,
    BillQueryParameters,
    BillGenVoucherParameters,
    BillDocumentModel,
    type IBillGenVoucherResult,
    BillWithVoucherModel,
    ExpenseBillQueryParameters,
    ExpenseBillGenVoucherParameters,
    ExpenseBillDocumentModel,
    type IExpenseBillGenVoucherResult,
    ExpenseBillWithVoucherModel,
    type IVoucherOperateLog,
} from "./types";
import {
    DataVoucherQueryParams,
    VoucherAttachFileModel,
    VoucherEntryModel,
    VoucherModel,
    VoucherQueryParams,
    VoucherSaveModel,
    VoucherSaveParams,
} from "../Voucher/types";
import { formatMoney, digitUppercase } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { PeriodStatus } from "@/api/period";
import { request, type IResponseModel } from "@/util/service";
import { dayjs } from "element-plus";
import Voucher from "@/components/Voucher/index.vue";
import { ElNotify } from "@/util/notify";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "../Table/IColumnProps";
import BubbleTip from "@/components/BubbleTip/index.vue";
import { ElConfirm } from "@/util/confirm";
import type { IVoucherSetting } from "@/components/Dialog/GenerateVoucherSetting/type";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountset";

import ToolTip from "@/components/Tooltip/index.vue";
const props = defineProps<{
    title: string;
    documentTitle: string;
    queryParams: BaseQueryParameters;
    errorTableColumns: IColumnProps[];
    mergeErrorFunction?: Function;
}>();
const emit = defineEmits(["back", "voucherChanged", "load-success", "saveOperateLog",]);
const slots = ref(["generate", "voucherView", "resultView"]);
const currentSlot = ref("generate");
const genVoucherModel = ref<IBatchGenerateVoucherModel<DocumentWithVoucherModel<BaseDocumentModel>>>();
const documentList = ref<IDocumentModel[]>([]);
const voucherList = ref<VoucherModel[]>([]);
const voucherDoucmentRelation = ref<Array<{ voucher: VoucherModel; documentList: Array<IDocumentModel> }>>();
const periodStore = useAccountPeriodStore();
const currentDate = computed(() => {
    let date = dayjs(
        new Date(
            periodStore.periodList.filter((p) => p.status === PeriodStatus.HasVoucher || p.status === PeriodStatus.ChangeOut).reverse()[0]
                ?.endDate || ""
        )
    ).format("YYYY-MM-DD");
    if (new Date(date).getFullYear() === new Date().getFullYear() && new Date(date).getMonth() === new Date().getMonth()) {
        date = dayjs(new Date()).format("YYYY-MM-DD");
    }
    return date;
});
const accountSubjectStore = useAccountSubjectStore();
const accountsetStore = useAccountSetStore();
const accountingStandard = ref(Number(accountsetStore.accountSet?.accountingStandard));
const voucherQueryParams = ref<VoucherQueryParams>();
const voucher = ref<InstanceType<typeof Voucher>>();
const genVoucherResult = ref<IGenVoucherResult<BaseDocumentModel>>();
const generateVoucherSetting = ref<IVoucherSetting>();
let asubDict: any = {};

function getTotal(voucher: VoucherModel, type: "debit" | "credit") {
    let total = 0;
    voucher.voucherLines.forEach((item) => {
        total += item[type];
    });
    return total;
}

function getDescription(voucherline: VoucherEntryModel) {
    var description = voucherline.description;
    if (voucherline.quantityAccounting === 1 && voucherline.quantity !== 0 && voucherline.price !== 0) {
        description += "(数量:" + voucherline.quantity + voucherline.measureUnit + ",单价:" + voucherline.price + "元）";
    }
    if (voucherline.foreigncurrency === 1 && !!voucherline.fcCode && voucherline.fcRate != 0) {
        description += "(" + voucherline.fcCode + ":" + voucherline.fcAmount + ",汇率:" + voucherline.fcRate + ")";
    }
    return description;
}

let vnumDict: any = {};
async function getVNum(vgId: number, vdate: string) {
    var key = vgId + "_" + new Date(vdate).getFullYear() + new Date(vdate).getMonth();
    if (!vnumDict[key]) {
        await request({
            url: `/api/Voucher/GetNewVNumAndVDate?vgId=${vgId}&vDate=${vdate}&dotAutoSupplementVNum=true`,
            method: "post",
        }).then((res: IResponseModel<{ vdate: string; vnum: number }>) => {
            if (res.state === 1000) {
                vnumDict[key] = res.data.vnum;
            }
        });
    }
    return vnumDict[key]++;
}

let removeRepeatedDescriptionMap = new Map<string, Set<string>>();
const estimatedItemHeightD = ref(143);
const estimatedItemHeightV = ref(173);

const visibleCount = ref(20);
const startIndexD = ref(0);
const endIndexD = ref(10);
const startIndexV = ref(0);
const endIndexV = ref(10);
const scrollTopD = ref(0);
const scrollTopV = ref(0);
const scrollBarHeight = ref(0);
const isEdited = computed(() => {
    const vid = voucher.value?.getVoucherModel().vid ?? 0;
    return vid === 0;
});
const checkIsEdited = () => isEdited.value && currentSlot.value === "voucherView";
let itemHeightCacheD:number[] = [];
let itemHeightCacheV:number[] = [];

let itemTopCacheD:number[] = [];
let itemTopCacheV:number[] = [];
const loading = ref(false);
const scrollContainerRef = ref();
const visibleDocumentList = ref<IDocumentModel[]>([]);
const visibleVoucherList = ref<VoucherModel[]>([]);
const calculateTotalHeight = (list:any, heightCache:number[], topCache:number[], estimatedItemHeight:number, getMarginBottom:Function) => {
    return list.reduce((pre:number, current:IDocumentModel|VoucherModel, index:number) => {
        let itemHeight = (current.height || estimatedItemHeight) + getMarginBottom(current);
        heightCache[index] = itemHeight;
        topCache[index] = index === 0 ? 0 : topCache[index - 1] + heightCache[index - 1];
        return pre + itemHeight;
    }, 0);
};
const generateEstimatedItemData = () => {
    loading.value = false;
    voucherList.value.forEach((v) => (v.height = v.voucherLines.length * 34 + 34 + 40 + v.voucherLines.length));
    const totalDocumentHeight = calculateTotalHeight(documentList.value, itemHeightCacheD, itemTopCacheD, estimatedItemHeightD.value, getDocumentMarginBottom);
    const totalVoucherHeight = calculateTotalHeight(voucherList.value, itemHeightCacheV, itemTopCacheV, estimatedItemHeightV.value, getVoucherMarginBottom);
    scrollBarHeight.value = Math.max(totalDocumentHeight, totalVoucherHeight) + 49;
};

const getStartIndex = (scrollTop: number, itemTopCache: number[]) => {
    let arr = itemTopCache;
    let index = 0;
    let left = 0,
        right = arr.length - 1;
    while (left <= right) {
        let mid = Math.floor((left + right) / 2);
        if (scrollTop >= arr[mid] && scrollTop < arr[mid + 1]) {
            return mid - 10 < 0 ? 0 : mid - 10;
        } else if (scrollTop < arr[mid]) {
            right = mid - 1;
        } else {
            left = mid + 1;
        }
    }
    index = left - 1;
    return index;
};
let scrollTopValue = 0;
const onScroll = () => {
    scrollTopValue = scrollContainerRef.value.scrollTop;
    const startIndexValueD = getStartIndex(scrollTopValue, itemTopCacheD);
    const startIndexValueV = getStartIndex(scrollTopValue, itemTopCacheV);
    startIndexD.value = startIndexValueD;

    endIndexD.value =
        startIndexValueD + visibleCount.value > documentList.value.length
            ? documentList.value.length
            : startIndexValueD + visibleCount.value;
    startIndexV.value = startIndexValueV;

    endIndexV.value =
        startIndexValueV + visibleCount.value > voucherList.value.length ? voucherList.value.length : startIndexValueV + visibleCount.value;
    scrollTopD.value = itemTopCacheD[startIndexD.value] || 0;
    scrollTopV.value = itemTopCacheV[startIndexV.value] || 0;

    visibleDocumentList.value = documentList.value.slice(startIndexD.value, endIndexD.value);
    visibleVoucherList.value = voucherList.value.slice(startIndexV.value, endIndexV.value);
};

function reloadVisibleData() {
    generateEstimatedItemData();
    visibleDocumentList.value = documentList.value.slice(startIndexD.value, endIndexD.value);
    visibleVoucherList.value = voucherList.value.slice(startIndexV.value, endIndexV.value);
}
let lastScrollTop = 0;
onActivated(() => {
    scrollContainerRef.value.scrollTop = lastScrollTop;
    visibleDocumentList.value = documentList.value.slice(startIndexD.value, endIndexD.value);
    visibleVoucherList.value = voucherList.value.slice(startIndexV.value, endIndexV.value);
});
onDeactivated(() => {
    lastScrollTop=scrollTopValue;
});
async function loadDocumentList(
    generateVoucherModel?: IBatchGenerateVoucherModel<DocumentWithVoucherModel<BaseDocumentModel>>,
    voucherSettings?: IVoucherSetting
) {
    loading.value = true;
    documentList.value = [];
    voucherList.value = [];
    startIndexD.value = 0;
    endIndexD.value = 10;
    startIndexV.value = 0;
    endIndexV.value = 10;
    scrollTopD.value = 0;
    scrollTopV.value = 0;
    scrollBarHeight.value = 0;
    scrollContainerRef.value.scrollTop = 0;
    voucherDoucmentRelation.value = [];
    itemHeightCacheD = [];
    itemHeightCacheV = [];
    itemTopCacheD = [];
    itemTopCacheV= [];
    visibleDocumentList.value = [];
    visibleVoucherList.value = [];
    generateVoucherSetting.value = voucherSettings;
    vnumDict = {};
    removeRepeatedDescriptionMap = new Map<string, Set<string>>();
    if (generateVoucherModel) {
        genVoucherModel.value = generateVoucherModel;
    }
    if (genVoucherModel.value) {
        if (props.queryParams instanceof InvoiceQueryParameters) {
            const queryParams = props.queryParams as InvoiceQueryParameters;
            let map = new Map<string, VoucherModel>();
            let voucherDoucmentMap = new Map<string, { voucher: VoucherModel; documentList: Array<IDocumentModel> }>();

            for (let i = 0; i < genVoucherModel.value.documentWithVoucherList.length; i++) {
                const model = genVoucherModel.value.documentWithVoucherList[i] as InvoiceWithVoucherModel;
                const voucherLines = JSON.parse(JSON.stringify(model.voucherLines)) as Array<VoucherEntryModel>;

                const document = {
                    title: model.document.invoiceType + (model.document.invoiceNum ? "：" + model.document.invoiceNum : ""),
                    contentList: [
                        `开票日期：${model.document.invoiceDate}`,
                        `业务类型：${model.document.businessType}`,
                        `金额：${model.document.amount.toFixed(2)}`,
                        `税额：${model.document.tax.toFixed(2)}`,
                    ],
                    document: model.document,
                    height: 24 * 4 + 48, // (17+7)*4+8+40
                };
                document.document.warnInfo = (model.document.warnInfo +=
                            model.document.warnInfo && checkExpensesCreditAccount(voucherLines)
                                ? "<br>"
                                : "" + checkExpensesCreditAccount(voucherLines));
                documentList.value.push(document);
                if (queryParams.isMerge && !document.document.errorInfo) {
                    let key = Array<string>();
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.invoiceDate;
                    }

                    if (queryParams.mergeDate) {
                        key.push(model.document.invoiceDate);
                    }
                    if (queryParams.mergeOpposite) {
                        key.push(model.document.nameId.toString() + model.document.oppositeName);
                    }
                    if (queryParams.mergeInvoiceType) {
                        key.push(model.document.invoiceType);
                    }
                    if (queryParams.mergeBusinessType) {
                        key.push(model.document.businessType);
                    }
                    let mapKey = key.join("-");
                    let voucherModel = new VoucherModel();
                    if (!map.has(mapKey)) {
                        map.set(mapKey, voucherModel);
                        voucherModel.vgId = model.vgId;
                        voucherModel.vgName = model.vgName;
                        voucherModel.vdate = vDate;
                        voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        voucherModel.attachments = 0;
                        voucherModel.voucherLines = [];
                        voucherList.value.push(voucherModel);
                        voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [] });
                        voucherDoucmentMap.set(mapKey, voucherDoucmentRelation.value[voucherDoucmentRelation.value.length - 1]);
                    } else {
                        voucherModel = map.get(mapKey) as VoucherModel;
                        if(vDate > voucherModel.vdate)  voucherModel.vdate = vDate;
                        if(new Date(vDate).getMonth() !== new Date(voucherModel.vdate).getMonth() && vDate > voucherModel.vdate  && !queryParams.mergeDate){
                            voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        }
                    }

                    voucherDoucmentMap.get(mapKey)?.documentList.push(document);
                    voucherModel.attachments = (voucherModel.attachments || 0) + (model.attachFiles.length || 1);
                    if(model.attachFileIds){
                        voucherModel.attachFileIds = voucherModel.attachFileIds
                            ? voucherModel.attachFileIds + "," + model.attachFileIds
                            : model.attachFileIds || "";
                    }

                    if (model.document.errorInfo) {
                        voucherModel.voucherLines = voucherModel.voucherLines.concat(voucherLines);
                    } else {
                        voucherLines.forEach((vl) => {
                            // if (model.document.invoiceCategory === 10070) {
                            //     vl.description = "销项发票_发票汇总";
                            // } else if (model.document.invoiceCategory === 10080) {
                            //     vl.description = "进项发票_发票汇总";
                            // }
                            if (queryParams.mergeOthers) {
                                if (vl.debit !== 0 && queryParams.mergeDebit) {
                                    const oldVoucherLine = voucherModel.voucherLines.filter(
                                        (ovl) =>
                                            ovl.asubId === vl.asubId &&
                                            ovl.asubCode === vl.asubCode &&
                                            ovl.asubName === vl.asubName &&
                                            ovl.fcId === vl.fcId &&
                                            ovl.aacode === vl.aacode &&
                                            ovl.debit !== 0
                                    )[0];
                                    if (oldVoucherLine) {
                                        mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                    } else {
                                        voucherModel.voucherLines.push(vl);
                                    }
                                } else if (vl.credit !== 0 && queryParams.mergeCredit) {
                                    const oldVoucherLine = voucherModel.voucherLines.filter(
                                        (ovl) =>
                                            ovl.asubId === vl.asubId &&
                                            ovl.asubCode === vl.asubCode &&
                                            ovl.asubName === vl.asubName &&
                                            ovl.fcId === vl.fcId &&
                                            ovl.aacode === vl.aacode &&
                                            ovl.credit !== 0
                                    )[0];
                                    if (oldVoucherLine) {
                                        mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                    } else {
                                        voucherModel.voucherLines.push(vl);
                                    }
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else {
                                voucherModel.voucherLines.push(vl);
                            }
                        });
                    }
                } else {
                    const voucherModel = new VoucherModel();
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.invoiceDate;
                    }
                    voucherModel.vgId = model.vgId;
                    voucherModel.vgName = model.vgName;
                    voucherModel.vdate = vDate;
                    voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                    voucherModel.attachments = (model.attachFiles?.length || 1);
                    voucherModel.attachFileIds = model.attachFileIds || "";
                    voucherModel.voucherLines = voucherLines;
                    voucherList.value.push(voucherModel);
                    voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [document] });
                }
            }

            if (queryParams.isMerge) {
                if (queryParams.mergeCredit || queryParams.mergeDebit) {
                    voucherList.value.forEach(v => {
                        v.voucherLines = sortArrayAndCheckDescription(v.voucherLines);
                    });
                }

                for(let i = 0; i < voucherList.value.length; i++){
                    voucherList.value[i].voucherLines = voucherList.value[i].voucherLines.filter((vl) => vl.debit !== 0 || vl.credit !== 0);
                }
            }

        }
        if (props.queryParams instanceof JournalQueryParameters) {
            const queryParams = props.queryParams as JournalQueryParameters;
            const emptyVoucherLine: Array<string> = [];
            let map = new Map<string, VoucherModel>();
            let voucherDoucmentMap = new Map<string, { voucher: VoucherModel; documentList: Array<IDocumentModel> }>();
            for (let i = 0; i < genVoucherModel.value.documentWithVoucherList.length; i++) {
                const model = genVoucherModel.value.documentWithVoucherList[i] as JournalWithVoucherModel;
                const voucherLines = JSON.parse(JSON.stringify(model.voucherLines)) as Array<VoucherEntryModel>;
                let amount = 0;
                if(model.document.ietypeName === "内部转账"){
                    if(model.document.income!==0){
                        amount = model.document.income;
                    } else{
                        amount = model.document.expenditure;
                    }
                } else{
                    amount = model.document.income + model.document.expenditure;
                }

                const document = {
                    title: `单据编号：${model.document.jtype === 1010 ? 1 : 2}-${model.document.cdAccountName.split("-")[0]
                    }-${model.document.cdDate.replace(/-/g, "")}-${model.document.lineSn}`,
                    contentList: [
                        `日期：${model.document.cdDate}`,
                        `收支类型：${model.document.ietypeName}`,
                        `摘要：${model.document.description}`,
                        `${model.document.income > 0 ? "收入" : "支出"}：${amount.toFixed(
                            2
                        )}`,
                    ],
                    document: model.document,
                    height: 24 * 4 + 48, // (17+7)*4+8+40
                };
                document.document.warnInfo = (model.document.warnInfo +=
                            model.document.warnInfo && checkExpensesCreditAccount(voucherLines)
                                ? "<br>"
                                : "" + checkExpensesCreditAccount(voucherLines));
                documentList.value.push(document);
                if (queryParams.isMerge && !document.document.errorInfo) {
                    let key = Array<string>();
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1 ) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.cdDate;
                    }

                    if (queryParams.mergeDate) {
                        key.push(model.document.cdDate);
                    }
                    if (queryParams.mergeIEType) {
                        key.push(model.document.ietypeName);
                    }
                    if (queryParams.mergeDirection) {
                        key.push(model.document.ietypeName.split("-")[0]);
                    }
                    if (queryParams.mergeAccount) {
                        key.push(model.document.cdAccount.toString());
                    }
                    let mapKey = key.join("-");
                    let voucherModel = new VoucherModel();
                    if (!map.has(mapKey)) {
                        map.set(mapKey, voucherModel);
                        voucherModel.vgId = model.vgId;
                        voucherModel.vgName = model.vgName;
                        voucherModel.vdate = vDate;
                        voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        voucherModel.attachments = 0;
                        voucherModel.attachFileIds = "";
                        voucherModel.voucherLines = [];
                        voucherList.value.push(voucherModel);
                        voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [] });
                        voucherDoucmentMap.set(mapKey, voucherDoucmentRelation.value[voucherDoucmentRelation.value.length - 1]);
                    } else {
                        voucherModel = map.get(mapKey) as VoucherModel;
                        if(vDate > voucherModel.vdate)  voucherModel.vdate = vDate;
                        if(new Date(vDate).getMonth() !== new Date(voucherModel.vdate).getMonth() && vDate > voucherModel.vdate && !queryParams.mergeDate ){
                            voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        }
                    }

                    voucherDoucmentMap.get(mapKey)?.documentList.push(document);
                    // voucherDoucmentRelation.value[0].documentList.push(document);
                    // const voucherModel = voucherList.value[0];
                    voucherModel.attachments = (voucherModel.attachments || 0) + model.attachFiles.length;
                    if(model.attachFileIds){
                        voucherModel.attachFileIds = voucherModel.attachFileIds
                            ? voucherModel.attachFileIds + "," + model.attachFileIds
                            : model.attachFileIds || "";
                    }

                    voucherLines.forEach((vl, i) => {
                        let mergeData = true;
                        if (vl.debit === 0 && vl.credit === 0) {
                            mergeData = false;
                            emptyVoucherLine.push(`${vl.asubId}-${vl.aacode}`);
                            voucherModel.voucherLines.push(vl);
                        } else if (
                            queryParams.mergeOthers &&
                            (!model.document.errorInfo || model.document.errorInfo === "部分凭证行没有金额")) {
                            mergeData = true;
                        }
                        else {
                            mergeData = false;
                            voucherModel.voucherLines.push(vl);
                        }
                        if (mergeData) {
                            if (vl.debit !== 0 && queryParams.mergeDebit) {
                                const oldVoucherLine = voucherModel.voucherLines.filter(
                                    (ovl) =>
                                        ovl.asubId === vl.asubId &&
                                        ovl.asubCode === vl.asubCode &&
                                        ovl.asubName === vl.asubName &&
                                        ovl.fcId === vl.fcId &&
                                        ovl.aacode === vl.aacode &&
                                        ovl.debit !== 0
                                )[0];
                                if (oldVoucherLine) {
                                    mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else if (vl.credit !== 0 && queryParams.mergeCredit) {
                                const oldVoucherLine = voucherModel.voucherLines.filter(
                                    (ovl) =>
                                        ovl.asubId === vl.asubId &&
                                        ovl.asubCode === vl.asubCode &&
                                        ovl.asubName === vl.asubName &&
                                        ovl.fcId === vl.fcId &&
                                        ovl.aacode === vl.aacode &&
                                        ovl.credit !== 0
                                )[0];
                                if (oldVoucherLine) {
                                    mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else{
                                voucherModel.voucherLines.push(vl);
                            }
                        }
                    });
                } else {
                    if(document.document.errorInfo){
                        voucherLines.forEach((vl, i) => {
                            if (vl.debit === 0 && vl.credit === 0) {
                                emptyVoucherLine.push(`${vl.asubId}-${vl.aacode}`);
                            }
                        });
                    }

                    const voucherModel = new VoucherModel();
                    voucherModel.vgId = model.vgId;
                    voucherModel.vgName = model.vgName;
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        voucherModel.vdate = currentDate.value;
                    } else {
                        voucherModel.vdate = model.document.cdDate;
                    }
                    voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                    voucherModel.attachments = model.attachFiles.length;
                    voucherModel.voucherLines = voucherLines;
                    voucherModel.attachFileIds = model.attachFileIds || "";
                    voucherList.value.push(voucherModel);
                    voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [document] });
                }
            }

            if (queryParams.isMerge) {
                if (queryParams.mergeCredit || queryParams.mergeDebit) {
                    voucherList.value.forEach(v => {
                        v.voucherLines = sortArrayAndCheckDescription(v.voucherLines);
                    });
                }

                for(let i = 0; i < voucherList.value.length; i++){
                    voucherList.value[i].voucherLines = voucherList.value[i].voucherLines.filter((vl) => vl.debit !== 0 || vl.credit !== 0 || emptyVoucherLine.indexOf(`${vl.asubId}-${vl.aacode}`) !== -1 );
                } 
            }
        }
        if (props.queryParams instanceof TransferQueryParameters) {
            const queryParams = props.queryParams as TransferQueryParameters;
            let map = new Map<string, VoucherModel>();
            let voucherDoucmentMap = new Map<string, { voucher: VoucherModel; documentList: Array<IDocumentModel> }>();
            for (let i = 0; i < genVoucherModel.value.documentWithVoucherList.length; i++) {
                const model = genVoucherModel.value.documentWithVoucherList[i] as TransferWithVoucherModel;
                const voucherLines = JSON.parse(JSON.stringify(model.voucherLines)) as Array<VoucherEntryModel>;
                const document = {
                    title: `内部转账：${i + 1}`,
                    contentList: [
                        `日期：${model.document.cdDate}`,
                        `摘要：${model.document.description}`,
                        `转出账户：${model.document.cdAccountName}`,
                        `转入账户：${model.document.cdAccountInName}`,
                    ],
                    height: 24 * 4 + 48,
                    document: model.document,
                };
                document.document.warnInfo = (model.document.warnInfo +=
                            model.document.warnInfo && checkExpensesCreditAccount(voucherLines)
                                ? "<br>"
                                : "" + checkExpensesCreditAccount(voucherLines));
                documentList.value.push(document);
                if (queryParams.isMerge && !document.document.errorInfo) {
                    let key = Array<string>();
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1 ) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.cdDate;
                    }
                    if (queryParams.mergeDate) {
                        key.push(model.document.cdDate);
                    }
                    if (queryParams.mergeAccount) {
                        key.push(model.document.cdAccount.toString());
                    }
                    let mapKey = key.join("-");
                    let voucherModel = new VoucherModel();
                    if (!map.has(mapKey)) {
                        map.set(mapKey, voucherModel);
                        voucherModel.vgId = model.vgId;
                        voucherModel.vgName = model.vgName;
                        voucherModel.vdate = vDate;
                        voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        voucherModel.attachments = 0;
                        voucherModel.voucherLines = [];
                        voucherList.value.push(voucherModel);
                        voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [] });
                        voucherDoucmentMap.set(mapKey, voucherDoucmentRelation.value[voucherDoucmentRelation.value.length - 1]);
                    } else {
                        voucherModel = map.get(mapKey) as VoucherModel;
                        if(vDate > voucherModel.vdate)  voucherModel.vdate = vDate;
                        if(new Date(vDate).getMonth() !== new Date(voucherModel.vdate).getMonth() && vDate > voucherModel.vdate  && !queryParams.mergeDate){
                            voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        }
                    }

                    voucherDoucmentMap.get(mapKey)?.documentList.push(document);
                    voucherLines.forEach((vl) => {
                        if (queryParams.mergeOthers) {
                            if (vl.debit !== 0 && queryParams.mergeDebit) {
                                const oldVoucherLine = voucherModel.voucherLines.filter(
                                    (ovl) =>
                                        ovl.asubId === vl.asubId &&
                                        ovl.asubCode === vl.asubCode &&
                                        ovl.asubName === vl.asubName &&
                                        ovl.fcId === vl.fcId &&
                                        ovl.aacode === vl.aacode &&
                                        ovl.debit !== 0
                                )[0];
                                if (oldVoucherLine) {
                                    mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else if (vl.credit !== 0 && queryParams.mergeCredit) {
                                const oldVoucherLine = voucherModel.voucherLines.filter(
                                    (ovl) =>
                                        ovl.asubId === vl.asubId &&
                                        ovl.asubCode === vl.asubCode &&
                                        ovl.asubName === vl.asubName &&
                                        ovl.fcId === vl.fcId &&
                                        ovl.aacode === vl.aacode &&
                                        ovl.credit !== 0
                                )[0];
                                if (oldVoucherLine) {
                                    mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else {
                                voucherModel.voucherLines.push(vl);
                            }
                        } else {
                            voucherModel.voucherLines.push(vl);
                        }
                    });
                } else {
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1 ) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.cdDate;
                    }
                    const voucherModel = new VoucherModel();
                    voucherModel.vgId = model.vgId;
                    voucherModel.vgName = model.vgName;
                    voucherModel.vdate = vDate;
                    voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                    voucherModel.attachments = 0;
                    voucherModel.voucherLines = voucherLines;
                    voucherList.value.push(voucherModel);
                    voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [document] });
                }
            }

            if (queryParams.isMerge) {
                if (queryParams.mergeCredit || queryParams.mergeDebit) {
                    voucherList.value.forEach(v => {
                        v.voucherLines = sortArrayAndCheckDescription(v.voucherLines);
                    });
                }

                for(let i = 0; i < voucherList.value.length; i++){
                    voucherList.value[i].voucherLines = voucherList.value[i].voucherLines.filter((vl) => vl.debit !== 0 || vl.credit !== 0);
                }
            }

        }
        if (props.queryParams instanceof DraftQueryParameters) {
            const queryParams = props.queryParams as DraftQueryParameters;
            for (let i = 0; i < genVoucherModel.value.documentWithVoucherList.length; i++) {
                const model = genVoucherModel.value.documentWithVoucherList[i] as DraftWithVoucherModel;
                const voucherLines = JSON.parse(JSON.stringify(model.voucherLines)) as Array<VoucherEntryModel>;
                const document = {
                    title: `票据号：${model.document.draftNo}`,
                    contentList: [
                        `出票日期：${model.document.draftCreatedDate}`,
                        `票据种类：${model.document.draftTypeName}`,
                        `出票人：${model.document.drawerName}`,
                        `金额：${model.document.draftAmount.toFixed(2)}`,
                    ],
                    height: 24 * 4 + 48,
                    document: model.document,
                };
                document.document.warnInfo = (model.document.warnInfo +=
                            model.document.warnInfo && checkExpensesCreditAccount(voucherLines)
                                ? "<br>"
                                : "" + checkExpensesCreditAccount(voucherLines));
                documentList.value.push(document);
                if (queryParams.isMerge) {
                    if (i === 0) {
                        const voucherModel = new VoucherModel();
                        voucherModel.vgId = model.vgId;
                        voucherModel.vgName = model.vgName;
                        voucherModel.vdate = currentDate.value;
                        voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        voucherModel.attachments = 0;
                        voucherModel.voucherLines = [];
                        voucherList.value.push(voucherModel);
                        voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [] });
                    }
                    voucherDoucmentRelation.value[0].documentList.push(document);
                    const voucherModel = voucherList.value[0];
                    model.attachFiles.forEach((item) => {
                        if (!voucherModel.attachFiles.some((af) => af.fileId === item.fileId)) {
                            voucherModel.attachFiles.push(item);
                            voucherModel.attachFileIds = voucherModel.attachFileIds
                                ? voucherModel.attachFileIds + "," + item.fileId
                                : item.fileId.toString();
                            if (!voucherModel.attachments) {
                                voucherModel.attachments = 0;
                            }
                            voucherModel.attachments++;
                        }
                    });

                    if (model.document.errorInfo) {
                        voucherModel.voucherLines = voucherModel.voucherLines.concat(voucherLines);
                    } else {
                        voucherLines.forEach((vl) => {
                            vl.description = "票据汇总";
                            const oldVoucherLine = voucherModel.voucherLines.filter(
                                (ovl) =>
                                    ovl.asubId === vl.asubId &&
                                    ovl.asubCode === vl.asubCode &&
                                    ovl.asubName === vl.asubName &&
                                    ovl.fcId === vl.fcId &&
                                    ovl.aacode === vl.aacode
                            )[0];
                            if (oldVoucherLine) {
                                mergeVoucherLine(oldVoucherLine, vl);
                            } else {
                                voucherModel.voucherLines.push(vl);
                            }
                        });
                    }
                } else {
                    const voucherModel = new VoucherModel();
                    voucherModel.vgId = model.vgId;
                    voucherModel.vgName = model.vgName;
                    voucherModel.vdate = currentDate.value;
                    voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                    voucherModel.attachments = model.attachFiles.length;
                    voucherModel.attachFileIds = model.attachFiles.map((item) => item.fileId).join(",");
                    voucherModel.voucherLines = voucherLines;
                    voucherList.value.push(voucherModel);
                    voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [document] });
                }
            }
            if (queryParams.isMerge) {
                voucherList.value[0].voucherLines = voucherList.value[0].voucherLines.filter((vl) => vl.debit !== 0 || vl.credit !== 0);
            }
        }
        if (props.queryParams instanceof BillQueryParameters) {
            const queryParams = props.queryParams as BillQueryParameters;
            let map = new Map<string, VoucherModel>();
            let voucherDoucmentMap = new Map<string, { voucher: VoucherModel; documentList: Array<IDocumentModel> }>();
            for (let i = 0; i < genVoucherModel.value.documentWithVoucherList.length; i++) {
                const model = genVoucherModel.value.documentWithVoucherList[i] as BillWithVoucherModel;
                const voucherLines = JSON.parse(JSON.stringify(model.voucherLines)) as Array<VoucherEntryModel>;
                const document = {
                    title: `单据编号：${model.document.bill.billNo}`,
                    contentList: [`日期：${model.document.bill.billDateText}`, `单据类型：${model.document.billTypeText}`],
                    document: model.document,
                    height: 24 * 2 + 48,
                };
                document.document.warnInfo = (model.document.warnInfo +=
                            model.document.warnInfo && checkExpensesCreditAccount(voucherLines)
                                ? "<br>"
                                : "" + checkExpensesCreditAccount(voucherLines));
                switch (queryParams.rootBillType) {
                    case 1010:
                        document.contentList.push(`供应商：${model.document.bill.vendorInfo.num} ${model.document.bill.vendorInfo.name}`);
                        if (model.document.bill.purchaseAmount) {
                            document.contentList.push(`金额：${formatMoney(model.document.bill.purchaseAmount)}`);
                        }
                        if (model.document.bill.taxAmount) {
                            document.contentList.push(`税额：${formatMoney(model.document.bill.taxAmount)}`);
                        }
                        model.document.bill.procurementCostList.forEach((item: any) => {
                            document.contentList.push(`${item.item2.name}：${item.item3.toFixed(2)}`);
                        });
                        if (model.document.bill.thisArrears) {
                            document.contentList.push(`本次付款：${formatMoney(model.document.bill.thisArrears)}`);
                        }
                        if (model.document.bill.paymentDiscount) {
                            document.contentList.push(`付款优惠：${model.document.bill.paymentDiscount.toFixed(2)}`);
                        }
                        break;
                    case 1020:
                        document.contentList.push(`客户：${model.document.bill.customerInfo.num} ${model.document.bill.customerInfo.name}`);
                        if (model.document.bill.thisArrears) {
                            document.contentList.push(`本次欠款：${model.document.bill.thisArrears.toFixed(2)}`);
                        }
                        if (model.document.bill.collectionAmount) {
                            document.contentList.push(`收款优惠：${model.document.bill.collectionAmount.toFixed(2)}`);
                        }
                        if (model.document.bill.taxAmount) {
                            document.contentList.push(`税额：${model.document.bill.taxAmount.toFixed(2)}`);
                        }
                        if (model.document.bill.customerCost) {
                            document.contentList.push(`客户承担费用：${model.document.bill.customerCost.toFixed(2)}`);
                        }
                        break;
                    case 1030:
                    case 1040:
                        document.contentList.push(
                            `${queryParams.rootBillType === 1030 ? "客户" : "供应商"}：${model.document.bill.basicData.num} ${model.document.bill.basicData.name
                            }`
                        );
                        if (model.document.bill.amount) {
                            document.contentList.push(`金额：${model.document.bill.amount.toFixed(2)}`);
                        }
                        break;
                    case 1050:
                    case 1060:
                    case 1070:
                        if (model.document.bill.amount) {
                            document.contentList.push(`金额：${model.document.bill.amount.toFixed(2)}`);
                        }
                        break;
                    case 1080:
                        break;
                    case 1090:
                    case 1100:
                        if (model.document.bill.accountDataList.length > 0) {
                            document.contentList.push(
                                `账户：${model.document.bill.accountDataList.map((item: any) => `${item.num} ${item.name}`).join("、")}`
                            );
                        }
                        if (model.document.bill.amount) {
                            document.contentList.push(`金额：${model.document.bill.totalAmount.toFixed(2)}`);
                        }
                        break;
                    case 1110:
                    case 1120:
                        if (model.document.bill.amount) {
                            document.contentList.push(
                                `${queryParams.rootBillType === 1110 ? "组装费用" : "拆卸费用"}：${model.document.bill.amount.toFixed(2)}`
                            );
                        }
                        break;
                    case 1130:
                        document.contentList.push(`客户：${model.document.bill.transferData.num} ${model.document.bill.transferData.name}`);
                        document.contentList.push(
                            `供应商：${model.document.bill.transferToData.num} ${model.document.bill.transferToData.name}`
                        );
                        if (model.document.bill.amount) {
                            document.contentList.push(`核销金额：${model.document.bill.amount.toFixed(2)}`);
                        }
                        break;
                    case 1140:
                        break;
                }
                document.height = 24 * document.contentList.length + 48;
                documentList.value.push(document);

                if (queryParams.isMerge && !document.document.errorInfo) {
                    let key = Array<string>();
                    if (queryParams.mergeDate) {
                        key.push(model.document.bill.billDateText);
                    }
                    if (queryParams.mergeBillType) {
                        key.push(model.document.billTypeText);
                    }
                    if (queryParams.mergeOpposite) {
                        key.push(getBillId(queryParams.rootBillType, model));
                    }
                    let mapKey = key.join("-");
                    let voucherModel = new VoucherModel();

                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.bill.billDateText;
                    }
                    if (!map.has(mapKey)) {
                        voucherModel.vgId = model.vgId;
                        voucherModel.vgName = model.vgName;
                        voucherModel.vdate = vDate;
                        voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        voucherModel.attachments = 0;
                        voucherModel.voucherLines = [];
                        voucherList.value.push(voucherModel);
                        voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [] });
                        map.set(mapKey, voucherModel);
                        voucherDoucmentMap.set(mapKey, voucherDoucmentRelation.value[voucherDoucmentRelation.value.length - 1]);
                    } else {
                        voucherModel = map.get(mapKey) as VoucherModel;
                        if(vDate > voucherModel.vdate)  voucherModel.vdate = vDate;
                        if(new Date(vDate).getMonth() !== new Date(voucherModel.vdate).getMonth() && vDate > voucherModel.vdate && !queryParams.mergeDate){
                            voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        }
                    }
                    voucherDoucmentMap.get(mapKey)?.documentList.push(document);

                    if (model.document.errorInfo) {
                        voucherModel.voucherLines = voucherModel.voucherLines.concat(voucherLines);
                    } else {
                        voucherLines.forEach((vl) => {
                            if(queryParams.mergeOthers){
                                if(vl.debit != 0 && queryParams.mergeDebit || vl.credit != 0 && queryParams.mergeCredit){
                                    const oldVoucherLine = voucherModel.voucherLines.filter(
                                    (ovl) =>{
                                        const sameDir = vl.debit != 0 && queryParams.mergeDebit ? ovl.debit !== 0 : ovl.credit !== 0;
                                        return  ovl.asubId === vl.asubId &&
                                        ovl.asubCode === vl.asubCode &&
                                        ovl.asubName === vl.asubName &&
                                        ovl.fcId === vl.fcId &&
                                        ovl.aacode === vl.aacode && sameDir
                                    })[0];

                                    if (oldVoucherLine) {
                                        mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                    } else {
                                        voucherModel.voucherLines.push(vl);
                                    }
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else {
                                voucherModel.voucherLines.push(vl);
                            }
                        });
                    }

                } else {
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.bill.billDateText;
                    }

                    const voucherModel = new VoucherModel();
                    voucherModel.vgId = model.vgId;
                    voucherModel.vgName = model.vgName;
                    voucherModel.vdate = model.document.bill.billDateText;
                    voucherModel.vdate = vDate;
                    voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                    voucherModel.attachments = 0;
                    voucherModel.voucherLines = voucherLines;
                    voucherList.value.push(voucherModel);
                    voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [document] });
                }
            }
            if (queryParams.isMerge) {
                if (queryParams.mergeCredit || queryParams.mergeDebit) {
                    voucherList.value.forEach(v => {
                        v.voucherLines = sortArrayAndCheckDescription(v.voucherLines);
                    });
                }

                for(let i = 0; i < voucherList.value.length; i++){
                    voucherList.value[i].voucherLines = voucherList.value[i].voucherLines.filter((vl) => vl.debit !== 0 || vl.credit !== 0);
                }
            }
        }
        if (props.queryParams instanceof ExpenseBillQueryParameters) {
            const queryParams = props.queryParams as ExpenseBillQueryParameters;
            let map = new Map<string, VoucherModel>();
            let voucherDoucmentMap = new Map<string, { voucher: VoucherModel; documentList: Array<IDocumentModel> }>();
            for (let i = 0; i < genVoucherModel.value.documentWithVoucherList.length; i++) {
                const model = genVoucherModel.value.documentWithVoucherList[i] as ExpenseBillWithVoucherModel;
                const voucherLines = JSON.parse(JSON.stringify(model.voucherLines)) as Array<VoucherEntryModel>;
                const document = {
                    title: `单据编号：${model.document.billNo}`,
                    contentList: [`日期：${model.document.billDate}`, `费用类型：${model.document.billTypeName}`, `费用事由：${model.document.billDesc}`,`结算方式：${model.document.pmName}`,`单据金额：${model.document.totalAmount}`],
                    document: model.document,
                    height: 24 * 5 + 48,
                };
                document.document.warnInfo = (model.document.warnInfo +=
                            model.document.warnInfo && checkExpensesCreditAccount(voucherLines)
                                ? "<br>"
                                : "" + checkExpensesCreditAccount(voucherLines));

                document.height = 24 * document.contentList.length + 48;
                documentList.value.push(document);

                if (queryParams.isMerge && !document.document.errorInfo) {
                    let key = Array<string>();
                    if (queryParams.mergeDate) {
                        key.push(model.document.billDate);
                    }
                    if (queryParams.mergeBillType) {
                        key.push(model.document.billTypeName);
                    }
                    if (queryParams.mergePayMethod) {
                        key.push(model.document.pmName);
                    }
                    let mapKey = key.join("-");
                    let voucherModel = new VoucherModel();

                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.billDate;
                    }
                    if (!map.has(mapKey)) {
                        voucherModel.vgId = model.vgId;
                        voucherModel.vgName = model.vgName;
                        voucherModel.vdate = vDate;
                        voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        voucherModel.attachments = 0;
                        voucherModel.voucherLines = [];
                        voucherList.value.push(voucherModel);
                        voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [] });
                        map.set(mapKey, voucherModel);
                        voucherDoucmentMap.set(mapKey, voucherDoucmentRelation.value[voucherDoucmentRelation.value.length - 1]);
                    } else {
                        voucherModel = map.get(mapKey) as VoucherModel;
                        if(vDate > voucherModel.vdate)  voucherModel.vdate = vDate;
                        if(new Date(vDate).getMonth() !== new Date(voucherModel.vdate).getMonth() && vDate > voucherModel.vdate && !queryParams.mergeDate){
                            voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                        }
                    }
                    voucherDoucmentMap.get(mapKey)?.documentList.push(document);

                    if (model.document.errorInfo) {
                        voucherModel.voucherLines = voucherModel.voucherLines.concat(voucherLines);
                    } else {
                        voucherLines.forEach((vl) => {
                            if(queryParams.mergeOthers){
                                if(vl.debit != 0 && queryParams.mergeDebit || vl.credit != 0 && queryParams.mergeCredit){
                                    const oldVoucherLine = voucherModel.voucherLines.filter(
                                    (ovl) =>{
                                        const sameDir = vl.debit != 0 && queryParams.mergeDebit ? ovl.debit !== 0 : ovl.credit !== 0;
                                        return  ovl.asubId === vl.asubId &&
                                        ovl.asubCode === vl.asubCode &&
                                        ovl.asubName === vl.asubName &&
                                        ovl.fcId === vl.fcId &&
                                        ovl.aacode === vl.aacode && sameDir
                                    })[0];

                                    if (oldVoucherLine) {
                                        mergeVoucherLine(oldVoucherLine, vl, mapKey);
                                    } else {
                                        voucherModel.voucherLines.push(vl);
                                    }
                                } else {
                                    voucherModel.voucherLines.push(vl);
                                }
                            } else {
                                voucherModel.voucherLines.push(vl);
                            }
                        });
                    }
                } else {
                    let vDate = "";
                    if (generateVoucherSetting.value?.voucherDate === 1) {
                        vDate = currentDate.value;
                    } else {
                        vDate = model.document.billDate;
                    }

                    const voucherModel = new VoucherModel();
                    voucherModel.vgId = model.vgId;
                    voucherModel.vgName = model.vgName;
                    voucherModel.vdate = model.document.billDate;
                    voucherModel.vdate = vDate;
                    voucherModel.vnum = await getVNum(voucherModel.vgId, voucherModel.vdate);
                    voucherModel.attachments = 0;
                    voucherModel.voucherLines = voucherLines;
                    voucherList.value.push(voucherModel);
                    voucherDoucmentRelation.value.push({ voucher: voucherModel, documentList: [document] });
                }
            }

            if (queryParams.isMerge) {
                if (queryParams.mergeCredit || queryParams.mergeDebit) {
                    voucherList.value.forEach(v => {
                        v.voucherLines = sortArrayAndCheckDescription(v.voucherLines);
                    });
                }

                for(let i = 0; i < voucherList.value.length; i++){
                    voucherList.value[i].voucherLines = voucherList.value[i].voucherLines.filter((vl) => vl.debit !== 0 || vl.credit !== 0);
                }
            }
        }
        if (props.queryParams.isMerge && voucherList.value[0].voucherLines.length === 0) {
            ElConfirm("您选择的" + props.documentTitle + "记录合并后数据为空，将无法生成凭证，系统将自动分开生成凭证。", true).then(() => {
                // props.queryParams.isMerge = false;
                props.mergeErrorFunction?.();
                loadDocumentList();
            });
        }
        visibleDocumentList.value = documentList.value.slice(0, visibleCount.value);
        visibleVoucherList.value = voucherList.value.slice(0, visibleCount.value);
        generateEstimatedItemData();
        emit("load-success");
    }
}

// 费用科目
const expenseAccount = computed(() => {
    switch (accountingStandard.value) {
        case 3:
            return 9;
        case 6:
            return 10;
        default:
            return 6;
    }
});
watchEffect(() => {
    const accountSubject = accountSubjectStore.accountSubjectList;
    asubDict = {};
    accountSubject.forEach((asub) => {
        if (asub.status === 0 && asub.isLeafNode) {
            asubDict[asub.asubId] = asub;
        }
    });
});

function checkExpensesCreditAccount(lines: Array<VoucherEntryModel>) {
    for (let i = 0; i < lines.length; i++) {
        let v = lines[i];
        if (
            asubDict[Number(v.asubId)] &&
            asubDict[Number(v.asubId)].asubType === expenseAccount.value &&
            isIncomeOrExpense(v, expenseAccount.value) &&
            v.credit
        ) {
            return v.asubAAName + "建议录入借方金额，否则会对利润表取值有影响哦~";
        }
    }
    return "";
}
function isIncomeOrExpense(line: VoucherEntryModel, expenseAccount: number): boolean {
    return expenseAccount !== 6 ? true : asubDict[Number(line.asubId)].direction === 1;
}
function getBillId(rootBillType:number, model:BillWithVoucherModel){
    let id: string | number = 0;
    switch (rootBillType) {
        case 1020:
        case 1060:
        case 1080:
            id = model.document.bill.customerInfo?.id??0;
            break;
        case 1010:
        case 1050:
            id = model.document.bill.vendorInfo?.id??0;
            break;
        case 1030:
        case 1040:
        case 1090:
        case 1100:
            if (model.document.bill.basicData) {
                id = model.document.bill.basicData.id;
            } else {
                id = "null-" + model.document.bill.id;
            }
            break;
        case 1130:
            id = model.document.bill.transferData.id + "-" + model.document.bill.transferToData.id;
            break;
    }
    return id.toString();
}

// 借贷排序、摘要长度检查
function sortArrayAndCheckDescription(array: Array<VoucherEntryModel>) {
    let arr = [];
    let arr1 = [];
    for (let i = 0; i < array.length; i++) {
        if (array[i].credit !== 0) {
            arr.push(array[i]);
        } else {
            arr1.push(array[i]);
        }
        if(array[i].description.length > 256){
            array[i].description = array[i].description.substring(0,256);
        }
    }
    return arr1.concat(arr);
}

function mergeVoucherLine(oldVoucherLine: VoucherEntryModel, newVoucherLine: VoucherEntryModel, key?: string) {
    const direction =
        (oldVoucherLine.debit === 0 && newVoucherLine.debit === 0) || (oldVoucherLine.credit === 0 && newVoucherLine.credit === 0) ? 1 : -1;
    if (direction === 1) {
        oldVoucherLine.debit = Number((oldVoucherLine.debit + newVoucherLine.debit).toFixed(2));
        oldVoucherLine.credit = Number((oldVoucherLine.credit + newVoucherLine.credit).toFixed(2));
    } else {
        const amount = Number((oldVoucherLine.debit - oldVoucherLine.credit + newVoucherLine.debit - newVoucherLine.credit).toFixed(8));
        if (amount > 0) {
            oldVoucherLine.debit = amount;
            oldVoucherLine.credit = 0;
        } else {
            oldVoucherLine.debit = 0;
            oldVoucherLine.credit = -amount;
        }
    }
    if (oldVoucherLine.quantityAccounting === 1) {
        oldVoucherLine.quantity = Number((oldVoucherLine.quantity + newVoucherLine.quantity * direction).toFixed(8));
        if (oldVoucherLine.quantity === 0) {
            oldVoucherLine.price = 0;
        } else {
            oldVoucherLine.price = Number(((oldVoucherLine.debit + oldVoucherLine.credit) / oldVoucherLine.quantity).toFixed(8));
        }
    }
    if (oldVoucherLine.foreigncurrency === 1) {
        oldVoucherLine.fcAmount = Number((oldVoucherLine.fcAmount + newVoucherLine.fcAmount * direction).toFixed(2));
        oldVoucherLine.fcRate = Number(((oldVoucherLine.debit + oldVoucherLine.credit) / oldVoucherLine.fcAmount).toFixed(8));
    }

    // 处理重复摘要
    if(key !== undefined){
        key = key + "-" + newVoucherLine.asubId + "-" + newVoucherLine.aacode + "-" + (newVoucherLine.debit === 0 ? "0" : "1" );
        if(newVoucherLine?.description === undefined || newVoucherLine.description === ''){
            return;
        }
        if(!removeRepeatedDescriptionMap.has(key)){
            removeRepeatedDescriptionMap.set(key, new Set(oldVoucherLine.description.split("_")));
        } 
        let set = removeRepeatedDescriptionMap.get(key) as Set<string>;
        let descriptionArr = newVoucherLine.description.split("_").filter((item) => !set.has(item));
        if(descriptionArr.length > 0){
            oldVoucherLine.description = oldVoucherLine.description + "_" + descriptionArr.join("_");
            descriptionArr.forEach((item) => {
                set.add(item);
            });
        }
        if(oldVoucherLine.description.length > 256){
            oldVoucherLine.description = oldVoucherLine.description.substring(0,256);
        }
    }
}

function getDocumentMarginBottom(document: IDocumentModel): number {
    const minMarginBottom = 10;
    if (props.queryParams.isMerge) {
        return minMarginBottom;
    }
    const relation = voucherDoucmentRelation.value?.find((i) => i.documentList.indexOf(document) !== -1);
    if (relation) {
        if (relation.documentList.indexOf(document) === relation.documentList.length - 1) {
            let voucherHeight =
                (relation.voucher.voucherLines.length + 1) * 34 + relation.voucher.voucherLines.length * 1 + 40 + minMarginBottom;
            let documentHeight = 0;
            relation.documentList.forEach((d) => {
                documentHeight += d.contentList.length * 24 + 8 + 40 + minMarginBottom;
            });
            if (documentHeight > voucherHeight) {
                return minMarginBottom;
            } else {
                return voucherHeight - documentHeight + minMarginBottom;
            }
        } else {
            return minMarginBottom;
        }
    } else {
        return minMarginBottom;
    }
}

function getVoucherMarginBottom(voucher: VoucherModel): number {
    const minMarginBottom = 10;
    if (props.queryParams.isMerge) {
        return minMarginBottom;
    }
    const relation = voucherDoucmentRelation.value?.find((i) => i.voucher === voucher);
    if (relation) {
        let voucherHeight =
            (relation.voucher.voucherLines.length + 1) * 34 + relation.voucher.voucherLines.length * 1 + 40 + minMarginBottom;
        let documentHeight = 0;
        relation.documentList.forEach((d) => {
            documentHeight += d.contentList.length * 24 + 8 + 40 + minMarginBottom;
        });
        if (documentHeight < voucherHeight) {
            return minMarginBottom;
        } else {
            return documentHeight - voucherHeight + minMarginBottom;
        }
    } else {
        return minMarginBottom;
    }
}

interface FileObject {
    fileId: number;
    fileName: string;
    filePath: string;
    fileSize: string;
    fileType: number;
    relativePath: string;
    thumbPath: string;
    webPath: string;
}

async function getVoucherAttachFileModelList(attachFileIds: string) {
    let attachFileIdList: Array<VoucherAttachFileModel> = [];
    await request({
        url: "/api/Voucher/GetVoucherAttachFileModelList?attachFileIds=" + attachFileIds,
        method: "post",
    }).then((res: IResponseModel<Array<FileObject>>) => {
        if (res.state === 1000) {
            attachFileIdList = res.data;
        }
    });
    return attachFileIdList;
}

let editVoucherLog : any = {};

function saveEditVoucherLogBefore (voucherModel: VoucherModel) {
    editVoucherLog = {
        vgId: voucherModel.vgId,
        vLines: []
    };

    for(let idx in voucherModel.voucherLines){
        const voucherLine = voucherModel.voucherLines[idx];
        editVoucherLog.vLines.push(`${voucherLine.asubName} ${voucherLine.credit} ${voucherLine.debit}`); 
    }
}

function saveEditVoucherLogAfter (voucherModel: VoucherModel) {
    const target = voucherDoucmentRelation.value?.find((i) => i.voucher === voucherModel);

    if(!target){
        return;
    }

    let content = "";
    if (props.queryParams instanceof InvoiceQueryParameters) {
        content = 'invId: ' + target.documentList.map((d) => (d.document as InvoiceDocumentModel).invoiceId).join(",") + '\n';
    } else if (props.queryParams instanceof JournalQueryParameters) {
        content += 'journalId: ' + target.documentList.map((d) => {
            const journal = d.document as JournalDocumentModel;
            return `${journal.jtype}_${journal.cdAccount}_${journal.cdDate}_${journal.lineSn}`;
        }).join(",") + '\n';
    }
    
    if(editVoucherLog.vgId !== voucherModel.vgId){
        content += `vgId: ${editVoucherLog.vgId} -> ${voucherModel.vgId}\n`;
    }

    let hasChange = false;
    const lineCount = Math.max(editVoucherLog.vLines.length, voucherModel.voucherLines.length);

    for(let idx = 0; idx < lineCount; idx++){
        const vLine = editVoucherLog.vLines[idx];
        const vLineStr = vLine && vLine.length ? vLine : "empty";
        const voucherLine = voucherModel.voucherLines[idx];
        const voucherLineStr = voucherLine ? `${voucherLine.asubName} ${voucherLine.credit} ${voucherLine.debit}` : "empty";

        if(vLineStr !== voucherLineStr){
            content += `${vLineStr} -> ${voucherLineStr}\n`;
            hasChange = true;
        }
    }

    if(!hasChange) return;

    const data : IVoucherOperateLog = {
        operateType: 1020,
        setting: "",
        content: content,
    };
    
    emit("saveOperateLog", data);
}

let editingVoucher: VoucherModel;
const carryOverVoucherTime = ref(0)
provide("carryOverVoucherTime", carryOverVoucherTime);
async function editVoucher(voucherModel: VoucherModel) {
    saveEditVoucherLogBefore(voucherModel);

    carryOverVoucherTime.value = 1500;
    currentSlot.value = "voucherView";
    const queryParams = new DataVoucherQueryParams(JSON.parse(JSON.stringify(voucherModel.voucherLines)));
    queryParams.vgId = voucherModel.vgId;
    queryParams.vdate = voucherModel.vdate;
    queryParams.vnum = voucherModel.vnum;
    queryParams.note = voucherModel.note;
    queryParams.attachments = voucherModel.attachments;
    queryParams.attachFileIds = voucherModel.attachFileIds;
    queryParams.tempAsubList = genVoucherModel.value
        ? genVoucherModel.value.temporaryAccountSubjectList.map((item: any) => {
              return {
                  asubCode: item.asubCode,
                  asubName: item.asubName,
              };
          })
        : [];
    if (voucherModel.attachFileIds) {
        queryParams.attachFiles = await getVoucherAttachFileModelList(voucherModel.attachFileIds || "");
    }
    if (props.queryParams instanceof BillQueryParameters) {
        if (genVoucherModel.value) {
            for (let aatype in genVoucherModel.value.temporaryAssitEntryList) {
                queryParams.tempAAEList = queryParams.tempAAEList.concat(
                    genVoucherModel.value.temporaryAssitEntryList[aatype].map((item: any) => {
                        return {
                            aatype: item.aatype,
                            num: item.aanum,
                            name: item.aaname,
                        };
                    })
                );
            }
        }
    } else {
        queryParams.tempAAEList = genVoucherModel.value
            ? genVoucherModel.value.temporaryAssitEntryList.map((item: any) => {
                  return {
                      aatype: item.aatype,
                      num: item.aanum,
                      name: item.aaname,
                  };
              })
            : [];
    }
    voucherQueryParams.value = queryParams;
    editingVoucher = voucherModel;
}

let isSaving = false;
function saveVoucher() {
    if (isSaving) return;
    isSaving = true;
    const saveParams = new VoucherSaveParams(0, (res: IResponseModel<VoucherSaveModel>, savingVoucherLines: Array<VoucherEntryModel>) => {
        isSaving = false;
        if (res.state === 1000) {
            currentSlot.value = "generate";
            useFullScreenStore().changeFullScreenStage(false);
            const voucherModel = voucher.value?.getVoucherModel();
            if (voucherModel) {
                editingVoucher.vgId = voucherModel.vgId;
                editingVoucher.vgName = voucherModel.vgName;
                editingVoucher.vdate = voucherModel.vdate;
                editingVoucher.vnum = voucherModel.vnum;
                editingVoucher.attachments = voucherModel.attachments;
                editingVoucher.note = voucherModel.note;
                editingVoucher.voucherLines = savingVoucherLines;
                editingVoucher.attachFileIds = voucherModel.attachFileIds;
                editingVoucher.attachFiles = voucherModel.attachFiles;
                let target = voucherDoucmentRelation.value?.find((i) => i.voucher === editingVoucher);
                target?.documentList.forEach((d) => {
                    d.document.errorInfo = "";
                    d.document.warnInfo = checkExpensesCreditAccount(target.voucher.voucherLines);
                    if (!props.queryParams.isMerge) {
                        let tempDocument = genVoucherModel.value?.documentWithVoucherList.find((item) => item.document === d.document);
                        if (tempDocument) {
                            tempDocument.voucherLines = savingVoucherLines;
                        }
                    }
                });
                reloadVisibleData();
                emit("voucherChanged");
                saveEditVoucherLogAfter(editingVoucher);
            }
        } else if (res.state === 2000) {
            if (res.subState !== -1) {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        } else if (res.state === 9999) {
            ElNotify({
                message: "保存失败",
                type: "warning",
            });
        }
    });
    saveParams.dotSave = true;
    voucher.value?.saveVoucher(saveParams);
}


function cancelEditVoucher() {
    currentSlot.value = "generate";
    useFullScreenStore().changeFullScreenStage(false);
    voucher.value?.removeEventListener();
}

const zoomState = ref<"in" | "out">("out");
function voucherZoomChange(state: "in" | "out") {
    zoomState.value = state;
}

function deleteVoucher(voucherModel: VoucherModel) {
    const relationDocumentList = voucherDoucmentRelation.value?.find((i) => i.voucher === voucherModel)?.documentList;
    if (relationDocumentList) {
        relationDocumentList.forEach((d) => {
            const index = documentList.value.indexOf(d);
            documentList.value.splice(index, 1);
            genVoucherModel.value?.documentWithVoucherList.splice(index, 1);
        });
    }
    const index = voucherList.value.indexOf(voucherModel);
    let delVoucher = voucherList.value[index];
    voucherList.value.splice(index, 1);
    let minNum = delVoucher.vnum;
    for (let i = index; i < voucherList.value.length; i++) {
        let tempNum = voucherList.value[i].vnum;
        voucherList.value[i].vnum = minNum; // 将 vnum 修改为新的值 newValue
        minNum = tempNum; // 将原来的 vnum 赋值给 minNum
    }
    reloadVisibleData();
}

function getGenVoucherParameters(): GenVoucherParameters<BaseDocumentModel> {
    if (props.queryParams instanceof InvoiceQueryParameters) {
        return new InvoiceGenVoucherParameters(
            voucherList.value.map((v) => {
                const relationDocumentList =
                    voucherDoucmentRelation.value?.find((i) => i.voucher === v)?.documentList?.map((d) => d.document) || [];
                const voucher = new GenVoucherModel();
                voucher.vnum = v.vnum;
                voucher.attachments = v.attachments || 0;
                voucher.note = v.note;
                voucher.vtype = v.vtype;
                voucher.vgId = v.vgId;
                voucher.vgName = v.vgName;
                voucher.vdate = v.vdate;
                voucher.isError = relationDocumentList.filter((d) => !!d.errorInfo).length > 0;
                voucher.voucherLines = v.voucherLines;
                voucher.attachFileIds = v.attachFileIds;
                return new GenVoucherWithDocumentModel<InvoiceDocumentModel>(
                    voucher,
                    relationDocumentList.map((d) => d as InvoiceDocumentModel)
                );
            }),
            genVoucherModel.value?.temporaryAccountSubjectList,
            genVoucherModel.value?.temporaryAssitEntryList
        );
    }
    if (props.queryParams instanceof JournalQueryParameters) {
        return new JournalGenVoucherParameters(
            voucherList.value.map((v) => {
                const relationDocumentList =
                    voucherDoucmentRelation.value?.find((i) => i.voucher === v)?.documentList?.map((d) => d.document) || [];
                const voucher = new GenVoucherModel();
                voucher.vnum = v.vnum;
                voucher.attachments = v.attachments || 0;
                voucher.note = v.note;
                voucher.vtype = v.vtype;
                voucher.vgId = v.vgId;
                voucher.vgName = v.vgName;
                voucher.vdate = v.vdate;
                voucher.isError = relationDocumentList.filter((d) => !!d.errorInfo).length > 0;
                voucher.voucherLines = v.voucherLines;
                voucher.attachFileIds = v.attachFileIds;
                return new GenVoucherWithDocumentModel<JournalDocumentModel>(
                    voucher,
                    relationDocumentList.map((d) => d as JournalDocumentModel)
                );
            }),
            genVoucherModel.value?.temporaryAccountSubjectList,
            genVoucherModel.value?.temporaryAssitEntryList
        );
    }
    if (props.queryParams instanceof TransferQueryParameters) {
        return new TransferGenVoucherParameters(
            voucherList.value.map((v) => {
                const relationDocumentList =
                voucherDoucmentRelation.value?.find((i) => i.voucher === v)?.documentList?.map((d) => d.document) || []
                const voucher = new GenVoucherModel();
                voucher.vnum = v.vnum;
                voucher.attachments = v.attachments || 0;
                voucher.note = v.note;
                voucher.vtype = v.vtype;
                voucher.vgId = v.vgId;
                voucher.vgName = v.vgName;
                voucher.vdate = v.vdate;
                voucher.isError = relationDocumentList.filter((d) => !!d.errorInfo).length > 0;
                voucher.voucherLines = v.voucherLines;
                voucher.attachFileIds = v.attachFileIds;
                return new GenVoucherWithDocumentModel<TransferDocumentModel>(
                    voucher,
                    relationDocumentList.map((d) => d as TransferDocumentModel)
                );
            }),
            genVoucherModel.value?.temporaryAccountSubjectList,
            genVoucherModel.value?.temporaryAssitEntryList
        );
    }
    if (props.queryParams instanceof DraftQueryParameters) {
        return new DraftGenVoucherParameters(
            voucherList.value.map((v) => {
                const relationDocumentList =
                    voucherDoucmentRelation.value?.find((i) => i.voucher === v)?.documentList?.map((d) => d.document) || [];
                const voucher = new GenVoucherModel();
                voucher.vnum = v.vnum;
                voucher.attachments = v.attachments || 0;
                voucher.note = v.note;
                voucher.vtype = v.vtype;
                voucher.vgId = v.vgId;
                voucher.vgName = v.vgName;
                voucher.vdate = v.vdate;
                voucher.isError = relationDocumentList.filter((d) => !!d.errorInfo).length > 0;
                voucher.voucherLines = v.voucherLines;
                voucher.attachFileIds = v.attachFileIds;
                return new GenVoucherWithDocumentModel<DraftDocumentModel>(
                    voucher,
                    relationDocumentList.map((d) => d as DraftDocumentModel)
                );
            }),
            genVoucherModel.value?.temporaryAccountSubjectList,
            genVoucherModel.value?.temporaryAssitEntryList
        );
    }
    if (props.queryParams instanceof BillQueryParameters) {
        return new BillGenVoucherParameters(
            voucherList.value.map((v) => {
                const relationDocumentList =
                    voucherDoucmentRelation.value?.find((i) => i.voucher === v)?.documentList?.map((d) => d.document) || [];
                const voucher = new GenVoucherModel();
                voucher.vnum = v.vnum;
                voucher.attachments = v.attachments || 0;
                voucher.note = v.note;
                voucher.vtype = v.vtype;
                voucher.vgId = v.vgId;
                voucher.vgName = v.vgName;
                voucher.vdate = v.vdate;
                voucher.isError = relationDocumentList.filter((d) => !!d.errorInfo).length > 0;
                voucher.voucherLines = v.voucherLines;
                voucher.attachFileIds = v.attachFileIds;
                return new GenVoucherWithDocumentModel<BillDocumentModel>(
                    voucher,
                    relationDocumentList.map((d) => d as BillDocumentModel)
                );
            }),
            genVoucherModel.value?.temporaryAccountSubjectList,
            genVoucherModel.value?.temporaryAssitEntryList
        );
    }
    if (props.queryParams instanceof ExpenseBillQueryParameters) {
        return new ExpenseBillGenVoucherParameters(
            voucherList.value.map((v) => {
                const relationDocumentList =
                    voucherDoucmentRelation.value?.find((i) => i.voucher === v)?.documentList?.map((d) => d.document) || [];
                const voucher = new GenVoucherModel();
                voucher.vnum = v.vnum;
                voucher.attachments = v.attachments || 0;
                voucher.note = v.note;
                voucher.vtype = v.vtype;
                voucher.vgId = v.vgId;
                voucher.vgName = v.vgName;
                voucher.vdate = v.vdate;
                voucher.isError = relationDocumentList.filter((d) => !!d.errorInfo).length > 0;
                voucher.voucherLines = v.voucherLines;
                voucher.attachFileIds = v.attachFileIds;
                return new GenVoucherWithDocumentModel<ExpenseBillDocumentModel>(
                    voucher,
                    relationDocumentList.map((d) => d as ExpenseBillDocumentModel)
                );
            }),
            genVoucherModel.value?.temporaryAccountSubjectList,
            genVoucherModel.value?.temporaryAssitEntryList
        );
    }
    return new GenVoucherParameters<BaseDocumentModel>([], null, null);
}

function loadSaveResult(result: IGenVoucherResult<BaseDocumentModel>) {
    currentSlot.value = "resultView";
    if (props.queryParams instanceof InvoiceQueryParameters) {
        const data = result as IInvocieGenVoucherResult;
        genVoucherResult.value = data;
    }
    if (props.queryParams instanceof JournalQueryParameters) {
        const data = result as IJournalGenVoucherResult;
        genVoucherResult.value = data;
    }
    if (props.queryParams instanceof TransferQueryParameters) {
        const data = result as ITransferGenVoucherResult;
        genVoucherResult.value = data;
    }
    if (props.queryParams instanceof DraftQueryParameters) {
        const data = result as IDraftGenVoucherResult;
        genVoucherResult.value = data;
    }
    if (props.queryParams instanceof BillQueryParameters) {
        const data = result as IBillGenVoucherResult;
        genVoucherResult.value = data;
    }
    if (props.queryParams instanceof ExpenseBillQueryParameters) {
        const data = result as IExpenseBillGenVoucherResult;
        genVoucherResult.value = data;
    }
}

function back() {
    currentSlot.value = "generate";
    emit("back");
}

function checkMergeVoucher(){
    if(documentList.value?.length > 0){
        for(let i = 0; i < documentList.value.length; i++){
            if(documentList.value[i].document.errorInfo){
                ElNotify({ message: "亲，生成的凭证存在错误暂不支持合并，请依据错误提示修改凭证后再进行合并哦！", type: "warning" });
                return false;
            }
        }
    }

    return true;
}

defineExpose({
    loadDocumentList,
    getGenVoucherParameters,
    loadSaveResult,
    checkMergeVoucher,
    checkIsEdited,
});
</script>
