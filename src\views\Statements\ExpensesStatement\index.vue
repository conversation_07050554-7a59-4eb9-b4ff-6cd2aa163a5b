<template>
    <div class="content">
        <div class="title">费用明细表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <div class="main-tool-left" @mouseenter="closePopover">
                        <SearchInfoContainer ref="containerRef">
                            <template v-slot:title>{{ currentPeriodInfo }}</template>
                            <div class="line-item first-item input">
                                <div class="line-item-title">会计期间：</div>
                                <div class="line-item-field">
                                    <PeriodPicker
                                        v-model:startPid="searchInfo.PeriodStart"
                                        v-model:endPid="searchInfo.PeriodEnd"
                                        v-model:periodInfo="periodInfo"
                                    />
                                </div>
                            </div>
                            <div class="line-item input">
                                <div class="line-item-title">科目：</div>
                                <div class="line-item-field">
                                    <MultipleSubjectPicker
                                        ref="asubRef"
                                        v-model:selectedList="searchInfo.SubjectIds"
                                        :filterByExpenses="true"
                                        @searchListLengthChange="handleSearchListEmpty"
                                    ></MultipleSubjectPicker>
                                </div>
                            </div>
                            <div class="subject-tips">例：{{ subjectTips }}</div>
                            <div class="line-item input">
                                <div class="line-item-title">科目级别：</div>
                                <div class="line-item-field">
                                    <el-input-number
                                        v-model="searchInfo.SubjectLevelStart"
                                        :min="1"
                                        :max="maxCodelength"
                                        controls-position="right"
                                        style="width: 132px"
                                    ></el-input-number>
                                    <div class="ml-10 mr-10">至</div>
                                    <el-input-number
                                        v-model="searchInfo.SubjectLevelEnd"
                                        :min="1"
                                        :max="maxCodelength"
                                        controls-position="right"
                                        style="width: 132px"
                                    ></el-input-number>
                                </div>
                            </div>
                            <div class="line-item single">
                                <div class="line-item-title">
                                    <el-checkbox v-model="searchInfo.IsAssist" label="显示辅助核算"></el-checkbox>
                                </div>
                            </div>
                            <div class="buttons">
                                <a class="button solid-button" @click="handleSearch">确定</a>
                                <a class="button" @click="handleClose">取消</a>
                                <a class="button" @click="handleReset">重置</a>
                            </div>
                        </SearchInfoContainer>
                        <!-- <ErpRefreshButton></ErpRefreshButton> -->
                        <div class="ml-20">
                            <el-checkbox v-model="expandAll" :disabled="showDataMask" label="展开所有级次"></el-checkbox>
                        </div>
                    </div>
                </div>
                <div class="main-tool-right">
                    <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['expensestatement-canprint']">
                        <li @click="handlePrint(0,getSearchParams())">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button" @click="handleExport" v-permission="['expensestatement-canexport']">导出</a>
                </div>
            </div>
            <div
                :class="['main-center', { erp: isErp }, { showDataMask: showDataMask }]"
                v-loading="loading"
                element-loading-text="正在加载数据..."
            >
                <Table
                    row-key="code"
                    :columns="columns"
                    border
                    stripe
                    show-overflow-tooltip
                    :class="isErp ? 'erp-table' : ''"
                    :data="tableData"
                    empty-text="暂无数据"
                    :expand-row-keys="expandRowKeys"
                    :tooltip-options="{ effect: 'light', placement: 'right-start', offset: -10 }"
                    :scrollbarShow="true"
                    :tableName="setModule"
                >
                    <template #name>
                        <el-table-column 
                            label="科目名称" 
                            min-width="90" 
                            fixed="left"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span :style="{ 'padding-left': (scope.row.asubLevel - 1) * 14 + 'px' }"> {{ `${scope.row.name}` }}</span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
                <DataMask v-if="showDataMask" ref="dataMaskRef" :showLines="5" :hasPage="false" />
            </div>
        </div>
    </div>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="费用明细表打印"
        :customNum="6"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getSearchParams())"
    ></StatementsPrint>
</template>
<script lang="ts">
export default {
    name: "ExpensesStatement",
};
</script>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import DataMask from "@/components/DataMask/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams, globalFormPost, globalPrint } from "@/util/url";
import { formatMoney } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import usePrint from "@/hooks/usePrint";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import MultipleSubjectPicker from "./components/MultipleSubjectPicker.vue";
import type { IAsubCodeLength, ITableData, ISearchInfo, IExpensesStatement } from "./types";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "ExpensesSheet";
const isErp = ref(window.isErp);

const trialStatusStore = useTrialStatusStore();
const columns = ref<Array<IColumnProps>>([
    { prop: "code", label: "科目编码", minWidth: 90, fixed: "left", width: getColumnWidth(setModule, 'code') },
    { slot: "name", label: "科目名称", minWidth: 90, fixed: "left" },
]);
// 是否过期
const isExpired = computed(() => {
    return trialStatusStore.isExpired;
});
// 是否过期遮罩
const showDataMask = computed(() => isExpired.value && dataLines.value > 5);

const periodStore = useAccountPeriodStore();

// 会计准则
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;

// 不同准则提示语
const subjectTips = ref<string>();
// 不同准则默认科目
const subjectIds = ref<string[]>([]);

// 根据账套准则设置变量
const filterByAccountStandard = () => {
    switch (accountStandard) {
        case 1:
            // 小企业
            subjectTips.value = "5001,5051,5601-5603";
            subjectIds.value = ["5601", "5602", "5603"];
            break;
        case 2:
            // 企业
            subjectTips.value = "6601,6602,6601-6603";
            subjectIds.value = ["6601", "6602", "6603"];
            break;
        case 3:
            // 民非
            subjectTips.value = "5101,5202-5401";
            subjectIds.value = ["5201", "5301", "5401"];
            break;
        case 4:
            // 农合
            subjectTips.value = "501,502,522-529";
            subjectIds.value = ["522"];
            break;
        case 5:
            // 新农合
            subjectTips.value = "501,502,522-529";
            subjectIds.value = ["523", "524"];
            break;
        default:
            // 默认企业
            subjectTips.value = "6601,6602,6601-6603";
            subjectIds.value = ["6601", "6602", "6603"];
    }
};
filterByAccountStandard();

const periodInfo = ref("");
const currentPeriodInfo = ref("");
const loading = ref(false);
const expandAll = ref(true);

const searchInfo = reactive<ISearchInfo>({
    PeriodStart: Number(periodStore.getPeriodRange().start),
    PeriodEnd: Number(periodStore.getPeriodRange().end),
    SubjectLevelStart: 1,
    SubjectLevelEnd: 4,
    SubjectIds: JSON.parse(localStorage.getItem(`expenses-subject-${accountStandard}`) || `[]`),
    /** 显示辅助核算 */
    IsAssist: false,
});

const dataMaskRef = ref<InstanceType<typeof DataMask>>();
const tableMaxHeight = ref("");
const tableData = ref<ITableData[]>([]);
const dataColumns = ref<{ title: string }[]>([]);
// 总计
const total = ref<ITableData>();
// 展开行列表
const expandRowKeys = ref<any>([]);

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const asubRef = ref<InstanceType<typeof MultipleSubjectPicker>>();

// 检测参数 type 1搜索，2打印，3导出
const vertifyParams = (type = 1) => {
    // 免费版过期数据遮罩
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    if (searchInfo.PeriodStart > searchInfo.PeriodEnd) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.SubjectLevelStart > searchInfo.SubjectLevelEnd) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    // 搜索科目为空时
    if (type === 1 && searchSubjectNotify.value) {
        ElNotify({
            message: "科目有误，仅支持损益类科目查询",
            type: "warning",
        });
        return false;
    }
    if ((type === 2 || type === 3) && tableData.value.length === 0) {
        ElNotify({
            message: `亲，当前没有数据可${type === 2 ? "打印" : "导出"}哦`,
            type: "warning",
        });
        return false;
    }
    return true;
};
const handleZoomChangeList = () => {
    tableMaxHeight.value = handleZoomChange();
};

function handleZoomChange() {
    const zoomLevel = window.devicePixelRatio;
    const zoomLevelNum = Number(zoomLevel).toFixed(2);
    let tableMaxHeight = "730px";
    switch (zoomLevelNum) {
        case "1.00":
            tableMaxHeight = "730px";
            break;
        case "1.10":
            tableMaxHeight = "630px";
            break;
        case "1.25":
            tableMaxHeight = "530px";
            break;
        case "1.50":
            tableMaxHeight = "404px";
            break;
    }
    return tableMaxHeight;
}

const handleSearch = () => {
    setTimeout(() => {
        if (!vertifyParams(1)) {
            return;
        }
        localStorage.setItem(`expenses-subject-${accountStandard}`, JSON.stringify(searchInfo.SubjectIds));
        periodStore.changePeriods(String(searchInfo.PeriodStart), String(searchInfo.PeriodEnd));
        handleClose();
        getTableData();
    }, 0);
};
const getTableData = () => {
    loading.value = true;
    const params = {
        PeriodStart: searchInfo.PeriodStart,
        PeriodEnd: searchInfo.PeriodEnd,
        SubjectLevelStart: searchInfo.SubjectLevelStart,
        SubjectLevelEnd: searchInfo.SubjectLevelEnd,
        SubjectIds: searchInfo.SubjectIds.join(","),
        IsAssist: searchInfo.IsAssist,
        appasid: getGlobalToken(),
    };
    request({
        url: "/api/ExpenseStatement/GetList",
        method: "post",
        data: params,
    })
        .then((res: IResponseModel<IExpensesStatement>) => {
            if (res.state === 1000 && res.data) {
                dataColumns.value = res.data.titles;
                columns.value = [
                    { prop: "code", label: "科目编码", minWidth: 90, fixed: "left", width: getColumnWidth(setModule, 'code') },
                    { slot: "name", label: "科目名称", minWidth: 90, fixed: "left" },
                ];
                dataColumns.value.forEach((item, index) => {
                    columns.value.push({
                        prop: `expensePeriods[${index}]`,
                        label: item.title,
                        minWidth: 105,
                        headerAlign: "center",
                        align: "right",
                        formatter: (row, column, value) => formatMoney(value),
                        width: getColumnWidth(setModule, `expensePeriods[${index}]`),
                        resizable: index !== dataColumns.value.length - 1
                    });
                });
                tableData.value = res.data.subjectExpenses;
                total.value = res.data.total;
                // 总计加在数据最后
                if (tableData.value.length > 0) {
                    // 有数据的时候加上总计
                    tableData.value.push(total.value);
                }
                // 重置再添加
                dataLines.value = 0;
                expandRowKeys.value = [];
                if (expandAll.value) {
                    setExpandRowKeys(tableData.value);
                }
            } else {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
                // 重置
                dataColumns.value = [];
                tableData.value = [];
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
            currentPeriodInfo.value = periodInfo.value;
            nextTick(() => {
                if (showDataMask.value) {
                    dataMaskRef.value?.getTableHeight();
                }
            });
        });
};
const handleClose = () => {
    containerRef.value?.handleClose();
};
const handleReset = () => {
    searchInfo.PeriodStart = Number(periodStore.getPeriodRange().start);
    searchInfo.PeriodEnd = Number(periodStore.getPeriodRange().end);
    searchInfo.SubjectLevelStart = 1;
    searchInfo.SubjectLevelEnd = 4;
    searchInfo.SubjectIds = JSON.parse(localStorage.getItem(`expenses-subject-${accountStandard}`) || `[]`);
    searchInfo.IsAssist = false;
};

const closePopover = () => {
    if (!containerRef.value?.popoverShow) {
        asubRef.value?.handleClose();
    }
};

// 科目级别
const codeLengthStr = ref("");
const maxCodelength = ref<number>(4);
function getAsubCodeLength() {
    return request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        codeLengthStr.value = res.data.codeLength.join("");
        const codeLengthList: number[] = res.data.codeLength;
        const codeLength = codeLengthList.length;
        maxCodelength.value = codeLength;
        // 默认最大级次
        searchInfo.SubjectLevelEnd = codeLength;
    });
}

function getSearchParams() {
    return {
        periodStart: searchInfo.PeriodStart,
        periodEnd: searchInfo.PeriodEnd,
        subjectLevelStart: searchInfo.SubjectLevelStart,
        subjectLevelEnd: searchInfo.SubjectLevelEnd,
        subjectIds: searchInfo.SubjectIds.join(","),
        isAssist: searchInfo.IsAssist,
    };
}

function validatePrint() {
    if (!vertifyParams(2)) {
        return false;
    }
    return true;
}
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "expenseStatement",
    `/api/ExpenseStatement/Print`,
    {},
    true,
    false,
    validatePrint
);
const handleExport = () => {
    if (!vertifyParams(3)) {
        return;
    }
    globalFormPost(`/api/ExpenseStatement/Export`,getSearchParams(),"export");
};

// 获取表格数据行数
const dataLines = ref(0);
// 展开所有
const setExpandRowKeys = (arr: ITableData[]) => {
    if (!arr || arr.length === 0) return;
    for (let i of arr) {
        dataLines.value++;
        if (i.hasChild) {
            expandRowKeys.value.push(i.code);
            if (i.children && i.children.length > 0) {
                setExpandRowKeys(i.children);
            }
        } else {
            continue;
        }
    }
};

// 判断搜索科目是否损益科目
const searchSubjectNotify = ref(false);
const handleSearchListEmpty = (show: boolean) => {
    searchSubjectNotify.value = show;
    if (show) {
        ElNotify({
            message: "科目有误，仅支持损益类科目查询",
            type: "warning",
        });
    }
};

watch(
    () => expandAll.value,
    (val) => {
        // 重置再添加
        expandRowKeys.value = [];
        if (val) {
            setExpandRowKeys(tableData.value);
        }
    }
);

onMounted(async () => {
    handleZoomChangeList();
    window.addEventListener("resize", handleZoomChangeList);
    await getAsubCodeLength();
    searchInfo.PeriodStart = Number(periodStore.getPeriodRange().start);
    searchInfo.PeriodEnd = Number(periodStore.getPeriodRange().end);
    // 第一次请求
    getTableData();
    document.body.scrollTop = 0;
    if (searchInfo.SubjectIds.length === 0) {
        // 科目赋初始值
        searchInfo.SubjectIds = subjectIds.value;
    }
});
</script>
<style lang="less" scoped>
.content {
    width: 100%;
    .main-content {
        width: 100%;
        .main-center {
            position: relative;
            :deep(.el-table) {
                .el-popper.is-light {
                    max-width: 300px;
                    text-align: left;
                }
                tbody {
                    .el-table__row {
                        .el-table__cell {
                            background-color: #fff;
                        }
                        &.current-row {
                            .el-table__cell {
                                background-color: var(--table-selected-hover-color);
                            }
                        }
                        &.hover-row {
                            .el-table__cell {
                                background-color: var(--table-selected-hover-color);
                            }
                        }
                    }
                }
            }
            &.showDataMask {
                :deep(.el-table) {
                    .el-scrollbar__view {
                        min-height: 0;
                        height: 575px;
                        overflow: hidden;
                    }
                    // 底部滚动条样式
                    .el-scrollbar__bar {
                        &.is-horizontal {
                            height: 8px;
                            bottom: 1px;
                            background-color: #fff;
                            .el-scrollbar__thumb {
                                &:hover {
                                    filter: brightness(1.2);
                                    opacity: 1;
                                }
                            }
                        }
                    }
                    // 禁用展开收起
                    .el-table__expand-icon {
                        display: none;
                    }
                }
            }
            .select-info-inner {
                font-size: 12px;
            }
        }
        .subject-tips {
            color: #c5c5c5;
            text-align: left;
            padding-left: 120px;
            margin-top: 5px;
        }
    }
}
:deep(.el-select-v2) {
    .el-select-v2__tags-text {
        max-width: 135px !important;
    }
}
:deep(.erp-table.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
    background-color: #fff !important;
}
</style>
