.edit-content {
    width: 1100px !important;
    // text-align: center;
    & .main-center {
        width: 1100px;
        display: block;
        margin: 0 auto;
        background-color: white;
        & .subtitle {
            font-size: var(--h3);
            line-height: 22px;
            color: var(--font-color);
            padding: 20px 40px;
            text-align: left;
        }
        & .form-table tr td {
            padding-bottom: 10px;
        }
        & .form-table tr td.td-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            text-align: right;
            &:nth-child(1) {
                width: 152px;
            }
            &:nth-child(3) {
                width: 174px;
            }
            &:nth-child(5) {
                width: 174px;
            }
        }
        & .form-table tr td.td-field input[type="text"],
        .content .main-center .form-table tr td.td-field input[type="password"],
        .content .main-center .form-table tr td.td-field input[type="number"] {
            width: 180px;
            height: 32px;
            border: 1px solid var(--input-border-color);
            outline: none;
            padding: 0;
            padding-left: 10px;
            padding-right: 10px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            box-sizing: border-box;
            border-radius: var(--input-border-radius);
        }
        
        & .form-line {
            padding-left: 60px;
            display: flex;
            align-items: center;
            & .form-field {
                display: flex;
                align-items: center;
            }
        }
        .ss-table-container {
            & :deep(.table) {
                width: 980px;
                padding: 20px 60px 10px 60px;
            }
            & :deep(.el-input__inner) {
                border: 0;
                text-align: right;
            }
        }

        & .buttons {
            display: flex;
            justify-content: center;
            padding: 20px 0 40px;
        }
    }
}