import { ElNotify } from "@/util/notify";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import { CashAAType } from "@/views/Cashier/CashOrDepositJournal/types";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import type { ISCurrcy } from "./types";

const setModule = "CashReportCombine";
export const judgeDate = (yearInfo: string | number, monthInfo: string | number) => {
    const year = Number(yearInfo);
    const month = Number(monthInfo);
    const date = getDaysInMonth(year, month);
    return {
        start: year + "-" + (month + "").padStart(2, "0") + "-01",
        end: year + "-" + (month + "").padStart(2, "0") + "-" + date,
    };
};

export const getText = (str: string) => {
    const regExp = />([^<]*)<\/a>/;
    const match = regExp.exec(str);
    return match ? match[1] : "";
};

export const checkReportTableEmpty = (length: number, minLength: number) => {
    if (length < minLength) {
        ElNotify({ type: "warning", message: "报表数据为空" });
        return false;
    }
    return true;
};

type QueryParam = { [key: string]: string };

export const getATagParams = (href: string): QueryParam => {
    if (href.includes('">')) href = href.replace('">', "'>");
    if (href.includes("')")) href = href.replace("')", "");
    const length = href.split("?").length;
    const queryParams = href.split("?")[length - 1]?.split("'>")[0].split("&");
    const params: QueryParam = {};
    queryParams?.forEach((queryParam) => {
        const [key, value] = queryParam.split("=");
        params[key] = value?.replace('")', "") ?? "";
    });
    return params;
};

//取特殊符号
export const getATagEspecialParams = (href: string) => {
    const aaValueRegex = /AA_Value=([^&]*(?:&(?!Date_s=|CD_IDS=|AA_Type=|fcid=|showDisabled=)[^&]*)*)/;
    const match = href.match(aaValueRegex);
    console.log("match", match);
    return match ? match[1] : "";
};

export const combineTypes = [
    { label: "项目", value: CashAAType.Project },
    { label: "部门", value: CashAAType.Department },
    { label: "往来单位", value: CashAAType.Unit },
    { label: "收支类别", value: CashAAType.IEType },
];

const defaultAfterColumns: Array<IColumnProps> = [
    { slot: "income" },
    { slot: "expenditure" },
    { 
        label: "收入笔数",
        prop: "incomeCount", 
        align: "left", 
        headerAlign: "left", 
        minWidth: 131,
        width: getColumnWidth(setModule, "incomeCount")
    },
    { 
        label: "支出笔数", 
        prop: "expendCount", 
        align: "left", 
        headerAlign: "left", 
        minWidth: 131,
        width: getColumnWidth(setModule, "expendCount")
    },
    { slot: "operate" },
];

function getDynamicColumns(
    type: CashAAType,
    isMainItem: boolean,
    erpColumnsType: CashAAType.Costumer | CashAAType.Vendor | CashAAType.Employee = CashAAType.Costumer
): Array<IColumnProps> {
    let columns: Array<IColumnProps> = [];
    const propCode = isMainItem ? "code" : "second_code";
    const propName = isMainItem ? "name" : "second_name";
    if (type === CashAAType.Project || type === CashAAType.Department) {
        const name = combineTypes.find((i) => i.value === type)!.label;
        columns = [
            { 
                label: name + "编码", 
                prop: propCode, 
                align: "left", 
                headerAlign: "left", 
                minWidth: 106,
                width: getColumnWidth(setModule, propCode) 
            },
            {
                label: name,
                prop: propName,
                align: "left",
                headerAlign: "left",
                minWidth: 178,
                formatter(row, column, value) {
                    return value.replace(/<[^<>]+>/g, "");
                },
                width: getColumnWidth(setModule, propName) 
            },
        ];
    } else if (type === CashAAType.IEType) {
        columns = [
            { 
                label: "收支类别编码", 
                prop: propCode, 
                align: "left", 
                headerAlign: "left", 
                minWidth: 106,
                width: getColumnWidth(setModule, propCode) 
            },
            {
                label: "收支类别",
                prop: propName,
                align: "left",
                headerAlign: "left",
                minWidth: 178,
                formatter(row, column, value) {
                    return value.replace(/<[^<>]+>/g, "");
                },
                width: getColumnWidth(setModule, propName)
            },
        ];
    } else if (type === CashAAType.Unit) {
        if (!window.isErp) {
            columns.push({
                label: "往来单位",
                prop: propName,
                align: "left",
                headerAlign: "left",
                minWidth: 178,
                formatter(row, column, value) {
                    return value.replace(/<[^<>]+>/g, "");
                },
                width: getColumnWidth(setModule, propName)
            });
        } else {
            const columnName = erpColumnsType === CashAAType.Costumer ? "客户" : erpColumnsType === CashAAType.Vendor ? "供应商" : "职员";
            columns = [
                { 
                    label: columnName + "编码", 
                    prop: propCode, 
                    align: "left", 
                    headerAlign: "left", 
                    minWidth: 106,
                    width: getColumnWidth(setModule, propCode)
                },
                {
                    label: columnName,
                    prop: propName,
                    align: "left",
                    headerAlign: "left",
                    minWidth: 178,
                    formatter(row, column, value) {
                        return value.replace(/<[^<>]+>/g, "");
                    },
                    width: getColumnWidth(setModule, propName)
                },
            ];
        }
    }
    return columns;
}

export function getColumns(mainItem: CashAAType, subItem: CashAAType, fcid: number = -1) {
    let columns:IColumnProps[] = JSON.parse(JSON.stringify(defaultAfterColumns));
    if (fcid > 1) {
        columns.unshift({ slot: "fcName" });
        columns.forEach((item) => {
            if (item.slot === "income" || item.slot === "expenditure") {
                item.slot += "Fc";
            }
        });
    } else {
        columns = JSON.parse(JSON.stringify(defaultAfterColumns))
    }
    const hasUnit = mainItem === CashAAType.Unit || subItem === CashAAType.Unit;
    const defaultColumns = [...getDynamicColumns(mainItem, true), ...getDynamicColumns(subItem, false), ...columns];
    if (!window.isErp || !hasUnit) return [defaultColumns, [], []];
    const secondColumns = [
        ...getDynamicColumns(mainItem, true, CashAAType.Vendor),
        ...getDynamicColumns(subItem, false, CashAAType.Vendor),
        ...columns,
    ];
    const thirdColumns = [
        ...getDynamicColumns(mainItem, true, CashAAType.Employee),
        ...getDynamicColumns(subItem, false, CashAAType.Employee),
        ...columns,
    ];
    return [defaultColumns, secondColumns, thirdColumns];
}

export function formatCombineParams(type: CashAAType) {
    switch (type) {
        case CashAAType.Project:
            return "PROJECT";
        case CashAAType.Department:
            return "DEPARTMENT";
        case CashAAType.Unit:
            return "OPPOSITE_PARTY";
        case CashAAType.IEType:
            return "IE_TYPE";
        default:
            return "";
    }
}

export function formatCombineUrlParams(type: string) {
    switch (type) {
        case "PROJECT":
            return CashAAType.Project;
        case "DEPARTMENT":
            return CashAAType.Department;
        case "OPPOSITE_PARTY":
            return CashAAType.Unit;
        case "IE_TYPE":
            return CashAAType.IEType;
        default:
            return 0;
    }
}

export function getSearchInfoCD(selectedList: number[], options:IBankAccountItem[]) {
    return selectedList.length === options.length ? "ALL" : selectedList.join(",");
}

export function getSearchParams(searchInfo: any, label: string, showDisabled?: boolean) {
    return {
        date_s: searchInfo.startPid,
        date_e: searchInfo.endPid,
        fcid: label === "reportAccount" ? -1 : searchInfo.fcid,
        showDisableAccount: label === "reportAccount" ? showDisabled : getShowDisabledAccount("cashReport"),
        cdAccIds: label === "reportAccount" ? "all" : searchInfo.CD_ACC_IDS,
    };
}

export function getFcCode(id: string, fcid: number, data: ISCurrcy[]) {
    if (!id) return "";
    if (id.includes("合计")) return "";
    return data.find((item) => item.id === fcid)?.label || "";
}