.sub-title {
    color: #333333;
    line-height: 22px;
    font-size: 16px;
    padding: 20px 40px 0;
    text-align: left;
 }
.line {
    margin-top: 30px;
}
.item-container {
    display: flex;
    padding-left: 110px;
    & .item-title {
        color: #333333;
        line-height: 32px;
        font-size: 14px;
        margin-top: 20px;
        flex-shrink: 0;
    }
    .items {
        display: flex;
        flex-wrap: wrap;
        :deep(.item-box) {
            display: flex;
            align-items: center;
            margin-right: 30px;
            margin-top: 20px;
            &:hover{
                & input{
                    border: 1px solid --var(main-color);
                    &+div{
                        display:inline-block
                    }
                }
            }
            & input {
                width: 130px;
                height: 32px;
                border: 1px solid #dadada;
                outline: none;
                padding: 0;
                padding-left: 10px;
                padding-right: 10px;
                color: #333333;
                font-size: 14px;
                line-height: 32px;
                box-sizing: border-box;
                border-radius: 0px;
                & .focused{
                    border: 1px solid #44b449;
                 }
            }
            & .delete-btn {
                width: 20px;
                height: 20px;
                cursor: pointer;
                margin-left: -24px;
                margin-right: 4px;
                background: url(@/assets/Salary/2.png) no-repeat center;
                background-size: 100%;
                display: none;
            }
        }
    }
}
.buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 30px;
}
