<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <!-- <span class="tip dingtalk-hidden" v-show="!isErp"
                ><span class="highlight-red">*</span> 提示：按 Ctrl + F 键并输入科目编码或者科目名称可以查找科目。</span
            > -->
            <SearchInfo :width="320" :height="30" :placeholder="'输入编码/名称/助记码/状态等关键字搜索'" @search="search"></SearchInfo>
            <ErpRefreshButton v-if="isErp"></ErpRefreshButton>
        </div>
        <div class="main-tool-right">
            <div class="mr-20">
                <el-checkbox v-model="showDisabled" label="显示停用科目" @change="changeHandle"></el-checkbox>
            </div>
            <a class="button solid-button mr-10" v-if="newSubjectBtnIsShow" v-permission="['accountsubject-canedit']" @click="addNewSubject"
                >新增科目</a
            >
            <img
                class="button-little-company mr-10"
                v-else-if="!isThirdPart"
                src="@/assets/Settings/member-badge-new-subject.png"
                alt=""
                @click="openProPurchaseDialog"
            />
            <a class="button mr-10" v-permission="['accountsubject-cancode']" @click="codeLengthHandle">编码设置</a>
            <a class="button mr-10" v-permission="['accountsubject-canexport']" @click="exportHandle">导出</a>
            <a class="button mr-10" v-permission="['accountsubject-canimport']" @click="importHandle">导入</a>
            <div v-permission="['accountsubject-canedit']" style="display: flex" class="help-tip mr-10">
                <a class="button" @click="showAsubToAssist">明细转辅助</a>
                <div @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/handle?subMenuId=110109')" v-show="!isHideBarcode">
                    <img src="@/assets/Settings/question.png" />
                </div>
                <div
                    class="help-tip-expend"
                    @click="!isHideBarcode ? globalWindowOpen('https://help.ningmengyun.com/#/jz/handle?subMenuId=110109') : ''"
                >
                    <div class="help-tip-icon"></div>
                    <div class="help-tip-content">
                        可以选择一个<span class="yellow">上级科目</span>，将其<span class="yellow">下面的明细科目</span>转为它的<span
                            class="yellow"
                            >辅助核算</span
                        >
                    </div>
                </div>
            </div>
            <Dropdown v-if="haveBatchPermission" btnTxt="批量操作" class="large-1" :downlistWidth="100">
                <li v-permission="['accountsubject-canedit']" class="large-1" @click="bulkOperation('start')">启用</li>
                <li v-permission="['accountsubject-canedit']" class="large-1" @click="bulkOperation('stop')">停用</li>
                <li v-permission="['accountsubject-candelete']" class="large-1" @click="bulkOperation('delete')">删除</li>
                <li v-permission="['accountsubject-canedit']" class="large-1" @click="bulkOperation('setAA')">设置辅助核算</li>
            </Dropdown>
            <RefreshButton></RefreshButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import SearchInfo from "@/components/SearchInfo/index.vue";
import { globalWindowOpen } from "@/util/url";
import Dropdown from "@/components/Dropdown/index.vue";
import { ref, computed } from "vue";
import { checkPermission } from "@/util/permission";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const showDisabled = ref(true);
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart);
const props = defineProps({
    // showDisabled: {
    //     type: Boolean,
    //     default: false
    // }
    newSubjectBtnIsShow: {
        type: Boolean,
        default: true,
    },
});

const emits = defineEmits([
    "disabledHandle",
    "codeLengthHandle",
    "exportHandle",
    "importHandle",
    "showAsubToAssist",
    "addSubjectRoot",
    "bulkOperation",
    "search",
]);
function changeHandle() {
    emits("disabledHandle", showDisabled.value);
}
function addNewSubject() {
    emits("addSubjectRoot");
}
function codeLengthHandle() {
    emits("codeLengthHandle");
}
function exportHandle() {
    emits("exportHandle");
}
function importHandle() {
    emits("importHandle");
}
function showAsubToAssist() {
    emits("showAsubToAssist");
}
function bulkOperation(option: string) {
    emits("bulkOperation", option);
}
function search(searchInfo: string) {
    emits("search", searchInfo);
}
const haveBatchPermission = computed(() => {
    return checkPermission(["accountsubject-canedit", "accountsubject-candelete"]);
});

function openProPurchaseDialog() {
    handleTrialExpired({ msg: ExpiredToBuyDialogEnum.addFirstLevelSubject, needExpired: false });
}
</script>

<style scoped lang="less">
.main-top {
    height: 28px;
    .main-tool-left {
        :deep(input::placeholder) {
            color: #999;
        }
    }
    .button-little-company {
        width: 94px;
        height: 29px;
        cursor: pointer;
    }
    .tip {
        color: #333;
    }

    & .help-tip {
        cursor: pointer;
        position: relative;

        & img {
            width: 16px;
            margin-left: 3px;
        }

        & .help-tip-expend {
            position: absolute;
            left: -66.5px;
            top: 26px;
            padding-top: 6px;
            width: 200px;
            font-size: 12px;
            text-align: center;
            visibility: hidden;
            opacity: 0;
            transition: var(--transition-time);
            z-index: 10;

            & .help-tip-content {
                text-align: left;
                background-color: var(--white);
                padding: 10px;
                box-shadow: 0px 0px 20px 0px rgba(179, 179, 179, 0.3);
                line-height: 18px;
                color: #808080;

                & .yellow {
                    color: var(--yellow);
                }
            }
        }

        &:hover .help-tip-expend {
            visibility: visible;
            opacity: 1;
        }
    }
}
</style>
