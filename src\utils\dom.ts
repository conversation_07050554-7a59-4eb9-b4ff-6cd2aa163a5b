/**
 * 测量文本宽度（复用 ToolTip 组件中的方法）
 * @param text 要测量的文本
 * @param fontSize 字体大小（数字）
 * @returns 文本宽度（像素）
 */
export const calculateTextWidth = (text: string, fontSize: number, fontFamily: string): number => {
  const span = document.createElement("span")
  span.style.cssText = `
      display: inline;
      width: auto;
      visibility: hidden;
      white-space: nowrap;
      font-size: ${fontSize}px;
      font-family: ${fontFamily};
    `
  span.textContent = text

  document.body.appendChild(span)
  const width = span.getBoundingClientRect().width
  document.body.removeChild(span)

  return width
}
