<template>
    <div class="expired-dialog" v-show="expiredDialogShow">
        <div class="expired-dialog-container">
            <div class="expired-dialog-title">
                <img class="vip-icon" src="@/assets/ExpiredDialgo/vip-white.png" alt="" />
                升级为专业版
            </div>
            <img class="expired-dialog-close" src="@/assets/ExpiredDialgo/trial7-dialog-close.png" @click="closeBuyDialog()" />
            <div class="txt">
                <iframe :src="iframeSrc"></iframe>
            </div>
        </div>
    </div>
    <div class="expired-dialog" v-show="retentionDialogShow">
        <div class="retention-dialog-container">
            <div class="retention-dialog-title">
                <p>确定放弃升级专业版吗?</p>
                <div class="text-money">
                    <div>
                        现在购买低至<span class="money">￥{{ price }}</span
                        >/账套/天
                    </div>
                </div>
                <img class="retention-dialog-close" src="@/assets/ExpiredDialgo/trial7-dialog-close2.png" @click="cancelUpgrade()" />
            </div>
            <div class="upgrade-rights">
                <div class="rights-title">放弃将错过以下智能功能</div>
                <div
                    class="rights-list"
                    @click="
                        buyPro();
                        retentionDialogShow = false;
                    ">
                    <div class="rights-item" v-for="(item, index) in rightsList" :key="index">
                        <img :src="getRightsImg(item.order)" alt="" />
                        <span>{{ item.name }}</span>
                    </div>
                </div>
            </div>
            <div class="btns">
                <a class="btn-cancel" @click="cancelUpgrade()">放弃升级</a><a class="btn-update" @click="continueUpgrade()">继续升级</a>
            </div>
        </div>
    </div>
    <div class="expired-dialog" v-show="customerServiceDialogShow">
        <div class="customer-service-container">
            <div class="bg">
                <img class="qr-bg" src="@/assets/ExpiredDialgo/qr-code.png" alt="" />
            </div>
            <div class="close-btn">
                <img src="@/assets/ExpiredDialgo/customer-service-close.png" alt="" @click="closeBuyDialog2" />
            </div>
        </div>
    </div>
    <div class="expired-dialog" v-show="buySuccessDialogShow">
        <div class="buy-success-container">
            <img class="dialog-close" src="@/assets/Icons/close-btn.png" @click="buySuccessDialogShow = false" />
            <div class="success-title">
                <el-icon><CircleCheckFilled /></el-icon>支付成功
            </div>
            <div class="success-msg">恭喜您!已成功购买柠檬云财务专业版</div>
            <div class="success-msg-s">立即扫码添加售后客服，为您提供VIP专属服务(向客服索取发票)</div>

            <img class="qr-code" src="@/assets/ExpiredDialgo/qr-code-pro.png" alt="" />
            <div class="qr-text">微信扫一扫，添加客服</div>
            <a class="button solid-button" @click="tryGoToPro()">前往专业版</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, watch } from "vue";
import { request } from "@/util/service";
import { buyPro } from "@/util/proUtils";
import { globalWindowOpenPage } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useTrialStatusStore } from "@/store/modules/trialStatus";

import { getCookie } from "@/util/cookie";
import { getHasProAccountSet } from "@/util/proUtils";
import { tryGoToPro } from "@/util/proUtils";
import { isLemonClient } from "@/util/lmClient";

import { CircleCheckFilled } from "@element-plus/icons-vue";
import { getRightsImg } from "./utils";
const props = defineProps<{
    heading: string;
}>();
const emits = defineEmits(["expiredToBuyDialogClose"]);
const rightsList = [
    { name: "发票管理", order: 1 },
    { name: "一键取票", order: 2 },
    { name: "资金管理", order: 3 },
    { name: "银企互联", order: 4 },
    { name: "资产管理", order: 5 },
    { name: "薪酬管理", order: 6 },
    { name: "10G空间", order: 7 },
    { name: "关联进销存", order: 9 },
];
const price = 0.27; // 固定值
const iframeSrc = computed(() => {
    return `${window.epHost}/#/upgrade/accpopup?mesKey=${props.heading}`;
});
const expiredDialogShow = ref(true);
const retentionDialogShow = ref(false);
const customerServiceDialogShow = ref(false);
const buySuccessDialogShow = ref(false);
const closeBuyDialog = () => {
    expiredDialogShow.value = false;
    retentionDialogShow.value = true;
};
const closeBuyDialog2 = () => {
    customerServiceDialogShow.value = false;
    emits("expiredToBuyDialogClose");
};
const continueUpgrade = () => {
    retentionDialogShow.value = false;
    expiredDialogShow.value = true;
};
const cancelUpgrade = () => {
    request({
        url: `${window.accountSrvHost}/api/QyWxHasDeptFollowUser.ashx?CurrentSystemType=1&systemType=1001`,
        method: "post",
    }).then((res: any) => {
        retentionDialogShow.value = false;

        if (!res.Data) {
            customerServiceDialogShow.value = true;
        } else {
            //则通过企业微信给客服群发送消息
            request({
                url: "/api/PaidConversion/GiveUpUpgradeNotify",
                method: "post",
            });
            emits("expiredToBuyDialogClose");
        }
    }).catch(() => {
        retentionDialogShow.value = false;
    });
    // 埋点
    getHasProAccountSet().then((r) => {
        request({
            url: window.apimHost + "/api/Commodity/PurchaseTracking",
            method: "post",
            params: {
                trackingType: 1040,
                hasProService: r,
                serviceId: "",
                endDate: "",
                asName: "",
            },
            headers: {
                Authorization: `Bearer ${getCookie("ningmengcookie")}`,
            },
        });
    });
};
const receiveMessage = (event: any) => {
    const accountSet = useAccountSetStore().accountSet;
    const trialStatusStore = useTrialStatusStore();
    const hash =
        "/upgrade/acc" +
        (accountSet == null || accountSet.asId == 0
            ? ""
            : "?remainingDays=" +
              (trialStatusStore.remainingDays && trialStatusStore.isTrial && !trialStatusStore.isExpired
                  ? trialStatusStore.remainingDays
                  : -1));
    if (event.data.type === "jump") {
        expiredDialogShow.value = false;
        globalWindowOpenPage("/Pro/PayDialog?appasid=" + getGlobalToken() + "&hash=" + hash, "升级专业版");
    } else if (event.data.type === "success") {
        expiredDialogShow.value = false;
        buySuccessDialogShow.value = true;
    }
};
onMounted(() => {
    window.addEventListener("message", receiveMessage);
});
onUnmounted(() => {
    window.removeEventListener("message", receiveMessage);
});
watch([expiredDialogShow, retentionDialogShow], (val) => {
    if (!val[0] && !val[1] && !isLemonClient()) {
        window.dispatchEvent(new Event("reloadOrderNews"));
    }
});
</script>

<style scoped lang="less">
.expired-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--shadow-color);
    z-index: 9999;
    & .expired-dialog-container {
        width: 1062px;
        height: 680px;
        background-color: var(--white);
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        border-radius: 5px;
        & .expired-dialog-title {
            height: 48px;
            font-size: 20px;
            font-weight: 600;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(to right, #ff9919, #ff5f1f);
            border-radius: 5px 5px 0 0;
            .vip-icon {
                width: 28px;
                height: 28px;
                padding-right: 8px;
            }
        }
        & .expired-dialog-close {
            height: 33px;
            width: 33px;
            cursor: pointer;
            position: absolute;
            top: 8px;
            right: 12px;
        }
        & .txt {
            flex: 1;
            iframe {
                width: 100%;
                height: 100%;
                border: none;
                margin: 0;
                padding: 0;
                vertical-align: top;
                border-radius: 4px;
            }
        }
        & .btn {
            width: 200px;
            height: 48px;
            text-align: center;
            line-height: 48px;
            align-self: center;
            margin-top: 87px;
            font-size: 20px;
            font-weight: 600;
            color: #6c4b15;
            background: linear-gradient(177deg, #fff7e8 0%, #f4dbb2 100%);
            border-radius: 24px;
            cursor: pointer;
        }
    }
    .retention-dialog-container {
        width: 440px;
        height: 480px;
        background-color: var(--white);
        border-radius: 8px;
        .retention-dialog-title {
            position: relative;
            overflow: hidden;
            height: 140px;
            text-align: center;
            background: url("@/assets/ExpiredDialgo/retention-dialog-bg.png") no-repeat;
            background-size: cover;
            .retention-dialog-close {
                position: absolute;
                right: 10px;
                top: 10px;
                width: 30px;
            }
            p {
                margin-top: 32px;
                font-size: 18px;
            }
            .text-money {
                width: 306px;
                margin: 20px auto;
                background-color: #ffecde;
                font-size: 21px;
                line-height: 44px;
                border-radius: 5px;
                .money {
                    color: #ff5f1f;
                    font-size: 24px;
                }
            }
        }
        .upgrade-rights {
            margin: 0 40px;
            .rights-title {
                font-size: 14px;
                text-align: center;
                line-height: 40px;
                margin-top: 15px;
                &::before {
                    content: " ";
                    display: inline-block;
                    width: 60px;
                    height: 1px;
                    background: #ececec;
                    margin-right: 10px;
                    position: relative;
                    top: -5px;
                }
                &::after {
                    content: " ";
                    display: inline-block;
                    width: 60px;
                    height: 1px;
                    background: #ececec;
                    margin-left: 10px;
                    position: relative;
                    top: -5px;
                }
            }
            .rights-list {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-around;
                padding: 5px 10px 10px;
                .rights-item {
                    width: 70px;
                    padding: 8px 5px;
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    font-size: 14px;
                    color: #333;
                    line-height: 24px;
                    img {
                        width: 50px;
                        height: 50px;
                        border-radius: 25px;
                    }
                }
            }
        }
        .btns {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            a {
                width: 160px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                font-size: 18px;
                border-radius: 20px;
                cursor: pointer;
                &.btn-cancel {
                    color: #666;
                    border: 1px solid #d9d9d9;
                }
                &.btn-update {
                    background: linear-gradient(150deg, #fff7e8 0%, #f4dbb2 100%);
                    color: #6c4b15;
                    margin-left: 30px;
                    font-weight: 600;
                }
            }
        }
    }
    .customer-service-container {
        .bg {
            width: 360px;
            height: 380px;
            position: relative;
            .qr-bg {
                width: 100%;
                height: 100%;
                border-radius: 4px;
            }
        }
        .close-btn {
            display: flex;
            justify-content: center;
            img {
                width: 40px;
                height: 40px;
                margin-top: 20px;
            }
        }
    }
    .buy-success-container {
        width: 600px;
        height: 400px;
        background-color: var(--white);
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        .dialog-close {
            position: absolute;
            top: 26px;
            right: 22px;
            width: 20px;
            height: 20px;
        }
        .success-title {
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-top: 22px;
            .el-icon {
                color: var(--main-color);
                font-size: 34px;
                margin-right: 14px;
                position: relative;
                top: 8px;
            }
        }
        .success-msg {
            text-align: center;
            font-size: 16px;
            margin-top: 22px;
        }
        .success-msg-s {
            text-align: center;
            font-size: 12px;
            margin: 5px auto 8px;
        }
        .qr-code {
            width: 128px;
            height: 128px;
            margin: 10px auto 5px;
            border: 1px solid var(--border-color);
        }
        .qr-text {
            font-size: 12px;
        }
        .button {
            width: 120px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            font-size: 14px;
            color: #fff;
            cursor: pointer;
            margin-top: 32px;
        }
    }
}
</style>
