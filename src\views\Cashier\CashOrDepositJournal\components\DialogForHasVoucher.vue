<template>
    <DialogTip
        ref="DialogTipRef"
        @closed="handleClosed"
        @confirm="handleConfirm"
        :tip-title="'资金数据已生成凭证，是否仍需修改？'"
        :tip-info="'修改会导致资金流水与凭证不一致~'"
    >
        <div class="mt-20">
            <el-checkbox v-model="notTip" @change="handleNoTipsChange" label="不再提示" />
        </div>
    </DialogTip>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import DialogTip from "@/components/Dialog/DialogTip/index.vue";

const DialogTipRef = ref();

const notTip = ref(false);
let editRow: null | (() => void) = null;

function handleNoTipsChange(val: any) {
    const check = !!val;
    const key = "tempHiddenFixedDialog4Journal-" + getGlobalToken();
    localStorage.setItem(key, String(check));
}
function handleClosed() {
    notTip.value = false;
    editRow = null;
}
function handleConfirm() {
    const notTip = localStorage.getItem("tempHiddenFixedDialog4Journal-" + getGlobalToken()) === "true";
    const key = "hiddenFixedDialog4Journal-" + getGlobalToken();
    localStorage.setItem(key, String(notTip));
    editRow && editRow();
}
function showDialog(callBack: () => void) {
    editRow = callBack;
    DialogTipRef.value?.showDialog();
}
defineExpose({ showDialog });
</script>

<style lang="less" scoped>
.voucher-tip-box {
    .box-main {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        .tip {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &.erp {
        .box-main {
            border: none;
            text-align: left;
            padding-bottom: 0;
            .tip {
                justify-content: flex-start;
            }
        }
        .buttons {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            padding: 20px 10px;
            border-top: 1px solid var(--border-color);
            margin-top: 20px;
            .button {
                margin: 0 10px;
            }
        }
    }
}
</style>
