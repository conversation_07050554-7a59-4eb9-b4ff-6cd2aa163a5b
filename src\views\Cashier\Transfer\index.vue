<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">内部转账</div>
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left" v-if="isErp">
                            <span class="label">日期：</span>
                            <div class="flex-space-10"></div>
                            <div class="picker-select">
                                <DatePicker
                                    v-model:startPid="searchInfo.startPid"
                                    v-model:endPid="searchInfo.endPid"
                                    v-model:periodInfo="periodInfo"
                                    :clearable="true"
                                    :disabled-date-start="disabledDateStart"
                                    :disabled-date-end="disabledDateEnd"
                                />
                            </div>
                            <div class="flex-space-10"></div>
                            <span class="label">转出账户：</span>
                            <div class="flex-space-10"></div>
                            <Select 
                                :teleported="false" 
                                v-model="searchInfo.CDAccount" 
                                :placeholder="'请选择'" 
                                :filterable="true"
                                :filter-method="accountFilterMethod"
                            >
                                <SelectOption 
                                    v-for="item in showAccountList" 
                                    :key="item.ac_id" 
                                    :value="item.ac_id" 
                                    :label="item.label" 
                                />
                            </Select>
                            <div class="flex-space-10"></div>
                            <span class="label">转入账户：</span>
                            <div class="flex-space-10"></div>
                            <Select 
                                :teleported="false" 
                                v-model="searchInfo.CDAccountIn" 
                                :placeholder="'请选择'" 
                                :filterable="true"
                                :filter-method="accountFilterMethod"
                            >
                                <SelectOption 
                                    v-for="item in showAccountList" 
                                    :key="item.ac_id" 
                                    :value="item.ac_id" 
                                    :label="item.label" 
                                />
                            </Select>
                            <div class="flex-space-10"></div>
                            <a class="solid-button" @click="handleLoadData">查询</a>
                            <div class="flex-space-10"></div>
                        </div>
                        <div class="main-tool-left" v-else>
                            <SearchInfoContainer ref="containerRef" class="invoice-search-container">
                                <template v-slot:title>{{ currentPeriodInfo }} </template>
                                <div class="line-item first-item input">
                                    <div class="line-item-title">日期：</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.startPid"
                                            v-model:endPid="searchInfo.endPid"
                                            v-model:periodInfo="periodInfo"
                                            :clearable="true"
                                            :disabled-date-start="disabledDateStart"
                                            :disabled-date-end="disabledDateEnd"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">凭证日期:</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.startVDate"
                                            v-model:endPid="searchInfo.endVDate"
                                            :disabled-date-start="disabledStartVDate"
                                            :disabled-date-end="disabledEndVDate"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">凭证字号：</div>
                                    <div class="line-item-field">
                                        <div class="vgid-line">
                                            <el-select
                                                v-model="searchInfo.vgId"
                                                :teleported="false"
                                                style="width: 90px"
                                                :fit-input-width="true"
                                                @change="handleVgIdChange"
                                            >
                                                <el-option :value="0" label="请选择">请选择</el-option>
                                                <el-option :value="1" label="全部">全部</el-option>
                                                <el-option
                                                    v-for="item in voucherGroup"
                                                    :value="item.id"
                                                    :key="item.id"
                                                    :label="item.name"
                                                ></el-option>
                                            </el-select>
                                            <el-input
                                                clearable
                                                type="text"
                                                class="ml-10"
                                                v-model="searchInfo.startVNum"
                                                :disabled="!searchInfo.vgId"
                                                @input="startVNumLimit"
                                            ></el-input>
                                            <span style="padding: 0 8px; line-height: 30px">至</span>
                                            <el-input
                                                clearable
                                                type="text"
                                                v-model="searchInfo.endVNum"
                                                :disabled="!searchInfo.vgId"
                                                @input="endVNumLimit"
                                            ></el-input>
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">转出账户：</div>
                                    <div class="line-item-field filed-large">
                                        <Select
                                            :circleClose="true"
                                            IconClearRight="26px"
                                            :clearable="true"
                                            :teleported="false"
                                            v-model="searchInfo.CDAccount"
                                            :placeholder="'请选择'"
                                            :filterable="true"
                                            :filter-method="accountFilterMethod"
                                            :fit-input-width="true"
                                        >
                                            <SelectOption
                                                v-for="item in showAccountList"
                                                :key="item.ac_id"
                                                :value="item.ac_id"
                                                :label="item.label"
                                            />
                                        </Select>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">转入账户：</div>
                                    <div class="line-item-field filed-large">
                                        <Select
                                            :circleClose="true"
                                            IconClearRight="26px"
                                            :clearable="true"
                                            :teleported="false"
                                            v-model="searchInfo.CDAccountIn"
                                            :placeholder="'请选择'"
                                            :filterable="true"
                                            :filter-method="accountFilterMethod"
                                            :fit-input-width="true"
                                        >
                                            <SelectOption
                                                v-for="item in showAccountList"
                                                :key="item.ac_id"
                                                :value="item.ac_id"
                                                :label="item.label"
                                            />
                                        </Select>
                                    </div>
                                </div>

                                <div class="buttons">
                                    <a class="button solid-button" @click="handleLoadData">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                        </div>
                        <div class="main-tool-right">
                            <Dropdown
                               btnTxt="打印列表"
                               class="mr-10 print"
                               :downlistWidth="isErp ? 96 : 84"
                               v-permission="['transfer-canprint']">
                                <li @click="handlePrint(0,getPrintOrExportParams())">直接打印</li>
                                <li @click="handlePrint(2)">打印设置</li>
                            </Dropdown>
                            <div class="flex-space-10"></div>
                            <Dropdown btnTxt="导出" class="mr-10" v-permission="['transfer-canexport']" :downlistWidth="100">
                                <li @click="handleExport('')">当前查询数据</li>
                                <li @click="handleExport('all')">所有数据</li>
                            </Dropdown>
                            <div class="flex-space-10"></div>
                            <a class="button" @click="handleShowImportDialog" v-permission="['transfer-canimport']">导入</a>
                            <div class="flex-space-10"></div>
                            <a class="button" @click="BatchDelete" v-permission="['transfer-candelete']">批量删除</a>
                            <div class="flex-space-10"></div>
                            <a class="button" v-if="isErp && checkPermission(['businessvoucher-canview'])" @click="genertaeErpVoucher">生成凭证</a>
                            <el-tooltip effect="light" content="本按钮只支持跳转业务生成凭证页面，单据的筛选和勾选不会带入" placement="top-start">
                                <el-icon v-if="isErp && checkPermission(['businessvoucher-canview'])" class="el-icon-question" color="#3385ff"><QuestionFilled /></el-icon>
                            </el-tooltip>
                            <div
                                v-permission="['transfer-cancreatevoucher']"
                                class="item ml-10"
                                v-if="!isErp"
                                style="position: relative; display: flex; align-items: center; flex-wrap: nowrap"
                                @mouseleave="() => (voucherFromJournalDownList = false)"
                            >
                                <a class="button solid-button" style="float: left; width: 100px" @click="newVoucher">生成凭证</a>
                                <a
                                    class="button solid-button down-click"
                                    style="float: left"
                                    @mouseenter="() => (voucherFromJournalDownList = true)"
                                ></a>
                                <div style="width: 120px; top: 28px" class="downlist" v-show="voucherFromJournalDownList">
                                    <li @click="handleShowJournalSettings">生成凭证设置</li>
                                </div>
                            </div>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <Table
                            ref="tableRef"
                            :class="isErp ? 'erp-table' : ''"
                            :loading="loading"
                            :data="tableData"
                            :columns="columns"
                            :page-is-show="true"
                            :layout="paginationData.layout"
                            :page-sizes="paginationData.pageSizes"
                            :page-size="paginationData.pageSize"
                            :total="paginationData.total"
                            :currentPage="paginationData.currentPage"
                            :selectable="setSelectable"
                            :show-overflow-tooltip="true"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            @selection-change="handleSelectionChange"
                            @row-click="handleRowClick"
                            @refresh="handleRerefresh"
                            :scrollbarShow="true"
                            :tableName="setModule"
                        >
                            <template #date>
                                <el-table-column
                                    label="日期"
                                    align="left"
                                    header-align="left"
                                    :min-width="127"
                                    prop="date"
                                    :width="getColumnWidth(setModule, 'date')"
                                >
                                    <template #default="scope">
                                        <span :class="[editNumber == scope.row.index ? 'none' : '', 'ellipsis']">
                                            {{ scope.row.cd_date_text }}
                                        </span>
                                        <div
                                            class="my-data-picker edit-item"
                                            v-if="scope.row.description && editNumber == scope.row.index"
                                            @click.capture="handleDateFocus"
                                        >
                                            <el-date-picker
                                                ref="elDatePickerRef"
                                                v-model="editRowInfo.cdDate"
                                                type="date"
                                                :clearable="false"
                                                value-format="YYYY-MM-DD"
                                                :editable="false"
                                                @change="handleCalendarChange"
                                                @visible-change="visibleChange"
                                            />
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #description>
                                <el-table-column
                                    label="摘要"
                                    align="left"
                                    header-align="left"
                                    :min-width="200"
                                    prop="description"
                                    :width="getColumnWidth(setModule, 'description')"
                                >
                                    <template #default="scope">
                                        <span :class="[editNumber == scope.row.index ? 'none' : '', 'ellipsis']">{{
                                            scope.row.description
                                        }}</span>
                                        <div class="description edit-item" v-if="scope.row.description && editNumber == scope.row.index">
                                            <el-input
                                                ref="inputRef"
                                                v-if="descriptionTextareaShow"
                                                :autosize="{ minRows: 1, maxRows: 3.5 }"
                                                type="textarea"
                                                maxlength="256"
                                                resize="none"
                                                v-model="editRowInfo.description"
                                                @focus="handleFocus()"
                                                @blur="handleBlur(scope.row, 'description')"
                                                @keyup="handleDescriptionEnter($event)"
                                                @paste="handleDescritionPaste($event)"
                                                @input="handleInput(editRowInfo.description, '摘要')"
                                            />
                                            <Tooltip v-else :content="editRowInfo.description" :isInput="true">
                                                <el-input
                                                    type="text"
                                                    ref="inputRef"
                                                    v-model="editRowInfo.description"
                                                    @focus="handleFocus('inputRef')"
                                                    @keyup="handleDescriptionEnter($event)"
                                                />
                                            </Tooltip>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #expenditure>
                                <el-table-column
                                    label="转出账户"
                                    align="left"
                                    header-align="left"
                                    :min-width="204"
                                    prop="expenditure"
                                    :width="getColumnWidth(setModule, 'expenditure')"
                                >
                                    <template #default="scope">
                                        <span :class="[editNumber == scope.row.index ? 'none' : '', 'ellipsis']">
                                            {{ scope.row.cd_account_name }}{{ getForbiddenText(String(scope.row.cd_account)) }}</span>
                                        <div
                                            class="expenditure-select edit-item"
                                            v-if="scope.row.description && editNumber == scope.row.index"
                                            @click.capture="handleFocus()"
                                        >
                                            <Select
                                                ref="expenditureSelectRef"
                                                v-model="expenditureAccountName"
                                                :teleported="true"
                                                :fit-input-width="true"
                                                :filterable="true"
                                                :automatic-dropdown="true"
                                                :clearable="false"
                                                placeholder=" "
                                                :immediatelyBlur="false"
                                                @blur="handleBlur(scope.row)"
                                                @keyup-enter="handleExpenditureEnter"
                                                @change="handleExpenditureChange"
                                                :filter-method="enableAccountFilterMethod"
                                            >
                                                <SelectOption
                                                    v-for="item in showEnableAccountList"
                                                    :key="item.ac_id"
                                                    :value="item.ac_id"
                                                    :label="item.label || ''"
                                                />
                                            </Select>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #income>
                                <el-table-column
                                    label="转入账户"
                                    align="left"
                                    header-align="left"
                                    :min-width="204"
                                    prop="income"
                                    :width="getColumnWidth(setModule, 'income')"
                                   
                                >
                                    <template #default="scope">
                                        <span :class="[editNumber == scope.row.index ? 'none' : '', 'ellipsis']">
                                            {{ scope.row.cd_account_in_name}}{{ getForbiddenText(scope.row.cd_account_in)}}</span>
                                        <div
                                            class="income-select edit-item"
                                            v-if="scope.row.description && editNumber == scope.row.index"
                                            @click.capture="handleFocus()"
                                        >
                                            <Select
                                                ref="incomeSelectRef"
                                                v-model="incomeAccountName"
                                                :teleported="true"
                                                :fit-input-width="true"
                                                :filterable="true"
                                                :automatic-dropdown="true"
                                                :clearable="false"
                                                placeholder=" "
                                                :immediatelyBlur="false"
                                                @blur="handleBlur(scope.row)"
                                                @keyup-enter="handleIncomeEnter"
                                                @change="handleIncomeChange"
                                                :filter-method="enableAccountFilterMethod"
                                            >
                                                <SelectOption
                                                    v-for="item in showEnableAccountList"
                                                    :key="item.ac_id"
                                                    :value="item.ac_id"
                                                    :label="item.label"
                                                />
                                            </Select>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #amount>
                                <el-table-column
                                    label="金额"
                                    align="right"
                                    header-align="right"
                                    :min-width="127"
                                    prop="amount"
                                    :width="getColumnWidth(setModule, 'amount')"
                                >
                                    <template #default="scope">
                                        <span
                                            :class="editNumber == scope.row.index ? 'none' : ''"
                                            v-html="scope.row.expenditure_data"
                                            class="ellipsis"
                                        >
                                        </span>
                                        <div class="amount edit-item" v-if="scope.row.description && editNumber == scope.row.index">
                                            <input
                                                ref="amountInputRef"
                                                type="text"
                                                v-model="editRowInfo.amount"
                                                @focus="(e) => handleAmountFocus(e)"
                                                @input="(e) => handleAmountInput(e)"
                                                @paste="(e) => handleAmountPaste(e)"
                                                @keydown="(e) => handleAmountKeyDown(e)"
                                                @blur="handleBlur(scope.row)"
                                                @keyup="handleAmountEnter($event)"
                                                @change="handleAmountChange"
                                            />
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #note>
                                <el-table-column
                                    label="备注"
                                    align="left"
                                    header-align="left"
                                    :min-width="127"
                                    prop="note"
                                    :width="getColumnWidth(setModule, 'note')"
                                >
                                    <template #default="scope">
                                        <span :class="[editNumber == scope.row.index ? 'none' : '', 'ellipsis']">{{ scope.row.note }}</span>
                                        <div class="note edit-item" v-if="scope.row.description && editNumber == scope.row.index">
                                            <el-input
                                                ref="noteRef"
                                                v-model="editRowInfo.note"
                                                v-if="noteTextareaShow"
                                                :autosize="{ minRows: 1, maxRows: 3.5 }"
                                                type="textarea"
                                                maxlength="256"
                                                resize="none"
                                                @focus="handleFocus()"
                                                @blur="handleBlur(scope.row, 'note')"
                                                @keyup="handleNoteEnter($event)"
                                                @input="handleInput(editRowInfo.note, '备注')"
                                            />
                                            <Tooltip v-else :content="editRowInfo.note" :isInput="true">
                                                <el-input
                                                    ref="noteRef"
                                                    v-model="editRowInfo.note"
                                                    @focus="handleFocus('noteRef')"
                                                    @keyup="handleNoteEnter($event)"
                                                />
                                            </Tooltip>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #voucher>
                                <el-table-column
                                    label="关联凭证"
                                    align="left"
                                    header-align="left"
                                    :min-width="127"
                                    prop="voucher"
                                    :width="getColumnWidth(setModule, 'voucher')"
                                >
                                    <template #default="scope">
                                        <span
                                            :class="checkVoucherPermission() ? 'link ellipsis' : 'cursor-default ellipsis'"
                                            @click.stop="checkVoucherPermission() ? goToDetail(scope.row) : ''"
                                            >{{ scope.row.v_num2 }}</span
                                        >
                                    </template>
                                </el-table-column>
                            </template>
                            <template #operation>
                                <el-table-column
                                    label="操作"
                                    align="left"
                                    header-align="left"
                                    min-width="87px"
                                    class-name="handle"
                                    :resizable="false"
                                >
                                    <template #default="scope">
                                        <a
                                            class="link"
                                            v-show="scope.row.showDelete && scope.row.cd_date_text >= checkDate"
                                            style="text-decoration: none"
                                            @click.stop="deleteItem(scope.row)"
                                            v-permission="['transfer-candelete']"
                                        >
                                            删除
                                        </a>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
            </template>
            <template #list>
                <GenerateVoucher
                    :title="'内部转账生成凭证'"
                    :documentTitle="'日记账'"
                    :query-params="genVoucherQueryParameters"
                    @load-success="loadSuccess = true"
                    :error-table-columns="genVoucherErrorTableColumns"
                    @back="genVoucherSaveSuccess"
                    :merge-error-function="mergeErrorFunction"
                    @voucher-changed="genVoucherChangedHandle"
                    ref="generateVoucherView"
                >
                    <template #toolbar>
                        <a class="solid-button" @click="saveGenVoucher()">保存</a>
                        <a class="button ml-10" @click="currentSlot = 'main'">取消</a>
                        <el-checkbox
                            class="ml27"
                            label="凭证合并"
                            @change="mergeVoucher()"
                            v-model="genVoucherQueryParameters.isMerge"
                        ></el-checkbox>
                        <SelectCheckbox
                            class="ml10"
                            ref="selectCheckboxRef"
                            inputPlaceholder="请选择凭证合并条件"
                            :useElIcon="true"
                            width="180px"
                            :options="options"
                            :select-all="false"
                            @update:selected-list="changeSelectedList"
                            v-show="genVoucherQueryParameters.isMerge"
                            v-model:selectedList="selectedList"
                            :show-all="false"
                            @check-box-show-change="checkBoxShowChange"
                        >
                        </SelectCheckbox>

                        <span class="ml10" style="font-size: 14px" v-show="genVoucherQueryParameters.isMerge">科目合并</span>
                        <SelectCheckbox
                            class="ml10"
                            :useElIcon="true"
                            width="180px"
                            :options="subjectOption"
                            :select-all="false"
                            @update:selected-list="changeSubjectSelectedList"
                            ref="selectSubjectRef"
                            :place-holder="'请选择科目合并方式'"
                            v-show="genVoucherQueryParameters.isMerge"
                            v-model:selectedList="subjectSelectedList"
                        >
                        </SelectCheckbox>
                    </template>
                </GenerateVoucher>
            </template>
        </ContentSlider>
    </div>

    <ImportSingleFileDialog
        :importTitle="'导入内部转账'"
        v-model:import-show="importShow"
        :importUrl="'/api/JournalTransfer/Import'"
        :uploadSuccess="uploadSuccess"
        :needLoading="true"
        class="cashier-transfer-import-dialog"
    >
        <template #download>
            <div>1.请选择下面任意一种方式导入内部转账</div>
            <div class="mt-10 ml-10">(1)在内部转账列表导出所需数据，确认后直接导入</div>
            <div class="mt-10 ml-10">
                (2)点击下载模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="downLoadImportTemplate">下载模板</a>
            </div>
        </template>
        <template #import-content>
            <span>2.选择文件导入</span>
        </template>
    </ImportSingleFileDialog>
    <el-dialog
        v-model="foriginAmountInfo.show"
        center
        :width="foriginAmountInfo.showWay === 'all' ? 650 :320"
        :show-close="false"
        :destroy-on-close="true"
        class="foriginAmountInfo-dialog dialogDrag"
        modal-class="modal-class"
    >
        <div class="add-ietype-dialog-container" v-dialogDrag>
            <div class="add-ietype-line-item" style="margin-bottom: 10px;">
                <div class="add-ietype-title transfertitle">
                    转出{{ getCurrencyCode(showExpenditureItem) }} 转入{{ getCurrencyCode(showIncomeItem) }}
                </div>
            </div>
            <div v-if="foriginAmountInfo.showWay === 'out'">
                <div class="add-ietype-line-item">
                    <div class="add-ietype-title">原币：</div>
                    <div class="add-ietype-field">
                        <input
                            type="text"
                            ref="amountTxtRef"
                            v-model="foriginAmountInfo.outTxt"
                            @input="handleAmountTxtChange($event)"
                            @blur="handleAmountOutInBlur('out')"
                            @focus="handleAmountOutInFocus('out')"
                            class="amt"
                        />
                    </div>
                </div>
                <div class="add-ietype-line-item">
                    <div class="add-ietype-title">汇率：</div>
                    <div class="add-ietype-field">
                        <input 
                            v-model="foriginAmountInfo.outRateTxt" 
                            @wheel.prevent 
                            @input="handleRateInput($event)"
                            @blur="handleRateInputBlur" 
                            class="amt" 
                        />
                    </div>
                </div>
                <div class="add-ietype-line-item">
                    <div class="add-ietype-title">本币：</div>
                    <div class="add-ietype-field">
                        <input type="text" v-model="foriginAmountInfo.baseTxt" disabled />
                    </div>
                </div>
            </div>
            <div v-if="foriginAmountInfo.showWay === 'in'">
                <div class="add-ietype-line-item">
                    <div class="add-ietype-title">本币：</div>
                    <div class="add-ietype-field">
                        <input
                            type="text"
                            ref="amountStdTxtRef"
                            v-model="foriginAmountInfo.baseTxt"
                            class="amt"
                            @input="handleStdChange($event)"
                            @blur="handleAmountOutInBlur('base')"
                            @focus="handleAmountOutInFocus('base')"
                        />
                    </div>
                </div>
                <div class="add-ietype-line-item">
                    <div class="add-ietype-title">汇率：</div>
                    <div class="add-ietype-field">
                        <input 
                            v-model="foriginAmountInfo.inRateTxt" 
                            @wheel.prevent 
                            @input="handleRateInput($event)"
                            @blur="handleRateInputBlur"
                            class="amt" 
                        />
                    </div>
                </div>
                <div class="add-ietype-line-item">
                    <div class="add-ietype-title">原币：</div>
                    <div class="add-ietype-field">
                        <input type="text" v-model="foriginAmountInfo.inTxt" disabled />
                    </div>
                </div>
            </div>
            <div v-if="foriginAmountInfo.showWay === 'all'">
                <div class="add-ietype-line-row">
                    <div class="add-ietype-line-item">
                        <div class="add-ietype-title">转出原币({{ getCurrencyCode(showExpenditureItem) }})：</div>
                        <div class="add-ietype-field">
                            <input
                                type="text"
                                ref="outTxtRef"
                                v-model="foriginAmountInfo.outTxt"
                                class="amt"
                                @input="handleOutInChange($event, 'out', 'in')"
                                @blur="handleAmountOutInBlur('out')"
                                @focus="handleAmountOutInFocus('out')"
                            />
                        </div>
                    </div>
                    <div class="add-ietype-line-item">
                        <div class="add-ietype-title">汇率({{`${getCurrencyCode(showExpenditureItem)}对RMB`}})：</div>
                        <div class="add-ietype-field">
                            <input 
                                type="text" 
                                v-model="foriginAmountInfo.outRateTxt"
                                @input="handleRateOutIn($event, 'out', 'in')"
                                @blur="handleRateOutInBlur('out', 'in')"
                                @focus="handleRateOutInFocus"
                                class="amt" 
                            />
                        </div>
                    </div>
                </div>
                <div class="add-ietype-line-row">
                    <div class="add-ietype-line-item">
                        <div class="add-ietype-title">转入原币({{ getCurrencyCode(showIncomeItem) }})：</div>
                        <div class="add-ietype-field">
                            <input 
                                type="text" 
                                v-model="foriginAmountInfo.inTxt" 
                                class="amt"
                                @input="handleOutInChange($event, 'in', 'out')"
                                @blur="handleAmountOutInBlur('in')"
                                @focus="handleAmountOutInFocus('in')"
                            />
                        </div>
                    </div>
                    <div class="add-ietype-line-item">
                        <div class="add-ietype-title">汇率({{`${getCurrencyCode(showIncomeItem)}对RMB`}})：</div>
                        <div class="add-ietype-field">
                            <input 
                                type="text" 
                                v-model="foriginAmountInfo.inRateTxt"
                                @input="handleRateOutIn($event, 'in', 'out')"
                                @blur="handleRateOutInBlur('in', 'out')"
                                @focus="handleRateOutInFocus"
                                class="amt"
                            />
                        </div>
                    </div>
                </div>
                <div class="add-ietype-line-row">
                    <div class="add-ietype-line-item">
                        <div class="add-ietype-title">本币(RMB)：</div>
                        <div class="add-ietype-field">
                            <input 
                                type="text" 
                                v-model="foriginAmountInfo.baseTxt" 
                                @input="handleBaseInput($event)" 
                                class="amt"
                                @blur="handleAmountOutInBlur('base')"
                                @focus="handleAmountOutInFocus('base')"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="add-ietype-buttons">
                <a class="button solid-button" @click="handleForiginAmountDialogSave">确认</a>
                <a class="button ml-10" @click="handleForiginAmountDialogCancel">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="提示" width="600px" center v-model="fcAccountHistoryDataSync" :show-close="false" class="dialogDrag">
        <div class="fc-account-history-data-sync-content" v-dialogDrag>
            <div class="data-sync-main">
                <div class="data-sync-title">
                    当前账户 <span v-html="syncTitle"></span> 请您确认历史日记账数据金额是否是
                    {{
                        syncFcName
                    }}？确认完成后系统会根据币别设置的汇率自动计算本币或原币，同时支持外币核算生成记账凭证功能。如存在多个外币账户，请切换账户逐一确认。
                </div>
                <div class="data-sync-tip">
                    <img src="@/assets/Icons/warn.png" />
                    <div>注意：{{ accountSetBackupTips() }}</div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="syncFcData(0)">录入的是{{ syncFcName }}</a>
                <a class="button ml-20" @click="syncFcData(1)">录入的是人民币</a>
            </div>
        </div>
    </el-dialog>
    <GenerateVoucherSettingDialog
        ref="generateVoucherSettingDialogRef"
        v-model="voucherSettingDialogShow"
        :setting-type="'transferJournal'"
    />
    <TransferPrint
        v-model:printDialogShow="printDialogVisible"
        :title="'内部转账列表打印'"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getPrintOrExportParams())"
    />
</template>
<script lang="ts">
export default {
    name: "Transfer",
};
</script>
<script setup lang="ts">
import { reactive, ref, watch, nextTick, onMounted, onUnmounted, onActivated, toRef, watchEffect, computed } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { usePagination } from "@/hooks/usePagination";
import { getUrlSearchParams, globalWindowOpenPage, globalPrint, globalExport, tryClearCustomUrlParams } from "@/util/url";
import { checkPermission } from "@/util/permission";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { dayjs } from "element-plus";
import Dropdown from "@/components/Dropdown/index.vue";
import { getshortdata } from "@/views/Cashier/CDJournal/utils";
import { FullScreenLoading } from "@/util/fullScreenLoading";
import { GetStart, checkFCAccountExistsHistoryData, syncFCAccountHistoryData } from "@/views/Cashier/CashOrDepositJournal/utils";
import { useAccountSetStore } from "@/store/modules/accountSet";
import Tooltip from "@/components/Tooltip/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableItem, ICurrency } from "./types";
import {
    TransferDocumentModel,
    TransferQueryParameters,
    type IBatchGenerateVoucherModel,
    TransferWithVoucherModel,
    type IGenVoucherNeedInsertAsub,
    type ITransferGenVoucherResult,
} from "@/components/GenerateVoucher/types";
import type { IGenerateVoucherPreCheckResult } from "@/views/Cashier/CashOrDepositJournal/types";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import { ElInput } from "element-plus";
import DatePicker from "@/components/DatePicker/index.vue";
import Table from "@/components/Table/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import GenerateVoucher from "@/components/GenerateVoucher/index.vue";
import Select from "@/components/Select/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import SelectOption from "@/components/Option/index.vue";
import TransferPrint from "@/components/PrintDialog/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useLoading } from "@/hooks/useLoading";
import GenerateVoucherSettingDialog from "@/components/Dialog/GenerateVoucherSetting/index.vue";
import type { Option } from "@/components/SelectCheckbox/types";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { textareaBottom } from "@/views/FixedAssets/FixedAssets/utils";
import { accountSetBackupTips } from "@/util/showTips";
import { editConfirm } from "@/util/editConfirm";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import type { IBankAccountLabelItem } from "@/views/Cashier/CDAccount/utils";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import usePrint from "@/hooks/usePrint";
import { useCurrencyStore } from "@/store/modules/currencyList";

const elDatePickerRef = ref();
const _ = getGlobalLodash();
const accountSubjectStore = useAccountSubjectStore();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const inputRef = ref<HTMLInputElement>();
const expenditureSelectRef = ref<InstanceType<typeof Select>>();
const incomeSelectRef = ref<InstanceType<typeof Select>>();
const amountTxtRef = ref<HTMLInputElement>();
const amountStdTxtRef = ref<HTMLInputElement>();
const amountInputRef = ref<HTMLInputElement>();
const noteRef = ref<InstanceType<typeof ElInput>>();
const voucherSettingDialogShow = ref(false);
const generateVoucherSettingDialogRef = ref<InstanceType<typeof GenerateVoucherSettingDialog>>();
const currentPeriodInfo = ref("");
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = voucherGroupStore.voucherGroupList;
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
function checkVoucherPermission() {
    if (isErp.value) {
        return checkPermission(["voucher-canview"]);
    } else {
        return checkPermission(["voucher-canview", "transfer-cancreatevoucher"]);
    }
}
function setSelectable(row: any, rowIndex: number): boolean {
    if (row.new || row.cd_date === "") {
        return false;
    }
    return true;
}
const isPasteDes = ref(false);
const handleDescritionPaste = (event: ClipboardEvent) => {
    isPasteDes.value = true;
};
const handleInput = (description: string, name: string) => {
    if (description.length === 256) {
        ElNotify({ type: "warning", message: "亲，" + name + "不能超过256个字符！" });
    }
    isPasteDes.value && (editRowInfo.description = description.trim());
    isPasteDes.value = false;
};
const handleDescriptionEnter = (event: KeyboardEvent) => {
    if (event.keyCode === 13) {
        event.preventDefault();
        event.stopPropagation();
        inputRef.value?.blur();
        setTimeout(() => {
            editing = true;
        }, 50);
        nextTick(() => {
            expenditureSelectRef.value?.focus();
        });
    }
};
const handleExpenditureEnter = () => {
    expenditureSelectRef.value?.blur();
    setTimeout(() => {
        editing = true;
    }, 50);
    nextTick(() => {
        incomeSelectRef.value?.focus();
    });
};
const handleIncomeEnter = () => {
    incomeSelectRef.value?.blur();
    setTimeout(() => {
        editing = true;
    }, 50);
    nextTick(() => {
        amountInputRef.value?.focus();
    });
};
const handleAmountEnter = (event: KeyboardEvent) => {
    if (event.keyCode === 13) {
        event.preventDefault();
        event.stopPropagation();
        amountInputRef.value?.blur();
        setTimeout(() => {
            editing = true;
        }, 50);
        nextTick(() => {
            noteRef.value?.focus();
        });
    }
};
const handleNoteEnter = (event: KeyboardEvent) => {
    if (event.keyCode === 13) {
        noteRef.value?.blur();
    }
};

const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const setModule = "Transfer";
const columns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 30, headerAlign: "center", align: "center" },
    { slot: "date" },
    { slot: "description" },
    { slot: "expenditure" },
    { slot: "income" },
    { slot: "amount" },
    { slot: "note" },
    { slot: "voucher" },
    { slot: "operation" },
]);

const isErp = ref(window.isErp);
const voucherFromJournalDownList = ref(false);
const slots = ["main", "list"];
const currentSlot = ref("main");
const periodInfo = ref("");
const loading = ref(false);
const importShow = ref(false);
const tableData = ref<ITableItem[]>([]);
const selectionList = ref<any[]>([]);
const accountList = ref<IBankAccountLabelItem[]>([]);
const currencyList = ref<ICurrency[]>([]);
const searchInfo = reactive({
    startPid: "",
    endPid: "",
    startVDate: "",
    endVDate: "",
    vgId: 0,
    startVNum: "",
    endVNum: "",
    CDAccount: "",
    CDAccountIn: "",
});
const descriptionTextareaShow = ref(false);
const noteTextareaShow = ref(false);
watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});
watch([() => searchInfo.startVDate, () => searchInfo.endVDate], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startVDate = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endVDate = "";
    }
});
const startVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.startVNum = val;
};
const endVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.endVNum = val;
};
const handleVgIdChange = (val: number) => {
    if (val === 0) {
        searchInfo.startVNum = "";
        searchInfo.endVNum = "";
    }
};
const handleReset = () => {
    searchInfo.startPid = searchStart.value;
    searchInfo.endPid = searchEnd.value;
    searchInfo.startVDate = "";
    searchInfo.endVDate = "";
    searchInfo.vgId = 0;
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
    searchInfo.CDAccount = "";
    searchInfo.CDAccountIn = "";
};
const searchStart = ref("");
const searchEnd = ref("");
const checkDate = ref("");
const startDate = ref("");
const generateVoucherView = ref<InstanceType<typeof GenerateVoucher>>();
const genVoucherQueryParameters = ref(new TransferQueryParameters());
const genVoucherChanged = ref(false);
const genVoucherErrorTableColumns = ref<IColumnProps[]>([
    {
        label: "单据编号",
        prop: "cdDate",
        align: "left",
        headerAlign: "left",
        width: 160,
        formatter: (row: TransferDocumentModel) => {
            return `${row.jtype === 1010 ? 1 : 2}-${row.cdAccountName.split("-")[0]}-${row.cdDate.replace(/-/g, "")}-${row.lineSn}`;
        },
    },
    {
        label: "收支类别",
        prop: "ietypeName",
        align: "left",
        headerAlign: "left",
        width: 110,
    },
    {
        label: "金额",
        prop: "amount",
        align: "right",
        headerAlign: "right",
        width: 136,
        formatter: (row: TransferDocumentModel) => {
            return _.round(row.income + row.expenditure, 2);
        },
    },
    {
        slot: "errorInfo",
    },
]);

const accountPeriodStore = useAccountPeriodStore();
const checkOutEndDate = computed(() => {
    return accountPeriodStore.periodList
        .filter((p) => p.status !== 3)[0]?.startDate || "";
});
const IsCanAdd = () => {
    return new Date(searchInfo.endPid) >= new Date(checkDate.value);
}

const getAccountListApi = (acType: number) => request({ url: "/api/CDAccount/List?acType=" + acType });
const getInitAddRow = () => {
    let date = new Date(searchInfo.startPid) >= new Date(checkDate.value) ? searchInfo.startPid : checkOutEndDate.value;
    return {
        showDelete: false,
        new: true,
        cd_date: date,
        cd_date_text: date,
        description: "资金内部调拨",
        expenditure: "",
        expenditure_data: "",
        expenditure_standard: 0,
        income: "",
        income_standard: 0,
        amount: "",
        amount_standard: "",
        note: "",
        cd_account: "",
        cd_account_in: "",
        cd_account_name: "",
        cd_account_in_name: "",
        created_date_txt: date,
        created_date: "",
        p_id: "",
        v_id: "",
        v_num2: "",
        fc_rate: 1,
    };
};
const fcAccountHistoryDataSync = ref(false);
const syncTitle = ref("");
const syncFcName = ref("");
const tableRef = ref();
function checkCanNext() {
    if (searchInfo.startPid === "" || searchInfo.endPid === "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    if (new Date(searchInfo.startPid) > new Date(searchInfo.endPid)) {
        searchInfo.startPid = searchStart.value;
        ElNotify({ type: "warning", message: "开始时间不能晚于结束时间哦~" });
        return false;
    }
    return true;
}
const handleLoadData = () => {
    if (!checkCanNext()) return;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleClose = () => containerRef.value?.handleClose();
const handleSearch = (row?: ITableItem) => {
    currentPeriodInfo.value = (searchInfo.startPid || " ") + " 至 " + (searchInfo.endPid || " ");
    resetRow();
    loading.value = true;
    const params = {
        dateStart: searchInfo.startPid,
        dateEnd: searchInfo.endPid,
        startVDate: searchInfo.startVDate,
        endVDate: searchInfo.endVDate,
        vgId: searchInfo.vgId,
        startVNum: searchInfo.startVNum,
        endVNum: searchInfo.endVNum,
        cdAccount: searchInfo.CDAccount,
        cdAccountIn: searchInfo.CDAccountIn,
        onlyUnRelationVoucherData: false,
        needVtInfo: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    request({ url: "/api/JournalTransfer/PagingList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                const list = res.data.rows.map((item: ITableItem) => {
                    item.new = false;
                    item.showDelete = true;
                    return item;
                });
                const canClick = IsCanAdd() && (res.data.total <= paginationData.pageSize || res.data.total <= paginationData.currentPage * paginationData.pageSize);
                if (canClick) {
                    if (row) {
                        const newRow: ITableItem = JSON.parse(JSON.stringify(row));
                        newRow.old_cd_date = newRow.cd_date;
                        newRow.cd_date = row.newDate as string;
                        newRow.cd_date_text = row.newDate as string;
                        newRow.new = row.new;
                        list.splice(list.length - 1, 0, newRow);
                    } else {
                        let index = list.length > 0 ? list.length - 1 : 0;
                        list.splice(index, 0, getInitAddRow());
                    }
                }
                tableData.value = list.map((item: ITableItem, index: number) => {
                    item.index = index;
                    return item;
                });
                paginationData.total = res.data.total;
                if (row) {
                    canClick && handleRowClick(tableData.value[tableData.value.length - 1]);
                } else {
                    const item = tableData.value.find((item) => item.new);
                    if (item && item.cd_date_text >= checkDate.value && paginationData.currentPage === 1) handleRowClick(item);
                }
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleSearchByDate = (init = false) => {
    request({ url: "/api/JournalTransfer/GetTransferDate", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            searchStart.value = res.data.searchStart;
            searchEnd.value = res.data.searchEnd;
            checkDate.value = res.data.checkDate;
            startDate.value = res.data.startDate;
            searchInfo.startPid = res.data.searchStart;
            searchInfo.endPid = res.data.searchEnd;
            if (init) {
                const { searchStartDate, searchEndDate, vdate, vnum, vgname, acc_in = "", acc_out = "" } = route.query;
                const _s = searchStartDate as string | undefined;
                const _e = searchEndDate as string | undefined;
                if (_s && _e) {
                    searchInfo.startPid = _s;
                    searchInfo.endPid = _e;
                    searchStart.value = _s;
                    searchEnd.value = _e;
                }
                if (vdate) {
                    searchInfo.startVDate = vdate as string;
                    searchInfo.endVDate = vdate as string;
                }
                if (vnum && vgname) {
                    const targetVgid = voucherGroup.find((item) => item.name === vgname)?.id;
                    searchInfo.vgId = targetVgid ?? 1;
                    searchInfo.startVNum = vnum as string;
                    searchInfo.endVNum = vnum as string;
                }
                if (acc_in) {
                    searchInfo.CDAccountIn = acc_in as string;
                }
                if (acc_out) {
                    searchInfo.CDAccount = acc_out as string;
                }
                const timer = setTimeout(() => {
                    tryClearCustomUrlParams(route);
                    clearTimeout(timer);
                });
            }
            handleSearch();
        }
    });
};
const handleGetCurrencyList = async () => {
    await useCurrencyStore().getCurrencyList();
    currencyList.value = [...useCurrencyStore().fcList];
};
const handleInit = async () => {
    await initAccountList().then(async () => {
        if (!window.isErp) {
            await checkFCAccountExistsHistoryData(
                "Transfer",
                accountList.value.map((item) => item.ac_id.toString())
            ).then((res) => {
                if (res.state === 1000) {
                    if (res.data.exists) {
                        let title = "";
                        for (let index = 0; index < res.data.data.length; index++) {
                            const element = res.data.data[index];
                            title +=
                                '<span class="green"> ' +
                                element.accountName +
                                " </span>" +
                                '的币别为 <span class="green"> ' +
                                element.fcName +
                                " </span>，";
                        }
                        syncTitle.value = title;
                        if (res.data.data.length === 1) {
                            syncFcName.value = res.data.data[0].fcName;
                        } else {
                            syncFcName.value = "外币";
                        }
                        fcAccountHistoryDataSync.value = true;
                    }
                } else {
                    ElNotify({ type: "warning", message: "加载失败，请刷新页面重试" });
                }
            });
        }
        handleSearchByDate(true);
    });
    handleGetCurrencyList();
};
const initAccountList = async () => {
    await Promise.all([getAccountListApi(1010), getAccountListApi(1020)]).then((res: any) => {
        accountList.value.splice(0, accountList.value.length);
        if (res[0].state == 1000) accountList.value.push(...res[0].data);
        if (res[1].state == 1000) accountList.value.push(...res[1].data);
        accountList.value = accountList.value.map((item) => {
            return {
                ...item,
                label: item.ac_no + '-' + item.ac_name + (item.state === "1" ? "（已禁用）" : ""),
            }
        });
        enableAccountListAll.value = accountList.value.filter((item: IBankAccountLabelItem) => item.state === "0");
    });
};
const syncFcData = (type: 0 | 1) => {
    fcAccountHistoryDataSync.value = false;
    const fullScreenLoading = FullScreenLoading();
    const cdAccountArray = accountList.value.map((item) => item.ac_id.toString());
    syncFCAccountHistoryData("Transfer", cdAccountArray, type).then((res) => {
        fullScreenLoading.close();
        if (res.state === 1000 && res.data) {
            ElNotify({ type: "success", message: "数据同步成功！" });
        } else {
            ElNotify({ type: "warning", message: "数据同步失败，请重试！" });
        }
        handleSearchByDate(false);
    });
};

let hasMounted = false;
onMounted(async () => {
    await handleInit();
    hasMounted = true;
    window.addEventListener("reloadCDAccount", initAccountList);
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop + "" !== "undefined") {
        setTimeout(() => {
            delete (currentRouterModel as any).stop;
        });
    }
});

const handleSelectionChange = (val: any) => {
    setTimeout(() => {
        editing = true;
    }, 100);
    selectionList.value = (val as ITableItem[]).filter((item: ITableItem) => !item.new);
};
const getPrintOrExportParams = () => {
    return {
        showAll: true,
        date_s: searchInfo.startPid,
        date_e: searchInfo.endPid,
        startVDate: searchInfo.startVDate,
        endVDate: searchInfo.endVDate,
        vgId: searchInfo.vgId,
        startVNum: searchInfo.startVNum,
        endVNum: searchInfo.endVNum,
        cdaccount: searchInfo.CDAccount,
        cdaccountIn: searchInfo.CDAccountIn,
    };
};
const validatorPrint = () => {
    if (searchInfo.startPid === "" || searchInfo.endPid === "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    return true;
};
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "transfer",
   "/api/Journal/PrintTransferTable",{}, true,true, validatorPrint
);

const handleExport = (exportAll:string) => {
    if (!checkCanNext()) return;
    const exportParams: any = getPrintOrExportParams();
    if (exportAll === "all") {
        exportParams.vgId = "";
        exportParams.startVNum = "";
        exportParams.endVNum = "";
        exportParams.startVDate = "";
        exportParams.endVDate = "";
    }
    globalExport("/api/Journal/ExportTransferTable?" + getUrlSearchParams(exportParams) + `&exportAll=${exportAll}`);
};
const downLoadImportTemplate = () => globalExport("/api/Journal/ExportImportTransferTemplate");
const handleShowImportDialog = () => {
    importShow.value = true;
};

const uploadSuccess = (res: IResponseModel<any>) => {
    if (res.state == 1000) {
        if (res.data.result !== "Success") {
            ElNotify({ type: "warning", message: res.data.error });
            return;
        }
        importShow.value = false;
        ElNotify({ type: "success", message: "导入成功！" });
        searchInfo.startPid = res.data.startDate;
        searchInfo.endPid = res.data.endDate;
        if (paginationData.currentPage > 1) {
            paginationData.currentPage = 1;
            return;
        }
        handleSearch();
    } else {
        ElNotify({ type: "warning", message: res.msg });
    }
};

const BatchDelete = () => {
    if (deleting) return;
    if (selectionList.value.length == 0) {
        ElNotify({ type: "warning", message: "请选择凭据后删除" });
        return;
    }
    //由于数据按照时间排序，只需校验第一行即可
    const tDate = new Date(selectionList.value[0].cd_date.replace(/-/g, "/"));
    const cDate = new Date(checkDate.value.replace(/-/g, "/"));
    if (cDate > tDate) {
        ElNotify({ type: "warning", message: "已结账期间内部转账数据不能删除！" });
        return;
    }
    deleting = true;
    const cdDates = selectionList.value.map((item: ITableItem) => item.cd_date).join(",");
    const createdDateArrs = selectionList.value.map((item: ITableItem) => item.created_date_txt).join(",");
    const cdAccounts = selectionList.value.map((item: ITableItem) => item.cd_account).join(",");
    const requestParams = getUrlSearchParams({ cdDates, createdDateArrs, cdAccounts });
    request({ url: "/api/JournalTransfer/Batch?" + requestParams, method: "delete" }).then((res: any) => {
        deleting = false;
        if (res.state == 1000) {
            ElNotify({ type: "success", message: "删除成功" });
            if (tableData.value.length > selectionList.value.length) {
                if (tableData.value.length - 1 === selectionList.value.length && tableData.value[tableData.value.length - 1].new){
                    handleSearchByDate();
                } else {
                    handleSearch();
                }
            } else {
                paginationData.currentPage === 1 ? handleSearchByDate() : paginationData.currentPage--;
            }
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
function genertaeErpVoucher() {
    const params = { billType: "3030", searchStartDate: searchInfo.startPid, searchEndDate: searchInfo.endPid };
    globalWindowOpenPage("/Erp/BusinessVoucher?" + getUrlSearchParams(params), "业务生成凭证");
}

// 凭证
let isGenVoucher = false;
const newVoucher = () => {
    if (isGenVoucher) {
        ElNotify({ type: "warning", message: "正在生成凭证，请稍后" });
        return;
    }
    isGenVoucher = true;
    if (selectionList.value.length === 0) {
        isGenVoucher = false;
        ElNotify({ type: "warning", message: "请选择凭据后生成凭证" });
        return;
    }
    //由于资金数据是按日期顺序排列的，所以只需要校验第一行的日期是否已结账即可
    const tDate = new Date(selectionList.value[0].cd_date.replace(/-/g, "/"));
    const cDate = new Date(checkDate.value.replace(/-/g, "/"));
    if (cDate > tDate) {
        isGenVoucher = false;
        ElNotify({ type: "warning", message: "已结账期间资金数据不能生成凭证数据" });
        return false;
    }

    // 从缓存中读取凭证合并的选项
    if (window.localStorage.getItem("genVoucher-transfer") && selectedList.value.length === 0) {
        const parseObject = JSON.parse(window.localStorage.getItem("genVoucher-transfer") as string) as TransferQueryParameters;
        Object.setPrototypeOf(parseObject, TransferQueryParameters.prototype);
        genVoucherQueryParameters.value = parseObject;
        if (parseObject.mergeDate) {
            selectedList.value.push(1);
        }
        if (parseObject.mergeAccount) {
            selectedList.value.push(8);
        }
        if (parseObject.isMerge && selectedList.value.length === 0) {
            selectedList.value.push(0);
        }
        if (parseObject.mergeCredit) {
            subjectSelectedList.value.push(16);
        }
        if (parseObject.mergeDebit) {
            subjectSelectedList.value.push(32);
        }
    }
    const cdDateArr = selectionList.value.map((item: ITableItem) => item.cd_date).join(",");
    const createdDateArr = selectionList.value.map((item: ITableItem) => item.created_date).join(",");
    const cdAccountArr = selectionList.value.map((item: ITableItem) => item.cd_account).join(",");
    getTransferAndVoucherInfo({ cdDates: cdDateArr, createdDates: createdDateArr, cdAccounts: cdAccountArr });
};
let genVoucherParameters = {};
const getTransferAndVoucherInfo = (params?: {}) => {
    if (params) {
        genVoucherParameters = params;
    }
    request({
        url: "/api/JournalVoucher/TransferGenerateVoucherPreCheck",
        method: "post",
        data: genVoucherParameters,
    })
        .then((res: any) => {
            const data = res as IResponseModel<IGenerateVoucherPreCheckResult>;
            if (data.state === 1000) {
                if (data.data.result) {
                    const parameters = {
                        VoucherSettings: generateVoucherSettingDialogRef.value?.getVoucherSetting(),
                        ...genVoucherParameters,
                    };
                    request({
                        url: "/api/JournalVoucher/TransferGenVoucher",
                        method: "post",
                        data: parameters,
                    })
                        .then((res: any) => {
                            const data = res as IResponseModel<IBatchGenerateVoucherModel<TransferWithVoucherModel>>;
                            if (data.state !== 1000) {
                                isGenVoucher = false;
                                ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
                                loadSuccess.value = true;
                                return;
                            }
                            genVoucherChanged.value = false;
                            if (genVoucherQueryParameters.value.isMerge && !checkMergeVoucher(data.data)) {
                                genVoucherQueryParameters.value.isMerge = false;
                            }

                            generateVoucherView.value?.loadDocumentList(
                                data.data,
                                generateVoucherSettingDialogRef.value?.getVoucherSetting()
                            );
                            currentSlot.value = "list";
                            const timer = setTimeout(() => {
                                isGenVoucher = false;
                                clearTimeout(timer);
                            }, 1000);
                        })
                        .catch(() => {
                            isGenVoucher = false;
                            ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
                            loadSuccess.value = true;
                        });
                } else {
                    isGenVoucher = false;
                    loadSuccess.value = true;
                    ElConfirm(data.data.msg).then((r) => {
                        if (r) {
                            switch (data.data.errorType) {
                                case "Account":
                                    if (checkPermission(["cdaccount-canedit"])) {
                                        globalWindowOpenPage(
                                            "/Cashier/CDAccount?editAcType=" + data.data.acType + "&editAcId=" + data.data.acId,
                                            "账户设置"
                                        );
                                    } else {
                                        ElNotify({ type: "warning", message: "您没有此功能权限！" });
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
                }
            } else if (data.state === 2000) {
                isGenVoucher = false;
                loadSuccess.value = true;
                ElNotify({ message: data.msg || "出现错误，请稍后重试！", type: "warning" });
            } else {
                isGenVoucher = false;
                loadSuccess.value = true;
                ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
            }
        })
        .catch(() => {
            isGenVoucher = false;
            loadSuccess.value = true;
            ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
        });
};

// 如果获取的数据里有错误数据，就不可以合并凭证
const checkMergeVoucher = (data: IBatchGenerateVoucherModel<TransferWithVoucherModel>) => {
    if (data) {
        for (let i = 0; i < data.documentWithVoucherList.length; i++) {
            if (data.documentWithVoucherList[i].document.errorInfo !== "") {
                ElNotify({ message: "亲，生成的凭证存在错误暂不支持合并，请依据错误提示修改凭证后再进行合并哦！", type: "warning" });
                return false;
            }
        }
    }
    return true;
};

function genVoucherChangedHandle() {
    genVoucherChanged.value = true;
}

let isSaving = false;
async function saveGenVoucher() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) {
        ElNotify({ type: "warning", message: "正在保存，请稍后" });
        return;
    }
    isSaving = true;
    const parameters = generateVoucherView.value?.getGenVoucherParameters();
    if (parameters) {
        useLoading().enterLoading("努力加载中，请稍后...");
        request({
            url: "/api/JournalVoucher/FindNeedAutoInsertAccountSubject",
            method: "post",
            data: parameters,
        })
            .then((res: IResponseModel<IGenVoucherNeedInsertAsub>) => {
                const data = res;
                if (data.state === 1000) {
                    new Promise<void>((resolve, reject) => {
                        for (let i = 0; i < parameters.temporaryAccountSubjectList.length; i++) {
                            if (
                                parameters.temporaryAccountSubjectList[i].asubCode
                                    .toString()
                                    .indexOf(parameters.temporaryAccountSubjectList[i].parentAsubCode.toString()) !== 0
                            ) {
                                isSaving = false;
                                useLoading().quitLoading();
                                ElConfirm("您的科目编码长度不足，智能匹配失败，是否立即前往科目编码设置？").then((r) => {
                                    if (r) {
                                        globalWindowOpenPage("/Settings/AccountSubject", "科目");
                                    }
                                });
                                reject();
                                return;
                            }
                        }
                        if (data.data.autoInsertAsubList.length > 0) {
                            const parentAsubs = [];
                            const asubs = [];
                            for (let i = 0; i < data.data.autoInsertAsubList.length; i++) {
                                parentAsubs.push(data.data.autoInsertAsubList[i].parentAsubName);
                                asubs.push(data.data.autoInsertAsubList[i].asubName);
                            }
                            const msg =
                                '<div>' +
                                parentAsubs.join("、") +
                                "已有凭证，将新增同名下级科目" + '<br>' +
                                asubs.join("、") +
                                "替代，您要继续吗？" +
                                "</div>";
                            useLoading().quitLoading();
                            ElAlert({ message: msg, leftJustifying: true }).then((r) => {
                                if (r) {
                                    useLoading().enterLoading("努力加载中，请稍后...");
                                    resolve();
                                } else {
                                    isSaving = false;
                                    reject();
                                }
                            });
                        } else {
                            resolve();
                        }
                    }).then(() => {
                        request({
                            url: "/api/JournalVoucher/Submit",
                            method: "post",
                            data: parameters,
                        })
                            .then((res: any) => {
                                useLoading().quitLoading();
                                const data = res as IResponseModel<ITransferGenVoucherResult>;
                                if (data.state === 1000) {
                                    dispatchReloadAsubAmountEvent();
                                    generateVoucherView.value?.loadSaveResult(data.data);
                                    accountSubjectStore.getAccountSubject();
                                    useAssistingAccountingStore().getAssistingAccounting();
                                    const timer = setTimeout(() => {
                                        isSaving = false;
                                        clearTimeout(timer);
                                    }, 1000);
                                } else {
                                    ElNotify({ message: data.msg || "出现错误，请稍后重试！", type: "warning" });
                                    isSaving = false;
                                }
                            })
                            .catch((err: any) => {
                                if (err.code !== "ERR_NETWORK") {
                                    isSaving = false;
                                    useLoading().quitLoading();
                                }
                            });
                    });
                } else {
                    isSaving = false;
                    useLoading().quitLoading();
                }
            })
            .catch(() => {
                isSaving = false;
                useLoading().quitLoading();
            });
    } else {
        isSaving = false;
    }
}
function genVoucherSaveSuccess() {
    currentSlot.value = "main";
    handleSearch();
}

// 行内操作
let editing = false;
let deleting = false;
let allowBlur = true;
const editNumber = ref(-1);
const editRowInfo = reactive({
    cdDate: "",
    description: "",
    amount: "", //金额
    note: "",
    expenditure: "", //转出账户
    income: "", //转入账户
});
const incomeAccountName = ref("");
const expenditureAccountName = ref("");
const resetRow = () => {
    editNumber.value = -1;
    editRowInfo.cdDate = "";
    editRowInfo.description = "";
    editRowInfo.amount = "";
    editRowInfo.note = "";
    editRowInfo.expenditure = "";
    editRowInfo.income = "";
    allowBlur = true;
};
const judgeIsEditing = () => {
    if (
        (editRowInfo.description !== "" && editRowInfo.description !== tableData.value[editNumber.value].description) ||
        editRowInfo.amount !== "" ||
        editRowInfo.note !== "" ||
        editRowInfo.expenditure !== "" ||
        editRowInfo.income !== ""
    ) {
        return true;
    } else {
        return false;
    }
};
const handleRowClick = (row: ITableItem) => {
    if (!checkPermission(["transfer-canedit"])) return;
    if (row.index === editNumber.value) return;
    if (judgeIsEditing()) {
        // 直接提交上次的编辑
        // allowBlur = false;
        // submitEdit(tableData.value[editNumber.value]);
        return;
    }
    // 已结账不允许编辑
    if (row.cd_date_text < checkDate.value) return;
    if (row.v_num) {
        if (judgeIsEditing()) return;
        ElNotify({ type: "warning", message: "资金数据已生成凭证，请先删除凭证！" });
        return;
    }
    const sameForign = row.fc_code_in === row.fc_code_out; // 相同币别不需要显示千分位
    editNumber.value = row.index as number;
    editRowInfo.cdDate = row.cd_date;
    editRowInfo.description = row.description;
    editRowInfo.note = row.note;
    editRowInfo.expenditure = row.cd_account + "";
    editRowInfo.income = row.cd_account_in + "";
    editRowInfo.amount = sameForign ? row.expenditure + "" : row.expenditure_data.replace("<br>", "");
    // editRowInfo.amount = sameForign ? row.expenditure + "" : row.expenditure_data;
    incomeAccountName.value = row.cd_account_in_name + getForbiddenText(String(row.cd_account_in));
    expenditureAccountName.value = row.cd_account_name + getForbiddenText(String(row.cd_account));

    foriginAmountInfo.outTxt = row.expenditure + "";
    foriginAmountInfo.outRateTxt = row.fc_rate_out + "";
    foriginAmountInfo.inRateTxt = row.fc_rate_in + "";
    foriginAmountInfo.baseTxt = (row.expenditure_standard || row.income_standard) + "";
    foriginAmountInfo.inTxt = row.income + "";
    const inTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
    foriginAmountInfo.inTxtMore = !isFinite(inTxt) ? "" : formatRate(inTxt);
    const outTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.outRateTxt);
    foriginAmountInfo.outTxtMore = !isFinite(outTxt) ? "" : formatRate(outTxt);

    nextTick().then(() => {
        inputRef.value?.focus();
        inputRef.value?.setSelectionRange && inputRef.value?.setSelectionRange(0, editRowInfo.description.length);
    });
};
const handleFocus = (nameRef?: string) => {
    textareaBottom(tableRef);
    if (nameRef === "inputRef") {
        descriptionTextareaShow.value = true;
    } else if (nameRef === "noteRef") {
        noteTextareaShow.value = true;
    }
    nextTick(() => {
        if (nameRef) {
            getTextareaFocus(nameRef);
        }
    });
    editing = true;
};
const getTextareaFocus = (nameRef: string) => {
    if (nameRef === "inputRef") {
        inputRef.value?.focus();
    } else if (nameRef === "noteRef") {
        noteRef.value?.focus();
    }
};
const datePopperShow = ref(false);
const visibleChange = (val: boolean) => {
    datePopperShow.value = val;
};
const handleDateFocus = () => {
    if (datePopperShow.value) {
        nextTick().then(() => {
            elDatePickerRef.value?.handleClose();
        });
    } else {
        nextTick().then(() => {
            elDatePickerRef.value?.handleOpen();
        });
    }
    setTimeout(() => {
        editing = true;
    }, 150);
};
const handleBlur = (row: ITableItem, column?: string) => {
    if (column === "description") {
        descriptionTextareaShow.value = false;
    } else if (column === "note") {
        noteTextareaShow.value = false;
    }
    editing = false;
    setTimeout(() => {
        if (!allowBlur) return;
        if (editing) return;
        if (row.new && (editRowInfo.expenditure === "" || editRowInfo.income === "")) return;
        submitEdit(row, true);
    }, 350);
};
let isRequesting = false;
interface ISubmitInfo {
    cd_date: string;
    description: string;
    cd_account: string;
    cd_account_in: string;
    note: string;
    standard: string;
    fc_rate_in: string;
    fc_rate_out: string;
    income: string;
    expenditure: string;
    created_date?: string;
    CDDate?: string;
    CDAccount?: string;
}
let useLastRow = false;
const submitEdit = (row: ITableItem, isBlur = false) => {
    const editRow = isBlur ? tableData.value[editNumber.value] : row;
    if (isRequesting) return;
    let passValid = true;
    let params: ISubmitInfo;
    let method: "post" | "put" = "post";
    if (editRow.new) {
        method = "post";
        params = {
            description: editRowInfo.description,
            cd_date: editRowInfo.cdDate,
            cd_account: editRowInfo.expenditure,
            cd_account_in: editRowInfo.income,
            note: editRowInfo.note,
            standard: foriginAmountInfo.baseTxt,
            fc_rate_in: foriginAmountInfo.inRateTxt,
            fc_rate_out: foriginAmountInfo.outRateTxt,
            income: foriginAmountInfo.inTxtMore,
            expenditure: foriginAmountInfo.outTxtMore,
        };
    } else {
        method = "put";
        params = {
            description: editRowInfo.description,
            created_date: editRow.created_date,
            cd_date: editRowInfo.cdDate,
            cd_account: editRowInfo.expenditure,
            cd_account_in: editRowInfo.income,
            note: editRowInfo.note,
            standard: foriginAmountInfo.baseTxt,
            fc_rate_in: foriginAmountInfo.inRateTxt,
            fc_rate_out: foriginAmountInfo.outRateTxt,
            income: foriginAmountInfo.inTxtMore,
            expenditure: foriginAmountInfo.outTxtMore,
            CDAccount: editRow.cd_account + "",
            CDDate: editRow.cd_date,
        };
        if (editRow.old_cd_date) {
            params.CDDate = editRow.old_cd_date;
        }
    }
    if (
        params.cd_date != "" &&
        params.description != "" &&
        params.cd_account != "" &&
        params.cd_account_in != "" &&
        params.expenditure != ""
    ) {
        if (params.cd_account === params.cd_account_in && params.cd_account && params.cd_account_in) {
            ElNotify({ type: "warning", message: "转出账户和转入账户不能一致!" });
            editRowInfo.income = "";
            incomeAccountName.value = "";
            useLastRow = true;
            passValid = false;
            editing = false;
            return;
        }
    } else {
        if (params.description == "" && !editRow.new) {
            ElNotify({ type: "warning", message: "亲，请输入摘要！" });
            passValid = false;
            editing = false;
            return;
        }
        if (!params.cd_account && !params.cd_account && !editRow.new) {
            ElNotify({ type: "warning", message: "亲，请选择转出账户！" });
            passValid = false;
            editing = false;
            return;
        }
        if (!params.cd_account_in && !editRow.new) {
            ElNotify({ type: "warning", message: "亲，请选择转入账户！" });
            passValid = false;
            editing = false;
            return;
        }
    }
    if (passValid) {
        if (params.note.length > 256) {
            //这个的数据库字段长度是256
            ElNotify({ type: "warning", message: "亲，备注最多输入256个字！" });
            passValid = false;
            editing = false;
        }
        //新增，数据量不能大于64个unicode字符
        if (params.description.length > 256) {
            ElNotify({ type: "warning", message: "亲，摘要最多输入256个字！" });
            passValid = false;
            editing = false;
        }
        if (params.description.trim().length === 0) {
            ElNotify({ type: "warning", message: "亲，请输入摘要!" });
            passValid = false;
            editing = false;
        }
    }
    if (passValid) {
        if (params.standard === "" || params.income === "" || params.expenditure === "") {
            ElNotify({ type: "warning", message: "请填写金额！" });
            editing = false;
            return;
        }
        isRequesting = true;
        request({
            url: "/api/JournalTransfer",
            method,
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            data: params,
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: res.msg || res.data || "保存失败" });
                    return;
                }
                if (res.data?.includes("Success")) {
                    ElNotify({ type: "success", message: "保存成功" });
                    handleForiginAmountDialogClose();
                    handleSearch();
                    resetRow();
                } else {
                    ElNotify({ type: "warning", message: res.msg || res.data || "保存失败" });
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存失败" });
            })
            .finally(() => {
                useLastRow = false;
                editing = false;
                isRequesting = false;
            });
    }
};
const deleteItem = (row: ITableItem) => {
    if (row.v_num2 !== "") {
        ElNotify({
            message: "资金数据已生成凭证，请先删除凭证！",
            type: "warning",
        });
        return;
    }
    const params = {
        cdDate: row.cd_date,
        createdDate: row.created_date_txt,
        cdAccount: row.cd_account,
    };
    request({ url: "/api/JournalTransfer?" + getUrlSearchParams(params), method: "delete" }).then((res: any) => {
        if (res.state == 1000) {
            ElNotify({ type: "success", message: "删除成功" });
            if (tableData.value.length > 2) {
                handleSearch();
            } else {
                paginationData.currentPage === 1 ? handleSearchByDate() : paginationData.currentPage--;
            }
        }
    });
};
const goToDetail = (row: ITableItem) => {
    const params = {
        pid: row.p_id,
        vid: row.v_id,
        fcode: "transfer-cancreatevoucher",
        from: "transfer",
    };
    globalWindowOpenPage("/Voucher/VoucherPage?" + getUrlSearchParams(params), "查看凭证");
};
const handleCalendarChange = (date: string) => {
    let maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 5);
    const value = new Date(date);
    const item = getRowData();
    if (value < new Date(startDate.value)) {
        ElNotify({ type: "warning", message: "不能选择账套启用前的期间" });
        editRowInfo.cdDate = getshortdata(item.cd_date);
        return;
    } else if (value < new Date(checkDate.value)) {
        ElNotify({ type: "warning", message: "不能选择已结账的日期" });
        editRowInfo.cdDate = getshortdata(item.cd_date);
        return false;
    } else if (value > new Date(maxDate)) {
        ElNotify({ type: "warning", message: "最晚只能录入当前日期五年内的数据" });
        editRowInfo.cdDate = getshortdata(item.cd_date);
        return false;
    } else {
        if (value < new Date(GetStart(getshortdata(searchInfo.startPid))) || value > new Date(getshortdata(searchInfo.endPid))) {
            ElConfirm("您录入的日期是" + date + "，系统将跳转到该日期的归属期间，请问是否确定？").then((r: any) => {
                if (r) {
                    const row = tableData.value[editNumber.value];
                    ChangeDate(date, row);
                } else {
                    editRowInfo.cdDate = getshortdata(item.cd_date);
                    return false;
                }
            });
        } else {
            const row = tableData.value[editNumber.value];
            submitEdit(row);
        }
    }
};
const ChangeDate = (date: string, row: ITableItem) => {
    const newRow = _.cloneDeep(row);
    // date 格式为 YYYY-MM-DD
    let time: string[] = date.split("-");
    const _s = time[0] + "-" + time[1] + "-01";
    const _e = date;
    searchInfo.startPid = _s;
    searchInfo.endPid = _e;
    paginationData.currentPage = 1;
    newRow.newDate = date;
    handleSearch(newRow);
};

const getRowData = () => {
    return tableData.value.find((item) => item.index === editNumber.value) as ITableItem;
}
const getRowNewStatus = () => {
    const row = getRowData();
    return row?.new || false;
}
const hanleInputVaule = (value: string, flag: boolean = false) => {
    value = value.replace(/[^0-9.]/g, "");
    let integer = value.split(".")[0];
    const decimal = value.split(".")[1];
    if (integer.length > 9) {
        integer = integer.slice(0, 9);
    }
    let decimalStr = "";
    if (decimal) {
        const sliceStr = decimal.slice(0, flag ? 8 : 2); //汇率8位小数，金额2位小数
        decimalStr = sliceStr;
    }
    let str = integer;
    if (value.includes(".")) {
        str = integer + "." + (decimalStr ? decimalStr : "");
    } else {
        str = integer + (decimalStr ? "." + decimalStr : "");
    }
    return str;
}
const getPropLabel = (mainProp: string, subProp: string) => {
    const mainTxt = mainProp + 'Txt' as keyof typeof foriginAmountInfo;
    const mainRateTxt = mainProp + 'RateTxt' as keyof typeof foriginAmountInfo;
    const mainTxtMore = mainProp + 'TxtMore' as keyof typeof foriginAmountInfo;
    const subTxt = subProp + 'Txt' as keyof typeof foriginAmountInfo;
    const subRateTxt = subProp + 'RateTxt' as keyof typeof foriginAmountInfo;
    const subTxtMore = subProp + 'TxtMore' as keyof typeof foriginAmountInfo;
    return {
        mainTxt,
        mainRateTxt,
        subTxt,
        subRateTxt,
        mainTxtMore,
        subTxtMore,
    }
}
function getOriginRate() {
    const row = getRowData();
    const expenditureCurrencyItem = findFcItemByID(showExpenditureItem.currency);
    const incomeCurrencyItem = findFcItemByID(showIncomeItem.currency);
    const outRate = (row.new ? expenditureCurrencyItem.rate : row.fc_rate_out) + "" || "0";
    const inRate = (row.new ? incomeCurrencyItem.rate : row.fc_rate_in) + "" || "0";
    return { outRate, inRate };
}
const formatRate = (rate: number) => {
    const decimalPart = String(rate).split('.')[1];
    const decimalLength = decimalPart ? decimalPart.length : 0;
    return (decimalLength < 8 ? rate : rate.toFixed(8)) + "";
}
const foriginAmountIsvalue= ref(false);  //转出转入本币是否都有值
const handleOutInChange = (e: Event, mainProp: string, subProp: string) => {
    const {mainTxt, mainRateTxt, subTxt, subRateTxt, mainTxtMore, subTxtMore} = getPropLabel(mainProp, subProp);
    const value = (e.target as HTMLInputElement).value;
    (foriginAmountInfo[mainTxt] as string) = hanleInputVaule(value);
    (foriginAmountInfo[mainTxtMore] as string) = hanleInputVaule(value);
    const base = Number(foriginAmountInfo[mainTxt]) * Number(foriginAmountInfo[mainRateTxt]);
    foriginAmountInfo.baseTxt = base + "" === "NaN" ? "" : _.round(base, 2);
    if (!foriginAmountIsvalue.value) {
        const txt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo[subRateTxt]);
        (foriginAmountInfo[subTxt] as string)= !isFinite(txt) ? "" : _.round(txt, 2);
        (foriginAmountInfo[subTxtMore] as string)= !isFinite(txt) ? "" : formatRate(txt);
    } else {
        const { outRate, inRate } = getOriginRate();
        let rateTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo[subTxtMore]);
        if (rateTxt === 0 || !isFinite(rateTxt)) {
            rateTxt = subProp === 'out' ? Number(outRate) : Number(inRate);
        }
        (foriginAmountInfo[subRateTxt] as string) = formatRate(rateTxt);
    }
}
const handleBaseInput = (e: Event) => {
    const value = (e.target as HTMLInputElement).value;
    foriginAmountInfo.baseTxt = hanleInputVaule(value);
    if (!foriginAmountIsvalue.value) {
        const inTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
        foriginAmountInfo.inTxt = !isFinite(inTxt) ? "" : _.round(inTxt, 2);
        foriginAmountInfo.inTxtMore = !isFinite(inTxt) ? "" : formatRate(inTxt);
        const outTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.outRateTxt);
        foriginAmountInfo.outTxt = !isFinite(outTxt) ? "" : _.round(outTxt, 2);
        foriginAmountInfo.outTxtMore = !isFinite(outTxt) ? "" : formatRate(outTxt);
    } else {
        const { outRate, inRate } = getOriginRate();
        let inRateTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inTxtMore);
        let outRateTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.outTxtMore);
        if (inRateTxt === 0 || !isFinite(inRateTxt)) {
            inRateTxt = Number(inRate);
        }
        if (outRateTxt === 0 || !isFinite(outRateTxt)) {
            outRateTxt = Number(outRate);
        }
        foriginAmountInfo.inRateTxt = formatRate(inRateTxt);
        foriginAmountInfo.outRateTxt = formatRate(outRateTxt);
    }
}
const handleRateOutIn = (e: Event, mainProp: string, subProp: string) => {
    const {mainTxt, mainRateTxt, subTxt, subRateTxt, mainTxtMore, subTxtMore} = getPropLabel(mainProp, subProp);
    const rate = (e.target as HTMLInputElement).value;
    (foriginAmountInfo[mainRateTxt] as string) = hanleInputVaule(rate, true);
    const base = Number(foriginAmountInfo[mainTxtMore]) * Number(foriginAmountInfo[mainRateTxt]);
    foriginAmountInfo.baseTxt = base + "" === "NaN" ? "" : _.round(base, 2);
    if (!foriginAmountIsvalue.value) {
        const inTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo[subRateTxt]);
        (foriginAmountInfo[subTxt] as string) = !isFinite(inTxt) ? "" : _.round(inTxt, 2);
        (foriginAmountInfo[subTxtMore] as string) = !isFinite(inTxt) ? "" : formatRate(inTxt);
    } else{
        const inRateTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo[subTxtMore]);
        (foriginAmountInfo[subRateTxt] as string) = !isFinite(inRateTxt) ? "" : formatRate(inRateTxt);
    }
};
const handleRateOutInBlur = (mainProp: string, subProp: string) => {
    const {mainTxt, mainRateTxt, subTxt, subRateTxt, mainTxtMore} = getPropLabel(mainProp, subProp);
    const { outRate, inRate } = getOriginRate();
    if (Number(foriginAmountInfo[mainRateTxt]) === 0) {
        (foriginAmountInfo[mainRateTxt]as string) = mainProp === 'out' ? outRate : inRate;
    }
    if (Number(foriginAmountInfo[subRateTxt]) === 0) {
        (foriginAmountInfo[subRateTxt]as string) = subProp === 'out' ? outRate : inRate;
    }
    const base = Number(foriginAmountInfo[mainTxtMore]) * Number(foriginAmountInfo[mainRateTxt]);
    foriginAmountInfo.baseTxt = base + "" === "NaN" ? "" : _.round(base, 2);
}
const handleAmountOutInBlur = (mainProp: string) => {
    let mainTxt = mainProp + 'Txt' as keyof typeof foriginAmountInfo
    (foriginAmountInfo[mainTxt] as string) = Number(foriginAmountInfo[mainTxt]) + "";
}
const handleAmountOutInFocus = (mainProp: string) => {
    let mainTxt = mainProp + 'Txt' as keyof typeof foriginAmountInfo
    (foriginAmountInfo[mainTxt] as string) = Number(foriginAmountInfo[mainTxt])!== 0 ? Number(foriginAmountInfo[mainTxt]) + "" : "";
    foriginAmountIsvalue.value = getBeforeValStatus();
}
const handleRateOutInFocus = () => {
    foriginAmountIsvalue.value = getBeforeValStatus();
}
//设置输入前值状态
function getBeforeValStatus() {
    return (Number(foriginAmountInfo.outTxt) !== 0) 
            && (Number(foriginAmountInfo.inTxt) !== 0)
            && (Number(foriginAmountInfo.baseTxt) !== 0);
}

// 金额
const foriginAmountInfo = reactive({
    showWay: "",
    show: false,
    baseTxt: "0", //本币
    outTxt: "0", //支出原币用于展示
    outRateTxt: "0", //支出汇率
    inTxt: "0", //收入原币用于展示
    inRateTxt: "0", //收入汇率
    inTxtMore: "0", //收入原币8位用于保存
    outTxtMore: "0", //收入原币8位用于保存
});
//重置金额
const resetForiginAmountInfo = () => {
    foriginAmountInfo.show = false;
    foriginAmountInfo.showWay = "";
    foriginAmountInfo.baseTxt = "0";
    foriginAmountInfo.outTxt = "0";
    foriginAmountInfo.outRateTxt = "0";
    foriginAmountInfo.inTxt = "0";
    foriginAmountInfo.inRateTxt = "0";
    foriginAmountInfo.inTxtMore = "0";
    foriginAmountInfo.outTxtMore = "0";
    
};
const handleForiginAmountDialogSave = () => {
    if (Number(foriginAmountInfo.outTxt) === 0) {
        ElNotify({ type: "warning", message: `请输入${foriginAmountInfo.showWay !== '' ? '转出' : ''}原币` });
        return;
    }
    if (isNaN(Number(foriginAmountInfo.outRateTxt))) {
        ElNotify({ type: "warning", message: `请输入${foriginAmountInfo.showWay !== '' ? '转出' : ''}汇率` });
        return;
    }
    if (Number(foriginAmountInfo.inTxt) === 0) {
        ElNotify({ type: "warning", message: `请输入${foriginAmountInfo.showWay !== '' ? '转入' : ''}原币` });
        return;
    }
    if (isNaN(Number(foriginAmountInfo.inRateTxt))) {
        ElNotify({ type: "warning", message: `请输入${foriginAmountInfo.showWay !== '' ? '转入' : ''}汇率` });
        return;
    }
    if (Number(foriginAmountInfo.baseTxt) === 0) {
        ElNotify({ type: "warning", message: "请输入本币" });
        return;
    }
    submitEdit(tableData.value[editNumber.value]);
}

const cancelAndSearch = () => {
    resetForiginAmountInfo();
    resetRow();
};
const handleForiginAmountDialogCancel = () => {
    allowBlur = true;
    editing = false;
    const item = tableData.value[editNumber.value];
    if (!item) {
        resetForiginAmountInfo();
        return;
    }
    if (!~~foriginAmountInfo.baseTxt) {
        if (item.new) {
            ElNotify({ type: "warning", message: "请填写金额！" });
            foriginAmountInfo.show = false;
            foriginAmountInfo.showWay = "";
        } else {
            cancelAndSearch();
        }
        return;
    }
    if (item.new) {
        foriginAmountInfo.show = false;
        foriginAmountInfo.showWay = "";
    } else {
        cancelAndSearch();
    }
};
const handleForiginAmountDialogClose = () => {
    resetForiginAmountInfo();
    allowBlur = true;
    editing = false;
};
const findFcItemByID = (id: string) => {
    const item = currencyList.value.find((item) => item.id + "" === id) as ICurrency;
    return item;
};
let showExpenditureItem:IBankAccountLabelItem;
let showIncomeItem:IBankAccountLabelItem;
function getAccountItem(val: string) {
    return accountList.value.find((item) => item.ac_id === val) as IBankAccountLabelItem;
}
function getForbiddenText(val: string) {
    return getAccountItem(val)?.state === '1' ? "(已禁用)" : "";
}
const outTxtRef = ref();
const handleAmountFocus = (event: Event) => {
    const row = getRowData();
    if (editRowInfo.expenditure === "") {
        (event.target as HTMLInputElement).blur();
        handleForiginAmountDialogClose();
        ElNotify({ type: "warning", message: "请先选择转出账户！" });
        editing = true;
        expenditureSelectRef.value?.focus();
        return;
    }
    if (editRowInfo.income === "") {
        (event.target as HTMLInputElement).blur();
        handleForiginAmountDialogClose();
        ElNotify({ type: "warning", message: "请先选择转入账户！" });
        editing = true;
        incomeSelectRef.value?.focus();
        return;
    }
    const amountTimer = setTimeout(() => {
        clearTimeout(amountTimer);
        editing = true;
        allowBlur = false;
        const expenditureItem = getAccountItem(editRowInfo.expenditure);
        const incomeItem = getAccountItem(editRowInfo.income);
        const expenditureCurrencyItem = findFcItemByID(expenditureItem.currency);
        const incomeCurrencyItem = findFcItemByID(incomeItem.currency);
        showExpenditureItem = _.cloneDeep(expenditureItem);
        showIncomeItem = _.cloneDeep(incomeItem);
        if (expenditureItem?.standard === "1" && incomeItem?.standard === "0") {
            // 外币转本币
            const rate = row.new ? expenditureCurrencyItem.rate : row.fc_rate_out;
            foriginAmountInfo.outRateTxt = rate + "" || "0";
            // 原币
            foriginAmountInfo.outTxt = row.expenditure + "" || "0";
            foriginAmountInfo.outTxtMore = row.expenditure + "" || "0";
            // 本币
            const computedMoney = Number(foriginAmountInfo.outTxt) * Number(foriginAmountInfo.outRateTxt);
            foriginAmountInfo.baseTxt = computedMoney + "" === "NaN" ? "0" : _.round(computedMoney, 2);

            foriginAmountInfo.show = true;
            foriginAmountInfo.showWay = "out";
            amountInputRef.value?.blur();
            const focusTimer = setTimeout(() => {
                clearTimeout(focusTimer);
                amountTxtRef.value?.focus();
            }, 100);
        } else if (expenditureItem?.standard === "0" && incomeItem?.standard === "1") {
            // 本币转外币
            const rate = row.new ? incomeCurrencyItem.rate : row.fc_rate_in;
            foriginAmountInfo.inRateTxt = rate + "" || "0";
            // 本币
            foriginAmountInfo.baseTxt = row.expenditure_standard + "" || "0";
            // 原币
            const computedMoney = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
            foriginAmountInfo.inTxt = computedMoney + "" === "NaN" ? "0" : _.round(computedMoney, 2);
            foriginAmountInfo.inTxtMore = computedMoney + "" === "NaN" ? "0" : formatRate(computedMoney);

            foriginAmountInfo.show = true;
            foriginAmountInfo.showWay = "in";
            amountInputRef.value?.blur();
            const focusTimer = setTimeout(() => {
                clearTimeout(focusTimer);
                amountStdTxtRef.value?.focus();
            }, 100);
        } else if (expenditureItem?.standard === "1" && incomeItem?.standard === "1" && incomeItem?.currency !== expenditureItem?.currency) {
            // 外币转外币
            //本币
            foriginAmountInfo.baseTxt = row.income_standard + "" || "0";
            //转出
            const outRate = row.new ? expenditureCurrencyItem.rate : row.fc_rate_out;
            const out = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.outRateTxt);
            foriginAmountInfo.outRateTxt = outRate + "" || "0";
            foriginAmountInfo.outTxt = row.expenditure + "" || "0";
            foriginAmountInfo.outTxtMore = out + "" === "NaN" ? "0" : formatRate(out);
            //转入
            const inRate = row.new ? incomeCurrencyItem.rate : row.fc_rate_in;
            const ins = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
            foriginAmountInfo.inRateTxt = inRate + "" || "0";
            foriginAmountInfo.inTxt = row.income + "" || "0";
            foriginAmountInfo.inTxtMore = ins + "" === "NaN" ? "0" : formatRate(ins);

            foriginAmountInfo.show = true;
            foriginAmountInfo.showWay = "all";
            amountInputRef.value?.blur();
            const focusTimer = setTimeout(() => {
                clearTimeout(focusTimer);
                outTxtRef.value?.focus();
            }, 100);
        } else {
            allowBlur = true;
        }
    }, 100);
};

function getCurrencyCode(data: IBankAccountLabelItem) {
    return findFcItemByID(data.currency).code;
}

const handleAmountKeyDown = (e: KeyboardEvent) => {
    const target = e.target as HTMLInputElement;
    const allValidKeys = [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        ".",
        "-",
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Enter",
    ];
    const validKeys = ["Backspace", "Delete", "ArrowLeft", "ArrowRight"];
    const regex = /(-?\d+)\.(\d+)/;
    const preValue = target.value.match(regex)?.[1] || "";
    const isCopy = (e.ctrlKey && e.key === "v") || (e.metaKey && e.key === "v");
    const unInput =
        (!allValidKeys.includes(e.key) && !isCopy) ||
        (target.selectionStart &&
            preValue.replace(/.-/g, "")?.length > 9 &&
            target.selectionStart < preValue.length &&
            !validKeys.includes(e.key));
    if (unInput) {
        e.preventDefault();
        return;
    }
};
let isPaste = false;
const handleAmountPaste = (e: ClipboardEvent) => {
    isPaste = true;
};
const handleAmountInput = (e: Event) => {
    let inputValue = (e.target as HTMLInputElement).value;
    if (isPaste) {
        inputValue = inputValue.replace(/,/g, "");
    }
    let regex = /^(-)?([0-9]{1,9}(\.[0-9]{0,2})?)?$/;
    let matchedValue = inputValue.match(regex);
    if (matchedValue) {
        editRowInfo.amount = matchedValue[0];
    } else {
        let extractedValue = inputValue.match(/(-)?\d{1,9}(\.\d{0,2})?/);
        editRowInfo.amount = extractedValue ? extractedValue[0] : "";
    }
    isPaste = false;
};
const handleStdChange = (e: Event) => {
    const value = (e.target as HTMLInputElement).value;
    foriginAmountInfo.baseTxt = hanleInputVaule(value);
    foriginAmountInfo.outTxt = foriginAmountInfo.baseTxt;
    const amount = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
    foriginAmountInfo.inTxt = !isFinite(amount) ? "" : _.round(amount, 2);
    foriginAmountInfo.inTxtMore = !isFinite(amount) ? "" : formatRate(amount);
    foriginAmountInfo.outTxtMore = foriginAmountInfo.outTxt;
};
const handleAmountTxtChange = (e: Event) => {
    const value = (e.target as HTMLInputElement).value;
    foriginAmountInfo.outTxt = hanleInputVaule(value);
    const amount = Number(foriginAmountInfo.outTxt) * Number(foriginAmountInfo.outRateTxt);
    foriginAmountInfo.baseTxt = amount + "" === "NaN" ? "" : _.round(amount, 2);
    foriginAmountInfo.inTxt = foriginAmountInfo.baseTxt;
    foriginAmountInfo.inTxtMore = foriginAmountInfo.baseTxt;
    foriginAmountInfo.outTxtMore = foriginAmountInfo.outTxt;
};
const handleRateInput = (e: Event) => {
    let rate = (e.target as HTMLInputElement).value;
    if (foriginAmountInfo.showWay === "out") {
        foriginAmountInfo.outRateTxt = hanleInputVaule(rate, true);
        const amount = Number(foriginAmountInfo.outRateTxt) * Number(foriginAmountInfo.outTxt);
        foriginAmountInfo.baseTxt = amount + "" === "NaN" ? "" : _.round(amount, 2);
        foriginAmountInfo.inTxt = foriginAmountInfo.baseTxt;
        foriginAmountInfo.inTxtMore = foriginAmountInfo.inTxt;
        return;
    } 
    if (foriginAmountInfo.showWay === "in") {
        foriginAmountInfo.inRateTxt = hanleInputVaule(rate, true);
        const amount = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
        foriginAmountInfo.inTxt = !isFinite(amount) ? "" : _.round(amount, 2);
        foriginAmountInfo.inTxtMore = !isFinite(amount) ? "" : formatRate(amount);
        return;
    }
};
const handleRateInputBlur = () => {
    const row = getRowData();
    const expenditureCurrencyItem = findFcItemByID(showExpenditureItem.currency);
    const incomeCurrencyItem = findFcItemByID(showIncomeItem.currency);
    const outRate = (row.new ? expenditureCurrencyItem.rate : row.fc_rate_out) + "" || "0";
    const inRate = (row.new ? incomeCurrencyItem.rate : row.fc_rate_in) + "" || "0";
    if (foriginAmountInfo.showWay === "out") {
        if (Number(foriginAmountInfo.outRateTxt) === 0) {
            foriginAmountInfo.outRateTxt = outRate;
        }
        const amount = Number(foriginAmountInfo.outRateTxt) * Number(foriginAmountInfo.outTxt);
        foriginAmountInfo.baseTxt = amount + "" === "NaN" ? "" : _.round(amount, 2);
        foriginAmountInfo.inTxt = foriginAmountInfo.baseTxt;
        foriginAmountInfo.inTxtMore = foriginAmountInfo.inTxt;
        return;
    } 
    if (foriginAmountInfo.showWay === "in") {
        if (Number(foriginAmountInfo.inRateTxt) === 0) {
            foriginAmountInfo.inRateTxt = inRate;
        }
        const amount = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
        foriginAmountInfo.inTxt = !isFinite(amount) ? "" : _.round(amount, 2);
        foriginAmountInfo.inTxtMore = !isFinite(amount) ? "" : formatRate(amount);
        return;
    }
}

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    handleSearch();
});
// 无论修改转入转出账户,都要保持转出金额不变
function handleExpenditureChange(val: string) {
    editRowInfo.expenditure = val;
    const incomeItem = getAccountItem(editRowInfo.income);
    const expenditureItem = getAccountItem(val);
    expenditureItem && (expenditureAccountName.value = expenditureItem.label);
    handleAccountChangeData(incomeItem, expenditureItem);
}
function handleIncomeChange(val: string) {
    editRowInfo.income = val;
    const incomeItem = getAccountItem(val);
    const expenditureItem = getAccountItem(editRowInfo.expenditure);
    incomeItem && (incomeAccountName.value = incomeItem.label);
    handleAccountChangeData(incomeItem, expenditureItem);
}
function handleAccountChangeData(incomeItem: IBankAccountLabelItem, expenditureItem: IBankAccountLabelItem) {
    const row = tableData.value[editNumber.value];
    if (incomeItem && expenditureItem) {
        //保持转出原币金额不变，转出税率为选择的账户币种税率；转入税率不变
        foriginAmountInfo.outTxt = row.expenditure + "";
        foriginAmountInfo.outRateTxt = expenditureItem.fc_rate + "";
        foriginAmountInfo.inRateTxt = incomeItem.fc_rate + "";
        //计算本币和转入原币
        const base = Number(foriginAmountInfo.outRateTxt) * Number(foriginAmountInfo.outTxt);
        foriginAmountInfo.baseTxt = base + "" === "NaN" ? "" : _.round(base, 2);
        const inTxt = Number(foriginAmountInfo.baseTxt) / Number(foriginAmountInfo.inRateTxt);
        foriginAmountInfo.inTxt = inTxt + "" === "NaN" ? "" : _.round(inTxt, 2);
        foriginAmountInfo.inTxtMore = inTxt + "" === "NaN" ? "" : formatRate(inTxt);
        foriginAmountInfo.outTxtMore = foriginAmountInfo.outTxt;
        if (expenditureItem.currency === incomeItem.currency) { //币别相同
            editRowInfo.amount = row.expenditure + "";
        }
    }
    
}
const handleAmountChange = () => {
    const val = editRowInfo.amount;
    const expenditureItem = getAccountItem(editRowInfo.expenditure);
    const incomeItem = getAccountItem(editRowInfo.income);
    if (incomeItem && expenditureItem) {
        if (incomeItem.currency === expenditureItem.currency) {
            foriginAmountInfo.outRateTxt = expenditureItem.fc_rate + "";
            foriginAmountInfo.inRateTxt = incomeItem.fc_rate + "";
            foriginAmountInfo.outTxt = val;
            foriginAmountInfo.inTxt = val;
            foriginAmountInfo.outTxtMore = val;
            foriginAmountInfo.inTxtMore = val;
            const base = Number(foriginAmountInfo.outRateTxt) * Number(foriginAmountInfo.outTxt);
            foriginAmountInfo.baseTxt = base + "" === "NaN" ? "" : _.round(base, 2);
        }
    }
};

const asStartDate = useAccountSetStore().accountSet?.asStartDate ?? "";
function disabledDateStart(time: Date) {
    const accountStartDate = dayjs(asStartDate).valueOf();
    const now = new Date();
    const year = now.getFullYear() + 5;
    const month = now.getMonth();
    const day = now.getDate();
    const maxDate = new Date(year, month, day);
    return time.getTime() < accountStartDate || time.getTime() > dayjs(maxDate).valueOf();
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(searchInfo.startPid).valueOf();
    return time.getTime() < startDate;
}
function disabledStartVDate(time: Date) {
    const accountStartDate = dayjs(asStartDate).valueOf();
    const endVDate = dayjs(searchInfo.endVDate).valueOf();
    return time.getTime() < accountStartDate || time.getTime() > endVDate;
}
function disabledEndVDate(time: Date) {
    let startVDate = dayjs(searchInfo.startVDate).valueOf();
    return time.getTime() < startVDate;
}
function handleShowJournalSettings() {
    voucherSettingDialogShow.value = true;
}

// 生成凭证
const loadSuccess = ref(true);

function genVoucherCheckboxChanged(flag: number, selectedist: any) {
    new Promise<boolean>((resolve) => {
        if (genVoucherChanged.value) {
            ElConfirm("系统可能不会保存您做的更改，确定要切换吗？").then((r) => {
                if (r) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(true);
        }
    }).then((r) => {
        if (r) {
            if (flag > 0) {
                generateVoucherView.value?.loadDocumentList(undefined, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                genVoucherChanged.value = false;
                window.localStorage.setItem("genVoucher-transfer", JSON.stringify(genVoucherQueryParameters.value));
            } else {
                // getTransferAndVoucherInfo();
                generateVoucherView.value?.loadDocumentList(undefined, generateVoucherSettingDialogRef.value?.getVoucherSetting());
                genVoucherChanged.value = false;
            }
        } else if (flag === 1) {
            genVoucherQueryParameters.value.isMerge = !genVoucherQueryParameters.value.isMerge;
            loadSuccess.value = true;
        } else if (selectedist !== undefined) {
            selectedist.value = _.cloneDeep(backSelectedList.value);
            loadSuccess.value = true;
        } else {
            genVoucherQueryParameters.value.isMerge = !genVoucherQueryParameters.value.isMerge;
            loadSuccess.value = true;
        }
    });
}

function mergeVoucher() {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if (genVoucherQueryParameters.value.isMerge && !generateVoucherView.value?.checkMergeVoucher()) {
        genVoucherQueryParameters.value.isMerge = false;
        return;
    }

    loadSuccess.value = false;
    if (genVoucherQueryParameters.value.isMerge) {
        genVoucherQueryParameters.value.mergeOthers = true;
        if (selectedList.value.length === 0 && subjectSelectedList.value.length === 0) {
            selectedList.value.push(0);
            subjectSelectedList.value.push(16);
            subjectSelectedList.value.push(32);
            selectSubjectRef.value?.changeSelectAll(true);
            genVoucherQueryParameters.value.mergeCredit = true;
            genVoucherQueryParameters.value.mergeDebit = true;
        }
    }
    if (genVoucherQueryParameters.value.isMerge) {
        if (genVoucherChanged.value) {
            genVoucherChanged.value = false;
        }
        genVoucherCheckboxChanged(1, selectedList);
    } else {
        genVoucherCheckboxChanged(0, undefined);
    }
}

function checkBoxShowChange(val: boolean) {
    if (!val && selectedList.value.length === 0 && genVoucherQueryParameters.value.isMerge) {
        ElNotify({ message: "凭证合并默认需要勾选一个条件哦~", type: "warning" });
        genVoucherQueryParameters.value.isMerge = true;
        selectedList.value.push(0);
        selectZero.value = true;
        genVoucherCheckboxChanged(1, selectedList);
    }
}

function mergeErrorFunction() {
    genVoucherQueryParameters.value.mergeCredit = false;
    genVoucherQueryParameters.value.mergeDebit = false;
    subjectSelectedList.value = [];
}

const options = ref<Array<Option>>([
    {
        id: 0,
        name: "合并成一张凭证",
    },
    {
        id: 1,
        name: "按日期分别合并",
    },
    {
        id: 8,
        name: "按账户分别合并", // 选择多个账户时才会出现
    },
]);
const subjectOption = ref<Array<Option>>([
    {
        id: 16,
        name: "相同借方科目合并",
    },
    {
        id: 32,
        name: "相同贷方科目合并",
    },
]);
const selectedList = ref<number[]>([]);
const subjectSelectedList = ref<number[]>([]);
const backSelectedList = ref<number[]>([]);
const selectSubjectRef = ref();
const selectZero = ref(true);

function changeSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    if (selectZero.value && val.length > 1 && val[0] === 0) {
        val.splice(0, 1);
        selectZero.value = false;
    } else if (val.findIndex((z) => z === 0) !== -1) {
        selectZero.value = true;
        val.splice(0);
        val.push(0);
    }

    backSelectedList.value = _.cloneDeep(selectedList.value);
    selectedList.value = val;
    genVoucherQueryParameters.value.mergeDate = val.includes(1) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeDirection = val.includes(4) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeAccount = val.includes(8) && genVoucherQueryParameters.value.isMerge;
    genVoucherCheckboxChanged(1, selectedList);
}

function changeSubjectSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    backSelectedList.value = _.cloneDeep(subjectSelectedList.value);
    subjectSelectedList.value = val;
    genVoucherQueryParameters.value.mergeDebit = val.includes(16) && genVoucherQueryParameters.value.mergeOthers;
    genVoucherQueryParameters.value.mergeCredit = val.includes(32) && genVoucherQueryParameters.value.mergeOthers;
    genVoucherCheckboxChanged(2, subjectSelectedList);
}

let cacheRouterQueryParams: any = null;

const currentPath = ref(route.path);
watch(currentSlot, (newSlot) => {
    const newVal = newSlot === "list";
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
let isEditPage = false;
let firstInit = true;
onBeforeRouteLeave((to, from, next) => {
    firstInit = false;
    isEditPage = currentSlot.value === "list";
    cacheRouterQueryParams = from.query;
    next();
});

onUnmounted(() => {
    cacheRouterQueryParams = null;
    window.removeEventListener("reloadCDAccount", initAccountList);
});

onActivated(() => {
    if (!hasMounted) return;
    if (!cacheRouterQueryParams?.from && route.query?.from === "voucherPage") {
        handleSearch();
    } else if (cacheRouterQueryParams?.from === "voucherPage" && cacheRouterQueryParams.r && cacheRouterQueryParams.r !== route.query.r) {
        handleSearch();
    }
    if (isFromOtherPage("voucherList") && !firstInit) {
        if (isEditPage) {
            // 正在编辑票据页面
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
    }
});
const reLoadCurrentPage = () => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if (currentRouterModel.stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        currentRouterModel.stop = true;
    }
};
const isFromOtherPage = (page: "voucherList"): boolean => {
    if (!cacheRouterQueryParams?.from && route.query.from === page) {
        return true;
    }
    if (
        cacheRouterQueryParams?.from === page &&
        route.query?.from === page &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }

    return false;
};

//下拉组件拼音首字母搜索
const showAccountList = ref<Array<IBankAccountLabelItem>>([]);
const showEnableAccountList = ref<Array<IBankAccountLabelItem>>([]);
const enableAccountListAll = ref<Array<IBankAccountLabelItem>>([]);
watchEffect(() => { 
    showAccountList.value = JSON.parse(JSON.stringify(accountList.value));  
    showEnableAccountList.value = JSON.parse(JSON.stringify(enableAccountListAll.value));  
});
function accountFilterMethod(value: string) {
    showAccountList.value = commonFilterMethod(value, accountList.value, 'label');
}
function enableAccountFilterMethod(value: string) {
    showEnableAccountList.value = commonFilterMethod(value, enableAccountListAll.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";
:deep(.el-table .el-table__cell) {
    z-index: auto;
}
:deep(.el-textarea__inner) {
    position: absolute;
    top: 3px;
    left: 8px;
    z-index: 1000;
    width: calc(100% - 16px);
}
:deep(.el-textarea) {
    position: static;
}
:deep(.span_wrap) {
    flex: 1;
}
.format-edit-item(@width) {
    :deep(.el-select) {
        width: @width;
        height: 32px;

        .select-trigger {
            height: 32px;
        }
    }

    :deep(.el-input) {
        height: 32px;

        .el-input__wrapper {
            padding: 0px 4px;

            input.el-input__inner {
                border: none;
            }
        }
    }
}

.ellipsis {
    white-space: nowrap; /* 避免换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 多余文本显示为省略号 */
}
.main-content {
    height: 100%;
    background-color: var(--white);

    .main-top {
        .main-tool-left {
            .filed-large {
                .detail-el-select(300px);
            }

            :deep(.el-select) {
                .el-popper {
                    .el-select-dropdown__item {
                        padding: 6px;
                        padding-left: 8px;
                        line-height: 16px;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        flex-wrap: wrap;
                    }
                }
            }

            & > span.label {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                flex-shrink: 0;
            }

            .picker-select {
                width: 276px !important;
            }
        }
    }

    .main-center {
        box-sizing: border-box;
        padding: 10px;

        :deep(.table) {
            display: flex;
            flex-direction: column;
            height: 100%;
            .el-scrollbar__view {
                min-height: 441px;
            }

            &.erp-table {
                .el-scrollbar__view {
                    min-height: 0px;
                }
            }
        }
    }

    .downlist {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;
            list-style-type: none;
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }

    .down-click {
        width: 19px;
        margin-left: 1px;
        background: url("@/assets/Icons/down-white.png") no-repeat center;
        background-color: var(--main-color);

        & + ul {
            width: 106px !important;
        }

        &:hover {
            border-color: var(--light-main-color);
            background-color: var(--light-main-color);
        }
    }
}

.dialog-content {
    .dialog-main {
        .dialog-item {
            margin-top: 30px;
            margin-left: 40px;
            font-size: var(--font-size);

            &:last-child {
                margin-bottom: 30px;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                flex-wrap: wrap;

                .right {
                    display: flex;

                    .filename {
                        max-width: 165px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }

    .buttons {
        box-sizing: border-box;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
        text-align: center;
    }
}

.none {
    display: none;
}

.content {
    height: 100%;
    .main-content {
        .main-center {
            flex: 1;
        }
    }
}

.main-center {
    :deep(.el-table) {
        flex: 1;
        .el-scrollbar__bar.is-horizontal {
            display: block !important;
        }

        .el-table__cell {
            &.handle {
                .cell {
                    justify-content: center;
                }
            }

            &.is-center {
                .cell {
                    justify-content: center;
                }
            }
            &.is-right {
                .cell {
                    justify-content: flex-end;
                }
            }

            &.is-left {
                .cell {
                    justify-content: flex-start;
                }
            }
        }

        .select-box {
            .cell {
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .cell {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
        }
    }
}

.edit-item {
    width: calc(100% - 2px);
    height: 32px;
    .format-edit-item(100%);

    &.my-data-picker {
        :deep(.el-date-editor--date) {
            height: 100%;
            width: 100%;
            box-sizing: border-box;

            .el-input__wrapper {
                padding: 0;
                padding-left: 8px;

                .el-input__inner {
                    border: none;
                }

                .el-input__prefix {
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }
        }
    }

    & > input {
        .detail-original-input(100%, 100%);
    }
}

.add-ietype-dialog-container {
    padding: 20px 0;
    text-align: center;
    .add-ietype-line-row {
        padding: 0 30px 0 20px;
        display: flex;
        text-align: left;
        .add-ietype-line-item {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 280px;
            box-sizing: border-box;
            &:nth-child(2n + 2) {
                width: 320px;
            }
        }
        & + .add-ietype-line-row {
            margin-top: 16px;
        }
        .add-ietype-line-item + .add-ietype-line-item {
            margin-top: 0;
        }
        .add-ietype-title {
            text-align: right;
        }
        .add-ietype-field {
            > input {
                .detail-original-input(150px, 32px);
            }
        }
    }

    .add-ietype-line-item {
        font-size: 0;

        & + .add-ietype-line-item {
            margin-top: 16px;
        }

        & + .add-ietype-line-row {
            margin-top: 16px;
        }
    }
    .add-ietype-title {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
        display: inline-block;
        vertical-align: middle;
    }

    .add-ietype-field {
        display: inline-block;
        vertical-align: middle;
        text-align: left;

        & > input {
            .detail-original-input(200px, 32px);

            &.amt {
                .detail-spin-button();
            }
        }
    }

    .add-ietype-buttons {
        margin-top: 24px;
    }
}

.fc-account-history-data-sync-content {
    .data-sync-main {
        .set-font;
        padding: 20px 30px;
        text-align: left;

        .data-sync-title {
            font-size: var(--h3);
            margin-bottom: 12px;
            line-height: 25px;
        }

        :deep(.data-sync-title) {
            .green {
                color: #6ed773;
            }
        }

        .data-sync-tip {
            font-size: var(--h5);
            color: #666666;
            line-height: 20px;
            display: flex;
            align-items: flex-start;

            img {
                width: 14px;
                margin-top: 5px;
                margin-right: 5px;
            }
        }
    }

    .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
        font-size: 0;

        .button {
            width: 120px;
        }
    }
}
body[erp] {
    .custom-table {
        :deep(.edit-item) {
            .el-textarea {
                .el-textarea__inner {
                    top: 6px;
                }
            }
        }
    }
    .main-content {
        .main-top {
            .main-tool-left {
                :deep(.el-date-editor) {
                    width: 120px;
                }
            }
        }
    }
}
.el-icon-question {
    margin-top: -36px;
}
</style>
<style lang="less">
.cashier-transfer-import-dialog {
    .import-dialog {
        .import-content {
            flex-direction: row;
            .file-button {
                margin-top: 0;
                margin-left: 20px !important;
            }
            .file-name {
                margin-left: 20px;
            }
        }
    }
}
.el-dialog.foriginAmountInfo-dialog {
    .el-dialog__header {
        display: none;
    }
}
</style>
