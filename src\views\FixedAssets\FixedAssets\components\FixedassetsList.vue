<template>
    <div class="fixedassets-list" style="background-color: var(--white)">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <SearchInfoContainer ref="containerRef">
                    <template v-slot:title>{{ currentPeriodInfo }}</template>
                    <div class="line-item first-item input" ref="settingOptionRef">
                        <div class="line-item-title">会计期间：</div>
                        <div class="line-item-field">
                            <div class="jqtransform float-l date-picker">
                                <el-date-picker
                                    v-model="startMonth"
                                    type="month"
                                    :disabled-date="disabledDate"
                                    :clearable="false"
                                    :editable="false"
                                    :teleported="false"
                                    :value-format="'YYYYMM'"
                                    :format="'YYYY年MM月'"
                                    style="width: 132px"
                                    @change="changeMonth"
                                    @blur="forcedShutdown = false"
                                    @focus="forcedShutdown = true"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">状态：</div>
                        <div class="line-item-field">
                            <el-select
                                v-model="searchInfo.status"
                                :teleported="false"
                                style="width: 132px"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                                :filterable="true"
                                :filter-method="statusFilterMethod"
                            >
                                <el-option 
                                    :label="item.name" 
                                    :value="item.value" 
                                    v-for="item in showStatusList" 
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产增加日期</div>
                        <div class="line-item-field date-picker">
                            <el-date-picker
                                style="width: 132px; height: 32px"
                                v-model="searchInfo.startTime"
                                :teleported="false"
                                :disabled-date="disabledDateStart"
                                type="date"
                                placeholder=" "
                                value-format="YYYY-MM-DD"
                                size="small"
                                clearable
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true" />
                            <span class="s-title item ml-10 mr-10">至</span>
                            <el-date-picker
                                style="width: 132px; height: 32px"
                                v-model="searchInfo.endTime"
                                :teleported="false"
                                :disabled-date="disabledDateEnd"
                                type="date"
                                placeholder=" "
                                value-format="YYYY-MM-DD"
                                size="small"
                                clearable
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true" />
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产编号：</div>
                        <div class="line-item-field">
                            <el-input style="width: 298px" v-model="searchInfo.fa_num" placeholder="" clearable></el-input>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产类别：</div>
                        <div class="line-item-field">
                            <div class="jqtransform float-l">
                                <VirtualSelectCheckbox
                                    :options="faTypeOptions"
                                    :use-el-icon="true"
                                    width="298px"
                                    v-model:selectedList="searchInfo.fa_type"
                                    class="item"
                                    v-model:forced-shutdown="forcedShutdown"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产属性：</div>
                        <div class="line-item-field">
                            <el-select
                                :teleported="false"
                                v-model="searchInfo.fa_property"
                                style="width: 298px"
                                placeholder="  "
                                :fit-input-width="true"
                                :clearable="true"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                                :filterable="true"
                                :filter-method="faPropertyFilterMethod"
                            >
                                <ElOption v-for="item in showfaPropertyList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产名称：</div>
                        <div class="line-item-field">
                            <el-input style="width: 298px" v-model="searchInfo.fa_name" placeholder="" clearable></el-input>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">规格型号</div>
                        <div class="line-item-field">
                            <el-input style="width: 298px" v-model="searchInfo.fa_model" placeholder="" clearable></el-input>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">使用部门：</div>
                        <div class="line-item-field">
                            <div class="jqtransform float-l">
                                <VirtualSelectCheckbox
                                    :options="departmentOptions"
                                    :use-el-icon="true"
                                    width="298px"
                                    v-model:selectedList="searchInfo.department"
                                    class="item"
                                    v-model:forced-shutdown="forcedShutdown"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">备注：</div>
                        <div class="line-item-field">
                            <el-input style="width: 298px" v-model="searchInfo.note" placeholder="" clearable></el-input>
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" @click="handleSearch('sure')">确定</a>
                        <a class="button" @click="handleClose">返回</a>
                        <a class="button" @click="handleReset">重置</a>
                    </div>
                </SearchInfoContainer>
                <ErpRefreshButton></ErpRefreshButton>
            </div>
            <div class="main-tool-right">
                <el-checkbox class="mr-20" v-model="searchInfo.showInfo" label="显示所有信息" @change="handleSearch"></el-checkbox>
                <a
                    class="button mr-10"
                    @click="addAndDelShow ? '' : increaseAssets('2')"
                    :class="addAndDelShow ? 'disabled' : ''"
                    v-permission="['fixedassets-card-canedit']"
                    :title="addAndDelShow ? '当期已计算折旧,不能增加资产' : ''"
                    >增加资产</a
                >
                <a
                    class="button mr-10"
                    @click="addAndDelShow ? '' : copyAssets()"
                    :class="addAndDelShow ? 'disabled' : ''"
                    v-permission="['fixedassets-card-canedit']"
                    :title="addAndDelShow ? '当期已计算折旧,不能增加资产' : ''"
                    >复制资产</a
                >
                <a class="button mr-10" @click="handleImport" v-permission="['fixedassets-card-canimport']">导入</a>
                <Dropdown :btnTxt="'导出'" class="mr-10" :downlistWidth="106" v-permission="['fixedassets-card-canexport']">
                    <li @click="handleExport" style="padding: 0px 6px">导出</li>
                    <li @click="handleExportForImport" style="padding: 0px 6px">按导入模板导出</li>
                </Dropdown>
                <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="isErp ? 76 : 86" v-permission="['fixedassets-card-canprint']">
                    <li @click="printDialogVisible = true">资产列表</li>
                    <li @click="PrintList">资产卡片</li>
                    <li @click="PrintCard">资产标签</li>
                </Dropdown>
                <Dropdown
                    :btnTxt="'批量操作'"
                    :downlistWidth="110"
                    v-permission="['fixedassets-card-canchange', 'fixedassets-card-canexcute', 'fixedassets-card-candelete']">
                    <li @click="batchChange" v-permission="['fixedassets-card-canchange']">批量变更</li>
                    <li @click="batchDispose" v-permission="['fixedassets-card-canexcute']">批量处置</li>
                    <li
                        :class="addAndDelShow ? 'disabled' : ''"
                        :title="addAndDelShow ? '当期已计算折旧,不能增加资产' : ''"
                        @click="addAndDelShow ? '' : Delete('2')"
                        v-permission="['fixedassets-card-candelete']">
                        批量删除
                    </li>
                    <el-tooltip 
                        content="通过导入方式进行批量科目调整"
                        class="tooltip"
                        effect="light"
                        popperClass="el-option-tool-tip"
                        placement="left"
                    >
                        <li style="padding-right:0px" @click="importDialogVisible = true" v-permission="['fixedassets-card-canchange']">
                            批量科目调整
                            <img
                                class="img-question"
                                src="@/assets/Icons/question.png"
                                style="height: 16px; " 
                            />
                        </li>
                    </el-tooltip>
                </Dropdown>
                <RefreshButton></RefreshButton>
            </div>
        </div>
        <div class="main-center">
            <Table
                ref="fixedassetsListRef"
                :data="tableData"
                :columns="columns"
                :loading="loading"
                :pageIsShow="true"
                :layout="paginationData.layout"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :current-page="paginationData.currentPage"
                :selectable="setSelectable"
                :use-normal-scroll="true"
                :scrollbar-show="true"
                @row-click="selectToggle"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @selection-change="selectCard"
                @refresh="handleRerefresh"
                @cell-drop="cellDrop"
                :tableName="setModule"
                @scroll="handleScroll">
                <template #fa_num>
                    <el-table-column
                        :show-overflow-tooltip="false"
                        label=""
                        prop="fa_num"
                        :width="getColumnWidth(setModule, 'fa_num')"
                        :min-width="150"
                        :class-name="'linkColor ' + getSlotClassName('fa_num', columns)"
                        :fixed="getSlotIsFreeze('fa_num', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>资产编号</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faNu', $event)"></div>
                                    <TableHeaderFilter
                                        :prop="'fa_num'"
                                        :isFilter="!!filterSearchInfo.fa_num"
                                        :hasSearchVal="filterSearchInfo.fa_num"
                                        @filterSearch="filterSearch"></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <a
                                v-if="scope.row.fa_num"
                                :title="scope.row.fa_num"
                                class="link"
                                @click="showDetail(scope.row.fa_id, searchInfo.period)">
                                <el-tooltip
                                    class="box-item"
                                    effect="light"
                                    placement="top"
                                    :ref="
                                        (el) => {
                                            ToolNumRef[scope.row.fa_id] = el;
                                        }
                                    ">
                                    <template #content>
                                        <span>
                                            <span id="fa_num">{{ scope.row.fa_num }}</span>
                                            <el-icon class="lineSn-copy" style="color: var(--main-color)" @click="handleCopy"
                                                ><DocumentCopy
                                            /></el-icon>
                                        </span>
                                    </template>
                                    <span class="text-overflow-ellipsis">{{ scope.row.fa_num }}</span>
                                </el-tooltip>
                            </a>
                            <span v-else>{{ "" }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #fa_type>
                    <el-table-column
                        prop="fa_type"
                        label=""
                        :width="getColumnWidth(setModule, 'fa_type')"
                        :min-width="145"
                        :class-name="getSlotClassName('fa_type', columns)"
                        :fixed="getSlotIsFreeze('fa_type', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>资产类别</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faType', $event)"></div>
                                    <TableHeaderFilter
                                        :prop="'fa_type'"
                                        :isSelect="true"
                                        :selectedList="searchInfo.fa_type"
                                        :option="searchOptions.fa_type"
                                        :hasSelectList="filterSearchInfo.fa_type"
                                        :isFilter="isFilterMultile(filterSearchInfo.fa_type, searchOptions.fa_type)"
                                        @filterSearch="filterSearch">
                                    </TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span>{{ scope.row.fa_type }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #fa_name>
                    <el-table-column
                        prop="fa_name"
                        label=""
                        :width="getColumnWidth(setModule, 'fa_name')"
                        :min-width="145"
                        :class-name="getSlotClassName('fa_name', columns)"
                        :fixed="getSlotIsFreeze('fa_name', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>资产名称</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faName', $event)"></div>
                                    <TableHeaderFilter
                                        :prop="'fa_name'"
                                        :isFilter="!!filterSearchInfo.fa_name"
                                        :hasSearchVal="filterSearchInfo.fa_name"
                                        @filterSearch="filterSearch"></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.fa_name }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #fa_model>
                    <el-table-column
                        prop="fa_model"
                        label=""
                        :width="getColumnWidth(setModule, 'fa_model')"
                        :min-width="88"
                        :class-name="getSlotClassName('fa_model', columns)"
                        :fixed="getSlotIsFreeze('fa_model', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>规格型号</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'fa_model'"
                                        :isFilter="!!filterSearchInfo.fa_model"
                                        :hasSearchVal="filterSearchInfo.fa_model"
                                        @filterSearch="filterSearch"></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.fa_model }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #started_date>
                    <el-table-column
                        prop="started_date"
                        label=""
                        :width="getColumnWidth(setModule, 'started_date')"
                        :min-width="118"
                        :class-name="getSlotClassName('started_date', columns)"
                        :fixed="getSlotIsFreeze('started_date', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>开始使用日期</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faStartDate', $event)"></div>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.started_date }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #department>
                    <el-table-column
                        prop="department"
                        label=""
                        :width="getColumnWidth(setModule, 'department')"
                        :min-width="90"
                        :class-name="getSlotClassName('department', columns)"
                        :fixed="getSlotIsFreeze('department', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>使用部门</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'department'"
                                        :isSelect="true"
                                        :selectedList="searchInfo.department"
                                        :option="searchOptions.department"
                                        :hasSelectList="filterSearchInfo.department"
                                        :isFilter="isFilterMultile(filterSearchInfo.department, searchOptions.department)"
                                        @filterSearch="filterSearch">
                                    </TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span>{{ scope.row.department }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #status>
                    <el-table-column
                        prop="status"
                        label=""
                        :width="getColumnWidth(setModule, 'status')"
                        :min-width="90"
                        :class-name="getSlotClassName('status', columns)"
                        :fixed="getSlotIsFreeze('status', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>使用状况</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'status'"
                                        :isSelect="true"
                                        :multiSelect="false"
                                        :SSinglist="statusList"
                                        :hasSSingVal="Number(filterSearchInfo.status)"
                                        :isFilter="Number(filterSearchInfo.status) > 0"
                                        @filterSearch="filterSearch">
                                    </TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span>{{ scope.row.status }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #note>
                    <el-table-column
                        prop="note"
                        label=""
                        :width="getColumnWidth(setModule, 'note')"
                        :min-width="70"
                        :class-name="getSlotClassName('note', columns)"
                        :fixed="getSlotIsFreeze('note', columns)">
                        <template #header>
                            <div class="header-operate">
                                <span>备注</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'note'"
                                        :isFilter="!!filterSearchInfo.note"
                                        :hasSearchVal="filterSearchInfo.note"
                                        @filterSearch="filterSearch"></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.note }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #operation>
                    <el-table-column
                        label="操作"
                        min-width="207"
                        align="left"
                        header-align="left"
                        :show-overflow-tooltip="false"
                        fixed="right"
                        :resizable="false">
                        <template #header>
                            <div class="header-operate">
                                <span>操作</span>
                                <div class="header-operate-rt">
                                    <div class="set-icon" @click="openColSet"></div>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <div class="operation" v-if="scope.row.fa_num">
                                <template v-if="scope.row.status === '已减少'">
                                    <el-tooltip
                                        placement="bottom"
                                        trigger="hover"
                                        effect="light"
                                        popper-class="el-popover"
                                        :ref="
                                            (el) => {
                                                viewRef[scope.row.fa_id] = el;
                                            }
                                        ">
                                        <span class="link mr-10">查看</span>
                                        <template #content>
                                            <ul class="operation">
                                                <li @click="handleExamine(scope.row.fa_id)">查看资产明细</li>
                                                <li @click="changeHistory(scope.row.fa_id)">变更历史</li>
                                            </ul>
                                        </template>
                                    </el-tooltip>
                                </template>
                                <template v-else>
                                    <template v-if="scope.row.created_period == FAPeriod && scope.row.data_type == 2">
                                        <a
                                            class="link disabled mr-10"
                                            title="本期增加的资产不能作变更，您可以直接点击[编辑]的链接来编辑您需要修改的内容"
                                            v-permission="['fixedassets-card-canchange']"
                                            >变更</a
                                        >
                                    </template>
                                    <template v-else>
                                        <template v-if="isFinishAccount">
                                            <a
                                                class="link disabled mr-10"
                                                title="资产已计提折旧，不能变更哦"
                                                v-permission="['fixedassets-card-canchange']"
                                                >变更</a
                                            >
                                        </template>
                                        <template v-else>
                                            <el-tooltip
                                                placement="bottom"
                                                trigger="hover"
                                                effect="light"
                                                popper-class="el-popover"
                                                v-if="checkPermission(['fixedassets-card-canchange'])"
                                                :ref="
                                                    (el) => {
                                                        changePoverRef[scope.row.fa_id] = el;
                                                    }
                                                ">
                                                <a class="link mr-10">变更</a>
                                                <template #content>
                                                    <ul class="operation">
                                                        <li
                                                            v-for="(item, index) in getChangeList(
                                                                scope.row.fa_property,
                                                                accountingStandard
                                                            )"
                                                            :key="index"
                                                            @click="handleChange(scope.row.fa_id, item.slot, item.name)">
                                                            {{ item.name }}
                                                        </li>
                                                    </ul>
                                                </template>
                                            </el-tooltip>
                                        </template>
                                    </template>
                                    <template v-if="scope.row.fa_property !== '2'">
                                        <template v-if="scope.row.created_period == FAPeriod && scope.row.data_type == 2">
                                            <a
                                                class="link disabled mr-10"
                                                title="资产录入当期不能处置哦"
                                                v-permission="['fixedassets-card-canexcute']"
                                                >处置</a
                                            >
                                        </template>
                                        <template v-else>
                                            <template v-if="isFinishAccount">
                                                <a
                                                    class="link disabled mr-10"
                                                    title="资产已计提折旧，不能处置哦"
                                                    v-permission="['fixedassets-card-canexcute']"
                                                    >处置</a
                                                >
                                            </template>
                                            <template v-else>
                                                <el-tooltip
                                                    placement="bottom"
                                                    trigger="hover"
                                                    effect="light"
                                                    popper-class="el-popover"
                                                    v-if="checkPermission(['fixedassets-card-canexcute'])">
                                                    <a class="link mr-10">处置</a>
                                                    <template #content>
                                                        <ul class="operation">
                                                            <li @click="HandleAssets(1, scope.row.fa_id)">出售</li>
                                                            <li v-if="scope.row.fa_property == 0" @click="HandleAssets(2, scope.row.fa_id)">
                                                                报废
                                                            </li>
                                                            <li @click="HandleAssets(3, scope.row.fa_id)">其他</li>
                                                        </ul>
                                                    </template>
                                                </el-tooltip>
                                            </template>
                                        </template>
                                    </template>
                                    <el-tooltip
                                        placement="bottom"
                                        trigger="hover"
                                        effect="light"
                                        popper-class="el-popover"
                                        :ref="
                                            (el) => {
                                                viewRef[scope.row.fa_id] = el;
                                            }
                                        ">
                                        <span class="link mr-10">查看</span>
                                        <template #content>
                                            <ul class="operation">
                                                <li @click="handleExamine(scope.row.fa_id)">查看资产明细</li>
                                                <li @click="changeHistory(scope.row.fa_id)">变更历史</li>
                                            </ul>
                                        </template>
                                    </el-tooltip>
                                    <a
                                        class="link"
                                        style="margin-right: 10px"
                                        @click="handleEdit(scope.row.fa_id)"
                                        v-permission="['fixedassets-card-canedit']"
                                        >编辑</a
                                    >
                                </template>
                                <a class="link" @click.stop="handleGetAttachFiles(scope.row)"
                                    >附件{{ scope.row.attachsCount > 0 ? `(${scope.row.attachsCount})` : "" }}</a
                                >
                            </div>
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
    </div>
    <ImportSingleFileDialog
        class="fixedlist-import-dialog"
        importTitle="批量科目调整"
        v-model:import-show="importDialogVisible"
        :importUrl="`/api/FixedAssets/ImportAsubChange?pid=${searchInfo.period}`"
        :uploadSuccess="uploadSuccess"
        :modalClass="' '"
    >
        <template #download>
            第一步：导出资产数据，修改科目后导入<a class="link ml-10" @click="downloadAsubChangeFile">下载模板</a>
        </template>
        <template #import-content>
            <span>第二步：选取文件导入</span>
        </template>
        <template #bottom-tips>
            <div class="asub-change-info" style="margin: 10px 40px; font-size: var(--h5); line-height: var(--line-height)">
                提示：导入科目后将修改原资产卡片中的科目
            </div>
        </template>
    </ImportSingleFileDialog>
    <AuxiliaryDialog
        v-model="auxiliaryDialogShow"
        :title="auxiliaryTitle"
        :asubName="auxiliaryAsubName"
        :aacode="auxiliaryCode"
        :aaAllowNull="auxiliaryAllowNull"
        :aaReadonly="auxiliaryReadonly"
        :aaId="auxiliaryId"
        @setAsubWithAAE="setAsubWithAAE"
    ></AuxiliaryDialog>
    <el-dialog class="custom-confirm dialogDrag" v-model="batchCopy.visible" title="复制资产" width="420">
        <div class="batchDialog" v-dialogDrag>
            <div class="copyDialog-content mt-20">
                复制数量：<el-input v-model="batchCopy.value" style="width: 200px" @input="restrictInput('copy')"></el-input>
            </div>
            <div class="batchDialog-warning">所选的{{ batchCopy.ids.length }}个资产将全部进行复制</div>
            <div class="buttons borderTop">
                <a class="button solid-button mr-10" @click="CopyDataInner()">确定</a>
                <a class="button" @click="batchCopy.visible = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog
        class="custom-confirm dialogDrag"
        v-model="batchChangeData.visible"
        title="批量变更"
        :width="batchChangeData.method === 'departmentChange' ? 720 : 480"
    >
        <div class="batchDialog" v-dialogDrag>
            <div class="batchContent mt-10">
                选择变更方式：
                <el-select 
                    v-model="batchChangeData.method" 
                    style="width: 200px" 
                    @change="changeMethod"
                    :filterable="true"
                    :filter-method="batchChangeWayFilterMethod"
                >
                    <el-option
                        v-for="item in showbatchChangeWayList"
                        :key="item.slot"
                        :value="item.slot"
                        :label="item.name"
                    ></el-option>
                </el-select>
            </div>
            <div
                class="mt-10"
                :style="batchChangeData.method === 'departmentChange' ? 'padding-left:40px;text-align:left' : 'padding-left:40px;'">
                调整为：
                <template v-if="batchChangeData.method === 'depreciationTypeChange'">
                    <el-select 
                        v-model="batchChangeData.value" 
                        style="width: 200px"
                        :filterable="true"
                        :filter-method="batchChangedepTypeFilterMethod"
                    >
                        <el-option 
                            v-for="item in showDepreciationTypeList" 
                            :label="item.label" 
                            :value="item.value" 
                            :key="item.value"
                        ></el-option>
                    </el-select>
                </template>
                <template v-else-if="batchChangeData.method === 'statusChange'">
                    <el-select 
                        v-model="batchChangeData.value" 
                        style="width: 200px"
                        :filterable="true"
                        :filter-method="batchChangeStatusFilterMethod"
                    >
                        <el-option 
                            v-for="item in showChangeStatusList" 
                            :value="item.value" 
                            :label="item.label" 
                            :key="item.value"
                        ></el-option>
                    </el-select>
                </template>
                <template v-else-if="batchChangeData.method === 'departmentChange'">
                    <div style="padding: 10px 40px 0 0">
                        <Table
                            :data="batchDepartmentData"
                            :columns="batchChangeColumns"
                            :show-overflow-tooltip="true"
                            :tooltip-options="{ effect: 'light', placement: 'top' }"
                            :hasAddSub="true"
                            :customSubShowIndex="0"
                            :addRowData="{ department_id: '', ratio: '', asub_id: '', asub_aae: '' }"
                            :tableName="batchDepartModule">
                            <template #department_id>
                                <el-table-column
                                    label="使用部门"
                                    min-width="100"
                                    align="center"
                                    header-align="center"
                                    prop="department_id"
                                    :width="getColumnWidth(batchDepartModule, 'department_id')">
                                    <template #default="scope">
                                        <Select 
                                            v-model="scope.row.department_id" 
                                            :fit-input-width="true" 
                                            class="batch-depart-id"
                                            :filterable="true"
                                            :filter-method="departmentFilterMethod"
                                        >
                                            <ElOption
                                                v-for="item in showdepartmentList"
                                                :key="item.aaeid"
                                                :value="item.aaeid"
                                                :label="item.aaname">
                                                <span>{{ item.aaname }}</span>
                                            </ElOption>
                                        </Select>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #ratio>
                                <el-table-column
                                    label="分摊比例"
                                    min-width="200"
                                    align="center"
                                    header-align="center"
                                    prop="ratio"
                                    :show-overflow-tooltip="false"
                                    :width="getColumnWidth(batchDepartModule, 'ratio')">
                                    <template #default="scope">
                                        <div class="batch-depart-ratio">
                                            <div>
                                                <el-input
                                                    style="width: 100%"
                                                    v-model="scope.row.ratio"
                                                    @input="(val) => restrictInput('ratio', val, scope.$index)" />
                                            </div>
                                            <span style="padding-left: 2px">%</span>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #asub>
                                <el-table-column
                                    :label="selectCardId[0].fa_property === '0' ? '折旧费用科目' : '摊销费用科目'"
                                    min-width="124"
                                    align="center"
                                    header-align="center"
                                    prop="asub_id"
                                    :resizable="false">
                                    <template #default="scope">
                                        <SelectV2
                                            class="batch-depart-id visibleSelect"
                                            :options="showCostAsubList"
                                            :filterable="true"
                                            :placeholder="' '"
                                            :props="{ value: 'asubId' }"
                                            v-model="scope.row.asub_id"
                                            :toolTipOptions="{ dynamicWidth: true }"
                                            :remote="true"
                                            @visible-change="costAsubVisibleChange"
                                            @clickOption="(value) => handleAsubChange(value, scope.$index)"
                                    >
                                    </SelectV2>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </template>
                <template v-else>
                    <el-input maxlength="13" @input="handleInput" v-model="batchChangeData.value" style="width: 200px"></el-input>
                </template>
            </div>
            <div class="batchDialog-warning mt-10" style="padding-left: 128px">
                所选的{{ batchChangeData.ids.length }}个资产将全部进行以上变更
            </div>
            <div class="buttons">
                <a class="button solid-button mr-10" @click="confirmBatchChange">调整</a>
                <a class="button" @click="cancelChange">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog class="custom-confirm dialogDrag" v-model="batchDisposeData.visible" title="批量处置" width="420">
        <div class="batchDialog" v-dialogDrag>
            <div class="batchContent mt-20">
                选择处置方式：
                <el-select 
                    v-model="batchDisposeData.method" 
                    style="width: 100px"
                    :filterable="true"
                    :filter-method="disposeFilterMethod"
                >
                    <el-option 
                        v-for="item in showDisposeList" 
                        :key="item.value" 
                        :value="item.value" 
                        :label="item.label"
                    ></el-option>
                </el-select>
            </div>
            <div class="batchDialog-warning mt-10" style="padding-left: 90px">
                所选的{{ batchDisposeData.ids.length }}个资产将全部进行以上处置
            </div>
            <div class="buttons">
                <a class="button solid-button mr-10" @click="confirmBatchDispose">调整</a>
                <a class="button" @click="batchDisposeData.visible = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <UploadFileDialog :readonly="readonly" ref="uploadFileDialogRef" @save="handleSaveAttachFiles" />
    <!-- 列设置 -->
    <ColumnSet
        ref="columnSetRef"
        v-model="columnSetShow"
        :data="fixedassetsListColumnsSet"
        :allColumns="allColumns"
        :setModuleType="module"
        @saveColumnSet="saveColumnSet">
    </ColumnSet>
    <FAPrint
        v-model:printDialogShow="printDialogVisible"
        :title="'资产列表打印'"
        modalClass="no-position-modal-class"
        :printData="printInfo"
        :dirShow="false"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3, getPrintParams())"></FAPrint>
</template>

<script setup lang="ts">
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Table from "@/components/Table/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Select from "@/components/Select/index.vue";
import ElOption from "@/components/Option/index.vue";
import SelectV2 from "@/components/AsubSelect/index.vue";
import FAPrint from "@/components/PrintDialog/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import AuxiliaryDialog from "./AuxiliaryDialog.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IAccountSubjectModel } from "@/api/accountSubject.js";
import type {
    IPeriod,
    ISelectStrItem,
    IAssetDetail,
    IFixedAssetsType,
    IGenerateChangeVoucher,
    IAssetGenerateChangeVoucher,
    IFSearchItem,
} from "../types";
import { batchData } from "../types";

import { ref, reactive, watch, watchEffect, onMounted, computed, toRef, nextTick } from "vue";
import { dayjs } from "element-plus";
import { useAccountSetStoreHook, useAccountSetStore } from "@/store/modules/accountSet";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { usePagination } from "@/hooks/usePagination";
import usePrint from "@/hooks/usePrint";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { checkPermission } from "@/util/permission";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalExport, globalPostPrint } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import {
    getChangeList,
    setColumns,
    handleZoomChange,
    changeApi,
    changeId,
    getYearMonthLastDay,
    checkNeedToastWithBillAndVoucher,
    saveAttachFileFn,
    getAssetsAttachFiles,
    isKeyOfIFSearchItem,
} from "../utils";
import { faPropertyList, depreciationTypeList, changeStatusList } from "@/views/FixedAssets/utils";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import ColumnSet from "@/components/ColumnSet/index.vue";
import type { IColItem } from "@/components/ColumnSet/utils";
import {
    getSlotIsFreeze,
    getSlotClassName,
    getShowColumn,
    getColumnWidth,
    alterArrayPos,
    getColumnListApi,
} from "@/components/ColumnSet/utils";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import { isFilterMultile } from "@/components/TableHeaderFilter/utils";
import { copyText } from "@/views/Cashier/BankAndCompany/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { getCostAsubList } from "../utils";
const batchDepartModule = "FixedBatchDepart";
const module = ref(700);
const props = defineProps({
    pid: {
        type: Number,
        default: 0,
    },
    period: {
        type: Array,
        default: () => [],
    },
    month: {
        type: String,
        default: "",
    }
});
const emits = defineEmits([
    "increaseAssets",
    "handleImport",
    "handleChange",
    "handleEdit",
    "handleExamine",
    "changeHistory",
    "usefullifeChange",
    "departmentChange",
    "changeStatus",
    "goToVoucher",
    "showDetail",
    "getChangeData",
]);

const forcedShutdown = ref(false);
const isErp = ref(window.isErp);
const fixedassetsListRef = ref();
const accountingStandard = ref(0);
const accountsetStore = useAccountSetStore();
accountingStandard.value = Number(accountsetStore.accountSet?.accountingStandard);
const trialStatusStore = useTrialStatusStore();

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const currentPeriodInfo = ref("");
const settingOptionRef = ref();
const loading = ref(false);
const addAndDelShow = ref(false);
const changeID = ref<number>();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination("List", false);
const tableMaxHeight = ref("");
const searchInfo = reactive({
    period: props.pid,
    status: 1,
    startTime: "",
    endTime: "",
    fa_num: "",
    fa_type: [] as number[],
    fa_property: "",
    fa_name: "",
    fa_model: "",
    department: [] as number[],
    showInfo: false,
    note: "",
});
const filterSearchInfo: IFSearchItem = reactive({
    fa_num: "",
    fa_name: "",
    fa_model: "",
    note: "",
    fa_type: [] as number[],
    department: [] as number[],
    status: -1,
});
const modifiedPeriod = ref(0);
const startMonth = ref("");
const FAPeriod = ref(0);
const statusList = [
    {
        value: -1,
        name: "全部",
    },
    {
        value: 1,
        name: "正常",
    },
    {
        value: 2,
        name: "停用",
    },
    {
        value: 3,
        name: "出租",
    },
    {
        value: 4,
        name: "已减少",
    },
];
const disposeList = ref<any[]>([]);

const accountSetStore = useAccountSetStoreHook();
const accountSet = accountSetStore.accountSet;

function setSelectable(row: IAssetDetail): boolean {
    if (row.fa_type === "") {
        return false;
    }
    return true;
}

const selectCardId = ref<IAssetDetail[]>([]);
function selectCard(selection: any) {
    selectCardId.value = selection.map((item: IAssetDetail) => item);
}

function selectToggle(row: any, column: any, event: any) {
    if (!setSelectable(row)) return;
    if (["link mr-10 el-tooltip__trigger el-tooltip__trigger", "link"].includes(event.target.className)) return;
    let selected = selectCardId.value.findIndex((item: IAssetDetail) => item.fa_num === row.fa_num) >= 0 ? true : false;
    fixedassetsListRef.value?.getTable().toggleRowSelection(row, !selected);
}
const handleSearch = (mode?: any) => {
    if (mode === "sure") {
        searchInfo.period = modifiedPeriod.value;
        restFilterInfo();
    }
    paginationData.currentPage = 1;
    const params = {
        period: searchInfo.period === -1 ? "" : searchInfo.period,
        datatype: 2,
        status: searchInfo.status,
        time_s: searchInfo.startTime === null ? "" : searchInfo.startTime,
        time_e: searchInfo.endTime === null ? "" : searchInfo.endTime,
        fa_type: getCommonSelect("fa_type", faTypeOptions.value),
        fa_num: getQueryVal("fa_num"),
        fa_name: getQueryVal("fa_name"),
        fa_model: getQueryVal("fa_model"),
        fa_property: searchInfo.fa_property,
        department: getCommonSelect("department", departmentOptions.value),
        note: getQueryVal("note"),
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        sortField: sortField.value,
        sortOrder: sortOrder.value,
    };
    FAPeriod.value = searchInfo.period;
    checkSettleAccount();
    EnableAddAndDel();
    if (periodList.value) {
        currentPeriodInfo.value = periodList.value.find((item) => searchInfo.period === item.pid)?.periodInfo || "";
    }
    loading.value = true;
    if (params.period !== "") {
        request({
            url: `/api/FixedAssets/PagingList`,
            method: "get",
            params,
        })
            .then((res: IResponseModel<{ data: IAssetDetail[]; count: number }>) => {
                ColumnsQuery();
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
            })
            .finally(() => {
                if (periodList.value) {
                    currentPeriodInfo.value = periodList.value.find((item) => searchInfo.period === item.pid)?.periodInfo || "";
                }
                loading.value = false;
            });
    } else {
        loading.value = false;
    }
    containerRef.value?.handleClose();
};

const handleClose = () => {
    containerRef.value?.handleClose();
};

const handleReset = () => {
    searchInfo.period = props.pid;
    searchInfo.status = 1;
    searchInfo.startTime = "";
    searchInfo.endTime = "";
    searchInfo.fa_num = "";
    searchInfo.fa_type = faTypeOptions.value.map((item) => item.id);
    searchInfo.fa_name = "";
    searchInfo.fa_model = "";
    searchInfo.department = departmentOptions.value.map((item) => item.id);
    searchInfo.fa_property = "";
};

const changePoverRef = ref<any>([]);

function handleChange(FA_ID: string, slot: string, title: string) {
    changePoverRef?.value[FA_ID].hide();
    request({
        url: `/api/FixedAssets/CanChange?faid=${FA_ID}&pid=${searchInfo.period}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.data) {
            // 获取资产条目明细数据
            request({
                url: `/api/FixedAssets?faid=${FA_ID}&pid=${searchInfo.period}`,
            }).then((res: IResponseModel<object>) => {
                const info = {
                    slot: slot,
                    title: title,
                    data: res.data,
                    pid: searchInfo.period,
                };
                emits("handleChange", info);
            });
        } else {
            ElNotify({
                type: "warning",
                message: "该资产后期存在变更记录，无法变更",
            });
        }
    });
}

let isDisposal = false;
function HandleAssets(flag: number, id: number) {
    request({
        url: `/api/FixedAssets/CanChange?faid=${id}&pid=${searchInfo.period}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.data) {
            ElConfirm("确定进行此操作吗？").then((r: boolean) => {
                if (r && !isDisposal) {
                    // 资产处置
                    isDisposal = true;
                    request({
                        url: `/api/FAChange/Disposal?faid=${id}&pid=${searchInfo.period}&type=${flag}`,
                        method: "post",
                    }).then((res: IResponseModel<number | string>) => {
                        isDisposal = false;
                        if (res.data != "") {
                            if (res.data == "period") {
                                ElNotify({
                                    type: "error",
                                    message: "当前期间无法执行此操作，请刷新页面重试",
                                });
                            } else {
                                changeID.value = Number(res.data);
                                if (useAccountSetStoreHook().permissions.includes("fixedassets-card-cancreatevoucher")) {
                                    ElConfirm("您的处置已保存。您需要生成凭证吗?")
                                        .then((r: boolean) => {
                                            if (r) {
                                                request({
                                                    url: `/api/FAVoucher/GenerateFAReduceVoucher?faid=${id}`,
                                                    method: "post",
                                                }).then((res: IResponseModel<object>) => {
                                                    const voucherData = res.data;
                                                    request({
                                                        url: `/api/FAPeriod/GetMaxDate?pid=${searchInfo.period}`,
                                                        method: "post",
                                                    }).then(() => {
                                                        emits("goToVoucher", voucherData, "资产处置凭证", "", "", changeID.value);
                                                    });
                                                });
                                            } else {
                                                emits("getChangeData");
                                            }
                                        })
                                        .finally(() => {
                                            getTableData();
                                        });
                                }
                            }
                        }
                    });
                }
            });
        } else {
            ElNotify({
                type: "error",
                message: "该资产后期存在变更记录，无法处置",
            });
        }
    });
}

//查看资产明细和变更历史气泡开关
const viewRef = ref<any>([]);

//查看资产明细
function handleExamine(FA_ID: string) {
    viewRef?.value[FA_ID].hide();
    emits("handleExamine", FA_ID, currentPeriodInfo.value);
}

//变更历史
function changeHistory(FA_ID: string) {
    viewRef?.value[FA_ID].hide();
    emits("changeHistory", FA_ID, currentPeriodInfo.value);
}

function handleEdit(faid: number) {
    emits("handleEdit", faid, searchInfo.period);
}
//增加资产
function increaseAssets(flag: string) {
    emits("increaseAssets", searchInfo.period);
}
// datatype, ids, valueCount
const batchCopy = ref(new batchData(false, "copy", [], "1"));
function restrictInput(type: string, value?: string, index?: number) {
    if (type === "copy") {
        batchCopy.value.value = batchCopy.value.value!.replace(/[^0-9]/g, "");
    }
    if (type === "ratio") {
        let ratio = value!.replace(".", "");
        const regex = /^(100|[1-9][0-9]?)$/;
        batchDepartmentData.value[index!].ratio = ratio.match(regex) ? ratio.match(regex)![0] : "";
    }
}
function copyAssets() {
    if (selectCardId.value.length == 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要复制的资产！",
        });
        return false;
    }
    CopyData();
}

const timer = ref(0); //防止点击多次重复提交的时间戳
function CopyData() {
    let valueCount = 0;
    let ids: number[] = [];
    let codeAndNames: string[] = [];
    selectCardId.value.forEach((item: IAssetDetail) => {
        if (item.fa_num !== "") {
            //去掉合计一栏的选择框
            valueCount++;
            ids.push(item.fa_id);
            codeAndNames.push(item.fa_num + " " + item.fa_name);
        }
    });
    if (valueCount == 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要复制的资产！",
        });
        return false;
    }
    if (valueCount === 1) {
        if (!timer.value) {
            batchCopy.value = new batchData(true, "copy", ids, "1");
            // CopyDataInner(datatype, ids, valueCount);
            timer.value = setTimeout(() => {
                timer.value = 0;
            }, 2000);
        }
    }
    if (valueCount > 1) {
        ElConfirm("亲，确定要批量复制资产吗？").then((r: Boolean) => {
            if (r) {
                if (!timer.value) {
                    batchCopy.value = new batchData(true, "copy", ids, "1");
                    // CopyDataInner(datatype, ids, valueCount);
                    timer.value = setTimeout(() => {
                        timer.value = 0;
                    }, 2000);
                }
            }
        });
    }
}
// datatype: string, faids: number[], valueCount: number
function CopyDataInner() {
    if (batchFlag.value) return;
    batchFlag.value = true;
    if (Number(batchCopy.value.value!) > 10) {
        batchFlag.value = false;
        ElNotify({
            type: "warning",
            message: "亲，最多只能复制10个资产！",
        });
        return false;
    }
    const { ids, value } = batchCopy.value;
    request({
        url: `/api/FixedAssets/MultipleBatchCopy?pid=${searchInfo.period}&count=${batchCopy.value.value}`,
        method: "post",
        data: ids,
        headers: {
            "Content-Type": "application/json; charset=utf-8",
        },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "亲，复制成功啦！",
                });
                paginationData.currentPage = Math.ceil((paginationData.total + Number(value)) / paginationData.pageSize);
                getTableData();
                emits("getChangeData");
                handleExpiredCheckData(ExpiredCheckModuleEnum.Assets);
            } else if (res.state === 2000) {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "遇到问题，请您联系系统管理员或稍后重试",
                });
            }
        })
        .finally(() => {
            batchFlag.value = false;
            batchCopy.value.visible = false;
        });
}

function Delete(datatype: string) {
    if (selectCardId.value.length === 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要删除的资产！",
        });
        return false;
    } else {
        // && $("#finishflag").val() == "1"
        if (datatype == "1") {
            ElConfirm("亲，资产初始化数据已经与期初的资产数据一致了，您确认还需要修改吗？").then((r: Boolean) => {
                if (r) {
                    DelData();
                } else {
                    return false;
                }
            });
        } else {
            DelData();
        }
    }
}

function DelData() {
    let valueCount = 0;
    let breakCount = 0;
    let ids: number[] = [];
    let codeAndNames: string[] = [];
    selectCardId.value.forEach((item: IAssetDetail) => {
        if (item.fa_num !== "") {
            if (item.hasDep == 0) {
                valueCount++;
                ids.push(item.fa_id);
                codeAndNames.push(item.fa_num + " " + item.fa_name);
            } else {
                breakCount++;
            }
        }
    });
    if (valueCount === 0 && breakCount === 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要删除的资产！",
        });
        return;
    }
    ElConfirm("此操作将删除已选择资产及其所有变更记录，确定删除吗?").then((r) => {
        if (r) {
            if (valueCount > 0) {
                //检查是否具备删除条件
                request({
                    url: `/api/FixedAssets/CanDelete`,
                    method: "post",
                    data: ids,
                }).then((res: IResponseModel<string>) => {
                    if (res.data == "") {
                        request({
                            url: `/api/FixedAssets/Batch`,
                            method: "delete",
                            data: ids,
                        }).then((res: IResponseModel<boolean>) => {
                            if (res.state === 1000 && res.data) {
                                getTableData();
                                emits("getChangeData", "list");
                                ElConfirm("成功：" + valueCount + "，跳过：" + breakCount + "（已计提折旧期间的资产将会被跳过！）", true);
                            } else {
                                ElNotify({
                                    type: "error",
                                    message: res.msg,
                                });
                            }
                        });
                    } else {
                        let flag = res.data.substr(res.data.length - 1, 1);
                        let datas = res.data.substr(0, res.data.length - 1);
                        if (flag == "D") {
                            ElNotify({
                                type: "warning",
                                message: "编号为" + datas + "资产已删除！",
                            });
                            getTableData();
                        } else if (flag == "V") {
                            ElNotify({
                                type: "warning",
                                message: "编号为" + datas + "资产已生成凭证无法删除，请先删除相关凭证！",
                            });
                        } else if (flag == "F") {
                            ElNotify({
                                type: "warning",
                                message: "编号为" + datas + "资产已生成折旧凭证无法删除，请先删除相关凭证！",
                            });
                        }
                    }
                });
            } else {
                ElConfirm("成功：" + valueCount + "，跳过：" + breakCount + "（已计提折旧期间的资产将会被跳过！）", true);
            }
        }
    });
}

function handleImport() {
    if(addAndDelShow.value){
        ElNotify({
            type: "warning",
            message: "亲，当期资产已折旧无法导入资产，请选择其他期间进行导入！",
        });
        return false;
    }
    emits("handleImport", "import");
}

function handleExport() {
    const params = {
        period: searchInfo.period,
        datatype: 2,
        status: searchInfo.status,
        time_s: searchInfo.startTime,
        time_e: searchInfo.endTime,
        fa_num: getQueryVal("fa_num"),
        fa_type: getCommonSelect("fa_type", faTypeOptions.value),
        fa_name: getQueryVal("fa_name"),
        fa_model: getQueryVal("fa_model"),
        department: getCommonSelect("department", departmentOptions.value),
        note: getQueryVal("note"),
        sortField: sortField.value,
        sortOrder: sortOrder.value,
    };
    const url = `/api/FixedAssets/Export?`;
    globalExport(url + getUrlSearchParams(params));
}

function handleExportForImport() {
    const params = {
        period: searchInfo.period,
        datatype: 2,
        status: searchInfo.status,
        time_s: searchInfo.startTime,
        time_e: searchInfo.endTime,
        fa_num: getQueryVal("fa_num"),
        fa_type: getCommonSelect("fa_type", faTypeOptions.value),
        fa_name: getQueryVal("fa_name"),
        fa_model: getQueryVal("fa_model"),
        department: getCommonSelect("department", departmentOptions.value),
        note: getQueryVal("note"),
        sortField: sortField.value,
        sortOrder: sortOrder.value,
    };
    const url = `/api/FixedAssets/ExportForImport?`;
    globalExport(url + getUrlSearchParams(params));
}

let batchFlag = ref(false);
const batchChangeColumns = ref<Array<IColumnProps>>([{ slot: "department_id" }, { slot: "ratio" }, { slot: "asub" }]);
const batchChangeData = ref(new batchData(false, "", [], ""));
const batchDepartmentData = ref([{ ratio: "", department_id: "", asub_id: "", asub_aae: "" }]);
function changeMethod() {
    switch (batchChangeData.value.method) {
        case "statusChange":
        case "depreciationTypeChange":
            batchChangeData.value.value = "1";
            break;
        default:
            batchChangeData.value.value = "";
            break;
    }
}
function batchChange() {
    if (selectCardId.value.length == 0) {
        ElNotify({
            message: "亲，请先选择需要变更的资产卡片哦~",
            type: "warning",
        });
        return false;
    }
    if (selectCardId.value.find((item) => item.created_period === FAPeriod.value && item.data_type == 2)) {
        ElNotify({
            message: "亲，本期新增的卡片不支持变更哦~",
            type: "warning",
        });
        return false;
    }
    let uniqueValues = new Set();
    selectCardId.value.forEach((obj) => {
        uniqueValues.add(obj.fa_property);
    });
    if (Array.from(uniqueValues).length > 1) {
        ElNotify({
            message: "亲，请选择相同属性的资产卡片哦~",
            type: "warning",
        });
        return false;
    }
    batchChangeData.value = new batchData(false, "", [], "");
    batchDepartmentData.value = [{ ratio: "", department_id: "", asub_id: "", asub_aae: "" }];
    batchChangeData.value.ids = selectCardId.value.reduce((pre: number[], cur: IAssetDetail) => {
        pre.push(cur.fa_id);
        return pre;
    }, []);
    batchChangeData.value.visible = true;
}
const checkedIds = ref<number[]>([]);
function confirmBatchChange() {
    if (batchFlag.value) return false;
    batchFlag.value = true;
    if (!batchChangeData.value.method) {
        batchFlag.value = false;
        ElNotify({
            type: "warning",
            message: "请选择变更方式",
        });
        return false;
    }
    if (batchChangeData.value.method === "departmentChange") {
        let sumRadio = 0;
        let hasNegativeRatio = false;
        let hasEmptyDepartment = false;
        let hasEmptyAsub = false;
        let hasEmptyRatio = false;
        batchDepartmentData.value.forEach((item: any) => {
            if (Number(item.ratio) < 0) hasNegativeRatio = true;
            if (!item.department_id) hasEmptyDepartment = true;
            if (!item.asub_id) hasEmptyAsub = true;
            if (item.department_id && item.asub_id) {
                if (!item.ratio) hasEmptyRatio = true;
                sumRadio += Number(item.ratio);
            }
        });
        if (hasEmptyDepartment) {
            batchFlag.value = false;
            ElNotify({
                type: "warning",
                message: "部门不能为空，请重新分配哦~",
            });
            return;
        }
        if (hasEmptyAsub) {
            batchFlag.value = false;
            ElNotify({
                type: "warning",
                message: "费用科目不能为空，请重新分配哦~",
            });
            return;
        }
        if (hasNegativeRatio) {
            batchFlag.value = false;
            ElNotify({
                type: "warning",
                message: "费用分摊比例不能为负数，请重新分配哦~",
            });
            return;
        }
        if (hasEmptyRatio) {
            batchFlag.value = false;
            ElNotify({
                type: "warning",
                message: "费用分摊比例不能为空，请重新分配哦~",
            });
            return;
        }
        if (sumRadio !== 100) {
            batchFlag.value = false;
            ElNotify({
                type: "warning",
                message: "费用分摊比例之和需为100%，请重新分配哦~",
            });
            return;
        }
        const data = {
            faids: batchChangeData.value.ids,
            pid: searchInfo.period,
            vid: 0,
            FAAmortizations: batchDepartmentData.value.map((item: any) => Object.assign({}, item, { ratio: item.ratio / 100 })),
        };
        request({
            url: changeApi[batchChangeData.value.method],
            method: "post",
            data,
        })
            .then((res: IResponseModel<any>) => {
                if (res.state !== 1000) {
                    ElNotify({
                        type: "warning",
                        message: res.msg || "保存失败！",
                    });
                    return false;
                }
                batchChangeData.value.visible = false;
                getTableData();
                emits("getChangeData");
            })
            .finally(() => {
                batchFlag.value = false;
            });
    } else {
        if (batchChangeData.value.value!.trim() === "") {
            batchFlag.value = false;
            ElNotify({
                type: "warning",
                message: "亲，请先录入需要调整的值哦",
            });
        }
        request({
            url:
                (changeApi as { [key: string]: string })[batchChangeData.value.method] +
                `?pid=${searchInfo.period}&value=${batchChangeData.value.value}&vid=0`,
            method: "post",
            data: batchChangeData.value.ids,
        })
            .then((res: IResponseModel<any>) => {
                if (res.state !== 1000) {
                    ElNotify({
                        type: "warning",
                        message: res.msg || "保存失败！",
                    });
                    return false;
                }
                batchChangeData.value.visible = false;
                getTableData();
                emits("getChangeData");
                checkedIds.value = [];
                res.data.forEach((item: any) => {
                    checkedIds.value.push(item.changeId);
                });
                if (["depreciationTypeChange", "statusChange", "usefullifeChange"].includes(batchChangeData.value.method)) return;
                ElConfirm("您的变更已保存，需要立即生成凭证吗？").then((r: Boolean) => {
                    if (r) {
                        getVoucher();
                    }
                });
            })
            .finally(() => {
                batchFlag.value = false;
            });
    }
}
function getVoucher() {
    const changeType = (changeId as unknown as { [key: string]: number })[batchChangeData.value.method];
    request({
        url: `/api/FAVoucher/BatchGenerateChangeVoucher?changeType=${changeType}&index=0&index2=0&isMerge=true`,
        method: "post",
        data: checkedIds.value,
    })
        .then((res: IResponseModel<IGenerateChangeVoucher>) => {
            const emitData: IAssetGenerateChangeVoucher = { ...res.data, changeType };
            const date = periodList.value!.find((item: IPeriod) => item.pid === searchInfo.period)?.periodInfo as string;
            const { year, month, lastDay } = getYearMonthLastDay(date);
            const voucherData = year + "-" + (month < 10 ? "0" + month : month) + "-" + lastDay;
            emits("goToVoucher", emitData, "批量生成变更凭证", checkedIds.value, voucherData, 0, true);
        })
        .catch((err) => {
            console.log(err);
        });
}
function cancelChange() {
    batchChangeData.value.visible = false;
}
const costAsubList = ref<IAccountSubjectModel[]>();
const costAsubListAll = ref<Array<any>>([]);
const showCostAsubList = ref<Array<any>>([]);
const costAsubVisibleChange = (visible: boolean) => {
    if (visible) {
        showCostAsubList.value = JSON.parse(JSON.stringify(costAsubListAll.value));
    }
}
watchEffect(() => {
    costAsubList.value = getCostAsubList(accountingStandard.value,0,true,true).costAsubList as IAccountSubjectModel[];
    costAsubListAll.value = costAsubList.value!.map((item) => {
        return {
            ...item,
            label: item.asubCode + ' ' + item.asubAAName,
            asubId: item.asubId.toString(),
        }
    });
    showCostAsubList.value = JSON.parse(JSON.stringify(costAsubListAll.value));
});

const auxiliaryDialogShow = ref(false);
const auxiliaryTitle = ref("");
const auxiliaryAsubName = ref("");
const auxiliaryCode = ref<string[]>([]);
const auxiliaryAllowNull = ref<string[]>([]);
const auxiliaryReadonly = ref(false);
const auxiliaryId = ref();
const costAsubIndex = ref(0);
const clickAsubId = ref();
const costAsubRef = ref<any>([]);
function handleAsubChange(value: any, index: number) {
  const selectedItem = showCostAsubList.value.find(item => item.asubId == value);
  if(selectedItem) {
    clickAsubOption(
      selectCardId.value[0].fa_property === '0' ? '折旧费用科目' : '摊销费用科目',
      selectedItem.asubName,
      selectedItem.aatypes,
      selectedItem.asubId,
      index,
      selectedItem.asubCode
    );
  }
}
function clickAsubOption(title: string, name: string, aaTypes: string, value: any, costIndex: number, asubCode: string) {
    if (!aaTypes) {
        batchDepartmentData.value[costIndex].asub_aae = "";
        return;
    }
    auxiliaryId.value = [];
    clickAsubId.value = value;
    costAsubIndex.value = costIndex ? costIndex : 0;
    auxiliaryTitle.value = title;
    auxiliaryAsubName.value = name;
    auxiliaryCode.value = aaTypes.includes(",")
        ? aaTypes.split(",").map((item) => {
              return item.split("_")[0];
          })
        : [aaTypes.split("_")[0]];
    auxiliaryAllowNull.value = aaTypes.includes(",")
        ? aaTypes.split(",").map((item) => {
              return item.split("_")[1];
          })
        : [aaTypes.split("_")[1]];
    auxiliaryId.value = batchDepartmentData.value[costAsubIndex.value].asub_aae
        ? batchDepartmentData.value[costAsubIndex.value].asub_aae.split(",")
        : [];
    auxiliaryDialogShow.value = true;
}
function setAsubWithAAE(params: any) {
    batchDepartmentData.value[costAsubIndex.value].asub_aae = params.map((item: any) => item.id).join(",");
    batchDepartmentData.value[costAsubIndex.value].asub_id = clickAsubId.value;
    setTimeout(() => {
        // costAsubRef.value[costAsubIndex.value].blur();
    });
}

const batchDisposeData = ref(new batchData(false, "", []));
let uniqueValues = new Set();

function batchDispose() {
    if (selectCardId.value.length == 0) {
        ElNotify({
            message: "亲，请先选择需要变更的资产卡片哦~",
            type: "warning",
        });
        return false;
    }
    if (selectCardId.value.every((item) => item.created_period === FAPeriod.value && item.data_type == 2)) {
        ElNotify({
            message: "亲，您选择的所有资产均为本期新增，不可处置哦~",
            type: "warning",
        });
        return false;
    }
    uniqueValues.clear();
    selectCardId.value.forEach((obj) => {
        uniqueValues.add(obj.fa_property);
    });
    if (Array.from(uniqueValues).length > 1) {
        ElNotify({
            message: "亲，请选择相同属性的资产卡片哦~",
            type: "warning",
        });
        return false;
    }
    if (uniqueValues.has("2")) {
        ElNotify({
            message: "亲，您选择的所有资产均为长期待摊费用，不可处置哦~",
            type: "warning",
        });
        return false;
    }
    batchDisposeData.value = new batchData(false, "", []);
    batchDisposeData.value.ids = selectCardId.value.reduce((pre: number[], cur: IAssetDetail) => {
        if (!(cur.created_period === FAPeriod.value && cur.data_type == 2)) pre.push(cur.fa_id);
        return pre;
    }, []);
    batchDisposeData.value.visible = true;
    const list = [
        {
            value: 1,
            label: "出售",
        },
        {
            value: 2,
            label: "报废",
        },
        {
            value: 3,
            label: "其他",
        },
    ];
    disposeList.value = list;
    if (selectCardId.value[0].fa_property !== "0") {
        disposeList.value.splice(1, 1);
    }
}
function confirmBatchDispose() {
    if (batchFlag.value) return false;
    batchFlag.value = true;
    if (!batchDisposeData.value.method) {
        batchFlag.value = false;
        ElNotify({
            type: "warning",
            message: "请选择处置方式",
        });
        return false;
    }
    const disposeData = {
        faids: batchDisposeData.value.ids,
        pid: searchInfo.period,
        type: batchDisposeData.value.method,
    };
    request({
        url: `/api/FaChange/BatchDisposal`,
        method: "post",
        data: disposeData,
    })
        .then((res: IResponseModel<any>) => {
            if (res.state === 1000) {
                checkedIds.value = [];
                res.data.forEach((item: any) => {
                    checkedIds.value.push(item.changeId);
                });
                getTableData();
                emits("getChangeData");
                const increaseFa = selectCardId.value.some((item) => item.created_period === FAPeriod.value && item.data_type == 2);
                const voucherMsg = increaseFa
                    ? "亲，您的处置已保存(本期新增的资产已跳过)，需要立即生成凭证吗？"
                    : "您的处置已保存，需要立即生成凭证吗？";
                if (useAccountSetStoreHook().permissions.includes("fixedassets-card-cancreatevoucher")) {
                    batchDisposeData.value.visible = false;
                    ElConfirm(voucherMsg)
                        .then((r: boolean) => {
                            if (r) {
                                request({
                                    url: `/api/FAVoucher/BatchGenerateChangeVoucher?changeType=1080&index=0&index2=0&isMerge=true`,
                                    method: "post",
                                    data: checkedIds.value,
                                })
                                    .then((res: IResponseModel<IGenerateChangeVoucher>) => {
                                        const voucherData: IAssetGenerateChangeVoucher = { ...res.data, changeType: 1080 };
                                        request({
                                            url: `/api/FAPeriod/GetMaxDate?pid=${searchInfo.period}`,
                                            method: "post",
                                        }).then(() => {
                                            emits("goToVoucher", voucherData, "资产处置凭证", checkedIds.value, "", 0, true);
                                        });
                                    })
                                    .catch((err) => {
                                        console.log(err);
                                    });
                            } else {
                                emits("getChangeData");
                            }
                        })
                        .finally(() => {
                            getTableData();
                        });
                }
            } else if (res.state === 2000) {
                ElNotify({
                    type: "warning",
                    message: res.msg ?? "处置失败啦！请联系侧边栏客服！",
                });
            }
        })
        .finally(() => {
            batchFlag.value = false;
        });
}

// 导入资产
const importDialogVisible = ref(false);
function downloadAsubChangeFile() {
    request({
        url: `/api/FixedAssets/CanChangeAsub`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            if (res.data) {
                globalExport(`/api/FixedAssets/ExportAsubChangeTemplate?r%=${Math.random()}`);
            } else {
                ElNotify({
                    message: "只能变更以前月份资产的相关科目，请重新选择。",
                    type: "warning",
                });
            }
        } else {
            ElNotify({
                message: "出现异常，请稍候重试或联系客服处理",
                type: "warning",
            });
        }
    });
}

const uploadSuccess = (res: IResponseModel<{ isSuccess: boolean; changeIdList: number[] }>) => {
    if (res.state === 1000) {
        if (res.data.isSuccess) {
            if (res.data.changeIdList.length == 0) {
                ElNotify({
                    message: "导入失败，没有符合条件的变更记录。",
                    type: "warning",
                });
            } else {
                importDialogVisible.value = false;
                if (checkPermission(["fixedassets-card-cancreatevoucher"])) {
                    ElConfirm("导入成功！科目调整记录已保存，是否立即将资产原科目的余额转移到新科目上？").then((r: boolean) => {
                        if (r) {
                            let changeIds = res.data.changeIdList;
                            request({
                                url: `/api/FAVoucher/BatchGenerateChangeVoucher?changeType=1090&index=0&index2=0&isMerge=true`,
                                method: "post",
                                data: changeIds,
                            }).then((res: IResponseModel<IGenerateChangeVoucher>) => {
                                const emitData: IAssetGenerateChangeVoucher = { ...res.data, changeType: 1090 };
                                emits("goToVoucher", emitData, "批量生成变更凭证", changeIds, "", "", true);
                            });
                        }
                    });
                } else {
                    ElNotify({
                        type: "success",
                        message: "导入成功！",
                    });
                }
            }
        } else {
            ElNotify({
                message: "导入失败，请稍后重试或联系客服",
                type: "warning",
            });
        }
    } else if (res.state === 2000) {
        ElNotify({
            message: res.msg,
            type: "warning",
        });
    } else {
        ElNotify({
            message: "导入失败，请稍后重试或联系客服",
            type: "warning",
        });
    }
};

const periodList = ref<IPeriod[]>([]);

const faTypeOptions = ref<Array<Option>>([]);
const faTypeList = ref<ISelectStrItem[]>([]);
function getFaTypeApi() {
    request({
        url: `/api/FixedAssetsType/List`,
        method: "get",
    }).then((res: IResponseModel<IFixedAssetsType[]>) => {
        if (res.state == 1000) {
            faTypeList.value = res.data.reduce((prev: ISelectStrItem[], item: IFixedAssetsType) => {
                prev.push({
                    value: String(item.typeId),
                    label: item.typeNum + "-" + item.typeName,
                });
                return prev;
            }, []);
            faTypeOptions.value = faTypeList.value.map((i) => new Option(Number(i.value), i.label));
            searchInfo.fa_type = faTypeOptions.value.map((item) => item.id);
        }
    });
}

const departmentList = toRef(useAssistingAccountingStore(), "departmentList");
const departmentOptions = ref<Array<Option>>([]);
watch(
    () => departmentList.value,
    () => {
        departmentOptions.value = departmentList.value.map((i) => new Option(Number(i.aaeid), i.aaname));
        searchInfo.department = departmentOptions.value.map((item) => item.id);
    },
    {
        immediate: true,
    }
);
const searchOptions = ref();
watch(
    () => [faTypeOptions, departmentOptions],
    () => {
        searchOptions.value = {
            fa_type: faTypeOptions.value,
            department: departmentOptions.value,
        };
    },
    {
        immediate: true,
        deep: true,
    }
);

function getPrintParams() {
    return {
        period: searchInfo.period,
        datatype: 2,
        status: searchInfo.status,
        time_s: searchInfo.startTime,
        time_e: searchInfo.endTime,
        fa_num: getQueryVal("fa_num"),
        fa_type: getCommonSelect("fa_type", faTypeOptions.value),
        fa_name: getQueryVal("fa_name"),
        fa_model: getQueryVal("fa_model"),
        department: getCommonSelect("department", departmentOptions.value),
        note: getQueryVal("note"),
        sortField: sortField.value,
        sortOrder: sortOrder.value,
    };
}

function PrintList() {
    if (!selectCardId.value.length) {
        ElNotify({
            message: "请选择要打印明细的资产",
            type: "warning",
        });
        return;
    }
    const url = "/api/FixedAssets/PrintDetail";
    const data = {
        pid: searchInfo.period,
        faids: JSON.stringify(selectCardId.value.map((item) => item.fa_id)),
    };
    globalPostPrint(url, data);
}
function PrintCard() {
    if (!selectCardId.value.length) {
        ElNotify({
            message: "请选择要打印标签的资产",
            type: "warning",
        });
        return;
    }
    let faids = selectCardId.value.map((item) => item.fa_id);
    globalPostPrint(`/api/FixedAssets/PrintCard`, { faids: JSON.stringify(faids) });
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint("fixedAssetsList", `/api/FixedAssets/Print`, {}, true);

//判断是否结账
const isFinishAccount = ref<boolean>(false);
function checkSettleAccount() {
    request({
        url: `/api/FAPeriod/CheckSettleAccount?pid=${searchInfo.period}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        isFinishAccount.value = res.data;
    });
}

const ToolNumRef = ref<any>([]);
// 跳转资产详情页
const showDetail = (faid: number, pid: number) => {
    ToolNumRef?.value[faid].hide();
    request({
        url: `/api/FixedAssets?faid=${faid}&pid=${pid}`,
    }).then((res: IResponseModel<IAssetDetail>) => {
        if (res.state == 1000) {
            emits("showDetail", res.data);
        }
    });
};

const columns = ref<Array<IColumnProps>>([]);
const fixedassetsListColumnsSet = ref<Array<IColumnProps>>([]);
function getQueryVal(name: string) {
    if (isKeyOfIFSearchItem(name)) {
        const baseInfo = searchInfo[name];
        const filterInfo = filterSearchInfo[name];
        return filterInfo ? `${baseInfo}${baseInfo ? "|-|" : ""}${filterInfo}` : baseInfo;
    }
    return "";
}
function getCommonSelect(name: keyof IFSearchItem, option: Option[]) {
    const list1 = searchInfo[name] as number[];
    const list2 = filterSearchInfo[name] as number[];
    if (list2.length > 0) {
        if (list1.length === option.length && list2.length === option.length) {
            return "";
        }
        const set2 = new Set(list2);
        const commonList = list1.filter((value) => set2.has(value));
        if (commonList.length) {
            return commonList.join();
        } else if (list1.length) {
            return list1.join();
        } else {
            return list2.join();
        }
    } else {
        return list1.length === option.length ? "" : list1.join();
    }
}
function restFilterInfo() {
    filterSearchInfo.fa_type = [];
    filterSearchInfo.department = [];
    filterSearchInfo.fa_num = "";
    filterSearchInfo.fa_name = "";
    filterSearchInfo.fa_model = "";
    filterSearchInfo.note = "";
    filterSearchInfo.status = -1;
}

const tableData = ref<IAssetDetail[]>([]);
const getTableData = () => {
    loading.value = true;
    const data = {
        period: searchInfo.period === -1 ? "" : searchInfo.period,
        datatype: 2,
        status: searchInfo.status,
        time_s: searchInfo.startTime,
        time_e: searchInfo.endTime,
        fa_num: getQueryVal("fa_num"),
        fa_name: getQueryVal("fa_name"),
        fa_model: getQueryVal("fa_model"),
        fa_type: getCommonSelect("fa_type", faTypeOptions.value),
        fa_property: searchInfo.fa_property,
        department: getCommonSelect("department", departmentOptions.value),
        note: getQueryVal("note"),
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        sortField: sortField.value,
        sortOrder: sortOrder.value,
    };
    if (data.period !== "") {
        request({
            url: `/api/FixedAssets/PagingList`,
            method: "get",
            params: data,
        })
            .then((res: IResponseModel<{ data: IAssetDetail[]; count: number }>) => {
                if (res.state === 1000) {
                    // 当前页没有数据时，跳回上一页
                    if (res.data.data.length === 0 && paginationData.currentPage > 1) {
                        return paginationData.currentPage--;
                    }
                    ColumnsQuery();
                    tableData.value = res.data.data;
                    paginationData.total = res.data.count;
                }
            })
            .finally(() => {
                if (periodList.value) {
                    currentPeriodInfo.value = periodList.value.find((item) => searchInfo.period === item.pid)?.periodInfo || "";
                }
                loading.value = false;
            });
    } else {
        loading.value = false;
    }
};

function EnableAddAndDel() {
    request({
        url: `/api/Depreciation/ExistsByPeriod?flag=2&pid=` + searchInfo.period,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (accountSet?.fixedasset === 2) {
            addAndDelShow.value = true;
            return;
        }
        if (res.state === 1000 && !res.data) {
            addAndDelShow.value = false;
        } else {
            addAndDelShow.value = true;
        }
    });
}
const initSearchInfo = () => {
    // getDepartmentApi()
    return Promise.all([getFaTypeApi()]).then(() => {
        getTableData();
    });
};

// 禁用日期
function disabledDateStart(time: Date) {
    return time.getTime() > dayjs(searchInfo.endTime).valueOf();
}

function disabledDateEnd(time: Date) {
    return time.getTime() < dayjs(searchInfo.startTime).valueOf();
}

function disabledDate(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableData();
});

watch(
    () => props.period,
    () => {
        periodList.value = props.period as IPeriod[];
        handleReset();
    },
    { immediate: true }
);
watch(
    () => props.pid,

    () => {
        searchInfo.period = props.pid;
        FAPeriod.value = props.pid;
        currentPeriodInfo.value = periodList.value!.find((item) => searchInfo.period === item.pid)?.periodInfo || "";
        modifiedPeriod.value = searchInfo.period;
    },
    { immediate: true }
);
watch(
    () => props.month,
    () => {
        startMonth.value = props.month;
    },
    { immediate: true }
);
function changeMonth() {
    searchInfo.period = periodList.value.find((item: any) => item.time === startMonth.value)?.pid || 0;
    modifiedPeriod.value = searchInfo.period;
}

watchEffect(() => {
    emits("changeStatus", searchInfo.status);
});
const handleZoomChangeList = () => {
    tableMaxHeight.value = handleZoomChange();
};
onMounted(() => {
    handleZoomChangeList();
    window.addEventListener("resize", handleZoomChangeList);
    EnableAddAndDel();
    initSearchInfo();
});
const showProvisionChange = computed(() => {
    return [2, 3].includes(accountingStandard.value);
});

function setFaNumForSearch(fa_num: string) {
    searchInfo.fa_num = fa_num;
}

defineExpose({ setFaNumForSearch, handleSearch, FAPeriod, getTableData, checkSettleAccount });

const readonly = ref(false);
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
let isNeedSaveToVoucherForDelete = false;
function handleGetAttachFiles(row: IAssetDetail) {
    readonly.value = row.status === "已减少" || isFinishAccount.value;
    getAssetsAttachFiles(row, uploadFileDialogRef);
}
function resetParams() {
    isNeedSaveToVoucherForDelete = false;
}
async function handleSaveAttachFiles(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
    const { faId } = _params;
    function successCallBack() {
        const row = tableData.value.find((item) => item.fa_id === faId);
        if (row) {
            row.attachsCount = fileList.length;
            row.attachFiles = fileList.map((item) => item.fileId).join(",");
        }
    }
    isNeedSaveToVoucherForDelete = false;
    if (!delFileids.length) {
        saveAttachFileFn(faId, newFileids, delFileids, fileList, successCallBack, resetParams);
        return;
    }
    const needToast = await checkNeedToastWithBillAndVoucher(_params.faId, delFileids.join(","));
    if (!needToast) {
        saveAttachFileFn(faId, newFileids, delFileids, fileList, successCallBack, resetParams);
        return;
    }
    showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
        isNeedSaveToVoucherForDelete = batchDelte;
        saveAttachFileFn(faId, newFileids, delFileids, fileList, successCallBack, resetParams, isNeedSaveToVoucherForDelete);
    });
}
function handleInput(value: string) {
    const regexDefault = /^\d+(?:\.\d{0,2})?/;
    const regexUseYear = /^\D*(\d+)/;
    const regex = batchChangeData.value.method === "usefullifeChange" ? regexUseYear : regexDefault;
    const match = value.match(regex);
    batchChangeData.value.value = match ? match[0] : "";
}

//列设置弹窗
const columnSetShow = ref(false);
const columnSetRef = ref<InstanceType<typeof ColumnSet>>();
const allColumns = ref<IColItem[]>([]);
function ColumnsQuery() {
    fixedassetsListColumnsSet.value = setColumns("2", true, accountingStandard.value);
    if (!searchInfo.showInfo) {
        columns.value =
            allColumns.value.length > 0
                ? getShowColumn(allColumns.value, fixedassetsListColumnsSet.value)
                : setColumns("2", searchInfo.showInfo);
    } else {
        columns.value = setColumns("2", searchInfo.showInfo, accountingStandard.value);
    }
    watchDrop();
}
function getColumnSetList() {
    getColumnListApi(module.value)
        .then((res) => {
            allColumns.value = res.data;
        })
        .catch((err) => {
            allColumns.value = [];
        });
}
getColumnSetList();
function openColSet() {
    columnSetShow.value = true;
    columnSetRef.value?.initData();
    nextTick(() => {
        columnSetRef.value?.rowDrop();
    });
}
function saveColumnSet(data: IColItem[]) {
    if (!searchInfo.showInfo) {
        columns.value = getShowColumn(data, fixedassetsListColumnsSet.value);
    }
    watchDrop();
    getColumnSetList();
}
//头部列拖拽设置
const setModule = "FixedassestList";
function cellDrop(oldIndex: number, newIndex: number) {
    let index1 = allColumns.value.findIndex((v) => v.columnName === columns.value[oldIndex].label);
    let index2 = allColumns.value.findIndex((v) => v.columnName === columns.value[newIndex].label);
    allColumns.value = alterArrayPos(allColumns.value, index1, index2);
    columns.value = alterArrayPos(columns.value, oldIndex, newIndex);
    columnSetRef.value?.saveData(module.value, allColumns.value);
}
function watchDrop() {
    nextTick(() => {
        fixedassetsListRef.value?.columnDrop(fixedassetsListRef.value?.$el, columns.value, searchInfo.showInfo);
    });
}

function handleScroll() {
    fixedassetsListRef.value?.$el.click();
}
// 表头字段模糊搜索
function filterSearch(prop: string, data: any) {
    if (isKeyOfIFSearchItem(prop)) {
        if (typeof data === "string" || typeof data === "number") {
            if (prop === "status") {
                (searchInfo[prop] as number) = Number(data);
            }
            if (typeof filterSearchInfo[prop] === "string") {
                (filterSearchInfo[prop] as string) = data.toString();
            }
            if (typeof filterSearchInfo[prop] === "number") {
                (filterSearchInfo[prop] as number) = Number(data);
            }
            getTableData();
        } else {
            if (data.length > 0) {
                const filteredData: number[] = data.filter((item: any): item is number => typeof item === "number");
                (filterSearchInfo[prop] as number[]) = filteredData;
                getTableData();
            }
        }
    }
}
//资产编号字段复制
function handleCopy() {
    const oText = document.getElementById("fa_num")!.innerText || "";
    copyText(oText);
    ElNotify({ type: "success", message: "复制成功！" });
}
//排序
const sortList: {
    [key: string]: number;
} = {
    default: 0,
    faNu: 1,
    faType: 2,
    faName: 3,
    faStartDate: 4,
};
let sortNum = ref(0);
let sortOrder = ref(-1); //默认； 0升序； 1降序
let sortField = ref(0); //申请排序的是哪个字段
function getSortCommon(name: string, event: any) {
    if (sortField.value !== 0 && sortField.value !== sortList[name]) {
        sortNum.value = 0;
        let sortItems = document.querySelectorAll(".header-caret");
        for (let i = 0; i < sortItems.length; i++) {
            sortItems[i].classList.remove("ascending");
            sortItems[i].classList.remove("descending");
        }
    }

    sortNum.value++;
    sortField.value = sortList[name];
    if (sortNum.value === 1) {
        event.target.classList.remove("ascending");
        event.target.classList.add("descending");
        sortOrder.value = 1;
    } else if (sortNum.value === 2) {
        event.target.classList.remove("descending");
        event.target.classList.add("ascending");
        sortOrder.value = 0;
    } else {
        sortNum.value = 0;
        event.target.classList.remove("ascending");
        event.target.classList.remove("descending");
        sortOrder.value = -1;
        sortField.value = 0;
    }
    getTableData();
}

watch(
    () => selectCardId.value[0]?.fa_property,
    (val) => {
        if (val === undefined) return;
        depreciationTypeList[1].label = selectCardId.value[0].fa_property === '0'  ? "不折旧" : "不摊销";
        batchChangeWayList.value = getChangeList(selectCardId.value[0].fa_property, accountingStandard.value, true);
    }
);

const showStatusList = ref<Array<any>>([]);
const showfaPropertyList = ref<Array<any>>([]);
const batchChangeWayList = ref<Array<any>>([]);
const showbatchChangeWayList = ref<Array<any>>([]);
const showDepreciationTypeList = ref<Array<any>>([]);
const showChangeStatusList = ref<Array<any>>([]);
const showdepartmentList = ref<Array<any>>([]);
const showDisposeList = ref<Array<any>>([]);
watchEffect(() => {
    showbatchChangeWayList.value = JSON.parse(JSON.stringify(batchChangeWayList.value));
    showDepreciationTypeList.value = JSON.parse(JSON.stringify(depreciationTypeList));
});
watchEffect(() => {
    showStatusList.value = JSON.parse(JSON.stringify(statusList)); 
    showfaPropertyList.value = JSON.parse(JSON.stringify(faPropertyList));
    showChangeStatusList.value = JSON.parse(JSON.stringify(changeStatusList));
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
    showDisposeList.value = JSON.parse(JSON.stringify(disposeList.value));
});
function statusFilterMethod(value: string) {
    showStatusList.value = commonFilterMethod(value, statusList, 'name');
}
function faPropertyFilterMethod(value: string) {
    showfaPropertyList.value = commonFilterMethod(value, faPropertyList, 'label');
}
function batchChangeWayFilterMethod(value: string) {
    showbatchChangeWayList.value = commonFilterMethod(value, batchChangeWayList.value, 'name');
}
function batchChangedepTypeFilterMethod(value: string) {
    showDepreciationTypeList.value = commonFilterMethod(value, depreciationTypeList, 'label');
}
function batchChangeStatusFilterMethod(value: string) {
    showChangeStatusList.value = commonFilterMethod(value, changeStatusList, 'label');
}
function departmentFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value, 'aaname');
}
function costAsubFilterMethod(value: string) {
    showCostAsubList.value = commonFilterMethod(value, costAsubListAll.value, 'label');
}
function disposeFilterMethod(value: string) {
    showDisposeList.value = commonFilterMethod(value, disposeList.value, 'label');
}
</script>

<style scoped lang="less">
:deep(tbody .linkColor .cell) {
    color: var(--link-color);
}
:deep(.link + .link) {
    margin-left: 0px !important;
}
ul.operation {
    margin: 0;
    padding: 0;
    li {
        cursor: pointer;
        height: 36px;
        line-height: 36px;
        list-style: none;
        border-radius: 3px;
        &:hover {
            background-color: #ebeef5;
            color: #42b547;
        }
    }
}

.asub-change-info {
    margin: 10px 40px;
    font-size: var(--h5);
    line-height: var(--line-height);
}

.line-item-title {
    width: 100px !important;
}
:deep(.el-table__cell) {
    .el-tooltip {
        width: 100% !important;
    }
}
:deep(.el-select-dropdown__list) {
    max-height: 200px;
    // overflow-y: auto;
}
:deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    & span {
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
:deep(.custom-table tbody tr td .cell input[type="text"]) {
    border: none;
}
:deep(.batch-depart-id) {
    width: 100%;
    > div {
        width: 100%;
    }
    .el-input {
        width: 100%;
    }
    .el-input__wrapper {
        width: 100%;
    }
}
.batch-depart-ratio {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    > div {
        min-width: 0;
        flex: 1;
    }
    :deep(.el-input__wrapper) {
        padding: 1px 10px;
    }
}
.date-picker {
    :deep(.el-date-editor) {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
        & .el-input__suffix-inner {
            position: absolute;
            right: 30px;
            top: 9px;
        }
    }
    :deep(.el-month-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
    :deep(.el-year-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
}
body[erp] {
    ul.operation {
        margin: 0 -12px;
        padding: 0;
        li {
            padding: 0 12px;
            cursor: pointer;
            height: 36px;
            line-height: 36px;
            list-style: none;
            border-radius: 3px;
            &:hover {
                background-color: #ebeef5;
                color: #333;
            }
        }
    }
}
</style>
<style lang="less">
body[erp] {
    .content {
        .fixedlist-import-dialog.custom-confirm {
            .el-dialog__body {
                .import-dialog {
                    .buttons {
                        flex-direction: row-reverse;
                        justify-content: flex-start;
                    }
                }
            }
        }
    }
}
</style>
