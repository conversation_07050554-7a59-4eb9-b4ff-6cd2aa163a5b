export interface ITableItem {
    as_id: string;
    mainkey: string;
    subkey: string; // id
    subsubkey: string; // 父级id
    num1: string;
    num2: string;
    value1: string; // 编码
    value2: string; // 名称
    value3: string; // 关键词
    description: string;
    created_by: string;
    created_date: string;
    modified_by: string;
    modified_date: string;
    haschild: number; // 是否有子级，1有，0无
    num3: string; //状态
}

export interface ISearchBack {
    currentPage: number;
    data: Array<ITableItem>;
    pageSize: number;
    total: number;
}
