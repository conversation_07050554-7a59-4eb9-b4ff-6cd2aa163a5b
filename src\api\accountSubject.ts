import { computed } from 'vue';
import type { IResponseModel } from '@/util/service';
import { request } from "@/util/service";

export const getAllAsubApi = async (showDisabled = false) => {
    return (await request({
        url: "/api/AccountSubject/List?showDisabled=" + showDisabled,
        method: "get",
    })) as any as IResponseModel<Array<IAccountSubjectModel>>;
};

export interface IAccountSubjectModelWithPinyin extends IAccountSubjectModel {
    pinyinArr: Array<string>;
}
export interface IAccountSubjectModelWithChecked extends IAccountSubjectModel {
    isChecked?: boolean;
}
export interface IAccountSubjectModel {
    //科目Id
    asubId: number;
    //科目父Id
    parentId: number;
    //科目编码
    asubCode: string;
    //科目类别
    asubType: number;
    //科目名称
    asubName: string;
    //完整科目名称
    asubAAName: string;
    //科目方向
    direction: number;
    //借记码
    acronym: string;
    //数量核算单位Id
    measureUnitId: string;
    //数量核算单位
    measureUnit: string;
    //状态
    status: number;
    //辅助核算
    assistingAccounting: number;
    //辅助核算项
    aatypeNames: string;
    //数量核算
    quantityAccounting: number;
    //科目级别
    asubLevel: number;
    //科目备注
    note: string;
    //是否开启外币核算
    foreigncurrency: number;
    //是否开启外币核算期末调汇
    fcAdjust: number;
    //开启外币核算的外币IDs
    fcIds: string;
    //辅助核算项目
    aatypes: string;
    //允许为空的核酸类别
    aatypesAllowNull: string;
    //容许操作
    operation: number;
    //限定性标志
    restricted: number;
    //USCC
    uscc: string;
    //是否是叶子节点
    isLeafNode: boolean;
    // 展示的文本
    showText: string;
}

export const saveAccountSubjectApi = (data: AccountSubjectSaveModel) => {
    return request({
        url: `/api/AccountSubject/${data.parentId ? "Child" : "Root"}`,
        method: "post",
        data: data,
    });
};

export class AccountSubjectSaveModel {
    //科目名称
    asubName: string = "";
    //科目编码
    asubCode: string = "";
    //修改前的科目编码
    asubCodeBefore: string = "";
    //科目类型
    asubType: number = 0;
    //父节点Id
    parentId: number | undefined = undefined;
    //科目方向 方向为1借，2贷
    direction: number = 1;
    //是否开启辅助核算 0-停用 1-启用
    assistingaccounting: number = 0;
    //辅助核算项目
    aaTypes: string = "";
    //允许为空的辅助核算类别
    allowNullAaTypes: string = "";
    //科目有数据情况下传递的默认辅助核算条目组合
    aaEntries: string = "";
    //是否开启数量核算 0-停用 1-启用
    quantityaccounting: number = 0;
    //单位 数量核算开启单位必须
    measureunit: string = "";
    //单位Id （业财用）
    measureunitId: string = "";
    //科目状态 0-启用 1-停用
    status: number = 0;
    //是否进行期末调汇
    fcadjust: number = 0;
    //是否开启外币核算 0-停用 1-启用
    foreigncurrency: number = 0;
    //外币核算值
    fcids: string = "";
    //外币核算编辑类型 0-新增 1-修改
    fcEditType: 0 | 1 = 0;
    //是否为已使用科目添加外币信息
    isUsedAsubAddFc: string = "";
    //0-添加根科目 1-添加子科目 2-编辑科目 3-只修改基础内容 4-删除
    editType: 0 | 1 | 2 | 3 | 4 = 0;
    //限定性标志 0-非限定性 1-限定性
    restricted: number = 0;
    //纳税人识别号 六大往来科目选择下拉框时使用
    uscc: string = "";
    //科目Id
    asubId: number = 0;
}
