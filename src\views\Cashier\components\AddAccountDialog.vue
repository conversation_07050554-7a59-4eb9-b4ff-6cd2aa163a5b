<template>
    <el-dialog :title="titleName" center width="440px" v-model="isShow" class="custom-confirm dialogDrag" :modal-class="modalClass" @closed="resetForm">
        <div class="add-account-content" v-dialogDrag>
            <div class="form-component" :class="hasIconTip ? 'journal' : ''">
                <el-form :model="searchInfo" label-width="80px" ref="formRef">
                    <el-form-item label="编码：" class="code">
                        <input class="input" v-model="searchInfo.AC_NO" placeholder="示例：001" />
                    </el-form-item>
                    <el-form-item label="银行：" class="name" v-show="props.j_type === 1020">
                        <Select 
                            v-model="searchInfo.bankid" 
                            placeholder="请选择银行" 
                            :teleported="false" 
                            :filterable="true"
                            :filter-method="bankFilterMethod"
                        >
                            <Option v-for="item in showBankList" :key="item.id" :value="item.id" :label="item.name" />
                        </Select>
                    </el-form-item>
                    <el-form-item label="账户名称：" class="name">
                        <Tooltip :content="searchInfo.AC_NAME" :isInput="true">
                            <input
                                class="input"
                                v-model="searchInfo.AC_NAME"
                                :placeholder="props.j_type === 1010 ? '示例：现金' : '示例：中国工商银行深圳宝安支行'"
                                @input="
                                    handleAAInput(LimitCharacterSize.Default, $event, '账户名称', 'name', changeSearchInfo, getErrorMsg('name'))
                                "
                                @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                            />
                        </Tooltip>
                    </el-form-item>
                    <el-form-item label="银行卡号：" v-show="props.j_type === 1020">
                        <input
                            class="input"
                            v-model="searchInfo.BANK_ACCOUNT"
                            placeholder="请输入完整的银行卡号"
                            @input="
                                handleAAInput(
                                    LimitCharacterSize.Default,
                                    $event,
                                    '银行卡号',
                                    'bank_account',
                                    changeSearchInfo,
                                    getErrorMsg('bankNo')
                                )
                            "
                            @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                        />
                    </el-form-item>
                    <el-form-item label="币别：" class="currency">
                        <Select 
                            v-model="searchInfo.CURRENCY" 
                            placeholder="请选择" 
                            :teleported="false" 
                            :filterable="true"
                            :filter-method="currencyFilterMethod"
                        >
                            <Option v-for="item in currencyListOption" :key="item.id" :value="item.id" :label="item.name" />
                        </Select>
                    </el-form-item>
                    <el-form-item label="会计科目：" class="subject">
                        <!-- <Select v-model="searchInfo.ASUB" :teleported="false" :filterable="true">
                            <Option value="" label="请选择" />
                            <Option v-for="item in subjectData" :key="item.asubCode" :label="getSelectLabel(item)" :value="item.asubId" />
                        </Select> -->

                        <SelectV2
                            v-model="searchInfo.ASUB"
                            placeholder="请选择"
                            :teleported="false"
                            :fit-input-width="true"
                            :filterable="true"
                            :tool-tip-options="{dynamicWidth: true}"
                            :options="subjectDataOption"
                            :remote="true"
                            :filter-method="subjectFilterMethod"
                            @visible-change="handleSubjectVisibleChange"
                            :isSuffixIcon="true"
                        ></SelectV2>
                    </el-form-item>
                </el-form>
            </div>
            <div class="tip-content">
                <div class="tip" v-show="!hasIconTip">说明：关联会计科目之后，资金数据自动和总账核对是否一致，同时也支持自动生成凭证。</div>
                <div class="has-icon-tip" v-show="hasIconTip">
                    关联会计科目之后，资金数据自动和总账核对是否一致，同时也支持自动生成凭证。
                </div>
            </div>
            <div class="buttons">
                <a class="solid-button mr-10" @click="handleSave">保存</a>
                <a class="button" @click="handleCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, reactive, watch, nextTick, watchEffect } from "vue";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";

import type { ICurrencyList } from "./types";
import { getUrlSearchParams } from "@/util/url";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import SelectV2 from "@/components/SelectV2/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const isErp = ref(window.isErp);

const getErrorMsg = (type: "name" | "bankNo") => {
    if (type === "name") {
        const name = props.j_type === 1010 ? "现金账户" : "银行账户";
        return "亲，" + name + "名称不能超过64个字符！";
    } else if (type === "bankNo") {
        return "亲，银行账号不能超过64个字符！";
    }
};
const props = withDefaults(
    defineProps<{
        modelValue: boolean;
        j_type: number;
        currencyList: ICurrencyList[];
        titleName?: string;
        hasIconTip?: boolean;
        modalClass?: string;
    }>(),
    {
        titleName: "",
        hasIconTip: false,
    }
);
const emit = defineEmits<{
    (event: "update:modelValue", value: boolean): void;
    (event: "saveSuccess", ac_no: string): void;
}>();
const isShow = computed({
    get() {
        return props.modelValue;
    },
    set(value: boolean) {
        emit("update:modelValue", value);
    },
});
const titleName = computed(() => {
    if (props.titleName) return props.titleName;
    return props.j_type === 1010 ? "新增现金账户" : "新增银行账户";
});

const subjectData = ref<Array<IAccountSubjectModel>>([]);
const searchInfo = reactive({
    AC_NO: "",
    AC_NAME: "",
    CURRENCY: 1,
    ASUB: "",
    BANK_ACCOUNT: "",
    AC_ID: "",
    bankid: "",
});

const changeSearchInfo = (key: string, value: string) => {
    if (key === "bank_account") {
        searchInfo.BANK_ACCOUNT = value;
    } else if (key === "name") {
        searchInfo.AC_NAME = value;
    }
};

function getSelectLabel(subject: IAccountSubjectModel) {
    return subject.asubCode + " " + subject.asubAAName;
}

let isJudgeng = false;
const CheckOnSubject = () => {
    isJudgeng = true;
    const params = {
        asub: searchInfo.ASUB,
        currency: searchInfo.CURRENCY,
        acId: searchInfo.AC_ID,
    };
    const currencyItem = props.currencyList.find((item) => item.id === searchInfo.CURRENCY);
    const asubItem = subjectData.value.find((item) => item.asubId + "" == searchInfo.ASUB);
    request({ url: "/api/CDAccount/CheckOnSubject?" + getUrlSearchParams(params), method: "post" })
        .then((res: any) => {
            const result = (res.data as string).toUpperCase();
            if (result == "FC") {
                if (currencyItem?.isBaseCurrency) {
                    if (asubItem?.foreigncurrency === 1) {
                        ElNotify({ type: "warning", message: "亲，该会计科目不具有" + currencyItem.name + "核算" });
                        searchInfo.ASUB = "";
                        return;
                    }
                } else {
                    ElNotify({ type: "warning", message: "亲，该会计科目不具有" + currencyItem?.name + "核算" });
                    searchInfo.ASUB = "";
                    return;
                }
            }
            if (result == "USE") {
                ElNotify({ type: "warning", message: "亲，该科目已被选择" });
                searchInfo.ASUB = "";
                return;
            }
        })
        .finally(() => {
            isJudgeng = false;
        });
};
watch(
    () => searchInfo.ASUB,
    (val) => {
        if (val !== "") {
            nextTick().then(() => {
                CheckOnSubject();
            });
        }
    }
);
watch(
    () => searchInfo.CURRENCY,
    () => {
        nextTick().then(() => {
            CheckOnSubject();
        });
    }
);

let isSaving = false;
const handleSave = () => {
    if (isJudgeng) return;
    if (isSaving) return;
    isSaving = true;
    const reg = /^[0-9a-zA-Z]{1,3}$/;
    if (searchInfo.AC_NO.trim() === "") {
        ElNotify({ type: "warning", message: "亲，账户编码不能为空" });
        isSaving = false;
        return;
    }
    if (!reg.test(searchInfo.AC_NO.trim())) {
        ElNotify({ type: "warning", message: "亲，编码只能输入三位及以下的数字字母组合" });
        isSaving = false;
        return;
    }
    if (searchInfo.AC_NAME.trim() === "") {
        ElNotify({ type: "warning", message: "亲，账户名称不能为空" });
        isSaving = false;
        return;
    }
    if (props.j_type == 1020) {
        if (!searchInfo.bankid && !isErp.value) {
            ElNotify({ type: "warning", message: "亲，请选择银行" });
            isSaving = false;
            return;
        }
        if (searchInfo.AC_NAME.length > LimitCharacterSize.Default) {
            ElNotify({ type: "warning", message: "亲，银行账户名称超过64个字，请修改后重新保存" });
            isSaving = false;
            return false;
        }
        if (searchInfo.BANK_ACCOUNT.length > 64) {
            ElNotify({ type: "warning", message: "亲，银行账号超过64个字，请修改后重新保存" });
            isSaving = false;
            return;
        }
    } else {
        if (searchInfo.AC_NAME.length > LimitCharacterSize.Default) {
            ElNotify({ type: "warning", message: "亲，现金账户名称超过64个字，请修改后重新保存" });
            isSaving = false;
            return false;
        }
    }
    const params: any = {
        acName: searchInfo.AC_NAME,
        bankAccount: searchInfo.BANK_ACCOUNT,
        acType: props.j_type,
        acNo: searchInfo.AC_NO,
        currency: searchInfo.CURRENCY,
        asub: searchInfo.ASUB,
        acId: searchInfo.AC_ID,
        bankid: ~~searchInfo.bankid,
    };
    if (!isErp.value) {
        request({
            url: "/api/CDAccount",
            method: "post",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            data: params,
        })
            .then((res: IResponseModel<{ acId: number; msg: string }>) => {
                isSaving = false;
                if (res.state == 1000) {
                    if (res.data.msg === "Success" || res.data.msg === "") {
                        ElNotify({ type: "success", message: "保存成功！" });
                        emit("saveSuccess", searchInfo.AC_NO);
                        handleCancel();
                    } else if (res.data.msg === "NO") {
                        ElNotify({ type: "warning", message: "亲，编码或会计科目重复了！" });
                    } else if (res.data.msg.indexOf("NAME") != -1) {
                        res.data.msg = res.data.msg.replace("NAME", "");
                        if (res.data.msg == "1010") {
                            ElNotify({ type: "warning", message: "亲，现金账户有相同名称，请更换后重试！" });
                        } else if (res.data.msg == "1020") {
                            ElNotify({ type: "warning", message: "亲，银行账户有相同名称，请更换后重试！" });
                        }
                    } else if (res.data.msg === "TextOverflow") {
                        if (props.j_type === 1020) {
                            ElNotify({ type: "warning", message: "亲，银行账户名称超过64个字，请修改后重新保存~ " });
                        } else {
                            ElNotify({ type: "warning", message: "亲，现金账户名称超过64个字，请修改后重新保存~ " });
                        }
                    } else if (res.data.msg === "AccountOverflow") {
                        ElNotify({ type: "warning", message: "亲，银行账号超过64个字，请修改后重新保存~ " });
                    } else {
                        ElNotify({ type: "warning", message: "保存失败了，请联系侧边栏客服" });
                    }
                } else {
                    ElNotify({ type: "warning", message: res.msg || "保存失败了，请联系侧边栏客服" });
                }
            })
            .catch(() => {
                isSaving = false;
                ElNotify({ type: "warning", message: "保存失败了，请联系侧边栏客服" });
            });
    } else {
        request({
            url: "/api/BaseDataForErp/SaveAccountType",
            method: "post",
            data: params,
        })
            .then((res: IResponseModel<string>) => {
                isSaving = false;
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败了，请联系侧边栏客服" });
                    return;
                }
                ElNotify({ type: "success", message: "保存成功！" });
                emit("saveSuccess", res.data);
                handleCancel();
            })
            .catch(() => {
                isSaving = false;
                ElNotify({ type: "warning", message: "保存失败了，请联系侧边栏客服" });
            });
    }
};
const handleCancel = () => {
    isShow.value = false;
};

const editSearchInfo = (data: any, autoAddName: string = "") => {
    Object.assign(searchInfo, data);
    searchInfo.AC_NAME = autoAddName.slice(0, LimitCharacterSize.Default);
    if (autoAddName.length > LimitCharacterSize.Default) {
        ElNotify({ type: "warning", message: getErrorMsg('name') });
    }
};
const resetForm = () => {
    searchInfo.AC_NO = "";
    searchInfo.AC_NAME = "";
    searchInfo.CURRENCY = 1;
    searchInfo.ASUB = "";
    searchInfo.BANK_ACCOUNT = "";
    searchInfo.AC_ID = "";
    searchInfo.bankid = "";
};
defineExpose({
    editSearchInfo,
    resetForm,
});

interface IBankBack {
    [key: string]: string;
}
interface IBankList {
    id: string;
    name: string;
}
const bankList = ref<Array<IBankList>>([]);
function handleGetBankList() {
    request({ url: "/api/CDAccount/Bank" }).then((res: IResponseModel<IBankBack>) => {
        if (res.state !== 1000) return;
        const list: Array<IBankList> = [];
        for (const key in res.data) {
            list.push({ id: key, name: res.data[key] });
        }
        bankList.value = list;
    });
}
props.j_type === 1020 && handleGetBankList();

const currencyListOption = ref<ICurrencyList[]>([]);
const subjectDataList = ref<any[]>([]);
const subjectDataOption = ref<any[]>([]);
const showBankList = ref<Array<IBankList>>([]);
watchEffect(() => {
    const accountSubjectList = useAccountSubjectStore().accountSubjectListWithoutDisabled;
    subjectData.value = accountSubjectList;
    subjectDataList.value = accountSubjectList.map((item) => {
        return {
            value: item.asubId,
            label: getSelectLabel(item),
        }
    });
    subjectDataOption.value = JSON.parse(JSON.stringify(subjectDataList.value));
});
watchEffect(() => {
    currencyListOption.value = JSON.parse(JSON.stringify(props.currencyList));
    showBankList.value = JSON.parse(JSON.stringify(bankList.value));
});
function handleSubjectVisibleChange(visible: boolean) {
    if (visible) {
        subjectDataOption.value = JSON.parse(JSON.stringify(subjectDataList.value));
    }
}
function currencyFilterMethod(value: string) {
    currencyListOption.value = commonFilterMethod(value, props.currencyList, 'name');
}
function subjectFilterMethod(value: string) {
    subjectDataOption.value = commonFilterMethod(value, subjectDataList.value, 'label');
}
function bankFilterMethod(value: string) {
    showBankList.value = commonFilterMethod(value, bankList.value, 'name');
}

</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.add-account-content {
    padding-top: 20px;
    .buttons {
        margin-top: 24px;
    }
    .form-component {
        &.journal {
            :deep(.el-form) {
                .currency,
                .subject {
                    .el-input__inner {
                        color: var(--font-color) !important;
                    }
                }
            }
        }
        width: 312px;
        margin: 0 auto;
        :deep(.el-form) {
            .subject {
                .el-input__inner {
                    color: gray !important;
                }
            }
            .el-form-item {
                .asub-img {
                    right: 2px;
                }
                &.code,
                &.name,
                &.currency {
                    .el-form-item__label {
                        &::before {
                            content: "*";
                            color: red;
                        }
                    }
                }
                .el-form-item__content {
                    & .input {
                        .detail-original-input(232px, 32px);
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
    }
    .detail-el-form(232px, 32px);
}
.buttons {
    border-top: 1px solid var(--border-color);
}
.el-form-item {
    &.currency {
        .detail-el-select(232px, 32px);
    }
}

.tip-content {
    display: flex;
    justify-content: center;
    align-items: center;
    .tip {
        width: 280px;
        line-height: 16px;
        font-size: 12px;
        margin-left: 30px;
        color: #666666;
        text-align: left;
        color: red;
    }
    .has-icon-tip {
        width: 333px;
        line-height: 20px;
        font-size: 14px;
        padding-left: 20px;
        color: #666666;
        text-align: left;
        background: url("@/assets/Icons/warn.png") no-repeat;
        background-size: 16px 16px;
        background-position: 1px 2px;
    }
}
</style>
