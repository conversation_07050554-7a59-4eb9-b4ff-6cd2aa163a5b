<template>
  <div
    class="content"
    v-mask="{ visible: loading, message: loadingMessage, global: true }">
    <div
      class="main-content"
      v-mask="{ visible: requestLoading, message: '加载数据中....', global: true }">
      <div class="tabs-header">
        <el-tabs
          v-model="activeName"
          class="demo-tabs">
          <el-tab-pane
            label="销项发票"
            :name="EnumTabName.销项发票">
            <div class="main-content-body">
              <InvoiceTable
                :data="outputInvoiceData"
                :columns="outputInvoiceColumns"
                :loading="tableLoading"
                rowKey="type"
                @operation="handleOutputOperation"></InvoiceTable>
              <div class="main-content-body-footer">
                快捷入口:
                <a
                  class="link"
                  @click="handleTaxAccountClick">
                  税务数字账户
                </a>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane
            label="进项发票"
            :name="EnumTabName.进项发票">
            <div class="main-content-body">
              <InvoiceTable
                @cell-click="handleInputCellClick"
                :data="inputInvoiceData"
                :columns="inputInvoiceColumns"
                rowKey="type"
                :loading="tableLoading"
                @operation="handleInputOperation"></InvoiceTable>
              <div class="main-content-body-footer">
                快捷入口:
                <a
                  class="link"
                  @click="handleTaxAccountClick">
                  税务数字账户
                </a>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane
            label="进项统计表"
            :name="EnumTabName.进项统计表">
            <div class="main-content-body">
              <DeductionTable
                :data="inputDeductionData"
                :loading="tableLoading"
                rowKey="type"></DeductionTable>
              <div class="statistics-explanation">
                <div class="explanation-title">说明：</div>
                <div class="explanation-item">1、以上数据源自【发票获取--进项发票】数据统计得出；</div>
                <div class="explanation-item">2、生成申报表时，附表二对应项目按此填充；</div>
                <div class="explanation-item">
                  3、系统暂不支持农产品、海关缴款书、代扣代缴完税凭证等场景的智能取数填表，请
                  <a class="link">联系客服登记您的需求</a>
                </div>
              </div>
              <div class="main-content-body-footer">
                快捷入口:
                <a
                  class="link"
                  @click="handleTaxAccountClick">
                  税务数字账户
                </a>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="header-center-info">
          <el-card>
            <span>{{ currentTaxPeriod }}月份统计确认状态:</span>
            <span :class="{ confirmed: isConfirmed, unconfirmed: !isConfirmed }">
              {{ isConfirmed ? "已确认" : "未确认" }}
            </span>
            <span>| 发票数据{{ lastUpdateTime }} 更新</span>
          </el-card>
        </div>
        <div class="header-right align-center">
          <a
            class="mr-20 button"
            @click="handleButtonJump">
            {{ jumpButtonText }}
          </a>

          <el-button
            class="button mr-20 solid-button"
            type="primary"
            :width="200"
            @click="handleGetInvoice"
            :loading="hasInvoiceTask">
            {{ hasInvoiceTask ? "正在取票中..." : "立即取票" }}
          </el-button>
          <a
            class="mr-20 button"
            :class="{ disabled: !isCanGenReport }"
            @click="() => handleGenerateDeclaration()">
            生成申报表
          </a>
          <a
            class="mr-20 align-center"
            @click="refreshDataClick">
            <el-icon size="16">
              <RefreshRight />
            </el-icon>
          </a>
        </div>
      </div>
    </div>

    <!-- 销项发票明细提示弹窗 -->
    <el-dialog
      v-model="outputInvoiceDetailDialogVisible"
      title="提示"
      width="30%"
      class="dialogDrag"
      modal-class="modal-class custom-confirm"
      :close-on-click-modal="false">
      <div
        v-dialog-drag
        class="dialog-content">
        <p>
          检测到存在
          {{ outputInvoiceDataWithoutDetail.length }}
          张没有发票明细的销项发票，不能生成到申报表中，请去【云财务-发票-销项发票】补齐信息！
        </p>
        <p>
          查看明细为空的销项发票：
          <a
            class="link"
            @click="showInvoiceNumbersDialog">
            发票号码
          </a>
        </p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <a
            class="mr-20 button"
            @click="goToCompleteDetails">
            去补齐明细
          </a>
          <a
            class="mr-20 button solid-button"
            @click="continueGenerateReport">
            继续生成表报
          </a>
        </span>
      </template>
    </el-dialog>

    <!-- 发票号码详情弹窗 -->
    <el-dialog
      v-model="invoiceNumbersDialogVisible"
      title="明细为空的发票列表"
      modal-class="modal-class custom-confirm"
      class="dialogDrag"
      width="30%">
      <div v-dialog-drag>
        <p style="margin-bottom: 10px">含有明细为空的销项发票:</p>
        <div class="invoice-list">
          <p
            v-for="(item, index) in outputInvoiceDataWithoutDetail"
            :key="index"
            style="margin: 5px 0">
            发票号码: {{ item.item1 }}, 发票代码: {{ item.item2 }}
          </p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <a
            class="mr-20 button"
            @click="invoiceNumbersDialogVisible = false">
            确定
          </a>
          <a
            class="mr-20 button solid-button"
            @click="copyInvoiceNumbers">
            复制
          </a>
        </span>
      </template>
    </el-dialog>

    <!-- 进项发票为0提示弹窗 -->
    <el-dialog
      v-model="inputInvoiceZeroDialogVisible"
      title="提示"
      width="30%"
      class="dialogDrag custom-confirm"
      modal-class="modal-class"
      :close-on-click-modal="false">
      <div
        v-dialog-drag
        class="input-invoice-message">
        <p style="color: #f56c6c; margin-bottom: 15px; font-size: 14px">
          您的进项发票数据为0，因进项发票的获取可能存在延迟，请再次核实您本月是否有已认证进项发票。
        </p>
        <p style="margin-bottom: 10px; color: #606266; font-size: 14px">若无，请点击【继续生成报表】；</p>
        <p style="color: #606266; font-size: 14px">若有，请点【一键取票】，获取最新进项发票数据。</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <a
            class="mr-20 button"
            @click="handleOneKeyFetch">
            一键取票
          </a>
          <a
            class="mr-20 button solid-button"
            @click="finalGenerateReport">
            继续生成报表
          </a>
        </span>
      </template>
    </el-dialog>

    <!-- 已有发票数据提示弹窗 -->
    <el-dialog
      v-model="existingInvoiceDialogVisible"
      title="填写已有发票数据"
      width="30%"
      class="dialogDrag custom-confirm"
      modal-class="modal-class"
      :close-on-click-modal="false">
      <div
        v-dialog-drag
        class="dialog-content">
        <p>提示: 系统检测到已存在进销项发票数据，是否需要重新获取发票或直接生成申报表?</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <a
            class="mr-20 button"
            @click="handleRefetchInvoices">
            重新取票
          </a>
          <a
            class="mr-20 button solid-button"
            @click="checkExistingInvoiceData">
            生成申报表
          </a>
        </span>
      </template>
    </el-dialog>

    <!-- 票表核对弹窗 -->
    <CheckDialog
      ref="checkDialogRef"
      @generate-report="generateReport"
      v-model="checkDialogVisible" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from "vue"
  import { dayjs } from "element-plus"
  import InvoiceTable from "./components/InvoiceTable.vue"
  import DeductionTable from "./components/DeductionTable.vue"
  import { EnumTabName } from "./constants"
  import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  import { ElNotify } from "@/utils/notify"
  import { useRoute } from "vue-router"
  import CheckDialog from "./components/CheckDialog.vue"
  import { formatAmount } from "./utils"
  import { IInputInvoiceItem, IOutputInvoiceItem } from "./types"
  import { getPurchaseList, getPurchaseSummary, getSalesList, getSummaryCheck, ICodeItem } from "@/api/invoiceAccess"
  import { IInvoiceTaskResult, useTaxBureauLogin } from "./composables/useTaxBureauLogin"
  import { usePermissonsStore } from "@/store/modules/permissons"
  import { globalWindowOpen, globalWindowOpenPage, tryClearCustomUrlParams } from "@/utils/url"
  import { TaxBureauLoginType, useTaxBureau } from "@/hooks/useTaxBureau"
  import { IResponseModel, Jrequest } from "@/utils/service"

  defineOptions({
    name: "InvoiceAccess",
  })

  // =============================================
  // 全局状态和路由
  // =============================================
  const route = useRoute()
  const activeName = ref(EnumTabName.销项发票)
  const tableLoading = ref(false)
  const requestLoading = ref(false)

  // =============================================
  // 税局相关状态和方法
  // =============================================
  const currentTaxPeriod = ref(3) // 当前税期月份，例如3月
  const isConfirmed = ref(false) // 确认状态：true = 已确认，false = 未确认
  const lastUpdateTime = ref("2025-03-10 12:22:33") // 最后更新时间

  const { currentPeriod } = storeToRefs(useTaxPeriodStore())
  const { taskCompleted, taskStartLoading, getInvoiceTaskList, invoiceTaskList, hasInvoiceTask } = useTaxBureauLogin()
  const { checkLoginState } = useTaxBureau()
  const loading = computed(() => taskStartLoading.value)

  const loadingMessage = computed(() => {
    const period = dayjs(currentPeriod.value).format("YYYY-MM")
    return `正在为您处理${period}期的发票数据，请稍候...`
  })

  // 获取发票所属期，确认状态，最新时间
  const getHeaderTipsInfo = () => {
    // 模拟API请求获取数据
    currentTaxPeriod.value = 4
    isConfirmed.value = true
    lastUpdateTime.value = "2025-04-10 12:22:33"
    return Promise.resolve()
  }

  // 获取是否可以申报状态
  const isCanGenReport = ref(true) // 是否可以生成申报表
  const getIsCanGenReport = () => {
    return getSummaryCheck().then((res) => {
      isCanGenReport.value = res.data
    })
  }

  // =============================================
  // 销项发票相关状态和方法
  // =============================================
  const outputInvoiceData = ref<any[]>([])
  const outputInvoiceDataWithoutDetail = ref<ICodeItem[]>([]) // 没有发票明细的销项发票号码
  const outputInvoiceDetailDialogVisible = ref(false) // 销项发票明细提示弹窗
  const invoiceNumbersDialogVisible = ref(false) // 发票号码详情弹窗

  // 销项发票列配置
  const outputInvoiceColumns = [
    { label: "发票类型", prop: "invoiceTypeText", minWidth: 180 },
    { label: "发票份数", prop: "invoiceCountText", minWidth: 300 },
    { label: "金额", prop: "amount", minWidth: 150, formatter: (row: any) => formatAmount(row) },
    { label: "税额", prop: "tax", minWidth: 150, formatter: (row: any) => formatAmount(row) },
    { label: "价税合计", prop: "invoiceTotal", minWidth: 150, formatter: (row: any) => formatAmount(row) },
    { label: "功能", slot: "operation", minWidth: 100 },
  ]

  // 获取销项发票数据
  const getOutputInvoiceData = () => {
    return getSalesList().then((res) => {
      outputInvoiceData.value = res.data.list
      outputInvoiceDataWithoutDetail.value = res.data.codes
    })
  }

  // 销项发票相关操作
  const handleOutputOperation = (row: IOutputInvoiceItem) => {
    jumpOutputInvoice(row.invoiceType)
  }

  const permissionsStore = usePermissonsStore()

  // 跳转到销项发票
  const jumpOutputInvoice = async (invoiceType?: number) => {
    const isHasPermission = await permissionsStore.hasPermission("invoice-output")
    if (!isHasPermission) {
      ElNotify({
        type: "info",
        message: "您没有云财务-发票-销项发票查看权限",
      })
      return
    }
    const searchStartDate = dayjs(currentPeriod.value).startOf("month").format("YYYY-MM-DD")
    const searchEndDate = dayjs(currentPeriod.value).endOf("month").format("YYYY-MM-DD")
    const url = invoiceType
      ? `${window.jAccH5Url}/Invoice/SalesInvoice?searchStartDate=${searchStartDate}&searchEndDate=${searchEndDate}&invoiceTypes=${invoiceType}&stay=true`
      : `${window.jAccH5Url}/Invoice/SalesInvoice?searchStartDate=${searchStartDate}&searchEndDate=${searchEndDate}&stay=true`

    globalWindowOpen(url)
  }

  // 显示发票号码详情弹窗
  const showInvoiceNumbersDialog = () => {
    invoiceNumbersDialogVisible.value = true
  }

  // 复制发票号码
  const copyInvoiceNumbers = () => {
    const textToCopy = outputInvoiceDataWithoutDetail.value.map((item) => `发票号码: ${item.item1}, 发票代码: ${item.item2}`).join("\n")
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        ElNotify({
          title: "提示",
          message: "发票号码已复制到剪贴板",
          type: "success",
        })
        invoiceNumbersDialogVisible.value = false
      })
      .catch(() => {
        ElNotify({
          title: "提示",
          message: "复制失败，请手动复制",
          type: "error",
        })
      })
  }

  // 去补齐明细
  const goToCompleteDetails = () => {
    outputInvoiceDetailDialogVisible.value = false
    jumpOutputInvoice()
  }

  // =============================================
  // 进项发票相关状态和方法
  // =============================================
  const inputInvoiceData = ref<any[]>([])
  const inputDeductionData = ref<any[]>([])
  const inputInvoiceZeroDialogVisible = ref(false) // 进项发票为0提示弹窗

  // 进项发票列配置
  const inputInvoiceColumns = [
    { label: "发票种类", prop: "invoiceTypeText", minWidth: 180 },
    { label: "认证发票份数", prop: "invoiceCount", minWidth: 150, className: "link" },
    { label: "认证发票金额", prop: "amount", minWidth: 150, formatter: (row: any) => formatAmount(row) },
    { label: "认证发票税额", prop: "tax", minWidth: 150, formatter: (row: any) => formatAmount(row) },
    { label: "功能", slot: "operation", minWidth: 100 },
  ]

  // 获取进项发票数据
  const getInputInvoiceData = () => {
    return getPurchaseList().then((res) => {
      inputInvoiceData.value = res.data
    })
  }

  // 获取进项统计表数据
  const getInputDeductionData = () => {
    return getPurchaseSummary().then((res) => {
      inputDeductionData.value = res.data
    })
  }

  // 进项发票相关操作
  const handleInputOperation = (row: IInputInvoiceItem) => {
    jumpInputInvoice(row.invoiceType)
  }

  // 点击进项发票数目列跳转
  const handleInputCellClick = (row: IInputInvoiceItem, column: any) => {
    if (column.property === "invoiceCount" && row.invoiceTypeText !== "合计") {
      jumpInputInvoice(row.invoiceType)
    }
  }

  // 跳转到进项发票
  const jumpInputInvoice = async (invoiceType?: number) => {
    const isHasPermission = await permissionsStore.hasPermission("invoice-input")
    if (!isHasPermission) {
      ElNotify({
        type: "info",
        message: "您没有云财务-发票-销项发票查看权限",
      })
      return
    }
    const searchStartDate = dayjs(currentPeriod.value).startOf("month").format("YYYY-MM-DD")
    const searchEndDate = dayjs(currentPeriod.value).endOf("month").format("YYYY-MM-DD")
    const url = invoiceType
      ? `${window.jAccH5Url}/Invoice/PurchaseInvoice?searchStartDate=${searchStartDate}&searchEndDate=${searchEndDate}&invoiceTypes=${invoiceType}&stay=true`
      : `${window.jAccH5Url}/Invoice/PurchaseInvoice?searchStartDate=${searchStartDate}&searchEndDate=${searchEndDate}&stay=true`
    globalWindowOpen(url)
  }

  // =============================================
  // 取票和刷新数据相关
  // =============================================
  const existingInvoiceDialogVisible = ref(false) // 已有发票数据提示弹窗

  // 一键取票逻辑
  const handleGetInvoice = () => {
    preCheckStatus()
  }

  let preCheck = false // 预检查状态
  const preCheckStatus = () => {
    if (preCheck) return

    preCheck = true
    return Jrequest({ url: "/api/TaxBureau/PreCheck", method: "get" })
      .then((res: any) => {
        if (res.state === 1000) {
          // 弹窗
          checkLoginState(105, 1, "", getInvoiceTaskList)
          return
        } else if (res.state === 2000) {
          if (res.data.result === "running" && hasInvoiceTask.value) {
            // 弹窗
            checkLoginState(105, 1, "", getInvoiceTaskList)
            return
          }
        }
        ElNotify({ type: "warning", message: res.msg })
        return
      })
      .catch((err) => {
        console.log(err)
        ElNotify({ type: "warning", message: "取票预检查发生错误，请稍后重试。" })
      })
      .finally(() => {
        preCheck = false
      })
  }

  // 取票完成后刷新数据
  watch(taskCompleted, (newVal) => {
    if (newVal) {
      refreshData()
    }
  })

  // 刷新所有数据
  const refreshData = () => {
    return Promise.all([getOutputInvoiceData(), getInputInvoiceData(), getInputDeductionData(), getHeaderTipsInfo(), getIsCanGenReport()])
  }

  // 刷新按钮点击事件
  const refreshDataClick = () => {
    tableLoading.value = true
    refreshData().finally(() => {
      tableLoading.value = false
    })
  }

  // 处理重新取票
  const handleRefetchInvoices = () => {
    existingInvoiceDialogVisible.value = false
    handleGetInvoice()
  }

  // 一键取票（从进项为0弹窗）
  const handleOneKeyFetch = () => {
    inputInvoiceZeroDialogVisible.value = false
    handleGetInvoice()
  }

  // =============================================
  // 跳转和导航相关
  // =============================================
  const jumpButtonText = computed(() => {
    return activeName.value === EnumTabName.销项发票 ? "记账销项" : "记账进项"
  })

  const handleButtonJump = () => {
    activeName.value === EnumTabName.销项发票 ? jumpOutputInvoice() : jumpInputInvoice()
  }

  // =============================================
  // 生成申报表流程相关
  // =============================================
  const checkDialogVisible = ref(false) // 票表核对弹窗

  // 开始生成申报表流程
  const handleGenerateDeclaration = () => {
    if (outputInvoiceData.value.length > 0 || inputInvoiceData.value.length > 0) {
      existingInvoiceDialogVisible.value = true
    } else {
      handleGetInvoice()
    }
  }

  // 检查销项发票数据明细问题
  const checkExistingInvoiceData = () => {
    existingInvoiceDialogVisible.value = false
    if (outputInvoiceData.value.length > 0 && outputInvoiceDataWithoutDetail.value.length > 0) {
      outputInvoiceDetailDialogVisible.value = true
    } else {
      checkInputInvoice()
    }
  }

  // 继续生成表报（从销项发票弹窗）
  const continueGenerateReport = () => {
    outputInvoiceDetailDialogVisible.value = false
    checkInputInvoice()
  }

  // 检查进项发票数据
  const checkInputInvoice = () => {
    if (inputInvoiceData.value.length === 0) {
      inputInvoiceZeroDialogVisible.value = true
    } else {
      finalGenerateReport()
    }
  }

  // 最终生成报表
  const finalGenerateReport = () => {
    inputInvoiceZeroDialogVisible.value = false
    checkDialogVisible.value = true
  }

  // 票表核对后生成报表
  const generateReport = () => {
    checkDialogVisible.value = false
    // 跳转到税种申报页面
    globalWindowOpenPage("/TaxDeclaration?declareTypeName=增值税及附加税费申报（一般纳税人适用）", "税种申报")
  }

  // =============================================
  // 生命周期钩子
  // =============================================

  // 组件挂载时加载数据
  onMounted(() => {
    refreshData()
    // 检查一下
    getInvoiceTaskList(true)
    // 首次加载检查一下任务状态
  })

  // 从申报页面跳转过来时自动触发申报流程

  onActivated(async () => {
    if (route.query.declare) {
      await refreshData() // 首次进入页面的时候是需要进行请求的
      handleGenerateDeclaration()
      tryClearCustomUrlParams(route)
    }
  })

  const handleTaxAccountClick = () => {
    checkLoginState(103, TaxBureauLoginType.Jump, "szc/szzh/sjswszzh/spHandler?cdlj=/szzh/szzh/")
  }
</script>
<style scoped lang="scss">
  .content {
    .main-content {
      .tabs-header {
        position: relative;
        height: 100%;

        :deep(.el-tabs__header) {
          margin-bottom: 0;
        }

        .header-center-info {
          position: absolute;
          left: 30%;
          top: 8px;
          font-size: var(--h5);
          :deep(.el-card__body) {
            line-height: 18px;
            padding: 6px 7px;
            color: var(--grey);
          }
          .confirmed {
            color: #67c23a; // 绿色，表示已确认
            font-weight: bold;
          }

          .unconfirmed {
            color: #f56c6c; // 红色，表示未确认
            font-weight: bold;
          }
        }
        .header-right {
          position: absolute;
          right: 10px;
          top: 8px;
        }
      }
      .statistics-explanation {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f8f8;
        border-radius: 4px;

        .explanation-title {
          font-weight: bold;
          margin-bottom: 10px;
        }

        .explanation-item {
          margin-bottom: 5px;
          font-size: var(--h5);
          line-height: 1.5;
          color: #666;

          .link {
            color: #409eff;
            cursor: pointer;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          .blue-tip {
            color: #999;
            font-size: 12px;
            font-style: italic;
          }
        }
      }
      .main-content-body-footer {
        height: 30px;
        margin-top: 20px;
        font-size: 14px;
        color: #999;
      }
    }
  }
  .mr-20 {
    margin-right: 20px;
  }

  .dialog-content {
    text-align: left;
    line-height: 1.6;

    p {
      margin-bottom: 10px;

      &:first-child {
        color: #303133;
        font-weight: 500;
      }
    }
  }

  :deep(.invoice-list) {
    max-height: 300px;
    overflow-y: auto;
    text-align: left;
    line-height: 1.6;
  }

  :deep(.input-invoice-message) {
    text-align: left;
    line-height: 1.6;
  }

  :deep(.el-dialog__body) {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .link {
    color: #409eff;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .disabled-button {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
</style>
