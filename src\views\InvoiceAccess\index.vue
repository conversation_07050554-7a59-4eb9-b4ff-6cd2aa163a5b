<template>
  <div
    class="content"
    v-mask="{ visible: taskStartLoading, message: loadingMessage, global: true }">
    <div
      class="main-content"
      v-mask="{ visible: requestLoading, message: '加载数据中....', global: true }">
      <div class="tabs-header">
        <el-tabs
          v-model="activeName"
          class="demo-tabs">
          <el-tab-pane
            label="销项发票"
            :name="EnumTabName.销项发票">
            <div class="main-content-body">
              <InvoiceTable
                class="invoice-table"
                :data="outputInvoiceData"
                :columns="outputInvoiceColumns"
                :loading="tableLoading"
                rowKey="type"
                @operation="handleOutputOperation"></InvoiceTable>
              <div class="main-content-body-footer">
                快捷入口:
                <a
                  class="link"
                  @click="handleTaxAccountClick">
                  税务数字账户
                </a>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane
            label="进项发票"
            v-if="!isSmallTaxLevel"
            :name="EnumTabName.进项发票">
            <div class="main-content-body">
              <InvoiceTable
                class="invoice-table"
                @cell-click="handleInputCellClick"
                :data="inputInvoiceData"
                :columns="inputInvoiceColumns"
                rowKey="type"
                :loading="tableLoading"
                @operation="handleInputOperation"></InvoiceTable>
              <div class="main-content-body-footer">
                快捷入口:
                <a
                  class="link"
                  @click="handleTaxAccountClick">
                  税务数字账户
                </a>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane
            label="进项统计表"
            v-if="!isSmallTaxLevel"
            :name="EnumTabName.进项统计表">
            <div class="main-content-body">
              <DeductionTable
                :data="inputDeductionData"
                :loading="tableLoading"
                rowKey="type"></DeductionTable>
              <div class="statistics-explanation">
                <div class="explanation-title">说明：</div>
                <div class="explanation-item">1、以上数据源自【发票获取--进项发票】数据统计得出；</div>
                <div class="explanation-item">2、生成申报表时，附表二对应项目按此填充；</div>
                <div class="explanation-item">
                  3、系统暂不支持农产品、海关缴款书、代扣代缴完税凭证等场景的智能取数填表，请
                  <a class="link">联系客服登记您的需求</a>
                </div>
              </div>
              <div class="main-content-body-footer">
                快捷入口:
                <a
                  class="link"
                  @click="handleTaxAccountClick">
                  税务数字账户
                </a>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div
          class="header-center-info"
          :class="isSmallTaxLevel ? 'small-taxpayer' : ''">
          <el-card v-if="!isSmallTaxLevel">
            <span>{{ currentTaxPeriod }}月份统计确认状态:</span>
            <span :class="{ confirmed: isConfirmed, unconfirmed: !isConfirmed }">
              {{ isConfirmed ? "已确认" : "未确认" }}
            </span>
            <span>| 发票数据 {{ FetchInvoicelatestTime }} 更新</span>
          </el-card>
          <el-card v-else-if="shouldShowSmallTaxpayerTip">
            <el-icon color="var(--orange)"><WarningFilled /></el-icon>
            温馨提示：1、当期税款所属期的申报时间在{{ declareDate.endDate }}截止，还有{{
              remainingDeclareDays
            }}天，请及时进行申报！2、一键取票取不到的发票种类，可以点击【操作】去新增。
          </el-card>
        </div>
        <div class="header-right align-center">
          <a
            class="mr-20 button"
            @click="handleButtonJump">
            {{ jumpButtonText }}
          </a>
          <el-popover width="164">
            <template #reference>
              <el-button
                class="button mr-20 solid-button"
                style="width: 100px"
                type="primary"
                @click="handleGetInvoice"
                :loading="hasInvoiceTask">
                {{ hasInvoiceTask ? "正在取票中..." : "立即取票" }}
              </el-button>
            </template>
            <div class="popover-content">
              <span class="time">{{ FetchInvoicelatestTime }} 更新</span>
            </div>
          </el-popover>

          <a
            class="mr-20 button"
            :class="{ disabled: !isCanGenReport }"
            @click="() => handleGenerateDeclaration()">
            生成申报表
          </a>
          <a
            class="mr-20 align-center"
            @click="refreshDataClick">
            <el-icon size="16">
              <RefreshRight />
            </el-icon>
          </a>
        </div>
      </div>
    </div>

    <!-- 销项发票明细提示弹窗 -->
    <Confirm
      v-model:visible="outputInvoiceDetailDialogVisible"
      title="提示"
      :onConfirm="continueGenerateReport"
      :onCancel="goToCompleteDetails"
      :options="{
        buttons: {
          confirm: '继续生成表报',
          cancel: '去补齐明细',
        },
        showCancel: true,
      }">
      <div class="dialog-content">
        <p>
          检测到存在
          {{ outputInvoiceDataWithoutDetail.length }}
          张没有发票明细的销项发票，不能生成到申报表中，请去【云财务-发票-销项发票】补齐信息！
        </p>
        <p>
          查看明细为空的销项发票：
          <a
            class="link"
            @click="showInvoiceNumbersDialog">
            发票号码
          </a>
        </p>
      </div>
    </Confirm>

    <!-- 发票号码详情弹窗 -->
    <Confirm
      v-model="invoiceNumbersDialogVisible"
      title="明细为空的发票列表"
      :onConfirm="() => (invoiceNumbersDialogVisible = false)"
      :onCancel="copyInvoiceNumbers"
      :options="{
        showCancel: true,
        buttons: { confirm: '确定', cancel: '复制' },
      }">
      <div>
        <p style="margin-bottom: 10px">含有明细为空的销项发票:</p>
        <div class="invoice-list">
          <p
            v-for="(item, index) in outputInvoiceDataWithoutDetail"
            :key="index"
            style="margin: 5px 0">
            发票号码: {{ item.item1 }}, 发票代码: {{ item.item2 }}
          </p>
        </div>
      </div>
    </Confirm>

    <!-- 进项发票为0提示弹窗 -->

    <Confirm
      v-model:visible="inputInvoiceZeroDialogVisible"
      title="提示"
      :onConfirm="handleOneKeyFetch"
      :onCancel="finalGenerateReport"
      :options="{
        showCancel: true,
        buttons: { confirm: '继续生成报表', cancel: '一键取票' },
      }">
      <div class="input-invoice-message">
        <p style="color: #f56c6c; margin-bottom: 15px; font-size: 14px">
          您的进项发票数据为0，因进项发票的获取可能存在延迟，请再次核实您本月是否有已认证进项发票。
        </p>
        <p style="margin-bottom: 10px; color: #606266; font-size: 14px">若无，请点击【继续生成报表】；</p>
        <p style="color: #606266; font-size: 14px">若有，请点【一键取票】，获取最新进项发票数据。</p>
      </div>
    </Confirm>

    <Confirm
      v-model:visible="existingInvoiceDialogVisible"
      title="填写已有发票数据"
      :onConfirm="checkExistingInvoiceData"
      :onCancel="handleRefetchInvoices"
      :options="{
        showCancel: true,
        buttons: { confirm: '生成申报表', cancel: '重新取票' },
      }">
      <div class="dialog-content">
        <p>提示: 系统检测到已存在进销项发票数据，是否需要重新获取发票或直接生成申报表?</p>
      </div>
    </Confirm>

    <!-- 票表核对弹窗 -->
    <CheckDialog
      ref="checkDialogRef"
      @generate-report="generateReport"
      v-model="checkDialogVisible" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from "vue"
  import { dayjs } from "element-plus"
  import InvoiceTable from "./components/InvoiceTable.vue"
  import DeductionTable from "./components/DeductionTable.vue"
  import { EnumTabName, EnumTaxpayerLevel, smallTaxpayerDeclareMonths } from "./constants"
  import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  import { ElNotify } from "@/utils/notify"
  import { useRoute } from "vue-router"
  import CheckDialog from "./components/CheckDialog.vue"
  import { formatValue } from "./utils"
  import { IInputInvoiceItem, IOutputInvoiceItem } from "./types"
  import {
    getInvoiceSelectionPeriod,
    getInvoiceSelectionStatus,
    getPurchaseList,
    getPurchaseSummary,
    getSalesList,
    getSummaryCheck,
    ICodeItem,
  } from "@/api/invoiceAccess"
  import { useTaxBureauLogin } from "../../hooks/useFetchInvoice"
  import { usePermissonsStore } from "@/store/modules/permissons"
  import { globalWindowOpen, globalWindowOpenPage, tryClearCustomUrlParams } from "@/utils/url"
  import { TaxBureauLoginType, useTaxBureau } from "@/hooks/useTaxBureau"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { getAddTaxMonthDeclarationDeadline } from "@/api/taxDeclaration"
  import quarterOfYear from "dayjs/plugin/quarterOfYear"

  defineOptions({
    name: "InvoiceAccess",
  })

  // =============================================
  // 全局状态和路由
  // =============================================
  const route = useRoute()
  const activeName = ref(EnumTabName.销项发票)
  const tableLoading = ref(false)
  const requestLoading = ref(false)

  const isSmallTaxLevel = computed(() => {
    return useBasicInfoStore().basicInfo.taxType === EnumTaxpayerLevel.Small
  })

  // 申报期限
  const declareDate = reactive({
    startDate: "",
    endDate: "",
  })

  dayjs.extend(quarterOfYear)
  // 判断当前税款所属期是否为应申报的期间（当前时间比税款所属期晚1个季度）
  const isCurrentPeriodInCurrentQuarter = computed(() => {
    console.log(dayjs())
    console.log(dayjs().diff(dayjs(currentPeriod.value), "quarter"), "dayjs().diff(dayjs(currentPeriod.value), 'quarter')")
    return dayjs().diff(dayjs(currentPeriod.value), "quarter") === 1
  })

  // 判断是否在申报期限内
  const isInDeclarePeriod = computed(() => {
    const currentDate = dayjs()
    const startDate = dayjs(declareDate.startDate)
    const endDate = dayjs(declareDate.endDate)

    // 使用 isAfter 和 isBefore 来判断是否在期间内
    return (
      (currentDate.isAfter(startDate) || currentDate.isSame(startDate, "day")) &&
      (currentDate.isBefore(endDate) || currentDate.isSame(endDate, "day"))
    )
  })

  // 是否显示小规模纳税人提示（当前为申报月份且选择的税款所属期为应申报期间且在申报期限内时显示）
  const shouldShowSmallTaxpayerTip = computed(() => {
    return isSmallTaxLevel.value && isCurrentPeriodInCurrentQuarter.value && isInDeclarePeriod.value
  })

  // 计算剩余申报天数
  const remainingDeclareDays = computed(() => {
    const currentDate = dayjs()
    const endDate = dayjs(declareDate.endDate)

    if (currentDate.isAfter(endDate)) {
      return 0 // 已过期
    }

    return endDate.diff(currentDate, "day") + 1 // 包含当天
  })

  // =============================================
  // 税局相关状态和方法
  // =============================================
  const currentTaxPeriod = ref("0") // 当前勾选税期月份，例如3月
  const isConfirmed = ref(false) // 确认状态：true = 已确认，false = 未确认

  const { currentPeriod, searchStartDate, searchEndDate } = storeToRefs(useTaxPeriodStore())
  const { taskCompleted, taskStartLoading, getInvoiceTaskList, hasInvoiceTask, invoiceTaskList, preCheckStatus } = useTaxBureauLogin()
  const { checkLoginState } = useTaxBureau()

  const loadingMessage = computed(() => {
    if (isSmallTaxLevel.value) {
      return `正在为您获取第${searchStartDate.value}至${searchEndDate.value}的发票数据，请稍候...`
    } else {
      const period = dayjs(currentPeriod.value).format("YYYY-MM")
      return `正在为您获取${period}期的发票数据，请稍候...`
    }
  })

  // 获取发票所属期，确认状态
  const getHeaderTipsInfo = () => {
    // 模拟API请求获取数据
    if (isSmallTaxLevel.value) {
      const currentMonth = dayjs().month() + 1
      // 是否是当期
      if (smallTaxpayerDeclareMonths.includes(currentMonth)) {
        return getAddTaxMonthDeclarationDeadline().then((res) => {
          if (res.state === 1000) {
            declareDate.startDate = res.data.startDate
            declareDate.endDate = res.data.endDate
          }
        })
      }
    } else {
      return getInvoiceSelectionPeriod().then((res) => {
        if (res.state === 1000) {
          currentTaxPeriod.value = res.data.substring(4, 6)
        }
        getInvoiceSelectionStatus({ invoicePeriodId: res.data }).then((res) => {
          isConfirmed.value = res.data == 1
        })
      })
    }
  }

  const FetchInvoicelatestTime = computed(() => {
    if (!invoiceTaskList.value) return null
    for (let i = 0; i < invoiceTaskList.value.length; i++) {
      if (invoiceTaskList.value[i].invoiceTaskStatus !== 1)
        return dayjs(invoiceTaskList.value[i]?.modifiedDate).format("YYYY-MM-DD HH:mm:ss")
    }
    return null
  })

  // 1、4、7、10月，首次进入【发票获取】自动获取发票数据，只取最新税款所属期的发票。
  watch(
    () => FetchInvoicelatestTime.value,
    (newVal) => {
      if (newVal) {
        const quarterEnds = [1, 4, 7, 10]
        const currentMonth = dayjs().month() + 1
        const HasFetchInvoice = dayjs(newVal).month() + 1 == currentMonth && dayjs(newVal).year() == dayjs().year()
        if (quarterEnds.includes(currentMonth) && !HasFetchInvoice && isSmallTaxLevel.value) {
          // 自动获取发票数据
          handleGetInvoice()
        }
      }
    },
  )

  // 获取是否可以申报状态
  const isCanGenReport = ref(true) // 是否可以生成申报表
  const getIsCanGenReport = () => {
    return getSummaryCheck().then((res) => {
      isCanGenReport.value = res.data
    })
  }

  // =============================================
  // 销项发票相关状态和方法
  // =============================================
  const outputInvoiceData = ref<IOutputInvoiceItem[]>([])
  const outputInvoiceDataWithoutDetail = ref<ICodeItem[]>([]) // 没有发票明细的销项发票号码
  const outputInvoiceDetailDialogVisible = ref(false) // 销项发票明细提示弹窗
  const invoiceNumbersDialogVisible = ref(false) // 发票号码详情弹窗

  // 销项发票列配置
  const outputInvoiceColumns = [
    { label: "发票类型", prop: "invoiceTypeText", minWidth: 180 },
    { label: "发票份数", prop: "invoiceCountText", minWidth: 300 },
    { label: "金额", prop: "amount", minWidth: 150, formatter: (row: IOutputInvoiceItem) => formatValue(row.amount) },
    { label: "税额", prop: "tax", minWidth: 150, formatter: (row: IOutputInvoiceItem) => formatValue(row.tax) },
    { label: "价税合计", prop: "invoiceTotal", minWidth: 150, formatter: (row: IOutputInvoiceItem) => formatValue(row.invoiceTotal) },
    { label: "功能", slot: "operation", minWidth: 100 },
  ]

  // 获取销项发票数据
  const getOutputInvoiceData = () => {
    return getSalesList().then((res) => {
      outputInvoiceData.value = res.data.list
      outputInvoiceDataWithoutDetail.value = res.data.codes
    })
  }

  // 销项发票相关操作
  const handleOutputOperation = (row: IOutputInvoiceItem) => {
    jumpOutputInvoice(row.invoiceType)
  }

  const permissionsStore = usePermissonsStore()

  // 跳转到销项发票
  const jumpOutputInvoice = async (invoiceType?: number) => {
    const isHasPermission = await permissionsStore.hasPermission("invoice-output")
    if (!isHasPermission) {
      ElNotify({
        type: "info",
        message: "您没有云财务-发票-销项发票查看权限",
      })
      return
    }

    const url = invoiceType
      ? `${window.jAccH5Url}/Invoice/SalesInvoice?searchStartDate=${searchStartDate.value}&searchEndDate=${searchEndDate.value}&invoiceTypes=${invoiceType}&stay=true`
      : `${window.jAccH5Url}/Invoice/SalesInvoice?searchStartDate=${searchStartDate.value}&searchEndDate=${searchEndDate.value}&stay=true`

    globalWindowOpen(url)
  }

  // 显示发票号码详情弹窗
  const showInvoiceNumbersDialog = () => {
    invoiceNumbersDialogVisible.value = true
  }

  // 复制发票号码
  const copyInvoiceNumbers = () => {
    const textToCopy = outputInvoiceDataWithoutDetail.value.map((item) => `发票号码: ${item.item1}, 发票代码: ${item.item2}`).join("\n")
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        ElNotify({
          title: "提示",
          message: "发票号码已复制到剪贴板",
          type: "success",
        })
        invoiceNumbersDialogVisible.value = false
      })
      .catch(() => {
        ElNotify({
          title: "提示",
          message: "复制失败，请手动复制",
          type: "error",
        })
      })
  }

  // 去补齐明细
  const goToCompleteDetails = () => {
    outputInvoiceDetailDialogVisible.value = false
    jumpOutputInvoice()
  }

  // =============================================
  // 进项发票相关状态和方法
  // =============================================
  const inputInvoiceData = ref<any[]>([])
  const inputDeductionData = ref<any[]>([])
  const inputInvoiceZeroDialogVisible = ref(false) // 进项发票为0提示弹窗

  // 进项发票列配置
  const inputInvoiceColumns = [
    { label: "发票种类", prop: "invoiceTypeText", minWidth: 180 },
    { label: "认证发票份数", prop: "invoiceCount", minWidth: 150, className: "link" },
    { label: "认证发票金额", prop: "amount", minWidth: 150, formatter: (row: IInputInvoiceItem) => formatValue(row.amount) },
    { label: "认证发票税额", prop: "tax", minWidth: 150, formatter: (row: IInputInvoiceItem) => formatValue(row.tax) },
    { label: "功能", slot: "operation", minWidth: 100 },
  ]

  // 获取进项发票数据
  const getInputInvoiceData = () => {
    return getPurchaseList().then((res) => {
      inputInvoiceData.value = res.data
    })
  }

  // 获取进项统计表数据
  const getInputDeductionData = () => {
    return getPurchaseSummary().then((res) => {
      inputDeductionData.value = res.data
    })
  }

  // 进项发票相关操作
  const handleInputOperation = (row: IInputInvoiceItem) => {
    jumpInputInvoice(row.invoiceType)
  }

  // 点击进项发票数目列跳转
  const handleInputCellClick = (row: IInputInvoiceItem, column: any) => {
    if (column.property === "invoiceCount" && row.invoiceTypeText !== "合计") {
      jumpInputInvoice(row.invoiceType)
    }
  }

  // 跳转到进项发票
  const jumpInputInvoice = async (invoiceType?: number) => {
    const isHasPermission = await permissionsStore.hasPermission("invoice-input")
    if (!isHasPermission) {
      ElNotify({
        type: "info",
        message: "您没有云财务-发票-销项发票查看权限",
      })
      return
    }
    const url = invoiceType
      ? `${window.jAccH5Url}/Invoice/PurchaseInvoice?searchStartDate=${searchStartDate.value}&searchEndDate=${searchEndDate.value}&invoiceTypes=${invoiceType}&stay=true`
      : `${window.jAccH5Url}/Invoice/PurchaseInvoice?searchStartDate=${searchStartDate.value}&searchEndDate=${searchEndDate.value}&stay=true`
    globalWindowOpen(url)
  }

  // =============================================
  // 取票和刷新数据相关
  // =============================================
  const existingInvoiceDialogVisible = ref(false) // 已有发票数据提示弹窗

  // 一键取票逻辑
  const handleGetInvoice = () => {
    preCheckStatus()
  }

  // 取票完成后刷新数据
  watch(taskCompleted, (newVal) => {
    if (newVal) {
      refreshData()
    }
  })

  // 刷新所有数据
  const refreshData = () => {
    return Promise.all([getOutputInvoiceData(), getInputInvoiceData(), getInputDeductionData(), getHeaderTipsInfo(), getIsCanGenReport()])
  }

  // 刷新按钮点击事件
  const refreshDataClick = () => {
    tableLoading.value = true
    refreshData().finally(() => {
      tableLoading.value = false
    })
  }

  // 处理重新取票
  const handleRefetchInvoices = () => {
    existingInvoiceDialogVisible.value = false
    handleGetInvoice()
  }

  // 一键取票（从进项为0弹窗）
  const handleOneKeyFetch = () => {
    inputInvoiceZeroDialogVisible.value = false
    handleGetInvoice()
  }

  // =============================================
  // 跳转和导航相关
  // =============================================
  const jumpButtonText = computed(() => {
    return activeName.value === EnumTabName.销项发票 ? "记账销项" : "记账进项"
  })

  const handleButtonJump = () => {
    activeName.value === EnumTabName.销项发票 ? jumpOutputInvoice() : jumpInputInvoice()
  }

  // =============================================
  // 生成申报表流程相关
  // =============================================
  const checkDialogVisible = ref(false) // 票表核对弹窗

  // 开始生成申报表流程
  const handleGenerateDeclaration = () => {
    if (outputInvoiceData.value.length > 0 || inputInvoiceData.value.length > 0) {
      existingInvoiceDialogVisible.value = true
    } else {
      handleGetInvoice()
    }
  }

  // 检查销项发票数据明细问题
  const checkExistingInvoiceData = () => {
    existingInvoiceDialogVisible.value = false
    if (outputInvoiceData.value.length > 0 && outputInvoiceDataWithoutDetail.value.length > 0) {
      outputInvoiceDetailDialogVisible.value = true
    } else {
      checkInputInvoice()
    }
  }

  // 继续生成表报（从销项发票弹窗）
  const continueGenerateReport = () => {
    outputInvoiceDetailDialogVisible.value = false
    if (isSmallTaxLevel.value) {
      generateReport()
    } else {
      checkInputInvoice()
    }
  }

  // 检查进项发票数据
  const checkInputInvoice = () => {
    if (inputInvoiceData.value.length === 0) {
      inputInvoiceZeroDialogVisible.value = true
    } else {
      finalGenerateReport()
    }
  }

  // 最终生成报表
  const finalGenerateReport = () => {
    inputInvoiceZeroDialogVisible.value = false
    checkDialogVisible.value = true
  }

  // 票表核对后生成报表
  const generateReport = () => {
    checkDialogVisible.value = false
    // 跳转到税种申报页面
    const declareTypeName = isSmallTaxLevel.value ? "增值税及附加税费申报（小规模纳税人适用）" : "增值税及附加税费申报（一般纳税人适用）"
    globalWindowOpenPage(`/TaxDeclaration?declareTypeName=${declareTypeName}`, "税种申报")
  }

  // =============================================
  // 生命周期钩子
  // =============================================

  // 组件挂载时加载数据
  onMounted(() => {
    refreshData()
    // 检查一下
    getInvoiceTaskList(true)
    // 首次加载检查一下任务状态
  })

  // 从申报页面跳转过来时自动触发申报流程

  onActivated(async () => {
    if (route.query.declare) {
      await refreshData() // 首次进入页面的时候是需要进行请求的
      handleGenerateDeclaration()
      tryClearCustomUrlParams(route)
    }
  })

  const handleTaxAccountClick = () => {
    checkLoginState(103, TaxBureauLoginType.Jump, "szc/szzh/sjswszzh/spHandler?cdlj=/szzh/szzh/")
  }
</script>
<style scoped lang="scss">
  .content {
    .main-content {
      .tabs-header {
        position: relative;
        height: 100%;

        :deep(.el-tabs__header) {
          margin-bottom: 0;
        }

        .header-center-info {
          position: absolute;
          left: 30%;
          top: 8px;
          font-size: var(--h5);
          :deep(.el-card__body) {
            line-height: 18px;
            padding: 6px 7px;
            color: var(--grey);
          }
          .confirmed {
            color: #67c23a; // 绿色，表示已确认
            font-weight: bold;
          }

          .unconfirmed {
            color: #f56c6c; // 红色，表示未确认
            font-weight: bold;
          }
        }
        .small-taxpayer {
          left: 20%;
          top: 1px;
          :deep(.el-card__body) {
            line-height: 16px;
            max-width: 666px;
          }
        }
        .header-right {
          position: absolute;
          right: 10px;
          top: 8px;
        }
      }
      .statistics-explanation {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f8f8;
        border-radius: 4px;

        .explanation-title {
          font-weight: bold;
          margin-bottom: 10px;
        }

        .explanation-item {
          margin-bottom: 5px;
          font-size: var(--h5);
          line-height: 1.5;
          color: #666;

          .link {
            color: #409eff;
            cursor: pointer;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          .blue-tip {
            color: #999;
            font-size: 12px;
            font-style: italic;
          }
        }
      }

      .main-content-body {
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: auto;
        .main-content-body-footer {
          height: 30px;
          margin-top: 20px;
          font-size: 14px;
          color: #999;
        }
        .invoice-table {
          flex: 1;
          overflow: auto;
        }
      }
    }
  }

  .dialog-content {
    text-align: left;
    line-height: 1.6;

    p {
      margin-bottom: 10px;

      &:first-child {
        color: #303133;
        font-weight: 500;
      }
    }
  }

  :deep(.invoice-list) {
    max-height: 300px;
    overflow-y: auto;
    text-align: left;
    line-height: 1.6;
  }

  :deep(.input-invoice-message) {
    text-align: left;
    line-height: 1.6;
  }

  :deep(.el-dialog__body) {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .link {
    color: #409eff;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .disabled-button {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  .popover-content {
    font-size: var(--h5);
  }
</style>
