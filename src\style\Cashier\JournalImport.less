@import "../Functions.less";
ul,
li {
    list-style: none;
    padding: 0;
    margin: 0;
}
// 一键导入
.importShow-content {
    .importShow-main {
        div {
            height: 18px;
            font-size: var(--font-size);
            color: #404040;
            margin-left: 40px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
// 全部账户导入
.importTipShow-content {
    .buttons {
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: space-evenly;
        align-items: center;
    }
    &.erp {
        .buttons {
            display: flex !important;
            justify-content: flex-end;
        }
    }
    .importTipShow-main {
        margin-top: 30px;
        margin-left: 40px;
        height: 124px;
        display: flex;
        align-items: center;
        .tip-select {
            margin-left: 15px;
            .detail-el-select(186px, 32px);
        }
    }
}
// 银行对账单(Excel)
.importBankShow-content {
    .importBankShow-main {
        padding-right: 40px;
        box-sizing: border-box;
        div {
            font-size: var(--font-size);
            color: #404040;
            margin-left: 40px;
        }
    }
}
// 银行对账单导入结果
.import-result-dialog {
    .import-result {
        padding: 0 20px;
        .import-result-content {
            .qrcode {
                display: block;
                height: 205px;
                margin: 0 auto;
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
// 支付宝导入
.importAlipayShow-content {
    ul.importAlipayShow-top {
        height: 30px;
        margin-top: 20px;
        display: flex;
        li {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            position: relative;
            span {
                color: #999999;
                font-size: 14px;
                line-height: 22px;
            }
            span.active {
                color: #147bff;
                font-size: 16px;
                font-weight: 600;
            }
            span.line {
                width: 67px;
                height: 3px;
                position: absolute;
                bottom: 0;
                left: 50%;
                background-color: transparent;
                transform: scaleY(0.5) translateX(-50%);
                &.active {
                    background-color: #1677fe;
                }
            }
        }
    }
    .importAlipayShow-main {
        display: flex;
        align-items: center;
        margin-top: 20px;
        > div {
            display: inline-block;
            &.left {
                margin-left: 33px;
            }
            &.right {
                margin-left: 22px;
                width: 233px;
                line-height: 25px;
                font-size: 14px;
                vertical-align: middle;
                .import-line {
                    line-height: 20px;
                    display: flex;
                    align-items: center;
                    & + .import-line {
                        margin-top: 10px;
                    }
                    > a {
                        color: var(--link-color);
                    }
                }
            }
        }
    }
    .buttons {
        border-top: none;
    }
}
// 微信导入
.importWechatShow-content {
    .importWechatShow-main {
        & > div {
            display: flex;
            align-items: center;
            &.switch-tabs {
                height: var(--tab-height);
                list-style: none;
                margin: 0;
                padding: 0;
                background-color: #fff;
                margin-top: 20px;
                margin-bottom: 20px;
                text-align: center;
                & > div {
                    width: 219px;
                    color: #999999;
                    font-size: 14px;
                    background: none;
                    line-height: 22px;
                    width: 219px;
                    background: none;
                    &.active {
                        color: #6ed773;
                        font-size: 14px;
                        font-weight: 600;
                        background: none;
                        .tab-line {
                            visibility: initial;
                        }
                    }
                    .tab-line {
                        width: 67px;
                        height: 2px;
                        background: #6ed773;
                        visibility: hidden;
                        margin-left: 75px;
                        display: block;
                        margin-top: 6px;
                    }
                }
            }
            & > div {
                display: inline-block;
                &.left {
                    margin-left: 33px;
                }
                &.right {
                    margin-left: 22px;
                    width: 233px;
                    font-size: 14px;
                    vertical-align: middle;
                    .line {
                        line-height: 20px;
                        height: 20px;
                        margin-top: 10px;
                        background-color: transparent;
                        &:first-child {
                            margin-top: 0;
                        }
                        a {
                            color: #6ed773;
                        }
                        &:last-child {
                            display: flex;
                            align-items: center;
                        }
                    }
                }
            }
        }
    }
    .buttons {
        border-top: none;
    }
    &.erp {
        .importWechatShow-main {
            & > div {
                &.switch-tabs {
                    & > div {
                        &.active {
                            color: #297beb;
                        }
                        .tab-line {
                            background: #297beb;
                        }
                    }
                }
                & > div {
                    &.right {
                        .line {
                            a {
                                color: #297beb;
                            }
                        }
                    }
                }
            }
        }
        .buttons {
            padding-right: 20px;
            text-align: right;
        }
    }
}
// 企微导入
.importEnterpriseWechatShow-content {
    margin-top: 18px;
    .content-left {
        display: inline-block;
        vertical-align: middle;
        margin-left: 33px;
    }
    .content-right {
        display: inline-block;
        margin-left: 22px;
        line-height: 25px;
        font-size: 14px;
        vertical-align: middle;
        width: 240px;
        .import-line {
            line-height: 20px;
            display: flex;
            align-items: center;
            a.link {
                color: #6ed773;
            }
            & + .import-line {
                margin-top: 8px;
                font-size: 14px;
            }
        }
    }
    &.erp {
        .content-right {
            .import-line {
                a.link {
                    color: #297beb;
                }
            }
        }
        .buttons {
            padding-right: 20px;
            text-align: right;
            margin-top: 20px;
        }
    }
}
// 明细账导入
.subsidiaryLedger-content {
    .subsidiaryLedger-main {
        .main-top {
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }
        .line-item-field {
            display: flex;
            align-items: center;
            > div.period-separative {
                margin: 0 10px;
            }
        }
        .main-body {
            padding: 0 20px;

            .accountRelation {
                cursor: pointer;
                color: var(--light-blue);
            }
            .tips {
                font-weight: bold;
                > div {
                    line-height: 14px;
                }
            }
            :deep(.el-table) {
                .el-checkbox.is-disabled {
                    display: inline-block;
                }
                .el-table__cell.disabled.asubName {
                    .cell {
                        color: #aaa;
                        cursor: default;
                    }
                }
            }
        }
    }
    &.erp {
        .buttons {
            margin-top: 20px;
        }
    }
}
// 银行回单导入
.recognize-content {
    .tip-content {
        margin: 5px 20px;
        .tip-line {
            font-size: 12px;
            font-weight: 600;
            line-height: 16px;
            span.link {
                font-size: 12px;
                position: relative;
                &.service {
                    &::after {
                        content: "";
                        display: none;
                        width: 200px;
                        height: 200px;
                        z-index: 100;
                        position: absolute;
                        left: 0;
                        top: 20px;
                        transform: translateX(-30%);
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                    }
                    &.acc {
                        &::after {
                            background-image: url("@/assets/Cashier/receipt_import_free.png");
                        }
                    }
                    &.pro {
                        &::after {
                            background-image: url("@/assets/Cashier/receipt_import_pro.png");
                        }
                    }
                    &.erp {
                        &::after {
                            background-image: url("@/assets/Cashier/receipt_import_erp.png");
                        }
                    }

                    &:hover::after {
                        display: block;
                    }
                }
            }
        }
    }
    .recognize-drag {
        margin: 0 20px 10px;
        :deep(.upload-demo) {
            position: relative;
            .el-upload-dragger {
                padding: 10px;
                padding-bottom: 40px;
            }
            .el-upload__tip {
                width: 100%;
                text-align: center;
                position: absolute;
                left: 0;
                bottom: 20px;
            }
        }
        :deep(.el-scrollbar) {
            .file-list {
                padding-left: 0;
                margin: 0;
                .file-list-item {
                    display: flex;
                    justify-content: space-between;
                    line-height: 30px;
                    font-size: 14px;
                    &:hover {
                        background-color: var(--el-fill-color-light);
                    }
                    > span {
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
                .file-list-oprate {
                    margin-left: 10px;
                    display: flex;
                    > span {
                        margin-right: 15px;
                        cursor: pointer;
                        color: var(--main-color);
                    }
                }
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
// 银行回单导入结果
.import-result-content {
    .import-result-main {
        margin: 10px 20px 10px;
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
// 样式
.solid-button {
    &.blue {
        border-color: #297beb;
        background-color: #297beb;
        &:hover {
            border-color: #2951eb;
            background-color: #2951eb;
        }
    }
}
.maincolor {
    color: var(--main-color);
}
.file-name {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
    cursor: pointer;
    display: inline-block;
}
.custom-confirm {
    .buttons {
        margin-top: 20px !important;
    }
}
.buttons.base {
    border-top: 1px solid var(--border-color);
}
