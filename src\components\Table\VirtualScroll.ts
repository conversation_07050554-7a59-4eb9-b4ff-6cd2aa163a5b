type OffsetMap = {  
    [key: string]: number;
}; 
export const getOffsetTop = (index: number, mapData: any[], offsetMap: OffsetMap, onlyKey: string) => {
    const item = mapData[index];
    if (item) return offsetMap[item[onlyKey]] || 0;
    return 0;
};
export const getSize = (index: number, virtuallyData: any[], sizes: OffsetMap, onlyKey: string, rowHeight: number) => {
    const item = virtuallyData[index];
    if (item) {
        const key = item[onlyKey];
        return sizes[key] || rowHeight;
    }
    return rowHeight;
};
export const getSEPos = (thisTop: number, thisBottom: number, data: any[], mapData: any[], offsetMap: OffsetMap, onlyKey: string) => {
    let l = 0;
    let r = data.length - 1;
    let mid = 0;
    while (l <= r) {
        mid = Math.floor((l + r) / 2);
        const midVal = getOffsetTop(mid, mapData, offsetMap, onlyKey);
        if (midVal < thisTop) {
            const midNextVal = getOffsetTop(mid + 1, mapData, offsetMap, onlyKey);
            if (midNextVal > thisTop) break;
            l = mid + 1;
        } else {
            r = mid - 1;
        }
    }

    let thisStart = mid;
    let thisEnd = data.length - 1;
    for (let i = thisStart + 1; i < data.length; i++) {
        const offsetTop = getOffsetTop(i, mapData, offsetMap, onlyKey);
        if (offsetTop >= thisBottom) {
            thisEnd = i;
            break;
        }
    }

    if (thisStart % 2) {
        thisStart = thisStart - 1;
    }
    return {
        thisStart: thisStart,
        thisEnd: thisEnd,
    }
}