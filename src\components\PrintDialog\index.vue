<template>
  <el-dialog v-model="printDialogShow" :title="title" center width="570" :modal-class="modalClass"
    class="custom-confirm dialogDrag" @close="handleClose">
    <div class="print-dialog" v-dialogDrag>
      <div class="print-content" @scroll="handleScroll">
        <div class="print-main">
          <slot name="custom"></slot>
          <div class="print-item mt-20" v-show="dirShow">
            <div class="print-item-label">打印方向：</div>
            <div class="print-item-field">
              <el-radio-group v-model="printInfo.direction" :disabled="dirDisabledFlag" @change="changeDir">
                <el-radio :label="0">纵向</el-radio>
                <el-radio :label="1">横向</el-radio>
              </el-radio-group>
            </div>
          </div>
          <div class="print-item">
            <div class="print-item-label">页边距：</div>
            <div class="print-item-field" style="width: 230px">
              <div class="flex-center" style="flex-wrap: wrap; justify-content: space-between">
                <div class="page-margin">
                  <span class="page-direction">上</span>
                  <el-input-number v-model="printInfo.top" :min="0" :max="20" controls-position="right"
                    @keyup="handleMargin($event, 'top', printInfo.top)"
                    @change="handleMargin($event, 'top', printInfo.top)" />
                  mm
                </div>
                <div class="page-margin">
                  <span class="page-direction">下</span>
                  <el-input-number v-model="printInfo.bottom" :min="0" :max="20" controls-position="right"
                    @keyup="handleMargin($event, 'bottom', printInfo.bottom)"
                    @input="handleMargin($event, 'bottom', printInfo.bottom)" 
                    @change="handleBottomBlur" 
                    @blur="handleBottomBlur" />
                  mm
                </div>
                <div class="page-margin">
                  <span class="page-direction">左</span>
                  <el-input-number v-model="printInfo.left" :min="0" :max="20" controls-position="right"
                    @keyup="handleMargin($event, 'left', printInfo.left)"
                    @change="handleMargin($event, 'left', printInfo.left)" />
                  mm
                </div>
                <div class="page-margin">
                  <span class="page-direction">右</span>
                  <el-input-number v-model="printInfo.right" :min="0" :max="20" controls-position="right"
                    @keyup="handleMargin($event, 'right', printInfo.right)"
                    @change="handleMargin($event, 'right', printInfo.right)" />
                  mm
                </div>
              </div>
            </div>
          </div>
          <div class="print-item">
            <div class="print-item-label">其他选项：</div>
            <div class="print-item-field">
              <div :class="{ 'grid-container': otherOptions?.length > 4 }" v-for="option in otherOptions"
                :key="option.key">
                <template v-if="option.label === '连续打印'">
                  <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start">
                    <template #content>
                      <div>勾选即接着上一页的空白直接打印，不勾选则另起一页打印</div>
                    </template>
                    <template #default>
                      <div style="width: 100px">
                        <el-checkbox v-model="printInfo[option.key]" :label="option.label"></el-checkbox>
                        <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px" />
                      </div>
                    </template>
                  </el-tooltip>
                </template>
                <template v-else-if="option.label === '显示打印日期'">
                  <el-checkbox v-model="printInfo[option.key]" :label="option.label"
                    @change="changeShowDate"></el-checkbox>
                  <el-date-picker style="width: 160px; height: 28px; margin-left: 10px" v-if="printInfo[option.key]"
                    v-model="printInfo['printDateText']" type="date" placeholder=" " value-format="YYYY-MM-DD"
                    size="small" :clearable="false" />
                </template>
                <template v-else>
                  <el-checkbox v-model="printInfo[option.key]" :label="option.label"
                    @change="(val) => changeCheckBox(val, option.key)"></el-checkbox>
                </template>
              </div>
            </div>
          </div>
          <div class="print-item">
            <div class="print-item-label">表尾字段设置：</div>
            <div class="print-item-field" style="padding-top: 8px">
              <div class="custom-add" @click="customAddText">+自定义</div>
              <div class="footer-list">
                <Container group-name="row" orientation="horizontal" drag-class="card-ghost"
                  v-for="(group, gi) in groupDisPlayArr" :key="gi" :animation-duration="100"
                  non-drag-area-selector=".editing" :get-child-payload="(i: number) => getChildPayload(i, gi)"
                  @drop="(dragResult: any) => handleDrop(dragResult, gi)">
                  <Draggable v-for="(item, index) in group" :key="item" class="oval" :class="{
                    editing: item.isEditting,
                    active: item.active,
                  }" :style="{
  width: item.width - 2 * horizontalMargin + 'px',
}" @click="handleActive(item)" @dblclick="handleEditDesc(item)">
                    <template v-if="item.type === 'selection'">
                      <el-checkbox v-if="item.prop === 'isHideAsName'" v-model="isShowAsName" :label="item.text"
                        @change="printInfo.isHideAsName = !printInfo.isHideAsName"></el-checkbox>
                      <el-checkbox v-else v-model="printInfo[item.prop]" :label="item.text"
                        @change="(val) => changeCheckBox(val, item.prop)"></el-checkbox>
                    </template>
                    <template v-else>
                      <template v-if="item.isEditting">
                        <el-input v-model="item.text" type="textarea" ref="textareaRef" resize="none" placeholder="请输入文本"
                          :autosize="{ minRows: 1, maxRows: 3 }" @input="(text: string) => handleInput(text, item)"
                          @blur="handleBlur(item)" />
                      </template>
                      <template v-else>
                        <ToolTip popperClass="print-voucher-tip" :content="item.text.trim()" :fontSize="12"
                          :line-clamp="1" :teleported="true" placement="top" :maxWidth="100">
                          <span class="hidden" :style="{ color: item.text.trim() ? '#333' : '#666' }">
                            {{ item.text.trim() || "请输入文本" }}
                          </span>
                        </ToolTip>
                        <span class="delete" @mousedown.stop @click.stop="handleDeleteOption(index, gi)"></span>
                      </template>
                    </template>
                  </Draggable>
                </Container>
              </div>
              <div class="cover" style="display: flex; margin-top: 15px; margin-bottom: 10px" v-if="coverEdit">
                <a class="button mr-10" @click="printFrontCover">单独打印封面</a>
                <div class="link" style="font-size: 14px; line-height: 26px; color: var(--link-color)" @click="editCover">
                  封面编辑
                  <img class="img-question" src="@/assets/Voucher/forward.png" style="height: 11px" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="buttons" style="border-top: 1px solid var(--border-color)">
        <a class="button" @click="printDialogShow = false">取消</a>
        <!-- 根据业财字体像素14px，业财边距24px -->
        <a class="button ml-20" :style="{ width: allPrint.length * 14 + (isErp ? 24 : 2) + 'px' }" v-if="allPrint"
          @click="continuePrint">
          {{ allPrint }}
        </a>
        <a class="button solid-button ml-20" @click="currentPrint">{{ allPrint ? "打印当前数据" : "打印" }}</a>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import ToolTip from "@/components/Tooltip/index.vue";
import { ElNotify } from "@/util/notify";
import { getFormatterDate } from "@/util/date";
import { globalWindowOpenPage, globalPrint, getUrlSearchParams } from "@/util/url";
import { defineProps, ref, computed, nextTick, watch } from "vue";
import { useRoute } from "vue-router";
import { ElInput } from "element-plus";
import { Container, Draggable } from "vue3-smooth-dnd";

const isErp = window.isErp;
const printDialogShow = defineModel<boolean>("printDialogShow");
const props = defineProps({
  title: {
    type: String,
    default: "明细账打印",
  },
  printData: {
    type: Object,
    default: () => {
      return {
        direction: 0,
        isShowPrintDate: false,
        basepage: false,
        printCatalog: true,
        ifpage: 0,
        HideSubject: true,
      };
    },
  },
  dirShow: {
    type: Boolean,
    default: true,
  },
  dirDisabled: {
    type: Boolean,
    default: false,
  },
  otherOptions: {
    type: Object,
  },
  coverEdit: {
    type: Boolean,
    default: false,
  },
  allPrint: {
    type: String,
    default: "",
  },
  modalClass: {
    type: String,
    default: "modal-class",
  },
  customNum: {
    type: Number,
    default: 5
  }
});
const emit = defineEmits(["continuePrint", "currentPrint", "update:printData"]);
const printInfo = computed({
  get: () => props.printData,
  set: (val) => {
    emit("update:printData", JSON.parse(JSON.stringify(val)));
  },
});
watch(() => printDialogShow.value, (val) => {
  if (val) {
    printInfo.value = JSON.parse(JSON.stringify(props.printData));
    initTableFooter();
  }
}, { immediate: true })
const groupDisPlayArr = ref();

const originPrintInfo = ref();
watch(
  () => props.printData,
  () => {
    originPrintInfo.value = JSON.parse(JSON.stringify(props.printData));
    initTableFooter();
  },
  { immediate: true }
);

const isShowAsName = computed(() => !printInfo.value.isHideAsName);

const dirDisabledFlag = computed(() => props.dirDisabled);

function changeShowDate(val: any) {
  if (val) {
    printInfo.value.printDateText = getFormatterDate("", "-");
    handleBottomBlur();
  }
}
function changeCheckBox(val: any, key: string) {
  if (key === "isShowPageNumber" && val) {
    handleBottomBlur();
  }
  if (key === "isPrintCashierName") {
    if (val) {
      flatfooterList();
    } else {
      const index = printInfo.value.bottomTextList.findIndex((item: string) => item === "#CASHIER");
      if (index !== -1) {
        printInfo.value.bottomTextList.splice(index, 1);
      }
    }
  }
}

function handleClose() {
  printInfo.value = JSON.parse(JSON.stringify(originPrintInfo.value))
}

function printFrontCover() {
  //账簿打印
  globalPrint(
    window.printHost +
    "/api/Voucher/OnlyPrintBasePage?" +
    getUrlSearchParams({
      pageType: printInfo.value.direction === 1 ? 9 : 6,
      settingType: printInfo.value.direction === 1 ? 14 : 11,
      direction: printInfo.value.direction === 1 ? "H" : "Z",
      from: route.meta.subMenu,
    })
  );
}

function changeDir(){
  // window.dispatchEvent(
  //       new CustomEvent("refreshPageSize", {
  //           detail: { direction: printInfo.value.direction === 1 ? "H" : "Z" },
  //       })
  //   );
}

const route = useRoute();
function editCover() {
  //目前是账簿打印封面，默认为A4整版
  globalWindowOpenPage(
    `/AccountBooks/BooksCoverPrint?printType=6&direction=${printInfo.value.direction === 1 ? "H" : "Z"}&from=${route.meta.subMenu}`,
    "账簿封面打印设置"
  );
}

const borderWdith = 1;
const horizontalPadding = 11;
const horizontalMargin = 4;
const containerWidth = 276;
function getOffsetWidth(text: string) {
  const div = document.createElement("div");
  div.innerText = text;
  div.style.display = "inline-block";
  div.style.visibility = "hidden";
  div.style.fontSize = "12px";
  document.body.appendChild(div);
  const width = div.getBoundingClientRect().width + 2 * (horizontalPadding + horizontalMargin + borderWdith);
  document.body.removeChild(div);
  return width > 130 ? 130 : width;
}

function getDescriptionGroup(descriptions: any) {
  console.log("descriptions", descriptions);
  let result: any = [];
  if (descriptions.length === 0) return result;
  for (let i = 0; i < descriptions.length; i++) {
    const item = descriptions[i];
    if (item.width > containerWidth) {
      result.push([item]);
    } else {
      if (result.length === 0) {
        result.push([item]);
      } else {
        const lastGroup = result[result.length - 1];
        const lastGroupWidth = lastGroup.reduce((acc, cur) => acc + cur.width, 0);
        if (lastGroupWidth + item.width > containerWidth) {
          result.push([item]);
        } else {
          lastGroup.push(item);
        }
      }
    }
  }
  return result;
}
function initTableFooter() {
  let footerList: any = [];
  if (Object.prototype.hasOwnProperty.call(props.printData, "isHideAsName")) {
    footerList = [{ text: "账套名称", isEditting: false, active: false, type: "selection", prop: "isHideAsName", width: 98 }];
  }
  // if (Object.prototype.hasOwnProperty.call(props.printData, "isPrintCashierName")) {
  //   footerList = [{ text: "出纳", isEditting: false, active: false, type: "selection", prop: "isPrintCashierName", width: 80 }];
  // }
  if (props.printData.bottomTextList.length) {
    props.printData.bottomTextList.forEach((item: string) => {
      if (item === "#CASHIER"){
        footerList.push({ text: "出纳", isEditting: false, active: false, type: "selection", prop: "isPrintCashierName", width: 80 }) 
      }else if(item.trim() && item !== "#CASHIER"){
        footerList.push({ text: item, isEditting: false, active: false, type: "text", prop: "custom", width: getOffsetWidth(item) });
      }
    });
  }
  groupDisPlayArr.value = getDescriptionGroup(footerList);
  flatfooterList();
}
function flatfooterList() {
  printInfo.value.bottomTextList = groupDisPlayArr.value
    .flat()
    .filter((item) => (item.text && (item.type !== "selection" && item.text !== "出纳") || (item.text === "出纳" && printInfo.value.isPrintCashierName)))
    .map((item) => (item.type === "selection" && item.text === "出纳" ? "#CASHIER" : item.text));
}

function customAddText() {
  //自定义的不超过6个字，超过6个字的话，就不添加了
  if (groupDisPlayArr.value.flat().filter((item) => item.prop === "custom").length >= props.customNum) {
    ElNotify({ type: "warning", message: `亲，最多只能增加${props.customNum}个自定义字段哦` });
    return;
  }
  //计算宽度
  const lastTotalWidth = groupDisPlayArr.value[groupDisPlayArr.value.length - 1]?.reduce((totalWidth: number, item) => {
    return totalWidth + item.width;
  }, 0);
  const textWidth = getOffsetWidth("请输入文本") + 2 * (horizontalMargin);
  if (lastTotalWidth + textWidth > containerWidth) {
    groupDisPlayArr.value.push([
      {
        text: "",
        isEditting: true,
        active: true,
        type: "text",
        prop: "custom",
        width: textWidth,
      },
    ]);
  } else {
    if (groupDisPlayArr.value.length === 0) {
      groupDisPlayArr.value.push([]);
    }
    groupDisPlayArr.value[groupDisPlayArr.value.length - 1].push({
      text: "",
      isEditting: true,
      active: true,
      type: "text",
      prop: "custom",
      width: textWidth,
    });
  }
  nextTick().then(() => {
    textareaRef.value && textareaRef.value[0]?.focus();
  });
  flatfooterList();
}

function getChildPayload(index: number, groupIndex: number) {
  return groupDisPlayArr.value[groupIndex][index];
}
let dragSelection = false;
function handleDrop(dragResult: any, groupIndex: number) {
  const { removedIndex, addedIndex, payload } = dragResult;
  if (removedIndex === null && addedIndex === null) {
    // dragSelection = false;
    return;
  }
  const gr = [...groupDisPlayArr.value];
  const result = [...groupDisPlayArr.value[groupIndex]];
  if (
    (result[addedIndex]?.type === "selection" && result[addedIndex]?.text === "账套名称") ||
    (payload.type === "selection" && payload.text === "账套名称")
  ) {
    dragSelection = true;
    return;
  }
  let itemToAdd = payload;
  if (removedIndex !== null && !dragSelection) {
    itemToAdd = result.splice(removedIndex, 1)[0];
  }
  if (addedIndex !== null) {
    result.splice(addedIndex, 0, itemToAdd);
  }
  dragSelection = false;
  gr.splice(groupIndex, 1, result);
  groupDisPlayArr.value = gr;
  const timer = setTimeout(() => {
    groupDisPlayArr.value = getDescriptionGroup([...groupDisPlayArr.value.flat()]);
    flatfooterList();
    clearTimeout(timer);
  });
}
let lastActiveDesc: any;
function clearLastActive() {
  if (lastActiveDesc) {
    lastActiveDesc.active = false;
    lastActiveDesc.editing = false;
    lastActiveDesc = null;
  }
}
function handleClickOutside(event: Event) {
  const allowList = ["oval", "el-textarea__inner", "hidden"];
  console.log("event.target", event.target);
  if (event.target && allowList.every((className) => !(event.target as Element).classList.contains(className))) {
    clearLastActive();
    document.removeEventListener("click", handleClickOutside);
  }
}
function handleActive(description: any) {
  // if (groupDisPlayArr.value.flat().some((item) => item.isEditting)) return;
  clearLastActive();
  // document.removeEventListener("click", handleClickOutside);
  description.active = true;
  lastActiveDesc = description;
  // document.addEventListener("click", handleClickOutside);
}
const textareaRef = ref<Array<InstanceType<typeof ElInput>>>();
function handleEditDesc(description: any) {
  description.isEditting = true;
  description.active = true;
  description.width = getOffsetWidth(description.text) < 108 ? 108 : getOffsetWidth(description.text);
  groupDisPlayArr.value = getDescriptionGroup([...groupDisPlayArr.value.flat()]);
  nextTick().then(() => {
    textareaRef.value && textareaRef.value[0]?.focus();
  });
}
function handleBlur(disItem: any) {
  console.log("handleBlur", disItem);
  disItem.isEditting = false;
  disItem.active = false;
  disItem.width = getOffsetWidth(disItem.text?.trim() || "请输入文本");
  groupDisPlayArr.value = getDescriptionGroup([...groupDisPlayArr.value.flat()]);
  flatfooterList();
}

function handleInput(text: string, description: any) {
  if (text.length > 30) {
    ElNotify({ type: "warning", message: `自定义文本不能超过30个字符` });
    description.text = text.slice(0, 30);
  }
}

function handleDeleteOption(index: number, groupIndex: number) {
  groupDisPlayArr.value[groupIndex].splice(index, 1);
  groupDisPlayArr.value = getDescriptionGroup([...groupDisPlayArr.value.flat()]);
  flatfooterList();
}

function continuePrint() {
  emit("continuePrint");
}

function currentPrint() {
  printInfo.value.bottomTextList = printInfo.value.bottomTextList.filter((item: string) => item.trim());
  flatfooterList();
  emit("currentPrint");
}

function handleScroll() {
  let tooltip = document.getElementsByClassName("print-voucher-tip");
  console.log("tooltip", tooltip.length);
  if (tooltip.length > 0) {
    (tooltip[tooltip.length - 1] as HTMLElement).style.display = "none";
  }
}

function handleBottomBlur() {
  if ((printInfo.value.isShowPageNumber || printInfo.value.isShowPrintDate) && printInfo.value.bottom < 10) {
    ElNotify({ type: "warning", message: "勾选打印页码或日期时，下边距不能小于10哦~" });
    nextTick(() => {
      printInfo.value.bottom = 10;
    });
  }
}

function handleMargin(event: any, type: string, val: number) {
  if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0 || Number(val) < 0) {
    ElNotify({ type: "warning", message: "请输入正整数边距~" });
    event.target.value = 0;
  }
  if (event.target && event.target.value > 20) {
    ElNotify({ type: "warning", message: "上下左右的边距不能大于20mm" });
    event.target.value = 20;
  }
  printInfo.value[type] = val;
  event && (event.target.value = Number(printInfo.value[type]));
  if (event.key && event.key === "Backspace") {
    event.target.blur();
  }
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.print-content {
  padding-left: 70px;
  padding-bottom: 10px;
  max-height: 336px;
  overflow: auto;
  .detail-lm-default-scroll(8px);

  .print-item {
    display: flex;
    align-items: center;
    padding-top: 8px;

    .print-item-label {
      text-align: right;
      line-height: 32px;
      width: 100px;
      padding-top: 0;
      align-self: flex-start;
      margin-right: 10px;
    }

    .print-item-field {
      .sub-level {
        padding-left: 20px;
      }

      .custom-add {
        border: 1px solid #44b449;
        color: #44b449;
        width: 60px;
        text-align: center;
        margin-bottom: 10px;

        &:hover {
          background-color: #44b449;
          color: #fff;
        }
      }

      .footer-list {
        width: 276px;
        display: flex;
        flex-wrap: wrap;

        .footer-item {
          width: 60px;
          height: 24px;
          line-height: 24px;
          text-align: left;
          margin-right: 20px;
          margin-top: 10px;

          ::after {
            content: "ddddddddd";
            background-image: url("@/assets/Menu/right-menu-close-all-icon.png");
          }

          &:hover {
            &::after {
              background-image: url("@/assets/Menu/right-menu-close-all-hover-icon.png");
            }
          }
        }
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;

  .page-margin {
    display: flex;
    font-size: 11px;
    line-height: 24px;
    margin-top: 10px;

    .page-direction {
      width: 24px;
      height: 24px;
      text-align: center;
      border: 1px solid #44b449;
      border-right: none;
      color: #666;
    }

    :deep(.el-input-number) {
      width: 48px;

      .el-input-number__decrease,
      .el-input-number__increase {
        height: 12px !important;
        width: 12px !important;
      }

      .el-input {
        height: 26px;

        .el-input__wrapper {
          box-shadow: 0 0 0 0;
          border: 1px solid #44b449;
          padding-left: 0 !important;
          padding-right: 0 !important;

          .el-input__inner {
            text-align: center;
            padding-right: 13px !important;
          }
        }
      }
    }

    .operate {
      width: 18px;
      height: 24px;
      border: 1px solid #a3cfff;
      display: flex;
      flex-wrap: wrap;

      span {
        cursor: pointer;
        width: 100%;
        height: 12px;
        text-align: center;
        line-height: 12px;
      }

      .add-btn {
        .el-icon-caret-top:before {
          content: "\E60C";
        }
      }

      .sub-btn {
        .el-icon-caret-bottom:before {
          content: "\E60B";
        }
      }
    }
  }
}

.oval {
  display: inline-block !important;
  padding: 3px 11px;
  border-radius: 12px;
  border: 1px solid #e9e9e9;
  margin: 0 4px;
  position: relative;
  cursor: default;
  font-size: 12px;
  min-height: 26px;
  max-width: 130px;

  :deep(.el-checkbox) {
    height: 20px;
  }

  :deep(.el-checkbox__label) {
    font-size: 12px;
  }

  &:hover {
    border-color: #44b449;
  }

  .hidden {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    // max-width: 100px;
  }

  .delete {
    display: none;
    height: 12px;
    width: 12px;
    background: url("@/assets/Icons/delete.png") no-repeat;
    background-size: 12px;
    position: absolute;
    right: -2px;
    top: -4px;
    cursor: pointer;
  }

  &:hover {
    .delete {
      display: inline-block;
    }
  }

  &.active {
    border-color: var(--main-color);

    :deep(.el-textarea) {
      .el-textarea__inner {
        padding: 0;
        box-shadow: none;
        font-size: 12px;
        word-break: break-all;
      }
    }

    &.editing {
      padding-left: 5px;
      padding-right: 5px;
      min-width: 100px;
      height: auto;
    }
  }
}

:deep(.smooth-dnd-container) {
  &+.smooth-dnd-container {
    margin-top: 8px;
  }

  min-height: auto;

  .smooth-dnd-draggable-wrapper {
    overflow: visible;
    height: 26px;
  }

  &:has(.card-ghost) .fixed {
    &:hover {
      border-color: #e9e9e9;
    }
  }
}

body[erp] {
  .custom-confirm .el-dialog__body .buttons a {
    float: none;
    display: inline-block;
  }

  .flex-center {
    .page-direction {
      border: 1px solid #3d7fff;
      border-right: none;
    }

    :deep(.el-input-number) {
      .el-input {
        .el-input__wrapper {
          border: 1px solid #3d7fff;
        }
      }
    }
  }

  .print-content {
    .print-item-field {
      .custom-add {
        border: 1px solid #3d7fff;
        color: #3d7fff;

        &:hover {
          background-color: #3d7fff;
          ;
          color: #fff;
        }
      }
    }
  }

  .oval {
    &:hover {
      border-color: #3d7fff;
    }
  }
}
</style>
