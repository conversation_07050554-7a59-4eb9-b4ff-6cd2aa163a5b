@media screen and (min-width: 0px) {
    .content {
        width: 100%;
        padding-left: var(--self-adaption-content-padding);
        padding-right: var(--self-adaption-content-padding);
        box-sizing: border-box;

        .main-content,
        .edit-content,
        .slot-content,
        .slot-mini-content {
            width: 100%;
        }
        &.narrow-content {
            // 页面调窄
            .main-top,
            .main-center {
                width: 1100px;
                margin: 0 auto;
            }
            .main-top {
                padding: 20px 0 !important;
            }
            .divider-line {
                height: 1px;
                background-color: var(--title-split-line);
                margin-bottom: 20px;
            }
        }
    }

    body[erp] {
        .content {
            margin-left: auto;
            margin-right: auto;
            padding-left: 0;
            padding-right: 0;

            .edit-content {
                border-radius: 6px;
            }
        }
    }
}
