import { ref } from "vue";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import store from "@/store";
import {
    getAssistingAccountApi,
    getAssistingAccountTypeApi,
    getDepartmentApi,
    getAAWithAcronymApi,
    AATypeEnum,
} from "@/api/assistingAccounting";
import type { IAssistingAccountType, IAssistingAccount, AAListWithAcronymItem } from "@/api/assistingAccounting";
import type { IResponseModel } from "@/util/service";

export const useAssistingAccountingStore = defineStore("assistingAccounting", () => {
    const assistingAccountingTypeList = ref<IAssistingAccountType[]>([]);
    const assistingAccountingList = ref<IAssistingAccount[]>([]);
    const assistingAccountingListAll = ref<IAssistingAccount[]>([]);
    const departmentList = ref<IAssistingAccount[]>([]);
    const departmentListAll = ref<IAssistingAccount[]>([]);
    const assistingAccountingWithAcronymList = ref<AAListWithAcronymItem[]>([]);
    const getAssistingAccountingType = () => {
        return new Promise<IAssistingAccountType[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getAssistingAccountTypeApi()
                    .then((res: any) => {
                        const data = res as IResponseModel<IAssistingAccountType[]>;
                        if (data.state === 1000) {
                            assistingAccountingTypeList.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    const getAssistingAccounting = (type?: AATypeEnum) => {
        return new Promise<IAssistingAccount[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getAssistingAccountApi()
                    .then((res: any) => {
                        const data = res as IResponseModel<IAssistingAccount[]>;
                        if (data.state === 1000) {
                            assistingAccountingList.value = data.data.filter((item) => item.aaeid > 0);
                            assistingAccountingListAll.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
                if (type && type === AATypeEnum.Stock) {
                    getAAListWithAcronym();
                }
            }
        });
    };

    const getAssistingAccountingModel = (aaeid: number) => {
        const item = assistingAccountingList.value.find((x) => x.aaeid === aaeid);
        return item;
    };

    const getAssistingAccountingByTypeModel = (aatype: number, status = true) => {
        const item = assistingAccountingListAll.value.filter((x) => (status ? x.aatype === aatype && x.status === 0 : x.aatype === aatype));
        return item;
    };

    const getDepartment = () => {
        return new Promise<IAssistingAccount[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getDepartmentApi(true, false)
                    .then((res: any) => {
                        const data = res as IResponseModel<IAssistingAccount[]>;
                        if (data.state === 1000) {
                            departmentList.value = data.data.filter((item) => item.aaeid > 0);
                            departmentListAll.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };
    const getAAListWithAcronym = (): Promise<AAListWithAcronymItem[]> => {
        return new Promise<AAListWithAcronymItem[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getAAWithAcronymApi()
                    .then((res: IResponseModel<AAListWithAcronymItem[]>) => {
                        if (res.state === 1000) {
                            assistingAccountingWithAcronymList.value = res.data;
                            resolve(res.data);
                        } else {
                            reject(res.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };
    return {
        assistingAccountingTypeList,
        assistingAccountingList,
        departmentList,
        departmentListAll,
        assistingAccountingListAll,
        getAssistingAccountingType,
        getAssistingAccounting,
        getAssistingAccountingModel,
        getAssistingAccountingByTypeModel,
        getDepartment,
        getAAListWithAcronym,
        assistingAccountingWithAcronymList,
    };
});

/** 在setup外使用 */
export function useAssistingAccountingStoreHook() {
    return useAssistingAccountingStore(store);
}
