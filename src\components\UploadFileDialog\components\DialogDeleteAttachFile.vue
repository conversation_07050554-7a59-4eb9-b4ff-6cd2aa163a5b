<template>
    <el-dialog
        :title="'提示'"
        v-model="relateVoucherOrBillDialogShow"
        destroy-on-close
        :width="500"
        align-center
        center
        :show-close="false"
        class="dialogDrag"
    >
        <div class="dialog-content tip" v-dialogDrag>
            <div class="dialog-content-body">
                <div class="dialog-content-message" style="text-align: left">
                    {{ relateVoucherOrBillDialogOptions.mainTip }}
                    <div style="height: 20px"></div>
                    点击'{{ relateVoucherOrBillDialogOptions.confirmButtonText }}'
                    {{ relateVoucherOrBillDialogOptions.subTip }}
                    点击'{{ relateVoucherOrBillDialogOptions.cancelButtonText }}'则同时解除附件与单据和凭证的关联
                </div>
            </div>
            <div class="buttons" :class="{ 'erp-buttons': isErp }">
                <a class="button solid-button large" @click="handleOnlyDelete">{{ relateVoucherOrBillDialogOptions.confirmButtonText }}</a>
                <a class="button ml-20 large" @click="handleBatchDelete">{{ relateVoucherOrBillDialogOptions.cancelButtonText }}</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

const isErp = ref(window.isErp);

const props = defineProps<{
    type: "bill" | "voucher";
    onlyDelete: Function;
    batchDelete: Function;
}>();
const billTipOptions = {
    mainTip: "删除单据的附件及其关联关系时，是否同时解除该附件与已关联凭证的关联关系？",
    subTip: "则解除附件与单据的关联，保留与凭证的关联",
    confirmButtonText: "仅删除单据关联",
    cancelButtonText: "删除凭证关联",
};
const voucherTipOptions = {
    mainTip: "删除凭证的附件及其关联关系时，是否同时解除该附件与已关联单据的关联关系？",
    subTip: "则解除附件与凭证的关联，保留与单据的关联",
    confirmButtonText: "仅删除凭证关联",
    cancelButtonText: "删除单据关联",
};
const relateVoucherOrBillDialogShow = ref(true);
const relateVoucherOrBillDialogOptions = computed(() => {
    return props.type === "bill" ? billTipOptions : voucherTipOptions;
});
function handleBatchDelete() {
    props.batchDelete();
    relateVoucherOrBillDialogShow.value = false;
}
function handleOnlyDelete() {
    props.onlyDelete();
    relateVoucherOrBillDialogShow.value = false;
}
</script>

<style lang="less" scoped>
.dialog-content {
    &.tip {
        .buttons {
            border-top: 1px solid var(--border-color);
            .button.large {
                width: 100px;
            }
            &.erp-buttons {
                .button.large {
                    width: auto;
                }
            }
        }
    }
}
</style>
