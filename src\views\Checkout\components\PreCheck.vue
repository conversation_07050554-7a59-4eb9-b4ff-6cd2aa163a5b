<template>
    <div class="fixed-check fixedOverFlow">
        <div class="checkitemBoxs">
            <div class="stepTitle">
                <span>第 1 步：预检查</span>
            </div>
            <div class="precheck-panal" v-show="initialUnBalanceShow">
                <div class="precheck-left"><span>期初检查</span></div>
                <div class="precheck-right">
                    <span>期初不平衡</span>
                    <a class="link ml-20" @click="handleClick('initialUnBalance')">点击处理</a>
                </div>
            </div>
            <div class="precheck-panal" v-show="fixedAssetsShow">
                <div class="precheck-left"><span>资产检查</span></div>
                <div class="precheck-right">
                    <span>当前期间资产未结账</span>
                    <a class="link ml-20" @click="handleClick('fixedAsset')">点击处理</a>
                </div>
            </div>
            <div class="precheck-panal" v-show="tempVoucherShow">
                <div class="precheck-left"><span>暂存凭证检查</span></div>
                <div class="precheck-right">
                    <span>当前期间存在暂存凭证</span>
                    <a class="link ml-20" @click="handleClick('tempVoucher')">点击处理</a>
                </div>
            </div>
            <div class="check-box-tool-bar">
                <a class="button" @click="() => emit('back2main')">上一步</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";

import type { IPreCheckList } from "../tpyes";
import { globalWindowOpenPage } from "@/util/url";

const isErp = ref(window.isErp);
const currentPid = ref(0);
const fixedAssetsShow = ref(false);
const tempVoucherShow = ref(false);
const initialUnBalanceShow = ref(false);
const emit = defineEmits(["back2main"]);

const fixedAssetHref = ref("");
const tempVoucherHref = ref("");
const initialUnBalanceHref = ref("");

const handleClick = (type: string) => {
    if (type === "initialUnBalance") {
        if (!checkPermission(["initialbalance1-canview"])) {
            ElNotify({ type: "warning", message: "您没有此权限" });
            return;
        }
        globalWindowOpenPage(initialUnBalanceHref.value, isErp.value ? "科目期初余额" : "期初");
    } else if (type === "fixedAsset") {
        if (!checkPermission(["fixedassets-init-canview"]) && !checkPermission(["fixedassets-card-canview"])) {
            ElNotify({ type: "warning", message: "您没有此权限" });
            return;
        }
        globalWindowOpenPage(fixedAssetHref.value, "资产管理");
    } else if (type === "tempVoucher") {
        if (!checkPermission(["voucher-canview"])) {
            ElNotify({ type: "warning", message: "您没有此权限" });
            return;
        }
        globalWindowOpenPage(tempVoucherHref.value, "凭证列表");
    }
};

const handleInit = (pid: number, preCheckList: IPreCheckList[]) => {
    currentPid.value = pid;
    preCheckList.forEach((item) => {
        if (item.preCheckName === "暂存凭证检查") {
            tempVoucherShow.value = !item.isPassed;
        } else if (item.preCheckName === "资产检查") {
            fixedAssetsShow.value = !item.isPassed;
        } else if (item.preCheckName === "期初检查") {
            initialUnBalanceShow.value = !item.isPassed;
        }
    });
    fixedAssetHref.value = "/FixedAssets/FixedAssets?from=checkout&period=" + currentPid.value + "&tabflag=3&appasid=" + getGlobalToken()+ "&r="+ Math.random();
    tempVoucherHref.value =
        "/Voucher/VoucherList?from=checkout&startP=" +
        currentPid.value +
        "&endP=" +
        currentPid.value +
        "&onlyTempVoucher=1&appasid=" +
        getGlobalToken();
    initialUnBalanceHref.value = "/Settings/InitialBalance1?from=checkout&appasid=" + getGlobalToken();
};

defineExpose({ handleInit });
</script>

<style lang="less" scoped>
.fixed-check {
    width: 1000px;
    margin: 0 auto;
    text-align: left;
    min-height: 500px;
    .checkitemBoxs {
        .stepTitle {
            color: var(--font-color);
            font-size: 24px;
            line-height: 30px;
            margin-top: 60px;
            padding-left: 130px;
        }
        .precheck-panal {
            margin-top: 40px;
            padding-left: 130px;
            .precheck-left {
                color: var(--font-color);
                font-size: var(--h3);
                line-height: 22px;
            }
            .precheck-right {
                margin-top: 8px;
                padding-left: 20px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                &:before {
                    content: "";
                    display: inline-block;
                    vertical-align: top;
                    margin-top: 8px;
                    height: 4px;
                    width: 4px;
                    background-color: var(--weaker-font-color);
                    border-radius: 2px;
                    margin-right: 10px;
                }
            }
        }
        .check-box-tool-bar {
            margin-top: 50px;
            text-align: center;
            margin-bottom: 40px;
        }
    }
}
</style>
