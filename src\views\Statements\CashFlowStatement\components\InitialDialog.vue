<template>
    <el-dialog
        v-model="visible"
        center
        width="700px"
        top="5vh"
        title="现金流量表期初数"
        :before-close="handleBeforeClose"
        :draggable="false"
        @closed="handleReset"
        class="dialogDrag"
    >
        <div class="initial-dialog-content" v-dialogDrag>
            <div class="initial-dialog-top" v-if="!isCheckOut">
                <a class="button solid-button mr-10" v-permission="['cashflowinitial-canedit']" @click="handleSubmit">保存</a>
                <a class="button mr-20" @click="handleImport" v-permission="['cashflowinitial-canimport']">导入</a>
            </div>
            <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                <Table height="500px" 
                    :data="tableData" 
                    :columns="columns" 
                    :scrollbar-show="true"
                    :tableName="setModule"
                >
                    <template #lineName>
                        <el-table-column 
                            label="项目" 
                            min-width="200" 
                            align="left" 
                            headerAlign="left" 
                            :show-overflow-tooltip="false"
                            prop="lineName"
                            :width="getColumnWidth(setModule, 'lineName')"
                        >
                            <template #default="{ row }: { row: ITableData }">
                                <span :class="assertNameClass(row)">
                                    {{ row.lineName }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #amount>
                        <el-table-column
                            :label="thirdLabel"
                            min-width="100"
                            align="right"
                            header-align="right"
                            :show-overflow-tooltip="false"
                            :resizable="false"
                        >
                            <template #default="{ row }: { row: ITableData }">
                                <template
                                    v-if="
                                        checkPermission(['cashflowinitial-canedit']) &&
                                        row.lineType === 1 &&
                                        !isCheckOut &&
                                        !disabledLines.includes(row.lineID)
                                    "
                                >
                                    <input
                                        type="text"
                                        :class="{ 'cash-amount-input': true, 'highlight-red': row.amount < 0 }"
                                        v-decimal-limit="[13, 2]"
                                        :value="formatMoney(row.amount) || ''"
                                        @blur="changeValue(row.lineID, $event)"
                                    />
                                </template>
                                <template v-else>
                                    <span :class="{ 'highlight-red': row.amount < 0 }">
                                        {{ formatMoney(row.amount) || "" }}
                                    </span>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </el-dialog>
<ImportSingleFileDialog
    importTitle="导入现金流量表期初"
    importUrl="/api/CashFlowStatement/Initial/Import"
    v-model:import-show="importDialogDisplay"
    :downloadTemplate="handleDownloadTemplate"
    :uploadSuccess="uploadSuccess"
></ImportSingleFileDialog>
</template>

<script lang="ts" setup>
import { nextTick, ref, onBeforeMount } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { globalExport } from "@/util/url";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import { formatMoney } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAccountSetStore } from "@/store/modules/accountset";
import { PeriodStatus } from "@/api/period";
import { hasNumberTitle } from "../utils";
import { getGlobalLodash } from "@/util/lodash";
import Big from "big.js";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const disabledLines = [********, ********];
const setModule = "CashInitailDialog";
interface ITableData {
    amount1: string;
    initalAmount1: string;
    createdManually: number;
    pid: number;
    asid: number;
    statementID: number;
    lineID: number;
    lineName: string;
    lineType: number;
    entryType: number;
    lineNumber: number;
    note: string;
    parentID: number;
    expand: number;
    amount: number;
    initialAmount: number;
    priority: number;
    coumType: number;
}

const { cloneDeep, isEqual } = getGlobalLodash();
const emit = defineEmits(["loadTableData"]);

const thirdLabel = ref("1月累计金额");
const columns: IColumnProps[] = [
    { slot: "lineName" },
    {
        label: "行次",
        prop: "lineNumber",
        headerAlign: "left",
        align: "left",
        minWidth: 40,
        formatter: function (row: ITableData, column: any, value: number) {
            return value === 0 ? "" : value.toString();
        },
        width: getColumnWidth(setModule, 'lineNumber')
    },
    { slot: "amount" },
];

const tableData = ref<Array<ITableData>>([]);
let cacheTableData: Array<ITableData> = [];
const loading = ref(false);
async function handleLoadTableData() {
    loading.value = true;
    await request({ url: "/api/CashFlowStatement/Initial" })
        .then((res: IResponseModel<Array<ITableData>>) => {
            loading.value = false;
            if (res.state !== 1000) return;
            cacheTableData = cloneDeep(res.data);
            tableData.value = res.data;
        })
        .catch(() => (loading.value = false));
}

let changed = false;
function handleBeforeClose(done: Function) {
    if (!changed && !isEqual(tableData.value, cacheTableData)) {
        changed = true;
    }
    if (changed) {
        ElConfirm("当前数据未保存，是否退出", false, () => {}, "提示", { confirmButtonText: "退出", cancelButtonText: "取消" }).then(
            (r: boolean) => {
                done(!r);
            }
        );
    } else {
        done(false);
    }
}
function isStringNumber(str: string) {
    str = str.replace(/,/g, "");
    return /^(-)?\d+(\.\d+)?$/.test(str);
}
function changeValue(lineID: number, e: Event) {
    const target = e.target as HTMLInputElement;
    const inputValue = target.value.replace(/,/g, "");
    if (!isStringNumber(inputValue)) {
        nextTick().then(() => {
            target.value = "";
        });
    }
    const value = isStringNumber(inputValue) ? Number(inputValue) : 0;
    calculator(lineID, value);
}
function calculatorHandle(totalLineId: number, totalIndex: number, diff: number, isPlus: boolean) {
    for (let i = totalIndex + 1; i < tableData.value.length; i++) {
        const item = tableData.value[i];
        if (!formulas.value[item.lineID]) continue;
        const changeAsub = formulas.value[item.lineID].find((v) => v.asubid === totalLineId);
        if (!changeAsub) continue;
        const plusMoney = Number(new Big(item.amount).plus(diff).toString()) || 0;
        const minusMoney = Number(new Big(item.amount).minus(diff).toString()) || 0;
        item.amount = changeAsub.operator === 1 && isPlus ? plusMoney : minusMoney;
        calculatorHandle(item.lineID, i, diff, changeAsub.operator === 1 && isPlus);
    }
}
function calculator(lineId: number, value: number) {
    const index = tableData.value.findIndex((v) => v.lineID === lineId);
    const diff = Number(new Big(value).minus(tableData.value[index].amount).toString()) || 0;
    tableData.value[index].amount = value;
    const totalLines: Array<ITableData> = [];
    let totalIndex = 0;
    let totalLineId = 0;
    for (let i = index + 1; i < tableData.value.length; i++) {
        const item = tableData.value[i];
        if (hasNumberTitle(item.lineName)) {
            break;
        } else {
            if (item.lineType === 3) {
                totalLines.push(item);
                totalIndex = i;
                totalLineId = item.lineID;
            }
        }
    }
    const length = totalLines.length;
    if (length === 0 || length > 3) return;
    let isPlus = true;
    totalLines.forEach((totalItems, index) => {
        const changeAsub = formulas.value[totalItems.lineID].find((v) => v.asubid === lineId);
        const plusMoney = Number(new Big(totalItems.amount).plus(diff).toString()) || 0;
        const minusMoney = Number(new Big(totalItems.amount).minus(diff).toString()) || 0;
        if (changeAsub) {
            isPlus = changeAsub.operator === 1;
            totalItems.amount = changeAsub.operator === 1 ? plusMoney : minusMoney;
        } else if (length > 1 && index === length - 1) {
            isPlus = length === 3;
            totalItems.amount = length === 3 ? plusMoney : minusMoney;
        }
    });
    calculatorHandle(totalLineId, totalIndex, diff, isPlus);
}
let isSaving = false;
function handleSubmit() {
    if (isSaving) return;
    isSaving = true;
    if (!changed && !isEqual(tableData.value, cacheTableData)) {
        changed = true;
    }
    if (!changed) {
        ElNotify({ message: "保存成功", type: "success" });
        visible.value = false;
        isSaving = false;
        return;
    }
    const data = tableData.value.map((v) => {
        return {
            lineID: v.lineID,
            initialAmount: v.amount,
        };
    });
    request({ url: "/api/CashFlowStatement/Initial", method: "post", data })
        .then((res: IResponseModel<boolean>) => {
            isSaving = false;
            if (res.state === 1000 && res.data) {
                ElNotify({ message: "保存成功", type: "success" });
                visible.value = false;
                emit("loadTableData");
            }
        })
        .catch(() => {
            isSaving = false;
            ElNotify({ message: "保存失败", type: "warning" });
        });
}

const importDialogDisplay = ref(false);
function handleImport() {
    importDialogDisplay.value = true;
}
function handleDownloadTemplate() {
    globalExport("/api/CashFlowStatement/Initial/ExportTemplate");
}
function uploadSuccess(res: IResponseModel<Array<ITableData>>) {
    importDialogDisplay.value = false;
    if (res.state !== 1000) {
        ElNotify({ message: res.msg, type: "warning" });
        return;
    }
    ElNotify({ message: "导入成功", type: "success" });
    tableData.value.forEach((item) => {
        item.amount = 0;
    });
    for (let i = 0; i < res.data.length; i++) {
        const diff = res.data[i].initialAmount || 0;
        if (diff === 0) continue;
        calculator(res.data[i].lineID, diff);
    }
    changed = true;
}

const isCheckOut = ref(false);
function handleInit() {
    const stardPeridInfo = useAccountPeriodStore().periodList[0];
    isCheckOut.value = PeriodStatus.CheckOut === stardPeridInfo.status;
    const asStartDate = useAccountSetStore().accountSet!.asStartDate;
    const [year, month] = asStartDate.split("-").map((v) => parseInt(v, 10));
    const monthStr = month === 2 ? "1月累计金额" : 1 + "月至" + (month - 1) + "月累计金额";
    thirdLabel.value = year + "年" + monthStr;
}

const visible = ref(false);
async function openDialog() {
    if (!init) return;
    handleInit();
    await handleLoadTableData();
    visible.value = true;
}

defineExpose({ openDialog });

function assertNameClass(row: ITableData) {
    return hasNumberTitle(row.lineName) ? "level1" : "level2";
}

function handleReset() {
    tableData.value.length = 0;
    cacheTableData.length = 0;
    changed = false;
}
interface IFormula {
    asid: number;
    statementID: number;
    lineId: number;
    equationType: number;
    source: number;
    asubid: number;
    asubname: string;
    asubtype: number;
    derection: number;
    operator: number;
    valueType: number;
    multiplication: number;
    rate: string;
    flag: number;
    equationID: number;
    equationSn: number;
    columnType: number;
}
interface IFormulas {
    [key: string]: Array<IFormula>;
}
const formulas = ref<IFormulas>({});
let init = false;
async function handleGetFormulas() {
    await request({ url: "/api/CashFlowStatement/Initial/Formulas" })
        .then((res: IResponseModel<IFormulas>) => {
            res.state === 1000 && (formulas.value = res.data);
        })
        .finally(() => {
            init = true;
        });
}
onBeforeMount(() => {
    handleGetFormulas();
});
</script>

<style scoped lang="less">
@import "@/style/Statements/Statements.less";
.initial-dialog-content {
    .initial-dialog-top {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-top: 10px;
    }
    .main-center {
        padding: 10px 20px 20px;
        :deep(.el-table) {
            .cell {
                box-sizing: border-box;
                padding: 2px 4px;
                & > span {
                    padding-right: 6px;
                }
            }
            input.cash-amount-input {
                display: block;
                width: 97% !important;
                border: 1px solid var(--el-border-color);
                height: 30px;
                line-height: 30px;
                border-radius: 2px;
                box-sizing: border-box;
                text-align: right;
                outline: none;
                appearance: none;
                &:hover {
                    border: 1px solid var(--el-border-color-hover);
                }
                &:focus {
                    border: 1px solid var(--el-color-primary);
                }
            }
        }
    }
}
</style>
