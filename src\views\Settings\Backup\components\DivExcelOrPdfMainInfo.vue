<template>
    <div class="main-top main-tool-bar">
        <a class="button solid-button large-1" @click="processExcelOrPdf">开始归档</a>
    </div>
    <div class="main-center">
        <Table
            empty-text="暂无数据"
            :data="divExcelOrPdfMainInfoData"
            :columns="divExcelMainInfoOrPdfColumns()"
            :rowClassName="judgeRowClassName"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="195" align="left" header-align="center" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.progress == 100">
                            <a class="link" v-permission="['backup-candelete']" @click="deleteBackupItem(scope.row.fileName, type)">
                                删除
                            </a>
                            <a class="link" v-permission="['backup-canview']" v-if="scope.row.expiredDate">
                                <el-popover
                                    placement="bottom-end"
                                    :width="300"
                                    trigger="hover"
                                    popper-class="tip-popover"
                                    :popper-style="popperStyle"
                                >
                                    <template #reference>
                                        <span>
                                            <span @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)">下载</span>
                                            <img
                                                src="@/assets/Settings/warnning-orange.png"
                                                alt=""
                                                style="width: 15px; vertical-align: top; margin-left: 3px; margin-top: 1px"
                                            />
                                        </span>
                                    </template>
                                    空间不足！已为您提供额外存储空间备份， <br />
                                    请于72小时内下载到本地，超时将自动删除
                                </el-popover>
                            </a>
                            <a
                                class="link"
                                v-permission="['backup-canview']"
                                @click="downloadbackupitem(scope.row.fileName, scope.row.fileType)"
                                v-else
                            >
                                <span>下载</span>
                            </a>
                        </span>
                        <span v-show="scope.row.progress !== 100">
                            <span v-show="scope.row.progress !== 0" style="display: flex; align-items: center">
                                <span class="progress-bar">
                                    <span class="progress-solid-bar" :style="{ width: scope.row.progress + '%' }"></span>
                                </span>
                                <span>{{ scope.row.progress + "%" }}</span>
                            </span>
                            <span class="loading-operator" v-show="scope.row.progress === 0">
                                <span class="loading-icon"></span>
                                <span class="link" style="margin-left: 3px">数据加载中...</span>
                            </span>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { divExcelMainInfoOrPdfColumns, judgeRowClassName, popperStyle } from "../utils";
import { getUrlSearchParams } from "@/util/url";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { request, type IResponseModel } from "@/util/service";

import type { ITableData, ICheckSpace, IProcessBack } from "../types";

import Table from "@/components/Table/index.vue";
import { ElNotify } from "@/util/notify";
import { useLoading } from "@/hooks/useLoading";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { checkCanProcessAndHasTemporaryFile } from "../utils";

const setModule = "DivExcelOrPdf";
const emit = defineEmits<{
    (e: "deleteBackupItem", fileName: string, type: "pdf" | "excel"): void;
    (e: "downloadbackupitem", fileName: string, fileType: number): void;
    (e: "insertRow", row: IProcessBack, type: "pdf" | "excel"): void;
}>();

const props = defineProps<{
    tableData: ITableData[];
    type: "pdf" | "excel";
    showBackupTipDialog: () => void;
    showSpaceDialog: (usedSpace: number, totalSpace: number) => void;
}>();
const divExcelOrPdfMainInfoData = computed(() => props.tableData);
const type = computed(() => props.type);

const deleteBackupItem = (fileName: string, type: "pdf" | "excel") => emit("deleteBackupItem", fileName, type);
const downloadbackupitem = (fileName: string, fileType: number) => emit("downloadbackupitem", fileName, fileType);
const processExcelOrPdf = () => {
    const fileType = type.value;
    const confirmMsg = "您已有临时的备份归档文件，继续备份归档将自动删除之前的临时文件，是否继续？";
    const msg = window.isAccountingAgent
        ? "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
        : "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：";
    checkCanProcessAndHasTemporaryFile(
        fileType,
        confirmMsg,
        go,
        () => {
            request({ url: "/api/Backup/CheckSpace?fileType=" + fileType, method: "post" }).then((r: IResponseModel<ICheckSpace>) => {
                if (r.state === 1000) {
                    if (r.data.overflow) {
                        useLoading().quitLoading();
                        if (useThirdPartInfoStoreHook().isThirdPart) {
                            props.showSpaceDialog(r.data.state.usedSpace, r.data.state.totalSpace);
                        } else {
                            tryShowPayDialog(1, "backup-" + fileType, msg, "会计电子档案", go);
                            return;
                        }
                    }
                }
                go();
            });
        },
        props.showBackupTipDialog
    );
};
const go = () => {
    useLoading().enterLoading("备份进行中，请稍候...");
    const fileType = type.value;
    const balanceSheetIsClass = window.localStorage.getItem("classificationSwitch") || "";
    const cashFlowSheetIsClass = window.localStorage.getItem("cashclassificationSwitch") || "";
    const urlPath = fileType === "pdf" ? "ProcessPdfBackup?" : "ProcessExcelBackup?";
    const params = {
        allowTemporaryFile: 1,
        isProcess: 1,
        balanceSheetIsClass,
        cashFlowSheetIsClass,
    };
    const url = "/api/Backup/" + urlPath + getUrlSearchParams(params);
    request({ url, method: "post" })
        .then((res: IResponseModel<IProcessBack>) => {
            useLoading().quitLoading();
            if (res.state !== 1000) {
                if (res.msg) {
                    ElNotify({ type: "warning", message: res.msg });
                    return;
                }
                ElNotify({ type: "warning", message: "备份操作出错，请稍后重试" });
                return;
            } else {
                const row = res.data;
                emit("insertRow", row, type.value);
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "备份操作出错，请稍后重试" });
        });
};
</script>

<style lang="less" scoped>
@import "@/style/Settings/Backup.less";
</style>
