import { formatMoney } from "@/util/format";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const GetNumber = (number: string | number) => (number == "" || Number(number) == 0 ? "" : formatMoney(number));
export const getshortdata = (data: any) => {
    if (data == null || data == undefined || data == "") {
        return "";
    }
    const time = data.match(/(\d{4}).(\d{1,2}).(\d{1,2})/);
    const dt = new Date(time[1], parseInt(time[2], 10) - 1, time[3]);
    return dt.getFullYear() + "-" + (dt.getMonth() + 1 + "").padStart(2, "0") + "-" + (dt.getDate() + "").padStart(2, "0");
};

export const setColumns = (showAll: boolean, CDJournal = true, standard = false, setModule: string, isFc = false) => {
    let columns: IColumnProps[] = [];
    if (showAll) {
        columns = [
            {
                label: "日期",
                prop: "cd_date",
                minWidth: 130,
                width: getColumnWidth(setModule, 'cd_date'),
                align: "left",
                headerAlign: "left",
                formatter: function (row: any, column: any, value: string) {
                    return value == "" ? "" : getshortdata(value);
                },
            },
            { label: "摘要", prop: "description", align: "left", headerAlign: "left", minWidth: 220, width: getColumnWidth(setModule, 'description') },
            {
                label: CDJournal ? "收支类别" : "账户名称",
                prop: CDJournal ? "ie_type_name" : "ac_name",
                align: "left",
                headerAlign: "left",
                minWidth: 100,
                width: getColumnWidth(setModule, CDJournal ? "ie_type_name" : "ac_name"),
            },
            { slot: "fcName"},
            { slot: "income" },
            { slot: "expenditure" },
            { slot: "amount" },
            { label: "往来单位", prop: "opposite_party", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "opposite_party") },
            { label: "项目", prop: "project_name", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "project_name") },
            { label: "部门", prop: "department_name", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "department_name") },
            { label: "结算方式", prop: "payment_method_name", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "payment_method_name") },
            {
                label: "票据号",
                prop: "receipt_no",
                align: "left",
                headerAlign: "left",
                minWidth: 100,
                width: getColumnWidth(setModule, "receipt_no"),
                formatter: function (row: any, column: any, value: any) {
                    return value == 0 ? "" : value;
                },
            },
            { label: "备注", prop: "note", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "note") },
            { slot: "lineSn" },
        ];
    } else {
        columns = [
            {
                label: "日期",
                prop: "cd_date",
                align: "left",
                headerAlign: "left",
                minWidth: 130,
                width: getColumnWidth(setModule, 'cd_date'),
                formatter: function (row: any, column: any, value: string) {
                    return value == "" ? "" : getshortdata(value);
                },
            },
            { label: "摘要", prop: "description", align: "left", headerAlign: "left", minWidth: 220, width: getColumnWidth(setModule, 'description') },
            {
                label: CDJournal ? "收支类别" : "账户名称",
                prop: CDJournal ? "ie_type_name" : "ac_name",
                align: "left",
                headerAlign: "left",
                minWidth: 100,
                width: getColumnWidth(setModule, CDJournal ? "ie_type_name" : "ac_name"),
            },
            { slot: "fcName"},
            { slot: "income" },
            { slot: "expenditure" },
            { slot: "amount" },
            { slot: "lineSn" },
        ];
    }
    const amountIndex = columns.findIndex((item) => item.slot === "amount");
    if (amountIndex > -1 && !CDJournal) columns.splice(amountIndex, 1);
    const fcIndex = columns.findIndex((item) => item.slot === "fcName");
    if (fcIndex > -1 && !isFc) columns.splice(fcIndex, 1);
    if (isFc) {
        columns.forEach((item) => {
            if (item.slot === "income" || item.slot === "expenditure" || item.slot === "amount") {
                item.slot += "Fc";
            }
        });
    }
    return columns;
};
