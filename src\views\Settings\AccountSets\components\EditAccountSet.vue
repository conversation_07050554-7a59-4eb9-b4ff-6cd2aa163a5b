<template>
    <div>
        <div class="edit-accountSet">
            <div class="edit-accountSet-title">
                {{ activeName == "first" ? "基本信息" : activeName == "second" ? "功能参数" : "税务信息" }}
            </div>
            <el-tabs v-model="activeName" class="demo-tabs">
                <el-tab-pane label="基本信息" name="first">
                    <div class="accountset-edit-content" style="padding-top: 20px">
                        <div class="line-item1">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <el-autocomplete
                                        ref="asNameRef"
                                        v-model="editData.asName"
                                        :fetch-suggestions="queryAsNameSearch"
                                        :trigger-on-focus="false"
                                        class="inline-input w-50"
                                        placeholder="请输入账套名称"
                                        style="width: 238px"
                                        @select="selectAsName"
                                        @input="handleAutoCompleteInput"
                                    >
                                        <template #default="{ item }">
                                            <div class="value">{{ item.value }}</div>
                                        </template>
                                    </el-autocomplete>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>账套名称：</div>
                            </div>
                        </div>
                        <div class="line-item1">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <div class="jqtransform" style="display: flex">
                                        <div @click="checkChangeStartDate">
                                            <el-select
                                                style="width: 99px"
                                                :teleported="false"
                                                :fit-input-width="true"
                                                v-model="yearNumber"
                                                :disabled="!editData.isEditSartDate"
                                                :class="!editData.isEditSartDate ? 'pointer-click' : ''"
                                            >
                                                <el-option
                                                    v-for="item in yearList"
                                                    :value="item"
                                                    :label="(item as number)"
                                                    :key="(item as number)"
                                                ></el-option>
                                            </el-select>
                                        </div>
                                        <span style="margin: 0 10px">年</span>
                                        <div @click="checkChangeStartDate">
                                            <el-select
                                                style="width: 99px"
                                                :teleported="false"
                                                :fit-input-width="true"
                                                v-model="monthNumber"
                                                :class="!editData.isEditSartDate ? 'pointer-click' : ''"
                                                :disabled="!editData.isEditSartDate"
                                            >
                                                <el-option
                                                    v-for="item in monthList"
                                                    :value="item"
                                                    :label="(item as number)"
                                                    :key="(item as number)"
                                                ></el-option>
                                            </el-select>
                                        </div>
                                        <span style="margin: 0 10px">月</span>
                                        <el-popover title="账套启用年月说明" placement="right" :width="200" trigger="hover">
                                            <template #reference>
                                                <div class="image-container"></div>
                                            </template>
                                            <template #default>
                                                有凭证、固定资产、资金、工资数据或存在结账期间，无法再修改账套启用年月。
                                            </template>
                                        </el-popover>
                                    </div>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>账套启用年月：</div>
                            </div>
                        </div>
                        <div class="line-item1 line-item-large clearfix">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <span id="spanAccountStandardForEdit">{{
                                        GetAccountStandardText(editData.accountingStandard, editData.subAccountingStandard)
                                    }}</span>
                                    <span
                                        id="divUpgrade"
                                        style="margin-left: 10px; float: none"
                                        v-if="
                                            !isAccountingAgent &&
                                            (editData.accountingStandard === 1 ||
                                                editData.accountingStandard === 2 ||
                                                editData.accountingStandard === 4)
                                        "
                                    >
                                        <a class="link" style="line-height: 32px" @click="ShowAccountSetUpgrade"
                                            >{{ editData.accountingStandard === 4 ? "切换2023年新准则" : "切换准则" }}
                                        </a>
                                        <div class="image-container inline-block" @click="() => (showAccountingStandardTip = true)"></div>
                                    </span>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>会计准则：</div>
                            </div>
                        </div>
                        <div class="line-item1">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <div class="jqtransform">
                                        <el-select
                                            v-model="editData.asIndustry"
                                            style="width: 262px"
                                            :teleported="false"
                                            :fit-input-width="true"
                                        >
                                            <el-option label="IT·通信·电子·互联网" :value="1010" />
                                            <el-option label="金融业" :value="1020" />
                                            <el-option label="房地产·建筑业" :value="1030" />
                                            <el-option label="商业服务" :value="1040" />
                                            <el-option label="贸易·批发·零售·租赁业" :value="1050" />
                                            <el-option label="文体教育·工艺美术" :value="1060" />
                                            <el-option label="生产·加工·制造" :value="1070" />
                                            <el-option label="交通·运输·物流·仓储" :value="1080" />
                                            <el-option label="服务业" :value="1090" />
                                            <el-option label="文化·传媒·娱乐·体育" :value="1100" />
                                            <el-option label="能源·矿产·环保" :value="1110" />
                                            <el-option label="政府·非盈利机构" :value="1120" />
                                            <el-option label="农·林·牧·渔·其他" :value="1130" />
                                            <el-option label="请选择" :value="0" />
                                        </el-select>
                                    </div>
                                </div>
                                <div class="line-item-title" style="width: 103px">行业：</div>
                            </div>
                        </div>
                        <div class="line-item2">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <el-radio-group v-model="editData.taxType">
                                        <el-radio :label="1" size="large">小规模纳税人</el-radio>
                                        <el-radio :label="2" size="large" style="margin-left: 28px">一般纳税人</el-radio>
                                    </el-radio-group>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>增值税种类：</div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="功能参数" name="second">
                    <div class="accountset-edit-content" style="padding-top: 20px">
                        <div title="模块设置" class="line-item2 top-title" style="font-size: 16px; font-weight: bold; margin-right: 300px">
                            模块/菜单设置
                        </div>
                        <div class="line-item2">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <el-radio-group v-model="editData.fixedasset" @change="changeFixedAsset" :disabled="disRadioFixedAsset">
                                        <el-radio :label="1" size="large"
                                            >启用{{ editData.fixedasset === 0 ? "" : editData.periodDisplayText }}
                                        </el-radio>
                                        <el-radio :label="0" size="large" style="margin-left: 80px">不启用</el-radio>
                                    </el-radio-group>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>资产模块：</div>
                            </div>
                        </div>
                        <div class="line-item2">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <el-radio-group v-model="editData.cashJournal">
                                        <el-radio :label="1" size="large">启用</el-radio>
                                        <el-radio :label="0" size="large" style="margin-left: 80px">不启用</el-radio>
                                    </el-radio-group>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>资金模块：</div>
                            </div>
                        </div>
                        <div class="line-item2" v-if="!isAccountingAgent && editData.accountingStandard !== 6 && editData.accountingStandard !== 7">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <el-radio-group v-model="editData.showScm">
                                        <el-radio :label="1" size="large">显示</el-radio>

                                        <el-radio :label="0" size="large" style="margin-left: 80px">不显示</el-radio>
                                    </el-radio-group>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>前往进销存：</div>
                            </div>
                        </div>
                        <div class="scmTip" v-if="!isAccountingAgent && editData.accountingStandard !== 6 && editData.accountingStandard !== 7">
                            <p class="tips">
                            <img src="@/assets/Settings/tips.png" alt="" />
                            云进销存和云财务是两个独立的系统，显示【前往进销存】，可快捷进入进销存系统哦~
                            </p>
                        </div>
                        <div style="border: 1px dashed #f1f1f1; max-width: 100%; margin-top: 28px; margin-bottom: 8px"></div>
                        <div title="系统参数" class="line-item2 top-title" style="font-size: 16px; font-weight: bold; margin-right: 300px">
                            系统参数
                        </div>
                        <div class="line-item2 divShowScm dingtalk-hidden">
                            <div class="line-item-single">
                                <div class="line-item-field">
                                    <el-radio-group v-model="editData.checkNeeded" @change="checkNeededHandle">
                                        <el-radio :label="1" size="large">审核</el-radio>
                                        <el-radio :label="0" size="large" style="margin-left: 80px">不审核</el-radio>
                                    </el-radio-group>
                                </div>
                                <div class="line-item-title"><span class="highlight-red">*</span>凭证审核：</div>
                            </div>
                        </div>
                        <div class="line-item2 divShowScm dingtalk-hidden">
                            <div class="line-item-single">
                                <div style="margin-left: 100px">
                                    资产负债表辅助核算余额取值方式：
                                    <el-popover placement="right" :width="350" trigger="hover">
                                        <template #reference>
                                            <div class="image-container" style="display: inline-block; margin-left: -10px"></div>
                                        </template>
                                        <template #default>
                                            此选项为设置资产负债表选择【辅助核算借/贷方余额】公式中辅助核算的取数方式，
                                            <a class="link" @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/commonPro?subMenuId=100172100&answerId=922')">
                                                点击详情
                                            </a>
                                            查看两种取数方式区别
                                        </template>
                                    </el-popover>
                                </div>
                            </div>
                        </div>
                        <div class="line-item2 divShowScm dingtalk-hidden" style="margin: 0 auto;height:30px">
                            <div class="line-item-single" style="width: 800px">
                                <div style="margin-left: 235px">
                                    <el-radio-group v-model="allAACalcStatementTemp" @change="changeAllAACalcStatement">
                                        <el-radio :label="0" size="large">仅供应商/客户辅助核算余额参与计算</el-radio>
                                        <el-radio :label="1" size="large">所有辅助核算余额参与计算</el-radio>
                                    </el-radio-group>
                                </div>
                            </div>
                        </div>
                        <div class="line-item2 divShowScm dingtalk-hidden" style="height: 32px">
                            <div class="line-item-single"  style="width: 720px">
                                <div class="line-item-field">
                                    <el-select
                                        v-model="editData.decimalPlace"
                                        style="width: 262px"
                                        :teleported="false"
                                        :fit-input-width="true"
                                    >
                                        <el-option label="0" :value="0" />
                                        <el-option label="1" :value="1" />
                                        <el-option label="2" :value="2" />
                                        <el-option label="3" :value="3" />
                                        <el-option label="4" :value="4" />
                                        <el-option label="5" :value="5" />
                                        <el-option label="6" :value="6" />
                                        <el-option label="7" :value="7" />
                                        <el-option label="8" :value="8" />
                                    </el-select>
                                    <div class="tip-question">
                                        <div class="image-container"></div>
                                        <div id="decimalPlaceTip" class="float-digit-tip">
                                            此设置为显示小数位，可随时调整。系统按照此处设置的小数位显示，按照8位小数计算。例如：单价为2.3334812，设置为4位小数后显示为2.3334
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item-title" style="margin-top: 5px;width: 130px;white-space:nowrap;">单价/数量/汇率小数：</div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="税务信息" name="third">
                    <div class="accountset-edit-content tax-info-content">
                        <div class="tax-info-tip" v-show="showTaxInfoTip">
                            <i class="remark-warnning"></i
                            >温馨提示：为了能正常使用一键取票、一键报税、一键登录电子税局等功能，请您及时补充税务信息
                            <a class="remark-close" @click="closeTaxInfoTip" style="cursor: pointer" alt="关闭">×</a>
                        </div>
                        <div class="tax-info-block">
                            <div class="tax-info-coltitle">
                                <div class="tax-info-col-left">纳税人信息</div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>公司名称：</div>
                                <div class="tax-info-col-right">
                                    <Tooltip 
                                        :content="
                                        isEisRelation
                                            ? `${editData.taxpayerName}已关联云发票，不可修改；如需修改，请先取消关联云发票企业！`
                                            : editData.taxpayerName
                                        "
                                        :alwaysShow="isEisRelation"
                                        placement="top-start" 
                                        :isInput="true">
                                        <el-autocomplete
                                            :teleported="false"
                                            :fit-input-width="true"
                                            ref="asNameRef"
                                            v-model="editData.taxpayerName"
                                            :prop="[{ required: true, message: '亲，公司名称不能为空', trigger: ['blur', 'change'] }]"
                                            :fetch-suggestions="querySearch"
                                            class="inline-input w-50"
                                            placeholder="请输入完整的公司名称"
                                            style="width: 238px"
                                            @select="selectName"
                                            :disabled="isEisRelation"
                                            >
                                            <template v-slot:default="{ item }">
                                                <Tooltip :content="item.value" :line-clamp="2" placement="right" :maxWidth='400' :teleported="true">
                                                <div style="line-height:18px">{{ item.value }}</div>
                                                </Tooltip>
                                            </template>
                                        </el-autocomplete>
                                    </Tooltip>
                                </div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>统一社会信用代码：</div>
                                <div class="tax-info-col-right">
                                    <el-tooltip
                                        :disabled="!isEisRelation"
                                        effect="light"
                                        content="已关联云发票，不可修改；如需修改，请先取消关联云发票企业！"
                                        placement="top-start">
                                        <el-input
                                            v-model="editData.taxNumberS"
                                            style="width: 238px"
                                            placeholder="请输入统一社会信用代码"
                                            :disabled="isEisRelation"
                                        />
                                    </el-tooltip>
                                </div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>报税地区：</div>
                                <div class="tax-info-col-right">
                                    <el-tooltip
                                        :disabled="!isEisRelation"
                                        effect="light"
                                        content="已关联云发票，不可修改；如需修改，请先取消关联云发票企业！"
                                        placement="right-start">
                                        <el-select
                                            v-model="editData.taxadId"
                                            @change="taxAreaChange"
                                            style="width: 238px"
                                            :disabled="isEisRelation"
                                            filterable
                                            teleported
                                            fit-input-width
                                            :filter-method="areaFilterMethod">
                                        <el-option
                                            v-for="item in showAreaList"
                                            :value="item.taxAreaId"
                                            :label="item.taxAreaName"
                                            :key="item.taxAreaId"></el-option>
                                        </el-select>
                                    </el-tooltip>
                                   
                                </div>
                            </div>
                        </div>
                        <div class="tax-info-block" v-show="editData.taxadId">
                            <div class="tax-info-coltitle">
                                <div class="tax-info-col-left">税局登录</div>
                                <div class="tax-info-col-right">用于一键取票和企税申报，请准确填写</div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>登录方式：</div>
                                <div class="tax-info-col-right">
                                    <el-select
                                        v-model="editData.taxBureauLoginType"
                                        @change="loginTypeChange"
                                        style="width: 238px"
                                        :teleported="true"
                                        :fit-input-width="true"
                                    >
                                        <!-- 暂时只支持新版登录方式 -->
                                        <!-- <el-option :value="9" :label="'新版账号密码登录'" :key="9"></el-option> -->
                                        <el-option 
                                            v-for="item in taxBureauLoginListOption" 
                                            :value="item.value" 
                                            :label="item.label" 
                                            :key="item.value"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('TaxAccount')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>登录账号：</div>
                                <div class="tax-info-col-right">
                                    <el-input v-model="editData.taxBureauAccount" style="width: 238px" placeholder="请输入税局登录账号" />
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('TaxPassword')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>登录密码：</div>
                                <div class="tax-info-col-right">
                                    <HidePwdInput
                                        v-model:inputValue="editData.taxpayerPassword"
                                        :placeholder="'请输入税局登录密码'"
                                    ></HidePwdInput>
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('PersonPositionType')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>身份类型：</div>
                                <div class="tax-info-col-right">
                                    <el-select
                                        v-model="editData.taxBureauPersonPositionType"
                                        style="width: 238px"
                                        :teleported="true"
                                        :fit-input-width="true"
                                    >
                                        <el-option :value="0" :label="'请选择办税人员身份类型'" :key="0"></el-option>
                                        <el-option
                                            :value="10"
                                            :label="TaxBureauLoginTable.PersonPositionTypeEnum[10].Description"
                                            :key="10"
                                        ></el-option>
                                        <el-option
                                            :value="20"
                                            :label="TaxBureauLoginTable.PersonPositionTypeEnum[20].Description"
                                            :key="20"
                                        ></el-option>
                                        <el-option
                                            :value="30"
                                            :label="TaxBureauLoginTable.PersonPositionTypeEnum[30].Description"
                                            :key="30"
                                        ></el-option>
                                        <el-option
                                            :value="110"
                                            :label="TaxBureauLoginTable.PersonPositionTypeEnum[110].Description"
                                            :key="110"
                                        ></el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('AgentTaxNumber')">
                                <div class="tax-info-col-left">
                                    <span class="tax-highlight-red">*</span>代理机构税号：
                                </div>
                                <div class="tax-info-col-right">
                                    <el-input v-model="editData.taxBureauAgentTaxNumber" style="width: 238px" placeholder="请输入代理机构统一社会信用代码" />
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('PersonIdentityNo')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>证件号码：</div>
                                <div class="tax-info-col-right">
                                    <el-input
                                        v-model="editData.taxBureauPersonIdentityNo"
                                        style="width: 238px"
                                        placeholder="请输入办税人证件号码"
                                    />
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('PersonAccount')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>个人用户账户：</div>
                                <div class="tax-info-col-right">
                                    <el-input
                                        v-model="editData.taxBureauPersonAccount"
                                        style="width: 238px"
                                        placeholder="请输入身份证号码/手机号码/用户名"
                                    />
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('PersonPassword')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>个人用户密码：</div>
                                <div class="tax-info-col-right">
                                    <HidePwdInput
                                        v-model:inputValue="editData.taxBureauPersonPassword"
                                        :placeholder="'请输入个人用户密码'"
                                    ></HidePwdInput>
                                </div>
                            </div>
                            <div class="tax-info-col" v-show="IsTaxBureauNeed('PersonPhone')">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>办税人手机号：</div>
                                <div class="tax-info-col-right">
                                    <el-input
                                        v-model="editData.taxBureauPersonPhone"
                                        style="width: 238px"
                                        placeholder="请输入办税人手机号"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="tax-info-block">
                            <div class="tax-info-coltitle">
                                <div class="tax-info-col-left">个税申报</div>
                                <div class="tax-info-col-right">用于自然人客户端中的个税申报</div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left">登录方式：</div>
                                <div class="tax-info-col-right">
                                    <el-radio-group v-model="taxDeclareLoginType">
                                        <el-radio :label="1" size="default">申报密码登录</el-radio>
                                    </el-radio-group>
                                </div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>个税申报人员姓名：</div>
                                <div class="tax-info-col-right">
                                    <el-input
                                        v-model="editData.taxDeclareEmployeeName"
                                        style="width: 238px"
                                        placeholder="请输入个税申报人员姓名"
                                    />
                                </div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>个税申报人员手机：</div>
                                <div class="tax-info-col-right">
                                    <el-input
                                        v-model="editData.taxDeclareEmployeePhone"
                                        style="width: 238px"
                                        placeholder="请输入个税申报人员手机"
                                    />
                                </div>
                            </div>
                            <div class="tax-info-col">
                                <div class="tax-info-col-left"><span class="tax-highlight-red">*</span>个税申报密码：</div>
                                <div class="tax-info-col-right">
                                    <HidePwdInput
                                        v-model:inputValue="editData.taxDeclarePassword"
                                        :placeholder="'请输入个税申报登录密码'"
                                    ></HidePwdInput>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <div class="buttons">
                <a
                    class="button solid-button"
                    id="saveASForEdit"
                    @click="submitAccountSetForEdit"
                    style="width: 102px; height: 32px; border-radius: 4px; line-height: 32px; margin: 0 5px"
                    >保存账套</a
                >
                <a
                    class="button"
                    tabindex="20"
                    @click="successEdit"
                    style="width: 102px; height: 32px; border-radius: 4px; line-height: 32px; margin: 0 5px"
                    >取消</a
                >
            </div>
            <span class="edit-tips">{{
                isProSystem ? "云财务专业版，支持随时修改账套信息" : (isHideBarcode ? "" : "柠檬云") + "不限账套，支持随时修改账套信息"
            }}</span>
        </div>
        <el-dialog v-model="upgradeStandard" title="切换准则" center width="461px" modal-class="modal-class" class="dialogDrag">
            <div class="accountSetUpgrade" v-dialogDrag>
                <div class="select-block">
                    切换到：
                    <div class="jqtransform jqtransformdone upgradeStandard" style="display: inline-block">
                        <el-select v-model="accountStandardForUpgrade" :teleported="false" style="width: 352px" :fit-input-width="true">
                            <template #prefix>
                                <span v-html="currentStandard(accountStandardForUpgrade)" class="upgradeStandard-current"></span>
                            </template>
                            <el-option v-for="(item, key) in accountingStandardList" :key="key" :value="item.value">
                                <template #default>
                                    <span v-html="formatStandard(item.text)"></span>
                                </template>
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="info-block">
                    <div>切换准则后，将自动复制原账套，并将本账套切换为新准则</div>
                    <div class="color-green" id="warnForNormal" v-show="warnForNormal">
                        <div class="round-dot"></div>
                        不会影响您的会计科目、凭证、账簿等数据
                    </div>
                    <div class="color-green" id="warnForNormalFor2019Excute" v-show="warnForNormalFor2019Excute">
                        <div>
                            <div class="round-dot"></div>
                            不会影响您凭证、账簿等数据
                        </div>
                        <div>
                            <div class="round-dot"></div>
                            会给您新增几个新准则的科目
                        </div>
                    </div>
                    <div class="color-red" id="warnForLittle" v-show="warnForLittle">
                        <div class="round-dot"></div>
                        会影响您的会计科目、凭证、账簿等数据
                    </div>
                    <div class="color-red">
                        <div class="round-dot"></div>
                        会影响您的会计报表，请谨慎操作
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="AccountSetUpgrade">确定</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="upgradeStandardFarmerCooperative" title="切换准则" center width="660px" modal-class="modal-class" class="dialogDrag">
            <div class="accountset-upgrade-farmerCooperative" v-dialogDrag>
                <div class="content-block">
                    切换到：
                    <div class="standard-farmerCooperative">农民合作社会计制度（2023年）</div>
                </div>
                <div class="content-block space-between">
                    切换准则后，将保留本账套，并按新准则新增一个新账套
                    <a class="link" @click="downloadFarmerCooperativeUpgradeAsubMap">查看对照关系</a>
                </div>
                <div
                    class="des-card"
                    :class="{ selected: !farmerCooperativeIsCopyData }"
                    :data-value="false"
                    @click="farmerCooperativeIsCopyData = false"
                >
                    <div class="card-title">新账套按新旧科目对照关系，结转科目期初并保留基础资料</div>
                    <div class="card-tip">
                        <span style="margin-right: 5px">① 新账套的启用日期为</span>
                        <el-select style="width: 99px" :teleported="false" :fit-input-width="true" v-model="farmerCooperativeStartYear">
                            <el-option v-for="(item, key) in farmerCooperativeYearList" :value="key" :label="key" :key="key"></el-option>
                        </el-select>
                        <span style="margin: 0 5px">年</span>
                        <el-select style="width: 99px" :teleported="false" :fit-input-width="true" v-model="farmerCooperativeStartMonth">
                            <el-option
                                v-for="item in farmerCooperativeYearList[farmerCooperativeStartYear]"
                                :value="item"
                                :label="item"
                                :key="item"
                            ></el-option>
                        </el-select>
                        <span style="margin: 0 5px">月</span>
                    </div>
                    <div class="card-tip">
                        ② 将本账套{{ farmerCooperativeStartMonth === 1 ? farmerCooperativeStartYear - 1 : farmerCooperativeStartYear }}年{{
                            farmerCooperativeStartMonth === 1 ? 12 : farmerCooperativeStartMonth - 1
                        }}月的科目余额结转为新账套{{ farmerCooperativeStartYear }}年{{ farmerCooperativeStartMonth }}月的科目期初余额
                    </div>
                    <div class="card-tip">③ 本账套中辅助核算、银行账户、员工等基础资料将自动带入</div>
                </div>
                <div
                    class="des-card"
                    :class="{ selected: farmerCooperativeIsCopyData }"
                    :data-data="true"
                    @click="farmerCooperativeIsCopyData = true"
                >
                    <div class="card-title">新账套按新旧科目对照关系，结转科目期初、保留基础资料并复制凭证</div>
                    <div class="card-tip">
                        <span style="margin-right: 5px">① 新账套的启用日期为</span>
                        <el-select style="width: 99px" :teleported="false" :fit-input-width="true" v-model="farmerCooperativeStartYearCopy">
                            <el-option v-for="(item, key) in farmerCooperativeYearList" :value="key" :label="key" :key="key"></el-option>
                        </el-select>
                        <span style="margin: 0 5px">年</span>
                        <el-select
                            style="width: 99px"
                            :teleported="false"
                            :fit-input-width="true"
                            v-model="farmerCooperativeStartMonthCopy"
                        >
                            <el-option
                                v-for="item in farmerCooperativeYearList[farmerCooperativeStartYearCopy]"
                                :value="item"
                                :label="item"
                                :key="item"
                            ></el-option>
                        </el-select>
                        <span style="margin: 0 5px">月</span>
                    </div>
                    <div class="card-tip">
                        ② 将本账套{{
                            farmerCooperativeStartMonthCopy === 1 ? farmerCooperativeStartYearCopy - 1 : farmerCooperativeStartYearCopy
                        }}年{{ farmerCooperativeStartMonthCopy === 1 ? 12 : farmerCooperativeStartMonthCopy - 1 }}月的科目余额结转为新账套{{
                            farmerCooperativeStartYearCopy
                        }}年{{ farmerCooperativeStartMonthCopy }}月的科目期初余额
                    </div>
                    <div class="card-tip">
                        ③ 本账套中基础资料和{{ farmerCooperativeStartYearCopy }}年{{
                            farmerCooperativeStartMonthCopy
                        }}月及以后的凭证、日记账、发票数据将自动带入
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="farmerCooperativeAccountSetUpgrade">确定</a>
                    <a class="button ml-10" @click="upgradeStandardFarmerCooperative = false">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="changeStartDate" title="修改账套启用年月" center width="440px" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div class="box-body">
                    <div class="body-message">
                        <div class="body-message-title">
                            亲，您需要删除所有凭证、资产、资金和工资数据，并且不存在结账期间，才能修改账套启用年月。
                        </div>
                        <div class="body-message-content content-none"></div>
                    </div>
                </div>
                <div class="buttons" style="border-top: 1px solid var(--border-color)">
                    <a class="button solid-button" @click="() => (changeStartDate = false)">确定</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="showAccountingStandardTip" title="会计准则说明" center width="440px" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div class="box-body" style="padding-top: 30px; padding-bottom: 30px">
                    <div class="body-message">
                        <div class="body-message-title" style="text-align: left">亲，请根据实际情况选择对应的会计准则。</div>
                        <div class="body-message-content">
                            {{
                                isProSystem
                                    ? "① 小企业会计准则是按照2013年版 《小企业会计准则》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。"
                                    : "① 小企业会计准则是按照2013年版 《小企业会计准则》设置，为了使报表取数准确，系统不允许增加一级科目，但可以增加二级、三级和四级科目。"
                            }}
                            <br />② 企业会计准则是按照2007年版 《企业会计准则》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />③
                            民间非营利组织会计制度是按照2005年版《民间非营利组织会计制度》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />④
                            农民专业合作社财务会计制度是按照2023年版《农民专业合作社会计制度》设置，系统可以增加一级科目，也可以增加二级、三级和四级科目。
                            <br />
                            {{
                                isAccountingAgent
                                    ? ""
                                    : "⑤ 需要使用《企业会计准则(2019年未执行新金融准则、新收入准则和新租赁准则)》或《企业会计准则(2019年已执行新金融准则、新收入准则和新租赁准则)》时，可以先选择《企业会计准则》新建账套，新建后到设置—账套—编辑页面去切换准则"
                            }}
                        </div>
                    </div>
                </div>
                <div class="box-footer button-ok" style="text-align: center; padding: 10px 0">
                    <a class="button solid-button" @click="() => (showAccountingStandardTip = false)">确定</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="startFixedAsset" title="启用资产模块" center width="461px" :before-close="startFixedAssetCancel" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div style="display: inline-block; padding: 25px 46px">
                    <div class="body-message-content">
                        <div>
                            <div>
                                资产模块启用年月：
                                <div class="jqtransform jqtransformdone" style="display: inline-block">
                                    <el-select
                                        style="width: 88px"
                                        :teleported="false"
                                        placeholder="请选择"
                                        v-model="editData.editFixedStartYear"
                                    >
                                        <el-option
                                            v-for="(item, key) in fixedAssetYearList"
                                            :key="key"
                                            :value="key"
                                            :label="key"
                                        ></el-option>
                                    </el-select>
                                    <span style="margin: 0 10px">年</span>
                                    <el-select
                                        style="width: 68px"
                                        :teleported="false"
                                        v-model="editData.editFixedStartMonth"
                                        @change="changeFixedAssetMonth"
                                    >
                                        <el-option
                                            v-for="item in fixedAssetYearList[editData.editFixedStartYear]"
                                            :key="item"
                                            :value="item"
                                            :label="item"
                                        ></el-option>
                                    </el-select>
                                    <span style="margin: 0 10px">月</span>
                                </div>
                            </div>
                            <div class="reset-info-block" v-show="fixedAssetExistsVoucher">
                                <img style="width: 14px; height: 14px; margin: 0 5px" src="@/assets/Settings/tips.png" /><span
                                    >注意：您选择的期间及以后期间存在手工录入的资产凭证，为保证启用后资产数据与总账数据核对正常，建议检查并删除手工凭证</span
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="startFixedAssetConfirm" :disabled="disFixedAssetConfirm">确定</a>
                    <a class="button ml-10" @click="startFixedAssetCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            v-model="startFixedAssetExistsData"
            title="启用资产模块"
            center
            width="461px"
            :before-close="startFixedAssetExistsDataCancel"
            class="dialogDrag"
        >
            <div class="new-message-box" v-dialogDrag>
                <div style="display: inline-block; padding: 25px 46px">
                    <div class="body-message-content">
                        <div class="info-block">亲：系统检测到该账套存在历史的资产数据哦~</div>
                        <el-radio-group v-model="raStartFixedAssetExistsData">
                            <el-radio :label="1" size="default">保留历史数据，继续使用</el-radio>
                            <el-radio :label="2" size="default">删除资产历史数据，重新初始化</el-radio>
                        </el-radio-group>
                        <div v-show="raStartFixedAssetExistsData === 2">
                            <div>
                                重新初始化启用年月：
                                <div class="jqtransform jqtransformdone" style="display: inline-block">
                                    <el-select
                                        style="width: 88px"
                                        :teleported="false"
                                        placeholder="请选择"
                                        v-model="editData.editFixedStartYear"
                                    >
                                        <el-option
                                            v-for="(item, key) in fixedAssetYearList"
                                            :key="key"
                                            :value="key"
                                            :label="key"
                                        ></el-option>
                                    </el-select>
                                    <span style="margin: 0 10px">年</span>
                                    <el-select
                                        style="width: 68px"
                                        :teleported="false"
                                        v-model="editData.editFixedStartMonth"
                                        @change="changeFixedAssetMonth"
                                    >
                                        <el-option
                                            v-for="item in fixedAssetYearList[editData.editFixedStartYear]"
                                            :key="item"
                                            :value="item"
                                            :label="item"
                                        ></el-option>
                                    </el-select>
                                    <span style="margin: 0 10px">月</span>
                                </div>
                            </div>
                            <div class="reset-info-block" v-show="fixedAssetExistsVoucher">
                                <img style="width: 14px; height: 14px; margin: 0 5px" src="@/assets/Settings/tips.png" /><span
                                    >注意：您选择的期间及以后期间存在手工录入的资产凭证，为保证启用后资产数据与总账数据核对正常，建议检查并删除手工凭证</span
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="startFixedAssetExistsDataConfirm" :disabled="disFixedAssetConfirm">确定</a>
                    <a class="button ml-10" @click="startFixedAssetExistsDataCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            v-model="startFixedAssetUseBeforeData"
            title="启用资产模块"
            center
            width="498px"
            :before-close="startFixedAssetUseBeforeDataCancel"
            class="dialogDrag"
        >
            <div class="new-message-box" v-dialogDrag>
                <div style="display: inline-block">
                    <div class="body-message-content">
                        <div class="info-block" style="margin: 27px 94px 12px 94px; font-weight: 500">
                            您将保留原有资产历史数据，继续使用
                        </div>
                        <div class="reset-info-block" style="color: #666666; margin: 0 42px 25px 42px; display: flex">
                            <img
                                style="width: 14px; height: 14px; margin-top: 3px; margin-right: 5px"
                                src="@/assets/Settings/tips.png"
                            /><span
                                >注意：非首次启用资产模块，保留历史数据时，可能存在资产数据与总账数据不平的情况，您可以，反结账后进行数据修正哦~</span
                            >
                        </div>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="startFixedAssetUseBeforeDataConfirm">确定</a>
                    <a class="button ml-10" @click="startFixedAssetUseBeforeDataCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            v-model="startFixedAssetUseResetData"
            title="初始化资产"
            center
            width="498px"
            :before-close="startFixedAssetUseResetDataCancel"
            class="dialogDrag"
        >
            <div class="new-message-box" v-dialogDrag>
                <div style="display: inline-block">
                    <div class="body-message-content">
                        <div class="info-block" style="margin: 27px 84px 12px 84px">
                            您将要删除所有资产模块历史数据，重新初始化<br />
                            请谨慎操作！确定要继续吗？
                        </div>
                        <div class="reset-info-block" style="margin: 0 42px 25px 82px; color: #666666">
                            <img style="width: 14px; height: 14px; margin: 0 5px" src="@/assets/Settings/tips.png" /><span
                                >注意：删除后，所有资产数据都不能再恢复</span
                            >
                        </div>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="startFixedAssetUseResetDataConfirm">确定</a>
                    <a class="button ml-10" @click="startFixedAssetUseResetDataCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="stopFixedAsset" title="初始化资产" center width="461px" :before-close="stopFixedAssetCancel" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div style="display: inline-block; padding: 25px 46px">
                    <div style="height: 32px; line-height: 32px">亲：您将要取消启用资产模块，请谨慎选择哦~</div>
                    <div class="body-message-content">
                        <el-radio-group v-model="raStopFixedAsset">
                            <el-radio :label="0" size="default">暂不取消，继续使用资产模块</el-radio>
                            <el-radio :label="1" size="default">取消启用（取消后将隐藏）</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button" @click="stopFixedAssetConfirm" :disabled="disFixedAssetConfirm">确定</a>
                    <a class="button ml-10" @click="stopFixedAssetCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="checkNeededDialog" title="启用审核功能" center width="440px" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div class="box-body" style="padding-top: 30px; padding-bottom: 30px">
                    <div class="body-message" style="text-align: center">
                        <div class="body-message-title" style="text-align: left">您将要启用审核功能，请谨慎选择！</div>
                        <div class="body-message-content">注意：启用期间只有审核了所有凭证才能结账。</div>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button mr-10" @click="checkNeededConfirm">确定</a
                    ><a class="button" @click="checkNeededCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="allAACalcStatementDialog" title="提示" center width="600px" @closed = "changeAllAACalcStatementCancel" class="dialogDrag">
            <div class="new-message-box" v-dialogDrag>
                <div class="box-body" style="padding: 35px 60px">
                    <div class="body-message" style="text-align: center">
                        <div class="body-message-title" style="text-align: left">
                            亲，您的操作将调整报表公式设置，会导致资产负债表变化，请谨慎选择！！
                        </div>
                        <div class="body-message-content">
                            <el-radio-group v-model="recalculateBalacnceType">
                                <el-radio :label="1" size="large">修改本账套所有期间的资产负债表的辅助核算取值</el-radio>
                                <el-radio :label="2" size="large"
                                    >修改本账套最后一个年度的所有会计期间资产负债表的辅助核算取值
                                    <el-popover placement="right" :width="280" trigger="hover">
                                        <template #reference>
                                            <div class="image-container" style="display: inline-block"></div>
                                        </template>
                                        <template #default>
                                            最后一个年度为您最后一笔凭证年份。<br />
                                            如：最后一笔凭证为2024年5月则只有2024年的报表改变取数方式，其他年份不受影响。
                                        </template>
                                    </el-popover>
                                </el-radio>
                            </el-radio-group>
                            <div class="backup-tips">
                                <span>
                                    <img src="@/assets/Icons/warn.png" />
                                    {{ accountSetBackupTips }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="buttons bottom-line">
                    <a class="button solid-button mr-10" @click="changeAllAACalcStatementConfirm">确定</a
                    ><a class="button" @click="changeAllAACalcStatementCancel">取消</a>
                </div>
            </div>
        </el-dialog>
        <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
    </div>
</template>

<script setup lang="ts">
import { GetAccountStandardText } from "../utils";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { setTopLocationhref, globalExport, globalWindowOpen } from "@/util/url";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { changeAS } from "../utils";
import { ref, computed, onMounted, watch, watchEffect } from "vue";
import type { IAccountSetItem, IAccountSetUpgradeResult, IAccountingStandard, ITaxArea, IYearList, ILoginTable } from "../types";
import { reloadAccountSetList } from "@/util/accountset";
import { useLoading } from "@/hooks/useLoading";
import { AppConfirmDialog } from "@/util/appConfirm";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import HidePwdInput from "./HidePwdInput.vue";
import TaxBureauLoginTableJson from "@/assets/Invoice/LoginTable.json";
import { getCookie, setCookie } from "@/util/cookie";
import { getCompanyList } from "@/util/getCompanyList";
import type { ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyDetailApi } from "@/api/getCompanyList";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import Tooltip from "@/components/Tooltip/index.vue";
import type { IAccountSetInfo } from "@/api/accountSet";
import { getServiceId } from "@/util/proUtils";
import { dangerousOperationNext } from "@/util/autoBackup";
import { commonFilterMethod } from "@/components/Select/utils";
import { taxBureauLoginList, getTaxBureauLoginList, getUseAgentTaxApi } from "../utils";
import { useAccountSetStore } from "@/store/modules/accountSet";


const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isProSystem = ref(window.isProSystem);
const isErp = ref(window.isErp);
const warnForNormal = ref(false);
const warnForNormalFor2019Excute = ref(false);
const warnForLittle = ref(false);
const upgradeStandard = ref(false);
const upgradeStandardFarmerCooperative = ref(false);
const changeStartDate = ref(false);
const checkNeededDialog = ref(false);
const showAccountingStandardTip = ref(false);
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");


let isAccountingAgent = window.isAccountingAgent;
const accountStandardForUpgrade = ref(20);
const accountingStandard = ref(1);
const props = defineProps({
    editAsId: {
        type: Number,
        default: 0,
    },
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
    childActiveName: {
        type: String,
        default: "first",
    },
    isEisRelation: {
        type: Boolean,
        default: false,
    },
});

const activeName = computed({
    get() {
        return props.childActiveName;
    },
    set(val) {
        emits("update:childActiveName", val);
    },
});

const emits = defineEmits(["cancelEdit", "backMain", "update:editData", "update:childActiveName"]);
const editData = computed(() => props.data);

// 选择资产负债表辅助核算取值弹窗
const allAACalcStatementTemp = ref(1);
// 改变取值方式弹窗
const allAACalcStatementDialog = ref(false);
const recalculateBalacnceType = ref(1);
// 判断取值方式是否改变
const allAACalcStatementHasChanged = ref(false);

const changeAllAACalcStatement = () => {
    request({ 
        url: `/api/AccountSetOnlyAuth/CheckBalance?asId=${editData.value.asId}`,
        method: "post"
    }).then((res: IResponseModel<boolean>) => {
        if(res.state===1000 && res.data){
            if(oldAllAACalcStatement.value !== Boolean(allAACalcStatementTemp.value)){
                allAACalcStatementDialog.value = true;
                recalculateBalacnceType.value = 1;
            }else{
                editData.value.allAACalcStatement = allAACalcStatementTemp.value === 1;
                allAACalcStatementHasChanged.value = !allAACalcStatementHasChanged.value;
            }
        }else{
            allAACalcStatementTemp.value = editData.value.allAACalcStatement ? 1 : 0;
            ElNotify({
                type: "warning",
                message: '资产负债表报表不平，请先调整资产负债表!',
            });
        }
    }).catch((err)=>{
        // allAACalcStatementTemp.value = editData.value.allAACalcStatement ? 1 : 0;
        console.log(err);
    });
};

// 弹窗确认
const changeAllAACalcStatementConfirm = () => {
    editData.value.allAACalcStatement = allAACalcStatementTemp.value === 1;
    // 弹窗确认的时候会改变，切换回来相当于没变
    allAACalcStatementHasChanged.value = !allAACalcStatementHasChanged.value;
    allAACalcStatementDialog.value = false;
};
// 弹窗取消
const changeAllAACalcStatementCancel = () => {
    allAACalcStatementTemp.value = editData.value.allAACalcStatement ? 1 : 0;
    allAACalcStatementDialog.value = false;
};

const queryAsNameParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
const queryAsNameSearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryAsNameParams);
};
const selectAsName = (item: any) => {
    editData.value.asName = item.value;
    editData.value.unifiedNumber = item.creditCode;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
};
const handleAutoCompleteInput = (value: string) => {
    if (value.trim() === "") {
        queryParams.name = "";
    }
};

const createdDate = ref();
const yearNumber = ref();
const monthNumber = ref();
const yearList = ref<Number[]>([]);
const monthList = ref<Number[]>([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
const accountingStandardList = ref<IAccountingStandard[]>([]);
let asStartYear: number;
let asStartMonth: number;

const areaList = ref<ITaxArea[]>([]);
const getAreaList = () => {
    request({ url: window.jtaxHost + "/api/TaxDeclare/GetAreaList", method: "post" }).then((res: IResponseModel<ITaxArea[]>) => {
        if (res.state === 1000) {
            let defaultList = [{ taxAreaId: 0, taxAreaName: "请选择报税地区" }];
            areaList.value = [...defaultList, ...res.data];
        }
    });
};
const asNameRef = ref();
const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};

const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};

function selectName(item: any) {
    editData.value.taxpayerName = item.value;
    editData.value.taxNumberS = item.creditCode;
    getCompanyDetailApi(1010, decodeURIComponent(item.value)).then((res: any) => {
        if (res.state === 1000 && res.data && res.data.taxAreaCode > 0) {
            editData.value.taxadId = res.data.taxAreaCode;
            taxAreaChange();
        }
    });
}
function checkNeededHandle(value: number | string | boolean): any {
    if (value) {
        editData.value.checkNeeded = 0;
        checkNeededDialog.value = true;
    }
}
function checkNeededConfirm() {
    editData.value.checkNeeded = 1;
    checkNeededDialog.value = false;
}
function checkNeededCancel() {
    checkNeededDialog.value = false;
}
function getYearList() {
    let year = new Date().getFullYear();
    for (let i = 2000; i < year + 6; i++) {
        yearList.value.push(i);
    }
}

function getAccountingStandardList() {
    request({
        url: `/api/AccountSet/GetAccountingStandard`,
        method: "post",
    }).then((res: any) => {
        accountingStandardList.value = res.data;
    });
}

function checkChangeStartDate() {
    if (!editData.value.isEditSartDate) changeStartDate.value = true;
}

watch(accountingStandard, () => {
    if (editData.value.accountingStandard === 2) {
        if (accountingStandard.value === 22) {
            warnForNormalFor2019Excute.value = true;
            warnForNormal.value = false;
        } else {
            warnForNormalFor2019Excute.value = false;
            warnForNormal.value = true;
        }
    }
});

function ShowAccountSetUpgrade() {
    if (editData.value.accountingStandard === 4) {
        const year = new Date().getFullYear();
        const maxYear = (year > 2023 ? year : 2023) + 5;
        let startYear = asStartYear;
        if (asStartYear < 2023) {
            startYear = 2023;
        } else {
            farmerCooperativeStartYear.value = asStartYear;
            farmerCooperativeStartMonth.value = asStartMonth;
            farmerCooperativeStartYearCopy.value = asStartYear;
            farmerCooperativeStartMonthCopy.value = asStartMonth;
        }

        for (let i: number = startYear; i <= maxYear; i++) {
            const list: number[] = [];
            const minMonth = i === asStartYear ? asStartMonth : 1;
            for (let j = minMonth; j <= 12; j++) list.push(j);
            farmerCooperativeYearList.value[i] = list;
        }

        upgradeStandardFarmerCooperative.value = true;
    } else {
        if (editData.value.accountingStandard === 1) {
            warnForLittle.value = true;
            warnForNormal.value = false;
        } else {
            warnForLittle.value = false;
            warnForNormal.value = true;
        }
        upgradeStandard.value = true;
    }
}

function AccountSetUpgrade() {
    if (accountingStandard.value === accountStandardForUpgrade.value) {
        ElNotify({
            type: "warning",
            message: "账套准则相同，无需切换",
        });
        return;
    }
    //这个由于需要配合工作台，需要把asid放到Url里
    let upgradeUrl = `/api/AccountSetOnlyAuth/UpgradeV2?asId=${editData.value.asId}&userType=${accountStandardForUpgrade.value}&serviceid=${getServiceId()}`;

    useLoading().enterLoading("切换准则中，请稍候...");
    request({
        url: upgradeUrl,
        method: "post",
        data: {},
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    })
        .then((res: IResponseModel<IAccountSetUpgradeResult>) => {
            useLoading().quitLoading();
            if (res.state == 1000) {
                ElNotify({
                    type: "success",
                    message: "准则切换成功",
                });
                reloadAccountSetList();
                if (res.data.needBuySpace) {
                    //
                } else {
                    if (useThirdPartInfoStoreHook().isThirdPart) {
                        thirdPartNotify(thirtPartNotifyTypeEnum.accountSetUpgradeAccountSet, {
                            oldAsId: useAccountSetStoreHook().accountSet?.asId,
                            newAsId: res.data.newAsId,
                        }).then(() => {
                            changeAS(res.data.newAsId);
                        });
                        return;
                    }
                    if (isLemonClient()) {
                        getLemonClient().setSelectedAccount(res.data.newAsId);
                        return;
                    }
                    setTopLocationhref(res.data.urlTarget);
                }
            } else if (res.state == 2000) {
                if (res.msg == "Overflow") {
                    // ShowCompanyModal($('#CreateAccountOverFlow'), 450);
                } else {
                    if(res.msg === '您的账套数量已超过已购买账套数量。'){
                        proOverFlowText.value = "您的账套数量已超过已购买账套数，建议您去增购~";
                        proOverFlowShow.value = true;
                    }else{
                        ElNotify({
                            type: "warning",
                            message: res.msg,
                        });
                    }
                }
            } else {
                ElNotify({
                    type: "warning",
                    message: "切换准则失败，请稍后重试！",
                });
            }
        })
        .catch((err: any) => {
            useLoading().quitLoading();
            if (err.response?.status === 400) {
                proOverFlowText.value = err.response.data;
                proOverFlowShow.value = true;
            } else {
                ElNotify({
                    type: "warning",
                    message: "切换准则失败，请稍后重试！",
                });
            }
        });
}

const farmerCooperativeIsCopyData = ref(false);
const farmerCooperativeYearList = ref<IYearList>({});
const farmerCooperativeStartYear = ref(2023);
const farmerCooperativeStartMonth = ref(1);
const farmerCooperativeStartYearCopy = ref(2023);
const farmerCooperativeStartMonthCopy = ref(1);

const downloadFarmerCooperativeUpgradeAsubMap = () => {
    globalExport("/api/AccountSetOnlyAuth/ExportImportBankTemplate?asid=" + editData.value.asId);
};

function farmerCooperativeAccountSetUpgrade() {
    let startDate = `${farmerCooperativeStartYear.value}-${farmerCooperativeStartMonth.value}-1`;
    if (farmerCooperativeIsCopyData.value) {
        startDate = `${farmerCooperativeStartYearCopy.value}-${farmerCooperativeStartMonthCopy.value}-1`;
    }
    let upgradeUrl = `/api/AccountSetOnlyAuth/UpgradeForFarmerCooperativeV2?asId=${editData.value.asId}&asStartDate=${startDate}&isCopyData=${farmerCooperativeIsCopyData.value}&serviceid=${getServiceId()}`;

    useLoading().enterLoading("切换准则中，请稍候...");
    request({
        url: upgradeUrl,
        method: "post",
        data: {},
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    })
        .then((res: IResponseModel<IAccountSetUpgradeResult>) => {
            useLoading().quitLoading();
            if (res.state == 1000) {
                ElNotify({
                    type: "success",
                    message: "准则切换成功",
                });
                reloadAccountSetList();
                if (useThirdPartInfoStoreHook().isThirdPart) {
                    thirdPartNotify(thirtPartNotifyTypeEnum.accountSetUpgradeAccountSet, {
                        oldAsId: useAccountSetStoreHook().accountSet?.asId,
                        newAsId: res.data.newAsId,
                    }).then(() => {
                        changeAS(res.data.newAsId);
                    });
                    return;
                }
                if (isLemonClient()) {
                    getLemonClient().setSelectedAccount(res.data.newAsId);
                    return;
                }
                setTopLocationhref(res.data.urlTarget);
            } else if (res.state == 2000) {
                if (res.msg == "Overflow") {
                    // ShowCompanyModal($('#CreateAccountOverFlow'), 450);
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            } else {
                ElNotify({
                    type: "warning",
                    message: "切换准则失败，请稍后重试！",
                });
            }
        })
        .catch((err: any) => {
            useLoading().quitLoading();
            if (err.response?.status === 400) {
                proOverFlowText.value = err.response.data;
                proOverFlowShow.value = true;
            } else {
                ElNotify({
                    type: "warning",
                    message: "切换准则失败，请稍后重试！",
                });
            }
        });
}

const isChangedStartDate = computed(() => {
    return yearNumber.value != asStartYear || monthNumber.value != asStartMonth;
});

function ValidateSelectedDateForEdit() {
    return ValidateSelectedDateInner(yearNumber.value, monthNumber.value);
}

function ValidateSelectedDateInner(yearSelector: number, monthSelector: number) {
    let _msg = "";
    let _succeed = true;
    if (!yearSelector) {
        _msg = "亲，请选择账套启用年月";
        _succeed = false;
    } else {
        if (!monthSelector) {
            _msg = "亲，请选择账套启用年月";
            _succeed = false;
        }
    }
    return { result: _succeed, msg: _msg };
}

async function submitAccountSetForEdit() {
    if (editData.value.asName == "") {
        ElNotify({
            type: "warning",
            message: "亲，单位名称不能为空！",
        });
        return;
    }
    if (editData.value.asName.match('[\\\\/*?:<>|"]')) {
        ElNotify({
            type: "warning",
            message: '亲，单位名称不能包含\\/*?:<>|"等特殊符号！',
        });
        return;
    }
    if (editData.value.asName.length < 5) {
        ElNotify({
            type: "warning",
            message: "亲，请输入完整的单位名称哦！至少5个字符~",
        });
        return;
    }
    if (editData.value.asName.length > 40) {
        ElNotify({
            type: "warning",
            message: "亲，单位名称最多40个字哦！",
        });
        return;
    }
    let _r = ValidateSelectedDateForEdit();
    if (!_r.result) {
        ElNotify({
            type: "warning",
            message: _r.msg,
        });
        return;
    }
    if (editData.value.taxpayerName.length > 64) {
        ElNotify({
            type: "warning",
            message: "亲，纳税人名称不能超过64个字，请修改后重试哦~",
        });
        return;
    }
    if (editData.value.taxNumberS && editData.value.taxNumberS != "") {
        const taxNumberPattern = /^(([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10})|([0-9X]{15})|([0-9X]{17})|([0-9X]{20}))$/;
        if (!taxNumberPattern.test(editData.value.taxNumberS)) {
            ElNotify({
                type: "warning",
                message: "亲，统一社会信用代码格式有误，请认真检查！",
            });
            return;
        }
    }
    if (editData.value.taxpayerPassword.length > 64) {
        ElNotify({
            type: "warning",
            message: "亲，电子税局密码不能超过64个字，请修改后重试哦~",
        });
        return;
    }
    if (editData.value.taxDeclareEmployeeName && editData.value.taxDeclareEmployeeName != "") {
        if (editData.value.taxDeclareEmployeeName.length > 10) {
            ElNotify({
                type: "warning",
                message: "亲，个税办税人姓名不能超过10个字哦！",
            });
            return;
        }
    }
    if (editData.value.taxDeclareEmployeePhone && editData.value.taxDeclareEmployeePhone != "") {
        if (editData.value.taxDeclareEmployeePhone.length != 11 || editData.value.taxDeclareEmployeePhone.indexOf("1") != 0) {
            ElNotify({
                type: "warning",
                message: "亲，个税办税人手机格式不正确！",
            });
            return;
        }
    }
    if (editData.value.taxDeclarePassword.length > 64) {
        ElNotify({
            type: "warning",
            message: "亲，个人系统密码不能超过64个字，请修改后重试哦~",
        });
        return;
    }

    // 税局登录检查
    if (taxBureauAreaLoginTable) {
        if (editData.value.taxBureauAgentTaxNumber && editData.value.taxBureauAgentTaxNumber != "" && IsTaxBureauNeed('AgentTaxNumber')) {
            const taxNumberPattern = /^(([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10})|([0-9X]{15})|([0-9X]{17})|([0-9X]{20}))$/;
            if (!taxNumberPattern.test(editData.value.taxBureauAgentTaxNumber)) {
                ElNotify({
                    type: "warning",
                    message: "亲，代理机构统一社会信用代码格式有误，请认真检查！",
                });
                return;
            }
        }
        if (editData.value.taxBureauAccount && editData.value.taxBureauAccount != "" && IsTaxBureauNeed("TaxAccount")) {
            if (editData.value.taxBureauAccount.match('[\\\\/*?:<>|"]')) {
                ElNotify({
                    type: "warning",
                    message: '亲，税局登录账号不能包含\\/*?:<>|"等特殊符号！',
                });
                return;
            }
        }

        if (
            editData.value.taxBureauPersonIdentityNo &&
            editData.value.taxBureauPersonIdentityNo != "" &&
            IsTaxBureauNeed("PersonIdentityNo")
        ) {
            const idPattern = /^[1-9]\d{5}(19|20)\d{2}(0\d|1[0-2])([0-2]\d|3[0-1])\d{3}([0-9]|X)$/;
            if (!idPattern.test(editData.value.taxBureauPersonIdentityNo)) {
                ElNotify({
                    type: "warning",
                    message: "亲，办税人员证件号码格式有误，请认真检查！",
                });
                return;
            }
        }

        if (editData.value.taxBureauPersonAccount && editData.value.taxBureauPersonAccount != "" && IsTaxBureauNeed("PersonAccount")) {
            const pattern =
                /^(0|86|17951)?(10|13|14|15|16|17|18|19)[0-9]{9}$|^[1-9]\d{5}(19|20)\d{2}(0\d|1[0-2])([0-2]\d|3[0-1])\d{3}([0-9]|X)$|^[A-Za-z][A-Za-z0-9]{5,19}$/;
            if (!pattern.test(editData.value.taxBureauPersonAccount)) {
                ElNotify({
                    type: "warning",
                    message: "亲，个人用户账户格式不正确！",
                });
                return;
            }
        }

        if (editData.value.taxBureauPersonPhone && editData.value.taxBureauPersonPhone != "" && IsTaxBureauNeed("PersonPhone")) {
            const mPattern = /^(0|86|17951)?(10|13|14|15|16|17|18|19)[0-9]{9}$/;
            if (!mPattern.test(editData.value.taxBureauPersonPhone)) {
                ElNotify({
                    type: "warning",
                    message: "亲，办税人员手机格式不正确！",
                });
                return;
            }
        }
    }

    // 资产负债表取值方式是否改变
    if (allAACalcStatementHasChanged.value) {
        const canNext = await dangerousOperationNext(undefined, editData.value.appAsid);
        if (!canNext) return;
        SubmitAccountSetInner();
    } else {
        SubmitAccountSetInner();
    }
}

function SubmitAccountSetInner() {
    const data = {
        accountingStandard: editData.value.accountingStandard,
        asId: editData.value.asId,
        asIndustry: editData.value.asIndustry,
        asName: editData.value.asName,
        asStartYear: yearNumber.value,
        asStartMonth: monthNumber.value,
        cashJournal: editData.value.cashJournal,
        checkNeeded: editData.value.checkNeeded,
        isChangedStartDate: isChangedStartDate.value,
        showScm: editData.value.showScm,
        taxType: editData.value.taxType,
        unifiedNumber: editData.value.unifiedNumber,
        creditCode: editData.value.unifiedNumber, //重复字段
        fixedAsset: editData.value.fixedasset,
        editFixedStartUsedHandle: raStartFixedAssetExistsData.value,
        editFixedStartYear: raStartFixedAssetExistsData.value === 1 ? null : editData.value.editFixedStartYear,
        editFixedStartMonth: raStartFixedAssetExistsData.value === 1 ? null : editData.value.editFixedStartMonth,
        decimalPlace: editData.value.decimalPlace,
        // 纳税人信息
        taxPayerName: editData.value.taxpayerName,
        taxPayerNumber: editData.value.taxNumberS,
        taxArea: editData.value.taxadId,
        taxDeclareRegion: areaList.value.find((item) => item.taxAreaId === editData.value.taxadId)?.taxAreaName ?? "",
        // 税局登录
        taxLoginType: editData.value.taxBureauLoginType,
        taxAccount: editData.value.taxBureauAccount,
        taxPayerPassword: editData.value.taxpayerPassword,
        personPositionType: editData.value.taxBureauPersonPositionType,
        personIdentityNo: editData.value.taxBureauPersonIdentityNo,
        personAccount: editData.value.taxBureauPersonAccount,
        personPassword: editData.value.taxBureauPersonPassword,
        personPhone: editData.value.taxBureauPersonPhone,
        agentTaxNumber: editData.value.taxBureauAgentTaxNumber ?? "",
        // 个税申报
        taxDeclareEmpName: editData.value.taxDeclareEmployeeName,
        taxDeclareEmpPhone: editData.value.taxDeclareEmployeePhone,
        taxDeclarePassword: editData.value.taxDeclarePassword,
        SubmitType: "Edit",
        // 资产负债表取值方式
        allAACalcStatement: editData.value.allAACalcStatement,
        recalculateBalacnceType: recalculateBalacnceType.value,
    };
    useLoading().enterLoading("保存账套中，请稍候...");
    // 修改不用过工作台
    if (isChangedStartDate.value) {
        request({
            url: `/api/AccountSetOnlyAuth/CheckCanEditStartDate?asId=${editData.value.asId}&asStartMonthStr=${monthNumber.value}`,
            method: "post",
        }).then((res: any) => {
            if (res.data.can) {
                put();
            } else {
                if (res.data.code === 2) {
                    ElConfirm("您的期初已有借贷方数据，修改后将会清空，是否确定修改？").then((r: boolean) => {
                        if (r) {
                            request({
                                url: `/api/InitialBalance/ClearGenDebitCredit?asId=${editData.value.asId}`,
                                method: "post",
                            }).then((res: any) => {
                                if (res.data) {
                                    ElNotify({
                                        type: "success",
                                        message: "账套修改成功",
                                    });
                                    // 取值方式改变，并且是当前账套
                                    if (allAACalcStatementHasChanged.value && useAccountSetStoreHook().accountSet?.asId === editData.value.asId) {
                                        window.dispatchEvent(
                                            new CustomEvent("changeAllAACalcStatement", { detail: editData.value.allAACalcStatement })
                                        );
                                    }
                                    // 修改成功，重置资产负债表取值是否改变
                                    allAACalcStatementHasChanged.value = false;
                                    useAccountSetStoreHook().getAccountSet()
                                    successEdit();
                                } else {
                                    if (res.message) {
                                        ElNotify({
                                            type: "success",
                                            message: res.message,
                                        });
                                    } else {
                                        // 重新获取资产负债表余额取值方式的实际取值
                                        request({
                                            url: `/api/AccountSetOnlyAuth/Info?asId=${props.editAsId}`,
                                        }).then((res: IResponseModel<IAccountSetInfo>) => {
                                            oldAllAACalcStatement.value = res.data.allAACalcStatement;

                                            // 暂时不切换回实际选中
                                            // allAACalcStatementHasChanged.value = false;
                                            // allAACalcStatementTemp.value = oldAllAACalcStatement.value ? 1 : 0;
                                        });
                                        ElNotify({
                                            type: "success",
                                            message: "亲，保存失败啦，请刷新重试！",
                                        });
                                    }
                                }
                            });
                        }
                    });
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.data.msg,
                    });
                }
            }
        }).finally(() => {
            useLoading().quitLoading();
        });
    } else {
        put();
    }
    function put() {
        request({
            url: `/api/AccountSetOnlyAuth`,
            method: "put",
            data,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            },
        }).then((res: any) => {
            if (res.state == 1000) {
                // InitUI();
                reloadAccountSetList();
                // CancelNewAccuntSet();
                //如果变更了启用年月，需要重新获取期间
                if (isChangedStartDate.value) {
                    const store = useAccountPeriodStore();
                    store.getPeriods();
                }
                ElNotify({
                    type: "success",
                    message: "账套修改成功！",
                });
                // 取值方式改变，并且是当前账套
                if (allAACalcStatementHasChanged.value && useAccountSetStoreHook().accountSet?.asId === editData.value.asId) {
                    window.dispatchEvent(
                        new CustomEvent("changeAllAACalcStatement", { detail: editData.value.vAllAACalcStatement })
                    );
                }
                // 修改成功，重置资产负债表取值是否改变
                allAACalcStatementHasChanged.value = false;
                thirdPartNotify(thirtPartNotifyTypeEnum.accountSetModifyAccountSet, { asId: editData.value.asId }).then(() => {});
                window.dispatchEvent(new CustomEvent("modifyAccountSet"));
                useAccountSetStore().getAccountSetInfo(true)
                window.postMessage({ type: "loadMenu" }, window.location.origin);
                //一些设计账套基本信息的，应该不需要modifyAccountSet
                useAccountSetStoreHook().getAccountSet()
                successEdit();
            } else {
                if (res.msg !== null && res.msg !== undefined && res.msg !== "" && res.msg !== "Failed") {
                    if (res.msg === "亲，期初的借方累计或贷方累计存在数据，需要先删除才能修改为1月哦~") {
                        ElConfirm(res.msg);
                    } else {
                        ElNotify({
                            type: "warning",
                            message: res.msg == "请使用管理员操作" ? "系统管理员才可以创建账套" : res.msg,
                        });
                    }
                } else {
                    ElNotify({
                        type: "error",
                        message: "亲，保存失败啦，请刷新重试！",
                    });
                }
            }
        }).finally(() => {
            useLoading().quitLoading();
        });
    }
}

/// 资产模块
const startFixedAsset = ref(false);
const startFixedAssetExistsData = ref(false);
const stopFixedAsset = ref(false);
const raStopFixedAsset = ref(0);
const raStartFixedAssetExistsData = ref(0);
const disRadioFixedAsset = ref(true);
const fixedAssetAllowAdd = ref(true);
const fixedAssetAllowAddMsg = ref("");
const fixedassetInitVal = ref(0);

var changeFixedAsset = function (val: number | string | boolean): any {
    if (val === 1 && fixedassetInitVal.value === 0) {
        if (fixedAssetAllowAdd.value) {
            if (fixedAssetExistsData.value) {
                raStartFixedAssetExistsData.value = 1;
                startFixedAssetExistsData.value = true;
            } else {
                raStartFixedAssetExistsData.value = 0;
                startFixedAsset.value = true;
            }
        } else {
            editData.value.fixedasset = 0;
            ElConfirm(fixedAssetAllowAddMsg.value, false, () => {}, "启用资产");
        }
    } else if (val === 0 && fixedassetInitVal.value === 1) {
        stopFixedAsset.value = true;
        raStopFixedAsset.value = 0;
    }
};

function startFixedAssetConfirm() {
    editData.value.fixedasset = 1;
    let monthText = editData.value.editFixedStartMonth;
    monthText = monthText.length === 1 ? "0" + monthText : monthText;
    editData.value.periodDisplayText = `(${editData.value.editFixedStartYear}-${Number(monthText) < 10 ? "0" + monthText : monthText})`;
    startFixedAsset.value = false;
}

function startFixedAssetCancel() {
    editData.value.fixedasset = 0;
    startFixedAsset.value = false;
}

const startFixedAssetUseBeforeData = ref(false);
const startFixedAssetUseResetData = ref(false);

function startFixedAssetExistsDataConfirm() {
    if (raStartFixedAssetExistsData.value == 1) {
        //保留之前数据
        startFixedAssetUseBeforeData.value = true;
    } else if (raStartFixedAssetExistsData.value == 2) {
        //重新初始化
        startFixedAssetUseResetData.value = true;
    }
    startFixedAssetExistsData.value = false;
}

function startFixedAssetExistsDataCancel() {
    startFixedAssetExistsData.value = false;
    editData.value.fixedasset = 0;
}

function startFixedAssetUseBeforeDataConfirm() {
    startFixedAssetUseBeforeData.value = false;
    editData.value.fixedasset = 1;
    editData.value.periodDisplayText = startPeriodDisplayText.value;
}

function startFixedAssetUseBeforeDataCancel() {
    startFixedAssetUseBeforeData.value = false;
    editData.value.fixedasset = 0;
}

function startFixedAssetUseResetDataConfirm() {
    editData.value.fixedasset = 0;
    startFixedAssetUseResetData.value = false;
    AppConfirmDialog(1160).then((r: boolean) => {
        if (r) {
            editData.value.fixedasset = 1;
            let monthText = editData.value.editFixedStartMonth;
            monthText = monthText.length === 1 ? "0" + monthText : monthText;
            editData.value.periodDisplayText = `(${editData.value.editFixedStartYear}-${
                Number(monthText) < 10 ? "0" + monthText : monthText
            })`;
        }
    });
}

function startFixedAssetUseResetDataCancel() {
    startFixedAssetUseResetData.value = false;
    editData.value.fixedasset = 0;
}

function stopFixedAssetConfirm() {
    stopFixedAsset.value = false;
    editData.value.fixedasset = raStopFixedAsset.value == 1 ? 0 : 1;
}
function stopFixedAssetCancel() {
    stopFixedAsset.value = false;
    editData.value.fixedasset = 1;
}

function initFixedAsset() {
    if (editData.value.fixedasset === 1) {
        disRadioFixedAsset.value = false;
    } else {
        //当资产模块关闭
        request({
            url: `/api/FixedAssets/CheckAllowAddByAsid?asId=${props.data.asId}`,
            method: "post",
            data: {},
        })
            .then((res: any) => {
                if (res.state === 1000) {
                    disRadioFixedAsset.value = false;
                    if (!res.data.isAllowAdd) {
                        fixedAssetAllowAdd.value = false;
                        fixedAssetAllowAddMsg.value = res.msg;
                    } else {
                        bindFixedAssetSelect(res);
                        //根据资产是否曾经有数据，分别处理 res.data.existsData
                    }
                }
            })
            .catch((res: any) => {
                disRadioFixedAsset.value = false;
                ElNotify({
                    type: "error",
                    message: "资产信息获取失败，请刷新重试！",
                });
            });
    }
}

var changeFixedAssetMonth = function () {};
const fixedAssetExistsData = ref(false);
const fixedAssetExistsVoucher = ref(false);
const disFixedAssetConfirm = ref(false);
const fixedAssetYearList = ref<IYearList>({});
const startPeriodDisplayText = ref("");

function bindFixedAssetSelect(result: any) {
    fixedAssetExistsData.value = result.data.existsData;
    editData.value.editFixedStartYear = result.data.currentYear;
    editData.value.editFixedStartMonth = result.data.currentMonth;

    let monthText = result.data.startMonth;
    monthText = Number(monthText) < 10 ? "0" + monthText : monthText;
    startPeriodDisplayText.value = `(${result.data.startYear}-${monthText})`;

    const maxYear = new Date().getFullYear() + 5;
    for (let i: number = result.data.startYear; i < maxYear + 1; i++) {
        const list: number[] = [];
        const minMonth = i === result.data.startYear ? result.data.startMonth : 1;
        for (let j = minMonth; j <= 12; j++) list.push(j);
        fixedAssetYearList.value[i] = list;
    }
    changeFixedAssetMonth = function () {
        request({
            url: `/api/FixedAssets/CheckExistsVoucherWithAsid?year=${editData.value.editFixedStartYear}&month=${editData.value.editFixedStartMonth}&asid=${editData.value.asId}`,
            method: "post",
            data: {},
        })
            .then((res: any) => {
                if (res.state === 1000) {
                    fixedAssetExistsVoucher.value = res.data;
                }
            })
            .catch(function () {
                disFixedAssetConfirm.value = true;
                ElNotify({
                    type: "error",
                    message: "资产相关凭证信息获取失败，请刷新重试",
                });
            });
    };
    changeFixedAssetMonth();
}

function successEdit() {
    emits("cancelEdit");
}

// 切换准则输入框默认
const currentStandard = computed(() => (val: number) => {
    if (accountingStandardList.value.length > 0) {
        let text = accountingStandardList.value.find((item) => item.value === val)?.text;
        return formatStandard(text);
    } else {
        return "";
    }
});

// 准则换行
const formatStandard = (text?: string) => {
    if (text && text.length > 23) {
        return `${text.slice(0, 23)}
            <br/>
            ${text.slice(23)}
            `;
    }
    return text;
};
const resetActiveName = () => {
    activeName.value = "first";
};

let hideTaxInfoCookies = getCookie("CloseTaxInfoTip") ?? "";
// 税局信息
const showTaxInfoTip = ref(false);
const closeTaxInfoTip = () => {
    showTaxInfoTip.value = false;

    let list = [];
    const cookie = getCookie("CloseTaxInfoTip");
    if (cookie) list = cookie.split(",");
    list.push(editData.value.asId.toString());
    setCookie("CloseTaxInfoTip", list.join(), "d3650");
};

const initTaxInfoTip = () => {
    const cookie = getCookie("CloseTaxInfoTip");
    if (!cookie) {
        showTaxInfoTip.value = true;
        return;
    }
    showTaxInfoTip.value = !cookie.split(",").includes(editData.value.asId.toString());
};

const taxDeclareLoginType = 1;
const TaxBureauLoginTable = TaxBureauLoginTableJson as ILoginTable;

let taxBureauAreaLoginTable: any = null;
const IsTaxBureauNeed = (name: string) => {
    if (taxBureauAreaLoginTable === null) return false;
    return !!taxBureauAreaLoginTable[name];
};
const taxAreaChange = () => {
    if (editData.value?.taxadId) {
        if (!invoiceAgentArea.value.includes(editData.value?.taxadId)) {
            editData.value.taxBureauLoginType = 9;
        }
        taxBureauAreaLoginTable = TaxBureauLoginTable[editData.value.taxadId][editData.value.taxBureauLoginType];
        editData.value.taxBureauPersonPositionType = editData.value.taxBureauPersonPositionType ?? 0;
    } else {
        taxBureauAreaLoginTable = null;
    }
};
const loginTypeChange = () => {
    taxBureauAreaLoginTable = TaxBureauLoginTable[editData.value.taxadId][editData.value.taxBureauLoginType];
}

const accountSetBackupTips = computed(() => {
    //  && window.isProSystem
    if (window.isAccountingAgent) {
        return "此操作可能会影响您的报表数据，系统将自动为您备份 (若15分钟内备份过则跳过)。如果想恢复操作的话，可到电子档案 - 数据备份中下载并到记账平台恢复";
    } else if (window.isErp) {
        return "此操作可能会影响您的报表数据，系统将自动为您备份 (若15分钟内备份过则跳过)。如果想恢复操作的话，可到【系统设置】-【备份与恢复】中恢复账套";
    } else {
        return "此操作可能会影响您的报表数据，系统将自动为您备份 (若15分钟内备份过则跳过)。如果想恢复操作的话，可到【设置】—【备份恢复】中恢复账套";
    }
})

defineExpose({ resetActiveName });
onMounted(() => {
    getUseAgentTax();
    getYearList();
    getAreaList();
    getAccountingStandardList();
});

const oldAllAACalcStatement = ref(true)
watch(
    () => props.data,
    () => {
        oldAllAACalcStatement.value = props.data.allAACalcStatement;
        allAACalcStatementTemp.value = editData.value.allAACalcStatement ? 1 : 0;
        if (props.data.asId) {
            createdDate.value = props.data.createdDate;
            yearNumber.value = new Date(props.data.asStartDate).getFullYear();
            asStartYear = new Date(props.data.asStartDate).getFullYear();
            monthNumber.value = new Date(props.data.asStartDate).getMonth() + 1;
            asStartMonth = new Date(props.data.asStartDate).getMonth() + 1;
            fixedassetInitVal.value = props.data.fixedasset;
            accountingStandard.value = props.data.accountingStandard;
            if (props.data.accountingStandard === 2) {
                accountingStandard.value *= 10;
                if (props.data.subAccountingStandard) {
                    accountingStandard.value += props.data.subAccountingStandard;
                }
                accountStandardForUpgrade.value = accountingStandard.value;
            } else {
                if (editData.value.fixedasset === 1) {
                    disRadioFixedAsset.value = false;
                }
                if (accountingStandardList.value.length > 0) {
                    accountStandardForUpgrade.value = accountingStandardList.value[0].value;
                }
            }
            if (editData.value.taxadId !== 0 && TaxBureauLoginTable[editData.value.taxadId] !== undefined) {
                taxBureauAreaLoginTable = TaxBureauLoginTable[editData.value.taxadId][editData.value.taxBureauLoginType];
            } else {
                taxBureauAreaLoginTable = null;
            }
            initFixedAsset();
            initTaxInfoTip();
        }
    },
    { immediate: true }
);

const showAreaList = ref<Array<ITaxArea>>([]);
watchEffect(() => { 
    showAreaList.value = JSON.parse(JSON.stringify(areaList.value));  
});
function areaFilterMethod(value: string) {
    showAreaList.value = commonFilterMethod(value, areaList.value, 'taxAreaName');
}
//代理业务登录
const taxBureauLoginListOption = ref(taxBureauLoginList);
const invoiceAgentArea = ref<number[]>([]);
watch(
    () => editData.value.taxadId,
    (val) => {
        taxBureauLoginListOption.value = getTaxBureauLoginList(invoiceAgentArea.value, val);
    }
);
const getUseAgentTax = () => {
    getUseAgentTaxApi().then((res: any) => {
        invoiceAgentArea.value = res;
    });
}
</script>

<style scoped lang="less">
@import "@/style/Settings/EditAccountSet.less";
@import "@/style/Functions.less";

.pointer-click {
    pointer-events: none;
}
:deep(.el-input.is-disabled) {
    & .el-input__inner {
        cursor: default !important;
    }
}
.edit-accountSet {
    position: relative;
    margin: 0 auto 50px;
    background-color: var(--white);
    &:before {
        content: "";
        position: absolute;
        z-index: 0;
        top: 85px;
        left: 0;
        right: 0;
        border-bottom: 1px solid var(--el-border-color-light);
    }
    .edit-tips {
        top: 99px;
        right: 0;
        left: 0;
        .set-font(#999999, 13px, 19px);
        text-align: right;
        width: 1000px;
        margin: auto;
    }

    :deep(.el-tabs) {
        position: relative;
        width: 1000px;
        margin: 0 auto;
        .el-tabs__content {
            overflow: visible;
        }

        .el-tabs__header {
            margin-bottom: 0;
            .el-tabs__nav {
                margin-left: -20px;
            }
            .el-tabs__nav-wrap:after {
                height: 0;
            }
        }
        &:before {
            content: "";
            position: absolute;
            z-index: 0;
            top: 96px;
            left: 0;
            right: 0;
            bottom: -80px;
            border: 1px solid var(--el-border-color-light);
        }
    }
    .buttons {
        border-top: none;
        position: relative;
    }
}

.accountset-edit-content {
    margin-top: 50px;
    // height: 392px !important;
    height: auto;
    .line-item2 {
        .top-title {
            height: 20px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 20px;
            margin-top: 20px;
            margin-top: 24px;
            font-size: 16px;
            font-weight: bold;
            margin-right: 300px;
        }
    }
    .scmTip{
        margin-top: 10px;
        margin-left:280px;
        .tips {
           display: flex;
           align-items: center;
           height: 20px;
           line-height: 20px;
           font-size: 13px;
           color: #888;
           img {
               width: 15px;
               height: 15px;
               margin-right:2px;
            }
           & ~ .tips {
               padding-left: 18px;
               white-space: nowrap;
            }
        }
    }
    .line-item1.line-item-large {
        min-height: 32px;
        height: auto;
        &.clearfix::after {
            content: "";
            display: table;
            clear: both;
        }
        .line-item-single {
            min-height: 32px;
            height: auto;
        }
    }

    .line-item-field {
        :deep(.el-radio-group) {
            display: flex;
            align-items: center;

            .el-radio {
                flex: 1;
                margin: 0 !important;
                display: flex;
                align-items: center;
            }
        }

        .tip-question {
            margin-left: 10px;
            position: relative;
            width: 15px;
            height: 15px;
            display: inline-block;

            &:hover {
                .float-digit-tip {
                    display: inline;
                }
            }
            .image-container {
                width: 16px;
                height: 16px;
            }
            .float-digit-tip {
                position: absolute;
                top: 100%;
                left: 100%;
                font-size: 13px;
                text-align: left;
                color: #333333;
                z-index: 900;
                background-color: rgb(255, 255, 255);
                padding: 12px 10px 14px 21px;
                border: 1px solid rgb(217, 217, 217);
                border-radius: 5px;
                box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 16px 0px;
                height: auto;
                width: 200px;
                overflow-wrap: break-word;
                white-space: pre-wrap;
                text-align: left;
                vertical-align: middle;
                display: none;
            }
        }
    }

    &.tax-info-content {
        padding-top: 20px;
    }
}

.tax-info-content {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    align-items: flex-start;
    overflow: auto;

    .tax-info-tip {
        width: 80%;
        height: 40px;
        line-height: 40px;
        margin-top: 10px;
        margin-left: 10%;
        background-color: #fff7e6;
        border-radius: 8px;
        font-size: 14px;
        .remark-warnning {
            background-image: url("@/assets/settings/warnning-orange.png");
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: 14px 14px;
            font-size: 12px;
            vertical-align: middle;
            margin: 0 5px 0 10px;
            display: inline-block;
            height: 16px;
            width: 16px;
        }
        .remark-close {
            cursor: pointer;
            margin-left: 50px;
        }
    }

    .tax-info-block {
        display: flex;
        width: 100%;
        min-height: 100px;
        margin-top: 10px;
        flex-wrap: wrap;
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        align-items: flex-start;

        .tax-info-col,
        .tax-info-coltitle {
            display: flex;
            width: 50%;
            min-height: 1px;
            padding: 10px 0;
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            .tax-info-col-left {
                display: block;
                text-align: right;
                width: 34%;
                margin-right: 1%;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 32px;
                min-height: 32px;
                .tax-highlight-red {
                    color: var(--red) !important;
                    margin-right: 5px;
                }
            }

            .tax-info-col-right {
                display: block;
                text-align: left;
                width: 64%;
                margin-left: 1%;
                font-size: var(--font-size);
                line-height: 32px;
                min-height: 32px;
                :deep(.span_wrap.el-tooltip__trigger)  {
                    width: 238px;
                }
            }
        }
        .tax-info-coltitle {
            width: 100%;

            .tax-info-col-left {
                width: 17%;
                margin-right: 1%;
                font-size: var(--h3);
                font-weight: bold;
                line-height: 32px;
            }

            .tax-info-col-right {
                width: 81%;
                margin-left: 1%;
                color: grey;
            }
        }
    }
}

.accountSetUpgrade {
    font-size: 12px;
    & .select-block {
        margin: 20px 24px 10px;
    }
    & .info-block {
        margin: 0px 24px 20px;
        background-color: rgb(245, 242, 242);
        padding: 11px;
        border-radius: 4px;
        & div + div {
            margin-top: 10px;
        }
        & .color-green {
            .round-dot {
                background-color: rgb(86, 156, 88);
            }
            color: rgb(86, 156, 88);
        }

        & .color-red {
            .round-dot {
                background-color: rgb(190, 88, 88);
            }
            color: rgb(190, 88, 88);
        }
        & .round-dot {
            height: 6px;
            width: 6px;
            border-radius: 3px;
            display: inline-block;
            margin-bottom: 2px;
        }
    }
}

.new-message-box {
    & .box-body {
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;

        & .body-message {
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            width: 100%;
            & .body-message-title {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                white-space: normal;
                word-break: break-all;
            }
            & .body-message-content {
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 17px;
                margin-top: 10px;
                text-align: left;
                width: 100%;
                padding: 0;
                white-space: normal;

                & .info-block {
                    height: 32px;
                    line-height: 32px;
                }

                & .reset-info-block {
                    margin: 0 53px 19px 91px;
                }
                .backup-tips {
                    margin-top: 10px;
                    span {
                        font-size: 14px;
                        line-height: 20px;
                        img {
                            height: 14px;
                            margin-right: 5px;
                        }
                    }
                }
            }
        }
    }
}

.upgradeStandard {
    :deep(.el-input__wrapper) {
        .el-input__prefix-inner {
            padding: 5px 0;
            .upgradeStandard-current {
                width: 300px;
                min-height: 24px;
                text-align: left;
                line-height: 24px;
                color: #808080;
            }
        }
        .el-input__inner {
            display: none;
        }
    }
}

:deep(.el-select-dropdown__item) {
    height: auto;
    padding-top: 5px;
    padding-bottom: 5px;
    span {
        line-height: 22px;
        white-space: wrap;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }
}
.asStartYearTip,
.accountingStandardTip {
    cursor: pointer;
}
.content-right {
    :deep(.el-autocomplete-suggestion__list) {
        li {
            font-size: 12px;
            padding: 4px 6px 4px 8px;
            line-height: 20px;
            height: auto;
            white-space: break-spaces;
            &:hover {
                background-color: var(--main-color) !important;
                color: var(--white) !important;
                font-weight: normal;
            }
        }
    }
}
.edit-accountSet-title {
    padding-top: 15px;
    font-size: 16px;
    color: #333333;
    line-height: 22px;
    font-weight: 600;
    text-align: center;
}
.line-item-single {
    .line-item-title {
        width: 110px;
        text-align: left !important;
    }
}
:deep(.el-autocomplete-suggestion li) {
    margin: 12px 0;
}
.image-container {
    width: 18px;
    height: 18px;
    cursor: pointer;
    background-image: url("@/assets/Settings/question.png");
    background-size: cover;
}

.image-container:hover {
    background-image: url("@/assets/Settings/question-green.png");
}
.inline-block {
    display: inline-block;
    margin-left: 5px;
}
</style>
