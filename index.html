<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <script src="/web.config.js"></script>
        <script src="/system.config.js"></script>
        <script src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js" referrerpolicy="origin"></script>
        <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js" referrerpolicy="origin"></script>
        <!-- <script src="https://hm.baidu.com/hm.js?196de8e5bab8a5ba9312dcdbb05c2c33"></script> -->
        <title>柠檬云财税</title>
        <script>
            var _hmt = _hmt || [];
            window._hmt = _hmt;
        </script>
    </head>
    <body>
        <div id="app"></div>
        <script>
            var _hmt = _hmt || [];
            (function () {
                var hm = document.createElement("script");
                if (window.isErp) {
                    hm.src = "https://hm.baidu.com/hm.js?362d2681d2e588b09374af74f369c6f2";
                } else if (window.isProSystem) {
                    hm.src = "https://hm.baidu.com/hm.js?0217b35db7c30c75816969b0f0f088b1";
                } else {
                    hm.src = "https://hm.baidu.com/hm.js?119d8aaca3d21a2bf416aa0346a12a67";
                }
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);

                var hm2 = document.createElement("script");
                hm2.src = "https://hm.baidu.com/hm.js?196de8e5bab8a5ba9312dcdbb05c2c33";
                var s2 = document.getElementsByTagName("script")[0];
                s2.parentNode.insertBefore(hm2, s);
            })();
            window._hmt = _hmt;
            // function getBrowserState() {
            //     var userAgent = navigator.userAgent;
            //     var edge = /Edge\/(\d+)\./.exec(userAgent);
            //     if (edge) {
            //         var version = Number(edge[1]);
            //         if (version >= 85) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     }
            //     var chromeMs = /Chrome\/(\d+)\./.exec(userAgent);
            //     if (chromeMs) {
            //         var version = Number(chromeMs[1]);
            //         if (version >= 85) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     }
            //     if (/Safari\//.test(userAgent)) {
            //         var version = Number(/Version\/(\d+)\./.exec(userAgent)[1] || 0);
            //         if (version >= 14) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     }
            //     var firefox = /Firefox\/(\d+)\./.exec(userAgent);
            //     if (firefox) {
            //         var version = Number(firefox[1]);
            //         if (version >= 79) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     }
            //     var cefMs = /WeComCEF\/(\d+)\./.exec(userAgent);
            //     if (cefMs) {
            //         var version = Number(cefMs[1]);
            //         if (version >= 85) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     }
            //     return false;
            // }
            // if (!getBrowserState()) {
            //     window.location.href = "/browserTip.html";
            // }

            // 提示
            function updateConfirm() {
                var userResponse = confirm(
                    "抱歉，您当前使用的客户端版本过低，已无法继续使用。为了获得更好的体验和功能请立即升级，感谢您的理解。"
                );
                if (userResponse) {
                    lmClient.openWindow("https://download.ningmengyun.com/ningmengyun.zip");
                    updateConfirm();
                } else {
                    updateConfirm();
                }
            }

            // 旧版客户端弹出更新提示;
            if (location.href.toLocaleLowerCase().indexOf("/default/default") >= 0) {
                var match = window.navigator.userAgent.match(/LemonClient\(Acc\)\/([\d\.]*)/);
                if (match) {
                    var version = parseInt(match[1].replace(/\./g, ""));
                    if (version < 600) {
                        if (lmClient.showNormalDialog) {
                            lmClient.showNormalDialog(window.jHost + "/App/ClientDialog.aspx?type=needUpdate", function () {});
                        } else {
                            updateConfirm();
                        }
                    }
                }
            }
        </script>
        <script type="module" src="/src/main.ts"></script>
    </body>
</html>
