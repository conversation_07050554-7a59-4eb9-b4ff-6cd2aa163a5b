<template>
    <el-input
        ref="inputRef"
        title=""
        v-model="inputValue"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChange"
        @wheel="handleWheel"
        @keydown="handleKeyDown"
        @input="handleInput"
        :disabled="disabled"
    />
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import type { ElInput } from "element-plus";

const inputRef = ref<InstanceType<typeof ElInput>>();

const props = withDefaults(defineProps<{ modelValue: string; limit?: number; disabled?: boolean }>(), { limit: 2, disabled: false });

const emit = defineEmits<{
    (event: "update:modelValue", value: string): void;
    (event: "change", value: string): void;
    (event: "input", value: string): void;
    (event: "key-down-enter"): void;
    (event: "blur"): void;
    (event: "focus"): void;
}>();

const inputValue = computed({
    get: () => props.modelValue,
    set: (value: string) => emit("update:modelValue", value),
});

function handleKeyDown(event: any) {
    if (event.key === "Enter") {
        emit("key-down-enter");
    }
    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        event.preventDefault();
    }
}
function handleWheel(e: WheelEvent) {
    e.preventDefault();
    e.stopPropagation();
}
function handleChange(value: string) {
    emit("change", value);
}
function handleBlur() {
    emit("blur");
}
function handleFocus() {
    emit("focus");
}
function handleInput(value: string) {
    inputValue.value = formatNumber(value);
    emit("input", formatNumber(value));
}
function formatNumber(value: string) {
    let result = value;
    const isMinus = value.startsWith("-");
    const regex = new RegExp(`^(-)?([0-9]{1,9}(\\.[0-9]{0,${props.limit}})?)?$`);
    if (!regex.test(value)) {
        const numberList = value.split(".");
        let integer = numberList[0]?.replace(/\D/g, "");
        let decimal = numberList.slice(1)?.join("")?.replace(/\D/g, "");
        integer = isMinus ? "-" + integer.slice(0, 9) : integer.slice(0, 9);
        decimal = decimal?.slice(0, props.limit);
        result = decimal ? (!integer ? "" : `${integer}.${decimal}`) : integer;
    }
    return result;
}
defineExpose({
    focus() {
        inputRef.value?.focus();
    },
    blur() {
        inputRef.value?.blur();
    },
});
</script>
