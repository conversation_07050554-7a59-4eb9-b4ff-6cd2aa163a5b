export const firstDayOfMonth = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const firstDay = `${year}-${month.toString().padStart(2, "0")}-01`;
    return firstDay;
};

export const lastDayOfMonth = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1;
    const lastDay = new Date(year, month, 0).getDate();
    const formattedDate = `${year}-${month.toString().padStart(2, "0")}-${lastDay.toString().padStart(2, "0")}`;
    return formattedDate;
};

export const myformatter = (date: Date) => {
    const y = date.getFullYear();
    const m = date.getMonth() + 1;
    const d = date.getDate();
    return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d);
};

export const findTreeIdById = (tree: any[], id: string): string => {
    for (const node of tree) {
        if (node.id === id) {
            return node.id;
        }
        if (node.children && node.children.length > 0) {
            const result = findTreeIdById(node.children, id);
            if (result !== "") {
                return result;
            }
        }
    }
    // 如果没有找到匹配的数据，则返回空字符串
    return "";
};