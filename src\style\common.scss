html,
body,
#app {
  width: 100%;
  height: 100%;
  margin: 0;
}

//margin
.ml-10 {
  margin-left: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.mr-20 {
  margin-right: 20px;
}
.mt-10 {
  margin-top: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}
//width
.w-180 {
  width: 180px;
}

.common-table {
  thead {
    tr {
      th {
        height: var(--table-title-height);
        text-align: left;

        div.cell {
          padding: 0 8px;
          @include set-font(var(--font-color), var(--table-title-font-size), var(--table-title-line-height));
          font-weight: bold;
        }

        &.money {
          text-align: right;
        }

        &.center {
          text-align: center;
        }
      }
      &.green-header {
        th {
          div {
            color: var(--white);
          }
          background-color: var(--main-color);
        }
      }
    }

    &.is-group {
      tr {
        th {
          height: var(--table-title-height);
          text-align: left;

          div {
            padding: 0 8px;
            @include set-font(var(--font-color), var(--table-title-font-size), var(--table-title-line-height));
            font-weight: bold;
          }

          &.money {
            text-align: right;
          }

          &.center {
            text-align: center;
          }
        }
        &.green-header {
          th {
            div {
              color: var(--white);
            }
            background-color: var(--main-color);
          }
        }
      }
    }
  }

  tbody {
    tr {
      td {
        &.el-table__cell {
          padding: 0;
        }
        height: var(--table-body-height);

        .cell {
          @include set-font(var(--font-color), var(--table-body-font-size), var(--table-body-line-height));
          .link {
            font-size: var(--table-body-font-size);
            line-height: var(--table-body-line-height);
          }

          .link + .link {
            margin-left: 10px;
          }

          &.money {
            text-align: right;
          }
          padding: 0 8px;
        }
      }
    }
  }
}
//自定义表格
.custom-table {
  @extend .common-table;
  .el-scrollbar__bar.is-horizontal {
    position: absolute;
    // bottom: -11px;
    height: 10px;
    border-radius: 10px;
    &:hover {
      height: 10px;
      border-radius: 10px;
    }
  }
  :deep(.el-table--enable-row-hover) {
    .el-popper {
      max-width: 300px;
      text-align: left;
    }
  }
  :deep(.table-column-header-popper) {
    max-width: 300px;
    text-align: left;
  }
}

//el-button样式
.el-button {
  &:not(.is-text) {
    border-radius: 2px;
    min-width: 84px;
    font-size: var(--h5);
    display: inline-block;
    cursor: pointer;
    text-align: center;
    padding: 8px;
    outline: none;
    transition: var(--transition-time);
    color: var(--font-color);
    border: 1px solid var(--button-border-color);
    background-color: var(--white);

    &.el-button--primary {
      background-color: var(--main-color);
      color: var(--white);

      &:hover {
        background-color: var(--light-main-color);
      }
    }
    &:hover {
      color: var(--white);
      background-color: var(--main-color);
    }
  }

  // 文字按钮样式
  &.is-text {
    padding: 0;
    color: var(--button-text-color);
    font-size: var(--h5);

    &:not(.is-disabled):hover {
      background-color: transparent;
    }
  }
}

// 公共内容样式
.content {
  width: 100%;
  height: 100%;
  // position: relative;
  overflow: hidden;
  @include set-font;
  .main-top {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    &.space-between {
      justify-content: space-between;
    }
    &.flex-end {
      justify-content: flex-end;
    }
    .main-tool-left {
      display: flex;
      align-items: center;
      .tool-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        span {
          white-space: nowrap;
        }
      }
    }

    .main-tool-right {
      display: flex;
      align-items: center;
    }
  }

  .main-content {
    background-color: var(--white);
    height: 100%;

    .main-content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 11px;
      border-bottom: 1px solid #e5e5e5;
    }
    .main-content-body {
      padding: 11px;
    }
  }

  .el-tabs {
    height: 100%;
    .el-tabs__header {
      margin-bottom: 0px;
    }
    .el-tabs__nav-scroll {
      .el-tabs__nav {
        padding-left: 0;
        .el-tabs__item {
          font-weight: 400;
          padding: 24px 30px;
          &.is-active {
            font-weight: 600;
          }
        }
      }
    }
    .el-tabs__content {
      flex: 1;
      display: flex;
      flex-direction: column;
      .el-tab-pane {
        height: 100%;
        display: flex;
        flex-direction: column;
        min-height: unset !important;
      }
    }
  }
}

// 公共确认弹框样式
.el-dialog {
  .el-dialog__header {
    padding: 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    color: var(--font-color);
    .el-dialog__headerbtn {
      height: 52px;
    }
    .el-dialog__title {
      font-size: var(--h3);
      line-height: 52px;
      font-weight: bold;
    }
  }
  .el-dialog__body {
    color: #363535;
    padding: 20px;
  }
  .el-dialog__footer {
    padding: 10px 0;
    border-top: 1px solid var(--border-color);
    text-align: center;
  }
}

.custom-confirm {
  position: relative;
  padding: 0;
}
.dialogDrag {
    height: fit-content;
}
// .el-overlay {
//   background-color: rgba(0, 0, 0, 0.2) !important;

//   .el-overlay-dialog {
//     .el-dialog {
//       padding: 10px 0 20px;

//       .el-dialog__header {
//         padding: 0 16px 10px 16px;
//         border-bottom: 1px solid #ddd;

//         // 关闭按钮
//         .el-dialog__headerbtn {
//           top: 5px;
//         }

//         .el-dialog__body {
//           .buttons {
//             &.dialog-buttons {
//               padding: 20px 20px 0px;
//               border-top: 1px solid var(--border-color);
//               justify-content: flex-end;
//             }
//           }

//           // 自定义内容
//           .dialog-content {
//             margin: 0 auto;
//             min-height: 50px;
//             padding: 20px;
//           }
//         }

//         .el-dialog__footer {
//           padding: 16px;
//           padding-bottom: 0;
//           border-top: 1px solid var(--border-color);

//           .buttons {
//             justify-content: flex-end;
//           }
//         }
//       }

//       .el-dialog__body {
//         .buttons {
//           &.dialog-buttons {
//             padding: 20px 20px 0px;
//             border-top: 1px solid var(--border-color);
//             justify-content: flex-end;

//             & > *:not(:first-child) {
//               margin-left: 28px;
//             }
//           }
//         }

//         // 自定义内容
//         .dialog-content {
//           margin: 0 auto;
//           min-height: 50px;
//           padding: 20px;
//         }
//       }

//       .el-dialog__footer {
//         padding: 16px;
//         padding-bottom: 0;
//         border-top: 1px solid var(--border-color);

//         .buttons {
//           justify-content: flex-end;
//         }
//       }
//     }

//     // 自定义内容
//     .dialog-content {
//       margin: 0 auto;
//       min-height: 50px;
//       padding: 20px;
//     }
//   }

//   &.modal-class {
//     position: absolute;
//     top: 0 !important;
//     left: 0 !important;
//     right: 0 !important;
//     bottom: 0 !important;
//   }
// }

.main-container .right-container .router-container {
  .el-overlay.modal-class {
    position: absolute;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    .el-overlay-dialog {
      position: absolute;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
    }
  }
}

// 按钮
.buttons {
  padding: 10px 0;
  text-align: center;
}

.button {
  min-width: 76px;
  white-space: nowrap;
  border: 1px solid var(--button-border-color);
  @include base-button(26px, 84px);
  @include hover(var(--main-color));
  @include active(var(--dark-main-color));
  &:hover {
    border-color: var(--main-color);
    color: var(--main-color);
    background-color: var(--white);
  }

  &:active {
    border-color: var(--main-color);
    color: var(--main-color);
    background-color: #f6faff;
  }

  &.dropdown {
    background-position: right 14px center;
    padding-right: 28px;
    white-space: nowrap;

    &:hover {
      // background-image: url("@/assets/Icons/down-black.png");
    }
  }

  &.disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #f5f5f5;
    border: 1px solid #e9e9e9;
  }

  &.solid-button {
    min-width: 76px;
    box-sizing: border-box;
    font-size: var(--font-size);
    @include hover(var(--light-main-color));
    @include active(var(--dark-main-color));
    @include base-button(28px, 84px);
    @include solid(var(--main-color));

    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
      background-image: none;
      background-color: #f5f5f5;
      border: 1px solid #e9e9e9;
    }
  }
}

// 链接
.link {
  color: var(--blue);
  cursor: pointer;
}
.align-center {
  display: flex;
  align-items: center;
}
.el-tooltip__popper {
  max-width: 300px !important;
}
