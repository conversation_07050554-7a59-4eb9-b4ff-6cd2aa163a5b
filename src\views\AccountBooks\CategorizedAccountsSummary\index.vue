<template>
    <div class="content narrow-content">
        <div class="title">科目汇总表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left" @mouseenter="closePopover">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    ref="periodRef"
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :isPeriodList="true"
                                    @getActPid="getActPid"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证字：</div>
                            <div class="line-item-field line-item-select">
                                <el-select 
                                    style="width: 132px" 
                                    :teleported="false"
                                    v-model="searchInfo.voucher"
                                    :filterable="true"
                                    :filter-method="voucherGroupFilterMethod"
                                >
                                    <el-option
                                        v-for="item in showVoucherGroupList"
                                        :key="item.id"
                                        :value="item.id"
                                        :label="item.name"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">起始科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="startAsubRef"
                                    v-model="searchInfo.startAsubCode"
                                    asubImgRight="14px"
                                    :defaultMaxWidth="280"
                                    :showDisabled="true"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">结束科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="endAsubRef"
                                    v-model="searchInfo.endAsubCode"
                                    asubImgRight="14px"
                                    :defaultMaxWidth="280"
                                    :showDisabled="true"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目级别：</div>
                            <div class="line-item-field">
                                <el-input-number
                                    v-model="searchInfo.startAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                                <div class="ml-10 mr-10">至</div>
                                <el-input-number
                                    v-model="searchInfo.endAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证号：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.startVoucher" style="width: 132px" clearable></el-input>
                                <div class="ml-10 mr-10">至</div>
                                <el-input v-model="searchInfo.endVoucher" style="width: 132px" clearable></el-input>
                            </div>
                        </div>
                        <div class="line-item input" v-show="fcIsShow">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field fcid-select line-item-select">
                                <el-select 
                                    v-model="searchInfo.fcid" 
                                    :teleported="false"
                                    :filterable="true"
                                    :filter-method="fcFilterMethod"
                                >
                                    <el-option 
                                        :label="item.label" 
                                        :value="item.id" 
                                        v-for="item in showfcList" 
                                        :key="item.id"
                                    >
                                        {{ item.label }}
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <SearchInfo
                        class="ml-10"
                        v-if="isErp"
                        :width="280"
                        :height="30"
                        :placeholder="'输入编码/名称等关键字搜索'"
                        @search="handleSearchInfo"
                    ></SearchInfo>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <el-checkbox class="mr-20" v-model="searchInfo.showQuantity" label="显示数量金额" @change="handleSearch"></el-checkbox>
                    <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['categorizedaccountssummary-canprint']">
                        <li @click="handlePrint(0,getDefaultParams())">直接打印</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button ml-10" @click="handleExport" v-permission="['categorizedaccountssummary-canexport']">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div v-if="isErp" class="divider-line"></div>
            <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                <div class="main-title">
                    <div class="main-title-left">
                        <Tooltip :content="asubInfo" :max-width="1260" :font-size="12">科目：{{ asubInfo }}</Tooltip>
                    </div>
                    <div class="main-title-right">【凭证数：{{ voucherCount }}张 附件数：{{ attachCount }}张】</div>
                </div>
                <!-- v-show="!loading" -->
                <AccountBooksTable
                    :data="tableData"
                    :columns="columns"
                    :pageIsShow="true"
                    :loading="loading"
                    :empty-text="emptyText"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :current-page="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tooltip-options="{ effect: 'light', placement: 'right-start' }"
                    :scrollbar-show="true"
                    :tableName="setModule"
                >
                    <template #name>
                        <el-table-column 
                            label="科目名称" 
                            align="left" 
                            header-align="left" 
                            :show-overflow-tooltip="true"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span
                                    :class="[
                                        scope.row.asub_code !== '0' && checkPermission(['subsidiaryledger-canview'])
                                            ? 'link'
                                            : 'cursor-default',
                                        asubClassName(scope.row.asub_code),
                                    ]"
                                    @click="
                                        scope.row.asub_code !== '0' && checkPermission(['subsidiaryledger-canview'])
                                            ? ShowDetail(scope.row.asub_name)
                                            : ''
                                    "
                                    v-show="scope.row.asub_name !== ''"
                                >
                                    {{ handleAsubName(scope.row.asub_name) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </AccountBooksTable>
            </div>
        </div>
    </div>
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        :title="isErp ? '凭证汇总表打印' : '科目汇总表打印'"
        :printData="printInfo"
        :dir-disabled="directionDisabled"
        :otherOptions="otherOptions"
        :customNum="6"
        @currentPrint="handlePrint(3,getDefaultParams())"
    />
</template>

<script lang="ts">
export default {
    name: "CategorizedAccountsSummary",
};
</script>
<script setup lang="ts">
import Dropdown from "@/components/Dropdown/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import SearchInfo from "@/components/SearchInfo/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { componentFinishKey } from "@/symbols";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISearchParams, IRows, ITableData, IFcList, IAsubCodeLength } from "./types";
import type { VoucherGroupModel } from "@/components/Voucher/types";
import { ElNotify } from "@/util/notify";
import { formatMoney, formatQuantity, handleAsubName } from "@/util/format";
import { usePagination } from "@/hooks/usePagination";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { extractParams } from "./utils";
import { provide, ref, reactive, watch, onMounted, watchEffect, computed } from "vue";
import { getAsubInfo } from "@/api/getAsubInfo";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import { checkPermission } from "@/util/permission";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import usePrint from "@/hooks/usePrint";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "CateGorizeSummary";
const periodStore = useAccountPeriodStore();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const startAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const endAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const currentPeriodInfo = ref("");
const searchInfo = reactive({
    searchInfo: "",
    startPid: Number(periodStore.getPeriodRange().start),
    endPid: Number(periodStore.getPeriodRange().end),
    startAsubCode: "",
    endAsubCode: "",
    startAsubLevel: 1,
    endAsubLevel: 4,
    voucher: 1000,
    startVoucher: "",
    endVoucher: "",
    showQuantity: false,
    fcid: -1,
    startMonth: "",
    endMonth: "",
});
const isErp = ref(window.isErp);

const { periodData } = usePeriodData(searchInfo, searchInfo.startPid, searchInfo.endPid); 
const getActPid = (start: number, end: number) => {
    searchInfo.startPid = start;
    searchInfo.endPid = end;
}

const voucherGroupList= computed(() => useVoucherGroupStore().voucherGroupList);

const loading = ref<boolean>(false);

function setParams(params: ISearchParams) {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (
        searchInfo.startAsubCode !== "" &&
        searchInfo.endAsubCode !== "" &&
        searchInfo.startAsubCode.split(" ")[0] > searchInfo.endAsubCode.split(" ")[0]
    ) {
        ElNotify({
            message: "亲，起始科目不能大于结束科目哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.startAsubLevel > searchInfo.endAsubLevel) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.startVoucher && !/^\+?[1-9][0-9]*$/.test(searchInfo.startVoucher)) {
        ElNotify({
            message: "起始凭证号只能是正整数",
            type: "warning",
        });
        searchInfo.startVoucher = "";
        return false;
    }
    if (searchInfo.endVoucher && !/^\+?[1-9][0-9]*$/.test(searchInfo.endVoucher)) {
        ElNotify({
            message: "结束凭证号只能是正整数",
            type: "warning",
        });
        searchInfo.endVoucher = "";
        return false;
    }
    params.period_s = searchInfo.startPid;
    params.period_e = searchInfo.endPid;
    params.sbj_leval_s = searchInfo.startAsubLevel;
    params.sbj_leval_e = searchInfo.endAsubLevel;
    params.vnumStart = searchInfo.startVoucher;
    params.vnumEnd = searchInfo.endVoucher;
    params.vg_id = searchInfo.voucher === 1000 ? "" : searchInfo.voucher;
    params.fcid = searchInfo.fcid;
    params.showNumber = searchInfo.showQuantity ? 1 : 0;
    return true;
}

const asubInfo = ref("");
const voucherCount = ref();
const attachCount = ref();

const ShowDetail = (aaname: string) => {
    const { ASUB_ID, period_s, period_e } = extractParams(aaname);
    const params = { ASUB_ID, period_s, period_e,ran: Math.random() };
    globalWindowOpenPage("/AccountBooks/SubsidiaryLedger?" + getUrlSearchParams(params), "明细账");
};

const codeLengthStr = ref("");
function rankCell(asubCode: string, val: string) {
    let formatString = "";
    const code = asubCode.toString();
    for (var i = 0; i < codeLengthStr.value.length; i++) {
        var length = 0;
        for (var j = 0; j <= i; j++) {
            length += Number(codeLengthStr.value[j]);
        }
        if (length == code.length) {
            formatString = "<span class='level" + (i + 1) + "' >" + val + "</span>";
            break;
        }
    }
    return formatString;
}
function asubClassName(asubCode: string) {
    let formatString = "";
    const code = asubCode.toString();
    for (var i = 0; i < codeLengthStr.value.length; i++) {
        var length = 0;
        for (var j = 0; j <= i; j++) {
            length += Number(codeLengthStr.value[j]);
        }
        if (length == code.length) {
            formatString = "level" + (i + 1) + "";
            break;
        }
    }
    return formatString;
}

const columns = ref<Array<IColumnProps>>([]);
function setColumns() {
    if (searchInfo.showQuantity) {
        if (searchInfo.fcid !== -1) {
            columns.value = [
                {
                    label: "科目编码",
                    prop: "asub_code",
                    align: "left",
                    headerAlign: "left",
                    useHtml: true,
                    formatter: (row, column, value) => rankCell(row.asub_code, value),
                    width: getColumnWidth(setModule, 'asub_code'),
                },
                { slot: "name" },
                { label: "币种", prop: "fc_code", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'fc_code') },
                { label: "计量单位", prop: "measureunit", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'measureunit') },
                {
                    label: "金额合计",
                    headerAlign: "center",
                    children: [
                        {
                            label: "借方",
                            prop: "debit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            }, 
                            width: getColumnWidth(setModule, 'debit')
                        },
                        {
                            label: "贷方",
                            prop: "credit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit')
                        },
                    ],
                },
                {
                    label: "外币合计",
                    headerAlign: "center",
                    children: [
                        {
                            label: "借方",
                            prop: "debit_fc",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'debit_fc')
                        },
                        {
                            label: "贷方",
                            prop: "credit_fc",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit_fc')
                        },
                    ],
                },
                {
                    label: "数量合计",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "借方",
                            prop: "debit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'debit_qut')
                        },
                        {
                            slot: "customizePrompt",
                            label: "贷方",
                            prop: "credit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'credit_qut'),
                            resizable: false,
                        },
                    ],
                },
            ];
        } else {
            columns.value = [
                {
                    label: "科目编码",
                    prop: "asub_code",
                    align: "left",
                    headerAlign: "left",
                    useHtml: true,
                    formatter: (row, column, value) => rankCell(row.asub_code, value),
                    width: getColumnWidth(setModule, 'asub_code')
                },
                { slot: "name" },
                { label: "计量单位", prop: "measureunit", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'measureunit') },
                {
                    label: "金额合计",
                    headerAlign: "center",
                    children: [
                        {
                            label: "借方",
                            prop: "debit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            }, 
                            width: getColumnWidth(setModule, 'debit')
                        },
                        {
                            label: "贷方",
                            prop: "credit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit')
                        },
                    ],
                },
                {
                    label: "数量合计",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "借方",
                            prop: "debit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'debit_qut')
                        },
                        {
                            slot: "customizePrompt",
                            label: "贷方",
                            prop: "credit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, 'credit_qut'),
                            resizable: false,
                        },
                    ],
                },
            ];
        }
    } else {
        if (searchInfo.fcid !== -1) {
            columns.value = [
                {
                    label: "科目编码",
                    prop: "asub_code",
                    align: "left",
                    headerAlign: "left",
                    useHtml: true,
                    formatter: (row, column, value) => rankCell(row.asub_code, value),
                    width: getColumnWidth(setModule, 'asub_code')
                },
                { slot: "name" },
                { label: "币种", prop: "fc_code", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'fc_code') },
                {
                    label: "金额合计",
                    headerAlign: "center",
                    children: [
                        {
                            label: "借方",
                            prop: "debit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            }, 
                            width: getColumnWidth(setModule, 'debit')
                        },
                        {
                            label: "贷方",
                            prop: "credit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit')
                        },
                    ],
                },
                {
                    label: "外币合计",
                    headerAlign: "center",
                    children: [
                        {
                            label: "借方",
                            prop: "debit_fc",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'debit_fc')
                        },
                        {
                            label: "贷方",
                            prop: "credit_fc",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit_fc'),
                            resizable: false,
                        },
                    ],
                },
            ];
        } else {
            columns.value = [
                {
                    label: "科目编码",
                    prop: "asub_code",
                    align: "left",
                    headerAlign: "left",
                    useHtml: true,
                    formatter: (row, column, value) => rankCell(row.asub_code, value),
                    width: getColumnWidth(setModule, 'asub_code')
                },
                { slot: "name" },
                {
                    label: "金额合计",
                    headerAlign: "center",
                    children: [
                        {
                            label: "借方",
                            prop: "debit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'debit')
                        },
                        {
                            label: "贷方",
                            prop: "credit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, 'credit'),
                            resizable: false,
                        },
                    ],
                },
            ];
        }
    }
}

//
function setInfos(res: IResponseModel<ITableData>) {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
    asubInfo.value = getAsubInfo(startAsubRef.value?.asubName, endAsubRef.value?.asubName);
    voucherCount.value = res.data.voucherCount;
    attachCount.value = res.data.attachCount;
}
const searchParams: ISearchParams = {
    period_s: 48,
    period_e: 49,
    sbj_leval_s: 1,
    sbj_leval_e: 4,
    vg_id: "",
    vnumStart: "",
    vnumEnd: "",
    fcid: searchInfo.fcid,
    showNumber: 0,
};

//改变设定重新获取数据
const emptyText = ref(" ");
const directionDisabled = ref(false);
function handleSearch() {
    if (searchInfo.fcid !== -1 && searchInfo.showQuantity) {
        printInfo.value.direction = 1;
        directionDisabled.value = true;
    } else {
        printInfo.value.direction = 0;
        directionDisabled.value = false;
    }
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return;
    }

    if (setParams(searchParams)) {
        periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
        currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
        setColumns();
        if (paginationData.currentPage === 1) {
            getTableData();
        }
        paginationData.currentPage = 1;
        containerRef.value?.handleClose();
    }
}

function handleSearchInfo(search: string) {
    searchInfo.searchInfo = search;
    handleSearch();
}

function handleClose() {
    containerRef.value?.handleClose();
}
const closePopover = () => {
    if (!containerRef.value?.popoverShow) {
        startAsubRef.value?.handleClose();
        endAsubRef.value?.handleClose();
    }
};
function handleReset() {
    searchInfo.startAsubCode = "";
    searchInfo.endAsubCode = "";
    searchInfo.startAsubLevel = 1;
    searchInfo.endAsubLevel = maxCodelength.value;
    searchInfo.startVoucher = "";
    searchInfo.endVoucher = "";
    searchInfo.voucher = 1000;
    searchInfo.fcid = -1;
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
}

const extraInfo = {
    isHideSubjectCode:false,
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "categorizedAccountsSummary",
    window.jAccountBooksHost + "/api/CategorizedAccountsSummary/Print",
    extraInfo,
    true,
    false,
    printValidator
);

otherOptions.value = [
    { key: "isHideSubjectCode", label: "表体不打印科目编码" },
    ...otherOptions.value,
];

function printValidator() {
    if (paginationData.total === 0) {
        ElNotify({
            message: "没有数据可打印！",
            type: "warning",
        });
        return false;
    }
    return true;
}

function getDefaultParams(){
    return {
        ...searchParams,
        sbj_id_s: searchInfo.startAsubCode.split(" ")[0],
        sbj_id_e: searchInfo.endAsubCode.split(" ")[0],
        showNumber: searchInfo.showQuantity ? 1 : 0,
    }
}

//导出
function handleExport() {
    const params = {
        ...searchParams,
        sbj_id_s: searchInfo.startAsubCode.split(" ")[0],
        sbj_id_e: searchInfo.endAsubCode.split(" ")[0],
        showNumber: searchInfo.showQuantity ? 1 : 0,
        async: !isLemonClient(),
        ran: Math.random(),
        fcid: searchInfo.fcid,
    };
    if (setParams(params)) {
        globalExport(window.jAccountBooksHost + "/api/CategorizedAccountsSummary/Export?" + getUrlSearchParams(params));
    }
}

//表格数据
const tableData = ref<IRows[]>([]);
const getTableData = () => {
    const data = {
        ...searchParams,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
        sbj_id_s: searchInfo.startAsubCode.split(" ")[0],
        sbj_id_e: searchInfo.endAsubCode.split(" ")[0],
        searchInfo: searchInfo.searchInfo,
    };
    loading.value = true;
    request({
        url: `/api/CategorizedAccountsSummary?` + getUrlSearchParams(data),
    })
        .then((res: IResponseModel<ITableData>) => {
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            setInfos(res);
            if (tableData.value.length === 0) {
                emptyText.value = "暂无数据";
                paginationData.total = 0;
            }
        })
        .finally(() => {
            loading.value = false;
        });
};

// 科目级别
// const codeLengthStr = ref("");
const maxCodelength = ref<number>(4);
function getAsubCodeLength() {
    request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        codeLengthStr.value = res.data.codeLength.join("");
        const codeLengthList: number[] = res.data.codeLength;
        const codeLength = codeLengthList.length;
        maxCodelength.value = codeLength;
        searchParams.sbj_leval_e = codeLength;
        searchInfo.endAsubLevel = codeLength;
    });
}

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        getAsubCodeLength();
        handleSearch();
    }
});
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (val) => {
    getTableData();
});
const fcIsShow = ref<boolean>(false);
const fcList = ref<{ id: number; label: string }[]>([]);
// 获取币别并判断币别是否展示
const InitCurrencyApi = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcListOptions];
};
// 判断是否存在使用了外币核算的科目
const InitPeriodApi2 = () => {
    return request({
        url: "/api/AccountSubject/ExistsFc",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        fcIsShow.value = res.data;
        InitCurrencyApi();
    });
};
onMounted(() => {
    // periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    InitPeriodApi2();
    getAsubCodeLength();
});

const voucherGroupListAll = ref<Array<VoucherGroupModel>>([]);
const showVoucherGroupList = ref<Array<VoucherGroupModel>>([]);
const showfcList = ref<any[]>([]);
watchEffect(() => { 
    voucherGroupListAll.value = JSON.parse(JSON.stringify(voucherGroupList.value));  
    voucherGroupListAll.value.unshift({
        id: 1000,
        name: "全部",
        title: "全部",
        isDefault: false,
    });
    showVoucherGroupList.value = JSON.parse(JSON.stringify(voucherGroupListAll.value));  
});
watchEffect(() => {  
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));  
});
function voucherGroupFilterMethod(value: string) {
    showVoucherGroupList.value = commonFilterMethod(value, voucherGroupListAll.value, 'name');
}
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'label');
}
</script>

<style scoped lang="less">
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";
:deep(.el-popper) {
    max-width: 300px;
    text-align: left;
}

.content {
    .main-center {
        display: flex;
        flex-direction: column;
        .table {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .el-table {
            height: 100%;
        }
        & :deep(.level2) {
            padding-left: 10px;
        }
        & :deep(.level3) {
            padding-left: 20px;
        }
        & :deep(.el-table__empty-text) {
            line-height: 393px;
        }
    }
    .main-title {
        border: 1px solid var(--border-color);
        border-bottom: none;
        margin: 0 !important;
        height: 36px;
        color: var(--font-color);
        font-size: var(--h5);
        line-height: 36px;
        display: flex;
        justify-content: space-between;

        .main-title-left {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 9;
            text-align: left;

            :deep(.span_wrap) {
                white-space: nowrap !important;
                overflow: hidden;
                display: inline-block !important;
            }
        }
        .main-title-right {
            text-align: right;
        }
    }
}

.print-content {
    text-align: center;
    .print-main {
        text-align: left;
        display: inline-block;

        .line-item {
            height: 20px;
            .set-font;
            display: flex;
            align-items: center;
        }
    }

    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}

:deep(.custom-table tbody tr td .cell) {
    white-space: nowrap;
    & span {
        display: block;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
.line-item-field.line-item-select {
    .detail-el-select(132px);
}
body[erp] {
    .custom-confirm {
        .print-content {
            .buttons {
                display: flex;
                justify-content: flex-end;
                .button {
                    float: initial;
                }
            }
        }
    }
}
</style>
