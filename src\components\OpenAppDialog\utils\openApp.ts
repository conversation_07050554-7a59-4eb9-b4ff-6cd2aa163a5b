import ElementPlus from "element-plus"
import OpenAppDialog from "@/components/OpenAppDialog/index.vue"
import { dialogDrag } from "@/directives/dialogDrag"

export const OpenApp = (): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    if (document.querySelector("#openAppDialog")) return
    const content = document.querySelector(".router-container .content")
    const contentBox = content
    const props = {
      confirm: () => {
        resolve(true)
        capp.unmount() //注销
        contentBox!.removeChild(container) //点击后清除弹窗
      },
      close: () => {
        resolve(false)
        capp.unmount()
        contentBox!.removeChild(container)
      },
      cancel: () => {
        resolve(false)
        capp.unmount()
        contentBox!.removeChild(container)
      },
    }
    const capp = createApp(OpenAppDialog, props)
    const container = document.createElement("div")
    capp.use(ElementPlus)
    capp.directive("dialogDrag", dialogDrag)
    capp.mount(container)
    contentBox!.insertBefore(container, contentBox!.firstChild) //插入到body最前面，层级更高
  })
}
