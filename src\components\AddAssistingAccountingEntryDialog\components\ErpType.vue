<template>
     <el-dialog
        v-model="display"
        :title="title"
        center
        :width="width + 'px'"
        class="custom-confirm dialogDrag"
    >
        <div class="add-type-dialog-container" v-dialogDrag>
            <div class="form-item">
                <div class="input-title">类别编码：</div>
                <div class="input-field">
                    <el-input v-model="aaNum" style="width: 192px;"></el-input>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title">类别名称：</div>
                <div class="input-field">
                    <el-input v-model="aaName"
                        v-if="aaNameTextareaShow"
                        ref="aaNameInputRef"
                        type="textarea"
                        maxlength="256"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        resize="none"
                        @blur="inputTypeBlur"
                        class='asubName-textarea'  
                        @input="limitInputLength(aaName, '类别名称', 64)"
                        style="width: 192px;"
                    />
                    <Tooltip :content="aaName" :isInput="true" v-else>
                        <el-input type="text" v-model="aaName" @focus="inputTypeFocus" style="width: 192px;" />
                    </Tooltip>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title">上级类别：</div>
                <div class="input-field">
                    <Select 
                        ref="departmentRef" 
                        v-model="parentAANum" 
                        style="width: 192px;"
                        :filterable="true"
                        :filterMethod="parentTypeFilterMethod"
                    >
                        <Option 
                            v-for="item in showParentList" 
                            :key="item.value" 
                            :value="item.value" 
                            :label="item.label" 
                        />
                    </Select>
                </div>
            </div>
            <div class="buttons erp">
                <a class="button" @click="cancel">取消</a>
                <a class="button solid-button ml-10" @click="confirm">确定</a>
            </div>
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, nextTick, watchEffect } from "vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import type { TreeNode } from "../utils";
import { commonFilterMethod } from "@/components/Select/utils";

const display = ref(false);
const props = defineProps({
    title: {
        type: String,
        default: "新增客户类别",
    },
    width: {
        type: Number,
        default: 440,
    },
    codeType: {
        type: Number,
        default: 0,
    },
    parentTypeList: {
        type: Array,
        default: () => []
    }
});
const emit = defineEmits<{
    (e: "save-type", code: number, data: number): void;
}>();

const aaNum = ref("");
const aaName = ref("");
const aaNameTextareaShow = ref(false);
const parentAANum = ref(0);
const parentList = ref<TreeNode[]>([]);
const aaNameInputRef = ref();
function initParentList() {
    parentList.value = [];
    parentList.value.unshift({value: 0, label: "全部类型"});
}
watchEffect(() => {
    props.parentTypeList.forEach((item: any) => parentList.value.push({...item}));
})

const inputTypeBlur = () => {
    aaNameTextareaShow.value = false;
};

const inputTypeFocus = (val: string) => {
    aaNameTextareaShow.value = true;
    nextTick(()=>{
        if(val) {
            getTextareaFocus();
        }
    })
};
const getTextareaFocus = () => {
    aaNameInputRef.value?.focus();
};
function limitInputLength(val: string, label: string, limitSize: 64) {
    if (val.length > limitSize) {
        ElNotify({ type: "warning", message: `亲，${label}不能超过${limitSize}个字哦~` });
        aaName.value = val.slice(0, limitSize);
    } else {
        aaName.value = val;
    }
}
function showTypeDialog(aType: number, autoAddTypeName: string= "") {
    display.value = true;
    limitInputLength(autoAddTypeName, '类别名称', 64);
    initParentList();
    let url = aType === 1 ? "GetNewCustTypeNo" : aType === 2 ?  "GetNewVendTypeNo" : "GetNewStockTypeNo";
    request({
        url: `/api/ErpAssistingAccounting/${url}`,
        method: "get",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            aaNum.value = res.data;
        }
    });
}
defineExpose({
    showTypeDialog,
});
const restFiled = () => {
    aaNum.value = "";
    aaName.value = "";
    parentAANum.value = 0;
}
const cancel = () => {
    display.value = false;
    restFiled();
}
const confirm = () => {
    if (!aaNum.value) {
        ElNotify({ message: "请填写类别编码", type: "warning" });
        return;
    }
    if (!aaName.value) {
        ElNotify({ message: "请填写类别名称", type: "warning" });
        return;
    }
    let data = {
        no: aaNum.value,
        name: aaName.value,
        parentId: Number(parentAANum.value)
    }
    let url = props.codeType === 1 ? "AddCustType" : props.codeType === 2 ?  "AddVendType" : "AddStockType";
    request({
        url: `/api/ErpAssistingAccounting/${url}`,
        method: "post",
        data,
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            if (res.data) {
                display.value = false;
                ElNotify({ message: "亲，保存成功啦", type: "success" });
                emit("save-type", props.codeType, Number(res.data));
            } else {
                ElNotify({ message: res.msg, type: "warning" });
            }
        } else {
            display.value = false;
            ElNotify({ message: res.msg ?? "保存出错了，请刷新页面重试或联系客服处理！", type: "warning" });
        }
    }).finally(() => {
        restFiled();
    });
}

const showParentList = ref<TreeNode[]>([]);
watchEffect(() => {
    showParentList.value = JSON.parse(JSON.stringify(parentList.value));
});
function parentTypeFilterMethod(value: string) {
    showParentList.value = commonFilterMethod(value, parentList.value, 'label');
}
</script>
<style lang="less" scoped>
@import "@/style/Functions.less";
.add-type-dialog-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-top: 19px;

    .form-item {
        align-self: center;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 16px;

        .input-title {
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }

        .input-field {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .detail-input(256px, 30px);
            .detail-el-select(256px, 30px);
            display: flex;
            align-items: center;
            .asubName-textarea{
                width: 192px;
                height: 32px;
            }
        }
    }

    .buttons {
        margin-top: 11px;
        padding: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
