<template>
    <div class="content">
        <div class="title">关联进销存</div>
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content align-center">
                    <!-- 代账版关联进销存首页 -->
                    <div v-if="isAccountingAgent && !isNextStep && !relationData[0].isRelation" class="slot-mini-content">
                        <div class="main-center is-accounting-agent">
                            <div class="main-center-block">
                                <div class="as-block">
                                    <div class="as-title">代账软件账套</div>
                                    <div id="AsNameText" class="as-info">{{ title }}</div>
                                </div>
                                <i class="relation"></i>
                                <div class="as-block">
                                    <div class="as-title">关联进销存账套名称</div>
                                    <div id="ScmAsNameText" class="as-info">{{ scmAsName }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="main-buttons">
                            <a class="button solid-button" @click="submit">下一步</a>
                        </div>
                    </div>

                    <!-- 普通版首页与代账已关联页面 -->
                    <div v-if="isNextStep || relationData[0].isRelation" class="slot-mini-content">
                        <div class="main-top space-between">
                            <div class="main-tool-left">
                                <a
                                    id="helpLink"
                                    class="link erp-hidden"
                                    v-show="!isErp && !isHideBarcode"
                                    @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=130250100')"
                                    ><span class="triangle"></span>进销存+财务管理操作视频</a
                                >
                            </div>
                            <div class="main-tool-right"><RefreshButton></RefreshButton></div>
                        </div>
                        <div class="main-table">
                            <Table :data="relationData" :columns="columns" :tableName="setModule">
                                <template #operator>
                                    <el-table-column label="操作" :min-width="228" :resizable="false">
                                        <template #default="scope">
                                            <template v-if="isAccountingAgent && scope.row.scmAsid !== 0">
                                                <a class="link" v-permission="['scmrelation-canedit']" @click="deleteRelationForAA()"
                                                    >取消关联</a
                                                >&nbsp;&nbsp;
                                            </template>
                                            <template v-else>
                                                <a
                                                    class="link"
                                                    v-permission="['scmrelation-canedit']"
                                                    @click="editScm(scope.row.scmProductType, scope.row.scmAsid)"
                                                    >编辑</a
                                                >&nbsp;&nbsp;
                                            </template>
                                            <template v-if="scope.row.scmAsid !== 0">
                                                <a
                                                    class="link"
                                                    @click="
                                                        enterScmUrl(
                                                            scope.row.scmProductType,
                                                            scope.row.scmAsid,
                                                            scope.row.scmAsName,
                                                            scope.row.cstId
                                                        )
                                                    "
                                                    >进入进销存账套</a
                                                >
                                            </template>
                                        </template>
                                    </el-table-column>
                                </template>
                            </Table>
                        </div>
                        <div class="main-center">
                            <div class="main-center-title">操作指引：</div>
                            <div class="main-center-block">
                                <div v-if="!isAccountingAgent" class="info-block aa-noshow">
                                    <div class="info-pic relation-pic" @click="editScm(1010, scmAsid)">
                                        <i class="pic"></i>
                                        <div class="info-title">关联进销存账套</div>
                                        <div class="info-word">只有同时拥有财务软件和进销存的账套管理员才有权限设置关联</div>
                                    </div>
                                </div>
                                <div v-if="!isAccountingAgent" class="connect-block aa-noshow">
                                    <span></span>
                                </div>
                                <div class="info-block">
                                    <div class="info-pic settings-pic" @click="handleScmSetting">
                                        <i class="pic"></i>
                                        <div class="info-title">设置进销存核算参数</div>
                                        <div class="info-word">检查确定进销存核算参数的设置</div>
                                    </div>
                                </div>
                                <div class="connect-block">
                                    <span></span>
                                </div>
                                <div class="info-block">
                                    <div class="info-pic data-pic" @click="handleRouterResolve('/Scm/ScmBasicData')">
                                        <i class="pic"></i>
                                        <div class="info-title">完善进销存基础资料</div>
                                        <div class="info-word">设置进销存基础资料与财务软件账套的会计科目之间的对应关系</div>
                                    </div>
                                </div>
                                <div class="connect-block">
                                    <span></span>
                                </div>
                                <div class="info-block">
                                    <div class="info-pic voucher-pic" @click="handleRouterResolve('/Scm/ScmVoucher')">
                                        <i class="pic"></i>
                                        <div class="info-title">自动生成凭证</div>
                                        <div class="info-word">选择进销存单据生成凭证</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-banner" :style="{marginTop:isAccountingAgent && !relationData[0].isRelation && !isNextStep ?'80px':'-8px'}" v-show="bannerSrc.length">
                        <img @click="linkHandle" :src="bannerSrc" />
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <div class="slot-mini-content">
                        <div class="item-line">
                            <div class="item-title">是否关联进销存：</div>
                            <div class="item-input">
                                <el-radio-group v-model="scmStatus" @change="scmStatusChange">
                                    <el-radio :label="0">不关联</el-radio>
                                    <el-radio :label="1">关联</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="item-line">
                            <div class="item-title">财务软件账套：</div>
                            <div class="item-input">
                                <span ref="spanAccountSetName" runat="server">{{ title }}</span>
                            </div>
                        </div>
                        <div v-if="!isAccountingAgent" class="item-line item-scm">
                            <div class="item-title" :style="{ lineHeight: '30px' }">关联进销存账套：</div>
                            <div class="item-input edit-scm-relation">
                                <Select
                                    v-model="scmAccount"
                                    :disabled="scmAccountBool"
                                    :fit-input-width="true"
                                    :teleported="false"
                                    style="width: 230px"
                                    :filterable="true"
                                    :filter-method="scmAccountFilterMethod"
                                >
                                    <Option v-for="item in showScmAccountList" :key="item.id" :value="item.id" :label="item.name">
                                        <div class="item-option">
                                            <img src="@/assets/Menu/vip-icon.png" class="vip-icon" v-if="item.scmType === 1020" />
                                            <span>{{ item.name }}</span>
                                        </div>
                                    </Option>
                                </Select>
                                <a
                                    id="editRelationScm"
                                    class="link"
                                    style="margin-left: 2px"
                                    @click="modifyHandle"
                                    :style="{ lineHeight: '30px' }"
                                >
                                    修改
                                </a>
                            </div>
                        </div>
                        <div v-if="!isAccountingAgent" class="item-line item-scm item-center" id="newScmAccountSetBtn">
                            <a class="link" @click="newScmAccountSet" style="text-align: center">没有账套？请点击前往进销存创建新账套>></a>
                        </div>
                        <div class="item-line item-center">
                            <a class="button solid-button" @click="submit">保存</a>
                            <a class="button ml-10" @click="cancelEditRelation">取消</a>
                        </div>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
    <el-dialog title="提示" v-model="relateDialog" width="460" class="dialogDrag">
        <div class="relate-container" v-dialogDrag>
            <div class="relate-info">
                <div>进销存账套：{{ isRelateScmAsName }}</div>
                <div>已关联记账账套：{{ relateInfo.asName }}（账套ID:{{ relateInfo.asid }}）</div>
                <div>选择继续关联，将解绑原帐套绑定关系，并重新绑定新账套</div>
            </div>
            <div class="buttons borderTop">
                <a class="button" @click="saveScmRelation">继续关联</a>
                <a class="button solid-button ml-10" @click="relateDialog = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="提示" v-model="relateBeforeDialog" width="460" class="dialogDrag">
        <div class="relate-container" v-dialogDrag>
            <div class="relate-info">
                <div>
                    该记账账套（{{ relationData[0].asName }}，账套ID:{{ relationData[0].asid }}）曾关联进销存账套（{{ isRelateScmAsName }}）
                </div>
                <div>选择原关联关系，会保留上次关联的科目及参数设置；选择重新关联，则会重置所有设置</div>
            </div>
            <div class="buttons borderTop">
                <a class="button" @click="saveScmRelation(false)">重新关联</a>
                <a class="button solid-button ml-10" style="width: 100px" @click="saveScmRelation(true)">保留原关联关系</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "ScmRelation",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { request } from "@/util/service";
import { getAACompanyId, getGlobalToken } from "@/util/baseInfo";
import { globalWindowOpen, getUrlSearchParams, globalWindowOpenPage, globalLoadMenu } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { AppConfirmDialog } from "@/util/appConfirm";
import { getScmHost } from "@/util/scm";
import { ref, watch, onMounted, computed, watchEffect } from "vue";
import { isInWxWork } from "@/util/wxwork";
import { checkHasScmSettings } from "@/api/scm";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "scmRelation";
const slots = ["main", "edit"];
const currentSlot = ref("main");
const isAccountingAgent = ref(window.isAccountingAgent);
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart);

const columns = ref<Array<IColumnProps>>([
    {
        prop: "scmAsid",
        label: "关联状态",
        minWidth: 150,
        align: "center",
        headerAlign: "center",
        useHtml: true,
        formatter: function (value) {
            if (value.scmAsid === 0) {
                return "<div class='state-block un-relation'>未关联</div>";
            } else {
                return "<div class='state-block relation'><i></i>已关联</div>";
            }
        },
        width: getColumnWidth(setModule, 'scmAsid')
    },
    { 
        prop: "asName", 
        label: "财务软件账套", 
        minWidth: 300, 
        align: "left", 
        headerAlign: "left", 
        width: getColumnWidth(setModule, 'asName')
    },
    { 
        prop: "scmAsName", 
        label: "进销存账套", 
        minWidth: 300, 
        align: "left", 
        headerAlign: "left", 
        width: getColumnWidth(setModule, 'scmAsName')
    },
    { slot: "operator" },
]);

//后续还有banner,可提取-首页
const bannerSrc = ref("");
let linkHandle = () => {};
enum BannerEnum {
    Free = 2081,
    FreeAgent = 2082,
    Pro = 2083,
    ProAgent = 2084,
}
const SystemTypeMap = {
    "true": { 
        "true": BannerEnum.ProAgent,
        "false": BannerEnum.FreeAgent,
    },
    "false": { 
        "true": BannerEnum.Pro,
        "false": BannerEnum.Free,
    }
}
function isShowBottomBanner() {
    if (isInWxWork()) return;
    if (!isHideBarcode.value || !isThirdPart.value) {
        const isAccountingAgent = window.isAccountingAgent ? "true" : "false";
        const isProSystem = window.isProSystem ? "true" : "false";
        const systemType = SystemTypeMap[isAccountingAgent][isProSystem];
        //2080为进销存
        request({ url: window.apimHost + "/api/Banner/JzPc?adsType=2080&dispType=" + systemType }).then((r: any) => {
            if (r.code == 1000 && r.data) {
                bannerSrc.value = r.data.picOne;
                if (r.data.link) {
                    linkHandle = () => {
                        const link = r.data.link;
                        globalWindowOpen(link + (link.indexOf("?") > -1 ? "&" : "?") + `appasid=${getGlobalToken()}&stay=true`);
                        request({ url: window.apimHost + "/api/Banner/JzPc?adId=" + r.data.adid, method: "post" });
                    };
                }
            }
        });
    }
}
isShowBottomBanner()

function deleteRelationForAA() {
    ElConfirm("确定取消进销存关联吗？").then((r: boolean) => {
        if (r) {
            request({
                url: `/api/ScmRelation/CancelRelation`,
                method: "post",
            }).then((res: any) => {
                if (res.state === 1000) {
                    ElNotify({
                        type: "success",
                        message: "取消关联成功",
                    });
                    // 回到代账进销存首页
                    isNextStep.value = false;
                    window.postMessage({ type: "refreshCurrent" }, window.location.origin);
                } else {
                    ElNotify({
                        type: "error",
                        message: "取消关联失败",
                    });
                }
            });
        }
    });
}

function enterScmUrl(scmProductType: number, scmAsid: number, scmAsName: string, cstId: number) {
    let serviceUrl = cstId ? "&serviceId=" + cstId : "";
    let url =
        getScmHost(scmProductType) +
        "/#/from_acc?dashboard&asid=" +
        scmAsid +
        "&asname=" +
        encodeURIComponent(scmAsName?.replace(/'/g, "\\'")) +
        serviceUrl;
    if (window.isAccountingAgent) {
        url += "&managedByDz=1&aaId=" + getAACompanyId();
    }
    globalWindowOpen(url);
}

interface IScmAccountList {
    asStartDate?: string;
    id: number | string;
    name: string;
    scmType?: number;
}
const title = ref("");
const spanAccountSetName = ref();
const scmAccountList = ref<Array<IScmAccountList>>([]);
const scmStatus = ref(0);
const scmAccount = ref<number | string>();
const isRelateScmAsName = computed(() => {
    return scmAccountList.value.find((item) => item.id === scmAccount.value)?.name;
});
const scmAsName = ref<string>("");
const scmAccountBool = ref(false);
const scmAsid = ref(0);
const scmProductType = ref(0);
const scmType = ref();
// 保存按钮，节流标识符
const submitFlag = ref<boolean>(true);
// 是否代账版关联进销存首页
const isNextStep = ref<boolean>(true);
isNextStep.value = !isAccountingAgent.value;

function checkIsRelation() {
    request({
        url: `/api/ScmRelation`,
    }).then((res: any) => {
        scmAsid.value = res.data.scmAsid;
        scmProductType.value = res.data.scmProductType;
    });
}

function handleScmSetting() {
    if (!relationData.value[0].isRelation) {
        ElConfirm("请先关联进销存账套，才能进行后续操作", true);
    } else {
        globalWindowOpenPage("/Scm/ScmSettings", "进销存核算参数");
    }
}
function handleRouterResolve(url: string) {
    if (!relationData.value[0].isRelation) {
        ElConfirm("请先关联进销存账套，才能进行后续操作", true);
    } else {
        checkHasScmSettings(scmProductType.value, scmAsid.value).then((res: any) => {
            if (res.data) {
                globalWindowOpenPage(url, "进销存凭证");
            } else {
                ElConfirm("请先设置进销存核算参数，才能进行后续操作", true);
            }
        });
    }
}

function editScm(scmProductType: number, scmAsid?: number) {
    AppConfirmDialog(1080).then((r: boolean) => {
        if (r) {
            handleEdit(scmAsid);
        }
    });
}
function handleEdit(scmAsid?: number) {
    scmStatus.value = scmAsid ? 1 : 0;
    currentSlot.value = "edit";
    spanAccountSetName.value.innerText = title.value;
    if (scmAsid !== 0) {
        scmAccountBool.value = true;
        (document.getElementById("editRelationScm") as HTMLElement).style.display = "block";
    } else {
        (document.getElementById("editRelationScm") as HTMLElement).style.display = "none";
    }
}
(window as any).editScm = editScm;

function newScmAccountSet() {
    request({ url: window.eHost + "/wb/enter/getScmServiceInfo?isadministrator=true" }).then((r: any) => {
        let url;
        if (r.statusCode && r.data.haveService) {
            url = window.scmProHost + "/#/from_acc?argsSetting&asId=-1&serviceId=" + r.data.serviceId;
        } else {
            url = window.scmHost + "/#/from_acc?argsSetting&asId=-1";
        }
        globalWindowOpen(url);
    });
}

const relateDialog = ref(false);
const relateBeforeDialog = ref(false);
const relateInfo = ref();
function submit() {
    if (!submitFlag.value) return;
    submitFlag.value = false;
    setTimeout(() => {
        submitFlag.value = true;
    }, 3000);
    if (scmStatus.value === 1 && !scmAccount.value && !isAccountingAgent.value) {
        ElNotify({
            type: "warning",
            message: "请选择进销存账套",
        });
        return false;
    }
    scmAccountList.value.forEach((item) => {
        if (item.id === scmAccount.value) {
            scmType.value = item.scmType;
        }
    });
    const data = {
        scmProductType: scmType.value,
        scmAsid: scmAccount.value,
    };
    if (!isAccountingAgent.value && scmStatus.value === 0) {
        request({
            url: `/api/ScmRelation/CancelRelation`,
            method: "post",
        }).then((res: any) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "取消关联成功",
                });
                initRelationTable();
                window.postMessage({ type: "refreshCurrent" }, window.location.origin);
                currentSlot.value = "main";
            } else {
                ElNotify({
                    type: "error",
                    message: "取消关联失败",
                });
            }
        });
        return false;
    }
    if (isAccountingAgent.value) {
        // 代账关联进销存
        request({
            url: `/api/ScmRelation/RelateForAA`,
            method: "post",
        })
            .then((res: any) => {
                if (res.state === 1000 && res.data) {
                    // 跳转进销存参数页面
                    globalWindowOpenPage("/Scm/ScmSettings", "进销存核算参数");
                    initRelationTable();
                    isNextStep.value = true;
                } else if (res.state === 2000) {
                    ElNotify({
                        type: "warning",
                        message: `<div style='text-align:left'><span >${res.msg}</span></div>`,
                    });
                } else {
                    ElNotify({
                        type: "error",
                        message: "保存失败，请刷新页面后重试",
                    });
                }
            })
            .catch((error) => {
                console.log(error);
                ElNotify({
                    type: "error",
                    message: "保存失败，请刷新页面后重试",
                });
            });
    } else {
        // 关联进销存
        request({
            url: `/api/ScmRelation/CheckScmRelation?` + getUrlSearchParams(data),
            method: "post",
        }).then((res: any) => {
            if (res.state !== 1000) {
                ElNotify({
                    type: "error",
                    message: "保存失败，请刷新页面后重试",
                });
                return;
            }
            if (res.data.relationStatus === 2) {
                relateInfo.value = res.data;
                relateDialog.value = true;
                return
            }
            if (res.data.relationStatus === 1) {
                relateInfo.value = res.data;
                relateBeforeDialog.value = true;
                return
            }
            saveScmRelation();
        });
    }
}

function saveScmRelation(isKeepBeforeSettings:boolean=false) {
    const data = {
        scmProductType: scmType.value,
        scmAsid: scmAccount.value,
        isKeepBeforeSettings,
    };
    request({
        url: `/api/ScmRelation/Relate?` + getUrlSearchParams(data),
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000 && res.data === true) {
                ElNotify({
                    type: "success",
                    message: "保存成功",
                });
                initRelationTable();
                window.postMessage({ type: "refreshCurrent" }, window.location.origin);
                globalLoadMenu();
                currentSlot.value = "main";
            } else if (res.state === 2000) {
                ElNotify({
                    type: "warning",
                    message: `<div style='text-align:left'><span >${res.msg}</span></div>`,
                });
            } else {
                ElNotify({
                    type: "error",
                    message: "保存失败，请刷新页面后重试",
                });
            }
        })
        .catch((error) => {
            console.log(error);
            ElNotify({
                type: "error",
                message: "保存失败，请刷新页面后重试",
            });
        });
}

function modifyHandle() {
    scmAccountBool.value = false;
    (document.getElementById("editRelationScm") as HTMLElement).style.display = "none";
}

const relationData = ref<any[]>([]);
//????
relationData.value = [
    {
        asName: "",
        asid: 0,
        scmAsName: "",
        isRelation: false,
        scmAsid: 0,
        scmProductType: 0,
    },
];

// 是否已关联
function initRelationTable() {
    request({
        url: `/api/ScmRelation/Info`,
    }).then((res: any) => {
        relationData.value = [res.data];
        title.value = res.data.asName;
        if (res.data.isRelation) {
            scmAccount.value = res.data.scmAsid;
            scmAsName.value = res.data.ScmAsName;
        } else {
            scmAccount.value = 0;
            scmAsName.value = res.data.asName;
        }
    });
}

function getScmAccount() {
    return request({
        url: `/api/ScmAccountSet/List`,
    });
}

function cancelEditRelation() {
    currentSlot.value = "main";
    scmAccount.value = scmAsid.value;
}

function scmStatusChange() {
    if (scmStatus.value === 1) {
        if (scmAccount.value === 0 && scmAccountList.value.length > 0) {
            scmAccount.value = scmAccountList.value[0].id;
        }
    }
}

onMounted(() => {
    checkIsRelation();
    Promise.all([getScmAccount(), initRelationTable()]).then((res) => {
        scmAccountList.value = res[0].data || [];
        if (res[0].data.length === 0) {
            scmAccountList.value = [{ name: "请到云进销存创建账套", id: "" }];
        } else if (scmAccount.value === 0) {
            scmAccount.value = res[0].data[0].id;
        }
    });
});

watch(
    scmStatus,
    (newValue) => {
        let elements = document.querySelectorAll(".item-scm");
        if (newValue) {
            for (let i = 0; i < elements.length; i++) {
                (elements[i] as HTMLElement).style.visibility = "visible";
            }
        } else {
            for (let i = 0; i < elements.length; i++) {
                (elements[i] as HTMLElement).style.visibility = "hidden";
            }
        }
    },
    { immediate: true }
);

const showScmAccountList = ref<Array<IScmAccountList>>([]);
watchEffect(() => {
    showScmAccountList.value = JSON.parse(JSON.stringify(scmAccountList.value));
});
function scmAccountFilterMethod(value: string) {
    showScmAccountList.value = commonFilterMethod(value, scmAccountList.value , 'name');
}
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/Scm/ScmRelation.less";

</style>
