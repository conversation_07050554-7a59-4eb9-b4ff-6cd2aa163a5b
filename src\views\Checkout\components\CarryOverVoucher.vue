<template>
    <div class="voucher-template fixedOverFlow" ref="carryOverVoucherRef" :class="{ 'double-voucher': createVoucherTemp2 }">
        <div class="stepTitle" :class="{ 'zoom-out': pageZoom === 'out', 'zoom-in': pageZoom === 'in' }">
            <span v-if="!isLastMonth">第 2 步：结转{{ props.asTheme }}</span>
            <span v-else>{{ title }}</span>
            <el-checkbox
                v-model="isClassify"
                :label="props.asTheme === '损益' ? '收入和成本分开结转' + props.asTheme : '分开结转' + props.asTheme"
                v-show="canClassify"
                @change="(check) => handleIsClassifyChange(check)"
            />
        </div>
        <div v-show="createVoucherTemp" class="single-voucher-content">
            <div class="voucher-content">
                <SingleVoucher
                    ref="singleVoucherRef"
                    v-model:query-params="singleVoucherQueryParams"
                    :get-disabled-date="getDisabledDate"
                    @voucher-changed="singleVoucherChanged"
                    @load-success="singloeVoucherLoadSuccess"
                    @zoom="handleZoomChange"
                    :hidden-button="true"
                    :hiddenLine="true"
                    from="checkout"
                />
            </div>
            <div class="check-box-tool-bar" v-if="!isLastMonth">
                <a class="button solid-button" @click="handleSingleSave" @mousedown="$event.stopPropagation()">保存凭证</a>
                <a class="button ml-10" @click="handleCancel" @mousedown="$event.stopPropagation()">取消</a>
            </div>
            <div class="check-box-tool-bar" v-else>
                <a class="button solid-button" @click="handleYearSave" @mousedown="$event.stopPropagation()">保存凭证</a>
                <a class="button solid-button large-2 ml-10" @click="handleYearNotSave" @mousedown="$event.stopPropagation()"
                    >不保存，下一步</a
                >
                <a class="button ml-10" @click="handleCancel" @mousedown="$event.stopPropagation()">上一步</a>
            </div>
        </div>
        <div v-show="createVoucherTemp2" class="double-voucher-content">
            <div class="voucher-content">
                <DoubleVoucher1
                    ref="doubleVoucher1Ref"
                    v-model:query-params="doubleVoucher1QueryParams"
                    :get-disabled-date="getDisabledDate"
                    @voucher-changed="double1VoucherChanged"
                    @load-success="double1VoucherLoadSuccess"
                    @zoom="handleZoomChange"
                    :hidden-button="true"
                    :hiddenLine="true"
                    from="checkout"
                />
                <DoubleVoucher2
                    ref="doubleVoucher2Ref"
                    v-model:query-params="doubleVoucher2QueryParams"
                    :get-disabled-date="getDisabledDate"
                    @voucher-changed="double2VoucherChanged"
                    @load-success="double2VoucherLoadSuccess"
                    @zoom="handleZoomChange"
                    :hidden-button="true"
                    :hiddenLine="true"
                    from="checkout"
                />
            </div>
            <div class="check-box-tool-bar">
                <a class="button solid-button" @click="handleDoubleleSave" @mousedown="$event.stopPropagation()">保存凭证</a>
                <a class="button ml-10" @click="handleCancel" @mousedown="$event.stopPropagation()">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { ElNotify } from "@/util/notify";

import {
    VoucherSaveModel,
    VoucherSaveParams,
    VoucherEntryModel,
    VoucherQueryParams,
    DataVoucherQueryParams,
} from "@/components/Voucher/types";

import type { IResponseModel } from "@/util/service";

import SingleVoucher from "@/components/Voucher/index.vue";
import DoubleVoucher1 from "@/components/Voucher/index.vue";
import DoubleVoucher2 from "@/components/Voucher/index.vue";

import {
    VoucherType,
    conbineincomeRows,
    conbineRestrictedRows,
    conbineOnRestrictedRows,
    conbineRestrictedRowsUnion,
    conbineincomeRowsUnion,
} from "../utils";
import type { IConbineIncomeRows, ICarryOverBack, IAccountingSubject } from "../tpyes";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import { watch } from "vue";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
import { getGlobalLodash } from "@/util/lodash";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { useAsubCodeLengthStore } from "@/store/modules/asubCodeLength";

const accountingStandard = useAccountSetStore().accountSet?.accountingStandard as number;
const _ = getGlobalLodash();
const singleVoucherRef = ref<InstanceType<typeof SingleVoucher>>();
const doubleVoucher1Ref = ref<InstanceType<typeof DoubleVoucher1>>();
const doubleVoucher2Ref = ref<InstanceType<typeof DoubleVoucher2>>();

const singleVoucherQueryParams = ref<VoucherQueryParams>();
const doubleVoucher1QueryParams = ref<VoucherQueryParams>();
const doubleVoucher2QueryParams = ref<VoucherQueryParams>();

const doubleVoucher1QueryParamsBack = ref<VoucherQueryParams>();
const doubleVoucher2QueryParamsBack = ref<VoucherQueryParams>();

const carryOverVoucherRef = ref<HTMLDivElement>();

const props = defineProps<{
    pageChange: Function;
    getVDate: Function;
    asTheme: string;
    pid: number;
}>();

function getDisabledDate(current: Date) {
    const periodList = useAccountPeriodStore().periodList;
    const periodInfo = periodList.find((item) => item.pid === props.pid);
    const p_s = new Date(periodInfo!.year, periodInfo!.sn - 1, 1); 
    const p_e = new Date(periodInfo!.year, periodInfo!.sn, 0);
    //这个信息不是有satrtDate和endDate,怎么还要自己算
    // const p_s = periodInfo?.year + "-" + periodInfo?.sn + "-01";
    // const p_e = periodInfo?.year + "-" + periodInfo?.sn + "-" + getDaysInMonth(periodInfo?.year as number, periodInfo?.sn as number);
    return current.getTime() < new Date(p_s).getTime() || current.getTime() > new Date(p_e).getTime();
}

const isClassify = ref(false);
const canClassify = ref(false);
const createVoucherTemp = ref(true);
const createVoucherTemp2 = ref(false);
const voucherGroupStore = useVoucherGroupStore();
const emit = defineEmits(["back", "listBack", "save-success", "year-save-success", "year-not-save"]);
const backType = ref<"listBack" | "back">("back");

const edited = ref(false);

let singleCanPageChange = false;
function singloeVoucherLoadSuccess() {
    singleCanPageChange && props.pageChange();
    edited.value = false;
}
const singleVoucherChanged = () => {};

let double1CanPageChange = false;
function double1VoucherLoadSuccess() {
    double1CanPageChange && double2CanPageChange && props.pageChange();
    edited.value = false;
}
const double1VoucherChanged = () => {
    edited.value = true;
};
let double2CanPageChange = false;
function double2VoucherLoadSuccess() {
    double1CanPageChange && double2CanPageChange && props.pageChange();
    edited.value = false;
    const voucherModel = doubleVoucher2Ref.value?.getVoucherModel();
    if (voucherModel) {
        voucherModel.vnum = (Number(voucherModel?.vnum) || 0) + 1;
        doubleVoucher2QueryParams.value = voucherModel;
    }
}
const double2VoucherChanged = () => {
    edited.value = true;
};

const reset = () => {
    singleCanPageChange = false;
    double1CanPageChange = false;
    double2CanPageChange = false;
    backType.value = "back";
    isLastMonth.value = false;
    title.value = "";
    singleVoucherQueryParams.value = new VoucherQueryParams();
    doubleVoucher1QueryParams.value = new VoucherQueryParams();
    doubleVoucher2QueryParams.value = new VoucherQueryParams();
    doubleVoucher1QueryParamsBack.value = new VoucherQueryParams();
    doubleVoucher2QueryParamsBack.value = new VoucherQueryParams();
    edited.value = false;
};
const resetTotalMoney = () => {
    singleVoucherRef.value?.resetAsubTotalAmount();
    doubleVoucher1Ref.value?.resetAsubTotalAmount();
    doubleVoucher2Ref.value?.resetAsubTotalAmount();
};
const handleCancel = () => {
    singleVoucherRef.value?.removeEventListener();
    doubleVoucher1Ref.value?.removeEventListener();
    doubleVoucher2Ref.value?.removeEventListener();
    backType.value === "back" ? emit("back") : emit("listBack");
    setTimeout(() => {
        reset();
        useFullScreenStore().changeFullScreenStage(false);
    }, 500);
};
const handleSaveSuccess = (emitEvent: "save-success" | "year-save-success" = "save-success") => {
    emit(emitEvent);
    useFullScreenStore().changeFullScreenStage(false);
};
let singleCanSave = true;
const handleYearSave = () => {
    singleSave("year-save-success");
};
const handleYearNotSave = () => {
    singleVoucherRef.value?.removeEventListener();
    doubleVoucher1Ref.value?.removeEventListener();
    doubleVoucher2Ref.value?.removeEventListener();
    emit("year-not-save");
};
const singleSave = (emit: "save-success" | "year-save-success" = "save-success") => {
    if (!singleCanSave) return;
    singleCanSave = false;
    singleVoucherRef.value?.saveVoucher(
        new VoucherSaveParams(1057, (res: IResponseModel<VoucherSaveModel>) => {
            if (res.state === 1000) {
                const voucherModel = singleVoucherRef.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({ message: "亲，保存成功啦！", type: "success" });
                }
                handleSaveSuccess(emit);
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({ message: res.msg, type: "warning" });
                }
            } else if (res.state === 9999) {
                ElNotify({ message: "保存失败", type: "warning" });
            }
            const timer = setTimeout(() => {
                singleCanSave = true;
                clearTimeout(timer);
            }, 1000);
        })
    );
};
const handleSingleSave = () => {
    singleSave();
};
const codeLength = computed(() => {
    return useAsubCodeLengthStore().codeLength;
});
const profitDistributionUndistributedProfitCode = computed(() => {
    return "4104" + "0".repeat(codeLength.value[1] - 1) + "6";
});
let doubleCanSave = true;
const handleDoubleleSave = () => {
    if (!doubleCanSave) return;
    if (accountingStandard === AccountStandard.CompanyStandard) {
        let hasPriorYearSunyiAdjustment = false;
        let hasProfitDistributionUndistributedProfit = false;
        for (let j = 0; j < doubleVoucher2QueryParams.value.voucherLines.length; j++) {
            let voucherline = doubleVoucher2QueryParams.value.voucherLines[j];
            if (voucherline.asubCode.indexOf("6901") === 0) {
                hasPriorYearSunyiAdjustment = true;
            }
            if (voucherline.asubCode.indexOf(profitDistributionUndistributedProfitCode.value) === 0) {
                hasProfitDistributionUndistributedProfit = true;
            }
        }
        if (!hasPriorYearSunyiAdjustment && hasProfitDistributionUndistributedProfit) {
            handleDouble2VoucherSave();
            return;
        }
    }
    // 第二张如果不合法的话先走一遍校验逻辑给出提示
    if (doubleVoucher1Ref.value?.warningRowIndex === -1 && doubleVoucher2Ref.value?.warningRowIndex !== -1) {
        handleDouble2VoucherSave();
        return;
    }

    doubleCanSave = false;
    doubleVoucher1Ref.value?.saveVoucher(
        new VoucherSaveParams(1057, (res: IResponseModel<VoucherSaveModel>) => {
            if (res.state === 1000) {
                const voucherModel = doubleVoucher1Ref.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                }
                handleDouble2VoucherSave();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({ message: res.msg, type: "warning" });
                }
                doubleCanSave = true;
            } else if (res.state === 9999) {
                ElNotify({ message: "保存失败", type: "warning" });
                doubleCanSave = true;
            }
        })
    );
};
const handleDouble2VoucherSave = () => {
    doubleVoucher2Ref.value?.saveVoucher(
        new VoucherSaveParams(1057, (res: IResponseModel<VoucherSaveModel>) => {
            if (res.state === 1000) {
                const voucherModel = doubleVoucher2Ref.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({ message: "亲，保存成功啦！", type: "success" });
                }
                handleSaveSuccess();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({ message: res.msg, type: "warning" });
                }
            } else if (res.state === 9999) {
                ElNotify({ message: "保存失败", type: "warning" });
            }
            doubleCanSave = true;
        })
    );
};

function initCreateChangeOutVoucher(flag: boolean, rows?: IConbineIncomeRows) {
    backType.value = "listBack";
    if (rows == undefined || rows.contactRows?.length == 0) {
        rows = { contactRows: [], rowsExpanse: [], rowsIncome: [] };
    }
    isClassify.value = false;
    createVoucherTemp.value = true;
    createVoucherTemp2.value = false;
    singleVoucherQueryParams.value = getCheckoutVoucherModel(rows.contactRows as VoucherEntryModel[]);
    if (rows.rowsIncome?.length == 0 || rows.rowsExpanse?.length == 0) {
        canClassify.value = false;
        singleCanPageChange = true;
    } else {
        doubleVoucher1QueryParamsBack.value = getCheckoutVoucherModel(rows.rowsIncome as VoucherEntryModel[]);

        const doubleVoucherModelTwo = getCheckoutVoucherModel(rows.rowsExpanse as VoucherEntryModel[]);
        doubleVoucher2QueryParamsBack.value = doubleVoucherModelTwo;
        canClassify.value = true;
        double1CanPageChange = true;
        double2CanPageChange = true;
    }
}

const getCheckoutVoucherModel = (voucherLines: VoucherEntryModel[], vnum?: number) => {
    const defaultVgId = voucherGroupStore.defaultVgId;
    const voucherModel = new DataVoucherQueryParams(voucherLines);
    voucherModel.vgId = defaultVgId;
    const vdate = props.getVDate();
    voucherModel.vdate = vdate;
    if (vnum !== undefined) {
        voucherModel.vnum = vnum;
    }
    voucherModel.vtype = VoucherType.profitLossChange;
    return voucherModel;
};
function dealWithCheckOutCreateVouchers(rdata: ICarryOverBack[]) {
    const cr = conbineincomeRows(rdata);
    initCreateChangeOutVoucher(true, cr);
    backType.value = "back";
}

function dealWithFolkCheckOutExistsVoucher(flag: boolean, rdata: ICarryOverBack[]) {
    dealWithFolkCheckOut(flag, rdata);
    backType.value = "listBack";
}

function dealWithFolkCheckOutCreateVouchers(flag: boolean, rdata: ICarryOverBack[]) {
    dealWithFolkCheckOut(flag, rdata);
    backType.value = "back";
}

function dealWithFolkCheckOut(flag: boolean, rdata: ICarryOverBack[]) {
    if (rdata == undefined || rdata.length == 0) {
        return;
    }
    const onRestrictedCR = conbineOnRestrictedRows(rdata);
    const restrictedCR = conbineRestrictedRows(rdata);

    if (onRestrictedCR.length !== 0 && restrictedCR.length !== 0) {
        initCreateDoubleChangeOutVoucher(flag, onRestrictedCR, restrictedCR);
    } else {
        const cr = onRestrictedCR.length !== 0 ? onRestrictedCR : restrictedCR;
        const type = onRestrictedCR.length !== 0 ? VoucherType.onRestrictedNetWorth : VoucherType.restrictedNetWorth;
        initCreateSingleChangeOutVoucher(flag, cr, type);
    }
}

function initCreateDoubleChangeOutVoucher(flag: boolean, rowsOne: VoucherEntryModel[], rowsTwo: VoucherEntryModel[]) {
    if (rowsOne == undefined || rowsOne.length == 0) {
        rowsOne = [];
    }

    if (rowsTwo == undefined || rowsTwo.length == 0) {
        rowsTwo = [];
    }
    const voucherModelOne = getCheckoutVoucherModel(rowsOne);
    voucherModelOne.vtype = rowsOne.length === 0 ? 0 : VoucherType.onRestrictedNetWorth;

    const voucherModelTwo = getCheckoutVoucherModel(rowsTwo);
    voucherModelTwo.vtype = rowsTwo.length === 0 ? 0 : VoucherType.restrictedNetWorth;
    // voucherModelTwo.vnum = (Number(voucherModelTwo.vnum) || 0) + 1;

    doubleVoucher1QueryParamsBack.value = voucherModelOne;
    doubleVoucher2QueryParamsBack.value = voucherModelTwo;

    createVoucherTemp.value = false;
    createVoucherTemp2.value = true;
    canClassify.value = false;
    isClassify.value = true;
    handleIsClassifyChange(true);
    double1CanPageChange = true;
    double2CanPageChange = true;
}

function dealWithUnionCheckOutCreateVouchers(rdata: ICarryOverBack[]) {
    if (rdata === undefined || rdata.length === 0) return;
    const restrictedCRIncome = conbineRestrictedRowsUnion(_.cloneDeep(rdata), 0);
    const restrictedCRExpance = conbineRestrictedRowsUnion(_.cloneDeep(rdata), 1);
    if (restrictedCRIncome.length !== 0 && restrictedCRExpance.length !== 0) {
        // 合并成一张的凭证
        const restrictedCRIncomeClone = _.cloneDeep(restrictedCRIncome);
        const restrictedCRExpanceClone = _.cloneDeep(restrictedCRExpance);
        let restrictedCRContactRows = conbineincomeRowsUnion(restrictedCRIncomeClone, restrictedCRExpanceClone);
        if (restrictedCRContactRows === undefined || restrictedCRContactRows.length === 0) {
            restrictedCRContactRows = [];
        }
        // 显示合并后的单张凭证
        const voucherModel = getCheckoutVoucherModel(restrictedCRContactRows.slice());
        voucherModel.vtype = VoucherType.profitLossChange;
        singleVoucherQueryParams.value = voucherModel;
        createVoucherTemp.value = true;
        createVoucherTemp2.value = false;
        canClassify.value = true;
        isClassify.value = false;
        singleCanPageChange = true;
        // 备份两张凭证的数据
        const voucherModelOne = getCheckoutVoucherModel(restrictedCRIncome.slice());
        voucherModelOne.vtype = VoucherType.profitLossChange;

        const voucherModelTwo = getCheckoutVoucherModel(restrictedCRExpance.slice());
        voucherModelTwo.vtype = VoucherType.profitLossChange;

        doubleVoucher1QueryParamsBack.value = voucherModelOne;
        doubleVoucher2QueryParamsBack.value = voucherModelTwo;
    } else {
        if (restrictedCRIncome.length !== 0) {
            initCreateSingleChangeOutVoucher(true, restrictedCRIncome, VoucherType.profitLossChange);
        } else if (restrictedCRExpance.length !== 0) {
            initCreateSingleChangeOutVoucher(true, restrictedCRExpance, VoucherType.profitLossChange);
        }
    }
}

function initCreateSingleChangeOutVoucher(flag: boolean, rows: VoucherEntryModel[], type: VoucherType) {
    if (rows == undefined || rows.length == 0) {
        rows = [];
    }
    const voucherModel = getCheckoutVoucherModel(rows);
    voucherModel.vtype = type;
    singleVoucherQueryParams.value = voucherModel;
    createVoucherTemp.value = true;
    createVoucherTemp2.value = false;
    canClassify.value = false;
    isClassify.value = false;
    singleCanPageChange = true;
}

const isLastMonth = ref(false);
const title = ref("");
function createUnchangeVouchers(asub: IAccountingSubject[]) {
    backType.value = "listBack";
    isLastMonth.value = true;
    let rows: Array<VoucherEntryModel> = [];
    title.value = "第 2 步 结转损益-结转未分配利润";
    let description = "年末结转损益";
    switch (accountingStandard) {
        case AccountStandard.FarmerCooperativeStandard:
            title.value = "第 2 步 结转盈余-结转未分配盈余";
            description = "年末结转盈余";
            break;
        case AccountStandard.UnionStandard:
            title.value = "第 2 步 工会资金结转结余、财政拨款结转结余";
            description = "年末结转净资产";
            break;
        case AccountStandard.VillageCollectiveEconomyStandard:
            title.value = "第 2 步 结转损益-结转未分配收益";
            break;
        default:
            break;
    }
    if (typeof asub === "undefined") {
        rows = [];
    } else {
        rows = map2VoucherModel(asub, description);
    }
    canClassify.value = false;
    isClassify.value = false;
    const voucherModel = getCheckoutVoucherModel(rows);
    voucherModel.vtype = VoucherType.yearProfitLoss;
    createVoucherTemp.value = true;
    createVoucherTemp2.value = false;
    singleVoucherQueryParams.value = voucherModel;
}

function map2VoucherModel(asub: Array<IAccountingSubject>, description: string) {
    const rows: Array<VoucherEntryModel> = [];
    for (let k = 0; k < asub.length; k++) {
        const row = new VoucherEntryModel();
        row.asubId = asub[k].asubid;
        row.asubCode = asub[k].asubcode;
        row.asubName = asub[k].asubcode + "    " + asub[k].asubname;
        row.fcId = asub[k].fcid;
        row.fcRate = asub[k].fcrate;
        row.fcAmount = asub[k].sumFcValue;
        row.foreigncurrency = Number(asub[k].foreigncurrency) || 0;
        // 贷方
        if (asub[k].derection === 0) {
            if (asub[k].sumValue > 0) {
                row.credit = asub[k].sumValue;
                row.debit = 0;
            } else {
                row.debit = asub[k].sumValue;
                row.credit = 0;
            }
        } else {
            // 借方
            if (asub[k].sumValue > 0) {
                row.credit = 0;
                row.debit = asub[k].sumValue;
            } else {
                row.debit = 0;
                row.credit = asub[k].sumValue;
            }
        }
        row.description = description;
        rows.push(row);
    }
    return rows;
}

const voucherChanged = () => {
    return edited.value;
};

defineExpose({
    dealWithCheckOutCreateVouchers,
    initCreateChangeOutVoucher,
    dealWithFolkCheckOutCreateVouchers,
    dealWithFolkCheckOutExistsVoucher,
    createUnchangeVouchers,
    reset,
    resetTotalMoney,
    voucherChanged,
    dealWithUnionCheckOutCreateVouchers,
});

const handleIsClassifyChange = (type: any) => {
    const check = !!type;
    if (check) {
        createVoucherTemp2.value = true;
        createVoucherTemp.value = false;
        doubleVoucher1QueryParams.value = doubleVoucher1QueryParamsBack.value;
        doubleVoucher2QueryParams.value = doubleVoucher2QueryParamsBack.value;
    } else {
        createVoucherTemp2.value = false;
        createVoucherTemp.value = true;
    }
};

const pageZoom = ref<"in" | "out">("out");
const checkoutZoom = ref("");
const handleZoomChange = (zoomState: "in" | "out") => {
    pageZoom.value = zoomState;
    checkoutZoom.value = zoomState;
};

watch(pageZoom, (zoomState) => {
    singleVoucherRef.value?.changeZoomState(zoomState);
});

watch(checkoutZoom, () => {
    doubleVoucher1Ref.value?.changeZoomState(pageZoom.value);
    doubleVoucher2Ref.value?.changeZoomState(pageZoom.value);
});
</script>

<style lang="less" scoped>
.voucher-template {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .stepTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 32px;
        margin: 20px 54px;
        box-sizing: border-box;
        flex-shrink: 0;
        :deep(.el-checkbox__label) {
            color: #3385ff !important;
        }
        > span {
            color: var(--font-color);
            font-size: 24px;
            line-height: 30px;
        }
        &.zoom-out {
            width: calc(100% - 108px);
        }
        &.zoom-in {
            width: 1050px;
            margin: 20px auto;
        }
    }
    .single-voucher-content {
        display: flex;
        flex-direction: column;
        align-items: stretch;

        .voucher-content {
            flex: 1;
        }
    }

    .double-voucher-content {
        display: flex;
        flex-direction: column;
        align-items: stretch;
    }
    .check-box-tool-bar {
        margin: 40px 0;
    }
}
</style>
