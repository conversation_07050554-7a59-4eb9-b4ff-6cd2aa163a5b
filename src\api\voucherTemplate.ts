import { request, type IResponseModel } from "@/util/service";
import { VoucherEntryModel, type VoucherLineSaveModel } from "@/components/Voucher/types";

export class VoucherTemplateModel {
    vtId = 0;
    vtName = "";
    vtType = 0;
    vgId = 0;
    hasErrorVoucherLines = false;
    voucherLines = new Array<VoucherTemplateEntryModel>();
}

export class VoucherTemplateEntryModel extends VoucherEntryModel {
    logicEnum = VoucherTemplateLogicEnum.Empty;
    valueType = 0;
}

export const createVoucherTemplate = (data: VoucherTemplateSaveModel) => {
    return request({
        url: "/api/VoucherTemplate",
        method: "post",
        data: data,
    }) as any as Promise<IResponseModel<number>>;
};

export const updateVoucherTemplate = (data: VoucherTemplateSaveModel) => {
    return request({
        url: "/api/VoucherTemplate",
        method: "put",
        data: data,
    }) as any as Promise<IResponseModel<boolean>>;
};

export const deleteVoucherTemplate = (vtId: number) => {
    return request({
        url: "/api/VoucherTemplate?vtId=" + vtId,
        method: "delete",
    }) as any as Promise<IResponseModel<boolean>>;
};

export class VoucherTemplateSaveModel {
    //模板Id
    vtId: number = 0;
    //模板类型
    vtType: number = 0;
    //模板编码
    vtCode: string = "";
    //模板名称
    vtName: string = "";
    //是否是默认模板
    vtDefault: boolean = false;
    //凭证字
    vgId: number = 0;
    //凭证
    voucherLines: Array<VoucherTemplateLineSaveModel> = [];
}

export class VoucherTemplateLineSaveModel {
    constructor(voucherLineSaveModel?: VoucherLineSaveModel) {
        if (!voucherLineSaveModel) {
            this.description = "";
            this.asubId = 0;
            this.debit = 0;
            this.credit = 0;
            this.quantityAccounting = 0;
            this.quantity = 0;
            this.price = 0;
            this.foreigncurrency = 0;
            this.fcId = 0;
            this.fcRate = 0;
            this.fcAmount = 0;
            this.assistingAccounting = 0;
            this.aacode = "";
            this.logicEnum = VoucherTemplateLogicEnum.Empty;
            this.valueType = 0;
            this.direction = 0;
        } else {
            this.description = voucherLineSaveModel.description;
            this.asubId = voucherLineSaveModel.asubId;
            this.debit = voucherLineSaveModel.debit;
            this.credit = voucherLineSaveModel.credit;
            this.quantityAccounting = voucherLineSaveModel.quantityAccounting;
            this.quantity = voucherLineSaveModel.quantity;
            this.price = voucherLineSaveModel.price;
            this.foreigncurrency = voucherLineSaveModel.foreigncurrency;
            this.fcId = voucherLineSaveModel.fcId;
            this.fcRate = voucherLineSaveModel.fcRate;
            this.fcAmount = voucherLineSaveModel.fcAmount;
            this.assistingAccounting = voucherLineSaveModel.assistingAccounting;
            this.aacode = voucherLineSaveModel.aacode;
            this.logicEnum = VoucherTemplateLogicEnum.Empty;
            this.valueType = 0;
            this.direction = voucherLineSaveModel.direction;
        }
    }
    //摘要
    description: string;
    //科目Id
    asubId: number;
    //借方金额
    debit: number;
    //贷方金额
    credit: number;
    //是否开启数量核算
    quantityAccounting: number;
    //数量
    quantity: number;
    //单价
    price: number;
    //是否开启外币核算
    foreigncurrency: number;
    //外币Id
    fcId: number;
    //汇率
    fcRate: number;
    //原币
    fcAmount: number;
    //是否开启辅助核算
    assistingAccounting: number;
    //辅助核算Id，逗号分隔
    aacode: string;
    //取值逻辑
    logicEnum: VoucherTemplateLogicEnum;
    //取值类型
    valueType: number;
    //方向
    direction?: number;
}

export enum VoucherTemplateLogicEnum {
    Empty = 0,
    TotalAsub = 1010,
    DetailAsub = 1020,
    AssisAsub = 1030,
}
