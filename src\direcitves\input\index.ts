import type { Directive } from "vue";

interface ElementWithValue extends HTMLElement {
    value: string;
}

function integer(el: ElementWithValue): void {
    el.value = el.value.replace(/[^\d]/g, '');
}

const map: { [key: string]: (el: ElementWithValue) => void } = { integer }

export const input: Directive = {
    mounted(el, binding,) {
    el = el.querySelector(".el-input__inner") || el.querySelector(".el-textarea__inner") || el;
    let lock = false; // 标记是否需要锁定输入框
    let isHandling = false; // 标记是否正在处理
    let lastValue: string | null = null;
    const handler = () => {
      if (lock) return; 
      if (isHandling) return; 
      if (el.value === lastValue) return; 
      isHandling = true; 
      const modifiers = Object.keys(binding.modifiers);
      const newModifier = modifiers[0] || "integer";
      if (map[newModifier as keyof typeof map]) {
        map[newModifier](el);
      }
      lastValue = el.value; 
      Promise.resolve().then(() => {
        el.dispatchEvent(new Event("input"));
      });
      isHandling = false;
    };
    el.addEventListener("input", handler);
    // compositionstart和compositionend事件解决的bug场景：限制只能输入数字的输入框，先输入数字，再切换为中文输入法，输入字母时，会将原来的数字删掉
    el.addEventListener("compositionstart", () => {
      lock = true;
    });
    el.addEventListener("compositionend", () => {
      lock = false;
      el.dispatchEvent(new Event("input"));
    });
    // 当指令与元素解绑时，移除事件监听器
    el._vnode.el.$once("hook:destroyed", () => {
      el.removeEventListener("input", handler);
    });
  },
};
