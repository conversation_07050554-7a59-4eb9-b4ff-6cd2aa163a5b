import { createApp } from "vue"
import PercentageLoading from "@/components/PercentageLoading/index.vue"
import ElementPlus from "element-plus"

export type LoadingArg = {
  showConfirmBtn?: boolean
  width?: number
  dialogTitle?: string
  alignCenter?: boolean
  bottomTipMsg?: string
  percentageShowText?: boolean
  strokeWidth?: number
}

export const percentageLoading = (options?: LoadingArg): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    const props = {
      ...options,
      close: () => {
        resolve(false)
        capp.unmount()
        document.body.removeChild(container)
      },
      confirm: () => {
        resolve(true)
        capp.unmount() //注销
        document.body.removeChild(container) //点击后清除弹窗
      },
      cancel: () => {
        resolve(false)
        capp.unmount()
        document.body.removeChild(container)
      },
    }
    const capp = createApp(PercentageLoading, props)
    const container = document.createElement("div")
    capp.use(ElementPlus)
    capp.mount(container)
    document.body.insertBefore(container, document.body.firstChild) //插入到body最前面，层级更高
  })
}
