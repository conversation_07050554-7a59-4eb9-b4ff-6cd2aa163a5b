<script lang="ts">
export default {
    name: "GeneralLedger",
};
</script>
<script lang="ts" setup>
import Dropdown from "@/components/Dropdown/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { provide, reactive, ref, watch, onMounted, nextTick,onUnmounted, watchEffect} from "vue";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { componentFinishKey } from "@/symbols";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISearchParams, ItableData, IR<PERSON>, <PERSON>c<PERSON>ist, IAsubCodeLength } from "./types";
import { getAsubInfo } from "@/api/getAsubInfo";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import TreeTable from "@/components/Table/TreeTable.vue";
import { handleAsubName } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { changeColumnName } from "./utils";
import { goToScrollTop } from "@/util/common";
import { useRoute } from "vue-router";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo, initStartOrEndMonth } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import usePrint from "@/hooks/usePrint";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "GeneralLedger";
const route = useRoute();
const periodStore = useAccountPeriodStore();
const loading = ref<boolean>(false);
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const startAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const endAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const printDisable = ref(false);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
let treeTable = ref();
const treeEmptyText = ref(" ");
const searchInfo = reactive({
    startPid: Number(periodStore.getPeriodRange().start),
    endPid: Number(periodStore.getPeriodRange().end),
    startAsubCode: "",
    endAsubCode: "",
    startAsubLevel: 1,
    endAsubLevel: 4,
    /** 显示辅助核算 */
    showAssist: false,
    /** 余额为0不显示 */
    balanceZeroUnShow: false,
    /** 无发生额且余额为0不显示 */
    noTransAndZeroBalUnShow: false,
    /** 无发生额不显示本期合计、本年累计 */
    noOccurrenctHiddenTotal: false,
    /** 显示数量金额 */
    showQuantity: false,
    fcid: -1,
    /** 联查总账科目列表 */
    SubjectIDList: "",
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, searchInfo.startPid, searchInfo.endPid);  
const getActPid = (start: number, end: number) => {
    searchInfo.startPid = start;
    searchInfo.endPid = end;
}
function updateRouteFateMonth() {
    const result = initStartOrEndMonth(periodData.value, searchInfo.startPid, searchInfo.endPid);  
    searchInfo.startMonth = result.startMonth;  
    searchInfo.endMonth = result.endMonth;  
}

const isErp = ref(window.isErp);
function handleReset() {
    searchInfo.startAsubCode = "";
    searchInfo.endAsubCode = "";
    searchInfo.startAsubLevel = 1;
    searchInfo.endAsubLevel = maxCodelength.value;
    searchInfo.showAssist = false;
    searchInfo.balanceZeroUnShow = false;
    searchInfo.noOccurrenctHiddenTotal = false;
    searchInfo.fcid = -1;
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    searchInfo.noTransAndZeroBalUnShow = false;
}

const useGreenHeader = ref(false);
const columns = ref<Array<IColumnProps>>([]);

const searchParams: ISearchParams = {
    period_s: Number(periodStore.getPeriodRange().start),
    period_e: Number(periodStore.getPeriodRange().end),
    sbj_id_s: '',
    sbj_id_e: '',
    sbj_leval_s: 1,
    sbj_leval_e: 4,
    fcid: searchInfo.fcid,
    /** 显示辅助核算 */
    assistAccount: 0,
    /** 余额为0不显示 */
    IsBalanceZero: 0,
    /** 无发生额不显示本期合计、本年累计 */
    hiddenTotal: 0,
    /** 显示数量金额 */
    showNumber: 0,
    /** 无发生额且余额为0不显示 */
    NoAmountIncurredAndBalanceZero: 0,
    /** 联查总账科目列表 */
    SubjectIDList: '',
};
function setParams(params: ISearchParams) {
    if (route.query.SubjectIDList && !searchInfo.startAsubCode && !searchInfo.endAsubCode) {
        searchInfo.SubjectIDList = String(route.query.SubjectIDList);
    } else {
        searchInfo.SubjectIDList = "";
    }
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (
        searchInfo.startAsubCode !== "" &&
        searchInfo.endAsubCode !== "" &&
        searchInfo.startAsubCode.split(" ")[0] > searchInfo.endAsubCode.split(" ")[0]
    ) {
        ElNotify({
            message: "亲，起始科目不能大于结束科目哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.startAsubLevel > searchInfo.endAsubLevel) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    params.period_s = searchInfo.startPid;
    params.period_e = searchInfo.endPid;
    params.sbj_id_s = searchInfo.startAsubCode.split(" ")[0];
    params.sbj_id_e = searchInfo.endAsubCode.split(" ")[0];
    params.sbj_leval_s = searchInfo.startAsubLevel;
    params.sbj_leval_e = searchInfo.endAsubLevel;
    /** 显示辅助核算 */
    params.assistAccount = searchInfo.showAssist ? 1 : 0;
    /** 余额为0不显示 */
    params.IsBalanceZero = searchInfo.balanceZeroUnShow ? 1 : 0;
    /** 无发生额且余额为0不显示 */
    params.NoAmountIncurredAndBalanceZero = searchInfo.noTransAndZeroBalUnShow ? 1 : 0;
    /** 无发生额不显示本期合计、本年累计 */
    params.hiddenTotal = searchInfo.noOccurrenctHiddenTotal ? 1 : 0;
    /** 显示数量金额 */
    params.showNumber = searchInfo.showQuantity ? 1 : 0;
    params.fcid = searchInfo.fcid;
    params.SubjectIDList = searchInfo.SubjectIDList;
    return true;
}

let accountSubjectList = useAccountSubjectStore().accountSubjectList;
let startAsubFull = "";
let endAsubFull = "";
function setInfos() {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
    if (route.query.SubjectIDList && !(searchInfo.startAsubCode) && !(searchInfo.endAsubCode) ) {
        let SubjectIDListName = String(route.query.SubjectIDList).split(",");
        let result: string[] = [];
        SubjectIDListName.forEach((code) => {
            accountSubjectList.forEach((item) => {
                if (Number(item.asubId) === Number(code)) {
                    result.push(item.asubCode + " " + item.asubAAName);
                }
            });
        });
        asubInfo.value = result.join(",");
    } else {
        startAsubFull = searchInfo.startAsubCode ? startAsubFull : "";
        endAsubFull = searchInfo.endAsubCode ? endAsubFull : "";
        asubInfo.value = getAsubInfo(startAsubFull, endAsubFull);
        // asubInfo.value = getAsubInfo(startAsubRef.value?.asubName, endAsubRef.value?.asubName);
    }
}
const handleAsubChange = (asubId: number, type: string) => {
    let findItem = accountSubjectList.filter((item) => item.asubId == asubId)[0];
    if (type === "startAsub") {
        startAsubFull = findItem.asubCode + " " + findItem.asubAAName;
    }
    if (type === "endAsub") {
        endAsubFull = findItem.asubCode + " " + findItem.asubAAName;
    }
};

const tableData = ref<IRows[]>([]);
const getTableData = () => {
    loading.value = true;
    columns.value = changeColumnName(searchInfo.showQuantity, searchParams.fcid + "");
    const params = {
        ...searchParams,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
    };
    request({
        url: "/api/GeneralLedger",
        method: "get",
        params: params,
    })
        .then((res: IResponseModel<ItableData>) => {
            loading.value = false;
            goToScrollTop();
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            treeEmptyText.value = res.data.total === 0 ? "暂无数据" : " ";
        })
        .catch((error) => {
            loading.value = false;
            tableData.value = [];
            paginationData.total = 0;
        })
        .finally(() => {
            treeTable.value?.initData(tableData.value);
            loading.value = false;
        });
};

function handleSearch() {
    if (setParams(searchParams)) {
        periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
        nextTick(() => {
            setInfos();
        });
        if (paginationData.currentPage === 1) {
            getTableData();
        }
        paginationData.currentPage = 1;
        handleClose();
    }
}
function handleSearchConfirm() {
    globalWindowOpenPage("/AccountBooks/GeneralLedger", "总账");
    handleSearch();
}
// 切换回页面，数据刷新
watch(
    () => route.query.SubjectIDList,
    () => {
        initEnter();
    }
);
function handleClose() {
    containerRef.value?.handleClose();
}

const closePopover = () => {
    if (!containerRef.value?.popoverShow) {
        startAsubRef.value?.handleClose();
        endAsubRef.value?.handleClose();
    }
};

const extraInfo = {
    isTitleSubjectShowLeaf: false,
    isHideSubjectCode: false,
    isHideSubject: false, 
    basepage: false,
    printCatalog: true,
    continuePrint: false,
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "generalLedger",
    window.jAccountBooksHost + "/api/GeneralLedger/Print",
    extraInfo,
    true,
    true,
    printValidator
);

otherOptions.value = [
    { key: "isTitleSubjectShowLeaf", label: "表头科目名称只打印末级" },
    { key: "isHideSubjectCode", label: "表体不打印科目编码" },
    { key: "isHideSubject", label: "表体不打印科目名称" },
    { key: "basepage", label: "打印封面" },
    { key: "printCatalog", label: "打印目录" },
    ...otherOptions.value,
    { key: "continuePrint", label: "连续打印" },
];

function printValidator(){
    if (searchParams.period_s > searchParams.period_e) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }

    if (searchParams.sbj_id_s != "" && searchParams.sbj_id_e != "" && searchParams.sbj_id_s > searchParams.sbj_id_e) {
        ElNotify({
            message: "亲，起始科目不能大于结束科目哦",
            type: "warning",
        });
        return false;
    }
    if (searchParams.sbj_leval_s && searchParams.sbj_leval_e && searchParams.sbj_leval_s > searchParams.sbj_leval_e) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    if (paginationData.total === 0) {
        ElNotify({
            message: "没有总账可打印！",
            type: "warning",
        });
        return false;
    }
    return true
}

function handleExport() {
    if (searchParams.period_s > searchParams.period_e) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (paginationData.total === 0) {
        ElNotify({
            message: "没有总账可导出！",
            type: "warning",
        });
        return false;
    }
    const params = {
        ...searchParams,
        ran: Math.random(),
        async: !isLemonClient(),
        fcid: searchParams.fcid,
    };
    globalExport(window.jAccountBooksHost + "/api/GeneralLedger/Export?" + getUrlSearchParams(params));
}

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        initEnter();
    }
});

function initEnter() {
    if (route.query.period_s) {
        searchInfo.startPid = Number(route.query.period_s);
    }
    if (route.query.period_e) {
        searchInfo.endPid = Number(route.query.period_e);
    }
    updateRouteFateMonth();
    getAsubCodeLength();
    handleSearch();
}
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], getTableData);
watch(searchInfo, () => {
    printDisable.value = searchInfo.showQuantity;
    printInfo.value.direction = searchInfo.showQuantity ? 1 : 0;
});
// const periodInfo = ref("");
const currentPeriodInfo = ref("");
const asubInfo = ref("");

const showDetail = (href: string) => {
    const { ASUB_ID, period_e, period_s } = getParamsFromHrefString(href);
    const params = { ASUB_ID, period_e, period_s, ran:Math.random() };
    globalWindowOpenPage("/AccountBooks/SubsidiaryLedger?" + getUrlSearchParams(params), "明细账");
};

function getParamsFromHrefString(href: string): { ASUB_ID: string; period_e: string; period_s: string } {
    const match = href.match(/ASUB_ID=(\d+).*period_s=(\d+).*period_e=(\d+)/);
    return match ? { ASUB_ID: match[1], period_s: match[2], period_e: match[3] } : { ASUB_ID: "", period_s: "", period_e: "" };
}

// 科目级别
const codeLengthStr = ref("");
const maxCodelength = ref<number>(4);
function getAsubCodeLength() {
    request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        codeLengthStr.value = res.data.codeLength.join("");
        const codeLengthList: number[] = res.data.codeLength;
        const codeLength = codeLengthList.length;
        maxCodelength.value = codeLength;
        searchParams.sbj_leval_e = codeLength;
        searchInfo.endAsubLevel = codeLength;
    });
}
const cellClassName = (row: any, column: any) => {
    if (column.label === "科目编码") {
        return asubClassName(row.asub_code);
    }
    return "";
};
function asubClassName(asubCode: string) {
    let formatString = "";
    const code = asubCode.toString();
    for (var i = 0; i < codeLengthStr.value.length; i++) {
        var length = 0;
        for (var j = 0; j <= i; j++) {
            length += Number(codeLengthStr.value[j]);
        }
        if (length == code.length) {
            formatString = "level" + (i + 1) + "";
            break;
        }
    }
    return formatString;
}

const fcIsShow = ref<boolean>(false);
const fcList = ref<{ id: number; label: string }[]>([]);
// 获取币别并判断币别是否展示
const InitCurrencyApi = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcListOptions];
};
// 判断是否存在使用了外币核算的科目
const InitPeriodApi2 = () => {
    return request({
        url: "/api/AccountSubject/ExistsFc",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        fcIsShow.value = res.data;
        InitCurrencyApi();
    });
};
onMounted(() => {
    // periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    InitPeriodApi2();
    document.body.scrollTop = 0;
    getAsubCodeLength();
    window.addEventListener("modifyaccountSubject", getTableData);
});
onUnmounted(() => {
    window.removeEventListener("modifyaccountSubject", getTableData);
});

const showfcList = ref<any[]>([]);
watchEffect(() => { 
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));    
});
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'label');
}
</script>

<template>
    <div class="content">
        <div class="title">总账</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left" @mouseenter="closePopover">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :isPeriodList="true"
                                    @getActPid="getActPid"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">起始科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="startAsubRef"
                                    v-model="searchInfo.startAsubCode"
                                    asubImgRight="14px"
                                    :showDisabled="true"
                                    @get-asub-id="handleAsubChange($event, 'startAsub')"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">结束科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="endAsubRef"
                                    v-model="searchInfo.endAsubCode"
                                    asubImgRight="14px"
                                    :showDisabled="true"
                                    @get-asub-id="handleAsubChange($event, 'endAsub')"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目级别：</div>
                            <div class="line-item-field">
                                <el-input-number
                                    v-model="searchInfo.startAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                                <div class="ml-10 mr-10">至</div>
                                <el-input-number
                                    v-model="searchInfo.endAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                            </div>
                        </div>
                        <div class="line-item input" v-show="fcIsShow">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field fcid-select">
                                <el-select 
                                    v-model="searchInfo.fcid" 
                                    :teleported="false" 
                                    style="width: 132px"
                                    :filterable="true"
                                    :filter-method="fcFilterMethod"
                                >
                                    <el-option :label="item.label" :value="item.id" v-for="item in showfcList" :key="item.id">
                                        {{ item.label }}
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.showAssist" label="显示辅助核算"></el-checkbox>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.balanceZeroUnShow" label="余额为0不显示"></el-checkbox>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.noTransAndZeroBalUnShow" label="无发生额且余额为0不显示"></el-checkbox>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox
                                    v-model="searchInfo.noOccurrenctHiddenTotal"
                                    label="无发生额不显示本期合计、本年累计"
                                ></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearchConfirm()">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <el-checkbox class="mr-20" v-model="searchInfo.showQuantity" label="显示数量金额" @change="handleSearch()"></el-checkbox>
                    <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['generalledger-canprint']">
                        <li @click="handlePrint(0,searchParams)">直接打印</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button" @click="handleExport" v-permission="['generalledger-canexport']">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                <div class="main-title">
                    <div class="main-title-left">
                        <Tooltip :content="asubInfo" :max-width="1580" :font-size="12">科目：{{ asubInfo }}</Tooltip>
                    </div>
                </div>
                <AccountBooksTable
                    v-if="searchInfo.showQuantity"
                    ref="table"
                    :data="tableData"
                    :columns="columns"
                    :loading="loading"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :hearderRowStyleName="useGreenHeader ? 'green-header' : ''"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :empty-text="treeEmptyText"
                    :scrollbarShow="true"
                    :tableName="setModule"
                >
                    <template #asub_code>
                        <el-table-column 
                            label="科目编码" 
                            min-width="100px" 
                            align="left" 
                            header-align="left"
                            prop="asub_code"
                            :width="getColumnWidth(setModule, 'asub_code')"
                        >
                            <template #default="scope">
                                <span :class="asubClassName(scope.row.asub_code)">{{ scope.row.asub_code }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #name>
                        <el-table-column 
                            label="科目名称" 
                            min-width="220px" 
                            align="left" 
                            header-align="left"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span
                                    v-if="scope.row.asub_name.indexOf('<a') >= 0"
                                    class="link"
                                    @click="showDetail(scope.row.asub_name)"
                                    :class="asubClassName(scope.row.asub_code)"
                                >
                                    {{ scope.row.asub_name.replace(/<[^<>]+>/g, "") }}
                                </span>
                                <span v-else :class="asubClassName(scope.row.asub_code)">
                                    {{ scope.row.asub_name }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </AccountBooksTable>
                <TreeTable
                    v-else
                    ref="treeTable"
                    :data="tableData"
                    :columns="columns"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :hearderRowStyleName="useGreenHeader ? 'green-header' : ''"
                    :empty-text="treeEmptyText"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @go-to-detail="showDetail"
                    @refresh="handleRerefresh"
                    row-key="full_asub_name"
                    other-field="asub_name"
                    first-label="科目编码"
                    view="GeneralLedger"
                    :slotLabels="[{ name: '科目名称', width: '220' }]"
                    :cell-class-name="cellClassName"
                    :tableName="setModule"
                >
                    <template #name>
                        <el-table-column 
                            label="科目名称" 
                            min-width="200" 
                            align="left" 
                            header-align="left" 
                            :show-overflow-tooltip="true"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span
                                    v-if="scope.row.asub_name.indexOf('<a') >= 0"
                                    class="link"
                                    @click="showDetail(scope.row.asub_name)"
                                    :class="asubClassName(scope.row.asub_code)"
                                >
                                    <!-- {{ scope.row.asub_name.replace(/<[^<>]+>/g, "") }} -->
                                    {{ handleAsubName(scope.row.asub_name) }}
                                </span>
                                <span v-else :class="asubClassName(scope.row.asub_code)">
                                    {{ scope.row.asub_name }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </TreeTable>
            </div>
        </div>
    </div>
    <!-- <el-dialog v-model="printDialogVisible" title="总账打印" center width="500" draggable class="custom-confirm">
        <div class="print-content">
            <div class="print-main">
                <div class="line-item mt-20 mb-20">
                    <span class="mr-20">图像方向：</span>
                    <el-radio-group v-model="printInfo.direction" :disabled="printDisable">
                        <el-radio :label="1">横向</el-radio>
                        <el-radio :label="0">纵向</el-radio>
                    </el-radio-group>
                </div>
                <div class="line-item pt-10 pb-10">
                    <el-checkbox v-model="printInfo.mergePrint" label="合并打印"></el-checkbox>
                    <el-checkbox v-model="printInfo.hidePrintDate" label="隐藏打印日期"></el-checkbox>
                    <el-checkbox v-model="printInfo.basepage" label="打印封面"></el-checkbox>
                    <el-checkbox v-model="printInfo.catalog" label="打印目录"></el-checkbox>
                </div>
                <div class="line-item mt-20 mb-20">
                    为了保证您的正常打印，请先下载安装
                    <a class="link" @click="globalWindowOpen('https://get.adobe.com/cn/reader/')">Adobe PDF阅读器</a>
                </div>
            </div>
            <div class="buttons a_nofloat">
                <a class="button" @click="printDialogVisible = false">取消</a>
                <a class="button solid-button ml-20" @click="handlePrint">打印</a>
            </div>
        </div>
    </el-dialog> -->
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        :title="'总账打印'"
        :coverEdit="true"
        :printData="printInfo"
        :dir-disabled="searchInfo.showQuantity"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,searchParams)"
    />
</template>

<style lang="less" scoped>
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";

.main-center {
    display: flex;
    flex-direction: column;
    .table {
        flex:1;
        min-height: 0;
        height: auto !important;
        display: flex;
        flex-direction: column;
    }
    .el-table {
        height: 100%;
    }
    & :deep(.level2) {
        padding-left: 10px;
    }
    & :deep(.level3) {
        padding-left: 20px;
    }
    & :deep(.tree-table) {
        .el-table__row.el-table__row--level-0 {
            & > *:not(:first-child) {
                display: none;
            }
        }
        .el-table__header {
            min-width: 100%;
        }
        .el-scrollbar__view {
            width: 100%;
            .el-table__body {
                min-width: 100%;
            }
        }
    }
}
:deep(.el-checkbox) {
    &.is-checked {
        & .el-checkbox__label {
            color: var(--font-color);
        }
    }
}
.main-title {
    border: 1px solid var(--border-color);
    margin: 0 !important;
    padding-left: 10px;
    height: 36px;
    color: var(--font-color);
    font-size: var(--h5);
    line-height: 36px;
    display: flex;
    .main-title-left {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        text-align: left;
        :deep(.span_wrap) {
            white-space: nowrap !important;
            overflow: hidden;
            display: inline-block !important;
        }
    }
}
:deep(.el-table--border .el-table__inner-wrapper::after) {
    height: 1px !important;
}

:deep(.el-scrollbar__bar) {
    .el-scrollbar__thumb {
        border-radius: 10px;
        opacity: var(--el-scrollbar-hover-opacity, 0.5);
    }
}
:deep(.el-table) {
    .el-table__row--level-0 {
        .level1 {
            padding-left: 0px;
        }
        .level2 {
            padding-left: 0px;
        }
        .level3 {
            padding-left: 0px;
        }
    }
    .el-table__row {
        td[colspan]:not([colspan="1"]) {
            padding-left: 0px;
        }
    }
}
body[erp] {
    .content {
        .main-content {
            // height: 96vh;
            .main-center .table.paging-show {
                flex: 1;
                min-height: 0;
            }
            :deep(.tree-table) {
                .el-scrollbar__view {
                    height: auto;
                }
            }
        }
    }
    .print-main {
        :deep(.el-radio) {
            .el-radio__input .el-radio__inner::after {
                width: 16px;
                height: 16px;
            }
            .el-radio__input.is-checked .el-radio__inner::after {
                transform: translate(-50%, -50%) scale(0.5);
            }
        }
    }
}
</style>
