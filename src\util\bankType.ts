import { BankType } from "../constants/bankKey";

enum JZBankLInk {
    ICBC = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    PABANK = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    CMBC = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    SPDB = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    CMB = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    CGB = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    // CEB = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    BCM = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    PSBC = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    BOC = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
    WeBank = "https://help.ningmengyun.com/#/jz/handle?subMenuId=*********",
}

enum YYCBankLink {
    ICBC = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    PABANK = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    CMBC = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    CMB = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    SPDB = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    CGB = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    // CEB = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    BCM = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    PSBC = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    BOC = "https://help.ningmengyun.com/#/yyc/handleForYYC?subMenuId=*********",
    WeBank = "https://help.ningmengyun.com/#/yyc/HandleForYYC?subMenuId=*********",
}

export const GetBankLink = (bankType: BankType) => {
    switch (bankType) {
        case BankType.ICBC:
            return window.isErp ? YYCBankLink.ICBC : JZBankLInk.ICBC;
        case BankType.PABANK:
            return window.isErp ? YYCBankLink.PABANK : JZBankLInk.PABANK;
        case BankType.CMBC:
            return window.isErp ? YYCBankLink.CMBC : JZBankLInk.CMBC;
        case BankType.SPDB:
            return window.isErp ? YYCBankLink.SPDB : JZBankLInk.SPDB;
        case BankType.CMB:
            return window.isErp ? YYCBankLink.CMB : JZBankLInk.CMB;
        case BankType.CGB:
            return window.isErp ? YYCBankLink.CGB : JZBankLInk.CGB;
        case BankType.CEB:
            return "";
        case BankType.BCM:
            return window.isErp ? YYCBankLink.BCM : JZBankLInk.BCM;
        case BankType.PSBC:
            return window.isErp ? YYCBankLink.PSBC : JZBankLInk.PSBC;
        case BankType.BOC:
            return window.isErp ? YYCBankLink.BOC : JZBankLInk.BOC;
        case BankType.WEBANK:
            return window.isErp ? YYCBankLink.WeBank : JZBankLInk.WeBank;
        default:
            return "";
    }
};

export const GetBankUrl = (bankType: BankType) => {
    switch (bankType) {
        case BankType.PABANK:
            return window.paBankUrl;
        case BankType.SPDB:
            return window.spdbBankUrl;
        case BankType.CMB:
            return window.cmbBankUrl;
        case BankType.PSBC:
            return window.psbcLoginUrl;
        default:
            return "";
    }
};
