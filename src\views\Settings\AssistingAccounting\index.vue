<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content" ref="tabsContentRef">
                    <div class="title">辅助核算设置</div>
                    <el-tabs
                        v-model="aaType"
                        :class="{ erp: isErp, start: !align, center: align, 'tab-pane-only-project': erpOnlyBuyInventory }"
                    >
                        <el-tab-pane
                            label="辅助核算类别"
                            :name="10000"
                            v-if="!erpOnlyBuyInventory && checkPermission(['assistingaccounting-category-canview'])"
                        >
                            <Category 
                                :table-data="tableData" 
                                @load-data="handleInit" 
                                @handle-new="handleNew" 
                                @handle-edit="handleEdit"
                                @switchAAType="switchAAType"
                            />
                        </el-tab-pane>
                        <el-tab-pane label="客户" :name="10001" v-if="!isErp && checkPermission(['assistingaccount-canview'])">
                            <CustomerOrVendor
                                ref="customerRef"
                                :aaType="10001"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                        <el-tab-pane label="供应商" :name="10002" v-if="!isErp && checkPermission(['assistingaccount-canview'])">
                            <CustomerOrVendor
                                ref="vendorRef"
                                :aaType="10002"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                        <el-tab-pane label="职员" :name="10003" v-if="!isErp && checkPermission(['assistingaccount-canview'])">
                            <Employee
                                ref="employeeRef"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                        <el-tab-pane label="部门" :name="10004" v-if="!isErp && checkPermission(['assistingaccount-canview'])">
                            <Department
                                ref="departmentRef"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                        <el-tab-pane label="项目" :name="10005" v-if="checkPermission(['assistingaccount-canview'])">
                            <Project
                                ref="projectRef"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                        <el-tab-pane label="存货" :name="10006" v-if="!isErp && checkPermission(['assistingaccount-canview'])">
                            <Stock
                                ref="stockRef"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                        <el-tab-pane
                            v-if="
                                !erpOnlyBuyInventory &&
                                ![4, 5, 6, 7].includes(accountingStandard) &&
                                checkPermission(['assistingaccount-canview'])
                            "
                            label="现金流"
                            :name="10007"
                        >
                            <CashFlow ref="cashFlowRef" />
                        </el-tab-pane>
                        <el-tab-pane
                            v-if="!erpOnlyBuyInventory"
                            :label="item.aaTypeName"
                            v-for="item in otherTabList"
                            :key="item.rowNum"
                            :name="item.aaType"
                        >
                            <Others
                                ref="customRefs"
                                :aaType="item.aaType"
                                :rows="item"
                                @handle-new="handleNew"
                                @handle-edit="handleEdit"
                                @handle-cancel="handleCancel"
                                @handle-import="handleImport"
                            />
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ (edtiType === "new" ? "新增" : "编辑") + aaTypeName }}</div>
                    <div class="slot-mini-content">
                        <FormContent
                            ref="formContentRef"
                            :aa-length="aaLength"
                            :rows="rows"
                            @handle-cancel="handleCancel"
                            @changeAAType="changeAAType"
                        />
                    </div>
                </div>
            </template>
            <template #import>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ "导入" + aaTypeName }}</div>
                    <div class="slot-mini-content">
                        <FileUpload
                            :aaTypeName="aaTypeName"
                            :aaType="aaType"
                            @cancelImport="cancelImportHandle"
                            @successImport="successImportHandle"
                        />
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>
<script lang="ts">
export default {
    name: "AssistingAccounting",
};
</script>
<script setup lang="ts">
import { ref, watch, nextTick, computed, onActivated } from "vue";
import { useRoute } from "vue-router";
import { ElNotify } from "@/util/notify";
import { getNextAaNum } from "./utils";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";

import type { IAssistingAccountType } from "@/api/assistingAccounting";

import ContentSlider from "@/components/ContentSlider/index.vue";
import Category from "./components/Category.vue";
import CustomerOrVendor from "./components/CustomerOrVendor.vue";
import Employee from "./components/Employee.vue";
import Department from "./components/Department.vue";
import Project from "./components/Project.vue";
import Stock from "./components/Stock.vue";
import CashFlow from "./components/CashFlow.vue";
import Others from "./components/Others.vue";
import FormContent from "./components/FormContent.vue";
import FileUpload from "./components/FileUpload.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { checkPermission } from "@/util/permission";
import { request, type IResponseModel } from "@/util/service";
import { getCookie } from "@/util/cookie";
import { getServiceId } from "@/util/proUtils";

const tabsContentRef = ref<HTMLDivElement>();
const align = ref(true);
const isErp = ref(window.isErp);
const route = useRoute();
const customerRef = ref<InstanceType<typeof CustomerOrVendor>>();
const vendorRef = ref<InstanceType<typeof CustomerOrVendor>>();
const employeeRef = ref<InstanceType<typeof Employee>>();
const departmentRef = ref<InstanceType<typeof Department>>();
const projectRef = ref<InstanceType<typeof Project>>();
const stockRef = ref<InstanceType<typeof Stock>>();
const cashFlowRef = ref<InstanceType<typeof CashFlow>>();
const customRefs = ref<any>(null);
const formContentRef = ref<InstanceType<typeof FormContent>>();

const accountingStandard = Number(useAccountSetStore().accountSet?.accountingStandard);

const aaType = ref(10000);
const slots = ["main", "add", "import"];
const currentSlot = ref("main");
const tableData = ref<IAssistingAccountType[]>([]);
const otherTabList = ref<IAssistingAccountType[]>([]);
const rows = ref<any>();

let count = 0;
const aaLength = computed(() => tableData.value.length);
const erpOnlyBuyInventory = ref(window.isErp);
if (window.isErp) {
    request({ url: window.eHost + "/wb/plan/hasAccInpackage", method: "post", data: { serviceId: +getServiceId()} }).then(
        (res: any) => {
            erpOnlyBuyInventory.value = res.data === 0;
            if (erpOnlyBuyInventory.value) {
                aaType.value = 10005;
            }
        }
    );
}

const handleInit = (EditType?: "New" | "Edit", formAaType?: number) => {
    useAssistingAccountingStore()
        .getAssistingAccountingType()
        .then((res: IAssistingAccountType[]) => {
            if (res.length) {
                count++;
                tableData.value = res;
                if (checkPermission(["assistingaccount-canview"])) {
                    otherTabList.value = res.filter((item: IAssistingAccountType) => item.aaType > 10007);
                }
                if (count === 1) {
                    if (route.query.aaType) {
                        aaType.value = Number(route.query.aaType);
                    } else if (!checkPermission(["assistingaccount-canview"])) {
                        aaType.value = 10000;
                    } else if (isErp.value) {
                        aaType.value = 10005;
                    } else {
                        aaType.value = 10001;
                    }

                    if (route.query.newItem === "1") {
                        handleNew(aaType.value);
                        return;
                    }
                }
                currentSlot.value = "main";
                nextTick().then(() => {
                    formContentRef.value?.handleReset(aaType.value);
                    if (EditType === "New") {
                        const lastAAType = tableData.value[tableData.value.length - 1].aaType;
                        aaType.value = lastAAType;
                    } else if (EditType === "Edit") {
                        customRefs.value[otherTabList.value.findIndex((item) => item.aaType === formAaType)]?.handleInit();
                    }
                    const contentEle = tabsContentRef.value?.querySelector(".el-tabs__nav-scroll");
                    if (contentEle) {
                        const scrollEle = contentEle.querySelector(".el-tabs__nav.is-top");
                        if (scrollEle) {
                            const scrollWidth = scrollEle.scrollWidth;
                            const clientWidth = contentEle.clientWidth;
                            align.value = scrollWidth <= clientWidth;
                        }
                    }
                });
            } else {
                ElNotify({ type: "warning", message: "加载失败，请刷新页面重试！" });
            }
        })
        .catch((msg: string) => {
            ElNotify({ type: "warning", message: msg });
        });
};
handleInit();
onActivated(() => {
    if (route.query.aaType) {
        aaType.value = Number(route.query.aaType);
    }
});
const edtiType = ref<"new" | "edit">("new");
const handleNew = (aaType: number) => {
    if (aaType === 10000) {
        if (aaLength.value === 20) {
            return ElNotify({ type: "warning", message: "辅助核算类别最多支持20种哦" });
        } else {
            formContentRef.value?.handleNew(10000, "");
            edtiType.value = "new";
            currentSlot.value = "add";
            return;
        }
    }
    getNextAaNum(aaType).then((res: IResponseModel<string>) => {
        formContentRef.value?.handleNew(aaType, res.data);
        nextTick().then(() => {
            edtiType.value = "new";
            currentSlot.value = "add";
        });
    });
};
const changeAAType = () => {
    handleInit("New");
};

const handleCancel = (formAaType?: number) => {
    const handleReset = () => {
        nextTick().then(() => {
            formContentRef.value?.handleReset(aaType.value);
        });
    };
    const successBack = () => {
        currentSlot.value = "main";
        handleReset();
    };

    const customeBack = (index: number) => {
        customRefs.value[index]?.handleInit();
        customRefs.value[index]?.handleSearch(successBack);
        currentSlot.value = "main";
        handleReset();
    };
    switch (aaType.value) {
        case 10000:
            handleInit("Edit", formAaType);
            break;
        case 10001:
            customerRef.value?.handleSearch(successBack);
            break;
        case 10002:
            vendorRef.value?.handleSearch(successBack);
            break;
        case 10003:
            employeeRef.value?.handleSearch(successBack);
            break;
        case 10004:
            departmentRef.value?.handleSearch(successBack);
            break;
        case 10005:
            projectRef.value?.handleSearch(successBack);
            break;
        case 10006:
            stockRef.value?.handleSearch(successBack);
            break;
        case 10007:
            cashFlowRef.value?.handleSearch(successBack);
            break;
        default:
            customeBack(otherTabList.value.findIndex((item) => item.aaType === aaType.value));
            break;
    }
};
const handleEdit = (aaType: number, params: any) => {
    formContentRef.value?.handleEdit(aaType, params);
    edtiType.value = "edit";
    currentSlot.value = "add";
};
const handleImport = () => (currentSlot.value = "import");
const cancelImportHandle = () => (currentSlot.value = "main");
const successImportHandle = () => {
    handleCancel();
    useAssistingAccountingStore().getAssistingAccounting(aaType.value);
    if (aaType.value === 10004) {
        useAssistingAccountingStore().getDepartment();
    }
    currentSlot.value = "main";
};

let customerLock = false;
let vendorLock = false;
let employeeLock = false;
let departmentLock = false;
let projectLock = false;
let cashFlowLock = false;
let stockLock = false;
watch(aaType, (val) => {
    if (val == 10001 && !customerLock) {
        customerRef.value?.handleSearch();
        customerLock = true;
    } else if (val == 10002 && !vendorLock) {
        vendorRef.value?.handleSearch();
        vendorLock = true;
    } else if (val == 10003 && !employeeLock) {
        employeeRef.value?.handleSearch();
        employeeLock = true;
    } else if (val == 10004 && !departmentLock) {
        departmentRef.value?.handleSearch();
        departmentLock = true;
    } else if (val == 10005 && !projectLock) {
        projectRef.value?.handleSearch();
        projectLock = true;
    } else if (val == 10006 && !stockLock) {
        stockRef.value?.handleSearch();
        stockLock = true;
    } else if (val == 10007 && !cashFlowLock) {
        cashFlowRef.value?.handleSearch();
        cashFlowLock = true;
    }
});
watch(aaType, (val) => {
    if (val > 10007) {
        const index = otherTabList.value.findIndex((item) => item.aaType === val);
        rows.value = otherTabList.value[index];
        customRefs.value[index]?.handleSearch();
    }
});

const aaTypeName = ref("");
watch(aaType, (val) => {
    if (val === 10000) {
        aaTypeName.value = "辅助核算类别";
        return;
    }
    const item = tableData.value.find((item) => item.aaType === val);
    aaTypeName.value = item?.aaTypeName || "";
});
function switchAAType(val: number) {
    aaType.value = val;
}
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.content {
    .main-content {
        :deep(.el-popper.is-light) {
            max-width: 400px !important;
        }
        :deep(.el-tabs) {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            .el-tabs__item.is-top:last-child {
                padding-right: 40px;
            }
            .el-tabs__content {
                flex: 1;
            }
            .el-tab-pane {
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }
    }

    .slot-content {
        .slot-mini-content {
            width: 1000px;
            margin-bottom: 10px;
        }
    }
}
body[erp] {
    .content {
        :deep(.el-tabs) {
            &.erp {
                &.start {
                    .el-tabs__nav-scroll {
                        justify-content: flex-start;
                    }
                }
                &.center {
                    .el-tabs__nav-scroll {
                        justify-content: center;
                    }
                }
                &.tab-pane-only-project {
                    .el-tabs__active-bar {
                        transform: unset !important;
                    }
                }
            }
        }
        .slot-content {
            height: auto;
            .slot-mini-content {
                height: auto;
            }
        }
    }
}
</style>
