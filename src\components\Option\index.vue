<template>
  <ToolTip
    :teleported="true"
    placement="right">
    <el-option
      ref="optionRef"
      v-bind="optionBindings"
      @mousedown.prevent="handleClick">
      <slot />
    </el-option>
  </ToolTip>
</template>

<script setup lang="ts">
  import { computed } from "vue"
  import { ElOption } from "element-plus"
  import type { OptionProps, OptionEmits } from "./types"

  const props = withDefaults(defineProps<OptionProps>(), {
    disabled: false,
    className: "",
  })

  const emit = defineEmits<OptionEmits>()

  const optionRef = ref<InstanceType<typeof ElOption>>()

  const optionBindings = computed(() => ({
    label: props.label,
    value: props.value,
    disabled: props.disabled,
    class: props.className,
    valueKey: props.valueKey,
  }))

  const handleClick = (e: MouseEvent) => {
    e.preventDefault()
    emit("click")
  }
</script>
