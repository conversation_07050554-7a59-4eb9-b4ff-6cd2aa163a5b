<template>
    <el-tooltip
        :visible="visible"
        popper-class="el-option-tool-tip"
        effect="light"
        :offset="-5"
        :content="props.optionValue ? props.optionValue : props.label"
        placement="right"
        :hide-after="0"
    >
        <el-option
            ref="optionRef"
            :label="label"
            :value="value"
            :disabled="disabled"
            :class="props.className"
            @mouseenter="checkOverflow"
            @mouseleave="visible = false"
            @mousedown.prevent="(e) => clickOption(e)"
        >
            <slot></slot>
        </el-option>
    </el-tooltip>
</template>

<script setup lang="ts">
import { ElOption, ElTooltip } from "element-plus";
import { isOverflowed, overflowElementStyle } from "./utils";

import { ref, onMounted, onUnmounted } from "vue";
const props = withDefaults(
    defineProps<{
        label: string;
        value: any;
        disabled?: boolean;
        className?: string;
        optionValue?: string;
        lineClamp?: number;
        paddingRight?: number;
    }>(),
    {
        disabled: false,
        className: "",
        lineClamp: 2,
        paddingRight: overflowElementStyle.right,
    }
);

const emits = defineEmits(["click"]);
const optionRef = ref<InstanceType<typeof ElOption>>();
const visible = ref(false);

function clickOption(e: any) {
    e.preventDefault();
    emits("click");
}

const checkOverflow = () => {
    const element = optionRef.value?.$el;
    const width = element.getBoundingClientRect().width;
    const text = props.optionValue ? props.optionValue : props.label;
    if (isOverflowed(text, width + "px", props.lineClamp, props.paddingRight)) {
        visible.value = true;
    } else {
        visible.value = false;
    }
};
const handleScroll = () => {
    visible.value = false;
};
onMounted(() => {
    const element = optionRef.value?.$el;
    if (element && element.parentNode) {
        if (element.parentNode.parentNode) {
            const scrollElement = element.parentNode.parentNode;
            scrollElement.addEventListener("scroll", handleScroll);
        }
    }
});
onUnmounted(() => {
    const element = optionRef.value?.$el;
    if (element && element.parentNode) {
        if (element.parentNode.parentNode) {
            const scrollElement = element.parentNode.parentNode;
            scrollElement.removeEventListener("scroll", handleScroll);
        }
    }
});
</script>
