import type { ModuleType } from "./utils";
export interface ISearchData {
    fid: number;
    startDate: string;
    endDate: string;
    uploadPerson?: string;
    matters?: string;
    uploadType?: string;
    fileState?: string;
    documentType?: string;
    voucherState?: string;
    checkStates?: string;
    paperState?: string;
    isPaperArchives?: string;
    fileName?: string;
    fileCategory?: string;
}

export interface ITree {
    id: number;
    text: string;
    children?: any;
}

export interface IBackupOrAuditItem {
    AS_ID: string;
    CREATED_BY: number;
    CREATED_DATE: string;
    CREATED_DATE_STR: string;
    FILE_NAME: string;
    FILE_SIZE: number;
    IsZip: boolean;
    SYS_ID: string;
    TYPE: number;
    Id?: string;
}

export interface IFileTableItem {
    As_Id: number;
    Channel: number;
    Check_State: number;
    Created_By: string;
    Created_By_Name: string;
    Created_Date: string;
    Created_Date_Str: string;
    DRAFT_ID: string;
    Document_Type: number;
    FA_ID: string;
    File_Category: number;
    File_Id: string;
    File_Name: string;
    File_Name_Pre: string;
    File_Size: number;
    File_State: number;
    File_Type: number;
    Flag: number;
    Hide: number;
    INV_ID: string;
    IsAttachDraft: boolean;
    IsAttachFixedAssets: boolean;
    IsAttachInvoice: boolean;
    IsAttachJournal: boolean;
    IsAttachSalary: boolean;
    IsAttachVouch: boolean;
    JOURNAL_CREATED_DATE: string;
    Matters: string;
    NUM: number;
    P_Id: string;
    Parent_Id: string;
    Parent_Name: string;
    Path: string;
    PathUrl: string;
    RelatedDocumentTypes: Array<ModuleType>;
    SALARY_M_ID: string;
    Source: any;
    Sys_Id: string;
    Thumbnail: string;
    ThumbnailUrl: string;
    TicketId: string;
    Upload_Type: number;
    V_Id: string;
    Virtual_Type: number;
    VoucherDate: string;
    VoucherGroup: any;
    VoucherGroupStr: string;
    VoucherNum: number;
    IsPaperArchives: boolean;
}

export interface OptionObj1 {
    value: string;
    selected: boolean;
    label: string;
}

export interface OptionObj2 {
    value: string;
    label: string;
}

export interface IhandleSearchParams {
    date: string;
    fileType: string | number;
    word: string;
}

export enum AttachFileCategory {
    // 凭证附件
    Voucher = 0,
    // 银行回单
    Receipt,
    // 电子发票
    Invoice,
    // 其他单据
    Other,
}

export interface IProcessItem {
    fileName: string;
    fileType: number;
    dateTime: string;
    fileSize: number;
    creator: string;
    opertion: number;
    progress: number;
    expiredDate: string;
    createBy: number;
}

export interface IFSearchItem {  
    uploadPerson: string;
    uploadType: string;
    fileState: string;
    documentType: string;  
    voucherState: string;
    checkStates: number[];
    fileCategory: string;
    matters: string;
    fileName: string;
}