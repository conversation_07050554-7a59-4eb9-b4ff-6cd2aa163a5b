<template>
    <el-dialog
        v-model="editEquationDialog"
        :title="`编辑公式-${selectedStartCell}`"
        width="1044px"
        destroy-on-close
        class="edit-equation-dialog custom-confirm dialogDrag"
    >
        <div class="edit-equation-dialog-container" v-dialogDrag>
            <el-tabs v-model="activeTabName" type="card" class="demo-tabs">
                <el-tab-pane label="科目" :name="equationValueTypeEnum.accountSubject">
                    <div class="tab-content">
                        <el-form :inline="true" class="demo-form-inline">
                            <el-form-item label="科目名称:">
                                <FilterCustomSelect
                                    v-model="formSubject.asubId"
                                    :teleported="false"
                                    :filterable="true"
                                    class="add-subject-input"
                                    :filter-method="handleAsubNameFilter"
                                    placeholder=" "
                                    @change="changeAsubName"
                                >
                                    <ElOption
                                        v-for="item in subjectData"
                                        :optionValue="item.asubCode + ' ' + item.asubAAName"
                                        :key="item.asubId"
                                        :label="item.asubAAName"
                                        :value="item.asubId"
                                    >
                                        <span>{{ item.asubCode }} {{ item.asubAAName }}</span>
                                    </ElOption>
                                </FilterCustomSelect>
                            </el-form-item>
                            <el-form-item label="辅助核算:">
                                <el-input
                                    placeholder=" "
                                    ref="asubAAtypesRef"
                                    style="width: 143px"
                                    v-model="formSubject.asubAAtypes"
                                    :disabled="!asubAAtypes"
                                    @focus="asubAAtypesFocus"
                                >
                                </el-input>
                            </el-form-item>
                            <el-form-item label="运算符号:">
                                <el-select placeholder=" " :suffix-icon="CaretBottom" v-model="formSubject.operator" style="width: 54px">
                                    <el-option label="+" :value="1" />
                                    <el-option label="-" :value="2" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="时间类型:">
                                <Select
                                    placeholder=" "
                                    v-model="formSubject.columnTypeDateType"
                                    style="width: 123px"
                                    :filterable="true"
                                    :filter-method="dateTypeFilterMethod"
                                    :remote-show-suffix="true"
                                >
                                    <ElOption
                                        v-for="item in showDateTypeOption"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item label="取数类型:">
                                <Select
                                    placeholder=" "
                                    v-model="formSubject.columnTypeDataType"
                                    style="width: 124px"
                                    :filterable="true"
                                    :remote="true"
                                    :filter-method="dataTypeFilterMethod"
                                    :remote-show-suffix="true"
                                >
                                    <ElOption
                                        v-for="item in showDataTypeOption"
                                        :label="item.label"
                                        :value="item.value"
                                        :key="item.key"
                                    ></ElOption>
                                </Select>
                            </el-form-item>
                        </el-form>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="报表" :name="equationValueTypeEnum.cell">
                    <div class="tab-content">
                        <el-form :inline="true" class="demo-form-inline">
                            <el-form-item label="报表名称:">
                                <Select
                                    placeholder=" "
                                    v-model="formCells.statementId"
                                    :suffix-icon="CaretBottom"
                                    @change="handleStatementChange"
                                    style="width: 143px"
                                >
                                    <ElOption
                                        v-for="item in customStatementList"
                                        :label="item.statementName"
                                        :value="item.statementId"
                                        :key="item.statementId"
                                    />
                                </Select>
                            </el-form-item>
                            <el-form-item label="单元格:">
                                <el-select
                                    placeholder=" "
                                    :filterable="true"
                                    v-model="formCells.cellName"
                                    :suffix-icon="CaretBottom"
                                    style="width: 70px"
                                >
                                    <el-option v-for="cellName in cellNameList" :label="cellName" :value="cellName" :key="cellName" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="运算符号:">
                                <el-select placeholder=" " v-model="formCells.operator" :suffix-icon="CaretBottom" style="width: 54px">
                                    <el-option label="+" :value="1" />
                                    <el-option label="-" :value="2" />
                                </el-select>
                            </el-form-item>
                        </el-form></div
                ></el-tab-pane>
                <el-tab-pane label="固定报表" :name="equationValueTypeEnum.statement">
                    <div class="tab-content">
                        <el-form :inline="true" class="demo-form-inline">
                            <el-form-item label="报表名称:">
                                <Select placeholder=" " v-model="formStatement.statementId" :suffix-icon="CaretBottom" style="width: 143px">
                                    <ElOption
                                        v-for="item in fixedReportList"
                                        :label="item.statementName"
                                        :value="item.statementId"
                                        :key="item.statementId"
                                    />
                                </Select>
                            </el-form-item>
                            <el-form-item label="报表项目:">
                                <Select placeholder=" " v-model="formStatement.lineID" :suffix-icon="CaretBottom" style="width: 143px">
                                    <ElOption
                                        v-for="item in fixedReportItems"
                                        :label="item.lineName.trim()"
                                        :value="item.lineID"
                                        :key="item.lineID"
                                    />
                                </Select>
                            </el-form-item>
                            <el-form-item label="取数类型:">
                                <Select placeholder=" " v-model="formStatement.dataType" :suffix-icon="CaretBottom" style="width: 124px">
                                    <ElOption v-for="(item, key) in fixedReportDataTypesList" :label="item" :value="key" :key="key" />
                                </Select>
                            </el-form-item>
                            <el-form-item label="运算符号:">
                                <el-select placeholder=" " v-model="formStatement.operator" :suffix-icon="CaretBottom" style="width: 54px">
                                    <el-option label="+" :value="1" />
                                    <el-option label="-" :value="2" />
                                </el-select>
                            </el-form-item>
                        </el-form></div
                ></el-tab-pane>
                <div class="btns-contaner">
                    <a class="button solid-button add-equation" @click="handleAddEquation">添加公式</a>
                </div>
            </el-tabs>
            <div class="table-container">
                <Table class="erp-table" 
                    :columns="columns" 
                    :data="tableData" 
                    :height="238" 
                    :scrollbarShow="Boolean(tableData.length)"
                    :tableName="setModule"
                >
                    <template #rate>
                        <el-table-column 
                            label="比例" 
                            min-width="139" 
                            :show-overflow-tooltip="false" 
                            align="right" 
                            header-align="right"
                            prop="rate"
                            :width="getColumnWidth(setModule, 'rate')"
                        >
                            <template #default="scope">
                                <div class="rate-editor">
                                    <el-input type="number" v-model="scope.row.rate" class="rate-cell-input" /><span>%</span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #operation>
                        <el-table-column label="操作" min-width="120" :resizable="false">
                            <template #default="scope">
                                <a class="link" @click="handleDelEquation(scope.$index)"> 删除</a>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
        <div class="buttons erp-buttons">
            <a class="button solid-button mr-10" @click="editEquationConfirm">确定</a>
            <a
                class="button"
                @click="
                    editEquationDialog = false;
                    resetEditEquation();
                "
                >取消</a
            >
        </div>
        <SelectAACodeDialog
            v-model="selectAACodeDialogShow"
            ref="selectAACodeDialogRef"
            @update="selectAACodeDialogShow = false"
            @selectAACodeConfirm="selectAACodeConfirm"
        ></SelectAACodeDialog>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, toRef, watch, reactive, type Ref } from "vue";
import { CaretBottom } from "@element-plus/icons-vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import FilterCustomSelect from "@/views/Statements/components/EditEquation/FilterCustomSelect.vue";
import SelectAACodeDialog from "./SelectAACodeDialog.vue";
import { getGlobalLodash } from "@/util/lodash";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { columnRelation, columnDateTypeEnum, columnDataTypeEnum, equationOperatorEnum, equationValueTypeEnum, dateTypeOption, dataTypeOption } from "../utils";
import { ElNotify } from "@/util/notify";
import type { IStatementEquationModel, ICustomStatementListItem, IAACodeTableItem } from "../types";
import { request, type IResponseModel } from "@/util/service";
import { convertToColumnName } from "../utils";
import { watchEffect } from "vue";
import Select from "@/components/Select/index.vue";
import ElOption from "@/components/Option/index.vue";
import type { IStatementLines, IStatementLinesItems, IAssistEntries,IAssistEntriesItem } from "../types";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { pinyin } from "pinyin-pro";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "CusStateEditEquation";
const assistingAccountingStore = useAssistingAccountingStore();
const emits = defineEmits(["update:modelValue", "editEquationConfirm"]);
const departmentListAll: Ref<IAssistEntriesItem[]> = toRef(assistingAccountingStore, "departmentListAll");
const _ = getGlobalLodash()
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    selectedStartCell: {
        type: String,
        default: "",
    },
    statementModelPid: {
        type: Number,
        default: 0,
    },
    customStatementList: {
        type: Array<ICustomStatementListItem>,
        default: [],
    },
    assistentries: {
        type: Object,
        default: {},
    },
});

const editEquationDialog = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
//科目
let formSubject = reactive({
    columnTypeDateType: 13,
    columnTypeDataType: 1,
    operator: 1,
    asubId: undefined,
    asubAAtypes: "",
});
// 报表
let customStatementList = computed(() => props.customStatementList.filter((i) => i.statementId > 0));
let formCells = reactive({
    statementId: customStatementList.value[0]?.statementId,
    cellName: "A1",
    operator: 1,
});
let formStatement = reactive<{
    statementId: undefined | number;
    lineID: undefined | number;
    dataType: undefined | string;
    operator: number;
}>({
    statementId: undefined,
    lineID: undefined,
    dataType: undefined,
    operator: 1,
});
const dataTypeList = ref([1, 2, 3, 4, 5, 6, 7]);
watch(
    () => formSubject.columnTypeDateType,
    (v: number) => {
        formSubject.columnTypeDataType = columnRelation[v - 1].dataType[0];
        dataTypeList.value = columnRelation[v - 1].dataType;
    },
    { immediate: true }
);
let activeTabName = ref(1);
// 科目
const accountSubjectStore = useAccountSubjectStore();
const subjectDataAll: Ref<IAccountSubjectModel[]> = toRef(accountSubjectStore, "accountSubjectListWithoutDisabled");
const subjectData = ref<IAccountSubjectModel[]>(_.cloneDeep(subjectDataAll.value));
watch(
    () => subjectDataAll,
    () => {
        subjectData.value = _.cloneDeep(subjectDataAll.value);
    }
);
const handleAsubNameFilter = (v: string) => {
    const lowerCaseValue = v.trim().toLowerCase();
    subjectData.value = subjectDataAll.value.filter((item: IAccountSubjectModel, index: number) =>{
        const itemProp = item.asubCode + ' ' + item.asubAAName; 
        const pinyinFirst = pinyin(itemProp, {   
            pattern: "first",   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();
        const pinyinFull = pinyin(itemProp, {   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();
        return (
            item.asubCode.includes(v.trim()) 
            || item.asubAAName.includes(v.trim()) 
            || item.acronym.includes(v.trim())
            || pinyinFirst.includes(lowerCaseValue) 
            || pinyinFull.includes(lowerCaseValue)
        )
    });
};
const asubAAtypes = ref("");
const selectAACodeDialogShow = ref(false);
let AACodeTableList = ref<IAACodeTableItem[]>([]);
const asubAAtypesRef = ref();
const selectAACodeDialogRef = ref();
let editAAcodeData = ref<Array<{ aatype: string; aaeid: string; aanum: string; aaname: string }>>([]);
let assistentries: Ref<{ [key: number | string]: IAssistEntries }> = computed(() => {
    return props.assistentries;
});
const changeAsubName = (asubid: number) => {
    asubAAtypes.value = subjectData.value.find((v: IAccountSubjectModel) => v.asubId === asubid)?.aatypes || "";
    formSubject.asubAAtypes = "";
    editAAcodeData.value = [];
    asubAAeids = [];
};
const asubAAtypesFocus = () => {
    AACodeTableList.value = [];
    let aatypes = asubAAtypes.value.split(",");
    aatypes.forEach((v, i) => {
        if (v === "10004") {
                assistentries.value[Number(v)].list = departmentListAll.value;
        }
        AACodeTableList.value.push({
            aatype: v,
            ...assistentries.value[Number(v)],
            aaeid: formSubject.asubAAtypes ? editAAcodeData.value.find((ve) => ve.aatype === v)?.aaeid : "",
            aaname: formSubject.asubAAtypes ? editAAcodeData.value.find((ve) => ve.aatype === v)?.aaname : "",
        });
    });

    selectAACodeDialogRef.value.initTable(AACodeTableList.value);
    asubAAtypesRef.value.blur();
    selectAACodeDialogShow.value = true;
};
let asubAAeids: number[] = [];
const selectAACodeConfirm = (res: Array<{ aatype: string; aaeid: string; aanum: string; aaname: string }>) => {
    editAAcodeData.value = res;
    asubAAeids = res.filter((v) => v.aaeid).map((item) => Number(item.aaeid));
    let aaNums = res.filter((v) => v.aaname).map((item) => item.aanum);
    formSubject.asubAAtypes = aaNums ? aaNums.join("_") : "";
};

let rowNumber = ref(20);
let columnNumber = ref(16);
const handleStatementChange = (statementId: number) => {
    let target = customStatementList.value.find((v: ICustomStatementListItem) => v.statementId === statementId) as ICustomStatementListItem;
    rowNumber.value = target.rowNumber;
    columnNumber.value = target.columnNumber;
};
let cellNameList = computed(() => {
    let arr = [];
    for (let r = 1; r <= rowNumber.value; r++) {
        for (let c = 1; c <= columnNumber.value; c++) {
            arr.push(convertToColumnName(c) + r);
        }
    }
    return arr;
});
// 固定报表
let fixedReportList = ref<IStatementLines[]>([]); // =旧代码中statementLines
request({
    url: "/api/CustomStatement/GetCustomStatementLines",
    method: "post",
}).then((res: IResponseModel<IStatementLines>) => {
    if (res.state === 1000) {
        fixedReportList.value = res.data;
        formStatement.statementId = fixedReportList.value[0].statementId;
    }
});
let fixedReportItems = ref();
let fixedReportDataTypesList = ref();

watchEffect(() => {
    formCells.statementId = customStatementList.value[0]?.statementId;
    rowNumber.value = customStatementList.value[0]?.rowNumber;
    columnNumber.value = customStatementList.value[0]?.columnNumber;
    let statementId = formStatement.statementId;
    let target = fixedReportList.value?.find((v: IStatementLines) => v.statementId === statementId);
    fixedReportItems.value = target?.statementItems.filter((v: IStatementLinesItems) => v.lineNumber !== 0);
    fixedReportDataTypesList.value = target?.dataTypes;
    formStatement.lineID = fixedReportItems.value && fixedReportItems.value[0]?.lineID;
    formStatement.dataType = target && Object.keys(target.dataTypes)[0];
});
let equation: IStatementEquationModel = {};
const handleAddEquation = () => {
    switch (activeTabName.value) {
        case equationValueTypeEnum.accountSubject:
            if (!formSubject.asubId) {
                ElNotify({
                    type: "warning",
                    message: "请选择科目",
                });
                return false;
            }
            let asubCode = (subjectDataAll.value as any)?.find((v: IAccountSubjectModel) => v.asubId === formSubject.asubId).asubCode;            
            equation = statementEquationModel({
                operator: formSubject.operator,
                source:
                    formSubject.asubId +
                    "_" +
                    asubCode +
                    (asubAAeids ? "-" + asubAAeids : "") +
                    "_" +
                    formSubject.columnTypeDateType +
                    "_" +
                    formSubject.columnTypeDataType,
                valueType: equationValueTypeEnum.accountSubject,
                rate: 100,
            });
            break;
        case equationValueTypeEnum.cell:
            if (!formCells.statementId) {
                ElNotify({
                    type: "warning",
                    message: "请选择报表",
                });
                return false;
            }
            if (!formCells.cellName) {
                ElNotify({
                    type: "warning",
                    message: "请填写单元格",
                });
                return false;
            }
            equation = statementEquationModel({
                operator: formCells.operator,
                source: formCells.statementId + "_" + formCells.cellName,
                valueType: equationValueTypeEnum.cell,
                rate: 100,
            });

            break;
        case equationValueTypeEnum.statement:
            equation = statementEquationModel({
                operator: formStatement.operator,
                source: formStatement.statementId + "_" + formStatement.lineID + "_" + formStatement.dataType,
                valueType: equationValueTypeEnum.statement,
                rate: 100,
            });
            break;
    }
    reCalcEquation(
        props.selectedStartCell,
        equation as IStatementEquationModel,
        (model: any) => {},
        () => {
            tableData.value.push(equation);
        }
    );
};
const handleDelEquation = (delIndex: number) => {
    tableData.value.splice(delIndex, 1);
};
const statementEquationModel = (params: IStatementEquationModel) => {
    let equation: IStatementEquationModel = {};
    equation.equationId = params.equationId || 0;
    equation.operator = params.operator || equationOperatorEnum.addition;
    equation.source = params.source || "";
    equation.valueType = params.valueType || equationValueTypeEnum.accountSubject;
    equation.value = params.value || 0;
    equation.rate = params.rate || 0;
    equation.evaluated = params.evaluated || false;
    return equation;
};
function reCalcEquation(cellName: string, equation: IStatementEquationModel, callback: Function, failCallback: Function) {
    failCallback && failCallback();
}
let tableData = ref<IStatementEquationModel[]>([]);
const initTableData = (data: IStatementEquationModel[] | []) => {
    tableData.value = data.slice();
};
const columns = ref<Array<IColumnProps>>([
    {
        label: "科目或项目",
        prop: "source",
        minWidth: 460,
        align: "left",
        headerAlign: "left",
        formatter: (row: any, column: any, cellValue: any, index: number) => {
            let arr = row.source.split("_");
            switch (row.valueType) {
                case equationValueTypeEnum.accountSubject:
                    let asubId = Number(arr[0]);
                    let asubCodeAndAACode = arr[1];
                    let asubCode = asubCodeAndAACode.split("-")[0];
                    let aaCode = asubCodeAndAACode.split("-")[1];
                    let asub = subjectDataAll.value.filter((i) => {
                        return i.asubId == asubId && i.asubCode == asubCode;
                    })[0];

                    if (aaCode) {
                        let aaCodeName = aaCode
                            .split(",")
                            .map(function (a: number) {
                                for (let aatype in assistentries.value) {
                                    let aae = assistentries.value[aatype].list.filter(function (b) {
                                        return a == b.aaeid;
                                    })[0];
                                    if (aae) {
                                        return aae.aaname;
                                    }
                                }
                            })
                            .join("_");
                        return asub.asubAAName + "_" + aaCodeName;
                    } else {
                        return asub.asubAAName;
                    }
                    break;
                case equationValueTypeEnum.cell:
                    let statementId = Number(arr[0]);
                    return customStatementList.value.filter((n: ICustomStatementListItem) => {
                        return n.statementId == statementId;
                    })[0].statementName;
                    break;
                case equationValueTypeEnum.statement:
                    let id = Number(arr[0]);
                    let lineId = Number(arr[1]);
                    let fixedReportById = fixedReportList.value.find((v: IStatementLines) => v.statementId === id);
                    return (
                        fixedReportById.statementName +
                        "-" +
                        fixedReportById.statementItems.filter((n: IStatementLinesItems) => {
                            return n.lineID == lineId;
                        })[0].lineName
                    );
                    break;
            }
        },
        width: getColumnWidth(setModule, "source")
    },
    {
        label: "运算项目",
        prop: "operator",
        minWidth: 80,
        align: "left",
        headerAlign: "left",
        formatter: (row: any, column: any, cellValue: any, index: number) => {
            switch (cellValue) {
                case equationOperatorEnum.addition:
                    return "+";
                case equationOperatorEnum.subtraction:
                    return "-";
                case equationOperatorEnum.multiplication:
                    return "*";
                case equationOperatorEnum.division:
                    return "/";
            }
        },
        width: getColumnWidth(setModule, "operator")
    },
    {
        label: "取数类型",
        prop: "valueType",
        minWidth: 180,
        align: "left",
        headerAlign: "left",
        formatter: (row: any, column: any, cellValue: any, index: number) => {
            let arr = row.source.split("_");
            switch (row.valueType) {
                case equationValueTypeEnum.accountSubject:
                    let dateType = Number(arr[2]);
                    let dataType1 = Number(arr[3]);
                    return columnDateTypeEnum[dateType] + columnDataTypeEnum[dataType1];
                    break;
                case equationValueTypeEnum.cell:
                    let cellName = arr[1];
                    return cellName;
                    break;
                case equationValueTypeEnum.statement:
                    let id = Number(arr[0]);
                    let dataType2 = Number(arr[2]);
                    return fixedReportList.value.find((v: IStatementLines) => v.statementId === id).dataTypes[dataType2];
                    break;
            }
        },
        width: getColumnWidth(setModule, "valueType")
    },
    { slot: "rate" },
    { slot: "operation" },
]);

const editEquationConfirm = () => {
    emits("editEquationConfirm", tableData.value);
    editEquationDialog.value = false;
    resetEditEquation();
};
const resetEditEquation = () => {
    formSubject.columnTypeDateType = 13;
    formSubject.columnTypeDataType = 1;
    formSubject.operator = 1;
    formSubject.asubId = undefined;
    formSubject.asubAAtypes = "";
    formCells.statementId = customStatementList.value[0]?.statementId;
    formCells.cellName = "A1";
    formCells.operator = 1;
    formStatement.statementId = fixedReportList.value[0].statementId;
    let target = fixedReportList.value?.find((v: IStatementLines) => v.statementId === formStatement.statementId);
    fixedReportItems.value = target?.statementItems.filter((v: IStatementLinesItems) => v.lineNumber !== 0);
    fixedReportDataTypesList.value = target?.dataTypes;
    formStatement.lineID = fixedReportItems.value && fixedReportItems.value[0]?.lineID;
    formStatement.dataType = target && Object.keys(target.dataTypes)[0];
    formStatement.operator = 1;
    tableData.value = [];
};
defineExpose({
    initTableData,
});

const dataTypeOptionAll = ref<Array<any>>([]);
const showDataTypeOption = ref<Array<any>>([]);
const showDateTypeOption = ref<Array<any>>([]);
watchEffect(() => {  
    dataTypeOptionAll.value = dataTypeOption.filter(option =>   
        dataTypeList.value.includes(option.value)  
    );  
    showDataTypeOption.value = JSON.parse(JSON.stringify(dataTypeOptionAll.value));
});
watchEffect(() => {
    showDateTypeOption.value = JSON.parse(JSON.stringify(dateTypeOption));
});
function dataTypeFilterMethod(value: string) {
    showDataTypeOption.value = commonFilterMethod(value, dataTypeOptionAll.value, 'label');
}
function dateTypeFilterMethod(value: string) {
    showDateTypeOption.value = commonFilterMethod(value, dateTypeOption, 'label');
}
</script>

<style lang="less" scoped>
.edit-equation-dialog {
    .edit-equation-dialog-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        :deep(.el-tabs) {
            margin: 15px 20px 0;
            .el-tabs__header {
                height: 28px;
                border: none;
                .el-tabs__nav-wrap {
                    height: 100%;
                    .el-tabs__nav-scroll {
                        justify-content: left;
                        height: 100%;
                        .el-tabs__nav {
                            border: none;
                            border-radius: 6px 6px 0 0;
                            .el-tabs__item {
                                width: 64px;
                                height: 28px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                background-color: #e2e2e2;
                                font-size: var(--h5);
                                line-height: 17px;
                                color: #777777;
                                cursor: pointer;
                                padding: 0;
                                border: none;
                                &.is-active {
                                    background-color: #f8f8f8;
                                    font-weight: 500;
                                    color: var(--main-color);
                                }
                                &:first-child {
                                    border-radius: 6px 0px 0px 0px;
                                }
                                &:last-child {
                                    border-radius: 0px 6px 0px 0px;
                                }
                            }
                            .el-tabs__item + .el-tabs__item {
                                margin-left: 2px;
                            }
                        }
                    }
                }
            }
            .el-tabs__content {
                background-color: #f8f8f8;
                overflow: visible;
                .tab-content {
                    display: flex;
                    align-items: center;
                    padding: 12px 0 12px 16px;

                    .el-form {
                        .el-form-item {
                            margin: 0;
                            .add-subject-input {
                                width: 143px;
                            }
                            .el-form-item__label {
                                padding-right: 8px;
                                color: var(--font-color);
                                font-size: var(--font-size);
                            }
                            .el-form-item__content {
                                line-height: 28px;
                                .el-select {
                                    .el-input__wrapper .el-input__suffix {
                                        display: inline-block;
                                        position: relative;
                                        right: -4px;
                                        .el-input__suffix-inner > :first-child {
                                            margin-left: 0;
                                        }
                                    }
                                }
                            }
                        }
                        .el-form-item + .el-form-item {
                            margin-left: 12px !important;
                        }
                    }
                }
            }
        }
        .btns-contaner {
            margin: 0 20px;
            padding: 6px 0 12px;
            display: flex;
            justify-content: center;
            background-color: #f8f8f8;
        }
        .table-container {
            margin: 12px 20px 16px;
            :deep(.el-table__empty-block) {
                width: 1000px !important;
                min-height: 0;
            }
            :deep(.rate-editor) {
                white-space: nowrap;
                .rate-cell-input {
                    width: 100%;
                    .el-input__wrapper {
                        padding: 1px 21px 1px 11px;
                        .el-input__inner {
                            text-align: right;
                        }
                    }
                }
                input::-webkit-outer-spin-button,
                input::-webkit-inner-spin-button {
                    -webkit-appearance: none !important;
                }
                span {
                    position: relative;
                    left: -20px;
                    top: 1px;
                }
            }
        }
    }
}
</style>
