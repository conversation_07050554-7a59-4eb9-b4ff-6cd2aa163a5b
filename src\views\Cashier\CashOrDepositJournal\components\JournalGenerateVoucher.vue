<template>
    <GenerateVoucher :title="'日记账生成凭证'" :documentTitle="'日记账'" :query-params="genVoucherQueryParameters"
        :error-table-columns="genVoucherErrorTableColumns" @back="genVoucherSaveSuccess" @load-success="loadSuccess = true"
        @save-operate-log="saveVoucherGenerateLog"
        :merge-error-function="mergeErrorFunction" 
        @voucher-changed="genVoucherChangedHandle"
        ref="generateVoucherView">
        <template #toolbar>
            <a class="solid-button" @click="genVoucher()">保存</a>
            <a class="button ml-10" @click="cancelGenVoucher()">取消</a>
            <el-checkbox class="ml27" label="凭证合并" @change="mergeVoucher()" v-model="genVoucherQueryParameters.isMerge"></el-checkbox>
            <SelectCheckbox class="ml10" ref="selectCheckboxRef" inputPlaceholder="请选择凭证合并条件" :useElIcon="true" width="180px" :options="options"
                :select-all="false" @update:selected-list="changeSelectedList" v-show="genVoucherQueryParameters.isMerge" @check-box-show-change="checkBoxShowChange"
                v-model:selectedList="selectedList" :show-all="false" >
            </SelectCheckbox>

            <span class="ml10" style="font-size: 14px;" v-show="genVoucherQueryParameters.isMerge">科目合并</span>
            <SelectCheckbox class="ml10" :useElIcon="true" width="180px" :options="subjectOption" :select-all="false"
                @update:selected-list="changeSubjectSelectedList" v-show="genVoucherQueryParameters.isMerge" ref="selectSubjectRef"
                v-model:selectedList="subjectSelectedList" :place-holder="'请选择科目合并方式'" >
            </SelectCheckbox>         

            <el-checkbox class="ml10"
                label="智能匹配往来单位"
                v-model="genVoucherQueryParameters.autoMatch"
                @change="genVoucherCheckboxChanged(3)"
            ></el-checkbox>
            <BubbleTip :bubble-width="354" :bubble-top="16" class="ml-10">
                <template #content>
                    <div class="help-icon"></div>
                </template>
                <template #tips>
                    <div>根据日记账的收支类型对应的凭证模板</div>
                    <div v-show="!isFarmer && !isUnion && !isVillage">1、智能匹配六大往来（应收账款、其他应收款、预收账款、</div>
                    <div v-show="!isFarmer && !isUnion && !isVillage">应付账款、其他应付款和预付账款）的客户/供应商</div>
                    <div v-show="isFarmer || isVillage">1、智能匹配往来科目（应收款、应付款）的客户/供应商</div>
                    <div v-show="isUnion">1、智能匹配往来科目（其他应收款、其他应付款）的客户/供应商</div>
                    <div>2、自动新增对应的往来单位</div>
                </template>
            </BubbleTip>
            <el-checkbox class="ml10"
                label="AI智能生成凭证"
                v-model="genVoucherQueryParameters.autoAIGenerateOpposite"
                @change="genVoucherCheckboxChanged(4)"
            ></el-checkbox>
            <BubbleTip :bubble-width="354" :bubble-top="16" class="ml-10">
                <template #content>
                    <div class="help-icon"></div>
                </template>
                <template #tips>
                    <div>勾选后，AI会自动根据日记账信息生成凭证。您也可以取消勾选，继续使用日记账收支类别对应的凭证模板生成对方科目</div>
                </template>
            </BubbleTip>
        </template>
    </GenerateVoucher>
</template>
<script setup lang="ts">
import GenerateVoucher from "@/components/GenerateVoucher/index.vue";
import BubbleTip from "@/components/BubbleTip/index.vue";
import { ref } from "vue";
import {
    JournalDocumentModel,
    JournalQueryParameters,
    JournalWithVoucherModel,
    type IBatchGenerateVoucherModel,
    type IGenVoucherNeedInsertAsub,
    type IJournalGenVoucherResult,
    type IVoucherOperateLog,
    type GenVoucherParameters,
    type BaseDocumentModel,
} from "@/components/GenerateVoucher/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IGenerateVoucherPreCheckResult, ITableItem } from "../types";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useLoading } from "@/hooks/useLoading";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import type { IVoucherSetting } from "@/components/Dialog/GenerateVoucherSetting/type";
import type { Option } from "@/components/SelectCheckbox/types";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
const selectCheckboxRef = ref<InstanceType<typeof SelectCheckbox>>();
const voucherSettings = ref<IVoucherSetting>({} as IVoucherSetting);
const setVoucherSettings = (voucherSetting: IVoucherSetting) => {
    voucherSettings.value = voucherSetting;
};
const _ = getGlobalLodash()
const accountSet = useAccountSetStore().accountSet;
const accountStandard = accountSet?.accountingStandard;
const isFarmer = ref(accountStandard === 4 || accountStandard === 5);
const isUnion = ref(accountStandard === 6);
const isVillage = ref(accountStandard === 7);

const props = defineProps<{
    checkDate: string;
    openGenVoucher: () => void;
    cancelGenVoucher: () => void;
    genVoucherSaveSuccess: () => void;
}>();
const generateVoucherView = ref<InstanceType<typeof GenerateVoucher>>();
const genVoucherQueryParameters = ref(new JournalQueryParameters());

const genVoucherChanged = ref(false);
const baseVoucherChanged = ref(false);
const genVoucherErrorTableColumns = ref<IColumnProps[]>([
    {
        label: "单据编号",
        prop: "cdDate",
        align: "left",
        headerAlign: "left",
        width: 160,
        formatter: (row: JournalDocumentModel) => {
            return `${row.jtype === 1010 ? 1 : 2}-${row.cdAccountName.split("-")[0]}-${row.cdDate.replace(/-/g, "")}-${row.lineSn}`;
        },
    },
    {
        label: "收支类别",
        prop: "ietypeName",
        align: "left",
        headerAlign: "left",
        width: 110,
    },
    {
        label: "金额",
        prop: "amount",
        align: "left",
        headerAlign: "left",
        width: 136,
        formatter: (row: JournalDocumentModel) => {
            return row.ietypeName === "内部转账" ? row.income.toFixed(2) :(row.income + row.expenditure).toFixed(2);
        },
    },
    {
        slot: "errorInfo",
    },
]);

// 凭证相关
let isGenVoucher = false;
const genVoucherFromJournal = (checkItems: ITableItem[]) => {
    if (isGenVoucher) {
        ElNotify({ message: "正在生成凭证，请稍后！", type: "warning" });
        return false;
    }
    isGenVoucher = true;
    let existHasVoucherJournal = false;
    //移除可能选中的全选按钮和已生成凭证数据
    for (let j = checkItems.length - 1; j >= 0; j--) {
        if (checkItems[j] === undefined) {
            checkItems.splice(j, 1);
            continue;
        }
        if (checkItems[j].p_id !== "" && checkItems[j].v_id !== "") {
            existHasVoucherJournal = true;
            checkItems.splice(j, 1);
        }
    }
    if (existHasVoucherJournal) {
        ElConfirm("亲，已生成凭证的单据将会被跳过，是否继续生成凭证？").then((r: any) => {
            if (r) {
                getVoucherFromCheckedItems(checkItems);
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else {
        getVoucherFromCheckedItems(checkItems);
    }
};

function saveGenerateVoucherLog(model: IBatchGenerateVoucherModel<JournalWithVoucherModel>) {
    const data : IVoucherOperateLog = {
        operateType: 1010,
        setting: "",
        content: "",
    };

    for (let idx in model.documentWithVoucherList) {
        const documentWithVoucher = model.documentWithVoucherList[idx];
        if (data.content.length > 0) data.content += ",";
        const document = documentWithVoucher.document;
        data.content += `${document.jtype}_${document.cdAccount}_${document.cdDate}_${document.lineSn}`;
    }

    saveVoucherGenerateLog(data);
}

function saveVoucherLog (parameters: GenVoucherParameters<BaseDocumentModel>) {
    const data : IVoucherOperateLog = {
        operateType: 1030,
        setting: "",
        content: "",
    };

    for (let idx in parameters.vouchers) {
        const voucherModel = parameters.vouchers[idx];
        data.content += 'journalId: ' + voucherModel.documentList.map((d) => {
            const journal = d as JournalDocumentModel;
            return `${journal.jtype}_${journal.cdAccount}_${journal.cdDate}_${journal.lineSn}`;
        }).join(",") + '\n';
        
        data.content += 'vgId: '+ voucherModel.voucher.vgId + '\n';
        for (let vIdx in voucherModel.voucher.voucherLines) {
            const voucherLine = voucherModel.voucher.voucherLines[vIdx];
            data.content += `${voucherLine.asubName} ${voucherLine.credit} ${voucherLine.debit}\n`; 
        }
        data.content += '\n';
    }

    saveVoucherGenerateLog(data);
}

function saveVoucherGenerateLog(data: IVoucherOperateLog) {
    data.unixTime = new Date().getTime();
    data.sourceType = 1010;
    data.setting = JSON.stringify(genVoucherQueryParameters.value);
    request({
        url: "/api/Intelligence/SaveVoucherGenerateLog",
        method: "post",
        data: data,
    })
}

const getVoucherFromCheckedItems = (checkItems: ITableItem[]) => {
    // 从缓存中读取凭证合并的选项 
    if(window.localStorage.getItem('genVoucher-journal')){
        const jsonObject = JSON.parse(window.localStorage.getItem('genVoucher-journal') as string);
        let parseObject = jsonObject as JournalQueryParameters;
        Object.setPrototypeOf(parseObject, JournalQueryParameters.prototype);
        if(selectedList.value.length === 0){
            if(parseObject.mergeDate){
                selectedList.value.push(1);
            }
            if(parseObject.mergeIEType){
                selectedList.value.push(2);
            }
            if(parseObject.mergeDirection){
                selectedList.value.push(4);
            }
            if(parseObject.mergeAccount){
                selectedList.value.push(8);
            }
            if(parseObject.isMerge){
                selectedList.value.push(0);
            }
        }
        
        if(subjectSelectedList.value.length === 0){
            if(parseObject.mergeCredit){
                subjectSelectedList.value.push(32);
            }
            if(parseObject.mergeDebit){
                subjectSelectedList.value.push(16);
            }
        }

        genVoucherQueryParameters.value = parseObject;
        
        if(jsonObject.autoAIGenerateOpposite === undefined) {
            genVoucherQueryParameters.value.autoMatch = false;
            genVoucherQueryParameters.value.autoAIGenerateOpposite = true;
        }
    } else{
        genVoucherQueryParameters.value.autoMatch = false;
        genVoucherQueryParameters.value.autoAIGenerateOpposite = true;
    }

    if (checkItems.length > 0) {
        let date = checkItems[0].cd_date;
        //由于资金数据是按日期顺序排列的，所以只需要校验第一行的日期是否已结账即可
        const jDate = new Date(date.replace(/-/g, "/"));
        const cDate = new Date(props.checkDate.replace(/-/g, "/"));
        if (cDate > jDate) {
            isGenVoucher = false;
            ElNotify({ message: "已结账期间资金数据不能生成凭证数据", type: "warning" });
            return false;
        }
        getCashierAndVoucherInfo(checkItems, voucherSettings.value);
    } else {
        isGenVoucher = false;
        ElNotify({ message: "请选择凭据后生成凭证！", type: "warning" });
        return false;
    }
};
let genVoucherList: ITableItem[] = [];
const getCashierAndVoucherInfo = (checkItems?: ITableItem[], voucherSettings?: IVoucherSetting) => {
    useLoading().enterLoading("努力加载中，请稍后...");
    if (checkItems) {
        genVoucherList = checkItems;
    }
    const list = genVoucherList.map((item: ITableItem) => {
        return {
            cdAccount: item.cd_account,
            date: item.cd_date,
            line_Sn: item.line_sn,
            created_date: item.created_date,
        };
    });
    const cdAccountList = [];
    for (let i = 0; i < list.length; i++) {
        if (cdAccountList.indexOf(list[i].cdAccount) === -1) {
            cdAccountList.push(list[i].cdAccount);
        }
    }
    const parameters = {
        cdAccountList: cdAccountList,
        lineSnList: list,
        autoMatch: genVoucherQueryParameters.value.autoMatch,
        autoAIGenerateOpposite: genVoucherQueryParameters.value.autoAIGenerateOpposite,
        voucherSettings: voucherSettings,
    };
    request({
        url: "/api/JournalVoucher/JournalGenerateVoucherPreCheck",
        method: "post",
        data: parameters,
    }).then((res: any) => {
        const data = res as IResponseModel<IGenerateVoucherPreCheckResult>;
        if (data.state === 1000) {
            if (data.data.result) {
                request({
                    url: "/api/JournalVoucher/GetVouchersFromJournal",
                    method: "post",
                    data: parameters,
                }).then((res: IResponseModel<any>) => {
                    if (res.state !== 1000) {
                        useLoading().quitLoading();
                        ElNotify({ message: res.msg || "出现错误，请稍后重试！", type: "warning" });
                        isGenVoucher = false;
                        loadSuccess.value = true;
                        return;
                    }
                    const data = res as IResponseModel<IBatchGenerateVoucherModel<JournalWithVoucherModel>>;
                    genVoucherChanged.value = false;
                    baseVoucherChanged.value = false;
                    // 如果获取的数据里有错误数据，就不可以合并凭证
                    if(genVoucherQueryParameters.value.isMerge === true){
                        if (!checkMergeVoucher(data.data)) {
                            genVoucherQueryParameters.value.isMerge = false;
                        } 
                    }

                    generateVoucherView.value?.loadDocumentList(data.data, voucherSettings);
                    props.openGenVoucher();
                
                    saveGenerateVoucherLog(data.data);
                    const timer = setTimeout(() => {
                        useLoading().quitLoading();
                        isGenVoucher = false;
                        clearTimeout(timer);
                    }, 1000);
                }).catch(() => {
                    useLoading().quitLoading();
                    isGenVoucher = false;
                    loadSuccess.value = true;
                    ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
                });
            } else {
                useLoading().quitLoading();
                isGenVoucher = false;
                loadSuccess.value = true;
                ElConfirm(data.data.msg).then((r) => {
                    if (r) {
                        switch (data.data.errorType) {
                            case "Account":
                                if (checkPermission(["cdaccount-canedit"])) {
                                    globalWindowOpenPage(
                                        "/Cashier/CDAccount?editAcType=" + data.data.acType + "&editAcId=" + data.data.acId,
                                        "账户设置"
                                    );
                                } else {
                                    ElNotify({ type: "warning", message: "您没有此功能权限！" });
                                }
                                break;
                            case "NoTemplate":
                                emit("no-template", data.data.ietype, data.data.ietypeId);
                                break;
                            case "ErrorTemplate":
                                break;
                            default:
                                break;
                        }
                    }
                });
            }
        } else if (data.state === 2000) {
            useLoading().quitLoading();
            isGenVoucher = false;
            loadSuccess.value = true;
            ElNotify({ message: data.msg || "出现错误，请稍后重试！", type: "warning" });
        } else {
            useLoading().quitLoading();
            isGenVoucher = false;
            loadSuccess.value = true;
            ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
        }
    }).catch(() => {
        useLoading().quitLoading();
        isGenVoucher = false;
        loadSuccess.value = true;
        ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
    });
};

// 如果获取的数据里有错误数据，就不可以合并凭证
const checkMergeVoucher = ( data: IBatchGenerateVoucherModel<JournalWithVoucherModel>) => {
    if (data) {
        for (let i = 0; i < data.documentWithVoucherList.length; i++) {
            if (data.documentWithVoucherList[i].document.errorInfo !== "") {
                ElNotify({ message: "亲，生成的凭证存在错误暂不支持合并，请依据错误提示修改凭证后再进行合并哦！", type: "warning" });
                return false;
            }
        }
    }
    return true;
}

const emit = defineEmits<{
    (event: "no-template", ietype: number, ietypeId: number): void;
}>();
function genVoucherChangedHandle() {    
    genVoucherChanged.value = true;
}

const genVoucher = _.debounce(saveGenVoucher,500)

let isSaving = false;
async function saveGenVoucher() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) {
        ElNotify({ message: "正在保存，请稍后！", type: "warning" });
        return;
    }
    isSaving = true;
    const parameters = generateVoucherView.value?.getGenVoucherParameters();
    if (parameters) {
        useLoading().enterLoading("努力加载中，请稍后...");
        request({
            url: "/api/JournalVoucher/FindNeedAutoInsertAccountSubject",
            method: "post",
            data: parameters,
        })
            .then((res: any) => {
                const data = res as IResponseModel<IGenVoucherNeedInsertAsub>;
                if (data.state === 1000) {
                    new Promise((resolve, reject) => {
                        for (let i = 0; i < parameters.temporaryAccountSubjectList.length; i++) {
                            if (
                                parameters.temporaryAccountSubjectList[i].asubCode
                                    .toString()
                                    .indexOf(parameters.temporaryAccountSubjectList[i].parentAsubCode.toString()) !== 0
                            ) {
                                isSaving = false;
                                useLoading().quitLoading();
                                ElConfirm("您的科目编码长度不足，智能匹配失败，是否立即前往科目编码设置？").then((r) => {
                                    if (r) {
                                        globalWindowOpenPage("/Settings/AccountSubject", "科目");
                                    }
                                });
                                reject();
                                return;
                            }
                        }
                        if (data.data.autoInsertAsubList.length > 0) {
                            const parentAsubs = [];
                            const asubs = [];
                            for (let i = 0; i < data.data.autoInsertAsubList.length; i++) {
                                parentAsubs.push(data.data.autoInsertAsubList[i].parentAsubName);
                                asubs.push(data.data.autoInsertAsubList[i].asubName);
                            }
                            const msg =
                                '<div>' +
                                parentAsubs.join("、") +
                                "已有凭证，将新增同名下级科目"+ '<br>' +
                                asubs.join("、") +
                                "替代，您要继续吗？" +
                                "</div>";
                            useLoading().quitLoading();
                            ElAlert({ message: msg, leftJustifying: true }).then((r) => {
                                if (r) {
                                    useLoading().enterLoading("努力加载中，请稍后...");
                                    resolve(0);
                                } else {
                                    isSaving = false;
                                    reject();
                                }
                            });
                        } else {
                            resolve(0);
                        }
                    }).then(() => {
                        request({
                            url: "/api/JournalVoucher/Submit",
                            method: "post",
                            data: parameters,
                        })
                            .then((res: IResponseModel<IJournalGenVoucherResult>) => {
                                useLoading().quitLoading();
                                const data = res;
                                if (data.state === 1000) {
                                    dispatchReloadAsubAmountEvent();
                                    useAccountSubjectStore().getAccountSubject();
                                    useAssistingAccountingStore().getAssistingAccounting();
                                    generateVoucherView.value?.loadSaveResult(data.data);
                                    saveVoucherLog(parameters);
                                    const timer = setTimeout(() => {
                                        isSaving = false;
                                        clearTimeout(timer);
                                    }, 1000);
                                }else{
                                    isSaving = false;
                                    if (data.msg != null && data.msg != undefined && (data.msg.indexOf("将截断字符串或二进制数据") != -1 || data.msg.indexOf("String or binary data would be truncated") != -1)) {
                                        ElNotify({
                                            message: "保存出错了，请检查往来单位不要超过64个汉字！",
                                            type: "warning",
                                        });
                                    }
                                    else {
                                        const message = data.msg?.includes("凭证重复") ? data.msg : "保存出错了，请刷新页面重试或联系客服处理！";
                                        ElNotify({ message,  type: "warning" });
                                    }
                                }
                            })
                            .catch((err: any) => {
                                if (err.code !== "ERR_NETWORK") {
                                    isSaving = false;
                                    useLoading().quitLoading();
                                }
                            });
                    });
                } else {
                    isSaving = false;
                    useLoading().quitLoading();
                }
            })
            .catch(() => {
                isSaving = false;
                useLoading().quitLoading();
            });
    }else{
        isSaving = false;
    }
}

const loadSuccess = ref(true);
function genVoucherCheckboxChanged(flag: number, selectedist?: any) {
    new Promise<boolean>((resolve) => {
        if (genVoucherChanged.value || baseVoucherChanged.value && flag === 3 ) {
            ElConfirm("系统可能不会保存您做的更改，确定要切换吗？").then((r) => {
                if (r) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(true);
        }
    }).then((r) => {
        if (r) {
            if (flag > 0 && selectedist !== undefined) {
                generateVoucherView.value?.loadDocumentList(undefined, voucherSettings.value);
                genVoucherChanged.value = false;
                window.localStorage.setItem('genVoucher-journal', JSON.stringify(genVoucherQueryParameters.value));
            } else {
                if (flag === 3 || flag === 4) {
                    if (flag === 3) {
                        if(genVoucherQueryParameters.value.autoMatch) {
                            genVoucherQueryParameters.value.autoAIGenerateOpposite = false;
                        }
                    } else if (flag === 4)  {
                        if(genVoucherQueryParameters.value.autoAIGenerateOpposite) {
                            genVoucherQueryParameters.value.autoMatch = false;
                        }
                    }
                    window.localStorage.setItem('genVoucher-journal', JSON.stringify(genVoucherQueryParameters.value));
                    getCashierAndVoucherInfo(undefined, voucherSettings.value);
                } else{
                    generateVoucherView.value?.loadDocumentList(undefined, voucherSettings.value);
                    genVoucherChanged.value = false; 
                    window.localStorage.setItem('genVoucher-journal', JSON.stringify(genVoucherQueryParameters.value));
                }
                
            }
        } else if(selectedist !== undefined) {
            selectedist.value = _.cloneDeep(backSelectedList.value);
            loadSuccess.value = true;
        } else if(flag === 3){
            genVoucherQueryParameters.value.autoMatch = !genVoucherQueryParameters.value.autoMatch;
            if(genVoucherQueryParameters.value.autoMatch) {
                genVoucherQueryParameters.value.autoAIGenerateOpposite = false;
            }
            loadSuccess.value = true;
        } else if(flag === 4){
            genVoucherQueryParameters.value.autoAIGenerateOpposite = !genVoucherQueryParameters.value.autoAIGenerateOpposite;
            if(genVoucherQueryParameters.value.autoAIGenerateOpposite) {
                genVoucherQueryParameters.value.autoMatch = false;
            }
            loadSuccess.value = true;
        } else {
            genVoucherQueryParameters.value.isMerge = !genVoucherQueryParameters.value.isMerge;
            loadSuccess.value = true;
        }
    });
}

const options = ref<Array<Option>>([
    {
        id: 0,
        name: "合并成一张凭证",
    },
    {
        id: 1,
        name: "按日期分开合并",
    },
    {
        id: 2,
        name: "按收支类别分开合并",
    },
    {
        id: 4,
        name: "按收支方向分开合并",
    },
    {
        id: 8,
        name: "按账户分开合并",  // 选择多个账户时才会出现
    }
]);
const subjectOption = ref<Array<Option>>([
    {
        id: 16,
        name: "相同借方科目合并",
    },
    {
        id: 32,
        name: "相同贷方科目合并",
    },
]);
const selectedList = ref<number[]>([]);
const subjectSelectedList = ref<number[]>([]);
const backSelectedList = ref<number[]>([]);
const selectSubjectRef = ref();
const selectZero = ref(true);

function changeSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if(selectZero.value && val.length > 1 && val[0] === 0 ){
        val.splice(0, 1);
        selectZero.value = false;       
    } else if(val.findIndex(z=>z === 0) !== -1){
        selectZero.value = true;
        val.splice(0);
        val.push(0);
    }

    backSelectedList.value = _.cloneDeep(selectedList.value);
    selectedList.value = val;
    genVoucherQueryParameters.value.mergeDate = val.includes(1) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeIEType = val.includes(2) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeDirection = val.includes(4) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeAccount = val.includes(8) && genVoucherQueryParameters.value.isMerge;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(1, selectedList);
}

function checkBoxShowChange(val: boolean) {
    if (!val && selectedList.value.length === 0 && genVoucherQueryParameters.value.isMerge) {
        ElNotify({ message: "凭证合并默认需要勾选一个条件哦~", type: "warning" });
        genVoucherQueryParameters.value.isMerge = true;
        selectedList.value.push(0);
        selectZero.value = true;
        genVoucherCheckboxChanged(1, selectedList);
    }
}

function mergeVoucher(){
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if(genVoucherQueryParameters.value.isMerge){
        if(!generateVoucherView.value?.checkMergeVoucher()){
            genVoucherQueryParameters.value.isMerge = false;
            return;
        }
    }

    loadSuccess.value = false;
    if(genVoucherQueryParameters.value.isMerge){
        genVoucherQueryParameters.value.mergeOthers = true;
        if(selectedList.value.length === 0 && subjectSelectedList.value.length === 0){
            selectedList.value.push(0);
            subjectSelectedList.value.push(16);
            subjectSelectedList.value.push(32);
            selectSubjectRef.value?.changeSelectAll(true);
            genVoucherQueryParameters.value.mergeCredit = true;
            genVoucherQueryParameters.value.mergeDebit = true;
        }

        if(genVoucherChanged.value){
            baseVoucherChanged.value = true;
            genVoucherChanged.value = false;
        }
        genVoucherCheckboxChanged(1, selectedList);
    } else {
        genVoucherCheckboxChanged(0, undefined);
    }
}

function changeSubjectSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    backSelectedList.value = _.cloneDeep(subjectSelectedList.value);
    subjectSelectedList.value = val;
    genVoucherQueryParameters.value.mergeDebit = val.includes(16) && genVoucherQueryParameters.value.mergeOthers;
    genVoucherQueryParameters.value.mergeCredit = val.includes(32) && genVoucherQueryParameters.value.mergeOthers;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(2, subjectSelectedList);
}

function mergeErrorFunction() {
    genVoucherQueryParameters.value.mergeCredit = false;
    genVoucherQueryParameters.value.mergeDebit = false;
    subjectSelectedList.value = [];
}

function checkIsEdited() {
    return generateVoucherView.value?.checkIsEdited();
}

defineExpose({
    genVoucherFromJournal, setVoucherSettings, checkIsEdited
});
</script>
