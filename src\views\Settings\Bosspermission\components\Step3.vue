<template>
    <div class="step-box">
        <div class="content-row">
            <div class="content-left width-five qr-left high">
                <div class="width-five">&nbsp;</div>
                <div class="width-five tx-left">
                    <span class="label-bold">绑定【老板看账】账号</span>
                    <span class="label-color mt-10" style="display: block">登录已授权的账号 > 绑定成功</span>
                </div>
            </div>
            <div class="content-right width-five mt-20">
                <img src="@/assets/Settings/4-1.png" class="boss-high-img left" alt="4-1.png" />
                <img src="@/assets/Settings/4-2.png" class="boss-high-img" alt="4-2.png" />
            </div>
        </div>
        <div class="content-row tx-center bottom-operate">
            <a class="button mr-20" @click="reduceStage">上一步</a>
            <a class="button solid-button long" @click="completeStage">完成绑定</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const isErp = ref<boolean>(window.isErp);

const emit = defineEmits(["reduce-stage", "complete-stage"]);
const reduceStage = () => emit("reduce-stage", 2);
const completeStage = () => emit("complete-stage");

</script>

<style lang="less" scoped>
@import "@/style/Settings/BossPermission.less";
</style>
