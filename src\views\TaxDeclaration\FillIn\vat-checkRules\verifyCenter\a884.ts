// @ts-nocheck
/* eslint-disable */
// var i = require("./24cc.js");
// var r = require("./e294.js");
import i from './24cc.js';
import r from './e294.js';
function n(t) {
  n = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function (t) {
    return typeof t;
  } : function (t) {
    if (t && typeof Symbol == "function" && t.constructor === Symbol && t !== Symbol.prototype) {
      return "symbol";
    } else {
      return typeof t;
    }
  };
  return n(t);
}
function l(t, e) {
  var a = typeof Symbol != "undefined" && t[Symbol.iterator] || t["@@iterator"];
  if (!a) {
    if (Array.isArray(t) || (a = u(t)) || e && t && typeof t.length == "number") {
      if (a) {
        t = a;
      }
      var i = 0;
      function r() {}
      return {
        s: r,
        n: function () {
          if (i >= t.length) {
            return {
              done: true
            };
          } else {
            return {
              done: false,
              value: t[i++]
            };
          }
        },
        e: function (t) {
          throw t;
        },
        f: r
      };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var n;
  var l = true;
  var o = false;
  return {
    s: function () {
      a = a.call(t);
    },
    n: function () {
      var t = a.next();
      l = t.done;
      return t;
    },
    e: function (t) {
      o = true;
      n = t;
    },
    f: function () {
      try {
        if (!l && a.return != null) {
          a.return();
        }
      } finally {
        if (o) {
          throw n;
        }
      }
    }
  };
}
function o(t) {
  return function (t) {
    if (Array.isArray(t)) {
      return p(t);
    }
  }(t) || function (t) {
    if (typeof Symbol != "undefined" && t[Symbol.iterator] != null || t["@@iterator"] != null) {
      return Array.from(t);
    }
  }(t) || u(t) || function () {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function d(t, e) {
  var a = Object.keys(t);
  if (Object.getOwnPropertySymbols) {
    var i = Object.getOwnPropertySymbols(t);
    if (e) {
      i = i.filter(function (e) {
        return Object.getOwnPropertyDescriptor(t, e).enumerable;
      });
    }
    a.push.apply(a, i);
  }
  return a;
}
function s(t) {
  for (var e = 1; e < arguments.length; e++) {
    var a = arguments[e] ?? {};
    if (e % 2) {
      d(Object(a), true).forEach(function (e) {
        b(t, e, a[e]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(t, Object.getOwnPropertyDescriptors(a));
    } else {
      d(Object(a)).forEach(function (e) {
        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(a, e));
      });
    }
  }
  return t;
}
function c(t, e) {
  return function (t) {
    if (Array.isArray(t)) {
      return t;
    }
  }(t) || function (t, e) {
    var a = t == null ? null : typeof Symbol != "undefined" && t[Symbol.iterator] || t["@@iterator"];
    if (a != null) {
      var i;
      var r;
      var n;
      var l;
      var o = [];
      var d = true;
      var s = false;
      try {
        n = (a = a.call(t)).next;
        if (e === 0) {
          if (Object(a) !== a) {
            return;
          }
          d = false;
        } else {
          for (; !(d = (i = n.call(a)).done) && (o.push(i.value), o.length !== e); d = true);
        }
      } catch (t) {
        s = true;
        r = t;
      } finally {
        try {
          if (!d && a.return != null && (l = a.return(), Object(l) !== l)) {
            return;
          }
        } finally {
          if (s) {
            throw r;
          }
        }
      }
      return o;
    }
  }(t, e) || u(t, e) || function () {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function u(t, e) {
  if (t) {
    if (typeof t == "string") {
      return p(t, e);
    }
    var a = {}.toString.call(t).slice(8, -1);
    if (a === "Object" && t.constructor) {
      a = t.constructor.name;
    }
    if (a === "Map" || a === "Set") {
      return Array.from(t);
    } else if (a === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)) {
      return p(t, e);
    } else {
      return undefined;
    }
  }
}
function p(t, e) {
  if (e == null || e > t.length) {
    e = t.length;
  }
  for (var a = 0, i = Array(e); a < e; a++) {
    i[a] = t[a];
  }
  return i;
}
function v(t, e) {
  for (var a = 0; a < e.length; a++) {
    var i = e[a];
    i.enumerable = i.enumerable || false;
    i.configurable = true;
    if ("value" in i) {
      i.writable = true;
    }
    Object.defineProperty(t, f(i.key), i);
  }
}
function b(t, e, a) {
  if ((e = f(e)) in t) {
    Object.defineProperty(t, e, {
      value: a,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    t[e] = a;
  }
  return t;
}
function f(t) {
  var e = function (t, e) {
    if (n(t) != "object" || !t) {
      return t;
    }
    var a = t[Symbol.toPrimitive];
    if (a !== undefined) {
      var i = a.call(t, e || "default");
      if (n(i) != "object") {
        return i;
      }
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (e === "string" ? String : Number)(t);
  }(t, "string");
  if (n(e) == "symbol") {
    return e;
  } else {
    return e + "";
  }
}
function g(t) {
  return JSON.parse(JSON.stringify(t));
}
var h = /getObjFromList\([^)]+\)\.[a-zA-Z0-9_]+/g;
var y = /sumList\s*\([^)]+\)/g;
var m = /customGet_[a-zA-Z0-9_]+/g;
var _ = Array.prototype.flat ? "flat" : "flatten";
function w(t) {
  if ([NaN, Infinity].includes(t)) {
    return t;
  } else {
    return JSON.stringify(t, function (t, e) {
      if (Number.isNaN(e)) {
        return "NaN";
      } else if (e === Infinity) {
        return "Infinity";
      } else {
        return e;
      }
    });
  }
}
export default (function (t, e, a) {
  if (e) {
    v(t.prototype, e);
  }
  if (a) {
    v(t, a);
  }
  Object.defineProperty(t, "prototype", {
    writable: false
  });
  return t;
})(function t() {
  var e = this;
  var a = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var n = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  var l = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : "";
  var o = arguments.length > 3 ? arguments[3] : undefined;
  var d = arguments.length > 4 ? arguments[4] : undefined;
  var u = arguments.length > 5 ? arguments[5] : undefined;
  (function (t, e) {
    if (!(t instanceof e)) {
      throw new TypeError("Cannot call a class as a function");
    }
  })(this, t);
  b(this, "round", function (t, e) {
    return Number(Object(i.default)(Number(t), e));
  });
  b(this, "rangeExec", function (t, a, i, n) {
    if (!i.includes("sumList") && typeof n == "number") {
      t = n;
      a = n + 1;
    }
    for (var l = t; l < a; l++) {
      var o = c(i.replace(/^\s*['"](.+)['"]\s*$/, "$1").replace("===", "@@@").split("@@@"), 2);
      var d = o[0];
      var s = o[1];
      var u = /\[\*\]/g;
      var p = d.replace(u, `[${l}]`);
      var v = s.replace(u, `[${l}]`);
      var b = e.callCustomFunction(r.default.content, p);
      var f = b;
      try {
        if ((f = e.callCustomFunction(r.default.content, v)) !== b) {
          e.callCustomFunction(r.default.content, `${p} = ${w(f)}`);
        }
      } catch (t) {
        e.setIntoErrorFormulas(i, t.message);
      }
      if (d && f !== b) {
        e.doCalDep(d, r.default.content, true, l);
      }
    }
  });
  b(this, "errorFormulas", []);
  b(this, "errorFormulasMap", {});
  this.formulas = a;
  this.allRules = n.filter(function (t) {
    return Number(t.isEnable) !== 0;
  });
  this.rules = n.filter(function (t) {
    return Number(t.validateType) !== 3 && Number(t.isEnable) !== 0;
  }).map(function (t) {
    return s(s({}, t), {}, {
      type: Number(t.validateType) === 1 ? "error" : "warning",
      preTables: t.preTable.split("&&")
    });
  });
  this.root = l;
  this.funcs = o;
  this.rootJson = u;
  this.effectMap = {};
  this.runtimeEffectMap = {};
  this.ruleMap = {};
  this.runtimeRuleMap = {};
  this.funcDepMap = {};
  this.newFunctionCache = {};
  this.generateFuncDepMap(d);
  this.generateMap();
  this.hasInitFuncs = false;
  this.validateResult = {};
  this.runtimeValidRules = [];
  this.nonDynamic = {};
}, [{
  key: "setNonDynamic",
  value: function (t) {
    this.nonDynamic = t || {};
  }
}, {
  key: "generateFuncDepMap",
  value: function (t) {
    var e = this;
    this.funcs.forEach(function (t) {
      if (t.functionDm.includes("customGet_")) {
        var a = [];
        try {
          a = t.dependencies && t.dependencies.startsWith("function") ? e.getFunction(`return ${t.dependencies}`)().call(e.rootJson) : JSON.parse(t.dependencies);
        } catch (t) {}
        e.funcDepMap[t.functionDm] = a || [];
      }
    });
  }
}, {
  key: "getFuncDep",
  value: function (t) {
    var e = this;
    return this.funcDepMap[t].map(function (t) {
      if (t.includes("customGet_")) {
        return o(e.getFuncDep(t));
      } else {
        return t;
      }
    });
  }
}, {
  key: "generateMap",
  value: function () {
    var t = this;
    this.formulas.forEach(function (e) {
      try {
        var a = c(e.replace("===", "@@@").split("@@@"), 2);
        var i = a[0];
        var r = a[1];
        if ((i = i.trim()).includes("rangeExec")) {
          i = i.split(",").splice(2).join(",").replace(/\s/g, "").replace(/^["']/g, "");
        }
        var n;
        var o = l(t.getFormulaDeps(r));
        try {
          for (o.s(); !(n = o.n()).done;) {
            var d = n.value;
            if (d !== i) {
              t.effectMap[d] ||= [];
              if (!t.effectMap[d].includes(e)) {
                t.effectMap[d].push(e);
              }
            }
          }
        } catch (t) {
          o.e(t);
        } finally {
          o.f();
        }
      } catch (t) {}
    });
    this.rules.forEach(function (e) {
      try {
        var a;
        var i = l(t.getFormulaDeps(e.ruleData));
        try {
          for (i.s(); !(a = i.n()).done;) {
            var r = a.value;
            t.ruleMap[r] ||= [];
            if (!t.ruleMap[r].includes(e)) {
              t.ruleMap[r].push(e);
            }
          }
        } catch (t) {
          i.e(t);
        } finally {
          i.f();
        }
      } catch (t) {}
    });
  }
}, {
  key: "resetRuntimeEffectMap",
  value: function (t) {
    var e = this;
    var a = this.getDynamicKeyMap(t).depsKeyMap;
    var i = {};
    Object.keys(a).forEach(function (t) {
      var r = a[t];
      if (r !== undefined) {
        var n = e.effectMap[t] || [];
        r.forEach(function (t) {
          var a = e.effectMap[t];
          if (Array.isArray(a)) {
            n = n.concat(a);
          }
        });
        n = Array.from(new Set(n));
        i[t] = n;
      }
    });
    this.runtimeEffectMap = i;
  }
}, {
  key: "resetRuntimeRuleMap",
  value: function (t) {
    var e = this;
    var a = this.getDynamicKeyMap(t).depsKeyMap;
    var i = {};
    Object.keys(a).forEach(function (t) {
      var r = a[t];
      if (r !== undefined) {
        var n = e.ruleMap[t] || [];
        r.forEach(function (t) {
          var a = e.ruleMap[t];
          if (Array.isArray(a)) {
            n = n.concat(a);
          }
        });
        i[t] = n;
      }
    });
    this.runtimeRuleMap = i;
  }
}, {
  key: "getFormulaDeps",
  value: function (t) {
    var e = this;
    var a = this.root ? new RegExp(`${this.root.replace(".", "\\.")}\\.[a-zA-Z0-9_\\.]+(\\[(\\*|\\d+)\\])*[a-zA-Z0-9_\\.]*`, "g") : /[a-zA-Z0-9_\.]+(\[(\*|\d+)\])*[a-zA-Z0-9_\.]+/g;
    var i = [];
    (t.match(m) || []).forEach(function (t) {
      if (t.includes("customGet_")) {
        i = i.concat(e.getFuncDep(t)[_]());
      } else {
        i.push(t);
      }
    });
    i = i.concat(t.match(h) || []);
    var r = ((t = t.replace(h, "1")).match(y) || []).map(function (t) {
      var e = t.trim().replace(/^sumList\s*\((.+)\)$/, "$1").split(",").map(function (t) {
        return t.replace(/['"\s]/g, "");
      });
      var a = [];
      if (e.length > 2 && e[3] && !e[3].includes("length")) {
        for (var i = Number(e[2]) || 0; i <= (Number(e[3]) || 0); i++) {
          if (e[4]) {
            a.push(`getObjFromList(${e[0]},'${e[4]}','${i}').${e[1]}`);
          } else {
            a.push(`${e[0]}[${i}].${e[1]}`);
          }
        }
      } else {
        a = `${e[0]}[*].${e[1]}`;
      }
      return a;
    });
    i = i.concat(r[_](99));
    t = t.replace(y, "1");
    return i = i.concat(t.match(a) || []);
  }
}, {
  key: "getKeyByChange",
  value: function (t, e, a = "") {
    if (e === undefined) {
      return null;
    }
    for (var i = Object.keys(t), r = [], l = 0; l < i.length; l++) {
      var o = i[l];
      var d = t[o];
      if (n(d) === "object" && d) {
        r = r.concat(this.getKeyByChange(d, e[o], require ? `${require}['${o}']` : `['${o}']`));
      } else if (d !== e[o] && (n(d) !== "object" || !d)) {
        r.push(o);
      }
    }
    return r;
  }
}, {
  key: "initFunc",
  value: function (t) {
    if (!this.hasInitFuncs) {
      r.default.initFunc(t, this.funcs);
    }
    this.hasInitFuncs = true;
  }
}, {
  key: "calAll",
  value: function () {
    if (!this.hasInitFuncs) {
      r.default.initFunc(this.rootJson, this.funcs);
    }
    this.hasInitFuncs = true;
    var t = this.rootJson.rangeExec;
    this.rootJson.rangeExec = this.rangeExec;
    this.rootJson.round = this.round;
    var e;
    var a = l(this.formulas);
    try {
      for (a.s(); !(e = a.n()).done;) {
        var i = e.value;
        this.calEachFormula(i, this.rootJson);
      }
    } catch (t) {
      a.e(t);
    } finally {
      a.f();
    }
    this.errorFormulas.forEach(function (t, e) {});
    this.rootJson.rangeExec = t;
  }
}, {
  key: "calAllLocalSumlist",
  value: function () {
    if (!this.hasInitFuncs) {
      r.default.initFunc(this.rootJson, this.funcs);
    }
    this.hasInitFuncs = true;
    var t = this.rootJson.rangeExec;
    this.rootJson.rangeExec = this.rangeExec;
    this.rootJson.round = this.round;
    var e;
    var a = l(this.formulas);
    try {
      for (a.s(); !(e = a.n()).done;) {
        var i = e.value;
        if (/localData/gi.test(i)) {
          this.calEachFormula(i, this.rootJson);
        }
      }
    } catch (t) {
      a.e(t);
    } finally {
      a.f();
    }
    this.errorFormulas.forEach(function (t, e) {});
    this.rootJson.rangeExec = t;
  }
}, {
  key: "callCustomFunction",
  value: function (t, e) {
    r.default.content = t;
    var a = `with(this){return ${e.replace(/ /gi, "")}}`;
    return this.getFunction(a).call(t);
  }
}, {
  key: "getFunction",
  value: function (t) {
    var e = t.replace(/ /gi, "");
    this.newFunctionCache[e] ||= new Function(t);
    return this.newFunctionCache[e];
  }
}, {
  key: "calEachFormula",
  value: function (t, e) {
    var a;
    var i = t;
    if (t) {
      var r = t.includes("rangeExec");
      try {
        if (r) {
          var n = e.rangeExec;
          e.rangeExec = this.rangeExec;
          this.callCustomFunction(e, t);
          e.rangeExec = n;
          return;
        }
        a = t.replace("===", "@@@").split("@@@")[0].trim();
      } catch (t) {
        this.setIntoErrorFormulas(i, t.message, e);
        return;
      }
      t = t.replace("===", "=");
      try {
        this.callCustomFunction(e, t);
      } catch (t) {
        this.setIntoErrorFormulas(i, t.message, e);
        return;
      }
      if (a) {
        this.doCalDep(a, e);
      }
    }
  }
}, {
  key: "getDepFormulas",
  value: function (t, e = false, a = 0, i = false) {
    var r = this.runtimeEffectMap[t] || this.effectMap[t] || [];
    if (t.includes("[*]") && e) {
      r = r.concat(this.effectMap[t.replace("[*]", `[${require}]`)]);
      r = Array.from(new Set(r)).filter(Boolean);
    }
    if (i) {
      r = r.filter(function (t) {
        return t.match(/sumList\s*\([^)]+\)/g);
      });
    }
    return r;
  }
}, {
  key: "dataChange",
  value: function (t, e, a, i) {
    this.runtimeValidRules = [];
    this.calDep(t, e, a, i);
    var r = this.checkRulesWithDepsByDep(e, t, i);
    this.runtimeValidRules = [];
    return r;
  }
}, {
  key: "calDep",
  value: function (t, e, a, i) {
    if (!this.hasInitFuncs) {
      r.default.initFunc(e, this.funcs);
    }
    this.hasInitFuncs = true;
    e.round = this.round;
    this.doCalDep(t, e, a.dynamic, a.dynamicIndex, i);
  }
}, {
  key: "doCalDep",
  value: function (t, e) {
    var a = this;
    var i = arguments.length > 2 && arguments[2] !== undefined && arguments[2];
    var r = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
    var n = arguments.length > 4 && arguments[4] !== undefined && arguments[4];
    this.resetRuntimeEffectMap(e);
    this.resetRuntimeRuleMap(e);
    this.runtimeValidRules = this.runtimeValidRules.concat(this.getDepRules(t, i, r));
    this.getDepFormulas(t, i, r, n).forEach(function (l) {
      var o = l.includes("rangeExec");
      if (o && i) {
        var d = t.split("[*]");
        if ((d ? d[0] : t) === l.match(/[a-zA-Z\._]+(?=\[\*\])/g)[0]) {
          l = l.replace(/\)\s*$/, `,${r})`);
        }
      }
      var s = 0;
      if (o) {
        var u = e.rangeExec;
        e.rangeExec = a.rangeExec;
        try {
          s = a.callCustomFunction(e, l);
        } catch (t) {
          a.setIntoErrorFormulas(l, t.message, e);
        }
        e.rangeExec = u;
      } else {
        var p = c(l.replace("===", "@@@").split("@@@"), 2);
        var v = p[0];
        var b = p[1];
        v = v.trim();
        try {
          s = a.callCustomFunction(e, b);
        } catch (t) {
          a.setIntoErrorFormulas(l, t.message, e);
        }
        try {
          if (a.getFunction(`with(this){  return ${v}}`).call(e) === s) {
            return;
          }
        } catch (t) {}
        try {
          a.getFunction(`with(this){  ${v} = ${w(s)}}`).call(e);
        } catch (t) {}
        if (i) {
          /\[\d+\]/g.test(v);
        }
        if (a.effectMap[v] && a.effectMap[v].length > 0) {
          a.doCalDep(v, e);
        } else {
          a.runtimeValidRules = a.runtimeValidRules.concat(a.getDepRules(v));
        }
        if (/\[\d+\]/.test(v)) {
          var f = c(v.match(/\[(\d+)\]/), 2)[1];
          var g = v.replace(/\[\d+\]/, "[*]");
          if (a.effectMap[g] && a.effectMap[g].length > 0) {
            a.doCalDep(g, e, true, f, n);
          } else {
            a.runtimeValidRules = a.runtimeValidRules.concat(a.getDepRules(g, true, f));
          }
        }
      }
    });
  }
}, {
  key: "setIntoErrorFormulas",
  value: function (t, e, a) {
    if (!this.errorFormulas.includes(t)) {
      this.errorFormulas.push(t);
    }
    this.errorFormulasMap[t] = e;
    r.default.errorFormulasMap = this.errorFormulasMap;
  }
}, {
  key: "initCheckFunc",
  value: function (t) {
    if (this.hasInitFuncs) {
      r.default.initFunc(t, this.funcs.filter(function (t) {
        return t.functionDm === "rangeExec";
      }));
    } else {
      r.default.initFunc(t, this.funcs);
    }
  }
}, {
  key: "getDepRules",
  value: function (t, e = false, a = 0) {
    var i = t ? this.runtimeRuleMap[t] || this.ruleMap[t] || [] : this.rules;
    if (t != null && t.includes("[*]") && e) {
      i = i.concat(this.ruleMap[t.replace("[*]", `[${require}]`)]);
      i = Array.from(new Set(i)).filter(Boolean);
    }
    return i;
  }
}, {
  key: "checkRules",
  value: function (t, e) {
    this.initCheckFunc(t || this.rootJson, this.funcs);
    this.hasInitFuncs = true;
    return r.default.validateAllForMessage(t || this.rootJson, g(e || this.rules), this.funcs);
  }
}, {
  key: "getRuleDeps",
  value: function (t) {
    var e = this;
    var a = (t.messageRules || t.ruleData || "").split("|$|").reduce(function (t, a) {
      return t.concat(e.getFormulaDeps(a));
    }, []).filter(function (t) {
      return t.includes("ywbw") || t.includes("localData");
    });
    var i = (t.messageRules || t.ruleData || "").match(/\[\d+\]/);
    if (i) {
      return a.map(function (t) {
        return t.replace(/\[\*\]/g, i[0]);
      });
    } else {
      return a.reduce(function (t, a) {
        if (a.includes("[*]") && !a.includes("(")) {
          var r = a.split("[*]")[0];
          for (var n = e.callCustomFunction(e.rootJson, r)?.length || 0, l = 0; l < n; l++) {
            t.push(a.replace(/\[\*\]/g, `[${l}]`));
          }
        } else {
          t.push(a);
        }
        return t;
      }, []);
    }
  }
}, {
  key: "getDynamicKeyMap",
  value: function (t) {
    var e = this.nonDynamic || {};
    var a = t || this.rootJson || {};
    var i = {};
    var r = {};
    var n = {};
    var l = {};
    Object.keys(e).forEach(function (t) {
      var e = /(?<=getObjFromList\()([^,]+)/gi;
      if (e.test(t)) {
        var o = t.match(e);
        var d = t.match(/(?<=getObjFromList\([^,]+,\\*['|"])([^\\\'\"]+)/gi);
        var s = t.match(/(?<=getObjFromList\([^,]+,[^,]+,\\*['|"])([^\\\'\"]+)/gi);
        var c = t.match(/(?<=getObjFromList\([^\)]+\)\.)([^\.\)]+)/gi);
        if (!o || !d || !s || !c) {
          return;
        }
        var u = o[0];
        var p = d[0];
        var v = s[0];
        var b = c[0];
        if (n[u] === undefined) {
          var f = {};
          var g = l[u];
          if (g === undefined) {
            g = new Function(`with(this){ return ${u} }`).call(a);
            l[u] = g;
          }
          (g || []).forEach(function (t, e) {
            f[t[p]] = e;
          });
          n[u] = f;
        }
        var h = n[u][v];
        var y = `${u}[${h}].${b}`;
        var m = `${u}[*].${b}`;
        i[y] = t;
        r[t] = [y, m];
      }
    });
    return {
      indexKeyMap: i,
      depsKeyMap: r
    };
  }
}, {
  key: "getItemsByNonDynamic",
  value: function (t, e) {
    var a = this.getDynamicKeyMap(e).indexKeyMap;
    var i = [];
    t.forEach(function (t) {
      var e = a[t] || t;
      i.push(e);
    });
    return i;
  }
}, {
  key: "checkRulesWithDeps",
  value: function (t, e) {
    var a = this;
    this.initCheckFunc(t || this.rootJson, this.funcs);
    this.hasInitFuncs = true;
    return r.default.validateAllForMessageAndDeps(t || this.rootJson, g(e || this.rules), this.funcs).map(function (e) {
      var i = a.getRuleDeps(e);
      return {
        type: e.type,
        content: e.content,
        preTables: e.preTables,
        ruleData: e.messageRules,
        items: a.getItemsByNonDynamic(i, t || a.rootJson)
      };
    });
  }
}, {
  key: "checkRulesWithDepsByDep",
  value: function (t, e, a) {
    var i = this;
    this.initCheckFunc(t || this.rootJson, this.funcs);
    var n = {};
    var l = [];
    var o = [];
    if (e) {
      this.runtimeValidRules.forEach(function (t) {
        if (!l.includes(t.ruleXh || t.ruleData)) {
          l.push(t.ruleXh || t.ruleData);
          o.push(t);
        }
      });
    } else {
      this.validateResult = {};
      o = this.rules;
    }
    if (a) {
      o = o.filter(function (t) {
        return t.ruleData.match(/sumList\s*\([^)]+\)/g);
      });
    }
    r.default.customByRules(t, o).forEach(function (e) {
      var a = i.getRuleDeps(e);
      var r = s(s({}, e), {}, {
        items: i.getItemsByNonDynamic(a, t || i.rootJson)
      });
      var l = e.ruleXh || e.ruleData;
      n[l] ||= [];
      var o = n[l].findIndex(function (t) {
        return t.messageRules === e.messageRules;
      });
      if (o > -1) {
        n[l].splice(o, 1, r);
      } else {
        n[l].push(r);
      }
      i.validateResult[l] = n[l];
    });
    return n;
  }
}, {
  key: "getFormatMessageByPreTable",
  value: function (t) {
    return Object.values(this.validateResult).reduce(function (e, a) {
      a.forEach(function (a) {
        if (a.preTables.includes(t) && a.passed === "unpass" && !e.find(function (t) {
          return t.ruleXh === a.ruleXh && t.content === (a.ruleMessage || a.content);
        })) {
          e.push({
            ruleXh: a.ruleXh,
            type: a.type,
            content: a.ruleMessage || a.content,
            preTables: a.preTables,
            ruleData: a.messageRules || a.ruleData || "",
            messagePriority: a.messagePriority,
            items: a.items
          });
        }
      });
      return e;
    }, []);
  }
}]);