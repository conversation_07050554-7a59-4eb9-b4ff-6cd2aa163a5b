<template>
    <el-input :type="inputType" v-model="inputValue" style="width: 238px" :placeholder="props.placeholder" :class="props.class"></el-input>
    <input type="password" autocomplete="new-password" style="display: none;" />
    <img :src="showPwdIcon ? hidePwdPng : showPwdPng" class="pwd-img" attr_show="0" @click="showPwd" />
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import hidePwdPng from "@/assets/Settings/hide-pwd.png";
import showPwdPng from "@/assets/Settings/show-pwd.png";
const showPwdIcon = ref(true);
const inputType = ref("password");
const props = defineProps({
    inputValue: {
        type: String,
        default: "",
    },
    placeholder: {
        type: String,
        default: "",
    },
    class: {},
});

const emits = defineEmits(["update:inputValue"]);

const inputValue = computed({
    get() {
        return props.inputValue;
    },
    set(value) {
        emits("update:inputValue", value);
    },
});

function showPwd() {
    showPwdIcon.value = inputType.value == "password" ? false : true;
    inputType.value = inputType.value == "password" ? "text" : "password";
}
</script>

<style scoped lang="less">
.pwd-img {
    width: 14px;
    height: 14px;
    position: relative;
    right: 24px;
    top: 4px;
}
</style>