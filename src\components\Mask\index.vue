<template>
  <div class="mask">
    <div class="box">
      <img
        class="is-loading"
        src="@/assets/Icons/loading.png"
        alt="" />
      <div class="prompt">
        {{ message }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  const props = withDefaults(defineProps<{ message: string }>(), { message: "默认内容" })
</script>
<style scoped lang="scss">
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .mask {
    z-index: 999;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.3);

    .box {
      display: flex;
      padding: 14px 25px;
      max-width: 220px;
      background-color: var(--white);
      opacity: 1;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);

      img {
        margin-top: 5px;
        width: var(--h3);
        height: var(--h3);
        animation: rotate 2s linear infinite;
      }

      .prompt {
        margin-left: 6px;
        font-size: var(--h3);
      }
    }
  }
</style>
