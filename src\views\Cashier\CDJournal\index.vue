<template>
    <div class="content">
        <div class="title">账户日记账</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <Select
                        style="width: 180px;"
                        v-model="searchInfo.account" 
                        :teleported="false" 
                        class="mr-20 cd-accountList" 
                        :filterable="true"
                        :filter-method="accountFilterMethod"
                    >
                        <ElOption 
                            v-for="item in showAccountList" 
                            :key="item.code" 
                            :value="item.code" 
                            :label="item.text"
                        >
                        </ElOption>
                        <div class="switch">
                            <el-switch 
                                v-model="showDisabledAccount" 
                                :active-value="true" 
                                :inactive-value="false" 
                                active-text="显示禁用账户"
                                @change="changeSwitchStatus"
                            ></el-switch>
                        </div>
                    </Select>
                    <DatePicker
                        :clearable="true"
                        v-model:startPid="searchInfo.startPid"
                        v-model:endPid="searchInfo.endPid"
                        :disabled-date-start="disabledDateStart"
                        :disabled-date-end="disabledDateEnd"
                    />
                    <a class="button solid-button ml-10" @click="handleSearchClick">查询</a>
                </div>
                <div class="main-tool-right">
                    <el-checkbox class="mr-10" v-model="showAll" label="显示所有信息" />
                    <Dropdown
                        btnTxt="打印列表"
                        class="print"
                        :width="isErp ? 96 : 84"
                        :downlistWidth="isErp ? 96 : 84">
                        <li @click="handlePrint(0,getPrintOrExportParams())">直接打印</li>
                        <li @click="printListSettings">打印设置</li>
                    </Dropdown>
                    <a class="button ml-10" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :loading="loading"
                    :use-normal-scroll="true"
                    :columns="columns"
                    :data="tableData"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :scrollbarShow="true"
                    :tableName="setModule"
                >
                    <template #income>
                        <el-table-column 
                            label="收入（借方）" 
                            :min-width="165" 
                            align="right" 
                            header-align="right"
                            prop="income"
                            :width="getColumnWidth(setModule, 'income')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.income) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column 
                            label="支出（贷方）" 
                            :min-width="165" 
                            align="right" 
                            header-align="right"
                            prop="expenditure"
                            :width="getColumnWidth(setModule, 'expenditure')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.expenditure) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #amount>
                        <el-table-column 
                            label="余额" 
                            :min-width="160" 
                            align="right" 
                            header-align="right"
                            prop="amount"
                            :width="getColumnWidth(setModule, 'amount')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.amount) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #lineSn>
                        <el-table-column label="日记账序号" min-width="155px" align="left" header-align="left" :resizable="false">
                            <template #default="scope">
                                <span v-if="!scope.row.line_sn_name.startsWith('<a')">{{ scope.row.line_sn_name }}</span>
                                <span class="link" @click="goToDetail(scope.row.line_sn_name,scope.row.created_date)" v-else>
                                    {{ getText(scope.row.line_sn_name) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <JouralPrint
    v-model:printDialogShow="printDialogVisible"
    title="日记账打印"
    :printData="printInfo"
    :dir-disabled="showAll"
    :otherOptions="otherOptions"
    @currentPrint="handlePrint(3,getPrintOrExportParams())" />
</template>

<script lang="ts">
export default {
    name: "CDJournal",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, onActivated, onUnmounted, watchEffect } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { request } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { setColumns, GetNumber } from "./utils";
import { ElNotify } from "@/util/notify";
import { getATagParams, getText } from "@/views/Cashier/Report/utils";
import { getUrlSearchParams, globalExport, globalWindowOpenPage } from "@/util/url";
import { dayjs } from "element-plus";
import { useAccountSetStore } from "@/store/modules/accountSet";
import usePrint from "@/hooks/usePrint";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableData } from "./types";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import Select from "@/components/Select/index.vue";
import ElOption from "@/components/Option/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import JouralPrint from "@/components/PrintDialog/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { commonFilterMethod } from "@/components/Select/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useCDAccountStore } from "@/store/modules/cdAccountList";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";

const cdAccountStore = useCDAccountStore();
const setModule = "CDJournal";
const _ = getGlobalLodash()
const isErp = ref(window.isErp)
interface IAccount {
    code: string;
    text: string;
}
const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const showAll = ref(false);
const loading = ref(false);
const accountList = ref<IAccount[]>([]);
const accountListAll = ref<IBankAccountItem[]>([]);
const tableData = ref<ITableData[]>([]);
const columns = ref<IColumnProps[]>([]);
const searchInfo = reactive({
    startPid: "",
    endPid: "",
    account: "",
});
watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});

const showDisabledAccount = ref(false);
function changeSwitchStatus() {
    const list: Array<IBankAccountItem> = showDisabledAccount.value 
        ? accountListAll.value 
        : accountListAll.value.filter((item) => item.state === "0");
    accountList.value = list.map((item) => {
        const suffix = item.state === "1" ? "（已禁用）" : "";
        return {
            code: item.ac_id,
            text: `${item.ac_no}-${item.ac_name}${suffix}`
        }
    });
}
const handleInit = () => {
    const { Date_s, Date_e, AC_ID, showDisabled } = route.query;
    if (Date_s) searchInfo.startPid = Date_s as string;
    if (Date_e) searchInfo.endPid = Date_e as string;
    if (showDisabled) showDisabledAccount.value = showDisabled === "true";
    if (AC_ID) searchInfo.account = AC_ID as string;
    accountListAll.value = [...cdAccountStore.cdAccountList];
    changeSwitchStatus();
    handleSearch();
};

const handleSearchClick = () => {
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
    } else {
        handleSearch();
    }
};

const handleSearch = () => {
    if (searchInfo.startPid === "" || searchInfo.endPid === "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    loading.value = true;
    request({ url: "/api/Journal/PagingListForCD?" + getUrlSearchParams(getParams()) })
        .then((res: any) => {
            columns.value = setColumns(showAll.value, true, false, setModule);
            if (res.state != 1000) return;
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
        })
        .finally(() => {
            loading.value = false;
            hasInit = true;
        });
};

const judgeCanPrintOrExport = () => {
    if (tableData.value.length === 0 || tableData.value.length === 1) {
        ElNotify({ type: "warning", message: "报表数据为空" });
        return false;
    }
    if (searchInfo.startPid == "" || searchInfo.endPid == "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    return true;
};

const {
  printDialogVisible,
  printInfo,
  otherOptions,
  updataPritnInfo,
  handlePrint
} = usePrint("joural", "/api/Journal/PrintCDTable", {}, true, true ,judgeCanPrintOrExport);
const printListSettings = () => {
    printDialogVisible.value = true;
};
onActivated(() => {
    printInfo.value = updataPritnInfo()
});

const handleExport = () => {
    if (!judgeCanPrintOrExport()) return;
    const params: any = getPrintOrExportParams();
    const showAll = params.showAll;
    delete params.showAll;
    globalExport("/api/Journal/ExportCDTable?showAll=" + showAll + "&" + getUrlSearchParams(params));
};
const goToDetail = (lineSnName: string,created_date:string) => {
    const path = "/Cashier/CDJournal";
    const params = { ...getATagParams(lineSnName) };
    params.from = path;
    params.CREATED_DATE = created_date;
    globalWindowOpenPage("/Cashier/JournalPage?" + getUrlSearchParams(params), params.JOURNAL_TYPE === "INCOME" ? "收款凭据" : "付款凭据");
};

watch(showAll, () => {
    //勾选显示所有，默认禁用，横向
    if(showAll.value) printInfo.value.direction = 1
    handleSearch();
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    if (!hasInit) return;
    handleSearch();
});

const getParams = () => {
    return {
        LimitScm: "",
        cd_account: searchInfo.account,
        date_s: searchInfo.startPid,
        date_e: searchInfo.endPid,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
};
const getPrintOrExportParams = () => {
    return {
        showAll: showAll.value ? 1 : 0,
        cd_account: searchInfo.account,
        date_s: searchInfo.startPid,
        date_e: searchInfo.endPid,
    };
};


const accountSetStore = useAccountSetStore();
function disabledDateStart(time: Date) {
    const endDate = dayjs(searchInfo.endPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    return time.getTime() > endDate || time.getTime() < asStartDate;
}
function disabledDateEnd(time: Date) {
    const startDate = dayjs(searchInfo.startPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    const minTime = Math.max(startDate, asStartDate);
    return time.getTime() < minTime;
}

const routeQueryParams = ref<any>(null);

let hasInit = false;
onActivated(() => {
    const thisRouteQueryParams = route.query;
    const isEqualRouterParams = _.isEqual(thisRouteQueryParams, routeQueryParams.value);
    if (!isEqualRouterParams) {
        hasInit = false;
        paginationData.currentPage = 1;
        handleInit();
    }
    routeQueryParams.value = thisRouteQueryParams;
});

onBeforeRouteLeave((to, from, next) => {
    routeQueryParams.value = from.query;
    next();
});

onUnmounted(() => {
    routeQueryParams.value = null;
});

const showAccountList = ref<Array<IAccount>>([]);
watchEffect(() => { 
    showAccountList.value = JSON.parse(JSON.stringify(accountList.value));  
});
function accountFilterMethod(value: string) {
    showAccountList.value = commonFilterMethod(value, accountList.value, 'text');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";
.main-content {
    .main-top {
        padding-left: 20px;
        padding-right: 20px;
        .main-tool-left {
            .detail-el-select(180px, 30px);
        }
    }
    .main-center {
        padding: 20px;
        padding-top: 0;
        :deep(.el-table) {
            .el-scrollbar__bar.is-horizontal {
                display: block !important;
            }
        }
    }
}
:deep(.el-select) {
    &.cd-accountList {
        .el-select-dropdown__wrap {
            margin-bottom: 30px;
        }
    }
}
.switch {
    border-top: 1px solid var(--border-color);
    padding-left: 5px;
    width: 100%;
    line-height: 30px;
    position: absolute;
    bottom: 1px;
    text-align: left;
    background-color: var(--white);
    .el-switch {
        margin-right: 2px;
    }
}
</style>
