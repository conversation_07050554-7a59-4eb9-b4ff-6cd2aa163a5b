<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">资产管理</div>
                    <ElTabs v-model="activeName" @tab-click="handleTabClick" :class="isErp ? 'erp-content' : ''">
                        <ElTabPane label="资产初始化" name="initFixedassets" v-if="checkPermission(['fixedassets-init-canview'])">
                            <template #label>
                                <component :is="renderTabContent('资产初始化',activeName === 'initFixedassets',commissioningDate)" />
                            </template>
                            <InitFixedassets
                                :pageInfo="'fixAsset'"
                                ref="initFixedassetsRef"
                                :commissioningPid="pid"
                                :commissioningDate="commissioningDate"
                                :currentPid="currentPid"
                                :currentPeriodInfo="currentPeriodInfo"
                                :iaPeriodInfo="iaPeriodInfo"
                                :iaPid="iaPid"
                                :status="status"
                                :finishflag="finishflag"
                                :initialData="initialData"
                                @CheckIfFinish="CheckIfFinishInit"
                                @handleEdit="handleEdit"
                                @increaseAssets="increaseAssets"
                                @showDetail="showAssetsDetail"
                                @handleImport="handleImport"
                                @getChangeData="getChangeData"
                                @startIntangibleAsset="startIntangibleAsset"
                                @toIntangibles="toIntangibles"
                                @getPeriodsApi="getPeriodsApi"
                            ></InitFixedassets>
                        </ElTabPane>
                        <ElTabPane label="资产列表" name="fixedassetsList" v-if="checkPermission(['fixedassets-card-canview'])">
                            <template #label >
                                <component :is="renderTabContent('资产列表',activeName === 'fixedassetsList',commissioningDate)" />
                            </template>
                            <FixedassetsList
                                ref="fixedassetsListRef"
                                :pid="currentPid"
                                :period="periodList"
                                :month="currentMonth"
                                @increaseAssets="increaseAssets"
                                @handleImport="handleImport"
                                @handleChange="handleChange"
                                @handleEdit="handleEdit"
                                @handleExamine="handleExamine"
                                @changeHistory="changeHistory"
                                @changeStatus="changeStatus"
                                @goToVoucher="goToVoucher"
                                @showDetail="showAssetsDetail"
                                @getChangeData="getChangeData"
                            ></FixedassetsList>
                        </ElTabPane>
                        <ElTabPane label="变更记录及生成凭证" name="changeRecord" v-if="checkPermission(['fixedassets-card-canview'])">
                            <ChangeRecord
                                ref="changeRecordRef"
                                :pid="currentPid"
                                :period="periodList"
                                :month="currentMonth"
                                @handleVoucher="handleVoucher"
                                @goToVoucher="goToVoucher"
                                @getAssetsList="fixedassetsListRef?.handleSearch()"
                            ></ChangeRecord>
                        </ElTabPane>
                        <ElTabPane label="计提折旧及生成凭证" name="depreciation" v-if="checkPermission(['fixedassets-card-canview'])">
                            <template #label>
                                <component :is="renderDepreciationTooltip('计提折旧及生成凭证',activeName === 'depreciation')" />
                            </template>
                            <Depreciation
                                ref="depreciationRef"
                                :finishflag="finishflag"
                                @handleVoucher="handleVoucher"
                                @handleActiveName="handleActiveName"
                                @generateVoucher="generateVoucher"
                                @reloadFixedAssetsList="reloadFixedAssetsList"
                            ></Depreciation>
                        </ElTabPane>
                    </ElTabs>
                </div>
            </template>
            <template #intangibles>
                <div class="main-content">
                    <InitFixedassets
                        ref="intangibleAssetsRef"
                        :pageInfo="'intangibles'"
                        :commissioningPid="pid"
                        :commissioningDate="commissioningDate"
                        :currentPid="currentPid"
                        :currentPeriodInfo="currentPeriodInfo"
                        :iaPeriodInfo="iaPeriodInfo"
                        :iaPid="iaPid"
                        :status="status"
                        :finishflag="finishflag"
                        :initialData="initialData"
                        @CheckIfFinish="CheckIfFinishInit"
                        @handleEdit="handleEdit"
                        @increaseAssets="increaseAssets"
                        @showDetail="showAssetsDetail"
                        @handleImport="handleImport"
                        @getChangeData="getChangeData"
                        @startIntangibleAsset="startIntangibleAsset"
                        @changeCancle="changeCancle"
                    ></InitFixedassets>
                </div>
            </template>
            <template #add>
                <div>
                    <div class="slot-title">新增资产</div>
                    <AddFixedAssets
                        ref="AddFARef"
                        :activeName="activeName"
                        :initType="initType"
                        :faid="editFaId"
                        :dateInfo="initType === `intangibles` ? iaPeriodInfo : dateInfo"
                        :pid="assetsListPid"
                        :iaPid="iaPid"
                        @divCancel="divCancel"
                        @goDepreciationTab="goDepreciationTab"
                    ></AddFixedAssets>
                </div>
            </template>
            <template #edit>
                <div>
                    <div class="slot-title">编辑资产</div>
                    <EditFixedAssets
                        ref="EditFARef"
                        :editData="editData"
                        :activeName="activeName"
                        :faid="editFaId"
                        :pid="editPid"
                        :max-date="maxDate"
                        :periodList="periodList"
                        :samePid="pid == iaPid"
                        @divCancel="divCancel"
                        @goDepreciationTab="goDepreciationTab"
                    ></EditFixedAssets>
                </div>
            </template>
            <template #import>
                <div class="slot-content align-center">
                    <div class="slot-mini-content" style="width: 1000px">
                        <div class="title">批量导入资产</div>
                        <div class="main-content box-center">
                            <div class="txt first-item">
                                <a
                                    class="link highlight-red"
                                    v-show="!isErp && !isHideBarcode"
                                    @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=130270170')"
                                    >不会操作？点此观看视频</a
                                >
                            </div>
                            <div class="txt mt-20">第一步： 请点击下面的链接下载Excel模板，并按照模板填写信息。</div>
                            <div class="txt mt-10">
                                <a class="link" id="excelmd" @click="downloadfile">下载模板</a>
                            </div>
                            <div class="txt mt-20">第二步： 导入Excel模板文件。</div>
                            <div class="txt mt-10">
                                <label class="file-button" style="display: flex">
                                    <input type="file" accept=".xls,.xlsx" @change="uploadFile" ref="selectedFileRef" /><a
                                        class="link"
                                        style="width: 70px"
                                        >选取文件</a
                                    ><span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 675px">{{
                                        fileName
                                    }}</span>
                                </label>
                                <span id="wait_loading" class="ml-20" style="display: none">请稍等...</span>
                            </div>
                            <div class="buttons">
                                <a class="button solid-button" @click="SubmitDR">导入</a
                                ><a class="button ml-10" @click="onImportCancel">取消</a>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #valueChange>
                <div class="slot-content align-center">
                    <div class="slot-title">原值调整</div>
                    <ValueChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :pid="changePid"
                        :key="currentSlot"
                        @changeCancle="changeCancle"
                        @goToVoucher="goToVoucher"
                        @getFixedAssetsList="fixedassetsListRef?.getTableData()"
                    ></ValueChange>
                </div>
            </template>
            <template #accumulatedChange>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ changeData?.fa_property === 0 ? "累计折旧" : "累计摊销" }}调整</div>
                    <AccumulatedChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :departmentShow="departmentShow"
                        :pid="changePid"
                        :key="currentSlot"
                        @changeCancle="changeCancle"
                        @goToVoucher="goToVoucher"
                        @getAssetsList="fixedassetsListRef?.handleSearch()"
                    >
                    </AccumulatedChange>
                </div>
            </template>
            <template #provisionChange>
                <div class="slot-content align-center">
                    <div class="slot-title">计提减值准备</div>
                    <ProvisionChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :departmentShow="departmentShow"
                        :pid="changePid"
                        :key="new Date().getTime()"
                        @changeCancle="changeCancle"
                        @goToVoucher="goToVoucher"
                    >
                    </ProvisionChange>
                </div>
            </template>
            <template #usefullifeChange>
                <div class="slot-content align-center">
                    <div class="slot-title">使用年限调整</div>
                    <UsefullifeChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :departmentShow="departmentShow"
                        :pid="changePid"
                        :key="new Date().getTime()"
                        @changeCancle="changeCancle"
                        @saveData="handleChangeSave"
                    >
                    </UsefullifeChange>
                </div>
            </template>
            <template #asubChange>
                <div class="slot-content align-center">
                    <div class="slot-title">科目调整</div>
                    <AsubChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :pid="changePid"
                        @changeCancle="changeCancle"
                        @saveData="handleChangeSave"
                    ></AsubChange>
                </div>
            </template>
            <template #departmentChange>
                <div class="slot-content align-center" style="overflow-y: auto;">
                    <div class="slot-title">部门调整</div>
                    <div>
                        <DepartmentChange
                            :changeData="changeData"
                            :changeTitle="changeTitle"
                            :departmentShow="departmentShow"
                            :pid="changePid"
                            :key="new Date().getTime()"
                            @changeCancle="changeCancle"
                            @saveData="handleChangeSave"
                        >
                        </DepartmentChange>
                    </div>
                </div>
            </template>
            <template #depreciationTypeChange>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ changeData?.fa_property === 0 ? "折旧方法" : "摊销方法" }}调整</div>
                    <DepreciationTypeChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :departmentShow="departmentShow"
                        :pid="changePid"
                        @changeCancle="changeCancle"
                        @saveData="handleChangeSave"
                    ></DepreciationTypeChange>
                </div>
            </template>
            <template #statusChange>
                <div class="slot-content align-center">
                    <div class="slot-title">状态调整</div>
                    <StatusChange
                        :changeData="changeData"
                        :changeTitle="changeTitle"
                        :departmentShow="departmentShow"
                        :pid="changePid"
                        :key="new Date().getTime()"
                        @changeCancle="changeCancle"
                        @saveData="handleChangeSave"
                    >
                    </StatusChange>
                </div>
            </template>
            <template #examine>
                <div style="height: 100%">
                    <div class="title">资产明细</div>
                    <div class="main-content">
                        <div class="main-top main-tool-bar space-between">
                            <div class="main-tool-left"></div>
                            <div class="main-tool-right">
                                <a class="button mr-10" @click="examineBack">返回</a>
                                <a class="button mr-10" @click="examineExport" v-permission="['fixedassets-card-canexport']">导出</a>
                                <a class="button mr-10" @click="examinePrint" v-permission="['fixedassets-card-canprint']">打印</a>
                                <RefreshButton :isFullRefresh="false" @refresh="getExamineData"></RefreshButton>
                            </div>
                        </div>
                        <div class="main-center">
                            <Table
                                :data="examineData"
                                :columns="examineColumns"
                                :pageIsShow="true"
                                :page-sizes="examinePagination.pageSizes"
                                :page-size="examinePagination.pageSize"
                                :total="examinePagination.total"
                                :current-page="examinePagination.currentPage"
                                :use-normal-scroll="true"
                                :loading="examineLoading"
                                :empty-text="emptyText.examineText"
                                @size-change="examineHandleSizeChange"
                                @current-change="examineHandleCurrentChange"
                                @refresh="examineRefresh"
                                :scrollbar-show="true"
                                :tableName="setModuleExamine"
                            >
                                <template #vgname>
                                    <ElTableColumn 
                                        label="凭证号" 
                                        align="left" 
                                        headerAlign="left" 
                                        prop="vgname" 
                                        :width="getColumnWidth(setModuleExamine, 'vgname')"
                                    >
                                        <template #default="scope">
                                            <a class="link" @click="ShowDetail(scope.row, 'examine')" v-show="scope.row.vg_name !== ''">
                                                {{ scope.row.vg_name }}
                                            </a>
                                        </template>
                                    </ElTableColumn>
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </template>
            <template #changeHistory>
                <div style="height: 100%;">
                    <div class="title">变更历史</div>
                    <div class="main-content">
                        <div class="main-top main-tool-bar space-between">
                            <div class="main-tool-left"></div>
                            <div class="main-tool-right">
                                <a class="button mr-10" @click="examineBack">返回</a>
                                <RefreshButton :isFullRefresh="false" @refresh="getChangeHistoryData"></RefreshButton>
                            </div>
                        </div>
                        <div class="main-center">
                            <Table
                                :data="changeHistoryData"
                                :columns="changeHistoryColumns"
                                :pageIsShow="true"
                                :page-sizes="changeHistoryPagination.pageSizes"
                                :page-size="changeHistoryPagination.pageSize"
                                :total="changeHistoryPagination.total"
                                :current-page="changeHistoryPagination.currentPage"
                                :use-normal-scroll="true"
                                :empty-text="emptyText.changeHistoryText"
                                :loading="changeHistoryLoading"
                                @size-change="changeHistoryHandleSizeChange"
                                @current-change="changeHistoryCurrentChange"
                                @refresh="changeHistoryRefresh"
                                :scrollbar-show="true"
                                :tableName="setModuleChangeHistory"
                            >
                                <template #vgname>
                                    <ElTableColumn 
                                        label="关联凭证" 
                                        align="left" 
                                        headerAlign="left"
                                        prop="vgname" 
                                        :width="getColumnWidth(setModuleChangeHistory, 'vgname')"
                                    >
                                        <template #default="scope">
                                                <a
                                                    :class="checkPermission(['fixedassets-card-cancreatevoucher']) ? 'link' : ''"
                                                    @click="
                                                        checkPermission(['fixedassets-card-cancreatevoucher'])
                                                            ? ShowDetail(scope.row, 'changeHistory')
                                                            : ''
                                                    "
                                                    v-show="scope.row.vg_name !== ''"
                                                >
                                                    {{ formatVoucherTxt(scope.row.v_date_text, scope.row.vg_name) }}
                                                </a>
                                        </template>
                                    </ElTableColumn>
                                </template>
                            </Table>
                        </div>
                    </div>
                </div>
            </template>
            <template #detail>
                <FixedAssetsDetail
                    v-if="detailData"
                    :detailData="detailData"
                    @div-cancel="divCancel"
                    :key="new Date().getTime()"
                ></FixedAssetsDetail>
            </template>
            <template #voucher>
                <div class="content">
                    <div class="voucher-edit-view slot-content">
                        <div class="title">记账凭证</div>
                        <div
                            class="main-content"
                            :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in', 'self-content': !isErp }"
                        >
                            <div class="voucher-content-bottom">
                                <VoucherView
                                    v-model:query-params="voucherQueryParams"
                                    @voucher-changed="voucherChanged"
                                    @load-success="voucherLoadSuccess(voucherQueryParams)"
                                    @zoom="zoomCallback"
                                    ref="voucherView"
                                    :hiddenLine="true"
                                    :showCancel="true"
                                    cancelTxt="取消"
                                    @save="editVoucherSave"
                                    @delete-voucher="deleteVoucher"
                                    @back="goBackPage"
                                    :edited="isEditting"
                                    :module-permission="'fixedassets-card-cancreatevoucher'"
                                >
                                </VoucherView>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #changeVoucher>
                <div class="content">
                    <div class="voucher-edit-view slot-content">
                        <div class="title">{{ voucherTitle }}</div>
                        <div
                            class="main-content"
                            :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in', 'self-content': !isErp }"
                        >
                            <div class="voucher-content-bottom">
                                <VoucherView
                                    ref="changeVoucher"
                                    v-model:query-params="changeVoucherQueryParams"
                                    :isInputAccout="isInputAccout"
                                    @load-success="voucherLoadSuccess(changeVoucherQueryParams)"
                                    @zoom="zoomCallback"
                                    @voucher-changed="voucherChanged"
                                    :get-disabled-date="getVDateDisabledState"
                                    :hiddenLine="true"
                                    :edited="isEditting"
                                    :showCancel="true"
                                    cancelTxt="取消"
                                    @save="saveChangeVoucher"
                                    @back="cancelMyVouchers"
                                    :module-permission="'fixedassets-card-cancreatevoucher'"
                                ></VoucherView>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #depreVoucher>
                <div class="content">
                    <div class="voucher-edit-view slot-content">
                        <div class="title">记账凭证</div>
                        <div
                            class="main-content"
                            :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in', 'self-content': !isErp }"
                        >
                            <div class="voucher-content-bottom">
                                <VoucherView
                                    v-model:query-params="depreVoucherQueryParams"
                                    @zoom="zoomCallback"
                                    @voucher-changed="voucherChanged"
                                    ref="deprevoucherView"
                                    @load-success="voucherLoadSuccess(depreVoucherQueryParams)"
                                    :hiddenLine="true"
                                    :edited="isEditting"
                                    :showCancel="true"
                                    cancelTxt="取消"
                                    @save="saveDepreciationVoucher"
                                    @back="depreVoucherBack"
                                    @delete-voucher="deletDepreVoucher"
                                    :module-permission="'fixedassets-card-cancreatevoucher'"
                                ></VoucherView>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
    <el-dialog class="functionUpdate no-dialog-header dialogDrag" v-model="updateTipShow" width="540" center :show-close="false">
        <div class="function-content" :class="isErp ? 'erpfunction-content' : ''" v-dialogDrag>
            <div class="function-title">功能更新提醒</div>
            <div class="function-tips">
                资产模块已更新<span style="color: red">无形资产</span>和<span style="color: red">长期待摊费用</span
                >功能，是否需要启用无形资产和长期待摊费用自动摊销？
            </div>
            <div class="function-message">可在资产列表-资产初始化中手动启用</div>
            <div class="function-buttons" style="height: 62px; padding-top: 48px">
                <el-button
                    :round="true"
                    style="background-color: #fafafa; color: #cccccc; height: 41px; width: 160px; font-size: 21px"
                    @click="() => (updateTipShow = false)"
                    >下次再说</el-button
                >
                <el-button
                    :round="true"
                    style="background-color: var(--main-color); color: #ffffff; height: 41px; width: 160px; font-size: 21px"
                    @click="startIntangibleAsset"
                    >去启用</el-button
                >
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="isStartIntangibles" title="启用无形资产和长期待摊费用" center width="540" class="dialogDrag">
        <div class="start-dialog" v-dialogDrag>
            <div class="start-content">
                <div class="start-date">
                    <span>启用年月：</span>
                    <el-select v-model="iaDate.year" style="width: 100px" @change="changeIaYear">
                        <el-option v-for="item in iaYearList" :value="item" :label="item" :key="item"></el-option>
                    </el-select>
                    年
                    <el-select v-model="iaDate.month" style="width: 100px">
                        <el-option v-for="item in iaMonthList[iaDate.year + '']" :value="item" :label="item" :key="item"></el-option>
                    </el-select>
                    月
                </div>
                <div class="start-tips">
                    <div>1.仅允许在原固定资产未折旧期间启用</div>
                    <div>2.启用后将可以通过资产模块自动进行无形资产和长期待摊费用摊销</div>
                    <div v-show="!isErp">3.后续取消启用需要到设置-账套-编辑-功能参数-资产模块和资产管理一起取消</div>
                </div>
                <div class="start-confirm-tip aa-hidden">
                    <img src="@/assets/Icons/warn.png" />
                    <div><span>{{ accountSetBackupTips() }}</span></div>
                </div>
            </div>
            <div class="buttons start-buttons">
                <a class="button" @click="isStartIntangibles = false">取消</a>
                <a class="button solid-button ml-10" @click="startIa">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "FixedAssets",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import Table from "@/components/Table/index.vue";
import InitFixedassets from "./components/Initfixedassets.vue";
import FixedassetsList from "./components/FixedassetsList.vue";
import ChangeRecord from "./components/ChangeRecord.vue";
import Depreciation from "./components/Depreciation.vue";
import ValueChange from "./components/ValueChange.vue";
import AccumulatedChange from "./components/AccumulatedChange.vue";
import ProvisionChange from "./components/ProvisionChange.vue";
import UsefullifeChange from "./components/UsefullifeChange.vue";
import AsubChange from "./components/AsubChange.vue";
import DepartmentChange from "./components/DepartmentChange.vue";
import DepreciationTypeChange from "./components/DepreciationTypeChange.vue";
import StatusChange from "./components/StatusChange.vue";
import EditFixedAssets from "./components/EditFixedAssets.vue";
import AddFixedAssets from "./components/AddFixedAssets.vue";
import VoucherView from "@/components/Voucher/index.vue";
import FixedAssetsDetail from "./components/FixedAssetsDetail.vue";
import type { ITableData, IPeriod, ICurrentPeriod, ICheckFinish, ICheckFinishItem2, IAssetDetail, ICommissioningDate, } from "./types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { usePagination } from "@/hooks/usePagination";
import { useLoading } from "@/hooks/useLoading";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { formatMoneyWithZero } from "@/util/format";
import { globalExport, globalPrint, globalWindowOpen, globalWindowOpenPage, reloadPeriodInfo, tryClearCustomUrlParams, downloadFile } from "@/util/url";
import { checkPermission } from "@/util/permission";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { ElNotify } from "@/util/notify";
import { ifCanUseVoucher, getYearMonthLastDay, useTooltip } from "./utils";
import { ref, onMounted, nextTick, watch, onUnmounted, computed } from "vue";
import { ElConfirm } from "@/util/confirm";
import {
    DataVoucherQueryParams,
    EditVoucherQueryParams,
    VoucherEntryModel,
    VoucherSaveModel,
    VoucherSaveParams,
} from "@/components/Voucher/types";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { getGlobalLodash } from "@/util/lodash";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { onActivated } from "vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { editConfirm } from "@/util/editConfirm";
import { formatVoucherTxt } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { dangerousOperationNext } from "@/util/autoBackup";
import { accountSetBackupTips } from "@/util/showTips";
import { handleExpiredCheckData, ExpiredCheckModuleEnum, handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const { renderTabContent, renderDepreciationTooltip } = useTooltip()
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const accountSetStore = useAccountSetStore();
const accountingStandard = accountSetStore.accountSet!.accountingStandard as number;
const route = useRoute();
const activeName = ref<string>("initFixedassets");
const _ = getGlobalLodash()
const slots = [
    "main",
    "add",
    "edit",
    "import",
    "examine",
    "changeHistory",
    "valueChange",
    "accumulatedChange",
    "provisionChange",
    "usefullifeChange",
    "asubChange",
    "departmentChange",
    "depreciationTypeChange",
    "statusChange",
    "voucher",
    "changeVoucher",
    "depreVoucher",
    "detail",
    "intangibles",
];
const currentSlot = ref("main");
const lastSlot = ref("main");
const editData = ref({});
const pid = ref<number>(-1);
const assetsListPid = ref<number>(-1);
const currentPid = ref<number>(-1);
const entryPeriod = ref<string>("");
const status = ref<number>(1);
const finishflag = ref<string>("");
const initialFlag = ref<string>("");
const detailData = ref();
const dateInfo = ref("");
const depreciationRef = ref<InstanceType<typeof Depreciation>>();
const changeVoucher = ref();
const deprevoucherView = ref();
const {
    paginationData: examinePagination,
    handleCurrentChange: examineHandleCurrentChange,
    handleSizeChange: examineHandleSizeChange,
    handleRerefresh: examineRefresh,
} = usePagination("examine", true);
const {
    paginationData: changeHistoryPagination,
    handleCurrentChange: changeHistoryCurrentChange,
    handleSizeChange: changeHistoryHandleSizeChange,
    handleRerefresh: changeHistoryRefresh,
} = usePagination("changeHistory", true);
const emptyText = ref({
    examineText: "数据加载中...",
    changeHistoryText: "数据加载中...",
});
const AddFARef = ref<InstanceType<typeof AddFixedAssets>>();

const fixedassetsListRef = ref<InstanceType<typeof FixedassetsList>>();
const initFixedassetsRef = ref<InstanceType<typeof InitFixedassets>>();
const intangibleAssetsRef = ref<InstanceType<typeof InitFixedassets>>();
const changeRecordRef = ref<InstanceType<typeof ChangeRecord>>();

function changeStatus(val: number) {
    status.value = val;
}

function toIntangibles() {
    currentSlot.value = "intangibles";
    intangibleAssetsRef.value?.getTableData();
}
const updateTipShow = ref(false);
const iaDate = ref({
    year: 0,
    month: 0,
});
const isStartIntangibles = ref(false);
const iaYearList = ref<number[]>([]);
const iaMonthList = ref<Record<string, number[]>>({});

function changeIaYear(val: number) {
    iaDate.value.month = iaMonthList.value[val + ""][0];
}

function startIntangibleAsset() {
    const regExp = /(\d+)年(\d+)月/;
    const matchResult: RegExpMatchArray | null = currentPeriodInfo.value.match(regExp);
    const year = parseInt(matchResult ? matchResult[1] : "");
    const month = parseInt(matchResult ? matchResult[2] : "");
    iaYearList.value = [];
    iaMonthList.value = {};
    for (let i = year; i < year + 6; i++) {
        if (month == 1 && i == year + 5) {
            break;
        }
        iaYearList.value.push(i);
        const months: number[] = [];
        if (i === year) {
            for (let j = month; j <= 12; j++) {
                months.push(j);
            }
        } else if (i === year + 5) {
            for (let j = 1; j < month; j++) {
                months.push(j);
            }
        } else {
            months.push(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);
        }
        iaMonthList.value[i.toString()] = months;
    }
    iaDate.value.year = iaYearList.value[0];
    iaDate.value.month = iaMonthList.value[year + ""][0];
    updateTipShow.value = false;
    isStartIntangibles.value = true;
}
let canTransfer = true;
async function startIa() {
    if (!canTransfer) return;
    canTransfer = false;
    const loadingText = "亲，正在启用无形资产，系统将自动备份账套，请不要进行其他操作。";
    const canNext = await dangerousOperationNext(loadingText);
    if (!canNext) {
        canTransfer = true;
        return;
    }
    enableIAStart();
}
function enableIAStart() {
    let pid = currentPid.value;
    for (let i = iaYearList.value[0]; i <= iaDate.value.year; i++) {
        iaMonthList.value[i + ""].some((cur) => {
            if (i === iaDate.value.year && cur === iaDate.value.month) {
                return true;
            }
            pid++;
            return false;
        });
    }
    useLoading().enterLoading("亲，正在启用无形资产，系统将自动备份账套，请不要进行其他操作。");
    request({
        url: "/api/FAPeriod/EnableIAStart?pid=" + pid,
        method: "post",
    })
        .then((res: IResponseModel<object>) => {
            canTransfer = true;
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "启用成功",
                });
                CheckIfFinishInit(true);
                iaPid.value = pid;
                iaPeriodInfo.value = iaDate.value.year + "年" + iaDate.value.month + "月";
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
            useLoading().quitLoading();
        })
        .catch(() => {
            canTransfer = true;
            useLoading().quitLoading();
            ElNotify({
                type: "warning",
                message: "出现错误，请刷新页面重试或联系客服",
            });
        });
}
function reloadFixedAssetsList() {
    initFixedassetsRef.value?.EnableAddAndDel();
    getCurrentPeriod();
    getPeriodApi();
    fixedassetsListRef?.value?.checkSettleAccount();
    isLoaded.value.fixedassetsList = false;
    isLoaded.value.changeRecord = false;
}

//获取启用日期
const commissioningDate = ref<string>("");
const iaPeriodInfo = ref<string>("");
const iaPid = ref<number>(-1);
function getPeriodsApi(keepCurrent: boolean = false) {
    request({
        url: `/api/FAPeriod/Start`,
        method: "get",
    }).then((res: IResponseModel<ICommissioningDate>) => {
        if (res.state == 1000) {
            commissioningDate.value = res.data.periodInfo;
            iaPeriodInfo.value = res.data.iaPeriodInfo;
            iaPid.value = res.data.iaPId;
            pid.value = res.data.pid;
            const updateShow = useAccountSetStoreHook().userInfo?.userSn + "-" + getGlobalToken() + "-visibleClick";
            if (!iaPid.value && !iaPeriodInfo.value && !localStorage.getItem(updateShow) && accountingStandard < 6) {
                updateTipShow.value = true;
                localStorage.setItem(updateShow, "1");
            }
            CheckIfFinishInit();
        }
        CheckIfFinish().then((res: IResponseModel<ICheckFinish>) => {
            if (res.subState === 0) {
                if (res.data.item1 && useAccountSetStoreHook().permissions.includes("fixedassets-card-canview")) {
                    if (!route.query.tabflag && !keepCurrent) {
                        isLoaded.value.fixedassetsList = true;
                        activeName.value = "fixedassetsList";
                        fixedassetsListRef.value?.handleSearch();
                    }
                    finishflag.value = "1";
                } else {
                    isLoaded.value.initFixedassets = true;
                    initFixedassetsRef.value?.getTableData();
                    finishflag.value = "0";
                }
            }
            //联查资产新开资产页面需要进行期初校验才能进行下一步操作避免onActivated第一次取不到finishflag
            if (isFromOtherPage(route.query.from as string) && firstInit) {
                reLoadCurrentPage();
            }
            checkInitialFixedAssetsSet();
        });
    });
}


// 获取资产当前期间信息
function getCurrentPeriod() {
    request({
        url: `/api/FAPeriod/Current`,
    }).then((res: IResponseModel<ICurrentPeriod>) => {
        if (res.state === 1000) {
            dateInfo.value = res.data.periodInfo;
        }
    });
}
const currentMonth = ref("");
const periodList = ref<IPeriod[]>([]);
const currentPeriodInfo = ref<string>("");
// 获取资产期间列表
function getPeriodApi() {
    request({
        url: `/api/FAPeriod/List?dpcFlag=false`,
        method: "get",
    }).then((res: IResponseModel<ICommissioningDate[]>) => {
        if (res.state == 1000) {
            periodList.value = [];  
            res.data.forEach((item: ICommissioningDate) => {  
                if (item.isDefault && !route.query.period) {  
                    currentPid.value = item.pid;  
                    currentPeriodInfo.value = item.periodInfo;  
                }  
                const year = item.periodInfo.slice(0, 4);  
                const sn = item.periodInfo.slice(5, item.periodInfo.indexOf("月") === 7 ? 7 : 6);  
                periodList.value.push({  
                    pid: item.pid,  
                    periodInfo: item.periodInfo,  
                    time: `${year}${sn.padStart(2, "0")}`,  
                });  
            });
        }
    });
}
watch(
    () => currentPid.value,
    () => {
        currentMonth.value = periodList.value.find((item) => item.pid === currentPid.value)?.time || "";
    }
);

// 初始化是否完成
function CheckIfFinish() {
    return request({
        url: `/api/FixedAssets/IsFinishInitialization?pid=` + pid.value,
        method: "post",
    });
}

// 期初不平时数据
const initialData = ref<ICheckFinishItem2[]>([]);
function CheckIfFinishInit(isClose?: boolean) {
    CheckIfFinish().then((res: IResponseModel<ICheckFinish>) => {
        isClose && (isStartIntangibles.value = false);
        if (res.data.item1) {
            finishflag.value = "1";
        } else {
            finishflag.value = "0";
            // 默认值
            initialData.value = res.data.item2.reduce((prev: ICheckFinishItem2[], cur: ICheckFinishItem2) => {
                for (let i = 0; i < 2; i++) {
                    prev = prev.concat(cur);
                }
                return prev;
            }, []);
        }
    });
}

function checkInitialFixedAssetsSet() {
    // 期末结转过来不弹窗
    if (route.query.from === "checkout") return;
    //判断资产是否初始化
    request({
        url: `/api/FixedAssets/ExistsInitialization?pid=` + pid.value,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.data) {
            initialFlag.value = "1";
        } else if (checkPermission(["fixedassets-card-canview"])) {
            ElConfirm(
                "请确定是否存在资产的期初数据 ？如有期初，请点击确定前往录入期初，否则请关闭~",
                true,
                () => {},
                "提示",
                undefined,
                false,
                9000,
                false,
                true,
                false,
                true
            ).then((r: boolean) => {
                if (r) {
                    if (!checkPermission(["initialbalance1-canview"])) {
                        ElConfirm("您没有权限查看该页面");
                    } else {
                        globalWindowOpenPage("/Settings/InitialBalance1", "期初");
                    }
                }
            });
            initialFlag.value = "0";
        }
    });
}

const formatDate = (date: string) => {
    return date
        .split(" ")[0]
        .split("/")
        .map((item, index) => {
            if (index !== 0) item = item.padStart(2, "0");
            return item;
        })
        .join("-");
};

const setModuleExamine = "FixedassetsExamine";
const examineData = ref<any[]>([]);
const examineLoading = ref<boolean>(false);
const examineColumns = ref<Array<IColumnProps>>([
    {
        label: "日期",
        prop: "created_date",
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => {
            return formatDate(value);
        },
        minWidth: 100,
        width: getColumnWidth(setModuleExamine, 'created_date'),
    },
    { slot: "vgname" },
    { 
        label: "摘要", 
        prop: "description", 
        align: "left", 
        headerAlign: "left", 
        minWidth: 180, 
        width: getColumnWidth(setModuleExamine, 'description') 
    },
    {
        label: "原值",
        headerAlign: "center",
        children: [
            {
                label: "借方",
                prop: "y_debit",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 90,
                width: getColumnWidth(setModuleExamine, 'y_debit'),
            },
            {
                label: "贷方",
                prop: "y_credit",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 90,
                width: getColumnWidth(setModuleExamine, 'y_credit'),
            },
            {
                label: "余额",
                prop: "y_total",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 100,
                width: getColumnWidth(setModuleExamine, 'y_total'),
            },
        ],
    },
    {
        label: "累计折旧",
        headerAlign: "center",
        children: [
            {
                label: "借方",
                prop: "l_debit",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 90,
                width: getColumnWidth(setModuleExamine, 'l_debit'),
            },
            {
                label: "贷方",
                prop: "l_credit",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 90,
                width: getColumnWidth(setModuleExamine, 'l_credit'),
            },
            {
                label: "余额",
                prop: "l_total",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 100,
                width: getColumnWidth(setModuleExamine, 'l_total'),
            },
        ],
    },
    {
        label: "减值准备",
        headerAlign: "center",
        children: [
            {
                label: "借方",
                prop: "j_debit",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 90,
                width: getColumnWidth(setModuleExamine, 'j_debit'),
            },
            {
                label: "贷方",
                prop: "j_credit",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 90,
                width: getColumnWidth(setModuleExamine, 'j_credit'),
            },
            {
                label: "余额",
                prop: "j_total",
                align: "right",
                headerAlign: "right",
                formatter: (row, column, value) => {
                    return formatMoneyWithZero(value);
                },
                minWidth: 100,
                width: getColumnWidth(setModuleExamine, 'j_total'),
            },
        ],
    },
    {
        label: "净值",
        prop: "newvalue",
        align: "right",
        headerAlign: "right",
        formatter: (row, column, value) => {
            return formatMoneyWithZero(value);
        },
        minWidth: 90,
        width: getColumnWidth(setModuleExamine, 'newvalue'),
    },
    { 
        label: "使用情况", 
        prop: "status", 
        align: "left", 
        headerAlign: "left", 
        minWidth: 70, 
        width: getColumnWidth(setModuleExamine, 'status') 
    },
    { 
        label: "使用部门", 
        prop: "department", 
        align: "left", 
        headerAlign: "left", 
        minWidth: 120, 
        width: getColumnWidth(setModuleExamine, 'department'),
        resizable: false,
    },
]);
const setModuleChangeHistory = "FixedassestsChangeHistory";
const examineFaId = ref("");
const changeHistoryData = ref<any[]>([]);
const changeHistoryColumns = ref<Array<IColumnProps>>([
    { label: "变动单类别", prop: "changetype", align: "left", headerAlign: "left", width: getColumnWidth(setModuleChangeHistory, 'changetype') },
    { label: "资产编号", prop: "fa_num", align: "left", headerAlign: "left", width: getColumnWidth(setModuleChangeHistory, 'fa_num') },
    { label: "资产名称", prop: "fa_name", align: "left", headerAlign: "left", width: getColumnWidth(setModuleChangeHistory, 'fa_name') },
    { label: "变动前内容", prop: "value1", align: "left", headerAlign: "left", width: getColumnWidth(setModuleChangeHistory, 'value1') },
    { label: "变动后内容", prop: "value2", align: "left", headerAlign: "left", width: getColumnWidth(setModuleChangeHistory, 'value2') },
    { label: "变动期间", prop: "period", align: "left", headerAlign: "left", width: getColumnWidth(setModuleChangeHistory, 'period') },
    { slot: "vgname" },
]);

const editFaId = ref(0);
const editPid = ref(-1);
const maxDate = ref();
function handleEdit(faid: number, pid: number, isIntangibles?: boolean) {
    editFaId.value = faid;
    editPid.value = pid;
    isBackIntangibles.value = isIntangibles ?? false;
    request({
        url: `/api/FixedAssets?faid=${faid}&pid=${pid}`,
        method: "get",
    }).then((res: IResponseModel<IAssetDetail>) => {
        editData.value = res.data;
        getPidMaxDate(pid).then((res: any) => {
            maxDate.value = res;
            nextTick().then(() => {
                currentSlot.value = "edit";
                EditFARef.value?.changeInit(true);
            });
        });
    });
}
function getPidMaxDate(pid: number) {
    return request({
        url: `/api/FAPeriod/GetMaxDate?pid=${pid}`,
        method: "post",
    });
}

function divCancel(isRefresh?: boolean,isIntangibles?: boolean,pid?: number) {
    if(pid !== undefined){
        !periodList.value.find((item)=>item.pid === pid) && getPeriodApi()
    }
    if(isIntangibles || isBackIntangibles.value){
        currentSlot.value = "intangibles";
        if(isIntangibles) getPeriodApi();
        intangibleAssetsRef.value?.getTableData(true);
    }else{
        currentSlot.value = "main";
    }
    fixedassetsListRef.value?.handleSearch();
    if(commissioningDate.value === iaPeriodInfo.value){
        initFixedassetsRef.value?.checkHasIaAssets();
    }else{
        intangibleAssetsRef.value?.checkHasIaAssets();
    }
    if (activeName.value === "initFixedassets") {
        initFixedassetsRef.value?.getTableData(true);
    }
    if (isRefresh) {
        changeRecordRef.value?.getTableData();
    }
}

function downloadfile() {
    downloadFile(`/api/FixedAssets/ExportFixedAssetsTemplate`);
}

const fileName = ref("");
const selectedFile = ref();
const selectedFileRef = ref();
const uploadFile = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const file: File = (input.files as FileList)[0];
    if (!file) {
        fileName.value = "";
        selectedFile.value = null;
        return;
    }
    fileName.value = file.name;
    selectedFile.value = file;
};

function SubmitDR() {
    if (!selectedFile.value) {
        ElNotify({
            type: "warning",
            message: "请选择文件",
        });
        return;
    }
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    useLoading().enterLoading("努力加载中，请稍候...");
    let importUrl =
        activeName.value === "initFixedassets"
            ? `/api/FixedAssets/ImportInitial?pid=${isBackIntangibles.value ? iaPid.value :pid.value}`
            : `/api/FixedAssets/Import?pid=${currentPid.value}`;
    request({
        url: importUrl,
        method: "post",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    })
        .then((res: IResponseModel<boolean>) => {
            useLoading().quitLoading();
            if (res.state == 1000) {
                ElNotify({
                    message: "导入成功",
                    type: "success",
                });

                onImportCancel();
                if(activeName.value ==="initFixedassets"){
                    isBackIntangibles.value
                        ? intangibleAssetsRef.value?.getTableData()
                        : initFixedassetsRef.value?.getTableData();
                }else{
                    fixedassetsListRef.value?.handleSearch();
                }
                handleExpiredCheckData(ExpiredCheckModuleEnum.Assets);
            } else if (res.state === 2000) {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            } else {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        })
        .finally(() => {
            useLoading().quitLoading();
        });
}

const onImportCancel = () => {
    fileName.value = "";
    selectedFile.value = null;
    if (selectedFileRef.value) selectedFileRef.value!.value = "";
    currentSlot.value = isBackIntangibles.value ? "intangibles":"main";
};

function getChangeData(type: string = "") {
    changeRecordRef.value?.getTableData();
    if(commissioningDate.value === iaPeriodInfo.value){
        initFixedassetsRef.value?.checkHasIaAssets();
    }else{
        intangibleAssetsRef.value?.checkHasIaAssets();
    }
    if (type === "list") {
        initFixedassetsRef.value?.getTableData();
    }else if(type === "init"){
        fixedassetsListRef.value?.handleSearch();
    }
}

function changeCancle(isChange: boolean = false) {
    currentSlot.value = "main";
    fixedassetsListRef.value?.handleSearch();
    if(isChange) isLoaded.value["changeRecord"] = false;
}

//资产变更生成凭证
function getVoucher(divid: string, changeId: number) {
    let generateUrl = "";
    switch (divid) {
        case "kmtz":
            generateUrl = `/api/FAVoucher/GenerateAsubChangeVoucher?changeId=${changeId}`;
    }
    // // 生成累计折旧调整凭证
    request({
        url: generateUrl,
        method: "post",
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            goToVoucher(res.data, changeTitle.value, [], "", changeId);
        } else {
            ElNotify({
                type: "error",
                message: "生成凭证失败",
            });
        }
    });
}

const changeDataTemp = ref({});
function handleChangeSave(response: any, divid: string, data?: any) {
    data && (changeDataTemp.value = data);
    if (response.state === 1000) {
        if (response.data == "period") {
            ElNotify({
                type: "warning",
                message: "当前期间无法执行此操作，请刷新页面重试",
            });
        } else if ((divid == "kmtz" || divid == "bmzy") && isNaN(response.data)) {
            ElNotify({
                type: "warning",
                message: response.data.substring(response.data.indexOf("error") + 6),
            });
        } else if (divid != "synxtz" && divid != "bmzy" && divid != "zjfftz" && divid != "ztxg") {
            ifCanUseVoucher(response.data, divid, changeData, () => changeCancle(true), getVoucher);
        } else {
            changeCancle(true);
        }
        //接口好像资产优化迁移了，这些错误信息的判断可能需要修改 2024-01-08
    } else if(response.state === 2000){
        if ((divid == "kmtz" || divid == "bmzy") && response.msg.indexOf("error") >= 0) {
            ElNotify({
                type: "warning",
                message: response.msg.substring(response.msg.indexOf("error") + 6),
            });
        } 
    }else {
        ElNotify({
            type: "error",
            message: "更新失败",
        });
    }
}

function examineBack() {
    currentSlot.value = "main";
}
function examineExport() {
    globalExport(`/api/FASubsidiaryLedger/Export?faid=${examineFaId.value}`);
}
function examinePrint() {
    globalPrint(`/api/FASubsidiaryLedger/Print?faid=${examineFaId.value}`);
}

const changeTitle = ref("");
const departmentShow = ref(false);

const initType = ref("");
function increaseAssets(pid: number, type?: string) {
    //获取当日时间
    let currentdate = new Date();
    let year = currentdate.getFullYear();
    let month = currentdate.getMonth() + 1;
    let currentDate = `${year}年${month}月`;
    assetsListPid.value = pid;
    entryPeriod.value = activeName.value === "initFixedassets" ? commissioningDate.value : currentDate;
    const increaseInfo = periodList.value.find((item) => item.pid === pid)
    if(increaseInfo){
        dateInfo.value = increaseInfo.periodInfo;
    }
    if (!type) type = !iaPid.value ? "fixAsset" : "";
    initType.value = type;
    nextTick().then(() => {
        currentSlot.value = "add";
        setTimeout(() => {
            AddFARef.value?.myFocus();
            AddFARef.value?.changeInit(true);
        }, 600);
    });
}
function handleImport(val: string,intangibles?: boolean) {
    if (activeName.value === "fixedassetsList") {
        fixedassetsListRef.value?.FAPeriod && (currentPid.value = fixedassetsListRef.value.FAPeriod);
    }
    currentSlot.value = val;
    isBackIntangibles.value = intangibles ?? false;
}

function handleVoucher(vPid: number, vVid: number, slot: string) {
    lastSlot.value = slot;
    voucherQueryParams.value = new EditVoucherQueryParams(vPid, vVid);
    currentSlot.value = "voucher";
    voucherIsInit = false;
    if (window.localStorage.getItem("voucherZoomState")) {
        zoomState.value = window.localStorage.getItem("voucherZoomState") === "in" ? "in" : "out";
    }
}

function handleActiveName(val: string) {
    activeName.value = val;
    changeRecordRef.value?.getTableData();
}

function goDepreciationTab(val: string) {
    currentSlot.value = "main";
    activeName.value = 'depreciation'
    if (!isLoaded.value["depreciation"]) {
        depreciationRef.value?.getTableData();
    }
}



const depreDelete = ref(false);
function generateVoucher(data: any, date: string, vPid?: number, vVid?: number) {
    if (vPid && vVid) {
        depreDelete.value = true;
        depreVoucherQueryParams.value = new EditVoucherQueryParams(vPid, vVid);
        currentSlot.value = "depreVoucher";
        voucherIsInit = false;
        return;
    }
    depreDelete.value = false;
    let params: VoucherEntryModel[] = [];
    params = data.map((item: any) => {
        return {
            veId: item.veId,
            asubId: item.asubId,
            asubType: item.asubType,
            asubCode: item.asubCode,
            asubName: item.asubName,
            asubAAName: item.asubAAName,
            debit: item.debit,
            credit: item.credit,
            description: item.description,
            fcId: item.fcId,
            fcCode: item.fcCode,
            fcAmount: item.fcAmount,
            fcRate: item.fcRate,
            foreigncurrency: item.foreigncurrency,
            measureUnit: item.measureUnit,
            quantity: item.quantity,
            price: item.price,
            quantityAccounting: item.quantityAccounting,
            aacode: item.aacode ? item.aacode : "",
            assistingAccounting: item.assistingAccounting,
            isFaLine: item.isFaLine,
        };
    });
    depreVoucherQueryParams.value = new DataVoucherQueryParams(params);
    depreVoucherQueryParams.value.vdate = date;
    currentSlot.value = "depreVoucher";
    voucherIsInit = true;
    if (window.localStorage.getItem("voucherZoomState")) {
        zoomState.value = window.localStorage.getItem("voucherZoomState") === "in" ? "in" : "out";
    }
}

const saveDepreciationVoucher = _.debounce(SaveDepreVoucher, 500);

let isSaveDepreVoucher = false;
async function SaveDepreVoucher() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaveDepreVoucher) return;
    isSaveDepreVoucher = true;
    deprevoucherView.value.saveVoucher(
        new VoucherSaveParams(1052, (res: IResponseModel<VoucherSaveModel>) => {
            if (res.state === 1000) {
                // 关联折旧与凭证
                request({
                    url: `/api/FAVoucher/BindDepreciationAndVoucher?pid=${res.data.pid}&vid=${res.data.vid}`,
                    method: "post",
                }).then((res: IResponseModel<boolean>) => {
                    if (res.data) {
                        ElNotify({
                            type: "success",
                            message: "凭证生成成功！",
                        });
                        currentSlot.value = "main";
                        useFullScreenStore().changeFullScreenStage(false);
                        setTimeout(() => {
                            isSaveDepreVoucher = false;
                        }, 1000);
                        reloadPeriodInfo();
                        depreciationRef.value?.getTableData();
                    }
                });
                voucherIsInit = false;
            } else if (res.state === 2000) {
                isSaveDepreVoucher = false;
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else {
                isSaveDepreVoucher = false;
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
    );
}
function deletDepreVoucher() {
    let voucherModel = deprevoucherView.value?.getVoucherModel();
    if (voucherModel) {
        ElConfirm("亲，确认要删除吗").then((r) => {
            if (r) {
                // 删除凭证
                request({ url: "/api/Voucher?pId=" + voucherModel?.pid + "&vId=" + voucherModel?.vid, method: "delete" }).then(
                    (res: IResponseModel<boolean>) => {
                        const data = res;
                        if (data.data) {
                            currentSlot.value = "main";
                            useFullScreenStore().changeFullScreenStage(false);
                            switch (activeName.value) {
                                case "depreciation":
                                    depreciationRef.value?.getTableData();
                                    break;
                                case "changeRecord":
                                    changeRecordRef.value?.getTableData();
                                    break;
                            }
                            ElNotify({
                                message: "亲，删除成功啦！",
                                type: "success",
                            });
                        } else {
                            ElNotify({
                                message: data.msg || "亲，删除失败啦！请联系侧边栏客服！",
                                type: "warning",
                            });
                        }
                    }
                );
            }
        });
    }
}
function depreVoucherBack() {
    deprevoucherView.value?.removeEventListener();
    currentSlot.value = "main";
    voucherIsInit = false;
    useFullScreenStore().changeFullScreenStage(false);
}
function deleteVoucher() {
    let voucherModel = voucherView.value?.getVoucherModel();
    if (voucherModel) {
        ElConfirm("亲，确认要删除吗").then((r) => {
            if (r) {
                // 删除凭证
                request({ url: "/api/Voucher?pId=" + voucherModel?.pid + "&vId=" + voucherModel?.vid, method: "delete" }).then(
                    (res: IResponseModel<boolean>) => {
                        const data = res;
                        if (data.data) {
                            currentSlot.value = "main";
                            useFullScreenStore().changeFullScreenStage(false);
                            switch (activeName.value) {
                                case "depreciation":
                                    depreciationRef.value?.getTableData();
                                    break;
                                case "changeRecord":
                                    changeRecordRef.value?.getTableData();
                                    break;
                            }
                            ElNotify({
                                message: "亲，删除成功啦！",
                                type: "success",
                            });
                        } else {
                            ElNotify({
                                message: data.msg || "亲，删除失败啦！请联系侧边栏客服！",
                                type: "warning",
                            });
                        }
                    }
                );
            }
        });
    }
}

function goBackPage() {
    voucherView.value?.removeEventListener();
    currentSlot.value = lastSlot.value;
    voucherIsInit = false;
    useFullScreenStore().changeFullScreenStage(false);
}
const changeData = ref();
const changePid = ref<number>(-1);
function handleChange(val: any) {
    changeTitle.value = val.title;
    departmentShow.value = val.departmentShow;
    changeData.value = val.data;
    changeData.value.department_name = changeData.value.faAmortizations.reduce((prev: string, cur: any, index: number) => {
        return index === changeData.value.faAmortizations.length - 1 ? prev + cur.departmentName : prev + cur.departmentName + ",";
    }, "");
    changePid.value = val.pid;
    nextTick(() => {
        currentSlot.value = val.slot;
    });
}
// 获取资产明细列表
function getExamineData() {
    examineLoading.value = true;
    request({
        url: `/api/FASubsidiaryLedger/PagingList?faid=${examineFaId.value}&periodInfo=${examinePeriod.value}&PageIndex=${examinePagination.currentPage}&PageSize=${examinePagination.pageSize}`,
        method: "post",
    }).then((res: IResponseModel<{ data: object[]; count: number }>) => {
        examineLoading.value = false;
        if (res.state === 1000) {
            examineData.value = res.data.data;
            examinePagination.total = res.data.count;
        }
    });
}
const examinePeriod = ref<string>("");
function handleExamine(FA_ID: string, currentPeriod: string) {
    currentSlot.value = "examine";
    examineFaId.value = FA_ID;
    examinePeriod.value = currentPeriod;
    getExamineData();
}

const ShowDetail = (row: ITableData, slot: string) => {
    lastSlot.value = slot;
    voucherQueryParams.value = new EditVoucherQueryParams(row.vp_id, row.vv_id);
    currentSlot.value = "voucher";
    if (window.localStorage.getItem("voucherZoomState")) {
        zoomState.value = window.localStorage.getItem("voucherZoomState") === "in" ? "in" : "out";
    }
};
const changeHistoryLoading = ref<boolean>(false);
function getChangeHistoryData() {
    changeHistoryLoading.value = true;
    request({
        url: `/api/FAChange/PagingListByFAId?faid=${examineFaId.value}&periodName=${examinePeriod.value}&PageIndex=${changeHistoryPagination.currentPage}&PageSize=${changeHistoryPagination.pageSize}`,
    }).then((res: IResponseModel<{ data: object[]; count: number }>) => {
        changeHistoryLoading.value = false;
        if (res.state == 1000) {
            changeHistoryData.value = res.data.data;
            changeHistoryPagination.total = res.data.count;
        }
    });
}
function changeHistory(FA_ID: string, currentPeriod: string) {
    currentSlot.value = "changeHistory";
    examineFaId.value = FA_ID;
    examinePeriod.value = currentPeriod;
    getChangeHistoryData();
}
// 变更后保存跳到凭证
const voucherQueryParams = ref();
const changeVoucherQueryParams = ref();
const depreVoucherQueryParams = ref();
const voucherView = ref<InstanceType<typeof VoucherView>>();
const zoomState = ref<"in" | "out">("in");
if (window.localStorage.getItem("voucherZoomState")) {
    zoomState.value = window.localStorage.getItem("voucherZoomState") === "in" ? "in" : "out";
}
let voucherTitle = ref();
let changeIds = ref<number[]>([]);
let changeId = ref<number>(0);
const bindchangeRecord = ref<boolean>(false);
function goToVoucher(data: any, title: string, ids?: number[], voucherdate?: string, id?: number, bindChange?: boolean) {
    // 无纸化序号4只改了这个接口  但是这个函数有很多地方调用  所以直接顶层判断一下
    const isBatchGenerateChangeVoucherApi = data.voucherLines !== undefined;
    const date = periodList.value!.find((item: any) => item.pid === currentPid.value)?.periodInfo as string;
    const { year, month, lastDay } = getYearMonthLastDay(date);
    voucherTitle.value = title;
    ids && (changeIds.value = ids);
    id && (changeId.value = id);
    bindChange && (bindchangeRecord.value = bindChange);
    let params: VoucherEntryModel[] = [];
    params = (isBatchGenerateChangeVoucherApi ? data.voucherLines : data).map((item: any) => {
        return {
            veId: item.veId,
            asubId: item.asubId,
            asubType: item.asubType,
            asubCode: item.asubCode,
            asubName: item.asubName,
            asubAAName: item.asubName,
            debit: item.debit,
            credit: item.credit,
            description: item.description,
            fcId: item.fcId,
            fcCode: item.fcCode,
            fcAmount: item.fcAmount,
            fcRate: item.fcRate,
            foreigncurrency: item.foreigncurrency,
            measureUnit: item.measureUnit,
            quantity: item.quantity,
            price: item.price,
            quantityAccounting: item.quantityAccounting,
            aacode: item.aacode,
            assistingAccounting: item.assistingAccounting,
            isFaLine: item.isFaLine,
        };
    });
    // 外币id 不改可能更好，但是是符合旧系统的表现，以后可以去掉
    params.forEach((z) => (z.fcId = 0));
    const queryParams = new DataVoucherQueryParams(params);
    // 只有新增资产才带附件
    if (1000 === data.changeType && isBatchGenerateChangeVoucherApi) {
        queryParams.attachFileIds = data.attachFileIds;
        queryParams.attachments = data.attachFiles.length;
        queryParams.attachFiles = data.attachFiles.map((item: any) => {
            item.relativePath = item.filePath.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
            return item;
        });
    }
    queryParams.vdate = voucherdate ? voucherdate : year + "-" + (month < 10 ? "0" + month : month) + "-" + lastDay;
    changeVoucherQueryParams.value = queryParams;
    currentSlot.value = "changeVoucher";
    voucherIsInit = true;
    if (changeRecordRef.value!.voucherflag) {
        lessGetVoucher();
    }
    if (window.localStorage.getItem("voucherZoomState")) {
        zoomState.value = window.localStorage.getItem("voucherZoomState") === "in" ? "in" : "out";
    }
}

//防止动画还没切过去重复点击生成凭证按钮
const timer = ref<any>(null);
const lessGetVoucher = () => {
    timer.value = setTimeout(() => {
        changeRecordRef.value!.voucherflag = false;
    }, 1000);
};
onUnmounted(() => {
    clearTimeout(timer.value);
});

let isSaving = false;
async function editVoucherSave() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) return;
    isSaving = true;
    let saveData = new VoucherSaveParams(1052, (res: IResponseModel<VoucherSaveModel>) => {
        isSaving = false;
        if (res.state === 1000) {
            ElNotify({
                type: "success",
                message:
                    activeName.value === "changeRecord"
                        ? "亲，保存成功"
                        : activeName.value === "fixedassetsListRef"
                        ? "添加凭证成功！"
                        : "亲，保存成功！",
            });
            currentSlot.value = "main";
            useFullScreenStore().changeFullScreenStage(false);
            voucherIsInit = false;
            activeName.value === "changeRecord" && changeRecordRef.value?.getTableData();
        } else if (res.state === 2000) {
            if (res.subState !== -1) {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
    saveData.autoVNum = false;
    voucherView.value?.saveVoucher(saveData);
}

const zoomCallback = ref((_zoomState: "in" | "out"): void => {
    zoomState.value = _zoomState;
});

const saveChangeVoucher = _.debounce(SaveMyVouchers, 500);

let isSaveChangeVoucher = false;
async function SaveMyVouchers() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaveChangeVoucher) return;
    isSaveChangeVoucher = true;
    let params = new VoucherSaveParams(1052, (res: IResponseModel<VoucherSaveModel>) => {
        if (res.state === 1000) {
            const data = {
                changeIds: changeIds.value.map((item: number) => item + ""),
                vouchers: changeIds.value.map(() => res.data.pid + "," + res.data.vid),
            };
            if (activeName.value === "changeRecord" || bindchangeRecord.value) {
                request({
                    url: `/api/FAVoucher/BindChangesAndVouchers`,
                    method: "post",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: JSON.stringify(data),
                }).then((res: IResponseModel<boolean>) => {
                    if (res.data) {
                        ElNotify({
                            type: "success",
                            message: "凭证生成成功!",
                        });
                        currentSlot.value = "main";
                        useFullScreenStore().changeFullScreenStage(false);
                        setTimeout(() => {
                            isSaveChangeVoucher = false;
                        }, 1000);
                        changeRecordRef.value?.getTableData();
                        edited.value = false;
                    }
                });
            } else {
                request({
                    url: `/api/FAVoucher/BindChangeAndVoucher?changeId=${changeId.value}&pid=${res.data.pid}&vid=${res.data.vid}`,
                    method: "post",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }).then((res: IResponseModel<boolean>) => {
                    if (res.data) {
                        ElNotify({
                            type: "success",
                            message: "添加凭证成功！",
                        });
                        currentSlot.value = "main";
                        useFullScreenStore().changeFullScreenStage(false);
                        setTimeout(() => {
                            isSaveChangeVoucher = false;
                        }, 1000);
                        fixedassetsListRef.value?.handleSearch();
                        edited.value = false;
                    }
                });
            }
            voucherIsInit = false;
        } else if (res.state === 2000) {
            isSaveChangeVoucher = false;
            if (res.subState !== -1) {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        } else {
            isSaveChangeVoucher = false;
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
    params.autoVNum = false;
    changeVoucher.value.saveVoucher(params);
}

const isInputAccout = ref(false);
function cancelMyVouchers() {
    currentSlot.value = "main";
    useFullScreenStore().changeFullScreenStage(false);
    voucherIsInit = false;
    isInputAccout.value = true;
    fixedassetsListRef.value?.handleSearch();
    setTimeout(() => {
        isInputAccout.value = false;
    }, 100);
}
const isBackIntangibles = ref(false);
const showAssetsDetail = (data: IAssetDetail,intangibles?:boolean) => {
    isBackIntangibles.value = intangibles ?? false;
    detailData.value = data;
    currentSlot.value = "detail";
};

onMounted(() => {
    // 获取启用日期
    getPeriodsApi();
    getPeriodApi();
    getCurrentPeriod();
});
let firstInit = true;
const reLoadCurrentPage = () => {
    const fromQuery = Array.isArray(route.query.from) ? route.query.from[0] ?? "" : route.query.from ?? "";
    if (!isFromOtherPage(fromQuery)) return;
    if (finishflag.value === "0") {
        ElConfirm("亲，请先完善资产初始化数据，否则与本期期初的资产数据不一致！", true).then(() => {
            activeName.value = "initFixedassets";
        });
        tryClearCustomUrlParams(route);
        return;
    }
    currentSlot.value = "main";
    if (route.query.period) {
        currentPid.value = Number(route.query.period);
    }
    if (route.query.tabflag == "1" && route.query.from === "ERecord") {
        isLoaded.value.fixedassetsList = true;
        activeName.value = "fixedassetsList";
        setTimeout(() => {
            route.query.faNum && fixedassetsListRef.value?.setFaNumForSearch(route.query.faNum as string);   
            fixedassetsListRef.value?.handleSearch();
            tryClearCustomUrlParams(route);
        });
    } else if (route.query.tabflag == "2") {
        isLoaded.value.changeRecord = true;
        activeName.value = "changeRecord";
        setTimeout(() => {
            changeRecordRef.value?.changeSearchParam(route.query);
            tryClearCustomUrlParams(route);   
        });
    } else if (route.query.tabflag == "3") {
        isLoaded.value.depreciation = true;
        activeName.value = "depreciation";
        setTimeout(() => {
            if (fromQuery === "voucherList") {
                depreciationRef.value?.getDepreciationPageIndex(route.query.dpid);
            } else {
                depreciationRef.value?.getTableData();
            }
            tryClearCustomUrlParams(route);   
        });
    }
};

let isInVoucherPage = false;
let isOtherPage = false;
let voucherIsInit = false;
const edited = ref(false);
const recordSlot = ref("");
const voucherChanged = () => {
    isLoaded.value["changeRecord"] = false;
    if (!voucherIsInit) return;
    edited.value = true;
};
const voucherLoadSuccess = (voucherParams: any) => {
    if (voucherParams instanceof EditVoucherQueryParams) {
        edited.value = false;
    }
    voucherIsInit = true;
};
let cacheRouterQueryParams: any = null;
const fromOtherPageList = ["voucherList", "checkout", "FACheckDetail", "ERecord"];
const isFromOtherPage = (fromQuery:string): boolean => {
    if (!cacheRouterQueryParams?.from && fromOtherPageList.includes(fromQuery)) {
        return true;
    }
    if (
        fromOtherPageList.includes(fromQuery) &&
        cacheRouterQueryParams.r &&
        cacheRouterQueryParams.r !== route.query.r
    ) {
        return true;
    }

    return false;
}
onBeforeRouteLeave((to, from, next) => {
    firstInit = false;
    cacheRouterQueryParams =  from.query;
    recordSlot.value = currentSlot.value;
    isInVoucherPage = ["voucher", "changeVoucher", "depreVoucher"].includes(currentSlot.value);
    isOtherPage = [
        "add",
        "edit",
        "import",
        "examine",
        "changeHistory",
        "valueChange",
        "accumulatedChange",
        "provisionChange",
        "usefullifeChange",
        "asubChange",
        "departmentChange",
        "depreciationTypeChange",
        "statusChange",
        "detail",
        "intangibles",
    ].includes(currentSlot.value);
    next();
});
onActivated(() => {
    if (firstInit) return;
    if (isInVoucherPage && edited.value && route.query.period) {
        editConfirm("voucherEdit", () => {}, reLoadCurrentPage, "资产管理");
    } else if ((route.query.from === "voucherList" && isFromOtherPage("voucherList")) || (route.query.from === "ERecord" && isFromOtherPage("ERecord"))) {
        if (isEditting.value) {
            editConfirm("otherEdit", () => {}, reLoadCurrentPage);
        } else {
            reLoadCurrentPage();
        }
    } else if (((isInVoucherPage || isOtherPage) && !edited.value) || (!route.query.period && isInVoucherPage)) {
        currentSlot.value = recordSlot.value;
    } else {
        reLoadCurrentPage();
    }
});
const EditFARef = ref<InstanceType<typeof EditFixedAssets>>();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
const isEditting = computed(() => {
    return (
        (currentSlot.value === "add" && AddFARef.value!.getAddSlotIsEditting()) ||
        (currentSlot.value === "edit" && EditFARef.value!.getEditSlotIsEditting()) ||
        (["voucher", "changeVoucher", "depreVoucher"].includes(currentSlot.value) && edited.value)
    );
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
const isLoaded = ref<ITabsName>({
    fixedassetsList: false,
    initFixedassets: false,
    changeRecord: false,
    depreciation: false,
});

interface ITabsName {
    fixedassetsList: Boolean;
    initFixedassets: Boolean;
    changeRecord: Boolean;
    depreciation: Boolean;
}
function handleTabClick(tab: any) {
    const { name } = tab.props;
    if (["fixedassetsList", "changeRecord", "depreciation"].includes(name)) {
        if (finishflag.value === "0") {
            ElConfirm("亲，请先完善资产初始化数据，否则与本期期初的资产数据不一致！", true).then(() => {
                activeName.value = "initFixedassets";
            });
            return;
        }
    }
    if (!isLoaded.value[name as keyof ITabsName]) {
        switch (name) {
            case "fixedassetsList":
                fixedassetsListRef.value?.handleSearch();
                break;
            case "initFixedassets":
                initFixedassetsRef.value?.getTableData();
                break;
            case "changeRecord":
                changeRecordRef.value?.getTableData();
                break;
            case "depreciation":
                depreciationRef.value?.getTableData();
                break;
        }
        isLoaded.value[name as keyof ITabsName] = true;
    }
}

function getVDateDisabledState(date: Date) {
    nextTick().then(() => {
        let thisDate = changeVoucherQueryParams?.value?.vdate;
        if (thisDate === undefined) {
            return false;
        }
        let endDate = new Date(thisDate);
        let beginDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        return !(date.getTime() >= beginDate.getTime() && date.getTime() <= endDate.getTime());
    });
}
watch(
    [() => changeHistoryPagination.currentPage, () => changeHistoryPagination.pageSize, () => changeHistoryPagination.refreshFlag],
    () => {
        getChangeHistoryData();
    }
);
watch([() => examinePagination.currentPage, () => examinePagination.pageSize, () => examinePagination.refreshFlag], () => {
    getExamineData();
});

watch(currentSlot, (newVal, oldVal) => {
    if (["voucher", "changeVoucher", "depreVoucher"].includes(oldVal)) {
        edited.value = false;
    }
});
</script>

<style scoped lang="less">
@import "@/style/FixedAssets/FixedAssets.less";
.image-container {
    width: 16px;
    height: 16px;
    cursor: pointer;
    display: inline-block;
    margin-left: 7px;
    background-image: url('@/assets/Settings/question.png');
    background-size: cover;
}

.image-container:hover{
    background-image: url('@/assets/Settings/question-green.png');
}

:deep(.el-tabs) {
    height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .el-tabs__content {
            flex: 1;
            display: flex;
            flex-direction: column;
            .main-center {
                flex: 1;
                .el-tab-pane {
                    height: 100%;
                    .el-scrollbar__view {
                        min-height: 0px;
                    }
                }
            }
        }
        .el-tab-pane {
            height: 100%;
        }
        .fixedassets-list,
        .init-fixedassets,
        .changeRecord,
        .depreciation {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
}
:deep(.el-tabs__content) {
    background-color: var(--white);
}

:deep(.el-tabs__header) {
    background-color: var(--white);
    margin: 0px;
}

:deep(.el-tabs__nav) {
    padding-left: 10px;
}

.voucher-edit-view {
    min-width: 1257px;

    .voucher-content-top {
        padding: 10px 20px;
        box-sizing: border-box;
        display: flex;
        justify-content: start;
        flex-shrink: 0;
    }

    .voucher-content-bottom {
        // padding: 10px;
        box-sizing: border-box;
        // height: 0;
        // flex: 1;
    }
}

.txt {
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    padding-left: 282px;
}

:deep(.change-tb) {
    & .tb-title {
        text-align: right;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
    }

    & .tb-field {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 14px;
        text-align: left;
        width: 334px;
        word-break: break-all;
        word-wrap: break-word;
    }

    & .span_wrap {
        display: -webkit-box;
        max-width: 100%;
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
        /* 显示省略号 */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 27px;
    }

    & .tb-hr {
        padding: 20px 20px 15px 20px;
        color: var(--font-color);
        font-size: var(--h3);
        line-height: 22px;
        font-weight: bold;
        text-align: left;
    }
}
.main-content {
    &.zoom-in.self-content {
        width: 1051px;
        align-self: center;
        & .voucher-content-top {
            padding: 10px 0px;
        }
    }
    &.zoom-out {
        min-width: 1002px;
        & .voucher-content-top {
            padding: 10px 54px;
        }
    }
}
.content {
    // overflow-y: auto;
    // height: 100%;
    :deep(.subject-dialog-container) {
        .el-tabs .el-tabs__content {
            padding-bottom: 0;
        }
    }
}
#addContentSlider,
#editContentSlider {
    display: flex;
    flex-direction: column;
    height: 100%;
}
#intangiblesContentSlider {
    :deep(.init-fixedassets) {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }
}
body[erp] {
    .erp-content {
        :deep(.search-info-selector) {
            background-color: transparent;
            color: var(--font-color);
        }
    }
    .content {
        #addContentSlider {
            :deep(.edit-content) {
                height: auto;
                width: 1050px;
            }
        }
        :deep(.container) {
            .voucher-container.zoom-in {
                .voucher-table {
                    box-sizing: border-box;
                }
            }
        }
        :deep(.custom-confirm) {
            .el-dialog__header .el-dialog__title {
                font-weight: 600;
            }
            .el-dialog__body .buttons {
                display: flex;
                justify-content: flex-end;
                a {
                    float: initial;
                }
            }
        }
        .main-content {
            :deep(.main-center) {
                .table.paging-show {
                    overflow: hidden;
                }
            }
            :deep(.el-table) {
                &.el-table--border {
                    &:before,
                    &:after {
                        background-color: var(--table-border-color);
                        width: 0px;
                    }
                }
                .el-table__inner-wrapper {
                    &:after {
                        background-color: var(--table-border-color);
                    }
                }
                .el-table__border-left-patch {
                    width: 0;
                }
                .el-table__header {
                    th.el-table__cell {
                        &.is-leaf {
                            border-bottom-color: var(--table-border-color);
                        }
                    }
                }
            }
            .voucher-content-bottom {
                flex: 1;
                overflow: hidden;
                overflow-y: auto;
            }
        }
    }
    .changeHistory {
        .main-center {
            height: 100%;
            overflow-y: auto;
        }
    }
    .voucher-edit-view {
        .main-content {
            &.zoom-in {
                .voucher-content-top {
                    width: 1051px;
                    padding-left: 0px;
                    padding-right: 0px;
                    margin: auto;
                }
            }

            &.zoom-out {
                .voucher-content-top {
                    padding-left: 54px;
                    padding-right: 54px;
                }
            }
        }
    }
}
</style>
