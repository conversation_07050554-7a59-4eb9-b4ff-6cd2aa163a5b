export interface IAcInfo {
    ac_id: string;
    ac_name: string;
    ac_no: string;
    ac_type: string;
    asub: string;
    asub_code: string;
    asub_name: string;
    as_id: string;
    bank_account: string;
    currency: string;
    currency_name: string;
    state: string;
}

export interface ITabledataAccountOrIetype {
    ac_id: string;
    ac_name: string;
    ac_no: string;
    expenditure_count: string;
    expenditure_sum: string;
    income_count: string;
    income_sum: string;
    initial: string;
    total: string;
}

export interface IIETableData {
    ie_id: string;
    ie_code: string;
    ie_name: string;
    income: string;
    expenditure: string;
    income_count: string;
    expenditure_count: string;
    count: number;
}

export interface IAAJournalTable {
    code: string;
    name: string;
    expend: number;
    expendCount: number;
    income: number;
    incomeCount: number;
    aaType?: string;
    aaNum?: string;
    income_standard: number;
    expend_standard: number;
    second_code?: string;
    second_name?: string;
}

export interface IAAJournalBack {
    total: number;
    rows: Array<IAAJournalTable>;
    totalRows: Array<IAAJournalTable> | null;
}

export interface IDateInfo {
    start: string;
    end: string;
}

export interface ICombineUrlParams {
    cd_ids: string;
    date_e: string;
    date_s: string;
    main_id: string;
    mian_item: string;
    sub_id: string;
    sub_item: string;
    fcid: string
    showDisabled: string;
}

export interface ISCurrcy {
    id: number;
    label: string;
    code: string;
}
