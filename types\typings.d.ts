declare interface Window {
    jHost: string;
    jFreeHost: string;
    jProHost: string;
    jAAFreeHost: string;
    jAAProHost: string;
    jApiHost: string;
    jFreeApiHost: string;
    jProApiHost: string;
    jAAFreeApiHost: string;
    jAAProApiHost: string;
    jFreeH5Host: string;
    jProH5Host: string;
    jAAH5Host: string;
    jAAProH5Host: string;
    jAABossH5Host: string;
    jAAProBossH5Host: string;
    accountSrvHost: string;
    wxShareReportUrl: string;
    shareReportHost: string;
    importFromOther: string;
    wxworkSuitId: string;
    productType: string;
    isAccountingAgent: boolean;
    isErp: boolean;
    isProSystem: boolean;
    jLmDiskHost: string;
    scmHost: string;
    scmProHost: string;
    isProSystem: boolean;
    apimHost: string;
    jAccountBooksHost: string;
    isWxworkService: boolean | undefined;
    isCurrentWxworkService: boolean | undefined;
    wxworkSuperAdminUserSn: number | undefined;
    wxworkSuperAdminOpenId: string | undefined;
    wwwHost: string;
    aaHost: string;
    eHost: string;
    epHost: string;
    erpHost: string;
    jmHost: string;
    jtaxHost: string;
    salaryHost: string;
    isBossSystem: boolean;
    proDialogOnclose: () => void;
    paBankUrl: string;
    spdbBankUrl: string;
    cmbBankUrl: string;
    psbcLoginUrl: string;
    preOpenBankUrl: string;
    printHost: string;
    authHost: string;
    maxRowNumber: number;
    wxworkSuitId: string;
    wxGzhAppId: string;
    maxSubjectNumber: number;
    isStopAIFunction: boolean;
    _hmt: any;
    invoiceConfigUrl: string;
    invoiceTaskPolllingInterval: number;
    erpApiUrl: string;
    jReceiptImportHost: string;
    FetchInoiceCodeTime: number;
    downLoadHost: string;
    appHost:string;
    fpHost:string;
    fpApiHost:string;
}
