import type { IRelationData } from "@/store/modules/eis";

export interface IEisCompanyListItem {
  id: string;
  name: string;
  uscic: string;
  taxDistrict: string;
  taxDistrictName: string;
  taxpayerType: number | null;
  systemRelationInfo: IRelationData | null;
  isAdministrator: boolean;
  isTaxBureauLogged: boolean;
}
export interface IRelationOperateRes {
  relation: IRelationData;
  isSuccess: boolean;
  errorMessage: string;
}


