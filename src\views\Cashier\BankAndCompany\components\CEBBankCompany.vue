<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:acid="autoBankForm.acid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :uscc-number="props.usccNumber"
        :help-show="false"
        @save-success="saveSuccessHandle"
        @confirm-bank="confirmBank"
    />
    <el-dialog v-model="CEBAuthorizeNotifyDialog" width="690px" center :destroy-on-close="true" title="提示" class="dialogDrag">
        <div class="ceb-dialog-content" v-dialogDrag>
            <div style="font-size: 18px; font-weight: bolder; text-align: center; margin-bottom: 30px">
                <p>
                    账号 <span>{{ CEBBankNumber }}</span> <span style="color: red">还未开通</span>光大银企互联功能哦~
                </p>
            </div>
            <div id="qrCode" style="float: left; height: 140px; text-align: center; margin-left: 20px">
                <img src="@/assets/Cashier/bankAndCompanyCustomerServiceQRCode.png" alt="" v-show="bankAndCompanyCustomerServiceQRCode" />
                <img
                    src="@/assets/Cashier/bankAndCompanyCustomerServiceQRCodePro.png"
                    alt=""
                    v-show="bankAndCompanyCustomerServiceQRCodePro"
                />
                <img
                    src="@/assets/Cashier/bankAndCompanyCustomerServiceQRCodeErp.png"
                    alt=""
                    v-show="bankAndCompanyCustomerServiceQRCodeErp"
                />
                <div
                    style="
                        font-size: 13px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #666666;
                        line-height: 18px;
                    "
                >
                    <p>
                        如有任何疑问
                        <br />
                        可联系客服处理哦~
                    </p>
                </div>
            </div>
            <div class="right-tip">
                <span style="font-weight: bold">光大银行银企互联开通步骤：</span>
                <div>
                    <div><span>①</span>下载打印申请表和授权书，根据样表填写内容进</div>
                    <div class="flex">行填写并盖章<a class="download" @click="handleDownload"> 下载申请表和授权书</a></div>
                </div>
                <div><span>②</span>联系开户行客户经理对申请表和授权书进行验印</div>
                <div><span>③</span>将已填写并验印的申请表和授权书寄给柠檬云</div>
                <div><span>④</span>柠檬云给您开通光大银企互联功能</div>
                <div><span>⑤</span>开通后客服会及时通知您并完成授权绑定</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="() => (CEBAuthorizeNotifyDialog = false)">我知道了</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watchEffect, onMounted, watch, nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { BankType } from "@/constants/bankKey";
import { GetBankLink } from "@/util/bankType";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { appendStyle, handleCheck } from "../utils";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, IBankHandleResult } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import { replaceAll } from "@/util/common";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = ["1、请确认公司名称和统一社会信用代码", "2、选择对应的银行账户，并确认对应的银行账号", "3、点击立即授权"];

const props = defineProps({
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const currencyList = computed(() => props.currencyList);
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const impowerValue = ref("立即授权");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    mainaccno: "",
});

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "申请中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};
const confirmBank = () => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    handleConfirmLock();
    request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000 || !res.data) {
            ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
            handleConfirmUnLock();
            return;
        }
        //检查当前账户的签约状态
        handleQuery();
    });
};

const CEBBankNumber = ref("");
const bankAndCompanyCustomerServiceQRCodePro = ref(false);
const bankAndCompanyCustomerServiceQRCodeErp = ref(false);
const bankAndCompanyCustomerServiceQRCode = ref(false);
const CEBAuthorizeNotifyDialog = ref(false);
const handleQuery = () => {
    const accountId = autoBankForm.acid;
    const companyName = autoBankForm.accname;
    const bankNo = replaceAll(autoBankForm.mainaccno, " ", "");
    const companyCode = autoBankForm.uscc;
    const accountName = bankAccountList.value.find((item) => item.ac_id == autoBankForm.acid)?.ac_name || "";
    const params = { bankNo, accountId, companyName, companyCode, accountName };
    const url = "/api/BankSign/CEBApply?" + getUrlSearchParams(params);
    const successMsg = "“" + accountName + "”已开通银企互联功能，签约后的每一笔款项都会实时自动录入该银行日记账哦~";
    request({ url, method: "post" }).then((res: IResponseModel<IBankHandleResult>) => {
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: "签约失败" });
            handleConfirmUnLock();
            return;
        }
        const result = res.data;
        if (result.code === 0) {
            if (result.data !== null && result.data.authorized === 1) {
                //已签约
                ElConfirm(appendStyle(successMsg), true, () => {}, "签约成功").then(() => {
                    handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
                });

            } else if (result.data !== null && result.data.authorized == 0) {
                CEBBankNumber.value = bankNo;
                if (window.isProSystem) {
                    bankAndCompanyCustomerServiceQRCodePro.value = true;
                } else if (window.isErp) {
                    bankAndCompanyCustomerServiceQRCodeErp.value = true;
                } else {
                    bankAndCompanyCustomerServiceQRCode.value = true;
                }
                CEBAuthorizeNotifyDialog.value = true;
            }
        } else {
            let errorMessage = "";
            if (result.data != null) {
                errorMessage = result.data.errorMsg;
            }
            ElConfirm(errorMessage, true);
        }
        handleConfirmUnLock();
        return;
    });
};

const handleDownload = () => {
    globalExport("/UploadFile/光大银企互联授权书和申请书.zip");
};

const bankHelpLink = ref("");
onMounted(() => {
    let res = GetBankLink(props.bankType);
    bankHelpLink.value = res;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});
watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);
defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
.ceb-dialog-content {
    background-color: var(--white);
    height: 380px;
    box-sizing: border-box;
    padding: 20px 56px;
    position: relative;
    .buttons {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        border-top: 1px solid var(--border-color);
        .button {
            width: 128px;
        }
    }
    .right-tip {
        width: 363px;
        float: right;
        font-size: 16px;
        > div {
            margin: 10px 0 0;
            line-height: 25px;
            .download {
                color: #3d7fff;
                font-size: 14px;
                line-height: 26px;
                cursor: pointer;
            }
            > span {
                margin-right: 3px;
            }
            .flex {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-right: 12px;
                padding-left: 18px;
            }
        }
    }
}
</style>
