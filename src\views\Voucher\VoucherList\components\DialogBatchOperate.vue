<template>
    <el-dialog title="批量修改" center width="440px" v-model="display" class="dialogDrag">
        <div class="batch-content" v-dialogDrag>
            <div class="batch-main">
                <div class="row">
                    <div class="row-label">修改字段：</div>
                    <SelectCheckBox
                        width="132px"
                        :showAll="false"
                        :isShowAllInfo="true"
                        :options="options"
                        v-model:selected-list="selectModifyOptions"
                    />
                </div>
                <div class="row">
                    <div class="row-label">修改为：</div>
                    <el-input v-model="newPeople" />
                </div>
                <div class="tip">修改制单人/审核人可能会导致系统和已打印出来的制单人/审核人姓名不一致</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleSave">确定</a>
                <a class="button ml-20" @click="display = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";

import type { IVoucherModel } from "@/views/Voucher/VoucherList/types";

import SelectCheckBox from "@/components/SelectCheckBox/index.vue";
import { request, type IResponseModel } from "@/util/service";

const checkNeeded = computed(() => useAccountSetStore().accountSet!.checkNeeded === 1);
const emit = defineEmits<{
    (event: "load-data", refresh: boolean): void;
}>();

const defaultOptions = [
    { id: 1, name: "制单人" },
    { id: 2, name: "审核人" },
];
const missingOptions = [{ id: 1, name: "制单人" }];
const options = ref<Array<{ id: number; name: string }>>(defaultOptions);
const selectModifyOptions = ref<Array<number>>([]);

const newPeople = ref("");
let isSaving = false;
interface IUpdateBack {
    type: number;
    batchOperationResult: {
        success: number;
        jump: number;
        faild: number;
    };
}

function handleSave() {
    if (isSaving) return;
    isSaving = true;
    if (selectModifyOptions.value.length === 0) {
        ElNotify({ type: "warning", message: "亲，请选择需要修改的字段哦~" });
        isSaving = false;
        return;
    }
    const name = newPeople.value.trim();
    if (name.length === 0) {
        ElNotify({ type: "warning", message: "亲，请录入修改后的信息哦~" });
        isSaving = false;
        return;
    }
    if (name.includes("'")) {
        ElNotify({ type: "warning", message: "亲，修改内容不能含有单引号哦~" });
        isSaving = false;
        return;
    }
    const params = {
        name,
        typeList: selectModifyOptions.value,
        pvList: mergeArray(voucherInfo),
    };
    request({ url: "/api/Voucher/BatchUpdateName", method: "put", data: params })
        .then((res: IResponseModel<Array<IUpdateBack>>) => {
            isSaving = false;
            display.value = false;
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "修改失败，请稍后再试" });
                return;
            }
            let error = 0;
            if (selectModifyOptions.value.length === 1 && selectModifyOptions.value[0] === 2) {
                error = res.data[0].batchOperationResult.faild;
            }
            if (error) {
                const success = voucherInfo.length - error;
                ElNotify({ type: "warning", message: `修改成功${success}张凭证，失败${error}张凭证（未审核的凭证已跳过）` });
            } else {
                ElNotify({ type: "success", message: `修改成功${voucherInfo.length}张凭证~` });
            }
            emit("load-data", selectModifyOptions.value.includes(1));
        })
        .catch(() => {
            isSaving = false;
        });
}
interface Result {
    pid: number;
    vidList: number[];
}
function mergeArray(arr: IVoucherInfo[]) {
    const resultMap: { [key: number]: number[] } = {};
    arr.forEach((item) => {
        if (resultMap[item.pid]) {
            resultMap[item.pid].push(item.vid);
        } else {
            resultMap[item.pid] = [item.vid];
        }
    });
    const resultArray: Result[] = Object.keys(resultMap).map((key) => ({
        pid: parseInt(key),
        vidList: resultMap[parseInt(key)],
    }));
    return resultArray;
}
interface IVoucherInfo {
    pid: number;
    vid: number;
    status: number;
}
const voucherInfo: Array<IVoucherInfo> = [];
const display = ref(false);
function handleOpenDialog(selected: Array<IVoucherModel>) {
    if (selected.length === 0) {
        ElNotify({ type: "warning", message: "请先勾选需要操作的凭证哦~" });
        return;
    }
    newPeople.value = "";
    voucherInfo.length = 0;
    selectModifyOptions.value.length = 0;
    let isMissing = true;
    for (let i = 0; i < selected.length; i++) {
        if (isMissing && selected[i].approvedBy !== "") {
            isMissing = false;
        }
        voucherInfo.push({
            pid: selected[i].pid,
            vid: selected[i].vid,
            status: selected[i].status,
        });
    }
    options.value = !checkNeeded.value && isMissing ? missingOptions : defaultOptions;
    display.value = true;
}
defineExpose({ handleOpenDialog });
</script>

<style lang="less" scoped>
.batch-content {
    .batch-main {
        padding: 20px 48px;
        .row {
            display: flex;
            align-items: center;
            justify-content: center;
            & + .row {
                margin-top: 20px;
            }
            .row-label {
                width: 70px;
                text-align: right;
            }
            :deep(.el-input) {
                width: 132px;
            }
        }
        .tip {
            margin-top: 20px;
            line-break: anywhere;
            position: relative;
            &::before {
                content: "";
                display: inline-block;
                width: 16px;
                height: 16px;
                position: absolute;
                top: 4px;
                left: -20px;
                background: url("@/assets/Icons/warn.png") no-repeat 100% 100%;
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
