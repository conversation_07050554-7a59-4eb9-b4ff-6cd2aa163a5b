<template>
    <Table
        ref="tableRef"
        :loading="loading"
        :data="tableData"
        :columns="columns"
        :page-is-show="true"
        :showOverflowTooltip="true"
        :layout="paginationData.layout"
        :page-sizes="paginationData.pageSizes"
        :page-size="paginationData.pageSize"
        :total="paginationData.total"
        :currentPage="paginationData.currentPage"
        :use-normal-scroll="true"
        :selectable="setSelectable"
        :scrollbar-show="true"
        row-key="billId"
        style="height: 100%"
        :highlight-current-row="false"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        @refresh="handleRerefresh"
        @scroll="handleScroll"
        :tableName="setModule"
    >
        <template #date>
            <el-table-column 
                label="单据日期" 
                min-width="140" 
                align="left" 
                header-align="left" 
                prop="billDate"
                :width="getColumnWidth(setModule, 'billDate')"
            >
                <template #default="scope">
                    <div
                        class="my-data-picker edit-item cd-date"
                        v-if="currentEditId === scope.row.billId"
                        @click.capture="handleDateFocus"
                    >
                        <el-date-picker
                            ref="datePickerRef"
                            v-model="scope.row.billDate"
                            type="date"
                            :clearable="false"
                            value-format="YYYY-MM-DD"
                            :editable="false"
                            popper-class="custom-pover"
                            @focus="handleFocus('datePickerRef')"
                            @change="handleDateChange($event, scope.row)"
                            @visible-change="visibleChange"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>

        <template #description>
            <el-table-column 
                label="费用事由" 
                min-width="180" 
                align="left" 
                header-align="left" 
                prop="billDesc"
                :width="getColumnWidth(setModule, 'billDesc')"
            >
                <template #default="scope">
                    <div class="description edit-item" v-if="currentEditId === scope.row.billId">
                        <el-input
                            ref="descriptionInputRef"
                            v-if="descriptionTextareaShow"
                            :autosize="{ minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            resize="none"
                            class="description-textarea"
                            v-model="scope.row.billDesc"
                            @focus="handleFocus('descriptionInputRef')"
                            @blur="handleBlur(scope.row, 'description')"
                            @keydown.enter="descriptionEnter"
                            @input="handleInput(scope.row.billDesc, '费用事由')"
                        />
                        <Tooltip v-else :content="scope.row.billDesc" :isInput="true" :teleported="true">
                            <el-input
                                ref="descriptionInputRef"
                                class="description-textarea"
                                v-model="scope.row.billDesc"
                                @focus="handleFocus('descriptionInputRef1')"
                                @keydown.enter="descriptionEnter"
                            />
                        </Tooltip>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #expenseType>
            <el-table-column 
                label="费用类型" 
                min-width="160" 
                align="left" 
                header-align="left" 
                prop="billTypeName"
                :width="getColumnWidth(setModule, 'billTypeName')"
            >
                <template #default="scope">
                    <div class="paymethod-select edit-item" v-if="currentEditId === scope.row.billId" @click.capture="handleFocus()">
                        <Select
                            ref="billTypeSelectRef"
                            v-model="scope.row.billTypeId"
                            :teleported="true"
                            :bottom-html="checkPermission(['expensebillsettings-canedit']) ? newSelectOptionHtml : ''"
                            :fit-input-width="true"
                            :filterable="true"
                            :clearable="true"
                            :immediately-blur="false"
                            placeholder=" "
                            popper-class="custom-pover"
                            @bottom-click="newSelectOption('billTypes')"
                            @keyup-enter="handleBillTypeTypeEnter"
                            @blur="handleBlur(scope.row)"
                            :filter-method="billTypeFilterMethod"
                        >
                            <Option
                                v-for="item in showBillTypes"
                                :key="item.billTypeId"
                                :value="item.billTypeId"
                                :label="item.billTypeName"
                            />
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #department>
            <el-table-column 
                label="部门" 
                min-width="160" 
                align="left" 
                header-align="left" 
                prop="depName"
                :width="getColumnWidth(setModule, 'depName')"
            >
                <template #default="scope">
                    <div class="project-select edit-item" v-if="currentEditId === scope.row.billId" @click.capture="handleFocus()">
                        <Select
                            ref="departmentSelectRef"
                            v-model="scope.row.depId"
                            :teleported="true"
                            :bottom-html="checkPermission(['assistingaccount-canedit']) ? newSelectOptionHtml : ''"
                            :fit-input-width="true"
                            :filterable="true"
                            :clearable="true"
                            :immediately-blur="false"
                            placeholder=" "
                            @bottom-click="newSelectOption('department')"
                            @keyup-enter="handleDepartmentEnter"
                            @blur="handleBlur(scope.row)"
                            :filter-method="departFilterMethod"
                        >
                            <Option 
                                v-for="item in showdepartmentList" 
                                :key="item.aaeid" 
                                :value="item.aaeid" 
                                :label="item.aaname" 
                            />
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>

        <template #payee>
            <el-table-column 
                label="收款人" 
                min-width="160" 
                align="left" 
                header-align="left" 
                prop="payeeName"
                :width="getColumnWidth(setModule, 'payeeName')"
            >
                <template #default="scope">
                    <div class="paymethod-select edit-item" v-if="currentEditId === scope.row.billId" @click.capture="handleFocus()">
                        <Select
                            ref="payeeRef"
                            v-model="scope.row.payeeId"
                            :teleported="true"
                            :bottom-html="checkPermission(['assistingaccount-canedit']) ? newSelectOptionHtml : ''"
                            :fit-input-width="true"
                            :filterable="true"
                            :clearable="true"
                            :default-first-option="true"
                            :immediately-blur="false"
                            placeholder=" "
                            @clear="scope.row.payeeName = ''"
                            @bottom-click="newSelectOption('payee')"
                            @keyup-enter="handlePayeeEnter"
                            @blur="handleBlur(scope.row)"
                            :filter-method="payeeFilterMethod"
                        >
                            <Option 
                                v-for="item in showPayeeList" 
                                :key="item.aaeid" 
                                :value="item.aaeid" 
                                :label="item.aaname" 
                            />
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #paymentMethod>
            <el-table-column 
                label="结算方式" 
                min-width="160" 
                align="left" 
                header-align="left" 
                prop="pmethodName"
                :width="getColumnWidth(setModule, 'pmethodName')"
            >
                <template #default="scope">
                    <div class="paymentMethod-select edit-item" v-if="currentEditId === scope.row.billId" @click.capture="handleFocus()">
                        <Select
                            ref="payMethodRef"
                            v-model="scope.row.pmethodId"
                            :teleported="true"
                            :fit-input-width="true"
                            :filterable="true"
                            :immediately-blur="false"
                            placeholder=" "
                            :clearable="true"
                            :bottom-html="checkPermission(['expensebillsettings-canedit']) ? newSelectOptionHtml : ''"
                            @bottom-click="newSelectOption('payMethod')"
                            @keyup-enter="handlePayMethodEnter"
                            @blur="handleBlur(scope.row)"
                            :filter-method="PayMethodFilterMethod"
                        >
                            <Option 
                                v-for="item in showPayMethods" 
                                :key="item.pmId" 
                                :value="item.pmId" 
                                :label="item.pmName" 
                            />
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>

        <template #deductibleTax>
            <el-table-column
                label="可抵扣税额"
                min-width="120"
                align="right"
                header-align="right"
                prop="tax"
                :formatter="(row:any,col:any,v:any)=>formatMoney(v)"
                :width="getColumnWidth(setModule, 'tax')"
            >
                <template #default="scope">
                    <div class="tax edit-item" v-if="currentEditId === scope.row.billId">
                        <el-input
                            ref="taxInputRef"
                            v-model="scope.row.tax"
                            @focus="handleFocus()"
                            @keydown.enter="handleTaxEnter"
                            v-decimal-limit
                            @input="handleBeginAmountInput($event, scope.row, 'tax')"
                            @wheel="handleWheel"
                            @blur="handleBlur(scope.row)"
                            @keydown="preventArrowKeyEvent"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>

        <template #amountIncludingTax>
            <el-table-column
                label="单据金额（含税）"
                min-width="120"
                align="right"
                header-align="right"
                prop="totalAmount"
                :formatter="(row:any,col:any,v:any)=>formatMoney(v)"
                :width="getColumnWidth(setModule, 'totalAmount')"
            >
                <template #default="scope">
                    <div class="total-mount edit-item" v-if="currentEditId === scope.row.billId">
                        <el-input
                            v-model="scope.row.totalAmount"
                            ref="amountInputRef"
                            @focus="handleFocus()"
                            @input="handleBeginAmountInput($event, scope.row, 'totalAmount')"
                            @keydown.enter="handleAmountEnter"
                            v-decimal-limit
                            @wheel="handleWheel"
                            @keydown="preventArrowKeyEvent"
                            @blur="handleBlur(scope.row)"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #note>
            <el-table-column 
                label="备注" 
                min-width="160" 
                align="left" 
                header-align="left" 
                prop="note"
                :width="getColumnWidth(setModule, 'note')"
            >
                <template #default="scope">
                    <div class="note edit-item" v-if="currentEditId === scope.row.billId">
                        <el-input
                            ref="noteRef"
                            v-if="noteTextareaShow"
                            :autosize="{ minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            resize="none"
                            v-model="scope.row.note"
                            @focus="handleFocus('noteRef')"
                            @blur="handleBlur(scope.row, 'note')"
                            @keydown.enter="handleNoteEnter"
                            @input="handleInput(scope.row.note, '备注')"
                        />
                        <Tooltip v-else :content="scope.row.note" :isInput="true" :teleported="true">
                            <el-input
                                ref="noteRef"
                                v-model="scope.row.note"
                                @focus="handleFocus('noteRef3')"
                                @keydown.enter="handleNoteEnter"
                            />
                        </Tooltip>
                    </div>
                </template>
            </el-table-column>
        </template>

        <template #voucherNumber>
            <el-table-column 
                label="关联凭证" 
                min-width="150" 
                align="left" 
                header-align="left" 
                prop="v_num2"
                :width="getColumnWidth(setModule, 'v_num2')"
            >
                <template #default="scope">
                    <span
                        :class="[checkVoucherPermission(scope.row.v_num2) ? 'link' : 'cursor-default', 'show']"
                        style="display: flex; align-items: center"
                        @click.stop="checkVoucherPermission(scope.row.v_num2) ? routerTo(scope.row) : ''"
                    >
                        {{ scope.row.v_num2 }}
                    </span>
                </template>
            </el-table-column>
        </template>

        <template #operation>
            <el-table-column 
                label="操作" 
                min-width="220" 
                align="left" 
                header-align="left"
                prop="operation"
                :width="getColumnWidth(setModule, 'operation')"
            >
                <template #default="scope">
                    <div v-if="scope.row.billId > 0" class="handle">
                        <a class="link" @click.stop="insertNewRow(scope.row)" v-if="checkPermission(['expensebill-canedit'])"> 插入 </a>
                        <a v-if="checkPermission(['expensebill-canedit'])" class="link" @click.stop="copyRow(scope.row)"> 复制 </a>
                        <a class="link" @click.stop="deleteRow(scope.row)" v-if="checkPermission(['expensebill-candelete'])"> 删除 </a>
                    </div>
                    <div v-else-if="scope.row.billId === -2" class="handle">
                        <a class="link" @click.stop="deleteCustomRow(scope.$index)"> 删除 </a>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #tranNo>
            <el-table-column 
                label="单据编号" 
                min-width="180" 
                align="left" 
                header-align="left" 
                prop="billNo" 
                :resizable="false"
            >
                <template #default="scope">
                    <div class="bill-no edit-item" v-if="currentEditId === scope.row.billId">
                        <el-input
                            v-model="scope.row.billNo"
                            ref="billNoInputRef"
                            :formatter="(val:string)=>val.replace(/[^a-zA-Z0-9]/g, '')"
                            @focus="handleFocus()"
                            @keydown.enter="handleBillNoEnter"
                            maxlength="30"
                            @wheel="handleWheel"
                            @blur="handleBlur(scope.row)"
                            @keydown="preventArrowKeyEvent"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
    </Table>
    <AddAAEntryDialog ref="AddAAEntryDialogRef" :autoAddName="autoAddName" @save="handleAddAssistingAccounting" @close="newEditDialogOpen = false"> </AddAAEntryDialog>
    <AddBillTypeOrPayMethodDialog
        ref="AddBillTypeOrPayMethodDialogRef"
        :billData="basicTypeList"
        editDialogType="添加"
        @reloadBillTypeList="handleAddBillType"
        @close="newEditDialogOpen = false"
    ></AddBillTypeOrPayMethodDialog>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch, watchEffect } from "vue";
import { formatMoney } from "@/util/format";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { formatDate } from "@/views/Cashier/CashOrDepositJournal/utils";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { textareaBottom } from "@/views/FixedAssets/FixedAssets/utils";
import type { ITableItem, BasicTypeList } from "../types";
import { ITableItemClass } from "../types";
import { showAllColumns, noShowAllColumns } from "../utils";
import Tooltip from "@/components/Tooltip/index.vue";
import { ElDatePicker, ElInput } from "element-plus";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import AddAAEntryDialog from "@/views/Cashier/CashOrDepositJournal/components/AddAAEntryDialog.vue";
import { CashAAType } from "@/views/Cashier/CashOrDepositJournal/types";
import Option from "@/components/Option/index.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import AddBillTypeOrPayMethodDialog from "./AddBillTypeOrPayMethodDialog.vue";
import { cloneDeep } from "lodash";
import dayjs from "dayjs";
import { handleExpiredCheckData, ExpiredCheckModuleEnum} from "@/util/proUtils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "InvoiceExpenseBill";
const props = defineProps<{
    loading: boolean;
    showAll: boolean;
    basicTypeList: BasicTypeList;
    checkDate: string;
    searchStart: string;
    searchEnd: string;
}>();
const defaultMathod = computed(() => props.basicTypeList.payMethods.find((item) => item.isDefault === 1)?.pmId || 1);
const AddAAEntryDialogRef = ref<InstanceType<typeof AddAAEntryDialog>>();
const AddBillTypeOrPayMethodDialogRef = ref<InstanceType<typeof AddBillTypeOrPayMethodDialog>>();
const emit = defineEmits<{
    (e: "update:loading", data: boolean): void;
    (e: "reloadTableData"): void;
    (e: "reloadBillTypeList", cate: string, no: string, name: string): void;
    (e: "handle-selection-change", data: ITableItem[]): void;
    (e: "change-date", startDate: string, endDate: string, row: ITableItem): void;
}>();
const currentEditId = ref(-1);

const tableData = ref<ITableItem[]>([]);
const originalDate = ref<ITableItem[]>([]);
const initTableData = (data: ITableItem[], total: number, row?: ITableItem) => {
    tableData.value = cloneDeep(data).map((item) => {
        return {
            ...item,
            originTax: Number(item.tax),
            originTotalAmount: Number(item.totalAmount),
        };
    });
    originalDate.value = cloneDeep(data);
    let insertRow = tableData.value.find((item) => item.rowType === 2);
    if (insertRow) {
        insertRow.billTypeId = null;
        insertRow.depId = null;
        insertRow.payeeId = null;
        insertRow.tax = "";
        insertRow.totalAmount = "";
        insertRow.originTax = 0;
        insertRow.originTotalAmount = 0;
    }

    let newInsertRow = tableData.value.findLastIndex((item) => item.billId === -1 && item.rowType === 2);
    row && tableData.value.splice(newInsertRow, 1, row);
    nextTick().then(() => {
        if (!checkPermission(["expensebill-canedit"])) {
            currentEditId.value = -10;
        } else {
            currentEditId.value = row ? row.billId : -1;
        }
    });

    paginationData.total = total;
};
let payeeList = computed(() =>
    useAssistingAccountingStore()
        .getAssistingAccountingByTypeModel(10003)
        .filter((item) => item.aaeid > 0)
);
let departmentList = computed(() => useAssistingAccountingStore().departmentList);

const datePickerRef = ref();
const descriptionInputRef = ref<InstanceType<typeof ElInput>>();
const billTypeSelectRef = ref<InstanceType<typeof Select>>();
const departmentSelectRef = ref<InstanceType<typeof Select>>();
const payeeRef = ref<InstanceType<typeof Select>>();
const payMethodRef = ref<InstanceType<typeof Select>>();
const taxInputRef = ref<InstanceType<typeof ElInput>>();
const amountInputRef = ref<InstanceType<typeof ElInput>>();
const noteRef = ref<InstanceType<typeof ElInput>>();
const billNoInputRef = ref<InstanceType<typeof ElInput>>();

function checkVoucherPermission(voucherNum: string) {
    return checkPermission(["voucher-canview"]) && voucherNum;
}
function handleWheel(e: WheelEvent) {
    e.preventDefault();
    e.stopPropagation();
}
function preventArrowKeyEvent(event: any) {
    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        event.preventDefault();
    }
}

// 回车操作
const descriptionEnter = () => {
    descriptionInputRef.value?.blur();
    billTypeSelectRef.value?.focus();
    isEdit = true;
};

const handleBillTypeTypeEnter = () => {
    billTypeSelectRef.value?.blur();
    if (props.showAll) {
        departmentSelectRef.value?.focus();
    } else {
        payeeRef.value?.focus();
    }
    isEdit = true;
};
const handleDepartmentEnter = () => {
    departmentSelectRef.value?.blur();
    payeeRef.value?.focus();
    isEdit = true;
};
const handlePayeeEnter = () => {
    payeeRef.value?.blur();
    payMethodRef.value?.focus();
    isEdit = true;
};
const handlePayMethodEnter = () => {
    payMethodRef.value?.blur();
    taxInputRef.value?.focus();
    isEdit = true;
};
const handleTaxEnter = () => {
    taxInputRef.value?.blur();
    amountInputRef.value?.focus();
    isEdit = true;
};
const handleAmountEnter = () => {
    amountInputRef.value?.blur();
    if (props.showAll) {
        noteRef.value?.focus();
    } else {
        billNoInputRef.value?.focus();
    }
    isEdit = true;
};
const handleNoteEnter = () => {
    noteRef.value?.blur();
    billNoInputRef.value?.focus();
    isEdit = true;
};
const handleBillNoEnter = () => {
    billNoInputRef.value?.blur();
    isEdit = false;
};

const elDatePickerRef = ref();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const setSelectable = (row: ITableItem): boolean => {
    return row.billId > 0 && Boolean(row.billDesc);
};

let newEditDialogOpen = ref(false);
const handleAddBillType = (cate: string, no: string, name: string) => {
    emit("reloadBillTypeList", cate, no, name);
    newEditDialogOpen.value = false;
};
const newSelectOptionHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">
            +点击添加
        </a>
    </div>`;
const newSelectOption = (select: string) => {
    allowBlur = false;
    switch (select) {
        case "billTypes":
            AddBillTypeOrPayMethodDialogRef.value?.initForm(null, "billTypes", autoAddName.value);
            break;
        case "payMethod":
            AddBillTypeOrPayMethodDialogRef.value?.initForm(null, "payMethods", autoAddName.value);
            break;
        case "department":
            AddAAEntryDialogRef.value?.showAADialog(10004);
            break;
        case "payee":
            AddAAEntryDialogRef.value?.showAADialog(10003);
            break;
    }
    newEditDialogOpen.value = true;
};
const handleAddAssistingAccounting = (aaType: number, code: string, name: string) => {
    newEditDialogOpen.value = false;
    if (aaType === CashAAType.Department) {
        useAssistingAccountingStore()
            .getDepartment()
            .then(() => {
                const list = departmentList.value;
                const item = list.find((item) => item.aanum === code && item.aaname === name);
                let currentRow = tableData.value.find((item) => item.billId === currentEditId.value);
                currentRow && (currentRow.depId = item?.aaeid || "");
            });
        useAssistingAccountingStore().getAssistingAccounting();
        return;
    }
    useAssistingAccountingStore()
        .getAssistingAccounting()
        .then(() => {
            const list = payeeList.value;
            const item = list.find((item) => item.aanum === code && item.aaname === name);
            let currentRow = tableData.value.find((item) => item.billId === currentEditId.value);
            currentRow && (currentRow.payeeId = item?.aaeid || "");
        });
};
const setcurrentBillTypeValue = (cate: string, targetId: number) => {
    let currentRow = tableData.value.find((item) => item.billId === currentEditId.value);
    currentRow && (cate === "billTypes" ? (currentRow.billTypeId = targetId) : (currentRow.pmethodId = targetId));
};
const columns = computed(() => {
    return props.showAll ? showAllColumns : noShowAllColumns;
});
watch(
    () => props.showAll,
    () => {
        // 解决编辑行列缺失
        let oldEditId = currentEditId.value;
        currentEditId.value = -10;
        nextTick().then(() => {
            tableRef.value?.getTable().doLayout();
            currentEditId.value = oldEditId;
        });
    }
);
const loading = computed({
    get() {
        return props.loading;
    },
    set(val) {
        emit("update:loading", val);
    },
});
const handleRowClick = (row: ITableItem, column?: any) => {
    if (row.billId === 0 || row.billId === currentEditId.value) return;
    if (!checkPermission(["expensebill-canedit"])) {
        currentEditId.value = -10;
        return;
    }

    const columnLabel: string = column === "amount" ? "amount" : column === "description" ? "费用事由" : column?.label ? column?.label : "";
    if (columnLabel === "操作" || (column && column.type) === "selection") return;

    if (formatDate(row.billDate) && formatDate(row.billDate) < props.checkDate && (row.v_num2 || row.v_id)) {
        ElNotify({ type: "warning", message: "当前单据生成的凭证所在期间已结账，请反结账删除凭证后再修改哦" });
        return;
    }

    if (!checkPermission(["expensebill-cancreatevoucher"]) && (row.v_num2 || row.v_id)) {
        ElNotify({ type: "warning", message: "修改已生成凭证的费用单据数据，需要删除凭证或拥有凭证编辑权限哦~" });
        return;
    }
    if (row.v_num2 || row.v_id) {
        ElNotify({ type: "warning", message: "修改已生成凭证的费用单据数据，需要先删除凭证哦~" });
        return;
    }
    if (
        currentEditId.value &&
        currentEditId.value !== -1 &&
        tableData.value.findIndex((item) => item.billId === currentEditId.value) > -1
    ) {
        if (isEdit) {
            isEdit = false;
        } else {
            let editRow = tableData.value.find((item) => item.billId === currentEditId.value);
            editRow && handleSubmit(editRow);
        }
    } else {
        if (row.depId === 0) {
            row.depId = null;
        }
        if (row.payeeId === 0) {
            row.payeeId = null;
        }
        if (!row.payeeId && row.payeeName) {
            row.payeeId = row.payeeName;
        }
        row.totalAmount = Number(row.totalAmount) !== 0 ? Number(row.totalAmount).toFixed(2) : "";
        row.tax = Number(row.tax) !== 0 ? Number(row.tax).toFixed(2) : "";
        currentEditId.value = row.billId;
        setRowInEditStatus(row, columnLabel);
    }
};

function setRowInEditStatus(row: ITableItem, columnLabel?: string) {
    nextTick(() => {
        if (columnLabel === "费用事由") {
            descriptionInputRef.value?.focus();
        } else if (columnLabel === "费用类型") {
            billTypeSelectRef.value?.focus();
        } else if (columnLabel === "部门") {
            departmentSelectRef.value?.focus();
        } else if (columnLabel === "收款人") {
            payeeRef.value?.focus();
        } else if (columnLabel === "结算方式") {
            payMethodRef.value?.focus();
        } else if (columnLabel === "可抵扣税额") {
            taxInputRef.value?.focus();
        } else if (columnLabel === "单据金额（含税）") {
            amountInputRef.value?.focus();
        } else if (columnLabel === "备注") {
            noteRef.value?.focus();
        } else if (columnLabel === "单据编号") {
            billNoInputRef.value?.focus();
        }
    });
}
const handleBeginAmountInput = (val: any, row: any, col: string) => {
    row[col] =
        val
            .replace(/[^-\d^.]+/g, "")
            .replace(/^-?(0+)(\d)/, "-$2")
            .replace(/^\./, "0.")
            .match(/^-?\d*(\.?\d{0,2})/g)[0] || "";
};

const handleDateChange = (date: string, row: ITableItem) => {
    let maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 5);
    const value = new Date(date);
    const item =
        (originalDate.value.find((item) => item.billId === currentEditId.value) as ITableItem) ||
        new ITableItemClass(-2, defaultMathod.value);

    if (date === item.billDate) return;

    if (value > new Date(maxDate)) {
        ElNotify({ type: "warning", message: "最晚只能录入当前日期五年内的数据" });
        row.billDate = item.billDate;

        return false;
    } else {
        if (value < new Date(props.searchStart) || value > new Date(props.searchEnd)) {
            ElConfirm("您录入的日期是" + date + "，系统将跳转到该日期的归属期间，请问是否确定？").then((r: any) => {
                if (r) {
                    ChangeDate(date, row);
                } else {
                    row.billDate = item.billDate;

                    return false;
                }
            });
        } else {
            nextTick().then(() => {
                descriptionInputRef.value?.focus();
            });
        }
    }
};

const ChangeDate = (date: string, row: ITableItem) => {
    // date 格式为 YYYY-MM-DD
    let time: string[] = date.split("-");
    const _s = time[0] + "-" + time[1] + "-01";
    const _e = date;
    emit("change-date", _s, _e, row);
};

let allowBlur = true;
let isEdit = false;
const tableRef = ref();

const handleSelectionChange = (val: ITableItem[]) => {
    emit("handle-selection-change", val);
};
const handleScroll = () => {
    isEdit = true;
};
const descriptionTextareaShow = ref(false);
const noteTextareaShow = ref(false);

const currentNeedSelectInput = ref("");
const handleFocus = (nameRef?: string) => {
    allowBlur = false;

    textareaBottom(tableRef);
    switch (nameRef) {
        case "descriptionInputRef1":
            descriptionTextareaShow.value = true;
            break;
        case "noteRef3":
            noteTextareaShow.value = true;
            break;
    }
    nextTick(() => {
        if (nameRef) {
            getTextareaFocus(nameRef);
        }
    });
    currentNeedSelectInput.value = nameRef || "";
    setTimeout(() => {
        if (nameRef === "descriptionInputRef" && "descriptionInputRef" === currentNeedSelectInput.value) {
            descriptionInputRef.value?.select();
        } else if (nameRef === "noteRef" && "noteRef" === currentNeedSelectInput.value) {
            noteRef.value?.select();
        }
    }, 50);
};
const getTextareaFocus = (nameRef: string) => {
    switch (nameRef) {
        case "descriptionInputRef1":
            descriptionInputRef.value?.focus();
            break;
        case "noteRef3":
            noteRef.value?.focus();
            break;
    }
};

const datePopperShow = ref(false);
const visibleChange = (val: boolean) => {
    datePopperShow.value = val;
};
const handleDateFocus = () => {
    currentNeedSelectInput.value = "";
    if (datePopperShow.value) {
        nextTick().then(() => {
            elDatePickerRef.value?.handleClose();
        });
    } else {
        nextTick().then(() => {
            elDatePickerRef.value?.handleOpen();
        });
    }
};
const checkoutInsertRow = (row: ITableItem) => {
    return !row.billTypeId && !row.billDesc && row.billNo && !row.totalAmount && !row.tax && !row.depId && !row.payeeId && !row.note;
};
const inputBlurToSubmit = (row: ITableItem) => {
    // 正在编辑不可再次提交

    setTimeout(() => {
        if (newEditDialogOpen.value) return;
        if (!allowBlur) return;
        if (isEdit) return;
        if (row.billId === -1 && checkoutInsertRow(row)) return;
        handleSubmit(row);
    }, 250);
};
const handleBlur = (row: ITableItem, column?: string) => {
    allowBlur = true;
    isEdit = false;
    switch (column) {
        case "description":
            descriptionTextareaShow.value = false;
            break;
        case "note":
            noteTextareaShow.value = false;
            break;
    }
    inputBlurToSubmit(row);
};
const handleInput = (description: string, name: string) => {
    if (description.length === 256) {
        ElNotify({ type: "warning", message: "亲，" + name + "不能超过256个字符！" });
    }
};
let isSubmit = false;
const handleSubmit = (row: ITableItem) => {
    if (!row.billDate && row.billId > 0) {
        ElNotify({ type: "warning", message: "日期不能为空" });
        return;
    }
    if (!row.billDesc.trim()) {
        ElNotify({ type: "warning", message: "亲，请录入费用事由" });
        return;
    }
    if (!row.billTypeId) {
        ElNotify({ type: "warning", message: "亲，请选择费用类型" });
        return;
    }

    if (!row.pmethodId) {
        ElNotify({ type: "warning", message: "亲，请选择结算方式" });
        return;
    }
    if (!row.totalAmount) {
        ElNotify({ type: "warning", message: "亲，请录入单据金额" });
        return;
    }
    if (!row.billNo) {
        ElNotify({ type: "warning", message: "请输入单据编号" });
        return;
    }
    if (isSubmit) return;
    if (row.payeeId && payeeList.value.findIndex((item) => item.aaeid === row.payeeId) === -1) {
        row.payeeName = String(row.payeeId);
    }
    let originMoney = {
        tax: row.originTax,
        totalAmount: row.originTotalAmount,
    };
    delete row.originTax;
    delete row.originTotalAmount;
    isSubmit = true;
    request({ url: "/api/ExpenseBill", method: row.billId > 0 ? "put" : "post", data: row })
        .then((res: IResponseModel<ITableItem | boolean>) => {
            isSubmit = false;
            if (res.state == 1000 && res.data) {
                ElNotify({ type: "success", message: "保存成功" });
                if(row.billId <= 0){
                    handleExpiredCheckData(ExpiredCheckModuleEnum.Invoice);
                }

                let resData = res.data as ITableItem;
                let data: ITableItem = {
                    ...resData,
                    payeeName: payeeList.value.find((item: any) => item.aaeid === resData.payeeId)?.aaname || row.payeeName,
                    billTypeName:
                        props.basicTypeList.billTypes.find((item: any) => item.billTypeId === resData.billTypeId)?.billTypeName ||
                        row.billTypeName,
                    pmethodName:
                        props.basicTypeList.payMethods.find((item: any) => item.pmId === resData.pmethodId)?.pmName || row.pmethodName,
                    depName: departmentList.value.find((item: any) => item.aaeid === resData.depId)?.aaname || row.depName,
                    originTax: Number(resData.tax),
                    originTotalAmount: Number(resData.totalAmount),
                };
                tableData.value.splice(
                    tableData.value.findIndex((item) => item.billId === row.billId),
                    1,
                    data
                );
                // 是否有合计行
                let isExitTotalRow = tableData.value.findLastIndex((item) => item.billId === 0 && item.rowType === 3);

                if (isExitTotalRow > -1) {
                    let totalRow = tableData.value[isExitTotalRow] as ITableItem;
                    totalRow.totalAmount = totalRow.totalAmount - (originMoney?.totalAmount || 0) + Number(data.totalAmount);
                    totalRow.tax = totalRow.tax - (originMoney?.tax || 0) + Number(data.tax);
                    totalRow.amount = Number(totalRow.totalAmount - totalRow.tax).toFixed(2);
                }
                currentEditId.value = -10;
                // 保留选中状态
                let selectRowList = tableRef.value.getTable().getSelectionRows();
                let selectRowHasEditRowIndex = selectRowList.findIndex((item: ITableItem) => item.billId === data.billId);
                if (selectRowHasEditRowIndex > -1) {
                    tableRef.value.getTable().toggleRowSelection(selectRowList[selectRowHasEditRowIndex], false);
                    tableRef.value.getTable().toggleRowSelection(data, true);
                }
                if (row.rowType === 2) {
                    tableData.value.splice(tableData.value.length - 1, 0, new ITableItemClass(-1, defaultMathod.value, row.billDate, 2));
                    currentEditId.value = -1;
                    nextTick(() => {
                        descriptionInputRef.value?.focus();
                    });
                }
            } else {
                ElNotify({ type: "warning", message: res.msg || "保存失败，请刷新页面重试" });
                currentEditId.value = row.billId;
            }
        })
        .catch(() => {
            isSubmit = false;
            row.billId = Math.random();
            ElNotify({ type: "warning", message: "保存失败，请刷新页面重试" });
        });
};
// 插入或复制=-2
const insertNewRow = (row: ITableItem) => {
    // 已经存在编辑行了，应该阻止
    let editRow = tableData.value.find((item) => item.billId === -2);
    if (editRow) {
        allowBlur = false;
        handleSubmit(editRow);
    } else {
        // 重置新增行
        let newInsertRow = tableData.value.findLastIndex((item) => item.billId === -1 && item.rowType === 2);
        if (newInsertRow > -1) {
            tableData.value[newInsertRow] = new ITableItemClass(-1, defaultMathod.value, tableData.value[newInsertRow].billDate, 2);
        }
        let position = tableData.value.findIndex((v) => v.billId === row.billId);
        tableData.value.splice(position, 0, new ITableItemClass(-2, defaultMathod.value, row.billDate));
        paginationData.total++;
        currentEditId.value = -2;
        nextTick(() => {
            descriptionInputRef.value?.focus();
        });
    }
};
let click = false;
const generateBillNo = () => {
    const targetString = "FY" + dayjs(new Date()).format("YYYYMMDDHHmmss");
    const targetIndex = tableData.value.findLastIndex((item) => item.billNo.includes(targetString));
    if (targetIndex === -1) {
        return targetString + "000001";
    } else {
        const lastBillNo = tableData.value[targetIndex].billNo;
        const lastNumber = parseInt(lastBillNo.substr(-6)); // 获取最后六位数字
        const newNumber = lastNumber + 1;
        return targetString + newNumber.toString().padStart(6, "0");
    }
};
const copyRow = (row: ITableItem) => {
    if (click) return;
    click = true;
    // 已经存在编辑行了，应该阻止
    let editRow = tableData.value.find((item) => item.billId === -2);
    if (editRow) {
        allowBlur = false;
        handleSubmit(editRow);
    } else {
        // 重置新增行,单据编号也跟着更新
        const findLastIndex = (arr: any[], predicate: (item: any) => boolean) => {
        for(let i = arr.length - 1; i >= 0; i--) {
            if(predicate(arr[i])) {
            return i;
            }
        }
        return -1;
        }
        let newInsertRow = findLastIndex(tableData.value, 
            item => item.billId === -1 && item.rowType === 2
        );
        if (newInsertRow > -1) {
            tableData.value[newInsertRow] = new ITableItemClass(-1, defaultMathod.value, tableData.value[newInsertRow].billDate, 2, tableData.value[newInsertRow].billNo);
        }
        let position = tableData.value.findIndex((v) => v.billId === row.billId);
        const billNo = generateBillNo();
        tableData.value.splice(position + 1, 0, {
            ...row,
            billId: -2,
            billNo,
            payeeId: row.payeeId ? row.payeeId : "",
            depId: row.depId ? row.depId : "",
            v_num: "",
            v_num2: "",
            v_id: 0,
            p_id: 0,
            tax: Number(row.tax) !== 0 ? Number(row.tax).toFixed(2) : "",
            totalAmount: Number(row.totalAmount) !== 0 ? Number(row.totalAmount).toFixed(2) : "",
            originTax: 0,
            originTotalAmount: 0,
            billSource: "",
        });
        paginationData.total++;

        currentEditId.value = -2;
        nextTick(() => {
            billNoInputRef.value?.focus();
            allowBlur = false;
        });
    }
    let timer = setTimeout(() => {
        click = false;
        clearTimeout(timer);
    }, 500);
};

const deleteRow = (row: any) => {
    if (row.billId < 0) {
        tableData.value.splice(
            tableData.value.findIndex((item) => item.billId === row.billId),
            1
        );
        return;
    }
    // 关联单据数据不能修改
    if (row.v_num) {
        ElNotify({ type: "warning", message: "单据数据已生成凭证，请先删除凭证！" });
        return false;
    }
    ElAlert({ message: "确定删除该行数据吗？" }).then((r: boolean) => {
        if (r) {
            request({ url: "/api/ExpenseBill", method: "delete", data: { billid: row.billId, billno: row.billNo } })
                .then((res: any) => {
                    if (res.state === 1000 && res.data) {
                        ElNotify({ type: "success", message: "删除成功" });
                        emit("reloadTableData");
                    } else {
                        ElNotify({ type: "warning", message: "删除失败，请刷新页面重试" });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "删除失败，请刷新页面重试" });
                });
        }
    });
};
const deleteCustomRow = (index: number) => {
    allowBlur = false;
    tableData.value.splice(index, 1);
    paginationData.total--;
    currentEditId.value = -10;
};

const routerTo = (row: ITableItem) => {
    const from = "expenseReceipts";
    const params = { pid: row.p_id, vid: row.v_id, fcode: "expensebill-cancreatevoucher", from };
    globalWindowOpenPage("/Voucher/VoucherPage?" + getUrlSearchParams(params), "查看凭证");
};
const setpaginationData = () => {
    paginationData.currentPage = 1;
};
const preventEditTableSubmit = () => {
    allowBlur = false;
};
const newBillIsEdit = computed(() => Boolean(tableData.value.find((item) => item.billId === currentEditId.value)?.billDesc.trim()));
defineExpose({
    initTableData,
    paginationData,
    setpaginationData,
    preventEditTableSubmit,
    newBillIsEdit,
    setcurrentBillTypeValue,
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    emit("reloadTableData");
});

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//模糊搜索
const showBillTypes = ref<Array<any>>([]);
const showdepartmentList = ref<Array<any>>([]);
const showPayeeList = ref<Array<any>>([]);
const showPayMethods = ref<Array<any>>([]);
watchEffect(() => {
    showBillTypes.value = JSON.parse(JSON.stringify(props.basicTypeList.billTypes));
    showPayMethods.value = JSON.parse(JSON.stringify(props.basicTypeList.payMethods));
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
    showPayeeList.value = JSON.parse(JSON.stringify(payeeList.value));
});
function billTypeFilterMethod(value: string) {
    showBillTypes.value = commonFilterMethod(value, props.basicTypeList.billTypes, 'billTypeName');
    autoAddName.value = showBillTypes.value.length === 0 ? value.trim() : ""; 
}
function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value , 'aaname');
    autoAddName.value = showdepartmentList.value.length === 0 ? value.trim() : ""; 
}
function payeeFilterMethod(value: string) {
    showPayeeList.value = commonFilterMethod(value, payeeList.value , 'aaname');
    autoAddName.value = showPayeeList.value.length === 0 ? value.trim() : ""; 
}
function PayMethodFilterMethod(value: string) {
    showPayMethods.value = commonFilterMethod(value, props.basicTypeList.payMethods , 'pmName');
    autoAddName.value = showPayMethods.value.length === 0 ? value.trim() : ""; 
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
:deep(.el-table .el-table__cell) {
    z-index: auto;
}
:deep(.el-textarea__inner) {
    position: absolute;
    top: -15px;
    z-index: 99;
}
:deep(.span_wrap) {
    flex: 1;
}

.format-edit-item(@width, @fontSize) {
    .detail-el-input(@width, 32px);
    :deep(.el-input) {
        .el-input__wrapper {
            padding: 0 4px;
            input.el-input__inner {
                border: none;
                font-size: @fontSize;
                color: var(--font-color);
            }
        }
    }
}
.my-data-picker {
    :deep(.el-date-editor--date) {
        height: 32px;
        width: 132px;
        box-sizing: border-box;
        .el-input__wrapper {
            padding: 0;
            padding-left: 8px;
            .el-input__inner {
                border: none;
            }
            .el-input__prefix {
                position: absolute;
                top: 2px;
                bottom: 2px;
                right: 3px;
                width: 18px;
                height: 86%;
                background-color: #fff;
                padding-left: 2px;
            }
        }
    }
}
.handle {
    width: calc(100% - 2px);
    .link {
        display: inline-block;
    }
    .link.disabled {
        color: gray;
        &:hover {
            text-decoration: underline;
        }
    }
}
.none {
    opacity: 0;
    display: none !important;
}

.edit-item.total-mount,
.edit-item.tax {
    :deep(.el-input) {
        .el-input__wrapper {
            padding: 0 4px;
        }
        .el-input__inner {
            text-align: right;
            &:focus {
                text-align: left;
            }
        }
    }
}
& :deep(.el-table) {
    height: 100%;
    .el-table__header {
        .cell {
            line-height: 36px;
        }
    }

    // 用于隐藏表格被禁用的选择框
    .el-table__row {
        td:first-child {
            .el-checkbox.is-disabled {
                display: none;
            }
        }
    }

    .el-table__body {
        .el-table__cell {
            &.journal-all-choose {
                background-color: var(--table-hover-color);
            }
            &.select-box {
                .cell {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
            .cell {
                width: 100%;
                height: 34px;
                line-height: 30px;
                padding: 2px 4px;
                position: relative;
                & > span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                & > span,
                & > a,
                & > .handle {
                    display: inline-block;
                    width: calc(100% - 2px);
                    height: calc(100% - 2px);
                    line-height: 34px;
                }
            }
            &.is-center {
                .cell {
                    justify-content: center;
                }
            }
        }
    }

    .el-table__footer {
        .el-table__cell {
            background-color: var(--white);
            border-bottom: none;
            border-top: 1px solid var(--border-color);
            padding: 0;
            height: 37px;
            .cell {
                padding: 0 4px;
                font-size: 12px;
            }
        }
    }
}

.normal-table {
    :deep(.el-table) {
        .el-scrollbar__view {
            min-height: 400px;
        }
    }
}
.edit-item {
    position: absolute;
    top: 2px;
    height: calc(100% - 4px);
    width: calc(100% - 8px);
    display: flex;
    justify-content: center;
    align-items: center;
    .detail-placehoder-color(var(--font-color));
    .format-edit-item(100%, 12px);
    &.oppositeParty {
        :deep(.el-textarea__inner) {
            position: absolute;
            top: -30px;
        }
        .icon {
            position: absolute;
            top: 50%;
            right: 1px;
            bottom: 4px;
            background: url("@/assets/Icons/book.png") no-repeat center #fff;
            cursor: pointer;
            width: 26px;
            height: 26px;
            z-index: 100;
            transform: translateY(-50%);
        }
        :deep(.el-autocomplete) {
            width: 100%;
        }
    }
    & > :deep(.el-select) {
        width: 100%;
        height: 32px;
    }
    & :deep(.el-input) {
        &.is-focus {
            .el-input__wrapper {
                box-shadow: 0 0 0 1px var(--border-color) inset !important;
                &.is-focus {
                    box-shadow: 0 0 0 1px var(--border-color) inset !important;
                }
            }
        }
        .el-input__wrapper {
            &.is-focus {
                box-shadow: 0 0 0 1px var(--border-color) inset;
            }
        }
    }
    &.cd-date {
        .el-input__suffix {
            position: absolute;
            top: 0;
            right: -5px;
        }
    }
}

body[erp] {
    .edit-item {
        position: absolute;
        top: 10px;
        left: 4px;
        height: calc(100% - 20px);
        .format-edit-item(100%, 14px);
        :deep(.el-select) {
            .el-select__input {
                border: none;
            }
        }
    }
}

.voucher-tip-box {
    .box-main {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        .tip {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &.erp {
        .box-main {
            border: none;
            text-align: left;
            padding-bottom: 0;
            .tip {
                justify-content: flex-start;
            }
        }
        .buttons {
            display: flex;
            align-items: center;
            flex-direction: row-reverse;
            padding: 20px 10px;
            border-top: 1px solid var(--border-color);
            margin-top: 20px;
            .button {
                margin: 0 10px;
            }
        }
    }
}

.el-select-dropdown__item {
    font-size: var(--table-body-font-size);
    &.hover {
        background-color: var(--main-color) !important;
        color: var(--white);
    }
}
.link {
    opacity: 0.9;
}
:deep(input[type="number"]) {
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
    }
    -moz-appearance: textfield;
    &:focus,
    &:hover {
        -moz-appearance: number-input;
    }
}
</style>
<style lang="less">
.el-popper.custom-pover {
    z-index: 1900 !important;
}
</style>
