<template>
  <el-form
    :model="formData"
    label-suffix="："
    :label-position="labelPosition">
    <el-form-item label="财报未结账取数">
      <el-radio-group v-model="formData.dataSource">
        <el-radio :value="0">未结账生成报表</el-radio>
        <el-radio :value="1">取最新一期已结账的财务数据</el-radio>
        <el-radio :value="2">生成0数据</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="报表重分类">
      <div class="checkbox-group">
        <div class="checkbox">
          <el-checkbox
            label="开启重分类"
            v-model="formData.isClassification"></el-checkbox>
          <img
            class="question-icon"
            src="@/assets/Icons/question.png"
            @click="showClassificationDialog = true" />
        </div>
        <div class="checkbox">
          <el-checkbox
            label="应交税金重分类"
            v-model="formData.isTaxClassification" />
          <Popover
            trigger="click"
            :useSlotContent="true"
            :max-width="160">
            <template #trigger>
              <img
                class="question-icon"
                src="@/assets/Icons/question.png" />
            </template>
            <template #content>
              <div>
                不重分类时：应交税费余额正常展示在应交税费的栏次
                <br />
                重分类时：借方有余额体现在其他流动资产，贷方余额正常显示应交税费栏
              </div>
              <div></div>
            </template>
          </Popover>
        </div>
      </div>
    </el-form-item>
    <el-form-item class="has-question-icon">
      <template #label>
        <span>是否填写现金流量表</span>
        <Popover
          trigger="click"
          :useSlotContent="true"
          :max-width="240">
          <template #trigger>
            <img src="@/assets/Icons/question.png" />
          </template>
          <template #content>
            <div>
              选项"是"：智能填写会填表现金流量表
              <br />
              选项"否"：智能填写不会填表现金流量表，现金流量表0申报
            </div>
            <div></div>
          </template>
        </Popover>
        ：
      </template>
      <el-radio-group v-model="formData.hasCashflowSheet">
        <el-radio :value="true">是</el-radio>
        <el-radio :value="false">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item class="has-question-icon">
      <template #label>
        <span>年初数（上年年末数） 取值来源</span>
        <Popover
          trigger="click"
          :useSlotContent="true"
          :max-width="200">
          <template #trigger>
            <img src="@/assets/Icons/question.png" />
          </template>
          <template #content>
            <div>影响资产负债表年初数（上年年末数）是取【申报表数据（上期已申报）】还是取【财务系统-资产负债表对应列】的数据！</div>
            <div></div>
          </template>
        </Popover>
        ：
      </template>
      <el-radio-group v-model="formData.yearStartBalance">
        <el-radio :value="0">申报表数据（上期已申报）</el-radio>
        <el-radio :value="1">财务数据（财务系统/报表）</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>

  <el-dialog
    title="重分类说明"
    width="730"
    v-model="showClassificationDialog"
    class="custom-confirm">
    <div class="classification-dialog-content dialog-content">
      <div class="dialog-main">
        <p
          v-for="(item, index) in tipsList"
          :key="index"
          :class="['tips', { 'font-16': index < 2 }]">
          {{ item }}
        </p>
      </div>
    </div>
    <template #footer>
      <a
        class="button solid-button"
        type="primary"
        @click="showClassificationDialog = false">
        我知道了
      </a>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
  const formData = defineModel({ type: Object as () => { [key: string]: any }, default: {} })
  const props = withDefaults(
    defineProps<{
      labelPosition?: "left" | "right" | "top"
    }>(),
    {
      labelPosition: "top",
    },
  )

  let showClassificationDialog = ref<boolean>(false)
  const tipsList = [
    "重分类调整只调表不调账，即不用调整凭证分录，只调整往来相关报表项目余额",
    "报表中涉及重分类的往来科目如下:",
    "【应收账款】=应收账款明细科目的借方余额合计+预收账款明细科目借方余额合计-计提的相应坏账准备",
    "【预收账款】=应收账款明细科目的贷方余额合计+预收账款明细科目贷方余额合计",
    "【应付账款】=应付账款明细科目的贷方余额合计+预付账款明细科目贷方余额合计",
    "【预付账款】=应付账款明细科目的借方余额合计+预付账款明细科目借方余额合计",
    "【其他应收账款】=其他应收款明细科目借方余额合计+其他应付款明细科目借方余额合计-计提的相应坏账准备",
    "【其他应付账款】=其他应收款明细科目贷方余额合计+其他应付款明细科目贷方余额合计",
  ]
</script>
<style lang="scss" scoped>
  .el-form {
    .checkbox-group {
      display: flex;
      justify-content: space-around;
      .checkbox {
        display: flex;
        margin-right: 20px;
        // align-items: center;
      }
    }
    :deep(.el-form-item) {
      margin-bottom: 28px;

      .el-form-item__label,
      .el-form-item__content {
        color: var(--dark-grey);
        font-size: var(--h4);
      }

      .el-form-item__content {
        font-weight: 400;
      }
    }

    .has-question-icon {
      :deep(.el-form-item__label) {
        display: flex;
        justify-content: flex-start;

        img {
          margin: 0 4px;
          width: 15px;
          height: 15px;
          transform: translateY(-4px);
        }
      }
    }

    .question-icon {
      cursor: pointer;
      margin-left: 4px;
      width: 15px;
      height: 15px;
    }
  }

  .dialog-content {
    .dialog-main {
      margin-top: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    &.classification-dialog-content {
      .dialog-main {
        margin: 0;
        padding: 15px 10px;
        box-sizing: border-box;
        .tips {
          width: 100%;
          margin: 0;
          text-align: left;
          font-size: 14px;
          line-height: 30px;
          &.font-16 {
            font-size: 16px;
            &:last-child {
              margin-bottom: 5px;
            }
          }
        }
      }
    }
  }
</style>
