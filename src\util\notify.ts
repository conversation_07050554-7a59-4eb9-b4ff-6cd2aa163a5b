import { ElNotification, type NotificationParams, ElMessage, type MessageParams } from "element-plus";
import { sendAppMessage } from "@/util/url";

export const ElNotify = (options?: NotificationParams | undefined | MessageParams) => {
    if (window.isErp) {
        ElMessage?.closeAll();
        const erpconfigDefault: MessageParams = {
            duration: 3000,
            showClose: false,
            dangerouslyUseHTMLString: true,
        };
        // return ElMessage(Object.assign(erpconfigDefault, options));
        Object.assign(erpconfigDefault, options);
        let noticeType = (erpconfigDefault.type as string) ?? "warning";
        if (noticeType === "error") {
            noticeType = "warning";
        }
        return sendAppMessage("提示", (erpconfigDefault.message as string) ?? "", noticeType);
    } else {
        ElNotification?.closeAll();
        const configDefault: NotificationParams = {
            title: "提示",
            duration: 3000,
            position: "bottom-right",
            showClose: true,
            dangerouslyUseHTMLString: true,
        };
        return ElNotification(Object.assign(configDefault, options));
    }
};
