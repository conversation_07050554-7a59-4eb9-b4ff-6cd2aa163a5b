import  type {IResponseModel} from "@/util/service";
import { getCompanyListApi, type ICompanyInfo, type ICompanyList } from "@/api/getCompanyList";

export const getCompanyList = (module:number,queryName: string, cb: any,queryParams:any) => {
    const successFun = (name: string, data: ICompanyInfo[]) => {
        queryParams.data = data;
        queryParams.name = name;
        const nameList = data.reduce((prev: any, item: any) => {
            prev.push({
                value: item.name,
                creditCode: item.creditCode,
                legalPersonName: item.legalPersonName,
            });
            return prev;
        }, []);
        cb(nameList);
        return;
    };
    if (queryParams.name && queryName.includes(queryParams.name) && queryParams.isFromDb && queryParams.data.length == 0){
        cb([]);
        return;
    }
    if (queryName === "") {
        // 无输入时隐藏下拉框,如有需要做到各个页面
        // asNameRef.value.close();
        queryParams.data = [];
        queryParams.isFromDb = false;
        cb([]);
        return;
    }
    if (queryParams.name === queryName) return;
    if (!queryParams.isFromDb && queryParams.data.length < 20 && queryParams.name && queryName.indexOf(queryParams.name) === 0) {
        const endWords = queryName.substring(queryParams.name.length);
        const rdata = [];
        for (let i = 0; i < queryParams.data.length; i++) {
            for (let j = 0; j < endWords.length; j++) {
                if (queryParams.data[i].name.indexOf(endWords[j]) != -1) {
                    rdata.push(queryParams.data[i]);
                    break;
                }
            }
        }
        successFun(queryName, rdata);
        return;
    }
    if (!queryParams.isFromDb && queryParams.data.length >= 20 && queryParams.name && queryName.indexOf(queryParams.name) === 0) {
        const rdata = [];
        for (let i = 0; i < queryParams.data.length; i++) {
            if (queryParams.data[i].name.indexOf(queryName) !== -1) {
                rdata.push(queryParams.data[i]);
            }
        }
        if (rdata.length > 0) {
            successFun(queryName, rdata);
            return;
        }
    }
    getCompanyListApi(module, queryName).then((res: IResponseModel<ICompanyList>) => {
        if (res.state === 1000) {
            queryParams.isFromDb = res.data.isFromDb;
            queryParams.name = queryName;
            if (res.data.companyInfos && res.data.companyInfos.length > 0) {
                const nameList = res.data.companyInfos.reduce((prev: any, item: any) => {
                    prev.push({
                        value: item.name,
                        creditCode: item.creditCode,
                        legalPersonName: item.legalPersonName,
                    });
                    return prev;
                }, []);
                queryParams.data = res.data.companyInfos;
                cb(nameList);
            } else {
                queryParams.data = res.data.companyInfos;
                cb([]);
            }
        } else {
            cb([]);
        }
    }).catch(() => {
        cb([]);
    });
};