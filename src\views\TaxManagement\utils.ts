// src/views/TaxManagement/utils.ts
/**
 * 格式化日期，将 ISO 格式 (2020-10-01T00:00:00) 转换为 YYYY-MM-DD 格式
 */
export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return ""
  // 如果日期已经是 YYYY-MM-DD 格式，直接返回
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return dateString
  // 否则尝试解析并格式化日期
  try {
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, "0")
    const day = String(date.getDate()).padStart(2, "0")
    return `${year}-${month}-${day}`
  } catch (error) {
    console.error("日期格式化错误:", error)
    return dateString // 如果解析失败，返回原始字符串
  }
}

/**
 * 重排序税种列表，将子项移动到对应父项的后面
 * 父项的 parentCode 为 "0"
 * 子项的 parentCode 对应父项的 projectCode
 */
/**
 * 重排序税种列表，将子项移动到对应父项的后面
 * 父项的 parentCode 为 "0"
 * 子项的 parentCode 对应父项的 projectCode
 * 注意：处理非唯一 projectCode 的情况
 */
export const reorderTaxList = (taxList: any[]): any[] => {
  // 创建原始数据的深拷贝，避免引用问题
  const originalData = [...taxList]
  const result: any[] = []
  // 使用索引位置作为唯一标识，而不是 projectCode
  const processedIndices = new Set<number>()

  // 先处理所有父项及其子项
  const parentIndices = originalData
    .map((item, index) => ({ item, index }))
    .filter(({ item }) => item.parentCode === "0")
    .map(({ index }) => index)

  for (const parentIndex of parentIndices) {
    if (processedIndices.has(parentIndex)) {
      continue
    }

    const parentItem = originalData[parentIndex]

    // 添加父项
    result.push(parentItem)
    processedIndices.add(parentIndex)

    // 查找并添加所有直接子项
    const childrenIndices = originalData
      .map((item, index) => ({ item, index }))
      .filter(({ item, index }) => item.parentCode === parentItem.projectCode && !processedIndices.has(index))
      .map(({ index }) => index)

    for (const childIndex of childrenIndices) {
      result.push(originalData[childIndex])
      processedIndices.add(childIndex)
    }
  }

  // 确保所有项都被处理（无论是否有父子关系）
  originalData.forEach((item, index) => {
    if (!processedIndices.has(index)) {
      result.push(item)
      processedIndices.add(index)
    }
  })

  // 最终检查确保没有丢失数据
  if (result.length !== originalData.length) {
    console.error("数据处理前后长度不一致:", {
      原始长度: originalData.length,
      处理后长度: result.length,
    })
    // 返回原始数据而不是处理后的结果
    return originalData
  }

  return result
}

/**
 * 根据值获取对应的key
 * @param dict 字典
 * @param targetValue 目标值
 * @returns 对应的key
 */
export const getKeyByValue = (dict: { [key: string]: string }, targetValue: string): string => {
  const entry = Object.entries(dict).find(([_, value]) => value === targetValue)
  return entry ? entry[0] : ""
}
