import { h, ref } from 'vue'
import { ElTooltip } from 'element-plus'
import { AccountStandard, useAccountSetStoreHook } from "@/store/modules/accountSet";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { globalWindowOpen } from "@/util/url";
import { formatMoney } from "@/util/format";
import { checkPermission } from "@/util/permission";
import { type IResponseModel, request } from "@/util/service";
import type { IAssetDetail, IFSearchItem, IAccountSubjectList} from "./types";
import type { IGetAttachFileListBack } from "@/components/UploadFileDialog/types";
import type { Ref } from "vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModel } from "@/api/accountSubject.js";
import questionIcon from '@/assets/Icons/question.png'
import hintIcon from '@/assets/Icons/hint.png'

export const ifCanUseVoucher = (changeid: number, divid: string, changeData: any, callback: Function, voucherCallBack: Function) => {
    if (useAccountSetStoreHook().permissions.includes("fixedassets-card-cancreatevoucher")) {
        let msg;
        if (divid == "kmtz") {
            msg = "是否立即将该资产的科目余额转移到新科目上？";
        } else {
            msg = "您的变更已保存，需要立即生成凭证吗？";
        }
        ElConfirm(msg).then((r: boolean) => {
            if (r) {
                // OverFlow(divid, divid + "Voucher");
                if (divid == "ztxg") {
                    if (changeData.status != "4") {
                        ElNotify({
                            type: "warning",
                            message: "仅状态更新为已减少时可生成凭证",
                        });
                        return false;
                    } else {
                        voucherCallBack(divid, changeid);
                        // Getvoucher(divid);
                    }
                } else if (divid == "kmtz") {
                    voucherCallBack(divid, changeid);
                    // Getvoucher(divid);
                } else {
                    // if ($("#" + divid + "_value2").val() == "") {
                    //     ElNotify({
                    //         type:'warning',
                    //         message:'未设定调整数值'
                    //     });
                    //     // Notify("提示", "未设定调整数值");
                    //     return false;
                    // }
                    // else {
                    //     voucherCallBack(divid,changeid)
                    //     // Getvoucher(divid);
                    // }
                }
            } else {
                callback();
            }
        });
    } else {
        ElNotify({
            type: "success",
            message: "保存成功",
        });
        callback();
    }
};

export const getChangeList = (property: string, accountingStandard: AccountStandard, isBatch?: Boolean) => {
    let list = [];
    //如果属性为2，则
    if (property === "2") {
        list = [
            {
                name: "原值调整",
                slot: "valueChange",
            },
            {
                name: "使用年限调整",
                slot: "usefullifeChange",
            },
            {
                name: "科目调整",
                slot: "asubChange",
            },
            {
                name: "部门调整",
                slot: "departmentChange",
            },
            {
                name: "摊销方法调整",
                slot: "depreciationTypeChange",
            },
        ];
    } else {
        list = [
            {
                name: "原值调整",
                slot: "valueChange",
            },
            {
                name: "累计折旧调整",
                slot: "accumulatedChange",
            },
            {
                name: "使用年限调整",
                slot: "usefullifeChange",
            },
            {
                name: "科目调整",
                slot: "asubChange",
            },
            {
                name: "部门调整",
                slot: "departmentChange",
            },
            {
                name: "折旧方法调整",
                slot: "depreciationTypeChange",
            },
            {
                name: "状态修改",
                slot: "statusChange",
            },
        ];
        if ((property === "1" && accountingStandard === 2) || (property === "0" && [2, 3].includes(accountingStandard))) {
            list.splice(2, 0, {
                name: "计提减值准备",
                slot: "provisionChange",
            });
        }
        if ([3, 4, 5].includes(accountingStandard) && property === "1") {
            list.splice(1, 1);
        }
    }
    if (property != "0") {
        list = list.map((obj) => {
            if (obj.name.includes("折旧")) {
                return {
                    ...obj,
                    name: obj.name.replace("折旧", "摊销"),
                };
            }
            return obj;
        });
    }
    if (isBatch) {
        list = list.filter((obj) => {
            return obj.name != "科目调整";
        });
    }
    return list;
};

export const setColumns = (type: string, showInfo: boolean, accountingStandard: AccountStandard) => {
    const setModule = type === "1" ? "Initfixedassets" : "FixedassestList";
    let columns: IColumnProps[] = [
        { slot: "selection", width: 27, fixed: "left" },
        { slot: "fa_num", label: "资产编号", className: "colCellMove" },
        { slot: "fa_type", label: "资产类别", className: "colCellMove" },
        {
            label: "资产属性",
            prop: "fa_property",
            minWidth: 125,
            width: getColumnWidth(setModule, "fa_property"),
            align: "left",
            headerAlign: "left",
            className: "colCellMove",
            formatter(row, column, cellValue) {
                if (cellValue === "0") {
                    return "固定资产";
                } else if (cellValue === "1") {
                    return "无形资产";
                } else if (cellValue === "2") {
                    return "长期待摊费用";
                } else {
                    return "";
                }
            },
        },
        { slot: "fa_name", label: "资产名称", className: "colCellMove" },
        { slot: "fa_model", label: "规格型号", className: "colCellMove" },
        { slot: "started_date", label: "开始使用日期", className: "colCellMove" },
        {
            label: "资产原值",
            prop: "value",
            minWidth: 136,
            width: getColumnWidth(setModule, "value"),
            align: "right",
            headerAlign: "right",
            className: "colCellMove",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
        },
        {
            label: type === "1" ? "累计折旧" : "本期折旧",
            prop: type === "1" ? "totaldeprecition" : "monthdeprecition",
            minWidth: 100,
            width: getColumnWidth(setModule, type === "1" ? "totaldeprecition" : "monthdeprecition"),
            align: "right",
            headerAlign: "right",
            className: "colCellMove",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
        },
        {
            label: "资产净值",
            prop: "netassetvalue",
            minWidth: 136,
            width: getColumnWidth(setModule, "netassetvalue"),
            align: "right",
            headerAlign: "right",
            className: "colCellMove",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
        },
        { slot: "operation", fixed: "right" },
    ];
    if (showInfo) {
        columns = [
            { slot: "selection", width: 27, fixed: "left" },
            { slot: "fa_num", label: "资产编号", className: "colCellMove" },
            { slot: "fa_type", label: "资产类别", className: "colCellMove" },
            {
                label: "资产属性",
                prop: "fa_property",
                minWidth: 125,
                width: getColumnWidth(setModule, "fa_property"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
                formatter(row, column, cellValue) {
                    if (cellValue === "0") {
                        return "固定资产";
                    } else if (cellValue === "1") {
                        return "无形资产";
                    } else if (cellValue === "2") {
                        return "长期待摊费用";
                    } else {
                        return "";
                    }
                },
            },
            { slot: "fa_name", label: "资产名称", className: "colCellMove" },
            { slot: "fa_model", label: "规格型号", className: "colCellMove" },
            { slot: "started_date", label: "开始使用日期", className: "colCellMove" },
            {
                label: "录入日期",
                prop: "created_date",
                minWidth: 100,
                width: getColumnWidth(setModule, "created_date"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return row.fa_num ? value.split("T")[0] : "";
                },
            },
            {
                label: "资产原值",
                prop: "value",
                minWidth: 136,
                width: getColumnWidth(setModule, "value"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            {
                label: "本期折旧",
                prop: "monthdeprecition",
                minWidth: 100,
                width: getColumnWidth(setModule, "monthdeprecition"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            {
                label: "本年累计折旧",
                prop: "yeardeprecition",
                minWidth: 120,
                width: getColumnWidth(setModule, "yeardeprecition"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            {
                label: "以前年度累计折旧",
                prop: "preyeardeprecition",
                minWidth: 130,
                width: getColumnWidth(setModule, "preyeardeprecition"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            {
                label: "累计折旧",
                prop: "totaldeprecition",
                minWidth: 100,
                width: getColumnWidth(setModule, "totaldeprecition"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            {
                label: "资产净值",
                prop: "netassetvalue",
                minWidth: 136,
                width: getColumnWidth(setModule, "netassetvalue"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            {
                label: "预计使用月份",
                prop: "usefullife",
                minWidth: 110,
                width: getColumnWidth(setModule, "usefullife"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    if (value) {
                        return value;
                    } else {
                        return "";
                    }
                },
            },
            {
                label: "已计提月份",
                prop: "provisionmonth",
                minWidth: 100,
                width: getColumnWidth(setModule, "provisionmonth"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    if (value) {
                        return value;
                    } else {
                        return "";
                    }
                },
            },
            { slot: "department", label: "使用部门", className: "colCellMove" },
            {
                label: "残值率(%)",
                prop: "netsalvage_rate",
                minWidth: 90,
                width: getColumnWidth(setModule, "netsalvage_rate"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    if (value) {
                        return formatMoney(value) + "%";
                    } else {
                        return "";
                    }
                },
            },
            {
                label: "减值准备",
                prop: "provision",
                minWidth: 80,
                width: getColumnWidth(setModule, "provision"),
                align: "right",
                headerAlign: "right",
                className: "colCellMove",
                formatter: (row, column, value) => {
                    if (value) {
                        return value;
                    } else {
                        return "";
                    }
                },
            },
            {
                label: "供应商",
                prop: "vendor",
                minWidth: 70,
                width: getColumnWidth(setModule, "vendor"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            {
                label: "使用人",
                prop: "use_people",
                minWidth: 70,
                width: getColumnWidth(setModule, "use_people"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            {
                label: "存放位置",
                prop: "location",
                minWidth: 80,
                width: getColumnWidth(setModule, "location"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            { slot: "status", label: "使用状况", className: "colCellMove" },
            {
                label: "资产科目",
                prop: "fa_asub_name",
                minWidth: 100,
                width: getColumnWidth(setModule, "fa_asub_name"),
                className: "colCellMove",
            },
            {
                label: "资产科目辅助核算",
                prop: "fa_asub_aae",
                minWidth: 120,
                width: getColumnWidth(setModule, "fa_asub_aae"),
                className: "colCellMove",
            },
            {
                label: "累计折旧科目",
                prop: "depreciation_asub_name",
                minWidth: 100,
                width: getColumnWidth(setModule, "depreciation_asub_name"),
                className: "colCellMove",
            },
            {
                label: "累计折旧科目辅助核算",
                prop: "depreciation_asub_aae",
                minWidth: 120,
                width: getColumnWidth(setModule, "depreciation_asub_aae"),
                className: "colCellMove",
            },
            {
                label: "资产处置科目",
                prop: "disposal_asub_name",
                minWidth: 100,
                width: getColumnWidth(setModule, "disposal_asub_name"),
                className: "colCellMove",
            },
            {
                label: "资产处置科目辅助核算",
                prop: "disposal_asub_aae",
                minWidth: 120,
                width: getColumnWidth(setModule, "disposal_asub_aae"),
                className: "colCellMove",
            },
            {
                label: "折旧费用科目",
                prop: "cost_asub_aae",
                minWidth: 110,
                width: getColumnWidth(setModule, "cost_asub"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            {
                label: "增加方式",
                prop: "created_way",
                minWidth: 80,
                width: getColumnWidth(setModule, "created_way"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            {
                label: "减少方式",
                prop: "decreased_way",
                minWidth: 80,
                width: getColumnWidth(setModule, "decreased_way"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            {
                label: "减少日期",
                prop: "decreased_date",
                minWidth: 80,
                width: getColumnWidth(setModule, "decreased_date"),
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
            },
            { slot: "note", label: "备注", className: "colCellMove" },
            { slot: "operation", fixed: "right" },
        ];
        if (accountingStandard === 2 || accountingStandard === 3) {
            columns.splice(
                -6,
                0,
                ...[
                    {
                        label: "资产减值准备科目",
                        prop: "impairment_provision_asub_name",
                        minWidth: 100,
                        width: getColumnWidth(setModule, "impairment_provision_asub_name"),
                        className: "colCellMove",
                    },
                    {
                        label: "资产减值准备科目辅助核算",
                        prop: "impairment_provision_asub_aae",
                        minWidth: 120,
                        width: getColumnWidth(setModule, "impairment_provision_asub_aae"),
                        className: "colCellMove",
                    },
                ]
            );
        }
        if (type === "1") {
            columns.splice(-2, 1);
        }
    }
    return columns;
};

export const getYearMonthLastDay = (date: string) => {
    const year = ~~date.match(/\d{4}(?=年)/g)![0];
    const month = ~~date.match(/\d{1,2}(?=月)/g)![0];
    const d = new Date(year, month, 1);
    const lastDay = new Date(d.getTime() - 1000 * 60 * 60 * 24).getDate();
    return { year, month, lastDay };
};
export const textareaBottom = (exampleRef: any) => {
    const textareaInner = exampleRef.value?.$el.querySelector(".el-textarea__inner");
    if (textareaInner) {
        // 使用 requestAnimationFrame 来确保元素已经准备好
        requestAnimationFrame(() => {
            textareaInner.scrollTop = textareaInner.scrollHeight;
        });
    }
};
export function handleZoomChange() {
    const zoomLevel = window.devicePixelRatio;
    const zoomLevelNum = Number(zoomLevel).toFixed(2);
    let tableMaxHeight = "650px";
    switch (zoomLevelNum) {
        case "1.00":
            tableMaxHeight = "650px";
            break;
        case "1.10":
            tableMaxHeight = "550px";
            break;
        case "1.25":
            tableMaxHeight = "450px";
            break;
        case "1.50":
            tableMaxHeight = "374px";
            break;
    }
    return tableMaxHeight;
}

export enum changeApi {
    "valueChange" = "/api/FAChange/BatchValueChange",
    "accumulatedChange" = "/api/FAChange/BatchAccumulatedChange",
    "provisionChange" = "/api/FAChange/BatchProvisionChange",
    "usefullifeChange" = "/api/FAChange/BatchUsefullifeChange",
    "departmentChange" = "/api/FAChange/BatchDepartmentChange",
    "depreciationTypeChange" = "/api/FAChange/BatchDepreciationTypeChange",
    "statusChange" = "/api/FAChange/BatchStatusChange",
}

export enum changeId {
    "valueChange" = 1010,
    "accumulatedChange" = 1020,
    "provisionChange" = 1030,
    "usefullifeChange" = 1040,
    "departmentChange" = 1050,
    "depreciationTypeChange" = 1060,
    "statusChange" = 1070,
}

export async function checkNeedToastWithBillAndVoucher(faId: number, attachFiles: string) {
    return await request({
        url: "/api/FixedAssets/GetNeedSaveToVoucher",
        method: "post",
        params: { faId, attachFiles },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000) return false;
            return res.data;
        })
        .catch(() => {
            return false;
        });
}

export function saveAttachFileFn(
    faId: number,
    newFileids: number[],
    delFileids: number[],
    fileList: any[],
    successCallback: () => void,
    resetParams: () => void,
    isNeedSaveToVoucherForDelete = false
) {
    request({
        url: "/api/FixedAssets/SaveAttachFile",
        method: "post",
        params: {
            faId,
            newFileids: newFileids.join(","),
            delFileids: delFileids.join(","),
            isNeedSaveToVoucherForAdd: true,
            isNeedSaveToVoucherForDelete,
        },
    })
        .then((res: IResponseModel<boolean>) => {
            resetParams();
            if (res.state === 1000 && res.data) {
                successCallback();
                ElNotify({ type: "success", message: "保存成功" });
            } else {
                ElNotify({ type: "warning", message: "保存失败" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "保存失败" });
            resetParams();
        });
}

export function getAssetsAttachFiles(row: IAssetDetail, uploadFileDialogRef: Ref<any>) {
    const { fa_id } = row;
    const params = { faId: fa_id };
    request({
        url: "/api/FixedAssets/GetAttachFileList",
        method: "post",
        params,
    }).then((res: IResponseModel<IGetAttachFileListBack>) => {
        if (res.state === 1000 && res.data.result) {
            const list = res.data.data.map((item: any) => {
                item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                return item;
            });
            uploadFileDialogRef.value?.open(params, list, res.data.parentId);
        }
    });
}

export function isKeyOfIFSearchItem(key: string): key is keyof IFSearchItem {  
    return [
        'fa_num', 
        'fa_name', 
        'fa_model', 
        'note',
        'fa_type', 
        'department',
        'status'
    ].includes(key);  
}

export function getCostAsubList(accountingStandard: number, faProperty: number, isBatch: boolean = false, asIdIsNumber: boolean = false) {
    const accountSubject = useAccountSubjectStore().accountSubjectList;
    const asubList = new Array<{ asubType: number; asubList: Array<IAccountSubjectModel | IAccountSubjectList> }>();

    asubList.push({
        asubType: 0,
        asubList: accountSubject
            .filter((asub) => asub.status === 0 && asub.isLeafNode)
            .map((asub) => (asIdIsNumber ? asub : { ...asub, asubId: asub.asubId + "" })),
    });
    const asubGroup: any = {};
    accountSubject.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = new Array<IAccountSubjectList>();
            asubList.push({ asubType: asub.asubType, asubList: asubGroup[asub.asubType] });
        }
        if (asub.status === 0 && asub.isLeafNode) {
            asubGroup[asub.asubType].push(asIdIsNumber ? asub : { ...asub, asubId: asub.asubId + "" });
        }
    });
    let asubType;
    switch (accountingStandard) {
        case AccountStandard.FolkComapnyStandard:
            asubType = [1, 9];
            break;
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.CompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            asubType = [1, 5, 6];
            break;
        case AccountStandard.UnionStandard:
            asubType = [1, 7];
            break;
    }
    let costAsubList = [];
    const costAsubAmList: any[] = [];
    costAsubList =
        asubType?.reduce((prev: IAccountSubjectModel[] | IAccountSubjectList[], item: number) => {
            let list = asubList.filter((i) => i.asubType === item)[0].asubList as any[];
            if (item === 1) {
                if ([0,-1].includes(faProperty)) {
                    if (accountingStandard === 2 || accountingStandard === 1) {
                        list = list.filter((i) =>
                            isBatch ? !i.asubCode.startsWith("1602") && !i.asubCode.startsWith("1702") : !i.asubCode.startsWith("1602")
                        );
                    } else if (accountingStandard === 3) {
                        list = list.filter((i) => !i.asubCode.startsWith("1502"));
                    } else if (accountingStandard === 4 || accountingStandard === 5) {
                        list = list.filter((i) => !i.asubCode.startsWith("152"));
                    } else if (accountingStandard === 6) {
                        list = list.filter((i) => !i.asubCode.startsWith("163"));
                    } else if (accountingStandard === 7) {
                        list = list.filter((i) => !i.asubCode.startsWith("152"));
                    }
                } else if (faProperty === 1) {
                    if (accountingStandard === 2 || accountingStandard === 1) {
                        list = list.filter((i) => !i.asubCode.startsWith("1702"));
                    }
                }
            }
            list.forEach((item) => {
                costAsubAmList.push({ label: item.asubCode + " " + item.asubAAName, value: item.asubId.toString() });
            });
            prev.push(...list);
            return prev;
        }, []) || [];
    return { costAsubList, costAsubAmList };
}

// 固定的内容配置
const FIXED_CONTENT = [
    '录入资产卡片的期间，即资产在系统中正式入账并纳入折旧核算的会计期间。该期间由计提折旧到哪个期间决定，不支持手动修改。如需修改这个期间，可前往',
    '资产达到可使用状态的期间，即资产实际投入使用并产生价值的会计期间，且开始使用期间不能晚于录入期间。'
  ] as const

const TOOLTIP_CONTENT = {
    init: {
      title: '资产初始化',
      content: '资产模块启用前已存在的资产（即2024 年 5 月前购置并正在使用的资产），需在「资产初始化」模块录入原值、折旧等历史数据。'
    },
    list: {
      title: '资产列表',
      content: '资产模块启用后新增的资产（即2024 年 5 月及以后购置），直接在「资产列表」模块添加。'
    },
    hint: '资产启用期间的资产初始化数据要确保和总账一致，且计提折旧后初始化数据不再支持修改'
} as const

const DEPRECIATION_CONTENT = [
    '计提折旧是将资产成本分摊至各会计期间',
    '计提折旧是为了确认当期处理，即便本月无资产数据，点击 "计提折旧" 可推进至下一会计期间，保障后续资产操作（如新增资产、变更、折旧等）正常进行',
    '计提折旧后的期间即为最新的卡片录入期间'
  ] as const

const handleDataMoreHelp = () => {
    globalWindowOpen('https://help.ningmengyun.com/#/yyc/commonProForYYC?subMenuId=100111&answerId=232')
}

const handleTabMoreHelp = () => {
    globalWindowOpen('https://help.ningmengyun.com/#/yyc/commonProForYYC?subMenuId=100111&answerId=231')
}
  
export function useTooltip() {
    // const renderTooltip = (title: string,content:any,) => {
    //     return h(ElTooltip, {
    //       effect: 'light',
    //       placement: 'right-start',
    //       popperClass: 'el-option-tool-tip',
    //       teleported: true,
    //       appendTo:'body',
    //     }, {
    //       content,
    //       default: () => h('div', {
    //         class: 'label-with-icon'
    //       }, [
    //         h('span', {}, title),
    //         h('img', {
    //           class: 'img-question',
    //           src: questionIcon,
    //           style: { height: '16px' }
    //         })
    //       ])
    //     })
    // }

    const renderDateContent = (title:string, handleToProvision:Function) => {
        const tooltipRef = ref()
        const handleClick = () => {
            if(checkPermission(['fixedassets-card-candepreciation'])) {
                tooltipRef.value?.hide?.() 
                handleToProvision()
            }
        }
        const content = () => h('div', { class: 'tips' }, [
            h('div', {}, [
            h('span', { class:'bold' }, '录入期间：'),
            `${FIXED_CONTENT[0]}`,
            h('span', {
                class: checkPermission(['fixedassets-card-candepreciation']) ? 'link' :'',
                style: { fontSize : '12px'},
                onClick: handleClick
            }, '【计提折旧及生成凭证】'),
            '页面计提或取消计提折旧到对应期间。'
            ]),
            h('div', {}, [ h('span', { class:'bold' }, '开始使用期间：'),`${FIXED_CONTENT[1]}`]),
            h('div', {
            class: 'link',
            style: { fontSize : '12px'},
            onClick: handleDataMoreHelp
            }, '查看更多帮助>>')
        ])
        return h(ElTooltip, {
            ref: tooltipRef,
            effect: 'light',
            placement: 'right-start',
            popperClass: 'el-option-tool-tip',
            trigger: 'hover', // 保持hover触发
            teleported: true,
            appendTo:'body',
        }, {
            content,
            default: () => h('div', {
                class: 'label-with-icon'
            }, [
                h('span', {}, title),
                h('img', {
                    class: 'img-question',
                    src: questionIcon,
                    style: { height: '16px' }
                })
            ])
        })
    }

    const renderTabContent = (title: string, isActive: boolean, period:string) => {
        const content = () => h('div', { class: 'tips', style: { color: "var(--black)" } }, [
            h('div', {}, [
                h('span', { class:'bold' }, `${TOOLTIP_CONTENT.init.title}：`),
                h('span', {}, TOOLTIP_CONTENT.init.content.replace('2024 年 5 月', period))
            ]),
            h('div', {}, [
                h('span', { class:'bold' }, `${TOOLTIP_CONTENT.list.title}：`),
                h('span', {}, TOOLTIP_CONTENT.list.content.replace('2024 年 5 月', period))
            ]),
            h('div', {}, [
                h('img', {
                    src: hintIcon,
                    style: {
                        height: '12px',
                        width: '12px',
                        verticalAlign: 'baseline',
                        marginRight: '2px',
                    }
                }),
                TOOLTIP_CONTENT.hint
            ]),
            h('div', {
                class: 'link',
                style: { fontSize : '12px'},
                onClick: handleTabMoreHelp
            }, '查看更多帮助>>')
        ])

        return h('div', {
            class: 'label-with-icon',
            style: {
                display: 'inline-flex',
                alignItems: 'center'
            }
        }, [
            h('span', {}, title),
            isActive && h(ElTooltip, {
                effect: 'light',
                placement: 'bottom',
                popperClass: 'el-option-tool-tip',
                teleported: true,
                appendTo:'body',
            }, {
                content,
                default: () => h('div', {
                    class: 'image-container',
                })
            })
        ])
    }

    const renderDepreciationTooltip = (title: string, isActive?: boolean) => {
        return h('div', {
            class: 'label-with-icon',
            style: {
                display: 'inline-flex',
                alignItems: 'center'
            }
        }, [
            h('span', {}, title),
            isActive && h(ElTooltip, {
                effect: 'light',
                placement: 'bottom',
                popperClass: 'el-option-tool-tip',
                teleported: true,
                appendTo: 'body',
            }, {
                content: () => h('div', { class: 'tips' }, [
                    ...DEPRECIATION_CONTENT.map((text, index) => 
                        h('div', { class: 'tips-text' }, `${index + 1}.${text}`)
                    )
                ]),
                default: () => h('div', {
                    class: 'image-container',
                })
            })
        ])
    }
  
    return {
        renderDateContent,
        renderTabContent,
        renderDepreciationTooltip,
    }
}