<template>
    <div class="content">
        <div class="guide-title">
            <img src="@/assets/Icons/corner-marker-left.png" alt="beginners-guide-left" />
            <span>新手指引</span>
            <img src="@/assets/Icons/corner-marker-right.png" alt="beginners-guide-left" />
        </div>
        <div class="guide-content">
            <div class="card" v-for="(item, index) in cardList" :key="item.id">
                <div class="card-title">
                    <span>{{ index + 1 }}</span
                    >{{ item.title }}
                </div>
                <ul class="card-text">
                    <li v-for="(text, index) in item.text" :key="index">{{ text }}</li>
                </ul>
                <a
                    class="button green mr-10"
                    @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=' + item.videoGuide)"
                    >视频指引</a
                >

                <a class="button solid-button" @click="handlePractice(item)"
                    >立即体验
                    <span v-show="!item.isStudy" class="no-study"></span>
                </a>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
export default {
    name: "BeginnersGuide",
};
</script>
<script lang="ts" setup>
import { globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { type IResponseModel, request } from "@/util/service";
import { onMounted, reactive } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { ElConfirm } from "@/util/confirm";

const cardList = reactive([
    {
        id: 1,
        title: "快速初始化",
        text: ["科目设置", "辅助核算设置", "维护期初余额", "一键导入旧账"],
        videoGuide: "*********",
        jumpPage: "/Settings/AccountSubject",
        pageTitle: "科目设置",
        isStudy: 1,
    },
    {
        id: 2,
        title: "发票智能化",
        text: ["直连税局，一键取票", "发票智能生成凭证", "智能发票风控报告"],
        videoGuide: "*********",
        jumpPage: "/Invoice/FetchInvoice",
        pageTitle: "一键取票",
        isStudy: 1,
    },
    {
        id: 3,
        title: "资金智能化",
        text: ["一键获取流水及回单", "流水智能生成凭证", "多维度资金分析"],
        videoGuide: "*********",
        jumpPage: "/Cashier/DepositJournal",
        pageTitle: "银行日记账",
        isStudy: 1,
    },
    {
        id: 4,
        title: "资产全面化",
        text: ["快速新增、变更、处置", "一键测算、计提折旧", "智能生成凭证"],
        videoGuide: "*********",
        jumpPage: "/FixedAssets/FixedAssets",
        pageTitle: "资产管理",
        isStudy: 1,
    },
    {
        id: 5,
        title: "凭证智能化",
        text: ["智能凭证模板", "AI辅助记账", "智能凭证附件"],
        videoGuide: "*********",
        jumpPage: "/Voucher/NewVoucher",
        pageTitle: "新增凭证",
        isStudy: 1,
    },
    {
        id: 6,
        title: "档案电子化",
        text: ["发票查验存档、防重复报销", "电子回单、合同存档", "收票宝快速收票"],
        videoGuide: "*********",
        jumpPage: "/ERecord/ERecord",
        pageTitle: "会计电子档案",
        isStudy: 1,
    },
]);
const handlePractice = (item: any) => {
    const accountSet = useAccountSetStore().accountSet;
    if ((item.id === 4 && accountSet?.fixedasset === 0) || (item.id === 3 && accountSet?.cashJournal === 0)) {
        ElConfirm(`您的账套未启用${item.id === 4 ? "资产" : "资金"}模块，是否立即前往启用？`).then((r) => {
            if (r) {
                globalWindowOpenPage("/Settings/AccountSets?isEditModule=true", "账套");
            }
        });
        return;
    }
    globalWindowOpenPage(item.jumpPage, item.pageTitle);
    request({
        url: "/api/PaidConversion/UpdateNewbieGuidePace?module=" + item.id,
        method: "post",
    }).then(async (res: IResponseModel<boolean>) => {
        if (res.data) {
            item.isStudy = 1;
            window.dispatchEvent(new CustomEvent("refreshGuidePace"));
        }
    });
};
const getNewbieGuidePace = () => {
    request({
        url: "/api/PaidConversion/NewbieGuidePace",
        method: "get",
    }).then(
        (
            res: IResponseModel<{
                isShowPace: boolean;
                paceItems: {
                    initStudy: number;
                    invoiceStudy: number;
                    cashierStudy: number;
                    assetsStudy: number;
                    voucherStudy: number;
                    diskStudy: number;
                };
            }>
        ) => {
            if (res.state === 1000) {
                const paceItems = res.data.paceItems;
                cardList[0].isStudy = paceItems.initStudy;
                cardList[1].isStudy = paceItems.invoiceStudy;
                cardList[2].isStudy = paceItems.cashierStudy;
                cardList[3].isStudy = paceItems.assetsStudy;
                cardList[4].isStudy = paceItems.voucherStudy;
                cardList[5].isStudy = paceItems.diskStudy;
            }
        }
    );
};
onMounted(() => {
    getNewbieGuidePace();
});
</script>
<style scoped lang="less">
.content {
    height: 100%;
    width: 100%;
    background: url("@/assets/BeginnersGuide/beginners-guide-bg.png") no-repeat;
    background-size: 103% 103%;
    background-position: center;
    box-sizing: border-box;

    .guide-title {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 56px;
        position: relative;
        top: 10px;
        img {
            width: 20px;
            height: 20px;
        }
        span {
            font-size: 24px;
            font-weight: bold;
            margin: 0 14px;
        }
    }
    .guide-content {
        width: 1020px;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .card {
            width: 262px;
            height: 220px;
            background: #fff;
            box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.08);
            border-radius: 4px;
            margin: 20px;
            padding: 15px 10px 10px;
            .card-title {
                font-size: 20px;
                font-weight: bold;
                color: #333;
                line-height: 28px;
                position: relative;
                top: 5px;
                left: 23px;
                span {
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    margin-right: 10px;
                    border-radius: 10px;
                    line-height: 20px;
                    font-size: 14px;
                    text-align: center;
                    color: #fff;
                    background-color: #07c160;
                    position: relative;
                    top: -2px;
                }
                text-align: left;
            }
            .card-text {
                height: 108px;
                margin: 20px 0;
                padding: 0 20px 0 40px;
                li {
                    font-size: 16px;
                    color: #333;
                    line-height: 24px;
                    margin: 5px 0;
                    text-align: left;
                }
            }
            .button {
                display: inline-block;
                width: 96px;
                height: 32px;
                line-height: 32px;
                text-align: center;
                font-size: 16px;
                cursor: pointer;
                position: relative;
                &.green,
                &.green:hover {
                    color: #07c160;
                    border: 1px solid #07c160;
                    background-color: #fff;
                }
                position: relative;
                .no-study {
                    position: absolute;
                    top: -3px;
                    right: -3px;
                    background-color: var(--red);
                    border-radius: 12px;
                    height: 6px;
                    width: 6px;
                }
            }
        }
    }
}
</style>
