<template>
    <div class="add-content">
        <el-form :model="formData" ref="formRef" label-width="272px">
            <el-form-item label="辅助核算类别：">
                <el-input v-model="formData.AATypeName" ref="AATypeNameRef" placeholder="名称不能超过10个字" />
            </el-form-item>
            <el-form-item label="默认列：">
                <el-input value="编码" :readonly="true" />
                <el-input value="名称" :readonly="true" />
                <el-input value="备注" :readonly="true" />
            </el-form-item>
            <el-form-item label="自定义列：" class="add-box">
                <div v-for="(item, index) in formData.columnArr" :key="index" class="user-add">
                    <el-input
                        :placeholder="'自定义列' + (index + 1)"
                        v-model="formData.columnArr[index]"
                        @input="(e) => onColumnChange(e, index)"
                    />
                    <a class="link deleteUserDefined" @click="deleteColumn(index)">删除</a>
                </div>
                <a
                    class="button solid-button float-l"
                    style="margin-bottom: 16px"
                    @click="addColumn"
                    v-show="formData.columnArr.length < 8"
                >
                    添加列
                </a>
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">取消</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { validAATypeName, validUserDefined, ValidataAaType } from "../validator";

import { ElForm } from "element-plus";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";

const formRef = ref<InstanceType<typeof ElForm>>();
const AATypeNameRef = ref();
let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => {
    EditType = val;
    if (val === "New") {
        let timer = setTimeout(() => {
            const focusedElement = document.activeElement as HTMLElement;
            focusedElement?.blur();
            AATypeNameRef.value.focus();
            clearTimeout(timer);
        }, 1000);
    }
};

const props = defineProps<{ aaLength: number }>();
const formData: any = reactive({
    AATypeName: "",
    AATypeId: 0,
    columnArr: [] as string[],
});

const emit = defineEmits(["changeAAType", "formCancel", "formChanged"]);

let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    if (props.aaLength >= 20 && EditType === "New") {
        ElNotify({ type: "warning", message: "亲，提交失败啦，辅助核算类别最多支持20种哦" });
        return;
    }
    const AATypeNameFlag = validAATypeName(formData.AATypeName, EditType);
    let UserDefinedFlag = true;
    for (let index = 0; index < formData.columnArr.length; index++) {
        if (!validUserDefined(formData.columnArr[index], index)) {
            UserDefinedFlag = false;
            break;
        }
    }
    if (!AATypeNameFlag || !UserDefinedFlag) return;
    if (ValidataAaType(formData.columnArr)) {
        const params = {
            aaTypeName: formData.AATypeName,
            columnList: formData.columnArr.filter((item: string) => item !== ""),
        };
        isSaving = true;
        request({
            url: "/api/AssistingAccountingType" + (EditType === "Edit" ? "?aaTypeId=" + formData.AATypeId : ""),
            method: EditType === "Edit" ? "put" : "post",
            data: params,
        })
            .then((res: IResponseModel<boolean>) => {
                if (res.state !== 1000 || !res.data) {
                    if (res.msg.includes("已经存在辅助核算类别")) {
                        ElNotify({ type: "warning", message: "亲，提交失败啦，辅助核算类别名称已存在，请修改后重新保存哦~" });
                    } else {
                        ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    }
                    return;
                }
                ElNotify({ message: "亲，保存成功啦！", type: "success" });
                if (EditType === "New") {
                    emit("changeAAType");
                } else {
                    handleCancel();
                }
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
                isSaving = false;
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};
const editForm = (data: any) => {
    Object.keys(formData).forEach((key) => {
        if (Object.keys(formData).includes(key)) {
            formData[key] = data[key];
        }
    });
};
const resetForm = () => {
    formData.AATypeName = "";
    formData.AATypeId = 0;
    formData.columnArr = [];
    EditType = "New";
};
defineExpose({ changeType, editForm, resetForm });
const addColumn = () => formData.columnArr.push("");
const deleteColumn = (index: number) => formData.columnArr.splice(index, 1);

const onColumnChange = (val: string, index: number) => {
    if (val.length > 64) {
        ElNotify({ message: "亲，自定义列名不能超过64个字！", type: "warning" });
        formData.columnArr[index] = val.slice(0, 64);
    }
};

watch(
    () => formData.AATypeName,
    (val) => {
        if (val.length > 10) {
            ElNotify({ message: "亲，辅助核算类别不能超过十个字！", type: "warning" });
            formData.AATypeName = val.slice(0, 10);
        }
    }
);
watch(
    formData,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
@import "@/style/Functions.less";
.add-content {
    :deep(.el-form) {
        .el-form-item {
            &:first-child,
            &:nth-child(2) {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
    .detail-el-input(188px, 32px);
    a.button {
        margin-right: 10px;
        &:first-child {
            margin-left: 0;
        }
    }
    .el-input {
        box-sizing: border-box;
        margin-left: 10px;
        &:first-child {
            margin-left: 0px;
        }
    }
    .add-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        width: 900px;
        .user-add {
            margin-right: 10px;
            position: relative;
            margin-bottom: 15px;
            :deep(.el-input) {
                .el-input__wrapper {
                    padding-right: 48px;
                }
            }
            &:hover {
                .deleteUserDefined {
                    color: var(--font-color);
                    display: block;
                    &:hover {
                        color: var(--main-color);
                    }
                }
            }
            .deleteUserDefined {
                color: var(--font-color);
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 10px;
                display: none;
            }
        }
    }
}
</style>
