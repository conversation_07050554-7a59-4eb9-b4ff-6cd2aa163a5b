export const AsubTypeEnum: any = {
    1: "资产",
    2: "负债",
    3: "共同",
    4: "权益",
    5: "成本",
    6: "损益",
    7: "净资产",
    8: "收入",
    9: "费用",
    10: "支出",
};
export interface ICheckedItem {
    code: string;
    name: string;
}
export interface IEditFormData {
    // as_id?: number;
    asubName: string;
    asubCode: string;
    asubCodeBefore: string;
    asubType: number;
    parentId: number;
    direction: number;
    assistingaccounting: number;
    aaTypes: string;
    allowNullAaTypes: string;
    aaEntries: string;
    quantityaccounting: number;
    measureunit: string;
    measureunitId: string;
    status: number;
    fcadjust: boolean | number;
    foreigncurrency: number;
    fcids: string;
    fceditType: number;
    isUsedAsubAddFC: string;
    editType: number;
    isCancelAssit: string;
    restricted: number;
    uscc: string;
    parent_asub_name?: string;
}
export interface ITableTreeData {
    aasj: ITableDataItem[];
    astate: ITableTreeDataState;
    casj: ITableDataItem[];
    cmasj: ITableDataItem[];
    cmstate: ITableTreeDataState;
    cstate: ITableTreeDataState;
    dasj: ITableDataItem[];
    dstate: ITableTreeDataState;
    exsj: ITableDataItem[];
    exstate: ITableTreeDataState;
    iasj: ITableDataItem[];
    istate: ITableTreeDataState;
    nasj: ITableDataItem[];
    nastate: ITableTreeDataState;
    oasj: ITableDataItem[];
    ostate: ITableTreeDataState;
    resj: ITableDataItem[];
    restate: ITableTreeDataState;
}
export interface ITableDataItem {
    aatypeNames: string;
    aatypes: string;
    aatypesAllowNull: string;
    acronym: string;
    assistingAccounting: number;
    asubAAName: string;
    asubCode: string;
    asubId: number;
    asubLevel: number;
    asubName: string;
    asubType: number;
    direction: number;
    fcAdjust: number;
    fcIds: string;
    foreigncurrency: number;
    isLeafNode: boolean;
    measureUnit: string;
    measureUnitId: string;
    note: string;
    operation: number;
    parentId: number;
    quantityAccounting: number;
    restricted: number;
    status: number;
    uscc: string;
    index?:number;
    isChecked?:boolean;
}

export interface ITableTreeDataState {
    isAssit: boolean;
    isFC: boolean;
    isQuant: boolean;
}
export interface Irow {
    row: any;
    rowIndex: number;
}
export interface ITabListItem {
    label: string;
    name: string;
    asubTypeCode: number;
}
export interface IAsubCodeLengthRes {
    preName: string;
    asid: number;
    codeLength: number[];
    firstCodeLength: number;
    secondCodeLength: number;
    thirdCodeLength: number;
    forthCodeLength: number;
    firstAsubLength: number;
    secondAsubLength: number;
    thirdAsubLength: number;
    forthAsubLength: number;
}
export interface ICurrencyResItem {
    asId: number;
    preName: string;
    id: number;
    code: string;
    name: string;
    rate: number;
    isBaseCurrency: boolean;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
    checked?:boolean;
}
export interface IAsubListItem {
    value: number;
    text: string;
}
export interface IAuxiliarySelectItem {
    code: string;
    model: string | number;
    name: string;
    selectList: IAuxiliarySelectListItem[];
}
export interface IAuxiliarySelectListItem {
    aaeid: number|string;
    aaname:  string;
    aanum: string;
    aatype: number;
    asid: number;
    createdBy: number;
    status: number;
}
export interface IAaTypeListItem {
    name:string;
    code:string;
}
