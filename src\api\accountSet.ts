import { request, type IResponseModel } from "@/util/service";

export async function getAccountSetBaseInfoApi() {
    return (await request({
        url: "/api/AccountSet/BaseInfo",
        method: "get",
    })) as any as IResponseModel<IAccountSetBaseInfo>;
}

export async function getAccountSetInfoApi(asid: number) {
    return (await request({
        url: "/api/AccountSetOnlyAuth/Info?asId=" + asid,
        method: "get",
    })) as any as IResponseModel<IAccountSetInfo>;
}

export async function getAccountSetInfoWithOnAsidApi() {
    return (await request({
        url: "/api/AccountSet/Info",
        method: "get",
    })) as any as IResponseModel<IAccountSetInfo>;
}

export interface IAccountSetBaseInfo {
    asId: number;
    asName: string;
    accountingStandard: AccountingStandard;
    subAccountingStandard: number;
    fixedasset: number;
    intangibleAsset: number;
    checkNeeded: number;
    cashJournal: number;
    asStartDate: string;
    showScm: number;
    taxType: number;
    lockState: number;
    decimalPlace: number;
    permission: string;
}

export interface IAccountSetInfo extends IAccountSetBaseInfo {
    asIndustry?: number;
    unifiedNumber?: string;
    taxNumberS?: string;
    faStartPeroid?: number;
    faStartDate?: string;
    faEndPeriod?: number;
    faEndDate?: string;
    asubLength?: string;
    taxpayerName?: string;
    taxpayerPassword?: string;
    taxadId?: number;
    createdBy?: number;
    createdDate?: string;
    modifiedBy?: number;
    modifiedDate?: string;
    usedBy?: number;
    usedDate?: string;
    deleteBy?: number;
    deleteDate?: string;
    destroyBy?: number;
    destroyDate?: string;
    isColdas: boolean;
    lockPassword?: string;
    periodDisplayText?: string;
    isEditSartDate: boolean;
    usedSpace: number;
    taxDeclareEmployeeName?: string;
    taxDeclareEmployeePhone?: string;
    taxDeclarePassword?: string;
    isExpired?: boolean;
    taxBureauLoginType?: number;
    taxBureauAccount?: string;
    taxBureauPersonPositionType?: number;
    taxBureauPersonIdentityNo?: string;
    taxBureauPersonAccount?: string;
    taxBureauPersonPassword?: string;
    taxBureauPersonPhone?: string;
    taxBureauAuthorized?: boolean;
    allAACalcStatement: boolean;
    taxBureauAgentTaxNumber?: string;
}

export enum AccountingStandard {
    LittleCompanyStandard = 1, //小企业会计准则
    CompanyStandard = 2, //企业会计准则
    FolkComapnyStandard = 3, // 民间非营利组织会计制度
    FarmerCooperativeStandard = 4, // 农民专业合作社财务会计制度
    FarmerCooperativeStandard2023 = 5, //农民专业合作社财务会计制度（2023年）
    UnionStandard = 6, // 工会会计制度
    VillageCollectiveEconomyStandard = 7, // 农村集体经济组织会计制度
}
