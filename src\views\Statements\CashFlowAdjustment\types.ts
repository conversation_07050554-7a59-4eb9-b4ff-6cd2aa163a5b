export interface IVoucherSplitBase {
    pid: number;
    vid: number;
    vgId: number;
    vgName: string;
    vnum: number;
    vdate: string;
    veId: number;
    splitNum: number;
    description: string;
    asubId: number;
    asubCode: string;
    asubName: string;
    debit: number;
    credit: number;
    mainItemName: string;
    subItemName: string;
    splited: boolean;
    // 是否是现金科目
    isCashAsub: boolean;
    // 对方科目是否是现金科目
    isOtherCashAsub: boolean;
    // 是否是损益类科目
    isProfitandloss: boolean;
    // 对方科目是否是损益类科目
    isOtherProfitandloss: boolean;
}

export interface IVoucherSplitItem extends IVoucherSplitBase {
    mainItemId: number;
    subItemId: number;
}

// export enum CashAssignRule {
//     // 现金 + 损益科目 + 经营活动  ===>  必须指定主表  不能指定附表
//     CashWithProfitWithBusiness = 1,
//     // 现金 + 损益科目 + 非经营活动  ===>  必须指定主表  必须指定附表
//     CashWithProfitWithOutBusiness = 2,
//     // 现金 + 非损益科目 + 经营活动  ===>  必须指定主表  必须指定附表
//     CashWithOutProfitWithBusiness = 3,
//     // 现金 + 非损益科目 + 非经营  ===>  必须指定主表  不能指定附表
//     CashWithOutProfitWithOutBusiness = 4,
//     // 非现金 + 损益科目  ===>  不能指定主表  必须指定附表
//     NoCashWithProfit = 5,
//     // 非现金 + 非损益科目  ===>  不能指定主表  可以指定附表
//     NoCashWithOutProfit = 6,
// }
