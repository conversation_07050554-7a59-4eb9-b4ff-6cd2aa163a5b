{"rules": [{"ruleXh": "772f89af87e34bc8a83ce1317f0680c3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , 'getIf([\\'\\',null].indexOf(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc) === -1,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse >0)')", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , 'getIf([\\'\\',null].indexOf(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc) === -1,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse >0)')", "ruleMessage": "《增值税减免税申报明细表》免税项目中第1栏“免征增值税项目销售额”第【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].ewbhxh-3】行应大于零。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_024", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "77397fb6d14848ddbde723b1ef052216", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "getIf ( ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 ) && ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0 ) && ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse > 0 ) , abs(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse,2)) <= 0.1 )", "ruleCode": "getIf ( ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 ) && ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0 ) && ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse > 0 ) , abs(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse,2)) <= 0.1 )", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第1栏“（一）应征增值税不含税销售额（3%征收率）”应等于本期《增值税及附加税费申报表（小规模纳税人适用）附列资料（一）》第8栏”应税行为（3%征收率）计税销售额计算\\不含税销售额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_002", "preTable": "ywbw.sbZzsXgm&&ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "75d3cce548284526bf2877ab85fb0f78", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1) > 0 && customGet_sumA() <= fzxx.tzxx.fzsbxx.qzd && customGet_sumA() >= customGet_sumB(),getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse <= round( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse * 0.03,2) && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse <= round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 * 0.05,2),2))", "ruleCode": "getIf(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1) > 0 && customGet_sumA() <= fzxx.tzxx.fzsbxx.qzd && customGet_sumA() >= customGet_sumB(),getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse <= round( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse * 0.03,2) && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse <= round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 * 0.05,2),2))", "ruleMessage": "当【主表】第2栏+第5栏>0，且第1栏+第4栏+第7栏+第9栏+第13栏未超过免税标准时，第16栏“本期应纳税额”应小于等于“第2栏×征收率+第5栏×征收率”，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_021", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "ce9553cbc4334539b5f357221713ed45", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length ,\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse >= round(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse * 0.03,2) && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse <= round(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse * 0.05,2)\")", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length ,\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse >= round(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse * 0.03,2) && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse <= round(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse * 0.05,2)\")", "ruleMessage": "《增值税减免税申报明细表》免税项目中第5栏“免税额”第【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].ewbhxh-3】行应大于等于第3栏“扣除后免税销售额” x征收率3%，且小于等于第3栏“扣除后免税销售额”×征收率5%。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_032第3栏“扣除后免税销售额” ×征收率3% ≤第5栏“免税额”≤第3栏“扣除后免税销售额”×征收率5%。", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "ce757396fce341b893f2a8d364ecc846", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= 0 , ywbw.sbFjsf.sbFjsfMx[*].bqyjse <= ywbw.sbFjsf.sbFjsfMx[*].bqynsfe )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , \" getIf ( 301.bqynsfe >= 0 , 301.bqyjse <= 301.bqynsfe ) \" )", "ruleMessage": "【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】第9列\"本期已缴税（费）额\"必须小于等于第4列\"本期应纳税（费）额，请检查！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "cdab00d4f7e44cc4a799a17d7e27911b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 === 0)", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 === 0)", "ruleMessage": "您应税服务属于未超过免税标准申报，【主表】应税服务、不动产和无形资产第6栏应该为0，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_111 当存在冲红非本所属期内的蓝字发票时即czchfbssqptfpdQy=Y，max(货物发票销售额合计，核定销售额合计)+max(服务发票销售额合计,服务核定销售额合计)）≤起征点(QZD)即满足填写校验规则起征点1的情况 ，那么【主表】应税服务第6栏必须为0", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "cc54a6cc22ab475ba3b32309bef4244f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec(1, ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length, \"getIf(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye !== 0 &&  ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.substr(0, 10) === '0001011608',ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse === ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse)\")", "ruleCode": "rangeExec(1, ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length, \"getIf(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye !== 0 &&  ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.substr(0, 10) === '0001011608',ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse === ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse)\")", "ruleMessage": "《增值税减免税申报明细表》减税项目中第4栏“本期实际抵减税额”第【[*][0]+1】行应等于第2栏“本期发生额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "008_*_4_校验", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "8a6534e241d0498caed56bd153c43790", "formXh": null, "fid": null, "regionCode": null, "relateTable": "", "sxh": null, "ruleData": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse),2) >= 0, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 >= 0)", "ruleCode": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse),2) >= 0, 001.bqyjse1[1] >= 0)", "ruleMessage": "货物及劳务：当期预缴余额大于等于0，申报表主表预缴栏次不能填报负数。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_23_1_校验1", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "0a86214b8d584b209caae57b2e254199", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse) >= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) , 2 )", "ruleCode": "abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse) >= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) , 2 )", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第19栏“本期免税额”应大于等于第20栏”其中：小微企业免税额“、第21栏”未达起征点免税额“的金额之和", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_067【主表】“本期数”“应税服务、不动产和无形资产”“其中：小微企业免税额”+“未达起征点免税额”之和需小于等于“本期免税额”", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "3fd3563c5e7d46158a6d84707eb09915", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "getIf (!!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz, !!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz && judgeTime(ywbw.sbFjsf.sbFjsfQtxx.bchssqz, ywbw.sbFjsf.sbFjsfQtxx.bchssqq, '>'))", "ruleCode": "getIf (!!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz, !!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz && judgeTime(ywbw.sbFjsf.sbFjsfQtxx.bchssqz, ywbw.sbFjsf.sbFjsfQtxx.bchssqq, '>'))", "ruleMessage": "被冲红所属期止必须大于被冲红所属期起！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_056", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "619e39fb820541cb839ed6012287448f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( fzxx.bqxx.gtjy === 'N' && customGet_Bbqzdbz () === 1 , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse , 2 ) <= 0 )", "ruleCode": "getIf ( fzxx.bqxx.gtjy === 'N' && customGet_Bbqzdbz () === 1 , round ( 001.yzzzsbhsxse[1] + 001.yzzzsbhsxse[2] - 001.swjgdkdzzszyfpbhsxse[1] - 001.swjgdkdzzszyfpbhsxse[2] + 001.xsczbdcbhsxse[2] - 001.swjgdkdzzszyfpbhsxse1[2] + 001.xssygdysgdzcbhsxse[1] , 2 ) <= 0 )", "ruleMessage": "您本期销售额未超过免税标准，请将本期应征增值税销售额（不包括开具或代开专用发票销售额）对应填写在【主表】第10栏“小微企业免税销售额”中；适用增值税差额征收政策的纳税人填写差额后的销售额，差额部分填写在附列资料对应栏次中。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_010", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "9d0fd966625a4d6bba30c106a427835c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "", "sxh": null, "ruleData": "getIf ((fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse) + Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2) >= 0) || fzxx.csxx.zzsYjyhQyBz !== 'Y', round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 ,2) <= round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse) + Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2))", "ruleCode": "getIf ((fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse) + Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2) >= 0) || fzxx.csxx.zzsYjyhQyBz !== 'Y', round ( 001.bqyjse1[1] + 001.bqyjse1[2] ,2) <= round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse) + Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2))", "ruleMessage": "主表“本期数\\货物及劳务”第23栏“本期预缴税额”+主表“本期数\\服务、不动产和无形资产”第23栏“本期预缴税额”应小于等于货物及劳务本期实际预缴税额【round(Number(fzxx.tzxx.fzsbxx.bqysHwYjse),2)】+ 服务、不动产和无形资产本期实际预缴税额【round(Number(fzxx.tzxx.fzsbxx.bqysFwYjse ),2)】。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_088/SB_009_ZB_JKGZ_089", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "c90f1f123b824e679fe9c65e182b9333", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse !== 0, ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc === customGet_jsxmJmxz([*]))\" )", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse !== 0, ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc === customGet_jsxmJmxz([*]))\" )", "ruleMessage": "主表（三）销售使用过的固定资产不含税销售额-货物及劳务本期数不为0时，《减免税申报明细表》减税项目减免性质需要选择“0001129902|SXA031900512|销售旧货（不含二手车经验）、已使用固定资产减征增值税”", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_036（仅提示）", "preTable": "ywbw.sbZzsXgm&&ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "fcf6ca37565649b99bfd9492c422e890", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1", "ruleCode": "001.ckmsxse[1] >= 001.skqjkjdptfpxse1[1]", "ruleMessage": "主表“本期数\\货物及劳务列”第13栏“（五）出口免税销售额”应大于等于第14栏“ 其中：其他增值税发票不含税销售额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_039本期数\\货物及劳务：第13栏必须大于等于第14栏", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "72226196a92f403185c24599329a4144", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse === 0)", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse === 0)", "ruleMessage": "您应税服务属于未超过免税标准申报，【主表】应税服务、不动产和无形资产第3栏应该为0，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_110 当存在冲红非本所属期内的蓝字发票时即czchfbssqptfpdQy=Y， max(货物发票销售额合计，核定销售额合计)+max(服务发票销售额合计,服务核定销售额合计)）≤起征点(QZD)即满足填写校验规则起征点1的情况，那么【主表】应税服务第3栏必须为0", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "50a3b1dcb0ed41d080df10b42eee4cd7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( customGet_Bbqzdbz () === 2 && fzxx.bqxx.gtjy ==='Y', round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse ,2) <= 0 && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 ,2 ) <= 0 || round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 ,2) === getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse ))", "ruleCode": "getIf ( customGet_Bbqzdbz () === 2 && fzxx.bqxx.gtjy ==='Y', round ( 001.yzzzsbhsxse[1] + 001.yzzzsbhsxse[2] - 001.swjgdkdzzszyfpbhsxse[1] - 001.swjgdkdzzszyfpbhsxse[2] + 001.xssygdysgdzcbhsxse[1] ,2) <= 0 && ( round ( 001.xsczbdcbhsxse[2] - 001.swjgdkdzzszyfpbhsxse1[2] ,2 ) <= 0 || round ( 001.xsczbdcbhsxse[2] - 001.swjgdkdzzszyfpbhsxse1[2] ,2) === 001.bdcxse[2] ))", "ruleMessage": "剔除不动产销售额后，您本期销售额未超过免税标准，请将除不动产销售额之外的本期应征增值税销售额（不含开具及代开专用发票销售额）对应填写在【主表】第11栏“未达起征点销售额”中；适用增值税差额征收政策的纳税人填写差额后的销售额，差额部分填写在附列资料对应栏次中。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_007", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "701aeb612b3c4c3e847a37ac9ec139bb", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , ' getIf ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje >= 0 , ywbw.sbFjsf.sbFjsfMx[*].phjmse === round ( ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme ) * ywbw.sbFjsf.sbFjsfMx[*].phjzbl / 100 , 2 ) ) ' )", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , ' getIf ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje >= 0 , ywbw.sbFjsf.sbFjsfMx[*].phjmse === round ( ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme ) * ywbw.sbFjsf.sbFjsfMx[*].phjzbl / 100 , 2 ) ) ' )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】“减征额”计算有误。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "301_*_8_校验1", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "3c0d792c7d7949ee852e88f91a74a0dd", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse > 0 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse <= 0 )", "ruleCode": "getIf ( 001.wdqzdmse[2] > 0 , 001.xwqymse[2] <= 0 )", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第20栏“其中：小微企业免税额”和第21栏“未达起征点免税额”不能同时填写。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_077", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "6f29271079174cb38f0faa02a8bf41ab", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse > 0 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse <= 0 )", "ruleCode": "getIf ( 001.wdqzdmse[1] > 0 , 001.xwqymse[1] <= 0 )", "ruleMessage": "主表“本期数\\货物及劳务”列第20栏“其中：小微企业免税额”和第21栏“未达起征点免税额”不能同时填写。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_076", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d62ee9e518db44eeba812446f154d935", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , 'getIf ( round ( abs ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye ) + abs ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse ) + abs ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse ) , 2 ) > 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc !== \\'\\' ) ')", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , 'getIf ( round ( abs ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye ) + abs ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse ) + abs ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse ) , 2 ) > 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc !== \\'\\' ) ')", "ruleMessage": "《增值税减免税申报明细表》减税项目中”减免性质代码及名称“有空白项，请填写完整后再保存。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_007", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "8bb43c6b87224cc3a215879c85080f3b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2) < 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 < 0, abs ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 ) <= round ( abs (Number(fzxx.tzxx.fzsbxx.bqysFwYjse)),2))", "ruleCode": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2) < 0 && 001.bqyjse1[2] < 0, abs ( 001.bqyjse1[2] ) <= round ( abs (Number(fzxx.tzxx.fzsbxx.bqysFwYjse)),2))", "ruleMessage": "服务、不动产和无形资产：当期预缴余额小于0，申报表主表预缴栏次可以填报负数，但负数绝对值【abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1)】应小于等于当期预缴余额的绝对值【round(abs(Number(fzxx.tzxx.fzsbxx.bqysFwYjse)),2)】。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_23_2_校验2", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "a112250e1aa74e5b992a7d7573e526ce", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'Y' &&   round(customGet_sumB(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) < round(customGet_sumB(), 2) , round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse, 2) === 0)", "ruleCode": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'Y' &&   round(customGet_sumB(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) < round(customGet_sumB(), 2) , round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse, 2) === 0)", "ruleMessage": "当【主表】第2栏、第5栏本期数均为零，且核定销售额未超过免税标准，那么本期应纳税额应当为零，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_106当第2栏、第5栏本期数均为零，且当存在冲红非本所属期内的蓝字发票时即czchfbssqptfpdQy=Y,且核定销售额≤起征点（QZD），且 第1栏+第4栏+第7栏+第9栏+第13栏<核定销售额时，且本期应纳税额不为0，", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "a0ce77ad850845d0a96908e28762a1b4", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , ' getIf ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje < 0 , ywbw.sbFjsf.sbFjsfMx[*].phjmse >= round ( ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme ) * ywbw.sbFjsf.sbFjsfMx[*].phjzbl / 100 , 2 ) && ywbw.sbFjsf.sbFjsfMx[*].phjmse <= 0 ) ' )", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , ' getIf ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje < 0 , ywbw.sbFjsf.sbFjsfMx[*].phjmse >= round ( ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme ) * ywbw.sbFjsf.sbFjsfMx[*].phjzbl / 100 , 2 ) && ywbw.sbFjsf.sbFjsfMx[*].phjmse <= 0 ) ' )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】行减征额不能大于0且不能小于\"（本期应纳税（费）额-减免税（费）额）× 减征比例\"计算的减征额。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "《附表二 附加税费情况表》-第8列“减征额”必须满足小于等于0，大于等于计算值", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "4b2108507a424975b8d9d06c90c5d55e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse >= round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse >= round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2)", "ruleMessage": "主表“本期数\\货物及劳务”列第2栏“增值税专用发票不含税销售额”【getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse】应大于等于货物及劳务类税率1%、3%的专用发票不含税销售额之和【round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2)】，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_005", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "4a8a6f1fc152427f9ffac17feb94110e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.qcye === Number(ytxx.sbZzsXgmFbFlzl.qcye)", "ruleCode": "006.qcye === Number(ytxx.sbZzsXgmFbFlzl.qcye)", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》中第1栏“期初余额”应该等于上期第4栏“期末余额”", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB01_JKGZ_001｜【附表一】第1栏计算公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "137648bc2a91468780eb4c025e18510e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf ( ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === ' ' , ywbw.sbFjsf.sbFjsfMx[*].jme === 0 )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , \" getIf ( 301.ssjmxzDm === ' ' , 301.jme === 0 ) \" )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】“减免性质代码”为空，不得享受减免", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_031｜SB_009_FB02_JKGZ_033｜SB_009_FB02_JKGZ_035", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "497746d828db4c34b5190c819e67e5bc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , 'getIf(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].bqsjkcje >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].msxsedyjxse >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse >0 ,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc !== \\'\\')')", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , 'getIf(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].bqsjkcje >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].msxsedyjxse >0 || ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse >0 ,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc !== \\'\\')')", "ruleMessage": "《增值税减免税申报明细表》免税项目中”免税性质代码及名称“有空白项，请填写完整后再保存。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_023", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "13321534f97c4b94b906ecb9a9afc89f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.bqxx.szlbDm===\"01\"  &&  round( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse), 2 ) === 0, round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2), 2) === 0 )", "ruleCode": "getIf (fzxx.bqxx.szlbDm===\"01\"  &&  round( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse), 2 ) === 0, round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2), 2) === 0 )", "ruleMessage": "您未认定服务、不动产和无形资产的税费种信息，且填写了5%征收率栏次的数据，目前暂不支持申报，有疑问请与主管税务机关联系。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_ips89577新增校验", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d2519cbcc7e74162ad953705582c59ae", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( !((fzxx.bqxx.szlbDm===\"01\") || (fzxx.bqxx.szlbDm===\"03\") || (fzxx.bqxx.bczzzssfzrdqy===\"Y\")) , round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse1)+  abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse) , 2 ) === 0 )", "ruleCode": "getIf ( !((fzxx.bqxx.szlbDm===\"01\") || (fzxx.bqxx.szlbDm===\"03\") || (fzxx.bqxx.bczzzssfzrdqy===\"Y\")) , round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse1)+  abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse) , 2 ) === 0 )", "ruleMessage": "《增值税及附加税费申报表（小规模纳税人适用）》，您为应税服务纳税人，但“货物及劳务-本期数”列填写了数值，请核实是否填写正确？若实际发生应税服务行为方需填写，否则无需填写。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TYGZ_003", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d14fde2f2c1846879ebd27eeaa41f622", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec (1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf (fzxx.bqxx.gtgsh === 'Y' && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc, !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013612') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013613') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001011814'))\")", "ruleCode": "rangeExec (1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf (fzxx.bqxx.gtgsh === 'Y' && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc, !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013612') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013613') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001011814'))\")", "ruleMessage": "您为个体工商户，《增值税减免税申报明细表》不可选择减免性质代码为【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.split('|')[0]】的减免。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "个体工商户减税项目校验2", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "0f61be773f2d44e2831bd744cd254e0b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.yqwsqdjybnsrzgdqy ==='Y', getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0)", "ruleCode": "getIf(fzxx.bqxx.yqwsqdjybnsrzgdqy ==='Y', getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0)", "ruleMessage": "您是逾期未认定企业，【主表】第10、11栏应税服务、不动产和无形资产必须都为0，请检查！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_012", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "77ebb9a676cb412b82bc0e9b79b71e74", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(customGet_Bbqzdbz() === 3,getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse >= round(fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj,2))", "ruleCode": "getIf(customGet_Bbqzdbz() === 3,getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse >= round(fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj,2))", "ruleMessage": "提示信息：主表“本期数\\服务、不动产和无形资料”列第3栏“其他增值税发票不含税销售额”【getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse】应大于等于服务、不动产和无形资产税率1%、3%的普通发票不含税销售额【round(fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj,2)】，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_010", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "204fe56bb0ba474299bee221a2fc9195", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "!checkRepeat(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,\"hmc\",\"swsxDm\")", "ruleCode": "!checkRepeat(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,\"hmc\",\"swsxDm\")", "ruleMessage": "《增值税减免税申报明细表》免税性质代码及名称不能重复。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_020《增值税减免税申报明细表》免税性质代码及名称不能重复。", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "1fb6ad0a04ee4fe0a505d2368056851a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse !== 0 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse <= round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse, 2))", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && 001.bdcxse[2] !== 0 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse <= round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse, 2))", "ruleMessage": "主表“本期销售不动产销售额”应该小于等于第4行第2列“应征增值税不含税销售额（5%征收率）-服务、不动产和无形资产”+第9行第2列“（四）免税销售额-服务、不动产和无形资产”，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_001", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "ddec59009124498f93cabf086af214e3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf ( ! ! ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm && ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30203 ' && max ( customGet_sumA ( ) , customGet_sumB ( ) ) > (Number(sbsx.nsqxDm) - 5) * 100000 , ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== ' 0061042802 ' )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf ( ! ! ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm && ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30203 ' && max ( customGet_sumA ( ) , customGet_sumB ( ) ) > (Number(sbsx.nsqxDm) - 5) * 100000 , ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== ' 0061042802 ' )\")", "ruleMessage": "您不满足减免性质代码为0061042802的减免条件，请核实！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_059", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "a99e45c0c7464887bb9b22a49d998080", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec (   0,   ywbw.sbFjsf.sbFjsfMx.length,   \"getIf(     ywbw.sbFjsf.sbFjsfMx[*].bqynsfe < 0 && ywbw.sbFjsf.sbFjsfMx[*].jmzlxDm === '01',     ywbw.sbFjsf.sbFjsfMx[*].jme === 0   )\" )", "ruleCode": "rangeExec (   0,   ywbw.sbFjsf.sbFjsfMx.length,   \"getIf(     ywbw.sbFjsf.sbFjsfMx[*].bqynsfe < 0 && ywbw.sbFjsf.sbFjsfMx[*].jmzlxDm === '01',     ywbw.sbFjsf.sbFjsfMx[*].jme === 0   )\" )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】“减免性质代码”不为空，“减免税额”必须等于0。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_062|SB_009_FB02_JKGZ_063|SB_009_FB02_JKGZ_064当本期应纳税额小于0 时， 若选择的减免性质不为免税，那么“减免税额”必须等于 0，否则提示具体的提示同变更前的模式进行提示。", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "a8d954f65ff342938802c046886217bb", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( customGet_Bbqzdbz() === 2 ,( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse , 2 ) > 0 ) || ( ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 , 2 ) > 0 ) && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 , 2 ) !== getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse ) ))", "ruleCode": "getIf ( customGet_Bbqzdbz() === 2 ,( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse , 2 ) > 0 ) || ( ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 , 2 ) > 0 ) && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 , 2 ) !== getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse ) ))", "ruleMessage": "剔除不动产销售额后，本期销售额未超过免税标准，请继续申报。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_018", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "1c7ec38dbe2946589fbe4c6c2585cfc4", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,053", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 <= round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj , 2 )", "ruleCode": "053.bqyjzzs1 <= round ( 001.ynsehj[1] + 001.ynsehj[2] , 2 )", "ruleMessage": "《电力企业增值税销项税额和进项税额传递单》中的“本期应缴增值税”应小于等于主表第24栏“应纳税额合计”的”本期数“两列合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB05_JKGZ_003", "preTable": "ywbw.sbZzsXgmFbDlqyjxhxx&&ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "4fd11753276e42359403a44dc27e5a2a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( !((fzxx.bqxx.szlbDm===\"02\") || (fzxx.bqxx.szlbDm===\"03\") || (fzxx.bqxx.bczzzssfzrdqy===\"Y\")), round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpxse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqybtse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) ,2 ) === 0.00 )", "ruleCode": "getIf ( !((fzxx.bqxx.szlbDm===\"02\") || (fzxx.bqxx.szlbDm===\"03\") || (fzxx.bqxx.bczzzssfzrdqy===\"Y\")), round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpxse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqybtse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) ,2 ) === 0.00 )", "ruleMessage": "《增值税及附加税费申报表（小规模纳税人适用）》，您为货物劳务纳税人，但服务、不动产和无形资产-本期数”列填写了数值，请核实是否填写正确？若实际销售货物或提供应税劳务方需填写，否则无需填写。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TYGZ_004", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "a6e40c43e54a4daea9d5cbb3ae0d5701", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz() === 1 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse === 0)", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz() === 1 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse === 0)", "ruleMessage": "您应税货物及劳务属于未超过免税标准申报，那么【主表】应税货物及劳务第3栏必须为0，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_098", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "2e44e5eb55d74992a1248ce24a2ae612", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( fzxx.bqxx.gtjy === 'Y' && customGet_Bbqzdbz () === 1 , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse , 2 ) <= 0 )", "ruleCode": "getIf ( fzxx.bqxx.gtjy === 'Y' && customGet_Bbqzdbz () === 1 , round ( 001.yzzzsbhsxse[1] + 001.yzzzsbhsxse[2] - 001.swjgdkdzzszyfpbhsxse[1] - 001.swjgdkdzzszyfpbhsxse[2] + 001.xsczbdcbhsxse[2] - 001.swjgdkdzzszyfpbhsxse1[2] + 001.xssygdysgdzcbhsxse[1] , 2 ) <= 0 )", "ruleMessage": "您本期销售额未超过免税标准，请将本期应征增值税销售额（不包括开具或代开专用发票销售额）对应填写在【主表】第11栏“未达起征点销售额”中；适用增值税差额征收政策的纳税人填写差额后的销售额，差额部分填写在附列资料对应栏次中。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_009", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d8fa8d5de95f44ca959ad333e319a938", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse) <= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse * 0.05) , 2 ) ) && ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse) >= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse * 0.03) , 2 ) )", "ruleCode": "( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse) <= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse * 0.05) , 2 ) ) && ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse) >= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse * 0.03) , 2 ) )", "ruleMessage": "第19栏“本期免税额”需要您自行填写，填写数值范围在服务、不动产和无形资产列第9栏【“（四）免税销售额”*3%，“（四）免税销售额”*5%】之间。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_069", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "27694ef2e51d4516b6dda0633f84158c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf (ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30216 ' && max(customGet_sumA() , customGet_sumB()) <= (Number(sbsx.nsqxDm) - 5) * 100000 && ywbw.sbFjsf.sbFjsfMx[*].bqynsfe > 0 , !( ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== '0099042802' || ywbw.sbFjsf.sbFjsfMx[*].jme !== ywbw.sbFjsf.sbFjsfMx[*].bqynsfe ) )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf (ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30216 ' && max(customGet_sumA() , customGet_sumB()) <= (Number(sbsx.nsqxDm) - 5) * 100000 && ywbw.sbFjsf.sbFjsfMx[*].bqynsfe > 0 , !( ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== '0099042802' || ywbw.sbFjsf.sbFjsfMx[*].jme !== ywbw.sbFjsf.sbFjsfMx[*].bqynsfe ) )\")", "ruleMessage": "您满足减免性质代码为0099042802的减免条件，请选择该减免性质代码后再申报！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_060", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "b3257232533b4f08920ca344c9bd47e6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008,301", "sxh": null, "ruleData": "customGet_zdrqts() === 'all'", "ruleCode": "customGet_zdrqts() === 'all'", "ruleMessage": "没有采集创业重点群体人员信息表的登记失业半年以上人员，零就业家庭、享受城市低保登记失业人员，毕业年度内高校毕业生从事个体经营人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "重点群体校验2", "preTable": "ywbw.sbZzsFbZzsjmssbmxb&&ywbw.sbFjsf", "passResult": false, "syqyList": null}, {"ruleXh": "7e5ee0169e9242629ab8754b2a57027b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse) >= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse) , 2 )", "ruleCode": "abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse) >= round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse) , 2 )", "ruleMessage": "主表“本期数\\货物及劳务”列第19栏“本期免税额”应大于等于第20栏”其中：小微企业免税额“、第21栏”未达起征点免税额“的金额之和。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_066 本栏次必须大于等于第20栏”其中：小微企业免税额“、第21栏”未达起征点免税额“的金额之和", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e6522e154b8f4e68bf2175216da62884", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "( ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 ) >= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.05 , 2 ) ) ) && ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 ) <= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.015 , 2 ) ) ) )", "ruleCode": "( ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 ) >= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.05 , 2 ) ) ) && ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 ) <= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.015 , 2 ) ) ) )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第16栏“不含税销售额”应在第15栏“含税销售额”÷（1+5%）与第15栏“含税销售额”÷（1+1.5%）之间", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB01_JKGZ_016", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "b00f51e03ae84e11942b731f55f7a21b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse === 0)", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse === 0)", "ruleMessage": "您应税货物及劳务属于未超过免税标准申报，那么【主表】应税货物及劳务第3栏应该为0，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_109当存在冲红非本所属期内的蓝字发票时即czchfbssqptfpdQy=Y，max(货物发票销售额合计，核定销售额合计)+max(服务发票销售额合计,服务核定销售额合计)）≤起征点(QZD)即满足填写校验规则起征点1的情况，那么【主表】应税货物及劳务第3栏必须为0", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "266adfa3a8224f2bb2aae116d88838bc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 >= round(fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj,2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 >= round(fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj,2)", "ruleMessage": "提示信息：主表“本期数\\服务、不动产和无形资产”列第5栏“增值税专用发票不含税销售额”【getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1】应大于等于服务、不动产和无形资产税率5%、1.5%的专用发票不含税销售额【round(fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj,2)】，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_015", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "ae4e5552bc4d4eccbcb5dc3f0b8b0bc9", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "getIf ( ! ! ywbw.sbFjsf.sbFjsfQtxx.bchssqz , judgeTime ( ywbw.sbFjsf.sbFjsfQtxx.bchssqz , sbsx.skssqz ,\"<=\") )", "ruleCode": "getIf ( ! ! 301.bchssqz , judgeTime ( 301.bchssqz , sbsx.skssqz , \" <= \" ) )", "ruleMessage": "被冲红所属期止不能大于申报表所属期止！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_055", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "7bc1f0709ad240bb8fa8f05945b01361", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec (1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf (fzxx.bqxx.gtgsh === 'N' && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc, !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013610') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013611') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001011813'))\")", "ruleCode": "rangeExec (1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf (fzxx.bqxx.gtgsh === 'N' && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc, !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013610') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001013611') && !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001011813'))\")", "ruleMessage": "您不为个体工商户，《增值税减免税申报明细表》不可选择减免性质代码为【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.split('|')[0]】的减免。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "个体工商户减税项目校验1", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "e2069469d2794dada7a303d22d504f9a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse !== 0 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse <= round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse, 2))", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'Y' && 001.bdcxse[2] !== 0 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse <= round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse, 2))", "ruleMessage": "主表“本期销售不动产销售额”应该小于等于第4行第2列“应征增值税不含税销售额（5%征收率）-服务、不动产和无形资产”+第9行第2列“（四）免税销售额-服务、不动产和无形资产”，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_104当存在冲红非本所属期内的蓝字发票时即czchfbssqptfpdQy=Y,本栏次应该小于等于第4行第2列“应征增值税不含税销售额（5%征收率）+第9行第2列“（四）免税销售额”", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "abf0b8fd48a447e88ba1ce27cf384f9b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "!checkRepeat(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,\"hmc\",\"swsxDm\")", "ruleCode": "!checkRepeat(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,\"hmc\",\"swsxDm\")", "ruleMessage": "《增值税减免税申报明细表》减税性质代码及名称不能重复。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_002《增值税减免税申报明细表》减税性质代码及名称不能重复。", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "ac8bc687c57e479a90b9c159f6cbe104", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,008", "sxh": null, "ruleData": "getIf ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse , 2 ) >= 0 , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse , 2 ) === getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').mzzzsxmxse )", "ruleCode": "getIf ( round ( 001.qtmsxse[1] + 001.qtmsxse[2] , 2 ) >= 0 , round ( 001.qtmsxse[1] + 001.qtmsxse[2] , 2 ) === 008.mzzzsxmxse[1] )", "ruleMessage": "主表第12栏“其他免税销售额”本期数之和应等于“《增值税减免税申报明细表》中“免税项目”合计栏“免征增值税项目销售额”的合计值。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_036【主表】第12行“其他免税销售额”“本期数”应等于【增值税减免税申报明细表】中免征增值税项目销售额“合计”", "preTable": "ywbw.sbZzsXgm&&ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "566c367dcb4a4d19bca6646dd3a2a62a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "getIf(!!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz, !!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz && compareDateSameYear(ywbw.sbFjsf.sbFjsfQtxx.bchssqq, ywbw.sbFjsf.sbFjsfQtxx.bchssqz))", "ruleCode": "getIf(!!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz, !!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz && compareDateSameYear(ywbw.sbFjsf.sbFjsfQtxx.bchssqq, ywbw.sbFjsf.sbFjsfQtxx.bchssqz))", "ruleMessage": "被冲红所属期起和被冲红所属期止必须在同一年！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_054", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "55129bdc41474af887a3861b5f15ad02", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse === 0)", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse === 0)", "ruleMessage": "您应税服务属于未超过免税标准申报，【主表】应税服务、不动产和无形资产第3栏必须为0，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_099", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "8779ccd1fde44fb3b465371002c84000", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'Y' &&   round(customGet_sumA(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) >= round(customGet_sumB(), 2) , round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse, 2) === 0)", "ruleCode": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'Y' &&   round(customGet_sumA(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) >= round(customGet_sumB(), 2) , round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse, 2) === 0)", "ruleMessage": "当【主表】第2栏、第5栏本期数均为零，且第1栏+第4栏+第7栏+第9栏+第13栏未超过免税标准，那么本期应纳税额应当为零，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_105当第2栏、第5栏本期数均为零，且当存在冲红非本所属期内的蓝字发票时即czchfbssqptfpdQy=Y,且第1栏+第4栏+第7栏+第9栏+第13栏≤起征点（QZD），且 第1栏+第4栏+第7栏+第9栏+第13栏>=核定销售额时，且本期应纳税额不为0", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "87cacde587e541f6be76f30863f4de3f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008,301", "sxh": null, "ruleData": "customGet_tysbts() === ''", "ruleCode": "customGet_tysbts() === ''", "ruleMessage": "【customGet_tysbts()】", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "退役士兵校验1", "preTable": "ywbw.sbFjsf&&ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "cb333ab1f38a4b8e81302cbdd0512a2d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.bqxx.gtjy === 'N' && fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz () === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse === getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse )", "ruleCode": "getIf (fzxx.bqxx.gtjy === 'N' && fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz () === 1, 001.yzzzsbhsxse[1] === 001.swjgdkdzzszyfpbhsxse[1] )", "ruleMessage": "您是属于【小微企业】，可享受的免税销售额，请将销售额调整到主表“本期数\\货物及劳务”的第10栏“其中：小微企业免税销售额”，如您开具或者代开了专用发票，应该填写在第2栏，并且第1栏和第2栏应该相等，请核实。", "messagePriority": null, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_108", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "b86dbe4eb40b4ab29998c0ec4b22f63d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec(0, ywbw.sbFjsf.sbFjsfMx.length, \"getIf(fzxx.bqxx.gtgsh === 'N',['0007013611','0007013610','0007011803','0061013609','0061013608','0061011803','0099013602','0099013601','0099011801'].indexOf(ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm) < 0)\")", "ruleCode": "rangeExec(0, ywbw.sbFjsf.sbFjsfMx.length, \"getIf(fzxx.bqxx.gtgsh === 'N',['0007013611','0007013610','0007011803','0061013609','0061013608','0061011803','0099013602','0099013601','0099011801'].indexOf(ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm) < 0)\")", "ruleMessage": "您不为个体工商户，《增值税及附加税费申报表（小规模纳税人适用）附列资料（二）》不可选择减免性质代码为【ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm】的减免。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "附表二个体工商户校验1", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "b796509be85f4496b6944b90c8fece99", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec (   0,   ywbw.sbFjsf.sbFjsfMx.length,   \"getIf(     ywbw.sbFjsf.sbFjsfMx[*].bqynsfe < 0 && ywbw.sbFjsf.sbFjsfMx[*].jmzlxDm === '02',     ywbw.sbFjsf.sbFjsfMx[*].jme === ywbw.sbFjsf.sbFjsfMx[*].bqynsfe   )\" )", "ruleCode": "rangeExec (   0,   ywbw.sbFjsf.sbFjsfMx.length,   \"getIf(     ywbw.sbFjsf.sbFjsfMx[*].bqynsfe < 0 && ywbw.sbFjsf.sbFjsfMx[*].jmzlxDm === '02',     ywbw.sbFjsf.sbFjsfMx[*].jme === ywbw.sbFjsf.sbFjsfMx[*].bqynsfe   )\" )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】“减免性质代码”不为空，“减免税额”必须等于“本期应纳税额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_062|SB_009_FB02_JKGZ_063|SB_009_FB02_JKGZ_064当本期应纳税额小于0 时， 若选择的减免性质为免税【根据减免性质的接口进行返回】， “减免税额”必须等于 本期应纳税额", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "41ce0a3c5b6c4d2ba1b51a0c95f628d7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "", "sxh": null, "ruleData": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2) >= 0, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 >= 0)", "ruleCode": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysFwYjse),2) >= 0, 001.bqyjse1[2] >= 0)", "ruleMessage": "服务、不动产和无形资产：当期预缴余额大于等于0，申报表主表预缴栏次不能填报负数。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_23_2_校验1", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d8dadebe40b3425ab7ea2a5b8255d5d3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.bqxx.gtjy === 'N' && fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz () === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse === getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse )", "ruleCode": "getIf (fzxx.bqxx.gtjy === 'N' && fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz () === 1, 001.yzzzsbhsxse[1] === 001.swjgdkdzzszyfpbhsxse[1] )", "ruleMessage": "您是属于【小微企业】，可享受的免税销售额，请将销售额调整到主表“本期数\\货物及劳务”的第10栏“其中：小微企业免税销售额”，如您开具或者代开了专用发票，请填写在第2栏，并且第1栏和第2栏需要相等。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_103", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "b747323953df4c4bb40a44d2ac14fcfe", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse) > round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse * 0.05) , 2 ) ) || (abs( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse) < round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse * 0.03) , 2 ) )", "ruleCode": "( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse) > round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse * 0.05) , 2 ) ) || (abs( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse) < round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse * 0.03) , 2 ) )", "ruleMessage": "第20栏“其中:小微企业免税额”需要您自行填写，填写数值范围在服务、不动产和无形资产列第10栏【“其中: 小微企业免税销售额”*3%，“其中: 小微企业免税销售额”*5%】之间。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_073", "preTable": "ywbw.sbZzsXgm", "passResult": false, "syqyList": null}, {"ruleXh": "b7107389bb104b4caa72f02024efc9f5", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,008", "sxh": null, "ruleData": "getIf ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze , 2 ) >= 0 , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze , 2 ) === getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqsjdjse )", "ruleCode": "getIf ( round ( 001.bqyn<PERSON><PERSON><PERSON>[1] + 001.bqyn<PERSON><PERSON><PERSON>[2] , 2 ) >= 0 , round ( 001.bqynsej<PERSON>[1] + 001.bqyn<PERSON>j<PERSON>[2] , 2 ) === 008.bqsjdjse[1] )", "ruleMessage": "主表“本期数\\货物及劳务”、“本期数\\服务、不动产和无形资产”两列的第18栏“本期应纳税额减征额”合计应等于《增值税减免申报明细表》的第1部分“一、减税项目”中第4栏“本期实际抵减税额”栏的合计数，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_061【主表】“本期应纳税额减征额”“本期数”应等于【增值税减免税申报明细表】中减税项目本期实际抵减税额“合计”", "preTable": "ywbw.sbZzsXgm&&ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "b562b0de96854dea91d5083f2e9750e3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse >= round(fzxx.tzxx.fzsbxx.zyfpFw1xsehj + fzxx.tzxx.fzsbxx.zyfpFw3xsehj ,2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse >= round(fzxx.tzxx.fzsbxx.zyfpFw1xsehj + fzxx.tzxx.fzsbxx.zyfpFw3xsehj ,2)", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第2栏“增值税专用发票不含税销售额”【getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse】应大于等于服务、不动产和无形资产税率1%、3%的专用发票不含税销售额之和【round(fzxx.tzxx.fzsbxx.zyfpFw1xsehj+fzxx.tzxx.fzsbxx.zyfpFw3xsehj,2)】，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_006", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e7a535564b4c430581f812d9b8d1bccd", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.jdcfphdnsr === 'N' && substr(nsrxx.zgswjDm,0,3) === '113',getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse)", "ruleCode": "getIf(fzxx.bqxx.jdcfphdnsr === 'N' && substr(nsrxx.zgswjDm,0,3) === '113',getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse)", "ruleMessage": "第1栏“（一）应征增值税不含税销售额（3%征收率）”服务、不动产和无形资产列本期数应大于等于第2栏“增值税专用发票不含税销售额”服务、不动产和无形资产列本期数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_1_2_校验", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "170c72770e93441891712a258285a1f9", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( fzxx.bqxx.gtjy === \" Y \" , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 )", "ruleCode": "getIf ( fzxx.bqxx.gtjy === \" Y \" , 001.xwqymsxse[1] === 0 && 001.xwqymsxse[2] === 0 )", "ruleMessage": "主表第10栏“其中：小微企业免税销售额”不能填写数据。个体工商户未达起征点的销售额，应填入第11栏“未达起征点销售额”。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_026/SB_009_ZB_JKGZ_027", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "5b6c8970728b4857833d609031d5ddb2", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf ( ! ! ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm && ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30216 ' && max ( customGet_sumA ( ) , customGet_sumB ( ) ) > (Number(sbsx.nsqxDm) - 5) * 100000 , ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== ' 0099042802 ' )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf ( ! ! ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm && ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30216 ' && max ( customGet_sumA ( ) , customGet_sumB ( ) ) > (Number(sbsx.nsqxDm) - 5) * 100000 , ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== ' 0099042802 ' )\")", "ruleMessage": "您不满足减免性质代码为0099042802的减免条件，请核实！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_061", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "c147e10c975046899fde23ce0c957c35", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf (ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30203 ' && max (customGet_sumA() , customGet_sumB()) <= (Number(sbsx.nsqxDm) - 5) * 100000 && ywbw.sbFjsf.sbFjsfMx[*].bqynsfe > 0 , ! ( ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== ' 0061042802 ' || ywbw.sbFjsf.sbFjsfMx[*].jme !== ywbw.sbFjsf.sbFjsfMx[*].bqynsfe ) )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf (ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === ' 30203 ' && max (customGet_sumA() , customGet_sumB()) <= (Number(sbsx.nsqxDm) - 5) * 100000 && ywbw.sbFjsf.sbFjsfMx[*].bqynsfe > 0 , ! ( ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm !== ' 0061042802 ' || ywbw.sbFjsf.sbFjsfMx[*].jme !== ywbw.sbFjsf.sbFjsfMx[*].bqynsfe ) )\")", "ruleMessage": "您满足减免性质代码为0061042802的减免条件，请选择该减免性质代码后再申报！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_058", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "c1432672610e4efda7769d9351f65c39", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec (1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf (fzxx.bqxx.gtgsh === 'N' && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc, !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001011705'))\")", "ruleCode": "rangeExec (1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \"getIf (fzxx.bqxx.gtgsh === 'N' && ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc, !ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc.includes('0001011705'))\")", "ruleMessage": "仅个体纳税人允许在《增值税减免税申报明细表》减税项目中”减免性质代码及名称“中填写“0001011705|SXA031900839|个人出租住房应按照5%的征收率减按1.5%计算应纳增值税|《财政部 国家税务总局关于全面推开营业税改征增值税试点的通知》 财税〔2016〕36号附件2第一条第（九）款第6项”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_004", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "be24014d613c47928bc0beacbcb5928f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz () === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse === getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse )", "ruleCode": "getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.bqxx.czchfbssqptfpdQy === 'Y' && customGet_Bbqzdbz () === 1, 001.yzzzsbhsxse[1] === 001.swjgdkdzzszyfpbhsxse[1] )", "ruleMessage": "您是属于【个体工商户和其他个人】，可享受的免税销售额，请将销售额调整到主表“本期数\\货物及劳务”的第11栏“未达起征点销售额”，如您开具或者代开了专用发票，应该填写在第2栏，并且第1栏和第2栏应该相等，请核实。", "messagePriority": null, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_107", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "c09062d6c857422cb05fc00b35fe9197", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdynse ) , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdynse ) , 2 )", "ruleMessage": "【主表】“核定应纳税额”的“本期数”的“货物及劳务”从核定取值不正确", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_057【主表】核定应纳税额，货物及劳务本期数从核定取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "bf4e9d2c80a244379b949ad41f4cd51c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'N' &&   round(customGet_sumB(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) < round(customGet_sumB(), 2) , round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse, 2) === 0)", "ruleCode": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'N' &&   round(customGet_sumB(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) < round(customGet_sumB(), 2) , round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse, 2) === 0)", "ruleMessage": "当【主表】第2栏、第5栏本期数均为零，且核定销售额未超过免税标准，那么本期应纳税额应当为零，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_020", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "bffeeb0f74c345b386a7ed757ef17877", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.jdcfphdnsr === 'N' && substr(nsrxx.zgswjDm,0,3) === '113',getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse)", "ruleCode": "getIf(fzxx.bqxx.jdcfphdnsr === 'N' && substr(nsrxx.zgswjDm,0,3) === '113',getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse)", "ruleMessage": "第1栏“（一）应征增值税不含税销售额（3%征收率）”货物及劳务列本期数应大于等于第2栏“增值税专用发票不含税销售额”货物及劳务列本期数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_1_1_校验", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "bfbd7209df7645ca8443db4af1702b02", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdynse ) , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdynse ) , 2 )", "ruleMessage": "【主表】“核定应纳税额”的“本期数”的“服务、不动产和无形资产”从核定取值不正确", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_058【主表】核定应纳税额，服务、不动产和无形资产本期数从核定取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "00d03f6b75214db7a6330ea12f30e076", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(customGet_Bbqzdbz() === 3,getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 >= round(fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj,2))", "ruleCode": "getIf(customGet_Bbqzdbz() === 3,getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 >= round(fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj,2))", "ruleMessage": "提示信息：主表“本期数\\服务、不动产和无形资产”列第6栏“其他增值税发票不含税销售额”【getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2】应大于等于服务、不动产和无形资产税率5%、1.5%的普通发票不含税销售额【round(fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj,2)】，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_017", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "48f77eb7885e4409912ed8584443ab9b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.bqxx.gtjy === 'N', getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0)", "ruleCode": "getIf (fzxx.bqxx.gtjy === 'N', 001.wdqzdxse[1] === 0 && 001.wdqzdxse[2] === 0)", "ruleMessage": "主表第11栏“未达起征点销售额”不能填写数据。小微企业销售额未达起征点的，应填入第10栏“其中：小微企业免税销售额”。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_031/SB_009_ZB_JKGZ_032", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "5979a35a695a4bfba6bbf7a61a87820d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "( ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse ) >= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse / 1.03 , 2 ) ) ) && ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse ) <= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse / 1.01 , 2 ) ) ) )", "ruleCode": "( ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse ) >= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse / 1.03 , 2 ) ) ) && ( abs ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse ) <= abs ( round ( ywbw.sbZzsXgmFbFlzl.ysfwxshsxse / 1.01 , 2 ) ) ) )", "ruleMessage": "增值税申报附列资料一第8栏“不含税销售额”，应在第7栏“含税销售额”÷（1+3%） 与第7栏“含税销售额”÷(1+1%)之间", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB01_JKGZ_008第8栏的“不含税销售额”的校验", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "59491cc74a71491ead3bcca6fc322517", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf( customGet_Bbqzdbz() === 1 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse === 0)", "ruleCode": "getIf( customGet_Bbqzdbz() === 1 , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse === 0)", "ruleMessage": "应税货物及劳务属于未超过免税标准申报，应税货物及劳务第7栏必须为0，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_101", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "57aa4c1e892c4d7e80992f9ca77ee64c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'N' &&   round(customGet_sumA(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) >= round(customGet_sumB(), 2),   abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse) === 0 )", "ruleCode": "getIf(   round(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1), 2) === 0 &&   fzxx.bqxx.czchfbssqptfpdQy === 'N' &&   round(customGet_sumA(), 2) <= fzxx.tzxx.fzsbxx.qzd &&   round(customGet_sumA(), 2) >= round(customGet_sumB(), 2),   abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse) === 0 )", "ruleMessage": "当【主表】第2栏、第5栏本期数均为零，且第1栏+第4栏+第7栏+第9栏+第13栏未超过免税标准，那么本期应纳税额应当为零，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_019", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "8b3f370438db42d1904bd49d002e372b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse >= 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze > 0, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze <= max ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse ,  getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse))", "ruleCode": "getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse >= 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze > 0, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze <= max ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse ,  getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse))", "ruleMessage": "当“本期数\\服务、不动产和无形资产”列第16栏“本期应纳税额”大于等于0时，主表“本期数\\服务、不动产和无形资产”列第18栏“本期应纳税额减征额”应小于等于第16栏“本期应纳税额”和第17栏“核定应纳税额”中的较大者。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_063本栏次必须小于等于第16栏“本期应纳税额”和隐藏栏2“核定应纳税额”中的较大者。", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "bd3c3cb58d0240feb89132f1f3459746", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.qcye5 === Number(ytxx.sbZzsXgmFbFlzl.qcye5)", "ruleCode": "006.qcye5 === Number(ytxx.sbZzsXgmFbFlzl.qcye5)", "ruleMessage": "增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第9栏“期初余额”应等于上期第12栏“期末余额”", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB01_JKGZ_009｜附表1第9栏自动取值", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "f0d86b691f28404283755bdf3addc7b7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,008", "sxh": null, "ruleData": "getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse !== 0 || getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse !== 0 ||  getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze !== 0 || getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze !== 0 ,doCatch ('ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[3].hmc') !== 0 || doCatch ('ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[1].hmc') !== 0)", "ruleCode": "getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse !== 0 || getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse !== 0 ||  getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze !== 0 || getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze !== 0 ,doCatch ('ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[3].hmc') !== 0 || doCatch ('ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[1].hmc') !== 0)", "ruleMessage": "【主表】第12栏“其他免税销售额”“本期数”或第18栏“本期应纳税额减征额”“本期数”有数据时，必须要填写增值税减免税申报明细表。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_016", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "6668aa72744f43fcb88c4b9fa7534e8a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec(0, ywbw.sbFjsf.sbFjsfMx.length, \"getIf(fzxx.bqxx.gtgsh === 'Y',['0007013612','0007013613','0007011804','0061013610','0061013611','0061011804','0099013603','0099013604','0099011802'].indexOf(ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm) < 0)\")", "ruleCode": "rangeExec(0, ywbw.sbFjsf.sbFjsfMx.length, \"getIf(fzxx.bqxx.gtgsh === 'Y',['0007013612','0007013613','0007011804','0061013610','0061013611','0061011804','0099013603','0099013604','0099011802'].indexOf(ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm) < 0)\")", "ruleMessage": "您为个体工商户，《增值税及附加税费申报表（小规模纳税人适用）附列资料（二）》不可选择减免性质代码为【ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm】的减免。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "附表二个体工商户校验2", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "63a6634b9dab43c8b15a6041f1b06ec0", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1) > 0 && customGet_sumB() <= fzxx.tzxx.fzsbxx.qzd && customGet_sumA() < customGet_sumB(),getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse <= round( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse * 0.03,2) && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse <= round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 * 0.05,2),2))", "ruleCode": "getIf(abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse) + abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1) > 0 && customGet_sumB() <= fzxx.tzxx.fzsbxx.qzd && customGet_sumA() < customGet_sumB(),getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse <= round( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse * 0.03,2) && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse <= round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 * 0.05,2),2))", "ruleMessage": "当【主表】第2栏+第5栏>0，且核定销售额未超过免税标准时，第16栏“本期应纳税额”应小于等于“第2栏×征收率+第5栏×征收率”，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_022", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "2e7615badcbb4465a7fa3df9c009836c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "getIf ( ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz ===\"3\", getLength ( ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy ) >= 10 && getLength ( ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy ) <= 1000 )", "ruleCode": "getIf ( ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz ===\"3\", getLength ( ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy ) >= 10 && getLength ( ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy ) <= 1000 )", "ruleMessage": "《附表二 附加税费情况表》计税依据修改原因选择其他时，\"其他修改原因\"为必录项，不少于10个字符，最长不超过1000字符。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "其他修改原因", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "62fe1f130467429a92ddc937bfef46ae", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(customGet_Bbqzdbz() === 3,getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse >= round(fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj,2))", "ruleCode": "getIf(customGet_Bbqzdbz() === 3,getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse >= round(fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj,2))", "ruleMessage": "主表“本期数\\货物及劳务”列第3栏“其他增值税发票不含税销售额”【getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse】应大于等于货物及劳务类税率1%、3%的普通发票不含税销售额【round(fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj,2)】，请核实。", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_009", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "96053f07d66c4a2494c9726271e00a87", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "getIf ( ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 ) && ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0 ) && ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 > 0 ) , abs(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5,2)) <= 0.1 )", "ruleCode": "getIf ( ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === 0 ) && ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === 0 ) && ( ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 > 0 ) , abs(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5,2)) <= 0.1 )", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第4栏“（二）应征增值税不含税销售额（5%征收率）”应等于本期《增值税及附加税费申报表（小规模纳税人适用）附列资料（一）》第16栏“应税行为（5%征收率）计税销售额计算\\不含税销售额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_013", "preTable": "ywbw.sbZzsXgm&&ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "631f789401fa4ff6b6852c0cb53abf79", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec(1,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length,\"customGet_jsxmQmyecheck(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qmye) === ''\")", "ruleCode": "rangeExec(1,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length,\"customGet_jsxmQmyecheck(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qmye) === ''\")", "ruleMessage": "《增值税减免税申报明细表》减免性质代码减税代码【customGet_jsxmQmyecheck(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye,ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qmye)】增值税对应的第5列期末余额不为0，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_037", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "2b3e99ec40554cbeb96cbf324aa5bd0b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpxse1", "ruleCode": "001.ckmsxse[2] >= 001.skqjkjdptfpxse1[2]", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”第13栏“（五）出口免税销售额”应大于等于第14栏“其中：其他增值税发票不含税销售额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_040", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "9441eab0b8a949789fd144537db2f122", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf(!ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz,ywbw.sbFjsf.sbFjsfMx[*].ybzzs === customGet_fjsJsyj ( ywbw.sbFjsf.sbFjsfMx[*].zsxmDm ))\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"getIf(!ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz,ywbw.sbFjsf.sbFjsfMx[*].ybzzs === customGet_fjsJsyj ( ywbw.sbFjsf.sbFjsfMx[*].zsxmDm ))\")", "ruleMessage": "【customGet_fjsJsyjTsxx(ywbw.sbFjsf.sbFjsfMx[*].zsxmDm)】", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_003｜SB_009_FB02_JKGZ_004｜SB_009_FB02_JKGZ_005", "preTable": "ywbw.sbFjsf&&ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "616412b1570d4313959044f3ae51c3f9", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "round(Number(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse)+Number(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse),2) <= round(Number(fzxx.tzxx.fzsbxx.qzd),2)", "ruleCode": "round(Number(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse)+Number(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse),2) <= round(Number(fzxx.tzxx.fzsbxx.qzd),2)", "ruleMessage": "主表第11栏“未达起征点销售额”的本期数之和应小于等于增值税起征点。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_033", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "9430f9705a2d4b5f85f94441810031f3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008,301", "sxh": null, "ruleData": "customGet_zdrqts() === '' || customGet_zdrqts() === 'all'", "ruleCode": "customGet_zdrqts() === '' || customGet_zdrqts() === 'all'", "ruleMessage": "【customGet_zdrqts()】", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "重点群体校验1", "preTable": "ywbw.sbFjsf&&ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "92e7d063794e467fa27906f4da4710c3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) > round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse * 0.05) , 2 ) ) || ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) < round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse * 0.03) , 2 ) )", "ruleCode": "( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) > round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse * 0.05) , 2 ) ) || ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse) < round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse * 0.03) , 2 ) )", "ruleMessage": "第21栏“未达起征点免税额”需要您自行填写，填写数值范围在服务、不动产和无形资产列第11栏【“未达起征点销售额”*3%，“未达起征点销售额”*5%】之间。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_079", "preTable": "ywbw.sbZzsXgm", "passResult": false, "syqyList": null}, {"ruleXh": "ea4082e5eff74acc9d0a05fc2780a7ef", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz () === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse === getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse )", "ruleCode": "getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz () === 1, 001.yzzzsbhsxse[1] === 001.swjgdkdzzszyfpbhsxse[1] )", "ruleMessage": "您是属于【个体工商户和其他个人】，可享受的免税销售额，请将销售额调整到主表“本期数\\货物及劳务”的第11栏“未达起征点销售额”，如您开具或者代开了专用发票，请填写在第2栏，并且第1栏和第2栏需要相等。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_102", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "92d712ac7a244dcb9af61a72efd511ab", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "( ywbw.sbZzsXgmFbFlzl.bqkce > round ( ywbw.sbZzsXgmFbFlzl.qcye + ywbw.sbZzsXgmFbFlzl.bqfse , 2 ) ) || ( ywbw.sbZzsXgmFbFlzl.bqkce > ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr )", "ruleCode": "( 006.bqkce > round ( 006.qcye + 006.bqfse , 2 ) ) || ( 006.bqkce > 006.ysfwxsqbhssr )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第3栏“本期扣除数”应小于等于第1栏“期初余额”+第2栏“本期发生额”之和，且第3栏“本期扣除额”小于等于第5栏“全部含税收入（适用3%征收率）”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB01_JKGZ_003“应税行为（3%征收率）扣除额计算”的“本期扣除额”的校验", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": false, "syqyList": null}, {"ruleXh": "91e36bed30f0488a8f725956a8282ed4", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (customGet_Bbqzdbz() === 3,round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse)+abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse) , 2 ) === 0)", "ruleCode": "getIf (customGet_Bbqzdbz() === 3,round ( abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse)+abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse)+ abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse) , 2 ) === 0)", "ruleMessage": "本期销售额已达起征点，【主表】第10栏“其中：小微企业免税销售额”或第11栏“未达起征点销售额”不应有数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_004", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "91ea1ac72977424ea028f28f1cd6d6c0", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008,301", "sxh": null, "ruleData": "customGet_tysbtstx() === ''", "ruleCode": "customGet_tysbtstx() === ''", "ruleMessage": "您已经采集的《重点群体或自主就业退役士兵就业信息表》，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" target=\"_blank\">可点此对采集内容进行确认</a>", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "退役士兵校验2", "preTable": "ywbw.sbZzsFbZzsjmssbmxb&&ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "91f891728ffc4c07ac63d70a07726d51", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008,301", "sxh": null, "ruleData": "customGet_zdrqtstx() === ''", "ruleCode": "customGet_zdrqtstx() === ''", "ruleMessage": "您已经采集的《重点群体或自主就业退役士兵就业信息表》，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" target=\"_blank\">可点此对采集内容进行确认</a>", "messagePriority": 0, "validateType": "2", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "重点群体校验4", "preTable": "ywbw.sbZzsFbZzsjmssbmxb&&ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "1852521319574de689ac9c88e765077b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse),2) < 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 < 0, abs ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 ) <= round ( abs (Number(fzxx.tzxx.fzsbxx.bqysHwYjse)),2))", "ruleCode": "getIf (fzxx.csxx.zzsYjyhQyBz === 'Y' && round (Number(fzxx.tzxx.fzsbxx.bqysHwYjse),2) < 0 && 001.bqyjse1[1] < 0, abs ( 001.bqyjse1[1] ) <= round ( abs (Number(fzxx.tzxx.fzsbxx.bqysHwYjse)),2))", "ruleMessage": "货物及劳务：当期预缴余额小于0，申报表主表预缴栏次可以填报负数，但负数绝对值【abs(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1)】应小于等于当期预缴余额的绝对值【round(abs(Number(fzxx.tzxx.fzsbxx.bqysHwYjse)),2)】。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "001_23_1_校验2", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "05d0a8fd05a34d16b8e5cf14ad210cc3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getIf (fzxx.bqxx.gtgsh === 'Y',customGet_jmbfgthjmxm ( ) === 0)", "ruleCode": "getIf (fzxx.bqxx.gtgsh === 'Y',customGet_jmbfgthjmxm ( ) === 0)", "ruleMessage": "个体纳税人不允许在《增值税减免税申报明细表》减税项目中”减免性质代码及名称“填写“0001011707|SXA031901216|对住房租赁企业适用简易计税方法的，按照5%的征收率减按1.5%征收增值税|《财政部 税务总局 住房城乡建设部关于完善住房租赁有关税收政策的公告》财政部 税务总局 住房城乡建设部公告2021年第24号”", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_038", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "0591f6e47c064327be888eac37f1b531", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.yqwsqdjybnsrzgdqy ==='Y', getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse === 0)", "ruleCode": "getIf(fzxx.bqxx.yqwsqdjybnsrzgdqy ==='Y', getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse === 0)", "ruleMessage": "您是逾期未认定企业，【主表】“其中：小微企业免税额”、“未达起征点免税额”应税服务、不动产和无形资产必须都为0，请检查！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_013", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "5b7a4a0ebe4a449790f263cf05d56f58", "formXh": null, "fid": null, "regionCode": null, "relateTable": "", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"customGet_XejmjeCheck([*]) === 1\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"customGet_XejmjeCheck([*]) === 1\")", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】的增值税限额减免金额需等于当期《增值税减免税申报明细表》填报的增值税使用的限额减免金额【customGet_fjsXejmje ( )】", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "301_zzsxejmje_校验", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "9ea1c67ba61f40c48fa49dea2022622e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec (   0,   ywbw.sbFjsf.sbFjsfMx.length,   \"getIf(     ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= 0 && !!ywbw.sbFjsf.sbFjsfMx[*].jmzlxDm,     ywbw.sbFjsf.sbFjsfMx[*].jme > 0 && ywbw.sbFjsf.sbFjsfMx[*].jme <= ywbw.sbFjsf.sbFjsfMx[*].bqynsfe   )\" )", "ruleCode": "rangeExec (   0,   ywbw.sbFjsf.sbFjsfMx.length,   \"getIf(     ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= 0 && !!ywbw.sbFjsf.sbFjsfMx[*].jmzlxDm,     ywbw.sbFjsf.sbFjsfMx[*].jme > 0 && ywbw.sbFjsf.sbFjsfMx[*].jme <= ywbw.sbFjsf.sbFjsfMx[*].bqynsfe   )\" )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】“减免性质代码”不为空，“减免税额”必须大于零且小于等于“本期应纳税额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_062|SB_009_FB02_JKGZ_063|SB_009_FB02_JKGZ_064当本期应纳税额大于等于0 时， 不管选择的减免性质是免税还是不是免税，那么：“减免税额”必须大于零且小于等于“本期应纳税额。", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "6b0a8854c15d446583110ec3b195f5fe", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 === 0)", "ruleCode": "getIf(fzxx.bqxx.czchfbssqptfpdQy === 'N' && customGet_Bbqzdbz() === 1, getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 === 0)", "ruleMessage": "您应税服务属于未超过免税标准申报，【主表】应税服务、不动产和无形资产第6栏必须为0，请核实。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_100", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "f58543913b854ff6b8ff0cc69061dcfa", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf ( customGet_Bbqzdbz () === 2 && fzxx.bqxx.gtjy ==='N', round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse ,2) <= 0 && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 ,2 ) <= 0 || round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 ,2) === getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse ))", "ruleCode": "getIf ( customGet_Bbqzdbz () === 2 && fzxx.bqxx.gtjy ==='N', round ( 001.yzzzsbhsxse[1] + 001.yzzzsbhsxse[2] - 001.swjgdkdzzszyfpbhsxse[1] - 001.swjgdkdzzszyfpbhsxse[2] + 001.xssygdysgdzcbhsxse[1] ,2) <= 0 && ( round ( 001.xsczbdcbhsxse[2] - 001.swjgdkdzzszyfpbhsxse1[2] ,2 ) <= 0 || round ( 001.xsczbdcbhsxse[2] - 001.swjgdkdzzszyfpbhsxse1[2] ,2) === 001.bdcxse[2] ))", "ruleMessage": "剔除不动产销售额后，您本期销售额未超过免税标准，请将除不动产销售额之外的本期应征增值税销售额（不含开具及代开专用发票销售额）对应填写在【主表】第10栏“小微企业免税销售额”中；适用增值税差额征收政策的纳税人填写差额后的销售额，差额部分填写在附列资料对应栏次中。", "messagePriority": null, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_008", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "34ddb450f99e4201b572cc354272df8a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec(0,ywbw.sbFjsf.sbFjsfMx.length,\"getIf(ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= 0,ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= ywbw.sbFjsf.sbFjsfMx[*].bqyjse)\")", "ruleCode": "rangeExec(0,ywbw.sbFjsf.sbFjsfMx.length,\"getIf(ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= 0,ywbw.sbFjsf.sbFjsfMx[*].bqynsfe >= ywbw.sbFjsf.sbFjsfMx[*].bqyjse)\")", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】第9列“本期已缴税（费）额”必须小于等于第4列“本期应纳税（费）额”，请检查！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_046｜SB_009_FB02_JKGZ_047｜SB_009_FB02_JKGZ_048", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "3430d3c781ec48b2b3015ac623b10fc8", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse >= 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze > 0, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze <= max ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse))", "ruleCode": "getIf(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse >= 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze > 0, getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze <= max ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse , getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse))", "ruleMessage": "当“本期数\\货物及劳务”列第16栏“本期应纳税额”大于等于0时，主表“本期数\\货物及劳务”列第18栏“本期应纳税额减征额”应小于等于第16栏“本期应纳税额”和第17栏“核定应纳税额”中的较大者。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_JKGZ_062本栏次必须小于等于第16栏“本期应纳税额”和隐藏栏2“核定应纳税额”中的较大者。", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "32dcd9ecdebf43d0b44a3730afeff3dd", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "( ywbw.sbZzsXgmFbFlzl.bqkce5 > round ( ywbw.sbZzsXgmFbFlzl.qcye5 + ywbw.sbZzsXgmFbFlzl.bqfse5 , 2 ) ) || ( ywbw.sbZzsXgmFbFlzl.bqkce5 > ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr5 )", "ruleCode": "( 006.bqkce5 > round ( 006.qcye5 + 006.bqfse5 , 2 ) ) || ( 006.bqkce5 > 006.ysfwxsqbhssr5 )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第11栏“本期扣除额”应小于等于第9栏“期初余额”+第10栏“本期发生额”之和，且第11栏“本期扣除额”应小于等于第13栏“全部含税收入（适用5%征收率）”", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB01_JKGZ_011“应税行为（5%征收率）扣除额计算”的“本期扣除额”的校验", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": false, "syqyList": null}, {"ruleXh": "68aafcac47064095a2afccd3ad2f160c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length ,\"( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse > ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqydjse )\")", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" ( 008.bqsjdjse > 008.bqydjse ) \" )", "ruleMessage": "《增值税减免税申报明细表》减税性质代码及名称【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc】第4列减税项目“本期实际抵减税额”必须小于等于第3列“本期应抵减税额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB03_JKGZ_015｜减税第4列>=0&&4<=3", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": false, "syqyList": null}, {"ruleXh": "68179098cb2b4825b22483ef6601241e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008,301", "sxh": null, "ruleData": "customGet_zdrqts() === 'all'", "ruleCode": "customGet_zdrqts() === 'all'", "ruleMessage": "没有采集创业重点群体人员信息表的脱贫人口人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "重点群体校验3", "preTable": "ywbw.sbFjsf&&ywbw.sbZzsFbZzsjmssbmxb", "passResult": false, "syqyList": null}, {"ruleXh": "9908648c2b334a1b9b79e4f08ab2d992", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "getIf(!!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz, !!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz && checkTimeBchssq(ywbw.sbFjsf.sbFjsfQtxx.bchssqq, ywbw.sbFjsf.sbFjsfQtxx.bchssqz))", "ruleCode": "getIf(!!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz, !!ywbw.sbFjsf.sbFjsfQtxx && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqq && !!ywbw.sbFjsf.sbFjsfQtxx.bchssqz && checkTimeBchssq(ywbw.sbFjsf.sbFjsfQtxx.bchssqq, ywbw.sbFjsf.sbFjsfQtxx.bchssqz))", "ruleMessage": "所属期不合法，必须为整年、半年、季或月！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_FB02_JKGZ_057", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "f38e055d2e7f43b6ad8291ce71afe391", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getIf(fzxx.bqxx.yqwsqdjybnsrzgdqy ==='Y', getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse === 0)", "ruleCode": "getIf(fzxx.bqxx.yqwsqdjybnsrzgdqy ==='Y', getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse === 0 && getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse === 0)", "ruleMessage": "您是逾期未认定企业，【主表】第10、11栏应税货物及劳务必须都为0，请检查！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "SB_009_ZB_TSGZ_011", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "98a0b9be095d416388d999c1a57b0d51", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','3').mzzzsxmxse > getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','2').mzzzsxmxse", "ruleCode": "008.mzzzsxmxse[3] > 008.mzzzsxmxse[2]", "ruleMessage": "《增值税减免税申报明细表》“其中：跨境服务”必须小于等于“出口免税”免征增值税项目销售额。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "1", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 0, "ruleName": "免税8<=9", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": false, "syqyList": null}], "formulas": [{"ruleXh": "ce900267ff28470abb59d82c655f0313", "formXh": null, "fid": null, "regionCode": null, "relateTable": "", "sxh": null, "ruleData": "ywbw.xsbdcqkbSbbdxxVO.xsbdcqkb.xsbdcbhsxseceh === round ( ( ywbw.xsbdcqkbSbbdxxVO.xsbdcqkb.xsbdcbhsxseceq * 1.05 - ywbw.xsbdcqkbSbbdxxVO.xsbdcqkb.xsbdcbqkce ) / 1.05 , 2 )", "ruleCode": "500.xsbdcbhsxseceh === round ( ( 500.xsbdcbhsxseceq * 1.05 - 500.xsbdcbqkce ) / 1.05 , 2 )", "ruleMessage": "“销售不动产不含税销售额(差额后) ”的“销售额”必须符合公式：1c=(1a*1.05-1b)/1.05。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "“销售不动产不含税销售额(差额后) ”的“销售额”计算公式", "preTable": "ywbw.xsbdcqkbSbbdxxVO", "passResult": true, "syqyList": null}, {"ruleXh": "ff128073198647aebc3b46d44e95132e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').xsczbdcbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').xsczbdcbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').xsczbdcbhsxse , 2 )", "ruleCode": "001.xsczbdcbhsxse[4] === round ( 001.xsczbdcbhsxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').xsczbdcbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').xsczbdcbhsxse , 2 )", "ruleMessage": "“（二）应征增值税不含税销售额（5%征收率）”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_014“（二）应征增值税不含税销售额（5%征收率）”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "0d763ef7cfb74ab38aa6f7b328fbb9bf", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr5 === customGet_fb1_13_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr5 === customGet_fb1_13_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_13_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "4258f440347b492d8cc6800385cdec6a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').skqjkjdptfpxse1 === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1 + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').skqjkjdptfpxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').skqjkjdptfpxse1 , 2 )", "ruleCode": "001.skqjkjdptfpxse1[3] === round ( 001.skqjkjdptfpxse1[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').skqjkjdptfpxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').skqjkjdptfpxse1 , 2 )", "ruleMessage": "“其中：其他增值税发票不含税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_043“其中：其他增值税发票不含税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "75a2eb6647bf4d7fb0cba85d0fed0d97", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').hdynse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').hdynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdynse , 2 )", "ruleCode": "001.hdynse[3] === round ( 001.hdynse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').hdynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdynse , 2 )", "ruleMessage": "“核定应纳税额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_059“核定应纳税额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "74ebe285e7d944f6bbe4d280369d5f23", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').bqsjkcje === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'bqsjkcje',3 ) , 2 )", "ruleCode": "008.bqs<PERSON><PERSON><PERSON>[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'bq<PERSON><PERSON><PERSON><PERSON>',3 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第2列“免税项目--免税销售额扣除项目本期实际扣除金额--合计”应等于本表第2列“免税项目--免税销售额扣除项目本期实际扣除金额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_028｜免税2列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "739d04f65fc94547813b8c500f65330f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , ' ywbw.sbFjsf.sbFjsfMx[*].phjmse === getIf ( ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje >= 0 ) || customGet_yywxsphjmBz ( ywbw.sbFjsf.sbFjsfMx[*].zsxmDm ) === \"Y\" , round ( ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme ) * ywbw.sbFjsf.sbFjsfMx[*].phjzbl / 100 , 2 ) , 0 ) ' )", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , ' 301.phjmse === getIf ( ( 301.ybzzs + 301.zzsxejmje >= 0 ) || customGet_yywxsphjmBz ( ywbw.sbFjsf.sbFjsfMx[*].zsxmDm ) === \"Y\" , round ( ( 301.bqynsfe - 301.jme ) * 301.phjzbl / 100 , 2 ) , 0 ) ' )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB02_JKGZ_042｜SB_009_FB02_JKGZ_043｜SB_009_FB02_JKGZ_044｜附表二：8=（4-6）×7", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "731d5ad3657c4a32bd74e52bd599cabd", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.ysfwxshsxse === round ( ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr - ywbw.sbZzsXgmFbFlzl.bqkce , 2 )", "ruleCode": "006.ysfwxshsxse === round ( 006.ysfwxsqbhssr - 006.bqkce , 2 )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第7栏“含税销售额”应等于第5栏“全部含税收入（适用3%征收率）”-第6栏“本期扣除额“", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB01_JKGZ_007｜006_7_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "3f5cb781332d4161869e1cd7ba7391bc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').xwqymse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').xwqymse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').xwqymse , 2 )", "ruleCode": "001.xwqymse[3] === round ( 001.xwqymse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').xwqymse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').xwqymse , 2 )", "ruleMessage": "“其中：小微企业免税额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_074“其中：小微企业免税额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "729141f9ad8943fb81c4be5405c0ae8d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').bqybtsejyfj === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtsejyfj + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqybtsejyfj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqybtsejyfj , 2 )", "ruleCode": "001.bqybtsejyfj[3] === round ( 001.bqybtsejyfj[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqybtsejyfj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqybtsejyfj , 2 )", "ruleMessage": "“教育费附加本期应补（退）费额”的“本年累计”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_095｜“教育费附加本期应补（退）费额”的“本年累计”的计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "09bbba45316f43d2813d21f70281a91c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye ) \" )", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" 008.qcye === getIf ( ! 008.hmc , 0 , 008.qcye ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "减税名称置空，期初余额置0", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "08bb0ea8000e4723840784b52a6e6f43", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse === round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse === round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2)", "ruleMessage": "【主表】“增值税专用发票不含税销售额”的“本期数”的“货物及劳务”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_2_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "3e1875d48c174a018e4015c3bd7edaa6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').xwqymsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').xwqymsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').xwqymsxse , 2 )", "ruleCode": "001.xwqymsxse[4] === round ( 001.xwqymsxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').xwqymsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').xwqymsxse , 2 )", "ruleMessage": "“其中：小微企业免税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_030“其中：小微企业免税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "06ecb458b4bf4a2798d5bd059aee1b37", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse === round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse * 0.05, 2),2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse === round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse * 0.05, 2),2)", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第16栏“本期应纳税额”应等于等于第1栏“应征增值税不含税销售额”×征收率3%、第4栏“应征增值税不含税销售额（5%征收率）”×征收率5%之和。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_052|001_16_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "715ee65c574e4d46beaa2f06bf9f95f7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').bqmse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqmse , 2 )", "ruleCode": "001.bqmse[3] === round ( 001.bqmse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqmse , 2 )", "ruleMessage": "“本期免税额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_070“本期免税额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "1679c5ce7e8c4804b881bcf27a3adfa7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse === customGet_Wdqzdmse_col2Init ()", "ruleCode": "001.wdqzdmse[2] === customGet_Wdqzdmse_col2Init ()", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_21_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "4bb4ca656cee43329c4b3d4da3b2e571", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse === getIf(fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw1Wjhdxsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw3Wjhdxsehj,2),round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2))", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse === getIf(fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw1Wjhdxsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw3Wjhdxsehj,2),round(fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj,2))", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_1_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "a142a2d8ec8a437aa512f756c86a8da8", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').hdxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').hdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse , 2 )", "ruleCode": "001.hdxse[4] === round ( 001.hdxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').hdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse , 2 )", "ruleMessage": "“核定销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_048“核定销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d605857385214326ab89a1c5841d8f8d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].bqsjkcje === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].bqsjkcje ) \" )", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" 008.bqsjkcje === getIf ( ! 008.hmc , 0 , 008.bqsjkcje ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "《减免税申报明细表》免税销售额扣除项目本期实际扣除金额自动清空赋值", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "9fd6341c73a84f019d79de1214798c79", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1 === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round ( fzxx.tzxx.fzsbxx.qtfpHwjlwck0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlwck0xsehj ,2),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpxse1 === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round ( fzxx.tzxx.fzsbxx.qtfpHwjlwck0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlwck0xsehj ,2),0)", "ruleMessage": "第14行其中：其他增值税发票不含税销售额第1列预填", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_14_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d3a7aeb27a0f4a5eaeb9ba3ad7198726", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqmse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse * 0.03 , 2 )", "ruleCode": "001.bqmse[1] === round ( 001.msxse[1] * 0.03 , 2 )", "ruleMessage": "主表“本期数\\货物及劳务”列第19栏“本期免税额”应等于第9栏的“免税销售额”×征收率3%。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_068｜001_19_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d352941e66cf42ceac758e849c81399f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy === getIf(ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz !== '3','',ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy)", "ruleCode": "ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy === getIf(ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz !== '3','',ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy)", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "301_jsyjxgyy_公式", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "d38392c4f181483fb7ca3fe8e53af2c0", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj === getIf ( (customGet_Bbqzdbz() === 3) && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse , 2 ) < Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse) ) && ( Number(getObjFromList(ywbw.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdynse) > 0 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze , 2 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze , 2 ) )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj === getIf ( (customGet_Bbqzdbz() === 3) && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse , 2 ) < Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse) ) && ( Number(getObjFromList(ywbw.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdynse) > 0 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze , 2 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze , 2 ) )", "ruleMessage": "【主表】应纳税额合计的本期的服务、不动产和无形资产 的计算不正确。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_22_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "15073a1db44c4309a3cfff053d60aabc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse ) \" )", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "《减免税申报明细表》免征增值税项目 销售额自动清空赋值", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "14115b7dd158463cb173d589bc197161", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round ( fzxx.tzxx.fzsbxx.qtfpHwjlwck0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlwck0xsehj , 2 ),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round ( fzxx.tzxx.fzsbxx.qtfpHwjlwck0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlwck0xsehj , 2 ),0)", "ruleMessage": "第13行出口免税销售额第1列预填", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_13_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "d0192fc5df6e41e7816532b7acb4864c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').bqynsejze === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').bqynsejze - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').bqynsej<PERSON> , 2 )", "ruleCode": "001.bq<PERSON><PERSON><PERSON><PERSON>[4] === round ( 001.bq<PERSON><PERSON><PERSON><PERSON>[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').bqynsejze - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').bqyn<PERSON><PERSON><PERSON> , 2 )", "ruleMessage": "“本期应纳税额减征额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_065“本期应纳税额减征额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "11e12de77ac64799a48490a9aa04cb69", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').ckmsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').ckmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').ckmsxse , 2 )", "ruleCode": "001.ckmsxse[3] === round ( 001.ckmsxse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').ckmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').ckmsxse , 2 )", "ruleMessage": "“（五）出口免税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_041“（五）出口免税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "11b29863ec124743bd3f07e82c714a3d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze === customGet_Bqynsejze_col1Init() ", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze === customGet_Bqynsejze_col1Init() ", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_18_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "460553478613445e9668d633d3b21865", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqybtse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj - getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 , 2 )", "ruleCode": "001.bqybtse[2] === round ( 001.ynsehj[2] - 001.bqyjse1[2] , 2 )", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第24栏“本期应补（退）税额”应等于第22栏“应纳税额合计” - 第23栏“本期预缴税额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_091｜001_24_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "46d28f73e05c4c8a92c3e1c441852743", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse >= getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse1", "ruleCode": "001.xssygdysgdzcbhsxse[1] >= 001.skqjkjdptfpbhsxse1[1]", "ruleMessage": "主表“本期数\\货物及劳务”列第7栏“（三）销售使用过的固定资产不含税销售额”应大于等于第8栏“ 其中：其他增值税发票不含税销售额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_019", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "591ad000a8bb4ac4ba7dad40da4241ba", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\" ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje === getIf (fzxx.bqxx.dtcjsSlqy === 'Y' && ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === '10109',0, customGet_fjsXejmje ())\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\" 301.zzsxejmje === getIf (fzxx.bqxx.dtcjsSlqy === 'Y' && ywbw.sbFjsf.sbFjsfMx[*].zsxmDm === '10109',0, customGet_fjsXejmje ())\")", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】的增值税限额减免金额需等于当期《增值税减免税申报明细表》填报的增值税使用的限额减免金额【customGet_fjsXejmje ( )】", "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB02_JKGZ_007｜SB_009_FB02_JKGZ_008｜SB_009_FB02_JKGZ_009｜附表二2列计算", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "7898f42404584cb9b41852a54196eec1", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse === customGet_fb1_8_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse === customGet_fb1_8_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_8_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "de26d89e88804f8b8ab1c13b84816310", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "ywbw.sbZzsXgm.slxxForm.bsrlxdh === nsrxx.nsrxxKzVO.bsryddh", "ruleCode": "001.bsrlxdh === nsrxx.nsrxxKzVO.bsryddh", "ruleMessage": "办税人员联系电话自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "办税人员联系电话自动取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "dd6fb7ea93ad48789e9934cb7bb44445", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,053", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 === getIf ( ywbw.sbZzsXgmFbDlqyjxhxx.qylxbz === '2' , customGet_dlqyxxyj() , getIf ( ywbw.sbZzsXgmFbDlqyjxhxx.qylxbz === '1' , customGet_dlqyxxyj()+customGet_dlqyxxysjwfy() , ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 ) )", "ruleCode": "ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 === getIf ( ywbw.sbZzsXgmFbDlqyjxhxx.qylxbz === '2' , customGet_dlqyxxyj() , getIf ( ywbw.sbZzsXgmFbDlqyjxhxx.qylxbz === '1' , customGet_dlqyxxyj()+customGet_dlqyxxysjwfy() , ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 ) )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "本期应缴增值税预填规则", "preTable": "ywbw.sbZzsXgmFbDlqyjxhxx", "passResult": true, "syqyList": null}, {"ruleXh": "dc7407c0e09b40e8a1dd92437c861f98", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').msxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').msxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').msxse , 2 )", "ruleCode": "001.msxse[3] === round ( 001.msxse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').msxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').msxse , 2 )", "ruleMessage": "“（四）免税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_024“（四）免税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "dbf3c1b0c8a44876ac11731fd0f8866c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdxse === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse ) , 2 )", "ruleCode": "001.hdxse[2] === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse ) , 2 )", "ruleMessage": "【主表】“核定销售额”的“本月数”的“服务、不动产和无形资产”从核定取值不正确。", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_046【主表】核定销售额，服务、不动产和无形资产本月数从核定取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "1dd3e2eead3944c6aa4b2f3f3b8cb9c6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').skqjkjdptfpbhsxse1 === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse1 + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').skqjkjdptfpbhsxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').skqjkjdptfpbhsxse1 , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').skqjkjdptfpbhsxse1 === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse1 + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').skqjkjdptfpbhsxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').skqjkjdptfpbhsxse1 , 2 )", "ruleMessage": "“其中：其他增值税发票不含税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_021“其中：其他增值税发票不含税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "1cd43b26729748a0a433724c26748e36", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').mzzzsxmxse === round ( getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','2').mzzzsxmxse + getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','3').mzzzsxmxse + sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'mzzzsxmxse',3 ) , 2 )", "ruleCode": "008.mzzzsxmxse[1] === round ( 008.mzzzsxmxse[2] + 008.mzzzsxmxse[3] + sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'mzzzsxmxse',3 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第1列“免税项目--免征增值税项目销售额--合计”应等于本表第1列“免税项目--免征增值税项目销售额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_026｜免税1列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "1c76e4736dea460f9542f00395590243", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqfse === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'bqfse',1 ) , 2 )", "ruleCode": "008.bqfse[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'bqfse',1 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第2列“减税项目--本期发生额--合计”应等于本表第2列“减税项目--本期发生额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_012｜减税2列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "1cfe6c5cc39f4669bb15b891497b6068", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').swjgdkdzzszyfpbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').swjgdkdzzszyfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').swjgdkdzzszyfpbhsxse , 2 )", "ruleCode": "001.swjgdkdzzszyfpbhsxse[3] === round ( 001.swjgdkdzzszyfpbhsxse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').swjgdkdzzszyfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').swjgdkdzzszyfpbhsxse , 2 )", "ruleMessage": "主表第2栏“增值税专用发票不含税销售额”的“本年累计\\货物及劳务”列应等于“本月数\\货物及劳务”列和“本年累计\\货物及劳务”列的上期数之和", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_007“增值税专用发票不含税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "52aea59867a741efa3954bb7ac515d70", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').wdqzdmse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').wdqzdmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').wdqzdmse , 2 )", "ruleCode": "001.wdqzdmse[3] === round ( 001.wdqzdmse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').wdqzdmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').wdqzdmse , 2 )", "ruleMessage": "“ 未达起征点免税额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_080｜“ 未达起征点免税额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "522f767030614e1f82b9a03d4633e28c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse , 2 )", "ruleCode": "001.msxse[1] === round ( 001.xwqymsxse[1] + 001.wdqzdxse[1] + 001.qtmsxse[1] , 2 )", "ruleMessage": "“（四）免税销售额 ”的“本期数”的“货物及劳务”必须符合公式：9=10+11+12。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_022｜001_9_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "50e170f699b94032bed674f9e42a2bd6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse * 0.03 , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse * 0.03 , 2 )", "ruleMessage": "主表“本期数\\货物及劳务”列第20栏“其中：小微企业免税额”应等于第10栏的“其中：小微企业免税销售额”×征收率3%", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_072｜001_20_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "50e21ea8cff640ab8ef8c9375c4df812", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').wdqzdxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').wdqzdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').wdqzdxse , 2 )", "ruleCode": "001.wdqzdxse[3] === round ( 001.wdqzdxse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').wdqzdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').wdqzdxse , 2 )", "ruleMessage": "“未达起征点销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_034“未达起征点销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "1b28803631aa4ab2b6f786b30d394dfb", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === customGet_fjsJmxz ( [*] )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , \" ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === customGet_fjsJmxz ( [*] ) \" )", "ruleMessage": "当销售额≤起征点（按月10万，按季30万）且本行第4列＞0时，本栏自动带出减免性质，可修改", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "511406c2350c42fdbd93f3fe162fabb3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.qmye === round ( ywbw.sbZzsXgmFbFlzl.qcye + ywbw.sbZzsXgmFbFlzl.bqfse - ywbw.sbZzsXgmFbFlzl.bqkce , 2 )", "ruleCode": "006.qmye === round ( 006.qcye + 006.bqfse - 006.bqkce , 2 )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第4栏“期末余额”应等于第1栏“期初余额”+第2栏“本期发生额”-第3栏“本期扣除额”", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB01_JKGZ_004｜006_4_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "40361e5a88a547778b2aae988e5ac5ce", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse === getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj,2), getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.tzxx.fzsbxx.qzdType === 1, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2),0))", "ruleCode": "001.wdqzdxse[2] === getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj,2), getIf (fzxx.bqxx.gtjy === 'Y' && fzxx.tzxx.fzsbxx.qzdType === 1, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2),0))", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_11_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "1a1116caeb514a95bea26141e74dd998", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').bqmse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').bqmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').bqmse , 2 )", "ruleCode": "001.bqmse[4] === round ( 001.bqmse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').bqmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').bqmse , 2 )", "ruleMessage": "“本期免税额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_071“本期免税额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "18d2e02ae6a84047941cb7dd321fed79", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse ) \" )", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" 008.bqsjdjse === getIf ( ! 008.hmc , 0 , 008.bqsjdjse ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "《减免税申报明细表》本期实际抵减税额自动清空赋值", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "180f6dcf01254c51bd187687827a308a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').hdxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').hdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse , 2 )", "ruleCode": "001.hdxse[3] === round ( 001.hdxse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').hdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse , 2 )", "ruleMessage": "“核定销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_047“核定销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "170f66e143ff4ba29ccfaaf4863b259b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].swsxDm === ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === '0061042802' ? 'SXA031900783':ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === '0099042802'?'SXA031901054':ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm?(ywbw.sbFjsf.sbFjsfMx[*].swsxDm||''):''\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].swsxDm === ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === '0061042802' ? 'SXA031900783':ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm === '0099042802'?'SXA031901054':ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm?(ywbw.sbFjsf.sbFjsfMx[*].swsxDm||''):''\")", "ruleMessage": "默认税务事项代码", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "2845362d60c4421c92853ee9a8b8844a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').ynsehj === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ynsehj + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').ynsehj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').ynsehj , 2 )", "ruleCode": "001.ynsehj[4] === round ( 001.ynsehj[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').ynsehj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').ynsehj , 2 )", "ruleMessage": "“应纳税额合计”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_087｜“应纳税额合计”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "27fe477b85ca4deaa2ab1d3f8f1363d6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,053", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx.length ,\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx[*].bqsjdkjxse === round ( ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx[*].bqfsjx - ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx[*].mshwyjxse - ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx[*].fzcssjxse - ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx[*].zrjxse , 2 )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxJx.length , \" 053.bqsjdkjxse === round ( 053.bqfsjx - 053.mshwyjxse - 053.fzcssjxse - 053.zrjxse , 2 ) \" )", "ruleMessage": "《电力企业增值税销项税额和进项税额传递单》进项中第11栏“本期实际抵扣进项”应等于第7栏“本期发生进项”-第8栏“免税货物用”-第9栏“非正常损失”-第10栏“折让”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "传递单第11栏“本期实际抵扣进项”计算公式", "preTable": "ywbw.sbZzsXgmFbDlqyjxhxx", "passResult": true, "syqyList": null}, {"ruleXh": "812686760eb64c49abe14486159561f9", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').skqjkjdptfpbhsxse2 === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').skqjkjdptfpbhsxse2 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').skqjkjdptfpbhsxse2 , 2 )", "ruleCode": "001.skqjkjdptfpbhsxse2[4] === round ( 001.skqjkjdptfpbhsxse2[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').skqjkjdptfpbhsxse2 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').skqjkjdptfpbhsxse2 , 2 )", "ruleMessage": "“其他增值税发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_018“其他增值税发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e65f70c5e6b24757aebe4bae27d6eef0", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].ybzzs === customGet_fjsJsyj ( ywbw.sbFjsf.sbFjsfMx[*].zsxmDm )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , \" 301.ybzzs === customGet_fjsJsyj ( 301.zsxmDm ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "【附表二】“增值税税额计税依据”自动计算", "preTable": "ywbw.sbZzsXgm&&ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "254b271decfb4eb984b73635bd48d7cc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').hdynse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').hdynse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').hdynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdynse , 2 )", "ruleCode": "001.hdynse[4] === round ( 001.hdynse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').hdynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdynse , 2 )", "ruleMessage": "“核定应纳税额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_060“核定应纳税额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "246267eb7f314afd9a98a0073c487ff6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').qtmsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').qtmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').qtmsxse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').qtmsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').qtmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').qtmsxse , 2 )", "ruleMessage": "“其他免税销售额 ”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_037“其他免税销售额 ”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "23bb19a7e37c4a2d8a9d4a65129c69db", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "ywbw.sbZzsXgm.slxxForm.fddbrxm === nsrxx.fddbrxm", "ruleCode": "001.fddbrxm === nsrxx.fddbrxm", "ruleMessage": "法定代表人自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "法定代表人自动取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "7d1e506ae51045409b17e7fc3d7618b4", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').msxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').msxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').msxse , 2 )", "ruleCode": "001.msxse[4] === round ( 001.msxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').msxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').msxse , 2 )", "ruleMessage": "“（四）免税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_025“（四）免税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "afa195e5cfed46c4a07693ee0cd7de1d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').wdqzdmse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdmse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').wdqzdmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').wdqzdmse , 2 )", "ruleCode": "001.wdqzdmse[4] === round ( 001.wdqzdmse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').wdqzdmse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').wdqzdmse , 2 )", "ruleMessage": "“ 未达起征点免税额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_081｜“ 未达起征点免税额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e0d9b636b0b34383b72a26cf6acc39b9", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').bqynsejze === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqynsejze - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqynsejze , 2 )", "ruleCode": "001.bq<PERSON><PERSON><PERSON><PERSON>[3] === round ( 001.bqyn<PERSON><PERSON><PERSON>[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqynsejze - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqyn<PERSON><PERSON><PERSON> , 2 )", "ruleMessage": "“本期应纳税额减征额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_064“本期应纳税额减征额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "dfa2a10dce3448bfad5fd5450c21fc82", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec(0,ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].bqybtse === round ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme - ywbw.sbFjsf.sbFjsfMx[*].phjmse - ywbw.sbFjsf.sbFjsfMx[*].bqyjse , 2)\")", "ruleCode": "rangeExec(0,ywbw.sbFjsf.sbFjsfMx.length ,\" ywbw.sbFjsf.sbFjsfMx[*].bqybtse === round ( ywbw.sbFjsf.sbFjsfMx[*].bqynsfe - ywbw.sbFjsf.sbFjsfMx[*].jme - ywbw.sbFjsf.sbFjsfMx[*].phjmse - ywbw.sbFjsf.sbFjsfMx[*].bqyjse , 2)\")", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】“本期应补（退）税（费）额”计算有误", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB02_JKGZ_050｜SB_009_FB02_JKGZ_051｜SB_009_FB02_JKGZ_052｜附表2第10列计算公式", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "dfe02a494556474ab44a2c560ec5da11", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 === round (fzxx.tzxx.fzsbxx.bqysHwYjse, 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 === round (fzxx.tzxx.fzsbxx.bqysHwYjse, 2 )", "ruleMessage": "货劳本期预缴税额本期数应等于本期应税货物预缴", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_23_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "219f527a4e384f1f8752c2208065bada", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse === round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse * 0.03, 2),2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse === round(round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse * 0.03,2) + round(getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse * 0.03, 2),2)", "ruleMessage": "主表第16行第1列必须等于第1栏“应征增值税不含税销售额（3%征收率）”×征收率3%、第7栏“销售使用过的应税固定资产不含税销售额”×征收率3%之和", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_051｜001_16_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "21e03abaf9be4725908e7c9d63b285a6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').wdqzdxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').wdqzdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').wdqzdxse , 2 )", "ruleCode": "001.wdqzdxse[4] === round ( 001.wdqzdxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').wdqzdxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').wdqzdxse , 2 )", "ruleMessage": "“未达起征点销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_035“未达起征点销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "88d80ad39d744e39a13d68f69be0e52f", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').bqynse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').bqynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').bqynse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').bqynse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').bqynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').bqynse , 2 )", "ruleMessage": "“本期应纳税额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_056“本期应纳税额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "881143dc9f434cd085b3aa2fb9a912c0", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc === customGet_jsxmJmxz([*]) \" )", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc === customGet_jsxmJmxz([*]) \" )", "ruleMessage": "主表（三）销售使用过的固定资产不含税销售额-货物及劳务本期数不为0时，《减免税申报明细表》减税项目减免性质需要选择“0001129902|SXA031900512|销售旧货（不含二手车经验）、已使用固定资产减征增值税”", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_036（仅计算）", "preTable": "ywbw.sbZzsFbZzsjmssbmxb&&ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "ba9bab10a6ad47579677815a001bc92e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse === customGet_Wdqzdxse_col1Init()", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse === customGet_Wdqzdxse_col1Init()", "ruleMessage": "【主表】“未达起征点销售额”的“本期数”的“服务、不动产和无形资产”，自动取值”的“本期数”的“货物及劳务”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_11_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "555c656bfaae4eb5a8afa26e040b8fb1", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').ckmsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').ckmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').ckmsxse , 2 )", "ruleCode": "001.ckmsxse[4] === round ( 001.ckmsxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').ckmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').ckmsxse , 2 )", "ruleMessage": "“（五）出口免税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_042“（五）出口免税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "87d0a34ef7e54e2499d41a1dbc1a641c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtsejyfj === customGet_fjsYbtse (\"30203\")", "ruleCode": "001.bqybtsejyfj[1] === customGet_fjsYbtse ( \" 30203 \" )", "ruleMessage": "第26行“教育费附加本期应补（退）税额”本期数应等于《增值税及附加税费申报表》的“教育费附加”的本期应补（退）税（费）额！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_094｜第26行本期数计算公式", "preTable": "ywbw.sbZzsXgm&&ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "ba367520cc9f44a2ad29e6044f28c9d6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.qmye5 === round ( ywbw.sbZzsXgmFbFlzl.qcye5 + ywbw.sbZzsXgmFbFlzl.bqfse5 - ywbw.sbZzsXgmFbFlzl.bqkce5 , 2 )", "ruleCode": "006.qmye5 === round ( 006.qcye5 + 006.bqfse5 - 006.bqkce5 , 2 )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第12栏“期末余额”应等于第9栏“期初余额” +第10栏“本期发生额”-第11栏“本期扣除额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB01_JKGZ_012｜006_12_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "b96487d5d81a4b7fa3dea2e160aa2ac2", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "ywbw.sbZzsXgm.slxxForm.cwfzrxm === nsrxx.nsrxxKzVO.cwfzrxm", "ruleCode": "001.cwfzrxm === nsrxx.nsrxxKzVO.cwfzrxm", "ruleMessage": "财务负责人姓名自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "财务负责人姓名自动取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "b85dc216738b4274b55b53c9ca86f35a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').bqybtsedfjyfj === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtsedfjyfj + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqybtsedfjyfj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqybtsedfjyfj , 2 )", "ruleCode": "001.bqybtsedfjyfj[3] === round ( 001.bqybtsedfjyfj[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqybtsedfjyfj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqybtsedfjyfj , 2 )", "ruleMessage": "“地方教育附加本期应补（退）费额”的“本年累计”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_097｜“地方教育附加本期应补（退）费额”的“本年累计”的计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "3962ee2a31a14f76839d2fb9a73016e7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 === getIf (fzxx.tzxx.fzsbxx.qzdType === 3, round (fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse,2), getIf (fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse,2),0))", "ruleCode": "001.skqjkjdptfpbhsxse2[2] === getIf (fzxx.tzxx.fzsbxx.qzdType === 3, round (fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse,2), getIf (fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse,2),0))", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_6_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "b8c28621759e4bfab15a028524fa7649", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.bqkce5 === customGet_fb1_10_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.bqkce5 === customGet_fb1_10_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_11_公式|006_11=006_10", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "ec61b86723f24fdf9c7b8f9d84e4416b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse === round(fzxx.tzxx.fzsbxx.zyfpFw1xsehj + fzxx.tzxx.fzsbxx.zyfpFw3xsehj ,2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse === round(fzxx.tzxx.fzsbxx.zyfpFw1xsehj + fzxx.tzxx.fzsbxx.zyfpFw3xsehj ,2)", "ruleMessage": "【主表】“增值税专用发票不含税销售额”的“本期数”的“服务、不动产和无形资产”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_2_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "848b46bb9435446389b0dd616f80900e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.bqfse5 === customGet_fb1_10_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.bqfse5 === customGet_fb1_10_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_10_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "8349518d5834411b9ef4ccca628916bc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').qmye === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'qmye',1 ) , 2 )", "ruleCode": "008.qmye[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'qmye',1 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第5列“减税项目--期末余额--合计”应等于本表第5列“减税项目--期末余额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_018｜减税5列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "8356e50518e7420ba7f1d5a774a37283", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').ynsehj === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').ynsehj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').ynsehj , 2 )", "ruleCode": "001.ynsehj[3] === round ( 001.ynsehj[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').ynsehj - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').ynsehj , 2 )", "ruleMessage": "“应纳税额合计”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_086｜“应纳税额合计”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e926a617026a43a2a8ed7e372662cb6e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqsjdjse === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'bqsjdjse',1 ) , 2 )", "ruleCode": "008.bqsjdjse[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'bqsjdjse',1 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第4列“减税项目--本期实际抵减税额--合计”应等于本表第4列“减税项目--本期实际抵减税额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_016｜减税4列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "e7c23a8222e24cc899d07d97aab4d73a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj,2),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj,2),0)", "ruleMessage": "【主表】“其他增值税发票不含税销售额”的“本期数”的“货物及劳务”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_3_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "b39de292cda04903b8290398cfa3d74c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').xwqymse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').xwqymse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').xwqymse , 2 )", "ruleCode": "001.xwqymse[4] === round ( 001.xwqymse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').xwqymse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').xwqymse , 2 )", "ruleMessage": "“其中：小微企业免税额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_075“其中：小微企业免税额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "dfa30269564e4a79a944567e95bc5575", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 1, round (fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj + fzxx.tzxx.fzsbxx.zyfpBdcxse,2), getIf (fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.zyfpBdcxse + fzxx.tzxx.fzsbxx.wpfpBdcxse,2), getIf ( customGet_fb1_16_Init () === 0, round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse2 + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2), customGet_fb1_16_Init ())))", "ruleCode": "001.xsczbdcbhsxse[2] === getIf (fzxx.tzxx.fzsbxx.qzdType === 1, round (fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj + fzxx.tzxx.fzsbxx.zyfpBdcxse,2), getIf (fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.zyfpBdcxse + fzxx.tzxx.fzsbxx.wpfpBdcxse,2), getIf ( customGet_fb1_16_Init () === 0, round ( 001.swjgdkdzzszyfpbhsxse1[2] + 001.skqjkjdptfpbhsxse2[2] + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2), customGet_fb1_16_Init ())))", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_4_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e89bb8c5d4f64861b1b4079a7b9aa263", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpxse1 === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round(fzxx.tzxx.fzsbxx.qtfpFwck0xsehj + fzxx.tzxx.fzsbxx.wpfpFwck0xsehj,2),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpxse1 === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round(fzxx.tzxx.fzsbxx.qtfpFwck0xsehj + fzxx.tzxx.fzsbxx.wpfpFwck0xsehj,2),0)", "ruleMessage": "第14行其中：其他增值税发票不含税销售额额第2列预填", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_14_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "5e83c738e09f40c1ad2877ef1bc79a28", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].msxsedyjxse === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].msxsedyjxse ) \" )", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" 008.msxsedyjxse === getIf ( ! 008.hmc , 0 , 008.msxsedyjxse ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "《减免税申报明细表》免税销售额对应的进项税额自动清空赋值", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "fe7c080f743e46d09b7d9f4b37de38a1", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 === round (fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj + fzxx.tzxx.fzsbxx.zyfpBdcxse,2)", "ruleCode": "001.swjgdkdzzszyfpbhsxse1[2] === round (fzxx.tzxx.fzsbxx.zyfpFw5xsehj + fzxx.tzxx.fzsbxx.zyfpFw15xsehj + fzxx.tzxx.fzsbxx.zyfpBdcxse,2)", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_5_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "90b54a92fa704f8bba1d7f8f6be11e95", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,053", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbDlqyjxhxx.bnljjnzzs === ytxx.sbZzsXgmFbDlqyjxhxx.bnljjnzzs + ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 - ywbw.sbZzsXgmFbDlqyjxhxx.bqyjzzshcbsk + ywbw.sbZzsXgmFbDlqyjxhxx.bqqjzzs - ytxx.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 + ytxx.sbZzsXgmFbDlqyjxhxx.bqyjzzshcbsk - ytxx.sbZzsXgmFbDlqyjxhxx.bqqjzzs", "ruleCode": "053.bnljjnzzs === ytxx.sbZzsXgmFbDlqyjxhxx.bnljjnzzs + 053.bqyjzzs1 - 053.bqyjzzshcbsk + 053.bqqjzzs - ytxx.sbZzsXgmFbDlqyjxhxx.bqyjzzs1 + ytxx.sbZzsXgmFbDlqyjxhxx.bqyjzzshcbsk - ytxx.sbZzsXgmFbDlqyjxhxx.bqqjzzs", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "053_本年累计缴纳增值税 计算", "preTable": "ywbw.sbZzsXgmFbDlqyjxhxx", "passResult": true, "syqyList": null}, {"ruleXh": "5da20a6089b4417aaa07510511ba05aa", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').xwqymsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').xwqymsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').xwqymsxse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').xwqymsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').xwqymsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').xwqymsxse , 2 )", "ruleMessage": "“其中：小微企业免税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_029“其中：小微企业免税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "8fa9568a7197479c880f58b0f8e580c3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtsedfjyfj === customGet_fjsYbtse (\"30216\")", "ruleCode": "001.bqybtsedfjyfj[1] === customGet_fjsYbtse ( \" 30216 \" )", "ruleMessage": "第27行“地方教育附加本期应补（退）税额”本期数应等于《增值税及附加税费申报表》的“地方教育附加”的本期应补（退）税（费）额！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_096｜第27行本期数计算公式", "preTable": "ywbw.sbZzsXgm&&ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "c2b41276aa8f48049d3e66a4cab754d7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').swjgdkdzzszyfpbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').swjgdkdzzszyfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').swjgdkdzzszyfpbhsxse , 2 )", "ruleCode": "001.swjgdkdzzszyfpbhsxse[4] === round ( 001.swjgdkdzzszyfpbhsxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').swjgdkdzzszyfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').swjgdkdzzszyfpbhsxse , 2 )", "ruleMessage": "“增值税专用发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_008“增值税专用发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "8eb5e5a09e244f469de85e1bb1cae536", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').wdqzdxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse , 2 )", "ruleCode": "001.msxse[2] === round ( 001.xwqymsxse[2] + 001.wdqzdxse[2] + 001.qtmsxse[2] , 2 )", "ruleMessage": "主表“本期数\\服务、不动产和无形资产”列第9栏“（四）免税销售额”应等于第10栏“其中：小微企业免税销售额”、第11栏“未达起征点销售额”、第12栏“其他免税销售额”之和。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_023｜001_9_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "01bb8d1ca5a342198952e01e534c284b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').bqybtsecjs === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtsecjs + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqybtsecjs - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqybtsecjs , 2 )", "ruleCode": "001.bqybtsecjs[3] === round ( 001.bqybtsecjs[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqybtsecjs - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqybtsecjs , 2 )", "ruleMessage": "提示信息：“城市维护建设税本期应补（退）税额”的“本年累计”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_093｜“城市维护建设税本期应补（退）税额”的“本年累计”的计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "010630a82f8a4c32acf6c9d37659b34b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').xssygdysgdzcbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').xssygdysgdzcbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').xssygdysgdzcbhsxse , 2 )", "ruleCode": "001.xssygdysgdzcbhsxse[3] === round ( 001.xssygdysgdzcbhsxse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').xssygdysgdzcbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').xssygdysgdzcbhsxse , 2 )", "ruleMessage": "主表“（三）销售使用过的固定资产不含税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_020“（三）销售使用过的固定资产不含税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "01019118baf74f44b3613f80959cdcff", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,053", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx.length ,\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx[*].sdsr === round ( ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx[*].xsdl * ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx[*].dj2 , 2 )\")", "ruleCode": "rangeExec ( 0 , ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx.length , \" 053.sdsr === round ( 053.xsdl * 053.dj2 , 2 ) \" )", "ruleMessage": "《电力企业增值税销项税额和进项税额传递单》销项中第3栏“售电收入”应等于第1栏“销售电量上网电量” × 第2栏“电价”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "传递单第3栏“售电收入”计算公式", "preTable": "ywbw.sbZzsXgmFbDlqyjxhxx", "passResult": true, "syqyList": null}, {"ruleXh": "8b126a9f4a544d42b59fbb48f165491c", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 === round(ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.05,2)", "ruleCode": "ywbw.sbZzsXgmFbFlzl.ysfwxsbhsxse5 === round(ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.05,2)", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_16_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "24598a5d740c4099b1aa6874ac6728be", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymsxse === getIf (fzxx.bqxx.gtjy === 'N' && fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj,2), getIf (fzxx.bqxx.gtjy === 'N' && fzxx.tzxx.fzsbxx.qzdType === 1, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2),0))", "ruleCode": "001.xwqymsxse[2] === getIf (fzxx.bqxx.gtjy === 'N' && fzxx.tzxx.fzsbxx.qzdType === 2, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj,2), getIf (fzxx.bqxx.gtjy === 'N' && fzxx.tzxx.fzsbxx.qzdType === 1, round (fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2),0))", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_10_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "bd7b3d85a83147ca87bf9df1ee86bff7", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').skqjkjdptfpxse1 === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpxse1 + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').skqjkjdptfpxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').skqjkjdptfpxse1 , 2 )", "ruleCode": "001.skqjkjdptfpxse1[4] === round ( 001.skqjkjdptfpxse1[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').skqjkjdptfpxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').skqjkjdptfpxse1 , 2 )", "ruleMessage": "“其中：其他增值税发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_044“其中：其他增值税发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "bd38af34702c4b78a9fda8addc3401eb", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqyjse1 , 2 )", "ruleCode": "001.bqybtse[1] === round ( 001.ynsehj[1] - 001.bqyjse1[1] , 2 )", "ruleMessage": "主表“本期数\\货物及劳务”列第24栏“本期应补（退）税额”应等于第22栏“应纳税额合计” - 第23栏“本期预缴税额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_090｜001_24_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "f13f6abad4b84c33b1951fab42d0fd80", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse  === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj,2),getIf (fzxx.tzxx.fzsbxx.qzdType === 2,round(fzxx.tzxx.fzsbxx.qtfpBdc0xse,2),0))", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse  === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj,2),getIf (fzxx.tzxx.fzsbxx.qzdType === 2,round(fzxx.tzxx.fzsbxx.qtfpBdc0xse,2),0))", "ruleMessage": "【主表】“其他免税销售额”的“本期数”的“服务、不动产和无形资产”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_12_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "f0fed037172c484187740c968ceb23dc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.bqfse === customGet_fb1_2_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.bqfse === customGet_fb1_2_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_2_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "efd86cdc3dac4f67a3406b531a3087bf", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze === customGet_Bqynsejze_col2Init()", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqynsejze === customGet_Bqynsejze_col2Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_18_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "31872d0a66544962b752696e52e5a7f5", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').skqjkjdptfpbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').skqjkjdptfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').skqjkjdptfpbhsxse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').skqjkjdptfpbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').skqjkjdptfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').skqjkjdptfpbhsxse , 2 )", "ruleMessage": "“其他增值税发票不含税销售额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_011“其他增值税发票不含税销售额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "31cae717ced54b539a2ea0311ae36647", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length ,\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqydjse === round ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye + ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse , 2 )\")", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length ,\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqydjse === round ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye + ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse , 2 )\")", "ruleMessage": "《增值税减免税申报明细表》减免性质代码为【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc】的第3列“减税项目--本期应抵减税额”应等于减税项目第1列“期初余额”+第2列“本期发生额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_013", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "31ee810f269e4ab789c82974eb4ef19b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 === round (fzxx.tzxx.fzsbxx.bqysFwYjse, 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqyjse1 === round (fzxx.tzxx.fzsbxx.bqysFwYjse, 2 )", "ruleMessage": "服务、不动产和无形资产本期预缴税额本期数应等于本期应税货物预缴", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_23_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "30f9e88911b743dea35fb6d3e98cd502", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length ,\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].kchmsxse === round ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mzzzsxmxse - ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].bqsjkcje , 2 )\")", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" 008.kchmsxse === round ( 008.mzzzsxmxse - 008.b<PERSON><PERSON><PERSON><PERSON><PERSON> , 2 ) \" )", "ruleMessage": "《增值税减免税申报明细表》减免性质代码为【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc】的第3列“免税项目--扣除后免税销售额”应等于免税项目第1列“免征增值税项目销售额”-第2列“免税销售额扣除项目 本期实际扣除金额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_029", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "64dc6c7ffac7497baf31840adbb76135", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdmse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').wdqzdxse * 0.03 , 2 )", "ruleCode": "001.wdqzdmse[1] === round ( 001.wdqzdxse[1] * 0.03 , 2 )", "ruleMessage": "主表“本期数\\货物及劳务”列第21栏“未达起征点免税额”应等于第11栏“未达起征点销售额”×征收率3% ", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_078｜001_21_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "968a8e83b80d41899abf703ad4219df2", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse === customGet_Yzzzsbhsxse_col2Init()", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse === customGet_Yzzzsbhsxse_col2Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_1_2_公式", "preTable": "ywbw.sbZzsXgm&&ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "63c108b9cfd34a12b9806f7be6589784", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round(fzxx.tzxx.fzsbxx.qtfpFwck0xsehj + fzxx.tzxx.fzsbxx.wpfpFwck0xsehj,2),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse === getIf ( fzxx.bqxx.zzzssbtbqxmdndQy === 'Y',round(fzxx.tzxx.fzsbxx.qtfpFwck0xsehj + fzxx.tzxx.fzsbxx.wpfpFwck0xsehj,2),0)", "ruleMessage": "第13行出口免税销售额第2列预填", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_13_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "2e50c7a1a7a64eccab3485b009d23bd3", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse === customGet_Bqmse_col2Init()", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqmse === customGet_Bqmse_col2Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_19_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "969a87fd75b6472abf095ff7e03f86ad", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse === getIf ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc === '0001129902|SXA031900512' , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse * 0.01 , 2 ) , getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse ) ) \" )", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" 008.bqfse === getIf ( 008.hmc === '0001129902|SXA031900512' , round ( 001.xssygdysgdzcbhsxse[1] * 0.01 , 2 ) , getIf ( ! 008.hmc , 0 , 008.bqfse ) ) \" )", "ruleMessage": "减免性质“0001129902|SXA031900512|销售旧货（不含二手车经验）、已使用固定资产减征增值税”的本期发生额应等于货物及劳务销售使用过的固定资产不含税销售额本期数×1%", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "《减免税申报明细表》减税项目本期发生额自动赋值", "preTable": "ywbw.sbZzsFbZzsjmssbmxb&&ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "629fad0653264225a4e3cab2e03b4b95", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').skqjkjdptfpbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').skqjkjdptfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').skqjkjdptfpbhsxse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').skqjkjdptfpbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').skqjkjdptfpbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').skqjkjdptfpbhsxse , 2 )", "ruleMessage": "“其他增值税发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_012“其他增值税发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "953f87c5a6874938a32ed196654d09d2", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').bqynse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqynse , 2 )", "ruleCode": "001.bqynse[3] === round ( 001.bqynse[1] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').bqynse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').bqynse , 2 )", "ruleMessage": "“本期应纳税额”的“本年累计”的“货物及劳务”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_055“本期应纳税额”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "2d6f783b950e49c89086afd054b0e0ef", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').qcye === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'qcye',1 ) , 2 )", "ruleCode": "008.qcye[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'qcye',1 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第1列“减税项目--期初余额--合计”应等于本表第1列“减税项目--期初余额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_011｜减税1列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "2df59f060f564fa0918cb815e905d698", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 === round ( ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr5 - ywbw.sbZzsXgmFbFlzl.bqkce5 , 2 )", "ruleCode": "006.ysfwxshsxse5 === round ( 006.ysfwxsqbhssr5 - 006.bqkce5 , 2 )", "ruleMessage": "《增值税纳税申报表（适用于增值税小规模纳税人）附列资料》第15栏“含税销售额”应等于第13栏“全部含税收入”-第14栏“本期扣除额”", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB01_JKGZ_015｜006_15_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "2c1de3d44e674f8799d8cb273678d121", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').kchmsxse === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'kchmsxse',3 ) , 2 )", "ruleCode": "008.kchmsxse[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'kchmsxse',3 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第3列“免税项目--扣除后免税销售额--合计”应等于本表第3列“免税项目--扣除后免税销售额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "免税3列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "2c193f00cd02463cb0eb396f8ec0ce2a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdxse === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse ) , 2 )", "ruleCode": "001.hdxse[1] === round ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse ) , 2 )", "ruleMessage": "【主表】“核定销售额”的“本月数”的“货物及劳务”不正确，应该等于核定节点YSHWHDXSE的值。", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_045核定销售额取值公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "2b254db4da9c4033a62992a9da811dc5", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj === getIf ( (customGet_Bbqzdbz() === 3) && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse , 2 ) < Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse) ) && ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdynse) > 0 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze , 2 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze , 2 ) )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ynsehj === getIf ( (customGet_Bbqzdbz() === 3) && ( round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse + getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse , 2 ) < Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse) ) && ( Number(getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdynse) > 0 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze , 2 ) , round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse - getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynsejze , 2 ) )", "ruleMessage": "【主表】应纳税额合计的本月数的货物及劳务的计算不正确。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_22_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "945c6a3abdd74ad1a2af0045f8e9ecc1", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqydjse === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'bqydjse',1 ) , 2 )", "ruleCode": "008.bqydjse[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'bqydjse',1 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第3列“减税项目--本期应抵减税额--合计”应等于本表第3列“减税项目--本期应抵减税额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_014", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "93a07ff03b834722968c863e03d32f9d", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].jme === customGet_fjsJmse([*])\")", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , \" ywbw.sbFjsf.sbFjsfMx[*].jme === customGet_fjsJmse([*]) \")", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "附表2-6列", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "c543a29b6110407b927df66bf9e33bc2", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,301", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtsecjs === customGet_fjsYbtse (\"10109\")", "ruleCode": "001.bqybtsecjs[1] === customGet_fjsYbtse ( \" 10109 \" )", "ruleMessage": "第25行“城市维护建设税本期应补（退）税额”本期数应等于《增值税及附加税费申报表》的“城市维护建设税”的本期应补（退）税（费）额！", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_092｜第25行本期数计算公式", "preTable": "ywbw.sbZzsXgm&&ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "c41e7928de764ebca3594324fa83b8cc", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length ,\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qmye === round ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqydjse - ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse , 2 )\")", "ruleCode": "rangeExec ( 1 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length , \" 008.qmye === round ( 008.bqydjse - 008.bqsjdjse , 2 ) \" )", "ruleMessage": "《增值税减免税申报明细表》减免性质代码为【ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc】第5列“减税项目--期未余额”应等于减税项目第3列“本期应抵减税额”-第4列“本期实际抵减税额”。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_017｜减税5=3-4", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "90e9001bc9f24abc8c6b3f082c9d5141", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse === round(fzxx.tzxx.fzsbxx.zyfpBdcxse + fzxx.tzxx.fzsbxx.qtfpBdcxse + fzxx.tzxx.fzsbxx.wpfpBdcxse,2)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bdcxse === round(fzxx.tzxx.fzsbxx.zyfpBdcxse + fzxx.tzxx.fzsbxx.qtfpBdcxse + fzxx.tzxx.fzsbxx.wpfpBdcxse,2)", "ruleMessage": "【主表】“本期销售不动产销售额”自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "【主表】“本期销售不动产销售额”自动取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "c39f49c41133444caa3301da76165415", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').msxsedyjxse === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'msxsedyjxse',3 ) , 2 )", "ruleCode": "008.msxsedyjxse[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'msxsedyjxse',3 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第4列“免税项目--免税销售额对应的进项税额--合计”应等于本表第4列“免税项目--免税销售额对应的进项税额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "免税4列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "f96b34c023314c2f8a72786b277f08bd", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'ewbhxh','1').mse === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'mse',3 ) , 2 )", "ruleCode": "008.mse[1] === round ( sumList ( ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm,'mse',3 ) , 2 )", "ruleMessage": "《增值税减免税申报明细表》合计行第5列“免税项目--免税额--合计”应等于本表第5列“免税项目--免税额”明细合计。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB03_JKGZ_034｜免税5列求和公式", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "f87f1f1c929146be864aa34d0658b7a2", "formXh": null, "fid": null, "regionCode": null, "relateTable": "008", "sxh": null, "ruleData": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse === getIf ( ! ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].hmc , 0 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse ) \" )", "ruleCode": "rangeExec ( 3 , ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length , \" 008.mse === getIf ( ! 008.hmc , 0 , 008.mse ) \" )", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "《减免税申报明细表》免税额自动清空赋值", "preTable": "ywbw.sbZzsFbZzsjmssbmxb", "passResult": true, "syqyList": null}, {"ruleXh": "05e91a6baf734a5c8b5585eb480ec13a", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').qtmsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').qtmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').qtmsxse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').qtmsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').qtmsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').qtmsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').qtmsxse , 2 )", "ruleMessage": "“其他免税销售额 ”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_038“其他免税销售额 ”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "3aecceba51f44ad4873307b84a6e25b4", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse === customGet_Xwqymsxse_col1Init()", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xwqymsxse === customGet_Xwqymsxse_col1Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_10_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "37e5f9578bb74500afe2c260b3b03e7e", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr === customGet_fb1_5_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr === customGet_fb1_5_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_5_公式", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "03993fa7f1f14e4482517a53a48649a6", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "ywbw.sbZzsXgm.slxxForm.bsrxm === nsrxx.nsrxxKzVO.bsrxm", "ruleCode": "001.bsrxm === nsrxx.nsrxxKzVO.bsrxm", "ruleMessage": "办税人姓名自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "办税人姓名自动取值", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "e04a329c1ce1467ca7659ffd69a52b58", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xwqymse === customGet_Xwqymse_col2Init ()", "ruleCode": "001.xwqymse[2] === customGet_Xwqymse_col2Init ()", "ruleMessage": null, "messagePriority": null, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_20_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "37718636d9c6459f9162ab59a99a2e91", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').yzzzsbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').yzzzsbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').yzzzsbhsxse , 2 )", "ruleCode": "001.yzzzsbhsxse[4] === round ( 001.yzzzsbhsxse[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').yzzzsbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').yzzzsbhsxse , 2 )", "ruleMessage": "“（一）应征增值税不含税销售额（3%征收率）”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_004“（一）应征增值税不含税销售额（3%征收率）”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "37a3338fbfd24ecb912e3fde2c7b2659", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj,2),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').qtmsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj,2),0)", "ruleMessage": "【主表】“其他免税销售额”的“本期数”的“货物及劳务”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_12_1_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "9e40c8b9ac1746c0848913984f62549b", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj,2),0)", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').skqjkjdptfpbhsxse === getIf (fzxx.tzxx.fzsbxx.qzdType === 3,round(fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj,2),0)", "ruleMessage": "【主表】“其他增值税发票不含税销售额”的“本期数”的“服务、不动产和无形资产”，自动取值", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "001_3_2_公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "9e1d0a833c8b48148328e14444b6b871", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').yzzzsbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').yzzzsbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').yzzzsbhsxse , 2 )", "ruleCode": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','3').yzzzsbhsxse === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BNLJ').yzzzsbhsxse - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').yzzzsbhsxse , 2 )", "ruleMessage": "主表第1栏“（一）应征增值税不含税销售额（3%征收率）”的“本年累计\\货物及劳务”列应等于“本月数\\货物及劳务”列和“本年累计\\货物及劳务”列的上期数之和", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_003“（一）应征增值税不含税销售额（3%征收率）”的“本年累计”的“货物及劳务”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}, {"ruleXh": "f6a75b5bfba94e7f982b0424f21ff713", "formXh": null, "fid": null, "regionCode": null, "relateTable": "301", "sxh": null, "ruleData": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , 'ywbw.sbFjsf.sbFjsfMx[*].bqynsfe === round ( ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje ) * ywbw.sbFjsf.sbFjsfMx[*].sl1 , 2 ) ' )", "ruleCode": "rangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length , 'ywbw.sbFjsf.sbFjsfMx[*].bqynsfe === round ( ( ywbw.sbFjsf.sbFjsfMx[*].ybzzs + ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje ) * ywbw.sbFjsf.sbFjsfMx[*].sl1 , 2 ) ' )", "ruleMessage": "《附表二 附加税费情况表》【ywbw.sbFjsf.sbFjsfMx[*].zsxmMc】本期应纳税（费）额需等于（增值税税额+增值税限额减免金额）*税（费）率（征收率）", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_FB02_JKGZ_023｜SB_009_FB02_JKGZ_024｜SB_009_FB02_JKGZ_025｜附表2-4列公式", "preTable": "ywbw.sbFjsf", "passResult": true, "syqyList": null}, {"ruleXh": "33d255cc11d445e1806e8c804e000a12", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001,006", "sxh": null, "ruleData": "ywbw.sbZzsXgmFbFlzl.bqkce === customGet_fb1_2_Init()", "ruleCode": "ywbw.sbZzsXgmFbFlzl.bqkce === customGet_fb1_2_Init()", "ruleMessage": "", "messagePriority": 0, "validateType": "3", "qd": null, "ruleType": "0", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "006_3_公式|006_3=006_2", "preTable": "ywbw.sbZzsXgmFbFlzl", "passResult": true, "syqyList": null}, {"ruleXh": "99792c15f56c4841add7abc42c6d0daf", "formXh": null, "fid": null, "regionCode": null, "relateTable": "001", "sxh": null, "ruleData": "getObjFromList(ywbw.sbZzsXgm,'ewblxh','4').swjgdkdzzszyfpbhsxse1 === round ( getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').swjgdkdzzszyfpbhsxse1 + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').swjgdkdzzszyfpbhsxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').swjgdkdzzszyfpbhsxse1 , 2 )", "ruleCode": "001.swjgdkdzzszyfpbhsxse1[4] === round ( 001.swjgdkdzzszyfpbhsxse1[2] + getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BNLJ').swjgdkdzzszyfpbhsxse1 - getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').swjgdkdzzszyfpbhsxse1 , 2 )", "ruleMessage": "“增值税专用发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”必须等于本月数+本年度历史累计数。", "messagePriority": 0, "validateType": "1", "qd": null, "ruleType": "2", "ruleParam": null, "isEnable": "1", "precisionDigit": null, "isDefault": null, "tag": null, "ruleCalc": 1, "ruleName": "SB_009_ZB_JKGZ_016“增值税专用发票不含税销售额”的“本年累计”的“服务、不动产和无形资产”计算公式", "preTable": "ywbw.sbZzsXgm", "passResult": true, "syqyList": null}]}