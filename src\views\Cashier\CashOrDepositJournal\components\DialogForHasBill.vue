<template>
    <DialogTip 
        ref="DialogTipRef"
        @closed="handleClosed"
        @confirm="handleConfirm"
        tip-title="<span style='font-weight:600'>该日记账已与单据核销，是否仍需修改？</span>"
        :is-large-tip-title="true"
        :tip-info-left="true"
        confirmText="去修改"
    >
        <template #tipSlot>
            <div style="text-align: left;">
                <div>1.日记账核销后仅支持修改项目、部门、往来单位账号、往来单位开户行、结算方式、票据号和备注7个字段，如需修改其他字段需要先取消与单据的核销</div>
                <div class="mt-5">2.如果点击【去修改】后发现没有找到上面7个字段，您可以勾选显示全部重新点击进行修改</div>
            </div>
        </template>
        <div class="mt-20" style="text-align: left;">
            <el-checkbox v-model="notTip" @change="handleNoTipsChange" label="不再提示" />
        </div>
    </DialogTip>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import DialogTip from "@/components/Dialog/DialogTip/index.vue";

const DialogTipRef = ref();
const notTip = ref(false);
let editRow: null | (() => void) = null;

function handleNoTipsChange(val: any) {
    const check = !!val;
    const key = "hiddenBillDialogJournal-" + getGlobalToken();
    localStorage.setItem(key, String(check));
}
function handleClosed() {
    notTip.value = false;
    editRow = null;
}
function handleConfirm() {
    const notTip = localStorage.getItem("hiddenBillDialogJournal-" + getGlobalToken()) === "true";
    const key = "hiddenBillDialogJournal-" + getGlobalToken();
    localStorage.setItem(key, String(notTip));
    editRow && editRow();
}
function showDialog(callBack: () => void) {
    editRow = callBack;
    DialogTipRef.value?.showDialog();
}
defineExpose({ showDialog });
</script>
