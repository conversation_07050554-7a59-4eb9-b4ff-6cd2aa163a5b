<template>
    <el-dialog
        class="no-dialog-header"
        title=""
        v-model="loadingVisible"
        width="298px"
        height="auto"
        :modal="false"
        :align-center="true"
        :show-close="false"
        :z-index="9999"
        destroy-on-close
        :append-to-body="!isErp"
    >
        <div class="custom-loading" :style="{padding:isErp?'0':'20px'}">
            <template v-if="isErp">
                <div
                    class="percent-loading"
                    element-loading-custom-class="erp-loading"
                    :element-loading-text="loadingTitle"
                    v-loading="true"
                    :fullscreen="false"
                >
                </div>
            </template>
            <template v-else>
                <div style="font-size: 14px; font-weight: normal; line-height: 30px">{{ loadingTitle }}</div>
                <el-progress :percentage="percentage" :stroke-width="16" style="height: 20px; margin-top: 10px" />
            </template>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useLoadingStoreHook } from "@/store/modules/loading";
import { watch } from "vue";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { toRef, computed } from "vue";

const isErp = ref(window.isErp);
const props = defineProps<{
    close: Function;
    confirm: Function;
    cancel: Function;
    visible?: boolean;
}>();
// const loadingVisible = ref(useLoadingStoreHook().loading > 0);
const loadingVisible = computed(() => useLoadingStoreHook().loading > 0);

const loadingTitle = toRef(useLoadingStoreHook(), "title");
const percentage = ref(10);
setInterval(() => {
    if (percentage.value < 100) {
        percentage.value += 10;
    } else {
        percentage.value = 0;
    }
}, 800);
watch(
    () => useLoadingStoreHook().loading,
    (val) => {
        if (loadingVisible.value === false && val > 0) {
            // 从不显示变为显示
            percentage.value = 10;
            if (isLemonClient()) {
                getLemonClient().showMenuMask("show");
            }
        }
        // loadingVisible.value = val > 0;
        if (loadingVisible.value === false) {
            if (isLemonClient()) {
                getLemonClient().showMenuMask("close");
            }
        }
    },
    { immediate: true }
);
watch(loadingVisible, (val) => {
    if (!val) {
        props.cancel();
    }
});
// watch(
//     () => useLoadingStoreHook().title,
//     (val) => {
//         loadingTitle.value = val;
//     },{immediate:true}
// );
</script>

<style lang="less" scoped>
.custom-loading {
    padding: 20px;
    transform: translateY(-4px);
    border-radius: 4px;
    background-color: var(--white);
}

</style>
