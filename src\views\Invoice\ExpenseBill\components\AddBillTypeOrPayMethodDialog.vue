<template>
    <el-dialog
        v-model="editDialogVisible"
        :title="editDialogType === '删除' ? '提示' : editDialogType + (category === 'billTypes' ? '费用类型' : '结算方式')"
        center
        width="440"
        :destroy-on-close="true"
        class="custom-confirm expense-type-edit-dialog dialogDrag"
        modal-class="modal-class"
    >
        <div class="expense-type-edit-content" v-dialogDrag>
            <div class="expense-type-edit-main">
                <span v-if="editDialogType === '删除'" class="delete-tip"> 亲，确认要删除吗？ </span>
                <div v-else>
                    <div class="line-item input">
                        <div class="line-item-title">编码：</div>
                        <div class="line-item-field">
                            <el-input
                                v-model="expForm.no"
                                @input="handleLengthLimit($event, 3, '编码', 'no')"
                                :formatter="(val:string)=>val.replace(/[^a-zA-Z0-9]/g, '')"
                            ></el-input>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">名称：</div>
                        <div class="line-item-field">
                            <el-input v-model="expForm.name" @input="handleLengthLimit($event, 20, '名称', 'name')"></el-input>
                        </div>
                    </div>

                    <div class="line-item input">
                        <div class="line-item-title">{{ category === "billTypes" ? " 费用" : "对应" }}科目：</div>
                        <div class="line-item-field">
                            <SubjectPicker
                                v-if="category === 'billTypes'"
                                ref="startAsubRef"
                                :isById="true"
                                v-model="expForm.asubId"
                                asubImgRight="4px"
                                :showDisabled="false"
                            ></SubjectPicker>
                            <SubjectPicker
                                v-else
                                ref="startAsubRef"
                                :isById="true"
                                v-model="expForm.asubId"
                                asubImgRight="4px"
                                :showDisabled="false"
                            ></SubjectPicker>
                        </div>
                    </div>
                    <div class="line-item input" v-if="category === 'payMethods'">
                        <div class="line-item-title">抵扣税额科目：</div>
                        <div class="line-item-field">
                            <SubjectPicker
                                ref="startAsubRef"
                                :isById="true"
                                v-model="expForm.taxAsubId"
                                asubImgRight="4px"
                                :showDisabled="false"
                            ></SubjectPicker>
                        </div>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button ml-10" @click="certificationSure">确定</a>
                <a class="button ml-10" @click="cancelEdit">取消</a>
            </div>
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { computed, ref } from "vue";
import { expenseTypeForm } from "../types";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import type { BasicTypeList, IBillTypeItem, IPayMethodItem } from "../types";
import { incrementString } from "../utils";
const props = defineProps<{
    editDialogType: string;
    billData: BasicTypeList;
}>();
const emits = defineEmits(["update:modelValue", "reloadBillTypeList", "close", "reloadTableData"]);
// 费用表单
let expForm = ref<expenseTypeForm>({
    id: 0,
    no: "",
    name: "",
    taxAsubId: "",
    asubId: "",
});

const editDialogVisible = ref(false);
const category = ref();
const initForm = (editRowData: any, cate: "billTypes" | "payMethods", autoAddName:string = "") => {
    editDialogVisible.value = true;
    category.value = cate;
    if (cate === "billTypes") {
        let lastData = props.billData[cate][props.billData[cate].length - 1] as IBillTypeItem;
        expForm.value = new expenseTypeForm(
            (editRowData as IBillTypeItem)
                ? {
                      id: editRowData.billTypeId,
                      no: editRowData.billTypeNo,
                      name: editRowData.billTypeName,
                      asubId: String(editRowData.asubId),
                  }
                : {
                      id: 0,
                      no: incrementString(lastData.billTypeNo),
                      name: autoAddName,
                      asubId: "",
                  }
        );
    } else {
        let lastData = props.billData[cate][props.billData[cate].length - 1] as IPayMethodItem;
        expForm.value = new expenseTypeForm(
            (editRowData as IPayMethodItem)
                ? {
                      id: editRowData.pmId,
                      no: editRowData.pmNo,
                      name: editRowData.pmName,
                      asubId: String(editRowData.asubId),
                      taxAsubId: String(editRowData.taxAsubId),
                  }
                : {
                      id: 0,
                      no: incrementString(lastData.pmNo),
                      name: autoAddName,
                      asubId: "",
                      taxAsubId: "",
                  }
        );
    }
    !editRowData && handleLengthLimit(autoAddName, 20, '名称', 'name');
};

const handleLengthLimit = (val: string, length: number, label: string, key: string) => {
    if (val.length > length) {
        ElNotify({
            type: "warning",
            message: label === "编码" ? `亲，编码只能输入三位及以下的数字字母组合` : "亲，名称不能超过20个字符！",
        });
        (expForm.value as any)[key] = val.slice(0, length);
    }
};
const certificationSure = () => {
    if (props.editDialogType !== "删除") {
        if (!expForm.value.no) {
            ElNotify({
                type: "warning",
                message: "编码不能为空",
            });
            return;
        } else if (expForm.value.no.length > 3) {
            ElNotify({
                type: "warning",
                message: "亲，编码只能输入三位及以下的数字字母组合",
            });
            return;
        }
        if (!expForm.value.name) {
            ElNotify({
                type: "warning",
                message: "名称不能为空",
            });
            return;
        } else if (expForm.value.name.length > 20) {
            ElNotify({
                type: "warning",
                message: "名称不能超过20个字符，请修改后重试哦~",
            });
            return;
        }
        if (!expForm.value.asubId) {
            ElNotify({
                type: "warning",
                message: `请选择${category.value === "billTypes" ? "费用" : ""}科目`,
            });
            return;
        }
        if (category.value === "payMethods" && !expForm.value.taxAsubId) {
            ElNotify({
                type: "warning",
                message: "请选择抵扣税额科目",
            });
            return;
        }
    } else {
        if (props.billData[category.value as "billTypes" | "payMethods"].length === 1) {
            editDialogVisible.value = false;
            ElNotify({
                type: "warning",
                message: "删除失败，至少保留一个" + (category.value === "billTypes" ? "费用类型" : "结算方式") + "！",
            });
            return;
        }
    }
    request({
        url: `/api/ExpenseBill/${category.value === "billTypes" ? "BillType" : "PayMethod"}`,
        method: props.editDialogType === "删除" ? "delete" : props.editDialogType === "添加" ? "post" : "put",
        data:
            category.value === "billTypes"
                ? {
                      billtypeid: expForm.value.id,
                      billtypeno: expForm.value.no,
                      billtypename: expForm.value.name,
                      asubid: Number(expForm.value.asubId),
                  }
                : {
                      pmid: expForm.value.id,
                      pmno: expForm.value.no,
                      pmname: expForm.value.name,
                      asubid: Number(expForm.value.asubId),
                      taxAsubId: Number(expForm.value.taxAsubId),
                  },
    }).then((res) => {
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: props.editDialogType + "成功！",
            });
            if (props.editDialogType === "修改") {
                emits("reloadBillTypeList");
                emits("reloadTableData");
            } else {
                emits("reloadBillTypeList", category.value, expForm.value.no, expForm.value.name);
            }
            editDialogVisible.value = false;
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "操作失败",
            });
        }
    });
};
const cancelEdit = () => {
    editDialogVisible.value = false;
    emits("close");
};
defineExpose({
    initForm,
});
</script>
<style scoped lang="less">
.expense-type-edit-dialog {
    .expense-type-edit-content {
        .expense-type-edit-main {
            .delete-tip {
                padding: 40px 70px;
                display: block;
                text-align: center;
            }
            padding: 20px 30px 0 20px;

            .line-item {
                display: flex;
                margin-bottom: 20px;
                .line-item-title {
                    width: 110px;
                    line-height: 32px;
                    text-align: right;
                    margin-right: 10px;
                    &:before {
                        content: "*";
                        color: var(--el-color-danger);
                        margin-right: 4px;
                    }
                }
                .line-item-field {
                    flex: 1;
                    position: relative;
                    :deep(.el-select-v2) {
                        width: 100% !important;
                    }
                }
            }
            .delete-tip {
                text-align: center;
                font-size: 16px;
                color: #333;
                margin-bottom: 20px;
            }
        }
        .buttons {
            text-align: center;
            margin-top: 20px;
        }
    }
}
</style>
