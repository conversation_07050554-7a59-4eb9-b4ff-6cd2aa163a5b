:deep(.cell) {
    padding: 2px 4px !important;
    & input[type="text"] {
        border: 0px !important;
        text-align: right !important;
    }
}
:deep(.el-input__wrapper) {
    width: 154px;
    // height: 14px;
    padding: 1px 2px;
}
.box-body {
    // border-bottom: 1px solid var(--border-color);
    padding: 40px 70px;
    text-align: center;
    min-height: 42px;
}
.box-footer {
    text-align: center;
    padding: 10px 0;
}
.add-assist-detail-content {
    margin: 10px;
    & .add-assist-detail-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 15px;
        margin-right: 15px;
        & .add-assist-detail-top-info {
            font-size: var(--h3);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            flex: 1;
            cursor: default;
        }
    }
    & .add-assist-detail-main {
        padding: 15px 15px 30px 15px;
        :deep(.cell) {
            & input[type="text"] {
                text-align: left !important;
            }
        }
        :deep(.el-input__wrapper) {
            height: 32px;
            margin-left: 0px;
            margin-right: 4px;
            padding-top: 3px;
            padding-bottom: 3px;
            padding-left: 10px;
            width: 337px;
        }
    }
}
.textbox-addon {
    position: absolute;
    top: 0;
}
.combo-arrow {
    overflow: hidden;
    display: inline-block;
    vertical-align: top;
    cursor: pointer;
    opacity: 0.6;
    background: url("@/assets/Settings/02.png") no-repeat center center;
}
.supplierList {
    position: relative;
    & .supplierName {
        display: flex;
        height: 30px;
        position: relative;
        & input {
            width: 100%;
            border: 1px solid #ccc;
        }
        & .activeInput {
            border-color: var(--main-color);
        }
        & .textbox-icon {
            opacity: 1;
            display: block;
            background-image: url(@/assets/Icons/down-black.png);
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            right: 0;
            height: 30px !important;
            width: 20px !important;
            margin-top: 1px;
            margin-right: 1px;
        }
    }
    :deep(.el-select){
        width: 100%;
    }
}
.supplierSeletorContainer {
    position: absolute;
    top: 30px;
    // left: 242px;
    z-index: 110001;
    display: block;
    & .supplierSeletor {
        // width: 178px;
        background-color: #fafafa;
        border: 1px solid #ccc;
        height: 198px;
        padding-bottom: 30px;
        overflow: auto;
        & .supplierItem {
            cursor: pointer;
            padding: 6px 8px 6px 7px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 16px;
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }
    & .add-btn {
        padding: 4px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
        position: absolute;
        left: 1px;
        right: 1px;
        bottom: 1px;
        background: #fff;
    }
}
.divAddAA {
    // margin-left: 87px;
    // & form {
    //     margin: 19px auto 0;
    // }
    & :deep(.el-input__wrapper) {
        width: 180px;
        height: 30px;
        padding: 1px 2px;
    }
}
.edit-assit-content {
    padding: 15px;
    & .input {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
    display: flex;

        & input {
            width: 260px;
            height: 32px;
            border: 1px solid var(--input-border-color);
            outline: none;
            padding: 0;
            padding-left: 10px;
            padding-right: 10px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            box-sizing: border-box;
            border-radius: var(--input-border-radius);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    }
}

:deep(.tooltip-trigger) {
    width: 100%;
    height: 40px;
    line-height: 40px;
    margin: 0;
}
