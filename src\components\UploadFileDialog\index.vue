<template>
    <el-dialog 
        v-model="display" 
        title="添加附件" 
        center 
        width="598" 
        class="custom-confirm dialogDrag" 
        @closed="handleAddDialogClosed"
    >
        <div class="dialog-content" v-dialogDrag ref="containerRef">
            <div class="top">
                <a
                    class="button solid-button mr-10"
                    :class="{ disabled: props.readonly }"
                    @click="handleNewFile"
                    v-if="checkPermission(getPermission())"
                    >电脑上传</a
                >
                <a
                    class="button mr-10"
                    style="width: 90px"
                    :class="{ disabled: props.readonly }"
                    @click="handleMobileUpload"
                    v-if="checkPermission(getPermission())"
                    >手机上传</a
                >
                <a
                    class="button large-2"
                    style="width: 157px"
                    :class="{ disabled: props.readonly }"
                    @click="handleFromERecord"
                    v-if="checkPermission(getPermission())"
                    >从会计电子档案选择</a
                >
            </div>
            <div class="main" @scroll="handleHideLastPreview">
                <div
                    v-for="item in fileList"
                    :key="item.fileId"
                    @click="handleFileClick(item)"
                    style="position: relative"
                    @mouseenter="handlePreview($event, item)"
                    @mouseleave="handleHideLastPreview"
                >
                    <a class="attach-list-content">
                        <span class="attach-list-img" :class="getIconClass(item.fileType)"></span>
                        <span class="attach-list-filename">{{ item.fileName }} （<span v-filesize="item.fileSize"></span>）</span>
                        <span class="attach-list-filepath">{{ item.relativePath && "所有文件/" + item.relativePath }}</span>
                    </a>
                    <span
                        class="btn-delete-file"
                        :class="isErp ? 'erp' : ''"
                        @mouseenter.stop="handleHideLastPreview"
                        @click.stop="handleDeleteFile(item)"
                        v-if="!props.readonly && checkPermission(getPermission())"
                    ></span>
                </div>
                <!-- 图片预览组件 -->
                <ul id="viewerjs">
                    <li v-for="(item, index) in picSrcList" :key="index">
                        <img :src="picSrcList[index].src" alt="" />
                    </li>
                </ul>
            </div>
            <div class="buttons mt-10" v-show="canSave">
                <a class="solid-button" :class="isErp ? '' : 'mr-10'" @click="saveFileChange()">确定</a>
                <a class="button" :class="{ 'ml-10': !isErp }" @click="handleFileCenterClose">取消</a>
            </div>
            <div class="preview-img" v-show="previewShow">
                <img :src="previewSrc" />
            </div>
        </div>
    </el-dialog>
    <el-dialog
        v-model="uploadNewFileParams.display"
        title="上传新文件"
        center
        width="688"
        top="10vh"
        @open="handleNewFileOpen"
        @close="handleClose"
        class="custom-confirm dialogDrag"
    >
        <div class="update-new-content" v-dialogDrag>
            <div class="top">
                <span>文件路径：</span>
                <div class="input" @click="() => (uploadNewFileParams.pathTreeDisplay = !uploadNewFileParams.pathTreeDisplay)">
                    <div class="text">{{ uploadNewFileParams.pathContent }}</div>
                    <div class="icon"></div>
                    <div class="tree" v-show="uploadNewFileParams.pathTreeDisplay">
                        <el-tree
                            node-key="id"
                            ref="uploadNewFileTreeRef"
                            :expand-on-click-node="false"
                            :indent="21"
                            :default-expand-all="true"
                            :data="pathList"
                            :props="pathTreeProps"
                            @node-click="handleNewFileTreeClick"
                        />
                    </div>
                </div>
                <a class="button solid-button ml-10" @click="upload">新增文件</a>
            </div>
            <div class="main" :class="isErp ? 'erp' : ''">
                <div v-for="(file, index) in uploadNewFileParams.fileList" :key="file">
                    <span class="file-icon" :class="'icon-' + addFileTypeImgPath(file.type)"></span>
                    <span class="upload-newfile-name">{{ file.name }}</span>
                    <span class="icon mr-20" @click="() => deleteNewFile(index)"></span>
                </div>
            </div>
            <div class="bottom">
                <el-popover
                    placement="right"
                    :width="300"
                    trigger="hover"
                    content="标记为纸档后，可筛选纸质档案查看对应凭证号，便于在整理纸质档案时在纸档上标注对应的凭证号或快速找到审计所需纸质文件"
                >
                    <template #reference>
                        <div style="display: inline-block">
                            <el-checkbox v-model="uploadNewFileParams.isPaper" label="标记为纸质档案"></el-checkbox>
                            <img class="tip" src="@/assets/Scm/question.png" />
                        </div>
                    </template>
                </el-popover>
            </div>
            <div class="buttons">
                <a class="solid-button" @click="deBounceBtnSureToUpload">确定</a>
                <a class="button" :class="{ 'ml-10': !isErp }" @click="btnCancelToUpload">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog
        v-model="fromERecordParams.display"
        title="从会计电子档案选择"
        width="918px"
        top="10vh"
        @open="handleERecordOpen"
        @close="handleClose"
        class="custom-confirm dialogDrag"
    >
        <div class="div-select-attach" v-dialogDrag>
            <div class="file-tree-tip-list">
                <div class="file-tree-tips">
                    <div class="float-label" style="margin-left: 20px">
                        <span>上传日期：</span>
                    </div>
                    <DatePicker
                        class="float-date"
                        v-model:start-pid="fromERecordParams.startDate"
                        v-model:end-pid="fromERecordParams.endDate"
                        :disabled-date-start="disabledDateStart"
                        :disabled-date-end="disabledDateEnd"
                        :clearable="true"
                    />
                    <el-checkbox
                        class="ml-10"
                        v-model="fromERecordParams.onlyShowNotRelateAttach"
                        @change="filterERecordTable"
                        label="只显示未关联档案"
                    />
                    <div class="float-input search-controller-e">
                        <input
                            type="text"
                            class="search-text-e"
                            placeholder="请输入关键字"
                            v-model="fromERecordParams.eRecordSecherTxt"
                            @keyup.enter="searchDoc"
                        />
                        <div class="search-submit-e" @click="searchDoc"></div>
                    </div>
                </div>
            </div>
            <div class="select-attach-body">
                <div class="select-attach-left">
                    <div class="top">文件夹</div>
                    <div class="tree">
                        <el-tree
                            node-key="id"
                            ref="eRecordTreeRef"
                            :data="pathList"
                            :indent="21"
                            :default-expand-all="true"
                            :expand-on-click-node="false"
                            :props="pathTreeProps"
                            @node-click="handleERecordTreeClick"
                        >
                        </el-tree>
                    </div>
                </div>
                <div class="select-attach-right" v-loading="loading" element-loading-text="正在加载数据...">
                    <Table
                        ref="UploadFileTableRef"
                        rowKey="fileId"
                        :emptyText="emptyText"
                        :data="fromERecordParams.displayFileList"
                        :columns="fromERecordParams.columns"
                        :selectable="judgeSelectable"
                        :virtualTable="true"
                        @selection-change="handleSelectionChange"
                        :tableName="setModule"
                    >
                        <template #fileName>
                            <el-table-column 
                                label="文件名称" 
                                min-width="100" 
                                header-align="left" 
                                align="left"
                                prop="fileName"
                                :width="getColumnWidth(setModule, 'fileName')"
                            >
                                <template #default="scope">
                                    <a class="link" v-if="haveDownloadPermission" @click="download(scope.row.fileId)">{{
                                        scope.row.fileName
                                    }}</a>
                                    <span v-else>{{ scope.row.fileName }}</span>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="select-attach-tip">已关联单据或凭证的电子档案不支持重复关联，所以不能选择哦~</div>
            <div class="buttons">
                <a class="button solid-button" :class="isErp ? '' : 'mr-10'" @click="eRecordDialogConfirm">确认选择</a>
                <a class="button" :class="{ 'ml-10': !isErp }" @click="eRecordDialogCancel">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 存在发票时提示弹窗 -->
    <el-dialog v-model="showTips" title="文件审核" center width="688" class="dialogDrag">
        <div class="tipsDialog" v-dialogDrag>
            <div class="main">
                <p>所选文件存在<span> 未查验通过 </span>的电子发票</p>
                <p>您可以</p>
                <ul>
                    <li>① 继续添加附件，则未查验通过的发票也会添加成功</li>
                    <li>② 选择跳过，则未查验通过的发票不会添加，添加其他文件</li>
                </ul>
                <!-- 注： -->
                <ul>
                    <li>
                        <i class="warining-icon"></i>
                        1. 目前系统支持一年内的增值税发票和全电发票的查验，其他类型的发票会显示查验失败
                    </li>
                    <li>2.您可以到列表筛选查验失败的发票重新查验或查看查验失败原因</li>
                </ul>
            </div>
            <div class="buttons">
                <a class="button mr-30" @click.prevent="addComfirm">继续添加</a>
                <a class="solid-button" @click.prevent="handleSkip">跳过</a>
            </div>
        </div>
    </el-dialog>
    <MobileUpload
        v-model="mobileUploadShow"
        ref="mobileUploadRef"
        :fid="mobileUploadFid"
        @close="handleMobileUpClose"
        @success-upload="handleMobileUploadSuccess"
    />
</template>

<script setup lang="ts">
import { request } from "@/util/service";
import { ref, reactive, computed, type Directive, nextTick } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import { ElTree } from "element-plus";
import { ElNotify } from "@/util/notify";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { isLemonClient } from "@/util/lmClient";
import {
    formatFileCategory,
    formatCheckState,
    getIconClass,
    mapToFileInfo,
    mapToMobileFileInfo,
    mapToERecordTableInfo,
    fileInfoFromERecordTomain,
    findPathById,
    getFileErrorTip,
    previewImg,
} from "./utils";
import dayjs from "dayjs";
import type { IFileInfo, IERecordTableInfo, IMobileUploadData } from "./types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import MobileUpload from "@/views/ERecord/components/MobileUpload.vue";
import Table from "@/components/Table/VirtualTable.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import { checkPermission } from "@/util/permission";
import { getGlobalLodash } from "@/util/lodash";
import { addFileTypeImgPath, formatDounmentType } from "@/views/ERecord/utils";
import { globalExport, globalPrint } from "@/util/url";
import { ElConfirm } from "@/util/confirm";
import { useLoading } from "@/hooks/useLoading";
import { onMounted, onUnmounted } from "vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { AttachFileCategory } from "@/views/ERecord/types";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { isInWxWork } from "@/util/wxwork";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const setModule = "UploadFileDialog";
const isErp = ref(window.isErp);
const _ = getGlobalLodash();
const uploadNewFileTreeRef = ref<InstanceType<typeof ElTree>>();
const eRecordTreeRef = ref<InstanceType<typeof ElTree>>();
const UploadFileTableRef = ref<InstanceType<typeof Table>>();
interface IPathInfo {
    id: number;
    text: string;
    children: IPathInfo[];
}
interface IPicSrc {
    fileId: number | string;
    src: string;
}
const props = defineProps<{
    readonly?: boolean;
}>();
const emit = defineEmits<{
    (e: "save", params: any, newFileids: number[], delFileids: number[], fileList: IFileInfo[], receiptFlag?: string): void;
    (e: "confirm-batch-rerelate"): void;
    (e: "only-rerelate-voucher"): void;
    (e: "only-rerelate-bill"): void;
}>();
const params = ref<any>();
const display = ref(false);
const canSave = ref(false);
const pathList = ref<IPathInfo[]>([]);
const fileList = ref<IFileInfo[]>([]);
const newFileids = ref<number[]>([]);
const delFileids = ref<number[]>([]);
const newInitFileid = ref(0);
// 图片预览
const picPreview = ref<boolean>(false);
// 图片预览地址列表
const picSrcList = ref<IPicSrc[]>([]);
const queryStartDate = ref("");
const queryEndDate = ref("");

// 会计电子档案弹窗
const eRecordDialogColumns: IColumnProps[] = [
    { slot: "selection", minWidth: 32, headerAlign: "center", align: "center" },
    { slot: "fileName" },
    {
        label: "文件类别",
        minWidth: 80,
        prop: "fileCategory",
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => formatFileCategory(value),
        width: getColumnWidth(setModule, 'fileCategory')
    },
    { 
        label: "上传日期", 
        minWidth: 80, 
        prop: "createdDate", 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'createdDate') 
    },
    { 
        label: "上传人", 
        minWidth: 80, 
        prop: "createdByName", 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'createdByName') 
    },
    {
        label: "关联单据类型",
        minWidth: 80,
        align: "left",
        headerAlign: "left",
        prop: "relatedDocumentTypes",
        formatter: (row, column, value) => formatDounmentType(value),
        width: getColumnWidth(setModule, 'relatedDocumentTypes')
    },
    { 
        label: "关联凭证", 
        minWidth: 100, 
        align: "left", 
        headerAlign: "left", 
        prop: "voucherGroupStr",
        width: getColumnWidth(setModule, 'voucherGroupStr')
    },
    {
        label: "校验状态",
        minWidth: 80,
        prop: "checkState",
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => formatCheckState(value),
        resizable: false,
    },
];
const fromERecordParams = reactive<{
    display: boolean;
    fileList: IERecordTableInfo[];
    displayFileList: IERecordTableInfo[];
    selectedFileList: IERecordTableInfo[];
    columns: IColumnProps[];
    startDate: string;
    endDate: string;
    eRecordSecherTxt: string;
    onlyShowNotRelateAttach: boolean;
}>({
    display: false,
    fileList: [],
    displayFileList: [],
    selectedFileList: [],
    columns: eRecordDialogColumns,
    startDate: "",
    endDate: "",
    eRecordSecherTxt: "",
    onlyShowNotRelateAttach: false,
});
const tempDate = reactive<{ startDate: string; endDate: string }>({
    startDate: "",
    endDate: "",
});

const judgeSelectable = (row: IERecordTableInfo): boolean => {
    const { source, vid } = row;
    return !source.length && !vid;
};

const handleERecordOpen = () => {
    eRecordTreeRef.value?.setCurrentKey(newInitFileid.value);
    //eRecordTreeRef.value?.getCurrentNode()不存在就先暂时找第一位,根治的话类别其他模块后端返回比较好
    if (eRecordTreeRef.value?.getCurrentNode() === null) {
        eRecordTreeRef.value?.setCurrentKey(pathList.value[0].children[0].id);
    }
    handleERecordTreeClick(eRecordTreeRef.value?.getCurrentNode());
};
const emptyText = ref("");
const loading = ref(false);
const handleERecordTreeClick = (data: any) => {
    loading.value = true;
    const fileId = data.id;
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetFileInfo.ashx?fid=" + fileId,
        method: "post",
    })
        .then((res: any) => {
            const tempFileList: IERecordTableInfo[] = res.Result.map((i: any) => mapToERecordTableInfo(i));
            const resultList = filterERecordTableByDate(tempFileList);
            const filterList = resultList.filter(
                (item) =>
                    item.fileName.indexOf(fromERecordParams.eRecordSecherTxt) !== -1 ||
                    formatFileCategory(item.fileCategory).indexOf(fromERecordParams.eRecordSecherTxt) !== -1
            );
            fromERecordParams.fileList = fromERecordParams.eRecordSecherTxt === "" ? resultList : filterList;
            filterERecordTable();

            if (!fromERecordParams.displayFileList.length) {
                emptyText.value = "暂无数据";
            } else {
                emptyText.value = "";
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
function filterERecordTable() {
    const displayList = fromERecordParams.onlyShowNotRelateAttach
        ? fromERecordParams.fileList.filter((item) => judgeSelectable(item)).slice()
        : fromERecordParams.fileList.slice();
    fromERecordParams.displayFileList.length = 0;
    UploadFileTableRef.value?.resetVirtualTableState();
    nextTick().then(() => {
        fromERecordParams.displayFileList = displayList;
        nextTick().then(() => {
            UploadFileTableRef.value?.updateView();
        })
    });
}
const filterERecordTableByDate = (list: IERecordTableInfo[]) => {
    if (!fromERecordParams.startDate && !fromERecordParams.endDate) {
        return list;
    } else if (!fromERecordParams.startDate && fromERecordParams.endDate) {
        return list.filter((item) => item.createdDate <= fromERecordParams.endDate);
    } else if (fromERecordParams.startDate && !fromERecordParams.endDate) {
        return list.filter((item) => item.createdDate >= fromERecordParams.startDate);
    } else {
        return list.filter((item) => item.createdDate >= fromERecordParams.startDate && item.createdDate <= fromERecordParams.endDate);
    }
};
function disabledDateStart(time: Date) {
    if (!fromERecordParams.endDate) return false;
    const endDate = dayjs(fromERecordParams.endDate).valueOf();
    return time.getTime() > endDate;
}

function disabledDateEnd(time: Date) {
    if (!fromERecordParams.startDate) return false;
    const startDate = dayjs(fromERecordParams.startDate).valueOf();
    return time.getTime() < startDate;
}
const handleSelectionChange = (val: any) => {
    const list: IERecordTableInfo[] = val;
    fromERecordParams.selectedFileList = list.filter((item) => judgeSelectable(item));
};

const eRecordDialogConfirm = () => {
    if (fromERecordParams.selectedFileList.length === 0) {
        return ElNotify({ type: "warning", message: "亲，请选择文件!" });
    }

    const tempList = fromERecordParams.selectedFileList.map((item) => fileInfoFromERecordTomain(item));
    const fileids = tempList.map((item) => item.fileId);
    for (let i = 0; i < fileids.length; i++) {
        const fileId = fileids[i];
        if (fileList.value.findIndex((item) => item.fileId === fileId) !== -1) {
            return ElNotify({ type: "warning", message: "亲，您已经选择过该文件，请勿重复选择！" });
        }
    }
    fileList.value = fileList.value.concat(...tempList);
    newFileids.value = newFileids.value.concat(...fromERecordParams.selectedFileList.map((item) => item.fileId));
    canSave.value = true;
    fromERecordParams.display = false;
    display.value = true;
};
const eRecordDialogCancel = () => {
    fromERecordParams.display = false;
    fromERecordParams.fileList = [];
    fromERecordParams.selectedFileList = [];
    fromERecordParams.startDate = "";
    fromERecordParams.endDate = "";
    fromERecordParams.eRecordSecherTxt = "";
    fromERecordParams.onlyShowNotRelateAttach = false;
    display.value = true;
};
const searchDoc = () => {
    handleERecordTreeClick(eRecordTreeRef.value?.getCurrentNode());
};
const changeERecordSearchDate = (startDate: string, endDate: string) => {
    tempDate.startDate = startDate;
    tempDate.endDate = endDate;
};

// 新增文件弹窗
const uploadNewFileParams = reactive<{
    display: boolean;
    pathTreeDisplay: boolean;
    pathContent: string;
    selectedPathId: number;
    fileList: any[];
    isPaper: boolean;
}>({
    display: false,
    pathTreeDisplay: false,
    pathContent: "",
    selectedPathId: 0,
    fileList: [],
    isPaper: false,
});
const handleNewFileTreeClick = (data: any) => {
    uploadNewFileParams.pathContent = findPathById(pathList.value, data.id);
    uploadNewFileParams.pathTreeDisplay = false;
    uploadNewFileParams.selectedPathId = data.id;
};
const download = (fileId: number) =>
    globalExport(window.jLmDiskHost + `/Services/Doc/DownloadDoc.ashx?fids=${fileId}`);
const deleteNewFile = (index: number) => {
    uploadNewFileParams.fileList.splice(index, 1);
};
const upload = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.multiple = true;
    input.onchange = (event: any) => {
        const file = event.target.files;
        Array.from(file).forEach((item: any) => {
            if (uploadNewFileParams.fileList.length !== 0) {
                if (uploadNewFileParams.fileList.some((file) => file.name === item.name)) {
                    return ElNotify({ type: "warning", message: "亲，您已经选择相同文件名的文件，请勿重复选择" });
                }
                return uploadNewFileParams.fileList.push(item);
            } else {
                uploadNewFileParams.fileList.push(item);
            }
        });
    };
    input.click();
};

function getAllCookies(): { key: string; value: string }[] {
    const cookies: { key: string; value: string }[] = [];

    if (isLemonClient()) {
        const keys: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                keys.push(key);
            }
        }
        const cookieList = keys.filter((key) => key.startsWith("fileCenter-" + getGlobalToken() + "-"));
        for (const cookie of cookieList) {
            const value = localStorage.getItem(cookie);
            if (value) {
                cookies.push({ key: cookie, value });
            }
        }
        return cookies;
    }

    if (document.cookie) {
        const cookieList = document.cookie.split(";");

        for (const cookie of cookieList) {
            const [key, value] = cookie.trim().split("=");

            cookies.push({ key, value });
        }
    }

    return cookies;
}
function deleteCookie(name: string) {
    document.cookie = encodeURIComponent(name) + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
}
const btnSureToUpload = () => {
    if (isErp.value) {
        useLoading().enterLoading("正在加载数据...");
    } else {
        useLoading().enterLoading("文件上传中，请稍候...");
    }
    if (uploadNewFileParams.fileList.length === 0) {
        ElNotify({ type: "warning", message: "亲，请选择文件" });
        useLoading().quitLoading();
        return;
    }
    let dataSize = 0;
    for (let i = 0; i < uploadNewFileParams.fileList.length; i++) {
        if (/^(video\/)/.test(uploadNewFileParams.fileList[i].type)) {
            ElNotify({ type: "warning", message: "无法上传视频类型的文件" });
            useLoading().quitLoading();
            return;
        }
        dataSize += uploadNewFileParams.fileList[i].size;
    }
    if (dataSize > 100 * 1024 * 1024) {
        ElNotify({ type: "warning", message: "文件大小不能超过100M" });
        useLoading().quitLoading();
        return;
    }

    if (dataSize > canUseSpace) {
        useLoading().quitLoading();
        if (useThirdPartInfoStoreHook().isThirdPart) {
            ElConfirm("您上传的文件大小已经超过可使用容量", true);
        }else if(window.isProSystem){
            ElConfirm("您的附件空间已达 10G 上限，无法上传，请联系侧边栏客服处理哦~", true);
        } else {
            // 这个弹窗应该会一直弹出专业版广告，所以这里的cookie应该是一直存在的，所以这里清除上次的文件中心的cookie，并设置一个新的
            const cookies = getAllCookies();
            const cookieItem = cookies.find((item) => item.key.startsWith("fileCenter-" + getGlobalToken() + "-"));
            if (cookieItem) {
                if (isLemonClient()) {
                    localStorage.removeItem(cookieItem.key);
                } else {
                    deleteCookie(cookieItem.key);
                }
            }
            if(window.isAccountingAgent || isInWxWork()){
                const msg = "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
                tryShowPayDialog(1, "fileCenter-" + getGlobalToken() + "-" + new Date().getTime(), msg, "会计电子档案");
            } else {
                handleTrialExpired({ msg: ExpiredToBuyDialogEnum.ERecordSpaceNone, needExpired: false });
            }
        }
        return;
    }
    ajaxFileUpload();
};
const deBounceBtnSureToUpload = _.debounce(() => {
    btnSureToUpload();
}, 500);
const ajaxFileUpload = () => {
    const fd = new FormData();
    // 发票会自动识别  除了凭证附件和银行回单  全部设置为其他单据
    let fileCategory = AttachFileCategory.Other;
    if (params.value.fileCategory === AttachFileCategory.Voucher) {
        fileCategory = AttachFileCategory.Voucher;
    } else if (params.value.fileCategory === AttachFileCategory.Receipt) {
        fileCategory = AttachFileCategory.Receipt;
    }
    uploadNewFileParams.fileList.forEach((item) => {
        fd.append("vfile", item);
    });
    fd.append("fid", uploadNewFileParams.selectedPathId.toString());
    fd.append("isBaseImage", "false");
    fd.append("isPaperArchives", uploadNewFileParams.isPaper ? "True" : "False");
    fd.append("fileCategory", fileCategory.toString());
    fd.append("submitType", "CheckFile"); // 检查文件是否电子发票
    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: fd,
    })
        .then((tdata: any) => {
            if (!tdata.Succeed) {
                ElNotify({ type: "warning", message: "亲，上传失败了" });
                uploadNewFileParams.display = false;
                uploadNewFileParams.fileList = [];
                return;
            }
            fd.append("fileCheckState", tdata.DetailMessage);
            request({
                url: window.jLmDiskHost + "/Services/Doc/UploadAccDoc.ashx",
                data: fd,
                method: "post",
                headers: { "Content-Type": "multipart/form-data" },
            })
                .then((res: any) => {
                    if (res.Succeed) {
                        // 图片预览地址列表
                        res.Result.forEach((item: { FileType: number; FileId: string; FilePath: string }) => {
                            if (item.FileType === 3010) {
                                picSrcList.value.push({
                                    fileId: Number(item.FileId),
                                    src: `${window.jLmDiskHost}/api/ImgHandler.ashx?fid=${item.FileId}&type=&asid=${
                                        getGlobalToken().split("I")[0]
                                    }`,
                                });
                            }
                        });

                        ElNotify({ type: "success", message: res.Message });
                        window.dispatchEvent(new CustomEvent("ERecordVoucherChange"));
                        const newFileList: IFileInfo[] = res.Result.map((i: any) => mapToFileInfo(i));
                        fileList.value.push(...newFileList);
                        newFileids.value = newFileids.value.concat(...newFileList.map((i) => i.fileId));
                        display.value = true;
                        canSave.value = true;
                    } else {
                        const message: string = res.Message ?? "亲，上传失败了";
                        ElNotify({ type: "warning", message: getFileErrorTip(message) });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "亲，上传失败了" });
                })
                .finally(() => {
                    uploadNewFileParams.display = false;
                    uploadNewFileParams.fileList = [];
                    useLoading().quitLoading();
                });
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "亲，上传失败了" });
            uploadNewFileParams.display = false;
            uploadNewFileParams.fileList = [];
            useLoading().quitLoading();
        });
};
const btnCancelToUpload = () => {
    uploadNewFileParams.display = false;
    uploadNewFileParams.pathTreeDisplay = false;
    display.value = true;
    uploadNewFileParams.fileList = [];
    uploadNewFileParams.isPaper = false;
};
const handleNewFileOpen = () => {
    uploadNewFileTreeRef.value?.setCurrentKey(newInitFileid.value);
    handleNewFileTreeClick(uploadNewFileTreeRef.value?.getCurrentNode());
};

const getPermission = () => {
    if (!params.value) return [""];
    if (params.value.permission) return params.value.permission;
    switch (params.value.fileCategory) {
        case 0:
            return ["voucher-canedit"];
        case 10:
            return ["invoice-output-canedit"];
        case 11:
            return ["invoice-input-canedit"];
        default:
            return ["voucher-canedit"];
    }
};

// 存在发票时提示弹窗
const showTips = ref<boolean>(false);
// 存在发票，确认添加
const addComfirm = () => {
    emit("save", params.value, newFileids.value, delFileids.value, fileList.value, "1");
    showTips.value = false;
    handleFileCenterClose();
};
// 跳过
const handleSkip = () => {
    let errorCount = fileList.value.length - canUseFileds.length;
    newFileids.value = canUseFileds;
    let successCount = newFileids.value.length;
    fileList.value = fileList.value.filter((item) => canUseFileds.indexOf(item.fileId) !== -1);
    fileList.value.length && emit("save", params.value, newFileids.value, delFileids.value, fileList.value, "2");
    _.debounce(() => {
        ElNotify({ type: "warning", message: `添加成功${successCount}个，失败${errorCount}个。 存在未查验通过的电子发票` });
    }, 500)();
    showTips.value = false;
    handleFileCenterClose();
};

// 主页面
const vFilesize: Directive = {
    mounted(el: any, binding: any) {
        if (typeof binding.value === "number") {
            let fileSize = Number(binding.value);
            let unit = "B";
            while (fileSize > 1024) {
                if (unit === "B") {
                    unit = "KB";
                } else if (unit === "KB") {
                    unit = "MB";
                } else if (unit === "MB") {
                    unit = "GB";
                } else if (unit === "GB") {
                    unit = "TB";
                } else if (unit === "TB") {
                    break;
                }
                fileSize = fileSize / 1024;
            }
            el.innerHTML = fileSize.toFixed(2) + unit;
        } else {
            el.innerHTML = binding.value;
        }
    },
};
const pathTreeProps = {
    children: "children",
    label: "text",
};
let canUseSpace = 10 * 1024 * 1024;
const deleteFile = (fileId: number) => {
    fileList.value = fileList.value.filter((item) => item.fileId !== fileId);
    delFileids.value.push(fileId);
    canSave.value = true;
};
const handleNewFile = () => {
    if (!props.readonly) {
        display.value = false;
        uploadNewFileParams.display = true;
    }
};
const handleFromERecord = () => {
    if (!props.readonly) {
        display.value = false;
        fromERecordParams.display = true;
    }
    if (queryStartDate.value && queryEndDate.value) {
        fromERecordParams.startDate = dayjs(queryStartDate.value).format("YYYY-MM-DD");
        fromERecordParams.endDate = dayjs(queryEndDate.value).format("YYYY-MM-DD");
    }
};
let saving = false;
let canUseFileds: number[] = [];
const saveFileChange = () => {
    if (saving) return;
    saving = true;
    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx",
        method: "post",
        params: { fileIds: fileList.value.map((f) => f.fileId).join(","), submitType: "GetCheckState" },
    }).then((res: any) => {
        if (!res.Succeed) {
            if (res.Message === "发票校验状态异常，无法添加该附件") {
                canUseFileds = fileList.value.length === 1 ? [] : res.DetailMessage.split(",").map((f: string) => Number(f));
                showTips.value = true;
            } else {
                canUseFileds = [];
                ElNotify({ type: "warning", message: res.Message });
            }
        } else {
            canUseFileds = [];
            emit("save", params.value, newFileids.value, delFileids.value, fileList.value);
            handleFileCenterClose();
            window.dispatchEvent(new CustomEvent("ERecordVoucherChange"));
        }
        saving = false;
    });
};
const handleFileCenterClose = () => {
    display.value = false;
    params.value = null;
    pathList.value = [];
    fileList.value = [];
    newFileids.value = [];
    delFileids.value = [];
    picSrcList.value = [];
    newInitFileid.value = 0;
    tempDate.startDate = "";
    tempDate.endDate = "";
    // loadingCount = 0;
    canSave.value = false;
};
const getDiskState = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetDiskState.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    }).then((res: any) => {
        if (res.Succeed === true) {
            canUseSpace = res.Result.Disk.TotalSpace - res.Result.Disk.UsedSpace || 0;
        }
    });
};
const getTreeListAndOpen = (id?: number) => {
    request({
        baseURL: window.jLmDiskHost,
        url: "/Services/Doc/GetPathInfo.ashx",
        method: "get",
        headers: { "Content-Type": "application/json" },
    })
        .then((res: any) => {
            pathList.value = res.Result;
            display.value = true;
            canSave.value = false;
            if (id) {
                newInitFileid.value = id;
            }
        })
        .finally(() => {
            canOpen = true;
        });
};
let canOpen = true;
const open = (_params: any, list: IFileInfo[], id?: number) => {
    if (!canOpen) return;
    canOpen = false;
    // loadingCount = 0;
    fileList.value = list;
    picSrcList.value = [];
    fileList.value?.forEach((item) => {
        if (item.fileType === 3010) {
            picSrcList.value.push({
                fileId: item.fileId,
                // src: item.webPath as string,
                // 拼接地址
                src: `${window.jLmDiskHost}/api/ImgHandler.ashx?fid=${item.fileId}&type=&asid=${getGlobalToken().split("I")[0]}`,
            });
        }
    });

    params.value = _params;
    if (_params.eRecordSearchDate) {
        queryStartDate.value = _params.eRecordSearchDate.startDate;
        queryEndDate.value = _params.eRecordSearchDate.endDate;
    }
    mobileUploadFid.value = id ?? 0;
    getTreeListAndOpen(id);
    getDiskState();
};
const haveDownloadPermission = computed(() => {
    return checkPermission(["lemondisk-candownload"]);
});

// 点击文件预览或下载
const handleFileClick = (val: IFileInfo) => {
    if (val.fileType === 3010) {
        picPreview.value = true;
        const index = picSrcList.value.findIndex((item) => item.fileId === val.fileId);
        const container = document.getElementById("viewerjs") as HTMLElement;
        previewImg(container, index);
    } else if (val.fileType === 2020) {
        globalPrint(window.jLmDiskHost + `/Services/Doc/DownloadDoc.ashx?fids=${val.fileId}`);
    } else {
        download(val.fileId);
    }
};

let timer: any;
const previewSrc = ref("");
const previewShow = ref(false);
const containerRef = ref<HTMLDivElement>();
function calcPreviewPosition(left: number, top: number) {
    const previewImg = containerRef.value?.querySelector(".preview-img") as HTMLElement;
    if (previewImg) {
        previewImg.style.left = left + "px";
        previewImg.style.top = top + "px";
    }
}
const moveInfo = ref({ x: 0, y: 0 });
function handleMouseMove(event: MouseEvent) {
    moveInfo.value = { x: event.clientX, y: event.clientY };
}
function handlePreview(event: MouseEvent, item: IFileInfo) {
    clearTimeout(timer);
    if (item.fileType !== 3010) return;
    const dom = event.target as HTMLElement;
    dom.addEventListener("mousemove", handleMouseMove);
    moveInfo.value = { x: event.clientX, y: event.clientY };
    timer = setTimeout(() => {
        previewSrc.value = findImgPath(item.fileId);
        previewShow.value = true;
        const x = moveInfo.value.x + 10;
        const y = moveInfo.value.y + 10;
        dom.removeEventListener("mousemove", handleMouseMove);
        calcPreviewPosition(x, y);
    }, 1000);
}
function handleHideLastPreview() {
    clearTimeout(timer);
    previewShow.value = false;
    moveInfo.value = { x: 0, y: 0 };
}
function findImgPath(fileId: number) {
    return picSrcList.value.find((item) => item.fileId == fileId)?.src || "";
}

const handleDeleteFile = (val: IFileInfo) => {
    if (val.fileType === 3010) {
        const fileIndex = picSrcList.value.findIndex((item) => item.fileId === val.fileId);
        picSrcList.value.splice(fileIndex, 1);
    }
    deleteFile(val.fileId);
};

const handleClose = () => {
    display.value = true;
};
const handleAddDialogClosed = () => {
    //所有弹窗都是关闭的时候清除是否上传成功轮询
    nextTick(() => {
        if (!(display.value || uploadNewFileParams.display || fromERecordParams.display || mobileUploadShow.value)) {
            newFileids.value = [];
            mobileUploadRef.value.clearFinishedTimer();
        }
    });
};

//手机上传
const mobileUploadShow = ref(false);
const mobileUploadRef = ref<any>(null);
const handleMobileUpload = () => {
    if (!props.readonly) {
        display.value = false;
        mobileUploadRef.value.getQRCode();
        mobileUploadShow.value = true;
    }
};
const handleMobileUpClose = () => {
    display.value = true;
};
//上传位置
const mobileUploadFid = ref(0);
const handleMobileUploadSuccess = (fileData: IMobileUploadData[]) => {
    fileData.forEach((item: IMobileUploadData) => {
        if (item.FileType === 3010) {
            picSrcList.value.push({
                fileId: Number(item.FileId),
                src: `${window.jLmDiskHost}/api/ImgHandler.ashx?fid=${item.FileId}&type=&asid=${getGlobalToken().split("I")[0]}`,
            });
        }
    });
    const newFileList: IFileInfo[] = fileData.map((i: IMobileUploadData) => mapToMobileFileInfo(i));
    fileList.value.push(...newFileList);
    newFileids.value = newFileids.value.concat(...newFileList.map((i) => i.fileId));
    canSave.value = true;
};
function resetDialogInfo() {
    uploadNewFileParams.display = false;
    fromERecordParams.display = false;
    mobileUploadShow.value = false;
    canSave.value = false;
    showTips.value = false;
    saving = false;
    canOpen = true;
    nextTick().then(() => {
        display.value = false;
    });
}

defineExpose({ open, changeERecordSearchDate, resetDialogInfo });
const reloadERecordDirList = () => {
    request({
        baseURL: window.jLmDiskHost,
        url: "/Services/Doc/GetPathInfo.ashx",
        method: "get",
        headers: { "Content-Type": "application/json" },
    }).then((res: any) => {
        pathList.value = res.Result;
    });
};
onMounted(() => {
    window.addEventListener("reloadERecordFileList", handleERecordOpen);
    window.addEventListener("reloadERecordDirList", reloadERecordDirList);
});
onUnmounted(() => {
    window.removeEventListener("reloadERecordFileList", handleERecordOpen);
    window.removeEventListener("reloadERecordDirList", reloadERecordDirList);
});
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.dialog-content {
    padding-bottom: 10px;
    .top {
        padding: 10px 20px;
    }
    .main {
        height: 280px;
        border: 1px solid var(--border-color);
        margin: 0 20px 10px;
        overflow: auto;
        & > div {
            position: relative;
            &:hover {
                background-color: var(--table-hover-color);
            }
        }
        .attach-list-content {
            padding: 10px 20px 10px 10px;
            display: block;
            position: relative;
            cursor: pointer;
            .attach-list-img {
                width: 30px;
                height: 30px;
                background-position: center;
                background-repeat: no-repeat;
                position: absolute;
                top: 12px;
                left: 10px;
            }
            .attach-list-filename,
            .attach-list-filepath {
                display: block;
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 17px;
                padding-left: 36px;
                padding-right: 30px;
                word-break: break-all;
            }
        }
        .btn-delete-file {
            position: absolute;
            top: 17px;
            right: 20px;
            cursor: pointer;
            background: url("@/assets/Cashier/trash.png") no-repeat;
            height: 20px;
            width: 20px;
            &:hover {
                background-image: url("@/assets/Cashier/trash-green.png");
            }
            &.erp {
                &:hover {
                    background-image: url("@/assets/Cashier/trash-green-erp.png");
                }
            }
        }
    }
    .preview-img {
        max-width: 400px;
        max-height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 100;
        img {
            max-width: 400px;
            max-height: 300px;
        }
    }
}
.update-new-content {
    overflow: hidden;
    .top {
        padding: 10px 20px;
        display: flex;
        align-items: center;
        div.input {
            flex: 1;
            height: 28px;
            box-sizing: border-box;
            border: 1px solid var(--border-color);
            cursor: text;
            display: flex;
            align-items: center;
            position: relative;
            div.text {
                flex: 1;
                height: 26px;
                box-sizing: border-box;
                padding: 0;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 28px;
                box-sizing: border-box;
                padding-left: 10px;
                overflow: hidden;
            }
            div.icon {
                height: 26px;
                width: 20px;
                cursor: pointer;
                margin-right: 1px;
                margin-top: 1px;
                background: url("@/assets/Icons/down-black.png") no-repeat center;
            }
            div.tree {
                position: absolute;
                top: 28px;
                left: 0;
                width: 100%;
                height: 200px;
                box-sizing: border-box;
                border: 1px solid var(--border-color);
                overflow: auto;
                background-color: var(--white);
                :deep(.el-tree) {
                    display: block;
                    .el-tree-node {
                        &.is-current {
                            & > .el-tree-node__content {
                                background-color: var(--table-title-color) !important;
                            }
                        }
                        &.is-focusable {
                            & > .el-tree-node__content {
                                background-color: var(--white);
                            }
                        }
                        .el-tree-node__content {
                            width: 100%;
                            height: 21px;
                            line-height: 21px;
                            font-size: 12px;
                            white-space: nowrap;
                            cursor: pointer;
                            .el-icon {
                                width: 21px;
                                height: 21px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                padding: 0;
                            }
                            &:hover {
                                background: var(--table-hover-color) !important;
                            }
                        }
                        .el-tree-node__label {
                            padding-left: 0;
                            background-image: none !important;
                        }
                    }
                }
            }
        }
    }
    .main {
        height: 280px;
        border: 1px solid var(--border-color);
        margin: 0 20px 10px;
        overflow: auto;
        div {
            min-height: 40px;
            border-bottom: 1px solid #eee;
            background-color: var(--white);
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--h5);
            line-height: var(--line-height);
            .file-icon {
                width: 28px;
                height: 28px;
                margin-left: 15px;
                &.icon-word {
                    background: url("@/assets/ERecord/word_new.png") no-repeat;
                }
                &.icon-excel {
                    background: url("@/assets/ERecord/excel_new.png") no-repeat;
                }
                &.icon-pdf {
                    background: url("@/assets/ERecord/pdf_new.png") no-repeat;
                }
                &.icon-ppt {
                    background: url("@/assets/ERecord/ppt.png") no-repeat center / 80%;
                }
                &.icon-txt {
                    background: url("@/assets/ERecord/txt.png") no-repeat center / 80%;
                }
                &.icon-zip {
                    background: url("@/assets/ERecord/zip.png") no-repeat center / 80%;
                }
                &.icon-img {
                    background: url("@/assets/ERecord/jpg_new.png") no-repeat;
                }
                &.icon-default {
                    background: url("@/assets/ERecord/default.png") no-repeat center / 88%;
                }
                &.icon-ofd_file {
                    background: url("@/assets/ERecord/ofd_file.png") no-repeat center / 80%;
                }
            }
            .upload-newfile-name {
                flex: 1;
                margin-left: 7px;
                word-break: break-all;
            }
            span.icon {
                width: 20px;
                height: 20px;
                background: url("@/assets/Icons/trash.png") no-repeat;
                cursor: pointer;
            }
            &:hover {
                background-color: var(--table-hover-color);
                span.icon {
                    background-image: url("@/assets/Icons/trash-green.png");
                }
            }
        }

        &.erp {
            div {
                &:hover {
                    span.icon {
                        background-image: url("@/assets/Cashier/trash-green-erp.png");
                    }
                }
            }
        }
    }
    .bottom {
        padding-left: 20px;
        img.tip {
            width: 16px;
            height: 16px;
            margin-left: 5px;
            transform: translateY(1px);
        }
    }
    .buttons {
        padding-bottom: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
.div-select-attach {
    color: #404040;
    font-size: 12px;
    .file-tree-tip-list {
        .file-tree-tips {
            height: 40px;
            padding: 0 20px 0 0;
            display: flex;
            align-items: center;
            .float-label {
                float: left;
            }
            .float-input {
                float: left;
            }
            .float-date {
                float: left;
            }
            .search-controller-e {
                position: relative;
                margin-left: 10px;
                flex-grow: 1;
                box-sizing: border-box;
                input {
                    .detail-original-input(100%, 28px);
                    font-size: 12px;
                }
                .search-submit-e {
                    position: absolute;
                    top: 6px;
                    right: 8px;
                    height: 16px;
                    width: 15px;
                    background: url("@/assets/Common/search-icon.png") no-repeat;
                    background-size: 15px 16px;
                    cursor: pointer;
                }
            }
            :deep(.picker) {
                .el-input {
                    height: 24px;
                    width: 110px;
                }
                & > span {
                    height: 24px;
                    line-height: 24px;
                    font-size: 12px;
                    width: 12px;
                }

                .el-date-editor {
                    & .el-input__suffix-inner {
                        right: 0px;
                        top: 5px;
                    }
                }
            }
        }
    }
    .select-attach-body {
        margin: 10px 20px 0;
        height: 350px;
        display: flex;
        .select-attach-left {
            width: 136px;
            box-sizing: border-box;
            border: 1px solid #dedede;
            margin-right: -1px;
            display: flex;
            flex-direction: column;
            margin-right: 10px;
            .top {
                height: 38px;
                background-color: #f2f2f2;
                box-sizing: border-box;
                color: var(--font-color);
                line-height: 38px;
                text-align: left;
                padding: 0 4px;
                color: var(--font-color);
                font-size: var(--table-title-font-size);
                font-weight: bold;
            }
            .tree {
                flex: 1;
                :deep(.el-tree) {
                    border-bottom: 1px solid var(--border-color);
                    .el-tree-node {
                        &.is-current {
                            & > .el-tree-node__content {
                                background-color: var(--table-hover-color) !important;
                            }
                        }
                        &.is-focusable {
                            & > .el-tree-node__content {
                                background-color: var(--white);
                            }
                        }
                    }
                    .el-tree-node__content {
                        width: auto;
                        min-width: 134px;
                        height: 37px;
                        border-top: 1px solid var(--border-color);
                        padding-right: 10px;
                        &:hover {
                            background-color: var(--table-hover-color) !important;
                        }
                    }
                }
            }
        }
        .select-attach-right {
            flex: 1;
            width: 0;
            :deep(.table) {
                height: 100%;
            }
            :deep(.el-table) {
                height: 100%;
                .el-scrollbar__view {
                    min-height: 310px;
                }
            }
        }
    }
    .select-attach-tip {
        margin-top: 10px;
        margin-left: 20px;
        text-align: left;
        font-size: var(--font-size);
        padding-left: 20px;
        background: url("@/assets/Icons/warn.png") no-repeat 0 2px;
    }
    .buttons {
        margin-top: 10px;
        border-top: 1px solid var(--border-color);
    }
}
.tree {
    width: 100%;
    height: auto;
    text-align: left;
    overflow: auto;
    .detail-el-tree();
    :deep(.el-tree) {
        .el-tree-node__content {
            width: 198px;
            box-sizing: border-box;
        }
    }
}
.spanTitle {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: keep-all;
}
.tree-indent,
.tree-file {
    display: inline-block;
    width: 16px;
    height: 18px;
    vertical-align: top;
    overflow: hidden;
    color: var(--font-color);
    font-size: var(--table-body-font-size);
    line-height: var(--table-body-line-height);
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tree-title {
    display: inline-block;
    div {
        display: flex;
        align-items: center;
    }
}
.tree-icon {
    background-size: contain !important;
}

.icon-word {
    background-image: url("@/assets/FileCenter/word.png");
}
.icon-excel {
    background-image: url("@/assets/FileCenter/excel.png");
}
.icon-ppt {
    background-image: url("@/assets/FileCenter/ppt.png");
}
.icon-access {
    background-image: url("@/assets/FileCenter/access.png");
}
.icon-txt,
.icon-notepad {
    background-image: url("@/assets/FileCenter/txt.png");
}
.icon-pdf {
    background-image: url("@/assets/FileCenter/pdf.png");
}
.icon-zip {
    background-image: url("@/assets/FileCenter/zip.png");
}
.icon-img,
.icon-picture {
    background-image: url("@/assets/FileCenter/img.png");
}
.icon-ofd {
    background-image: url("@/assets/FileCenter/ofd.png");
}
.icon-default,
.icon-other {
    background-image: url("@/assets/FileCenter/default.png");
}
// 发票提示弹窗
.tipsDialog {
    height: 340px;

    .main {
        width: 600px;
        margin: 0 auto;
        height: 280px;
        p {
            font-size: 16px;
            & ~ p {
                font-size: 14px;
            }
            span {
                color: #f00;
            }
        }
        ul {
            list-style: none;
            margin-bottom: 20px;
            li {
                position: relative;
                line-height: 30px;
                .warining-icon {
                    position: absolute;
                    top: 8px;
                    left: -20px;
                    width: 14px;
                    height: 14px;
                    background: url(../../assets/Settings/warnning-yellow.png) no-repeat 0 0;
                    background-size: 100% 100%;
                }
            }
            & ~ ul {
                li {
                    font-size: 12px;
                }
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}

// 图片预览
.pic-preview {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 999;
    .img-cont {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        // width: 300px;
        img {
            width: 300px;
        }
    }
}
#viewerjs {
    display: none;
}
.dialog-content.tip {
    .buttons {
        border-top: 1px solid var(--border-color);
        .button.large {
            width: 100px;
        }
    }
}
</style>
