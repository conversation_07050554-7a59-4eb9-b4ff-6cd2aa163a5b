<template>
  <div
    :id="boxId"
    class="box">
    <el-tooltip
      v-if="!checkResult"
      effect="light">
      <el-icon :class="checkType === 'error' ? 'red' : 'yellow'">
        <WarningFilled />
      </el-icon>
      <template #content>
        <div style="max-width: 500px">
          {{ checkPrompt }}
        </div>
      </template>
    </el-tooltip>
    <el-tooltip
      effect="light"
      :disabled="tooltipDisabled">
      <span @mouseenter="tooltipMouseEnter($event)">{{ formatValue }}</span>
      <template #content>
        <div style="max-width: 500px">
          {{ formatValue }}
        </div>
      </template>
    </el-tooltip>
  </div>
</template>
<script setup lang="ts">
  import { useCheckable } from "@/hooks/useCheckable"
  const props = withDefaults(
    defineProps<{
      tableValidateMessages?: any[]
      boxId?: string
      value?: string | number
    }>(),
    {},
  )

  const ruleCellRelationships = inject<any[]>("ruleCellRelationships")

  const { checkResult, checkType, checkPrompt, ruleToCell } = useCheckable()

  watch(
    () => props.tableValidateMessages,
    (newVal) => {
      ruleToCell(newVal, ruleCellRelationships, props.boxId as string)
    },
    { immediate: true },
  )

  const formatValue = computed(() => {
    if (props.value === null || props.value === "") {
      return ""
    }
    if (isNaN(props.value as number)) {
      // 非数字单元格
      return props.value
    }
    return String(parseFloat(props.value as string).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  })

  let tooltipDisabled = ref<boolean>(true)
  function tooltipMouseEnter(e: MouseEvent) {
    const target = e.target as HTMLElement

    // 创建一个临时的canvas元素
    var canvas = document.createElement("canvas")
    var context = canvas.getContext("2d")
    // 测量文本的宽度
    var width = context!.measureText(target.innerText).width
    const parent = target.parentElement

    tooltipDisabled.value = !(width > parent!.offsetWidth)
  }
</script>
<style lang="scss" scoped>
  .box {
    display: flex;
    justify-content: space-between;
    padding: 0 8px;

    .el-icon {
      font-size: var(--h3);
      cursor: pointer;

      &.red {
        color: var(--red);
      }

      &.yellow {
        color: var(--yellow);
      }
    }

    span {
      margin-left: auto;
      font-size: var(--h4);
      color: var(--light-grey);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
</style>
