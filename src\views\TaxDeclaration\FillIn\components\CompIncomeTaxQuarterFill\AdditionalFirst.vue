<template>
  <div>
    <div class="declaration-form">
      <div class="form-title">{{ tabData.describe }}</div>
      <div class="form-info">
        <div class="info">
          <div
            v-for="(item, index) in formHeaderInfo"
            :key="index">
            <span class="label">{{ item.label }}：</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- A201020固定资产加速折旧(扣除)优惠明细表 -->
    <div class="asset-depreciation-table">
      <LMTable
        :header-cell-style="headerCellStyle"
        ref="assetDepreciationTableRef"
        row-key="id"
        :data="assetDepreciationData"
        :columns="assetDepreciationColumns"
        border>
        <template #projectSelection="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              <el-select
                filterable
                v-if="!row.isFirstRow"
                v-model="row[slotColumn.prop]">
                <el-option
                  v-for="item in projectList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label" />
              </el-select>
            </template>
          </el-table-column>
        </template>
        <template #checkInput="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              <checkable-input
                v-if="!row.isFirstRow"
                v-model="row[slotColumn.prop]"
                :row-data="row"></checkable-input>
            </template>
          </el-table-column>
        </template>
        <template #operation>
          <el-table-column
            label="操作"
            width="120"
            align="center">
            <template #default="{ row }">
              <div class="operation-btns">
                <a
                  class="link"
                  v-if="(row.category === 1 && categoryOneCount < 5) || (row.category === 2 && categoryTwoCount < 5)"
                  @click.prevent="handleAddRow(row.category)">
                  增行
                </a>
                <a
                  class="link"
                  v-if="row.isFirstRow !== true"
                  @click.prevent="handleDeleteRow(row)">
                  删除
                </a>
              </div>
            </template>
          </el-table-column>
        </template>
      </LMTable>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { sumField } from "@/views/TaxDeclaration/utils"

  const props = defineProps({
    tabData: {
      type: Object,
      required: true,
    },
  })

  const formHeaderInfo = ref([
    { label: "税款所属期", value: "2024-10-01至2024-12-31" },
    { label: "填表日期", value: "2025-01-10" },
    { label: "金额单位", value: "元，至角分" },
  ])
  // 表格二 - 资产加速折旧优惠明细表相关
  // 表格列定义
  const assetDepreciationColumns = ref([
    { prop: "rowNum", label: "行次", align: "center" },
    { prop: "project", label: "项目", width: "200px", slot: "projectSelection" },
    { label: "本年享受优惠的资产原值", align: "center", children: [{ label: "1", slot: "checkInput", prop: "assetOriginalValue" }] },
    {
      label: "本年累计折旧\\摊销（扣除）金额",
      children: [
        { label: "账载折旧\\摊销金额", align: "center", children: [{ label: "2", slot: "checkInput", prop: "accountDepreciationAmount" }] },
        {
          label: "按照税收一般规定计算的折旧\\摊销金额",
          align: "center",
          children: [{ label: "3", slot: "checkInput", prop: "taxRegulationAmount" }],
        },
        {
          label: "享受加速政策计算的折旧\\摊销金额",
          align: "center",
          children: [{ label: "4", slot: "checkInput", prop: "acceleratedPolicyAmount" }],
        },
        { label: "纳税调减金额", align: "center", children: [{ label: "5", slot: "checkInput", prop: "taxReductionAmount" }] },
        { label: "享受加速政策优惠金额", align: "center", children: [{ label: "6(4-3)", slot: "checkInput", prop: "preferentialAmount" }] },
      ],
    },
    { children: [{ label: "7", slot: "operation" }] },
  ])

  const headerCellStyle = (_: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    // 这里可以根据需要添加样式逻辑
    // 目前未使用该函数，但保留以便将来扩展
    return {}
  }

  // 自增ID生成器
  let idCounter = 1
  const generateId = () => idCounter++

  // 表格数据
  const assetDepreciationData = ref([
    {
      id: generateId(),
      rowNum: "1",
      project: "其他行业研发设备加速折旧",
      assetOriginalValue: "0.00",
      accountDepreciationAmount: "0.00",
      taxRegulationAmount: "0.00",
      acceleratedPolicyAmount: "0.00",
      taxReductionAmount: "0.00",
      preferentialAmount: "0.00",
      category: 1,
      isFirstRow: true,
    },
    {
      id: generateId(),
      rowNum: "2",
      project: "一次性扣除（2.1****+...）",
      assetOriginalValue: "0.00",
      accountDepreciationAmount: "0.00",
      taxRegulationAmount: "0.00",
      acceleratedPolicyAmount: "0.00",
      taxReductionAmount: "0.00",
      preferentialAmount: "0.00",
      category: 2,
      isFirstRow: true,
    },
    {
      id: generateId(),
      rowNum: "3",
      project: "合计（1+2）",
      assetOriginalValue: "0.00",
      accountDepreciationAmount: "0.00",
      taxRegulationAmount: "0.00",
      acceleratedPolicyAmount: "0.00",
      taxReductionAmount: "0.00",
      preferentialAmount: "0.00",
      category: 3,
      isFirstRow: true,
    },
  ])

  // 需要累加的字段列表
  const fieldsToCalculate = [
    "assetOriginalValue",
    "accountDepreciationAmount",
    "taxRegulationAmount",
    "acceleratedPolicyAmount",
    "taxReductionAmount",
    "preferentialAmount",
  ] as const

  // 计算每个类别的当前行数
  const categoryOneCount = computed(() => {
    return assetDepreciationData.value.filter((item) => item.category === 1).length
  })

  const categoryTwoCount = computed(() => {
    return assetDepreciationData.value.filter((item) => item.category === 2).length
  })

  // 行操作方法
  const handleAddRow = (category: number) => {
    // 获取当前类别的所有行
    const categoryRows = assetDepreciationData.value.filter((item) => item.category === category)
    const lastRow = categoryRows[categoryRows.length - 1]
    const categoryPrefix = category + "."
    const nextRowIndex = categoryRows.length

    // 如果当前行是总行，则不允许添加新行
    if (category === 3) {
      return
    }

    // 创建新行
    const newRow = {
      id: generateId(),
      rowNum: `${categoryPrefix}${nextRowIndex}`,
      project: "",
      assetOriginalValue: "0.00",
      accountDepreciationAmount: "0.00",
      taxRegulationAmount: "0.00",
      acceleratedPolicyAmount: "0.00",
      taxReductionAmount: "0.00",
      preferentialAmount: "0.00",
      category: category,
      isFirstRow: false,
    }

    // 找到插入位置 - 在当前行的后面
    const insertIndex = assetDepreciationData.value.findIndex((item) => item.id === lastRow.id)
    assetDepreciationData.value.splice(insertIndex + 1, 0, newRow)
  }

  const handleDeleteRow = (row: any) => {
    const index = assetDepreciationData.value.findIndex((item) => item.id === row.id)
    if (index !== -1) {
      assetDepreciationData.value.splice(index, 1)

      // 重新编号该类别的行
      const categoryRows = assetDepreciationData.value.filter((item) => item.category === row.category && !item.isFirstRow)
      const categoryPrefix = row.category === 1 ? "1." : "2."

      categoryRows.forEach((item, index) => {
        item.rowNum = `${categoryPrefix}${index + 1}`
      })
    }
  }

  const projectList = ref([
    {
      value: "1",
      label: "项目1",
    },
    {
      value: "2",
      label: "项目2",
    },
    {
      value: "3",
      label: "项目3",
    },
  ])

  const category1Children = computed(() => {
    return assetDepreciationData.value.filter((item) => item.category === 1 && !item.isFirstRow)
  })

  const category2Children = computed(() => {
    return assetDepreciationData.value.filter((item) => item.category === 2 && !item.isFirstRow)
  })

  const category1Totals = computed(() => {
    const result: Record<string, string> = {}

    fieldsToCalculate.forEach((field) => {
      result[field] = sumField(category1Children.value, field)
    })

    return result
  })

  const category2Totals = computed(() => {
    const result: Record<string, string> = {}

    fieldsToCalculate.forEach((field) => {
      result[field] = sumField(category2Children.value, field)
    })

    return result
  })

  // 计算总行数据
  const grandTotals = computed(() => {
    const result: Record<string, string> = {}

    fieldsToCalculate.forEach((field) => {
      const total = parseFloat(category1Totals.value[field] || "0") + parseFloat(category2Totals.value[field] || "0")
      result[field] = total.toFixed(2)
    })

    return result
  })

  watch(
    [category1Totals, category2Totals, grandTotals],
    ([cat1Totals, cat2Totals, totals]) => {
      const parent1 = assetDepreciationData.value.find((item) => item.category === 1 && item.isFirstRow)
      if (parent1) {
        fieldsToCalculate.forEach((field) => {
          ;(parent1 as any)[field] = cat1Totals[field]
        })
      }

      const parent2 = assetDepreciationData.value.find((item) => item.category === 2 && item.isFirstRow)
      if (parent2) {
        fieldsToCalculate.forEach((field) => {
          ;(parent2 as any)[field] = cat2Totals[field]
        })
      }

      const totalRow = assetDepreciationData.value.find((item) => item.category === 3 && item.isFirstRow)
      if (totalRow) {
        fieldsToCalculate.forEach((field) => {
          ;(totalRow as any)[field] = totals[field]
        })
      }
    },
    { immediate: true },
  )
</script>

<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
  @include colspan-width(10);
  .table2 {
    display: flex;
    flex-direction: column;
    height: 100%;

    .asset-depreciation-table {
      flex: 1;
      margin-top: 15px;
      overflow: hidden;
    }
  }
</style>
