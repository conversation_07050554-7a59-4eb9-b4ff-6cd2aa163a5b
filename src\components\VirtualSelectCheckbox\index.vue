<template>
    <div class="select-checkbox" :style="{ width: width }" ref="selectCheckboxRef">
        <div class="top-box" @click="toggleCheckBoxShow" v-if="!hasPoppver">
            <Tooltip :maxWidth="overflowWidth" :content="placeHoderVal" :isInput="false" placement="right" :teleported="true">
                <div class="place-hoder" :class="{stretch: !useIcon, useIcon: useIcon}" @click="handlePlaceHoderClick">
                    {{ placeHoderVal }}
                </div>
            </Tooltip>
        </div>
        <div :class="['arrow-click', {'open': checkBoxShow}]">
            <template v-if="props.useIcon">
                <el-icon>
                    <ArrowDown />
                </el-icon>
            </template>
            <template v-else>
                <el-icon class="el-select__caret"></el-icon>
            </template>
        </div>
        <div :class="['check-box', {'haspoppover': hasPoppver}]" v-if="hasPoppver || checkBoxShow" style="overflow: visible;">
            <div class="select-box">
                <div class="select-flag" v-if="currenctList.length > 0">
                    <div class="select-flag-item" v-if="currenctList.length < 2">
                        <Tooltip 
                            :content="currenctList[0][assignProps.name]" 
                            :isInput="false" 
                            placement="top-start" 
                            :dynamicWidth="true" 
                            :teleported="true"
                            :fontSize="12"
                            :offset="2"
                        >
                            <span class="item-txt">{{ currenctList[0][assignProps.name] }}</span>
                        </Tooltip> 
                        <el-icon @click.stop="delSelect(currenctList[0][assignProps.id])"><Close /></el-icon>
                    </div>
                    <div class="select-flag-item item-more" v-else @click="visibleDetails = true">
                        <el-tooltip 
                            placement="bottom-start" 
                            effect="light" 
                            popper-class="detail-class" 
                            :teleported="false"
                            :visible="visibleDetails"
                            trigger="click"
                            :offset="5"
                        >
                            <template #content>
                                <el-scrollbar :max-height="170" :always="true" @mouseleave="handleMouseLeave" @scroll="handleScrollSel">
                                    <div :class="['detail-item', {'pr-20': currenctList.length > 7}]" v-for="(item, index) in currenctList" :key="item[assignProps.id]">
                                        <template v-if="index >= (currentPageSel - 3) * pageSizeSel && index < (currentPageSel + 2) * pageSizeSel">
                                            <Tooltip 
                                                :content="item[assignProps.name]" 
                                                :isInput="false" 
                                                placement="right" 
                                                :dynamicWidth="true" 
                                                :teleported="true"
                                                :fontSize="12"
                                                :offset="20"
                                            >
                                                <div class="detail-txt">{{ item[assignProps.name] }}</div>
                                            </Tooltip>
                                            <el-icon @click.stop="delSelect(item[assignProps.id])"><Close /></el-icon>
                                        </template>
                                        <div v-else :style="{ height: minHeightSel + 'px' }"></div>
                                    </div>
                                </el-scrollbar>
                            </template>
                            <span class="item-txt">已选 {{currenctList.length }} 项</span>
                        </el-tooltip>
                    </div>
                </div>
                <div class="input-box">
                    <input
                        ref="inputRef"
                        v-model="searchVal"
                        placeholder="请输入搜索"
                        type="text"
                        @input="handleInput"
                    />
                    <div class="searchIcon"></div>
                </div>
            </div>
            <el-scrollbar :max-height="196" :always="true" ref="scrollBarRef" @scroll="handleScroll">
                <el-checkbox name="all" v-show="showAll && showOptions.length > 0" v-model="all" @change="allChange">
                    全部
                </el-checkbox>
                <el-checkbox-group v-model="currenctListId" @change="handleCheckBoxClick">
                    <template v-for="(item, index) in showOptions" :key="item[assignProps.id]">
                        <template v-if="index >= (currentPage - 3) * pageSize && index < (currentPage + 2) * pageSize">
                            <Checkbox
                                :showTip="showTip"
                                :setTipShow="setTipShow"
                                :label="item[assignProps.id]"
                                :text="item[assignProps.name]"
                                :line-count="1"
                            ></Checkbox>
                        </template>
                        <div v-else :style="{ height: minHeight + 'px' }"></div>
                    </template>
                </el-checkbox-group>
                <div v-if="!showOptions.length" class="options-empty">无搜索结果</div>
            </el-scrollbar>
            <div class="new-add-button" v-if="bottomText">
                <a class="link" style="text-align: center" @click="handleClick">{{ bottomText }}</a>
            </div>
            <div class="new-slot-switch" v-if="bottomSwitch">
                <el-switch 
                    v-model="bottomSwitchValue" 
                    :active-value="true" 
                    :inactive-value="false" 
                    :active-text="bottomSwitchText"
                    @change="changeSwitchStatus"
                ></el-switch>
            </div>
            <slot></slot>
            <div class="buttons mt-10" :class="{'shadow' : showOptions.length}" v-if="hasPoppver">
                <a class="button solid-button" @click="confirmSelect">确定</a>
                <a class="button" @click="cancalSelect">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { ElScrollbar } from "element-plus";

import Checkbox from "@/components/SelectCheckbox/Checkbox.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
const { cloneDeep, isEqual, debounce } = getGlobalLodash();
const scrollBarRef = ref<InstanceType<typeof ElScrollbar>>();
const selectCheckboxRef = ref<HTMLDivElement>();

interface IProps {
    id: string;
    name: string;
}
const defaultProps: IProps = {
    id: "id",
    name: "name",
};
const props = withDefaults(
    defineProps<{
        options: Array<any>;
        selectedList: Array<number | string>;
        width?: string;
        showAll?: boolean;
        customProps?: IProps;
        useValueLabel?: boolean;
        connector?: string;
        customFilterMethod?: (query: string) => Array<any>;
        useIcon?: boolean;
        selectWithBlur?: boolean;
        blurMethod?: (query: string, fullListWithBlank?: boolean) => Array<any>;
        filterByExpenses?: boolean;
        filterStartCode?: string;
        hasPoppver?: boolean; //表头位置的多选
        forcedShutdown?: boolean;
        isSearchStatus?: boolean; //支持多选支持搜索
        bottomText?: string;
        maxWidth?: number;
        cacheLastSelect?: boolean;
        defaultNotSelectAll?: boolean;
        //开关
        bottomSwitch?: boolean;
        bottomSwitchText?: string;
        bottomSwitchValue?: boolean;
    }>(),
    {
        width: "200px",
        customProps: () => ({} as IProps),
        showAll: true,
        useValueLabel: false,
        connector: "；",
        useIcon: false,
        selectWithBlur: false,
        filterByExpenses: false,
        filterStartCode: "",
        hasPoppver: false,
        forcedShutdown: false,
        isSearchStatus: true,
        bottomText: "",
        maxWidth: 0,
        cacheLastSelect: false,
        defaultNotSelectAll: false,
        bottomSwitch: false,
        bottomSwitchText: "",
        bottomSwitchValue: false,
    }
);
const assignProps = computed(() => Object.assign({}, defaultProps, props.customProps));

const emit = defineEmits<{
    (event: "update:selectedList", args: Array<number | string>): void;
    (event: "checkBoxShowChange", args: boolean): void;
    // 输出匹配科目的数量，判断是否为空
    (event: "searchListLengthChange", show: boolean): void;
    (event: "multiSelectChange", data: Array<number | string>): void;
    (event: "handleClick"): void;
    (event: "change"): void;
    (event: "cancelSelect"): void;
    (event: "update:bottomSwitchValue", args: boolean): void;
    (event: "changeSwitchStatus"): void;
}>();
const bottomSwitchValue = computed({
    get() {
        return props.bottomSwitchValue || false;
    },
    set(value: boolean) {
        emit("update:bottomSwitchValue", value);
    },
});
const selectedList = computed({
    get() {
        return props.selectedList;
    },
    set(value: Array<number | string>) {
        emit("update:selectedList", value);
    },
});

// 需要拿父元素的宽度减掉左边距  不然会有12px的偏差
const overflowWidth = computed(() => ((props.useIcon && !props.maxWidth) ? 186 : (props.maxWidth ? props.maxWidth : 286)));
const placeHoderVal = ref("");
const inputRef = ref<HTMLInputElement>();
const checkBoxShow = ref(false);
const searchVal = ref("");
const all = ref(!props.defaultNotSelectAll);
const options = computed(() => props.options);
const showOptions = ref<Array<any>>([]);
const currenctList = ref<any[]>([]);
const currenctListId = ref<any[]>([]);
const visibleDetails = ref(false);

watch(
    () => props.forcedShutdown,

    () => {
        if (checkBoxShow.value) {
            checkBoxShow.value = false;
        }
    }
);

const allChange = (checked: any) => {
    if (checked) {
        showOptions.value.forEach((item) => {  
            const exists = currenctList.value.some((v) => item[assignProps.value.id] === v[assignProps.value.id]);  
            if (!exists) {  
                currenctList.value.push({ ...item });  
            }  
        });
    } else {
        if (currenctList.value.length === showOptions.value.length) {
            currenctList.value = [];
        } else {
            showOptions.value.forEach((item)=>{
                const index = currenctList.value.findIndex((v)=> item[assignProps.value.id] === v[assignProps.value.id]);
                if (index !== -1) {
                    currenctList.value.splice(index, 1);
                }
            })
        }
    }
    currenctListId.value = currenctList.value.map((item) => item[assignProps.value.id]);
    handleSearchBox();
    emit('change');
};

function restAll() {
    // 首先设置显示选项为所有选项
    showOptions.value = options.value;

    // 如果有传入的selectedList，确保currenctListId和currenctList正确反映这些选择
    if (selectedList.value.length) {
        // 设置当前选中的ID列表
        currenctListId.value = cloneDeep(selectedList.value);

        // 根据ID列表更新当前选中的项目列表
        currenctList.value = options.value
            .filter(item => selectedList.value.includes(item[assignProps.value.id]))
            .map(item => ({ ...item }));
    } else {
        currenctList.value = [];
        currenctListId.value = [];
    }

    // 如果是弹出框或有底部文本，并且有选中项，则过滤显示选项
    if ((props.hasPoppver || props.bottomText) && selectedList.value.length) {
        showOptions.value = options.value.filter((item)=>selectedList.value.includes(item[assignProps.value.id]));
    }

    // 检查是否所有选项都被选中
    const equal = isEqual(
        cloneDeep(selectedList.value).sort(),
        cloneDeep(showOptions.value).map((item:any) => item[assignProps.value.id]).sort()
    );

    // 更新全选状态
    all.value = selectedList.value.length === showOptions.value.length && selectedList.value.length !== 0 && equal;
}
watch(
    options,
    () => {
        restAll();
    },
    { immediate: true, deep: true }
);
watch(
    [currenctListId, showOptions],
    ()=>{
        all.value = showOptions.value.every((item) => currenctListId.value.includes(item[assignProps.value.id]));
    },
    { immediate: true, deep: true }
)
watch(
    [selectedList],
    () => {
        // 始终使用传入的selectedList作为勾选项
        currenctListId.value = cloneDeep(selectedList.value);

        // 更新当前选中列表
        getCurrentList();

        // 检查是否全选
        const equal = isEqual(
        cloneDeep(selectedList.value).sort(),
        cloneDeep(showOptions.value)
            .map((item: any) => item[assignProps.value.id])
            .sort()
        );
        if (props.cacheLastSelect) {
            currenctListId.value = cloneDeep(selectedList.value);
            getCurrentList();
        }
        all.value = selectedList.value.length === showOptions.value.length && selectedList.value.length !== 0 && equal;

        // 更新显示文本
        updatePlaceHolderVal();
    },
    { immediate: true, deep: true }
);

// 更新显示文本
function updatePlaceHolderVal() {
    const arr = options.value
        .filter((item) => selectedList.value.includes(item[assignProps.value.id]))
        .map((item) => item[assignProps.value[props.useValueLabel ? "id" : "name"]]);

    if (arr.length === 0) {
        placeHoderVal.value = "";
    } else if (arr.length < options.value.length) {
        placeHoderVal.value = arr.join(props.connector);
    } else if (arr.length === options.value.length) {
        placeHoderVal.value = "全部";
    }
}
function delSelect(key: any) {
    let index2 = currenctList.value.findIndex(item => item[assignProps.value.id] === key);
    currenctList.value.splice(index2, 1);
    let index = currenctListId.value.findIndex(item => item === key);
    currenctListId.value.splice(index, 1);
    handleSearchBox();
}
function confirmSelect() {
    if (props.hasPoppver) {
        if (!props.defaultNotSelectAll && currenctListId.value.length === 0) {
            currenctListId.value = props.options.map((item) => item[assignProps.value.id]);
        }
        emit("multiSelectChange", currenctListId.value);
    } else {
        checkBoxShow.value = false;
        searchVal.value = "";
        selectedList.value = cloneDeep(currenctListId.value);
        filterMethod(searchVal.value);
    }
}
function cancalSelect() {
    checkBoxShow.value = false;
    searchVal.value = "";
    filterMethod(searchVal.value);
    if(props.hasPoppver) {
        emit("cancelSelect");
    }
}
function changeSwitchStatus(val: boolean) {
    bottomSwitchValue.value = val;
    emit("changeSwitchStatus");
    nextTick(() => {
        selectedList.value = cloneDeep(currenctListId.value);
    })
}

function toggleCheckBoxShow() {
    checkBoxShow.value = !checkBoxShow.value;
    handlePlaceHoderClick();
}
function handlePlaceHoderClick() {
    if (!props.isSearchStatus) return;
    nextTick().then(() => {
        inputRef.value?.focus();
    });
}
function handleCheckBoxClick() {
    getCurrentList();
    handleSearchBox();
    emit('change');
}
function handleSearchBox() {
    if (!props.hasPoppver) {
        selectedList.value = cloneDeep(currenctListId.value);
    }
}
function getCurrentList() {
    currenctList.value = currenctListId.value.map(id => options.value.find(v => v[assignProps.value.id] === id))
    .filter(item => item !== undefined).map(item => ({ ...item }));
}
const showTip = ref(true);
function setTipShow(val: boolean) {
    showTip.value = val;
}
const pageSize = computed(() => 8);
const minHeight = computed(() => 28);
const currentPage = ref(0);
function handleScroll(event: { scrollLeft: number; scrollTop: number }) {
    showTip.value = false;
    currentPage.value = Math.max(Math.floor(event.scrollTop / (minHeight.value * pageSize.value)), 0);
}
//已选气泡虚拟滚动
const pageSizeSel = computed(() => 8);
const minHeightSel = computed(() => 24);
const currentPageSel = ref(0);
function handleScrollSel(event: { scrollLeft: number; scrollTop: number }) {
    currentPageSel.value = Math.max(Math.floor(event.scrollTop / (minHeightSel.value * pageSizeSel.value)), 0);
}
function handleMouseLeave() {
    visibleDetails.value = false;
    currentPageSel.value = 0;
}

function filterMethodFn(query: string) {
    if (!query.trim()) {
        showOptions.value = options.value;
        return;
    }
    if (props.customFilterMethod) {
        showOptions.value = props.customFilterMethod(query);
        return;
    }
    showOptions.value = commonFilterMethod(query, options.value, assignProps.value.name);
}
const filterMethod = debounce(filterMethodFn, 500);
function handleInput(e: Event) {
    checkBoxShow.value = true;
    filterMethod(searchVal.value);
}

function handleEnter() {
    closeCheckBoxWithOperate(true);
}

let cacheQuery = "";
function closeCheckBoxWithOperate(triggerByEnter = false) {
    checkBoxShow.value = false; 
    showOptions.value = options.value;
    scrollBarRef.value?.setScrollTop(0);
    currentPage.value = 0;
    currentPageSel.value = 0;
    if (!searchVal.value.trim() && triggerByEnter) {
        selectedList.value = [];
        searchVal.value = "";
        return;
    }
    if (placeHoderVal.value.trim() === cacheQuery) return;
    if (placeHoderVal.value.trim() === "全部") {
        selectedList.value = options.value.map((item) => item[assignProps.value.id]);
        cacheQuery = "全部";
        return;
    }
    if (props.selectWithBlur && props.blurMethod) {
        const blurSelectList = props.blurMethod(placeHoderVal.value, false);
        selectedList.value = blurSelectList.map((item) => item[assignProps.value.id]);
        cacheQuery = placeHoderVal.value;
    }
    nextTick().then(() => {
        searchVal.value = "";
    });
}

const setBoxNotShow = (e: MouseEvent) => {
    if (!selectCheckboxRef.value?.contains(e.target as HTMLElement)) {
        closeCheckBoxWithOperate();
    }
};

onMounted(() => {
    document.addEventListener("click", (e) => setBoxNotShow(e));
});
onUnmounted(() => {
    document.removeEventListener("click", setBoxNotShow);
    cacheQuery = "";
});

function closeCheckBox() {
    checkBoxShow.value = false;
}
defineExpose({ closeCheckBox, searchVal: searchVal.value, restAll });
watch(checkBoxShow, (visible) => {
    emit("checkBoxShowChange", visible);
});
const handleClick = () => {
    emit("handleClick");
};
</script>

<style lang="less" scoped>
:deep(.el-popper) {
    &.detail-class {
        padding-left: 0;
        padding-right: 0;
    }
    &.poppver-overflow {
        padding: 5px;
        .popover_content {
            max-width: 180px;
        }
    }
}
.select-box {
    display: flex; 
    align-items: center;
    padding: 0 4px;
    width: 100%;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    border: 1px solid var(--input-border-color);
    border-radius: 2px;
}
.input-box {
    flex: 1;
    min-width: 30px;
    height: 100%;
    position: relative;
    text-align: left;
    > input {
        width: calc(100% - 26px);
        height: 30px;
        outline: none;
        color: var(--font-color);
        font-size: 14px;
        line-height: 30px;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border: none;
        caret-color: var(--main-color);
    }
    .searchIcon {
        position: absolute;
        z-index: 999;
        top: 1px;
        right: 1px;
        width: 24px;
        height: 28px;
        background: #fff url("@/assets/ERecord/searchIcon.png") center center no-repeat;
        background-size: 14px 14px;
    }
}

.options-empty {
    width: 100%;
    text-align: center;
    line-height: 100px;
    color: #999;
}
.down {
    display: none;
}

.select-checkbox {
    position: relative;
    width: 200px;

    .check-box {
        width: 100%;
        border: 1px solid var(--border-color);
        box-sizing: border-box;
        background-color: var(--white);
        // max-height: 220px;
        overflow: auto;
        z-index: 200;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 34px;
        left: 0;
        // border-top: none;
        box-shadow: 0px 4px 16px 0px rgba(0,0,0,0.1);

        &.haspoppover {
            position: relative;
            top: 0;
            border: none;
            box-shadow: none;
            margin: 0 -11px;
            width: auto;
            .select-box {
                margin: 0 10px 5px;
                width: auto;
                border: 1px solid var(--border-color);
                border-radius: 2px;
            }
        }
        .select-box {
            border: none;
            border-bottom: 1px solid var(--border-color);
            border-radius: 0;
        }

        .buttons.mt-10 {
            margin-top: 10px;
            justify-content: flex-end;
        }

        :deep(.el-checkbox-group) {
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        :deep(.el-checkbox) {
            width: 100%;
            margin-right: 0;
            padding: 4px 8px;
            box-sizing: border-box;
            min-height: 20px;
            height: auto;

            &:hover {
                background-color: var(--table-hover-color);
            }

            .el-checkbox__input.is-checked + .el-checkbox__label {
                color: var(--font-color);
            }

            display: flex;
            align-items: flex-start;

            .el-checkbox__label {
                flex: 1;
                text-align: left;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                /* 设置最多显示1行 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                white-space: normal;
                word-break: break-all;
                text-align: left;
                line-height: var(--line-height);
                vertical-align: baseline;
                font-size: var(--font-size);
            }

            .el-checkbox__input {
                margin-top: 3px;
            }
        }

        :deep(.el-scrollbar) {
            .el-scrollbar__wrap {
                overflow-y: auto;
            }

            .el-scrollbar__view {
                display: flex;
                flex-direction: column;
                align-items: start;
            }
        }
    }
    .shadow {
        box-shadow: 0px -5px 6px 0px rgba(0,0,0,0.06);
    }
    .top-box {
        box-sizing: border-box;
        height: 32px;
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;

        :deep(.span_wrap) {
            width: 100%;
        }

        div.place-hoder {
            // width: 200px;
            width: 100%;
            height: 32px;
            position: absolute;
            top: 1px;
            z-index: 100;
            line-height: 30px;
            padding-left: 10px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            border: 1px solid var(--input-border-color);
            border-radius: 2px;
            text-align: left;

            &.stretch {
                width: 100%;
            }
            &.useIcon {
                padding-right: 20px;
            }
        }
    }
}
.select-flag {
    display: flex; 
    align-items: center;
    height: 100%;
    .select-flag-item {
        margin-right: 5px;
        display: flex;
        align-items: center;
        background-color: #f5f7fa; 
        padding: 0 4px; 
        height: 24px; 
        border-radius: 2px; 
        line-height: 24px;
        &.item-more {
            cursor: pointer;
            background-color: var(--main-color);
            .item-txt {
                color: var(--white);
            }
        }
        :deep(.el-scrollbar) {
            .el-scrollbar__wrap {
                overflow-y: auto;
            }
            .el-scrollbar__view {
                display: block;
            }

        }
    }
    .item-txt {
        max-width: 88px;
        display: block;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .el-icon {
        margin-left: 4px;
        font-size: 12px;
        color: #333;
        cursor: pointer;
    }
}
.arrow-click {
    position: absolute;
    right: 0;
    top: 0px;
    height: 100%;
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: start;
    padding-right: 1px;

    .el-icon {
        color: var(--el-text-color-placeholder);
        transition: transform var(--el-transition-duration);
        transform: rotateZ(0);
    }

    &.open {
        .el-icon {
            transform: rotateZ(-180deg);
        }
    }
}
.new-add-button {
    border-top: solid 1px var(--border-color); 
    padding: 0; 
    margin: 0; 
    text-align: center;
}
.new-slot-switch {
    border-top: solid 1px var(--border-color); 
    padding: 0 8px; 
    text-align: left;
}
</style>
<style lang="less">
.detail-class {
    padding-left: 0;
    padding-right: 0;
}
.detail-item {
    padding: 0 11px;
    display: flex;
    align-items: center;
    &:hover {
        background-color: var(--table-hover-color);
    }
    .detail-txt {
        max-width: 170px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 24px;
    }
    .el-icon {
        margin-left: 4px;
        font-size: 12px;
        color: #333;
        cursor: pointer;
    }
}
</style>
