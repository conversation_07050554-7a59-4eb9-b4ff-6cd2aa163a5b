import { checkHasScmSettings, getScmRelation,getScmRelationInfo } from "@/api/scm";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { globalWindowOpen, closeCurrentTab } from "@/util/url";
import { globalWindowOpenPage } from "./url";
import { getGlobalToken } from "./baseInfo";

export const checkHasScmSettingsWrapper = async (info: string) => {
    const res = await getScmRelationInfo();
    if (res.state !== 1000) {
        ElNotify({
            type: "error",
            message: res.msg,
        });
        return res;
    }
    if (res.data.isRelation) {
        const settingsRes = await checkHasScmSettings(res.data.scmProductType, res.data.scmAsid);
        if (settingsRes.state === 1000 && settingsRes.data) {
            // successCallback();
            return res;
        } else if (settingsRes.state === 2000) {
            ElNotify({
                type: "error",
                message: settingsRes.msg,
            });
        } else {
            const r = await ElConfirm("请先设置进销存核算参数，" + info, true);
            if (r) {
                if (useAccountSetStoreHook().permissions.includes("scmrelation-canedit")) {
                    // closeCurrentTab();
                    globalWindowOpenPage("/Scm/ScmSettings", "进销存核算参数");
                } else {
                    ElNotify({
                        type: "error",
                        message: "您没有设置进销存核算参数权限！",
                    });
                    // closeCurrentTab();
                    globalWindowOpenPage("/Default/Default", "首页");
                }
            }
        }
    } else {
        const scmCanShow = localStorage.getItem("scmShow-" + getGlobalToken()) === "true";
        if (useAccountSetStoreHook().permissions.includes("scmrelation-canedit") && scmCanShow) {
            const r = await ElConfirm("请先关联进销存账套，" + info, true);
            // if (r) {
            // closeCurrentTab();
            globalWindowOpenPage("/Scm/ScmRelation", "关联进销存");
        } else {
            ElConfirm("请联系账套管理员关联进销存账套，" + info, true);
        }
    }
    res.state = 2000;
    return res;
};

export const openScmTab = (url: string, scmProductType: number) => {
    const host = getScmHost(scmProductType);
    globalWindowOpen(host + "/#" + url);
};

export const getScmHost = (scmProductType?: number) => {
    if (scmProductType == 1020) {
        return window.scmProHost;
    } else {
        return window.scmHost;
    }
};
