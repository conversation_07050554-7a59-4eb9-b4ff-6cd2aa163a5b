import { checkPermission } from "@/util/permission";
import { ElNotify } from "@/util/notify";
import { formatMoney, formatNumberStr, toDecimal3 } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import type { IQueryParams, ITreeData, IVoucherCombineInfo } from "./types";
import type { FilterOrder, IColumnProps } from "@/components/Table/IColumnProps";

export enum ERPVoucherSummaryWay {
    None = 1000, // 不汇总
    All = 1001, // 按所有单据
    BillType = 1002, // 按单据类型
    BillDate = 1003, // 按单据日期
    Customer = 1004, // 按客户
    Supporter = 1005, // 按供应商
    Project = 1006, // 按项目
    Department = 1007, // 按部门
    Employee = 1008, // 按职员
    CDAccount = 1009, // 按账户
    IEType = 1100, // 按收支类别
    IEDirection = 1011, // 按收支方向
    InvoiceCategory = 1012, // 按发票种类
    PaymentType = 1013, // 费用类型
    OffsetType = 1014, // 核销类型
    BusinessType = 1015, // 业务类型
    OppositeParty = 1016, // 往来单位
}
const commonOptions = [
    { id: ERPVoucherSummaryWay.All, name: "合并成一张凭证" },
    { id: ERPVoucherSummaryWay.BillType, name: "按单据类型合并" },
    { id: ERPVoucherSummaryWay.BillDate, name: "按单据日期合并" },
];
export const listCombineOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.Customer, name: "按客户合并" },
    { id: ERPVoucherSummaryWay.Supporter, name: "按供应商合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.Department, name: "按部门合并" },
    { id: ERPVoucherSummaryWay.Employee, name: "按职员合并" },
    { id: ERPVoucherSummaryWay.CDAccount, name: "按账户合并" },
    { id: ERPVoucherSummaryWay.IEType, name: "按收支类别合并" },
    { id: ERPVoucherSummaryWay.IEDirection, name: "按收支方向合并" },
    { id: ERPVoucherSummaryWay.InvoiceCategory, name: "按发票种类合并" },
    { id: ERPVoucherSummaryWay.PaymentType, name: "按费用类型合并" },
    { id: ERPVoucherSummaryWay.OffsetType, name: "按核销类型合并" },
    { id: ERPVoucherSummaryWay.BusinessType, name: "按业务类型合并" },
    { id: ERPVoucherSummaryWay.OppositeParty, name: "按往来单位合并" },
];

const incomeOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.Supporter, name: "按供应商合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.Department, name: "按部门合并" },
    { id: ERPVoucherSummaryWay.Employee, name: "按职员合并" },
];
const expendOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.Customer, name: "按客户合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.Department, name: "按部门合并" },
    { id: ERPVoucherSummaryWay.Employee, name: "按职员合并" },
];
const othersWarehousingOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.Customer, name: "按客户合并" },
    { id: ERPVoucherSummaryWay.Supporter, name: "按供应商合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.Department, name: "按部门合并" },
    { id: ERPVoucherSummaryWay.Employee, name: "按职员合并" },
    { id: ERPVoucherSummaryWay.BusinessType, name: "按业务类型合并" },
];
const otherReceiptOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.Customer, name: "按客户合并" },
    { id: ERPVoucherSummaryWay.Supporter, name: "按供应商合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.Department, name: "按部门合并" },
    { id: ERPVoucherSummaryWay.Employee, name: "按职员合并" },
    { id: ERPVoucherSummaryWay.PaymentType, name: "按费用类型合并" },
    { id: ERPVoucherSummaryWay.OffsetType, name: "按核销类型合并" },
];
const cashierOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.OppositeParty, name: "按往来单位合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.Department, name: "按部门合并" },
    { id: ERPVoucherSummaryWay.CDAccount, name: "按账户合并" },
    { id: ERPVoucherSummaryWay.IEType, name: "按收支类别合并" },
    { id: ERPVoucherSummaryWay.IEDirection, name: "按收支方向合并" },
];
const invoiceOptions = [
    ...commonOptions,
    { id: ERPVoucherSummaryWay.Customer, name: "按客户合并" },
    { id: ERPVoucherSummaryWay.Supporter, name: "按供应商合并" },
    { id: ERPVoucherSummaryWay.Project, name: "按项目合并" },
    { id: ERPVoucherSummaryWay.InvoiceCategory, name: "按发票种类合并" },
];
enum ModuleBillType {
    Income = 1010,
    Expend = 1020,
    othersWarehousing = 1030,
    otherReceipt = 1040,
    Cashier = 1050,
    Invoice = 1060,
}

const voucherModuleInfo = [
    {
        title: "采购业务",
        type: ModuleBillType.Income,
        bills: ["采购入库", "采购退货"],
        voucherCombines: incomeOptions,
    },
    {
        title: "销售业务",
        type: ModuleBillType.Expend,
        bills: ["销售出库", "销售退货", "结转出库成本-出库", "结转出库成本-退货"],
        voucherCombines: expendOptions,
    },
    {
        title: "其他出入库",
        type: ModuleBillType.othersWarehousing,
        bills: ["其他入库", "其他出库", "组装", "拆卸", "盘盈", "盘亏", "成本调整"],
        voucherCombines: othersWarehousingOptions,
    },
    {
        title: "其他应收应付",
        type: ModuleBillType.otherReceipt,
        bills: ["其他支出", "其他收入", "核销"],
        voucherCombines: otherReceiptOptions,
    },
    {
        title: "资金流水",
        type: ModuleBillType.Cashier,
        bills: ["日记账收入", "日记账支出", "内部转账"],
        voucherCombines: cashierOptions,
    },
    {
        title: "发票数据",
        type: ModuleBillType.Invoice,
        bills: ["进项发票", "销项发票"],
        voucherCombines: invoiceOptions,
    },
];

export function getIncludeBills(type: number) {
    const bills = voucherModuleInfo.find((item) => item.type === type)?.bills;
    if (!bills) return "";
    return bills.join("、");
}

export function getDisplayModuleText(type: number, count: number) {
    const bill = voucherModuleInfo.find((item) => item.type === type);
    if (!bill) return "";
    const unit = bill.type === ModuleBillType.Cashier ? "笔" : "张";
    const appendText = [ModuleBillType.Income, ModuleBillType.Expend, ModuleBillType.othersWarehousing].includes(type) ? "单据" : "";
    return `<span style="font-weight: bold; color: #000;">共${count}${unit}</span> ${bill.title}${appendText}`;
}

const vtTypeIdMap = {
    1011: 501, // 采购入库
    1012: 502, // 采购退货
    1021: 503, // 销售出库
    1022: 504, // 销售退货
    1050: 550, // 其他入库单
    1051: 507, // 盘盈
    1052: 505, // 其他入库
    1060: 551, // 其他出库单
    1061: 508, // 盘亏
    1062: 506, // 其他出库
    1070: 509, // 成本调整
    1081: 510, // 结转出库成本-出库
    1082: 511, // 结转出库成本-退货
    1090: 517, // 其他收入
    1103: 544, // 其他支出-采购费用未分摊
    1104: 545, // 其他支出-采购费用已分摊
    1105: 546, // 其他支出-销售费用
    1106: 547, // 其他支出-管理费用
    1107: 548, // 其他支出-其他费用
    1108: 549, // 其他支出-代收代付
    1110: 512, // 组装
    1120: 513, // 拆卸
    1131: 514, // 核销-应收冲应付
    1132: 515, // 核销-应收转应收
    1133: 516, // 核销-应付转应付
    2010: 531, // 销项发票
    2020: 532, // 进项发票
    3030: 543, // 内部转账
};
export function getTemplateTypeId(billType: number) {
    const vtType = billType as keyof typeof vtTypeIdMap;
    return vtTypeIdMap[vtType];
}

export function getMergeOptions(type: number) {
    const options = voucherModuleInfo.find((item) => item.type === type)?.voucherCombines;
    if (!options) return [];
    return [...options];
}

export enum SearchType {
    // 客户
    Customer = 1,
    // 供应商
    Vender,
    // 项目
    Project,
    // 部门
    Department,
    // 职员
    Employee,
    // 是否生成凭证
    HasGenerateVoucher,
    // 凭证日期
    Vdate,
    // 凭证字号
    Vgid,
    // 采购员
    Purchaser,
    // 销售人员
    Sales,
    // 账户
    Account,
    // 收支类别
    Ietype,
    // 发票种类
    InvoiceType,
    // 发票类型
    InvoiceCategory,
    // 转出账户
    OutAccount,
    // 转入账户
    InAccount,
    // 组装商品名称
    AssemblyGoodsName,
    // 拆卸商品名称
    DisassemblyGoodsName,
    // 入库仓库
    InWarehouse,
    // 出库仓库
    OutWarehouse,
}
const searchInfoMap = [
    { searchType: SearchType.Customer, prop: "customer", label: "客户" },
    { searchType: SearchType.Vender, prop: "vender", label: "供应商" },
    { searchType: SearchType.Project, prop: "project", label: "项目" },
    { searchType: SearchType.Department, prop: "department", label: "部门" },
    { searchType: SearchType.Employee, prop: "employee", label: "职员" },
    { searchType: SearchType.HasGenerateVoucher, prop: "hasGenerateVoucher", label: "是否生成凭证" },
    { searchType: SearchType.Vdate, prop: "vdate", label: "凭证日期" },
    { searchType: SearchType.Vgid, prop: "vgid", label: "凭证字号" },
    { searchType: SearchType.Purchaser, prop: "purchaser", label: "采购员" },
    { searchType: SearchType.Sales, prop: "sales", label: "销售人员" },
    { searchType: SearchType.Account, prop: "account", label: "账户" },
    { searchType: SearchType.Ietype, prop: "ietype", label: "收支类别" },
    { searchType: SearchType.InvoiceCategory, prop: "invoiceCategory", label: "发票种类" },
    { searchType: SearchType.InvoiceType, prop: "invoiceType", label: "发票类型" },
    { searchType: SearchType.InAccount, prop: "account_out", label: "转出账户" },
    { searchType: SearchType.OutAccount, prop: "account_in", label: "转入账户" },
    { searchType: SearchType.AssemblyGoodsName, prop: "assemblyGoodsName", label: "组装商品名称" },
    { searchType: SearchType.DisassemblyGoodsName, prop: "disassemblyGoodsName", label: "拆卸商品名称" },
    { searchType: SearchType.InWarehouse, prop: "wareHouse_in", label: "入库仓库" },
    { searchType: SearchType.OutWarehouse, prop: "wareHouse_out", label: "出库仓库" },
];

const moduleSearchType: Array<{ text: string; id: number; searchs: Array<SearchType> }> = [
    // 包含了所有的根级节点以及所有的单据类型节点  核销比较特殊所以包含了核销的子级也就是业务类型
    { id: 1000, text: "全部", searchs: [1, 2, 3, 4, 5, 6, 7, 8] },

    { id: 1010, text: "采购管理", searchs: [2, 3, 4, 9, 6, 7, 8] },
    { id: 1011, text: "采购入库", searchs: [2, 3, 4, 9, 13, 6, 7, 8] },
    { id: 1012, text: "采购退货", searchs: [2, 3, 4, 9, 13, 6, 7, 8] },
    { id: 2020, text: "进项发票", searchs: [2, 3, 14, 6, 7, 8] },

    { id: 1020, text: "销售管理", searchs: [1, 3, 4, 10, 6, 7, 8] },
    { id: 1021, text: "销售出库", searchs: [1, 3, 4, 10, 6, 7, 8] },
    { id: 1022, text: "销售退货", searchs: [1, 3, 4, 10, 6, 7, 8] },
    { id: 2010, text: "销项发票", searchs: [1, 3, 14, 6, 7, 8] },
    { id: 1081, text: "结转出库成本(出库)", searchs: [1, 3, 4, 10, 6, 7, 8] },
    { id: 1082, text: "结转出库成本(退货)", searchs: [1, 3, 4, 10, 6, 7, 8] },

    { id: 1030, text: "应收应付", searchs: [1, 2, 3, 4, 5, 6, 7, 8] },
    { id: 1090, text: "其他收入", searchs: [1, 3, 4, 6, 7, 8] },
    { id: 1100, text: "其他支出", searchs: [1, 2, 3, 4, 5, 6, 7, 8] },
    { id: 1130, text: "核销", searchs: [1, 2, 6, 7, 8] },
    { id: 1131, text: "应收冲应付", searchs: [1, 2, 6, 7, 8] },
    { id: 1132, text: "应收转应收", searchs: [1, 6, 7, 8] },
    { id: 1133, text: "应付转应付", searchs: [2, 6, 7, 8] },

    { id: 1040, text: "资金管理", searchs: [11, 12, 1, 2, 5, 3, 4, 6, 7, 8] },
    { id: 3010, text: "现金日记账", searchs: [11, 12, 1, 2, 3, 4, 5, 6, 7, 8] },
    { id: 3020, text: "银行日记账", searchs: [11, 12, 1, 2, 3, 4, 5, 6, 7, 8] },
    { id: 3030, text: "内部转账", searchs: [16, 15, 6, 7, 8] },

    { id: 1140, text: "库存管理", searchs: [1, 2, 3, 4, 5, 6, 7, 8] },
    { id: 1050, text: "其他入库单", searchs: [1, 2, 3, 4, 6, 7, 8] },
    { id: 1060, text: "其他出库单", searchs: [1, 3, 4, 6, 7, 8] },
    { id: 1110, text: "组装", searchs: [17, 19, 6, 7, 8] },
    { id: 1120, text: "拆卸", searchs: [18, 20, 6, 7, 8] },
    { id: 1070, text: "成本调整", searchs: [] },
];

export function getSearchTypeText(type: SearchType) {
    return searchInfoMap.find((item) => item.searchType === type)?.label || "";
}

export function getModuleSearchType(currentNode: ITreeData | undefined, moduleTree: Array<ITreeData>) {
    const hasBusinessTypes = [1100, 1130, 3010, 3020, 1050, 1060]; // 只有 其他支出 核销 现金日记账 银行日记账 其他入库 其他出库 有业务类型
    const allSearchs = moduleSearchType.find((item) => item.id === 1000)!.searchs;
    if (!currentNode) return [...allSearchs];
    if (currentNode.children) {
        const searchs = moduleSearchType.find((item) => item.text === currentNode.text)?.searchs || [];
        return [...searchs];
    } else {
        const parent = findParentById(moduleTree, currentNode.id);
        if (!parent) return [...allSearchs];
        if (!hasBusinessTypes.includes(~~parent.id)) {
            const searchs = moduleSearchType.find((item) => item.text === currentNode.text)?.searchs || [];
            return [...searchs];
        } else {
            const searchs = moduleSearchType.find((item) => item.text === parent.text)?.searchs || [];
            return [...searchs];
        }
    }
}

function findParentById(tree: Array<ITreeData>, targetId: string, skipOffset = true): ITreeData | null {
    let parentNode: ITreeData | null = null;
    function traverse(nodes: Array<ITreeData>, parent?: ITreeData) {
        for (const node of nodes) {
            if (node.id === targetId) {
                parentNode = parent || null;
                if (skipOffset && parentNode?.id === "1130") {
                    // 核销单的查询条件和单据类型的不一样 所以核销单的查询条件是业务类型
                    parentNode = node;
                }
                return;
            }
            if (node.children) {
                traverse(node.children, node);
            }
        }
    }
    traverse(tree);
    return parentNode;
}

export function appendTreeLevel(data: Array<ITreeData>, level = 0) {
    data.forEach((item) => {
        item.level = level;
        if (item.children) {
            appendTreeLevel(item.children, level + 1);
        }
    });
}

export function getCurrentAllBillType(currentNode: ITreeData | undefined, moduleTree: Array<ITreeData>) {
    if (!currentNode) return [];
    if (currentNode.level >= 3) {
        const parent = findParentById(moduleTree, currentNode.id, false);
        if (parent) {
            return [{ id: parent.id, name: parent.text }];
        } else {
            return [];
        }
    } else {
        return deepLoopTree(currentNode);
    }
}
function deepLoopTree(currentNode: ITreeData) {
    const result: Array<{ id: string; name: string }> = [];
    function traverse(node: ITreeData) {
        if (node.children && node.level < 2) {
            node.children.forEach((item) => {
                traverse(item);
            });
        } else {
            result.push({ id: node.id, name: node.text });
        }
    }
    traverse(currentNode);
    return result;
}

const sortColumnListMap = [
    { sortEnum: 1, prop: "billDate", label: "单据日期" },
    { sortEnum: 2, prop: "billNo", label: "单据编号" },
    { sortEnum: 3, prop: "billType", label: "单据类型" },
    { sortEnum: 4, prop: "totalAmount", label: "单据金额" },
    { sortEnum: 5, prop: "projectName", label: "项目名称" },
    { sortEnum: 6, prop: "vendorName", label: "供应商名称" },
    { sortEnum: 7, prop: "customerName", label: "客户名称" },
    { sortEnum: 8, prop: "departmentName", label: "部门名称" },
    { sortEnum: 9, prop: "employeeName", label: "职员名称" },
    { sortEnum: 10, prop: "invoiceTypeName", label: "发票类型" },
    { sortEnum: 11, prop: "note", label: "备注" },
    { sortEnum: 12, prop: "description", label: "摘要" },
    { sortEnum: 13, prop: "expenseTypeName", label: "费用类型" },
    { sortEnum: 14, prop: "payeeName", label: "收款单位" },
    { sortEnum: 15, prop: "offsetTypeName", label: "核销类型" },
    { sortEnum: 16, prop: "transferName", label: "转出方" },
    { sortEnum: 17, prop: "transferToName", label: "转入方" },
    { sortEnum: 18, prop: "businessTypeName", label: "业务类型" },
    { sortEnum: 19, prop: "oppositeParty", label: "往来单位" },
    { sortEnum: 20, prop: "ieDirection", label: "收入/支出" },
    { sortEnum: 21, prop: "ieTypeName", label: "收入类别" },
    { sortEnum: 22, prop: "cdAccount", label: "银行账户/转出账户" },
    { sortEnum: 23, prop: "cdAccountIn", label: "转入账户" },
    { sortEnum: 24, prop: "assemblyName", label: "商品名称" },
    { sortEnum: 25, prop: "warehouses", label: "仓库" },
    { sortEnum: 26, prop: "quantity", label: "组装/拆卸数量" },
    { sortEnum: 27, prop: "vtName", label: "凭证模板" },
    { sortEnum: 28, prop: "voucherInfo", label: "关联凭证" },
];
export function getCurrentSortColumn(prop: string) {
    return sortColumnListMap.find((item) => item.prop === prop)?.sortEnum || 1;
}
export enum RelationVoucherState {
    All = 0,
    Relation = 1010,
    UnRelation = 1020,
}
// 列查询参数
export class ColSearchs {
    // 单据日期
    startBillDate = "";
    // 单据日期
    endBillDate = "";
    // 单据编号
    billNo = "";
    // 单据类型
    billType: Array<number> = [];
    // 单据起始金额
    startAmount = "";
    // 单据结束金额
    endAmount = "";
    // 项目名称
    projectNames: Array<string> = [];
    // 供应商名称
    vendorNames: Array<string> = [];
    // 客户名称
    customerNames: Array<string> = [];
    // 部门名称
    departmentNames: Array<string> = [];
    // 职员名称
    employeeNames: Array<string> = [];
    // 发票类型
    invoiceTypeNames: Array<string> = [];
    // 备注
    note = "";
    // 摘要
    description = "";
    // 费用类型
    expenseTypeNames: Array<string> = [];
    // 收款单位
    payeeNames: Array<string> = [];
    // 核销类型
    offsetTypeNames: Array<string> = [];
    // 转入方
    transferToNames: Array<string> = [];
    // 往来单位  业务类型
    businessTypeNames: Array<string> = [];
    // 收入/支出
    ieDirections: Array<string> = [];
    // 收支类别
    ieTypeNames: Array<string> = [];
    // 转出账户
    cdAccounts: Array<string> = [];
    // 转入账户
    cdAccountIns: Array<string> = [];
    // 商品名称
    assemblyNames: Array<string> = [];
    // 仓库
    warehousess: Array<string> = [];
    // 开始数量
    startQuantity = "";
    // 结束数量
    endQuantity = "";
    // 凭证模板
    vtNames: Array<string> = [];
}
// 主查询参数
class MainSearchs {
    // 开始时间
    startDate = "";
    // 结束时间
    endDate = "";
    // 节点id
    nodeId = "1000";
    // 单据类型
    billTypes: Array<number> = []; // 只有发票模块查看单据才会传值
    // 关联凭证状态
    relationVoucherState: RelationVoucherState = RelationVoucherState.All;
    // 查询数据(同时查询单据编号、客户名称、供应商名称)
    searchData = "";

    // 客户名称
    customerIds: Array<number> = [];
    // 供应商名称
    vendorIds: Array<number> = [];
    // 项目id
    projectIds: Array<number> = [];
    // 部门id
    departmentIds: Array<number> = [];
    // 职员id
    employeeIds: Array<number> = [];
    // 是否生成凭证
    hasVoucher = -1; // -1 全部 0 未生成 1 已生成
    // 凭证字
    vgId = 0;
    // 凭证号
    startVNum = "";
    // 凭证号
    endVNum = "";
    // 凭证开始时间
    startVDate = "";
    // 凭证结束时间
    endVDate = "";
    // 账户
    cdAccounts: Array<number> = [];
    // 转出账户
    cdAccountOuts: Array<number> = [];
    // 转入账户
    cdAccountIns: Array<number> = [];
    // 收支类别
    ieTypes: Array<number> = [];
    // 发票类型
    invoiceTypes: Array<string> = [];
    // 商品名称
    stockIds: Array<number> = [];
    // 仓库
    whIds: Array<number> = [];
}
export class Searchs {
    customer: Array<number> = [];
    vender: Array<number> = [];
    project: Array<number> = [];
    department: Array<number> = [];
    employee: Array<number> = [];
    hasGenerateVoucher = -1;
    vdate_s = "";
    vdate_e = "";
    vgid = 0;
    vnum_s = "";
    vnum_e = "";
    purchaser: Array<number> = []; // 走职员
    sales: Array<number> = []; // 走职员
    account: Array<number> = [];
    ietype: Array<number> = [];
    invoiceType: Array<string> = [];
    invoiceCategory: Array<string> = []; // 走发票类型
    account_in: Array<number> = [];
    account_out: Array<number> = [];
    assemblyGoodsName: Array<number> = []; // 走商品名称
    disassemblyGoodsName: Array<number> = []; // 走商品名称
    wareHouse_in: Array<number> = []; // 走仓库
    wareHouse_out: Array<number> = []; // 走仓库
}
export function combineSearchs(customerSearchs: Searchs, condition: Array<SearchType>) {
    const requestSearchs = new MainSearchs();
    const assignSearchs = {
        customerIds: customerSearchs.customer,
        vendorIds: customerSearchs.vender,
        projectIds: customerSearchs.project,
        departmentIds: customerSearchs.department,
        employeeIds: customerSearchs.employee,
        hasVoucher: customerSearchs.hasGenerateVoucher,
        startDate: customerSearchs.vdate_s,
        endDate: customerSearchs.vdate_e,
        vgId: ~~customerSearchs.vgid,
        startVNum: customerSearchs.vnum_s,
        endVNum: customerSearchs.vnum_e,

        startVDate: customerSearchs.vdate_s,
        endVDate: customerSearchs.vdate_e,

        cdAccounts: customerSearchs.account,
        ieTypes: customerSearchs.ietype,

        invoiceTypes: customerSearchs.invoiceType,

        cdAccountOuts: customerSearchs.account_out,
        cdAccountIns: customerSearchs.account_in,

        stockIds: customerSearchs.assemblyGoodsName,
        whIds: customerSearchs.wareHouse_in,
    };
    Object.assign(requestSearchs, assignSearchs);
    if (condition.includes(SearchType.Purchaser)) {
        requestSearchs.employeeIds = customerSearchs.purchaser;
    }
    if (condition.includes(SearchType.Sales)) {
        requestSearchs.employeeIds = customerSearchs.sales;
    }
    if (condition.includes(SearchType.InvoiceCategory)) {
        requestSearchs.invoiceTypes = customerSearchs.invoiceCategory;
    }
    if (condition.includes(SearchType.DisassemblyGoodsName)) {
        requestSearchs.stockIds = customerSearchs.disassemblyGoodsName;
    }
    if (condition.includes(SearchType.OutWarehouse)) {
        requestSearchs.whIds = customerSearchs.wareHouse_out;
    }
    return requestSearchs;
}

const aaeSearchs = [
    SearchType.Customer,
    SearchType.Vender,
    SearchType.Employee,
    SearchType.Department,
    SearchType.Project,
    SearchType.Purchaser,
    SearchType.Sales,
    SearchType.AssemblyGoodsName,
    SearchType.DisassemblyGoodsName,
];
export const accountSearchs = [SearchType.Account, SearchType.InAccount, SearchType.OutAccount];
export function getCircleKey(searchType: SearchType) {
    const key = searchInfoMap.find((item) => item.searchType === searchType)?.prop || "";
    return key as keyof Searchs;
}
export function checkUsePublicSelect(searchOption: SearchType) {
    if (aaeSearchs.includes(searchOption)) return true;
    if (accountSearchs.includes(searchOption)) return true;
    if (searchOption === SearchType.InvoiceCategory) return true;
    const publicOptionSearchs = [SearchType.Ietype, SearchType.InvoiceType, SearchType.InWarehouse, SearchType.OutWarehouse];
    if (publicOptionSearchs.includes(searchOption)) return true;
    return false;
}

export const subjectOption = [
    { id: 2001, name: "相同借方科目合并" },
    { id: 2002, name: "相同贷方科目合并" },
];

export function checkBillNoPermission(billType: number) {
    const isCashJournal = [3010, 3011, 3012].includes(billType);
    const isDepositJournal = [3020, 3021, 3022].includes(billType);
    if (billType === 2010 && !checkPermission(["invoice-output-canview"])) return false;
    if (billType === 2020 && !checkPermission(["invoice-input-canview"])) return false;
    if (isCashJournal && !checkPermission(["cashjournal-canview"])) return false;
    if (isDepositJournal && !checkPermission(["depositjournal-canview"])) return false;
    if (billType === 3030 && !checkPermission(["transfer-canview"])) return false;
    if (billType == 1011 && !checkPermission(["Warehousing-查看"])) return false;
    if (billType == 1012 && !checkPermission(["WarehousingReturn-查看"])) return false;
    if (billType == 1021 && !checkPermission(["SellDelivery-查看"])) return false;
    if (billType == 1022 && !checkPermission(["SellReturn-查看"])) return false;
    if (billType == 1081 && !checkPermission(["SellDelivery-查看"])) return false;
    if (billType == 1082 && !checkPermission(["SellReturn-查看"])) return false;
    if (billType == 1090 && !checkPermission(["OtherReceipt-查看"])) return false;
    if (billType == 1100 && !checkPermission(["OtherPayment-查看"])) return false;
    if (billType == 1131 && !checkPermission(["Offset-查看"])) return false;
    if (billType == 1132 && !checkPermission(["Offset-查看"])) return false;
    if (billType == 1133 && !checkPermission(["Offset-查看"])) return false;
    if (billType == 1052 && !checkPermission(["OthersWarehousing-查看"])) return false;
    if (billType == 1062 && !checkPermission(["OthersWarehousingOut-查看"])) return false;
    if (billType == 1110 && !checkPermission(["Assembly-查看"])) return false;
    if (billType == 1120 && !checkPermission(["Disassembly-查看"])) return false;
    if (billType == 1051 && !checkPermission(["OthersWarehousing-查看"])) return false;
    if (billType == 1050 && !checkPermission(["OthersWarehousing-查看"])) return false; // 其他入库
    if (billType == 1061 && !checkPermission(["OthersWarehousingOut-查看"])) return false;
    if (billType == 1060 && !checkPermission(["OthersWarehousingOut-查看"])) return false; // 其他出库
    if (billType == 1070 && !checkPermission(["CostAdjustment-查看"])) return false;
    return true;
}
const setModule = "ErpBusinessVoucher";
function tryInsertColumnsBeforeAmount(parentid: number, billType: number, columns: Array<IColumnProps>) {
    // 采购管理 销售管理 结转出库成本 其他收入 其他支出
    if ([1010, 1020, 1080, 1090, 1100].includes(parentid)) {
        let name = "";
        let prop = "name";
        let alias: undefined | string = undefined;
        let filterOrder: undefined | FilterOrder = "multiSelect";
        if ([1011, 1012].includes(billType)) {
            // 采购管理
            name = "供应商";
            prop = "vendorName";
        } else if ([1021, 1022, 1081, 1082, 1090].includes(billType)) {
            // 销售管理
            name = "客户";
            prop = "customerName";
        } else if (parentid === 1100) {
            // 其他支出
            name = "收款单位";
            prop = "name";
            alias = "oppositeParty";
            filterOrder = "text";
        }
        if (name) {
            columns.splice(4, 0, {
                label: name,
                prop,
                align: "left",
                headerAlign: "left",
                minWidth: 120,
                filterOrder,
                alias,
                headerSort: true,
                width: getColumnWidth(setModule, prop),
            });
        }
    } else if ([1130].includes(parentid)) {
        // 核销单
        const columnInfo = ["", "transferName", "", "transferToName"];
        let aliasList: Array<undefined | string> = [undefined, undefined];
        if (billType === 1131) {
            columnInfo[0] = "客户";
            columnInfo[2] = "供应商";
            aliasList = ["customerName", "vendorName"];
        } else if (billType === 1132) {
            columnInfo[0] = "转出客户";
            columnInfo[2] = "转入客户";
        } else if (billType === 1133) {
            columnInfo[0] = "转出供应商";
            columnInfo[2] = "转入供应商";
        } else if (billType === 1130) {
            columnInfo[0] = "转出往来单位";
            columnInfo[2] = "转入往来单位";
        }
        if (columnInfo.every((item) => item)) {
            const appendColumns: Array<IColumnProps> = [
                {
                    label: columnInfo[0],
                    prop: columnInfo[1],
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    alias: aliasList[0],
                    width: getColumnWidth(setModule, columnInfo[1]),
                },
                {
                    label: columnInfo[2],
                    prop: columnInfo[3],
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    alias: aliasList[1],
                    width: getColumnWidth(setModule, columnInfo[3]),
                },
            ];
            if (billType === 1131) {
                appendColumns.forEach((item) => {
                    item.filterOrder = "multiSelect";
                });
            }
            columns.splice(4, 0, ...appendColumns);
        }
    }
}
function tryInsertColumnsBeforeNote(parentid: number, billType: number, columns: Array<IColumnProps>, taxEnabled: boolean) {
    // 成本调整 组装 拆卸 核销
    if (![1070, 1110, 1120, 1130].includes(parentid)) {
        const insertColumns: Array<IColumnProps> = [
            {
                label: "项目",
                prop: "projectName",
                align: "left",
                headerAlign: "left",
                minWidth: 120,
                filterOrder: "multiSelect",
                width: getColumnWidth(setModule, "projectName"),
            },
            {
                label: "部门",
                prop: "departmentName",
                align: "left",
                headerAlign: "left",
                minWidth: 120,
                filterOrder: "multiSelect",
                width: getColumnWidth(setModule, "departmentName"),
            },
        ];
        // 采购管理 销售管理 结转出库成本
        if ([1010, 1020, 1080].includes(parentid)) {
            let name = "销售人员";
            if ([1010].includes(parentid)) {
                name = "采购员";
            }
            insertColumns.push({
                label: name,
                prop: "employeeName",
                align: "left",
                headerAlign: "left",
                minWidth: 120,
                filterOrder: "multiSelect",
                width: getColumnWidth(setModule, "employeeName"),
            });
            if (parentid === 1010 && taxEnabled) {
                insertColumns.push({
                    label: "发票类型",
                    prop: "invoiceTypeName",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    filterOrder: "multiSelect",
                    width: getColumnWidth(setModule, "invoiceTypeName"),
                });
            }
        } else if ([1105, 1108].includes(billType) || [1050, 1060].includes(parentid)) {
            // 其他支出-销售费用 其他支出-代收代付 其他入库 其他出库（盘亏盘盈统计为其他收入库，属于其业务类型）
            const customerColumns: Array<IColumnProps> = [
                {
                    label: "客户",
                    prop: "customerName",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    filterOrder: "multiSelect",
                    width: getColumnWidth(setModule, "customerName"),
                },
            ];
            if ([1050, 1060].includes(parentid)) {
                customerColumns.unshift({
                    label: "业务类型",
                    prop: "businessTypeName",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    headerSort: true,
                    filterOrder: "multiSelect",
                    width: getColumnWidth(setModule, "businessTypeName"),
                });
                if (parentid === 1050) {
                    customerColumns.push({
                        label: "供应商",
                        prop: "vendorName",
                        align: "left",
                        headerAlign: "left",
                        minWidth: 120,
                        filterOrder: "multiSelect",
                        width: getColumnWidth(setModule, "vendorName"),
                    });
                }
            }
            insertColumns.unshift(...customerColumns);
        }
        const index = columns.findIndex((item) => item.label === "单据备注");
        index !== -1 && columns.splice(index, 0, ...insertColumns);
    }
    // 组装 拆卸
    if ([1110, 1120].includes(parentid)) {
        const name1 = parentid === 1110 ? "组装商品名称" : "拆卸商品名称";
        const name2 = parentid === 1110 ? "入库仓库" : "出库仓库";
        const name3 = parentid === 1110 ? "组装数量" : "拆卸数量";
        const insertColumns: Array<IColumnProps> = [
            {
                label: name1,
                prop: "assemblyName",
                align: "left",
                headerAlign: "left",
                minWidth: 150,
                filterOrder: "multiSelect",
                width: getColumnWidth(setModule, "assemblyName"),
            },
            {
                label: name2,
                prop: "whName",
                align: "left",
                headerAlign: "left",
                minWidth: 120,
                filterOrder: "multiSelect",
                width: getColumnWidth(setModule, "whName"),
            },
            {
                label: name3,
                prop: "quantity",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                filterOrder: "number",
                width: getColumnWidth(setModule, "quantity"),
            },
        ];
        const index = columns.findIndex((item) => item.label === "单据备注");
        index !== -1 && columns.splice(index, 0, ...insertColumns);
    }
}
function tryAppendColumnsAfterNote(parentid: number, columns: Array<IColumnProps>) {
    // 其他支出 核销单
    if ([1100, 1130].includes(parentid)) {
        const typeName = parentid === 1100 ? "费用类型" : "核销类型";
        const prop = parentid === 1100 ? "expenseTypeName" : "offsetTypeName";
        columns.push({
            label: typeName,
            prop,
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            headerSort: true,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, prop),
        });
    }
}
function getBillColumns(parentid: number, billType: number, taxEnabled: boolean, formatBillText: (billType: number) => string) {
    const columns: Array<IColumnProps> = [
        { slot: "selection", fixed: "left" },
        {
            label: "单据日期",
            prop: "billDateText",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "date",
            width: getColumnWidth(setModule, "billDateText"),
        },
        { slot: "billNo" },
        {
            label: "单据类型",
            prop: "billType",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "billType"),
            formatter: (_w, _c, val) => {
                return formatBillText(val);
            },
        },
        {
            label: "单据金额",
            prop: "totalAmount",
            align: "right",
            headerAlign: "right",
            minWidth: 120,
            filterOrder: "number",
            width: getColumnWidth(setModule, "totalAmount"),
            formatter: (row) => {
                return formatNumberStr(toDecimal3(row.totalAmount)) as string;
            },
        },
        { slot: "vtName" },
        { slot: "voucherInfo" },
        {
            label: "单据备注",
            prop: "note",
            align: "left",
            headerAlign: "left",
            minWidth: 195,
            filterOrder: "text",
            width: getColumnWidth(setModule, "note"),
        },
    ];
    tryInsertColumnsBeforeAmount(parentid, billType, columns);
    tryInsertColumnsBeforeNote(parentid, billType, columns, taxEnabled);
    // 只有其他支出单和核销单  不会涉及别的  所以暂时还是使用这种前三位补0的方式计算出来的parentid
    tryAppendColumnsAfterNote(parentid, columns);
    return columns;
}
function getTransferColumns(formatBillText: (billType: number) => string) {
    const columns: Array<IColumnProps> = [
        { slot: "selection", fixed: "left" },
        {
            label: "单据日期",
            prop: "billDateText",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "date",
            width: getColumnWidth(setModule, "billDateText"),
        },
        {
            label: "单据类型",
            prop: "billType",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "billType"),
            formatter: (_w, _c, val) => {
                return formatBillText(val);
            },
        },
        {
            label: "摘要",
            prop: "description",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "text",
            width: getColumnWidth(setModule, "description"),
        },
        {
            label: "转出账户",
            prop: "cdaccount",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            alias: "cdaccountOut",
            width: getColumnWidth(setModule, "cdaccountOut"),
        },
        {
            label: "转入账户",
            prop: "cdaccountIn",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "cdaccountIn"),
        },
        {
            label: "金额",
            prop: "totalAmount",
            align: "right",
            headerAlign: "right",
            minWidth: 120,
            filterOrder: "number",
            width: getColumnWidth(setModule, "totalAmount"),
            formatter: (row: any) => {
                return formatMoney(row.totalAmount);
            },
        },
        { slot: "vtName" },
        { slot: "voucherInfo" },
        {
            label: "备注",
            prop: "note",
            align: "left",
            headerAlign: "left",
            minWidth: 195,
            filterOrder: "text",
            width: getColumnWidth(setModule, "note"),
        },
    ];
    return columns;
}
function getInvoiceColumns(category: number, formatBillText: (billType: number) => string) {
    let nameTitle = "客户";
    let nameProp = "customerName";
    if (category === 10080) {
        nameTitle = "供应商";
        nameProp = "vendorName";
    }
    const columns: Array<IColumnProps> = [
        { slot: "selection", fixed: "left" },
        {
            label: "开票日期",
            prop: "billDateText",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "date",
            width: getColumnWidth(setModule, "billDateText"),
        },
        { slot: "billNo", label: "发票号码" },
        {
            label: "单据类型",
            prop: "billType",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "billType"),
            formatter: (_w, _c, val) => {
                return formatBillText(val);
            },
        },
        {
            label: nameTitle,
            prop: nameProp,
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, nameProp),
        },
        {
            label: "单据金额",
            prop: "totalAmount",
            align: "right",
            headerAlign: "right",
            minWidth: 120,
            filterOrder: "number",
            width: getColumnWidth(setModule, "totalAmount"),
            formatter: (row: any) => {
                return formatMoney(row.totalAmount);
            },
        },
        { slot: "vtName" },
        { slot: "voucherInfo" },
        {
            label: "项目",
            prop: "projectName",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "projectName"),
        },
        {
            label: "发票种类",
            prop: "invoiceTypeName",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            alias: "invoiceCategory",
            width: getColumnWidth(setModule, "invoiceCategory"),
        },
        {
            label: "单据备注",
            prop: "note",
            align: "left",
            headerAlign: "left",
            minWidth: 195,
            filterOrder: "text",
            width: getColumnWidth(setModule, "note"),
        },
    ];
    return columns;
}
function getCashierColumns(billType: string, billRootType: string, formatBillText: (billType: number) => string) {
    let ieAlias: undefined | string = undefined;
    let amountName = "收入/支出";
    if (["3011", "3021"].includes(billType)) {
        ieAlias = "ietypeName_in";
        amountName = "收入";
    } else if (["3012", "3022"].includes(billType)) {
        ieAlias = "ietypeName_out";
        amountName = "支出";
    }
    const columns: Array<IColumnProps> = [
        { slot: "selection", fixed: "left" },
        {
            label: "单据日期",
            prop: "billDateText",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "date",
            width: getColumnWidth(setModule, "billDateText"),
        },
        { slot: "billNo" },
        {
            label: "单据类型",
            prop: "billType",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "billType"),
            formatter: (_w, _c, val) => {
                return formatBillText(val);
            },
        },
        {
            label: "摘要",
            prop: "description",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "text",
            width: getColumnWidth(setModule, "description"),
        },
        {
            label: "往来单位",
            prop: "oppositeParty",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            headerSort: true,
            width: getColumnWidth(setModule, "oppositeParty"),
        },
        {
            label: amountName,
            prop: "totalAmount",
            align: "right",
            headerAlign: "right",
            minWidth: 120,
            filterOrder: "number",
            width: getColumnWidth(setModule, "totalAmount"),
            formatter: (row: any) => {
                return formatMoney(row.totalAmount);
            },
        },
        {
            label: "收支类别",
            prop: "ietypeName",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            alias: ieAlias,
            width: getColumnWidth(setModule, "ietypeName"),
        },
        { slot: "vtName" },
        { slot: "voucherInfo" },
        {
            label: billRootType === "3010" ? "现金账户" : "银行账户",
            prop: "cdaccount",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "cdaccount"),
        },
        {
            label: "项目",
            prop: "projectName",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "projectName"),
        },
        {
            label: "部门",
            prop: "departmentName",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "departmentName"),
        },
        {
            label: "备注",
            prop: "note",
            align: "left",
            headerAlign: "left",
            minWidth: 195,
            filterOrder: "text",
            width: getColumnWidth(setModule, "note"),
        },
    ];
    return columns;
}
function getDefaultColumns(billType: string, formatBillText: (billType: number) => string) {
    // 1000 全部单据 1010 采购管理 1020 销售管理 1030 应收应付 1040 资金管理 1140 库存管理
    const columns: Array<IColumnProps> = [
        { slot: "selection", fixed: "left" },
        {
            label: "单据日期",
            prop: "billDateText",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "date",
            width: getColumnWidth(setModule, "billDateText"),
        },
        { slot: "billNo" },
        {
            label: "单据类型",
            prop: "billType",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "billType"),
            formatter: (_w, _c, val) => {
                return formatBillText(val);
            },
        },
        {
            label: "单据金额",
            prop: "totalAmount",
            align: "right",
            headerAlign: "right",
            minWidth: 120,
            filterOrder: "number",
            width: getColumnWidth(setModule, "totalAmount"),
            formatter: (row) => {
                return formatNumberStr(toDecimal3(row.totalAmount)) as string;
            },
        },
        { slot: "vtName" },
        { slot: "voucherInfo" },
        {
            label: "单据备注",
            prop: "note",
            align: "left",
            headerAlign: "left",
            minWidth: 195,
            filterOrder: "text",
            width: getColumnWidth(setModule, "note"),
        },
    ];
    if (billType === "1010" || billType === "1020") {
        const label = billType === "1010" ? "供应商" : "客户";
        const prop = billType === "1010" ? "vendorName" : "customerName";
        const insertIndex = columns.findIndex((item) => item.label === "单据金额");
        columns.splice(insertIndex, 0, {
            label,
            prop,
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, prop),
        });
        const insertIndex2 = columns.findIndex((item) => item.label === "单据备注");
        columns.splice(insertIndex2, 0, {
            label: "项目",
            prop: "projectName",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "multiSelect",
            width: getColumnWidth(setModule, "projectName"),
        });
    } else if (billType === "1040") {
        const insertIndex = columns.findIndex((item) => item.label === "单据类型");
        columns.splice(insertIndex, 0, {
            label: "摘要",
            prop: "description",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            filterOrder: "text",
            width: getColumnWidth(setModule, "description"),
        });
    }
    return columns;
}
export function formatBillTypeText(billType: number, moduleTree: Array<ITreeData>) {
    let level = 0;
    let parentText = "";
    function search(data: Array<ITreeData>, id: string): string {
        for (let i = 0; i < data.length; i++) {
            if (data[i].id === id) return data[i].text;
            if (!data[i].children) continue;
            level++;
            parentText = data[i].text;
            const result = search(data[i].children || [], id);
            if (result) {
                return level === 2 ? parentText || "" : result;
            } else {
                level--;
            }
        }
        return "";
    }
    return search(moduleTree, billType.toString());
}
export function getErpEntryColumns(billType: string, billRootType: string, taxEnabled: boolean, moduleTree: Array<ITreeData>) {
    const isRoot = ["1000", "1010", "1020", "1030", "1040", "1140"].includes(billType);
    function format(billType: number) {
        return formatBillTypeText(billType, moduleTree);
    }
    let columns: Array<IColumnProps> = [];
    const typeIdKey = parseInt(~~billType / 1000 + "", 10);
    if (isRoot) {
        columns = getDefaultColumns(billType, format);
    } else if (typeIdKey === 1 || billRootType === "1050" || billRootType === "1060") {
        columns = getBillColumns(~~billRootType, ~~billType, taxEnabled, format);
    } else if (typeIdKey === 2) {
        columns = getInvoiceColumns(billType === "2010" ? 10070 : 10080, format);
    } else if (typeIdKey === 3) {
        columns = billType === "3030" ? getTransferColumns(format) : getCashierColumns(billType, billRootType, format);
    } else {
        columns = getDefaultColumns(billType, format);
    }
    return columns;
}

export function changeSelectedList(val: number[], combineInfo: IVoucherCombineInfo) {
    const allIndex = val.findIndex((z) => z === ERPVoucherSummaryWay.All);
    if (val.length === 1 && allIndex === 0) {
        combineInfo.selectZero = true;
        return;
    }
    if (!combineInfo.selectZero && val.length > 1 && allIndex !== -1) {
        combineInfo.selectZero = true;
        val.splice(0);
        val.push(ERPVoucherSummaryWay.All);
    } else if (allIndex !== -1) {
        val.splice(allIndex, 1);
        combineInfo.selectZero = false;
    }
}
export function checkCanSearch(params: IQueryParams) {
    const { startDate, endDate } = params;
    if (!startDate || !endDate) {
        ElNotify({ message: "单据日期不能为空", type: "warning" });
        return false;
    }
    if (startDate > endDate) {
        ElNotify({ message: "单据开始日期不能大于结束日期", type: "warning" });
        return false;
    }
    return true;
}

export const erpSeparatorOption = [
    { label: "无", value: "" },
    { label: "空格 ", value: " " },
    { label: "下划线_", value: "_" },
    { label: "短横线-", value: "-" },
    { label: "星号*", value: "*" },
    { label: "正斜杠/", value: "/" },
    { label: "反斜杠\\", value: "\\" },
    { label: "连接符&", value: "&" },
];
