@import "./Statements.less";
.main-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--white);
    .cash-flow-select {
        .detail-el-select(110px,30px);
        &.center {
            :deep(.el-select .el-input.el-input--suffix .el-input__inner) {
                padding-left: 14px;
            }
        }
    }
    .main-tool-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
.jqtransform {
    width: 110px;
    height: 30px;
    margin-right: 10px;
    :deep(.el-input__wrapper) {
        padding: 0 20px 0 7px;
        position: relative;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 28px;
        .el-input__suffix {
            position: absolute;
            right: 10px;
            top: 0px;
        }
    }
}

.option {
    padding: 6px 6px 6px 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 110px;
    height: 30px;
    font-weight: normal;
    color: var(--font-color);
    background-color: var(--white);
    &.selected {
        background-color: var(--main-color);
        color: var(--white);
        font-weight: normal;
    }
    &:hover {
        background-color: var(--main-color) !important;
        color: var(--white) !important;
    }
}
:deep(.main-center) {
    padding: 0;
    background-color: var(--white);
    tr {
        td {
            .cell {
                .el-table__indent {
                    display: none !important;
                }
                .el-table__placeholder {
                    display: none;
                }
                .el-table__expand-icon {
                    position: absolute;
                }
                .el-icon {
                    margin-top: 2px;
                }
                input.draft-input {
                    width: 240px;
                    height: 21px;
                    border: 1px solid var(--input-border-color);
                    outline: none;
                    padding: 0;
                    padding-left: 10px;
                    padding-right: 10px;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 21px;
                    box-sizing: border-box;
                    border-radius: var(--input-border-radius);
                    text-align: right;
                    &:focus {
                        border-color: var(--input-border-focus-color);
                        background-color: var(--input-border-outline-color);
                    }
                }
                .level2 {
                    padding-left: 33px;
                }
            }
        }
    }

    :deep(.el-table) {
        .el-loading-spinner {
            top: 12%;
        }
    }
}
.buttons {
    border-top: 1px solid var(--border-color);
    text-align: center;
    padding: 10px 0;
}
.calmethod .el-select-dropdown__list {
    &:hover {
        & > .selected {
            color: var(--font-color);
            background-color: var(--white);
        }
    }
}
