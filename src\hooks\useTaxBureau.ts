import ElementPlus from "element-plus"
import { dialogDrag } from "@/directives/dialogDrag"
import TaxBureauDialog from "@/components/TaxBureauDialog/index.vue"
import { IResponseModel, Jrequest } from "@/utils/service"
import { ElNotify } from "@/utils/notify"
import { useInitializeStore } from "@/store/modules/initialize"
import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
import { useBasicInfoStore } from "@/store/modules/basicInfo"
import { OpenApp } from "@/components/OpenAppDialog/utils/openApp"

export interface ILoginTaskValue {
  success: boolean
  result: string
  message: string
  info: string
  taskId: number
  reportId: number
}

export enum TaxBureauLoginType {
  Login = 1,
  Jump = 2,
}

export enum TaskType {
  TaxBureau = 103,
  FetchInvoice = 105,
}

export function useTaxBureau() {
  const isAppOpened = ref(false)

  //检查税局登录状态
  async function checkLoginState(taskSource: number, type: number = TaxBureauLoginType.Login, website: string = "", callBack?: Function) {
    const info = useBasicInfoStore().basicInfo
    const data = {
      taxPayerName: info.taxpayerName,
      taxPayerNumber: info.taxNumberS,
      taxType: info.taxType,
      taxArea: info.taxAreaId,
      taxLoginType: info.taxBureauLoginType,
      taxAccount: info.taxBureauAccount,
      personPositionType: info.taxBureauPersonPositionType,
      personIdentityNo: info.taxBureauPersonIdentityNo,
      personAccount: info.taxBureauPersonAccount,
      personPassword: info.taxBureauPersonPassword,
      personPhone: info.taxBureauPersonPhone,
      agentTaxNumber: info.taxBureauAgentTaxNumber,
      startDate: taskSource === TaskType.FetchInvoice ? useTaxPeriodStore().searchStartDate : "",
      endDate: taskSource === TaskType.FetchInvoice ? useTaxPeriodStore().searchEndDate : "",
    }

    function init() {
      if (!info.initCompleted) {
        useInitializeStore().initialize({
          periodId: useTaxPeriodStore().currentPeriodId,
        })
      }
    }

    try {
      const response = await Jrequest({
        url: `api/TaxBureau/LoginState?isAuthorized=${info.taxBureauAuthorized}&taskSource=${taskSource}`,
        method: "post",
        data,
      })

      const url = checkJumpLink(info.nationalTaxBureauUrl, website)

      const success = type === TaxBureauLoginType.Login ? init : () => openUriWithTimeoutHack(info, url)

      if (response.data.result === "firstLogin") {
        openTaxBureau(taskSource, 1, success)
      } else if (response.data.result === "success") {
        success()
      } else if (response.data.result === "taskStart") {
        callBack && callBack()
      } else if (
        ["needUpdate", "notSureAndInfo", "loginFail"].includes(response.data.result) ||
        (!info.taxBureauAuthorized && response.data.result === "error")
      ) {
        openTaxBureau(taskSource, 1, success)
      } else if (info.taxBureauAuthorized) {
        openTaxBureau(taskSource, 3, success)
      } else {
        ElNotify({ type: "warning", message: response.msg || "请求失败" })
      }
    } catch (error) {
      ElNotify({ type: "warning", message: "请求失败" })
    }
  }

  function openUriWithTimeoutHack(info: any, website: string) {
    Jrequest({
      url: `/api/TaxBureau/SetUserIdentifier?taxArea=${info.taxAreaId}&personAccount=${info.taxBureauPersonAccount}&taxNumber=${info.taxNumberS}`,
      method: "post",
    }).then((res: any) => {
      if (res.data.code !== 200) {
        ElNotify({ type: "warning", message: res.data.message || "请求失败" })
        return
      }

      const openApp = new Promise((resolve, reject) => {
        const protocol = `nmyswzs://?USCC=${info.taxNumberS}&AreaCode=${info.rpaAreaId}&Identifier=${res.data.message}`

        const encodedUrl = encodeURIComponent(website)
        const newProtocol = protocol + "&url=" + encodedUrl

        const ipt = document.createElement("input")
        ipt.style.position = "absolute"
        ipt.style.clip = "rect(0,0,0,0)"
        ipt.style.top = "0"
        ipt.style.left = "0"
        function blur() {
          clearTimeout(timeout)
          window.onblur = null
          isAppOpened.value = true
          resolve(true)
        }
        function focus() {
          isAppOpened.value = false
          window.onfocus = null
        }
        ipt.addEventListener("blur", blur)
        ipt.addEventListener("focus", focus)
        document.body.appendChild(ipt)
        ipt.focus()
        const start = Date.now()

        const timeout = setTimeout(() => {
          if (Date.now() - start < 2500) {
            return
          }
          ipt.removeEventListener("blur", blur)
          ipt.removeEventListener("focus", focus)
          // 如果应用没有在一定时间内被唤起，就认为唤起失败，调用 reject 函数
          reject()
        }, 2500)
        window.location.href = newProtocol
      })

      async function tryOpenApp() {
        try {
          await openApp
        } catch (e) {
          if (isAppOpened.value) return

          await OpenApp().then((r) => {
            if (r) {
              window.location.href =
                window.downLoadUrl +
                (window.downLoadUrl.includes("test") ? "AutoUpdateDownload/" : "taxclient/installer/") +
                encodeURIComponent("柠檬云税务助手安装程序.zip")
            }
          })
        }
      }

      tryOpenApp()
    })
  }

  function openTaxBureau(taskSource: number, step: number = 1, successLogin: Function): Promise<boolean> {
    return new Promise<boolean>(() => {
      const content = document.querySelector(".router-container .content")
      if (!content) return Promise.resolve(false)

      const props = {
        taskSource,
        step,
        successLogin,
      }

      const capp = createApp(TaxBureauDialog, props)
      const container = document.createElement("div")
      capp.use(ElementPlus)
      capp.directive("dialogDrag", dialogDrag)
      capp.mount(container)
      content.insertBefore(container, content.firstChild)
    })
  }

  // 跳转税局域名不一致的路径
  const TaxBureauDomainMap: { [key: string]: any } = {
    "#/userCenter/baseInfoEE?client_id=": "tpass",
    "kpfw/spHandler?cdlj=blue-invoice-makeout": "dppt",
    "credit/application": "dppt",
    "hdjl/spHandler?cdlj=/": "znhd",
  }
  // 判断跳转税局的链接是否要修改域名
  function checkJumpLink(baseLink: string, path: string): string {
    if (TaxBureauDomainMap[path]) {
      baseLink = baseLink.replace("etax", TaxBureauDomainMap[path])
    }
    return baseLink + path
  }

  return {
    isAppOpened,
    checkLoginState,
    openTaxBureau,
    openUriWithTimeoutHack,
  }
}
