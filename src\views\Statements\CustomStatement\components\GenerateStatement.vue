<template>
    <div class="edit-content statement-table-content">
        <div class="title" id="statementTableTitle">BB002-自定义报表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between" v-if="statementModel.pId === 0" style="box-sizing: border-box">
                <div class="main-tool-left">
                    <div class="form-title">编码：</div>
                    <div class="form-field">
                        <el-input v-model="statementModel.statementCode" @input="handleCodeInput" @focus="cancelTableCellSelect" />
                    </div>
                    <div class="form-title ml-10">名称：</div>
                    <div class="form-field">
                        <el-input v-model="statementModel.statementName" @input="handleNameInput" @focus="cancelTableCellSelect" />
                    </div>
                    <div class="form-title ml-10">备注：</div>
                    <div class="form-field">
                        <el-input v-model="statementModel.note" @focus="cancelTableCellSelect" />
                    </div>
                </div>
                <div class="main-tool-right">
                    <a class="solid-button button" @click="saveStatementModel()">保存</a>
                    <a class="button ml-10" @click="cancelStatementModel()">取消</a>
                </div>
            </div>
            <div class="main-top main-tool-bar" id="statementTableToolbar" v-else>
                <div class="toolbar-left" style="display: flex">
                    <a v-permission="['customstatement-canedit']" class="solid-button button mr-10" @click="saveStatementModelPId()"
                        >保存</a
                    >
                    <a v-permission="['customstatement-canedit']" class="button mr-10" @click="refreshStatementModel()">重新计算</a>

                    <a v-permission="['customstatement-canexport']" class="button mr-10" @click="exportSingle()">导出</a>
                    <a class="button mr-10" @click="cancelStatementModel()">取消</a>
                    <PaginationPeriodPicker
                        class="pagination-period-picker"
                        v-model="statementModel.pId"
                        @selectedPidChange="reloadStatementModel(statementModel.statementId, statementModel.pId, false)"
                        ref="periodRef"
                    ></PaginationPeriodPicker>
                </div>
                <div class="toobar-right"></div>
            </div>
            <div class="statement-table-container">
                <div class="statement-table" id="statementTable">
                    <div class="table-btnsbar" v-if="statementModel.pId === 0">
                        <div class="selector-content">
                            <el-select
                                class="set-font-family select2-hidden-accessible"
                                style="width: 86px"
                                tabindex="-1"
                                v-model="fontType"
                                @change="setFontFamily"
                            >
                                <el-option v-for="item in fontFamilyArr" :key="item" :label="item" :value="item" />
                            </el-select>
                        </div>
                        <div class="selector-content ml-20">
                            <el-select
                                class="set-font-size select2-hidden-accessible"
                                v-model="fontSize"
                                style="width: 52px"
                                tabindex="-1"
                                aria-hidden="true"
                                @change="setFontSize"
                            >
                                <el-option
                                    v-for="item in [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 42, 48, 72]"
                                    :key="item"
                                    :label="item"
                                    :value="item"
                                />
                            </el-select>
                        </div>
                        <div
                            :class="['btn icon-btn set-vertical-align', { hover: verticalAlignHover === verticalAlignEnum.top }]"
                            :data-align-type="verticalAlignEnum.top"
                            @click="setCellAlign('verticalAlign', verticalAlignEnum.top)"
                            title="顶端对齐"
                        ></div>
                        <div
                            :class="['btn icon-btn set-vertical-align', { hover: verticalAlignHover === verticalAlignEnum.center }]"
                            :data-align-type="verticalAlignEnum.center"
                            @click="setCellAlign('verticalAlign', verticalAlignEnum.center)"
                            title="垂直居中"
                        ></div>
                        <div
                            :class="['btn icon-btn set-vertical-align', { hover: verticalAlignHover === verticalAlignEnum.bottom }]"
                            :data-align-type="verticalAlignEnum.bottom"
                            @click="setCellAlign('verticalAlign', verticalAlignEnum.bottom)"
                            title="底端对齐"
                        ></div>
                        <div
                            :class="['btn icon-btn set-horizontal-align', { hover: horizontalAlignHover === horizontalAlignEnum.left }]"
                            :data-align-type="horizontalAlignEnum.left"
                            @click="setCellAlign('horizontalAlign', horizontalAlignEnum.left)"
                            title="左对齐"
                        ></div>
                        <div
                            :class="['btn icon-btn set-horizontal-align', { hover: horizontalAlignHover === horizontalAlignEnum.center }]"
                            :data-align-type="horizontalAlignEnum.center"
                            @click="setCellAlign('horizontalAlign', horizontalAlignEnum.center)"
                            title="水平居中"
                        ></div>
                        <div
                            :class="['btn icon-btn set-horizontal-align', { hover: horizontalAlignHover === horizontalAlignEnum.right }]"
                            :data-align-type="horizontalAlignEnum.right"
                            @click="setCellAlign('horizontalAlign', horizontalAlignEnum.right)"
                            title="右对齐"
                        ></div>
                        <div class="btn cells-span" @click="handleCellsSpan">合并单元格</div>
                        <div class="btn down-icon">
                            <div class="more">
                                <div class="buttons">
                                    <div class="cells-pre-span" @click="handleCellsPreSpan">拆分单元格</div>
                                </div>
                            </div>
                        </div>
                        <div class="btn insert-row" @click="handleInsertRow">插入行</div>
                        <div class="btn down-icon">
                            <div class="more">
                                <div class="buttons">
                                    <div class="delete-row" @click="handleDeleteRow">删除行</div>
                                </div>
                            </div>
                        </div>
                        <div class="btn insert-column" @click="handleInsertCol">插入列</div>
                        <div class="btn down-icon">
                            <div class="more">
                                <div class="buttons">
                                    <div class="delete-column" @click="handleDeleteCol">删除列</div>
                                </div>
                            </div>
                        </div>
                        <div class="btn set-equation" @click="handleSetEquation">公式设置</div>
                        <div class="btn create-cell-from-module" @click="fillCellsDialog = true">批量填充</div>
                    </div>
                    <div class="table-toolbar">
                        <el-input type="text" class="selected-cell" v-model="selectedStartCell" readonly />
                        <div class="fx-icon">fx</div>
                        <el-input type="text" class="selected-cell-content" v-model="cellContent" readonly style="flex: 1" />
                    </div>
                    <div class="table-container" ref="tableContainerRef" @keydown="handleKeyDown">
                        <div class="table-all-btn" ref="tableAllBtnRef" @click="handleTableAllBtnClick"></div>
                        <div class="table-top-title" @mouseleave="isColumnSelecting = false">
                            <div class="t-t-line"></div>
                            <template v-for="item in statementModel.columnNumber" :key="item">
                                <div
                                    :class="['t-t', 'c-' + item]"
                                    :style="statementModel.columnWidths[item] ? 'width: ' + statementModel.columnWidths[item] + 'px;' : ''"
                                    :data-column-number="item"
                                    @mousedown="handleColMouseDown($event, convertToColumnName(item))"
                                    @mouseenter="handleColMouseEnter($event, convertToColumnName(item))"
                                    @mouseup="isColumnSelecting = false"
                                >
                                    {{ convertToColumnName(item) }}
                                </div>
                                <div
                                    :class="['t-t-line', { draggable: statementModel.pId === 0 }]"
                                    v-draggable-c="statementModel.pId === 0"
                                ></div>
                            </template>
                        </div>
                        <div class="table-left-title" @mouseleave="isRowSelecting = false">
                            <div class="l-t-line"></div>
                            <span v-for="item in statementModel.rowNumber" :key="item">
                                <div
                                    :class="['l-t', 'r-' + item]"
                                    :data-row-number="item"
                                    :style="statementModel.rowHeights[item] ? 'height: ' + statementModel.rowHeights[item] + 'px;' : ''"
                                    @mousedown="handleRowMousedown($event, item)"
                                    @mouseenter="handleRowMouseEnter($event, item)"
                                    @mouseup="isRowSelecting = false"
                                >
                                    {{ item }}
                                </div>
                                <div
                                    :class="['l-t-line', { draggable: statementModel.pId === 0 }]"
                                    v-draggable-l="statementModel.pId === 0"
                                ></div>
                            </span>
                        </div>
                        <div class="assit-row-line" ref="assitRowLineRef"></div>
                        <div class="assit-col-line" ref="assitColLineRef"></div>
                        <div class="table-content" @mouseleave="isSelecting = false">
                            <div v-for="row in statementModel.rowNumber" :key="row" :class="['r-l', 'r-' + row]">
                                <div
                                    v-for="col in statementModel.columnNumber"
                                    :key="col"
                                    :class="[
                                        'c-c',
                                        'c-' + col,
                                        cellClassStyle[convertToColumnName(col) + row]
                                            ? cellClassStyle[convertToColumnName(col) + row].moreClass
                                            : '',
                                    ]"
                                    :style="
                                        cellClassStyle[convertToColumnName(col) + row]
                                            ? cellClassStyle[convertToColumnName(col) + row].moreStyle
                                            : ''
                                    "
                                    :data-cell-name="convertToColumnName(col) + row"
                                    :data-row-number="row"
                                    :data-column-number="col"
                                    @mousedown="handleCellMouseDown($event, convertToColumnName(col) + row, row, col)"
                                    @mousemove="handleCellMouseMove($event)"
                                    @mouseenter="handleCellMouseEnter($event, convertToColumnName(col) + row, row, col)"
                                    @mouseup="isSelecting = false"
                                    @dblclick="
                                        statementModel.pId === 0
                                            ? handleCellDbClick($event, row, col, buildTdContent(convertToColumnName(col) + row))
                                            : () => {}
                                    "
                                >
                                    <div class="cell-content">
                                        {{ buildTdContent(convertToColumnName(col) + row) }}
                                    </div>
                                    <textarea
                                        ref="currentTextareaRef"
                                        v-model="currentTextareaValue"
                                        @blur="handleTextareaBlur($event, convertToColumnName(col) + row)"
                                    >
                                    </textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <BatchFillingDialog v-model="fillCellsDialog" :selectedStartCell="selectedStartCell"></BatchFillingDialog>
        <EditEquationDialog
            v-model="editEquationDialog"
            ref="editEquationRef"
            :selectedStartCell="selectedStartCell"
            :statementModelPid="statementModel.pId"
            :customStatementList="customStatementList"
            :assistentries="assistentries"
            @editEquationConfirm="editEquationConfirm"
        ></EditEquationDialog>
    </div>
</template>

<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { ref, toRef, reactive, watch, onMounted, onUnmounted, nextTick } from "vue";
import $bus from "@/bus";
import { ElNotify } from "@/util/notify";
import BatchFillingDialog from "./BatchFillingDialog.vue";
import EditEquationDialog from "./EditEquationDialog.vue";
import { convertToColumnName, splitCellName } from "../utils";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useLoading } from "@/hooks/useLoading";
import { columnDateTypeEnum, columnDataTypeEnum, equationOperatorEnum, equationValueTypeEnum } from "../utils";
import { ElConfirm } from "@/util/confirm";
import { getLocalStorage } from "@/util/localStorageOperate";
import { globalExport } from "@/util/url";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import type {
    IStatementModel,
    IStatementModelCellValue,
    ICustomStatementListItem,
    IStatementLines,
    IStatementLinesItems,
    ICustomStatementDetail,
    IStatementEquationModel,
    IAssistEntries,
} from "../types";
const asubEquationDateType = columnDateTypeEnum;
const asubEquationDataType = columnDataTypeEnum;
const emits = defineEmits(["submitSuccess", "goMain"]);
let fillCellsDialog = ref(false);
let editEquationDialog = ref(false);
let fontType = ref("微软雅黑");
let fontSize = ref(14);
const _ = getGlobalLodash()
const contentTypeEnum = {
    text: 1,
    equation: 2,
};
const verticalAlignEnum = {
    top: 1,
    center: 2,
    bottom: 3,
};
const horizontalAlignEnum = {
    left: 1,
    center: 2,
    right: 3,
};
// 整个表单内容
let statementModel = reactive<IStatementModel>({
    statementId: 0,
    statementCode: "",
    statementName: "",
    rowNumber: 20,
    columnNumber: 16,
    rowHeights: {},
    columnWidths: {},
    pId: 0,
    note: "",
    createdBy: "",
    createdDate: "",
    modifiedBy: "",
    modifiedDate: "",
    cells: reactive({
        // A1:reactive( {
        //     cellContent: "科目编码",
        //     cellId: 0,
        //     columnSpan: 1,
        //     contentType: 1,
        //     equations: null,
        //     evaluated: true,
        //     evaluating: false,
        //     fontFamily: "宋体",
        //     fontSize: 12,
        //     horizontalAlign: 0,
        //     rowSpan: 1,
        //     verticalAlign: 0,
        // }),
    }),
});
let cellClassStyle = reactive<{ [key: string]: { moreClass: string; moreStyle: { [key: string]: string } } }>({});
const setStatementModel = (params: any) => {
    statementModel.statementId = params.statementId || 0;
    statementModel.statementCode = params.statementCode || "";
    statementModel.statementName = params.statementName || "";
    statementModel.rowNumber = params.rowNumber || 20;
    statementModel.columnNumber = params.columnNumber || 16;
    statementModel.rowHeights = params.rowHeights || {};
    statementModel.columnWidths = params.columnWidths || {};
    statementModel.pId = params.pid || 0;
    statementModel.note = params.note || "";
    statementModel.createdBy = params.createdBy || "";
    statementModel.createdDate = params.createdDate || "";
    statementModel.modifiedBy = params.modifiedBy || "";
    statementModel.modifiedDate = params.modifiedDate || "";
    if (!params.statementCode) {
        getStatementCode();
    }
    if (params.cells) {
        for (let cellName in params.cells) {
            statementModel.cells[cellName] = setStatementCellModel(params.cells[cellName]);
        }
        autoAddNumber();
    }
    for (let row = 1; row <= statementModel.rowNumber; row++) {
        for (let column = 1; column <= statementModel.columnNumber; column++) {
            let columnName = convertToColumnName(column);
            let rowName = row.toString();
            let cellName = columnName + rowName;
            cellClassStyle[cellName] = reactive({
                moreStyle: { width: "", height: "", fontSize: "", fontFamily: "", marginRight: "", marginBottom: "" },
                moreClass: "",
            });
        }
    }
    refreshTable();
};
setStatementModel({});

function setStatementCellModel(params): IStatementModelCellValue {
    const self: IStatementModelCellValue = reactive({
        cellId: params.cellId || 0,
        cellContent: params.cellContent || "",
        contentType: params.contentType || contentTypeEnum.text,
        rowSpan: params.rowSpan || 1,
        columnSpan: params.columnSpan || 1,
        equations: params.equations || [],
        verticalAlign: params.verticalAlign || verticalAlignEnum.top,
        horizontalAlign: params.horizontalAlign || horizontalAlignEnum.left,
        fontSize: params.fontSize || 14,
        fontFamily: params.fontFamily || "微软雅黑",
        evaluated: params.evaluated || true,
        evaluating: params.evaluating || false,
    });

    return self;
}

function getStatementCode() {
    if (!statementModel.statementCode) {
        request({
            url: "/api/CustomStatement/GetNextStatementCode",
            method: "post",
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                statementModel.statementCode = res.data;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg || "获取编码失败",
                });
            }
        });
    }
}

function getCell(cellName: string) {
    if (!statementModel.cells[cellName]) {
        statementModel.cells[cellName] = setStatementCellModel({});
    }
    return statementModel.cells[cellName];
}
function saveCellChange(cellName: string) {
    return function (key: string, value: any) {
        if (!statementModel.cells[cellName]) {
            statementModel.cells[cellName] = setStatementCellModel({});
        }
        (statementModel.cells[cellName] as any)[key] = value;
    };
}
const editEquationRef = ref();
function handleSetEquation() {
    editEquationDialog.value = true;
    editEquationRef.value?.initTableData(statementModel.cells[selectedStartCell.value]?.equations || []);
}
function editEquationConfirm(editTableData: IStatementEquationModel[]) {
    if (editTableData.length) {
        saveCellChange(selectedStartCell.value)("equations", [].concat(editTableData));
        saveCellChange(selectedStartCell.value)("contentType", contentTypeEnum.equation);
    } else {
        saveCellChange(selectedStartCell.value)("equations", []);
        saveCellChange(selectedStartCell.value)("contentType", contentTypeEnum.text);
    }
    saveCellChange(selectedStartCell.value)("cellContent", "");
    resetCellEuationState(selectedStartCell.value);
    reCalcCells();
}
function resetCellEuationState(editCellName: string) {
    for (let cellName in statementModel.cells) {
        let cell = statementModel.cells[cellName];
        if (cell.contentType === contentTypeEnum.equation) {
            cell.equations.forEach(function (e) {
                if (e.valueType === equationValueTypeEnum.cell && e.source === statementModel.statementId + "_" + editCellName) {
                    e.evaluated = false;
                }
            });
        }
    }
}
function reCalcCells(callback?: Function, failCallback?: Function) {
    refreshTable();
    removeSelectedClass();
    addSelectedClass();
    failCallback && failCallback();
}
function appendCells(cells: { [key: string]: IStatementModelCellValue }) {
    for (let cellName in cells) {
        statementModel.cells[cellName] = setStatementCellModel(cells[cellName]);
    }
    autoAddNumber();
}
function autoAddNumber() {
    for (let cellName in statementModel.cells) {
        const rows = /[^\d]+(\d+)$/.exec(cellName);
        const cols = /([^\d]+)\d+$/.exec(cellName);
        statementModel.rowNumber = Math.max(statementModel.rowNumber, !rows ? 0 : ~~rows[1]);
        statementModel.columnNumber = Math.max(statementModel.columnNumber, !cols ? 0 : convertToColumnNumber(cols[1]));
    }
}
function convertToColumnNumber(columnName: string) {
    let r = 0;
    for (let i = 0; i < columnName.length; i++) {
        r += (columnName.charCodeAt(i) - 64) * Math.pow(26, columnName.length - i - 1);
    }
    return r;
}

function buildTdContent(cellName: string) {
    return statementModel.cells[cellName]
        ? statementModel.cells[cellName].contentType === contentTypeEnum.text
            ? statementModel.cells[cellName].cellContent
            : Number(statementModel.cells[cellName].cellContent).toFixed(2)
        : "";
}

let isSelecting = ref(false);
let isRowSelecting = ref(false);
let isColumnSelecting = ref(false);
let selectedStartCell = ref("");
let selectedEndCell = ref("");
const fontFamilyArr = ref(["微软雅黑", "宋体", "黑体", "仿宋", "楷体"]);
function refreshStatementModel() {
    reloadStatementModel(statementModel.statementId, statementModel.pId, false);
}
function reloadStatementModel(statementId: number, pId: number, editable: boolean) {
    useLoading().enterLoading("正在加载数据...");
    request({
        url: editable
            ? `/api/CustomStatement?statementId=${statementId}`
            : `/api/CustomStatement/WithData?statementId=${statementId}&pId=${pId}&classification=${
                  getLocalStorage("classificationSwitch") || false
              }`,
        method: "get",
    }).then((res: IResponseModel<ICustomStatementDetail>) => {
        useLoading().quitLoading();
        if (res.state === 1000) {
            setStatementModel(res.data);
        }
    });
}
function exportSingle() {
    globalExport(
        `/api/CustomStatement/Export?statementId=${statementModel.statementId}&pId=${statementModel.pId}&classification=${
            getLocalStorage("classificationSwitch") || false
        }`
    );
}

function removeSelectedClass() {
    let tableTopTitles = document.querySelectorAll("#statementTable .table-top-title .t-t");
    let tableLeftTitles = document.querySelectorAll("#statementTable .table-left-title .l-t");
    let ccs = document.querySelectorAll("#statementTable .c-c");
    tableTopTitles.forEach(function (element) {
        element.classList.remove("selected");
    });
    tableLeftTitles.forEach(function (element) {
        element.classList.remove("selected");
    });
    ccs.forEach(function (element) {
        nextTick(() => {
            element.classList.remove("border-top-hover");
            element.classList.remove("border-bottom-hover");
            element.classList.remove("border-left-hover");
            element.classList.remove("border-right-hover");
            element.classList.remove("selected");
            element.classList.remove("background-hover");
        });
    });
}
const accountSubjectStore = useAccountSubjectStore();
const accountSubjects = toRef(accountSubjectStore, "accountSubjectList");
const assistingAccountingStore = useAssistingAccountingStore();
const assistingAccountingTypeList = toRef(assistingAccountingStore, "assistingAccountingTypeList");
const assistingAccountingListAll = toRef(assistingAccountingStore, "assistingAccountingListAll");

let assistentries = ref<{ [key: number]: IAssistEntries }>({});
watch(
    [assistingAccountingTypeList, assistingAccountingListAll],
    () => {
        for (let i = 0; i < assistingAccountingTypeList.value.length; i++) {
            assistentries.value[assistingAccountingTypeList.value[i].aaType] = {
                code: assistingAccountingTypeList.value[i].rowNum,
                name: assistingAccountingTypeList.value[i].aaTypeName,
                list: assistingAccountingStore.getAssistingAccountingByTypeModel(assistingAccountingTypeList.value[i].aaType, false),
            };
        }
    },
    { deep: true, immediate: true }
);
let customStatementList = ref();
const getCustomStatementList = () => {
    request({
        url: "/api/CustomStatement/List",
        method: "get",
    }).then((res: any) => {
        if (res.state === 1000) {
            customStatementList.value = res.data;
            if (statementModel.statementId === 0) {
                customStatementList.value.unshift({
                    statementId: 0,
                    statementCode: statementModel.statementCode,
                    statementName: statementModel.statementName,
                    columnNumber: statementModel.columnNumber,
                    rowNumber: statementModel.rowNumber,
                });
            }
        }
    });
};
getCustomStatementList();
$bus.on("reloadCustomStatementList", () => {
    _.debounce(() => {
        getCustomStatementList();
    }, 500);
});
let statementLines = ref();
const getCustomStatementLines = () => {
    request({
        url: "/api/CustomStatement/GetCustomStatementLines",
        method: "post",
    }).then((res: IResponseModel<IStatementLines>) => {
        if (res.state === 1000) {
            statementLines.value = res.data;
        }
    });
};
getCustomStatementLines();
const assitColLineRef = ref();
const assitRowLineRef = ref();
const tableContainerRef = ref();
const tableAllBtnRef = ref();
const vDraggableC = {
    mounted: (el: any, binding: any) => {
        {
            el.addEventListener("mousedown", (e: MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                document.addEventListener("mousemove", handleMouseMove);
                document.addEventListener("mouseup", handleMouseUp);
            });

            function handleMouseMove(e: MouseEvent) {
                e.preventDefault();
                const left = el.offsetLeft + e.clientX - el.getBoundingClientRect().left + tableAllBtnRef.value.offsetWidth + "px";
                assitColLineRef.value.style.left = left;
                assitColLineRef.value.style.display = "block";
            }

            function handleMouseUp(e: MouseEvent) {
                e.preventDefault();
                document.removeEventListener("mousemove", handleMouseMove);
                document.removeEventListener("mouseup", handleMouseUp);
                let offset = e.clientX - el.offsetLeft - tableAllBtnRef.value.offsetWidth;
                const columnNumber = Number(el.previousElementSibling.dataset.columnNumber);
                if (selectedStartCell.value) {
                    let startR = getRowNumberData(selectedStartCell.value);
                    let endR = getRowNumberData(selectedEndCell.value);
                    let startC = getColNumberData(selectedStartCell.value);
                    let endC = getColNumberData(selectedEndCell.value);
                    let minC = Math.min(startC, endC);
                    let maxC = Math.max(startC, endC);
                    if (endR - startR + 1 === statementModel.rowNumber && columnNumber >= minC && columnNumber <= maxC) {
                        for (let c = minC; c <= maxC; c++) {
                            statementModel.columnWidths[c] = Math.max(parseInt(el.previousElementSibling.offsetWidth + offset), 62);
                        }
                    } else {
                        statementModel.columnWidths[columnNumber] = Math.max(parseInt(el.previousElementSibling.offsetWidth + offset), 62);
                    }
                } else {
                    statementModel.columnWidths[columnNumber] = Math.max(parseInt(el.previousElementSibling.offsetWidth + offset), 62);
                }
                assitColLineRef.value.style.display = "none";
                removeSelectedClass();
                addSelectedClass();
                refreshTable();
            }
        }
    },
};
const vDraggableL = {
    mounted: (el: any, binding: any) => {
        {
            el.addEventListener("mousedown", (e: MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                document.addEventListener("mousemove", handleMouseMove);
                document.addEventListener("mouseup", handleMouseUp);
            });

            function handleMouseMove(e: MouseEvent) {
                e.preventDefault();
                const top = el.offsetTop + e.clientY - el.getBoundingClientRect().top + tableAllBtnRef.value.offsetHeight + "px";
                assitRowLineRef.value.style.top = top;
                assitRowLineRef.value.style.display = "block";
            }

            function handleMouseUp(e: MouseEvent) {
                e.preventDefault();
                document.removeEventListener("mousemove", handleMouseMove);
                document.removeEventListener("mouseup", handleMouseUp);
                let offset = e.clientY - el.getBoundingClientRect().top;
                const rowNumber = Number(el.previousElementSibling.dataset.rowNumber);
                if (selectedStartCell.value) {
                    let startC = getColNumberData(selectedStartCell.value);
                    let endC = getColNumberData(selectedEndCell.value);
                    let startR = getRowNumberData(selectedStartCell.value);
                    let endR = getRowNumberData(selectedEndCell.value);
                    let minR = Math.min(startR, endR);
                    let maxR = Math.max(startR, endR);
                    if (endC - startC + 1 === statementModel.columnNumber && rowNumber >= minR && rowNumber <= maxR) {
                        for (let r = minR; r <= maxR; r++) {
                            statementModel.rowHeights[r] = Math.max(parseInt(el.previousElementSibling.offsetHeight + offset), 22);
                        }
                    } else {
                        statementModel.rowHeights[rowNumber] = Math.max(parseInt(el.previousElementSibling.offsetHeight + offset), 22);
                    }
                } else {
                    statementModel.rowHeights[rowNumber] = Math.max(parseInt(el.previousElementSibling.offsetHeight + offset), 22);
                }
                statementModel.rowHeights[rowNumber] = Math.max(parseInt(el.previousElementSibling.offsetHeight + offset), 22);
                assitRowLineRef.value.style.display = "none";

                refreshTable();
                removeSelectedClass();
                addSelectedClass();
            }
        }
    },
};

let cellContent = ref("");
let selectedCell = ref<string[]>([]);
let verticalAlignHover = ref();
let horizontalAlignHover = ref();
function addSelectedClass() {
    if (!selectedStartCell.value) return;
    cellContent.value = "";
    selectedCell.value = [];
    let startR = getRowNumberData(selectedStartCell.value);
    let startC = getColNumberData(selectedStartCell.value);
    let endR = getRowNumberData(selectedEndCell.value);
    let endC = getColNumberData(selectedEndCell.value);
    let startSpanR =
        startR + (statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].rowSpan : 1) - 1;
    let startSpanC =
        startC + (statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].columnSpan : 1) - 1;
    let endSpanR = endR + (statementModel.cells[selectedEndCell.value] ? statementModel.cells[selectedEndCell.value].rowSpan : 1) - 1;
    let endSpanC = endC + (statementModel.cells[selectedEndCell.value] ? statementModel.cells[selectedEndCell.value].columnSpan : 1) - 1;
    let minR = Math.min(endR, endSpanR, startR, startSpanR);
    let maxR = Math.max(endR, endSpanR, startR, startSpanR);
    let minC = Math.min(endC, endSpanC, startC, startSpanC);
    let maxC = Math.max(endC, endSpanC, startC, startSpanC);
    for (let i = minR; i <= maxR; i++) {
        for (let j = minC; j <= maxC; j++) {
            let cell = document.querySelector("#statementTable .r-l.r-" + i + " .c-c.c-" + j) as HTMLElement;
            let cellName = convertToColumnName(j) + i;
            let cellModel = statementModel.cells[cellName];
            let rowSpan = cellModel ? cellModel.rowSpan : 1;
            let columnSpan = cellModel ? cellModel.columnSpan : 1;
            if (i === minR) {
                (document.querySelector("#statementTable .t-t.c-" + j) as HTMLElement).classList.add("selected");
            }
            if (cell.classList.contains("spaned")) continue;
            if (i === minR) {
                nextTick(() => {
                    cell.classList.add("border-top-hover");
                });
            }
            if (i === maxR || i + rowSpan - 1 === maxR) {
                nextTick(() => {
                    cell.classList.add("border-bottom-hover");
                });
            }
            if (j === minC) {
                nextTick(() => {
                    cell.classList.add("border-left-hover");
                });
            }
            if (j === maxC || j + columnSpan - 1 === maxC) {
                nextTick(() => {
                    cell.classList.add("border-right-hover");
                });
            }
            if (!(i === startR && j === startC)) {
                nextTick(() => {
                    cell.classList.add("background-hover");
                });
            }
            cell.classList.add("selected");
            selectedCell.value.push(cellName);
        }
        (document.querySelector("#statementTable .l-t.r-" + i) as HTMLElement).classList.add("selected");
    }
    fontSize.value = statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].fontSize || 14 : 14;
    fontType.value = statementModel.cells[selectedStartCell.value]
        ? statementModel.cells[selectedStartCell.value].fontFamily || "微软雅黑"
        : "微软雅黑";

    verticalAlignHover.value = selectedStartCell.value
        ? statementModel.cells[selectedStartCell.value]
            ? statementModel.cells[selectedStartCell.value].verticalAlign
            : verticalAlignEnum.top
        : 0;
    horizontalAlignHover.value = selectedStartCell.value
        ? statementModel.cells[selectedStartCell.value]
            ? statementModel.cells[selectedStartCell.value].horizontalAlign
            : horizontalAlignEnum.left
        : 0;

    // 计算cellContent
    if (statementModel.cells[selectedStartCell.value]) {
        if (statementModel.cells[selectedStartCell.value].contentType === contentTypeEnum.equation) {
            statementModel.cells[selectedStartCell.value].equations.forEach(function (e, i) {
                let operator = "";
                switch (e.operator) {
                    case equationOperatorEnum.addition: //1
                        operator = i === 0 ? "" : "+";
                        break;
                    case equationOperatorEnum.subtraction: //2
                        operator = "-";
                        break;
                    case equationOperatorEnum.multiplication: //3
                        operator = "*";
                        break;
                    case equationOperatorEnum.division: //4
                        operator = "/";
                        break;
                }
                let content = "";
                let sourceArr = e.source?.split("_") as string[];
                switch (e.valueType) {
                    case equationValueTypeEnum.accountSubject: //1
                        let asubId = Number(sourceArr[0]);
                        let asubCodeAndAACode = sourceArr[1];
                        let asubCode = asubCodeAndAACode.split("-")[0];
                        let aaCode = asubCodeAndAACode.split("-")[1];
                        let asubName = accountSubjects.value.filter(function (n) {
                            return n.asubId === asubId && n.asubCode === asubCode;
                        })[0].asubAAName;
                        let dateType = asubEquationDateType[Number(sourceArr[2])];
                        let dataType = asubEquationDataType[Number(sourceArr[3])];
                        if (aaCode) {
                            let aaCodeName = aaCode
                                .split(",")
                                .map((a: string) => {
                                    for (let aatype in assistentries.value) {
                                        let aae = assistentries.value[aatype].list.filter((b: any) => {
                                            return Number(a) === b.aaeid;
                                        })[0];
                                        if (aae) {
                                            return aae.aaname;
                                        }
                                    }
                                })
                                .join("_");
                            content = asubName + "_" + aaCodeName + "." + dateType + "." + dataType;
                        } else {
                            content = asubName + "." + dateType + "." + dataType;
                        }
                        break;
                    case equationValueTypeEnum.cell: //2
                        let statementId2 = Number(sourceArr[0]);
                        let statementModel = customStatementList.value.filter((n: ICustomStatementListItem) => {
                            return n.statementId == statementId2;
                        })[0];
                        content = statementModel.statementCode + statementModel.statementName + "." + sourceArr[1];
                        break;
                    case equationValueTypeEnum.statement: //3
                        let statementId3 = Number(sourceArr[0]);
                        let lineId = Number(sourceArr[1]);
                        let dataType3 = Number(sourceArr[2]);
                        let statementLinesItem = statementLines.value.find((v: IStatementLines) => v.statementId === statementId3);
                        let statementName = statementLinesItem.statementName;
                        let lineName = statementLinesItem.statementItems.filter((n: IStatementLinesItems) => {
                            return n.lineID === lineId;
                        })[0].lineName;

                        let dataTypeName = statementLinesItem.dataTypes[dataType3];
                        content = statementName + "." + lineName + "." + dataTypeName;
                        break;
                }
                cellContent.value += operator + content;
            });
        } else {
            cellContent.value = statementModel.cells[selectedStartCell.value].cellContent;
        }
    }
}
function refreshTable() {
    nextTick(() => {
        setStyleAndClass();
        spanCells();
    });
}
function getRowNumberData(cell: string) {
    return splitCellName(cell).rowNumber;
}
function getColNumberData(cell: string) {
    if (!cell) {
        cell = "K4";
    }
    return splitCellName(cell).columnNumber;
}
// 插入行
function handleInsertRow() {
    statementModel.rowNumber++;
    let newCells: { [key: string]: IStatementModelCellValue } = {};
    if (selectedStartCell.value) {
        let newSelectedStartCell = selectedStartCell.value;
        let newSelectedEndCell = selectedEndCell.value;
        let rowNumber = getRowNumberData(selectedStartCell.value);
        for (let cellName in statementModel.cells) {
            let r = getRowNumberData(cellName);
            let c = getColNumberData(cellName);
            if (r >= rowNumber) {
                let newCellName = convertToColumnName(c) + (r + 1);
                if (cellName === selectedStartCell.value) {
                    newSelectedStartCell = newCellName;
                }
                if (cellName === selectedEndCell.value) {
                    newSelectedEndCell = newCellName;
                }
                newCells[newCellName] = statementModel.cells[cellName];
                delete statementModel.cells[cellName];
            }
            if (
                statementModel.cells[cellName] &&
                r <= rowNumber &&
                r + statementModel.cells[cellName].rowSpan - 1 >= rowNumber &&
                statementModel.cells[cellName].rowSpan !== 1
            ) {
                saveCellChange(cellName)("rowSpan", statementModel.cells[cellName].rowSpan + 1);
            }
        }
        selectedStartCell.value = newSelectedStartCell;
        selectedEndCell.value = newSelectedEndCell;
        let rowHeights = _.cloneDeep(statementModel.rowHeights);
        let updatedRowHeights = {};
        for (let key in rowHeights) {
            if (Number(key) >= rowNumber) {
                if (rowHeights[key]) {
                    updatedRowHeights[parseInt(key) + 1] = rowHeights[key];
                }
            } else {
                updatedRowHeights[parseInt(key)] = rowHeights[key];
            }
        }
        delete updatedRowHeights[rowNumber];
        statementModel.rowHeights = updatedRowHeights;
        appendCells(newCells);
        removeSelectedClass();
        nextTick(() => {
            refreshTable();
            addSelectedClass();
        });
    } else {
        appendCells(newCells);
        removeSelectedClass();
        nextTick(() => {
            refreshTable();
            addSelectedClass();
        });
    }
}
// 删除行
function handleDeleteRow() {
    // let startC = getColNumberData(selectedStartCell.value);
    // let endC = getColNumberData(selectedEndCell.value);
    // if (endC - startC + 1 !== statementModel.columnNumber) {
    //     ElNotify({
    //         type: "warning",
    //         message: "请选择要删除的行",
    //     });
    //     return;
    // }
    if (!selectedStartCell.value) {
        ElNotify({
            type: "warning",
            message: "请选择要删除的行",
        });
        return;
    }

    let startR = getRowNumberData(selectedStartCell.value);
    let endR = getRowNumberData(selectedEndCell.value);
    if (endR - startR + 1 === statementModel.rowNumber) {
        ElConfirm("亲，确定要删除所有行吗?").then((r: boolean) => {
            if (r) {
                deleteRow();
                refreshTable();
                addSelectedClass();
            }
        });
    } else {
        deleteRow();
    }
}
function deleteRow() {
    let startR = getRowNumberData(selectedStartCell.value);
    let endR = getRowNumberData(selectedEndCell.value);
    let minR = Math.min(startR, endR);
    let maxR = Math.max(startR, endR);
    let newCells: { [key: string]: IStatementModelCellValue } = {};
    let newSelectedStartCell = selectedStartCell;
    let newSelectedEndCell = selectedEndCell;
    for (let cellName in statementModel.cells) {
        let r = getRowNumberData(cellName);
        let c = getColNumberData(cellName);
        if (r < minR) continue;
        if (r > maxR) {
            let newCellName = convertToColumnName(c) + (r - (maxR - minR + 1));
            if (cellName === selectedStartCell.value) {
                newSelectedStartCell.value = newCellName;
            }
            if (cellName === selectedEndCell.value) {
                newSelectedEndCell.value = newCellName;
            }
            newCells[newCellName] = statementModel.cells[cellName];
            delete statementModel.cells[cellName];
            continue;
        }
        delete statementModel.cells[cellName];
    }
    selectedStartCell.value = newSelectedStartCell;
    selectedEndCell.value = newSelectedEndCell;
    statementModel.rowNumber = Math.max(20, statementModel.rowNumber - (maxR - minR + 1));
    for (let key in statementModel.rowHeights) {
        if (Number(key) >= Number(minR) && Number(key) <= Number(maxR)) {
            delete statementModel.rowHeights[key];
        } else if (Number(key) > Number(maxR)) {
            statementModel.rowHeights[Number(key) - (maxR - minR + 1)] = statementModel.rowHeights[key];
            delete statementModel.rowHeights[key];
        }
    }
    nextTick(() => {
        appendCells(newCells);
        removeSelectedClass();
        nextTick(() => {
            refreshTable();
            addSelectedClass();
        });
    });
}
function handleInsertCol() {
    statementModel.columnNumber++;
    let newCells: { [key: string]: IStatementModelCellValue } = {};
    if (selectedStartCell.value) {
        let newSelectedStartCell = selectedStartCell.value;
        let newSelectedEndCell = selectedEndCell.value;
        let columnNumber = getColNumberData(selectedStartCell.value);
        for (let cellName in statementModel.cells) {
            let r = getRowNumberData(cellName);
            let c = getColNumberData(cellName);
            if (c >= columnNumber) {
                let newCellName = convertToColumnName(c + 1) + r;
                if (cellName === selectedStartCell.value) {
                    newSelectedStartCell = newCellName;
                }
                if (cellName === selectedEndCell.value) {
                    newSelectedEndCell = newCellName;
                }
                newCells[newCellName] = statementModel.cells[cellName];
                delete statementModel.cells[cellName];
            }
            if (
                statementModel.cells[cellName] &&
                c <= columnNumber &&
                c + statementModel.cells[cellName].columnSpan - 1 >= columnNumber &&
                statementModel.cells[cellName].columnSpan !== 1
            ) {
                saveCellChange(cellName)("columnSpan", statementModel.cells[cellName].columnSpan + 1);
            }
        }
        selectedStartCell.value = newSelectedStartCell;
        selectedEndCell.value = newSelectedEndCell;
        let columnWidths = _.cloneDeep(statementModel.columnWidths);
        let updatedColumnWidths = {};
        for (let key in columnWidths) {
            if (Number(key) >= columnNumber) {
                if (columnWidths[key]) {
                    updatedColumnWidths[parseInt(key) + 1] = columnWidths[key];
                }
            } else {
                updatedColumnWidths[parseInt(key)] = columnWidths[key];
            }
        }
        delete updatedColumnWidths[columnNumber];
        statementModel.columnWidths = updatedColumnWidths;
        appendCells(newCells);
        nextTick(() => {
            refreshTable();
            addSelectedClass();
        });
    } else {
        appendCells(newCells);
        refreshTable();
        addSelectedClass();
    }
}
function handleDeleteCol() {
    // let startR = getRowNumberData(selectedStartCell.value);
    // let endR = getRowNumberData(selectedEndCell.value);
    // if (endR - startR + 1 !== statementModel.rowNumber) {
    //     ElNotify({
    //         type: "warning",
    //         message: "请选择要删除的列",
    //     });
    //     return;
    // }
    if (!selectedStartCell.value) {
        ElNotify({
            type: "warning",
            message: "请选择要删除的列",
        });
        return;
    }
    let startC = getColNumberData(selectedStartCell.value);
    let endC = getColNumberData(selectedEndCell.value);
    if (endC - startC + 1 === statementModel.columnNumber) {
        ElConfirm("亲，确定要删除所有列吗?").then((r: boolean) => {
            if (r) {
                deleteCol();
            }
        });
    } else {
        deleteCol();
    }
}
function deleteCol() {
    let startC = getColNumberData(selectedStartCell.value);
    let endC = getColNumberData(selectedEndCell.value);
    let minC = Math.min(startC, endC);
    let maxC = Math.max(startC, endC);
    let newCells: { [key: string]: IStatementModelCellValue } = {};
    let newSelectedStartCell = selectedStartCell.value;
    let newSelectedEndCell = selectedEndCell.value;
    for (let cellName in statementModel.cells) {
        let r = getRowNumberData(cellName);
        let c = getColNumberData(cellName);
        if (c < minC) continue;
        if (c > maxC) {
            let newCellName = convertToColumnName(c - (maxC - minC + 1)) + r;
            if (cellName === selectedStartCell.value) {
                newSelectedStartCell = newCellName;
            }
            if (cellName === selectedEndCell.value) {
                newSelectedEndCell = newCellName;
            }
            newCells[newCellName] = statementModel.cells[cellName];
            delete statementModel.cells[cellName];
            continue;
        }
        delete statementModel.cells[cellName];
    }
    selectedStartCell.value = newSelectedStartCell;
    selectedEndCell.value = newSelectedEndCell;
    statementModel.columnNumber = Math.max(16, statementModel.columnNumber - (maxC - minC + 1));
    for (let key in statementModel.columnWidths) {
        if (Number(key) >= Number(minC) && Number(key) <= Number(maxC)) {
            delete statementModel.columnWidths[key];
        } else if (Number(key) > Number(maxC)) {
            statementModel.columnWidths[Number(key) - (maxC - minC + 1)] = statementModel.columnWidths[key];
            delete statementModel.columnWidths[key];
        }
    }
    appendCells(newCells);
    nextTick(() => {
        refreshTable();
        addSelectedClass();
    });
}
function handleCellsSpan() {
    if (!selectedStartCell.value) {
        ElNotify({
            type: "warning",
            message: "请选择单元格",
        });
        return;
    }
    if (selectedStartCell.value === selectedEndCell.value) return;
    let startR = getRowNumberData(selectedStartCell.value);
    let startC = getColNumberData(selectedStartCell.value);
    let startSpanR =
        startR + (statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].rowSpan : 1) - 1;
    let startSpanC =
        startC + (statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].columnSpan : 1) - 1;
    let endR = getRowNumberData(selectedEndCell.value);
    let endC = getColNumberData(selectedEndCell.value);
    let endSpanR = endR + (statementModel.cells[selectedEndCell.value] ? statementModel.cells[selectedEndCell.value].rowSpan : 1) - 1;
    let endSpanC = endC + (statementModel.cells[selectedEndCell.value] ? statementModel.cells[selectedEndCell.value].columnSpan : 1) - 1;

    let minR = Math.min(endR, endSpanR, startR, startSpanR);
    let maxR = Math.max(endR, endSpanR, startR, startSpanR);
    let minC = Math.min(endC, endSpanC, startC, startSpanC);
    let maxC = Math.max(endC, endSpanC, startC, startSpanC);
    for (let r = minR; r <= maxR; r++) {
        for (let c = minC; c <= maxC; c++) {
            let cellName = convertToColumnName(c) + r;
            if (
                statementModel.cells[cellName] &&
                ((statementModel.cells[cellName].contentType === contentTypeEnum.text && !!statementModel.cells[cellName].cellContent) ||
                    (statementModel.cells[cellName].contentType === contentTypeEnum.equation &&
                        statementModel.cells[cellName].equations.length > 0))
            ) {
                ElConfirm("合并后将只保留左上角单元格中的内容，继续合并吗？").then((r) => {
                    if (r) {
                        spanFun();
                    }
                });
                return;
            }
        }
    }

    spanFun();
    function spanFun() {
        let spanCellName = convertToColumnName(minC) + minR;
        saveCellChange(spanCellName)("rowSpan", maxR - minR + 1);
        saveCellChange(spanCellName)("columnSpan", maxC - minC + 1);
        selectedStartCell.value = spanCellName;
        selectedEndCell.value = spanCellName;
        for (let i = minR; i <= maxR; i++) {
            for (let j = minC; j <= maxC; j++) {
                let cellName = convertToColumnName(j) + i;
                if (cellName !== spanCellName) {
                    saveCellChange(cellName)("cellContent", "");
                    saveCellChange(cellName)("contentType", contentTypeEnum.text);
                    saveCellChange(cellName)("rowSpan", 1);
                    saveCellChange(cellName)("columnSpan", 1);
                    saveCellChange(cellName)("equations", []);
                }
                resetCellEuationState(cellName);
            }
        }
        removeSelectedClass();
        refreshTable();
        addSelectedClass();
    }
}
function spanCells() {
    for (let cellName in statementModel.cells) {
        let cell = statementModel.cells[cellName];
        let rowNumber = splitCellName(cellName).rowNumber;
        let columnNumber = splitCellName(cellName).columnNumber;
        if (cell.rowSpan > 1) {
            let domHeight = parseInt(cellClassStyle[cellName].moreStyle.height.slice(0, -2), 10);
            let marginBottom = 0;
            for (let i = rowNumber + 1; i < rowNumber + cell.rowSpan; i++) {
                let columnName = convertToColumnName(columnNumber) + i;
                domHeight += parseInt(cellClassStyle[columnName].moreStyle.height.slice(0, -2), 10) - 1;
                marginBottom += parseInt(cellClassStyle[columnName].moreStyle.height.slice(0, -2), 10) - 1;
            }
            cellClassStyle[cellName].moreStyle.height = domHeight + "px";
            cellClassStyle[cellName].moreStyle.marginBottom = -marginBottom + "px";
        }
        if (cell.columnSpan > 1) {
            let domWidth = parseInt(cellClassStyle[cellName].moreStyle.width.slice(0, -2), 10);
            let marginRight = 0;
            for (let i = columnNumber + 1; i < columnNumber + cell.columnSpan; i++) {
                let columnName = convertToColumnName(i) + rowNumber;
                domWidth += parseInt(cellClassStyle[columnName].moreStyle.width.slice(0, -2), 10) - 1;
                marginRight += parseInt(cellClassStyle[columnName].moreStyle.width.slice(0, -2), 10) - 1;
            }
            cellClassStyle[cellName].moreStyle.width = domWidth + "px";
            cellClassStyle[cellName].moreStyle.marginRight = -marginRight + "px";
        }

        for (let i = rowNumber; i < rowNumber + cell.rowSpan; i++) {
            for (let j = columnNumber; j < columnNumber + cell.columnSpan; j++) {
                if (i === rowNumber && j === columnNumber) continue;
                cellClassStyle[convertToColumnName(j) + i].moreClass += " spaned";
            }
        }
    }
}
function setStyleAndClass() {
    for (let row = 1; row <= statementModel.rowNumber; row++) {
        for (let column = 1; column <= statementModel.columnNumber; column++) {
            let columnName = convertToColumnName(column);
            let rowName = row.toString();
            let cellName = columnName + rowName;
            let cellModel = statementModel.cells[cellName];
            cellClassStyle[cellName] = { moreClass: "", moreStyle: {} };
            if (cellModel) {
                switch (cellModel.verticalAlign) {
                    case verticalAlignEnum.top:
                        cellClassStyle[cellName].moreClass += " vertical-top";
                        break;
                    case verticalAlignEnum.center:
                        cellClassStyle[cellName].moreClass += " vertical-center";
                        break;
                    case verticalAlignEnum.bottom:
                        cellClassStyle[cellName].moreClass += " vertical-bottom";
                        break;
                }
                switch (cellModel.horizontalAlign) {
                    case horizontalAlignEnum.left:
                        cellClassStyle[cellName].moreClass += " horizontal-left";
                        break;
                    case horizontalAlignEnum.center:
                        cellClassStyle[cellName].moreClass += " horizontal-center";
                        break;
                    case horizontalAlignEnum.right:
                        cellClassStyle[cellName].moreClass += " horizontal-right";
                        break;
                }
                cellClassStyle[cellName].moreStyle.fontSize = (cellModel.fontSize || 14) + "px";
                cellClassStyle[cellName].moreStyle.fontFamily = cellModel.fontFamily || "微软雅黑";
            }
            cellClassStyle[cellName].moreStyle.width = "62px";
            cellClassStyle[cellName].moreStyle.height = "22px";
            cellClassStyle[cellName].moreStyle.marginBottom = "0";
            cellClassStyle[cellName].moreStyle.marginRight = "0";
            if (statementModel.rowHeights && statementModel.rowHeights[row]) {
                cellClassStyle[cellName].moreStyle.height = statementModel.rowHeights[row] + "px";
            }

            if (statementModel.columnWidths && statementModel.columnWidths[column]) {
                cellClassStyle[cellName].moreStyle.width = statementModel.columnWidths[column] + "px";
            }
        }
    }
}

function handleCellsPreSpan() {
    if (!selectedStartCell.value) {
        ElNotify({
            type: "warning",
            message: "请选择单元格",
        });
        return;
    }
    if (selectedStartCell.value !== selectedEndCell.value) return;
    saveCellChange(selectedStartCell.value)("rowSpan", 1);
    saveCellChange(selectedStartCell.value)("columnSpan", 1);
    refreshTable();
    removeSelectedClass();
    addSelectedClass();
}
function setCellAlign(alignDirection: string, alignType: number) {
    if (selectedCell.value.length === 0) {
        ElNotify({
            type: "warning",
            message: "请选择单元格",
        });
        return;
    }
    selectedCell.value.forEach((v) => {
        saveCellChange(v)(alignDirection, alignType);
        refreshTable();
    });
    removeSelectedClass();
    addSelectedClass();
}
function setFontSize(fontSize: number) {
    if (selectedCell.value.length === 0) {
        return;
    }
    selectedCell.value.forEach((v) => {
        saveCellChange(v)("fontSize", fontSize);
        refreshTable();
    });
    addSelectedClass();
}
function setFontFamily(fontFamily: string) {
    if (selectedCell.value.length === 0) {
        return;
    }
    selectedCell.value.forEach((v) => {
        saveCellChange(v)("fontFamily", fontFamily);
        refreshTable();
    });
    addSelectedClass();
}
function handleCellMouseDown(event, cellName: string, row: number, col: number) {
    if (isEditingCell.value !== cellName) {
        const focusedElement = document.activeElement as HTMLElement;
        focusedElement.blur();
    }
    selectedStartCell.value = cellName;
    selectedEndCell.value = cellName;
    isSelecting.value = true;
    removeSelectedClass();
    addSelectedClass();
}
function handleCellMouseMove(event) {
    event.preventDefault();
}
function cancelTableCellSelect() {
    if (selectedStartCell.value) {
        selectedStartCell.value = "";
        removeSelectedClass();
    }
}
function handleCellMouseEnter(event, cellName: string, row: number, col: number) {
    event.preventDefault();
    if (isSelecting.value) {
        selectedEndCell.value = cellName;
        removeSelectedClass();
        addSelectedClass();
    }
}
function handleColMouseDown(event, columnName: string) {
    event.preventDefault();
    selectedStartCell.value = columnName + 1;
    selectedEndCell.value = columnName + statementModel.rowNumber;
    isColumnSelecting.value = true;
    removeSelectedClass();
    addSelectedClass();
}

function handleColMouseEnter(event, columnName: string) {
    event.preventDefault();
    if (isColumnSelecting.value) {
        selectedEndCell.value = columnName + statementModel.rowNumber;
        removeSelectedClass();
        addSelectedClass();
    }
}
function handleRowMousedown(event, rowNumber: string) {
    event.preventDefault();
    selectedStartCell.value = convertToColumnName(1) + rowNumber;
    selectedEndCell.value = convertToColumnName(statementModel.columnNumber) + rowNumber;
    isRowSelecting.value = true;
    removeSelectedClass();
    addSelectedClass();
}
function handleRowMouseEnter(event, rowNumber: string) {
    event.preventDefault();
    if (isRowSelecting.value) {
        selectedEndCell.value = convertToColumnName(statementModel.columnNumber) + rowNumber;
        removeSelectedClass();
        addSelectedClass();
    }
}
let isEditing = ref(false);
let isEditingCell = ref("");
let currentTextareaValue = ref("");
function handleCellDbClick(event: any, row, col, cellContent) {
    const parent = event.target.parentNode;
    parent.classList.add("editing");
    isEditing.value = true;
    isEditingCell.value = convertToColumnName(col) + row;
    const textarea = parent.querySelector("textarea");
    textarea.value = cellContent.trim();
    textarea.select();
    currentTextareaValue.value = cellContent.trim();
}

let currentTextareaRef = ref();
function handleTextareaBlur(event: any, cellName: string) {
    const parent = event.target.parentNode;
    saveCellChange(cellName)("cellContent", event.target.value);
    saveCellChange(cellName)("contentType", contentTypeEnum.text);
    saveCellChange(cellName)("equations", []);
    resetCellEuationState(cellName);
    reCalcCells();
    parent.classList.remove("editing");
    isEditing.value = false;
}
function handleTableAllBtnClick() {
    selectedStartCell.value = convertToColumnName(1) + 1;
    selectedEndCell.value = convertToColumnName(statementModel.columnNumber) + statementModel.rowNumber;
    removeSelectedClass();
    addSelectedClass();
}
function handleKeyDown(e) {
    const { keyCode } = e;
    switch (keyCode) {
        case 37: // 左箭头键
            tableContainerRef.value.container.scrollLeft -= 10;
            break;
        case 38: // 上箭头键
            tableContainerRef.value.container.scrollTop -= 10;

            break;
        case 39: // 右箭头键
            tableContainerRef.value.container.scrollLeft += 10;

            break;
        case 40: // 下箭头键
            tableContainerRef.value.container.scrollTop += 10;

            break;
    }
    if (isEditing.value) return;
    if (keyCode === 8 || keyCode === 46) {
        if (selectedStartCell.value && selectedEndCell.value) {
            let startR = getRowNumberData(selectedStartCell.value);
            let startC = getColNumberData(selectedStartCell.value);
            let endR = getRowNumberData(selectedEndCell.value);
            let endC = getColNumberData(selectedEndCell.value);
            let startSpanR =
                startR + (statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].rowSpan : 1) - 1;
            let startSpanC =
                startC + (statementModel.cells[selectedStartCell.value] ? statementModel.cells[selectedStartCell.value].columnSpan : 1) - 1;
            let endSpanR =
                endR + (statementModel.cells[selectedEndCell.value] ? statementModel.cells[selectedEndCell.value].rowSpan : 1) - 1;
            let endSpanC =
                endC + (statementModel.cells[selectedEndCell.value] ? statementModel.cells[selectedEndCell.value].columnSpan : 1) - 1;
            let minR = Math.min(endR, endSpanR, startR, startSpanR);
            let maxR = Math.max(endR, endSpanR, startR, startSpanR);
            let minC = Math.min(endC, endSpanC, startC, startSpanC);
            let maxC = Math.max(endC, endSpanC, startC, startSpanC);
            for (let r = minR; r <= maxR; r++) {
                for (let c = minC; c <= maxC; c++) {
                    let cellName = convertToColumnName(c) + r;
                    saveCellChange(cellName)("cellContent", "");
                    saveCellChange(cellName)("contentType", contentTypeEnum.text);
                    saveCellChange(cellName)("equations", []);
                    resetCellEuationState(cellName);
                }
            }
            refreshTable();
            addSelectedClass();
        }
    }
}
$bus.on("getCellsByStatementModule", (cells: { [key: string]: IStatementModelCellValue }) => {
    appendCells(cells);
    let rowArr: number[] = [];
    let colArr: number[] = [];
    for (let key in cells) {
        let rowNumber = splitCellName(key)?.rowNumber as number;
        let columnNumber = splitCellName(key)?.columnNumber as number;
        if (!rowArr.includes(rowNumber)) {
            rowArr.push(rowNumber);
        }
        if (!colArr.includes(columnNumber)) {
            colArr.push(columnNumber);
        }
    }
    statementModel.rowNumber = Math.max(rowArr.length, 20);
    statementModel.columnNumber = Math.max(colArr.length, 16);
    getStatementCode();
    removeSelectedClass();
    refreshTable();
});
$bus.on("setStatementModel", (data: any) => {
    resetStatementModel();
    setStatementModel(data);
    removeSelectedClass();
});

$bus.on("getFillingCellsByStatementModule", (cells: { [key: string]: IStatementModelCellValue }) => {
    appendCells(cells);
    removeSelectedClass();
    refreshTable();
});

function saveStatementModel() {
    if (!statementModel.statementCode) {
        ElNotify({
            type: "warning",
            message: "请填写报表编码！",
        });
        return false;
    }
    if (statementModel.statementCode.length > 40) {
        ElNotify({
            type: "warning",
            message: "报表编码长度不能超过40个字符！",
        });
        return false;
    }
    if (!statementModel.statementName) {
        ElNotify({
            type: "warning",
            message: "请填写报表名称！",
        });
        return false;
    }
    if (statementModel.statementName.length > 60) {
        ElNotify({
            type: "warning",
            message: "报表名称长度不能超过60个字符！",
        });
        return false;
    }
    if (statementModel.note.length > 255) {
        ElNotify({
            type: "warning",
            message: "备注长度不能超过255个字符！",
        });
        return false;
    }
    useLoading().enterLoading("数据加载中...");
    request({
        url: "/api/CustomStatement",
        method: "post",
        data: statementModel,
    }).then((res: IResponseModel<boolean>) => {
        useLoading().quitLoading();
        if (res.data) {
            ElNotify({
                type: "success",
                message: "保存成功！",
            });
            emits("submitSuccess");
            getCustomStatementList();
        } else {
            if (res.subState === 1) {
                ElNotify({
                    type: "warning",
                    message: "报表编码重复，请重新输入。",
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg || "保存失败。",
                });
            }
        }
    });
}
function saveStatementModelPId() {
    request({
        url: `/api/CustomStatement/PId?statementId=${statementModel.statementId}&pId=${statementModel.pId}`,
        method: "put",
    }).then((res: any) => {
        if (res.data) {
            ElNotify({
                type: "success",
                message: "保存成功！",
            });

            emits("submitSuccess");
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存成功！",
            });
        }
    });
}

function cancelStatementModel() {
    resetStatementModel();
    refreshTable();
    emits("goMain");
}
function resetStatementModel() {
    statementModel.statementId = 0;
    statementModel.statementCode = "";
    statementModel.statementName = "";
    statementModel.rowNumber = 20;
    statementModel.columnNumber = 16;
    statementModel.rowHeights = {};
    statementModel.columnWidths = {};
    statementModel.pId = 0;
    statementModel.note = "";
    statementModel.createdBy = "";
    statementModel.createdDate = "";
    statementModel.modifiedBy = "";
    statementModel.modifiedDate = "";
    statementModel.cells = {};
    cellContent.value = "";
    selectedStartCell.value = "";
    verticalAlignHover.value = 0;
    horizontalAlignHover.value = 0;
}
defineExpose({});
const handleCodeInput = (value: string) => {
    statementModel.statementCode = value.replace(/[^a-zA-Z\d]/g, "");
};
const handleNameInput = (value: string) => {
    statementModel.statementName = value.replace(/[^a-zA-Z0-9_\-\s\u4e00-\u9fa5]/g, "");
};
onMounted(() => {
    document.addEventListener("keydown", handleKeyDown);
});
onUnmounted(() => {
    document.removeEventListener("keydown", handleKeyDown);
});
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";

.statement-table-content {
    .main-top {
        width: 100%;
        margin: 0 !important;
        :deep(.pagination-period-button .pagination-period-panel) {
            z-index: 60;
        }
        .main-tool-left {
            .el-input {
                height: 28px;
            }
        }
    }

    .statement-table-container {
        .statement-table {
            height: calc(100vh - 68px);
            display: flex;
            flex-direction: column;
            align-items: stretch;
            --background-color: #f1f3f5;
            --background-hover-color: #e1e6eb;
            --cell-height: 22px;
            --cell-width: 62px;
            --left-title-width: 38px;
            --table-font-color: #4f5d79;
            --table-border-color: #d4d4d4;
            --table-border-hover-color: #3d7fff;
            --cell-hover-background: #f1f5fc;

            .table-btnsbar {
                border-top: 1px solid var(--border-color);
                padding: 0 8px;
                height: 48px;
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                .selector-content {
                    height: 28px;
                    border-radius: var(--input-border-radius);

                    :deep(.el-select) {
                        height: 26px;
                        line-height: 26px;

                        .el-input__wrapper {
                            height: 26px;
                            padding: 1px 5px 1px 10px;

                            .el-input__suffix-inner > :first-child {
                                margin-left: 0;
                            }
                        }
                    }
                }

                .btn {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    cursor: pointer;
                    border-radius: 4px;
                    margin-left: 32px;

                    &.down-icon {
                        height: 14px;
                        width: 14px;
                        background-image: url(@/assets/Icons/down-black.png);
                        background-position: center;
                        background-repeat: no-repeat;
                        margin-left: 8px;
                        position: relative;

                        .more {
                            display: none;
                            position: absolute;
                            top: 14px;
                            right: 0;
                            padding-top: 3px;
                            z-index: 2;

                            .buttons {
                                padding: 0;
                                background-color: var(--white);
                                box-shadow: 0px 2px 2px 0px rgba(173, 187, 200, 0.19), 0px 4px 4px 0px rgba(64, 97, 121, 0.05),
                                    0px 8px 8px 0px rgba(234, 239, 243, 0.05), 0px 16px 16px 0px rgba(215, 217, 219, 0.05),
                                    0px 32px 32px 0px rgba(234, 239, 243, 0.3);
                                border: 1px solid var(--border-color);
                                border-radius: 2px;

                                div {
                                    height: 30px;
                                    line-height: 30px;
                                    font-size: var(--font-size);
                                    color: var(--font-color);
                                    padding: 0 8px;
                                    cursor: pointer;
                                    white-space: nowrap;
                                }
                            }

                            &:hover {
                                display: block;
                            }
                        }

                        &:hover {
                            .more {
                                display: block;
                            }
                        }
                    }

                    &.icon-btn {
                        height: 20px;
                        width: 20px;
                        background-size: 17px 17px;
                        background-position: center;
                        background-repeat: no-repeat;

                        &.set-vertical-align[data-align-type="1"] {
                            background-image: url(@/assets/Statements/vertical-align-top.png);
                        }

                        &.set-vertical-align[data-align-type="2"] {
                            background-image: url(@/assets/Statements/vertical-align-center.png);
                        }

                        &.set-vertical-align[data-align-type="3"] {
                            background-image: url(@/assets/Statements/vertical-align-bottom.png);
                        }

                        &.set-horizontal-align[data-align-type="1"] {
                            background-image: url(@/assets/Statements/horizontal-align-left.png);
                        }

                        &.set-horizontal-align[data-align-type="2"] {
                            background-image: url(@/assets/Statements/horizontal-align-center.png);
                        }

                        &.set-horizontal-align[data-align-type="3"] {
                            background-image: url(@/assets/Statements/horizontal-align-right.png);
                        }
                    }
                    &:hover,
                    &.hover {
                        background-color: rgba(61, 127, 255, 0.3);
                    }
                }
            }

            .table-toolbar {
                background-color: var(--background-color);
                box-sizing: border-box;
                padding: 5px 5px 3px 5px;
                display: flex;
                align-items: flex-start;
                .selected-cell {
                    width: 86px;
                    :deep(.el-input__wrapper .el-input__inner) {
                        text-align: center;
                    }
                }

                .fx-icon {
                    font-size: var(--h1);
                    line-height: 28px;
                    color: #999999;
                    margin: 0 24px;
                }
            }

            .table-container {
                flex: 1;
                display: flex;
                position: relative;
                overflow: auto;

                .table-all-btn {
                    position: absolute;
                    left: 0;
                    top: 0;
                    background-color: var(--background-color);
                    height: var(--cell-height);
                    width: var(--left-title-width);
                    cursor: pointer;

                    &::after {
                        content: " ";
                        width: 0;
                        height: 0;
                        position: absolute;
                        right: 6px;
                        bottom: 6px;
                        border-right: 11px solid #4f5d79;
                        border-top: 11px solid transparent;
                    }
                }

                .table-top-title {
                    display: flex;
                    flex-wrap: nowrap;
                    position: absolute;
                    top: 0;
                    left: var(--left-title-width);

                    .t-t-line {
                        width: 0;
                        height: var(--cell-height);
                        flex-shrink: 0;
                        border-left: 1px solid;
                        border-image: linear-gradient(180deg, #f0f2f4, #d4d4d4) 1 1;
                        margin-left: -1px;
                        position: relative;
                        z-index: 49;

                        &.draggable {
                            cursor: col-resize;
                        }
                    }

                    .t-t {
                        width: var(--cell-width);
                        height: var(--cell-height);
                        background-color: var(--background-color);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: var(--table-font-color);
                        font-size: var(--font-size);
                        line-height: var(--line-height);
                        box-sizing: border-box;
                        flex-shrink: 0;
                        cursor: pointer;
                        margin-left: -1px;
                        position: relative;
                        &.selected {
                            border-bottom: 1px solid var(--table-border-hover-color);
                            background-color: var(--background-hover-color);
                            // border-color: var(--table-border-hover-color);
                            z-index: 48;
                        }
                        &:hover {
                            background-color: var(--background-hover-color);
                            border-color: var(--table-border-hover-color);
                            z-index: 48;
                        }
                    }
                }

                .table-left-title {
                    display: flex;
                    flex-direction: column;
                    flex-wrap: nowrap;
                    position: absolute;
                    top: var(--cell-height);
                    left: 0;

                    .l-t-line {
                        width: var(--left-title-width);
                        height: 0;
                        flex-shrink: 0;
                        border-top: 1px solid;
                        border-image: linear-gradient(90deg, #f0f2f4, #d4d4d4) 1 1;
                        margin-top: -1px;
                        position: relative;
                        z-index: 49;

                        &.draggable {
                            cursor: row-resize;
                        }
                    }

                    .l-t {
                        width: var(--left-title-width);
                        height: var(--cell-height);
                        background-color: var(--background-color);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: var(--table-font-color);
                        font-size: var(--font-size);
                        line-height: var(--line-height);
                        box-sizing: border-box;
                        // border-right: 1px solid var(--table-border-color);
                        flex-shrink: 0;
                        cursor: pointer;
                        margin-top: -1px;
                        position: relative;
                        &.selected {
                            background-color: var(--background-hover-color);
                            // border-color: var(--table-border-hover-color);
                            z-index: 48;
                            border-right: 1px solid var(--table-border-hover-color);
                        }
                        &:hover {
                            background-color: var(--background-hover-color);
                            border-color: var(--table-border-hover-color);
                            z-index: 48;
                        }
                    }
                }

                .assit-row-line {
                    position: absolute;
                    left: 0;
                    right: 0;
                    border-top: 1px dashed var(--table-border-color);
                    display: none;
                }

                .assit-col-line {
                    position: absolute;
                    top: 0;
                    left: 20px;
                    bottom: 0;
                    border-left: 1px dashed var(--table-border-color);
                    display: none;
                }

                .table-content {
                    display: flex;
                    flex-direction: column;
                    flex-wrap: nowrap;
                    margin-top: var(--cell-height);
                    margin-left: var(--left-title-width);

                    .r-l {
                        display: flex;
                        flex-wrap: nowrap;
                        flex-shrink: 0;

                        .c-c {
                            width: var(--cell-width);
                            height: var(--cell-height);
                            display: flex;
                            box-sizing: border-box;
                            border: 1px solid var(--table-border-color);
                            flex-shrink: 0;
                            cursor: pointer;
                            margin-top: -1px;
                            margin-left: -1px;
                            position: relative;
                            color: var(--font-color);
                            font-size: var(--font-size);
                            line-height: var(--line-height);
                            line-height: 1.5;
                            &.editing {
                                .cell-content {
                                    width: 100%;
                                    height: 100%;
                                    display: flex;
                                    padding: 0 4px;
                                    overflow: hidden;
                                    display: none;
                                }
                                textarea {
                                    width: 100%;
                                    height: 100%;
                                    display: block;
                                }
                            }
                            .cell-content {
                                display: flex;
                                padding: 0 4px;
                                overflow: hidden;
                                width: 100%;
                                height: 100%;
                            }

                            &.horizontal-left .cell-content {
                                justify-content: flex-start;
                            }

                            &.horizontal-center .cell-content {
                                justify-content: center;
                            }

                            &.horizontal-right .cell-content {
                                justify-content: flex-end;
                            }

                            &.vertical-top .cell-content {
                                align-items: flex-start;
                            }

                            &.vertical-center .cell-content {
                                align-items: center;
                            }

                            &.vertical-bottom .cell-content {
                                align-items: flex-end;
                            }

                            & textarea {
                                resize: none;
                                outline: none;
                                border: none;
                                width: 100%;
                                height: 100%;
                                padding: 0 4px;
                                box-sizing: border-box;
                                vertical-align: top;
                                display: none;
                            }
                            &.border-left-hover {
                                border-left-color: var(--table-border-hover-color);
                                z-index: 9;
                            }
                            &.border-bottom-hover {
                                border-bottom-color: var(--table-border-hover-color);
                                z-index: 9;
                            }
                            &.border-right-hover {
                                border-right-color: var(--table-border-hover-color);
                                z-index: 9;
                            }
                            &.border-top-hover {
                                border-top-color: var(--table-border-hover-color);
                                z-index: 48;
                            }
                            &.selected {
                                z-index: 49;
                            }
                            &.background-hover {
                                background-color: var(--cell-hover-background);
                            }
                            &:hover {
                                border-color: var(--table-border-hover-color);
                                z-index: 60;
                            }
                            &.spaned {
                                visibility: hidden;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
