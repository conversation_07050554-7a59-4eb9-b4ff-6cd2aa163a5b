<template>
    <el-dialog
        v-model="display"
        title="提示"
        width="620"
        :draggable="false"
        :show-close="props.hasAccountSet"
        @closed="props.hasAccountSet ? props.onClose() : gotoCreate()"
    >
        <div class="virtual-as-dialog-container">
            <div class="tips">
                <div>
                    亲，您的云财务专业版试用期已过，已自动转为免费版。免费版只能<span style="color: #ff7500">一个账套管理员进入</span
                    >，如果有多个账套管理员，首次进入账套的<span style="color: #ff7500">账套管理员才能使账套。</span>
                </div>
                <div>&nbsp;</div>
                <div>当前有权限登录的账套管理员手机号：</div>
                <div v-html="ownerPhone"></div>
                <div>您可以联系他将权限移交给您继续使用:进入账套，点设置—权限设置-移交。</div>
                <div>&nbsp;</div>
                <div>如果您需要多个人使用账套，请联系管理员升级专业版</div>
            </div>
            <div class="buttons">
                <a class="button" @click="props.hasAccountSet ? close() : gotoCreate()">{{ props.hasAccountSet ? "取消" : "新建账套" }}</a>
                <a @click="showBuyDialog()" class="button solid-button ml-10">立即升级</a>
            </div>
        </div>
    </el-dialog>
</template>
<style lang="less">
.virtual-as-dialog-container {
    .tips {
        padding: 16px 20px 28px 25px;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 26px;
        text-align: left;
    }

    .buttons {
        border-top: 1px solid var(--border-color);
        text-align: center;
        padding: 10px 0;
    }
}
</style>
<script setup lang="ts">
import { ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { setTopLocationhref } from "@/util/url";

const props = defineProps<{
    onClose: () => void;
    showBuyDialog: () => void;
    hasAccountSet: boolean;
    newAsid: string;
}>();

const display = ref(false);
const ownerPhone = ref("");

const close = () => {
    display.value = false;
};

const showBuyDialog = () => {
    props.showBuyDialog();
};

const gotoCreate = () => {
    display.value = false;
    // globalWindowOpenPage("/Settings/AccountSets1", "创建账套");
    // setTopLocationhref(`/Settings/AccountSets1`);
    setTopLocationhref(`/Settings/AccountSets1?appasid=${props.newAsid}`);
};

const showDialog = (currentAsId: number) => {
    request({
        url: `/api/PermissionsOnlyAuth/GetOwnerPhone?asid=${currentAsId}`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            ownerPhone.value = res.data.split(",").join("<br/>");
        }
    });
    display.value = true;
};

const closeDialog = () => {
    display.value = false;
};

defineExpose({
    showDialog,
    closeDialog,
});
</script>
