:deep(.el-form-item.is-error .el-input__wrapper) {
    box-shadow: 0 0 0 1px #c0c4cc !important;

    &.is-focus {
        box-shadow: 0 0 0 1px var(--main-color) inset !important;
    }
}
:deep(.el-form-item__error) {
    display: none;
}
.ml-28 {
    margin-left: 28px;
}
.longer-button {
    width: 108px;
}
body[erp]{
    .slot-content {
        overflow: unset !important;
        .slot-mini-content {
            overflow: unset;
        }
    }
}
.open-account {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    &.detail-content {
        .detail-back-button {
            position: absolute;
            left: 50px;
            top: 16px;
            display: flex;
            align-items: center;
            cursor: pointer;
            img {
                width: 9px;
                height: 13px;
                margin-right: 5px;
            }
            span {
                color: gray;
                font-size: 14px;
                line-height: 22px;
            }
        }
        .slot-title {
            font-size: 16px;
        }
        .open-main-content {
            .openbank-line {
                padding-left: 120px;
            }
            :deep(.el-form-item__content) {
                align-items: start;
            }
        }
        .w-240 {
            width: 240px;
        }
    }
    .title {
        width: 100%;
    }

    .open-main-content {
        width: 1000px;
        .step-edit {
            & .line {
                width: 840px;
                margin: 0 auto;
                background-color: #f1f1f1;
            }
            & .block-title {
                padding: 24px 20px 0px 100px;
                color: var(--font-color);
                font-size: var(--h3);
                line-height: 22px;
                font-weight: bold;
                text-align: left;
            }
            & .block-tip {
                padding: 8px 0px 0px 100px;
                display: flex;
                align-items: center;
                color: #666666;
                font-size: 12px;
                line-height: 22px;
                text-align: left;
                & img {
                    width: 14px;
                    height: 15px;
                    margin-right: 8px;
                }
            }
            & .block-main {
                display: flex;
                flex-direction: column;
                margin-top: 20px;
                & .isRow {
                    padding: 0 40px;
                    text-align: left;
                    :deep(.el-col) {
                        display: flex;
                        align-items: center;
                    }
                    &.addressRow {
                        :deep(.el-select) {
                            margin-right: 8px;
                        }
                    }
                    :deep(.el-checkbox) {
                        margin-left: 14px;
                        .el-checkbox__label {
                            font-size: 12px;
                            color: #666666;
                        }
                    }
                    .require-icon {
                        color: #ff2f1f;
                    }
                    .row-label {
                        font-size: 14px;
                    }
                }
            }
        }
    }
}
:deep(.el-form-item) {
    margin-bottom: 23px;
    .el-form-item__label {
        padding: 0px;
    }
}
.slot-mini-content {
    width: 100% !important;
    align-items: center;
}
.slot-content,
.main-content {
    width: 100% !important;
}
.bank-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 100px;
    .bank-tip {
        display: flex;
        align-items: center;
        padding-left: 20px;
        color: #666666;
        font-size: var(--font-size);
        line-height: 22px;
        text-align: left;
        & img {
            width: 14px;
            height: 15px;
            margin-right: 8px;
        }
        margin-bottom: 29px;
    }
    .bank-info {
        padding: 0px 140px;
        display: flex;
        justify-content: center;

        .bank-card {
            margin: 0 10px;
            width: 180px;
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 4px;
            border: 1px solid #dddddd;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            img {
                width: 120px;
                height: 82px;
            }
            .bank-gift {
                color: #ff6105;
                height: 22px;
                line-height: 22px;
                margin-top: -15px;
            }
            &.selected {
                border: 1px solid #ff6105;
                background: url("@/assets/Cashier/isSelected.png") no-repeat;
                background-position: right bottom;
            }
            &.center {
                justify-content: center;
            }
        }
    }
}

.openbank-line {
    margin-bottom: 23px;
    padding-left: 90px;
    font-size: 14px;
}
.resultdialog {
    padding-top: 20px;

    .resultdialog-content {
        text-align: center;
        width: 400px;
        margin: 0 auto;
        .result-title {
            font-size: 20px;
            font-weight: bold;
            color: #333333;
            line-height: 24px;
            margin-bottom: 8px;
        }
        .result-info {
            font-size: 16px;
            color: #333333;
            margin-bottom: 20px;
            line-height: 24px;
        }
        .official-qrcode {
            width: 100px;
            height: 100px;
            margin-bottom: 20px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
