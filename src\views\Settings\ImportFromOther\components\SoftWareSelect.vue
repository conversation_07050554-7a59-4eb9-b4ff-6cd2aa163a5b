<template>
    <el-popover
        :visible="visible"
        placement="bottom-start"
        popper-class="software-list-popper"
        :width="340"
        :teleported="false"
        @show="onKeyWordChange"
    >
        <template #reference>
            <div :class="['software-list-trigger']" :title="softWareName" @click="() => (visible = !visible)">
                {{ softWareName }}
                <div class="dropdown-icon"></div>
            </div>
        </template>
        <div v-loading="props.softWareListLoading" class="softWare-dropDownList">
            <div class="search-box">
                <el-input
                    ref="searchIptRef"
                    v-model="searchKeyWord"
                    type="text"
                    class="search-ipt"
                    clearable
                    placeholder="请输入关键词搜索"
                />
            </div>
            <div class="suggest-list">
                <div
                    v-for="item in suggestList"
                    :key="item.softTypeId"
                    @click="selectTab(item)"
                    :class="['suggest-list-item', { selected: searchKeyWord === item.softTypeName }]"
                >
                    {{ item.softTypeName }}
                </div>
            </div>
            <el-scrollbar v-if="resultList && resultList.length > 0" ref="softwareListRef" style="width: 320px" height="200px" always>
                <ul class="software-list">
                    <li
                        v-for="item in resultList"
                        :key="item.softCompanyName"
                        @click="handleSelectSoftware(item.softCompanyId, item.softCompanyName, item.type)"
                        :class="{ current: props.softWare === item.softCompanyId }"
                    >
                        {{ item.softCompanyName }}
                    </li>
                </ul>
            </el-scrollbar>
            <p v-if="!resultList || resultList.length === 0" class="empty-tips">暂无数据</p>
        </div>
    </el-popover>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import type { ISoftWareItem, ISoftWareListItem, ISoftWareSuggest } from "../types";
import { pinyin } from "pinyin-pro";

const props = withDefaults(
    defineProps<{
        softWareList: ISoftWareListItem[];
        softWare: number;
        softWareName: string;
        softWareListLoading: boolean;
    }>(),
    {
        softWare: -1,
        softWareName: "--请选择--",
        softWareListLoading: false,
    }
);
const emits = defineEmits<{
    (e: "changeSoftWare", softCompanyId: number, softCompanyName: string, type: number): void;
}>();

// 是否显示下拉框
const visible = ref(false);
// 搜索关键词
const searchIptRef = ref();
const searchKeyWord = ref("");
// 第三方软件列表
const resultList = ref<ISoftWareItem[]>();

const softwareListRef = ref();

// 建议列表
const initSuggestList = [
    {
        softTypeId: 1,
        softTypeName: "金蝶",
    },
    {
        softTypeId: 2,
        softTypeName: "用友畅捷通",
    },
    {
        softTypeId: 5,
        softTypeName: "管家婆",
    },
    {
        softTypeId: 6,
        softTypeName: "金算盘",
    },
    {
        softTypeId: 7,
        softTypeName: "速达",
    },
    {
        softTypeId: 9,
        softTypeName: "浪潮",
    },
    {
        softTypeId: 11,
        softTypeName: "云代账",
    },
    {
        softTypeId: 12,
        softTypeName: "诺诺云",
    },
    {
        softTypeId: 13,
        softTypeName: "亿企赢",
    },
    {
        softTypeId: 99,
        softTypeName: "其他",
    },
];
const suggestList = ref<ISoftWareSuggest[]>(initSuggestList);

// 当前id
const currentId = ref(-1);
// 选择建议标签
const selectTab = (item: ISoftWareSuggest) => {
    if (searchKeyWord.value === item.softTypeName) {
        searchKeyWord.value = "";
        currentId.value = -1;
    } else {
        searchKeyWord.value = item.softTypeName;
        currentId.value = item.softTypeId;
    }
};

// 搜索关键词变化
const onKeyWordChange = () => {
    searchKeyWord.value = searchKeyWord.value.trim();
    if (searchKeyWord.value === "") {
        resultList.value = props.softWareList.reduce((prev: ISoftWareItem[] | [], item: ISoftWareListItem) => {
            prev = [...prev, ...item.softCompanyModels];
            return prev;
        }, []);
    } else {
        resultList.value = [];
        if (suggestList.value.every((item) => item.softTypeName !== searchKeyWord.value)) {
            currentId.value = -1;
        }
        // 如果包含特殊字符串
        if (/[^\da-zA-Z\u4e00-\u9fa5]/g.test(searchKeyWord.value)) {
            for (let item of props.softWareList) {
                if (item.softTypeName.includes(searchKeyWord.value)) {
                    resultList.value.push(...item.softCompanyModels);
                } else {
                    for (let model of item.softCompanyModels) {
                        if (model.softCompanyName.includes(searchKeyWord.value)) {
                            resultList.value.push(model);
                        }
                    }
                }
            }
        } else {
            const reg = new RegExp(searchKeyWord.value, "i");
            for (let item of props.softWareList) {
                const pinyinFirst = pinyin(item.softTypeName, {   
                    pattern: "first",   
                    toneType: "none",   
                    type: "array"   
                }).join("").toLowerCase();
                const pinyinFull = pinyin(item.softTypeName, {   
                    toneType: "none",   
                    type: "array"   
                }).join("").toLowerCase();
                if (
                    reg.test(item.softTypeName) 
                    || item.softTypeName.includes(searchKeyWord.value) 
                    || item.softTypeId === currentId.value
                    || reg.test(pinyinFirst)
                    || reg.test(pinyinFull)
                ) {
                    resultList.value.push(...item.softCompanyModels);
                } else {
                    for (let model of item.softCompanyModels) {
                        const pinyinFirstM = pinyin(model.softCompanyName, {   
                            pattern: "first",   
                            toneType: "none",   
                            type: "array"   
                        }).join("").toLowerCase();
                        const pinyinFullM = pinyin(model.softCompanyName, {   
                            toneType: "none",   
                            type: "array"   
                        }).join("").toLowerCase();
                        if (
                            model.softCompanyName.includes(searchKeyWord.value) ||
                            reg.test(model.softCompanyName) ||
                            reg.test(String(model.softCompanyId)) ||
                            reg.test(pinyinFirstM) ||
                            reg.test(pinyinFullM)
                        ) {
                            resultList.value.push(model);
                        }
                    }
                }
            }
        }
    }
    softwareListRef.value?.setScrollTop(0);
};

// 选择第三方软件
const handleSelectSoftware = (softCompanyId: number, softCompanyName: string, type: number) => {
    emits("changeSoftWare", softCompanyId, softCompanyName, type);
    visible.value = false;
};

watch(() => searchKeyWord.value, onKeyWordChange);

// 监听打开关闭事件
watch(
    () => visible.value,
    () => {
        //  点击空白处关闭下拉框 事件绑定与移除
        if (!visible.value) {
            document.onclick = null;
        } else {
            // 自动聚焦
            searchIptRef.value.focus();
            document.onclick = (event) => {
                // 检查点击区域是否在 popover 外  并且不在触发器上
                if (
                    !(event.target as any).closest(".software-list-popper") &&
                    !(event.target as any).closest(".software-list-trigger") &&
                    !(event.target as any).closest(".el-input__suffix")
                ) {
                    visible.value = false;
                }
            };
        }
    }
);

watch(() => props.softWareList, onKeyWordChange);
</script>
<style lang="less">
.software-list-trigger {
    position: relative;
    width: 270px;
    height: 32px;
    font-size: 14px;
    border: 1px solid #edefee;
    line-height: 32px;
    text-align: left;
    padding: 0 20px 0 12px;
    box-sizing: border-box;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &.show,
    &:hover {
        border: 1px solid var(--main-color);
    }
    .dropdown-icon {
        position: absolute;
        top: 10px;
        right: 8px;
        width: 12px;
        height: 11px;
        background: url(@/assets/Settings/arrow-down.png) no-repeat 0 0;
        background-size: 11px 11px;
    }
}
.software-list-popper {
    padding: 15px;
    .softWare-dropDownList {
        position: relative;
        width: 300px;
        // height: 330px;
        .search-box {
            position: relative;
            .search-ipt {
                .el-input__wrapper {
                    justify-content: flex-start;
                    width: 295px;
                    height: 30px;
                    border-radius: 14px;
                    line-height: 30px;
                    box-sizing: border-box;
                    padding: 0 16px;
                    background: #ffffff;
                    .el-input__inner {
                        flex-grow: unset;
                        width: 87%;
                    }
                }
            }
            &::after {
                position: absolute;
                top: 6px;
                right: 12px;
                content: "";
                width: 15px;
                height: 16px;
                background: url(@/assets/Settings/search-icon.png) no-repeat 0 0;
                background-size: 15px 16px;
            }
        }
        .suggest-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            width: 300px;
            font-size: 12px;
            min-height: 62px;
            margin: 15px 0;
            .suggest-list-item {
                padding: 5px 8px;
                margin: 0 5px 5px 0;
                color: #44b449;
                background-color: #f5fbf6;
                border-radius: 2px;
                cursor: pointer;
                &.selected,
                &:hover {
                    color: #fff;
                    background-color: #44b449;
                }
            }
        }
        .software-list {
            width: 300px;
            height: 200px;
            margin: 0;
            padding: 0;
            list-style: none;
            li {
                height: 22px;
                margin-bottom: 5px;
                font-size: 14px;
                font-weight: 400;
                color: #333;
                line-height: 22px;
                cursor: pointer;
                &.current,
                &:hover {
                    color: var(--main-color);
                }
            }
        }
        .empty-tips {
            width: 300px;
            height: 200px;
            text-align: center;
            line-height: 200px;
            color: #c3c3c3;
        }
    }
}
</style>
<style>
.software-list-popper.el-popover.el-popper {
    .el-popper__arrow {
        &::before {
            right: 113px;
        }
    }
}
</style>
