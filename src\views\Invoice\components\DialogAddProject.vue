<template>
    <el-dialog
        class="custom-confirm dialogDrag"
        title="添加项目"
        center
        v-model="addProjectShow"
        width="440px"
        :destroy-on-close="true"
        @close="handleProjectCancel"
    >
        <div class="add-ietype-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="add-ietype-line-item required">
                <div class="add-ietype-title">项目编码：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="projectCode"
                        @input="handleAAInput(LimitCharacterSize.Code, $event, '项目编码', 'code', changeProjectData)"
                        @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-line-item required">
                <div class="add-ietype-title">项目名称：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="projectName"
                        @input="handleAAInput(LimitCharacterSize.Name, $event, '项目名称', 'name', changeProjectData)"
                        @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-line-item">
                <div class="add-ietype-title">备注：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="projectNote"
                        @input="handleAAInput(LimitCharacterSize.Note, $event, '备注', 'note', changeProjectData)"
                        @paste="handleAAPaste(LimitCharacterSize.Note, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-buttons">
                <a class="button solid-button" @click="handleProjectSave">保存</a>
                <a class="button ml-10" @click="handleProjectCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import { ValidataProject } from "@/views/Settings/AssistingAccounting/validator";

const isErp = ref(window.isErp);

const props = defineProps<{
    addProjectShow: boolean;
}>();
const emit = defineEmits<{
    (e: "update:addProjectShow", value: boolean): void;
    (e: "save-project", data:any): void;
}>();

const addProjectShow = computed({
    get: () => props.addProjectShow,
    set: (value) => emit("update:addProjectShow", value),
});

// 项目
const projectCode = ref("");
const projectName = ref("");
const projectNote = ref("");
const handleProjectSave = () => {
    if (!projectCode.value.trim()) {
        ElNotify({ type: "warning", message: "请输入项目编码" });
        return;
    }
    if (!projectName.value.trim()) {
        ElNotify({ type: "warning", message: "请输入项目名称" });
        return;
    }
    createCheck(10005, 0, projectCode.value, projectName.value, SaveProject);
};
const handleProjectCancel = () => {
    addProjectShow.value = false;
    projectCode.value = "";
    projectName.value = "";
    projectNote.value = "";
};
const SaveProject = () => {
    const entityParams = {
        department: "",
        owner: "",
        mobilePhone: "",
        startDate: "",
        endDate: "",
        note: projectNote.value,
    };
    const params = {
        entity: entityParams,
        aaNum: projectCode.value,
        aaName: projectName.value,
        uscc: "",
        status: 0,
        hasEntity: true,
        ifvoucher: true,
    };
    const requestParams = {
        url: "/api/AssistingAccounting/Project",
        method: "post",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(params),
    };
    if (!ValidataProject(entityParams, params.aaNum, params.aaName)) return;
    request(requestParams).then((res: any) => {
        if (res.state == 1000) {
            emit("save-project", Number(res.data));
            handleProjectCancel();
        }
    });
};
const changeProjectData = (key: string, val: string) => {
    if (key === "code") {
        projectCode.value = val;
    } else if (key === "name") {
        projectName.value = val;
    } else if (key === "note") {
        projectNote.value = val;
    }
};

const changeCode = (value: string) => {
    projectCode.value = value;
};
const changeName = (name: string) => {
    projectName.value = name.slice(0, LimitCharacterSize.Name);
    if (name.length > LimitCharacterSize.Name) {
        ElNotify({ type: "warning", message: `亲，项目名称不能超过${LimitCharacterSize.Name}个字符!` });
    }
};
defineExpose({ changeCode, changeName });
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.add-ietype-content {
    padding: 20px 0;
    text-align: center;
    .add-ietype-line-item {
        margin-right: 40px;
        &.required {
            .add-ietype-title:before {
                content: "*";
                color: var(--red);
            }
        }
        & + .add-ietype-line-item {
            margin-top: 16px;
        }
        .add-ietype-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            display: inline-block;
            vertical-align: middle;
            width: 120px;
            text-align: right;
        }
        .add-ietype-field {
            display: inline-block;
            vertical-align: middle;
            text-align: left;
            .detail-el-select(200px, 32px);
            & > input {
                .detail-original-input(200px, 32px);
            }
        }
    }
    .add-ietype-buttons {
        margin-top: 24px;
    }

    &.erp {
        padding-bottom: 0px;
        .add-ietype-buttons {
            height: 30px;
            display: block;
            box-sizing: content-box;
            border-top: 1px solid var(--border-color);
            text-align: right;
            padding: 16px 20px 16px 0;
            & a {
                text-align: center;
                display: block;
                float: right;
                width: 70px;
                min-width: 70px;
                height: 30px;
                border-radius: 2px;
                margin-left: 20px;
                line-height: 30px;
            }
            & button {
                box-sizing: border-box;
                padding: 0 12px;
                font-size: var(--font-size);
            }
        }
    }
}
</style>
