<template>
    <iframe ref="urlIframe" :src="iframeSrc"></iframe>
</template>
<script lang="ts">
export default {
    name: "PreviewPrint",
};
</script>
<script setup lang="ts">
import { useRoute } from "vue-router";
import { onMounted, ref, onBeforeUnmount, onActivated } from "vue";

const iframeSrc = ref("");
const urlIframe = ref<HTMLIFrameElement | null>(null);
const route = useRoute();
const isErp = ref(window.isErp);

function getQueryString() {
    const queryParams = route.query;

    // 过滤掉不需要的键
    const filteredQueryParams = Object.fromEntries(
        Object.entries(queryParams).filter(([key]) => key !== 'appasid' && key !== 'iframeSrc')
    );

    // 使用过滤后的查询参数构建字符串
    const queryString = Object.entries(filteredQueryParams).map(([key, value]) => {
        return `${key}=${value}`;
    }).join('&');
    return queryString;
}

onMounted(() => {
    setTimeout(() => {
        if(isErp.value){
            let iframeSrcString = (route.query.iframeSrc?.toString() || "") + "&" + getQueryString();
            iframeSrc.value = iframeSrcString;
        } else{
            iframeSrc.value = route.query.iframeSrc?.toString() || "";
        }
    });
});
onBeforeUnmount(() => {
    // 移除 iframe 元素
    urlIframe.value && urlIframe.value.remove();
});

onActivated(() => {
    const route = useRoute();
    setTimeout(() => {
        let previreUrl = route.query.iframeSrc?.toString() || "";
        if(isErp.value){
            previreUrl = previreUrl + "&" + getQueryString();
        }
        if (previreUrl && previreUrl !== iframeSrc.value) {
            iframeSrc.value = previreUrl;
        }
    });
});

</script>

<style scoped lang="less">
iframe {
    width: 100%;
    height: 100%;
    border: none;
    margin: 0;
    padding: 0;
    vertical-align: top;
}
</style>