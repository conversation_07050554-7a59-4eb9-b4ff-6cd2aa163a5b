<template>
    <el-dialog v-model="easyRecommendDialog.display" class="custom-confirm easy-recommend" :show-close="false" center width="550px">
        <div class="easy-recommend-container">
            <div class="recommend-title">
                <div>好用功能推荐-{{ easyRecommendDialog.dialogContent.title }}</div>
            </div>
            <p class="recommend-content">
                <span v-for="(item, index) in easyRecommendDialog.dialogContent.content" :key="index"
                    >{{ item }}<br v-show="index < easyRecommendDialog.dialogContent.content.length - 1"
                /></span>
            </p>
            <p class="video-guide">
                快速指引：
                <a class="link mr-5" @click="gotoStudy"> 视频教程 </a>
                <img src="@/assets/Settings/video.png" @click="gotoStudy" alt="视频" style="height: 16px" />
            </p>
            <div class="btns">
                <a class="recommend-button mr-30" @click="closeEasyRecommendDialog()">关闭</a>
                <a class="recommend-button solid-button" @click="handleGoPage(easyRecommendDialog.dialogContent)">去看看</a>
            </div>
            <div class="no-more-prompt">
                <el-checkbox v-model="easyRecommendPromptStatus" label="以后不再提示" />
            </div>
        </div>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import type { IEasyRecommendationContent } from "./types";
import { checkPermission } from "@/util/permission";
import { ElNotify } from "@/util/notify";
import { globalWindowOpenPage } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { globalWindowOpen } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountset";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { ElConfirm } from "@/util/confirm";
const trialStatusStore = useTrialStatusStore();
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart);
const needCheckEasyRecommend = computed(() => {
    return !isThirdPart.value && trialStatusStore.isTrial && [1, 2].includes(Number(useAccountSetStore().accountSet?.accountingStandard));
});
const easyRecommendationList: { [key: number]: IEasyRecommendationContent } = {
    100: {
        title: "资金管理",
        content: ["全自动获取银行交易流水，支持多个银行；", "一键自动生成资金凭证，丰富的资金报表，实时掌握资金状况；"],
        videoGuide: "*********",
        jumpPage: "/Cashier/DepositJournal",
        pageTitle: "银行日记账",
        viewPermission: "depositjournal-canview",
        frequency: "day",
    },
    200: {
        title: "发票管理",
        content: ["进销项发票直连税局一键取票，一键生成凭证；", "智能分析发票风险，出具发票风险分析报告，规避发票风险；"],
        videoGuide: "*********",
        jumpPage: "/Invoice/FetchInvoice",
        viewPermission: "invoice-fetch",
        frequency: "day",
        pageTitle: "一键取票",
    },
    300: {
        title: "资产管理",
        content: ["快速新增资产卡片，支持变更或处置资产；", "自动计提折旧/摊销，智能生成折旧/摊销凭证，轻松管理资产；"],
        videoGuide: "130190110",
        jumpPage: "/FixedAssets/FixedAssets",
        viewPermission: "fixedassets-card-canview",
        frequency: "month",
        pageTitle: "资产管理",
    },
    400: {
        title: "工资管理",
        content: ["快速生成工资表，一键申报个税，简单快速；", "自动生成计提、发放工资凭证；", "一键群发工资条；"],
        videoGuide: "130180120",
        jumpPage: "/Salary/SalaryManage",
        viewPermission: "salarymanage-canview",
        frequency: "month",
        pageTitle: "工资管理",
    },
};

const easyRecommendDialog = reactive<{
    display: boolean;
    dialogContent: IEasyRecommendationContent;
    callback: (r: boolean) => void;
}>({
    display: false,
    dialogContent: {} as IEasyRecommendationContent,
    callback: () => {},
});
const easyRecommendPromptStatus = ref(false);
const handleGoPage = (dialogContent: IEasyRecommendationContent) => {
    const accountSet = useAccountSetStore().accountSet;
    if ((dialogType.value === 300 && accountSet?.fixedasset === 0) || (dialogType.value === 100 && accountSet?.cashJournal === 0)) {
        ElConfirm(`您的账套未启用${dialogType.value === 300 ? "资产" : "资金"}模块，是否立即前往启用？`).then((r) => {
            if (r) {
                globalWindowOpenPage("/Settings/AccountSets?isEditModule=true", "账套");
            }
        });
        return;
    }
    if (checkPermission([dialogContent.viewPermission])) {
        closeEasyRecommendDialog();
        globalWindowOpenPage(dialogContent.jumpPage, dialogContent.pageTitle);
    } else {
        ElNotify({
            message: "暂无权限，请联系管理员进行添加",
            type: "warning",
        });
    }
};
const dialogType = ref(0);
const closeEasyRecommendDialog = () => {
    easyRecommendDialog.display = false;
    if (easyRecommendPromptStatus.value) {
        request({
            url: "/api/PaidConversion/CloseRecommend?module=" + dialogType.value,
            method: "post",
        });
    }
};
const setDialog = (pid: number, vid: number) => {
    if (needCheckEasyRecommend.value) {
        request({
            url: "/api/PaidConversion/CheckIsPromptRecommend",
            method: "get",
            params: {
                pid,
                vid,
            },
        }).then((res: IResponseModel<{ isRecommend: boolean; recommendModule: number }>) => {
            if (res.state === 1000) {
                easyRecommendDialog.display = res.data.isRecommend;
                dialogType.value = res.data.recommendModule;
                easyRecommendDialog.dialogContent = easyRecommendationList[dialogType.value];
                easyRecommendPromptStatus.value = false;
            }
        });
    }
};
const gotoStudy = () => {
    globalWindowOpen("https://help.ningmengyun.com/#/jz/videoPlayer?qType=" + easyRecommendDialog.dialogContent.videoGuide);
};
defineExpose({
    setDialog,
});
</script>
<style scoped lang="less">
.el-dialog.custom-confirm.easy-recommend {
    .easy-recommend-container {
        width: 100%;
        height: auto;
        border-radius: 10px;
        position: relative;
        .recommend-title {
            background-image: url("@/assets/ExpiredDialgo/trial7-dialog-bg.png");
            background-repeat: no-repeat;
            background-size: 100%;
            margin: -12px -13px 0px -12px;
            height: 140px;
            display: flex;
            align-items: center;
            justify-content: center;
            & div {
                font-size: 24px;
                font-weight: 600;
                color: #735524;
                line-height: 16px;
                line-height: 20px;
                height: 40px;
            }
        }
        p {
            padding: 10px 30px 0;
            line-height: 30px;
            font-size: 16px;
            margin: 0;
        }

        .video-guide {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            height: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            line-height: 20px;
        }
        .btns {
            height: 86px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            .recommend-button {
                width: 170px;
                height: 48px;
                text-align: center;
                line-height: 48px;
                align-self: center;
                font-size: 18px;
                border-radius: 24px;
                cursor: pointer;
                display: inline-block;
                border: 1px solid var(--border-color);
                color: #8a8989;
                &.solid-button {
                    font-weight: 600;
                    color: #6c4b15;
                    background: linear-gradient(177deg, #fff7e8 0%, #f4dbb2 100%);
                    border: none;
                }
            }
        }
        .no-more-prompt {
            display: flex;
            width: 100%;
            height: 44px;
            justify-content: center;
            line-height: 30px;
            .el-checkbox__label {
                font-size: 15px;
                color: gray;
            }
        }
    }
}
</style>
<style lang="less">
.el-dialog.custom-confirm.easy-recommend {
    border-radius: 12px;
    .el-dialog__header {
        border: none;
    }
    .el-dialog__body {
        overflow: hidden;
    }
}
</style>
