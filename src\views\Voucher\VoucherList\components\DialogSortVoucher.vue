<template>
    <el-dialog v-model="display" top="10vh" center width="1002" title="凭证号排序" @close="preResetDialogInfo" @closed="resetDialogInfo" class="dialogDrag">
        <div class="about-voucher-content" v-dialogDrag>
            <div class="about-voucher-main">
                <div class="voucher-main-top">
                    <div class="left">点击拖拽可快速调整凭证顺序，调整后将按照凭证顺序重新整理凭证号</div>
                    <div class="right">
                        <el-input v-model="searchText" placeholder="请输入凭证号定位" />
                        <a class="button solid-button position" @click="handlePosition">定位</a>
                        <a class="button solid-button ml-20 expend" @click="handleExpendAll">{{ allStatus ? "收起" : "展开" }}</a>
                    </div>
                </div>
                <div class="voucher-main-center">
                    <div class="table-title">
                        <div class="title-cell-item date">凭证日期</div>
                        <div class="title-cell-item num">凭证字号</div>
                        <div class="title-cell-item desc">摘要</div>
                        <div class="title-cell-item asub">科目</div>
                        <div class="title-cell-item debit">借方金额</div>
                        <div class="title-cell-item credit">贷方金额</div>
                        <div class="title-cell-item create">制单人</div>
                        <div class="title-cell-item check">审核人</div>
                        <div class="title-cell-item operate">操作</div>
                    </div>
                    <div ref="toolTipRef" v-show="visible" class="tool-tip-dom" :style="{ top: tipTop + 'px', left: tipLeft + 'px' }">
                        {{ content }}
                    </div>
                    <el-scrollbar
                        height="300px"
                        ref="scrollBarRef"
                        v-loading="loading"
                        element-loading-text="凭证数据加载中..."
                        @scroll="handleScroll"
                    >
                        <Container orientation="vertical" drag-class="card-ghost" :animation-duration="100" @drop="handleDrop">
                            <Draggable v-for="(item, index) in tableData" :key="item.vid">
                                <div
                                    v-if="!useVirtualScroll || (index > startIndex - pageSize && index < startIndex + pageSize * 2)"
                                    class="table-item"
                                    :class="{ highLight: item.highLight }"
                                    :style="{ height: item.height + 'px' }"
                                >
                                    <div class="table-cell-item pl-5 date">
                                        <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                            {{ formatDate(item.vdate) }}
                                        </div>
                                    </div>
                                    <div class="table-cell-item pl-5 num">
                                        <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                            {{ item.vgName + "-" + item.vnum }}
                                        </div>
                                    </div>
                                    <div class="table-cell-item desc" :class="{ expend: item.expend }">
                                        <div
                                            class="pl-5"
                                            v-show="item.expend || lineIndex < 2"
                                            v-for="(voucherLine, lineIndex) in item.voucherLines"
                                            :key="lineIndex"
                                        >
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                {{ voucherLine.description }}
                                            </div>
                                        </div>
                                        <div v-show="item.expend" class="pl-5 f-700">
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                合计：{{ digitUppercase(calcTotalMoney(item.voucherLines, "debit")) }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-cell-item asub" :class="{ expend: item.expend }">
                                        <div
                                            class="pl-5"
                                            v-show="item.expend || lineIndex < 2"
                                            v-for="(voucherLine, lineIndex) in item.voucherLines"
                                            :key="lineIndex"
                                        >
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                {{ voucherLine.asubName }}
                                            </div>
                                        </div>
                                        <div v-show="item.expend" class="pl-5"></div>
                                    </div>
                                    <div class="table-cell-item debit" :class="{ expend: item.expend }">
                                        <div
                                            class="pl-5"
                                            v-show="item.expend || lineIndex < 2"
                                            v-for="(voucherLine, lineIndex) in item.voucherLines"
                                            :key="lineIndex"
                                        >
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                {{ formatMoney(voucherLine.debit) }}
                                            </div>
                                        </div>
                                        <div v-show="item.expend" class="pl-5">
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                {{ formatMoney(calcTotalMoney(item.voucherLines, "debit")) }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-cell-item credit" :class="{ expend: item.expend }">
                                        <div
                                            class="pl-5"
                                            v-show="item.expend || lineIndex < 2"
                                            v-for="(voucherLine, lineIndex) in item.voucherLines"
                                            :key="lineIndex"
                                        >
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                {{ formatMoney(voucherLine.credit) }}
                                            </div>
                                        </div>
                                        <div v-show="item.expend" class="pl-5">
                                            <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                                {{ formatMoney(calcTotalMoney(item.voucherLines, "credit")) }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-cell-item pl-5 create">
                                        <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                            {{ item.preparedBy }}
                                        </div>
                                    </div>
                                    <div class="table-cell-item pl-5 check">
                                        <div class="text-overflow" @mouseenter="handleCheckOverflow" @mouseleave="hideTooltip">
                                            {{ item.approvedBy }}
                                        </div>
                                    </div>
                                    <div class="table-cell-item pl-5 operate">
                                        <span class="link" @click="handleExpend(item)">{{ item.expend ? "收起" : "展开" }}</span>
                                    </div>
                                </div>
                                <div v-else class="table-item virtual" :style="{ height: item.height + 'px' }"></div>
                            </Draggable>
                        </Container>
                    </el-scrollbar>
                </div>
                <div class="voucher-main-bottom">
                    <span>此操作不可逆！可先到</span>
                    <span :class="{ link: hasPermission() }" @click="changeCurrentRoute">{{ backupPathName }}</span>
                    <span>中备份账套，再进行操作</span>
                </div>
            </div>
            <div class="buttons bt">
                <a class="button solid-button" @click="handleSave">确定</a>
                <a class="button" @click="display = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, nextTick, onActivated, ref } from "vue";
import { formatMoney, digitUppercase } from "@/util/format";
import { dayjs } from "element-plus";
import { ElNotify } from "@/util/notify";
import { ElScrollbar } from "element-plus";
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage } from "@/util/url";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { request, type IResponseModel } from "@/util/service";

import type { IVoucherModel, IVoucherLine } from "@/views/Voucher/VoucherList/types";

import { Container, Draggable } from "vue3-smooth-dnd";

const toolTipRef = ref<HTMLDivElement>();
const content = ref("");
const tipTop = ref(0);
const tipLeft = ref(0);
const visible = ref(false);
function handleCheckOverflow(event: MouseEvent) {
    const dom = event.target as HTMLElement;
    if (!dom) {
        visible.value = false;
        return;
    }
    const overflow = dom.offsetWidth < dom.scrollWidth;
    if (!overflow) {
        visible.value = false;
        return;
    }
    content.value = dom.innerText;
    const { top = 0, left = 0, width = 0 } = dom.getBoundingClientRect();
    tipTop.value = top;
    tipLeft.value = left + width;
    visible.value = true;
}
function hideTooltip() {
    visible.value = false;
}

const voucherGroupList = computed(() => useVoucherGroupStore().voucherGroupList);
const vgIdMap = computed(() => {
    const map: Map<string, number> = new Map();
    voucherGroupList.value.forEach((item) => {
        map.set(item.name, item.id);
    });
    return map;
});

interface IVoucherItem extends IVoucherModel {
    expend: boolean;
    highLight: boolean;
    height: number;
}
const display = ref(false);
const tableData = ref<Array<IVoucherItem>>([]);
let pid = 0;
function resetDialogInfo() {
    useVirtualScroll.value = false;
    tableData.value.length = 0;
    pid = 0;
    cacheScrollTop = 0;
    searchText.value = "";
    resetPositionInfo();
    allStatus.value = false;
}
function preResetDialogInfo() {
    scrollBarRef.value?.setScrollTop(0);
}
const emit = defineEmits<{
    (e: "load-data"): void;
}>();
let isSaving = false;
function handleSave() {
    if (isSaving) return;
    isSaving = true;
    const params = tableData.value.map((item) => {
        return {
            vid: item.vid,
            vnum: item.vnum,
            vgId: vgIdMap.value.get(item.vgName),
        };
    });
    request({ url: "/api/Voucher/VoucherReNum?pid=" + pid, method: "post", data: params })
        .then((res: IResponseModel<boolean>) => {
            isSaving = false;
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "排序失败" });
                return;
            }
            ElNotify({ type: "success", message: "排序成功" });
            display.value = false;
            emit("load-data");
        })
        .catch(() => {
            isSaving = false;
            ElNotify({ type: "warning", message: "排序失败" });
        });
}
function handleExpend(item: IVoucherItem) {
    item.expend = !item.expend;
    item.height = calcTableItemHeight(item.voucherLines.length, item.expend);
    allStatus.value = tableData.value.every((item) => item.expend);
}
const allStatus = ref(false);
function handleExpendAll() {
    allStatus.value = !allStatus.value;
    tableData.value.forEach((item) => {
        item.expend = allStatus.value;
        item.height = calcTableItemHeight(item.voucherLines.length, allStatus.value);
    });
}
const searchText = ref("");
let lastSearchText = "";
let positionList: Array<{ item: IVoucherItem; index: number }> = [];
let lastPosition: IVoucherItem | null = null;
let lastPositionIndex = -1;
const scrollBarRef = ref<InstanceType<typeof ElScrollbar>>();
function resetPositionInfo() {
    positionList = [];
    lastPosition && (lastPosition.highLight = false);
    lastPosition = null;
    lastSearchText = "";
    lastPositionIndex = -1;
}
function handlePosition() {
    const text = searchText.value.trim();
    if (!text) {
        resetPositionInfo();
        return;
    }
    if (!/^[0-9]\d*$/g.test(text)) {
        ElNotify({ type: "warning", message: "亲，请输入数字哦~" });
        resetPositionInfo();
        return;
    }
    if (!tableData.value.some((item) => item.vnum === ~~text)) {
        ElNotify({ type: "warning", message: "亲，" + ~~text + "号凭证不存在，请确认后重新定位哦~" });
        resetPositionInfo();
        return;
    }
    if (lastSearchText === text) {
        if (lastPosition) {
            lastPosition.highLight = false;
            lastPositionIndex = lastPositionIndex + 1 >= positionList.length ? 0 : lastPositionIndex + 1;
            lastPosition = positionList[lastPositionIndex].item;
            lastPosition.highLight = true;
            calcTransFormY(positionList[lastPositionIndex].index);
        }
        return;
    }
    resetPositionInfo();
    const position: Array<{ item: IVoucherItem; index: number }> = [];
    for (let i = 0; i < tableData.value.length; i++) {
        const item = tableData.value[i];
        if (item.vnum === ~~text) {
            position.push({ item, index: i });
        }
    }
    lastSearchText = text;
    if (!position.length) return;
    positionList = position;
    lastPositionIndex = 0;
    lastPosition = positionList[0].item;
    lastPosition.highLight = true;
    calcTransFormY(positionList[0].index);
}
function calcTransFormY(index: number) {
    let height = 0;
    for (let i = 0; i < index; i++) {
        height += tableData.value[i].height;
    }
    height += index;
    scrollBarRef.value?.setScrollTop(height);
}

interface IDragResult {
    removedIndex: number | null;
    addedIndex: number | null;
    payload: any;
}
function handleDrop(dragResult: IDragResult) {
    const { removedIndex, addedIndex } = dragResult;
    if (removedIndex === null || addedIndex === null || removedIndex === addedIndex) return;
    const targetVgName = tableData.value[addedIndex].vgName;
    const originVgName = tableData.value[removedIndex].vgName;
    if (originVgName === targetVgName) {
        const result = [...tableData.value];
        const itemToAdd = result.splice(removedIndex, 1)[0];
        result.splice(addedIndex, 0, itemToAdd);
        tableData.value = result;
        return;
    }
    ElNotify({ type: "warning", message: `凭证字为“${originVgName}”的凭证不可以拖动到凭证字为“${targetVgName}”的区域哦~` });
    const direction = removedIndex < addedIndex ? "down" : "up";
    const temp = tableData.value[removedIndex];
    if (direction === "down") {
        const lastItem = tableData.value.slice(0, addedIndex).findLast((item) => item.vgName === originVgName);
        const insertIndex = tableData.value.indexOf(lastItem as any);
        tableData.value.splice(insertIndex + 1, 0, temp);
        tableData.value.splice(removedIndex, 1);
        nextTick().then(() => {
            calcTransFormY(insertIndex);
        });
    } else {
        const insertIndex = tableData.value.findIndex((item) => item.vgName === originVgName);
        tableData.value.splice(removedIndex, 1);
        tableData.value.splice(insertIndex, 0, temp);
        nextTick().then(() => {
            calcTransFormY(insertIndex);
        });
    }
}

const defaultLineHeight = 37;
function calcTableItemHeight(length: number, expend: boolean) {
    if (!expend) return defaultLineHeight * 2;
    return defaultLineHeight * (length + 1);
}
function calcTotalMoney(voucherLines: Array<IVoucherLine>, type: "debit" | "credit") {
    const result: number = voucherLines.reduce((prev, cur) => {
        prev += cur[type] - 0;
        return prev;
    }, 0);
    return result;
}
function formatDate(date: string) {
    return dayjs(date).format("YYYY-MM-DD");
}
function changeCurrentRoute() {
    if (!hasPermission()) return;
    globalWindowOpenPage(window.isErp ? "/backup" : "/Settings/Backup", "备份恢复");
}
function hasPermission() {
    if (window.isAccountingAgent) return false;
    return checkPermission(["backup-canview"]);
}
const backupPathName = computed(() => {
    if (window.isErp) return "系统设置-备份与恢复";
    if (window.isAccountingAgent) return "代账-记账平台";
    return "设置-备份恢复";
});

let useVirtualScroll = ref(false);
const minVirtualLength = 50;
const startIndex = ref(0);
const pageSize = computed(() => 10);
let cacheScrollTop = 0;
function handleScroll(event: { scrollLeft: number; scrollTop: number }) {
    hideTooltip();
    if (!useVirtualScroll.value) return;
    const { scrollTop } = event;
    cacheScrollTop = scrollTop;
    let height = 0;
    for (let i = 0; i < tableData.value.length; i++) {
        const currentHeight = tableData.value[i].height + 1;
        if (height <= scrollTop && height + currentHeight > scrollTop) {
            startIndex.value = i;
            break;
        }
        height += currentHeight;
    }
}

onActivated(() => {
    cacheScrollTop && scrollBarRef.value?.setScrollTop(cacheScrollTop);
});

interface IVouchersBack {
    count: number;
    data: Array<IVoucherModel>;
}
const loading = ref(false);
function handleOpenDialog(currentPid: number) {
    if (display.value) return;
    display.value = true;
    pid = currentPid;
    if (loading.value) return;
    loading.value = true;
    request({ url: "/api/Voucher/VouchersByPID?pid=" + pid, method: "post" })
        .then((res: IResponseModel<IVouchersBack>) => {
            loading.value = false;
            useVirtualScroll.value = res.data.count > minVirtualLength;
            tableData.value = res.data.data.map((item) => {
                return {
                    ...item,
                    expend: false,
                    highLight: false,
                    height: calcTableItemHeight(item.voucherLines.length, false),
                };
            });
        })
        .catch(() => {
            loading.value = false;
        });
}
defineExpose({ handleOpenDialog });
</script>

<style lang="less" scoped>
.about-voucher-content {
    .buttons.bt {
        border-top: 1px solid var(--border-color);
        .solid-button {
            margin-right: 50px;
        }
    }
    .about-voucher-main {
        padding: 10px 25px 10px;
        .title-cell-item,
        .table-cell-item {
            box-sizing: border-box;
            &.date {
                width: 100px;
            }
            &.num,
            &.debit,
            &.credit {
                width: 80px;
            }
            &.desc,
            &.asub {
                width: 210px;
            }
            &.create {
                width: 70px;
            }
            &.operate,
            &.check {
                width: 60px;
            }
        }
        .table-title {
            border: 1px solid var(--border-color);
            display: inline-flex;
            align-items: center;
            background-color: #f0f7f0;
            height: 40px;
            border-bottom: none;
            position: relative;
            &::after {
                width: 100%;
                height: 1px;
                background-color: var(--border-color);
                content: "";
                position: absolute;
                left: 0;
                bottom: 0;
            }
            .title-cell-item {
                height: 100%;
                line-height: 40px;
                border-right: 1px solid var(--border-color);
                padding: 0 5px;
                font-weight: 500;
                &.operate {
                    border-right: none;
                }
                &.debit,
                &.credit {
                    text-align: right;
                }
            }
        }
        .table-item {
            border-bottom: 1px solid var(--border-color);
            border-top: none;
            display: flex;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
            .pl-5 {
                padding: 0 5px;
            }
            &.highLight {
                .table-cell-item.num {
                    background-color: #fffdf0;
                    color: var(--red);
                }
            }
            .table-cell-item {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                border-right: 1px solid var(--border-color);
                overflow: hidden;
                font-weight: 400;
                .f-700 {
                    font-weight: 700;
                }
                &.operate {
                    border-right: none;
                    flex-direction: row;
                    align-items: center;
                    justify-content: flex-start;
                }
                &.debit,
                &.credit {
                    text-align: right;
                }
                & > div {
                    box-sizing: border-box;
                    height: 37px;
                    line-height: 37px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 100%;
                    white-space: nowrap;
                    font-weight: 400;
                }
                &.asub,
                &.desc,
                &.debit,
                &.credit {
                    > div {
                        border-bottom: 1px solid var(--border-color);
                    }
                }
                .text-overflow {
                    overflow: hidden;
                    width: 100%;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                &.expend {
                    & > div {
                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }

                &:not(.expend) {
                    & > div {
                        &:nth-child(2) {
                            border-bottom: none;
                        }
                    }
                }
            }
            &:first-child {
                border-bottom: 1px solid var(--border-color);
            }
        }
        :deep(.el-scrollbar) {
            position: relative;
            border-left: 1px solid var(--border-color);
            border-right: 1px solid var(--border-color);
            &::after,
            &::before {
                content: "";
                width: 100%;
                height: 1px;
                background-color: var(--border-color);
                position: absolute;
                left: 0;
            }
            &::after {
                bottom: 0;
            }
            &::before {
                top: 0;
                height: 0;
            }

            .smooth-dnd-draggable-wrapper {
                &:hover {
                    background-color: #f0f7f0;
                }
            }
            .card-ghost {
                border: 1px solid var(--border-color);
                background-color: #f0f7f0;
            }

            .smooth-dnd-container:has(.card-ghost) .smooth-dnd-draggable-wrapper {
                &:hover {
                    background-color: var(--white);
                }
            }
        }
    }
    .voucher-main-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            :deep(.el-input) {
                width: 175px;
                height: 28px;
                .el-input__wrapper {
                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                }
            }
            .solid-button {
                height: 28px;
                line-height: 28px;
            }
            .position {
                width: auto;
                padding: 0 10px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
            .expend {
                width: 75px;
            }
        }
    }
    .voucher-main-bottom {
        margin-top: 10px;
    }
}
.tool-tip-dom {
    max-width: 300px;
    position: fixed;
    z-index: 9999;
    background-color: var(--white);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    border-radius: 2px;
    word-break: break-all;
}
</style>
