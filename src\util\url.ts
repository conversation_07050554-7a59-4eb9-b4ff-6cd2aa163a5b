import { useAccountPeriodStoreHook } from "@/store/modules/accountPeriod";
import { useCorsagentIframeStoreHook } from "@/store/modules/corsagent";
import { isInWxWork } from "@/util/wxwork";
import { getAACompanyId, getGlobalToken, getServiceID } from "./baseInfo";
import { getClientVersion, getLemonClient, isLemonClient } from "./lmClient";
import { erpCloseTab, erpCreateTab, erpOpenWindow, erpSetTopLocationhref } from "./erpUtils";
import router from "@/router";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useUserStoreHook } from "@/store/modules/user";
import { ElNotify } from "./notify";
import { request } from "./service";
import { cloneDeep } from "lodash";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "./thirdpart";
import { useLoading } from "@/hooks/useLoading";
import { constantRoutes } from "@/router";

const corsagentIframeStore = useCorsagentIframeStoreHook();
const accountPeriodStore = useAccountPeriodStoreHook();
const routerArrayStore = useRouterArrayStoreHook();

export const appendAACompanyId = (url: string) => {
    if (window.isAccountingAgent) {
        let hash = "";
        if (url.indexOf("#") != -1) {
            hash = url.substring(url.indexOf("#"));
            url = url.substring(0, url.indexOf("#"));
        }
        if (url.indexOf("AACompanyId") != -1) {
            return url + hash;
        }
        const aaCompanyId = getAACompanyId();
        if (url.indexOf("?") != -1) {
            url += "&AACompanyId=" + aaCompanyId;
        } else {
            url += "?AACompanyId=" + aaCompanyId;
        }
        return url + hash;
    }
    return url;
};

export const appendAppasid = (url: string, addServiceID = true) => {
    if (url.indexOf("appasid") === -1) {
        if (url.indexOf("?") !== -1) {
            url += "&appasid=" + getGlobalToken();
        } else {
            url += "?appasid=" + getGlobalToken();
        }
    }
    return addServiceID ? appendServiceId(url) : url;
};
export const appendServiceId = (url: string) => {
    const serviceID = getServiceID();
    if (url.indexOf("serviceID") === -1 && serviceID) {
        if (url.indexOf("?") !== -1) {
            url += "&serviceID=" + serviceID;
        } else {
            url += "?serviceID=" + serviceID;
        }
    }
    return url;
};

export const setTopLocationhref = (href: string, erpSetCurrentLocation = false) => {
    if (!href.startsWith("http")) {
        href = appendAACompanyId(href);
    }
    if (isLemonClient()) {
        if (href.indexOf("http") !== 0) {
            href = window.location.origin + appendServiceId(href);
        }
        getLemonClient().setHref(href);
    } else if (window.isErp) {
        erpSetCurrentLocation ? erpSetTopLocationhref(href) : globalWindowOpen(href);
    } else {
        thirdPartNotify(thirtPartNotifyTypeEnum.globalHrefTo, { url: href }).then(() => {
            window.location.href = appendServiceId(href);
        });
    }
};

export const globalWindowOpen = (href: string) => {
    if (!href.startsWith("http")) {
        href = appendAACompanyId(href);
    }
    if (isLemonClient()) {
        if (href.indexOf("http") !== 0) {
            href = window.location.origin + href;
        }
        getLemonClient().openWindow(href);
    } else if (window.isErp) {
        erpOpenWindow(href);
    } else if (isInWxWork()) {
        setTopLocationhref(href);
    } else {
        thirdPartNotify(thirtPartNotifyTypeEnum.globalOpenWindow, { url: href }).then(() => {
            window.open(href);
        });
    }
};

export const globalWindowOpenPage = (href: string, title: string) => {
    if(title.trim() === ""){
        const route = constantRoutes.find((item) => item.path !== '/' && href.includes(item.path)) as any;
        title = route && route.meta.title;
    }
    if (window.isErp) {
        erpCreateTab(href, title);
        return;
    }
    const routerArrayStore = useRouterArrayStoreHook();
    if (
        routerArrayStore.routerArray.length >= 20 &&
        !routerArrayStore.routerArray.find((item) => item.path.toLowerCase() === href.toLowerCase().split("?")[0])
    ) {
        ElNotify({ message: "页签达到上限，可右键快速关闭其他页", type: "warning" });
        return;
    }
    thirdPartNotify(thirtPartNotifyTypeEnum.globalOpenWindow, {
        url: window.location.origin + appendAACompanyId(appendAppasid(href)),
    }).then(() => {
        router.push(appendAACompanyId(appendServiceId(appendAppasid(href)))).then(() => {
            document.title = title + " - 柠檬云财税";
            routerArrayStore.routerArray.find((item) => item.alive)!.title = title;
          });
    });
};

export const globalWindowOpenErp = (href: string, title: string) => {
    thirdPartNotify(thirtPartNotifyTypeEnum.globalOpenWindow, {
        url: window.location.origin + appendAACompanyId(appendAppasid(href)),
    }).then(() => {
        router.push(appendAACompanyId(appendAppasid(href)));
    });
}

// 打开第三方网页所使用的方法，比如税局网站
export const globalWindowOpenNoReferrer = (href: string) => {
    globalWindowOpen(window.location.origin + "/noReferrerTo.html?href=" + encodeURIComponent(href));
};

export const globalExport = (href: string) => {
    href = appendAACompanyId(href);
    href = appendAppasid(href, false);
    if (isLemonClient()) {
        if (href.indexOf("http") != 0) {
            href = window.jApiHost + href;
        }
        getLemonClient().openWindow(href);
        return;
    }
    if (href.startsWith(window.jAccountBooksHost) && !isInWxWork()) {
        window.open(appendServiceId(href));
        return;
    }
    if (href.indexOf("http") != 0) {
        href = window.jApiHost + href;
    }
    thirdPartNotify(thirtPartNotifyTypeEnum.globalDownload, { url: href }).then(() => {
        window.location.href = href;
    });
};

//后续下载模版可尝试都调整为这个方法
export const downloadFile = (href: string,method: string = 'get', data?: any) => {
    request({
        url: href,
        method,
        data,
        responseType: "blob",
    })
        .then((res: any) => {
            console.log(res);
            if (res.type === "text/plain") {
                ElNotify({ type: "warning", message: "出错了，请刷新页面重试或联系客服处理！"  });
            } else if (res.type === "application/json") {
                const reader = new FileReader();
                reader.readAsText(res, "utf-8");
                reader.addEventListener("loadend", function () {
                    if (reader.error) {
                        console.error("Error reading file:", reader.error);
                    } else {
                        if (typeof reader.result === "string") {
                            const resObj = JSON.parse(reader.result); 
                            ElNotify({ type: "warning", message: resObj.msg ??  "出错了，请刷新页面重试或联系客服处理！" });
                        }
                    }
                });
            } else {
                const name = res["content-disposition"]?.split(";")[1].split("=")[1];
                if (name) {
                    const fileName = decodeURIComponent(name).replace(new RegExp('"', 'g'), '');
                    const blob = new Blob([res], {
                        type: res.type,
                    });
                    const a = document.createElement("a");
                    const URL = window.URL || window.webkitURL;
                    const herf = URL.createObjectURL(blob);
                    a.href = herf;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(herf);
                } else {
                    ElNotify({ type: "warning", message: "出错了，请刷新页面重试或联系客服处理！" });
                }
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "出错了，请刷新页面重试或联系客服处理！" });
        })
        .finally(() => {
            useLoading().quitLoading();
        });
}

export const globalPrint = (href: string) => {
    href = appendAACompanyId(href);
    href = appendAppasid(href, false);
    if (isLemonClient()) {
        if (href.indexOf("http") != 0) {
            href = window.jApiHost + href;
        }
        getLemonClient().openWindow(href);
        return;
    }
    if (href.startsWith(window.jAccountBooksHost) || href.startsWith(window.printHost)) {
       href = appendServiceId(href);
    }
    if (href.indexOf("http") != 0) {
        href = window.jApiHost + href;
    }
    thirdPartNotify(thirtPartNotifyTypeEnum.globalPrint, { url: href }).then(() => {
        window.open(href);
    });
};

export const globalPostPrint = (href: string, data: Record<string, any>): Promise<void> => {
    return new Promise((resolve, reject) => {
        try {
            href = appendAACompanyId(href);
            href = appendAppasid(href);
            if (!href.startsWith('http')) {
                href = window.jApiHost + href;
            }

            if (isLemonClient()) {
                const lemonData = cloneDeep(data);
                for (const key in lemonData) {
                    if (Object.prototype.hasOwnProperty.call(lemonData, key)) {
                        if (typeof lemonData[key] === "object") {
                            lemonData[key] = JSON.stringify(lemonData[key]);
                        }
                    }
                }
                getLemonClient().openPostWindow(href, lemonData, "打印");
                resolve();
                return;
            }
            if(isInWxWork()){
                const form = document.createElement("form");
                form.action = href;
                form.method = "post";
                form.target = "_blank";
                
                for (const key in data) {
                    if (Object.prototype.hasOwnProperty.call(data, key)) {
                        const input = document.createElement("input");
                        input.type = "hidden";
                        input.name = key;
                        input.value = typeof data[key] === "object" ? JSON.stringify(data[key]) : String(data[key]);
                        form.appendChild(input);
                    }
                }
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
                return
            }

            const formId = `print_form_${Date.now()}`;
            const form = document.createElement("form");
            form.setAttribute('id', formId);
            form.setAttribute('method', 'POST');
            form.setAttribute('action', href);
            form.style.display = 'none';

            Object.entries(data).forEach(([key, value]) => {
                const input = document.createElement("input");
                input.type = "hidden";
                input.name = key;
                input.value = typeof value === "object" ? JSON.stringify(value) : String(value);
                form.appendChild(input);
            });

            const cleanup = () => {
                const oldForm = document.getElementById(formId);
                if (oldForm) {
                    document.body.removeChild(oldForm);
                }
            };

            // 创建打印窗口并设置目标
            const target = `print_window_${Date.now()}`;
            const printWindow = window.open('about:blank', target);
            
            if (!printWindow) {
                cleanup();
                reject(new Error('无法创建打印窗口'));
                return;
            }

            // 设置表单目标为新窗口
            form.setAttribute('target', target);
            
            const checkWindowInterval = setInterval(() => {
                if (printWindow.closed) {
                    clearInterval(checkWindowInterval);
                    cleanup();
                    resolve();
                }
            }, 1000);

            document.body.appendChild(form);
            form.submit();

            setTimeout(cleanup, 100);

        } catch (error) {
            reject(error);
            console.error('打印失败:', error);
            ElNotify({ 
                type: 'error', 
                message: '打印失败，请重试' 
            });
        }
    });
};

export const globalDirectPrint = (href: string, method: string = "get", data: any = {}, loadingTxt: string = "凭证打印中，请稍候...") => {
    href = appendAACompanyId(href);
    href = appendAppasid(href);
    href = appendServiceId(href);
    if (href.indexOf("http") != 0) {
        href = window.jApiHost + href;
    }
    if (isLemonClient() && method === "get") {
        getLemonClient().openWindow(href);
        return;
    }
    if (isLemonClient() && getClientVersion() >= 505 && method === "post") {
        getLemonClient().openPostWindow(href, data, "打印");
        return;
    }
    useLoading().enterLoading(loadingTxt);
    thirdPartNotify(thirtPartNotifyTypeEnum.globalPrint, { url: href }).then(() => {
        request({
            url: href,
            method: method,
            responseType: "blob",
            data: data,
        })
            .then((res: any) => {
                if (res.type === "application/json") {
                    const reader = new FileReader();
                    reader.readAsText(res, "utf-8");
                    reader.addEventListener("loadend", function () {
                        if (reader.error) {
                            console.error("Error reading file:", reader.error);
                        } else {
                            if (typeof reader.result === "string") {
                                const res = JSON.parse(reader.result); // 返回的数据
                                if(res.state === 1000 && res.subState === 8){
                                    globalWindowOpen(res.message);
                                }else{
                                    ElNotify({ type: "warning", message: res.message ?? res.message ?? "出错了，请刷新页面重试或联系客服处理！" });
                                }
                            }
                        }
                    });
                    return;
                }
                const blob = new Blob([res], {
                    // 设置返回的文件类型
                    type: res.type,
                });
                const blobUrl = URL.createObjectURL(blob);
                const iframe = document.createElement("iframe");
                iframe.className = "tmp-pdf";
                iframe.style.display = "none";
                // const conent = new Blob([res], {type: 'application/pdf'})
                iframe.src = URL.createObjectURL(blob);
                document.body.appendChild(iframe);
                setTimeout(function () {
                    iframe.contentWindow!.print();
                    URL.revokeObjectURL(blobUrl);
                }, 100);
                // const blobUrl = URL.createObjectURL(blob);
                // // 创建新窗口
                // const newWindow = window.open(blobUrl);
                //     if(newWindow){
                //     // 等待内容加载完成后再打印
                //     newWindow.onload = function () {
                //         newWindow.print();
                //     };
                //     // 当窗口关闭时释放 Blob URL
                //     newWindow.onbeforeunload = function () {
                //         URL.revokeObjectURL(blobUrl);
                //     };
                // }else{
                //     ElNotify({ type: "warning", message: "打印失败" });
                // }
            })
            .finally(() => {
                useLoading().quitLoading();
            });
    });
};

const getMaxRequestLength = () => {
    // 获取用户代理字符串
    const userAgent = navigator.userAgent;
    // 判断浏览器类型
    let maxUrlLength = 0;
    if (userAgent.indexOf("Opera") !== -1 || userAgent.indexOf("OPR") !== -1) {
        maxUrlLength = 2083;
    } else if (userAgent.indexOf("Firefox") !== -1) {
        maxUrlLength = 2083;
    } else if (userAgent.indexOf("Safari") !== -1) {
        maxUrlLength = 2083;
    } else if (userAgent.indexOf("Edge") !== -1) {
        maxUrlLength = 2083;
    } else if (userAgent.indexOf("Trident") !== -1) {
        maxUrlLength = 2048;
    } else if (userAgent.indexOf("Chrome") !== -1) {
        maxUrlLength = 2083;
    } else {
        maxUrlLength = 2083;
    }

    // 客户端要减去token的长度
    if (userAgent.indexOf("LemonClient(Acc)") !== -1) {
        maxUrlLength -= 960;
    }

    return maxUrlLength;
};

export function globalFormPost(href: string, params: any, type: "print" | "export" = "print") {
    if (!href.startsWith("http")) {
        const appasidParam = "appasid=" + getGlobalToken();
        href = href.startsWith("/api/") ? window.jApiHost + href : window.jHost + href;
        if (href.indexOf("appasid") === -1) {
            if (href.indexOf("?") !== -1) {
                href += "&" + appasidParam;
            } else {
                href += "?" + appasidParam;
            }
        }
    }

    let url = href.indexOf("?") !== -1 ? href + "&" + getUrlSearchParams(params) : href + "?" + getUrlSearchParams(params);
    if(url.includes(window.printHost)){
        url = appendServiceId(url);
    }

    if (url.length < getMaxRequestLength()) {
        type === "export" ? globalExport(url) : globalPrint(url);
        return;
    } else if (isLemonClient() && getClientVersion() >= 505) {
        getLemonClient().openPostWindow(href, params, type === "export" ? "导出" : "打印");
        return;
    } else {
        const form = document.createElement("form");
        form.action = href;
        form.method = "post";
        form.target = type === "export" ? "_self" : "_blank";
        for (const key in params) {
            if (Object.prototype.hasOwnProperty.call(params, key)) {
                const input = document.createElement("input");
                input.type = "hidden";
                input.name = key;
                input.value = params[key];
                form.appendChild(input);
            }
        }
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        return;
    }
}

export function closeCurrentTab() {
    thirdPartNotify(thirtPartNotifyTypeEnum.globalCloseWindow).then(() => {
        closeTab(routerArrayStore.routerArray.find((item) => item.alive)!.path);
    });
}

export function closeTab(path: string) {
    if (window.isErp) {
        erpCloseTab(window.location.pathname, document.title);
    } else {
        routerArrayStore.removeRouter(path);
    }
}
export function closeWindow() {
    thirdPartNotify(thirtPartNotifyTypeEnum.globalCloseWindow).then(() => {
        window.close();
    });
}

export function reloadPeriodInfo() {
    if (window.isErp) return;
    accountPeriodStore.getPeriods();
    thirdPartNotify(thirtPartNotifyTypeEnum.globalReloadAccountSetInfo).then(() => {});
}

export function globalLoadMenu() {
    if (window.isErp) return;
    if (isLemonClient()) {
        getLemonClient().loadMenu();
    } else {
        window.postMessage({ type: "loadMenu" }, window.location.origin);
        thirdPartNotify(thirtPartNotifyTypeEnum.globalReloadMenu).then(() => {});
    }
}

export function loadTopUserName() {
    if (window.isErp) return;
    if (isLemonClient()) {
        getLemonClient().loadTopUserName();
    }
    const userStore = useUserStoreHook();
    userStore.getUserName();
}

export const getUrlSearchParams = (params: any) => {
    const searchParams = new URLSearchParams();
    for (const key in params) {
        if (Object.prototype.hasOwnProperty.call(params, key)) {
            searchParams.append(key, params[key]);
        }
    }
    return searchParams.toString();
};

export const sendAppMessage = (title: string, msg: string, type?: string) => {
    corsagentIframeStore.pushNoticeUrl(
        "fn=appMessage&t=" +
            encodeURIComponent(encodeURIComponent(title)) +
            "&msg=" +
            encodeURIComponent(encodeURIComponent(msg)) +
            "&type=" +
            (type || "warning") +
            "&noReferrer=1&r=" +
            Math.random()
    );
};

export const logout = () => {
    request({
        url: window.accountSrvHost + "/api/Logout.ashx?CurrentSystemType=1",
        method: "get",
    }).then((res: any) => {
        if (res.result === "True") {
            thirdPartNotify(thirtPartNotifyTypeEnum.globalLogout).then(() => {
                setTopLocationhref(window.wwwHost + (isInWxWork() ? "/WxworkLandingpage.aspx" : ""));
            });
        }
    });
};

//目前仅用在凭证导出
export const globalExportNew = (url: string, method:string="get", data?:any) => {
    useLoading().enterLoading("凭证导出中，请稍候...");
    request({
        url: url,
        method,
        data,
        responseType: "blob",
    })
        .then((res: any) => {
            if (res.type === "application/json") {
                const reader = new FileReader();
                    reader.readAsText(res, "utf-8");
                    reader.addEventListener("loadend", function () {
                        if (reader.error) {
                            console.error("Error reading file:", reader.error);
                        } else {
                            if (typeof reader.result === "string") {
                                const res = JSON.parse(reader.result); 
                                if(res.State === 1000 && res.SubState === 0){
                                    globalWindowOpen(res.Data);
                                }else if(res.State === 2000 && res.SubState === 1){
                                    ElNotify({ type: "warning", message: res.Message });
                                }
                            }
                        }
                    });
                    return;
            }
            const name = res["content-disposition"]?.split(";")[1].split("=")[1];
            if (name) {
                const fileName = decodeURIComponent(name).replace(new RegExp('"', 'g'), '');
                const blob = new Blob([res], {
                    // 设置返回的文件类型
                    type: res.type,
                });
                const a = document.createElement("a");
                const URL = window.URL || window.webkitURL;
                const herf = URL.createObjectURL(blob);
                a.href = herf;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(herf);
                ElNotify({ type: "success", message: "导出成功" });
            } else {
                ElNotify({ type: "warning", message: "导出失败" });
            }
            
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "导出失败" });
        })
        .finally(() => {
            useLoading().quitLoading();
        });
};

export const getUrlAllowParams = () => {
    const allowParams = ["appasid", "stay", "fetch", "isthirdPart"];
    if (window.isAccountingAgent) allowParams.push("AACompanyId");
    if (window.isErp) allowParams.push("jxcst");
    if (window.isProSystem) allowParams.push("serviceID");
    return allowParams;
};

const checkHasCustomUrlParams = (query: any) => {
    let checkHasCustomUrlParams = false;
    const allowParams = getUrlAllowParams();
    for (const key in query) {
        if (!allowParams.includes(key)) {
            checkHasCustomUrlParams = true;
            break;
        }
    }
    return checkHasCustomUrlParams;
};

export const tryClearCustomUrlParams = (route: any) => {
    if (!checkHasCustomUrlParams(route.query)) return;
    const allowParams = getUrlAllowParams();
    const params = { ...route.query };
    for (const key in params) {
        if (!allowParams.includes(key)) {
            delete params[key];
        }
    }
    const url = route.path + "?" + getUrlSearchParams(params);
    useRouterArrayStoreHook().replaceCurrentRouter(url);
    // 这个在业财预生产有问题  会导致跳出预生产
    window.isErp && erpSetTopLocationhref(window.erpHost + "/#" + route.path);
};
