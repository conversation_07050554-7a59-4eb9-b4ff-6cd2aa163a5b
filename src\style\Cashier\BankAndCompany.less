@import "../SelfAdaption.less";
@import "../Functions.less";
.content {
    position: static;


    .slot-content {
        .slot-title {
            border-bottom: none;
        }

        .back {
            position: absolute;
            left: 50px;
            top: 16px;
            display: flex;
            align-items: center;
            cursor: pointer;
            img {
                width: 9px;
                height: 13px;
                margin-right: 5px;
            }
            span {
                color: gray;
                font-size: 14px;
                line-height: 22px;
            }
        }

        :deep(.el-tabs) {
            width: 100%;
        }

        :deep(.el-tabs__header) {
            .el-tabs__nav-wrap::after {
                background-color: rgba(0, 0, 0, 0.08);
            }
            .el-tabs__nav-scroll {
                width: 1000px;
                margin: 0 auto;

                .el-tabs__nav {
                    margin-left: -20px;
                }
            }
        }
        :deep(.el-tabs__content) {
            background-color: var(--white);
            overflow: visible;
        }

        :deep(.slot-content-mini) {
            width: 1000px;
            border: 1px solid var(--slot-title-color);
            border-radius: 8px;
            margin-top: 32px;

            .autoBankInfo {
                align-self: center;
                font-size: 12px;
                text-align: center;
                color: #5d5d5d;
                padding-bottom: 40px;
                margin-top: 12px;
                position: relative;
                .consulting {
                    position: relative;
                    font-size: 12px;
                    img {
                        position: absolute;
                        bottom: -48px;
                        left: 100%;
                        display: none;
                        width: 200px;
                    }
                    &:hover {
                        img {
                            display: block;
                        }
                    }
                }
            }

            .report-img {
                display: flex;
                justify-content: center;
                align-self: center;
                margin-bottom: 46px;
                img {
                    width: 987px;
                }
            }
        }

        &.bankandcompany {
            :deep(.el-tabs__header) {
                .el-tabs__nav-scroll {
                    width: 1200px;
                }
            }

            :deep(.slot-content-mini) {
                padding: 0 100px;
                position: relative;
            }
        }
    }
}
.panel-body {
    div.autoBanktitle {
        position: absolute;
        right: 185px;
        top: 24px;
        &::before {
            width: 16px;
            height: 16px;
            content: "";
            position: absolute;
            top: 5px;
            left: -18px;
            background-image: url("@/assets/Icons/help.png");
        }
        a.link {
            font-size: 12px;
        }
    }
    div.top {
        height: 220px;
        margin-top: 70px;
        position: relative;
        font-family: PingFangHK-Regular;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &.more {
            height: 270px;
        }

        div.autoBankSelect {
            font-size: 14px;
            font-family: PingFangHK-Regular;
            font-weight: 400;
            color: #333333;
            line-height: 20px;
            float: left;
            width: 423px;
            margin-left: 90px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .auto-bank-form-item {
                margin-bottom: 12px;
                &.bank-account {
                    .detail-el-select(286px, 30px);

                    :deep(.new-cd-account) {
                        height: 32px;
                        line-height: 32px;
                    }
                    &.base {
                        :deep(.el-select) {
                            .el-input__inner {
                                color: gray;
                            }
                        }
                    }
                }
                &:last-child {
                    margin-bottom: 0;
                }
                :deep(.el-form-item__label) {
                    padding: 0;
                }
                :deep(.el-input) {
                    width: 286px;
                    height: 30px;
                    .el-input__wrapper {
                        padding: 1px 8px;
                    }
                }

                &.boc {
                    .help-tip {
                        position: absolute;
                        right: -20px;
                        width: 14px;
                        height: 14px;
                        cursor: pointer;
                        background-repeat: no-repeat;
                        background-size: 100%;
                        background-image: url("@/assets/Icons/help-tip.png");

                        &:hover {
                            background-image: url("@/assets/Icons/help-tip-hover.png");
                        }
                    }
                }
            }
        }
        div.autoBankDescription {
            width: 383px;
            height: 180px;
            background: #f8f8f8;
            border-radius: 2px;
            margin-right: 52px;
            text-align: left;
            p.textRed {
                font-size: 14px;
                font-weight: 400;
                color: #ff2741;
                line-height: 1px;
                padding-top: 30px;
                margin-left: 40px;
            }
            p.textNormal {
                font-weight: 400;
                color: #5d5d5d;
                text-align: left;
                line-height: 24px;
                margin-left: 40px;
                font-size: 12px;
            }
        }
    }
    .bottom {
        overflow: hidden;
        & > div {
            text-align: center;
            margin-bottom: 12px;
            a.link {
                font-size: 12px;
            }
        }
    }
}
.edit-temp-table {
    box-sizing: border-box;
    overflow: hidden;

    .slot-content-mini {
        padding-top: 50px !important;
    }
    div.autoBanktitle {
        position: absolute;
        right: 185px;
        top: 24px;
        &::before {
            width: 16px;
            height: 16px;
            content: "";
            position: absolute;
            top: 5px;
            left: -18px;
            background-image: url("@/assets/Icons/help.png");
        }
        a.link {
            font-size: 12px;
        }
    }
    .autoBankSelect {
        font-size: 14px;
        font-family: PingFangHK-Regular;
        font-weight: 400;
        color: #333333;
        line-height: 20px;
        float: left;
        margin-left: 90px;
        .autoBankItem {
            margin-bottom: 12px;
            text-align: left;
        }
    }
    .autoBankDescription {
        width: 383px;
        height: 180px;
        background: #f8f8f8;
        border-radius: 2px;
        margin-left: 555px;
        text-align: left;
        color: #404040;
        font-size: 12px;
        .copy-file {
            padding-left: 15px;
            background: url("@/assets/Icons/copy-file.png") no-repeat left center;
            background-size: 15px 15px;
            cursor: pointer;
            float: right;
            margin-right: 48px;
        }
        .pub-key {
            width: 300px;
            word-wrap: break-word;
            margin: 10px 40px 13px 40px;
            cursor: pointer;
            display: -webkit-box;
            overflow: hidden;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
    }
    .back-button {
        color: var(--main-color);
        background-color: white;
        border: 1px solid var(--main-color);
        height: 30px;
        line-height: 30px;
        width: 100px;
        margin-right: 20px;

        &:hover {
            color: var(--white);
            background-color: var(--main-color);
        }

        &:active {
            color: var(--white);
            background-color: var(--dark-main-color);
            border-color: var(--dark-main-color);
        }
    }
    #btnICBCConfirm {
        border: none;
        font-size: 13px;
        display: inline-block;
        cursor: pointer;
        text-align: center;
        padding: 0;
        outline: none;
        transition: var(--transition-time);
        color: var(--white);
        border-color: var(--main-color);
        background-color: var(--main-color);
        border-radius: 4px;
        height: 32px;
        width: 102px;
        line-height: 32px;
    }

    .authButton {
        text-align: center;
        margin-top: 20px;
        margin-bottom: 20px;
    }
}
.open-body {
    height: 420px;
    text-align: left;
    &.icbc {
        background-image: url("@/assets/Icons/icbc_open.png");
    }
    &.spdb {
        background-image: url("@/assets/Icons/spdb_open.png");
    }
    &.pab {
        background-image: url("@/assets/Icons/pab_open.png");
        background-size: cover;
    }
    .open-title {
        margin-left: 80px;
        height: 34px;
        padding-top: 60px;
        .open-icon {
            width: 206px;
            height: 34px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            img.lemon {
                margin-top: 5px;
                margin-left: 7px;
                margin-right: 4px;
                width: 24px;
                height: 22px;
                float: left;
            }
            div.open-ningmeng-text {
                font-size: 15px;
                font-weight: 600;
                color: #333333;
                float: left;
                padding-top: 8px;
            }
            div.open-ningmeng-text2 {
                float: left;
                color: #b7b7b7;
                font-size: 26px;
                font-weight: 400;
                margin-left: 4px;
            }
            > img.bank {
                margin-top: 7px;
                margin-left: 4px;
                width: 92px;
                height: 22px;
                float: left;
            }
            .spdb {
                display: inline-block;
                > img {
                    margin-top: 7px;
                    margin-left: 4px;
                    width: 27px;
                    height: 22px;
                    float: left;
                    margin-right: 4px;
                }
                > div {
                    float: left;
                    font-size: 15px;
                    font-weight: 600;
                    color: #020073;
                    padding-top: 8px;
                }
            }
            > .pab {
                margin-top: 12px;
                margin-left: 10px;
                width: 63px;
                height: 13px;
                float: left;
            }
        }
    }
    .openTextTitle {
        padding-top: 20px;
        padding-left: 80px;
        font-size: 25px;
        font-weight: 500;
        line-height: 50px;
        color: rgba(0, 0, 0, 0.85);
    }
    .openTextContent {
        padding-left: 80px;
        font-size: 15px;
        line-height: 28px;
        font-weight: 400;
        color: #808080;
    }
    .openTextTip {
        padding-left: 80px;
        font-size: 15px;
        line-height: 40px;
        font-weight: 400;
        color: #808080;
    }
    .openButton {
        padding-left: 80px;
        margin-top: 50px;
        display: flex;
        align-items: center;
        a.solid-button.large,
        a.button.large {
            width: 102px;
            height: 32px;
            line-height: 32px;
        }
        a.solid-button.orange {
            border: 1px solid #fd7400;
            background-color: #fd7400;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            &:hover {
                border-color: #fd9d4c;
                background-color: #fd9d4c;
            }
        }
        a.button.orange {
            color: #fd7400;
            border-color: #fd7400;
            text-decoration: none;
            font-size: 12px;
            &:hover {
                background-color: #fd9d4c;
                border-color: #fd9d4c;
                color: var(--white);
            }
        }
        a.solid-button.blue {
            border: 1px solid #0095ff;
            background-color: #0095ff;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            &:hover {
                border-color: #2f70ff;
                background-color: #2f70ff;
            }
        }
        a.button.blue {
            color: #0095ff;
            border-color: #0095ff;
            text-decoration: none;
            font-size: 12px;
            &:hover {
                background-color: #2f70ff;
                border-color: #2f70ff;
                color: var(--white);
            }
        }
    }
}

.autoBankSelect {
    :deep(.el-form) {
        .auto-bank-form-item {
            .el-form-item__content {
                .detail-placehoder-color(gray);
            }
        }
        .auto-bank-form-item.necessary {
            .el-form-item__label {
                &::before {
                    content: "*";
                    color: red;
                    margin-right: 3px;
                }
            }
        }
    }
}
.authButton {
    display: flex;
    align-items: center;
    justify-content: center;
}
