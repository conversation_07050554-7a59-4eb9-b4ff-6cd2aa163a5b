<template>
    <el-dialog
        v-model="visible"
        class="create-success-dialog custom-confirm no-split-line dialogDrag"
        center
        :width="isTrial ? '700px' : '800px'"
        style="border-radius: 12px; overflow: hidden"
    >
        <template #header>
            <p class="create-success-title">恭喜您，账套创建成功</p>
        </template>
        <div class="create-success-content" v-dialogDrag>
            <p class="create-success-tips">
                {{
                    isPro
                        ? "云财务专业版，支持随时修改账套信息；创建后还可领取惊喜福利"
                        : "柠檬云不限账套，支持随时修改账套信息；创建后还可领取惊喜福利"
                }}
                <img src="@/assets/Settings/gift.png" style="height: 20px; cursor: pointer; margin-left: 5px" />
            </p>
            <div :class="['create-success-main', { 'is-trial': isTrial }]">
                <div class="create-success-main-left" v-if="isTrial">
                    <p>6步快速体验产品:</p>
                    <ul class="step-list">
                        <li><span>1</span> 快速初始化</li>
                        <li><span>2</span> 发票智能化</li>
                        <li><span>3</span> 资金智能化</li>
                        <li><span>4</span> 资产全面化</li>
                        <li><span>5</span> 凭证智能化</li>
                        <li><span>6</span> 档案电子化</li>
                    </ul>
                    <a
                        class="button solid-button mt-10"
                        @click="
                            visible = false;
                            globalWindowOpenPage('/BeginnersGuide', '新手指引');
                        "
                        >立即体验</a
                    >
                </div>
                <div class="create-success-main-left" v-else>
                    <p class="video-guide">
                        快速指引：
                        <img src="@/assets/Settings/video.png" alt="视频" style="height: 16px; margin-right: 5px" />
                        <a class="link" @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=130100100')">
                            视频教程
                        </a>
                    </p>
                    <ul class="step-list">
                        <li><span>1</span> 进入设置-科目，新增明细科目或辅助核算</li>
                        <li><span>2</span> 进入设置-期初，录入科目期初余额</li>
                        <li><span>3</span> 点击凭证-新增凭证，录入凭证数据</li>
                        <li><span>4</span> 查看账簿和财务报表</li>
                    </ul>
                    <a class="button solid-button" @click="() => (visible = false)">开始记账</a>
                </div>
                <div class="create-success-main-right">
                    <img v-if="!isPro" src="@/assets/Settings/create-accountset-success.png" alt="" />
                    <img v-else src="@/assets/Settings/create-accountset-success-pro.png" alt="" />
                </div>
            </div>
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import { globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { useTrialStatusStore } from "@/store/modules/trialStatus";

const isPro = window.isProSystem;
const trialStatusStore = useTrialStatusStore();
const isTrial = computed(() => {
    return trialStatusStore.isTrial;
});
const visible = ref(false);

const showDialog = () => {
    visible.value = true;
};

defineExpose({ showDialog });
</script>
<style lang="less" scoped>
.create-success-dialog {
    .create-success-title {
        height: 28px;
        font-size: 20px;
        font-weight: 600;
        color: #333;
        line-height: 28px;
        text-align: center;
        margin-top: 30px;
    }
}
.create-success-content {
    .create-success-tips {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        height: 20px;
        margin: 0 auto;
        font-size: 14px;
        color: #999;
        line-height: 20px;
        text-align: center;
    }
    .create-success-main {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 560px;
        margin: 45px auto;
        &-left {
            width: 340px;
            .video-guide {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                height: 20px;
                font-size: 14px;
                font-weight: 500;
                color: #333;
                line-height: 20px;
                padding: 0;
                margin: 0;
            }
            .step-list {
                width: 100%;
                list-style: none;
                padding: 0;
                li {
                    height: 20px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #333;
                    line-height: 20px;
                    margin-bottom: 12px;
                    span {
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        margin-right: 5px;
                        border-radius: 10px;
                        line-height: 20px;
                        text-align: center;
                        color: #fff;
                        background-color: #07c160;
                    }
                }
            }
            .button {
                width: 180px;
                height: 32px;
                line-height: 32px;
                margin-top: 30px;
            }
        }
        &-right {
            position: relative;
            width: 230px;
            height: 230px;
            background: url("@/assets/Settings/create-accountsets-guide-bg.png") no-repeat 100% 100%;
            background-size: 100% 100%;
            img {
                position: absolute;
                bottom: 23px;
                left: 67px;
                width: 101px;
                cursor: pointer;
            }
        }
        &.is-trial {
            margin: 10px auto 45px;
            .create-success-main-left {
                width: 218px;
                .button {
                    margin-top: 10px;
                }
            }
        }
    }
}
</style>
<style lang="less">
.create-success-dialog.el-dialog .el-dialog__header {
    border-bottom: 0;
}
</style>
