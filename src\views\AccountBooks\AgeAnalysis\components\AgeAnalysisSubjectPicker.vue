<template>
    <div class="ageAnalysis-subject-picker">
        <el-select
            :fit-input-width="true"
            :teleported="false"
            v-model="sbjCode"
            clearable
            style="width: 298px"
            @visible-change="handleVisibleChange"
            :filterable="true"
            :filter-method="ageSubjectFilterMethod"
        >
            <el-option
                v-for="item in showSubjectList"
                :label="item.lable"
                :value="item.asubCode"
                :key="item.asubId"
            />
        </el-select>
    </div>
</template>
<script lang="ts" setup>
import { computed, inject, ref, watchEffect } from "vue";
import { stopPopoverCloseKey } from "@/components/Picker/SubjectPicker/symbols";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { commonFilterMethod } from "@/components/Select/utils";

const props = withDefaults(
    defineProps<{
        sbjCode: string;
        subjectList: IAccountSubjectModel[];
    }>(),
    {
        sbjCode: "",
        subjectList: () => [],
    }
);

const emit = defineEmits<{
    (event: "update:sbjCode", args: string): void;
}>();

const sbjCode = computed({
    get() {
        return props.sbjCode;
    },
    set(value: string) {
        emit("update:sbjCode", value);
    },
});

// 阻止弹窗关闭
const stopPopoverClose = inject(stopPopoverCloseKey) as Function;
const handleVisibleChange = (visible: boolean) => {
    if (!visible) {
        if (stopPopoverClose) stopPopoverClose();
    }
};

interface IIAccountSubjectModel extends IAccountSubjectModel {
    lable: string;
}
const showSubjectList = ref<Array<IIAccountSubjectModel>>([]);
const subjectListAll = ref<Array<IIAccountSubjectModel>>([]);
watchEffect(() => { 
    subjectListAll.value = props.subjectList.map((item) => {
        return {
            ...item,
            lable: item.asubCode + '  ' + item.asubName,
        }
    })
    showSubjectList.value = JSON.parse(JSON.stringify(subjectListAll.value));  
});
function ageSubjectFilterMethod(value: string) {
    showSubjectList.value = commonFilterMethod(value, subjectListAll.value, 'lable');
}
</script>
