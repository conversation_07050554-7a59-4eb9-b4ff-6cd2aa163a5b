import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { getGlobalToken } from "@/util/baseInfo";
import { getCookie } from "./cookie";


export const pidToString = (pid: number | string) => {
    const target: any = useAccountPeriodStore().periodList.find((item: any) => item.pid == pid);
    return target.year + "年" + target.sn + "月";
};
// 根据字符串2022年9月获取pid
export const getYearMonthPid = (dateStr = "2022年9月") => {
    const regExp = /(\d+)年(\d+)月/;
    const matchResult: RegExpMatchArray | null = dateStr.match(regExp);
    const year = parseInt(matchResult ? matchResult[1] : "");
    const month = parseInt(matchResult ? matchResult[2] : "");
    return useAccountPeriodStore().periodList.find((item: any) => item.year === year && item.sn === month)?.pid;
};
export const localStoragePeriod = () => {

    const getIdentity = (): string => {
        return [currentUser(), currentAccountSet()].join("-");
    };
    const currentUser = (): string | undefined => {
        if (window.isErp) {
            return getCookie("auth") as string;
        }
        return useAccountSetStoreHook().userInfo?.userSn?.toString();
    };
    const currentAccountSet = (): string => {
        if (window.isErp) {
            return getGlobalToken();
        }
        return getGlobalToken();
    };

    const setStart = (identity: string, pid: string) => {
        if (pid) {
            localStorage.setItem(identity + "-start", pid);
        }
    };
    const setEnd = (identity: string, pid: string) => {
        if (pid) {
            localStorage.setItem(identity + "-end", pid);
        }
    };
    const changePeriod = (pid: string) => {
        setStart(getIdentity(), pid);
        setEnd(getIdentity(), pid);
    };
    const changePeriodRange = (start: string, end: string) => {
        if (Number(start) > Number(end)) {
            return false;
        }
        setStart(getIdentity(), start);
        setEnd(getIdentity(), end);
    };
    const getCurrentPeriod = (): string | number => {
        return getPeriodRange()?.end ?? useAccountPeriodStore().period.endPid;
    };
    const getPeriodRange = () => {
        let p = getPeriod(getIdentity());
        const periodStoreP = useAccountPeriodStore().period;
        if (p.start === "NaN" || p.end === "NaN") {
            p.start = useAccountPeriodStore().period.startPid;
            p.end = useAccountPeriodStore().period.endPid;
        }
        if (!(Number(p.start) >= periodStoreP.startPid &&
            Number(p.start) <= periodStoreP.endPid &&
            Number(p.end) >= periodStoreP.startPid &&
            Number(p.end) <= periodStoreP.endPid
        )
        ) {
            changePeriod((periodStoreP.endPid).toString())
            p = getPeriod(getIdentity());
        }
        return p;
    };
    const getPeriod = (identity: string) => {
        const start = localStorage.getItem(identity + "-start") ?? 0;
        const end = localStorage.getItem(identity + "-end") ?? 0;
        return { start: start, end: end };
    };
    const reset = () => {
        remove(getIdentity());
    };
    const remove = (identity: string) => {
        localStorage.removeItem(identity + "-start");
        localStorage.removeItem(identity + "-end");
    };

    return { changePeriod, changePeriodRange, getCurrentPeriod, getPeriodRange ,reset};
};
