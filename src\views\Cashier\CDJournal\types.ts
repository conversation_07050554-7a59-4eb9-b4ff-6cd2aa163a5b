export interface ICDAccountList {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
}

export interface ITableData {
    j_type: string;
    cd_account: string;
    cd_account_in: string;
    line_sn: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    line_sn_name: string;
    cd_date: string;
    description: string;
    ie_type: string;
    ie_type_name: string;
    income: string;
    expenditure: string;
    amount: string;
    amount_data: string;
    opposite_party: string;
    opposite_party_no: string;
    opposite_party_uscc: string;
    opposite_party_int: string;
    payment_method: string;
    payment_method_name: string;
    receipt_no: string;
    note: string;
    flag: string;
    checked: string;
    erp_offset: string;
    type: string;
    created_date: string;
    tran_no: string;
    modified_date: string;
    p_id: string;
    v_id: string;
    v_num: string;
    v_date: string;
    v_num2: string;
    receiptCount: string;
    project: string;
    project_name: string;
    department: string;
    department_name: string;
    receipt_state: string;
    showFromBankReceiptMessage: string;
}