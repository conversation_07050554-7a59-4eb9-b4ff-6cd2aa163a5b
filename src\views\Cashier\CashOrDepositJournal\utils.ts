import { request, type IResponseModel } from "@/util/service";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { showExpiredDialog } from "@/util/showExpiredDialog";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { getshortdata } from "@/views/Cashier/CDJournal/utils";
import { dayjs } from "element-plus";

import type { InjectionKey } from "vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableItem, IPayMethod, IRowItemEditInfo, IJournalBack } from "./types";
import type { IIETypeItem } from "@/views/Cashier/type";
import type { ICDAccountItem } from "@/views/Cashier/components/types";

export const ieTypeKey = Symbol() as InjectionKey<() => Array<IIETypeItem>>;
export const payMethodKey = Symbol() as InjectionKey<() => Array<IPayMethod>>;

export const formatDate = (date: string) => {
    if (date.trim() === "") return "";
    const maybeSplit = [" ", "T"];
    if (maybeSplit.some((item) => date.includes(item))) {
        const split = maybeSplit.find((item) => date.includes(item)) as string;
        date = date.split(split)[0];
    }
    return dayjs(date).format("YYYY-MM-DD");
};

export const initRowData = {
    j_type: "",
    cd_account: "",
    cd_account_in: "",
    line_sn: "",
    ac_no: "",
    ac_name: "",
    bank_account: "",
    line_sn_name: "",
    cd_date: "",
    description: "",
    ie_type: "",
    ie_type_name: "",
    income: "",
    income_standard: "",
    expenditure: "",
    expenditure_standard: "",
    amount: "",
    amount_rate: "",
    amount_standard: "",
    amount_data: "",
    amountstd_data: "",
    opposite_party: "",
    opposite_party_no: "",
    opposite_party_uscc: "",
    opposite_party_int: "",
    opposite_party_bank: "",
    payment_method: "",
    payment_method_name: "",
    receipt_no: "",
    note: "",
    flag: "",
    checked: "",
    erp_offset: "",
    type: "",
    created_date: "",
    tran_no: "",
    modified_date: "",
    p_id: "",
    v_id: "",
    v_num: "",
    v_date: "",
    v_num2: "",
    receiptCount: "",
    project: "",
    project_name: "",
    department: "",
    department_name: "",
};

const noShowAllColumns: Array<IColumnProps> = [
    // { slot: "selection", width: 36, headerAlign: "center", align: "center", fixed: "left" },
    { slot: "select", width: 36, headerAlign: "center", align: "center", fixed: "left" },
    { slot: "date", label: "日期", className: "colCellMove" },
    { slot: "description", label: "摘要", className: "colCellMove" },
    { slot: "ieTypeName", label: "收支类别", className: "colCellMove" },
    { slot: "oppositeParty", label: "往来单位", className: "colCellMove" },
    { slot: "income", label: "收入（借方）", className: "colCellMove" },
    { slot: "expenditure", label: "支出（贷方）", className: "colCellMove" },
    { slot: "amount", label: "余额", className: "colCellMove" },
    { slot: "account", label: "", className: "colCellMove" },
    { slot: "includes", label: "关联凭证", className: "colCellMove" },
    { slot: "lineSn", label: "日记账序号", className: "colCellMove" },
    { slot: "operation", fixed: "right" },
];
const showAllColumns: Array<IColumnProps> = [
    // { slot: "selection", width: 36, headerAlign: "center", align: "center", fixed: "left" },
    { slot: "select", width: 36, headerAlign: "center", align: "center", fixed: "left" },
    { slot: "date", label: "日期", className: "colCellMove" },
    { slot: "description", label: "摘要", className: "colCellMove" },
    { slot: "ieTypeName", label: "收支类别", className: "colCellMove" },
    { slot: "oppositeParty", label: "往来单位", className: "colCellMove" },
    { slot: "project", label: "项目", className: "colCellMove" },
    { slot: "department", label: "部门", className: "colCellMove" },
    { slot: "income", label: "收入（借方）", className: "colCellMove" },
    { slot: "expenditure", label: "支出（贷方）", className: "colCellMove" },
    { slot: "amount", label: "余额", className: "colCellMove" },
    { slot: "account", label: "", className: "colCellMove" },
    { slot: "includes", label: "关联凭证", className: "colCellMove" },
    { slot: "oppositePartyNo", label: "往来单位账号", className: "colCellMove" },
    { slot: "oppositePartyNoBank", label: "往来单位开户行", className: "colCellMove" },
    { slot: "paymentMethod", label: "结算方式", className: "colCellMove" },
    { slot: "receiptNo", label: "票据号", className: "colCellMove" },
    { slot: "note", label: "备注", className: "colCellMove" },
    { slot: "tranNo", label: "交易流水号", className: "colCellMove" },
    { slot: "lineSn", label: "日记账序号", className: "colCellMove" },
    { slot: "operation", fixed: "right" },
];
export const getTableColumns = (cdAccount: string, type: string, showAll?: boolean, useFc?: boolean, accountLength?: number) => {
    const columns: Array<IColumnProps> = showAll
        ? JSON.parse(JSON.stringify(showAllColumns))
        : JSON.parse(JSON.stringify(noShowAllColumns));

    const oppositePartyNoIndex = columns.findIndex((item) => item.slot === "oppositePartyNo");
    type === "1010" && showAll && columns.splice(oppositePartyNoIndex, 1);
    const oppositePartyNoBankIndex = columns.findIndex((item) => item.slot === "oppositePartyNoBank");
    type === "1010" && showAll && columns.splice(oppositePartyNoBankIndex, 1);
    
    const index = columns.findIndex((item) => item.slot === "account");
    columns[index].label = type === "1010" ? "现金账户" : "银行账户";
    if (cdAccount !== "all" || (cdAccount === "all" && accountLength === 1)) {
        columns.splice(index, 1);
    }
    if (useFc) {
        columns.splice(2, 0, { label: "币别", minWidth: 60, align: "left", headerAlign: "left", prop: "fc_name", className: "colCellMove" });
    }
    // if (showAll && useFc) {
    //     columns.forEach((item) => {
    //         if (item.slot === "income" || item.slot === "expenditure" || item.slot === "amount") {
    //             item.slot += "Fc";
    //         }
    //     });
    // }
    return columns;
};

export const getAccountList = (actype: number) => request({ url: "/api/CDAccount/List?acType=" + actype });

// 拆分a标签
export const splitATag = (str: string): string[] => {
    const regex = /(<a.*?>)(.*?)(<\/a>)/;
    const matches = str.match(regex);
    if (matches) {
        return [matches[1], matches[2], matches[3]];
    } else {
        return [];
    }
};

export const GetStart = (str: string) => {
    return str.split("-")[0] + "-" + str.split("-")[1] + "-" + "01";
};

export const GetEnd = (str: string) => {
    const times = new Date(Number(str.split("-")[0]), Number(str.split("-")[1]), 0);
    const year = times.getFullYear();
    const month = times.getMonth() + 1;
    const day = times.getDate();
    let time = year + "-";
    if (month < 10) {
        time += "0";
    }
    time += month + "-";
    if (day < 10) {
        time += "0";
    }
    time += day;
    return time;
};

export const checkFCAccountExistsHistoryData = (
    type: "Cash" | "Deposit" | "Transfer",
    cdAccounts: Array<string>
): Promise<IResponseModel<IFCAccountExistsHistoryDataCheckResult>> => {
    return request({
        url: "/api/Journal/Check" + type + "FCAccountExsitsHistoryData",
        data: cdAccounts,
        method: "post",
    });
};

interface IFCAccountExistsHistoryDataCheckResult {
    exists: boolean;
    data: Array<IFCAccountExistsHistoryDataCheckData>;
}

interface IFCAccountExistsHistoryDataCheckData {
    accountName: string;
    fcName: string;
}

export const syncFCAccountHistoryData = (
    type: "Cash" | "Deposit" | "Transfer",
    cdAccounts: Array<string>,
    syncType: 0 | 1 | 2
): Promise<IResponseModel<boolean>> => {
    return request({
        url: "/api/Journal/" + type + "SyncFCAccountHistoryData",
        data: { cdaccountList: cdAccounts, syncType: syncType },
        method: "post",
    });
};

export const formatRate = (value: number | string | undefined) => {
    const decimalPlace = useAccountSetStoreHook().accountSet?.decimalPlace || 2;
    if (value) {
        if (typeof value === "number" && !isNaN(value)) {
            return value.toFixed(decimalPlace).replace(/\.?0+$/, "");
        } else if (typeof value === "string") {
            const numberValue = Number(value);
            if (numberValue === 0) {
                return "";
            } else {
                return numberValue.toFixed(decimalPlace).replace(/\.?0+$/, "");
            }
        }
    }
    return "";
};

export function isNumericString(str: string) {
    // 去除字符串中的逗号
    const cleanedStr = str.replace(/,/g, "");
    // 使用正则表达式匹配纯数字
    const regex = /^-?[0-9]+(\.[0-9]+)?$/;
    return regex.test(cleanedStr);
}

export function extractLineSN(input: string) {
    const regex = /LINE_SN=(\d+)/;
    const match = input.match(regex);
    return match ? match[1] : "";
}

export const alipayWebsite = "https://openauth.alipay.com";
export const alipayAppid = "****************";

export function needShowExpiredDialog() {
    const trialStatusStore = useTrialStatusStore();
    if (trialStatusStore.isTrial && trialStatusStore.isExpired) {
        showExpiredDialog();
        return true;
    }
    return false;
}

export function selectable(row: ITableItem) {
    return row.cd_date !== "" && row.ie_type !== "" && !row.isCopyData;
}

export const selectBottomHtml = `
    <div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">+点击添加</a>
    </div>`;

export function getLineSnAndPerfix(lineSnName: string) {
    const localLineSnName = lineSnName.replace(/<[^<>]+>/g, "");
    const localLineSnNameList = localLineSnName.split("-");
    const perfix = localLineSnNameList.slice(0, localLineSnNameList.length - 1).join("-");
    const lineSn = localLineSnNameList[localLineSnNameList.length - 1];
    return [perfix, lineSn];
}

type stringNumber = string | number;
export function getLineSnNameWithAnchorTag(lineSnName: string, cdAccount: string, cdDate: string, lineSn: stringNumber, isIncome: boolean) {
    const params = {
        AC_ID: cdAccount,
        CD_DATE: cdDate,
        LINE_SN: lineSn,
        JOURNAL_TYPE: isIncome ? "INCOME" : "EXPENDITURE",
    };
    return `<a class='link' onclick="window.open('/Cashier/JournalPage?${getUrlSearchParams(params)}')">${lineSnName}</a>`;
}

export function checkCanSave(row: any, setUseLastRow: () => void) {
    const maxAmount = *********.99;
    if (row.cd_date === "") {
        ElNotify({ type: "warning", message: "亲，请输入日期！" });
        setUseLastRow();
        return false;
    }
    if (row.description.trim() === "") {
        ElNotify({ type: "warning", message: "亲，请输入摘要！" });
        setUseLastRow();
        return false;
    }
    if (row.description.length > 256) {
        ElNotify({ type: "warning", message: "亲，摘要不能超过256个字符！" });
        setUseLastRow();
        return false;
    }
    if (row.ie_type === undefined || row.ie_type == "") {
        ElNotify({ type: "warning", message: "亲，请选择收支类别！" });
        setUseLastRow();
        return false;
    }
    if (row.income !== "" && parseFloat(row.income) >= maxAmount) {
        ElNotify({ type: "warning", message: "收入金额不能大于亿位" });
        setUseLastRow();
        return false;
    }
    if (row.expenditure !== "" && parseFloat(row.expenditure) >= maxAmount) {
        ElNotify({ type: "warning", message: "支出金额不能大于亿位" });
        setUseLastRow();
        return false;
    }
    if (isNaN(Number(row.receipt_no)) || row.receipt_no.length > 18) {
        ElNotify({ type: "warning", message: "票据号只能输入18位及以下的数字" });
        setUseLastRow();
        return false;
    }
    if (row.note && row.note.length > 256) {
        ElNotify({ type: "warning", message: "亲，备注不能超过256个字哦~" });
        setUseLastRow();
        return false;
    }
    return true;
}

export function getReplaceRow(row: ITableItem, targetLineSn: number, targetIndex: number, date: string, jType: string) {
    const replaceRow: any = {
        line_sn: row.ie_type && row.description ? row.line_sn : targetLineSn.toString(),
        line_sn_name: `${jType}-${row.ac_no}-${getshortdata(date).split("-").join()}-${targetLineSn}`,
        ac_no: row.ac_no,
        cd_account: row.cd_account,
        cd_date: date,
        description: row.description,
        ie_type: row.ie_type,
        income: row.income,
        income_rate: row.income_rate,
        income_standard: row.income_standard,
        expenditure: row.expenditure,
        expenditure_rate: row.expenditure_rate,
        expenditure_standard: row.expenditure_standard,
        amount: row.amount,
        amount_data: row.amount,
        opposite_party_int: row.opposite_party_int,
        opposite_party_no: row.opposite_party_no,
        opposite_party: row.opposite_party,
        opposite_party_uscc: row.opposite_party_uscc,
        payment_method: row.payment_method,
        receipt_no: row.receipt_no,
        note: row.note,
        created_date: row.created_date,
        department: row.department,
        project: row.project,
        index: targetIndex,
        showAll: false,
        onlyShowDelete: true,
        changeDate: true,
        oldCdDate: row.cd_date,
        oldCDAccount: row.cd_account,
        defaultClickItem: !!row.defaultClickItem,
        changeDataPut: !!row.changeDataPut,
    };
    row.originalDate && (replaceRow.originalDate = row.originalDate);
    return replaceRow as unknown as ITableItem;
}

export function isMoreFc(cdAccountList: Array<ICDAccountItem>) {
    let result = false;
    const fcArr: string[] = [];
    for (let i = 0; i < cdAccountList.length; i++) {
        const currency = cdAccountList[i].currency;
        if (fcArr.includes(currency)) {
            continue;
        } else {
            if (fcArr.length === 1) {
                result = true;
                break;
            }
            fcArr.push(currency);
        }
    }
    return result;
}

export function getCombineSubmitParams(oldRow: ITableItem, rowItemSearchInfo: IRowItemEditInfo) {
    return {
        as_id: 0,
        j_type: "1010",
        cd_account: rowItemSearchInfo.ACCOUNT,
        line_sn: oldRow.line_sn,
        cd_date: rowItemSearchInfo.CD_DATE,
        description: rowItemSearchInfo.DESCRIPTION,
        ie_type: rowItemSearchInfo.IE_TYPE,
        fc_rate: 0,
        income: Number(rowItemSearchInfo.INCOME) ? Number(rowItemSearchInfo.INCOME).toFixed(2) : "",
        income_standard: Number(rowItemSearchInfo.INCOME_STANDARD) ? Number(rowItemSearchInfo.INCOME_STANDARD).toFixed(2) : "",
        expenditure: Number(rowItemSearchInfo.EXPENDITURE) ? Number(rowItemSearchInfo.EXPENDITURE).toFixed(2) : "",
        expenditure_standard: Number(rowItemSearchInfo.EXPENDITURE_STANDARD)
            ? Number(rowItemSearchInfo.EXPENDITURE_STANDARD).toFixed(2)
            : "",
        opposite_party: rowItemSearchInfo.OPPOSITE_PARTY,
        opposite_party_no: rowItemSearchInfo.OPPOSITE_PARTY_NO,
        opposite_party_uscc: rowItemSearchInfo.OPPOSITE_PARTY_USCC,
        opposite_party_bank: rowItemSearchInfo.OPPOSITE_PARTY_BANK,
        opposite_party_int: "",
        payment_method: rowItemSearchInfo.PAYMENT_METHOD,
        receipt_no: rowItemSearchInfo.RECEIPT_NO,
        note: rowItemSearchInfo.NOTE,
        p_id: oldRow.p_id,
        v_id: oldRow.v_id,
        created_date: oldRow.created_date,
        opposite_account_name: rowItemSearchInfo.ACCOUNT_NAME,
        opposite_account_no: rowItemSearchInfo.ACCOUNT_NO,
        department: rowItemSearchInfo.DEPARTMENT,
        project: rowItemSearchInfo.PROJECT,
    };
}

export function getDifferenceAmount(params: IJournalBack, oldRow: ITableItem, type: "post" | "put") {
    let differentIncome = params.income;
    let differentIncomeStandard = params.income_standard;
    let differentExpenditure = params.expenditure;
    let differentExpenditureStandard = params.expenditure_standard;

    if (type === "put") {
        differentIncome = Number(params.income) - Number(oldRow.income);
        differentIncomeStandard = Number(params.income_standard) - Number(oldRow.income_standard);
        differentExpenditure = Number(params.expenditure) - Number(oldRow.expenditure);
        differentExpenditureStandard = Number(params.expenditure_standard) - Number(oldRow.expenditure_standard);
    }

    let lastAmount =
        Number(oldRow.income) === 0 ? Number(oldRow.amount) + Number(oldRow.expenditure) : Number(oldRow.amount) - Number(oldRow.income);
    let lastAmountStandard =
        Number(oldRow.income_standard) === 0
            ? Number(oldRow.amount_standard) + Number(oldRow.expenditure_standard)
            : Number(oldRow.amount_standard) - Number(oldRow.income_standard);
    let thisAmount = params.isIncome ? lastAmount + params.income : lastAmount - params.expenditure;
    let thisAmountStandard = params.isIncome
        ? lastAmountStandard + params.income_standard
        : lastAmountStandard - params.expenditure_standard;
    let differentAmount = thisAmount - Number(oldRow.amount);
    let differentAmountStandard = thisAmountStandard - Number(oldRow.amount_standard);

    if (oldRow.isCopyData) {
        lastAmount = Number(oldRow.amount);
        lastAmountStandard = Number(oldRow.amount_standard);
        thisAmount = params.isIncome ? lastAmount + differentIncome : lastAmount - differentExpenditure;
        thisAmountStandard = params.isIncome
            ? lastAmountStandard + differentIncomeStandard
            : lastAmountStandard - differentExpenditureStandard;
        differentAmount = thisAmount - Number(oldRow.amount);
        differentAmountStandard = thisAmountStandard - Number(oldRow.amount_standard);
    }

    return {
        differentAmount,
        differentAmountStandard,
        differentIncome,
        differentIncomeStandard,
        differentExpenditure,
        differentExpenditureStandard,
    };
}

export enum AliPaySystem {
    Acc = "Acc",
    Pro = "Pro",
    AgentFree = "AgentFree",
    AgentPro = "AgentPro",
    Erp = "Erp",
}
