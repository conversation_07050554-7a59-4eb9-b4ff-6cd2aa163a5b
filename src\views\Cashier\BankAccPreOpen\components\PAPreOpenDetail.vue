<template>
    <div class="open-account detail-content">
        <div class="detail-back-button" @click="back">
            <img src="@/assets/Icons/back.png" alt="" />
            <span>返回</span>
        </div>
        <div class="slot-title">预约开户详情</div>
        <div class="open-main-content">
            <div class="step-edit">
                <div class="block-title">开户银行</div>
                <div class="block-main">
                    <el-row class="isRow"><div class="openbank-line">开户银行：平安银行</div> </el-row>
                </div>
                <div class="line"></div>
                <div class="block-title">银行网点信息</div>
                <div class="block-main">
                    <el-form :model="PADeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="网点机构号：">
                                <div class="w-240">{{ PADeltailInfo.branchNo }}</div>
                            </el-form-item>
                            <el-form-item label="网点名称：">
                                <div class="w-240">{{ PADeltailInfo.branchName }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="网点地址：">
                                <div class="w-240">{{ PADeltailInfo.branchAddr }}</div>
                            </el-form-item>
                            <el-form-item label="网点电话：">
                                <div class="w-240">{{ PADeltailInfo.branchTel }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">基本信息</div>
                <div class="block-main">
                    <el-form :model="PADeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="公司名称：">
                                <div class="w-240">{{ PADeltailInfo.companyName }}</div>
                            </el-form-item>
                            <el-form-item label="统一社会信用代码：">
                                <div class="w-240">{{ PADeltailInfo.unifiedNumber }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人姓名：">
                                <div class="w-240">{{ PADeltailInfo.legalName }}</div>
                            </el-form-item>
                            <el-form-item label="法人手机号：">
                                <div class="w-240">{{ PADeltailInfo.legalMobile }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">更多信息</div>
                <div class="block-main">
                    <el-form :model="PADeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="注册地址：">
                                <div class="w-240">
                                    {{ PADeltailInfo.regProvName + "-" + PADeltailInfo.regCityName + "-" + PADeltailInfo.regAddr }}
                                </div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="经营地址：">
                                <div class="w-240">
                                    {{ PADeltailInfo.bizProvName + "-" + PADeltailInfo.bizCityName + "-" + PADeltailInfo.bizAddr }}
                                </div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="账户类型：">
                                <div class="w-240">
                                    <div class="w-240">{{ PADeltailInfo.accountType === 0 ? "没有基本户" : "已有基本户" }}</div>
                                </div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">预约时间</div>
                <div class="block-main">
                    <el-form :model="PADeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="预约时间：">
                                <div class="w-240">{{ PADeltailInfo.appointmentTime }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import type { IBankDetail, IPADetail } from "../types";

const props = defineProps({
    data: {
        type: Object as PropType<IBankDetail<IPADetail>>,
        default: () => ({
            bankType: 0,
            openState: 0,
            id: "",
            content: {
                branchName: "",
                branchNo: "",
                branchTel: "",
                branchAddr: "",
                regProvName: "",
                regProvCode: "",
                regCityName: "",
                regCityCode: "",
                regAddr: "",
                bizProvName: "",
                bizProvCode: "",
                bizCityName: "",
                bizCityCode: "",
                bizAddr: "",
                companyName: "",
                unifiedNumber: "",
                legalName: "",
                legalMobile: "",
                accountType: 0,
                appointmentTime: "",
            },
        }),
    },
});
const emit = defineEmits<{
    (e: "back"): void;
}>();

const PADeltailInfo = computed(() => props.data.content);

const back = () => {
    emit("back");
};
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
