<template>
    <div :class="['content', { 'narrow-content': !searchInfo.classification }]">
        <div class="title">利润表季报</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <PaginationPeriodPicker v-model="searchInfo.pid" :isQuarter="true" ref="periodRef"></PaginationPeriodPicker>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <div>
                        <el-checkbox v-model="searchInfo.classification" label="显示本年所有季度" @change="changeQuarter"></el-checkbox>
                    </div>
                    <div class="mr-10" :style="{ marginLeft: '30px' }">
                        <el-checkbox
                            v-model="searchInfo.lastYearPeriod"
                            :label="accountStandard === 1 ? '显示上年累计金额' : '显示上年同期累计金额'"
                            @change="changeLastYearPeriod"></el-checkbox>
                    </div>
                    <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['incomequartersheet-canprint']">
                        <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button mr-10" @click="handleExport" v-permission="['incomequartersheet-canexport']">导出</a>
                    <a class="button" v-if="!isHideBarcode" v-permission="['incomequartersheet-canshare']" @click="handleShare">微信分享</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div v-if="isErp" class="divider-line"></div>
            <div class="main-center">
                <IncomeTable
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :tableData="tableData"
                    :empty-text="emptyText"
                    :accountStandard="accountStandard"
                    :quarterShow="searchInfo.classification"
                    :isTax="searchInfo.lastYearPeriod"></IncomeTable>
            </div>
        </div>
    </div>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="利润表季报打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "IncomeQuarterSheet",
};
</script>
<script setup lang="ts">
import Dropdown from "@/components/Dropdown/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import IncomeTable from "./components/IncomeQuarterTable.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { globalPrint, globalExport, getUrlSearchParams } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { request, type IResponseModel } from "@/util/service";
import { ref, reactive, watch } from "vue";
import { ElNotify } from "@/util/notify";
import { share } from "@/views/Statements/utils";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useRoute } from "vue-router";
import usePrint from "@/hooks/usePrint";
import type { IIncomeSheetQuarter } from "./types";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

const isErp = ref(window.isErp);
const route = useRoute();
const periodStore = useAccountPeriodStore();
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const searchInfo = reactive({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    classification: false, //是否显示本年所有季度
    lastYearPeriod: false, //是否显示去年同期
});
const accountStandard = useAccountSetStore().accountSet?.accountingStandard ?? 1;
const tableData = ref<IIncomeSheetQuarter[]>([]);
const loading = ref(false);
const emptyText = ref("");
const isHideBarcode = useThirdPartInfoStoreHook().isHideBarcode;

//获取利润季报
function handleSearch() {
    loading.value = true;
    emptyText.value = " ";
    request({
        url: `/api/IncomeSheet/Quarter?` + getUrlSearchParams(getDefaultParams()),
        method: "get",
    }).then((res: IResponseModel<IIncomeSheetQuarter[]>) => {
        loading.value = false;
        if (res.state === 1000) {
            tableData.value = [];
            let parent = null;
            for (let index = 0; index < res.data.length; index++) {
                const element = res.data[index];
                if (element.expand === 1) {
                    element.children = [];
                    parent = element;
                } else if (element.fold === 1) {
                    parent?.children!.push(element);
                    continue;
                }
                if (index !== 0 && res.data[index - 1].proName.includes("其中：")) {
                    element.indentation = true;
                }
                tableData.value.push(element);
            }
        } else {
            console.log(res.msg);
        }
        if (!tableData.value.length) {
            emptyText.value = "暂无数据";
        }
    });
}

//是否显示本年所有季度classification as Boolean
function changeQuarter(val: any) {
    if (val) {
        searchInfo.lastYearPeriod = false;
    }
    handleSearch();
}
//是否显示去年同期
function changeLastYearPeriod(val: any) {
    if (val) {
        searchInfo.classification = false;
    }
    handleSearch();
}
//去年同期的pid

function getDefaultParams() {
    return {
        pid: searchInfo.pid,
        isFullYear: searchInfo.classification,
        isTax:searchInfo.lastYearPeriod,
    };
}
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "incomeSheet",
    "/api/IncomeSheet/PrintQuarter",
    {},
    false,
    false,
);
//导出
function handleExport(printType: number) {
    globalExport(`/api/IncomeSheet/ExportQuarter?` + getUrlSearchParams(getDefaultParams()));
}

//微信分享
const shareReportHost = ref("");
function handleShare() {
    request({
        url: `/api/IncomeSheet/ShareQuarter?` + getUrlSearchParams(getDefaultParams()),
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            shareReportHost.value =
                window.accountSrvHost +
                "/api/WxPay/MakeQRCode.ashx?data=" +
                window.shareReportHost +
                "/ShareReport/" +
                res.data +
                "&CurrentSystemType=1";
            share(shareReportHost.value);
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
}

watch(
    () => searchInfo.pid,
    (val) => {
        handleSearch();
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";

:deep(.main-center) {
    tr {
        td {
            .cell {
                //这里样式已经控制好了，禁用掉element的样式
                .el-table__indent {
                    display: none !important;
                }

                .el-table__placeholder {
                    display: none;
                }

                .el-table__expand-icon {
                    position: absolute;
                }

                .el-icon {
                    margin-top: 2px;
                }

                .level2 {
                    span {
                        // max-width: 200px;
                        white-space: normal;
                    }
                }

                // .level3.pl-60 {
                //     padding-left: 60px;
                // }
            }
        }
    }
}
:deep(.el-table) {
    .el-table__expand-icon {
        transform: rotate(90deg);
        margin-top: 3px;
        color: #fff;
        .el-icon {
            width: 1em;
            height: 1em;
            margin: 0 !important;
            border-radius: 50%;
            background-color: #2abe2a;
        }
        &.el-table__expand-icon--expanded {
            transform: rotate(-90deg) !important;
        }
    }
}
</style>
