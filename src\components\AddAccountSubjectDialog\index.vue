<template>
    <el-dialog 
        v-model="display" 
        title="新增科目" 
        center 
        width="452px" 
        class="custom-confirm dialogDrag"
        @closed="emit('closed')" >
        <div class="add-asub-dialog-container" v-dialogDrag>
            <div class="form-item">
                <div class="input-title"><span class="highlight-red">*</span>上级科目：</div>

                <div class="input-field">
                    <el-select-v2
                        ref="selectRef"
                        v-model="model.parentId"
                        :options="showSelectOptions"
                        :popper-class="'add-subject'"
                        :class="visibleSelect ? 'visibleSelect' : ''"
                        @visible-change="handleVisibleChange"
                        :fit-input-width="true"
                        :filterable="true"
                        :default-first-option="true"
                        :reserve-keyword="false"
                        :required="true"
                        :scrollbar-always-on="true"
                        placement="bottom"
                        suffix-icon=" "
                        :remote="true"
                        :filter-method="subjectFilterMethod"
                    >
                        <template #default="{ item }">
                            <div class="custom-select-option" @click="clickParentAsub(item, $event)">
                                <span>{{ item.label }}</span>
                            </div>
                        </template>
                    </el-select-v2>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title"><span class="highlight-red">*</span>科目编号：</div>
                <div class="input-field">
                    <div class="asubcode" ref="asubCodeDiv" :title="(parentAsub?.asubCode || '') + model.asubCode">
                        <span>{{ parentAsub?.asubCode }}</span>
                        <input type="text" :maxlength="firstCodeLength" v-model="model.asubCode" />
                    </div>
                </div>
            </div>
            <div class="form-item" style="position: relative">
                <div class="input-title"><span class="highlight-red">*</span>科目名称：</div>
                <div class="input-field">
                    <div v-if="checkSixMajor(parentAsub?.asubCode || '', accountingStandard, firstCodeLength)">
                        <el-autocomplete
                            ref="subjectNameInputRef"
                            v-if="asubNameTextareaShow"
                            v-model="model.asubName"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur"
                            maxlength="256"
                            @change="limitInputLength(model.asubName, '科目名称')"
                            :prop="[{ required: true, trigger: ['change'] }]"
                            :fetch-suggestions="querySearch"
                            :trigger-on-focus="false"
                            placeholder=" "
                            :fit-input-width="true"
                            :teleported="false"
                            :debounce="500"
                            @select="handleSelect"
                        >
                            <template #default="{ item }">
                                <Tooltip :content="item.value" :line-clamp="2" placement="right" :maxWidth='484' :teleported="true">
                                    <div class="value">{{ item.value }}</div>
                                </Tooltip>
                            </template>
                        </el-autocomplete>
                        <Tooltip :content="model.asubName" placement="right"  :isInput="true" v-else>
                            <el-autocomplete
                                ref="subjectNameInputRef"
                                v-model="model.asubName"
                                :prop="[{ required: true, trigger: ['change'] }]"
                                :fetch-suggestions="querySearch"
                                :trigger-on-focus="false"
                                placeholder=" "
                                :fit-input-width="true"
                                :teleported="false"
                                :debounce="500"
                                @select="handleSelect"
                                @focus="inputTypeFocus"
                            >
                                <template #default="{ item }">
                                    <Tooltip :content="item.value" :line-clamp="2" placement="right" :maxWidth='484' :teleported="true">
                                        <div class="value">{{ item.value }}</div>
                                    </Tooltip>
                                </template>
                            </el-autocomplete>
                        </Tooltip>
                    </div>
                    <div v-else>
                        <el-input v-model="model.asubName" 
                            v-if="asubNameTextareaShow"
                            ref="subjectNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur"
                            class='asubName-textarea'
                            maxlength="256"
                            @input="limitInputLength(model.asubName,'科目名称')"
                        />
                        <Tooltip  :content="model.asubName" placement="right" :isInput="true" v-else>
                            <input type="text" v-model="model.asubName"  @focus="inputTypeFocus"/>
                        </Tooltip>
                    </div>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title"><span class="highlight-red">*</span>科目类别：</div>
                <div class="input-field">
                    <el-select
                        v-model="model.asubType"
                        :disabled="model.parentId !== undefined && model.parentId !== 0"
                        @change="asubTypeChange()"
                        :filterable="true"
                        :filter-method="asubTypeFilterMethod"
                    >
                        <el-option
                            v-for="asubType in showAsubTypeList"
                            :key="asubType.id"
                            :label="asubType.name"
                            :value="asubType.id"
                        ></el-option>
                    </el-select>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title"><span class="highlight-red">*</span>余额方向：</div>
                <div class="input-field">
                    <el-radio-group v-model="model.direction">
                        <el-radio :label="1">借</el-radio>
                        <el-radio :label="2">贷</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="form-item" v-show="accountingStandard === 3 && model.asubType === 8">
                <div class="input-title"><span class="highlight-red">*</span>收入属性：</div>
                <div class="input-field">
                    <el-radio-group
                        v-model="model.restricted"
                        :disabled="
                            model.parentId !== undefined &&
                            model.parentId !== 0 &&
                            ((parentLevel2Asub && Number(parentLevel2Asub.asubId) < 80000) || parentAsubUsed)
                        "
                    >
                        <el-radio :label="0">非限定性</el-radio>
                        <el-radio :label="1">限定性</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title">
                    <el-checkbox
                        v-model="model.quantityaccounting"
                        label="数量核算"
                        :true-label="1"
                        :false-label="0"
                        :disabled="model.parentId !== undefined && parentAsub?.quantityAccounting === 1"
                    ></el-checkbox>
                </div>
                <div class="input-field measureunit">
                    <template v-if="model.quantityaccounting === 1">
                        计量单位：<el-select v-show="isErp" v-model="model.measureunitId">
                            <el-option v-for="item in unitList" :key="item.id" :value="item.id" :label="item.value"></el-option></el-select
                        ><input
                            v-show="!isErp"
                            type="text"
                            style="width: 48px; margin: -5px 0"
                            v-model="model.measureunit"
                            :disabled="model.parentId !== undefined && parentAsub?.quantityAccounting === 1"
                            maxlength="20"
                        />
                    </template>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title" style="align-self: flex-start">
                    <el-checkbox
                        v-model="model.assistingaccounting"
                        label="辅助核算"
                        :true-label="1"
                        :false-label="0"
                        :disabled="model.parentId !== undefined && parentAsub?.assistingAccounting === 1"
                    ></el-checkbox>
                </div>
                <div class="input-field" style="display: block; font-size: 0">
                    <template v-if="model.assistingaccounting === 1">
                        <template v-for="(aat, index) in aatList" :key="index">
                            <el-checkbox
                                @change="assistCheckboxChange(aat.selected, aat)"
                                v-model="aat.selected"
                                :disabled="(model.parentId !== undefined && parentAsub?.assistingAccounting === 1)"
                                v-show="
                                    aat.id !== 10007 ||
                                    (accountingStandard !== 1 && accountingStandard !== 2 && accountingStandard !== 3) ||
                                    ((accountingStandard === 1 || accountingStandard === 2) &&
                                        /^(1001)|(1002)|(1012)/.test(parentAsub?.asubCode || '')) ||
                                    (accountingStandard === 3 && /^(1001)|(1002)|(1009)/.test(parentAsub?.asubCode || ''))
                                "
                                >{{ aat.name }}</el-checkbox
                            >
                            <br v-if="index % 2 === 1" />
                        </template>
                    </template>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title" style="align-self: flex-start">
                    <el-checkbox
                        v-model="model.foreigncurrency"
                        label="外币核算"
                        :true-label="1"
                        :false-label="0"
                        :disabled="model.parentId !== undefined && parentAsub?.foreigncurrency === 1"
                    ></el-checkbox>
                </div>
                <div class="input-field" style="flex-direction: column; align-items: flex-start">
                    <template v-if="model.foreigncurrency === 1">
                        <div style="display: flex" v-show="model.asubType === 1 || model.asubType === 2">
                            <el-checkbox
                                v-model="model.fcadjust"
                                label="期末调汇"
                                :true-label="1"
                                :false-label="0"
                                :disabled="model.parentId !== undefined && parentAsub?.foreigncurrency === 1"
                            ></el-checkbox>
                        </div>
                        <div style="display: block; font-size: 0">
                            <template v-for="(fc, index) in fcList" :key="index">
                                <el-checkbox
                                    v-model="fc.selected"
                                    :disabled="model.parentId !== undefined && parentAsub?.foreigncurrency === 1"
                                    >{{ fc.name }}</el-checkbox
                                >
                                <br v-if="index % 2 === 1" />
                            </template>
                        </div>
                    </template>
                </div>
            </div>
            <div class="asublength-tip"><span class="highlight-red">注意：</span>科目编码规则：{{ asubLength }}。</div>
            <div class="buttons">
                <a class="button" @click="display = false">取消</a>
                <a class="button solid-button ml-10" @click="saveAccountSubject()">确定</a>
            </div>
        </div>
    </el-dialog>
</template>
<style lang="less" scoped>
@import "@/style/Functions.less";

:deep(.el-input__inner){
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.add-asub-dialog-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .form-item {
        padding-left: 53px;
        display: flex;
        align-items: center;
        margin-top: 10px;

        .el-checkbox,
        .el-radio {
            height: 20px;
            vertical-align: top;
        }

        .asubcode {
            display: flex;
            align-items: center;
            position: relative;

            span {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 10px;
                line-height: 30px;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .input-title {
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            width: 88px;
            flex-shrink: 0;
        }

        .input-field {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .detail-input(256px, 30px);
            .detail-el-select(256px, 30px);
            .detail-el-autocomplete(256px, 30px);
            .asubName-textarea{
                width:256px;
                height:30px;
            }
            display: flex;
            align-items: center;
            input{
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            &.measureunit {
                .detail-el-select(100px, 30px);
            }
        }
    }

    .asublength-tip {
        padding-left: 53px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        margin-top: 10px;
    }

    .buttons {
        margin-top: 20px;
        padding: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
:deep(.el-select-v2) {
    width: 256px;
}
// 文字选择框长度
:deep(.el-select-v2__input-wrapper) {
    justify-content: flex-start;

    .el-select-v2__combobox-input {
        width: 85% !important;
        flex-grow: initial;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

body[erp] .custom-confirm .el-dialog__body .buttons {
    display: flex;
    justify-content: flex-end;
}

.custom-select-option {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}
</style>
<style lang="less">
.el-select-v2__popper.add-subject {
    &.el-popper.is-light,
    &.el-popper.is-dark {
        border-color: #dcdfe6;
    }
    .el-popper__arrow {
        display: none;
    }
    .el-select-dropdown {
        .el-select-dropdown__wrap {
            max-height: 200px;
            overflow-y: auto;
        }
        .el-select-dropdown__list {
            margin: 0 !important;
            .el-select-dropdown__option-item {
                height: 34px !important;
                padding: 2px 10px 0px 8px;
                line-height: 14px;
                white-space: normal;
                background-color: var(--white);
                color: var(--font-color);
                width: 100%;
                display: flex;
                align-items: center;
                & span {
                    display: -webkit-box;
                    -webkit-line-clamp: 2; /* 设置最多显示2行 */
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    white-space: normal;
                    word-break: break-all;
                    text-align: left;
                }
            }
        }
        .el-select-v2__empty {
            height: 170px;
        }
    }
}

.el-select-v2__popper.subject-picker .el-select-dropdown .el-vl__wrapper {
    max-height: 170px;
}
</style>
<script setup lang="ts">
import Tooltip from "@/components/Tooltip/index.vue";
import { ref, computed, onMounted, onUnmounted } from "vue";
import { type IAccountSubjectModel, AccountSubjectSaveModel, saveAccountSubjectApi } from "@/api/accountSubject.js";
import { request, type IResponseModel } from "@/util/service";
import { AsubTypeEnum } from "@/views/Settings/AccountSubject/types";
import { watch } from "vue";
import { nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { autocompleteSelfAdaption } from "@/views/Settings/AssistingAccounting/utils";
import { getGlobalLodash } from "@/util/lodash";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { watchEffect } from "vue";
import { checkSixMajor } from "@/util/subject";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import type { IAssistingAccountType } from "@/api/assistingAccounting";
import { getCompanyList } from "@/util/getCompanyList";
import { commonFilterMethod } from "@/components/Select/utils";
import { getAsubCodeIsUsedBaseItem } from "@/views/Settings/AccountSubject/utils";
import { useCurrencyStore } from "@/store/modules/currencyList";

const props = withDefaults(
    defineProps<{
        immediatelyRefresh?: boolean;
    }>(),
    {
        immediatelyRefresh: true,
    }
);

// 访问全局的 lodash 方法
const _ = getGlobalLodash()
const accountingStandard = ref(0);
const emit = defineEmits(["save-success", "closed"]);
const display = ref(false);
const model = ref<AccountSubjectSaveModel>(new AccountSubjectSaveModel());
const asubs = ref<Array<IAccountSubjectModel>>();
const asubTypeList = ref(new Array<{ id: number; name: string }>());
const aatList = ref(new Array<{ id: number; name: string; selected: boolean }>());
const fcList = ref(new Array<{ id: number; name: string; selected: boolean }>());
const asubLength = ref("");
const firstCodeLength = ref();
const parentAsub = ref<IAccountSubjectModel>();
const parentAsubUsed = ref(false);
const parentLevel2Asub = ref<IAccountSubjectModel>();
const asubCodeDiv = ref();
const isErp = ref(window.isErp);

const accountsetStore = useAccountSetStore();
accountingStandard.value = Number(accountsetStore.accountSet?.accountingStandard);

const unitList = ref<Array<{ id: string; value: string }>>([]);
const asubNameTextareaShow=ref(false)
const subjectNameInputRef=ref()
request({
    url: "/api/AccountSubject/GetUnitList",
    method: "post",
}).then((res: IResponseModel<Array<{ id: number; value: string }>>) => {
    if (res.state === 1000) {
        unitList.value = res.data.map((item) => {
            return {
                id: item.id.toString(),
                value: item.value,
            };
        });
    }
});

const assistCheckboxChange = (value: boolean, data: any) => {
    if (value) {
        const selectedAATypes = aatList.value.filter((aat) => aat.selected);
        if (selectedAATypes.length > 5) {
            ElNotify({
                message: "您最多只能设置5个辅助核算类别！",
                type: "warning",
            });
            data.selected = false;
        }
    }
};
const currencyStore = useCurrencyStore();
const getCurrencyList = async () => {
    await currencyStore.getCurrencyList();
    fcList.value = currencyStore.fcList.map((fc) => {
        return { id: fc.id, name: fc.name, selected: false };
    });
};
const inputTypeBlur = () => {
    asubNameTextareaShow.value = false;
};

const inputTypeFocus = () => {
    asubNameTextareaShow.value = true;
    nextTick(()=>{
        subjectNameInputRef.value?.focus();
    })
};

function limitInputLength(val: string,label: string) {
    switch (label) {
        case '科目名称':
            if (val.length === 256) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
            }
            break;
    }
}
const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
}
const querySearch = (queryString: string, cb: any) => {
    if (!model.value.asubName) {
        return;
    }
    if (queryString.length > 50) {
      cb([]);
    }else{
        nextTick(()=>{
            autocompleteSelfAdaption()
        })
        getCompanyList(1030,queryString,cb,queryParams)
    }
};
function handleSelect(val: any) {
    model.value.asubName = val.value;
    getCompanyDetailApi(1030, val.value)
}
let saving = false;
const saveAccountSubject = _.debounce(saveAccountSubjectInner, 500);
function saveAccountSubjectInner() {
    if (saving) return;
    new Promise<void>((resolve, reject) => {
        if (model.value.parentId === undefined) {
            reject("亲，请选择上级科目！");
            return;
        }
        if (
            !model.value.asubCode ||
            !/^\d+$/.test(model.value.asubCode) ||
            model.value.asubCode.length !== Number(asubLength.value.split("-")[parentAsub.value?.asubLevel || 0])
        ) {
            reject("亲，请输入正确的科目编码！");
            return;
        }
        if (!model.value.asubName) {
            reject("亲，请输入科目名称！");
            return;
        }
        if (asubs.value) {
            if (asubs.value.filter((asub) => asub.asubCode === (parentAsub.value?.asubCode || "") + model.value.asubCode).length > 0) {
                reject("亲，科目编码已存在！");
                return;
            }
            if (
                asubs.value.filter((v) => v.asubType === model.value.asubType).filter(
                    (asub) =>
                        asub.asubAAName ===
                        (parentAsub.value?.asubAAName ? `${parentAsub.value?.asubAAName}-${model.value.asubName}` : model.value.asubName)
                ).length > 0
            ) {
                reject("亲，科目名称已存在！");
                return;
            }
        }
        if (model.value.quantityaccounting === 1 && !model.value.measureunit) {
            reject("请设置数量核算单位名称！");
            return;
        }
        const selectedAATypes = aatList.value.filter((aat) => aat.selected);
        if (model.value.assistingaccounting === 1 && selectedAATypes.length === 0) {
            reject("请设置辅助核算的具体核算项!");
            return;
        }
        if (selectedAATypes.length > 5) {
            reject("您最多只能设置5个辅助核算类别！");
            return;
        }
        model.value.aaTypes = selectedAATypes.map((aat) => aat.id).join(",");
        model.value.fcids = fcList.value
            .filter((fc) => fc.selected)
            .map((fc) => fc.id)
            .join(",");
        if (model.value.foreigncurrency === 1 && model.value.fcids === "1") {
            reject("外币不能只选择人民币！");
            return;
        }
        if (parentAsubUsed.value) {
            ElConfirm("亲，上级科目已有凭证，保存后新增科目将替代凭证中的上级科目，您要继续吗？").then((r) => {
                if (r) {
                    resolve();
                }
            });
        } else {
            const currentName = model.value.asubName;
            const currentNameE = currentName.replace("(","（").replace(")","）");
            const itemE = getAsubCodeIsUsedBaseItem(asubs.value || [], currentNameE, parentAsub.value?.asubId);
            if (checkSixMajor(parentAsub.value?.asubCode || "", accountingStandard.value, firstCodeLength.value) && itemE) {
                let code = itemE ? itemE.asubCode : "";
                let name = itemE ? itemE.asubName : "";
                ElConfirm(`亲，已存在同级科目：${code}-${name}，是否继续保存?`).then((r) => {
                    if (r) {
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        }
    })
        .then(() => {
            let data = JSON.parse(JSON.stringify(model.value));
            if (parentAsub.value) {
                data.asubCode = parentAsub.value.asubCode + data.asubCode;
            }
            if (accountingStandard.value === 3 && data.asubType !== 8) {
                data.restricted = 0;
            }
            saving = true;
            saveAccountSubjectApi(data).then((res: IResponseModel<number>) => {
                saving = false;
                if (res.state === 1000) {
                    display.value = false;
                    props.immediatelyRefresh && useAccountSubjectStore().getAccountSubject();
                    emit("save-success", res.data);
                    ElNotify({
                        message: "保存成功",
                        type: "success",
                    });
                } else {
                    ElNotify({
                        message: "新增科目保存失败，" + res.msg,
                        type: "warning",
                    });
                }
            });
        })
        .catch((res: any) => {
            ElNotify({
                message: res,
                type: "warning",
            });
        });
}
//显示下拉框 输入框文字样式变浅
const visibleSelect = ref(false);
const handleVisibleChange = (value: boolean) => {
    visibleSelect.value = value;
    showSelectOptions.value = JSON.parse(JSON.stringify(selectOptions.value));
};
watchEffect(() => {
    const accountSubject = useAccountSubjectStore().accountSubjectList;
    asubs.value = accountSubject.filter((asub) => asub.status === 0 && (accountingStandard.value === 3 ? true : asub.asubType !== 7));
    asubTypeList.value = [];
    let asubGroup: any = {};
    accountSubject.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = asub.asubType;
            asubTypeList.value.push({
                id: asub.asubType,
                name: (AsubTypeEnum[asub.asubType] === "权益" ? "所有者权益" : AsubTypeEnum[asub.asubType]) + "类",
            });
        }
    });
    model.value.asubType = asubTypeList.value[0].id;
    const aatListStore = useAssistingAccountingStore().assistingAccountingTypeList;
    aatList.value = aatListStore.map((aat: IAssistingAccountType) => {
        return { id: aat.aaType, name: aat.aaTypeName, selected: false };
    });
});

function getAsubsSelector() {
    if (accountingStandard.value === 1 && !window.isProSystem && !window.isErp) {
        return asubs.value;
    } else {
        return [{ asubId: 0, asubCode: "", asubAAName: "没有上级科目" }].concat(asubs.value || []);
    }
}
const selectOptions = computed(() => {
    if (accountingStandard.value === 1 && !window.isProSystem && !window.isErp) {
        return asubs.value?.map((item) => {
            return {
                label: item.asubCode + "" + item.asubAAName,
                value: item.asubId,
            };
        }) || [];
    } else if (accountingStandard.value === 3) {
        return [{ value: 0, label: "没有上级科目" }].concat(
            asubs.value
                ?.filter((item) => item.asubType !== 7)
                .map((item) => {
                    return {
                        label: item.asubCode + "" + item.asubAAName,
                        value: item.asubId,
                    };
                }) || []
        );
    } else {
        return [{ value: 0, label: "没有上级科目" }].concat(
            asubs.value?.map((item) => {
                return {
                    label: item.asubCode + "" + item.asubAAName,
                    value: item.asubId,
                };
            }) || []
        );
    }
});

const selectRef = ref();

function clickParentAsub(clickItem: { label: string; value: number }, event: MouseEvent) {
    const item = asubs.value?.find((asub) => asub.asubId === clickItem.value);
    if (item) {
        const asubLevel = item.asubLevel;
        if (asubLevel === codeLength.value.length) {
            event.stopPropagation();
            event.preventDefault();
            selectRef.value?.toggleMenu();
            ElNotify({
                message: "亲，" + codeLength.value.length + "级科目下不能新建子科目的哟！",
                type: "warning",
            });
            return;
        }
    }
}
function showAADialog() {
    display.value = true;
    model.value.parentId = undefined;
    reset();
    getAsubsSelector();
    getCurrencyList();
}

function reset() {
    model.value.asubCode = "";
    model.value.asubName = "";
    parentAsub.value = undefined;
    parentAsubUsed.value = false;
    model.value.asubType = asubTypeList.value[0].id;
    model.value.direction = 1;
    model.value.restricted = 0;
    model.value.quantityaccounting = 0;
    model.value.measureunit = "";
    model.value.measureunitId = "";
    model.value.assistingaccounting = 0;
    model.value.aaTypes = "";
    model.value.allowNullAaTypes = "";
    for (let i = 0; i < aatList.value.length; i++) {
        aatList.value[i].selected = false;
    }
    model.value.foreigncurrency = 0;
    model.value.fcadjust = 0;
    model.value.fcids = "";
    for (let i = 0; i < fcList.value.length; i++) {
        fcList.value[i].selected = false;
    }
    if (model.value.parentId === 0) {
        const roots = asubs.value?.filter((asub) => asub.parentId === 0 && asub.asubType === model.value.asubType) || [];
        model.value.asubCode = (Number(roots[roots.length - 1].asubCode) + 1).toString();
    }
    asubCodeDiv.value && (asubCodeDiv.value.querySelector("input").style.paddingLeft = "");
}

function asubTypeChange() {
    if (model.value.parentId === 0) {
        const roots = asubs.value?.filter((asub) => asub.parentId === 0 && asub.asubType === model.value.asubType) || [];
        if (roots.length > 0) {
            model.value.asubCode = (Number(roots[roots.length - 1].asubCode) + 1).toString();
        } else {
            model.value.asubCode = "0001";
        }
    }
}
watch(
    () => model.value.parentId,
    (value: any) => {
        if (value === "") {
            model.value.parentId = undefined;
            return;
        }
        if (value === undefined || value === 0) {
            reset();
        } else {
            parentAsub.value = asubs.value?.find((asub) => asub.asubId === value);
            parentLevel2Asub.value = asubs.value?.find(
                (asub) => parentAsub.value && parentAsub.value.asubCode.startsWith(asub.asubCode) && asub.asubLevel === 2
            );
            parentAsubUsed.value = false;
            if (parentAsub.value && asubs.value) {
                let children = asubs.value.filter((asub) => asub.parentId+'' === model.value.parentId+'');
                let maxAsubCode = 0;
                for (let i = 0; i < children.length; i++) {
                    let asubCode = Number(children[i].asubCode.substring(parentAsub.value.asubCode.length));
                    maxAsubCode = Math.max(maxAsubCode, asubCode);
                }
                model.value.asubCode = (maxAsubCode + 1)
                    .toString()
                    .padStart(Number(asubLength.value.split("-")[parentAsub.value.asubLevel]), "0");
                model.value.asubType = parentAsub.value.asubType;
                model.value.direction = parentAsub.value.direction;
                model.value.restricted = parentAsub.value.restricted;
                model.value.quantityaccounting = parentAsub.value.quantityAccounting;
                model.value.measureunit = parentAsub.value.measureUnit;
                model.value.measureunitId = parentAsub.value.measureUnitId;
                model.value.assistingaccounting = parentAsub.value.assistingAccounting;
                model.value.aaTypes = parentAsub.value.aatypes;
                model.value.allowNullAaTypes = parentAsub.value.aatypesAllowNull;
                for (let i = 0; i < aatList.value.length; i++) {
                    aatList.value[i].selected =
                        model.value.aaTypes
                            .split(",")
                            .map((id) => Number(id))
                            .indexOf(aatList.value[i].id) !== -1;
                }
                model.value.foreigncurrency = parentAsub.value.foreigncurrency;
                model.value.fcadjust = parentAsub.value.fcAdjust;
                model.value.fcids = parentAsub.value.fcIds;
                for (let i = 0; i < fcList.value.length; i++) {
                    fcList.value[i].selected =
                        (model.value.fcids || "")
                            .split(",")
                            .map((id) => Number(id))
                            .indexOf(fcList.value[i].id) !== -1;
                }
                nextTick(() => {
                    asubCodeDiv.value.querySelector("input").style.paddingLeft =
                        asubCodeDiv.value.querySelector("span").clientWidth + 10 + "px";
                });
            }
            request({
                url: "/api/AccountSubject/CheckAusbUsedInVcAndGl?asubId=" + value,
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    parentAsubUsed.value = res.data;
                }
            });
        }
    }
);

watch(
    () => model.value.measureunitId,
    () => {
        if (model.value.measureunitId) {
            model.value.measureunit = unitList.value.find((item) => item.id === model.value.measureunitId)?.value || "";
        }
    }
);

defineExpose({
    showAADialog,
});

const codeLength = ref()
function getAsubCodeLength() {
    request({
        url: "/api/AccountSubject/GetAsubCodeLength",
        method: "post",
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            codeLength.value = res.data.codeLength;
            asubLength.value = res.data.codeLength.join("-");
            firstCodeLength.value = res.data.firstCodeLength;
        }
    });
}

onMounted(() => {
    getAsubCodeLength();
    window.addEventListener("updateAccountSubjectCodeLength", getAsubCodeLength);
    window.addEventListener("reloadCurrency", getCurrencyList);
});

onUnmounted(() => {
    window.removeEventListener("updateAccountSubjectCodeLength", getAsubCodeLength);
    window.removeEventListener("reloadCurrency", getCurrencyList);
});

const showSelectOptions = ref<Array<any>>([]);
const showAsubTypeList = ref<Array<any>>([]);
watchEffect(() => {
    showSelectOptions.value = JSON.parse(JSON.stringify(selectOptions.value));
    showAsubTypeList.value = JSON.parse(JSON.stringify(asubTypeList.value));
});
function subjectFilterMethod(value: string) {
    showSelectOptions.value = commonFilterMethod(value, selectOptions.value, 'label');
}
function asubTypeFilterMethod(value: string) {
    showAsubTypeList.value = commonFilterMethod(value, asubTypeList.value, 'name');
}
</script>
