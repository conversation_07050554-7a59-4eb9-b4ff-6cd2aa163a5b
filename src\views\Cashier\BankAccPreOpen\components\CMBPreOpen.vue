<template>
    <div class="slot-content">
        <div class="slot-mini-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #step_three>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="3" />
                                <div class="step-edit">
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="开户省份：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.openProvince"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectProvince"
                                                    >
                                                        <Option
                                                            v-for="item in props.provinceData"
                                                            :value="item.id"
                                                            :label="item.name"
                                                            :key="item.id"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="开户城市：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.openCity"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectCity"
                                                    >
                                                        <Option v-for="item in cityList" :value="item.id" :label="item.name" :key="item.id">
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="选择网点：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in bankOutlets"
                                                            :value="item.branchNo"
                                                            :label="item.bankName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToCMBInfo.branchAddr || companyToCMBInfo.branchTel">
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToCMBInfo.branchAddr">
                                                        {{ companyToCMBInfo.branchAddr }}
                                                    </div>
                                                    <div v-show="companyToCMBInfo.branchTel">
                                                        网点电话：{{ companyToCMBInfo.branchTel }}
                                                    </div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="190px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true">
                                                    <el-radio-group v-model="companyToCMBInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="预约开户币别：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.actCcyTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in actCcyTypes"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="预约开户账户类型：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.aplActTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in aplActTypes"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="back">上一步</a>
                                    <a class="button ml-28 solid-button" @click="toStepFour">下一步</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #step_four>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="4" />
                                <div class="step-edit">
                                    <div class="block-title">基本信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>系统会自动同步账套内的数据，请仔细核对。</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow">
                                                <el-form-item label="公司名称：" :required="true">
                                                    <el-autocomplete
                                                        @blur="handleCompanyBlur"
                                                        v-model="companyToCMBInfo.companyName"
                                                        :fetch-suggestions="querySearch"
                                                        :trigger-on-focus="false"
                                                        placeholder="请输入完整的单位名称"
                                                        style="width: 240px"
                                                        @select="selectName"
                                                    />
                                                </el-form-item>
                                                <el-form-item label="统一社会信用代码：" :required="true">
                                                    <el-input
                                                        v-model="companyToCMBInfo.unifiedNumber"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="公司证件类型：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.companyCertType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in companyCertTypes"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="公司证件号：" :required="true">
                                                    <el-input
                                                        v-model="companyToCMBInfo.companyCertNo"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">法人信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow">
                                                <el-form-item label="是否法人亲自办理：" :required="true">
                                                    <el-radio-group v-model="companyToCMBInfo.oprWhtrLgp">
                                                        <el-radio label="Y">是</el-radio>
                                                        <el-radio label="N">否</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人姓名：" :required="true">
                                                    <el-input
                                                        v-model="legalInfo.mbrNm"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                        @blur="handleNameBlur"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="法人手机号：" :required="true">
                                                    <el-input
                                                        v-model="legalInfo.mbrCtcTel"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件类型：" :required="true">
                                                    <Select
                                                        v-model="legalInfo.mbrDocTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in mbrDocTyps"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="法人证件号：" :required="true">
                                                    <el-input
                                                        v-model="legalInfo.mbrDocId"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line" v-show="companyToCMBInfo.oprWhtrLgp === 'N'"></div>
                                    <div class="block-title" v-show="companyToCMBInfo.oprWhtrLgp === 'N'">申请人信息</div>
                                    <div class="block-main" v-show="companyToCMBInfo.oprWhtrLgp === 'N'">
                                        <el-form :model="applyInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow">
                                                <el-form-item label="申请人姓名：" :required="true">
                                                    <el-input
                                                        v-model="applyInfo.mbrNm"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="申请人手机号：" :required="true">
                                                    <el-input
                                                        v-model="applyInfo.mbrCtcTel"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="申请人证件类型：" :required="true">
                                                    <Select
                                                        v-model="applyInfo.mbrDocTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in mbrDocTyps"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="申请人证件号：" :required="true">
                                                    <el-input
                                                        v-model="applyInfo.mbrDocId"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="申请人职务：" :required="true">
                                                    <el-radio-group v-model="companyToCMBInfo.applicantCPos">
                                                        <el-radio label="1">财务负责人</el-radio>
                                                        <el-radio label="2">其他</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_three'">上一步</a>
                                    <a class="button ml-28 solid-button longer-button" @click="toCofirmInfo">立即预约开户</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #confirm>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">确认预约开户详情</div>
                            <div class="open-main-content">
                                <div class="step-edit">
                                    <div class="block-title">开户银行</div>
                                    <div class="block-main">
                                        <el-row class="isRow"><div class="openbank-line">开户银行：招商银行</div> </el-row>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="开户省份：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.openProvince"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectProvince"
                                                    >
                                                        <Option
                                                            v-for="item in props.provinceData"
                                                            :value="item.id"
                                                            :label="item.name"
                                                            :key="item.id"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="开户城市：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.openCity"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectCity"
                                                    >
                                                        <Option v-for="item in cityList" :value="item.id" :label="item.name" :key="item.id">
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="选择网点：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectOutlet"
                                                    >
                                                        <Option
                                                            v-for="item in bankOutlets"
                                                            :value="item.branchNo"
                                                            :label="item.bankName"
                                                            :key="item.branchNo"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow" v-show="companyToCMBInfo.branchAddr || companyToCMBInfo.branchTel">
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToCMBInfo.branchAddr">
                                                        {{ companyToCMBInfo.branchAddr }}
                                                    </div>
                                                    <div v-show="companyToCMBInfo.branchTel">
                                                        网点电话：{{ companyToCMBInfo.branchTel }}
                                                    </div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="190px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true">
                                                    <el-radio-group v-model="companyToCMBInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="预约开户币别：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.actCcyTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in actCcyTypes"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="预约开户账户类型：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.aplActTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in aplActTypes"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">基本信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>系统会自动同步账套公司信息，请您检查是否准确</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow">
                                                <el-form-item label="公司名称：" :required="true">
                                                    <el-autocomplete
                                                        @blur="handleCompanyBlur"
                                                        v-model="companyToCMBInfo.companyName"
                                                        :fetch-suggestions="querySearch"
                                                        :trigger-on-focus="false"
                                                        placeholder="请输入完整的单位名称"
                                                        style="width: 240px"
                                                        @select="selectName"
                                                    />
                                                </el-form-item>
                                                <el-form-item label="统一社会信用代码：" :required="true">
                                                    <el-input
                                                        v-model="companyToCMBInfo.unifiedNumber"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="公司证件类型：" :required="true">
                                                    <Select
                                                        v-model="companyToCMBInfo.companyCertType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in companyCertTypes"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="公司证件号：" :required="true">
                                                    <el-input
                                                        v-model="companyToCMBInfo.companyCertNo"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">法人信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请补充如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToCMBInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow">
                                                <el-form-item label="是否法人亲自办理：" :required="true">
                                                    <el-radio-group v-model="companyToCMBInfo.oprWhtrLgp">
                                                        <el-radio label="Y">是</el-radio>
                                                        <el-radio label="N">否</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人姓名：" :required="true">
                                                    <el-input
                                                        v-model="legalInfo.mbrNm"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                        @blur="handleNameBlur"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="法人手机号：" :required="true">
                                                    <el-input
                                                        v-model="legalInfo.mbrCtcTel"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件类型：" :required="true">
                                                    <Select
                                                        v-model="legalInfo.mbrDocTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in mbrDocTyps"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="法人证件号：" :required="true">
                                                    <el-input
                                                        v-model="legalInfo.mbrDocId"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line" v-show="companyToCMBInfo.oprWhtrLgp === 'N'"></div>
                                    <div class="block-title" v-show="companyToCMBInfo.oprWhtrLgp === 'N'">申请人信息</div>
                                    <div class="block-main" v-show="companyToCMBInfo.oprWhtrLgp === 'N'">
                                        <el-form :model="applyInfo" label-position="right" label-width="180px">
                                            <el-row class="isRow">
                                                <el-form-item label="申请人姓名：" :required="true">
                                                    <el-input
                                                        v-model="applyInfo.mbrNm"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                                <el-form-item label="申请人手机号：" :required="true">
                                                    <el-input
                                                        v-model="applyInfo.mbrCtcTel"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="申请人证件类型：" :required="true">
                                                    <Select
                                                        v-model="applyInfo.mbrDocTyp"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                    >
                                                        <Option
                                                            v-for="item in mbrDocTyps"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="申请人证件号：" :required="true">
                                                    <el-input
                                                        v-model="applyInfo.mbrDocId"
                                                        style="width: 240px"
                                                        placeholder="请输入"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="申请人职务：" :required="true">
                                                    <el-radio-group v-model="companyToCMBInfo.applicantCPos">
                                                        <el-radio label="1">财务负责人</el-radio>
                                                        <el-radio label="2">其他</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_four'">上一步</a>
                                    <a class="button ml-28 solid-button" @click="saveInfo">确认无误</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </ContentSlider>
        </div>
        <PreOpenResDialog v-model="openResultDialog" bank="招商银行" @close="toMain" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getCompanyDetailApi } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { checkLegalPersonName, cancelConfirm, checkCompanyInfo } from "../utils";
import { Member, CMBCompanyInfoModel, aplActTypes, companyCertTypes, mbrDocTyps, actCcyTypes } from "../types";

import type { IArea, IBaseCompanyInfo } from "../types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import TopStep from "./TopStep.vue";
import PreOpenResDialog from "./PreOpenResDialog.vue";
import { cloneDeep } from "lodash";

const asId = useAccountSetStore()?.accountSet!.asId;

const props = defineProps<{
    provinceData: Array<IArea>;
    systemType: number;
}>();
const emit = defineEmits<{
    (event: "back", baseCompanyInfo: IBaseCompanyInfo): void;
    (event: "success"): void;
}>();

const slots = ["step_three", "step_four", "confirm"];
const currentSlot = ref("step_three");
interface ICityItem {
    id: string;
    name: string;
    hotCity: boolean;
    cityPinyin: string;
    cityShortPy: string;
    provinceId: string;
}
interface ICMBBranchItem {
    branchNo: string; //网点号
    bankName: string; //网点名称
    branchAddr: string; //网点地址
    branchTel: string; //网点电话
    business: string; //营业时间
}
const cityList = ref<Array<ICityItem>>([]);
const bankOutlets = ref<Array<ICMBBranchItem>>([]);
const usePioIdList = ["110100", "310100", "120100", "500100"];
const companyToCMBInfo = reactive(new CMBCompanyInfoModel());
const legalInfo = reactive(new Member("1"));
const applyInfo = reactive(new Member("2"));

const queryParams = reactive({
    isFromDb: false,
    name: "",
    data: [],
});
function querySearch(queryString: string, cb: any) {
    getCompanyList(1010, queryString, cb, queryParams);
}

const rightLegalPersonName = ref("");
function selectName(item: any) {
    companyToCMBInfo.companyName = item.value;
    companyToCMBInfo.unifiedNumber = item.creditCode;
    companyToCMBInfo.legalName = rightLegalPersonName.value = item.legalPersonName;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}
function handleCompanyBlur(event: any) {
    getCompanyDetailApi(1010, decodeURIComponent(event.target.value)).then((res: any) => {
        if (res.state !== 1000) return;
        rightLegalPersonName.value = res.data?.legalPersonName ?? "";
    });
}
function getCityList(proId: string) {
    request({ url: `/api/City/CityList?proId=${proId}` }).then((res: IResponseModel<Array<ICityItem>>) => {
        res.state === 1000 && (cityList.value = res.data);
    });
}
function getCMBBranch(cityCode: string, cityName: string) {
    const data = { cityCode, cityName };
    if (usePioIdList.includes(cityCode)) {
        // 直辖市需要传省一级的id
        data.cityCode = cityCode.substring(0, 3) + "000";
    }
    request({ url: window.preOpenBankUrl + "/api/CMBPreOpen/QueryBranch", method: "post", data }).then(
        (res: IResponseModel<Array<ICMBBranchItem>>) => {
            if (res.state !== 1000) return;
            bankOutlets.value = res.data;
        }
    );
}
function selectProvince(val: string) {
    companyToCMBInfo.openCity = "";
    clearBranchInfo();
    getCityList(val);
}
function clearBranchInfo() {
    const assignInfo = {
        branchName: "",
        branchNo: "",
        branchAddr: "",
        branchTel: "",
    };
    bankOutlets.value.length = 0;
    Object.assign(companyToCMBInfo, assignInfo);
}
function selectCity(cityCode: string) {
    clearBranchInfo();
    const cityName = cityList.value.find((item: any) => item.id === cityCode)?.name || "";
    getCMBBranch(cityCode, cityName);
}
function selectOutlet(val: string) {
    const outlet = bankOutlets.value.find((item) => item.branchNo === val);
    if (outlet) {
        const { branchAddr = "", branchTel = "", bankName = "" } = outlet;
        const assignInfo = { branchAddr, branchTel, branchName: bankName };
        Object.assign(companyToCMBInfo, assignInfo);
    }
}
function back() {
    cancelConfirm().then((r) => {
        if (!r) return;
        const replaceInfo = {
            companyName: companyToCMBInfo.companyName,
            socialCreditCode: companyToCMBInfo.unifiedNumber,
            legalPersonName: companyToCMBInfo.legalName,
            legalPhone: companyToCMBInfo.legalMobile,
            rightLegalPersonName: rightLegalPersonName.value,
            accountType: companyToCMBInfo.accountType,
        };
        emit("back", replaceInfo);
        saveCancelInfo();
    });
}
function saveCancelInfo() {
    request({
        url: window.preOpenBankUrl + "/api/CMBPreOpen/Save",
        method: "post",
        data: getOpenParams(),
    });
}
function toStepFour() {
    if (!checkStepThreeCanNext()) return;
    currentSlot.value = "step_four";
}
function toCofirmInfo() {
    if (!checkCompanyInfo(companyToCMBInfo)) return;
    if (!checkCMBCompanyInfo()) return;
    if (!checkStepFourCanNext()) return;
    currentSlot.value = "confirm";
}

const isCanToNext = ref(true);
function handleNameBlur() {
    companyToCMBInfo.legalName = legalInfo.mbrNm;
    if (!checkLegalPersonName(companyToCMBInfo.legalName, rightLegalPersonName.value)) {
        isCanToNext.value = false;
    }
    const timer = setTimeout(() => {
        isCanToNext.value = true;
        clearTimeout(timer);
    }, 200);
}
const openResultDialog = ref(false);
let canClickSaveInfo = true;
function saveInfo() {
    if (!isCanToNext.value) return;
    if (!canClickSaveInfo) return;
    if (!checkAllInfo()) return;
    if (!checkLegalPersonName(companyToCMBInfo.legalName, rightLegalPersonName.value)) return;
    canClickSaveInfo = false;
    const data = getOpenParams();
    request({ url: window.preOpenBankUrl + "/api/CMBPreOpen/Open", method: "post", data })
        .then((res: any) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg });
                return;
            }
            openResultDialog.value = true;
        })
        .finally(() => {
            canClickSaveInfo = true;
        });
}
function toMain() {
    currentSlot.value = "step_three";
    emit("success");
    resetInfo();
}

function initBaseCompanyInfo(data: IBaseCompanyInfo) {
    const {
        companyName,
        socialCreditCode,
        legalPersonName,
        legalPhone,
        accountType,
        rightLegalPersonName: rightName,
        taxProvinceName,
        taxProvinceCode,
    } = data;
    const assignInfo = {
        companyName,
        unifiedNumber: socialCreditCode,
        legalName: legalPersonName,
        legalMobile: legalPhone,
        accountType,
    };
    Object.assign(companyToCMBInfo, assignInfo);
    rightLegalPersonName.value = rightName;
    legalInfo.mbrNm = legalPersonName;
    legalInfo.mbrCtcTel = legalPhone;
    if (taxProvinceName && companyToCMBInfo.openProvince === "") {
        let province = props.provinceData.find((item) => item.name === taxProvinceName);
        if (!province && taxProvinceCode) {
            province = props.provinceData.find((item) => ~~item.id === ~~taxProvinceCode);
        }
        if (province) {
            companyToCMBInfo.openProvince = province.id;
            getCityList(province.id);
        }
    }
}
function initCancelCMBInfo(data: any) {
    Object.assign(companyToCMBInfo, data);
    companyToCMBInfo.openProvince && getCityList(data.openProvince);
    companyToCMBInfo.openCity && getCMBBranch(data.openCity, data.branchCityName);
    companyToCMBInfo.branchNo && selectOutlet(data.branchNo);
}
function resetInfo() {
    Object.assign(companyToCMBInfo, new CMBCompanyInfoModel());
    rightLegalPersonName.value = "";
}
defineExpose({ initBaseCompanyInfo, initCancelCMBInfo, resetInfo, saveCancelInfo });

function getOpenParams() {
    const params = { ...companyToCMBInfo };
    params.members = cloneDeep([legalInfo, applyInfo]);
    if (params.oprWhtrLgp === "Y") {
        params.members.pop();
        params.applicantCPos = "0";
        params.applicantName = legalInfo.mbrNm;
        params.applicantMobile = legalInfo.mbrCtcTel;
    } else {
        const applyInfo = { ...params.members[1] };
        params.applicantName = applyInfo.mbrNm;
        params.applicantMobile = applyInfo.mbrCtcTel;
    }
    return {
        id: "",
        asId: asId + "",
        system: props.systemType,
        content: params,
    };
}
function checkStepThreeCanNext() {
    if (!companyToCMBInfo.openProvince) {
        ElNotify({ type: "warning", message: "请选择开户省份" });
        return false;
    }
    if (!companyToCMBInfo.openCity) {
        ElNotify({ type: "warning", message: "请选择开户城市" });
        return false;
    }
    if (!companyToCMBInfo.branchNo) {
        ElNotify({ type: "warning", message: "请选择选择网点" });
        return false;
    }
    if (companyToCMBInfo.accountType !== 0 && companyToCMBInfo.accountType !== 1) {
        ElNotify({ type: "warning", message: "请选择账户类型" });
        return false;
    }
    if (!companyToCMBInfo.actCcyTyp) {
        ElNotify({ type: "warning", message: "请选择预约开户币别" });
        return false;
    }
    if (!companyToCMBInfo.aplActTyp) {
        ElNotify({ type: "warning", message: "请选择预约开户账户类型" });
        return false;
    }
    return true;
}
function checkCMBCompanyInfo() {
    if (!companyToCMBInfo.companyCertType) {
        ElNotify({ type: "warning", message: "请选择公司证件类型" });
        return false;
    }
    if (!companyToCMBInfo.companyCertNo.trim()) {
        ElNotify({ type: "warning", message: "请输入公司证件号" });
        return false;
    }
    return true;
}
function checkStepFourCanNext() {
    if (!legalInfo.mbrNm.trim()) {
        ElNotify({ type: "warning", message: "请输入法人姓名" });
        return false;
    }
    if (!legalInfo.mbrCtcTel.trim()) {
        ElNotify({ type: "warning", message: "请输入法人手机号" });
        return false;
    }
    if (!legalInfo.mbrDocTyp.trim()) {
        ElNotify({ type: "warning", message: "请选择法人证件类型" });
        return false;
    }
    if (!legalInfo.mbrDocId.trim()) {
        ElNotify({ type: "warning", message: "请输入法人证件号" });
        return false;
    }
    if (companyToCMBInfo.oprWhtrLgp === "N") {
        if (!applyInfo.mbrNm.trim()) {
            ElNotify({ type: "warning", message: "请输入申请人姓名" });
            return false;
        }
        if (!applyInfo.mbrCtcTel.trim()) {
            ElNotify({ type: "warning", message: "请输入申请人手机号" });
            return false;
        }
        if (!applyInfo.mbrDocTyp.trim()) {
            ElNotify({ type: "warning", message: "请选择申请人证件类型" });
            return false;
        }
        if (!applyInfo.mbrDocId.trim()) {
            ElNotify({ type: "warning", message: "请输入申请人证件号" });
            return false;
        }
    }
    return true;
}
function checkAllInfo() {
    if (!checkStepThreeCanNext()) return false;
    if (!checkCompanyInfo(companyToCMBInfo)) return false;
    if (!checkCMBCompanyInfo()) return false;
    if (!checkStepFourCanNext()) return false;
    return true;
}
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
