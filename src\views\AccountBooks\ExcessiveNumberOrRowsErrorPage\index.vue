<template>
    <div>
        <img src="@/assets/AccountBooks/error.png" style="margin-top: 120px" />
        <div style="margin: 20px auto; font-size: 14px; line-height: 24px">
            您选择导出或打印的数据太多了，请在会计期间处重新筛选后再导出或打印哦~
        </div>
        <a class="button solid-button" @click="reload()">确定</a>
    </div>
</template>
<script setup lang="ts">
import { useRoute } from "vue-router";
import { globalWindowOpenPage } from "@/util/url";
import { ref } from "vue";

//const maxRowNumber = ref(window.maxRowNumber);
const route = useRoute();
const reload = () => {
    let sourcePagePath = route.query.sourcePagePath?.toString() || "/Default/Default";
    const sourcePageName = route.query.sourcePageName?.toString() || "首页";
    globalWindowOpenPage(sourcePagePath, sourcePageName);
};
</script>
