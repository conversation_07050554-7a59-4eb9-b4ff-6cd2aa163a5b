<template>
    <el-popover
        ref="AccountsetListPopperRef"
        popper-class="accountset-list-popper"
        placement="bottom-start"
        :visible="visible"
        :teleported="false"
        :width="600"
    >
        <template #reference>
            <div :class="['accountSet-list-trigger', { show: visible }]" @click="() => (visible = !visible)">
                <p v-if="props.currentList.length === 0" class="placeholder">下拉选择第三方财务软件账套</p>
                <div v-if="props.currentList.length > 0" class="currentList-tabs" :title="accountsetTabName">
                    {{ accountsetTabName }}
                </div>
                <div class="dropdown-icon"></div>
            </div>
        </template>
        <div class="accountSet-dropDownList">
            <div class="search-box">
                <input
                    v-model.trim="searchKeyWord"
                    @keyup.enter="handleSearch"
                    type="text"
                    class="search-ipt"
                    placeholder="请输入关键词搜索"
                />
                <div @click="handleSearch" class="search-icon"></div>
            </div>
            <Table
                :class="isAccountingAgent ? 'aa-table' : ''"
                ref="accountSetTableRef"
                :height="230"
                :data="tableData"
                :columns="tableColumns"
                :scrollbarShow="true"
                :page-is-show="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :currentPage="paginationData.currentPage"
                :empty-text="emptyText"
                :showOverflowTooltip="true"
                :customPageStartEnd="customPageStartEnd"
                @selection-change="handleSelectChange"
                @select-all="handleSelectAll"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @refresh="handleRerefresh"
                :tableName="setModule"
            >
                <template #name>
                    <el-table-column
                        label="账套名称"
                        min-width="200"
                        align="left"
                        headerAlign="center"
                        prop="name"
                        sortable
                        :sort-method="customSortByName"
                        :width="getColumnWidth(setModule, 'name')"
                    >
                    </el-table-column>
                </template>
                <template #accountingStandardName>
                    <el-table-column
                        v-if="!hideAccountStandard"
                        label="准则"
                        min-width="110"
                        align="left"
                        headerAlign="center"
                        prop="accountingStandardName"
                        sortable
                        :sort-method="customSortByStandardName"
                        :width="getColumnWidth(setModule, 'accountingStandardName')"
                    >
                    </el-table-column>
                </template>
                <template #isImport>
                    <el-table-column
                        v-if="showIsImport"
                        label="是否已导账"
                        min-width="100"
                        align="left"
                        headerAlign="center"
                        sortable
                        sort-by="isImport"
                        :resizable="false"
                    >
                        <template #default="scope">
                            {{ scope.row.isImport === 1 ? "是" : "否" }}
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
    </el-popover>
</template>
<script lang="ts" setup>
import { ref, watch, computed, nextTick } from "vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IAccountSetInfo } from "../types";
import { ElNotify } from "@/util/notify";
import { usePagination } from "@/hooks/usePagination";
import { pinyin } from "pinyin-pro";

const isAccountingAgent = window.isAccountingAgent;
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "ImportAccountList";
const props = withDefaults(
    defineProps<{
        currentList: IAccountSetInfo[];
        accountsetsList: IAccountSetInfo[];
    }>(),
    {
        currentList: () => [],
        accountsetsList: () => [],
    }
);
const emits = defineEmits<{
    (e: "changeAccountsetList", val: IAccountSetInfo[]): void;
}>();

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

// 本地数据
const tableData = ref(props.accountsetsList);
const tableColumns: IColumnProps[] = [
    { slot: "selection" },
    { slot: "name" },
    { slot: "accountingStandardName" },
    {
        slot: "isImport",
    },
];

const AccountsetListPopperRef = ref();
// 是否显示下拉框
const visible = ref(false);
// 关键词
const searchKeyWord = ref("");
// 暂无数据文字
const emptyText = ref("暂无数据");
// 表格ref
const accountSetTableRef = ref();

// 当前选择列表
const selectSet = new Set(props.currentList);
let waiting = false;
// 是否全选
let isAllSelect = false;
// 选择变化
const handleSelectChange = (val: IAccountSetInfo[]) => {
    if (val.length > 0) {
        const newItem = val[val.length - 1];
        // 如果不支持导账
        if (newItem.accountingStandard === 0) {
            const index = tableData.value.findIndex((item) => item.id === newItem.id);
            const noticeMsg =
                newItem.accountingStandardName.length > 0
                    ? `${newItem.accountingStandardName}准则暂不支持导账`
                    : `该账套没有准则或该会计准则暂不支持导账`;
            accountSetTableRef.value.toggleRowSelect(index, false);

            if (!waiting) {
                ElNotify({ type: "warning", message: noticeMsg });
                waiting = true;
                setTimeout(() => {
                    waiting = false;
                }, 3000);
            }
        } else {
            if (isAccountingAgent && val.length > 1) {
                const oldIndex = tableData.value.findIndex((item) => item.id === val[0].id);
                if (oldIndex !== -1) {
                    accountSetTableRef.value.toggleRowSelect(oldIndex, false);
                }
                val = [newItem];
            }
        }
    } else {
        isAllSelect = false;
    }
    for (let i of tableData.value) {
        if (val.includes(i) && i.accountingStandard !== 0) {
            if (isAccountingAgent) {
                selectSet.clear();
            }
            selectSet.add(i);
        } else {
            selectSet.delete(i);
        }
    }
};
// 全选
const handleSelectAll = (selection: IAccountSetInfo[]) => {
    waiting = true;
    setTimeout(() => {
        waiting = false;
    }, 3000);

    if (isAllSelect === false) {
        for (let i in selection) {
            const index = tableData.value.findIndex((item) => item.id === selection[i].id);
            if (selection[i].accountingStandard === 0) {
                accountSetTableRef.value.toggleRowSelect(index, false);
                selectSet.delete(tableData.value[index]);
            } else {
                accountSetTableRef.value.toggleRowSelect(index, true);
                selectSet.add(tableData.value[index]);
            }
        }
        isAllSelect = true;
    } else {
        accountSetTableRef.value.clearSelection();
        isAllSelect = false;
    }
};
// 搜索关键词变化
const onKeyWordChange = () => {
    if (searchKeyWord.value === "") {
        tableData.value = props.accountsetsList;
    } else {
        const reg = new RegExp(searchKeyWord.value, "i");
        tableData.value = props.accountsetsList.filter((item) => reg.test(item.name));
    }
};
// 已选择账套名
const accountsetTabName = computed(() => {
    let tabName = "";
    if (props.currentList.length > 0) {
        for (let i of props.currentList) {
            tabName += i.name + ";";
        }
    }
    return tabName;
});

// 表格列
// 显示是否已导账
const showIsImport = computed(() => {
    return tableData.value.some((item) => item.isImport !== -1);
});
// 是否显示账套准则
const hideAccountStandard = computed(() => {
    return tableData.value.every((item) => item.accountingStandardName === "");
});

// 分页器变动
const customPageStartEnd = ref<number[]>([]);
const handlePageChange = () => {
    // 总条数
    paginationData.total = tableData.value.length;

    const startIndex = paginationData.pageSize * (paginationData.currentPage - 1);
    const endIndex = startIndex + paginationData.pageSize;
    tableData.value = tableData.value.slice(startIndex, endIndex);
    customPageStartEnd.value = [startIndex, endIndex];

    accountSetTableRef.value.tableScrollTo(0, 0);

    for (let i in tableData.value) {
        if (selectSet.has(tableData.value[i])) {
            nextTick(() => {
                accountSetTableRef.value.toggleRowSelect(i, true);
            });
        }
    }
};

const handleSearch = () => {
    if (paginationData.currentPage === 1) {
        onKeyWordChange();
        handlePageChange();
    } else {
        paginationData.currentPage = 1;
    }
};

// 排序
const convertToPinyinFirstLetter = (str: string) => {
    if (typeof str === "number" && !isNaN(str)) {
        return str; // 如果是数字，直接返回
    } else {
        return pinyin(str).substring(0, 1).toUpperCase();
    }
};

const customSortByName = (a: IAccountSetInfo, b: IAccountSetInfo) => {
    const aName = a.name.trim()[0];
    const bName = b.name.trim()[0];
    if (typeof aName === "number" && typeof bName === "number" && !isNaN(aName) && !isNaN(bName)) {
        return aName - bName; // 数字排序
    } else {
        const pinyinA = convertToPinyinFirstLetter(aName);
        const pinyinB = convertToPinyinFirstLetter(bName);
        return pinyinA.localeCompare(pinyinB);
    }
};

const customSortByStandardName = (a: IAccountSetInfo, b: IAccountSetInfo) => {
    const aName = a.accountingStandardName.trim()[0];
    const bName = b.accountingStandardName.trim()[0];
    if (typeof aName === "number" && typeof bName === "number" && !isNaN(aName) && !isNaN(bName)) {
        return aName - bName; // 数字排序
    } else {
        const pinyinA = convertToPinyinFirstLetter(aName);
        const pinyinB = convertToPinyinFirstLetter(bName);
        return pinyinA.localeCompare(pinyinB);
    }
};

// 点击空白处关闭弹窗
const closeDialog = (event: MouseEvent) => {
    // 检查点击区域是否在 popover 外  并且不在触发器上
    if (!(event.target as any).closest(".accountset-list-popper") && !(event.target as any).closest(".accountSet-list-trigger")) {
        visible.value = false;
    }
};

watch(
    () => visible.value,
    () => {
        if (visible.value) {
            searchKeyWord.value = "";
            tableData.value = props.accountsetsList;
            if (!props.currentList.length) {
                selectSet.clear();
            }
            handlePageChange();

            document.addEventListener("click", closeDialog);

            // 代账版不允许多选
            if (isAccountingAgent) {
                const selectAllCheckbox = accountSetTableRef.value?.$refs.TableComponents.$refs.headerWrapper.querySelector(
                    ".el-table__header .el-checkbox__input"
                );
                selectAllCheckbox?.remove();
            }
        } else {
            //  点击空白处关闭下拉框 事件绑定与移除
            emits("changeAccountsetList", [...selectSet]);
            document.removeEventListener("click", closeDialog);
        }
    }
);

// 切换分页
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    onKeyWordChange();
    handlePageChange();
});

watch(
    () => props.accountsetsList,
    () => {
        // 重置
        selectSet.clear();
        paginationData.currentPage = 1;
    }
);
</script>
<style lang="less">
.accountSet-list-trigger {
    position: relative;
    width: 270px;
    height: 32px;
    font-size: 14px;
    border: 1px solid #edefee;
    line-height: 32px;
    text-align: left;
    padding: 0 20px 0 12px;
    box-sizing: border-box;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &.show,
    &:hover {
        border: 1px solid var(--main-color);
    }
    .placeholder {
        color: #929292;
        user-select: none;
        margin: 0;
    }
    .currentList-tabs {
        display: flex;
        align-items: center;
        width: 235px;
        height: 32px;
        overflow: hidden;
        .currentList-tabs-item {
            padding: 3px 10px;
            margin-right: 10px;
            font-size: 14px;
            line-height: 20px;
            box-sizing: border-box;
            color: #909399;
            background-color: #f0f2f5;
            border-radius: 10px;
            user-select: none;
        }
    }
    .dropdown-icon {
        position: absolute;
        top: 10px;
        right: 8px;
        width: 12px;
        height: 11px;
        background: url(@/assets/Settings/arrow-down.png) no-repeat 0 0;
        background-size: 11px 11px;
    }
}
.accountset-list-popper {
    padding: 14px 20px;
    box-sizing: border-box;
    .accountSet-dropDownList {
        position: relative;
        width: 570px;
        // height: 330px;
        .search-box {
            position: relative;
            margin-bottom: 8px;
            .search-ipt {
                width: 570px;
                height: 32px;
                line-height: 28px;
                padding: 0 16px;
                background: #ffffff;
                border-radius: 2px;
                border: 1px solid #e6e6e6;
                box-sizing: border-box;
            }
            .search-icon {
                position: absolute;
                top: 6px;
                right: 12px;
                content: "";
                width: 15px;
                height: 16px;
                background: url(@/assets/Settings/search-icon.png) no-repeat 0 0;
                background-size: 15px 16px;
            }
        }
        .aa-table {
            .el-checkbox__input {
                border-radius: 50%;
                border: 1px solid var(--main-color);
                .el-checkbox__inner {
                    border-radius: 50%;
                    border: 2px solid #fff;
                    &::after {
                        display: none;
                    }
                }
            }
        }
    }
}
// 遮罩层
.mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
</style>
<style>
.accountset-list-popper.el-popover.el-popper > .el-popper__arrow {
    &::before {
        right: 113px;
    }
}
</style>
