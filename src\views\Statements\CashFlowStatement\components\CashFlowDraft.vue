<template>
    <div class="main-content">
        <div class="main-top main-tool-bar space-between" style="width: 1100px; margin: 0 auto">
            <div>
                <CheckOutTooltip v-if="periodIsCheckOut" :isDraft="true"></CheckOutTooltip>
            </div>
            <div class="main-tool-right">
                <div class="isclass-box">
                    <el-checkbox v-model="isclass" label="开启重分类"></el-checkbox>
                    <div class="warning" @click="isWarning"></div>
                </div>
                <a class="button ml-10" @click="resetHandle" v-if="flag" v-permission="['cashflowstatement-canedit']">重置</a>
                <a class="button solid-button ml-10" @click="draftSaveHandle" v-permission="['cashflowstatement-canedit']">保存</a>
                <a class="button ml-10 mr-10" @click="draftCancelHandle">取消</a>
                <Dropdown :btnTxt="'打印'" :downlistWidth="102" v-permission="['cashflowstatement-canprint']">
                    <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                    <li @click="handlePrint(2)">打印设置</li>
                </Dropdown>
            </div>
        </div>
        <div class="main-center" style="width: 1100px; margin: 0 auto">
            <el-table
                :data="tableData"
                border
                v-loading="loading"
                element-loading-text="正在加载数据..."
                :empty-text="emptyText"
                fit
                stripe
                highlight-current-row
                scrollbar-always-on
                :class="isErp ? 'erp-table' : 'custom-table'"
                row-key="lineID"
                :tooltipOptions="{ effect: 'light', placement: 'bottom' }"
                :row-class-name="setTitleRowStyle"
                @header-dragend="headerDragend"
            >
                <el-table-column 
                    label="项目" 
                    min-width="450" 
                    align="left" 
                    headerAlign="left"
                    prop="lineName"
                    :width="getColumnWidth(setModule, 'lineName')"
                >
                    <template #default="scope">
                        <div :title="scope.row.lineName" :class="assertNameClass(scope.row)">
                            {{ scope.row.lineName }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="行次"
                    min-width="68"
                    align="left"
                    headerAlign="left"
                    prop="lineNumber"
                    :formatter="rowNumberFormatter"
                    :width="getColumnWidth(setModule, 'lineNumber')"
                >
                </el-table-column>
                <el-table-column 
                    label="本期金额" 
                    min-width="250" 
                    align="right" 
                    header-align="center"
                    prop="amount"
                    :width="getColumnWidth(setModule, 'amount')"
                >
                    <template #default="scope">
                        <!-- <span v-if="scope.row.lineType === 3 && scope.row.amount"> {{ formatMoney(scope.row.amount) || "" }}</span> -->
                        <!-- <template v-if="scope.row.lineType !== 3"> -->
                        <span
                            v-if="
                                isCheckOut ||
                                !checkPermission(['cashflowstatement-canedit']) ||
                                [10041360, 20041640].includes(scope.row.lineID) ||
                                scope.row.lineType === 3
                            "
                            style="width: 100%"
                            >{{ formatMoney(scope.row.amount) || "" }}</span
                        >
                        <div class="ipt-box" v-else>
                            <input
                                type="text"
                                class="draft-input"
                                v-decimal-limit="[15, 2]"
                                :value="formatMoney(scope.row.amount) || ''"
                                :disabled="iptDisabled(scope.row)"
                                @change="(e) => changeValue(scope.row.lineID, e)"
                            />
                            <div v-if="iptDisabled(scope.row)" class="ipt-mask" @click="onDraftInputClick(scope.row)"></div>
                        </div>
                        <!-- </template> -->
                    </template>
                </el-table-column>
                <el-table-column 
                    label="操作" 
                    min-width="170" 
                    align="left" 
                    header-align="center"
                    :resizable="false"
                >
                    <template #default="scope">
                        <template
                            v-if="!isCheckOut && scope.row.lineType !== 3 && scope.row.lineID != 10041360 && scope.row.lineID != 20041640"
                        >
                            <div v-if="!iptDisabled(scope.row)" class="handle">
                                <Note
                                    :Amount="scope.row.amount"
                                    :LineName="scope.row.lineName"
                                    v-model="scope.row.remark"
                                    @update:model-value="changeRemark($event, scope.row.lineID)"
                                />
                                <a
                                    class="link"
                                    style="margin-left: 60px"
                                    v-show="scope.row.createdManually === 1"
                                    @click="deleteHandle(scope.row.lineID)"
                                    >重置</a
                                >
                            </div>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <el-dialog title="提示" width="440px" v-model="dialogShow" class="custom-confirm dialogDrag">
        <div class="dialog-content" v-dialogDrag>
            <div class="dialog-main">
                <span v-if="useAccountSetStore().accountSet?.accountingStandard === 3"
                    >开启重分类，系统会根据公式法自动计算的“支付的其他与业务活动有关的现金”的负数余额，自动重分类到“收到的其他与业务活动有关的现金”。</span
                >
                <span v-else>
                    开启重分类，系统会根据公式法自动计算的“支付其他与经营活动有关的现金”的负数余额，自动重分类到“收到其他与经营活动有关的现金”。
                </span>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="closeDialog">确定</a>
            </div>
        </div>
    </el-dialog>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="现金流量表底稿打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    ></StatementsPrint>
</template>

<script setup lang="ts">
import { ref, watch, computed, reactive, nextTick } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { formatMoney } from "@/util/format";
import usePrint from "@/hooks/usePrint";
import { hasNumberTitle, rowNumberFormatter } from "../utils";
import Note from "./Note.vue";
import { getGlobalLodash } from "@/util/lodash";
import { useAccountSetStore, useAccountSetStoreHook } from "@/store/modules/accountSet";
import type { IAdjustFlowSheet, IState } from "../types";
import { checkPermission } from "@/util/permission";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { globalPrint } from "@/util/url";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";
import Dropdown from "@/components/Dropdown/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";

const setModule = "CashFlowDraft";
const props = defineProps<{ modelValue: boolean; state: IState; periodIsCheckOut: boolean }>();
const isErp = ref(window.isErp);
const emit = defineEmits<{
    (e: "update:modelValue", value: boolean): void;
    (e: "draft-save"): void;
    (e: "draft-cancel"): void;
    (e: "draft-print"): void;
}>();
const _ = getGlobalLodash()
const isclass = computed({
    get() {
        return props.modelValue;
    },
    set(value: boolean) {
        emit("update:modelValue", value);
        localStorage.setItem("cashclassificationSwitch", String(value));
        window.dispatchEvent(new CustomEvent("cashclassificationSwitchChange", { detail: { isclass: value } }));
    },
});

const iptDisabled = computed(() => (row: { lineID: number }) => {
    return isclass.value && (row.lineID === 10040160 || row.lineID === 10040780 || row.lineID === 20040200 || row.lineID === 20040870);
});

const isChange = ref<boolean>(false);
const tableData = ref<IAdjustFlowSheet[]>([]);
let tableOriginData = ref<IAdjustFlowSheet[]>([]);
const dialogShow = ref<boolean>(false);
const searchInfo = reactive<any>({
    pid: props.state.pid,
    isTax: false,
    calmethod: "1",
});
const flag = ref(false);
const loading = ref(false);
const emptyText = ref("");
const getTableList = () => {
    loading.value = true;
    emptyText.value = " ";
    request({
        url: "/api/CashFlowStatement/AdjustFlowSheet",
        method: "get",
        params: {
            PId: searchInfo.pid,
            CalMethod: searchInfo.calmethod,
            IsTax: searchInfo.isTax,
            IsClass: isclass.value,
        },
    })
        .then((res: IResponseModel<IAdjustFlowSheet[]>) => {
            tableData.value = [];
            tableOriginData.value = [];
            if (res.state === 1000 && res.data.length) {
                let parent = null;
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children!.push(element);
                        continue;
                    }
                    tableData.value.push(element);
                }
                let editIndex = tableData.value.findIndex((item: IAdjustFlowSheet) => item.createdManually === 1);
                flag.value = editIndex >= 0;
                loading.value = false;
                if (!tableData.value.length) {
                    emptyText.value = "暂无数据";
                }
                tableOriginData.value = _.cloneDeep(tableData.value);
                nextTick().then(() => {
                    init = true;
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
};

const closeDialog = () => (dialogShow.value = false);

const isWarning = () => {
    isclass.value = !isclass.value;
    dialogShow.value = true;
};
let changeRowIndexList: number[] = [];
const changeValue = (lineID: any, e: any) => {
    let valueStr = e.target.value.replace(/,/g, "");
    const val = formatMoney(valueStr) || "0.00";
    const index = tableData.value.findIndex((item) => item.lineID == lineID);
    tableData.value[index].amount = Number(valueStr) || 0.0;
    changeRowIndexList.push(index);
    const parentLevel1 = tableData.value
        .slice(0, index)
        .reverse()
        .find((item) => item.lineType === 3 && item.lineNumber !== 0) as IAdjustFlowSheet;
    const parentLevel1Index = tableData.value.findIndex((v) => v.lineID === parentLevel1.lineID);
    parentLevel1.amount =
        tableOriginData.value[parentLevel1Index].amount - tableOriginData.value[index].amount + tableData.value[index].amount;
    tableOriginData.value[index].amount = tableData.value[index].amount;
    tableOriginData.value[parentLevel1Index].amount = parentLevel1.amount;
};
const changeRemark = (e: string, lineID: any) => {
    const index = tableData.value.findIndex((item) => item.lineID == lineID);
    changeRowIndexList.push(index);
};
const draftSaveSubmit = () => {
    let requestTableData = _.cloneDeep(tableData.value);
    changeRowIndexList.forEach((v) => (requestTableData[v].createdManually = 1));
    request({
        url: "/api/CashFlowStatement/AdjustFlowSheet",
        params: { pId: searchInfo.pid },
        method: "put",
        headers: {
            "Content-Type": "application/json",
        },
        data: JSON.stringify(requestTableData),
        // IResponseModel<boolean>
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            ElNotify({
                type: "success",
                message: "更新成功啦!",
            });
            getTableList();
            resetInit()
            emit("draft-save");
        } else {
            ElNotify({
                type: "warning",
                // res.message
                message: res.msg || "请输入正确数值",
            });
        }
    });
};
const debounce = (fn: Function, delay: number, immediate = true) => {
    let timer: any = null;
    let delayTime = delay || 3000;
    if (timer) {
        clearTimeout(timer);
    }
    return function () {
        if (timer) {
            ElNotify({
                type: "warning",
                message: "亲，您操作过快",
            });
            return;
        }
        if (immediate) {
            let bool = !timer;
            timer = setTimeout(() => (timer = null), delayTime);
            return bool && fn();
        }
    };
};
const draftSaveHandle = debounce(draftSaveSubmit, 1000);

const deleteHandle = (lineId: number) => {
    if (!useAccountSetStoreHook().permissions.includes("cashflowstatement-canedit")) {
        ElNotify({
            type: "warning",
            message: "您没有权限进行相关操作！",
        });
        return false;
    }
    request({
        url: "/api/CashFlowStatement/AdjustFlowSheet/Row",
        params: { pId: searchInfo.pid, lineId },
        method: "delete",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "重置成功了",
            });
            resetInit();
            getTableList();
        }
    });
};
const resetHandle = () => {
    request({
        url: "/api/CashFlowStatement/AdjustFlowSheet",
        params: { pId: searchInfo.pid },
        method: "delete",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "重置成功了",
            });
            resetInit();
            getTableList();
        }
    });
};
let isCheckOut = ref(false);
const isCheckOutByPid = () => {
    request({
        url: "/api/Period/IsCheckOut",
        params: { pid: props.state.pid },
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        isCheckOut.value = res.data;
    });
};
isCheckOutByPid();
watch([isclass, searchInfo], getTableList);
watch([isclass, tableData], () => (isChange.value = true));
watch(props.state, (state: any) => {
    for (let key in state) {
        searchInfo[key] = state[key];
    }
    isCheckOutByPid();
});

const assertNameClass = (row: any) => {
    let className: string;
    // if (hasNumberTitle(row.lineName) || row.lineName.trim() === "自动倒求数据平衡现金流量表" || row.lineNumber != 0) {
    if (row.lineType < 3 && row.lineID != 10041360 && row.lineID != 20041640) {
        className = "level2";
    } else {
        className = "level1";
    }
    // if (row.lineType === 3) {
    //     className = "level1";
    // } else {
    //     className = "level2";
    // }
    return className;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle(data.row.lineName)) {
        return "highlight-title-row";
    }
}
const draftCancelHandle = () =>{
    resetInit();
    emit("draft-cancel");
} 
defineExpose({
    getTableList,
});

function getDefaultParams() {
    return {
        pid: searchInfo.pid,
        calMethod: searchInfo.calmethod,
        isTax: searchInfo.isTax,
        isClass: isclass.value,
    };
}
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "cashFlowDraft",
    `/api/CashFlowStatement/AdjustFlowSheet/Print`,
    {},
    false,
    false,
);

const onDraftInputClick = (row: { lineID: number }) => {
    if (iptDisabled.value(row)) {
        return ElNotify({
            message: "您需要取消重分类才能编辑此行！",
            type: "warning",
        });
    }
    return;
};
let init = false;
const isEditting = ref(false);
const resetInit = () => {
    init = false;
    isEditting.value = false;
};
watch(
    tableData,
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    { deep: true }
);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/CashFlowStatement.less";
@import "@/style/Statements/Statements.less";

.handle {
    display: flex;
    align-items: center;
}

.isclass-box {
    display: flex;
    align-items: center;

    .warning {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url(@/assets/Settings/question.png) no-repeat;
        background-size: contain;
        vertical-align: top;
        margin-top: 2px;
        margin-left: 6px;
        cursor: pointer;
    }
}

.dialog-content {
    .dialog-main {
        display: block;
        padding: 20px 78px 20px 50px;
        text-align: center;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        white-space: normal;
        word-break: break-all;
    }

    .buttons {
        border-top: 1px solid var(--border-color);
        text-align: center;
        padding: 10px 0;
    }
}
.content .main-center {
    :deep(.el-table__body-wrapper) {
        .el-table__cell {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            .cell {
                height: 100%;
                display: flex;
                align-items: center;
            }
        }
    }

    :deep(.cell.el-tooltip span) {
        width: 100%;
    }
    tr td .cell {
        input.draft-input {
            font-size: var(--h4);
            &:focus {
                background-color: #fff;
            }
        }
    }
    :deep(.el-table--border){
        .el-table__cell:last-child {
            border-right: none;
        }
    }
}
.ipt-box {
    position: relative;
    flex: 1;
    .ipt-mask {
        position: absolute;
        top: 0;
        left: 4px;
        width: 98%;
        height: 21px;
        background-color: rgba(245, 247, 245, 0.3);
    }
}
body[erp] {
    .main-content {
        width: 100%;
    }
    .dialog-content {
        .dialog-main {
            text-align: left;
        }
    }
    .content .main-center {
        tr td .cell {
            .ipt-box {
                input.draft-input {
                    height: 32px;
                    line-height: 32px;
                }
            }
        }
    }
}
</style>
