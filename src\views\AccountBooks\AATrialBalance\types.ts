export interface ITableData {
    rows: IRows[];
    total: number;
}

export interface IRows {
    aa_name: string;
    aa_num: string;
    aaeid: number;
    credit: number;
    credit_price?: number;
    credit_qut?: number;
    credit_sum?: string;
    debit: number;
    debit_price?: number;
    debit_qut?: number;
    debit_sum?: string;
    e_credit: number;
    e_credit_price?: number;
    e_credit_qut?: number;
    e_credit_sum?: string;
    e_debit: number;
    e_debit_price?: number;
    e_debit_qut?: number;
    e_debit_sum?: string;
    s_credit: number;
    s_credit_price?: number;
    s_credit_qut?: number;
    s_credit_sum?: string;
    s_debit: number;
    s_debit_price?: number;
    s_debit_qut?: number;
    s_debit_sum?: string;
    t_credit: number;
    t_credit_price?: number;
    t_credit_qut?: number;
    t_credit_sum?: string;
    t_debit: number;
    t_debit_price?: number;
    t_debit_qut?: number;
    t_debit_sum?: string;
}

export interface IWithAsub {
    columns: any[];
    rows: any[];
    total: number;
}

export interface IAssistingAccount {
    /** asid */
    asid: number;
    /** 辅助核算类别 */
    aatype: number;
    /** 辅助核算目标id */
    aaeid: number;
    /** 辅助核算目标编码 */
    aanum: string;
    /** 辅助核算目标名称 */
    aaname: string;
    /**  */
    value01: string;
    /** 是否启用 */
    status: number;
    /**  */
    uscc: string;
    /** 创建人  */
    createdBy: number;
    /** 创建时间 */
    createdDate: string;
    /**  */
    preName: string;
}

export interface IAsubCodeLength {
    asid: number;
    codeLength: Array<number>;
    firstAsubLength: number;
    firstCodeLength: number;
    forthAsubLength: number;
    forthCodeLength: number;
    preName: string;
    secondAsubLength: number;
    secondCodeLength: number;
    thirdAsubLength: number;
    thirdCodeLength: number;
}

export interface ISelectList {
    id: number;
    label: string;
}