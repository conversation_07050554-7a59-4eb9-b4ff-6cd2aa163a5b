<template>
    <div class="table-box" ref="containerRef" id="e-record-table">
        <Table
            ref="ERecordTableRef"
            :data="tableData"
            :columns="columns"
            :scrollbar-show="true"
            :empty-text="emptyText"
            hearderRowStyleName="header-section-cell"
            :show-overflow-tooltip="true"
            @cell-click="tableCellClick"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
            :height="'100%'"
            :page-is-show="props.pageIsShow"
            :layout="props.layout"
            :page-sizes="props.pageSizes"
            :page-size="props.pageSize"
            :total="props.total"
            :currentPage="props.currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :tableName="setModule"
            @scroll="handleScroll"
        >
            <template #name>
                <el-table-column
                    label="文件名称"
                    :min-width="200"
                    :width="getColumnWidth(setModule, 'File_Name')"
                    align="left"
                    header-align="left"
                    prop="File_Name"
                    :show-overflow-tooltip="false"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>文件名称</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('File_Name', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'fileName'"
                                    :isFilter="!!filterSearchInfo.fileName"
                                    :hasSearchVal="filterSearchInfo.fileName" 
                                    @filterSearch="filterSearch"
                                ></TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span
                            :class="{ link: haveDownloadPermission, 'file-name-container': true }"
                            @mouseleave="editIconShow = false"
                            style="display: flex; align-items: center"
                        >
                            <span 
                                @mouseenter="handlePreviewImg(scope.row, $event)"
                                @mouseleave="hidePreview"
                                :class="'file-icon icon-' + fileTypeImgPath(scope.row.File_Type ?? '')"
                            ></span>
                            <Tooltip placement="right-start" :teleported="true" :font-size="12" :line-clamp="1" :dynamic-width="true" :content="scope.row.File_Name" >
                                <span
                                    class="file-name"
                                    :title="formatTitle(scope.row.File_Type)"
                                    @mouseenter="handleEditIconShow(scope.$index)"
                                    @click="handleDownLoadFile(scope.row.File_Id, scope.row.File_Type)"
                                >
                                    <span class="ellipsis">
                                        <span v-if="props.isNewName && scope.row.File_Name_Pre"> {{ scope.row.File_Name_Pre }}_ </span>
                                        {{ handleFileName(scope.row.File_Name)[0] }}
                                    </span>
                                    <span class="file-type" @mouseenter="handleEditIconShow(scope.$index)">
                                        {{ handleFileName(scope.row.File_Name)[1] ? "." + handleFileName(scope.row.File_Name)[1] : "" }}
                                    </span>
                                </span>

                                <span
                                    class="rename-tooltip"
                                    v-permission="['lemondisk-canedit']"
                                    @mouseenter="handleEditIconShow(scope.$index)"
                                >
                                    <el-tooltip
                                        :teleported="true"
                                        class="filename-rename-tooltip"
                                        effect="light"
                                        :show-arrow="false"
                                        trigger="click"
                                        placement="bottom"
                                        :offset="6"
                                        popper-class="filename-rename-popper"
                                        :append-to="appendToHtml()"
                                    >
                                        <template #default>
                                            <span>
                                                <el-icon
                                                    title="重命名"
                                                    v-show="editIconShow && currentEditIndex === scope.$index"
                                                    @click="openRenameInput(scope.row.File_Id, scope.row.File_Name)"
                                                    class="rename-icon"
                                                    ><EditPen
                                                /></el-icon>
                                            </span>
                                        </template>
                                        <template #content>
                                            <span class="file-rename-content" v-if="renameInputShow">
                                                <el-input v-model="fileRename"></el-input>
                                                <div class="file-rename-buttons">
                                                    <span class="file-rename-button" @click="fileRenameSubmit(scope.row, scope.$index)">
                                                        <el-icon><Check /></el-icon>
                                                    </span>
                                                    <span
                                                        class="file-rename-button"
                                                        @click="
                                                            renameInputShow = false;
                                                            editIconShow = false;
                                                        "
                                                    >
                                                        <el-icon><Close /></el-icon>
                                                    </span>
                                                </div>
                                            </span>
                                        </template>
                                    </el-tooltip>
                                </span>
                        </Tooltip>
                        </span>
                    </template>
                </el-table-column>
            </template>
            <template #fileCategory>
                <el-table-column
                    label="文件类别"
                    :min-width="108"
                    :width="getColumnWidth(setModule, 'File_Category')"
                    align="left"
                    prop="File_Category"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>文件类别</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('File_Category', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'fileCategory'" 
                                    :isSelect="true"
                                    :selectedList="headerMulSeletVal.fileCategory"
                                    :option="searchOptions.fileCategory"
                                    :hasSelectList="filterSearchInfo.fileCategory"
                                    :isFilter="isFilterMultile(filterSearchInfo.fileCategory, searchOptions.fileCategory)"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span v-if="scope.row.File_Category !== 2">{{ formatFileCategory(scope.row.File_Category ?? "") }}</span>
                        <el-tooltip class="box-item" popper-class="file-category-popper" effect="light" placement="right">
                            <span
                                class="link"
                                style="text-decoration: none"
                                @mouseenter="getRowDetail(scope.row.File_Id, scope.$index)"
                                v-if="scope.row.File_Category === 2"
                                >{{ formatFileCategory(scope.row.File_Category ?? "") }}</span
                            >
                            <template #content>
                                <span style="color: #333" v-if="scope.row.Check_State === 4">查验失败，无法查看详情哦~</span>
                                <span style="color: #333" v-else-if="!rowDetailData[scope.row.File_Id]">正在获取发票详情，请稍后... </span>
                                <span v-else class="invoice-detail">
                                    <p class="detail-title">发票详情</p>
                                    <div class="detail-content">
                                        <div class="detail-list-item">
                                            <span class="detail-label">购买方：</span>
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].B_NUM }}</span>
                                        </div>
                                        <div class="detail-list-item">
                                            <span class="detail-label">销售方：</span>
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].S_NUM }}</span>
                                        </div>
                                        <div class="detail-list-item">
                                            <span class="detail-label"
                                                >{{ rowDetailData[scope.row.File_Id].INV_CODE ? "" : "全电" }}发票号码：</span
                                            >
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].INV_NUM }}</span>
                                        </div>
                                        <div class="detail-list-item" v-if="rowDetailData[scope.row.File_Id].INV_CODE">
                                            <span class="detail-label">发票代码：</span>
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].INV_CODE }}</span>
                                        </div>
                                        <div class="detail-list-item">
                                            <span class="detail-label">开票日期：</span>
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].INV_DATE }}</span>
                                        </div>
                                        <div class="detail-list-item">
                                            <span class="detail-label">价税合计：</span>
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].TOTAL }}元</span>
                                        </div>
                                        <div class="detail-list-item">
                                            <span class="detail-label">合计税额：</span>
                                            <span class="detail-data">{{ rowDetailData[scope.row.File_Id].TAX }}元</span>
                                        </div>
                                    </div>
                                </span>
                            </template>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </template>
            <template #documentType>
                <el-table-column
                    label="单据类型"
                    :min-width="108"
                    :width="getColumnWidth(setModule, 'Document_Type')"
                    prop="Document_Type"
                    align="left"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>单据类型</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('Document_Type', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'documentType'" 
                                    :isSelect="true"
                                    :selectedList="headerMulSeletVal.documentType"
                                    :option="searchOptions.documentType"
                                    :hasSelectList="filterSearchInfo.documentType"
                                    :isFilter="isFilterMultile(filterSearchInfo.documentType, searchOptions.documentType)"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span>{{ formatDocumentType(scope.row.Document_Type ?? "") }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #createdBy>
                <el-table-column
                    label="上传人"
                    :min-width="98"
                    :width="getColumnWidth(setModule, 'Created_By')"
                    prop="Created_By"
                    align="left"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>上传人</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('Created_By', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'uploadPerson'" 
                                    :isSelect="true"
                                    :selectedList="headerMulSeletVal.uploadPerson"
                                    :option="searchOptions.uploadPerson"
                                    :hasSelectList="filterSearchInfo.uploadPerson"
                                    :isFilter="isFilterMultile(filterSearchInfo.uploadPerson, searchOptions.uploadPerson)"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span>{{ scope.row.Created_By }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #uploadType>
                <!--prop="Created_By"  Upload_Type值不生效 -->
                <el-table-column
                    label="上传方式"
                    :min-width="108"
                    :width="getColumnWidth(setModule, 'Upload_Type')"
                    prop="Upload_Type"
                    align="left"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>上传方式</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('Upload_Type', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'uploadType'" 
                                    :isSelect="true"
                                    :selectedList="headerMulSeletVal.uploadType"
                                    :option="searchOptions.uploadType"
                                    :hasSelectList="filterSearchInfo.uploadType"
                                    :isFilter="isFilterMultile(filterSearchInfo.uploadType, searchOptions.uploadType)"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span>{{ formatUploadType(scope.row.Upload_Type) }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #date>
                <el-table-column
                    label="上传日期"
                    :min-width="98"
                    :width="getColumnWidth(setModule, 'Created_Date_Str')"
                    prop="Created_Date_Str"
                    align="left"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>上传日期</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('Created_Date_Str', $event)">
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span>{{ scope.row.Created_Date_Str || "" }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #size>
                <el-table-column
                    label="大小"
                    :min-width="98"
                    :width="getColumnWidth(setModule, 'File_Size')"
                    align="left"
                    prop="File_Size"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>大小</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('File_Size', $event)">
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span>{{ sizeFormatter(scope.row.File_Size ?? 0) }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #relateDocumentType>
                <el-table-column
                    label="关联单据"
                    :min-width="98"
                    :width="getColumnWidth(setModule, 'RelatedDocumentTypes')"
                    align="left"
                    prop="RelatedDocumentTypes"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>关联单据</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('RelatedDocumentTypes', $event)">
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span>{{ formatDounmentType(scope.row.RelatedDocumentTypes) }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #relateDocumentNumber>
                <el-table-column
                    label="关联单据号"
                    :min-width="128"
                    :width="getColumnWidth(setModule, 'File_Size')"
                    align="left"
                    prop="File_Size"
                    header-align="left"
                    :show-overflow-tooltip="false"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>关联单据号</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('relateDocumentNumber', $event)">
                                </div>
                            </div>
                        </div>
                    </template>
                    <template #default="scope: { row: IFileTableItem }">
                        <Popover
                            title=""
                            :hover-trigger="true"
                            :getTriggerScrollWidth="getTriggerScrollWidth"
                            :useSlotContent="true"
                            :maxWidth="150"
                            :minWidth="120"
                            :padding-right="10"
                            :minOverflowHeight="minOverflowHeight"
                        >
                            <template #trigger>
                                <div :class="{ billNo: true }">
                                    <template v-for="(source, index) in scope.row.Source" :key="index">
                                        <span
                                            :class="{ link: checkHasBillPermission(scope.row.RelatedDocumentTypes, index) }"
                                            @click="toBillPage(scope.row, index)"
                                        >
                                            {{ source[getBillNoKey(scope.row.RelatedDocumentTypes, index)] }}
                                        </span>
                                        <span
                                            :class="{ link: checkHasBillPermission(scope.row.RelatedDocumentTypes, index) }"
                                            v-if="index !== scope.row.Source.length - 1"
                                        >
                                            、
                                        </span>
                                    </template>
                                </div>
                            </template>
                            <template #content>
                                <div>
                                    <div
                                        :class="{ link: checkHasBillPermission(scope.row.RelatedDocumentTypes, index) }"
                                        style="font-size: 12px; overflow: hidden; text-overflow: ellipsis"
                                        v-for="(source, index) in scope.row.Source"
                                        :key="index"
                                        @click="toBillPage(scope.row, index)"
                                    >
                                        {{ source[getBillNoKey(scope.row.RelatedDocumentTypes, index)] }}
                                    </div>
                                </div>
                            </template>
                        </Popover>
                    </template>
                </el-table-column>
            </template>
            <template #voucherLink>
                <el-table-column
                    label="凭证"
                    :min-width="128"
                    :width="getColumnWidth(setModule, 'VoucherGroupStr')"
                    align="left"
                    prop="VoucherGroupStr"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>凭证</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('VoucherGroupStr', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'voucherState'" 
                                    :isSelect="true"
                                    :multiSelect="false"
                                    :hasSSingVal="filterSearchInfo.voucherState"
                                    :SSinglist="searchOptions.voucherState"
                                    :isFilter="filterSearchInfo.voucherState > -1"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span v-show="scope.row.Flag != 0 || (scope.row.Flag == 0 && !scope.row.IsAttachVouch)"></span>
                        <span v-show="scope.row.Flag == 0 && !checkPermission(['voucher-canview'])">
                            {{ scope.row.VoucherGroupStr || "" }}
                        </span>
                        <span v-show="scope.row.Flag == 0 && checkPermission(['voucher-canview'])">
                            <a class="link" @click.stop="routerTo(scope.row)">{{ scope.row.VoucherGroupStr || "" }}</a>
                        </span>
                    </template>
                </el-table-column>
            </template>
            <template #matters>
                <el-table-column
                    label="事项"
                    :min-width="98"
                    :width="getColumnWidth(setModule, 'Matters')"
                    prop="Matters"
                    align="left"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>事项</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('Matters', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'matters'"
                                    :isFilter="!!filterSearchInfo.matters"
                                    :hasSearchVal="filterSearchInfo.matters" 
                                    @filterSearch="filterSearch"
                                ></TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <el-input
                            v-permission="['lemondisk-canedit']"
                            v-if="currentClickRowId === scope.row.File_Id"
                            v-model="mattersEditStr"
                            class="edit-matters-input"
                            placeholder="请修改事项"
                            ref="editMattersInputRef"
                            @change="changeMatters($event, scope.row.File_Id)"
                            @input="changeMattersInput($event)"
                            @focus="handleMattersFocus($event, scope.row.File_Id)"
                            @blur="blurMatters($event, scope.row.Matters)"
                        >
                        </el-input>
                        <span v-else class="matters-span">{{ removeNull(scope.row.Matters) ?? "" }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #checkState>
                <el-table-column
                    label="校验状态"
                    :min-width="110"
                    :width="getColumnWidth(setModule, 'Check_State')"
                    prop="Check_State"
                    align="left"
                    header-align="left"
                    :show-overflow-tooltip="false"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>校验状态</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('Check_State', $event)">
                                </div>
                                <TableHeaderFilter 
                                    :prop="'checkStates'" 
                                    :isSelect="true"
                                    :selectedList="headerMulSeletVal.checkStates"
                                    :option="searchOptions.checkStates"
                                    :hasSelectList="filterSearchInfo.checkStates"
                                    :isFilter="isFilterMultile(filterSearchInfo.checkStates, searchOptions.checkStates)"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <Tooltip
                            :content="formatCheckState(scope.row.Check_State ?? '')"
                            effect="light"
                            :lineClamp="1"
                            placement="right"
                            :teleported="true"
                            :dynamic-width="true"
                            v-if="![7, 8, 9, 10, 11, 12].includes(scope.row.Check_State)"
                        >
                            <div class="flex-check-content">
                                <span v-if="![7, 8, 9, 10, 11, 12].includes(scope.row.Check_State)"
                                    >{{ formatCheckState(scope.row.Check_State ?? "") }}
                                </span>
                                <span
                                    class="refresh-icon-cord"
                                    @click="refreshFile(Number(scope.row.File_Id))"
                                    v-if="[4, 7, 8, 9, 10, 11, 12].includes(scope.row.Check_State)"
                                >
                                    <img src="@/assets/Common/refresh.png" alt="refresh" />
                                </span>
                            </div>
                        </Tooltip>
                        <div class="flex-check-content" v-else>
                            <el-tooltip :content="formatCheckState(scope.row.Check_State ?? '')" placement="right" effect="light">
                                <span>查验失败</span>
                            </el-tooltip>
                            <span
                                class="refresh-icon-cord"
                                @click="refreshFile(Number(scope.row.File_Id))"
                                v-if="[4, 7, 8, 9, 10, 11, 12].includes(scope.row.Check_State)"
                            >
                                <img src="@/assets/Common/refresh.png" alt="refresh" />
                            </span>
                        </div>
                    </template>
                </el-table-column>
            </template>
            <template #fileState>
                <el-table-column
                    label="文件状态"
                    :min-width="110"
                    :width="getColumnWidth(setModule, 'File_State')"
                    prop="File_State"
                    align="left"
                    header-align="left"
                >
                    <template #header>
                        <div class="header-operate">
                            <span>文件状态</span>
                            <div class="header-operate-rt">
                                <div class="header-caret" @click="getSortCommon('File_State', $event)">
                                </div>
                                <TableHeaderFilter
                                    :prop="'fileState'" 
                                    :isSelect="true"
                                    :multiSelect="false"
                                    :SSinglist="searchOptions.fileState"
                                    :hasSSingVal="filterSearchInfo.fileState"
                                    :isFilter="filterSearchInfo.fileState > -1"
                                    @filterSearch="filterSearch"
                                >
                                </TableHeaderFilter>
                            </div>
                        </div>
                    </template>
                    <template #default="scope">
                        <span class="file-state">{{ formatFileState(scope.row.File_State ?? "") }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #isPaper>
                <el-table-column 
                    label="纸质档案"
                    :min-width="98" 
                    :width="getColumnWidth(setModule, 'IsPaperArchives')"
                    align="left" 
                    header-align="left"
                    prop="IsPaperArchives"
                >
                    <template #default="scope">
                        <span>{{ scope.row.IsPaperArchives ? "是" : "否" }}</span>
                    </template>
                </el-table-column>
            </template>
            <template #path>
                <el-table-column 
                    label="路径" 
                    min-width="198px" 
                    align="left" 
                    header-align="left" 
                    :resizable="false"
                >
                    <template #default="scope">
                        <span>{{ formatPath(scope.row.Path ?? "") }}</span>
                    </template>
                </el-table-column>
            </template>
        </Table>
        <!-- 图片预览组件 -->
        <ul ref="previewcontainerRef" id="imgPreviewContainer">
            <li v-for="(item, index) in srcList" :key="index">
                <img :src="item.src" alt="" />
            </li>
        </ul>
        <div class="preview-img" v-show="previewShow">
            <img :src="previewSrc" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from "vue";
import { sizeFormatter } from "@/util/format";
import { checkPermission } from "@/util/permission";
import Tooltip from "@/components/Tooltip/index.vue";
import {
    fileTypeImgPath,
    formatFileCategory,
    formatDocumentType,
    formatUploadType,
    removeNull,
    formatCheckState,
    formatFileState,
    formatPath,
    formatDounmentType,
    ModuleType,
    checkHasBillPermission,
    getBillNoKey,
    handleSort
} from "../utils";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import type { IFileTableItem } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElNotify } from "@/util/notify";
import Table from "@/components/Table/index.vue";
import { request } from "@/util/service";
import dayjs from "dayjs";
import Popover from "@/components/Popover/index.vue";
import { useThirdPartInfoStore } from "@/store/modules/thirdpart";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import { isFilterMultile } from "@/components/TableHeaderFilter/utils";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { previewImg } from "@/components/UploadFileDialog/utils";

const setModule = ref("ERecord");
const editIconShow = ref(false);
const renameInputShow = ref(false);
const columns = ref<Array<IColumnProps>>([
    { slot: "selection" },
    { slot: "name" },
    { slot: "fileCategory" },
    { slot: "documentType" },
    { slot: "date" },
    { slot: "relateDocumentType" },
    { slot: "relateDocumentNumber" },
    { slot: "voucherLink" },
    { slot: "matters" },
    { slot: "checkState" },
    { slot: "fileState" },
    { slot: "createdBy" },
    { slot: "uploadType" },
    { slot: "size" },
    { slot: "isPaper" },
    { slot: "path" },
]);
const props = withDefaults(
    defineProps<{
        tableData: IFileTableItem[];
        emptyText: string;
        pageIsShow?: boolean;
        layout?: string;
        pageSizes?: Array<number>;
        pageSize?: number;
        total?: number;
        currentPage?: number;
        isNewName: boolean;
        searchOptions: any,
        headerMulSeletVal: any,
        filterSearchInfo: any,
        // originData: IFileTableItem[];
    }>(),
    {
        emptyText: "",
        pageIsShow: false,
        layout: () => "total, sizes, prev, pager, next, jumper",
        pageSizes: () => [1, 2, 10, 20, 30],
        pageSize: () => 20,
        total: () => 0,
        currentPage: () => 1,
        isNewName: false,
        searchOptions: () => {},
        headerMulSeletVal: () => {},
        filterSearchInfo: () => {}
    }
);
let ERecordTableRef = ref<InstanceType<typeof Table>>();

const emit = defineEmits([
    "selection-change", 
    "refresh-file", 
    "get-table-list", 
    "size-change", 
    "current-change", 
    "refresh",
    "filterSearch",
]);

const tableData = computed(() => props.tableData);
const srcList = computed(() => {
    const list: Array<{ id: string, src: string }> = [];
    for (let i = 0; i < tableData.value.length; i++) {
        const file = tableData.value[i];
        if (file.File_Type !== 3010) continue;
        list.push({
            id: file.File_Id,
            src: `${window.jLmDiskHost}/api/ImgHandler.ashx?fid=${file.File_Id}&type=&asid=${file.As_Id}&r=${new Date().getTime() + i}`,
        });
    }
    return list;
});

const handleFileName = (fileName: string): string[] => {
    const lastDotIndex = fileName.lastIndexOf(".");

    const name = lastDotIndex > 0 ? fileName.slice(0, lastDotIndex) : fileName.slice(0);
    const ext = (lastDotIndex > 0 && fileName.slice(lastDotIndex + 1)) || "";

    return [name, ext];
};
const fileRename = ref("");
const currentEditIndex = ref(-1);
const handleEditIconShow = (index: number) => {
    currentEditIndex.value = index;
    editIconShow.value = true;
};
function formatTitle(fileType: number) {
    return haveDownloadPermission.value ? (fileType === 3010 || fileType === 2020 ? "点击预览" : "点击下载") : "";
}
const openRenameInput = (id: string, name: string) => {
    renameInputShow.value = true;
    fileRename.value = name;
};
const appendToHtml = (): HTMLElement => {
    return document.querySelector(`#e-record-table`) as HTMLElement;
};
const fileRenameSubmit = (row: IFileTableItem, index: number) => {
    if (row.File_Name.trim() === fileRename.value) {
        ElNotify({
            type: "success",
            message: "文件名称修改成功",
        });
        renameInputShow.value = false;
        return false;
    }
    request({
        url: window.jLmDiskHost + "/Services/Doc/ModifyName.ashx",
        method: "post",
        data: {
            fid: row.File_Id,
            name: fileRename.value,
        },
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
    }).then((res: any) => {
        if (res.Succeed) {
            renameInputShow.value = false;
            tableData.value[index].File_Name = fileRename.value;
            let originIndex = originData.value.findIndex((v) => v.File_Id === row.File_Id);
            if (originIndex > -1) {
                originData.value[originIndex].File_Name = fileRename.value;
            }
            ElNotify({
                type: "success",
                message: "文件名称修改成功",
            });
            emit("get-table-list");
        } else {
            ElNotify({
                type: "warning",
                message: res.Message,
            });
        }
    });
    renameInputShow.value = false;
    editIconShow.value = false;
};
let rowDetailData = ref<any>({});
const getRowDetail = (fid: string, index: number) => {
    rowDetailData.value[fid] = undefined;

    request({
        url: window.jLmDiskHost + "/Services/Doc/GetInfoOfInvoice.ashx",
        method: "post",
        data: {
            fid,
        },
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
    }).then((res: any) => {
        if (res.Succeed) {
            rowDetailData.value[fid] = res.Result;
            tableData.value[index].Check_State = res.Result.CHECK_STATE;
            let originIndex = originData.value.findIndex((v) => v.File_Id === fid);
            if (originIndex > -1) {
                originData.value[originIndex].Check_State = res.Result.CHECK_STATE;
            }
        } else {
            rowDetailData.value[fid] = undefined;
        }
    });
};
let currentClickRowId = ref("");
let editMattersInputRef = ref();
const tableCellClick = (row: any, column: any) => {
    if (column.label !== "事项") return;
    currentClickRowId.value = row.File_Id;
    nextTick(() => {
        mattersEditStr.value = row.Matters;
        editMattersInputRef.value?.focus();
    });
};

const sortChange = (data: { column: any; prop: string; order: any }) => {
    if (tableData.value.every((v: any) => !v[data.prop])) {
        ERecordTableRef.value?.getTable()?.clearSort();
    }
};

const mattersEditStr = ref("");

const handleMattersFocus = (event: any, fid: string) => {
    mattersEditStr.value = tableData.value.find((v) => v.File_Id === fid)?.Matters as string;
};

const blurMatters = (event: any, matters: string) => {
    if (mattersEditStr.value === matters) {
        currentClickRowId.value = "-1";
    }
};

const changeMattersInput = (matters: string) => {
    if (matters.length >= 36) {
        ElNotify({
            type: "warning",
            message: "事项最大字数为36！",
        });
        mattersEditStr.value = matters.slice(0, 36);
    }
};
const changeMatters = (editMatter: string, editMatterFid: string) => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx",
        method: "post",
        data: { fid: editMatterFid, matters: editMatter, submitType: "UpdateMatters" },
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
    }).then((res: any) => {
        if (res.Succeed) {
            ElNotify({
                type: "success",
                message: "文件事项修改成功",
            });
        }
        let targetIndex = tableData.value.findIndex((v) => v.File_Id === editMatterFid);
        if (targetIndex > -1) {
            tableData.value[targetIndex].Matters = editMatter;
        }
        let originIndex = originData.value.findIndex((v) => v.File_Id === editMatterFid);
        if (originIndex > -1) {
            originData.value[originIndex].Matters = editMatter;
        }
        mattersEditStr.value = "";
        currentClickRowId.value = "-1";

        // emit("get-table-list");
    });
};
let timer: any;
const previewSrc = ref("");
const previewShow = ref(false);
const containerRef = ref<HTMLDivElement>();
function calcPreviewPosition(left: number, top: number) {
    const previewImg = containerRef.value?.querySelector(".preview-img") as HTMLElement;
    if (previewImg) {
        previewImg.style.left = left + "px";
        previewImg.style.top = top + "px";
    }
}
function getImgSrc(fileId: string) {
    const asid = useAccountSetStore().accountSet!.asId;
    return `${window.jLmDiskHost}/api/ImgHandler.ashx?fid=${fileId}&type=&asid=${asid}&r=${new Date().getTime()}`;
}
function handlePreviewImg(row: IFileTableItem, event: MouseEvent) {
    clearTimeout(timer);
    if (row.File_Type !== 3010) return;
    timer = setTimeout(() => {
        previewSrc.value = getImgSrc(row.File_Id);
        previewShow.value = true;
        const { x = 0, width = 0, y = 0 } = (event.target as HTMLElement)!.getBoundingClientRect();
        const translateY = 5; // 偏移量
        const positionX = x + width;
        let positionY = y + translateY;
        nextTick().then(() => {
            const previewImg = containerRef.value?.querySelector(".preview-img") as HTMLElement;
            const { height = 0 } = previewImg.getBoundingClientRect();
            const routerContainerMargin = window.isErp ? 20 : 12;
            if (positionY + height > window.innerHeight - routerContainerMargin) {
                positionY = window.innerHeight - height - routerContainerMargin;
            }
            calcPreviewPosition(positionX, positionY);
        })
    }, 1000);
}
function hidePreview() {
    clearTimeout(timer);
    previewShow.value = false;
}
const handleDownLoadFile = (fileId: string, fileType: number) => {
    if (!haveDownloadPermission.value) return;
    if (fileType === 3010) {
        handlePreview(fileId);
        return;
    } else if (fileType === 2020) {
        globalPrint(window.jLmDiskHost + `/Services/Doc/DownloadDoc.ashx?fids=${fileId}`);
        return;
    }
    globalExport(window.jLmDiskHost + `/Services/Doc/DownloadDoc.ashx?fids=${fileId}&isNewName=${props.isNewName}`);
};
const previewcontainerRef = ref<HTMLUListElement>();
function handlePreview(fileId: string) {
    const index = srcList.value.findIndex((item) => item.id === fileId);
    previewImg(previewcontainerRef.value as HTMLUListElement, index);
}

const handleSelectionChange = (val: any) => {
    emit("selection-change", val);
};

const routerTo = (row: IFileTableItem) => {
    const params = { pid: row.P_Id, vid: row.V_Id, fcode: "lemondisk-cancreatevoucher" };
    globalWindowOpenPage("/Voucher/VoucherPage?" + getUrlSearchParams(params), "查看凭证");
};

const refreshFile = (id: number) => emit("refresh-file", id);
const haveDownloadPermission = computed(() => {
    return checkPermission(["lemondisk-candownload"]);
});

const handleSizeChange = (val: any) => {
    emit("size-change", val);
};
const handleCurrentChange = (val: any) => {
    emit("current-change", val);
};
const handleRerefresh = () => {
    emit("refresh");
};
function toBillPage(row: IFileTableItem, index = 0) {
    const { RelatedDocumentTypes, Source } = row;
    if (!RelatedDocumentTypes || !checkHasBillPermission(RelatedDocumentTypes, index)) return;
    switch (RelatedDocumentTypes[index]) {
        case ModuleType.SalesInvoice:
        case ModuleType.PurchaseInvoice:
            toInvoicePage(Source);
            break;
        case ModuleType.CashJournal:
        case ModuleType.DepositJournal:
            toJournalPage(Source, index);
            break;
        case ModuleType.Draft:
            toDraftPage(Source);
            break;
        case ModuleType.Salary:
            toSalaryPage(Source);
            break;
        case ModuleType.FixedAssets:
            toAssetPage(Source);
            break;
        default:
            break;
    }
}
const getDefaultUrlParams = () => ({ from: "ERecord", r: Math.random() });
function toJournalPage(Source: any[], index = 0) {
    const { jType, acId, date, lineSn } = Source[index];
    const journalDate = dayjs(date).format("YYYY-MM-DD");
    const params = { searchStartDate: journalDate, searchEndDate: journalDate, cdAccount: acId, lineSn, ...getDefaultUrlParams() };
    const path = jType === "1020" ? "/Cashier/DepositJournal?" : "/Cashier/CashJournal?";
    const name = jType === "1020" ? "银行日记账" : "现金日记账";
    const url = path + getUrlSearchParams(params);
    globalWindowOpenPage(url, name);
}
function toDraftPage(Source: any[]) {
    const { draftCreatedDate, draftNo } = Source[0];
    const params = { searchStartDate: draftCreatedDate, searchEndDate: draftCreatedDate, draftNo, ...getDefaultUrlParams() };
    globalWindowOpenPage("/Draft/Draft?" + getUrlSearchParams(params), "票据管理");
}
function toInvoicePage(Source: any[]) {
    const { invDate, invCategory, invNum } = Source[0];
    const params = { searchStartDate: invDate, searchEndDate: invDate, invoiceCategory: invCategory, invNum, ...getDefaultUrlParams() };
    const path = invCategory === "10080" ? "/Invoice/PurchaseInvoice?" : "/Invoice/SalesInvoice?";
    const name = invCategory === "10080" ? "进项发票" : "销项发票";
    const url = path + getUrlSearchParams(params);
    globalWindowOpenPage(url, name);
}
function toSalaryPage(Source: any[]) {
    const { mid } = Source[0];
    const params = { dateKey: mid, ...getDefaultUrlParams() };
    globalWindowOpenPage("/Salary/SalaryManage?" + getUrlSearchParams(params), "工资管理");
}
function toAssetPage(Source: any[]) {
    const { cpid, faNum } = Source[0];
    const params = { tabflag: 1, period: cpid, faNum, ...getDefaultUrlParams() };
    globalWindowOpenPage("/FixedAssets/FixedAssets?" + getUrlSearchParams(params), "固定资产");
}
function getTriggerScrollWidth(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target) return 0;
    const trigger = target.querySelector(".billNo") as HTMLElement;
    if (!trigger) return 0;
    return trigger.scrollWidth;
}
const thirdPartInfoStore = useThirdPartInfoStore();
const minOverflowHeight = computed(() => {
    if ((thirdPartInfoStore.isThirdPart && !thirdPartInfoStore.isOEM) || window.isErp) return 125;
    return 150;
});
//排序
const originData = ref<any[]>([]);
watch(
    ()=>props.tableData,
    () => {
        originData.value = JSON.parse(JSON.stringify(props.tableData));
        sortNum.value = 0;
        let sortItems = document.querySelectorAll(".header-caret");
        for(let i =0; i <sortItems.length; i++) {
            sortItems[i].classList.remove("ascending");
            sortItems[i].classList.remove("descending");
        }
    }
)
let sortNum = ref(0);
let saveName = ref("");
function getSortCommon(name: string, event: any) {
    if (saveName.value !== name) {
        sortNum.value = 0;
        saveName.value = name;
    }
    let sortItems = document.querySelectorAll(".header-caret");
    for(let i =0; i <sortItems.length; i++) {
        sortItems[i].classList.remove("ascending");
        sortItems[i].classList.remove("descending");
    }
    sortNum.value++;
    if (sortNum.value === 1) {
        event.target.classList.remove("ascending");
        event.target.classList.add("descending");
        handleSort(-1, name, tableData.value, originData.value);
    } else if(sortNum.value === 2) {
        event.target.classList.remove("descending");
        event.target.classList.add("ascending");
        handleSort(1, name, tableData.value, originData.value);
    } else {
        event.target.classList.remove("ascending");
        event.target.classList.remove("descending");
        handleSort(0, name, tableData.value, originData.value);
        sortNum.value = 0;
    }
}
//表头字段模糊搜索
function filterSearch(prop: string, value: any) {
    emit("filterSearch", prop, value);
}
function handleScroll() {
    ERecordTableRef.value?.$el.click();
}
</script>

<style lang="less" scoped>
.table-box {
    height: 100%;
    #imgPreviewContainer {
        display: none;
    }
    .custom-table {
        display: flex;
        flex-direction: column;
    }
    :deep(.el-table) {
        overflow-y: visible;
        .el-table__body-wrapper {
            // height: 300px;

            .cell {
                .el-input__inner {
                    border: none;
                }
                .new-popover-trigger {
                    display: flex;
                    align-items: center;
                }
                .billNo {
                    display: inline-block;
                    max-width: 100%;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    .link {
                        padding-left: 0;
                        margin-left: 0;
                    }
                }
            }
        }
    }
}
#preview {
    display: none;
}
.file-name {
    display: flex;
    align-items: center;
    overflow: hidden;
    .ellipsis {
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}

.rename-tooltip {
    .el-icon.rename-icon {
        width: 14px;
        height: 14px;
        color: #666;
        &:hover {
            color: var(--link-color);
        }
        & svg {
            width: 100%;
            height: 100%;
        }
    }
}

:deep(.filename-rename-popper.is-light.el-popper) {
    width: 184px !important;
    padding: 0 !important;
    border: none !important;
    background-color: rgba(255, 255, 255, 1);
    .filename-rename-popper {
        .el-input {
            height: 20px;

            :deep(.el-input__wrapper) {
                height: 20px;

                .el-input__inner {
                    line-height: 25px !important;
                }
            }
        }
    }
}

.file-rename-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .file-rename-buttons {
        border: 1px solid var(--el-input-border-color, var(--el-border-color));
        border-left: none;
        height: 30px;
        width: 24%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .file-rename-button {
            font-size: 18px;
            color: var(--green);
            font-weight: bolder;
            padding-top: 4px;
            .el-icon {
                padding: 0 2px;
            }
        }
    }
    :deep(.el-input) {
        width: 76%;
        .el-input__inner {
            border: none !important;
        }
    }
}
.file-category-popper {
    .invoice-detail {
        .detail-title {
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #333333;
            margin-left: 6px;
        }
        .detail-content {
            padding: 0 10px;
            .detail-list-item {
                padding: 7px 0;
                .detail-label {
                    color: #666;
                    width: 100px;
                    font-size: 14px;
                    display: inline-block;
                }
                .detail-data {
                    font-size: 14px;
                    color: #333333;
                    display: inline-block;
                }
            }
        }
    }
}
.file-icon {
    width: 18px;
    height: 18px;
    margin-right: 6px;
    flex-shrink: 0;

    &.icon-folder {
        background: url("@/assets/ERecord/folder.png") no-repeat;
    }

    &.icon-word {
        background: url("@/assets/ERecord/word.png") no-repeat;
    }

    &.icon-excel {
        background: url("@/assets/ERecord/excel.png") no-repeat;
    }

    &.icon-pdf {
        background: url("@/assets/ERecord/pdf.png") no-repeat;
    }

    &.icon-ppt {
        background: url("@/assets/ERecord/ppt.png") no-repeat;
    }

    &.icon-txt {
        background: url("@/assets/ERecord/txt.png") no-repeat;
    }

    &.icon-zip {
        background: url("@/assets/ERecord/zip.png") no-repeat;
    }

    &.icon-img {
        background: url("@/assets/ERecord/img.png") no-repeat;
    }

    &.icon-default {
        background: url("@/assets/ERecord/default.png") no-repeat;
    }

    &.icon-ofd_file {
        background: url("@/assets/ERecord/ofd_file.png") no-repeat;
    }
}

.flex-check-content {
    display: flex;
    align-items: center;
    width: 100%;
    span {
        width: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .refresh-icon-cord,
    img {
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: inline-block;
    }

    .refresh-icon {
        background-image: none;
    }
}
.file-name-container {
    & > :deep(.span_wrap.el-tooltip__trigger.el-tooltip__trigger) {
        display: flex;
        align-items: center;
        overflow: hidden;
        .file-type {
            padding-left: 5px;
            white-space: nowrap;
        }
    }
}
.preview-img {
    max-width: 400px;
    max-height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    img {
        max-width: 400px;
        max-height: 300px;
    }
}
</style>
