<script setup lang="ts">
import { ref, useSlots, watch, nextTick, computed, onMounted, onUnmounted, inject, onActivated, onDeactivated} from "vue";
import type { IColumnProps } from "./IColumnProps";
import type { ElTable } from "element-plus/es/components";
import { handleAsubName } from "@/util/format";
import TablePagination from "./TablePagination.vue";
import ColumnItem from "./ColumnItem.vue";
import { checkPermission } from "@/util/permission";
import { pageScrollKey } from "@/symbols";
import { saveColWidth } from "@/components/ColumnSet/utils";
import { getOffsetTop, getSize, getSEPos } from "@/components/Table/VirtualScroll";

interface ISlotLabel {
    name: string; // 插槽列对应label
    width: number | string; //列宽  min-width值
    align?: "left" | "center" | "right"; // 对齐方式
    headerAlign?: "left" | "center" | "right"; // 表头对齐方式
}
const props = withDefaults(
    defineProps<{
        loading?: boolean;
        data?: Array<any>;
        columns?: Array<IColumnProps>;
        pageIsShow?: boolean;
        pageSizes?: Array<number>;
        pageSize?: number;
        layout?: string;
        total?: number;
        border?: boolean;
        stripe?: boolean;
        fit?: boolean;
        size?: any;
        treeProps?: Object;
        rowKey?: any; // string | Function
        currentPage?: number;
        maxHeight?: number;
        minHeight?: string;
        emptyText?: string;
        height?: number | string;
        hearderRowStyleName?: string;
        showHeader?: boolean;
        rowClassName?: any; // string | Function
        hasAddSub?: boolean;
        // 表格有左加右减功能,根据哪个字段来判断当前行
        addSubField?: string;
        expandRowKeys?: Array<string>;
        width?: number;
        otherField?: string;
        firstLabel?: string;
        firstProp?: string;
        tableLayout?:any;  
        view?: string;
        treePageIsNotShow?: boolean;
        // treeTable里面放插槽的话需要传这个参数，否则表头和数据对不齐
        slotLabels?: ISlotLabel[];
        tooltipOptions?: any;
        cellClassName?: any;
        tableName?: string;
    }>(),
    {
        loading: false,
        data: () => [],
        columns: () => [],
        pageIsShow: false,
        pageSizes: () => [1, 2, 10, 20, 30],
        pageSize: () => 20,
        layout: () => "total, sizes, prev, pager, next, jumper",
        total: () => 0,
        border: true,
        stripe: false,
        fit: true,
        size: "default",
        // treeProps: () => ({ hasChildren: true, children: "family" }),
        treeProps: () => ({  }),
        rowKey: "",
        currentPage: () => 1,
        hearderRowStyleName: "",
        showHeader: true,
        rowClassName: "",
        emptyText: "数据加载中...",
        hasAddSub: false,
        addSubField: "",
        expandRowKeys: () => [],
        width: 100,
        otherField: "",
        firstLabel: "",
        firstProp: "",
        view: "",
        treePageIsNotShow: true,
        slotLabels: () => [],
        tooltipOptions: () => ({ effect: "light", placement: "top" }),
        cellClassName: () => "",
        tableLayout:"fixed",
        tableName: "",
    }
);
const slots = useSlots();
const slotsArr = Object.keys(slots);

const isErp = ref(window.isErp);

const TableComponents = ref<InstanceType<typeof ElTable>>();
//计算表头列数进行合并
const columnCount = computed(() => {
    return getColumnCount(props.columns);
});
const getColumnCount = (columns: IColumnProps[]): number => {
    let count = 0;
    for (const column of columns) {
        if (column.children) {
            count += getColumnCount(column.children);
        } else {
            count++;
        }
    }
    return count;
};
const objectSpanMethod = (data: { row: any; rowIndex: number; column: any; columnIndex: number }) => {
    if (data.row.hasChildren) {
        if (data.columnIndex === 0 ) {
            return {
                rowspan: 1,
                colspan: columnCount.value,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
};
const customEmptyText = ref(props.emptyText);
const handleResize = () => {
    TableComponents.value?.doLayout();
    updateView();
};
watch(
    () => props.emptyText,
    (val) => {
        customEmptyText.value = val;
    },
    { immediate: true }
);
watch(
    () => props.currentPage,
    () => {
        handleResize();
    }
);

// 保存的滚动条位置
const savedScrollPosition = ref(0); 
const scrollTop = ref(0);
const saveScrollPosition = (left: number) => {
    savedScrollPosition.value = left;
};
const setScrollLeft = (scrollLeft: number) => {
    TableComponents.value?.setScrollLeft(scrollLeft);
};

onMounted(() => {
    const scrollDom = findScrollDom();
    scrollDom && scrollDom.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleResize);
});
onUnmounted(() => {
    const scrollDom = findScrollDom();
    scrollDom && scrollDom.removeEventListener("scroll", handleScroll);
    window.removeEventListener("resize", handleResize);
});

const pageScroll = inject(pageScrollKey);
function handleScroll(e: any) {
    pageScroll && pageScroll(e.target.scrollTop > 0, () => {
        e.target.scrollTop > 0 && setScrollTop(0);
    });
    emit("scroll");
    updateView();
    saveScrollPosition(e.target.scrollLeft);
    scrollTop.value = e.target.scrollTop;
}

onActivated(() => {
    const timer = setTimeout(() => {
        pageScroll && pageScroll(scrollTop.value > 0, () => {
            scrollTop.value > 0 && setScrollTop(0);
        });
        clearTimeout(timer);
    })
    setScrollTop(scrollTop.value);
    setScrollLeft(savedScrollPosition.value);
});
onDeactivated(() => {
    pageScroll && pageScroll(false, () => {});
});

// 表格数据变化时展开所有有子元素的项
// watch(
//     () => props.data,
//     () => {
//         const hasChildrenRow = props.data.filter((item) => item.hasChildren);
//         for (let row of hasChildrenRow) {
//             expandRow(row);
//         }
//     }
// );

const emit = defineEmits<{
    (e: "selection-change", value: any): void;
    (e: "row-click", value: any, column: any, event: any): void;
    (e: "size-change", value: number): void;
    (e: "current-change", value: number): void;
    (e: "sort-change", val: any): void;
    (e: "cell-click", row: any, column: any, cell: any, event: any): void;
    //(e: "get-row-info", $event: MouseEvent, row: any): void;
    (e: "table-add-or-subtract", val: any): void;
    // (e: "table-subtract", val: any): void;
    (e: "go-to-detail", val: any): void;
    (e: "refresh"): void;
    (e: "scroll"): void;
}>();
const handleSizeChange = (val: any) => {
    emit("size-change", val);
};
const handleCurrentChange = (val: any) => {
    emit("current-change", val);
};
const goToDetail = (val: any) => {
    emit("go-to-detail", val);
};
const handleRerefresh = () => {
    emit("refresh");
};
let expands = ref<any[]>([]);

// 扩展table行
// function expandRow(row: any) {
//     let index = expands.value.indexOf(row[props.rowKey]);
//     if (index < 0) {
//         // 如果当前没有该扩展列，expands添加该列，扩展
//         expands.value.push(row[props.rowKey]);
//         //   this.getOneTableDetail(row)
//     } else {
//         // 如果当前已经有该扩展列，expands清空，收回
//         expands.value.splice(index, 1);
//     }
//     TableComponents.value?.toggleRowExpansion(row);
// }

function expandRow(row: any) {
    tableDataAll.value.forEach((item) => {
        if (item.parentId == row.id) {
            item.show = !item.show;
        }
    });
    let index = expands.value.indexOf(row[props.rowKey]);
    if (index < 0) {
        expands.value.push(row[props.rowKey]);
    } else {
        expands.value.splice(index, 1);
    }
}
function handleRowClassName(data: { row: any, rowIndex: number }) {
    return !data.row.show ? "none" : "";
}

let tableData = ref<any[]>([]);
let tableDataAll = ref<any[]>([]);

const initData = (data: any) => {
    tableData.value = [];
    expands.value = [];
    // 总账孩子，排序
    if (props.view === "GeneralLedger") {
        data.forEach((item: any, index: number) => {
            item.index = index;
            if (item.asub_code.length > 4) {
                item.level = 2;
            }
        });
    }
    if (data) {
        if (data.length) {
            data.forEach((item: any, index: number) => {
                item.id = index;
                handleData(item, index);
            });
        }
    }
    if (props.view === "GeneralLedger") {
        tableData.value = tableData.value.sort((a: any, b: any) => {
            return a.index - b.index;
        });
    }

    tableDataAll.value = tableData.value.reduce((acc, item) => {  
        if (item.hasChildren) {  
            item.parentId = -1;  
            item.show = true;  
        }  
        acc.push(item);  
        item.family.forEach((v:any) => {  
            acc.push({  
                ...v,  
                parentId: item.id,  
                show: true  
            });  
        });  
        return acc;  
    }, []);  
};

defineExpose({
    initData,
});
function handleData(item: any, index: number) {
    let parentIndex = tableData.value.findIndex((v: any) => {
        if (props.view === "GeneralLedger") {
            if (item["asub_name"].includes("未录入辅助核算")) {
                return v[props.rowKey] === item[props.rowKey] && v["asub_name"].includes("未录入辅助核算");
            } else if (item["asub_code"].includes("_")) {
                return v[props.rowKey] === item[props.rowKey] && !v["asub_name"].includes("未录入辅助核算");
            } else {
                return v[props.rowKey] === item[props.rowKey] && v.period === item.period && !v["asub_name"].includes("未录入辅助核算");
            }
        } else {
            return v[props.rowKey] === item[props.rowKey];
        }
    });
    let data: any = {};

    if (parentIndex < 0) {
        data = { ...item };
        data.id = index * 1000 + index;
        data.family = [];
        data.family.push(item);
        data.hasChildren = true;
        tableData.value.push(data);
        expands.value.push(item[props.rowKey]);
        data.balance = checkUneven(item);
    } else {
        tableData.value.forEach((v: any) => {
            // 资金-核对总账判断平不平
            if (v[props.rowKey] === item[props.rowKey] && v.period === item.period) {
                v.balance = checkUneven(item);
                v.family.push(item);
            } else if (
                props.view === "GeneralLedger" &&
                v[props.rowKey] === item[props.rowKey] &&
                (item["asub_code"].includes("_") || v["asub_name"].includes("未录入辅助核算"))
            ) {
                v.balance = checkUneven(item);
                v.family.push(item);
            }
        });
    }
}
// 检查平不平
function checkUneven(item: any) {
    if (props.view === "CDCheck" && item.name === "差异" && item.ac_name === "不平") {
        return false;
    }
    // 资产-核对总账判断平不平
    if (props.view === "FACheck" && item.itemTypeText === "差异" && (item.credit || item.debit || item.initial || item.total)) {
        return false;
    }
    return true;
}

//设置样式
function handleCellClassName({ row, column }: any) {
    return props.cellClassName ?  props.cellClassName(row, column) : '';
}

watch(
    () => props.loading,
    (val) => {
        if (!val && props.data.length === 0) {
            customEmptyText.value = "暂无数据";
        } else if (val && props.data.length === 0) {
            customEmptyText.value = "数据加载中...";
        } else {
            customEmptyText.value = "  ";
        }
    }
);
const setScrollTop = (val: number) => {
    TableComponents.value?.setScrollTop(val);
};
// 切换页面滚动到顶部
watch(
    () => props.data,
    () => {
        //表格内部滚动到顶部
        setScrollTop(0);
    }
);
// 资金核对总账项目列
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    let elements = document.querySelectorAll(`.${column.id} .cell`);
    let max = elements[0].scrollWidth;
    elements.forEach((item) => {
        if (item.scrollWidth > max) {
            max = item.scrollWidth;
        }
    });
    if (newWidth === oldWidth && column.width < max) {
        column.width = max + 16; //单元格左右有8内边距
    }
    //列宽拖动保存浏览器
    if (props.tableName) {
        saveColWidth(props.tableName, column.width, column.property);
    }
}

watch(
    () => tableDataAll.value,
    () => {
        updateView();
    },
    {deep: true}
)

//虚拟滚动
const hasVirtualCount = 20;
const onlyKey = ref("");
const selectKey = "customSelect";
const mapData = ref<any[]>([]);
function getKeyAndData() {
    onlyKey.value = tableDataAll.value.length > hasVirtualCount ? "onlyKey" : "";
    mapData.value = tableDataAll.value.map((item, index) => ({ 
        ...item, 
        [onlyKey.value]: index, 
        [selectKey]: false
    }))
    getOffsetMap();
}

const rowHeight = window.isErp ? 44 : 37;
const virtuallyData = ref<any[]>([]);
const sizes = ref<any>({});
const buffer = 500;
const start = ref(0);
const end = ref(0);
let timer: any = null;

function findScrollDom() {
    return TableComponents.value?.$el.querySelector(".el-scrollbar__wrap") as HTMLElement;
}
const offsetMap = ref({});
function getOffsetMap() {
    const res: any = {};
    let total = 0;
    for (let i = 0; i < tableDataAll.value.length; i++) {
        const key = mapData.value[i][onlyKey.value];
        res[key] = total;
        const curSize = mapData.value[i].show ? sizes.value[key] : 0;
        const size = typeof curSize === "number" ? curSize : rowHeight;
        total += size;
    }
    let last = tableDataAll.value.length - 1;
    if (last > 0 && !tableDataAll.value[last].show) {
        res[last] = res[last] - rowHeight;
    }
    offsetMap.value = res;
}
const renderVirtualTable = (shouldUpdate = true) => {
    updateSizes();
    calcRenderData();
    calcPosition();
    shouldUpdate && updatePosition();
};
const updateSizes = () => {
    const tableBody = TableComponents.value?.$refs.tableBody as HTMLElement;
    const rows = tableBody?.querySelectorAll(".el-table__row") as unknown as any[];
    if (rows) {
        Array.from(rows).forEach((row, index) => {
            const item = virtuallyData.value[index];
            if (!item) return;
            const key = item[onlyKey.value];
            const offsetHeight = row.offsetHeight;
            if (sizes.value[key] !== offsetHeight) {
                sizes.value[key] = offsetHeight;
            }
        });
    }
};
const calcRenderData = () => {
    const scroller = findScrollDom();
    const thisTop = scroller.scrollTop - buffer;
    const thisBottom = scroller.scrollTop + scroller.offsetHeight + buffer;

    const result = getSEPos(thisTop, thisBottom, tableDataAll.value, mapData.value, offsetMap.value, onlyKey.value);

    start.value = result.thisStart;
    end.value = result.thisEnd;
    virtuallyData.value = mapData.value.slice(result.thisStart, result.thisEnd + 1);
};
const calcBodyHeight = () => {
    const last = tableDataAll.value.length - 1;
    const wrapHeight = getOffsetTop(last, mapData.value, offsetMap.value, onlyKey.value) + getSize(last, virtuallyData.value, sizes.value, onlyKey.value, rowHeight);

    const el = TableComponents.value?.$refs.bodyWrapper as any;
    if (!el) return;
    const virtualBody = (TableComponents.value?.$refs.tableBody as HTMLElement).parentNode as HTMLElement;
    virtualBody && (virtualBody.style.height = wrapHeight + "px");
};
const calcPosition = () => {
    const el = TableComponents.value?.$refs.bodyWrapper as any;
    if (!el) return;
    const offsetTop = getOffsetTop(start.value, mapData.value, offsetMap.value, onlyKey.value);
    const tableBody = TableComponents.value?.$refs.tableBody as HTMLElement;
    tableBody && (tableBody.style.transform = `translateY(${offsetTop}px)`);
    calcBodyHeight();
};
const updatePosition = () => {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
        timer && clearTimeout(timer);
        renderVirtualTable(false);
    }, 100);
};
const updateView = () => {
    getKeyAndData();
    renderVirtualTable();
};
</script>

<template>
    <div class="table tree-table custom-table" :class="[treePageIsNotShow ? 'tree-page-is-not-show' : '', isErp ? 'erp-tree-table' : '']">
        <el-table
            ref="TableComponents"
            v-loading="loading"  element-loading-text="正在加载数据..."
            default-expand-all
            :size="size"
            :show-header="showHeader"
            :border="border"
            :data="virtuallyData"
            :tree-props="treeProps"
            :fit="fit"
            :stripe="stripe"
            :max-height="maxHeight"
            :header-row-class-name="hearderRowStyleName"
            :height="height"
            :indent="0"
            :empty-text="customEmptyText"
            :automatic-dropdown="false"
            :show-overflow-tooltip="true"
            :span-method="objectSpanMethod"
            :expand-row-keys="expandRowKeys"
            :tooltip-options="tooltipOptions"
            :style="{ minHeight: minHeight ? minHeight : '0px' }"
            :scrollbar-always-on="true"
            :cell-class-name="handleCellClassName"
            :table-layout="tableLayout"
            :row-class-name="handleRowClassName"
            @header-dragend="headerDragend"
        >
            <el-table-column
                :prop="props.columns[0]?.prop || firstProp"
                :label="props.columns[0]?.label"
                :formatter="props.columns[0]?.formatter"
                :min-width="props.columns[0]?.minWidth"
                :width="props.columns[0]?.width"
                :align="props.columns[0]?.align"
                :header-align="props.columns[0]?.headerAlign"
                :fixed="props.columns[0]?.fixed"
                :type="props.columns[0]?.type"
                :class-name="props.columns[0]?.className"
            >
                <template #default="scope">
                    <span v-if="scope.row.hasChildren">
                        <span style="height: 100%" class="pr-10" @click="expandRow(scope.row)">
                            <img v-show="expands.includes(scope.row[rowKey])" src="@/assets/icons/down-black.png" alt="" />
                            <img v-show="!expands.includes(scope.row[rowKey])" src="@/assets/icons/right-black.png" alt="" />
                        </span>
                        <span
                            class="link"
                            style="font-weight: bold"
                            v-if="scope.row[otherField].includes('<a')"
                            @click="goToDetail(scope.row[otherField])"
                        >
                            {{ handleAsubName(scope.row[otherField]) }}
                        </span>
                        <span v-else style="font-weight: bold">
                            {{ scope.row[otherField] === "平" || scope.row[otherField] === "不平" ? "" : scope.row[otherField] }}</span
                        >
                        <div
                            v-if="!scope.row.balance && expands.includes(scope.row[rowKey])"
                            class="show-state"
                            :class="isErp ? 'erp-show-state' : ''"
                        ></div>
                    </span>

                    <span v-else>
                        <template v-if="props.view === 'CDCheck'">
                            <a
                                v-if="scope.row[props.columns[0].prop as string] === '差异' && checkPermission(['cdcheckdetail-canview'])"
                                class="link"
                                @click="goToDetail(scope.row)"
                            >
                                {{ scope.row[firstProp] }}
                            </a>
                            <span v-else>{{ scope.row[props.columns[0].prop as string] }}</span>
                        </template>
                        <template v-else>
                            <a
                                v-if="scope.row[props.columns[0].prop as string] === '差异' && checkPermission(['checkledgerdedetail-canview'])"
                                class="link"
                                @click="goToDetail(scope.row)"
                            >
                                {{ scope.row[firstProp] }}
                            </a>
                            <span v-if="scope.row[props.columns[0].prop as string] !== '差异'">
                                <span>{{ scope.row[props.columns[0].prop as string] }}</span>
                            </span>
                        </template>
                    </span>
                </template>
            </el-table-column>
            <template v-for="colItem in props.columns.slice(1)" :key="colItem.prop">
                <ColumnItem :column="colItem">
                    <template v-for="slotItem in slotsArr" #[slotItem]="{ slotColumn }" :key="slotItem">
                        <slot :name="slotItem" :slotColumn="slotColumn"></slot>
                    </template>
                </ColumnItem>
            </template>
        </el-table>
        <TablePagination
            v-if="pageIsShow"
            class="pagination"
            :size="size"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :layout="layout"
            :total="total"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
        >
        </TablePagination>
    </div>
</template>
<style lang="less" scoped>
.table {
    overflow: hidden;
}
.show-state {
    position: absolute;
    top: 46px;
    left: 775px;
    width: 120px;
    height: 60px;
    background: url(../../assets/FixedAssets/020FixedAssets.png) no-repeat 0 -120px;
    z-index: 10000;
    pointer-events: none;
}
.erp-show-state {
    left: 600px;
}
.tree-table {
    :deep(.el-table__expand-icon) {
        visibility: hidden;
        display: none !important;
    }
    :deep(.table-row) {
        background-color: #f8faf8 !important;
        &:hover > td.el-table__cell,
        &.current-row > td.el-table__cell {
            background-color: #f8faf8 !important;
        }
    }
    :deep(.el-table) {
        & > .el-popper {
            max-width: 300px;
            text-align: left;
        }
        .el-scrollbar__view {
            padding-bottom: 0;
        }
        // .el-table__inner-wrapper .el-table__body-wrapper .el-scrollbar .el-scrollbar__wrap {
        //     overflow-x: hidden;
        // }

        // .el-scrollbar__bar.is-horizontal {
        //     display: none !important;
        // }

        .title-column {
            .cell {
                height: 100%;
                display: flex;
                align-items: center;
                > span.pr-10 {
                    padding-right: 6px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }
    }
    &.tree-page-is-not-show {
        :deep(.el-table) {
            .el-scrollbar__view {
                .custom-table {
                    .el-scrollbar__view {
                        padding-bottom: 0px;
                    }
                }
            }
        }
    }
    :deep(.el-table--border .el-table__inner-wrapper::after) {
        height: 0;
    }
    :deep(.el-table__cell .el-table__expanded-cell) {
        border-bottom: none;
    }
    :deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
        z-index: 0 !important;
    }
    :deep(.el-table__row.el-table__row--level-0) {
        background-color: #f8faf8;
    }
    :deep(.el-table__placeholder) {
        display: none;
    }
}
.erp-tree-table {
    :deep(.el-table__row.el-table__row--level-0) {
        background-color: var(--table-hover-color);
    }
    .datagrid-pager {
        border: none;
    }
}
:deep(.none) {
    display: none;
}
</style>
