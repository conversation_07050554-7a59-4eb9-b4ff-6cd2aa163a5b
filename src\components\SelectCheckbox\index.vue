<template>
    <div class="select-checkbox" :style="{ width: width }" ref="selectCheckboxRef">
        <div class="top-box" @click="selectToggle('click')">
            <Tooltip :content="searchVal" :isInput="true" placement="right" :teleported="teleported">
                <el-input type="text" :placeholder="inputPlaceholder" :disabled="disabled" :value="searchVal" @focus.stop.prevent="selectToggle('focus')" />
            </Tooltip>
            <div id="toggleCheckbox" :class="checkBoxShow ? 'bg-click open' : 'bg-click'" @click.stop.prevent="toggleOptions">
                <template v-if="props.useElIcon">
                    <el-icon>
                        <ArrowDown />
                    </el-icon>
                </template>
                <template v-else>
                    <el-icon class="el-select__caret"></el-icon>
                </template>
            </div>
        </div>
        <div class="check-box" :style="{ zIndex }" v-show="checkBoxShow">
            <slot name="cancel-button"></slot>
            <el-scrollbar max-height="200px" :always="true">
                <el-checkbox name="all" v-show="showAll" v-model="all" @change="(check) => allChange(check)"> 全部 </el-checkbox>
                <el-checkbox-group v-model="selectedList" style="display: flex; flex-direction: column">
                    <Checkbox
                        v-for="(item, index) in options"
                        :key="item.id"
                        :label="item.id"
                        :text="item.name"
                        @change="(check) => changeOption(check, index,item.id)"
                    ></Checkbox>
                </el-checkbox-group>
            </el-scrollbar>
            <slot name="buttom-button">
                <div style="border-top: solid 1px var(--border-color); padding: 0; margin: 0; text-align: center">
                    <a class="link" style="text-align: center" @click="handleClick">{{ bottomText }}</a>
                </div>
            </slot>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted, watchEffect } from "vue";
import type { Option } from "./types";
import Checkbox from "./Checkbox.vue";
import Tooltip from "@/components/Tooltip/index.vue";

const selectCheckboxRef = ref<HTMLDivElement>();

const props = withDefaults(
    defineProps<{
        options: Option[];
        selectedList: number[];
        useClick?: boolean;
        width?: string;
        useElIcon?: boolean;
        forcedShutdown?: boolean;
        selectAll?: boolean;
        showAll?: boolean;
        placeHolder?: string;
        teleported?: boolean;
        bottomText?: string;
        disabled?: boolean;
        inputPlaceholder?: string;
        isShowAllInfo?: boolean;
        zIndex?: number;
    }>(),
    {
        useClick: false,
        width: "200px",
        inputPlaceholder: "",
        useElIcon: false,
        forcedShutdown: false,
        showAll: true,
        teleported: false,
        bottomText: "",
        disabled: false,
        isShowAllInfo: false,
        zIndex: 100,
    }
);

const emit = defineEmits<{
    (event: "update:selectedList", args: number[]): void;
    (event: "checkBoxShowChange", args: boolean): void;
    (event: "handleClick"): void;
    (event: "change", checked: boolean, value:any ,isAll?:true): void;
}>();
const selectedList = computed({
    get() {
        return props.selectedList;
    },
    set(value: number[]) {
        emit("update:selectedList", value);
    },
});
const options = computed(() => props.options);

const searchVal = ref("");
const all = ref(true);
if (props.selectAll != undefined) {
    all.value = props.selectAll;
}

const checkBoxShow = ref(false);

const selectToggle = (operate: string) => {
    operate === "click" && toggleOptions();
};
const toggleOptions = () => {
    checkBoxShow.value = !checkBoxShow.value;
    emit("checkBoxShowChange", checkBoxShow.value);
};
const allChange = (checked: any) => {
    selectedList.value = checked ? options.value.map((item) => item.id) : [];
    emit('change', checked ,selectedList.value,true);
};

const changeOption = (checked: any, index: number,value:any) => {
   emit('change', checked ,value);
};

watchEffect(() => {
    const val = selectedList.value;
    all.value = val.length === options.value.length;
    const arr = options.value
        .filter((item) => {
            if (val.includes(item.id)) {
                return item;
            }
        })
        .map((item) => item.name);

    if (arr.length === 0) {
        if (props.placeHolder) {
            searchVal.value = props.placeHolder;
        } else {
            searchVal.value = "";
        }
    } else if (arr.length < options.value.length) {
        searchVal.value = arr.join("；");
    } else if (arr.length === options.value.length) {
        searchVal.value = props.isShowAllInfo ? arr.join("；"):"全部";
    }
});

watch(
    () => props.forcedShutdown,
    () => {
        if (checkBoxShow.value) {
            checkBoxShow.value = false;
        }
    }
);

const changeShow = (show: boolean) => (checkBoxShow.value = show);
const changeSelectAll = (val: boolean) => (all.value = val);

defineExpose({ changeShow, changeSelectAll });

const setBoxNotShow = (e: MouseEvent) => {
    if (!selectCheckboxRef.value?.contains(e.target as HTMLElement)) {
        checkBoxShow.value = false;
        if (selectedList.value.length > 0) {
            emit("checkBoxShowChange", checkBoxShow.value);
        }
    }
};

const handleClick = () => {
    emit("handleClick");
};
onMounted(() => {
    document.addEventListener("click", (e) => setBoxNotShow(e));
});
onUnmounted(() => {
    document.removeEventListener("click", setBoxNotShow);
});
</script>

<style lang="less" scoped>
@import "@/style/common.less";

.select-checkbox {
    position: relative;
    width: 200px;

    .check-box {
        width: 100%;
        border: 1px solid var(--border-color);
        box-sizing: border-box;
        background-color: var(--white);
        // max-height: 233px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 32px;
        left: 0;
        border-top: none;

        :deep(.el-checkbox-group) {
            width: 100%;
        }

        :deep(.el-checkbox) {
            width: 100%;
            margin-right: 0;
            padding: 4px 8px;
            box-sizing: border-box;
            min-height: 20px;
            height: auto;

            &:hover {
                background-color: var(--table-hover-color);
            }

            .el-checkbox__input.is-checked + .el-checkbox__label {
                color: var(--font-color);
            }

            display: flex;
            align-items: flex-start;

            .el-checkbox__label {
                flex: 1;
                text-align: left;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                /* 设置最多显示2行 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                white-space: normal;
                word-break: break-all;
                text-align: left;
                line-height: var(--line-height);
                vertical-align: baseline;
                font-size: var(--font-size);
            }

            .el-checkbox__input {
                margin-top: 3px;
            }
        }

        :deep(.el-scrollbar) {
            .el-scrollbar__wrap {
                overflow-y: auto;
            }

            .el-scrollbar__view {
                display: flex;
                flex-direction: column;
                align-items: start;
            }

        }
    }

    .top-box {
        box-sizing: border-box;
        height: 32px;
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;

        :deep(.span_wrap) {
            width: 100%;
        }

        input {
            width: 100%;
            height: 32px;
            border: 1px solid var(--input-border-color);
            outline: none;
            padding-left: 10px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            box-sizing: border-box;
            border-radius: var(--input-border-radius);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-right: 21px;
        }

        .bg-click {
            position: absolute;
            right: 0;
            top: 0px;
            height: 100%;
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: start;
            padding-right: 1px;

            .el-icon {
                color: var(--el-text-color-placeholder);
                transition: transform var(--el-transition-duration);
                transform: rotateZ(0);
            }

            &.open {
                .el-icon {
                    transform: rotateZ(-180deg);
                }
            }
        }
    }
}
</style>
