<template>
    <el-tooltip placement="bottom" effect="dark">
        <template #content
            ><div class="help-tip-content" ref="tipRef">
                <div class="help-tip-remark" v-show="!isVirtual && !isProSystem">
                    移交：移交账套后，您将不能再查看本账套，您拥有的本账套权限会转移给接收人
                </div>
                <div class="help-tip-remark">新增成员：您可以增加新成员加入本账套，并且让他拥有本账套相应的权限</div>
                <div class="help-tip-round"></div>
                <div class="help-tip-title">账套管理员</div>
                <div class="help-tip-message" v-show="!notShowAll">
                    凭证 | 资金 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 账簿 | 报表 | 新增账套 | 删除账套 | 备份恢复 | 重新初始化账套 |
                    关联进销存
                </div>
                <div class="help-tip-message" v-show="notShowAll">
                    凭证 | 资金 | 发票 | 工资 | 资产 | 期末结转 | 账簿 | 报表 | 新增账套 | 删除账套 | 备份恢复 | 重新初始化账套
                </div>
                <div class="help-tip-round"></div>
                <div class="help-tip-title">主管</div>
                <div class="help-tip-message" v-show="!isProSystem && !notShowAll">
                    凭证 | 资金 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 账簿 | 报表 | 新增账套
                </div>
                <div class="help-tip-message" v-show="!isProSystem && notShowAll">
                    凭证 | 资金 | 发票 | 工资 | 资产 | 期末结转 | 账簿 | 报表 | 新增账套
                </div>
                <div class="help-tip-message" v-show="isProSystem && !notShowAll">
                    凭证 | 资金 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 账簿 | 报表
                </div>
                <div class="help-tip-message" v-show="isProSystem && notShowAll">
                    凭证 | 资金 | 发票 | 工资 | 资产 | 期末结转 | 账簿 | 报表
                </div>
                <div class="help-tip-round"></div>
                <div class="help-tip-title">制单人</div>
                <div class="help-tip-message" v-show="!isProSystem && !notShowAll">
                    凭证 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 查看资金、账簿和报表 | 新增账套
                </div>
                <div class="help-tip-message" v-show="!isProSystem && notShowAll">
                    凭证 | 发票 | 工资 | 资产 | 期末结转 | 查看资金、账簿和报表 | 新增账套
                </div>
                <div class="help-tip-message" v-show="isProSystem && !notShowAll">
                    凭证 | 发票 | 工资 | 资产 | 税务 | 期末结转 | 查看资金、账簿和报表
                </div>
                <div class="help-tip-message" v-show="isProSystem && notShowAll">
                    凭证 | 发票 | 工资 | 资产 | 期末结转 | 查看资金、账簿和报表
                </div>
                <div class="help-tip-round"></div>
                <div class="help-tip-title">出纳</div>
                <div class="help-tip-message" v-show="!isProSystem">资金 | 查看凭证、资产、账簿和报表 | 新增账套</div>
                <div class="help-tip-message" v-show="isProSystem">资金 | 查看凭证、资产、账簿和报表</div>
                <div class="help-tip-round"></div>
                <div class="help-tip-title">查看</div>
                <div class="help-tip-message" v-show="!isProSystem">查看凭证、资金、资产、账簿和报表 | 新增账套</div>
                <div class="help-tip-message" v-show="isProSystem">查看凭证、资金、资产、账簿和报表</div>
                <div class="help-info-message">
                    <div>提示：如需凭证审核功能，请联系管理员在“设置—账套—编辑账套”启用凭证审核功能；</div>
                    <div class="help-info-indent">当您审核凭证时，个人设置的姓名即为您的审核人姓名。</div>
                </div>
            </div>
        </template>
        <a class="help-icon"></a>
    </el-tooltip>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { isVirtualAccount } from "@/util/wxwork";

const tipRef = ref<HTMLDivElement>();
const accountSet = useAccountSetStore().accountSet;
const accountStandard = accountSet?.accountingStandard;
const isProSystem = ref(window.isProSystem);
const notShowAll = ref(accountStandard === 4 || accountStandard === 6 || accountStandard === 7);
const isVirtual = ref(await isVirtualAccount());
</script>

<style lang="less" scoped>
.help-icon {
    margin-top: 6px;
    display: inline-flex;
    height: 16px;
    width: 16px;
    background: url(@/assets/Settings/help.png) no-repeat;
    vertical-align: top;
}

.help-tip-content {
    width: 530px;
    text-align: left;
    display: block;
    padding: 20px 20px 6px 20px;
    .help-tip-remark {
        color: var(--white);
        font-size: var(--h4);
        line-height: 20px;
        margin-bottom: 14px;
    }
    .help-tip-round {
        margin-top: 8px;
        margin-right: 10px;
        margin-left: 10px;
        width: 4px;
        height: 4px;
        background: rgba(255, 255, 255, 0.45);
        float: left;
    }
    .help-tip-title {
        color: var(--white);
        font-size: var(--h4);
        line-height: 20px;
        margin-bottom: 2px;
    }
    .help-tip-message {
        color: var(--white);
        font-size: var(--h5);
        line-height: 17px;
        margin-bottom: 14px;
        margin-left: 24px;
        width: 473px;
    }
    .help-info-message {
        color: var(--white);
        font-size: var(--h5);
        line-height: 17px;
        margin-top: 20px;
        margin-bottom: 20px;
        .help-info-indent {
            margin-left: 36px;
        }
    }
}
</style>
