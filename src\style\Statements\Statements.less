@import (reference) "../Functions.less";
.main-top {
    .uneven-prompt {
        position: relative;
        flex-shrink: 1;
        .uneven-prompt-icon {
            width: 14px;
            height: 14px;
            padding-right: 4px;
            position: absolute;
            top: 3px;
        }
        .uneven-prompt-text {
            position: relative;
            top: 1px;
            display: inline-block;
            margin-left: 30px;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            @media (max-width: 1240px) {
                max-width: 240px;
            }
            @media (min-width: 1240px) {
                max-width: 500px;
            }
        }
    }
}
:deep(.main-center) {
    tr {
        td {
            .cell {
                cursor: default;
                .default-content {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .level1 {
                    font-size: var(--font-size);
                    font-weight: bold;
                    overflow: hidden;
                    vertical-align: middle;

                    span {
                        vertical-align: middle;
                    }

                    .link {
                        margin-left: 10px;
                        display: none;
                        vertical-align: middle;
                        font-weight: normal;
                    }
                }

                .level2 {
                    overflow: hidden;
                    padding-left: 20px;

                    span {
                        display: inline-block;
                        overflow: hidden;
                        vertical-align: middle;
                    }

                    .link {
                        margin-left: 10px;
                        display: none;
                        vertical-align: middle;
                    }

                    &.expand-icon {
                        background-repeat: no-repeat;
                        background-position-x: 0;
                        background-position-y: 2.5px;
                        background-image: url("@/assets/Statements/down.png");

                        &.down {
                            background-image: url("@/assets/Statements/up.png");
                        }
                    }
                }

                .level3,
                .level4 {
                    overflow: hidden;
                    padding-left: 40px;

                    span {
                        display: inline-block;
                        overflow: hidden;
                        vertical-align: middle;
                    }

                    .link {
                        margin-left: 10px;
                        display: none;
                        vertical-align: middle;
                    }
                }

                .level4 {
                    padding-left: 60px;
                }

                .calc-icon {
                    width: 18px;
                    height: 16px;
                    float: left;
                    position: relative;
                    background-position-y: 2.5px;
                    padding-left: 18px;
                    height: 0;
                    background: url("@/assets/Statements/equal.png") no-repeat 0 0;
                    background-size: 12px 12px;
                }

                .edit-equation-icon {
                    display: inline-block;
                    width: 0;
                    overflow: hidden;
                }

                .custom-input {
                    input[type="text"] {
                        border: none;
                    }
                }
            }
        }
        &:hover {
            td {
                .cell {
                    .level1,
                    .level2,
                    .level3,
                    .level4 {
                        .link {
                            display: inline-block;
                        }
                    }

                    .calc-icon {
                        height: 16px;
                        background-image: url("@/assets/Statements/equal.png");
                    }
                    .edit-equation-icon {
                        width: 12px;
                    }
                    .cell-link {
                        &:after {
                            content: "";
                            position: absolute;
                            bottom: 1px;
                            left: 0;
                            right: 0;
                            border-bottom: 1px solid;
                        }
                    }
                }
            }
        }
        &.hidden-row {
            display: none;
        }
        &.default-edit-row {
            td {
                .cell {
                    .edit-equation-icon {
                        width: 12px;
                    }
                }
            }
        }
    }
}
// // 兼容业财样式
:deep(.erp-table) {
    .highlight-title-row {
        background-color: var(--erp-table-hover-color);
    }
    tr {
        &:hover {
            td {
                .cell {
                    .calc-icon {
                        width: 12px;
                        height: 12px;
                        background-position: 0;
                        background-size: contain;
                        background-image: url("@/assets/Statements/equal-erp.png");
                    }
                }
            }
        }
    }
}

.content {
    height: 100%;
    .main-content {
        height: 100%;
        :deep(.main-center) {
            flex: 1;
            overflow: hidden;
            .el-table {
                height: 100%;
            }
        }
    }
    .slot-content,
    .slot-mini-content {
        height: 100%;
    }
}
:deep(.table-header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.table-header {
    :deep(.el-icon) {
        color: #333;
        font-size: 14px;
        &:hover {
            color: #3385ff;
            cursor: pointer;
        }
    }
}
