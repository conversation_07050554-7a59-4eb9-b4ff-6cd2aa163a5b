import { ElNotify } from "@/util/notify";
import { isNumberOrLetter } from "@/util/validator";
const checkMobileLength = (s: string) => {
    return s.length === 11;
};
const isSpecificSymbol = (type: string) => {
    type += "";
    return type.indexOf("\\") > -1 || type.indexOf('"') > -1 || type.indexOf("'") > -1 || type.indexOf("\\n") > -1;
};
// 辅助核算类别
export const validAATypeName = (value: string, EditType: string) => {
    if (value.trim() == "" && EditType != "Delete") {
        ElNotify({ message: "亲，请输入类别名称！", type: "warning" });
        return false;
    }
    if (value && isSpecificSymbol(value)) {
        ElNotify({ message: "辅助核算类别不能带有特殊字符！", type: "warning" });
        return false;
    }
    return true;
};
// 自定义列
export const validUserDefined = (value: string, index: number) => {
    if (value.length > 256) {
        ElNotify({ message: "自定义列" + (index + 1) + "名称不能大于256位", type: "warning" });
        return false;
    }
    return true;
};
// 辅助核算提交校验
export const ValidataAaType = (columnArr?: string[]) => {
    columnArr?.forEach((item) => {
        if (item != undefined) {
            if (item && isSpecificSymbol(item)) {
                ElNotify({ message: "自定义列不能带有特殊字符！", type: "warning" });
                return false;
            }
        }
    });
    return true;
};

export const ValidataCustomerOrVendor = (aaEntity: any, fatype: number, aaNum: string, aaName: string) => {
    const { type, mobilePhone, address, contact, taxNumber, note } = aaEntity;
    const name = fatype === 10001 ? "客户" : "供应商";
    if (aaNum.trim().length === 0) {
        ElNotify({ message: name + "编码不能为空！", type: "warning" });
        return false;
    } else if (!isNumberOrLetter(aaNum)) {
        ElNotify({ type: "warning", message: name + "编码不是数字和字母组合，请修改后重试~" });
        return false;
    } else if (aaNum.length > 18) {
        ElNotify({ type: "warning", message: name + "编码为不超过18位的字母或数字组合！" });
        return false;
    } else if (aaName.trim().length === 0) {
        ElNotify({ message: name + "名称不能为空！", type: "warning" });
        return false;
    } else if (aaName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return false;
    } else if (mobilePhone && !checkMobileLength(mobilePhone)) {
        ElNotify({ message: "请输入正确的手机号码！", type: "warning" });
        return false;
    } else if (type && isSpecificSymbol(type)) {
        ElNotify({ message: name + "类别不能带有特殊字符！", type: "warning" });
        return false;
    } else if (address && isSpecificSymbol(address)) {
        ElNotify({ message: "经营地址不能带有特殊字符！", type: "warning" });
        return false;
    } else if (contact && isSpecificSymbol(contact)) {
        ElNotify({ message: "联系人不能带有特殊字符！", type: "warning" });
        return false;
    } else if (taxNumber && isSpecificSymbol(taxNumber)) {
        ElNotify({ message: "税号不能带有特殊字符！", type: "warning" });
        return false;
    } else if (note && isSpecificSymbol(note)) {
        ElNotify({ message: "备注不能带有特殊字符！", type: "warning" });
        return false;
    } else if (address.length > 64) {
        ElNotify({ message: "亲，地址最多支持64个字哦~", type: "warning" });
        return false;
    }
    return true;
};
export const ValidataEMployee = (aaEntity: any, aaNum: string, aaName: string) => {
    const { mobilePhone, departmentId, departmentName, title, position, note } = aaEntity;
    if (aaNum.trim().length === 0) {
        ElNotify({ message: "职员编码不能为空！", type: "warning" });
        return false;
    } else if (!isNumberOrLetter(aaNum)) {
        ElNotify({ type: "warning", message: "职员编码不是数字和字母组合，请修改后重试~" });
        return false;
    } else if (aaNum.length > 18) {
        ElNotify({ type: "warning", message: "职员编码为不超过18位的字母或数字组合！" });
        return false;
    } else if (aaName.trim().length === 0) {
        ElNotify({ message: "职员名称不能为空！", type: "warning" });
        return false;
    } else if (aaName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return false;
    } else if (mobilePhone && !checkMobileLength(mobilePhone)) {
        ElNotify({ message: "请输入正确的手机号码！", type: "warning" });
        return false;
    } else if (departmentId && isSpecificSymbol(departmentId)) {
        ElNotify({ message: "部门编码不能带有特殊字符！", type: "warning" });
        return false;
    } else if (departmentName && isSpecificSymbol(departmentName)) {
        ElNotify({ message: "部门名称不能带有特殊字符！", type: "warning" });
        return false;
    } else if (title && isSpecificSymbol(title)) {
        ElNotify({ message: "职务不能带有特殊字符！", type: "warning" });
        return false;
    } else if (position && isSpecificSymbol(position)) {
        ElNotify({ message: "岗位不能带有特殊字符！", type: "warning" });
        return false;
    } else if (note && isSpecificSymbol(note)) {
        ElNotify({ message: "备注不能带有特殊字符！", type: "warning" });
        return false;
    }
    return true;
};
export const ValidataDepartment = (aaEntity: any, aaNum: string, aaName: string) => {
    const { mobilePhone, manager, note } = aaEntity;
    if (aaNum.trim().length === 0) {
        ElNotify({ message: "部门编码不能为空！", type: "warning" });
        return false;
    } else if (!isNumberOrLetter(aaNum)) {
        ElNotify({ type: "warning", message: "部门编码不是数字和字母组合，请修改后重试~" });
        return false;
    } else if (aaNum.length > 18) {
        ElNotify({ type: "warning", message: "部门编码为不超过18位的字母或数字组合！" });
        return false;
    } else if (aaName.trim().length === 0) {
        ElNotify({ message: "部门名称不能为空！", type: "warning" });
        return false;
    } else if (aaName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return false;
    } else if (mobilePhone && !checkMobileLength(mobilePhone)) {
        ElNotify({ message: "请输入正确的手机号码！", type: "warning" });
        return false;
    } else if (manager && isSpecificSymbol(manager)) {
        ElNotify({ message: "负责人不能带有特殊字符！", type: "warning" });
        return false;
    } else if (note && isSpecificSymbol(note)) {
        ElNotify({ message: "备注不能带有特殊字符！", type: "warning" });
        return false;
    }
    return true;
};
export const ValidataProject = (aaEntity: any, aaNum: string, aaName: string) => {
    const { mobilePhone, department, owner, note } = aaEntity;
    if (aaNum.trim().length === 0) {
        ElNotify({ message: "项目编码不能为空！", type: "warning" });
        return false;
    } else if (!isNumberOrLetter(aaNum)) {
        ElNotify({ type: "warning", message: "项目编码不是数字和字母组合，请修改后重试~" });
        return false;
    } else if (aaNum.length > 18) {
        ElNotify({ type: "warning", message: "项目编码为不超过18位的字母或数字组合！" });
        return false;
    } else if (aaName.trim().length === 0) {
        ElNotify({ message: "请输入名称！", type: "warning" });
        return false;
    } else if (aaName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return false;
    } else if (mobilePhone && !checkMobileLength(mobilePhone)) {
        ElNotify({ message: "请输入正确的手机号码！", type: "warning" });
        return false;
    } else if (department && isSpecificSymbol(department)) {
        ElNotify({ message: "负责部门不能带有特殊字符！", type: "warning" });
        return false;
    } else if (owner && isSpecificSymbol(owner)) {
        ElNotify({ message: "责任人不能带有特殊字符！", type: "warning" });
        return false;
    } else if (note && isSpecificSymbol(note)) {
        ElNotify({ message: "备注不能带有特殊字符！", type: "warning" });
        return false;
    }
    return true;
};
export const ValidataStock = (aaEntity: any, aaNum: string, aaName: string) => {
    const { unit, stockModel, note } = aaEntity;
    if (aaNum.trim().length === 0) {
        ElNotify({ message: "存货编码不能为空！", type: "warning" });
        return false;
    } else if (!isNumberOrLetter(aaNum)) {
        ElNotify({ type: "warning", message: "存货编码不是数字和字母组合，请修改后重试~" });
        return false;
    } else if (aaNum.length > 18) {
        ElNotify({ type: "warning", message: "存货编码为不超过18位的字母或数字组合！" });
        return false;
    } else if (aaName.trim().length === 0) {
        ElNotify({ message: "存货名称不能为空！", type: "warning" });
        return false;
    } else if (aaName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return false;
    } else if (stockModel && isSpecificSymbol(stockModel)) {
        ElNotify({ message: "规格型号不能带有特殊字符！", type: "warning" });
        return false;
    } else if (note && isSpecificSymbol(note)) {
        ElNotify({ message: "备注不能带有特殊字符！", type: "warning" });
        return false;
    } else if (unit && isSpecificSymbol(unit)) {
        ElNotify({ message: "计量单位不能带有特殊字符！", type: "warning" });
        return false;
    }
    return true;
};
export const ValidataCustom = (aaEntity: any, aaNum: string, aaName: string, aaTypeName: string) => {
    const { note } = aaEntity;
    const arr = Object.keys(aaEntity).filter((item) => item.includes("value"));
    if (aaNum.trim().length === 0) {
        ElNotify({ message: "编码不能为空！", type: "warning" });
        return false;
    } else if (!isNumberOrLetter(aaNum)) {
        ElNotify({ type: "warning", message: aaTypeName + "编码不是数字和字母组合，请修改后重试~" });
        return false;
    } else if (aaNum.length > 18) {
        ElNotify({ type: "warning", message: aaTypeName + "编码为不超过18位的字母或数字组合！" });
        return false;
    } else if (aaName.trim().length === 0) {
        ElNotify({ message: "名称不能为空！", type: "warning" });
        return false;
    } else if (aaName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return false;
    } else if (note && isSpecificSymbol(note)) {
        ElNotify({ message: "备注不能带有特殊字符！", type: "warning" });
        return false;
    }
    arr.forEach((item) => {
        if (item && isSpecificSymbol(item)) {
            ElNotify({ message: "自定义列不能带有特殊字符！", type: "warning" });
        }
    });
    return true;
};
