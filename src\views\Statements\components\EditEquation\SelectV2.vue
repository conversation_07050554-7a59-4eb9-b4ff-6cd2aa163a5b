<template>
    <el-select-v2
        ref="selectRef"
        v-model="value"
        :filterable="filterable"
        :class="[props.class, 'select', 'custom-select-v2']"
        :popper-class="popperClass + ' select-down'"
        :options="options"
        :placeholder="placeholder"
        :style="style"
        :teleported="teleported"
        :scrollbar-always-on="scrollbarAlwaysOn"
        :item-height="28"
        :height="198"
        :props="prop"
        :reserve-keyword="reserveKeyword"
        :allow-create="allowCreate"
        @change="handleChange"
        @visible-change="handleVisibleChange"
        @input="handleInput"
        @focus="handleFocus"
        @mouseenter="enterInput"
        @mouseleave="leaveInput"
        :remote="remote"
        :remote-method="filterMethod"
    >
        <template #prefix>
            <div v-show="clearable && iconFlag" class="icon_clear_v2" @click="handleClearClick" :style="{ right: iconClearRight + 'px' }">
                <el-icon><Close /></el-icon>
            </div>
        </template>
        <template #default="{ item }">
            <ToolTip
                v-if="props.computedByWidth"
                :content="item[prop.label]"
                :line-clamp="1"
                :teleported="true"
                :maxWidth="props.maxWidth"
                placement="right-start"
            >
                {{ item[prop.label] }}
            </ToolTip>
            <el-tooltip
                v-else
                :key="item.value"
                popper-class="el-option-tool-tip"
                :visible="currentItem === item.value"
                effect="light"
                :content="item[prop.label]"
                placement="right"
                :hide-after="0"
            >
                <div class="textOverFlow" ref="optionRef" @mouseenter="showTooltip(item)" @mouseleave="currentItem = -1">
                    {{ item[prop.label] }}
                </div>
            </el-tooltip>
        </template>
    </el-select-v2>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import ToolTip from "@/components/ToolTip/index.vue";

interface IProps {
    label?: string;
    value?: string;
    options?: string;
    disabled?: string;
    // 根据宽度显示气泡
    computedByWidth?: boolean;
    maxWidth?: number;
}

const defaultProps = {
    label: "label",
    value: "value",
    options: "options",
    disabled: "disabled",
    computedByWidth: false,
    maxWidth: 300,
};

const selectRef = ref();

const props = withDefaults(
    defineProps<{
        options: Array<any>;
        modelValue: string | number;
        teleported?: boolean;
        class?: string;
        filterable?: boolean;
        placeholder?: string;
        scrollbarAlwaysOn?: boolean;
        style?: any;
        popperClass?: string;
        clearable?: boolean;
        iconClearRight?: number;
        allowCreate?: boolean;
        props?: IProps;
        reserveKeyword?: boolean;
        computedByWidth?: boolean;
        maxWidth?: number;
        remote?: boolean;
        filterMethod?: Function;
    }>(),
    {
        teleported: true,
        filterable: false,
        class: "",
        placeholder: "",
        scrollbarAlwaysOn: true,
        style: "",
        popperClass: "",
        clearable: false,
        iconClearRight: 18,
        allowCreate: false,
        props: () => ({}),
        reserveKeyword: true,
        computedByWidth: false,
        maxWidth: 300,
        remote: false,
        filterMethod: ()=>{},
    }
);

const prop = Object.assign({}, defaultProps, props.props);

const emit = defineEmits<{
    (event: "update:modelValue", args: string | number): void;
    (event: "change", args: string): void;
    (event: "visible-change", args: boolean): void;
    (event: "input", args: string): void;
    (event: "focus", args: FocusEvent): void;
    (event: "keydownEnter", args: KeyboardEvent): void;
}>();

const value = computed<any>({
    get() {
        return props.modelValue ? props.modelValue : undefined;
    },
    set(value: string | number) {
        emit("update:modelValue", value === undefined ? "" : value);
    },
});

const handleChange = (value: string) => {
    emit("change", value);
};
const handleVisibleChange = (value: boolean) => {
    if (props.remote) {
        const suffixEl = selectRef.value?.$el?.querySelector(".el-select-v2__suffix .el-select-v2__caret");
        if (value) { 
            suffixEl && suffixEl.classList.add("is-reverse");
        } else {
            suffixEl && suffixEl.classList.remove("is-reverse");
        }
    }
    emit("visible-change", value);
};
const handleInput = (event: InputEvent) => {
    emit("input", (event.target as HTMLInputElement)?.value || "");
};
const handleFocus = (event: FocusEvent) => {
    emit("focus", event);
};

const handleClearClick = () => {
    value.value = "";
    iconFlag.value = false;
};
const enterInput = () => {
    iconFlag.value = value.value ? true : false;
};
const leaveInput = () => {
    iconFlag.value = false;
};
const iconFlag = ref(false);
const currentItem = ref(-1);
const optionRef = ref();
const showTooltip = (item: any) => {
    const element = optionRef.value;
    const width = element.getBoundingClientRect().width;
    if (item.label.length * 14 > width) {
        currentItem.value = item.value;
    } else {
        currentItem.value = -1;
    }
};
onMounted(() => {
    if (props.remote) {
        const suffixEl = selectRef.value?.$el?.querySelector(".el-select-v2__suffix");
        if (suffixEl) {
            const html = `
                <i class="el-icon el-select-v2__caret el-input__icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                        <path fill="currentColor" d="m488.832 344.32-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872 319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0"></path>
                    </svg>
                </i>
            `;
            suffixEl.insertAdjacentHTML('beforeend', html);
        }
    }
});
</script>

<style lang="less">
.icon_clear_v2 {
    position: absolute;
    right: 0 !important;
    width: 20px;
    height: 20px;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.custom-select-v2 {
    .el-select-v2__wrapper {
        > div {
            &:not(.el-select-v2__input-wrapper) {
                width: 20px;
                height: 20px;
                position: absolute;
                right: 30px;
            }
        }
    }
}
.el-select-dropdown .el-select-dropdown__list .el-select-dropdown__option-item {
    font-size: 14px;
    .textOverFlow {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
    }
}
</style>
