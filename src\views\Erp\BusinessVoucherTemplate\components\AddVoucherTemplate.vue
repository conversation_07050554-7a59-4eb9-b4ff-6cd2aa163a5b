<template>
    <div class="slot-content">
        <div class="slot-top">
            <div class="main-top-left">
                <a class="button solid-button mr-20" @click="sumbitTemplate">保存</a>
                <a class="button solid-button mr-20" v-show="feature === 'edit'" @click="handleResetTemplate">重置</a>
                <a class="button" @click="cancelTemplate">取消</a>
            </div>
            <span id="editTitle">{{ editTitle }}单模板</span>
        </div>
        <div class="add-info-wrap">
            <div class="add-info">
                <div class="row">
                    <div class="add-field">
                        <span>单据类型：</span>
                        <Select v-model="info.vtType" style="width: 150px" @change="handleVtTypeChange" :disabled="feature === 'edit'">
                            <Option v-for="item in vtTypeList" :value="item.value" :key="item.value" :label="item.label"></Option>
                        </Select>
                    </div>
                    <div class="add-field" v-show="checkShowBusinessType">
                        <span>{{ childType }}类型：</span>
                        <Select v-model="info.billType" style="width: 150px" @change="handleBusinessChange" :disabled="feature === 'edit'">
                            <Option v-for="item in businessTypes" :value="item.vtType" :key="item.vtType" :label="item.vtTypeName"></Option>
                        </Select>
                    </div>
                    <div class="add-field overflow">
                        <span>模板编码：</span>
                        <ToolTip :isInput="true" :max-width="86" :content="info.code">
                            <el-input v-model="info.code" style="width: 108px"></el-input>
                        </ToolTip>
                    </div>
                    <div class="add-field overflow">
                        <span>模板名称：</span>
                        <ToolTip :isInput="true" :max-width="86" :content="info.vtName">
                            <el-input v-model="info.vtName" style="width: 108px"></el-input>
                        </ToolTip>
                    </div>
                    <div class="add-field">
                        <span>凭证字：</span>
                        <el-select v-model="info.vgId" style="width: 108px">
                            <el-option v-for="item in voucherGroupList" :key="item.id" :value="item.id" :label="item.name"></el-option>
                        </el-select>
                    </div>
                    <div class="add-field" v-show="!isDefaultShow">
                        <span>是否默认<span class="ml-10">：</span></span>
                        <el-checkbox v-model="info.isDefault"></el-checkbox>
                        <el-popover :width="300" placement="top-start">
                            <div>
                                1.设置为默认模板后，业务凭证中对应单据类型凭证模板会默认这个模板
                                <br />
                                2. 每个单据类型只能有一个默认模板，当前模板设置为默认后，原默认模板会自动取消默认
                            </div>
                            <template #reference>
                                <span class="question"></span>
                            </template>
                        </el-popover>
                    </div>
                </div>
                <div class="row">
                    <div class="add-field">
                        <span>备注：</span>
                        <div class="note-container" :class="{ editing: noteEditing }">
                            <ToolTip :content="info.note" :maxWidth="210">
                                <div class="display" @click="handleNoteClick">{{ info.note }}</div>
                            </ToolTip>
                            <el-input
                                ref="noteRef"
                                :autosize="{ minRows: 1, maxRows: 3.5 }"
                                type="textarea"
                                v-model="info.note"
                                resize="none"
                                @blur="handleNoteBlur"
                                @input="handleNoteInput"
                            ></el-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class="add-voucher-template">
                <Table
                    ref="tableRef"
                    add-sub-field="index"
                    :data="tableData"
                    :columns="columns"
                    :hasAddSub="true"
                    :customSubtract="true"
                    :continuousClick="true"
                    :can-copy="true"
                    :show-overflow-tooltip="false"
                    :customAdd="true"
                    @table-add-or-subtract="handleTableAddSub"
                    @handleSubtract="handleSubrtact"
                    @cell-click="handleCellClick"
                    @copy="handleCopy"
                    @handleAdd="handleAdd"
                    @scroll="handleTableScroll"
                >
                    <template #index>
                        <el-table-column label="序号" width="48px">
                            <template #default="{ row }: { row: VoucherTemplateLine }">
                                <span>{{ row.index + 1 }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #description>
                        <el-table-column label="摘要" min-width="300px" :showOverflowTooltip="false">
                            <template #default="{ row }: { row: VoucherTemplateLine }">
                                <div class="description-input-container">
                                    <div ref="descriptionInputContainerRef" v-if="row.descriptionInput" class="description-input">
                                        <el-input
                                            ref="descriptionInputRef"
                                            v-model="row.displayDescription"
                                            @input="(val: string) => handleDescInput(val, row)"
                                            @keydown="handleDescKeyDowm($event, row)"
                                            @blur="reBindSortable"
                                            @focus="unBindSortable"
                                            @mousedown="handleInputMouseDown"
                                        ></el-input>
                                    </div>
                                    <div class="description-input-display" :class="{ active: currentSelectDescIndex === row.index }" v-else>
                                        <ToolTip :teleported="true" :content="row.displayDescription" :line-clamp="1" :dynamic-width="true">
                                            {{ row.displayDescription }}
                                        </ToolTip>
                                    </div>
                                    <span class="more" @click.stop="handleShowMoreDesc(row)"></span>
                                    <span
                                        class="delete-icon"
                                        v-show="row.description || row.customDescriptions.length"
                                        @click.stop="handleDeleteDesc(row)"
                                    >
                                        <el-icon color="#a8abb2"><CircleClose /></el-icon>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #accountSubject>
                        <el-table-column label="会计科目" min-width="300px">
                            <template #default="{ row }: { row: VoucherTemplateLine }">
                                <div class="asub-line-container">
                                    <input
                                        v-if="row.asubIdSelect"
                                        placeholder="请选择或搜索"
                                        class="asub-selector-input"
                                        ref="asubInputRef"
                                        v-model="asubDisplayText"
                                        @input="filterMethod"
                                        @keydown="handleAsubKeyDown"
                                        @keyup.enter="trySelectAsub"
                                        @blur="reBindSortable"
                                        @focus="unBindSortable"
                                        @mousedown="handleInputMouseDown"
                                    />
                                    <div class="asub-input" v-else>
                                        <ToolTip :teleported="true" :content="row.asubName" :line-clamp="1" :dynamic-width="true">
                                            {{ row.asubName }}
                                        </ToolTip>
                                    </div>
                                    <span class="select-icon">
                                        <el-icon v-show="!row.asubIdSelect" color="#a8abb2"><ArrowDown /></el-icon>
                                        <el-icon v-show="row.asubIdSelect" color="#a8abb2"><ArrowUp /></el-icon>
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #direction>
                        <el-table-column min-width="100px" label="方向" :show-overflow-tooltip="false">
                            <template #default="{ row, $index }: { row: VoucherTemplateLine, $index: number }">
                                <el-select
                                    :ref="(el: any) => getDirectionRef(el, $index)"
                                    @focus="hideDescAndAsubSelect"
                                    v-model="row.direction"
                                    style="width: 100%; height: 100%"
                                >
                                    <el-option label="借" :value="1"></el-option>
                                    <el-option label="贷" :value="2"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                    </template>
                    <template #money>
                        <el-table-column label="金额" min-width="150px">
                            <template #default="{ row, $index }: { row: VoucherTemplateLine, $index: number }">
                                <el-select
                                    :ref="(el: any) => getValueTypeRef(el, $index)"
                                    @focus="hideDescAndAsubSelect"
                                    v-model="row.valueType"
                                    style="width: 100%; height: 100%"
                                >
                                    <el-option
                                        v-for="item in amountCalcList"
                                        :key="item.value"
                                        :label="item.text"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="add-tips">
                <div>1.科目使用辅助核算时，柠檬云能自动根据单据信息获取客户、供应商等辅助核算，无需手工指定</div>
                <div>
                    2.损益科目借贷方自动调整：生成凭证时，若收入类方向为借方，则自动调整为贷方负数；若费用类方向为贷方，则自动调整为借方负数
                </div>
            </div>
        </div>
        <div class="asub-selector" ref="asubSelectorRef" v-show="asubSelectorDisplay">
            <div class="asub-selector-type">
                <div
                    v-for="item in asubTypeList"
                    :key="item.value"
                    :class="{ 'type-item': true, active: activeAsubType === item.value }"
                    @click="handleActiveAsubType(item.value)"
                >
                    {{ item.label }}
                    <el-popover
                        placement="top"
                        :width="240"
                        trigger="hover"
                        :content="item.value === AsubSelectorType.Relate ? '根据科目关联设置中的设置取科目' : '默认取这个固定的系统科目'"
                    >
                        <template #reference>
                            <span class="icon" v-show="activeAsubType === item.value"></span>
                        </template>
                    </el-popover>
                </div>
            </div>
            <div class="display-list" ref="asubListRef">
                <el-scrollbar @scroll="handleAllAsubPopover" height="212px" :always="true" ref="scrollbarRef">
                    <template v-for="(item, index) in selectShowOptions" :key="item.value">
                        <div
                            v-if="item.value !== 7010"
                            class="display-item"
                            :class="{
                                current: currentSelectAsubIndex === index,
                                active: item.active,
                                select: item.value === tableData[findCurrentAsubLineIndex()]?.asubId,
                            }"
                            @mouseenter="handleMouseEnter($event, item)"
                            @mouseleave="handleOptionMouseLeave"
                            @click="selectAsub(item)"
                        >
                            {{ item.displayLabel }}
                        </div>
                        <el-popover
                            v-else
                            effect="light"
                            trigger="hover"
                            :teleported="true"
                            :width="408"
                            :offset="0"
                            content="其他支出单中，收款人类型为客户时取应收账款科目，收款人类型为供应商时取应付账款科目，收款人类型为职员时取职员科目"
                            placement="right"
                        >
                            <template #reference>
                                <div
                                    class="display-item"
                                    @click="selectAsub(item)"
                                    :class="{ active: item.active, current: currentSelectAsubIndex === index }"
                                >
                                    {{ item.displayLabel }}
                                </div>
                            </template>
                        </el-popover>
                    </template>
                </el-scrollbar>
                <div class="overflow-toolTip" v-show="asubOverflowToolTipShow" ref="asubOverflowToolTip">
                    {{ tipAsubName }}
                    <span class="allow"></span>
                </div>
            </div>
            <div class="bottom-button">
                <span
                    :class="{ 'not-ps': !checkPermission(['asubrelationsettings-canview']) }"
                    v-show="activeAsubType === AsubSelectorType.Relate"
                    @click="handleToAsubRelation"
                >
                    科目关联设置
                </span>
                <span
                    :class="{ 'not-ps': !checkPermission(['accountsubject-canedit']) }"
                    v-show="activeAsubType === AsubSelectorType.System"
                    @click="handleNewAsub"
                >
                    + 点击添加
                </span>
            </div>
        </div>
        <div
            class="relate-asub-detail-content"
            ref="asubSelectorDetailRef"
            v-show="asubSelectorDetailDisplay"
            @mouseenter="asubSelectorDetailMouseEnter"
            @mouseleave="handleMouseLeave"
        >
            <div class="allow custom"></div>
            <div class="detail-title">默认科目</div>
            <div class="display-detail-asubInfo">
                <div class="left-label">{{ detailDisplayInfo.label + "：" }}</div>
                <ToolTip :line-clamp="1" :content="detailDisplayInfo.asubDisplay" :maxWidth="180">
                    <div class="right-content">{{ detailDisplayInfo.asubDisplay }}</div>
                </ToolTip>
            </div>
            <div class="row">类别明细设置</div>
            <Table :data="detailTableData" :columns="detailColumns" v-loading="loading" element-loading-text="正在加载数据...">
                <template #name>
                    <el-table-column :label="detailName" width="140px" align="left" header-align="left">
                        <template #default="{ row }: { row: DetailAsubRealation }">
                            <span :class="{ link: checkCanLink(row) }" @click="handleRouteToOtherPage(row)">{{ row.name }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #empty>
                    <div class="empty">
                        <div class="row">当前账套没有{{ activeAsubTypeDetailLabel }}类别，生成凭证自动取默认科目</div>
                        <div class="row">
                            您也可以点击
                            <span
                                :class="{ blue: checkHasModulePermission(activeAsubTypeDetail) }"
                                @click="routeToModulePage(activeAsubTypeDetail)"
                            >
                                “{{ activeAsubTypeDetailLabel }}”
                            </span>
                            去维护{{ activeAsubTypeDetailLabel }}类别，再到
                            <span
                                :class="{ blue: checkHasAsubRelatePermission() }"
                                @click="routeToAsubRelationSettings(activeAsubTypeDetail)"
                            >
                                科目关联明细设置
                            </span>
                            中按{{ activeAsubTypeDetailLabel }}类别配置科目哦~
                        </div>
                    </div>
                </template>
            </Table>
        </div>
        <div class="description-selector" v-show="desListSelectorDisplay" ref="desListSelector" @mousedown="$event.stopPropagation()">
            <div class="des-ls">
                <div class="des-i" v-for="(desc, index) in getDescList()" :key="index" @click="desListSelect(desc)">
                    <ToolTip placement="right-start" :content="desc" :line-clamp="1" :dynamic-width="true">{{ desc }}</ToolTip>
                </div>
            </div>
            <div class="buttom-button">
                <span @click="handleShowMoreDesc(tableData[findCurrentSelectDescIndex()])">自定义摘要配置</span>
            </div>
        </div>
        <DialogDescription ref="dialogDescriptionRef" @save="setCustomDesc" @closed="descDialogClosed" />
        <el-dialog
            destroy-on-close
            center
            title="提示"
            v-model="notBalanceTableInfo.display"
            width="587px"
            class="custom-confirm"
            modal-class="modal-class"
            top="5vh"
            @closed="resetUnBalanceTableInfo"
        >
            <div class="check-balance-content">
                <div class="check-balance-main">
                    <div class="icon"></div>
                    <div class="check-title">
                        <div>当前模板配置，会导致凭证借贷不平</div>
                        <div>业务生成的凭证会自动保存为暂存凭证，确定要保存吗？</div>
                    </div>
                    <div class="row">举例说明:</div>
                    <div class="row mb-8">1.{{ notBalanceTableInfo.example }}</div>
                    <div class="row mb-8">2.当前模板</div>
                    <Table :data="tableData" :columns="balanceColumns" :max-height="157"> </Table>
                    <div class="row mt-8 mb-8">3.根据以上例子生成凭证:</div>
                    <Table
                        :data="notBalanceTableInfo.tableData"
                        :columns="notBalanceTableInfo.columns"
                        :max-height="194"
                        :show-summary="true"
                        :summary-method="summaryMethod"
                    >
                    </Table>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="continueSave">继续保存</a>
                    <a class="button" @click="notBalanceTableInfo.display = false">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            destroy-on-close
            v-model="resetDialogInfo.display"
            title="提示"
            center
            width="540px"
            class="custom-confirm"
            modal-class="modal-class"
        >
            <div class="reset-content">
                <div class="reset-label">是否确认按照系统默认模板重置？</div>
                <div class="vt-content">
                    <div class="vt-info">{{ formatVtType() }}系统默认模板:</div>
                    <div class="vt-main">
                        <ToolTip :content="getResetTemplateName()" :font-size="18" :line-clamp="1" :dynamic-width="true">
                            <div class="vt-label">{{ getResetTemplateName() }}</div>
                        </ToolTip>
                        <div class="vt-line" v-for="(line, index) in resetDialogInfo.voucherLines" :key="index">
                            <div class="left">
                                <ToolTip
                                    :content="formatAsubName(line.asubId)"
                                    :calc-content="formatResetLineContent(line)"
                                    :line-clamp="1"
                                    :dynamic-width="true"
                                >
                                    {{ formatResetLineContent(line) }}
                                </ToolTip>
                            </div>
                            <div class="right">
                                <ToolTip :content="formatAmount(line.amountCalcWay)" :line-clamp="1" :dynamic-width="true">
                                    {{ formatAmount(line.amountCalcWay) }}
                                </ToolTip>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="handleConfirmReset">重置</a>
                    <a class="button" @click="resetDialogInfo.display = false">取消</a>
                </div>
            </div>
        </el-dialog>
        <AddAccountSubjectDialog
            :immediatelyRefresh="false"
            ref="accountSubjectAddDialog"
            @save-success="addAccountSubjectSuccess"
            @closed="isNewAsub = false"
        ></AddAccountSubjectDialog>
    </div>
</template>

<script setup lang="ts">
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Table from "@/components/Table/index.vue";
import DialogDescription from "./DialogDescription.vue";
import ToolTip from "@/components/ToolTip/index.vue";
import AddAccountSubjectDialog from "@/components/AddAccountSubjectDialog/index.vue";

import { ref, computed, nextTick, reactive, onMounted, onBeforeUnmount } from "vue";
import { request, type IResponseModel } from "@/util/service";
import Sortable from "sortablejs";
import { pinyin } from "pinyin-pro";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { ElNotify } from "@/util/notify";
import { asubRelationInfo } from "../../AsubRelationSettings1/utils";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { cloneDeep, debounce } from "lodash";
import { checkPermission } from "@/util/permission";
import { ElAlert } from "@/util/confirm";
import {
    checkHasModulePermission,
    routeToModulePage,
    checkCanLink,
    checkHasAsubRelatePermission,
    routeToAsubRelationSettings,
    getTypeName,
    getDetailLabel,
    getDetailAsub,
    asubTypeList,
    type DescriptionInfo,
    checkVoucherTemplateIsBalance,
    getNotBalanceTableData,
    getUnBalanceExample,
    RequestCustomDesc,
    getDisplayDescFn,
} from "../utils";
import { AsubSelectorType, VoucherTemplate, NewVoucherTemplate, BusinessVoucherTemplateLine, VoucherTemplateLine } from "../types";

import type { AsubRelationTypeCode, IAsubRelationInfo, AsubRelationType } from "@/views/Erp/AsubRelationSettings1/utils";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ElScrollbar, ElInput, ElSelect } from "element-plus";
import { type IAsubRelationRequestBack, DetailAsubRealation } from "../../AsubRelationSettings/types";
import type {
    IVtTypeAmountCalcWayPairs,
    IScmVoucherTemplateList,
    IBusinessSelect,
    IAsubSelect,
    ICalSelect,
    ITemplateAsubList,
    ICheckBalanceResult,
    IBusinessType,
} from "../types";
import type { IVoucherDescriptionModel } from "@/components/Voucher/types";

const tableRef = ref<InstanceType<typeof Table>>();
const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>();

const props = defineProps<{
    feature: string;
    scmProductType: number;
    scmAsid: number;
    defaultAsubList: Record<AsubRelationType, string>;
}>();

const emits = defineEmits(["cancelTemplate", "save-success"]);

const voucherGroupList = computed(() => useVoucherGroupStore().voucherGroupList);
const defaultVgid = computed(() => useVoucherGroupStore().defaultVgId);
const info = reactive({
    vtId: 0,
    vtType: 501,
    billType: 0,
    vtName: "",
    code: "",
    name: "",
    vgId: defaultVgid.value,
    isDefault: false,
    note: "",
});

const noteEditing = ref(false);
const noteRef = ref<InstanceType<typeof ElInput>>();
function handleNoteClick() {
    noteEditing.value = true;
    nextTick().then(() => {
        noteRef.value?.focus();
    });
}
function handleNoteBlur() {
    noteEditing.value = false;
}
function handleNoteInput() {
    if (info.note.length > 256) {
        ElNotify({ type: "warning", message: "亲，备注最多256个字" });
        info.note = info.note.slice(0, 256);
    }
}

const editTitle = computed(() => vtTypeList.value.find((item) => item.value == info.vtType)?.label || "");
const isDefaultShow = ref(false);
const descriptionInputRef = ref<InstanceType<typeof ElInput>>();
const descriptionInputContainerRef = ref<HTMLDivElement>();
const vtTypeList = ref(Array<IBusinessSelect>());
const tableData = ref<Array<VoucherTemplateLine>>([]);
const columns = ref<Array<IColumnProps>>([
    { slot: "index" },
    { slot: "description" },
    { slot: "accountSubject" },
    { slot: "direction" },
    { slot: "money" },
]);
const amountCalcList = ref(Array<ICalSelect>());
const selectOptions = ref<Array<IAsubSelect>>([]);
const relateSelectOptions = ref<Array<IAsubSelect>>([]);
const systemSelectOptions = ref<Array<IAsubSelect>>([]);
const selectShowOptions = ref<Array<IAsubSelect>>([]);
function findCurrentAsubLineIndex() {
    return tableData.value.findIndex((item) => item.asubIdSelect);
}

function handleTableAddSub(data: any) {
    tableData.value = data;
}
function handleCopy(index: number) {
    if (index === -1) return;
    const row = cloneDeep(tableData.value[index]);
    tableData.value.splice(index + 1, 0, row);
    tableData.value = tableData.value.map((item, index) => {
        item.index = index;
        return item;
    });
}
function handleAdd(index: number) {
    const scrollWrapRef = tableRef.value?.getTable()?.scrollBarRef?.wrapRef;
    const top = (scrollWrapRef?.scrollTop || 0) + 44; // 44是行高
    const line = new VoucherTemplateLine(index, 1, amountCalcList.value[0].value);
    tableData.value.splice(index, 0, line);
    tableData.value = tableData.value.map((item, index) => {
        item.index = index;
        return item;
    });
    nextTick().then(() => {
        tableRef.value?.setScrollTop(top);
    });
}
function handleSubrtact(index: number) {
    if (tableData.value.length <= 2) {
        ElNotify({ type: "warning", message: "凭证模板不能少于2行哦" });
        setTimeout(() => {
            tableData.value[index].description = "";
            tableData.value[index].displayDescription = "";
            tableData.value[index].asubId = 0;
            tableData.value[index].asubName = "";
        });
        return false;
    }
    tableData.value.splice(index, 1);
    tableData.value = tableData.value.map((item, index) => {
        item.index = index;
        return item;
    });
}

const asubInputRef = ref<HTMLInputElement>();
const asubSelectorDisplay = ref(false);
const asubSelectorRef = ref<HTMLDivElement>();
const asubListRef = ref<HTMLDivElement>();
const asubSelectorDetailDisplay = ref(false);
const asubSelectorDetailRef = ref<HTMLDivElement>();
function hideDescAndAsubSelect() {
    descriptionBlur();
    clearAllDisplayInfo();
}
const directionRefList = ref<Array<InstanceType<typeof ElSelect> | null>>([]);
function getDirectionRef(el: any, index: number) {
    if (el) {
        directionRefList.value[index] = el;
    }
    return directionRefList.value[index];
}
const valueTypeRefList = ref<Array<InstanceType<typeof ElSelect> | null>>([]);
function getValueTypeRef(el: any, index: number) {
    if (el) {
        valueTypeRefList.value[index] = el;
    }
    return valueTypeRefList.value[index];
}
function handleTableScroll() {
    hideDescAndAsubSelect();
    asubInputRef.value?.blur();
    directionRefList.value.forEach((directionSelect) => {
        directionSelect?.blur();
    });
    valueTypeRefList.value.forEach((valueTypeSelect) => {
        valueTypeSelect?.blur();
    });
}
function handleCellClick(row: VoucherTemplateLine, column: any, cell: any, event: Event) {
    if (column.label === "摘要") {
        if (!checkIsClickInput(event, "description-input-container")) return;
        if (!row.description && !row.customDescriptions.length) {
            // 空摘要行
            if (row.index > 0) {
                const lastRow = tableData.value[row.index - 1];
                if (lastRow.customDescriptions.length) {
                    row.customDescriptions = lastRow.customDescriptions.map((item) => ({ ...item }));
                    row.displayDescription = getDisplayDesc(row);
                } else if (lastRow.description) {
                    row.description = lastRow.description;
                    row.displayDescription = row.description;
                }
            }
        }
        if (row.descriptionInput) {
            if (row.customDescriptions.length) {
                descriptionInputRef.value?.blur();
                descriptionBlur();
            } else {
                descriptionInputRef.value?.focus();
                return;
            }
        }
        nextTick().then(() => {
            hideDescAndAsubSelect();
            descFocus(row);
        });
    } else if (column.label === "会计科目") {
        descriptionBlur();
        if (!checkIsClickInput(event, "asub-line-container")) return;
        if (row.asubIdSelect) {
            filterMethodFn({ target: { value: "" } } as any);
            asubInputRef.value?.focus();
            return;
        }
        hideDescAndAsubSelect();
        const relateAsubIndex = relateSelectOptions.value.findIndex((asub) => asub.value === row.asubId);
        if (relateAsubIndex === -1 && row.asubId) {
            activeAsubType.value = AsubSelectorType.System;
        }
        filterMethodFn({ target: { value: "" } } as any);
        focusAsub(row);
    }
}
function descFocus(row: VoucherTemplateLine) {
    row.descriptionInput = true;
    const calcTimer = setTimeout(() => {
        row.descriptionInput = true;
        desListSelectorDisplay.value = true;
        document.addEventListener("click", descriptionBlurEvent);
        window.addEventListener("resize", calcDesListSelectorPosition);
        calcDesListSelectorPosition();
        clearTimeout(calcTimer);
    });
    nextTick().then(() => {
        descriptionInputRef.value?.focus();
        descriptionInputRef.value?.select();
    });
}
function checkIsClickInput(event: Event, className: string) {
    let target = event.target as HTMLElement;
    while (target) {
        if (target.classList.contains(className)) return true;
        if (target.classList.contains("cell")) return false;
        target = target.parentElement as HTMLElement;
    }
    return false;
}
function getClickCellDom(type: "desc" | "asub") {
    const index = type === "desc" ? 1 : 2;
    const tableBody = tableRef.value?.getTable()?.$refs.tableBody as HTMLElement;
    if (!tableBody) return;
    const rows = tableBody.querySelectorAll(".el-table__row");
    if (!rows || !rows.length) return;
    const activeIndex = tableData.value.findIndex((item) => (type === "desc" ? item.descriptionInput : item.asubIdSelect));
    if (activeIndex === -1) return;
    const activeRow = rows[activeIndex] as HTMLElement;
    const cells = activeRow.querySelectorAll(".el-table__cell");
    if (!cells || !cells.length) return;
    return cells[index];
}
function clacAsubSelectorPosition() {
    const cell = getClickCellDom("asub");
    if (!cell) return;
    const { width } = asubInputRef.value?.getBoundingClientRect() || { width: 0 };
    const { height, left, top } = cell.getBoundingClientRect();
    const style = {
        left: left + 8 + "px",
        top: top + height - 5 + "px",
        width: width + "px",
    };
    if (asubSelectorRef.value) {
        Object.assign(asubSelectorRef.value.style, style);
        asubSelectorDisplay.value = true;
    }
}
function asubResize() {
    const timer = setTimeout(() => {
        clacAsubSelectorPosition();
        clearTimeout(timer);
    });
}
const asubDisplayText = ref("");
let timer: any;
let hoverAsub: IAsubSelect | null = null;
const activeAsubType = ref<AsubSelectorType>(1);
const currentSelectAsubIndex = ref(-1);
function handleActiveAsubType(type: AsubSelectorType) {
    if (activeAsubType.value === type) return;
    currentSelectAsubIndex.value = -1;
    activeAsubType.value = type;
    scrollbarRef.value?.setScrollTop(0);
    handleMouseLeave();
    filterMethodFn({ target: { value: "" } } as any);
    nextTick().then(() => {
        asubInputRef.value?.focus();
    });
}
function getAsubDisplayList() {
    return activeAsubType.value === AsubSelectorType.System ? systemSelectOptions.value : relateSelectOptions.value;
}
function clearLastHover() {
    if (hoverAsub) {
        hoverAsub.active = false;
        hoverAsub = null;
    }
}
function handleMouseEnter(event: MouseEvent, item: IAsubSelect) {
    clearTimeout(timer);
    if (activeAsubType.value === AsubSelectorType.System) {
        checkShowPopover(event, item);
        return;
    }
    timer = setTimeout(() => {
        const { top = 0 } = (event.target as HTMLElement).getBoundingClientRect();
        clearLastHover();
        item.active = true;
        hoverAsub = item;
        handleShowAsbuRelateDetail(top, item);
    }, 800);
}
const asubOverflowToolTip = ref<HTMLDivElement>();
const tipAsubName = ref("");
const asubOverflowToolTipShow = ref(false);
function checkShowPopover(event: MouseEvent, item: IAsubSelect) {
    const target = event.target as HTMLElement;
    if (!target || !target.classList.contains("display-item")) {
        asubOverflowToolTipShow.value = false;
        return;
    }
    if (target.offsetWidth === target.scrollWidth) {
        asubOverflowToolTipShow.value = false;
        return;
    }
    tipAsubName.value = item.displayLabel;
    asubOverflowToolTipShow.value = true;
    const { top } = target.getBoundingClientRect();
    const { top: ft } = asubListRef.value?.getBoundingClientRect() || { top: 0 };
    const calcTop = top - ft;
    if (!asubOverflowToolTip.value) return;
    asubOverflowToolTip.value.style.top = calcTop + "px";
}
function handleAllAsubPopover() {
    asubOverflowToolTipShow.value = false;
    handleMouseLeave();
}
function handleOptionMouseLeave(event: MouseEvent) {
    asubOverflowToolTipShow.value = false;
    const toElement = (event as any).toElement as HTMLElement;
    // 滚动条
    if (asubListRef.value?.contains(toElement) && !toElement.classList.contains("display-item")) return;
    // 已经移动到了详情
    if (asubSelectorDetailRef.value?.contains(toElement)) return;
    handleMouseLeave();
}
function handleMouseLeave() {
    clearTimeout(timer);
    const leaveTimer = setTimeout(() => {
        clearTimeout(leaveTimer);
        !asubSelectorDetailDisplay.value && clearLastHover();
    });
    asubSelectorDetailDisplay.value = false;
}
function otherClick(event: MouseEvent) {
    if (isSelect) {
        isSelect = false;
        return;
    }
    if (isNewAsub) return;
    if (asubInputRef.value?.contains(event.target as HTMLElement)) return;
    if (asubSelectorRef.value?.contains(event.target as HTMLElement)) return;
    if (asubSelectorDetailRef.value?.contains(event.target as HTMLElement)) return;
    const index = tableData.value.findIndex((i) => i.asubIdSelect);
    if (index === -1) return;
    const item = tableData.value[index];
    item && (item.asubIdSelect = false);
    clearAllDisplayInfo();
    tryAppendRow(index);
}
function clearAllDisplayInfo() {
    window.removeEventListener("click", otherClick);
    window.removeEventListener("resize", asubResize);
    asubSelectorDisplay.value = false;
    activeAsubType.value = AsubSelectorType.Relate;
    tableData.value.forEach((item) => {
        item.asubIdSelect = false;
    });
    currentSelectAsubIndex.value = -1;
    clearLastHover();
    clearTimeout(timer);
}
function selectAsub(item: IAsubSelect) {
    asubSelectorDetailDisplay.value = false;
    const selectIndex = tableData.value.findIndex((i) => i.asubIdSelect);
    if (selectIndex === -1) return;
    asubDisplayText.value = item.label;
    const assignParams = { asubId: item.value, asubName: item.label, asubIdSelect: false };
    Object.assign(tableData.value[selectIndex], assignParams);
    clearAllDisplayInfo();
    tryAppendRow(selectIndex);
}
let isDescEnter = false;
function trySelectAsub() {
    if (isDescEnter) {
        isDescEnter = false;
        return;
    }
    let selectItem: IAsubSelect | null = null;
    if (currentSelectAsubIndex.value !== -1) {
        selectItem = selectShowOptions.value[currentSelectAsubIndex.value];
    }
    selectItem && selectAsub(selectItem);
    asubSelectorDetailDisplay.value = false;
    clearAllDisplayInfo();
}
function handleAsubKeyDown(event: any) {
    if (event.keyCode === 38) {
        if (currentSelectAsubIndex.value === -1) {
            currentSelectAsubIndex.value = selectShowOptions.value.length - 1;
        } else {
            const nextIndex = currentSelectAsubIndex.value - 1;
            currentSelectAsubIndex.value = nextIndex === -1 ? selectShowOptions.value.length - 1 : nextIndex;
        }
        autoScroll();
    }
    if (event.keyCode === 40) {
        if (currentSelectAsubIndex.value === -1) {
            currentSelectAsubIndex.value = 0;
        } else {
            const nextIndex = currentSelectAsubIndex.value + 1;
            currentSelectAsubIndex.value = nextIndex === selectShowOptions.value.length ? 0 : nextIndex;
        }
        autoScroll();
    }
}
function autoScroll() {
    const content = asubListRef.value?.querySelector(".el-scrollbar__wrap") as any;
    if (!content) return;
    const scrollTop = content.scrollTop;
    const contentHeight = content.clientHeight;
    const offsetTop = content.querySelectorAll("div")[currentSelectAsubIndex.value]?.offsetTop || 0;
    const itemHeight = (content.querySelectorAll("div")[currentSelectAsubIndex.value]?.clientHeight || 0) + 28;
    if (scrollTop > offsetTop) {
        content.scrollTop = offsetTop;
    } else {
        if (offsetTop + itemHeight > scrollTop + contentHeight) {
            if (itemHeight > contentHeight) {
                content.scrollTop = offsetTop;
            } else {
                content.scrollTop = offsetTop - contentHeight + itemHeight;
            }
        }
    }
}
function filterMethodFn(event: Event) {
    currentSelectAsubIndex.value = -1;
    scrollbarRef.value?.setScrollTop(0);
    const list = getAsubDisplayList();
    const query = (event.target as HTMLInputElement).value || "";
    if (query.trim() === "") {
        selectShowOptions.value = list;
        return;
    }
    selectShowOptions.value = list.filter((item) => {
        return item.displayLabel.includes(query) || item.pinyinArr?.join("").includes(query);
    });
}
const filterMethod = debounce(filterMethodFn, 500);
function handleDescKeyDowm(event: KeyboardEvent, row: VoucherTemplateLine) {
    if (event.key === "Enter") {
        tryFocusAsub(row);
        return;
    }
    if (row.customDescriptions.length) {
        if (!["Backspace", "Delete"].includes(event.key)) {
            event.preventDefault();
            event.stopPropagation();
        }
    }
}
function focusAsub(row: VoucherTemplateLine) {
    // 历史数据对停用科目点击后置空，类似新增编辑凭证
    if (row.asubId) {
        const relateAsubIndex = relateSelectOptions.value.findIndex((asub) => asub.value === row.asubId);
        if (relateAsubIndex === -1) {
            const systemAsubIndex = systemSelectOptions.value.findIndex((asub) => asub.value === row.asubId);
            if (systemAsubIndex === -1) {
                row.asubId = 0;
                row.asubName = "";
                asubDisplayText.value = "";
            }
        }
    }
    row.asubIdSelect = true;
    const calcTimer = setTimeout(() => {
        window.addEventListener("click", otherClick);
        window.addEventListener("resize", asubResize);
        clacAsubSelectorPosition();
        setTimeout(() => {
            clacAsubSelectorPosition();
        });
        clearTimeout(calcTimer);
    });
    asubDisplayText.value = row.asubName;
    scrollbarRef.value?.setScrollTop(0);
    nextTick().then(() => {
        asubInputRef.value?.focus();
        asubInputRef.value?.select();
    });
}
function tryFocusAsub(row: VoucherTemplateLine) {
    row.descriptionInput = false;
    descriptionBlur();
    isDescEnter = true;
    const timer = setTimeout(() => {
        focusAsub(row);
        clearTimeout(timer);
    });
}

const balanceColumns: Array<IColumnProps> = [
    {
        prop: "description",
        label: "摘要",
        minWidth: "150px",
        formatter: (row: VoucherTemplateLine) => getDisplayDesc(row),
    },
    { prop: "asubName", label: "会计科目", minWidth: "120px" },
    { prop: "direction", label: "方向", minWidth: "100px", formatter: (row: VoucherTemplateLine) => (row.direction === 1 ? "借" : "贷") },
    {
        prop: "valueType",
        label: "金额",
        minWidth: "100px",
        formatter: (row: VoucherTemplateLine) => {
            return amountCalcList.value.find((item) => item.value === row.valueType)?.text || "";
        },
    },
];
const balanceExampleColumns: Array<IColumnProps> = [
    { prop: "description", label: "摘要", minWidth: "150px" },
    { prop: "asubName", label: "会计科目", minWidth: "120px" },
    { prop: "debit", label: "借方金额", minWidth: "100px", formatter: (_r: any, _c: any, v: any) => v || "" },
    { prop: "credit", label: "贷方金额", minWidth: "100px", formatter: (_r: any, _c: any, v: any) => v || "" },
];
const notBalanceTableInfo = reactive<{
    display: boolean;
    columns: Array<IColumnProps>;
    tableData: Array<ICheckBalanceResult>;
    lines: Array<BusinessVoucherTemplateLine>;
    example: string;
}>({
    display: false,
    columns: balanceExampleColumns,
    tableData: [],
    lines: [],
    example: "",
});
function resetUnBalanceTableInfo() {
    const assignParams = { display: false, tableData: [], lines: [], example: "" };
    Object.assign(notBalanceTableInfo, assignParams);
}
function showNotBalanceDialog(vtType: number, lines: Array<BusinessVoucherTemplateLine>) {
    const { billName, result } = getUnBalanceExample(vtType, lines);
    const businessName = getResetTemplateName().split(" ")[1];
    let name = billName;
    if (billName.includes("(") && businessName) {
        name = billName.replace(/\(.*?\)/, `(${businessName})`);
    }
    const assignParams = {
        display: true,
        example: result.length === 0 ? "" : name + "中" + result.join("，"),
        lines,
        tableData: getNotBalanceTableData(vtType, lines, headerList.value, bodyList.value, footerList.value),
    };
    Object.assign(notBalanceTableInfo, assignParams);
}
function summaryMethod(params: { columns: Array<any>; data: Array<any> }) {
    const { columns, data } = params;
    const sums: Array<string | number> = [];
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = "合计";
            return;
        }
        if (index === 1) {
            sums[index] = "";
            return;
        }
        sums[index] = data.reduce((prev, curr) => prev + curr[column.property], 0) || "";
    });
    return sums;
}
const continueSave = debounce(() => {
    notBalanceTableInfo.display = false;
    handleSubmit(notBalanceTableInfo.lines);
}, 500);
async function sumbitTemplate() {
    if (!info.vtType || info.vtType == 0) {
        ElNotify({ type: "warning", message: "请选择模板类别" });
        return false;
    }
    if (!info.code) {
        ElNotify({ type: "warning", message: "模板编码不能为空" });
        return false;
    }
    if (info.code.length > 40) {
        ElNotify({ type: "warning", message: "模板编码最多40位" });
        return false;
    }
    if (!/^[0-9a-zA-Z]{1,40}$/.test(info.code)) {
        ElNotify({ type: "warning", message: "编码只能输入数字字母组合" });
        return false;
    }
    if (!info.vtName) {
        ElNotify({ type: "warning", message: "模板名称不能为空" });
        return false;
    }
    if (info.vtName.length > 40) {
        ElNotify({ type: "warning", message: "模板名称最多40位" });
        return false;
    }
    if (!info.vgId) {
        ElNotify({ type: "warning", message: "请选择凭证字" });
        return false;
    }
    const lines: Array<BusinessVoucherTemplateLine> = [];
    let valueTypePairs: any = {};
    let totalJie = 0;
    let totalDia = 0;
    for (let i = 0; i < tableData.value.length; i++) {
        let tr = tableData.value[i];
        const zhaiyao = tr.description.replace(/\n/g, "").replace(/\\/g, "\\\\").replace(/\t/g, "").trim();
        const isCustomDesc = tr.customDescriptions.length > 0;
        //摘要和科目为空的行跳过
        let kemuText = tr.asubId;
        // 541 542 543 日记账收入/支出 内部转账  原逻辑是不需要摘要的  新逻辑新建账套会自动配置摘要
        if (!kemuText && zhaiyao === "" && !isCustomDesc) continue;
        if (zhaiyao.indexOf("'") > -1 || zhaiyao.indexOf("\\") > -1 || zhaiyao.indexOf('"') > -1 || zhaiyao.indexOf("\\n") > -1) {
            if (!isCustomDesc) {
                ElNotify({ type: "warning", message: "亲，第" + (i + 1) + "行,摘要不能包含特殊字符！" });
                return;
            }
        }
        if (zhaiyao.length === 0 && !isCustomDesc) {
            ElNotify({ type: "warning", message: "亲，第" + (i + 1) + "行，摘要不允许为空！" });
            return;
        }
        if (zhaiyao.length > 256 && !isCustomDesc) {
            //凭证摘要256
            ElNotify({ type: "warning", message: "亲，第" + (i + 1) + "行，摘要不能超过256个字哦！" });
            return;
        }
        if (!kemuText) {
            ElNotify({ type: "warning", message: "亲，第" + (i + 1) + "行，请选择科目！" });
            return;
        }
        let valueType = tr.valueType;
        if (valueTypePairs[valueType] !== undefined) {
            let count = valueTypePairs[valueType];
            if (count == 2) {
                ElNotify({ type: "warning", message: "亲，相同的取值规则行只能存在两行！" });
                return;
            }
            valueTypePairs[valueType] = count + 1;
        } else {
            valueTypePairs[valueType] = 1;
        }
        totalJie += parseFloat(tr.direction == 1 ? "1" : "0");
        totalDia += parseFloat(tr.direction == 2 ? "1" : "0");

        lines.push(new BusinessVoucherTemplateLine(tr));
    }
    if (lines.length === 0) {
        ElNotify({ type: "warning", message: "亲，请录入您的凭证模板行！" });
        return;
    }
    if (totalDia === 0 || totalJie === 0) {
        ElNotify({ type: "warning", message: "亲，凭证模板必须有借有贷！" });
        return;
    }
    let vtType = info.vtType;
    if (checkShowBusinessType.value) {
        vtType = info.billType;
        if (info.vtType === 550 || info.vtType === 551) {
            vtType = info.vtType;
        }
    }
    // 优先业务类型 再单据类型
    const [amount] = checkVoucherTemplateIsBalance(vtType, lines);
    if (amount !== 0) {
        showNotBalanceDialog(vtType, lines);
        return;
    }
    await handleSubmit(lines);
}
function checkHasLines() {
    return tableData.value.some((item) => item.description || item.customDescriptions.length || item.asubId);
}
async function handleSubmit(lines: Array<BusinessVoucherTemplateLine>) {
    const voucherTemplateModel = {
        vtId: info.vtId,
        vtType: info.vtType,
        businessType: info.billType,
        vtCode: info.code,
        vtName: info.vtName,
        vgId: info.vgId,
        vtDefault: info.isDefault,
        voucherLines: lines,
        note: info.note,
    };
    const method = props.feature === "add" ? "post" : "put";
    await request({ url: "/api/VoucherTemplate/Business", method, data: voucherTemplateModel }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000 || !res.data) {
            ElNotify({ type: "warning", message: res.msg || "保存失败" });
            return;
        }
        ElNotify({ type: "success", message: "保存成功" });
        emits("save-success");
        cancelTemplate();
    });
}
function cancelTemplate() {
    info.code = "";
    info.vtName = "";
    info.note = "";
    info.isDefault = false;
    emits("cancelTemplate");
}
interface TemplateLine {
    description: string;
    asubId: number;
    direction: number;
    amountCalcWay: number;
    customDescriptions: CustomDescription[];
}

interface CustomDescription {
    isCustom: boolean;
    descriptionType: number;
    description: string;
}
const resetDialogInfo = reactive({
    display: false,
    voucherLines: [] as Array<TemplateLine>,
});
function getResetTemplateName() {
    return formatVtType() + (!checkShowBusinessType.value ? "" : " " + formatBusinessType());
}
function formatAsubName(asubId: number) {
    let asub = systemSelectOptions.value.find((item) => item.value === asubId);
    if (asub) return asub.label || "";
    asub = relateSelectOptions.value.find((item) => item.value === asubId);
    return asub ? asub.label || "" : "";
}
function formatResetLineContent(line: TemplateLine) {
    const direction = line.direction === 2 ? "贷" : "借";
    return `${direction}：${formatAsubName(line.asubId)}`;
}
function formatAmount(amountCalcWay: number) {
    return amountCalcList.value.find((item) => item.value === amountCalcWay)?.text || "";
}
function formatVtType() {
    return vtTypeList.value.find((item) => item.value === info.vtType)?.label || "";
}
function formatBusinessType() {
    return businessTypes.value.find((item) => item.vtType === info.billType)?.vtTypeName || "";
}
function handleResetTemplate() {
    request({ url: "/api/VoucherTemplate/Reset?vtId=" + info.vtId }).then((res: IResponseModel<Array<TemplateLine>>) => {
        if (res.state !== 1000 || !res.data) {
            ElNotify({ type: "warning", message: res.msg || "重置失败" });
            return;
        }
        resetDialogInfo.voucherLines = res.data;
        resetDialogInfo.display = true;
    });
}
function handleConfirmReset() {
    tableData.value.length = 0;
    resetDialogInfo.voucherLines.forEach((line, index) => {
        const row = new VoucherTemplateLine(index, line.direction, line.amountCalcWay);
        row.description = line.description;
        row.displayDescription = getDisplayDesc(line);
        row.customDescriptions = line.customDescriptions;
        row.asubId = line.asubId;
        let asub = relateSelectOptions.value.find((item) => item.value === line.asubId);
        if (!asub) {
            asub = systemSelectOptions.value.find((item) => item.value === line.asubId);
        }
        if (asub) {
            row.asubName = asub.label;
        }
        tableData.value.push(row);
    });
    resetDialogInfo.display = false;
    resetDialogInfo.voucherLines.length = 0;
    ElNotify({ type: "success", message: "重置成功" });
}
function tryAppendRow(blurIndex: number) {
    if (blurIndex === tableData.value.length - 1 && blurIndex !== -1) {
        const newItem = new VoucherTemplateLine(tableData.value.length, 1, amountCalcList.value[0].value);
        tableData.value.push(newItem);
    }
}
interface ITemplateDescBack {
    templateType: number;
    inout: number;
    headerFields: Array<{ key: number; value: string }>;
    bodyFields: Array<{ key: number; value: string }>;
    bodyFields2: Array<{ key: number; value: string }>;
}
const headerList = ref<Array<{ id: number; label: string }>>([]);
const bodyList = ref<Array<{ id: number; label: string }>>([]);
const footerList = ref<Array<{ id: number; label: string }>>([]);
async function handleGetCustomDescInfo() {
    // 优先使用业务类型进行查找 业务类型为 0 才考虑 vtType
    let vt = info.billType || info.vtType;
    if (info.vtType === 550 || info.vtType === 551) {
        // 其他出入库例外
        vt = info.vtType;
    }
    if (!vt) return;
    headerList.value.length = 0;
    bodyList.value.length = 0;
    dialogDescriptionRef.value?.reset();
    const params = { vtType: vt, scmProductType: props.scmProductType, scmAsid: props.scmAsid };
    await request({ url: "/api/VoucherForErp/TemplateDescriptionField?" + getUrlSearchParams(params) }).then(
        (res: IResponseModel<ITemplateDescBack>) => {
            if (res.state !== 1000) return;
            headerList.value = res.data.headerFields.map((item) => ({ id: item.key, label: item.value }));
            bodyList.value = res.data.bodyFields.map((item) => ({ id: item.key, label: item.value }));
            footerList.value = res.data.bodyFields2.map((item) => ({ id: item.key, label: item.value }));
            const title = vtTypeList.value.find((item) => item.value === info.vtType)?.label || "";
            dialogDescriptionRef.value?.initSelectOptions(title, headerList.value, bodyList.value, footerList.value);
        }
    );
}
function getDisplayDesc(row: { description: string; customDescriptions: Array<RequestCustomDesc> }) {
    return getDisplayDescFn({ row, headerList: headerList.value, bodyList: bodyList.value, footerList: footerList.value });
}
const businessTypes = ref<Array<IBusinessType>>([]);
const checkShowBusinessType = computed(() => businessTypes.value.length > 0);
const childType = computed(() => {
    if (info.vtType === 544) return "费用";
    if (info.vtType === 514) return "核销";
    return "业务";
});
let lastVtType = 0;
function handleVtTypeChange() {
    if (checkHasLines()) {
        ElAlert({
            message: "亲，切换单据类型后会清空当前模板数据，是否继续切换？",
            options: {
                confirmButtonText: "继续",
                cancelButtonText: "取消",
            },
        }).then((r) => {
            if (r) {
                rebindVtInfo();
            } else {
                info.vtType = lastVtType;
            }
        });
        return;
    }
    rebindVtInfo();
}
function rebindVtInfo() {
    resetVtInfo();
    info.isDefault = false;
    lastVtType = info.vtType;
    resetTableData();
}
function handleBusinessChange() {
    resetVtInfo(false);
    info.isDefault = false;
    // 切换业务类型不清空下面分录
    resetTableData(false);
}
function resetTableData(clearTable = true) {
    if (props.feature !== "add") return;
    handleGetCustomDescInfo();
    handleGetNextVtCode();
    if (!clearTable) return;
    tableData.value = [
        new VoucherTemplateLine(0, 1, amountCalcList.value[0].value),
        new VoucherTemplateLine(1, 2, amountCalcList.value[0].value),
    ];
}
function resetVtInfo(examineBillType = true) {
    const vtInfo = vtTypeList.value.find((item) => item.value === info.vtType);
    // 重置业务类型信息
    businessTypes.value = vtInfo?.businessTypes || [];
    // 重置取值规则
    const amountKey = info.vtType === 550 || info.vtType === 551 ? info.vtType : info.billType || info.vtType;
    amountCalcList.value = amountCalcWayPair.value[amountKey];
    // 赋值业务类型id
    if (!examineBillType) return;
    info.billType = businessTypes.value.length === 0 ? 0 : businessTypes.value[0].vtType;
    // 修正业务类型
    if (!vtInfo) return;
    if (vtInfo.label === "其他支出") {
        const item = businessTypes.value.find((item) => item.vtTypeName === "其他费用");
        item && (info.billType = item.vtType);
    } else if (vtInfo.label === "其他入库" || vtInfo.label === "其他出库") {
        const item = businessTypes.value.find((item) => item.vtTypeName === vtInfo.label);
        item && (info.billType = item.vtType);
    }
}

async function getAsubList() {
    await request({
        url: `/api/VoucherForErp/GetTemplateAsubList?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
        method: "post",
    }).then((res: IResponseModel<Array<ITemplateAsubList>>) => {
        if (res.state !== 1000) return;
        const vtInfo = vtTypeList.value.find((item) => item.value === info.vtType);
        relateSelectOptions.value.length = 0;
        res.data.forEach((item) => {
            if (item.code === 8010) return; // 不展示职员选项
            if(vtInfo && vtInfo.label !== "其他支出" && item.code === 7010) return; // 其他支出不展示收款人科目
            const newItem = {
                label: item.name,
                displayLabel: getTypeName(item.code, item.name),
                value: item.code,
                pinyinArr: pinyin(item.name, { pattern: "pinyin", toneType: "none", type: "array" }),
                type: AsubSelectorType.Relate,
                active: false,
            };
            relateSelectOptions.value.push(newItem);
            selectOptions.value.push(newItem);
        });
        handleInitSystemAsub();
        selectShowOptions.value = activeAsubType.value === AsubSelectorType.Relate ? relateSelectOptions.value : systemSelectOptions.value;
    });
}
function handleInitSystemAsub() {
    systemSelectOptions.value.length = 0;
    const accountSubject = useAccountSubjectStore().accountSubjectList;
    accountSubject.forEach((item) => {
        const newItem = {
            label: item.asubCode + " " + item.asubAAName,
            displayLabel: item.asubCode + " " + item.asubAAName,
            value: item.asubId,
            pinyinArr: pinyin(item.asubCode + " " + item.asubAAName, { pattern: "pinyin", toneType: "none", type: "array" }),
            type: AsubSelectorType.System,
            active: false,
        };
        if (item.isLeafNode && item.status !== 1) {
            systemSelectOptions.value.push(newItem);
            selectOptions.value.push(newItem);
        }
    });
}
async function getVtType() {
    await request({ url: "/api/voucherTemplate/GetScmVoucherTemplateList?limitAcc=", method: "post" }).then(
        (res: IResponseModel<Array<IScmVoucherTemplateList>>) => {
            if (res.state !== 1000) return;
            vtTypeList.value = res.data.reduce((prev: IBusinessSelect[], item) => {
                prev.push({ label: item.vtTypeName, value: item.vtType, businessTypes: item.businessTypes });
                return prev;
            }, []);
        }
    );
}
function handleGetNextVtCode() {
    const params = { vtType: info.vtType, businessTypeId: info.billType };
    request({ url: "/api/VoucherTemplate/GetNextTemplateCode?" + getUrlSearchParams(params), method: "post" }).then(
        (res: IResponseModel<string>) => {
            if (res.state !== 1000) return;
            info.code = res.data;
            // 新增第一个模板是001  勾选上默认
            if (res.data === "001") {
                info.isDefault = true;
            }
        }
    );
}
async function initEditInfo(data: VoucherTemplate) {
    info.vtId = data.vtId;
    info.code = data.code;
    info.isDefault = data.isDefault;
    isDefaultShow.value = info.isDefault;
    info.vgId = data.vgId;
    info.vtType = data.vtType;
    info.vtName = data.vtName;
    info.note = data.note;
    info.billType = data.businessType;
    resetVtInfo(false);
    const isNewTemplate = data instanceof NewVoucherTemplate;
    if (isNewTemplate) {
        lastVtType = info.vtType;
        handleGetNextVtCode();
    }
    await getAsubList();
    await handleGetCustomDescInfo(); // 摘要依赖配置模板的加载完成
    tableData.value = data.voucherLines.reduce((prev: Array<VoucherTemplateLine>, item: any, index: number) => {
        item.direction = index === 0 && isNewTemplate ? 1 : item.debit ? 1 : 2;
        item.asubId = item.asubId || "";
        item.index = index;
        item.descriptionInput = false;
        item.asubIdSelect = false;
        item.customDescriptions = item.customDescriptions || [];
        item.displayDescription = getDisplayDesc(item);
        if (isNewTemplate) {
            item.valueType = amountCalcList.value[0].value;
        }
        prev.push(item);
        return prev;
    }, []);
}
const amountCalcWayPair = ref<IVtTypeAmountCalcWayPairs>({});
async function getVtTypeAmountCalcWayPairs() {
    if (!props.scmAsid) return;
    await request({
        url: `/api/VoucherForErp/GetVtTypeAmountCalcWayPairs?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
        method: "post",
    }).then((res: IResponseModel<IVtTypeAmountCalcWayPairs>) => {
        if (res.state !== 1000) return;
        amountCalcWayPair.value = res.data;
        amountCalcList.value = res.data[info.vtType];
    });
}
function handleGetDescHistory() {
    request({ url: "/api/VoucherDescription/List" }).then((res: IResponseModel<Array<IVoucherDescriptionModel>>) => {
        if (res.state !== 1000) return;
        descHistoryList.value = res.data.map((item) => item.description);
    });
}
defineExpose({ getVtTypeAmountCalcWayPairs, getVtType, getAsubList, initEditInfo, handleGetDescHistory, refreshAsub });

let sortable: Sortable | null = null;
function bindSortable() {
    const tableBody = (tableRef.value?.getTable()?.$refs.tableBody as HTMLElement)?.querySelector("tbody") as HTMLElement;
    if (!tableBody) return;
    sortable = Sortable.create(tableBody, {
        animation: 150,
        ghostClass: "ghostClass",
        chosenClass: "chosenClass",
        dragClass: "dragClass",
        handle: ".el-table__row",
        forceFallback: true,
        delay: 50,
        filter: function () {
            return tableData.value.some((i) => i.asubIdSelect || i.descriptionInput);
        },

        onStart: () => {
            document.onselectstart = null;
            document.ondragstart = null;
            // 虽然克隆出来了一个行元素  但是单元格的宽度是不会跟着变化的 所以需要手动设置
            const table = tableRef.value?.getTable()?.$el as HTMLElement;
            if (!table) return;
            const firstRow = table.querySelector(".el-table__row") as HTMLElement;
            const dragDom = table.querySelector(".dragClass") as HTMLElement;
            if (!firstRow || !dragDom) return;
            const cells = firstRow.querySelectorAll(".el-table__cell") as NodeListOf<HTMLElement>;
            cells.forEach((cell, index) => {
                const { width } = cell.getBoundingClientRect();
                const style = {
                    width: width + "px",
                };
                Object.assign((dragDom.querySelectorAll(".el-table__cell")[index] as HTMLElement).style, style);
            });
        },

        onEnd: (event) => {
            document.onselectstart = null;
            document.ondragstart = null;
            const { oldIndex, newIndex } = event;
            if (oldIndex === undefined || newIndex === undefined || newIndex === oldIndex) return;
            const back = [...tableData.value];
            const item = back.splice(oldIndex, 1)[0];
            back.splice(newIndex, 0, item);
            tableData.value = [];
            nextTick().then(() => {
                tableData.value = back.map((item, index) => {
                    item.index = index;
                    return item;
                });
            });
        },
    });
}
function reBindSortable() {
    if (!sortable) return;
    sortable.destroy();
    bindSortable();
}
function unBindSortable() {
    if (!sortable) return;
    sortable.options.disabled = true;
}
let isSelect = false;
function handleInputMouseDown() {
    isSelect = true;
}
onMounted(() => {
    bindSortable();
});
onBeforeUnmount(() => {
    unBindSortable();
});

function handleToAsubRelation() {
    if (!checkPermission(["asubrelationsettings-canview"])) return;
    globalWindowOpenPage("/Erp/AsubRelationSettings1", "科目关联设置");
}
const accountSubjectAddDialog = ref<InstanceType<typeof AddAccountSubjectDialog>>();
let isNewAsub = false;
function handleNewAsub() {
    if (!checkPermission(["accountsubject-canedit"])) return;
    isNewAsub = true;
    accountSubjectAddDialog.value?.showAADialog();
}
function addAccountSubjectSuccess(asubId: number) {
    refreshAsub().then(() => {
        const item = systemSelectOptions.value.find((i) => i.value === asubId);
        item && selectAsub(item);
    });
}
async function refreshAsub() {
    await useAccountSubjectStore()
        .getAccountSubject()
        .then(() => {
            handleInitSystemAsub();
        });
}
function asubSelectorDetailMouseEnter() {
    clearTimeout(timer);
    asubSelectorDetailDisplay.value = true;
}
const detailDisplayInfo = reactive({
    label: "",
    asubDisplay: "",
});
async function handleShowAsbuRelateDetail(selectTop: number, item: IAsubSelect) {
    const { width = 0, top = 0, left = 0, height = 0 } = asubSelectorRef.value?.getBoundingClientRect() as DOMRect;
    const asubRelationDetailHeight = 424;
    const translateY = (asubRelationDetailHeight - height) / 2;
    let diff = 0;
    const allowWidth = 4; // 箭头宽度
    const styleObj = {
        left: left + width - allowWidth + "px",
        top: top - translateY + "px",
    };
    if (parseFloat(styleObj.top) + asubRelationDetailHeight > window.innerHeight) {
        styleObj.top = window.innerHeight - asubRelationDetailHeight + "px";
        diff = top - translateY - (window.innerHeight - asubRelationDetailHeight);
    }
    if (asubSelectorDetailRef.value) {
        const asubRelationDetail = asubRelationInfo.find((i) => i.asubRelationType.some((j) => j === item.value));
        if (!asubRelationDetail) return;
        detailDisplayInfo.label = getDetailLabel(item.value, asubRelationDetail);
        detailDisplayInfo.asubDisplay = getDetailAsub(item.value, asubRelationDetail, props.defaultAsubList);
        getDitailAsubTableData(item.value, asubRelationDetail);
        Object.assign(asubSelectorDetailRef.value.style, styleObj);
        asubSelectorDetailDisplay.value = true;
        nextTick().then(() => {
            const allow = asubSelectorDetailRef.value?.querySelector(".allow.custom") as HTMLElement;
            if (allow) {
                const halfLineHeight = 6; // 一半行高
                const asubSelectorTop = asubSelectorRef.value?.getBoundingClientRect().top || 0;
                const allowTop = selectTop - asubSelectorTop + translateY + halfLineHeight + diff + "px";
                allow.style.top = allowTop;
            }
        });
    }
}
const asubRelationInfoRequestInfo = asubRelationInfo.map((item) => {
    return {
        type: item.requestType,
        requested: false,
        tableData: [] as Array<DetailAsubRealation>,
    };
});
const loading = ref(false);
function getDitailAsubTableData(type: number, asubRelationDetail: IAsubRelationInfo | undefined) {
    if (!asubRelationDetail) return;
    const requestRecord = asubRelationInfoRequestInfo.find((item) => item.type === asubRelationDetail.requestType);
    loading.value = true;
    setDetailColumnsInfo(type, asubRelationDetail);
    if (requestRecord?.requested) {
        loading.value = false;
        detailTableData.value = requestRecord.tableData.slice();
        return;
    }
    requestRecord && (requestRecord.requested = true);
    const data = {
        type: asubRelationDetail.requestType,
        PageIndex: 1,
        PageSize: 20,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    loading.value = true;
    request({ url: "/api/AsubRelation/PagingList?" + getUrlSearchParams(data) })
        .then((res: IResponseModel<IAsubRelationRequestBack>) => {
            if (res.state !== 1000) {
                detailTableData.value.length = 0;
                return;
            }
            detailTableData.value = res.data.data.slice(0, 5);
            if (res.data.count > 0) {
                const append = new DetailAsubRealation();
                append.name = "查看更多>>";
                detailTableData.value.push(append);
            }
            requestRecord && (requestRecord.tableData = detailTableData.value.slice());
        })
        .finally(() => {
            loading.value = false;
        });
}
const detailColumns = ref<Array<IColumnProps>>([
    { slot: "name" },
    { prop: "asubName1", label: "应收账款科目", minWidth: 200, align: "left", headerAlign: "left" },
]);
const detailTableData = ref<Array<DetailAsubRealation>>([]);
const detailName = ref("");
const activeAsubTypeDetail = ref<AsubRelationTypeCode | "">("");
const activeAsubTypeDetailLabel = computed(() => asubRelationInfo.find((item) => item.typeCode === activeAsubTypeDetail.value)?.type || "");
function setDetailColumnsInfo(type: number, asubRelationDetail: IAsubRelationInfo) {
    activeAsubTypeDetail.value = asubRelationDetail.typeCode;
    const code = type as AsubRelationType;
    const index = asubRelationDetail.asubRelationType.findIndex((i) => i === code);
    const backColumns = detailColumns.value.slice();
    detailColumns.value.length = 0;
    const columnTimer = setTimeout(() => {
        clearTimeout(columnTimer);
        detailName.value = asubRelationDetail.type + "类别";
        const column = backColumns[1];
        column.prop = "asubName" + (index + 1);
        column.label = asubRelationDetail.asubs[index];
        detailColumns.value = backColumns;
    }, 10);
}
function handleRouteToOtherPage(row: DetailAsubRealation) {
    if (!checkCanLink(row)) return;
    routeToAsubRelationSettings(activeAsubTypeDetail.value);
}

const desListSelector = ref<HTMLDivElement>();
const desListSelectorDisplay = ref(false);
const desListSearchInfo = ref("");
const descHistoryList = ref<Array<string>>([]);
const dialogDescriptionRef = ref<InstanceType<typeof DialogDescription>>();
function getDescList() {
    let count = 0;
    const maxCount = 10;
    let list = new Array<string>();
    tableData.value.forEach((v) => {
        if (count >= maxCount || !v.description.trim() || list.filter((i) => i.trim() === v.description.trim()).length > 0) return;
        if (!desListSearchInfo.value.trim() || v.description.trim().indexOf(desListSearchInfo.value.trim()) !== -1) {
            !v.descriptionInput && !v.customDescriptions.length && list.push(v.description.trim());
            count++;
        }
    });
    descHistoryList.value.forEach((d) => {
        if (count >= maxCount || list.filter((i) => i.trim() === d.trim()).length > 0) return;
        if (!desListSearchInfo.value.trim() || d.trim().indexOf(desListSearchInfo.value.trim()) !== -1) {
            list.push(d);
            count++;
        }
    });
    return list;
}
function desListSelect(desc: string) {
    const row = tableData.value.find((i) => i.descriptionInput);
    if (row) {
        row.description = desc;
        row.displayDescription = desc;
        row.customDescriptions.length = 0;
    }
    descriptionBlur();
}
function handleDescInput(val: string, row: VoucherTemplateLine) {
    row.customDescriptions.length = 0;
    desListSearchInfo.value = val;
    row.description = val;
    calcDesListSelectorPosition();
    if (val.length > 256) {
        ElNotify({ type: "warning", message: "亲，摘要不能超过256个字哦！" });
        if (row) {
            row.description = val.slice(0, 256);
            row.displayDescription = val.slice(0, 256);
        }
    }
}
function descriptionBlurEvent(event: Event) {
    if (isSelect) {
        isSelect = false;
        return;
    }
    const target = event.target as HTMLElement;
    if (!target) return;
    if (descriptionInputContainerRef.value?.contains(target)) return;
    if (desListSelector.value?.contains(target)) return;
    descriptionBlur();
}
function descriptionBlur() {
    desListSelectorDisplay.value = false;
    desListSearchInfo.value = "";
    isDescEnter = false;
    const index = tableData.value.findIndex((i) => i.descriptionInput);
    index !== -1 && (tableData.value[index].descriptionInput = false);
    tryAppendRow(index);
    document.removeEventListener("click", descriptionBlurEvent);
    window.removeEventListener("resize", calcDesListSelectorPosition);
}
function calcDesListSelectorPosition() {
    if (!desListSelector.value) return;
    const cell = getClickCellDom("desc");
    if (!cell) return;
    const { width, top, height, left } = cell.getBoundingClientRect();
    const halfPaddingW = 8; // 左右边距
    const halfPaddingH = 5; // 上下边距
    const borderWidth = 1; // 边框宽度
    const style = {
        left: left + halfPaddingW + "px",
        top: top + height - halfPaddingH + "px",
        width: width - halfPaddingW * 2 - borderWidth * 2 - borderWidth * 2 + "px", // 减去单元格以及自己的边框宽度 以及左右边距
    };
    Object.assign(desListSelector.value.style, style);
    desListSelectorDisplay.value = true;
}
function handleDeleteDesc(row: VoucherTemplateLine) {
    row.description = "";
    row.displayDescription = "";
    row.customDescriptions.length = 0;
    descriptionBlur();
    descFocus(row);
}
const currentSelectDescIndex = ref(-1);
function handleShowMoreDesc(row: VoucherTemplateLine) {
    descriptionBlur();
    currentSelectDescIndex.value = row.index;
    desListSelectorDisplay.value = false;
    dialogDescriptionRef.value?.showDialog(row.customDescriptions);
}
function setCustomDesc(descList: Array<DescriptionInfo>) {
    if (currentSelectDescIndex.value === -1) return;
    const row = tableData.value[currentSelectDescIndex.value];
    row.description = "";
    row.customDescriptions = descList.map((desc) => new RequestCustomDesc(desc));
    row.displayDescription = getDisplayDesc(row);
    descriptionBlur();
}
function resetCurrentSelectDescIndexInfo() {
    currentSelectDescIndex.value = -1;
    window.removeEventListener("mousedown", resetCurrentSelectDescIndexInfo);
}
function descDialogClosed() {
    window.addEventListener("mousedown", resetCurrentSelectDescIndexInfo);
}
function findCurrentSelectDescIndex() {
    return tableData.value.findIndex((i) => i.descriptionInput);
}
</script>

<style scoped lang="less">
@import "@/style/Erp/BusinessVoucherTemplate.less";
</style>
