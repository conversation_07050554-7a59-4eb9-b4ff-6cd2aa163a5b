export interface IOptionItem {
    fc_code: any;
    fc_id: number;
    fc_name: string;
}
export interface IAccountItem {
    AC_ID: string;
    AC_NAME: string;
}
export interface ITableItem {
    name: string;
    asub: string;
    ac_name: string;
    asub_name: string;
    fc_name: string;
    initial: string;
    income_sum: string;
    expenditure_sum: string;
    total: string;
}
export interface IPeriodList {
    asid: number
    pid: number
    isActive: boolean
    year: number
    sn: number
    startDate: string
    endDate: string
    status: number
    fastatus: number
}
export interface IPeriodResponseData {
    year: number;
    sn: number;
    key: string;
    isActived: boolean;
}

export interface IIPeriodData {
    year: number;
    sn: number;
    key: string;
    pid: number;
}