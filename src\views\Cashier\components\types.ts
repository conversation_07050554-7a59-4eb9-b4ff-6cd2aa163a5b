export interface ICDAccountItem {
    as_id: string;
    ac_type: string;
    ac_id: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    currency: string;
    asub: string;
    currency_name: string;
    asub_code: string;
    asub_name: string;
    state: string;
    standard: string;
    fc_rate: string;
    label?: string;
}

export interface ICurrencyList {
    asId: number;
    preName: string;
    id: number;
    code: string;
    name: string;
    rate: number;
    isBaseCurrency: boolean;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}

export interface IIETypeItem {
    subkey: string;
    value1: string;
    value2: string;
    value3: string;
}
