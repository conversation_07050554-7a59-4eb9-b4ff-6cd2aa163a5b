export enum overflowElementStyle {
    top = 6,
    bottom = 6,
    left = 4,
    right = 10,
    lineHeight = 16,
}

export function isOverflowed(text: string, width: string, lineClamp = 2, paddingRight = overflowElementStyle.right) {
    const lineHeight = overflowElementStyle.lineHeight;
    const element = document.createElement("div");
    element.style.display = "inline-block";
    element.style.visibility = "hidden";
    element.style.boxSizing = "border-box";
    element.style.paddingLeft = overflowElementStyle.left + "px";
    element.style.paddingRight = paddingRight + "px";
    element.style.paddingTop = overflowElementStyle.top + "px";
    element.style.paddingBottom = overflowElementStyle.bottom + "px";
    element.style.whiteSpace = "noraml";
    element.style.lineHeight = lineHeight + "px";
    document.body.appendChild(element);
    element.innerText = text;
    const isOverflow =
        element.scrollWidth >
        (parseFloat(width) - overflowElementStyle.left - paddingRight) * lineClamp + overflowElementStyle.left + overflowElementStyle.right;
    document.body.removeChild(element);

    return isOverflow;
}
