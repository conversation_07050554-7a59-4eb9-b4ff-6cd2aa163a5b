//第一步基础信息
export class IBaseCompanyInfo {
    companyName: string = "";
    socialCreditCode: string = "";
    legalPersonName: string = "";
    legalPhone: string = "";
    accountType: number = 1;
    rightLegalPersonName: string = "";
    taxProvinceName?: string = undefined;
    taxProvinceCode?: string = undefined;
}
export interface IArea {
    id: string;
    name: string;
}
//邮储法人证件类型
export const PSBCLegalCertTypeList = [
    { value: "10", label: "居民身份证" },
    { value: "11", label: "临时身份证" },
    { value: "20", label: "军人身份证" },
    { value: "21", label: "士兵证" },
    { value: "22", label: "军官证" },
    { value: "23", label: "文职干部证" },
    { value: "24", label: "军官退休证" },
    { value: "25", label: "文职干部退休证" },
    { value: "30", label: "武警身份证" },
    { value: "31", label: "武警士兵证" },
    { value: "32", label: "警官证" },
    { value: "33", label: "武警文职干部证" },
    { value: "34", label: "武警军官退休证" },
    { value: "35", label: "武警文职干部退休证" },
    { value: "40", label: "户口簿" },
    { value: "50", label: "本国护照" },
    { value: "51", label: "外国护照" },
    { value: "60", label: "港澳居民来往内地通行证" },
    { value: "61", label: "台湾居民来往大陆通行证" },
    { value: "62", label: "边民出入境通行证" },
    { value: "63", label: "港澳居民居住证" },
    { value: "64", label: "台湾居民居住证" },
    { value: "70", label: "外国人永久居留证" },
    { value: "99", label: "其他" },
];
//邮储企业证件类型
export const PSBCCertTypeList = [
    { value: "01", label: "营业执照" },
    { value: "02", label: "政府批文" },
    { value: "03", label: "登记证书（法人登记证书）" },
    { value: "04", label: "开户证明" },
    { value: "05", label: "主管部门批文" },
    { value: "06", label: "借款合同" },
    { value: "07", label: "主管部门证明" },
    { value: "08", label: "财政部门证明" },
    { value: "09", label: "法规、规章" },
    { value: "10", label: "政府部门文件" },
    { value: "11", label: "其他证明" },
    { value: "12", label: "机关登记注册证明文件" },
    { value: "13", label: "境外注册证书" },
    { value: "14", label: "三证合一证件号/统一社会信用代码" },
];

//邮储银行预开户信息查询
export interface IPSBCAreaItem {
    areaCode: string;
    areaName: string;
}
export interface IPSBCAreaRequestItem {
    areaCode: number;
    areaName: string;
}
export interface IPSBCBranchInfo {
    code: string;
    name: string;
    address: string;
    provinceName: string;
    provinceCode: number;
    cityName: string;
    cityCode: number;
    countyName: string;
    countyCode: number;
    phone: string;
}
export interface IBankDetail<T> {
    bankType: number;
    openState: number;
    id: string;
    content: T;
}
export interface IPSBCDetail {
    branchName: string;
    branchNo: string;
    applicantMobile: string;
    companyCertType: string;
    companyCertNo: string;
    legalCertType: string;
    legalCertNo: string;
    companyName: string;
    unifiedNumber: string;
    legalName: string;
    legalMobile: string;
    accountType: number;
    appointmentTime: string;
}
export interface IPADetail {
    branchName: string;
    branchNo: string;
    branchTel: string;
    branchAddr: string;
    regProvName: string;
    regProvCode: string;
    regCityName: string;
    regCityCode: string;
    regAddr: string;
    bizProvName: string;
    bizProvCode: string;
    bizCityName: string;
    bizCityCode: string;
    bizAddr: string;
    companyName: string;
    unifiedNumber: string;
    legalName: string;
    legalMobile: string;
    accountType: number;
    appointmentTime: string;
}
export interface ISPDBDetail {
    companyName: string;
    unifiedNumber: string;
    legalName: string;
    legalMobile: string;
    accountType: number;
    appointmentTime: string;
    defaultMobile: string;
    clientName: string;
    acctType: string;
    companyAddr: string;
    unifiedNumberExpData: string;
    regCapital: string;
    legalRprsntIDType: string;
    legalRprsntIDNo: string;
    legalRprsntIDExpData: string;
    depositAcctType: string;
    openBankName: string;
    basicBankAcctNo: string;
    openAcctLcnsId: string;
    branchAddress: string;
    branchProvince: string;
    branchCity: string;
    branchNo: string;
    branchName: string;
    branchTel: string;
}
export const SPDBlegalRprsntIDTypeList = [
    { value: "1", label: "身份证" },
    { value: "2", label: "护照" },
    { value: "3", label: "军官证" },
    { value: "4", label: "武警证" },
    { value: "5", label: "港澳居民来往内地通行证" },
    { value: "6", label: "户口簿" },
    { value: "7", label: "其他" },
    { value: "8", label: "警官证" },
    { value: "9", label: "执行公务证" },
    { value: "A", label: "士兵证" },
    { value: "B", label: "台湾同胞来往内地通行证" },
    { value: "C", label: "临时身份证" },
    { value: "D", label: "外国人居留证" },
];
export const SPDBacctTypeList = [
    {
        label: "借款需要",
        value: "1",
    },
    {
        label: "其他结算需要",
        value: "2",
    },
];
//进度查询状态
export interface IItemProgressInfo {
    id: string;
    openState: number; //[ 0, 1, 2, 3, 4, 5, 6, 7, 8, 9 ]
    bankType: number;
    createTime: string;
    branchName?: string;
    companyName?: string;
    accountType?: string; //选择的账户类型
    legalPersonName?: string; //法人姓名
}
export enum ProgressStatus {
    //预约开户失败
    Fail = -1,
    //暂存 未提交
    Staging = 0,
    //预约开户成功
    Successded = 1,
    //审批中
    UnderApproval = 2,
    //审批通过
    ApprovalSucceeded = 21,
    //审批拒绝
    ApprovalFail = 22,
    //取消
    Canceled = 3,
}

//平安
export class PACompanyInfoModel {
    companyName: string = "";
    unifiedNumber: string = "";
    legalName: string = "";
    legalMobile: string = "";
    accountType: number = 0;
    appointmentTime: string = "";
    branchCityName: string = "";
    branchCityNo: string = "";
    branchName: string = "";
    branchNo: string = "";
    branchTel: string = "";
    branchAddr: string = "";
    regProvName: string = "";
    regProvCode: string = "";
    regCityName: string = "";
    regCityCode: string = "";
    regAddr: string = "";
    bizProvName: string = "";
    bizProvCode: string = "";
    bizCityName: string = "";
    bizCityCode: string = "";
    bizAddr: string = "";
}

//邮储
export class PSBCCompanyInfoModel {
    companyName: string = "";
    unifiedNumber: string = "";
    legalName: string = "";
    legalMobile: string = "";
    accountType: number = 0;
    appointmentTime: string = "";
    openProvince: string = "";
    openCity: string = "";
    openCounty: string = "";
    branchAddress: string = "";
    branchMobile: string = "";
    branchName: string = "";
    branchNo: string = "";
    applicantMobile: string = "";
    companyCertType: string = "";
    companyCertNo: string = "";
    legalCertType: string = "";
    legalCertNo: string = "";
}

//浦发
export class SPDBCompanyInfoModel {
    companyName: string = "";
    unifiedNumber: string = "";
    legalName: string = "";
    legalMobile: string = "";
    accountType: number = 0;
    appointmentTime: string = "";
    branchProvince: string = "";
    branchCity: string = "";
    branchNo: string = "";
    branchName: string = "";
    branchTel: string = "";
    defaultMobile: string = "";
    clientName: string = "";
    acctType: string = "C";
    companyAddr: string = "";
    unifiedNumberExpData: string = "";
    regCapital: string = "";
    legalRprsntIDType: string = "";
    legalRprsntIDNo: string = "";
    legalRprsntIDExpData: string = "";
    depositAcctType: string = "";
    openBankName: string = "";
    basicBankAcctNo: string = "";
    openAcctLcnsId: string = "";
    branchAddress: string = "";
}

const ApplicantCPost = {
    "0": "法定代表人",
    "1": "财务负责人",
    "2": "其他",
    "3": "单位负责人",
};
const CompanyCertType = {
    C01: "营业执照",
    C35: "统一社会信用代码",
    C09: "组织机构代码证",
    C03: "境外企业注册证书",
    C06: "社会团体登记证书",
    C07: "主管部门批文",
    C08: "事业单位法人证书",
    C04: "临时营业执照",
    C10: "部队开户核准通知书",
    C11: "国家税务登记证",
    C12: "地方税务登记证",
    C13: "贷款证",
    C14: "金融机构许可证",
    C15: "商业登记证",
    C16: "香港有限公司注册证书",
    C17: "海外有限公司注册证书",
    C18: "SWIFT CODE",
    C19: "中国现代支付行行号",
    C20: "银行代码",
    C22: "机构信用代码证",
    C23: "税务登记证（国地税）",
    C05: "民办非企业登记证书",
    C36: "纳税人识别号（增值税）",
    C98: "TAX ID",
    C99: "单位其他证件",
    C51: "律师事务所执业许可证",
    C49: "办学许可证",
    C50: "经营证券期货业务许可",
    C48: "工会法人资格证书",
    C47: "基金会登记证书",
    C46: "宗教活动场所登记证",
    C45: "外资企业驻华代表机构",
    C44: "清算组批文",
    C43: "LEI",
    C24: "农村集体经济组织登记",
    C52: "社会福利机构执业证",
};
const ActCcyType = {
    "10": "人民币",
    "21": "港币",
    "24": "新西兰元",
    "29": "澳元",
    "32": "美元",
    "35": "欧元",
    "39": "加拿大元",
    "43": "英镑",
    "65": "日元",
    "69": "新加坡元",
    "87": "瑞士法郎",
};
const AplActTyp = {
    "0": "基本账户",
    "1": "一般账户",
    "2": "专用账户",
    "3": "临时账户",
};
const MbrDocTyp = {
    P01: "居民身份证",
    P02: "学生证",
    P03: "临时居民身份证",
    P04: "军人证",
    P08: "武警身份证",
    P16: "居民户口簿",
    P20: "港澳居民来往内地通行证",
    P21: "台湾居民来往大陆通行证",
    P22: "监护人证件",
    P23: "居住证",
    P24: "暂住证",
    P31: "护照",
    P34: "外国人永久居留身份证",
    P35: "大陆居民往来港澳通行证",
    P36: "大陆居民往来台湾通行证",
    P37: "外交人员证",
    P38: "行政技术人员证",
    P39: "国际组织人员证",
    P40: "港澳居民居住证",
    P41: "台湾居民居住证",
    P99: "个人其它证件",
};
const MbrTyp = {
    "1": "法人/单位负责人",
    "2": "经办人",
};
export class Member {
    constructor(mbrTyp: keyof typeof MbrTyp) {
        this.mbrTyp = mbrTyp;
    }
    // 成员类型
    mbrTyp: "" | keyof typeof MbrTyp = "";
    // 成员姓名
    mbrNm = "";
    // 成员国别 仅支持CHN
    readonly mbrDocNtn = "CHN";
    // 成员证件类型
    mbrDocTyp: "" | keyof typeof MbrDocTyp = "";
    // 成员证件号
    mbrDocId = "";
    // 成员手机号
    mbrCtcTel = "";
}
export const aplActTypes = (Object.keys(AplActTyp) as Array<keyof typeof AplActTyp>).map((key) => ({ label: AplActTyp[key], value: key }));
export const companyCertTypes = (Object.keys(CompanyCertType) as Array<keyof typeof CompanyCertType>).map((key) => ({
    label: CompanyCertType[key],
    value: key,
}));
export const mbrDocTyps = (Object.keys(MbrDocTyp) as Array<keyof typeof MbrDocTyp>).map((key) => ({ label: MbrDocTyp[key], value: key }));
export const actCcyTypes = (Object.keys(ActCcyType) as Array<keyof typeof ActCcyType>).map((key) => ({
    label: ActCcyType[key],
    value: key,
}));
export class CMBCompanyInfoModel {
    companyName = ""; // 公司名称
    unifiedNumber = ""; // 统一社会信用代码
    legalName = ""; // 法人姓名
    legalMobile = ""; // 法人手机号
    accountType: number = 0; // 是否已有基本户 0:否 1:是
    openProvince = ""; // 开户省份
    openCity = ""; // 开户城市
    branchName = ""; // 网点名称
    branchNo = ""; // 网点编号
    branchTel = ""; // 网点电话
    branchAddr = ""; // 网点地址
    applicantName = ""; // 申请人姓名
    applicantMobile = ""; // 申请人手机号
    companyCertNo = ""; // 公司证件号
    oprWhtrLgp = "Y"; // 是否法人亲自办理  Y:是 N:否
    members: Array<Member> = []; // 列表成员
    applicantCPos: "" | keyof typeof ApplicantCPost = "1"; // 申请人职务
    companyCertType: "" | keyof typeof CompanyCertType = ""; // 公司证件类型
    actCcyTyp: "" | keyof typeof ActCcyType = ""; // 币种
    aplActTyp: "" | keyof typeof AplActTyp = ""; // 账户类型
}
