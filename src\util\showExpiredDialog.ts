import { createApp } from "vue";
import ExpiredDialog from "@/components/ExpiredDialgo/index.vue";
import ExpiredToBuyDialog from "@/components/ExpiredToBuyDialog/index.vue";
import ElementPlus from "element-plus";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "./thirdpart";

export const showExpiredDialog = (): Promise<boolean> => {
    return new Promise<boolean>(() => {
        const insertBody = () => {
            const capp = createApp(ExpiredDialog);
            const container = document.createElement("div");
            capp.use(ElementPlus);
            capp.mount(container);
            document.body.insertBefore(container, document.body.firstChild); //插入到body最前面，层级更高
        };
        if (useThirdPartInfoStoreHook().isHideBarcode) {
            thirdPartNotify(thirtPartNotifyTypeEnum.globalAccountSetExpired).then(() => {
                insertBody();
            });
        } else {
            insertBody();
        }
    });
};
export const showExpiredToBuyDialog = (heading: string): Promise<boolean> => {
    return new Promise<boolean>(() => {
        const insertBody = () => {
            const props = {
                heading: heading,
            };
            const capp = createApp(ExpiredToBuyDialog, props);
            const container = document.createElement("div");
            capp.use(ElementPlus);
            capp.mount(container);
            document.body.insertBefore(container, document.body.firstChild); //插入到body最前面，层级更高
        };
        if (useThirdPartInfoStoreHook().isHideBarcode) {
            thirdPartNotify(thirtPartNotifyTypeEnum.globalAccountSetExpired).then(() => {
                insertBody();
            });
        } else {
            insertBody();
        }
    });
};
