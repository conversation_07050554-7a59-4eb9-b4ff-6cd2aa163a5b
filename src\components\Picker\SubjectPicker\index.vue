<template>
    <el-tooltip
        :visible="visible"
        popper-class="el-option-tool-tip"
        effect="light"
        :content="content"
        placement="right"
        :hide-after="0"
        :virtual-ref="inputElement"
    >
        <el-select-v2
            ref="selectRef"
            v-model="innerAsubCode"
            :options="selectOptions"
            :popper-class="'subject-picker'"
            :class="[generateId, , visibleSelect ? 'visibleSelect' : '']"
            :style="{ width: diyWidth, position: 'relative' }"
            :teleported="props.isToBody"
            :fit-input-width="true"
            :filterable="true"
            :remote="!useOriginFilter"
            :placeholder="dynamicPlaceholder"
            :default-first-option="true"
            :reserve-keyword="false"
            :clearable="clearable"
            :required="true"
            :scrollbar-always-on="true"
            :disabled="props.disabled"
            placement="bottom"
            suffix-icon=""
            :remote-method="filterMethod"
            @clear="clickClearable"
            @visible-change="handleVisibleChange"
            @input="handleInput"
            @change="handleChange"
            @mouseenter="visibleMouseenter"
            @mouseleave="visible = false"
            @focus="handleFocus"
            @blur="handleBlur"
        >
            <template #default="{ item }">
                <Tooltip
                    :key="item.value"
                    :content="item.label"
                    :max-width="defaultMaxWidth"
                    :line-clamp="1"
                    :font-size="14"
                    :placement="'left'"
                    :teleported="true"
                    :dynamicWidth="dynamicWidth"
                >
                    <span class="select-info-inner">
                        {{ item.label }}
                    </span>
                </Tooltip>
            </template>
        </el-select-v2>
    </el-tooltip>
    <div>
        <el-popover
            popper-class="subject-popover"
            placement="right"
            trigger="click"
            :width="'372px'"
            :teleported="props.isToBody"
            :disabled="props.disabled"
            ref="popoverRef"
            @after-enter="showTree = true"
            @before-leave="showTree = false"
            @before-enter="handleDialogShow"
        >
            <template #reference>
                <div class="asub-img" @click.stop v-if="!props.disabled" :style="{ right: asubImgRight }"></div>
            </template>
            <SubjectDialog
                :expose-id-and-name="exposeIdAndName"
                :isById="isById"
                :isExpansion="isExpansion"
                :is-by-number-id="isByNumberId"
                :showTree="showTree"
                :showDisabled="showDisabled"
                :subjext-picker-back-text="subjextPickerBackText"
            ></SubjectDialog>
        </el-popover>
    </div>
</template>

<script lang="ts" setup>
import { ElPopover } from "element-plus";
import SubjectDialog from "./SubjectDialog.vue";
import { computed, provide, watch, ref, nextTick, inject } from "vue";
import { updateAsubCodeKey, popoverHandleCloseKey, stopPopoverCloseKey } from "./symbols";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { onMounted, watchEffect } from "vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import Tooltip from "@/components/Tooltip/index.vue";
import { pinyin } from "pinyin-pro";

const accountSubjectStore = useAccountSubjectStore();
const selectRef = ref();

const stopPopoverClose = inject(stopPopoverCloseKey) as Function;

const props = defineProps({
    modelValue: { type: String || Number, required: true },
    isById: { type: Boolean, default: false },
    isToBody: { type: Boolean, default: false },
    diyWidth: { type: String, default: "298px" },
    isByNumberId: { type: Boolean, default: false },
    isExpansion: { type: Boolean, default: false },
    isParentShow: { type: Boolean, default: true },
    clearable: { type: Boolean, default: true },
    asubImgRight: { type: String, default: "35px" },
    showDisabled: { type: Boolean, default: false },
    immediate: { type: Boolean, default: true },
    needAsubName: { type: Boolean, default: true },
    disabled: { type: Boolean, default: false },
    isErpAsub: { type: Boolean, default: false },
    defaultMaxWidth: { type: Number, default: 200 },
    isAccount: { type: Boolean, default: false },
    exposeIdAndName: { type: Boolean, default: false },
    subjextPickerBackText: { type: String, default: "返回" },
    dynamicWidth: { type: Boolean, default: false },
    useOriginFilter: { type: Boolean, default: false },
});

const showTree = ref(false);

const selectOptions = computed(() => {
    return showSubjectData.value.map((item) => {
        if (!item.showText) {
            item.showText = item.asubCode + " " + item.asubAAName;
        }
        return {
            label: item.showText,
            value: props.isById || props.isByNumberId ? item.asubId.toString() : item.asubCode,
        };
    });
});
const isById = computed(() => {
    return props.isById;
});
const isExpansion = computed(() => {
    return props.isExpansion;
});

function handleFocus() {
    emit("focus");
}
function handleBlur() {
    emit("blur");
}
const emit = defineEmits<{
    (e: "update:modelValue", value: number | string): void;
    (e: "get-asub-id", value: number): void;
    (e: "subjectCountChange"): void;
    (e: "change", value: any, asubName: string, name?: string): void;
    (e: "focus"): void;
    (e: "blur"): void;
    (e: "before-show"): void;
}>();

function handleDialogShow() {
    emit("before-show");
}

const filterMethod = (query: string) => {
    if (query.trim() === "") {
        showSubjectData.value = subjectData.value;
        return;
    }
    showSubjectData.value = subjectData.value.filter((item: IAccountSubjectModel) => {
        const pinyinFull = pinyin(item.asubAAName, {   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();
        return (
            item.asubCode.includes(query) ||
            item.asubAAName.includes(query) ||
            item.acronym.includes(query.trim()) ||
            getSelectLabel(item).includes(query) ||
            pinyinFull.includes(query)
        );
    });
};

const subjectData = ref<Array<IAccountSubjectModel>>([]);
const showSubjectData = ref<Array<IAccountSubjectModel>>([]);
const dynamicPlaceholder = ref(" ");
const innerAsubCode = computed<any>({
    get() {
        return props.modelValue ? props.modelValue : undefined;
    },
    set(value: string) {
        emit("update:modelValue", value === undefined ? "" : value);
    },
});

watch(innerAsubCode, (value: string) => {
    let asubId: number = subjectData.value.find((item: IAccountSubjectModel) => item.asubCode === (value + "").split(" ")[0])
        ?.asubId as number;
    emit("get-asub-id", asubId);
});
const asubId = ref(0);
const asubName = ref("");
const foreigncurrency = ref(0);
watch(innerAsubCode, (value: string) => {
    for (let i = 0; i < subjectData.value.length; i++) {
        const item = subjectData.value[i];
        if (isById.value) {
            if (item.asubId + "" === value) {
                asubId.value = item.asubId - 0;
                asubName.value = getSelectLabel(item);
                foreigncurrency.value = item.foreigncurrency;
                return;
            }
        } else {
            if (item.asubCode === value) {
                asubName.value = getSelectLabel(item);
                asubId.value = item.asubId - 0;
                foreigncurrency.value = item.foreigncurrency;
                return;
            }
        }
    }
    asubName.value = "";
    asubId.value = 0;
});
function getFullLabel(key: string) {
    return showSubjectData.value.find((v) => v[!props.isById && !props.isByNumberId ? "asubCode" : "asubId"].toString() === key)?.showText;
}
// 生成一个八位的随机数
const random = () => {
    return Math.floor(Math.random() * 100000000);
};
// 生成一个唯一的id
const generateId = `select-${random()}`;
//一键清除后展开下拉列表
const clickClearable = () => {
    selectRef.value.toggleMenu();
};
// 这一段是选中之后直接失焦，如果溢出会立马显示...
const visibleSelect = ref(false);
const handleVisibleChange = (visible: boolean) => {
    visibleSelect.value = visible;
    let timer = setTimeout(() => {
        let selectFilterInput = document.querySelector(`.${generateId} .select-trigger .el-input__inner`) as HTMLInputElement;
        if (selectFilterInput) {
            selectFilterInput.value = innerAsubCode.value
                ? getSelectLabel(
                      showSubjectData.value.find(
                          (v) => v[!props.isById && !props.isByNumberId ? "asubCode" : "asubId"].toString() === innerAsubCode.value
                      ) as IAccountSubjectModel
                  )
                : "";
        }
        clearTimeout(timer);
    }, 0);
    if (innerAsubCode.value === undefined) {
        showSubjectData.value = subjectData.value;
    }
    const selectDom = selectRef.value?.$refs?.selectRef;
    if (!visible) {
        nextTick().then(() => {
            selectDom?.classList.add("noVisible");
            if (stopPopoverClose) {
                stopPopoverClose();
            }
        });
    } else {
        nextTick().then(() => {
            if (selectDom?.classList.contains("noVisible")) {
                selectDom?.classList.remove("noVisible");
            }
        });
    }
};

const searchRet = ref();
const handleInput = (event: InputEvent) => {
    if (props.useOriginFilter) return;
    // 搜索结果重置
    const value = (event.target as HTMLInputElement)?.value || "";
    searchRet.value = [];
    if (value.trim()) {
        searchRet.value = selectOptions.value.filter((item) => item.label.includes(value));
        innerAsubCode.value = searchRet.value && searchRet.value[0].value;
    } else {
        searchRet.value = [];
    }
    let selectFilterInput = document.querySelector(`.${generateId} .select-trigger .el-input__inner`) as HTMLInputElement;
    if (selectFilterInput) {
        if (!selectFilterInput.value || !innerAsubCode.value) {
            selectFilterInput.placeholder = "";
            innerAsubCode.value = "";
        }
    }
};
const handleChange = (value: any) => {
    emit("change", value, selectRef.value?.states?.selectedLabel || "");
};

function getSelectLabel(subject: IAccountSubjectModel) {
    if (!subject.showText) {
        subject.showText = subject.asubCode + " " + subject.asubAAName;
    }
    return subject.showText;
}
const popoverRef = ref<InstanceType<typeof ElPopover>>();
const handleClose = () => {
    popoverRef.value?.hide();
};

provide(popoverHandleCloseKey, handleClose);

function updateAsubCode(asubCode: string, asubName?: string) {
    if (props.exposeIdAndName) {
        innerAsubCode.value = asubCode;
        const name = getFullLabel(asubCode);
        emit("change", asubCode, asubName || "", name);
    } else if (!asubName || !props.needAsubName) {
        innerAsubCode.value = asubCode;
        if (props.isAccount) emit("change", asubCode, asubName || "");
    } else {
        innerAsubCode.value = asubCode + " " + asubName;
    }
}

provide(updateAsubCodeKey, updateAsubCode);

const backupAsubList = ref<Array<IAccountSubjectModel>>([]);

let isEmit = false;
const list = computed(() => {
    let asubs = props.showDisabled ? accountSubjectStore.accountSubjectList : accountSubjectStore.accountSubjectListWithoutDisabled;
    if (!props.isParentShow) {
        asubs = asubs.filter((item: IAccountSubjectModel) => item.isLeafNode === true);
    }
    return asubs;
});
watch(
    list,
    () => {
        if (
            list.value.findIndex(
                (item: IAccountSubjectModel) => item.asubId === Number(innerAsubCode.value) || item.asubCode === innerAsubCode.value
            ) < 0
        ) {
            innerAsubCode.value = "";
        }
    },
    { immediate: true }
);
watchEffect(() => {
    const asubWithPYList = list.value.slice();

    if (asubWithPYList.findIndex((item: IAccountSubjectModel) => item.asubId === Number(innerAsubCode.value)) < 0 && props.isErpAsub) {
        innerAsubCode.value = "";
    }
    if (props.immediate) {
        subjectData.value = asubWithPYList;
        showSubjectData.value = asubWithPYList;
        if (isEmit) return;
        emit("subjectCountChange");
        isEmit = true;
    } else {
        backupAsubList.value = asubWithPYList;
    }
});

const handleAsyncRender = () => {
    if (!props.immediate) {
        subjectData.value = backupAsubList.value;
        showSubjectData.value = subjectData.value;
        emit("subjectCountChange");
    }
};

function focus() {
    const input = selectRef.value?.$el?.querySelector("input");
    input?.focus();
    clickClearable();
}
defineExpose({
    asubName,
    asubId,
    foreigncurrency,
    handleClose,
    handleAsyncRender,
    focus,
});

watch(innerAsubCode, (val) => {
    if (val) {
        dynamicPlaceholder.value = " ";
    }
});

const visible = ref(false);
const content = ref("");
const inputElement = ref(selectRef.value?.$refs?.reference?.input);
const tooltipCanshow = ref(true);
const checkOverflow = () => {
    const input = selectRef.value?.$refs?.selectionRef;
    const spanElement = input?.querySelector(".el-select-v2__placeholder");
    let label = "";
    if (selectRef.value?.states?.selectedLabel) {
        content.value = selectRef.value?.states?.selectedLabel;
        label = selectRef.value?.states?.selectedLabel.trim();
    } else {
        content.value = "";
        label = "";
    }
    if (!tooltipCanshow.value) return;
    if (!spanElement) {
        visible.value = false;
        return;
    }
    if (spanElement.scrollWidth > spanElement.clientWidth && label.trim() !== "") {
        visible.value = true;
    } else {
        visible.value = false;
    }
};
const visibleMouseenter = () => {
    checkOverflow();
};
const hiddenOverflow = () => {
    visible.value = false;
};
onMounted(() => {
    const input = selectRef.value?.$refs?.reference?.input;
    if (input) {
        checkOverflow();
        input.addEventListener("mouseenter", checkOverflow);
        input.addEventListener("mouseleave", hiddenOverflow);
        input.addEventListener("focus", () => {
            nextTick().then(() => {
                tooltipCanshow.value = false;
            });
        });
        input.addEventListener("blur", () => {
            nextTick().then(() => {
                tooltipCanshow.value = true;
            });
        });
    }
});
</script>

<style lang="less" scoped>
:deep(.el-popover.el-popper) {
    min-width: 372px;
}

:deep(.subject-picker) {
    .el-popper__arrow {
        display: none;
    }
}

.asub-img {
    position: absolute;
    right: 16px;
    top: 1px;
    bottom: 1px;
    width: 28px;
    height: 28px;
    background: url("@/assets/AccountBooks/book.png") no-repeat center;
    cursor: pointer;
}

:deep(.el-select-v2__suffix) {
    position: absolute;
    top: 50%;
    right: 26px;
    transform: translateY(-50%);

    .el-select-v2__caret {
        & + .el-select-v2__caret {
            display: inline-flex;
        }
    }
}
:deep(.el-select-v2__placeholder) {
    text-align: left;
}

// 长科目显示气泡
:deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
}

// 文字选择框长度
:deep(.el-select-v2__input-wrapper) {
    justify-content: flex-start;
    cursor: text !important;
    .el-select-v2__combobox-input {
        width: 85% !important;
        flex-grow: initial;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: text;
    }
}

:deep(.el-select-v2__wrapper.is-focused) {
    .el-select-v2__placeholder {
        color: var(--border-color);
    }
}
.noVisible {
    :deep(.el-select-v2__wrapper.is-focused) {
        .el-select-v2__placeholder {
            color: var(--font-color);
        }
    }
}
</style>
<style lang="less">
.el-select-v2__popper.subject-picker .el-select-dropdown .el-vl__wrapper {
    max-height: 170px;
}
</style>
