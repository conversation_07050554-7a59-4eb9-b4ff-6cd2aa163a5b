<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title></title>
    </head>
    <body>
        <script>
            function getQueryStringByName(name) {
                var result = location.search.match(new RegExp("[\?\&]" + name + "=([^\&]+)", "i"));
                if (result == null || result.length < 1) {
                    return "";
                }
                return result[1];
            }
            var href = getQueryStringByName("href");
            var meta = document.createElement("meta");
            meta.content = "never";
            meta.name = "referrer";
            document.getElementsByTagName("head")[0].appendChild(meta);
            window.location.href = decodeURIComponent(href);
        </script>
    </body>
</html>
