<template>
    <div class="select-checkbox" :style="{ width: width }" ref="selectCheckboxRef">
        <div class="top-box" @click="toggleCheckBoxShow">
            <Tooltip :maxWidth="overflowWidth" :content="placeHoderVal" :isInput="false" placement="right">
                <div class="place-hoder" :class="{ over: isFocus, down: !isFocus, stretch: !useIcon }" @click="handlePlaceHoderClick">
                    {{ placeHoderVal }}
                </div>
            </Tooltip>
            <input
                ref="inputRef"
                :class="{ down: isFocus, over: !isFocus, stretch: !useIcon }"
                v-model="searchVal"
                type="text"
                @input="handleInput"
                @keydown.enter="handleEnter"
            />
        </div>
        <slot name="icon"></slot>
        <div class="check-box" v-show="checkBoxShow">
            <el-scrollbar :height="220" :always="true" ref="scrollBarRef" @scroll="handleScroll">
                <el-checkbox name="all" v-show="showAll && showOptions.length > 0" v-model="all" @change="(check) => allChange(check)">
                    全部
                </el-checkbox>
                <el-checkbox-group v-model="selectedList" @change="handleCheckBoxClick">
                    <template v-for="(item, index) in showOptions" :key="item[assignProps.id]">
                        <template v-if="index >= (currentPage - 3) * pageSize && index < (currentPage + 2) * pageSize">
                            <Checkbox
                                :showTip="showTip"
                                :setTipShow="setTipShow"
                                :label="item[assignProps.id]"
                                :text="item[assignProps.name]"
                                :line-count="1"
                            ></Checkbox>
                        </template>
                        <div v-else :style="{ height: minHeight + 'px' }"></div>
                    </template>
                </el-checkbox-group>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { ElScrollbar } from "element-plus";

import Checkbox from "@/components/SelectCheckbox/Checkbox.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const { cloneDeep, isEqual, debounce } = getGlobalLodash();
const scrollBarRef = ref<InstanceType<typeof ElScrollbar>>();
const selectCheckboxRef = ref<HTMLDivElement>();

interface IProps {
    id: string;
    name: string;
}
const defaultProps: IProps = {
    id: "id",
    name: "name",
};
const props = withDefaults(
    defineProps<{
        options: Array<any>;
        selectedList: Array<number | string>;
        width?: string;
        showAll?: boolean;
        customProps?: IProps;
        useValueLabel?: boolean;
        connector?: string;
        customFilterMethod?: (query: string) => Array<any>;
        useIcon?: boolean;
        selectWithBlur?: boolean;
        blurMethod?: (query: string, fullListWithBlank?: boolean) => Array<any>;
        filterByExpenses?: boolean;
        filterStartCode?: string;
    }>(),
    {
        width: "200px",
        customProps: () => ({} as IProps),
        showAll: true,
        useValueLabel: false,
        connector: "；",
        useIcon: false,
        selectWithBlur: false,
        filterByExpenses: false,
        filterStartCode: "",
    }
);
const assignProps = computed(() => Object.assign({}, defaultProps, props.customProps));

const emit = defineEmits<{
    (event: "update:selectedList", args: Array<number | string>): void;
    (event: "checkBoxShowChange", args: boolean): void;
    // 输出匹配科目的数量，判断是否为空
    (event: "searchListLengthChange", show: boolean): void;
}>();
const selectedList = computed({
    get() {
        return props.selectedList;
    },
    set(value: Array<number | string>) {
        emit("update:selectedList", value);
    },
});

// 需要拿父元素的宽度减掉左边距  不然会有12px的偏差
const overflowWidth = computed(() => (props.useIcon ? 254 : 286));
const placeHoderVal = ref("");
const isFocus = ref(true);
const inputRef = ref<HTMLInputElement>();
const checkBoxShow = ref(false);
const searchVal = ref("");
const all = ref(true);
const options = computed(() => props.options);
const showOptions = ref<Array<any>>([]);

const allChange = (checked: any) => {
    selectedList.value = checked ? showOptions.value.map((item) => item[assignProps.value.id]) : [];
    isFocus.value = true;
};

watch(
    options,
    () => {
        showOptions.value = options.value;
        const equal = isEqual(
            selectedList.value,
            showOptions.value.map((item) => item[assignProps.value.id])
        );
        all.value = selectedList.value.length === showOptions.value.length && selectedList.value.length !== 0 && equal;
    },
    { immediate: true, deep: true }
);

watch(
    [selectedList, showOptions],
    () => {
        const val = selectedList.value;
        const equal = isEqual(
            cloneDeep(selectedList.value).sort(),
            cloneDeep(showOptions.value)
                .map((item: any) => item[assignProps.value.id])
                .sort()
        );
        all.value = val.length === showOptions.value.length && val.length !== 0 && equal;
        const arr = showOptions.value
            .filter((item) => val.includes(item[assignProps.value.id]))
            .map((item) => item[assignProps.value[props.useValueLabel ? "id" : "name"]]);
        if (arr.length === 0) {
            placeHoderVal.value = "";
        } else if (arr.length < options.value.length) {
            placeHoderVal.value = arr.join(props.connector);
        } else if (arr.length === options.value.length) {
            placeHoderVal.value = "全部";
        }
    },
    { immediate: true, deep: true }
);

function modifySearchVal() {
    searchVal.value = placeHoderVal.value || "";
}

function toggleCheckBoxShow() {
    checkBoxShow.value = !checkBoxShow.value;
}
function handlePlaceHoderClick() {
    searchVal.value = placeHoderVal.value || "";
    isFocus.value = false;
    nextTick().then(() => {
        inputRef.value?.focus();
    });
}
function handleCheckBoxClick() {
    isFocus.value = true;
    modifySearchVal();
}
const showTip = ref(true);
function setTipShow(val: boolean) {
    showTip.value = val;
}
const pageSize = computed(() => 8);
const minHeight = computed(() => 28);
const currentPage = ref(0);
function handleScroll(event: { scrollLeft: number; scrollTop: number }) {
    showTip.value = false;
    currentPage.value = Math.max(Math.floor(event.scrollTop / (minHeight.value * pageSize.value)), 0);
}

function filterMethodFn(query: string) {
    if (!query.trim()) {
        showOptions.value = options.value;
        return;
    }
    if (props.customFilterMethod) {
        showOptions.value = props.customFilterMethod(query);
        return;
    }
    const label = assignProps.value.name;
    // showOptions.value = options.value.filter((item) => item[assignProps.value.name].includes(query));
    showOptions.value = commonFilterMethod(query, options.value, label);
}
const filterMethod = debounce(filterMethodFn, 500);
function handleInput(e: Event) {
    checkBoxShow.value = true;
    const target = e.target as HTMLInputElement;
    placeHoderVal.value = target.value;
    filterMethod(target.value);
}

function handleEnter() {
    closeCheckBoxWithOperate(true);
}

let cacheQuery = "";
function closeCheckBoxWithOperate(triggerByEnter = false) {
    checkBoxShow.value = false;
    isFocus.value = true;
    showOptions.value = options.value;
    scrollBarRef.value?.setScrollTop(0);
    if (!searchVal.value.trim() && triggerByEnter) {
        selectedList.value = [];
        searchVal.value = "";
        return;
    }
    if (placeHoderVal.value.trim() === cacheQuery) return;
    if (placeHoderVal.value.trim() === "全部") {
        selectedList.value = options.value.map((item) => item[assignProps.value.id]);
        cacheQuery = "全部";
        return;
    }
    if (props.selectWithBlur && props.blurMethod) {
        const blurSelectList = props.blurMethod(placeHoderVal.value, false);
        selectedList.value = blurSelectList.map((item) => item[assignProps.value.id]);
        cacheQuery = placeHoderVal.value;
    }
    nextTick().then(() => {
        searchVal.value = "";
    });
}

const setBoxNotShow = (e: MouseEvent) => {
    if (!selectCheckboxRef.value?.contains(e.target as HTMLElement)) {
        closeCheckBoxWithOperate();
    }
};

// 费用明细表判断是否损益科目
const isIncrease = () => {
    if (searchVal.value === "全部") return;
    if (props.filterByExpenses && props.filterStartCode !== "") {
        let searchStr = "";
        if (searchVal.value.includes("-")) {
            searchStr = searchVal.value.split("-")[0];
        } else if (searchVal.value.includes(",")) {
            const arr = searchVal.value.split(",");
            searchStr = arr[arr.length - 1];
        } else {
            searchStr = searchVal.value;
        }
        if (
            (!searchStr.startsWith(props.filterStartCode) && searchStr.trim() !== "" && searchStr.length >= 3) ||
            showOptions.value.length === 0
        ) {
            emit("searchListLengthChange", true);
        } else {
            emit("searchListLengthChange", false);
        }
    }
};
const isIncreaseFn = debounce(isIncrease, 1000);
watch(
    () => searchVal.value,
    () => {
        // 费用明细表判断是否损益科目
        isIncreaseFn();
    }
);

onMounted(() => {
    document.addEventListener("click", (e) => setBoxNotShow(e));
});
onUnmounted(() => {
    document.removeEventListener("click", setBoxNotShow);
    cacheQuery = "";
});

function closeCheckBox() {
    checkBoxShow.value = false;
}
function changeUpElement(up: boolean) {
    isFocus.value = up;
}
defineExpose({ closeCheckBox, changeUpElement, searchVal: searchVal.value });
watch(checkBoxShow, (visible) => {
    emit("checkBoxShowChange", visible);
});
</script>

<style lang="less" scoped>
.down {
    display: none;
}

.select-checkbox {
    position: relative;
    width: 200px;

    .check-box {
        width: 100%;
        border: 1px solid var(--border-color);
        box-sizing: border-box;
        background-color: var(--white);
        max-height: 220px;
        overflow: auto;
        z-index: 200;
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 32px;
        left: 0;
        border-top: none;

        :deep(.el-checkbox-group) {
            width: 100%;
            display: flex;
            flex-direction: column;
        }

        :deep(.el-checkbox) {
            width: 100%;
            margin-right: 0;
            padding: 4px 8px;
            box-sizing: border-box;
            min-height: 20px;
            height: auto;

            &:hover {
                background-color: var(--table-hover-color);
            }

            .el-checkbox__input.is-checked + .el-checkbox__label {
                color: var(--font-color);
            }

            display: flex;
            align-items: flex-start;

            .el-checkbox__label {
                flex: 1;
                text-align: left;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                /* 设置最多显示1行 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                white-space: normal;
                word-break: break-all;
                text-align: left;
                line-height: var(--line-height);
                vertical-align: baseline;
                font-size: var(--font-size);
            }

            .el-checkbox__input {
                margin-top: 3px;
            }
        }

        :deep(.el-scrollbar) {
            .el-scrollbar__wrap {
                overflow-y: auto;
            }

            .el-scrollbar__view {
                display: flex;
                flex-direction: column;
                align-items: start;
            }

        }
    }

    .top-box {
        box-sizing: border-box;
        height: 32px;
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
        border: 1px solid var(--input-border-color);

        :deep(.span_wrap) {
            width: 100%;
        }

        div.place-hoder {
            width: 266px;
            height: 28px;
            position: absolute;
            top: 1px;
            z-index: 100;
            line-height: 30px;
            padding-left: 10px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &.stretch {
                width: 100%;
            }
        }
        input {
            width: 266px;
            height: 28px;
            outline: none;
            padding-left: 10px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 30px;
            box-sizing: border-box;
            border-radius: var(--input-border-radius);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            // padding-right: 21px;
            position: absolute;
            top: 1px;
            border: none;

            &.stretch {
                width: 100%;
            }
        }
    }
}
</style>
