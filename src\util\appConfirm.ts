import { createApp } from "vue";
import AppConfirm from "@/components/AppConfirm/index.vue";
import ElementPlus from "element-plus";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

export const AppConfirmDialog = (operationType: number): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if (useThirdPartInfoStoreHook().isHideBarcode || window.isAccountingAgent || window.isErp || window.isProSystem) {
            resolve(true);
            return;
        }

        const props = {
            operationType,
            close: () => {
                if (close) close();
                resolve(false);
                capp.unmount();
                document.body.removeChild(container);
            },
            confirm: () => {
                resolve(true);
                capp.unmount(); //注销
                document.body.removeChild(container); //点击后清除弹窗
            },
            cancel: () => {
                resolve(false);
                capp.unmount();
                document.body.removeChild(container);
            },
        };

        const capp = createApp(AppConfirm, props);
        const container = document.createElement("div");
        capp.use(ElementPlus);
        capp.mount(container);
        document.body.insertBefore(container, document.body.firstChild); //插入到body最前面，层级更高
    });
};
