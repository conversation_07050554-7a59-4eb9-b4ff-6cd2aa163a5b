<template>
    <div class="content">
        <div class="title">{{ importModeList[importMode] }}</div>
        <div class="slot-title no-border">{{ importModeList[importMode] }}</div>
        <div :class="['main-content', { historyRecordContent: importMode === 'historyRecord' }]">
            <!-- 新版旧账导入 -->
            <div :class="['import-box', { historyRecord: importMode === 'historyRecord' }, { hasBorder }]" v-loading="loading">
                <!-- 普通导账 -->
                <div v-show="importMode === 'init'" :class="['import-box-header', { statusList: currentStep === 4 }]">
                    <div v-if="currentStep === 4" class="top-buttons">
                        <div v-if="isAccountingAgent">
                            <a class="button new-style" @click="handleChangeStep(1)">返回</a>
                        </div>
                        <div v-else>
                            <a class="button new-style" @click="handleContinueImport">继续导入账套</a>
                            <a class="button new-style ml-20" @click.prevent="returnCreate">创建账套</a>
                            <a v-if="isFromCreate && newAppasid" class="button ml-20 solid-button" @click.prevent="returnHome">
                                开始记账
                            </a>
                        </div>
                    </div>
                    <ul v-else class="step-list">
                        <li :class="{ current: currentStep === 1 }">
                            <span class="step-num-bg"></span>
                            <span class="step-num">{{ currentStep > 1 ? "√" : "1" }}</span>
                            选择软件
                        </li>
                        <li></li>
                        <li :class="{ current: currentStep === 2 }">
                            <span class="step-num-bg"></span>
                            <span class="step-num">{{ currentStep > 2 ? "√" : "2" }}</span>
                            输入账号
                        </li>
                        <li></li>
                        <li :class="{ current: currentStep === 3 }">
                            <span class="step-num-bg"></span><span class="step-num">3</span>导入旧账数据
                        </li>
                    </ul>
                </div>
                <div v-show="importMode === 'init'" :class="['import-box-content', { statusList: currentStep === 4 }]">
                    <div class="step">
                        <div
                            v-if="importMode === 'init' && currentStep !== 4"
                            class="main-center-row"
                            style="height: 40px; margin-bottom: 3px"
                        >
                            <div class="row-title" style="width:132px">
                                <span v-if="currentStep === 1" class="highlight-red">*</span>
                                {{ currentStep === 1 ? "选择软件厂商：" : "产品版本：" }}
                            </div>
                            <div class="row-content">
                                <SoftWareSelect
                                    :softWareList="softWareList"
                                    :softWare="softWare"
                                    :softWareName="softWareName"
                                    :softWareListLoading="softWareListLoading"
                                    @changeSoftWare="handleSoftWareChange"
                                />
                                <el-popover placement="right" :width="300" trigger="hover">
                                    <template #reference>
                                        <img src="@/assets/Settings/question.png" style="height: 18px; cursor: pointer; margin-left: 7px" />
                                    </template>
                                    <template #default>
                                        柠檬云财务软件支持导入 <span style="color: #fa7c27">100+财务软件</span>
                                        的财务数据，节省用户迁移数据的人力投入，帮助用户快速体验财务软件的智能记账过程!
                                    </template>
                                </el-popover>
                            </div>
                        </div>
                        <div v-if="currentStep === 1">
                            <div v-if="isFromCreate" class="buttons">
                                <a class="button solid-button mr-20" @click.prevent="handleChangeStep(2)"> 下一步 </a>
                                <a class="button return-btn" style="padding: 0 5px" @click.prevent="returnCreate">返回创建账套</a>
                            </div>
                            <div v-if="!isFromCreate" class="buttons">
                                <a class="button solid-button" @click.prevent="handleChangeStep(2)"> 下一步 </a>
                            </div>
                            <div class="tips-box">
                                <p>您可以选择以下任意一种方式进行旧账导入：</p>
                                <p>① 在线导入旧账数据</p>
                                <p>② 下载<a class="link" @click="openDownload">导账工具</a>进行旧账导入</p>
                                <p>③ 使用<a class="link" @click="gotoTrailBalance">科目余额表导账</a>进行导入</p>
                                <p>④ 扫码添加客服，免费为您迁移账套</p>
                            </div>
                        </div>
                        <div v-if="currentStep === 2 || currentStep === 3">
                            <div class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title"><span class="highlight-red">*</span>用户名：</div>
                                <div class="row-content">
                                    <el-input
                                        v-model.trim="userName"
                                        type="text"
                                        style="width: 268px; height: 32px"
                                        placeholder="请输入当前选择产品版本管理员账号"
                                        clearable
                                    />
                                </div>
                            </div>
                            <div v-if="needCorporateAccount" class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title"><span class="highlight-red">*</span>企业号：</div>
                                <div class="row-content">
                                    <el-input
                                        v-model.trim="corporateAccount"
                                        type="text"
                                        style="width: 268px; height: 32px"
                                        placeholder="请输入企业号"
                                        clearable
                                    />
                                </div>
                            </div>
                            <div class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title"><span class="highlight-red">*</span>密码：</div>
                                <div class="row-content">
                                    <el-input
                                        v-model.trim="password"
                                        type="password"
                                        :style="{ width: needVertifyCode ? '268px' : '200px', height: ' 32px' }"
                                        placeholder="请输入管理员账号密码"
                                        clearable
                                    />
                                    <a
                                        v-if="!needVertifyCode"
                                        class="button solid-button ml-10"
                                        style="width: 60px; height: 32px"
                                        @click.prevent="searchAccountSets"
                                    >
                                        查询
                                    </a>
                                </div>
                            </div>
                            <div v-if="needVertifyCode" class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title">验证码：</div>
                                <div class="row-content">
                                    <el-input
                                        v-model="vertifyCode"
                                        type="text"
                                        style="width: 90px; height: 32px"
                                        placeholder="请输入验证码"
                                        clearable
                                    />
                                    <img
                                        v-if="vertifyCodePicUrl"
                                        :src="vertifyCodePicUrl"
                                        @click="getVertifyCode"
                                        class="vertify-code"
                                        alt="验证码"
                                    />
                                    <a class="refresh-btn ml-5 mr-5" @click.prevent="getVertifyCode"></a>
                                    <a class="button solid-button" style="width: 60px; height: 32px" @click.prevent="searchAccountSets">
                                        查询
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div v-show="currentStep === 3">
                            <div class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title">账套列表：</div>
                                <div class="row-content">
                                    <AccountSetListSelect
                                        :accountsetsList="accountsetsList"
                                        :currentList="currentAccountSetList"
                                        @change-accountset-list="handlechangecurrentList"
                                    />
                                </div>
                            </div>
                            <div v-if="showAccountStandardSelect" class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title">
                                    <span class="highlight-red">*</span>
                                    选择会计准则：
                                </div>
                                <div class="row-content">
                                    <el-select
                                        :teleported="false"
                                        v-model="accountStandard"
                                        placeholder="请选择会计准则"
                                        style="width: 268px; height: 32px"
                                    >
                                        <el-option label="--请选择--" :value="-1" />
                                        <el-option label="小企业会计准则" :value="1" />
                                        <el-option label="企业会计准则" :value="2" />
                                        <el-option v-if="![30131,30132].includes(softWare)" label="农民合作社" :value="4" />
                                    </el-select>
                                </div>
                            </div>
                            <div v-if="needImportOtherMode" class="main-center-row" style="height: 40px; margin-bottom: 3px">
                                <div class="row-title">
                                    <span class="highlight-red">*</span>
                                    导入其他模块：
                                </div>
                                <div class="row-content">
                                    <el-checkbox v-model="isJournal" label="资金" size="large" />
                                    <el-checkbox v-model="isInvoice" label="发票" size="large" />
                                    <el-checkbox v-model="isFixedAsset" label="资产" size="large" />
                                    <el-popover placement="right" :width="300" trigger="hover">
                                        <template #reference>
                                            <img
                                                src="@/assets/Settings/question.png"
                                                style="height: 18px; cursor: pointer; margin-left: 7px"
                                            />
                                        </template>
                                        <template #default>
                                            <span>{{ importOtherModeTips }}</span>
                                        </template>
                                    </el-popover>
                                </div>
                            </div>
                            <div v-if="showTaxType" class="main-center-row">
                                <div class="row-title">
                                    <span class="highlight-red">*</span>
                                    增值税种类：
                                </div>
                                <div class="row-content">
                                    <el-radio-group v-model="taxType">
                                        <el-radio label="1" size="large">小规模纳税人</el-radio>
                                        <el-radio label="2" size="large">一般纳税人</el-radio>
                                    </el-radio-group>
                                </div>
                            </div>
                        </div>
                        <div v-if="currentStep === 4">
                            <!-- 导入状态列表 -->
                            <ImoprtStatusTable
                                :statusList="importStatusList"
                                @changeImportMode="handleChangeImportMode"
                                @importSuccess="importSuccess"
                            />
                        </div>
                    </div>
                    <div v-if="currentStep === 3" class="bottom-tips" :class="{ 'aa-tips': isAccountingAgent }">
                        <div v-if="isAccountingAgent">
                            <p class="tips">
                                <img src="@/assets/Settings/tips.png" alt="" />
                                1.账套导入会覆盖本账套数据，请谨慎操作，导账完成后，原账套将会放进回收站
                            </p>
                            <p class="tips">2.导入的数据默认为科目期初余额和凭证数据</p>
                            <p class="tips">3.在线导账仅支持单账套导入，如需批量导入账套请下载导账工具导入</p>
                        </div>
                        <p v-else class="tips">
                            <img src="@/assets/Settings/tips.png" alt="" />
                            导入的数据默认为科目期初余额和凭证数据
                        </p>
                    </div>
                    <div v-if="currentStep === 3" class="buttons">
                        <a class="button solid-button mr-20" @click.prevent="handleSubmit">确定</a>
                        <a class="button return-btn" @click.prevent="handleChangeStep(2)">上一步</a>
                    </div>
                    <div class="bottom-tool">
                        <p v-if="historyTotal > 0">
                            <a class="link" @click.prevent="() => (importMode = 'historyRecord')">导账历史记录 </a>
                        </p>
                        <p>
                            <img src="@/assets/Settings/import-icon.png" alt="" style="height: 14px; margin-right: 5px" />
                            <a class="link" @click.prevent="gotoImportTool">导账工具一键导入</a>
                        </p>
                        <p>
                            <img src="@/assets/Settings/file-icon.png" alt="" style="height: 14px; margin-right: 5px" />
                            <a class="link" @click.prevent="gotoTrailBalance">科目余额表导账</a>
                        </p>
                    </div>
                </div>
                <!-- 科目余额表导账 -->
                <div v-if="importMode === 'trailBalance'" class="trialBalance-import">
                    <TrailBalanceImport
                        :softWareName="softWareName"
                        @returnBack="handleChangeImportMode('init')"
                        @gotoHistory="handleChangeImportMode"
                    />
                </div>
                <!-- 导账工具-导账 -->
                <ImportTool
                    v-if="importMode === 'importTool'"
                    :showImportToolTips="showImportToolTips"
                    :softWareName="softWareName"
                    :softWareType="currentSoftWareType"
                    @download="openDownload"
                    @returnBack="handleChangeImportMode('init')"
                />
                <!-- 历史记录 -->
                <HistoryRecord
                    v-show="importMode === 'historyRecord'"
                    :importMode="importMode"
                    :historyData="historyData"
                    :historyTotal="historyTotal"
                    :loading="historyLoading"
                    @getHistory="getHistory"
                    @changeImportMode="handleChangeImportMode"
                />
                <!-- 二维码 -->
                <div
                    v-if="importMode !== 'statusError'"
                    class="help-box"
                    :style="{ top: helpBoxTop[importMode] + 'px', right: importMode === 'historyRecord' ? '-40px' : '40px' }"
                >
                    <p class="video-guide">
                        <img src="@/assets/Settings/help.png" alt="" style="height: 16px; margin-right: 5px" />
                        <a
                            class="link"
                            v-show="!isErp"
                            @click="globalWindowOpen(`https://help.ningmengyun.com/#/jz/videoPlayer?qType=${videoURL[importMode]}`)"
                            >不会操作？点此观看视频教程</a
                        >
                    </p>
                    <div class="qrcode-box">
                        <div>
                            <img v-if="isProSystem" src="@/assets/Settings/import-pro.png" alt="" style="height: 160px" />
                            <img v-else src="@/assets/Settings/import-other.png" alt="" style="height: 160px" />
                        </div>
                        <div class="qrcode-tip">遇到问题扫码添加客服<br />免费为你迁移账套</div>
                    </div>
                </div>
                <!-- 状态错误页面 -->
                <StatusError
                    v-if="importMode === 'statusError'"
                    :errorData="errorData"
                    :lastImportMode="lastImportMode"
                    @resetErrorData="resetErrorData"
                    @changeImportMode="handleChangeImportMode"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "ImportFromOther",
};
</script>
<script setup lang="ts">
import { ref, watch, computed, onMounted, onUnmounted } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { globalWindowOpenPage, globalWindowOpen, globalExport, setTopLocationhref, getUrlSearchParams } from "@/util/url";

import SoftWareSelect from "./components/SoftWareSelect.vue";
import AccountSetListSelect from "./components/AccountSetListSelect.vue";
import HistoryRecord from "./components/HistoryRecord.vue";
import ImportTool from "./components/ImportTool.vue";
import TrailBalanceImport from "./components/TrailBalanceImport.vue";
import ImoprtStatusTable from "./components/ImoprtStatusTable.vue";
import StatusError from "./components/StatusError.vue";
import type {
    ISoftWareListItem,
    ImportModeType,
    IAccountSetData,
    IAccountSetInfo,
    IImportData,
    IImportListItem,
    IHistoryListItem,
    IHistoryParams,
    IErrorInfo,
    IErrorReasonParams,
} from "./types";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getCookie } from "@/util/cookie";
import { ElConfirm } from "@/util/confirm";
import { getServiceId } from "@/util/proUtils";

const accountsetStore = useAccountSetStore();
const asId = accountsetStore.accountSet?.asId || 0;

const isErp = window.isErp;
const isProSystem = window.isProSystem;
const isAccountingAgent = window.isAccountingAgent;

// 新版旧账导入
const loading = ref(false);
// 下载导账工具
const openDownload = () => {
    globalExport("https://eus.ningmengyun.com/柠檬云导账工具.zip");
};

// 选择第三方软件
const softWare = ref(-1);
const softWareName = ref("--请选择--");
// 科目余额表软件Id
const newSoftWare = ref(-1);
// 当前选择软件类型
const currentSoftWareType = ref(-1);
const handleSoftWareChange = (softCompanyId: number, softCompanyName: string, type: number) => {
    softWare.value = softCompanyId;
    softWareName.value = softCompanyName;
    currentSoftWareType.value = type;
};
// 获取软件列表
const softWareList = ref<ISoftWareListItem[]>([]);
const softWareListLoading = ref(false);
const getSoftware = () => {
    softWareListLoading.value = true;
    request({
        url: `${window.importFromOther}/api/ImportFromYun/GetSoftCompanyInfo`,
        method: "POST",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    })
        .then((res: any) => {
            if (res.state !== 1000 || !res.data) return;
            softWareList.value = res.data;
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
            softWareListLoading.value = false;
        });
};
// 用户名
const userName = ref("");
// 密码
const password = ref("");
// 企业号
const corporateAccount = ref("");
// 验证码
const vertifyCode = ref("");
// 导入其他模块
// 发票
const isInvoice = ref(false);
// 资产
const isFixedAsset = ref(false);
// 资金
const isJournal = ref(false);
//增值税种类
// -1：显示用户自己选择   1：小规模纳税人   2：一般纳税人
const taxType = ref("1");
// 是否用户选择
const showTaxType = computed(() => accountsetsList.value.every((item) => item.taxtype_nm === -1));

// 导账状态列表
const importStatusList = ref<IImportListItem[]>([]);
// 导账历史记录
const historyData = ref<IHistoryListItem[]>([]);
const historyTotal = ref<number>(0);
const historyLoading = ref<boolean>(false);
// 是否跳转新账套
const importInfoId = ref("");

// 获取历史记录
const getHistory = (historyParams?: IHistoryParams) => {
    historyLoading.value = true;
    const params = !historyParams
        ? {
              AS_Name: "",
              VagueKey: "",
              Status: "",
              Import_Type: "",
              StartDateTime: "",
              EndDateTime: "",
              PageNumber: 1,
              PageSize: 20,
          }
        : {
              // 账套名称
              AS_Name: "",
              // 模糊搜索
              VagueKey: historyParams.searchKeyWord,
              Status: historyParams.importStatus === -1 ? null : historyParams.importStatus,
              Import_Type: historyParams.importType === -1 ? null : historyParams.importType,
              // 开始时间
              StartDateTime: historyParams.startDate,
              // 结束时间
              EndDateTime: historyParams.endDate,
              // 页码编号
              PageNumber: historyParams.currentPage,
              // 页面大小
              PageSize: historyParams.pageSize,
          };
    request({
        url: `${window.importFromOther}/api/ImportFromYun/GetImportInfo?${getUrlSearchParams(params)}`,
        method: "GET",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    })
        .then((res: IResponseModel<{ data: IHistoryListItem[]; count: number }>) => {
            if (res.state === 1000) {
                historyData.value = res.data.data;
                historyTotal.value = res.data.count;
            } else {
                historyData.value = [];
                historyTotal.value = 0;
            }
        })
        .finally(() => {
            historyLoading.value = false;
        });
    if (importInfoId.value) {
        request({
            url: `${window.importFromOther}/api/ImportFromYun/GetImportInfoById?importInfoId=${importInfoId.value}`,
            method: "GET",
            headers: {
                withCredentials: true,
                Asid: asId,
                Authorization: `Bearer ${getCookie("ningmengcookie")}`,
            },
        }).then((res: IResponseModel<{ data: IHistoryListItem; count: number }>) => {
            if (res.state === 1000) {
                if (res.data.data.appAsId) {
                    setTopLocationhref(`/Default/Default?appasid=${res.data.data.appAsId}`);
                }
            }
        });
    }
};

// 第三方账套列表
const accountsetsList = ref<IAccountSetInfo[]>([]);
// 已选第三方账套列表
const currentAccountSetList = ref<IAccountSetInfo[]>([]);
// 账套Id列表
const asidList = ref<string[]>([]);
const asNameList = ref<string[]>([]);
const handlechangecurrentList = (val: IAccountSetInfo[]) => {
    asidList.value = [];
    asNameList.value = [];
    currentAccountSetList.value = val;
    if (val.length) {
        for (let i of currentAccountSetList.value) {
            asidList.value.push(i.id);
            asNameList.value.push(i.name);
        }
    }
};
// 账套准则
const accountStandard = ref(-1);
// 是否需要用户选择准则
const showAccountStandardSelect = computed(() => {
    // 每一个选中的账套都是-1时，用户手动选择账套准则
    return currentAccountSetList.value.every((item) => item.accountingStandard === -1);
});
// 验证码图片地址
const vertifyCodePicUrl = ref("");
// 需要验证码的软件
const needVertifyCode = ref(false);
// 需要企业号的软件
const needCorporateAccount = ref(false);
// 需要导入其他模块
const needImportOtherMode = ref(false);

const importOtherModeTips = computed(() => {
    if ([30100, 30101, 30102, 30103, 30131].includes(softWare.value)) {
        return `有多部门、多折旧费用科目、其他不支持的折旧方式时，所有资产数据不支持导入`;
    } else {
        return `有多部门、多折旧费用科目、单卡片多数量、其他不支持的折旧方式时，所有资产数据不支持导入`;
    }
});

// 导账模式 初始 init 导账工具导账 importTool 科目余额表导账 trialBalance
const importMode = ref<ImportModeType>("init");
// 上一个模式
const lastImportMode = ref<ImportModeType>("init");
const importModeList = {
    init: "旧账导入",
    importTool: "导账工具导账",
    trailBalance: "科目余额表导账",
    historyRecord: "导账历史记录",
    statusError: "",
};

// 状态错误信息
const getInitErrorData = () => {
    return {
      count: 0,
      appAsId: "",
      errorList: [],
      asName: "",
    };
};
const errorData = ref<IErrorInfo>(getInitErrorData());
const resetErrorData = () => {
    errorData.value = getInitErrorData();
};
  // 切换导账模式
const handleChangeImportMode = (mode: ImportModeType, params?: IErrorReasonParams | string): void => {
    showImportToolTips.value = false;
    if (params) {
      if (mode === "statusError") {
        const { appAsId, errorMsgList, asName } = params as IErrorReasonParams;
        errorData.value.errorList = errorMsgList;
        errorData.value.appAsId = appAsId;
        errorData.value.asName = asName;

        request({
          url: `${window.importFromOther}/api/AccImport/GetImportErrorCount`,
          method: "get",
          headers: {
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
          },
          params: {
            asid: appAsId,
          },
        }).then((res: any) => {
          errorData.value.count = res.Data;
        });
      }
      if (mode === "historyRecord") {
        isAccountingAgent && (importInfoId.value = params as string);
      }
    }
    importMode.value = mode;
};
// 导账工具显示提示
const showImportToolTips = ref(false);
// 当前第几步
const currentStep = ref<1 | 2 | 3 | 4>(1);
// 保存上一步
const lastStep = ref<1 | 2 | 3 | 4>(1);
// 判断软件支持导账类型
// 切换步数
const handleChangeStep = (stepNum: 1 | 2 | 3 | 4) => {
    if (stepNum === 2) {
        if (softWare.value === -1) {
            ElNotify({ message: "亲，请选择软件厂商~", type: "warning" });
            return;
        }
        if (!gotoOtherImport()) return;
    }
    currentStep.value = stepNum;
};
// 返回创建账套
const returnCreate = () => {
    globalWindowOpenPage(`/Settings/AccountSets${asId ? "" : "1"}`, "创建账套");
    // history.back();
};

// 是否有导入成功的账套
const newAppasid = ref("");
const importSuccess = (appasid: string) => {
    newAppasid.value = appasid;
};
// 返回首页
const returnHome = () => {
    // change2newasid(newAsid.value);
    setTopLocationhref(`/Default/Default?appasid=${newAppasid.value}`);
};

// 查询第三方账套
const searchAccountSets = () => {
    if (!gotoOtherImport()) return;

    if (!userName.value || !password.value) {
        ElNotify({ message: "请输入正确的用户名与密码~", type: "warning" });
        handleChangeStep(2);
        return;
    }

    if (importMode.value === "init" && !corporateAccount.value.length && needCorporateAccount.value) {
        ElNotify({ type: "warning", message: "请输入正确的企业号" });
        return;
    }

    if (importMode.value === "init" && !vertifyCode.value.length && needVertifyCode.value) {
        ElNotify({ type: "warning", message: "请输入正确验证码" });
        return;
    }

    loading.value = true;
    // 登录
    const params = {
        userName: `${userName.value}${needCorporateAccount.value ? "-" + corporateAccount.value : ""}`,
        passWord: password.value,
        imgCode: needVertifyCode.value ? vertifyCode.value : "",
        softwareID: softWare.value,
    };
    request({
        url: `${window.importFromOther}/api/ImportFromYun/Login`,
        method: "post",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
        params,
    })
        .then((res: IResponseModel<IAccountSetData>) => {
            if (res.state === 1000) {
                if (res.data.code === 0) {
                    // 账套列表
                    accountsetsList.value = res.data.data;
                    asidList.value = [];
                    asNameList.value = [];
                    currentAccountSetList.value = [];
                    ElNotify({ message: res.data.message, type: "success" });
                    handleChangeStep(3);
                } else if (res.data.code === 1) {
                    ElNotify({ message: "查询失败!请检查用户名和密码后重新查询哦~", type: "warning" });
                    handleChangeStep(2);
                }
            } else {
                handleChangeStep(2);
                return;
            }
        })
        .finally(() => {
            // 刷新验证码
            // getVertifyCode();
            loading.value = false;
        });
};
// 验证码刷新
const getVertifyCode = () => {
    request({
        url: `${window.importFromOther}/api/ImportFromYun/GetImageStream?softwareID=${softWare.value}`,
        method: "get",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
        responseType: "blob",
    })
        .then((res: any) => {
            const imgBase64 = URL.createObjectURL(new Blob([res], { type: "image/jpeg" }));
            vertifyCodePicUrl.value = imgBase64;
            vertifyCode.value = "";
        })
        .finally(() => {
            loading.value = false;
        });
};

// 确定提交
const handleSubmit = () => {
    if (!currentAccountSetList.value.length) {
        ElNotify({ type: "warning", message: "请选择需要升级的账套" });
        return;
    }
    if (accountStandard.value === -1 && showAccountStandardSelect.value) {
        ElNotify({ type: "warning", message: "请选择会计准则" });
        return;
    }
    if (isAccountingAgent && asId) {
        ElConfirm("账套导入会覆盖本账套数据，您确定继续导入吗？").then((r) => {
            if (r) {
                handleSubmitInner(1);
            }
        });
    } else {
        handleSubmitInner(2);
    }
};

const handleSubmitInner = async (asNew: 1 | 2 = 2) => {
    loading.value = true;
    await handleComplete();
    // 在线导账
    const params = {
        softwareID: softWare.value,
        accountingstandard: accountStandard.value,
        asidList: asidList.value.join(","),
        isInvoice: needImportOtherMode.value ? isInvoice.value : true,
        isFixedAsset: needImportOtherMode.value ? isFixedAsset.value : false,
        isJournal: needImportOtherMode.value ? isJournal.value : true,
        // 默认小规模
        taxType: showTaxType.value ? taxType.value : "1",
        // 导入是否覆盖当前账套，1覆盖，2不覆盖
        asNew,
        softwareUserID: userName.value,
        ...(window.isProSystem ? { serviceId:  getServiceId() } : {}),
    };
    request({
        url: `${window.importFromOther}/api/ImportFromYun/Export?${getUrlSearchParams(params)}`,
        method: "post",
        data: {
            asNameList: asNameList.value,
        },
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    })
        .then((res: IResponseModel<IImportData>) => {
            if (res.state === 1000) {
                if (res.data.code === 0) {
                    asidList.value = [];
                    asNameList.value = [];
                    currentAccountSetList.value = [];
                    importStatusList.value = res.data.data;
                    importMode.value = "init";
                    // 获取最新导账进度
                    getImportProgressModel();
                    handleChangeStep(4);
                } else {
                    ElNotify({ type: "warning", message: res.data.message });
                }
            } else {
                return;
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
        });
};
// 是否有边框
const hasBorder = computed(() => {
    return importMode.value !== "importTool" && importMode.value !== "historyRecord";
});

// 继续导入账套,判断是否已完成
const handleContinueImport = () => {
    lastStep.value ? handleChangeStep(lastStep.value) : handleChangeStep(1);
};
// 强制完成之前的导账
const handleComplete = () => {
    return request({
        url: `${window.importFromOther}/api/ImportFromYun/Complete`,
        method: "POST",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    });
};

// 获取最新导账进度
const getImportProgressModel = (changeStep = false) => {
    request({
        url: `${window.importFromOther}/api/ImportFromYun/GetImportProgressModel`,
        method: "GET",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    })
        .then((res: IResponseModel<IImportData>) => {
            if (res.state === 1000) {
                if (res.data.code === 0) {
                    importMode.value = "init";
                    importStatusList.value = res.data.data;
                } else {
                    importStatusList.value = [];
                }
            } else {
                return;
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            if (changeStep) {
                if (importStatusList.value.length > 0 && importStatusList.value.some((item) => item.status === 0)) {
                    // 有导账未结束的，跳转导账状态页
                    handleChangeStep(4);
                }
            }
        });
};

// 视频地址
const videoURL = {
    init: "130260130",
    importTool: "130260100",
    trailBalance: "130260110",
    historyRecord: "130260130",
    statusError: "130260130",
};

// 二维码高度
const helpBoxTop = {
    init: 100,
    importTool: 20,
    trailBalance: 90,
    historyRecord: 20,
    statusError: 100,
};

// 判断是否从创建账套跳转过来
const isFromCreate = ref(false);

const gotoImportTool = () => {
    importMode.value = "importTool";
    showImportToolTips.value = false;
};

const gotoTrailBalance = () => {
    importMode.value = "trailBalance";
    newSoftWare.value = -1;
};

//匹配去科目余额表导账的软件
const filterTrailBalanceNameList= [
  "T3",
  "T+标准版",
  "T6",
  "易代账",
  "好会计",
  "用友通",
  "管家婆财贸双全",
  "云代帐",
  "航天信息",
]
// 跳转对应导账页
const gotoOtherImport = () => {
    if (importMode.value === "init" && currentSoftWareType.value !== 0) {
        ElNotify({ type: "warning", message: "当前选择软件不支持在线导账" });
         // 导账1.0 filterTrailBalanceNameList涉及版本直接去科目余额表导账  其余的按之前逻辑 0在线导账 1进入导账工具 其他的进入科目余额表导账
        if(filterTrailBalanceNameList.some(item=> softWareName.value.includes(item))){
            importMode.value = "trailBalance";
        } else if (currentSoftWareType.value === 1) {
            importMode.value = "importTool";
            showImportToolTips.value = true;
        } else {
            importMode.value = "trailBalance";
        }
        return false;
    } else {
        return true;
    }
};

watch(
    () => currentStep.value,
    (val, oldVal) => {
        lastStep.value = oldVal;
        if (val === 2 && needVertifyCode.value) {
            getVertifyCode();
        }
        if (val === 3) {
            currentAccountSetList.value = [];
            asidList.value = [];
            asNameList.value = [];
            accountStandard.value = -1;
            vertifyCode.value = "";
        }
    }
);
watch(
    () => importMode.value,
    (val, oldVal) => {
        if (val === "init") {
            newSoftWare.value = softWare.value;
            if (currentStep.value === 1) {
                getSoftware();
            }
            if ((currentStep.value === 2 || currentStep.value === 3) && needVertifyCode.value) {
                getVertifyCode();
            }
        }
        lastImportMode.value = oldVal;
    }
);
watch(
    () => needVertifyCode.value,
    (val) => {
        if (!val) {
            vertifyCode.value = "";
        }
        if (val && (currentStep.value === 2 || currentStep.value === 3)) {
            getVertifyCode();
        }
    }
);

watch(
    () => softWare.value,
    () => {
        newSoftWare.value = softWare.value;
        // 账号密码清空
        userName.value = "";
        password.value = "";
        // 需要验证码

        if ([30302, 30131, 30602, 30603, 30604].includes(softWare.value)) {
            needVertifyCode.value = true;
        } else {
            needVertifyCode.value = false;
        }
        // 需要企业号
        if ([30601, 30602, 30603, 30604].includes(softWare.value)) {
            needCorporateAccount.value = true;
        } else {
            needCorporateAccount.value = false;
            corporateAccount.value = "";
        }
        // 需要导入其他模块选项
        // softWare.value === 30131 亿企代账暂时不加
        if ([10011, 10012, 30100, 30101, 30102, 30103, 30201, 30202].includes(softWare.value)) {
            needImportOtherMode.value = true;
        } else {
            needImportOtherMode.value = false;
            isInvoice.value = false;
            isJournal.value = false;
            isFixedAsset.value = false;
        }

        gotoOtherImport();
    }
);
onMounted(async () => {
    getSoftware();
    // 获取最新导账进度
    getImportProgressModel(true);
    isFromCreate.value = JSON.parse(localStorage.getItem("isFromCreateAccountSet") || "false");
    getHistory();
});
onUnmounted(() => {
    localStorage.removeItem("isFromCreateAccountSet");
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.content {
    .main-content {
        height: calc(100% - 54px);
        &.historyRecordContent {
            overflow: hidden;
        }
    }
}

// 新版旧账导入
.import-box {
    position: relative;
    width: 1200px;
    min-height: 581px;
    background-color: #fff;
    margin: 0 auto;
    &.historyRecord {
        width: 1260px;
        flex: 1;
        min-height: 0;
    }
    &.hasBorder {
        border: 1px solid #e5e5e5;
    }
    .import-box-header {
        width: 1000px;
        height: 75px;
        padding-top: 24px;
        margin: 0 auto;
        box-sizing: border-box;
        border-bottom: 1px solid #e8eaea;
        &.statusList {
            border: 0;
        }
        .step-list {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 530px;
            height: 30px;
            padding: 0;
            margin: 0 auto;
            list-style: none;
            li {
                position: relative;
                padding-left: 38px;
                line-height: 30px;
                color: #666;
                &:nth-child(even) {
                    width: 50px;
                    height: 1px;
                    background-color: #e6e9ed;
                }
                span {
                    position: absolute;
                    display: inline-block;
                    line-height: 24px;
                    text-align: center;
                    border-radius: 24px;
                    color: #fff;
                    &.step-num-bg {
                        top: 0;
                        left: 0;
                        width: 30px;
                        height: 30px;
                        background-color: #f4f5f6;
                        opacity: 0.5;
                    }
                    &.step-num {
                        top: 3px;
                        left: 3px;
                        width: 24px;
                        height: 24px;
                        background-color: #e0e1e6;
                    }
                }
                &.current {
                    color: #333;
                    span {
                        &.step-num-bg {
                            background-color: var(--main-color);
                            opacity: 0.5;
                        }
                        &.step-num {
                            background-color: var(--main-color);
                        }
                    }
                }
            }
        }
        .top-buttons {
            display: flex;
            justify-content: flex-start;
            .button {
                &:first-child {
                    padding: 0 5px;
                }
                &.new-style {
                    border: 1px solid var(--main-color);
                    color: var(--main-color);
                    &:hover {
                        color: #fff;
                    }
                }
            }
        }
        .dialog-info-box {
            color: #fd7400;
            border: 1px solid var(--border-color);
            padding: 5px;
        }
        .dialog-info-text {
            word-break: break-all;
        }
    }
    .import-box-content,
    .trialBalance-import {
        position: relative;
        min-height: 500px;
        padding-top: 30px;
        box-sizing: border-box;
        &.statusList {
            padding: 0;
            .step {
                width: 750px;
                margin-left: 100px;
            }
        }
        .step {
            width: 450px;
            margin: 0 auto;
            .main-center-row {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .row-title {
                    width: 130px;
                    text-align: right;
                }
                .row-content {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    .vertify-code {
                        width: 90px;
                        height: 40px;
                    }
                    .refresh-btn {
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        background: url("@/assets/Settings/icon-refresh.png") no-repeat 0 0;
                        background-size: 20px 20px;
                    }
                }
            }
            .buttons {
                margin-top: 30px;
                .return-btn {
                    border: 1px solid var(--main-color);
                    color: var(--main-color);
                    &:hover {
                        color: #fff;
                    }
                }
            }
            .tips-box {
                width: 376px;
                margin-top: 50px;
                padding: 16px 28px;
                text-align: left;
                background: #fafafa;
                font-size: 12px;
                p {
                    &:first-child {
                        font-weight: 700;
                    }
                }
                .link {
                    font-size: 12px;
                }
            }
        }
        .bottom-tool {
            position: absolute;
            bottom: 0;
            right: 25px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            p {
                display: flex;
                align-items: center;
                height: 20px;
                line-height: 14px;
                padding: 0 10px;
                &:last-child {
                    border-left: 1px solid #ccc;
                }
                .link {
                    font-size: 14px;
                }
            }
        }
    }
    .help-box {
        position: absolute;
        top: 100px;
        right: 40px;
        width: 180px;
        height: 250px;
        .video-guide {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            height: 20px;
            font-size: 12px;
            color: #333;
            line-height: 20px;
            padding: 0;
            margin: 0;
            margin-bottom: 10px;
            .link {
                font-size: 12px;
            }
        }
        .qrcode-box {
            border: 1px solid var(--border-color);
            padding: 10px;
            .qrcode-tip {
                font-size: 14px;
            }
        }
    }

    .import-status-alert {
        position: fixed;
        top: 123px;
        left: 55%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        width: 373px;
        height: 33px;
        padding: 0 10px;
        line-height: 33px;
        background: #fffcf5;
        border: 1px solid #ffbf00;
        font-size: 12px;
        .link {
            font-size: 12px;
            line-height: 33px;
        }
    }
}
.bottom-tips {
    width: 500px;
    margin: 10px 0 10px 500px;
    &.aa-tips {
        margin-left: 375px;
        width: 550px;
    }
    .tips {
        display: flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #333;
        margin: 0;
        img {
            width: 15px;
            height: 15px;
            margin-right: 3px;
        }
        & ~ .tips {
            padding-left: 18px;
            white-space: nowrap;
        }
    }
}
& :deep(.el-tabs) {
    .el-tabs__header {
        .el-tabs__nav {
            padding-left: 0;
        }
        .el-tabs__nav-wrap {
            &::after {
                display: none;
            }
            .el-tabs__item {
                padding: 20px 20px 20px 0;
                & + .el-tabs__item {
                    padding-left: 20px;
                }
            }
        }
    }
    .el-tabs__content {
        margin: 0 auto;
        margin-top: 32px;
        width: 1000px;
        border: 1px solid var(--slot-title-color);
    }
}
.main-right-bottom-link {
    right: 30px;
}
</style>
