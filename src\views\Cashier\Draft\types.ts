export interface IDraftStatusOptions {
    value: number;
    name: string;
}

export interface ITableItem {
    acceptorAccount: string;
    acceptorBank: string;
    acceptorName: string;
    asId: number;
    canTransfer: number;
    discountAmount: string | number | null;
    discountBank: string | number | null;
    discountBankCode: string | number | null;
    discountDate: null | string;
    discountDateStr: string;
    discountPId: null | string | number;
    discountRate: null | string | number;
    discountResult: null | string | number;
    discountStatus: null | string | number;
    discountStatusName: string;
    discountVId: null | string | number;
    discountVoucherGroup: null | string | number;
    draftAmount: number | string;
    draftCreateDate: string;
    draftCreateDateStr: string;
    draftEndDate: string;
    draftEndDateStr: string;
    draftNo: string;
    draftPic: null | number | string;
    draftStatus: number | string;
    draftStatusName: string;
    draftType: number | string;
    draftTypeName: string;
    drawerAccount: string;
    drawerBank: string;
    drawerName: string;
    getMoneyPId: null | number | string;
    getMoneyVId: null | number | string;
    getMoneyVoucherGroup: null | number | string;
    gotMoneyDate: null | number | string;
    gotMoneyDateStr: string;
    id: string;
    ieType: number;
    ietypeVue: null | number | string;
    isCreateDiscountVoucher: boolean;
    isCreateDraftVoucher: boolean;
    isCreateGetMoneyVoucher: boolean;
    isCreateVoucher: boolean;
    isGotMoney: boolean;
    isTransfered: boolean;
    memo: string;
    pId: null | number | string;
    receiverAccount: string;
    receiverBank: string;
    receiverName: string;
    transferee: null | number | string;
    transfereeAmount: null | number | string;
    transfereeDate: null | number | string;
    transfereeDateStr: string;
    vId: null | number | string;
    voucherGroup: null | number | string;
    focus?: boolean;
    ieType1?: number;
    attachsCount: number;
    attachFiles: string;
}
export interface IEditDraftItem {
    id: string;
    draftCreateDate: string;
    draftEndDate: string;
    draftStatus: string | number;
    draftType: string | number;
    draftNo: string;
    draftAmount: string | number;
    canTransfer: string | number;
    drawerName: string;
    drawerAccount: string;
    drawerBank: string;
    receiverName: string;
    receiverAccount: string;
    receiverBank: string;
    acceptorName: string;
    acceptorAccount: string;
    acceptorBank: string;
    memo: string;
    draftPic: string;
    attachsCount: number;
    attachFiles: string;
}
export interface IETypeOption {
    subkey: number;
    value2: string;
}
export interface IJDStatusRes {
    realnameInfo: string;
    realnameStatus: number;
    regStatus: number;
}
export interface IORCRes {
    billNo: string;
    billNoErrCode: string;
    billNoErrDesc: string;
    drawerAccount: string;
    drawerBankName: string;
    drawerName: string;
    issueDate: string;
    billAmt: number;
    accepterAccount: string;
    accepterBank: string;
    accepterBankName: string;
    accepterName: string;
    maturityDate: string;
    payeeAccount: string;
    payeeBankName: string;
    payeeName: string;
    responseCode: string;
    responseDesc: string;
}
export interface IFSearchItem {  
    draftNo: string;
    drawerName: string;
    acceptorName: string;
    draftStatus: number[];  
    draftType: number;
}
