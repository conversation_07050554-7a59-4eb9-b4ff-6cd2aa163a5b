<template>
    <div class="init-fixedassets">
        <div class="main-title" v-if="pageInfo === 'intangibles'">
            <span class="main-back" @click="backMain">&lt;&nbsp;返回</span>
            <span>无形资产和长期待摊费用初始化</span>
        </div>
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <div class="row" style="text-align: left">
                    <span class="txt"
                        >{{ iaPeriodInfo && commissioningDate !== iaPeriodInfo ? "固定" : "" }}资产启用期间：{{ commissioningDate }}</span
                    ><br />
                    <span class="txt" v-show="iaPeriodInfo && commissioningDate !== iaPeriodInfo"
                        >无形资产启用期间：{{ iaPeriodInfo }}</span
                    >
                </div>
                <ErpRefreshButton></ErpRefreshButton>
                <span v-if="props.finishflag === '0'" @click="() => (dialogVisible = true)" class="show-detail ml-10">
                    <i></i>
                    初始化不平，点击查看原因
                </span>
            </div>
            <div class="main-tool-right">
                <div
                    v-if="closeButton"
                    class="button mr-10"
                    :style="isErp ? '' : 'width: 96px'"
                    v-permission="['fixedassets-init-canedit']"
                    @click="closeIaShow ? '' : closeIntangibleAsset()"
                >
                    关闭无形资产
                </div>
                <div
                    v-if="!iaPeriodInfo || (iaPeriodInfo && commissioningDate !== iaPeriodInfo && pageInfo === 'fixAsset')"
                    class="button mr-10"
                    :style="isErp ? '' : 'width: 96px'"
                    v-permission="['fixedassets-init-canedit']"
                    @click="isStartIa ? checkIntangibleAsset() : startIntangibleAsset()"
                >
                    {{ isStartIa ? "无形资产初始化" : "启用无形资产" }}
                </div>
                <el-checkbox class="mr-20" v-model="showInfo" label="显示所有信息" @change="handleSearch"></el-checkbox>
                <div
                    id="add1"
                    class="button mr-10"
                    :class="addAndDelShow ? 'disabled' : ''"
                    v-permission="['fixedassets-init-canedit']"
                    :title="addAndDelShow ? '当期已计算折旧,不能增加资产' : ''"
                    @click="addAndDelShow ? '' : increaseAssets('1')"
                >
                    增加资产
                </div>
                <div
                    id="copy1"
                    class="button mr-10"
                    :class="addAndDelShow ? 'disabled' : ''"
                    v-permission="['fixedassets-init-canedit']"
                    :title="addAndDelShow ? '当期已计算折旧,不能复制定资产' : ''"
                    @click="addAndDelShow ? '' : copyAssets('1')"
                >
                    复制资产
                </div>
                <div
                    id="importFixed"
                    class="button mr-10"
                    :class="addAndDelShow ? 'disabled' : ''"
                    v-permission="['fixedassets-init-canimport']"
                    :title="addAndDelShow ? '当期已计算折旧,不能增加资产' : ''"
                    @click="addAndDelShow ? '' : handleImport()"
                >
                    导入
                </div>
                <Dropdown :btnTxt="'导出'" class="mr-10" :downlistWidth="106" v-permission="['fixedassets-init-canexport']">
                    <li @click="handleExport" style="padding: 0px 6px">导出</li>
                    <li @click="handleExportForImport" style="padding: 0px 6px">按导入模板导出</li>
                </Dropdown>
                <Dropdown :btnTxt="'打印'" :class="'mr-10'" :downlistWidth="isErp ? 76 : 86" v-permission="['fixedassets-init-canprint']">
                    <li @click="printDialogVisible = true">资产列表</li>
                    <li @click="PrintList">资产卡片</li>
                    <li @click="PrintCard">资产标签</li>
                </Dropdown>
                <Dropdown
                    :btnTxt="'更多'"
                    :class="'downlist mr-10'"
                    :downlistWidth="
                        isErp ? 76 : pageInfo === 'intangibles' || !checkPermission(['fixedassets-init-cansyncinitial']) ? 86 : 116
                    "
                    v-permission="['fixedassets-init-canprint']"
                >
                    <li
                        :class="addAndDelShow ? 'disabled' : ''"
                        v-permission="['fixedassets-init-candelete']"
                        :title="addAndDelShow ? '当期已计算折旧,不能删除资产' : ''"
                        @click="addAndDelShow ? '' : Delete('1')"
                    >
                        删除资产
                    </li>
                    <li
                        style="position: relative"
                        v-if="(!iaPeriodInfo || commissioningDate === iaPeriodInfo) && checkPermission(['fixedassets-init-cansyncinitial'])"
                        :title="addAndDelShow ? '当期已计算折旧,不能同步到科目期初' : ''"
                    >
                        <div
                            style="width: 116px"
                            @click="addAndDelShow ? '' : postSyncInitialPreCheck()"
                            :class="addAndDelShow ? 'disabled' : ''"
                        >
                            同步到科目期初
                        </div>
                        <el-tooltip effect="light" placement="top-start">
                            <template #content
                                >将卡片期初中资产原值、累计折旧、减<br />值准备的金额同步到科目期初的资产、<br />累计折旧、资产减值准备中</template
                            >
                            <img src="@/assets/Settings/question.png" class="question-img" />
                        </el-tooltip>
                    </li>
                </Dropdown>
                <RefreshButton ref="refreshButton"></RefreshButton>
            </div>
        </div>
        <div class="main-center">
            <Table
                ref="initfixedassetsRef"
                :data="tableData"
                :loading="loading"
                :columns="columns"
                :pageIsShow="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :current-page="paginationData.currentPage"
                :selectable="setSelectable"
                :scrollbar-show="true"
                @row-click="selectToggle"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @selection-change="selectCard"
                @refresh="handleRerefresh"
                @cell-drop="cellDrop"
                :tableName="setModule"
                @scroll="handleScroll"
            >
                <template #fa_num>
                    <el-table-column
                        :show-overflow-tooltip="false"
                        prop="fa_num"
                        label="" 
                        :width="getColumnWidth(setModule, 'fa_num')" 
                        :min-width="150"
                        :class-name="'linkColor ' + getSlotClassName('fa_num', columns)"
                        :fixed="getSlotIsFreeze('fa_num', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>资产编号</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faNu', $event)">
                                    </div>
                                    <TableHeaderFilter
                                        :prop="'fa_num'"
                                        :isFilter="!!filterSearchInfo.fa_num"
                                        :hasSearchVal="filterSearchInfo.fa_num" 
                                        @filterSearch="filterSearch"
                                    ></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <a
                                v-if="scope.row.fa_num"
                                :title="scope.row.fa_num"
                                class="link"
                                @click="showDetail(scope.row.fa_id)"
                            >
                                <el-tooltip
                                    class="box-item"
                                    effect="light"
                                    placement="top"
                                >
                                    <template #content>
                                        <span>
                                            <span id="fa_num">{{scope.row.fa_num}}</span>
                                            <el-icon class="lineSn-copy" style="color: var(--main-color);" @click="handleCopy"><DocumentCopy /></el-icon>
                                        </span>
                                    </template>
                                    <span class="text-overflow-ellipsis">{{ scope.row.fa_num }}</span>
                                </el-tooltip>  
                            </a>
                            <span v-else>{{ "" }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #fa_type>
                    <el-table-column
                        prop="fa_type"
                        label="" 
                        :width="getColumnWidth(setModule, 'fa_type')"
                        :min-width="145"
                        :class-name="getSlotClassName('fa_type', columns)"
                        :fixed="getSlotIsFreeze('fa_type', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>资产类别</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faType', $event)">
                                    </div>
                                    <TableHeaderFilter
                                        :prop="'fa_type'" 
                                        :isSelect="true"
                                        :selectedList="initSelect.fa_type"
                                        :option="faTypeOptions"
                                        :hasSelectList="filterSearchInfo.fa_type"
                                        :isFilter="isFilterMultile(filterSearchInfo.fa_type, faTypeOptions)"
                                        @filterSearch="filterSearch"
                                    >
                                    </TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span>{{ scope.row.fa_type }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #fa_name>
                    <el-table-column
                        prop="fa_name"
                        label="" 
                        :width="getColumnWidth(setModule, 'fa_name')" 
                        :min-width="145"
                        :class-name="getSlotClassName('fa_name', columns)"
                        :fixed="getSlotIsFreeze('fa_name', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>资产名称</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faName', $event)">
                                    </div>
                                    <TableHeaderFilter
                                        :prop="'fa_name'"
                                        :isFilter="!!filterSearchInfo.fa_name"
                                        :hasSearchVal="filterSearchInfo.fa_name"
                                        @filterSearch="filterSearch"
                                    ></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.fa_name }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #fa_model>
                    <el-table-column
                        prop="fa_model"
                        label="" 
                        :width="getColumnWidth(setModule, 'fa_model')"
                        :min-width="88"
                        :class-name="getSlotClassName('fa_model', columns)"
                        :fixed="getSlotIsFreeze('fa_model', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>规格型号</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'fa_model'"
                                        :isFilter="!!filterSearchInfo.fa_model"
                                        :hasSearchVal="filterSearchInfo.fa_model"
                                        @filterSearch="filterSearch"
                                    ></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.fa_model }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #started_date>
                    <el-table-column
                        prop="started_date"
                        label="" 
                        :width="getColumnWidth(setModule, 'started_date')" 
                        :min-width="118"
                        :class-name="getSlotClassName('started_date', columns)"
                        :fixed="getSlotIsFreeze('started_date', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>开始使用日期</span>
                                <div class="header-operate-rt">
                                    <div class="header-caret" @click="getSortCommon('faStartDate', $event)">
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.started_date }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #department>
                    <el-table-column
                        prop="department"
                        label="" 
                        :width="getColumnWidth(setModule, 'department')" 
                        :min-width="80"
                        :class-name="getSlotClassName('department', columns)"
                        :fixed="getSlotIsFreeze('department', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>使用部门</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'department'" 
                                        :isSelect="true"
                                        :selectedList="initSelect.department"
                                        :option="departmentOptions"
                                        :hasSelectList="filterSearchInfo.department"
                                        :isFilter="isFilterMultile(filterSearchInfo.department, departmentOptions)"
                                        @filterSearch="filterSearch"
                                    >
                                    </TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span>{{ scope.row.department }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #status>
                    <el-table-column
                        prop="status"
                        label="" 
                        :width="getColumnWidth(setModule, 'status')"
                        :min-width="80"
                        :class-name="getSlotClassName('status', columns)"
                        :fixed="getSlotIsFreeze('status', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>使用状况</span>
                            </div>
                        </template>
                        <template #default="scope">
                            <span>{{ scope.row.status }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #note>
                    <el-table-column
                        prop="note"
                        label="" 
                        :width="getColumnWidth(setModule, 'note')"
                        :min-width="70"
                        :class-name="getSlotClassName('note', columns)"
                        :fixed="getSlotIsFreeze('note', columns)"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>备注</span>
                                <div class="header-operate-rt">
                                    <TableHeaderFilter
                                        :prop="'note'"
                                        :isFilter="!!filterSearchInfo.fa_model"
                                        :hasSearchVal="filterSearchInfo.fa_model"
                                        @filterSearch="filterSearch"
                                    ></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <span> {{ scope.row.note }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #operation>
                    <el-table-column 
                        label="" 
                        min-width="92" 
                        align="left" 
                        header-align="left"
                        fixed="right"
                        :resizable="false"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span>操作</span>
                                <div class="header-operate-rt">
                                    <div class="set-icon" @click="openColSet"></div>
                                </div>
                            </div>
                        </template>     
                        <template #default="scope">
                            <a
                                class="link"
                                v-if="scope.row.fa_num && checkPermission(['fixedassets-init-canedit'])"
                                @click="handleEdit(scope.row.fa_id)"
                            >
                                编辑
                            </a>
                            <a v-if="scope.row.fa_num" class="link" @click.stop="handleGetAttachFiles(scope.row)"
                                >附件{{ scope.row.attachsCount > 0 ? `(${scope.row.attachsCount})` : "" }}</a
                            >
                        </template>
                    </el-table-column>
                </template>
            </Table>
            <!-- 不平图片 -->
            <div v-if="props.finishflag === '0'" class="imbalance-icon"></div>
        </div>
        <div class="tip">亲，资产初始化数据要与期初的资产数据一致哦！</div>
    </div>
    <el-dialog v-if="finishflag === '0'" v-model="dialogVisible" title="资产初始化不平" center :width="776" class="dialogDrag">
        <div class="dialog-cont" v-dialogDrag>
            <div class="dia-date" style="padding-bottom: 10px">
                <span class="txt">账套启用期间：{{ currentPeriodTxt }}</span>
                <span class="txt ml-10">{{ iaPid && iaPid !== commissioningPid ? "固定" : "" }}资产启用期间：{{ commissioningDate }}</span>
            </div>
            <div class="dia-table">
                <el-table
                    :data="iaPid && iaPid !== commissioningPid ? faInitialData : initialData"
                    :span-method="faObjectSpanMethod"
                    border
                >
                    <el-table-column label="资产类别" align="center" prop="v1_name" :min-width="140" :resizable="false"></el-table-column>
                    <el-table-column label="资产初始化卡片" align="right" header-align="right" :min-width="150" :resizable="false">
                        <template #default="scope">
                            <span>{{ scope.$index % 2 ? scope.row.v1_value : scope.row.v1_name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="iaPid !== commissioningPid && iaInitialData.length > 0 ? commissioningDate + '科目期初' : '科目期初'"
                        align="right"
                        header-align="right"
                        :min-width="174"
                        :resizable="false"
                    >
                        <template #default="scope">
                            <div
                                v-show="scope.$index % 2"
                                class="unBalanceIcon"
                                :class="unbalanceClass(scope.row.v1_value, scope.row.v2_value)"
                            ></div>
                            <span>{{ scope.$index % 2 ? formatMoneyWithZero(scope.row.v2_value) : scope.row.v2_name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="差额" align="center" :min-width="120" :resizable="false">
                        <template #default="scope">
                            <span>{{ imbalanceCount(scope.row.v1_value, scope.row.v2_value) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" align="center" :min-width="150" :resizable="false">
                        <template #default="scope">
                            <div v-if="imbalanceCount(scope.row.v1_value, scope.row.v2_value) === '0.00'">平</div>
                            <div v-else class="unbalance">
                                不平
                                <div class="unbalance-block">
                                    <el-popover
                                        placement="bottom"
                                        :width="200"
                                        trigger="hover"
                                        :content="'可点击对应卡片或科目期初余额联查，核对并修改哦'"
                                    >
                                        <template #reference><img src="@/assets/Icons/warn-red.png" /></template>
                                    </el-popover>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <template v-if="iaPid && iaPid !== commissioningPid && iaInitialData.length > 0">
                <div class="dia-date" style="padding: 10px 0">
                    <span class="txt">账套启用期间：{{ currentPeriodTxt }}</span>
                    <span class="txt ml-10"
                        >无形资产启用期间：{{ iaPid && iaPid !== commissioningPid ? iaPeriodInfo : commissioningDate }}</span
                    >
                </div>
                <div class="dia-table">
                    <el-table :data="iaInitialData" :span-method="iaObjectSpanMethod" border>
                        <el-table-column label="资产类别" align="center" prop="v1_name" :min-width="140" :resizable="false"></el-table-column>
                        <el-table-column label="资产初始化卡片" align="right" header-align="right" :min-width="150" :resizable="false">
                            <template #default="scope">
                                <span>{{ scope.$index % 2 ? scope.row.v1_value : scope.row.v1_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="iaPeriodInfo + '科目期初'" align="right" header-align="right" :min-width="174" :resizable="false">
                            <template #default="scope">
                                <div
                                    v-show="scope.$index % 2"
                                    class="unBalanceIcon"
                                    :class="unbalanceClass(scope.row.v1_value, scope.row.v2_value)"
                                ></div>
                                <span>{{ scope.$index % 2 ? formatMoneyWithZero(scope.row.v2_value) : scope.row.v2_name }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="差额" align="center" :min-width="120" :resizable="false">
                            <template #default="scope">
                                <span>{{ imbalanceCount(scope.row.v1_value, scope.row.v2_value) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" align="center" :min-width="150" :resizable="false">
                            <template #default="scope">
                                <div v-if="imbalanceCount(scope.row.v1_value, scope.row.v2_value) === '0.00'">平</div>
                                <div v-else class="unbalance">
                                    不平
                                    <div class="unbalance-block">
                                        <el-popover
                                            placement="bottom"
                                            :width="200"
                                            trigger="hover"
                                            :content="'可点击对应卡片或科目期初余额联查，核对并修改哦'"
                                        >
                                            <template #reference><img src="@/assets/Icons/warn-red.png" /></template>
                                        </el-popover>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
            <div class="dia-tips">
                <img src="@/assets/Settings/warn.png" style="width: 14px; height: 14px" />
                <span v-if="[1, 4, 5, 6, 7].includes(accountingStandard)" style="color: #666666; font-size: 12px">
                    亲，您在“资产初始化”录入的资产原值、累计折旧合计数必须要与当期总账中的“资产”、“累计折旧”科目的期初相等，否则系统会显示不平，您也不能进行计提折旧操作哟！
                </span>
                <span style="color: #666666; font-size: 12px" v-else>
                    亲，您在“资产初始化”录入的资产原值、累计折旧合计数必须要与当期总账中的“资产”、“累计折旧”、“资产减值准备”
                    科目的期初相等，否则系统会显示不平，您也不能进行计提折旧操作哟！
                </span>
            </div>
        </div>
    </el-dialog>
    <el-dialog class="custom-confirm dialogDrag" v-model="batchCopy.visible" title="复制资产" width="420">
        <div class="batchDialog" v-dialogDrag>
            <div class="copyDialog-content mt-20">
                复制数量：<el-input v-model="batchCopy.value" style="width: 200px" @input="restrictInput('copy')"></el-input>
            </div>
            <div class="batchDialog-warning">所选的{{ batchCopy.ids.length }}个资产将全部进行复制</div>
            <div class="buttons borderTop">
                <a class="button solid-button mr-10" @click="CopyDataInner()">确定</a>
                <a class="button" @click="batchCopy.visible = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <UploadFileDialog :readonly="addAndDelShow" ref="uploadFileDialogRef" @save="handleSaveAttachFiles" />
    <!-- 列设置 -->
    <ColumnSet
        ref="columnSetRef" 
        v-model="columnSetShow"
        :data="fixedassetsListColumnsSet"
        :allColumns="allColumns"
        :setModuleType="module"
        @saveColumnSet="saveColumnSet"
    >
    </ColumnSet>
    <FAPrint
        v-model:printDialogShow="printDialogVisible"
        :title="'资产初始化打印'"
        modalClass=""
        :printData="printInfo"
        :dirShow="false"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getPrintParams())"
    ></FAPrint>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import FAPrint from "@/components/PrintDialog/index.vue";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney, formatMoneyWithZero } from "@/util/format";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import { getUrlSearchParams, globalExport, globalFormPost,globalPostPrint } from "@/util/url";
import { ref, watch, computed, nextTick, reactive } from "vue";
import { ElConfirm } from "@/util/confirm";
import type { 
    IAssetDetail, 
    IFixedAssetsType,
    ISelectStrItem,
    IFSearchItem,
} from "../types";
import { batchData } from "../types";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { getAssetsAttachFiles, saveAttachFileFn, setColumns, isKeyOfIFSearchItem } from "../utils";
import usePrint from "@/hooks/usePrint";
import ColumnSet from "@/components/ColumnSet/index.vue";
import type { IColItem } from "@/components/ColumnSet/utils";
import { 
    getSlotIsFreeze, 
    getSlotClassName, 
    getShowColumn, 
    getColumnWidth,
    alterArrayPos, 
    getColumnListApi 
} from "@/components/ColumnSet/utils";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { isFilterMultile } from "@/components/TableHeaderFilter/utils";
import { copyText } from "@/views/Cashier/BankAndCompany/utils";

const props = defineProps<{
    commissioningDate: string;
    commissioningPid: number;
    currentPid: number;
    currentPeriodInfo: string;
    iaPeriodInfo: string;
    iaPid: number;
    status: number;
    finishflag: string;
    initialData: Array<any>;
    pageInfo: string;
}>();
const emits = defineEmits([
    "handleEdit",
    "increaseAssets",
    "CheckIfFinish",
    "showDetail",
    "handleImport",
    "getChangeData",
    "startIntangibleAsset",
    "toIntangibles",
    "changeCancle",
    "getPeriodsApi",
]);
const filterSearchInfo:IFSearchItem = reactive({
    fa_num: "",
    fa_name: "",
    fa_model: "",
    note: "",
    fa_type: [] as number[],
    department: [] as number[],
    status: 1,
});

const isStartIa = computed(() => props.iaPeriodInfo && props.iaPid);
const closeIaShow = ref(false);
const closeButton = computed(() => {
    return (
        ![6, 7].includes(accountingStandard.value) &&
        props.iaPeriodInfo &&
        !closeIaShow.value &&
        ((props.pageInfo === "fixAsset" && props.commissioningDate === props.iaPeriodInfo) || props.pageInfo === "intangibles")
    );
});

function checkHasIaAssets() {
    request({
        url: "/api/FixedAssets/CheckHasIaAssets",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            closeIaShow.value = res.data;
        }
    });
}
checkHasIaAssets();

const faTypeOptions = ref<Array<Option>>([]);
const faTypeList = ref<ISelectStrItem[]>([]);
const initSelect = reactive({
    fa_type: [] as number[],
    department: [] as number[],
})
//固定资产时间和无形资产时间不一致
function getFaInitType() {
    let initType = "all";
    if (props.pageInfo === "intangibles") {
        initType = "intangibles";
    } else {
        initType = props.commissioningPid !== props.iaPid ? "fixAsset" : "all";
    }
    return initType;
}
function getFaTypeApi() {
    request({
        url: `/api/FixedAssetsType/List`,
        method: "get",
    }).then((res: IResponseModel<IFixedAssetsType[]>) => {
        if (res.state == 1000) {
            let initType = getFaInitType();
            faTypeList.value = res.data.reduce((prev: ISelectStrItem[], item: IFixedAssetsType) => {
                switch (initType) {
                    case "fixAsset":
                        if (item.fa_property === 0) {
                            prev.push({
                                value: item.typeId + "",
                                label: item.typeNum + "-" + item.typeName,
                            });
                        }
                        break;
                    case "intangibles":
                        if ([1, 2].includes(item.fa_property)) {
                            prev.push({
                                value: item.typeId + "",
                                label: item.typeNum + "-" + item.typeName,
                            });
                        }
                        break;
                    default:
                        prev.push({
                            value: item.typeId + "",
                            label: item.typeNum + "-" + item.typeName,
                        });
                        break;
                }
                return prev;
            }, []);

            faTypeOptions.value = faTypeList.value.map((i) => new Option(Number(i.value), i.label));
            filterSearchInfo.fa_type = faTypeOptions.value.map((item) => item.id);
            initSelect.fa_type = filterSearchInfo.fa_type;
        }
    });
}
const departmentOptions = ref<Array<Option>>([]);
function getDepartmentOptions() {
    let departmentList = useAssistingAccountingStore().departmentList;
    departmentOptions.value = departmentList.map((i) => new Option(Number(i.aaeid), i.aaname));
    if (initEntry.value) {
        filterSearchInfo.department = departmentOptions.value.map((item) => item.id);
        initSelect.department = filterSearchInfo.department;
    }
}

function closeIntangibleAsset() {
    ElConfirm("是否关闭无形资产，关闭后资产模块将禁用无形资产所有功能,请谨慎选择").then((r: boolean) => {
        if (r) {
            request({
                url: "/api/FAPeriod/DisableIAStartPeriod",
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    closeIaShow.value = true;
                    emits("getPeriodsApi", true);
                    ElNotify({
                        type: "success",
                        message: "无形资产已关闭",
                    });
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
}

function startIntangibleAsset() {
    emits("startIntangibleAsset");
}
const accountPeriodStore = useAccountPeriodStore();
const currentPeriodTxt = computed(() => {
    const currentPeriodStartDate = accountPeriodStore.periodList.slice().sort((p1, p2) => p1.pid - p2.pid)[0]?.startDate;
    if (currentPeriodStartDate) {
        const date = new Date(currentPeriodStartDate);
        return `${date.getFullYear()}年${date.getMonth() + 1}月`;
    } else {
        return "";
    }
});

const showInfo = ref(false);
const addAndDelShow = ref(false);
const loading = ref(false);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination("Init", false);
const isErp = ref(window.isErp);
const accountingStandard = ref<number>(useAccountSetStoreHook().accountSet!.accountingStandard);
const refreshButton = ref<InstanceType<typeof RefreshButton>>();
// 资产不平弹窗
const dialogVisible = ref<boolean>(false);

function backMain() {
    emits("changeCancle");
}
const initfixedassetsRef = ref();

//排序
const sortList:{  
    [key: string]: number;  
} = {
    default: 0,
    faNu: 1,
    faType: 2,
    faName: 3,
    faStartDate: 4,
}
let sortNum = ref(0);
let sortOrder = ref(-1); //默认； 0升序； 1降序
const sortField = ref(0); //申请排序的是哪个字段
function getSortCommon(name: string, event: any) {
    if (sortField.value !== 0 && sortField.value !== sortList[name]) {
        sortNum.value = 0;
        let sortItems = document.querySelectorAll(".header-caret");
        for(let i =0; i <sortItems.length; i++) {
            sortItems[i].classList.remove("ascending");
            sortItems[i].classList.remove("descending");
        }
    }
    
    sortNum.value++;
    sortField.value = sortList[name];
    if (sortNum.value === 1) {
        event.target.classList.remove("ascending");
        event.target.classList.add("descending");
        sortOrder.value = 1;
    } else if(sortNum.value === 2) {
        event.target.classList.remove("descending");
        event.target.classList.add("ascending");
        sortOrder.value = 0;
    } else {
        sortNum.value = 0;
        event.target.classList.remove("ascending");
        event.target.classList.remove("descending");
        sortOrder.value = -1;
        sortField.value = 0;
    }
    getTableData();
}

function selectToggle(row: any, column: any, event: any) {
    if (!setSelectable(row)) return;
    if (["link el-tooltip__trigger el-tooltip__trigger", "link"].includes(event.target.className)) return;
    let selected = selectCardId.value.findIndex((item: any) => item.fa_num === row.fa_num) >= 0 ? true : false;
    initfixedassetsRef.value?.getTable().toggleRowSelection(row, !selected);
}

function setSelectable(row: IAssetDetail): boolean {
    if (!row.fa_num) {
        return false;
    }
    return true;
}

const faInitialData = computed(() => {
    return props.initialData.filter((item) => ["固定资产", "累计折旧", "固定资产减值准备"].includes(item.v1_name));
});
const iaInitialData = computed(() => {
    return props.initialData.filter((item) =>
        ["无形资产净值", "无形资产", "累计摊销", "长期待摊费用净值", "待摊费用净值"].includes(item.v1_name)
    );
});
const faObjectSpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    if (columnIndex === 0) {
        let rowspan = 1;
        if (row.v1_name === "固定资产" && rowIndex === 0) {
            rowspan = props.initialData.reduce((prev, cur) => {
                if (["固定资产", "累计折旧", "固定资产减值准备"].includes(cur.v1_name)) {
                    prev++;
                }
                return prev;
            }, 0);
            return {
                rowspan,
                colspan: 1,
            };
        } else if (
            ["无形资产净值", "无形资产"].includes(row.v1_name) &&
            rowIndex === props.initialData.findIndex((item) => ["无形资产净值", "无形资产"].includes(item.v1_name))
        ) {
            rowspan = props.initialData.reduce((prev, cur) => {
                if (["无形资产", "累计摊销", "无形资产减值准备", "无形资产净值"].includes(cur.v1_name)) {
                    prev++;
                }
                return prev;
            }, 0);
            return {
                rowspan,
                colspan: 1,
            };
        } else if (
            ["长期待摊费用净值", "待摊费用净值"].includes(row.v1_name) &&
            rowIndex === props.initialData.findIndex((item) => ["长期待摊费用净值", "待摊费用净值"].includes(item.v1_name))
        ) {
            rowspan = props.initialData.reduce((prev, cur) => {
                if (["长期待摊费用净值", "待摊费用净值"].includes(cur.v1_name)) {
                    prev++;
                }
                return prev;
            }, 0);
            return {
                rowspan,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    } else if (columnIndex === 3) {
        if (rowIndex % 2 === 0) {
            return {
                rowspan: 2,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    } else if (columnIndex === 4) {
        if (rowIndex % 2 === 0) {
            return {
                rowspan: 2,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
};
const iaObjectSpanMethod = ({ row, column, rowIndex, columnIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    if (columnIndex === 0) {
        let rowspan = 1;
        if (
            ["无形资产净值", "无形资产"].includes(row.v1_name) &&
            rowIndex === iaInitialData.value.findIndex((item) => ["无形资产净值", "无形资产"].includes(item.v1_name))
        ) {
            rowspan = iaInitialData.value.reduce((prev, cur) => {
                if (["无形资产", "累计摊销", "无形资产减值准备", "无形资产净值"].includes(cur.v1_name)) {
                    prev++;
                }
                return prev;
            }, 0);
            return {
                rowspan,
                colspan: 1,
            };
        } else if (
            ["长期待摊费用净值", "待摊费用净值"].includes(row.v1_name) &&
            rowIndex === iaInitialData.value.findIndex((item) => ["长期待摊费用净值", "待摊费用净值"].includes(item.v1_name))
        ) {
            rowspan = iaInitialData.value.reduce((prev, cur) => {
                if (["长期待摊费用净值", "待摊费用净值"].includes(cur.v1_name)) {
                    prev++;
                }
                return prev;
            }, 0);
            return {
                rowspan,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    } else if (columnIndex === 3) {
        if (rowIndex % 2 === 0) {
            return {
                rowspan: 2,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    } else if (columnIndex === 4) {
        if (rowIndex % 2 === 0) {
            return {
                rowspan: 2,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
};

function handleExport() {
    const params = {
        period: props.commissioningPid,
        datatype: 1,
        fa_property: props.pageInfo === "intangibles" ? "1,2" : "0",
        fa_num: filterSearchInfo.fa_num,
        fa_type: getSelectVal(filterSearchInfo.fa_type, faTypeOptions.value),
        fa_name: filterSearchInfo.fa_name,
        fa_model: filterSearchInfo.fa_model,
        department: getSelectVal(filterSearchInfo.department, departmentOptions.value),
        note: filterSearchInfo.note,
        status: filterSearchInfo.status,
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        sortField: sortField.value,
        sortOrder: sortOrder.value
    };
    const url = `/api/FixedAssets/Export?`;
    globalExport(url + getUrlSearchParams(params));
}

function handleExportForImport() {
    const params = {
        period: props.commissioningPid,
        datatype: 1,
        fa_property: props.pageInfo === "intangibles" ? "1,2" : "0",
        fa_num: filterSearchInfo.fa_num,
        fa_type: getSelectVal(filterSearchInfo.fa_type, faTypeOptions.value),
        fa_name: filterSearchInfo.fa_name,
        fa_model: filterSearchInfo.fa_model,
        department: getSelectVal(filterSearchInfo.department, departmentOptions.value),
        note: filterSearchInfo.note,
        status: filterSearchInfo.status,
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        sortField: sortField.value,
        sortOrder: sortOrder.value
    };
    const url = `/api/FixedAssets/ExportForImport?`;
    globalExport(url + getUrlSearchParams(params));
}

function handleSearch() {
    getTableData();
}

const columns = ref<Array<IColumnProps>>([]);
const fixedassetsListColumnsSet = ref<Array<IColumnProps>>([]);
const tableData = ref<IAssetDetail[]>([]);
function getSelectVal(data: number[], option: Option[]) { 
    return data.length === option.length ? "" : data.join();
}
const initEntry = ref(true);
const getTableData = (isRefreshIaButton?:boolean) => {
    getColumnSetList();
    if(isRefreshIaButton && !closeIaShow.value){
        checkHasIaAssets();
    }
    getDepartmentOptions();
    if (initEntry.value) {
        getFaTypeApi();
    }
    EnableAddAndDel();
    let dataUrl = `/api/FixedAssets/PagingList`;
    const params = {
        period: 0,
        datatype: 1,
        fa_property: "",
        fa_num: filterSearchInfo.fa_num,
        fa_type: getSelectVal(filterSearchInfo.fa_type, faTypeOptions.value),
        fa_name: filterSearchInfo.fa_name,
        fa_model: filterSearchInfo.fa_model,
        department: getSelectVal(filterSearchInfo.department, departmentOptions.value),
        note: filterSearchInfo.note,
        status: filterSearchInfo.status,
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        sortField: sortField.value,
        sortOrder: sortOrder.value
    };
    if (props.pageInfo === "fixAsset") {
        params.period = props.commissioningPid;
        params.fa_property = `${ props.commissioningPid === props.iaPid ? "" : "0"}`;
    } else {
        params.period = props.iaPid;
        params.fa_property = "1,2";
    }
    loading.value = true;
    request({
        url: dataUrl,
        method: "get",
        params,
    })
        .then((res: IResponseModel<{ data: IAssetDetail[]; count: number }>) => {
            if (res.data.data.length === 0 && paginationData.currentPage > 1) {
                return paginationData.currentPage--;
            }
            ColumnsQuery();
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
            emits("CheckIfFinish");
        })
        .finally(() => {
            loading.value = false;
        });
};
function checkIntangibleAsset() {
    emits("toIntangibles");
    getTableData();
}
// 同步科目期初检查
const postSyncInitialPreCheck = () => {
    request({
        url: `/api/FixedAssets/SyncInitialPreCheck?pid=${props.commissioningPid}`,
        method: "post",
    }).then((res: IResponseModel<object>) => {
        if (res.state === 1000) {
            if (res.subState === 0) {
                postSyncInitial();
            }
        } else if (res.state === 2000) {
            if (res.subState === 1) {
                ElConfirm(res.msg);
            } else if (res.subState === 3) {
                ElConfirm(
                    res.msg + '<br><div style="color: red;font-size: 12px;margin-top: 15px;">此操作不可回退，请谨慎操作！</div>',
                    false,
                    () => {},
                    "提示",
                    { confirmButtonText: "继续同步", cancelButtonText: "取消" }
                ).then((r: boolean) => {
                    if (r) {
                        postSyncInitial();
                    }
                });
            }
        } else {
            ElConfirm(res.msg);
        }
    });
};
// 同步科目期初
const postSyncInitial = () => {
    request({
        url: `/api/FixedAssets/SyncInitial?pid=${props.commissioningPid}`,
        method: "post",
    }).then((res: IResponseModel<object>) => {
        if (res.state === 1000 && res.subState === 0 && res.data) {
            if (res.msg !== "") {
                ElConfirm(res.msg).then(() => {
                    if (res.msg.includes('同步完成') && refreshButton.value) {
                        refreshButton.value.$el.click(); // 触发点击事件
                    }
                });
            } else {
                ElConfirm("同步成功!").then(() => {
                    if (refreshButton.value) {
                        refreshButton.value.$el.click(); // 触发点击事件
                    }
                });
            }
        } else if (res.state === 2000 && res.subState === 1 && !res.data) {
            ElConfirm(res.msg);
        } else {
            ElConfirm(res.msg);
        }
    });
};
const showDetail = (faid: number) => {
    request({
        url: `/api/FixedAssets/Initial?faid=${faid}`,
    }).then((res: IResponseModel<object>) => {
        if (res.state == 1000) {
            emits("showDetail", res.data, props.pageInfo === "intangibles");
        }
    });
};
const selectCardId = ref<IAssetDetail[]>([]);
function selectCard(selection: IAssetDetail[]) {
    selectCardId.value = selection.map((item: IAssetDetail) => item);
}

function increaseAssets(flag: string) {
    let initType = "";
    if (props.pageInfo === "intangibles") {
        initType = "intangibles";
    } else {
        initType = props.commissioningPid !== props.iaPid ? "fixAsset" : "all";
    }
    if (props.finishflag == "1") {
        ElConfirm("亲，资产初始化数据已经与期初的资产数据一致了，您确认还需要修改吗？").then((r: boolean) => {
            if (r) {
                emits("increaseAssets", props.pageInfo === "intangibles" ? props.iaPid : props.commissioningPid, initType);
            } else {
                return false;
            }
        });
    } else {
        emits("increaseAssets", props.pageInfo === "intangibles" ? props.iaPid : props.commissioningPid, initType);
    }
}
function getPrintParams() {
    return {
        period: props.pageInfo === "intangibles" ? props.iaPid : props.commissioningPid,
        datatype: 1,
        fa_property: props.pageInfo === "intangibles" ? "1,2" : props.commissioningDate !== props.iaPeriodInfo ? "0" : "0,1,2",
        fa_num: filterSearchInfo.fa_num,
        fa_type: getSelectVal(filterSearchInfo.fa_type, faTypeOptions.value),
        fa_name: filterSearchInfo.fa_name,
        fa_model: filterSearchInfo.fa_model,
        department: getSelectVal(filterSearchInfo.department, departmentOptions.value),
        note: filterSearchInfo.note,
        status: filterSearchInfo.status,
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        sortField: sortField.value,
        sortOrder: sortOrder.value
    };
}
function PrintList() {
    if (!selectCardId.value.length) {
        ElNotify({
            message: "请选择要打印明细的资产",
            type: "warning",
        });
        return
    } 
    const url = "/api/FixedAssets/PrintDetail";
    const data = {
        pid: props.pageInfo === "intangibles" ? props.iaPid : props.commissioningPid,
        faids: JSON.stringify(selectCardId.value.map((item: IAssetDetail) => item.fa_id)),
    }
    globalPostPrint(url, data);
}
function PrintCard() {
    if (selectCardId.value.length == 0) {
        ElNotify({
            message: "请选择要打印标签的资产",
            type: "warning",
        });
        return false;
    } 
    const faids = selectCardId.value.reduce((pre: number[], cur: IAssetDetail) => {
        pre.push(cur.fa_id);
        return pre;
    }, []);
    globalFormPost(`/api/FixedAssets/PrintCard`, { faids: JSON.stringify(faids) },"print");
}
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "initFixedAssets",
     `/api/FixedAssets/Print`,
     {},
     true
);
function handleEdit(faid: number) {
    emits("handleEdit", faid, props.pageInfo === "intangibles" ? props.iaPid : props.commissioningPid, props.pageInfo === "intangibles");
}

function EnableAddAndDel() {
    request({
        url: `/api/Depreciation/ExistsByPeriod?flag=1&pid=${props.pageInfo === "fixAsset" ? props.commissioningPid : props.iaPid}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (useAccountSetStoreHook().accountSet?.fixedasset === 2) {
            addAndDelShow.value = true;
            return;
        }
        if (res.state === 1000 && !res.data) {
            addAndDelShow.value = false;
        } else {
            addAndDelShow.value = true;
        }
    });
}

// 不平时弹框类名
const unbalanceClass = computed(() => (num1: number, num2: number) => {
    if (Number(num1) > Number(num2)) {
        return "moreBalance";
    } else if (Number(num1) < Number(num2)) {
        return "lessBalance";
    } else {
        return "";
    }
});

// 不平时数据差额
const imbalanceCount = computed(() => (num1: number, num2: number) => {
    if (Number(num1) === Number(num2)) {
        return "0.00";
    }
    return Number(num1) > Number(num2) ? formatMoney(num1 - num2) : formatMoney(num2 - num1);
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableData();
});

//复制资产
const batchCopy = ref(new batchData(false, "copy", [], "1"));
function restrictInput(type: string) {
    if (type === "copy") {
        batchCopy.value.value = batchCopy.value.value!.replace(/[^0-9]/g, "");
    }
}
function copyAssets(datatype: string) {
    if (selectCardId.value.length == 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要复制的资产！",
        });
        return false;
    } else {
        if (datatype == "1" && props.finishflag !== "0") {
            ElConfirm("亲，资产初始化数据已经与期初的资产数据一致了，您确认还需要修改吗？").then((r: boolean) => {
                if (r) {
                    CopyData(datatype);
                } else {
                    return;
                }
            });
        } else {
            CopyData(datatype);
        }
    }
}
function CopyData(datatype: string) {
    let valueCount = 0;
    let ids: number[] = [];
    let codeAndNames: string[] = [];
    selectCardId.value.forEach((item: IAssetDetail) => {
        if (item.fa_num !== "") {
            //去掉合计一栏的选择框
            valueCount++;
            ids.push(item.fa_id);
            codeAndNames.push(item.fa_num + " " + item.fa_name);
        }
    });
    if (valueCount == 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要复制的资产！",
        });
        return false;
    }
    if (valueCount === 1) {
        batchCopy.value = new batchData(true, "copy", ids, "1");
    }
    if (valueCount > 1) {
        ElConfirm("亲，确定要批量复制资产吗？").then((r: Boolean) => {
            if (r) {
                batchCopy.value = new batchData(true, "copy", ids, "1");
            }
        });
    }
}
function CopyDataInner() {
    if (Number(batchCopy.value.value!) > 10) {
        ElNotify({
            type: "warning",
            message: "亲，最多只能复制10个资产！",
        });
        return false;
    }
    const { ids, value } = batchCopy.value;
    let copyPid = props.pageInfo === "fixAsset" ? props.commissioningPid : props.iaPid;
    request({
        url: `/api/FixedAssets/MultipleInitialBatchCopy?pid=${copyPid}&count=${batchCopy.value.value}`,
        method: "post",
        data: ids,
        headers: {
            "Content-Type": "application/json; charset=utf-8",
        },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "亲，复制成功啦！",
                });
                paginationData.currentPage = Math.ceil((paginationData.total + Number(value)) / paginationData.pageSize);
                getTableData();
                emits("getChangeData", "init");
            } else if (res.state === 2000) {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "遇到问题，请您联系系统管理员或稍后重试",
                });
            }
        })
        .finally(() => {
            batchCopy.value.visible = false;
        });
}

//删除资产
function Delete(datatype: string) {
    if (selectCardId.value.length === 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要删除的资产！",
        });
        return false;
    }
    if (datatype == "1" && props.finishflag !== "0") {
        ElConfirm("亲，资产初始化数据已经与期初的资产数据一致了，您确认还需要修改吗？").then((r: Boolean) => {
            if (r) {
                DelData();
            } else {
                return false;
            }
        });
    } else {
        DelData();
    }
}
function DelData() {
    let valueCount = 0;
    let breakCount = 0;
    let ids: number[] = [];
    let codeAndNames: string[] = [];
    selectCardId.value.forEach((item: IAssetDetail) => {
        if (item.fa_num !== "") {
            if (item.hasDep == 0) {
                valueCount++;
                ids.push(item.fa_id);
                codeAndNames.push(item.fa_num + " " + item.fa_name);
            } else {
                breakCount++;
            }
        }
    });
    if (valueCount === 0 && breakCount === 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择要删除的资产！",
        });
        return;
    }
    ElConfirm("此操作将删除已选择资产及其所有变更记录，确定删除吗?").then((r) => {
        if (r) {
            if (valueCount > 0) {
                request({
                    url: `/api/FixedAssets/CanDelete`,
                    method: "post",
                    data: ids,
                }).then((res: IResponseModel<string>) => {
                    if (res.data == "") {
                        request({
                            url: `/api/FixedAssets/InitialBatch`,
                            method: "delete",
                            data: ids,
                        }).then((res: IResponseModel<boolean>) => {
                            if (res.state === 1000 && res.data) {
                                ElConfirm("成功：" + valueCount + "，跳过：" + breakCount + "（已计提折旧期间的资产将会被跳过！）", true);
                                getTableData();
                                emits("getChangeData", "init");
                            } else {
                                ElNotify({
                                    type: "error",
                                    message: res.msg,
                                });
                            }
                        });
                    } else {
                        let flag = res.data.substr(res.data.length - 1, 1);
                        let datas = res.data.substr(0, res.data.length - 1);
                        if (flag == "D") {
                            ElNotify({
                                type: "warning",
                                message: "编号为" + datas + "资产已删除！",
                            });
                            getTableData();
                        } else if (flag == "V") {
                            ElNotify({
                                type: "warning",
                                message: "编号为" + datas + "资产已生成凭证无法删除，请先删除相关凭证！",
                            });
                        } else if (flag == "F") {
                            ElNotify({
                                type: "warning",
                                message: "编号为" + datas + "资产已生成折旧凭证无法删除，请先删除相关凭证！",
                            });
                        }
                    }
                });
            } else {
                ElConfirm("成功：" + valueCount + "，跳过：" + breakCount + "（已计提折旧期间的资产将会被跳过！）", true);
            }
        }
    });
}
//导入
function handleImport() {
    if (props.finishflag == "1") {
        ElConfirm("亲，资产初始化数据已经与期初的资产数据一致了，您确认还需要修改吗？").then((r: boolean) => {
            if (r) {
                emits("handleImport", "import", props.pageInfo === "intangibles");
            } else {
                return false;
            }
        });
    } else {
        emits("handleImport", "import", props.pageInfo === "intangibles");
    }
}
defineExpose({ getTableData, EnableAddAndDel, checkHasIaAssets });

const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
function handleGetAttachFiles(row: IAssetDetail) {
    getAssetsAttachFiles(row, uploadFileDialogRef);
}
function handleSaveAttachFiles(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
    const { faId } = _params;
    function successCallBack() {
        const row = tableData.value.find((item) => item.fa_id === faId);
        if (row) {
            row.attachsCount = fileList.length;
            row.attachFiles = fileList.map((item) => item.fileId).join(",");
        }
    }
    saveAttachFileFn(faId, newFileids, delFileids, fileList, successCallBack, () => {});
}

//列设置弹窗
const columnSetShow = ref(false);
const columnSetRef = ref<InstanceType<typeof ColumnSet>>();
const allColumns = ref<IColItem[]>([]);
const module = ref(600);
function ColumnsQuery() {
    fixedassetsListColumnsSet.value = setColumns("1", true, accountingStandard.value);
    if (!showInfo.value) {
        columns.value = allColumns.value.length > 0 ? getShowColumn(allColumns.value, fixedassetsListColumnsSet.value) : setColumns("1", showInfo.value, accountingStandard.value);
    } else {
        columns.value = setColumns("1", showInfo.value, accountingStandard.value);
    }
    watchDrop();
}
function getColumnSetList() {
    getColumnListApi(module.value).then((res) => {
        allColumns.value = res.data;
        ColumnsQuery();
    }).catch(() => {
        allColumns.value = [];
    })
}
function openColSet() {
    columnSetShow.value = true;
    columnSetRef.value?.initData();
    nextTick(() => {
        columnSetRef.value?.rowDrop();
    })
}
function saveColumnSet(data: IColItem[]) {
    if (!showInfo.value) {
        columns.value = getShowColumn(data, fixedassetsListColumnsSet.value);
    }
    watchDrop();
    getColumnSetList();
}
//头部列拖拽设置
const setModule = "Initfixedassets";
function cellDrop(oldIndex: number, newIndex: number) {  
    let index1 = allColumns.value.findIndex((v) =>v.columnName === columns.value[oldIndex].label);
    let index2 = allColumns.value.findIndex((v) =>v.columnName === columns.value[newIndex].label);
    allColumns.value = alterArrayPos(allColumns.value, index1, index2);
    columns.value = alterArrayPos(columns.value, oldIndex, newIndex);
    columnSetRef.value?.saveData(module.value, allColumns.value);
}
function watchDrop() {
    nextTick(() => {
        initfixedassetsRef.value?.columnDrop(initfixedassetsRef.value?.$el, columns.value, showInfo.value);
    })
}
function handleScroll() {
    initfixedassetsRef.value?.$el.click();
}
//表头字段模糊搜索
function filterSearch(prop: string, data: any) {
    if (isKeyOfIFSearchItem(prop)) {  
        if (typeof data === "string" || typeof data === "number") {
            if (typeof filterSearchInfo[prop] === "string") {
                (filterSearchInfo[prop] as string) = data.toString();
            }
            if (typeof filterSearchInfo[prop] === "number") {
                (filterSearchInfo[prop] as number) = Number(data);
            } 
            initEntry.value = false; 
            getTableData(); 
        } else {
            if (data.length > 0) {
                const filteredData: number[] = data.filter((item: any): item is number => typeof item === 'number');  
                (filterSearchInfo[prop] as number[]) = filteredData;
                initEntry.value = false; 
                getTableData();
            } 
        }
    } 
}
//资产编号字段复制
function handleCopy() {
    const oText = document.getElementById('fa_num')!.innerText ||"";
    copyText(oText);
    ElNotify({ type: "success", message: "复制成功！" }); 
}

</script>

<style scoped lang="less">
:deep(tbody .linkColor .cell) {
    color: var(--link-color);
}
.init-fixedassets {
    text-align: left;
    background-color: var(--white);
    .main-title {
        padding: 10px;
        text-align: center;
        .main-back {
            float: left;
            cursor: default;
        }
    }
    .downlist + ul {
        & li {
            padding: 0 !important;
            text-align: center !important;
        }
    }
    .disabled {
        color: var(--border-color);
        border-color: var(--border-color);
        background-color: var(--white);
        opacity: 0.5;
        &:hover {
            color: var(--border-color);
            background-color: var(--white) !important;
        }
        & .question-img {
            opacity: 0.5;
        }
    }
}
.init-iaassets {
    .main-title {
        padding: 10px;
        .main-back {
            float: left;
            cursor: default;
        }
    }
    .disabled {
        color: var(--border-color);
        border-color: var(--border-color);
        background-color: var(--white);
        opacity: 0.5;

        &:hover {
            color: var(--border-color);
            background-color: var(--white) !important;
        }
        & .question-img {
            opacity: 0.5;
        }
    }
}
.dialog-cont {
    padding: 22px 32px;
    :deep(.el-table--enable-row-hover) {
        .el-table__body tr:hover > td.el-table__cell {
            background-color: var(--white);
        }
    }
    :deep(.cell) {
        .unbalance {
            color: var(--red);

            .unbalance-block {
                display: inline-block;
                cursor: pointer;
                position: absolute;

                img {
                    width: 14px;
                    margin-top: 5px;
                    margin-left: 2px;
                }
            }
        }
    }
    .unBalanceIcon {
        float: left;
        height: 18px;
        width: 18px;
        background-size: cover;
        background-position: center;
    }
    .moreBalance {
        background-image: url("@/assets/FixedAssets/more.png");
    }
    .lessBalance {
        background-image: url("@/assets/FixedAssets/less.png");
    }
}

.tip {
    text-align: left;
    color: var(--red);
    font-size: var(--font-size);
    line-height: var(--line-height);
    padding: 0 20px 10px;
}

.show-detail {
    color: #fd5055;
    user-select: none;
    cursor: pointer;
    &:hover {
        text-decoration: underline;
    }
    i {
        background: url("@/assets/FixedAssets/warn-red.png") no-repeat 0 center;
        display: inline-block;
        vertical-align: middle;
        width: 16px;
        height: 16px;
        margin: -2px 0 0;
    }
}

.main-center {
    position: relative;
}
.imbalance-icon {
    position: absolute;
    top: 50px;
    left: 370px;
    width: 120px;
    height: 60px;
    background: url("@/assets/FixedAssets/020FixedAssets.png") no-repeat 0px -120px;
    z-index: 999;
}
.dialog-cont {
    padding: 10px 20px;
    min-width: 400px;
    box-sizing: border-box;
    .check-fixed {
        display: flex;
        justify-content: space-between;
        list-style: none;
        padding: 0;
        li {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 300px;
            height: 260px;
            .check-title {
                font-size: 20px;
                text-align: center;
            }
            & ~ li {
                border-left: 1px solid #ccc;
            }
            span {
                position: absolute;
                &.v1_value {
                    top: 130px;
                    left: 35px;
                }
                &.total {
                    top: 180px;
                    left: 50%;
                    transform: translateX(-50%);
                }
                &.v2_value {
                    top: 130px;
                    right: 35px;
                }
            }

            .pic {
                width: 238px;
                height: 110px;
                background: url("@/assets/FixedAssets/020FixedAssets.png") -41px -336px no-repeat;
            }
            &.left {
                .v1_value {
                    top: 160px;
                }
                .v2_value {
                    top: 110px;
                }
                .pic {
                    height: 130px;
                    background-position: -41px -449px;
                }
            }
            &.right {
                .v1_value {
                    top: 110px;
                }
                .v2_value {
                    top: 160px;
                }
                .pic {
                    height: 130px;
                    background-position: -41px -586px;
                }
            }
        }
    }
    .dia-tips {
        width: 100%;
        margin-top: 20px;
        p {
            width: 100%;
            font-size: 12px;
            line-height: 16px;
            margin: 12px auto;
            &.tips-title {
                color: #f00;
            }
        }
    }
}

.question-img {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 0;
    left: 100px;
}
.dia-table {
    :deep(.el-table__empty-text) {
        margin: 100px auto;
    }
    :deep(.el-table) {
        .el-table__header th:last-child {
            border-right: none;
        }
        .el-table__body th:last-child {
            border-right: none;
        }
    }
}
</style>
