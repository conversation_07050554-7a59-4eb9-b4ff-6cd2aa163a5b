import type { IPeriod } from "@/api/period";
import type { VoucherEntryModel } from "@/components/Voucher/types";
export interface IPeriodData {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
    time?: string;
}

export interface ICheckoutPeriod extends IPeriod {
    greenLocked?: boolean;
    statusByGen?: number;
}

export interface ICheckoutItem {
    [key: number | string]: Array<ICheckoutPeriod>;
}

export interface ICheckoutPageItem {
    isPaased: boolean;
    msg: string;
    isError: boolean;
    onlyMessage: boolean;
    project: string;
}

export interface ICheckResultItem {
    sn: number;
    isLastMouth: boolean;
    status: number;
    vouchers: Voucher[];
    checktype: number;
    checkDerection: number;
    voucherSums: any;
    vtId: number;
    vtName: string;
    accountingSubjects: IAccountingSubject[];
    fcitems: IFcitem[];
    cardInfo?: any;
    extendInfo?: any;
    newVoucherInfo?: any;
    hasLoading?: boolean;
    suppleFcData?: (rowData: ICheckResultItem, pid: number, callBack: Function) => void;
    attachFiles?: Array<any>;
}
export interface ICreateVoucher {
    assistingaccounting: number;
    assistsetting: string;
    asub_code: string;
    asub_id: number;
    asub_name: string;
    credit: number;
    debit: number;
    description: string;
    index: number;
    index2: number;
    is_farow: number;
    measureupnit: number;
    price: number;
    quantity: number;
    quantityaccounting: number;
    fcamount: number;
    fccode: string;
    fcid: number;
    fcrate: number;
    foreigncurrency: number;
    isjtsub: number;
}
export interface Voucher {
    asid: number;
    pid: number;
    vid: number;
    vgid: number;
    vnum: number;
    vtype: number;
    vdate: string;
    attachment: number;
    preparedBy: string;
    preparedDate: string;
    approvedBy: string;
    approvedDate: string;
    approveStatus: number;
    createBy: number;
    createDate: string;
    modifiedBy: number;
    voucherLines: IVoucherLine[];
    debitSum: number;
    creditSum: number;
    modifiedDate: string;
    vgname: string;
}

export interface IVoucherLine {
    asid: number;
    pid: number;
    vid: number;
    veid: number;
    description: string;
    vsid: number;
    asubname: string;
    asubCode: string;
    asubid: number;
    debit: number;
    credit: number;
    quantity: number;
    price: number;
    foreignCurrency: number;
    fcid: number;
    fcrate: number;
    fcamount: number;
    aacode: string;
}

export interface IAccountingSubject {
    asubid: number;
    asubcode: string;
    asubname: string;
    derection: number;
    assistingaccounting: string;
    assistsetting: string;
    foreigncurrency: string;
    sumValue: number;
    fcid: number;
    fcrate: number;
    sumFcValue: number;
    quantityaccounting: number;
    quantity: number;
    price: number;
}

export interface IAccountingSubjectItem {
    asubId: number;
    asubCode: string;
    asubName: string;
    direction: number;
    assistingAccounting: number;
    assistSetting: string;
    foreigncurrency: string;
    sumValue: number;
    fcid: number;
    fcrate: number;
    fcsumValue: number;
    quantityAccounting: number;
    quantity: number;
    price: number;
}

export interface IFcitem {
    fcid: number;
    name: string;
    code: string;
    rate: number;
    valueSum: number;
    fcvalueSum: number;
    derection: number;
}

export interface IVoucherTemplateModel {
    vtId: number;
    vtName: string;
    vtType: number;
    vgId: number;
    vgName: string;
    state: boolean;
    show?: boolean;
    type?: "new" | "edit";
    voucherTemplateLines: IVoucherTemplateLine[];
}

export interface IVoucherTemplateLine {
    veId?: number;
    description: string;
    asubId?: number;
    asubCode?: string;
    asubName?: string;
    aacode?: string;
    amount?: number;
    qutAmount?: number;
    valueType?: number;
    valueRate?: number;
    directory?: number;
    calculations?: ICalculation[];
    show?: boolean;
    asubAAName?: string;
}

export interface ICalculation {
    vcId: number;
    asubId: number;
    asubCode: string;
    asubName: string;
    operator: number;
    valueType: number;
    valueRate: number;
    index?: number;
}

export interface IVoucherLineTemplates {
    sc: ITemplateVoucherLines[];
    aw: ITemplateVoucherLines[];
    ad: ITemplateVoucherLines[];
    dp: ITemplateVoucherLines[];
    ab: ITemplateVoucherLines[];
    at: ITemplateVoucherLines[];
    ut: ITemplateVoucherLines[];
    ai: ITemplateVoucherLines[];
}
export interface ITemplateVoucherLines {
    asub_code: string;
    asub_id: string;
    asub_name: string;
    credit: string;
    debit: string;
    description: string;
    assistingaccounting: string;
    assistsetting: string;
    quantityaccounting: string;
    measureunit: string;
    price: string;
    quantity: string;
    foreigncurrency: number;
    fc_id: number;
    fc_rate: string;
    fc_amount: string;
}
export interface INewSalesCostInfoRow {
    aacode: string;
    aatype: string;
    assistingAccounting: number;
    asubAAName: string;
    asubCode: string;
    asubId: number;
    asubName: string;
    asubType: number;
    credit: number;
    debit: number;
    description: string;
    fcAmount: number;
    fcCode: string;
    fcId: number;
    fcRate: number;
    foreigncurrency: number;
    isFaLine: boolean;
    measureUnit: string;
    price: number;
    quantity: number;
    quantityAccounting: number;
    veId: number;
}
export interface ICarryOverBack {
    sn: number;
    isLastMouth: boolean;
    status: number;
    vouchers: Voucher[];
    checktype: number;
    checkDerection: number;
    voucherSums: any;
    vtId: number;
    vtName: string;
    accountingSubjects: IAccountingSubject[];
    fcitems: IFcitem[];
}

export interface IConbineIncomeRows {
    rowsIncome?: VoucherEntryModel[];
    rowsExpanse?: VoucherEntryModel[];
    contactRows?: VoucherEntryModel[];
}

export interface IPreCheckList {
    isPassed: boolean;
    preCheckId: number;
    preCheckName: string;
}
