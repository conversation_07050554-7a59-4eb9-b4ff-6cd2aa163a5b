import { defineStore } from "pinia"
import { request, IResponseModel } from "@/utils/service"

const getAccountStandardText = (accountStandard: number | string, subAccountStandard: number | string) => {
  switch (accountStandard) {
    case 1:
    case "1":
      return "小企业会计准则"
    case 2:
    case "2":
      switch (subAccountStandard) {
        case 1:
        case "1":
          return "企业会计准则(2019年未执行新金融准则、新收入准则和新租赁准则)"
        case 2:
        case "2":
          return "企业会计准则(2019年已执行新金融准则、新收入准则和新租赁准则)"
        default:
          return "企业会计准则(2007年)"
      }
    case 3:
    case "3":
      return "民间非营利组织会计制度"
    case 4:
    case "4":
      return "农民专业合作社财务会计制度"
    case 5:
    case "5":
      return "农民专业合作社财务会计制度（2023年）"
    case 6:
    case "6":
      return "工会会计制度"
    case 7:
    case "7":
      return "农村集体经济组织会计制度"
    default:
      return ""
  }
}

export interface ITaxBureauUserInfo {
  // 企业基本信息
  asId: number // 企业ID
  asName: string // 企业名称
  taxpayerName: string // 纳税人名称
  taxNumberS: string // 统一社会信用代码
  industry: string // 行业
  establishDate: string // 成立日期

  // 税务相关信息
  creditLevel: string // 信用等级
  taxaionType: string // 税收类型
  taxType: number // 增值税种类
  taxAreaId: string // 税区ID
  isTaxClassification: boolean // 是否重分类

  // 会计准则信息
  accountingStandard: number // 会计准则
  subAccountingStandard: number // 子会计准则
  accountingStandardName: string // 会计准则名称
  taxAccountStandard: number // 税务会计准则
  taxAccountStandardName: string // 税务会计准则名称

  // 财务信息
  hasFinancialInfo: boolean // 财务报表报送信息
  hasCashflowSheet: boolean // 是否填写现金流量表
  yearStartBalance: number // 年初取值来源
  isClassification: boolean // 是否重重分类
  dataSource: number // 财报未结账取数
  initCompleted: boolean // 是否初始化完成

  // 税局登录信息
  taxBureauLoginType: number // 税局登录类型
  taxBureauAccount: string // 税局账号
  taxBureauPersonPositionType: number // 个人职务类型 (如：30-办税员)
  taxBureauPersonIdentityNo: string // 个人身份证号
  taxBureauPersonAccount: string // 个人账号
  taxBureauPersonPassword: string // 个人密码
  taxBureauPersonPhone: string // 个人电话
  taxBureauAuthorized: boolean // 是否已授权
  taxBureauAgentTaxNumber: string // 代理税号
  nationalTaxBureauUrl: string // 税局网址
}

export const useBasicInfoStore = defineStore("basicInfo", () => {
  const basicInfo = ref<{ [key: string]: any }>({})
  // 获取基础信息
  const getBasicInfo = () => {
    return new Promise<{ [key: string]: any }>((resolve, reject) => {
      const url = `/api/basic/info`

      request({
        url,
      }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
          basicInfo.value = res.data

          const { accountingStandard, subAccountingStandard } = res.data
          basicInfo.value.financeStandard = getAccountStandardText(accountingStandard, subAccountingStandard)

          resolve(res.data)
        } else {
          reject(res.msg)
        }
      })
    })
  }

  const accountSetList = ref<{ [key: string]: any }[]>([])
  // 获取账套列表
  const getAccountSetList = () => {
    return new Promise<{ [key: string]: any }[]>((resolve, reject) => {
      const url = `/api/common/top-info`

      request({
        url,
        method: "get",
      }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
          accountSetList.value = res.data
          resolve(res.data)
        } else {
          reject(res.msg)
        }
      })
    })
  }

  return { basicInfo, accountSetList, getBasicInfo, getAccountSetList }
})
