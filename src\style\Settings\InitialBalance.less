.main-tool-right {
    & .fc-content {
        height: 28px;
        font-size: 14px;
        display: flex;
        align-items: center;
        margin-right: 20px;
        :deep(.el-input__wrapper){
            padding-left:6px;
            padding-right: 3px;
        }
        :deep(.el-input__suffix-inner){
            width:18px
        }
    }
    & .help {
        &:hover{
            cursor: pointer;
        }
        & img {
            vertical-align: middle;
        }
        & span {
            vertical-align: middle;
            margin-left: 8px;
            display: inline-block;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 28px;
        }
    }
}
.main-center{
    padding: 0 20px 10px;
    overflow-x: hidden;
    background-color: var(--white);
    // & :deep(.el-input__wrapper){
    //     height:18px;
    // }

}
:deep(.el-tabs__nav) {
    padding-left: 10px;
}
#injectTable {
    .inject-table-content {
        & .inject-table-main {
            padding: 20px 40px;
            overflow: hidden;
        }
        & .buttons {
            padding: 10px 0;
            text-align: center;
            border-top: 1px solid var(--border-color);
        }
    }
    .inject-message {
        & .inject-message-main {
            padding: 20px 40px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            min-height: 120px;
        }
        & .buttons {
            padding: 10px 0;
            text-align: center;
            border-top: 1px solid var(--border-color);
        }
    }
}
#checkBanlace {
    & .pop {
        float: left;
        width: 345px;
        & .pop-heading {
            height: 60px;
            text-align: center;
            & span {
                line-height: 60px;
                font-size: 19px;
            }
        }
        & .pop-body {
            height: 240px;
            & ul {
                position: relative;
                padding-left: 0px;
                list-style: none;
                width: 320px;
                height: 210px;
                display: inline-block;
                margin: 0;
                margin-left: 18px;
                & li {
                    width: 130px;
                }
                & .ibc {
                    position: relative;
                    left: -15px;
                    float: right;
                    height: 20px;
                    line-height: 20px;
                    text-align: center;
                }

                & .ibd {
                    display: inline;
                    position: relative;
                    float: left;
                    height: 20px;
                    text-align: center;
                }

                & .ibt {
                    position: relative;
                    left: -46px;
                    float: left;
                    line-height: 20px;
                    text-align: center;
                }

                & .ahc {
                    position: relative;
                    left: -10px;
                    float: right;
                    height: 20px;
                    line-height: 20px;
                    text-align: center;
                }

                & .ahd {
                    display: inline;
                    position: relative;
                    float: left;
                    height: 20px;
                    text-align: center;
                }

                & .aht {
                    position: relative;
                    left: -46px;
                    float: left;
                    line-height: 20px;
                    text-align: center;
                }

                & .bac {
                    position: relative;
                    float: right;
                    left: -20px;
                    height: 20px;
                    line-height: 20px;
                    text-align: center;
                }

                & .bad {
                    display: inline;
                    position: relative;
                    float: left;
                    left: -10px;
                    height: 20px;
                    text-align: center;
                }

                & .bat {
                    position: relative;
                    left: -57px;
                    float: left;
                    line-height: 20px;
                    text-align: center;
                }
                & .bg {
                    position: absolute;
                    left: 22px;
                    top: 80px;
                    width: 238px;
                    height: 120px;
                    background: url(@/assets/Icons/060Settings.png) -160px 0px no-repeat;
                }
                & .bg2 {
                    position: absolute;
                    left: 22px;
                    top: 80px;
                    width: 238px;
                    height: 130px;
                    background: url(@/assets/Icons/060Settings.png) -400px 0px no-repeat;
                }
                & .bg3 {
                    position: absolute;
                    left: 22px;
                    top: 80px;
                    width: 238px;
                    height: 130px;
                    background: url(@/assets/Icons/060Settings.png) -670px 0px no-repeat;
                }
                & .balance {
                    background-position: -152px -8px;
                }
                
                & .credit {
                    background-position: -160px -261px;
                }

                & .debit {
                    background-position: -160px -132px;
                }

                & .balance1 {
                    background-position: -420px 0px;
                }

                & .credit1 {
                    background-position: -420px -261px;
                }

                & .debit1 {
                    background-position: -420px -132px;
                }

                & .balance2 {
                    background-position: -670px 0px;
                }

                & .credit2 {
                    background-position: -670px -261px;
                }

                & .debit2 {
                    background-position: -670px -132px;
                }
            }
        }
        & .erp-pop-body{
            & .bg {
                background-image: url(@/assets/Icons//060Settings-erp.png) !important;
            }
            & .bg2 {
                background-image: url(@/assets/Icons//060Settings-erp.png)!important;
            }
            & .bg3 {
                background-image: url(@/assets/Icons//060Settings-erp.png)!important;
            }
        }
    }
}
:deep(.el-tabs__content) {
    background-color: var(--white);
}
:deep(.el-tabs__header) {
    background-color: white;
    margin: 0px;
}
.file-button .file-box {
    color: var(--font-color);
    font-size: var(--font-size);
    line-height: var(--line-height);
    margin-left: 20px;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
}