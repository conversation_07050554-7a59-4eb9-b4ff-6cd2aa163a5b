<template>
    <div class="dropdown-button">
        <a :class="'button dropdown more ' + (props.class ?? '')" :style="props.width ? { width: width + 'px' } : {}">{{ btnTxt }}</a>
        <ul :style="props.downlistWidth ? { width: downlistWidth + 'px' } : {}" :class="{ center: center, showTop: props.showTop }">
            <slot></slot>
        </ul>
    </div>
</template>
<style lang="less" scoped>
.dropdown-button {
    position: relative;
    display: flex;
    ul {
        overflow: hidden;
        list-style: none;
        padding: 0;
        margin: 0;
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        top: 28px;
        left: 0;
        display: none;
        &.showTop {
            top: unset;
            bottom: 28px;
        }

        :deep(li) {
            width: 100%;
            height: 32px;
            line-height: 32px;
            cursor: pointer;
            font-size: 13px;
            padding: 0 11px;
            box-sizing: border-box;
            text-align: left;
            &:hover {
                // border-color: var(--table-hover-color);
                // background-color: var(--table-hover-color);
                // color: var(--main-color);
                background-color: var(--main-color);
                color: var(--white);
            }

            &:active {
                background-color: var(--dark-main-color);
                color: var(--white);
            }
            &.disabled {
                color: var(--border-color);
                background-color: var(--white);
                &:hover {
                background-color: var(--white);
                color: var(--border-color);
                }
            }
            // 带复选框样式
            .el-checkbox {
                &:hover {
                    color: var(--white);
                }
            }
        }

        &.center {
            :deep(li) {
                text-align: center;
            }
        }
    }

    &:hover {
        ul {
            display: block;
            z-index: 99;
        }
    }
}
</style>
<script lang="ts" setup>
const props = defineProps<{
    btnTxt: string;
    class?: string;
    downlistWidth?: number;
    center?: boolean;
    width?: number;
    showTop?: boolean;
}>();
</script>
