<script setup lang="ts">
import { ref, useSlots, computed, watch, nextTick } from "vue";
import type { IColumnProps } from "./IColumnProps";
import Table from "./index.vue";
import type { ElTable } from "element-plus/es/components";
import { getPeriodsApi } from "@/api/period";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { getGlobalLodash } from "@/util/lodash";
import { getYearMonthPid } from "@/util/period";
import { handleAsubName } from "@/util/format";
import TablePagination from "./TablePagination.vue";

interface ISlotLabel {
    name: string; // 插槽列对应label
    width: number | string; //列宽  min-width值
    align?: "left" | "center" | "right"; // 对齐方式
    headerAlign?: "left" | "center" | "right"; // 表头对齐方式
}
const props = withDefaults(
    defineProps<{
        loading?: boolean;
        data?: Array<any>;
        columns?: Array<IColumnProps>;
        pageIsShow?: boolean;
        pageSizes?: Array<number>;
        pageSize?: number;
        layout?: string;
        total?: number;
        border?: boolean;
        stripe?: boolean;
        fit?: boolean;
        size?: any;
        treeProps?: Object;
        rowKey?: any; // string | Function
        currentPage?: number;
        maxHeight?: number;
        minHeight?: string;
        emptyText?: string;
        height?: number | string;
        hearderRowStyleName?: string;
        showHeader?: boolean;
        rowClassName?: any; // string | Function
        objectSpanMethod?: any; // Function
        hasAddSub?: boolean;
        // 表格有左加右减功能,根据哪个字段来判断当前行
        addSubField?: string;
        expandRowKeys?: Array<string>;
        width?: number;
        otherField?: string;
        firstLabel?: string;
        view?: string;
        treePageIsNotShow?: boolean;
        // treeTable里面放插槽的话需要传这个参数，否则表头和数据对不齐
        slotLabels?: ISlotLabel[];
    }>(),
    {
        loading: false,
        data: () => [],
        columns: () => [],
        pageIsShow: false,
        pageSizes: () => [1, 2, 10, 20, 30],
        pageSize: () => 20,
        layout: () => "total, sizes, prev, pager, next, jumper",
        total: () => 0,
        border: true,
        stripe: false,
        fit: true,
        size: "default",
        treeProps: () => ({}),
        rowKey: "",
        currentPage: () => 1,
        hearderRowStyleName: "",
        showHeader: true,
        rowClassName: "",
        emptyText: "数据加载中...",
        objectSpanMethod: () => {},
        hasAddSub: false,
        addSubField: "",
        expandRowKeys: () => [],
        width: 100,
        otherField: "",
        firstLabel: "",
        view: "",
        treePageIsNotShow: true,
        slotLabels: () => [],
    }
);
const slots = useSlots();
const slotsArr = Object.keys(slots);
const _ = getGlobalLodash()
const TableComponents = ref<InstanceType<typeof ElTable>>();
const objectSpanMethod = (data: { row: any; rowIndex: number; column: any; columnIndex: number }) => {
    if (data.columnIndex > 0)
        return {
            rowspan: 1,
            colspan: props.columns.length + 10,
        };
};
const customEmptyText = ref(props.emptyText);
watch(
    () => props.emptyText,
    (val) => {
        customEmptyText.value = val;
    },
    { immediate: true }
);
const emit = defineEmits<{
    (e: "selection-change", value: any): void;
    (e: "row-click", value: any, column: any, event: any): void;
    (e: "size-change", value: number): void;
    (e: "current-change", value: number): void;
    (e: "sort-change", val: any): void;
    (e: "cell-click", row: any, column: any, cell: any, event: any): void;
    //(e: "get-row-info", $event: MouseEvent, row: any): void;
    (e: "table-add-or-subtract", val: any): void;
    // (e: "table-subtract", val: any): void;
    (e: "go-to-detail", val: any): void;
    (e: "refresh"): void;
}>();
const handleSizeChange = (val: any) => {
    emit("size-change", val);
};
const handleCurrentChange = (val: any) => {
    emit("current-change", val);
};
const goToDetail = (val: any) => {
    emit("go-to-detail", val);
};
const handleRerefresh = () => {
    emit("refresh");
};
let expands = ref<any[]>([]);

// 扩展table行
function expandRow(row: any) {
    let index = expands.value.indexOf(row[props.rowKey]);
    if (index < 0) {
        // 如果当前没有该扩展列，expands添加该列，扩展
        expands.value.push(row[props.rowKey]);
        //   this.getOneTableDetail(row)
    } else {
        // 如果当前已经有该扩展列，expands清空，收回
        nextTick(() => {
            expands.value.splice(index, 1);
        });
    }
}
let tableData = ref<any[]>([]);
let parentCol = computed(() => {
    let columnsP = [{ slot: "expand" }, { slot: "other" }, ..._.cloneDeep(props.columns)];
    columnsP.forEach((v: any) => {
        if (v.prop) {
            v.prop = "";
        }
        if (v.children && v.children.length) {
            v.children.forEach((ch: any) => {
                ch.prop = "";
            });
        }
    });
    columnsP.splice(2, 1);
    return columnsP;
});

const initData = (data: any) => {
    // 总账孩子，排序
    if (props.view === "GeneralLedger") {
        data.forEach((item: any, index: number) => {
            item.index = index;
            if (item.asub_code.length > 4) {
                item.level = 2;
            }
        });
    }
    if (data) {
        tableData.value = [];
        if (data.length) {
            data.forEach((item: any) => {
                handleData(item);
            });
        }
    }
    if (props.view === "GeneralLedger") {
        tableData.value = tableData.value.sort((a: any, b: any) => {
            // if (b.asub_code !== a.asub_code) {
            //     return a.asub_code - b.asub_code;
            // } else {
            //     return (getYearMonthPid(a.period) || 0) - (getYearMonthPid(b.period) || 0);
            // }
            return a.index - b.index;
        });
    }
};

defineExpose({
    initData,
});
function handleData(item: any) {
    let index = tableData.value.findIndex((v: any) => {
        if (props.view === "GeneralLedger") {
            if (item["asub_name"].includes("未录入辅助核算")) {
                return v[props.rowKey] === item[props.rowKey]  && v["asub_name"].includes("未录入辅助核算");
            } else {
                return v[props.rowKey] === item[props.rowKey]  && !v["asub_name"].includes("未录入辅助核算");
            }
        } else {
            return v[props.rowKey] === item[props.rowKey];
        }
    });
    let data: any = {};

    if (index < 0) {
        data = { ...item };
        data.family = [];
        data.family.push(item);
        data.hasChildren = true;
        tableData.value.push(data);
        expands.value.push(item[props.rowKey]);
        data.balance = checkUneven(item);
    } else {
        tableData.value.forEach((v: any) => {
            // 资金-核对总账判断平不平

            if (props.view === "GeneralLedger") {
                if (v[props.rowKey] === item[props.rowKey] ) {
                    v.family.push(item);
                }
            } else {
                if (v[props.rowKey] === item[props.rowKey]) {
                    v.balance = checkUneven(item);
                    v.family.push(item);
                }
            }
        });
    }
}
// 检查平不平
function checkUneven(item: any) {
    if (props.view === "CDCheck" && item.name === "差异" && item.ac_name === "不平") {
        return false;
    }
    // 资产-核对总账判断平不平
    if (props.view === "FACheck" && item.itemTypeText === "差异" && (item.credit || item.debit || item.initial || item.total)) {
        return false;
    }
    return true;
}
watch(
    () => props.loading,
    (val) => {
        if (!val && props.data.length === 0) {
            customEmptyText.value = "暂无数据";
        } else if (val && props.data.length === 0) {
            customEmptyText.value = "数据加载中...";
        } else {
            customEmptyText.value = "  ";
        }
    }
);
// 资金核对总账项目列
</script>

<template>
    <div class="tree-table custom-table" :class="treePageIsNotShow ? 'tree-page-is-not-show' : ''">
        <Table
            :data="tableData"
            :columns="parentCol"
            :loading="loading"
            :empty-text="customEmptyText"
            :page-is-show="pageIsShow"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :layout="layout"
            :total="total"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :hearderRowStyleName="hearderRowStyleName"
            :object-span-method="objectSpanMethod"
            :border="true"
            :stripe="false"
            :row-key="rowKey"
            :expand-row-keys="expands"
            row-class-name="table-row"
            :style="{ minHeight: minHeight ? minHeight : '0' }"
        >
            <template #expand>
                <el-table-column type="expand" :resizable="true" align="left" width="-1" class="expand-col">
                    <template #default="props">
                        <Table :data="props.row.family" :stripe="true" :columns="columns" :show-header="false">
                            <template v-for="item in slotsArr" #[item] :key="item">
                                <slot :name="item"></slot>
                            </template>
                        </Table>
                        <div v-if="!props.row.balance" class="show-state"></div>
                    </template>
                </el-table-column>
            </template>
            <template #other>
                <el-table-column :label="firstLabel" :min-width="width" class-name="title-column">
                    <template #default="props">
                        <span style="height: 100%" class="pr-10" @click="expandRow(props.row)">
                            <img v-show="expands.includes(props.row[rowKey])" src="@/assets/icons/down-black.png" alt="" />
                            <img v-show="!expands.includes(props.row[rowKey])" src="@/assets/icons/right-black.png" alt="" />
                        </span>
                        <span
                            class="link"
                            style="font-weight: bold"
                            v-if="props.row[otherField].includes('<a')"
                            @click="goToDetail(props.row[otherField])"
                        >
                            {{ handleAsubName(props.row[otherField]) }}
                        </span>
                        <span v-else style="font-weight: bold">
                            {{ props.row[otherField] === "平" || props.row[otherField] === "不平" ? "" : props.row[otherField] }}</span
                        >
                    </template>
                </el-table-column>
            </template>
            <template v-for="(item, index) in slotsArr" #[item] :key="item">
                <el-table-column
                    :label="slotLabels[index].name"
                    :min-width="slotLabels[index].width"
                    :align="slotLabels[index]?.align || 'left'"
                    :header-align="slotLabels[index]?.headerAlign || 'left'"
                >
                    <template #default="props"> </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>
<style lang="less" scoped>
.show-state {
    position: absolute;
    top: 10px;
    left: 775px;
    width: 120px;
    height: 60px;
    background: url(../../assets/FixedAssets/020FixedAssets.png) no-repeat 0 -120px;
    z-index: 999;
}
.tree-table {
    :deep(.el-table__expand-icon) {
        visibility: hidden;
    }
    :deep(.table-row) {
        background-color: #f8faf8 !important;
        &:hover > td.el-table__cell,
        &.current-row > td.el-table__cell {
            background-color: #f8faf8 !important;
        }
    }
    :deep(.el-table) {
        & > .el-popper {
            max-width: 300px;
            text-align: left;
        }
        .el-scrollbar__view {
            padding-bottom: 0;
        }
        .el-table .el-table__inner-wrapper .el-table__body-wrapper .el-scrollbar .el-scrollbar__wrap {
            position: static;
            margin-bottom: 0;
        }

        .el-scrollbar__bar.is-horizontal {
            display: none !important;
        }
        .title-column {
            .cell {
                height: 100%;
                display: flex;
                align-items: center;
                > span.pr-10 {
                    padding-right: 6px;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }
    }
    &.tree-page-is-not-show {
        :deep(.el-table) {
            .el-scrollbar__view {
                .custom-table {
                    .el-scrollbar__view {
                        padding-bottom: 0px;
                    }
                }
            }
        }
    }
    :deep(.el-table--border .el-table__inner-wrapper::after) {
        height: 0;
    }
    :deep(.el-table__cell .el-table__expanded-cell) {
        border-bottom: none;
    }
    > .custom-table {
        > :deep(.el-table) {
            > .el-table__inner-wrapper {
                > .el-table__header-wrapper {
                    > .el-table__header {
                        > thead {
                            > tr {
                                th + th {
                                    // border-top: 1px solid var(--border-color);
                                    box-sizing: border-box;
                                }
                            }
                        }
                    }
                }
                > .el-table__body-wrapper {
                    > .el-scrollbar {
                        > .el-scrollbar__wrap {
                            > .el-scrollbar__view {
                                > .el-table__body {
                                    > tbody {
                                        tr {
                                            .el-table__cell {
                                                .el-table {
                                                    .el-table__body-wrapper {
                                                        .el-table__body {
                                                            tbody {
                                                                th,
                                                                tr {
                                                                    &:last-child {
                                                                        .el-table__cell {
                                                                            border-bottom: none;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
