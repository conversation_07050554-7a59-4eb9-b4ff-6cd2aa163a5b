<template>
    <div class="add-content">
        <el-form :model="formList" label-width="222px" class="formRef" ref="formRef">
            <div class="isRow">
                <el-form-item label="存货编码：">
                    <div class="row">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Code, $event, '存货编码', 'aaNum', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                            v-model="formList.aaNum"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="存货名称：">
                    <div class="row">
                        <el-input
                            v-model="formList.aaName"
                            class="input-ellipsis"
                            @blur="inputTypeBlur('aaNameInputRef')"
                            v-if="aaNameTextareaShow"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            @input="limitInputLength(formList.aaName,'存货名称')"
                            @focus="inputTypeFocus()"
                            resize="none"
                            ref="aaNameInputRef"
                        />
                        <Tooltip :content="formList.aaName" :isInput="true" placement="right" v-else>
                            <input
                                @focus="inputTypeFocus(1)"
                                ref="aaNameInputRef"
                                type="text"
                                class="input-ellipsis"
                                v-model="formList.aaName"
                            />
                        </Tooltip>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="规格型号：">
                    <div class="row">
                    <Tooltip :content="formList.aaEntity.stockModel" :is-input="true" placement="right">
                        <input
                            type="text"
                            maxlength="256"
                            class="input-ellipsis"
                            v-model="formList.aaEntity.stockModel"
                            @input="limitInputLength(formList.aaEntity.stockModel,'规格型号')"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="存货类别：">
                    <div class="row">
                        <el-select
                            v-model="formList.aaEntity.stockType"
                            placeholder=" "
                            style="width: 100%"
                        >
                            <el-option v-for="item in options" :value="item" :label="item" :key="item"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="计量单位：">
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.unit" :is-input="true" placement="right">
                            <input
                                type="text"
                                class="input-ellipsis"
                                v-model="formList.aaEntity.unit"
                                @input="handleAAInput(LimitCharacterSize.Default, $event, '计量单位', 'unit', changeFormListData)"
                                @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                            />
                        </Tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="启用日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.aaEntity.startDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateStart"
                        />
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="停用日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.aaEntity.endDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateEnd"
                        />
                    </div>
                </el-form-item>
            </div>
            <el-form-item label="备注：">
                <div class="max">
                    <el-input
                        v-model="formList.aaEntity.note"
                        class="more-long input-ellipsis"
                        @blur="inputTypeBlur('noteInputRef')"
                        v-if="noteTextareaShow"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        type="textarea"
                        maxlength="1024"
                        @input="limitInputLength(formList.aaEntity.note,'备注')"
                        @focus="inputTypeFocus()"
                        resize="none"
                        ref="aaNameInputRef"
                    />
                    <div v-else class='note-tooltip-width'>
                        <Tooltip :content="formList.aaEntity.note" :isInput="true" placement="bottom">
                            <input
                                @focus="inputTypeFocus(2)"
                                ref="aaNameInputRef"
                                type="text"
                                v-model="formList.aaEntity.note"
                                class="more-long input-ellipsis"
                            />
                        </Tooltip>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="是否启用：" class="status">
                <el-checkbox label="启用" v-model="formList.aaEntity.status" @change="handleStatusChange" />
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">返回</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, watch } from "vue";
import { createCheck, getNextAaNum, LimitCharacterSize, handleAAInput, handleAAPaste, textareaBottom } from "../utils";
import Tooltip from "@/components/Tooltip/index.vue";
import { dayjs } from "element-plus";
import { ValidataStock } from "../validator";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
const options = ["原材料","低值易耗品","在产品","半成品","库存商品"];

const formList = reactive<any>({
    aaNum: "",
    aaName: "",
    aaeID: "",
    USCC: "",
    hasEntity: true,
    aaEntity: {
        stockModel: "",
        stockType: "",
        unit: "",
        startDate: "",
        endDate: "",
        note: "",
        status: true,
    },
});
const changeFormListData = (key: string, val: string) => {
    const keys = Object.keys(formList);
    const aaEntityKeys = Object.keys(formList.aaEntity);
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            formList[key] = val;
            return;
        }
    }
    for (let i = 0; i < aaEntityKeys.length; i++) {
        if (aaEntityKeys[i] === key) {
            formList.aaEntity[key] = val;
            break;
        }
    }
};

let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => (EditType = val);
const formRef = ref<any>(null);
const emit = defineEmits(["formCancel", "formChanged", "formCancelEdit"]);

let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    const aaeID = EditType === "Edit" ? formList.aaeID : 0;
    const aaNum = formList.aaNum;
    const aaName = formList.aaName;
    createCheck(10006, aaeID, aaNum, aaName, Save);
};
const aaNameTextareaShow=ref(false)
const noteTextareaShow=ref(false)
const aaNameInputRef=ref()
const inputTypeBlur = (value:string) => {
    switch (value) {
        case 'aaNameInputRef':
            aaNameTextareaShow.value = false;
            break;
        case 'noteInputRef':
            noteTextareaShow.value = false;
            break;
    }
};
const inputTypeFocus = (num?:number) => {
    textareaBottom(formRef)
    switch (num) {
        case 1:
            aaNameTextareaShow.value = true;
            break;
        case 2:
            noteTextareaShow.value = true;
            break;
    }
    nextTick(()=>{
        if(num){
            getTextareaFocus(num)
        }
    })
};
const getTextareaFocus = (num:number) => {
    switch (num) {
        case 1:
        case 2:
            aaNameInputRef.value.focus();
            break;
    }
};
function limitInputLength(val: string,label: string) {
    switch (label) {
    case '备注':
        if (val.length === 1024) {
            ElNotify({ type: "warning", message: `亲，${label}不能超过1024个字哦~` });
        }
        break;
    case '存货名称':
    case '规格型号':
        if (val.length === 256) {
            ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
        }
        break;
    }
}
const Save = () => {
    const entityParams = {
        stockModel: formList.aaEntity.stockModel,
        stockType: formList.aaEntity.stockType,
        unit: formList.aaEntity.unit,
        startDate: formList.aaEntity.startDate,
        endDate: formList.aaEntity.endDate,
        note: formList.aaEntity.note,
    };
    const params = {
        entity: entityParams,
        aaNum: formList.aaNum,
        aaName: formList.aaName,
        uscc: formList.USCC,
        status: formList.aaEntity.status ? 0 : 1,
        hasEntity: formList.hasEntity,
        ifvoucher: true,
    };
    const urlPath = EditType === "Edit" ? "Stock?aaeid=" + formList.aaeID : "Stock";
    if (ValidataStock(entityParams, params.aaNum, params.aaName)) {
        isSaving = true;
        request({
            url: "/api/AssistingAccounting/" + urlPath,
            method: EditType === "New" ? "post" : "put",
            headers: { "Content-Type": "application/json" },
            data: JSON.stringify(params),
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000 || "Failed" === res.data) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    isSaving = false;
                    return;
                }
                ElNotify({ type: "success", message: "保存成功" });
                useAssistingAccountingStore().getAssistingAccounting(10006);
                if (EditType === "New") {
                    getNextAaNum(10006)
                        .then((res: IResponseModel<string>) => {
                            resetForm();
                            formList.aaNum = res.data;
                            emit("formCancelEdit");
                        })
                        .finally(() => {
                            isSaving = false;
                        });
                } else {
                    handleCancel();
                    isSaving = false;
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
                isSaving = false;
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};
const resetForm = () => {
    const initParams = {
        aaNum: "",
        aaName: "",
        aaeID: "",
        USCC: "",
        hasEntity: true,
        aaEntity: {
            stockModel: "",
            stockType: "",
            unit: "",
            startDate: "",
            endDate: "",
            note: "",
            status: true,
        },
    };
    editForm(initParams);
};
const editForm = (data: any) => {
    Object.keys(data).forEach((key) => {
        if (Object.keys(formList).includes(key)) {
            formList[key] = data[key];
        } else if (Object.keys(formList.aaEntity).includes(key)) {
            formList.aaEntity[key] = data[key];
        }
    });
};
watch(
    formList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
defineExpose({ resetForm, editForm, changeType });

function disabledDateStart(time: Date) {
    let endDate = dayjs(formList.aaEntity.endDate).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(formList.aaEntity.startDate).valueOf();
    return time.getTime() < startDate;
}
const handleStatusChange = (check: any) => {
    if (!check) {
        ElConfirm("亲，辅助核算项目停用后不能再在凭证中使用哦，是否确认停用？").then((r: boolean) => {
            formList.aaEntity.status = !r;
        });
    }
};
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
:deep(.el-textarea__inner){
    z-index: 1000;
}
.add-content {
    :deep(.el-form) {
        .isRow {
            &:first-child {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
}
.formRef {
    input {
        .detail-original-input(188px, 32px);
        &.middle {
            .detail-original-input(288px, 32px);
        }
        &.more-long {
            .detail-original-input(598px, 32px);
        }
        &.big {
            .detail-original-input(698px, 32px);
        }
    }
    .isRow {
        display: flex;
        align-items: center;
    }
}
</style>
