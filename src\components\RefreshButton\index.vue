<template>
    <div :class="['refresh-icon', props.marginLeft]" v-if="!isErp" @click="menuRefreshCurrent" title="刷新"></div>
</template>
<script lang="ts" setup>
import { ref, computed, toRef } from "vue";
import { useRouterArrayStoreHook, type IRouterModel } from "@/store/modules/routerArray";
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const currentPage = computed(() => {
    return routerArray.value.find((item) => item.alive);
});
const isErp = ref(window.isErp);
const props = defineProps({
    marginLeft: {
        type: String,
        default: "ml-10",
    },
    isFullRefresh: {
        type: Boolean,
        default: true,
    },
});
const isFullRefresh = computed(() => props.isFullRefresh);
const emits = defineEmits(["refresh"]);
const menuRefreshCurrent = () => {
    isFullRefresh.value ? routerArrayStore.refreshRouter(currentPage.value!.path) : emits("refresh");
};
</script>
