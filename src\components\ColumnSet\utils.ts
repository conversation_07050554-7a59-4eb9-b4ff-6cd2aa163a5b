import type { IColumnProps } from "@/components/Table/IColumnProps";
import { getLocalStorage, setLocalStorage } from "@/util/localStorageOperate";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { request, type IResponseModel } from "@/util/service";

export interface IColItem {
    columnField: string;
    columnName: string;
    isShow: boolean;
    isFreeze: boolean;
}
export interface IDataItem {
    columnField: string;
    columnName: string;
    isShow: boolean;
    isFreeze: boolean;
    no: number;
    isRequire: boolean;
}

export enum ColumnSetModuleType {
    CashJournal = 100,
    DepositJournal = 200,
    Draft = 300,
    SalesInvoice = 400,
    PurchaseInvoice = 500,
    FixedAssetsInit = 600,
    FixedAssetList = 700,
}
interface colType {  
    [key: number]: string[];  
}
const ColumnSetRequire:colType  = {
    100: ["日期", "摘要", "收支类别", "收入（借方）", "支出（贷方）", "余额", "现金账户", "币别"],
    200: ["日期", "摘要", "收支类别", "收入（借方）", "支出（贷方）", "余额", "银行账户", "币别"],
    300: ["收支类别", "出票日", "票据号", "票据金额"],
    400: ["发票种类", "发票号码", "客户名称"],
    500: ["发票种类", "发票号码", "供应商名称"],
    600: ["资产编号", "资产名称"],
    700: ["资产编号", "资产名称"],
}
export default ColumnSetRequire;

export function getColumnListApi(setModule: number, isDefault: boolean = false) {
    return request({ 
        url: "/api/ColumnSet/List",
        method: "get",
        params: {
            module: setModule,
            isDefault: isDefault,
        }
    });
}

//拖拽完成更新数据变化
export function alterArrayPos(array: any[], oldIndex: number, newIndex: number) {
    const item = array[oldIndex]; // 直接获取元素  
    array.splice(oldIndex, 1); // 移除元素  
    array.splice(newIndex, 0, { ...item }); // 在新索引插入元素
    return array;
}

export function savColumnListApi(setModule: number, dataColumnsAll: IColItem[]) {
    return request({ 
        url: "/api/ColumnSet/Save",
        method: "post",
        params: {
            module: setModule,
        },
        data: dataColumnsAll
    });
}

export const getSlotIsFreeze = (slotName: string, data: IColumnProps[]) => {
    const item = data.find((col: IColumnProps)=> col.slot === slotName);
    return item?.fixed ?? false;
}
export const getSlotClassName = (slotName: string, data: IColumnProps[]) => {
    const item = data.find((col: IColumnProps)=> col.slot === slotName);
    return item?.className ?? "";
}
//筛选出isShow为true的项用于页面展示
export const getShowColumn = (columns: IColItem[], data: IColumnProps[]) => {
    const list = columns.filter(item => item.isShow);
    let result = data.filter(v =>  
        list.some(item => item.columnName === v.label)  
    );
    for (let i=0; i<result.length; i++) {
        for (let j=0; j<list.length; j++) {
            if (result[i].label === list[j].columnName) {
                if (list[j].isFreeze) {
                    result[i]["fixed"] = "left";
                } else if (result[i]["fixed"]) {
                    delete result[i]["fixed"]; 
                }
            }
        }
    }
    const orginList = list.filter(v =>  
        data.some(item => item.label === v.columnName)  
    );
    orginList.forEach((item, index) => {
        const findIndex = result.findIndex((v) => v.label === item.columnName);
        if (index !== findIndex) {
            result = alterArrayPos(result, findIndex, index);
        }
    })
    if (data[0].slot === "selection" || data[0].slot === "select") {
        result.unshift(data[0]);
    }
    if (data[data.length-1].slot === "operation") {
        result.push(data[data.length -1]);
    }
    return result;
}
//列宽
export const getColumnWidth = (tableName: string, colName: string, oldWidth?: number) => {
    if (!colName || !tableName) {
        return 0;
    }
    const key = useAccountSetStoreHook().userInfo?.userSn + "-" + getGlobalToken() + "-ColWidth";
    let obj:any  = {};
    if (getLocalStorage(key)) {
        obj = JSON.parse(getLocalStorage(key) as string);
    }
    return obj[tableName]?.[colName] ?? (oldWidth || 0);
}
//保存列宽
export const saveColWidth = (tableName: string, newWidth: number, prop: string) => {
    if (!tableName) return;
    let obj:any = {};
    const key = useAccountSetStoreHook().userInfo?.userSn + "-" + getGlobalToken() + "-ColWidth";
    if (getLocalStorage(key)) {
        obj = JSON.parse(getLocalStorage(key) as string);
    }
    if (obj[tableName] === undefined) {
        obj[tableName] = {};
    }
    obj[tableName][prop] = newWidth;
    setLocalStorage(key, JSON.stringify(obj));
}