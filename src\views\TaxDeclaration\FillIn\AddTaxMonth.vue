<!-- 增值税月报 -->
<template>
  <category-tabs
    v-if="tabList.length"
    class="main-content-body"
    :tab-list="tabList"
    :show-total="true"
    @tab-click="handleTabClick"
    @scroll="handleScroll">
    <template #content="{ activeIndex, tabData }">
      <div class="declaration-form">
        <div class="form-title">{{ tabData.describe || "增值税及附加税费申报表" }}</div>
        <div
          class="form-subtitle"
          v-if="tabData.describe">
          {{ tabData.describe || "（适用已执行新金融准则、新收入准则和新租赁准则的一般企业）" }}
        </div>
        <div class="form-info">
          <div class="info">
            <div
              v-for="(item, index) in addTaxMonthForm"
              :key="index">
              <span class="label">{{ item.label }}：</span>
              <span class="value">{{ item.value }}</span>
            </div>
          </div>
          <span>金额单位：{{ 100000000000000000 }}元，至角分</span>
        </div>
      </div>
      <div
        v-if="tabData.title === '主表'"
        class="main-table">
        <el-tabs
          v-model="innerActiveTab"
          @tab-change="handleInnerTabChange">
          <el-tab-pane
            v-for="(item, index) in mainTableTabList"
            :key="index"
            :label="item.title"
            :name="item.className"></el-tab-pane>
        </el-tabs>
        <div
          v-for="(item, index) in mainTableTabList"
          :key="index"
          :class="`tab-table-${item.className}`"
          style="scroll-margin-top: 60px">
          <div
            v-if="index !== 0"
            class="title">
            {{ item.title }}
          </div>
          <LMTable
            row-key="id"
            :columns="mainTableColumns"
            :data="item.data">
            <template #subTitle="{ slotColumn }">
              <el-table-column v-bind="slotColumn">
                <template #default="{ row }">
                  <span style="white-space: pre-wrap">{{ row[slotColumn.prop] }}</span>
                </template>
              </el-table-column>
            </template>
            <template #input="{ slotColumn }">
              <el-table-column v-bind="slotColumn">
                <template #default="{ row }">
                  <add-month-cell-content
                    v-model="row[slotColumn.prop]"
                    :custom-table="false"
                    :row="row"
                    :tab-data="tabData"
                    :slot-column="slotColumn"
                    @blur="handleInputBlur"
                    @formula="generateFormula"></add-month-cell-content>
                  <!-- <Popover
                    v-if="row.formula.split(',')[slotColumn.columnIndex] == 1"
                    placement="right"
                    :use-slot-content="true">
                    <template #trigger>
                      <div class="equal-icon" />
                      <span style="float: right; line-height: 12px">
                        {{ String(parseFloat(row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                      </span>
                    </template>
                    <template #content>
                      <div v-html="generateFormula(row, tabData, slotColumn)"></div>
                    </template>
                  </Popover>
                  <template v-else>
                    <checkable-input
                      v-if="row.readonly.split(',')[slotColumn.columnIndex] == 0"
                      v-model="row[slotColumn.prop]"
                      @blur="handleInputBlur($event, row, tabData, slotColumn.prop)"></checkable-input>
                    <span v-else-if="row.readonly.split(',')[slotColumn.columnIndex] == 1">
                      {{ String(parseFloat(row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                    </span>
                    <span v-else>--</span>
                  </template> -->
                </template>
              </el-table-column>
            </template>
          </LMTable>
          <el-divider v-if="index !== mainTableTabList.length - 1" />
        </div>
      </div>
      <LMTable
        row-key="id"
        v-else-if="['附列资料一', '附列资料三'].includes(tabData.title)"
        :columns="tabData.columns"
        :span-method="tabData.spanMethod || function () {}"
        :data="tabData.data"
        :header-cell-style="tabData.headerCellStyle"
        scrollbar-always-on>
        <template #input="{ slotColumn }">
          <el-table-column v-bind="slotColumn">
            <template #default="{ row }">
              <add-month-cell-content
                v-model="row[slotColumn.prop]"
                :custom-table="false"
                :row="row"
                :tab-data="tabData"
                :slot-column="slotColumn"
                @blur="handleInputBlur"
                @formula="generateFormula"></add-month-cell-content>
              <!-- <Popover
                v-if="row.formula.split(',')[slotColumn.columnIndex] == 1"
                placement="right"
                :use-slot-content="true">
                <template #trigger>
                  <div class="equal-icon" />
                  <span style="float: right; line-height: 12px">
                    {{ String(parseFloat(row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                  </span>
                </template>
                <template #content>
                  <div v-html="generateFormula(row, tabData, slotColumn)"></div>
                </template>
              </Popover>
              <template v-else>
                <checkable-input
                  v-if="row.readonly.split(',')[slotColumn.columnIndex] == 0"
                  v-model="row[slotColumn.prop]"
                  @blur="handleInputBlur($event, row, tabData, slotColumn.prop)"></checkable-input>
                <span v-else-if="row.readonly.split(',')[slotColumn.columnIndex] == 1">
                  {{ String(parseFloat(row[slotColumn.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                </span>
                <span v-else>--</span>
              </template> -->
            </template>
          </el-table-column>
        </template>
      </LMTable>

      <div
        class="appendix-2 native-table"
        v-if="tabData.title === '附列资料二'">
        <table
          cellpadding="10"
          cellspacing="0">
          <thead>
            <tr>
              <th colspan="5">一、申报抵扣的进项税额</th>
            </tr>
            <tr>
              <th>项目</th>
              <th>栏次</th>
              <th>份数</th>
              <th>金额</th>
              <th>税额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tabData.data![0]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Props1"
                :key="innerIndex"
                :style="{ textAlign: (innerItem.align || 'left') as any }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <Popover
                  v-if="innerItem.columnIndex && item.formula.split(',')[innerItem.columnIndex] == 1"
                  placement="right"
                  :use-slot-content="true">
                  <template #trigger>
                    <div class="equal-icon" />
                    <span style="float: right; line-height: 12px">
                      {{ String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                    </span>
                  </template>
                  <template #content>
                    <div v-html="generateFormula(item, tabData, innerItem)"></div>
                  </template>
                </Popover>
                <template v-else>
                  <checkable-input
                    v-if="innerItem.columnIndex && item.readonly.split(',')[innerItem.columnIndex] == 0"
                    v-model="item[innerItem.prop]"
                    @blur="handleInputBlur($event, item, tabData, innerItem.prop)"></checkable-input>
                  <span v-else-if="!innerItem.columnIndex || item.readonly.split(',')[innerItem.columnIndex] == 1">
                    {{
                      !innerItem.columnIndex
                        ? item[innerItem.prop]
                        : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }}
                  </span>
                  <span v-else>--</span>
                </template> -->
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="5">二、进项税额转出额</th>
            </tr>
            <tr>
              <th>项目</th>
              <th>栏次</th>
              <th colspan="3">税额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tabData.data![1]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Props2"
                :key="innerIndex"
                :colspan="innerIndex !== attachedInfo2Props2.length - 1 ? 1 : 3"
                :style="{ textAlign: (innerItem.align || 'left') as any }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <Popover
                  v-if="innerItem.columnIndex && item.formula.split(',')[innerItem.columnIndex] == 1"
                  placement="right"
                  :use-slot-content="true">
                  <template #trigger>
                    <div class="equal-icon" />
                    <span style="float: right; line-height: 12px">
                      {{ String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                    </span>
                  </template>
                  <template #content>
                    <div v-html="generateFormula(item, tabData, innerItem)"></div>
                  </template>
                </Popover>
                <template v-else>
                  <checkable-input
                    v-if="'columnIndex' in innerItem && item.readonly == 0"
                    v-model="item[innerItem.prop]"
                    @blur="handleInputBlur($event, item, tabData, innerItem.prop)"></checkable-input>
                  <span v-else-if="!('columnIndex' in innerItem) || item.readonly == 1">
                    {{
                      !innerItem.columnIndex
                        ? item[innerItem.prop]
                        : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }}
                  </span>
                  <span v-else>--</span>
                </template> -->
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="5">三、待抵扣进项税额</th>
            </tr>
            <tr>
              <th>项目</th>
              <th>栏次</th>
              <th>份数</th>
              <th>金额</th>
              <th>税额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tabData.data![2]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Props1"
                :key="innerIndex"
                :style="{ textAlign: (innerItem.align || 'left') as any }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <Popover
                  v-if="innerItem.columnIndex && item.formula.split(',')[innerItem.columnIndex] == 1"
                  placement="right"
                  :use-slot-content="true">
                  <template #trigger>
                    <div class="equal-icon" />
                    <span style="float: right; line-height: 12px">
                      {{ String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",") }}
                    </span>
                  </template>
                  <template #content>
                    <div v-html="generateFormula(item, tabData, innerItem)"></div>
                  </template>
                </Popover>
                <template v-else>
                  <checkable-input
                    v-if="innerItem.columnIndex && item.readonly.split(',')[innerItem.columnIndex] == 0"
                    v-model="item[innerItem.prop]"
                    @blur="handleInputBlur($event, item, tabData, innerItem.prop)"></checkable-input>
                  <span v-else-if="!innerItem.columnIndex || item.readonly.split(',')[innerItem.columnIndex] == 1">
                    {{
                      !innerItem.columnIndex || item[innerItem.prop] === null
                        ? item[innerItem.prop]
                        : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }}
                  </span>
                  <span v-else>--</span>
                </template> -->
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="5">四、其他</th>
            </tr>
            <tr>
              <th>项目</th>
              <th>栏次</th>
              <th>份数</th>
              <th>金额</th>
              <th>税额</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tabData.data![3]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Props1"
                :key="innerIndex"
                :style="{ textAlign: (innerItem.align || 'left') as any }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <checkable-input
                  v-if="innerItem.columnIndex && item.readonly.split(',')[innerItem.columnIndex] == 0"
                  v-model="item[innerItem.prop]"
                  @blur="handleInputBlur($event, item, tabData, innerItem.prop)"></checkable-input>
                <span v-else-if="!innerItem.columnIndex || item.readonly.split(',')[innerItem.columnIndex] == 1">
                  {{
                    !innerItem.columnIndex
                      ? item[innerItem.prop]
                      : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </span>
                <span v-else>--</span> -->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        v-if="tabData.title === '附列资料四'"
        class="appendix-4 native-table">
        <table
          cellpadding="10"
          cellspacing="0">
          <thead>
            <tr>
              <th colspan="8">一、税额抵减情况</th>
            </tr>
            <tr>
              <th rowspan="2">序号</th>
              <th rowspan="2">抵减项目</th>
              <th>期初余额</th>
              <th>本期发生额</th>
              <th>本期应抵减税额</th>
              <th>本期实际抵减税额</th>
              <th colspan="2">期末余额</th>
            </tr>
            <tr>
              <th>1</th>
              <th>2</th>
              <th>3=1+2</th>
              <th>4≤3</th>
              <th colspan="2">5=3-4</th>
            </tr>
          </thead>
          <colgroup>
            <col style="width: 45px" />
          </colgroup>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tabData.data![0]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo4Props1"
                :key="innerIndex"
                :colspan="innerIndex === attachedInfo4Props1.length - 1 ? 2 : 1"
                :style="{ textAlign: (innerItem.align || 'left') as any }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <checkable-input
                  v-if="innerItem.columnIndex && item.readonly.split(',')[innerItem.columnIndex] == 0"
                  v-model="item[innerItem.prop]"
                  @blur="handleInputBlur($event, item, tabData, innerItem.prop)"></checkable-input>
                <span v-if="!innerItem.columnIndex || item.readonly.split(',')[innerItem.columnIndex] == 1">
                  {{
                    !innerItem.columnIndex
                      ? item[innerItem.prop]
                      : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </span> -->
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="8">二、加计抵减情况</th>
            </tr>
            <tr>
              <th rowspan="2">序号</th>
              <th rowspan="2">抵减项目</th>
              <th>期初余额</th>
              <th>本期发生额</th>
              <th>本期调减额</th>
              <th>本期可抵减额</th>
              <th>本期实际抵减税额</th>
              <th>期末余额</th>
            </tr>
            <tr>
              <th>1</th>
              <th>2</th>
              <th>3</th>
              <th>4=1+2+3</th>
              <th>5</th>
              <th>6=4-5</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tabData.data![1]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo4Props2"
                :key="innerIndex"
                :style="{ textAlign: (innerItem.align || 'left') as any }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <checkable-input
                  v-if="innerItem.columnIndex && item.readonly.split(',')[innerItem.columnIndex] == 0"
                  v-model="item[innerItem.prop]"
                  @blur="handleInputBlur($event, item, tabData, innerItem.prop)"></checkable-input>
                <span v-if="!innerItem.columnIndex || item.readonly.split(',')[innerItem.columnIndex] == 1">
                  {{
                    !innerItem.columnIndex
                      ? item[innerItem.prop]
                      : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </span> -->
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        v-if="activeIndex === 5"
        class="appendix-5 native-table">
        <table
          cellpadding="10"
          cellspacing="0">
          <colgroup>
            <col />
            <col />
          </colgroup>
          <thead>
            <tr>
              <th colspan="2">被冲红所属期起</th>
              <td colspan="6">{{ (tabData.data as any).statementExtension.bchPeriodBegin }}</td>
              <th colspan="3">被冲红所属期止</th>
              <td colspan="6">{{ (tabData.data as any).statementExtension.bchPeriodEnd }}</td>
            </tr>
            <tr>
              <th colspan="2">减征政策适用主体</th>
              <td colspan="3">
                <el-checkbox
                  v-model="(tabData.data as any).statementExtension.jzzcType"
                  disabled
                  label="个体工商户"
                  true-value="22" />
                <el-checkbox
                  v-model="(tabData.data as any).statementExtension.jzzcType"
                  disabled
                  label="小型微利企业"
                  true-value="21" />
              </td>
              <th colspan="3">本期是否适用小微企业“六税两费”减征政策</th>
              <td colspan="3">
                <el-radio-group v-model="(tabData.data as any).statementExtension.satisfyJzzc">
                  <el-radio value="Y">是</el-radio>
                  <el-radio value="N">否</el-radio>
                </el-radio-group>
              </td>
              <th colspan="3">适用减征政策起止时间</th>
              <td colspan="3">
                {{ (tabData.data as any).statementExtension.jzzcPeriodBegin }}至{{ (tabData.data as any).statementExtension.jzzcPeriodEnd }}
              </td>
            </tr>
          </thead>
          <thead>
            <tr>
              <th
                rowspan="4"
                colspan="2">
                税（费）种
              </th>
            </tr>
            <tr>
              <th colspan="4">计税（费）依据</th>
              <th rowspan="2">税（费）率（%）</th>
              <th rowspan="2">本期应纳税（费）额</th>
              <th colspan="2">本期减免税（费）额</th>
              <th colspan="3">小微企业“六税两费”减征政策</th>
              <th colspan="2">试点建设培育产教融合型企业</th>
              <th rowspan="2">本期已缴税（费）额</th>
              <th rowspan="2">本期应补（退）税（费）额</th>
            </tr>
            <tr>
              <th>增值税税额</th>
              <th>增值税限额减免金额</th>
              <th>增值税免抵税额</th>
              <th>留抵退税本期扣除额</th>
              <th>减免性质代码</th>
              <th>减免税（费）额</th>
              <th>减免性质代码</th>
              <th>减征比例（%）</th>
              <th>减征额</th>
              <th>减免性质代码</th>
              <th>本期抵免金额</th>
            </tr>
            <tr>
              <th>1</th>
              <th>2</th>
              <th>3</th>
              <th>4</th>
              <th>5</th>
              <th>6=（1+2+3-4）×5</th>
              <th>7</th>
              <th>8</th>
              <th></th>
              <th>9</th>
              <th>10</th>
              <th>11</th>
              <th>12</th>
              <th>13</th>
              <th>14=6-8-10-12-13</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in (tabData.data as any).statementDatas[0]"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo5Props"
                :key="innerIndex">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :row="item"
                  :tab-data="tabData"
                  :slot-column="innerItem"
                  @blur="handleInputBlur"
                  @formula="generateFormula"></add-month-cell-content>
                <!-- <checkable-input
                  v-if="innerItem.columnIndex && item.readonly.split(',')[innerItem.columnIndex] == 0"
                  v-model="item[innerItem.prop]"></checkable-input>
                <span v-else-if="!innerItem.columnIndex || item.readonly.split(',')[innerItem.columnIndex] == 1">
                  {{
                    !innerItem.columnIndex || isNaN(item[innerItem.prop])
                      ? item[innerItem.prop]
                      : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                </span>
                <span v-else>--</span> -->
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr>
              <th
                rowspan="4"
                colspan="5">
                本期是否适用试点建设培育产教融合型企业抵免政策
              </th>
              <td
                rowspan="4"
                colspan="2">
                <el-radio-group v-model="(tabData.data as any).statementExtension.educationExemption">
                  <el-radio value="Y">是</el-radio>
                  <el-radio value="N">否</el-radio>
                </el-radio-group>
              </td>
            </tr>
            <tr>
              <th colspan="7">当期新增投资额</th>
              <th>5</th>
              <td colspan="2">{{ (tabData.data as any).statementExtension.currentInvestment }}</td>
            </tr>
            <tr>
              <th colspan="7">上期留抵可抵免金额</th>
              <th>6</th>
              <td colspan="2">{{ (tabData.data as any).statementExtension.lastExemption }}</td>
            </tr>
            <tr>
              <th colspan="7">结转下期可抵免金额</th>
              <th>7</th>
              <td colspan="2">{{ (tabData.data as any).statementExtension.nextExemption }}</td>
            </tr>
            <tr>
              <th
                rowspan="4"
                colspan="7">
                可用于扣除的增值税留抵退税额使用情况
              </th>
            </tr>
            <tr>
              <th colspan="7">当期新增可用于扣除的留抵退税额</th>
              <th>8</th>
              <td colspan="2">{{ (tabData.data as any).statementExtension.currentDeductionRebate }}</td>
            </tr>
            <tr>
              <th colspan="7">上期结存可用于扣除的留抵退税额</th>
              <th>9</th>
              <td colspan="2">{{ (tabData.data as any).statementExtension.lastDeductionRebate }}</td>
            </tr>
            <tr>
              <th colspan="7">结转下期可用于扣除的留抵退税额</th>
              <th>10</th>
              <td colspan="2">{{ (tabData.data as any).statementExtension.nextDeductionRebate }}</td>
            </tr>
            <tr>
              <th colspan="4">计税（费）依据修改原因</th>
              <td colspan="3">{{ (tabData.data as any).statementExtension.modifyTaxBasisValue }}</td>
              <th colspan="7">其他修改原因</th>
              <td colspan="3">{{ (tabData.data as any).statementExtension.otherModifyReason }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="appendix-6 native-table"
        v-if="tabData.title === '增值税减免税申报明细表'">
        <table
          cellpadding="10"
          cellspacing="0">
          <thead>
            <tr>
              <th colspan="6">一、减税项目</th>
            </tr>
            <tr>
              <th rowspan="3">减税性质及代码</th>
            </tr>
            <tr>
              <th>期初余额</th>
              <th>本期发生额</th>
              <th>本期应抵减税额</th>
              <th>本期实际抵减税额</th>
              <th>期末余额</th>
            </tr>
            <tr>
              <th>1</th>
              <th>2</th>
              <th>3=1+2</th>
              <th>4≤3</th>
              <th>5=3-4</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="dynamic-row"
              v-for="(item, index) in tabData.data![0]"
              :key="index">
              <template
                v-for="(innerItem, innerIndex) in attachedInfo6Props1"
                :key="innerItem.prop">
                <th v-if="innerIndex === 0 && index === 0">
                  {{
                    isNaN(item[innerItem.prop])
                      ? item[innerItem.prop]
                      : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                  <el-icon
                    color="#43B449"
                    class="add"
                    @click="taxReductionAdd(tabData.data![0])">
                    <CirclePlusFilled />
                  </el-icon>
                </th>
                <td
                  v-else
                  :style="{ textAlign: (innerItem.align || 'left') as any }">
                  <LMSelect
                    v-if="innerItem.columnIndex === '0'"
                    v-model="item[innerItem.prop]"
                    placeholder="请选择"
                    clearable>
                    <LMOption></LMOption>
                  </LMSelect>
                  <template v-else>
                    <checkable-input
                      v-if="item.readonly.split(',')[innerItem.columnIndex] == 0"
                      v-model="item[innerItem.prop]"></checkable-input>
                    <span v-else>
                      {{
                        isNaN(item[innerItem.prop])
                          ? item[innerItem.prop]
                          : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                      }}
                    </span>
                  </template>
                </td>
              </template>
              <el-icon
                v-if="tabData.data![0].length > 2"
                class="minus"
                color="#F6404E"
                @click="tabData.data![0].splice(index, 1)">
                <RemoveFilled />
              </el-icon>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="6">二、免税项目</th>
            </tr>
            <tr>
              <th rowspan="3">免税性质及代码</th>
            </tr>
            <tr>
              <th>免征增值税项目销售额</th>
              <th>免税销售额扣除项目本期实际扣除金额</th>
              <th>扣除后免税销售额</th>
              <th>免税销售额对应的进项税额</th>
              <th>免税额</th>
            </tr>
            <tr>
              <th>1</th>
              <th>2</th>
              <th>3=1-2</th>
              <th>4</th>
              <th>5</th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="dynamic-row"
              v-for="(item, index) in tabData.data![1]"
              :key="index">
              <template
                v-for="(innerItem, innerIndex) in attachedInfo6Props2"
                :key="innerItem.prop">
                <th v-if="(index as number) < 2 && innerIndex === 0">
                  {{ item[innerItem.prop] }}
                  <el-icon
                    v-if="index === 0"
                    color="#43B449"
                    class="add"
                    @click="taxFreeAdd(tabData.data![1])">
                    <CirclePlusFilled />
                  </el-icon>
                </th>
                <td
                  v-else
                  :style="{ textAlign: (innerItem.align || 'left') as any }">
                  <LMSelect
                    v-if="innerItem.columnIndex === '0' && (index as number) > 2"
                    v-model="item[innerItem.prop]"
                    placeholder="请选择"
                    clearable>
                    <LMOption></LMOption>
                  </LMSelect>
                  <checkable-input
                    v-else-if="item.readonly.split(',')[innerItem.columnIndex] == 0"
                    v-model="item[innerItem.prop]"></checkable-input>
                  <span v-else-if="item.readonly.split(',')[innerItem.columnIndex] == 1">
                    {{
                      isNaN(item[innerItem.prop])
                        ? item[innerItem.prop]
                        : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }}
                  </span>
                  <span v-else>--</span>
                </td>
              </template>
              <el-icon
                v-if="tabData.data![1].length > 4 && (index as number) > 2"
                class="minus"
                color="#F6404E"
                @click="tabData.data![1].splice(index, 1)">
                <RemoveFilled />
              </el-icon>
            </tr>
          </tbody>
        </table>
      </div>
    </template>
  </category-tabs>
</template>
<script setup lang="ts">
  import CategoryTabs from "../components/CategoryTabs.vue"
  import type { TabObj } from "../components/types"
  import { getAddTaxMonthData } from "@/api/taxDeclaration"
  import type { IColumnProps } from "@/components/Table/types"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { storeToRefs } from "pinia"
  import AddMonthCellContent from "../components/AddMonthCellContent.vue"

  onMounted(() => {
    // 用于处理横向滚动条样式
    setTimeout(() => {
      const mainContentBody = document.querySelector(".main-content-body") as HTMLElement
      mainContentBody.style.transform = "translate3d(0, 0, 0)"
    }, 2000)
  })

  onBeforeUnmount(() => {
    const mainContentBody = document.querySelector(".main-content-body") as HTMLElement
    mainContentBody.style.transform = ""
  })

  const props = withDefaults(defineProps<{ currentTaxInfo?: { [key: string]: any } }>(), {
    currentTaxInfo: () => ({}),
  })

  // 接口回传数据
  const tabList = ref<TabObj[]>([])
  function getTabList() {
    getAddTaxMonthData("/init").then((res) => {
      if (res.state === 1000) {
        tabList.value = res.data.declareDetailList.map((da: any) => {
          const index = allTabList.value.findIndex((item) => item.title === da.statementTitle)
          ;(allTabList.value[index] as { [key: string]: any }).func()

          return {
            id: da.statementCode,
            describe: da.statementSubTitle,
            isRequired: da.required,
            ...allTabList.value[index],
            data: toRef(allTabList.value[index], "data"),
          }
        })
        console.log(tabList.value, "tabList")
      }
    })
  }
  // 所有表格公式
  const allTableFormulaList: { [key: string]: any } = ref({})
  // 获取所有公式信息
  function getFormula() {
    getAddTaxMonthData("/formulasAndRules").then((res) => {
      if (res.state === 1000) {
        allTableFormulaList.value = res.data
        getTabList()
      }
    })
  }
  getFormula()

  const { basicInfo } = storeToRefs(useBasicInfoStore())
  let formData = reactive([
    { label: "纳税人名称（公章）", value: basicInfo.value.taxpayerName },
    { label: "税款所属期", value: `${props.currentTaxInfo.periodStart}--${props.currentTaxInfo.periodEnd}` },
    { label: "填表日期", value: new Date().toISOString().slice(0, 10) },
  ])
  let addTaxMonthForm = reactive<Array<{ [key: string]: any }>>(formData)
  function handleTabClick(row: TabObj, index: number) {
    // 仅主表展示填表日期
    if (index !== 0) {
      addTaxMonthForm = formData.slice(0, 2)
    } else {
      addTaxMonthForm = formData
    }
  }

  // 主表表头
  const mainTableColumns = [
    { label: "项目", prop: "subTitle", slot: "subTitle" },
    { label: "栏次", prop: "rowDisplay" },
    {
      label: "一般项目",
      children: [
        { label: "本月数", prop: "periodGeneral", columnIndex: 0, slot: "input", align: "right" },
        { label: "本年累计数", prop: "totalGeneral", columnIndex: 1, slot: "input", align: "right" },
      ],
    },
    {
      label: "即征即退项目",
      children: [
        { label: "本月数", prop: "periodReturn", columnIndex: 2, slot: "input", align: "right" },
        { label: "本年累计数", prop: "totalReturn", columnIndex: 3, slot: "input", align: "right" },
      ],
    },
  ]
  const mainTableTabList = ref([
    {
      title: "销售额",
      className: "salesVolume",
      data: [],
    },
    {
      title: "税款计算",
      className: "totalTaxAmount",
      data: [],
    },
    {
      title: "税款缴纳",
      className: "taxPayment",
      data: [],
    },
    {
      title: "附加税费",
      className: "additionalTaxes",
      data: [],
    },
  ])
  const innerActiveTab = ref("salesVolume")

  // 附表一表头
  const attachedInfo1Columns: IColumnProps[] = [
    { label: "项目及栏次", prop: "title", fixed: "left" as "left", align: "center" as "center" },
    { label: "项目及栏次", prop: "subTitle", fixed: "left" as "left" },
    { label: "项目及栏次", prop: "thdTitle", fixed: "left" as "left" },
    { label: "项目及栏次", prop: "rowDisplay", fixed: "left" as "left" },
    {
      label: "开具增值税专用发票",
      children: [
        {
          label: "销售额",
          children: [{ label: "1", prop: "prefessionalInvoiceSalesAmt", columnIndex: 0, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "销项（应纳）税额",
          children: [{ label: "2", prop: "prefessionalInvoiceTaxAmt", columnIndex: 1, slot: "input", width: 180, align: "right" }],
        },
      ],
    },
    {
      label: "开具其他发票",
      children: [
        {
          label: "销售额",
          children: [{ label: "3", prop: "otherInvoiceSalesAmt", columnIndex: 2, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "销项（应纳）税额",
          children: [{ label: "4", prop: "otherInvoiceTaxAmt", columnIndex: 3, slot: "input", width: 180, align: "right" }],
        },
      ],
    },
    {
      label: "未开具发票",
      children: [
        {
          label: "销售额",
          children: [{ label: "5", prop: "notIssuedInvoiceSalesAmt", columnIndex: 4, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "销项（应纳）税额",
          children: [{ label: "6", prop: "notIssuedInvoiceTaxAmt", columnIndex: 5, slot: "input", width: 180, align: "right" }],
        },
      ],
    },
    {
      label: "纳税检查调整",
      children: [
        {
          label: "销售额",
          children: [{ label: "7", prop: "taxCheckAdjustSalesAmt", columnIndex: 6, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "销项（应纳）税额",
          children: [{ label: "8", prop: "taxCheckAdjustTaxAmt", columnIndex: 7, slot: "input", width: 180, align: "right" }],
        },
      ],
    },
    {
      label: "合计",
      children: [
        {
          label: "销售额",
          children: [{ label: "9=1+3+5+7+9", prop: "totalSalesAmt", columnIndex: 8, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "销项（应纳）税额",
          children: [{ label: "10=2+4+6+8", prop: "totalOutputTaxAmt", columnIndex: 9, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "价税合计",
          children: [{ label: "11=9+10", prop: "totalPriceTaxAmt", columnIndex: 10, slot: "input", width: 180, align: "right" }],
        },
      ],
    },
    {
      label: "服务、不动产和无形资产扣除项目本期实际扣除金额",
      children: [
        {
          label: "",
          children: [{ label: "12", prop: "serviceActuallyOffAmt", columnIndex: 11, slot: "input", width: 180, align: "right" }],
        },
      ],
    },
    {
      label: "扣除后",
      children: [
        {
          label: "含税（免税）销售额",
          children: [{ label: "13=11-12", prop: "afterOffSalesAmt", columnIndex: 12, slot: "input", width: 180, align: "right" }],
        },
        {
          label: "销项（应纳）税额",
          children: [
            {
              label: "14=13÷（100%+税率或征收率）×税率或征收率",
              prop: "afterOffOutputTaxAmt",
              columnIndex: 13,
              slot: "input",
              width: 180,
              align: "right",
            },
          ],
        },
      ],
    },
  ]
  // 附表一表头合并处理
  function attachedInfo1HeaderCellStyle({ column, rowIndex, columnIndex }: { column: any; rowIndex: number; columnIndex: number }) {
    nextTick(() => {
      if (rowIndex === 0) {
        if (columnIndex === 0) {
          document.querySelector(`.${column.id}`)!.setAttribute("colSpan", "4")
        } else if (columnIndex > 0 && columnIndex <= 3) {
          ;(document.querySelector(`.${column.id}`) as HTMLElement).style.display = "none"
        } else if (columnIndex === 9) {
          document.querySelector(`.${column.id}`)!.setAttribute("rowSpan", "2")
        }
      }
      if (rowIndex === 1 && columnIndex === 11) {
        ;(document.querySelector(`.${column.id}`) as HTMLElement).style.display = "none"
      }
    })
  }
  // 附表一处理数据合并
  function attachedInfo1SpanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
    const spanMap: { [key: string]: any } = {
      0: { 0: [7, 1], 7: [11, 1], 18: [2, 1], 20: [2, 1] },
      1: { 0: [5, 1], 5: [2, 1], 7: [9, 1], 16: [2, 1], 18: [1, 2], 19: [1, 2], 20: [1, 2], 21: [1, 2] },
    }
    if (spanMap[columnIndex] && spanMap[columnIndex][rowIndex]) {
      return spanMap[columnIndex][rowIndex]
    }

    const notShow: { [key: string]: any } = {
      0: [
        [0, 7],
        [7, 18],
        [18, 20],
        [20, 22],
      ],
      1: [
        [0, 5],
        [5, 7],
        [7, 16],
        [16, 18],
      ],
      2: [[17, 22]],
    }
    if (notShow[columnIndex] && notShow[columnIndex].some((item: number[]) => item[0] < rowIndex && rowIndex < item[1])) {
      return [0, 0]
    }
  }
  // 附表二列对应变量
  const attachedInfo2Props1 = [
    { prop: "title" },
    { prop: "rowDisplay" },
    { prop: "count", columnIndex: "0", align: "right" },
    { prop: "amount", columnIndex: "1", align: "right" },
    { prop: "taxAmt", columnIndex: "2", align: "right" },
  ]
  const attachedInfo2Props2 = [{ prop: "title" }, { prop: "rowDisplay" }, { prop: "taxAmt", columnIndex: "0", align: "right" }]
  // 附表二数据
  // const attachedInfo2Data = ref<{ [key: string]: any }[]>([])

  // 附表三表头
  const attachedInfo3Columns: IColumnProps[] = [
    { label: "项目及栏次", prop: "title" },
    { label: "项目及栏次1", prop: "rowDisplay", width: 40 },
    {
      label: "本期服务、不动产和无形资产价税合计额（免税销售额）",
      children: [{ label: "", children: [{ label: "1", prop: "periodServiceTaxPrice", slot: "input", columnIndex: 0, align: "right" }] }],
    },
    {
      label: "服务、不动产和无形资产扣除项目",
      children: [
        { label: "期初余额", children: [{ label: "2", prop: "periodStartAmount", slot: "input", columnIndex: 1, align: "right" }] },
        { label: "本期发生额", children: [{ label: "3", prop: "periodAmount", slot: "input", columnIndex: 2, align: "right" }] },
        {
          label: "本期应扣除余额",
          children: [{ label: "4=2+3", prop: "periodShouldMinusAmount", slot: "input", columnIndex: 3, align: "right" }],
        },
        {
          label: "本期实际扣除金额",
          children: [{ label: "5（5≤1且5≤4）", prop: "periodActualMinusAmount", slot: "input", columnIndex: 4, align: "right" }],
        },
        { label: "期末余额", children: [{ label: "6=4-5", prop: "periodEndAmount", slot: "input", columnIndex: 5, align: "right" }] },
      ],
    },
  ]
  // 附表三表头合并处理
  function attachedInfo3HeaderCellStyle({ column, rowIndex, columnIndex }: { column: any; rowIndex: number; columnIndex: number }) {
    nextTick(() => {
      if (rowIndex === 0 && columnIndex === 0) {
        document.querySelector(`.${column.id}`)!.setAttribute("colSpan", "2")
      } else if (rowIndex === 0 && columnIndex === 1) {
        ;(document.querySelector(`.${column.id}`) as HTMLElement).style.display = "none"
      }
      if (rowIndex === 0 && columnIndex === 2) {
        document.querySelector(`.${column.id}`)!.setAttribute("rowSpan", "2")
      } else if (rowIndex === 1 && columnIndex === 0) {
        ;(document.querySelector(`.${column.id}`) as HTMLElement).style.display = "none"
      }
    })
  }
  // 附表3表数据合并处理
  function attachedInfo3SpanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
    if (rowIndex === 8) {
      if (columnIndex === 0) {
        return [1, 8]
      } else {
        return [0, 0]
      }
    }
  }
  // 附表四表格列变量
  const attachedInfo4Props1 = [
    { prop: "rowDisplay" },
    { prop: "title" },
    { prop: "periodStartAmount", columnIndex: "0", align: "right" },
    { prop: "periodAmount", columnIndex: "1", align: "right" },
    { prop: "periodShouldMinusAmount", columnIndex: "2", align: "right" },
    { prop: "periodActualMinusAmount", columnIndex: "3", align: "right" },
    { prop: "periodEndAmount", columnIndex: "4", align: "right" },
  ]
  const attachedInfo4Props2 = [
    { prop: "rowDisplay" },
    { prop: "title" },
    { prop: "periodStartAmount", columnIndex: "0", align: "right" },
    { prop: "periodAmount", columnIndex: "1", align: "right" },
    { prop: "periodReductionAmount", columnIndex: "2", align: "right" },
    { prop: "periodShouldMinusAmount", columnIndex: "3", align: "right" },
    { prop: "periodActualMinusAmount", columnIndex: "4", align: "right" },
    { prop: "periodEndAmount", columnIndex: "5", align: "right" },
  ]
  // const attachedInfo4Data = ref<{ [key: string]: any }[]>([])
  // 附表5表格列对应变量
  const attachedInfo5Props = [
    { prop: "title" },
    { prop: "rowDisplay" },
    { prop: "baseAmount", columnIndex: "0", align: "right" },
    { prop: "baseReduceAmount", columnIndex: "1", align: "right" },
    { prop: "baseDeductionAmount", columnIndex: "2", align: "right" },
    { prop: "baseRebateAmount", columnIndex: "3", align: "right" },
    { prop: "taxRate", columnIndex: "4", align: "right" },
    { prop: "currentPayable", columnIndex: "5", align: "right" },
    { prop: "reduceName", columnIndex: "6", align: "right" },
    { prop: "reduceAmount", columnIndex: "7", align: "right" },
    { prop: "jzzcReduceCode", columnIndex: "8", align: "right" },
    { prop: "jzzcReduceRate", columnIndex: "9", align: "right" },
    { prop: "jzzcReduceAmount", columnIndex: "10", align: "right" },
    { prop: "educationReduceName", columnIndex: "11", align: "right" },
    { prop: "educationCreditAmount", columnIndex: "12", align: "right" },
    { prop: "paidAmount", columnIndex: "13", align: "right" },
    { prop: "payableAmount", columnIndex: "14", align: "right" },
  ]
  // 附表5表单数据
  // const attachedInfo5FormData = ref<{ [key: string]: any }>({})
  // // 附表5表格数据
  // const attachedInfo5Data = ref<{ [key: string]: any }[]>([])
  // 增值税减免明细表表格1列对应变量
  const attachedInfo6Props1 = [
    { prop: "title", columnIndex: "0" },
    { prop: "periodStartAmount", columnIndex: "1", align: "right" },
    { prop: "periodAmount", columnIndex: "2", align: "right" },
    { prop: "periodShouldMinusAmount", columnIndex: "3", align: "right" },
    { prop: "periodActualMinusAmount", columnIndex: "4", align: "right" },
    { prop: "periodEndAmount", columnIndex: "5", align: "right" },
  ]
  // 增值税减免税表格1合计数据
  // const attachedInfo6Data = ref<{ [key: string]: any }[]>([])

  // 增值税减免明细表表2列对应变量
  const attachedInfo6Props2 = [
    { prop: "title", columnIndex: "0" },
    { prop: "freeEventSalesAmount", columnIndex: "1", align: "right" },
    { prop: "minusFreeEventAmount", columnIndex: "2", align: "right" },
    { prop: "afterMinusSaleAmount", columnIndex: "3", align: "right" },
    { prop: "freeSaleAmountInputTaxAmount", columnIndex: "4", align: "right" },
    { prop: "freeTaxAmount", columnIndex: "5", align: "right" },
  ]
  // 增值税月报所有会出现的表格
  const allTabList = ref<TabObj[]>([
    {
      id: "m001",
      title: "主表",
      func: getMainTableData,
      data: [],
    },
    {
      id: "m002",
      title: "附列资料一",
      columns: attachedInfo1Columns,
      headerCellStyle: attachedInfo1HeaderCellStyle,
      func: getAttach1Data,
      spanMethod: attachedInfo1SpanMethod,
      data: [],
    },
    { id: "m003", title: "附列资料二", func: getAttach2Data, data: [] },
    {
      id: "m006",
      title: "附列资料三",
      columns: attachedInfo3Columns,
      headerCellStyle: attachedInfo3HeaderCellStyle,
      func: getAttach3Data,
      spanMethod: attachedInfo3SpanMethod,
      data: [],
    },
    { id: "m031", title: "附列资料四", func: getAttach4Data, data: [] },
    { id: "m301", title: "附列资料五", func: getAttach5Data, data: [] },
    { id: "m081", title: "增值税减免税申报明细表", func: getAttach6Data, data: [] },
  ])
  // 获取所有表数据
  function getMainTableData() {
    getAddTaxMonthData("/major").then((res) => {
      if (res.state === 1000) {
        res.data.statementDatas.forEach((item: any, index: number) => {
          mainTableTabList.value[index].data = item
        })
        allTabList.value[0].data = res.data.statementDatas
      }
    })
  }
  function getAttach1Data() {
    getAddTaxMonthData("/attach1").then((res) => {
      if (res.state === 1000) {
        allTabList.value[1].data = res.data.statementDatas[0]
      }
    })
    console.log(allTabList.value)
  }
  function getAttach2Data() {
    getAddTaxMonthData("/attach2").then((res) => {
      if (res.state === 1000) {
        // attachedInfo2Data.value = res.data.statementDatas
        allTabList.value[2].data = res.data.statementDatas
      }
    })
  }
  function getAttach3Data() {
    getAddTaxMonthData("/attach3").then((res) => {
      if (res.state === 1000) {
        allTabList.value[3].data = res.data.statementDatas[0]
        allTabList.value[3].data?.push({
          title: "注：纳税人在本期发生以前所属期（月份）调整（调增或者调减）应税服务扣除项目金额的，直接在第三列“本期发生额”栏次进行调整。",
        })
      }
    })
  }
  function getAttach4Data() {
    getAddTaxMonthData("/attach4").then((res) => {
      if (res.state === 1000) {
        // attachedInfo4Data.value = res.data.statementDatas
        allTabList.value[4].data = res.data.statementDatas
      }
    })
  }
  function getAttach5Data() {
    getAddTaxMonthData("/attach5").then((res) => {
      // attachedInfo5FormData.value = res.data.statementExtension
      // attachedInfo5Data.value = res.data.statementDatas[0]
      allTabList.value[5].data = res.data
    })
  }
  function getAttach6Data() {
    getAddTaxMonthData("/attach6").then((res) => {
      if (res.state === 1000) {
        // attachedInfo6Data.value = res.data.statementDatas
        allTabList.value[6].data = res.data.statementDatas
      }
    })
  }

  function generateObj(original: { [key: string]: any }[], order: number) {
    return original.reduce((result, item) => {
      result[item.prop] = ""
      if (order === 1) {
        result.readonly = allTabList.value[6].data![0][1].readonly
      } else {
        result.readonly = allTabList.value[6].data![1][3].readonly
      }
      return result
    }, {})
  }
  // 增值税减免税申报明细表减税性质及代码添加
  function taxReductionAdd(data: any) {
    const obj = generateObj(attachedInfo6Props1, 1)
    // attachedInfo6Data.value[0].push(obj)
    data.push(obj)
  }
  // 增值税减免税申报明细表免税性质及代码添加
  function taxFreeAdd(data: any) {
    const obj = generateObj(attachedInfo6Props2, 2)
    // attachedInfo6Data.value[1].push(obj)
    data.push(obj)
  }
  function handleInnerTabChange(name: string) {
    const tabTable = document.querySelector(`.tab-table-${name}`)

    if (tabTable) {
      tabTable.scrollIntoView({ behavior: "smooth", inline: "nearest" })
    }
  }
  // 主表鼠标滚动事件
  function handleScroll(event: Event, tabData: { [key: string]: any }) {
    if (tabData.id === "m001") {
      const tableList = document.querySelectorAll("[class*='tab-table-']")
      tableList.forEach(function (table) {
        const rect = table.getBoundingClientRect()
        if (rect.top < window.innerHeight && rect.bottom >= 0) {
          const className = table.className.split("-")
          innerActiveTab.value = className[className.length - 1]
        }
      })
    }
    //  else if (tabData.id === "m002") {
    // requestAnimationFrame(() => {
    //   const scrollTop = (event.target as HTMLElement).scrollTop
    //   const scroll = (event.target as HTMLElement).querySelector(".el-scrollbar__bar.is-horizontal") as HTMLElement
    //   scroll.style.bottom = -scrollTop + "px"
    // })
    // }
  }

  // 主表输入框失焦
  function handleInputBlur(value: number, row: { [key: string]: any }, currentTabInfo: { [key: string]: any }, calcField: string) {
    console.log(calcField, "calcFIel")

    const currentCellInfo = `${currentTabInfo.id}.${row.row}.${calcField}`
    console.log(currentCellInfo, "当前单元格信息")

    // 获取对应计算公式信息
    const formulaInfo = allTableFormulaList.value.formulaItems.find((item: any) => item.expressItem === currentCellInfo)
    console.log(formulaInfo, "formula'InfoformulaInfo")

    if (formulaInfo) {
      for (let index = 0; index < formulaInfo.formulaIdList.length; index++) {
        const item = formulaInfo.formulaIdList[index]
        const formula = allTableFormulaList.value.formulas.find((innerItem: any) => innerItem.formulaId === item)
        if (!formula) continue
        let express = formula.express
        // TODO 现在公式列表里有快捷填写的公式，后端删了公式后可以去除此段代码
        if (formula.statementCode.startsWith("f")) {
          continue
        }
        const splitInfo = formula.expressItems.split(",")
        let calcVal = value
        splitInfo.forEach((innerItem: any) => {
          console.log(innerItem !== currentCellInfo, innerItem)

          if (innerItem !== currentCellInfo) {
            const [tableId, row, columnProp] = innerItem.split(".")
            calcVal = getCellInfo(tableId, row, columnProp).info
          } else {
            calcVal = value
          }

          // console.log(innerItem, calcVal)

          express = express.replace(innerItem, ` ${calcVal}`)
          // console.log(express, "公式+++++++")
        })
        const formulaRowInfo = getCellInfo(formula.statementCode, formula.row, formula.colName, false)
        console.log(`${formulaRowInfo.info} = ${express}`)
        eval(`${formulaRowInfo.info} = ${eval(express).toFixed(2)}`)
        console.log(currentCellInfo, express, "计算后结果")
        // console.log(`${formula.statementCode}.${formula.row}.${formula.colName}`, "====")

        // 这里处理循环调用
        const nextCalcInfo = getCellInfo(formula.statementCode, formula.row, formula.colName)
        handleInputBlur(nextCalcInfo.info, nextCalcInfo.calcRow, nextCalcInfo.currentTabInfo, formula.colName)
      }
    }
  }
  // 获取单元格信息
  function getCellInfo(tableId: number | string, row: number, columnProp: string, isFormula: boolean = true) {
    console.log(tableId, row, columnProp, "======单元格信息")

    const index = tabList.value.findIndex((tab: any) => tab.id === tableId)
    console.log(index, "index")

    let str = "",
      calcRow = {}
    if (tableId === "m001" || tableId === "m003" || tableId === "m031") {
      outerLoop: for (let i = 0; i < (tabList.value[index].data?.length || 0); i++) {
        const item = tabList.value[index].data![i]
        for (let j = 0; j < item.length; j++) {
          const innerItem = item[j]
          if (innerItem.row == row) {
            str = `tabList.value[${index}].data[${i}][${j}].${columnProp}`
            calcRow = tabList.value[index].data![i][j]
            break outerLoop
          }
        }
      }
    } else if (tableId === "m301") {
      if ((tabList.value[index].data as any).statementDatas) {
        const rowIndex = (tabList.value[index].data as any).statementDatas[0].findIndex((da: any) => da.row == row)
        str = `tabList.value[${index}].data.statementDatas[0][${rowIndex}].${columnProp}`
        calcRow = tabList.value[index].data![rowIndex as number]
      }
    } else {
      const rowIndex = tabList.value[index].data?.findIndex((da: any) => da.row == row)
      str = `tabList.value[${index}].data[${rowIndex}].${columnProp}`
      calcRow = tabList.value[index].data![rowIndex as number]
    }
    // console.log(`tabList.value[${index}].data[${rowIndex}].${columnProp}`, "单元格信息")
    console.log(str, "str")

    return {
      info: isFormula ? eval(str) : str,
      calcRow,
      currentTabInfo: tabList.value[index],
    }
  }
  // 生成公式
  const generateFormula = (
    row: { [key: string]: any },
    currentTabInfo: { [key: string]: any },
    slotColumn: { [key: string]: any },
    callback: (result: string) => void,
  ) => {
    // console.log(callback, "callback")

    // console.log(currentTabInfo.id, row.row, slotColumn.prop, "生成公式")

    const formula = allTableFormulaList.value.formulas.find(
      (item: any) => item.statementCode === currentTabInfo.id && item.row == row.row && item.colName === slotColumn.prop,
    )
    // console.log(formula, "公式信息")
    if (formula) {
      const calcList = formula.expressItems.split(",")
      let str = "<span>公式：<span><br/>"

      for (let i = 0; i < formula.formulaDescList.length; i++) {
        const item = formula.formulaDescList[i]
        if (item.includes(calcList[i])) {
          const [tableId, row, columnProp] = calcList[i].split(".")

          const calcVal = getCellInfo(tableId, row, columnProp).info
          str += `<span>${item.replace(calcList[i], String(parseFloat(calcVal).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ","))}</span><br>`
        } else {
          str += `<span>${item}</span><br>`
        }
      }

      callback(str)
      // return str
    }
  }

  function save() {
    console.log(tabList.value, "tabList")
  }

  defineExpose({ save })
</script>
<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
  .declaration-form {
    .form-title {
      margin-bottom: 4px;
      font-size: var(--h3);
      text-align: center;
    }

    .form-subtitle {
      margin-bottom: 14px;
      font-weight: 400;
      font-size: var(--h4);
      color: var(--dark-grey);
      text-align: center;
    }

    .form-info {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .info {
        flex: 3;
        display: flex;
        flex-wrap: wrap;

        div {
          box-sizing: border-box;
          margin-right: 50px;

          span {
            line-height: 20px;
            font-size: var(--h4);
          }
        }
      }
      span {
        flex: 1;
        align-self: flex-end;
        line-height: 32px;
        font-size: 14px;
        text-align: right;
      }
    }
  }

  .main-table {
    :deep(.el-tabs) {
      z-index: 999;
      height: auto;
      position: sticky;
      top: 0;
      background-color: var(--white);

      .el-tabs__header {
        margin-bottom: 12px;
      }
    }

    .title {
      display: inline-block;
      padding: 8px 16px;
      margin-bottom: 15px;
      border: 1px solid var(--button-border-color);
    }

    .el-divider--horizontal {
      margin: 20px 0;
    }
  }

  .custom-table :deep(.el-table__body-wrapper) {
    .el-scrollbar__bar.is-horizontal {
      z-index: 9999;

      position: fixed;
      left: 195px;
      bottom: 14px;
    }
  }

  .appendix-2,
  .appendix-4 {
    table {
      table-layout: fixed;
    }
  }

  .appendix-5 {
    width: 100%;
    table {
      width: max-content;
    }
  }
  .appendix-6 {
    table {
      width: 100%;
      border-right: 0;

      :deep(.el-select) {
        width: 95% !important;
        float: right;
      }
    }

    tr {
      position: relative;
      &.dynamic-row {
        .minus {
          display: none;
          position: absolute;
          top: 50%;
          left: 2px;
          transform: translateY(-50%);
        }
        &:hover .minus {
          display: block;
        }
      }
    }

    th {
      width: 16.7%;
    }

    .el-icon {
      font-size: var(--h4);
      vertical-align: sub;
      cursor: pointer;
    }
  }
</style>
