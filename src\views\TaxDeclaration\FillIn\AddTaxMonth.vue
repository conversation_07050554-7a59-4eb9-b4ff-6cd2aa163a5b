<!-- 增值税月报 -->
<template>
  <category-tabs
    v-if="Object.keys(tableData).length"
    class="main-content-body"
    v-model="activeIndex"
    :tab-list="allTabList"
    :show-total="true"
    :force-validate-number="finalValidateResults.filter((item: any) => item.type === 'error').length"
    :prompt-validate-number="finalValidateResults.filter((item: any) => item.type === 'warning').length"
    @tab-click="handleTabClick"
    @scroll="handleScroll"
    @arrow-click="openVerificationResultsDialog(true)">
    <template #content="{ activeIndex, tabData }">
      <div class="declaration-form">
        <div class="form-title">{{ tabData.describe || "增值税及附加税费申报表" }}</div>
        <div
          class="form-subtitle"
          v-if="tabData.describe">
          {{ tabData.describe || "（适用已执行新金融准则、新收入准则和新租赁准则的一般企业）" }}
        </div>
        <div class="form-info">
          <div class="info">
            <div
              v-for="(item, index) in addTaxMonthForm"
              :key="index">
              <span class="label">{{ item.label }}：</span>
              <span class="value">{{ item.value }}</span>
            </div>
          </div>
          <span>金额单位：{{ 100000000000000000 }}元，至角分</span>
        </div>
      </div>
      <div
        v-if="tabData.title === '主表'"
        class="main-table">
        <el-tabs
          v-model="innerActiveTab"
          @tab-change="handleInnerTabChange">
          <el-tab-pane
            v-for="(item, index) in mainTableTabList"
            :key="index"
            :label="item.title"
            :name="item.className"></el-tab-pane>
        </el-tabs>
        <div
          v-for="(item, index) in mainTableTabList"
          :key="index"
          :class="`tab-table-${item.className}`"
          style="scroll-margin-top: 60px">
          <div
            v-if="index !== 0"
            class="title">
            {{ item.title }}
          </div>
          <table class="native-table native-table-fixed">
            <thead>
              <tr>
                <th rowspan="2">项目</th>
                <th rowspan="2">栏次</th>
                <th colspan="2">一般项目</th>
                <th colspan="2">即征即退项目</th>
              </tr>
              <tr>
                <th>本月数</th>
                <th>本年累计</th>
                <th>本月数</th>
                <th>本年累计</th>
              </tr>
            </thead>
            <tbody>
              <tr
                class="table-data"
                v-for="(innerItem, innerIndex) in mainTableColField[index]"
                :key="innerIndex">
                <td
                  v-for="(column, columnKey) in 6"
                  :key="columnKey">
                  <span
                    v-if="Number(columnKey) < 2"
                    style="white-space: pre-wrap">
                    {{ tableData.ywbw.sbZzsYbnsr[columnKey][innerItem.prop] }}
                  </span>
                  <template v-else>
                    <Popover
                      v-if="tableData.ywbw.sbZzsYbnsr[columnKey].formula.split(',')[innerItem.index] == 1"
                      placement="right"
                      :use-slot-content="true">
                      <template #trigger>
                        <div class="equal-icon" />
                        <span style="float: right; line-height: 12px">
                          {{
                            String(parseFloat(tableData.ywbw.sbZzsYbnsr[columnKey][innerItem.prop] as string).toFixed(2)).replace(
                              /\B(?=(\d{3})+(?!\d))/g,
                              ",",
                            )
                          }}
                        </span>
                      </template>
                      <template #content>
                        <!-- <div v-html="generateFormula(rowData, tabData, columnInfo)"></div> -->
                      </template>
                    </Popover>
                    <checkable-input
                      v-if="tableData.ywbw.sbZzsYbnsr[columnKey].readonly.split(',')[innerItem.index] == 0"
                      :box-id="`getObjFromList(${tabData.id}, 'ewblxh', '${tableData.ywbw.sbZzsYbnsr[columnKey].ewblxh}').${innerItem.prop}Id`"
                      v-model="tableData.ywbw.sbZzsYbnsr[columnKey][innerItem.prop]"
                      :table-validate-messages="finalValidateResults"
                      @blur="handleInputBlur"></checkable-input>
                    <checkable-span
                      v-else-if="tableData.ywbw.sbZzsYbnsr[columnKey].readonly.split(',')[innerItem.index] == 1"
                      :box-id="`getObjFromList(${tabData.id}, 'ewblxh', '${tableData.ywbw.sbZzsYbnsr[columnKey].ewblxh}').${innerItem.prop}Id`"
                      :value="tableData.ywbw.sbZzsYbnsr[columnKey][innerItem.prop]"
                      :table-validate-messages="finalValidateResults"></checkable-span>
                    <span v-else>--</span>
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
          <el-divider v-if="index !== mainTableTabList.length - 1" />
        </div>
      </div>
      <div
        v-if="tabData.title === '附列资料一'"
        class="appendix-1">
        <table class="native-table native-table-fixed">
          <colgroup>
            <col
              v-for="(item, index) in attachedInfo1ColumnWidth"
              :key="index"
              :width="item" />
          </colgroup>
          <thead>
            <tr
              v-for="(item, index) in attachedInfo1Columns"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb1Bqxsqkmx.sort((a: any, b: any) => a.row - b.row)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo1Field"
                :key="innerIndex"
                :rowspan="item.spanInfo[innerItem.prop] && item.spanInfo[innerItem.prop][0]"
                :colspan="item.spanInfo[innerItem.prop] && item.spanInfo[innerItem.prop][1]"
                :style="{ display: item.spanInfo[innerItem.prop] === 0 ? 'none' : 'table-cell' }">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="appendix-2"
        v-if="tabData.title === '附列资料二'">
        <table class="native-table native-table-fixed">
          <thead>
            <tr>
              <th colspan="5">一、申报抵扣的进项税额</th>
            </tr>
            <tr>
              <th
                v-for="(item, index) in attachedInfo2Columns1"
                :key="index">
                {{ item.prop }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb2Bqjxsemx.sort((a: any, b: any) => a.row - b.row).slice(0, 13)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Field1"
                :key="innerIndex">
                <span v-if="!Object.keys(innerItem).includes('index')">
                  {{ item[innerItem.prop] }}
                </span>
                <template v-else>
                  <add-month-cell-content
                    v-model="item[innerItem.prop]"
                    :rowData="item"
                    :tab-data="tabData"
                    :column-info="innerItem"
                    :table-validate-messages="finalValidateResults"></add-month-cell-content>
                </template>
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="5">二、进项税额转出额</th>
            </tr>
            <tr>
              <th
                v-for="(item, index) in attachedInfo2Columns2"
                :key="index"
                :colspan="item.colspan || 1">
                {{ item.prop }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb2Bqjxsemx.sort((a: any, b: any) => a.row - b.row).slice(13, 25)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Field2"
                :key="innerIndex"
                :colspan="innerItem.colspan || 1">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="5">三、待抵扣进项税额</th>
            </tr>
            <tr>
              <th
                v-for="(item, index) in attachedInfo2Columns1"
                :key="index">
                {{ item.prop }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb2Bqjxsemx.sort((a: any, b: any) => a.row - b.row).slice(25, 36)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Field1"
                :key="innerIndex">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
          <thead>
            <tr>
              <th colspan="5">四、其他</th>
            </tr>
            <tr>
              <th
                v-for="(item, index) in attachedInfo2Columns1"
                :key="index">
                {{ item.prop }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb2Bqjxsemx.sort((a: any, b: any) => a.row - b.row).slice(36)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo2Field1"
                :key="innerIndex">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="appendix-3"
        v-if="tabData.title === '附列资料三'">
        <table class="native-table native-table-fixed">
          <colgroup>
            <col />
            <col style="width: 40px" />
          </colgroup>
          <thead>
            <tr
              v-for="(item, index) in attachedInfo3Columns"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb3Ysfwkcxm"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo3Field"
                :key="innerIndex">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
            <tr>
              <td colspan="8">
                注：纳税人在本期发生以前所属期（月份）调整（调增或者调减）应税服务扣除项目金额的，直接在第三列“本期发生额”栏次进行调整。
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        v-if="tabData.title === '附列资料四'"
        class="appendix-4">
        <table class="native-table native-table-fixed">
          <colgroup>
            <col style="width: 41px" />
          </colgroup>
          <thead>
            <tr
              v-for="(item, index) in attachedInfo4Columns1"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb4Sedjqk.slice(0, 5)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo4Field1"
                :key="innerIndex"
                :colspan="innerItem.colspan || 1">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
          <thead>
            <tr
              v-for="(item, index) in attachedInfo4Columns2"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsYbnsrFb4Sedjqk.slice(5)"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo4Field2"
                :key="innerIndex">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        v-if="activeIndex === 5"
        class="appendix-5">
        <table class="native-table native-table-fixed">
          <colgroup>
            <col
              v-for="(item, index) in attachedInfo5ColumnWidth"
              :key="index"
              :style="{ width: item + 'px' }" />
          </colgroup>
          <thead>
            <tr>
              <th colspan="2">被冲红所属期起</th>
              <td colspan="6">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.bchssqq }}</td>
              <th colspan="3">被冲红所属期止</th>
              <td colspan="6">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.bchssqz }}</td>
            </tr>
            <tr>
              <th colspan="2">减征政策适用主体</th>
              <td colspan="3">
                <el-checkbox-group
                  v-model="tableData.ywbw.sbFjsf.sbFjsfQtxx.jzzcsyztDm"
                  disabled>
                  <el-checkbox
                    label="个体工商户"
                    true-value="22" />
                  <el-checkbox
                    disabled
                    label="小型微利企业"
                    true-value="21" />
                </el-checkbox-group>
              </td>
              <th colspan="3">本期是否适用小微企业“六税两费”减征政策</th>
              <td colspan="3">
                <el-radio-group v-model="tableData.ywbw.sbFjsf.sbFjsfQtxx.bqsfsyxgmyhzc">
                  <el-radio value="Y">是</el-radio>
                  <el-radio value="N">否</el-radio>
                </el-radio-group>
              </td>
              <th colspan="3">适用减征政策起止时间</th>
              <td colspan="3">
                {{ tableData.ywbw.sbFjsf.sbFjsfQtxx.syxgmjzzcqssj }}至{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.syxgmjzzczzsj }}
              </td>
            </tr>
          </thead>
          <thead>
            <tr
              v-for="(item, index) in attachedInfo5Columns"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="table-data"
              v-for="(item, index) in tableData.ywbw.sbFjsf.sbFjsfMx"
              :key="index">
              <td
                v-for="(innerItem, innerIndex) in attachedInfo5Field"
                :key="innerIndex">
                <add-month-cell-content
                  v-model="item[innerItem.prop]"
                  :rowData="item"
                  :tab-data="tabData"
                  :column-info="innerItem"
                  :table-validate-messages="finalValidateResults"></add-month-cell-content>
              </td>
            </tr>
          </tbody>
          <tbody>
            <tr>
              <th
                rowspan="3"
                colspan="5">
                本期是否适用试点建设培育产教融合型企业抵免政策
              </th>
              <td
                rowspan="3"
                colspan="2">
                <el-radio-group v-model="tableData.ywbw.sbFjsf.sbFjsfQtxx.bqsfsycjyhxqyjzzc">
                  <el-radio value="Y">是</el-radio>
                  <el-radio value="N">否</el-radio>
                </el-radio-group>
              </td>
              <th colspan="7">当期新增投资额</th>
              <th>5</th>
              <td colspan="2">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.dqxztze }}</td>
            </tr>
            <tr>
              <th colspan="7">上期留抵可抵免金额</th>
              <th>6</th>
              <td colspan="2">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.sqldkdmje }}</td>
            </tr>
            <tr>
              <th colspan="7">结转下期可抵免金额</th>
              <th>7</th>
              <td colspan="2">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.jzxqkdmje }}</td>
            </tr>
            <tr>
              <th
                rowspan="3"
                colspan="7">
                可用于扣除的增值税留抵退税额使用情况
              </th>
              <th colspan="7">当期新增可用于扣除的留抵退税额</th>
              <th>8</th>
              <td colspan="2">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.dqxzldtse }}</td>
            </tr>
            <tr>
              <th colspan="7">上期结存可用于扣除的留抵退税额</th>
              <th>9</th>
              <td colspan="2">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.sqjcldtse }}</td>
            </tr>
            <tr>
              <th colspan="7">结转下期可用于扣除的留抵退税额</th>
              <th>10</th>
              <td colspan="2">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.jzxqldtse }}</td>
            </tr>
            <tr>
              <th colspan="4">计税（费）依据修改原因</th>
              <td colspan="3">
                {{ tableData.ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz === "3" ? "其他" : tableData.ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz }}
              </td>
              <th colspan="7">其他修改原因</th>
              <td colspan="3">{{ tableData.ywbw.sbFjsf.sbFjsfQtxx.jsyjxgyy }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="reduce-details"
        v-if="tabData.title === '增值税减免税申报明细表'">
        <table class="native-table native-table-fixed">
          <thead>
            <tr
              v-for="(item, index) in reduceDetailsColumns1"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="dynamic-row table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm"
              :key="index">
              <template
                v-for="(innerItem, innerIndex) in reduceDetailsField1"
                :key="innerItem.prop">
                <th v-if="innerIndex === 0 && index === 0">
                  {{
                    isNaN(item[innerItem.prop])
                      ? item[innerItem.prop]
                      : String(parseFloat(item[innerItem.prop]).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                  }}
                  <el-icon
                    color="#43B449"
                    class="add"
                    @click="taxReductionAdd(tabData.data![0])">
                    <CirclePlusFilled />
                  </el-icon>
                </th>
                <td v-else>
                  <LMSelect
                    v-if="item.readonly.split(',')[innerItem.index] == 3"
                    v-model="item[innerItem.prop]"
                    placeholder="请选择"
                    clearable>
                    <LMOption></LMOption>
                  </LMSelect>
                  <template v-else>
                    <add-month-cell-content
                      v-model="item[innerItem.prop]"
                      :rowData="item"
                      :tab-data="tabData"
                      :column-info="innerItem"
                      :table-validate-messages="finalValidateResults"></add-month-cell-content>
                  </template>
                </td>
              </template>
              <el-icon
                v-if="tableData.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.length > 2"
                class="minus"
                color="#F6404E"
                @click="tableData.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.splice(index, 1)">
                <RemoveFilled />
              </el-icon>
            </tr>
          </tbody>
          <thead>
            <tr
              v-for="(item, index) in reduceDetailsColumns2"
              :key="index">
              <th
                v-for="(innerItem, innerIndex) in item"
                :key="innerIndex"
                :rowspan="innerItem.rowspan || 1"
                :colspan="innerItem.colspan || 1">
                {{ innerItem.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              class="dynamic-row table-data"
              v-for="(item, index) in tableData.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm"
              :key="index">
              <template
                v-for="(innerItem, innerIndex) in reduceDetailsField2"
                :key="innerItem.prop">
                <th v-if="(index as number) < 2 && innerIndex === 0">
                  {{ item[innerItem.prop] }}
                  <el-icon
                    v-if="index === 0"
                    color="#43B449"
                    class="add"
                    @click="taxFreeAdd(tabData.data![1])">
                    <CirclePlusFilled />
                  </el-icon>
                </th>
                <td v-else>
                  <LMSelect
                    v-if="item.readonly.split(',')[innerItem.index] == 3"
                    v-model="item[innerItem.prop]"
                    placeholder="请选择"
                    clearable>
                    <LMOption></LMOption>
                  </LMSelect>
                  <template v-else>
                    <add-month-cell-content
                      v-model="item[innerItem.prop]"
                      :rowData="item"
                      :tab-data="tabData"
                      :column-info="innerItem"
                      :table-validate-messages="finalValidateResults"></add-month-cell-content>
                  </template>
                </td>
              </template>
              <el-icon
                v-if="tableData.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.length > 4 && (index as number) > 2"
                class="minus"
                color="#F6404E"
                @click="tableData.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.splice(index, 1)">
                <RemoveFilled />
              </el-icon>
            </tr>
          </tbody>
        </table>
      </div>
    </template>
  </category-tabs>
</template>
<script setup lang="ts">
  import CategoryTabs from "../components/CategoryTabs.vue"
  import type { TabObj } from "../components/types"
  import { getAddTaxMonthData } from "@/api/taxDeclaration"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { storeToRefs } from "pinia"
  import AddMonthCellContent from "../components/AddMonthCellContent.vue"
  import { ReportTypeEnum } from "./vat-checkRules/enums"
  import { vatCheckRules, type RootInfo } from "./addTaxUtils"

  import readonly from "./vat-checkRules/readonly.json"
  import ruleCorrespondenceRelationship from "./vat-checkRules/getObjFromList相关5.json"

  const props = withDefaults(defineProps<{ currentTaxInfo?: { [key: string]: any } }>(), {
    currentTaxInfo: () => ({}),
  })

  const updateValidateMessages = inject("updateValidateMessages") as (params: any) => void
  const openVerificationResultsDialog = inject("openVerificationResultsDialog") as (params: any) => void

  // 增值税月报所有会出现的表格
  const allTabList = ref<TabObj[]>([
    {
      id: "ywbw.sbZzsYbnsr",
      title: "主表",
      // func: getMainTableData,
      data: [],
    },
    {
      id: "ywbw.sbZzsYbnsrFb1Bqxsqkmx",
      title: "附列资料一",
      // func: getAttach1Data,
      spanMethod: attachedInfo1SpanMethod,
      data: [],
    },
    { id: "ywbw.sbZzsYbnsrFb2Bqjxsemx", title: "附列资料二", func: getAttach2Data, data: [] },
    {
      id: "ywbw.sbZzsYbnsrFb3Ysfwkcxm",
      title: "附列资料三",
      // func: getAttach3Data,
      // spanMethod: attachedInfo3SpanMethod,
      data: [],
    },
    { id: "ywbw.sbZzsYbnsrFb4Sedjqk", title: "附列资料四", func: getAttach4Data, data: [] },
    { id: "ywbw.sbFjsf", title: "附列资料五", func: getAttach5Data, data: [] },
    { id: "ywbw.sbZzsFbZzsjmssbmxb", title: "增值税减免税申报明细表", func: getAttach6Data, data: [] },
  ])
  const activeIndex = ref(0)
  const currentTable = computed(() => {
    return allTabList.value[activeIndex.value]
  })

  const tableData = ref<{ [key: string]: any }>(readonly)
  const validateResults = ref<any>([])
  const finalValidateResults = ref<any>([])
  // // 获取增值税月报数据
  // function getData() {
  //   getAddTaxMonthData("/declare_data").then((res) => {
  //     if (res.state === 1000) {
  //       tableData.value = res.data
  //       res.data.ytxx.sbZzsYbnsr[2].ajybfjsxse = 2
  //       res.data.ytxx.sbZzsYbnsr[2].jybfNsjctzxse = 1000
  //       tableData.value.ywbw = res.data.ytxx
  //       // tableData.value.ywbw.SbZzsYbnsr[2].JybfNsjctzxse = 1000
  //       validateMessage.value = vatCheckRules(ReportTypeEnum.GeneralVatMonthly, tableData.value as RootInfo)
  //     }
  //   })
  // }
  // getData()

  tableData.value.ywbw = tableData.value.ytxx
  validateResults.value = vatCheckRules(ReportTypeEnum.GeneralVatMonthly, tableData.value as RootInfo)
  // 过滤出当前表的校验结果
  function getCurrentTableValidateResults() {
    finalValidateResults.value = validateResults.value.reduce((result: any, curr: any) => {
      const correspondingRelationship = ruleCorrespondenceRelationship.find((item) => item.NmyRuleMessage === curr.content)
      if (!correspondingRelationship) {
        return result
      }
      const isCurrentTableRule = correspondingRelationship.RelateCells.find((item) => item.PreTable === currentTable.value.id)
      if (isCurrentTableRule) {
        result.push(curr)
      }
      return result
    }, [])
    updateValidateMessages(finalValidateResults.value)
  }
  getCurrentTableValidateResults()

  provide("ruleCellRelationships", ruleCorrespondenceRelationship)

  function handleInputBlur() {
    validateResults.value = vatCheckRules(ReportTypeEnum.GeneralVatMonthly, tableData.value as RootInfo)
    getCurrentTableValidateResults()
  }

  const { basicInfo } = storeToRefs(useBasicInfoStore())
  let formData = reactive([
    { label: "纳税人名称（公章）", value: basicInfo.value.taxpayerName },
    { label: "税款所属期", value: `${props.currentTaxInfo.periodStart}--${props.currentTaxInfo.periodEnd}` },
    { label: "填表日期", value: new Date().toISOString().slice(0, 10) },
  ])
  let addTaxMonthForm = reactive<Array<{ [key: string]: any }>>(formData)
  function handleTabClick(row: TabObj, index: number) {
    console.log(tableData.value.ywbw)

    if (row.spanMethod) row.spanMethod()
    // 仅主表展示填表日期
    if (index !== 0) {
      addTaxMonthForm = formData.slice(0, 2)
    } else {
      addTaxMonthForm = formData
    }
    getCurrentTableValidateResults()
  }

  // 处理主表鼠标滚动事件
  function handleScroll() {
    const tableList = document.querySelectorAll("[class*='tab-table-']")
    tableList.forEach(function (table) {
      const rect = table.getBoundingClientRect()
      if (rect.top < window.innerHeight && rect.bottom >= 0) {
        const className = table.className.split("-")
        innerActiveTab.value = className[className.length - 1]
      }
    })
  }

  const mainTableTabList = ref<{ [key: string]: any }[]>([
    {
      title: "销售额",
      className: "salesVolume",
      // rowData: [
      //   { title: "（一）按适用税率计税销售额", rowDisplay: "1" },
      //   { title: "    其中：应税货物销售额", rowDisplay: "2" },
      //   { title: "         应税劳务销售额", rowDisplay: "3" },
      //   { title: "         纳税检查调整的销售额", rowDisplay: "4" },
      //   { title: "（二）按简易办法计税销售额", rowDisplay: "5" },
      //   { title: "    其中：纳税检查调整的销售额", rowDisplay: "6" },
      //   { title: "（三）免、抵、退办法出口销售额", rowDisplay: "7" },
      //   { title: "（四）免税销售额", rowDisplay: "8" },
      //   { title: "    其中：免税货物销售额", rowDisplay: "9" },
      //   { title: "         免税劳务销售额", rowDisplay: "10" },
      // ],
    },
    {
      title: "税款计算",
      className: "totalTaxAmount",
      // rowData: [
      //   { title: "销项税额", rowDisplay: "11" },
      //   { title: "进项税额", rowDisplay: "12" },
      //   { title: "上期留抵税额", rowDisplay: "13" },
      //   { title: "进项税额转出", rowDisplay: "14" },
      //   { title: "免、抵、退应退税额", rowDisplay: "15" },
      //   { title: "按适用税率计算的纳税检查应补缴税额", rowDisplay: "16" },
      //   { title: "应抵扣税额合计", rowDisplay: "17=12+13-14-15+16" },
      //   { title: "实际抵扣税额", rowDisplay: "18（如17<11，则为17，否则为11）" },
      //   { title: "应纳税额", rowDisplay: "19=11-18" },
      //   { title: "期末留抵税额", rowDisplay: "20=17-18" },
      //   { title: "简易计税办法计算的应纳税额", rowDisplay: "21" },
      //   { title: "按简易计税办法计算的纳税检查应补缴税额", rowDisplay: "22" },
      //   { title: "应纳税额减征额", rowDisplay: "23" },
      //   { title: "应纳税额合计", rowDisplay: "24=19+21-23" },
      // ],
    },
    {
      title: "税款缴纳",
      className: "taxPayment",
      // rowData: [
      //   { title: "期初末缴税额（多缴为负数）", rowDisplay: "25" },
      //   { title: "实收出口开具专用缴款书退税额", rowDisplay: "26" },
      //   { title: "本期已缴税额", rowDisplay: "27=28+29+30+31" },
      //   { title: "①分次预缴税额", rowDisplay: "28" },
      //   { title: "②出口开具专用缴款书预缴税额", rowDisplay: "29" },
      //   { title: "③本期缴纳上期应纳税额", rowDisplay: "30" },
      //   { title: "④本期缴纳欠缴税额", rowDisplay: "31" },
      //   { title: "期末未缴税额（多缴为负数）", rowDisplay: "32=24+25+26-27" },
      //   { title: "     其中：欠缴税额（≥0）", rowDisplay: "33=25+26-27" },
      //   { title: "本期应补（退）税额", rowDisplay: "34=24-28-29" },
      //   { title: "即征即退实际退税额", rowDisplay: "35" },
      //   { title: "期初未缴查补税额", rowDisplay: "36" },
      //   { title: "本期入库查补税额", rowDisplay: "37" },
      //   { title: "期末未缴查补税额", rowDisplay: "38=16+22+36-37" },
      // ],
    },
    {
      title: "附加税费",
      className: "additionalTaxes",
      // rowData: [
      //   { title: "城市维护建设税本期应补（退）税额", rowDisplay: "39" },
      //   { title: "教育附加本期应补（退）费额", rowDisplay: "40" },
      //   { title: "地方教育附加本期应补（退）费额", rowDisplay: "41" },
      // ],
    },
  ])
  // 主表按列的字段
  const mainTableColField = [
    [
      { prop: "asysljsxse", index: 0 },
      { prop: "yshwxse", index: 1 },
      { prop: "yslwxse", index: 2 },
      { prop: "syslNsjctzxse", index: 3 },
      { prop: "ajybfjsxse", index: 4 },
      { prop: "jybfNsjctzxse", index: 5 },
      { prop: "mdtbfckxse", index: 6 },
      { prop: "msxse", index: 7 },
      { prop: "mshwxse", index: 8 },
      { prop: "mslwxse", index: 9 },
    ],
    [
      { prop: "xxse", index: 10 },
      { prop: "jxse", index: 11 },
      { prop: "sqldse", index: 12 },
      { prop: "jxsezc", index: 13 },
      { prop: "mdtytse", index: 14 },
      { prop: "syslNsjcybjse", index: 15 },
      { prop: "ydksehj", index: 16 },
      { prop: "sjdkse", index: 17 },
      { prop: "ynse", index: 18 },
      { prop: "qmldse", index: 19 },
      { prop: "jybfYnse", index: 20 },
      { prop: "jybfNsjcybjse", index: 21 },
      { prop: "ynsejze", index: 22 },
      { prop: "ynsehj", index: 23 },
    ],
    [
      { prop: "qcwjse", index: 24 },
      { prop: "ssckkjzyjkstse", index: 25 },
      { prop: "bqyjse", index: 26 },
      { prop: "fcyjse", index: 27 },
      { prop: "ckkjzyjksyjse", index: 28 },
      { prop: "bqjnsqynse", index: 29 },
      { prop: "bqjnqjse", index: 30 },
      { prop: "qmwjse", index: 31 },
      { prop: "qmwjseQjse", index: 32 },
      { prop: "bqybtse", index: 33 },
      { prop: "jzjtsjtse", index: 34 },
      { prop: "qcwjcbse", index: 35 },
      { prop: "bqrkcbse", index: 36 },
      { prop: "qmwjcbse", index: 37 },
    ],
    [
      { prop: "bqybtsecjs", index: 38 },
      { prop: "bqybtsejyfj", index: 39 },
      { prop: "bqybtsedfjyfj", index: 40 },
    ],
  ]
  const innerActiveTab = ref("salesVolume")

  // 附表一表头
  const attachedInfo1Columns: { [key: string]: any }[][] = [
    [
      { label: "项目及栏次", rowspan: 3, colspan: 4, width: 75 },
      { label: "开具增值税专用发票", colspan: 2, width: 75 },
      { label: "开具其他发票", colspan: 2, width: 265 },
      { label: "未开具发票", colspan: 2, width: 60 },
      { label: "纳税检查调整", colspan: 2 },
      { label: "合计", colspan: 3, width: 75 },
      { label: "服务、不动产和无形资产扣除项目本期实际扣除金额", rowspan: 2 },
      { label: "扣除后", colspan: 2 },
    ],
    [
      { label: "销售额" },
      { label: "销项（应纳）税额" },
      { label: "销售额" },
      { label: "销项（应纳）税额" },
      { label: "销售额" },
      { label: "销项（应纳）税额" },
      { label: "销售额" },
      { label: "销项（应纳）税额" },
      { label: "销售额" },
      { label: "销项（应纳）税额" },
      { label: "价税合计" },
      { label: "含税（免税）销售额" },
      { label: "销项（应纳）税额" },
    ],
    [
      { label: 1 },
      { label: 2 },
      { label: 3 },
      { label: 4 },
      { label: 5 },
      { label: 6 },
      { label: 7 },
      { label: 8 },
      { label: "9=1+3+5+7" },
      { label: "10=2+4+6+8" },
      { label: "11=9+10" },
      { label: 12 },
      { label: "13=11-12" },
      { label: "14=13÷(100%+税率或征收率)×税率或征收率" },
    ],
  ]
  // 附表一列宽
  const attachedInfo1ColumnWidth = [75, 75, 265, 60, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150]
  // 附表一字段
  const attachedInfo1Field = [
    { prop: "title" },
    { prop: "subTitle" },
    { prop: "hmc" },
    { prop: "rowDisplay" },
    { prop: "kjskzzszyfpXse", index: 0 },
    { prop: "kjskzzszyfpXxynse", index: 1 },
    { prop: "kjqtfpXse", index: 2 },
    { prop: "kjqtfpXxynse", index: 3 },
    { prop: "wkjfpXse", index: 4 },
    { prop: "wkjfpXxynse", index: 5 },
    { prop: "nsjctzdxse", index: 6 },
    { prop: "nsjctzXxynse", index: 7 },
    { prop: "xse", index: 8 },
    { prop: "hjXxynse", index: 9 },
    { prop: "jshj", index: 10 },
    { prop: "ysfwkcxmbqsjkcje", index: 11 },
    { prop: "kchHsmsxse", index: 12 },
    { prop: "kchXxynse", index: 13 },
  ]
  // 附表一处理数据合并
  function attachedInfo1SpanMethod() {
    // 处理一下数据按正常顺序展示
    tableData.value.ywbw.sbZzsYbnsrFb1Bqxsqkmx = tableData.value.ywbw.sbZzsYbnsrFb1Bqxsqkmx.sort((a: any, b: any) => a.row - b.row)
    const spanMap: { [key: string]: any } = {
      0: { 0: [7, 1], 7: [11, 1], 18: [2, 1], 20: [2, 1] },
      1: { 0: [5, 1], 5: [2, 1], 7: [9, 1], 16: [2, 1], 18: [1, 2], 19: [1, 2], 20: [1, 2], 21: [1, 2] },
    }
    for (let key in spanMap) {
      const colField = attachedInfo1Field[Number(key)].prop
      const value = spanMap[key]
      for (let k in value) {
        if (tableData.value.ywbw.sbZzsYbnsrFb1Bqxsqkmx[k].spanInfo) {
          tableData.value.ywbw.sbZzsYbnsrFb1Bqxsqkmx[k].spanInfo[colField] = value[k]
        } else {
          tableData.value.ywbw.sbZzsYbnsrFb1Bqxsqkmx[k].spanInfo = {
            [colField]: value[k],
          }
        }
      }
    }

    const notShow: { [key: string]: any } = {
      0: [
        [0, 7],
        [7, 18],
        [18, 20],
        [20, 22],
      ],
      1: [
        [0, 5],
        [5, 7],
        [7, 16],
        [16, 18],
      ],
      2: [[17, 22]],
    }
    tableData.value.ywbw.sbZzsYbnsrFb1Bqxsqkmx.forEach((item: any, index: number) => {
      for (let key in notShow) {
        const colField = attachedInfo1Field[Number(key)].prop
        const value = notShow[key]

        value.forEach((v: any) => {
          if (index > v[0] && index < v[1]) {
            if (item.spanInfo) {
              item.spanInfo[colField] = 0
            } else {
              item.spanInfo = {
                [colField]: 0,
              }
            }
          }
        })
      }
    })
  }

  // 附表二表头
  const attachedInfo2Columns1 = [{ prop: "项目" }, { prop: "栏次" }, { prop: "份数" }, { prop: "金额" }, { prop: "税额" }]
  const attachedInfo2Columns2 = [{ prop: "项目" }, { prop: "栏次" }, { prop: "税额", colspan: 3 }]
  // 附表二表字段
  const attachedInfo2Field1 = [
    { prop: "title" },
    { prop: "rowDisplay" },
    { prop: "fs", index: 0 },
    { prop: "je", index: 2 },
    { prop: "se", index: 2 },
  ]
  const attachedInfo2Field2 = [{ prop: "title" }, { prop: "rowDisplay" }, { prop: "se", index: 0, colspan: 3 }]

  // 附表三表头
  const attachedInfo3Columns: { [key: string]: any }[][] = [
    [
      { label: "项目及栏次", rowspan: 3, colspan: 2 },
      { label: "本期服务、不动产和无形资产价税合计额（免税销售额）", rowspan: 2 },
      { label: "服务、不动产和无形资产扣除项目", colspan: 5 },
    ],
    [{ label: "期初余额" }, { label: "本期发生额" }, { label: "本期应扣除金额" }, { label: "本期实际扣除金额" }, { label: "期末余额" }],
    [{ label: "1" }, { label: "2" }, { label: "3" }, { label: "4=2+3" }, { label: "5(5≤1且5≤4)" }, { label: "6=4-5" }],
  ]
  // 附表三表字段
  const attachedInfo3Field = [
    { prop: "hmc" },
    { prop: "rowDisplay" },
    { prop: "msxse", index: 0 },
    { prop: "qcye", index: 1 },
    { prop: "bqfse", index: 2 },
    { prop: "bqykcje", index: 3 },
    { prop: "bqsjkcje", index: 4 },
    { prop: "qmye", index: 5 },
  ]

  // 附表四表头
  const attachedInfo4Columns1: { [key: string]: any }[][] = [
    [{ label: "一、税额抵减情况", colspan: 8 }],
    [
      { label: "序号", rowspan: 2 },
      { label: "抵减项目", rowspan: 2 },
      { label: "期初余额" },
      { label: "本期发生额" },
      { label: "本期应抵减税额" },
      { label: "本期实际抵减税额" },
      { label: "期末余额", colspan: 2 },
    ],
    [{ label: "1" }, { label: "2" }, { label: "3=1+2" }, { label: "4≤3" }, { label: "5=3-4", colspan: 2 }],
  ]
  const attachedInfo4Columns2: { [key: string]: any }[][] = [
    [{ label: "一、加计抵减情况", colspan: 8 }],
    [
      { label: "序号", rowspan: 2 },
      { label: "加计抵减项目", rowspan: 2 },
      { label: "期初余额" },
      { label: "本期发生额" },
      { label: "本期调减额" },
      { label: "本期可抵减额" },
      { label: "本期实际抵减额" },
      { label: "期末余额" },
    ],
    [{ label: "1" }, { label: "2" }, { label: "3" }, { label: "4=1+2-3" }, { label: "5" }, { label: "6=4-5" }],
  ]
  // 附表四表格列变量
  const attachedInfo4Field1 = [
    { prop: "ewbhxh" },
    { prop: "hmc" },
    { prop: "qcye", index: 0 },
    { prop: "bqfse", index: 1 },
    { prop: "bqydjse", index: 2 },
    { prop: "bqsjdjse", index: 3 },
    { prop: "qmye", index: 4, colspan: 2 },
  ]
  const attachedInfo4Field2 = [
    { prop: "ewbhxh" },
    { prop: "hmc" },
    { prop: "qcye", index: 0 },
    { prop: "bqfse", index: 1 },
    { prop: "bqzce", index: 2 },
    { prop: "bqkjjdkjxse", index: 3 },
    { prop: "bqsjjjdkjxse", index: 4 },
    { prop: "qmye", index: 5 },
  ]

  // 附表五表头
  const attachedInfo5Columns: { [key: string]: any }[][] = [
    [
      { label: "税（费）种", rowspan: 3, colspan: 2 },
      { label: "计税（费）依据", colspan: 4 },
      { label: "税（费）率（%）", rowspan: 2 },
      { label: "本期应纳税（费）额", rowspan: 2 },
      { label: "本期减免税（费）额", colspan: 2 },
      { label: "小微企业“六税两费”减征政策", colspan: 3 },
      { label: "试点建设培育产教融合型企业", colspan: 2 },
      { label: "本期已缴税（费）额", rowspan: 2 },
      { label: "本期应补（退）税（费）额", rowspan: 2 },
    ],
    [
      { label: "增值税税额" },
      { label: "增值税限额减免金额" },
      { label: "增值税免抵税额" },
      { label: "留抵退税本期扣除额" },
      { label: "减免性质代码" },
      { label: "减免税（费）额" },
      { label: "减免性质代码" },
      { label: "减征比例（%）" },
      { label: "减征额" },
      { label: "减免性质代码" },
      { label: "本期抵免金额" },
    ],
    [
      { label: "1" },
      { label: "2" },
      { label: "3" },
      { label: "4" },
      { label: "5" },
      { label: "6=(1+2+3-4)×5" },
      { label: "7" },
      { label: "8" },
      { label: "" },
      { label: "9" },
      { label: "10" },
      { label: "11" },
      { label: "12" },
      { label: "13" },
      { label: "14=6-8-10-12-13" },
    ],
  ]
  // 附表5表格列对应变量
  const attachedInfo5Field = [
    { prop: "zsxmMc" },
    { prop: "rowDisplay" },
    { prop: "ybzzs", index: 0 },
    { prop: "zzsxejmje", index: 1 },
    { prop: "zzsmdse", index: 2 },
    { prop: "zzsldtse", index: 3 },
    { prop: "sl1", index: 4 },
    { prop: "bqynsfe", index: 5 },
    { prop: "ssjmxzDm", index: 6 },
    { prop: "jme", index: 7 },
    { prop: "lslfjzsxMc", index: 8 },
    { prop: "phjzbl", index: 9 },
    { prop: "phjmse", index: 10 },
    { prop: "cjrhjmxzDm", index: 11 },
    { prop: "bqcjrhxqydmje", index: 12 },
    { prop: "bqyjse", index: 13 },
    { prop: "bqybtse", index: 14 },
  ]
  // 附表五列宽
  const attachedInfo5ColumnWidth = [150, 50, 150, 160, 150, 160, 150, 160, 150, 150, 150, 150, 150, 150, 150, 160, 180]

  // 增值税减免申报明细表表头
  const reduceDetailsColumns1: { [key: string]: any }[][] = [
    [{ label: "一、减税项目", colspan: 6 }],
    [
      { label: "减税性质代码及名称", rowspan: 2 },
      { label: "期初余额" },
      { label: "本期发生额" },
      { label: "本期应抵减税额" },
      { label: "本期实际抵减税额" },
      { label: "期末余额" },
    ],
    [{ label: "1" }, { label: "2" }, { label: "3=1+2" }, { label: "4≤3" }, { label: "5=3-4" }],
  ]
  const reduceDetailsColumns2: { [key: string]: any }[][] = [
    [{ label: "二、免税项目", colspan: 6 }],
    [
      { label: "免税性质代码及名称", rowspan: 2 },
      { label: "免征增值税项目销售额" },
      { label: "免税销售额扣除项目本期实际扣除金额" },
      { label: "扣除后免税销售额" },
      { label: "免税销售额对应的进项税额" },
      { label: "免税额" },
    ],
    [{ label: "1" }, { label: "2" }, { label: "3=1-2" }, { label: "4" }, { label: "5" }],
  ]
  // 增值税减免明细表表格1列对应变量
  const reduceDetailsField1 = [
    { prop: "hmc", index: 0 },
    { prop: "qcye", index: 1 },
    { prop: "bqfse", index: 2 },
    { prop: "bqydjse", index: 3 },
    { prop: "bqsjdjse", index: 4 },
    { prop: "qmye", index: 5 },
  ]
  // 增值税减免明细表表2列对应变量
  const reduceDetailsField2 = [
    { prop: "hmc", index: 0 },
    { prop: "mzzzsxmxse", index: 1 },
    { prop: "bqsjkcje", index: 2 },
    { prop: "kchmsxse", index: 3 },
    { prop: "msxsedyjxse", index: 4 },
    { prop: "mse", index: 5 },
  ]

  // 获取所有表数据
  function getMainTableData() {
    getAddTaxMonthData("/major").then((res) => {
      if (res.state === 1000) {
        res.data.statementDatas.forEach((item: any, index: number) => {
          mainTableTabList.value[index].data = item
        })
        allTabList.value[0].data = res.data.statementDatas
      }
    })
  }
  function getAttach1Data() {
    getAddTaxMonthData("/attach1").then((res) => {
      if (res.state === 1000) {
        allTabList.value[1].data = res.data.statementDatas[0]
      }
    })
    console.log(allTabList.value)
  }
  function getAttach2Data() {
    getAddTaxMonthData("/attach2").then((res) => {
      if (res.state === 1000) {
        // attachedInfo2Data.value = res.data.statementDatas
        allTabList.value[2].data = res.data.statementDatas
      }
    })
  }
  function getAttach3Data() {
    getAddTaxMonthData("/attach3").then((res) => {
      if (res.state === 1000) {
        allTabList.value[3].data = res.data.statementDatas[0]
        allTabList.value[3].data?.push({
          title: "注：纳税人在本期发生以前所属期（月份）调整（调增或者调减）应税服务扣除项目金额的，直接在第三列“本期发生额”栏次进行调整。",
        })
      }
    })
  }
  function getAttach4Data() {
    getAddTaxMonthData("/attach4").then((res) => {
      if (res.state === 1000) {
        // attachedInfo4Data.value = res.data.statementDatas
        allTabList.value[4].data = res.data.statementDatas
      }
    })
  }
  function getAttach5Data() {
    getAddTaxMonthData("/attach5").then((res) => {
      // attachedInfo5FormData.value = res.data.statementExtension
      // attachedInfo5Data.value = res.data.statementDatas[0]
      allTabList.value[5].data = res.data
    })
  }
  function getAttach6Data() {
    getAddTaxMonthData("/attach6").then((res) => {
      if (res.state === 1000) {
        // attachedInfo6Data.value = res.data.statementDatas
        allTabList.value[6].data = res.data.statementDatas
      }
    })
  }

  function generateObj(original: { [key: string]: any }[], order: number) {
    return original.reduce((result, item) => {
      result[item.prop] = ""
      if (order === 1) {
        result.readonly = allTabList.value[6].data![0][1].readonly
      } else {
        result.readonly = allTabList.value[6].data![1][3].readonly
      }
      return result
    }, {})
  }
  // 增值税减免税申报明细表减税性质及代码添加
  function taxReductionAdd(data: any) {
    const obj = generateObj(attachedInfo6Props1, 1)
    // attachedInfo6Data.value[0].push(obj)
    data.push(obj)
  }
  // 增值税减免税申报明细表免税性质及代码添加
  function taxFreeAdd(data: any) {
    const obj = generateObj(attachedInfo6Props2, 2)
    // attachedInfo6Data.value[1].push(obj)
    data.push(obj)
  }
  function handleInnerTabChange(name: string) {
    const tabTable = document.querySelector(`.tab-table-${name}`)

    if (tabTable) {
      tabTable.scrollIntoView({ behavior: "smooth", inline: "nearest" })
    }
  }

  // 主表输入框失焦
  // function handleInputBlur(value: number, row: { [key: string]: any }, currentTabInfo: { [key: string]: any }, calcField: string) {
  //   console.log(calcField, "calcFIel")

  //   const currentCellInfo = `${currentTabInfo.id}.${row.row}.${calcField}`
  //   console.log(currentCellInfo, "当前单元格信息")

  //   // 获取对应计算公式信息
  //   const formulaInfo = allTableFormulaList.value.formulaItems.find((item: any) => item.expressItem === currentCellInfo)
  //   console.log(formulaInfo, "formula'InfoformulaInfo")

  //   if (formulaInfo) {
  //     for (let index = 0; index < formulaInfo.formulaIdList.length; index++) {
  //       const item = formulaInfo.formulaIdList[index]
  //       const formula = allTableFormulaList.value.formulas.find((innerItem: any) => innerItem.formulaId === item)
  //       if (!formula) continue
  //       let express = formula.express
  //       // TODO 现在公式列表里有快捷填写的公式，后端删了公式后可以去除此段代码
  //       if (formula.statementCode.startsWith("f")) {
  //         continue
  //       }
  //       const splitInfo = formula.expressItems.split(",")
  //       let calcVal = value
  //       splitInfo.forEach((innerItem: any) => {
  //         console.log(innerItem !== currentCellInfo, innerItem)

  //         if (innerItem !== currentCellInfo) {
  //           const [tableId, row, columnProp] = innerItem.split(".")
  //           calcVal = getCellInfo(tableId, row, columnProp).info
  //         } else {
  //           calcVal = value
  //         }

  //         // console.log(innerItem, calcVal)

  //         express = express.replace(innerItem, ` ${calcVal}`)
  //         // console.log(express, "公式+++++++")
  //       })
  //       const formulaRowInfo = getCellInfo(formula.statementCode, formula.row, formula.colName, false)
  //       console.log(`${formulaRowInfo.info} = ${express}`)
  //       eval(`${formulaRowInfo.info} = ${eval(express).toFixed(2)}`)
  //       console.log(currentCellInfo, express, "计算后结果")
  //       // console.log(`${formula.statementCode}.${formula.row}.${formula.colName}`, "====")

  //       // 这里处理循环调用
  //       const nextCalcInfo = getCellInfo(formula.statementCode, formula.row, formula.colName)
  //       handleInputBlur(nextCalcInfo.info, nextCalcInfo.calcRow, nextCalcInfo.currentTabInfo, formula.colName)
  //     }
  //   }
  // }
  // // 获取单元格信息
  // function getCellInfo(tableId: number | string, row: number, columnProp: string, isFormula: boolean = true) {
  //   console.log(tableId, row, columnProp, "======单元格信息")

  //   const index = tabList.value.findIndex((tab: any) => tab.id === tableId)
  //   console.log(index, "index")

  //   let str = "",
  //     calcRow = {}
  //   if (tableId === "m001" || tableId === "m003" || tableId === "m031") {
  //     outerLoop: for (let i = 0; i < (tabList.value[index].data?.length || 0); i++) {
  //       const item = tabList.value[index].data![i]
  //       for (let j = 0; j < item.length; j++) {
  //         const innerItem = item[j]
  //         if (innerItem.row == row) {
  //           str = `tabList.value[${index}].data[${i}][${j}].${columnProp}`
  //           calcRow = tabList.value[index].data![i][j]
  //           break outerLoop
  //         }
  //       }
  //     }
  //   } else if (tableId === "m301") {
  //     if ((tabList.value[index].data as any).statementDatas) {
  //       const rowIndex = (tabList.value[index].data as any).statementDatas[0].findIndex((da: any) => da.row == row)
  //       str = `tabList.value[${index}].data.statementDatas[0][${rowIndex}].${columnProp}`
  //       calcRow = tabList.value[index].data![rowIndex as number]
  //     }
  //   } else {
  //     const rowIndex = tabList.value[index].data?.findIndex((da: any) => da.row == row)
  //     str = `tabList.value[${index}].data[${rowIndex}].${columnProp}`
  //     calcRow = tabList.value[index].data![rowIndex as number]
  //   }
  //   // console.log(`tabList.value[${index}].data[${rowIndex}].${columnProp}`, "单元格信息")
  //   console.log(str, "str")

  //   return {
  //     info: isFormula ? eval(str) : str,
  //     calcRow,
  //     currentTabInfo: tabList.value[index],
  //   }
  // }
  // // 生成公式
  // const generateFormula = (
  //   row: { [key: string]: any },
  //   currentTabInfo: { [key: string]: any },
  //   slotColumn: { [key: string]: any },
  //   callback: (result: string) => void,
  // ) => {
  //   // console.log(callback, "callback")

  //   // console.log(currentTabInfo.id, row.row, slotColumn.prop, "生成公式")

  //   const formula = allTableFormulaList.value.formulas.find(
  //     (item: any) => item.statementCode === currentTabInfo.id && item.row == row.row && item.colName === slotColumn.prop,
  //   )
  //   // console.log(formula, "公式信息")
  //   if (formula) {
  //     const calcList = formula.expressItems.split(",")
  //     let str = "<span>公式：<span><br/>"

  //     for (let i = 0; i < formula.formulaDescList.length; i++) {
  //       const item = formula.formulaDescList[i]
  //       if (item.includes(calcList[i])) {
  //         const [tableId, row, columnProp] = calcList[i].split(".")

  //         const calcVal = getCellInfo(tableId, row, columnProp).info
  //         str += `<span>${item.replace(calcList[i], String(parseFloat(calcVal).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ","))}</span><br>`
  //       } else {
  //         str += `<span>${item}</span><br>`
  //       }
  //     }

  //     callback(str)
  //     // return str
  //   }
  // }

  function save() {
    console.log(tabList.value, "tabList")
  }

  defineExpose({ save })
</script>
<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
  .declaration-form {
    position: sticky;
    left: 0;

    .form-title {
      margin-bottom: 4px;
      font-size: var(--h3);
      text-align: center;
    }

    .form-subtitle {
      margin-bottom: 14px;
      font-weight: 400;
      font-size: var(--h4);
      color: var(--dark-grey);
      text-align: center;
    }

    .form-info {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .info {
        flex: 3;
        display: flex;
        flex-wrap: wrap;

        div {
          box-sizing: border-box;
          margin-right: 50px;

          span {
            line-height: 20px;
            font-size: var(--h4);
          }
        }
      }
      span {
        flex: 1;
        align-self: flex-end;
        line-height: 32px;
        font-size: 14px;
        text-align: right;
      }
    }
  }

  .main-table {
    :deep(.el-tabs) {
      z-index: 999;
      height: auto;
      position: sticky;
      top: 0;
      background-color: var(--white);

      .el-tabs__header {
        margin-bottom: 12px;
      }
    }

    .title {
      display: inline-block;
      padding: 8px 16px;
      margin-bottom: 15px;
      border: 1px solid var(--button-border-color);
    }

    .el-divider--horizontal {
      margin: 20px 0;
    }
  }

  .appendix-1 {
    thead {
      z-index: 999;
      position: sticky;
      top: 0;
    }
  }
  .appendix-2,
  .reduce-details {
    thead:not(:first-of-type) {
      tr th {
        border-top: 0;
      }
    }
  }
  .appendix-5 {
    thead:first-of-type {
      tr:first-child {
        td {
          border-top: 1px var(--el-border-color) var(--el-border-style);
        }
      }
      tr:last-child {
        th,
        td {
          border-bottom: 0;
        }
      }
    }
  }
</style>
