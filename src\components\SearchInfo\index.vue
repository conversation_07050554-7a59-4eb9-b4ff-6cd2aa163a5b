<template>
    <div class="search-input-box" :style="{ width: width + 'px', height: height + 'px' }">
        <el-input
            v-model="value"
            :style="{ width: width + 'px', height: height + 'px' }"
            :placeholder="placeholder"
            @keyup.enter="search"
            @change="changeValue"
            clearable
        />
        <div class="search-btn" :class="{ erp: isErp }" @click="search"></div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
const value = ref("");
defineProps<{ width: number; height: number; placeholder: string }>();
const emit = defineEmits(["search", "changeValue"]);
const isErp = ref(window.isErp);
const search = () => {
    emit("search", value.value);
};
function clear() {
    value.value = "";
}
function changeValue() {
    emit("changeValue", value.value);
}
defineExpose({
    clear,
});
</script>

<style lang="less" scoped>
.search-input-box {
    display: inline-block;
    :deep(.el-input__wrapper) {
        line-height: 30px;
        color: var(--font-color);
        font-size: var(--font-size);
        padding-right: 28px !important;
    }
    .search-btn {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background-size: 15px 16px;
        width: 25px;
        cursor: pointer;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url("@/assets/Icons/sousuo.png");

        &.erp {
            background-image: url("@/assets/Icons/search-erp.png");
        }
    }
}
</style>
