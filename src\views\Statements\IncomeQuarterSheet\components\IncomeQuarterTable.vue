<template>
    <el-table
        :class="isErp ? 'erp-table' : ''"
        :data="props.tableData"
        border
        :empty-text="emptyText"
        fit
        stripe
        scrollbar-always-on
        highlight-current-row
        class="custom-table"
        row-key="lineID"
        :row-class-name="setTitleRowStyle"
        @header-dragend="headerDragend"
    >
        <el-table-column 
            label="项目" 
            min-width="400px" 
            align="left" 
            headerAlign="center"
            prop="proName"
            :width="getColumnWidth(setModule, 'proName')"
        >
            <template #default="scope">
                <div>
                    <div :class="[assertNameClass(scope.row)]" :title="scope.row.proName">
                        <span>{{ scope.row.proName }}</span>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="行次" 
            min-width="60" 
            align="left" 
            header-align="left" 
            prop="lineNumber" 
            :formatter="rowNumberFormatter"
            :width="getColumnWidth(setModule, 'lineNumber')"
        >
        </el-table-column>
        <el-table-column 
            label="本年累计金额" 
            min-width="250" 
            align="right" 
            header-align="right"
            prop="yearTotal"
            :width="getColumnWidth(setModule, 'yearTotal')"
        >
            <template #default="scope">
                <TableAmountItem
                    :amount="scope.row.yearTotal"
                    :formula="calcFormula(scope.row, quarterShow ? 0 : 1)"
                    :line-number="scope.row.lineNumber"
                ></TableAmountItem>
            </template>
        </el-table-column>
        <template v-if="!quarterShow">
            <el-table-column
                :label="!props.isTax ? '本季金额' : accountStandard === 1 ? '上年累计金额' : '上年同期累计金额'"
                min-width="250"
                align="right"
                header-align="right"
                :resizable="false"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.monthTotal"
                        :formula="calcFormula(scope.row, 0)"
                        :line-number="scope.row.lineNumber"
                    ></TableAmountItem>
                </template>
            </el-table-column>
        </template>
        <template v-else>
            <el-table-column 
                label="第一季度" 
                min-width="150" 
                align="right" 
                header-align="right" 
                :formatter="rowNumberFormatter"
                prop="quater1"
                :width="getColumnWidth(setModule, 'quater1')"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.quater1"
                        :formula="calcFormula(scope.row, 1)"
                        :line-number="scope.row.lineNumber"
                    ></TableAmountItem>
                </template>
            </el-table-column>
            <el-table-column 
                label="第二季度" 
                min-width="150" 
                align="left" 
                header-align="right" 
                :formatter="rowNumberFormatter"
                prop="quater2"
                :width="getColumnWidth(setModule, 'quater2')"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.quater2"
                        :formula="calcFormula(scope.row, 2)"
                        :line-number="scope.row.lineNumber"
                    ></TableAmountItem>
                </template>
            </el-table-column>
            <el-table-column 
                label="第三季度" 
                min-width="150" 
                align="left" 
                header-align="right" 
                :formatter="rowNumberFormatter"
                prop="quater3"
                :width="getColumnWidth(setModule, 'quater3')"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.quater3"
                        :formula="calcFormula(scope.row, 3)"
                        :line-number="scope.row.lineNumber"
                    ></TableAmountItem> </template
            ></el-table-column>
            <el-table-column 
                label="第四季度" 
                min-width="150" 
                align="left" 
                header-align="right" 
                :formatter="rowNumberFormatter"
                :resizable="false"
            >
                <template #default="scope">
                    <TableAmountItem
                        :formula="calcFormula(scope.row, 4)"
                        :amount="scope.row.quater4"
                        :line-number="scope.row.lineNumber"
                    ></TableAmountItem> </template
            ></el-table-column>
        </template>
    </el-table>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import type { IIncomeSheetQuarter } from "../types";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "IncomeQuaterSheet";
const isErp = ref(window.isErp);

const props = defineProps({
    tableData: { type: Array<IIncomeSheetQuarter> },
    quarterShow: { type: Boolean },
    isTax: { type: Boolean },
    accountStandard: { type: Number },
    emptyText: { type: String },
});
//判断level1 2
const hasNumberTitle = (value: string) => {
    const chinaNumber = [
        "一、",
        "二、",
        "三、",
        "四、",
        "五、",
        "六、",
        "七、",
        "八、",
        "九、",
        "十、",
        "(一)",
        "(二)",
        "(四)",
        "(五)",
        "(六)",
        "(七)",
        "(八)",
        "(九)",
        "(十)",
        "（一）",
        "（二）",
        "（三）",
        "（四）",
    ];
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.indexOf(chinaNumber[i]) > -1) return true;
    }
    return false;
};
//判定assertnameclass
function assertNameClass(row: IIncomeSheetQuarter) {
    let className: string;
    if (row.expand == 1) {
        className = "level2";
    } else if (row.lineNumber == 0 || hasNumberTitle(row.proName)) {
        className = "level1";
    } else {
        className = "level2";
    }
    return className;
}
const hasNumberTitle2 = (value: string) => {
    const chinaNumber = ["一、", "二、", "三、", "四、", "五、", "六、", "七、", "八、", "九、", "十、"];
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.indexOf(chinaNumber[i]) > -1) return true;
    }
    return false;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle2(data.row.proName)) {
        return "highlight-title-row";
    }
}
//行次
function rowNumberFormatter(_row: any, _column: any, cellValue: any) {
    if (cellValue === 0) {
        return "";
    } else {
        return cellValue;
    }
}

//
function calcFormula(row: IIncomeSheetQuarter, columnIndex: number) {
    return row.note?.split("|")[columnIndex];
}
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped></style>
