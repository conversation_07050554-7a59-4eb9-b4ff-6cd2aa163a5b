<template>
    <div class="progress-content">
        <div class="title">进度查询</div>
        <div class="main-center" v-if="preOpenList.length">
            <div class="card" v-for="item in preOpenList" :key="item.id">
                <div class="card-header">
                    当前状态：<span :class="item.openState === ProgressStatus.Successded ? 'finished' : 'handling'">{{
                        formatStatus(item.openState, "content")
                    }}</span>
                </div>
                <div class="card-content">
                    <div class="card-content-left">
                        <div :class="handleBankImg(item.bankType)" class="bankimg"></div>
                        <div class="accountinfo">
                            <div class="companyname" v-if="item.companyName">{{ item.companyName }}</div>
                            <div class="fs-14" v-if="item.branchName">开户网点：{{ item.branchName }}</div>
                            <div class="fs-14" v-if="item.accountType">选择账户类型：{{ getAccountType(item.accountType) }}</div>
                        </div>
                    </div>
                    <!-- 微众银行隐藏查看详情 -->
                    <div class="card-content-right" v-show="item.bankType !== BankType.WEBANK" @click="toDetail(item.id, item.bankType)">查看详情</div>
                </div>
                <div class="card-footer">
                    <el-collapse>
                        <el-collapse-item title="展开" :name="item.id">
                            <div :class="['pro-step-content', {'webank-height': item.bankType === BankType.WEBANK}]"> 
                                <div class="pro-step-lt">
                                    <el-steps direction="vertical" process-status="finish" :active="formatStatus(item.openState, 'active')">
                                        <el-step :title="item.bankType !== BankType.WEBANK ? '提交预约申请' : '提交公司基础信息【完成】'">
                                            <template #description>
                                                <div v-if="item.bankType === BankType.WEBANK">
                                                    <div>公司名称：{{ item.companyName }}</div>
                                                    <div>法人姓名：{{ item.legalPersonName }}</div>
                                                </div>
                                                <div>申请时间：{{ item.createTime }}</div>
                                            </template>
                                            <template #icon>
                                                <span>1</span>
                                            </template>
                                        </el-step>
                                        <el-step :title="item.bankType !== BankType.WEBANK ? '银行审批中' : '银行审批中' + getWeBankSecondStatus(item.openState, 'progress')">
                                            <template #description>
                                                <div>{{ getStepTwoInfo(item) }}</div>
                                            </template>
                                            <template #icon>
                                                <span>2</span>
                                            </template>
                                        </el-step>
                                        <el-step :title="item.bankType !== BankType.WEBANK ? '线下网点面签' : '线上开户' + getWeBankSecondStatus(item.openState, 'result')" v-if="![BankType.SPDB, BankType.CMB].includes(item.bankType)">
                                            <template #description>
                                                <div v-if="item.bankType !== BankType.WEBANK">银行线上审批通过，请您尽快携带相关资料前</div>
                                                <div v-else> {{ getWEBankResultInfo(item.openState) }}</div>
                                            </template>
                                            <template #icon>
                                                <span>3</span>
                                            </template>
                                        </el-step>
                                    </el-steps>
                                </div>
                                <div class="webank-scan-code" v-if="item.bankType === BankType.WEBANK && item.openState === ProgressStatus.ApprovalSucceeded">
                                    <img src="@/assets/Cashier/webank-code.png" alt="">
                                    <div class="mt-10">打开微信扫一扫立即开户</div>
                                </div>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </div>
        </div>
        <div class="main-center" v-else>
            <div class="empty-content">
                <img src="@/assets/Cashier/nodata.png" />
            </div>
        </div>
        <div class="buttons">
            <a class="button" @click="back">返回</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { type IItemProgressInfo, ProgressStatus } from "../types";
import { BankType } from "@/constants/bankKey";

const props = defineProps({
    systemType: { type: Number, required: true, default: 0 },
});
const emit = defineEmits<{
    (e: "back"): void;
    (e: "toDetail", id: string, bankTypeId: number): void;
}>();
const currentAsid = useAccountSetStore().accountSet?.asId;

const back = () => {
    emit("back");
};
function getStepTwoInfo(row: IItemProgressInfo) {
    let info = "银行正在审核预约资料，请耐心等待～";
    if (row.bankType === BankType.SPDB) info = "银行正在处理中，请留意银行发送的短信信息，具体进度查询请以银行通知为准";
    if (row.bankType === BankType.CMB) info = "银行正在处理中，后续将由银行客户经理对接跟进，具体进度查询请以银行通知为准";
    if (row.bankType === BankType.WEBANK) info = "银行正在审核您的开户资质，请耐心等待~";
    return info;
}

//获取预约信息
const preOpenList = ref<IItemProgressInfo[]>([]);
const getPreOpenList = () => {
    request({
        url: window.preOpenBankUrl + `/api/PreOpen/List`,
        method: "get",
        params: {
            asId: currentAsid,
            system: props.systemType,
        },
    }).then((res: IResponseModel<IItemProgressInfo[]>) => {
        if (res.state === 1000) {
            preOpenList.value = res.data;
            handleChange();
        }
    });
};
const handleBankImg = (bankTypeId: number) => {
    return BankType[bankTypeId]?.toLowerCase() ?? "";
};
const formatStatus = (openState: number, type: "content" | "active"): any => {
    const isContent = type === "content";
    const isAcitve = type === "active";
    switch (openState) {
        case ProgressStatus.Fail:
            if (isContent) {
                return "预约开户失败";
            }
            if (isAcitve) {
                return 3;
            }
            return "";
        case ProgressStatus.Staging:
            if (isContent) {
                return "暂存中，未提交";
            }
            if (isAcitve) {
                return 0;
            }
            return "";
        case ProgressStatus.Successded:
            if (isContent) {
                return "预约开户成功";
            }
            if (isAcitve) {
                return 3;
            }
            return "";
        case ProgressStatus.UnderApproval:
            if (isContent) {
                return "审批中";
            }
            if (isAcitve) {
                return 1;
            }
            return "";
        case ProgressStatus.ApprovalSucceeded:
            if (isContent) {
                return "审批通过";
            }
            if (isAcitve) {
                return 2;
            }
            return "";
        case ProgressStatus.ApprovalFail:
            if (isContent) {
                return "审批拒绝";
            }
            if (isAcitve) {
                return 2;
            }
            return "";
        case ProgressStatus.Canceled:
            if (isContent) {
                return "取消";
            }
            if (isAcitve) {
                return 3;
            }
            return "";
        default:
            return;
    }
};

const getAccountType = (type: string) => {
    return type === "1" ? "已有基本账户" : "没有基本账户";
};
const getWeBankSecondStatus = (openState: number, step: string): string => {
    const status = (step === "progress")   
        ? (openState === ProgressStatus.UnderApproval ? "【处理中】" : "【完成】")   
        : (openState === ProgressStatus.UnderApproval 
            ? "【未开始】" : (openState === ProgressStatus.ApprovalSucceeded ? "【审批通过】" : "【审批拒绝】")
        );  
    return status;  
};
const getWEBankResultInfo = (openState: number):string => {
    switch (openState) {
        case ProgressStatus.UnderApproval:
            return "开户资质审核通过后，请您通过扫描线上开户二维码完成开户";
        case ProgressStatus.ApprovalSucceeded:
            return "开户资质审核已通过，请您扫描右侧二维码进行线上开户操作";
        case ProgressStatus.ApprovalFail:
            return "开户资质审核失败，无法进行线上开户";
        default:
            return "";
    }
};
const handleChange = () => {
    preOpenList.value.forEach((item) => {
        if (BankType.WEBANK === item.bankType && !item.legalPersonName) {
            expandDetail(item.id);
        }
    });
};
const expandDetail = (id: string) => {
    request({
        url: window.preOpenBankUrl + `/api/PreOpen/GetDetailsInfo`,
        method: "get",
        params: {
            id: id,
        }
    }).then((res: any) => {
        let item = preOpenList.value.find((item) => item.id === id);
        if (!item) return;
        if (!item.legalPersonName) {
            item["legalPersonName"] = res.content.legalName;
        }
    });
};

const toDetail = (id: string, bankTypeId: number) => {
    emit("toDetail", id, bankTypeId);
};

onMounted(() => {
    getPreOpenList();
});

defineExpose({
    getPreOpenList,
});
</script>

<style scoped lang="less">
.pagination.datagrid-pager {
    margin-top: 20px;
    border: none;
    width: 892px;
}
.progress-content {
    .main-center {
        // padding-top: 28px !important;
        .empty-content {
            padding-top: 60px;
            height: 600px;
        }
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        :deep(.el-card) {
            width: 896px;
            margin-top: 18px;
            text-align: left;
        }
        .card {
            width: 896px;
            margin-top: 18px;
            text-align: left;
            border: 1px solid #e6e6e6;

            .card-header {
                line-height: 48px;
                height: 48px;
                border-bottom: 1px solid #e6e6e6;
                padding: 0px 28px;
                font-size: 14px;
                .handling {
                    color: #ff991e;
                    font-weight: bold;
                }
                .finished {
                    color: #333333;
                    font-weight: bold;
                }
            }
            .card-content {
                height: 100px;
                border-bottom: 1px solid #e6e6e6;
                padding: 0px 28px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .card-content-left {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .bankimg {
                        margin-right: 28px;
                        &.icbc {
                            width: 120px;
                            height: 82px;
                            background: url("@/assets/Cashier/ICBC.png") no-repeat;
                            background-size: 100% 100%;
                        }
                        &.pabank {
                            width: 120px;
                            height: 82px;
                            background: url("@/assets/Cashier/PAB.png") no-repeat;
                            background-size: 100% 100%;
                        }
                        &.cmb {
                            width: 120px;
                            height: 82px;
                            background: url("@/assets/Cashier/CMB.png") no-repeat;
                            background-size: 100% 100%;
                        }
                        &.spdb {
                            width: 120px;
                            height: 82px;
                            background: url("@/assets/Cashier/SPDB.png") no-repeat;
                            background-size: 100% 100%;
                        }
                        &.psbc {
                            width: 120px;
                            height: 82px;
                            background: url("@/assets/Cashier/PSBC.png") no-repeat;
                            background-size: 100% 100%;
                        }
                        &.webank {
                            width: 120px;
                            height: 82px;
                            background: url("@/assets/Cashier/WEBANK.png") no-repeat;
                            background-size: 100% 100%;
                        }
                    }
                    .accountinfo {
                        display: flex;
                        flex-direction: column;
                        .companyname {
                            height: 20px;
                            font-size: 16px;
                            font-weight: bold;
                            color: #333333;
                            line-height: 20px;
                        }
                        .fs-14 {
                            font-size: 14px;
                            line-height: 20px;
                            height: 20px;
                        }
                    }
                }
                .card-content-right {
                    width: 100px;
                    height: 32px;
                    line-height: 32px;
                    text-align: center;
                    background: #ffffff;
                    border: 1px solid #00c256;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight: bold;
                    color: #00c256;
                    border-radius: 25px;
                }
            }
            .card-footer {
                // height: 48px;
                text-align: right;
                padding: 1px 28px;
                :deep(.el-collapse) {
                    border: none;
                    .el-collapse-item {
                        .el-collapse-item__header {
                            border: none;
                            justify-content: end;
                            font-size: 14px;
                            color: #666666;
                            .el-collapse-item__arrow {
                                margin: 0;
                            }
                        }
                        .el-collapse-item__wrap {
                            border: none;
                            .el-collapse-item__content {
                                .el-steps {
                                    .el-step__head {
                                        .el-step__line {
                                            color: #f2f3f5;
                                            background-color: #f2f3f5;
                                        }
                                        .el-step__icon {
                                            width: 24px;
                                            height: 24px;
                                            text-align: center;
                                            background-color: #dddddd;
                                            border: none;
                                            border-radius: 50%;
                                            color: #ffffff;
                                        }
                                        &.is-finish {
                                            .el-step__icon {
                                                background-color: #00c256;
                                            }
                                        }
                                    }

                                    .el-step__title {
                                        color: #333333;
                                        font-weight: 500;
                                    }
                                    .el-step__description {
                                        color: #666666;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
.pro-step-content {
    display: flex;
    height: 220px;
    &.webank-height {
        height: 250px;
    }
    .pro-step-lt {
        flex: 1;
        min-width: 0;
    }
    .webank-scan-code {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        width: 200px;
        text-align: center;
        > img {
            margin: 0 auto;
            display: block;
            width: 150px;
            height: 150px;
        }
    }
}
</style>
