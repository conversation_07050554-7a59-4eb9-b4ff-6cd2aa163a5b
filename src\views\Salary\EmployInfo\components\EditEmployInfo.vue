<template>
    <div class="edit-content">
        <div class="title">{{ props.title }}</div>
        <div class="main-center">
            <div class="subtitle">基本信息</div>
            <form>
                <table class="form-table" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="td-title"><span class="highlight-red">*</span>编号：</td>
                        <td class="td-field">
                            <ToolTip :content="editData[1]" :isInput="true">
                                <el-input
                                    ref="codeRef"
                                    v-model="editData[1]"
                                    style="width: 180px"
                                    autocomplete="on"
                                    name="personId"
                                    type="text"
                                    @input="handleAAInput(LimitCharacterSize.Default, { target: { value: $event } }, '编号', 1, changeEditInfo)"
                                    @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                                ></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>姓名：</td>
                        <td class="td-field">
                            <ToolTip :content="editData[2]" :isInput="true">
                                <el-input
                                    ref="nameRef"
                                    v-model="editData[2]"
                                    style="width: 180px"
                                    autocomplete="on"
                                    :maxlength='256'
                                    @input='limitInputLength(editData[2])'
                                    name="name"
                                    type="text"
                                ></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>性别：</td>
                        <td class="td-field">
                            <div class="jqtransform">
                                <!-- automatic-dropdown -->
                                <el-select
                                    ref="genderRef"
                                    v-model="editData[3]"
                                    style="width: 180px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                >
                                    <div style="max-height: 170px">
                                        <el-option label="男" value="1" />
                                        <el-option label="女" value="2" />
                                    </div>
                                </el-select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-title"><span class="highlight-red">*</span>证件类型：</td>
                        <td class="td-field">
                            <div class="jqtransform" style="width: 180px">
                                <Select
                                    ref="typeRef"
                                    v-model="editData[21]"
                                    style="width: 180px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                    :filterable="true"
                                    :filter-method="idCardFilterMethod"
                                >
                                    <div style="max-height: 170px">
                                        <Option v-for="item in showIdCardList" :label="item.label" :value="item.value" :key="item.value"></Option>
                                    </div>
                                </Select>
                            </div>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>证件号码：</td>
                        <td class="td-field">
                            <el-input
                                ref="idRef"
                                v-model="editData[4]"
                                style="width: 180px"
                                @blur="getBirthById"
                                @keyup.enter="getBirthById"
                                autocomplete="on"
                                name="idnumber"
                                type="text"
                            ></el-input>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>出生日期：</td>
                        <td class="td-field">
                            <el-date-picker
                                ref="birthdayRef"
                                v-model="editData[7]"
                                style="width: 180px"
                                clearable
                                :disabled-date="disabledBirthday"
                            ></el-date-picker>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-title"><span class="highlight-red">*</span>手机：</td>
                        <td class="td-field">
                            <el-input
                                ref="mobileRef"
                                v-model="editData[5]"
                                style="width: 180px"
                                @blur="validateData(5)"
                                autocomplete="on"
                                name="telphone"
                                type="text"
                            ></el-input>
                        </td>
                        <td class="td-title">邮箱：</td>
                        <td class="td-field">
                            <ToolTip :content="editData[6]" :isInput="true">
                                <el-input
                                    ref="emailRef"
                                    v-model="editData[6]"
                                    style="width: 180px"
                                    @blur="validateData(6)"
                                    autocomplete="on"
                                    name="per_email"
                                    type="text"
                                ></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title">学历：</td>
                        <td class="td-field">
                            <div class="jqtransform">
                                <el-select
                                    ref="educationRef"
                                    v-model="editData[8]"
                                    style="width: 180px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                    :filterable="true"
                                    :filter-method="educationFilterMethod"
                                >
                                    <div style="max-height: 170px">
                                        <el-option v-for="item in showEducationList" :label="item.label" :value="item.value" :key="item.value" />
                                    </div>
                                </el-select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-title">银行名称：</td>
                        <td class="td-field">
                            <ToolTip :content="editData[10]" :isInput="true">
                                <el-input ref="bankRef" v-model="editData[10]" style="width: 180px"></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title">银行账号：</td>
                        <td class="td-field">
                            <ToolTip :content="editData[11]" :isInput="true">
                                <el-input ref="bankAccountRef" v-model="editData[11]" style="width: 180px"></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>入职日期：</td>
                        <td class="td-field">
                            <!-- @blur="validateData(9)" -->
                            <el-date-picker
                                ref="startDateRef"
                                v-model="editData[9]"
                                style="width: 180px"
                                :editable="false"
                                clearable
                            ></el-date-picker>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-title"><span class="highlight-red">*</span>部门：</td>
                        <td class="td-field">
                            <div style="width: 180px">
                                <Select
                                    :fitInputWidth="true"
                                    v-model="editData[22]"
                                    :teleported="true"
                                    :bottom-html="checkPermission(['assistingaccount-canedit']) ? newDepartmentHtml : ''"
                                    @bottom-click="clickAddDepartment"
                                    :filterable="true"
                                    :filter-method="departFilterMethod"
                                >
                                    <Option 
                                        v-for="item in showdepartmentList" 
                                        :key="item.value" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </Select>
                            </div>
                        </td>
                        <td class="td-title">岗位：</td>
                        <td class="td-field">
                            <ToolTip :isInput="true" :content="editData[14]">
                                <el-input ref="positionRef" v-model="editData[14]" style="width: 180px"></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>工资始发日期：</td>
                        <td class="td-field">
                            <!-- @blur="validateData(12)" -->
                            <el-date-picker
                                ref="salaryStartDateRef"
                                v-model="editData[12]"
                                style="width: 180px"
                                :editable="false"
                                clearable
                            ></el-date-picker>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-title">职称：</td>
                        <td class="td-field">
                            <ToolTip :isInput="true" :content="editData[15]">
                                <el-input ref="titleRef" v-model="editData[15]" style="width: 180px"></el-input>
                            </ToolTip>
                        </td>
                        <td class="td-title"><span class="highlight-red">*</span>计提工资科目：</td>
                        <td class="td-field">
                            <div :class="['select-content-c', {'warning': isAAEWarning}]" style="width: 180px; position: relative">
                                <SelectV2
                                    ref="subjectRef"
                                    :options="showSalaryAsubList"
                                    :teleported="false"
                                    :fit-input-width="true"
                                    :filterable="true"
                                    placeholder="请选择"
                                    v-model="editData[17]"
                                    :props="salaryAsubProps"
                                    :toolTipOptions="{ dynamicWidth: true }"
                                    :isOuterTooltip="false"
                                    @focus="isAAEWarning = false"
                                    @visible-change="clickChange"
                                    :remote="true"
                                    :filter-method="salaryAsubFilterMethod"
                                    :isSuffixIcon="true"
                                ></SelectV2>
                            </div>
                        </td>
                        <td class="td-title">备注：</td>
                        <td class="td-field">
                            <ToolTip :isInput="true" :content="editData[16]" :teleported="true" placement="right">
                                <el-input ref="noteRef" v-model="editData[16]" style="width: 180px"></el-input>
                            </ToolTip>
                        </td>
                    </tr>
                    <tr>
                        <td class="td-title"><span class="highlight-red">*</span>员工状态：</td>
                        <td class="td-field">
                            <div class="jqtransform">
                                <el-select
                                    ref="statusRef"
                                    v-model="editData[18]"
                                    style="width: 180px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                >
                                    <div style="max-height: 170px">
                                        <el-option label="在职" value="0" />
                                        <el-option label="离职" value="1" />
                                    </div>
                                </el-select>
                            </div>
                        </td>
                        <template v-if="editData[18] === '1'">
                            <td class="td-title" id="OutDate"><span class="highlight-red">*</span>离职日期：</td>
                            <td id="OutDateInput" class="td-field">
                                <el-date-picker v-model="editData[19]" style="width: 180px"></el-date-picker>
                            </td>
                            <td class="td-title" id="SalaryStop"><span class="highlight-red">*</span>工资停发日期：</td>
                            <td id="SalaryStopSelect" class="td-field">
                                <el-date-picker v-model="editData[20]" style="width: 180px"></el-date-picker>
                            </td>
                        </template>
                    </tr>
                </table>
            </form>
            <div class="line"></div>
            <div class="subtitle">扣除款项</div>
            <div class="form-line">
                <div class="form-title"><span class="highlight-red">*</span>是否缴纳五险一金：</div>
                <div class="form-field">
                    <el-radio-group v-model="editData[24]" class="ml-4">
                        <el-radio label="1" size="large">是</el-radio>
                        <el-radio label="0" size="large">否</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <template v-if="editData[24] === '1'">
                <div class="ss-table-container">
                    <Table 
                        :data="insuranceData" 
                        :columns="columns" 
                        :loading="loading" 
                        :showOverflowTooltip="false"
                        :tableName="setModule"
                    >
                        <template #name>
                            <el-table-column 
                                label="项目" 
                                header-align="left" 
                                align="left"
                                prop="name"
                                :width="getColumnWidth(setModule, 'name')"
                            >
                                <template v-slot="scope">
                                    {{ scope.row.e_code_name }}
                                </template>
                            </el-table-column>
                        </template>
                        <template #base>
                            <el-table-column 
                                label="缴纳基数" 
                                header-align="center" 
                                align="center"
                                prop="base"
                                :width="getColumnWidth(setModule, 'base')"
                            >
                                <template v-slot="scope">
                                    <el-input
                                        v-if="!scope.row.pay_mode"
                                        :ref="
                                            (el) => {
                                                baseRef[scope.$index] = el;
                                            }
                                        "
                                        autocomplete="on"
                                        name="base"
                                        v-model="scope.row.base"
                                        style="width: 100%"
                                        type="text"
                                        @blur="formatBaseNum($event, scope.row)"
                                        @keyup.enter="formatBaseNum($event, scope.row, scope.$index)"
                                    ></el-input>
                                </template>
                            </el-table-column>
                        </template>
                        <template #company>
                            <el-table-column label="公司承担部分" header-align="center">
                                <el-table-column 
                                    label="公司缴纳比例" 
                                    header-align="left" 
                                    align="right"
                                    prop="companyPercent"
                                    :width="getColumnWidth(setModule, 'companyPercent')"
                                >
                                    <template #default="scope">
                                        <div class="item-percent" v-if="!scope.row.pay_mode && (!(
                                                    (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                    scope.row.com_percent == '0'
                                                ))">
                                            <el-input
                                                :ref="
                                                    (el) => {
                                                        comPercentRef[scope.$index] = el;
                                                    }
                                                "
                                                autocomplete="on"
                                                name="com_percent"
                                                type="text"
                                                v-model="scope.row.com_percent"
                                                style="width: 89%"
                                                @blur="formatCPercent($event, scope.row, 'com')"
                                                @keyup.enter="formatCPercent($event, scope.row, 'com', scope.$index)"
                                            ></el-input>
                                            <span style="padding-left: 2px">%</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column 
                                    label="公司缴纳金额" 
                                    header-align="left" 
                                    align="right" 
                                    prop="companyAmount"
                                    :width="getColumnWidth(setModule, 'companyAmount')"
                                >
                                    <template #default="scope">
                                        <el-input
                                            :ref="
                                                (el) => {
                                                    comAmountRef[scope.$index] = el;
                                                }
                                            "
                                            v-if="
                                                !(
                                                    (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                    scope.row.com_percent == '0' && (!scope.row.pay_mode)
                                                )
                                            "
                                            v-model="scope.row.com_amount"
                                            style="width: 100%"
                                            autocomplete="on"
                                            name="com_amount"
                                            type="text"
                                            @blur="handleCPAmount($event, scope.row, 'com')"
                                            @keyup.enter="handleCPAmount($event, scope.row, 'com', scope.$index)"
                                        ></el-input>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                        </template>
                        <template #person>
                            <el-table-column label="个人承担部分" header-align="center">
                                <el-table-column 
                                    label="个人缴纳比例" 
                                    header-align="left" 
                                    align="right"
                                    prop="personPercent"
                                    :width="getColumnWidth(setModule, 'personPercent')"
                                >
                                    <template #default="scope">
                                        <div
                                            class="item-percent"
                                            style="text-align: left"
                                            v-if="
                                                !scope.row.pay_mode && (
                                                !(
                                                    (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                    scope.row.per_percent == '0'
                                                ))
                                            "
                                        >
                                            <el-input
                                                :ref="
                                                    (el) => {
                                                        perPercentRef[scope.$index] = el;
                                                    }
                                                "
                                                autocomplete="on"
                                                name="per_percent"
                                                type="text"
                                                v-model="scope.row.per_percent"
                                                style="width: 89%"
                                                @blur="formatCPercent($event, scope.row, 'per')"
                                                @keyup.enter="formatCPercent($event, scope.row, 'per', scope.$index)"
                                            ></el-input>
                                            <span style="padding-left: 2px">%</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="个人缴纳金额" header-align="left" align="right" :resizable="false">
                                    <template #default="scope">
                                        <el-input
                                            :ref="
                                                (el) => {
                                                    perAmountRef[scope.$index] = el;
                                                }
                                            "
                                            name="per_amount"
                                            autocomplete="on"
                                            type="text"
                                            v-model="scope.row.per_amount"
                                            v-if="
                                                !(
                                                    (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                    scope.row.per_percent == '0' && (!scope.row.pay_mode)
                                                )
                                            "
                                            style="width: 100%"
                                            @blur="handleCPAmount($event, scope.row, 'per')"
                                            @keyup.enter="handleCPAmount($event, scope.row, 'per', scope.$index)"
                                        ></el-input>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </template>
            <div class="line"></div>
            <div class="buttons">
                <a class="button solid-button" id="submit" @click="submitEmployee">保存</a>
                <a class="button ml-10" @click="cancelEdit">取消</a>
            </div>
        </div>
    </div>
    <el-dialog v-model="departmentAdd" title="创建新部门" center width="576px" class="dialogDrag">
        <div class="departmentAddContent" v-dialogDrag>
            <el-form ref="departemntRef" :model="departmentAddForm">
                <el-row class="isRow requiredProp">
                    <el-form-item label="部门编号：" prop="departmentCode">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Code, $event, '部门编号', 'departmentCode', changeSearchInfo)"
                            @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                            v-model="departmentAddForm.departmentCode"
                        />
                    </el-form-item>
                    <el-form-item label="部门名称：" prop="departmentName">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Name, $event, '部门名称', 'departmentName', changeSearchInfo)"
                            @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                            v-model="departmentAddForm.departmentName"
                        />
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="负责人：" prop="departmentManager">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Default, $event, '负责人', 'departmentManager', changeSearchInfo)"
                            @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                            v-model="departmentAddForm.departmentManager"
                        />
                    </el-form-item>
                    <el-form-item label="手机：" prop="departmentManagerPhone">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机', 'departmentManagerPhone', changeSearchInfo)"
                            @paste="handleAAPaste(LimitCharacterSize.Phone, $event)"
                            v-model="departmentAddForm.departmentManagerPhone"
                        />
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="成立日期：" prop="departmentEstablishDate">
                        <el-date-picker
                            v-model="departmentAddForm.departmentEstablishDate"
                            :disabled-date-start="disabledDateStart"
                            style="width: 160px; height: 28px"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="撤销日期：" prop="departmentCancelDate">
                        <el-date-picker
                            v-model="departmentAddForm.departmentCancelDate"
                            :disabled-date="disabledDateEnd"
                            style="width: 160px; height: 28px"
                        ></el-date-picker>
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="备注：" prop="departmentRemark">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Note, $event, '备注', 'departmentRemark', changeSearchInfo)"
                            @paste="handleAAPaste(LimitCharacterSize.Note, $event)"
                            v-model="departmentAddForm.departmentRemark"
                        />
                    </el-form-item>
                </el-row>
            </el-form>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click="SaveDepartment">保存</a>
            <a class="button ml-10" @click="cancelDepartment">取消</a>
        </div>
    </el-dialog>
    <AddAssistingAccountingEntryDialog
        ref="addAssistingAccountingEntryDialogRef"
        @save-success="erpAAESaveSuccess"
    ></AddAssistingAccountingEntryDialog>
    <AuxiliaryDialog
        ref="AuxiliaryDialogRef"
        v-model="auxiliaryDialogShow"
        :title="'计提工资科目'"
        :asubName="auxiliaryAsubName"
        :aacode="auxiliaryCode"
        :aaReadonly="auxiliaryReadonly"
        :aaAllowNull="auxiliaryAllowNull"
        :departId="editData[22] ? editData[22] : ''"
        :employName="editData[2].trim()"
        @setAsubAAE="setAsubAAE"
        :aaId="auxiliaryId"
    ></AuxiliaryDialog>
    <el-dialog 
        title="提示" 
        center 
        v-model="departChangeShow" 
        width="440px" 
        @close="cancelSubjectDepart"
        class="dialogDrag"
    >
        <div class="box-main" v-dialogDrag>
            <div style="font-size: 14px; color: #333333">员工部门发生变化，计提工资科目的部门辅助核算是否需要修改？</div>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click="updateSubjectDepart">修改</a>
            <a class="button  ml-10" @click="cancelSubjectDepart">取消</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import Option from "@/components/Option/index.vue";
import ToolTip from "@/components/ToolTip/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISelectItem, IInsuranceItem} from "../types";
import { toDecimal2 } from "@/util/format";
import { request } from "@/util/service";
import { ValidateEmplotInfo, IsEmail, IsID, IsPhone } from "../validator";
import { ElNotify } from "@/util/notify";
import { ElConfirm,  ElAlert} from "@/util/confirm";
import dayjs from "dayjs";
import { ref, reactive, onMounted, watchEffect, nextTick, watch, onUnmounted, computed } from "vue";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { checkPermission } from "@/util/permission";
import { useRoute } from "vue-router";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import AuxiliaryDialog from "./AuxiliaryDialog.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { getFormatterDate } from "@/util/date";
import { commonFilterMethod } from "@/components/Select/utils";
import { idCardList, educationList } from "../utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";

const setModule = "EditEmployInfo"
const accountingStandard = useAccountSetStore().accountSet?.accountingStandard as number;
const codeRef = ref();
const nameRef = ref();
const genderRef = ref();
const typeRef = ref();
const idRef = ref();
const birthdayRef = ref();
const mobileRef = ref();
const emailRef = ref();
const educationRef = ref();
const bankRef = ref();
const bankAccountRef = ref();
const startDateRef = ref();
const positionRef = ref();
const salaryStartDateRef = ref();
const titleRef = ref();
const subjectRef = ref();
const noteRef = ref();
const statusRef = ref();

const loading = ref(false);
const departemntRef = ref();
const props = defineProps({
    title: {
        type: String,
        default: "",
    },
    asId: {
        type: Number,
        default: 0,
    },
    E_SN: {
        type: Number,
        default: 0,
    },
    subject: {
        type: String,
        default: "",
    },
    saveType: {
        type: String,
        default: "",
    },
    tableData: {
        type: Array<any>,
        default: () => [],
    },
});
const emits = defineEmits(["cancelEdit", "successModify", "saveSubject"]);

const departmentList = ref<ISelectItem[]>([]);
const editData = ref(["", "", "", "1", "", "", "", "", "5", "", "", "", "", "", "", "", "", "", "0", "", "", "1", "总经办", "", "1", ""]);

const insuranceData = ref<IInsuranceItem[]>([]);
const columns = ref<Array<IColumnProps>>([
    {
        slot: "name",
    },
    { slot: "base" },
    { slot: "company" },
    { slot: "person" },
]);

const departmentAdd = ref(false);
const departmentAddForm = reactive<any>({
    departmentCode: "",
    departmentName: "",
    departmentManager: "",
    departmentManagerPhone: "",
    departmentEstablishDate: "",
    departmentCancelDate: "",
    departmentRemark: "",
});
const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
const newDepartmentHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">
            +添加新部门
        </a>
    </div>`;

const changeSearchInfo = (key: string, value: string) => {
    departmentAddForm[key] = value;
}; 
const changeEditInfo = (key: number, value: string) => {
    editData.value[key] = value;
};

const validateData = (val: 1 | 2 | 4 | 5 | 6 | 9 | 12) => {
    // 身份证号
    if (val === 4) {
        // 格式校验
        IsID(editData.value[21], editData.value[val]);
        // 身份证号重复
        if (props.saveType === "add" && props.tableData.some((item) => item.e_id === editData.value[4])) {
            editData.value[4] = "";
            return ElNotify({
                type: "error",
                message: "证件号码已重复，请重新输入",
            });
        }
    }
    // 手机号
    if (val === 5) {
        IsPhone(editData.value[5]);
    }
    // 邮箱
    if (val === 6) {
        IsEmail(editData.value[6]);
    }
};
function limitInputLength(val:string) {
        if(val.length===256){
            ElNotify({ type: "warning", message: `亲，姓名不能超过256个字哦~` });
        }
}
function getBirthById() {
    if (editData.value[21] != "1") return;
    let idcode = editData.value[4];
    let birthday = "";
    if (idcode.length === 18) {
        if (IsOnlyWordNumber(idcode)) {
            let year = idcode.slice(6, 10);
            if (Number(year) < 1900) {
                ElNotify({
                    type: "warning",
                    message: "请输入正确格式的身份证号！",
                });
                return false;
            }
            let month = idcode.slice(10, 12);
            let day = idcode.slice(12, 14);
            birthday = year + "-" + month + "-" + day;
            if (IsDate(birthday) && isNotFutureDate(birthday)) {
                editData.value[7] = birthday;
            } else {
                ElNotify({
                    type: "warning",
                    message: "请输入正确格式的身份证号！",
                });
                return false;
            }
        } else {
            ElNotify({
                type: "warning",
                message: "请输入正确格式的身份证号！",
            });
            return false;
        }
    } else if (idcode.length != 0) {
        ElNotify({
            type: "warning",
            message: "请输入正确格式的身份证号！",
        });
        return false;
    }
}

//计提工资科目
const salaryAsubProps = {
    value: "value",
    label: "label",
};
const subjectData = ref<Array<IAccountSubjectModel>>();
const asubList = ref(new Array<{ asubType: number; asubList: Array<IAccountSubjectModel> }>());
const accountSubjectStore = useAccountSubjectStore();
const salaryAsubList = ref<any[]>([]);
watchEffect(() => {
    asubList.value = [];
    subjectData.value = [];
    const accountSubjectList: IAccountSubjectModel[] = window.isErp
    ? JSON.parse(JSON.stringify(accountSubjectStore.accountSubjectList))
    : JSON.parse(JSON.stringify(accountSubjectStore.accountSubjectListWithoutDisabled));
    asubList.value.push({
        asubType: 0,
        asubList: accountSubjectList.filter((asub) => asub.status === 0 && asub.isLeafNode).map((asub) => ({ 
            ...asub
        })),
    });
    let asubGroup: any = {};
    accountSubjectList.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = new Array<IAccountSubjectModel>();
            asubList.value.push({ asubType: asub.asubType, asubList: asubGroup[asub.asubType] });
        }
        if (asub.status === 0 && asub.isLeafNode) {
            asubGroup[asub.asubType].push({ ...asub });
        }
    });
    let asubType;
    switch (accountingStandard) {
        case AccountStandard.FolkComapnyStandard:
            asubType = [1, 9];
            break;
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.CompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            asubType = [1, 5, 6];
            break;
        case AccountStandard.UnionStandard:
            asubType = [1, 7, 10];
            break;
    }
    subjectData.value = asubType?.reduce((prev: IAccountSubjectModel[], item: number) => {
        const foundItem = asubList.value.find((i) => i.asubType === item);  
        const list = foundItem ? foundItem.asubList : [];
        list.forEach((item) => {
            salaryAsubList.value.push({ label: item.asubCode + " " + item.asubAAName, value: item.asubId.toString() });
        });
        prev.push(...list);
        return prev;
    }, []);
});
const AuxiliaryDialogRef = ref();
const auxiliaryDialogShow = ref(false);
const auxiliaryAsubName = ref("");
const clickAsubId = ref();
const auxiliaryCode = ref<string[]>([]); //科目辅助核算类型编码集合
const auxiliaryAllowNull = ref<string[]>([]);  //科目辅助核算非必录项
const auxiliaryReadonly = ref(false);
const auxiliaryId = ref<string[]>([]); //科目辅助核算设置的值
const clickChange = (visible: boolean) => {
    if (!visible) {
        clickAsubOption(Number(editData.value[17]));
        showSalaryAsubList.value = JSON.parse(JSON.stringify(salaryAsubList.value));
    }
}
const clickAsubOption = (asubid: number|string) => {
    clickAsubId.value = Number(asubid);
    if (isHasAccount(Number(asubid))) {
        let asubAAName = subjectData.value?.filter((v)=>Number(v.asubId) === Number(asubid))[0].asubAAName || "";
        auxiliaryAsubName.value = asubAAName;
        auxiliaryId.value = editData.value[25] ? editData.value[25].split(",") : [];
        nextTick(() => {
            AuxiliaryDialogRef.value?.getInitAcconting();
        })
    }
}
//判断科目是否开启辅助核算
function isHasAccount(asubid: number) {
    auxiliaryCode.value = [];
    auxiliaryAllowNull.value = [];
    let item = subjectData.value?.filter((v)=>Number(v.asubId) === asubid);
    if (item && item[0] && item[0].aatypes) {
        auxiliaryCode.value = item[0].aatypes.includes(",") ? item[0].aatypes.split(",") : [item[0].aatypes];
        let list = item[0].aatypesAllowNull.includes(",") ? item[0].aatypesAllowNull.split(",") : [item[0].aatypesAllowNull];
        auxiliaryAllowNull.value = auxiliaryCode.value.map((el) => {
            let index = list.indexOf(el);
            return index !==-1 ? el : "0";
        });
        return true;
    } else {
        return false;
    }
}
//判断所有辅助核算明细是否都已选择有值
const assistingAccountingListAll = computed(() => {
    return useAssistingAccountingStore().assistingAccountingListAll;
});
const salaryCodeList = ref();
function isHasDetail() {
    let detailList = editData.value[25].split(",");
    if (detailList.length !== auxiliaryCode.value.length) {  
        return false;  
    } 
    salaryCodeList.value = [];
    for(let i=0; i < auxiliaryCode.value.length; i++) {
        let list = assistingAccountingListAll.value.filter(v => v.aatype === Number(auxiliaryCode.value[i]));
        for (let j=0; j<list.length; j++) {
            salaryCodeList.value.push(list[j].aaeid);
        }
    }
    const hasAllDetails = detailList.every(el =>salaryCodeList.value.includes(Number(el)));
    return  hasAllDetails && detailList.every(detail => detail.trim()); 
}
const setAsubAAE = (data: any) => {
    editData.value[17] = clickAsubId.value.toString(); //科目
    editData.value[25] = data.map((item: any) => item.id).join(",");//辅助核算
}

function IsOnlyWordNumber(str: string) {
    if (str.length != 0) {
        let reg = /^[a-zA-Z0-9_]+$/;
        if (!reg.test(str)) {
            return false;
        }
        return true;
    }
    return false;
}

function IsDate(str: string) {
    if (str.length != 0) {
        var reg = /^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
        var r = str.match(reg);
        if (r != null) return true;
    }
    return false;
}

function isNotFutureDate(str: string) {
    let today = new Date();
    let inputDate = new Date(str);
    return inputDate > today ? false : true;
}

function disabledBirthday(time: Date) {
    return time.getTime() > dayjs().valueOf();
}

const baseRef = ref<any>([]);
const comPercentRef = ref<any>([]);
const comAmountRef = ref<any>([]);
const perPercentRef = ref<any>([]);
const perAmountRef = ref<any>([]);

//五险一金缴纳比例数字格式化
const formatPercent = (value: string | undefined | number) => {
    if (typeof value === "number" && !isNaN(value)) {
        return value;
    } else if (typeof value === "string") {
        let reg = /^[0-9]+(\.[0-9]{1,3})?$/;
        if (reg.test(value)) {
            let baseValue = Number(value);
            return baseValue;
        } else {
            ElNotify({
                type: "warning",
                message: "缴纳比例必须为数字，最多3位小数",
            });
            return "";
        }
    }
    return "";
};
const formatDeduction = (value: string | undefined | number) => {
    if (typeof value === "number" && !isNaN(value)) {
        return value.toFixed(2);
    } else if (typeof value === "string") {
        let reg = /^[0-9]+(\.[0-9]{1,2})?$/;
        if (reg.test(value) || value === "") {
            let baseValue = Math.round(Number(value) * 100) / 100;
            return baseValue.toFixed(2);
        } else {
            ElNotify({
                type: "warning",
                message: "缴纳基数必须为数字，最多2位小数",
            });
            return "";
        }
    }
    return "";
};
const formatDeAmount = (value: string | undefined | number) => {
    if (typeof value === "number" && !isNaN(value)) {
        return value.toFixed(2);
    } else if (typeof value === "string") {
        let reg = /^[0-9]+(\.[0-9]{1,2})?$/;
        if (reg.test(value) || value === "") {
            let baseValue = Math.round(Number(value) * 100) / 100;
            return baseValue.toFixed(2);
        } else {
            ElNotify({
                type: "warning",
                message: "缴纳金额必须为数字，最多2位小数",
            });
            return "";
        }
    }
    return "";
};

//处理缴纳基数
function formatBaseNum(e: any, row: any, index?: number) {
    e.target.value = formatDeduction(e.target.value);
    if (e.target.value === "") {
        row.com_amount = "";
        row.per_amount = "";
    } else {
        row.com_amount = row.com_percent ? (Number(((e.target.value * row.com_percent * 100) / 100).toFixed(0)) / 100).toFixed(2) : "";
        row.per_amount = row.per_percent ? (Number(((e.target.value * row.per_percent * 100) / 100).toFixed(0)) / 100).toFixed(2) : "";
    }
    row.base = e.target.value;
    if (index === undefined) return;
    enterNext("base", index);
}
//处理缴纳比例
function formatCPercent(e: any, row: any, type: string, index?: number) {
    //非NaN可计算
    e.target.value = formatPercent(e.target.value);
    if (e.target.value === "") {
        row[`${type}_amount`] = "";
        row[`${type}_percent`] = "";
    } else {
        row[`${type}_amount`] = (Number(((row.base * e.target.value * 100) / 100).toFixed(0)) / 100).toFixed(2);
    }
    if (index === undefined) return;
    enterNext(`${type}_percent`, index);
}
//处理缴纳金额
function handleCPAmount(e: any, row: any, type: string, index?: number) {
    e.target.value = formatDeAmount(e.target.value);
    row[`${type}_amount`] = e.target.value;
    if (index === undefined) return;
    enterNext(`${type}_amount`, index);
}

function enterNext(field: string, index: number) {
    if (field === "base") {
        if (comPercentRef.value[index]) {
            comPercentRef.value[index].focus();
        } else {
            enterNext("com_percent", index);
        }
    } else if (field === "com_percent") {
        if (comAmountRef.value[index]) {
            comAmountRef.value[index].focus();
        } else {
            enterNext("com_amount", index);
        }
    } else if (field === "com_amount") {
        if (perPercentRef.value[index]) {
            perPercentRef.value[index].focus();
        } else {
            enterNext("per_percent", index);
        }
    } else if (field === "per_percent") {
        if (perAmountRef.value[index]) {
            perAmountRef.value[index].focus();
        } else {
            enterNext("per_amount", index);
        }
    } else if (field === "per_amount") {
        if (index === insuranceData.value.length - 1) {
            if (baseRef.value[0]) {
                baseRef.value[0].focus();
            }
        } else {
            if (baseRef.value[index + 1]) {
                baseRef.value[index + 1].focus();
            }
        }
    }
}

function saveAs() {
    const departmentData = {
        aaNum: departmentAddForm.departmentCode,
        aaName: departmentAddForm.departmentName.trim(),
        entity: {
            manager: departmentAddForm.departmentManager,
            mobilePhone: departmentAddForm.departmentManagerPhone,
            startDate: departmentAddForm.departmentEstablishDate,
            endDate: departmentAddForm.departmentCancelDate,
            note: departmentAddForm.departmentRemark,
        },
        uscc: " ",
        status: 0,
        hasEntity: true,
        ifvoucher: false,
    };
    request({
        url: `/api/AssistingAccounting/Department`,
        method: "post",
        data: departmentData,
    }).then((res: any) => {
        if (res.state === 1000 && res.data) {
            getDepartment(() => { editData.value[22] = res.data; })
            departmentAdd.value = false;
            departemntRef.value.resetFields();
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
}

function SaveDepartment() {
    if (!departmentAddForm.departmentCode) {
        ElNotify({
            type: "warning",
            message: "请录入部门编号！",
        });
        return;
    }
    if (!departmentAddForm.departmentName) {
        ElNotify({
            type: "warning",
            message: "请录入部门名称！",
        });
        return;
    }
    const data = {
        aaNum: departmentAddForm.departmentCode,
        aaName: departmentAddForm.departmentName,
    };
    request({
        url: `/api/AssistingAccounting/Check?aaType=10004`,
        data,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000 && res.data === "N") {
            saveAs();
        } else if (res.data == "Name") {
            ElConfirm("亲，此名称已存在，是否继续保存？").then((r: boolean) => {
                if (r) saveAs();
            });
        } else {
            ElNotify({
                type: "warning",
                message: "部门编号重复，请重新编号！",
            });
        }
    });
}

function cancelDepartment() {
    departmentAdd.value = false;
    departemntRef.value.resetFields();
}

function clickAddDepartment() {
    let name = autoAddName.value;
    if (window.isErp) {
        addAssistingAccountingEntryDialogRef.value?.showAADialog(10004);
    } else {
        request({
            url: `/api/AssistingAccounting/GetNewAANum?aaType=10004&categoryId=0`,
            method: "post",
        }).then((res: any) => {
            if (res.state === 1000) {
                departmentAddForm.departmentCode = res.data;
                departmentAdd.value = true;
                departmentAddForm.departmentName = name.slice(0, LimitCharacterSize.Name);
                if (name.length > LimitCharacterSize.Name) {
                    ElNotify({
                        type: "warning",
                        message: "部门名称不能超过" + LimitCharacterSize.Name + "个字符！",
                    });
                }
            }
        });
    }
}

function erpAAESaveSuccess(data: any) {
    editData.value[22] = data.departmentName;
    getDepartment();
}
const formatDate = (date: string) => {
    return date
        .split(" ")[0]
        .split("/")
        .map((item, index) => {
            if (index !== 0) item = item.padStart(2, "0");
            return item;
        })
        .join("-");
};
function getEditData(): any {
    request({
        url: `/api/Employee?sn=${props.E_SN}`,
    })
        .then((res: any) => {
            editData.value = res.data;
            editData.value[17] = editData.value[17] ? editData.value[17] : "";
            editData.value[7] = formatDate(editData.value[7]);
            editData.value[9] = formatDate(editData.value[9]);
            editData.value[12] = formatDate(editData.value[12]);
            editData.value[19] = editData.value[18] == "0" ? "" : formatDate(editData.value[19]);
            editData.value[20] = editData.value[18] == "0" ? "" : formatDate(editData.value[20]);
            editData.value[22] = departmentList.value.find((item: ISelectItem) => item.label === res.data[22])!.value; //部门名字转为部门id
            saveDepartId.value = editData.value[22];
            editData.value[25] = editData.value[25] ? editData.value[25] : "";
            if (isHasAccount(Number(editData.value[17])) && !isHasDetail()) { //计提工资科目启用了辅助核算 且辅助核算编码为空
                // 弹窗提示打开
                let msgSubjectInfo = subjectData.value?.filter(item => Number(item.asubId) === Number(editData.value[17]))[0].asubCode + " " + editData.value[23];
                ElAlert({
                    message: `计提工资科目：${msgSubjectInfo}启用了辅助核算，请重新选择计提工资科目，完善辅助核算明细后，再点击保存哦~`,
                    hideCancel: true
                }).then((r)=>{
                    isAAEWarning.value = true;
                })
            }
            nextTick(() => {
                isFormInit = true;
            });
        })
        .catch((error) => {
            console.log(error);
        });
}
const isAAEWarning = ref(false);
const saveDepartId = ref("1");
const departChangeShow = ref(false);

const initComPercent = ref()
function getinauranceData() {
    loading.value = true;
    request({
        url: `/api/EmployeeInsurance?sn=${props.E_SN}`,
    })
        .then((res: any) => {
            insuranceData.value = res.data;
            initComPercent.value = insuranceData.value.map((item: any) => item.com_percent);
            insuranceData.value.forEach((item: any) => {
                //pay-mode 1为按金额
                item.base = item.base && item.pay_mode !== 1 ? toDecimal2(item.base) : "";
                item.com_amount = item.com_amount ? toDecimal2(item.com_amount) : "";
                item.per_amount = item.per_amount ? toDecimal2(item.per_amount) : "";
            });
            nextTick(() => {
                insuranceInit = true;
            });
        })
        .finally(() => {
            loading.value = false;
        });
}

function getSocailSecurityData() {
    loading.value = true;
    request({
        url: `/api/EmployeeInsurance/Default`,
    })
        .then((res: any) => {
            insuranceData.value = res.data;
            initComPercent.value = insuranceData.value.map((item: any) => item.com_percent);
            insuranceData.value.forEach((item: any) => {
                item.base = item.base ? toDecimal2(item.base) : "";
                item.com_amount = item.com_amount ? toDecimal2(item.com_amount) : "";
                item.per_amount = item.per_amount ? toDecimal2(item.per_amount) : "";
            });
            nextTick(() => {
                insuranceInit = true;
            });
        })
        .catch((error) => {
            console.log(error);
        })
        .finally(() => {
            loading.value = false;
        });
}
//若五险一金项目按金额，保存的基数/公司比例/个人比例传入为0
function insuranceDataHandle() {
    insuranceData.value.forEach((item)=>{
        if(item.pay_mode === 1) {
            item.com_percent = "0";
            item.per_percent = "0";
        }
    })
}
//部门修改提示
const acceptStatus = ref(false);
function updateSubjectDepart() {
    clickAsubOption(Number(editData.value[17]));
    acceptSaveFlag.value = true;
    departChangeShow.value = false;
    acceptStatus.value = true;
}
const acceptSaveFlag = ref(false);
function cancelSubjectDepart() {
    departChangeShow.value = false;
    acceptSaveFlag.value = true;
    if (!acceptStatus.value) {
        submitEmployee();
    }
}

let submitFlag = true;
async function submitEmployee() {
    if (!submitFlag) return;
    try {
        const isValid = await ValidateEmplotInfo(editData.value);
        if (!isValid) return;
    } catch (error: any) {
        if (error.message === "Validation failed") {
            return;
        } else {
            // 处理其他错误
        }
    }
    if (getBirthById() === false) return;
    if (isHasAccount(Number(editData.value[17])) && !isHasDetail()) {
        let warnSubjectItem = subjectData.value?.filter(item => Number(item.asubId) === Number(editData.value[17]))[0];
        ElNotify({
            type: "warning",
            message: `${warnSubjectItem?.asubCode} ${warnSubjectItem?.asubAAName}启用了辅助核算，请重新选择计提工资科目，完善辅助核算明细后，再点击保存哦~`,
         });
        return;
    }
    if (
        isHasAccount(Number(editData.value[17])) 
        && auxiliaryCode.value.includes("10004")
        && saveDepartId.value !== editData.value[22] && 
        props.saveType === 'Edit'
        && !acceptSaveFlag.value
    ) { //编辑员工修改了部门且计提科目开启了部门辅助核算
        departChangeShow.value = true;
        return;
    }
    insuranceDataHandle();
    const data = {
        as_id: editData.value[0],
        e_sn: null,
        e_code: editData.value[1],
        e_name: editData.value[2],
        gender: editData.value[3],
        e_type: editData.value[21],
        e_id: editData.value[4],
        mobile_phone: editData.value[5],
        email: editData.value[6],
        birthday: editData.value[7],
        education: editData.value[8],
        start_date: getFormatterDate(editData.value[9]),
        bank: editData.value[10],
        bank_account: editData.value[11],
        salary_start_date: getFormatterDate(editData.value[12]),
        department: editData.value[22],
        position: editData.value[14],
        title: editData.value[15],
        note: editData.value[16],
        salary_asub: editData.value[17],
        status: editData.value[18],
        end_date: editData.value[18] === "1" ? getFormatterDate(editData.value[19]) : "",
        salary_end_date: editData.value[18] === "1" ? getFormatterDate(editData.value[20]) : "",
        ss: editData.value[24],
        tax_base: 0,
        aa_code: isHasAccount(Number(editData.value[17])) ? editData.value[25] : "", //计提工资科目的辅助核算
        // department_name: "string",
        // asub_name: "string",
        insuranceSettings: editData.value[24] === "1" ? insuranceData.value : null,
    };
    submitFlag = false;
    let employee = {
        submitType: props.saveType,
        code: data.e_code,
        idcard: data.e_id,
        sn: props.saveType === "Edit" ? props.E_SN : "",
    };
    request({
        url: `/api/Employee/CheckCode`,
        method: "post",
        data: employee,
    }).then((res: any) => {
        submitFlag = true;
        if (res.data === "") {
            if (props.saveType == "Add") {
                emits("saveSubject", editData.value[17]);
                request({
                    url: `/api/Employee`,
                    method: "post",
                    data,
                }).then((res: any) => {
                    if (res.state == 1000 && res.data) {
                        ElNotify({
                            type: "success",
                            message: res.data,
                        });
                        acceptStatus.value = false;
                        acceptSaveFlag.value = false;
                        emits("successModify");
                        handleExpiredCheckData(ExpiredCheckModuleEnum.Salary);
                        window.dispatchEvent(new CustomEvent('reloadAccountSubjectTree'));
                    } else if (res.state == 2000) {
                        ElNotify({
                            type: "warning",
                            message: res.msg,
                        });
                    }
                });
            } else {
                request({
                    url: `/api/Employee?sn=${props.E_SN}`,
                    method: "put",
                    data,
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                }).then((res: any) => {
                    if (res.state == 1000 && res.data) {
                        ElNotify({
                            type: "success",
                            message: "员工信息修改成功",
                        });
                        acceptStatus.value = false;
                        acceptSaveFlag.value = false;
                        emits("successModify");
                        window.dispatchEvent(new CustomEvent('reloadAccountSubjectTree'));
                    } else if (res.state == 2000) {
                        ElNotify({
                            type: "warning",
                            message: res.msg,
                        });
                    }else{
                         ElNotify({
                            type: "warning",
                            message: res.msg,
                        });
                    }
                });
            }
        } else {
            ElNotify({
                type: "warning",
                message: res.data,
            });
        }
    });
}

function getDepartment(callback?:Function) {
    useAssistingAccountingStore()
        .getDepartment()
        .then(() => {
            const list = useAssistingAccountingStore().departmentList;
            departmentList.value = list.reduce((prev: ISelectItem[], item: any) => {
                if (item.aaname !== "未录入辅助核算") {
                    prev.push({
                        value: item.aaeid + "",
                        label: item.aaname,
                    });
                }
                return prev;
            }, []);
            callback && callback();
        });
}

function cancelEdit() {
    emits("saveSubject", editData.value[17]);
    emits("cancelEdit");
}

let genderFlag = false;
let typeFlag = false;
let educationFlag = false;
function bindEnterAndTab() {
    codeRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            nameRef.value.focus();
        }
    });
    nameRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            genderRef.value.focus();
        }
    });
    genderRef.value.$el.addEventListener("keyup", (e: any) => {
        e.preventDefault();
        if (e.keyCode === 13) {
            genderFlag && typeRef.value.focus();
            genderFlag = !genderFlag;
        }
    });
    typeRef.value.$el.addEventListener("keyup", (e: any) => {
        if (e.keyCode === 13) {
            typeFlag && idRef.value.focus();
            typeFlag = !typeFlag;
        }
    });
    idRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            birthdayRef.value.focus();
        }
    });
    mobileRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            emailRef.value.focus();
        }
    });
    emailRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            educationRef.value.focus();
        }
    });
    educationRef.value.$el.addEventListener("keyup", (e: any) => {
        if (e.keyCode === 13) {
            educationFlag && bankRef.value.focus();
            educationFlag = !educationFlag;
        }
    });
    bankRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            bankAccountRef.value.focus();
        }
    });
    bankAccountRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            startDateRef.value.focus();
        }
    });
    positionRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            salaryStartDateRef.value.focus();
        }
    });
    titleRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            subjectRef.value.focusInput();
        }
    });
    noteRef.value.$el.addEventListener("keydown", (e: any) => {
        if (e.keyCode === 13) {
            statusRef.value.focus();
        }
    });
}


watch(
    () => editData.value[24],
    (newVal) => {
        if (newVal === "1") {
            props.title === "编辑员工" ? getinauranceData() : getSocailSecurityData();
        }
    }
);
const handleModifyaccountSubject = () => {
    if (useRouterArrayStoreHook().routerArray.find((item) => item.title.includes("员工信息"))) {
        request({
            url: `/api/Employee/GetDefaultSalaryAsub`,
            method: "post",
        }).then((res: any) => {
            if (res.state == 1000) {
                editData.value[17] = res.data + "";
            }
        });
    }
};
function listenerInsurance() {
    if (props.title == "编辑员工") {
        getinauranceData();
    } else {
        getSocailSecurityData(); 
    }
}
onMounted(() => {
    getDepartment();
    if (props.title == "编辑员工") {
        getEditData();
        getinauranceData();
    } else {
        editData.value = ["", "", "", "1", "", "", "", "", "8", "", "", "", "", "", "", "", "", "", "0", "", "", "1", "1", "", "1"];
        let departItem = assistingAccountingListAll.value.filter((item)=> item.aatype === 10004).filter((v) => v.aaeid > 0)[0];
        if (departItem) {
            editData.value[22] = departItem.aaeid + "";
        }
        if (isHasAccount(Number(props.subject))) {
            editData.value[17] = "";
        } else {
            editData.value[17] = props.subject;
        }
        getSocailSecurityData(); 
        nextTick(() => {
            isFormInit = true;
        });
    }
    window.addEventListener("modifyaccountSubject", handleModifyaccountSubject);
    window.addEventListener("updateInsuranceSet", listenerInsurance);
    bindEnterAndTab();
});
watch(
    () => props.subject,
    () => {
        editData.value[17] = props.subject;
    }
);
//新增和编辑 浏览器编辑校验
let isFormInit = false
let insuranceInit = false;
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
const isEditting = ref(false);
watch(
    [editData, insuranceData],
    () => {
        if (!isFormInit || !insuranceInit) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
onUnmounted(() => {
    window.removeEventListener("modifyaccountSubject", handleModifyaccountSubject);
    window.removeEventListener("updateInsuranceSet", listenerInsurance);
    routerArrayStore.changeRouterEditting(currentPath.value, false);
});
function disabledDateStart(time: Date) {
    if (!departmentAddForm.departmentCancelDate) return false;
    const endDate = dayjs(departmentAddForm.departmentCancelDate).valueOf();
    return time.getTime() > endDate;
}

function disabledDateEnd(time: Date) {
    if (!departmentAddForm.departmentEstablishDate) return false;
    const startDate = dayjs(departmentAddForm.departmentEstablishDate).valueOf();
    return time.getTime() < startDate;
}

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//模糊搜索
const showdepartmentList = ref<Array<any>>([]);
const showIdCardList = ref<Array<ISelectItem>>([]);
const showEducationList = ref<Array<ISelectItem>>([]);
const showSalaryAsubList = ref<Array<any>>([]);
watchEffect(() => {
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
    showIdCardList.value = JSON.parse(JSON.stringify(idCardList));
    showEducationList.value = JSON.parse(JSON.stringify(educationList));
    showSalaryAsubList.value = JSON.parse(JSON.stringify(salaryAsubList.value));
});
function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value , 'label');
    autoAddName.value = showdepartmentList.value.length === 0 ? value.trim() : ""; 
}
function idCardFilterMethod(value: string) {
    showIdCardList.value = commonFilterMethod(value, idCardList , 'label');
}
function educationFilterMethod(value: string) {
    showEducationList.value = commonFilterMethod(value, educationList , 'label');
}
function salaryAsubFilterMethod(value: string) {
    showSalaryAsubList.value = commonFilterMethod(value, salaryAsubList.value , 'label');
}
</script>

<style scoped lang="less">
@import "@/style/Salary/EditSlot.less";
@import "@/style/Functions.less";

.box-main {
    padding: 20px 50px;
    border-bottom: 1px solid var(--border-color);
}
.select-content-c {
    &.warning {
        :deep(.el-select) {
            .el-input .el-input__wrapper {
                box-shadow: 0 0 0 1px var(--red);
            }
        }
        :deep(.el-select-v2) {
            .el-select-v2__wrapper {
                border-color: var(--red);
            }
        }
    }
    :deep(.el-select-v2__placeholder) {
        text-align: left;
    }
}
.departmentAddContent {
    input {
        .detail-original-input(160px, 28px);
    }
}
:deep(.el-date-editor) {
    .el-input__prefix {
        position: absolute;
        right: 0;
    }
    .el-input__suffix-inner {
        position: absolute;
        right: 30px;
        top: 9px;
    }
}
:deep(.ss-table-container .custom-table tbody tr td .cell) {
    font-size: 13px !important;
    .el-input__inner {
        font-size: 14px !important;
        flex-grow: 0;
        padding: 0 1px;
    }
}
:deep(.el-select-dropdown__list) {
    max-height: 200px;
    // overflow-y: auto;
}
:deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
}
.departmentAddContent {
    padding: 20px 0 10px 26px;
    .isRow {
        height: 42px;
        &.requiredProp {
            :deep(.el-form-item__label::before) {
                content: "*";
                color: var(--red);
            }
        }
        & :deep(.el-form-item) {
            & .el-form-item__label {
                width: 102px;
                text-align: right;
            }
        }
    }
}
:deep(.el-input__inner) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.item-percent {
    display: flex;
    align-items: center;
}
</style>
