<template>
    <div class="look-voucher-box" :class="isErp ? 'erp-look-voucher-box' : ''">
        <div class="check-voucher-head">
            <div class="check-voucher-title" style="width: 226px">摘要</div>
            <div class="check-voucher-title" style="width: 446px">科目</div>
            <div class="check-voucher-title" style="width: 104px; text-align: right">借方金额</div>
            <div class="check-voucher-title" style="width: 104px; text-align: right">贷方金额</div>
        </div>
        <div v-show="allData.length === 0" style="margin-top: 10px; min-height: 321px"></div>
        <div class="check-voucher-list" v-for="(listTotalItem, listTotalIndex) in allData" :key="listTotalIndex">
            <div
                class="voucher-list-item"
                v-for="listItem in listTotalItem.vouchers"
                :key="listItem.vid"
                @dblclick="editVoucher(listItem.pid, listItem.vid)"
            >
                <div class="voucher-list-head">
                    <span class="voucher-date float-l">{{ "日期：" + listItem.vdate.split("T")[0] }}</span>
                    <span class="voucher-num float-l ml-20">{{ "凭证字号：" + getVgName(listItem) + "-" + listItem.vnum }}</span>
                    <a class="link hover-display float-r ml-20" @click="() => deleteVoucher(listItem.pid, listItem.vid, listTotalIndex)">删除</a>
                    <a class="link hover-display float-r ml-20" @click="() => editVoucher(listItem.pid, listItem.vid)">修改</a>
                </div>
                <div class="voucher-list-lines">
                    <div class="voucher-list-line" v-for="item in listItem.voucherLines" :key="item.veid">
                        <div style="width: 226px">{{ resetDescription(item.description) }}</div>
                        <div style="width: 444px">{{ item.asubCode + " " + item.asubname }}</div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(item.debit) }}</div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(item.credit) }}</div>
                    </div>
                    <div class="voucher-list-line">
                        <div style="width: 226px">合计</div>
                        <div style="width: 444px">
                            {{ digitUppercase(getTotalMoney(listItem.voucherLines, "debit")) }}
                        </div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(getTotalMoney(listItem.voucherLines, "debit")) }}</div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(getTotalMoney(listItem.voucherLines, "credit")) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="check-box-tool-bar">
            <a class="button mr-10" @click="handleCancel">取消</a>
            <a class="solid-button" @click="newVouchers" v-if="showNewVouchers">新增凭证</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { formatMoney } from "@/util/format";
import { getTotalMoney, resetDescription } from "../utils";
import { digitUppercase } from "@/util/format";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { VoucherType, getVoucherIndex } from "../utils";
const propss = defineProps<{
    resetTotalMoney: Function;
}>();

const voucherGroupStore = useVoucherGroupStore();

const voucherGroupList = computed(() => voucherGroupStore.voucherGroupList);
function getVgName(voucherInfo: any) {
    const { vgid, vgname } = voucherInfo;
    if (vgname) return vgname;
    const vg = voucherGroupList.value.find((item) => item.id === vgid);
    return vg ? vg.name : "";
}

const allData = ref<any[]>([]);
const isErp = ref<boolean>(window.isErp);

const emit = defineEmits(["handleCancel", "newVouchers", "editVoucher", "deleteVoucher"]);
const handleCancel = () => emit("handleCancel");
const newVouchers = () => {
    const length = allData.value.length;
    const defaultVgId = voucherGroupStore.defaultVgId;
    if (length === 0) {
        emit("newVouchers", defaultVgId);
        return;
    }
    const vgId = allData.value[length - 1].vouchers[allData.value[length - 1].vouchers.length - 1].vgid ?? defaultVgId;
    emit("newVouchers", vgId);
};

const editVoucher = (PID:number, VID: number) => emit("editVoucher", PID, VID);
const deleteVoucher = (PID:number, VID: number, listTotalIndex: number, callback:() => void = () => {}) => {
    const successHandle = () => {
        const listTotalItem = allData.value[listTotalIndex];
        propss.resetTotalMoney();
        if (listTotalItem.vouchers.length === 1) {
            allData.value.splice(listTotalIndex, 1);
        } else {
            const voucherIndex = listTotalItem.vouchers.findIndex((item: any) => item.vid === VID);
            listTotalItem.vouchers.splice(voucherIndex, 1);
        }
        callback()
    };
    emit("deleteVoucher", PID, VID, successHandle);
};
const getLookVoucherIndex = (vid: number, pid: number) => {
    return getVoucherIndex(allData.value, vid, pid);
};
const showNewVouchers = ref(true);
const setData = (loadingData: any[]) => {
    allData.value = loadingData;
    if (allData.value.length) {
        const vtype = allData.value[0].cardInfo.vtype;
        showNewVouchers.value = vtype !== VoucherType.accruedSalaryModule && vtype !== VoucherType.paySalaryModule;
    } else {
        showNewVouchers.value = true;
    }
};

defineExpose({ setData, deleteVoucher, getLookVoucherIndex });
</script>

<style lang="less" scoped>
.look-voucher-box {
    box-sizing: border-box;
    width: 1000px;
    margin: 0 auto;
    padding-top: 20px;
    .check-box-tool-bar {
        margin-top: 20px;
        text-align: center;
        margin-bottom: 40px;
    }
    .check-voucher-head {
        width: 980px;
        margin: 0 auto;
        background-color: var(--table-title-color);
        height: 37px;
        .check-voucher-title {
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 37px;
            font-weight: bold;
            float: left;
            padding-left: 20px;
            text-align: left;
        }
    }
    .check-voucher-list {
        margin-top: 10px;
        min-height: 321px;
        .voucher-list-item {
            box-sizing: border-box;
            width: 980px;
            margin: 10px auto 0;
            border: 1px solid #bcbcbc;
            &:hover {
                border-color: var(--main-color);
                & > .voucher-list-head {
                    & > a.hover-display {
                        display: inline-block;
                    }
                }
            }
            .voucher-list-head {
                color: #404040;
                font-size: 12px;
                height: 39px;
                position: relative;
                padding: 0 20px;
                & > span {
                    display: inline-block;
                    color: #7f7f7f;
                    font-size: var(--h5);
                    line-height: 17px;
                    margin-top: 11px;
                }
                .hover-display {
                    margin-top: 11px;
                    line-height: 17px;
                    display: none;
                    font-size: var(--h5);
                }
            }
            .voucher-list-lines {
                color: #404040;
                font-size: 12px;
                text-align: left;
                .voucher-list-line {
                    display: flex;
                    align-items: center;
                    color: var(--font-color);
                    font-size: var(--h5);
                    line-height: 17px;
                    padding: 8px 20px 7px 0px;
                    cursor: pointer;
                    border-top: 1px solid #f0f0f0;
                    &:first-child {
                        border-top-color: #bcbcbc;
                    }
                    & > div {
                        padding-left: 20px;
                        display: inline-block;
                        word-break: break-all;
                        vertical-align: top;
                    }
                    &:hover {
                        background-color: var(--table-hover-color);
                    }
                }
            }
        }
    }
}
</style>
