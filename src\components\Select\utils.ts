import { ref, computed, onMounted, nextTick } from "vue"
import type { SelectProps, SelectEmits } from "./types"
import { pinyin } from "pinyin-pro"

export const useSelect = (props: SelectProps, emit: SelectEmits) => {
  const selectRef = ref()
  const visible = ref(false)
  const content = ref("")
  const iconFlag = ref(false)

  const value = computed({
    get: () => props.modelValue,
    set: (val) => emit("update:modelValue", val),
  })

  const handleVisibleChange = (isVisible: boolean) => {
    emit("visible-change", isVisible)
  }

  const handleInput = (val: string) => {
    emit("input", val)
  }
  const handleClick = (event: Event) => {
    emit("click", event)
  }

  const handleChange = (value: any) => {
    emit("change", value)
  }

  const handleFocus = (event: FocusEvent) => {
    emit("focus", event)
  }

  const handleBlur = (event: FocusEvent) => {
    emit("blur", event)
  }

  const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
      emit("keyupEnter")
      return
    }
    emit("keyup", event)
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
      emit("keydownEnter")
    }
  }

  const handleOverflow = () => {
    const input = selectRef.value?.$refs?.reference?.input
    if (!input) return

    const label = selectRef.value?.selected?.currentLabel?.trim() || ""
    content.value = label

    visible.value = input.scrollWidth > input.clientWidth && label !== ""
  }

  const initListeners = () => {
    const input = selectRef.value?.$refs?.reference?.input
    if (!input) return

    input.addEventListener("keydown", handleKeyDown)
    input.addEventListener("mouseenter", handleOverflow)
    input.addEventListener("mouseleave", () => (visible.value = false))
    input.addEventListener("focus", () => {
      nextTick(() => (visible.value = false))
    })
  }

  onMounted(() => {
    initListeners()
  })

  return {
    selectRef,
    visible,
    content,
    value,
    iconFlag,
    handleVisibleChange,
    handleOverflow,
    handleInput,
    handleClick,
    handleChange,
    handleFocus,
    handleBlur,
    handleKeyUp,
    handleKeyDown,
  }
}

export function commonFilterMethod(value: string, option: any[], prop: string) {
  if (value.trim() === "") {
    return option
  }
  const lowerCaseValue = value.toLowerCase()
  const result = option.filter((item) => {
    const itemProp = prop ? item[prop] : item
    const pinyinFirst = pinyin(itemProp, {
      pattern: "first",
      toneType: "none",
      type: "array",
    })
      .join("")
      .toLowerCase()
    const pinyinFull = pinyin(itemProp, {
      toneType: "none",
      type: "array",
    })
      .join("")
      .toLowerCase()

    return (
      itemProp.includes(value) || // 直接匹配原字符串
      pinyinFirst.includes(lowerCaseValue) || // 匹配拼音首字母
      pinyinFull.includes(lowerCaseValue) // 匹配全拼
    )
  })
  return result
}
