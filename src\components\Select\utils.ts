import { pinyin } from "pinyin-pro";

export function commonFilterMethod(value: string, option:any[], prop: string) {
    if (value.trim() === "") {
        return option;
    } 
    const lowerCaseValue = value.toLowerCase();
    const result = option.filter((item) => {
        const itemProp = prop ? item[prop] : item; 
        const pinyinFirst = pinyin(itemProp, {   
            pattern: "first",   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();
        const pinyinFull = pinyin(itemProp, {   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();

        return (
            itemProp.includes(value) || // 直接匹配原字符串  
            pinyinFirst.includes(lowerCaseValue) || // 匹配拼音首字母  
            pinyinFull.includes(lowerCaseValue) // 匹配全拼
        );
    });
    return result;
}