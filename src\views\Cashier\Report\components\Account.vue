<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <DatePicker
                v-model:start-pid="searchInfo.startPid"
                v-model:end-pid="searchInfo.endPid"
                :clearable="true"
                :disabled-date-start="disabledDateStart"
                :disabled-date-end="disabledDateEnd"
            />
            <a class="solid-button ml-10" @click="getTableList">查询</a>
        </div>
        <div class="main-tool-right">
            <span v-if="hasDisableAccount(options)" class="mr-10">
                <el-checkbox v-model="showDisabled" label="显示禁用账户" @change="changeStatus" />
            </span>
            <a class="button mr-10" v-permission="['report-canprint']" @click="handlePrintOrExport('print')">打印</a>
            <a class="button mr-10" v-permission="['report-canexport']" @click="handlePrintOrExport('export')">导出</a>
            <a class="button mr-10" v-if="subscribeShow" @click="scanShare">扫码分享</a>
            <a class="button" v-if="subscribeShow" @click="subscribe">报表订阅推送</a>
            <RefreshButton></RefreshButton>
        </div>
    </div>
    <div class="report-content" style="overflow-y: auto;">
        <div class="main-center" v-loading="loading"  element-loading-text="正在加载数据...">
            <Table 
                :columns="columns" 
                :data="tableData1010" 
                empty-text="暂无数据" 
                :show-overflow-tooltip="true"
                @save-header-dragend="headerDragend"
                :tableName="setModule"
            >
                <template #name>
                    <el-table-column 
                        label="现金账户" 
                        min-width="154px" 
                        align="left" 
                        header-align="left"
                        prop="name"
                        :width="getColumnWidth(setModule, 'name')"
                    >
                        <template #default="scope">
                            <span v-if="!scope.row.ac_name.startsWith('<a')">{{ scope.row.ac_name }}</span>
                            <span class="link" @click="goToDetail(scope.row.ac_name)" v-else>{{ getText(scope.row.ac_name) }}{{ getForbiddenText(scope.row.ac_id) }}</span>
                        </template>
                    </el-table-column>
                </template>
            </Table>
            <div class="mb-10"></div>
            <Table 
                :columns="columns" 
                :data="tableData1020" 
                empty-text="暂无数据" 
                :show-overflow-tooltip="true" 
                @save-header-dragend="headerDragend"
                :tableName="setModule"
            >
                <template #name>
                    <el-table-column 
                        label="银行账户" 
                        min-width="154px" 
                        align="left" 
                        header-align="left"
                        prop="name"
                        :width="getColumnWidth(setModule, 'name')"
                    >
                        <template #default="scope">
                            <span v-if="!scope.row.ac_name.startsWith('<a')">{{ scope.row.ac_name }}</span>
                            <span class="link" @click="goToDetail(scope.row.ac_name)" v-else>{{ getText(scope.row.ac_name) }}{{ getForbiddenText(scope.row.ac_id) }}</span>
                        </template>
                    </el-table-column>
                </template>
            </Table>
            <div class="mb-10"></div>
            <Table
                :columns="totalColumns"
                :data="tableDataTotal"
                :show-header="false"
                row-class-name="current-row"
                empty-text="暂无数据"
                :show-overflow-tooltip="true"
                @save-header-dragend="headerDragend"
                :tableName="setModule"
            >
                <template #name>
                    <el-table-column 
                        label="" 
                        min-width="154px" 
                        align="left" 
                        header-align="left"
                        :width="getColumnWidth(setModule, 'name')"
                    >
                        <template #default="scope">
                            <span>{{ scope.row.ac_name }}</span>
                        </template>
                    </el-table-column>
                </template>
            </Table>    
        </div>
        <div class="main-bottom">
            <div style="color: var(--red)">注意：</div>
            <div class="mt-10">
                <span>总计金额统计为人民币，外币账户会根据日记账中的本币金额进行计算，可能会存在一定的误差</span>
            </div>
        </div>
    </div>
    <ScanShareDialog ref="ScanShareRef"></ScanShareDialog>
    <SubscribePushDialog ref="SubscribePushRef"></SubscribePushDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { request } from "@/util/service";
import { formatMoney } from "@/util/format";
import { getText, getATagParams, getSearchParams } from "../utils";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

import type { ITabledataAccountOrIetype, IDateInfo } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { dayjs } from "element-plus";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import ScanShareDialog from "./ScanShareDialog.vue";
import SubscribePushDialog from "./SubscribePushDialog.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { setShowDisabledAccount, getShowDisabledAccount, hasDisableAccount } from "@/views/Cashier/CDAccount/utils";

const setModule = "CashReportAccount";
const accountSetStore = useAccountSetStore();

const props = defineProps<{ 
    dateInfo: IDateInfo;
    options: IBankAccountItem[]; 
}>();

const subscribeShow = computed(() => {
    return !window.isErp && !window.isAccountingAgent && !window.isBossSystem && !useThirdPartInfoStoreHook().isThirdPart
});
const ScanShareRef = ref();
const SubscribePushRef = ref();
function scanShare() {
    ScanShareRef.value.initShareDialog({tab:'Account',startDate:searchInfo.startPid,endDate:searchInfo.endPid });
}
function subscribe() {
    SubscribePushRef.value.initSubscribe();
}

const dateInfo = computed(() => props.dateInfo);
const calcColumns = (initialColumn: string, incomeColumn: string, expenditureColumn: string, totalColumn: string): Array<IColumnProps> => {
    return [
        { 
            label: "编码", 
            prop: "ac_no", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 92,
            width: getColumnWidth(setModule, 'ac_no'),
        },
        { slot: "name" },
        {
            label: "期初余额",
            prop: initialColumn,
            align: "right",
            headerAlign: "right",
            minWidth: 132,
            formatter: (row, column, value: string) => {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, initialColumn),
        },
        {
            label: "收入总额",
            prop: incomeColumn,
            align: "right",
            headerAlign: "right",
            minWidth: 132,
            formatter: (row, column, value: string) => {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, incomeColumn),
        },
        {
            label: "支出总额",
            prop: expenditureColumn,
            align: "right",
            headerAlign: "right",
            minWidth: 132,
            formatter: (row, column, value: string) => {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, expenditureColumn),
        },
        {
            label: "余额",
            prop: totalColumn,
            align: "right",
            headerAlign: "right",
            minWidth: 132,
            formatter: (row, column, value: string) => {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, totalColumn),
        },
        { 
            label: "收入笔数", 
            prop: "income_count", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 102,
            width: getColumnWidth(setModule, "income_count"), 
        },
        { 
            label: "支出笔数", 
            prop: "expenditure_count", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 102,
            resizable: false
        },
    ];
};
const columns = ref<Array<IColumnProps>>();
columns.value = calcColumns("initial", "income_sum", "expenditure_sum", "total");
const totalColumns = ref<Array<IColumnProps>>();
totalColumns.value = calcColumns("initial_standard", "income_standard_sum", "expenditure_standard_sum", "total_standard");

const tableData1010 = ref<ITabledataAccountOrIetype[]>([]);
const tableData1020 = ref<ITabledataAccountOrIetype[]>([]);
const tableDataTotal = ref<ITabledataAccountOrIetype[]>([]);
const searchInfo = reactive({
    startPid: "",
    endPid: "",
});
watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});
const showDisabled = ref(getShowDisabledAccount("reportAccount"));
const changeStatus = () => {
    setShowDisabledAccount("reportAccount", showDisabled.value);
    getTableList();
}
function getForbiddenText(ac_id: string) {
    return props.options.find((item) => item.ac_id === ac_id)?.state === "1" ? "（已禁用）" : "";
}

const loading = ref(false);
const getTableList = () => {
    if (searchInfo.startPid === "" || searchInfo.endPid === "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    loading.value = true;
    Promise.all([getTableListApi("Search", 1010), getTableListApi("Search", 1020), getTableListApi("Total")])
        .then((res: any) => {
            if (res[0].state == 1000) tableData1010.value = res[0].data.rows;
            if (res[1].state == 1000) tableData1020.value = res[1].data.rows;
            if (res[2].state == 1000) tableDataTotal.value = res[2].data.rows;
        })
        .finally(() => {
            loading.value = false;
        });
};
const handlePrintOrExport = (type: "print" | "export") => {
    if (searchInfo.startPid == "" || searchInfo.endPid == "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    const params = getSearchParams(searchInfo, "reportAccount", showDisabled.value);
    const urlPath = type === "print" ? "PrintForCD?" : "ExportForCD?";
    const url = "/api/CashierReport/" + urlPath + getUrlSearchParams(params);
    type === "print" ? globalPrint(url) : globalExport(url);
};
const isErp = ref(window.isErp);
const goToDetail = (acname: string) => {
    const params = { 
        ...getATagParams(acname),
        showDisabled: showDisabled.value,
        ran: Math.random(),
    };
    const basePath = isErp.value ? "Journal" : "CDJournal";
    globalWindowOpenPage("/Cashier/" + basePath + "?" + getUrlSearchParams(params), "账户日记账");
};

watch(
    dateInfo,
    (val: IDateInfo) => {
        const { start, end } = val;
        searchInfo.startPid = start;
        searchInfo.endPid = end;
        getTableList();
    },
    { deep: true }
);

const getTableListApi = (type: "Search" | "Total", j_type?: number | string) => {
    const commonParams = getSearchParams(searchInfo, "reportAccount", showDisabled.value);
    const params = type === "Search" ? { ...commonParams, j_type } : commonParams;
    const urlPath = type === "Search" ? "ListForCD" : "ListForCDTotal";
    return request({ url: "/api/CashierReport/" + urlPath + "?" + getUrlSearchParams(params) });
};

function disabledDateStart(time: Date) {
    const endDate = dayjs(searchInfo.endPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    return time.getTime() > endDate || time.getTime() < asStartDate;
}
function disabledDateEnd(time: Date) {
    const startDate = dayjs(searchInfo.startPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    const minTime = Math.max(startDate, asStartDate);
    return time.getTime() < minTime;
}
//总计表(无表头)同步字表列宽
function headerDragend(width: number, prop: string) {
    if(prop === "initial") {
        saveColWidth(setModule, width, "initial_standard");
    }
    if (prop === "income_sum") {
        saveColWidth(setModule, width, "income_standard_sum");
    }
    if (prop === "expenditure_sum") {
        saveColWidth(setModule, width, "expenditure_standard_sum");
    }
    if (prop === "total") {
        saveColWidth(setModule, width, "total_standard");
    }
    columns.value = calcColumns("initial", "income_sum", "expenditure_sum", "total");
    totalColumns.value = calcColumns("initial_standard", "income_standard_sum", "expenditure_standard_sum", "total_standard");
}
</script>

<style scoped lang="less">
@import "@/style/Cashier/Report.less";
</style>
