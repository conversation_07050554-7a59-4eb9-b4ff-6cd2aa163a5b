import { IResponseModel, Jrequest, request, requestWithPeriodId } from "@/utils/service"
import { IDeductionItem, IInputInvoiceItem } from "@/views/InvoiceAccess/types"

interface ITaxBureauLoginParams {
  isAuthorized: boolean
  taskSource: string
}

export interface IInvoiceTaskValue {
  success: boolean
  result: string
  message: string
  info: string
  taskId: number
  reportId: number
}

export interface ICodeItem {
  item1: string
  item2: string
}

export interface IResSalesInvoice {
  list: IInputInvoiceItem[]
  codes: ICodeItem[]
}

export async function getTaxBureauLogin(params: ITaxBureauLoginParams, body: any) {
  return (await Jrequest({
    url: "api/TaxBureau/Login",
    params,
    data: body,
    method: "post",
  })) as any as Promise<IResponseModel<IInvoiceTaskValue>>
}

// 查询任务状态
export async function lastSummaryTask() {
  return (await Jrequest({
    url: "api/TaxBureau/LastSummaryTask",
    method: "get",
  })) as any as Promise<IResponseModel<any>>
}

// get /api/invoice/purchase-list
// 进项发票分页列表
export async function getPurchaseList() {
  return (await requestWithPeriodId({
    url: "api/invoice/purchase-list",
    method: "get",
  })) as any as Promise<IResponseModel<IInputInvoiceItem[]>>
}
// get /api/invoice/sales-list
// 销项发票分页列表

export async function getSalesList() {
  return (await requestWithPeriodId({
    url: "api/invoice/sales-list",
    method: "get",
  })) as any as Promise<IResponseModel<IResSalesInvoice>>
}
// get /api/invoice/summary-check
// 生成申报表是否可用
export async function getSummaryCheck() {
  return (await requestWithPeriodId({
    url: "api/invoice/summary-check",
    method: "get",
  })) as any as Promise<IResponseModel<boolean>>
}

// get /api/invoice/purchase-summary
// 进项统计表
export async function getPurchaseSummary() {
  return (await requestWithPeriodId({
    url: "api/invoice/purchase-summary",
    method: "get",
  })) as any as Promise<IResponseModel<Array<IDeductionItem>>>
}
// get /api/invoice/invoice-table-check
// 获取票表核对信息
export async function getInvoiceTableCheck() {
  return (await requestWithPeriodId({
    url: "api/invoice/invoice-table-check",
    method: "get",
  })) as any as Promise<IResponseModel<any>>
}
