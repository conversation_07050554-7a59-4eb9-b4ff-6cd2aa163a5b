.hidden {
    overflow: hidden;
}
.content {
    :deep(.import-dialog) {
        .download-content,
        .import-content {
            margin-left: 70px;
        }
    }
}
.main-content {
    height: 100vh;
    .main-top {
        padding: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        .main-tool-right {
            .button {
                padding-left: 30px;
                background: url("@/assets/Erp/switch_h.png") no-repeat 5px 4px;
                background-size: 20px 20px;
                &:hover {
                    background-image: url("@/assets/Erp/switch_h_blue.png");
                }
            }
        }
    }
    .main-center {
        padding: 20px;
        display: flex;
        flex-wrap: wrap;
        overflow-y: auto;
        overflow-x: hidden;
        flex: 1;
        min-height: auto;
        display: flex;
        .main-template {
            padding: 20px;
            margin: 0;
            box-sizing: border-box;
            width: 33%;
            height: 219px;
            position: relative;
            &:hover {
                z-index: 9;
            }

            .main-block {
                min-height: 152px;
                background: #ffffff;
                box-shadow: 0px 6px 18px 8px rgba(21, 27, 41, 0.05), 0px 6px 10px -4px rgba(21, 27, 41, 0.08);
                border-radius: 8px;
                margin: 0 auto;
                position: relative;
                width: 100%;
                min-width: 340px;
                padding-bottom: 1px;
                .defaultImg {
                    position: absolute;
                    height: 60px;
                    width: 60px;
                    right: 0;
                    bottom: 0;
                }
                .main-block-title {
                    padding: 20px 16px 16px 20px;
                    color: var(--font-color);
                    font-size: 13px;
                    font-weight: 700;
                    line-height: 18px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: flex-start;
                    .title_tooltip {
                        width: 200px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        text-align: left;
                    }
                    .t {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                    .main-block-button {
                        cursor: pointer;
                        position: relative;
                        padding: 0 5px;
                        &:hover {
                            & .main-block-edit-expend {
                                visibility: visible;
                                opacity: 1;
                            }
                        }
                        img {
                            height: 13px;
                            vertical-align: top;
                        }
                        .main-block-edit-expend {
                            position: absolute;
                            width: 66px;
                            text-align: center;
                            visibility: hidden;
                            opacity: 0;
                            transition: var(--transition-time);
                            z-index: 10;
                            left: -32px;
                            top: 24px;
                            .main-block-edit-content {
                                background: #ffffff;
                                box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1);
                                border: 1px solid #dfe4eb;
                                top: 30px;
                                right: 0px;
                                background-color: #ffffff;
                                border-radius: 3px;
                                color: var(--font-color);
                                font-size: var(--h5);
                                line-height: 17px;
                                font-weight: 400;
                                div {
                                    padding: 7px 0 8px 11px;
                                    text-align: left;
                                    &:hover {
                                        background: #f1f5fc;
                                    }
                                }
                            }
                        }
                    }
                }
                .main-block-label {
                    padding: 0 20px;
                    text-align: left;
                    font-weight: 500;
                    font-size: 17px;
                    color: #333333;
                    line-height: 28px;
                    margin-bottom: 4px;
                    font-weight: 700;
                    > :deep(span) {
                        max-width: 200px;
                        overflow: hidden;
                    }
                }
                .main-block-info {
                    padding: 0 24px;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 24px;
                    height: 72px;
                    margin-bottom: 20px;
                    overflow: hidden;
                    .main-block-info-line {
                        display: flex;
                        flex-direction: row;
                        justify-content: start;
                        text-align: left;
                        font-weight: 400;
                        font-size: 14px;
                        color: #333333;
                        line-height: 24px;
                        .main-block-info-line-left {
                            width: 140px;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            overflow: hidden;
                        }
                        .main-block-info-line-right {
                            margin-left: 30px;
                        }
                    }
                    .main-block-note {
                        text-align: left;
                        display: none;
                        word-break: break-all;
                    }
                }
                &:hover {
                    .main-block-info {
                        min-height: 73px;
                        height: auto;
                    }
                    .main-block-note {
                        display: block;
                    }
                }
            }
        }
        > div {
            height: 100%;
            overflow: hidden;
        }
        .main-center-left {
            flex: 1;
            :deep(.el-scrollbar) {
                width: 100%;
                .el-scrollbar__view {
                    display: flex;
                    flex-wrap: wrap;
                }
                .el-scrollbar__bar.is-vertical {
                    width: 8px;
                }
            }
            :deep(.table.custom-table) {
                height: calc(100% - 20px);
                .el-table__header .el-table__cell.show_overflow_tooltip .cell {
                    height: 100%;
                    .is-default-header {
                        height: 100%;
                    }
                }
            }
        }
        .main-center-right {
            height: calc(100% - 20px);
            margin-top: 20px;
            width: 224px;
            border: 1px solid var(--border-color);
            box-sizing: border-box;
            overflow: visible;
            display: flex;
            flex-direction: column;
            border-radius: 2px;
            position: relative;
            .function {
                position: absolute;
                top: 50%;
                left: -10px;
                width: 10px;
                height: 90px;
                background-image: url(@/assets/Icons/erp-close-tree.png);
                background-repeat: no-repeat;
                background-size: 100% 100%;
                cursor: pointer;
                display: block;
                transform: translateY(-50%);
                &.expend {
                    background-image: url(@/assets/Icons/erp-open-tree.png);
                }
            }
            :deep(.el-scrollbar) {
                flex: 1;
            }
            :deep(.el-tree) {
                padding-right: 14px;
                padding-bottom: 10px;
                width: auto;
                min-width: 100%;
                display: inline-block;
                box-sizing: border-box;
                .tree-icon {
                    display: inline-block;
                    vertical-align: top;
                    height: 21px;
                    width: 21px;
                    background-repeat: no-repeat;
                    background-position-y: center;
                    background-position-x: center;
                }
                .is-expanded {
                    & .tree-icon.tree-folder.tree-folder-open {
                        background-image: url(@/assets/Icons/folder-open.png);
                    }
                }
                .tree-icon.tree-folder.tree-folder-open {
                    background-image: url(@/assets/Icons/folder.png);
                }
                .tree-icon.tree-file {
                    background-image: url(@/assets/Icons/file-erp.png);
                    background-size: 16px 18px;
                }
                .tree-title {
                    color: var(--font-color);
                    font-size: var(--h5);
                    line-height: 21px;
                    display: inline-block;
                }
                .is-current {
                    & > .el-tree-node__content {
                        background-color: var(--table-title-color);
                    }
                }
                .el-tree-node.is-root {
                    & > .el-tree-node__content {
                        padding-left: 24px !important;
                        .el-icon {
                            display: none;
                        }
                    }
                }
                .el-tree-node > .el-tree-node__content {
                    .el-icon.el-tree-node__expand-icon {
                        svg {
                            display: none;
                        }
                        background-size: 15px 14px;
                        background-repeat: no-repeat;
                        background-position: center;
                        transform: rotate(0deg);
                        background-image: url(@/assets/Erp/plus.png);
                        &.expanded {
                            background-image: url(@/assets/Erp/minus.png);
                        }
                    }
                }
            }
            &.expend {
                width: 0;
                border: none;
            }
        }
    }
}
.module-item {
    width: 100%;
    .module-title {
        height: 28px;
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        line-height: 28px;
        text-align: left;
        font-style: normal;
        margin-top: 12px;
        margin-left: 20px;
    }
    .module-content {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
    }
}
.is-default-header {
    display: flex;
    align-items: center;
}
.hover-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-top: -13px;
    background: url("@/assets/Icons/question.png") no-repeat center;
    background-size: 14px 14px;
    &:hover {
        background-image: url("@/assets/Icons/question-erp.png");
    }
}
.template-empty {
    margin-top: 200px;
    flex: 1;
}
.help {
    cursor: pointer;
    display: flex;
    align-items: center;
}
.import-tip {
    margin-left: auto;
    margin-right: auto;
    padding-left: 25px;
    background: url(@/assets/Icons/hint.png) no-repeat;
    background-size: 16px 16px;
    background-position: 2px 2px;
}
.main-center-left {
    :deep(.el-checkbox__inner) {
        width: 16px !important;
        height: 16px !important;
    }
}

@media screen and (min-width: 1921px) {
    .content .main-content .main-center .main-template {
        width: 24%;
    }
}
