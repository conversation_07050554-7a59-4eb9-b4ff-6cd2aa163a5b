import { nextTick, type Directive } from "vue"

// el-dialog拖拽指令
export const dialogDrag: Directive = {
  mounted(el: any) {
    nextTick(() => {
      let targetEl = el
      function getDialogDom() {
        while (targetEl) {
          if (targetEl.classList.contains("dialogDrag")) {
            return targetEl
          }
          targetEl = targetEl.parentNode
        }
      }

      const dragDom = getDialogDom()
      const dialogHeaderEl = dragDom!.querySelector(".el-dialog__header")

      if (!dialogHeaderEl || !dragDom) return

      dialogHeaderEl.style.cursor = "move"

      const handleMouseDown = (e: MouseEvent) => {
        const disX = e.clientX
        const disY = e.clientY
        const boxTop = dragDom.offsetTop
        const boxLeft = dragDom.offsetLeft
        const screenHeight = document.body.clientHeight

        const handleMouseMove = (e: MouseEvent) => {
          const dx = e.clientX - disX
          const dy = e.clientY - disY
          let moveTop = boxTop + dy
          const moveLeft = boxLeft + dx
          if (moveTop < 0) {
            moveTop = 0
          } else if (moveTop > screenHeight - dialogHeaderEl.offsetHeight) {
            moveTop = screenHeight - dialogHeaderEl.offsetHeight
          }
          dragDom.style.margin = "0px"
          dragDom.style.top = `${moveTop}px`
          dragDom.style.left = moveLeft < 0 ? "0px" : `${moveLeft}px`
        }

        const handleMouseUp = () => {
          document.removeEventListener("mousemove", handleMouseMove)
          document.removeEventListener("mouseup", handleMouseUp)
        }

        document.addEventListener("mousemove", handleMouseMove)
        document.addEventListener("mouseup", handleMouseUp)
      }

      dialogHeaderEl.addEventListener("mousedown", handleMouseDown)
    })
  },
}
