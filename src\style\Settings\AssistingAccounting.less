@import "../Functions.less";
.main-tool-bar {
    .main-tool-left {
        .detail-el-input(190px, 30px);
        .label {
            width: 112px;
            height: 20px;
            color: var(--font-color);
            line-height: 20px;
            font-size: 14px;
        }
    }
}

.content {
    .isRow {
        display: flex;
        align-items: center;
        .row {
            width: 188px;
            height: 32px;
        }
    }
    .max {
        width: 598px;
        height: 32px;
    }
    .button {
        margin-left: 10px;
        &:first-child {
            margin-left: 0;
        }
    }
}
:deep(.el-form) {
    .el-form-item__content {
        .el-date-editor {
            position: relative;
            .el-input__prefix {
                position: absolute;
                right: 0;
                top: 0;
            }
            .el-input__suffix-inner {
                transform: translateX(-15px);
            }
        }
    }
}

.add-content {
    width: 1000px;
    margin: 0 auto;
    padding: 32px 0;
    border: 1px solid var(--slot-title-color);
    margin-top: 32px;
    box-sizing: border-box;
}
.input-ellipsis{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.note-tooltip-width{
    :deep(.popover_content){
        max-width: 680px !important;
   }
}
