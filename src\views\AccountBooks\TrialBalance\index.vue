<template>
    <div class="content">
        <div class="title">科目余额表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left" @mouseenter="closePopover">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :isPeriodList="true"
                                    @getActPid="getActPid"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">起始科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="startAsubRef"
                                    v-model="searchInfo.startAsubCode"
                                    asubImgRight="14px"
                                    :showDisabled="true"
                                    @get-asub-id="handleAsubChange($event, 'startAsub')"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">结束科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="endAsubRef"
                                    v-model="searchInfo.endAsubCode"
                                    asubImgRight="14px"
                                    :showDisabled="true"
                                    @get-asub-id="handleAsubChange($event, 'endAsub')"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目级别：</div>
                            <div class="line-item-field">
                                <el-input-number
                                    v-model="searchInfo.startAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                                <div class="ml-10 mr-10">至</div>
                                <el-input-number
                                    v-model="searchInfo.endAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                            </div>
                        </div>
                        <div class="line-item input" v-show="fcIsShow">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field fcid-select">
                                <el-select 
                                    v-model="searchInfo.fcid" 
                                    :teleported="false"
                                    :filterable="true"
                                    :filter-method="fcFilterMethod"
                                >
                                    <el-option :label="item.label" :value="item.id" v-for="item in showfcList" :key="item.id">
                                        {{ item.label }}
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.showAssist" label="显示辅助核算"></el-checkbox>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.balanceZeroUnShow" label="余额为0不显示"></el-checkbox>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox
                                    v-model="searchInfo.noAmountIncurredAndBalanceZero"
                                    label="无发生额且余额为0不显示"
                                ></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <div class="mr-20">
                        <el-checkbox v-model="searchInfo.showPeriod" label="按月列示" @change="getTableData"></el-checkbox>
                    </div>
                    <div class="mr-20">
                        <el-checkbox v-model="searchInfo.showQuantity" label="显示数量金额" @change="getTableData"></el-checkbox>
                    </div>
                    <div class="mr-20">
                        <el-checkbox v-model="searchInfo.showYear" label="显示本年累计" @change="getTableData"></el-checkbox>
                    </div>
                    <!-- <a class="button mr-10" @click="printDialogVisible = true" v-permission="['trialbalance-canprint']"> 打印 </a> -->
                    <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['trialbalance-canprint']">
                        <li @click="handlePrint(0,getDefaultParams())">直接打印</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button" @click="handleExport" v-permission="['trialbalance-canexport']">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div v-loading="loading" element-loading-text="正在加载数据..." class="main-center">
                <div class="main-title">
                    <div class="main-title-left">
                        <Tooltip :content="asubInfo" :max-width="1580" :font-size="12">科目：{{ asubInfo }}</Tooltip>
                    </div>
                </div>
                <AccountBooksTable
                    :data="tableData"
                    :columns="columns"
                    :loading="loading"
                    :empty-text="emptyText"
                    :cell-style="cellStyle"
                    :pageIsShow="true"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :current-page="paginationData.currentPage"
                    :scrollbar-show="true"
                    :tooltip-options="{ effect: 'light', placement: 'right-start' }"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #asub_code>
                        <el-table-column 
                            label="科目编码"  
                            align="left" 
                            header-align="left" 
                            :show-overflow-tooltip="true"
                            prop="asub_codes"
                            :width="getColumnWidth(setModule, 'asub_codes', 110)"
                        >
                            <template #default="scope">
                                <span :style="{ 'padding-left': asubLevel(scope.row.asub_code) * 10 + 'px' }">
                                    {{ scope.row.asub_code === '0' ? '' : scope.row.asub_code}}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #name>
                        <el-table-column 
                            label="科目名称" 
                            align="left" 
                            header-align="left" 
                            :show-overflow-tooltip="true"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name', 165)"
                        >
                            <template #default="scope">
                                <span
                                    :class="
                                        scope.row.asub_code !== '0' && checkPermission(['subsidiaryledger']) ? 'link' : 'cursor-default'
                                    "
                                    :style="{ 'padding-left': asubLevel(scope.row.asub_code) * 10 + 'px' }"
                                    @click="
                                        scope.row.asub_code !== '0' && checkPermission(['subsidiaryledger'])
                                            ? ShowDetail(scope.row.asub_name)
                                            : ''
                                    "
                                >
                                    {{ handleAsubName(scope.row.asub_name) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </AccountBooksTable>
            </div>
        </div>
    </div>
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        :title="'科目余额表打印'"
        :printData="printInfo"
        :dir-disabled="printDiasble"
        :otherOptions="otherOptions"
        :customNum="6"
        @currentPrint="handlePrint(3,getDefaultParams())"
    />
</template>

<script lang="ts">
export default {
    name: "TrialBalance",
};
</script>
<script setup lang="ts">
import Dropdown from "@/components/Dropdown/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISearchParams, ItableData, IRows, IFcList, IAsubCodeLength } from "./types";
import { usePagination } from "@/hooks/usePagination";
import { provide, reactive, ref, watch, onMounted, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { componentFinishKey } from "@/symbols";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { setColumns, rankCell, extractParams } from "./utils";
import { getAsubInfo } from "@/api/getAsubInfo";
import { handleAsubName } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import { checkPermission } from "@/util/permission";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import usePrint from "@/hooks/usePrint";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "TrialBalance";

const periodStore = useAccountPeriodStore();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const currentPeriodInfo = ref("");
// const periodInfo = ref("");
const loading = ref<boolean>(false);
const asubInfo = ref("");
const startAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const endAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const printDiasble = ref(false);
const columns = ref<Array<IColumnProps>>([]);

const searchInfo = reactive({
    startPid: Number(periodStore.getPeriodRange().start),
    endPid: Number(periodStore.getPeriodRange().end),
    startAsubCode: "",
    endAsubCode: "",
    startAsubLevel: 1,
    endAsubLevel: 4,
    showAssist: false,
    balanceZeroUnShow: false,
    noAmountIncurredAndBalanceZero: false,
    showQuantity: false,
    showPeriod: false,
    showYear: false,
    fcid: -1,
    startMonth: "",
    endMonth: "",
});

watch(
    () => searchInfo,
    (newVal) => {
        if (newVal.showQuantity || newVal.showYear) {
            printInfo.value.direction = 1;
            printDiasble.value = true;
            return;
        } else {
            printInfo.value.direction = 0;
            printDiasble.value = false;
        }
    },
    { deep: true }
);
const { periodData } = usePeriodData(searchInfo, searchInfo.startPid, searchInfo.endPid); 
const getActPid = (start: number, end: number) => {
    searchInfo.startPid = start;
    searchInfo.endPid = end;
}
const cellStyle = (row: any) => {
    if ((row.column.property === "total_debit" || row.column.property === "total_credit") && row.row[row.column.property] < 0) {
        return { backgroundColor: "#FFEEEF" };
    }
};
let accountSubjectList = useAccountSubjectStore().accountSubjectList;
let startAsubFull = "";
let endAsubFull = "";
function setInfos() {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
    startAsubFull = searchInfo.startAsubCode ? startAsubFull : ""; 
    endAsubFull = searchInfo.endAsubCode ? endAsubFull : "";
    asubInfo.value = getAsubInfo(startAsubFull, endAsubFull);
    // asubInfo.value = getAsubInfo(startAsubRef.value?.asubName, endAsubRef.value?.asubName);
}
const handleAsubChange = (asubId: number, type: string) => {
    let findItem = accountSubjectList.filter((item)=>item.asubId == asubId)[0];
    if(type === "startAsub") { 
        startAsubFull = findItem.asubCode + " " + findItem.asubAAName;
    }
    if(type === "endAsub") {
        endAsubFull = findItem.asubCode + " " + findItem.asubAAName;    
    }
};

const ShowDetail = (aaname: string) => {
    const { ASUB_ID, AA_CODE, period_s, period_e } = extractParams(aaname);
    const params = { ASUB_ID, AA_CODE, period_s, period_e,ran: Math.random() };
    globalWindowOpenPage("/AccountBooks/SubsidiaryLedger?" + getUrlSearchParams(params), "明细账");
};
const searchParams: ISearchParams = {
    period_s: searchInfo.startPid,
    period_e: searchInfo.endPid,
    sbj_leval_s: searchInfo.startAsubLevel,
    sbj_leval_e: searchInfo.endAsubLevel,
    fcid: searchInfo.fcid,
    showPeriod: searchInfo.showPeriod ? 1 : 0,
    showNumber: searchInfo.showQuantity ? 1 : 0,
    showYear: searchInfo.showYear ? 1 : 0,
};

function setParams(params: ISearchParams) {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (
        searchInfo.startAsubCode !== "" &&
        searchInfo.endAsubCode !== "" &&
        searchInfo.startAsubCode.split(" ")[0] > searchInfo.endAsubCode
    ) {
        ElNotify({
            message: "亲，起始科目不能大于结束科目哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.startAsubLevel > searchInfo.endAsubLevel) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    params.period_s = searchInfo.startPid;
    params.period_e = searchInfo.endPid;
    params.sbj_leval_s = searchInfo.startAsubLevel;
    params.sbj_leval_e = searchInfo.endAsubLevel;
    params.showNumber = searchInfo.showQuantity ? 1 : 0;
    params.showPeriod = searchInfo.showPeriod ? 1 : 0;
    params.showYear = searchInfo.showYear ? 1 : 0;
    params.fcid = searchInfo.fcid;
    return true;
}
const emptyText = ref(" ");
function handleSearch() {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return;
    }
    if (setParams(searchParams)) {
        periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
        currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
        // if (searchInfo.showPeriod) paginationData.currentPage = 1;
        paginationData.currentPage = 1;
        getTableData();
        handleClose();
    }
}

function handleClose() {
    containerRef.value?.handleClose();
}
const closePopover = () => {
    if (!containerRef.value?.popoverShow) {
        startAsubRef.value?.handleClose();
        endAsubRef.value?.handleClose();
    }
};
function handleReset() {
    searchInfo.startAsubCode = "";
    searchInfo.endAsubCode = "";
    searchInfo.startAsubLevel = 1;
    searchInfo.endAsubLevel = maxCodelength.value;
    searchInfo.showAssist = false;
    searchInfo.balanceZeroUnShow = false;
    searchInfo.noAmountIncurredAndBalanceZero = false;
    searchInfo.fcid = -1;
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
}

function getDefaultParams(){
    return {
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        sbj_leval_s: searchInfo.startAsubLevel,
        sbj_leval_e: searchInfo.endAsubLevel,
        sbj_id_s: searchInfo.startAsubCode.split(" ")[0],
        sbj_id_e: searchInfo.endAsubCode.split(" ")[0],
        fcid: searchInfo.fcid,
        showPeriod: searchInfo.showPeriod ? 1 : 0,
        showNumber: searchInfo.showQuantity ? 1 : 0,
        showYear: searchInfo.showYear ? 1 : 0,
        assistAccount: searchInfo.showAssist ? 1 : 0,
        balanceZero: searchInfo.balanceZeroUnShow ? 1 : 0,
        NoAmountIncurredAndBalanceZero: searchInfo.noAmountIncurredAndBalanceZero ? 1 : 0,
    }
}

const extraInfo = {
    isHideSubjectCode:false,
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "trialBalance",
    window.jAccountBooksHost + "/api/TrialBalance/Print",
    extraInfo,
    true,
    false,
    printValidator
);

otherOptions.value = [
    { key: "isHideSubjectCode", label: "表体不打印科目编码" },
    ...otherOptions.value,
];

function printValidator() {
    if (paginationData.total === 0) {
        ElNotify({
            message: "没有数据可打印！",
            type: "warning",
        });
        return false;
    }
    setParams(getDefaultParams())
    return true;
}

function handleExport() {
    if (paginationData.total === 0) {
        ElNotify({
            message: "没有数据可导出！",
            type: "warning",
        });
        return false;
    }
    const params = {
        ...getDefaultParams(),
        async: !isLemonClient(),
        ran: Math.random(),
    };
    if (setParams(params)) {
        globalExport(window.jAccountBooksHost + "/api/TrialBalance/Export?" + getUrlSearchParams(params));
    }
}

const tableData = ref<IRows[]>([]);
const getTableData = () => {
    loading.value = true;
    columns.value = setColumns(searchInfo.showPeriod, searchInfo.showQuantity, searchInfo.showYear, codeLengthStr.value, searchInfo.fcid);
    const data = {
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        sbj_leval_s: searchInfo.startAsubLevel,
        sbj_leval_e: searchInfo.endAsubLevel,
        fcid: searchInfo.fcid,
        showPeriod: searchInfo.showPeriod ? 1 : 0,
        showNumber: searchInfo.showQuantity ? 1 : 0,
        showYear: searchInfo.showYear ? 1 : 0,
        IsSearch: 1,
        asub_code: "",
        sbj_id_s: searchInfo.startAsubCode.split(" ")[0],
        sbj_id_e: searchInfo.endAsubCode.split(" ")[0],
        assistAccount: searchInfo.showAssist ? 1 : 0,
        balanceZero: searchInfo.balanceZeroUnShow ? 1 : 0,
        NoAmountIncurredAndBalanceZero: searchInfo.noAmountIncurredAndBalanceZero ? 1 : 0,
        rows: paginationData.pageSize,
        page: paginationData.currentPage,
    };
    request({
        url: `/api/TrialBalance?` + getUrlSearchParams(data),
    })
        .then((res: IResponseModel<ItableData>) => {
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            if (tableData.value.length === 0) {
                paginationData.total = 0;
                emptyText.value = "暂无数据";
            }
            setInfos();
        })
        .finally(() => {
            loading.value = false;
        });
};
// 科目级别
const codeLengthStr = ref("");
const maxCodelength = ref<number>(4);
function getAsubCodeLength() {
    return request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        codeLengthStr.value = res.data.codeLength.join("");
        const codeLengthList: number[] = res.data.codeLength;
        const codeLength = codeLengthList.length;
        maxCodelength.value = codeLength;
        searchParams.sbj_leval_e = codeLength;
        searchInfo.endAsubLevel = codeLength;
    });
}

function asubClassName(asubCode: string) {
    let formatString = "";
    const code = asubCode.toString().split("_")[0];
    for (var i = 0; i < codeLengthStr.value.length; i++) {
        var length = 0;
        for (var j = 0; j <= i; j++) {
            length += Number(codeLengthStr.value[j]);
        }
        if (length == code.length) {
            formatString = "level" + (i + 1) + "";
            break;
        }
    }
    return formatString;
}
function asubLevel(asubCode: string) {
    let formatString = 0;
    const code = asubCode.toString().split("_")[0];
    for (var i = 0; i < codeLengthStr.value.length; i++) {
        var length = 0;
        for (var j = 0; j <= i; j++) {
            length += Number(codeLengthStr.value[j]);
        }
        if (length == code.length) {
            formatString = i;
            break;
        }
    }
    return formatString;
}

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        // Promise.all([getAsubCodeLength()]).then((res: [IResponseModel<any>]) => {
        //     codeLengthStr.value = res[0].data.codeLength.join("");
        //     handleSearch();
        // });
        // 先获取科目字符串长度，再搜索
        getAsubCodeLength().then(() => {
            handleSearch();
        });
    }
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => getTableData());

const fcIsShow = ref<boolean>(false);
const fcList = ref<{ id: number; label: string }[]>([]);
// 获取币别并判断币别是否展示
const InitCurrencyApi = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcListOptions];
};
// 判断是否存在使用了外币核算的科目
const InitPeriodApi2 = () => {
    return request({
        url: "/api/AccountSubject/ExistsFc",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        fcIsShow.value = res.data;
        InitCurrencyApi();
    });
};
onMounted(() => {
    InitPeriodApi2();
    // periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
});

const showfcList = ref<any[]>([]);
watchEffect(() => { 
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));    
});
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'label');
}
</script>

<style scoped lang="less">
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";
:deep(.el-popper) {
    max-width: 300px;
    text-align: left;
}
.content {
    .main-center {
        display: flex;
        flex-direction: column;
        padding: 0 20px 10px;
        & :deep(.level2) {
            padding-left: 10px;
        }
        & :deep(.level3) {
            padding-left: 20px;
        }
        & :deep(.level4) {
            padding-left: 30px;
        }
        :deep(.table) {
            flex: 1;
            min-height: 0;
        }
    }
    .main-title {
        border: 1px solid var(--border-color);
        border-bottom: none;
        margin: 0;
        height: 36px;
        color: var(--font-color);
        font-size: var(--h5);
        line-height: 36px;
        display: flex;

        .main-title-left {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            flex: 1;
            text-align: left;

            :deep(.span_wrap) {
                white-space: nowrap !important;
                overflow: hidden;
                display: inline-block !important;
            }
        }
    }
}

.print-content {
    text-align: center;
    .print-main {
        text-align: left;
        display: inline-block;

        .line-item {
            height: 20px;
            .set-font;
            display: flex;
            align-items: center;
        }
    }

    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}

:deep(.el-radio__input.is-disabled .el-radio__inner) {
    background-color: #fff;
    border-color: var(--el-color-info-light-7);
    cursor: default !important;
}
:deep(.el-radio__input.is-disabled + span.el-radio__label) {
    color: #333;
    cursor: default !important;
}
:deep(.el-table__cell) {
    .cell.el-tooltip {
        min-width: 0px !important;
    }
}
:deep(.el-table .el-table__header .cell) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: clip;
}
:deep(.custom-table tbody tr td .cell) {
    // overflow: hidden;
    white-space: nowrap;
    // text-overflow:ellipsis;
    & span {
        display: block;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
// 兼容业财样式
body[erp] {
    .content {
        // .main-content {
        //     height: 96vh;
        // }
        .main-center {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            .main-title {
                margin: 0;
            }
            .table.paging-show {
                max-height: 692px;
                overflow: hidden;
            }
        }
    }
}
</style>
