<template>
    <!-- <div> -->
        <div v-show="voucherDialogIsShow" class="edit-content">
            <GenerateVoucherDialog
                ref="generateVoucherDialogRef"
                :columns="columns"
                v-model:voucher-dialog-is-show="voucherDialogIsShow"
                :preCheck="preCheck"
                :default-line="defaultLine"
                :auto-add-next-line="autoAddNextLine"
                width="670px"
                :not-delete-row="2"
                not-delete-notify-text="凭证模板不能少于2行哦"
                @voucherSave="handleVoucherSave"
                class="generate-voucher-dialog"
                :setModule="setModuleDialog"
            >
                <span class="txt" style="display: flex">
                    <span class="highlight-red">*</span><span>模板名称：</span>

                    <Tooltip :content="templateName" :max-width="140">
                        <input id="template-name-input" v-focus="autoFocus" type="text" v-model="templateName" @input="handleInput($event)" />
                    </Tooltip>
                </span>
            </GenerateVoucherDialog>
        </div>
        <div class="edit-content-custom">
            <div class="slot-title no-border">发票模板</div>
            <el-tabs v-model="InvoiceCategory">
                <el-tab-pane label="销售模板" name="10070"> </el-tab-pane>
                <el-tab-pane label="采购模板" name="10080"> </el-tab-pane>
            </el-tabs>
            <div class="edit-content-outer">
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between">
                        <div></div>
                        <div class="main-tool-right">
                            <a v-permission="['invoicevouchertemplate-canedit']" class="solid-button float-r" @click="showVoucherTemplateNew">新建</a>
                            <a class="button ml-10 float-r" @click="goBackAdd">返回</a>
                        </div>
                    </div>
                    <div class="main-center" id="salesTempTable">
                        <div
                            :class="['edit-temp-block', { 'mb-10': index !== tempTableData.length - 1 }]"
                            v-for="(item, index) in tempTableData"
                            :key="index"
                        >
                            <div class="temp-block-top">
                                <span class="temp-block-bold float-l mb-10">
                                    <!-- <Tooltip :content="item.businessTypeModel.name"> -->
                                        {{ item.businessTypeModel.name }}
                                    <!-- </Tooltip> -->
                                    <span v-if="item.voucherTemplateModel.hasErrorVoucherLines">
                                        <img class="error-icon" src="@/assets/Icons/warn-red.png" alt="" />
                                        <span class="highlight-red">科目逻辑有误</span>
                                    </span>
                                </span>

                                <a v-permission="['invoicevouchertemplate-candelete']" class="link ml-10 float-r" @click="deleteInvoiceBusinessType(item)">删除</a>
                                <a v-permission="['invoicevouchertemplate-canedit']" class="link float-r" @click="showVoucherTemplateEdit(item.businessTypeModel.name, item)">修改</a>
                                <div class="float-clear"></div>
                            </div>
                            <div class="edit-temp-lines">
                                <div class="edit-temp-line" v-for="(line, i) in item.voucherTemplateModel.voucherLines" :key="i">
                                    {{ line.debit ? "借:" : "贷:" }}&nbsp;&nbsp;&nbsp;{{ line.asubName }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!-- </div> -->
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import GenerateVoucherDialog from "@/components/Dialog/GenerateVoucher/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { request, type IResponseModel } from "@/util/service";
import { watch } from "vue";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import type { VoucherTemplateModel, ITableDataRow } from "../types";
import type { VoucherTemplateEntryModel } from "@/api/VoucherTemplate";

import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { useLoading } from "@/hooks/useLoading";
import Tooltip from "@/components/Tooltip/index.vue";

const setModuleDialog = "InvoiceGenerateVoucherDialog"
const emit = defineEmits(["goBackAdd", "templateEditSuccess"]);
const props = defineProps({
    invoiceCategory: {
        type: String,
        required: true,
    },
    autoAddName: {
        type: String,
        default: "",
    }
});

const InvoiceCategory = ref<string>("10070");
const voucherDialogIsShow = ref<boolean>(false);
const columns = ref<Array<IColumnProps>>([{ slot: "asubId" }, { slot: "direction" }, { slot: "InvoicevalueType" }]);
const tempTableData = ref<ITableDataRow[]>([]);
const generateVoucherDialogRef = ref<InstanceType<typeof GenerateVoucherDialog>>();
const templateName = ref<string>("");
const goBackAdd = () => {
    emit("goBackAdd");
};
const editRowId = ref<number>(0);
// 自定义指令v-if情况下生效，但是数据又及时渲染不上去，点击新增添加聚焦，首次不聚焦，两者结合使用
const vFocus = {
    mounted: (el: any, binding: any) => {
        if (binding.value) {
            let timer = setTimeout(() => {
                const focusedElement = document.activeElement as HTMLElement;
                focusedElement?.blur();
                el.focus();
                clearTimeout(timer);
            }, 100);
        }
    },
};
const defaultLine = {
    asubId: "",
    direction: 1,
    valueType: 1010,
};

function handleInput(e: any) {
    const { value } = e.target as HTMLInputElement;
    if (value.length > 30) {
        ElNotify({ type: "warning", message: `亲，模板名称不能超过30个字符!` });
        e.target.value = value.slice(0, 30);
        templateName.value = value.slice(0, 30);
        return;
    }
}

let defaultVgId = useVoucherGroupStore().defaultVgId;
let autoFocus = ref(false);
let autoAddNextLine = ref(false);
function showVoucherTemplateNew() {
    autoFocus.value = true;
    autoAddNextLine.value = true;
    voucherDialogIsShow.value = true;
    templateName.value = props.autoAddName.slice(0, 30);
    if (props.autoAddName.length > 30) {
        ElNotify({ type: "warning", message: `亲，模板名称不能超过30个字符!` });
    }
    const data = {
        vgId: defaultVgId,
        vtId: 0,
        voucherTemplateLines: [
            {
                asubId: "",
                direction: 1,
                valueType: 1010,
            },
            {
                asubId: "",
                direction: 2,
                valueType: 1010,
            },
            {
                asubId: "",
                direction: 1,
                valueType: 1010,
            },
        ],
    };
    editRowId.value = 0;
    generateVoucherDialogRef.value?.initEditForm(data, "new");
    const input = document.getElementById("template-name-input");
    let timer = setTimeout(() => {
        const focusedElement = document.activeElement as HTMLElement;
        focusedElement?.blur();
        input?.focus();
        clearTimeout(timer);
    }, 100);
}

function showVoucherTemplateEdit(name: string, rowData: ITableDataRow) {
    autoFocus.value = false;
    autoAddNextLine.value = true;
    voucherDialogIsShow.value = true;
    templateName.value = name;
    editRowId.value = rowData.businessTypeModel.id;
    const data = {
        vgId: rowData.voucherTemplateModel.vgId,
        vtId: rowData.voucherTemplateModel.vtId,
        voucherTemplateLines: rowData.voucherTemplateModel.voucherLines.map((item: VoucherTemplateEntryModel) => {
            return {
                asubId: String(item.asubId),
                direction: item.debit > 0 ? 1 : 2,
                valueType: item.valueType,
                valueId: "",
            };
        }),
    };

    generateVoucherDialogRef.value?.initEditForm(data);
}

function initCategory(cate: string) {
    InvoiceCategory.value = cate;
    getInitDataList();
}
function getInitDataList() {
    request({
        url: `/api/InvoiceTemplate/${InvoiceCategory.value === "10070" ? "Sales" : "Purchase"}List`,
        method: "get",
    }).then((res: IResponseModel<ITableDataRow[]>) => {
        tempTableData.value = res.data;
    });
}

const preCheck = () => {
    if (!templateName.value.trim()) {
        ElNotify({
            type: "warning",
            message: "发票模板名称不能为空",
        });
        return false;
    }
    return true;
};

function handleVoucherSave() {
    const id = editRowId.value;
    useLoading().enterLoading("努力加载中，请稍候...");

    request({
        url: `/api/InvoiceBusinessType/${InvoiceCategory.value === "10070" ? "Sales" : "Purchase"}CheckName`,
        method: "post",
        params: {
            id: id,
            name: templateName.value,
        },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            useLoading().quitLoading();

            if (!res.data) {
                const params = { vtType: 400, vtName: "发票模板" };
                generateVoucherDialogRef.value
                    ?.saveVoucherTemplate(params)
                    .then((res: any) => {
                        let newBUsinessId = res;
                        let method: string;
                        if (id !== 0) {
                            method = "put";
                        } else {
                            method = "post";
                        }
                        request({
                            url: `/api/InvoiceBusinessType/${InvoiceCategory.value === "10070" ? "Sales" : "Purchase"}`,
                            method: method,
                            params: {
                                id: id,
                            },
                            data: {
                                name: templateName.value, //string
                                templateId: res, //int
                            },
                        }).then((res: any) => {
                            if (res.state === 1000 && res.data) {
                                ElNotify({
                                    type: "success",
                                    message: "保存成功",
                                });
                                voucherDialogIsShow.value = false;
                                getInitDataList();
                                emit("templateEditSuccess", newBUsinessId);
                            } else {
                                ElNotify({
                                    type: "error",
                                    message: "出现异常，请稍后重试",
                                });
                            }
                        });
                    })
                    .catch((error: any) => {
                        console.log(error);
                        ElNotify({
                            type: "error",
                            message: "出现异常，请稍后重试",
                        });
                    });
            } else {
                ElNotify({
                    type: "warning",
                    message: "发票模板名称重复，请修改",
                });
            }
        } else {
            ElNotify({
                type: "warning",
                message: "出现异常，请稍后重试",
            });
        }
    });
}
watch(InvoiceCategory, () => {
    getInitDataList();
});
function deleteInvoiceBusinessType(item: ITableDataRow) {
    ElConfirm("亲，确认要删除吗？").then((r: boolean) => {
        if (r) {
            request({
                url: `/api/InvoiceBusinessType/${InvoiceCategory.value === "10070" ? "Sales" : "Purchase"}`,
                method: "delete",
                params: {
                    id: item.businessTypeModel.id,
                },
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000 && res.data) {
                        ElNotify({
                            type: "success",
                            message: "删除成功",
                        });
                        emit("templateEditSuccess");
                        getInitDataList();
                    } else {
                        ElNotify({
                            type: "warning",
                            message: res.msg,
                        });
                    }
                })
                .catch(() => {
                    ElNotify({
                        type: "warning",
                        message: "删除失败，请联系客服或管理员～",
                    });
                });
        }
    });
}
defineExpose({
    getInitDataList,
    initCategory,
    showVoucherTemplateNew,
});
</script>

<style lang="less" scoped>
@import url(@/style/Functions.less);
.content {
    .edit-content-custom {
        width:100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .el-tabs{
            :deep(.el-tabs__nav-scroll){
                overflow: hidden;
                width: var(--content-width);
                margin: 0 auto;
            }
        }
        .edit-content-outer {
            flex: 1;
            overflow-y: auto;
        }
        .main-content {
            width: var(--content-width);
            margin: 0 auto;

            .main-top {
                padding: 10px 20px;
            }
            .main-center {
                padding:0 20px 20px;
                overflow: initial;
            }
        }
    }
}
:deep(.el-tabs__header) {
    margin: 0;
}

.edit-temp-top {
    padding: 16px 20px;
    height: 30px;
}

.float-clear {
    display: block;
    clear: both;
}

.main-center {
    font-size: var(--h5);
    color: var(--font-color);

    .edit-temp-block {
        border: 1px solid var(--border-color);

        .temp-block-top {
            display: block;
            margin: 10px 20px 0px 20px;
            line-height: 17px;

            .temp-block-bold {
                font-weight: bold;
                text-align: left;
                white-space: normal;
                .error-icon {
                    width: 16px;
                    height: 16px;
                    display: inline-block;
                    padding: 0px 6px 0px 14px;
                    position: relative;
                    top: 2px;
                }
            }

            .link {
                font-size: var(--h5);
            }
        }

        .edit-temp-lines {
            margin: 10px 40px 10px 40px;

            .edit-temp-line {
                text-align: left;
                margin-top: 6px;
                &:first-of-type {
                    margin-top: 0;
                }
            }
        }
    }
}

.generate-voucher-dialog {
    input {
        .detail-original-input(180px, 30px);
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .txt {
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 30px;
        margin-right: 2px;
    }
}
:deep(.temp-block-bold) {
    max-width: 875px;
    max-height: 34px;
    overflow: hidden;
    word-break: break-all;
}
</style>
