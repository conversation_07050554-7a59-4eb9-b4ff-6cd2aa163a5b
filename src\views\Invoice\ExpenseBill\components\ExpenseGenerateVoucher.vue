<template>
    <GenerateVoucher
        :title="'费用单据生成凭证'"
        :documentTitle="'费用单据'"
        :query-params="genVoucherQueryParameters"
        :error-table-columns="genVoucherErrorTableColumns"
        @back="genVoucherSaveSuccess"
        @load-success="loadSuccess = true"
        :merge-error-function="mergeErrorFunction"
        @voucher-changed="genVoucherChangedHandle"
        ref="generateVoucherView"
    >
        <template #toolbar>
            <a class="solid-button" @click="genVoucher()">保存</a>
            <a class="button ml-10" @click="cancelGenVoucher()">取消</a>
            <el-checkbox class="ml27" label="凭证合并" @change="mergeVoucher()" v-model="genVoucherQueryParameters.isMerge"></el-checkbox>
            <SelectCheckbox
                class="ml10"
                ref="selectCheckboxRef"
                inputPlaceholder="请选择凭证合并条件"
                :useElIcon="true"
                width="180px"
                :options="options"
                :select-all="false"
                @update:selected-list="changeSelectedList"
                v-show="genVoucherQueryParameters.isMerge"
                @check-box-show-change="checkBoxShowChange"
                v-model:selectedList="selectedList"
                :show-all="false"
            >
            </SelectCheckbox>

            <span class="ml10" style="font-size: 14px" v-show="genVoucherQueryParameters.isMerge">科目合并</span>
            <SelectCheckbox
                class="ml10"
                :useElIcon="true"
                width="180px"
                :options="subjectOption"
                :select-all="false"
                @update:selected-list="changeSubjectSelectedList"
                v-show="genVoucherQueryParameters.isMerge"
                ref="selectSubjectRef"
                v-model:selectedList="subjectSelectedList"
                :place-holder="'请选择科目合并方式'"
            >
            </SelectCheckbox>

            <el-checkbox
                class="ml10"
                label="智能匹配往来单位"
                v-model="genVoucherQueryParameters.autoMatch"
                @change="genVoucherCheckboxChanged(3)"
            ></el-checkbox>
            <BubbleTip :bubble-width="354" :bubble-top="16" class="ml-10">
                <template #content>
                    <div class="help-icon"></div>
                </template>
                <template #tips>
                    <div>根据费用单据的费用类型、结算方式设置的科目</div>
                    <div v-show="!isFarmer && !isUnion && !isVillage">1、智能匹配六大往来（应收账款、其他应收款、预收账款、</div>
                    <div v-show="!isFarmer && !isUnion && !isVillage">应付账款、其他应付款和预付账款）的职员/客户/供应商</div>
                    <div v-show="isFarmer || isVillage">1、智能匹配往来科目（应收款、应付款）的职员/客户/供应商</div>
                    <div v-show="isUnion">1、智能匹配往来科目（其他应收款、其他应付款）的职员/客户/供应商</div>
                    <div>2、自动新增对应的往来单位</div>
                </template>
            </BubbleTip>
        </template>
    </GenerateVoucher>
</template>
<script setup lang="ts">
import GenerateVoucher from "@/components/GenerateVoucher/index.vue";
import BubbleTip from "@/components/BubbleTip/index.vue";
import { ref } from "vue";
import {
    ExpenseBillQueryParameters,
    ExpenseBillWithVoucherModel,
    type IBatchGenerateVoucherModel,
    type IGenVoucherNeedInsertAsub,
    type IExpenseBillGenVoucherResult,
} from "@/components/GenerateVoucher/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableItem } from "../types";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { globalWindowOpenPage } from "@/util/url";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useLoading } from "@/hooks/useLoading";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import type { IVoucherSetting } from "@/components/Dialog/GenerateVoucherSetting/type";
import type { Option } from "@/components/SelectCheckbox/types";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { formatMoney } from "@/util/format";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";


const selectCheckboxRef = ref<InstanceType<typeof SelectCheckbox>>();
const voucherSettings = ref<IVoucherSetting>({} as IVoucherSetting);
const setVoucherSettings = (voucherSetting: IVoucherSetting) => {
    voucherSettings.value = voucherSetting;
};
const _ = getGlobalLodash();
const accountSet = useAccountSetStore().accountSet;
const accountStandard = accountSet?.accountingStandard;
const isFarmer = ref(accountStandard === 4 || accountStandard === 5);
const isUnion = ref(accountStandard === 6);
const isVillage = ref(accountStandard === 7);

const props = defineProps<{
    openGenVoucher: () => void;
    cancelGenVoucher: () => void;
    genVoucherSaveSuccess: () => void;
}>();
const generateVoucherView = ref<InstanceType<typeof GenerateVoucher>>();
const genVoucherQueryParameters = ref(new ExpenseBillQueryParameters());

const genVoucherChanged = ref(false);
const baseVoucherChanged = ref(false);
const genVoucherErrorTableColumns = ref<IColumnProps[]>([
    {
        label: "单据编号",
        prop: "billNo",
        align: "left",
        headerAlign: "left",
        minWidth: 160,
    },
    {
        label: "费用类型",
        prop: "billTypeName",
        align: "left",
        headerAlign: "left",
        minWidth: 110,
    },
    {
        label: "金额",
        prop: "amount",
        align: "left",
        headerAlign: "left",
        minWidth: 136,
        formatter(row, column, cellValue) {
            return formatMoney(cellValue);
        },
    },
    {
        slot: "errorInfo",
    },
]);

// 凭证相关
let isGenVoucher = false;
let checkItemsCopy: ITableItem[] = [];
const genVoucherFromExpenseBill = (checkItems: ITableItem[]) => {
    if (isGenVoucher) {
        ElNotify({ message: "正在生成凭证，请稍后！", type: "warning" });
        return false;
    }
    isGenVoucher = true;
    let existHasVoucher = false;
    //移除可能选中的全选按钮和已生成凭证数据
    for (let j = checkItems.length - 1; j >= 0; j--) {
        if (checkItems[j] === undefined) {
            checkItems.splice(j, 1);
            continue;
        }
        if (checkItems[j].v_id > 0 && checkItems[j].v_num2) {
            existHasVoucher = true;
            checkItems.splice(j, 1);
        }
    }
    if (existHasVoucher) {
        ElAlert({ message: "亲，已生成凭证的单据将会被跳过，是否继续生成凭证？" }).then((r: any) => {
            if (r) {
                getVoucherFromCheckedItems(checkItems);
            } else {
                isGenVoucher = false;
                return false;
            }
        });
    } else {
        getVoucherFromCheckedItems(checkItems);
    }
    checkItemsCopy = checkItems;
};

const getVoucherFromCheckedItems = (checkItems: ITableItem[]) => {
    // 从缓存中读取凭证合并的选项
    if (window.localStorage.getItem("genVoucher-expense-bill")) {
        let parseObject = JSON.parse(window.localStorage.getItem("genVoucher-expense-bill") as string) as ExpenseBillQueryParameters;
        Object.setPrototypeOf(parseObject, ExpenseBillQueryParameters.prototype);
        if (parseObject.mergeDate) {
            selectedList.value.push(1);
        }
        if (parseObject.mergeBillType) {
            selectedList.value.push(2);
        }
        if (parseObject.mergePayMethod) {
            selectedList.value.push(4);
        }
        if (parseObject.isMerge && selectedList.value.length === 0) {
            selectedList.value.push(0);
        }
        if (parseObject.mergeCredit) {
            subjectSelectedList.value.push(32);
        }
        if (parseObject.mergeDebit) {
            subjectSelectedList.value.push(16);
        }
        genVoucherQueryParameters.value = parseObject;
    } else {
        genVoucherQueryParameters.value.autoMatch = false;
    }

    if (checkItems.length > 0) {
        getExpenseBillAndVoucherInfo(checkItems, voucherSettings.value);
    } else {
        isGenVoucher = false;
        ElNotify({ message: "请选择凭据后生成凭证！", type: "warning" });

        return false;
    }
};
const getExpenseBillAndVoucherInfo = (checkItems?: ITableItem[], voucherSettings?: IVoucherSetting) => {
    const parameters = {
        BillIds: checkItems?.map((item) => item.billId),
        AutoMatch: genVoucherQueryParameters.value.autoMatch,
        VoucherSettings: voucherSettings,
    };

    request({
        url: "/api/ExpenseBillVoucher/GetVouchersFromExpenseBill",
        method: "post",
        data: parameters,
    })
        .then((res: IResponseModel<any>) => {
            if (res.state !== 1000) {
                ElNotify({ message: res.msg || "出现错误，请稍后重试！", type: "warning" });
                isGenVoucher = false;
                loadSuccess.value = true;
                return;
            }

            const data = res as IResponseModel<IBatchGenerateVoucherModel<ExpenseBillWithVoucherModel>>;
            genVoucherChanged.value = false;
            baseVoucherChanged.value = false;
            // 如果获取的数据里有错误数据，就不可以合并凭证
            if (genVoucherQueryParameters.value.isMerge === true) {
                if (!checkMergeVoucher(data.data)) {
                    genVoucherQueryParameters.value.isMerge = false;
                }
            }

            generateVoucherView.value?.loadDocumentList(data.data, voucherSettings);
            props.openGenVoucher();
            const timer = setTimeout(() => {
                isGenVoucher = false;
                clearTimeout(timer);
            }, 1000);
        })
        .catch(() => {
            isGenVoucher = false;
            loadSuccess.value = true;
            ElNotify({ message: "出现错误，请稍后重试！", type: "warning" });
        });
};

// 如果获取的数据里有错误数据，就不可以合并凭证
const checkMergeVoucher = (data: IBatchGenerateVoucherModel<ExpenseBillWithVoucherModel>) => {
    if (data) {
        for (let i = 0; i < data.documentWithVoucherList.length; i++) {
            if (data.documentWithVoucherList[i].document.errorInfo !== "") {
                ElNotify({ message: "亲，生成的凭证存在错误暂不支持合并，请依据错误提示修改凭证后再进行合并哦！", type: "warning" });
                return false;
            }
        }
    }
    return true;
};

const emit = defineEmits<{
    (event: "no-template", ietype: number, ietypeId: number): void;
    (event: "voucherSetting"): void;
}>();
function genVoucherChangedHandle() {
    genVoucherChanged.value = true;
}

const genVoucher = _.debounce(saveGenVoucher, 500);

let isSaving = false;
async function saveGenVoucher() {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (isSaving) {
        ElNotify({ message: "正在保存，请稍后！", type: "warning" });
        return;
    }
    isSaving = true;
    const parameters = generateVoucherView.value?.getGenVoucherParameters();
    if (parameters) {
        useLoading().enterLoading("努力加载中，请稍后...");
        request({
            url: "/api/ExpenseBillVoucher/FindNeedAutoInsertAccountSubject",
            method: "post",
            data: parameters,
        })
            .then((res: any) => {
                const data = res as IResponseModel<IGenVoucherNeedInsertAsub>;
                if (data.state === 1000) {
                    new Promise((resolve, reject) => {
                        for (let i = 0; i < parameters.temporaryAccountSubjectList.length; i++) {
                            if (
                                parameters.temporaryAccountSubjectList[i].asubCode
                                    .toString()
                                    .indexOf(parameters.temporaryAccountSubjectList[i].parentAsubCode.toString()) !== 0
                            ) {
                                isSaving = false;
                                useLoading().quitLoading();
                                ElConfirm("您的科目编码长度不足，智能匹配失败，是否立即前往科目编码设置？").then((r) => {
                                    if (r) {
                                        globalWindowOpenPage("/Settings/AccountSubject", "科目");
                                    }
                                });
                                reject();
                                return;
                            }
                        }
                        if (data.data.autoInsertAsubList.length > 0) {
                            const parentAsubs = [];
                            const asubs = [];
                            for (let i = 0; i < data.data.autoInsertAsubList.length; i++) {
                                parentAsubs.push(data.data.autoInsertAsubList[i].parentAsubName);
                                asubs.push(data.data.autoInsertAsubList[i].asubName);
                            }
                            const msg =
                                '<div>' +
                                parentAsubs.join("、") +
                                "已有凭证，将新增同名下级科目"+ '<br>' +
                                asubs.join("、") +
                                "替代，您要继续吗？" +
                                "</div>";
                            useLoading().quitLoading();
                            ElAlert({ message: msg, leftJustifying: true }).then((r) => {
                                if (r) {
                                    useLoading().enterLoading("努力加载中，请稍后...");
                                    resolve(0);
                                } else {
                                    isSaving = false;
                                    reject();
                                }
                            });
                        } else {
                            resolve(0);
                        }
                    }).then(() => {
                        request({
                            url: "/api/ExpenseBillVoucher/Submit",
                            method: "post",
                            data: parameters,
                        })
                            .then((res: IResponseModel<IExpenseBillGenVoucherResult>) => {
                                useLoading().quitLoading();
                                const data = res;
                                if (data.state === 1000) {
                                    dispatchReloadAsubAmountEvent();
                                    useAccountSubjectStore().getAccountSubject();
                                    useAssistingAccountingStore().getAssistingAccounting();
                                    generateVoucherView.value?.loadSaveResult(data.data);
                                    const timer = setTimeout(() => {
                                        isSaving = false;
                                        clearTimeout(timer);
                                    }, 1000);
                                } else {
                                    isSaving = false;
                                    if (
                                        data.msg != null &&
                                        data.msg != undefined &&
                                        (data.msg.indexOf("将截断字符串或二进制数据") != -1 ||
                                            data.msg.indexOf("String or binary data would be truncated") != -1)
                                    ) {
                                        ElNotify({
                                            message: "保存出错了，请检查往来单位不要超过64个汉字！",
                                            type: "warning",
                                        });
                                    } else {
                                        ElNotify({
                                            message: data.msg || "保存出错了，请刷新页面重试或联系客服处理！",
                                            type: "warning",
                                        });
                                    }
                                }
                            })
                            .catch((err: any) => {
                                if (err.code !== "ERR_NETWORK") {
                                    isSaving = false;
                                    useLoading().quitLoading();
                                }
                            });
                    });
                } else {
                    isSaving = false;
                    useLoading().quitLoading();
                }
            })
            .catch(() => {
                isSaving = false;
                useLoading().quitLoading();
            });
    } else {
        isSaving = false;
    }
}

const loadSuccess = ref(true);
function genVoucherCheckboxChanged(flag: number, selectedist?: any) {
    new Promise<boolean>((resolve) => {
        if (genVoucherChanged.value || (baseVoucherChanged.value && flag === 3)) {
            ElConfirm("系统可能不会保存您做的更改，确定要切换吗？").then((r) => {
                if (r) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(true);
        }
    }).then((r) => {
        if (r) {
            if (flag > 0 && selectedist !== undefined) {
                generateVoucherView.value?.loadDocumentList(undefined, voucherSettings.value);
                genVoucherChanged.value = false;
                window.localStorage.setItem("genVoucher-expense-bill", JSON.stringify(genVoucherQueryParameters.value));
            } else {
                if (flag === 3) {
                    window.localStorage.setItem("genVoucher-expense-bill", JSON.stringify(genVoucherQueryParameters.value));
                    getExpenseBillAndVoucherInfo(checkItemsCopy, voucherSettings.value);
                } else {
                    generateVoucherView.value?.loadDocumentList(undefined, voucherSettings.value);
                    genVoucherChanged.value = false;
                    window.localStorage.setItem("genVoucher-expense-bill", JSON.stringify(genVoucherQueryParameters.value));
                }
            }
        } else if (selectedist !== undefined) {
            selectedist.value = _.cloneDeep(backSelectedList.value);
            loadSuccess.value = true;
        } else if (flag === 3) {
            genVoucherQueryParameters.value.autoMatch = !genVoucherQueryParameters.value.autoMatch;
            loadSuccess.value = true;
        } else {
            genVoucherQueryParameters.value.isMerge = !genVoucherQueryParameters.value.isMerge;
            loadSuccess.value = true;
        }
    });
}

const options = ref<Array<Option>>([
    {
        id: 0,
        name: "合并成一张凭证",
    },
    {
        id: 1,
        name: "按日期分开合并",
    },
    {
        id: 2,
        name: "按费用类型分开合并",
    },
    {
        id: 4,
        name: "按结算方式分开合并",
    },
]);
const subjectOption = ref<Array<Option>>([
    {
        id: 16,
        name: "相同借方科目合并",
    },
    {
        id: 32,
        name: "相同贷方科目合并",
    },
]);
const selectedList = ref<number[]>([]);
const subjectSelectedList = ref<number[]>([]);
const backSelectedList = ref<number[]>([]);
const selectSubjectRef = ref();
const selectZero = ref(true);

function changeSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if (selectZero.value && val.length > 1 && val[0] === 0) {
        val.splice(0, 1);
        selectZero.value = false;
    } else if (val.findIndex((z) => z === 0) !== -1) {
        selectZero.value = true;
        val.splice(0);
        val.push(0);
    }

    backSelectedList.value = _.cloneDeep(selectedList.value);
    selectedList.value = val;
    genVoucherQueryParameters.value.mergeDate = val.includes(1) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergeBillType = val.includes(2) && genVoucherQueryParameters.value.isMerge;
    genVoucherQueryParameters.value.mergePayMethod = val.includes(4) && genVoucherQueryParameters.value.isMerge;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(1, selectedList);
}

function checkBoxShowChange(val: boolean) {
    if (!val && selectedList.value.length === 0 && genVoucherQueryParameters.value.isMerge) {
        ElNotify({ message: "凭证合并默认需要勾选一个条件哦~", type: "warning" });
        genVoucherQueryParameters.value.isMerge = true;
        selectedList.value.push(0);
        selectZero.value = true;
        genVoucherCheckboxChanged(1, selectedList);
    }
}

function mergeVoucher() {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }
    if (genVoucherQueryParameters.value.isMerge) {
        if (!generateVoucherView.value?.checkMergeVoucher()) {
            genVoucherQueryParameters.value.isMerge = false;
            return;
        }
    }

    loadSuccess.value = false;
    if (genVoucherQueryParameters.value.isMerge) {
        genVoucherQueryParameters.value.mergeOthers = true;
        if (selectedList.value.length === 0 && subjectSelectedList.value.length === 0) {
            selectedList.value.push(0);
            subjectSelectedList.value.push(16);
            subjectSelectedList.value.push(32);
            selectSubjectRef.value?.changeSelectAll(true);
            genVoucherQueryParameters.value.mergeCredit = true;
            genVoucherQueryParameters.value.mergeDebit = true;
        }

        if (genVoucherChanged.value) {
            baseVoucherChanged.value = true;
            genVoucherChanged.value = false;
        }
        genVoucherCheckboxChanged(1, selectedList);
    } else {
        genVoucherCheckboxChanged(0, undefined);
    }
}

function changeSubjectSelectedList(val: number[]) {
    if (!loadSuccess.value) {
        ElNotify({ message: "请等待数据加载完成", type: "warning" });
        return;
    }

    backSelectedList.value = _.cloneDeep(subjectSelectedList.value);
    subjectSelectedList.value = val;
    genVoucherQueryParameters.value.mergeDebit = val.includes(16) && genVoucherQueryParameters.value.mergeOthers;
    genVoucherQueryParameters.value.mergeCredit = val.includes(32) && genVoucherQueryParameters.value.mergeOthers;
    loadSuccess.value = false;
    genVoucherCheckboxChanged(2, subjectSelectedList);
}

function mergeErrorFunction() {
    genVoucherQueryParameters.value.mergeCredit = false;
    genVoucherQueryParameters.value.mergeDebit = false;
    subjectSelectedList.value = [];
}
defineExpose({
    genVoucherFromExpenseBill,
    setVoucherSettings,
});
</script>
