export interface IpagerSize {
    label: string;
    value: number;
    width: number;
    height: number;
    marginTop: number;
    marginBottom: number;
    marginLeft: number;
    marginRight: number;
}
export enum PrintSettingTypeEnum {
    VoucherBase = 1,
    VoucherBasePreview = 3,
    AccountBooks = 11,
    AccountBooksPreview = 13,
    AccountBookHorizontalBaseImage = 14,
    AccountBookHorizontalBaseImagePreview = 16,
}

export enum PintSettingIdEnum {
    VoucherBase = 11,
    VoucherBasePreview = 12,
}

export interface IFiled{
    fieldName: string,
    checked: boolean,
    fieldType: string,
    order: number,
}
export interface IMoudule{
    moudleType: string,
    moudleName: string,
    order: number,
    seniorFieldList:IFiled[]
}
export type TextAlign = 'left' | 'center' | 'right' | 'justify';
export interface IStyle {
    fontFamily: string;
    fontSize: number;
    shadowFontSize: number;
    fontWeight: string;
    fontStyle: string;
    textDecoration: string;
    whiteSpace: string;
    shadowWhiteSpace: string;
    wordBreak: string;
    overflow: string;
    textAlign: TextAlign;
    justifyContent: string;
    isSelected: boolean;
    [key: string]: string | number | boolean;
}
export interface IDragBoxOptions {
    top: number;
    left: number;
    id?: number;
    positionType?: number;
    height?: number;
    width?: number;
    label?: string;
    content?: ArrayBuffer;
    placeholder?: string;
    style?: IStyle;
    columnName?: string;
    dataSource?: string | ArrayBuffer;
}
export type ModuleType = 'text' | 'img' | 'barcode' | 'qrCode' | 'line' | 'rectangle' | 'table' | 'tableHeader' | 'tableBody' | 'tableTotal' | 'textNoLabel';
export interface IPrintData {
    id: number;
    label: string;
    text: string;
    dataSource: string;
    positionType: number;
    dataType: number;
    left: number;
    top: number;
    width: number;
    height: number;
    index: number;
    style: IStyle;
}
export interface ISavePrintData {
    id: number;
    label: string;
    text: string;
    dataSource: string | ArrayBuffer;
    positionType: number;
    dataType: number;
    left: number;
    top: number;
    width: number;
    height: number;
    index: number;
    fontFamily: string,
    fontSize: number,
    isBold: boolean,
    isItalic: boolean,
    textAlign: number,
    justifyContent: number,
    overflow: number,
    style: IStyle;
}
export interface IModuleData {
    label: string;
    text: string;
    dataSource: string | ArrayBuffer;
    positionType: number;
    id: number;
    active: boolean;
    type: ModuleType;
    left: number;
    top: number;
    width: number;
    height: number;
    index: number;
    inputable: boolean;
    sticks: string[];
    style: IStyle;
}

export interface BoxItem extends IModuleData {
    // 可以添加额外的属性
    hearderHeight?: number;
    footerHeight?: number;
    header?: IModuleData;
}

export interface ITableList{
    id: number;
    label: string;
    text: string;
    dataSource: string;
    width: number;
    headerHeight: number;
    bodyHeight: number;
    footerHeight:number,
    index:number;
    inputable: boolean;
    headerStyle: IStyle;
    bodyStyle: IStyle;
    footerStyle: IStyle;
    isSelected: boolean;
}

export enum TextAlignmentType {
    left = 0, //左对齐
    center = 1, //水平居中
    right = 2, // 右对齐
    justify = 3, // 两端对齐
}

export enum VoucherPrintDataType{
    text = 1,
    img = 2,
    barcode = 3,
    qrCode = 4,
    line = 5,
    rectangle = 6,
    table = 7,
    tableHeader = 8,
    tableBody = 9,
    tableTotal = 10,
    coverImg = 11,
    textNoLabel = 12,
}
