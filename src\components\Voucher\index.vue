<template>
    <div class="container" ref="containerRef">
        <div
            class="voucher-container"
            :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in' }"
            ref="voucherContainer"
            @click="orderClickIndex = -1"
            :style="{ marginBottom: appendMargin + 'px' }"
        >
            <div
                class="voucher-toolbar mb-10"
                v-show="toolbarDisplay && !hiddenButton === true"
                :class="{ 'voucher-bottomBtn': isBottomBtn }"
            >
                <VoucherButton
                    ref="voucherButtonRef"
                    v-model:queryParams="propsQueryParams"
                    v-model:voucherModel="voucherModel"
                    :checkAllVoucherLines="checkAllVoucherLines"
                    :rebindVoucherlineOnScroll="rebindVoucherlineOnScroll"
                    :clearVoucher="clearVoucher"
                    :saveVoucher="saveVoucher"
                    :new-voucher="newVoucher"
                    :edited="edited"
                    @save="$emit('save')"
                    @saveAndNew="$emit('saveAndNew')"
                    :load-voucher-template="loadVoucherTemplate"
                    :load-voucher-draft="loadVoucherDraft"
                    :showCancel="showCancel"
                    :cancelTxt="cancelTxt"
                    :voucher-title="voucherTitle"
                    @back="$emit('back')"
                    @delete-voucher="$emit('deleteVoucher')"
                    :showKeysTip="showKeysTip"
                    :showSwitchBtn="showSwitchBtn"
                    :switchInfo="switchInfo"
                    @preVoucher="$emit('preVoucher')"
                    @nextVoucher="$emit('nextVoucher')"
                    :isBottomBtn="isBottomBtn"
                    :isGenerateVoucherPage="isGenerateVoucherPage"
                    :module-permission="modulePermission"
                    :resetWarningRow="resetWarningRow"
                />
            </div>
            <div class="line mt-10 mb-10 voucher-button-line" v-show="toolbarDisplay && !(hiddenLine === true) && !isBottomBtn"></div>
            <div class="voucher-toolbar mt-10" v-show="toolbarDisplay">
                <div class="toolbar-left">
                    <div class="voucher-vgname">
                        <Select
                            v-model="voucherModel.vgId"
                            :teleported="false"
                            @change="resetVDateAndVNum(undefined, true)"
                            :disabled="isVoucherReadonly"
                            :use-custom-class="false"
                        >
                            <template v-if="voucherModel.vgId === 0">
                                <Option :value="0" :label="' '"></Option>
                            </template>
                            <template v-else>
                                <Option v-for="vg in voucherGroupList" :key="vg.id" :value="vg.id" :label="vg.name"></Option>
                            </template>
                        </Select>
                    </div>
                    <div class="voucher-num">
                        <el-input-number
                            v-if="voucherModel.vnum !== 0"
                            v-model="voucherModel.vnum"
                            :min="1"
                            :max="9999"
                            :precision="0"
                            :controls="false"
                            :disabled="isVoucherReadonly"
                        />
                        <el-input v-else value="" :disabled="isVoucherReadonly" />
                        <span>号</span>
                    </div>
                    <div class="voucher-date">
                        <span>日期：</span>
                        <el-date-picker
                            v-model="voucherModel.vdate"
                            ref="dateRef"
                            type="date"
                            :clearable="false"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled="isVoucherReadonly"
                            :disabled-date="getVDateDisabledState"
                            @keydown.enter="handleKeyDown($event)"
                            @change="resetVDateAndVNum()"
                        >
                            <template #default="cell">
                                <div class="el-date-table-cell" @click="resetVDateAndVNum()">
                                    <span class="el-date-table-cell__text">{{ cell.text }}</span>
                                </div>
                            </template>
                        </el-date-picker>
                    </div>
                </div>
                <span class="tips" v-show="!isVoucherReadonly">亲，按回车键，能快速选择单元格哟!</span>
                <div class="toolbar-right">
                    <div class="voucher-readonly-tip mr-20" v-show="isVoucherReadonly">
                        <img src="@/assets/Voucher/checked.png" v-show="voucherModel.pstatus === 3" />
                        <img src="@/assets/Voucher/approved.png" v-show="voucherModel.approveStatus === 1 && voucherModel.pstatus !== 3" />
                    </div>
                    <div class="voucher-attachments">
                        <span>附单据</span>
                        <el-input-number
                            ref="attachmentsRef"
                            v-model="voucherModel.attachments"
                            :min="0"
                            :max="99999"
                            :precision="0"
                            :controls="false"
                            :disabled="isVoucherReadonly"
                            @keyup.enter="focusFirstLine(true)"
                            @keypress="handleAttachmentsKeyPress"
                        />
                        <span>张</span>
                    </div>
                    <a class="voucher-attachfiles link" @click="attachfilesClick()">附件</a>
                    <a
                        class="voucher-note link"
                        @click="noteClick($event)"
                        :title="!voucherModel.note?.trim() ? '亲，可以在此录入备注哦！' : ''"
                        >备注</a
                    >
                    <a class="voucher-help" title="记账帮助" @click="globalWindowOpen(helplink)"></a>
                    <keep-alive>
                        <div class="voucher-zoom-btn" @click="changeZoomState(zoomState === 'in' ? 'out' : 'in')">
                            {{ zoomState === "in" ? "放大" : "缩小" }}
                        </div>
                    </keep-alive>
                    <div
                        v-if="!isLemonClient()"
                        :class="['voucher-zoom-screen-btn', fullScreen ? (isErp ? 'full-screen erp' : 'full-screen') : 'exit-full-screen']"
                        @click="fullScreen = !fullScreen"
                    >
                        <span> {{ !fullScreen ? "全屏" : "退出全屏" }}</span>
                    </div>
                </div>
            </div>
            <div class="voucher-table" :class="{ 'toolbar-hidden': !toolbarDisplay }">
                <div class="assit-col-line" ref="assitColLineRef"></div>
                <div :class="['voucher-title', { 'bottom-red': warningRowIndex === 1 }, { 'bottom-dragger': orderClickIndex === 0 ,'dragger-enter-border': dragEnterRowIndex === -100}]" @dragenter="handleDragEnter(-100)" @drop="handleDragEnd(-100)"
                        @dragover.prevent.stop="handleDragMove"
                      >
                    <div class="voucherline-order-number">序号</div>
                    <div class="adaptive-container" ref="adaptiveContainerRef">
                        <div
                            class="voucherline-description"
                            :style="{ 'min-width': descMinWidth ? descMinWidth : '' }"
                            ref="descriptionTitleRef"
                        >
                            摘要
                        </div>
                        <div class="draggable-line" v-draggable-c></div>
                        <div class="voucherline-asub" :style="{ 'min-width': asubMinWidth ? asubMinWidth : '' }">会计科目</div>
                    </div>
                    <div class="voucherline-amount">
                        <div class="voucherline-amount-title">借方金额</div>
                        <div class="voucher-amount-lines">
                            <span>亿</span>
                            <span>千</span>
                            <span>百</span>
                            <span>十</span>
                            <span>万</span>
                            <span>千</span>
                            <span>百</span>
                            <span>十</span>
                            <span>元</span>
                            <span>角</span>
                            <span>分</span>
                        </div>
                    </div>
                    <div class="voucherline-amount credit-amount">
                        <div class="voucherline-amount-title">贷方金额</div>
                        <div class="voucher-amount-lines">
                            <span>亿</span>
                            <span>千</span>
                            <span>百</span>
                            <span>十</span>
                            <span>万</span>
                            <span>千</span>
                            <span>百</span>
                            <span>十</span>
                            <span>元</span>
                            <span>角</span>
                            <span>分</span>
                        </div>
                    </div>
                </div>

                <div
                    class="voucherlines-container"
                    @click="
                        warningRowIndex = -1;
                        orderClickIndex = -1;
                        dragEnterRowIndex = -1;
                    "
                    @mouseup="dragEnterRowIndex = -1"
                >
                    <div
                        v-for="(voucherLine, index) in voucherModel.voucherLines"
                        :draggable="!isVoucherReadonly && !isFolkChangeoutVoucher && allowDrag && checkDisplay(index)"
                        :class="{
                            'error-border': warningRowIndex === index + 1,
                            'error-border-top': index === warningRowIndex - 2,
                            'dragger-border': orderClickIndex === index,
                            'dragger-border-top': orderClickIndex === index + 1,
                            'dragger-enter-border': dragEnterRowIndex === index,
                            'move-line': isDragging && currentDragTargetIndex === index,
                            erp: isErp,
                        }"
                        :data-line-number="index + 1"
                        @dragstart.stop="handleDragStart($event, voucherLine, index)"
                        @dragover.prevent.stop="handleDragMove"
                        @dragenter="handleDragEnter(index)"
                        @drop="handleDragEnd(index)"
                        :key="index"
                    >
                        <div class="voucherline" v-if="checkDisplay(index)">
                            <div class="voucherline-order-number" @click.stop="orderClickIndex = index">{{ index + 1 }}</div>
                            <div class="adaptive-container">
                                <div
                                    class="voucherline-description"
                                    :style="{ 'min-width': descMinWidth ? descMinWidth : '' }"
                                    :class="{
                                        editing: editingInfo.editingLine === voucherLine && editingInfo.editingType === 'description',
                                    }"
                                    @click.stop="voucherLineClick($event, 'description', voucherLine, index)"
                                >
                                    <div class="voucherline-display" :title="voucherLine.description">{{ voucherLine.description }}</div>
                                    <div class="voucherline-textarea">
                                        <textarea
                                            v-model="voucherLine.description"
                                            maxlength="256"
                                            @focus="descriptionFocus($event)"
                                            @click="descriptionClick()"
                                            @input="descriptionInput"
                                            @mousedown="
                                                $event.stopPropagation();
                                                allowDrag = false;
                                            "
                                            @keydown="descriptionKeydown($event)"
                                            @blur="allowDrag = true"
                                        ></textarea>
                                    </div>
                                </div>
                                <div
                                    class="voucherline-asub"
                                    :style="{ 'min-width': asubMinWidth ? asubMinWidth : '' }"
                                    :class="{
                                        editing: editingInfo.editingLine === voucherLine && editingInfo.editingType === 'asub',
                                        locked: voucherLine.asubId !== 0 && isFolkChangeoutVoucher,
                                    }"
                                    @click="voucherLineClick($event, 'asub', voucherLine, index)"
                                >
                                    <div
                                        class="voucherline-display"
                                        :style="{
                                            minHeight:
                                                'calc(100% - ' +
                                                ((voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                    (!!voucherLine.asubId ? 20 : 0)) +
                                                'px)',
                                            marginBottom:
                                                (voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                (!!voucherLine.asubId ? 20 : 0) +
                                                'px',
                                            '-webkit-line-clamp':
                                                6 -
                                                ((voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                    (!!voucherLine.asubId ? 20 : 0)) /
                                                    20,
                                        }"
                                        :title="voucherLine.asubName"
                                    >
                                        {{ voucherLine.asubName }}
                                    </div>
                                    <div class="voucherline-textarea">
                                        <textarea
                                            :style="{
                                                paddingBottom: (!!voucherLine.asubId ? 20 : 0) + 'px',
                                                '-webkit-line-clamp':
                                                    6 -
                                                    ((voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                        (!!voucherLine.asubId ? 20 : 0)) /
                                                        20,
                                            }"
                                            @focus="asubFocus($event, index)"
                                            @mousedown="
                                                $event.stopPropagation();
                                                allowDrag = false;
                                            "
                                            @keydown="asubKeydown($event)"
                                            @keyup="asubKeyup($event)"
                                            placeholder="请输入科目名称/科目编码/科目名称拼音首字母快速搜索"
                                            @blur="allowDrag = true"
                                        ></textarea>
                                    </div>
                                    <div class="voucherline-asub-flow">
                                        <div class="voucherline-asub-quantityaccounting" v-show="voucherLine.quantityAccounting === 1">
                                            <div
                                                class="voucherline-asub-quantity"
                                                :class="{
                                                    editing:
                                                        editingInfo.editingLine === voucherLine && editingInfo.editingType === 'quantity',
                                                }"
                                                @click="
                                                    $event.stopPropagation();
                                                    voucherLineClick($event, 'quantity', voucherLine, index);
                                                "
                                            >
                                                <span>数量：</span>
                                                <span class="voucherline-asub-display">{{
                                                    formatNumber(voucherLine.quantity, 8, "0")
                                                }}</span>
                                                <div class="voucherline-asub-input">
                                                    <input
                                                        type="text"
                                                        v-model="voucherLine.quantity"
                                                        :controls="false"
                                                        @blur="amountBlur()"
                                                        @keydown="quantityKeydown($event)"
                                                        @input="numberInput($event, 8)"
                                                    />
                                                </div>
                                                <img
                                                    src="@/assets/Voucher/setting.png"
                                                    class="cost-accounting-icon"
                                                    v-show="
                                                        editingInfo.editingLine === voucherLine &&
                                                        editingInfo.editingType === 'quantity' &&
                                                        canSetCostAccounting(voucherLine)
                                                    "
                                                    @mousedown="showCostAccountingSettingsDialog($event)"
                                                />
                                            </div>
                                            <div
                                                class="voucherline-asub-price"
                                                :class="{
                                                    editing: editingInfo.editingLine === voucherLine && editingInfo.editingType === 'price',
                                                }"
                                                style="margin-left: 5px; margin-right: 5px"
                                                @click="
                                                    $event.stopPropagation();
                                                    voucherLineClick($event, 'price', voucherLine, index);
                                                "
                                            >
                                                <span>单价：</span>
                                                <span class="voucherline-asub-display">{{ formatNumber(voucherLine.price, 8, "0") }}</span>
                                                <div class="voucherline-asub-input">
                                                    <input
                                                        type="text"
                                                        v-model="voucherLine.price"
                                                        :controls="false"
                                                        @blur="amountBlur()"
                                                        @focus="priceFocus()"
                                                        @keydown="priceKeydown($event)"
                                                        @input="numberInput($event, 8)"
                                                    />
                                                </div>
                                                <img
                                                    src="@/assets/Voucher/setting.png"
                                                    class="cost-accounting-icon"
                                                    v-show="
                                                        editingInfo.editingLine === voucherLine &&
                                                        editingInfo.editingType === 'price' &&
                                                        canSetCostAccounting(voucherLine)
                                                    "
                                                    @mousedown="showCostAccountingSettingsDialog($event)"
                                                />
                                            </div>
                                        </div>
                                        <div class="voucherline-asub-foreigncurrency" v-show="voucherLine.foreigncurrency === 1">
                                            <div class="voucherline-asub-fc mr-10" @click="$event.stopPropagation()">
                                                <select
                                                    v-model="voucherLine.fcId"
                                                    @click="
                                                        $event.stopPropagation();
                                                        voucherLineClick($event, 'fc', voucherLine, index);
                                                    "
                                                    @blur="cancelEdit()"
                                                    @change="fcChange(voucherLine)"
                                                    @keydown="fcKeydown($event)"
                                                    :disabled="
                                                        isVoucherReadonly || editingInfo.editingType === 'asub' || isFolkChangeoutVoucher
                                                    "
                                                >
                                                    <option v-for="fc in getFcList(voucherLine)" :key="fc.id" :value="fc.id">
                                                        {{ fc.code }}
                                                    </option>
                                                </select>
                                            </div>
                                            <div
                                                class="voucherline-asub-fcrate"
                                                :class="{
                                                    editing:
                                                        editingInfo.editingLine === voucherLine && editingInfo.editingType === 'fcRate',
                                                }"
                                                @click="
                                                    $event.stopPropagation();
                                                    voucherLineClick($event, 'fcRate', voucherLine, index);
                                                "
                                            >
                                                <span>汇率：</span>
                                                <span class="voucherline-asub-display">{{ formatNumber(voucherLine.fcRate, 8, "0") }}</span>
                                                <div class="voucherline-asub-input">
                                                    <input
                                                        type="text"
                                                        v-model="voucherLine.fcRate"
                                                        :controls="false"
                                                        @blur="amountBlur()"
                                                        @keydown="fcrateKeydown($event)"
                                                        @input="numberInput($event, 8)"
                                                    />
                                                </div>
                                            </div>
                                            <div
                                                class="voucherline-asub-fcamount"
                                                :class="{
                                                    editing:
                                                        editingInfo.editingLine === voucherLine && editingInfo.editingType === 'fcAmount',
                                                }"
                                                style="margin-left: 5px; margin-right: 5px"
                                                @click="
                                                    $event.stopPropagation();
                                                    voucherLineClick($event, 'fcAmount', voucherLine, index);
                                                "
                                            >
                                                <span>原币：</span>
                                                <span class="voucherline-asub-display">{{ Number(voucherLine.fcAmount).toFixed(2) }}</span>
                                                <div class="voucherline-asub-input">
                                                    <input
                                                        type="text"
                                                        v-model="voucherLine.fcAmount"
                                                        :controls="false"
                                                        @blur="amountBlur()"
                                                        @keydown="fcamountKeydown($event)"
                                                        @input="numberInput($event, 2)"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="voucherline-asub-amount"
                                        :class="{ negative: Number(getAsubTotalAmount(voucherLine)) < 0 }"
                                        v-show="!checkIsVoucherTemplate() && !!voucherLine.asubId"
                                        @click="asubAmountClick($event, voucherLine)"
                                    >
                                        <BubbleTip
                                            effect="dark"
                                            style="display: inline-block"
                                            :auto-close="5000"
                                            :bubble-width="186"
                                            :bubble-top="20"
                                            font-size="14px"
                                            :hide-popover="!amountPopoverVisible"
                                        >
                                            <template #content>
                                                <span style="color: #333" data-action="settleBalance">余额</span>
                                            </template>
                                            <template #tips>
                                                <div>
                                                    <div class="mb-10" style="font-weight: 400">点击余额可快速冲平哦~</div>
                                                    <div style="text-align: right; margin: 0">
                                                        <el-button
                                                            size="small"
                                                            style="color: #fff; background-color: #333"
                                                            @click="hideBalancePopover"
                                                            >我知道了</el-button
                                                        >
                                                    </div>
                                                </div>
                                            </template>
                                        </BubbleTip>
                                        ：<span data-action="gotoSubsidiaryLedger">{{ getAsubTotalAmount(voucherLine) }}</span>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="voucherline-amount"
                                :class="{
                                    editing: editingInfo.editingLine === voucherLine && editingInfo.editingType === 'debit',
                                    locked: voucherLine.asubId !== 0 && isFolkChangeoutVoucher,
                                }"
                                @click="voucherLineClick($event, 'debit', voucherLine, index)"
                            >
                                <div class="voucher-amount-lines" :class="{ negative: voucherLine.debit < 0 }">
                                    <span v-for="(n, i) in getAmountArrar(voucherLine.debit)" :key="i">{{ n }}</span>
                                </div>
                                <div class="voucherline-amount-input">
                                    <input
                                        type="text"
                                        @focus="amountFocus($event)"
                                        @blur="editAmount($event, 'debit', voucherLine)"
                                        @keydown="debitKeydown($event)"
                                        @input="amountInput($event)"
                                        @keyup="debitKeyup($event)"
                                        @paste="amountPaste($event)"
                                        @dblclick="amountDblclick($event)"
                                    />
                                </div>
                            </div>
                            <div
                                class="voucherline-amount credit-amount"
                                :class="{
                                    editing: editingInfo.editingLine === voucherLine && editingInfo.editingType === 'credit',
                                    locked: voucherLine.asubId !== 0 && isFolkChangeoutVoucher,
                                }"
                                @click="voucherLineClick($event, 'credit', voucherLine, index)"
                            >
                                <div class="voucher-amount-lines" :class="{ negative: voucherLine.credit < 0 }">
                                    <span v-for="(n, i) in getAmountArrar(voucherLine.credit)" :key="i">{{ n }}</span>
                                </div>
                                <div class="voucherline-amount-input">
                                    <input
                                        type="text"
                                        @focus="amountFocus($event)"
                                        @blur="editAmount($event, 'credit', voucherLine)"
                                        @keydown="creditKeydown($event)"
                                        @input="amountInput($event)"
                                        @keyup="creditKeyup($event)"
                                        @paste="amountPaste($event)"
                                        @dblclick="amountDblclick($event)"
                                    />
                                </div>
                            </div>
                            <div
                                class="voucherline-add-btn"
                                :class="{ erp: isErp }"
                                @click="addVoucherLine(index, 'add')"
                                v-show="!isVoucherReadonly && !isFolkChangeoutVoucher"
                            ></div>
                            <BubbleTip
                                v-if="!isVoucherReadonly && !isFolkChangeoutVoucher"
                                :class="['voucherline-copy-btn', { erp: isErp }]"
                                effect="light"
                                :bubble-width="120"
                                :bubble-top="20"
                                fontSize="14px"
                                content="点击复制本行"
                                :auto-close="3000"
                                :hide-popover="!copyRowPopoverVisible"
                                @hide="hideCopyPopover"
                                @click="addVoucherLine(index, 'copy')"
                            >
                            </BubbleTip>
                            <div
                                class="voucherline-del-btn"
                                :class="{ erp: isErp }"
                                @click="delVoucherLine(index)"
                                v-show="!isVoucherReadonly && !isFolkChangeoutVoucher"
                            ></div>
                        </div>
                        <div class="voucherline" v-else>
                            <div class="voucherline-order-number">{{ index + 1 }}</div>
                            <div class="adaptive-container">
                                <div class="voucherline-description" :style="{ 'min-width': descMinWidth ? descMinWidth : '' }">
                                    <div class="voucherline-display" :title="voucherLine.description">{{ voucherLine.description }}</div>
                                </div>
                                <div class="voucherline-asub" :style="{ 'min-width': asubMinWidth ? asubMinWidth : '' }">
                                    <div
                                        class="voucherline-display"
                                        :style="{
                                            minHeight:
                                                'calc(100% - ' +
                                                ((voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                    (!!voucherLine.asubId ? 20 : 0)) +
                                                'px)',
                                            marginBottom:
                                                (voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                (!!voucherLine.asubId ? 20 : 0) +
                                                'px',
                                            '-webkit-line-clamp':
                                                6 -
                                                ((voucherLine.quantityAccounting + voucherLine.foreigncurrency) * 20 +
                                                    (!!voucherLine.asubId ? 20 : 0)) /
                                                    20,
                                        }"
                                        :title="voucherLine.asubName"
                                    >
                                        {{ voucherLine.asubName }}
                                    </div>
                                </div>
                            </div>
                            <div class="voucherline-amount"></div>
                            <div class="voucherline-amount credit-amount"></div>
                        </div>
                    </div>
                </div>
                <div
                    :class="[
                        'voucherline voucher-total-line',
                        {
                            'error-last-row': warningRowIndex === voucherModel.voucherLines.length,
                            'dragger-last-row': orderClickIndex === voucherModel.voucherLines.length - 1,
                            'dragger-enter-last-row': dragEnterRowIndex === voucherModel.voucherLines.length - 1,
                        },
                    ]"
                >
                    <div class="voucherline-total">合计：{{ digitUppercase(getTotal("debit")) }}</div>
                    <div class="voucherline-amount">
                        <div class="voucher-amount-lines" :class="{ negative: getTotal('debit') < 0 }">
                            <span v-for="(n, i) in getAmountArrar(getTotal('debit'))" :key="i">{{ n }}</span>
                        </div>
                    </div>
                    <div class="voucherline-amount credit-amount">
                        <div class="voucher-amount-lines" :class="{ negative: getTotal('credit') < 0 }">
                            <span v-for="(n, i) in getAmountArrar(getTotal('credit'))" :key="i">{{ n }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="voucher-preparedby" v-show="toolbarDisplay">
                <div class="voucher-preparedby-item">
                    <span
                        v-show="!isAccountingAgent && !isErp && !isUserNameEditing && !isThirdPart"
                        class="icon mr-10"
                        @click="editUserName('preparedBy')"
                    ></span>
                    <span class="name">制单人：{{ isUserNameEditing ? "" : userName }}</span>
                    <el-input
                        v-model="editingUserName"
                        v-show="isUserNameEditing"
                        ref="editingUserNameRef"
                        @blur="submitUserName('preparedBy')"
                    ></el-input>
                </div>
                <div class="voucher-preparedby-item" v-show="checkNeeded || voucherModel.approveStatus === 1">
                    <span
                        v-show="!isAccountingAgent && !isErp && !isApprovedNameEditing && !isThirdPart"
                        class="icon mr-10"
                        @click="editUserName('approvedBy')"
                    ></span>
                    <span class="name">审核人：{{ isApprovedNameEditing ? "" : approvedName }}</span>
                    <el-input
                        v-model="editingApprovedName"
                        v-show="isApprovedNameEditing"
                        ref="editingApprovedNameRef"
                        @blur="submitUserName('approvedBy')"
                    ></el-input>
                </div>
            </div>
        </div>
        <div class="deslist-selector-container" v-show="desListSelectorDisplay" ref="desListSelector" @mousedown="$event.stopPropagation()">
            <div
                v-for="(des, index) in getDesList()"
                :key="index"
                :class="{ selected: desListSelectIndex === index }"
                @click="desListSelect(des.description)"
            >
                <Tooltip :content="des.description" :teleported="true" placement="right">
                    <div class="abstract-list">{{ des.description }}</div>
                </Tooltip>
            </div>
        </div>
        <div class="asub-selector-container" v-show="asubSelectorDisplay === DisplayStatus.Visible" ref="asubSelector">
            <div class="asub-selector" v-show="editingInfo.editingLine.assistingAccounting === 0" @mousedown="$event.stopPropagation()">
                <div class="asubtypes">
                    <div
                        class="asubtype"
                        :class="{ selected: asubType.selected }"
                        v-for="asubType in asubTypeList"
                        :key="asubType.id"
                        @click="selectAsubType(asubType.id)"
                    >
                        <!-- <img class="ai-suggestion" v-if="asubType.id === 9999" src="@/assets/Voucher/ai-suggestion.png"/> -->
                        <div>{{ asubType.name }}</div>
                    </div>
                </div>
                <div class="asubs-content">
                    <div
                        v-for="(asub, index) in getAsubList()"
                        :key="index"
                        :class="{ selected: asubSelectIndex === index }"
                        @click="selectAsub(asub)"
                    >
                        {{ asub.asubCode + " " + asub.asubAAName + " " }}
                        <span v-show="getAmount(asub.asubId) !== ''" style="display: inline-block">{{ getAmount(asub.asubId) }}</span>
                    </div>
                    <div v-if="getAsubList().length === 0">暂无可推荐科目</div>
                </div>
                <div class="add-btn link" v-permission="['accountsubject-canedit']" @click="addAccountSubject()">+ 点击添加</div>
            </div>
            <div
                class="asub-aa-selector"
                v-show="editingInfo.editingLine.assistingAccounting === 1"
                @mousedown="
                    $event.stopPropagation();
                    aaeBlur();
                "
            >
                <table class="aatype-content" cellspacing="0" paddingspace="0" style="width: 100%">
                    <tr v-for="(aatype, index) in asubAASelectorInfo" :key="index" class="aatype-tr">
                        <td style="max-width: 90px; height: auto; line-height: 16px">
                            {{ aatype.aaTypeName }}
                        </td>
                        <td class="input-td">
                            <Tooltip :content="aatype.input" :isInput="true" placement="right">
                                <el-input
                                    v-model="aatype.input"
                                    @focus="aaeFocus($event, aatype.aaType, index)"
                                    @input="aaeInput()"
                                    @keydown="aaeKeydown($event)"
                                    @mousedown="$event.stopPropagation()"
                                ></el-input>
                            </Tooltip>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div
            class="aae-selector-container"
            v-show="aaeSelectorInfo.aaeSelectorDisplay && editingInfo.editingLine.asubId !== 0"
            ref="aaeSelector"
            @mousedown="$event.stopPropagation()"
        >
            <div :class="{ aaes: true, filter: aaeSelectorInfo.searchInfo }">
                <template v-if="getAAEList().length > 0">
                    <div
                        v-for="(aae, index) in getAAEList()"
                        :key="index"
                        :class="{ selected: aaeSelectorInfo.selectIndex === index }"
                        @click="selectAAE(aae.id, aae.name)"
                    >
                        {{ aae.name }}
                        <template v-if="aae.model?.stockModel || aae.model?.unit">
                            &nbsp;{{ aae.model?.stockModel ? aae.model?.stockModel : "" }}&nbsp;&nbsp;{{ aae.model?.unit }}
                        </template>
                        <span v-show="asubAASelectorInfo.length === 1 && getAaeAmount(aae.id) !== ''" style="display: inline-block">{{
                            asubAASelectorInfo.length > 1 ? "" : " " + getAaeAmount(aae.id)
                        }}</span>
                    </div>
                </template>
                <template v-else>
                    <div class="no-data">暂无数据</div>
                </template>
            </div>
            <div
                class="add-btn"
                @click="showAaeAddDialog(aaeSelectorInfo.aaType)"
                v-show="
                    hasPermissionAccounting(aaeSelectorInfo.aaType) &&
                    (aaeSelectorInfo.aaType !== 10007 || accountingStandard === 4 || accountingStandard === 5)
                "
            >
                添加辅助核算项目
            </div>
        </div>
        <div class="note-container" v-show="noteDialogDisplay" ref="noteDialog" @mousedown="$event.stopPropagation()">
            <textarea
                v-model="voucherModel.note"
                placeholder="请输入备注内容！"
                maxlength="256"
                @blur="noteDialogDisplay = false"
                :readonly="isVoucherReadonly"
                @input="noteInput"
                @keydown="preventWrap"
            ></textarea>
        </div>
        <el-dialog
            v-model="faConfrimDialogInfo.display"
            title="提示"
            center
            width="438px"
            :show-close="false"
            :class="['dialogDrag', isErp ? 'custom-confirm' : '']"
        >
            <div class="fa-confirm-container" v-dialogDrag>
                <div class="fa-confirm-main" style="padding:20px 38px">
                    <div class="txt">亲，当前期间启用了资产模块，手工修改或录入资产凭证，将导致资产模块数据与总账资产数据对账不平，对账不平不影响总账结账</div>
                    <div class="txt mt-10">但为保证数据一致与准确，建议通过资产模块生成凭证哦~</div>
                    <div class="txt mt-10">
                        <img src="@/assets/Icons/warn.png" style="width:16px; height:16px; position: relative; top: 3px; left: -4px;"/>
                        对账不平原因：凭证未与资产建立关联、金额不一致等
                    </div>
                    <div class="mt-20 ml-5">
                        <el-checkbox v-model="faConfrimDialogInfo.neverAlert" label="不再提示"></el-checkbox>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="faConfrimDialogInfo.callback(true)">确定</a>
                    <a class="button ml-10" @click="faConfrimDialogInfo.callback(false)">取消</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            v-model="expenseACreditDialogInfo.display"
            title="提示"
            center
            width="510px"
            :class="['dialogDrag', {'custom-confirm': isErp }]"
            :show-close="false"
        >
            <div class="fa-confirm-container" v-dialogDrag>
                <div class="close" @click="expenseACreditDialogInfo.callback(ProfitAmountOperation.Close)">
                    <el-icon><Close /></el-icon>
                </div>
                <div class="fa-confirm-main reverse" style="padding: 20px">
                    <div class="txt b">系统检测到当前凭证存在损益科目录入不规范的凭证行哦~</div>
                    <div class="txt mt-10">
                        <span class="highlight-orange">收入类</span>要做在 <span class="highlight-orange">贷方</span>，
                        <span class="highlight-orange">费用类</span>要做在 <span class="highlight-orange">借方</span>。
                        如果要冲回，应该录入相同方向的红字，即负数。否则科目余额表和利润表数据会对不上
                    </div>
                    <div class="txt">需要调整金额方向的凭证行：{{ showErrAsubLines() }}</div>
                    <div class="tip-content">
                        <div class="txt">
                            <span>选择批量调整，系统帮您自动调整所有金额方向；</span><br />
                            <span>自动调整后请确认凭证无误后重新保存</span>
                        </div>
                        <div class="txt mt-10">选择继续保存，则直接保存凭证</div>
                        <div class="txt mt-10">关闭弹窗，自行修改凭证后重新保存</div>
                    </div>
                    <div class="bug-link">具体可点击<a class="link" @click="toHelp">常见问题</a>了解</div>
                </div>
                <div class="buttons">
                    <a class="button mr-10" @click="expenseACreditDialogInfo.callback(ProfitAmountOperation.Save)">继续保存</a>
                    <a class="button solid-button" @click="expenseACreditDialogInfo.callback(ProfitAmountOperation.Adjust)">批量调整</a>
                </div>
            </div>
        </el-dialog>
        <div @mousedown="$event.stopPropagation()">
            <AddAccountSubjectDialog ref="accountSubjectAddDialog" @save-success="addAccountSubjectSuccess"> </AddAccountSubjectDialog>
        </div>
        <div @mousedown="$event.stopPropagation()">
            <AddAssistingAccountingEntryDialog
                :title="AddAssistingAccountingTitle"
                ref="aaeAddDialog"
                @save-success="addAssistingAccountingSuccess"
                @close="addAssistingAccountingClose"
            >
            </AddAssistingAccountingEntryDialog>
        </div>
        <UploadFileDialog ref="uploadFileDialogRef" @save="saveAttachfiles" :readonly="isVoucherReadonly" />
        <div v-show="costAccountingSettingsDialogInfo.display" class="cost-accounting-settings-dialog">
            <div class="dialog-shadow" @click="costAccountingSettingsDialogInfo.display = false"></div>
            <div class="cost-accounting-settings-container" ref="costAccountingSettingsContainer">
                <div class="triangle-container">
                    <div class="triangle-outer"></div>
                    <div class="triangle-inner"></div>
                </div>
                <div class="cost-accounting-settings-content">
                    <div class="dialog-title">
                        自动结转成本设置
                        <a
                            class="link mr-20"
                            v-show="!isErp && !isHideBarcode"
                            @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/project?subMenuId=*********')"
                            >点此查看帮助</a
                        >
                    </div>
                    <div class="form-item">
                        <div class="input-title">出库成本核算：</div>
                        <div class="input-field">
                            <el-radio-group v-model="costAccountingSettingsDialogInfo.useCostAccounting">
                                <el-radio :label="true">启用</el-radio>
                                <el-radio :label="false">不启用</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="form-item" v-show="costAccountingSettingsDialogInfo.useCostAccounting">
                        <div class="input-title">核算方法：</div>
                        <div class="input-field">
                            <el-radio-group v-model="costAccountingSettingsDialogInfo.costAccountingSetting">
                                <el-radio :label="1">移动加权法</el-radio>
                                <el-radio :label="2">先进先出法</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" @click="saveCostAccountingSetting()">确定</a>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog 
            v-model="amountCalcInfo.display" 
            class="custom-confirm dialogDrag" 
            title="四则运算" 
            center 
            width="600px"
        >
            <div class="amount-calculator-container" v-dialogDrag>
                <div class="calculator-example">计算 [例如：5.99+(10-8.8)*5]</div>
                <div class="calculator-content">
                    <textarea
                        v-model="amountCalcInfo.calculator"
                        ref="amountCalculator"
                        @keypress.enter="confirmCalculatResult()"
                    ></textarea>
                </div>
                <div class="calculat-result">
                    <div class="tip">亲，按回车键可以快速保存计算结果哦！</div>
                    <div class="result-content">
                        计算结果：<span>{{ amountCalcResult }}</span>
                    </div>
                </div>
                <div class="buttons" :style="isErp ? 'display: flex; justify-content: flex-end' : ''">
                    <a class="button" @click="amountCalcInfo.display = false">取消</a>
                    <a class="button solid-button ml-10" @click="confirmCalculatResult()">确定</a>
                </div>
            </div>
        </el-dialog>
        <el-dialog
            v-model="checkoutCompanySubmitDialogShow"
            title="提示"
            center
            width="482px"
            :draggable="false"
            :class="isErp ? 'custom-confirm' : ''"
        >
            <div class="fa-confirm-container">
                <div class="fa-confirm-main">
                    结转损益凭证中除了“4103 本年利润”、“{{ profitDistributionUndistributedProfitCode }} 利润分配-未分配利润”和损益类科目之外，不允许使用其他科目，请修改。<div class="mt-10" style="text-align:left;"><img src="@/assets/Icons/warn.png" style="width:16px; height:16px; position: relative; top: 3px; left: -4px;"/>结转“6901 以前年度损益调整”时，才可以使用“{{ profitDistributionUndistributedProfitCode }} 利润分配-未分配利润”哦~</div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="checkoutCompanySubmitDialogShow=false">确定</a>
                </div>
            </div>
        </el-dialog>
        <div
            v-show="amountZoomOutInfo.display"
            class="amount-zoomout-container"
            ref="amountZoomOutContainer"
            :class="{ 'twinkle-before': amountZoomOutInfo.twinkleBefore }"
        >
            <span
                v-for="(item, index) in amountZoomOutInfo.amountSpanArray()"
                :key="index"
                :class="{ highlight: item.highlight, twinkle: item.twinkle }"
                >{{ item.num }}</span
            >
        </div>
        <div v-show="amountZoomOutInfo.tipDisplay" class="amount-tip-container" ref="amountTipContainer">
            <span class="allow"></span>
            当凭证行没有录入金额时，点击Enter键可以快速切换借贷方向哦~
        </div>
    </div>
</template>
<style lang="less" scoped>
@import "@/style/voucher/voucher.less";
</style>
<script setup lang="ts">
import { nextTick, onActivated, ref, watch, onDeactivated, onMounted, onBeforeUnmount } from "vue";
import {
    VoucherEntryModel,
    VoucherModel,
    VoucherQueryParams,
    EditVoucherQueryParams,
    NewVoucherQueryParams,
    NewVoucherTemplateQueryParams,
    EditVoucherTemplateQueryParams,
    InsertVoucherQueryParams,
    CopyVoucherQueryParams,
    OffsetVoucherQueryParams,
    LoadVoucherTemplateQueryParams,
    DataVoucherQueryParams,
    LoadVoucherDraftQueryParams,
    LoadVoucherRecyclebintQueryParams,
    RecyclebinVoucherQueryParams,
    type IVoucherDescriptionModel,
    VoucherSettingsModel,
    VoucherLineSaveModel,
    VoucherSaveModel,
    type IVoucherSaveResultModel,
    VoucherSaveParams,
    type BaseVoucherSaveParams,
    VoucherTemplateSaveParams,
    VoucherDraftSaveParams,
    DraftSaveParams,
    VoucherTemplateModel,
    type CurrencyModel,
    VoucherAttachFileModel,
    VoucherSwitchInfoModel,
    DisplayStatus,
} from "@/components/Voucher/types";
import {
    VoucherTemplateSaveModel,
    VoucherTemplateLineSaveModel,
    createVoucherTemplate,
    updateVoucherTemplate,
} from "@/api/voucherTemplate";
import { request, type IResponseModel } from "@/util/service";
import { reactive, toRef, inject } from "vue";
import type { Ref } from "vue";
import { ElNotify } from "@/util/notify";
import type { IAccountSubjectModel } from "@/api/accountSubject.js";
import { AsubTypeEnum } from "@/views/Settings/AccountSubject/types";
import { getCookie, setCookie } from "@/util/cookie";
import { getGlobalToken } from "@/util/baseInfo";
import { ElConfirm, ElAlert } from "@/util/confirm";
import AddAccountSubjectDialog from "@/components/AddAccountSubjectDialog/index.vue";
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { AccountStandard, useAccountSetStore, AccountSubjectType } from "@/store/modules/accountset";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import type { IFileInfo } from "../UploadFileDialog/types";
import { computed } from "vue";
import { useUserStore } from "@/store/modules/user";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useVoucherSettingsStore } from "@/store/modules/voucherSettings";
import { PeriodStatus } from "@/api/period";
import { globalWindowOpen, globalWindowOpenPage, loadTopUserName } from "@/util/url";
import { digitUppercase, formatMoneyWithZero, getMonthStart, getMonthEnd, formatNumber } from "@/util/format";
import { isInWxWork } from "@/util/wxwork";
import { useVoucherGroupStoreHook } from "@/store/modules/voucherGroup";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { watchEffect } from "vue";
import { getScrollTop, setScrollTop } from "@/util/common";
import { useRoute } from "vue-router";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import Tooltip from "@/components/Tooltip/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { checkPermission } from "@/util/permission";
import { isLemonClient } from "@/util/lmClient";
import BubbleTip from "@/components/BubbleTip/index.vue";
import VoucherButton from "@/components/VoucherButton/index.vue";
import { ElInputNumber } from "element-plus";
import { HistoryStack, reloadAsubAmountEventKey } from "./utils";
import { createWorker } from "tesseract.js";
import { handleCheckHasDialog } from "@/views/Voucher/NewVoucher/utils";
import { AttachFileCategory } from "@/views/ERecord/types";
import { showDeleteBillOrVoucherConfirm } from "../UploadFileDialog/utils";
import type { IAssistingAccount } from "@/api/assistingAccounting";
import { useAsubCodeLengthStore } from "@/store/modules/asubCodeLength";
import { useLoading } from "@/hooks/useLoading";
import { useCurrencyStore } from "@/store/modules/currencyList";

const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const route = useRoute();
const fullScreen = toRef(useFullScreenStore(), "fullScreen");
const _ = getGlobalLodash();
const specialAsubCodeDict: any = {
    1: {
        yearNetIncomeAsub: "3103",
        fixedAssetAsub: "1601",
        depreciationAsub: "1602",
        intangibleAsub: "1701",
        accumulatedAmortizationAsub: "1702",
        longTermDeferredExpensesAsub: "1801",
    },
    2: {
        yearNetIncomeAsub: "4103",
        fixedAssetAsub: "1601",
        depreciationAsub: "1602",
        intangibleAsub: "1701",
        accumulatedAmortizationAsub: "1702",
        longTermDeferredExpensesAsub: "1801",
    },
    3: {
        yearNetIncomeAsub: "",
        fixedAssetAsub: "1501",
        depreciationAsub: "1502",
        intangibleAsub: "1601",
        accumulatedAmortizationAsub: "",
        longTermDeferredExpensesAsub: "1301",
    },
    4: {
        yearNetIncomeAsub: "331",
        fixedAssetAsub: "151",
        depreciationAsub: "152",
        intangibleAsub: "161",
        accumulatedAmortizationAsub: "",
        longTermDeferredExpensesAsub: "171",
    },
    5: {
        yearNetIncomeAsub: "331",
        fixedAssetAsub: "151",
        depreciationAsub: "152",
        intangibleAsub: "161",
        accumulatedAmortizationAsub: "",
        longTermDeferredExpensesAsub: "171",
    },
    6: {
        yearNetIncomeAsub: "",
        fixedAssetAsub: "162",
        depreciationAsub: "163",
        intangibleAsub: "",
        accumulatedAmortizationAsub: "172",
        longTermDeferredExpensesAsub: "",
    },
    7: {
        yearNetIncomeAsub: "321",
        fixedAssetAsub: "151",
        depreciationAsub: "152",
        intangibleAsub: "",
        accumulatedAmortizationAsub: "162",
        longTermDeferredExpensesAsub: "",
    },
};

const props = defineProps<{
    queryParams: VoucherQueryParams | undefined;
    getDisabledDate?: Function | undefined;
    isInputAccout?: boolean;

    voucherTitle?: string | undefined;
    edited?: boolean; //是否编辑状态
    newVoucher?: Function | undefined;
    loadVoucherTemplate?: Function | undefined;
    loadVoucherDraft?: Function | undefined;
    showCancel?: boolean;
    cancelTxt?: string;
    isBottomBtn?: boolean; //按钮是否在顶部 默认顶部
    showKeysTip?: boolean; //是否显示快捷键提示
    showSwitchBtn?: boolean; //是否显示切换上下一张按钮
    switchInfo?: VoucherSwitchInfoModel | undefined;
    hiddenLine?: boolean; //隐藏按钮和凭证之间的线
    hiddenButton?: boolean; //隐藏按钮
    isGenerateVoucherPage?: boolean; //是否是生成凭证中间页页面
    modulePermission?: string; //模块权限
    from?: string; //来源
}>();

const emit = defineEmits<{
    (e: "zoom", zoomState: "in" | "out"): void;
    (e: "loadSuccess", voucherModel: VoucherModel): void;
    (e: "voucherChanged", voucherReadonly: boolean): void;
    (e: "voucherVgIdChanged", vgId: number): void;
    (e: "resetVDateAndVNum"): void;

    (e: "update:queryParams", VoucherQueryParams: VoucherQueryParams): void;
    (e: "save"): void;
    (e: "saveAndNew"): void;
    (e: "back"): void;
    (e: "deleteVoucher"): void;
    (e: "preVoucher"): void;
    (e: "nextVoucher"): void;
}>();
const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart);
const isErp = ref(window.isErp);
const containerRef = ref();
const voucherContainer = ref();
const voucherModel = ref(new VoucherModel());
const oldVoucherModel = ref(new VoucherModel());
const isVoucherReadonly = computed(() => voucherModel.value.approveStatus === 1 || voucherModel.value.pstatus === 3);
const isFolkChangeoutVoucher = computed(
    () => (voucherModel.value.vtype === 400 || voucherModel.value.vtype === 402) && route.fullPath.indexOf("VoucherList") === -1
);
const isTempVoucher = ref(false);
const editingInfo = reactive<{
    editingType: string;
    editingLine: VoucherEntryModel;
    editingTr: any;
    editingIndex: number;
}>({
    editingType: "",
    editingLine: new VoucherEntryModel(),
    editingTr: null,
    editingIndex: -1,
});
const zoomState = ref<"in" | "out">("in");
const accountSubjectStore = useAccountSubjectStore();
const voucherSettingsStore = useVoucherSettingsStore();
const voucherSettings = computed(() => voucherSettingsStore.voucherSettings ?? new VoucherSettingsModel());
const userStore = useUserStore();
const currentUserName = toRef(userStore, "userName");
const userName = computed(() => {
    if (tempUserName.value) {
        return tempUserName.value;
    } else {
        return currentUserName.value;
    }
});
const approvedName = ref("");
const accountSet = toRef(useAccountSetStore(), "accountSet");
const checkNeeded = computed(() => accountSet.value!.checkNeeded === 1);
const tempUserName = ref("");
const editingUserName = ref("");
const isUserNameEditing = ref(false);
const editingUserNameRef = ref();
const editingApprovedName = ref("");
const isApprovedNameEditing = ref(false);
const editingApprovedNameRef = ref();
const isAccountingAgent = computed(() => window.isAccountingAgent);
const toolbarDisplay = ref(true);
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
let desList = new Array<IVoucherDescriptionModel>();
const desListSelectorDisplay = ref(false);
const desListSearchInfo = ref("");
const desListSelector = ref();
const desListSelectIndex = ref(-1);
const accountSubjectAddDialog = ref<InstanceType<typeof AddAccountSubjectDialog>>();
const aaeAddDialog = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
const aaeAADialogDisplay = ref(false);
const asubList = ref(new Array<{ asubType: number; asubList: Array<IAccountSubjectModel> }>());
let asubDict: any = {};
let tempAsubList: Array<{ asubCode: string; asubName: string }> = [];
const asubAmountDict = ref<any>({});
const asubTypeList = ref(new Array<{ id: number; name: string; selected: boolean }>());
const asubSearchInfo = ref("");
const asubSelectorDisplay = ref(DisplayStatus.Hidden);
const asubSelector = ref();
const asubSelectIndex = ref(-1);
const asubAASelectorInfo = ref(
    new Array<{
        aaType: number;
        aaTypeName: string;
        allowNull: boolean;
        aaeId: number;
        input: string;
    }>()
);
const aaeSelectorInfo = reactive<{
    aaeSelectorDisplay: boolean;
    aaType: number;
    index: number;
    searchInfo: string;
    selectIndex: number;
}>({
    aaeSelectorDisplay: false,
    aaType: 0,
    index: 0,
    searchInfo: "",
    selectIndex: -1,
});
const addAAEIndex = ref(0);
const aaeSelector = ref();
const aaeGroup = ref<any>();
let aaeDict: any = {};
let tempAAEList: Array<{ aatype: number; num: string; name: string }> = [];
const currencyList = ref(new Array<CurrencyModel>());
let currencyDict: any = {};
const noteDialogDisplay = ref(false);
const noteDialog = ref();
const faConfrimDialogInfo = reactive<{
    display: boolean;
    neverAlert: boolean;
    callback: (r: boolean) => void;
}>({
    display: false,
    neverAlert: false,
    callback: () => {},
});
enum ProfitAmountOperation {
    Save = 1,
    Adjust = 2,
    Close = 3,
}
const expenseACreditDialogInfo = reactive<{
    display: boolean;
    numberRows: Array<number>;
    asubName: string;
    callback: (r: ProfitAmountOperation) => void;
}>({
    display: false,
    numberRows: [],
    asubName: "",
    callback: () => {},
});
const costAccountingSettingsDialogInfo = reactive<{
    display: boolean;
    useCostAccounting: boolean;
    costAccountingSetting: number;
}>({
    display: false,
    useCostAccounting: false,
    costAccountingSetting: 0,
});
const costAccountingSettingsContainer = ref();
const accountsetStore = useAccountSetStore();
const accountingStandard = ref(Number(accountsetStore.accountSet?.accountingStandard));
const activeFixedModel = accountsetStore.accountSet?.fixedasset;
const activeIaModel = accountsetStore.accountSet?.intangibleAsset;
const voucherGroupStore = useVoucherGroupStoreHook();
const voucherGroupList = toRef(voucherGroupStore, "voucherGroupList");
const amountPopoverVisible = ref(
    !(localStorage.getItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-voucher-balance-popover-show") === "false") &&
        !isVoucherReadonly.value
);
const hideBalancePopover = () => {
    localStorage.setItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-voucher-balance-popover-show", String(false));
    amountPopoverVisible.value = false;
};
const copyRowPopoverVisible = ref(
    !(localStorage.getItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-voucher-copy-popover-show") === "false")
);
const hideCopyPopover = () => {
    localStorage.setItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-voucher-copy-popover-show", String(false));
    copyRowPopoverVisible.value = false;
};
const assitColLineRef = ref();
const descriptionTitleRef = ref();
const adaptiveContainerRef = ref();
const descMinWidth = ref("");
const asubMinWidth = ref("");
const vDraggableC = {
    mounted: (el: any) => {
        let totalWidth = 0;
        el.addEventListener("mousedown", (e: MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();
            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", handleMouseUp);
            totalWidth = adaptiveContainerRef.value.offsetWidth;
        });

        function handleMouseMove(e: MouseEvent) {
            e.preventDefault();
            const left = el.offsetLeft + e.clientX - el.getBoundingClientRect().left;
            assitColLineRef.value.style.left = Math.max(Math.min(left, totalWidth - 352 / 2), 135) + "px";
            assitColLineRef.value.style.display = "block";
            assitColLineRef.value.style.zIndex = "999";
        }

        function handleMouseUp(e: MouseEvent) {
            e.preventDefault();
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            let descriptionWidth = Math.max(
                Math.min(descriptionTitleRef.value.offsetWidth + e.clientX - el.getBoundingClientRect().left, totalWidth - 352 / 2),
                100
            );
            descMinWidth.value = (descriptionWidth / totalWidth) * 100 + "%";
            asubMinWidth.value = (1 - descriptionWidth / totalWidth) * 100 + "%";
            assitColLineRef.value.style.display = "none";

            if (desListSelectorDisplay.value) {
                nextTick(() => {
                    calcDesListSelectorPosition();
                });
            }

            nextTick(() => {
                if (asubSelectorDisplay.value === DisplayStatus.Visible) {
                    calcAsubSelectorPosition();
                }
            });
        }
    },
};
const helplink = computed(() => {
    switch (accountingStandard.value) {
        case 1:
            return "https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/LittleCompanyStandard.pdf";
        case 2:
            return "https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/NormalCompanyStandard.pdf";
        case 3:
            return "https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/FolkCompanyStandard.pdf";
        case 4:
        case 5:
            return "https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/FarmerCooperativeStandard.pdf";
        case 6:
            return "https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/UnionStandard.pdf";
        case 7:
            return "https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/VillageCollectiveEconomyStandard.pdf";
        default:
            return "";
    }
});
const periodStore = useAccountPeriodStore();
const accountSetMinDate = new Date(
    periodStore.periodList
        .sort((p1, p2) => (p1.pid > p2.pid ? 1 : -1))
        .find((p) => p.status === PeriodStatus.HasVoucher || p.status === PeriodStatus.ChangeOut)?.startDate || ""
);
const amountCalcInfo = reactive<{
    display: boolean;
    calculator: string;
    editingInfo: {
        editingType: string;
        editingLine: VoucherEntryModel;
        editingTr: any;
    };
}>({
    display: false,
    calculator: "",
    editingInfo: {
        editingType: "",
        editingLine: new VoucherEntryModel(),
        editingTr: null,
    },
});
const amountCalcResult = computed(() => {
    const pattern = /(\d+(\.\d+)?)/g;
    const evaluatedExpression = amountCalcInfo.calculator.replace(pattern, (match, number) => {
        //10
        const parsedNumber = parseFloat(number);
        return parsedNumber.toString();
    });
    try {
        const calcuText = evaluatedExpression
            .replace("（", "(")
            .replace("【", "(")
            .replace("[", "(")
            .replace("{", "(")
            .replace("）", ")")
            .replace("】", ")")
            .replace("]", ")")
            .replace("}", ")");
        return _.round((Number(eval(calcuText)) || 0),2);
    } catch {
        return "0.00";
    }
});
const amountCalculator = ref();
const amountZoomOutInfo = reactive<{
    display: boolean;
    content: string;
    highlightIndex: number;
    amountSpanArray: () => Array<{ num: string; highlight: boolean; twinkle: boolean }>;
    twinkleIndex: number;
    twinkleBefore: boolean;
    tipDisplay: boolean;
}>({
    display: false,
    content: "",
    highlightIndex: -1,
    amountSpanArray: () => {
        const result = new Array<{ num: string; highlight: boolean; twinkle: boolean }>();
        let amount = amountZoomOutInfo.content;
        amount = formatMoneyWithZero(amount);
        for (let i = 0, index = 0; i < amount.length; i++, index++) {
            if (amount[i] === ",") {
                index--;
            }
            result.push({
                num: amount[i],
                highlight: index === amountZoomOutInfo.highlightIndex && amount[i] !== ",",
                twinkle: index === amountZoomOutInfo.twinkleIndex && amount[i] !== ",",
            });
        }
        return result;
    },
    twinkleIndex: -1,
    twinkleBefore: false,
    tipDisplay: false,
});
const amountZoomOutContainer = ref();
const amountTipContainer = ref();
const voucherlineMinheight = computed(() => 70);
const voucherlinePageSize = computed(() => 20);
const trialStatusStore = useTrialStatusStore();
const creditTipKey = "voucherCreditAmountHasTip-" + getGlobalToken();
function showVoucherTip() {
    amountZoomOutInfo.tipDisplay = true;
    const containerOffset = containerRef.value.getBoundingClientRect();
    const input = editingInfo.editingTr.querySelector(".voucherline-amount.credit-amount");
    const offset = input.getBoundingClientRect();
    amountTipContainer.value.style.left = offset.left - containerOffset.left - 20 + "px";
    amountTipContainer.value.style.top = offset.top + 75 - containerOffset.top + "px";
}
let creditTipTimer: any;
watch(
    () => amountZoomOutInfo.tipDisplay,
    (display) => {
        if (!display) {
            clearTimeout(creditTipTimer);
            localStorage.getItem(creditTipKey) !== "true" && localStorage.setItem(creditTipKey, "true");
        } else {
            creditTipTimer = setTimeout(() => {
                if (amountZoomOutInfo.tipDisplay) amountZoomOutInfo.tipDisplay = false;
                clearTimeout(creditTipTimer);
            }, 5000);
        }
    }
);

let voucherInited = false;
Promise.all([loadAAE()]).then(() => {
    voucherInited = true;
    loadVoucher();
});

async function getVoucherCurrencyList() {
    await useCurrencyStore().getCurrencyList();
    currencyList.value = useCurrencyStore().fcList;
    useCurrencyStore().fcList.forEach((f) => {
        currencyDict[f.id] = f;
    });
}
getVoucherCurrencyList();

function getVoucherDescription() {
    request({
        url: "/api/VoucherDescription/List",
        method: "get",
    }).then((res: IResponseModel<Array<IVoucherDescriptionModel>>) => {
        if (res.state === 1000) {
            desList = res.data;
        }
    });
}
getVoucherDescription();

let isNeedSaveToOther = false;
function loadVoucher() {
    if (!voucherInited) {
        return;
    }
    asubBlurExecute();
    tempAsubList = [];
    tempAAEList = [];
    lastVDate.value = "2000-01-01";
    approvedName.value = "";
    appendMargin.value = 0;
    isNeedSaveToOther = false;
    refreshVoucherCache();
    if (props.queryParams instanceof NewVoucherQueryParams) {
        const queryParams = props.queryParams as NewVoucherQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        voucherModel.value.vgId = queryParams.vgId || voucherGroupList.value.filter((vg) => vg.isDefault)[0].id;
        voucherModel.value.vnum = queryParams.vnum;
        voucherModel.value.attachFileIds = queryParams.attachFileIds;
        voucherModel.value.attachFiles = queryParams.attachFiles;
        if (voucherModel.value.attachFiles.length > 0) {
            voucherModel.value.attachments = voucherModel.value.attachFiles.length;
        }
        if (queryParams.lastVoucherDate !== "") {
            voucherModel.value.vdate = queryParams.lastVoucherDate;
        }

        resetVDateAndVNum(() => {
            checkAllVoucherLines();
            nextTick(() => {
                voucherLoadSuccess();
            });
        });
    }
    if (props.queryParams instanceof EditVoucherQueryParams) {
        const queryParams = props.queryParams as EditVoucherQueryParams;
        toolbarDisplay.value = true;
        request({
            url: `/api/Voucher?pId=${queryParams.pId}&vId=${queryParams.vId}`,
            method: "get",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value = res.data;
                lastVDate.value = voucherModel.value.vdate;
                oldVoucherModel.value = JSON.parse(JSON.stringify(res.data));
                tempUserName.value = res.data.preparedBy;
                approvedName.value = res.data.approvedBy;
                handleGetAllTotalAmount();
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            }
            // 如果请求到凭证不存在或者别的什么原因失败了，让页面可以正常显示出凭证号和凭证日期
            else {
                voucherModel.value.vgId = voucherGroupList.value[0].id;
                resetVDateAndVNum();
            }
        });
    }
    if (props.queryParams instanceof CopyVoucherQueryParams) {
        const queryParams = props.queryParams as CopyVoucherQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        voucherModel.value.vgId = voucherGroupList.value.filter((vg) => vg.isDefault)[0].id;

        resetVDateAndVNum(() => {
            request({
                url: `/api/Voucher?pId=${queryParams.pId}&vId=${queryParams.vId}`,
                method: "get",
            }).then((res: IResponseModel<VoucherModel>) => {
                if (res.state === 1000) {
                    voucherModel.value.voucherLines = res.data.voucherLines;
                }
                request({
                    url: `/api/Voucher/WithoutVoucherEntry?pId=${queryParams.pId}&vId=${queryParams.vId}`,
                    method: "get",
                }).then((res: IResponseModel<VoucherModel>) => {
                    if (res.state === 1000) {
                        voucherModel.value.note = res.data.note;
                        tempUserName.value = res.data.preparedBy;
                        approvedName.value = res.data.approvedBy;
                        checkAllVoucherLines();
                        nextTick(() => {
                            voucherLoadSuccess();
                        });
                    }
                });
            });
        });
    }
    if (props.queryParams instanceof InsertVoucherQueryParams) {
        const queryParams = props.queryParams as InsertVoucherQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        request({
            url: `/api/Voucher/WithoutVoucherEntry?pId=${queryParams.pId}&vId=${queryParams.vId}`,
            method: "get",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value.vgId = res.data.vgId;
                voucherModel.value.vdate = res.data.vdate;
                lastVDate.value = voucherModel.value.vdate;
                voucherModel.value.vnum = res.data.vnum;
                tempUserName.value = res.data.preparedBy;
                approvedName.value = res.data.approvedBy;
                handleGetAllTotalAmount();
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            }
        });
    }
    if (props.queryParams instanceof RecyclebinVoucherQueryParams) {
        const queryParams = props.queryParams as RecyclebinVoucherQueryParams;
        toolbarDisplay.value = true;
        request({
            url: `/api/Voucher/GetVoucherFromVoucherRecyclebin?rId=${queryParams.rId}`,
            method: "post",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value = res.data;
                oldVoucherModel.value = JSON.parse(JSON.stringify(res.data));
                checkAllVoucherLines();
                if (voucherModel.value.vnum) {
                    voucherModel.value.vnum = res.data.vnum;
                    handleGetAllTotalAmount();
                    voucherLoadSuccess();
                } else {
                    resetVDateAndVNum(() => {
                        nextTick(() => {
                            voucherLoadSuccess();
                        });
                    });
                }
            }
        });
    }
    if (props.queryParams instanceof OffsetVoucherQueryParams) {
        const queryParams = props.queryParams as OffsetVoucherQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        request({
            url: `/api/Voucher/WithoutVoucherEntry?pId=${queryParams.pId}&vId=${queryParams.vId}`,
            method: "get",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value.vgId = res.data.vgId;
                tempUserName.value = "";
                request({
                    url: `/api/Voucher/GetOffsetVoucherEntryList?pId=${queryParams.pId}&vId=${queryParams.vId}`,
                    method: "post",
                }).then((res: IResponseModel<Array<VoucherEntryModel>>) => {
                    if (res.state === 1000) {
                        voucherModel.value.voucherLines = res.data;
                        checkAllVoucherLines();
                        resetVDateAndVNum(() => {
                            nextTick(() => {
                                voucherLoadSuccess();
                            });
                        });
                    }
                });
            }
        });
    }
    if (props.queryParams instanceof LoadVoucherTemplateQueryParams) {
        const queryParams = props.queryParams as LoadVoucherTemplateQueryParams;
        const voucherModelTemp = voucherModel.value;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        voucherModel.value.vgId = voucherModelTemp.vgId || voucherGroupList.value.filter((vg) => vg.isDefault)[0].id;
        voucherModel.value.attachFileIds = queryParams.attachFileIds;
        voucherModel.value.attachFiles = queryParams.attachFiles;
        if (voucherModel.value.attachFiles.length > 0) {
            voucherModel.value.attachments = voucherModel.value.attachFiles.length;
        }

        if (voucherModelTemp.vid === 0 && voucherModelTemp.vdate && voucherModelTemp.vnum) {
            voucherModel.value.vdate = voucherModelTemp.vdate;
            handleGetAllTotalAmount();
            voucherModel.value.vnum = voucherModelTemp.vnum;
        } else {
            resetVDateAndVNum();
        }
        request({
            url: `/api/VoucherTemplate?vtId=${queryParams.vtId}`,
            method: "get",
        }).then((res: IResponseModel<VoucherTemplateModel>) => {
            if (res.state === 1000) {
                voucherModel.value.voucherLines = res.data.voucherLines;
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            }
        });
    }
    if (props.queryParams instanceof LoadVoucherDraftQueryParams) {
        const queryParams = props.queryParams as LoadVoucherDraftQueryParams;
        const voucherModelTemp = voucherModel.value;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        voucherModel.value.vgId = voucherModelTemp.vgId || voucherGroupList.value.filter((vg) => vg.isDefault)[0].id;
        if (voucherModelTemp.vid === 0 && voucherModelTemp.vdate && voucherModelTemp.vnum) {
            voucherModel.value.vdate = voucherModelTemp.vdate;
            handleGetAllTotalAmount();
            voucherModel.value.vnum = voucherModelTemp.vnum;
        } else {
            resetVDateAndVNum();
        }
        request({
            url: `/api/Voucher/GetVoucherFromVoucherDraft?pId=${queryParams.pId}&vId=${queryParams.vId}`,
            method: "post",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value.note = res.data.note;
                voucherModel.value.voucherLines = res.data.voucherLines;
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            }
        });
    }
    if (props.queryParams instanceof LoadVoucherRecyclebintQueryParams) {
        const queryParams = props.queryParams as LoadVoucherRecyclebintQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        request({
            url: `/api/Voucher/GetVoucherFromVoucherRecyclebin?rId=${queryParams.rId}`,
            method: "post",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value.vgId = res.data.vgId;
                voucherModel.value.vdate = res.data.vdate;
                if (voucherModel.value.vnum) {
                    voucherModel.value.vnum = res.data.vnum;
                    handleGetAllTotalAmount();
                } else {
                    resetVDateAndVNum(() => {});
                }
                voucherModel.value.voucherLines = res.data.voucherLines;
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            }
        });
    }
    if (props.queryParams instanceof NewVoucherTemplateQueryParams) {
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = false;
        checkAllVoucherLines();
        handleGetAllTotalAmount();
        nextTick(() => {
            voucherLoadSuccess();
        });
    }
    if (props.queryParams instanceof EditVoucherTemplateQueryParams) {
        const queryParams = props.queryParams as EditVoucherTemplateQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = false;
        handleGetAllTotalAmount();
        request({
            url: `/api/VoucherTemplate?vtId=${queryParams.vtId}`,
            method: "get",
        }).then((res: IResponseModel<VoucherTemplateModel>) => {
            if (res.state === 1000) {
                voucherModel.value.voucherLines = res.data.voucherLines;
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            }
        });
    }
    if (props.queryParams instanceof DataVoucherQueryParams) {
        const queryParams = props.queryParams as DataVoucherQueryParams;
        voucherModel.value = new VoucherModel();
        oldVoucherModel.value = new VoucherModel();
        toolbarDisplay.value = true;
        voucherModel.value.vgId = queryParams.vgId || voucherGroupList.value.filter((vg) => vg.isDefault)[0].id;
        voucherModel.value.vdate = queryParams.vdate;
        voucherModel.value.vnum = queryParams.vnum;
        voucherModel.value.attachments = queryParams.attachments;
        voucherModel.value.attachFileIds = queryParams.attachFileIds;
        voucherModel.value.attachFiles = queryParams.attachFiles;
        voucherModel.value.vtype = queryParams.vtype;
        voucherModel.value.note = queryParams.note;
        tempAsubList = queryParams.tempAsubList;
        tempAAEList = queryParams.tempAAEList;
        lastVDate.value = queryParams.vnum ? queryParams.vdate : lastVDate.value;
        if (voucherModel.value.vdate && voucherModel.value.vnum) {
            handleGetAllTotalAmount();
            voucherModel.value.voucherLines = queryParams.voucherLines;
            checkAllVoucherLines();
            nextTick(() => {
                voucherLoadSuccess();
            });
        } else {
            resetVDateAndVNum(() => {
                voucherModel.value.voucherLines = queryParams.voucherLines;
                checkAllVoucherLines();
                nextTick(() => {
                    voucherLoadSuccess();
                });
            });
        }
    }
    if (props.queryParams !== undefined) {
        if (voucherSettings.value.autoSupplementVNumStatus === 0) {
            request({
                url: "/api/VoucherSettings/Check?voucherDate=&vgId=0",
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    if (res.data) {
                        ElAlert({ message: "本月凭证存在断号，是否自动补号？" }).then((r: any) => {
                            if (r) {
                                voucherSettings.value.autoSupplementVNumStatus = 1;
                            } else {
                                voucherSettings.value.autoSupplementVNumStatus = 2;
                            }
                            request({
                                url: "/api/VoucherSettings",
                                method: "put",
                                data: voucherSettings.value,
                            }).then(async (res: IResponseModel<boolean>) => {
                                if (res.state === 1000 && res.data) {
                                    await voucherSettingsStore.getVoucherSettings();
                                    resetVDateAndVNum();
                                }
                            });
                        });
                    }
                }
            });
        }
    }
}
const salaryCode = ref([]);
const salaryCodeList = ref();
//判断计提工资科目是否开启辅助核算
function isHasAccount(asubid: number) {
    salaryCode.value = [];
    if (asubDict[asubid] && asubDict[asubid].aatypes) {
        salaryCode.value = asubDict[asubid].aatypes.includes(",") ? asubDict[asubid].aatypes.split(",") : [asubDict[asubid].aatypes];
        return true;
    } else {
        return false;
    }
}
//判断计提工资科目辅助核算明细是否都已有值且对应其辅助核算类别
const assistingAccountingListAll = ref<IAssistingAccount[]>([]);
function isHasDetail(aacode: string) {
    let detailList = aacode.split(",");
    salaryCodeList.value = [];
    if (detailList.length !== salaryCode.value.length) {  
        return false;  
    }
    for(let i=0; i <salaryCode.value.length; i++) {
        let list = assistingAccountingListAll.value.filter(v => v.aatype === Number(salaryCode.value[i]));
        for (let j=0; j<list.length; j++) {
            salaryCodeList.value.push(list[j].aaeid);
        }
    }
    const hasAllDetails = detailList.every(el =>salaryCodeList.value.includes(Number(el)));
    return  hasAllDetails && detailList.every(detail => detail.trim()); 
}

const codeLength = computed(() => {
    return useAsubCodeLengthStore().codeLength;
});
const profitDistributionUndistributedProfitCode = computed(() => {
    return '4104' + '0'.repeat(codeLength.value[1]-1) + '6';
});
function handleFocusErrorRow() {
    const voucherLines = voucherContainer.value?.querySelectorAll(".voucherline");
    if (!voucherLines || voucherLines.length === 0) return;
    const errRow = voucherLines[warningRowIndex.value - 1];
    if (!errRow) return;
    const asubDom = errRow.querySelector(".voucherline-asub");
    if (!asubDom) return;
    const timer = setTimeout(() => {
        asubDom.click();
        clearTimeout(timer);
    });
}
function checkAllVoucherLines() {
    generateCheckTip.value = "";
    let hasErrLine = false;
    // 利润分配-未分配利润
    let hasProfitDistributionUndistributedProfit = false;
    // 以前年度损益调整
    let hasPriorYearSunyiAdjustment = false;
    let title = "";
    if (route.path === "/Salary/SalaryManage" || route.path === "/Checkout/Checkout") {
        assistingAccountingListAll.value = useAssistingAccountingStore().assistingAccountingListAll;
        for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
            if (voucherModel.value.voucherLines[i].asubId !== 0 || tempAsubList.length > 0) {
                let salaryAsubid = voucherModel.value.voucherLines[i].asubId;
                let salaryAacode = voucherModel.value.voucherLines[i].aacode;
                if (
                    voucherModel.value.voucherLines[i].isjtsub 
                    && isHasAccount(Number(salaryAsubid)) 
                    && !isHasDetail(salaryAacode)
                )  { 
                    if (title.indexOf(asubDict[salaryAsubid].asubCode) === -1) {
                        title += asubDict[salaryAsubid].asubCode + " " + asubDict[salaryAsubid].asubAAName + "，"; 
                    } 
                }
            }
        }
        if(title !== "") {
            title += `启用了辅助核算，请前往${isErp.value ? '【基础资料-业务资料-职员】' : '【工资-员工信息】' }点击编辑，重新选择计提工资科目并完善辅助核算明细哦~` 
        }
    }
    for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
        if (voucherModel.value.voucherLines[i].asubId !== 0 || tempAsubList.length > 0) {
            if (!checkVoucherLineAsub(voucherModel.value.voucherLines[i], true) 
                ||  (voucherModel.value.voucherLines[i].isjtsub 
                    && isHasAccount(Number(voucherModel.value.voucherLines[i].asubId)) 
                    && !isHasDetail(voucherModel.value.voucherLines[i].aacode)) 
            ) {
                if (!isTempVoucher.value) {
                    voucherModel.value.voucherLines[i].asubId = 0;
                    voucherModel.value.voucherLines[i].foreigncurrency = 0;
                    voucherModel.value.voucherLines[i].quantityAccounting = 0;
                    voucherModel.value.voucherLines[i].assistingAccounting = 0;
                    voucherModel.value.voucherLines[i].asubAAName = voucherModel.value.voucherLines[i].asubName;
                }
                if (!hasErrLine) {
                    hasErrLine = true;
                    if (title !== "") { //计提工资生成凭证
                        ElAlert({ 
                            message: title, 
                            hideCancel: true,
                            close: () => {},
                            title: '提示',
                            options: {
                                confirmButtonText: '前往编辑', cancelButtonText: ''
                            }
                        }).then((r) => {
                            if (r) {
                                if (!isErp.value) {
                                    globalWindowOpenPage("/Salary/EmployInfo", "员工信息");
                                } else {
                                    globalWindowOpenPage("/Employees", "职员列表");
                                }
                            } else {
                                ElAlert({
                                    message: "亲，第" + (i + 1) + "行，请选择科目！", 
                                    hideCancel: true, 
                                }).then(() => {
                                    setWarningRow(i + 1);
                                });
                            }
                        });
                    }else{
                        ElAlert({
                            message: `亲，第${i + 1}行，` + (generateCheckTip.value || '请选择科目！'), 
                            hideCancel: true, 
                        }).then(() => {
                            setWarningRow(i + 1);
                            isTempVoucher.value && handleFocusErrorRow();
                        });
                    }
                }
            } else {
                if (asubDict[voucherModel.value.voucherLines[i].asubId]) {
                    voucherModel.value.voucherLines[i].asubAAName =
                        voucherModel.value.voucherLines[i].asubCode + " " + asubDict[voucherModel.value.voucherLines[i].asubId].asubAAName;
                }
                if (voucherModel.value.voucherLines[i].foreigncurrency === 0) {
                    voucherModel.value.voucherLines[i].fcId = 1;
                    voucherModel.value.voucherLines[i].fcCode = "";
                    voucherModel.value.voucherLines[i].fcAmount = 0;
                    voucherModel.value.voucherLines[i].fcRate = 0;
                }
                if (voucherModel.value.voucherLines[i].quantityAccounting === 0) {
                    voucherModel.value.voucherLines[i].measureUnit = "";
                    voucherModel.value.voucherLines[i].quantity = 0;
                    voucherModel.value.voucherLines[i].price = 0;
                }
                if (voucherModel.value.voucherLines[i].assistingAccounting === 0) {
                    voucherModel.value.voucherLines[i].aacode = "";
                }
            }
        } else if (voucherModel.value.voucherLines[i].asubName) {
            //科目不存在
            voucherModel.value.voucherLines[i].asubId = 0;
            if (!hasErrLine) {
                hasErrLine = true;
                ElAlert({
                    message: "亲，第" + (i + 1) + "行，请选择科目！", 
                    hideCancel: true, 
                }).then(() => {
                    setWarningRow(i + 1);
                });
            }
        }
        if(props.from === 'checkout' && accountingStandard.value === AccountStandard.CompanyStandard){
            if(voucherModel.value.voucherLines[i].asubCode.indexOf("6901") === 0){
                hasPriorYearSunyiAdjustment = true;
            }
            if(voucherModel.value.voucherLines[i].asubCode.indexOf(profitDistributionUndistributedProfitCode.value) === 0){
                hasProfitDistributionUndistributedProfit = true;
            }
        }
    }
    if(props.from === 'checkout' && accountingStandard.value === AccountStandard.CompanyStandard && hasPriorYearSunyiAdjustment && !hasProfitDistributionUndistributedProfit){
        ElAlert({
            message: `“以前年度损益调整”期末结转至“利润分配-未分配利润”，未检测到系统预设“${profitDistributionUndistributedProfitCode.value} 利润分配-未分配利润”科目，请手动录入凭证或创建自定义结转模板`, 
            hideCancel: true,
            leftJustifying: true,
            isInTabsContent: true,
            dialogWidth: '465',
        })
    }
    if (voucherModel.value.voucherLines.length < 4) {
        let needLength = 4 - voucherModel.value.voucherLines.length;
        for (let i = 0; i < needLength; i++) {
            voucherModel.value.voucherLines.push(new VoucherEntryModel());
        }
    }
}

function voucherLoadSuccess() {
    emit("loadSuccess", voucherModel.value);
    rebindVoucherlineOnScroll();
}
const minInViewportVoucherLineIndex = ref(0);
const maxInViewportVoucherLineIndex = ref(voucherlinePageSize.value - 1);
let onOnScrolling = false;
let recordScrollTop = 0;
const voucherlineOnScroll = () => {
    if (Math.abs(recordScrollTop - findScrollNode().scrollTop) < 70) return;
    if (onOnScrolling) return;
    onOnScrolling = true;
    requestAnimationFrame(() => {
        onOnScrolling = false;
    });
    recordScrollTop = findScrollNode().scrollTop;
    if (asubSelectorDisplay.value === DisplayStatus.Hidden && appendMargin.value !== 0) {
        appendMargin.value = 0;
    }
    const voucherLines = containerRef.value?.querySelectorAll(".voucherline");
    if (!voucherLines) return;
    const voucherLinesArray: Array<HTMLElement> = Array.from(voucherLines);
    let inCirculation = false;
    for (let i = 0; i < voucherLinesArray.length; i++) {
        if (isElementInViewport(voucherLinesArray[i])) {
            if (!inCirculation) {
                minInViewportVoucherLineIndex.value = i;
                inCirculation = true;
                if (minInViewportVoucherLineIndex.value + voucherlinePageSize.value > voucherLinesArray.length - 1) {
                    maxInViewportVoucherLineIndex.value = voucherLinesArray.length - 1;
                    break;
                }
            }
        } else {
            // 进入循环之后  如果遇到不在视口的元素 说明已经遍历完了
            if (inCirculation) {
                maxInViewportVoucherLineIndex.value = i - 1;
                break;
            }
        }
    }
};
function checkDisplay(index: number) {
    return (
        index >= minInViewportVoucherLineIndex.value - voucherlinePageSize.value &&
        index < maxInViewportVoucherLineIndex.value + 2 * voucherlinePageSize.value
    );
}
const resetScrollIndex = () => {
    minInViewportVoucherLineIndex.value = 0;
    maxInViewportVoucherLineIndex.value = voucherlinePageSize.value - 1;
};
function isElementInViewport(element: HTMLElement) {
    const rect = element.getBoundingClientRect();
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

    return rect.top <= viewportHeight && rect.bottom >= 0;
}

const scrollTime: Ref<number> | undefined = inject("carryOverVoucherTime");
let voucherlineScrollBinded = false;
const rebindVoucherlineOnScroll = () => {
    if (voucherlineScrollBinded) return;
    const time = scrollTime ? scrollTime.value : 0;
    setTimeout(() => {
        if (voucherlineScrollBinded) {
            return;
        }
        const scrollNode = findScrollNode();
        if (scrollNode.scrollHeight > scrollNode.clientHeight) {
            scrollNode.addEventListener("scroll", voucherlineOnScroll);
            voucherlineScrollBinded = true;
        }
    }, time);
};

const lastVDate = ref("1999-12-31");
const getChangeNumFlag = (newVal: string, oldVal: string) => {
    if (newVal && oldVal) {
        return newVal.slice(0, 7) === oldVal.slice(0, 7);
    }
    return false;
};

const dateRef = ref();
function handleKeyDown(event: any) {
    event.currentTarget.value = event.currentTarget.value.replace(
        /(\d{4})-(\d{1,2})-(\d{1,2})/,
        function (match: string, year: string, month: string, day: string) {
            return year + "-" + month.padStart(2, "0") + "-" + day.padStart(2, "0");
        }
    );
    voucherModel.value.vdate = event.currentTarget.value;
    handleGetAllTotalAmount();
    dateRef.value.handleClose();
}

function resetVDateAndVNum(callback?: () => void, skipDate?: boolean) {
    if (!voucherModel.value.vdate) {
        request({
            url: "/api/Period/GetCurrentPeriod",
            method: "post",
        }).then((res: IResponseModel<any>) => {
            if (res.state === 1000) {
                let date = new Date(res.data.endDate);
                if (date.getFullYear() === new Date().getFullYear() && date.getMonth() === new Date().getMonth()) {
                    date = new Date();
                }
                voucherModel.value.vdate = `${date.getFullYear()}-${date.getMonth() + 1 < 10 ? "0" : ""}${date.getMonth() + 1}-${
                    date.getDate() < 10 ? "0" : ""
                }${date.getDate()}`;
                handleGetAllTotalAmount();
                resetVDateAndVNum(callback);
            }
        });
        return;
    }
    //只在当月改变天的日期不改变凭证号
    if (!skipDate && getChangeNumFlag(lastVDate.value, voucherModel.value.vdate) && voucherModel.value.vdate.slice(0, 7) !== "2000-01") {
        return;
    } else {
        lastVDate.value = voucherModel.value.vdate;
    }

    if (getChangeNumFlag(voucherModel.value.vdate, oldVoucherModel.value.vdate) && oldVoucherModel.value.vgId === voucherModel.value.vgId && props.queryParams instanceof EditVoucherQueryParams) {
        voucherModel.value.vnum = oldVoucherModel.value.vnum;
        handleGetAllTotalAmount();
        emit("resetVDateAndVNum");
        callback && callback();
        return;
    }

    request({
        url: `/api/Voucher/GetNewVNumAndVDate?vgId=${voucherModel.value.vgId}&vDate=${voucherModel.value.vdate}&dotAutoSupplementVNum=false`,
        method: "post",
    }).then((res: IResponseModel<{ vdate: string; vnum: number }>) => {
        if (res.state === 1000) {
            voucherModel.value.vnum = res.data.vnum;
            handleGetAllTotalAmount();
            if (!voucherModel.value.vdate) {
                voucherModel.value.vdate = res.data.vdate;
            }
            emit("resetVDateAndVNum");
            callback && callback();
        }
    });
}

function getVDateDisabledState(date: Date) {
    if (props.getDisabledDate !== undefined) {
        return props.getDisabledDate(date);
    }

    const now = new Date();
    now.setFullYear(now.getFullYear() + 5);
    if (date.getTime() >= accountSetMinDate.getTime() && date.getTime() < now.getTime()) {
        return false;
    } else {
        return true;
    }
}

function changeZoomState(state: "in" | "out") {
    window.localStorage.setItem("voucherZoomState", state);
    zoomState.value = state;
    nextTick().then(() => {
        asubSelectorDisplay.value === DisplayStatus.Visible && calcAsubSelectorPosition();
    });
    emit("zoom", zoomState.value);
}

function initSubjectDic() {
    const accountSubject = accountSubjectStore.accountSubjectList;
    asubDict = {};
    accountSubject.forEach((asub) => {
        if (asub.status === 0 && asub.isLeafNode) {
            asubDict[asub.asubId] = asub;
        }
    });
}

function saveVoucherSuggestionCount(asubId: number) {
    if (window.isStopAIFunction) return;
    request({
        url: "/api/Intelligence/SaveVoucherSuggestionCount?isSuccess=" + (voucherSuggestion.includes(asubId) ? "true" : "false"),
        method: "put",
    }).then((response: IResponseModel<boolean>) => {
        if (response.state === 1000) {
            if (response.data) {
                console.log("保存推荐记录成功");
            }
        }
    });
}

function refreshVoucherCache() {
    if (window.isStopAIFunction) return;
    request({
        url: "/api/Intelligence/RefreshVoucherCache",
        method: "post",
    });
}

let voucherSuggestion: Array<number> = [];
async function getVoucherSuggestionAsync(): Promise<void> {
    if (window.isStopAIFunction) return;
    let lineIndex = editingInfo.editingIndex + 1;
    let description = editingInfo.editingLine.description.trim();
    let asubIds = [];
    for (var key in voucherModel.value.voucherLines) {
        asubIds.push(voucherModel.value.voucherLines[key].asubId);
    }

    voucherSuggestion = [];
    await Promise.race([
        request({
            url: "/api/Intelligence/GetVoucherSuggestion",
            method: "post",
            data: { asubIds, description, lineIndex },
        }).then((response: IResponseModel<Array<any>>) => {
            if (response.state === 1000) {
                voucherSuggestion = response.data;
            } else {
                voucherSuggestion = [];
            }
        }),
        new Promise<any>((_, reject) => setTimeout(() => reject("凭证AI科目推荐超时"), 1000)),
    ]);
}

watchEffect(() => {
    const accountSubject = accountSubjectStore.accountSubjectList;
    asubList.value = [];
    asubTypeList.value = [];
    asubDict = {};
    if (!window.isStopAIFunction) {
        asubList.value.push({ asubType: 9999, asubList: [] });
        asubTypeList.value.push({ id: 9999, name: "AI推荐", selected: true });
    }
    asubList.value.push({ asubType: 0, asubList: accountSubject.filter((asub) => asub.status === 0 && asub.isLeafNode) });
    asubTypeList.value.push({ id: 0, name: "全部", selected: true });
    let asubGroup: any = {};
    accountSubject.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = new Array<IAccountSubjectModel>();
            asubList.value.push({ asubType: asub.asubType, asubList: asubGroup[asub.asubType] });
            asubTypeList.value.push({ id: asub.asubType, name: AsubTypeEnum[asub.asubType], selected: false });
        }
        if (asub.status === 0 && asub.isLeafNode) {
            asubGroup[asub.asubType].push(asub);
            asubDict[asub.asubId] = asub;
        }
    });
});

watchEffect(() => {
    useAssistingAccountingStore().assistingAccountingList;
    loadAAE();
});

let addAccountSubjectEditingInfo: {
    editingType: string;
    editingLine: VoucherEntryModel;
    editingTr: any;
};
async function addAccountSubjectSuccess(asubId: number) {
    accountSubjectStore.getAccountSubject().then(() => {
        initSubjectDic();
        selectAsub(asubDict[asubId]);
    });
    editingInfo.editingType = addAccountSubjectEditingInfo.editingType;
    editingInfo.editingLine = addAccountSubjectEditingInfo.editingLine;
    editingInfo.editingTr = addAccountSubjectEditingInfo.editingTr;
}

function addAccountSubject() {
    addAccountSubjectEditingInfo = {
        editingType: editingInfo.editingType,
        editingLine: editingInfo.editingLine,
        editingTr: editingInfo.editingTr,
    };
    accountSubjectAddDialog.value?.showAADialog();
}

function escapeRegExp(aanum: string) {
    return aanum.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

async function loadAAE() {
    await request({
        url: "/api/AssistingAccounting/ListWithAcronym",
        method: "get",
    }).then((res: IResponseModel<Array<any>>) => {
        if (res.state === 1000) {
            aaeGroup.value = {};
            aaeDict = {};
            res.data
                .filter((aae) => aae.aaeid > 0)
                .forEach((aae) => {
                    aae.aaname = aae.aaname.replace(new RegExp(`^${escapeRegExp(aae.aanum)} `), "");
                    aaeDict[aae.aaeid] = aae;
                    if (
                        aae.aatype === 10007 &&
                        aae.aaname === "期初现金余额" &&
                        accountingStandard.value !== 4 &&
                        accountingStandard.value !== 5
                    ) {
                        return;
                    }
                    if (!aaeGroup.value[aae.aatype]) {
                        aaeGroup.value[aae.aatype] = new Array<{ id: number; name: string; zjm: string; model: any }>();
                    }
                    aaeGroup.value[aae.aatype].push({
                        id: aae.aaeid,
                        name: aae.aanum + " " + aae.aaname,
                        zjm: aae.aaacronym,
                        model: aae.model,
                    });
                });
        }
    });
}

function clearAAE() {
    aaeGroup.value = {};
    aaeDict = {};
}
const AddAssistingAccountingTitle = ref("添加辅助核算项目");
function showAaeAddDialog(aaType: number) {
    aaeAADialogDisplay.value = true;
    addAAEIndex.value = aaeSelectorInfo.index;
    document.removeEventListener("mousedown", asubBlur);
    if (window.isErp) {
        aaeAddDialog.value?.setDialogWidth(aaType === 10001 || aaType === 10002 || aaType === 10006 ? 740 : 440);
        switch (aaType) {
            case 10001:
                AddAssistingAccountingTitle.value = "新增客户";
                break;
            case 10002:
                AddAssistingAccountingTitle.value = "新增供应商";
                break;
            case 10003:
                AddAssistingAccountingTitle.value = "新增职员";
                break;
            case 10004:
                AddAssistingAccountingTitle.value = "新增部门";
                break;
            case 10006:
                AddAssistingAccountingTitle.value = "新增商品";
                break;
            default:
                AddAssistingAccountingTitle.value = "添加辅助核算项目";
                break;
        }
    }
    let autoAddName = getAAEList().length === 0 ? aaeSelectorInfo.searchInfo : "";
    aaeAddDialog.value?.showAADialog(aaType, autoAddName);
}

async function addAssistingAccountingSuccess() {
    await loadAAE();
    setTimeout(() => {
        asubSelector.value.querySelectorAll(".asub-aa-selector .aatype-content input")[addAAEIndex.value].focus();
    }, 0);
}

function addAssistingAccountingClose() {
    aaeAADialogDisplay.value = false;
    document.addEventListener("mousedown", asubBlur);
}

let oldAttachFileNum = 0;
function attachfilesClick() {
    oldAttachFileNum = voucherModel.value.attachFiles?.length | 0;
    uploadFileDialogRef.value?.open(
        {
            eRecordSearchDate: {
                startDate: getMonthStart(new Date(voucherModel.value.vdate)),
                endDate: getMonthEnd(new Date(voucherModel.value.vdate)),
            },
            fileCategory: AttachFileCategory.Voucher,
        },
        voucherModel.value.attachFiles,
        10001
    );
}
async function checkNeedToastWithBillAndVoucher(delFileids: string) {
    return await request({
        url: "/api/Voucher/GetNeedSaveToOther",
        method: "post",
        params: { pid: voucherModel.value.pid || 0, vid: voucherModel.value.vid || 0, delFileids },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000) return false;
        return res.data;
    }).catch(() => {
        return false;
    })
}
async function saveAttachfiles(params: any, newFileids: Number[], delFileids: Number[], fileList: IFileInfo[]) {
    voucherModel.value.attachFiles = fileList.map((f) => {
        let model = new VoucherAttachFileModel();
        model.fileId = f.fileId;
        model.fileName = f.fileName;
        model.fileSize = f.fileSize;
        model.relativePath = f.relativePath;
        model.fileType = f.fileType;
        return model;
    });
    voucherModel.value.attachFileIds = voucherModel.value.attachFiles.map((f) => f.fileId).join(",");

    const attachFileNum = voucherModel.value.attachFiles.length;
    if (voucherModel.value.attachments !== undefined) {
        const newAttachNum = voucherModel.value.attachments + attachFileNum - oldAttachFileNum;
        if (newAttachNum < 0) {
            voucherModel.value.attachments = attachFileNum;
        } else {
            voucherModel.value.attachments = newAttachNum;
        }
    } else {
        voucherModel.value.attachments = attachFileNum;
    }
    if (props.queryParams instanceof EditVoucherQueryParams && delFileids.length > 0) {
        const needToast = await checkNeedToastWithBillAndVoucher(delFileids.join(","));
        if (needToast) {
            showDeleteBillOrVoucherConfirm("voucher").then((batchDelete: boolean) => {
                isNeedSaveToOther = batchDelete;
            });
        }
    }
}

function noteClick(event: any) {
    noteDialogDisplay.value = true;
    const containerOffset = containerRef.value.getBoundingClientRect();
    let offset = event.currentTarget.getBoundingClientRect();
    noteDialog.value.style.left = offset.left - 1 - 200 - containerOffset.left + "px";
    noteDialog.value.style.top = offset.top + offset.height + 12 - containerOffset.top + "px";
    nextTick(() => {
        noteDialog.value.querySelector("textarea").focus();
    });
}
function preventWrap(e: any) {
    if (e.keyCode === 13) {
        e.preventDefault();
    }
}

initCostAccountingSettings();
function showCostAccountingSettingsDialog(event: any) {
    costAccountingSettingsDialogInfo.display = true;
    initCostAccountingSettings();
    const containerOffset = containerRef.value.getBoundingClientRect();
    let offset = event.currentTarget.getBoundingClientRect();
    costAccountingSettingsContainer.value.style.left = offset.left - 201 - containerOffset.left + "px";
    costAccountingSettingsContainer.value.style.top = offset.top + offset.height + 12 - containerOffset.top + "px";
}

function initCostAccountingSettings() {
    const costAccountingSetting = getCostAccountingSetting();
    if (costAccountingSetting === 0) {
        costAccountingSettingsDialogInfo.useCostAccounting = false;
        costAccountingSettingsDialogInfo.costAccountingSetting = 1;
    } else {
        costAccountingSettingsDialogInfo.useCostAccounting = true;
        costAccountingSettingsDialogInfo.costAccountingSetting = costAccountingSetting;
    }
}
function getCostAccountingSetting(): number {
    const costAccountingSetting = localStorage.getItem("costAccountingSetting");
    if (costAccountingSetting) {
        return Number(costAccountingSetting);
    } else {
        return 1;
    }
}

function saveCostAccountingSetting() {
    let costAccountingSetting = 0;
    if (costAccountingSettingsDialogInfo.useCostAccounting) {
        costAccountingSetting = costAccountingSettingsDialogInfo.costAccountingSetting;
    }
    localStorage.setItem("costAccountingSetting", costAccountingSetting.toString());
    costAccountingSettingsDialogInfo.display = false;
}

function addVoucherLine(index: number, op: string = "add") {
    let line = voucherModel.value.voucherLines[index];
    if (line && line.assistingAccounting === 1 && !checkVoucherLineAsub(line)) {
        const element = document.getElementsByClassName("asub-aa-selector")[0];
        const computedStyle = window.getComputedStyle(element);
        const displayPropertyValue = computedStyle.getPropertyValue("display");
        if (displayPropertyValue === "none") {
            ElNotify({
                message: "亲，请选择辅助核算项目!",
                type: "warning",
            });
        }
        return;
    }
    op === "add"
        ? voucherModel.value.voucherLines.splice(index, 0, new VoucherEntryModel())
        : voucherModel.value.voucherLines.splice(index, 0, _.cloneDeep(line));
    if (op === "copy") {
        if (getTotal("debit") > *********.99 || getTotal("credit") > *********.99) {
            ElNotify({
                type: "warning",
                message: "亲，合计金额不允许超亿位哦~",
            });
            line.debit ? (line.debit = 0) : (line.credit = 0);
        }
    }
    rebindVoucherlineOnScroll();
    setScrollTopChange(voucherlineMinheight.value);
    maxInViewportVoucherLineIndex.value = Math.min(index + voucherlinePageSize.value / 2, voucherModel.value.voucherLines.length);
}
let currentDragTargetIndex = ref(0);
const isDragging = ref(false);
const isDragEnter = ref(false);
let currentDragTarget = ref();
const allowDrag = ref(true);
const orderClickIndex = ref(-1);
const dragEnterRowIndex = ref(-1);
function handleDragStart(event: MouseEvent, item: VoucherEntryModel, index: number) {
    if (isVoucherReadonly.value || isFolkChangeoutVoucher.value) return;
    currentDragTargetIndex.value = index;
    currentDragTarget.value = item;
    isDragging.value = true;
    let timer = setTimeout(() => {
        isDragging.value = false;
        clearTimeout(timer);
    }, 0);
}
function handleDragMove() {
    isDragging.value = false;
    isDragEnter.value = true;
}
function handleDragEnter(index: number) {
    if (isVoucherReadonly.value || isFolkChangeoutVoucher.value) return;
    dragEnterRowIndex.value = currentDragTargetIndex.value !== index || isDragEnter.value ? index : -1;
}
function handleDragEnd(index: number) {
    if (!isVoucherReadonly.value && !isFolkChangeoutVoucher.value) {
        voucherModel.value.voucherLines.splice(currentDragTargetIndex.value, 1);
        const position = index === -100 ? 0 : currentDragTargetIndex.value < index ? index : index + 1;
        voucherModel.value.voucherLines.splice(position, 0, currentDragTarget.value);
        orderClickIndex.value = position;
        currentDragTargetIndex.value = -1;
        isDragEnter.value = false;
        dragEnterRowIndex.value = -1;
    }
}
function delVoucherLine(index: number) {
    if (editingInfo.editingLine === voucherModel.value.voucherLines[index]) {
        return;
    }
    voucherModel.value.voucherLines.splice(index, 1);
    if (voucherModel.value.voucherLines.length < 4) {
        voucherModel.value.voucherLines.push(new VoucherEntryModel());
    }
}

function setScrollTopChange(value: number) {
    setTimeout(() => {
        if (window.isErp) {
            setScrollTopForErp(getScrollTopForErp() + value);
        } else {
            setScrollTop(getScrollTop() + value);
        }
    }, 0);
}

function setScrollTopForErp(scrollTop: number) {
    findScrollNode().scrollTop = scrollTop;
}

function getScrollTopForErp() {
    return findScrollNode().scrollTop;
}

function findScrollNode() {
    if (window.isErp) {
        let node = containerRef.value.parentNode;
        // 直到body为止，向上找到第一个有滚动条的节点，
        while (node.scrollHeight === node.clientHeight) {
            node = node.parentNode;
            if (node === document.body) {
                break;
            }
        }
        return node;
    } else {
        return document.querySelector(".router-container");
    }
}

function tryAppendVoucherLine(voucherline: VoucherEntryModel, type: string) {
    if (isFolkChangeoutVoucher.value) return;
    if (voucherModel.value.voucherLines.indexOf(voucherline) === voucherModel.value.voucherLines.length - 1) {
        const vl: any = voucherline;
        if (vl[type]) {
            const timer = setTimeout(() => {
                addVoucherLine(voucherModel.value.voucherLines.length);
                if (editingInfo.editingType === "asub" && asubSelectorDisplay.value === DisplayStatus.Visible) {
                    nextTick().then(() => {
                        calcAsubSelectorPosition();
                    });
                }
                clearTimeout(timer);
            }, 100);
        }
    }
}

function voucherLineClick(event: any, editingType: string, editingLine: VoucherEntryModel, editingIndex: number) {
    orderClickIndex.value = -1;
    if (isFolkChangeoutVoucher.value) {
        if (editingType !== "description" || editingLine.asubId === 0) {
            return;
        }
    }
    if (isVoucherReadonly.value) {
        return;
    }
    if (editingInfo.editingType !== "") {
        return;
    }
    if (editingType === "fcRate" && editingLine.fcId === 1) {
        return;
    }
    if (editingType === "debit" || editingType === "credit") {
        if (editingType === "debit") {
            debitHistoryStack.cacheVoucherLine = debitHistoryStack.currentVoucherLine;
            debitHistoryStack.currentVoucherLine = editingIndex;
        } else {
            creditHistoryStack.cacheVoucherLine = creditHistoryStack.currentVoucherLine;
            creditHistoryStack.currentVoucherLine = editingIndex;
        }
    }
    editingInfo.editingType = editingType;
    editingInfo.editingLine = editingLine;
    editingInfo.editingIndex = editingIndex;
    let trDom = event.currentTarget.parentNode;
    while (trDom.attributes.class.value !== "voucherline") {
        trDom = trDom.parentNode;
    }
    editingInfo.editingTr = trDom;
    let target = event.currentTarget;
    nextTick(() => {
        if (
            editingType === "debit" ||
            editingType === "credit" ||
            editingType === "quantity" ||
            editingType === "price" ||
            editingType === "fcAmount" ||
            editingType === "fcRate"
        ) {
            target.querySelector("input").focus();
            target.querySelector("input").select();
        } else if (editingType !== "fc") {
            target.querySelector("textarea").focus();
            target.querySelector("textarea").select();
        }
    });
}

function cancelEdit() {
    editingInfo.editingType = "";
    editingInfo.editingLine = new VoucherEntryModel();
    editingInfo.editingTr = null;
}

const initNextDescriptionFlag = ref(false);
const recordDescription = ref("");
function descriptionFocus(event: any) {
    allowDrag.value = false;
    warningRowIndex.value = -1;
    desListSelectorDisplay.value = true;
    recordDescription.value = editingInfo.editingLine.description;
    if (!editingInfo.editingLine.description || initNextDescriptionFlag.value) {
        initNextDescriptionFlag.value = false;
        let tempDes = "";
        for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
            if (voucherModel.value.voucherLines[i] === editingInfo.editingLine) {
                editingInfo.editingLine.description = tempDes;
                let target = event.currentTarget;
                nextTick(() => {
                    target.select();
                });
                break;
            }
            tempDes = voucherModel.value.voucherLines[i].description || tempDes;
        }
    }
    calcDesListSelectorPosition();
    document.addEventListener("mousedown", descriptionBlur);
    window.addEventListener("resize", calcDesListSelectorPosition);
}

function calcDesListSelectorPosition() {
    if (desListSelectorDisplay.value) {
        const containerOffset = containerRef.value.getBoundingClientRect();
        let offset = editingInfo.editingTr.querySelector(".voucherline-description textarea").getBoundingClientRect();
        desListSelector.value.style.left = offset.left - 1 - containerOffset.left + "px";
        desListSelector.value.style.top = offset.top + offset.height - containerOffset.top + "px";
        desListSelector.value.style.width = editingInfo.editingTr.querySelector(".voucherline-description textarea").clientWidth + 2 + "px";
    }
}

function descriptionClick() {
    desListSelectorDisplay.value = true;
}

function descriptionBlur() {
    desListSelectorDisplay.value = false;
    desListSearchInfo.value = "";
    desListSelectIndex.value = -1;
    tryAppendVoucherLine(editingInfo.editingLine, editingInfo.editingType);
    cancelEdit();
    document.removeEventListener("mousedown", descriptionBlur);
    window.removeEventListener("resize", calcDesListSelectorPosition);
}

function descriptionInput() {
    if (editingInfo.editingLine.description.length === 256) {
        ElNotify({ type: "warning", message: "亲，摘要不能超过256个字哦~" });
    }
    desListSearchInfo.value = editingInfo.editingLine.description;
    calcDesListSelectorPosition();
}
function descriptionPaste(event: any) {
    const textarea = event.currentTarget as HTMLTextAreaElement;
    const selectionStart = textarea.selectionStart;
    const selectionEnd = textarea.selectionEnd;
    const beforeVal = textarea.value.substring(0, selectionStart);
    const afterVal = textarea.value.substring(selectionEnd);
    const pasteVal = event.clipboardData.getData("text");
    if (pasteVal.length === 0) return;
    if (pasteVal.indexOf("\r\n") !== -1) {
        let list: Array<string> = pasteVal.split("\r\n");
        if (list[list.length - 1] === "") list.pop();
        event.preventDefault();
        const targetDescription = beforeVal + list[0] + afterVal;
        if (targetDescription.length > 256) {
            ElNotify({ type: "warning", message: "亲，摘要不能超过256个字哦~" });
            textarea.value = targetDescription.substring(0, 256);
            editingInfo.editingLine.description = targetDescription.substring(0, 256);
            textarea.setSelectionRange(256, 256);
        } else {
            textarea.value = targetDescription;
            editingInfo.editingLine.description = targetDescription;
            const targetSelectionPosition = selectionStart + list[0].length;
            textarea.setSelectionRange(targetSelectionPosition, targetSelectionPosition);
        }
        const appendVoucherLines = list.length - (voucherModel.value.voucherLines.length - editingInfo.editingIndex);
        tryPasteAddVoucherLine(appendVoucherLines);
        for (let i = 1; i < list.length; i++) {
            const targetVal = list[i].slice(0, 256);
            const voucherLine = voucherModel.value.voucherLines[editingInfo.editingIndex + i];
            if (voucherLine) {
                voucherLine.description = targetVal;
            }
        }
        setTimeout(() => {
            calcDesListSelectorPosition();
        });
    }
}
function tryPasteAddVoucherLine(appendVoucherLines: number) {
    if (appendVoucherLines > 0) {
        const newLength = voucherModel.value.voucherLines.length + appendVoucherLines;
        const initlength = voucherModel.value.voucherLines.length;
        for (let i = initlength; i < newLength; i++) {
            addVoucherLine(voucherModel.value.voucherLines.length);
        }
    }
}
function noteInput() {
    if (voucherModel.value.note.length == 256) {
        ElNotify({ type: "warning", message: "亲，备注不能超过256个字哦~" });
    }
}
function desListSelect(des: string) {
    editingInfo.editingLine.description = des;
    let asubDom = editingInfo.editingTr.querySelector(".voucherline-asub");
    descriptionBlur();
    setTimeout(() => {
        asubDom.click();
    });
}

function descriptionKeydown(event: any) {
    const textarea = event.target as HTMLTextAreaElement;
    if (event.keyCode === 37) {
        if (editingInfo.editingIndex !== 0 && checkSelectionIsFirst(textarea)) {
            desListSelectIndex.value = -1;
            const targetIndex = editingInfo.editingIndex - 1;
            const voucherLinesContainer = editingInfo.editingTr.parentNode.parentNode;
            if (voucherLinesContainer) {
                const targetTr = voucherLinesContainer.children[targetIndex];
                if (targetTr) {
                    const asubDom = targetTr.querySelectorAll(".voucherline-amount")[1];
                    descriptionBlur();
                    setTimeout(() => {
                        asubDom.click();
                    });
                }
            }
        }
    }
    if (event.keyCode === 38) {
        if (desListSelectIndex.value === -1) {
            desListSelectIndex.value = getDesList().length - 1;
        } else {
            const nextIndex = desListSelectIndex.value - 1;
            desListSelectIndex.value = nextIndex === -1 ? getDesList().length - 1 : nextIndex;
        }
        autoScroll(desListSelector.value);
    }
    if (event.keyCode === 39) {
        if (checkSelectionIsLast(textarea)) {
            let asubDom = editingInfo.editingTr.querySelector(".voucherline-asub");
            descriptionBlur();
            setTimeout(() => {
                asubDom.click();
            });
        }
    }
    if (event.keyCode === 40) {
        if (desListSelectIndex.value === -1) {
            desListSelectIndex.value = 0;
        } else {
            const nextIndex = desListSelectIndex.value + 1;
            desListSelectIndex.value = nextIndex === getDesList().length ? 0 : nextIndex;
        }
        autoScroll(desListSelector.value);
    }
    if (event.keyCode === 13 || event.keyCode === 9) {
        if (desListSelectIndex.value !== -1) {
            desListSelect(getDesList()[desListSelectIndex.value].description);
        } else {
            let asubDom = editingInfo.editingTr.querySelector(".voucherline-asub");
            descriptionBlur();
            setTimeout(() => {
                asubDom.click();
            });
        }
    }
    if (event.ctrlKey) {
        if (event.keyCode === 187) {
            event.preventDefault();
            const container = editingInfo.editingTr.parentNode.parentNode;
            const index = editingInfo.editingIndex + 1;
            descriptionBlur();
            addVoucherLine(editingInfo.editingIndex);
            setTimeout(() => {
                container.children[index].querySelector(".voucherline-description").click();
            });
        } else if (event.keyCode === 189) {
            event.preventDefault();
            descriptionBlur();
            delVoucherLine(editingInfo.editingIndex);
        }
    }
    if (event.keyCode === 114) {
        if (editingInfo.editingIndex !== 0) {
            event.preventDefault();
            const lastValue = voucherModel.value.voucherLines[editingInfo.editingIndex - 1].description || "";
            editingInfo.editingLine.description = lastValue;
            const dom = editingInfo.editingTr.querySelector(".voucherline-description");
            textarea.blur();
            descriptionBlur();
            setTimeout(() => {
                dom.click();
            });
        }
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}
function checkSelectionIsLast(input: HTMLInputElement | HTMLTextAreaElement) {
    const selectionStart = input.selectionStart;
    const selectionEnd = input.selectionEnd;
    if (selectionStart === selectionEnd && selectionStart === input.value.length) {
        return true;
    }
    return false;
}
function checkSelectionIsFirst(input: HTMLInputElement | HTMLTextAreaElement) {
    const selectionStart = input.selectionStart;
    const selectionEnd = input.selectionEnd;
    if (selectionStart === selectionEnd && selectionStart === 0) {
        return true;
    }
    return false;
}
function checkHasSelection(input: HTMLInputElement | HTMLTextAreaElement) {
    const selectionStart = input.selectionStart;
    const selectionEnd = input.selectionEnd;
    if (selectionStart !== selectionEnd || input.value.length === 0) {
        return true;
    }
    return false;
}

function getDesList(): Array<IVoucherDescriptionModel> {
    let count = 0;
    const maxCount = 10;
    let list = new Array<IVoucherDescriptionModel>();
    voucherModel.value.voucherLines.forEach((v, index) => {
        if (count >= maxCount) return;
        if (!v.description.trim()) {
            return;
        }
        if (list.filter((i) => i.description.trim() === v.description.trim()).length > 0) {
            return;
        }
        if (!desListSearchInfo.value.trim() || v.description.trim().indexOf(desListSearchInfo.value.trim()) !== -1) {
            if (index === editingInfo.editingIndex && v.description.trim() !== recordDescription.value.trim()) {
                //
            } else {
                list.push({ description: v.description.trim(), acronym: "", pinyin: "" });
            }
            count++;
        }
    });
    desList.forEach((d) => {
        if (count >= maxCount) return;
        if (list.filter((i) => i.description.trim() === d.description.trim()).length > 0) {
            return;
        }
        if (
            !desListSearchInfo.value.trim() ||
            d.description.trim().indexOf(desListSearchInfo.value.trim()) !== -1 ||
            d.acronym.trim().indexOf(desListSearchInfo.value.trim()) !== -1 ||
            d.pinyin.trim().indexOf(desListSearchInfo.value.trim()) !== -1
        ) {
            list.push(d);
            count++;
        }
    });
    return list;
}
function smoothScroll(targetPosition: number, duration: number) {
    const routerContainer = document.querySelector(".router-container");
    const startTime = performance.now();
    function scrollAnimation(currentTime: number) {
        const elapsedTime = currentTime - startTime;
        routerContainer?.scrollTo({
            top: targetPosition,
        });
        if (elapsedTime < duration) {
            requestAnimationFrame(scrollAnimation);
        }
    }
    requestAnimationFrame(scrollAnimation);
}
let aaeBoolean = false;

function showAsubSelector(type: number) {
    if (asubSelectorDisplay.value !== DisplayStatus.Hidden) {
        asubSelectorDisplay.value = DisplayStatus.Visible;
        selectAsubType(type);
        nextTick().then(() => {
            calcAsubSelectorPosition();
        });
    }
}

function asubFocus(event: any, index: number) {
    allowDrag.value = false;
    if (asubSelectorDisplay.value === DisplayStatus.Hidden) {
        asubSelectorDisplay.value = DisplayStatus.Loading;

        // 是否默认显示AI推荐，如果默认的话，需要先等一会
        if (window.isStopAIFunction) {
            showAsubSelector(0);
        } else if (voucherSettings.value.defaultAISuggestionStatus === 2) {
            getVoucherSuggestionAsync();
            showAsubSelector(0);
        } else {
            getVoucherSuggestionAsync()
                .then(() => {
                    if (voucherSuggestion.length > 0) {
                        showAsubSelector(9999);
                    } else {
                        showAsubSelector(0);
                    }
                })
                .catch(() => {
                    showAsubSelector(0);
                });
        }

        // 调用滚动动画函数来模拟滚动效果
        //最后一个及倒数第二个都可以
        if (index + 1 === voucherModel.value.voucherLines.length || index === voucherModel.value.voucherLines.length - 2) {
            aaeBoolean = true;
            smoothScroll(********, 100); // ********为目标滚动位置，100为动画持续时间（毫秒）
        } else {
            aaeBoolean = false;
        }
        let asubName = editingInfo.editingLine.asubAAName;
        if (checkHasTempAAE() || isTempVoucher.value) asubName = editingInfo.editingLine.asubName;
        event.currentTarget.value = asubName;
        if (editingInfo.editingLine.asubId !== 0 && asubDict[editingInfo.editingLine.asubId]) {
            if (editingInfo.editingLine.assistingAccounting === 1) {
                resetAsubAASelectorInfo();
            }
        } else {
            const isTempAsub = tempAsubList.some((item) => item.asubCode + " " + item.asubName === asubName);
            if (!isTempAsub && !isTempVoucher.value) {
                clearAsub(editingInfo.editingLine);
            }
        }
        asubSelectIndex.value = -1;
        document.addEventListener("mousedown", asubBlur);
        window.addEventListener("resize", calcAsubSelectorPosition);
        nextTick().then(() => {
            calcAsubSelectorPosition();
            // let container = document.querySelector(".router-container");
            // container && (container.addEventListener("mousedown", asubBlur));
        });
    } else {
        calcAsubSelectorPosition();
    }
}
function checkHasTempAAE() {
    if (!(props.queryParams instanceof DataVoucherQueryParams)) return false;
    const aaeIds = editingInfo.editingLine.aacode.split(",");
    const aaTypes = ((editingInfo.editingLine as any).aatype || "").split(",");
    // 有临时辅助核算项目
    let hasTempAAE = false;
    if (editingInfo.editingLine.assistingAccounting === 1 && aaeIds.length > 0 && aaeIds.some((item) => item === "0")) {
        for (let i = 0; i < aaeIds.length; i++) {
            if (aaeIds[i] !== "0") continue;
            if (tempAAEList.some((item) => item.aatype.toString() === aaTypes[i])) {
                hasTempAAE = true;
                break;
            }
        }
    }
    return hasTempAAE;
}

const appendMargin = ref(0);
const asubSelecterHeight = 256;
function calcAsubSelectorPosition() {
    const containerOffset = containerRef.value.getBoundingClientRect();
    let offset = editingInfo.editingTr.querySelector(".voucherline-asub textarea").getBoundingClientRect();
    asubSelector.value.style.left = offset.left - 1 - containerOffset.left + "px";
    const top = offset.top + offset.height - containerOffset.top;
    asubSelector.value.style.top = top + "px";
    asubSelector.value.style.width = editingInfo.editingTr.querySelector(".voucherline-asub textarea").clientWidth + 4 + "px";
    if (asubSelectorDisplay.value === DisplayStatus.Visible) {
        const bottom = containerOffset.height - top;
        if (bottom < asubSelecterHeight) {
            appendMargin.value = asubSelecterHeight - bottom;
        } else {
            appendMargin.value = 0;
        }
    }
}
const isFlag = ref(false);
watch(
    () => props.isInputAccout,
    () => {
        isFlag.value = props.isInputAccout;
        if (isFlag.value) {
            document.removeEventListener("mousedown", asubBlur);
            editingInfo.editingLine.assistingAccounting = 0;
            editingInfo.editingTr = null;
            editingInfo.editingType = "";
            editingInfo.editingLine.asubId = 0;
            asubSelectorDisplay.value = DisplayStatus.Hidden;
        }
    }
);
onActivated(() => {
    let target = voucherModel.value.voucherLines.findIndex(
        (v: VoucherEntryModel) => v.assistingAccounting === 1 && !checkVoucherLineAsub(v)
    );
    if (target > -1) {
        setWarningRow(target + 1);
        editingInfo.editingLine = voucherModel.value.voucherLines[target];
        const row = document.querySelector(`[data-line-number="${target + 1}"]`)?.querySelector(".voucherline-asub") as HTMLElement;
        if (row) {
            nextTick(() => {
                row.click();
            });
        }
    }
});
function asubBlur() {
    if (aaeAADialogDisplay.value) return;
    if (editingInfo.editingLine.asubId === 0) {
        let asubName = editingInfo.editingLine.asubAAName;
        if (checkHasTempAAE()) asubName = editingInfo.editingLine.asubName;
        const isTempAsub = tempAsubList.some((item) => item.asubCode + " " + item.asubName === asubName);
        if (!isTempAsub) {
            clearAsub(editingInfo.editingLine);
        }
    } else {
        const hasTempAAE = checkHasTempAAE();
        if (!checkVoucherLineAsub(editingInfo.editingLine, hasTempAAE)) {
            ElNotify({
                message: "亲，请选择辅助核算项目!",
                type: "warning",
            });
            return;
        }
    }
    asubBlurExecute();
}
function asubBlurExecute() {
    asubSelectorDisplay.value = DisplayStatus.Hidden;
    asubSearchInfo.value = "";
    aaeSelectorInfo.aaeSelectorDisplay = false;
    tryAppendVoucherLine(editingInfo.editingLine, "asubId");
    cancelEdit();
    document.removeEventListener("mousedown", asubBlur);
    // let container = document.querySelector(".router-container");
    // container && (container.removeEventListener("mousedown", asubBlur));
    window.removeEventListener("resize", calcAsubSelectorPosition);
}

function asubInput(event: any) {
    asubSearchInfo.value = event.currentTarget.value;
    if (asubSearchInfo.value.trim()) {
        asubSelectIndex.value = 0;
    } else {
        asubSelectIndex.value = -1;
    }
    asubSelector.value.querySelector(".asub-selector .asubs-content").scrollTop = 0;
    clearAsub(editingInfo.editingLine);
    const target = event.currentTarget;
    setTimeout(() => {
        const containerOffset = containerRef.value.getBoundingClientRect();
        let offset = target.getBoundingClientRect();
        asubSelector.value.style.left = offset.left - 1 - containerOffset.left + "px";
        asubSelector.value.style.top = offset.top + offset.height - containerOffset.top + "px";
    });
}

function selectAsubType(id: number) {
    asubTypeList.value.forEach((asubType) => {
        asubType.selected = false;
        if (asubType.id === id) {
            asubType.selected = true;
        }
    });
    nextTick(() => {
        asubSelector.value.querySelector(".asub-selector .asubs-content").scrollTop = 0;
    });
}

function asubKeydown(event: any) {
    const textarea = event.target as HTMLTextAreaElement;
    if (event.keyCode === 37) {
        if (checkHasSelection(textarea) || checkSelectionIsFirst(textarea)) {
            let debitDom = editingInfo.editingTr.querySelector(".voucherline-description");
            asubBlur();
            setTimeout(() => {
                debitDom.click();
            });
        }
    }
    if (event.keyCode === 38) {
        if (asubSelectIndex.value === -1) {
            asubSelectIndex.value = getAsubList().length - 1;
        } else {
            const nextIndex = asubSelectIndex.value - 1;
            asubSelectIndex.value = nextIndex === -1 ? getAsubList().length - 1 : nextIndex;
        }
        autoScroll(asubSelector.value.querySelector(".asub-selector .asubs-content"));
    }
    if (event.keyCode === 39) {
        if (checkHasSelection(textarea) || checkSelectionIsLast(textarea)) {
            let debitDom = editingInfo.editingTr.querySelector(".voucherline-amount");
            asubBlur();
            setTimeout(() => {
                debitDom.click();
            });
        }
    }
    if (event.keyCode === 40) {
        if (asubSelectIndex.value === -1) {
            asubSelectIndex.value = 0;
        } else {
            const nextIndex = asubSelectIndex.value + 1;
            asubSelectIndex.value = nextIndex === getAsubList().length ? 0 : nextIndex;
        }
        autoScroll(asubSelector.value.querySelector(".asub-selector .asubs-content"));
    }
    if (event.keyCode === 120) {
        event.preventDefault();
        event.stopPropagation();
        quickSettleBalance(editingInfo.editingLine, () => asubBlur());
    }
    if (event.keyCode === 13 || event.keyCode === 9) {
        if (asubSelectIndex.value !== -1 || (editingInfo.editingLine && editingInfo.editingLine.asubId !== 0)) {
            if (editingInfo.editingLine.assistingAccounting === 1) {
                event.currentTarget.blur();
                setTimeout(() => {
                    asubSelector.value.querySelector(".asub-aa-selector .aatype-content input").focus();
                });
                return;
            } else {
                if (asubSelectIndex.value !== -1) {
                    selectAsub(getAsubList()[asubSelectIndex.value]);
                    return;
                }
            }
        }
        let nextDom = editingInfo.editingTr.querySelector(".voucherline-amount");
        if (editingInfo.editingLine.asubId !== 0) {
            const direction = asubDict[editingInfo.editingLine.asubId].direction;
            const amountDoms = editingInfo.editingTr.querySelectorAll(".voucherline-amount");
            nextDom = getAsubNextDom(direction, amountDoms);
        }
        asubBlur();
        setTimeout(() => {
            nextDom.click();
        });
    }
    if (event.ctrlKey) {
        if (event.keyCode === 187) {
            event.preventDefault();
            const container = editingInfo.editingTr.parentNode.parentNode;
            const index = editingInfo.editingIndex + 1;
            asubBlur();
            addVoucherLine(editingInfo.editingIndex);
            setTimeout(() => {
                container.children[index].querySelector(".voucherline-asub").click();
            });
        } else if (event.keyCode === 189) {
            event.preventDefault();
            asubBlur();
            delVoucherLine(editingInfo.editingIndex);
        }
    }
    if (event.keyCode === 114) {
        if (editingInfo.editingIndex !== 0) {
            event.preventDefault();
            const dom = editingInfo.editingTr.querySelector(".voucherline-asub");
            copyLastAsub();
            asubBlur();
            setTimeout(() => {
                setTimeout(() => {
                    dom.click();
                });
            });
        }
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}
function copyLastAsub() {
    const lastVoucherLine = voucherModel.value.voucherLines[editingInfo.editingIndex - 1];
    const defaultValues = {
        asubId: 0,
        asubType: 0,
        asubName: "",
        asubAAName: "",
        assistingAccounting: 0,
        quantityAccounting: 0,
        foreigncurrency: 0,
        measureUnit: "",
        aacode: "",
        fcId: 1,
        fcRate: 0,
        fcCode: "",
    };
    const {
        asubId,
        asubType,
        asubName,
        asubAAName,
        assistingAccounting,
        quantityAccounting,
        foreigncurrency,
        measureUnit,
        aacode,
        fcId,
        fcRate,
        fcCode,
    } = { ...defaultValues, ...lastVoucherLine };
    const assignParams = {
        asubId,
        asubType,
        asubName,
        asubAAName,
        assistingAccounting,
        quantityAccounting,
        foreigncurrency,
        measureUnit,
        aacode,
        fcId,
        fcRate,
        fcCode,
    };
    Object.assign(editingInfo.editingLine, assignParams);
}

function asubKeyup(event: any) {
    const isCopy = event.ctrlKey && event.keyCode === 67;
    if (![9, 13, 17, 37, 38, 39, 40, 114, 120, 255].includes(event.keyCode) && !isCopy) {
        asubInput(event);
    }
}

function getAsubList(): Array<IAccountSubjectModel> {
    let asubType = asubTypeList.value.filter((a) => a.selected)[0]?.id;
    let list: Array<IAccountSubjectModel> = [];

    if (asubType === 9999) {
        const dictVoucherSuggestion: { [key: number]: number } = {};
        for (let i = 0; i < voucherSuggestion.length; i++) {
            dictVoucherSuggestion[voucherSuggestion[i]] = i + 1;
        }

        const templist = asubList.value.filter((i) => i.asubType === 0)[0]?.asubList;

        if (asubSearchInfo.value.trim()) {
            // 如果搜索，在全部里面搜索结果，但是将推荐科目提前
            const matchAsub = templist.find((i) => i.asubCode === asubSearchInfo.value.trim());
            if (matchAsub) {
                return [matchAsub];
            }
            list = templist.filter(
                (i) =>
                    (i.asubCode + " " + i.asubAAName).indexOf(asubSearchInfo.value.trim()) !== -1 ||
                    i.acronym.indexOf(asubSearchInfo.value.trim()) !== -1
            );
        } else {
            // 如果不搜索，直接返回推荐的科目
            list = templist.filter((i) => dictVoucherSuggestion[i.asubId]);
        }
        list = list.sort((a, b) => {
            let indexA = dictVoucherSuggestion[a.asubId];
            let indexB = dictVoucherSuggestion[b.asubId];

            indexA = indexA === undefined ? 9999 : indexA;
            indexB = indexB === undefined ? 9999 : indexB;

            return indexA - indexB;
        });
    } else {
        list = asubList.value.filter((i) => i.asubType === asubType)[0]?.asubList;

        if (asubSearchInfo.value.trim()) {
            const matchAsub = list.find((i) => i.asubCode === asubSearchInfo.value.trim());
            if (matchAsub) {
                return [matchAsub];
            }
            list = list.filter(
                (i) =>
                    (i.asubCode + " " + i.asubAAName).indexOf(asubSearchInfo.value.trim()) !== -1 ||
                    i.acronym.indexOf(asubSearchInfo.value.trim()) !== -1
            );
        }
    }

    return list;
}

function getAsubNextDom(direction: number, amountDoms: NodeListOf<Element>) {
    let nextDom: any;
    if (voucherSettings.value.amountDirectionByAsubStatus === 1) {
        nextDom = direction === 1 ? amountDoms[0] : amountDoms[1];
    } else {
        nextDom = amountDoms[0];
    }
    return nextDom;
}
function selectAsub(asub: IAccountSubjectModel) {
    const lastKey = editingInfo.editingLine.asubId + "_" + editingInfo.editingLine.aacode;
    if (asub.asubId !== editingInfo.editingLine.asubId) {
        asubSelecterAmountDict.value[lastKey] = asubAmountDict.value[lastKey];
    }
    editingInfo.editingTr.querySelector(".voucherline-asub .voucherline-textarea textarea").value = asub.asubCode + " " + asub.asubAAName;
    editingInfo.editingLine.asubId = asub.asubId;
    editingInfo.editingLine.asubType = asub.asubType;
    editingInfo.editingLine.asubCode = asub.asubCode;
    editingInfo.editingLine.asubName = asub.asubCode + " " + asub.asubAAName;
    editingInfo.editingLine.asubAAName = asub.asubCode + " " + asub.asubAAName;
    editingInfo.editingLine.quantityAccounting = asub.quantityAccounting;
    if (editingInfo.editingLine.quantityAccounting === 0) {
        editingInfo.editingLine.quantity = 0;
        editingInfo.editingLine.price = 0;
    }
    editingInfo.editingLine.measureUnit = asub.measureUnit;
    editingInfo.editingLine.assistingAccounting = asub.assistingAccounting;
    if (editingInfo.editingLine.assistingAccounting === 1) {
        editingInfo.editingLine.aacode = new Array(asub.aatypes.split(",").length).fill("").join(",");
    } else {
        editingInfo.editingLine.aacode = "";
    }
    editingInfo.editingLine.foreigncurrency = asub.foreigncurrency;
    if (editingInfo.editingLine.foreigncurrency === 0) {
        editingInfo.editingLine.fcId = 1;
        editingInfo.editingLine.fcRate = 0;
        editingInfo.editingLine.fcCode = "";
        editingInfo.editingLine.fcAmount = 0;
    } else {
        editingInfo.editingLine.fcId = Number(asub.fcIds.split(",")[0]);
        editingInfo.editingLine.fcRate = currencyDict[editingInfo.editingLine.fcId].rate;
        editingInfo.editingLine.fcCode = currencyDict[editingInfo.editingLine.fcId].code;
        amountChange(editingInfo.editingLine, "fcRate");
    }
    if (checkVoucherLineAsub(editingInfo.editingLine)) {
        let tr = editingInfo.editingTr;
        let nextDom: any;
        if (editingInfo.editingLine.quantityAccounting === 1) {
            nextDom = tr.querySelector(".voucherline-asub-quantity");
        } else if (editingInfo.editingLine.foreigncurrency === 1) {
            nextDom = tr.querySelector(".voucherline-asub-fc select");
        } else {
            const amountDoms = tr.querySelectorAll(".voucherline-amount");
            nextDom = getAsubNextDom(asub.direction, amountDoms);
        }
        asubBlur();
        setTimeout(() => {
            if (nextDom.nodeName === "SELECT") {
                nextDom.focus();
            } else {
                nextDom.click();
            }
        });
    }
    if (editingInfo.editingLine.assistingAccounting === 1) {
        editingInfo.editingLine.aacode = asub.aatypes
            .split(",")
            .map((aatype) => (asub.aatypesAllowNull.split(",").indexOf(aatype) === -1 ? "0" : "-" + aatype))
            .join(",");
        resetAsubAASelectorInfo();
        editingInfo.editingTr.querySelector(".voucherline-asub .voucherline-textarea textarea").blur();
        setTimeout(() => {
            editingInfo.editingTr.querySelector(".voucherline-asub .voucherline-textarea textarea").focus();
            setTimeout(() => {
                asubSelector.value.querySelector(".asub-aa-selector .aatype-content input").focus();
            });
        });
    }
    saveVoucherSuggestionCount(asub.asubId);
}

const generateCheckTip = ref('')
const isSalaryOrFaGenerate = computed(() => {
    return props.queryParams instanceof DataVoucherQueryParams && ['/salary/salarymanage', '/fixedassets/fixedassets'].includes(route.path.toLocaleLowerCase()) 
});
function checkVoucherLineWithoutAssistAccounting(voucherline: VoucherEntryModel, withTempItems: boolean = false): boolean {
    if (isVoucherReadonly.value) {
        return true;
    }
    if (withTempItems) {
        if (tempAsubList?.some((item) => item.asubCode === voucherline.asubCode)) {
            return true;
        }
    }
    if (!voucherline.asubId) {
        return false;
    }
    let asub = asubDict[voucherline.asubId];
    if (!asub) {
        // 科目停用
        if(isSalaryOrFaGenerate.value && accountSubjectStore.accountSubjectList.find((item) => item.asubId === voucherline.asubId)?.status === 1) {
            generateCheckTip.value = "科目已停用，请到科目列表启用该科目或重新选择其他可用科目！";
            return false;
        }
        return false;
    }
    if (asub.quantityAccounting !== voucherline.quantityAccounting) {
        return false;
    }
    if (asub.foreigncurrency !== voucherline.foreigncurrency) {
        return false;
    }
    if (asub.assistingAccounting !== voucherline.assistingAccounting) {
        return false;
    }
    if (voucherline.foreigncurrency === 1) {
        if (asub.fcIds.split(",").indexOf(voucherline.fcId.toString()) === -1) {
            return false;
        }
    }
    return true;
}

function checkVoucherLineAsub(voucherline: VoucherEntryModel, withTempItems: boolean = false): boolean {
    if (!checkVoucherLineWithoutAssistAccounting(voucherline, withTempItems)) {
        return false;
    }
    if (!checkVoucherLineAssistAccounting(voucherline, withTempItems)) {
        return false;
    }
    return true;
}

function checkVoucherLineAssistAccounting(voucherline: VoucherEntryModel, withTempItems: boolean = false): boolean {
    if (voucherline.assistingAccounting === 1) {
        let asub = asubDict[voucherline.asubId];
        const aaeIds = voucherline.aacode.split(",");
        if (!voucherline.aacode) {
            //未选择辅助核算
            isSalaryOrFaGenerate.value && (generateCheckTip.value = "请选择辅助核算项目!");
            return false;
        }
        if (
            aaeIds.filter(
                (a) =>
                    a.indexOf("-") === 0 &&
                    asub.aatypesAllowNull
                        .split(",")
                        .map((n: any) => `-${n}`)
                        .indexOf(a) === -1
            ).length > 0
        ) {
            return false;
        }
        for (let i = 0; i < aaeIds.length; i++) {
            const aaeId = aaeIds[i];
            if (aaeId.startsWith("-")) {
                continue;
            }
            if (!aaeDict[Number(aaeId)]) {
                if (withTempItems && aaeId === "0") {
                    if (
                        !tempAAEList?.some(
                            (item) =>
                                item.aatype === Number(asub.aatypes.split(",")[i]) &&
                                voucherline.asubName.indexOf(`_${item.num} ${item.name}`)
                        )
                    ) {
                        return false;
                    }
                } else {
                    isSalaryOrFaGenerate.value && (generateCheckTip.value = "请选择辅助核算项目!");
                    return false;
                }
            }
        }
    }
    return true;
}

function clearAsub(voucherline: VoucherEntryModel) {
    voucherline.asubId = 0;
    voucherline.asubType = 0;
    voucherline.asubCode = "";
    voucherline.asubName = "";
    voucherline.asubAAName = "";
    voucherline.foreigncurrency = 0;
    voucherline.quantityAccounting = 0;
    voucherline.measureUnit = "";
    voucherline.assistingAccounting = 0;
    voucherline.aacode = "";
}
clearAsub(editingInfo.editingLine);
const asubSelecterAmountDict = ref<any>({});
function getAmount(asubId: number) {
    if (checkIsVoucherTemplate()) return "";
    const key = asubId + "_";
    if (asubSelecterAmountDict.value[key] === undefined) {
        return "";
    }
    return asubSelecterAmountDict.value[key] ? asubSelecterAmountDict.value[key].toFixed(2) : "";
}
function getAaeAmount(aaeId: number) {
    if (checkIsVoucherTemplate()) return "";
    const key = editingInfo.editingLine.asubId + "_" + aaeId;
    if (asubSelecterAmountDict.value[key] === undefined) {
        return "";
    }
    return asubSelecterAmountDict.value[key] ? asubSelecterAmountDict.value[key].toFixed(2) : "";
}
function getAsubTotalAmount(voucherline: VoucherEntryModel): string {
    if (checkIsVoucherTemplate()) return "";
    let asub = asubDict[voucherline.asubId];
    if (!asub) return "0";
    if (!checkVoucherLineWithoutAssistAccounting(voucherline)) return "0";
    const aacode =
        checkVoucherLineAsub(voucherline) && (!aaeSelectorInfo.aaeSelectorDisplay || editingInfo.editingLine !== voucherline)
            ? voucherline.aacode
            : "";

    let key = voucherline.asubId + "_" + aacode;
    if (asubAmountDict.value[key] === undefined) {
        asubAmountDict.value[key] = 0;
    }
    let totalAmount = asubAmountDict.value[key];
    for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
        if (
            voucherModel.value.voucherLines[i].asubId === voucherline.asubId &&
            voucherModel.value.voucherLines[i].aacode === voucherline.aacode
        ) {
            totalAmount = Number(
                (
                    totalAmount +
                    (voucherModel.value.voucherLines[i].debit - voucherModel.value.voucherLines[i].credit) * (asub.direction === 1 ? 1 : -1)
                ).toFixed(2)
            );
        }
    }
    for (let i = 0; i < oldVoucherModel.value.voucherLines.length; i++) {
        if (
            oldVoucherModel.value.voucherLines[i].asubId === voucherline.asubId &&
            oldVoucherModel.value.voucherLines[i].aacode === voucherline.aacode
        ) {
            totalAmount = Number(
                (
                    totalAmount +
                    Number(
                        (
                            (oldVoucherModel.value.voucherLines[i].debit - oldVoucherModel.value.voucherLines[i].credit) *
                            (asub.direction === 1 ? -1 : 1)
                        ).toFixed(2)
                    )
                ).toFixed(2)
            );
        }
    }
    asubSelecterAmountDict.value[key] = totalAmount;
    return totalAmount.toFixed(2);
}

interface IAllTotalAmount {
    asubId: number;
    aacode: string;
    total: number;
}
function handleGetAllTotalAmount() {
    if (checkIsVoucherTemplate()) return "";
    const [year, month] = voucherModel.value.vdate.split("-");
    if (!year || !month) return;
    request({ url: "/api/AccountSubject/GetAllTotalAmount", method: "post", params: { year, month } }).then(
        (res: IResponseModel<Array<IAllTotalAmount>>) => {
            if (res.state === 1000) {
                const dict: any = {};
                const dictAae: any = {};
                res.data.forEach((item) => {
                    dict[item.asubId + "_" + item.aacode] = item.total;
                    dictAae[item.asubId + "_" + item.aacode] = item.total;
                });
                asubAmountDict.value = dict;
                asubSelecterAmountDict.value = dictAae;
            }
        }
    );
}

async function asubAmountClick(event: MouseEvent, voucherline: VoucherEntryModel) {
    event.stopPropagation();
    const action = (event.target as HTMLElement).dataset.action;
    if (action === "settleBalance") {
        // 获取余额
        const currentBalance = await getAsubTotalAmount(voucherline);
        if (currentBalance.split(".")[0].length > 9) {
            return ElNotify({
                message: "亲，借贷金额不能超过亿位哦~",
                type: "warning",
            });
        }
        settleBalance(voucherline);
    } else if (action === "gotoSubsidiaryLedger") {
        gotoSubsidiaryLedger(voucherline);
    }
}
function settleBalance(voucherline: VoucherEntryModel, blur?: () => void) {
    if (voucherModel.value.approveStatus === 1 || voucherModel.value.pstatus === 3 || Number(getAsubTotalAmount(voucherline)) === 0) {
        return;
    }
    voucherline.credit = 0;
    voucherline.debit = 0;
    const isCredit = asubDict[Number(voucherline.asubId)].direction === 1;
    if (isCredit) {
        voucherline.credit = Number(getAsubTotalAmount(voucherline));
    } else {
        voucherline.debit = Number(getAsubTotalAmount(voucherline));
    }
    if (blur) {
        let nextDom = editingInfo.editingTr.querySelector(".voucherline-amount");
        if (isCredit) {
            nextDom = editingInfo.editingTr.querySelectorAll(".voucherline-amount")[1];
        }
        const input = nextDom.querySelector("input");
        input.value = voucherline.credit.toFixed(2);
        input.select();
        blur();
        setTimeout(() => {
            nextDom.click();
        });
    }
}
async function quickSettleBalance(voucherline: VoucherEntryModel, blur?: () => void) {
    if (!voucherline.asubId) return;
    const currentBalance = await getAsubTotalAmount(voucherline);
    if (currentBalance.split(".")[0].length > 9) {
        ElNotify({
            message: "亲，借贷金额不能超过亿位哦~",
            type: "warning",
        });
        return;
    }
    settleBalance(voucherline, blur);
}
function resetAsubTotalAmount() {
    asubAmountDict.value = {};
}

function appendAsubTotalAmount() {
    if (checkIsVoucherTemplate()) return;
    const dict: any = {};
    voucherModel.value.voucherLines.forEach((vl) => {
        if (dict[vl.asubId + "_" + vl.aacode] === 0) return;
        dict[vl.asubId + "_" + vl.aacode] = 0;
        asubAmountDict.value[vl.asubId + "_" + vl.aacode] = Number(getAsubTotalAmount(vl));
    });
    for (const key in asubAmountDict.value) {
        if (dict[key] !== 0) {
            delete asubAmountDict.value[key];
        }
    }
}

function gotoSubsidiaryLedger(voucherline: VoucherEntryModel) {
    for (let i = 0; i < periodStore.periodList.length; i++) {
        if (
            new Date(voucherModel.value.vdate + "T00:00:00").getTime() >= new Date(periodStore.periodList[i].startDate).getTime() &&
            new Date(voucherModel.value.vdate + "T00:00:00").getTime() <= new Date(periodStore.periodList[i].endDate).getTime()
        ) {
            useFullScreenStore().changeFullScreenStage(false);
            globalWindowOpenPage(
                `/AccountBooks/SubsidiaryLedger?ASUB_ID=${voucherline.asubId}&period_s=${periodStore.periodList[i].pid}&period_e=${
                    periodStore.periodList[i].pid
                }&ran=${Math.random()}`,
                "明细账"
            );
            return;
        }
    }
}

function resetAsubAASelectorInfo() {
    asubAASelectorInfo.value = new Array<{
        aaType: number;
        aaTypeName: string;
        allowNull: boolean;
        aaeId: number;
        input: string;
    }>();
    let asub = asubDict[editingInfo.editingLine.asubId];
    if (asub && asub.assistingAccounting === 1) {
        let aaeIds = editingInfo.editingLine.aacode.split(",");
        let aaTypes = asub.aatypes.split(",");
        let aatypeNames = asub.aatypeNames.split(",");
        let aatypesAllowNull = asub.aatypesAllowNull.split(",");
        for (let i = 0; i < aaTypes.length; i++) {
            let aaeId = Number(aaeIds[i] || "0");
            let aaeName = "";
            let aae = aaeDict[aaeId];
            if (!aae) {
                if (aatypesAllowNull.indexOf(aaTypes[i]) !== -1) {
                    aaeId = -Number(aaTypes[i]);
                } else {
                    aaeId = 0;
                }
            } else {
                aaeName = aae.aanum + " " + aae.aaname;
            }
            asubAASelectorInfo.value.push({
                aaType: Number(aaTypes[i]),
                aaTypeName: aatypeNames[i],
                allowNull: aatypesAllowNull.indexOf(aaTypes[i]) !== -1,
                aaeId: aaeId,
                input: aaeName,
            });
        }
    }
}

function getAAEList(): Array<{ id: number; name: string; model?: any }> {
    if (aaeSelectorInfo.aaType === 0) {
        return new Array<{ id: number; name: string }>();
    }
    let list = aaeGroup.value[aaeSelectorInfo.aaType] || [];
    if (aaeSelectorInfo.searchInfo) {
        list = list.filter(
            (aae: any) => aae.name.indexOf(aaeSelectorInfo.searchInfo) !== -1 || aae.zjm.indexOf(aaeSelectorInfo.searchInfo) !== -1
        );
        if (aaeSelectorInfo.selectIndex === -1 && list.length > 0) {
            aaeSelectorInfo.selectIndex = 0;
        }
    }
    return list;
}
function aaeFocus(event: any, aaType: number, index: number) {
    if (aaeBoolean) {
        smoothScroll(********, 100);
    }
    aaeSelectorInfo.aaeSelectorDisplay = true;
    aaeSelectorInfo.aaType = aaType;
    aaeSelectorInfo.index = index;
    aaeSelectorInfo.searchInfo = "";
    aaeSelectorInfo.selectIndex = -1;
    event.currentTarget.select();
    const containerOffset = containerRef.value.getBoundingClientRect();
    let offset = event.currentTarget.getBoundingClientRect();
    aaeSelector.value.style.left = offset.left - 8 - containerOffset.left + "px";
    aaeSelector.value.style.top = offset.top + offset.height + 5 - containerOffset.top + "px";
    aaeSelector.value.style.width = offset.width + 15 + "px";
    document.addEventListener("mousedown", aaeBlur);
}

function aaeBlur() {
    if (aaeAADialogDisplay.value) return;
    let aatype = asubAASelectorInfo.value[aaeSelectorInfo.index];
    if (aatype.aaeId === 0 || aatype.aaeId < 0) {
        aatype.input = "";
    }
    aaeSelectorInfo.aaeSelectorDisplay = false;
    aaeSelectorInfo.aaType = 0;
    aaeSelectorInfo.index = 0;
    aaeSelectorInfo.searchInfo = "";
    aaeSelectorInfo.selectIndex = -1;
    document.removeEventListener("mousedown", aaeBlur);
}

function aaeInput() {
    let aatype = asubAASelectorInfo.value[aaeSelectorInfo.index];
    aaeSelectorInfo.searchInfo = aatype.input;
    aaeSelectorInfo.selectIndex = -1;
    if (aatype.allowNull) {
        aatype.aaeId = -aatype.aaType;
    } else {
        aatype.aaeId = 0;
    }
    editingInfo.editingLine.aacode = asubAASelectorInfo.value.map((i) => i.aaeId).join(",");
    editingInfo.editingLine.asubName =
        editingInfo.editingLine.asubAAName +
        asubAASelectorInfo.value
            .filter((i) => !!aaeDict[i.aaeId])
            .map((i) => "_" + aaeDict[i.aaeId].aanum + " " + aaeDict[i.aaeId].aaname)
            .join("");
}

function selectAAE(id: number, name: string) {
    const lastKey = editingInfo.editingLine.asubId + "_" + editingInfo.editingLine.aacode;
    let aatype = asubAASelectorInfo.value[aaeSelectorInfo.index];
    aatype.aaeId = id;
    aatype.input = name;
    if (aaeSelectorInfo.index !== asubAASelectorInfo.value.length - 1) {
        let aaeSelectorInfoIndex = aaeSelectorInfo.index;
        setTimeout(() => {
            asubSelector.value
                .querySelectorAll(".asub-aa-selector .aatype-content tr")
                ?.[aaeSelectorInfoIndex + 1].querySelector("input")
                .focus();
        });
    } else {
        setTimeout(() => {
            // asubSelector.value
            //     .querySelectorAll(".asub-aa-selector .aatype-content tr")
            //     ?.[asubAASelectorInfo.value.length - 1].querySelector("input")
            //     .focus();
            aaeSelectorInfo.aaeSelectorDisplay = false;
        });
    }
    aaeBlur();
    editingInfo.editingLine.aacode = asubAASelectorInfo.value.map((i) => i.aaeId).join(",");
    const currentKey = editingInfo.editingLine.asubId + "_" + editingInfo.editingLine.aacode;
    if (lastKey !== currentKey) {
        asubSelecterAmountDict.value[lastKey] = asubAmountDict.value[lastKey] || 0;
    }
    editingInfo.editingLine.asubName =
        editingInfo.editingLine.asubAAName +
        asubAASelectorInfo.value
            .filter((i) => !!aaeDict[i.aaeId])
            .map((i) => "_" + aaeDict[i.aaeId].aanum + " " + aaeDict[i.aaeId].aaname)
            .join("");
}
watch(asubSelectorDisplay, (newValue, oldValue) => {
    if (oldValue !== DisplayStatus.Hidden && newValue === DisplayStatus.Hidden) {
        asubSelector.value
            ?.querySelectorAll(".asub-aa-selector .aatype-content tr")
            ?.[asubAASelectorInfo.value.length - 1]?.querySelector("input")
            ?.blur();
    }
});

function aaeKeydown(event: any) {
    if (event.keyCode === 38) {
        if (aaeSelectorInfo.selectIndex === -1) {
            aaeSelectorInfo.selectIndex = 0;
        } else {
            aaeSelectorInfo.selectIndex = Math.max(0, aaeSelectorInfo.selectIndex - 1);
        }
        aaeSelector.value.querySelectorAll(".aaes div")[aaeSelectorInfo.selectIndex].scrollIntoView();
        autoScroll(aaeSelector.value.querySelector(".aaes"));
    }
    if (event.keyCode === 40) {
        if (aaeSelectorInfo.selectIndex === -1) {
            aaeSelectorInfo.selectIndex = 0;
        } else {
            aaeSelectorInfo.selectIndex = Math.min(getAAEList().length - 1, aaeSelectorInfo.selectIndex + 1);
        }
        autoScroll(aaeSelector.value.querySelector(".aaes"));
    }
    if (event.keyCode === 13 || event.keyCode === 9) {
        let aaeSelectorInfoIndex = aaeSelectorInfo.index;
        if (aaeSelectorInfo.selectIndex !== -1) {
            selectAAE(getAAEList()[aaeSelectorInfo.selectIndex].id, getAAEList()[aaeSelectorInfo.selectIndex].name);
        }
        if (aaeSelectorInfoIndex === asubAASelectorInfo.value.length - 1) {
            let tr = editingInfo.editingTr;
            let nextDom: any;
            if (editingInfo.editingLine.quantityAccounting === 1) {
                nextDom = tr.querySelector(".voucherline-asub-quantity");
            } else if (editingInfo.editingLine.foreigncurrency === 1) {
                nextDom = tr.querySelector(".voucherline-asub-fc select");
            } else {
                const direction = asubDict[editingInfo.editingLine.asubId].direction;
                const amountDoms = tr.querySelectorAll(".voucherline-amount");
                nextDom = getAsubNextDom(direction, amountDoms);
            }
            asubBlur();
            setTimeout(() => {
                if (nextDom.nodeName === "SELECT") {
                    nextDom.focus();
                } else {
                    nextDom.click();
                }
            });
        } else {
            asubSelector.value.querySelectorAll(".asub-aa-selector .aatype-content tr")[aaeSelectorInfoIndex].querySelector("input").blur();
            setTimeout(() => {
                asubSelector.value
                    .querySelectorAll(".asub-aa-selector .aatype-content tr")
                    ?.[aaeSelectorInfoIndex + 1].querySelector("input")
                    .focus();
            });
        }
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function getFcList(voucherline: VoucherEntryModel): Array<CurrencyModel> {
    let list = new Array<CurrencyModel>();
    let asub = asubDict[voucherline.asubId];
    if (voucherline.asubId === 0 && tempAsubList?.some((item) => item.asubCode === voucherline.asubCode)) {
        asub = accountSubjectStore.accountSubjectList
            .filter((item) => voucherline.asubCode.startsWith(item.asubCode))
            .sort((a, b) => b.asubCode.length - a.asubCode.length)[0];
    }
    if (asub && asub.foreigncurrency === 1) {
        let fcIds = asub.fcIds.split(",").map((f: any) => Number(f));
        list = currencyList.value.filter((f) => fcIds.indexOf(f.id) !== -1);
    }
    return list;
}

function fcChange(voucherline: VoucherEntryModel) {
    let fc = currencyDict[voucherline.fcId];
    voucherline.fcRate = fc.rate;
    voucherline.fcCode = fc.code;
    amountChange(voucherline, "fcRate");
}

function quantityKeydown(event: any) {
    if (event.keyCode === 13 || event.keyCode === 9) {
        let priceDom = editingInfo.editingTr.querySelector(".voucherline-asub-price");
        amountBlur();
        setTimeout(() => {
            setTimeout(() => {
                priceDom.click();
            });
        });
    } else {
        event.currentTarget.attributes["oldValue"] = event.currentTarget.value;
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function priceKeydown(event: any) {
    if (event.keyCode === 13 || event.keyCode === 9) {
        if (editingInfo.editingLine.foreigncurrency === 1) {
            let fcDom = editingInfo.editingTr.querySelector(".voucherline-asub-fc select");
            amountBlur();
            setTimeout(() => {
                setTimeout(() => {
                    fcDom.focus();
                });
            });
        } else {
            const direction = asubDict[editingInfo.editingLine.asubId].direction;
            const amountDoms = editingInfo.editingTr.querySelectorAll(".voucherline-amount");
            let nextDom = amountDoms[0];
            if (amountDirectionFlag.value) {
                nextDom = amountDoms[amountDoms.length - 1];
                amountDirectionFlag.value = false;
            } else {
                nextDom = getAsubNextDom(direction, amountDoms);
            }
            amountBlur();
            setTimeout(() => {
                setTimeout(() => {
                    nextDom.click();
                });
            });
        }
    } else {
        event.currentTarget.attributes["oldValue"] = event.currentTarget.value;
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function priceFocus() {
    if (editingInfo.editingLine.assistingAccounting === 1 && asubDict[editingInfo.editingLine.asubId].aatypes === "10006") {
        const costAccountingSetting = getCostAccountingSetting();
        if (costAccountingSetting && !editingInfo.editingLine.debit && editingInfo.editingLine.quantity !== 0) {
            request({
                url: "/api/Voucher/GetCostAccounting",
                method: "post",
                params: {
                    type: costAccountingSetting,
                    asubId: editingInfo.editingLine.asubId,
                    fcId: editingInfo.editingLine.fcId,
                    aaCode: editingInfo.editingLine.aacode,
                    quantity: editingInfo.editingLine.quantity,
                },
            }).then((res: IResponseModel<number>) => {
                if (res.state === 1000) {
                    editingInfo.editingLine.price = res.data;
                    amountChange(editingInfo.editingLine, editingInfo.editingType);
                }
            });
        }
    }
}

function canSetCostAccounting(voucherline: VoucherEntryModel) {
    let asub = asubDict[voucherline.asubId];
    if (asub && asub.quantityAccounting === 1 && asub.assistingAccounting === 1) {
        if (asub.aatypes === "10006") {
            return true;
        }
    }
    return false;
}

function fcKeydown(event: any) {
    if (event.keyCode === 13 || event.keyCode === 9) {
        if (editingInfo.editingLine.fcId === 1) {
            let fcamountDom = editingInfo.editingTr.querySelector(".voucherline-asub-fcamount");
            event.currentTarget.blur();
            setTimeout(() => {
                setTimeout(() => {
                    fcamountDom.click();
                });
            });
        } else {
            let fcrateDom = editingInfo.editingTr.querySelector(".voucherline-asub-fcrate");
            event.currentTarget.blur();
            setTimeout(() => {
                setTimeout(() => {
                    fcrateDom.click();
                });
            });
        }
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function fcrateKeydown(event: any) {
    if (event.keyCode === 13 || event.keyCode === 9) {
        let fcamountDom = editingInfo.editingTr.querySelector(".voucherline-asub-fcamount");
        amountBlur();
        setTimeout(() => {
            setTimeout(() => {
                fcamountDom.click();
            });
        });
    } else {
        event.currentTarget.attributes["oldValue"] = event.currentTarget.value;
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function fcamountKeydown(event: any) {
    if (event.keyCode === 13 || event.keyCode === 9) {
        const direction = asubDict[editingInfo.editingLine.asubId].direction;
        const amountDoms = editingInfo.editingTr.querySelectorAll(".voucherline-amount");
        const nextDom = getAsubNextDom(direction, amountDoms);
        amountBlur();
        setTimeout(() => {
            setTimeout(() => {
                nextDom.click();
            });
        });
    } else {
        event.currentTarget.attributes["oldValue"] = event.currentTarget.value;
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function numberInput(event: any, decimal: number) {
    const value = event.currentTarget.value;
    if (value === "-") return;
    if (isNaN(Number(value)) || Number(value) >= ********00 || (value.indexOf(".") !== -1 && value.split(".")[1].length > decimal)) {
        const oldValue = event.currentTarget.attributes["oldValue"] || 0;
        if (editingInfo.editingType === "quantity") {
            editingInfo.editingLine.quantity = oldValue;
        }
        if (editingInfo.editingType === "price") {
            editingInfo.editingLine.price = oldValue;
        }
        if (editingInfo.editingType === "fcAmount") {
            editingInfo.editingLine.fcAmount = oldValue;
        }
        if (editingInfo.editingType === "fcRate") {
            editingInfo.editingLine.fcRate = oldValue;
        }
    }
}

function debitKeydown(event: any) {
    const input = event.target as HTMLInputElement;
    if (event.keyCode === 13 || event.keyCode === 9) {
        if (!Number(event.currentTarget.value)) {
            let creditDom = editingInfo.editingTr.querySelectorAll(".voucherline-amount")[1];
            event.currentTarget.blur();
            setTimeout(() => {
                setTimeout(() => {
                    creditDom.click();
                });
            });
        } else {
            toNextLine(event);
        }
    } else if (event.keyCode === 32) {
        event.preventDefault();
        const debit = Number(event.currentTarget.value) || 0;
        const credit = editingInfo.editingLine.credit;
        editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
        if (credit !== 0 && debit === 0) {
            editingInfo.editingLine.debit = credit;
            event.currentTarget.value = credit.toFixed(2);
            amountZoomOutInfo.twinkleIndex = -1;
        } else {
            editingInfo.editingLine.credit = Number(event.currentTarget.value) || 0;
            const creditDom = editingInfo.editingTr.querySelectorAll(".voucherline-amount")[1];
            event.currentTarget.value = "";
            event.currentTarget.blur();
            setTimeout(() => {
                setTimeout(() => {
                    creditDom.click();
                });
            });
        }
    } else if (
        (event.keyCode === 187 && !event.shiftKey && !event.ctrlKey) ||
        (event.keyCode === 229 && event.code === "Equal" && !event.shiftKey) ||
        event.keyCode === 61
    ) {
        editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
        event.currentTarget.value = Number((getTotal("credit") - getTotal("debit")).toFixed(2));
    } else if (event.keyCode === 37) {
        if (checkSelectionIsFirst(input)) {
            let asubDom = editingInfo.editingTr.querySelector(".voucherline-asub");
            input.blur();
            setTimeout(() => {
                asubDom.click();
            });
        }
    } else if (event.keyCode === 38) {
        if (editingInfo.editingIndex !== 0 && (checkHasSelection(input) || checkSelectionIsFirst(input))) {
            const targetIndex = editingInfo.editingIndex - 1;
            const voucherLinesContainer = editingInfo.editingTr.parentNode.parentNode;
            if (voucherLinesContainer) {
                const targetTr = voucherLinesContainer.children[targetIndex];
                if (targetTr) {
                    const asubDom = targetTr.querySelector(".voucherline-amount");
                    input.blur();
                    setTimeout(() => {
                        asubDom.click();
                    });
                }
            }
        }
    } else if (event.keyCode === 39) {
        if (checkSelectionIsLast(input)) {
            let asubDom = editingInfo.editingTr.querySelectorAll(".voucherline-amount")[1];
            input.blur();
            setTimeout(() => {
                asubDom.click();
            });
        }
    } else if (event.keyCode === 40) {
        if (
            editingInfo.editingIndex !== voucherModel.value.voucherLines.length - 1 &&
            (checkHasSelection(input) || checkSelectionIsLast(input))
        ) {
            const targetIndex = editingInfo.editingIndex + 1;
            const voucherLinesContainer = editingInfo.editingTr.parentNode.parentNode;
            if (voucherLinesContainer) {
                const targetTr = voucherLinesContainer.children[targetIndex];
                if (targetTr) {
                    const asubDom = targetTr.querySelector(".voucherline-amount");
                    input.blur();
                    setTimeout(() => {
                        asubDom.click();
                    });
                }
            }
        }
    } else if (event.keyCode === 114) {
        if (editingInfo.editingIndex !== 0) {
            event.preventDefault();
            const lastDebit = voucherModel.value.voucherLines[editingInfo.editingIndex - 1]?.debit || 0;
            const dom = editingInfo.editingTr.querySelector(".voucherline-amount");
            editingInfo.editingLine.credit = 0;
            editingInfo.editingLine.debit = lastDebit;
            input.value = lastDebit.toFixed(2);
            input.blur();
            setTimeout(() => {
                dom.click();
            });
        }
    } else if (event.keyCode === 120) {
        event.preventDefault();
        event.stopPropagation();
        quickSettleBalance(editingInfo.editingLine, () => event.currentTarget.blur());
    } else if (event.keyCode === 187 && event.ctrlKey) {
        event.preventDefault();
        const container = editingInfo.editingTr.parentNode.parentNode;
        const index = editingInfo.editingIndex + 1;
        event.currentTarget.blur();
        addVoucherLine(editingInfo.editingIndex);
        setTimeout(() => {
            container.children[index].querySelector(".voucherline-amount").click();
        });
    } else if (event.keyCode === 189 && event.ctrlKey) {
        event.preventDefault();
        event.currentTarget.blur();
        delVoucherLine(editingInfo.editingIndex);
    } else if (event.keyCode === 90 && event.ctrlKey) {
        if (debitHistoryStack.currentVoucherLine === debitHistoryStack.cacheVoucherLine) {
            const oldValue = debitHistoryStack.popStack();
            editingInfo.editingLine.debit = Number(oldValue) || 0;
            event.currentTarget.value = oldValue;
        }
    } else if (event.keyCode === 89 && event.ctrlKey) {
        if (debitHistoryStack.currentVoucherLine === debitHistoryStack.cacheVoucherLine) {
            const oldValue = debitHistoryStack.popHistory();
            editingInfo.editingLine.debit = Number(oldValue) || 0;
            event.currentTarget.value = oldValue;
        }
    } else if (!handleCalcKeydown(event)) {
        event.currentTarget.attributes["oldValue"] = event.currentTarget.value;
        if (event.keyCode === 123) {
            document.onkeydown && document.onkeydown(event);
        }
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function debitKeyup(event: any) {
    if (
        (event.keyCode === 187 && !event.shiftKey && !event.ctrlKey) ||
        (event.keyCode === 229 && event.code === "Equal" && !event.shiftKey) ||
        event.keyCode === 61
    ) {
        editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
        event.currentTarget.value = Number((getTotal("credit") - getTotal("debit")).toFixed(2));
    }

    amountKeyup(event);
    syncAmount(event);
}

function creditKeydown(event: any) {
    const input = event.target as HTMLInputElement;
    if (event.keyCode === 13 || event.keyCode === 9) {
        if (Number(event.currentTarget.value) || editingInfo.editingLine.debit) {
            toNextLine(event);
        } else {
            editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
            editingInfo.editingLine.debit = Number(event.currentTarget.value) || 0;
            let debittDom = editingInfo.editingTr.querySelectorAll(".voucherline-amount")[0];
            event.currentTarget.value = "";
            event.currentTarget.blur();
            setTimeout(() => {
                debittDom.click();
            });
        }
    } else if (event.keyCode === 32) {
        event.preventDefault();
        const debit = editingInfo.editingLine.debit;
        const credit = Number(event.currentTarget.value) || 0;
        editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
        if (debit !== 0 && credit === 0) {
            editingInfo.editingLine.credit = debit || 0;
            event.currentTarget.value = debit.toFixed(2);
            amountZoomOutInfo.twinkleIndex = -1;
        } else {
            editingInfo.editingLine.debit = Number(event.currentTarget.value) || 0;
            const debitDom = editingInfo.editingTr.querySelector(".voucherline-amount");
            event.currentTarget.value = "";
            event.currentTarget.blur();
            setTimeout(() => {
                setTimeout(() => {
                    debitDom.click();
                });
            });
        }
    } else if (
        (event.keyCode === 187 && !event.shiftKey && !event.ctrlKey) ||
        (event.keyCode === 229 && event.code === "Equal" && !event.shiftKey) ||
        event.keyCode === 61
    ) {
        editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
        event.currentTarget.value = Number((getTotal("debit") - getTotal("credit")).toFixed(2));
    } else if (event.keyCode === 37) {
        if (checkSelectionIsFirst(input)) {
            let debitDom = editingInfo.editingTr.querySelector(".voucherline-amount");
            input.blur();
            setTimeout(() => {
                debitDom.click();
            });
        }
    } else if (event.keyCode === 38) {
        if (editingInfo.editingIndex !== 0 && (checkHasSelection(input) || checkSelectionIsFirst(input))) {
            const targetIndex = editingInfo.editingIndex - 1;
            const voucherLinesContainer = editingInfo.editingTr.parentNode.parentNode;
            if (voucherLinesContainer) {
                const targetTr = voucherLinesContainer.children[targetIndex];
                if (targetTr) {
                    const creditDom = targetTr.querySelectorAll(".voucherline-amount")[1];
                    input.blur();
                    setTimeout(() => {
                        creditDom.click();
                    });
                }
            }
        }
    } else if (event.keyCode === 39) {
        if (editingInfo.editingIndex !== voucherModel.value.voucherLines.length - 1 && checkSelectionIsLast(input)) {
            const targetIndex = editingInfo.editingIndex + 1;
            const voucherLinesContainer = editingInfo.editingTr.parentNode.parentNode;
            if (voucherLinesContainer) {
                const targetTr = voucherLinesContainer.children[targetIndex];
                if (targetTr) {
                    const descriptionDom = targetTr.querySelector(".voucherline-description");
                    input.blur();
                    setTimeout(() => {
                        descriptionDom.click();
                    });
                }
            }
        }
    } else if (event.keyCode === 40) {
        if (
            editingInfo.editingIndex !== voucherModel.value.voucherLines.length - 1 &&
            (checkHasSelection(input) || checkSelectionIsLast(input))
        ) {
            const targetIndex = editingInfo.editingIndex + 1;
            const voucherLinesContainer = editingInfo.editingTr.parentNode.parentNode;
            if (voucherLinesContainer) {
                const targetTr = voucherLinesContainer.children[targetIndex];
                if (targetTr) {
                    const asubDom = targetTr.querySelectorAll(".voucherline-amount")[1];
                    input.blur();
                    setTimeout(() => {
                        asubDom.click();
                    });
                }
            }
        }
    } else if (event.keyCode === 114) {
        if (editingInfo.editingIndex !== 0) {
            event.preventDefault();
            const lastCredit = voucherModel.value.voucherLines[editingInfo.editingIndex - 1]?.credit || 0;
            const dom = editingInfo.editingTr.querySelectorAll(".voucherline-amount")[1];
            editingInfo.editingLine.debit = 0;
            editingInfo.editingLine.credit = lastCredit;
            input.value = lastCredit.toFixed(2);
            input.blur();
            setTimeout(() => {
                dom.click();
            });
        }
    } else if (event.keyCode === 120) {
        event.preventDefault();
        event.stopPropagation();
        quickSettleBalance(editingInfo.editingLine, () => event.currentTarget.blur());
    } else if (event.keyCode === 187 && event.ctrlKey) {
        event.preventDefault();
        const container = editingInfo.editingTr.parentNode.parentNode;
        const index = editingInfo.editingIndex + 1;
        event.currentTarget.blur();
        addVoucherLine(editingInfo.editingIndex);
        setTimeout(() => {
            container.children[index].querySelectorAll(".voucherline-amount")[1].click();
        });
    } else if (event.keyCode === 189 && event.ctrlKey) {
        event.preventDefault();
        event.currentTarget.blur();
        delVoucherLine(editingInfo.editingIndex);
    } else if (event.keyCode === 90 && event.ctrlKey) {
        if (creditHistoryStack.currentVoucherLine === creditHistoryStack.cacheVoucherLine) {
            const oldValue = creditHistoryStack.popStack();
            editingInfo.editingLine.credit = Number(oldValue) || 0;
            event.currentTarget.value = oldValue;
        }
    } else if (event.keyCode === 89 && event.ctrlKey) {
        if (creditHistoryStack.currentVoucherLine === creditHistoryStack.cacheVoucherLine) {
            const oldValue = creditHistoryStack.popHistory();
            editingInfo.editingLine.credit = Number(oldValue) || 0;
            event.currentTarget.value = oldValue;
        }
    } else if (!handleCalcKeydown(event)) {
        event.currentTarget.attributes["oldValue"] = event.currentTarget.value;
        if (event.keyCode === 123) {
            document.onkeydown && document.onkeydown(event);
        }
    }
    if (event.keyCode === 9) {
        event.preventDefault();
    }
}

function creditKeyup(event: any) {
    if (
        (event.keyCode === 187 && !event.shiftKey && !event.ctrlKey) ||
        (event.keyCode === 229 && event.code === "Equal" && !event.shiftKey) ||
        event.keyCode === 61
    ) {
        editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
        event.currentTarget.value = Number((getTotal("debit") - getTotal("credit")).toFixed(2));
    }

    amountKeyup(event);
    syncAmount(event);
}

function handleCalcKeydown(event: any) {
    if (
        event.keyCode == 106 ||
        event.keyCode == 107 ||
        event.keyCode == 111 ||
        event.keyCode == 191 ||
        (event.shiftKey && (event.keyCode == 56 || event.keyCode == 57 || event.keyCode == 48 || event.keyCode == 187)) ||
        (event.currentTarget.selectionStart !== 0 &&
            (event.keyCode == 109 ||
                event.keyCode == 189 ||
                (event.keyCode === 229 && (event.code === "Minus" || event.code === "NumpadSubtract")))) ||
        (event.keyCode === 229 &&
            (event.code === "NumpadAdd" ||
                event.code === "NumpadMultiply" ||
                event.code === "NumpadDivide" ||
                event.code === "Slash" ||
                (event.shiftKey &&
                    (event.code === "Equal" || event.code === "Digit8" || event.code === "Digit9" || event.code === "Digit0"))))
    ) {
        amountCalcInfo.display = true;
        amountCalcInfo.editingInfo.editingType = editingInfo.editingType;
        amountCalcInfo.editingInfo.editingLine = editingInfo.editingLine;
        amountCalcInfo.editingInfo.editingTr = editingInfo.editingTr;
        let operator = "";
        switch (event.keyCode) {
            case 106:
            case 56:
                operator += "*";
                break;
            case 107:
            case 187:
                operator += "+";
                break;
            case 111:
            case 191:
                operator += "/";
                break;
            case 109:
            case 189:
                operator += "-";
                break;
            case 48:
                operator += ")";
                break;
            case 57:
                operator += "(";
                break;
            case 229:
                switch (event.code) {
                    case "NumpadAdd":
                    case "Equal":
                        operator += "+";
                        break;
                    case "NumpadSubtract":
                    case "Minus":
                        operator += "-";
                        break;
                    case "NumpadMultiply":
                    case "Digit8":
                        operator += "*";
                        break;
                    case "NumpadDivide":
                    case "Slash":
                        operator += "/";
                        break;
                    case "Digit9":
                        operator += "(";
                        break;
                    case "Digit0":
                        operator += ")";
                        break;
                }
                break;
        }
        nextTick(() => {
            if (event.currentTarget.selectionStart === event.currentTarget.selectionEnd) {
                amountCalcInfo.calculator =
                    event.currentTarget.value.substring(0, event.currentTarget.selectionStart) +
                    operator +
                    event.currentTarget.value.substring(event.currentTarget.selectionStart);
            } else {
                amountCalcInfo.calculator = event.currentTarget.value.substring(event.currentTarget.selectionStart) + operator;
            }
            // amountCalcInfo.calculator =
            //     event.currentTarget.value.substring(0, event.currentTarget.selectionStart) +
            //     operator +
            //     event.currentTarget.value.substring(event.currentTarget.selectionStart);
            setTimeout(() => {
                amountCalculator.value.focus();
            });
        });
        return true;
    }
}
function confirmCalculatResult() {
    if (Number(amountCalcResult.value) > *********.99) {
        ElNotify({
            message: "您输入的数字超过了有效范围！",
            type: "warning",
        });
        amountCalcInfo.calculator = "";
        return;
    }
    amountCalcInfo.display = false;
    if (amountCalcInfo.editingInfo.editingType === "debit") {
        amountCalcInfo.editingInfo.editingLine.debit = Number(amountCalcResult.value);
        amountCalcInfo.editingInfo.editingTr.querySelectorAll(".voucherline-amount")[0].click();
    }
    if (amountCalcInfo.editingInfo.editingType === "credit") {
        amountCalcInfo.editingInfo.editingLine.credit = Number(amountCalcResult.value);
        amountCalcInfo.editingInfo.editingTr.querySelectorAll(".voucherline-amount")[1].click();
    }
}

function amountInput(event: any) {
    const oldValue = event.currentTarget.attributes["oldValue"] || "0";
    event.currentTarget.value = tesetAmountValue(event.currentTarget.value, oldValue);
    amountZoomOutInfo.twinkleIndex = event.currentTarget.selectionStart - 1;
}
function tesetAmountValue(val: string, oldValue: string) {
    let returnText = "";
    let value = val.replace(/=$/, "").replace(/,/g, "").trim();
    if (value === "-") {
        returnText = "-";
    } else if (!/^(-)?[0-9]+(.[0-9]{2})?$/.test(value)) {
        const match = value.match(/^(-)?\d+(\.\d{0,2})?/);
        let standardValues = "";
        if (match) {
            standardValues = match[0];
        } else if (value.indexOf(".") === 0) {
            const dotMatch = value.match(/\.\d{0,2}/);
            standardValues = dotMatch ? dotMatch[0] : "";
        } else {
            standardValues = "";
        }
        returnText = standardValues.replace(/[+-][^\d.]/g, "");
    } else if (
        isNaN(Number(value)) ||
        Math.abs(Number(value)) > *********.99 ||
        (value.indexOf(".") !== -1 && value.split(".")[1].length > 2)
    ) {
        returnText = oldValue;
    } else {
        returnText = value;
    }
    return returnText;
}

function amountPaste(event: any) {
    const selectionStart = event.currentTarget.selectionStart;
    const selectionEnd = event.currentTarget.selectionEnd;
    const beforeVal = event.currentTarget.value.substring(0, selectionStart);
    const afterVal = event.currentTarget.value.substring(selectionEnd);
    const input = event.currentTarget as HTMLInputElement;
    const oldValue = event.currentTarget.attributes["oldValue"] || "0";
    if (input.value.startsWith("0.00")) {
        input.value = input.value.replace("0.00", "");
    }
    const clipboardData = event.clipboardData;
    const originalText = clipboardData.getData("text");
    if (originalText.length !== 0) {
        if (originalText.indexOf("\r\n") !== -1) {
            let list: Array<string> = originalText.split("\r\n");
            if (list[list.length - 1] === "") list.pop();
            list = list.map((i) => i.split("\t")[0].replace(/[^0-9.-]+/g, ""));
            event.preventDefault();
            const currentText = beforeVal + list[0] + afterVal;
            const targetSelectionPosition = selectionStart + list[0].length;
            input.value = tesetAmountValue(currentText, oldValue);
            if (input.value === oldValue) {
                amountZoomOutInfo.twinkleIndex = input.value.length - 1;
                input.setSelectionRange(input.value.length, input.value.length);
            } else {
                amountZoomOutInfo.twinkleIndex = targetSelectionPosition;
                input.setSelectionRange(targetSelectionPosition, targetSelectionPosition);
                amountZoomOutInfo.twinkleIndex = targetSelectionPosition - 1;
            }
            const appendVoucherLines = list.length - (voucherModel.value.voucherLines.length - editingInfo.editingIndex);
            tryPasteAddVoucherLine(appendVoucherLines);
            for (let i = 1; i < list.length; i++) {
                const pasteVal = tesetAmountValue(list[i], "0");
                const voucherLine = voucherModel.value.voucherLines[editingInfo.editingIndex + i];
                if (voucherLine) {
                    if (editingInfo.editingType === "debit") {
                        voucherLine.debit = Number(pasteVal);
                        voucherLine.credit = 0;
                    }
                    if (editingInfo.editingType === "credit") {
                        voucherLine.credit = Number(pasteVal);
                        voucherLine.debit = 0;
                    }
                }
            }
        } else if (!/^-?\d*\.?\d+$/g.test(originalText)) {
            event.preventDefault();
            const text = originalText.replace(/[^0-9.-]+/g, "");
            const currentText = beforeVal + text + afterVal;
            const targetSelectionPosition = selectionStart + text.length;
            input.value = tesetAmountValue(currentText, oldValue);
            if (input.value === oldValue) {
                amountZoomOutInfo.twinkleIndex = input.value.length - 1;
                input.setSelectionRange(input.value.length, input.value.length);
            } else {
                amountZoomOutInfo.twinkleIndex = targetSelectionPosition;
                input.setSelectionRange(targetSelectionPosition, targetSelectionPosition);
                amountZoomOutInfo.twinkleIndex = targetSelectionPosition - 1;
            }
        }
    } else if (clipboardData.files.length > 0) {
        const file = event.clipboardData?.files[0];
        if (!file || !file.type.startsWith("image")) return;
        event.preventDefault();
        let reader = new FileReader();
        reader.onload = async function (e) {
            const worker = await createWorker("eng");
            worker
                .recognize((e.target as any).result)
                .then((result: any) => {
                    input.value = tesetAmountValue(result.data.text.replace(/[^0-9.-]+/g, ""), oldValue);
                    amountZoomOutInfo.content = input.value;
                    amountZoomOutInfo.twinkleIndex = amountZoomOutInfo.content.length - 1;
                    amountZoomOutInfo.display = true;
                    const containerOffset = containerRef.value.getBoundingClientRect();
                    let offset = input.getBoundingClientRect();
                    amountZoomOutContainer.value.style.left = offset.left - containerOffset.left + "px";
                    amountZoomOutContainer.value.style.top = offset.top - 72 - containerOffset.top + "px";
                })
                .finally(() => {
                    worker && worker.terminate();
                });
        };
        reader.readAsDataURL(file);
    }
}

function amountDblclick(event: any) {
    event.preventDefault();
    editingInfo.editingLine.debit = editingInfo.editingLine.credit = 0;
    let value = 0;
    if (editingInfo.editingType === "debit") {
        value = Number((getTotal("credit") - getTotal("debit")).toFixed(2));
        editingInfo.editingLine.debit = value;
    } else if (editingInfo.editingType === "credit") {
        value = Number((getTotal("debit") - getTotal("credit")).toFixed(2));
        editingInfo.editingLine.credit = value;
    }
    event.currentTarget.value = value;
}

function amountKeyup(event: any) {
    amountZoomOutInfo.twinkleBefore = false;
    const selectionStart = event.currentTarget.selectionStart;
    const selectionEnd = event.currentTarget.selectionEnd;
    if (event.keyCode === 37 || event.keyCode === 39) {
        amountZoomOutInfo.twinkleIndex = selectionStart - 1;
        amountZoomOutInfo.twinkleBefore = selectionStart === 0 && selectionEnd === 0;
    }
    if (event.keyCode >= 48 && event.keyCode <= 57) {
        amountZoomOutInfo.highlightIndex = selectionStart - 1;
    } else {
        amountZoomOutInfo.highlightIndex = -1;
    }

    if (isNaN(Number(event.currentTarget.value))) {
        amountZoomOutInfo.content = "0";
    } else {
        amountZoomOutInfo.content = event.currentTarget.value;
    }

    amountZoomOutInfo.display = true;
    const containerOffset = containerRef.value.getBoundingClientRect();
    let offset = event.currentTarget.getBoundingClientRect();
    amountZoomOutContainer.value.style.left = offset.left - containerOffset.left + "px";
    amountZoomOutContainer.value.style.top = offset.top - 72 - containerOffset.top + "px";
}

function toNextLine(event: any) {
    let parentNode = editingInfo.editingTr.parentNode.parentNode;
    let index = 0;
    for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
        if (editingInfo.editingLine === voucherModel.value.voucherLines[i]) {
            index = i;
            break;
        }
    }
    if (index === voucherModel.value.voucherLines.length - 1) {
        addVoucherLine(index + 1);
    }
    event.currentTarget.blur();
    const nextLine = voucherModel.value.voucherLines[index + 1];
    if (nextLine.credit === 0 && nextLine.debit === 0) {
        let amount = getTotal("debit") - getTotal("credit");
        if (amount) {
            if (amount > 0) {
                voucherModel.value.voucherLines[index + 1].credit = amount;
            } else {
                voucherModel.value.voucherLines[index + 1].debit = -amount;
            }
            const { quantityAccounting, foreigncurrency, asubCode } = nextLine;
            if (asubCode) {
                quantityAccounting === 1 && calcQua(nextLine);
                foreigncurrency === 1 && calcFc(nextLine);
            }      
        }
    }

    nextTick(() => {
        let nextlineDom = parentNode.querySelectorAll(".voucherline")[index + 1].querySelector(".voucherline-description");
        setTimeout(() => {
            initNextDescriptionFlag.value = true;
            nextlineDom.click();
        });
    });
}

function amountBlur() {
    if (editingInfo.editingType === "quantity") {
        editingInfo.editingLine.quantity = Number(
            (editingInfo.editingLine.quantity.toString() === "-" ? 0 : editingInfo.editingLine.quantity) || 0
        );
    }
    if (editingInfo.editingType === "price") {
        editingInfo.editingLine.price = Number((editingInfo.editingLine.price.toString() === "-" ? 0 : editingInfo.editingLine.price) || 0);
    }
    if (editingInfo.editingType === "fcAmount") {
        editingInfo.editingLine.fcAmount = Number(
            (editingInfo.editingLine.fcAmount.toString() === "-" ? 0 : editingInfo.editingLine.fcAmount) || 0
        );
    }
    if (editingInfo.editingType === "fcRate") {
        editingInfo.editingLine.fcRate = Number(
            (editingInfo.editingLine.fcRate.toString() === "-" ? 0 : editingInfo.editingLine.fcRate) || 0
        );
    }
    amountChange(editingInfo.editingLine, editingInfo.editingType);
    editingInfo.editingTr.querySelector(".editing input").setSelectionRange(0, 0);
    cancelEdit();
}

function amountFocus(event: any) {
    amountZoomOutInfo.twinkleIndex = -1;
    allowDrag.value = false;
    let amount = 0;
    if (editingInfo.editingType === "debit") {
        amount = editingInfo.editingLine.debit;
    } else if (editingInfo.editingType === "credit") {
        amount = editingInfo.editingLine.credit;
        if (editingInfo.editingLine.debit === 0 && editingInfo.editingLine.credit === 0 && localStorage.getItem(creditTipKey) !== "true") {
            showVoucherTip();
        }
    }
    event.currentTarget.value = amount.toFixed(2);
}

const debitHistoryStack = new HistoryStack();
const creditHistoryStack = new HistoryStack();
function syncAmount(event: any, history = false) {
    if (event.currentTarget.value === "-") return;
    if (event.currentTarget.value === ".") return;
    let amount = Number(event.currentTarget.value || "0");
    if (isNaN(amount)) {
        ElNotify({
            message: "请输入正确的数字",
            type: "warning",
        });
        cancelEdit();
        return;
    }
    if (amount > *********.99) {
        ElNotify({
            message: "最大不能超过亿位",
            type: "warning",
        });
        cancelEdit();
        return;
    }
    if (editingInfo.editingType === "debit") {
        editingInfo.editingLine.debit = amount;
        if (amount) {
            editingInfo.editingLine.credit = 0;
        }
        if (history) {
            if (debitHistoryStack.currentVoucherLine !== debitHistoryStack.cacheVoucherLine) {
                debitHistoryStack.clearStack();
            }
            debitHistoryStack.pushStack(event.currentTarget.value);
        }
    } else if (editingInfo.editingType === "credit") {
        editingInfo.editingLine.credit = amount;
        if (amount) {
            editingInfo.editingLine.debit = 0;
        }
        if (history) {
            if (creditHistoryStack.currentVoucherLine !== creditHistoryStack.cacheVoucherLine) {
                creditHistoryStack.clearStack();
            }
            creditHistoryStack.pushStack(event.currentTarget.value);
        }
    }
}

function editAmount(event: any, direction: "debit" | "credit", voucherLine: any) {
    allowDrag.value = true;
    amountZoomOutInfo.display = false;
    amountZoomOutInfo.tipDisplay = false;
    syncAmount(event, true);
    amountChange(editingInfo.editingLine, editingInfo.editingType);
    tryAppendVoucherLine(editingInfo.editingLine, editingInfo.editingType);
    cancelEdit();
    if (getTotal(direction) > *********.99) {
        ElNotify({
            type: "warning",
            message: "亲，合计金额不允许超亿位哦~",
        });
        event.currentTarget.value = 0;
        voucherLine[direction] = 0;
    }
}

function getAmountArrar(amount: number): string[] {
    amount = Math.abs(amount);
    return (amount === 0 ? "" : amount.toFixed(2).replace(".", ""))
        .replace(/^0+(?=[^0])/g, "")
        .padStart(11, " ")
        .split("");
}

function getStockFlag() {
    let stockFlag = false;

    if (
        editingInfo.editingLine.aacode !== "" &&
        !isNaN(Number(editingInfo.editingLine.aacode)) &&
        costAccountingSettingsDialogInfo.useCostAccounting
    ) {
        if (editingInfo.editingLine.aacode.indexOf("-") !== -1) {
            if (editingInfo.editingLine.aacode === "-10006") {
                stockFlag = true;
            }
        } else if (aaeDict[editingInfo.editingLine.aacode].aatype === 10006) {
            stockFlag = true;
        }
    }

    return stockFlag;
}

const amountDirectionFlag = ref(false);
function amountChange(voucherline: VoucherEntryModel, field: string) {
    if (field === "quantity" || field === "price") {
        calcAmountByQua(voucherline);
        calcFc(voucherline);
    }
    if (field === "fcAmount" || field === "fcRate") {
        if (voucherSettings.value.allowFcZeroStatus === 1 && field === "fcAmount" && !voucherline.fcAmount) {
            return;
        }
        calcAmountByFc(voucherline);
        calcQua(voucherline);
    }
    if (field === "debit" || field === "credit") {
        calcFc(voucherline);
        calcQua(voucherline);
    }
    function calcAmountByQua(voucherline: VoucherEntryModel) {
        if (checkQuaEqual(voucherline)) return;
        let stockFlag = getStockFlag();
        voucherline.quantity = Number(voucherline.quantity.toFixed(8));
        voucherline.price = Number(voucherline.price.toFixed(8));
        let amount = Math.min(Number((voucherline.quantity * voucherline.price).toFixed(2)), *********.99);
        if (voucherline.credit !== 0 || (stockFlag && (voucherline.debit === 1 || voucherline.debit === 0))) {
            voucherline.credit = amount;
            if (voucherline.debit === 1) {
                voucherline.debit = 0;
            }
            amountDirectionFlag.value = true;
        } else {
            const direction = asubDict[voucherline.asubId].direction;
            if (voucherSettings.value.amountDirectionByAsubStatus === 1 && direction === 2) {
                voucherline.credit = amount;
            } else {
                voucherline.debit = amount;
            }
        }
    }
    function calcAmountByFc(voucherline: VoucherEntryModel) {
        if (checkFcEqual(voucherline)) return;
        voucherline.fcAmount = Number(voucherline.fcAmount.toFixed(2));
        voucherline.fcRate = Number(voucherline.fcRate.toFixed(8));
        let amount = Math.min(Number(_.round((voucherline.fcAmount * 100 * voucherline.fcRate) / 100, 2)), *********.99);
        if (voucherline.credit !== 0) {
            voucherline.credit = amount;
        } else {
            voucherline.debit = amount;
        }
    }
}
function checkQuaEqual(voucherline: VoucherEntryModel) {
  return (voucherline.quantity * voucherline.price).toFixed(2) === (voucherline.debit + voucherline.credit).toFixed(2);
}

function checkFcEqual(voucherline: VoucherEntryModel) {
  return (voucherline.fcAmount * voucherline.fcRate).toFixed(2) === (voucherline.debit + voucherline.credit).toFixed(2);
}

function calcQua(voucherline: VoucherEntryModel) {
        if (voucherSettings.value.autoCalcVoucherStatus === 1) return;
        if (voucherline.price === 0 && voucherline.quantity === 0) {
            return;
        }
        if (checkQuaEqual(voucherline)) return;
        let amount = voucherline.debit + voucherline.credit;
        if (voucherline.quantity === 0) {
            voucherline.quantity = Number((amount / voucherline.price).toFixed(8));
        } else {
            voucherline.price = Number((amount / voucherline.quantity).toFixed(8));
        }
    }
    function calcFc(voucherline: VoucherEntryModel) {
        if (voucherSettings.value.autoCalcVoucherStatus === 1) return;
        if (voucherline.fcAmount === 0 && voucherline.fcRate === 0) {
            return;
        }
        if (checkFcEqual(voucherline)) return;
        let amount = voucherline.debit + voucherline.credit;
        if (voucherline.fcAmount === 0 || voucherline.fcId === 1) {
            voucherline.fcAmount = Number((amount / voucherline.fcRate).toFixed(2));
        } else {
            voucherline.fcRate = Number((amount / voucherline.fcAmount).toFixed(8));
        }
    }
function getTotal(type: "debit" | "credit"): number {
    let sum = 0;
    voucherModel.value.voucherLines.forEach((v) => {
        sum += type === "debit" ? v.debit : v.credit;
    });
    return Number(sum.toFixed(2));
}

function autoScroll(content: any) {
    const scrollTop = content.scrollTop;
    const contentHeight = content.clientHeight;
    const offsetTop = content.querySelectorAll("div")[asubSelectIndex.value].offsetTop;
    const itemHeight = content.querySelectorAll("div")[asubSelectIndex.value].clientHeight + 22;
    if (scrollTop > offsetTop) {
        content.scrollTop = offsetTop;
    } else {
        if (offsetTop + itemHeight > scrollTop + contentHeight) {
            if (itemHeight > contentHeight) {
                content.scrollTop = offsetTop;
            } else {
                content.scrollTop = offsetTop - contentHeight + itemHeight;
            }
        }
    }
}

function editUserName(type: "approvedBy" | "preparedBy") {
    if (isInWxWork()) {
        if (type === "preparedBy") {
            isUserNameEditing.value = true;
            editingUserName.value = userName.value;
            editingUserNameRef.value.focus();
            editingUserNameRef.value.select();
        } else {
            isApprovedNameEditing.value = true;
            editingApprovedName.value = approvedName.value;
            editingApprovedNameRef.value.focus();
            editingApprovedNameRef.value.select();
        }
    } else {
        fullScreen.value = false;
        globalWindowOpenPage("/Default/PersonalInfo", "个人设置");
    }
}

function submitUserName(type: "approvedBy" | "preparedBy") {
    const name = type === "approvedBy" ? editingApprovedName.value : editingUserName.value;
    if (name && name.length <= 20) {
        request({
            url: window.accountSrvHost + "/Default/Services/SubmitForPersonalInfo.ashx?CurrentSystemType=1",
            method: "get",
            params: { UserName: name, EditType: "Info", ChangeID: 0, PreparedType: 1 },
        }).then((res: any) => {
            isUserNameEditing.value = false;
            if (res === "Success") {
                loadTopUserName();
                userStore.userName = name;
                tempUserName.value = "";
                approvedName.value = "";
                ElNotify({ message: "亲，保存成功啦！", type: "success" });
            } else {
                if (res === "Failed") {
                    ElNotify({ message: "亲，保存失败啦！请联系侧边栏客服！", type: "warning" });
                } else {
                    ElNotify({ message: res, type: "warning" });
                }
            }
        });
    } else {
        isUserNameEditing.value = false;
        isApprovedNameEditing.value = false;
        if (name.length > 20) {
            ElNotify({ message: "姓名最多20个字哦！", type: "warning" });
        }
    }
}
const warningRowIndex = ref(-1);
function resetWarningRow() {
    warningRowIndex.value = -1;
}
function setWarningRow(rowNumber: number) {
    warningRowIndex.value = rowNumber;
    const row = containerRef.value.querySelector(`[data-line-number="${rowNumber}"]`) as HTMLElement;
    const routerContainerTop = document.querySelector(".router-container")!.getBoundingClientRect().top;
    let scrollHeight = row.getBoundingClientRect().top - routerContainerTop - (window.innerHeight - routerContainerTop - 48 - row.clientHeight) / 2;
    setScrollTop(scrollHeight);
    maxInViewportVoucherLineIndex.value = Math.min(rowNumber + voucherlinePageSize.value / 2, voucherModel.value.voucherLines.length);
}
const checkoutCompanySubmitDialogShow = ref(false);
function saveVoucher(saveParams: BaseVoucherSaveParams, auto = false) {
    let hasFaRow = false; //存在固定资产相关科目
    let hasUserFaRow = false; //存在用户手动输入的固定资产相关科目

    let hasProfit = false; //是否含有本年利润
    let hasSunyilei = false; //是否含有损益类科目
    let hasCommon = false; //是否含有共同的科目

    let hasNewNetWorth = false; //是否含有新增净资产类科目  （民非）
    let hasResNetWorth = false; //限定性净资产
    let hasOnResNetWorth = false; //非限定性净资产
    let hasOnResIncomeOrExpenses = false; //非限定性收入或费用
    let hasResIncome = false; //限定性收入

    let IsLimitNetWorth = false; //限定性净资产为借方的凭证行
    let IsNoLimitNetWorth = false; //非限定性净资产为贷方的凭证行
    var IsNetWorthVoucher = false;
    // 利润分配-未分配利润 涉及准则：企业
    let hasProfitDistributionUndistributedProfit = false;
    // 以前年度损益调整
    let hasPriorYearSunyiAdjustment = false;

    let quaErrList = new Array<string>();

    const savingVoucherLines = new Array<VoucherEntryModel>();

    new Promise<IResponseModel<VoucherSaveModel>>((resolve, reject) => {
        let voucherSaveModel = new VoucherSaveModel(voucherModel.value);
        let res: IResponseModel<VoucherSaveModel> = { state: 1000, subState: 0, data: voucherSaveModel, msg: "" };
        if (editingInfo.editingType !== "") {
            if (saveParams instanceof DraftSaveParams) {
                res.state = 2000;
                res.subState = -1;
                res.msg = "";
                reject(res);
                return;
            }
            if (editingInfo.editingType === "description" || editingInfo.editingType === "asub") {
                document.dispatchEvent(new MouseEvent("mousedown"));
            } else if (
                editingInfo.editingType === "quantity" ||
                editingInfo.editingType === "price" ||
                editingInfo.editingType === "fcAmount" ||
                editingInfo.editingType === "fcRate"
            ) {
                amountBlur();
            } else if (editingInfo.editingType === "debit" || editingInfo.editingType === "credit") {
                editingInfo.editingTr
                    .querySelectorAll(".voucherline-amount")
                    ?.[editingInfo.editingType === "debit" ? 0 : 1].querySelector("input")
                    .blur();
            } else if (editingInfo.editingType === "fc") {
                cancelEdit();
            }
            if (editingInfo.editingType !== "") {
                res.state = 2000;
                res.subState = -1;
                res.msg = "";
                reject(res);
                return;
            }
        }
        if (!/^\d+$/.test(voucherModel.value.vnum.toString())) {
            res.state = 2000;
            res.msg = "凭证号只允许填写数字，请修改。";
            reject(res);
            return;
        }
        if (!/^\d+$/.test((voucherModel.value.attachments || 0).toString())) {
            res.state = 2000;
            res.msg = "单据数量只允许填写数字，请修改。";
            reject(res);
            return;
        }
        function getSubjectErrorMessage(rowIndex: number, voucherline: any, generateCheckTip: string) {
            const baseMsg = `第${rowIndex + 1}行`;
            if(isSalaryOrFaGenerate.value){
                let asub
                for(const key in asubDict) {
                    const value = asubDict[key];
                    if(value.asubCode === voucherline.asubCode) {
                        asub = value
                        break
                    }
                }
                console.log(voucherline,asub)
                if(voucherline.asubName.trim().length === 0 ){
                    return `亲，${baseMsg}，请选择科目！`;
                }else if(!asub) {
                    return `亲，${baseMsg}，科目已停用，请到科目列表启用该科目或重新选择其他可用科目！`;
                }else if(voucherline.aacode.split(",").filter((item:string) => item !== '').length !== asub.aatypes.split(",").length) {
                    return `亲，${baseMsg}，请选择辅助核算项目！`;
                }
            }
            return `${baseMsg}${voucherline.asubName}科目存在下级科目或当前不可用，请重新选择科目或在科目设置中启用！`;
        }
        for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
            let voucherline = voucherModel.value.voucherLines[i];
            const hasTempAsub = !voucherline.asubId && tempAsubList?.some((item) => item.asubCode === voucherline.asubCode);
            if (!voucherline.asubId && !voucherline.asubName && !hasTempAsub && !voucherline.debit && !voucherline.credit) {
                if (!voucherline.description && i === 0) {
                    res.state = 2000;
                    res.msg = "亲，第" + (i + 1) + "行不能为空！";
                    !auto && setWarningRow(i + 1);
                    reject(res);
                    return;
                }
                continue;
            }
            if (!voucherline.description.trim()) {
                res.state = 2000;
                res.msg = "亲，第" + (i + 1) + "行，请输入摘要！";
                !auto && setWarningRow(i + 1);
                reject(res);
                return;
            }
            if (voucherline.description.length > 256) {
                res.state = 2000;
                res.msg = "亲，第" + (i + 1) + "行，摘要过长啦！";
                !auto && setWarningRow(i + 1);
                reject(res);
                return;
            }
            if (!voucherline.asubName && !voucherline.asubId && !hasTempAsub) {
                res.state = 2000;
                res.msg = "亲，第" + (i + 1) + "行，请选择科目！";
                !auto && setWarningRow(i + 1);
                reject(res);
                return;
            }
            if (!asubDict[voucherline.asubId] && !hasTempAsub) {
                if (saveParams instanceof DraftSaveParams) {
                    res.msg = getSubjectErrorMessage(i, voucherline, generateCheckTip.value);
                } else {
                    ElConfirm(
                        getSubjectErrorMessage(i, voucherline, generateCheckTip.value)
                    ).then((r: boolean) => {
                        !auto && r && setWarningRow(i + 1);
                    });
                }
                res.state = 2000;
                res.subState = -1;
                reject(res);
                return;
            }
            if (saveParams instanceof VoucherSaveParams) {
                if (!voucherline.debit && !voucherline.credit) {
                    res.state = 2000;
                    res.msg = "亲，第" + (i + 1) + "行，请输入金额！";
                    !auto && setWarningRow(i + 1);
                    reject(res);
                    return;
                }
            }
            if (voucherline.quantityAccounting === 1) {
                if (!voucherline.quantity) {
                    quaErrList.push("第" + (i + 1) + "行，数量为空或者0！");
                }
                if (
                    voucherSettings.value.autoCalcVoucherStatus !== 1 &&
                    Number((voucherline.quantity * voucherline.price).toFixed(2)) !== voucherline.debit + voucherline.credit &&
                    Number(((voucherline.debit + voucherline.credit) / voucherline.quantity).toFixed(8)) !== voucherline.price &&
                    Number(((voucherline.debit + voucherline.credit) / voucherline.price).toFixed(8)) !== voucherline.quantity
                ) {
                    quaErrList.push("第" + (i + 1) + "行，数量和金额不匹配！");
                }
            }
            if (voucherline.foreigncurrency === 1) {
                if (
                    voucherSettings.value.allowFcZeroStatus !== 1 &&
                    !voucherline.fcAmount &&
                    saveParams instanceof VoucherSaveParams &&
                    voucherModel.value.vtype !== 300
                ) {
                    res.state = 2000;
                    res.msg = "亲，第" + (i + 1) + "行，原币不能为空或者0！";
                    !auto && setWarningRow(i + 1);
                    reject(res);
                    return;
                }
                if (!voucherline.fcRate && saveParams instanceof VoucherSaveParams && voucherModel.value.vtype !== 300) {
                    res.state = 2000;
                    res.msg = "亲，第" + (i + 1) + "行，汇率不能为空或者0！";
                    !auto && setWarningRow(i + 1);
                    reject(res);
                    return;
                }
                if (voucherModel.value.vtype !== 300 && voucherModel.value.vtype !== 170 && saveParams instanceof VoucherSaveParams) {
                    if (
                        voucherSettings.value.autoCalcVoucherStatus !== 1 &&
                        voucherSettings.value.allowFcZeroStatus !== 1 &&
                        Number((voucherline.fcAmount * voucherline.fcRate).toFixed(2)) !== voucherline.debit + voucherline.credit &&
                        Number(((voucherline.debit + voucherline.credit) / voucherline.fcRate).toFixed(2)) !== voucherline.fcAmount &&
                        Number(((voucherline.debit + voucherline.credit) / voucherline.fcAmount).toFixed(8)) !== voucherline.fcRate
                    ) {
                        res.state = 2000;
                        res.msg = "亲，第" + (i + 1) + "行，金额和汇率、原币不匹配！";
                        !auto && setWarningRow(i + 1);
                        reject(res);
                        return;
                    }
                }
            }
            if (voucherline.assistingAccounting === 1) {
                if (!checkVoucherLineAssistAccounting(voucherline, true)) {
                    res.state = 2000;
                    res.msg = "亲，第" + (i + 1) + "行，请选择辅助核算！";
                    !auto && setWarningRow(i + 1);
                    reject(res);
                    return;
                }
            }
            if (
                [
                    specialAsubCodeDict[accountingStandard.value].fixedAssetAsub,
                    specialAsubCodeDict[accountingStandard.value].depreciationAsub,
                ].some((item) => item && voucherline.asubCode.indexOf(item) === 0) ||
                (activeIaModel &&
                    [
                        specialAsubCodeDict[accountingStandard.value].intangibleAsub,
                        specialAsubCodeDict[accountingStandard.value].accumulatedAmortizationAsub,
                        specialAsubCodeDict[accountingStandard.value].longTermDeferredExpensesAsub,
                    ].some((item) => item && voucherline.asubCode.indexOf(item) === 0))
            ) {
                hasFaRow = true;
                if (!voucherline.isFaLine) {
                    hasUserFaRow = true;
                }
            }
            savingVoucherLines.push(voucherline);
            voucherSaveModel.voucherLines.push(new VoucherLineSaveModel(voucherline));
        }
        if (voucherSaveModel.voucherLines.length === 0) {
            res.state = 2000;
            res.msg = "亲，请录入您的凭证行！";
            reject(res);
            return;
        }
        if (saveParams instanceof VoucherSaveParams && getTotal("debit") !== getTotal("credit")) {
            res.state = 2000;
            res.msg = "亲，借贷不平衡！";
            reject(res);
            return;
        }
        if (voucherSaveModel.note.length > 256) {
            res.state = 2000;
            res.msg = "您输入的备注长度不能超过256！";
            reject(res);
            return;
        }
        resolve(res);
    })
        .then((res: IResponseModel<VoucherSaveModel>) => {
            return new Promise<IResponseModel<VoucherSaveModel>>((resolve, reject) => {
                const voucherSaveModel = res.data;
                for (let i = 0; i < voucherSaveModel.voucherLines.length; i++) {
                    let voucherline = voucherSaveModel.voucherLines[i];
                    if (voucherline.asubId === 0 && tempAsubList.length > 0) continue;
                    let asub = asubDict[voucherline.asubId];
                    if (accountingStandard.value === AccountStandard.FolkComapnyStandard) {
                        if (asub.asubType === 7) {
                            if (asub.asubCode.indexOf("3101") === 0) {
                                hasOnResNetWorth = true;
                                IsLimitNetWorth = true;
                            } else if (asub.asubCode.indexOf("3102") === 0) {
                                hasResNetWorth = true;
                                IsNoLimitNetWorth = true;
                            } else {
                                hasNewNetWorth = true;
                            }
                        } else if (asub.asubType === 8) {
                            if (asub.restricted === 0) {
                                hasOnResIncomeOrExpenses = true;
                            } else {
                                hasResIncome = true;
                            }
                        } else if (asub.asubType === 9) {
                            hasOnResIncomeOrExpenses = true;
                        } else {
                            hasCommon = true;
                        }
                    } else if (AccountStandard.UnionStandard === accountingStandard.value) {
                        if (asub.asubType === 7) {
                            if (["32103", "32203", "33103"].some((asubCode) => asub.asubCode.indexOf(asubCode) === 0)) {
                                hasProfit = true;
                            }
                        } else if (asub.asubType === 8 || asub.asubType === 10) {
                            hasOnResIncomeOrExpenses = true;
                        } else {
                            hasCommon = true;
                        }
                        if (hasProfit && hasOnResIncomeOrExpenses && hasCommon) {
                            if (saveParams instanceof DraftSaveParams) {
                                res.msg =
                                    "结转损益凭证中除了“工会资金结余-本年收支结转、工会资金结转-本年收支结转、财政拨款结转-本年收支结转”和收入支出类科目之外，不允许使用其他科目，请修改。";
                            } else {
                                ElConfirm(
                                    "结转损益凭证中除了“工会资金结余-本年收支结转、工会资金结转-本年收支结转、财政拨款结转-本年收支结转”和收入支出类科目之外，不允许使用其他科目，请修改。",
                                    true
                                );
                            }
                            res.state = 2000;
                            res.subState = -1;
                            reject(res);
                            return;
                        }
                    } else {
                        if (
                            asub.asubType === 4 && asub.asubCode.indexOf(specialAsubCodeDict[accountingStandard.value].yearNetIncomeAsub) === 0
                        ) {
                                hasProfit = true;
                        } else if (asub.asubType === 4 && accountingStandard.value === AccountStandard.CompanyStandard && asub.asubCode.indexOf(profitDistributionUndistributedProfitCode.value) === 0){
                                hasProfitDistributionUndistributedProfit = true;
                                // 是否有以前年度损益调整
                                if(!hasPriorYearSunyiAdjustment) {
                                    for (let j = i + 1; j < voucherSaveModel.voucherLines.length; j++) {
                                        let voucherline = voucherSaveModel.voucherLines[j];
                                        if (voucherline.asubId === 0 && tempAsubList.length > 0) continue;
                                        let asub = asubDict[voucherline.asubId];
                                        if(asub.asubCode.indexOf(6901) === 0){
                                            hasPriorYearSunyiAdjustment=true;
                                            break;
                                        }
                                    }
                                }

                        }
                        else if (asub.asubType === 6) {
                            hasSunyilei = true;
                            if (asub.asubCode.indexOf(6901) === 0) {
                                hasPriorYearSunyiAdjustment=true;
                            }
                        }
                        else {
                            hasCommon = true;
                        }
                        if(accountingStandard.value === AccountStandard.CompanyStandard&&!hasProfitDistributionUndistributedProfit&&hasProfit && hasSunyilei && (hasCommon ||hasPriorYearSunyiAdjustment)){
                            // 是否有未分配利润
                            for (let j = i + 1; j < voucherSaveModel.voucherLines.length; j++) {
                                let voucherline = voucherSaveModel.voucherLines[j];
                                if (voucherline.asubId === 0 && tempAsubList.length > 0) continue;
                                let asub = asubDict[voucherline.asubId];
                                if(asub.asubCode.indexOf(profitDistributionUndistributedProfitCode.value) === 0){
                                    hasProfitDistributionUndistributedProfit=true;
                                    break;
                                }
                            }
                        }
                        if ((hasProfit && hasSunyilei && (hasCommon || (hasProfitDistributionUndistributedProfit && !hasPriorYearSunyiAdjustment)))) {
                            let asubTipName = "本年利润";
                            if (accountingStandard.value === AccountStandard.VillageCollectiveEconomyStandard) {
                                asubTipName = "本年收益";
                            }
                            if (saveParams instanceof DraftSaveParams) {
                                res.msg = `结转损益凭证中除了“${asubTipName}”和损益类科目之外，不允许使用其他科目，请修改。`;
                            } else {
                                if (hasProfitDistributionUndistributedProfit && (!hasPriorYearSunyiAdjustment || hasCommon)) {
                                    checkoutCompanySubmitDialogShow.value = true;
                                } else if (hasCommon) {
                                    ElConfirm(`结转损益凭证中除了“${asubTipName}”和损益类科目之外，不允许使用其他科目，请修改。`, true);
                                }
                            }
                            res.state = 2000;
                            res.subState = -1;
                            reject(res);
                            return;
                        } else if(accountingStandard.value === AccountStandard.CompanyStandard && hasProfitDistributionUndistributedProfit && hasSunyilei && (!hasPriorYearSunyiAdjustment || hasCommon) && props.from === 'checkout'){
                            checkoutCompanySubmitDialogShow.value = true;
                            res.state = 2000;
                            res.subState = -1;
                            reject(res);
                            return;
                        }
                    }
                }
                if (accountingStandard.value === AccountStandard.FolkComapnyStandard) {
                    if (
                        voucherSaveModel.vtype === 0 &&
                        hasResNetWorth &&
                        hasResIncome &&
                        !hasOnResNetWorth &&
                        !hasOnResIncomeOrExpenses &&
                        !hasNewNetWorth &&
                        !hasCommon
                    ) {
                        voucherSaveModel.vtype = 401;
                        IsNetWorthVoucher = true;
                    } else if (
                        voucherSaveModel.vtype === 0 &&
                        !hasResNetWorth &&
                        !hasResIncome &&
                        hasOnResNetWorth &&
                        hasOnResIncomeOrExpenses &&
                        !hasNewNetWorth &&
                        !hasCommon
                    ) {
                        voucherSaveModel.vtype = 403;
                        IsNetWorthVoucher = true;
                    } else if (
                        voucherSaveModel.vtype === 0 &&
                        voucherSaveModel.voucherLines.length === 2 &&
                        hasResNetWorth &&
                        hasOnResNetWorth &&
                        !hasNewNetWorth &&
                        !hasCommon
                    ) {
                        if (IsLimitNetWorth && IsNoLimitNetWorth) {
                            //这是一张限定性资产转为非限定性资产的凭证
                            voucherSaveModel.vtype = 406;
                        }
                    }
                    if (
                        (voucherSaveModel.vtype === 400 ||
                            voucherSaveModel.vtype === 401 ||
                            voucherSaveModel.vtype === 402 ||
                            voucherSaveModel.vtype == 403) &&
                        (hasCommon || hasNewNetWorth)
                    ) {
                        voucherSaveModel.vtype = 0;
                    }
                    if (voucherSaveModel.vtype === 406) {
                        if (voucherSaveModel.voucherLines.length === 2 && IsLimitNetWorth && IsNoLimitNetWorth) {
                            voucherSaveModel.vtype = 406;
                        } else {
                            voucherSaveModel.vtype = 0;
                        }
                    }
                } else if (AccountStandard.UnionStandard === accountingStandard.value) {
                    if (voucherSaveModel.vtype !== 170 && voucherSaveModel.vtype !== 171 && hasProfit && hasOnResIncomeOrExpenses) {
                        voucherSaveModel.vtype = 171;
                    }
                    if ((voucherSaveModel.vtype === 170 || voucherSaveModel.vtype === 171) && !(hasProfit && hasOnResIncomeOrExpenses)) {
                        voucherSaveModel.vtype = 0;
                    }
                }else {
                    if (voucherSaveModel.vtype !== 170 && voucherSaveModel.vtype !== 171 && hasProfit && hasSunyilei) {
                        voucherSaveModel.vtype = 171;
                    }
                    if ((voucherSaveModel.vtype === 170 || voucherSaveModel.vtype === 171) && !(hasProfit && hasSunyilei)) {
                        voucherSaveModel.vtype = 0;
                    }
                }
                resolve(res);
            });
        })
        .then((res: IResponseModel<VoucherSaveModel>) => {
            return new Promise<IResponseModel<VoucherSaveModel>>((resolve, reject) => {
                if (saveParams instanceof VoucherSaveParams && activeFixedModel) {
                    if (
                        hasUserFaRow ||
                        (props.queryParams instanceof EditVoucherQueryParams && (voucherModel.value.isFaVoucher || hasFaRow))
                    ) {
                        if (
                            getCookie("hiddenFixedDialog-" + getGlobalToken()) !== "1" &&
                            (!trialStatusStore.isTrial || !trialStatusStore.isExpired)
                        ) {
                            faConfrimDialogInfo.display = true;
                            faConfrimDialogInfo.neverAlert = false;
                            faConfrimDialogInfo.callback = (r) => {
                                faConfrimDialogInfo.display = false;
                                if (r) {
                                    if (faConfrimDialogInfo.neverAlert) {
                                        setCookie("hiddenFixedDialog-" + getGlobalToken(), "1", "d3650");
                                    }
                                    resolve(res);
                                } else {
                                    res.state = 2000;
                                    res.subState = -1;
                                    reject(res);
                                }
                            };
                            return;
                        }
                    }
                }
                resolve(res);
            });
        })
        .then((res: IResponseModel<VoucherSaveModel>) => {
            const voucherSaveModel = res.data;
            return new Promise<IResponseModel<VoucherSaveModel>>((resolve, reject) => {
                if (
                    quaErrList.length > 0 &&
                    saveParams instanceof VoucherSaveParams &&
                    voucherSaveModel.vtype !== 170 &&
                    voucherSaveModel.vtype !== 400 &&
                    voucherSaveModel.vtype !== 402
                ) {
                    let quaErrMsg = quaErrList.slice(0, 3).join("");
                    if (quaErrList.length > 3) {
                        quaErrMsg += "...<br/>";
                    }
                    ElConfirm(quaErrMsg + "确认要保存吗？").then((r) => {
                        if (r) {
                            resolve(res);
                        } else {
                            res.state = 2000;
                            res.subState = -1;
                            const match = quaErrMsg.match(/第(\d+)行/);
                            !auto && setWarningRow(Number(match && match[1]));
                            reject(res);
                        }
                    });
                    return;
                } else {
                    resolve(res);
                }
            });
        })
        .then((res: IResponseModel<VoucherSaveModel>) => {
            return new Promise<IResponseModel<VoucherSaveModel>>((resolve, reject) => {
                const voucherSaveModel = res.data;
                if (saveParams instanceof VoucherSaveParams && ![170, 171, 400, 402, 401, 403, 406].includes(voucherSaveModel.vtype)) {
                    let flag = false;
                    const errList: Array<number> = [];
                    for (let i = 0; i < voucherModel.value.voucherLines.length; i++) {
                        const voucherline = voucherModel.value.voucherLines[i];
                        const target = asubDict[Number(voucherline.asubId)];
                        const isWholeVoucherLine =
                            voucherline.description && voucherline.asubId && (voucherline.debit || voucherline.credit);
                        if (
                            isWholeVoucherLine &&
                            isErrorProfitVoucherLine(target.asubType, target.direction, voucherline.debit, voucherline.credit)
                        ) {
                            flag = true;
                            errList.push(i);
                        }
                    }
                    if (!flag) {
                        resolve(res);
                    } else {
                        expenseACreditDialogInfo.display = true;
                        expenseACreditDialogInfo.numberRows = errList;
                        expenseACreditDialogInfo.callback = (r: ProfitAmountOperation) => {
                            expenseACreditDialogInfo.display = false;
                            if (ProfitAmountOperation.Close === r) {
                                !auto && setWarningRow(errList[0] + 1);
                                res.state = 2000;
                                res.subState = -1;
                                reject(res);
                                return;
                            } else if (ProfitAmountOperation.Adjust === r) {
                                for (let i = 0; i < expenseACreditDialogInfo.numberRows.length; i++) {
                                    const voucherline = voucherModel.value.voucherLines[expenseACreditDialogInfo.numberRows[i]];
                                    if (voucherline.debit !== 0) {
                                        voucherline.credit = -voucherline.debit;
                                        voucherline.debit = 0;
                                    } else if (voucherline.credit !== 0) {
                                        voucherline.debit = -voucherline.credit;
                                        voucherline.credit = 0;
                                    }
                                }
                                warningRowIndex.value = -1;
                                res.state = 2000;
                                res.subState = -1;
                                reject(res);
                                return;
                            } else {
                                warningRowIndex.value = -1;
                                resolve(res);
                            }
                        };
                    }
                } else {
                    resolve(res);
                }
            });
        })
        .then((res: IResponseModel<VoucherSaveModel>) => {
            return new Promise<IResponseModel<VoucherSaveModel>>((resolve, reject) => {
                if (saveParams instanceof VoucherSaveParams && accountingStandard.value === 3 && IsNetWorthVoucher) {
                    ElConfirm("亲，结转净资产的凭证只会在期末结转模块生成才有效哦，您确认要继续保存吗？").then((r) => {
                        if (r) {
                            resolve(res);
                        } else {
                            res.state = 2000;
                            res.subState = -1;
                            reject(res);
                        }
                    });
                } else {
                    resolve(res);
                }
            });
        })
        .then((res: IResponseModel<VoucherSaveModel>) => {
            const data = res;
            const voucherSaveModel = res.data;
            if (saveParams instanceof VoucherSaveParams) {
                const voucherSaveParams = saveParams as VoucherSaveParams;
                lastVDate.value = voucherSaveModel.vdate;
                if (voucherSaveParams.dotSave) {
                    voucherSaveParams.callback(data, savingVoucherLines);
                } else {
                    if (props.queryParams instanceof EditVoucherQueryParams) {
                        useLoading().enterLoading("努力保存中，请稍候...");
                        const requestParams = { ...voucherSaveModel, isNeedSaveToOther };
                        request({
                            url: "/api/Voucher",
                            method: "put",
                            data: requestParams,
                        }).then((response: IResponseModel<IVoucherSaveResultModel>) => {
                            if (response.state === 1000) {
                                voucherSaveModel.pid = response.data.pid;
                                voucherSaveModel.vid = response.data.vid;
                                voucherSaveModel.vnum = response.data.vnum;
                                appendAsubTotalAmount();
                                oldVoucherModel.value = JSON.parse(JSON.stringify(voucherModel.value));
                                voucherSaveParams.callback(data, savingVoucherLines);
                                warningRowIndex.value = -1;
                            } else {
                                data.state = response.state;
                                data.subState = response.subState;
                                data.msg = response.msg;
                                voucherSaveParams.callback(data, savingVoucherLines);
                            }
                        }).finally(() => {
                            useLoading().quitLoading();
                        });
                    } else {
                        let editType = 1;
                        if (props.queryParams instanceof InsertVoucherQueryParams) {
                            editType = 2;
                        } else if (props.queryParams instanceof CopyVoucherQueryParams) {
                            editType = 3;
                        } else if (props.queryParams instanceof LoadVoucherTemplateQueryParams) {
                            editType = props.queryParams.originEditType;
                        } else if (props.queryParams instanceof LoadVoucherDraftQueryParams) {
                            editType = props.queryParams.originEditType;
                        }
                        if (editType === 2) {
                            voucherSaveParams.autoVNum = false;
                        }
                        const isCombine = props.queryParams instanceof DataVoucherQueryParams && props.queryParams.isCombine;
                        const requestParams: any = { ...voucherSaveModel, autoVNum: voucherSaveParams.autoVNum };
                        let url = "/api/Voucher?editType=" + editType + "&createdWay=" + voucherSaveParams.createdWay;
                        if (props.queryParams instanceof OffsetVoucherQueryParams) {
                            requestParams.sourcePid = props.queryParams.pId;
                            requestParams.sourceVid = props.queryParams.vId;
                            requestParams.sourceType = 200;
                        } else if (isCombine) {
                            requestParams.sourcePid = props.queryParams.pid;
                            requestParams.sourceVids = props.queryParams.vids;
                            requestParams.sourceType = 300;
                            url = "/api/Voucher/MergeVoucher?editType=1&createdWay=" + voucherSaveParams.createdWay;
                        }
                    
                        useLoading().enterLoading("努力保存中，请稍候...");
                        request({ url, method: "post", data: requestParams}).then((response: IResponseModel<IVoucherSaveResultModel>) => {
                            if (response.state === 1000) {
                                voucherSaveModel.pid = response.data.pid;
                                voucherSaveModel.vid = response.data.vid;
                                voucherSaveModel.vnum = response.data.vnum;
                                appendAsubTotalAmount();
                                oldVoucherModel.value = JSON.parse(JSON.stringify(voucherModel.value));
                                voucherSaveParams.callback(data, savingVoucherLines);
                            } else {
                                data.state = response.state;
                                data.subState = response.subState;
                                data.msg = response.msg;
                                voucherSaveParams.callback(data, savingVoucherLines);
                            }
                        }).finally(() => {
                            useLoading().quitLoading();
                        });
                    }
                }
            }
            if (saveParams instanceof VoucherTemplateSaveParams) {
                const voucherTemplateSaveParams = saveParams as VoucherTemplateSaveParams;
                let voucherTemplateSaveModel = new VoucherTemplateSaveModel();
                voucherTemplateSaveModel.vtType = voucherTemplateSaveParams.vtType;
                voucherTemplateSaveModel.vtCode = voucherTemplateSaveParams.vtCode;
                voucherTemplateSaveModel.vtName = voucherTemplateSaveParams.vtName;
                voucherTemplateSaveModel.vtDefault = voucherTemplateSaveParams.vtDefault;
                voucherTemplateSaveModel.vgId = voucherTemplateSaveParams.vgId;
                voucherTemplateSaveModel.voucherLines = voucherSaveModel.voucherLines.map((i) => {
                    const newValues:any = {};
                    newValues.direction = i.debit ? 1 : i.credit ? 2 : 0;
                    if (!voucherTemplateSaveParams.saveAmount) {
                        newValues.credit = 0;
                        newValues.debit = 0;
                    }
                    return new VoucherTemplateLineSaveModel({
                        ...i,
                        ...newValues
                    });
                });
                // 保存成模板前校验
                if (!voucherTemplateSaveParams.vtType && !voucherTemplateSaveParams.vtName) {
                    voucherTemplateSaveParams.callback({ state: 1000, subState: 0, data: voucherTemplateSaveModel, msg: "" });
                    return;
                }
                if (props.queryParams instanceof EditVoucherTemplateQueryParams) {
                    const queryParams = props.queryParams as EditVoucherTemplateQueryParams;
                    voucherTemplateSaveModel.vtId = queryParams.vtId;
                    updateVoucherTemplate(voucherTemplateSaveModel).then((res) => {
                        if (res.state === 1000 && res.data) {
                            voucherTemplateSaveParams.callback({ state: 1000, subState: 0, data: voucherTemplateSaveModel, msg: "" });
                        }
                    });
                } else {
                    createVoucherTemplate(voucherTemplateSaveModel).then((res) => {
                        if (res.state === 1000 && res.data !== 0) {
                            voucherTemplateSaveModel.vtId = res.data;
                            voucherTemplateSaveParams.callback({ state: 1000, subState: 0, data: voucherTemplateSaveModel, msg: "" });
                        }
                    });
                }
            }
            if (saveParams instanceof VoucherDraftSaveParams) {
                const voucherDraftSaveParams = saveParams as VoucherDraftSaveParams;
                request({
                    url: "/api/VoucherDraft",
                    method: "post",
                    data: voucherSaveModel,
                }).then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        voucherDraftSaveParams.callback({ state: 1000, subState: 0, data: voucherSaveModel, msg: "" });
                    } else {
                        data.state = res.state;
                        data.subState = res.subState;
                        data.msg = res.msg;
                        voucherDraftSaveParams.callback(data);
                    }
                });
            }
            if (saveParams instanceof DraftSaveParams) {
                const draftSaveParams = saveParams as DraftSaveParams;
                draftSaveParams.callback(voucherModel.value);
            }
        })
        .catch((res) => {
            if (res["state"] !== undefined) {
                if (saveParams instanceof VoucherSaveParams) {
                    const voucherSaveParams = saveParams as VoucherSaveParams;
                    voucherSaveParams.callback(res, savingVoucherLines);
                }
                if (saveParams instanceof VoucherTemplateSaveParams) {
                    const response = res as IResponseModel<VoucherSaveModel>;
                    const r: IResponseModel<VoucherTemplateSaveModel> = {
                        state: response.state,
                        subState: response.subState,
                        data: new VoucherTemplateSaveModel(),
                        msg: response.msg,
                    };
                    const voucherTemplateSaveParams = saveParams as VoucherTemplateSaveParams;
                    voucherTemplateSaveParams.callback(r);
                }
                if (saveParams instanceof VoucherDraftSaveParams) {
                    const voucherDraftSaveParams = saveParams as VoucherDraftSaveParams;
                    voucherDraftSaveParams.callback(res);
                }
            } else {
                res.state = 9999;
                if (res["state"] !== undefined) {
                    const voucherSaveParams = saveParams as VoucherSaveParams;
                    voucherSaveParams.callback(res, savingVoucherLines);
                }
            }
        });
}
function showErrAsubLines() {
    const appendStr = "...";
    const errLength = expenseACreditDialogInfo.numberRows.length;
    if (errLength <= 5) {
        return expenseACreditDialogInfo.numberRows.map((item) => item + 1).join("、");
    } else {
        return (
            expenseACreditDialogInfo.numberRows
                .slice(0, 5)
                .map((item) => item + 1)
                .join("、") + appendStr
        );
    }
}
function toHelp() {
    globalWindowOpen("https://help.ningmengyun.com/?appasid=200080546I9l9#/jz/commonPro?subMenuId=*********&answerId=658");
}
enum ProfitType {
    Income = 1,
    Expense = 2,
    False = 3,
}
function isIncomeOrExpenseAsub(asubType: number, direction: number): ProfitType {
    switch (accountingStandard.value) {
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.CompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            return AccountSubjectType.Income !== asubType ? ProfitType.False : direction === 2 ? ProfitType.Income : ProfitType.Expense;
        case AccountStandard.FolkComapnyStandard:
            return AccountSubjectType.Revenue === asubType
                ? ProfitType.Income
                : AccountSubjectType.Expenses === asubType
                ? ProfitType.Expense
                : ProfitType.False;
        case AccountStandard.UnionStandard:
            return AccountSubjectType.Revenue === asubType
                ? ProfitType.Income
                : AccountSubjectType.Disbursement === asubType
                ? ProfitType.Expense
                : ProfitType.False;
        default:
            return ProfitType.False;
    }
}
function isErrorProfitVoucherLine(asubType: number, direction: number, debit: number, credit: number) {
    const suggestDirection = isIncomeOrExpenseAsub(asubType, direction);
    if (suggestDirection === ProfitType.False) return false;
    return suggestDirection === ProfitType.Income ? debit !== 0 : credit !== 0;
}
function getVoucherModel(): VoucherModel {
    if (voucherModel.value.vgId !== 0 && voucherModel.value.vgName == "") {
        voucherModel.value.vgName = voucherGroupList.value.find((i) => i.id === voucherModel.value.vgId)?.name ?? "记";
    }
    return voucherModel.value;
}

function focusFirstLine(flag?: boolean) {
    nextTick(() => {
        let firstlineDom = voucherContainer.value.querySelectorAll(".voucherline")[0].querySelector(".voucherline-description");
        setTimeout(() => {
            firstlineDom.click();
            setTimeout(() => {
                if (!flag) {
                    desListSelectorDisplay.value = false;
                }
            });
        });
    });
}

function handleAttachmentsKeyPress(event: KeyboardEvent) {
    const input = event.target as HTMLInputElement;
    if (input.value.length >= 5) {
        event.preventDefault();
    }
}

if (window.localStorage.getItem("voucherZoomState")) {
    changeZoomState(window.localStorage.getItem("voucherZoomState") === "in" ? "in" : "out");
} else {
    changeZoomState("in");
}

function removeEventListener() {
    asubBlurExecute();
    document.removeEventListener("mousedown", asubBlur);
    warningRowIndex.value = -1;
}
onDeactivated(() => {
    asubBlurExecute();
});
function clearVoucher() {
    voucherModel.value.voucherLines = [];
    voucherModel.value.attachments = undefined;
    voucherModel.value.note = "";
    voucherModel.value.attachFiles = [];
    voucherModel.value.attachFileIds = "";
    asubBlurExecute();
    checkAllVoucherLines();
    warningRowIndex.value = -1;
    focusFirstLine();
    handleGetAllTotalAmount();
}

const voucherButtonRef = ref<InstanceType<typeof VoucherButton>>();
function showPrintVoucherDialog() {
    voucherButtonRef.value?.showPrintVoucherDialog();
}
function showVoucherTemplates() {
    voucherButtonRef.value?.showVoucherTemplates();
}
function saveAsTemplate() {
    voucherButtonRef.value?.saveAsTemplate();
}
function deleteVoucher() {
    voucherButtonRef.value?.deleteVoucher();
}
function handleSave() {
    voucherButtonRef.value?.save();
}
function defaultNewVoucher() {
    voucherButtonRef.value?.defaultNewVoucher();
}
function setIsTempVoucher(isTemp: boolean) {
    const timer = setTimeout(() => {
        isTempVoucher.value = isErp.value && isTemp && props.queryParams instanceof EditVoucherQueryParams;
        clearTimeout(timer);
    });
}
function closeDialog() {
    uploadFileDialogRef.value?.resetDialogInfo();
    expenseACreditDialogInfo.display = false;
    faConfrimDialogInfo.display = false;
    costAccountingSettingsDialogInfo.display = false;
    amountCalcInfo.display = false;
    checkoutCompanySubmitDialogShow.value = false;
    voucherButtonRef.value?.close();
}
defineExpose({
    getAsubList,
    saveVoucher,
    getVoucherModel,
    focusFirstLine,
    changeZoomState,
    resetAsubTotalAmount,
    removeEventListener,
    addAccountSubjectSuccess,
    loadAAE,
    clearAAE,
    getVoucherDescription,
    clearVoucher,
    warningRowIndex,
    resetScrollIndex,
    checkAllVoucherLines,
    showPrintVoucherDialog,
    showVoucherTemplates,
    saveAsTemplate,
    deleteVoucher,
    handleSave,
    defaultNewVoucher,
    setIsTempVoucher,
    closeDialog,
});
const propsQueryParams = computed({
    get: () => props.queryParams,
    set: (val: any) => {
        emit("update:queryParams", val);
    },
});
watch(() => props.queryParams, loadVoucher);
watch(
    voucherModel,
    (newVal, oldVal) => {
        if (_.isEqual(oldVal, new VoucherModel())) return;
        const voucherReadonly = voucherModel.value.approveStatus === 1 || voucherModel.value.pstatus === 3;
        emit("voucherChanged", voucherReadonly);
    },
    { deep: true }
);
watch(
    () => voucherModel.value.vgId,
    () => {
        const vg = voucherGroupList.value.find((i) => i.id === voucherModel.value.vgId);
        if (vg) {
            voucherModel.value.vgName = vg.name;
        } else {
            voucherModel.value.vgName = "";
        }
        emit("voucherVgIdChanged", voucherModel.value.vgId);
    },
    { immediate: true }
);
watch(
    () => route.path,
    () => {
        removeEventListener();
    }
);
//是否有辅助核算添加的权限
const hasPermissionAccounting = (aaType: number) => {
    if (isErp.value) {
        if (aaType === 10001) {
            return checkPermission(["Customers-编辑"]);
        } else if (aaType === 10002) {
            return checkPermission(["Vendors-编辑"]);
        } else if (aaType === 10003) {
            return checkPermission(["Employees-编辑"]);
        } else if (aaType === 10004) {
            return checkPermission(["Department-编辑"]);
        } else if (aaType === 10006) {
            return checkPermission(["ProductManager-编辑"]);
        } else {
            return checkPermission(["assistingaccount-canedit"]);
        }
    } else {
        return checkPermission(["assistingaccount-canedit"]);
    }
};
const attachmentsRef = ref<InstanceType<typeof ElInputNumber>>();
function findDialogContent() {
    let node = containerRef.value.parentNode;
    while (node.classList && (!node.classList.contains("content") || node.classList.contains("router-container"))) {
        node = node.parentNode;
        if (node === document.body) {
            break;
        }
    }
    return node;
}
function handleDocumentKeydown(event: KeyboardEvent) {
    const e = event || window.event;
    const kc = e.keyCode || e.charCode;
    const bounding = containerRef.value?.getBoundingClientRect();
    if (
        bounding &&
        bounding.bottom >= 0 &&
        bounding.top <= (window.innerHeight || document.documentElement.clientHeight) &&
        bounding.width > 0 &&
        bounding.height > 0
    ) {
        if (kc === 115) {
            if (handleCheckHasDialog(containerRef.value) || handleCheckHasDialog(findDialogContent())) return;
            // F4 跳转至附单据数栏
            // 笔记本是fn + F4
            event.preventDefault();
            nextTick().then(() => {
                !isVoucherReadonly.value && attachmentsRef.value?.focus();
            });
            return false;
        } else if (kc === 76 && e.ctrlKey) {
            if (handleCheckHasDialog(containerRef.value) || handleCheckHasDialog(findDialogContent())) return;
            // ctrl + L 跳转至日期栏
            event.preventDefault();
            nextTick().then(() => {
                !isVoucherReadonly.value && dateRef.value?.focus();
            });
            return false;
        }
    }
}
function reloadAsubAmount() {
    const bounding = containerRef.value?.getBoundingClientRect();
    const contentNode = findVoucherContentNode();
    // 当前页面会自动刷新  无需多刷  只有隐藏并且该页面正处于凭证页面的凭证会去加载
    if (bounding && bounding.height === 0 && bounding.width === 0 && contentNode.style.display !== "none") {
        handleGetAllTotalAmount();
    }
}
function findVoucherContentNode() {
    let node = containerRef.value.parentNode;
    const contentClassList = ["main-content", "slot-content", "slot-mini-content", "content"];
    while (node) {
        if (contentClassList.some((item) => node.classList.contains(item))) {
            return node;
        }
        node = node.parentNode;
    }
    return node;
}
function checkIsVoucherTemplate() {
    return props.queryParams instanceof EditVoucherTemplateQueryParams || props.queryParams instanceof NewVoucherTemplateQueryParams;
}
onMounted(() => {
    document.addEventListener("keydown", handleDocumentKeydown);
    window.addEventListener(reloadAsubAmountEventKey, reloadAsubAmount);
});
onBeforeUnmount(() => {
    document.removeEventListener("keydown", handleDocumentKeydown);
    // let container = document.querySelector(".router-container");
    // container && (container.removeEventListener("mousedown", asubBlur));
    window.removeEventListener(reloadAsubAmountEventKey, reloadAsubAmount);
    asubBlurExecute();
});
</script>