<template>
    <div class="content" v-if="isShow" style="width: 100%">
        <div class="title">{{ currentSlot === "recycle" ? "回收站" : "账套管理" }}</div>
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content" style="width: 100%; height:auto;">
                    <div
                        class="main-top main-tool-bar space-between split-line"
                        style="border-bottom: 1px solid var(--border-color); padding: 10px 20px"
                    >
                        <div class="main-tool-left">
                            <a class="solid-button" id="btnNewAs" v-if="btnNewAs" @click="() => (currentSlot = 'create')">新建账套</a>
                            <div
                                class="transfer-free-accountset third-part-hidden"
                                v-if="btnTransferNewAccountSet && !isThirdPart"
                                @click="importFreeAccountSet"
                            >
                                导入免费版账套
                            </div>
                            <div
                                v-else
                                v-show="hasProAccountSet && !isThirdPart"
                                class="transfer-free-accountset pro-hidden third-part-hidden"
                                style="width: 140px"
                                @click="transferAccountSet"
                            >
                                升级账套至专业版
                            </div>
                            <span class="main-top-txt"
                                >当前账套数量为 <span id="accountTotol" class="highlight-green">{{ accountNumber }}</span
                                >个</span
                            >
                        </div>
                        <div class="main-tool-right">
                            <div class="input-group">
                                <el-input
                                    type="text"
                                    v-model="initsearchText"
                                    autocomplete="off"
                                    :readonly="searchReadonly"
                                    @keyup.enter="getAccountData"
                                    @change="changeSearchText"
                                    @focus="() => (searchReadonly = false)"
                                />
                                <input type="password" autocomplete="new-password" style="display: none;" />
                                <el-input type="text" style="opacity: 0;" />
                                <div class="icon" @click="getAccountData"></div>
                            </div>
                            <div class="table-icon selected" ref="tableIconRef" @click="changeModel(1)"></div>
                            <div class="list-icon" ref="listIconRef" @click="changeModel(2)"></div>
                            <div class="recyclebin-icon" @click="enterRecycle">回收站</div>
                        </div>
                    </div>
                    <div class="main-center" style="padding: 20px">
                        <template v-if="loading">
                            <div class="main-center-loading">正在加载账套信息，请稍等！</div>
                        </template>
                        <template v-else-if="accountData.length">
                            <template v-if="model == 1">
                                <div
                                    class="account-item"
                                    v-for="(item, index) in accountData"
                                    :key="item.asStartDate"
                                    :class="[
                                        index == 0 ? 'current' : '',
                                        item.needLockPassword ? 'locked' : item.lockState ? 'unlocked' : '',
                                    ]"
                                    @click="tryChangeAS(item.asId, item.asName, item.needLockPassword, $event)"
                                >
                                    <div v-if="item.currentAs" class="account-image"></div>
                                    <div class="account-item-title">
                                        <div class="account-item-title-txt" :title="item.asName">{{ item.asName }}</div>
                                        <div
                                            v-if="HasPermission(item, ['accountset-canedit', 'accountset-candelete']) && !item.isDisabled"
                                            class="account-item-settings"
                                        >
                                            <div class="account-item-settings-downlist">
                                                <template v-if="item.needLockPassword">
                                                    <div @click.stop="EditLockAccout(item.asId, item.asName)">管理密码锁</div>
                                                </template>
                                                <template v-else>
                                                    <div
                                                        v-if="HasPermission(item, ['accountset-canedit'])"
                                                        @click.stop="editHandle(item.asId)"
                                                    >
                                                        编辑
                                                    </div>
                                                    <div
                                                        v-if="HasPermission(item, ['accountset-canedit'])"
                                                        @click.stop="copyHandle(item.asId)"
                                                    >
                                                        复制
                                                    </div>
                                                    <div
                                                        v-if="HasPermission(item, ['accountset-candelete'])"
                                                        @click.stop="Delete(item, index)"
                                                    >
                                                        删除
                                                    </div>
                                                    <Suspense>
                                                        <AsyncVirtualAccount>
                                                            <template #content>
                                                                <template v-if="item.lockState">
                                                                    <div
                                                                        v-if="HasPermission(item, ['accountset-canedit'])"
                                                                        @click.stop="EditLockAccout(item.asId, item.asName)"
                                                                    >
                                                                        管理密码锁
                                                                    </div>
                                                                </template>
                                                                <template v-else>
                                                                    <div
                                                                        v-if="HasPermission(item, ['accountset-canedit'])"
                                                                        @click.stop="lockAccoutScan(item.asId, item.asName)"
                                                                    >
                                                                        密码锁
                                                                    </div>
                                                                </template>
                                                            </template>
                                                        </AsyncVirtualAccount>
                                                    </Suspense>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="account-item-content">
                                        <Tooltip :content="item.asName" placement="right">
                                            <div class="account-item-company-name">{{ item.asName }}</div>
                                        </Tooltip>

                                        <div class="account-item-marks">
                                            <div class="account-item-mark">{{ GetAccoundTaxType(item.taxType) }}</div>
                                        </div>
                                        <div class="account-item-info-line">
                                            会计准则：{{ GetAccountStandardText(item.accountingStandard, item.subAccountingStandard) }}
                                        </div>
                                        <div class="account-item-info-line">
                                            启用时间：{{
                                                item.asStartYear +
                                                "年" +
                                                (Number(item.asStartMonth) < 10 ? "0" + item.asStartMonth : item.asStartMonth) +
                                                "月"
                                            }}
                                        </div>
                                        <div class="account-item-info-line">账套ID：{{ item.asId }}</div>
                                    </div>
                                    <div class="account-item-shadow">
                                        <div class="account-item-title">
                                            <div class="account-item-title-txt">{{ item.asName }}</div>
                                            <div
                                                v-if="
                                                    HasPermission(item, ['accountset-canedit', 'accountset-candelete']) && !item.isDisabled
                                                "
                                                class="account-item-settings"
                                            >
                                                <div class="account-item-settings-downlist">
                                                    <template v-if="item.needLockPassword">
                                                        <div @click.stop="EditLockAccout(item.asId, (item as any).asName)">管理密码锁</div>
                                                    </template>
                                                    <template v-else>
                                                        <div
                                                            v-if="HasPermission(item, ['accountset-canedit'])"
                                                            @click.stop="editHandle(item.asId)"
                                                        >
                                                            编辑
                                                        </div>
                                                        <div
                                                            v-if="HasPermission(item, ['accountset-canedit'])"
                                                            @click.stop="copyHandle(item.asId)"
                                                        >
                                                            复制
                                                        </div>
                                                        <div
                                                            v-if="HasPermission(item, ['accountset-candelete'])"
                                                            @click.stop="Delete(item, index)"
                                                        >
                                                            删除
                                                        </div>
                                                        <template v-if="item.lockState">
                                                            <div
                                                                v-if="HasPermission(item, ['accountset-canedit'])"
                                                                @click.stop="EditLockAccout(item.asId, item.asName)"
                                                            >
                                                                管理密码锁
                                                            </div>
                                                        </template>
                                                        <template v-else>
                                                            <div
                                                                v-if="HasPermission(item, ['accountset-canedit'])"
                                                                @click.stop="lockAccoutScan(item.asId, item.asName)"
                                                            >
                                                                密码锁
                                                            </div>
                                                        </template>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="unlock-button-content">
                                            <div class="unlock-button-img"></div>
                                            <div class="unlock-button">输入账套密码</div>
                                        </div>
                                    </div>
                                </div>
                                <!-- </div> -->
                            </template>
                            <template v-else>
                                <div class="main-center-list" style="padding: 0px 13px 10px 0px">
                                    <Table 
                                        :data="accountData" 
                                        :columns="columns" 
                                        :scrollbarShow="true" 
                                        :empty-text="'暂无数据'"
                                        :tableName="setModule"
                                    >
                                        <template #firstColumn>
                                            <el-table-column
                                                label=""
                                                prop="currentAs"
                                                align="left"
                                                header-align="left"
                                                :width="getColumnWidth(setModule, 'currentAs', 40)"
                                                :show-overflow-tooltip="false"
                                            >
                                                <template #default="scope">
                                                    <img
                                                        v-if="scope.row.currentAs"
                                                        src="@/assets/Settings/list-current.png"
                                                        style="width: 27px; height: 27px; position: absolute; left: -1px; top: -1px"
                                                    />
                                                    <img
                                                        v-if="scope.row.needLockPassword"
                                                        src="@/assets/Settings/lock-icon-gray.png"
                                                        class="list-lock-icon"
                                                    />
                                                    <template v-else>
                                                        <img
                                                            v-if="scope.row.lockState"
                                                            src="@/assets/Settings/unlock-icon-gray.png"
                                                            class="list-lock-icon"
                                                        />
                                                    </template>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #asName>
                                            <el-table-column 
                                                label="单位名称"  
                                                align="left" 
                                                header-align="left"
                                                prop="asName"
                                                :width="getColumnWidth(setModule, 'asName', 180)"
                                            >
                                                <template #default="scope">
                                                    <a
                                                        class="link"
                                                        @click="
                                                            tryChangeAS(
                                                                scope.row.asId,
                                                                scope.row.asName,
                                                                scope.row.needLockPassword,
                                                                $event
                                                            )
                                                        "
                                                    >
                                                        {{ scope.row.asName }}
                                                    </a>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #operator>
                                            <el-table-column
                                                label="操作"
                                                width="155"
                                                align="left"
                                                header-align="left"
                                                :show-overflow-tooltip="false"
                                                :resizable="false"
                                            >
                                                <template #default="scope">
                                                    <div class="operator">
                                                        <template v-if="!scope.row.isDisabled && !scope.row.needLockPassword">
                                                            <a
                                                                class="link"
                                                                v-if="HasPermission(scope.row, ['accountset-canedit'])"
                                                                @click="editHandle(scope.row.asId)"
                                                                title="编辑账套"
                                                            >
                                                                编辑
                                                            </a>
                                                            <a
                                                                class="link"
                                                                v-if="HasPermission(scope.row, ['accountset-canedit'])"
                                                                @click="copyHandle(scope.row.asId)"
                                                                title="复制账套"
                                                                >复制</a
                                                            >
                                                            <a
                                                                class="link"
                                                                v-if="HasPermission(scope.row, ['accountset-candelete'])"
                                                                @click="Delete(scope.row, scope.$index)"
                                                                title="删除账套"
                                                                >删除</a
                                                            >
                                                        </template>
                                                        <Suspense>
                                                            <AsyncVirtualAccount>
                                                                <template #content v-if="!scope.row.isDisabled">
                                                                    <template v-if="scope.row.needLockPassword">
                                                                        <a
                                                                            class="link"
                                                                            v-if="HasPermission(scope.row, ['accountset-canedit'])"
                                                                            @click="
                                                                                global_EnterLockedAccountSet(
                                                                                    scope.row.asId,
                                                                                    scope.row.asName
                                                                                )
                                                                            "
                                                                            title="密码"
                                                                            >密码</a
                                                                        >
                                                                    </template>
                                                                    <template v-else>
                                                                        <template v-if="scope.row.lockState">
                                                                            <a
                                                                                class="link"
                                                                                v-if="HasPermission(scope.row, ['accountset-canedit'])"
                                                                                @click="EditLockAccout(scope.row.asId, scope.row.asName)"
                                                                                title="管理密码锁"
                                                                            >
                                                                                管理密码锁
                                                                            </a>
                                                                        </template>
                                                                        <template v-else>
                                                                            <a
                                                                                class="link"
                                                                                v-if="HasPermission(scope.row, ['accountset-canedit'])"
                                                                                @click="lockAccoutScan(scope.row.asId, scope.row.asName)"
                                                                                title="密码锁"
                                                                                >密码锁</a
                                                                            >
                                                                        </template>
                                                                    </template>
                                                                </template>
                                                            </AsyncVirtualAccount>
                                                        </Suspense>
                                                    </div>
                                                </template>
                                            </el-table-column>
                                        </template>
                                    </Table>
                                </div>
                            </template>
                        </template>
                        <template v-else>
                            <div class="main-center-loading" style="color: #999">暂无数据</div>
                        </template>
                    </div>
                </div>
            </template>
            <template #create>
                <CreateAccountSetView :key="new Date().getTime()" :adId="asId" @cancelCreate="cancelCreate"></CreateAccountSetView>
            </template>
            <template #edit>
                <EditAccountSetView
                    ref="editAccountSetRef"
                    :editAsId="editAsId"
                    :isEisRelation="isEisRelation"
                    :data="editData"
                    v-model:childActiveName="childActiveName"
                    @cancelEdit="cancelCreate"
                    @backMain="backMain"
                ></EditAccountSetView>
            </template>
            <template #copy>
                <CopyAccountSetView :copyAsId="copyAsId" @cancelCopy="cancelCopy"> </CopyAccountSetView>
            </template>
            <template #recycle>
                <RecycleView ref="recycleref" @backMain="backMain" @getAccountSet="getAccountData"></RecycleView>
            </template>
        </ContentSlider>
    </div>
    <el-dialog v-model="deleteBool" title="删除账套" center width="438px" class="dialogDrag">
        <div class="delete-confirm-content" v-dialogDrag>
            <div class="delete-confirm-main">
                <div class="txt delete-font highlight-red">
                    您将要删除以下账套：<span id="txtDeleteAccount">{{ txtDeleteAccount }}</span>
                </div>
                <div class="txt mt-10">
                    账套启用年月：<span id="txtAsStartDate">{{ txtAsStartDate }}</span>
                </div>
                <div class="txt mt-10">
                    注意：如需保存账套数据，请先备份下载到本地保存。被删除的账套将不再显示。如有需要，您可以在回收站重新还原账套。
                </div>
            </div>
            <div id="deleteBtn" class="buttons">
                <a class="delete-button button hover-red" @click="DeleteConfirm">删除</a>
                <a class="button ml-10 hover-red" @click="() => (deleteBool = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="setLockVisible" title="设置账套密码锁" center width="438px" class="dialogDrag">
        <div class="accountset-lock-dialog-content" v-dialogDrag>
            <table cellpadding="0" cellspacing="0">
                <tbody>
                    <tr>
                        <td class="tb-title">账套：</td>
                        <td id="set-accountset-lock-asname" class="tb-field" style="line-height: 40px">
                            <Tooltip
                                :teleported="true"
                                placement="right"
                                :content="setAsName"
                                :maxWidth="200"
                                :font-size="16"
                                :line-clamp="1"
                                >{{ setAsName }}</Tooltip
                            >
                        </td>
                    </tr>
                    <tr>
                        <td id="set-accountset-lock-password-title" class="tb-title">输入密码：</td>
                        <td class="tb-field">
                            <el-input
                                type="password"
                                id="set-accountset-lock-pwd"
                                placeholder="6-12位字母或数字"
                                autocomplete="off"
                                v-model="password"
                                show-password
                            />
                            <input type="password" autocomplete="new-password" style="display: none;" />
                        </td>
                    </tr>
                    <tr>
                        <td class="tb-title">确认密码：</td>
                        <td class="tb-field">
                            <el-input
                                type="password"
                                id="set-accountset-lock-confirm-pwd"
                                placeholder="再次确认密码"
                                autocomplete="off"
                                v-model="confirmPassword"
                                show-password
                            />
                            <input type="password" autocomplete="new-password" style="display: none;" />
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="buttons">
                <a id="SetAccountSetLockConfirm" class="button solid-button" @click="LockAccout">确认</a>
                <a @click="() => (setLockVisible = false)" class="button ml-10">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="editAccountSetLockVisible" title="管理账套密码锁" center width="438px" destroy-on-close class="dialogDrag">
        <div id="EditAccountSetLock" title="" style="width: 438.4px" class="panel-body panel-body-noborder window-body" v-dialogDrag>
            <div class="accountset-lock-dialog-content" id="editAccountSetLockPage1">
                <table cellpadding="0" cellspacing="0">
                    <tbody>
                        <tr>
                            <td class="tb-title">账套：</td>
                            <td id="edit-accountset-lock-asname1" class="tb-field" style="line-height: 40px">
                                <Tooltip :content="editAsName" :maxWidth="200" :line-clamp="1" :teleported="true" placement="right">
                                    {{ editAsName }}
                                </Tooltip>
                            </td>
                        </tr>
                        <tr>
                            <td class="tb-title">手机号：</td>
                            <td id="edit-accountset-lock-phone" class="tb-field">{{ editPhone }}</td>
                        </tr>
                        <tr>
                            <td class="tb-title">验证码：</td>
                            <td class="tb-field" style="max-width: 206px">
                                <el-input
                                    v-model="editAccountSetLockCaptcha"
                                    placeholder="输入验证码"
                                    style="width: 116px; margin-right: 4px; height: 28px"
                                ></el-input>
                                <a
                                    :class="captchaStatus === '获取验证码' ? 'button solid-button' : 'button disabled'"
                                    id="edit-accountset-lock-send-captcha"
                                    @click="captchaStatus === '获取验证码' ? editAccountsetLockSendCaptcha() : ''"
                                    >{{ captchaStatus }}</a
                                >
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="buttons">
                    <a id="EditAccountSetLockConfirm1" class="button solid-button" @click="EditAccountSetLockConfirm1">确认</a
                    ><a @click="() => (editAccountSetLockVisible = false)" class="button ml-10">取消</a>
                </div>
            </div>
            <div class="accountset-lock-dialog-content" id="editAccountSetLockPage2" style="padding-top: 33px; display: none">
                <table cellpadding="0" cellspacing="0">
                    <tbody>
                        <tr style="display: flex">
                            <td class="tb-title flush">账套：</td>
                            <td class="tb-field flush" style="max-width: 220px">
                                <Tooltip :content="editAsName" placement="right" :lineClamp="1" :maxWidth="220">
                                    <div>{{ editAsName }}</div>
                                </Tooltip>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div>
                                    <el-radio-group
                                        v-model="editAccountSetLockType"
                                        class="ml-4"
                                        style="display: flex; flex-flow: column nowrap; align-items: flex-start"
                                    >
                                        <el-radio label="1" size="large">重置密码</el-radio>
                                        <el-radio label="2" size="large">取消密码锁</el-radio>
                                    </el-radio-group>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="buttons" style="margin-top: 42px">
                    <a id="EditAccountSetLockConfirm2" class="button solid-button" @click="EditAccountSetLockConfirm2">确认</a
                    ><a @click="() => (editAccountSetLockVisible = false)" class="button ml-10">取消</a>
                </div>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="resetLockVisible" title="重置账套密码锁" center width="438px" class="dialogDrag">
        <div class="accountset-lock-dialog-content" v-dialogDrag>
            <table cellpadding="0" cellspacing="0">
                <tbody>
                    <tr>
                        <td class="tb-title">账套：</td>
                        <td id="set-accountset-lock-asname" class="tb-field flush" style="max-width: 220px">
                            <Tooltip :content="resetAsName" placement="right" :lineClamp="1" :maxWidth="220">
                                <div>{{ resetAsName }}</div>
                            </Tooltip>
                        </td>
                    </tr>
                    <tr>
                        <td id="set-accountset-lock-password-title" class="tb-title">输入密码：</td>
                        <td class="tb-field">
                            <el-input type="password" id="set-accountset-lock-pwd" placeholder="6-12位字母或数字" v-model="password" />
                        </td>
                    </tr>
                    <tr>
                        <td class="tb-title">确认密码：</td>
                        <td class="tb-field">
                            <el-input
                                type="password"
                                id="set-accountset-lock-confirm-pwd"
                                placeholder="再次确认密码"
                                v-model="confirmPassword"
                            />
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="buttons">
                <a id="SetAccountSetLockConfirm" class="button solid-button" @click="LockAccout">确认</a>
                <a @click="() => (resetLockVisible = false)" class="button ml-10">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "AccountSets",
};
</script>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import CreateAccountSetView from "./components/CreateAccountSet.vue";
import EditAccountSetView from "./components/EditAccountSet.vue";
import CopyAccountSetView from "./components/CopyAccountSet.vue";
import RecycleView from "./components/Recyclebin.vue";
import AsyncVirtualAccount from "./components/AsyncVirtualAccount.vue";
import type { IAccountSetItem } from "./types";
import { AppConfirmDialog } from "@/util/appConfirm";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { getCookie, setCookie } from "@/util/cookie";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { isLemonClient } from "@/util/lmClient";
import { isInWxWork } from "@/util/wxwork";
import { closeCurrentTab, globalWindowOpen, globalWindowOpenPage, setTopLocationhref } from "@/util/url";
import { reloadAccountSetList, setSelectedAccount } from "@/util/accountset";
import { GetAccountStandardText, GetAccoundTaxType, changeAS, global_EnterLockedAccountSet } from "./utils";
import { onMounted, watch, ref, onActivated } from "vue";
import router from "@/router";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { getHasProAccountSet, tryGoToPro } from "@/util/proUtils";
import { getLemonClient } from "@/util/lmClient";
import { getGlobalToken } from "@/util/baseInfo";
import { onUnmounted } from "vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import type { IAccountSetInfo } from "@/api/accountSet";
import { getServiceId } from "@/util/proUtils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { getEisRelationInfoApi } from "@/api/eis";

const setModule = "AccountSet";
const editAccountSetRef = ref();

const recycleref = ref();
const slots = ["main", "create", "edit", "copy", "recycle"];
const currentSlot = ref(window.isAccountingAgent ? "edit" : "main");
const isShow = ref(false);
const isThirdPart = useThirdPartInfoStoreHook().isThirdPart;
const isHideBarcode = useThirdPartInfoStoreHook().isHideBarcode;
const accountNumber = ref(0);
const deleteBool = ref(false);
const txtDeleteAccount = ref("");
const txtAsStartDate = ref("");
const setLockVisible = ref(false);
const editAccountSetLockVisible = ref(false);
const setAsName = ref("");
const setAsId = ref<number>();
const editAsName = ref("");
const editPhone = ref("");
const editAccountSetLockCaptcha = ref("");
const captchaStatus = ref("获取验证码");
const editId = ref<number>();
const editAccountSetLockType = ref("1");
const resetLockVisible = ref(false);
const resetAsName = ref("");
const password = ref("");
const confirmPassword = ref("");
const accountsetStore = useAccountSetStoreHook();
const asId = accountsetStore.accountSet?.asId;

const searchReadonly = ref(true);
const initsearchText = ref("");
const searchText = ref("");
const model = ref(1);
const tableIconRef = ref();
const listIconRef = ref();
const editAsId = ref(0);
const loading = ref(false);
const columns = ref<Array<IColumnProps>>([
    { slot: "firstColumn" },
    { slot: "asName" },
    { label: "账套ID", prop: "asId", align: "left", headerAlign: "left", minWidth: 60, width: getColumnWidth(setModule, "asId") },
    {
        label: "当前记账年月",
        prop: "asStartDate",
        align: "left",
        headerAlign: "left",
        minWidth: 83,
        formatter: (row: any) => periodFormatter(row.asCurrentYear, row.asCurrentMonth),
        width: getColumnWidth(setModule, "asStartDate")
    },
    {
        label: "账套启用年月",
        prop: "createDate",
        align: "left",
        headerAlign: "left",
        minWidth: 83,
        formatter: (row: any) => periodFormatter(row.asStartYear, row.asStartMonth),
        width: getColumnWidth(setModule, "createDate")
    },
    {
        label: "会计准则",
        prop: "accountingStandard",
        align: "left",
        headerAlign: "left",
        minWidth: 150,
        formatter: (row: any) => GetAccountStandardText(row.accountingStandard, row.subAccountingStandard),
        width: getColumnWidth(setModule, "accountingStandard")
    },
    {
        label: "资金模块",
        prop: "cashJournal",
        align: "left",
        headerAlign: "left",
        minWidth: 60,
        formatter: (row: any) => (row.cashJournal === "1" ? "启用" : "未启用"),
        width: getColumnWidth(setModule, "cashJournal")
    },
    {
        label: "资产模块",
        prop: "fixedAsset",
        align: "left",
        headerAlign: "left",
        minWidth: 82,
        formatter: (row: any) => (row.fixedAsset === "1" ? "启用" : "未启用"),
        width: getColumnWidth(setModule, "fixedAsset")
    },
    {
        label: "凭证审核",
        prop: "checkNeeded",
        align: "left",
        headerAlign: "left",
        minWidth: 58,
        formatter: (row: any) => (row.checkNeeded === "1" ? "启用" : "未启用"),
        width: getColumnWidth(setModule, "checkNeeded")
    },
    {
        label: "账套权限",
        prop: "permissionName",
        align: "left",
        headerAlign: "left",
        minWidth: 73,
        formatter: (row: any) => row.permissionName?.join(""),
        width: getColumnWidth(setModule, "permissionName")
    },
    { slot: "operator" },
]);
const accountData = ref<IAccountSetItem[]>([]);
window.isAccountingAgent && editHandle(asId as number);
const hasProAccountSet = ref(false);
getHasProAccountSet().then((r) => {
    hasProAccountSet.value = r;
});
const btnNewAs = ref(false);
const btnTransferNewAccountSet = ref(false);
if (window.isProSystem) {
    // $("#btnNewAs").hide();
    // $('#btnTransferNewAccountSet').hide();
    request({
        url: window.eHost + "/wb/plan/is-administrator?serviceID=" + getServiceId(),
        method: "get",
    })
        .then((res: any) => {
            if (res.statusCode) {
                btnNewAs.value = true;
                btnTransferNewAccountSet.value = true;
            }
        })
        .catch(() => {
            ElNotify({
                title: "error",
                message: "出现异常，请刷新页面或联系系统管理员",
            });
        });
} else {
    btnNewAs.value = true;
}
let hasTaxHandle = false;
function getAccountData() {
    searchText.value = initsearchText.value;
    loading.value = true;
    var url = `/api/AccountSet/FullList?searchText=${searchText.value}`;
    if (window.isProSystem) {
        url = window.eHost + "/wb/valveacc_vip_web" + url + "&serviceID=" + getServiceId();
    }
    request({
        url,
    })
        .then((res: any) => {
            accountData.value = res.data ? res.data : [];
            accountNumber.value = accountData.value.length;
            if (getCookie("AccountSetShowModel")) {
                changeModel(getCookie("AccountSetShowModel"));
            }
        })
        .finally(() => {
            loading.value = false;
        });
    if (router.currentRoute.value.query.isFromTax == "true" || router.currentRoute.value.query.isEditModule == "true") {
        if (hasTaxHandle) return;
        hasTaxHandle = true;
        setActiveInfo();
    }
}
function changeSearchText(v:string) {
    if(!v) {
        getAccountData();
    }
}
function periodFormatter(year: string, month: string) {
    if (Number(month) < 10) {
        month = "0" + month;
    }
    return year + "年" + month + "月";
}

function cancelCreate() {
    if (window.isAccountingAgent) {
        closeCurrentTab();
        globalWindowOpenPage("/Default/Default", "首页");
    } else {
        getAccountData();
        currentSlot.value = "main";
    }
}

function cancelCopy() {
    getAccountData();
    copyAsId.value = 0;
    currentSlot.value = "main";
}

function importFreeAccountSet() {
    globalWindowOpenPage("/Settings/TransferPro", "升级账套");
}

function transferAccountSet() {
    if (isLemonClient()) {
        getLemonClient().upgradeToPro();
    } else {
        if (isInWxWork()) {
            tryGoToPro();
        } else {
            request({
                url: window.eHost + `/wb/plan?pageSize=100&currentPage=1&isAdministrator=true&productType=` + window.productType,
                method: "get",
            }).then((res: any) => {
                if (res.statusCode) {
                    if (res.data.totalCount > 1) {
                        // 服务数大于1，跳转首页
                        globalWindowOpen(window.wwwHost + "/Partial.aspx?p=w#/workbench");
                    } else {
                        globalWindowOpen(
                            window.eHost +
                                "/wb/enter/redirect?productType=" +
                                window.productType +
                                "&id=" +
                                res.data.data[0].id +
                                "&suffix=" +
                                encodeURIComponent("/Settings/TransferPro")
                        );
                    }
                }
            });
        }
    }
}

function changeModel(val: number) {
    model.value = val;
    if (val == 1) {
        tableIconRef.value.classList.add("selected");
        listIconRef.value.classList.remove("selected");
    } else {
        tableIconRef.value.classList.remove("selected");
        listIconRef.value.classList.add("selected");
    }
    setCookie("AccountSetShowModel", val + "", "d30");
}

function enterRecycle() {
    currentSlot.value = "recycle";
    recycleref.value.getRecycleData();
}
function backMain() {
    currentSlot.value = "main";
}

function tryChangeAS(asId: number, asName: string, isLocked: boolean, event: Event) {
    if ((event.target as HTMLElement).className === "account-item-settings") {
        return;
    }
    checkMultiUserAccountSet(function (r: boolean) {
        if (r) {
            tryShowPayDialog(1, "permission", "开通专业版，立即解锁多用户协同。更多专业版功能如下：", "用户限制", function () {
                //如果有账套锁，且为这些环境时，直接调用解锁事件，否则通过下拉列表的change触发后续处理
                if (isLocked) {
                    global_EnterLockedAccountSet(asId, asName);
                } else {
                    changeAS(asId);
                }
            });
        } else {
            if (isLocked) {
                global_EnterLockedAccountSet(asId, asName);
            } else {
                changeAS(asId, isLocked, asName);
            }
        }
    }, asId);
}

function checkMultiUserAccountSet(callback: Function, asId?: number) {
    //只有免费版需要多用户检查，其他直接跳过即可
    if (window.isProSystem || window.isErp) {
        callback && callback(false);
        return;
    }
    request({
        url: `/api/PermissionsOnlyAuth/GetAccountSetUserNumber?asId=${asId}`,
        method: "post",
    }).then((res: any) => {
        callback && callback(res.data > 1);
    });
}

const editData = ref<any>({});
const isEisRelation = ref(false);
function editHandle(as_id: number) {
    editAsId.value = as_id;
    currentSlot.value = "edit";
    request({
        url: `/api/AccountSetOnlyAuth/Info?asId=${editAsId.value}`,
    }).then((res: IResponseModel<IAccountSetInfo>) => {
        isShow.value = true;
        editData.value = res.data;
    });
    getEisRelationInfoApi(as_id).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            isEisRelation.value = res.data !== null;
        } else {
            isEisRelation.value = false;
            ElNotify({
                type: "warning",
                message: res.msg[0],
            });
        }
    });
}

const copyAsId = ref(0);
function copyHandle(as_id: number) {
    copyAsId.value = as_id;
    currentSlot.value = "copy";
}

let accountSetCurrentDelelteAsid = -1;
let accountSetNextAsid = -1;
let accountSetName = "";
let asCreateDate = "";
let deleteConfirm = true;
function DeleteConfirm() {
    let deleteUrl = `/api/AccountSetOnlyAuth/DropAccountSetToRecycleBinV2?asId=${accountSetCurrentDelelteAsid}&serviceid=${getServiceId()}`;
    if (deleteConfirm) {       
         deleteBool.value = false;
        AppConfirmDialog(1010).then((r: boolean) => {
            if (r) {
                deleteAccount(deleteUrl);
            }
        });
    }
}

function deleteAccount(deleteUrl: string) {
    deleteConfirm = false;
    request({
        url: deleteUrl,
        method: "post",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    }).then((res: IResponseModel<boolean>) => {
        deleteConfirm = true;
        if (res.state === 1000) {
            if (res.subState === 0 && res.data) {
                ElNotify({
                    type: "success",
                    message: "亲，删除成功啦！",
                });
                thirdPartNotify(thirtPartNotifyTypeEnum.accountSetDeleteAccountSet, { asId: accountSetCurrentDelelteAsid }).then(() => {});
                if (accountSetNextAsid > 0) {
                    //如果不是最后一个账套
                    if (isLemonClient()) {
                        getLemonClient().setSelectedAccount(accountSetNextAsid);
                        return;
                    }
                    changeAS(accountSetNextAsid);
                } else {
                    //值为-1表示删除的已经是最后一个账套了
                    if (isLemonClient() && accountSetNextAsid === -1) {
                        getLemonClient().init();
                    } else if (window.isErp) {
                        //
                    } else {
                        //InitUI();
                        reloadAccountSetList();
                    }
                    if (accountData.value.length <= 1 && !isLemonClient()) {
                        closeCurrentTab();
                        globalWindowOpenPage("/Settings/AccountSets1", "新建账套");
                    }
                }
            } else {
                ElNotify({
                    type: "error",
                    message: res.msg,
                });
            }
        } else {
            ElNotify({
                type: "error",
                message: "亲，删除失败啦，请联系侧边栏客服！",
            });
        }
    });
}

function Delete(row: IAccountSetItem, index: number) {
    let asStartDate = row.asStartYear + "年" + (Number(row.asStartMonth) < 10 ? "0" + row.asStartMonth : row.asStartMonth) + "月";
    if (row.currentAs === 1) {
        let changeAsid = -1;
        if (accountData.value.length <= 1) {
            changeAsid = -1;
        } else {
            //先向后找，如果没找到，再向前找，排除不可使用的账套
            for (let i = index + 1; i < accountData.value.length; i++) {
                if (!accountData.value[i].isDisabled) {
                    changeAsid = accountData.value[i].asId;
                    break;
                }
            }
            if (changeAsid === -1) {
                for (let i = index - 1; i >= 0; i--) {
                    if (!accountData.value[i].isDisabled) {
                        changeAsid = accountData.value[i].asId;
                        break;
                    }
                }
            }
        }
        accountSetDeleteHandle(row.asId, function () {
            accountSetCurrentDelelteAsid = Number(row.asId);
            accountSetNextAsid = changeAsid;
            accountSetName = row.asName;
            asCreateDate = asStartDate;
            if (isThirdPart && isHideBarcode) {
                txtDeleteAccount.value = accountSetName;
                txtAsStartDate.value = asCreateDate;
                deleteBool.value = true;
            } else {
                DeleteAccountSetModal();
            }
        });
    } else {
        accountSetDeleteHandle(row.asId, function () {
            accountSetCurrentDelelteAsid = row.asId;
            accountSetNextAsid = useAccountSetStoreHook().accountSet!.asId;
            accountSetName = row.asName;
            asCreateDate = asStartDate;
            if (isThirdPart && isHideBarcode) {
                txtDeleteAccount.value = accountSetName;
                txtAsStartDate.value = asCreateDate;
                deleteBool.value = true;
            } else {
                DeleteAccountSetModal();
            }
        });
    }
}

function accountSetDeleteHandle(accountAsid: number, validCallback: Function) {
    checkIsPayAccountSet(accountAsid, function () {
        checkDeletePermission(accountAsid, validCallback);
    });
    deleteBool.value = false;
}

function DeleteAccountSetModal() {
    txtDeleteAccount.value = accountSetName;
    txtAsStartDate.value = asCreateDate;
    deleteBool.value = true;
}

function checkIsPayAccountSet(accountSetId: number, passdCallback: () => void) {
    request({
        url: `/api/AccountSetOnlyAuth/IsPayAccountSet?asid=${accountSetId}`,
        method: "post",
    }).then((res: any) => {
        if (res.data === 0) {
            passdCallback();
        } else {
            ElConfirm(
                "这是一个付费的账套！删除后，所有的账套信息，包括" +
                    "都会被删除，并且已经支付的费用不会返还，请慎重操作！如有需要，您可以在回收站重新还原账套。"
            ).then((r: boolean) => {
                if (r) {
                    passdCallback();
                }
            });
        }
    });
}

function checkDeletePermission(accountSetId: number, okCallback: Function) {
    request({
        url: `/api/AccountSetOnlyAuth/CheckDelete?asId=${accountSetId}`,
        method: "post",
    }).then((res: any) => {
        if (res.data === "Success") {
            okCallback();
        } else {
            ElConfirm("亲，您在当前账套没有删除权限，只有账套管理员具有删除权限，请您核对！");
        }
    });
}

function EditLockAccout(asid: number, asname: string) {
    //加验证
    AppConfirmDialog(1150).then((r: boolean) => {
        if (r) {
            request({
                url: `/api/User/UserInfo`,
                method: "post",
            }).then((res: any) => {
                editPhone.value = res.data.data.mobile.replace(/^(\d{3})(\d{4})(\d{4})$/, "$1 $2 $3");
                editAsName.value = asname;
                editId.value = asid;
                editAccountSetLockVisible.value = true;
                (document.getElementById("editAccountSetLockPage1") as HTMLElement).style.display = "block";
                (document.getElementById("editAccountSetLockPage2") as HTMLElement).style.display = "none";
                editAccountSetLockCaptcha.value = "";
            });
        }
    });
}

let canSendSms = true;
function editAccountsetLockSendCaptcha() {
    if (!canSendSms) return;
    canSendSms = false;
    captchaStatus.value = "正在发送...";
    request({
        url:
            window.accountSrvHost +
            "/Default/Services/SendSMSForConfirm.ashx" +
            "?CurrentSystemType=1&" +
            "Phone=" +
            editPhone.value.replace(/\s/g, "") +
            "&stype=14",
    }).then((res: any) => {
        if (res === "Success") {
            editAccountsetLockDaojishi(60);
        } else {
            ElNotify({
                type: "warning",
                message: "亲，验证码发送太频繁，请1小时后再试。如需帮助，请联系客服",
            });
            captchaStatus.value = "获取验证码";
        }
        canSendSms = true;
    });
}

let accountsetLock: number;
function editAccountsetLockDaojishi(seconds: number) {
    if (seconds < 0) {
        captchaStatus.value = "获取验证码";
        canSendSms = true;
        return;
    }
    captchaStatus.value = seconds + "s";
    seconds--;
    accountsetLock = setTimeout(function () {
        editAccountsetLockDaojishi(seconds);
    }, 1000);
}

function EditAccountSetLockConfirm1() {
    let captcha = editAccountSetLockCaptcha.value.trim();
    if (!captcha) {
        ElNotify({
            type: "warning",
            message: "请输入验证码！",
        });
        return;
    }
    if (!/\d{6}/.test(captcha)) {
        ElNotify({
            type: "warning",
            message: "验证码错误！",
        });
        return;
    }
    const data = {
        mobile: editPhone.value.replace(/\s/g, ""),
        captcha,
    };
    request({
        url: `/api/ConfirmCode/Verify`,
        method: "post",
        data,
    }).then((res: any) => {
        if (res.data) {
            accountsetLock && clearTimeout(accountsetLock);
            (document.getElementById("editAccountSetLockPage1") as HTMLElement).style.display = "none";
            (document.getElementById("editAccountSetLockPage2") as HTMLElement).style.display = "block";
        } else {
            ElNotify({
                type: "warning",
                message: "验证码错误！",
            });
        }
    });
}

function EditAccountSetLockConfirm2() {
    switch (editAccountSetLockType.value) {
        case "1":
            editAccountSetLockVisible.value = false;
            resetAsName.value = editAsName.value; //asname;
            resetLockVisible.value = true;
            // LockAccout();
            break;
        case "2":
            ElConfirm("确认取消密码锁吗?").then((r: boolean) => {
                if (r) {
                    let url =
                        "/api/AccountSetOnlyAuth/UnLock?asId=" +
                        editId.value +
                        `&captcha=${editAccountSetLockCaptcha.value.trim()}&phoneNum=${editPhone.value.replace(/\s/g, "")}`;
                    request({
                        url: url,
                        method: "post",
                    }).then((res: any) => {
                        if (res.state === 1000 && res.data === true) {
                            editAccountSetLockVisible.value = false;
                            getAccountData();
                            ElNotify({
                                type: "success",
                                message: "保存成功！",
                            });
                        } else if (res.state === 2000) {
                            ElNotify({
                                type: "warning",
                                message: res.msg,
                            });
                        } else {
                            ElNotify({
                                type: "warning",
                                message: "保存失败！",
                            });
                        }
                    });
                }
            });
            break;
    }
}

function lockAccoutScan(asid: number, asname: string) {
    AppConfirmDialog(1140).then((r: boolean) => {
        if (r) {
            lockAccount(asid, asname);
        }
    });
}

function lockAccount(asid: number, asname: string) {
    setAsName.value = asname;
    setAsId.value = asid;
    editId.value = asid;
    setLockVisible.value = true;
}

function LockAccout() {
    // setAsName.value = asname
    if (!password.value) {
        ElNotify({
            type: "warning",
            message: "亲，请输入密码！",
        });
        return;
    }
    if (!/^[a-zA-Z\d]{6,12}$/.test(password.value)) {
        ElNotify({
            type: "warning",
            message: "密码由6-12位字母或数字组成！",
        });
        return;
    }
    if (password.value.trim() !== confirmPassword.value.trim()) {
        ElNotify({
            type: "warning",
            message: "亲，两次输入密码不同哦！",
        });
        return;
    }
    request({
        url: `/api/AccountSetOnlyAuth/Lock?asId=${editId.value}&lockPassword=${password.value}`,
        method: "post",
    }).then((res: any) => {
        if (res.data) {
            ElNotify({
                type: "success",
                message: "保存成功！",
            });
            resetLockVisible.value = false;
            setLockVisible.value = false;
            if (editId.value === asId) {
                // if (isLemonClient()) {
                //     getLemonClient().init();
                // } else {
                setTimeout(function () {
                    setTopLocationhref("/Default/Default?appasid=" + getGlobalToken());
                }, 500);
                // }
            } else {
                getAccountData();
            }
        }
    });
}

watch(setLockVisible, (val) => {
    if (!val) {
        password.value = "";
        confirmPassword.value = "";
    }
});

function HasPermission(obj: IAccountSetItem, permissions: string[]): boolean {
    let flag = true;
    for (let i = 0; i < permissions.length; i++)
        if (!obj.permissionFunctionCode.includes(permissions[i])) {
            flag = false;
            break;
        }

    return flag;
}

onMounted(() => {
    searchText.value = "";
    initsearchText.value = "";
    if (!window.isAccountingAgent) {
        isShow.value = true;
        getAccountData();
    }
    // 账套升级后刷新事件
    window.addEventListener("refresh-accountsets-list", getAccountData);
});

const childActiveName = ref("first");
watch(currentSlot, (val) => {
    if (val !== "edit") {
        childActiveName.value = "first";
    } else {
        if ("true" === router.currentRoute.value.query?.isFromTax||router.currentRoute.value.query?.isEditModule) {
            return;
        } else {
            editAccountSetRef.value.resetActiveName();
        }
    }
    if (val === "main") {
        searchText.value = "";
        // getAccountData();
    }
});
watch(editAccountSetLockVisible, () => {
    editAccountSetLockCaptcha.value = "";
    clearTimeout(accountsetLock);
    captchaStatus.value = "获取验证码";
});

function setActiveInfo() {
    editHandle(asId as number);
    childActiveName.value = router.currentRoute.value.query?.isFromTax ? "third" : "second";
}

onActivated(() => {
    const isFromTax = router.currentRoute.value.query?.isFromTax;
    const isEditModule = router.currentRoute.value.query?.isEditModule;
    if ("true" === isFromTax || "true" === isEditModule) {
        setActiveInfo();
    }
});

onUnmounted(() => {
    // 取消监听账套刷新
    window.removeEventListener("refresh-accountsets-list", getAccountData);
});
</script>

<style scoped lang="less">
@import "@/style/Settings/AccountSets.less";

.operator {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
:deep(.el-table--default .cell) {
    padding: 0 4px !important;
}
:deep(.el-table) {
    .el-scrollbar__bar.is-horizontal {
        position: absolute;
        bottom: 0px;
        height: 10px;
        border-radius: 10px;
    }
}
.button.hover-red {
    &:hover {
        background-color: #fd5055;
        color: #fff;
        border-color: #fd5055;
    }
}
.flush {
    line-height: 40px !important;
    height: 40px !important;
}
.main-center {
    .main-center-loading {
        text-align: center;
        line-height: 400px;
    }
}
.main-content .main-top {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--white);
}
</style>
