import { request } from "@/util/service";

export interface ISubjectTree {
    id: string;
    text: string;
    attributes: {
        code: string;
        name: string;
        fullName: string;
    };
    children?: ISubjectTree[];
    label?: string;
}

export const getAsubTree = (asubType: number, showDisabled = false) => {
    const canuse = showDisabled ? "0" : "1";
    return request({
        url: `/api/SubsidiaryLedger/Tree?ASUB_TYPE=${asubType}&canuse=${canuse}`,
        method: "get",
    });
}

export function formatData(data: ISubjectTree[]) {
    const list = data.slice(0);
    return list.map((item) => {
        item.label = item.attributes.code + " " + item.attributes.fullName;
        if (item.children && item.children.length > 0) {
            formatData(item.children);
        }
        return item;
    });
}

export function expansionTreeList(list: ISubjectTree[]) {
    const newList: ISubjectTree[] = [];
    for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (!item.children || item.children?.length === 0) {
            newList.push(item);
        } else {
            newList.push(...expansionTreeList(item.children));
        }
    }
    return newList;
}    