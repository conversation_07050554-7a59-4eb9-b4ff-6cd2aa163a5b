import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router"
import { useVisitedRoutesStore } from "@/store/modules/visitedRoutes"

const routes: Array<RouteRecordRaw> = [
  {
    //路由初始指向
    path: "/",
    name: "Home",
    component: { render: () => null },
  },
  {
    path: "/TaxDeclaration",
    name: "TaxDeclaration",
    meta: {
      title: "税种申报",
    },
    component: () => import("@/views/TaxDeclaration/index.vue"),
  },
  {
    path: "/TaxReportSettings",
    name: "TaxReportSettings",
    meta: {
      title: "报税设置",
    },
    component: () => import("@/views/TaxReportSettings/index.vue"),
  },
  {
    path: "/TaxManagement",
    name: "TaxManagement",
    meta: {
      title: "税费种管理",
    },
    component: () => import("@/views/TaxManagement/index.vue"),
  },
  {
    path: "/InvoiceAccess",
    name: "InvoiceAccess",
    meta: {
      title: "发票获取",
    },
    component: () => import("@/views/InvoiceAccess/index.vue"),
  },
  {
    path: "/InvoiceSelection/DeductionSelection",
    name: "DeductionSelection",
    meta: {
      title: "抵扣勾选",
    },
    component: () => import("@/views/InvoiceSelection/DeductionSelection/index.vue"),
  },
  {
    path: "/InvoiceSelection/NonDeductionSelection",
    name: "NonDeductionSelection",
    meta: {
      title: "不抵扣勾选",
    },
    component: () => import("@/views/InvoiceSelection/NonDeductionSelection/index.vue"),
  },
  {
    path: "/InvoiceSelection/CertificationResults",
    name: "CertificationResults",
    meta: {
      title: "统计确认结果",
    },
    component: () => import("@/views/InvoiceSelection/CertificationResults/index.vue"),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

router.beforeEach((to, from, next) => {
  const routerArrayStore = useVisitedRoutesStore()
  routerArrayStore.enterRouter({
    name: to.name?.toString() || "",
    title: (to.meta.title as string) || to.fullPath,
    path: to.path,
    fullPath: to.fullPath,
    alive: false,
    cache: true,
  })
  next()
})

export default router
