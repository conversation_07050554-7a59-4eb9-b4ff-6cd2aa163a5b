import { createRouter, createWebHistory, type RouteRecordRaw } from "vue-router";
import HomeView from "@/views/HomeView.vue";
import GeneralLedgerView from "@/views/AccountBooks/GeneralLedger/index.vue";
import BalanceSheetView from "@/views/Statements/BalanceSheet/index.vue";
import CustomStatementView from "@/views/Statements/CustomStatement/index.vue";
import VoucherGroupView from "@/views/Settings/VoucherGroup/index.vue";
import CurrencyView from "@/views/Settings/Currency/index.vue";
import AssistingAccountingView from "@/views/Settings/AssistingAccounting/index.vue";
import OperationLogView from "@/views/Settings/OperationLog/index.vue";
import BosspermissionView from "@/views/Settings/Bosspermission/index.vue";
import FixedAssetTypeView from "@/views/FixedAssets/FixedAssetType/index.vue";
import IncomeStatementView from "@/views/Statements/IncomeStatement/index.vue";
import IncomeQuarterSheetView from "@/views/Statements/IncomeQuarterSheet/index.vue";
import FinancialSummaryView from "@/views/Statements/FinancialSummary/index.vue";
import CashFlowQuarterStatementView from "@/views/Statements/CashFlowQuarterStatement/index.vue";
import CashFlowStatementView from "@/views/Statements/CashFlowStatement/index.vue";
import DepartmentIncomeStatementView from "@/views/Statements/DepartmentIncomeStatement/index.vue";
import ProjectIncomeAssistStatementView from "@/views/Statements/ProjectIncomeStatement/index.vue";
import BusinessActivityStatementView from "@/views/Statements/BusinessActivityStatement/index.vue";
import SurplusDistributionStatementView from "@/views/Statements/SurplusDistributionStatement/index.vue";
import MemberEquityChangesStatementView from "@/views/Statements/MemberEquityChangesStatement/index.vue";
import TrialBalanceView from "@/views/AccountBooks/TrialBalance/index.vue";
import CategorizedAccountsSummaryView from "@/views/AccountBooks/CategorizedAccountsSummary/index.vue";
import SubsidiaryLedgerView from "@/views/AccountBooks/SubsidiaryLedger/index.vue";
import AASubsidiaryLedgerView from "@/views/AccountBooks/AASubsidiaryLedger/index.vue";
import JournalView from "@/views/AccountBooks/Journal/index.vue";
import MultiColumnLedgerView from "@/views/AccountBooks/MultiColumnLedger/index.vue";
import CashJournalView from "@/views/Cashier/CashJournal/index.vue";
import BankAccPreOpen from "@/views/Cashier/BankAccPreOpen/index.vue";
import DepositJournalView from "@/views/Cashier/DepositJournal/index.vue";
import TransferView from "@/views/Cashier/Transfer/index.vue";
import DraftView from "@/views/Cashier/Draft/index.vue";
import AATrialBalanceView from "@/views/AccountBooks/AATrialBalance/index.vue";
import FixedAssetsView from "@/views/FixedAssets/FixedAssets/index.vue";
import DepreciationLedgerView from "@/views/FixedAssets/DepreciationLedger/index.vue";
import FASummaryView from "@/views/FixedAssets/FASummary/index.vue";
import FACheckView from "@/views/FixedAssets/FACheck/index.vue";
import FACheckDetailView from "@/views/FixedAssets/FACheckDetail/index.vue";
import CDCheckView from "@/views/Cashier/CDCheck/index.vue";
import CDCheckDetailView from "@/views/Cashier/CDCheckDetail/index.vue";
import BankAndCompanyView from "@/views/Cashier/BankAndCompany/index.vue";
import SalaryManageView from "@/views/Salary/SalaryManage/index.vue";
import CDAccountView from "@/views/Cashier/CDAccount/index.vue";
import IETypeView from "@/views/Cashier/IEType/index.vue";
import ReportView from "@/views/Cashier/Report/index.vue";
import CheckoutView from "@/views/Checkout/index.vue";
import EmployInfoView from "@/views/Salary/EmployInfo/index.vue";
import SalarySummaryView from "@/views/Salary/SalarySummary/index.vue";
import InsuranceSet from "@/views/Salary/InsuranceSet/index.vue";
import AccountSets1View from "@/views/Settings/AcconutSets1/index.vue";
import AccountSubjectView from "@/views/Settings/AccountSubject/index.vue";
import TaxDeclarationView from "@/views/Tax/index.vue";
import TaxReportView from "@/views/Statements/TaxReport/index.vue";
import VoucherTemplateView from "@/views/Settings/VoucherTemplate/index.vue";
import BusinessVoucherView from "@/views/Erp/BusinessVoucher/index.vue";
import PermissionsView from "@/views/Settings/Permissions/index.vue";
import TransferProView from "@/views/Settings/TransferPro/index.vue";
import BusinessVoucherTemplateView from "@/views/Erp/BusinessVoucherTemplate/index.vue";
import ReinitializeView from "@/views/Settings/Reinitialize/index.vue";
import ImportFromOtherView from "@/views/Settings/ImportFromOther/index.vue";
import AsubRelationSettingsView from "@/views/Erp/AsubRelationSettings/index.vue";
import AsubRelationSettings1View from "@/views/Erp/AsubRelationSettings1/index.vue";
import BackupView from "@/views/Settings/Backup/index.vue";
import ERecordView from "@/views/ERecord/index.vue";
import InitialBalanceView from "@/views/Settings/InitialBalance/index.vue";
import ScmRelationView from "@/views/Scm/ScmRelation/index.vue";
import ScmSettingsView from "@/views/Scm/ScmSettings/index.vue";
import ScmBasicDataView from "@/views/Scm/ScmBasicData/index.vue";
import ScmVoucherView from "@/views/Scm/ScmVoucher/index.vue";
import MiniProgramBindingView from "@/views/MiniProgramBinding/index.vue";
// import InvoiceView from "@/views/Invoice/index.vue";
import SalesInvoiceView from "@/views/Invoice/SalesInvoice/index.vue";
import PurchaseInvoiceView from "@/views/Invoice/PurchaseInvoice/index.vue";
import TaxCalculationView from "@/views/Invoice/TaxCalculation/index.vue";
import InvoiceRiskAnalyView from "@/views/Invoice/RiskAnaly/index.vue";
import ExpenseBill from "@/views/Invoice/ExpenseBill/index.vue";
import CashierInvoiceTable from "@/views/Invoice/CashierInvoiceTable/index.vue";
import CashierInvoiceInfo from "@/views/Invoice/CashierInvoiceInfo/index.vue";
import VoucherListView from "@/views/Voucher/VoucherList/index.vue";
import DefaultView from "@/views/Default/index.vue";
import NewVoucherView from "@/views/Voucher/NewVoucher/index.vue";
import VoucherPageView from "@/views/Voucher/VoucherPage/index.vue";
import CDJournalView from "@/views/Cashier/CDJournal/index.vue";
import IEJournalView from "@/views/Cashier/IEJournal/index.vue";
import AAJournalView from "@/views/Cashier/AAJournal/index.vue";
import JournalPageView from "@/views/Cashier/JournalPage/index.vue";
import AlipayImportJournalView from "@/views/Cashier/AlipayImportJournal/index.vue";
import ExcessiveNumberOrRowsErrorPageView from "@/views/AccountBooks/ExcessiveNumberOrRowsErrorPage/index.vue";
import ExistsAsyncFileProcessErrorPageView from "@/views/AccountBooks/ExistsAsyncFileProcessErrorPage/index.vue";
import ClientInitView from "@/views/Client/ClientInit/index.vue";
import BackUpPlusView from "@/views/Settings/BackUpPlus/index.vue";
import PayDialogView from "@/views/Pro/PayDialog/index.vue";
import PersonalInfo from "@/views/Default/PersonalInfo/index.vue";
import AccountingEntry from "@/views/MasterPages/AccountingEntry/index.vue";
import MessageSearchView from "@/views/MasterPages/MessageSearch/index.vue";
import AlipayImportJournalRedirectRouteView from "@/views/Cashier/AlipayImportJournalRedirectRoute/index.vue";
import DepartmentJournalView from "@/views/Cashier/DepartmentJournal/index.vue";
import ProjectJournalView from "@/views/Cashier/ProjectJournal/index.vue";
import CombineJournalView from "@/views/Cashier/CombineJournal/index.vue";
import AgeAnalysis from "@/views/AccountBooks/AgeAnalysis/index.vue";
import ReLoginView from "@/views/410/index.vue";
import ExpensesStatement from "@/views/Statements/ExpensesStatement/index.vue";
import AssistCombineStatementView from "@/views/AccountBooks/AssistCombineStatement/index.vue";
import IncomeAndExpenditureSheetView from "@/views/Statements/IncomeAndExpenditureSheet/index.vue";
import CostSheetView from "@/views/Statements/CostSheet/index.vue";
import IncomeAndDistributionSheetView from "@/views/Statements/IncomeAndDistributionSheet/index.vue";
import OwerEquityChangeStatementView from "@/views/Statements/OwerEquityChangeStatement/index.vue";
import StandardCashFlowStatementView from "@/views/Statements/StandardCashFlowStatement/index.vue";
import CashFlowAdjustmentView from "@/views/Statements/CashFlowAdjustment/index.vue";
import CashflowAssumptionView from "@/views/Statements/CashflowAssumption/index.vue";
import VoucherSplitView from "@/views/Statements/VoucherSplit/index.vue";
import FetchInvoiceView from "@/views/Invoice/FetchInvoice/index.vue";
import SeniorCoverPrint from "@/views/Voucher/SeniorCoverPrint/index.vue";
import PreviewPrint from "@/views/Voucher/PreviewPrint/index.vue";
import MidPages from "@/views/MidPages/index.vue";
import BeginnersGuideView from "@/views/BeginnersGuide/index.vue";
import { createOperationLogApi } from "@/api/operationLog";
import { MainMenuType, SubMenuType } from "@/constants/menuKey";
import { getGlobalToken } from "@/util/baseInfo";

/** 常驻路由 */
export const constantRoutes: RouteRecordRaw[] = [
    {
        path: "/",
        name: "home",
        component: HomeView,
    },
    {
        path: "/Default/Default",
        name: "Default",
        component: DefaultView,
        meta: {
            title: "首页",
            mainMenu: MainMenuType.BaseFunction,
            subMenu: SubMenuType.HomePage,
        },
    },
    {
        path: "/Default/PersonalInfo",
        name: "PersonalInfo",
        component: PersonalInfo,
        meta: {
            title: "个人设置",
        },
    },
    {
        path: "/BeginnersGuide",
        name: "BeginnersGuide",
        component: BeginnersGuideView,
        meta: {
            title: "新手指引",
        },
    },
    {
        path: "/AccountBooks/GeneralLedger",
        name: "GeneralLedger",
        component: GeneralLedgerView,
        meta: {
            title: "总账",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.GeneralLedger,
        },
    },
    {
        path: "/FixedAssets/FixedAssetType",
        name: "FixedAssetType",
        component: FixedAssetTypeView,
        meta: {
            title: "资产类别设置",
            mainMenu: MainMenuType.FixAsset,
            subMenu: SubMenuType.FixedAssetType,
        },
    },
    {
        path: "/Statements/BalanceSheet",
        name: "BalanceSheet",
        component: BalanceSheetView,
        meta: {
            title: "资产负债表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.BalanceSheet,
        },
    },
    {
        path: "/Statements/CustomStatement",
        name: "CustomStatement",
        component: CustomStatementView,
        meta: {
            title: "数据透视表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.CustomStatement,
        },
    },
    {
        path: "/Settings/VoucherGroup",
        name: "VoucherGroup",
        component: VoucherGroupView,
        meta: {
            title: "凭证字",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.VoucherGroup,
            cache: true,
        },
    },
    {
        path: "/Settings/Currency",
        name: "Currency",
        component: CurrencyView,
        meta: {
            title: "币别",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.Currency,
            cache: true,
        },
    },
    {
        path: "/Settings/AssistingAccounting",
        name: "AssistingAccounting",
        component: AssistingAccountingView,
        meta: {
            title: "辅助核算",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.AssistingAccounting,
        },
    },
    {
        path: "/Settings/OperationLog",
        name: "OperationLog",
        component: OperationLogView,
        meta: {
            title: "操作日志",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.OperationLog,
        },
    },
    {
        path: "/Settings/Bosspermission",
        name: "Bosspermission",
        component: BosspermissionView,
        meta: {
            title: "老板看账",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.BossPermission,
        },
    },
    {
        path: "/Statements/IncomeStatement",
        name: "IncomeStatement",
        component: IncomeStatementView,
        meta: {
            title: "利润表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.IncomeStatement,
        },
    },
    {
        path: "/Statements/IncomeQuarterSheet",
        name: "IncomeQuarterSheet",
        component: IncomeQuarterSheetView,
        meta: {
            title: "利润表季报",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.IncomeQuaterStatement,
        },
    },
    {
        path: "/Statements/FinancialSummary",
        name: "FinancialSummary",
        component: FinancialSummaryView,
        meta: {
            title: "财务概要信息",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.FinancialSummary,
        },
    },
    {
        path: "/Statements/CashFlowQuarterStatement",
        name: "CashFlowQuarterStatement",
        component: CashFlowQuarterStatementView,
        meta: {
            title: "现金流量表季报",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.CashFlowStatement,
        },
    },
    {
        path: "/Statements/CashFlowStatement",
        name: "CashFlowStatement",
        component: CashFlowStatementView,
        meta: {
            title: "现金流量表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.CashFlowStatement,
        },
    },
    {
        path: "/Statements/DepartmentIncomeStatement",
        name: "DepartmentIncomeStatement",
        component: DepartmentIncomeStatementView,
        meta: {
            title: "部门利润表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.AssistIncomeStatementDepartment,
        },
    },
    {
        path: "/Statements/ProjectIncomeAssistStatement",
        name: "ProjectIncomeAssistStatement",
        component: ProjectIncomeAssistStatementView,
        meta: {
            title: "项目利润表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.AssistIncomeStatementProject,
        },
    },
    {
        path: "/Statements/BusinessActivityStatement",
        name: "BusinessActivityStatement",
        component: BusinessActivityStatementView,
        meta: {
            title: "业务活动表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.BusinessActivity,
        },
    },
    {
        path: "/Statements/SurplusDistributionStatement",
        name: "SurplusDistributionStatement",
        component: SurplusDistributionStatementView,
        meta: {
            title: "盈余及盈余分配表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.SurplusDistributionStatement,
        },
    },
    {
        path: "/Statements/MemberEquityChangesStatement",
        name: "MemberEquityChangesStatement",
        component: MemberEquityChangesStatementView,
        meta: {
            title: "成员权益变动表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.MemberEquityChangesStatament,
        },
    },
    {
        path: "/AccountBooks/TrialBalance",
        name: "TrialBalance",
        component: TrialBalanceView,
        meta: {
            title: "科目余额表",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.TrialBalance,
        },
    },
    {
        path: "/AccountBooks/CategorizedAccountsSummary",
        name: "CategorizedAccountsSummary",
        component: CategorizedAccountsSummaryView,
        meta: {
            title: "科目汇总表",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.CategorizedAccountsSummary,
        },
    },
    {
        path: "/AccountBooks/SubsidiaryLedger",
        name: "SubsidiaryLedger",
        component: SubsidiaryLedgerView,
        meta: {
            title: "明细账",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.SubsidiaryLedger,
        },
    },
    {
        path: "/AccountBooks/AASubsidiaryLedger",
        name: "AASubsidiaryLedger",
        component: AASubsidiaryLedgerView,
        meta: {
            title: "核算项目明细账",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.AASubsidiaryLedger,
        },
    },
    {
        path: "/AccountBooks/Journal",
        name: "Journal",
        component: JournalView,
        meta: {
            title: "序时账",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.Journal,
        },
    },
    {
        path: "/AccountBooks/MultiColumnLedger",
        name: "MultiColumnLedger",
        component: MultiColumnLedgerView,
        meta: {
            title: "多栏账",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.MultiColumnLedger,
        },
    },
    {
        path: "/AccountBooks/AgeAnalysis",
        name: "AgeAnalysis",
        component: AgeAnalysis,
        meta: {
            title: "账龄分析表",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.AgeAnalysis,
        },
    },
    {
        path: "/Cashier/CashJournal",
        name: "CashJournal",
        component: CashJournalView,
        meta: {
            title: "现金日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.CashJournal,
        },
    },
    {
        path: "/AccountBooks/AATrialBalance",
        name: "AATrialBalance",
        component: AATrialBalanceView,
        meta: {
            title: "核算项目余额表",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.AATrialBalance,
        },
    },
    {
        path: "/FixedAssets/FixedAssets",
        name: "FixedAssets",
        component: FixedAssetsView,
        meta: {
            title: "资产管理",
            mainMenu: MainMenuType.FixAsset,
            subMenu: SubMenuType.FixedAssets,
        },
    },
    {
        path: "/FixedAssets/DepreciationLedger",
        name: "DepreciationLedger",
        component: DepreciationLedgerView,
        meta: {
            title: "折旧摊销明细表",
            mainMenu: MainMenuType.FixAsset,
            subMenu: SubMenuType.DepreciationLedger,
        },
    },
    {
        path: "/FixedAssets/FASummary",
        name: "FASummary",
        component: FASummaryView,
        meta: {
            title: "资产汇总",
            mainMenu: MainMenuType.FixAsset,
            subMenu: SubMenuType.FASummary,
        },
    },
    {
        path: "/FixedAssets/FACheck",
        name: "FACheck",
        component: FACheckView,
        meta: {
            title: "资产核对总账",
            mainMenu: MainMenuType.FixAsset,
            subMenu: SubMenuType.FACheck,
        },
    },
    {
        path: "/FixedAssets/FACheckDetail",
        name: "FACheckDetail",
        component: FACheckDetailView,
        meta: {
            title: "资产差异明细",
            mainMenu: MainMenuType.FixAsset,
            subMenu: SubMenuType.FACheckDetail,
        },
    },
    {
        path: "/Cashier/Transfer",
        name: "Transfer",
        component: TransferView,
        meta: {
            title: "内部转账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.Transfer,
        },
    },
    {
        path: "/Cashier/DepositJournal",
        name: "DepositJournal",
        component: DepositJournalView,
        meta: {
            title: "银行日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.DepositJournal,
        },
    },
    {
        path: "/Cashier/CDCheck",
        name: "CDCheck",
        component: CDCheckView,
        meta: {
            title: "核对总账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.CDCheck,
        },
    },
    {
        path: "/Cashier/CDCheckDetail",
        name: "CDCheckDetail",
        component: CDCheckDetailView,
        meta: {
            title: "资金差异明细",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.CDCheckDetail,
        },
    },
    {
        path: "/Cashier/BankAndCompany",
        name: "BankAndCompany",
        component: BankAndCompanyView,
        meta: {
            title: "银企互联",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.BankAndCompany,
        },
    },
    {
        path: "/Draft/Draft",
        name: "Draft",
        component: DraftView,
        meta: {
            title: "票据管理",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.Draft,
        },
    },
    {
        path: "/Salary/SalaryManage",
        name: "SalaryManage",
        component: SalaryManageView,
        meta: {
            title: "工资管理",
            mainMenu: MainMenuType.Salary,
            subMenu: SubMenuType.SalaryManage,
        },
    },
    {
        path: "/Cashier/CDAccount",
        name: "CDAccount",
        component: CDAccountView,
        meta: {
            title: "账户设置",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.CDAccount,
        },
    },
    {
        path: "/Cashier/IEType",
        name: "IEType",
        component: IETypeView,
        meta: {
            title: "收支类别",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.IEType,
        },
    },
    {
        path: "/Cashier/Report",
        name: "Report",
        component: ReportView,
        meta: {
            title: "资金报表",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.Report,
        },
    },
    {
        path: "/Checkout/Checkout",
        name: "Checkout",
        component: CheckoutView,
        meta: {
            title: "期末结转",
            mainMenu: MainMenuType.CheckOut,
            subMenu: SubMenuType.Checkout,
        },
    },
    {
        path: "/Salary/EmployInfo",
        name: "EmployInfo",
        component: EmployInfoView,
        meta: {
            title: "员工信息",
            mainMenu: MainMenuType.Salary,
            subMenu: SubMenuType.EmployInfo,
        },
    },
    {
        path: "/Salary/SalarySummary",
        name: "SalarySummary",
        component: SalarySummaryView,
        meta: {
            title: "工资统计表",
            mainMenu: MainMenuType.Salary,
            subMenu: SubMenuType.SalarySummary,
        },
    },
    {
        path: "/Salary/InsuranceSet",
        name: "InsuranceSet",
        component: InsuranceSet,
        meta: {
            title: "五险一金设置",
            mainMenu: MainMenuType.Salary,
            subMenu: SubMenuType.InsuranceSet,
        },
    },
    {
        path: "/Settings/AccountSets",
        name: "AccountSets",
        component: () => import("@/views/Settings/AccountSets/index.vue"),
        meta: {
            title: "账套",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.AccountSets,
        },
    },
    {
        path: "/Settings/AccountSets1",
        name: "AccountSets1",
        component: AccountSets1View,
        meta: {
            title: "新建账套",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.AccountSets,
        },
    },
    {
        path: "/Settings/AccountSubject",
        name: "AccountSubject",
        component: AccountSubjectView,
        meta: {
            title: "科目",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.AccountingSubject,
        },
    },
    {
        path: "/Tax/TaxDeclaration",
        name: "TaxDeclaration",
        component: TaxDeclarationView,
        meta: {
            title: "税务申报",
            mainMenu: MainMenuType.Tax,
            subMenu: SubMenuType.TaxDeclaration,
        },
    },
    {
        path: "/Statements/TaxReport",
        name: "TaxReport",
        component: TaxReportView,
        meta: {
            title: "纳税统计表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.TaxReport,
        },
    },
    {
        path: "/Statements/ExpensesStatement",
        name: "ExpensesStatement",
        component: ExpensesStatement,
        meta: {
            title: "费用明细表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.ExpenseStatement,
        },
    },
    {
        path: "/AccountBooks/AssistCombineStatement",
        name: "AssistCombineStatement",
        component: AssistCombineStatementView,
        meta: {
            title: "核算项目组合表",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.AssistCombineStatement,
        },
    },
    {
        path: "/Settings/VoucherTemplate",
        name: "VoucherTemplate",
        component: VoucherTemplateView,
        meta: {
            title: "凭证模板",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.VoucherTemplate,
        },
    },
    {
        path: "/Erp/BusinessVoucher",
        name: "BusinessVoucher",
        component: BusinessVoucherView,
        meta: {
            title: "业务生成凭证",
            mainMenu: MainMenuType.ERP,
            subMenu: SubMenuType.BusinessVoucher,
        },
    },
    {
        path: "/Settings/Permissions",
        name: "Permissions",
        component: PermissionsView,
        meta: {
            title: "权限设置",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.Permissions,
        },
    },
    {
        path: "/Settings/TransferPro",
        name: "TransferPro",
        component: TransferProView,
        meta: {
            title: "账套升级",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.Permissions,
        },
    },
    {
        path: "/Erp/BusinessVoucherTemplate",
        name: "BusinessVoucherTemplate",
        component: BusinessVoucherTemplateView,
        meta: {
            title: "业务凭证模板",
            mainMenu: MainMenuType.ERP,
            subMenu: SubMenuType.BusinessVoucherTemplate,
        },
    },
    {
        path: "/Settings/Reinitialize",
        name: "Reinitialize",
        component: ReinitializeView,
        meta: {
            title: "重新初始化",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.Reinitialize,
        },
    },
    {
        path: "/Settings/ImportFromOther",
        name: "ImportFromOther",
        component: ImportFromOtherView,
        meta: {
            title: "旧账导入",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.ImportFromOther,
        },
    },
    {
        path: "/Erp/AsubRelationSettings",
        name: "AsubRelationSettings",
        component: AsubRelationSettingsView,
        meta: {
            title: "科目关联明细设置",
            mainMenu: MainMenuType.ERP,
            subMenu: SubMenuType.AsubRelationSettings,
        },
    },
    {
        path: "/Erp/AsubRelationSettings1",
        name: "AsubRelationSettings1",
        component: AsubRelationSettings1View,
        meta: {
            title: "科目关联设置",
            // mainMenu: MainMenuType.ERP,
            // subMenu: SubMenuType.AsubRelationSettings,
        },
    },
    {
        path: "/Settings/Backup",
        name: "Backup",
        component: BackupView,
        meta: {
            title: "备份恢复",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.BackUp,
        },
    },
    {
        path: "/ERecord/ERecord",
        name: "ERecord",
        component: ERecordView,
        meta: {
            title: "会计电子档案",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.LemonDisk,
        },
    },
    {
        path: "/Settings/InitialBalance1",
        name: "InitialBalance1",
        component: InitialBalanceView,
        meta: {
            title: "期初",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.InitialBalance,
        },
    },
    {
        path: "/Scm/ScmRelation",
        name: "ScmRelation",
        component: ScmRelationView,
        meta: {
            title: "关联进销存",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.ScmRelation,
        },
    },
    {
        path: "/Settings/EisRelation",
        name: "EisRelation",
        component: ()=>import("@/views/Settings/EisRelation/index.vue"),
        meta: {
            title: "关联云发票",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.EisRelation,
        },
    },
    {
        path: "/Scm/ScmSettings",
        name: "ScmSettings",
        component: ScmSettingsView,
        meta: {
            title: "进销存核算参数",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.ScmSettings,
        },
    },
    {
        path: "/Scm/ScmBasicData",
        name: "ScmBasicData",
        component: ScmBasicDataView,
        meta: {
            title: "进销存基础资料",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.ScmBasicData,
        },
    },
    {
        path: "/MiniProgramBinding/MiniProgramBinding",
        name: "MiniProgramBinding",
        component: MiniProgramBindingView,
        meta: {
            title: "收票宝",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.LemonDiskSPB,
        },
    },
    {
        path: "/Invoice/SalesInvoice",
        name: "SalesInvoice",
        component: SalesInvoiceView,
        meta: {
            title: "销项发票",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.SalesInvoice,
        },
    },
    {
        path: "/Invoice/PurchaseInvoice",
        name: "PurchaseInvoice",
        component: PurchaseInvoiceView,
        meta: {
            title: "进项发票",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.PurchaseInvoice,
        },
    },
    {
        path: "/Invoice/FetchInvoice",
        name: "FetchInvoice",
        component: FetchInvoiceView,
        meta: {
            title: "一键取票",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.FetchInvoice,
        },
    },
    {
        path: "/Invoice/TaxCalculation",
        name: "TaxCalculation",
        component: TaxCalculationView,
        meta: {
            title: "增值税测算",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.TaxCalculation,
        },
    },
    {
        path: "/Invoice/RiskAnaly",
        name: "InvoiceRiskAnaly",
        component: InvoiceRiskAnalyView,
        meta: {
            title: "发票风险分析",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.RiskAnaly,
        },
    },
    {
        path: "/Invoice/ExpenseBill",
        name: "ExpenseBill",
        component: ExpenseBill,
        meta: {
            title: "费用单据",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.ExpenseBill,
        },
    },
    {
        path: "/Invoice/CashierInvoiceTable",
        name: "CashierInvoiceTable",
        component: CashierInvoiceTable,
        meta: {
            title: "发票资金一览表",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.CashierInvoiceTable,
        },
    },
    {
        path: "/Invoice/CashierInvoiceInfo",
        name: "CashierInvoiceInfo",
        component: CashierInvoiceInfo,
        meta: {
            title: "发票资金一览表明细",
            mainMenu: MainMenuType.Invoice,
            subMenu: SubMenuType.CashierInvoiceInfo,
        },
    },
    {
        path: "/Voucher/VoucherList",
        name: "VoucherList",
        component: VoucherListView,
        meta: {
            title: "凭证列表",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.VoucherList,
        },
    },
    {
        path: "/Scm/ScmVoucher",
        name: "ScmVoucher",
        component: ScmVoucherView,
        meta: {
            title: "进销存凭证",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.ScmVoucher,
        },
    },
    {
        path: "/Voucher/NewVoucher",
        name: "NewVoucher",
        component: NewVoucherView,
        meta: {
            title: "新增凭证",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.NewVoucher,
        },
    },
    {
        path: "/Voucher/VoucherPage",
        name: "VoucherPage",
        component: VoucherPageView,
        meta: {
            title: "查看凭证",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.NewVoucher,
        },
    },
    {
        path: "/Cashier/CDJournal",
        name: "CDJournal",
        component: CDJournalView,
        meta: {
            title: "账户日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.CDJournal,
        },
    },
    {
        path: "/Cashier/IEJournal",
        name: "IEJournal",
        component: IEJournalView,
        meta: {
            title: "收支类别日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.IEJournal,
        },
    },
    {
        path: "/Cashier/AAJournal",
        name: "AAJournal",
        component: AAJournalView,
        meta: {
            title: "往来单位日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.AAJournal,
        },
    },
    {
        path: "/Cashier/DepartmentJournal",
        name: "DepartmentJournal",
        component: DepartmentJournalView,
        meta: {
            title: "部门日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.DepartmentJournal,
        },
    },
    {
        path: "/Cashier/ProjectJournal",
        name: "ProjectJournal",
        component: ProjectJournalView,
        meta: {
            title: "项目日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.ProjectJournal,
        },
    },
    {
        path: "/Cashier/CombineJournal",
        name: "CombineJournal",
        component: CombineJournalView,
        meta: {
            title: "组合类别日记账",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.AAJournalCombination,
        },
    },
    {
        path: "/Cashier/JournalPage",
        name: "JournalPage",
        component: JournalPageView,
        meta: {
            title: "收款凭据",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.JournalPage,
        },
    },
    {
        path: "/Cashier/AlipayImportJournal",
        name: "AlipayImportJournal",
        component: AlipayImportJournalView,
        meta: {
            title: "支付宝导入",
        },
    },
    {
        path: "/Pro/PayDialog",
        name: "PayDialog",
        iframeComponet: PayDialogView,
        meta: {
            title: "升级专业版",
        },
    },
    {
        path: "/Cashier/BankAccPreOpen",
        name: "BankAccPreOpen",
        component: BankAccPreOpen,
        meta: {
            title: "预约开户",
            mainMenu: MainMenuType.Cashier,
            subMenu: SubMenuType.BankAndCompany,
        },
    },
    {
        path: "/AccountBooks/ExcessiveNumberOrRowsErrorPage",
        name: "ExcessiveNumberOrRowsErrorPage",
        component: ExcessiveNumberOrRowsErrorPageView,
    },
    {
        path: "/AccountBooks/ExistsAsyncFileProcessErrorPage",
        name: "ExistsAsyncFileProcessErrorPage",
        component: ExistsAsyncFileProcessErrorPageView,
    },
    {
        path: "/Client/ClientInit",
        name: "ClientInit",
        component: ClientInitView,
    },
    {
        path: "/Settings/BackUpPlus",
        name: "BackUpPlus",
        component: BackUpPlusView,
        meta: {
            title: "数据归档",
            mainMenu: MainMenuType.Settings,
            subMenu: SubMenuType.BackUpPlus,
        },
    },
    {
        path: "/MasterPages/AccountingEntry",
        name: "AccountingEntry",
        component: AccountingEntry,
        meta: {
            title: "分录大全",
        },
    },
    {
        path: "/MasterPages/MessageSearch",
        name: "MessageSearch",
        component: MessageSearchView,
        meta: {
            title: "查询导航",
        },
    },
    {
        path: "/Cashier/AlipayImportJournalRedirectRoute",
        name: "AlipayImportJournalRedirectRoute",
        component: AlipayImportJournalRedirectRouteView,
        meta: {
            title: "支付宝导入",
        },
    },
    {
        path: "/410",
        name: "410",
        component: ReLoginView,
        meta: {
            title: "异常登陆",
        },
    },
    {
        path: "/Statements/IncomeAndExpenditureSheet",
        name: "IncomeAndExpenditureSheet",
        component: IncomeAndExpenditureSheetView,
        meta: {
            title: "收入支出表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.IncomeAndExpenditureStatement,
        },
    },
    {
        path: "/Statements/CostSheet",
        name: "CostSheet",
        component: CostSheetView,
        meta: {
            title: "成本费用表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.CostStatement,
        },
    },
    {
        path: "/Statements/IncomeAndDistributionSheet",
        name: "IncomeAndDistributionSheet",
        component: IncomeAndDistributionSheetView,
        meta: {
            title: "收益及收益分配表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.IncomeDistributionStatement,
        },
    },
    {
        path: "/Statements/OwerEquityChangeStatement",
        name: "OwerEquityChangeStatement",
        component: OwerEquityChangeStatementView,
        meta: {
            title: "所有者权益变动表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.OwerEquityChangeStatement,
        },
    },
    {
        path: "/Statements/StandardCashFlowStatement",
        name: "StandardCashFlowStatement",
        component: StandardCashFlowStatementView,
        meta: {
            title: "标准现金流量表",
            mainMenu: MainMenuType.Statement,
            subMenu: SubMenuType.StandardCashFlowStatement,
        },
    },
    {
        path: "/Statements/CashFlowAdjustment",
        name: "CashFlowAdjustment",
        component: CashFlowAdjustmentView,
        meta: {
            title: "现金流量调整",
            // mainMenu: MainMenuType.Statement,
            // subMenu: SubMenuType.CashFlowAdjustment,
        },
    },
    {
        path: "/Statements/CashflowAssumption",
        name: "CashflowAssumption",
        component: CashflowAssumptionView,
        meta: {
            title: "现金流量预设",
            // mainMenu: MainMenuType.Statement,
            // subMenu: SubMenuType.CashflowAssumption,
        },
    },
    {
        path: "/Statements/VoucherSplit",
        name: "VoucherSplit",
        component: VoucherSplitView,
        meta: {
            title: "凭证拆分",
            // mainMenu: MainMenuType.Statement,
            // subMenu: SubMenuType.VoucherSplit,
        },
    },
    {
        path: "/Voucher/SeniorVoucherPrint",
        name: "SeniorVoucherPrint",
        component: () => import("@/views/Voucher/SeniorVoucherPrint/index.vue"),
        meta: {
            title: "凭证打印设置",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.VoucherPrintSettings,
        },
    },
    {
        path: "/Voucher/SeniorCoverPrint",
        name: "SeniorCoverPrint",
        component: SeniorCoverPrint,
        meta: {
            title: "凭证封面打印设置",
            mainMenu: MainMenuType.Voucher,
            subMenu: SubMenuType.VoucherCoverSettings,
        },
    },
    {
        path: "/PreviewPrint",
        name: "PreviewPrint",
        component: PreviewPrint,
        meta: {
            title: '凭证',
        },
    },
    {
        path: "/MidPage",
        name: "MidPage",
        component: MidPages,
        meta: {
            title: "中间页",
        }
    },
    {
        path: "/AccountBooks/BooksCoverPrint",
        name: "BooksCoverPrint",
        component: ()=>import("@/views/AccountBooks/BooksCoverPrint/index.vue"),
        meta: {
            title: "账簿封面打印设置",
            mainMenu: MainMenuType.AccoutBook,
            subMenu: SubMenuType.AccountBookCoverSettings,
        },
    },
];

/**
 * 动态路由
 * 用来放置有权限 (Roles 属性) 的路由
 * 必须带有 Name 属性
 */
export const asyncRoutes: RouteRecordRaw[] = [];

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: constantRoutes,
});

// 加入百度统计
router.beforeEach((to, from, next) => {
    if (to.path) {
        if (window._hmt) {
            window._hmt.push(["_trackPageview", "/#" + to.fullPath]);
        }
    }
    next();
});

// 只有访问成功才记录日志
router.afterEach(async (to,from) => {
    // 未携带appasid 访问页面时，如果记录Log会报错
    if (to.meta.title) {
        document.title = to.meta.title + " - 柠檬云财税";
    } else {
        document.title = "柠檬云财税";
    }

    if (getGlobalToken() !== "") {
        if(from && to.name === from.name) return
        if (to.meta.mainMenu) {
            const mainMenu: MainMenuType = to.meta.mainMenu as MainMenuType;
            const subMenu: SubMenuType = to.meta.subMenu as SubMenuType;
            try {
                await createOperationLogApi(mainMenu, subMenu, subMenu === SubMenuType.AccountBookCoverSettings && typeof to.query.from === 'string' ? to.query.from as unknown as SubMenuType : 0 );
            } catch (error) {
                // handle error
            }
        }
    }
});

export function resetRouter() {
    // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
    try {
        router.getRoutes().forEach((route) => {
            const { name, meta } = route;
            if (name && meta.permissions?.length) {
                router.hasRoute(name) && router.removeRoute(name);
            }
        });
    } catch (error) {
        // 强制刷新浏览器也行，只是交互体验不是很好
        window.location.reload();
    }
}

export default router;
