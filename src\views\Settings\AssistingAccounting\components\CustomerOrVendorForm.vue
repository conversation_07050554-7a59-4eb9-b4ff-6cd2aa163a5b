<template>
    <div class="add-content">
        <el-form :model="formList" label-width="222px" ref="formRef" class="formRef">
            <div class="isRow">
                <el-form-item :label="props.name + '编码：'">
                    <input
                        type="text"
                        @input="handleAAInput(LimitCharacterSize.Code, $event, props.name + '编码', 'aaNum', changeFormListData)"
                        @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                        v-model="formList.aaNum"
                    />
                </el-form-item>
                <el-form-item :label="props.name + '名称：'">
                    <el-autocomplete
                        v-if="aaNameTextareaShow"
                        ref="oppositePartyRef"
                        v-model="formList.aaName"
                        :fetch-suggestions="querySearch"
                        :prop="[{ required: true, trigger: ['change'] }]"
                        :trigger-on-focus="false"
                        placeholder=" "
                        @blur="inputTypeBlur('aaName')"
                        type="textarea"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        resize="none"
                        class="oppositePartyInput middle"
                        :debounce="300"
                        maxlength="256"
                        @change="limitInputLength(formList.aaName,props.name + '名称')"
                        :teleported="false"
                        :fit-input-width="true"
                        popper-class="opposite-autocomplete-popper"
                        @focus="inputTypeFocus()"
                        @select="handleSelect"
                    >
                        <template #default="{ item }">
                            <div class="value">{{ item.value }}</div>
                        </template>
                    </el-autocomplete>
                    <Tooltip :content="formList.aaName"  :isInput="true" placement="right"  v-else>
                        <el-autocomplete
                            @focus="inputTypeFocus('aaName')"
                            ref="oppositePartyRef"
                            v-model="formList.aaName"
                            :fetch-suggestions="querySearch"
                            :prop="[{ required: true, trigger: ['change'] }]"
                            :trigger-on-focus="false"
                            placeholder=" "
                            class="oppositePartyInput middle"
                            :debounce="300"
                            :teleported="true"
                            :fit-input-width="true"
                            popper-class="opposite-autocomplete-popper"
                            @select="handleSelect"
                        >
                            <template #default="{ item }">
                                <Tooltip :content="item.value" :line-clamp="2" placement="right" :maxWidth='548' :teleported="true">
                                    <div class="value">{{ item.value }}</div>
                                </Tooltip>
                            </template>
                        </el-autocomplete>
                    </Tooltip>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item :label="props.name + '类别：'">
                <Tooltip :content="formList.aaEntity.type" :is-input="true" placement="right">
                    <input
                        type="text"
                        @input="handleAAInput(LimitCharacterSize.Default, $event, props.name + '类别', 'type', changeFormListData)"
                        @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                        v-model="formList.aaEntity.type"
                        class="input-ellipsis"
                    />
                </Tooltip>
                </el-form-item>
                <el-form-item label="统一社会信用代码：">
                <Tooltip :content="formList.aaEntity.taxNumber" :is-input="true" placement="right">
                    <input
                        type="text"
                        @input="handleAAInput(LimitCharacterSize.USCC, $event, '统一社会信用代码', 'taxNumber', changeFormListData)"
                        @paste="handleAAPaste(LimitCharacterSize.USCC, $event)"
                        v-model="formList.aaEntity.taxNumber"
                        class="middle"
                    />
                </Tooltip>
                </el-form-item>
            </div>
            <el-form-item label="经营地址：">
                <AreaBox :selected-area-info="areaInfo" @address="addressChange" ref="arearef" />
            </el-form-item>
            <el-form-item>
            <Tooltip :content="formList.aaEntity.address" :is-input="true" placement="right">
                <input
                    type="text"
                    @input="handleAAInput(LimitCharacterSize.Default, $event, '经营地址', 'address', changeFormListData)"
                    @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                    v-model="formList.aaEntity.address"
                    class="big input-ellipsis"
                />
            </Tooltip>
            </el-form-item>
            <div class="isRow">
                <el-form-item label="联系人：">
                <Tooltip :content="formList.aaEntity.contact" :is-input="true" placement="right">
                    <input
                        type="text"
                        @input="handleAAInput(LimitCharacterSize.Default, $event, '联系人', 'contact', changeFormListData)"
                        @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                        v-model="formList.aaEntity.contact"
                        class="input-ellipsis"
                    />
                </Tooltip>
                </el-form-item>
                <el-form-item label="手机：">
                    <input
                        type="text"
                        @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机', 'mobilePhone', changeFormListData)"
                        @paste="handleAAPaste(LimitCharacterSize.Phone, $event)"
                        v-model="formList.aaEntity.mobilePhone"
                        class="middle"
                    />
                </el-form-item>
            </div>
            <el-form-item label="备注：">
                <div v-if="noteTextareaShow">
                    <el-input
                    class="note big"
                    v-model="formList.aaEntity.note"
                    @blur="inputTypeBlur('note')"
                    :autosize="{minRows: 1, maxRows: 3.5 }"
                    type="textarea"
                    maxlength="1024"
                    @input="limitInputLength(formList.aaEntity.note,'备注')"
                    @focus="inputTypeFocus()"
                    resize="none"
                    ref="oppositePartyRef"
                    style="width: 698px;"
                />
                </div>                    
                <div v-else class='note-tooltip-width'>
                    <Tooltip :content="formList.aaEntity.note" :isInput="true" placement="bottom" >
                    <input
                        @focus="inputTypeFocus('note')"
                        ref="oppositePartyRef"
                        class="note big input-ellipsis"
                        type="text"
                        v-model="formList.aaEntity.note"
                    />
                </Tooltip>
                </div>
            </el-form-item>
            <el-form-item label="是否启用：" class="status">
                <el-checkbox v-model="formList.status" label="启用" @change="handleStatusChange" />
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">返回</a>
        </div>
    </div>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste, getNextAaNum, textareaBottom, autocompleteSelfAdaption } from "../utils";
import { ValidataCustomerOrVendor } from "../validator";
import { ElConfirm } from "@/util/confirm";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import Tooltip from "@/components/Tooltip/index.vue";
import AreaBox from "./AreaBox.vue";
import { ElNotify } from "@/util/notify";
import type { ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import { ElAutocomplete } from "element-plus";

const oppositePartyRef = ref<InstanceType<typeof ElAutocomplete>>();
const noteTextareaShow=ref(false)
const aaNameTextareaShow=ref(false)
interface IFormList {
    [key: string]: any;
    aaEntity: {
        [key: string]: any;
    };
}

const props = defineProps<{
    aaType: number;
    name: string;
}>();
const formList = reactive<IFormList>({
    aaName: "",
    aaNum: "",
    aaeID: "",
    USCC: "",
    status: true,
    hasEntity: true,
    aaEntity: {
        type: "",
        adLevel0: "",
        adLevel1: "",
        adLevel2: "",
        adLevel3: "",
        address: "",
        contact: "",
        mobilePhone: "",
        taxNumber: "",
        note: "",
    },
});
const changeFormListData = (key: string, val: string) => {
    const keys = Object.keys(formList);
    const aaEntityKeys = Object.keys(formList.aaEntity);
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            formList[key] = val;
            return;
        }
    }
    for (let i = 0; i < aaEntityKeys.length; i++) {
        if (aaEntityKeys[i] === key) {
            formList.aaEntity[key] = val;
            break;
        }
    }
};
const inputTypeBlur = (val:string) => {
    switch (val) {
        case 'note':
            noteTextareaShow.value = false;
            break;
        case 'aaName':
            aaNameTextareaShow.value = false;
            break;
    }
};

const inputTypeFocus = (val?:string) => {
    textareaBottom(formRef)
    switch (val) {
        case 'note':
            noteTextareaShow.value = true;
            break;
        case 'aaName':
            aaNameTextareaShow.value = true;
            break;
    }
    nextTick(()=>{
        if(val){
            getTextareaFocus(val)
        }
    })
};
const getTextareaFocus = (val:string) => {
    switch (val) {
    case 'note':
    case 'aaName':
        oppositePartyRef.value?.focus();
        break;
    }
};
function limitInputLength(val: string,label: string) {
    switch (label) {
        case '备注':
            if (val.length === 1024) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过1024个字哦~` });
            }
            break;
        default:
            if (val.length === 256) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
            }
    }
}
const areaInfo = reactive({
    adLevel1: "",
    adLevel2: "",
    adLevel3: "",
});
const resetAreaInfo = () => {
    arearef.value?.editInputVal("");
    areaInfo.adLevel1 = "";
    areaInfo.adLevel2 = "";
    areaInfo.adLevel3 = "";
};

watch(
    [() => formList.aaEntity.adLevel1, () => formList.aaEntity.adLevel2, () => formList.aaEntity.adLevel3],
    ([adLevel1, adLevel2, adLevel3]) => {
        areaInfo.adLevel1 = adLevel1 + "";
        areaInfo.adLevel2 = adLevel2 + "";
        areaInfo.adLevel3 = adLevel3 + "";
    }
);
const arearef = ref<any>(null);
const formRef = ref<any>(null);
const addressChange = (val: any) => {
    formList.aaEntity.adLevel1 = val.adLevel1;
    formList.aaEntity.adLevel2 = val.adLevel2;
    formList.aaEntity.adLevel3 = val.adLevel3;
};
let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => (EditType = val);
const emit = defineEmits(["formCancel", "formChanged", "formCancelEdit"]);
let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    const aaeID = EditType === "Edit" ? formList.aaeID : 0;
    const aaNum = formList.aaNum;
    const aaName = formList.aaName;
    createCheck(props.aaType, aaeID, aaNum, aaName, Save);
};
const Save = () => {
    const entityParams = {
        type: formList.aaEntity.type,
        adLevel0: formList.aaEntity.adLevel0 || "",
        adLevel1: formList.aaEntity.adLevel1 || "",
        adLevel2: formList.aaEntity.adLevel2 || "",
        adLevel3: formList.aaEntity.adLevel3 || "",
        address: formList.aaEntity.address,
        contact: formList.aaEntity.contact,
        mobilePhone: formList.aaEntity.mobilePhone,
        taxNumber: formList.aaEntity.taxNumber,
        note: formList.aaEntity.note,
    };
    const params = {
        entity: entityParams,
        aaNum: formList.aaNum,
        aaName: formList.aaName,
        uscc: formList.USCC,
        status: formList.status ? 0 : 1,
        hasEntity: formList.hasEntity,
        ifvoucher: true,
    };
    let urlPath = "";
    if (props.aaType === 10001) {
        urlPath = EditType === "Edit" ? "Customer?aaeid=" + formList.aaeID : "Customer";
    } else if (props.aaType === 10002) {
        urlPath = EditType === "Edit" ? "Vendor?aaeid=" + formList.aaeID : "Vendor";
    }
    if (ValidataCustomerOrVendor(entityParams, props.aaType, params.aaNum, params.aaName)) {
        isSaving = true;
        request({
            url: "/api/AssistingAccounting/" + urlPath,
            method: EditType === "New" ? "post" : "put",
            headers: { "Content-Type": "application/json" },
            data: JSON.stringify(params),
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000 || "Failed" === res.data) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    isSaving = false;
                    return;
                }
                ElNotify({ type: "success", message: "保存成功" });
                useAssistingAccountingStore().getAssistingAccounting();
                if (EditType === "New") {
                    getNextAaNum(props.aaType)
                        .then((res: IResponseModel<string>) => {
                            resetForm();
                            formList.aaNum = res.data;
                            emit("formCancelEdit");
                        })
                        .finally(() => {
                            isSaving = false;
                        });
                } else {
                    handleCancel();
                    isSaving = false;
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
                isSaving = false;
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};

// 重置表单数据
const resetForm = () => {
    const initParams: any = {
        aaName: "",
        aaNum: "",
        aaeID: "",
        USCC: "",
        status: true,
        hasEntity: true,
        aaEntity: {
            type: "",
            adLevel0: "",
            adLevel1: "",
            adLevel2: "",
            adLevel3: "",
            address: "",
            contact: "",
            mobilePhone: "",
            taxNumber: "",
            note: "",
        },
    };
    Object.keys(initParams).forEach((key) => {
        if (key in formList) {
            formList[key] = initParams[key];
        } else if (key in formList.aaEntity) {
            formList.aaEntity[key] = initParams[key];
        }
    });
    resetAreaInfo();
};
const editForm = (data: any): void => {
    Object.keys(data).forEach((key) => {
        if (key in formList) {
            formList[key] = data[key];
        } else if (key in formList.aaEntity) {
            formList.aaEntity[key] = data[key];
        }
    });
    EditType === "Edit" &&
        (function () {
            areaInfo.adLevel1 = data.adLevel1 + "";
            areaInfo.adLevel2 = data.adLevel2 + "";
            areaInfo.adLevel3 = data.adLevel3 + "";
        })();
    editAreaData();
};
defineExpose({ resetForm, editForm, changeType });
const editAreaData = () => {
    request({
        url: "/api/City/GetNameByAreaId?areaId=" + formList.aaEntity.adLevel3,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        arearef.value?.editInputVal(res.data);
    });
};

const queryParams = reactive({
    name: "",
    data: [] as ICompanyInfo[],
    isFromDb: false,
});
const handleSelect = (val: any) => {
    formList.aaName = val.value;
    formList.aaEntity.taxNumber = val.creditCode;
};
const querySearch = (queryString: string, cb: any) => {
    if (!queryString) {
        return;
    }
    if (queryString.length > 50) {
      cb([]);
    }else{
        nextTick(()=>{
            autocompleteSelfAdaption()
        })
        getCompanyList(1020, queryString, cb, queryParams);
    }
};
const handleStatusChange = (check: any) => {
    if (!check) {
        ElConfirm("亲，辅助核算项目停用后不能再在凭证中使用哦，是否确认停用？").then((r: boolean) => {
            formList.status = !r;
        });
    }
};
const handleInput = (e: Event) => {
    handleAAInput(LimitCharacterSize.Name, e, props.name + "名称", "aaName", changeFormListData);
};
watch(
    formList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
onMounted(() => {
    oppositePartyRef.value?.inputRef?.input?.addEventListener("input", handleInput);
});
onUnmounted(() => {
    oppositePartyRef.value?.inputRef?.input?.removeEventListener("input", handleInput);
});
</script>
<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
@import "@/style/Functions.less";
:deep(.el-textarea__inner){
    position: absolute;
    top: -32px;
    z-index: 1000;
}
.add-content {
    :deep(.el-form) {
        .el-form-item__content {
            position: relative;
        }
        .isRow {
            &:first-child {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
}
.formRef {
    input {
        .detail-original-input(188px, 32px);
        &.middle {
            .detail-original-input(288px, 32px);
        }
        &.big {
            .detail-original-input(698px, 32px);
        }
    }
    .isRow {
        display: flex;
        align-items: center;
    }
    :deep(.el-autocomplete) {
        &.middle {
            width: 288px;
        }
    }
}
.button {
    margin-left: 10px;
    &:first-child {
        margin-left: 0;
    }
}
:deep(.el-input__inner){
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.search-box {
    position: absolute;
    left: 0;
    top: 31px;
    width: 288px;
    height: 208px;
    overflow-y: auto;
    z-index: 100;
    box-sizing: border-box;
    border: 1px solid var(--border-color);
    background-color: var(--white);
    font-family: 微软雅黑 !important;
    color: Gray;
    font-size: 12px !important;
    & > div {
        box-sizing: border-box;
        padding: 4px 6px 4px 8px;
        cursor: pointer;
        color: var(--font-color);
        font-size: var(--h5);
        line-height: 18px;
        text-align: left;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        &:hover {
            background-color: var(--main-color);
            color: var(--white);
        }
    }
}
:deep(.el-input__inner){
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
}
input.note {
    .detail-original-input(698px, 32px);
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 

}
</style>
