<template>
    <el-tooltip
        effect="light"
        placement="bottom"
        :offset="2"
        trigger="click"
        :visible="visible"
        :popper-class="['table-header-filter', { hasSelect: isSelect }]"
    >
        <template #content>
            <template v-if="useCommonSelect">
                <div v-if="filterOrder === 'date'">
                    <div
                        class="head-date-picker-container"
                        style="position: relative"
                        @mouseenter="handleInputEnter"
                        @mouseleave="clearFlag = false"
                    >
                        <DatePicker
                            :disabledDateStart="disabledStartDate"
                            :disabledDateEnd="disabledEndDate"
                            v-model:start-pid="dateInfo.s"
                            v-model:end-pid="dateInfo.e"
                        ></DatePicker>
                    </div>
                    <div class="buttons mt-10" :class="{ erp: isErp }">
                        <a class="button solid-button" @click="handleDateSearch">确定</a>
                        <a class="button ml-10" @click="handleCancel">取消</a>
                    </div>
                </div>
                <div v-else-if="filterOrder === 'code' || filterOrder === 'number'">
                    <div style="position: relative" @mouseenter="handleInputEnter" @mouseleave="clearFlag = false">
                        <el-input
                            ref="codeInputRefS"
                            v-model="codeInfo.s"
                            placeholder="请输入"
                            style="width: 132px"
                            @input="handleCodeInput('s')"
                        >
                            <template #suffix>
                                <el-icon
                                    v-show="codeInfo.s_clear"
                                    style="font-size: 14px; color: #999; cursor: pointer"
                                    @click.stop.prevent="handleCodeSClear"
                                >
                                    <CircleClose />
                                </el-icon>
                            </template>
                        </el-input>
                        <span class="ml-10 mr-10">至</span>
                        <el-input
                            ref="codeInputRefE"
                            v-model="codeInfo.e"
                            placeholder="请输入"
                            style="width: 132px"
                            @input="handleCodeInput('e')"
                        >
                            <template #suffix>
                                <el-icon
                                    v-show="codeInfo.e_clear"
                                    style="font-size: 14px; color: #999; cursor: pointer"
                                    @click.stop.prevent="handleCodeEClear"
                                >
                                    <CircleClose />
                                </el-icon>
                            </template>
                        </el-input>
                    </div>
                    <div class="buttons mt-10" :class="{ erp: isErp }">
                        <a class="button solid-button" @click="handleCodeSearch">确定</a>
                        <a class="button ml-10" @click="handleCancel">取消</a>
                    </div>
                </div>
                <div v-else-if="filterOrder === 'text'">
                    <div style="position: relative" @mouseenter="handleInputEnter" @mouseleave="clearFlag = false">
                        <el-input
                            ref="searchValRef"
                            v-model="searchVal"
                            placeholder="请输入搜索"
                            style="width: 216px"
                            @input="handleInputEnter"
                        >
                            <template #suffix>
                                <el-icon
                                    v-show="clearFlag"
                                    style="font-size: 14px; color: #999; cursor: pointer"
                                    @click.stop.prevent="handleClear"
                                >
                                    <CircleClose />
                                </el-icon>
                            </template>
                        </el-input>
                    </div>
                    <div class="buttons mt-10" :class="{ erp: isErp }">
                        <a class="button solid-button" @click="handleSearch">确定</a>
                        <a class="button ml-10" @click="handleCancel">取消</a>
                    </div>
                </div>
                <template v-else-if="filterOrder === 'multiSelect'">
                    <VirtualSelectCheckbox
                        width="260px"
                        class="item header-select"
                        :options="currentOptions"
                        :use-el-icon="true"
                        :hasPoppver="true"
                        :isSearchStatus="isSearchStatus"
                        :customProps="assignProps"
                        :class="{ erp: isErp }"
                        :default-not-select-all="defaultNotSelectAll"
                        v-model:selectedList="currentList"
                        @multi-select-change="multiSelectChange"
                        @cancel-select="cancalSelect"
                    />
                </template>
            </template>
            <template v-else>
                <div v-if="!isSelect">
                    <div style="position: relative" @mouseenter="handleInputEnter" @mouseleave="clearFlag = false">
                        <el-input
                            ref="searchValRef"
                            v-model="searchVal"
                            placeholder="请输入搜索"
                            style="width: 216px"
                            @input="handleInputEnter"
                        >
                            <template #suffix>
                                <el-icon v-show="clearFlag" style="font-size: 14px; color: #999" @click.stop.prevent="handleClear">
                                    <CircleClose />
                                </el-icon>
                            </template>
                        </el-input>
                    </div>
                    <div class="buttons mt-10">
                        <a class="button solid-button" @click="handleSearch">确定</a>
                        <a class="button ml-10" @click="handleCancel">取消</a>
                    </div>
                </div>
                <div v-else>
                    <template v-if="multiSelect">
                        <VirtualSelectCheckbox
                            :options="currentOptions"
                            :use-el-icon="true"
                            :hasPoppver="true"
                            :isSearchStatus="isSearchStatus"
                            width="260px"
                            v-model:selectedList="currentList"
                            class="item"
                            @multi-select-change="multiSelectChange"
                            @cancel-select="cancalSelect"
                        />
                    </template>
                    <template v-else>
                        <div :class="[{ 'oppsite-style': isOppsite }]">
                            <div class="sing-select">
                                <el-select
                                    v-if="!isVirtual"
                                    :teleported="false"
                                    placeholder=" "
                                    v-model="singSelect"
                                    :suffix-icon="CaretBottom"
                                    :filterable="true"
                                    :filter-method="filterMethod"
                                >
                                    <el-option
                                        v-for="item in singList"
                                        :key="item.value"
                                        :value="item.value"
                                        :label="item.name"
                                    ></el-option>
                                </el-select>
                                <SelectV2
                                    v-else
                                    v-model="virtualValue"
                                    class="item"
                                    placeholder="请选择或输入"
                                    :teleported="false"
                                    :fit-input-width="true"
                                    :filterable="true"
                                    :clearable="true"
                                    :allow-create="allowCreate"
                                    :toolTipOptions="{ dynamicWidth: true }"
                                    :props="selectV2Props"
                                    :options="virtualList"
                                    :remote="SSinglist.length > 0"
                                    :filter-method="filterMethod"
                                    @visible-change="handleVisibleChange"
                                    :isSuffixIcon="true"
                                ></SelectV2>
                            </div>
                        </div>
                        <div :class="['buttons', { 'mt-10': !isOppsite }]">
                            <a class="button solid-button" @click="headerSelect">确定</a>
                            <a v-if="!isOppsite" class="button ml-10" @click="handleCancel">取消</a>
                        </div>
                    </template>
                </div>
            </template>
        </template>
        <div ref="tableHeaderFilterRef" :class="['filter-icon', { active: isFilter || visible }]" @click="clickFilter">
            <svg diplay="block" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <title></title>
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g class="table-filter-fill" transform="translate(-704.000000, -294.000000)" fill-rule="nonzero">
                        <g transform="translate(177.750731, 279.605861)">
                            <g transform="translate(0.500000, 0.000000)">
                                <g transform="translate(526.540615, 15.000000)">
                                    <g transform="translate(1.000000, 1.000000)">
                                        <rect opacity="0" x="0" y="0.25" width="12" height="12"></rect>
                                        <path
                                            d="M11.3861053,0.871743893 L7.68491426,5.07876053 C7.44371041,5.35274936 7.31139956,5.705188 7.31139956,6.06933556 L7.31139956,11.8746204 C7.31139956,12.2141792 6.89456184,12.3792751 6.66272514,12.129875 L4.78929718,10.1229653 C4.72489809,10.0538826 4.68860043,9.962553 4.68860044,9.86771072 L4.68860044,6.06933556 C4.68860044,5.705188 4.55628959,5.35274937 4.31508574,5.07993142 L0.613894652,0.871743893 C0.400792219,0.630540038 0.57291341,0.249999987 0.894908836,0.249999987 L11.1050912,0.249999987 C11.4270866,0.249999987 11.5992078,0.630540038 11.3861053,0.871743893 Z"
                                        ></path>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </svg>
        </div>
    </el-tooltip>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, type PropType, reactive } from "vue";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import { CaretBottom } from "@element-plus/icons-vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
import type { FilterOrder } from "../Table/IColumnProps";
import DatePicker from "@/components/DatePicker/index.vue";
import { ElNotify } from "@/util/notify";
import { getGlobalLodash } from "@/util/lodash";

const isErp = ref(window.isErp);

interface IList {
    value: number;
    name: string;
}
interface VIList {
    value: string;
    text: string;
}
interface IProps {
    id: string;
    name: string;
}
const selectV2Props = {
    value: "value",
    label: "text",
};

const props = defineProps({
    prop: {
        //搜索查询选择的字段名
        type: String,
        default: "",
    },
    isSelect: {
        //纯搜索，有选择是为true
        type: Boolean,
        default: false,
    },
    selectedList: {
        //多选的值
        type: Array,
        default: () => [],
    },
    option: {
        //多选的项
        type: Array,
        default: () => [],
    },
    isFilter: {
        //是否处于筛选状态
        type: Boolean,
        default: false,
    },
    hasSearchVal: {
        //左边搜索框的值
        type: String,
        default: "",
    },
    hasSelectList: {
        //表头上次多选框的值
        type: Array,
        default: () => [],
    },
    hasSSingVal: {
        //左边单选框的值
        type: Number,
        default: 0,
    },
    multiSelect: {
        //多选还是单选
        type: Boolean,
        default: true,
    },
    SSinglist: {
        //单选的项
        type: Array,
        default: () => [],
    },
    isSearchStatus: {
        type: Boolean,
        default: true,
    },
    isVirtual: {
        //虚拟单选
        type: Boolean,
        default: false,
    },
    virtualSelect: {
        type: String,
        default: "",
    },
    isOppsite: {
        //日记账往来单位
        type: Boolean,
        default: false,
    },
    filterOrder: {
        type: String as PropType<FilterOrder>,
        default: "",
    },
    useCommonSelect: {
        type: Boolean,
        default: false,
    },
    customProp: {
        type: Object as PropType<IProps>,
        default: () => ({}),
    },
    clearLastData: {
        type: Boolean,
        default: true,
    },
    disabledStartDate: {
        type: Function,
        default: undefined,
    },
    disabledEndDate: {
        type: Function,
        default: undefined,
    },
    defaultNotSelectAll: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits<{
    (event: "update:selectedList", args: Array<number | string>): void;
    (e: "filterSearch", prop: string, value: any, filterOrder: FilterOrder): void;
    (e: "update:isFilter", value: boolean): void;
}>();

const defaultProps = { id: "id", name: "name" };
const assignProps = computed(() => Object.assign({}, defaultProps, props.customProp));
const searchVal = ref("");
const visible = ref(false);
const isFilter = computed({
    get() {
        return props.isFilter;
    },
    set(value: boolean) {
        emit("update:isFilter", value);
    },
});

const singSelect = ref(0);
const singList = ref<IList[]>([]);
const virtualValue = ref("");
const virtualList = ref<VIList[]>([]);

const currentList = ref([]);
function clickFilter() {
    props.clearLastData && (searchVal.value = props.hasSearchVal);
    if (props.hasSelectList.length) {
        currentList.value = JSON.parse(JSON.stringify(props.hasSelectList));
    } else {
        currentList.value = JSON.parse(JSON.stringify(props.selectedList));
    }
    if (props.isVirtual) {
        virtualValue.value = props.virtualSelect;
        virtualList.value = JSON.parse(JSON.stringify(props.SSinglist));
    } else {
        singSelect.value = Number(props.hasSSingVal);
        singList.value = JSON.parse(JSON.stringify(props.SSinglist));
    }
    visible.value = !visible.value;
}
function handleCancel() {
    searchVal.value = "";
    visible.value = false;
}
function handleSearch() {
    visible.value = false;
    searchVal.value = searchVal.value.trim();
    emit("filterSearch", props.prop, searchVal.value, props.filterOrder);
}

function closeVisible() {
    visible.value = false;
}

const currentOptions = ref<Option[]>([]);
watch(
    () => [props.selectedList, props.option],
    () => {
        currentOptions.value = [];
        if (!props.selectedList.length) {
            currentOptions.value = JSON.parse(JSON.stringify(props.option));
        } else {
            props.option.forEach((item: any) => {
                const exists = props.selectedList.some((v) => item.id === v);
                if (exists) {
                    currentOptions.value.push({ ...item });
                }
            });
        }
    },
    {
        immediate: true,
        deep: true,
    }
);

function multiSelectChange(data: Array<number | string>) {
    visible.value = false;
    if (data.length || props.defaultNotSelectAll) {
        emit("filterSearch", props.prop, data, props.filterOrder);
    }
}
function cancalSelect() {
    visible.value = false;
}
function headerSelect() {
    visible.value = false;
    if (props.isVirtual) {
        const item = virtualList.value.filter((item) => item.value === virtualValue.value);
        if (item && item[0]) {
            virtualValue.value = item[0].text;
        }
        emit("filterSearch", props.prop, virtualValue.value, props.filterOrder);
    } else {
        emit("filterSearch", props.prop, singSelect.value, props.filterOrder);
    }
}
const searchValRef = ref();
const clearFlag = ref(false);
function handleInputEnter() {
    clearFlag.value = !!searchVal.value.trim();
}
function handleClear() {
    searchVal.value = "";
    clearFlag.value = false;
    searchValRef.value?.focus();
}

const tableHeaderFilterRef = ref();
const setBoxNotShow = (e: MouseEvent) => {
    let outer = document.querySelector(".table-header-filter");
    let el = e.target as HTMLElement;
    let needClose = true;
    if (props.filterOrder === "date") {
        let dom = el;
        while (dom) {
            if (dom.classList.contains("head-date-picker-container")) {
                needClose = false;
                break;
            }
            if (dom === document.body) {
                break;
            }
            dom = dom.parentElement as HTMLElement;
        }
    }
    if (needClose && !tableHeaderFilterRef.value?.contains(el) && !outer?.contains(el)) {
        closeVisible();
    }
};

const allowCreate = ref(true);
function filterMethod(val: string) {
    if (!props.isVirtual) {
        singList.value = commonFilterMethod(val, props.SSinglist, "name");
    } else {
        virtualList.value = commonFilterMethod(val, props.SSinglist, "text");
    }
}
function handleVisibleChange(visible: boolean) {
    if (visible) {
        virtualList.value = JSON.parse(JSON.stringify(props.SSinglist));
    }
}

const dateInfo = reactive({
    s: "",
    e: "",
});
function handleDateSearch() {
    if (dateInfo.s > dateInfo.e) {
        ElNotify({ type: "warning", message: "亲，开始时间不能大于结束时间哦~" });
        return;
    }
    visible.value = false;
    emit("filterSearch", props.prop, dateInfo, props.filterOrder);
}
const codeInfo = reactive({
    s: "",
    e: "",
    s_clear: false,
    e_clear: false,
});
const codeInputRefS = ref();
const codeInputRefE = ref();
function handleCodeInput(type: "s" | "e") {
    if (type === "s") {
        codeInfo.s_clear = !!codeInfo.s.trim();
    } else {
        codeInfo.e_clear = !!codeInfo.e.trim();
    }
}
function handleCodeSClear() {
    codeInfo.s = "";
    codeInfo.s_clear = false;
    codeInputRefS.value?.focus();
}
function handleCodeEClear() {
    codeInfo.e = "";
    codeInfo.e_clear = false;
    codeInputRefE.value?.focus();
}
function handleCodeSearch() {
    emit("filterSearch", props.prop, codeInfo, props.filterOrder);
    visible.value = false;
}
const eventType = props.filterOrder === "date" ? "mousedown" : "click";
onMounted(() => {
    document.addEventListener(eventType, setBoxNotShow);
    let myElement = document.querySelector(".search-info-container");
    myElement?.addEventListener("mouseenter", closeVisible);
});
onUnmounted(() => {
    document.removeEventListener(eventType, setBoxNotShow);
    let myElement = document.querySelector(".search-info-container");
    myElement?.removeEventListener("mouseenter", closeVisible);
});
function resetFilter() {
    if (!props.useCommonSelect) return;
    searchVal.value = "";
    dateInfo.s = "";
    dateInfo.e = "";
    codeInfo.s = "";
    codeInfo.e = "";
    codeInfo.s_clear = false;
    codeInfo.e_clear = false;
    clearFlag.value = false;
    currentList.value = [];
    visible.value = false;
}
const { intersection } = getGlobalLodash();
function setCustomFilter(filterOrder: FilterOrder, value: any) {
    if (!props.useCommonSelect) return;
    if (filterOrder === "date") {
        if (value.s) dateInfo.s = value.s;
        if (value.e) dateInfo.e = value.e;
    } else if (filterOrder === "code" || filterOrder === "number") {
        if (value.s) codeInfo.s = value.s;
        if (value.e) codeInfo.e = value.e;
    } else if (filterOrder === "text") {
        searchVal.value = value;
        clearFlag.value = !!value;
    } else if (filterOrder === "multiSelect") {
        currentList.value = intersection(value, currentOptions.value.map((item: any) => item[assignProps.value.id]));
    }
}
defineExpose({
    closeVisible,
    resetFilter,
    setCustomFilter,
});
</script>
<style lang="less" scoped>
.active {
    .table-filter-fill {
        fill: var(--main-color);
    }
}
.sing-select {
    margin: 0 10px;
}
.pointerClass {
    cursor: pointer;
}
.oppsite-style {
    padding-bottom: 5px;
    display: flex;
    align-items: center;
    .sing-select {
        margin-right: 5px;
        width: 180px;
    }
    .buttons {
        padding: 0 10px 0 0;
        .solid-button {
            width: 60px;
        }
    }
}
.header-select.erp {
    :deep(.buttons) {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        justify-content: flex-start !important;
        .solid-button {
            margin-left: 10px;
            margin-right: 10px;
        }
    }
}
.buttons.erp {
    display: flex;
    flex-direction: row-reverse;
    .solid-button {
        margin-left: 10px;
    }
}
</style>
<style lang="less">
.hasSelect.el-popper {
    padding: 10px 0 5px;
    .select-checkbox {
        margin: 0 11px;
        .button {
            margin-right: 10px;
            &:last-child {
                margin-right: 0;
            }
        }
    }
}
</style>
