<template>
    <div class="slot-content">
        <div class="slot-mini-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #main>
                    <div class="slot-mini-content main-content">
                        <div
                            :class="['main-top main-tool-bar space-between', { 'split-line': !isErp }]"
                            style="width: 1100px; margin: 0 auto"
                        >
                            <div class="main-tool-left">
                                <div class="cash-flow-select">
                                    <el-select
                                        id="calmethod"
                                        v-model="searchtype"
                                        :fit-input-width="true"
                                        class="jqtransform"
                                        :teleported="false"
                                    >
                                        <el-option label="本期数据" value="1" class="option"> </el-option>
                                        <el-option label="本年累计数据" value="2" class="option"> </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="main-tool-right">
                                <a class="button ml-10" @click="detailPrintHandle" v-permission="['cashflowstatement-canexport']">导出</a>
                                <a class="button ml-10" @click="backHandle">返回</a>
                            </div>
                        </div>
                        <div class="main-center" style="width: 1100px; margin: 0 auto">
                            <div class="left">
                                <div class="main-title">科目：{{ tableTitle }}</div>
                                <Table
                                    :pageIsShow="true"
                                    :columns="columns"
                                    :data="state.tableShow"
                                    v-loading="loading"
                                    element-loading-text="正在加载数据..."
                                    :emptyText="emptyText"
                                    style="height: calc(100% - 37px)"
                                    :page-sizes="[20, 30, 40, 50]"
                                    :page-size="paginationData.pageSize"
                                    :total="paginationData.total"
                                    :scrollbar-always-on="true"
                                    :current-page="paginationData.currentPage"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    @refresh="handleRerefresh"
                                    :show-overflow-tooltip="true"
                                    :tooltipOptions="{ effect: 'light', placement: 'bottom' }"
                                    :scrollbar-show="true"
                                    :tableName="setModule"
                                >
                                    <template #vgName>
                                        <el-table-column 
                                            label="凭证字号" 
                                            align="left" 
                                            min-width="100px" 
                                            headerAlign="left"
                                        >
                                            <template #default="scope">
                                                <a class="link" @click="ShowDetail(scope.row)" v-show="scope.row.v_name !== ''">
                                                    {{ scope.row.v_name.replace(/<[^<>]+>/g, "") }}
                                                </a>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </Table>
                            </div>
                            <div class="edit-adjust-detail-right ml-10">
                                <div class="edit-adjust-detail-right-title">快速切换</div>
                                <div class="select-content">
                                    <el-input
                                        autocomplete="off"
                                        autocapitalize="false"
                                        type="text"
                                        @click="searchHandle"
                                        @blur="blurHandle"
                                        @input="searchInputChange"
                                        v-model="searchValue"
                                        @keydown.enter="handleKeyPress"
                                        class="search-input"
                                    >
                                        <template #suffix>
                                            <div class="search-box"></div>
                                        </template>
                                    </el-input>
                                    <div class="tip" v-show="actived">
                                        <div
                                            class="tip-item"
                                            v-for="item in state.tableList"
                                            :key="item.aaeid"
                                            @click="selectedhandle(item)"
                                        >
                                            {{ item.aanum + " " + item.aaname }}
                                        </div>
                                        <div class="tip-item" style="text-align: center" v-show="state.tableList.length === 0">
                                            没有匹配的选项
                                        </div>
                                    </div>
                                </div>
                                <ul id="subject_tree">
                                    <el-scrollbar :always="true">
                                        <li
                                            @click="changeTableShow(item)"
                                            :class="aa_code === String(item.aaeid) ? 'actived' : ''"
                                            v-for="item in state.tableData"
                                            :key="item.aaeid"
                                        >
                                            <!-- {{ item.text }} -->
                                            <span class="pr-5">{{ item.aanum }}</span>
                                            <span>{{ item.aaname }}</span>
                                        </li>
                                    </el-scrollbar>
                                </ul>
                            </div>
                        </div>
                    </div>
                </template>
                <template #voucher>
                    <div class="slot-mini-content">
                        <BooksVoucher
                            ref="booksVoucherRef"
                            @voucher-cancel="currentSlot = 'main'"
                            @load-data="handleVoucherChange"
                        ></BooksVoucher>
                    </div>
                </template>
            </ContentSlider>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { usePagination } from "@/hooks/usePagination";
import type { ILoadVoucherParams } from "@/components/CheckVoucher/types";
import Table from "@/components/Table/index.vue";
import { formatMoney } from "@/util/format";
import ContentSlider from "@/components/ContentSlider/index.vue";
import BooksVoucher from "@/views/AccountBooks/components/BooksVoucher.vue";
import type { ICashFlowList, ICashFlowtDetails, ICashFlowState } from "../types";
import { useAccountSetStore } from "@/store/modules/accountset";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const accountStandard = useAccountSetStore().accountSet!.accountingStandard;

const setModule = "CashFlowDetail";
const props = defineProps<{ pid: number }>();
const state = reactive<ICashFlowState>({
    tableData: [],
    tableList: [],
    tableShow: [],
    total: 0,
});
const slots = ["main", "voucher"];
let currentSlot = ref<string>("main");
const pid = computed(() => props.pid);

const columns: IColumnProps[] = [
    { 
        label: "日期", 
        prop: "v_date", 
        minWidth: 120, 
        align: "left", 
        headerAlign: "left", 
        width: getColumnWidth(setModule, "v_date") 
    },
    { slot: "vgName" },
    { 
        label: "科目", 
        prop: "asub_name", 
        minWidth: 146, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "asub_name")  
    },
    { 
        label: "摘要", 
        prop: "description", 
        minWidth: 176, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "description") 
    },
    {
        label: "借方",
        prop: "debit",
        minWidth: 87,
        align: "right",
        headerAlign: "right",
        formatter: (row, col, value) => {
            return formatMoney(value);
        },
        width: getColumnWidth(setModule, "debit") 
    },
    {
        label: "贷方",
        prop: "credit",
        minWidth: 87,
        align: "right",
        headerAlign: "right",
        formatter: (row, col, value) => {
            return formatMoney(value);
        },
        resizable: false,
    },
];
const isErp = ref(window.isErp);
const aa_code = ref<string>("");
const actived = ref<boolean>(false);
const searchValue = ref<string>("");
const searchtype = ref<string>("1");
const emit = defineEmits(["handle-back"]);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
let tableTitle = ref<string>("");

const changeTableShow = (item: ICashFlowList) => {
    aa_code.value = String(item.aaeid);
    tableTitle.value = item.aanum + " " + item.aaname;
};
let initTableTitle = "";
const getTableList = () => {
    request({
        url: `/api/AssistingAccounting/CashFlowList?showAll=${false}`,
        method: "get",
    })
        .then((res: IResponseModel<ICashFlowList[]>) => {
            state.tableData = [];
            if (res.state === 1000 && res.data.length) {
                accountStandard === 1 && res.data.pop();
                state.tableData = res.data.filter((v: ICashFlowList) => v.aaeid !== 31);
                state.tableList = state.tableData;
                aa_code.value = String(state.tableData[0].aaeid) || "";
                tableTitle.value =
                    Number(state.tableList[0].aanum) > 0
                        ? state.tableList[0].aanum + " " + state.tableList[0].aaname
                        : state.tableList[0].aaname;
                initTableTitle =
                    Number(state.tableList[0].aanum) > 0
                        ? state.tableList[0].aanum + " " + state.tableList[0].aaname
                        : state.tableList[0].aaname;
                getTableShow();
            }
        })
        .catch((error) => {
            console.log(error);
        });
};
const booksVoucherRef = ref<InstanceType<typeof BooksVoucher>>();

const ShowDetail = (row: any) => {
    booksVoucherRef.value?.initVoucherData(row.pid, row.vid);
    currentSlot.value = "voucher";
};
const loading = ref(false);
const emptyText = ref("");
const getTableShow = () => {
    const searchData = {
        aaeId: aa_code.value,
        pId: pid.value,
        valueType: searchtype.value === "1" ? "amount" : "totalAmount",
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    loading.value = true;
    request({
        url: `/api/CashFlowStatement/GetPagingCashFlowStatementAssistDetails`,
        params: searchData,
        method: "post",
    })
        .then((res: IResponseModel<ICashFlowtDetails>) => {
            loading.value = false;
            state.tableShow = res.data.rows;
            if (!state.tableShow.length) {
                emptyText.value = "暂无数据";
            } else {
                emptyText.value = "";
            }
            paginationData.total = res.data.total;
        })
        .catch((e) => {
            loading.value = false;
            state.tableShow = [];
            emptyText.value = "暂无数据";
            console.log(e);
        });
};

const searchHandle = () => (actived.value = true);

const blurHandle = () => setTimeout(() => (actived.value = false), 300);
const searchInputChange = (val: any) => {
    state.tableList = state.tableData.filter(
        (item) =>
            (item.aaname as string).includes(searchValue.value) ||
            (item.aanum as string).includes(searchValue.value) ||
            ((item.aanum + " " + item.aaname) as string).includes(searchValue.value)
    );
};
const handleKeyPress = () => {
    searchValue.value = state.tableList[0].aanum + " " + state.tableList[0].aaname;
    aa_code.value = state.tableList[0].aaeid.toString();
    actived.value = false;
    tableTitle.value = state.tableList[0].aanum + " " + state.tableList[0].aaname;
};
const selectedhandle = (val: any) => {
    searchValue.value = val.aanum + " " + val.aaname;
    aa_code.value = val.aaeid.toString();
    actived.value = false;
    tableTitle.value = val.aanum + " " + val.aaname;
};

const backHandle = () => {
    aa_code.value = String(state.tableData[0].aaeid) || "";
    searchtype.value = "1";
    searchValue.value = "";
    tableTitle.value = initTableTitle;
    emit("handle-back");
};
const handleVoucherChange = () => {
    getTableShow()
};
// 报表明细打印
const detailPrintHandle = () => {
    const params = {
        pId: pid.value,
        //amount：本期数据，totalAmount：本年累计数据
        valueType: searchtype.value === "1" ? "amount" : "totalAmount",
    };
    globalExport(`/api/CashFlowStatement/ExportCashFlowStatementAssistDetails?${getUrlSearchParams(params)}`);
};

watch(
    aa_code,
    (aa_code) => {
        if (aa_code !== "") getTableShow();
    },
    { immediate: true }
);
watch(searchtype, () => getTableShow());
watch(
    pid,
    (v) => {
        getTableList();
    },
    { immediate: true }
);
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableShow();
});
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/Statements/CashFlowStatement.less";



.content {
    .slot-mini-content {
        width: 100%;
    }
}
.main-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .cash-flow-select {
        .detail-el-select(125px,30px);
    }

    .main-tool-right {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .jqtransform {
        height: 30px;

        :deep(.el-input__wrapper) {
            padding: 0 20px 0 7px;
            position: relative;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 28px;

            .el-input__suffix {
                position: absolute;
                right: 10px;
                top: 0px;
            }
        }
    }
}

.main-center {
    display: flex;
    justify-content: space-between;
    overflow-y: auto;
    .main-title {
        text-align: left;
        padding-left: 10px;
        border: 1px solid var(--border-color);
        border-bottom: none;
        color: var(--weaker-font-color);
        font-size: var(--h5);
        line-height: 36px;
    }
    .left {
        width: 77%;
    }
    .table {
        width: 100%;
        :deep(.el-table--fit.el-table--default.el-table) {
            height: calc(100% - 31px);
        }
    }
    .edit-adjust-detail-right {
        // width: 224px;
        width: 22%;
        border: 1px solid var(--border-color);
        text-align: center;

        .edit-adjust-detail-right-title {
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 36px;
            font-weight: bold;
            padding-left: 10px;
            text-align: left;
        }

        ul {
            padding: 0px;
            margin: 0px;
            list-style: none;
            text-align: left;
            height: calc(100% - 78px);
            overflow: auto;

            li {
                white-space: nowrap;
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 21px;
                display: inline-block;
                box-sizing: border-box;
                padding-left: 42px;
                width: auto;
                min-width: 100%;
                cursor: pointer;
                background: url("@/assets/Icons/file.png") no-repeat 21px;

                &.actived {
                    background-color: var(--table-title-color);
                }
                &:hover {
                    background-color: var(--table-hover-color);
                    // background-color: var(--table-hover-color);
                }
            }
        }

        .select-content {
            text-align: center;
            position: relative;
            margin-bottom: 10px;
            box-sizing: border-box;
            padding: 0 10px 0 10px;

            input {
                height: 30px;
                // width: 166px;
                width: 98%;
                border: 1px solid var(--main-color);
                border-radius: 4px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                outline: none;
            }
            .search-input {
                height: 32px;
                // width: 166px;
                width: 100%;
                border: 1px solid var(--main-color);
                border-radius: 4px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                outline: none;
                box-shadow: none;
                :deep(.el-input__wrapper) {
                    justify-content: space-between;
                    box-shadow: none;
                    .el-input__inner {
                        width: calc(100% - 20px);
                    }
                    .el-input__suffix {
                        width: 18px;
                    }
                    &.is-focus {
                        box-shadow: 0 0 0 0.5px var(--el-input-focus-border-color) inset;
                    }
                }
            }
            .search-box {
                position: absolute;
                right: 8px;
                top: 7px;
                width: 18px;
                height: 18px;
                background: url("@/assets/Icons/search-erp.png") no-repeat center;
                background-size: 100% 100%;
            }

            .tip {
                width: 92%;
                height: 178px;
                border: 1px solid var(--border-color);
                background-color: var(--white);
                overflow: auto;
                position: absolute;
                left: 50%;
                top: 32px;
                transform: translateX(-50%);
                display: inline-block;
                z-index: 1;
                box-shadow: 0 0 12px rgba(0, 0, 0, .12);
                &::-webkit-scrollbar-thumb {
                    background-color: #c3c3c3;
                    border-radius: 8px;
                }
                &::-webkit-scrollbar {
                    background-color: white;
                    width: 6px;
                }
                &::-webkit-scrollbar-track {
                    background-color: white;
                }
                &::-webkit-scrollbar-corner {
                    background-color: white;
                }
                &::-webkit-scrollbar-track-piece {
                    background-color: white;
                    width: 6px
                }

                .tip-item {
                    color: var(--font-color);
                    font-size: var(--h4);
                    line-height: 16px;
                    padding: 6px 6px 6px 8px;
                    margin: 0;
                    width: auto;
                    display: block;
                    text-align: left;
                    cursor: pointer;

                    &:hover {
                        background-color: var(--main-color);
                        color: var(--white);
                    }
                }
            }
        }
    }
}
#subject_tree {
    .el-scrollbar {
        padding-right: 10px;
        padding-bottom: 10px;
        box-sizing: border-box;
    }
}
// 兼容业财样式
body[erp] {
    .edit-adjust-detail-right ul li {
        background: url(@/assets/icons/file-erp.png) no-repeat 21px;
        background-size: 16px 18px;
    }
}
</style>
