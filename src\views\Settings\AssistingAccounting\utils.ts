import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { useAssistingAccountingStoreHook } from "@/store/modules/assistingAccouting";
import { debounce } from "lodash";

export const clearHanele = (length: number, aaType: number, successCallBack: Function) => {
    if (length === 0) {
        ElNotify({ type: "warning", message: "亲，没有数据，您不需要清空哦" });
        return;
    }
    ElConfirm("亲，您确认要清空所有的数据吗?").then((r: boolean) => {
        if (r) {
            request({
                url: "/api/AssistingAccounting/CheckIfUsed?aaeId=&aaType=" + aaType,
                method: "post",
            }).then((res: IResponseModel<string>) => {
                if (res.state == 1000) {
                    if (res.data == "N") {
                        handleClear(aaType, successCallBack);
                    } else if (res.data == "ERPY") {
                        ElNotify({ type: "warning", message: "亲，该项目已被引用，不允许删除哦" });
                    } else {
                        ElNotify({
                            type: "warning",
                            message: "亲，该类别下存在已生成过凭证或关联期初、资产、资金、费用单据、工资模块的辅助核算项目，不能清空哦！",
                        });
                    }
                }
            });
        }
    });
};
const handleClear = (aaType: number, successCallBack: Function) => {
    const basePath = getUrlPath(aaType) + "Clear";
    const urlPath = aaType > 10007 ? basePath + "?aaType=" + aaType : basePath;
    const requestParams: any = {
        url: "/api/AssistingAccounting/" + urlPath,
        method: "delete",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    };
    if (aaType > 10007) requestParams.data = { aaType };
    request({
        url: "/api/AssistingAccounting/" + urlPath,
        method: "delete",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { aaType },
    })
        .then((res: IResponseModel<string>) => {
            if (res.state == 1000) {
                if (res.data == "Success") {
                    ElNotify({ type: "success", message: "清空成功" });
                    useAssistingAccountingStoreHook().getAssistingAccounting(aaType);
                    if (aaType === 10004) {
                        useAssistingAccountingStoreHook().getDepartment();
                    }
                    successCallBack();
                } else {
                    ElNotify({ type: "warning", message: res.msg });
                }
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "清空失败" });
        });
};
export const deleteHandle = (aaType: number, aaeId: number, successCallBack: Function) => {
    ElConfirm("亲，确认要删除吗?").then((r: boolean) => {
        if (r) {
            const checkParams = { aaType, aaeId };
            request({
                url: "/api/AssistingAccounting/CheckIfUsed?" + getUrlSearchParams(checkParams),
                method: "post",
            })
                .then((res: IResponseModel<string>) => {
                    if (res.state == 1000) {
                        if (res.data == "N") {
                            handleDelete(aaType, aaeId, successCallBack);
                        } else if (res.data == "ERPY") {
                            ElNotify({ type: "warning", message: "亲，该项目已被引用，不允许删除哦" });
                        } else {
                            ElNotify({
                                type: "warning",
                                message: "亲，该类别下存在已生成过凭证或关联期初、资产、资金、费用单据、工资模块的辅助核算项目，不能删除哦！",
                            });
                        }
                    }
                })
                .finally(() => {
                    window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
                });
        }
    });
};

const handleDelete = (aaType: number, aaeId: number, successCallBack: Function) => {
    const basePath = getUrlPath(aaType) + "Batch";
    const urlPath = aaType > 10007 ? basePath + "?aaType=" + aaType : basePath;
    const aaeIdList = [];
    aaeIdList.push(aaeId);
    request({
        url: "/api/AssistingAccounting/" + urlPath,
        method: "delete",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(aaeIdList),
    })
        .then((res: IResponseModel<string>) => {
            if (res.state == 1000) {
                if (res.data == "Success") {
                    ElNotify({ type: "success", message: "亲，删除成功啦！" });
                    window.dispatchEvent(new CustomEvent("deleteDepartment", { detail: aaeId }));
                    useAssistingAccountingStoreHook().getAssistingAccounting(aaType);
                    if (aaType === 10004) {
                        useAssistingAccountingStoreHook().getDepartment();
                    }
                    successCallBack();
                } else {
                    ElNotify({ type: "warning", message: res.msg });
                }
            } else {
                const obj: { [key: string]: string } = {
                    CashierUsed: "日记账",
                    ExpenseBillUsed: "费用单据",
                };
                if ([10004, 10005].includes(aaType) && obj[res.msg]) {
                    ElNotify({ type: "warning", message: `亲，该辅助核算项目已被${obj[res.msg]}引用，不能删除哦！` });
                } else {
                    ElNotify({ type: "warning", message: res.msg });
                }
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "亲，删除失败啦！" });
        });
};
export const exportHandle = (aaType: number, length: number) => {
    const basePath = getUrlPath(aaType) + "Export";
    const urlPath = aaType > 10007 ? basePath + "?aaType=" + aaType : basePath;
    globalExport("/api/AssistingAccounting/" + urlPath);
};
export const getNextAaNum = (aaType: number) => {
    const params = {
        categoryId: 0,
        aaType,
    };
    return request({ url: "/api/AssistingAccounting/GetNewAANum?" + getUrlSearchParams(params), method: "post" });
};
export const getModelApi = (aaeID: string | number, aaType: number) => {
    const basePath = getUrlPath(aaType) + "Model";
    const urlPath = aaType > 10007 ? basePath + "?aaTypeId=" + aaType : basePath;
    return request({ url: "/api/AssistingAccounting/" + urlPath + (aaType > 10007 ? "&" : "?") + "aaeId=" + aaeID });
};
export const formatDate = (_row: any, _column: any, time: any) => {
    return time ? time.split("T")[0] : "";
};
export const getUrlPath = (aaType: number) => {
    let urlPath = "";
    switch (aaType) {
        case 10001:
            urlPath = "Customer";
            break;
        case 10002:
            urlPath = "Vendor";
            break;
        case 10003:
            urlPath = "Employee";
            break;
        case 10004:
            urlPath = "Department";
            break;
        case 10005:
            urlPath = "Project";
            break;
        case 10006:
            urlPath = "Stock";
            break;
        case 10007:
            urlPath = "CashFlow";
            break;
        default:
            urlPath = "Custom";
            break;
    }
    return urlPath;
};
export const createCheck = debounce(
    (aaType: number, aaeId: string | number, aaNum: string | number, aaName: string, successCallBack: Function) => {
        const params = { aaeId, aaNum, aaName };
        request({
            url: "/api/AssistingAccounting/Check?aaType=" + aaType,
            method: "post",
            data: params,
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
        }).then((res: IResponseModel<string>) => {
            if (res.state != 1000) return;
            const result = res.data;
            if (result == "Y") {
                ElNotify({ type: "warning", message: "编号重复，请重新编号！" });
                return;
            }
            if (result == "Name") {
                ElConfirm("亲，此名称已存在，是否继续保存？").then((r: any) => {
                    if (r) successCallBack();
                });
            } else {
                successCallBack();
            }
        });
    },
    500
);

export enum LimitCharacterSize {
    // 编码
    Code = 18,
    // 备注
    Note = 1024,
    // 名称
    Name = 256,
    // 统一社会信用代码
    USCC = 18,
    // 手机号
    Phone = 11,
    // 默认
    Default = 64,
    // 规格
    Size = 256,
    //收支类别编码
    categoryCode = 4,
}
let lastValue = "";
let count = 0;
export const handleAAInput = (
    limitSize: LimitCharacterSize,
    e: any,
    type: string,
    key?: string,
    callBack?: (key: string, val: string) => void,
    errMsg?: string
) => {
    const { value } = e.target as HTMLInputElement;
    if (value.length > limitSize) {
        count++;
        ElNotify({
            type: "warning",
            message: errMsg ? errMsg : "亲，" + type + "不能超过" + limitSize + "个字符!",
        });
        const newValue = count === 1 ? value.slice(0, limitSize) : lastValue;
        // e.target.value = newValue;
        lastValue = newValue;
        if (key && callBack) callBack(key, newValue);
    } else if(value.length === limitSize) {
        lastValue = value;
        count = 1;
    }
};
export const handleAAPaste = (
    limitSize: LimitCharacterSize,
    e: any,
) => {
    const clipboardData = e.clipboardData || (e.originalEvent as ClipboardEvent)?.clipboardData;  
    const pastedText = clipboardData?.getData('text') || '';
    if ( pastedText.length > limitSize)  {
        lastValue = "";
        count = 0;
    }
};

export const textareaBottom =(exampleRef:any)=>{
    const  textareaInner = exampleRef.value?.$el.querySelector('.el-textarea__inner');
    if (textareaInner) {
        // 使用 requestAnimationFrame 来确保元素已经准备好
        requestAnimationFrame(() => {
            textareaInner.scrollTop = textareaInner.scrollHeight;
        });
    }
};
export const autocompleteSelfAdaption =()=>{
    const autocompleteHeight=document.querySelector('.el-autocomplete__popper.el-popper');
    const textareaHeight=document.querySelector('.el-textarea__inner');
    if(textareaHeight&&autocompleteHeight){
        const height=textareaHeight.style.height
        switch (height) {
            case '31px':
                autocompleteHeight.style.marginTop = '0';
                break;
            case '52px':
                autocompleteHeight.style.marginTop = '20px';
                break;
            case '73px':
                autocompleteHeight.style.marginTop = '40px';
                break;
        }
    }
}
