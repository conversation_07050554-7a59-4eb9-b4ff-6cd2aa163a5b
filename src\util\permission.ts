import { useAccountSetStoreHook } from "@/store/modules/accountSet";

/** 全局权限判断函数，和指令 v-permission 功能类似 */
export const checkPermission = (value: string[]) => {
    if (value && value instanceof Array && value.length > 0) {
        //判断传递进来的按钮权限，用户是否拥有
        const permissions = useAccountSetStoreHook().permissions;
        //Array.some(),数组中有一个结果是true返回true，剩下的元素不会再检测
        return value.some((role: any) => {
            return permissions.includes(role);
        });
    } else {
        console.error("need roles! Like v-permission=\"['admin','editor']\"");
        return false;
    }
};
