import { onActivated, ref } from "vue";
import { isLemonClient } from "@/util/lmClient";
import { globalDirectPrint, globalPostPrint } from "@/util/url";
import { getLocalStorage, setLocalStorage } from "@/util/localStorageOperate";
import { getFormatterDate } from "@/util/date";

//关于打印，相似度较高，所以提取得到这个hook
const usePrint = (
    storeKey: string,
    printApi: string,
    extraInfo: any = {},
    dir: boolean = false,
    asName: boolean = true,
    validatorCallBack: () => boolean = () => true
) => {
    const printDialogVisible = ref(false);
    const dialogType = ref<"print" | "export">("print");
    const showDialog = ref(false);
    const printUrl = ref(printApi);
    //0是纵向打印，1是横向打印
    // 左  12, 右 11,  上  6, 下  20
    const printInfo = ref<PrintInfoType>({
        top: 6,
        bottom: 20,
        left: 12,
        right: 11,
        isShowPageNumber: false,
        isShowPrintDate: false,
        printDateText: "",
        bottomTextList: [],
        ...(dir ? { direction: 0 } : {}),
        ...extraInfo,
        ...(asName ? { isHideAsName: false } : {}),
    });
    marginEnum[storeKey] && (printInfo.value = { ...printInfo.value, ...marginEnum[storeKey] });
    const storeData = JSON.parse(getLocalStorage(storeKey + "Print") ?? "{}");
    printInfo.value = { ...printInfo.value, ...storeData, printDateText: getFormatterDate("", "-") };
    // 处理历史数据移除isHideAsName
    if(!asName) if(Object.prototype.hasOwnProperty.call(printInfo.value, "isHideAsName")) delete printInfo.value.isHideAsName

    const otherOptions = ref([
        { key: "isShowPrintDate", label: "显示打印日期" },
        { key: "isShowPageNumber", label: "显示页码" },
    ]);
    function isEmptyFunction(func: Function) {
        const funcAsString = func.toString().replace(/\s/g, "");
        return funcAsString === "function(){}" || funcAsString === "()=>{}";
    }
    // 0: 直接打印 1: 批量打印 2: 打印设置 3: 打印设置预览
    function handlePrint(printType: 0 | 1 | 2 | 3, defaultParams: any = {}, printApiWithParams: string = "") {
        if (printType === 0) {
            const printData = {
                seniorModelJson: JSON.stringify(printInfo.value),
                ...defaultParams,
            };
            if (!isEmptyFunction(validatorCallBack) && !validatorCallBack()) return;
            const nonAsync = ["AssistCombineStatement"];
            let printApi = printUrl.value
            if (
                printUrl.value.includes(window.jAccountBooksHost) &&
                !printUrl.value.includes("Direct") &&
                nonAsync.every((item) => printUrl.value.indexOf(item) === -1)
            )
            printApi = printUrl.value.replace("Print", "DirectPrint") + (!printUrl.value.includes("?async") ? "?async=" + !isLemonClient() : "");
            globalDirectPrint(printApiWithParams ? printApiWithParams : printApi, "post", printData,"打印中，请稍候...");
        } else if (printType === 1) {
            // 批量打印
            dialogType.value = "print";
            showDialog.value = true;
        } else if (printType === 2) {
            printDialogVisible.value = true;
        } else if (printType === 3) {
            const printData = {
                seniorModelJson: { ...printInfo.value },
                ...defaultParams,
            };
            if (!isEmptyFunction(validatorCallBack) && !validatorCallBack()) return;
            setLocalStorage(storeKey + "Print", JSON.stringify(printInfo.value));
            const nonAsync = ["AssistCombineStatement"];
            let printApi = printUrl.value
            if (
                printUrl.value.includes(window.jAccountBooksHost) &&
                nonAsync.every((item) => printUrl.value.indexOf(item) === -1) &&
                !printUrl.value.includes("?async")
            ) {
                printApi = printUrl.value + "?async=" + !isLemonClient();
            }
            printDialogVisible.value = false
            globalPostPrint(printApiWithParams ? printApiWithParams : printApi, printData);
        }
    }

    function updatePrintApi(printApi: string) {
        printUrl.value = printApi;
    }
    
    function updataPritnInfo() {
        marginEnum[storeKey] && (printInfo.value = { ...printInfo.value, ...marginEnum[storeKey] });
        const storeData = JSON.parse(getLocalStorage(storeKey + "Print") ?? "{}");
        return { ...printInfo.value, ...storeData, printDateText: getFormatterDate("", "-") };
    }

    onActivated(() => {
        if(!syncstoreKey.includes(storeKey)) return;
        printInfo.value = updataPritnInfo();
    });

    return { printDialogVisible, dialogType, showDialog, printInfo, otherOptions, handlePrint, updatePrintApi, updataPritnInfo };
};

export type PrintInfoType = {
    top: number;
    bottom: number;
    left: number;
    right: number;
    isShowPageNumber: boolean;
    isShowPrintDate: boolean;
    printDateText: string;
    bottomTextList: string[];
    isHideAsName?: boolean;
    basepage?: boolean;
    printCatalog?: boolean;
    isHideSubject?: boolean;
    isTitleSubjectShowLeaf?: boolean;
    isBodySubjectShowLeaf?: boolean;
    continuePrint?: boolean;
    direction?: number;
    pageType?: number;
    isPrintCashierName?: boolean; //出纳
    isPrintAccountantName?: boolean; //会计
    isPrintUsedName?: boolean; //经办人
    isPrintDepartment?: boolean;
    isPrintProject?: boolean;
    isHideAACode?: boolean;
    font?: string;
};

const marginEnum: { [key: string]: { top: number; bottom: number; left: number; right: number } } = {
    generalLedger: { top: 12, bottom: 20, left: 3, right: 3 },
    balanceSheet: { top: 12, bottom: 0, left: 8, right: 4 },
    joural:{ top: 10, bottom: 20, left: 0, right: 0 },
    fixedAssetsList:{ top: 20, bottom: 20, left: 3, right: 2 },
    initFixedAssets:{ top: 20, bottom: 20, left: 3, right: 2 },
    incomeSheet:{ top: 12, bottom: 0, left: 12, right: 8 },
    subsidiaryLedger:{ top: 10, bottom: 20, left: 12, right: 11 },
    businessActivityStatement:{ top: 12, bottom: 0, left: 12, right: 8 },
    memberEquityChangesStatement:{ top: 11, bottom: 0, left: 10, right: 7 },
    incomeAndExpenditureStatement:{ top: 11, bottom: 0, left: 20, right: 17 },
    costStatement:{ top: 11, bottom: 0, left: 20, right: 17 },
    cashFlowStatement:{ top: 10, bottom: 0, left: 12, right: 8 },
    standardCashFlowStatement:{ top: 10, bottom: 0, left: 8, right: 5 },
    cashFlowAdjust:{top: 11, bottom: 0, left: 20, right: 20},
    cashFlowDraft:{top: 11, bottom: 5, left: 12, right: 8},
    categorizedAccountsSummary:{top: 10, bottom: 20, left: 8, right: 8},
    trialBalance:{top: 10, bottom: 20, left: 8, right: 8},
    aaSubsidiaryLedger:{top: 10, bottom: 20, left: 12, right: 11},
    aaTrialBalance:{top: 10, bottom: 20, left: 12, right: 11},
    assistCombineStatement:{top: 14, bottom: 20, left: 0, right: 0},
    multiColumnLedger:{top: 14, bottom: 20, left: 12, right: 11},
    ageAnalysis:{top: 14, bottom: 20, left: 12, right: 11},
    surplusDistributionStatement:{top: 10, bottom: 20, left: 12, right: 11},
    salary:{top: 11, bottom: 15, left: 13, right: 13},
    transfer:{top: 10, bottom: 10, left: 6, right: 6},
    cashflowAssumption:{top: 10, bottom: 0, left: 15, right: 15},
    sales:{top: 10, bottom: 20, left: 12, right: 11},
    purchase:{top: 10, bottom: 20, left: 12, right: 11},
    expenseStatement:{top: 10, bottom: 20, left: 12, right: 11},
    financialSummary:{top: 12, bottom: 20, left: 12, right: 11},
    incomeDistributionSheet:{top: 10, bottom: 20, left: 20, right: 20},
    receipt:{top: 5, bottom: 0, left: 20, right: 16},
};

const syncstoreKey = ["incomeSheet","cashFlowStatement"]

export default usePrint;
