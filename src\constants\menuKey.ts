export enum MainMenuType {
    BaseFunction = 1000000000,
    Voucher = 1010000000,
    Cashier = 1015000000,
    Invoice = 1016000000,
    Salary = 1017000000,
    FixAsset = 1020000000,
    AccoutBook = 1030000000,
    CheckOut = 1040000000,
    Statement = 1050000000,
    Tax = 1055000000,
    Settings = 1060000000,
    ERP = 1070000000,
}

export enum SubMenuType {
    Login = 1000010000,
    Logout = 1000020000,
    HomePage = **********,
    Calc = **********,
    PersonalCalc = **********,
    PersonalInfo = **********,
    NewVoucher = **********,
    VoucherList = **********,
    ScmVoucher = **********,
    LemonDisk = **********,
    LemonDiskSPB = **********,
    BackUpPlus = **********,
    CashJournal = **********,
    DepositJournal = **********,
    Report = **********,
    AAJournalCombination = **********,
    CDCheck = **********,
    CDCheckDetail = **********,
    IEType = **********,
    CDAccount = **********,
    CDJournal = **********,
    CDJournalDetail = **********,
    Journal = **********,
    IEJournal = **********,
    AAJournal = **********,
    DepartmentJournal = **********,
    ProjectJournal = **********,
    JournalPage = **********,
    Transfer = **********,
    BankAndCompany = **********,
    Invoice = **********,
    
    SalesInvoice = **********,
    PurchaseInvoice = **********,
    TaxCalculation = **********,
    FetchInvoice = **********,
    RiskAnaly = **********,
    ExpenseBill = **********,
    CashierInvoiceTable = **********,
    CashierInvoiceInfo = **********,

    SalaryManage = **********,
    EmployInfo = **********,
    InsuranceSet = **********,
    SalarySummary = **********,
    
    FixedAssets = **********,
    DepreciationLedger = **********,
    FASummary = **********,
    FixedAssetType = **********,
    FACheck = **********,
    FACheckDetail = **********,
    SubsidiaryLedger = **********,
    GeneralLedger = **********,
    TrialBalance = **********,
    AASubsidiaryLedger = **********,
    AATrialBalance = **********,
    MultiColumnLedger = **********,
    CategorizedAccountsSummary = **********,
    AJournal = **********,
    AssistCombineStatement = **********,
    AccountBookCoverSettings = **********,  
    Checkout = **********,
    BalanceSheet = **********,
    IncomeStatement = **********,
    AssistIncomeStatementDepartment = **********,
    AssistIncomeStatementProject = **********,
    IncomeQuaterStatement = **********,
    IncomeStatementDepartment = **********,
    IncomeStatementProject = **********,
    CashFlowStatement = **********,
    CashFlowStatementQuarter = **********,
    CustomStatement = **********,
    TaxReport = **********,
    FinancialSummary = **********,
    BusinessActivity = **********,
    SurplusDistributionStatement = **********,
    MemberEquityChangesStatament = **********,
    OwerEquityChangeStatement = **********,
    TaxDeclaration = **********,
    TaxInformation = **********,
    TaxDeclarationRecord = **********,
    IncomeAndExpenditureStatement = **********,
    CostStatement = **********,
    IncomeDistributionStatement = **********,
    AccountSets = **********,
    AccountSetsRecycleBin = **********,
    AccountingSubject = **********,
    InitialBalance = **********,
    Currency = **********,
    VoucherGroup = **********,
    OperationLog = **********,
    Permissions = **********,
    AssistingAccounting = **********,
    VoucherTemplate = **********,
    Reinitialize = **********,
    BackUp = **********,
    BackUpData = **********,
    EisRelation = **********,
    ScmRelation = **********,
    ScmSettings = **********,
    ScmBasicData = **********,
    ImportFromOther = **********,
    CompanyCertification = **********,
    WeworkBinding = **********,
    Draft = 1060099000,
    BusinessVoucher = 1070010000,
    AsubRelationSettings = 1070020000,
    BusinessVoucherTemplate = 1070030000,
    BossPermission = 1070040000,
    AgeAnalysis = 1030100000,    
    ExpenseStatement = 1050095000,
    StandardCashFlowStatement = 1050091000,
    VoucherPrintSettings = 1010070000,
    VoucherCoverSettings = 1010071000,
}
