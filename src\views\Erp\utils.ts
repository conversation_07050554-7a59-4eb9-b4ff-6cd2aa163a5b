import { useScmInfoStoreHook } from "@/store/modules/scm";
import { request } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";

export async function resetErpVoucherHistory() {
    const { scmAsid, scmProductType } = useScmInfoStoreHook();
    const params = { scmAsid, scmProductType };
    await request({ url: "/api/VoucherForErp/Repair?" + getUrlSearchParams(params), method: "post" });
}
