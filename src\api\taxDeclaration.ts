import { IResponseModel, request, Jrequest, requestWithPeriodId } from "@/utils/service"

// 逾期列表
export async function overdueList() {
  return (await requestWithPeriodId({
    url: "/api/declare/late-list",
  })) as any as Promise<IResponseModel<any>>
}

// 获取增值税月报申报期限
interface DeclarationPeriodData {
  id: number
  year: number
  month: number
  startDate: string
  endDate: string
}
export async function getAddTaxMonthDeclarationDeadline(): Promise<IResponseModel<DeclarationPeriodData>> {
  return await request({
    method: "get",
    url: "/api/basic/current-period",
  })
}

// 申报列表
export async function taxDeclarationList() {
  return (await requestWithPeriodId({
    url: "/api/declare/list",
  })) as any as Promise<IResponseModel<any>>
}

// 获取税款所属期内最后一次更新申报结果的时间
export async function getLatestUpdateTime(params: any) {
  return (await requestWithPeriodId({
    url: "/api/task/latest-by-periodid",
    params,
  })) as any as Promise<IResponseModel<any>>
}

// 更新申报结果
export async function updateTaxDeclarationResult() {
  return (await requestWithPeriodId({
    method: "post",
    url: "/api/declare/refresh",
  })) as any as Promise<IResponseModel<any>>
}

// 所有税款期财务结账列表(账套起始日期)
export async function getFinancialSettlementList() {
  return (await Jrequest({
    url: "/api/Period/ListWithStatus",
  })) as any as Promise<IResponseModel<any>>
}

// 财务报表表格数据
export async function getFinancialReportData(urlList: string[]) {
  return await Promise.all(
    urlList.map((url) => {
      return requestWithPeriodId({
        url,
      })
    }),
  )
}

// 财务报表表格数据保存
export async function saveFinancialReportData(urlList: string[], data: any) {
  return await Promise.all(
    urlList.map((url, index) => {
      return requestWithPeriodId({
        method: "post",
        url,
        data: data[index],
      })
    }),
  )
}

// 财报表格公式
export async function getFinancialReportFormula(url: string) {
  return (await request({
    url,
  })) as any as Promise<IResponseModel<any>>
}

// 增值税月报相关接口
export async function getAddTaxMonthData(url: string) {
  return (await requestWithPeriodId({
    url: "/api/month_added_tax" + url,
  })) as any as Promise<IResponseModel<any>>
}

// 增值税月报保存相关接口
export async function saveAddTaxMonthData(url: string, data: any) {
  return (await requestWithPeriodId({
    method: "post",
    url: "/api/month_added_tax" + url,
    data: {
      datas: data,
    },
  })) as any as Promise<IResponseModel<any>>
}
