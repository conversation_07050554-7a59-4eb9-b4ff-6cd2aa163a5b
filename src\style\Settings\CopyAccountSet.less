:deep(.el-radio.el-radio--large) {
    height: 32px;
}
.slot-content-mini {
    width: 1000px;
    padding: 32px 0 0;
    border: 1px solid var(--slot-title-color);
    margin-top: 32px;
}
.create-content {
    // width: var(--edit-content-width);
    background-color: var(--white);
    overflow: hidden;
    & .error-msg {
        margin: 0 auto;
        width: var(--edit-content-width);
        height: 17px;
        // margin-top: 40px;
        padding-left: 204px;
        color: var(--red);
        font-size: var(--h5);
        line-height: 17px;
        text-align: left;
        box-sizing: border-box;
    }
    & .line-item1 {
        height: 32px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 32px;
        margin-top: 4px;
        margin-top: 16px;
        & .line-item-left {
            height: 32px;
            float: left;
            width: 50%;
            & .line-item-field {
                width: 296px;
                text-align: left;
                padding-left: 10px;
                float: right;
                position: relative;

                & :deep(el-input__wrapper) {
                    padding: 0;
                }
            }
            & .line-item-title {
                float: right;
                text-align: right;
                // line-height: 32px;
            }
        }
        & .line-item-right {
            height: 32px;
            float: left;
            width: 50%;
            & .line-item-title {
                width: 156px;
                float: left;
                text-align: right;
                // line-height: 32px;
            }
            & .line-item-field {
                float: left;
                text-align: left;
                padding-left: 10px;
                position: relative;
            }
        }
    }
    & .line-item2 {
        height: 20px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 20px;
        margin-top: 20px;
        & .line-item-left {
            height: 20px;
            float: left;
            width: 50%;
            & .line-item-field {
                width: 296px;
                float: right;
                text-align: left;
                padding-left: 10px;
                position: relative;
            }
            & .line-item-title {
                float: right;
                text-align: right;
            }
        }
        & .line-item-right {
            height: 20px;
            float: left;
            width: 50%;
            & .line-item-title {
                width: 156px;
                float: left;
                text-align: right;
            }
            & .line-item-field {
                float: left;
                text-align: left;
                padding-left: 10px;
                position: relative;
            }
        }
    }
    & .buttons {
        margin-top: 32px;
        text-align: center;
        & a {
            margin: 0 5px;
        }
    }
    & .report-container {
        background: url(@/assets/Settings/account-set-import-bg2.png) no-repeat;
        height: 78px;
        margin-top: 27px;
        & .report-container-title {
            float: left;
            margin-top: 25px;
            color: var(--white);
            font-size: 22px;
            line-height: 30px;
            font-weight: 500;
            margin-left: 50px;
        }
        & .report-container-sub-title {
            float: left;
            margin-top: 30px;
            color: var(--white);
            font-size: var(--font-size);
            line-height: 20px;
        }
        & .report-container-btn {
            float: right;
            margin-right: 60px;
            margin-top: 24px;
            border-radius: 16px;
            width: 102px;
            height: 32px;
            background: var(--white);
            cursor: pointer;
            text-align: center;
            color: #1890ff;
            font-size: var(--h5);
            line-height: 32px;
            font-weight: 600;
            position: relative;
            &:hover {
                & .link-qrcode {
                    visibility: visible;
                    opacity: 1;
                }
            }
            & .link-qrcode {
                visibility: hidden;
                opacity: 0;
                transition: 0.2s;
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                bottom: 35px;
                padding-bottom: 5px;
                width: 182px;
            }
        }
    }
}
.select-copy-content {
    height: 20px;
    margin-top: 32px;
    margin-bottom: -16px;
    text-align: center;
}
.tips {
    padding-left: 68px;
    width: var(--edit-content-width);
    margin: 8px auto 32px;
    position: relative;
    text-align: left;
    box-sizing: border-box;
    & .tips-title {
        color: var(--red);
        font-size: var(--h5);
        line-height: 17px;
    }
    & .tips-content div {
        color: var(--weaker-font-color);
        font-size: var(--h5);
        line-height: var(--line-height);
    }
}
.select-content-content {
    // width: var(--edit-content-width);
    background-color: var(--white);
    // transform: translateX(calc((var(--content-width) - var(--edit-content-width)) / 2));
    // z-index: 100;
    // margin:0 auto;
    & .select-copy-content-container {
        padding: 46px 0 33px;
        & .select-copy-content-title {
            color: var(--font-color);
            font-size: var(--h3);
            line-height: 22px;
            font-weight: bold;
            margin: 0 auto;
            width: 487px;
            margin-bottom: 2px;
            text-align: left;
        }
        & .select-copy-content-item-card {
            box-sizing: border-box;
            width: 487px;
            margin: 20px auto 0;
            padding: 17px 0 17px 16px;
            border: 1px solid #d4d4d4;
            border-radius: 4px;
            font-size: 0;
            cursor: pointer;
            &.selected {
                & .card-circle {
                    background-image: url(@/assets/Settings/circle-solid.png);
                }
                border-color: var(--main-color);
                background-color: rgba(68, 180, 73, 0.1);
            }
            & .card-circle {
                display: inline-block;
                vertical-align: middle;
                width: 23px;
                height: 23px;
                background: url(@/assets/Settings/circle-ring.png) no-repeat;
                background-position: center center;
                background-size: 100%;
            }
            & .card-content {
                display: inline-block;
                vertical-align: middle;
                padding-left: 13px;
                max-width: 406px;
                & .card-title {
                    margin-bottom: 6px;
                    height: 20px;
                    width: 421px;
                    font-size: 14px;
                    text-align: left;
                    & span.txt {
                        font-size: var(--font-size);
                        float: left;
                        color: var(--font-color);
                        line-height: 20px;
                    }
                    & .jqtransform {
                        float: left;
                        margin-top: -1.5px;
                        margin-bottom: -1.5px;
                        & .select-year {
                            width: 80px;
                        }
                        & .select-month {
                            margin-left: 10px;
                            width: 60px;
                        }
                    }
                }
                & .card-desc {
                    height: 17px;
                    white-space: normal;
                    word-break: break-all;
                    font-size: 12px;
                    & .icon {
                        width: 14px;
                        height: 14px;
                        margin-right: 4px;
                        float: left;
                        margin-top: 1px;
                    }
                    & .txt {
                        color: #8f8f8f;
                        font-size: var(--h5);
                        line-height: 17px;
                        float: left;
                        & + .icon {
                            margin-left: 19px;
                        }
                    }
                }
            }
        }
        & .copy-files {
            margin-top: 25px;
            text-align: center;
            & .checkbox-button {
                height: 20px;
                display: inline-block;
                & .checkbox-box:last-child {
                    padding-left: 22px;
                    background-repeat: no-repeat;
                    background-position: top 3px left;
                    background-image: url(@/assets/Settings/checkbox.png);
                }
                & input[type="checkbox"]:nth-child(1):checked + .checkbox-box {
                    background-image: url(@/assets/Settings/checkbox-checked.png);
                }
            }
        }
        & .buttons {
            text-align: center;
            margin-top: 44px;
        }
    }
}

.new-message-box {
    & .box-body {
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;

        & .body-message {
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            width: 100%;
            & .body-message-title {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                white-space: normal;
                word-break: break-all;
            }
            & .body-message-content {
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 17px;
                margin-top: 10px;
                text-align: left;
                width: 100%;
                padding: 0;
                white-space: normal;

                & .info-block {
                    height: 32px;
                    line-height: 32px;
                }

                & .reset-info-block {
                    margin: 0 53px 19px 91px;
                }
            }
        }
    }
}
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
    // width: var(--content-width) !important;
    width: 100% !important;
}
:deep(.el-radio.is-disabled) {
    .el-radio__input .el-radio__inner {
        background-color: white !important;
        border-color: #d9d9d9;
    }
    .el-radio__label {
        color: black !important;
    }
}
