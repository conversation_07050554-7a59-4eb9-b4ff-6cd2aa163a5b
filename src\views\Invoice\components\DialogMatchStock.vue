<template>
    <el-dialog class="custom-confirm match-stock-dialog dialogDrag" title="自定义匹配存货" center width="80%" v-model="matchStocktShow">
        <div class="match-stock-dialog-content" v-dialogDrag>
            <div class="match-stock-dialog-top">
                <a class="button ml-10" @click="handleBatchMatchStock">批量匹配</a>
            </div>
            <div class="match-stock-dialog-center">
                <Table
                    class="match-stock-table"
                    ref="matchStockTableRef"
                    :data="matchTableData"
                    :columns="Columns"
                    :page-is-show="false"
                    v-loading="loading"
                    empty-text="暂无数据"
                    row-key="rowId"
                    :scrollbarShow="true"
                    :row-class-name="setErrorRowStyle"
                    @selection-change="tableSelectionChange"
                    @row-click="tableRowClick"
                    :tableName="setModule"
                >
                    <template #stockName>
                        <el-table-column 
                            label="存货名称" 
                            min-width="200" 
                            align="left" 
                            header-align="left" 
                            prop="stockName"
                            :width="getColumnWidth(setModule, 'stockName')"
                        >
                            <template #default="scope">
                                <div style="width: 100%; height: 100%">
                                    <div class="click-show-item select-complete">
                                        <GoodSelect
                                            v-model="scope.row.modelValue"
                                            placeholder=" "
                                            placement="bottom-end"
                                            filterable
                                            :ref="
                                                (el) => {
                                                    stockSelectRef[scope.$index] = el;
                                                }
                                            "
                                            suffix-icon="arrow"
                                            :fit-input-width="true"
                                            :bottom-html="checkPermission(['assistingaccount-canedit']) ? selectBottomHtml : ''"
                                            :options="showStockListOptions"
                                            :props="GoodsProp"
                                            @bottom-click="handleNewStock(scope.$index)"
                                            @change="handleChangeCurrentRowStock($event, scope.$index, scope.row)"
                                            :remote="true"
                                            :filter-method="stockFilterMethod"
                                            @visible-change="handleVisibleChange"
                                        >
                                        </GoodSelect>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
        <div class="buttons bt">
            <a class="button solid-button" @click="submitMatchStockData">保存</a>

            <a class="button ml-10" @click="matchStocktShow = false">取消</a>
        </div>
        <el-dialog v-model="batchMatchDialogShow" title="批量匹配" center width="440" class="custom-confirm dialogDrag" top="25vh">
            <div class="certification-show-content" v-dialogDrag>
                <div class="certification-show-main">
                    <el-form-item label="存货：" class="certification-form line-item" :show-message="false" prop="businessType">
                        <GoodSelect
                            ref="stockBatchSelectRef"
                            v-model="stockSlected"
                            placeholder="请选择"
                            placement="bottom-end"
                            filterable
                            suffix-icon="arrow"
                            :fit-input-width="true"
                            :options="showStockListbatchOptions"
                            :props="GoodsProp"
                            @change="handleBatchMatchStockSelected"
                            :remote="true"
                            :filter-method="batchstockFilterMethod"
                            @visible-change="handleVisibleChangeBatch"
                        >
                        </GoodSelect>
                    </el-form-item>
                </div>
                <div class="buttons">
                    <a class="button solid-button ml-10" @click="certificationSure()">确定</a>
                    <a class="button ml-10" @click="() => (batchMatchDialogShow = false)">取消</a>
                </div>
            </div>
        </el-dialog>
        <DialogAddStockt ref="dialogAddStocktRef" :autoAddName="autoAddName" v-model:add-stockt-show="addStocktShow" @save-stockt="normalSaveStockt"></DialogAddStockt>
    </el-dialog>
</template>
<script setup lang="ts">
import { nextTick, ref, computed, watchEffect } from "vue";
import { type IResponseModel, request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import Table from "@/components/Table/index.vue";
import { selectBottomHtml } from "@/views/Cashier/CashOrDepositJournal/utils";
import DialogAddStockt from "./DialogAddStockt.vue";
import { checkPermission } from "@/util/permission";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import GoodSelect from "./GoodSelect.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

import type { ICustomStockItem, IInvoiceStockItem, ICustomStockItemApi, AAListWithAcronymItem } from "../types";
const dialogAddStocktRef = ref();
const addStocktShow = ref(false);
const props = defineProps<{
    invoiceCategory: string;
}>();
const emit = defineEmits<{
    (e: "save-stockt"): void;
}>();

const matchStocktShow = ref(false);
const setModule = "InvoiceMatchStock";
const Columns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 40, headerAlign: "center", align: "center" },
    {
        label: "序号",
        prop: "rowId",
        headerAlign: "left",
        align: "left",
        minWidth: "55",
        width: getColumnWidth(setModule, 'rowId')
    },
    { slot: "index" },
    {
        label: "发票",
        prop: "",
        headerAlign: "center",
        children: [
            { label: "商品名称", prop: "ename", minWidth: "200", width: getColumnWidth(setModule, 'ename') },
            { label: "规格型号", prop: "emodel", minWidth: "200", width: getColumnWidth(setModule, 'remodelowId') },
        ],
    },
    {
        label: "存货",
        prop: "",
        headerAlign: "center",
        children: [
            { slot: "stockName" },
            { label: "规格型号", prop: "stockModel", minWidth: "200", width: getColumnWidth(setModule, 'stockModel') },
            { label: "单位", prop: "stockUnit", minWidth: "200", resizable: false },
        ],
    },
]);
const matchTableData = ref<ICustomStockItem[]>([]);
const batchMatchDialogShow = ref(false);
const stockSlected = ref<any>(null);
const GoodsProp = {
    value: "value",
    text: "label",
    label: "name",
};
function escapeRegExp(aanum: string) {
    return aanum.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
const stockListOptions = computed(()=>{
    let data  = useAssistingAccountingStore().assistingAccountingWithAcronymList;
    let list: IInvoiceStockItem[] = [];
    for (let i = 0; i < data.length; i++) {
            const item = data[i];
            if (item.aatype === 10006 && item.aaeid > 0 && item.status === 0) {
                const pushItem = {
                    name: item.aaname.replace(new RegExp(`^${escapeRegExp(item.aanum)} `), ""),
                    value: item.aaeid,
                    num: item.aanum,
                    model: item.model ? item.model.stockModel : "",
                    unit: item.model ? item.model.unit : "",
                    label: "",
                };
                pushItem.label = pushItem.num + " " + pushItem.name + " " + pushItem.model + " " + pushItem.unit;
                list.push(pushItem);
            }
        }
        return list;
})
const updateStockInfo = (item: ICustomStockItem, stockInfo: IInvoiceStockItem) => {
    item.stockName = stockInfo.name;
    item.stockModel = stockInfo.model;
    item.stockUnit = stockInfo.unit;
    item.stockAAEId = stockSlected.value;
    item.modelValue = stockSlected.value;
};
const stockBatchSelectRef = ref<InstanceType<typeof GoodSelect>>();
const handleBatchMatchStockSelected = () => {
    nextTick(() => {
        stockBatchSelectRef.value?.blur();
    });
};
let selectedTableData = ref<ICustomStockItem[]>([]);
const certificationSure = (): void => {
    if (!stockSlected.value) {
        ElNotify({
            type: "warning",
            message: "亲，请选择存货哦~",
        });
        return;
    }

    let stockInfo = stockListOptions.value.find((v) => v.value === stockSlected.value);
    if (stockInfo) {
        selectedTableData.value.forEach((v) => {
            updateStockInfo(v, stockInfo);
        });
    }

    batchMatchDialogShow.value = false;
    ElNotify({
        type: "success",
        message: "批量匹配成功！",
    });
};

const handleChangeCurrentRowStock = (value: number, index: number, row: ICustomStockItem) => {
    stockSlected.value = value;
    let stockInfo = stockListOptions.value.find((v) => v.value === value);
    if (stockInfo) {
        updateStockInfo(row, stockInfo);
    }
    nextTick(() => {
        stockSelectRef.value[index].blur();
    });
};
const handleVisibleChange = (visible: boolean) => {
    if (visible) {
        showStockListOptions.value = JSON.parse(JSON.stringify(stockListOptions.value));
    }
}
const handleVisibleChangeBatch = (visible: boolean) => {
    if (visible) {
        showStockListbatchOptions.value = JSON.parse(JSON.stringify(stockListOptions.value));
    }
}
function tableRowClick(row: any, column: any) {
    if (column.label === "存货名称") {
        return;
    }
    let selected = selectedTableData.value.findIndex((item: any) => item.rowId === row.rowId) >= 0;
    matchStockTableRef.value?.getTable()?.toggleRowSelection(row, !selected);
}
const tableSelectionChange = (selection: ICustomStockItem[]) => {
    selectedTableData.value = selection;
};

const handleBatchMatchStock = () => {
    if (selectedTableData.value.length === 0) {
        ElNotify({
            type: "warning",
            message: "亲，请选择发票商品哦~",
        });
        return;
    }
    batchMatchDialogShow.value = true;
    stockSlected.value = "";
};
let currentIndex = ref(-1);
const stockSelectRef = ref<any>([]);
const handleNewStock = (index: number) => {
    stockSelectRef.value[index].blur();
    currentIndex.value = index;
    addStocktShow.value = true;
    dialogAddStocktRef.value?.changeCode(autoAddName.value);
};

const normalSaveStockt = async (data: string) => {
    addStocktShow.value = false;
    await useAssistingAccountingStore().getAAListWithAcronym();
    handleChangeCurrentRowStock(Number(data), currentIndex.value, matchTableData.value[currentIndex.value]);
};
let errorRowIndex = ref(-1);
const matchStockTableRef = ref<InstanceType<typeof Table>>();
let isSaving = false;
const submitMatchStockData = () => {
    if (matchTableData.value.length === 0) {
        matchStocktShow.value = false;
        ElNotify({
            type: "warning",
            message: "亲，暂无数据,无需保存哦~",
        });
        return;
    }
    if (isSaving) {
        ElNotify({
            type: "warning",
            message: "亲，正在保存中，请稍后~",
        });
        return;
    }
    for (let i = 0; i < matchTableData.value.length; i++) {
        if (!matchTableData.value[i].modelValue) {
            ElNotify({
                type: "warning",
                message: "亲，第" + (i + 1) + "行，请匹配存货哦~",
            });
            errorRowIndex.value = i;
            matchStockTableRef.value?.getTable()?.setScrollTop((i + 1 - 5) * 37);
            return;
        }
    }
    isSaving = true;
    request({
        url: "/api/InvoiceVoucher/SaveStockMatchList?category=" + props.invoiceCategory,
        method: "post",
        data: matchTableData.value,
    }).then((res: IResponseModel<boolean>) => {
        isSaving = false;
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "保存成功！",
            });
            matchStocktShow.value = false;
            emit("save-stockt");
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存失败！",
            });
        }
    });
};
function setErrorRowStyle(data: { row: ICustomStockItem; rowIndex: number }): string {
    if (data.rowIndex === errorRowIndex.value) {
        return "highlight-red-border";
    }
    return "";
}
const loading = ref(false);
async function getStockList(invoiceIds: number[]) {
    matchStocktShow.value = true;
    errorRowIndex.value = -1;
    loading.value = true;
    await useAssistingAccountingStore().getAAListWithAcronym();
    request({
        url: "/api/InvoiceVoucher/StockMatchList",
        method: "post",
        data: invoiceIds,
    }).then((res: IResponseModel<ICustomStockItemApi[]>) => {
        loading.value = false;
        if (res.state === 1000) {
            matchTableData.value = res.data.map((item: ICustomStockItemApi, index: number) => {
                return {
                    ...item,
                    modelValue: item.stockAAEId ? item.stockAAEId : item.stockName,
                    rowId: index + 1,
                };
            });
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "出现错误,请刷新页面重试",
            });
        }
    });
}
defineExpose({
    getStockList,
});

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//模糊搜索
const showStockListOptions = ref<any[]>([]);
const showStockListbatchOptions = ref<any[]>([]);
watchEffect(() => { 
    showStockListOptions.value = JSON.parse(JSON.stringify(stockListOptions.value));
    showStockListbatchOptions.value = JSON.parse(JSON.stringify(stockListOptions.value));
});
function stockFilterMethod(value: string) {
    showStockListOptions.value = commonFilterMethod(value, stockListOptions.value, 'label');
    autoAddName.value = showStockListOptions.value.length === 0 ? value.trim() : "";
}
function batchstockFilterMethod(value: string) {
    showStockListbatchOptions.value = commonFilterMethod(value, stockListOptions.value, 'label');
}
</script>
<style lang="less" scoped>
.custom-confirm.match-stock-dialog {
    .el-dialog__body {
        padding: 0;
    }
    .match-stock-dialog-content {
        box-sizing: border-box;
        padding: 10px 20px 0;
        height: 500px;
        .match-stock-dialog-top {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 10px;
        }
        .match-stock-dialog-center {
            height: calc(100% - 50px);
            .match-stock-table {
                height: 100%;
                :deep(.el-table) {
                    height: 100%;
                    .el-scrollbar__view {
                        height: 100%;
                    }
                    th {
                        .cell {
                            white-space: nowrap;
                        }
                    }
                    tr.highlight-red-border td.el-table__cell {
                        border-top: 1px solid red;
                        border-bottom: 1px solid red;
                        &:first-child::before {
                            content: "";
                            position: absolute;
                            top: 0;
                            left: 1px;
                            width: 1px;
                            height: 100%;
                            background-color: red;
                            z-index: 999;
                        }
                        &:last-child::after {
                            content: "";
                            position: absolute;
                            top: 0;
                            right: 1px;
                            width: 0.5px;
                            height: 100%;
                            background-color: red;
                            z-index: 999;
                        }
                    }
                    .cell {
                        .select.goods-select-v2 {
                            width: 100%;
                            .el-icon {
                                right: 5px;
                                top: 2px;
                            }
                            .el-select-v2__wrapper.is-focused {
                                .el-icon {
                                    right: -5px;
                                }
                            }
                            .el-select-v2__placeholder {
                                margin-left: 8px;
                                width: calc(100% - 32px);
                            }
                        }
                    }
                }

                .click-show-item {
                    position: absolute;
                    top: 3px;
                    left: 4px;
                    width: calc(100% - 8px);
                    height: calc(100% - 6px);
                    display: flex;
                    align-items: center;

                    &.select-complete {
                        :deep(.el-select) {
                            width: 100%;

                            .el-input {
                                .el-input__inner {
                                    border: none;
                                }
                                &.is-focus {
                                    .el-input__wrapper {
                                        box-shadow: 0 0 0 1px var(--main-color) inset !important;
                                    }
                                }
                                .el-input__wrapper {
                                    &:hover {
                                        box-shadow: 0 0 0 1px var(--main-color) inset !important;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .certification-show-content {
        .certification-form {
            padding: 45px 60px 20px;

            :deep(.el-date-editor.el-input) {
                width: 160px;
            }
            :deep(.select.goods-select-v2) {
                width: 100%;
                .el-icon {
                    right: 5px;
                    top: 2px;
                }
                .el-select-v2__wrapper.is-focused {
                    .el-icon {
                        right: -5px;
                    }
                }
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.match-stock-tip.el-popper {
    span {
        display: block;
        font-size: 14px;
        line-height: 30px;
        &.example {
            margin: 10px 0;
        }
    }
    img {
        width: 100%;
        height: 100px;
        position: relative;
        left: -10px;
    }
}
</style>
