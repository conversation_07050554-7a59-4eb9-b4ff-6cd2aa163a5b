import { checkPermission } from "@/util/permission";
import type { IFileTableItem, OptionObj1, OptionObj2 } from "./types";

// 获取本地时间
export const getUploadDate = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1 + "").padStart(2, "0");
    const day = (date.getDate() + "").padStart(2, "0");
    const START_DATE = `${year}-${month}-01`;
    const END_DATE = `${year}-${month}-${day}`;
    return { START_DATE, END_DATE };
};

// 递归获取el-tree所点击节点的完整路径
export const getFilePath = (node: any, returnArray: string[]) => {
    returnArray.push(node.data.text);
    if (!Array.isArray(node.parent.data)) getFilePath(node.parent, returnArray);
    return returnArray;
};

// 获取文件图标
export const fileTypeImgPath = (i: string) => {
    const __i = parseInt(i);
    let __str = "";
    switch (__i) {
        case 0:
            __str = "folder";
            break;
        case 1010:
            __str = "word";
            break;
        case 1020:
            __str = "excel";
            break;
        case 1030:
            __str = "ppt";
            break;
        case 1040:
            __str = "default";
            break;
        case 2010:
            __str = "txt";
            break;
        case 2020:
            __str = "pdf";
            break;
        case 2030:
            __str = "zip";
            break;
        case 3010:
            __str = "img";
            break;
        case 4010:
            __str = "ofd_file";
            break;
        case 9010:
            __str = "default";
            break;
        default:
            __str = "";
    }
    return __str;
};

export const addFileTypeImgPath = (str: string) => {
    let __str = "";
    switch (str) {
        case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            __str = "word";
            break;
        case "application/msword":
            __str = "word";
            break;
        case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            __str = "excel";
            break;
        case "application/vnd.ms-excel":
            __str = "excel";
            break;
        case "application/vnd.ms-powerpoint":
            __str = "ppt";
            break;
        case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
            __str = "ppt";
            break;
        case "text/plain":
            __str = "txt";
            break;
        case "application/pdf":
            __str = "pdf";
            break;
        case "application/x-zip-compressed":
            __str = "zip";
            break;
        case "image/png":
            __str = "img";
            break;
        case "image/jpeg":
            __str = "img";
            break;
        default:
            __str = "default";
    }
    return __str;
};

// 获取文件类别
export const formatFileCategory = (i: number) => {
    // const _i = parseInt(i);

    let _str = "";
    switch (i) {
        case 0:
            _str = "凭证附件";
            break;
        case 1:
            _str = "银行回单";
            break;
        case 2:
            _str = "电子发票";
            break;
        case 3:
            _str = "其他单据";
            break;
    }
    return _str;
};

// 获取票据类型
export const formatDocumentType = (i: number) => {
    // const _i = parseInt(i);
    let _str = "";
    switch (i) {
        case 0:
            _str = "其他";
            break;
        case 1:
            _str = "电子普票";
            break;
        case 2:
            _str = "电子专票";
            break;
        case 3:
            _str = "银行回单";
            break;
        case 4:
            _str = "发票";
            break;
        case 5:
            _str = "合同";
            break;
        case 6:
            _str = "库存单据";
            break;
        case 7:
            _str = "证照";
            break;
        case 8:
            _str = "凭证附件";
            break;
        case 9:
            _str = "全电普票";
            break;
        case 10:
            _str = "全电专票";
            break;
    }
    return _str;
};

// 获取上传方式
export const formatUploadType = (i: number) => {
    // const _i = parseInt(i);
    let _str = "";
    switch (i) {
        case 0:
            _str = "财务系统";
            break;
        case 1:
            _str = "收票宝";
            break;
        case 2:
            _str = "一键取票";
            break;
    }
    return _str;
};

// 获取检验状态
export const formatCheckState = (i: number) => {
    // const _i = parseInt(i);
    let _str = "";
    switch (i) {
        case 0:
            _str = "非电子发票，无需查验";
            break;
        case 1:
            _str = "未查验";
            break;
        case 2:
            _str = "查验中";
            break;
        case 3:
            _str = "已查验";
            break;
        case 4:
            _str = "查验失败";
            break;
        case 5:
            _str = "已查验，发票号码重复";
            break;
        case 6:
            _str = "已查验，发票抬头与公司名称不符";
            break;
        case 7:
            _str = "发票开具24小时内，不支持查验";
            break;
        case 8:
            _str = "该发票类型暂不支持查验";
            break;
        case 9:
            _str = "未查验到该发票";
            break;
        case 10:
            _str = "目前只支持五年内的发票查验";
            break;
        case 11:
            _str = "税局查验服务器出小差了，请稍后再试";
            break;
        case 12:
            _str = "超过该张票当天查验次数，请明日再试";
            break;
        case 100:
            _str = "一键取票获取，无需查验";
            break;
    }

    return _str;
};

// 获取文件状态
export const formatFileState = (i: number) => {
    // const _i = parseInt(i);
    let _str = "";
    switch (i) {
        case 0:
            _str = "未审核";
            break;
        case 1:
            _str = "已审核";
            break;
        case 2:
            _str = "驳回";
            break;
    }

    return _str;
};

// 去除null
export const removeNull = (text: any) => {
    return text == "null" || text == null ? "" : text;
};

// // 文件路径修正
export const formatPath = (path: string) => {
    const pathSplit = path.split("/");
    pathSplit.splice(0, 4);
    pathSplit.pop();
    pathSplit.unshift("所有文件");
    return pathSplit.join(">");
};

// {value: '25', selected: true, label: '2023年1月'}
export const convertOptionsString1 = (str: string): OptionObj1[] => {
    const optionRegex = /<option.*?value='(\d+)'.*?(selected=['"]?([^'"]*)['"]?)?.*?>(.*?)<\/option>/g;
    const matches = [...str.matchAll(optionRegex)];
    const result: OptionObj1[] = [];
    for (const match of matches) {
        const optionObj: OptionObj1 = {
            value: match[1],
            selected: match[3] === "selected" || match[3] === "true",
            label: match[4],
        };
        result.push(optionObj);
    }
    return result;
};

// {value: '1',  label: '记-1'}
export const convertOptionsString2 = (str: string): OptionObj2[] => {
    const optionRegex = /<option.*?value='(\d+)'.*?>(.*?)<\/option>/g;
    const matches = [...str.matchAll(optionRegex)];
    const result: OptionObj2[] = [];
    for (const match of matches) {
        const optionObj: OptionObj2 = {
            value: match[1],
            label: match[2],
        };
        result.push(optionObj);
    }
    return result;
};

export const checkFilesState = (list: IFileTableItem[]) => {
    let __result = false;
    for (let i = 0; i < list.length; i++) {
        if (list[i].File_State == 0) {
            __result = true;
            break;
        }
    }
    return __result;
};
export const hasAttachOfCheckFiles = (list: IFileTableItem[]) => {
    let attach = false;
    for (let i = 0; i < list.length; i++) {
        if (list[i].IsAttachVouch) {
            attach = true;
            break;
        }
    }
    return attach;
};
export const checkFileUpload = (list: IFileTableItem[], flag: string | number) => {
    let upload = false;
    for (let i = 0; i < list.length; i++) {
        if (list[i].Upload_Type == flag) {
            upload = true;
            break;
        }
    }
    return upload;
};
export const hasJournalOfCheckFiles = (list: IFileTableItem[]) => {
    const files = getCheckFiles(list);
    const fileNames = list.map((item) => item.File_Name);
    let attachRes = "";
    for (let i = 0; i < files.length; i++) {
        if (files[i].attachJournal) attachRes += fileNames[i] + ";";
    }
    if (attachRes.length > 1) attachRes = attachRes.substring(0, attachRes.length - 1);
    return attachRes;
};
export const getCheckFiles = (list: IFileTableItem[]) => {
    const __files: any[] = [];
    list.forEach((item) => {
        __files.push({
            id: item.File_Id,
            flag: item.Flag,
            attach: item.IsAttachVouch,
            attachJournal: item.IsAttachJournal,
        });
    });
    return __files;
};
export const allAttachOfCheckFiles = (list: IFileTableItem[]) => {
    const files = getCheckFiles(list);
    let attach = true;
    for (let i = 0; i < files.length; i++) {
        if (!files[i].attach) {
            attach = false;
            break;
        }
    }
    return attach;
};
export const debounce = (fn: Function, delay: number = 3000, immediate: boolean = true) => {
    let timer: any = null;
    const delayTime = delay;
    if (timer) {
        clearTimeout(timer);
    }
    return function () {
        if (timer) {
            return;
        }
        if (immediate) {
            const bool = !timer;
            timer = setTimeout(() => (timer = null), delayTime);
            return bool && fn();
        }
    };
};
export function handleSort(val: number, name: string, data:IFileTableItem[], originData:IFileTableItem[] ) {
    if (!data.length) return;
    if (val === 0) {
        data.splice(0, data.length, ...originData);
        return;
    }
    data.sort((a, b) => {
        if (name === "File_Name") {
            return a["File_Name"].localeCompare(b["File_Name"], "zh");
        }
        if (name === "File_Category") {
            return formatFileCategory(a.File_Category ?? "").localeCompare(formatFileCategory(b.File_Category ?? ""), "zh");
        }
        if(name === "Document_Type") {
            return formatDocumentType(a.Document_Type ?? "").localeCompare(formatDocumentType(b.Document_Type ?? ""), "zh");
        }
        if (name === "Created_By") {
            return a["Created_By"].localeCompare(b["Created_By"], "zh");
        }
        if (name === "Upload_Type") {
            return formatUploadType(a.Upload_Type ?? "").localeCompare(formatUploadType(b.Upload_Type ?? ""), "zh");
        }
        if (name === "Created_Date_Str") {
            return new Date(a.Created_Date_Str).getTime() - new Date(b.Created_Date_Str).getTime();
        }
        if (name === "File_Size") {
            return a.File_Size - b.File_Size;
        }
        if (name === "RelatedDocumentTypes") {
            return formatDounmentType(a.RelatedDocumentTypes ?? []).localeCompare(formatDounmentType(b.RelatedDocumentTypes ?? []), "zh");
        }
        if (name === "relateDocumentNumber") {
            return formatBillNoFn(a.RelatedDocumentTypes, a.Source).localeCompare(formatBillNoFn(b.RelatedDocumentTypes, b.Source), "zh");
        }
        if (name === "VoucherGroupStr") {
            if (a.P_Id === b.P_Id) {
                return Number(a.V_Id) - Number(b.V_Id);
            } else {
                return Number(a.P_Id) - Number(b.P_Id)
            }
            // return a["VoucherGroupStr"].localeCompare(b["VoucherGroupStr"], "zh");
        }
        if (name === "Matters") {
            return a["Matters"].localeCompare(b["Matters"], "zh");
        }
        if (name === "Check_State") {
            return formatCheckState(a.Check_State ?? "").localeCompare(formatCheckState(b.Check_State ?? ""), "zh");
        }
        if (name === "File_State") {
            return formatFileState(a.File_State ?? "").localeCompare(formatFileState(b.File_State ?? ""), "zh");
        }
        return 0;
    })
    if (val < 0) {
        data.reverse();
    }
    return data;
}
export const characterSortName = (a: IFileTableItem, b: IFileTableItem): number => {
    return a["File_Name"].localeCompare(b["File_Name"], "zh");
};
export const characterSortType = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatFileCategory(a.File_Category ?? "").localeCompare(formatFileCategory(b.File_Category ?? ""), "zh");
};
export const characterSortDocumentType = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatDocumentType(a.Document_Type ?? "").localeCompare(formatDocumentType(b.Document_Type ?? ""), "zh");
};
export const characterSortCreated = (a: IFileTableItem, b: IFileTableItem): number => {
    return a["Created_By"].localeCompare(b["Created_By"], "zh");
};
export const characterSortUpload = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatUploadType(a.Upload_Type ?? "").localeCompare(formatUploadType(b.Upload_Type ?? ""), "zh");
};

export const characterSortVoucher = (a: IFileTableItem, b: IFileTableItem): number => {
    return a["VoucherGroupStr"].localeCompare(b["VoucherGroupStr"], "zh");
};

export const characterSortMatters = (a: IFileTableItem, b: IFileTableItem): number => {
    return a["Matters"].localeCompare(b["Matters"], "zh");
};
export const characterSortCheck = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatCheckState(a.Check_State ?? "").localeCompare(formatCheckState(b.Check_State ?? ""), "zh");
};
export const characterSortFileState = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatFileState(a.File_State ?? "").localeCompare(formatFileState(b.File_State ?? ""), "zh");
};
export const characterSortBillState = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatDounmentType(a.RelatedDocumentTypes ?? "").localeCompare(formatDounmentType(b.RelatedDocumentTypes ?? ""), "zh");
};
export const characterSortBillNoState = (a: IFileTableItem, b: IFileTableItem): number => {
    return formatBillNoFn(a.RelatedDocumentTypes, a.Source).localeCompare(formatBillNoFn(b.RelatedDocumentTypes, b.Source), "zh");
};
function formatBillNoFn(RelatedDocumentType: ModuleType[], Source: any[]) {
    let billNo = "";
    if (!RelatedDocumentType.length || !Source.length) return billNo;
    switch (RelatedDocumentType[0]) {
        case ModuleType.SalesInvoice:
        case ModuleType.PurchaseInvoice:
            billNo = Source[0]?.invNum || "";
            break;
        case ModuleType.CashJournal:
        case ModuleType.DepositJournal:
            billNo = Source.map((item) => item.name).join("、");
            break;
        case ModuleType.Draft:
            billNo = Source[0]?.draftNo || "";
            break;
        case ModuleType.Salary:
            billNo = Source[0]?.source || "";
            break;
        case ModuleType.FixedAssets:
            billNo = Source[0]?.faNum || "";
            break;
        default:
            billNo = "";
            break;
    }
    return billNo;
}
export function getBillNoKey(RelatedDocumentType: ModuleType[], index = 0) {
    let billNo = "";
    switch (RelatedDocumentType[index]) {
        case ModuleType.SalesInvoice:
        case ModuleType.PurchaseInvoice:
            billNo = "invNum";
            break;
        case ModuleType.CashJournal:
        case ModuleType.DepositJournal:
            billNo = "name";
            break;
        case ModuleType.Draft:
            billNo = "draftNo";
            break;
        case ModuleType.Salary:
            billNo = "source";
            break;
        case ModuleType.FixedAssets:
            billNo = "faNum";
            break;
        default:
            billNo = "";
            break;
    }
    return billNo;
}

export enum ModuleType {
    Default = 0,
    SalesInvoice = 1,
    PurchaseInvoice = 2,
    CashJournal = 3,
    DepositJournal = 4,
    Draft = 5,
    Salary = 6,
    FixedAssets = 7,
}
export function formatDounmentType(type: number[]) {
    if (!type || !type.length) return "";
    return type.map((item) => getDocumentTypeName(item)).join("、");
}
function getDocumentTypeName(type: number) {
    let str = "";
    switch (type) {
        case ModuleType.CashJournal:
            str = "现金日记账";
            break;
        case ModuleType.DepositJournal:
            str = "银行日记账";
            break;
        case ModuleType.Draft:
            str = "票据";
            break;
        case ModuleType.SalesInvoice:
            str = "销项发票";
            break;
        case ModuleType.PurchaseInvoice:
            str = "进项发票";
            break;
        case ModuleType.Salary:
            str = "工资";
            break;
        case ModuleType.FixedAssets:
            str = "资产";
            break;
    }
    return str;
}
export function checkHasBillPermission(documentType: ModuleType[], index = 0) {
    let hasPermission = true;
    switch (documentType[index]) {
        case ModuleType.SalesInvoice:
            hasPermission = checkPermission(["invoice-output-canview"]);
            break;
        case ModuleType.PurchaseInvoice:
            hasPermission = checkPermission(["invoice-input-canview"]);
            break;
        case ModuleType.CashJournal:
            hasPermission = checkPermission(["cashjournal-canview"]);
            break;
        case ModuleType.DepositJournal:
            hasPermission = checkPermission(["depositjournal-canview"]);
            break;
        case ModuleType.Draft:
            hasPermission = checkPermission(["draft-canview"]);
            break;
        case ModuleType.Salary:
            hasPermission = checkPermission(["salarymanage-canview"]);
            break;
        case ModuleType.FixedAssets:
            hasPermission = checkPermission(["fixedassets-card-canview"]);
            break;
        default:
            hasPermission = true;
            break;
    }
    return hasPermission;
}
export const uploadTypeOptions = [
    {id: 0, name: "财务系统"},
    {id: 1, name: "收票宝"},
    {id: 2, name: "一键取票"},
];
export const documentTypeOptions = [
    {id: 10, name: "全电专票"},
    {id: 9, name: "全电普票"},
    {id: 2, name: "电子专票"},
    {id: 1, name: "电子普票"},
    {id: 8, name: "凭证附件"},
    {id: 3, name: "银行回单"},
    {id: 4, name: "发票"},
    {id: 5, name: "合同"},
    {id: 6, name: "库存单据"},
    {id: 7, name: "证照"},
    {id: 0, name: "其他"},
];
export const checkStatesOptions = [
    {id: 1, name: "未查验"},
    {id: 2, name: "查验中"},
    {id: 3, name: "已查验"},
    {id: 4, name: "查验失败"},
    {id: 5, name: "已查验，发票号码重复"},
    {id: 6, name: "已查验，发票抬头与公司名称不符"},
    {id: 0, name: "非电子发票，无需查验"},
    {id: 7, name: "发票开具24小时内，不支持查验"},
    {id: 8, name: "该发票类型暂不支持查验"},
    {id: 9, name: "未查验到该发票"},
    {id: 10, name: "目前只支持五年内的发票查验"},
    {id: 11, name: "税局查验服务器出小差了，请稍后再试"},
    {id: 12, name: "超过该张票当天查验次数，请明日再试"},
    {id: 100, name: "一键取票获取，无需查验"},
];
export const fileCategoryOptions = [
    {id: 0, name: "凭证附件"},
    {id: 1, name: "银行回单"},
    {id: 2, name: "电子发票"},
    {id: 3, name: "其他单据"},
];
export const fileStateList = [
    {value: -1, name: "全部"}, 
    {value: 0, name: "未审核"}, 
    {value: 1, name: "已审核"}
];
export const voucherStateList = [
    {value: -1, name: "全部"}, 
    {value: 0, name: "否"}, 
    {value: 1, name: "是"}
];