<template>
    <el-dialog v-model="bodySettingShow" title="表体设置" :width="440" class="dialogDrag">
        <div class="setting-content" v-dialogDrag>
            <div class="setting-item">
                <el-checkbox label="科目编码和名称合并显示" v-model="bodySetting.isMergeSubject"></el-checkbox>
            </div>
            <div class="setting-item">
                <el-checkbox label="科目名称只显示末级" v-model="bodySetting.isShowLeaf"></el-checkbox>
            </div>
            <div class="setting-item">
                <el-checkbox label="辅助核算和科目合并显示" v-model="bodySetting.isMergeAAE"></el-checkbox>
            </div>
            <div class="setting-item">
                <el-checkbox label="不打印辅助核算编码" v-model="bodySetting.isHideAAECode"></el-checkbox>
            </div>
            <div class="setting-item">
                <el-checkbox label="不打印现金流辅助核算" v-model="bodySetting.isHideAAECashflow"></el-checkbox>
            </div>
            <div class="setting-item">
                <el-checkbox label="外币核算和摘要合并打印" v-model="bodySetting.isShowFC"></el-checkbox>
            </div>
            <div class="setting-item">
                <el-checkbox label="数量核算和摘要合并打印" v-model="bodySetting.isShowQuantity"></el-checkbox>
            </div>
        </div>
        <div class="setting-tip">
            <div class="tip-title">
                <img src="@/assets/Icons/hint.png" />
                两个字段合并的理解
            </div>
            <div class="tip">当表体中有这两个字段时则合并到一个单元格打印</div>
            <div class="tip">当表体中只有其中一个字段时则只打印这一个字段</div>
        </div>
        <div class="buttons borderTop">
            <a class="button solid-button mr-10" @click="updateSetting">确定</a>
            <a  class="button" @click="bodySettingShow = false">取消</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { getGlobalLodash } from "@/util/lodash";

const _ = getGlobalLodash();
const props = defineProps<{
    bodySetCheck: any;
}>();
const emit = defineEmits<{
    (event: "update", args: any): void;
}>();
const bodySettingShow = defineModel<boolean>("bodySettingShow");
const bodySetting = ref(_.cloneDeep(props.bodySetCheck));
// const bodySetting = computed({
//     get: () => {
//         return _.cloneDeep(props.bodySetCheck);
//     },
//     set: (val) =>  val
// });
function updateSetting() {
    bodySettingShow.value = false;
    emit("update", bodySetting.value);
}
watch(bodySettingShow, (val) => {
    if(val) {
        bodySetting.value = _.cloneDeep(props.bodySetCheck);
    }
});
</script>

<style scoped lang="less">
.setting-content {
    padding:10px 0;
    .setting-item {
        padding-left: 100px;
        text-align: left;
    }
}
.setting-tip{
    padding-left:100px;
    padding-bottom: 10px;
    text-align: left;
    .tip-title {
        margin-left: -12px;
        padding-bottom: 5px;
        img{
            width:14px;
            height:14px;
            
        }
        // font-size: 14px;
        // font-weight: bold;
        // margin-bottom: 10px;
    }
    .tip {
        padding-top:6px;
        font-size: 13px;
        color: #999;
    }
}
</style>
