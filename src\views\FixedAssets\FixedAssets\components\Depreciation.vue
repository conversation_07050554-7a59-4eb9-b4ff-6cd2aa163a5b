<template>
    <div class="depreciation" style="background-color: var(--white)">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <div style="color: #ff8200">
                    亲，点击“计提折旧”即可生成折旧摊销明细表，点击“生成凭证”即可生成折旧凭证！灰色表示不能操作，如有疑问，请将鼠标移至灰字上面，查看提示操作。
                </div>
            </div>
        </div>
        <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
            <Table
                :data="tableData"
                :columns="columns"
                :empty-text="emptyText"
                :pageIsShow="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :current-page="paginationData.currentPage"
                :row-class-name="handleLightRow"
                @size-change="handleChangePlus"
                @current-change="handleCurrentChange"
                @refresh="handleRerefresh"
                :scrollbar-show="true"
                :tableName="setModule"
            >
                <template #vgname>
                    <el-table-column 
                        label="折旧凭证" 
                        align="left" 
                        headerAlign="left"
                        prop="vgname"
                        :width="getColumnWidth(setModule, 'vgname')"
                    >
                        <template #default="scope">
                            <template v-if="scope.row.voucher_detail !== '无需生成凭证'">
                                <a
                                    v-if="checkPermission(['fixedassets-card-cancreatevoucher'])"
                                    class="link"
                                    @click="ShowDetail(scope.row)"
                                >
                                    {{ scope.row.voucher_detail.replace(/<[^<>]+>/g, "") }}
                                </a>
                                <span v-else> {{ scope.row.voucher_detail.replace(/<[^<>]+>/g, "") }}</span>
                            </template>
                            <span v-else> {{ scope.row.voucher_detail.replace(/<[^<>]+>/g, "") }}</span>
                        </template>
                    </el-table-column>
                </template>
                <template #operation>
                    <el-table-column class-name='operationCol' label="操作" align="left" headerAlign="left" :resizable="false">
                        <template #default="scope">
                            <template v-if="scope.row.option.includes('计提折旧')">
                                <a
                                    v-permission="['fixedassets-card-candepreciation']"
                                    class="link options-link-left"
                                    :pid="scope.row.p_id"
                                    @click="sumDepreciation(scope.row.p_id)"
                                    >计提折旧</a
                                >
                                <el-tooltip 
                                    v-if="[scope.row.change_detail, scope.row.assets_detail,scope.row.depreciation_detail].every(item => item === '无数据')"
                                    class="tooltip"
                                    effect="light"
                                    popperClass="el-option-tool-tip"
                                    placement="left"
                                    >
                                    <template #content>
                                        <div class="tips">
                                            <div class="tips-text">1.计提折旧是将资产成本分摊至各会计期间</div>
                                            <div class="tips-text">2.计提折旧是为了确认当期处理，即便本月无资产数据，点击 “计提折旧” 可推进至下一会计期间，保障后续资产操作（如新增资产、变更、折旧等）正常进行</div>
                                            <div class="tips-text">3.计提折旧后的期间即为最新的卡片录入期间</div>
                                        </div>
                                    </template>
                                    <img
                                        class="img-question"
                                        src="@/assets/Icons/question.png"
                                        style="height: 16px; margin-left: 3px" />
                                </el-tooltip>
                            </template>
                            <template v-else>
                                <template v-if="scope.row.option.includes('取消折旧')">
                                    <a
                                        v-permission="['fixedassets-card-candepreciation']"
                                        class="link options-link-left"
                                        :class="judgeDisabled(scope.row.option, 0, 'class')"
                                        :pid="scope.row.p_id"
                                        :title="judgeDisabled(scope.row.option, 0, 'title')"
                                        @click="judgeDisabled(scope.row.option, 0, 'class') ? () => {} : delDepreciation(scope.row.p_id)"
                                        >取消折旧</a
                                    >
                                </template>
                                <template v-if="scope.row.option.includes('生成凭证')">
                                    <a
                                        v-permission="['fixedassets-card-cancreatevoucher']"
                                        class="link options-link-left"
                                        :class="judgeDisabled(scope.row.option, 1, 'class')"
                                        :title="
                                            judgeDisabled(scope.row.option, 1, 'title')
                                        "
                                        :pid="scope.row.p_id"
                                        @click="judgeDisabled(scope.row.option, 1, 'class') ? ()=>{} : getDepreVoucher(scope.row.p_id)"
                                        >生成凭证</a
                                    >
                                </template>
                            </template>
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
    </div>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableData, IDepreciation } from "../types";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { ElAlert } from "@/util/confirm";
import { ref, watch, onMounted } from "vue";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import { getYearMonthLastDay } from "../utils";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { handleZoomChange } from "../utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const setModule = "FixedassetsDepreciation";
const props = defineProps({
    finishflag: {
        type: String,
        default: "",
    },
});
const emits = defineEmits(["handleActiveName", "generateVoucher", "reloadFixedAssetsList"]);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination("Depreciation", true);
let checkflag = true;
const handleChangePlus = (val: any) => {
    checkflag = true;
    handleSizeChange(val);
};
const tableMaxHeight = ref("");
const columns = ref<Array<IColumnProps>>([
    { label: "会计期间", prop: "period", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'period') },
    { label: "新增资产", prop: "assets_detail", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'assets_detail') },
    { label: "变更记录", prop: "change_detail", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'change_detail') },
    { label: "计提折旧", prop: "depreciation_detail", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'depreciation_detail') },
    { slot: "vgname" },
    { slot: "operation" },
]);

const tableData = ref<any[]>([]);
const emptyText = ref(" ");
const loading = ref<boolean>(false);
const trialStatusStore = useTrialStatusStore();
const handleZoomChangeList = () => {
    tableMaxHeight.value = handleZoomChange();
};
onMounted(() => {
    handleZoomChangeList();
    window.addEventListener("resize", handleZoomChangeList);
});
const getTableData = (flag?: boolean) => {
    loading.value = true;
    let hasNewAssetWithoutVoucher = false;
    let hasChangeWithoutVoucher = false;
    request({
        url: `/api/Depreciation/PagingList?PageIndex=${paginationData.currentPage}&PageSize=${paginationData.pageSize}`,
    })
        .then((res: IResponseModel<{ data: IDepreciation[]; count: number }>) => {
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
            flag && (sumDepreciationFlag = false);
            if (res.data != undefined && res.data.data != undefined && res.data.count >= 1) {
                let currentRow = res.data.data[0];
                if (currentRow.assets_detail === "有数据，已生成部分凭证" || currentRow.assets_detail === "有数据，未生成凭证") {
                    hasNewAssetWithoutVoucher = true;
                } else {
                    hasNewAssetWithoutVoucher = false;
                }
                if (currentRow.change_detail === "有数据，已生成部分凭证" || currentRow.change_detail === "有数据，未生成凭证") {
                    hasChangeWithoutVoucher = true;
                } else {
                    hasChangeWithoutVoucher = false;
                }
            }

            if (res.data.count === 0) {
                emptyText.value = "暂无数据";
            }
            if (checkflag || hasChangeWithoutVoucher || hasNewAssetWithoutVoucher) {
                showGenVoucherTrans(hasNewAssetWithoutVoucher, hasChangeWithoutVoucher);
            }
        })
        .finally(() => {
            loading.value = false;
        });
};

function judgeDisabled(option: string, order: number, type: string) {
    if (type === "class") {
        if (option.includes("</a><a")) {
            let optionArr = option.split("</a><a");
            return optionArr[order].includes("disabled") ? "disabled" : "";
        } else {
            return option.includes("disabled") ? "disabled" : "";
        }
    } else {
        let regex = /title='(.*?)'/;
        if (option.includes("</a><a")) {
            let optionArr = option.split("</a><a");
            let match = regex.exec(optionArr[order]);
            return match ? match[1] : "";
        } else {
            return regex.exec(option) ? regex.exec(option)![1] : "";
        }
    }
}
let sumDepreciationFlag = false;
const sumDepreciation = (pid: number) => {
    if (sumDepreciationFlag) return;
    if (!checkPermission(["fixedassets-card-candepreciation"])) return;
    if (props.finishflag != "1") {
        ElAlert({message: "资产初始化未完成，您不能计提折旧哦！", hideCancel: true}).then(() => {
            emits("handleActiveName", "initFixedassets");
        });
    }
    sumDepreciationFlag = true;
    request({
        url: `/api/Depreciation?pid=${pid}`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.data == "Success") {
            ElNotify({
                type: "success",
                message: "计提折旧成功！",
            });
            window.dispatchEvent(new CustomEvent("depreciation"));
            emits("reloadFixedAssetsList");
            getTableData(true);
        } else if (res.data == "NONE") {
            ElNotify({
                type: "success",
                message: "计提折旧成功，本期无需生成凭证！",
            });
            window.dispatchEvent(new CustomEvent("depreciation"));
            emits("reloadFixedAssetsList");
            getTableData(true);
        } else if (res.data == "CanNot") {
            emits("reloadFixedAssetsList");
            getTableData(true);
        } else if (res.data === "InitialFailed") {
            sumDepreciationFlag = false;
            ElAlert({message: "资产初始化未完成，您不能计提折旧哦！"}).then(() => {
                emits("handleActiveName", "initFixedassets");
            });
        } else {
            sumDepreciationFlag = false;
            ElNotify({
                type: "warning",
                message: "计提折旧失败！",
            });
        }
    });
};

const delDepreciation = (pid: number) => {
    if (!checkPermission(["fixedassets-card-candepreciation"])) return;
    request({
        url: `/api/Depreciation?pid=${pid}`,
        method: "delete",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "取消折旧成功！",
            });
            window.dispatchEvent(new CustomEvent("depreciation"));
            emits("reloadFixedAssetsList");
            getTableData();
        }
    });
};

const getDepreVoucher = async (pid: number) => {
    if (!checkPermission(["fixedassets-card-cancreatevoucher"])) return;
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    request({
        url: `/api/FAVoucher/GenerateDepreciationVoucher?pid=${pid}`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            if (res.data == "Success") {
                ElNotify({
                    type: "success",
                    message: "计提折旧成功，折旧为0，无需生成凭证！",
                });
            } else {
                if (res.data != "") {
                    const date = tableData.value.find((item) => item.p_id === pid)?.period;
                    const { year, month, lastDay } = getYearMonthLastDay(date);
                    emits("generateVoucher", res.data, year + "-" + (month < 10 ? "0" + month : month) + "-" + lastDay);
                } else {
                    ElNotify({
                        type: "error",
                        message: "亲，获取凭证失败啦！",
                    });
                }
            }
        } else {
            ElNotify({
                type: "error",
                message: res.msg,
            });
        }
    });
};

const ShowDetail = (row: ITableData) => {
    emits("generateVoucher", "", "", row.vp_id, row.vv_id);
};

const showGenVoucherTrans = (hasNewAssetWithoutVoucher: boolean, hasChangeWithoutVoucher: boolean) => {
    let message = "";
    if (hasNewAssetWithoutVoucher) {
        message = "资产变动还有数据未生成凭证哦，是否立即前往处理？";
    } else if (hasChangeWithoutVoucher) {
        message = "变更记录还有数据未生成凭证哦，是否立即前往处理？";
    }
    if (hasNewAssetWithoutVoucher || hasChangeWithoutVoucher) {
        ElAlert({message: message}).then((r: boolean) => {
            if (r) {
                emits("handleActiveName", "changeRecord");
            }
        });
    }
    checkflag = false;
};
//凭证列表跳转对应折旧高亮

const handleLightRow = (data: { row: ITableData; rowIndex: number }) => {
    return data.row.p_id === lightVPid.value ? "hight-row" : "";
};

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableData();
});
//凭证列表联查对应折旧
const lightVPid = ref(-1);
const getDepreciationPageIndex = (dpid: any) => {
    lightVPid.value = Number(dpid);
    request({
        url: `/api/Depreciation/DepreciationPageIndex?pageSize=${paginationData.pageSize}&pid=${dpid}`,
        method: "get",
    })
        .then((res: IResponseModel<number>) => {
            if (res.state === 1000) {
                if (paginationData.currentPage !== res.data + 1) {
                    handleCurrentChange({ pageNum: res.data + 1 });
                } else {
                    getTableData();
                }
            }
        })
        .catch((err) => {
            console.log(err);
            getTableData();
        });
};

defineExpose({ getTableData, getDepreciationPageIndex });
</script>

<style scoped lang="less">
:deep(.options-link-left) {
    display: inline-block;
    width: 65px;
    height: 17px;
    text-align: right;
}
:deep(.el-table__row) {
    &.current-row {
        .link {
            text-decoration: underline;
        }
    }
    &.hight-row {
        .el-table__cell {
            background-color: var(--el-table-row-hover-bg-color) !important;
        }
    }
    .operationCol{
        .cell{
            line-height: var(--table-body-height);
        }
    }
}
</style>
