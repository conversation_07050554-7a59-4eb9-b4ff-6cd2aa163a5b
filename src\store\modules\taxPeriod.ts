import { defineStore } from "pinia"
import { useNow } from "@vueuse/core"
import { useBasicInfoStore } from "./basicInfo"
import { EnumTaxpayerLevel } from "@/views/InvoiceAccess/constants"
import { getQuarterInfo } from "@/views/InvoiceAccess/utils"
import dayjs from "dayjs"

export const useTaxPeriodStore = defineStore("taxPeriod", () => {
  const now = useNow()

  // 计算上月日期
  const lastMonth = computed(() => {
    const date = new Date(now.value)
    date.setMonth(date.getMonth() - 1)
    return date.toISOString().slice(0, 7)
  })

  // 当前税期
  const currentPeriod = ref(lastMonth.value)
  const currentPeriodId = computed(() => {
    return currentPeriod.value.replace(/-/g, "")
  })

  // 可选日期范围验证
  const disabledDate = (time: Date) => {
    return time.getTime() < new Date("2022-01").getTime() || time.getTime() > new Date(lastMonth.value).getTime()
  }

  // 更新税期方法
  const updatePeriod = (period: string) => {
    currentPeriod.value = period
  }

  const isSmallTaxLevel = computed(() => {
    return useBasicInfoStore().basicInfo.taxType === EnumTaxpayerLevel.Small
  })

  // 操作时间
  const searchStartDate = computed(() => {
    if (isSmallTaxLevel.value) {
      // 返回税款所属期对应季下的1号
      return getQuarterInfo(currentPeriod.value).startDate
    } else {
      return dayjs(currentPeriod.value).startOf("month").format("YYYY-MM-DD")
    }
  })

  const searchEndDate = computed(() => {
    if (isSmallTaxLevel.value) {
      // 返回税款所属期对应季下的最后一天
      return getQuarterInfo(currentPeriod.value).endDate
    } else {
      return dayjs(currentPeriod.value).endOf("month").format("YYYY-MM-DD")
    }
  })

  return {
    currentPeriod,
    currentPeriodId,
    lastMonth,
    disabledDate,
    updatePeriod,
    searchStartDate,
    searchEndDate,
  }
})
