import { defineStore } from "pinia"
import { useNow } from "@vueuse/core"

export const useTaxPeriodStore = defineStore("taxPeriod", () => {
  const now = useNow()

  // 计算上月日期
  const lastMonth = computed(() => {
    const date = new Date(now.value)
    date.setMonth(date.getMonth() - 1)
    return date.toISOString().slice(0, 7)
  })

  // 当前税期
  const currentPeriod = ref(lastMonth.value)
  const currentPeriodId = computed(() => {
    return currentPeriod.value.replace(/-/g, "")
  })

  // 可选日期范围验证
  const disabledDate = (time: Date) => {
    return time.getTime() < new Date("2022-01").getTime() || time.getTime() > new Date(lastMonth.value).getTime()
  }

  // 更新税期方法
  const updatePeriod = (period: string) => {
    currentPeriod.value = period
  }

  return {
    currentPeriod,
    currentPeriodId,
    lastMonth,
    disabledDate,
    updatePeriod,
  }
})
