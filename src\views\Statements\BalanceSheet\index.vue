<template>
    <div class="content">
        <div class="title">资产负债表</div>
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef"></PaginationPeriodPicker>
                            <CheckOutTooltip v-if="periodIsCheckOut"></CheckOutTooltip>
                            <ErpRefreshButton></ErpRefreshButton>
                            <div v-show="causeOfUnevenness" class="uneven-prompt">
                                <UnevenTooltip
                                    :content="causeOfUnevenness"
                                    :max-width="500"
                                    :line-clamp="1"
                                    :dynamicWidth="true"
                                    :isHtmlContent="!causeOfFlag">
                                    <img v-show="isErp" src="@/assets/Erp/tip-erp.png" alt="" class="uneven-prompt-icon ml-10" />
                                    <span :class="['highlight-red uneven-prompt-text', { 'pl-10': isErp }]" v-if="causeOfFlag">{{
                                        causeOfUnevenness
                                    }}</span>
                                    <span :class="['highlight-red uneven-prompt-text', { 'pl-10': isErp }]" v-else>
                                        不平原因：损益类科目还有余额，请点击<span
                                            class="link"
                                            @click="globalWindowOpenPage('/Checkout/Checkout', '期末结转')"
                                            >期末结转</span
                                        >继续结转损益
                                    </span>
                                </UnevenTooltip>
                            </div>
                        </div>
                        <div class="main-tool-right">
                            <div v-if="[1, 2, 3].includes(accountStandard)" class="mr-10">
                                <el-checkbox
                                    v-model="searchInfo.classification"
                                    label="开启重分类"
                                    @change="changeClassification"></el-checkbox>
                                <img
                                    src="@/assets/Settings/question.png"
                                    style="height: 18px; cursor: pointer; margin-left: 7px"
                                    @click="showClassificationDialog = true" />
                            </div>
                            <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['balancesheet-canprint']">
                                <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                <li @click="handlePrint(1)">批量打印</li>
                                <li @click="handlePrint(2)">打印设置</li>
                            </Dropdown>
                            <Dropdown :btnTxt="'导出'" class="mr-10" :downlistWidth="102" v-permission="['balancesheet-canexport']">
                                <li @click="handleExport(0)">当前报表数据</li>
                                <li @click="handleExport(1)">批量导出</li>
                            </Dropdown>
                            <a
                                class="button mr-10 solid-button"
                                v-show="resetFormulaButton"
                                v-permission="['balancesheet-canedit']"
                                @click="resetFormula"
                                >重置公式</a
                            >
                            <div
                                class="button large-1 dropdown"
                                v-if="
                                    checkPermission(['balancesheet-canshare']) &&
                                    checkPermission(['bosspermissions-canview']) &&
                                    !isHideBarcode &&
                                    !isHideBossPermission
                                ">
                                <span class="ml-10">报表分享</span>
                                <div class="downlist">
                                    <div class="downlist-buttons large" :style="{ width: isErp ? '108px' : '' }">
                                        <div
                                            class="text-align"
                                            v-if="!isAccountingAgent"
                                            @click="globalWindowOpenPage('/Settings/Bosspermission', '老板看账')">
                                            授权老板看账
                                        </div>
                                        <div class="text-align" @click="handleShare">微信分享</div>
                                    </div>
                                </div>
                                <div class="divNew" v-if="!isAccountingAgent"></div>
                            </div>
                            <template v-else>
                                <a
                                    class="button large-1 mr-10 tx-left"
                                    v-if="!isWxwork && !isAccountingAgent && !isHideBarcode && !isHideBossPermission"
                                    v-permission="['bosspermissions-canview']"
                                    @click="() => globalWindowOpenPage('/Settings/Bosspermission', '老板看账')">
                                    授权老板看账
                                </a>
                                <a
                                    class="button tx-left"
                                    v-if="!isHideBarcode"
                                    v-permission="['balancesheet-canshare']"
                                    @click="handleShare"
                                    >微信分享</a
                                >
                            </template>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center custom-table-normal">
                        <div v-show="causeOfUnevenness" :class="isErp ? 'unbalance-erp' : 'unbalance'" id="unbalance" style=""></div>
                        <el-table
                            :class="isErp ? 'erp-table' : ''"
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                            :data="tableData"
                            :empty-text="emptyText"
                            border
                            fit
                            stripe
                            scrollbar-always-on
                            highlight-current-row
                            row-key="lineID"
                            ref="tableRef"
                            @cell-dblclick="handleCellClick"
                            @header-dragend="headerDragend">
                            <el-table-column
                                label="资产"
                                min-width="210"
                                align="left"
                                headerAlign="center"
                                prop="assetProName"
                                :width="getColumnWidth(setModule, 'assetProName')">
                                <template #default="scope">
                                    <div>
                                        <div :class="assertNameClass(scope.row)" :title="scope.row.assetProName">
                                            <span>{{ scope.row.assetProName }}</span>
                                            <template v-if="assertShowEquationEdit(scope.row)">
                                                <div
                                                    v-permission="['balancesheet-canedit']"
                                                    @click="
                                                        openEquationDialog(
                                                            scope.row.assertLineType,
                                                            scope.row.lineID,
                                                            scope.row.statementID,
                                                            scope.row.assetProName
                                                        )
                                                    "
                                                    class="link">
                                                    编辑公式
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <template v-if="isShowBeginYearFirst">
                                <template v-if="accountStandard === 6">
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assertInital"
                                        :width="getColumnWidth(setModule, 'assertInital')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                <span>年初余额</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else-if="accountStandard === 7">
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                <span>{{ searchInfo.pid % 12 === 0 ? "年末余额" : "期末余额" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assertInital"
                                        :width="getColumnWidth(setModule, 'assertInital')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column
                                        label="行次"
                                        min-width="50"
                                        align="left"
                                        header-align="left"
                                        prop="assetNumber"
                                        :formatter="rowNumberFormatter"
                                        :width="getColumnWidth(setModule, 'assetNumber')">
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '期末余额' : '年初数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                <span>{{ accountStandard < 3 ? "期末余额" : "年初数" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '年初余额' : '期末数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assertInital"
                                        :width="getColumnWidth(setModule, 'assertInital')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                            <template v-else>
                                <template v-if="accountStandard === 6">
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assertInital"
                                        :width="getColumnWidth(setModule, 'assertInital')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                <span>年初余额</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else-if="accountStandard === 7">
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                <span>{{ searchInfo.pid % 12 === 0 ? "年末余额" : "期末余额" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                :iconShow="scope.row.assetNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column
                                        label="行次"
                                        min-width="50"
                                        align="left"
                                        header-align="left"
                                        prop="assetNumber"
                                        :formatter="rowNumberFormatter"
                                        :width="getColumnWidth(setModule, 'assetNumber')">
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '年初余额' : '期末数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assertInital"
                                        :width="getColumnWidth(setModule, 'assertInital')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '期末余额' : '年初数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="assetTotal"
                                        :width="getColumnWidth(setModule, 'assetTotal')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(1)" /></el-icon>
                                                <span>{{ accountStandard < 3 ? "期末余额" : "年初数" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.assetTotal"
                                                :formula="calcFormula(scope.row, 1)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 1)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 1, '期末余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.assertInital"
                                                :formula="calcFormula(scope.row, 0)"
                                                :line-number="scope.row.assetNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 0)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 0, '年初数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                            <el-table-column
                                :label="accountStandard % 3 === 0 ? '负债和净资产' : '负债和所有者权益'"
                                min-width="210"
                                align="left"
                                header-align="center"
                                prop="debtsProName"
                                :width="getColumnWidth(setModule, 'debtsProName')">
                                <template #default="scope">
                                    <div>
                                        <div :class="debitNameClass(scope.row)" :title="scope.row.debtsProName">
                                            <span>{{ scope.row.debtsProName }}</span>
                                            <template v-if="debitShowEquationEdit(scope.row)">
                                                <div
                                                    v-permission="['balancesheet-canedit']"
                                                    @click="
                                                        openEquationDialog(
                                                            scope.row.debitsLineType,
                                                            scope.row.debtsLineID,
                                                            scope.row.statementID,
                                                            scope.row.debtsProName
                                                        )
                                                    "
                                                    class="link">
                                                    编辑公式
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <template v-if="isShowBeginYearLast">
                                <template v-if="accountStandard === 6">
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsInital"
                                        :width="getColumnWidth(setModule, 'debtsInital')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                <span>年初余额</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsTotal"
                                        :width="getColumnWidth(setModule, 'debtsTotal')"
                                        :resizable="false">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsTotal"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                placement="left"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else-if="accountStandard === 7">
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsTotal"
                                        :width="getColumnWidth(setModule, 'debtsTotal')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                <span>{{ searchInfo.pid % 12 === 0 ? "年末余额" : "期末余额" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsTotal"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                placement="left"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsInital"
                                        :width="getColumnWidth(setModule, 'debtsInital')"
                                        :resizable="false">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column
                                        label="行次"
                                        min-width="50"
                                        align="left"
                                        header-align="left"
                                        prop="debtsNumber"
                                        :formatter="rowNumberFormatter"
                                        :width="getColumnWidth(setModule, 'debtsNumber')">
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '期末余额' : '年初数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsTotal"
                                        :width="getColumnWidth(setModule, 'debtsTotal')">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                <span>{{ accountStandard < 3 ? "期末余额" : "年初数" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.debtsTotal"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                placement="left"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '年初余额' : '期末数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsInital"
                                        :width="getColumnWidth(setModule, 'debtsInital')"
                                        :resizable="false">
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.debtsTotal"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                            <template v-else>
                                <template v-if="accountStandard === 6">
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsTotal"
                                        :width="getColumnWidth(setModule, 'debtsTotal')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsTotal"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                placement="left"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsInital"
                                        :width="getColumnWidth(setModule, 'debtsInital')"
                                        :resizable="false">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                <span>年初余额</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else-if="accountStandard === 7">
                                    <el-table-column
                                        label="年初余额"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsInital"
                                        :width="getColumnWidth(setModule, 'debtsInital')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="searchInfo.pid % 12 === 0 ? '年末余额' : '期末余额'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsTotal"
                                        :width="getColumnWidth(setModule, 'debtsTotal')"
                                        :resizable="false">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                <span>{{ searchInfo.pid % 12 === 0 ? "年末余额" : "期末余额" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                :amount="scope.row.debtsTotal"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                placement="left"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                :iconShow="scope.row.debtsNote.length"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末余额')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column
                                        label="行次"
                                        min-width="50"
                                        align="left"
                                        header-align="left"
                                        prop="debtsNumber"
                                        :formatter="rowNumberFormatter"
                                        :width="getColumnWidth(setModule, 'debtsNumber')" />
                                    <el-table-column
                                        :label="accountStandard < 3 ? '年初余额' : '期末数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsInital"
                                        :width="getColumnWidth(setModule, 'debtsInital')">
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.debtsTotal"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                    <el-table-column
                                        :label="accountStandard < 3 ? '期末余额' : '年初数'"
                                        min-width="100"
                                        align="right"
                                        header-align="right"
                                        prop="debtsTotal"
                                        :width="getColumnWidth(setModule, 'debtsTotal')"
                                        :resizable="false">
                                        <template #header>
                                            <div class="table-header">
                                                <el-icon><Switch @click="swapColumns(2)" /></el-icon>
                                                <span>{{ accountStandard < 3 ? "期末余额" : "年初数" }}</span>
                                            </div>
                                        </template>
                                        <template #default="scope">
                                            <TableAmountItem
                                                v-if="accountStandard < 3"
                                                :amount="scope.row.debtsTotal"
                                                :formula="calcFormula(scope.row, 3)"
                                                :line-number="scope.row.debtsNumber"
                                                placement="left"
                                                :queryFormulas="isQueryFormulas(scope.row, 3)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 3, '期末余额')"></TableAmountItem>
                                            <TableAmountItem
                                                v-else
                                                :amount="scope.row.debtsInital"
                                                placement="left"
                                                :formula="calcFormula(scope.row, 2)"
                                                :line-number="scope.row.debtsNumber"
                                                :queryFormulas="isQueryFormulas(scope.row, 2)"
                                                @go-to-query-formulas="goToQueryFormulas(scope.row, 2, '年初数')"></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                        </el-table>
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <EditEquation
                        ref="editRef"
                        month-title="期末数"
                        year-title="年初数"
                        :class="[{ 'edit-content': !isErp }, 'slot-mini-content']"
                        :statement-id="editData.statementID"
                        :line-id="editData.lineID"
                        :pid="editData.pid"
                        :title="editData.title"
                        :isSixComeGo="editData.isSixComeGo"
                        :isClassification="searchInfo.classification"
                        :value-type-options="valueTypeOptions"
                        :isBalanceSheet="true"
                        :allAACalcStatement="allAACalcStatement"
                        @handle-submit-success="handleEditSubmitSuccess"
                        @handle-cancel="handleEditCancel"
                        @resetDiyFormula="resetDiyFormula"></EditEquation>
                </div>
            </template>
        </ContentSlider>
    </div>
    <el-dialog title="重分类说明" width="730" v-model="showClassificationDialog" :draggable="false" class="custom-confirm">
        <div class="classification-dialog-content dialog-content">
            <div class="dialog-main">
                <p v-for="(item, index) in tipsList" :key="index" :class="['tips', { 'font-16': index < 2 }]">
                    {{ item }}
                </p>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="showClassificationDialog = false">我知道了</a>
            </div>
        </div>
    </el-dialog>
    <!-- 打印导出弹窗 -->
    <PrintOrExportDialog :show-dialog="showDialog" :type="dialogType" :pid="searchInfo.pid" :fromStatement="1" @close="handleDialogClose" />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="资产负债表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "BalanceSheet",
};
</script>
<script lang="ts" setup>
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { useRoute } from "vue-router";
import { computed, nextTick, reactive, ref, watch, onMounted, onUnmounted } from "vue";
import { PeriodStatus } from "@/api/period";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import { globalWindowOpenPage, globalExport, getUrlSearchParams } from "@/util/url";
import ContentSlider from "@/components/ContentSlider/index.vue";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import type { IValueTypeOption } from "../types";
import { checkPermission } from "@/util/permission";
import { isInWxWork } from "@/util/wxwork";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import usePrint from "@/hooks/usePrint";
import { formatMoney } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import type { IBalanceSheet, IPeriod } from "./types";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { handleCellClick, getSwitchState, share } from "@/views/Statements/utils";
import Dropdown from "@/components/Dropdown/index.vue";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import UnevenTooltip from "@/views/Statements/components/UnevenTooltip/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";
import StatementsPrint from "@/components/PrintDialog/index.vue";

const setModule = "BalanceSheet";
const isErp = ref(window.isErp);

const accountsetStore = useAccountSetStore();
const asId = accountsetStore.accountSet?.asId || 0;
const periodStore = useAccountPeriodStore();
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isWxwork = ref(isInWxWork());
const isAccountingAgent = ref(window.isAccountingAgent);
const route = useRoute();
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;
const tableRef = ref();
const isShowBeginYearFirst = ref(getSwitchState("isShowBeginYearFirst"));
const isShowBeginYearLast = ref(getSwitchState("isShowBeginYearLast"));
const colunmOrderChange = ref("0,0");
const swapColumns = (num: number) => {
    if (num === 1) {
        isShowBeginYearFirst.value = !isShowBeginYearFirst.value;
        localStorage.setItem("isShowBeginYearFirst", JSON.stringify(isShowBeginYearFirst.value));
    } else if (num === 2) {
        isShowBeginYearLast.value = !isShowBeginYearLast.value;
        localStorage.setItem("isShowBeginYearLast", JSON.stringify(isShowBeginYearLast.value));
    }
    colunmOrderChange.value = (isShowBeginYearFirst.value ? "0" : "1") + "," + (isShowBeginYearLast.value ? "0" : "1");
    tableRef.value.doLayout();
};
const isHideBossPermission = ref(accountStandard === 6 || accountStandard === 7);

const searchInfo = reactive({
    pid: Number(periodStore.getCurrentPeriod()),
    classification: localStorage.getItem("classificationSwitch") === "true" && ![5, 6, 7].includes(accountStandard) ? true : false,
});

const tableData = ref<IBalanceSheet[]>([]);
let loading = ref(false);
let emptyText = ref(" ");
let loadingCount = 0;

const tipsList = [
    "重分类调整只调表不调账，即不用调整凭证分录，只调整往来相关报表项目余额",
    "报表中涉及重分类的往来科目如下:",
    "【应收账款】=应收账款明细科目的借方余额合计+预收账款明细科目借方余额合计-计提的相应坏账准备",
    "【预收账款】=应收账款明细科目的贷方余额合计+预收账款明细科目贷方余额合计",
    "【应付账款】=应付账款明细科目的贷方余额合计+预付账款明细科目贷方余额合计",
    "【预付账款】=应付账款明细科目的借方余额合计+预付账款明细科目借方余额合计",
    "【其他应收账款】=其他应收款明细科目借方余额合计+其他应付款明细科目借方余额合计-计提的相应坏账准备",
    "【其他应付账款】=其他应收款明细科目贷方余额合计+其他应付款明细科目贷方余额合计",
];

function handleSearch() {
    if (loadingCount === 0 && route.query.pid) {
        searchInfo.pid = Number(route.query.pid);
    }
    loadingCount++;
    loading.value = true;
    // 获取资产负债表
    request({
        url: `/api/BalanceSheet?IsClassification=${searchInfo.classification}&PId=${searchInfo.pid}`,
        method: "get",
    })
        .then((res: IResponseModel<IBalanceSheet[]>) => {
            loading.value = false;

            if (res.state === 1000) {
                tableData.value = [];
                let parent = null;
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children?.push(element);
                        continue;
                    }
                    tableData.value.push(element);
                }
                checkCauseOfUnevenness();
            } else {
                tableData.value = [];
                emptyText.value = "暂无数据";
            }
        })
        .catch((error) => {
            console.log(error);
        })
        .finally(() => {
            setTimeout(searchLineId, 0);
        });
}
const lineIDList = ref();
function searchLineId() {
    request({
        url: `/api/BalanceSheet/Formulas?IsClassification=${searchInfo.classification}&PId=${searchInfo.pid}`,
        method: "get",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            lineIDList.value = res.data;
        }
    });
}
const querySubject = (row: IBalanceSheet, columnIndex: number) => {
    let data;
    switch (columnIndex) {
        case 0:
            data = lineIDList.value[row.lineID];
            break;
        case 1:
            data = lineIDList.value[row.lineID];
            break;
        case 2:
            data = lineIDList.value[row.debtsLineID];
            break;
        case 3:
            data = lineIDList.value[row.debtsLineID];
            break;
        default:
            break;
    }
    return data ?? [];
};
const isQueryFormulas = (row: IBalanceSheet, columnIndex: number) => {
    if (lineIDList.value && checkPermission(["generalledger-canview"])) {
        let result = querySubject(row, columnIndex);
        return result?.length > 0 ? !!result[0].asubname : false;
    } else {
        return false;
    }
};
const goToQueryFormulas = (row: IBalanceSheet, columnIndex: number, val: string) => {
    let time = searchInfo.pid;
    if (val.indexOf("年初") !== -1) {
        if (searchInfo.pid <= 12) {
            time = periodStore.period.startPid;
        } else {
            time = 12 * Math.floor((searchInfo.pid - 1) / 12) + 1;
        }
    }
    let result = querySubject(row, columnIndex);
    let SubjectIDList = "&SubjectIDList=" + result.map((item: any) => item.asubid).join(",");
    let params = {
        period_s: time,
        period_e: time,
    };
    if (result.length > 0) {
        globalWindowOpenPage("/AccountBooks/GeneralLedger?" + getUrlSearchParams(params) + SubjectIDList, "总账");
    }
};

function isSixComeGo(statementID: number, lineID: number) {
    if (statementID == 1001) {
        if (lineID == ********) return true;
        if (lineID == ********) return true;
        if (lineID == ********) return true;
        if (lineID == ********) return true;
        if (lineID == ********) return true;
        if (lineID == ********) return true;
    }
    if (statementID == 2001 || statementID == 2022 || statementID == 2023) {
        var subLineId = lineID.toString().substr(4);
        if (
            subLineId == "0050" ||
            subLineId == "0060" ||
            subLineId == "0090" ||
            subLineId == "0480" ||
            subLineId == "0490" ||
            subLineId == "0540"
        ) {
            return true;
        }
    }
    if (statementID == 3001) {
        if (lineID == ********) return true;
        if (lineID == ********) return true;
        if (lineID == 30010340) return true;
        if (lineID == 30010370) return true;
    }
    if (statementID == 4001) {
        if (lineID == 40010020) return true;
        if (lineID == ********) return true;
    }
    if (statementID == 5001) {
        if (lineID == ********) return true;
        if (lineID == ********) return true;
        if (lineID == ********) return true;
    }
    return false;
}

const periodIsCheckOut = computed(() => {
    return periodRef.value?.periodStatus === PeriodStatus.CheckOut;
});

let isFristChild = false;

function assertNameClass(row: IBalanceSheet) {
    let className: string;
    if (row.parentID == 0) {
        if (accountStandard !== 6) {
            className = "level1";
        } else {
            if (row.assertLineType === 0 || row.assertLineType === 3) {
                className = "level1";
            } else {
                className = "level2";
            }
        }
    } else if (row.expand == 1) {
        isFristChild = true;
        className = "level2";
    } else if (row.fold) {
        if (isFristChild) {
            className = "level3";
        } else {
            className = "level3 pl-60";
        }
        isFristChild = false;
    } else {
        if (accountStandard === 7 && row.assetProName.trim().startsWith("减：")) {
            className = "level3";
        } else {
            className = "level2";
        }
    }
    return className;
}
function assertShowEquationEdit(row: IBalanceSheet) {
    if (row.expand == 1 || row.fold || row.assertLineType == 1) {
        return !periodIsCheckOut.value;
    } else {
        return false;
    }
}

function debitNameClass(row: IBalanceSheet) {
    if (row.debitsLineType == 0 || row.debitsLineType == 3) {
        if (accountStandard === 6) {
            return "level1";
        }
        if (row.debtsParentId == "0") {
            return "level1";
        }
    } else {
        return "level2";
    }
}

function debitShowEquationEdit(row: IBalanceSheet) {
    if (row.debitsLineType === 1) {
        return !periodIsCheckOut.value;
    } else {
        return false;
    }
}

function rowNumberFormatter(_row: any, _column: any, cellValue: any) {
    if (cellValue === 0) {
        return "";
    } else {
        return cellValue;
    }
}

function calcFormula(row: IBalanceSheet, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.assetNote?.split("|")[0].replace(/期末/g, "年初") ?? "";
            break;
        case 1:
            formula = row.assetNote?.split("|")[1] ?? "";
            break;
        case 2:
            formula = row.debtsNote?.split("|")[0].replace(/期末/g, "年初") ?? "";
            break;
        case 3:
            formula = row.debtsNote?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}

// 打印导出
const handleDialogClose = () => {
    showDialog.value = false;
};


function getDefaultParams() {
    return {
        isClassification: searchInfo.classification,
        pId: searchInfo.pid,
        colunmOrderChange: colunmOrderChange.value,
    };
}
const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "balanceSheet",
    `/api/BalanceSheet/Print`,
    {},
    false,
    false,
);

function handleExport(exportType: number) {
    if (exportType === 0) {
        globalExport(`/api/BalanceSheet/Export?` + getUrlSearchParams(getDefaultParams()));
    } else {
        // 批量导出
        dialogType.value = "export";
        showDialog.value = true;
    }
}
const reportType: { [key: number]: string } = {
    0: "BalanceSheet",
    1: "NewBalanceSheetUnExecuted",
    2: "NewBalanceSheetExecuted",
};
let subAccountingStandardReport: string = reportType[useAccountSetStore().accountSet?.subAccountingStandard || 0];
const editRef = ref<InstanceType<typeof EditEquation>>();
const editData = reactive({
    statementID:
        Number(useAccountSetStore().accountSet?.accountingStandard) * 1000 + Number(ReportTypeEnum[subAccountingStandardReport as any]),
    lineID: 0,
    pid: Number(periodStore.getCurrentPeriod()),
    title: "",
    isSixComeGo: false,
});
const valueTypeOptions: IValueTypeOption[] = [
    { value: "1", label: "余额" },
    { value: "8", label: "本级科目借方余额" },
    { value: "9", label: "本级科目贷方余额" },
    { value: "10", label: "末级科目借方余额" },
    { value: "11", label: "末级科目货方余额" },
    { value: "12", label: "辅助核算借方余额" },
    { value: "13", label: "辅助核算贷方余额" },
];

const recordScrollTop = ref(0);
function openEquationDialog(lineType: number, lineID: number, statementID: number, title: string) {
    if (lineType === 1) {
        editData.statementID = statementID;
        editData.lineID = lineID;
        editData.pid = searchInfo.pid;
        editData.title = title;
        recordScrollTop.value = document.body.scrollTop;
        editData.isSixComeGo = isSixComeGo(statementID, lineID) ? true : false;
        nextTick().then(() => {
            editRef.value?.init();
            currentSlot.value = "edit";
        });
    }
}
// 重置公式
function resetFormula() {
    ElConfirm(
        "确认删除此报表所有自定义公式？<div style='margin-top: 10px;'>重置公式仅影响未结账期间的报表数据</div>",
        false,
        () => {},
        "重置此报表公式"
    ).then((r: boolean) => {
        if (r) {
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${editData.statementID}`,
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    ElNotify({
                        type: "success",
                        message: "已经成功重置",
                    });
                    handleSearch();
                    isHasEquation();
                    checkAccPeriodStatus();
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
}
// 重置自定义公式
const resetDiyFormula = (statementId: number, lineId: number, isClassification: boolean) => {
    request({
        url: `/api/StatementEquation/ResetEqutionsByLineId?statementId=${statementId}&lineID=${lineId}&isClassification=${isClassification}`,
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "已经成功重置",
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .finally(() => {
            editRef.value?.searchEquationData();
        });
};
// 检查是否有自定义公式
let resetFormulaButton = ref<boolean>();
function isHasEquation() {
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${editData.statementID}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            resetFormulaButton.value = res.data;
        }
    });
}

// 获取当前账套指定期间的状态,结账状态不允许编辑公式
const status = ref("1");
const checkAccPeriodStatus = () => {
    request({
        url: `/api/Period?pid=${searchInfo.pid}`,
        method: "get",
    }).then((res: IResponseModel<IPeriod>) => {
        if (res.state === 1000) {
            status.value = String(res.data.status);
        }
    });
};
function handleEditSubmitSuccess() {
    handleSearch();
    // searchLineId();
    isHasEquation();
    currentSlot.value = "main";
}

function handleEditCancel() {
    currentSlot.value = "main";
    setTimeout(() => {
        document.body.scrollTop = recordScrollTop.value;
    }, 0);
}
const slots = ["main", "edit"];
const currentSlot = ref("main");

const shareReportHost = ref("");

const showClassificationDialog = ref(false);

// 分享
function handleShare() {
    request({
        url: `/api/BalanceSheet/Share?IsClassification=${searchInfo.classification}&PId=${searchInfo.pid}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                shareReportHost.value =
                    window.accountSrvHost +
                    "/api/WxPay/MakeQRCode.ashx?data=" +
                    window.shareReportHost +
                    "/ShareReport/" +
                    res.data +
                    "&CurrentSystemType=1";
                share(shareReportHost.value);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((err) => {
            console.log(err);
        });
}
let causeOfUnevenness = ref("");
let causeOfFlag = ref(true);
function checkCauseOfUnevenness() {
    let lastLines = tableData.value[tableData.value.length - 1];
    let assetTotal = formatMoney(lastLines.assetTotal);
    let debtsTotal = formatMoney(lastLines.debtsTotal);
    let assertInital = formatMoney(lastLines.assertInital);
    let debtsInital = formatMoney(lastLines.debtsInital);
    if (assetTotal !== debtsTotal || assertInital !== debtsInital) {
        // 检查报表不平的原因
        request({
            url: `/api/BalanceSheet/GetNotBalanceReason?pId=${searchInfo.pid}&IsClassification=${searchInfo.classification}`,
            method: "post",
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                causeOfUnevenness.value = res.data;
                if (causeOfUnevenness.value === "不平原因：损益类科目还有余额，请继续结转损益") {
                    causeOfFlag.value = false;
                } else {
                    causeOfFlag.value = true;
                }
            } else {
                causeOfUnevenness.value = "";
            }
        });
    } else {
        causeOfUnevenness.value = "";
    }
}

function changeClassification() {
    window.localStorage.setItem("classificationSwitch", searchInfo.classification.toString());
}

const allAACalcStatement = ref(true);
const getAccountSetInfo = () => {
    request({
        url: "/api/AccountSetOnlyAuth/Info?asId=" + asId,
        method: "get",
    }).then((res: IResponseModel<any>) => {
        allAACalcStatement.value = res.data.allAACalcStatement ?? true;
    });
};

watch(
    searchInfo,
    () => {
        handleSearch();
        checkAccPeriodStatus();
        isHasEquation();
        searchLineId();
    },
    { immediate: true }
);
onMounted(() => {
    colunmOrderChange.value = (isShowBeginYearFirst.value ? "0" : "1") + "," + (isShowBeginYearLast.value ? "0" : "1");
    // searchInfo.classification = JSON.parse(window.localStorage.getItem("classificationSwitch") || "false");
    colunmOrderChange.value = (isShowBeginYearFirst.value ? "0" : "1") + "," + (isShowBeginYearLast.value ? "0" : "1");
    getAccountSetInfo();
    window.addEventListener("changeAllAACalcStatement", getAccountSetInfo);
    window.addEventListener("peroidListChange", handleSearch);
});

onUnmounted(() => {
    window.removeEventListener("changeAllAACalcStatement", getAccountSetInfo);
    window.removeEventListener("peroidListChange", handleSearch);
});

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";

.content {
    .edit-content {
        width: 1100px;
    }
}
.main-top {
    .uneven-prompt {
        .uneven-prompt-text {
            &:first-child {
                margin-left: 0;
            }
        }
    }
}
:deep(.main-center) {
    position: relative;
    .unbalance {
        pointer-events: none;
        position: absolute;
        top: 23px;
        left: 40%;
        width: 120px;
        height: 70px;
        transform: translateX(-50%);
        background-image: url(@/assets/Common/not-balance.png);
        background-position: 0px -80px;
        background-repeat: no-repeat;
        z-index: 5;
    }
    .unbalance-erp {
        position: absolute;
        top: 30px;
        right: 35px;
        width: 120px;
        height: 70px;
        transform: translateX(-50%);
        background-image: url(@/assets/Common/not-balance.png);
        background-position: 0px -80px;
        background-repeat: no-repeat;
        z-index: 5;
    }
    tr {
        td {
            .cell {
                //这里样式已经控制好了，禁用掉element的样式
                .el-table__indent {
                    display: none !important;
                }

                .el-table__placeholder {
                    display: none;
                }

                .el-table__expand-icon {
                    position: absolute;
                }

                .el-icon {
                    margin-top: 2px;
                }

                .level2 {
                    span {
                        max-width: 200px;
                        white-space: normal;
                    }
                }

                .level3.pl-60 {
                    padding-left: 60px;
                }
            }
        }
    }
}

:deep(.el-table) {
    .el-table__expand-icon {
        transform: rotate(90deg);
        margin-top: 3px;
        color: #fff;

        .el-icon {
            width: 1em;
            height: 1em;
            margin: 0 !important;
            border-radius: 50%;
            background-color: #2abe2a;
        }

        &.el-table__expand-icon--expanded {
            transform: rotate(-90deg) !important;
        }
        &.el-table__expand-icon--expanded {
            transform: rotate(-90deg) !important;
        }
    }
    .el-loading-spinner {
        top: 36%;
    }
}

.dropdown {
    &.large-1 {
        width: 94px;
        background-position-x: 64px;
        text-align: left;
    }

    .divNew {
        background: url("@/assets/Icons/new2.png") no-repeat;
        width: 20px;
        height: 20px;
        background-size: 20px 10px;
        position: absolute;
        top: 0;
        right: 0;
    }
}

.dialog-content {
    .dialog-main {
        margin-top: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .img-container,
        .img-container .img {
            width: 126px;
            height: 126px;
        }

        .text-desc {
            margin-top: 7px;
            margin-bottom: 30px;
            .set-font;
        }
    }
    &.classification-dialog-content {
        .dialog-main {
            margin: 0;
            padding: 15px 10px;
            box-sizing: border-box;
            .tips {
                width: 100%;
                margin: 0;
                text-align: left;
                font-size: 14px;
                line-height: 30px;
                &.font-16 {
                    font-size: 16px;
                    &:last-child {
                        margin-bottom: 5px;
                    }
                }
            }
        }
    }

    .buttons {
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}
.text-align {
    text-align: left !important;
    padding: 0 12px !important;
}
body[erp] {
    .button.dropdown {
        width: auto;
    }
}
</style>
