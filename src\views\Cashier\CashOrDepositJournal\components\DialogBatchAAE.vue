<template>
    <el-dialog
        class="custom-confirm dialogDrag"
        v-model="show"
        :title="'批量指定' + props.name"
        center
        width="706"
        :destroy-on-close="true"
        @close="handleCancel"
        modal-class="modal-class"
    >
        <div class="unit-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="unit-main">
                <div class="tabs-top">
                    <div class="left">
                        <el-input v-model="opTypeSearchVal" />
                        <a class="button solid-button ml-10" @click="handleSearch">查询</a>
                    </div>
                </div>
                <div class="tabs-center">
                    <Table
                        :loading="loading"
                        :max-height="300"
                        :data="tableData"
                        :columns="customerColumns"
                        :page-is-show="true"
                        :layout="paginationData.layout"
                        :page-sizes="paginationData.pageSizes"
                        :page-size="paginationData.pageSize"
                        :total="paginationData.total"
                        :currentPage="paginationData.currentPage"
                        :scrollbar-show="true"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        @row-click="handleRowClickOpType"
                        :tableName="setModule"
                    />
                </div>
                <el-checkbox v-model="override" class="ml-20 mt-10" :class="isErp ? 'fw-600' : ''">覆盖原有{{ aaName }}</el-checkbox>
            </div>
            <div class="buttons">
                <a class="button" @click="handleSure">确定</a>
                <a class="button ml-20" @click="handleCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { request } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { usePagination } from "@/hooks/usePagination";

import { CashAAType, type IDepartmentItem, type IProjectItem } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const isErp = ref(window.isErp);
const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();

const props = defineProps<{
    aatype: CashAAType.Department | CashAAType.Project | CashAAType.Employee;
    name: string;
}>();
const _override = ref(true);
const loading = ref(false);
const emit = defineEmits<{
    (event: "handle-sure", value: string, aatype: CashAAType.Department | CashAAType.Project | CashAAType.Employee): void;
    (event: "update:override", value: boolean): void;
}>();
const show = ref(false);
const override = computed({
    get() {
        return _override.value;
    },
    set(val) {
        _override.value = val;
        emit("update:override", val);
    },
});
const aaNameEnum: any = {
    [CashAAType.Project]: "项目",
    [CashAAType.Department]: "部门",
    [CashAAType.Employee]: "职员",
};

const aaName = computed(() => aaNameEnum[props.aatype] || "");
const setModule = "CashBatchOperate"
const customerColumns = ref<Array<IColumnProps>>([]);
    const setColumns = ():IColumnProps[] => {
    return [
        { label: aaName.value + "编码", align: "left", headerAlign: "left", minWidth: 150, prop: "aaNum", width: getColumnWidth(setModule, 'aaNum') },
        { label: aaName.value + "名称", align: "left", headerAlign: "left", minWidth: 261, prop: "aaName", width: getColumnWidth(setModule, 'aaName') },
        { label: "备注", align: "left", headerAlign: "left", minWidth: 250, prop: "note", resizable: false, },
    ];
}
const tableData = ref<Array<IDepartmentItem | IProjectItem>>([]);
const opTypeSearchVal = ref("");
let aaeId: string = "";
function handleRowClickOpType(row: IDepartmentItem | IProjectItem) {
    aaeId = row.aaeId.toString();
}
const urlPathByAAtype: any = {
    [CashAAType.Project]: "PagingProjectList?",
    [CashAAType.Department]: "PagingDepartmentList?",
    [CashAAType.Employee]: "PagingEmployeeList?",
};
function handleSearch() {
    aaeId = "";
    const params: any = {
        status: 0,
        searchStr: opTypeSearchVal.value.trim(),
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        hidedisable: true,
    };
    if (props.aatype === CashAAType.Project || props.aatype === CashAAType.Department) params.onlyLeaf = true;
    loading.value = true;
    let urlPath = "/api/AssistingAccounting/";
    urlPath += urlPathByAAtype[props.aatype] || "?";
    request({ url: urlPath + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
            }
        })
        .finally(() => (loading.value = false));
}
function handleSure() {
    if (!aaeId) {
        ElNotify({ type: "warning", message: "请选择" + aaName.value });
        return;
    }
    show.value = false;
    emit("handle-sure", aaeId, props.aatype);
    handleCancel();
}
function handleCancel() {
    show.value = false;
    opTypeSearchVal.value = "";
    aaeId = "";
}

function handleInit() {
    show.value = true;
    customerColumns.value = setColumns();
    handleSearch();
}

watch([() => paginationData.currentPage, () => paginationData.pageSize], () => {
    handleSearch();
});
defineExpose({ handleInit });
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.unit-content {
    .tabs-top {
        margin: 12px 20px 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > div {
            display: flex;
            align-items: center;
        }
        .left {
            .detail-el-input(180px, 30px);
            :deep(.el-input__wrapper) {
                padding: 0 8px;
                .el-input__inner {
                    border: none;
                }
            }
        }
    }
    .tabs-center {
        margin: 10px 20px 20px 20px;
        .custom-table {
            min-height: 100px;
        }
        :deep(.el-table) {
            .el-scrollbar__view {
                height: 240px;
            }
        }
    }
    .buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        box-sizing: border-box;
        border-top: 1px solid var(--border-color);
        // margin-top: 51px;
        margin-top: 10px;
    }
    &.erp {
        .tabs-center {
            margin: 16px 20px 0px 20px;
            :deep(.el-table) {
                .el-table__empty-block {
                    width: 100% !important;
                }
            }
        }
        .buttons {
            .button {
                color: var(--blue);
                border-color: var(--blue);
            }
        }
    }
}
.fw-600 {
    font-weight: 600;
}
</style>
