<template>
  <div class="new-popover">
    <div
      class="new-popover-trigger"
      @click="handleClick"
      @mouseenter="handleMouseEnter">
      <slot name="trigger"></slot>
    </div>
    <div
      ref="contentRef"
      v-show="false">
      <slot name="content"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
  interface IPopoverProps {
    title?: string
    content?: string
    placement?: "left" | "right" | "top" | "bottom"
    showOtherTitle?: boolean
    resetHandle?: () => void
    hoverTrigger?: boolean
    getTriggerScrollWidth?: (event: MouseEvent) => number
    useSlotContent?: boolean
    maxWidth?: number
    minWidth?: number
    minOverflowHeight?: number
    paddingRight?: number
  }
  const props = withDefaults(defineProps<IPopoverProps>(), {
    title: "",
    content: "",
    placement: "right",
    showOtherTitle: false,
    resetHandle: () => {},
    hoverTrigger: false,
    useSlotContent: false,
    minOverflowHeight: 150,
  })

  const contentRef = ref<HTMLDivElement>()

  // 移除元素
  const removePopCon = () => {
    const popCon = document.querySelector(".new-popover-content")
    popCon && document.body.removeChild(popCon)
  }
  // 创建元素（根据当前元素配置的props创建）
  const createPopCon = (currentProps: IPopoverProps) => {
    // 防止多次创建
    const hasPopCon = document.querySelector(".new-popover-content")

    if (hasPopCon) {
      hasPopCon.remove()
    }

    const popCon = document.createElement("div")
    popCon.className = "new-popover-content"
    currentProps.minWidth && (popCon.style.minWidth = currentProps.minWidth + "px")
    currentProps.maxWidth && (popCon.style.maxWidth = currentProps.maxWidth + "px")
    currentProps.paddingRight && (popCon.style.paddingRight = currentProps.paddingRight + "px")
    popCon.innerHTML = `
  <div class='arrow'></div>
  <div class="new-popover-content-inner"></div>
  `
    const newPopoverContentInner = popCon.querySelector(".new-popover-content-inner") as HTMLElement
    if (currentProps.maxWidth) {
      newPopoverContentInner && (newPopoverContentInner.style.maxWidth = currentProps.maxWidth + "px")
    }
    if (currentProps.minWidth) {
      newPopoverContentInner && (newPopoverContentInner.style.minWidth = currentProps.minWidth + "px")
    }
    document.body.insertBefore(popCon, document.body.firstChild)
  }

  const handleReset = (e: Event) => {
    if (e.target && (e.target as HTMLElement).classList.contains("link")) {
      props.resetHandle()
    }
  }

  function handleClick(event: MouseEvent) {
    if (props.hoverTrigger) {
      hidePopover()
      return
    }
    showPop(event)
  }
  function handleMouseEnter(event: MouseEvent) {
    if (!props.hoverTrigger) return
    showPop(event)
  }

  const contentInnerRef = ref<HTMLElement>()
  // 点击显示气泡
  const showPop = (event: MouseEvent) => {
    // 触发器属性
    const {
      left: triggerLeft,
      top: triggerTop,
      width: triggerWidth,
      height: triggerHeight,
    } = (event.target as HTMLElement).getBoundingClientRect()

    if (props.hoverTrigger && props.getTriggerScrollWidth && triggerWidth > props.getTriggerScrollWidth(event)) {
      return
    }

    createPopCon(props)
    document.addEventListener("mousemove", closePop)

    let popCon = document.querySelector(".new-popover-content") as HTMLElement
    const popConInner = popCon.querySelector(".new-popover-content-inner") as HTMLElement
    const popConArrow = popCon.querySelector(".arrow") as HTMLElement

    popCon.style.display = "block"
    popCon.addEventListener("click", handleReset)
    const content = contentRef.value?.children[0] as HTMLElement
    if (props.useSlotContent && !contentInnerRef.value) {
      contentInnerRef.value = content
      contentInnerRef.value.addEventListener("click", hidePopover)
      contentRef.value?.remove()
    }
    props.useSlotContent && !contentInnerRef.value && ((contentInnerRef.value = content), contentRef.value?.remove())

    // 设置元素内容与样式;
    let htmlContent = `
      <div class="popover-title" style="display: flex; justify-content: space-between; align-items: center;">
          <span>${props.title}</span>
          ${props.showOtherTitle ? "<span class='link' style='font-size: 12px; margin-right: 20px;'>按公式重置</span>" : ""}
      </div>
  `
    if (!props.useSlotContent) {
      htmlContent += `<div>${props.content}</div>`
    }
    popConInner.innerHTML = htmlContent

    if (props.useSlotContent && contentInnerRef.value) {
      popConInner.appendChild(contentInnerRef.value)
    }

    popCon.style.left = "0px"
    popCon.style.top = "0px"
    // 气泡宽高
    const { width: popConWidth, height: popConHeight } = popCon.getBoundingClientRect()
    const popConPosition = {
      top: "0px",
      left: "0px",
    }

    // 设置定位
    switch (props.placement) {
      case "right":
        popConPosition.left = (props.hoverTrigger ? triggerLeft + triggerWidth : triggerLeft + 20) + 5 + "px"
        popConPosition.top = triggerTop - popConHeight / 2 + 7 + "px"
        break
      case "left":
        popConPosition.left = triggerLeft - popConWidth - triggerWidth / 2 + "px"
        popConPosition.top = triggerTop - popConHeight / 2 + 7 + "px"
        break
      case "top":
        popConPosition.left = triggerLeft - popConWidth / 2 + 7 + "px"
        popConPosition.top = triggerTop - popConHeight - 5 + "px"
        break
      case "bottom":
        popConPosition.left = triggerLeft - popConWidth / 2 + 5 + "px"
        popConPosition.top = triggerTop + triggerHeight + 5 + "px"
        break
      default:
        popConPosition.left = triggerLeft + 23 + "px"
        popConPosition.top = triggerTop - popConHeight / 2 + 3 + "px"
    }

    popCon.classList.add(props.placement)

    const mainContentList = document.querySelectorAll(".main-content")
    const mainContent = Array.from(mainContentList).find((e) => {
      const style = getComputedStyle(e)
      return style.display !== "none"
    })
    const { bottom: mainContentBottom, left: mainContentLeft, right: mainContentRight } = mainContent!.getBoundingClientRect()

    const oldPopConTop = Number(popConPosition.top.slice(0, -2))
    const oldPopConLeft = Number(popConPosition.left.slice(0, -2))

    // 左右显示时 防止超出屏幕
    if (props.placement === "left" || props.placement === "right") {
      if (oldPopConTop < props.minOverflowHeight) {
        // 防止顶部超出
        popConArrow.style.top = popConHeight / 2 - Math.abs(oldPopConTop - props.minOverflowHeight) + "px"
        popConPosition.top = props.minOverflowHeight + "px"
        popConArrow.style.zIndex = "-1"
      } else if (mainContentBottom - popConHeight < oldPopConTop) {
        // 防止底部超出
        popConArrow.style.top = popConHeight / 2 + Math.abs(oldPopConTop - (mainContentBottom - popConHeight)) + 10 + "px"
        popConPosition.top = mainContentBottom - popConHeight - 10 + "px"
        popConArrow.style.zIndex = "-1"
      } else {
        popConArrow.style.top = "50%"
        popConArrow.style.zIndex = "0"
      }
      if (oldPopConLeft + popConWidth > mainContentRight) {
        // 防止右侧超出
        popCon.classList.remove("right")
        popCon.classList.add("left")

        popConPosition.left = triggerLeft - popConWidth - triggerWidth / 2 + "px"
      } else if (oldPopConLeft < mainContentLeft) {
        // 防止左侧超出
        popCon.classList.remove("left")
        popCon.classList.add("right")

        popConPosition.left = triggerLeft + 20 + "px"
      }
    }

    // 上下显示暂未使用
    if (props.placement === "top" || props.placement === "bottom") {
      if (oldPopConLeft < mainContentLeft) {
        // 防止左侧超出
        popConArrow.style.left = popConWidth / 2 - Math.abs(oldPopConLeft - mainContentLeft) + 10 + "px"
        popConPosition.left = mainContentLeft + 10 + "px"
      } else if (oldPopConLeft + popConWidth > mainContentRight) {
        // 防止右侧超出
        popConArrow.style.left = Math.abs(oldPopConLeft + popConWidth - mainContentRight) + popConWidth / 2 + 10 + "px"
        popConPosition.left = mainContentRight - popConWidth - 10 + "px"
      } else {
        popConArrow.style.left = "50%"
      }
      if (oldPopConTop < 150) {
        // 防止上顶部超出
        popCon.classList.remove("top")
        popCon.classList.add("bottom")

        popConPosition.top = triggerTop + triggerHeight + 5 + "px"
      } else if (oldPopConTop + popConHeight > mainContentBottom) {
        // 防止底部超出
        popCon.classList.remove("bottom")
        popCon.classList.add("top")

        popConPosition.top = triggerTop - popConHeight - 5 + "px"
      }
    }
    nextTick().then(() => {
      popCon.style.left = popConPosition.left
      popCon.style.top = popConPosition.top
    })
  }

  // 点击空白处关闭弹窗
  const closePop = (event: MouseEvent) => {
    // 检查点击区域是否在 popover 外  并且不在触发器上
    if (!(event.target as any).closest(".new-popover-content") && !(event.target as any).closest(".new-popover-trigger")) {
      hidePopover()
    }
  }
  function hidePopover() {
    const popCon = document.querySelector(".new-popover-content") as HTMLElement
    if (popCon) {
      popCon.className = "new-popover-content"
      popCon.style.display = "none"
      popCon.removeEventListener("click", handleReset)
    }
    document.removeEventListener("mousemove", closePop)
  }

  onUnmounted(() => {
    removePopCon()
  })
</script>
<style lang="scss">
  .new-popover {
    .new-popover-trigger {
      cursor: pointer;
    }
  }
  .new-popover-content {
    z-index: 999;
    display: none;
    position: fixed;
    padding: 10px;
    font-size: 12px;
    line-height: 20px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 4px;

    .new-popover-content-inner {
      max-width: 350px;
      max-height: 500px;
      overflow: auto;
    }
    .arrow {
      position: absolute;
      display: block;
      content: "";
      width: 10px;
      height: 10px;
      background-color: #fff;
      z-index: 0;
    }
    &.right,
    &.left,
    &.top,
    &.bottom {
      .arrow {
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
      }
    }
    &.right {
      .arrow {
        left: -5px;
      }
    }
    &.left {
      .arrow {
        right: -5px;
      }
    }
    &.top {
      .arrow {
        bottom: -5px;
      }
    }
    &.bottom {
      .arrow {
        top: -5px;
      }
    }
  }
</style>
