<template>
    <el-input
        v-model="value"
        :placeholder="placeholder"
        :required="required"
        :disabled="disabled"
        @blur="round"
    >
        <slot></slot>
    </el-input>
</template>

<script lang="ts" setup>

import { computed } from "vue";
import type { ElInput } from "element-plus";
const props = withDefaults(
    defineProps<{
        modelValue: any;
        placeholder?: string;
        required?: boolean;
        disabled?: boolean;
        decimalPlaces?: number;
    }>(),
    {
        placeholder: "",
        required: false,
        disabled: false,     
        decimalPlaces: 2,
        modelValue: "",
    }
);
const emits = defineEmits(["update:modelValue"]);

const round = () => {
    if (props.modelValue) {
        if(isNaN(props.modelValue)){
            return;
        }
        emits("update:modelValue", Number(props.modelValue).toFixed(props.decimalPlaces));
    }
};

const value = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
</script>