<template>
  <el-dialog
    v-model="updateHistoryVisible"
    title="更新记录"
    center
    class="dialogDrag"
    modal-class="modal-class"
    width="800">
    <div v-dialogDrag>
      <LMTable
        :pageIsShow="true"
        row-key="created"
        :page-size="10"
        :page-sizes="[10]"
        :total="paginationData.total"
        :current-page="paginationData.currentPage"
        :refreshFlag="paginationData.refreshFlag"
        :data="historyData"
        @refresh="handleRefresh"
        @current-change="handleCurrentChange"
        :columns="historyColumns">
        <template #image>
          <el-table-column
            label="截图"
            #default="{ row }">
            <div
              v-if="row.imageUrl"
              class="table-image-container">
              <el-image
                :src="row.imageUrl"
                fit="cover"
                style="width: 30px; height: 30px" />
            </div>
          </el-table-column>
        </template>
      </LMTable>
    </div>
    <template #footer>
      <el-button @click="closeHistoryModal">知道了</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { getTaxLogList } from "@/api/taxManagement"
  import { usePagination } from "@/hooks/usePagination"
  const { paginationData, handleCurrentChange, handleRefresh } = usePagination()

  const props = defineProps<{
    visible: boolean
  }>()

  const emit = defineEmits<{
    (e: "update:visible", value: boolean): void
  }>()

  const updateHistoryVisible = computed({
    get: () => props.visible,
    set: (value) => emit("update:visible", value),
  })
  const historyData = ref([])

  const historyColumns = [
    { label: "更新人", prop: "createdByName", width: 180 },
    { label: "更新时间", prop: "createdDate", width: 180 },
    { label: "变动情况", prop: "log" },
    { slot: "image" },
  ]

  const closeHistoryModal = () => {
    updateHistoryVisible.value = false
  }

  const loadPageData = () => {
    getTaxLogList({
      "paging.pageSize": 10,
      "paging.pageIndex": paginationData.currentPage,
    }).then((res: any) => {
      if (res.data) {
        historyData.value = res.data.data
        paginationData.total = res.data.total
      }
    })
  }

  watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    loadPageData()
  })

  watch(
    () => updateHistoryVisible.value,
    (newVal) => {
      if (newVal) {
        loadPageData()
      }
    },
  )
</script>

<style scoped>
  .table-image {
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 5px;
  }
</style>
