<template>
    <div class="slot-content">
        <div class="slot-mini-content">
            <ContentSlider :slots="slots" :currentSlot="currentSlot">
                <template #step_three>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="3" />
                                <div class="step-edit">
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="bankOutletsInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="开户省份：" prop="openProvince">
                                                    <Select
                                                        v-model="companyToPSBCInfo.openProvince"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectProvince"
                                                    >
                                                        <Option
                                                            v-for="item in PSBCProvinceList"
                                                            :value="item.areaCode"
                                                            :label="item.areaName"
                                                            :key="item.areaCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="开户城市：" prop="openCity">
                                                    <Select
                                                        v-model="companyToPSBCInfo.openCity"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectCity"
                                                    >
                                                        <Option
                                                            v-for="item in cityList"
                                                            :value="item.areaCode"
                                                            :label="item.areaName"
                                                            :key="item.areaCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="城市区县：" prop="openCounty">
                                                    <Select
                                                        v-model="companyToPSBCInfo.openCounty"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectDistrict"
                                                    >
                                                        <Option
                                                            v-for="item in districtList"
                                                            :value="item.areaCode"
                                                            :label="item.areaName"
                                                            :key="item.areaCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="选择网点：" prop="branchNo">
                                                    <Select
                                                        v-model="companyToPSBCInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectBranch"
                                                    >
                                                        <Option
                                                            v-for="item in branchList"
                                                            :value="item.code"
                                                            :label="item.name"
                                                            :key="item.code"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row
                                                class="isRow"
                                                v-show="companyToPSBCInfo.branchAddress || companyToPSBCInfo.branchMobile"
                                            >
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToPSBCInfo.branchAddress">
                                                        {{ companyToPSBCInfo.branchAddress }}
                                                    </div>
                                                    <div v-show="companyToPSBCInfo.branchMobile">
                                                        网点电话：{{ companyToPSBCInfo.branchMobile }}
                                                    </div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToPSBCInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true" prop="accountType">
                                                    <el-radio-group v-model="companyToPSBCInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="back">上一步</a>
                                    <a class="button ml-28 solid-button" @click="toStepFour">下一步</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #step_four>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">预约开户</div>
                            <div class="open-main-content">
                                <TopStep :stepNumber="4" />
                                <div class="step-edit">
                                    <div class="block-title">公司信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="企业证件类型：" :required="true" prop="companyCertType">
                                                    <Select
                                                        v-model="companyToPSBCInfo.companyCertType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                    >
                                                        <Option
                                                            v-for="item in PSBCCertTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                            <span>{{ item.label }}</span>
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="企业证件号码：" :required="true" prop="companyCertNo">
                                                    <el-input v-model="companyToPSBCInfo.companyCertNo" style="width: 240px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件类型：" :required="true" prop="legalCertType">
                                                    <Select
                                                        v-model="companyToPSBCInfo.legalCertType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                    >
                                                        <Option
                                                            v-for="item in PSBCLegalCertTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                            @change="legalCertTypeChange"
                                                        >
                                                            <span>{{ item.label }}</span>
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="法人证件号码：" :required="true" prop="legalCertNo">
                                                    <el-input
                                                        v-model="companyToPSBCInfo.legalCertNo"
                                                        style="width: 240px"
                                                        @input="legalCertNoInput"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">申请人信息</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="申请人手机号：" :required="true" prop="applicantMobile">
                                                    <el-input v-model="companyToPSBCInfo.applicantMobile" style="width: 240px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">预约时间</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="180px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="预约期望时间：" :required="true" prop="appointmentTime">
                                                    <el-date-picker
                                                        v-model="companyToPSBCInfo.appointmentTime"
                                                        type="date"
                                                        placeholder="请选择预约时间，具体以银行审核为准"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 330px"
                                                        :disabled-date="disabledDateStart"
                                                        @change="dateChange"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_three'">上一步</a>
                                    <a class="button ml-28 solid-button longer-button" @click="toCofirmInfo">立即预约开户</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template #confirm>
                    <div class="slot-content">
                        <div class="open-account">
                            <div class="title">确认预约开户详情</div>
                            <div class="open-main-content">
                                <div class="step-edit">
                                    <div class="block-title">开户银行</div>
                                    <div class="block-main">
                                        <el-row class="isRow"><div class="openbank-line">开户银行：邮政储蓄银行</div> </el-row>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">银行网点信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请选择需要办理预约开户业务的银行网点，确认业务信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="160px"
                                            :rules="bankOutletsInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="开户省份：" prop="openProvince">
                                                    <Select
                                                        v-model="companyToPSBCInfo.openProvince"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectProvince"
                                                    >
                                                        <Option
                                                            v-for="item in PSBCProvinceList"
                                                            :value="item.areaCode"
                                                            :label="item.areaName"
                                                            :key="item.areaCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="开户城市：" prop="openCity">
                                                    <Select
                                                        v-model="companyToPSBCInfo.openCity"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectCity"
                                                    >
                                                        <Option
                                                            v-for="item in cityList"
                                                            :value="item.areaCode"
                                                            :label="item.areaName"
                                                            :key="item.areaCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="城市区县：" prop="openCounty">
                                                    <Select
                                                        v-model="companyToPSBCInfo.openCounty"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectDistrict"
                                                    >
                                                        <Option
                                                            v-for="item in districtList"
                                                            :value="item.areaCode"
                                                            :label="item.areaName"
                                                            :key="item.areaCode"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="选择网点：" :required="true" prop="branchNo">
                                                    <Select
                                                        v-model="companyToPSBCInfo.branchNo"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        :filterable="true"
                                                        @change="selectBranch"
                                                    >
                                                        <Option
                                                            v-for="item in branchList"
                                                            :value="item.code"
                                                            :label="item.name"
                                                            :key="item.code"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                            </el-row>
                                            <el-row
                                                class="isRow"
                                                v-show="companyToPSBCInfo.branchAddress || companyToPSBCInfo.branchMobile"
                                            >
                                                <el-form-item label="网点详情：">
                                                    <div class="mr-20" v-show="companyToPSBCInfo.branchAddress">
                                                        {{ companyToPSBCInfo.branchAddress }}
                                                    </div>
                                                    <div v-show="companyToPSBCInfo.branchMobile">
                                                        网点电话：{{ companyToPSBCInfo.branchMobile }}
                                                    </div>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">业务信息</div>
                                    <div class="block-main">
                                        <el-form :model="companyToPSBCInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="选择账户类型：" :required="true" prop="accountType">
                                                    <el-radio-group v-model="companyToPSBCInfo.accountType" class="ml-4">
                                                        <el-radio :label="1">已有基本户</el-radio>
                                                        <el-radio :label="0">没有基本户</el-radio>
                                                    </el-radio-group>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">基本信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>系统会自动同步账套公司信息，请您检查是否准确</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form :model="companyToPSBCInfo" label-position="right" label-width="160px">
                                            <el-row class="isRow">
                                                <el-form-item label="公司名称：" :required="true">
                                                    <el-autocomplete
                                                        ref="asNameRef"
                                                        @blur="handleCompanyBlur"
                                                        v-model="companyToPSBCInfo.companyName"
                                                        :prop="[
                                                            {
                                                                required: true,
                                                                message: '亲，单位名称不能为空',
                                                                trigger: ['blur', 'change'],
                                                            },
                                                        ]"
                                                        :fetch-suggestions="querySearch"
                                                        :trigger-on-focus="false"
                                                        placeholder="请输入完整的单位名称"
                                                        style="width: 238px"
                                                        @select="selectName"
                                                    />
                                                </el-form-item>
                                                <el-form-item label="法人姓名：" :required="true">
                                                    <el-input
                                                        v-model="companyToPSBCInfo.legalName"
                                                        style="width: 240px"
                                                        @blur="handleNameBlur"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人手机号：" :required="true">
                                                    <el-input v-model="companyToPSBCInfo.legalMobile" style="width: 240px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="企业证件类型：" :required="true">
                                                    <Select
                                                        v-model="companyToPSBCInfo.companyCertType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                    >
                                                        <Option
                                                            v-for="item in PSBCCertTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="企业证件号码：" :required="true">
                                                    <el-input v-model="companyToPSBCInfo.companyCertNo" style="width: 240px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                            <el-row class="isRow">
                                                <el-form-item label="法人证件类型：" :required="true">
                                                    <Select
                                                        v-model="companyToPSBCInfo.legalCertType"
                                                        :teleported="false"
                                                        style="width: 240px"
                                                        @change="legalCertTypeChange"
                                                    >
                                                        <Option
                                                            v-for="item in PSBCLegalCertTypeList"
                                                            :value="item.value"
                                                            :label="item.label"
                                                            :key="item.value"
                                                        >
                                                        </Option>
                                                    </Select>
                                                </el-form-item>
                                                <el-form-item label="法人证件号码：" :required="true">
                                                    <el-input
                                                        v-model="companyToPSBCInfo.legalCertNo"
                                                        style="width: 240px"
                                                        @input="legalCertNoInput"
                                                    ></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">更多信息</div>
                                    <div class="block-tip">
                                        <img src="@/assets/Cashier/warn.png" />
                                        <span>请补充如实补充以下信息</span>
                                    </div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="190px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="申请人手机号：" :required="true" prop="applicantMobile">
                                                    <el-input v-model="companyToPSBCInfo.applicantMobile" style="width: 240px"></el-input>
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                    <div class="line"></div>
                                    <div class="block-title">预约时间</div>
                                    <div class="block-main">
                                        <el-form
                                            :model="companyToPSBCInfo"
                                            label-position="right"
                                            label-width="190px"
                                            :rules="operateInfoRules"
                                        >
                                            <el-row class="isRow">
                                                <el-form-item label="预约期望时间：" :required="true">
                                                    <el-date-picker
                                                        v-model="companyToPSBCInfo.appointmentTime"
                                                        type="date"
                                                        placeholder="请选择预约时间，具体以银行审核为准"
                                                        value-format="YYYY-MM-DD"
                                                        style="width: 330px"
                                                        :disabled-date="disabledDateStart"
                                                        @change="dateChange"
                                                    />
                                                </el-form-item>
                                            </el-row>
                                        </el-form>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button" @click="currentSlot = 'step_four'">上一步</a>
                                    <a class="button ml-28 solid-button" @click="saveInfo">确认无误</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </ContentSlider>
        </div>
        <PreOpenResDialog v-model="openResultDialog" bank="邮储银行" @close="toMain" />
    </div>
</template>

<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import { ref, reactive, type Ref, onMounted } from "vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import TopStep from "./TopStep.vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import {
    PSBCLegalCertTypeList,
    PSBCCertTypeList,
    type IBaseCompanyInfo,
    type IPSBCAreaItem,
    type IPSBCBranchInfo,
    type IPSBCAreaRequestItem,
    PSBCCompanyInfoModel,
} from "../types";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import PreOpenResDialog from "./PreOpenResDialog.vue";
import {
    disabledDateStart,
    isBeforeToday,
    checkLegalPersonName,
    cancelConfirm,
    checkPSBCStepThree,
    checkPSBCStepFour,
    checkCompanyInfo,
} from "../utils";
import { useAccountSetStore } from "@/store/modules/accountSet";

const props = defineProps({
    systemType: { type: Number, required: true },
});
const emit = defineEmits<{
    (
        e: "back",
        data: {
            companyName: string;
            legalPersonName: string;
            socialCreditCode: string;
            rightLegalPersonName: string;
            accountType: number;
            legalPhone: string;
        }
    ): void;
    (e: "success"): void;
}>();
const asId = useAccountSetStore().accountSet?.asId;
const slots = ["step_three", "step_four", "confirm"];
const currentSlot = ref("step_three");
const asNameRef = ref();
const queryParams = reactive({
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
});
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};
const rightLegalPersonName = ref("");
function selectName(item: any) {
    companyToPSBCInfo.value.companyName = item.value;
    companyToPSBCInfo.value.unifiedNumber = item.creditCode;
    companyToPSBCInfo.value.legalName = rightLegalPersonName.value = item.legalPersonName;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}
const handleCompanyBlur = (event: any) => {
    getCompanyDetailApi(1010, decodeURIComponent(event.target.value)).then((res: any) => {
        if (res.state === 1000) {
            rightLegalPersonName.value = res.data?.legalPersonName ?? "";
        }
    });
};
const dateChange = (date: any) => {
    if (isBeforeToday(date)) {
        companyToPSBCInfo.value.appointmentTime = "";
    }
};
//步骤三 网点信息
const PSBCProvinceList = ref<IPSBCAreaItem[]>([]);
const cityList = ref<IPSBCAreaItem[]>([]);
const districtList = ref<IPSBCAreaItem[]>([]);
const branchList = ref<IPSBCBranchInfo[]>([]);

const bankOutletsInfoRules = ref({
    openProvince: [{ required: true, message: "请选择开户省份", trigger: "blur" }],
    openCity: [{ required: true, message: "请选择开户城市", trigger: "blur" }],
    outletName: [{ required: true, message: "请选择网点", trigger: "blur" }],
    openCounty: [{ required: true, message: "请选择区县", trigger: "blur" }],
    branchNo: [{ required: true, message: "请选择网点", trigger: "blur" }],
});

const selectProvince = (val: string) => {
    companyToPSBCInfo.value.openCity = "";
    companyToPSBCInfo.value.openCounty = "";
    resetBranchInfo();
    cityList.value = [];
    districtList.value = [];
    getAreaInfo(cityList, val);
};

const selectCity = (val: string) => {
    companyToPSBCInfo.value.openCounty = "";
    resetBranchInfo();
    districtList.value = [];
    getAreaInfo(districtList, val);
};
const selectDistrict = (val: string) => {
    resetBranchInfo();
    branchList.value = [];
    getBranch(val);
};
const resetBranchInfo = () => {
    companyToPSBCInfo.value.branchNo = "";
    companyToPSBCInfo.value.branchName = "";
    companyToPSBCInfo.value.branchAddress = "";
    companyToPSBCInfo.value.branchMobile = "";
};

const selectBranch = (val: string) => {
    const branch = branchList.value.find((item: IPSBCBranchInfo) => item.code === val);
    if (branch) {
        companyToPSBCInfo.value.branchName = branch.name;
        companyToPSBCInfo.value.branchAddress = branch.address;
        companyToPSBCInfo.value.branchMobile = branch.phone;
    }
};
const getAreaInfo = (targetArr: Ref<IPSBCAreaItem[]>, QueryArea: string = "999999") => {
    request({
        url: window.preOpenBankUrl + `/api/PSBCPreOpen/QueryArea?QueryArea=${QueryArea}`,
        method: "get",
    }).then((res: IResponseModel<IPSBCAreaRequestItem[]>) => {
        if (res.state === 1000) {
            targetArr.value = res.data.map((item: IPSBCAreaRequestItem) => {
                return {
                    areaCode: item.areaCode + "",
                    areaName: item.areaName,
                };
            });
        }
    });
};
const getPSBCAreaList = () => {
    getAreaInfo(PSBCProvinceList);
};

const getBranch = (areaId: string) => {
    request({
        url: window.preOpenBankUrl + `/api/PSBCPreOpen/QueryBranch?QueryArea=${areaId}`,
        method: "get",
    }).then((res: IResponseModel<IPSBCBranchInfo[]>) => {
        if (res.state === 1000) {
            branchList.value = res.data;
        }
    });
};
const saveCancelInfo = () => {
    const data = {
        ...getPSBCBankParams(),
    };
    request({
        url: window.preOpenBankUrl + `/api/PSBCPreOpen/Save`,
        method: "post",
        data,
    });
};
const back = () => {
    cancelConfirm().then((r) => {
        if (r) {
            emit("back", {
                companyName: companyToPSBCInfo.value.companyName,
                legalPersonName: companyToPSBCInfo.value.legalName,
                socialCreditCode: companyToPSBCInfo.value.unifiedNumber,
                accountType: companyToPSBCInfo.value.accountType,
                rightLegalPersonName: rightLegalPersonName.value,
                legalPhone: companyToPSBCInfo.value.legalMobile,
            });
            saveCancelInfo();
        }
    });
};
const toStepFour = () => {
    if (!checkPSBCStepThree(companyToPSBCInfo.value)) return;
    currentSlot.value = "step_four";
};

//步骤三

//步骤四 补全公司以及申请人信息
const companyToPSBCInfo = ref(new PSBCCompanyInfoModel());

const operateInfoRules = ref({
    companyCertType: [{ required: true, message: "请选择企业证件类型", trigger: "blur" }],
    companyCertNo: [{ required: true, message: "请输入企业证件号码", trigger: "blur" }],
    legalCertType: [{ required: true, message: "请选择法人证件类型", trigger: "blur" }],
    legalCertNo: [{ required: true, message: "请输入法人证件号码", trigger: "blur" }],
    mobileNo: [{ required: true, message: "请输入申请人手机号码", trigger: "blur" }],
    appointmentTime: [{ required: true, message: "请选择预约时间", trigger: "blur" }],
});
//法人证件身份证 限制18位
const legalCertTypeChange = (value: string) => {
    if (value === "10") {
        companyToPSBCInfo.value.legalCertNo = companyToPSBCInfo.value.legalCertNo.slice(0, 18);
    }
};
const legalCertNoInput = (value: string) => {
    if (value.length > 18 && companyToPSBCInfo.value.legalCertType === "10") {
        companyToPSBCInfo.value.legalCertNo = value.slice(0, 18);
    }
};
const toCofirmInfo = () => {
    if (!checkPSBCStepFour(companyToPSBCInfo.value)) return;
    currentSlot.value = "confirm";
};

//确认预约
const isCanToNext = ref(true);
const handleNameBlur = () => {
    if (!checkLegalPersonName(companyToPSBCInfo.value.legalName, rightLegalPersonName.value)) {
        isCanToNext.value = false;
    }
    const timer = setTimeout(() => {
        isCanToNext.value = true;
        clearTimeout(timer);
    }, 200);
};
const openResultDialog = ref(false);
let canClickSaveInfo = true;
const saveInfo = () => {
    if (!isCanToNext.value) return; //法人姓名失焦校验失败防止提交校验2次提示
    if (!canClickSaveInfo) return;
    if (!checkAllInfo()) return;
    if (!checkLegalPersonName(companyToPSBCInfo.value.legalName, rightLegalPersonName.value)) return;
    canClickSaveInfo = false;
    const data = {
        ...getPSBCBankParams(),
    };
    request({
        url: window.preOpenBankUrl + "/api/PSBCPreOpen/Open",
        method: "post",
        data,
    })
        .then((res: any) => {
            if (res.state === 1000) {
                openResultDialog.value = true;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .finally(() => {
            canClickSaveInfo = true;
        });
};

const getPSBCBankParams = () => {
    const content = JSON.parse(JSON.stringify(companyToPSBCInfo.value));
    return {
        id: "",
        asId: asId + "",
        system: props.systemType,
        content,
    };
};
const toMain = () => {
    currentSlot.value = "step_three";
    resetInfo();
    emit("success");
};
const initBaseCompanyInfo = (data: IBaseCompanyInfo) => {
    const {
        companyName,
        socialCreditCode,
        legalPersonName,
        accountType,
        legalPhone,
        rightLegalPersonName: rightName,
        taxProvinceName,
    } = data;
    companyToPSBCInfo.value.companyName = companyName;
    companyToPSBCInfo.value.unifiedNumber = socialCreditCode;
    companyToPSBCInfo.value.legalName = legalPersonName;
    rightLegalPersonName.value = rightName;
    companyToPSBCInfo.value.accountType = accountType;
    companyToPSBCInfo.value.legalMobile = legalPhone;
    if (taxProvinceName && companyToPSBCInfo.value.openProvince === "") {
        const province = PSBCProvinceList.value.find((item) => item.areaName === taxProvinceName);
        if (province) {
            companyToPSBCInfo.value.openProvince = province.areaCode;
            getAreaInfo(cityList, province.areaCode);
        }
    }
};
const initCancelPSBCInfo = (data: any) => {
    companyToPSBCInfo.value = Object.assign(companyToPSBCInfo.value, data);
    companyToPSBCInfo.value.openProvince && getAreaInfo(cityList, companyToPSBCInfo.value.openProvince);
    companyToPSBCInfo.value.openCity && getAreaInfo(districtList, companyToPSBCInfo.value.openCity);
    companyToPSBCInfo.value.openCounty && getBranch(companyToPSBCInfo.value.openCounty);
};

const resetInfo = () => {
    companyToPSBCInfo.value = new PSBCCompanyInfoModel();
    rightLegalPersonName.value = "";
};
const checkAllInfo = () => {
    return (
        checkPSBCStepThree(companyToPSBCInfo.value) &&
        checkCompanyInfo(companyToPSBCInfo.value) &&
        checkPSBCStepFour(companyToPSBCInfo.value)
    );
};
onMounted(() => {
    getPSBCAreaList();
});
defineExpose({
    initBaseCompanyInfo,
    getPSBCAreaList,
    initCancelPSBCInfo,
    resetInfo,
    saveCancelInfo,
});
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
