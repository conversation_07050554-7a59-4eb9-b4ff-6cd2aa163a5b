<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:acid="autoBankForm.acid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :uscc-number="props.usccNumber"
        :order-account-show="true"
        :bank-name="props.bankName"
        @save-success="saveSuccessHandle"
        @confirm-bank="confirmBank"
    />
</template>

<script setup lang="ts">
import { reactive, ref, computed, watchEffect, onMounted, watch, nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { BankType } from "@/constants/bankKey";
import { GetBankLink, GetBankUrl } from "@/util/bankType";
import { ElConfirm } from "@/util/confirm";
import { globalWindowOpen } from "@/util/url";
import { appendStyle, handleCheck, upgradeApi, getSuccessMsg } from "../utils";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, CMBGradeResult } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import { replaceAll } from "@/util/common";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = [
    "1、请确认公司名称和统一社会信用代码",
    "2、选择对应的银行账户，并确认对应的银行账号",
    "3、在电脑上插入“平安银行”U盾",
    "4、设置授权记账",
];

const props = defineProps({
    bankName: { type: String, default: "" },
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const currencyList = computed(() => props.currencyList);
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const impowerValue = ref("立即授权");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    mainaccno: "",
});

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "申请中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};
const confirmBank = () => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    handleConfirmLock();
    props.checkAuthorization(props.bankType, replaceAll(autoBankForm.mainaccno, " ", ""), () => {
        request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
                handleConfirmUnLock();
                return;
            }
            //检查当前账户的签约状态
            handleQuery();
        });
    });
};

const handleQuery = () => {
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Query");
    });
};

const handleApply = () => {
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Apply");
    });
};

function handleResult(res: IResponseModel<CMBGradeResult>, type: string) {
    const { successMsg, acctNo,  acname} =  getSuccessMsg(autoBankForm, bankAccountList.value);
    if (res.state !== 1000) {
        ElNotify({ type: "warning", message: res.msg || "申请签约失败，请重试！" });
        handleConfirmUnLock();
        return;
    }
    const result = res.data;
    if (result.status === 1 || result.status === 2) {
        //已签约成功
        ElConfirm(appendStyle(successMsg), true, () => {}, "授权成功").then(() => {
            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
        });
        handleConfirmUnLock();
        return;
    }
    if (result.status === 3 && type === "Query") { //签约中查询
        let authUrl = result.data?.authUrl || "";
        ElConfirm("签约受理成功，请前往“平安银行—>企业网银—>现金管理—>开放银行”签约确认。", true).then(() => {
            authUrl && globalWindowOpen(authUrl); //平安银行网银地址
            ElConfirm("是否已前往“平安银行—>企业网银—>现金管理—>开放银行”完成签约确认？").then((r: any) => {
                if (r) handleApply(); //查询授权签约结果
            });
        });
        handleConfirmUnLock();
        return;
    } 
    if (result.status === 3 && type === "Apply") { //签约中申请
        ElNotify({ type: "warning", message: "未查询到本银行账户“" + acname + "”账号“" + acctNo + "”在平安银行的授权信息。" });
        handleConfirmUnLock();
        return;
    } 
    if (result.status === 4) {
        let errorMsg = result.message;
        if (result.data && result.data.Msg) {
            errorMsg = result.data.Msg;
        }
        ElConfirm(errorMsg || "申请签约失败", true, () => {}, "授权失败");
        handleConfirmUnLock();
        return;
    }
    if (result.status === 5 || result.status === 6) {
        //其他账套成功签约/其他账套签约中
        ElConfirm(result.message, true, () => {}, "授权失败");
        handleConfirmUnLock();
        return;
    }
}

const bankHelpLink = ref("");
onMounted(() => {
    let res = GetBankLink(props.bankType);
    bankHelpLink.value = res;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});

watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);
defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
</style>
