export interface IAsubRelationType {
    commodity: boolean;
    customer: boolean;
    vendor: boolean;
    employee: boolean;
    account: boolean;
    income: boolean;
    expense: boolean;
}

export interface IAsubRelationRequestBack {
    data: Array<DetailAsubRealation>;
    count: number;
}

export class DetailAsubRealation {
    asubName1 = "";
    asubName2 = "";
    asubName3 = "";
    type = 0;
    id = 0;
    parentId = 0;
    name = "";
    asubId1 = 0;
    asubId2 = 0;
    asubId3 = 0;
}
