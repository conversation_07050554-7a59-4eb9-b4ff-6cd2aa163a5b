import { defineStore } from "pinia"
import router from "@/router"
import { globalWindowOpenPage, getAppasid } from "@/utils/url"

export interface IRouterModel {
  name: string
  title: string
  path: string
  fullPath: string
  alive: boolean
  cache: boolean
  isEditting?: boolean
  stop?: boolean
  scrollTop?: number
}

export const useVisitedRoutesStore = defineStore("visitedRoutes", () => {
  const routerArray = ref<Array<IRouterModel>>([])
  const leaveValidatorArray = ref<Array<{ path: string; validator: () => boolean }>>([])

  const currentRoute = computed(() => routerArray.value.find((item) => item.alive))

  const findRouteIndex = (path: string): number => routerArray.value.findIndex((item) => item.path.toLowerCase() === path.toLowerCase())

  const findRoute = (path: string): IRouterModel | undefined =>
    routerArray.value.find((item) => item.path.toLowerCase() === path.toLowerCase())

  const enterRouter = (routerModel: IRouterModel) => {
    if (routerModel.path === "/") return

    // 关闭其他路由
    routerArray.value.forEach((item) => (item.alive = false))

    const existingRoute = findRoute(routerModel.path)
    if (existingRoute) {
      existingRoute.fullPath = routerModel.fullPath
      existingRoute.alive = true
    } else {
      routerArray.value.push({
        ...routerModel,
        alive: true,
      })
    }
  }

  const removeRouter = (path: string) => {
    if (!leaveValidate(path) || path === "/TaxDeclaration") return
    const index = findRouteIndex(path)
    if (index === -1) return
    const currentRoute = routerArray.value[index]
    if (currentRoute.alive) {
      // 跳转到下一个或前一个路由
      const nextRoute = routerArray.value[index + 1] || routerArray.value[index - 1]
      if (nextRoute) {
        globalWindowOpenPage(nextRoute.fullPath, nextRoute.title)
      }
    }
    routerArray.value.splice(index, 1)
  }

  const removeAllRouter = () => {
    for (let i = 1; i < routerArray.value.length; i++) {
      if (!leaveValidate(routerArray.value[i].path) || routerArray.value[i].path === "/TaxDeclaration") {
        continue
      } else {
        routerArray.value.splice(i, 1)
        i--
      }
    }
    if (!routerArray.value[0].alive) {
      globalWindowOpenPage(routerArray.value[0].fullPath, routerArray.value[0].title)
    }
  }

  const removeOthersRouter = (path: string) => {
    const index = findRouteIndex(path)
    if (index === 0) {
      removeAllRouter()
      return
    }
    const currentRouter = routerArray.value[index]
    if (currentRouter) {
      routerArray.value = routerArray.value.filter((router, index) => {
        return index === 0 || !leaveValidate(router.path) || router === currentRouter
      })
    }
    if (!currentRouter) {
      globalWindowOpenPage(routerArray.value[routerArray.value.length - 1].fullPath, routerArray.value[routerArray.value.length - 1].title)
    } else {
      if (!currentRouter.alive) {
        globalWindowOpenPage(currentRouter.fullPath, currentRouter.title)
      }
    }
  }

  const refreshRouter = async (path: string) => {
    if (!leaveValidate(path)) return
    const route = findRoute(path)
    if (!route) return
    route.cache = false
    await nextTick()
    await router.replace("/?appasid=" + getAppasid())
    router.replace(route.fullPath)
    route.cache = true
  }

  const replaceCurrentRouter = (fullPath: string) => {
    const currentRouter = routerArray.value.find((item) => item.alive)
    if (currentRouter) {
      currentRouter.fullPath = fullPath
    }
    router.replace(fullPath)
  }

  const registerLeaveValidator = (path: string, validator: () => boolean): { dispose: () => void } => {
    const item = { path: path, validator: validator }
    leaveValidatorArray.value.push(item)
    return {
      dispose: () => {
        const index = leaveValidatorArray.value.findIndex((i) => i.path === item.path && i.validator === item.validator)
        if (index !== -1) {
          leaveValidatorArray.value.splice(index, 1)
        }
      },
    }
  }

  const leaveValidate = (path: string) => {
    for (let i = 0; i < leaveValidatorArray.value.length; i++) {
      const item = leaveValidatorArray.value[i]
      if (item.path === path && !item.validator()) {
        return false
      }
    }
    return true
  }

  const rememberScrollTop = (path: string) => {
    const route = findRoute(path)
    if (!route) return

    const container = document.querySelector(".router-container")
    if (!container) return

    route.scrollTop = container.scrollTop
  }

  const restoreScrollTop = () => {
    const route = currentRoute.value
    if (!route) return

    const container = document.querySelector(".router-container")
    if (!container) return

    container.scrollTop = route.scrollTop || 0
  }

  const changeRouterEditting = (path: string, isEditting: boolean) => {
    const route = findRoute(path)
    if (!route) return
    route.isEditting = isEditting
  }

  const resetRouterQuery = (path: string) => {
    const route = findRoute(path)
    if (!route) return
    route.fullPath = route.path
  }

  return {
    routerArray,
    enterRouter,
    removeRouter,
    removeAllRouter,
    removeOthersRouter,
    refreshRouter,
    replaceCurrentRouter,
    registerLeaveValidator,
    leaveValidate,
    rememberScrollTop,
    restoreScrollTop,
    changeRouterEditting,
    resetRouterQuery,
  }
})
