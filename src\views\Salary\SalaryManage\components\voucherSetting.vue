<template>
    <div class="voucher-setting">
        <div class="main-top main-tool-bar space-between" :style="isErp?'padding-left:10px':''">
            <div class="main-tool-left"></div>
            <div class="main-tool-right">
                <a class="button solid-button mr-10" @click="addHandle" v-permission="['salarymanage-setting-canedit']">新增</a>
                <a class="button" @click="handleClose">返回</a>
            </div>
        </div>
        <div class="edit-temp-wrap">
            <div class="edit-temp-table">
                <div class="edit-temp-block" v-for="item in props.voucherSettingList" :key="item.vt_id">
                    <div class="temp-block-top">
                        <span class="temp-block-bold float-l">{{ item.vt_name }}</span>
                        <div class="btns">
                            <a 
                                v-if="![310, 320, 321, 322].includes(item.vt_type)" 
                                @click="deleteTemplate(item.vt_id)" 
                                v-permission="['salarymanage-setting-candelete']"  
                                class="link float-r ml-10" 
                                style="font-size: 12px;"
                            > 删除 </a>
                            <a
                                v-permission="['salarymanage-setting-canedit']"
                                class="link float-r"
                                @click="ShowVoucherTemplateEdit(item.vt_id, item.vt_name, item.vg_id, item)"
                                style="font-size: 12px;"
                            >
                                修改
                            </a>
                        </div>

                        <div class="float-clear"></div>
                    </div>
                    <div class="edit-temp-lines">
                        <div class="edit-temp-line" v-for="project in item.svlist" :key="project.asub_id">
                            {{ project.direction == 1 ? "贷" : "借" }}：{{ project.asub_code+' '+project.asub_name }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref} from 'vue';
const props = defineProps<{
    voucherSettingList: any;
}>();

const emits = defineEmits(["modifyRules", "newVoucherTemplate", "divCancel", "deleteVoucherTemplate"]);

const isErp= ref(window.isErp)
function ShowVoucherTemplateEdit(vt_id: number, vt_name: string, vg_id: number, item: any) {
    emits("modifyRules", item);
}

function addHandle() {
    emits("newVoucherTemplate");
}
function handleClose() {
    emits("divCancel");
}
const deleteTemplate = (vt_id: number) => {
    emits("deleteVoucherTemplate", vt_id);
};
</script>

<style scoped lang="less">
.voucher-setting {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    background-color: var(--white);
    .main-top.main-tool-bar {
        width: 1100px;
        margin: 0 auto;
    }
    .edit-temp-wrap {
        width: 100%;
        overflow-y: auto;
    }
    .edit-temp-table {
        width: 1100px;
        margin: 0 auto;
        padding: 0 10px 12px 10px;
        font-size: var(--h5);
        color: var(--font-color);
        & .edit-temp-block {
            position: relative;
            border: 1px solid var(--border-color);
            & + .edit-temp-block {
                margin-top: 10px;
            }
            & .temp-block-top {
                margin: 10px 20px 0px 20px;
                line-height: 17px;
                height: 20px;
                & .temp-block-bold {
                    font-weight: bold;
                }
                .btns {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    width: 70px;
                    font-size: 12px;
                }
            }
            & .edit-temp-lines {
                text-align: left;
                margin: 10px 40px 10px 40px;
                & .edit-temp-line {
                    font-size: 14px;
                    overflow-wrap: break-word;
                    word-break: break-all;
                    & + .edit-temp-line {
                        margin-top: 6px;
                    }
                }
            }
        }
    }
}
</style>
