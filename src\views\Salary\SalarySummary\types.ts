import type { TableColumnCtx } from 'element-plus'
export interface ITableItem {
    departmentId: number;
    departmentName: string;
    netSalary: number;
    totalCost: number;
    grossPay: number;
    sspamount: number;
    sscamount: number;
}
export interface ITableItemDep  extends ITableItem {
    emcount: number;
}
export interface ITableItemEm extends ITableItem {
    ename: string;
    esn: number;
}
export interface SpanMethodProps {
    row: ITableItem;
    column: TableColumnCtx<ITableItem>;
    rowIndex: number;
    columnIndex: number;
}
export interface ISelectItem {
    value: string;
    label: string;
}
export interface IPeriodItem {
    value: string;
    label: string;
    time: string;
}
