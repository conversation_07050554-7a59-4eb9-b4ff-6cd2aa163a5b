import { checkPermission } from "@/util/permission";
import { ElNotify } from "@/util/notify";
import type { AsubRelationType, AsubRelationTypeCode, IAsubRelationInfo } from "../AsubRelationSettings1/utils";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { asubRelationActiveTabKey, asubRelationInfo } from "../AsubRelationSettings1/utils";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { ValueType } from "./types";
import type { ICheckBalanceResult, AsubSelectorType, BusinessVoucherTemplateLine } from "./types";
import type { DetailAsubRealation } from "../AsubRelationSettings/types";

export enum DescriptionType {
    Field = 1,
    FixedText = 2,
}
export const descLimitLength = 50;
export class DescriptionInfo {
    type: DescriptionType;
    field: number;
    fixedText: string;
    width = 0;
    active = false;
    editing = false;
    onlyKey = 0;
    constructor(type: DescriptionType, fixedText: string, field: number, width: number) {
        this.type = type;
        this.field = field;
        this.fixedText = fixedText;
        this.width = width;
        this.onlyKey = new Date().getTime();
    }
}
export class RequestCustomDesc {
    constructor(desc: DescriptionInfo) {
        this.isCustom = desc.type === DescriptionType.Field;
        this.descriptionType = desc.type === DescriptionType.Field ? desc.field : 0;
        this.description = desc.fixedText;
    }
    isCustom = false;
    descriptionType = 0;
    description = "";
}
export interface IDescGroup {
    label: string;
    options: Array<{ label: string; id: number }>;
}
export function mapRowDescToDescriptionInfo(customDescriptions: Array<RequestCustomDesc>, groups: Array<IDescGroup>) {
    const now = new Date().getTime();
    return customDescriptions.map((item, index) => {
        const description = item.isCustom ? getFieldInfo(item.descriptionType, groups) : item.description;
        const descriptionItem = new DescriptionInfo(
            item.isCustom ? DescriptionType.Field : DescriptionType.FixedText,
            description,
            item.descriptionType,
            getOffsetWidth(description)
        );
        descriptionItem.onlyKey = now + index;
        return descriptionItem;
    });
}
export function getFieldInfo(field: number, groups: Array<IDescGroup>) {
    const headers = groups[0].options;
    const header = headers.find((item) => item.id === field);
    if (header) return "表头." + header.label;
    const bodys = groups[1].options;
    const body = bodys.find((item) => item.id === field);
    if (body) return "表体." + body.label;
    const footers = groups[2].options;
    const footer = footers.find((item) => item.id === field);
    if (footer) return "表体." + footer.label;
    return "";
}

const borderWdith = 1;
const horizontalPadding = 11;
const maxItemWidth = 224;
export function getOffsetWidth(text: string) {
    const div = document.createElement("div");
    div.innerText = text;
    div.style.display = "inline-block";
    div.style.visibility = "hidden";
    div.style.fontSize = "12px";
    document.body.appendChild(div);
    const width = div.offsetWidth + 2 * (horizontalPadding + borderWdith) + 1; // 1px 是因为可能会有小数 所以进一稳妥一点
    document.body.removeChild(div);
    return width > maxItemWidth ? maxItemWidth : width;
}

export function checkHasModulePermission(typeCode: string) {
    switch (typeCode) {
        case "commodity":
            return checkPermission(["ProductManager-查看"]);
        case "customer":
            return checkPermission(["Customers-查看"]);
        case "vendor":
            return checkPermission(["Vendors-查看"]);
        case "employee":
            return checkPermission(["Employees-查看"]);
        case "income":
        case "expense":
            return checkPermission(["AssistData-查看"]);
        case "account":
            return checkPermission(["Accounts-查看"]);
        default:
            return false;
    }
}

export function routeToModulePage(typeCode: AsubRelationTypeCode | "", tip = false) {
    if (!typeCode) return;
    if (!checkHasModulePermission(typeCode)) {
        tip && ElNotify({ type: "warning", message: "亲，您没有该功能权限哦~" });
        return;
    }
    let url = "";
    let title = "";
    switch (typeCode) {
        case "commodity":
            url = "/Products";
            title = "商品列表";
            break;
        case "customer":
            url = "/Customers";
            title = "客户列表";
            break;
        case "vendor":
            url = "/Vendors";
            title = "供应商列表";
            break;
        case "employee":
            url = "/Employees";
            title = "职员列表";
            break;
        case "income":
        case "expense":
            url = "/assistantData";
            title = "辅助资料";
            break;
        case "account":
            url = "/Accounts";
            title = "账户管理";
            break;
        default:
            break;
    }
    if (!url) return;
    globalWindowOpenPage(url, title);
}

export function checkCanLink(row: DetailAsubRealation) {
    return row.name === "查看更多>>" && row.type === 0 && row.id === 0 && row.parentId === 0 && checkHasAsubRelatePermission();
}

export function checkHasAsubRelatePermission() {
    return checkPermission(["asubrelationsettings-canview"]);
}

export function routeToAsubRelationSettings(activeAsubTypeDetail: string = "") {
    if (!checkHasAsubRelatePermission()) return;
    const params = {
        [asubRelationActiveTabKey]: activeAsubTypeDetail,
    };
    globalWindowOpenPage("/Erp/AsubRelationSettings?" + getUrlSearchParams(params), "科目关联明细设置");
}

export function getTypeName(type: number, name: string) {
    // 收入  支出  账户
    const commonList = [4010, 5010, 6010];
    if (commonList.includes(type)) name = "对应科目";
    const item = asubRelationInfo.find((item) => item.asubRelationType.some((i) => i === type));
    const perfix = item?.type || "";
    return (perfix ? perfix + "." : "") + name;
}

export function getDetailLabel(type: number, asubRelationDetail: IAsubRelationInfo | undefined) {
    if (!asubRelationDetail) return "";
    const index = asubRelationDetail.asubRelationType.findIndex((i) => i === type);
    return asubRelationDetail.asubs[index] || "";
}

export function getDetailAsub(
    type: number,
    asubRelationDetail: IAsubRelationInfo | undefined,
    defaultAsubList: Record<AsubRelationType, string>
) {
    if (!asubRelationDetail) return "";
    const code = type as AsubRelationType;
    const asubId = defaultAsubList[code];
    const asub = useAccountSubjectStore().accountSubjectList.find(
        (item) => item.status !== 1 && item.isLeafNode && item.asubId === ~~asubId
    );
    return asub ? asub.asubCode + " " + asub.asubAAName : "";
}

export const asubTypeList: Array<{ label: string; value: AsubSelectorType }> = [
    { label: "关联科目", value: 1 },
    { label: "系统科目", value: 2 },
];

interface IGetDescParams {
    row: { description: string; customDescriptions: Array<RequestCustomDesc> };
    headerList: Array<{ id: number; label: string }>;
    bodyList: Array<{ id: number; label: string }>;
    footerList: Array<{ id: number; label: string }>;
}
export function getDisplayDescFn(params: IGetDescParams) {
    const { row, headerList, bodyList, footerList } = params;
    if (row.customDescriptions.length > 0) {
        const fullDesc = row.customDescriptions.map((item) => {
            if (!item.isCustom) return item.description;
            const header = headerList.find((i) => i.id === item.descriptionType);
            if (header) return "表头." + header.label;
            const body = bodyList.find((i) => i.id === item.descriptionType);
            if (body) return "表体." + body.label;
            const footer = footerList.find((i) => i.id === item.descriptionType);
            if (footer) return "表体." + footer.label;
            return "";
        });
        return fullDesc.map((item) => `"${item}"`).join("+");
    }
    return row.description;
}

const valueTypeMap = {
    [ValueType.Amount]: "金额",
    [ValueType.NoTaxAmount]: "不含税金额",
    [ValueType.TaxAmount]: "税额",
    [ValueType.TotalAmount]: "价税合计",
    [ValueType.InAmount]: "入库金额",
    [ValueType.OutAmount]: "出库金额",
    [ValueType.AdjustAmount]: "调整金额",
    [ValueType.WriteOffAmount]: "本次核销金额",
    [ValueType.IncomeAmount]: "收入金额",
    [ValueType.ExpenseAmount]: "支出金额",
    [ValueType.DiscountAmount]: "优惠金额",
    [ValueType.DiscountedAmount]: "优惠后金额",
    [ValueType.CustomerCost]: "客户承担费用",
    [ValueType.AssemblyCost]: "组装费用",
    [ValueType.DisassemblyCost]: "拆卸费用",
};

const billVtTypeMap = [
    { label: "采购入库单", value: 501 },
    { label: "采购退货单", value: 502 },
    { label: "销售出库单", value: 503 },
    { label: "销售退货单", value: 504 },
    { label: "其他入库单", value: 505 },
    { label: "其他出库单", value: 506 },
    { label: "盘盈单", value: 507 },
    { label: "盘亏单", value: 508 },
    { label: "成本调整单", value: 509 },
    { label: "结转出库成本单(出库)", value: 510 },
    { label: "结转出库成本单(退货)", value: 511 },
    { label: "组装单", value: 512 },
    { label: "拆卸单", value: 513 },
    { label: "核销单(应收冲应付)", value: 514 },
    { label: "核销单(应收转应收)", value: 515 },
    { label: "核销单(应付转应付)", value: 516 },
    { label: "其他收入单", value: 517 },
    { label: "销项发票", value: 531 },
    { label: "进项发票", value: 532 },
    { label: "日记账收入", value: 541 },
    { label: "日记账支出", value: 542 },
    { label: "内部转账", value: 543 },
    { label: "其他支出单(采购费用未分摊)", value: 544 },
    { label: "其他支出单(采购费用已分摊)", value: 545 },
    { label: "其他支出单(销售费用)", value: 546 },
    { label: "其他支出单(管理费用)", value: 547 },
    { label: "其他支出单(其他费用)", value: 548 },
    { label: "其他支出单(代收代付)", value: 549 },
];
// 采购入库和采购退货一样
function getWarehousingAmount(lines: Array<BusinessVoucherTemplateLine>) {
    const valueTypes = [...new Set(lines.map((item) => item.valueType))];
    let amountMap: { [key: number]: number } = {};

    const Amount = 100;
    const TaxAmount = 11;
    let TotalAmount = 0;
    const DiscountAmount = 1;
    let DiscountedAmount = 0;

    if (valueTypes.includes(ValueType.DiscountAmount)) {
        if (valueTypes.includes(ValueType.TaxAmount)) {
            DiscountedAmount = Amount + TaxAmount - DiscountAmount;
            TotalAmount = DiscountedAmount + DiscountAmount;
            amountMap = {
                [ValueType.Amount]: Amount,
                [ValueType.TaxAmount]: TaxAmount,
                [ValueType.TotalAmount]: TotalAmount,
                [ValueType.DiscountAmount]: DiscountAmount,
                [ValueType.DiscountedAmount]: DiscountedAmount,
            };
        } else {
            DiscountedAmount = Amount - DiscountAmount;
            TotalAmount = DiscountedAmount + DiscountAmount;
            amountMap = {
                [ValueType.Amount]: Amount,
                [ValueType.TotalAmount]: TotalAmount,
                [ValueType.DiscountAmount]: DiscountAmount,
                [ValueType.DiscountedAmount]: DiscountedAmount,
            };
        }
    } else {
        if (valueTypes.includes(ValueType.TaxAmount)) {
            DiscountedAmount = Amount + TaxAmount;
            TotalAmount = Amount + TaxAmount;
            amountMap = {
                [ValueType.Amount]: Amount,
                [ValueType.TaxAmount]: TaxAmount,
                [ValueType.TotalAmount]: TotalAmount,
                [ValueType.DiscountedAmount]: TotalAmount,
            };
        } else {
            DiscountedAmount = Amount;
            TotalAmount = Amount;
            amountMap = {
                [ValueType.Amount]: Amount,
                [ValueType.TotalAmount]: TotalAmount,
                [ValueType.DiscountedAmount]: TotalAmount,
            };
        }
    }
    return amountMap;
}
// 销售出库和销售退货一样
function getSellDeliveryAmount(lines: Array<BusinessVoucherTemplateLine>) {
    const valueTypes = [...new Set(lines.map((item) => item.valueType))];
    let amountMap: { [key: number]: number } = {};

    const Amount = 100;
    const TaxAmount = 11;
    let TotalAmount = 0;
    const DiscountAmount = 1;
    let DiscountedAmount = 0;
    const CustomerCost = 10;

    if (valueTypes.includes(ValueType.CustomerCost)) {
        if (valueTypes.includes(ValueType.DiscountAmount)) {
            if (valueTypes.includes(ValueType.TaxAmount)) {
                DiscountedAmount = Amount + TaxAmount - DiscountAmount;
                TotalAmount = DiscountedAmount + DiscountAmount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TaxAmount]: TaxAmount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountAmount]: DiscountAmount,
                    [ValueType.DiscountedAmount]: DiscountedAmount,
                    [ValueType.CustomerCost]: CustomerCost,
                };
            } else {
                DiscountedAmount = Amount - DiscountAmount;
                TotalAmount = DiscountedAmount + DiscountAmount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountAmount]: DiscountAmount,
                    [ValueType.DiscountedAmount]: DiscountedAmount,
                    [ValueType.CustomerCost]: CustomerCost,
                };
            }
        } else {
            if (valueTypes.includes(ValueType.TaxAmount)) {
                DiscountedAmount = Amount + TaxAmount;
                TotalAmount = Amount + TaxAmount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TaxAmount]: TaxAmount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountedAmount]: TotalAmount,
                    [ValueType.CustomerCost]: CustomerCost,
                };
            } else {
                DiscountedAmount = Amount;
                TotalAmount = Amount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountedAmount]: TotalAmount,
                    [ValueType.CustomerCost]: CustomerCost,
                };
            }
        }
    } else {
        if (valueTypes.includes(ValueType.DiscountAmount)) {
            if (valueTypes.includes(ValueType.TaxAmount)) {
                DiscountedAmount = Amount + TaxAmount - DiscountAmount;
                TotalAmount = DiscountedAmount + DiscountAmount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TaxAmount]: TaxAmount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountAmount]: DiscountAmount,
                    [ValueType.DiscountedAmount]: DiscountedAmount,
                };
            } else {
                DiscountedAmount = Amount - DiscountAmount;
                TotalAmount = DiscountedAmount + DiscountAmount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountAmount]: DiscountAmount,
                    [ValueType.DiscountedAmount]: DiscountedAmount,
                };
            }
        } else {
            if (valueTypes.includes(ValueType.TaxAmount)) {
                DiscountedAmount = Amount + TaxAmount;
                TotalAmount = Amount + TaxAmount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TaxAmount]: TaxAmount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountedAmount]: TotalAmount,
                };
            } else {
                DiscountedAmount = Amount;
                TotalAmount = Amount;
                amountMap = {
                    [ValueType.Amount]: Amount,
                    [ValueType.TotalAmount]: TotalAmount,
                    [ValueType.DiscountedAmount]: TotalAmount,
                };
            }
        }
    }
    return amountMap;
}
// 组装和拆卸一样  只是一个是组装费用 一个是拆卸费用
function getAssemblyAmount(valueType: number, lines: Array<BusinessVoucherTemplateLine>) {
    const valueTypes = [...new Set(lines.map((item) => item.valueType))];
    let amountMap: { [key: number]: number } = {};
    if (![512, 513].includes(valueType)) return amountMap;
    const asub = valueType === 512 ? ValueType.AssemblyCost : ValueType.DisassemblyCost;
    if (!valueTypes.includes(asub)) {
        amountMap = {
            [ValueType.OutAmount]: 100,
            [ValueType.InAmount]: 100,
        };
    } else {
        amountMap = {
            [ValueType.OutAmount]: 100,
            [asub]: 10,
            [ValueType.InAmount]: 110,
        };
    }
    return amountMap;
}
// 进项发票和销项发票一样
function getInvoiceAmount(lines: Array<BusinessVoucherTemplateLine>) {
    const valueTypes = [...new Set(lines.map((item) => item.valueType))];
    let amountMap: { [key: number]: number } = {};
    if (valueTypes.includes(ValueType.TaxAmount)) {
        amountMap = {
            [ValueType.NoTaxAmount]: 100,
            [ValueType.TaxAmount]: 10,
            [ValueType.TotalAmount]: 110,
        };
    } else {
        amountMap = {
            [ValueType.NoTaxAmount]: 100,
            [ValueType.TotalAmount]: 100,
        };
    }
    return amountMap;
}
// 其他收入单和其他支出单一样
function getIncomeAmount(valueType: number, lines: Array<BusinessVoucherTemplateLine>) {
    const valueTypes = [...new Set(lines.map((item) => item.valueType))];
    const Amount = 100;
    const TaxAmount = 10;
    const CustomerCost = 20;
    const TotalAmount = valueTypes.includes(ValueType.TaxAmount) ? Amount + TaxAmount : Amount;
    let amountMap: { [key: number]: number } = {};
    if (![517, 544, 545, 546, 547, 548, 549].includes(valueType)) return amountMap;
    if (valueType === 546) {
        // 546 销售费用
        amountMap = {
            [ValueType.Amount]: Amount,
            [ValueType.TaxAmount]: TaxAmount,
            [ValueType.TotalAmount]: TotalAmount,
            [ValueType.CustomerCost]: CustomerCost,
        };
    } else {
        amountMap = {
            [ValueType.Amount]: Amount,
            [ValueType.TaxAmount]: TaxAmount,
            [ValueType.TotalAmount]: TotalAmount,
        };
    }
    if (!valueTypes.includes(ValueType.TaxAmount)) {
        delete amountMap[ValueType.TaxAmount];
    }
    if (!valueTypes.includes(ValueType.CustomerCost)) {
        delete amountMap[ValueType.CustomerCost];
    }
    return amountMap;
}
function checkLinesIsBalance(amountMap: { [key: number]: number }, lines: Array<BusinessVoucherTemplateLine>) {
    let debitAmount = 0;
    let creditAmount = 0;
    for (const line of lines) {
        let currentAmount = amountMap[line.valueType] || 0;
        // 优惠金额取负数
        if (line.valueType === ValueType.DiscountAmount) {
            currentAmount = -currentAmount;
        }
        if (line.debit === 1) {
            debitAmount += currentAmount;
        } else {
            creditAmount += currentAmount;
        }
    }
    return [debitAmount - creditAmount, debitAmount, creditAmount];
}
function getAmountMap(vtType: number, voucherLines: Array<BusinessVoucherTemplateLine>) {
    let amountMap: { [key: number]: number } = {};
    if (vtType === 501 || vtType === 502) {
        amountMap = getWarehousingAmount(voucherLines);
    } else if (vtType === 503 || vtType === 504) {
        amountMap = getSellDeliveryAmount(voucherLines);
    } else if (vtType === 512 || vtType === 513) {
        amountMap = getAssemblyAmount(vtType, voucherLines);
    } else if (vtType === 531 || vtType === 532) {
        amountMap = getInvoiceAmount(voucherLines);
    } else if ([517, 544, 545, 546, 547, 548, 549].includes(vtType)) {
        amountMap = getIncomeAmount(vtType, voucherLines);
    }
    return amountMap;
}
export function checkVoucherTemplateIsBalance(vtType: number, lines: Array<BusinessVoucherTemplateLine>) {
    const voucherLines = lines.slice();
    if ((vtType >= 505 && vtType <= 511) || [514, 515, 516].includes(vtType) || [541, 542].includes(vtType)) {
        return [0, 0, 0];
    }
    return checkLinesIsBalance(getAmountMap(vtType, voucherLines), voucherLines);
}

export function getUnBalanceExample(vtType: number, lines: Array<BusinessVoucherTemplateLine>) {
    const result: Array<string> = [];
    const billName = billVtTypeMap.find((item) => item.value === vtType)?.label || "";
    const amountMap = getAmountMap(vtType, lines);
    if (!Object.keys(amountMap).length) return { billName, result };
    for (const key in amountMap) {
        const valueType = ~~key as ValueType;
        const name = valueTypeMap[valueType] || "";
        result.push(name + Math.abs(amountMap[valueType]));
    }
    return { billName, result };
}

export function getNotBalanceTableData(
    vtType: number,
    lines: Array<BusinessVoucherTemplateLine>,
    headerList: Array<{ id: number; label: string }>,
    bodyList: Array<{ id: number; label: string }>,
    footerList: Array<{ id: number; label: string }>
) {
    const result: Array<ICheckBalanceResult> = [];
    if ((vtType >= 505 && vtType <= 511) || [514, 515, 516].includes(vtType) || [541, 542].includes(vtType)) {
        return result;
    }
    const amountMap = getAmountMap(vtType, lines);
    if (Object.keys(amountMap).length === 0) {
        return result;
    }
    for (const line of lines) {
        const { valueType, debit, asubName, description, customDescriptions } = line;
        let currentAmount = amountMap[valueType] || 0;
        // 优惠金额负数
        if (valueType === ValueType.DiscountAmount) {
            currentAmount = -currentAmount;
        }
        const debitAmount = debit === 1 ? currentAmount : 0;
        const creditAmount = debit === 1 ? 0 : currentAmount || 0;
        result.push({
            description: getDisplayDescFn({ row: { description, customDescriptions }, headerList, bodyList, footerList }),
            asubName,
            debit: debitAmount,
            credit: creditAmount,
        });
    }
    return result;
}
