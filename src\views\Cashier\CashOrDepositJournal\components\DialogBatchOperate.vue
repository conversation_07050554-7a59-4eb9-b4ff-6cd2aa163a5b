<template>
    <!-- 收支类别 -->
    <el-dialog v-model="ieTypeInfo.display" title="批量指定收支类别" center width="440" class="custom-confirm dialogDrag">
        <div class="ieType-content" v-dialogDrag>
            <div class="ieType-main">
                <div class="selected">
                    <span class="label">选择收支类别：</span>
                    <Select 
                        :teleported="false" 
                        v-model="ieTypeInfo.id"
                        :filterable="true"
                        @change="handleIeTypeChange"
                        :filter-method="ietypeFilterMethod"
                    >
                        <Option v-for="item in filterIEType" :key="item.subkey" :value="item.subkey" :label="item.value2"></Option>
                    </Select>
                </div>
                <div class="tip">
                    <span v-show="ieTypeInfo.text.startsWith('收')">注：只修改金额在收入（借方）的数据</span>
                    <span v-show="ieTypeInfo.text.startsWith('支')">注：只修改金额在支出（贷方）的数据</span>
                </div>
            </div>
            <div class="buttons">
                <a class="solid-button mr-5" @click="handleIETypeConfirm">确定</a>
                <a class="button" @click="handleIETypeCancel">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 往来单位 -->
    <DialogUnit ref="dialogUnitRef" :canAdd="false" @update:override="handleOverride" @handle-sure="handleUnitConfirm" />
    <!-- 项目 / 部门 -->
    <DialogBatchAAE
        ref="dialogAAEInfoRef"
        :aatype="aaTypeInfo"
        :name="aaTypeName"
        @handle-sure="handleAATypeConfirm"
        @update:override="handleOverride"
    />
    <!-- 摘要 -->
    <el-dialog v-model="descriptionInfo.display" title="批量修改摘要" center width="440" class="custom-confirm dialogDrag">
        <div class="description-content" v-dialogDrag>
            <div class="description-main">
                <span class="label">摘要：</span>
                <el-input
                    v-model="descriptionInfo.description"
                    type="textarea"
                    rows="4"
                    placeholder="请输入摘要"
                    resize="none"
                    @keydown="handleKeyDown"
                />
            </div>
            <div class="buttons">
                <a class="solid-button mr-10" @click="handleDescriptionConfirm">确定</a>
                <a class="button" @click="handleDescriptionCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, inject, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { payMethodKey } from "../utils";

import { CashAAType, type IPayMethod, type IUnitUpdateParams } from "../types";

import DialogUnit from "./DialogUnit.vue";
import DialogBatchAAE from "./DialogBatchAAE.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const getIETypeList = inject(payMethodKey) as () => Array<IPayMethod>;
const ieTypeList = ref<Array<IPayMethod>>([]);
const props = defineProps<{
    override: boolean;
}>();

const emit = defineEmits<{
    (event: "saveIEType", id: string): void;
    (event: "saveUnit", params: IUnitUpdateParams, aaType: number): void;
    (event: "saveAAType", id: string, aaType: CashAAType.Department | CashAAType.Project): void;
    (event: "update:override", value: boolean): void;
    (event: "saveDescription", description: string): void;
}>();

const override = computed({
    get() {
        return props.override;
    },
    set(val) {
        emit("update:override", val);
    },
});
function handleOverride(val: boolean) {
    override.value = val;
}

// 收支类别
const ieTypeInfo = reactive({
    display: false,
    id: "",
    text: "",
});
function handleIeTypeChange(id: string) {
    const ieType = ieTypeList.value.find((item) => item.subkey === id);
    ieTypeInfo.text = ieType ? ieType.value2 : "";
}
function handleIETypeConfirm() {
    if (!ieTypeInfo.id) {
        ElNotify({ message: "请选择收支类别", type: "warning" });
        return;
    }
    emit("saveIEType", ieTypeInfo.id);
    ieTypeInfo.display = false;
}
function handleIETypeCancel() {
    ieTypeInfo.display = false;
    ieTypeInfo.id = "";
    ieTypeInfo.text = "";
}

// 往来单位
const dialogUnitRef = ref<InstanceType<typeof DialogUnit>>();
function handleUnitConfirm(params: IUnitUpdateParams | null, aaType: number) {
    if (!params) return;
    dialogUnitRef.value?.changeDialogShow(false);
    emit("saveUnit", params, aaType);
}

// 项目 / 部门
const dialogAAEInfoRef = ref<InstanceType<typeof DialogBatchAAE>>();
const aaTypeInfo = ref<CashAAType.Department | CashAAType.Project>(CashAAType.Department);
const aaTypeName = computed(() => (aaTypeInfo.value === CashAAType.Department ? "部门" : "项目"));
function handleAATypeConfirm(id: string) {
    emit("saveAAType", id, aaTypeInfo.value);
}

// 摘要
const descriptionInfo = reactive({
    display: false,
    description: "",
});
function handleKeyDown(event: any) {
    const textarea = event.target as HTMLTextAreaElement;
    const allowKeys = ["Delete", "Backspace", "ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"];
    if (textarea.value.length >= 256 && !allowKeys.includes(event.key)) {
        ElNotify({ message: "亲，摘要不能超过256个字！", type: "warning" });
        event.preventDefault();
    }
}
function handleDescriptionConfirm() {
    descriptionInfo.description = descriptionInfo.description.trim();
    if (!descriptionInfo.description) {
        ElNotify({ message: "请输入摘要", type: "warning" });
        return;
    }
    if (descriptionInfo.description.length > 256) {
        ElNotify({ message: "亲，摘要不能超过256个字！", type: "warning" });
        return;
    }
    descriptionInfo.display = false;
    emit("saveDescription", descriptionInfo.description);
}
function handleDescriptionCancel() {
    descriptionInfo.display = false;
    descriptionInfo.description = "";
}

function handleOpenDialog(aatype: CashAAType) {
    switch (aatype) {
        case CashAAType.IEType:
            ieTypeList.value = getIETypeList();
            ieTypeInfo.id = ieTypeList.value[0].subkey;
            ieTypeInfo.text = ieTypeList.value[0].value2;
            ieTypeInfo.display = true;
            break;
        case CashAAType.Unit:
            dialogUnitRef.value?.handleInit();
            dialogUnitRef.value?.changeDialogShow(true);
            break;
        case CashAAType.Department:
        case CashAAType.Project:
            aaTypeInfo.value = aatype;
            nextTick().then(() => {
                dialogAAEInfoRef.value?.handleInit();
            });
            break;
        case CashAAType.Description:
            descriptionInfo.description = "";
            descriptionInfo.display = true;
            break;
    }
}
defineExpose({ handleOpenDialog });

//下拉组件拼音首字母搜索
const filterIEType = ref<Array<IPayMethod>>([]);
watchEffect(() => { 
    filterIEType.value = JSON.parse(JSON.stringify(ieTypeList.value));  
});
function ietypeFilterMethod(value: string) {
    filterIEType.value = commonFilterMethod(value, ieTypeList.value, 'value2');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
// 收支类别
.ieType-content {
    .ieType-main {
        .selected {
            padding: 24px 24px 24px 40px;
            color: #404040;
            font-size: 12px;
            span.label {
                font-size: 14px;
                font-weight: 600;
            }
            .detail-el-select(174px, 32px);
        }
        .tip {
            padding: 24px 24px 24px 40px;
            height: 24px;
            color: #404040;
            font-size: 12px;
            font-weight: 600;
        }
    }
    .buttons {
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid var(--border-color);
    }
}
// 摘要
.description-content {
    .description-main {
        padding: 20px 60px;
        display: flex;
        align-items: center;
        span.label {
            width: 60px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
