<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">科目管理</div>
                    <el-tabs v-model="activeName" class="demo-tabs">
                        <el-tab-pane v-for="(item, index) in tabList" :label="item.label" :name="item.name" :key="index"> </el-tab-pane>
                    </el-tabs>
                    <SubjectHeaders
                        :showDisabled="showDisabled"
                        :newSubjectBtnIsShow="newSubjectBtnIsShow"
                        @addSubjectRoot="addHandle({ parent_asub_name: '', currentLevel: 1, codeLengthRule: '', accountCategory: '' }, 0)"
                        @disabledHandle="disabledHandle"
                        @codeLengthHandle="codeLengthHandle"
                        @exportHandle="exportHandle"
                        @importHandle="importHandle"
                        @showAsubToAssist="showAsubToAssist"
                        @bulkOperation="bulkOperation"
                        @search="search"
                    >
                    </SubjectHeaders>
                    <div class="main-center">
                        <keep-alive>
                            <Table
                                class="subject-table"
                                ref="accountSubjectTableRef"
                                :data="virtuallyData"
                                :header-select-none="true"
                                :columns="Columns"
                                :fit="true"
                                height="100%"
                                v-loading="loading"
                                :empty-text="emptyText"
                                :scrollbar-show="true"
                                :highlight-current-row="false"
                                :row-class-name="rowStyleHandle"
                                :selectable="setSelectable"
                                :show-overflow-tooltip="true"
                                @selection-change="tableSelectionChange"
                                @scroll="handleScroll"
                                :tableName="setModule"
                                :isSubjectPage="true"
                            >
                                <template #selection>
                                    <el-table-column width="36px" align="center" props="isChecked">
                                        <template #default="scope">
                                            <el-checkbox
                                                v-model="scope.row.isChecked"
                                                v-if="scope.row.operation !== 0"
                                                @change="tableRowSelectedNew(scope.row)"
                                            />
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #code>
                                    <el-table-column 
                                        label="科目编码" 
                                        min-width="120px"
                                        prop="code" 
                                        :width="getColumnWidth(setModule, 'code')" 
                                        align="left" 
                                        header-align="left"
                                    >
                                        <template #default="scope">
                                            <span :class="setCodeClass(scope.row.asubLevel)">{{ scope.row.asubCode }}</span>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #name>
                                    <el-table-column 
                                        label="科目名称" 
                                        min-width="180px" 
                                        align="left" 
                                        header-align="left"
                                        prop="name"
                                        :width="getColumnWidth(setModule, 'name')" 
                                    >
                                        <template #default="scope">
                                            <span :class="setCodeClass(scope.row.asubLevel)">{{ scope.row.asubName }}</span>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #operator>
                                    <el-table-column 
                                        label="操作" 
                                        align="left" 
                                        min-width="90px" 
                                        header-align="left"
                                        :resizable="false"
                                    >
                                        <template #default="scope">
                                            <!-- 用 v-permission="['accountsubject-candelete']" 会出vue错误-->
                                            <a
                                                class="link-option pr-10"
                                                v-if="
                                                    (scope.row.operation & 1) === 1 &&
                                                    useAccountSetStoreHook().permissions.includes('accountsubject-canedit')
                                                "
                                                @click="addHandle(scope.row, 1)"
                                                >新增</a
                                            >
                                            <a
                                                class="link-option pr-10"
                                                v-if="
                                                    ((scope.row.operation & 2) === 2 ||
                                                        (scope.row.operation === 0 && !['3101', '3102'].includes(scope.row.asubCode))) &&
                                                    useAccountSetStoreHook().permissions.includes('accountsubject-canedit')
                                                "
                                                @click="editHandle(scope.row)"
                                                >编辑</a
                                            >
                                            <a
                                                class="link-option"
                                                v-if="
                                                    (scope.row.operation & 4) === 4 &&
                                                    useAccountSetStoreHook().permissions.includes('accountsubject-candelete')
                                                "
                                                @click="deleteHandle(scope.row, '1')"
                                                >删除</a
                                            >
                                        </template>
                                    </el-table-column>
                                </template>
                            </Table>
                        </keep-alive>
                    </div>
                </div>
            </template>

            <template #add>
                <div class="slot-content align-center">
                    <AddSlot
                        ref="addSlot"
                        :currentRow="currentRow"
                        :addTitle="addTitle"
                        :subjectCode="subjectCode"
                        :curTableData="noSearchTableData[activeName]"
                        :codeLengthRule="asubCodeLength"
                        @addCancel="addCancel"
                        @submitAdd="submitAdd"
                        @updateAllData="updateNoSearchTableData"
                        :first-code-length="asubCodeLength?.firstCodeLength"
                        :currentSlot="currentSlot"
                    ></AddSlot>
                </div>
            </template>
        </ContentSlider>

        <el-dialog
            v-model="openCodeLengthDialog"
            title="科目编码设置"
            center
            destroy-on-close
            :width="isThirdPart && !isProSystem ? '400px':'560px'"
            class="custom-confirm dialogDrag"
        >
            <div class="subjectCodeSet" v-dialogDrag>
                <div class="codeLength">
                    <div class="code-length-main">
                        <div v-show="!(isThirdPart && !isProSystem)" class="input mt-5">
                            <div class="input-title">   <img
                                class="vip-icon"
                                v-show="!isProSystem && !isErp && !isThirdPart"
                                src="@/assets/Menu/vip-icon.png"
                            />科目级次：</div>
                            <div class="input-content">
                                <div class="code-length-edit-select level">
                                    <el-select
                                        v-model="accountLevel"
                                        :suffix-icon="CaretBottom"
                                        :fit-input-width="true"
                                        tabindex="-1"
                                        :teleported="false"
                                        class="code-length-select"
                                        popper-class="code-level-popper"
                                    >
                                        <el-option :value="4">4</el-option>
                                        <el-option :value="5">5</el-option>
                                        <el-option :value="6">6</el-option>
                                        <el-option :value="7">7</el-option>
                                        <el-option :value="8">8</el-option>
                                        <el-option :value="9">9</el-option>
                                        <el-option :value="10">10</el-option>
                                    </el-select>
                                </div>
                                <span>
                                    <span class="highlight-red pl-20">注：</span>
                                    <span>科目级次调大后，只能调小到已增加科目的最大级次</span>
                                </span>
                            </div>
                        </div>
                        <div class="input mt-10">
                            <div class="input-title">编码长度：</div>
                            <div class="code-level-list">
                                <div class="input-content" v-for="(item, index) in accountLevel" :key="item">
                                    <div class="code-length-edit-select">
                                        <el-select
                                            v-model="code[index]"
                                            :suffix-icon="CaretBottom"
                                            :fit-input-width="true"
                                            :disabled="index === 0"
                                            tabindex="-1"
                                            :teleported="false"
                                            placeholder=" "
                                            class="code-length-select"
                                        >
                                            <el-option :value="2">2</el-option>
                                            <el-option :value="3">3</el-option>
                                            <el-option :value="4">4</el-option>
                                        </el-select>
                                    </div>
                                    <span v-show="item !== accountLevel" class="split-line pl-5 pr-5">-</span>
                                </div>
                            </div>
                        </div>
                        <div class="txt mt-10">
                            <span class="highlight-green">示例：</span
                            ><span id="spanExample" class="highlight-green"
                                >{{ accountStandard > 3 ? 101 : 1001 }}
                                <span v-for="(item, index) in accountLevel" :key="item">
                                    <span v-if="index > 0"> {{ getCodeExample(code[index]) }}&nbsp; </span>
                                </span>
                            </span>
                        </div>
                    </div>
                    <div v-if="!isErp" class="buttons">
                        <a class="button ml-10" @click="hideCodeLength">取消</a>
                        <a
                            :class="['button', 'solid-button', ' ml-10', isFirstClick ? '' : 'disabled']"
                            id="submitCodeLength"
                            @click="changeCodeLength"
                            >确定</a
                        >
                    </div>
                    <div v-else class="buttons">
                        <a
                            :class="['button', 'solid-button', ' ml-10', isFirstClick ? '' : 'disabled']"
                            id="submitCodeLength"
                            @click="changeCodeLength"
                            >确定</a
                        >
                        <a class="button ml-10" @click="hideCodeLength">取消</a>
                    </div>
                </div>
            </div>
        </el-dialog>

        <ImportSingleFileDialog
            :importTitle="'导入科目'"
            v-model:import-show="importShow"
            :importUrl="'/api/AccountSubject/Import'"
            :uploadSuccess="uploadSuccess"
            :need-loading="true"
        >
            <template #top-tips>
                <div style="margin-left: 40px; margin-top: 20px" v-show="!isErp && !isHideBarcode">
                    <a @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=*********')" class="link"
                        >不会操作？点此观看视频
                    </a>
                </div>
            </template>
            <template #download>
                <div>1.请选择下面任意一种方式导入科目</div>
                <div class="mt-10 ml-10">(1)在科目列表导出所需数据，确认后直接导入</div>
                <div class="mt-10 ml-10">
                    (2)点击下载模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="downloadExcel">下载模板</a>
                </div>
            </template>
            <template #import-content>
                <span>2.选择文件导入</span>
            </template>
        </ImportSingleFileDialog>
        <el-dialog
            ref="transferAuxiliaryRef"
            v-model="transferAuxiliary"
            center
            width="428px"
            class="transfer-auxiliary-dialog custom-confirm dialogDrag"
        >
            <template #header="{ titleId, titleClass }">
                <div class="my-header">
                    <span :id="titleId" :class="titleClass">明细科目转辅助核算</span>
                    <template v-if="!isHideBarcode">
                        <el-tooltip effect="light" placement="top">
                            <template #content>
                                <a @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/handle?subMenuId=110109')" class="link"
                                    >点击查看帮助</a
                                >
                            </template>
                            <img
                                class="transfer-auxiliary-help-icon"
                                src="@/assets/Icons/question.png"
                                alt=""
                                @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/handle?subMenuId=110109')"
                            />
                        </el-tooltip>
                    </template>
                </div>
            </template>
            <div id="injectTable" title="" class="panel-body panel-body-noborder window-body" v-dialogDrag>
                <div id="message" class="inject-message">
                    <div class="asub-to-assist-main">
                        <div class="asub-to-assist-info">
                            请选择需要转换的科目：(1.只能选非末级科目；2.该科目下的所有明细科目将全部转为辅助核算)
                        </div>
                        <div class="asub-to-assist-select">
                            <span style="width: 80px"> 科目： </span>
                            <Select
                                v-model="asubChecked"
                                :fit-input-width="true"
                                :teleported="true"
                                :suffix-icon="CaretBottom"
                                :placeholder="asubList[0]?.text"
                                :filterable="true"
                                :filter-method="asubFilterMethod"
                                :remote="true"
                                :remote-show-suffix="true"
                            >
                                <Option 
                                    v-for="(item, index) in showAsubList" 
                                    :label="item.text" 
                                    :value="item.value" 
                                    :key="index" 
                                />
                            </Select>
                        </div>
                        <div class="asub-to-assist-info">请选择该科目需要设置的辅助核算：</div>
                        <div class="asub-to-assist-select">
                            <span style="width: 80px"> 辅助核算：</span>
                            <Select
                                v-model="assistChecked"
                                :fit-input-width="true"
                                :teleported="true"
                                :suffix-icon="CaretBottom"
                                :placeholder="assistList[0]?.text"
                                :filterable="true"
                                :filter-method="assistFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showAssistList" 
                                    :label="item.text" 
                                    :value="item.value" 
                                    :key="item.value" 
                                />
                            </Select>
                        </div>
                        <div class="asub-to-assist-tip">
                            <img src="@/assets/Icons/warn.png" />科目的下级含有数量、外币、辅助核算时，不支持转换哦~
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" id="finishBtn" @click="submitTransfer">确定</a>
                        <a class="button ml-10" id="finishBtn" @click="() => (transferAuxiliary = false)">取消</a>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="transferPrompt" title="提示" center width="440px" class="custom-confirm dialogDrag">
            <div id="injectTable" title="" class="panel-body panel-body-noborder window-body" v-dialogDrag>
                <div id="message" class="inject-message">
                    <div class="asub-to-assist-confirm-main">
                        <div class="asub-to-assist-confirm-title">是否立即转换？</div>
                        <div class="asub-to-assist-confirm-info">
                            <div class="yellow ellipsis" id="asubToAssistAsubInfo">
                                <Tooltip
                                    :content="(asubList.find((item) => item.value === asubChecked) as IAsubListItem).text"
                                    :maxWidth="365"
                                    :lineClamp="1"
                                    placement="right"
                                >
                                    {{ (asubList.find((item) => item.value === asubChecked) as IAsubListItem).text }}
                                </Tooltip>
                            </div>
                            下所有的<span class="yellow">明细科目</span>将转换为<span class="yellow"
                                >辅助核算(<span id="asubToAssistAssistInfo">{{
                                    (assistList.find((item) => item.value === assistChecked) as IAsubListItem).text
                                }}</span
                                >)</span
                            >，此操作将更新所有凭证
                        </div>
                        <div class="asub-to-assist-confirm-tip aa-hidden">
                            <img src="@/assets/Icons/warn.png" />
                            <div>
                                <span>
                                    {{ accountSetBackupTips() }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" id="finishBtn" @click="submitPromptTransfer">转换</a>
                        <a class="button ml-10" id="finishBtn" @click="() => (transferPrompt = false)">取消</a>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="stopDialog" title="提示" center width="428px" class="custom-confirm dialogDrag">
            <div id="injectTable" title="" class="panel-body panel-body-noborder window-body" v-dialogDrag>
                <div id="message" class="inject-message">
                    <div class="asub-disbaled-confirm-main">
                        <div class="asub-disbaled-confirm-info">亲，科目停用后录入凭证时将选择不到该科目，是否确认停用？</div>
                        <div class="asub-disbaled-confirm-tip">
                            <img src="@/assets/Icons/warn.png" />
                            <div>注：勾选上级科目时会自动勾选其下级科目，并一起停用</div>
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" id="finishBtn" @click="bulkSubmit('BatchDisabled')">确定</a>
                        <a class="button ml-10" id="finishBtn" @click="() => (stopDialog = false)">取消</a>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="deleteSuccessTips" title="科目删除" center width="428px" class="custom-confirm dialogDrag">
            <div id="injectTable" title="" class="panel-body panel-body-noborder window-body" v-dialogDrag>
                <div id="message" class="inject-message">
                    <div class="asub-disbaled-confirm-main">
                        <div class="asub-disbaled-confirm-info">删除成功{{ deleteSuccess }}个，失败{{ deleteFail }}个</div>
                        <div class="asub-delete-fail-tip asub-disbaled-confirm-tip" v-for="item in deleteResData" :key="item">
                            {{ item }}
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" id="finishBtn" @click="() => (deleteSuccessTips = false)">确定</a>
                    </div>
                </div>
            </div>
        </el-dialog>
        <el-dialog v-model="bulkSetAADialog" title="批量设置辅助核算" center width="560px" class="custom-confirm dialogDrag">
            <div id="injectTable" title="" class="panel-body panel-body-noborder window-body" v-dialogDrag>
                <div id="message" class="inject-message">
                    <div class="asub-disbaled-confirm-main">
                        <div class="asub-disbaled-confirm-info">您选择了{{ checkedTableData.length }}个科目，请勾选需要设置的辅助核算:</div>
                        <div class="asub-disbaled-confirm-tip pl-20">
                            <el-checkbox-group v-model="aaTypesChecked">
                                <el-checkbox v-for="item in aaTypeList" :key="item.code" :label="item.code">{{ item.name }}</el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div class="line-item-field" style="height: 40px; line-height: 40px">
                            <a class="link" @click="globalWindowOpenPage('/Settings/AssistingAccounting?aaType=10000', '辅助核算')"
                                >设置自定义类别</a
                            >
                        </div>
                        <div class="line-item3" v-show="aaTypesChecked.length" style="height: auto">
                            <div class="line-item-title" id="trAssist" style="line-height: 32px">
                                <span>设置非必录项</span>
                            </div>
                            <div class="line-item-field aa-types-check-list">
                                <el-checkbox-group class="float-l pl-20 mb-20" style="width: 100%" v-model="aaTypesAllowNullChecked">
                                    <el-checkbox v-for="item in aaTypeAllowNullList" :key="item.code" :label="item.code">{{
                                        item.name
                                    }}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                        <div class="line-item-field mt-20">
                            非末级科目、已使用的科目、已设置辅助核算的科目不支持批量设置辅助核算，将会跳过哦~
                        </div>
                    </div>

                    <div class="buttons">
                        <a class="button solid-button" id="finishBtn" @click="bulkSubmit('BatchSetAA')">确定</a>
                        <a class="button ml-10" id="finishBtn" @click="() => (bulkSetAADialog = false)">取消</a>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "AccountSubject",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import Table from "@/components/Table/index.vue";
import AddSlot from "./components/AddSlot.vue";
import SubjectHeaders from "./components/SubjectHeaders.vue";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { globalExport, globalWindowOpen } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { ref, onMounted, onUnmounted, reactive, nextTick, computed, onActivated, watch, watchEffect } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { CaretBottom } from "@element-plus/icons-vue";
import Option from "@/components/Option/index.vue";
import { useLoading } from "@/hooks/useLoading";
import Select from "@/components/Select/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import Tooltip from "@/components/Tooltip/index.vue";
import { globalWindowOpenPage } from "@/util/url";
import { accountSetBackupTips } from "@/util/showTips";
import { getColumnWidth } from "@/components/ColumnSet/utils";

import type {
    ITableTreeData,
    ITableDataItem,
    ITableTreeDataState,
    Irow,
    ITabListItem,
    ICurrencyResItem,
    IAsubListItem,
    ICheckedItem,
} from "./types";
import { asubTypeCode, setCodeClass, getCodeExample, getAccountSubjectTabList } from "./utils";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import type { IAssistingAccountType } from "@/api/assistingAccounting";
import { useAsubCodeLengthStore } from "@/store/modules/asubCodeLength";
import { dangerousOperationNext } from "@/util/autoBackup";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const _ = getGlobalLodash();
const setModule = "SetAccountSubject";
// 科目级次
const slots = ["main", "add", "edit"];
const currentSlot = ref("main");
const activeName = ref("aasj");
const showDisabled = ref(true);
const openCodeLengthDialog = ref(false);
const codeLength = computed(() => {
    return useAsubCodeLengthStore().codeLength;
});
const code = ref(_.cloneDeep(codeLength.value));
const importShow = ref(false);
const asubToAssistShow = ref(false);
const addSlot = ref();
let deleteSuccessTips = ref(false);

const tabList = ref<ITabListItem[]>([]);
let tableData = reactive<any>({
    aasj: {
        data: [],
        state: "astate",
    },
    dasj: {
        data: [],
        state: "dstate",
    },
    cmasj: {
        data: [],
        state: "cmstate",
    },
    oasj: {
        data: [],
        state: "ostate",
    },
    casj: {
        data: [],
        state: "cstate",
    },
    iasj: {
        data: [],
        state: "istate",
    },
    nasj: {
        data: [],
        state: "nastate",
    },
    resj: {
        data: [],
        state: "restate",
    },
    exsj: {
        data: [],
        state: "exstate",
    },
    dissj: {
        data: [],
        state: "disstate",
    },
});
const Columns = ref();
const accountSetStore = useAccountSetStore();
const accountSet = accountSetStore.accountSet;
const accountStandard = accountSet!.accountingStandard;
tabList.value = getAccountSubjectTabList();

let currentActiveType: { [key: string]: string } = reactive({
    aasj: "资产类",
    dasj: "负债类",
    cmasj: "共同类",
    oasj: "权益类",
    casj: "成本类",
    iasj: "损益类",
    nasj: "净资产类",
    resj: "收入类",
    exsj: "费用类",
    dissj: "支出类",
});
let aaTypesChecked = ref<string[]>([]);
let aaTypesAllowNullChecked = ref<string[]>([]);
let aaTypeAllowNullList = ref<Array<any>>([]);
watch(aaTypesChecked, (newVal: string[]) => {
    if (newVal.length === 6) {
        newVal.pop();
        ElNotify({
            type: "warning",
            message: "您最多只能设置5个辅助核算类别",
        });
    }
    aaTypeAllowNullList.value = aaTypeList.value.filter((item: ICheckedItem) => newVal.includes(item.code));
});
const aaTypeList = computed(() => {
    const list: Array<{ name: string, code: string }> = [];
    for (let i = 0; i < useAssistingAccountingStore().assistingAccountingTypeList.length; i++) {
        const aaItem = useAssistingAccountingStore().assistingAccountingTypeList[i];
        // 批量设置不允许选择现金流
        if (aaItem.aaType !== 10007) {
            list.push({
                name: aaItem.aaTypeName,
                code: String(aaItem.aaType),
            });
        }
    }
    return list;
});
// 是否是专业版
let isProSystem = ref<boolean>(window.isProSystem);
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart);
let accountLevel = ref(isProSystem.value || isErp.value ? codeLength.value.length : 4);
watch(accountLevel, (level: number) => {
    for (let i = 0; i < level; i++) {
        if (!code.value[i]) {
            code.value[i] = 2;
        }
    }
});

// 新增科目按钮是否显示
let newSubjectBtnIsShow = ref(accountStandard !== 1 || window.isProSystem || window.isErp);

function setColumns(columnsBool: ITableTreeDataState, columns: Array<IColumnProps>) {
    columns = [
        { slot: "selection", width: 36, headerAlign: "center", align: "center" },
        { label: "序号", prop: "index", minWidth: 36, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "index") },
        { slot: "code" },
        { slot: "name" },
        { label: "助记码", prop: "acronym", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "acronym") },
        {
            label: "余额方向",
            prop: "direction",
            align: "left",
            minWidth: 50,
            headerAlign: "left",
            formatter: (row: any) => {
                if (row.direction == 1) {
                    return "借";
                } else if (row.direction == 2) {
                    return "贷";
                } else {
                    return "未知";
                }
            },
            width: getColumnWidth(setModule, "direction")
        },
        {
            label: "状态",
            prop: "status",
            align: "left",
            minWidth: 50,
            headerAlign: "left",
            formatter: (row: any) => {
                if (row.status == 0) {
                    return "启用";
                } else if (row.status == 1) {
                    return "停用";
                } else {
                    return "未知";
                }
            },
            width: getColumnWidth(setModule, "status")
        },
        { slot: "operator" },
    ];
    if (columnsBool.isFC) {
        columns.splice(6, 0, {
            label: "外币核算",
            prop: "foreigncurrency",
            align: "left",
            headerAlign: "left",
            formatter: (row: any): any => {
                if (row.foreigncurrency === 0) {
                    return " ";
                } else if (row.foreigncurrency === 1) {
                    return getCurrency(row.fcIds);
                }
            },
            width: getColumnWidth(setModule, "foreigncurrency")
        });
    }

    if (columnsBool.isAssit) {
        columns.splice(6, 0, { 
            label: "辅助核算", 
            minWidth: 100, 
            prop: "aatypeNames", 
            align: "left", 
            headerAlign: "left", 
            width: getColumnWidth(setModule, "aatypeNames") 
        });
    }
    if (columnsBool.isQuant) {
        columns.splice(6, 0, { label: "数量核算", prop: "measureUnit", align: "left", headerAlign: "left", width: getColumnWidth(setModule, "measureUnit") });
    }
    if (accountStandard === 3 && activeName.value === "resj") {
        columns.splice(6, 0, {
            label: "收入属性",
            prop: "restricted",
            align: "left",
            headerAlign: "left",
            formatter: (row: any, col: any, value): any => {
                if (row.isLeafNode && value === 0) {
                    return "非限定性";
                } else if (row.isLeafNode && value === 1) {
                    return "限定性";
                }
            },
            width: getColumnWidth(setModule, "restricted")
        });
    }
    return columns;
}
function getCurrency(fcIds: string) {
    let fcList = addSlot.value?.fcList;
    let targetArr: string[] = [];

    fcList
        ?.filter((item: ICurrencyResItem) => fcIds.split(",").includes(String(item.id)))
        .forEach((item: any) => targetArr.push(item.name));
    return targetArr.join("/");
}
let tableAllData: any = reactive({});
let loading = ref<boolean>(false);
let emptyText = ref("");

let searchInfo = ref("");
let virtuallyData = ref<ITableDataItem[]>([]);
let tableDataCurrent = ref<ITableDataItem[]>([]);
let startIndex = ref(0);
let endIndex = ref(20);
let tableBodyHeight = ref(0);
let rowHeight = window.isErp ? 44 : 37;
let noSearchTableData = ref();
function updateNoSearchTableData() {
    request({
        url: `/api/AccountSubject/Tree?asType=20&showDisabled=${showDisabled.value}&searchInfo=`,
        method: "get",
    }).then((res: IResponseModel<ITableTreeData>) => {
        noSearchTableData.value = _.cloneDeep(res.data);
    });
}
function getTableData() {
    loading.value = true;
    request({
        url: `/api/AccountSubject/Tree?asType=20&showDisabled=${showDisabled.value}&searchInfo=${encodeURIComponent(
            searchInfo.value || ""
        )}`,
        method: "get",
    }).then((res: IResponseModel<ITableTreeData>) => {
        tableAllData = {};
        loading.value = false;
        if (res.state === 1000) {
            tableAllData = res.data;
            tableData[activeName.value].data = tableAllData[activeName.value].map((item: ITableDataItem, index: number) => {
                return { ...item, index: index + 1, isChecked: false };
            });
            !searchInfo.value && (noSearchTableData.value = _.cloneDeep(tableAllData));
            if (!tableData[activeName.value].data.length) {
                emptyText.value = "暂无数据";
            }
            tableDataCurrent.value = _.cloneDeep(tableData[activeName.value].data);
            // 总列表长度
            tableBodyHeight.value = tableData[activeName.value].data.length * rowHeight;
            const virtualBody = accountSubjectTableRef.value?.$el.querySelector(".my-table-virtual");
            virtualBody.style.height = tableBodyHeight.value + "px";

            const tableBody = accountSubjectTableRef.value?.$el.querySelector(".el-scrollbar__wrap");
            let scrollTop = tableBody.scrollTop;
            const tableElView = accountSubjectTableRef.value?.$el.querySelector(".el-table__body");
            const bodyHeight = tableBody.offsetHeight;
            // 列表一页最大展示数据
            const visibleRows = Math.ceil(bodyHeight / rowHeight); // 可见的行数
            //民非净资产科目科目列表未超过屏幕（不用分页）
            if (tableData[activeName.value].data.length > visibleRows) {
                virtuallyData.value = tableData[activeName.value].data.slice(Math.max(0, startIndex.value), endIndex.value);
            } else {
                virtuallyData.value = tableData[activeName.value].data;
            }
            Columns.value = setColumns(tableAllData[tableData[activeName.value].state], Columns.value);
        }
    });
}
const tableHeight = ref(0); // 表格的高度
const visibleRows = ref(0); // 可见的行数
onMounted(() => {
    nextTick(() => {
        const newDivElement = document.createElement("div");
        newDivElement.classList.add("my-table-virtual");
        const childElement = accountSubjectTableRef.value?.$el.querySelector(".el-table__body");
        const parentElement = childElement.parentNode;
        parentElement.parentNode.insertBefore(newDivElement, parentElement);
        newDivElement.appendChild(childElement);
        tableHeight.value = accountSubjectTableRef.value?.$el.querySelector(".el-scrollbar__wrap").offsetHeight;
        visibleRows.value = Math.ceil(tableHeight.value / rowHeight);
        endIndex.value = startIndex.value + visibleRows.value * 2;
        getTableData(); // 初始化加载数据
    });
    window.addEventListener("reloadAccountSubjectTree", getTableData);
});
let tableScrollTop = 0;
function handleScroll() {
    const tableBody = accountSubjectTableRef.value?.$el.querySelector(".el-scrollbar__wrap");
    let scrollTop = tableBody.scrollTop;
    const tableElView = accountSubjectTableRef.value?.$el.querySelector(".el-table__body");
    const bodyHeight = tableBody.offsetHeight;
    // 列表一页最大展示数据
    const visibleRows = Math.ceil(bodyHeight / rowHeight); // 可见的行数

    tableElView.style.transform = `translateY(${scrollTop - (scrollTop % rowHeight)}px)`;

    startIndex.value = Math.min(Math.floor(scrollTop / rowHeight), Math.max(0, tableData[activeName.value].data.length - visibleRows));
    endIndex.value = Math.min(startIndex.value + visibleRows * 2, tableData[activeName.value].data.length);
    startIndex.value = startIndex.value > 0 ? startIndex.value : 0; //防止取到负数
    virtuallyData.value = tableData[activeName.value].data.slice(startIndex.value, endIndex.value);
    tableScrollTop = scrollTop;
}
onActivated(() => {
    const tableBody = accountSubjectTableRef.value?.$el.querySelector(".el-scrollbar__wrap");
    tableBody.scrollTop = tableScrollTop;
});
watch(activeName, () => {
    tableData[activeName.value].data = tableAllData[activeName.value].map((item: ITableDataItem, index: number) => {
        return { ...item, index: index + 1, isChecked: false };
    });
    if (!tableData[activeName.value].data.length) {
        emptyText.value = "暂无数据";
    }
    accountSubjectTableRef.value?.getTable()?.setScrollTop(0);
    startIndex.value = 0;
    endIndex.value = Math.min(startIndex.value + visibleRows.value * 2, tableData[activeName.value].data.length);
    virtuallyData.value = tableData[activeName.value].data.slice(startIndex.value, endIndex.value);
    tableBodyHeight.value = tableData[activeName.value].data.length * rowHeight;
    const virtualBody = accountSubjectTableRef.value?.$el.querySelector(".my-table-virtual");
    virtualBody.style.height = tableBodyHeight.value + "px";
    Columns.value = setColumns(tableAllData[tableData[activeName.value].state], Columns.value);
});
watch(accountLevel, (v: number) => {
    code.value = code.value.slice(0, v);
});
let checkedTableData = computed<ITableDataItem[]>(() => {
    return tableData[activeName.value].data.filter((v: ITableDataItem) => v.isChecked);
});
// 表格行数据被选
let accountSubjectTableRef = ref<InstanceType<typeof Table>>();
function tableRowSelectedNew(row: ITableDataItem) {
    parentIds = [];
    let status = row.isChecked;
    // 上级选中下级自动选中，下级取消，上级也取消
    status ? findChild(row) : findParent(row.parentId);
    if (status) {
        nextTick(() => {
            checkedTableData.value.forEach((item: ITableDataItem) => {
                setSelectedClassName(item.asubId);
            });
        });
    } else {
        removeSelectedClassName(row.asubId);
        nextTick(() => {
            parentIds.forEach((asubId) => {
                removeSelectedClassName(asubId);
            });
        });
    }
}
function findChild(row: ITableDataItem) {
    tableData[activeName.value].data
        .filter((v: ITableDataItem) => v.asubCode.startsWith(row.asubCode.trim()))
        .forEach((item: ITableDataItem, index: number) => {
            item.isChecked = true;
        });
}
function findParent(cParentId: number) {
    if (cParentId === 0) return;
    tableData[activeName.value].data.forEach((item: ITableDataItem) => {
        if (item.asubId === cParentId) {
            item.isChecked = false;
            findParent(item.parentId);
        }
    });
}
let parentIds: number[] = [];
function findParent2(cParentId: number) {
    if (cParentId === 0) return;
    tableData[activeName.value].data.forEach((item: ITableDataItem) => {
        if (item.asubId === cParentId) {
            parentIds.push(item.asubId);
            findParent2(item.parentId);
        }
    });
}
function setSelectedClassName(asubId: number) {
    let selectedRow = accountSubjectTableRef.value?.$el.querySelector(".row-id-" + asubId);

    selectedRow.classList.add("row-selected");
}
function removeSelectedClassName(asubId: number) {
    let selectedRow = accountSubjectTableRef.value?.$el.querySelector(".row-id-" + asubId);
    selectedRow.classList.remove("row-selected");
}
function tableSelectionChange(currentRow: any) {
    // checkedTableData.value = currentRow;
}
function disabledHandle(data: any) {
    showDisabled.value = data;
    getTableData();
}
function codeLengthHandle() {
    openCodeLengthDialog.value = true;
    isFirstClick.value = true;
    useAsubCodeLengthStore().getAsubCodeLength();
}
function hideCodeLength() {
    openCodeLengthDialog.value = false;
}
let asubCodeLength = computed(() => {
    return _.cloneDeep(useAsubCodeLengthStore().allCodeLengthInfo);
});
let isFirstClick = ref(false);
const changeCodeLength = _.throttle(
    () => {
        changeCodeLengthFn();
    },
    1000,
    { leading: true, trailing: false }
);
function changeCodeLengthFn() {
    if (!window.isProSystem && !window.isErp && accountLevel.value > 4) {
        handleTrialExpired({ msg: ExpiredToBuyDialogEnum.modifySubjectCodeLevel, needExpired: false });
        accountLevel.value = 4;
        code.value = _.cloneDeep(codeLength.value);
        return;
    }
    isFirstClick.value = false;
    request({
        url: `/api/AccountSubject/ChangeSubjectCodeLength`,
        params: { codeLength: code.value.join("") },
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.data) {
            openCodeLengthDialog.value = false;
            ElNotify({
                type: "success",
                message: "保存成功!",
            });
            useAccountSubjectStore().getAccountSubject();
            window.dispatchEvent(new CustomEvent("updateAccountSubjectCodeLength"));
            getTableData();
            useAsubCodeLengthStore().getAsubCodeLength();
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
            isFirstClick.value = true;
        }
    });
}
function exportHandle() {
    globalExport(`/api/AccountSubject/Export?type=4&fileName=科目列表`);
}
function importHandle() {
    importShow.value = true;
}
function downloadExcel() {
    globalExport(`/api/AccountSubject/ExportTemplate`);
}

const uploadSuccess = (res: IResponseModel<string>) => {
    checkedTableData.value.forEach((item) => {
        removeSelectedClassName(item.asubId);
    });
    if (res.state === 1000) {
        ElNotify({
            type: "success",
            message: "导入成功！",
        });
        useAccountSubjectStore().getAccountSubject();
        importShow.value = false;
        getTableData();
    } else {
        if (res.msg.includes("成功导入")) {
            importShow.value = false;
            ElConfirm(res.msg, true, () => {}, "导入科目", undefined, true);
            getTableData();
            useAccountSubjectStore().getAccountSubject();
        } else {
            if (res.msg.includes("</br>")) {
                importShow.value = false;
                ElConfirm(res.msg, true, () => {}, "导入科目", undefined, true);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        }
    }
};

// 转辅助弹框
let transferAuxiliary = ref(false);
let transferPrompt = ref(false);
const asubList = ref<IAsubListItem[]>([]);
const assistList = ref<IAsubListItem[]>([]);
let asubChecked = ref();
let assistChecked = ref();
let transferAuxiliaryRef = ref();
// 弹框点确认
const submitTransfer = () => {
    if (!asubChecked.value) {
        ElNotify({
            type: "warning",
            message: "亲，请先选择科目哦~",
        });
        return;
    }
    request({
        url: `/api/AccountSubject/CheckAsubToAssist?asubId=${asubChecked.value}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                transferAuxiliary.value = false;
                transferPrompt.value = true;
            } else {
                transferAuxiliary.value = false;
                ElConfirm(res.msg, false, () => {}, "提示", undefined, true).then(() => {
                    transferAuxiliary.value = true;
                });
            }
        })
        .catch((e) => {});
};
async function checkConvertErpMessageAlert() {
    let result = false;
    await request({
        url: "/api/AccountSubject/CheckConvertErpMessageAlert",
        params: { asubId: asubChecked.value, aaType: assistChecked.value },
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 || res.state === 2000) {
            if (res.data) {
                ElConfirm(res.msg).then(async (r) => {
                    //已发生业务弹窗
                    if(res.msg.includes("已发生业务") && r) {
                        const canNext = await dangerousOperationNext();
                        if (!canNext) return
                        convertAsubIntoAssist();
                    }
                    result = r;
                });
            } else {
                result = true;
            }
        }
    });
    return result;
}
let canTransfer = true;
const submitPromptTransfer = async () => {
    if (!canTransfer) return;
    canTransfer = false;
    if (isErp.value && !(await checkConvertErpMessageAlert())) {
        canTransfer = true;
        return;
    }
    const canNext = await dangerousOperationNext();
    if (!canNext) {
        canTransfer = true;
        return;
    }
    convertAsubIntoAssist();
};
function convertAsubIntoAssist() {
    useLoading().enterLoading("努力加载中，请稍候...");
    request({
        url: "/api/AccountSubject/ConvertAsubIntoAssist",
        params: { asubId: asubChecked.value, aaType: assistChecked.value },
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            canTransfer = true;
            useLoading().quitLoading();
            transferPrompt.value = false;
            if (res.state === 1000) {
                ElNotify({ type: "success", message: "明细科目转辅助核算成功！" });
                useAccountSubjectStore().getAccountSubject();
                window.dispatchEvent(new CustomEvent("reloadBackupData"));
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
            getTableData();
        })
        .catch(() => {
            canTransfer = true;
            useLoading().quitLoading();
        });
}
async function showAsubToAssist() {
    transferAuxiliary.value = true;
    Promise.all([
        request({
            url: `/api/AccountSubject/AsubDropDownList`,
            method: "post",
        }).then((res: IResponseModel<IAsubListItem[]>) => {
            if (res.state === 1000) {
                asubList.value = res.data;
                asubChecked.value = asubList.value[0].value;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        }),
        request({
            url: "/api/AccountSubject/AssistDropDownList",
            method: "post",
        }).then((res: IResponseModel<IAsubListItem[]>) => {
            if (res.state === 1000) {
                assistList.value = res.data;
                assistChecked.value = assistList.value[0].value;
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        }),
    ]).then(() => {
        asubToAssistShow.value = true;
    });
}

function rowStyleHandle({ row, rowIndex }: Irow) {
    let className = "row-id-" + row.asubId;
    if (row.status == 1) {
        className += " row-deactivate";
    }
    if (rowIndex % 2 === 1) {
        className += " el-table__row--striped";
    }
    return className;
}

function setSelectable(row: any, rowIndex?: number): boolean {
    return row.operation !== 0;
}
function search(searchStr: string) {
    searchInfo.value = searchStr;
    startIndex.value = 0;
    endIndex.value = 20;
    getTableData();
}

// 批量操作
let needOption = ref<number[]>([]);
let stopDialog = ref(false);
let bulkSetAADialog = ref(false);
function bulkOperation(option: string) {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "请先勾选科目哦~",
        });
        return false;
    }
    needOption.value = [];
    checkedTableData.value.forEach((value, index) => {
        if (!setSelectable(value)) {
            findParent2(value.parentId);

            parentIds.forEach((parentId: number) => {
                let targetIndex = needOption.value.findIndex((v) => v === parentId);
                if (targetIndex > -1) {
                    needOption.value.splice(targetIndex, 1);
                }
            });
        } else {
            needOption.value.push(value.asubId);
        }
    });
    switch (option) {
        case "start":
            bulkSubmit("BatchEnabled");
            break;
        case "delete":
            ElConfirm("亲，确认要删除吗？").then((r: boolean) => {
                if (r) {
                    bulkSubmit("Batch");
                } else {
                    needOption.value = [];
                }
            });
            break;
        case "stop":
            if (checkedTableData.value.find((obj) => obj.asubLevel === 1 && (obj.acronym === "xj" || obj.acronym === "kcxj"))) {
                ElConfirm("库存现金一级科目停用将影响资金模块使用,不允许停用!");
                needOption.value = [];
            } else if (checkedTableData.value.find((obj) => obj.asubLevel === 1 && obj.acronym === "yhck")) {
                ElConfirm("银行存款一级科目停用将影响资金模块使用,不允许停用!");
                needOption.value = [];
            } else {
                stopDialog.value = true;
            }
            break;
        case "setAA":
            bulkSetAADialog.value = true;
            aaTypesChecked.value = [];
            aaTypesAllowNullChecked.value = [];
            break;
    }
}
/**
 * @description: 批量操作
 * @return {*}
 * @param {*} option - 删除 'Batch',启动 'BatchEnabled',停用 'BatchDisabled'
 */
let deleteSuccess = ref();
let deleteFail = ref();
let deleteResData = ref<string[]>([]);
function bulkSubmit(option: string) {
    let optionsLength = checkedTableData.value.length;
    if (option === "BatchSetAA" && !aaTypesChecked.value.length) {
        ElNotify({
            type: "warning",
            message: "请勾选要设置的辅助核算",
        });
        return false;
    }
    let setAAData = {
        asubIdList: needOption.value,
        aaTypes: aaTypesChecked.value.join(","),
        allowNullAaTypes: aaTypesAllowNullChecked.value.join(","),
    };
    request({
        url: `/api/AccountSubject/${option}`,
        method: option === "Batch" ? "delete" : "post",
        data: option === "BatchSetAA" ? setAAData : needOption.value,
        headers: {
            "Content-Type": "application/json",
        },
    })
        .then((res: IResponseModel<any>) => {
            if (res.state === 1000) {
                if (option !== "Batch") {
                    if (option === "BatchSetAA") {
                        ElNotify({
                            type: "success",
                            message:
                                res.data.successCount === needOption.value.length
                                    ? `设置成功${res.data.successCount}个`
                                    : `设置成功${res.data.successCount}个，失败${res.data.failCount}个。非末级科目、已使用的科目、已设置辅助核算的科目已跳过哦~`,
                        });
                        bulkSetAADialog.value = false;
                        aaTypesChecked.value = [];
                        aaTypesAllowNullChecked.value = [];
                    } else {
                        ElNotify({
                            type: "success",
                            message: `${option === "BatchEnabled" ? "启用" : option === "BatchDisabled" ? "停用" : "删除"}成功${
                                option === "Batch" ? needOption.value.length : res.data
                            }个科目！${
                                option === "BatchEnabled"
                                    ? optionsLength > (res.data as number)
                                        ? "启用状态的科目已跳过"
                                        : ""
                                    : optionsLength > (res.data as number)
                                    ? "已是停用状态或科目余额不为0的科目已跳过"
                                    : ""
                            }`,
                        });
                    }
                } else {
                    if ((res.data as string[]).length) {
                        deleteSuccessTips.value = true;
                        deleteFail.value = (res.data as string[]).length;
                        deleteSuccess.value = optionsLength - (res.data as string[]).length;
                        deleteResData.value = res.data as string[];
                    } else {
                        ElNotify({
                            type: "success",
                            message: `删除成功${optionsLength}个`,
                        });
                    }
                }
                nextTick(() => {
                    checkedTableData.value.forEach((item) => {
                        removeSelectedClassName(item.asubId);
                    });
                });
                useAccountSubjectStore().getAccountSubject();
                window.dispatchEvent(new CustomEvent("modifyaccountSubject"));
            } else if (res.state === 2000) {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: `${
                        option === "BatchSetAA"
                            ? "设置辅助核算"
                            : option === "BatchEnabled"
                            ? "启用"
                            : option === "BatchDisabled"
                            ? "停用"
                            : "删除"
                    }失败，请刷新页面重试或联系客服`,
                });
            }
            needOption.value = [];
            getTableData();
        })
        .catch((e) => {
            ElNotify({
                type: "warning",
                message: `${
                    option === "BatchSetAA"
                        ? "设置辅助核算"
                        : option === "BatchEnabled"
                        ? "启用"
                        : option === "BatchDisabled"
                        ? "停用"
                        : "删除"
                }失败，请刷新页面重试或联系客服`,
            });
            needOption.value = [];
        });
    stopDialog.value = false;
}

//增删改
const currentRow = ref({});
const addTitle = ref("");
const subjectCode = ref("");

/**
 * @description: 添加科目
 * @return {}Object
 * @param {Object} rowData -行数据
 * @param {number} editType -添加科目按钮0/行添加1/编辑2
 */
function addHandle(rowData: any, editType: number) {
    currentSlot.value = "add";
    currentRow.value = rowData;
    addTitle.value = currentActiveType[activeName.value];
    if (editType === 0) {
        rowData.currentLevel = 1;
        rowData.parentId2 = 0;
    } else if (editType === 1) {
        rowData.currentLevel = rowData.asubLevel + 1;
        rowData.parentId2 = rowData.asubId;
    }
    rowData.accountCategory = asubTypeCode[currentActiveType[activeName.value] as any];
    addSlot.value?.initState(rowData, editType);
}

// 编辑
function editHandle(rowData: any) {
    currentSlot.value = "add";
    currentRow.value = rowData;
    addTitle.value = currentActiveType[activeName.value];
    let currentTableData = _.cloneDeep(tableData[activeName.value].data);
    let lastestId = currentTableData.sort((a: ITableDataItem, b: ITableDataItem) => b.asubId - a.asubId)[0].asubId;
    rowData.parentId2 = rowData.asubId ? rowData.asubId : lastestId + 1;
    rowData.accountCategory = asubTypeCode[currentActiveType[activeName.value] as any];
    rowData.currentLevel = rowData.asubLevel;
    addSlot.value?.initState(rowData, 2);
}
function submitAdd() {
    switchScrollTop();
    currentSlot.value = "main";
    getTableData();
}
function addCancel(refresh: Boolean) {
    switchScrollTop();
    currentSlot.value = "main";
    if (refresh) {
        getTableData();
    }
}
function switchScrollTop() {
    requestAnimationFrame(() => {
        const tableBody = accountSubjectTableRef.value?.$el.querySelector(".el-scrollbar__wrap");
        tableBody.scrollTop = tableScrollTop;
    });
}
// 检测科目是否被凭证和总账使用
function checkAusbUsedInVcAndGl(asubId: number) {
    return request({
        url: "/api/AccountSubject/CheckAusbUsedInVcAndGl",
        method: "post",
        params: { asubId: asubId },
    });
}
function deleteHandle(row: ITableDataItem, type: string) {
    let id = row.asubId;
    let currentTableData = _.cloneDeep(tableData[activeName.value].data);
    ElConfirm("亲，确认要删除吗？").then((res: boolean) => {
        if (res) {
            checkAusbUsedInVcAndGl(id).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000 && !res.data) {
                    deleteAsub(id);
                } else {
                    let parentData = noSearchTableData.value[activeName.value].find((item: ITableDataItem) => item.asubId === row.parentId);
                    let childLength = noSearchTableData.value[activeName.value].filter(
                        (item: ITableDataItem) => item.parentId == row.parentId
                    ).length;
                    if (childLength === 1) {
                        ElConfirm(
                            `该科目已有数据，删除后，本科目的数据将转入上级科目${parentData.asubCode}${parentData.asubName}，您确定要继续吗？`
                        ).then((r: boolean) => {
                            if (r) {
                                deleteAsub(id);
                            }
                        });
                    } else {
                        ElConfirm("亲，此科目已在期初或凭证中使用，不能删除！", true);
                    }
                }
            });
        }
    });
}
function deleteAsub(id: number) {
    request({
        url: `/api/AccountSubject`,
        method: "delete",
        params: { asubId: id },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 2000) {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
            return;
        }
        if (res.state === 1000) {
            ElNotify({
                type: "success",
                message: "删除成功",
            });
            getTableData();
            useAccountSubjectStore().getAccountSubject();
            window.dispatchEvent(new CustomEvent("modifyaccountSubject"));
        }
    });
}

onUnmounted(() => {
    window.removeEventListener("reloadAccountSubjectTree", getTableData);
});

const showAsubList = ref<IAsubListItem[]>([]);
const showAssistList = ref<IAsubListItem[]>([]);
watchEffect(() => {
    showAsubList.value = JSON.parse(JSON.stringify(asubList.value));
    showAssistList.value = JSON.parse(JSON.stringify(assistList.value));
});
function asubFilterMethod(value: string) {
    showAsubList.value = commonFilterMethod(value, asubList.value, 'text');
}
function assistFilterMethod(value: string) {
    showAssistList.value = commonFilterMethod(value, assistList.value, 'text');
}
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";

.content {
    // padding-bottom: 50px;

    .main-content {
        height: calc(100vh - 244px);
        .main-center {
            height: 100%;
            overflow: initial;
            .subject-table {
                height: 100%;

                :deep(.el-table__body-wrapper) {
                    height: calc(100% - 40px);
                }

                :deep(.el-table--default) {
                    .cell {
                        padding: 0 8px;
                    }
                }
                :deep(.my-table-virtual) {
                    margin-bottom: -18px;
                }
            }
        }
    }
}

.link-option {
    text-decoration: none;
    outline: none;
    cursor: pointer;
    text-decoration: none;
    .set-font(var(--link-color));
    font-size: var(--table-body-font-size);

    &:hover {
        text-decoration: underline;
    }
}

:deep(.custom-table tbody tr.row-deactivate td .cell) {
    color: #aaaaaa !important;
}

.subjectCodeSet {
    & .codeLength {
        text-align: center;

        & .code-length-main {
            text-align: left;
            display: inline-block;
            padding: 30px 25px;

            & .input {
                display: flex;

                & .code-level-list {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    width: 84%;
                    height: auto;
                }

                & .input-title {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    flex-shrink: 0;
                    align-self: flex-start;
                    line-height: 28px;
                    position: relative;
                    .vip-icon {
                        width: 18px;
                        height: 20px;
                        position: absolute;
                        left: -22px;
                        top: 3px;
                    }
                }

                & .input-content {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    line-height: 28px;
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    margin: 2px 0;

                    .code-length-select {
                        width: 40px;

                        :deep(.el-input__wrapper) {
                            padding: 0 0 0 6px;

                            .el-input__suffix-inner {
                                width: 20px;
                            }
                        }
                    }
                }
            }

            & .txt {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
            }
        }

        & .buttons {
            padding: 10px 0;
            text-align: center;
            border-top: 1px solid var(--border-color);

            &.disabled {
                background-color: var(--border-color);
                border-color: var(--border-color);
                color: var(--white);
            }
        }
    }
}
:deep(.el-table--striped .el-table__body tr.row-selected td.el-table__cell) {
    background: var(--table-hover-color);
}
.code-length-edit-select {
    .detail-el-select(40px, 26px);

    :deep(.el-select .el-input__wrapper) {
        padding-right: 0;
    }
}

.code-length-edit-select.level {
    .detail-el-select(50px, 26px) !important;

    :deep(.el-select .el-input__wrapper) {
        padding-right: 0 !important;
    }

    & .el-select .el-select-dropdown__item.hover {
        background-color: var(--main-color) !important;
        color: #fff !important;
    }
}

.inject-table-content {
    & .inject-table-main {
        padding: 20px 40px;
        overflow: hidden;
    }

    & .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }

    .file-button {
        display: flex;

        #myfile {
            width: 60px;
        }

        .file-name-box {
            width: 300px;
            overflow: hidden;
            margin-left: 30px;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

.inject-message {
    & .inject-message-main {
        padding: 20px 40px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        min-height: 120px;
    }

    & .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }
}

.asub-to-assist-main {
    text-align: left;
    display: inline-block;
    padding: 20px 30px;

    .asub-to-assist-info {
        font-size: 15px;
        margin-bottom: 10px;
    }

    .asub-to-assist-select {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding-right: 15px;
        margin-bottom: 10px;
    }

    .asub-to-assist-tip {
        font-size: var(--h5);
        color: #aaaaaa;
        display: flex;
        align-items: center;

        img {
            width: 14px;
            margin-right: 5px;
        }
    }
}

.asub-to-assist-confirm-main {
    text-align: left;
    display: inline-block;
    padding: 20px 30px;

    .asub-to-assist-confirm-title {
        font-size: var(--h3);
        font-weight: 500;
        margin-bottom: 7px;
    }

    .asub-to-assist-confirm-info {
        font-size: var(--h3);
        margin-bottom: 12px;
        line-height: 25px;

        .yellow {
            color: #fa6400;
        }
        .ellipsis {
            width: 368px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .asub-to-assist-confirm-tip {
        font-size: var(--h5);
        color: #666666;
        line-height: 20px;
        display: flex;
        align-items: flex-start;

        img {
            width: 14px;
            margin-top: 5px;
            margin-right: 5px;
        }
    }
}

.asub-disbaled-confirm-main {
    text-align: left;
    display: inline-block;
    padding: 20px 30px;

    .asub-disbaled-confirm-info {
        font-size: var(--h3);
        margin-bottom: 12px;
        line-height: 25px;

        .yellow {
            color: #fa6400;
        }
    }

    .asub-disbaled-confirm-tip {
        font-size: var(--h5);
        color: #666666;
        line-height: 20px;
        display: flex;
        align-items: flex-start;

        img {
            width: 14px;
            margin-top: 4px;
            margin-right: 5px;
        }
    }

    .asub-delete-fail-tip {
        font-size: var(--h4);
    }
}

.transfer-auxiliary-dialog {
    .asub-to-assist-main {
        .asub-to-assist-select {
            :deep(.el-select) {
                width: calc(100% - 80px);
            }
        }
        :deep(.el-input__inner) {
            color: #808080 !important;
        }
    }

    .transfer-auxiliary-help-icon {
        width: 15px;
        height: 15px;
        cursor: pointer;
    }
}
</style>
<style lang="less" scoped>
body[erp] {
    .el-dialog.transfer-auxiliary-dialog {
        .transfer-auxiliary-help-icon {
            position: relative;
            top: -10px;
        }
    }
    .slot-content {
        height: 100vh !important;
        overflow: auto !important;
    }
}
</style>
