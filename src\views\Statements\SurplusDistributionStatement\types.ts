export interface ISurplusDistribution {
    statementID: number;
    surplusLineID: number;
    surplusLineType: number;
    surplusPartentID: number;
    surplusProName: string;
    surplusLineNumber: number;
    surplusTotal: number;
    surplusNote: string;
    surplusDistributionLineID: number;
    surplusDistributionLineType: number;
    surplusDistributionPartentID: number;
    surplusDistributionProName: string;
    surplusDistributionLineNumber: number;
    surplusDistributionTotal: number;
    surplusDistributionNote: string;
    expand?: number;
    children?: ISurplusDistribution[];
    fold?: number;
}
