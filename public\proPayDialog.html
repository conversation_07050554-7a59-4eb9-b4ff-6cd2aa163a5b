<!DOCTYPE html>
<html style="overflow: auto">
    <head>
        <meta charset="utf-8" />
        <title></title>
    </head>
    <body style="background: transparent !important; overflow: auto">
        <div id="app"></div>

        <script src="/web.config.js"></script>
        <script src="/system.config.js"></script>
        <script>
            var style = document.createElement("link");
            style.href = window.epHost + "/js/app.js";
            style.rel = "preload";
            style.type = "script";
            document.head.append(style);
            var script = document.createElement("script");
            script.src = window.epHost + "/js/app.js";
            document.body.append(script);
            let css = document.createElement("style");
            css.textContent = `
                .page {
                    overflow: visible !important;
                }
                .page .top {
                    border-radius: 20px 20px 0 0;
                    overflow: hidden;
                }
                .page .bg {
                    border: 1px solid #d9d9d9;
                }
            `;
            document.head.appendChild(css);
        </script>
        <script>
            function closeDialog() {
                parent.window.proDialogOnclose();
            }

            function gotoPro() {
                parent.window.location.href = jProH5Host + "/Default/Default";
            }
        </script>
    </body>
</html>
