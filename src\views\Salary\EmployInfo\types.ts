export interface ISelectItem {
    value: string;
    label: string;
}

export interface IsearchInfo {
    department: number | string;
    status: number;
    e_name: string;
    time_s: string;
    time_e: string;
    showAllInfo: boolean;
}
export interface IInsuranceSettings {
    itemId: number;
    itemName: string;
    base: number;
    companyPercent: number;
    personPercent: number;
    companyAmount: number;
    personAmount: number;
    paymentMode: number;
}
export interface IInsuranceItem {
    as_id: number;
    base: string;
    com_amount: string;
    com_percent: string;
    e_code_name: string;
    e_sn: null;
    per_amount: string;
    per_percent: string;
    ss_type: number;
    pay_mode?: number;
}
export interface IEmployItem {
    aae_id: number;
    as_id: number;
    asub_name: string;
    bank: string;
    bank_account: string;
    birthday: string;
    department: number;
    department_name: string;
    e_code: string;
    e_id: string;
    e_name: string;
    e_sn: number;
    e_type: number;
    education: number;
    email: string;
    end_date: string;
    gender: number;
    insuranceSettings: IInsuranceItem[] | null;
    mobile_phone: string;
    note: string;
    position: string;
    salary_asub: number;
    salary_end_date: string;
    salary_start_date: string;
    ss: number;
    start_date: string;
    status: number;
    tax_base: number;
    title: string;
}
export interface IEmployData {
    count: number;
    data: IEmployItem[];
}
export interface ISyncData {
    succeeCount: number;
    skipCount: number
    skipMsg: string;
}