<template>
    <el-dialog
        v-model="display"
        :title="title"
        center
        :width="width + 'px'"
        @close="close"
        @closed="resetDialogWidth"
        class="custom-confirm dialogDrag"
    >
        <Default ref="defaultRef" v-dialogDrag :inputTitle="inputTitle" v-if="!showErpDialog" @cancel="close" @save-fail="close" @save-success="success"></Default>
        <Erp ref="erpRef" v-if="showErpDialog" @cancel="close" @save-fail="handleSaveFail" @save-success="success"></Erp>
    </el-dialog>
</template>
<style lang="less" scoped>
@import "@/style/Functions.less";

.add-aae-dialog-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-top: 19px;

    .form-item {
        align-self: center;
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .input-title {
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }

        .input-field {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .detail-input(256px, 30px);
            .detail-el-select(256px, 30px);
            display: flex;
            align-items: center;
        }
    }

    .buttons {
        margin-top: 11px;
        padding: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
<script lang="ts" setup>
import { ref } from "vue";
import Default from "./components/Default.vue";
import Erp from "./components/Erp.vue";
import { nextTick } from "vue";
const props = defineProps({
    title: {
        type: String,
        default: "添加辅助核算项目",
    },
    closeOnSaveFail: {
        type: Boolean,
        default: false,
    },
    inputTitle: {
        type: String,
        default: "辅助核算项目",
    }
});
const emit = defineEmits<{
    (e: "save-success", data: any): void;
    (e: "close"): void;
    (e: "save-fail"): void;
}>();
const display = ref(false);
const showErpDialog = ref(false);
const defaultRef = ref<InstanceType<typeof Default>>();
const erpRef = ref<InstanceType<typeof Erp>>();
const width = ref(440);

function showAADialog(aatype: number, autoAddName: string = "") {
    display.value = true;
    if (window.isErp && [10001, 10002, 10003, 10004, 10006].indexOf(aatype) !== -1) {
        if (aatype === 10001 || aatype === 10002 || aatype === 10006) {
            width.value = 740;
        } else {
            width.value = 440;
        }
        showErpDialog.value = true;
        nextTick(() => {
            erpRef.value?.init(aatype, autoAddName);
        });
    } else {
        width.value = 440;
        showErpDialog.value = false;
        nextTick(() => {
            defaultRef.value?.init(aatype, autoAddName);
        });
    }
}

function success(data: any) {
    close();
    emit("save-success", data);
}

function close() {
    display.value = false;
    emit("close");
}

function handleSaveFail() {
    emit("save-fail");
    if (props.closeOnSaveFail) {
        display.value = false;
    }
}

function resetDialogWidth() {
    width.value = 440;
}

function setDialogWidth(w: number) {
    width.value = w;
}

defineExpose({
    showAADialog,
    setDialogWidth,
});
</script>
