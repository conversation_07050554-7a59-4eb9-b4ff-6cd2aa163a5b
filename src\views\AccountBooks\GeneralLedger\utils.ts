import { ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney, formatQuantity } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "GeneralLedger";

let columns: Array<IColumnProps> = [];
const codeLengthStr = ref("");

function getAsubCodeLength() {
    request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<any>) => {
        codeLengthStr.value = res.data.codeLength.join("");
    });
}

function rankCell(asubCode: any, val: any) {
    let formatString = "";
    const code = asubCode.toString().split('_')[0];
    for (let i = 0; i < codeLengthStr.value.length; i++) {
        let length = 0;
        for (let j = 0; j <= i; j++) {
            length += Number(codeLengthStr.value[j]);
        }
        if (length == code.length) {
            formatString = "<span class='level" + (i + 1) + "' >" + val + "</span>";
            break;
        }
    }
    return formatString;
}

// 切换表头
export const changeColumnName = (showQuantity: boolean, fc_code: string) => {
    getAsubCodeLength();

    // 初始表格
    const baseColumns: Array<IColumnProps> = [
        {
            label: "科目编码",
            prop: "asub_code",
            minWidth: 140,
            align: "left",
            headerAlign: "left",
            useHtml: true,
            formatter: (row, column, value) => rankCell(row.asub_code, value),
            width: getColumnWidth(setModule, 'asub_code', 140),
        },
        { slot: "name" },
        { 
            label: "期间", 
            prop: "period", 
            minWidth: 140, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'period'),
        },
        { 
            label: "摘要", 
            prop: "title", 
            minWidth: 210, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'title'),
        },
        {
            label: "借方金额",
            prop: "debit",
            minWidth: 130,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, 'debit'),
        },
        {
            label: "贷方金额",
            prop: "credit",
            minWidth: 130,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, 'credit'),
        },
        { 
            label: "方向", 
            prop: "direction", 
            minWidth: 80, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'direction'), 
        },
        {
            label: "余额",
            prop: "total",
            minWidth: 120,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
            resizable: false, 
        },
    ];
    // 初始表格显示数量金额
    const showQuantityColums: Array<IColumnProps> = [
        { slot: "asub_code" },
        { slot: "name" },
        { 
            label: "单位", 
            prop: "measureunit", 
            minWidth: 60, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'measureunit'),  
        },
        {
            label: "期初余额",
            headerAlign: "center",
            children: [
                { 
                    label: "方向", 
                    prop: "initial_direction", 
                    minWidth: 50, 
                    align: "left", 
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'initial_direction'), 
                },
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "initial_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'initial_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "initial_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'initial_price'),
                },
                {
                    label: "金额",
                    prop: "initial",
                    minWidth: 120,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'initial'),
                },
            ],
        },
        {
            label: "本期借方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "debit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'debit_qut'),
                },
                {
                    label: "金额",
                    prop: "debit",
                    minWidth: 120,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit'),
                },
            ],
        },
        {
            label: "本期贷方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "credit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'credit_qut'),
                },
                {
                    label: "金额",
                    prop: "credit",
                    minWidth: 120,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit'),
                },
            ],
        },
        {
            label: "本年累计借方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "year_debit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'year_debit_qut'),
                },
                {
                    label: "金额",
                    prop: "year_debit",
                    minWidth: 120,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'year_debit'),
                },
            ],
        },
        {
            label: "本年累计贷方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "year_credit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'year_credit_qut'),
                },
                {
                    label: "金额",
                    prop: "year_credit",
                    minWidth: 120,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'year_credit'),
                },
            ],
        },
        {
            label: "期末余额",
            headerAlign: "center",
            children: [
                { 
                    label: "方向", 
                    prop: "total_direction", 
                    minWidth: 50, 
                    align: "left", 
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'total_direction'),
                },
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "total_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'total_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "total_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'total_price'),
                },
                {
                    label: "金额",
                    prop: "total",
                    minWidth: 120,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ],
        },
    ];
    // 综合本位币
    const baseFcidColumns: Array<IColumnProps> = [
        {
            label: "科目编码",
            prop: "asub_code",
            minWidth: 100,
            align: "left",
            headerAlign: "left",
            useHtml: true,
            formatter: (row, column, value) => rankCell(row.asub_code, value),
            width: getColumnWidth(setModule, 'asub_code', 100),
        },
        { slot: "name" },
        { 
            label: "期间", 
            prop: "period", 
            minWidth: 90, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'period'),
        },
        { 
            label: "摘要", 
            prop: "title", 
            minWidth: 110, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'title'), 
        },
        { 
            label: "币别", 
            prop: "fc_code", 
            minWidth: 70, 
            align: "left", 
            headerAlign: "left", 
            width: getColumnWidth(setModule, 'fc_code'), 
        },
        {
            label: "借方金额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "原币",
                    prop: "debit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit_fc'), 
                },
                {
                    label: "本位币",
                    prop: "debit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit'), 
                },
            ],
        },
        {
            label: "贷方金额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "原币",
                    prop: "credit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit_fc'), 
                },
                {
                    label: "本位币",
                    prop: "credit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit'), 
                },
            ],
        },
        {
            label: "余额",
            align: "right",
            headerAlign: "center",
            children: [
                {
                    label: "方向",
                    prop: "direction",
                    minWidth: 50,
                    align: "left",
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'direction'), 
                },
                {
                    label: "原币",
                    prop: "total_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total_fc'),
                },
                {
                    label: "本位币",
                    prop: "total",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    resizable: false,
                },
            ],
        },
    ];
    // 切换币别
    const showQuantityFcidColums: Array<IColumnProps> = [
        { slot: "asub_code" },
        { slot: "name" },
        { 
            label: "单位", 
            prop: "measureunit", 
            minWidth: 60, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'measureunit'), 
        },
        { 
            label: "币别", 
            prop: "fc_code", 
            minWidth: 70, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'fc_code'),
        },
        {
            label: "期初余额",
            headerAlign: "center",
            children: [
                { 
                    label: "方向", 
                    prop: "initial_direction", 
                    minWidth: 50, 
                    align: "left", 
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'initial_direction'),
                },
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "initial_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'initial_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "initial_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'initial_price'),
                },
                {
                    label: "金额(原币)",
                    prop: "initial_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'initial_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "initial",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'initial'),
                },
            ],
        },
        {
            label: "本期借方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "debit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'debit_qut'),
                },
                {
                    label: "金额(原币)",
                    prop: "debit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "debit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit'),
                },
            ],
        },
        {
            label: "本期贷方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "credit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'credit_qut'),
                },
                {
                    label: "金额(原币)",
                    prop: "credit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "credit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit'),
                },
            ],
        },
        {
            label: "本年累计借方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "year_debit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'year_debit_qut'),
                },
                {
                    label: "金额(原币)",
                    prop: "year_debit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'year_debit_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "year_debit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'year_debit'),
                },
            ],
        },
        {
            label: "本年累计贷方",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "year_credit_qut",
                    minWidth: 80,
                    align: "left",
                    headerAlign: "left",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'year_credit_qut'),
                },
                {
                    label: "金额(原币)",
                    prop: "year_credit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'year_credit_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "year_credit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'year_credit'),
                },
            ],
        },
        {
            label: "期末余额",
            headerAlign: "center",
            children: [
                { label: "方向", prop: "total_direction", minWidth: 50, align: "left", headerAlign: "left" },
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "total_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'total_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "total_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, 'total_price'),
                },
                {
                    label: "金额(原币)",
                    prop: "total_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "total",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    resizable: false,
                },
            ],
        },
    ];

    // 综合本位币
    if (fc_code === "-1") {
        // 不显示金额
        if (!showQuantity) {
            columns = baseColumns;
        } else {
            columns = showQuantityColums;
        }
    } else {
        // 不显示金额
        if (!showQuantity) {
            columns = baseFcidColumns;
        } else {
            columns = showQuantityFcidColums;
        }
    }

    return columns;
};
