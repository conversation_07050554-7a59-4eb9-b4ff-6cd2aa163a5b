<template>
    <div class="content">
        <div class="title">核算项目余额表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :isPeriodList="true"
                                    @getActPid="getActPid"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">辅助项目：</div>
                            <div class="line-item-field aatype-select">
                                <SelectWithLotOfData
                                    v-model="searchInfo.aae_id"
                                    style="width: 298px"
                                    :teleported="false"
                                    :filterable="true"
                                    :clearable="true"
                                    :class="generateId"
                                    :placeholder="currentAuxiliary"
                                    :options="showRealyArray"
                                    :icon-clear-right="33"
                                    @change="handleAuxiliaryChange"
                                    @visible-change="handleVisibleChange"
                                    @input="handleInput"
                                    :remote="realyArray.length > 0"
                                    :filter-method="realyFilterMethod"
                                    :isSuffixIcon="true"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="startAsubRef"
                                    asubImgRight="14px"
                                    v-model="searchInfo.subjectCode"
                                    :showDisabled="true"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目级别：</div>
                            <div class="line-item-field">
                                <el-input-number
                                    v-model="searchInfo.startAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                                <div class="ml-10 mr-10">至</div>
                                <el-input-number
                                    v-model="searchInfo.endAsubLevel"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                            </div>
                        </div>
                        <div class="line-item input" v-if="aaShow">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field fcid-select">
                                <el-select 
                                    v-model="searchInfo.fcid" 
                                    :teleported="false" 
                                    placeholder=" "
                                    :filterable="true"
                                    :filter-method="fcFilterMethod"
                                >
                                    <el-option 
                                        :label="item.label" 
                                        :value="item.id" 
                                        v-for="item in showfcList" 
                                        :key="item.id"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.balanceZeroUnShow" label="余额为0不显示"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearchSwitch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <span class="txt ml-10">辅助类别：</span>
                    <div class="jqtransform fztype ml-10 jqtransformdone">
                        <Select 
                            v-model="searchInfo.aa_type" 
                            :fit-input-width="true" 
                            :teleported="false" 
                            @change="aatypeChange"
                            :filterable="true"
                            :filter-method="fzTypeFilterMethod"
                        >
                            <Option v-for="item in showfzTypeList" :label="item.aatypeName" :value="item.aatypeId" :key="item.aatypeId" />
                        </Select>
                    </div>
                    <a class="button ml-10 solid-button" @click="handleSearchSwitch">查询</a>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <div class="mr-20 sbjhorizon">
                        <el-checkbox v-model="searchInfo.showsbj" label="显示科目"></el-checkbox>
                    </div>
                    <div class="mr-20 sbjhorizon">
                        <el-checkbox v-model="searchInfo.showhHrizontal" label="科目横向展示" :disabled="!searchInfo.showsbj"></el-checkbox>
                    </div>
                    <div class="mr-20 sbjhorizon">
                        <el-checkbox v-model="searchInfo.showQuanlity" label="显示数量金额"></el-checkbox>
                    </div>
                    <div class="mr-20 sbjhorizon">
                        <el-checkbox v-model="searchInfo.showYear" label="显示本年累计"></el-checkbox>
                    </div>
                    <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['aatrialbalance-canprint']">
                        <li @click="handlePrint(0,getDefaultParams())">直接打印</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <Dropdown :btnTxt="'导出'" :downlistWidth="102" v-permission="['aatrialbalance-canexport']">
                        <li @click="handleExport(0)">当前查询数据</li>
                        <li @click="showExportDialog">批量导出</li>
                    </Dropdown>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div v-loading="loading" element-loading-text="正在加载数据..." class="main-center">
                <AccountBooksTable
                    ref="tableComponent"
                    :data="tableData"
                    :columns="columns"
                    :loading="loading"
                    :empty-text="emptyText"
                    :pageIsShow="true"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :current-page="paginationData.currentPage"
                    :tooltipOptions="{ effect: 'light', placement: 'bottom' }"
                    :scrollbar-show="true"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :show-overflow-tooltip="true"
                    :tableName="setModule"
                >
                    <template #num>
                        <el-table-column 
                            :label="aaType + '编码'"  
                            align="left" 
                            header-align="left"
                            prop="num"
                            :width="getColumnWidth(setModule, 'num', 100)"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.aa_num }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #name>
                        <el-table-column 
                            :label="aaType + '名称'"  
                            align="left" 
                            header-align="left"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name', 280)"
                        >
                            <template #default="scope">
                                <span
                                    :class="
                                        checkPermission(['aasubsidiaryledger']) && scope.row.aa_name !== '合计' ? 'link' : 'cursor-default'
                                    "
                                    @click="
                                        checkPermission(['aasubsidiaryledger']) && scope.row.aa_name !== '合计'
                                            ? ShowDetail(scope.row.aa_name)
                                            : ''
                                    "
                                    v-show="scope.row.aa_name !== ''"
                                >
                                    {{ handleAsubName(scope.row.aa_name) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </AccountBooksTable>
            </div>
        </div>
    </div>
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        title="核算项目余额表打印"
        :printData="printInfo"
        :dir-disabled="true"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    />
    <el-dialog v-model="exportDialogVisible" title="批量导出" center width="550" class="custom-confirm">
        <div class="export-content">
            <div class="export-main">
                <div class="line-item mt-20 mb-20">
                    <span class="mr-20">导出类别：</span>
                    <Select
                        class="export-type-select"
                        v-model="exportInfo.aa_types"
                        filterable
                        :fitInputWidth="true"
                        :teleported="false"
                        :multiple="true"
                        :style="{ width: '400px' }"
                        @change="onExportTypeChange"
                    >
                        <Option v-for="item in fzTypeList" :label="item.aatypeName" :value="item.aatypeId" :key="item.aatypeId"> </Option>
                    </Select>
                </div>
                <div class="line-item mt-20 mb-20">
                    <span class="mr-20">期间：</span>
                    <PeriodPicker v-model:startPid="exportInfo.startPid" v-model:endPid="exportInfo.endPid" :style="{ width: '400px' }" />
                </div>
                <div class="line-item mt-20 mb-20">
                    <span class="mr-20">文件格式：</span>
                    <el-radio-group v-model="exportInfo.ifpage" :style="{ width: '400px' }">
                        <el-radio label="1">按类别多页签</el-radio>
                        <el-radio label="2">按类别文件分多个文件</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="buttons a_nofloat">
                <a class="button" @click="exportDialogVisible = false">取消</a>
                <a class="button solid-button ml-20" @click="handleExport(1)">导出</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "AATrialBalance",
};
</script>
<script setup lang="ts">
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import SelectWithLotOfData from "@/views/AccountBooks/AASubsidiaryLedger/components/SelectWithLotOfData.vue";
import { componentFinishKey } from "@/symbols";
import { request, type IResponseModel } from "@/util/service";
import type { ITableData, IWithAsub, IAssistingAccount, IAsubCodeLength, ISelectList } from "./types";
import { SelectWithLotOfDataOptions } from "@/views/AccountBooks/AASubsidiaryLedger/components/types";
import { usePagination } from "@/hooks/usePagination";
import { ref, reactive, provide, watch, nextTick, onMounted, watchEffect } from "vue";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { handleAsubName } from "@/util/format";
import { setColumns, setTransverseColumns, extractParams } from "./utils";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import { checkPermission } from "@/util/permission";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import usePrint from "@/hooks/usePrint";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "AATrialBalance";
const periodStore = useAccountPeriodStore();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const aaRef = ref();
const currentPeriodInfo = ref("");
// const periodInfo = ref("");
const loading = ref<boolean>(false);
const aaType = ref("");
const tableComponent = ref<InstanceType<typeof AccountBooksTable>>();
const setAAType = () => {
    const aaTypeList = useAssistingAccountingStore().assistingAccountingTypeList;
    const item = aaTypeList.find((item) => item.aaType === Number(searchInfo.aa_type));
    aaType.value = item?.aaTypeName ?? "";
};

const ShowDetail = (aaname: string) => {
    const { AA_TYPE, AAE_ID, period_s, period_e } = extractParams(aaname);
    const params = { AA_TYPE, AAE_ID, period_s, period_e, ran: Math.random() };
    globalWindowOpenPage("/AccountBooks/AASubsidiaryLedger?" + getUrlSearchParams(params), "核算项目明细账");
};
//币别
const fcList = ref<any[]>([]);

const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();

const searchInfo = reactive({
    startPid: Number(periodStore.getPeriodRange().start),
    endPid: Number(periodStore.getPeriodRange().end),
    aae_id: "",
    subjectCode: "",
    startAsubLevel: 1,
    endAsubLevel: 4,
    balanceZeroUnShow: false,
    fcid: -1,
    showsbj: false,
    showhHrizontal: false,
    showQuanlity: false,
    showYear: false,
    aa_type: "10001",
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, searchInfo.startPid, searchInfo.endPid); 
const getActPid = (start: number, end: number) => {
    searchInfo.startPid = start;
    searchInfo.endPid = end;
}

/** 用searchInfo的数据更新params */
function setParams(params: any) {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.startAsubLevel > searchInfo.endAsubLevel) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    params.period_s = searchInfo.startPid;
    params.period_e = searchInfo.endPid;
    params.sbj_leval_s = searchInfo.startAsubLevel;
    params.sbj_leval_e = searchInfo.endAsubLevel;
    params.showsbj = searchInfo.showsbj ? 1 : 0;
    params.showNumber = searchInfo.showQuanlity ? 1 : 0;
    params.showYear = searchInfo.showYear ? 1 : 0;
    params.fcid = searchInfo.fcid;
    return true;
}
const realyArray = ref<Array<SelectWithLotOfDataOptions>>([]);
let currentAuxiliary = ref(" ");
const handleAuxiliaryChange = (item: string) => {
    let current = searchPeriodList.value.find((v: ISelectList) => v.id + "" === item);
    currentAuxiliary.value = current!.label;
};
const random = () => {
    return Math.floor(Math.random() * 100000000);
};
// 生成一个唯一的id
const generateId = `select-${random()}`;
const handleVisibleChange = (visible: boolean) => {
    let timer = setTimeout(() => {
        let selectFilterInput = document.querySelector(`.${generateId} .select-trigger .el-input__inner`) as HTMLInputElement;
        if (selectFilterInput) {
            selectFilterInput.value = searchInfo.aae_id
                ? (searchPeriodList.value.find((item) => item.id === Number(searchInfo.aae_id)) as any).label
                : "";
        }
        clearTimeout(timer);
    }, 0);
    if (visible) {
        showRealyArray.value = JSON.parse(JSON.stringify(realyArray.value));
    }
};

const emptyText = ref(" ");
const handleSearch = () => {
    setAAType();
    periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
    const params = {
        // IsSearch: 1,
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        sbj_id: searchInfo.subjectCode.split(" ")[0],
        sbj_leval_s: searchInfo.startAsubLevel,
        sbj_leval_e: searchInfo.endAsubLevel,
        aa_type: searchInfo.aa_type,
        showsbj: searchInfo.showsbj ? 1 : 0,
        isBalanceZero: searchInfo.balanceZeroUnShow ? 1 : 0,
        aa_num: searchInfo.aae_id,
        fcid: searchInfo.fcid,
        quanlity: searchInfo.showQuanlity,
        showall: searchInfo.showYear,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
    };
    loading.value = true;
    request({
        url: `/api/AATrialBalance`,
        method: "get",
        params,
    })
        .then((res: IResponseModel<ITableData>) => {
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            loading.value = false;
            columns.value = setColumns(searchInfo.aa_type, searchInfo.showYear, searchInfo.showQuanlity, searchInfo.fcid > 0);
            if (res.data.rows.length === 0) {
                emptyText.value = "暂无数据";
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
        });
};

function handleHorizontalChange() {
    periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
    const params = {
        // IsSearch: 1,
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        sbj_id: searchInfo.subjectCode.split(" ")[0],
        sbj_leval_s: searchInfo.startAsubLevel,
        sbj_leval_e: searchInfo.endAsubLevel,
        aa_type: searchInfo.aa_type,
        showsbj: searchInfo.showsbj ? 1 : 0,
        // showsbj: 1,
        isBalanceZero: searchInfo.balanceZeroUnShow ? 1 : 0,
        aa_num: searchInfo.aae_id,
        fcid: searchInfo.fcid,
        quanlity: searchInfo.showQuanlity,
        showall: searchInfo.showYear,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
    };
    loading.value = true;
    columns.value = [];
    request({
        url: `/api/AATrialBalance/WithAsub`,
        method: "get",
        params,
    })
        .then((res: IResponseModel<IWithAsub>) => {
            loading.value = false;
            tableData.value = res.data?.rows;
            columns.value = setTransverseColumns(res.data, searchInfo.showYear, searchInfo.showQuanlity);
            paginationData.total = res.data?.total;
        })
        .catch((err) => {
            loading.value = false;
            console.log(err);
        });
}
//取消
function handleClose() {
    containerRef.value?.handleClose();
}
//重置
function handleReset() {
    searchInfo.subjectCode = "";
    searchInfo.startAsubLevel = 1;
    searchInfo.endAsubLevel = maxCodelength.value;
    searchInfo.aae_id = " ";
    searchInfo.fcid = -1;
    searchInfo.balanceZeroUnShow = false;
    searchRet.value = [];
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
}

//打印
function getDefaultParams(){
    return {
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        sbj_id: searchInfo.subjectCode.split(" ")[0],
        sbj_leval_s: searchInfo.startAsubLevel,
        sbj_leval_e: searchInfo.endAsubLevel,
        aa_type: searchInfo.aa_type,
        showsbj: searchInfo.showsbj ? 1 : 0,
        isBalanceZero: searchInfo.balanceZeroUnShow ? 1 : 0,
        aa_num: searchInfo.aae_id,
        fcid: searchInfo.fcid,
        quanlity: searchInfo.showQuanlity,
        showall: searchInfo.showYear,
        // page: -1,
        // rows: -1,
    }
}

const extraInfo = {
    isHideSubjectCode:false,
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "aaTrialBalance",
    window.jAccountBooksHost + "/api/AATrialBalance/Print",
    extraInfo,
    true,
    true,
    printValidator
);

//核算余额表默认是横向打印，方向禁用？
printInfo.value.direction = 1

function printValidator() {
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可打印！", type: "warning" });
        printDialogVisible.value = false;
        return false;
    }
    if (searchInfo.showhHrizontal) {
        ElNotify({
            type: "warning",
            message: "数据列过多，请导出后打印。",
        });
        return false;
    } else if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    } else if (searchInfo.startAsubLevel > searchInfo.endAsubLevel) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    return true;
}
otherOptions.value = [
    { key: "isHideSubjectCode", label: "表体不打印编码" },
    ...otherOptions.value,
];

//导出
const exportDialogVisible = ref<boolean>(false);
const exportInfo = reactive({
    aa_types: [Number(searchInfo.aa_type)],
    startPid: searchInfo.startPid,
    endPid: searchInfo.endPid,
    ifpage: "1", //0 单个  1按类别多页签   2 按类别多个文件
});

const onExportTypeChange = () => {
    const ipt = document.querySelector(".export-type-select .el-select__input");
    nextTick(() => {
        (ipt as HTMLInputElement).value = "";
    });
};

// 批量导出弹窗
const showExportDialog = () => {
    exportInfo.aa_types = [Number(searchInfo.aa_type)];
    exportInfo.startPid = searchInfo.startPid;
    exportInfo.endPid = searchInfo.endPid;
    exportInfo.ifpage = "1";
    exportDialogVisible.value = true;
};

function handleExport(exportType: number) {
    if (searchInfo.startAsubLevel > searchInfo.endAsubLevel) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    if (!exportInfo.aa_types.length) {
        ElNotify({
            message: "亲，请选择导出类别哦",
            type: "warning",
        });
        return false;
    }
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可导出！", type: "warning" });
        printDialogVisible.value = false;
        return;
    }
    const params = {
        // AS_ID: asId,
        period_s: exportType === 0 ? searchInfo.startPid : exportInfo.startPid,
        period_e: exportType === 0 ? searchInfo.endPid : exportInfo.endPid,
        sbj_id: searchInfo.subjectCode.split(" ")[0],
        sbj_leval_s: searchInfo.startAsubLevel,
        sbj_leval_e: searchInfo.endAsubLevel,
        aa_type: exportType === 0 ? searchInfo.aa_type : null,
        aa_types: exportType === 0 ? null : exportInfo.aa_types.join(","),
        showsbj: searchInfo.showsbj ? 1 : 0,
        isBalanceZero: searchInfo.balanceZeroUnShow ? 1 : 0,
        fcid: searchInfo.fcid,
        quanlity: searchInfo.showQuanlity,
        showall: searchInfo.showYear,
        aa_num: searchInfo.aae_id,
        page: -1,
        rows: -1,
        async: !isLemonClient(),
        showNumber: searchInfo.showQuanlity ? 1 : 0,
        showYear: searchInfo.showYear ? 1 : 0,
        ifpage: exportType === 0 ? 0 : exportInfo.ifpage,
    };
    if (params.period_s > params.period_e) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    globalExport(
        `${window.jAccountBooksHost}/api/AATrialBalance/Export${searchInfo.showhHrizontal ? "WithAsub" : ""}?` + getUrlSearchParams(params)
    );
}

const columns = ref<Array<IColumnProps>>([]);

//表格数据
const tableData = ref<any[]>([]);
const getTableData = () => {
    loading.value = true;
    columns.value = setColumns(searchInfo.aa_type, searchInfo.showYear, searchInfo.showQuanlity, searchInfo.fcid > 0);
    periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);

    const params = {
        ran: Math.random(),
        period_s: searchInfo.startPid,
        period_e: searchInfo.endPid,
        aa_type: searchInfo.aa_type,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
    };
    loading.value = true;
    request({
        url: "/api/AATrialBalance",
        method: "get",
        params,
    })
        .then((res: IResponseModel<ITableData>) => {
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            setAAType();
            loading.value = false;
            if (res.data.rows.length === 0) {
                emptyText.value = "暂无数据";
            }
        })
        .catch((err) => {
            console.log(err);
        })
        .finally(() => {
            loading.value = false;
        });
};

const aatypeChange = () => {
    searchInfo.aae_id = "";
    request({
        url: `/api/AssistingAccounting/ListByAAType?aaTypeId=${searchInfo.aa_type}&showAll=${true}`,
    }).then((res: IResponseModel<IAssistingAccount[]>) => {
        if (res.state === 1000) {
            searchPeriodList.value = res.data.map((item: any) => {
                return {
                    id: item.aaeid,
                    label: item.aanum + " " + item.aaname,
                };
            });
            realyArray.value = res.data.map((v) => new SelectWithLotOfDataOptions(v.aaeid + "", v.aanum + " " + v.aaname));
        }
    });
};

// 获取币别并判断币别是否展示
const InitCurrencyApi = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcListOptions];
};
// 判断是否存在使用了外币核算的科目
const InitPeriodApi2 = () => {
    return request({
        url: "/api/AccountSubject/ExistsFc",
        method: "post",
    });
};
//辅助类别
const aaShow = ref(false);
const searchPeriodList = ref<any[]>([]);
let fzTypeList = ref<any[]>([]);

function getAaType() {
    return request({
        url: "/api/AssistingAccountingType/List",
        method: "get",
    });
}
const InitPeriod = () => {
    return Promise.all([InitPeriodApi2(), getAaType()]).then((res: any) => {
        InitCurrencyApi();
        aaShow.value = res[0].data;
        searchInfo.fcid = -1;
        fzTypeList.value = res[1].data;
        searchInfo.aa_type = fzTypeList.value[0].aatypeId;
        aatypeChange();
    });
};
// 辅助项目
const searchRet = ref();
const handleInput = (value: string) => {
    // 搜索结果重置
    searchRet.value = [];
    if (value.trim()) {
        searchRet.value = showRealyArray.value.filter((item) => item.label.includes(value));
        searchRet.value.length > 0 && (searchInfo.aae_id = searchRet.value[0].value);
    } else {
        searchRet.value = [];
    }
    let selectFilterInput = document.querySelector(`.${generateId} .select-trigger .el-input__inner`) as HTMLInputElement;
    if (selectFilterInput) {
        if (!selectFilterInput.value || !searchInfo.aae_id) {
            selectFilterInput.placeholder = "";
            searchInfo.aae_id = "";
        }
    }
};
//选择框搜索后回车事件
const handleKeyDownEnter = () => {
    if (searchRet.value && searchRet.value.length > 0) {
        searchInfo.aae_id = searchRet.value[0].aaeid;
        searchRet.value = [];
    } else {
        // searchInfo.aae_id = "";
        return;
    }
};

// 科目级别
const codeLengthStr = ref("");
const maxCodelength = ref<number>(4);
function getAsubCodeLength() {
    request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        codeLengthStr.value = res.data.codeLength.join("");
        const codeLengthList: number[] = res.data.codeLength;
        const codeLength = codeLengthList.length;
        maxCodelength.value = codeLength;
        searchInfo.endAsubLevel = codeLength;
    });
}

//初始化加载
let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        InitPeriod().then(() => getTableData());
    }
});

watch(
    [
        () => searchInfo.showsbj,
        () => searchInfo.showhHrizontal,
        () => searchInfo.showQuanlity,
        () => searchInfo.showYear,
        () => paginationData.currentPage,
        () => paginationData.pageSize,
    ],
    (status) => {
        if (!status[0]) {
            searchInfo.showhHrizontal = false;
            handleSearchSwitch();
        } else {
            handleSearchSwitch();
        }
    }
);
function handleSearchSwitch() {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return false;
    }

    searchInfo.showhHrizontal ? handleHorizontalChange() : handleSearch();
    handleClose();
}

onMounted(() => {
    // periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    document.body.scrollTop = 0;
    getAsubCodeLength();
});

const showRealyArray = ref<Array<SelectWithLotOfDataOptions>>([]);
const showfzTypeList = ref<Array<any>>([]);
const showfcList = ref<any[]>([]);
watchEffect(() => { 
    showRealyArray.value = JSON.parse(JSON.stringify(realyArray.value));  
    showfzTypeList.value = JSON.parse(JSON.stringify(fzTypeList.value));  
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));  
});
function realyFilterMethod(value: string) {
    showRealyArray.value = commonFilterMethod(value, realyArray.value, 'label');
}
function fzTypeFilterMethod(value: string) {
    showfzTypeList.value = commonFilterMethod(value, fzTypeList.value, 'aatypeName');
}
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'label');
}
</script>

<style scoped lang="less">
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";
.main-tool-right {
    @media screen and (max-width: 1240px) {
        .mr-20 {
            margin-right: 5px;
        }
        .mr-20 {
            margin-right: 5px;
        }
    }
}

.main-center {
    padding: 10px 20px;
    padding-top: 0;
}
.sbjhorizon {
    :deep(.el-checkbox__label) {
        color: var(--font-color) !important;
        cursor: default !important;
    }
}

.print-content {
    text-align: center;

    .print-main {
        text-align: left;
        display: inline-block;

        .line-item {
            height: 20px;
            :deep(.el-checkbox) {
                font-weight: normal;
            }
        }
    }

    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}
.export-content {
    text-align: center;

    .export-main {
        width: 510px;
        text-align: left;
        display: inline-block;

        .line-item {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 20px;
            padding-top: 10px;
            span {
                width: 70px;
                text-align: right;
            }
            :deep(.el-checkbox) {
                font-weight: normal;
            }
            :deep(.el-select-dropdown) {
                &.is-multiple .el-select-dropdown__item.selected.hover {
                    background: var(--main-color);
                }
            }
        }
    }

    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}

.main-tool-left {
    height: 32px;
    .fztype {
        .text {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }

        text-align: left;
        float: right;
        .detail-el-select(122px);
    }
}

:deep(select2-selection--single) {
    border-color: var(--input-border-color);
    height: 32px;
    border-radius: var(--input-border-radius);
    background-color: #fff;
    border: 1px solid #e2e7eb;
    border-radius: 1px;
    outline: none;
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 34px;
    user-select: none;
    -webkit-user-select: none;
}

.main-content {
    min-width: 1210px;
}
:deep(.el-tooltip__trigger) {
    .el-input__wrapper {
        width: 276px;
    }
}
:deep(.el-table__empty-text) {
    line-height: 393px;
}
body[erp] {
    .a_nofloat {
        a {
            float: none !important;
            display: inline-block !important;
        }
    }
    .main-center {
        :deep(.el-table) {
            border: none;
            overflow: hidden;
            &.el-table--border {
                &::before,
                &::after {
                    width: 1px;
                }
            }
            .el-table__inner-wrapper {
                &::before,
                &::after {
                    height: 1px;
                }
            }
        }
    }
}
</style>
