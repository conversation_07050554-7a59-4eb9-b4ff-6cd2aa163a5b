import { request, type IResponseModel } from "@/util/service";

export async function checkHasScmSettings (scmProductType:number, scmAsid: number){
    return (await request({
        url: `/api/ScmSettings/IsSet?scmProductType=${scmProductType}&scmAsid=${scmAsid}`,
        method: "post",
    })) as any as IResponseModel<boolean>;
}

export async function getScmRelation() {
    return (await request({
        url: `/api/ScmRelation`,
    })) as any as IResponseModel<ScmRelation>;
}

export interface ScmRelation{
    isRelation: boolean;
    scmProductType: number;
    scmAsid: number;
}

export async function getScmRelationInfo() {
    return (await request({
        url: `/api/ScmRelation/Info`,
    })) as any as IResponseModel<ScmRelationInfo>;
}

export interface ScmRelationInfo extends ScmRelation{
    scmAsName: string;
    asid: number;
    asName: string;
    cstId: number;
}