import { request } from "@/util/service";
import type { VoucherSettingsModel } from "@/components/Voucher/types";

export const getVoucherSettingsApi = () => {
    return request({
        url: "/api/VoucherSettings",
        method: "get",
    });
}

export const getVoucherPrintSettingsApi = () => {
    return request({
        url: window.printHost + "/api/VoucherPrintSettings/BaseByType?settingType=0&printType=" + 1,
        method: "get",
    });
}

export interface IVoucherSettings extends VoucherSettingsModel {}