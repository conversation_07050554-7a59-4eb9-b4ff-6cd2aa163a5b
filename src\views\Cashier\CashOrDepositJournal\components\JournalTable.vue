<template>
    <Table
        ref="tableRef"
        :loading="loading"
        :data="tableData"
        :columns="columns"
        :page-is-show="true"
        :showOverflowTooltip="true"
        :layout="paginationData.layout"
        :page-sizes="paginationData.pageSizes"
        :page-size="paginationData.pageSize"
        :total="paginationData.total"
        :currentPage="paginationData.currentPage"
        :use-normal-scroll="true"
        :scrollbar-show="true"
        :class="isErp ? 'erp-table' : 'normal-table'"
        :highlight-current-row="false"
        :row-class-name="rowClassName"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @row-click="handleRowClick"
        @refresh="handleRerefresh"
        @scroll="handleInEditStatus"
        @cell-drop="cellDrop"
        :tableName="setModule"
        @agin-column-drop="watchDrop"
        @move-drop="moveDrop"
        @cell-mouse-enter="cellMouseEnter"
        @cell-mouse-leave="cellMouseLeve"
        @mousedown="mouseDownAll"
        @mouseup="mouseUpAll"
    >
        <template #select>
            <el-table-column min-width="36" align="center" header-align="center" fixed="left" :resizable="false">
                <template #header>
                    <el-checkbox class="custom" :indeterminate="indeterminate" v-model="selectAllStatus" @change="handleSelectAllChange" />
                </template>
                <template #default="scope">
                    <el-checkbox
                        v-if="selectable(scope.row)"
                        v-model="scope.row[selectKey]"
                        @click.stop
                        @change="(check: boolean) => handleSelectChange(scope.row, check)"
                    />
                </template>
            </el-table-column>
        </template>
        <template #date>
            <el-table-column
                label="日期"
                prop="date"
                :min-width="140"
                :width="getColumnWidth(setModule, 'date')"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('date', columns)"
                :fixed="getSlotIsFreeze('date', columns)"
            >
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ formatDate(scope.row.cd_date) }}</span>
                    <div
                        class="my-data-picker edit-item cd-date"
                        @click.capture="handleDateFocus"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <el-date-picker
                            ref="elDatePickerRef"
                            v-model="rowItemSearchInfo.CD_DATE"
                            type="date"
                            :clearable="false"
                            value-format="YYYY-MM-DD"
                            :editable="false"
                            @change="handleDateChange"
                            @visible-change="visibleChange"
                            :disabled="scope.row.erp_offset === '1'"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #description>
            <el-table-column
                label="摘要"
                :min-width="190"
                :width="getColumnWidth(setModule, 'description')"
                align="left"
                header-align="left"
                prop="description"
                :class-name="'opposite-column ' + getSlotClassName('description', columns)"
                :fixed="getSlotIsFreeze('description', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>摘要</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'DESCRIPTION'"
                                :isFilter="!!filterSearchInfo.DESCRIPTION"
                                :hasSearchVal="filterSearchInfo.DESCRIPTION"
                                @filterSearch="filterSearch"
                            ></TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div class="description edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                        <el-input
                            ref="descriptionInputRef"
                            v-if="descriptionTextareaShow"
                            :autosize="{ minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            resize="none"
                            v-model="rowItemSearchInfo.DESCRIPTION"
                            @focus="handleFocus('descriptionInputRef')"
                            @blur="handleBlur(scope.row, 'description')"
                            @keydown.enter="descriptionEnter"
                            @input="checkMaxLength(rowItemSearchInfo.DESCRIPTION, '摘要', 256)"
                            :disabled="scope.row.erp_offset === '1'"
                        />
                        <Tooltip v-else :content="rowItemSearchInfo.DESCRIPTION" :isInput="true" :teleported="true">
                            <el-input
                                ref="descriptionInputRef"
                                v-model="rowItemSearchInfo.DESCRIPTION"
                                @focus="handleFocus('descriptionInputRef1')"
                                @keydown.enter="descriptionEnter"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </Tooltip>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #ieTypeName>
            <el-table-column
                label="收支类别"
                :min-width="120"
                :width="getColumnWidth(setModule, 'ie_type_name')"
                align="left"
                header-align="left"
                prop="ie_type_name"
                :class-name="getSlotClassName('ieTypeName', columns)"
                :fixed="getSlotIsFreeze('ieTypeName', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>收支类别</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'IE_TYPE'"
                                :isSelect="true"
                                :selectedList="searchInfo.IE_TYPE"
                                :option="searchOptions.IE_TYPE"
                                :hasSelectList="filterSearchInfo.IE_TYPE"
                                :isFilter="isFilterMultile(filterSearchInfo.IE_TYPE, searchOptions.IE_TYPE)"
                                @filterSearch="filterSearch"
                            >
                            </TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div
                        class="paymethod-select edit-item"
                        @click.capture="handleFocus()"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <FilterCustomSelect
                            ref="ietypeSelectRef"
                            v-model="rowItemSearchInfo.IE_TYPE_NAME"
                            :teleported="true"
                            :bottom-html="checkIeTypeBottomHtml()"
                            :fit-input-width="true"
                            :filterable="true"
                            :clearable="scope.row.erp_offset !== '1'"
                            :filter-method="ietypeFilterMethod"
                            :immediately-blur="false"
                            placeholder=" "
                            @bottom-click="handleNewAAE(CashAAType.IEType)"
                            @keyup-enter="handleIeTypeEnter"
                            @blur="handleBlur(scope.row, 'ieType')"
                            @change="handleIeTypeChange"
                            :disabled="scope.row.erp_offset === '1'"
                        >
                            <Option v-for="item in filterIEType" :key="item.subkey" :value="item.subkey" :label="item.value2" />
                        </FilterCustomSelect>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #oppositeParty>
            <el-table-column
                label="往来单位"
                :min-width="215"
                :width="getColumnWidth(setModule, 'opposite_party')"
                align="left"
                header-align="left"
                prop="opposite_party"
                :class-name="'opposite-column ' + getSlotClassName('oppositeParty', columns)"
                :fixed="getSlotIsFreeze('oppositeParty', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>往来单位</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'OPPOSITE_PARTY'"
                                :isSelect="true"
                                :multiSelect="false"
                                :isVirtual="true"
                                :SSinglist="searchOptions.OPPOSITE_PARTY"
                                :virtualSelect="filterSearchInfo.OPPOSITE_PARTY"
                                :isFilter="!!filterSearchInfo.OPPOSITE_PARTY"
                                @filterSearch="filterSearch"
                                :isOppsite="true"
                            >
                            </TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div
                        class="oppositeParty edit-item"
                        @click.capture="handleFocus()"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <div class="icon" @click.stop="handleOpenUnitDialog" v-show="!isErp"></div>
                        <el-autocomplete
                            v-if="oppositePartyTextareaShow"
                            ref="oppositePartyRef"
                            v-model="rowItemSearchInfo.OPPOSITE_PARTY"
                            :fetch-suggestions="querySearch"
                            :prop="[{ required: true, trigger: ['change'] }]"
                            :trigger-on-focus="false"
                            placeholder=" "
                            type="textarea"
                            maxlength="256"
                            :autosize="{ minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            class="oppositePartyInput"
                            :debounce="300"
                            :teleported="true"
                            :fit-input-width="true"
                            popper-class="opposite-autocomplete-popper journal-table-popper"
                            @select="handleUnitSelect"
                            @keyup.enter="handleOpTypeEnter"
                            @focus="handleUnitFocus"
                            @blur="handleBlur(scope.row, 'oppositeParty')"
                            @change="checkMaxLength(rowItemSearchInfo.OPPOSITE_PARTY, '往来单位')"
                            :disabled="scope.row.erp_offset === '1'"
                        >
                            <template #default="{ item }">
                                <div class="value">
                                    <ToolTip :content="item.value" :line-clamp="2" :teleported="true" placement="right-start" :offset="-8">
                                        {{ item.value }}
                                    </ToolTip>
                                </div>
                            </template>
                        </el-autocomplete>
                        <Tooltip v-else :content="rowItemSearchInfo.OPPOSITE_PARTY" :isInput="true" :teleported="true">
                            <el-autocomplete
                                ref="oppositePartyRef"
                                v-model="rowItemSearchInfo.OPPOSITE_PARTY"
                                :fetch-suggestions="querySearch"
                                :prop="[{ required: true, trigger: ['change'] }]"
                                :trigger-on-focus="false"
                                placeholder=" "
                                class="oppositePartyInput"
                                :debounce="300"
                                :teleported="true"
                                :fit-input-width="true"
                                popper-class="opposite-autocomplete-popper journal-table-popper"
                                @select="handleUnitSelect"
                                @keyup.enter="handleOpTypeEnter"
                                @focus="handleUnitFocus"
                                @blur="handleBlur(scope.row)"
                                :disabled="scope.row.erp_offset === '1'"
                            >
                                <template #default="{ item }">
                                    <div class="value">{{ item.value }}</div>
                                </template>
                            </el-autocomplete>
                        </Tooltip>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #project>
            <el-table-column
                label="项目"
                :min-width="172"
                :width="getColumnWidth(setModule, 'project_name')"
                align="left"
                header-align="left"
                prop="project_name"
                :class-name="getSlotClassName('project', columns)"
                :fixed="getSlotIsFreeze('project', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>项目</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'PROJECT'"
                                :isSelect="true"
                                :selectedList="searchInfo.PROJECT"
                                :option="searchOptions.PROJECT"
                                :hasSelectList="filterSearchInfo.PROJECT"
                                :isFilter="isFilterMultile(filterSearchInfo.PROJECT, searchOptions.PROJECT)"
                                @filterSearch="filterSearch"
                            >
                            </TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div
                        class="project-select edit-item"
                        @click.capture="handleFocus()"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <Select
                            ref="projectRef"
                            popperClass="journal-table-popper"
                            v-model="rowItemSearchInfo.PROJECT"
                            :teleported="true"
                            :bottom-html="checkPermission(['assistingaccount-canedit']) ? selectBottomHtml : ''"
                            :fit-input-width="true"
                            :filterable="true"
                            :clearable="true"
                            :immediately-blur="false"
                            @bottom-click="handleNewAAE(CashAAType.Project)"
                            @keyup-enter="handleProjectEnter"
                            @blur="handleBlur(scope.row)"
                            @visible-change="handleProjectVisibleChange"
                            placeholder=" "
                            :filter-method="projectFilterMethod"
                        >
                            <Option v-for="item in projectListOptions" :key="item.aaeid" :value="item.aaeid + ''" :label="item.aaname" />
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #department>
            <el-table-column
                label="部门"
                :min-width="172"
                :width="getColumnWidth(setModule, 'department_name')"
                align="left"
                header-align="left"
                prop="department_name"
                :class-name="getSlotClassName('department', columns)"
                :fixed="getSlotIsFreeze('department', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>部门</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'DEPARTMENT'"
                                :isSelect="true"
                                :selectedList="searchInfo.DEPARTMENT"
                                :option="searchOptions.DEPARTMENT"
                                :hasSelectList="filterSearchInfo.DEPARTMENT"
                                :isFilter="isFilterMultile(filterSearchInfo.DEPARTMENT, searchOptions.DEPARTMENT)"
                                @filterSearch="filterSearch"
                            >
                            </TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div
                        class="project-select edit-item"
                        @click.capture="handleFocus()"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <Select
                            ref="departmentRef"
                            popperClass="journal-table-popper"
                            v-model="rowItemSearchInfo.DEPARTMENT"
                            :teleported="true"
                            :bottom-html="hasPermissionDepartment() ? selectBottomHtml : ''"
                            :fit-input-width="true"
                            :filterable="true"
                            :clearable="true"
                            :immediately-blur="false"
                            placeholder=" "
                            @bottom-click="handleNewAAE(CashAAType.Department)"
                            @keyup-enter="handleDepartmentEnter"
                            @blur="handleBlur(scope.row)"
                            :filter-method="departFilterMethod"
                        >
                            <Option v-for="item in departmentListOptions" :key="item.aaeid" :value="item.aaeid + ''" :label="item.aaname" />
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #income>
            <el-table-column
                label="收入（借方）"
                :min-width="100"
                :width="getColumnWidth(setModule, 'income')"
                align="right"
                header-align="right"
                prop="income"
                :class-name="getSlotClassName('income', columns)"
                :fixed="getSlotIsFreeze('income', columns)"
            >
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ formatMoney(scope.row.income) }}</span>
                    <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                        <JournalAmountInput
                            ref="incomeInputRef"
                            v-model="rowItemSearchInfo.INCOME"
                            @focus="handleAmountFocus('income')"
                            @blur="handleAmountBlur(scope.row, 'income')"
                            @key-down-enter="handleIncomeEnter"
                            @change="handleIncomeChange"
                            :disabled="scope.row.erp_offset === '1'"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #incomeFc>
            <el-table-column
                label="收入（借方）"
                header-align="center"
                :class-name="getSlotClassName('incomeFc', columns)"
                :fixed="getSlotIsFreeze('incomeFc', columns)"
            >
                <el-table-column
                    label="原币"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'incomeFc')"
                    align="right"
                    header-align="right"
                    prop="incomeFc"
                >
                    <template #default="scope">
                        <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ formatMoney(scope.row.income) }}</span>
                        <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                            <JournalAmountInput
                                ref="incomeInputRef"
                                v-model="rowItemSearchInfo.INCOME"
                                @focus="handleAmountFocus('income')"
                                @blur="handleAmountBlur(scope.row, 'income')"
                                @input="handleIncomeInput"
                                @key-down-enter="handleIncomeFcEnter"
                                @change="handleIncomeChange"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="汇率"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'income_rate')"
                    align="right"
                    header-align="right"
                    prop="income_rate"
                >
                    <template #default="scope">
                        <DecimalSpan :class="currentEditIndex === scope.row.index ? 'none' : ''" :value="scope.row.income_rate" />
                        <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                            <JournalAmountInput
                                :limit="8"
                                v-model="rowItemSearchInfo.INCOME_RATE"
                                @focus="handleFocus"
                                @blur="handleBlur(scope.row)"
                                @input="handleIncomeRateInput"
                                @change="(val) => (rowItemSearchInfo.INCOME_RATE = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="本币"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'income_standard')"
                    align="right"
                    header-align="right"
                    prop="income_standard"
                >
                    <template #default="scope">
                        <span :class="currentEditIndex === scope.row.index ? 'none' : ''">
                            {{ formatMoney(scope.row.income_standard) }}
                        </span>
                        <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                            <JournalAmountInput
                                v-model="rowItemSearchInfo.INCOME_STANDARD"
                                @focus="handleFocus"
                                @blur="handleBlur(scope.row)"
                                @input="handleIncomeStandardInput"
                                @change="(val) => (rowItemSearchInfo.INCOME_STANDARD = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                    </template>
                </el-table-column>
            </el-table-column>
        </template>
        <template #expenditure>
            <el-table-column
                label="支出（贷方）"
                :min-width="100"
                :width="getColumnWidth(setModule, 'expenditure')"
                align="right"
                header-align="right"
                prop="expenditure"
                :class-name="getSlotClassName('expenditure', columns)"
                :fixed="getSlotIsFreeze('expenditure', columns)"
            >
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ formatMoney(scope.row.expenditure) }}</span>
                    <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                        <JournalAmountInput
                            ref="expenditureInputRef"
                            v-model="rowItemSearchInfo.EXPENDITURE"
                            @focus="handleAmountFocus('expenditure')"
                            @blur="handleAmountBlur(scope.row, 'expenditure')"
                            @key-down-enter="handleExpenditureEnter"
                            @change="handleExpentitureChange"
                            :disabled="scope.row.erp_offset === '1'"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #expenditureFc>
            <el-table-column
                label="支出（贷方）"
                header-align="center"
                :class-name="getSlotClassName('expenditureFc', columns)"
                :fixed="getSlotIsFreeze('expenditureFc', columns)"
            >
                <el-table-column
                    label="原币"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'expenditureFc')"
                    align="right"
                    header-align="right"
                    prop="expenditureFc"
                >
                    <template #default="scope">
                        <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ formatMoney(scope.row.expenditure) }}</span>
                        <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                            <JournalAmountInput
                                ref="expenditureInputRef"
                                v-model="rowItemSearchInfo.EXPENDITURE"
                                @focus="handleAmountFocus('expenditure')"
                                @blur="handleAmountBlur(scope.row, 'expenditure')"
                                @input="handleExpenditureInput"
                                @key-down-enter="handleExpenditureFcEnter"
                                @change="handleExpentitureChange"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="汇率"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'expenditure_rate')"
                    align="right"
                    header-align="right"
                    prop="expenditure_rate"
                >
                    <template #default="scope">
                        <DecimalSpan :class="currentEditIndex === scope.row.index ? 'none' : ''" :value="scope.row.expenditure_rate" />
                        <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                            <JournalAmountInput
                                v-model="rowItemSearchInfo.EXPENDITURE_RATE"
                                :limit="8"
                                @focus="handleFocus"
                                @blur="handleBlur(scope.row)"
                                @input="handleExpenditureRateInput"
                                @change="(val) => (rowItemSearchInfo.EXPENDITURE_RATE = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="本币"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'expenditure_standard')"
                    align="right"
                    header-align="right"
                    prop="expenditure_standard"
                >
                    <template #default="scope">
                        <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{
                            formatMoney(scope.row.expenditure_standard)
                        }}</span>
                        <div class="income edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                            <JournalAmountInput
                                v-model="rowItemSearchInfo.EXPENDITURE_STANDARD"
                                @focus="handleFocus()"
                                @blur="handleBlur(scope.row)"
                                @input="handleExpenditureStandardInput"
                                @change="(val) => (rowItemSearchInfo.EXPENDITURE_STANDARD = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                    </template>
                </el-table-column>
            </el-table-column>
        </template>
        <template #amount>
            <el-table-column
                label="余额"
                :min-width="120"
                :width="getColumnWidth(setModule, 'amount')"
                align="right"
                header-align="right"
                prop="amount"
                :class-name="getSlotClassName('amount', columns)"
                :fixed="getSlotIsFreeze('amount', columns)"
            >
                <template #default="scope">
                    <div
                        class="amount edit-item"
                        v-if="
                            scope.row.description === '初始化余额' &&
                            (cdAccount !== 'all' || (cdAccount === 'all' && accountListLength === 1)) &&
                            props.initAmountCanEdit &&
                            checkJournalPermission('canedit')
                        "
                    >
                        <JournalAmountInput
                            v-model="amountValue"
                            ref="amountInputRef"
                            @focus="handleFocus"
                            @blur="handleBeginBlur"
                            @input="handleBeginAmountInput"
                            @key-down-enter="handleAmountEnter"
                            @change="(val) => (amountValue = val)"
                            :disabled="scope.row.erp_offset === '1'"
                        />
                    </div>
                    <span v-else>{{ formatMoney(scope.row.amount) }}</span>
                </template>
            </el-table-column>
        </template>
        <template #amountFc>
            <el-table-column
                label="余额"
                header-align="center"
                :class-name="getSlotClassName('amountFc', columns)"
                :fixed="getSlotIsFreeze('amountFc', columns)"
            >
                <el-table-column
                    label="原币"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'amountFc')"
                    align="right"
                    header-align="right"
                    prop="amountFc"
                >
                    <template #default="scope">
                        <div
                            class="amount edit-item"
                            v-if="
                                scope.row.description === '初始化余额' &&
                                cdAccount !== 'all' &&
                                !hiddenAmountFc &&
                                props.initAmountCanEdit &&
                                checkJournalPermission('canedit')
                            "
                        >
                            <JournalAmountInput
                                ref="amountInputRef"
                                v-model="amountValue"
                                @focus="handleFocus"
                                @blur="handleBeginBlur"
                                @input="handleBeginAmountInput"
                                @key-down-enter="handleAmountEnter"
                                @change="(val) => (amountValue = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                        <span v-else> {{ formatMoney(scope.row.amount) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="汇率"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'amount_rate')"
                    align="right"
                    header-align="right"
                    prop="amount_rate"
                >
                    <template #default="scope">
                        <div
                            class="amount edit-item"
                            v-if="
                                scope.row.description === '初始化余额' &&
                                cdAccount !== 'all' &&
                                !hiddenAmountFc &&
                                props.initAmountCanEdit &&
                                checkJournalPermission('canedit')
                            "
                        >
                            <JournalAmountInput
                                ref="amountRateInputRef"
                                :limit="8"
                                v-model="amountRateValue"
                                @focus="handleFocus"
                                @blur="handleBeginBlur"
                                @input="handleBeginAmountRateInput"
                                @keydown.enter="handleAmountRateEnter"
                                @change="(val) => (amountRateValue = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                        <DecimalSpan v-else :value="scope.row.amount_rate" />
                    </template>
                </el-table-column>
                <el-table-column
                    label="本币"
                    :min-width="90"
                    :width="getColumnWidth(setModule, 'amount_standard')"
                    align="right"
                    header-align="right"
                    prop="amount_standard"
                >
                    <template #default="scope">
                        <div
                            class="amount edit-item"
                            v-if="
                                scope.row.description === '初始化余额' &&
                                cdAccount !== 'all' &&
                                props.initAmountCanEdit &&
                                checkJournalPermission('canedit')
                            "
                        >
                            <JournalAmountInput
                                ref="amountStandardInputRef"
                                v-model="amountStandardValue"
                                @focus="handleFocus"
                                @blur="handleBeginBlur"
                                @input="handleBeginAmountStandardInput"
                                @keydown.enter="handleAmountStandardEnter"
                                @change="(val) => (amountStandardValue = val)"
                                :disabled="scope.row.erp_offset === '1'"
                            />
                        </div>
                        <span v-else>{{ formatMoney(scope.row.amount_standard) }}</span>
                    </template>
                </el-table-column>
            </el-table-column>
        </template>
        <template #account>
            <el-table-column
                :label="props.journalType === '1020' ? '银行账户' : '现金账户'"
                :min-width="155"
                :width="getColumnWidth(setModule, 'account')"
                prop="account"
                align="right"
                header-align="right"
                :class-name="getSlotClassName('account', columns)"
                :fixed="getSlotIsFreeze('account', columns)"
            >
                <template #default="scope">
                    <div class="edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index" @click.capture="handleFocus()">
                        <Select
                            ref="amountSelectRef"
                            popperClass="journal-table-popper"
                            v-model="rowItemSearchInfo.ACCOUNT"
                            :teleported="true"
                            :fit-input-width="true"
                            :filterable="true"
                            :immediately-blur="false"
                            @keyup-enter="handleAccountEnter"
                            @blur="handleBlur(scope.row)"
                            placeholder=" "
                            :disabled="scope.row.erp_offset === '1'"
                            :filter-method="accountFilterMethod"
                        >
                            <Option
                                v-for="item in cdAccountListOptions"
                                :fit-input-width="true"
                                :key="item.ac_id"
                                :value="item.ac_id"
                                :label="item.label"
                            />
                        </Select>
                    </div>
                    <ToolTip
                        v-else
                        :content="
                            (scope.row?.defaultClickItem || !scope.row?.cd_date
                                ? ''
                                : scope.row?.cd_account === ''
                                ? ''
                                : scope.row?.ac_no + ' - ' + scope.row?.ac_name + getForbiddenText(scope.row?.ac_no)) || ''
                        "
                        :class="currentEditIndex === scope.row.index ? 'none' : ''"
                    />
                </template>
            </el-table-column>
        </template>
        <template #includes>
            <el-table-column
                label="关联凭证"
                :min-width="150"
                :width="getColumnWidth(setModule, 'includes')"
                prop="includes"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('includes', columns)"
                :fixed="getSlotIsFreeze('includes', columns)"
            >
                <template #default="scope">
                    <span
                        v-if="scope.row.v_num2"
                        :class="[checkVoucherPermission(scope.row.v_num2) ? 'link' : 'cursor-default', '']"
                        style="display: flex; align-items: center"
                        @click.stop="checkVoucherPermission(scope.row.v_num2) ? routeToVoucherPage(scope.row) : ''"
                    >
                        {{ scope.row.v_num2 }}
                    </span>
                </template>
            </el-table-column>
        </template>
        <template #oppositePartyNo>
            <el-table-column
                label="往来单位账号"
                :min-width="115"
                :width="getColumnWidth(setModule, 'oppositePartyNo')"
                prop="oppositePartyNo"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('oppositePartyNo', columns)"
                :fixed="getSlotIsFreeze('oppositePartyNo', columns)"
            >
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ scope.row.opposite_party_no }}</span>
                    <div class="note edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                        <el-input
                            maxlength="128"
                            ref="oppsitePartyNoRef"
                            v-model="rowItemSearchInfo.OPPOSITE_PARTY_NO"
                            @focus="handleFocus('oppsitePartyNoRef')"
                            @blur="handleBlur(scope.row)"
                            @input="checkMaxLength(rowItemSearchInfo.OPPOSITE_PARTY_NO, '往来单位账号', 128)"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #oppositePartyNoBank>
            <el-table-column
                label="往来单位开户行"
                :min-width="115"
                :width="getColumnWidth(setModule, 'oppositePartyBank')"
                prop="oppositePartyBank"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('oppositePartyBank', columns)"
                :fixed="getSlotIsFreeze('oppositePartyBank', columns)"
            >
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ scope.row.opposite_party_bank }}</span>
                    <div class="note edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                        <el-input
                            maxlength="128"
                            ref="oppsitePartyBankRef"
                            v-model="rowItemSearchInfo.OPPOSITE_PARTY_BANK"
                            @focus="handleFocus('oppsitePartyBankRef')"
                            @blur="handleBlur(scope.row)"
                            @input="checkMaxLength(rowItemSearchInfo.OPPOSITE_PARTY_BANK, '往来单位开户行', 128)"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #paymentMethod>
            <el-table-column
                label="结算方式"
                :min-width="165"
                :width="getColumnWidth(setModule, 'payment_method_name')"
                align="left"
                header-align="left"
                prop="payment_method_name"
                :class-name="getSlotClassName('paymentMethod', columns)"
                :fixed="getSlotIsFreeze('paymentMethod', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>结算方式</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'PAYMENT_METHOD'"
                                :isSelect="true"
                                :selectedList="searchInfo.PAYMENT_METHOD"
                                :option="searchOptions.PAYMENT_METHOD"
                                :hasSelectList="filterSearchInfo.PAYMENT_METHOD"
                                :isFilter="isFilterMultile(filterSearchInfo.PAYMENT_METHOD, searchOptions.PAYMENT_METHOD)"
                                @filterSearch="filterSearch"
                            >
                            </TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div
                        class="paymentMethod-select edit-item"
                        @click.capture="handleFocus()"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <Select
                            ref="payMethodRef"
                            popperClass="pay-method-popper journal-table-popper"
                            v-model="rowItemSearchInfo.PAYMENT_METHOD"
                            :teleported="true"
                            :fit-input-width="true"
                            :filterable="true"
                            :immediately-blur="false"
                            placeholder=" "
                            noMatchText="无数据"
                            :bottom-html="selectBottomHtml"
                            @bottom-click="handleNewAAE(CashAAType.PayMethod)"
                            @keyup-enter="handlePayMethodEnter"
                            @blur="handleBlur(scope.row)"
                            :filter-method="paymentFilterMethod"
                        >
                            <Option
                                v-for="item in payMethodListOptions"
                                :key="item.id"
                                :value="item.id"
                                :label="item.value"
                                :line-clamp="1"
                                :class-name="item.purview === 'True' ? 'edit-payment-option payment-option' : 'payment-option'"
                                :paddingRight="item.purview === 'True' ? canEditPaymentPaddingRight : overflowElementStyle.right"
                            >
                                <span>{{ item.value }}</span>
                                <template v-if="item.purview === 'True'">
                                    <span @click.stop="handleNewAAE(CashAAType.PayMethod, item.id, item.value)" class="edit option-icon">
                                        <el-icon><EditPen /></el-icon>
                                    </span>
                                    <span @click.stop="handleDeletePayMethod(item.id, item.value)" class="delete option-icon">
                                        <el-icon><Close /></el-icon>
                                    </span>
                                </template>
                            </Option>
                        </Select>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #receiptNo>
            <el-table-column
                label="票据号"
                :min-width="80"
                :width="getColumnWidth(setModule, 'receiptNo')"
                prop="receiptNo"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('receiptNo', columns)"
                :fixed="getSlotIsFreeze('receiptNo', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>票据号</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'RECEIPT_NO'"
                                :isFilter="!!filterSearchInfo.RECEIPT_NO"
                                :hasSearchVal="filterSearchInfo.RECEIPT_NO"
                                @filterSearch="filterSearch"
                            ></TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ scope.row.receipt_no }}</span>
                    <div
                        class="receiptNo edit-item"
                        @click.capture="handleFocus()"
                        v-if="scope.row.cd_date && currentEditIndex === scope.row.index"
                    >
                        <el-input
                            ref="receiptNoRef"
                            v-model="rowItemSearchInfo.RECEIPT_NO"
                            @blur="handleBlur(scope.row)"
                            @keydown.enter="handleReceiptNoEnter"
                            @focus="handleFocus('receiptNoRef')"
                        />
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #note>
            <el-table-column
                label="备注"
                :min-width="115"
                :width="getColumnWidth(setModule, 'note')"
                prop="note"
                align="left"
                header-align="left"
                :class-name="'opposite-column ' + getSlotClassName('note', columns)"
                :fixed="getSlotIsFreeze('note', columns)"
            >
                <template #header>
                    <div class="header-operate">
                        <span>备注</span>
                        <div class="header-operate-rt">
                            <TableHeaderFilter
                                :prop="'NOTE'"
                                :isFilter="!!filterSearchInfo.NOTE"
                                :hasSearchVal="filterSearchInfo.NOTE"
                                @filterSearch="filterSearch"
                            ></TableHeaderFilter>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <span :class="currentEditIndex === scope.row.index ? 'none' : ''">{{ scope.row.note }}</span>
                    <div class="note edit-item" v-if="scope.row.cd_date && currentEditIndex === scope.row.index">
                        <el-input
                            ref="noteRef"
                            v-if="noteTextareaShow"
                            :autosize="{ minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            resize="none"
                            v-model="rowItemSearchInfo.NOTE"
                            @focus="handleFocus('noteRef')"
                            @blur="handleBlur(scope.row, 'note')"
                            @keydown.enter="handleNoteEnter"
                            @input="checkMaxLength(rowItemSearchInfo.NOTE, '备注', 256)"
                        />
                        <Tooltip v-else :content="rowItemSearchInfo.NOTE" :isInput="true" :teleported="true">
                            <el-input
                                style="width: 102%"
                                ref="noteRef"
                                v-model="rowItemSearchInfo.NOTE"
                                @focus="handleFocus('noteRef3')"
                                @keydown.enter="handleNoteEnter"
                            />
                        </Tooltip>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #tranNo>
            <el-table-column
                label="交易流水号"
                :min-width="180"
                :width="getColumnWidth(setModule, 'tranNo')"
                prop="tranNo"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('tranNo', columns)"
                :fixed="getSlotIsFreeze('tranNo', columns)"
            >
                <template #default="scope">
                    <span>{{ scope.row.tran_no }}</span>
                </template>
            </el-table-column>
        </template>
        <template #lineSn>
            <el-table-column
                label="日记账序号"
                :show-overflow-tooltip="false"
                :min-width="170"
                :width="getColumnWidth(setModule, 'lineSn')"
                prop="lineSn"
                align="left"
                header-align="left"
                :class-name="getSlotClassName('lineSn', columns)"
                :fixed="getSlotIsFreeze('lineSn', columns)"
            >
                <template #default="scope">
                    <a
                        class="link"
                        style="display: flex; align-items: center"
                        v-if="scope.row.line_sn_name.startsWith('<')"
                        @click.stop="routeToJournalPage(scope.row.line_sn_name,scope.row.created_date)"
                    >
                        <el-tooltip class="box-item" effect="light" placement="top">
                            <template #content>
                                <span>
                                    <span id="lineSn">{{ scope.row.line_sn_name.replace(/<[^<>]+>/g, "") }}</span>
                                    <el-icon class="lineSn-copy" style="color: var(--main-color)" @click="handleCopy"
                                        ><DocumentCopy
                                    /></el-icon>
                                </span>
                            </template>
                            <span class="text-overflow-ellipsis">{{ scope.row.line_sn_name.replace(/<[^<>]+>/g, "") }}</span>
                        </el-tooltip>
                    </a>
                    <span v-else>
                        <el-tooltip class="box-item" effect="light" placement="top">
                            <template #content>
                                <span>
                                    <span id="lineSn">{{ scope.row.line_sn_name.replace(/<[^<>]+>/g, "") }}</span>
                                    <el-icon class="lineSn-copy" style="color: var(--main-color)" @click="handleCopy"
                                        ><DocumentCopy
                                    /></el-icon>
                                </span>
                            </template>
                            <span class="text-overflow-ellipsis">{{ scope.row.line_sn_name.replace(/<[^<>]+>/g, "") }}</span>
                        </el-tooltip>
                    </span>
                </template>
            </el-table-column>
        </template>
        <template #operation>
            <el-table-column
                min-width="220"
                align="left"
                header-align="left"
                :fixed="getSlotIsFreeze('operation', columns)"
                :resizable="false"
                label="操作"
            >
                <template #header>
                    <div class="header-operate">
                        <span>操作</span>
                        <div class="header-operate-rt">
                            <div class="set-icon" @click="openColSet"></div>
                        </div>
                    </div>
                </template>
                <template #default="scope">
                    <div v-if="scope.row.cd_date" class="handle">
                        <a
                            class="link"
                            @click.stop="insertNewRow(scope.row)"
                            v-show="scope.row.showAll && formatDate(scope.row.cd_date) >= props.checkDate"
                            v-if="checkJournalPermission('canedit')"
                        >
                            插入
                        </a>
                        <a
                            :class="scope.row.ie_type === '-1' || scope.row.erp_offset === '1' ? 'link disabled' : 'link'"
                            @click.stop="deleteRow(scope.row)"
                            v-if="checkJournalPermission('candelete')"
                            v-show="(scope.row.showAll || scope.row.onlyShowDelete) && formatDate(scope.row.cd_date) >= props.checkDate"
                            :title="
                                scope.row.ie_type === '-1'
                                    ? '请在内部转账删除'
                                    : scope.row.erp_offset === '1'
                                    ? '资金数据已经关联单据，请先取消关联！'
                                    : ''
                            "
                        >
                            删除
                        </a>
                        <a
                            class="link"
                            v-if="checkJournalPermission('canprint')"
                            @click.stop="printRow(scope.row)"
                            v-show="scope.row.showAll"
                        >
                            打印
                        </a>
                        <template v-if="scope.row.showAll">
                            <el-tooltip v-if="scope.row.showFromBankReceiptMessage === '1'" effect="light" :offset="6">
                                <template #content>银企互联第2天下午才同步回单，您也可以点击【回单】手动上传~</template>
                                <a class="link" @click.stop="receiptRow(scope.row)">回单</a>
                            </el-tooltip>
                            <a class="link" @click.stop="receiptRow(scope.row)" v-else>
                                {{ formatFileState(scope.row.receiptCount) }}
                            </a>
                        </template>
                        <a
                            v-if="checkJournalPermission('canedit')"
                            :class="scope.row.ie_type == '-1' ? 'link disabled' : 'link'"
                            @click.stop="copyRow(scope.row)"
                            v-show="scope.row.showAll && formatDate(scope.row.cd_date) >= props.checkDate"
                            :title="scope.row.ie_type == '-1' ? '内部转账数据不支持复制' : ''"
                        >
                            复制
                        </a>
                    </div>
                </template>
            </el-table-column>
        </template>
        <template #pageOther v-if="!isErp && selections.length > 0">
            <span>
                已选 {{ selections.length }} 条
                <a class="link pl-20" @click="handleOpenPageSelectDialog">查看</a>
                <a class="link pl-20" @click="handleClearSelection">取消全部</a>
            </span>
        </template>
    </Table>
    <DialogForHasVoucher ref="dialogForHasVoucherRef" />
    <DialogCellOperate
        :jType="journalType"
        ref="dialogCellOperateRef"
        @save-ietype="handleIETypeSave"
        @save-aa="handleSaveAAE"
        @save-pay-method="handleSavePayMethod"
        @unit-closed="handleOpTypeEnter"
        @unit-change="changeCellUnitInfo"
        :autoAddName="autoAddName"
        :trySyncCompany="trySyncCompany"
    />
    <DiffPageSelected
        ref="diffPageSelectedRef"
        rowKey="rowKey"
        :selectedData="selections"
        :columns="dialogColumns"
        @row-click="handlePageRowClick"
        @select="handlePageSelect"
        @select-all="handleClearSelection"
    />
    <!-- 列设置 -->
    <ColumnSet
        ref="columnSetRef"
        v-model="columnSetShow"
        :data="cashierColumnsSet"
        :allColumns="allColumns"
        :setModuleType="module"
        @saveColumnSet="saveColumnSet"
    >
    </ColumnSet>
    <!-- 业财已关联单据可修改字段弹窗 -->
    <DialogForHasBill ref="DialogForHasBillRef" />
</template>

<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch, watchEffect } from "vue";
import { formatClipPointNum, formatMoney } from "@/util/format";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";
import { getATagParams } from "@/views/Cashier/Report/utils";
import {
    formatDate,
    getTableColumns,
    GetStart,
    GetEnd,
    formatRate,
    selectable,
    selectBottomHtml,
    getLineSnAndPerfix,
    getLineSnNameWithAnchorTag,
} from "../utils";
import { usePagination, type IPaginationData } from "@/hooks/usePagination";
import { getshortdata } from "@/views/Cashier/CDJournal/utils";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import { ElConfirm } from "@/util/confirm";
import { getCompanyList } from "@/util/getCompanyList";
import { textareaBottom } from "@/views/FixedAssets/FixedAssets/utils";
import { CashAAType, selectKey } from "../types";
import { autocompleteSelfAdaption } from "@/views/Settings/AssistingAccounting/utils";
import { overflowElementStyle } from "@/components/Option/utils";
import { useDebugCurrentPage } from "@/hooks/useDebugCurrentPage";
import { getGlobalLodash } from "@/util/lodash";

import { dayjs, type ElDatePicker, type ElInput } from "element-plus";
import type { ICompanyInfo } from "@/api/getCompanyList";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ICDAccountItem } from "@/views/Cashier/components/types";
import type {
    IPaymentMethodItem,
    IPayMethod,
    ITableItem,
    IRowItemEditInfo,
    CashIEType,
    IUnitUpdateParams,
    ISearchInfo,
    ISOption,
    IFSearchItem,
} from "../types";
import type { IAssistingAccount } from "@/api/assistingAccounting";
import type { IWordCut } from "@/util/wordCut";

import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import DialogCellOperate from "./DialogCellOperate.vue";
import DecimalSpan from "@/components/DecimalSpan/index.vue";
import Option from "@/components/Option/index.vue";
import ToolTip from "./JournalTableToolTip.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import FilterCustomSelect from "@/views/Statements/components/EditEquation/FilterCustomSelect.vue";
import JournalAmountInput from "./JournalAmountInput.vue";
import DialogForHasVoucher from "./DialogForHasVoucher.vue";
import DiffPageSelected from "@/components/Dialog/DiffPageSelected/index.vue";
import ColumnSet from "@/components/ColumnSet/index.vue";
import type { IColItem } from "@/components/ColumnSet/utils";
import {
    getSlotIsFreeze,
    getSlotClassName,
    getShowColumn,
    getColumnWidth,
    alterArrayPos,
    getColumnListApi,
} from "@/components/ColumnSet/utils";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";
import { isFilterMultile } from "@/components/TableHeaderFilter/utils";
import { copyText } from "@/views/Cashier/BankAndCompany/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import DialogForHasBill from "./DialogForHasBill.vue";

const elDatePickerRef = ref();
const oppositePartyRef = ref();
const tableRef = ref<InstanceType<typeof Table>>();
const dialogCellOperateRef = ref<InstanceType<typeof DialogCellOperate>>();
const dialogForHasVoucherRef = ref<InstanceType<typeof DialogForHasVoucher>>();
const descriptionInputRef = ref<InstanceType<typeof ElInput>>();
const ietypeSelectRef = ref<InstanceType<typeof Select>>();
const incomeInputRef = ref<InstanceType<typeof JournalAmountInput>>();
const expenditureInputRef = ref<InstanceType<typeof JournalAmountInput>>();
const projectRef = ref<InstanceType<typeof Select>>();
const departmentRef = ref<InstanceType<typeof Select>>();
const payMethodRef = ref<InstanceType<typeof Select>>();
const receiptNoRef = ref<InstanceType<typeof ElInput>>();
const noteRef = ref<InstanceType<typeof ElInput>>();
const amountSelectRef = ref<InstanceType<typeof Select>>();
const oppsitePartyNoRef = ref<InstanceType<typeof ElInput>>();
const oppsitePartyBankRef = ref<InstanceType<typeof ElInput>>();
const amountInputRef = ref<InstanceType<typeof JournalAmountInput>>();
const amountRateInputRef = ref<InstanceType<typeof JournalAmountInput>>();
const amountStandardInputRef = ref<InstanceType<typeof JournalAmountInput>>();
const diffPageSelectedRef = ref<InstanceType<typeof DiffPageSelected>>();

const isErp = ref(window.isErp);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const descriptionTextareaShow = ref(false);
const oppositePartyTextareaShow = ref(false);
const noteTextareaShow = ref(false);

function checkVoucherPermission(voucherNum: string) {
    if (isErp.value) {
        return checkPermission(["voucher-canview"]) && voucherNum;
    } else {
        const journalVoucher = props.journalType === "1020" ? "depositjournal-cancreatevoucher" : "cashjournal-cancreatevoucher";
        return checkPermission(["voucher-canview", journalVoucher]) && voucherNum;
    }
}
function checkJournalPermission(type: string) {
    return checkPermission([props.journalType === "1020" ? `depositjournal-${type}` : `cashjournal-${type}`]);
}

// 回车操作
function descriptionEnter() {
    descriptionInputRef.value?.blur();
    isEdit.value = true;
    ietypeSelectRef.value?.focus();
}
function checkIeTypeBottomHtml(){
    if(window.isErp){
        return checkPermission(['AssistData-编辑']) ? selectBottomHtml : '' 
    }else{
        return checkPermission(['ietype-canedit']) ? selectBottomHtml : '' 
    }
}
function handleIeTypeChange(val: string) {
    rowItemSearchInfo.IE_TYPE = val;
}
function handleIeTypeEnter() {
    ietypeSelectRef.value?.blur();
    isEdit.value = true;
    isErp.value ? handleOpenUnitDialog() : oppositePartyRef.value?.focus();
}
function handleOpTypeEnter() {
    oppositePartyRef.value?.blur();
    const ieTypeItem = props.payList.find((item) => item.subkey === rowItemSearchInfo.IE_TYPE);
    const timer = setTimeout(() => {
        clearTimeout(timer);
        isEdit.value = true;
        if (props.showAllList) {
            projectRef.value?.focus();
            return;
        }
        ieTypeItem?.value2?.startsWith("支-") ? expenditureInputRef.value?.focus() : incomeInputRef.value?.focus();
    }, 0);
}
function handleProjectEnter() {
    projectRef.value?.blur();
    isEdit.value = true;
    departmentRef.value?.focus();
}
function handleDepartmentEnter() {
    departmentRef.value?.blur();
    const ieTypeItem = props.payList.find((item) => item.subkey === rowItemSearchInfo.IE_TYPE);
    isEdit.value = true;
    ieTypeItem?.value2?.startsWith("支-") ? expenditureInputRef.value?.focus() : incomeInputRef.value?.focus();
}
function handleIncomeNext() {
    isEdit.value = true;
    rowItemSearchInfo.INCOME ? payMethodRef.value?.focus() : expenditureInputRef.value?.focus();
}
function handleIncomeEnter() {
    incomeInputRef.value?.blur();
    if (props.showAllList) {
        handleIncomeNext();
        return;
    }
    if (!rowItemSearchInfo.INCOME) {
        isEdit.value = true;
        expenditureInputRef.value?.focus();
    }
}
function handleExpenditureEnter() {
    if (props.showAllList) {
        expenditureInputRef.value?.blur();
        isEdit.value = true;
        payMethodRef.value?.focus();
        return;
    }
    if (rowItemSearchInfo.EXPENDITURE) expenditureInputRef.value?.blur();
}
function handleIncomeFcEnter() {
    incomeInputRef.value?.blur();
    handleIncomeNext();
}
function handleExpenditureFcEnter() {
    expenditureInputRef.value?.blur();
    isEdit.value = true;
    props.cdAccount === "all" ? amountSelectRef.value?.focus() : payMethodRef.value?.focus();
}
function handleAccountEnter() {
    amountSelectRef.value?.blur();
    isEdit.value = true;
    payMethodRef.value?.focus();
}
function handlePayMethodEnter() {
    payMethodRef.value?.blur();
    isEdit.value = true;
    receiptNoRef.value?.focus();
}
function handleReceiptNoEnter() {
    receiptNoRef.value?.blur();
    isEdit.value = true;
    noteRef.value?.focus();
}
function handleNoteEnter() {
    noteRef.value?.blur();
    isEdit.value = false;
}
function handleAmountEnter() {
    amountInputRef.value?.blur();
}
function handleAmountRateEnter() {
    amountRateInputRef.value?.blur();
}
function handleAmountStandardEnter() {
    amountStandardInputRef.value?.blur();
}

const props = defineProps<{
    tableData: Array<ITableItem>;
    payList: Array<IPayMethod>;
    projectList: Array<IAssistingAccount>;
    departmentList: Array<IAssistingAccount>;
    paymentMethodList: Array<IPaymentMethodItem>;
    cdAccountList: Array<ICDAccountItem>;
    loading: boolean;
    showAllList: boolean;
    useFc: boolean;
    hiddenAmountFc: boolean;
    disabledProjectList: Array<IAssistingAccount>;
    isEdit: boolean;
    cdAccount: string;
    startDate: string;
    checkDate: string;
    journalType: "1010" | "1020";
    initAmountCanEdit: boolean;
    accountListLength: number;
    allAccountListLength: number;
    wordCutList: IWordCut[];
    getSelectionList: () => Array<ITableItem>;
    searchInfo: ISearchInfo;
    searchOptions: ISOption;
    filterSearchInfo: IFSearchItem;
    trySyncCompany: (aaList: Array<{ aaeId: number, aaName: string }>) => void;
}>();
const loading = computed({
    get: () => props.loading,
    set: (val) => emit("update:loading", val),
});
const isEdit = computed({
    get: () => props.isEdit,
    set: (val) => emit("update:isEdit", val),
});
const emit = defineEmits<{
    (e: "update:loading", data: boolean): void;
    (e: "update:isEdit", data: boolean): void;
    (e: "submitEdit", row: ITableItem, rowItemSearchInfo: any): void;
    (e: "insertNewRow", data: ITableItem): void;
    (e: "copyRow", data: ITableItem): void;
    (e: "deleteRow", data: ITableItem): void;
    (e: "printRow", data: ITableItem): void;
    (e: "receiptRow", data: ITableItem): void;
    (e: "selection-change", data: Array<ITableItem>): void;
    (e: "load-data"): void;
    (e: "save-ietype", type: CashIEType, code: string, name: string): void;
    (e: "save-project", code: string, name: string): void;
    (e: "save-department", code: string, name: string): void;
    (e: "save-pay-method", name: string): void;
    (e: "change-row-date", startDate: string, endDate: string, row: ITableItem): void;
    (e: "filterSearch", prop: string, value: any): void;
    (e: "filterSearch", prop: string, value: any): void;
    (e: "move-drop", oldIndex: number, newIndex: number, scrollTop: number, evt:any, originScrollTop: number): void;
}>();

let module = computed(() => {
    return props.journalType === "1010" ? 100 : 200;
});
const wordCutList = computed(() => props.wordCutList || []);

const dialogColumns = ref<Array<IColumnProps>>([]);
function handlePageRowClick(row: ITableItem) {
    const realRow = selections.value.find((item) => checkIsSameJournal(item, row));
    const currentRow = props.tableData.find((item) => checkIsSameJournal(item, row));
    if (currentRow) {
        currentRow[selectKey] = false;
    }
    realRow && handleSelectChange(realRow, false);
}
function handlePageSelect(select: Array<ITableItem>) {
    const row = selections.value.find((row) => !select.includes(row));
    row && handlePageRowClick(row);
}
const moreFcColumns = [
    { label: "原币", prop: "" },
    { label: "汇率", prop: "_rate" },
    { label: "本币", prop: "_standard" },
];
function handleOpenPageSelectDialog() {
    const defaultColumns: Array<IColumnProps> = [
        { slot: "selection", minWidth: 36, headerAlign: "center", align: "center", reserveSelection: true },
        {
            label: "日期",
            prop: "cd_date",
            headerAlign: "left",
            align: "left",
            minWidth: 80,
            formatter(_row, _col, val: string) {
                return formatDate(val);
            },
        },
        {
            label: "摘要",
            prop: "description",
            headerAlign: "left",
            align: "left",
            minWidth: 100,
        },
        {
            label: "收支类别",
            prop: "ie_type_name",
            headerAlign: "left",
            align: "left",
            minWidth: 100,
        },
        {
            label: "往来单位",
            prop: "opposite_party",
            headerAlign: "left",
            align: "left",
            minWidth: 100,
        },
        {
            label: "收入（借方）",
            prop: "income",
            headerAlign: "right",
            align: "right",
            minWidth: 100,
            formatter(_w, _c, val) {
                return formatMoney(val);
            },
        },
        {
            label: "支出（贷方）",
            prop: "expenditure",
            headerAlign: "right",
            align: "right",
            minWidth: 100,
            formatter(_w, _c, val) {
                return formatMoney(val);
            },
        },
        {
            label: "余额",
            prop: "amount",
            headerAlign: "right",
            align: "right",
            minWidth: 100,
            formatter(_w, _c, val) {
                return formatMoney(val);
            },
            resizable: false,
        },
    ];
    if (columns.value.find((column) => column.slot === "incomeFc")) {
        for (let i = 5; i < defaultColumns.length; i++) {
            defaultColumns[i].children = moreFcColumns.map((_c, index) => {
                const column = {
                    ...defaultColumns[i],
                    label: _c.label,
                    prop: defaultColumns[i].prop + _c.prop,
                    resizable: i !== defaultColumns.length - 1 || index !== moreFcColumns.length - 1,
                };
                if (_c.prop === "_rate") {
                    column.formatter = (_w, _c, val) => {
                        return formatClipPointNum(val);
                    };
                }
                return column;
            });
            defaultColumns[i].align = "center";
            defaultColumns[i].headerAlign = "center";
        }
    }
    if (columns.value.find((column) => column.prop === "fc_name")) {
        const column: IColumnProps = {
            label: "币别",
            prop: "fc_name",
            headerAlign: "left",
            align: "left",
            minWidth: 60,
        };
        defaultColumns.splice(2, 0, column);
    }
    dialogColumns.value = defaultColumns;
    diffPageSelectedRef.value?.handleOpen();
}
const selectAllStatus = ref(false);
const selections = ref<Array<ITableItem>>([]);
function checkIsSameJournal(row: ITableItem, _row: ITableItem) {
    const row_cd_date = dayjs(row.cd_date).format("YYYY-MM-DD");
    const _row_cd_date = dayjs(_row.cd_date).format("YYYY-MM-DD");
    return row.cd_account === _row.cd_account && row_cd_date === _row_cd_date && row.line_sn === _row.line_sn;
}
const maxlength = 600;
function handleSelectAllChange(check: boolean) {
    for (let i = 0; i < props.tableData.length; i++) {
        const item = props.tableData[i];
        if (!selectable(item)) continue;
        item[selectKey] = !!check;
        const selectRowIndex = selections.value.findIndex((row) => checkIsSameJournal(row, item));
        if (check && selectRowIndex === -1) {
            if (selections.value.length >= maxlength) {
                ElNotify({ type: "warning", message: `最多支持${maxlength}条哦！` });
                item[selectKey] = false;
                selectAllStatus.value = false;
                emit("selection-change", selections.value);
                return;
            }
            selections.value.push(item);
        } else if (!check && selectRowIndex > -1) {
            selections.value.splice(selectRowIndex, 1);
        }
    }
    emit("selection-change", selections.value);
}
function handleSelectChange(row: ITableItem, select: boolean) {
    row[selectKey] = select;
    const index = selections.value.findIndex((item) => checkIsSameJournal(item, row));
    if (select && index === -1) {
        if (selections.value.length >= maxlength) {
            ElNotify({ type: "warning", message: `最多支持${maxlength}条哦！` });
            row[selectKey] = false;
            return;
        }
        selections.value.push(row);
    } else if (!select && index > -1) {
        selections.value.splice(index, 1);
    }
    selectAllStatus.value = props.tableData.every((item) => !selectable(item) || !!item[selectKey]);
    emit("selection-change", selections.value);
}
function handleClearSelection() {
    selections.value.forEach((row) => {
        row[selectKey] = false;
    });
    props.tableData.forEach((row) => {
        if (selectable(row)) row[selectKey] = false;
    });
    selections.value.length = 0;
    selectAllStatus.value = false;
    emit("selection-change", selections.value);
}
const indeterminate = computed(() => {
    return !!selections.value.length && props.tableData.some((row) => row[selectKey] === false);
});

// 单元格操作
function handleIETypeSave(type: CashIEType, no: string, name: string) {
    emit("save-ietype", type, no, name);
}
function handleOpenUnitDialog() {
    oppositePartyRef.value?.blur();
    const timer = setTimeout(() => {
        isEdit.value = true;
        clearTimeout(timer);
    }, 100);
    dialogCellOperateRef.value?.openAADialog(CashAAType.Unit);
}
function changeCellUnitInfo(row: IUnitUpdateParams | null) {
    const replaceData =
        row === null
            ? { OPPOSITE_PARTY: "", OPPOSITE_PARTY_INT: "" }
            : { OPPOSITE_PARTY: row.opposite_party, OPPOSITE_PARTY_INT: row.opposite_party_int.toString()};
    if (!rowItemSearchInfo.DEPARTMENT && isErp.value) {
        rowItemSearchInfo.DEPARTMENT = row?.departmentId || "";
    }
    changeEditInfo(replaceData);
    inputMatchIEType(row, "oppositeParty");
    handleOpTypeEnter();
}
function handleUnitFocus() {
    handleFocus("oppositePartyRef");
    if (isErp.value) {
        handleOpenUnitDialog();
        oppositePartyRef.value?.blur();
    }
}
function handleNewAAE(aaType: CashAAType, payMethodId = "", payMethodName = "") {
    const timer = setTimeout(() => {
        isEdit.value = true;
        clearTimeout(timer);
    }, 100);
    dialogCellOperateRef.value?.openAADialog(aaType, payMethodId, payMethodName);
}
function handleSaveAAE(aaType: CashAAType.Department | CashAAType.Project, code: string, name: string) {
    aaType === CashAAType.Project ? emit("save-project", code, name) : emit("save-department", code, name);
}
function hasPermissionDepartment() {
    return isErp.value ? checkPermission(["Department-编辑"]) : checkPermission(["assistingaccount-canedit"]);
}
function handleSavePayMethod(name: string) {
    emit("save-pay-method", name);
}

const canEditPaymentPaddingRight = 45;
let paymentCanDelete = true;
function handleDeletePayMethod(id: string, name: string) {
    if (!paymentCanDelete) return;
    paymentCanDelete = false;
    ElConfirm(`确定删除${name}吗`).then((r: boolean) => {
        if (r) {
            const url = props.journalType === "1010" ? "/api/Payment/Cash" : "/api/Payment/Deposit";
            request({ url, method: "delete", params: { payment_id: id } })
                .then((res: IResponseModel<boolean>) => {
                    paymentCanDelete = true;
                    if (res.state !== 1000 || !res.data) {
                        ElNotify({ type: "warning", message: res.msg || "结算方式删除失败" });
                        return;
                    }
                    ElNotify({ type: "success", message: "亲，删除成功啦！" });
                    emit("save-pay-method", "");
                })
                .catch(() => {
                    paymentCanDelete = true;
                });
        } else {
            paymentCanDelete = true;
        }
    });
}

const currentEditIndex = ref(-1);
function rowClassName({ row, rowIndex }: { row: ITableItem; rowIndex: number }) {
    const jDate = formatDate(row.cd_date);
    const isRowMove = props.cdAccount !== "all" && (jDate >= props.checkDate);
    const isInitRow = row.description === "初始化余额" && row.ie_type === "" && row.cd_date === "";
    if (isInitRow) return "init-row";
    if (currentEditIndex.value === rowIndex) return row[selectKey] ? "journal-row-selected edit-row" : "edit-row";
    if (row[selectKey]) return ("journal-row-selected" + (isRowMove ? " row-move" : ""));
    if (row.cd_date !== "" && isRowMove) return "row-move";
    return "";
}
const rowItemSearchInfo = reactive<IRowItemEditInfo>({
    CD_DATE: "", // 日期
    DESCRIPTION: "", // 摘要
    IE_TYPE: "", // 收支类型
    OPPOSITE_PARTY: "", // 往来单位
    PROJECT: "", // 项目
    DEPARTMENT: "", // 部门
    INCOME: "", // 收入
    INCOME_RATE: "", // 收入汇率
    INCOME_STANDARD: "", // 收入本位币
    EXPENDITURE: "", // 支出
    EXPENDITURE_RATE: "", // 支出汇率
    EXPENDITURE_STANDARD: "", // 支出本位币
    PAYMENT_METHOD: "", // 结算方式
    RECEIPT_NO: "", // 票据号
    NOTE: "", // 备注
    AAE_ID: "", // 操作员
    ACCOUNT: "", // 账户
    ACCOUNT_NAME: "", // 账户名称
    ACCOUNT_NO: "", // 账户编号
    OPPOSITE_PARTY_NO: "", //往来单位账号
    OPPOSITE_PARTY_USCC: "",
    OPPOSITE_PARTY_INT: "",
    IE_TYPE_NAME: "", // 收支类型名称
    OPPOSITE_PARTY_BANK: "", //往来单位开户行
});
function resetRowInfo() {
    currentEditIndex.value = -1;
    rowItemSearchInfo.CD_DATE = "";
    rowItemSearchInfo.DESCRIPTION = "";
    rowItemSearchInfo.IE_TYPE = "";
    rowItemSearchInfo.OPPOSITE_PARTY = "";
    rowItemSearchInfo.PROJECT = "";
    rowItemSearchInfo.DEPARTMENT = "";
    rowItemSearchInfo.INCOME = "";
    rowItemSearchInfo.EXPENDITURE = "";
    rowItemSearchInfo.PAYMENT_METHOD = "";
    rowItemSearchInfo.RECEIPT_NO = "";
    rowItemSearchInfo.NOTE = "";
    rowItemSearchInfo.AAE_ID = "";
    rowItemSearchInfo.ACCOUNT = "";
    rowItemSearchInfo.ACCOUNT_NAME = "";
    rowItemSearchInfo.ACCOUNT_NO = "";
    rowItemSearchInfo.OPPOSITE_PARTY_NO = "";
    rowItemSearchInfo.OPPOSITE_PARTY_USCC = "";
    rowItemSearchInfo.INCOME_RATE = "";
    rowItemSearchInfo.INCOME_STANDARD = "";
    rowItemSearchInfo.EXPENDITURE_RATE = "";
    rowItemSearchInfo.EXPENDITURE_STANDARD = "";
    rowItemSearchInfo.IE_TYPE_NAME = "";
    rowItemSearchInfo.OPPOSITE_PARTY_BANK = "";
    needWaiting = false;
    useLastRow = false;
    allowBlur = true;
    isEdit.value = false;
}
function hasChange() {
    if (currentEditIndex.value === -1) return false;
    const item = props.tableData[currentEditIndex.value];
    if (item?.defaultClickItem) {
        if (
            rowItemSearchInfo.DESCRIPTION.trim() !== "" ||
            rowItemSearchInfo.IE_TYPE !== "" ||
            rowItemSearchInfo.PROJECT !== "" ||
            rowItemSearchInfo.DEPARTMENT !== "" ||
            rowItemSearchInfo.PAYMENT_METHOD !== "" ||
            rowItemSearchInfo.OPPOSITE_PARTY !== "" ||
            rowItemSearchInfo.INCOME.trim() !== "" ||
            rowItemSearchInfo.INCOME_STANDARD.trim() !== "" ||
            rowItemSearchInfo.EXPENDITURE.trim() !== "" ||
            rowItemSearchInfo.EXPENDITURE_STANDARD.trim() !== "" ||
            rowItemSearchInfo.RECEIPT_NO !== ""
        ) {
            return true;
        }
        return false;
    } else {
        return true;
    }
}

// 初始化金额
const amountValue = ref("");
const amountRateValue = ref("");
const amountStandardValue = ref("");
function handleBeginAmountInput(val: string) {
    const amount = Number(val);
    const amountRate = Number(amountRateValue.value);
    if (!Number.isNaN(amount) && amount !== 0 && !Number.isNaN(amountRate) && amountRate !== 0) {
        amountStandardValue.value = (amount * amountRate).toFixed(2).toString();
    } else {
        amountStandardValue.value = "";
    }
}
function handleBeginAmountRateInput(val: string) {
    const amount = Number(amountValue.value);
    const amountRate = Number(val);
    if (!Number.isNaN(amount) && amount !== 0 && !Number.isNaN(amountRate) && amountRate !== 0) {
        amountStandardValue.value = (amount * amountRate).toFixed(2).toString();
    } else {
        amountStandardValue.value = "";
    }
}
function handleBeginAmountStandardInput(val: string) {
    const amountStandard = Number(val);
    const rate = Number(amountRateValue.value);
    //期初不动汇率，调整原币
    if (!Number.isNaN(amountStandard) && amountStandard !== 0 && !Number.isNaN(rate) && rate !== 0) {
        amountValue.value = (amountStandard / rate).toFixed(2).toString();
    } else {
        amountValue.value = "0.00";
    }
}
function handleBeginBlur() {
    if (props.cdAccount.indexOf(",") != -1) {
        ElNotify({ type: "warning", message: "保存失败，多账户显示时不能修改账户初始化余额哦~" });
        setAmountFromTable(props.tableData);
        return;
    }
    const params = {
        cdAccount: props.cdAccount,
        amount: amountValue.value || "0",
        amountStandard: amountStandardValue.value || "0",
        fcRate: amountRateValue.value || "0",
    };
    const urlPath = props.journalType === "1020" ? "SaveDepositBegin" : "SaveCashBegin";
    request({ url: "/api/Journal/" + urlPath + "?" + getUrlSearchParams(params), method: "post" })
        .then((res: any) => {
            if (res.state !== 1000) return;
            if (res.data === "Success") {
                ElNotify({ type: "success", message: "期初余额保存成功" });
                emit("load-data");
            } else {
                ElNotify({ type: "warning", message: "期初余额保存失败，请刷新页面重试" });
            }
            resetRowInfo();
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "期初余额保存失败，请刷新页面重试" });
            resetRowInfo();
        });
}
function setAmountFromTable(tableValue: ITableItem[]) {
    const beginItem = tableValue.find((item) => item.description === "初始化余额");
    if (beginItem) {
        amountValue.value = beginItem.amount;
        amountRateValue.value = formatRate(beginItem.amount_rate);
        amountStandardValue.value = beginItem?.amount_standard;
    } else {
        amountValue.value = "";
        amountRateValue.value = "";
        amountStandardValue.value = "";
    }
}

//流水拖动
const rowMouse = ref();
let longTimer: any = null;
function cellMouseEnter(row: any, column: any, cell: any, event: any) {
    if (row.cd_date && (formatDate(row.cd_date) < props.checkDate)) {
        rowMouse.value = row;
    }
}
function cellMouseLeve() {
    rowMouse.value = null;
}
function handleLongTip() {
    if (formatDate(rowMouse.value.cd_date) < props.checkDate) {
        ElNotify({ 
            type: "warning", 
            message: "当前流水所属期间已结账，不可以进行排序调整哦", 
        });
    }
}
function mouseDownAll() {
    if (!rowMouse.value || rowMouse.value.cd_date === "") return;
    longTimer = setTimeout(() => {
        handleLongTip();
      }, 400);
    }
function mouseUpAll() {
    clearTimeout(longTimer);
    longTimer = null;
}
function moveDrop(oldIndex: number, newIndex: number, scrollTop: number, evt: any, originScrollTop: number) {
    nextTick(() => {
        emit("move-drop", oldIndex, newIndex, scrollTop, evt, originScrollTop);
    });
}
const setScrollTop = (val: number) => {
    tableRef.value?.setScrollTop(val);
};
function watchRowDrop() {
    nextTick(() => {
        tableRef.value?.rowDrop(tableRef.value?.$el);
    });
}
watch(
    () => props.tableData,
    (val) => {
        setAmountFromTable(val);
        checkCurrentSelectStatus();
        updateSelections();
        watchRowDrop();
    }
);

// 原币/本位币联动
const _ = getGlobalLodash();
function calcStandard(val: string, rate: string, defaultValue: string) {
    const valNumber = Number(val);
    const rateNumber = Number(rate);
    if (!Number.isNaN(valNumber) && valNumber !== 0 && !Number.isNaN(rateNumber) && rateNumber !== 0) {
        // return (valNumber * rateNumber).toFixed(2).toString();
        return _.round(valNumber * rateNumber, 2).toString();
    } else {
        return defaultValue;
    }
}
function setIncomeStandard(income: string, incomeRate: string, defaultValue: string) {
    rowItemSearchInfo.INCOME_STANDARD = calcStandard(income, incomeRate, defaultValue);
}
function setExpenditureStandard(expenditure: string, expenditureRate: string, defaultValue: string) {
    rowItemSearchInfo.EXPENDITURE_STANDARD = calcStandard(expenditure, expenditureRate, defaultValue);
}
function changeInputValue(value: string) {
    const maxInteger = 9;
    const maxDigits = 2;
    const regex = new RegExp(`^-?\\d{0,${maxInteger}}(\\.\\d{0,${maxDigits}})?$`);
    if (!regex.test(value)) {
        const numberList = value.split(".");
        let integer = numberList[0];
        let decimal = numberList[1];
        integer = integer.startsWith("-") ? integer.slice(0, maxInteger + 1) : integer.slice(0, maxInteger);
        decimal = decimal?.slice(0, maxDigits);
        value = decimal ? `${integer}.${decimal}` : integer;
    }
    return value;
}
function handleIncomeInput(val: string) {
    setIncomeStandard(changeInputValue(val), rowItemSearchInfo.INCOME_RATE, "0.00");
}
function handleIncomeChange(val: string) {
    rowItemSearchInfo.INCOME = val;
    if (!props.showAllList) setIncomeStandard(val, rowItemSearchInfo.INCOME_RATE, "0.00");
}
function handleIncomeRateInput(val: string) {
    setIncomeStandard(rowItemSearchInfo.INCOME, val, "0.00");
}
// 是否正在通过本位币计算原币
const useStandardCalcAmount = ref(false);
function handleStandardAmountInput(val: string, type: "income" | "expendture") {
    //输入本位币，若存在原币，则重新计算汇率
    //若原币不存在，且汇率存在，则反算原币
    const amountStandard = Number(val);
    const amount = Number(type === "income" ? rowItemSearchInfo.INCOME : rowItemSearchInfo.EXPENDITURE);
    const amountRate = Number(type === "income" ? rowItemSearchInfo.INCOME_RATE : rowItemSearchInfo.EXPENDITURE_RATE);
    if (!Number.isNaN(amountStandard) && amountStandard !== 0) {
        if (!useStandardCalcAmount.value && !Number.isNaN(amount) && amount !== 0) {
            type === "income"
                ? (rowItemSearchInfo.INCOME_RATE = formatRate(amountStandard / amount))
                : (rowItemSearchInfo.EXPENDITURE_RATE = formatRate(amountStandard / amount));
        } else if (!Number.isNaN(amountRate) && amountRate !== 0) {
            useStandardCalcAmount.value = true;
            type === "income"
                ? (rowItemSearchInfo.INCOME = (amountStandard / amountRate).toFixed(2).toString())
                : (rowItemSearchInfo.EXPENDITURE = (amountStandard / amountRate).toFixed(2).toString());
        }
    } else {
        if (!useStandardCalcAmount.value && !Number.isNaN(amount) && amount !== 0) {
            type === "income" ? (rowItemSearchInfo.INCOME_RATE = "0") : (rowItemSearchInfo.EXPENDITURE_RATE = "0");
        } else if (!Number.isNaN(amountRate) && amountRate !== 0) {
            useStandardCalcAmount.value = true;
            type === "income" ? (rowItemSearchInfo.INCOME = "0.00") : (rowItemSearchInfo.EXPENDITURE = "0.00");
        }
    }
}
function handleIncomeStandardInput(val: string) {
    handleStandardAmountInput(val, "income");
}
function handleExpenditureInput(val: string) {
    const newVal = changeInputValue(val);
    setExpenditureStandard(newVal, rowItemSearchInfo.EXPENDITURE_RATE, "0.00");
}
function handleExpentitureChange(val: string) {
    rowItemSearchInfo.EXPENDITURE = val;
    if (!props.showAllList) setExpenditureStandard(val, rowItemSearchInfo.EXPENDITURE_RATE, "0.00");
}
function handleExpenditureRateInput(val: string) {
    setExpenditureStandard(rowItemSearchInfo.EXPENDITURE, val, "0.00");
}
function handleExpenditureStandardInput(val: string) {
    handleStandardAmountInput(val, "expendture");
}
watch(
    () => rowItemSearchInfo.ACCOUNT,
    (account, oldAccount) => {
        // 当前行的账户变动联动效果只有在显示全部时需要
        // 日记账的每一行进入编辑状态时已经将本行的相关信息赋值，无需再次联动
        // 只有当账户变动时，才需要对金额做出联动调整
        if (props.cdAccount === "all" && account && account !== "" && oldAccount !== "") {
            const item = props.cdAccountList.find((item) => item.ac_id === account) as ICDAccountItem;
            rowItemSearchInfo.ACCOUNT_NAME = item.ac_no + " - " + item.ac_name;
            rowItemSearchInfo.ACCOUNT_NO = item.ac_no;
            rowItemSearchInfo.INCOME_RATE = item.fc_rate;
            setIncomeStandard(rowItemSearchInfo.INCOME, item.fc_rate, "");
            rowItemSearchInfo.EXPENDITURE_RATE = item.fc_rate;
            setExpenditureStandard(rowItemSearchInfo.EXPENDITURE, item.fc_rate, "");
            if (currentEditIndex.value !== -1) {
                const rowItem = props.tableData.find((item) => item.index === currentEditIndex.value) as ITableItem;
                if (rowItem) rowItem.fc_name = item.currency_name;
            }
        }
    }
);

function handleDateChange(date: string) {
    let maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() + 5);
    const value = new Date(date);
    const item = props.tableData.find((item) => item.index === currentEditIndex.value) as ITableItem;
    if (date === item.originalDate) return;
    if (value < new Date(props.startDate)) {
        ElNotify({ type: "warning", message: "不能选择账套启用前的期间" });
        rowItemSearchInfo.CD_DATE = getshortdata(item.cd_date);
        return;
    } else if (value < new Date(props.checkDate)) {
        ElNotify({ type: "warning", message: "不能选择已结账的日期" });
        rowItemSearchInfo.CD_DATE = getshortdata(item.cd_date);
        return false;
    } else if (value > new Date(maxDate)) {
        ElNotify({ type: "warning", message: "最晚只能录入当前日期五年内的数据" });
        rowItemSearchInfo.CD_DATE = getshortdata(item.cd_date);
        return false;
    } else {
        if (value < new Date(GetStart(getshortdata(item.cd_date))) || value > new Date(GetEnd(getshortdata(item.cd_date)))) {
            ElConfirm("您录入的日期是" + date + "，系统将跳转到该日期的归属期间，请问是否确定？").then((r: boolean) => {
                if (r) {
                    if (!item.originalDate) item.originalDate = getshortdata(item.cd_date);
                    changeDate(date, item);
                } else {
                    rowItemSearchInfo.CD_DATE = getshortdata(item.cd_date);
                    return false;
                }
            });
        } else {
            nextTick().then(() => {
                changeLineSnName(props.cdAccount, date, item);
                descriptionInputRef.value?.focus();
            });
        }
    }
}
function changeLineSnName(cdAccount: string, date_e: string, item: ITableItem) {
    request({ url: "/api/Journal/GetNumber?cdAccount=" + cdAccount + "&date_e=" + date_e, method: "post" }).then(
        (res: IResponseModel<number>) => {
            if (res.state !== 1000) return;
            const oldLineSnName = item.line_sn_name;
            const lineSn = res.data;
            item.line_sn = item.ie_type && item.description ? item.line_sn : lineSn + "";
            const thisDate = date_e.split("-").join("");
            if (!item.ie_type && !item.description) {
                item.cd_date = date_e;
                const oldLineSnNameArray = oldLineSnName.split("-");
                const oldJType = oldLineSnNameArray[0];
                const oldNo = oldLineSnNameArray[1];
                item.line_sn_name = oldJType + "-" + oldNo + "-" + thisDate + "-" + item.line_sn;
            } else {
                const reallyOldLineSnName = oldLineSnName.replace(/<[^<>]+>/g, "");
                const oldLineSnNameArray = reallyOldLineSnName.split("-");
                const oldJType = oldLineSnNameArray[0];
                const oldNo = oldLineSnNameArray[1];
                const thisReallyLineSnName = oldJType + "-" + oldNo + "-" + thisDate + "-" + item.line_sn;
                item.line_sn_name = item.line_sn_name.replace(reallyOldLineSnName, thisReallyLineSnName);

                const regexLineSn = /&LINE_SN=(\d+)/g;
                item.line_sn_name = item.line_sn_name.replace(regexLineSn, `&LINE_SN=${item.line_sn}`);
                const regexCDDate = /&CD_DATE=([^&]+)/g;
                const date = date_e.split("-").join("/") + " 0:00:00";
                item.line_sn_name = item.line_sn_name.replace(regexCDDate, `&CD_DATE=${date}`);
            }
            changeAfterLineSnNameAndLineSn(oldLineSnName, item.index, item.line_sn_name);
        }
    );
}
function changeAfterLineSnNameAndLineSn(oldLineSnName: string, editIndex: number, newLineSnName: string) {
    const [oldPerfix] = getLineSnAndPerfix(oldLineSnName);
    const [newPerfix] = getLineSnAndPerfix(newLineSnName);

    for (let i = editIndex + 1; i < props.tableData.length - 1; i++) {
        const item = props.tableData[i];
        const [perfix, lineSn] = getLineSnAndPerfix(item.line_sn_name);
        if (perfix === oldPerfix) {
            const newLineSn = ~~lineSn - 1;
            if (item.line_sn_name.startsWith("<a")) {
                item.line_sn_name = getLineSnNameWithAnchorTag(
                    perfix + "-" + newLineSn,
                    item.cd_account,
                    item.cd_date,
                    newLineSn,
                    ~~item.expenditure === 0
                );
                item.line_sn = (~~item.line_sn - 1).toString();
            } else {
                item.line_sn_name = perfix + "-" + newLineSn;
                item.line_sn = (~~item.line_sn - 1).toString();
            }
        } else if (perfix === newPerfix) {
            if (!item.line_sn_name.startsWith("<a")) {
                const newLineSn = ~~lineSn + 1;
                item.line_sn_name = perfix + "-" + newLineSn;
                item.line_sn = (~~item.line_sn + 1).toString();
            }
        } else {
            continue;
        }
    }
}
function changeDate(date: string, row: ITableItem) {
    const time = date.split("-"); // YYYY-MM-DD
    const _s = time[0] + "-" + time[1] + "-01";
    const _e = date;
    resetRowInfo();
    emit("change-row-date", _s, _e, row);
}

let needWaiting = false;
let allowBlur = true;
function handleProjectVisibleChange() {
    if (!rowItemSearchInfo.PROJECT) return;
    const disabledProject = props.disabledProjectList.find((item) => item.aaeid.toString() === rowItemSearchInfo.PROJECT);
    const input = projectRef.value?.getSelect()?.$el?.querySelector("input") as HTMLInputElement;
    if (disabledProject && input) {
        nextTick().then(() => {
            input.value = disabledProject.aaname;
        });
    }
}
const DialogForHasBillRef = ref<InstanceType<typeof DialogForHasBill>>();
function handleRowClick(row: ITableItem, column?: any) {
    if (!checkJournalPermission("canedit")) {
        currentEditIndex.value = -1;
        resetRowInfo();
        return;
    }
    if (row.ie_type == "-1") return;
    if (row.cd_date === "") return;
    if (row.erp_offset === "1" && !isErp.value) {
        // 会与保存成功的提示同时触发，保存成功的提示优先
        if (!hasChange()) ElNotify({ type: "warning", message: "资金数据已经关联单据，请先取消关联！" });
        return;
    }
    const columnLabel: string = column === "amount" ? "amount" : column === "description" ? "摘要" : column?.label || "";
    if (columnLabel === "操作" || (column && column.type) === "selection") return;
    if (row.index === currentEditIndex.value) return;
    if (row.index !== currentEditIndex.value) {
        if (hasChange()) return;
        useLastRow = false;
        needWaiting = false;
    }
    if (needWaiting) return;
    needWaiting = true;
    dateBillPopperShow.value = false;
    if (formatDate(row.cd_date) && formatDate(row.cd_date) < props.checkDate) return;
    if (row.erp_offset === "1" && isErp.value) {
        dateBillPopperShow.value = true;
        if (localStorage.getItem("hiddenBillDialogJournal-" + getGlobalToken()) === "true") {
            setRowInEditBillStatus(row, columnLabel);
            return;
        }
        DialogForHasBillRef.value?.showDialog(() => setRowInEditBillStatus(row, columnLabel));
        return;
    }
    if (row.v_num === "" || row.v_num === undefined) {
        setRowInEditStatus(row, columnLabel);
        return;
    }
    if (!checkJournalPermission("cancreatevoucher") && !isErp.value) {
        ElNotify({ type: "warning", message: "修改已生成凭证的日记账数据，需要删除凭证或拥有凭证编辑权限哦~" });
        return;
    }
    if (localStorage.getItem("hiddenFixedDialog4Journal-" + getGlobalToken()) === "true") {
        setRowInEditStatus(row, columnLabel);
        return;
    }
    dialogForHasVoucherRef.value?.showDialog(() => setRowInEditStatus(row));
}
let useLastRow = false;
function setUseLastRow(val: boolean) {
    useLastRow = val;
}
function setRowInEditStatus(row: ITableItem, columnLabel?: string) {
    if (useLastRow) return;
    // 进入编辑状态前需要先重置编辑信息（父级组件可能并未提交，仅切换当前编辑行时并未对编辑行信息重置）
    resetRowInfo();
    setRowInEditContent(row);
    nextTick(() => {
        isEdit.value = true;
        if (columnLabel === "摘要") {
            descriptionInputRef.value?.focus();
        } else if (columnLabel === "收支类别") {
            ietypeSelectRef.value?.focus();
        } else if (columnLabel === "往来单位") {
            isErp.value ? handleOpenUnitDialog() : oppositePartyRef.value?.focus();
        } else if (columnLabel === "项目") {
            projectRef.value?.focus();
        } else if (columnLabel === "部门") {
            departmentRef.value?.focus();
        } else if (columnLabel === "收入（借方）") {
            incomeInputRef.value?.focus();
        } else if (columnLabel === "支出（贷方）") {
            expenditureInputRef.value?.focus();
        } else if (columnLabel === "结算方式") {
            payMethodRef.value?.focus();
        } else if (columnLabel === "票据号") {
            receiptNoRef.value?.focus();
        } else if (columnLabel === "备注") {
            noteRef.value?.focus();
        } else if (columnLabel === "往来单位账号") {
            oppsitePartyNoRef.value?.focus();
        } else if (columnLabel === "往来单位开户行") {
            oppsitePartyBankRef.value?.focus();
        } else if (columnLabel === "amount") {
            rowItemSearchInfo.EXPENDITURE !== "" ? expenditureInputRef.value?.focus() : incomeInputRef.value?.focus();
        } else {
            incomeInputRef.value?.focus();
        }
        props.showAllList && handleProjectVisibleChange();
    });
}
function setRowInEditContent(row: ITableItem) {
    currentEditIndex.value = row.index;
    setRowInEditContentDetail(row);
}
function setRowInEditContentDetail(row: ITableItem) {
    rowItemSearchInfo.CD_DATE = formatDate(row.cd_date);
    rowItemSearchInfo.DESCRIPTION = row.description;
    rowItemSearchInfo.IE_TYPE = row.ie_type;
    rowItemSearchInfo.AAE_ID = row.opposite_party_int;
    rowItemSearchInfo.OPPOSITE_PARTY = row.opposite_party;
    rowItemSearchInfo.PROJECT = row.project + "";
    rowItemSearchInfo.DEPARTMENT = row.department + "";
    rowItemSearchInfo.INCOME = getStringAmount(row.income, "amount");
    rowItemSearchInfo.INCOME_RATE = getStringAmount(row.income_rate, "rate");
    rowItemSearchInfo.INCOME_STANDARD = getStringAmount(row.income_standard, "amount");
    rowItemSearchInfo.EXPENDITURE = getStringAmount(row.expenditure, "amount");
    rowItemSearchInfo.EXPENDITURE_RATE = getStringAmount(row.expenditure_rate, "rate");
    rowItemSearchInfo.EXPENDITURE_STANDARD = getStringAmount(row.expenditure_standard, "amount");
    rowItemSearchInfo.PAYMENT_METHOD = row.payment_method == "0" ? "" : row.payment_method;
    rowItemSearchInfo.RECEIPT_NO = row.receipt_no;
    rowItemSearchInfo.NOTE = row.note;
    rowItemSearchInfo.ACCOUNT = row.cd_account;
    const item = props.cdAccountList.find((item) => item.ac_id === row.cd_account) as ICDAccountItem;
    rowItemSearchInfo.ACCOUNT_NAME = item.ac_no + " - " + item.ac_name;
    rowItemSearchInfo.ACCOUNT_NO = row.ac_no;
    rowItemSearchInfo.OPPOSITE_PARTY_NO = row.opposite_party_no;
    rowItemSearchInfo.OPPOSITE_PARTY_USCC = row.opposite_party_uscc;
    rowItemSearchInfo.OPPOSITE_PARTY_INT = row.opposite_party_int;
    rowItemSearchInfo.IE_TYPE_NAME = row.ie_type_name;
    rowItemSearchInfo.OPPOSITE_PARTY_BANK = row.opposite_party_bank;
    return rowItemSearchInfo;
}
function setRowInEditBillStatus(row: ITableItem, columnLabel: string) {
    if (useLastRow) return;
    let billListLabel = ["部门", "项目", "往来单位账号", "结算方式", "票据号", "备注"];
    let columnsfilter = columns.value.slice(1, columns.value.length - 1);
    let billListLabelIndex: number[] = billListLabel.map(
        (item) => columnsfilter.findIndex((column) => column.label === item)
    );
    let hasIndex = billListLabelIndex.some((item) => item > -1);
    if (!hasIndex) return;
    // 进入编辑状态前需要先重置编辑信息（父级组件可能并未提交，仅切换当前编辑行时并未对编辑行信息重置）
    resetRowInfo();
    setRowInEditContent(row);
    nextTick(() => {
        isEdit.value = true;
        if (hasIndex) {
            const inputRefs:{
                [key: string]: any;
            } = {  
                "部门": departmentRef,  
                "项目": projectRef,  
                "往来单位账号": oppsitePartyNoRef,  
                "结算方式": payMethodRef,  
                "票据号": receiptNoRef,  
                "备注": noteRef  
            };
            if (columnsfilter.findIndex((item) => item.label === "备注") > -1) {
                inputRefs[columnLabel] ? inputRefs[columnLabel]?.value?.focus() : noteRef.value?.focus(); 
            } else {
                let firstIndex = -1;  
                for (let i = 0; i < billListLabelIndex.length; i++) {  
                    if (billListLabelIndex[i] > -1 && (firstIndex === -1 || billListLabelIndex[i] < billListLabelIndex[firstIndex])) {  
                        firstIndex = i;  
                    }  
                }   
                const firstLabel = billListLabel[firstIndex];  
                inputRefs[columnLabel] ? inputRefs[columnLabel]?.value?.focus() : inputRefs[firstLabel]?.value?.focus();
            }
            props.showAllList && handleProjectVisibleChange();
        }
    });
}
function getStringAmount(value: string, type: "amount" | "rate") {
    const amount = Number(value);
    if (Number.isNaN(amount) || amount === 0) return "";
    return type === "amount" ? formatMoney(value, false) : value.toString();
}

// 表格相关操作
function handleInEditStatus() {
    isEdit.value = true;
    tableRef.value?.$el.click();
    ietypeSelectRef.value?.blur();
    projectRef.value?.blur();
    departmentRef.value?.blur();
    payMethodRef.value?.blur();
}
const currentNeedSelectInput = ref("");
function handleFocus(nameRef?: string) {
    textareaBottom(tableRef);
    switch (nameRef) {
        case "descriptionInputRef1":
            descriptionTextareaShow.value = true;
            break;
        case "noteRef3":
            noteTextareaShow.value = true;
            break;
        case "oppositePartyRef":
            !isErp.value && (oppositePartyTextareaShow.value = true);
            break;
    }
    nextTick(() => {
        nameRef && getTextareaFocus(nameRef);
    });
    currentNeedSelectInput.value = nameRef || "";
    const timer = setTimeout(() => {
        isEdit.value = true;
        if (nameRef === "descriptionInputRef" && "descriptionInputRef" === currentNeedSelectInput.value) {
            descriptionInputRef.value?.select();
        } else if (nameRef === "oppositePartyRef" && "oppositePartyRef" === currentNeedSelectInput.value && !isErp.value) {
            oppositePartyRef.value.inputRef?.select();
        } else if (nameRef === "oppsitePartyNoRef" && "oppsitePartyNoRef" === currentNeedSelectInput.value) {
            oppsitePartyNoRef.value?.select();
        } else if (nameRef === "oppsitePartyBankRef" && "oppsitePartyBankRef" === currentNeedSelectInput.value) {
            oppsitePartyBankRef.value?.select();
        } else if (nameRef === "receiptNoRef" && "receiptNoRef" === currentNeedSelectInput.value) {
            receiptNoRef.value?.select();
        } else if (nameRef === "noteRef" && "noteRef" === currentNeedSelectInput.value) {
            noteRef.value?.select();
        }
        clearTimeout(timer);
    }, 50);
}
function getTextareaFocus(nameRef: string) {
    switch (nameRef) {
        case "descriptionInputRef1":
            descriptionInputRef.value?.focus();
            break;
        case "noteRef3":
            noteRef.value?.focus();
            break;
        case "oppositePartyRef":
            !isErp.value && oppositePartyRef?.value?.focus();
            break;
    }
}
const datePopperShow = ref(false);
const dateBillPopperShow = ref(false);
function visibleChange(val: boolean) {
    datePopperShow.value = val;
}
function handleDateFocus() {
    currentNeedSelectInput.value = "";
    nextTick().then(() => {
        if (datePopperShow.value || dateBillPopperShow.value) {
            elDatePickerRef.value?.handleClose();
        } else {
            elDatePickerRef.value?.handleOpen();
        }
    });
    const timer = setTimeout(() => {
        isEdit.value = true;
        clearTimeout(timer);
    }, 50);
}
function inputBlurToSubmit(row: any, handle: () => void) {
    isEdit.value = false;
    useStandardCalcAmount.value = false;
    handle && handle();
    const timer = setTimeout(() => {
        clearTimeout(timer);
        if (!allowBlur) return;
        if (isEdit.value) return;
        needWaiting = false;
        handleSubmit(row);
    }, 250);
}
function handleBlur(row: any, column?: string) {
    switch (column) {
        case "description":
            descriptionTextareaShow.value = false;
            break;
        case "note":
            noteTextareaShow.value = false;
            break;
        case "oppositeParty":
            oppositePartyTextareaShow.value = false;
            break;
    }
    inputBlurToSubmit(row, () => {
        inputMatchIEType(row, column);
    });
}
function inputMatchIEType(row: any, column?: string) {
    const mainKey = rowItemSearchInfo.INCOME !== "" ? 10050 : rowItemSearchInfo.EXPENDITURE !== "" ? 10060 : 0;
    if (column === "description" || column === "note") {
        const content = column === "description" ? rowItemSearchInfo.DESCRIPTION.trim() : rowItemSearchInfo.NOTE.trim();
        //摘要、备注有内容且收支类别为空时，通过摘要、备注对收支类别进行匹配
        if (content !== "" && (rowItemSearchInfo.IE_TYPE === "" || row.canMatchIEType)) {
            let currentMatchLength = 0;
            let matchIndex = -1;
            let mathName = "";
            for (let i = 0; i < wordCutList.value.length; i++) {
                const element = wordCutList.value[i];
                for (let j = 0; j < element.matchKeys.length; j++) {
                    const matchKey = element.matchKeys[j];
                    if (matchKey === "") continue;
                    if (content.indexOf(matchKey) !== -1 && matchKey.length > currentMatchLength) {
                        matchIndex = element.subKey;
                        mathName = element.value2;
                        currentMatchLength = matchKey.length;
                    }
                }
            }

            if (matchIndex <= 0) {
                // 预设关键词匹配不到，使用智能生成的关键词
                for (let i = 0; i < wordCutList.value.length; i++) {
                    const element = wordCutList.value[i];
                    if (element.hasChild || (mainKey !== 0 && element.mainKey !== mainKey)) continue;

                    for (let j = 0; j < element.matchKeywords.length; j++) {
                        const matchKeyword = element.matchKeywords[j];
                        if (matchKeyword === "") continue;

                        // 在类别内词已经按照长度排序，所以可break;
                        if (matchKeyword.indexOf("*") !== -1 && matchKeyword.length > currentMatchLength) {
                            const reg = new RegExp(matchKeyword.replace("*", ".*"));
                            if (content.match(reg) !== null) {
                                matchIndex = element.subKey;
                                mathName = element.value2;
                                currentMatchLength = matchKeyword.length;
                                break;
                            }
                        } else if (content.indexOf(matchKeyword) !== -1 && matchKeyword.length > currentMatchLength) {
                            matchIndex = element.subKey;
                            mathName = element.value2;
                            currentMatchLength = matchKeyword.length;
                            break;
                        }
                    }
                }
            }
            if (matchIndex > 0) {
                rowItemSearchInfo.IE_TYPE = matchIndex.toString();
                rowItemSearchInfo.IE_TYPE_NAME = mathName;
            }
        }
    } else if (column === "oppositeParty") {
        const party = rowItemSearchInfo.OPPOSITE_PARTY.trim();
        if (party !== "" && (rowItemSearchInfo.IE_TYPE === "" || row.canMatchIEType)) {
            let matchIndex = -1;
            let mathName = "";
            for (let i = 0; i < wordCutList.value.length; i++) {
                const element = wordCutList.value[i];
                if (element.hasChild || (mainKey !== 0 && element.mainKey !== mainKey)) continue;

                for (let j = 0; j < element.matchParties.length; j++) {
                    const matchParty = element.matchParties[j];
                    if (matchParty === "") continue;

                    if (party === matchParty) {
                        matchIndex = element.subKey;
                        mathName = element.value2;
                        break;
                    }
                }

                if (matchIndex > 0) break;
            }

            if (matchIndex > 0) {
                rowItemSearchInfo.IE_TYPE = matchIndex.toString();
                rowItemSearchInfo.IE_TYPE_NAME = mathName;
            }
        }
    } else if (column === "ieType") {
        row.canMatchIEType = rowItemSearchInfo.IE_TYPE === "";
    }
}
function checkMaxLength(description: string, name: string, limitSize: number) {
    if (description.length === limitSize) {
        ElNotify({ type: "warning", message: `亲，${name}不能超过${limitSize}个字符！` });
    }
}
function handleAmountFocus(type: "income" | "expenditure") {
    currentNeedSelectInput.value = "";
    const timer = setTimeout(() => {
        isEdit.value = true;
        function incomeCheck() {
            rowItemSearchInfo.EXPENDITURE !== "" && (rowItemSearchInfo.INCOME = "");
        }
        function expenditureCheck() {
            rowItemSearchInfo.INCOME !== "" && (rowItemSearchInfo.EXPENDITURE = "");
        }
        type === "income" ? incomeCheck() : expenditureCheck();
        clearTimeout(timer);
    }, 50);
}
function handleAmountBlur(row: ITableItem, type: "income" | "expenditure") {
    inputBlurToSubmit(row, () => {
        if (type === "income") {
            if (rowItemSearchInfo.INCOME !== "" && rowItemSearchInfo.EXPENDITURE !== "") {
                rowItemSearchInfo.EXPENDITURE = "";
            }
        } else {
            if (rowItemSearchInfo.EXPENDITURE !== "" && rowItemSearchInfo.INCOME !== "") {
                rowItemSearchInfo.INCOME = "";
            }
        }
    });
}
function handleSubmit(row: ITableItem) {
    if (rowItemSearchInfo.INCOME !== "" || rowItemSearchInfo.EXPENDITURE !== "") {
        const isZero =
            (Number(rowItemSearchInfo.INCOME) === 0 && rowItemSearchInfo.EXPENDITURE === "") ||
            (Number(rowItemSearchInfo.EXPENDITURE) === 0 && rowItemSearchInfo.INCOME === "");
        if (isZero) {
            ElConfirm("收入数据为0，不需要输入哦~", true).then(() => {
                useLastRow = true;
                if (rowItemSearchInfo.INCOME !== "" && rowItemSearchInfo.EXPENDITURE === "") {
                    rowItemSearchInfo.INCOME = "";
                    incomeInputRef.value?.focus();
                } else if (rowItemSearchInfo.EXPENDITURE !== "" && rowItemSearchInfo.INCOME === "") {
                    rowItemSearchInfo.EXPENDITURE = "";
                    expenditureInputRef.value?.focus();
                }
            });
            return;
        }
        if (rowItemSearchInfo.DESCRIPTION.trim() === "") {
            ElNotify({ type: "warning", message: "亲，请输入摘要！" });
            useLastRow = true;
            return;
        }
        if (rowItemSearchInfo.IE_TYPE === "") {
            ElNotify({ type: "warning", message: "亲，请选择收支类别！" });
            useLastRow = true;
            return;
        }
        // 有的用户还是会触发保存  自己无法复现  拦截一下
        if (dialogCellOperateRef.value?.checkDialogUnitDisplay()) return;
        emit("submitEdit", row, rowItemSearchInfo);
    }
}
function insertNewRow(row: ITableItem) {
    if (props.tableData.findIndex((item) => !!item.isCopyData) !== -1) return;
    isEdit.value = true;
    emit("insertNewRow", row);
}
function copyRow(row: ITableItem) {
    const copyIndex = props.tableData.findIndex((item) => !!item.isCopyData);
    if (copyIndex !== -1) {
        const item = props.tableData[copyIndex];
        allowBlur = false;
        handleSubmit(item);
    }
    isEdit.value = true;
    if (row.ie_type == "-1") return;
    emit("copyRow", row);
}
function deleteRow(row: ITableItem) {
    isEdit.value = true;
    if (row.ie_type == "-1") return;
    if (row.erp_offset == "1") return;
    emit("deleteRow", row);
}
function printRow(row: ITableItem) {
    if (props.tableData.findIndex((item) => !!item.isCopyData) !== -1) return;
    isEdit.value = true;
    emit("printRow", row);
}
function receiptRow(row: ITableItem) {
    if (props.tableData.findIndex((item) => !!item.isCopyData) !== -1) return;
    isEdit.value = true;
    emit("receiptRow", row);
}
function formatFileState(receiptCount: string) {
    const fileType = props.journalType === "1020" ? "回单" : "附件";
    const fileNum = ~~receiptCount === 0 ? "" : `(${receiptCount})`;
    return fileType + fileNum;
}

function routeToJournalPage(name: string,created_date:string) {
    const path = props.journalType === "1010" ? "/Cashier/CashJournal" : "/Cashier/DepositJournal";
    const params = { ...getATagParams(name) };
    params.CD_DATE = formatDate(params.CD_DATE);
    params.from = path;
    params.CREATED_DATE = created_date;
    globalWindowOpenPage("/Cashier/JournalPage?" + getUrlSearchParams(params), params.JOURNAL_TYPE === "INCOME" ? "收款凭据" : "付款凭据");
}
function routeToVoucherPage(row: ITableItem) {
    const from = props.journalType === "1010" ? "cashjournal" : "depositjournal";
    const fcode = props.journalType === "1010" ? "cashjournal-cancreatevoucher" : "depositjournal-cancreatevoucher";
    const params = { pid: row.p_id, vid: row.v_id, fcode, from };
    globalWindowOpenPage("/Voucher/VoucherPage?" + getUrlSearchParams(params), "查看凭证");
}

const debugCanChangeCurrentPage = useDebugCurrentPage(
    paginationData,
    () => {
        resetRowInfo();
        emit("load-data");
    },
    20
);

function checkCurrentSelectStatus() {
    props.tableData.forEach((row) => {
        const cacheRow = selections.value.find((item) => checkIsSameJournal(row, item));
        // 勾选行点插入会创建一个一摸一样的行出来但是这个行是不能勾选的 会导致页面异常
        if (cacheRow && selectable(row)) {
            row[selectKey] = true;
        } else if (selectable(row)) {
            row[selectKey] = false;
        }
    });
    selectAllStatus.value =
        props.tableData.filter((row) => selectable(row)).length > 0 && props.tableData.every((row) => !selectable(row) || !!row[selectKey]);
}

const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
function querySearch(queryString: string, cb: any) {
    if (!queryString) {
        return;
    }
    if (queryString.length > 50) {
        cb([]);
    } else {
        nextTick(() => {
            autocompleteSelfAdaption();
        });
        getCompanyList(1010, queryString, cb, queryParams);
    }
}
function handleUnitSelect(val: any) {
    rowItemSearchInfo.OPPOSITE_PARTY = val.value;
    oppositePartyRef.value?.blur();
    const timer = setTimeout(() => {
        isEdit.value = true;
        clearTimeout(timer);
    }, 50);
    props.showAllList ? projectRef.value?.focus() : incomeInputRef.value?.focus();
}

watch(
    () => rowItemSearchInfo.IE_TYPE,
    (ie_type, old) => {
        if (ie_type !== "") {
            const ietypeItem = props.payList.find((item) => item.subkey === ie_type) as IPayMethod;
            const tableItem = props.tableData.find((item) => item.index === currentEditIndex.value) as ITableItem;
            if (tableItem && ietypeItem) {
                if (rowItemSearchInfo.INCOME || rowItemSearchInfo.EXPENDITURE) {
                    const direction = ietypeItem.value2.slice(0, 1);
                    const amountDirection = rowItemSearchInfo.INCOME === "" ? "支" : "收";
                    if (direction !== amountDirection) {
                        nextTick().then(() => {
                            ElNotify({ type: "warning", message: "收支类别和金额方向不匹配，不能保存哦" });
                            rowItemSearchInfo.IE_TYPE = "";
                            rowItemSearchInfo.IE_TYPE_NAME = "";
                        });
                    }
                }
            }
        }
    }
);

function changeEditInfo(data: any) {
    Object.assign(rowItemSearchInfo, data);
}
function setPaginationData(data: IPaginationData) {
    Object.assign(paginationData, data);
}
function getPaginationData() {
    return paginationData;
}
function resetAllowBlur() {
    allowBlur = true;
}
const newJournalIsEdit = computed(() => rowItemSearchInfo.DESCRIPTION.trim() !== "");
const columns = ref<Array<IColumnProps>>([]);
const cashierColumnsSet = ref<Array<IColumnProps>>([]);
function setColumns() {
    columns.value.length = 0;
    const timer = setTimeout(() => {
        let defaultList = getTableColumns(props.cdAccount, props.journalType, props.showAllList, props.useFc, props.allAccountListLength);
        cashierColumnsSet.value = getTableColumns(props.cdAccount, props.journalType, true, props.useFc, props.allAccountListLength);
        if (!props.showAllList) {
            columns.value = allColumns.value.length > 0 ? getShowColumn(allColumns.value, cashierColumnsSet.value) : defaultList;
        } else {
            if (props.useFc) {
                defaultList.forEach((item) => {
                    if (item.slot === "income" || item.slot === "expenditure" || item.slot === "amount") {
                        item.slot += "Fc";
                    }
                });
            }
            columns.value = JSON.parse(JSON.stringify(defaultList));
        }
        watchDrop();
        clearTimeout(timer);
    });
}
function toggleSelection2False(row: ITableItem) {
    if (!selectable(row)) return;
    row[selectKey] = false;
    const index = selections.value.findIndex((item) => checkIsSameJournal(row, item));
    if (index !== -1) selections.value.splice(index, 1);
    checkCurrentSelectStatus();
}
function updateSelections() {
    const timer = setTimeout(() => {
        props.tableData.forEach((row) => {
            if (row[selectKey]) {
                const cacheRowIndex = selections.value.findIndex((item) => checkIsSameJournal(row, item));
                cacheRowIndex !== -1 && Object.assign(selections.value[cacheRowIndex], row);
            }
        });
        emit("selection-change", selections.value);
        clearTimeout(timer);
    });
}
defineExpose({
    changeEditInfo,
    handleRowClick,
    setPaginationData,
    getPaginationData,
    resetRowInfo,
    setUseLastRow,
    setColumns,
    debugCanChangeCurrentPage,
    resetAllowBlur,
    newJournalIsEdit,
    handleClearSelection,
    toggleSelection2False,
    updateSelections,
    setScrollTop,
    setRowInEditContentDetail,
});

//日记账序列号字段复制
function handleCopy() {
    const oText = document.getElementById("lineSn")!.innerText || "";
    copyText(oText);
    ElNotify({ type: "success", message: "复制成功！" });
}
//表头字段模糊搜索
function filterSearch(prop: string, value: any) {
    emit("filterSearch", prop, value);
}
//列设置弹窗
const columnSetShow = ref(false);
const columnSetRef = ref<InstanceType<typeof ColumnSet>>();
const allColumns = ref<IColItem[]>([]);
function getColumnSetList() {
    getColumnListApi(module.value)
        .then((res) => {
            allColumns.value = res.data;
        })
        .catch(() => {
            allColumns.value = [];
        });
}
getColumnSetList();
function openColSet() {
    columnSetShow.value = true;
    columnSetRef.value?.initData();
    nextTick(() => {
        columnSetRef.value?.rowDrop();
    });
}
function saveColumnSet(data: IColItem[]) {
    if (!props.showAllList) {
        columns.value.length = 0;
        nextTick(() => {
            columns.value = getShowColumn(data, cashierColumnsSet.value);
        });
    }
    watchDrop();
    getColumnSetList();
}
//头部列拖拽设置
const setModule = computed(() => (props.journalType === "1010" ? "CashJournal" : "DepositJournal"));
function cellDrop(oldIndex: number, newIndex: number) {
    let index1 = allColumns.value.findIndex((v) => v.columnName === columns.value[oldIndex].label);
    let index2 = allColumns.value.findIndex((v) => v.columnName === columns.value[newIndex].label);
    allColumns.value = alterArrayPos(allColumns.value, index1, index2);
    let data = JSON.parse(JSON.stringify(columns.value));
    columns.value.length = 0;
    nextTick(() => {
        columns.value = alterArrayPos(data, oldIndex, newIndex);
        columnSetRef.value?.saveData(module.value, allColumns.value);
    });
}
watch(
    () => props.showAllList,
    () => {
        watchDrop();
    },
    { immediate: true }
);
function watchDrop() {
    nextTick(() => {
        tableRef.value?.columnDrop(tableRef.value?.$el, columns.value, props.showAllList);
    });
}

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");  
//下拉组件拼音首字母搜索
const filterIEType = ref<Array<IPayMethod>>([]);
const payMethodListOptions = ref<IPaymentMethodItem[]>([]);
const projectListOptions = ref<IAssistingAccount[]>([]);
const departmentListOptions = ref<IAssistingAccount[]>([]);
const cdAccountListOptions = ref<any[]>([]);
watchEffect(() => { 
    filterIEType.value = JSON.parse(JSON.stringify(props.payList));
    payMethodListOptions.value = JSON.parse(JSON.stringify(props.paymentMethodList));  
    projectListOptions.value = JSON.parse(JSON.stringify(props.projectList));  
    departmentListOptions.value = JSON.parse(JSON.stringify(props.departmentList));  
    cdAccountListOptions.value = JSON.parse(JSON.stringify(props.cdAccountList));
});
function ietypeFilterMethod(value: string) {
    filterIEType.value = commonFilterMethod(value, props.payList, 'value2');
    autoAddName.value = filterIEType.value.length === 0 ? value.trim() : "";
}
function paymentFilterMethod(value: string) {
    payMethodListOptions.value = commonFilterMethod(value, props.paymentMethodList, 'value');
    autoAddName.value = payMethodListOptions.value.length === 0 ? value.trim() : "";
}
function projectFilterMethod(value: string) {
    projectListOptions.value = commonFilterMethod(value, props.projectList, 'aaname');
    autoAddName.value = projectListOptions.value.length === 0 ? value.trim() : "";
}
function departFilterMethod(value: string) {
    departmentListOptions.value = commonFilterMethod(value, props.departmentList, 'aaname');
    autoAddName.value = departmentListOptions.value.length === 0 ? value.trim() : "";
}
function accountFilterMethod(value: string) {
    cdAccountListOptions.value = commonFilterMethod(value, props.cdAccountList, 'label');
}

function getForbiddenText(ac_no: string) {
    return props.cdAccountList.find((item) => item.ac_no === ac_no)?.state === "1" ? "（已禁用）" : "";
}

</script>

<style lang="less" scoped>
@import "@/style/Cashier/JournalTable.less";
</style>
<style lang="less">
@import "@/style/Cashier/JournalPopper.less";
.lineSn-copy {
    margin-left: 10px;
    cursor: pointer;
}
</style>
