<template>
    <div class="edit-content" style="width: 1000px; padding: 0px">
        <div class="title">{{ props.changeTitle }}</div>
        <div style="border: 1px solid var(--slot-title-color);margin-top: 32px;">
            <table class="change-tb">
                <tr>
                    <td colspan="4" class="tb-hr">基本信息</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px; text-align: right">资产编号：</td>
                    <td id="ztxg_bh" class="tb-field" style="width: 326px">{{ props.changeData.fa_num }}</td>
                    <td class="tb-title" style="width: 84px">资产类别：</td>
                    <td id="ztxg_lb" class="tb-field">
                        <Tooltip :content="props.changeData.fa_type_name" :max-width="326">{{ props.changeData.fa_type_name }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">资产名称：</td>
                    <td id="ztxg_mc" class="tb-field">
                        <Tooltip :content="props.changeData.fa_name" :max-width="326">{{ props.changeData.fa_name }}</Tooltip>
                    </td>
                    <td class="tb-title">资产型号：</td>
                    <td id="ztxg_xh" class="tb-field">
                        <Tooltip :content="props.changeData.fa_model" :max-width="326">{{ props.changeData.fa_model }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">所属部门：</td>
                    <td id="ztxg_bm" class="tb-field">
                        <Tooltip :content="props.changeData.department_name" :max-width="326">{{
                            props.changeData.department_name
                        }}</Tooltip>
                    </td>
                </tr>
            </table>
            <div class="line"></div>
            <table class="change-tb" cellspacing="0" cellpadding="0">
                <tr>
                    <td colspan="4" class="tb-hr">变更内容</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px">原状态：</td>
                    <td id="ztxg_value1" class="tb-field" style="width: 326px">
                        {{ changeData.status == "1" ? "正常" : changeData.status == "2" ? "停用" : "出租" }}
                    </td>
                    <td class="tb-title" style="width: 84px"></td>
                    <td class="tb-field" style="width: 416px"></td>
                </tr>
                <tr>
                    <td class="tb-title">状态调整为：</td>
                    <td class="tb-field">
                        <div class="jqtransform">
                            <el-select 
                                v-model="dataValue"
                                :filterable="true"
                                :filter-method="changeStatusFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showChangeStatusList" 
                                    :value="item.value" 
                                    :label="item.label" 
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="buttons" style="margin-top: 22px; margin-bottom: 40px; text-align: center">
                <a class="button solid-button" style="margin: 0 5px" @click="savedata">保存</a>
                <a class="button" style="margin: 0 5px" @click="changeCancle">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { formatMoney } from "@/util/format";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ref, watch, watchEffect } from "vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";
import { changeStatusList } from "@/views/FixedAssets/utils";

const props = defineProps({
    changeData: {
        type: Object,
        default: () => {
            return {};
        },
    },
    changeTitle: {
        type: String,
        default: "",
    },
    pid: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(["changeCancle", "saveData"]);

const dataValue = ref("1");

function savedata() {
    if (dataValue.value == props.changeData.status) {
        ElNotify({
            type: "warning",
            message: "亲，您的数据没有变动，不能保存哦",
        });
        return;
    }
    request({
        url: `/api/FAChange/StatusChange?faid=${props.changeData.fa_id}&pid=${props.pid}&value=${dataValue.value}&vid=0`,
        method: "post",
    }).then((res: IResponseModel<number | string>) => {
        emits("saveData", res,'ztxg',{status:dataValue});
    });
}
function changeCancle() {
    emits("changeCancle");
}

watch(
    () => props.changeData,
    () => {
        dataValue.value = props.changeData.status;
    },{immediate:true}
);

const showChangeStatusList = ref<Array<any>>([]);
watchEffect(() => {
    showChangeStatusList.value = JSON.parse(JSON.stringify(changeStatusList));
});
function changeStatusFilterMethod(value: string) {
    showChangeStatusList.value = commonFilterMethod(value, changeStatusList, 'label');
}
</script>

<style scoped lang="less">
.tb-hr {
    padding: 20px 20px 15px 20px;
    color: var(--font-color);
    font-size: var(--h3);
    line-height: 22px;
    font-weight: bold;
    text-align: left;
}
.change-tb {
    & .tb-title {
        text-align: right;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
    }
    & .tb-field {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 14px;
        text-align: left;
    }
}
</style>
