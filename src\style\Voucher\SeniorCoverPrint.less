@import "../SelfAdaption.less";
@import "../Functions.less";
:deep(.el-textarea__inner) {
    min-height: 100% !important;
}
.line {
    position: absolute;
}
.line-x {
    left: 0;
    right: 0;
    height: 1px;
    background-color: #44b449;
    width: 100%;
    &.isErp {
        background-color: #3d7fff !important;
    }
}
.line-y {
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: #44b449;
    height: 100%;
    &.isErp {
        background-color: #3d7fff !important;
    }
}
:deep(.el-input__wrapper) {
    padding: 0 11px;
    height: 100%;
}

:deep(.vdr-container) {
    &.active {
        border-style: none !important;
    }

    &.resizable.active {
        border-style: solid !important;
        border-color: #44b449 !important;
        &.isErp {
            border-color: #3d7fff !important;
        }
    }
    &.unactive {
        border-style: none !important;
        .vdr-handle {
            display: none !important;
        }
    }
    & .vdr-handle {
        z-index: 9999;
    }
}

:deep(.smooth-dnd-container) {
    display: flex !important;
    height: 100%;
    width: 100%;
}

.seniorPrint {
    padding: 10px !important;
    height: 100%;
    box-sizing: border-box;
    //拖动时取消选中效果
    user-select: none;
}
.print-top {
    display: flex;
    justify-content: space-between;
}
.print-tool-left {
    display: flex;
    justify-content: flex-start;
    padding: 0 0 10px 0;
}
.zoom-btn {
    position: absolute;
    right:276px;
}
.print-tool-right {
    .screen-btn {
        margin-left: 18px;
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
        &::before {
            content: " ";
            width: 17px;
            height: 17px;
            background-repeat: no-repeat;
            background-size: 100%;
            margin-right: 7px;
        }

        &:hover {
            color: var(--main-color);
        }
        &.full-screen {
            color: var(--main-color);
            &::before {
                width: 20px;
                height: 20px;
                background-image: url("@/assets/Voucher/suoxiao.png");
            }
            &.erp {
                &::before {
                    background-image: url("@/assets/Voucher/erp-suoxiao.png");
                }
            }
        }
        &.exit-full-screen {
            color: var(--font-color);
            &::before {
                width: 19px;
                height: 19px;
                background-image: url("@/assets/Voucher/fangda.png");
            }
            span {
                position: relative;
                top: 1px;
            }
        }
    }
}
.video-guide {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 26px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    line-height: 26px;
    padding: 0;
    margin: 0;
}
.print-content {
    position: relative;
    height: calc(100% - 38px);
    display: flex;
    text-align: left;
    .arrowLeftNav {
        background-image: url("@/assets/Voucher/arrow-left.png");
        width: 40px;
        height: 50px;
        position: absolute;
        left: 248px;
        top: 300px;
        z-index: 999;
        &:hover {
            background-image: url("@/assets/Voucher/arrow-left-active.png");
        }
    }
    .expandLeftNav {
        background-image: url("@/assets/Voucher/expand-left.png");
        width: 40px;
        height: 50px;
        position: absolute;
        left: 0;
        top: 300px;
        z-index: 999;
    }
    .arrowRightNav {
        background-image: url("@/assets/Voucher/arrow-right.png");
        width: 40px;
        height: 50px;
        position: absolute;
        right: 248px;
        top: 300px;
        z-index: 999;
        &:hover {
            background-image: url("@/assets/Voucher/arrow-right-active.png");
        }
    }
    .expandRightNav {
        background-image: url("@/assets/Voucher/expand-right.png");
        width: 40px;
        height: 50px;
        position: absolute;
        right: 0;
        top: 300px;
        z-index: 999;
    }

    .nav-name,
    .page {
        padding-left: 20px;
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        font-style: normal;
    }
    .nav-tool {
        height: 80px;
        border-bottom: 1px solid #ddd;
        padding: 16px 0;
        .tool-list {
            display: flex;
            // margin-top: 16px;
            flex-wrap: wrap;
            margin: 12px 8px;
            justify-content: space-between;
            .tool {
                flex-basis: calc(25% - 10px); /* 计算为三分之一减去间距 */
                margin-bottom: 10px; /* 设置元素间距 */
            }
            .tool {
                width: 42px;
                margin-bottom: 12px;
                padding-top: 4px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                background: #f8fbf8;
                &:hover {
                    color: #44b449;
                }
            }
            i {
                width: 25px;
                height: 25px;
            }
            .text-icon {
                i {
                    background: url("@/assets/Voucher/text.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/text-hover.png");
                }
            }
            .img-icon {
                i {
                    background: url("@/assets/Voucher/img.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/img-hover.png");
                }
            }
            .line-icon {
                i {
                    background: url("@/assets/Voucher/line.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/line-hover.png");
                }
            }
            .square-icon {
                i {
                    background: url("@/assets/Voucher/square.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/square-hover.png");
                }
            }
            .tool-name {
                line-height: 22px;
                font-size: 12px;
                white-space: nowrap;
            }
        }
    }
}

.print-left {
    width: 248px;
    border: 1px solid #ddd;
    margin-right: 16px;
    box-sizing: border-box;
    overflow-y: hidden;
    // height: 610px;
}
.print-center {
    flex: 1;
    border: 1px solid #ddd;
    background-color: #f6f6f6;
    color: #000;
    overflow: auto;
    position: relative;
    .detail-lm-default-scroll(8px);
    .main-drag-box {
        position: relative;
        background-color: #fff;
        margin: 20px auto;
        .drag-box {
            position: absolute;
            span {
                display: inline-block;
            }
        }
        img {
            width: 100%;
            height: 100%;
        }
    }
}
.print-right {
    width: 248px;
    border: 1px solid #ddd;
    overflow: scroll;
    .detail-lm-default-scroll(8px);
    margin-left: 16px;
    .rich-text {
        border-bottom: 1px solid #ddd;
        padding: 16px 0;
        .rich-text-name {
            padding-left: 22px;
        }
        .typeFace {
            margin: 12px 20px;
            .rich-text-btn {
                padding-left: 10px;
                &.is-active {
                    color: #44b449;
                }
            }
        }
        .rich-text-col {
            display: flex;
            margin: 12px 10px;
            justify-content: flex-start;
            flex-wrap: wrap;
            .cell {
                text-align: center;
                align-items: center;
                display: flex;
                flex-direction: column;
                width: 48px;
                height: 37px;
                padding-top: 4px;
                margin-right: 4px;
                margin-bottom: 12px;
                img {
                    width: 17px;
                    height: 17px;
                }
                span {
                    font-size: 12px;
                    white-space: nowrap;
                }
                &.is-active {
                    color: #44b449;
                }
            }
        }
    }
}

.erpSeniorPrint{
    .print-content {
        .nav-table {
            :deep(.el-collapse-item__header) {
                background-color: #f7f9fd;
            }
        }
        .nav-tool {
            .tool-list .tool {
                background-color: #f7f9fd;
                &:hover {
                    color: #3d7fff;
                }
                &.text-icon:hover i {
                    background-image: url("@/assets/Voucher/text-blue.png");
                }
                &.img-icon:hover i {
                    background-image: url("@/assets/Voucher/img-blue.png");
                }
                &.square-icon:hover i {
                    background-image: url("@/assets/Voucher/square-blue.png");
                }
                &.line-icon:hover i {
                    background-image: url("@/assets/Voucher/line-blue.png");
                }
            }
        }
        .arrowLeftNav {
            &:hover {
                background-image: url("@/assets/Voucher/arrow-left-blue.png");
            }
        }
        .arrowRightNav {
            &:hover {
                background-image: url("@/assets/Voucher/arrow-right-blue.png");
            }
        }
        .expandLeftNav {
            background-image: url("@/assets/Voucher/expand-left-blue.png");
        }
        .expandRightNav {
            background-image: url("@/assets/Voucher/expand-right-blue.png");
        }
    }
    .print-right{
        .rich-text{
            .rich-text-btn{
                &.is-active{
                    color: #3d7fff;
                }
            }
        }
        .rich-text-col .cell{
            &.is-active{
                color: #3d7fff;
            }
        }
    }
    
}
