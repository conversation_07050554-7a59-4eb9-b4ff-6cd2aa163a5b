<template>
    <div class="content">
        <div class="title">进销存基础资料</div>
        <div class="main-content align-center">
            <div class="slot-mini-content">
                <el-tabs v-model="tabName" @tab-click="handleTabClick">
                    <el-tab-pane label="客户" name="Customer">
                        <CustomerView
                            ref="CustomerRef"
                            v-if="isLoaded.Customer"
                            :scmAsid="scmAsid"
                            :scmProductType="scmProductType"
                            @handleImport="handleImportDialog"
                        ></CustomerView>
                    </el-tab-pane>
                    <el-tab-pane label="供应商" name="Vendor">
                        <VendorView
                            ref="VendorRef"
                            v-if="isLoaded.Vendor"
                            :scmAsid="scmAsid"
                            :scmProductType="scmProductType"
                            @handleImport="handleImportDialog"
                        ></VendorView>
                    </el-tab-pane>
                    <el-tab-pane label="商品" name="Commodity">
                        <CommodityView
                            ref="CommodityRef"
                            v-if="isLoaded.Commodity"
                            :scmAsid="scmAsid"
                            :scmProductType="scmProductType"
                            @handleImport="handleImportDialog"
                        ></CommodityView>
                    </el-tab-pane>
                    <el-tab-pane label="账户" name="Account">
                        <AccountView
                            ref="AccountRef"
                            v-if="isLoaded.Account"
                            :scmAsid="scmAsid"
                            :scmProductType="scmProductType"
                            @handleImport="handleImportDialog"
                        ></AccountView>
                    </el-tab-pane>
                    <el-tab-pane label="其他收支" name="Other">
                        <OtherView
                            ref="OtherRef"
                            v-if="isLoaded.Other"
                            :scmAsid="scmAsid"
                            :scmProductType="scmProductType"
                            @handleImport="handleImportDialog"
                        ></OtherView>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <el-dialog
            v-model="showImportDialog"
            title="导入"
            :lock-scroll="false"
            center
            destroy-on-close
            width="550"
            class="custom-confirm dialogDrag"
        >
            <div class="import-dialog" v-dialogDrag>
                <div class="import-main">
                    <p>第一步，请点击下面的链接下载Excel模板，并按照模板填写信息</p>
                    <a class="link" @click="downloadTemplate">下载模板</a>
                    <br />
                    <p>第二步，导入Excel模板文件</p>
                    <p class="choose-file">
                        <label class="file-button mr-20">
                            <input @change="onFileSelected" ref="fileInputRef" type="file" :accept="allowFileType" />
                            <a class="link">选取文件</a>
                        </label>
                        <span class="file-name">{{ fileName }}</span>
                    </p>
                    <br />
                    <el-checkbox v-model="isCover">覆盖导入</el-checkbox>
                </div>
                <div class="buttons">
                    <a class="button solid-button mr-10" @click="uploadFile">导入</a>
                    <a class="button" @click="showImportDialog = false">取消</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "ScmBasicData",
};
</script>
<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import CustomerView from "./components/Customer.vue";
import VendorView from "./components/Vendor.vue";
import CommodityView from "./components/Commodity.vue";
import AccountView from "./components/Account.vue";
import OtherView from "./components/Other.vue";
import { request } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { ElNotify } from "@/util/notify";

const tabName = ref("Customer");

// 获取组件
const CustomerRef = ref();
const VendorRef = ref();
const CommodityRef = ref();
const AccountRef = ref();
const OtherRef = ref();

const isLoaded = reactive({
    Customer: true,
    Vendor: false,
    Commodity: false,
    Account: false,
    Other: false,
});
const scmAsid = ref(0);
const scmProductType = ref(0);
function checkIsRelation() {
    request({
        url: `/api/ScmRelation`,
    }).then((res: any) => {
        scmAsid.value = res.data.scmAsid;
        scmProductType.value = res.data.scmProductType;
    });
}

function handleTabClick(tab: any) {
    const { name } = tab.props;
    if (!(isLoaded as any)[name]) {
        (isLoaded as any)[name] = true;
    }
}

// 导入
const showImportDialog = ref(false);
const fileName = ref("");
const selectedFile = ref();
const fileInputRef = ref();

// 支持文件类型
const allowFileType = ref(".xls,.xlsx");

// 是否覆盖
const isCover = ref(false);
// 导入类型
const importType = ref("");

// 导入弹窗
const handleImportDialog = (type: string) => {
    fileName.value = "";
    selectedFile.value = null;
    importType.value = type;
    isCover.value = false;
    showImportDialog.value = true;
};

// 下载模板
const downloadTemplate = () => {
    const params = {
        type: importType.value,
        scmAsid: scmAsid.value,
        scmProductType: scmProductType.value,
        appasid: getGlobalToken(),
    };
    globalExport(`/api/ScmBasicData/ExportScmAsubChangeTemplate?${getUrlSearchParams(params)}`);
};
// 选择文件
const onFileSelected = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const file: File = (input.files as FileList)[0];
    if (!file) {
        fileName.value = "";
        selectedFile.value = null;
        return;
    }
    fileName.value = file.name;
    selectedFile.value = file;
};

// 上传文件
let canImport = true;
const uploadFile = async () => {
    if (!canImport) return;
    if (!selectedFile.value) {
        ElNotify({
            type: "warning",
            message: "请先选择导入文件",
        });
        return;
    }

    const fileExtension = selectedFile.value.name.split(".").pop().toLowerCase();
    if (
        !allowFileType.value
            .split(",")
            .map((ext) => ext.trim().replace(".", ""))
            .includes(fileExtension)
    ) {
        ElNotify({
            type: "warning",
            message: `亲，请使用正确的文件导入`,
        });
        return;
    }
    canImport = false;
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    const params = {
        type: importType.value,
        scmAsid: scmAsid.value,
        scmProductType: scmProductType.value,
        appasid: getGlobalToken(),
        isCover: isCover.value ? 1 : 0,
    };

    request({
        url: `/api/ScmBasicData/ImportScmAsubChange?${getUrlSearchParams(params)}`,
        data: formData,
        method: "post",
    })
        .then((res) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: `导入成功！`,
                });
                nextTick(() => {
                    switch (importType.value) {
                        case "1010":
                            CustomerRef.value.initCustomer();
                            break;
                        case "1020":
                            VendorRef.value.initVendor();
                            break;
                        case "1030":
                            CommodityRef.value.initCommodity();
                            break;
                        case "1040":
                            AccountRef.value.initAccount();
                            break;
                        case "1050":
                            OtherRef.value.initOther();
                            break;
                        default:
                            console.log("success");
                    }
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch((error) => {
            ElNotify({
                type: "warning",
                message: `导入失败`,
            });
        })
        .finally(() => {
            showImportDialog.value = false;
            canImport = true;
        });
};

onMounted(() => {
    checkIsRelation();
});
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";

.content {
    .main-content {
        .slot-mini-content {
            height: 100%;
        }
    }
}
:deep(.el-tabs) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .el-tabs__nav-wrap {
        overflow: initial;
        &:after {
            left: -10px;
            right: 0;
            width: auto;
        }
    }
}

:deep(.el-tabs__content) {
    background-color: var(--white);
    flex: 1;
    .el-tab-pane {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
}
:deep(.el-tabs__header) {
    padding-left: 10px !important;
    background-color: white;
    margin: 0px;
}
:deep(.main-top) {
    padding: 16px 10px;
}

// 导入弹窗
.import-dialog {
    .import-main {
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
        padding: 20px 50px;
        text-align: left;
        .choose-file {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .file-name {
                max-width: 160px;
                overflow: hidden;
                text-overflow: ellipsis;
                height: 20px;
                word-break: break-all;
                white-space: nowrap;
                cursor: pointer;
            }
        }
    }

    .buttons {
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        padding: 10px;
    }
}
</style>
