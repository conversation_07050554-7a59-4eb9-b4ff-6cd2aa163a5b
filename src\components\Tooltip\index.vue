<template>
    <el-tooltip
        :placement="placement"
        :offset="offset"
        :effect="effect"
        :visible="visible"
        :popper-class="popperClass"
        :teleported="teleported"
        class="customPover"
        :append-to="appendTo"
    >
        <template #content>
            <div class="popover_content" style="text-align: left">{{ content }}</div>
        </template>
        <span
            class="span_wrap"
            title=""
            ref="tooltipRef"
            :class="props.class"
            :style="{ '-webkit-line-clamp': props.lineClamp }"
            @mouseenter="checkOverflow"
            @mouseleave="handleMouseLeave"
            @input="controlInput"
        >
            <slot> </slot>
        </span>
    </el-tooltip>
</template>

<script setup lang="ts">
import { ref, type Ref } from "vue";

type Placement =
    | "top"
    | "top-start"
    | "top-end"
    | "bottom"
    | "bottom-start"
    | "bottom-end"
    | "left"
    | "left-start"
    | "left-end"
    | "right"
    | "right-start"
    | "right-end";

const props = withDefaults(
    defineProps<{
        content: string;
        maxWidth?: number;
        fontSize?: number;
        lineClamp?: number;
        isInput?: boolean;
        shieldDistance?: number;
        teleported?: boolean;
        placement?: Placement;
        offset?: number;
        class?: string;
        effect?: string;
        dynamicWidth?: boolean;
        popperClass?: string;
        appendTo?: HTMLElement;
        calcContent?: string;
        alwaysShow?: boolean;
    }>(),
    {
        content: "",
        maxWidth: 300,
        fontSize: 14,
        lineClamp: 2,
        isInput: false,
        shieldDistance: 0,
        teleported: false,
        placement: "top",
        offset: 12,
        class: "",
        effect: "light",
        dynamicWidth: false,
        popperClass: "",
        calcContent: "",
        alwaysShow: false,
    }
);
const visible = ref(false);
const tooltipRef: Ref<HTMLElement | null> = ref(null);
const checkOverflow = (event: Event) => {
    if (inputflag) {
        return;
    }
    if (props.alwaysShow) {
        visible.value = true;
        return;
    } 
    if (props.isInput) {
        // el-input和原生input两种情况
        const ipt = tooltipRef.value?.querySelector(".el-input__inner") || tooltipRef.value?.querySelector("input");
        let offsetWidth = (ipt as HTMLElement).offsetWidth;
        let scrollWidth = ipt?.scrollWidth;
        if (scrollWidth! > offsetWidth) {
            visible.value = true;
        } else {
            visible.value = false;
        }
    } else {
        // 创建一个新的 span 元素
        let clone = document.createElement("span");
        // 将原来的 span 元素的文本复制到新的 span 元素
        clone.textContent = props.calcContent || props.content;
        // 设置新的 span 元素的样式
        clone.style.display = "inline";
        clone.style.width = "auto";
        clone.style.visibility = "hidden";
        clone.style.whiteSpace = "nowrap"; // 确保文本不会换行
        clone.style.fontSize = props.fontSize + "px";

        // 将新的 span 元素添加到 DOM
        document.body.appendChild(clone);
        // 获取新的 span 元素的宽度
        let width = clone.getBoundingClientRect().width;

        const dom = event.target as HTMLElement;
        const domWidth = dom?.getBoundingClientRect().width || props.maxWidth;
        const maxWidth = props.dynamicWidth ? domWidth : props.maxWidth;
        // 从 DOM 中移除新的 span 元素
        document.body.removeChild(clone);
        if (width > maxWidth) {
            visible.value = true;
        } else {
            visible.value = false;
        }
    }
};
const handleMouseLeave = () => {
    inputflag = false;
    visible.value = false;
};
let inputflag = false;
const controlInput = (event: any) => {
    const target = event.target;

    if (target.tagName === "INPUT") {
        // 处理输入事件
        inputflag = true;
        visible.value = false;
    }
};
</script>

<style lang="less">
.popover_content {
    max-width: 300px;
    white-space: normal;
    color: black;
    overflow-wrap: break-word;
    word-break: break-all;
}

.span_wrap {
    display: -webkit-box;
    max-width: 100%;
    //会遮挡输入框
    // overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    /* 显示省略号 */
    -webkit-box-orient: vertical;
}
.customPover {
    max-width: 300px;
    text-align: left;
}
</style>
