<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <span class="highlight-red">*</span>
            <span style="color: rgba(0, 0, 0, 0.45)"> 提示：实时同步显示关联的进销存其他收支信息，匹配的会计科目会自动生成凭证。 </span>
        </div>
        <div class="main-tool-right">
            <a class="button large-2" @click="batchEdit">批量指定科目</a>
            <a class="button ml-10" @click="handleImport">导入</a>
            <a class="button ml-10" @click="handleExport">导出</a>
        </div>
    </div>
    <div class="main-center">
        <Table
            :data="tableData"
            :loading="loading"
            :columns="columns"
            :pageIsShow="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            :show-overflow-tooltip="true"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @selection-change="handleSelectionChange"
            @row-click="handleRowClick"
            @refresh="handleRerefresh"
            ref="tableRef"
            :scrollbarShow="true"
            :tableName="setModule"
        ></Table>
    </div>
    <el-dialog v-model="batchDialogVisible" :title="batchDialogTitle" center width="439" class="dialogDrag">
        <div class="dialog-center" v-dialogDrag>
            <div class="dialog-line">
                <div class="dialog-line-title">会计科目：</div>
                <div class="dialog-line-field asub1" style="position: relative">
                    <SubjectPicker v-model="SubjectControl7" :diyWidth="'218px'" :isById="true" :asubImgRight="'4px'"></SubjectPicker>
                </div>
            </div>
        </div>
        <div class="dialog-bottom">
            <a class="button solid-button" @click="batchSubmit">确定</a>
            <a class="button ml-10" @click="() => (batchDialogVisible = false)">取消</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted  } from "vue";
import Table from "@/components/Table/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { request } from "@/util/service";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";
import { usePagination } from "@/hooks/usePagination";
import { ElNotify } from "@/util/notify";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "ScmOther";
const props = defineProps({
    scmAsid: {
        type: Number,
        default: 0,
    },
    scmProductType: {
        type: Number,
        default: 0,
    },
});

const emits = defineEmits<{
    (e: "handleImport", type: string): void;
}>();

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const columns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 30, headerAlign: "center", align: "center" },
    { prop: "num", label: "编码", minWidth: 95, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "num") },
    { prop: "name", label: "进销存其他收支名称", minWidth: 495, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "name") },
    { prop: "asubName1", label: "会计科目", minWidth: 445, align: "left", headerAlign: "left", resizable: false, },
]);

const tableData = ref([]);
const loading = ref(false);
function initOther() {
    loading.value = true;
    const data = {
        type: 1050,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    request({
        url: `/api/ScmBasicData/PagingList?` + getUrlSearchParams(data),
        // headers: {
        //     scmProductType: props.scmProductType,
        //     scmAsid: props.scmAsid,
        // },
    })
        .then((res: any) => {
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
        })
        .finally(() => {
            loading.value = false;
        });
}

const batchDialogVisible = ref<boolean>(false);
const batchDialogTitle = ref<string>("批量指定科目");
const SubjectControl7 = ref("");
const batchIds = ref<number[]>([]);
function handleSelectionChange(val: any) {
    batchIds.value = val.map((item: any) => {
        return item.id;
    });
}
function batchEdit() {
    if (batchIds.value.length === 0) {
        ElNotify({
            type: "warning",
            message: "请选择数据后批量设置科目",
        });
        return;
    }
    batchDialogTitle.value = "批量指定科目";
    SubjectControl7.value = "";
    batchDialogVisible.value = true;
    type = "multiple";
}
function batchSubmit() {
    const asubids = [Number(SubjectControl7.value)];
    const data = {
        type: 1050,
        ids: type === "multiple" ? batchIds.value : singleIds,
        asubids: asubids,
    };
    request({
        url: `/api/ScmBasicData/BatchSetAsubs?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
        method: "post",
        data,
        // headers: {
        //     scmProductType: props.scmProductType,
        //     scmAsid: props.scmAsid,
        // },
    }).then((res: any) => {
        if (res.state === 1000 && res.data === true) {
            ElNotify({
                type: "success",
                message: "保存成功",
            });
            batchDialogVisible.value = false;
            initOther();
        } else if (res.state === 2000) {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        } else {
            ElNotify({
                type: "error",
                message: "保存失败，请刷新页面后重试",
            });
        }
    });
}

// 导入
const handleImport = () => {
    emits("handleImport", "1050");
};
// 导出
const handleExport = () => {
    const params = {
        type: 1050,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
        appasid: getGlobalToken(),
    };
    globalExport(`/api/ScmBasicData/Export?${getUrlSearchParams(params)}`);
};

defineExpose({
    initOther,
});
watch(
    [() => props.scmAsid],
    () => {
        initOther();
    },
    { immediate: true }
);

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], initOther);

//点击行选中 @row-click="handleRowClick" ref="tableRef"
const tableRef = ref();
let type: "single" | "multiple" = "single";
let singleIds: Array<number> = [];
const handleRowClick = (val: any, column: any, event: any) => {
    if (column.property === "asubName1") {
        // 未选择科目时，为空
        SubjectControl7.value = val.asubId1 === 0 ? "" : val.asubId1 + "";
        batchDialogTitle.value = "指定科目";
        batchDialogVisible.value = true;
        type = "single";
        singleIds = [val.id];
        return;
    }
    tableRef.value.getTable().toggleRowSelection(val);
};
onMounted(() => {
    window.addEventListener("modifyaccountSubject", initOther);
});
onUnmounted(() => {
    window.removeEventListener("modifyaccountSubject", initOther);
});
</script>

<style scoped lang="less">
.dialog-center {
    padding-top: 50px;
    padding-bottom: 49px;
    text-align: left;

    & div {
        display: inline-block;
    }
    & .dialog-line {
        & .dialog-line-title {
            text-align: right;
            width: 145px;
        }
    }
}
.dialog-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 11px;
    padding-bottom: 10px;
    text-align: center;
}
</style>
