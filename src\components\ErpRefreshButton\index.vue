<template>
    <div :class="['refresh-icon', props.marginLeft]" v-if="isErp" @click="refreshCurrent" title="刷新"></div>
</template>
<script lang="ts" setup>
import { ref, toRef, computed } from "vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const currentPage = computed(() => {
    return routerArray.value.find((item) => item.alive);
});
const isErp = ref(window.isErp);
const props = withDefaults(
    defineProps<{
        marginLeft?: string;
        reload?: () => void;
    }>(),
    {
        marginLeft: "ml-20",
    }
);
const refreshCurrent = () => {
    props.reload ? props.reload() : routerArrayStore.refreshRouter(currentPage.value!.path);
};
</script>
