<template>
    <el-dialog
        class="custom-confirm dialogDrag"
        title="添加存货"
        center
        v-model="addStocktShow"
        width="440px"
        :destroy-on-close="true"
        @close="handleStocktCancel"
    >
        <div class="add-ietype-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="add-ietype-line-item required">
                <div class="add-ietype-title">存货编码：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="stocktCode"
                        @input="handleAAInput(LimitCharacterSize.Code, $event, '存货编码', 'aaNum', changeStocktData)"
                        @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-line-item required">
                <div class="add-ietype-title">存货名称：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="stocktName"
                        @input="handleAAInput(LimitCharacterSize.Name, $event, '存货名称', 'aaName', changeStocktData)"
                        @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-line-item">
                <div class="add-ietype-title">规格型号：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="stocktModel"
                        @input="handleAAInput(LimitCharacterSize.Size, $event, '规格型号', 'stockModel', changeStocktData)"
                        @paste="handleAAPaste(LimitCharacterSize.Size, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-line-item">
                <div class="add-ietype-title">计量单位：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="stocktUnit"
                        @input="handleAAInput(LimitCharacterSize.Default, $event, '计量单位', 'unit', changeStocktData)"
                        @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-line-item">
                <div class="add-ietype-title">备注：</div>
                <div class="add-ietype-field">
                    <input
                        type="text"
                        v-model="stocktNote"
                        @input="handleAAInput(LimitCharacterSize.Note, $event, '备注', 'note', changeStocktData)"
                        @paste="handleAAPaste(LimitCharacterSize.Note, $event)"
                    />
                </div>
            </div>
            <div class="add-ietype-buttons">
                <a class="button solid-button" @click="handleStocktSave">保存</a>
                <a class="button ml-10" @click="handleStocktCancel">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import { getNextAaNum } from "@/views/Settings/AssistingAccounting/utils";

const isErp = ref(window.isErp);

const props = defineProps<{
    addStocktShow: boolean;
}>();
const emit = defineEmits<{
    (e: "update:addStocktShow", value: boolean): void;
    (e: "save-stockt", data: string): void;
}>();

const addStocktShow = computed({
    get: () => props.addStocktShow,
    set: (value) => emit("update:addStocktShow", value),
});

const stocktCode = ref("");
const stocktName = ref("");
const stocktNote = ref("");
const stocktModel = ref("");
const stocktUnit = ref("");
const handleStocktSave = () => {
    if (!stocktCode.value.trim()) {
        ElNotify({ type: "warning", message: "请输入编码" });
        return;
    }
    if (!stocktName.value.trim()) {
        ElNotify({ type: "warning", message: "请输入名称" });
        return;
    }
    createCheck(10006, 0, stocktCode.value, stocktName.value, SaveStockt);
};
const handleStocktCancel = () => {
    addStocktShow.value = false;
    stocktCode.value = "";
    stocktName.value = "";
    stocktNote.value = "";
    stocktModel.value = "";
    stocktUnit.value = "";
};
const SaveStockt = () => {
    const entityParams = {
        endDate: "",
        note: stocktNote.value,
        startDate: "",
        stockModel: stocktModel.value,
        stockType: "",
        unit: stocktUnit.value,
    };
    const params = {
        entity: entityParams,
        aaNum: stocktCode.value,
        aaName: stocktName.value,
        uscc: "",
        status: 0,
        hasEntity: true,
        ifvoucher: true,
    };
    const requestParams = {
        url: "/api/AssistingAccounting/Stock",
        method: "post",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(params),
    };
    request(requestParams).then((res: any) => {
        if (res.state == 1000 && res.data) {
            ElNotify({ type: "success", message: "保存成功" });
            emit("save-stockt", res.data);
            handleStocktCancel();
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const changeStocktData = (key: string, val: string) => {
    if (key === "aaNum") {
        stocktCode.value = val;
    } else if (key === "aaName") {
        stocktName.value = val;
    } else if (key === "note") {
        stocktNote.value = val;
    } else if (key === "stockModel") {
        stocktModel.value = val;
    } else if (key === "unit") {
        stocktUnit.value = val;
    }
};

const changeCode = (autoAddName: string = "") => {
    stocktName.value = autoAddName.slice(0, LimitCharacterSize.Name);
    if (autoAddName.length > LimitCharacterSize.Name) {
        ElNotify({ type: "warning", message: `亲，存货名称不能超过${LimitCharacterSize.Name}个字符!` });
    }
    getNextAaNum(10006).then((res) => {
        if (res.state === 1000) {
            stocktCode.value = res.data;
        }
    });
};

defineExpose({ changeCode });
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.add-ietype-content {
    padding: 20px 0;
    text-align: center;
    .add-ietype-line-item {
        margin-right: 40px;
        &.required {
            .add-ietype-title:before {
                content: "*";
                color: var(--red);
            }
        }
        & + .add-ietype-line-item {
            margin-top: 16px;
        }
        .add-ietype-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            display: inline-block;
            vertical-align: middle;
            width: 120px;
            text-align: right;
        }
        .add-ietype-field {
            display: inline-block;
            vertical-align: middle;
            text-align: left;
            .detail-el-select(200px, 32px);
            & > input {
                .detail-original-input(200px, 32px);
            }
        }
    }
    .add-ietype-buttons {
        margin-top: 24px;
    }

    &.erp {
        padding-bottom: 0px;
        .add-ietype-buttons {
            height: 30px;
            display: block;
            box-sizing: content-box;
            border-top: 1px solid var(--border-color);
            text-align: right;
            padding: 16px 20px 16px 0;
            & a {
                text-align: center;
                display: block;
                float: right;
                width: 70px;
                min-width: 70px;
                height: 30px;
                border-radius: 2px;
                margin-left: 20px;
                line-height: 30px;
            }
            & button {
                box-sizing: border-box;
                padding: 0 12px;
                font-size: var(--font-size);
            }
        }
    }
}
</style>
