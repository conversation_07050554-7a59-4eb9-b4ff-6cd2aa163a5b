export interface DialogOptions {
  message: string | VNode
  title?: string
  showCancel?: boolean
  buttons?: {
    confirm: string
    cancel?: string
  }
  modal?: {
    closeOnClick?: boolean
    showClose?: boolean
    width?: string
    class?: string
  }
  position?: {
    zIndex?: number
    align?: "left" | "center"
  }
  mount?: {
    global?: boolean
    inTabsContent?: boolean
  }
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
  onClose?: () => void
}

export interface ConfirmInstance {
  show: (options: DialogOptions) => Promise<boolean>
  close: () => void
}
