import { createApp } from "vue";
import ElementPlus from "element-plus";
import { ElNotify } from "./notify";
import { useLoading } from "@/hooks/useLoading";
import { type IResponseModel, request } from "./service";
import { getGlobalToken } from "./baseInfo";

import type { IProcessBack } from "@/views/Settings/Backup/types";

import BackupConfirm from "@/components/BackupConfirm/index.vue";

enum BackStatus {
    CanNext = 1,
    Error,
    Timeout,
    NeedBackup,
}
enum CurrentBackStatus {
    Success = 1,
    Error,
    Waiting,
}
enum ErpBackStatus {
    Error = 0,
    Waiting,
    Success,
}
type LoopBackStatus = BackStatus.CanNext | BackStatus.Error | BackStatus.Timeout;
interface ErpCheckBacking {
    id: string;
    createdDate: string;
    fileName: string;
    fileSize: number;
    status: ErpBackStatus;
}
function mapCurrentBackStatusToBackStatus(status: CurrentBackStatus) {
    let backStatus: BackStatus = BackStatus.Error;
    switch (status) {
        case CurrentBackStatus.Success:
            backStatus = BackStatus.CanNext;
            break;
        case CurrentBackStatus.Error:
            backStatus = BackStatus.Error;
            break;
    }
    return backStatus;
}
async function checkHasBacking(token?: string): Promise<BackStatus> {
    let backStatus = BackStatus.Error;
    const appasid = token || getGlobalToken();
    await request({ url: "/api/Backup/BackupList?appasid=" + appasid })
        .then(async (res: IResponseModel<Array<IProcessBack>>) => {
            if (res.state !== 1000 || !res.data) {
                backStatus = BackStatus.Error;
                ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
            } else if (!res.data.length) {
                backStatus = BackStatus.NeedBackup;
            } else if (res.data[0].progress !== 100) {
                useLoading().enterLoading("备份数据加载中，请稍后...");
                backStatus = await loopCheckCanNext(res.data[0].fileName, token);
            } else if (new Date().getTime() <= new Date(res.data[0].dateTime).getTime() + 15 * 60 * 1000) {
                backStatus = BackStatus.CanNext;
            } else {
                backStatus = BackStatus.NeedBackup;
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
        });
    return backStatus;
}
async function checkErpHasBacking(): Promise<BackStatus> {
    let backStatus = BackStatus.Error;
    await request({ url: "/api/ErpBackUp/GetLastCompletedBackFile" }).then(async (res: IResponseModel<ErpCheckBacking | null>) => {
        if (res.state !== 1000) {
            backStatus = BackStatus.Error;
        } else if (!res.data || res.data.status === ErpBackStatus.Error) {
            backStatus = BackStatus.NeedBackup;
        } else if (res.data.status === ErpBackStatus.Waiting) {
            useLoading().enterLoading("备份数据加载中，请稍后...");
            backStatus = await loopCheckCanNext(res.data.id);
        } else if (new Date().getTime() <= new Date(res.data.createdDate).getTime() + 15 * 60 * 1000) {
            backStatus = BackStatus.CanNext;
        } else {
            backStatus = BackStatus.NeedBackup;
        }
    });
    return backStatus;
}
async function beginBackup(loadingText?: string, token?: string): Promise<LoopBackStatus> {
    loadingText = loadingText || "备份中，请稍后...";
    let result = BackStatus.Error;
    useLoading().enterLoading(loadingText);
    const appasid = token || getGlobalToken();
    let url = window.isErp
        ? "/api/AccountSubject/ErpStartBackup?appasid="
        : "/api/Backup/ProcessBackup?allowTemporaryFile=1&isProcess=1&appasid=";
    url += appasid;
    await request({ url, method: "post" })
        .then(async (res: IResponseModel<IProcessBack | string>) => {
            if (res.state !== 1000) {
                useLoading().quitLoading();
                ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
                result = BackStatus.Error;
                return;
            }
            const param = window.isErp ? (res.data as string) : (res.data as IProcessBack).fileName;
            result = await loopCheckCanNext(param, token);
        })
        .catch(() => {
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
            result = BackStatus.Error;
        });
    return result;
}
async function getErpProgress(backUpId: string): Promise<CurrentBackStatus> {
    let status = CurrentBackStatus.Error;
    await request({ url: "/api/AccountSubject/CheckErpBackupState", method: "post", params: { backUpId } })
        .then((res: IResponseModel<number>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
                status = CurrentBackStatus.Error;
            } else if (res.data === 1) {
                status = CurrentBackStatus.Waiting;
            } else if (res.data === 2) {
                status = CurrentBackStatus.Success;
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
        });
    return status;
}
async function getProgress(fileName: string, token?: string): Promise<CurrentBackStatus> {
    const appasid = token || getGlobalToken();
    let status = CurrentBackStatus.Error;
    await request({ url: `/api/Backup/GetFileRecord?appasid=${appasid}&filename=${encodeURIComponent(fileName)}`, method: "post" })
        .then((res: IResponseModel<IProcessBack>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
                status = CurrentBackStatus.Error;
            } else if (res.data.progress !== 100) {
                status = CurrentBackStatus.Waiting;
            } else {
                status = CurrentBackStatus.Success;
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
        });
    return status;
}
function showBackupTip() {
    const contentBox = document.querySelector(".router-container .content");
    const capp = createApp(BackupConfirm);
    const container = document.createElement("div");
    capp.use(ElementPlus);
    capp.mount(container);
    contentBox!.insertBefore(container, contentBox!.firstChild);
}
async function loopCheckCanNext(params: string, token?: string) {
    const date_s = new Date().getTime();
    return new Promise<LoopBackStatus>((resolve) => {
        function task() {
            const date_e = new Date().getTime();
            if (date_e - date_s > 60 * 1000) {
                useLoading().quitLoading();
                showBackupTip();
                resolve(BackStatus.Timeout);
                return;
            }
            const timer = setTimeout(async () => {
                clearTimeout(timer);
                const currentBackStatus = window.isErp ? await getErpProgress(params) : await getProgress(params, token);
                if (currentBackStatus === CurrentBackStatus.Waiting) {
                    task();
                    return;
                }
                useLoading().quitLoading();
                resolve(mapCurrentBackStatusToBackStatus(currentBackStatus));
            }, 5000);
        }
        task();
    });
}
export async function dangerousOperationNext(loadingText?: string, token?: string) {
    let backStatus = window.isErp ? await checkErpHasBacking() : await checkHasBacking(token);
    if (backStatus === BackStatus.NeedBackup) {
        backStatus = await beginBackup(loadingText, token);
    }
    if (backStatus === BackStatus.Timeout) {
        backStatus = BackStatus.Error;
    }
    return backStatus === BackStatus.CanNext;
}
