<template>
    <div class="open-account">
        <div class="title">预约开户</div>
        <div class="open-main-content">
            <TopStep :stepNumber="2" />
            <div class="bank-center">
                <div class="bank-tip">
                    <img src="@/assets/Cashier/warn.png" />
                    <span>请选择需要办理预约开户业务的银行</span>
                </div>
                <div class="bank-info">
                    <!-- 工商银行后续再开放 -->
                    <div class="bank-card" :class="bank === 'icbc' ? 'selected' : ''" @click="selectBank('icbc')" v-if="false">
                        <img src="@/assets/Cashier/ICBC.png" />
                    </div>
                    <div class="bank-card" :class="bank === 'webank' ? 'selected' : ''" @click="selectBank('webank')">
                        <img src="@/assets/Cashier/WEBANK.png" />
                        <div class="bank-gift">纯线上开户，免转账手续费</div>
                    </div>
                    <div class="bank-card center" :class="bank === 'pa' ? 'selected' : ''" @click="selectBank('pa')">
                        <img src="@/assets/Cashier/PAB.png" />
                    </div>
                    <div class="bank-card center" :class="bank === 'spdb' ? 'selected' : ''" @click="selectBank('spdb')">
                        <img src="@/assets/Cashier/SPDB.png" />
                    </div>
                    <div class="bank-card center" :class="bank === 'psbc' ? 'selected' : ''" @click="selectBank('psbc')">
                        <img src="@/assets/Cashier/PSBC.png" />
                    </div>
                    <div class="bank-card center" :class="bank === 'cmb' ? 'selected' : ''" @click="selectBank('cmb')">
                        <img src="@/assets/Cashier/CMB.png" />
                    </div>
                    <div class="bank-card center">
                        <div>更多银行敬请期待~</div>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <a class="button" @click="back">上一步</a>
                <a class="button ml-28 solid-button" @click="toStepThree">下一步</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TopStep from "./TopStep.vue";
import { ElNotify } from "@/util/notify";

const emit = defineEmits<{
    (e: "back"): void;
    (e: "nextStep", data: string): void;
}>();

const back = () => {
    emit("back");
};
const bank = ref("webank");
const selectBank = (name: string) => {
    bank.value = name;
};
const toStepThree = () => {
    if (!bank.value) {
        ElNotify({
            type: "warning",
            message: "请选择银行",
        });
        return;
    }
    emit("nextStep", bank.value);
};
defineExpose({
    selectBank,
});
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
