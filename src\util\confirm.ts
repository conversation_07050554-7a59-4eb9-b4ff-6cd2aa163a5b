import { createApp } from "vue";
import Confirm from "@/components/Confirm/index.vue";
import ElementPlus from "element-plus";
import { dialogDrag }  from "@/direcitves/dialogDrag/index";

export interface IConfirmOptions {
    confirmButtonText: string;
    cancelButtonText: string;
}

export const ElConfirm = (
    message: string,
    hideCancel?: boolean,
    close?: Function,
    title?: string,
    options?: IConfirmOptions,
    leftJustifying?: boolean, // 是否左对齐
    zIndex?: number, // 层级
    closeOnClickModal?: boolean, //是否可以通过点击 modal 关闭 Dialog
    showClose?: boolean, // 是否显示关闭按钮
    isGlobal?: boolean, //是否插入body
    isInTabsContent?: boolean, //是否插入当前路由content，非全局插入body的时候可以关闭多页签时把弹窗关闭掉
    dialogWidth?: string, //弹窗宽度
    modalClass?: string,
    confirmHandle?: () => void,
    cancelHandle?: () => void,
    buttonClass?: string,
    id?: string,
): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        const isInTabsContentFlag = isInTabsContent ?? true;
        const content = isInTabsContentFlag
            ? document.querySelector(".router-container .content")
            : document.querySelector(".router-container");
        const contentBox = isGlobal ? document.body : content;
        const props = {
            message,
            hideCancel,
            close: () => {
                if (close) close();
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
            title,
            options,
            leftJustifying,
            zIndex,
            closeOnClickModal,
            confirm: () => {
                confirmHandle?.();
                resolve(true);
                capp.unmount(); //注销
                contentBox!.removeChild(container); //点击后清除弹窗
            },
            cancel: () => {
                cancelHandle?.();
                resolve(false);
                capp.unmount();
                contentBox!.removeChild(container);
            },
            showClose: showClose,
            isGlobal: isGlobal ?? false,
            dialogWidth,
            modalClass: modalClass ?? "modal-class",
            buttonClass,
            id,
        };
        const capp = createApp(Confirm, props);
        const container = document.createElement("div");
        capp.use(ElementPlus);
        capp.directive('dialogDrag', dialogDrag);
        capp.mount(container);
        contentBox!.insertBefore(container, contentBox!.firstChild); //插入到body最前面，层级更高
    });
};
interface IAlertOptions {
    message: string;
    hideCancel?: boolean;
    close?: Function;
    title?: string;
    options?: IConfirmOptions;
    leftJustifying?: boolean; // 是否左对齐
    zIndex?: number; // 层级
    closeOnClickModal?: boolean; //是否可以通过点击 modal 关闭 Dialog
    showClose?: boolean; // 是否显示关闭按钮
    isGlobal?: boolean; //是否插入body
    isInTabsContent?: boolean; //是否插入当前路由content，非全局插入body的时候可以关闭多页签时把弹窗关闭掉
    dialogWidth?: string; //弹窗宽度
    modalClass?: string;
    confirmHandle?: () => void;
    cancelHandle?: () => void;
    buttonClass?: string;
    id?: string;
}
export const ElAlert = (alertOptions: IAlertOptions) => {
    const {
        message,
        hideCancel,
        close,
        title,
        options,
        leftJustifying,
        zIndex,
        closeOnClickModal,
        showClose,
        isGlobal,
        isInTabsContent,
        dialogWidth,
        modalClass,
        confirmHandle,
        cancelHandle,
        buttonClass,
        id,
    } = alertOptions;
    const hideCancelFlag = hideCancel ?? false;
    const closeFlag = close ?? function () {};
    const titleFlag = title ?? "提示";
    const optionsFlag = options ?? { confirmButtonText: "确定", cancelButtonText: "取消" };
    const leftJustifyingFlag = leftJustifying ?? false;
    const closeOnClickModalFlag = closeOnClickModal ?? false;
    const showCloseFlag = showClose ?? true;
    const isGlobalFlag = isGlobal ?? false;
    const isInTabsContentFlag = isInTabsContent ?? true;
    const dialogWidthFlag = dialogWidth ?? "440";
    const modalClassFlag = modalClass ?? "modal-class";
    const handleConfirm = confirmHandle ?? function () {};
    const handleCancel = cancelHandle ?? function () {};
    const buttonClassFlag = buttonClass ?? "";
    const DialogId = id ?? "";
    return ElConfirm(
        message,
        hideCancelFlag,
        closeFlag,
        titleFlag,
        optionsFlag,
        leftJustifyingFlag,
        zIndex,
        closeOnClickModalFlag,
        showCloseFlag,
        isGlobalFlag,
        isInTabsContentFlag,
        dialogWidthFlag,
        modalClassFlag,
        handleConfirm,
        handleCancel,
        buttonClassFlag,
        DialogId,
    );
};
