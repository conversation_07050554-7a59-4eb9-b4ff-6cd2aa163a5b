<template>
    <div class="trial-dialog" v-if="trialDialogShow">
        <div class="trial-dialog-container">
            <div class="trial-dialog-title">
                <img src="@/assets/ExpiredDialgo/trial7-dialog-icon.png" />
                <div>您的专业版功能体验期还剩 {{ remainingDays }}天！</div>
            </div>
            <img class="trial-dialog-close" src="@/assets/ExpiredDialgo/trial7-dialog-close.png" @click="trialDialogShow = false" />
            <div class="txt">凭证、账簿、结账、报表等免费版功能可继续长期免费正常使用！</div>
            <div class="txt mt-20">
                <div>资金、发票、工资、资产、关联进销存模块为专业版功能，体验</div>
                <div>到期后专业版功能不能使用，但之前的录入数据可以查询和导出。</div>
            </div>
            <div class="btn" @click="onclickPurchase">立即升级</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { buyPro } from "@/util/proUtils";
import { computed, ref } from "vue";

const trialDialogShow = ref(true);

const trialStatusStore = useTrialStatusStore();
const remainingDays = computed(() => {
    return trialStatusStore.remainingDays;
});

const onclickPurchase = () => {
    buyPro();
    trialDialogShow.value = false;
};
</script>

<style scoped lang="less">
.trial-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--shadow-color);
    z-index: 9999;
    & .trial-dialog-container {
        width: 630px;
        height: 401px;
        background-color: var(--white);
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        border-radius: 16px;
        & .trial-dialog-title {
            background-image: url("@/assets/ExpiredDialgo/trial7-dialog-bg.png");
            background-repeat: no-repeat;
            background-size: 100%;
            margin: -14px -14px 22px;
            height: 163px;
            display: flex;
            align-items: center;
            justify-content: center;
            & img {
                width: 33px;
                height: 33px;
                margin-right: 8px;
            }
            & div {
                font-size: 24px;
                font-weight: 600;
                color: #735524;
                line-height: 16px;
            }
        }
        & .trial-dialog-close {
            height: 33px;
            width: 33px;
            cursor: pointer;
            position: absolute;
            top: 12px;
            right: 12px;
        }
        & .txt {
            font-size: 18px;
            font-weight: 500;
            color: #563e17;
            line-height: 30px;
            padding-left: 43px;
        }
        & .btn {
            width: 200px;
            height: 48px;
            text-align: center;
            line-height: 48px;
            align-self: center;
            // margin-top: 87px;
            margin-top: 50px;
            font-size: 20px;
            font-weight: 600;
            color: #6c4b15;
            background: linear-gradient(177deg, #fff7e8 0%, #f4dbb2 100%);
            border-radius: 24px;
            cursor: pointer;
        }
    }
}
</style>
