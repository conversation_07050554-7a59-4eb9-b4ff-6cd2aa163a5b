@import "../Functions.less";
.main-content {
    .right-bottom-link-img,
    .main-right-bottom-link-img {
        height: 16px;
        width: 16px;
        background-image: url("@/assets/Settings/icon-qqservice.png");
        float: left;
        margin-left: 100px;
    }
    .right-bottom-link-text,
    .main-right-bottom-link-text {
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        color: rgba(24, 144, 255, 1);
        float: right;
        position: relative;
        .link-qrcode {
            visibility: hidden;
            opacity: 0;
            transition: 0.2s;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0);
            bottom: 16px;
            padding-bottom: 5px;
        }
    }
    :deep(.el-tabs) {
        .el-tabs__header {
            margin-bottom: 0;
        }
        .el-tabs__content {
            overflow: visible;
            height: 550px;
            & > div {
                position: relative;
            }
        }
    }
    .main-center-statement {
        margin: 0 auto 10px;
        height: 420px;
        padding-top: 50px;
        .main-center-left {
            width: 450px;
            float: left;
            padding-left: 50px;
            padding-right: 0px;
            min-height: 430px;
            padding-top: 20px;
            box-sizing: border-box;
            .main-center-left-row {
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                color: rgba(51, 51, 51, 1);
                line-height: 40px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                .row-content {
                    width: 238px;
                    display: flex;
                    align-items: center;
                    margin-right: 60px;
                    position: relative;
                    line-height: 20px;
                    .detail-el-select(238px, 34px);
                    &.date {
                        .detail-el-select(86px, 34px);
                    }
                    > input {
                        .detail-original-input(100%, 32px);
                    }
                    .file-Name {
                        position: absolute;
                        left: 0;
                        top: 20px;
                        font-size: 12px;
                        color: #44b449;
                        display: flex;
                        align-items: center;
                        &:hover {
                            .clear {
                                display: inline-block;
                            }
                        }
                        .icon {
                            width: 10px;
                            height: 11px;
                            margin-right: 5px;
                            background: url("@/assets/Settings/icon-file.png") no-repeat center;
                        }
                        .clear {
                            width: 10px;
                            height: 11px;
                            overflow: hidden;
                            padding-left: 10px;
                            width: 10px;
                            height: 11px;
                            background: url("@/assets/Settings/icon-delete.png") no-repeat center;
                            cursor: pointer;
                            display: none;
                        }
                    }
                }
                .row-title {
                    flex: 1;
                    text-align: right;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                }
            }
        }
        .main-center-link {
            right: 20px;
            top: 20px;
        }
        .main-center-right {
            height: 310px;
            width: 450px;
            float: left;
            padding-top: 8px;
            .main-center-right-row {
                font-size: 14px;
                font-weight: 400;
                color: rgba(51, 51, 51, 1);
                margin-top: 5px;
                .main-center-right-row-soft-img {
                    background-image: url("@/assets/Settings/soft.png");
                    width: 500px;
                    height: 345px;
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                }
            }
        }
        .main-right-bottom-link {
            width: 170px;
            position: relative;
            float: right;
            margin-top: 110px;
            text-align: right;
            height: 16px;
            &:hover {
                .main-right-bottom-link-text {
                    .link-qrcode {
                        visibility: visible;
                        opacity: 1;
                    }
                }
            }
        }
    }
    
    .bottom {
        margin-bottom: 30px;
        height: 30px;
        margin-top: 15px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .bottom-button {
        width: 102px;
        height: 32px;
        background: rgba(68, 180, 73, 1);
        border: 1px solid rgba(68, 180, 73, 1);
        border-radius: 4px;
        text-align: center;
        margin: 0 auto;
        font-size: 12px;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
        line-height: 32px;
        cursor: pointer;
        &:hover {
            color: #ffffff;
            border-color: #6ed773;
            background-color: #6ed773;
        }
    }
    .main-center-link {
        font-size: 12px;
        font-weight: 400;
        color: rgba(24, 144, 255, 1);
        width: 170px;
        position: absolute;
        display: flex;
        align-items: center;
        text-decoration: none;
        cursor: pointer;
        .main-center-link-img {
            height: 24px;
            width: 24px;
            background-image: url("@/assets/Settings/icon-help.png");
        }
    }
}
