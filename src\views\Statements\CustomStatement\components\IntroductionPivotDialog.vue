<template>
    <div>
        <el-dialog
            v-model="introductionPivotDialogShow"
            width="540px"
            title="数据透视表"
            class="introduction-Pivot-dialog custom-confirm dialogDrag"
            @close="closeDialog"
        >
            <div class="dialog-content" v-dialogDrag>
                <div class="dialog-container">
                    <div class="left-container">
                        <img src="@/assets/Statements/introduction-pivot.png" alt="数据透视表" />
                    </div>
                    <div class="right-container">
                        <div class="text">
                            <p>
                                您可以通过数据透视表将会计科目、辅助核算 类别、取值金额等字段通过拖拽方式，自定义
                                会计区间来查看不同级别的明细数据;
                            </p>
                            <p>根据您自己的需求对报表数据进行自定义排列 组合布局，生成属于您的专属数据报表</p>
                        </div>
                    </div>
                </div>
                <div class="buttons erp-buttons">
                    <a class="button solid-button mr-10" @click="goAdd">去看看</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { checkPermission } from "@/util/permission";
import { ElNotify } from "@/util/notify";

const accountsetStore = useAccountSetStore();
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
});
const emits = defineEmits(["update:modelValue", "goPivotTable"]);
const introductionPivotDialogShow = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
const goAdd = () => {
    if (checkPermission(["customstatement-canedit"])) {
        emits("goPivotTable");
    } else {
        ElNotify({
            type: "warning",
            message: "您没有数据透视表的编辑权限，请联系管理员添加",
        });
    }
    introductionPivotDialogShow.value = false;
    localStorage.setItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-pivot-intro-dialog", String(false));
};
const closeDialog = () => {
    introductionPivotDialogShow.value = false;
    localStorage.setItem(accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "-pivot-intro-dialog", String(false));
};
</script>

<style lang="less" scoped>
.introduction-Pivot-dialog {
    .dialog-content {
        .dialog-container {
            display: flex;
            justify-content: space-between;
            padding: 40px 28px;
            box-sizing: border-box;
            .left-container {
                width: 160px;
                img {
                    width: 155px;
                    height: 154px;
                }
            }
            .right-container {
                flex: 1;
                margin-left: 26px;
                .text {
                    p {
                        text-align: left;
                        line-height: 24px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
        .buttons {
            border-top: 1px solid var(--border-color);
        }
    }
}
</style>
