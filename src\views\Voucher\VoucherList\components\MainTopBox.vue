<template>
    <div class="main-top main-tool-bar space-between split-line">
        <div class="main-tool-left">
            <SearchInfoContainer ref="containerRef" :class="{ expend: expendSearchContainer }">
                <template v-slot:title>{{ currentPeriodInfo }}</template>
                <div class="line-item first-item input">
                    <div class="line-item-title">
                        <el-select :teleported="false" v-model="firstSearchItem">
                            <el-option label="会计期间：" value="0" />
                            <el-option label="凭证日期：" value="1" />
                        </el-select>
                    </div>
                    <div class="line-item-field">
                        <!-- <PeriodPicker
                            ref="periodRef"
                            v-model:startPid="startPid"
                            v-model:endPid="endPid"
                            v-model:periodInfo="periodInfo"
                            v-show="firstSearchItem === '0'"
                        /> -->
                        <DatePicker
                            ref="periodRef"
                            v-model:startPid="searchInfo.startMonth"
                            v-model:endPid="searchInfo.endMonth"
                            :clearable="false"
                            :editable="false"
                            :dateType="'month'"
                            :value-format="'YYYYMM'"
                            :label-format="'YYYY年MM月'"
                            :isPeriodList="true"
                            @getActPid="getActPid"
                            v-show="firstSearchItem === '0'"
                        />
                        <DatePicker
                            v-model:startPid="startDate"
                            v-model:endPid="endDate"
                            v-model:periodInfo="periodInfoDate"
                            v-show="firstSearchItem === '1'"
                            :clearable="true"
                            :disabled-date-start="disabledDate"
                            :disabled-date-end="disabledDate"
                        />
                    </div>
                </div>
                <div class="line-item input">
                    <div class="line-item-title">凭证字：</div>
                    <div class="line-item-field">
                        <el-select 
                            :teleported="false" 
                            v-model="searchInfo.credentialWord" 
                            :filterable="true"
                            :filter-method="vourcherGroupFilterMethod"
                        >
                            <el-option v-for="item in showVoucherGroupList" :key="item.id" :value="item.id + ''" :label="item.title" />
                        </el-select>
                    </div>
                </div>
                <div class="line-item input">
                    <div class="line-item-title">制单人：</div>
                    <div class="line-item-field">
                        <Select 
                            :teleported="false" 
                            v-model="searchInfo.prepareBy" 
                            :filterable="true"
                            :remote="true"
                            :filter-method="userNameFilterMethod"
                            :remote-show-suffix="true"
                        >
                            <ElOption v-for="(item, index) in showUserNameList" :key="index" :label="item.label" :value="item.value" />
                        </Select>
                    </div>
                </div>
                <div class="line-item input" v-show="checkNeeded">
                    <div class="line-item-title">是否审核：</div>
                    <div class="line-item-field">
                        <el-select :teleported="false" v-model="searchInfo.selectApprovalStatus">
                            <el-option label="全部" value="-1" />
                            <el-option label="已审核" value="2" />
                            <el-option label="未审核" value="1" />
                        </el-select>
                    </div>
                </div>
                <div class="line-item input">
                    <div class="line-item-title">辅助核算：</div>
                    <div class="line-item-field aae">
                        <Select
                            class="type"
                            :circleClose="true"
                            IconClearRight="26px"
                            :teleported="false"
                            v-model="searchInfo.aaType"
                            :clearable="true"
                            @change="handleAATypeChange"
                            @visible-change="handleAATypeVisibleChange"
                            :filterable="true"
                            :remote="true"
                            :filter-method="aaTypeFilterMethod"
                            :remote-show-suffix="true"
                        >
                            <ElOption v-for="(item, index) in showAATypeList" :key="index" :label="item.label" :value="item.value" />
                        </Select>
                        <SelectV2
                            :clearable="true"
                            :emptyIsValue="true"
                            :filterable="true"
                            :teleported="false"
                            :options="showAAEListDet"
                            :toolTipOptions="{ dynamicWidth: true }"
                            v-model="searchInfo.aaeid"
                            @visible-change="handleAAVisibleChange"
                            :remote="true"
                            :filter-method="aaeFilterMethod"
                            :isSuffixIcon="true"                 
                        />
                    </div>
                </div>
                <div class="line-item input">
                    <div class="line-item-title">数量核算：</div>
                    <div class="line-item-field">
                        <el-select :teleported="false" v-model="searchInfo.quantityType">
                            <el-option label="全部" value="0" />
                            <el-option label="数量核算凭证" value="1" />
                            <el-option label="非数量核算凭证" value="2" />
                        </el-select>
                    </div>
                </div>
                <div class="line-item input">
                    <div class="line-item-title">外币核算：</div>
                    <div class="line-item-field">
                        <el-select :teleported="false" v-model="searchInfo.fcType">
                            <el-option label="全部" value="0" />
                            <el-option label="外币核算凭证" value="1" />
                            <el-option label="非外币核算凭证" value="2" />
                        </el-select>
                    </div>
                </div>
                <div v-show="expendSearchContainer">
                    <div class="line-item input">
                        <div class="line-item-title">摘要：</div>
                        <div class="line-item-field">
                            <el-input v-model="searchInfo.txtDescription" style="width: 298px" clearable />
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">备注：</div>
                        <div class="line-item-field">
                            <el-input v-model="searchInfo.txtNote" style="width: 298px" clearable />
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">科目：</div>
                        <div class="line-item-field subject">
                            <SubjectPicker
                                ref="asubRef"
                                v-model="searchInfo.asubCode"
                                :need-asub-name="false"
                                :show-disabled="true"
                                asubImgRight="16px"
                            ></SubjectPicker>
                        </div>
                    </div>
                    <div class="line-item input" v-show="searchInfo.asubCode !== ''">
                        <div class="line-item-title">借贷方向：</div>
                        <div class="line-item-field">
                            <el-select :teleported="false" v-model="searchInfo.direction">
                                <el-option label="全部" value="0" />
                                <el-option label="借" value="1" />
                                <el-option label="贷" value="2" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">金额：</div>
                        <div class="line-item-field">
                            <el-input v-model="searchInfo.txtMoneyStart" style="width: 132px" clearable />
                            <span class="mr-10 ml-10">至</span>
                            <el-input v-model="searchInfo.txtMoneyEnd" style="width: 132px" clearable />
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">凭证号：</div>
                        <div class="line-item-field">
                            <el-input
                                v-model="searchInfo.txtVoucherNoStart"
                                style="width: 132px"
                                clearable
                                @keydown="handleKeyDown"
                                @input="vNumSInput"
                            />
                            <span class="mr-10 ml-10">至</span>
                            <el-input
                                v-model="searchInfo.txtVoucherNoEnd"
                                style="width: 132px"
                                clearable
                                @keydown="handleKeyDown"
                                @input="vNumEInput"
                            />
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">排序依据：</div>
                        <div class="line-item-field">
                            <el-radio-group v-model="searchInfo.sortColumn">
                                <el-radio label="0">凭证号排序</el-radio>
                                <el-radio label="1">凭证日期排序</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">排序方式：</div>
                        <div class="line-item-field">
                            <el-radio-group v-model="searchInfo.sortType">
                                <el-radio label="0">升序</el-radio>
                                <el-radio label="1">降序</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="line-item input" v-show="isErp">
                        <div class="line-item-title">
                            <el-checkbox v-model="searchInfo.onlyTempVoucher" label="只显示暂存凭证" />
                        </div>
                        <div class="line-item-field"></div>
                    </div>
                </div>
                <div class="buttons left">
                    <a class="button solid-button" @click="btnSearch">确定</a>
                    <a class="button" @click="handleClose">取消</a>
                    <a class="button" @click="handleReset">重置</a>
                    <div class="pack-up" @click="handleExpendSearchContainer">
                        <span class="link">{{ expendSearchContainer ? "收起更多" : "更多查询" }}</span>
                        <el-icon>
                            <ArrowUp v-if="expendSearchContainer" />
                            <ArrowDown v-else />
                        </el-icon>
                    </div>
                </div>
            </SearchInfoContainer>
            <SearchInfo
                class="ml-10"
                v-if="isErp"
                :width="320"
                :height="30"
                :placeholder="'输入凭证号/摘要/科目/辅助核算/金额等关键字搜索'"
                @search="handleSearchInfo"
            ></SearchInfo>
            <ErpRefreshButton></ErpRefreshButton>
        </div>
        <div class="main-tool-right">
            <a class="button solid-button mr-10" v-permission="['voucher-canedit']" @click="handleToNewVoucher">新增凭证</a>
            <a class="button mr-10" v-show="checkNeeded" v-permission="['voucher-cancheck']" @click="handleAudit">审核</a>
            <a class="button mr-10" v-show="checkNeeded" v-permission="['voucher-canuncheck']" @click="handleUnAudit">取消审核</a>
            <!-- <a class="button mr-10" v-permission="['voucher-canprint']" @click="handlePrint">打印</a> -->
            <Dropdown :btnTxt="'打印'" :downlistWidth="85" class="mr-10" v-permission="['voucher-canprint']" >
                <li @click="directPrint">直接打印</li>
                <li @click="handlePrint">打印设置</li>
            </Dropdown>
            <Dropdown btnTxt="导出" class="mr-10" v-permission="['voucher-canexport']" :downlistWidth="100">
                <li @click="handleExport('1')">选中凭证</li>
                <li @click="handleExport('2')">当前查询凭证</li>
                <li @click="handleExport('0')">所有凭证</li>
            </Dropdown>
            <a class="button mr-10" v-permission="['voucher-canimport']" @click="handleImportDialog">导入凭证</a>
            <a class="button mr-10" v-if="canSort" @click="emit('openSortDialog')"> 整理凭证 </a>
            <Dropdown
                :btnTxt="'批量操作'"
                class="mr-10"
                :class="''"
                :downlistWidth="98"
                v-if="canCopyAndDeleteBatch || isManager || canSort || canEdit"
            >
                <li v-if="canCopyAndDeleteBatch" @click="emit('copyVouchersBatch')">批量复制</li>
                <li v-if="canCopyAndDeleteBatch" @click="emit('deleteVouchersBatch')">批量删除</li>
                <li v-if="isManager" @click="emit('batchOperate')">批量修改</li>
                <li v-if="canSort" @click="emit('sortVoucherNum')">凭证号排序</li>
                <li v-if="canSort" @click="emit('changeVoucherNum')">凭证号调整</li>
                <li v-if="canEdit" @click="emit('combineVoucher')">凭证合并</li>
            </Dropdown>
            <a class="button mr-10" @click="emit('copyVouchersBatch')" v-if="!canCopyAndDeleteBatch && canEdit">批量复制</a>
            <a class="button mr-10" @click="emit('deleteVouchersBatch')" v-if="!canCopyAndDeleteBatch && canDeleteBatch">批量删除</a>
            <Dropdown :btnTxt="'更多'" class="mr-10" :class="isErp ? 'show-more' : 'base'" :downlistWidth="124">
                <li :class="{ checked: showQut }"><el-checkbox label="显示数量单价" v-model="showQut" /></li>
                <li :class="{ checked: showFc }"><el-checkbox label="显示原币汇率" v-model="showFc" /></li>
            </Dropdown>
            <a class="button" @click="emit('openRecyclebin')">回收站</a>
            <RefreshButton></RefreshButton>
        </div>
    </div>
    <ImportSingleFileDialog
        :importTitle="'导入凭证'"
        v-model:import-show="importDialogShow"
        :importUrl="'/api/Voucher/Import?autoAddAsub=' + autoAddAccountCheckbox"
        :uploadSuccess="uploadSuccess"
        :isClearFile="isClearFile"
    >
        <template #top-tips>
            <div style="margin-left: 40px; margin-top: 20px" v-show="!isErp && !isHideBarcode">
                <a @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=*********')" class="link">
                    不会操作？点此观看视频
                </a>
            </div>
        </template>
        <template #download>
            <div>1.请选择下面任意一种方式导入凭证</div>
            <div class="mt-10 ml-10">(1)在凭证列表导出所需数据，确认后直接导入</div>
            <div class="mt-10 ml-10">
                (2)点击下载模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="downloadTemplate">下载模板</a>
            </div>
        </template>
        <template #import-content>
            <span>2.选择文件导入</span>
        </template>
        <template #bottom-tips>
            <div style="margin: 15px 20px 10px 40px; display: flex">
                <div @mouseenter="autoAddAccountVisible = true" @mouseleave="autoAddAccountVisible = false">
                    <el-checkbox v-if="!isErp" v-model="autoAddAccountCheckbox" label="自动新增科目和辅助核算"></el-checkbox>
                    <el-checkbox v-else v-model="autoAddAccountCheckbox" label="自动新增科目"></el-checkbox>
                    <el-popover
                        placement="right-start"
                        width="550"
                        :visible="autoAddAccountVisible"
                        :show-arrow="true"
                        :popper-style="{ padding: '6px' }"
                    >
                        <template #reference>
                            <span class="help-icon"></span>
                        </template>
                        <template #default>
                            <div>
                                <div class="auto-add-text">以下情况不支持新增科目</div>
                                <div class="auto-add-text"><span class="bold">1、上级科目已使用：</span>上级科目存在凭证数据，若还存在下级科目有凭证数据时，则不支持新增下级科目</div>
                                <div class="auto-add-text"><span class="bold">2、混合使用上下级科目：</span>凭证中同时包含上级和下级科目，系统无法自动匹配，不支持新增科目</div>
                                <div class="auto-add-text"><span class="bold">3、多辅助核算：</span>同一行科目在不同行使用不同辅助核算（如客户、供应商），不支持新增科目</div>
                            </div>
                        </template>
                    </el-popover>
                </div>
            </div>
        </template>
    </ImportSingleFileDialog>
    <PrintSettingDialog ref="printDialogRef" v-model:printDialogShow="printDialogShow" :printVoucherCountTypeShow="true" @confirm-print="handlePrintConfirm" @senior-print="seniorPrint"/>
    <!-- 导入凭证失败原因 -->
    <el-dialog
        v-model="importErrorReason"
        title="导入失败原因"
        center
        width="550px"
        class="custom-confirm dialogDrag"
        :before-close="cancleErrorReason"
    >
        <div class="import-error-reason" v-dialogDrag>
            <div class="import-error-reason-tip">请根据失败原因修改模板后重新导入</div>
            <div class="import-error-reason-list">
                <el-scrollbar :always="true" :max-height="300">
                    <div class="reason-item" v-for="(item, index) in errorList" :key="index">
                        <div class="reason-text">
                            (<span>{{ index + 1 }}</span
                            >) <span>{{ item }}</span>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
            <div class="buttons">
                <a class="button mr-10" @click="cancleErrorReason">取消</a>
                <a class="button solid-button" @click="comfirmErrorReason">确认</a>
            </div>
        </div>
    </el-dialog>
    <!-- 新增一级科目 -->
    <el-dialog
        v-model="addFirstSubject"
        title="新增一级科目"
        center
        width="600px"
        class="custom-confirm dialogDrag"
        :before-close="cancleAddFirstSubject"
    >
        <div class="add-first-subject" v-dialogDrag>
            <div class="add-first-subject-tip">
                <el-icon class="tip-warning"> <WarningFilled /></el-icon>
                亲，系统识别到您表格中有新增的一级科目，请确认一级科目资料，否则会导致相关凭证无法导入哦！
            </div>
            <div class="add-first-subject-table">
                <Table
                    :loading="loading"
                    :data="tableDataSubject"
                    :columns="tableColumnsSubject"
                    :page-is-show="false"
                    :height="258"
                    :scrollbar-show="true"
                >
                    <template #asubType>
                        <el-table-column label="科目类别" min-width="100" prop="asubType" align="left" header-align="left">
                            <template #header>
                                <span>科目类别</span>
                            </template>
                            <template #default="scope">
                                <Select
                                    class="subject-item"
                                    :suffix-icon="CaretBottom"
                                    v-model="tableDataSubject[scope.$index].asubType"
                                    placeholder=""
                                    :teleported="true"
                                >
                                    <ElOption
                                        v-for="item in subjectType"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></ElOption>
                                </Select>
                            </template>
                        </el-table-column>
                    </template>
                    <template #direction>
                        <el-table-column label="余额方向" min-width="100" prop="direction" align="left" header-align="left">
                            <template #header>
                                <span>余额方向</span>
                            </template>
                            <template #default="scope">
                                <Select
                                    class="subject-item"
                                    :suffix-icon="CaretBottom"
                                    v-model="tableDataSubject[scope.$index].direction"
                                    placeholder=""
                                    :teleported="true"
                                >
                                    <ElOption
                                        v-for="item in directionList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></ElOption>
                                </Select>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="buttons">
                <a class="button mr-10" @click="cancleAddFirstSubject">取消</a>
                <a class="button solid-button" @click="comfirmAddSubject">确认</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { provide, reactive, ref, computed, watch, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { globalExport, globalFormPost, globalPrint, globalWindowOpen, globalExportNew, globalWindowOpenPage, globalDirectPrint } from "@/util/url";
import { getUrlSearchParams } from "@/util/url";
import { ElConfirm } from "@/util/confirm";
import { componentFinishKey } from "@/symbols";
import { dayjs } from "element-plus";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { checkPermission } from "@/util/permission";
import { handleKeyDown, useNumberInput } from "@/util/format";
import { getDaysInMonth } from "../utils";

import type { IVoucherModel, IVoucherGroup, ISearchParams, ISubjectItem } from "../types";

import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import PrintSettingDialog from "@/views/Voucher/components/PrintSettingDialog/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import SearchInfo from "@/components/SearchInfo/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import type { IPeriod } from "@/api/period";
import { getCookie, setCookie } from "@/util/cookie";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useLoading } from "@/hooks/useLoading";
import { WarningFilled, CaretBottom } from "@element-plus/icons-vue";
import Select from "@/components/Select/index.vue";
import ElOption from "@/components/Option/index.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import type { IPaginationData } from "@/hooks/usePagination";
import SelectV2 from "@/components/SelectV2/index.vue";
import { getAccountSubjectTabList } from "@/views/Settings/AccountSubject/utils";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { initStartOrEndMonth, getCurrentPeriodInfo, type IPeriodData } from "@/components/DatePicker/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { useVoucherSettingsStore } from "@/store/modules/voucherSettings";
import { isLemonClient } from "@/util/lmClient";

const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const route = useRoute();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const printDialogRef = ref<InstanceType<typeof PrintSettingDialog>>();
const asubRef = ref<InstanceType<typeof SubjectPicker>>();
// const periodRef = ref<InstanceType<typeof PeriodPicker>>();
const periodRef = ref();
const periodStore = useAccountPeriodStore();

const props = defineProps<{
    columnType: number;
    getSelectedvoucherList: Function;
    tableData: any[];
    paginationData: IPaginationData;
    checkNeeded: boolean;
    voucherGroupList: IVoucherGroup[];
    maxSearchDate: string;
    periodDateList: IPeriod[];
}>();
const emit = defineEmits<{
    (e: "update:columnType", columnType: number): void;
    (e: "openRecyclebin"): void;
    (e: "deleteVouchersBatch"): void;
    (e: "copyVouchersBatch"): void;
    (e: "openSortDialog"): void;
    (e: "handleSearch", needCheck?: boolean): void;
    (e: "handleInitSearch", needCheck?: boolean): void;
    (e: "loadData"): void;
    (e: "handlePrint"): void;
    (e: "importSuccess"): void;
    (e: "batchOperate"): void;
    (e: "sortVoucherNum"): void;
    (e: "changeVoucherNum"): void;
    (e: "combineVoucher"): void;
}>();
const columnType = computed({
    get() {
        return props.columnType;
    },
    set(value: number) {
        emit("update:columnType", value);
    },
});
const startDate = ref("");
const endDate = ref("");
watch(startDate, (val) => {
    if (val + "" === "null") {
        startDate.value = "";
    }
});
watch(endDate, (val) => {
    if (val + "" === "null") {
        endDate.value = "";
    }
});
const checkNeeded = computed(() => props.checkNeeded);
const voucherGroupList = computed(() => props.voucherGroupList);
const userNameList = ref<string[]>([]);

const asStartDate = useAccountSetStore().accountSet?.asStartDate ?? "";
// const periodInfo = ref("");
const periodInfoDate = ref("");
const currentPeriodInfo = ref("");
const firstSearchItem = ref("0");
const showQut = ref(false);
const showFc = ref(false);
const importDialogShow = ref(false);
const printDialogShow = ref(false);
const searchInfo = reactive({
    asubCode: "",
    credentialWord: "",
    prepareBy: "",
    selectApprovalStatus: "-1",
    txtDescription: "",
    txtNote: "",
    txtMoneyStart: "",
    txtMoneyEnd: "",
    txtVoucherNoStart: "",
    txtVoucherNoEnd: "",
    sortColumn: "0",
    sortType: "0",
    onlyTempVoucher: route.query.onlyTempVoucher === "1",
    aaType: "",
    aaeid: "",
    quantityType: "0",
    fcType: "0",
    direction: "0",
    startMonth: "",
    endMonth: "",
});
const startPid = ref(Number(periodStore.getPeriodRange().start));
const endPid = ref(Number(periodStore.getPeriodRange().end));
const periodData = ref<IPeriodData[]>([]);
watch(
    () => props.periodDateList,
    () => {
        periodData.value = props.periodDateList.map((item) => {
            return {
                year: item.year,
                sn: item.sn,
                pid: item.pid,
                time: item.year + "" + String(item.sn).padStart(2, "0"),
            }
        });
        updateRouteFateMonth();
    },
    {
        immediate: true
    }
);
function updateRouteFateMonth() {
    let result = initStartOrEndMonth(periodData.value, startPid.value, endPid.value);
    searchInfo.startMonth = result.startMonth;
    searchInfo.endMonth = result.endMonth;
}

const getActPid = (start: number, end: number) => {
    startPid.value = start;
    endPid.value = end;
}

const isErp = ref(window.isErp);

const vNumSInput = useNumberInput(searchInfo, "reactive", "txtVoucherNoStart");
const vNumEInput = useNumberInput(searchInfo, "reactive", "txtVoucherNoEnd");

watch([startPid, endPid], ([_s, _e]) => {
    periodStore.changePeriods(String(_s), String(_e));
    updateRouteFateMonth();
});

function setSearchParams(data: Partial<typeof searchInfo>) {
    Object.assign(searchInfo, data);
}

function keepOnlyNumber(value: string) {
    let val = value.trim();
    return val.replace(/[^0-9.-]/g, "");
}

const getSearchParams = (): ISearchParams => {
    return {
        description: searchInfo.txtDescription,
        asubCode: searchInfo.asubCode || "",
        startAmount: keepOnlyNumber(searchInfo.txtMoneyStart),
        endAmount: keepOnlyNumber(searchInfo.txtMoneyEnd),
        selectorType: firstSearchItem.value,
        startPId: startPid.value,
        endPId: endPid.value,
        startDate: startDate.value,
        endDate: endDate.value,
        approvalStatus: searchInfo.selectApprovalStatus,
        vgId: searchInfo.credentialWord,
        prepareBy: searchInfo.prepareBy,
        startVNum: searchInfo.txtVoucherNoStart,
        endVNum: searchInfo.txtVoucherNoEnd,
        sortColumn: searchInfo.sortColumn,
        sortType: searchInfo.sortType,
        note: searchInfo.txtNote,
        searchInfo: searchInfoTxt.value,
        onlyTempVoucher: searchInfo.onlyTempVoucher,
        aaType: searchInfo.aaType,
        aaeid: searchInfo.aaeid,
        quantityType: searchInfo.quantityType,
        fcType: searchInfo.fcType,
        asubDirection: searchInfo.direction,
    };
};
watchEffect(() => {
    const searchParams: ISearchParams = JSON.parse(window.localStorage.getItem("voucherList-searchParams-" + getGlobalToken()) || "{}");
    if (Object.keys(searchParams).length !== 0) {
        if (searchParams.sortColumn) searchInfo.sortColumn = searchParams.sortColumn;
        if (searchParams.sortType) searchInfo.sortType = searchParams.sortType;
    }
});
const btnSearch = () => {
    handleSearch();
};
const handleSearch = (needCheck?: boolean) => {
    if (firstSearchItem.value === "0") {
        if (startPid.value === 0 || endPid.value === 0) {
            ElNotify({ type: "warning", message: "亲，开始期间和结束期间不能为空哟！" });
            return;
        }
        if (startPid.value > endPid.value) {
            ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哟！" });
            return;
        }
    } else {
        if (dayjs(startDate.value).valueOf() > dayjs(endDate.value).valueOf()) {
            ElNotify({ type: "warning", message: "亲，开始日期不能晚于结束日期哟！" });
            return;
        }
        if (!startDate.value || !endDate.value) {
            ElNotify({ type: "warning", message: "亲，起止日期不能为空！" });
            return;
        }
    }

    handleClose();
    emit("handleSearch", needCheck);
};
const handleInitSearch = (needCheck?: boolean) => {
    if (startPid.value > endPid.value) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哟！" });
        return;
    }
    emit("handleInitSearch", needCheck);
};
const handleClose = () => {
    containerRef.value?.handleClose();
};
const handleReset = () => {
    searchInfo.asubCode = "";
    searchInfo.credentialWord = "";
    searchInfo.prepareBy = "";
    searchInfo.selectApprovalStatus = "-1";
    searchInfo.txtDescription = "";
    searchInfo.txtNote = "";
    searchInfo.txtMoneyStart = "";
    searchInfo.txtMoneyEnd = "";
    searchInfo.txtVoucherNoStart = "";
    searchInfo.txtVoucherNoEnd = "";
    searchInfo.sortColumn = "0";
    searchInfo.sortType = "0";
    searchInfo.onlyTempVoucher = false;
    firstSearchItem.value = "0";
    searchInfo.aaType = "";
    searchInfo.aaeid = "";
    searchInfo.quantityType = "0";
    searchInfo.fcType = "0";
    searchInfo.direction = "0";
};
function handleToNewVoucher() {
    globalWindowOpenPage("/Voucher/NewVoucher", "新增凭证");
}
const downloadTemplate = () => globalExport("/api/Voucher/GetImportTemplate");
const handleAudit = () => auditVoucherHandle("audit");
const handleUnAudit = () => auditVoucherHandle("unAudit");
const auditVoucherHandle = (type: "audit" | "unAudit") => {
    const voucherList: IVoucherModel[] = props.getSelectedvoucherList();
    if (voucherList.length === 0) {
        ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！" });
        return;
    }
    const list = voucherList.map((item) => {
        return {
            vid: item.vid,
            pid: item.pid,
        };
    });
    confirmApproveVouchers(type, list);
};
const confirmApproveVouchers = (type: "audit" | "unAudit", queryParamsList: { pid: number; vid: number }[]) => {
    const confirmMessage = type === "audit" ? "亲，确认要审核吗？" : "亲，确认要取消审核吗？";
    const url = type === "audit" ? "/api/Voucher/ApproveVouchers" : "/api/Voucher/UnApproveVouchers";
    ElConfirm(confirmMessage).then((r: any) => {
        if (r) {
            request({
                url,
                method: "post",
                headers: { "content-type": "application/json" },
                data: JSON.stringify(queryParamsList),
            })
                .then((res: any) => {
                    if (res.state != 1000 || !res.data) {
                        ElNotify({ type: "warning", message: "亲，" + type === "audit" ? "" : "取消" + "审核失败啦！请联系侧边栏客服！" });
                        return;
                    }
                    const result = res.data;
                    if (type === "audit") {
                        if (window.isErp) {
                            ElConfirm(
                                "成功：" +
                                    result.success +
                                    "，跳过：" +
                                    result.jump +
                                    "（暂存凭证或已经审核的凭证或者已经结账的凭证，将会被跳过！）",
                                true
                            );
                        } else {
                            ElConfirm(
                                "成功：" + result.success + "，跳过：" + result.jump + "（已经审核、已经结账的凭证，将会被跳过！）",
                                true
                            );
                        }
                    } else {
                        ElConfirm(
                            "成功：" + result.success + "，跳过：" + result.jump + "（没有审核的凭证或者已经结账的凭证，将会被跳过！）",
                            true
                        );
                    }
                    emit("loadData");
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "亲，" + type === "audit" ? "" : "取消" + "审核失败啦！请联系侧边栏客服！" });
                });
        }
    });
};
const errorList = ref([]);
const saveUploadFile = ref();
const uploadSuccess = (res: IResponseModel<any>, file: any) => {
    saveUploadFile.value = file;
    setCookie("autoAddAccount" + getGlobalToken(), String(autoAddAccountCheckbox.value), "d30");
    if (!autoAddAccountCheckbox.value) {
        //未勾选自动新增科目和辅助核算
        if (res.state != 1000) {
            const errMsg = res.msg ?? "导入失败";
            if (window.isErp) {
                ElNotify({ type: "warning", message: errMsg });
            } else {
                const appendMsg = '<span style="white-space:normal; word-break:break-all;">' + errMsg + "</span>";
                ElNotify({ type: "warning", message: appendMsg });
            }
            return;
        }
        ElNotify({ type: "success", message: "亲，导入成功啦！" });
        importDialogShow.value = false;
        dispatchReloadAsubAmountEvent();
        emit("importSuccess");
    } else {
        //勾选
        if (res.state === 1000) {
            ElNotify({ type: "success", message: "亲，导入成功啦！" });
            importDialogShow.value = false;
            isClearFile.value = true;
            dispatchReloadAsubAmountEvent();
            emit("importSuccess");
        } else if (res.state === 2000) {
            isClearFile.value = false;
            if (res.data.isAddAsub) {
                //需要增加一级科目
                importDialogShow.value = false;
                addFirstSubject.value = true;
                tableDataSubject.value = res.data.asubList;
            } else if (res.data.isError) {
                //错误提示列表
                importDialogShow.value = false;
                importErrorReason.value = true;
                errorList.value = res.data.errorList;
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
        }
    }
};

const handleExport = (exportOption: string) => {
    switch (exportOption) {
        case "0": {
            // 导出全部
            const params = {
                sortColumn: searchInfo.sortColumn,
                sortType: searchInfo.sortType,
                exportType: 1,
                async:!isLemonClient()
            };
            globalExportNew(window.jAccountBooksHost + "/api/Voucher/ExportAll?" + getUrlSearchParams(params));
            break;
        }
        case "1": {
            // 导出选中
            const voucherList = props.getSelectedvoucherList();
            if (voucherList.length === 0) {
                ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！" });
                return;
            }
            const pids = voucherList.map((item: any) => item.pid).join(",");
            const vids = voucherList.map((item: any) => item.vid).join(",");
            if (isHideBarcode.value) {
                globalExport("/api/Voucher/Export?" + getUrlSearchParams({ pids, vids }));
            } else {
                globalExportNew(window.jAccountBooksHost + `/api/Voucher/Export?async=${!isLemonClient()}`,'post',{ pids, vids },);
            }
            break;
        }
        case "2": {
            // 导出查询
            const params = getSearchParams();
            globalExportNew(window.jAccountBooksHost + `/api/Voucher/ExportAll?async=${!isLemonClient()}&` + getUrlSearchParams(params));
            break;
        }
    }
};
const handlePrint = () => {
    printDialogShow.value = true;
};
const directPrint = async () => {
    const voucherList = props.getSelectedvoucherList();
    if (voucherList.length === 0) {
        ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！" });
        return;
    }
    const pids = voucherList.map((item: any) => item.pid).join(",");
    const vids = voucherList.map((item: any) => item.vid).join(",");
    const printInfo = printDialogRef.value?.getPrintInfo() as { pageType: string } & Record<string, any>;
    let url = printInfo?.isSeniorPrint ? "/api/Voucher/DirectSeniorPrint?" : "/api/Voucher/DirectPrint?" ;
    if(printInfo?.isSeniorPrint){
        const check = await useVoucherSettingsStore().checkSeniorSettings(printInfo.pageType)
        if (!check) return
    }
    if (isHideBarcode.value) {
        globalDirectPrint(window.printHost + url + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams({ pids, vids }));
    } else {
        globalDirectPrint(window.printHost + url + getUrlSearchParams(printInfo), "post", { pids, vids });
    }
};
const handlePrintConfirm = (printInfo: any) => {
    const printType = printDialogRef.value?.getPrintType() as string;
    if (printType === "PrintAll") {
        if (props.paginationData.total === 0) {
            ElNotify({ type: "warning", message: "没有凭证可打印！" });
            return;
        }
        const params = { ...printInfo, ...getSearchParams() };
        globalPrint(window.printHost + "/api/Voucher/PrintAll?" + getUrlSearchParams(params));
    } else {
        const voucherList = props.getSelectedvoucherList();
        if (voucherList.length === 0) {
            ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！" });
            return;
        }
        const pids = voucherList.map((item: any) => item.pid).join(",");
        const vids = voucherList.map((item: any) => item.vid).join(",");
        if (isHideBarcode.value) {
            globalExport(window.printHost + "/api/Voucher/Print?" + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams({ pids, vids }));
        } else {
            printInfo.appasid = getGlobalToken();
            globalFormPost(window.printHost + "/api/Voucher/Print?" + getUrlSearchParams(printInfo), { pids, vids });
        }
    }
};
const seniorPrint = async (printInfo:any) =>{
    const printType = printDialogRef.value?.getPrintType() as string;
    const check = await useVoucherSettingsStore().checkSeniorSettings(printInfo.pageType)
    if(!check) return
    if (printType === "PrintAll") {
        if (props.paginationData.total === 0) {
            ElNotify({ type: "warning", message: "没有凭证可打印！" });
            return;
        }
        const params = { ...printInfo, ...getSearchParams() };
        globalPrint(window.printHost + "/api/Voucher/SeniorPrintAll?" + getUrlSearchParams(params));
    } else {
        const voucherList = props.getSelectedvoucherList();
        if (voucherList.length === 0) {
            ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！" });
            return;
        }
        const pids = voucherList.map((item: any) => item.pid).join(",");
        const vids = voucherList.map((item: any) => item.vid).join(",");
        if (isHideBarcode.value) {
            globalExport(window.printHost + "/api/Voucher/SeniorPrint?" + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams({ pids, vids }));
        } else {
            printInfo.appasid = getGlobalToken();
            globalFormPost(window.printHost + "/api/Voucher/SeniorPrint?" + getUrlSearchParams(printInfo), { pids, vids });
        }
    }
}
let hasBrekNum = false;
function checkHasBreakNum() {
    return hasBrekNum;
}
const checkBreakNum = () => {
    const params = {
        startPId: startPid.value,
        endPId: endPid.value,
        startVDate: startDate.value,
        endVDate: endDate.value,
        selectType: firstSearchItem.value,
        vgId: searchInfo.credentialWord,
    };
    request({
        url: "/api/Voucher/CheckBreakNum",
        method: "post",
        headers: { "content-type": "application/x-www-form-urlencoded" },
        data: params,
    }).then((res: any) => {
        hasBrekNum = res.data;
        if (res.data === true) {
            setTimeout(() => {
                ElNotify({ type: "warning", message: "亲，您选择的期间凭证有断号，请点击整理凭证查看！" });
            }, 3000);
        }
    });
};

watch([showQut, showFc], ([qut, fc]) => {
    columnType.value = qut && fc ? 3 : qut ? 1 : fc ? 2 : 0;
});

const printVoucherItem = async (vid: string | number, pid: string | number) => {
    const printInfo = printDialogRef.value!.getPrintInfo();
    let url = printInfo?.isSeniorPrint ? "/api/Voucher/SeniorPrint?" : "/api/Voucher/Print?" ;
    if(printInfo?.isSeniorPrint){
        const check = await useVoucherSettingsStore().checkSeniorSettings(printInfo.pageType)
        if (!check) return
    }
    if (isHideBarcode.value) {
        globalDirectPrint(window.printHost + url + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams({ pids: pid, vids: vid }));
    } else {
        globalDirectPrint(window.printHost + url + getUrlSearchParams(printInfo), "post",  { pids: pid, vids: vid });
    }
};
const setPeriodInfo = (type: "0" | "1") => {
    currentPeriodInfo.value = type === "0" ? getCurrentPeriodInfo(periodData.value, startPid.value, endPid.value) : periodInfoDate.value;
};
const changePeriodInfo = (_s: number, _e: number) => {
    startPid.value = _s;
    endPid.value = _e;
    periodStore.changePeriods(String(_s), String(_e));
};
const changePeriodData = async (_s: number, _e: number) => {
    await periodRef.value?.loadPeriodData(_s, _e);
};
const searchInfoTxt = ref("");
const handleSearchInfo = (txt: string) => {
    searchInfoTxt.value = txt;
    handleSearch();
};

let childComponentFinishCount = 0;

const handleInitSearchAction = () => {
    const unApprove = route.query.unApprove;
    const CheckOutP = route.query.CheckOutP;
    const sortVNums = route.query.sortVNums;
    const startP = route.query.startP;
    const endP = route.query.endP;
    if (CheckOutP) {
        startPid.value = Number(CheckOutP);
        endPid.value = Number(CheckOutP);
    }
    if (sortVNums) {
        searchInfo.sortColumn = "0";
    }
    if (startP != undefined && startP != null && startP != "") {
        startPid.value = Number(startP);
    }
    if (endP != undefined && endP != null && endP != "") {
        endPid.value = Number(endP);
    }
    if (unApprove) {
        searchInfo.selectApprovalStatus = unApprove + "";
        handleInitSearch(true);
        return;
    }
    handleInitSearch();
};

provide(componentFinishKey, async () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        await handleInit();
        handleInitSearchAction();
    }
});

const disabledDate = (time: Date) => {
    const now = new Date();
    const accountStartDate = dayjs(asStartDate).valueOf();
    now.setFullYear(now.getFullYear() + 5);
    return time.getTime() < accountStartDate || time.getTime() > dayjs(now).valueOf();
};

const canEdit = ref(false);
const canDeleteBatch = ref(false);
const isManager = ref(false);
const canSort = ref(false);
const canCopyAndDeleteBatch = ref(false);

const getUserNameList = async () => {
    const defaultNames: Array<string> = [];
    return await request({ url: "/api/Voucher/GetVoucherPreparedBys", method: "post" })
        .then((res: IResponseModel<Array<string>>) => {
            if (res.state !== 1000) return defaultNames;
            userNameList.value = res.data;
            return userNameList.value;
        })
        .catch(() => defaultNames);
};

const getCurrentPeriod = async () => {
    await request({ url: "/api/Period/GetCurrentPeriod", method: "post" }).then((res: IResponseModel<IPeriod>) => {
        if (res.state === 1000) {
            let item = res.data;
            const start = item.year + "-" + (item.sn + "").padStart(2, "0") + "-" + "01";
            const end = item.year + "-" + (item.sn + "").padStart(2, "0") + "-" + getDaysInMonth(item.year, item.sn);
            startDate.value = start;
            endDate.value = end;
        }
    });
};

const subjectType = ref<ISubjectItem[]>([]);
const getSubjectTypeList = () => {
    subjectType.value = getAccountSubjectTabList().map((item) => {
        return {
            label: item.label,
            value: item.asubTypeCode,
        };
    });
};

const handleInit = async () => {
    getSubjectTypeList();
    if (checkPermission(["voucher-candelete"])) {
        canDeleteBatch.value = true;
    }
    if (checkPermission(["voucher-canedit"])) {
        canEdit.value = true;
    }
    const mangerPermission = isErp.value ? "*********" : "10001";
    if (mangerPermission === useAccountSetStore().accountSet!.permission) {
        isManager.value = true;
    }
    if (checkPermission(["voucher-canclean"]) && checkPermission(["voucher-canedit"])) {
        canSort.value = true;
    }
    canCopyAndDeleteBatch.value = canEdit.value && canDeleteBatch.value;
    await getUserNameList();
    await getCurrentPeriod();
};
//自动新增科目和辅助核算
const autoAddAccountVisible = ref(false);
const autoAddAccountCheckbox = ref(false);
const handleImportDialog = () => {
    importDialogShow.value = true;
    autoAddAccountCheckbox.value = getCookie("autoAddAccount" + getGlobalToken()) === "true";
};
const isClearFile = ref(true); //是否清空选择的文件
const importErrorReason = ref(false); //错误原因
const cancleErrorReason = () => {
    isClearFile.value = true;
    importErrorReason.value = false;
};
const comfirmErrorReason = () => {
    importErrorReason.value = false;
    importDialogShow.value = true;
};
const addFirstSubject = ref(false); //增加一级科目
const loading = ref(false);
const tableDataSubject = ref();
const tableColumnsSubject = ref<Array<IColumnProps>>([
    { label: "科目编码", prop: "asubCode", minWidth: 90, align: "left", headerAlign: "left" },
    { label: "科目名称", prop: "asubName", minWidth: 100, align: "left", headerAlign: "left" },
    { slot: "asubType" },
    { slot: "direction" },
]);

const directionList = [
    { label: "借", value: 1 },
    { label: "贷", value: 2 },
];
const cancleAddFirstSubject = () => {
    isClearFile.value = true;
    addFirstSubject.value = false;
};
const comfirmAddSubject = () => {
    useLoading().enterLoading("努力导入中，请稍候...");
    request({
        url: "/api/Voucher/BatchRoot",
        method: "post",
        data: tableDataSubject.value,
    }).then((res: any) => {
        if (res.state === 1000) {
            window.dispatchEvent(new CustomEvent("reloadAccountSubjectTree"));
            useAccountSubjectStore().getAccountSubject();
            useAssistingAccountingStore().getAssistingAccounting();
            handleUploadVoucher();
        } else {
            ElNotify({ type: "warning", message: res.msg });
            useLoading().quitLoading();
        }
    });
};
const handleUploadVoucher = () => {
    const formData = new FormData();
    formData.append("file", saveUploadFile.value);
    request({
        url: "/api/Voucher/Import?autoAddAsub=true",
        data: formData,
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
        },
    })
        .then((res) => {
            if (res.state === 1000) {
                ElNotify({ type: "success", message: "导入成功" });
                isClearFile.value = true;
                emit("importSuccess");
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
        })
        .catch(() => {
            ElNotify({
                type: "warning",
                message: "亲，出错了，请刷新页面重试或联系客服",
            });
        })
        .finally(() => {
            addFirstSubject.value = false;
            useLoading().quitLoading();
        });
};

const aaTypeList = computed(() => {
    const defaultList: Array<{ label: string; value: string }> = [{ label: "全部", value: "" }];
    const assistingAccountingTypeList = useAssistingAccountingStore().assistingAccountingTypeList.map((item) => {
        return {
            label: item.aaTypeName,
            value: item.aaType.toString(),
        };
    });
    return [...defaultList, ...assistingAccountingTypeList];
});
const assistingAccountingList = computed(() => {
    return useAssistingAccountingStore().assistingAccountingList;
});
const showAAEList = ref<Array<{ value: string; label: string }>>([{ value: "", label: "全部" }]);
function handleAATypeChange(aaType: string) {
    searchInfo.aaeid = "";
    if (aaType === "") {
        showAAEList.value = [{ value: "", label: "全部" }];
        return;
    }
    const list = assistingAccountingList.value
        .filter((item) => item.aatype.toString() === aaType)
        .map((item) => {
            return {
                value: item.aaeid.toString(),
                label: item.aaname,
            };
        });
    showAAEList.value = [{ value: "", label: "全部" }, ...list];
}

function handleAATypeVisibleChange(visible: boolean) {
    if (!visible) {
        containerRef.value?.stopCloseSearchPopover();
    }
}
function handleAAVisibleChange(visible: boolean) {
    if (!visible) {
        containerRef.value?.stopCloseSearchPopover();
    }
    if (visible) {
        showAAEListDet.value = JSON.parse(JSON.stringify(showAAEList.value));
    }
}
const expendSearchContainer = ref(false);
const handleExpendSearchContainer = () => {
    expendSearchContainer.value = !expendSearchContainer.value;
    containerRef.value?.stopCloseSearchPopover();
};

defineExpose({
    printVoucherItem,
    setPeriodInfo,
    getSearchParams,
    checkBreakNum,
    changePeriodInfo,
    changePeriodData,
    getUserNameList,
    setSearchParams,
    checkHasBreakNum,
});

//拼音首字母筛选
const voucherGroupListAll = ref<any[]>([]);
const showVoucherGroupList = ref<any[]>([]);
const showUserNameListAll = ref<any[]>([]);
const showUserNameList = ref<any[]>([]);
const showAATypeList = ref<any[]>([]);
const showAAEListDet = ref<any[]>([]);
watchEffect(() => {
    //凭证字
    voucherGroupListAll.value = JSON.parse(JSON.stringify(voucherGroupList.value)); 
    voucherGroupListAll.value.unshift({
        id: "",
        isDefault: false,
        name: "",
        title: "全部",
    });
    showVoucherGroupList.value = JSON.parse(JSON.stringify(voucherGroupListAll.value));
    //制单人
    showUserNameListAll.value = userNameList.value.map((item) => {
        return {
            value: item,
            label: item,
        }
    });
    showUserNameListAll.value.unshift({
        value: "",
        label: "全部",
    });
    showUserNameList.value = JSON.parse(JSON.stringify(showUserNameListAll.value));
    //辅助核算
    showAATypeList.value = JSON.parse(JSON.stringify(aaTypeList.value));
    showAAEListDet.value = JSON.parse(JSON.stringify(showAAEList.value));
});
function vourcherGroupFilterMethod(value: string) {
    showVoucherGroupList.value = commonFilterMethod(value, voucherGroupListAll.value, 'title');
}
function userNameFilterMethod(value: string) {
    showUserNameList.value = commonFilterMethod(value, showUserNameListAll.value, 'label');
}
function aaTypeFilterMethod(value: string) {
    showAATypeList.value = commonFilterMethod(value, aaTypeList.value, 'label');
}
function aaeFilterMethod(value: string) {
    showAAEListDet.value = commonFilterMethod(value, showAAEList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Voucher/VoucherList.less";
.help-icon {
    margin-left: 5px;
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url("@/assets/Voucher/help.png") no-repeat 0 0;
    background-size: contain;
    cursor: pointer;
}
.auto-add-text {
    line-height: 30px;
}
.import-error-reason {
    .import-error-reason-tip {
        padding: 15px 20px;
        color: var(--red);
    }
    .import-error-reason-list {
        margin: 0 20px 20px;
        border: 1px solid var(--border-color);
    }
    .reason-item {
        padding: 10px 10px;
        line-height: 20px;
        border-bottom: 1px dashed var(--border-color);
        &:last-child {
            border-bottom: none;
        }
    }
}
.add-first-subject {
    .add-first-subject-tip {
        padding: 20px;
        font-size: 12px;
    }
    .tip-warning {
        color: var(--el-color-warning);
        font-size: 16px;
        top: 3px;
    }
    .add-first-subject-table {
        margin: 0 20px 10px;
    }
}
:deep(.el-select) {
    &.subject-item {
        input[type="text"] {
            border: none !important;
        }
    }
}

.import-error-reason,
.inject-table-content,
.export-content {
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.line-item-field.subject {
    :deep(.el-select) {
        .el-input__suffix {
            background-color: var(--white);
            height: 30px;
            .el-icon {
                margin-left: 0px;
            }
        }
    }
}
.first-item {
    margin-left: -4px;
}
.main-tool-right {
    :deep(.dropdown-button) {
        .show-more + ul {
            li:hover {
                background-color: var(--white);
                color: var(--font-color);
                .el-checkbox {
                    &:hover {
                        color: var(--font-color);
                    }
                }
            }
        }
        .base + ul {
            li {
                &.checked {
                    background-color: var(--white);
                    color: var(--font-color);
                }
            }
        }
    }
}

.main-tool-left {
    .search-info-container.expend .search-info-container-popover .downlist .buttons.left {
        margin-top: 0;
    }
}

.buttons {
    &.left {
        text-align: left;
        padding-right: 100px;
        position: relative;
        .pack-up {
            width: 80px;
            height: 28px;
            position: absolute;
            right: 15px;
            top: 10px;
            display: flex;
            align-items: center;
            :deep(.el-icon) {
                color: var(--link-color);
                padding-left: 5px;
            }
        }
    }
}
</style>
