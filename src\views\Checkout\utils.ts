import { formatMoney } from "@/util/format";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { VoucherEntryModel } from "@/components/Voucher/types";
import { cloneDeep, cloneDeepWith } from "lodash";

import type {
    IPeriodData,
    ICheckResultItem,
    IVoucherLine,
    IVoucherLineTemplates,
    IAccountingSubject,
    ICarryOverBack,
    IConbineIncomeRows,
    IAccountingSubjectItem,
    IFcitem,
} from "./tpyes";
import { useAccountSubjectStoreHook } from "@/store/modules/accountSubject";
export const formatPeriodData = (data: IPeriodData[]) => {
    const obj = data.reduce((acc: any, cur) => {
        const { year, ...rest } = cur;
        acc[year] ? acc[year].unshift({ year, ...rest }) : (acc[year] = [{ year, ...rest }]);
        return acc;
    }, {});
    const arr = Object.keys(obj)
        .map((key) => ({ [key]: obj[key] }))
        .reverse();
    return arr;
};

export const getNormalDate = (dateString: string) => {
    const matches = dateString.match(/\d+/);
    if (!matches) return "";
    const timestamp = matches[0];
    const date = new Date(parseFloat(timestamp));
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month}-${day}`;
};

// "2020/2/29 0:00:00" => "2020-02-29"
export const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
};

export function reverseArray<T>(array: T[]): T[] {
    return array.slice().reverse();
}

export const autoCheckOutHandlerApi = (year: number, month: number, isLast = false) => {
    return request({ url: "/api/Checkout/BatchCheckout?year=" + year + "&month=" + month + "&isLast=" + isLast, method: "post" });
};

export const autoCheckOutHandler = (checkList: IPeriodData[], successCallBack?: Function): Promise<void> => {
    return new Promise((resolve, reject) => {
        const processItem = (items: IPeriodData[]) => {
            if (items.length === 0) {
                resolve();
                return;
            }
            const item = items[0];
            autoCheckOutHandlerApi(item.year, item.sn, items.length === 1)
                .then((res: any) => {
                    if (res.state === 1000) {
                        successCallBack && successCallBack(item.pid);
                        processItem(items.slice(1));
                    } else {
                        reject({ msg: res.msg ?? res.message ?? "", pid: item.pid });
                    }
                })
                .catch((error: any) => {
                    error.pid = item.pid;
                    processItem(items.slice(1));
                });
        };

        processItem(checkList);
    });
};

export const setCheckPresetData = (loadingData: ICheckResultItem[]) => {
    const temporaryArray: any[] = [];
    loadingData.forEach((item) => {
        let checkPresetItem: any;
        switch (item.checktype) {
            case 0:
                checkPresetItem = checkSalesInfo(item);
                break;
            case 1:
                checkPresetItem = checkAccuredWagesInfo(item);
                break;
            case 8:
                checkPresetItem = checkAccuredBadDebitInfo(item);
                break;
            case 2:
                checkPresetItem = checkAccruedDepreciationInfo(item);
                break;
            case 3:
                checkPresetItem = checkDeferredPrepaidExpenseInfo(item);
                break;
            case 4:
                checkPresetItem = checkAccruedTaxInfo(item);
                break;
            case 5:
                checkPresetItem = checkUnpaidVATInfo(item);
                break;
            case 6:
                checkPresetItem = checkAccruedIncomeTaxInfo(item);
                break;
            case 20:
                checkPresetItem = checkFCAdjustInfo(item);
                break;
            case 99:
                checkPresetItem = checkCustomTemplateInfo(item);
                break;
            case 310:
                checkPresetItem = checkAccruedSalaryModule(item);
                break;
            case 320:
            case 321:
            case 322:
                checkPresetItem = checkPaySalaryModule(item); //321:发放工资第一次；322：发放工资第二次； 320：未勾选多次发放工资
                break;
        }
        if (checkPresetItem !== null) temporaryArray.push(checkPresetItem);
    });
    return temporaryArray;
};

export const getTotalMoney = (list: IVoucherLine[], type: "debit" | "credit") => {
    return list.reduce((prev: number, cur: IVoucherLine) => {
        type === "debit" ? (prev += cur.debit) : (prev += cur.credit);
        return prev;
    }, 0);
};

export enum CheckOutCardType {
    Check = 1,
    Create = 2,
    Loading = 3,
}

// checktype === 0
const checkSalesInfo = (rowData: ICheckResultItem) => {
    const income = rowData.voucherSums.mainIncome;
    const percent = rowData.voucherSums.carryOverPercent;
    const stock = rowData.voucherSums.stockTotal;
    // getSalesInfoVouche()
    rowData.cardInfo = {
        title: "结转销售成本",
        money: rowData.status === 1 ? formatMoney(rowData.voucherSums.mainCost || 0) || "0.00" : resetSalesMoney(rowData) || "0.00",
        otherInfo: [
            { label: "1. 本期主营业务收入", value: formatMoney(income || 0) || "0.00" },
            { label: "2. 结转百分比", value: percent + "", indexType: "ChangeSales", indexSubType: "Default" },
            { label: "3. 库存商品余额", value: formatMoney(stock || 0) || "0.00" },
            { label: "4. 综合计算结果", value: "0.00", showHelpCenter: true },
        ],
        // 1 查看凭证 蓝色  -----   2  生成凭证  绿色  有弹出层 --- 3  生成凭证  绿色  没有弹出层
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.salesCheck,
    };
    rowData.newVoucherInfo = initCreateSalesVoucher(true, rowData);
    return rowData;
};
export const resetSalesMoney = (rowData: ICheckResultItem) => {
    const income = rowData.voucherSums.mainIncome;
    const percent = rowData.voucherSums.carryOverPercent;
    const stock = rowData.voucherSums.stockTotal;
    return (income * parseFloat(percent)) / 100 > stock
        ? formatMoney(stock < 0 ? 0 : stock) || "0.00"
        : formatMoney((income * parseFloat(percent)) / 100 || 0) || "0.00";
};
export const initCreateSalesVoucher = (flag: boolean, rowData: ICheckResultItem) => {
    const accountStandard = useAccountSetStoreHook().accountSet?.accountingStandard as number;
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    const indexCode = accountStandard === 1 ? "5401" : "6401";
    const indexCode2 = "1405";
    let rowFlag1 = true;
    let rowFlag2 = true;
    if (Object.keys(voucherLineTemplates).length !== 0) {
        const costRows = voucherLineTemplates.sc;
        for (let i = 0; i < costRows.length; i++) {
            if (costRows[i].asub_code.substr(0, 4) == indexCode && rowFlag1) {
                if (flag) {
                    costRows[i].debit = rowData.cardInfo.money;
                } else {
                    costRows[i].debit = "0";
                }
                rowFlag1 = false;

                if (costRows[i].foreigncurrency === 1) {
                    costRows[i].fc_amount = toFixed(Number(costRows[i].credit) / Number(costRows[i].fc_rate), 2);
                }
            }
            if (costRows[i].asub_code.substr(0, 4) == indexCode2 && rowFlag2) {
                if (flag) {
                    costRows[i].credit = rowData.cardInfo.money;
                } else {
                    costRows[i].credit = "0";
                }
                rowFlag2 = false;
                if (costRows[i].foreigncurrency === 1) {
                    costRows[i].fc_amount = toFixed(Number(costRows[i].credit) / Number(costRows[i].fc_rate), 2);
                }
            }
            costRows[i].description = sn + "月 结转销售成本";
        }
        return costRows;
    } else {
        return [];
    }
};
// checktype === 1
const checkAccuredWagesInfo = (rowData: ICheckResultItem) => {
    const accountStandard = useAccountSetStoreHook().accountSet?.accountingStandard as number;
    rowData.cardInfo = {
        title: accountStandard == 3 || accountStandard == 4 ? "计提工资" : "计提职工薪酬",
        money: formatMoney(rowData.voucherSums.accruedWagesTotal || 0) || "0.00",
        otherInfo: [
            {
                label: accountStandard == 3 || accountStandard == 4 ? "本期应付工资余额" : "本期应付职工薪酬余额",
                value: formatMoney(rowData.voucherSums.accruedWagesTotal || 0) || "0.00",
            },
        ],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.accruedWagesCheck,
    };
    rowData.newVoucherInfo = initAccuredWagesVoucher();
    return rowData;
};
const checkAccruedSalaryModule = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: "计提工资（工资模块）",
        money: formatMoney(rowData.voucherSums.amount || 0) || "0.00",
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Loading,
        vtype: VoucherType.accruedSalaryModule,
        vtId: rowData.vtId,
    };
    rowData.newVoucherInfo = rowData.vouchers;
    rowData.hasLoading = true;
    return rowData;
};
const checkPaySalaryModule = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: rowData.checktype === 321 ?  "发放工资第一次（工资模块）" : rowData.checktype === 322 ? "发放工资第二次（工资模块）" : "发放工资（工资模块）",
        money: formatMoney(rowData.voucherSums.amount || 0) || "0.00",
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Loading,
        vtype: VoucherType.paySalaryModule,
        vtId: rowData.vtId,
    };
    rowData.newVoucherInfo = rowData.vouchers;
    rowData.hasLoading = true;
    return rowData;
};
const initAccuredWagesVoucher = () => {
    const accountStandard = useAccountSetStoreHook().accountSet?.accountingStandard as number;
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    if (Object.keys(voucherLineTemplates).length !== 0) {
        const awTemp = voucherLineTemplates.aw;
        for (let i = 0; i < awTemp.length; i++) {
            awTemp[i].description = accountStandard == 3 || accountStandard === 4 ? sn + "月  计提工资" : sn + "月  计提职工薪酬";
        }
        return awTemp;
    } else {
        return [];
    }
};
// checktype === 8
const checkAccuredBadDebitInfo = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: "计提坏账",
        money: formatMoney(rowData.voucherSums.accruedBadDebitTotal || 0) || "0.00",
        otherInfo: [{ label: "本期计提坏账", value: formatMoney(rowData.voucherSums.accruedBadDebitTotal || 0) || "0.00" }],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.accuredBadDebit,
    };
    rowData.newVoucherInfo = initAccuredBadDebitVoucher();
    return rowData;
};
const initAccuredBadDebitVoucher = () => {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    if (Object.keys(voucherLineTemplates).length !== 0) {
        const rows = voucherLineTemplates.ab;
        for (let i = 0; i < rows.length; i++) {
            rows[i].description = sn + "月  计提坏账";
        }
        return rows;
    } else {
        return [];
    }
};
// checktype === 2
const checkAccruedDepreciationInfo = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: "计提折旧",
        money: formatMoney(rowData.voucherSums.accruedDepreciation || 0) || "0.00",
        otherInfo: [{ label: "本期累计折旧", value: formatMoney(rowData.voucherSums.accruedDepreciation || 0) || "0.00" }],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.accruedDepreciation,
    };
    if (rowData.status === 2) return null;
    if (rowData.status === 0) {
        rowData.newVoucherInfo = initAccruedDepreciationVoucher();
    } else if (rowData.status === 1) {
        rowData.newVoucherInfo = initAccruedDepreciationVoucher();
    } else {
        const backData = cloneDeepWith(rowData);
        rowData.newVoucherInfo = dealwithAccruedDepreciationCreateVoucher(backData);
    }
    return rowData;
};

function initAccruedDepreciationVoucher() {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    const rows = voucherLineTemplates.ad;
    for (let i = 0; i < rows.length; i++) {
        rows[i].description = sn + "月  计提折旧";
    }

    return rows;
}

function dealwithAccruedDepreciationCreateVoucher(rowData: ICheckResultItem) {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLines = rowData.vouchers[0].voucherLines;
    const rows = [];
    for (let i = 0; i < voucherLines.length; i++) {
        const stringName = voucherLines[i].asubname;
        const vline = new VoucherEntryModel();

        vline.asubCode = stringName.split(" ")[0];
        vline.asubId = voucherLines[i].asubid;
        vline.asubName = voucherLines[i].asubname;

        vline.credit = ~~formatMoney(voucherLines[i].credit).replace(/,/g, "") || 0;
        vline.debit = ~~formatMoney(voucherLines[i].debit).replace(/,/g, "") || 0;
        vline.description = sn + "月  计提折旧";
        rows.push(vline);
    }

    return rows;
}
// checktype === 3
const checkDeferredPrepaidExpenseInfo = (rowData: ICheckResultItem) => {
    const accountStandard = useAccountSetStoreHook().accountSet?.accountingStandard as number;
    rowData.cardInfo = {
        title: rowData.status === 1 ? "摊销费用" : "摊销待摊费用",
        money: formatMoney(rowData.voucherSums.deferredPrepaidExpense || 0) || "0.00",
        otherInfo: [
            {
                label: accountStandard % 3 === 0 ? "1. 待摊费用余额" : "1. 长期待摊费用余额",
                value: formatMoney(rowData.voucherSums.deferredPrepaidExpense || 0) || "0.00",
            },
            { label: "2. 上期摊销费用", value: formatMoney(rowData.voucherSums.deferredPrepaidExpenseBefore || 0) || "0.00" },
        ],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.deferredPrepaidExpense,
    };
    rowData.newVoucherInfo = initDeferredPrepaidExpenseVoucher();
    return rowData;
};
const initDeferredPrepaidExpenseVoucher = () => {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    if (Object.keys(voucherLineTemplates).length !== 0) {
        const rows = voucherLineTemplates.dp;
        for (let i = 0; i < rows.length; i++) {
            rows[i].description = sn + "月  摊销待摊费用";
        }
        return rows;
    } else {
        return [];
    }
};
// checktype === 4
const checkAccruedTaxInfo = (rowData: ICheckResultItem) => {
    const accruedTaxTotal = rowData.voucherSums.accruedTax < 0 ? 0 : rowData.voucherSums.accruedTax;
    rowData.cardInfo = {
        title: "计提税金",
        money: resetAccruedTaxMoney(rowData) || "0.00",
        otherInfo: [
            {
                label: "1. 本期应交增值税+本期应交消费税+本期应交营业税",
                value: formatMoney(accruedTaxTotal || 0) || "0.00",
            },
            {
                label: "2. 应交城市维护建设税税率",
                value: rowData.voucherSums.cityBuildTaxRate + "",
                indexType: "CalcTax",
                indexSubType: "Default",
            },
            {
                label: "3. 教育费附加税率",
                value: rowData.voucherSums.educationPlusTaxRate + "",
                indexType: "CalcTax",
                indexSubType: "Second",
            },
            {
                label: "4. 地方教育费附加税率",
                value: rowData.voucherSums.localEduPlusTaxRate + "",
                indexType: "CalcTax",
                indexSubType: "Third",
            },
        ],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.accruedTax,
    };
    rowData.newVoucherInfo = initAccruedTaxVoucher(true, rowData);
    return rowData;
};
export const initAccruedTaxVoucher = (flag: boolean, rowData: ICheckResultItem) => {
    const accountStandard = useAccountSetStoreHook().accountSet?.accountingStandard as number;
    const currentAccuedTaxData = rowData.voucherSums;
    currentAccuedTaxData.accruedTaxTotal = currentAccuedTaxData.accruedTax < 0 ? 0 : currentAccuedTaxData.accruedTax;
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    const codeLength: number[] = JSON.parse(window.localStorage.getItem("codeLength-" + getGlobalToken()) ?? "[4, 3, 2, 2]");

    const secondCodeLength = parseInt(codeLength[1] + "", 10);
    const firstAsubCodeLength = parseInt(codeLength[0] + "", 10);
    const secondAsubCodeLength = firstAsubCodeLength + secondCodeLength;
    const indexCode1 = accountStandard == 1 ? "5403" : "6403";
    const indexCode2 = "2221" + getRepeatCode(secondCodeLength - 2, "0") + "13";
    const indexCode3 = "2221" + getRepeatCode(secondCodeLength - 1, "0") + "8";
    const indexCode4 = "2221" + getRepeatCode(secondCodeLength - 2, "0") + "14";

    let flagCode1 = true;
    let flagCode2 = true;
    let flagCode3 = true;
    let flagCode4 = true;
    const totalpercent =
        parseFloat(toFixed(currentAccuedTaxData.educationPlusTaxRate, 2)) +
        parseFloat(toFixed(currentAccuedTaxData.cityBuildTaxRate, 2)) +
        parseFloat(toFixed(currentAccuedTaxData.localEduPlusTaxRate, 2));
    // 纠正由于四舍五入引起的1分钱问题
    const code2Credit = Math.round(currentAccuedTaxData.accruedTaxTotal * currentAccuedTaxData.educationPlusTaxRate) / 100;
    let code3Credit = Math.round(currentAccuedTaxData.accruedTaxTotal * currentAccuedTaxData.cityBuildTaxRate) / 100;
    const code4Credit = Math.round(currentAccuedTaxData.accruedTaxTotal * currentAccuedTaxData.localEduPlusTaxRate) / 100;

    const totalTax = Math.round(currentAccuedTaxData.accruedTaxTotal * totalpercent) / 100;
    if (Math.abs(totalTax - code2Credit - code3Credit - code4Credit) >= 0.005) {
        const fixedNumber = Math.round((totalTax - code2Credit - code3Credit - code4Credit) * 100) / 100;
        code3Credit = code3Credit + fixedNumber;
    }

    if (Object.keys(voucherLineTemplates).length !== 0) {
        const rows = voucherLineTemplates.at;
        for (let i = 0; i < rows.length; i++) {
            if (rows[i].asub_code.substr(0, firstAsubCodeLength) == indexCode1 && flagCode1) {
                if (flag) {
                    rows[i].debit = rowData.cardInfo.money;
                } else {
                    rows[i].debit = "0";
                }
                flagCode1 = false;
            }
            if (rows[i].asub_code.substr(0, secondAsubCodeLength) == indexCode2 && flagCode2) {
                if (flag) {
                    rows[i].credit = code2Credit + "";
                } else {
                    rows[i].credit = "0";
                }
                flagCode2 = false;
            }
            if (rows[i].asub_code.substr(0, secondAsubCodeLength) == indexCode3 && flagCode3) {
                if (flag) {
                    rows[i].credit = code3Credit + "";
                } else {
                    rows[i].credit = "0";
                }
                flagCode3 = false;
            }
            if (rows[i].asub_code.substr(0, secondAsubCodeLength) == indexCode4 && flagCode4) {
                if (flag) {
                    rows[i].credit = code4Credit + "";
                } else {
                    rows[i].credit = "0";
                }
                flagCode4 = false;
            }

            rows[i].description = sn + "月  计提税金";
        }
        return rows;
    } else {
        return [];
    }
};
export const resetAccruedTaxMoney = (rowData: ICheckResultItem) => {
    const currentAccuedTaxData = rowData.voucherSums;
    const totalpercent =
        (parseFloat(toFixed(currentAccuedTaxData.cityBuildTaxRate, 2)) +
            parseFloat(toFixed(currentAccuedTaxData.educationPlusTaxRate, 2)) +
            parseFloat(toFixed(currentAccuedTaxData.localEduPlusTaxRate, 2))) /
        100;
    const accruedTaxTotal = rowData.voucherSums.accruedTax < 0 ? 0 : rowData.voucherSums.accruedTax;
    const newVoucherMoney = formatMoney(accruedTaxTotal * totalpercent || 0) || "0.00";
    const checkVoucherMoney = formatMoney(rowData.voucherSums.accruedTax || 0) || "0.00";
    return rowData.status === 1 ? checkVoucherMoney : newVoucherMoney;
};
// checktype === 5
const checkUnpaidVATInfo = (rowData: ICheckResultItem) => {
    const moneyTotal = rowData.status === 1 ? rowData.voucherSums.unVAT : rowData.voucherSums.taxPayable;
    rowData.cardInfo = {
        title: "结转未交增值税",
        money: formatMoney(moneyTotal || 0) || "0.00",
        otherInfo: [{ label: "本期应交增值税贷方余额", value: formatMoney(rowData.voucherSums.taxPayable || 0) || "0.00" }],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.unpaidTax,
    };
    rowData.newVoucherInfo = initUnpaidVATVoucher(false, rowData);
    return rowData;
};
const initUnpaidVATVoucher = (flag: boolean, rowData: ICheckResultItem) => {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    const codeLength: number[] = JSON.parse(window.localStorage.getItem("codeLength-" + getGlobalToken()) ?? "[4, 3, 2, 2]");

    const secondCodeLength = parseInt(codeLength[1] + "", 10);
    const thirdCodeLength = parseInt(codeLength[2] + "", 10);
    const firstAsubCodeLength = parseInt(codeLength[0] + "", 10);
    const secondAsubCodeLength = firstAsubCodeLength + secondCodeLength;
    const thirdAsubCodeLength = secondAsubCodeLength + thirdCodeLength;
    const indexCode1 = "2221" + getRepeatCode(secondCodeLength - 1, "0") + "1" + getRepeatCode(thirdCodeLength - 1, "0") + "3";
    const indexCode2 = "2221" + getRepeatCode(secondCodeLength - 1, "0") + "2";

    let flagCode1 = true;
    let flagCode2 = true;

    if (Object.keys(voucherLineTemplates).length !== 0) {
        const rows = voucherLineTemplates.ut;
        for (let i = 0; i < rows.length; i++) {
            if (rows[i].asub_code.substr(0, thirdAsubCodeLength) == indexCode1 && flagCode1) {
                // if (flag) {
                rows[i].debit = rowData.cardInfo.money;
                // }
                flagCode1 = false;
            }
            if (rows[i].asub_code.substr(0, secondAsubCodeLength) == indexCode2 && flagCode2) {
                // if (flag) {
                rows[i].credit = rowData.cardInfo.money;
                // }
                flagCode2 = false;
            }
            rows[i].description = sn + "月  结转未交增值税";
        }
        return rows;
    } else {
        return [];
    }
};
// checktype === 6
const checkAccruedIncomeTaxInfo = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: "计提所得税",
        money: resetAccruedIncomeTaxMoney(rowData) || "0.00",
        otherInfo: [
            { label: "1. 本年累计利润总额", value: formatMoney(rowData.voucherSums.incomeTotal || 0) || "0.00" },
            { label: "2. 税率", value: rowData.voucherSums.incomeTaxRate + "", indexType: "CalcIncomeTax", indexSubType: "Default" },
            { label: "3. 应交所得税贷方累计", value: formatMoney(rowData.voucherSums.incomeTaxPayed || 0) || "0.00" },
        ],
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.accruedIncomeTax,
    };
    rowData.newVoucherInfo = initAccruedIncomeTaxVoucher(true, rowData);
    return rowData;
};
export const initAccruedIncomeTaxVoucher = (flag: boolean, rowData: ICheckResultItem) => {
    const accountStandard = useAccountSetStoreHook().accountSet?.accountingStandard as number;
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const voucherLineTemplates: IVoucherLineTemplates = JSON.parse(
        window.localStorage.getItem("voucherLineTemplates-" + getGlobalToken()) ?? "{}"
    );
    const codeLength: number[] = JSON.parse(window.localStorage.getItem("codeLength-" + getGlobalToken()) ?? "[4, 3, 2, 2]");
    const secondCodeLength = parseInt(codeLength[1] + "", 10);
    const firstAsubCodeLength = parseInt(codeLength[0] + "", 10);
    const secondAsubCodeLength = firstAsubCodeLength + secondCodeLength;
    const indexCode1 = accountStandard == 1 ? "5801" : "6801";
    const indexCode2 = "2221" + getRepeatCode(secondCodeLength - 1, "0") + "6";

    const flagCode1 = true;
    const flagCode2 = true;

    if (Object.keys(voucherLineTemplates).length !== 0) {
        const rows = voucherLineTemplates.ai;
        for (let i = 0; i < rows.length; i++) {
            if (rows[i].asub_code.substr(0, firstAsubCodeLength) == indexCode1 && flagCode1) {
                if (flag) {
                    rows[i].debit = rowData.cardInfo.money;
                } else {
                    rows[i].debit = "0";
                }
            }
            if (rows[i].asub_code.substr(0, secondAsubCodeLength) == indexCode2 && flagCode2) {
                if (flag) {
                    rows[i].credit = rowData.cardInfo.money;
                } else {
                    rows[i].credit = "0";
                }
            }
            rows[i].description = sn + "月  计提所得税";
        }
        return rows;
    } else {
        return [];
    }
};
export const resetAccruedIncomeTaxMoney = (rowData: ICheckResultItem) => {
    const valueResult = (rowData.voucherSums.incomeTotal * rowData.voucherSums.incomeTaxRate) / 100 - rowData.voucherSums.incomeTaxPayed;
    const moneyTotal = rowData.status === 1 ? rowData.voucherSums.accruedIncomeTax : valueResult < 0 ? 0 : valueResult;
    return formatMoney(moneyTotal || 0) || "0.00";
};
// checktype === 20
const checkFCAdjustInfo = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: "结转汇兑损益",
        money: resetFCAdjustInfo(rowData) || "0.00",
        otherInfo: (rowData.fcitems ?? []).map((item) => {
            return {
                label: item.code,
                value: item.rate + "",
                id: item.fcid,
            };
        }),
        // rowData.status  0create   1check
        type: rowData.status === 1 ? CheckOutCardType.Check : CheckOutCardType.Create,
        vtype: VoucherType.fcAdjust,
    };
    rowData.newVoucherInfo = initAdjustFCVoucher(false, rowData.fcitems.slice(), rowData.accountingSubjects.slice());
    if (rowData.status !== 0 && rowData.status !== 1) return null;
    rowData.suppleFcData = suppleFcData;
    return rowData;
};

const suppleFcData = (rowData: ICheckResultItem, pid: Number, callBack: Function) => {
    request({
        url: "/api/Checkout/GetFCAdjustAsubList?pid=" + pid,
        method: "post",
    }).then((res: IResponseModel<IAccountingSubjectItem[]>) => {
        rowData.accountingSubjects = res.data.map(convertIAccountingSubjectItem).slice();
        rowData.newVoucherInfo = initAdjustFCVoucher(false, rowData.fcitems.slice(), rowData.accountingSubjects.slice());
        callBack();
    });
};

export const initAdjustFCVoucher = (isListCreateFlag: boolean, fcitems: IFcitem[], accountingSubjects: IAccountingSubject[]) => {
    const rows = accountSubjects2Voucherline(fcitems, accountingSubjects);
    return rows;
};

const convertIAccountingSubjectItem = (item: IAccountingSubjectItem) => {
    const result = {} as IAccountingSubject;
    result.asubid = item.asubId;
    result.asubcode = item.asubCode;
    result.asubname = item.asubName;
    result.derection = item.direction;
    result.assistingaccounting = item.assistingAccounting + "";
    result.assistsetting = item.assistSetting;
    result.foreigncurrency = item.foreigncurrency;
    result.sumValue = item.sumValue;
    result.fcid = item.fcid;
    result.fcrate = item.fcrate;
    result.sumFcValue = item.fcsumValue;
    result.quantityaccounting = item.quantityAccounting;
    result.quantity = item.quantity;
    result.price = item.price;
    return result;
};

const accountSubjects2Voucherline = (fcitems: IFcitem[], accountingSubjects: IAccountingSubject[]) => {
    const asubList = useAccountSubjectStoreHook().accountSubjectList;
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const codeLength: number[] = JSON.parse(window.localStorage.getItem("codeLength-" + getGlobalToken()) ?? "[4, 3, 2, 2]");
    const rows = [];
    const fcItems = fcitems;
    const asubrows = accountingSubjects;
    let value = 0;
    const secondCodeLength = parseInt(codeLength[1] + "", 10);
    const asubCode1 = "5603" + getRepeatCode(secondCodeLength - 1, "0") + "3";
    const asubCode2 = "6603" + getRepeatCode(secondCodeLength - 1, "0") + "3";
    const asubCode4 = "522" + getRepeatCode(secondCodeLength - 1, "0") + "7";
    const asubCode5 = "523" + getRepeatCode(secondCodeLength - 1, "0") + "7";
    for (let i = 0; i < asubrows.length; i++) {
        const row = new VoucherEntryModel();
        row.asubId = asubrows[i].asubid;
        row.asubCode = asubrows[i].asubcode;
        row.asubName = asubrows[i].asubcode + " " + asubrows[i].asubname;
        row.asubAAName = asubrows[i].asubcode + " " + asubList.find((item) => item.asubId === asubrows[i].asubid)?.asubAAName || "";
        row.assistingAccounting = Number(asubrows[i].assistingaccounting) || 0;
        row.aacode = asubrows[i].assistsetting;
        row.quantityAccounting = asubrows[i].quantityaccounting;
        row.quantity = asubrows[i].quantity;
        row.foreigncurrency = 1;
        if (row.quantityAccounting === 1) {
            row.price = 0;
        }

        if (
            asubrows[i].asubcode.toString().indexOf(asubCode1) > -1 ||
            asubrows[i].asubcode.toString().indexOf(asubCode2) > -1 ||
            asubrows[i].asubcode.toString().indexOf("5301") > -1 ||
            asubrows[i].asubcode.toString().indexOf(asubCode4) > -1 ||
            asubrows[i].asubcode.toString().indexOf(asubCode5) > -1
        ) {
            row.foreigncurrency = 0;
            rows.push(row);
            break;
        }
        if (asubrows[i].derection === 0) {
            for (let m = 0; m < fcItems.length; m++) {
                if (asubrows[i].fcid == fcItems[m].fcid) {
                    row.fcId = asubrows[i].fcid;
                    row.fcRate = fcItems[m].rate;
                    row.credit = Number(toFixed(fcItems[m].rate * fomat2decision(asubrows[i].sumFcValue) - asubrows[i].sumValue, 2)) || 0;
                    row.fcAmount = 0;
                    value += Number(row.credit);
                    break;
                }
            }
            row.debit = 0;
        } else {
            for (let m = 0; m < fcItems.length; m++) {
                if (asubrows[i].fcid == fcItems[m].fcid) {
                    row.fcId = asubrows[i].fcid;
                    row.fcRate = fcItems[m].rate;
                    row.debit = Number(toFixed(fcItems[m].rate * fomat2decision(asubrows[i].sumFcValue) - asubrows[i].sumValue, 2)) || 0;
                    row.fcAmount = 0;
                    value -= Number(row.debit);
                    break;
                }
            }
            row.credit = 0;
        }
        row.description = sn + "月  结转汇兑损益";
        if (Number(toFixed(asubrows[i].sumFcValue * row.fcRate - asubrows[i].sumValue, 2)) === 0) {
            value += Number(asubrows[i].derection == 0 ? row.credit : row.debit);
            continue;
        }
        rows.push(row);
    }

    const tempRows = [];

    for (let i = 0; i < rows.length; i++) {
        if (
            rows[i].asubCode.toString().indexOf(asubCode1) > -1 ||
            rows[i].asubCode.toString().indexOf(asubCode2) > -1 ||
            rows[i].asubCode.toString().indexOf("5301") > -1 ||
            rows[i].asubCode.toString().indexOf(asubCode4) > -1 ||
            rows[i].asubCode.toString().indexOf(asubCode5) > -1
        ) {
            rows[i].debit = Number(toFixed(value, 2)) || 0;
            rows[i].credit = 0;
            rows[i].description = sn + "月  结转汇兑损益";
            tempRows.push(rows[i]);
        }
    }
    for (let j = 0; j < rows.length; j++) {
        if (Number(toFixed(rows[j].credit, 2)) == 0 && Number(toFixed(rows[j].debit, 2)) == 0) {
            rows.splice(j, 1);
        }
    }

    if (rows.length === 0) {
        return tempRows;
    }

    return rows.reverse();
};
export const resetFCAdjustInfo = (rowData: ICheckResultItem) => {
    if (rowData.status === 1) return formatMoney(rowData.voucherSums.fcAdjust || 0) || "0.00";
    return calcAdjustCurrcy(rowData.fcitems ?? [], rowData.accountingSubjects ?? []) || "0.00";
};

// checktype === 99
const checkCustomTemplateInfo = (rowData: ICheckResultItem) => {
    rowData.cardInfo = {
        title: rowData.vtName,
        money: "0.00",
        otherInfo: [],
        type: CheckOutCardType.Loading,
    };
    rowData.hasLoading = false;
    return rowData;
};

export const periodStatus = { NoVoucher: 0, HasVoucher: 1, ChangeOut: 2, CheckOut: 3, ReCheckOut: 4 };
export class PermissionModel {
    pid = 0;
    checkout = false;
    changeout = false;
}
export const handleSetOpPermission = (periodRows: IPeriodData[]) => {
    const opPermission: PermissionModel[] = [];
    let currentCheckIndex = -1;
    let currentChangeIndex = -1;
    for (let i = 0; i < periodRows.length; i++) {
        if (periodRows[i].status == periodStatus.CheckOut) {
            currentCheckIndex = i;
        }
        if (periodRows[i].status == periodStatus.ChangeOut) {
            currentChangeIndex = i;
        }
    }
    // 修正可结账与可结转损益的指针
    if (currentCheckIndex == -1) {
        if (currentChangeIndex == -1) {
            currentCheckIndex = 0;
            currentChangeIndex = 0;
        } else {
            currentCheckIndex = 0;
            currentChangeIndex++;
        }
    } else {
        if (currentChangeIndex == -1) {
            currentCheckIndex++;
            currentChangeIndex = currentCheckIndex;
        } else {
            currentChangeIndex++;
            currentCheckIndex++;
        }
    }
    opPermission.length = 0;
    // 为期间赋予权限
    for (let j = 0; j < periodRows.length; j++) {
        if (Number(j) < Number(currentCheckIndex)) {
            const permissions = new PermissionModel();
            permissions.pid = periodRows[j].pid;
            permissions.checkout = false;
            permissions.changeout = false;
            opPermission.push(permissions);
        }
        if (Number(j) == Number(currentCheckIndex)) {
            const permissions = new PermissionModel();
            permissions.pid = periodRows[j].pid;
            permissions.checkout = true;
            permissions.changeout = true;
            opPermission.push(permissions);
        }
        if (Number(currentChangeIndex) > Number(currentCheckIndex)) {
            if (Number(j) <= Number(currentChangeIndex) && Number(j) > Number(currentCheckIndex)) {
                const permissions = new PermissionModel();
                permissions.pid = periodRows[j].pid;
                permissions.checkout = false;
                permissions.changeout = true;
                opPermission.push(permissions);
            }
        }
    }
    window.localStorage.setItem("opPermission", JSON.stringify(opPermission));
};

function fomat2decision(v: string | number) {
    return Number(toFixed(v, 2));
}
function calcAdjustCurrcy(fcItems: any[], asubSubjects: any[]) {
    let valueadd = 0;
    let valueadd2 = 0;
    let valuesub = 0;
    let valuesub2 = 0;
    const distinctFcItems: any[] = [];
    const hasAlreadyExist = function (id: number) {
        for (const i in distinctFcItems) {
            if (distinctFcItems[i] == id) {
                return true;
            }
        }
        return false;
    };
    for (let i = 0; i < fcItems.length; i++) {
        if (hasAlreadyExist(fcItems[i].fcid)) {
            continue;
        }
        distinctFcItems.push(fcItems[i].fcid);
        for (let j = 0; j < asubSubjects.length; j++) {
            const r = asubSubjects[j];
            if (r.fcid == fcItems[i].fcid) {
                if (r.derection == 1) {
                    const tempVal = fomat2decision(r.sumFcValue) * fcItems[i].rate - fomat2decision(r.sumValue);
                    if (tempVal < 0) {
                        valuesub += fomat2decision(tempVal);
                    } else {
                        valuesub2 += fomat2decision(tempVal);
                    }
                } else {
                    const tempVal2 = fomat2decision(r.sumFcValue) * fcItems[i].rate - fomat2decision(r.sumValue);
                    if (tempVal2 < 0) {
                        valueadd += fomat2decision(tempVal2);
                    } else {
                        valueadd2 += fomat2decision(tempVal2);
                    }
                }
            }
        }
    }
    const zeroNotZero = hasNotZeroNumber(valueadd, valueadd2, valuesub, valuesub2);
    const rValue = Number(valueadd + valueadd2 - valuesub - valuesub2);
    const rv = Number(rValue.toFixed(2));
    return rv == 0 && zeroNotZero ? formatMoney(rv) + "(冲减)" : formatMoney(rv);
}

function hasNotZeroNumber(...args: any[]) {
    for (const i in args) {
        if (Number(toFixed(args[i], 2)) !== 0) {
            return true;
        }
    }
    return false;
}

const toFixed = function (value: string | number, precision: number) {
    // precision 参数的值将被赋值为 checkPrecision 函数的返回值
    precision = checkPrecision(precision, useAccountSetStoreHook().accountSet?.decimalPlace ?? 2);

    // 计算一个 10 的幂，幂的值为 precision
    const power = Math.pow(10, precision);

    // 将 value 使用 lib.unformat 函数解析为数值，然后乘以 power，再四舍五入
    // 最后再除以 power，得到一个精确到指定小数位数的数值
    return (Math.round(Number(value) * power) / power).toFixed(precision);
};

function checkPrecision(val: number, base: number) {
    val = Math.round(Math.abs(val));
    return isNaN(val) ? base : val;
}

function getRepeatCode(count: number, code: string) {
    let str = "";
    for (let i = 0; i < count; i++) {
        str += code;
    }
    return str;
}

export function dealWithCheckOutIncome(asubRowsIncome: IAccountingSubject[]) {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const rowsIncome = [];
    for (let i = 0; i < asubRowsIncome.length; i++) {
        const row = new VoucherEntryModel();
        row.asubId = asubRowsIncome[i].asubid;
        row.asubCode = asubRowsIncome[i].asubcode;
        row.asubName = asubRowsIncome[i].asubcode + " " + asubRowsIncome[i].asubname;
        row.assistingAccounting = Number(asubRowsIncome[i].assistingaccounting);
        // row.ASSISTSETTING = asubRowsIncome[i].ASSISTINGACCOUNTING == "1" ? asubRowsIncome[i].ASSISTSETTING : "";
        row.aacode = asubRowsIncome[i].assistingaccounting === "1" ? asubRowsIncome[i].assistsetting : "";
        row.quantityAccounting = asubRowsIncome[i].quantityaccounting;
        row.quantity = asubRowsIncome[i].quantity;
        row.price = asubRowsIncome[i].price;
        row.fcId = asubRowsIncome[i].fcid;
        row.fcRate = asubRowsIncome[i].fcrate;
        row.fcAmount = asubRowsIncome[i].sumFcValue;
        row.foreigncurrency = Number(asubRowsIncome[i].foreigncurrency);
        if (asubRowsIncome[i].derection == 0) {
            row.credit = asubRowsIncome[i].sumValue;
            row.debit = 0;
        } else {
            row.credit = 0;
            row.debit = asubRowsIncome[i].sumValue;
        }
        row.description = sn + "月  结转损益";
        rowsIncome.push(row);
    }

    return rowsIncome.reverse();
}

export function dealWithCheckOutExpanse(asusbRowsExpanse: IAccountingSubject[]) {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const rowsExpanse = [];
    for (let i = 0; i < asusbRowsExpanse.length; i++) {
        const row = new VoucherEntryModel();
        row.asubId = asusbRowsExpanse[i].asubid;
        row.asubCode = asusbRowsExpanse[i].asubcode;
        row.asubName = asusbRowsExpanse[i].asubcode + " " + asusbRowsExpanse[i].asubname;
        row.assistingAccounting = Number(asusbRowsExpanse[i].assistingaccounting);
        // row.ASSISTSETTING = asusbRowsExpanse[i].ASSISTINGACCOUNTING == "1" ? asusbRowsExpanse[i].ASSISTSETTING : "";
        row.aacode = asusbRowsExpanse[i].assistingaccounting === "1" ? asusbRowsExpanse[i].assistsetting : "";
        row.quantityAccounting = asusbRowsExpanse[i].quantityaccounting;
        row.quantity = asusbRowsExpanse[i].quantity;
        row.price = asusbRowsExpanse[i].price;
        row.fcId = asusbRowsExpanse[i].fcid;
        row.fcRate = asusbRowsExpanse[i].fcrate;
        row.fcAmount = asusbRowsExpanse[i].sumFcValue;
        row.foreigncurrency = Number(asusbRowsExpanse[i].foreigncurrency);
        if (asusbRowsExpanse[i].derection == 0) {
            row.credit = asusbRowsExpanse[i].sumValue;
            row.debit = 0;
        } else {
            row.credit = 0;
            row.debit = asusbRowsExpanse[i].sumValue;
        }

        //可能会有问题，如果当前结账的不是当月，就会出现问题
        row.description = sn + "月  结转损益";
        rowsExpanse.push(row);
    }

    return rowsExpanse.reverse();
}

export function conbineincomeRows(rdata: ICarryOverBack[]): IConbineIncomeRows {
    const resultRows: {
        rowsIncome?: VoucherEntryModel[];
        rowsExpanse?: VoucherEntryModel[];
        contactRows?: VoucherEntryModel[];
    } = {};
    let rowsIncome: VoucherEntryModel[] = [];
    let rowsExpanse: VoucherEntryModel[] = [];
    ///将收入损益与支出损益进行重新调整
    (function () {
        for (let j = 0; j < rdata.length; j++) {
            if (rdata[j].checkDerection == 0) {
                rowsIncome = dealWithCheckOutIncome(rdata[j].accountingSubjects);
            } else {
                rowsExpanse = dealWithCheckOutExpanse(rdata[j].accountingSubjects);
            }
        }
    })();
    resultRows.rowsIncome = [].concat(JSON.parse(JSON.stringify(rowsIncome))).reverse();
    resultRows.rowsExpanse = [].concat(JSON.parse(JSON.stringify(rowsExpanse))).reverse();
    // 判断有几行本年利润行
    const filterCodes = ["3103", "4103", "321", "331"];
    const incomeYearProfitRow = rowsIncome.filter((item) => filterCodes.includes(item.asubCode.toString().substring(0, 4))).length;
    const expanseYearProfitRow = rowsExpanse.filter((item) => filterCodes.includes(item.asubCode.toString().substring(0, 4))).length;
    if (
        resultRows.rowsExpanse.length > 0 &&
        expanseYearProfitRow < 2 &&
        !filterCodes.includes(resultRows.rowsExpanse[0].asubCode.toString().substring(0, 4))
    ) {
        resultRows.rowsExpanse.unshift(resultRows.rowsExpanse[resultRows.rowsExpanse.length - 1]);
        resultRows.rowsExpanse.splice(resultRows.rowsExpanse.length - 1, 1);
    }
    // 汇总利润分录：合并两行利润分录后删除多余的行
    (function () {
        if (incomeYearProfitRow === 1 && expanseYearProfitRow === 1) {
            let indexIncome = -1;
            let indexExpanse = -1;
            for (let i = 0; i < rowsIncome.length; i++) {
                const asubCode = rowsIncome[i].asubCode.toString().substring(0, 4);
                if (filterCodes.includes(asubCode)) {
                    indexIncome = i;
                    break;
                }
            }
            for (let j = 0; j < rowsExpanse.length; j++) {
                const asubCode = rowsExpanse[j].asubCode.toString().substring(0, 4);
                if (filterCodes.includes(asubCode)) {
                    indexExpanse = j;
                    break;
                }
            }
            if (indexIncome != -1 && indexExpanse != -1) {
                if (
                    Number(rowsIncome[indexIncome].credit) + Number(rowsIncome[indexIncome].debit) !=
                    Number(rowsExpanse[indexExpanse].credit) + Number(rowsExpanse[indexExpanse].debit)
                ) {
                    rowsIncome[indexIncome].credit = Number(rowsIncome[indexIncome].credit) + Number(rowsExpanse[indexExpanse].credit);
                    rowsIncome[indexIncome].debit = Number(rowsIncome[indexIncome].debit) + Number(rowsExpanse[indexExpanse].debit);
                    rowsExpanse.splice(indexExpanse, 1);
                }
            }
        }
    })();
    let newRowsIncome = rowsIncome.reverse();
    let newRowExpanse = rowsExpanse.reverse();
    newRowsIncome.length &&
        newRowExpanse.length &&
        incomeYearProfitRow === 1 &&
        newRowsIncome.unshift(newRowsIncome.pop() as VoucherEntryModel);

    const filterItems = (items: VoucherEntryModel[]) => {
        return items.filter((item) => !filterCodes.includes(item.asubCode.toString().substring(0, 4)));
    };

    if (incomeYearProfitRow > 1 && expanseYearProfitRow === 1) {
        newRowsIncome = filterItems(newRowsIncome);
    } else if (incomeYearProfitRow === 1 && expanseYearProfitRow > 1) {
        newRowExpanse = filterItems(newRowExpanse);
    }
    const contactRows = newRowsIncome.concat(newRowExpanse);
    (function () {
        if (incomeYearProfitRow === 1 || expanseYearProfitRow === 1) {
            for (let i = 0; i < contactRows.length; i++) {
                const asubCode = contactRows[i]?.asubCode.toString().substring(0, 4);
                if (filterCodes.includes(asubCode)) {
                    const va = Number(contactRows[i].debit) - Number(contactRows[i].credit);
                    if (va > 0) {
                        contactRows[i].credit = 0;
                        contactRows[i].debit = va;
                    }
                    if (va < 0) {
                        contactRows[i].credit = 0 - va;
                        contactRows[i].debit = 0;
                    }
                    if (va == 0) {
                        contactRows.splice(i, 1);
                    }

                    break;
                }
            }
        }
    })();
    resultRows.contactRows = contactRows.slice();
    if (
        resultRows.contactRows.length > 0 &&
        !(incomeYearProfitRow > 1 && expanseYearProfitRow > 1) &&
        filterCodes.includes(resultRows.contactRows[resultRows.contactRows.length - 1].asubCode.toString().substring(0, 4))
    ) {
        if (resultRows.contactRows[resultRows.contactRows.length - 1].debit != 0) {
            resultRows.contactRows.unshift(resultRows.contactRows[resultRows.contactRows.length - 1]);
            resultRows.contactRows.splice(resultRows.contactRows.length - 1, 1);
        }
    }
    return resultRows;
}
export enum VoucherType {
    salesCheck = 100,
    accruedWagesCheck = 110,
    accruedDepreciation = 120,
    deferredPrepaidExpense = 130,
    accruedTax = 140,
    unpaidTax = 150,
    accruedIncomeTax = 160,
    profitLossChange = 170,
    yearProfitLoss = 190,
    accuredBadDebit = 180,
    fcAdjust = 300,
    restrictedNetWorth = 402,
    onRestrictedNetWorth = 400,
    accruedSalaryModule = 500,
    paySalaryModule = 600,
}

function dealWithFolkOrUnionCheckOut(asusbRows: IAccountingSubject[], type?: 1010 | 1020): VoucherEntryModel[] {
    const sn = window.localStorage.getItem("checkoutSN-" + getGlobalToken()) ?? "";
    const rowsExpanse = [];
    for (let i = 0; i < asusbRows.length; i++) {
        const row = new VoucherEntryModel();
        row.asubId = asusbRows[i].asubid;
        row.asubCode = asusbRows[i].asubcode;
        row.asubName = asusbRows[i].asubcode + " " + asusbRows[i].asubname;
        row.assistingAccounting = Number(asusbRows[i].assistingaccounting) || 0;
        // row.ASSISTSETTING = asusbRows[i].ASSISTINGACCOUNTING == "1" ? asusbRows[i].ASSISTSETTING : "";
        row.aacode = asusbRows[i].assistingaccounting === "1" ? asusbRows[i].assistsetting : "";
        row.quantityAccounting = asusbRows[i].quantityaccounting;
        row.quantity = asusbRows[i].quantity;
        row.price = asusbRows[i].price;
        row.fcId = asusbRows[i].fcid;
        row.fcRate = asusbRows[i].fcrate;
        row.fcAmount = asusbRows[i].sumFcValue;
        row.foreigncurrency = Number(asusbRows[i].foreigncurrency) || 0;
        if (asusbRows[i].derection === 0) {
            row.credit = asusbRows[i].sumValue;
            row.debit = 0;
        } else {
            row.credit = 0;
            row.debit = asusbRows[i].sumValue;
        }
        row.description =
            type == 1010 ? sn + "月  结转非限定性净资产" : type === 1020 ? sn + "月  结转限定性净资产" : sn + "月  结转净资产";
        rowsExpanse.push(row);
    }
    return type === undefined ? rowsExpanse : rowsExpanse.reverse();
}

export function conbineRestrictedRowsUnion(rdata: ICarryOverBack[], checkDerection: 0 | 1) {
    const rowsRestrictedIncome: VoucherEntryModel[] = [];
    (function () {
        for (let j = 0; j < rdata.length; j++) {
            if (rdata[j].checkDerection === checkDerection) {
                rowsRestrictedIncome.push(...dealWithFolkOrUnionCheckOut(rdata[j].accountingSubjects));
            }
        }
    })();
    return rowsRestrictedIncome;
}

export function conbineincomeRowsUnion(rowsIncome: VoucherEntryModel[], rowsExpanse: VoucherEntryModel[]) {
    const rowsIncomeRows = cloneDeep(rowsIncome);
    const rowsExpanseRows = cloneDeep(rowsExpanse);
    const row: Array<VoucherEntryModel> = [];
    const rows = row.concat(rowsIncomeRows).concat(rowsExpanseRows.reverse());
    const contactRows = cloneDeep(rows);
    const contactRowsClone = cloneDeep(contactRows);
    const returnRows: VoucherEntryModel[] = [];
    const netWorthRows: VoucherEntryModel[] = [];
    const netWorthAsubCodes: Array<string> = ["32203", "33103", "32103"];
    const notNeedCombineAsubCodes: Array<string> = [];

    // 先合并相同的科目，需要注意的是如果是相同的结转科目借贷方向不一致但是金额互补的情况下，可能会造成合并完之后金额为0，也就会删除掉那一行凭证
    // 所以需要先循环一遍加上值，再循环一遍处理金额为0会删除该净资产类科目凭证行的情况
    (function () {
        for (let i = 0; i < contactRowsClone.length; i++) {
            const isCheckoutAsub =
                netWorthAsubCodes.findIndex((item) => contactRowsClone[i].asubCode.toString().substr(0, 5) === item) === 0;
            if (isCheckoutAsub) {
                const currentItem = contactRowsClone[i];
                const originalIndex = netWorthRows.findIndex((item) => {
                    const isSameCode = item.asubCode === currentItem.asubCode;
                    if (currentItem.assistingAccounting === 1) {
                        return isSameCode && item.aacode === currentItem.aacode;
                    }
                    return isSameCode;
                });
                if (originalIndex > -1) {
                    const originalItem = netWorthRows[originalIndex];
                    originalItem.credit = originalItem.credit + currentItem.credit;
                    originalItem.debit = originalItem.debit + currentItem.debit;
                } else {
                    netWorthRows.push(currentItem);
                }
            }
        }

        for (let j = 0; j < netWorthRows.length; j++) {
            const va = Number(netWorthRows[j].debit) - Number(netWorthRows[j].credit);
            if (va === 0) {
                notNeedCombineAsubCodes.push(netWorthRows[j].asubCode);
            }
        }
    })();

    (function () {
        for (let i = 0; i < contactRows.length; i++) {
            const currentItem = contactRows[i];
            let notNeedCombineAsubCode = false;
            if (
                notNeedCombineAsubCodes.length > 0 &&
                notNeedCombineAsubCodes.findIndex((item) => currentItem.asubCode.toString().substr(0, 5) === item) !== -1
            ) {
                notNeedCombineAsubCode = true;
            }

            const originalIndex = returnRows.findIndex((item) => {
                const isSameCode = item.asubCode === currentItem.asubCode;
                if (currentItem.assistingAccounting === 1) {
                    return isSameCode && item.aacode === currentItem.aacode;
                }
                return isSameCode;
            });
            // 不合并净资产类结转科目最后的金额为0的科目
            if (originalIndex > -1 && !notNeedCombineAsubCode) {
                const originalItem = returnRows[originalIndex];
                originalItem.credit = originalItem.credit + currentItem.credit;
                originalItem.debit = originalItem.debit + currentItem.debit;
            } else {
                returnRows.push(currentItem);
            }
        }

        for (let j = 0; j < returnRows.length; j++) {
            const va = Number(returnRows[j].debit) - Number(returnRows[j].credit);
            // 已经不存在净资产类结转科目合并后金额为0的情况了
            // 其他科目金额如果为0后可以删除
            if (va > 0) {
                returnRows[j].credit = 0;
                returnRows[j].debit = va;
            }
            if (va < 0) {
                returnRows[j].credit = 0 - va;
                returnRows[j].debit = 0;
            }
            if (va === 0) {
                returnRows.splice(j, 1);
            }
        }
    })();

    return returnRows;
}

export function conbineRestrictedRows(rdata: ICarryOverBack[]) {
    let rowsRestrictedIncome: VoucherEntryModel[] = [];
    let indexIncome = -1;
    ///将非限定性收入与费用类进行重新调整
    (function () {
        for (let j = 0; j < rdata.length; j++) {
            if (rdata[j].checkDerection === 0 && rdata[j].checktype === 10) {
                rowsRestrictedIncome = dealWithFolkOrUnionCheckOut(rdata[j].accountingSubjects, 1020);
            }
        }
    })();
    // 判断有几行限定性净资产行
    const incomeRestrictedRow = rowsRestrictedIncome.filter((item) => item.asubCode.toString().substring(0, 4) == "3102").length;
    (function () {
        if (incomeRestrictedRow === 1) {
            for (let i = 0; i < rowsRestrictedIncome.length; i++) {
                if (rowsRestrictedIncome[i].asubCode.toString().substr(0, 4) == "3102") {
                    indexIncome = i;
                    break;
                }
            }
            if (indexIncome != -1) {
                const temp = rowsRestrictedIncome[indexIncome];
                rowsRestrictedIncome.splice(indexIncome, 1);
                rowsRestrictedIncome.unshift(temp);
                // rowsRestrictedIncome.push(temp);
            }
        } else {
            rowsRestrictedIncome = rowsRestrictedIncome.reverse();
        }
    })();
    return rowsRestrictedIncome;
}
export function conbineOnRestrictedRows(rdata: ICarryOverBack[]) {
    // let rowsRestrictedIncome = [];
    let rowsOnRestrictedIncome: VoucherEntryModel[] = [];
    let rowsExpanse: VoucherEntryModel[] = [];
    ///将非限定性收入与费用类进行重新调整
    (function () {
        for (let j = 0; j < rdata.length; j++) {
            if (rdata[j].checkDerection == 0) {
                //非限定
                switch (rdata[j].checktype) {
                    case 9:
                        rowsOnRestrictedIncome = dealWithFolkOrUnionCheckOut(rdata[j].accountingSubjects, 1010);
                        break;
                }
            } else {
                rowsExpanse = dealWithFolkOrUnionCheckOut(rdata[j].accountingSubjects, 1010);
            }
        }
    })();
    // 判断有几行限定性净资产行
    const incomeOnRestrictededRow = rowsOnRestrictedIncome.filter((item) => item.asubCode.toString().substring(0, 4) == "3101").length;
    const expanseOnRestrictededRow = rowsExpanse.filter((item) => item.asubCode.toString().substring(0, 4) == "3101").length;
    // 汇总非限定性净资产分录：合并两行非限定性净资产分录后删除多余的行
    (function () {
        if (incomeOnRestrictededRow === 1 && expanseOnRestrictededRow === 1) {
            let indexIncome = -1;
            let indexExpanse = -1;
            for (let i = 0; i < rowsOnRestrictedIncome.length; i++) {
                if (rowsOnRestrictedIncome[i].asubCode.toString().substring(0, 4) == "3101") {
                    indexIncome = i;
                    break;
                }
            }
            for (let j = 0; j < rowsExpanse.length; j++) {
                if (rowsExpanse[j].asubCode.toString().substring(0, 4) == "3101") {
                    indexExpanse = j;
                    break;
                }
            }
            if (indexIncome != -1 && indexExpanse != -1) {
                if (
                    Number(rowsOnRestrictedIncome[indexIncome].credit) + Number(rowsOnRestrictedIncome[indexIncome].debit) !=
                    Number(rowsExpanse[indexExpanse].credit) + Number(rowsExpanse[indexExpanse].debit)
                ) {
                    rowsOnRestrictedIncome[indexIncome].credit =
                        Number(rowsOnRestrictedIncome[indexIncome].credit) + Number(rowsExpanse[indexExpanse].credit);
                    rowsOnRestrictedIncome[indexIncome].debit =
                        Number(rowsOnRestrictedIncome[indexIncome].debit) + Number(rowsExpanse[indexExpanse].debit);
                    rowsExpanse.splice(indexExpanse, 1);
                }
            }
        }
    })();
    let newRowsOnRestrictedIncome = rowsOnRestrictedIncome.reverse();
    let newRowsExpanse = rowsExpanse.reverse();
    newRowsOnRestrictedIncome.length &&
        incomeOnRestrictededRow === 1 &&
        newRowsOnRestrictedIncome.unshift(newRowsOnRestrictedIncome.pop() as VoucherEntryModel);
    const filterItems = (items: VoucherEntryModel[]) => {
        return items.filter((item) => item.asubCode.toString().substring(0, 4) !== "3101");
    };

    if (incomeOnRestrictededRow > 1 && expanseOnRestrictededRow === 1) {
        newRowsOnRestrictedIncome = filterItems(newRowsOnRestrictedIncome);
    } else if (incomeOnRestrictededRow === 1 && expanseOnRestrictededRow > 1) {
        newRowsExpanse = filterItems(rowsExpanse);
    }
    const contactRows = newRowsOnRestrictedIncome.concat(newRowsExpanse);

    (function () {
        if (incomeOnRestrictededRow === 1 || expanseOnRestrictededRow === 1) {
            for (let i = 0; i < contactRows.length; i++) {
                if (contactRows[i].asubCode.toString().substring(0, 4) == "3101") {
                    const va = Number(contactRows[i].debit) - Number(contactRows[i].credit);

                    if (va > 0) {
                        contactRows[i].credit = 0;
                        contactRows[i].debit = va;
                    }
                    if (va < 0) {
                        contactRows[i].credit = 0 - va;
                        contactRows[i].debit = 0;
                    }
                    if (va == 0) {
                        contactRows.splice(i, 1);
                    }
                    break;
                }
            }
        }
    })();
    return contactRows;
}

export function resetDescription(inputString: string) {
    // 使用正则表达式匹配数字，并对其进行抹零处理
    const outputString = inputString.replace(/(\d+\.\d+)(0+)?/g, (match, p1, p2) => {
        // 判断小数部分是否全是0
        if (p2 && p2.length === p2.replace(/0/g, "").length) {
            return parseFloat(p1).toFixed(0); // 如果小数部分全是0，则保留0位小数
        } else {
            return parseFloat(p1).toString(); // 否则保留原有小数位
        }
    });
    return outputString;
}
export const getVoucherIndex = (voucherListData: any[], vid: number, pid: number) => {
    let targetIndexObj = { listTotalIndex: -1, index: -1 };
    voucherListData.forEach((item: any, listTotalIndex: number) => {
        const index = item.vouchers.findIndex((voucher: any) => {
            return voucher.vid === vid && voucher.pid === pid;
        });

        if (index !== -1) {
            targetIndexObj = { listTotalIndex, index };
        }
    });
    return targetIndexObj;
};
