import { ref } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import type { IResponseModel } from "@/util/service";
import {getAsubCodeLengthApi,type IAsubCodeLengthRes} from "@/api/getAsubCodeLengthApi";
export const useAsubCodeLengthStore = defineStore("asubCodeLength", () => {
    const codeLength = ref<number[]>([]);
    const allCodeLengthInfo = ref<IAsubCodeLengthRes>();
    const getAsubCodeLength = () => {
        return new Promise<IAsubCodeLengthRes>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getAsubCodeLengthApi().then((res:any) => {
                    const data = res as IResponseModel<IAsubCodeLengthRes>;
                    if (data.state === 1000) {
                        allCodeLengthInfo.value=data.data;
                        codeLength.value=data.data.codeLength;
                        resolve(data.data);
                    } else {
                        reject(data.msg);
                    }
                })
                .catch((error) => {
                    reject(error);
                });
            }
        });
    }
    return { codeLength,allCodeLengthInfo, getAsubCodeLength };
});
export function useAsubCodeLengthStoreHook() {
    return useAsubCodeLengthStore(store);
}