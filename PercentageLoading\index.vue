<template>
  <el-dialog
    modal-class="radius-dialog"
    :title="dialogTitle"
    v-model="loadingVisible"
    :width="width"
    height="auto"
    :modal="false"
    :align-center="true"
    :show-close="showClose"
    @closed="closed"
    destroy-on-close>
    <div
      class="custom-loading"
      :class="alignCenter ? 'alignCenter' : ''">
      <div
        style="font-size: 14px; font-weight: normal; line-height: 30px"
        v-if="loadingTitle">
        {{ loadingTitle }}
      </div>
      <div class="percentage">
        <el-progress
          :percentage="percentage"
          :show-text="percentageShowText"
          :stroke-width="strokeWidth"
          color="#44b449"
          style="height: 20px; margin-top: 10px" />
      </div>
      <div
        class="loading-tip"
        v-if="bottomTipMsg">
        <img
          src="@/assets/icons/warning.png"
          class="tip-icon mr-5" />
        {{ bottomTipMsg }}
      </div>
    </div>
    <template
      #footer
      v-if="showConfirmBtn">
      <div class="buttons flexEnd">
        <span
          class="button confirm"
          @click="confirm()">
          我知道了
        </span>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { useLoadingStoreHook } from "@/store/modules/loading"

  const props = withDefaults(
    defineProps<{
      close: Function
      confirm: Function
      cancel: Function
      showClose?: boolean
      percentageShowText?: boolean
      strokeWidth?: number
      dialogTitle?: string
      bottomTipMsg?: string
      showConfirmBtn?: boolean
      width: number
      alignCenter?: boolean
    }>(),
    {
      showConfirmBtn: false,
      dialogTitle: "",
      alignCenter: false,
      bottomTipMsg: "",
      strokeWidth: 16,
      width: 298,
      percentageShowText: true,
    },
  )

  const loadingVisible = computed(() => useLoadingStoreHook().loading > 0)

  const loadingTitle = toRef(useLoadingStoreHook(), "title")
  const closed = () => {
    props.cancel()
    useLoadingStoreHook().loading = 0
    loadingTitle.value = ""
  }
  const percentage = ref(10)
  setInterval(() => {
    if (percentage.value < 100) {
      percentage.value += 10
    } else {
      percentage.value = 0
    }
  }, 800)
  watch(
    () => useLoadingStoreHook().loading,
    (val) => {
      if (loadingVisible.value === false && val > 0) {
        // 从不显示变为显示
        percentage.value = 10
      }
    },
    { immediate: true },
  )
</script>

<style lang="scss" scoped>
  .custom-loading {
    transform: translateY(-4px);
    border-radius: 4px;
    background-color: var(--white);
    &.alignCenter {
      display: flex;
      flex-direction: column;
      align-items: center;
      .percentage {
        width: 350px;
      }
    }

    .loading-tip {
      margin-top: 20px;
      display: flex;
      align-items: center;
      .tip-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
  .buttons {
    &.flexEnd {
      justify-content: flex-end;
    }
  }
</style>
