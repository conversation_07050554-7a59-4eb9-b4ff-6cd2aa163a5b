<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <mainContent
                    ref="mainContentRef"
                    :asname="asname"
                    :currentPermission="currentPermission"
                    :permissionList="permissionList"
                    :accountList="accountList"
                    :trialButtonOnclick="buyPro"
                    @adduser="adduser"
                    @load-data="handleSearch"
                    @edit-permission="editPermission"
                />
            </template>
            <template #edit>
                <editContent
                    ref="editContentRef"
                    :asname="asname"
                    :roleInfoList="roleInfoList"
                    :tableData="permissionList"
                    :trialButtonOnclick="buyPro"
                    @handle-back="handleBack"
                    @add-role-info="addRoleInfo"
                    @edit-role-info="editRoleInfo"
                    @delete-role-info="deleteRoleInfo"
                    @form-changed="handleEditSlotEditting"
                />
            </template>
            <template #add>
                <addContent
                    ref="addContentRef"
                    :menuFunctionList="(menuFunctionList as MenuFunction[])"
                    @cancel-role-info="handleCancelAddSlotInfo"
                    @save-role-info="saveRoleInfo"
                    @form-changed="handleAddSlotEditting"
                />
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "Permissions",
};
</script>
<script setup lang="ts">
import { nextTick, ref, watch, computed } from "vue";
import { request } from "@/util/service";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { MenuFunction, processMenuFunctions, swapMenuFunctions } from "./utils";

import type { ITableItem, IMenuFunction, IEditRoleItem, IAccountList } from "./types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import mainContent from "./components/MainContent.vue";
import editContent from "./components/EditContent.vue";
import addContent from "./components/AddContent.vue";
import { globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { buyPro } from "@/util/proUtils";
import { getAccountList } from "@/api/getAccountList";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
// import { onMounted } from "vue";

const trialStatusStore = useTrialStatusStore();
const accountSet = useAccountSetStore().accountSet;
const asname = ref("");
const editContentRef = ref<InstanceType<typeof editContent>>();
const addContentRef = ref<InstanceType<typeof addContent>>();

const permissionList = ref<ITableItem[]>([]);
const roleInfoList = ref<any[]>([]);
const menuFunctionList = ref<IMenuFunction[]>([]);
const accountList = ref<IAccountList[]>([]);
const currentPermission = ref("");
const slots = ["main", "edit", "add"];
const currentSlot = ref("main");

const setAsname = () => (asname.value = accountSet?.asName as string);
const handleSearch = () => {
    request({ url: "/api/Permissions/List" }).then((res: any) => {
        if (res.state != 1000) return;
        permissionList.value = res.data;
    });
};
const getRoleInfoList = () => {
    request({ url: "/api/PermissionsRole/List" }).then((res: any) => {
        if (res.state == 1000) roleInfoList.value = res.data;
    });
};
const getMenuFunctionList = () => {
    request({ url: "/api/Permissions/GetMenuFunctionList", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            const list = res.data.slice();
            const rootList: IMenuFunction[] = list.filter((item: IMenuFunction) => item.parentId === 0);
            const newMenuFunctionList: MenuFunction[] = processMenuFunctions(rootList as MenuFunction[]);
            const list2 = swapMenuFunctions(newMenuFunctionList);
            menuFunctionList.value = list2;
        }
    });
};
const getCurrentPermission = () => {
    request({ url: "/api/PermissionsRole/CurrentPermission" }).then((res: any) => {
        if (res.state == 1000) currentPermission.value = res.data;
    });
};
const getAccounts = () => {
    getAccountList().then((res: any) => {
        if (res.state === 1000) {
            accountList.value = res.data.accountList;
        }
    });
};
const handleInit = () => {
    handleSearch();
    getRoleInfoList();
    getMenuFunctionList();
    getCurrentPermission();
    setAsname();
    getAccounts();
};

handleInit();

const adduser = () => {
    editContentRef.value?.changeSubmitType("New");
    currentSlot.value = "edit";
    nextTick(() => {
        initEditSlot = true;
    });
};
const editPermission = (row: ITableItem) => {
    editContentRef.value?.changeSubmitType("Edit");
    editContentRef.value?.changeTelePhoneNumber(row.mobile);
    editContentRef.value?.changePermissionType(Number(row.pid));
    editContentRef.value?.changeDisabled(true);
    currentSlot.value = "edit";
};
const addRoleInfo = () => {
    addContentRef.value?.handleInit("", "", []);
    addContentRef.value?.setEditType("New");
    currentSlot.value = "add";
    nextTick(() => {
        initAddSlot = true;
    });
};
const editRoleInfo = (roleInfo: IEditRoleItem) => {
    initEditSlot = false;
    addContentRef.value?.setEditType("Edit");
    addContentRef.value?.setDisabled(roleInfo.isDisabled);
    addContentRef.value?.handleInit(roleInfo.roleId + "", roleInfo.roleName, roleInfo.functionsState);
    currentSlot.value = "add";
    nextTick(() => {
        initAddSlot = true;
    });
};
const deleteRoleInfo = (roleId: number) => {
    roleInfoList.value = roleInfoList.value.filter((item) => item.roleId !== roleId);
};
const mainContentRef = ref();
const saveRoleInfo = () => {
    getRoleInfoList();
    handleCancelAddSlotInfo();
};
const handleBack = () => {
    handleSearch();
    resetEditSlotInit()
    currentSlot.value = "main";
};

//取消和保存权限勾选 重置AddSlot监听状态，进入EditSlot开启状态
const handleCancelAddSlotInfo = () => {
    initAddSlot = false;
    isEdittingAddContent.value = false;
    currentSlot.value = "edit";
    initEditSlot = true;
};

const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
let initEditSlot = false;
let initAddSlot = false;
const isEdittingEditContent = ref(false);
const isEdittingAddContent = ref(false);
const handleEditSlotEditting = () => {
    if (!initEditSlot) return;
    isEdittingEditContent.value = true;
};
const handleAddSlotEditting = () => {
    if (!initAddSlot) return;
    isEdittingAddContent.value = true;
};
const resetEditSlotInit = () => {
    initEditSlot = false;
    isEdittingEditContent.value = false;
};
const addOrEditSlotEditting = computed(() => {
    return isEdittingEditContent.value || isEdittingAddContent.value;
});
watch(addOrEditSlotEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.trial-content {
    padding-left: 28px;
    margin-top: 24px;
    font-size: 15px;
    color: #333333;
    line-height: 24px;
}
.trial-buttons {
    margin-top: 80px;
    padding: 10px;
    border-top: 1px solid var(--border-color);
    text-align: center;
}
</style>
