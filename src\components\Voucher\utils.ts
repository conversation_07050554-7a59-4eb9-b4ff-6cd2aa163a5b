import { getGlobalToken } from "@/util/baseInfo";
import { isLemonClient } from "@/util/lmClient";
export interface IPrintInfo {
    direction: string;
    printLoacation: string;
    isPrintUserName: boolean;
    isPrintApprovedName: boolean;
    isPrintFrontCover: boolean;
    printType: number;
    marginTop: number;
    marginLeft: number;
    marginBottom: number;
    marginRight: number;
    voucherLineCount: number;
    voucherFontSize: number;
    isPrintDirectorName: boolean;
    directorName: string;
    isPrintMakerName: boolean;
    makerName: string;
    isPrintCashierName: boolean;
    cashierName: string;
    isShowOriginalCoinAndRate: boolean;
    isShowQuantityAndUnitPrice: boolean;
    isShowAsubCode: boolean;
    isPrintFileNum: boolean;
    isShowSummaryPrint: boolean;
    summaryAsubLevel: number;
    isSummaryByDirection: boolean;
    isHideZero: boolean;
    isShowAssitItem: boolean;
    font: number;
    printFile: boolean;
    simultaneouslyPrintFileList: boolean;
    continuousFiles: boolean;
}
export interface ISeniorPrintInfo extends IPrintInfo {
    isShowPrintDate: boolean;
    isShowPageNumber: boolean;
    isShowSplitLine: boolean;
    isSplitPageByVNum: boolean;
    printDateText: string;
    isLineHeightAdaptive: boolean;
    isHideEmpty: boolean;
}
export const printInfo = {
    direction: "Z",
    printLoacation: "Center",
    isPrintUserName: true,
    isPrintApprovedName: true,
    isPrintFrontCover: false,
    printType: 1,
    marginTop: 14,
    marginLeft: 25,
    marginBottom: 15,
    marginRight: 10,
    voucherLineCount: 5,
    voucherFontSize: 10,
    isPrintDirectorName: true,
    directorName: "",
    isPrintMakerName: true,
    makerName: "",
    isPrintCashierName: true,
    cashierName: "",
    isShowOriginalCoinAndRate: true,
    isShowQuantityAndUnitPrice: true,
    isShowAsubCode: true,
    isPrintFileNum: false,
    isShowSummaryPrint: false,
    summaryAsubLevel: 1,
    isSummaryByDirection: true,
    isHideZero: true,
    isShowAssitItem: true,
    font: 0,
    printFile: false,
    simultaneouslyPrintFileList: false,
    continuousFiles: false,
};
export function copyPrintInfo(storePrintInfo: IPrintInfo | ISeniorPrintInfo, printInfo: IPrintInfo | ISeniorPrintInfo) {
    const { font = 0, printFile = false, simultaneouslyPrintFileList = false, continuousFiles = false, ...rest } = storePrintInfo;
     printInfo = {
        ...rest,
        font,
        printFile,
        simultaneouslyPrintFileList,
        continuousFiles,
    };
    return printInfo
}

export const getVoucherPrintInfo = (): IPrintInfo => {
    const storePrintInfoStr = localStorage.getItem("voucherPrintInfo");
    if (storePrintInfoStr) {
        const storePrintInfo = JSON.parse(storePrintInfoStr) as IPrintInfo;
        return copyPrintInfo(storePrintInfo, printInfo);
    }
    return printInfo;
};

export const getVoucherSeniorPrintInfo = (): ISeniorPrintInfo => {
    let seniorPrintInfo = {
        ...printInfo,
        isShowPrintDate: false,
        isShowPageNumber: false,
        isShowSplitLine: true,
        isSplitPageByVNum: false,
        printDateText: "",
        isLineHeightAdaptive: false,
        isHideEmpty: false,
    };
    const storePrintInfoStr = localStorage.getItem("voucherSeniorPrintInfo");
    if (storePrintInfoStr) {
        const storePrintInfo = JSON.parse(storePrintInfoStr) as ISeniorPrintInfo;
        seniorPrintInfo = copyPrintInfo(storePrintInfo, seniorPrintInfo) as ISeniorPrintInfo;
    } else {
        const storePrintInfoStr = localStorage.getItem("voucherPrintInfo");
        if (storePrintInfoStr) {
            const storePrintInfo = JSON.parse(storePrintInfoStr) as IPrintInfo;
            
            const tempPrintInfo = copyPrintInfo(storePrintInfo, printInfo);
            seniorPrintInfo = {
                ...tempPrintInfo,
                isShowPrintDate: false,
                isShowPageNumber: false,
                isShowSplitLine: true,
                isSplitPageByVNum: false,
                printDateText: "",
                isLineHeightAdaptive: false,
                isHideEmpty: false
            };
        }
    }
    return seniorPrintInfo;
};

export const setVoucherPrintInfo = (printInfo: IPrintInfo) => {
    localStorage.setItem("voucherPrintInfo", JSON.stringify(printInfo));
};

export const setVoucherSeniorPrintInfo = (printInfo: IPrintInfo,currentVersion?:number,settingsId?:number) => {
    localStorage.setItem("voucherSeniorPrintInfo", JSON.stringify(printInfo));
    const localPrintType = localStorage.getItem(getGlobalToken() + "-printType");
    if(settingsId === 1){
        localStorage.setItem(getGlobalToken() + "-printType", currentVersion === 1 ? "senior" : "default");
    }else{
        if(currentVersion !== undefined && localPrintType !== null){
            localStorage.setItem(getGlobalToken() + "-printType", currentVersion === 1 ? "senior" : 'default');
        }
    }

};

export const printParams = (printInfo: any) => {
    const {
        printType,
        voucherLineCount,
        direction,
        marginTop,
        marginLeft,
        isPrintFrontCover,
        printFile,
        simultaneouslyPrintFileList,
        continuousFiles,
        isShowSummaryPrint,
        summaryAsubLevel,
        isSummaryByDirection,
        isHideZero,
        isLineHeightAdaptive,
        isHideEmpty,
        isShowPrintDate,
        printDateText,
        isShowAssitItem
    } = printInfo;
    return {
        pageType: printType,
        voucherLine: voucherLineCount,
        direction,
        marginTop,
        marginLeft,
        isPrintTitlePage: isPrintFrontCover,
        isIncludeFile: printFile,
        includeManifest: simultaneouslyPrintFileList,
        isContinuePrint: continuousFiles,
        isShowSummaryPrint,
        summaryAsubLevel,
        isSummaryByDirection,
        isHideZero,
        isLineHeightAdaptive,
        isHideEmpty,
        isShowPrintDate,
        printDateText,
        isShowSummaryAssist: isShowAssitItem,
        async: !isLemonClient(),
    };
};

export class HistoryStack {
    stack: Array<string> = [];
    historyStack: Array<string> = [];
    cacheVoucherLine: number = -1;
    currentVoucherLine: number = -1;
    clearStack() {
        this.stack.length = 0;
        this.historyStack.length = 0;
    }
    pushStack(value: string) {
        this.stack.push(value);
    }
    popStack() {
        const value = this.stack.pop();
        if (value) {
            this.historyStack.push(value);
        }
        return this.stack[this.stack.length - 1] || "";
    }
    popHistory() {
        const value = this.historyStack.pop();
        if (value) {
            this.stack.push(value);
        }
        return this.stack[this.stack.length - 1] || "";
    }
}

export const reloadAsubAmountEventKey = "reloadAsubAmount";
export function dispatchReloadAsubAmountEvent() {
    window.dispatchEvent(new CustomEvent(reloadAsubAmountEventKey));
}
