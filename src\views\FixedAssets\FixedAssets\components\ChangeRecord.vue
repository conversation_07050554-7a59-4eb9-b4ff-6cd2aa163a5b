<template>
    <div class="changeRecord" style="background-color: var(--white)">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <SearchInfoContainer ref="containerRef">
                    <template v-slot:title>{{ currentPeriodInfo }}</template>
                    <div class="line-item first-item input">
                        <div class="line-item-title">会计期间：</div>
                        <div class="line-item-field vdate">
                            <DatePicker
                                v-model:startPid="startMonth"
                                v-model:endPid="endMonth"
                                :clearable="false"
                                :editable="false"
                                :dateType="'month'"
                                :value-format="'YYYYMM'"
                                :label-format="'YYYY年MM月'"
                                :disabledDateStart="disabledDate"
                                :disabledDateEnd="disabledDate"
                            />
                        </div>
                    </div>
                    <div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证日期:</div>
                            <div class="line-item-field vdate">
                                <DatePicker v-model:startPid="searchInfo.startVDate" v-model:endPid="searchInfo.endVDate" />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证字号：</div>
                            <div class="line-item-field">
                                <div class="vgid-line">
                                    <el-select v-model="searchInfo.vgId" :teleported="false" style="width: 90px" @change="handleVgIdChange">
                                        <el-option :value="0" label="请选择">请选择</el-option>
                                        <el-option :value="1" label="全部">全部</el-option>
                                        <el-option
                                            v-for="item in voucherGroup"
                                            :value="item.id"
                                            :key="item.id"
                                            :label="item.name"
                                        ></el-option>
                                    </el-select>
                                    <el-input
                                        clearable
                                        type="text"
                                        class="ml-10"
                                        v-model="searchInfo.startVNum"
                                        :disabled="!searchInfo.vgId"
                                        @input="startVNumLimit"
                                    ></el-input>
                                    <span style="padding: 0 8px; line-height: 30px">至</span>
                                    <el-input
                                        clearable
                                        type="text"
                                        v-model="searchInfo.endVNum"
                                        :disabled="!searchInfo.vgId"
                                        @input="endVNumLimit"
                                    ></el-input>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">状态：</div>
                        <div class="line-item-field">
                            <el-select 
                                v-model="searchInfo.status" 
                                :teleported="false" 
                                style="width: 122px" 
                                placeholder="请选择"
                                :filterable="true"
                                :filter-method="statusFilterMethod"
                            >
                                <el-option 
                                    :label="item.label" 
                                    :value="item.value" 
                                    v-for="item in showStatusList" 
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">变更类别：</div>
                        <div class="line-item-field">
                            <div class="jqtransform float-l">
                                <el-select 
                                    v-model="searchInfo.changetype" 
                                    :teleported="false" 
                                    style="width: 122px" 
                                    placeholder="请选择"
                                    :filterable="true"
                                    :filter-method="changeTypeFilterMethod"
                                >
                                    <el-option
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in showChangeTypeList"
                                        :key="item.value"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产编号：</div>
                        <div class="line-item-field">
                            <el-input style="width: 298px" v-model="searchInfo.fa_num" placeholder="" clearable></el-input>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产类别：</div>
                        <div class="line-item-field">
                            <div class="jqtransform float-l">
                                <el-select 
                                    :teleported="false" 
                                    v-model="searchInfo.fa_type" 
                                    style="width: 298px" 
                                    placeholder=" "
                                    :filterable="true"
                                    :filter-method="faTypeFilterMethod"
                                >
                                    <Option 
                                        v-for="item in showfaTypeList" 
                                        :key="item.value" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产属性：</div>
                        <div class="line-item-field">
                            <div class="jqtransform float-l">
                                <el-select
                                    :teleported="false"
                                    v-model="searchInfo.fa_property"
                                    style="width: 298px"
                                    placeholder="  "
                                    :fit-input-width="true"
                                    :clearable="true"
                                    :filterable="true"
                                    :filter-method="faPropertyFilterMethod"
                                >
                                    <Option 
                                        v-for="item in showfaPropertyList" 
                                        :key="item.value" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">资产名称：</div>
                        <div class="line-item-field">
                            <el-input style="width: 298px" v-model="searchInfo.fa_name" placeholder="" clearable></el-input>
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" @click="handleSearch(false)">确定</a>
                        <a class="button" @click="handleClose">返回</a>
                        <a class="button" @click="handleReset">重置</a>
                    </div>
                </SearchInfoContainer>
                <ErpRefreshButton></ErpRefreshButton>
            </div>
            <div class="main-tool-right">
                <a class="button" v-show="showSelectAll && checkPermission(['fixedassets-card-cancreatevoucher'])" @click="GetVouchers"
                    >生成凭证</a
                >
                <RefreshButton></RefreshButton>
            </div>
        </div>
        <div class="main-center">
            <Table
                ref="changeRecordTableRef"
                :data="tableData"
                :columns="columns"
                :loading="loading"
                :pageIsShow="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :current-page="paginationData.currentPage"
                :selectable="setSelectable"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @row-click="selectToggle"
                @selection-change="selectHandle"
                @refresh="handleRerefresh"
                :cell-class-name="cellClassNameFn"
                :scrollbar-show="true"
                :tableName="setModule"
            >
                <template #vgname>
                    <el-table-column 
                        label="关联凭证" 
                        align="left" 
                        headerAlign="left"
                        prop="vgname"
                        :width="getColumnWidth(setModule, 'vgname')"
                    >
                        <template #default="scope">
                            <span
                                :class="checkPermission(['fixedassets-card-cancreatevoucher']) ? 'link' : ''"
                                @click="checkPermission(['fixedassets-card-cancreatevoucher']) ? ShowDetail(scope.row) : ''"
                            >
                                {{ formatVoucherTxt(scope.row.v_date_text, scope.row.vg_name) }}
                            </span>
                        </template>
                    </el-table-column>
                </template>
                <template #operation>
                    <el-table-column label="操作" header-align="left" align="left" :resizable="false">
                        <template #default="scope">
                            <a
                                v-if="scope.row.option"
                                :class="getClassName(scope.row.option)"
                                :title="getTitle(scope.row.option)"
                                @click="handleDelete(scope.row.change_id, scope.row.option)"
                            >
                                删除
                            </a>
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed, watchEffect } from "vue";
import type {
    ISelectStrItem,
    IChangeRecordSearchInfo,
    IPeriod,
    ITableData,
    IFAChangePagingList,
    IFAChangePagingData,
    IGenerateChangeVoucher,
    IFAType,
    IAssetGenerateChangeVoucher,
} from "../types";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Table from "@/components/Table/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { getYearMonthLastDay } from "../utils";
import Option from "@/components/Option/index.vue";
import { checkPermission } from "@/util/permission";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import DatePicker from "@/components/DatePicker/index.vue";
import { faPropertyList } from "@/views/FixedAssets/utils";
import { handleZoomChange } from "../utils";
import { formatVoucherTxt } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { dayjs } from "element-plus";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const setModule = "FixedassetsChangeRecord";
const accountingStandard = useAccountSetStore().accountSet?.accountingStandard as number;
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const changeRecordTableRef = ref();
const currentPeriodInfo = ref("");
const tableMaxHeight = ref("");
const checkItems = ref<IFAChangePagingData[]>([]);
const isErp = ref(window.isErp);
const props = defineProps({
    pid: {
        type: Number,
        default: 0,
    },
    period: {
        type: Array,
        default: () => [],
    },
    month: {
        type: String,
        default: "",
    }
});
const emits = defineEmits(["goToVoucher", "handleVoucher", "getAssetsList"]);
const showSelectAll = ref(false);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const loading = ref(false);
const voucherGroupStore = useVoucherGroupStore();
const voucherGroup = voucherGroupStore.voucherGroupList;
const searchInfo = reactive<IChangeRecordSearchInfo>({
    startPid: 0,
    endPid: 0,
    startVDate: "",
    endVDate: "",
    vgId: 0,
    startVNum: "",
    endVNum: "",
    status: "",
    changetype: "",
    fa_num: "",
    fa_type: "",
    fa_property: "",
    fa_name: "",
    fa_model: "",
    department: "0",
    showInfo: false,
});
const startMonth = ref("");
const endMonth = ref("");
const statusList: ISelectStrItem[] = [
    {
        value: "1",
        label: "正常",
    },
    {
        value: "2",
        label: "停用",
    },
    {
        value: "3",
        label: "出租",
    },
    {
        value: "4",
        label: "已减少",
    },
];
const startVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.startVNum = val;
};
const endVNumLimit = (e: string) => {
    let val = e.replace(/[^\d]/g, "");
    searchInfo.endVNum = val;
};
const changeTypeList = computed(() => {
    const typeList = [
        { value: "1000", label: "新增" },
        { value: "1010", label: "原值变更" },
        { value: "1020", label: "累计折旧/摊销调整" },
        { value: "1090", label: "科目调整" },
        { value: "1050", label: "部门调整" },
        { value: "1040", label: "使用年限调整" },
        { value: "1030", label: "计提减值准备" },
        { value: "1070", label: "状态变更" },
        { value: "1080", label: "资产处置" },
    ];
    if (AccountStandard.UnionStandard === accountingStandard || AccountStandard.VillageCollectiveEconomyStandard === accountingStandard) {
        const index = typeList.findIndex((item) => item.value === "1030");
        typeList.splice(index, 1);
    }
    return typeList;
});
const handleZoomChangeList = () => {
    tableMaxHeight.value = handleZoomChange();
};
function changeMonth() {
    searchInfo.startPid = periodList.value.find((item) => item.time === startMonth.value)?.pid || 0;
    searchInfo.endPid = periodList.value.find((item) => item.time === endMonth.value)?.pid || 0;
}
const handleSearch = (isBackFirst:boolean = true) => {
    changeMonth();
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦",
        });
        return;
    }
    containerRef.value?.handleClose();
    loading.value = true;
    if (paginationData.currentPage !== 1 && isBackFirst) {
        paginationData.currentPage = 1;
        return;
    }
    const params = {
        periodS: searchInfo.startPid,
        periodE: searchInfo.endPid,
        status: searchInfo.status == "" ? "" : searchInfo.status,
        faNum: searchInfo.fa_num,
        faType: searchInfo.fa_type,
        FaProperty: searchInfo.fa_property,
        faName: searchInfo.fa_name,
        changeType: searchInfo.changetype === "" ? "" : searchInfo.changetype,
        pageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        startVDate: searchInfo.startVDate,
        endVDate: searchInfo.endVDate,
        vgId: searchInfo.vgId,
        startVNum: searchInfo.startVNum,
        endVNum: searchInfo.endVNum,
    };
    request({
        url: `/api/FAChange/PagingList?` + getUrlSearchParams(params),
    })
        .then((res: IResponseModel<IFAChangePagingList>) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                getCurrentInfoTitle();
                showSelectAll.value = false;
                res.data.data.forEach((row: IFAChangePagingData) => {
                    if (
                        (row.vp_id != 0 && row.vv_id != 0) ||
                        row.change_type_id == 1060 ||
                        row.change_type_id == 1070 ||
                        row.change_type_id == 1050 ||
                        row.change_type_id == 1040 ||
                        (parseFloat(row.value3) == 1 && row.change_type_id == 1000)
                    ) {
                        // showSelectAll.value = false;
                        row.can_select = false;
                    } else {
                        showSelectAll.value = true;
                        row.can_select = true;
                    }
                });
            }
        })
        .finally(() => (loading.value = false));
};

const handleClose = () => containerRef.value?.handleClose();

const handleReset = () => {
    searchInfo.startPid = (periodList.value as IPeriod[])[0]?.pid;
    searchInfo.endPid = (periodList.value as IPeriod[])[0]?.pid;
    searchInfo.status = "";
    searchInfo.changetype = "";
    searchInfo.fa_num = "";
    searchInfo.fa_type = "";
    searchInfo.fa_property = "";
    searchInfo.fa_name = "";
    searchInfo.startVDate = "";
    searchInfo.endVDate = "";
    searchInfo.vgId = 0;
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
    startMonth.value = periodList.value.find((item) => item.pid === searchInfo.startPid)?.time || "";
    endMonth.value = periodList.value.find((item) => item.pid === searchInfo.endPid)?.time || "";
};

const ShowDetail = async (row: ITableData) => {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.relateVoucher });
    if (isTrilExpired) {
        return;
    }
    emits("handleVoucher", row.vp_id, row.vv_id, "main");
};
const getClassName = (optionStr: string) => {
    if (optionStr)
        return (optionStr as any)
            .match(/class=['|"]?([\w+\s]+)['|"]?/g)[0]
            .split("=")[1]
            .replace(/'/g, "");
};
const getTitle = (str: string) => {
    if (str) {
        // 获取中文
        const ret = /\p{Unified_Ideograph}+/u.exec(str);
        return ret ? ret[0] : "";
    }
};

const handleDelete = (id: number, title: string) => {
    if (getClassName(title).indexOf("disabled") > -1) {
        ElConfirm((title.match(/title='(.*?)'/) as string[])[1], true, () => {}, "删除变更提示");
    } else {
        ElConfirm("此操作将删除该资产此后所有的变更记录，确定删除吗?").then((r: boolean) => {
            if (r) {
                request({
                    url: `/api/FAChange?changeId=` + id,
                    method: "delete",
                }).then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000 && res.data) {
                        getTableData();
                        emits("getAssetsList");
                    } else {
                        ElNotify({
                            type: "error",
                            message: "删除失败！",
                        });
                    }
                });
            }
        });
    }
};

function setSelectable(row: IFAChangePagingData, rowIndex: number): boolean {
    if (
        !showSelectAll.value ||
        (row.vp_id != 0 && row.vv_id != 0) ||
        row.change_type_id == 1060 ||
        row.change_type_id == 1070 ||
        row.change_type_id == 1050 ||
        row.change_type_id == 1040 ||
        (parseFloat(row.value3) == 1 && row.change_type_id == 1000)
    ) {
        return false;
    }
    return true;
}

function selectToggle(row: any, column: any, event: any) {
    if (!setSelectable(row, row.index)) return;
    if (["link el-tooltip__trigger el-tooltip__trigger", "link"].includes(event.target.className)) return;
    let selected = checkItems.value.findIndex((item: IFAChangePagingData) => item.fa_num === row.fa_num) >= 0 ? true : false;
    changeRecordTableRef.value?.getTable().toggleRowSelection(row, !selected);
}

function selectHandle(selectionItems: IFAChangePagingData[]) {
    checkItems.value = selectionItems ? selectionItems : [];
}

let periods: number[];
function checkperiod() {
    periods = [];
    checkItems.value.forEach((item: IFAChangePagingData) => {
        if (
            (item.vp_id != 0 && item.vv_id != 0) ||
            item.change_type_id == 1060 ||
            item.change_type_id == 1070 ||
            item.change_type_id == 1050 ||
            item.change_type_id == 1040 ||
            (parseFloat(item.value3) == 1 && item.change_type_id == 1000)
        ) {
            //
        } else {
            periods.push(item.p_id);
        }
    });
    let str = [];
    for (let i = 0; i < periods.length; i++) {
        //选取不重复的类别
        !RegExp(String(periods[i]), "g").test(str.join(",")) && str.push(periods[i]);
    }
    return str;
}
let checkedIds = ref<number[]>([]);
let types: number[];
function checktype() {
    types = [];
    checkedIds.value = [];
    if (!checkItems.value) {
        return types;
    } else {
        checkItems.value.forEach((item: IFAChangePagingData) => {
            if (
                (item.vp_id != 0 && item.vv_id != 0) ||
                item.change_type_id == 1060 ||
                item.change_type_id == 1070 ||
                item.change_type_id == 1050 ||
                item.change_type_id == 1040 ||
                (parseFloat(item.value3) == 1 && item.change_type_id == 1000)
            ) {
                //
            } else {
                types.push(item.change_type_id);
                checkedIds.value.push(item.change_id);
            }
        });
    }
    let str = [];
    for (let i = 0; i < types.length; i++) {
        //选取不重复的类别
        !RegExp(String(types[i]), "g").test(str.join(",")) && str.push(types[i]);
    }
    checkedIds.value = Array.from(new Set(checkedIds.value));
    return str;
}

//根据变更类别批量生成凭证
//
const voucherflag = ref(false);
function GetVouchers() {
    if (voucherflag.value) return;
    voucherflag.value = true;
    //得到类别总数
    types = checktype();
    if (types.length == 0) {
        ElNotify({
            type: "warning",
            message: "请勾选变更记录后再生成凭证！",
        });
        voucherflag.value = false;
    } else if (types.length > 1) {
        //由于凭证不支持多张同时处理，所以在此处限制只能有一张凭证生成
        ElNotify({
            type: "warning",
            message: "亲，批量生成凭证只能选择同一变更类别哦。",
        });
        voucherflag.value = false;
    } else {
        GetVoucherData();
    }
}

async function GetVoucherData() {
    periods = checkperiod();
    if (periods.length > 1) {
        //由于凭证不支持多张同时处理，所以在此处限制只能有一张凭证生成
        ElNotify({
            type: "warning",
            message: "亲，批量生成凭证只能选择同一期间数据哦。",
        });
        voucherflag.value = false;
        return;
    }
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
        return;
    }
    for (let j = 0; j < periods.length; j++) {
        for (let i = 0; i < types.length; i++) {
            // $("#Getvouchers").append("<div id='voucher" + i.toString() + "_" + j.toString() + "'></div>"); //生成多个凭证
            // var checkedItems = $("#table3").datagrid("getChecked");
            request({
                url: `/api/FAVoucher/BatchGenerateChangeVoucher?changeType=${types[i]}&index=${i}&index2=${j}&isMerge=true`,
                method: "post",
                data: checkedIds.value,
            })
                .then((res: IResponseModel<IGenerateChangeVoucher>) => {
                    const emitData: IAssetGenerateChangeVoucher = { ...res.data, changeType: types[i] };
                    const date = periodList.value!.find((item: IPeriod) => item.pid === periods[j])?.periodInfo as string;
                    const { year, month, lastDay } = getYearMonthLastDay(date);
                    const voucherData = year + "-" + (month < 10 ? "0" + month : month) + "-" + lastDay;
                    emits("goToVoucher", emitData, "批量生成变更凭证", checkedIds.value, voucherData);
                })
                .catch((err) => {
                    voucherflag.value = false;
                    console.log(err);
                });
        }
    }
}

function getCurrentInfoTitle() {
    if (searchInfo.startPid === searchInfo.endPid) {
        currentPeriodInfo.value = periodList.value?.find((item) => item.pid === searchInfo.startPid)?.periodInfo || "";
    } else {
        currentPeriodInfo.value =
            (periodList.value?.find((item) => item.pid === searchInfo.startPid)?.periodInfo || "") +
            "—" +
            (periodList.value?.find((item) => item.pid === searchInfo.endPid)?.periodInfo || "");
    }
}

const periodList = ref<IPeriod[]>([]);

const faTypeList = ref<ISelectStrItem[]>([]);
function getFaTypeApi() {
    request({
        url: `/api/FixedAssetsType/List`,
        method: "get",
    }).then((res: IResponseModel<IFAType[]>) => {
        if (res.state == 1000) {
            faTypeList.value = res.data.reduce((prev: ISelectStrItem[], item: IFAType) => {
                prev.push({
                    value: String(item.typeId),
                    label: item.typeNum + "-" + item.typeName,
                });
                return prev;
            }, []);
        }
    });
}

const columns = ref<Array<IColumnProps>>([
    { slot: "selection" },
    { label: "变动单类别", prop: "changetype", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'changetype') },
    { label: "资产编号", prop: "fa_num", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'fa_num') },
    { label: "资产名称", prop: "fa_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'fa_name') },
    {
        label: "变动前内容",
        prop: "value1",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'value1'),
        formatter(row) {
            if (row.change_type_id == 1040) {
                return row.value1.split(".")[0];
            } else {
                return row.value1;
            }
        },
    },
    {
        label: "变动后内容",
        prop: "value2",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'value2'),
        formatter(row) {
            if (row.change_type_id == 1040) {
                return row.value2.split(".")[0];
            } else {
                return row.value2;
            }
        },
    },
    { label: "变动期间", prop: "period", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'period') },
    { slot: "vgname" },
    // { label: "关联凭证", prop: "vg_name", align: "left", headerAlign: "left", useHtml: true },
    { slot: "operation" },
]);

const tableData = ref<IFAChangePagingData[]>([]);
const getTableData = () => {
    loading.value = true;
    const params = {
        periodS: searchInfo.startPid,
        periodE: searchInfo.endPid,
        pageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
    };
    request({
        url: `/api/FAChange/PagingList?` + getUrlSearchParams(params),
    })
        .then((res: IResponseModel<IFAChangePagingList>) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                !(searchInfo.startPid > searchInfo.endPid) && getCurrentInfoTitle();
                showSelectAll.value = false;
                res.data.data.forEach((row: IFAChangePagingData) => {
                    if (
                        (row.vp_id != 0 && row.vv_id != 0) ||
                        row.change_type_id == 1060 ||
                        row.change_type_id == 1070 ||
                        row.change_type_id == 1050 ||
                        row.change_type_id == 1040 ||
                        (parseFloat(row.value3) == 1 && row.change_type_id == 1000)
                    ) {
                        // $("input[name='pcl3']").eq(index).attr("style", "display:none;");
                        // showSelectAll.value = false;
                    } else {
                        showSelectAll.value = true;
                    }
                });
            }
        })
        .finally(() => (loading.value = false));
};

watch(
    () => props.period,
    () => {
        periodList.value = props.period as IPeriod[];
    },
    { immediate: true }
);
function disabledDate(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch(false);
});
watch([() => searchInfo.startVDate, () => searchInfo.endVDate], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startVDate = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endVDate = "";
    }
});
onMounted(() => {
    getFaTypeApi();
    handleZoomChangeList();
    window.addEventListener("resize", handleZoomChangeList);
});
watch(
    () => props.pid,
    (val) => {
        searchInfo.startPid = val;
        searchInfo.endPid = val;
    },
    { immediate: true }
);
watch(
    () => props.month,
    () => {
        startMonth.value = props.month;
        endMonth.value = props.month;
    },
    { immediate: true }
);
const handleVgIdChange = (val: number) => {
    if (val === 0) {
        searchInfo.startVNum = "";
        searchInfo.endVNum = "";
    }
};
const changeSearchParam = (queryData: any) => {
    handleReset();
    const { period, vdate, vnum, vgname } = queryData;
    if (period) {
        searchInfo.startPid = Number(period);
        searchInfo.endPid = Number(period);
        startMonth.value = periodList.value.find((item) => item.pid === searchInfo.startPid)?.time || "";
        endMonth.value = periodList.value.find((item) => item.pid === searchInfo.endPid)?.time || "";
    }
    if (vdate) {
        searchInfo.startVDate = vdate;
        searchInfo.endVDate = vdate;
    }
    if (vnum && vgname) {
        const targetVgid = voucherGroup.find((item) => item.name === vgname)?.id;
        searchInfo.vgId = targetVgid ?? 1;
        searchInfo.startVNum = vnum;
        searchInfo.endVNum = vnum;
    }
    handleSearch();
};
defineExpose({
    getTableData,
    voucherflag,
    startPid: () => searchInfo.startPid,
    endPid: () => searchInfo.endPid,
    changeSearchParam,
});

const cellClassNameFn = (row: { columnIndex: number; row: { status: number } }) => {
    if (row.columnIndex === 0 && row.row.status === 1) {
        return "table-column-hidden";
    }

    return "";
};

const showStatusList = ref<Array<ISelectStrItem>>([]);
const showChangeTypeList = ref<Array<any>>([]);
const showfaTypeList = ref<Array<ISelectStrItem>>([]);
const showfaPropertyList = ref<Array<any>>([]);
watchEffect(() => { 
    showStatusList.value = JSON.parse(JSON.stringify(statusList));   
    showChangeTypeList.value = JSON.parse(JSON.stringify(changeTypeList.value));   
    showfaTypeList.value = JSON.parse(JSON.stringify(faTypeList.value)); 
    showfaPropertyList.value = JSON.parse(JSON.stringify(faPropertyList)); 
});
function statusFilterMethod(value: string) {
    showStatusList.value = commonFilterMethod(value, statusList, 'label');
}
function changeTypeFilterMethod(value: string) {
    showChangeTypeList.value = commonFilterMethod(value, changeTypeList.value, 'label');
}
function faTypeFilterMethod(value: string) {
    showfaTypeList.value = commonFilterMethod(value, faTypeList.value, 'label');
}
function faPropertyFilterMethod(value: string) {
    showfaPropertyList.value = commonFilterMethod(value, faPropertyList, 'label');
}
</script>

<style scoped lang="less">
.box-main {
    border-bottom: 1px solid #dadada;
    padding: 40px 70px;
    text-align: center;
    min-height: 42px;
}
.box-footer {
    text-align: center;
    padding: 10px 0;
}
:deep(.link) {
    font-size: var(--table-body-font-size);
    line-height: var(--table-body-line-height);
    &.disabled {
        text-decoration: none;
        color: var(--border-color);
        cursor: default;
    }
}
:deep(.el-select-dropdown__list) {
    max-height: 200px;
    // overflow-y: auto;
}
:deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    & span {
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.table-column-hidden .el-checkbox__input {
    display: none;
}

.line-item-field {
    &.vdate {
        :deep(.el-input) {
            width: 122px;
        }
    }
}
</style>
