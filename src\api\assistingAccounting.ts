import { request } from "@/util/service";

export const getAssistingAccountTypeApi = () => {
    return request({
        // /api/AssistingAccountingType/list 返回固定的辅助核算列表没有手动新增，修改成FullList，若有问题可改回来
        url: "/api/AssistingAccountingType/FullList",
        method: "get",
    });
};

export const getAssistingAccountApi = (showAll = true) => {
    return request({
        url: "/api/AssistingAccounting/List?showAll=" + showAll,
        method: "get",
    });
};

export const getDepartmentApi = (onlyLeaf: boolean, showAll = true) => {
    return request({
        url: "/api/AssistingAccounting/DepartmentList?showAll=" + showAll + "&onlyLeaf=" + onlyLeaf,
        method: "get",
    });
};
export const getAAWithAcronymApi = () => {
    return request({
        url: "/api/AssistingAccounting/ListWithAcronym",
        method: "get",
    });
};
export const getAssistingAccountByAATypeApi = (aaType: number, showAll = true) => {
    return request({
        url: "/api/AssistingAccounting/ListByAAType?aaTypeId=" + aaType + "&showAll=" + showAll,
        method: "get",
    });
};
export interface IAssistingAccountWithChecked extends IAssistingAccount {
    isChecked?: boolean;
}
export interface IAssistingAccountType {
    /** asid */
    // asid: number;
    // /** 辅助核算Id */
    // aatypeId: number;
    // /** 辅助核算类别 */
    // aatypeName: string;
    // /** 是否启用 */
    // status: number;
    // /**  */
    // preName: string;

    // FullList的
    aaType: number;
    aaTypeName: string;
    column01: string;
    column02: string;
    column03: string;
    column04: string;
    column05: string;
    column06: string;
    column07: string;
    column08: string;
    option: boolean;
    rowNum: number;
}

export interface IAssistingAccount {
    /** asid */
    asid: number;
    /** 辅助核算类别 */
    aatype: number;
    /** 辅助核算目标id */
    aaeid: number;
    /** 辅助核算目标编码 */
    aanum: string;
    /** 辅助核算目标名称 */
    aaname: string;
    /**  */
    value01: string;
    /** 是否启用 */
    status: number;
    /**  */
    uscc: string;
    /** 创建人  */
    createdBy: number;
    /** 创建时间 */
    createdDate: string;
    /**  */
    preName: string;
}
export interface AAListWithAcronymItem {
    aaacronym: string;
    aaeid: number;
    aaname: string;
    aanum: string;
    aanumLen: number;
    aatype: number;
    asid: number;
    model: null | AAListWithAcronymModel;
    status: number;
}
export interface AAListWithAcronymModel {
    aa_name: string;
    aaeid: number;
    asId: number;
    endDate: null | string;
    note: string;
    startDate: null | string;
    stockModel: string;
    stockType: string;
    unit: string;
    userSn: number;
}
export enum AATypeEnum {
    IEType = 10000,
    Costumer = 10001,
    Vendor = 10002,
    Employee = 10003,
    Department = 10004,
    Project = 10005,
    Stock = 10006,
    Unit = 30006, // 10001 + 10002 + 10003
    PayMethod = 40001,
}