<template>
    <div class="main-content tabs-content">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <div class="line-item input" v-for="(asub, index) in asubs" :key="index">
                    <div class="line-item-title">默认{{ asub }}：</div>
                    <div class="line-item-field">
                        <SubjectPicker
                            v-model="searchInfo[getAsubSearchInfoKey(index)]"
                            :is-by-id="true"
                            :diyWidth="'178px'"
                            :defaultMaxWidth="155"
                            :asubImgRight="'1px'"
                            :isExpansion="true"
                            :isParentShow="false"
                            :isErpAsub="true"
                            :is-account="true"
                            :clearable="false"
                            :useOriginFilter="true"
                            :disabled="!checkPermission(['asubrelationsettings-canedit'])"
                            @change="defaultSubmit"
                        ></SubjectPicker>
                    </div>
                </div>
                <ErpRefreshButton :reload="refreshCurrent"></ErpRefreshButton>
            </div>
            <div class="mian-tool-right">
                <a class="button solid-button" v-if="checkPermission(['asubrelationsettings-canedit']) && typeCode !== 'account'" @click="showDialog">批量设置科目</a>
                <a class="button ml-10" v-permission="['asubrelationsettings-canimport']" @click="handleImport">导入</a>
            </div>
        </div>
        <div class="main-center">
            <Table
                :class="{ 'single-column': asubColumnsInfo.length === 1, 'not-init': !hasLoading }"
                :data="tableData"
                :columns="columns"
                :loading="loading"
                :page-is-show="true"
                :layout="paginationData.layout"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :currentPage="paginationData.currentPage"
                :table-name="setModule"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @cell-click="handleCellClick"
                @selection-change="handleSelectionChange"
            >
                <template #name>
                    <el-table-column
                        :label="formatFirstColumn()"
                        min-width="303px"
                        align="left"
                        header-align="left"
                        :show-overflow-tooltip="true"
                        :resizable="asubColumnsInfo.length > 1"
                    >
                        <template #default="scope">
                            <span>
                                <span :style="{ marginLeft: scope.row.parentiIds ? scope.row.parentiIds.length * 16 + 'px' : 0 }"></span>
                                {{ scope.row.name }}
                            </span>
                        </template>
                    </el-table-column>
                </template>
                <template #[item.slot] v-for="(item, index) in asubColumnsInfo" :key="index">
                    <el-table-column
                        :label="item.label"
                        min-width="303px"
                        align="left"
                        header-align="left"
                        :show-overflow-tooltip="false"
                        :width="checkColumnWidth(item.slot, index)"
                        :resizable="checkCanResize(index)"
                    >
                        <template #default="{ row, $index }: { row: DetailAsubRealation, $index: number }">
                            <template v-if="currentEditIndex === $index">
                                <div class="asub-selector-container">
                                    <SubjectPicker
                                        :ref="(el) => getRiskSpreadRef(el, index)"
                                        diyWidth="100%"
                                        subjext-picker-back-text="取消"
                                        v-model="editRowInfo[item.bindValue]"
                                        :is-by-id="true"
                                        :asubImgRight="'1px'"
                                        :isExpansion="true"
                                        :isParentShow="false"
                                        :isErpAsub="true"
                                        :is-to-body="true"
                                        :is-account="true"
                                        :expose-id-and-name="true"
                                        :dynamicWidth="true"
                                        :useOriginFilter="true"
                                        :disabled="!checkPermission(['asubrelationsettings-canedit'])"
                                        @focus="handleFocus"
                                        @blur="handleBlur"
                                        @change="(asubId: string, name: string, asubName?: string) => handleSubmitAsub(index, asubId, name, asubName)"
                                        @before-show="handleFocus"
                                    ></SubjectPicker>
                                </div>
                            </template>
                            <template v-else>
                                <div class="asub-selector-container display">
                                    <ToolTip
                                        placement="right"
                                        :teleported="true"
                                        :content="row[item.slot]"
                                        :dynamic-width="true"
                                        :line-clamp="1"
                                    >
                                        {{ row[item.slot] }}
                                    </ToolTip>
                                </div>
                            </template>
                        </template>
                    </el-table-column>
                </template>
                <template #empty>
                    <div class="empty" v-show="!loading && hasLoading">
                        <div class="row">当前账套没有{{ tabPane.replace("类别", "") }}类别，生成凭证自动取默认科目</div>
                        <div class="row">
                            您也可以点击“
                            <span class="blue" @click="routeToModulePage(typeCode, true)">
                                {{ tabPane }}
                            </span>
                            ”去维护{{ tabPane.replace("类别", "") }}类别，再来按{{ tabPane.replace("类别", "") }}类别配置科目哦~
                        </div>
                    </div>
                </template>
            </Table>
        </div>
        <el-dialog v-model="dialogShow" title="批量设置科目" class="custom-confirm" center width="440">
            <div class="dialog-content">
                <div class="dialog-main">
                    <div class="dialog-box" v-for="(asub, index) in asubs" :key="index">
                        {{ asub + "：" }}
                        <SubjectPicker
                            v-model="batchAsubInfo[getAsubBatchInfoKey(index)]"
                            :is-by-id="true"
                            :diyWidth="'220px'"
                            :asubImgRight="'1px'"
                            :isExpansion="true"
                            :isParentShow="false"
                        ></SubjectPicker>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="batchSubmit">确定</a>
                    <a class="button ml-20" @click="dialogShow = false">取消</a>
                </div>
            </div>
        </el-dialog>
        <ImportSingleFileDialog
            :importTitle="'导入科目关联设置'"
            v-model:import-show="importInfo.display"
            allowFileType=""
            :importUrl="importUrl"
            :preCheck="false"
            :isClearFile="false"
            :uploadSuccess="uploadSuccess"
            :uploadCheck="uploadCheck"
            dialogWidth="440px"
        >
            <template #download>
                <div>
                    1.点击下载模板，按照模板格式进行数据整理再导入
                    <div class="mt-20" style="display: flex; align-items: center">
                        <a class="link" @click="downloadTemplate(false)">下载{{ tabPane }}模板</a>
                        <a class="link ml-20" @click="downloadTemplate(true)">下载所有模板</a>
                        <el-popover placement="right" :width="400" trigger="hover">
                            <template #reference>
                                <span class="icon ml-5"></span>
                            </template>
                            <div class="import-tip-content">下载的模版文件会将7个基础资料分不同页签导出，可一起整理后批量导入</div>
                        </el-popover>
                    </div>
                </div>
            </template>
            <template #import-content>
                <span>2.选择导入文件</span>
            </template>
            <template #bottom-tips>
                <div class="import-tip mb-10">
                    <el-checkbox v-model="importInfo.cover">已有数据覆盖导入</el-checkbox>
                </div>
            </template>
        </ImportSingleFileDialog>
        <el-dialog title="导入科目关联设置" v-model="importInfo.errDisplay" width="440px">
            <div class="error-import-content">
                <div class="main">
                    <div class="err-title">导入结果：</div>
                    <div class="err-line">导入成功：{{ importInfo.success }}条</div>
                    <div class="err-line">
                        导入失败：{{ importInfo.fail }}条
                        <span class="ml-20 link" @click="handleDownloadErrFile">下载导入失败文件</span>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button mr-20" @click="(importInfo.errDisplay = false), (importInfo.display = true)">上一步</a>
                    <a class="button solid-button" @click="importInfo.errDisplay = false">关闭</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import ToolTip from "@/components/ToolTip/index.vue";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { AsubRelationType, AsubRelationTypeCode, IAsubRelationInfo } from "@/views/Erp/AsubRelationSettings1/utils";
import type { IAsubRelationRequestBack, DetailAsubRealation, IAsubRelationType } from "../types";

import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import { formatAsub } from "../utils";
import { checkPermission } from "@/util/permission";
import { asubRelationInfo } from "../../AsubRelationSettings1/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useScmInfoStore } from "@/store/modules/scm";
import { routeToModulePage } from "@/views/Erp/BusinessVoucherTemplate/utils";
import { ElAlert } from "@/util/confirm";

const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();
const props = defineProps<{
    scmAsid: number;
    scmProductType: number;
    defaultAsubList: Record<AsubRelationType, string>;
    typeCode: AsubRelationTypeCode;
    refreshOtherTabRequestState: (key: keyof IAsubRelationType) => void;
    requestInitAsubInfo: () => void;
    getDefaultAsubRelation: () => void;
}>();

const currentAsubRelationInfo = asubRelationInfo.find((item) => item.typeCode === props.typeCode) as IAsubRelationInfo;
const { asubs, asubRelationType: asubRelationTypes, requestType, type: tabPane } = currentAsubRelationInfo;

const setModule = computed(() => "ErpAsub-" + props.typeCode);

class AsubInfo {
    asubId1 = "";
    asubId2 = "";
    asubId3 = "";
}
type AsubNameInfo = "asubName1" | "asubName2" | "asubName3";
const searchInfo = reactive(new AsubInfo());
watch(
    () => props.defaultAsubList,
    (val) => {
        const assignInfo: any = {};
        for (let i = 0; i < asubs.length; i++) {
            assignInfo["asubId" + (i + 1)] = val[asubRelationTypes[i]];
        }
        Object.assign(searchInfo, assignInfo);
    },
    { immediate: true }
);
const loading = ref<boolean>(false);
const columns = ref<Array<IColumnProps>>([
    { slot: "selection" },
    { slot: "name" },
    { slot: "asubName1" },
    { slot: "asubName2" },
    { slot: "asubName3" },
]);
function formatFirstColumn() {
    if (props.typeCode === "account") {
        return "账户名称";
    } else if (["employee", "income", "expense"].includes(props.typeCode)) {
        return tabPane + "名称";
    } else {
        return tabPane + "类别";
    }
}
interface IAsubColumnsInfo {
    slot: AsubNameInfo;
    label: string;
    bindValue: keyof typeof editRowInfo;
}
const asubColumnsInfo = ref<Array<IAsubColumnsInfo>>([]);
function getAsubSearchInfoKey(index: number) {
    return Object.keys(searchInfo)[index] as keyof typeof searchInfo;
}
function getAsubBatchInfoKey(index: number) {
    return Object.keys(batchAsubInfo)[index] as keyof typeof batchAsubInfo;
}
function setAsubColumnsInfo() {
    const asub_columns = asubs.map((asubName, index) => {
        return {
            slot: ("asubName" + (index + 1)) as unknown as AsubNameInfo,
            label: asubName,
            bindValue: ("asubId" + (index + 1)) as keyof typeof editRowInfo,
        };
    });
    asubColumnsInfo.value = asub_columns;
}
setAsubColumnsInfo();
const tableData = ref<Array<DetailAsubRealation>>([]);
const hasLoading = ref(false);
function getTableData() {
    if (!useScmInfoStore().hasGetInfo) return;
    const data = {
        type: requestType,
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    loading.value = true;
    currentEditIndex.value = -1;
    request({ url: "/api/AsubRelation/PagingList?" + getUrlSearchParams(data) })
        .then((res: IResponseModel<IAsubRelationRequestBack>) => {
            if (res.state !== 1000) return;
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
        })
        .finally(() => {
            loading.value = false;
            hasLoading.value = true;
        });
}
function refreshCurrent() {
    props.requestInitAsubInfo();
    props.getDefaultAsubRelation();
    getTableData();
}
function defaultSubmit() {
    const data: any = {};
    for (let i = 0; i < asubs.length; i++) {
        const asub = searchInfo[getAsubSearchInfoKey(i)];
        if (!asub) {
            ElNotify({ type: "warning", message: "请选择科目" });
            return;
        }
        data[asubRelationTypes[i]] = searchInfo[getAsubSearchInfoKey(i)];
    }
    request({
        url: `/api/AsubRelation/SubmitDefaultSettings?scmAsid=${props.scmAsid}&scmProductType=${props.scmProductType}`,
        method: "post",
        data,
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({ type: "success", message: "操作成功" });
        } else {
            ElNotify({ type: "warning", message: "操作失败，请刷新重试或联系客服" });
        }
    });
}
const dialogShow = ref(false);
const batchAsubInfo = reactive(new AsubInfo());
function resetBatchAsubInfo() {
    Object.assign(batchAsubInfo, new AsubInfo());
}
function showDialog() {
    if (selection.value.length === 0) {
        ElNotify({ type: "warning", message: "亲，请先选择需要设置科目的类别哦~" });
        return;
    }
    resetBatchAsubInfo();
    dialogShow.value = true;
}
let canBatchSubmit = true;
function batchSubmit() {
    if (!canBatchSubmit) return;
    canBatchSubmit = false;
    const ids = selection.value.map((item) => item.id);
    const params = { asubIds: Object.values(batchAsubInfo).map((asub) => ~~asub), ids, type: requestType };
    const query = { scmProductType: props.scmProductType, scmAsid: props.scmAsid };
    request({ url: "/api/AsubRelation/BatchSubmitSettings?" + getUrlSearchParams(query), method: "post", data: params })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: "操作失败，请刷新重试或联系客服" });
                return;
            }
            ElNotify({ type: "success", message: "操作成功" });
            dialogShow.value = false;
            getTableData();
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "操作失败，请刷新重试或联系客服" });
        })
        .finally(() => {
            canBatchSubmit = true;
        });
}

// 检查科目是否已被选择
let canEdit = true;
function handleCheckOnlySubject(asub: string): Promise<boolean> {
  return new Promise((resolve) => {
    if (!canEdit) {
      resolve(false);
      return;
    }

    canEdit = false;

    request({ 
      url: "/api/CDAccount/CheckOnlySubject",
      method: "post",
      params: {
        asub,
        acid: dialogId.value
      }
    })
      .then((res: IResponseModel<string>) => {
        canEdit = true;
        if (res.state === 1000 && res.data === "Use") {
          ElNotify({ 
            type: "warning", 
            message: "亲，该科目已被选择" 
          });
          resolve(false);
        } else {
          resolve(true);
        }
      })
      .catch(() => {
        canEdit = true;
        resolve(false);
      });
  });
}

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableData();
});

onMounted(() => {
    getTableData();
});

const editRowInfo = reactive(new AsubInfo());
const currentEditIndex = ref(-1);
const dialogId = ref("");
const subjectPickerRefList = ref<Array<InstanceType<typeof SubjectPicker> | null>>([]);
function getRiskSpreadRef(el: any, index: number) {
    if (el) {
        subjectPickerRefList.value[index] = el;
    }
    return subjectPickerRefList.value[index];
}
function handleCellClick(row: DetailAsubRealation, column: any, cell: any, event: Event) {
    if (!asubs.includes(column.label)) return;
    const target = event.target as HTMLElement;
    const parent = target.parentNode as HTMLElement;
    if (!target || !parent) return;
    if ([target, parent].every((element) => !element.classList.contains("asub-selector-container"))) return;
    const index = tableData.value.findIndex((item) => item.id === row.id);
    currentEditIndex.value = index;
    dialogId.value = row.id.toString();
    const assignInfo = {
        asubId1: formatAsubId(row.asubId1),
        asubId2: formatAsubId(row.asubId2),
        asubId3: formatAsubId(row.asubId3),
    };
    let columnIndex = asubs.findIndex((asubName) => asubName === column.label);
    if (columnIndex === -1) columnIndex = 0;
    Object.assign(editRowInfo, assignInfo);
    nextTick().then(() => {
        subjectPickerRefList.value[columnIndex]?.focus();
    });
}
function formatAsubId(id: number | undefined) {
    return id ? String(id) : "";
}
function handleSubmitAsub(columnIndex: number, asubId: string, _name: string, asubName?: string) {
    if(props.typeCode === "account") {
        handleCheckOnlySubject(asubId).then((res) => {
            if(res) {
                submitAsub(columnIndex, asubId, _name, asubName);
            }else{
                currentEditIndex.value = -1;
            }
        });
    }else{
        submitAsub(columnIndex, asubId, _name, asubName);
    }
    function submitAsub(columnIndex: number, asubId: string, _name: string, asubName?: string) {
        const data = [formatAsub(editRowInfo.asubId1), formatAsub(editRowInfo.asubId2), formatAsub(editRowInfo.asubId3)];
        request({
            url: `/api/AsubRelation/SubmitSettings?type=${requestType}&id=${dialogId.value}`,
            method: "post",
            params: { scmProductType: props.scmProductType, scmAsid: props.scmAsid },
            data,
        }).then((res: IResponseModel<boolean>) => {
            if (res.state === 1000 && res.data) {
                const row = tableData.value[currentEditIndex.value];
                const idKey = asubColumnsInfo.value[columnIndex].bindValue;
                const nameKey = asubColumnsInfo.value[columnIndex].slot;
                row[idKey] = ~~asubId;
                row[nameKey] = asubName || _name;
                currentEditIndex.value = -1;
                ElNotify({ type: "success", message: "修改成功" });
            } else {
                currentEditIndex.value = -1;
                ElNotify({ type: "warning", message: res.msg ?? "修改失败" });
            }
        });
    }
}
let isEditing = false;
function handleFocus() {
    isEditing = true;
}
function handleBlur() {
    isEditing = false;
    const timer = setTimeout(() => {
        clearTimeout(timer);
        if (isEditing) return;
        currentEditIndex.value = -1;
    }, 250);
}
const selection = ref<Array<DetailAsubRealation>>([]);
function handleSelectionChange(val: Array<any>) {
    selection.value = val;
}
const importInfo = reactive({
    display: false,
    cover: false,
    errDisplay: false,
    success: 0,
    fail: 0,
});
enum FileType {
    Batch = 0,
    Single = 1,
    Error = 2,
}
async function CheckImportTemplate(file: any) {
    let fileType = FileType.Error as FileType;
    const formData = new FormData();
    formData.append("file", file);
    const params = { type: requestType, scmProductType: props.scmProductType, scmAsid: props.scmAsid };
    await request({
        url: "/api/AsubRelation/CheckImportTemplate?" + getUrlSearchParams(params),
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: formData,
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            fileType = res.data ? FileType.Batch : FileType.Single;
        } else {
            ElNotify({ type: "warning", message: res.msg || "导入失败，请刷新重试或联系客服" });
        }
    });
    return fileType;
}
async function handleImportConfirm() {
    let canNext = false;
    const cover = importInfo.cover ? `且勾选了<span class="highlight-red">覆盖导入</span>` : "没有勾选覆盖导入";
    const append = importInfo.cover ? "覆盖" : "追加";
    await ElAlert({
        message: `
            <div style="line-height: 25px">您选择的导入文件为<span class="highlight-red">批量导入模板</span>，${cover}</div>
            <div style="line-height: 25px">选择继续导入，则7个类别的科目关联设置数据将一起${append}导入</div>
            <div style="line-height: 25px">是否继续导入？</div>
        `,
        dialogWidth: "550px",
        options: { confirmButtonText: "继续导入", cancelButtonText: "取消" },
    }).then((r) => {
        canNext = r;
        if (!r) {
            importInfo.display = true;
        } else {
            isBatch.value = true;
        }
    });
    return canNext;
}
async function uploadCheck(file: any) {
    isBatch.value = false;
    if (!file) {
        ElNotify({ type: "warning", message: "请先选择导入文件" });
        return false;
    }
    const fileExtension = file.name.split(".").pop().toLowerCase();
    if (fileExtension !== "xls" && fileExtension !== "xlsx") {
        ElNotify({ type: "warning", message: "亲，请使用正确的文件导入~" });
        return false;
    }
    const fileType = await CheckImportTemplate(file);
    if (fileType === FileType.Error) return false;
    if (fileType === FileType.Single) return true;
    importInfo.display = false;
    const canNext = await handleImportConfirm();
    return canNext;
}
function uploadSuccess(res: IResponseModel<boolean>) {
    importInfo.display = false;
    if (res.state !== 1000 || !res.data) {
        if (res.msg && res.msg.includes("导入成功")) {
            // 部分导入
            const match = res.msg.match(/导入成功(\d+)条，导入失败(\d+)条/);
            const [, success, fail] = match || [];
            importInfo.success = ~~success;
            importInfo.fail = ~~fail;
            importInfo.errDisplay = true;
            props.refreshOtherTabRequestState(props.typeCode);
            getTableData();
        } else {
            // 全部失败
            ElNotify({ type: "warning", message: res.msg || "导入失败，请刷新重试或联系客服" });
        }
        return;
    }
    ElNotify({ type: "success", message: "导入成功" });
    props.refreshOtherTabRequestState(props.typeCode);
    getTableData();
}
function downloadTemplate(all = false) {
    const type = all ? 0 : requestType;
    const params = { type, scmProductType: props.scmProductType, scmAsid: props.scmAsid };
    globalExport("/api/AsubRelation/ExportImportTemplate?" + getUrlSearchParams(params));
}
function handleDownloadErrFile() {
    const params = { scmProductType: props.scmProductType, scmAsid: props.scmAsid };
    globalExport("/api/AsubRelation/ExportImportError?" + getUrlSearchParams(params));
}
function handleImport() {
    importInfo.display = true;
}
function checkColumnWidth(prop: string, index: number) {
    if (index === asubColumnsInfo.value.length - 1) return undefined;
    return getColumnWidth(setModule.value, prop);
}
function checkCanResize(index: number) {
    if (index === asubColumnsInfo.value.length - 1) return false;
    return undefined;
}
const isBatch = ref(false);
const importUrl = computed(() => {
    const type = importInfo.cover || isBatch.value ? 0 : requestType;
    const params = { type, isCover: importInfo.cover, scmProductType: props.scmProductType, scmAsid: props.scmAsid };
    return "/api/AsubRelation/Import?" + getUrlSearchParams(params);
});
defineExpose({ getTableData });
</script>

<style scoped lang="less">
.line-item {
    display: flex;
    height: 28px;
    line-height: 28px;
    text-align: left;
    & + .line-item {
        margin-left: 40px;
    }
    & .line-item-field {
        position: relative;
        :deep(.el-tabs__nav-scroll) {
            display: block !important;
        }
    }
}
:deep(.asub-img) {
    right: 1px;
    background-color: transparent;
}
:deep(.el-tooltip) {
    .subject-text {
        height: 40px;
        line-height: 40px;
        cursor: pointer;
    }
}
.dialog-content {
    .dialog-main {
        padding: 50px 0;
        text-align: center;
        .dialog-box {
            display: inline-block;
            position: relative;
            & + .dialog-box {
                margin-top: 10px;
            }
            :deep(.el-select-v2__placeholder) {
                text-align: left;
            }
        }
    }
}
.main-center {
    :deep(.el-table) {
        .el-select-v2 {
            input {
                border: none;
            }
        }
        .el-table__empty-block {
            font-size: 12px;
            align-items: flex-start;
        }
        .el-table__empty-text {
            width: 50%;
            padding: 16px 20px;
            margin: auto;
            background: var(--el-table-header-bg-color);
        }
        .empty {
            color: var(--font-color);
            text-align: center;
            line-height: 17px;
            font-size: 14px;
            .row + .row {
                margin-top: 12px;
            }
            .blue {
                color: var(--main-color);
                cursor: pointer;
            }
        }
    }
    .asub-selector-container {
        height: 32px;
        width: 100%;
        position: relative;
        &.display {
            line-height: 32px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
    :deep(.single-column) {
        .asub-selector-container {
            width: 50%;
        }
    }
    :deep(.not-init) {
        .el-table__empty-text {
            background: transparent !important;
        }
    }
}
.import-tip {
    margin-left: 40px;
}
.icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("@/assets/Scm/question.png") no-repeat center;
    background-size: 100% 100%;
}
.error-import-content {
    .main {
        padding: 20px 40px;
        text-align: left;
        line-height: 20px;
        .err-title {
            font-size: 16px;
            margin-bottom: 10px;
        }
        .err-line {
            padding-left: 20px;
        }
    }
    .buttons {
        padding-right: 20px;
        text-align: right;
        border-top: 1px solid var(--border-color);
    }
}
</style>
