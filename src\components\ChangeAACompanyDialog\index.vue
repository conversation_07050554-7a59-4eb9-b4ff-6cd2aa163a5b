<template>
    <el-dialog v-model="display" title="切换代账公司" :width="440">
        <div class="change-aacompany-container">
            <div class="tips">现有多个代账公司为您服务，请选择需要查账的代账公司</div>
            <div class="input">
                代账公司：
                <el-select v-model="currentAACompany">
                    <el-option v-for="item in aaCompanyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
            </div>
            <div class="buttons">
                <a @click="confirm()" class="button solid-button mr-10">确定</a>
                <a @click="display = false" class="button">取消</a>
            </div>
        </div>
    </el-dialog>
</template>
<style scoped lang="less">
@import "@/style/Functions.less";

.change-aacompany-container {
    display: flex;
    align-items: stretch;
    flex-direction: column;
    text-align: left;

    .tips {
        font-size: 13px;
        line-height: 20px;
        color: #ff821c;
        padding: 35px 50px 25px;
    }

    .input {
        display: flex;
        align-items: center;
        padding: 0 50px 46px;
        font-size: 13px;
        line-height: 20px;
        color: var(--font-color);

        .detail-el-select(250px, 34px);
    }

    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
<script lang="ts" setup>
import { getAACompanyId } from "@/util/baseInfo";
import { ref } from "vue";
import type { IAABossCompanyInfo } from "./utils";
import { request, type IResponseModel } from "@/util/service";
import { setTopLocationhref } from "@/util/url";

const display = ref(false);
const currentAACompany = ref(0);
const aaCompanyList = ref<IAABossCompanyInfo[]>([]);

const show = () => {
    display.value = true;
    currentAACompany.value = Number(getAACompanyId());
    request({
        url: "/api/AABossCompany/List",
        method: "get",
    }).then((res: IResponseModel<IAABossCompanyInfo[]>) => {
        if (res.state === 1000) {
            aaCompanyList.value = res.data;
        }
    });
};

const confirm = () => {
    setTopLocationhref(window.jHost + "/App/Default.aspx?AACompanyId=" + currentAACompany.value);
};

defineExpose({
    show,
});
</script>
