<template>
    <Table
        class="preview-pivot-table"
        ref="previewPivotTableRef"
        :data="tableDataSlice"
        :columns="Columns"
        :fit="true"
        height="100%"
        row-key="asubId"
        :object-span-method="objectSpanMethod"
        :scrollbar-show="false"
        :highlight-current-row="false"
        :show-overflow-tooltip="true"
        :cell-class-name="handleCellClassName"
    >
    </Table>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import { ref, reactive, computed, watch } from "vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";

const props = defineProps({
    tableData: {
        type: Array,
        default: [],
    },
    Columns: {
        type: Array<IColumnProps>,
        default: [],
    },
});
const previewPivotTableRef = ref();
const tableRowHeight = window.isErp ? 44 : 37;
const tableDataSlice = computed(() => {
    let tableBodyHeight = previewPivotTableRef.value?.$el.querySelector(".el-scrollbar__view").clientHeight;
    let tableDataLength = Math.ceil(tableBodyHeight / tableRowHeight);
    return props.tableData.slice(0, tableDataLength);
});
let rowSpanData = ref<{ [key: number]: number }>({ 0: 0 });
let colPropsData = ref<{ [key: number]: string }>({ 0: "" });
let mergeColArr = ref<string[]>([]);
let cachedSpans = {} as any;
watch(
    [tableDataSlice, () => props.Columns],
    () => {
        precomputeSpans();
        previewPivotTableRef.value?.getTable().doLayout();
    },
    { immediate: true }
);
function precomputeSpans() {
    let flattenChildrenColumns = flattenChildren(props.Columns);
    tableDataSlice.value.forEach((row: any, rowIndex: number) => {
        flattenChildrenColumns.forEach((column, columnIndex) => {
            let key = "row" + rowIndex + "column" + columnIndex;
            colPropsData.value[columnIndex] = column.prop || "";
            let prop = column.prop || "";
            const currentValue = row[prop];
            const preRow = tableDataSlice.value[rowIndex - 1] as any;
            const preValue = preRow ? preRow[prop] : null;
            if (currentValue === preValue && checkAllValuesEqual(row, rowIndex, columnIndex)) {
                cachedSpans[key] = { rowspan: 0, colspan: 0 };
            } else {
                let rowspan = 1;
                for (let i = rowIndex + 1; i < tableDataSlice.value.length; i++) {
                    const nextRow = tableDataSlice.value[i] as any;
                    const nextValue = nextRow[prop];
                    if (nextValue === currentValue && checkAllValuesEqual(nextRow, rowIndex + 1, columnIndex)) {
                        rowspan++;
                    } else {
                        break;
                    }
                }
                rowSpanData.value[columnIndex] = rowspan;
                let needSpan = columnIndex === 0 ? rowspan : Math.min(rowspan, rowSpanData.value[columnIndex - 1]);
                if (needSpan > 1 && !mergeColArr.value.includes(prop)) {
                    mergeColArr.value.push(prop);
                }
                cachedSpans[key] = { rowspan: needSpan, colspan: 1 };
            }
        });
    });
}
const objectSpanMethod = (data: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    let { rowIndex, columnIndex } = data;
    let key = "row" + rowIndex + "column" + columnIndex;
    return cachedSpans[key] ? cachedSpans[key] : { rowspan: 1, colspan: 1 };
};
// 判断相同行前面所有列的值是否都相等
const checkAllValuesEqual = (row: any, rowIndex: number, columnIndex: number) => {
    if (columnIndex === 0) {
        return true;
    }
    for (let i = 0; i < columnIndex; i++) {
        const prop = colPropsData.value[i];
        if (row[prop] !== (tableDataSlice.value[rowIndex - 1] as any)[prop]) {
            return false;
        }
    }
    return true;
};

function handleCellClassName(data: any) {
    if (Object.values(mergeColArr.value).includes(data.column.property)) {
        return "merge-col";
    }
}
function flattenChildren(arr:IColumnProps[]) {
    let result:IColumnProps[] = [];
    arr.forEach((item) => {
        if (item.children && item.children.length > 0) {
            result.push(...flattenChildren(item.children));
        } else {
            result.push(item);
        }
    });

    return result;
}
</script>

<style scoped lang="less">
.preview-pivot-table {
    width: 100%;
    height: 100%;
    :deep(.el-table__empty-block) {
        width: 100%;
        height: 100%;
    }
    :deep(.el-table__inner-wrapper) {
        .el-table__body-wrapper {
            .el-scrollbar__wrap,
            .el-scrollbar__view {
                overflow: hidden;
                width: 100%;
                height: 100%;
            }
        }
        .el-table__body tr:hover,
        .el-table__body tr:hover > td.merge-col.el-table__cell {
            background-color: unset !important;
        }
    }
}
</style>
