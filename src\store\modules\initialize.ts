// 初始化相关
import { defineStore } from "pinia"
import { request, IResponseModel } from "@/utils/service"
import { useBasicInfoStore } from "./basicInfo"

interface IInitial {
  periodId: string
}

export const useInitializeStore = defineStore("initialize", () => {
  const intervalId = ref()

  function initialize(params: IInitial) {
    return new Promise<boolean>((resolve) => {
      request({
        method: "post",
        url: "/api/basic/first-init",
        params,
      }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
          if (!res.data.success && !intervalId.value) {
            intervalId.value = setInterval(() => {
              pollingTask(res.data.taskIds)
            })
          }
          resolve(res.data.success)
        }
      })
    })
  }

  function pollingTask(taskIds: string) {
    return new Promise<boolean>((resolve) => {
      request({
        url: "/api/task/init-result",
        method: "get",
        params: {
          taskIds,
        },
      }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
          if (res.data) {
            clearInterval(intervalId.value)
            intervalId.value = null
            // 更新basicInfo中initCompleted为true
            // useBasicInfoStore().basicInfo.value.initCompleted = true
            // location.reload()
          }
          resolve(res.data)
        }
      })
    })
  }

  return { intervalId, initialize }
})
