<template>
    <div class="content">
        <!-- 之前是content设置最大宽度，现在是div设置最大宽度，改动content影响弹窗插入 -->
        <div style="max-width: 1500px;display: flex;flex-direction: column;margin: 0 auto;">
        <div class="title-info">
            <div class="link-content" v-show="!isHideBarcode">
                <img class="link-icon" src="@/assets/Default/horn.png" />
                <span>联系专属导账顾问，为您提供免费导账服务，助您轻松迁移账套数据</span>
                <a class="link" style="margin-left: 5px">
                    立即导账>
                    <img class="link-qrcode" src="@/assets/Default/daoz-qrcode-default.png" />
                </a>
            </div>
            <div class="wxwork-choosespace-btn float-r" v-show="wxworkChooseSpaceBtnShow">
                <img class="icon" src="@/assets/Default/wxwork-icon.png" />
                <span>升级</span>
                <span>{{ wxworkSpaceProductName }}</span>
                <span>空间</span>
            </div>
        </div>
        <div class="main-content">
            <div class="top-content">
                <div class="item-voucher" @click="handleRouterTo('voucher')">
                    <div class="title-item-top">查看凭证</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-trialbalance" @click="handleRouterTo('trialbalance')">
                    <div class="title-item-top">科目余额表</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-balancesheet" @click="handleRouterTo('balancesheet')">
                    <div class="title-item-top">资产负债表</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-businessactivitystatement" v-if="isFolk" @click="handleRouterTo('businessactivitystatement')">
                    <div class="title-item-top">业务活动表</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-surplusdistributionstatement" v-else-if="isFarmer" @click="handleRouterTo('surplusdistributionstatement')">
                    <div class="title-item-top">盈余及盈余分配表</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-incomeandexpendituresheet" v-else-if="isUnion" @click="handleRouterTo('incomeandexpenditure')">
                    <div class="title-item-top">收入支出表</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-incomeanddistributionsheet" v-else-if="isVillage" @click="handleRouterTo('incomeanddistrubutionsheet')">
                    <div class="title-item-top">收益及收益分配表</div>
                    <div class="title-item-bottom"></div>
                </div>
                <div class="item-incomestatement" v-else @click="handleRouterTo('incomestatement')">
                    <div class="title-item-top">利润表</div>
                    <div class="title-item-bottom"></div>
                </div>
            </div>
            <div class="center-content">
                <div class="center-content-left">
                    <div class="center-content-left-top"></div>
                    <div class="center-content-left-bottom">
                        <div class="cross" @click="handleRouterTo('newVoucher')">
                            <div class="cross-c"></div>
                            <div class="cross-r"></div>
                        </div>
                        <div class="cross-text">新增凭证</div>
                        <div class="bottom-desc" v-show="!isProSystem">欢迎来到这里体验我们的产品！</div>
                    </div>
                </div>
                <div class="center-content-right" :class="{ folk: isFolk }">
                    <div class="center-content-rigth-top">
                        <span class="center-content-rigth-top-title">{{ indexTitle }}</span>
                        <div class="center-content-rigth-top-date">
                            <div @click="perPeroid" :class="'img goto-left-' + (isFirst ? 'disable' : 'normal')"></div>
                            <span>{{ indexDate }}</span>
                            <div @click="nextPeroid" :class="'img goto-right-' + (isLast ? 'disable' : 'normal')"></div>
                        </div>
                    </div>
                    <div class="center-content-rigth-unit">单位：（万元）</div>
                    <div class="center-content-center1">
                        <div class="center-content-center-item" v-for="(item, index) in financialIndex" :key="index">
                            <div class="center-content-center-item-top">{{ item.value }}</div>
                            <div class="center-content-center-item-bottom">{{ item.title }}</div>
                        </div>
                    </div>
                    <div class="line"></div>
                    <div class="center-content-center2">
                        <div class="center-content-center2-item" v-show="theme === 'month'">
                            <div class="center-content-center-item" v-for="(item, index) in monthIndex" :key="index">
                                <div class="center-content-center-item-top" :class="{ important: index === 2 }">
                                    {{ item.value }}
                                </div>
                                <div class="center-content-center-item-bottom">{{ item.title }}</div>
                            </div>
                        </div>
                        <div class="center-content-center2-item" v-show="theme === 'year'">
                            <div class="center-content-center-item" v-for="(item, index) in yearIndex" :key="index">
                                <div class="center-content-center-item-top" :class="{ important: index === 2 }">
                                    {{ item.value }}
                                </div>
                                <div class="center-content-center-item-bottom">{{ item.title }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="center-content-buttons">
                        <a class="button1 hover" v-if="isFarmer" style="border-radius: 4px 4px 4px 4px">年度指标</a>
                        <template v-else>
                            <a class="button1" :class="{ hover: theme === 'month' }" @click="() => (theme = 'month')">月度指标</a>
                            <a class="button2" :class="{ hover: theme === 'year' }" @click="() => (theme = 'year')">年度指标</a>
                        </template>
                    </div>
                </div>
            </div>
            <div class="bottom-ad-content" v-show="bannerSrc.length">
                <img @click="linkHandle" :src="bannerSrc" />
            </div>
        </div>
        </div>
    </div>
    <!-- 创建成功弹窗 -->
    <CreateAccountSetsSuccessDialog ref="createSuccessRef" />
</template>

<script lang="ts">
export default {
    name: "Default",
};
</script>
<script setup lang="ts">
import { ref, watch, toRef, watchEffect, onMounted, inject } from "vue";
import { checkPermission } from "@/util/permission";
import { globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { isInWxWork } from "@/util/wxwork";
import { useRoute } from "vue-router";
import { menuClickKey, type IFinanceIndexSheetItem } from "./types";
import { BannerEnum } from "./types";
import { formatMoney } from "@/util/format";
import { getGlobalToken } from "@/util/baseInfo";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import type { IPeriod } from "@/api/period";
import CreateAccountSetsSuccessDialog from "@/components/Dialog/CreateAccountSetsSuccessDialog/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";

const accountStandard = useAccountSetStore().accountSet?.accountingStandard as number;
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);

const menuClick = inject(menuClickKey);

const bannerSrc = ref("");
const wxworkChooseSpaceBtnShow = ref(false);
const wxworkSpaceProductName = ref("10G");
const isFolk = ref(false);
const isFarmer = ref(false);
const isUnion = ref(false);
const isVillage = ref(false);
const isFirst = ref(false);
const isLast = ref(false);
const theme = ref<"year" | "month">("month");
const periodList = toRef(useAccountPeriodStore(), "periodList");
const isProSystem = ref(window.isProSystem);
const pid = ref(-1);
let minPid = -1;
let maxPid = -1;
let canChange = true;

const indexTitle = ref("----年--期财务指标");
const indexDate = ref("----年--月");
interface IndexRows {
    title: string;
    value: string;
}
const financialIndex = ref<Array<IndexRows>>([]);
const monthIndex = ref<Array<IndexRows>>([]);
const yearIndex = ref<Array<IndexRows>>([]);
function setFinancialIndex() {
    let index: Array<IndexRows> = [];
    switch (accountStandard) {
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            index = [
                { title: "货币资金", value: "- -" },
                { title: "应收款项", value: "- -" },
                { title: "存货", value: "- -" },
                { title: "固定资产", value: "- -" },
                { title: "应付款项", value: "- -" },
            ];
            break;
        case AccountStandard.FolkComapnyStandard:
            index = [
                { title: "货币资金", value: "- -" },
                { title: "应收账款", value: "- -" },
                { title: "存货", value: "- -" },
                { title: "应付账款", value: "- -" },
            ];
            break;
        case AccountStandard.UnionStandard:
            index = [
                { title: "货币资金", value: "- -" },
                { title: "其他应收款", value: "- -" },
                { title: "库存物品", value: "- -" },
                { title: "固定资产", value: "- -" },
                { title: "其他应付款", value: "- -" },
            ];
            break;
        default:
            index = [
                { title: "货币资金", value: "- -" },
                { title: "应收账款", value: "- -" },
                { title: "存货", value: "- -" },
                { title: "固定资产", value: "- -" },
                { title: "应付账款", value: "- -" },
            ];
    }
    financialIndex.value = index;
}
function setMonthIndex() {
    let index: Array<IndexRows> = [];
    switch (accountStandard) {
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
            index = [];
            break;
        case AccountStandard.FolkComapnyStandard:
            index = [
                { title: "综合税负率", value: "- -" },
                { title: "应交税金", value: "- -" },
                { title: "收入", value: "- -" },
                { title: "费用", value: "- -" },
            ];
            break;
        case AccountStandard.UnionStandard:
            index = [
                { title: "收入", value: "- -" },
                { title: "支出", value: "- -" },
                { title: "收支差额", value: "- -" },
                { title: "年初结余", value: "- -" },
                { title: "年末结余", value: "- -" },
            ];
            break;
        case AccountStandard.VillageCollectiveEconomyStandard:
            index = [
                { title: "经营收入", value: "- -" },
                { title: "经营收益", value: "- -" },
                { title: "收益总额", value: "- -" },
                { title: "净收益", value: "- -" },
                { title: "可分配收益", value: "- -" },
            ];
            break;
        default:
            index = [
                { title: "综合税负率", value: "- -" },
                { title: "应交税费", value: "- -" },
                { title: "营业收入", value: "- -" },
                { title: "利润额", value: "- -" },
                { title: "净利率", value: "- -" },
            ];
    }
    monthIndex.value = index;
}
function setYearIndex() {
    let index: Array<IndexRows> = [];
    switch (accountStandard) {
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
            index = [
                { title: "经营收入", value: "- -" },
                { title: "经营支出", value: "- -" },
                { title: "管理费用", value: "- -" },
                { title: "本年盈余", value: "- -" },
                { title: "未分配盈余", value: "- -" },
            ];
            break;
        case AccountStandard.FolkComapnyStandard:
            index = [
                { title: "综合税负率", value: "- -" },
                { title: "应交税金", value: "- -" },
                { title: "收入", value: "- -" },
                { title: "费用", value: "- -" },
            ];
            break;
        case AccountStandard.UnionStandard:
            index = [
                { title: "收入", value: "- -" },
                { title: "支出", value: "- -" },
                { title: "收支差额", value: "- -" },
                { title: "年初结余", value: "- -" },
                { title: "年末结余", value: "- -" },
            ];
            break;
        case AccountStandard.VillageCollectiveEconomyStandard:
            index = [
                { title: "经营收入", value: "- -" },
                { title: "经营收益", value: "- -" },
                { title: "收益总额", value: "- -" },
                { title: "净收益", value: "- -" },
                { title: "可分配收益", value: "- -" },
            ];
            break;
        default:
            index = [
                { title: "综合税负率", value: "- -" },
                { title: "应交税费", value: "- -" },
                { title: "营业收入", value: "- -" },
                { title: "利润额", value: "- -" },
                { title: "净利率", value: "- -" },
            ];
    }
    yearIndex.value = index;
}
function changeStandard() {
    switch (accountStandard) {
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
            isFarmer.value = true;
            break;
        case AccountStandard.FolkComapnyStandard:
            isFolk.value = true;
            break;
        case AccountStandard.UnionStandard:
            isUnion.value = true;
            break;
        case AccountStandard.VillageCollectiveEconomyStandard:
            isVillage.value = true;
            break;
    }
}
function IsShowSpaceBtn() {
    request({ url: "/api/WxWork/IsShowSpaceBtn", method: "post" }).then((r: any) => {
        if (r.state == 1000 && !!r.data.isShow) {
            // wxworkChooseSpaceBtnShow.value = true;
            wxworkSpaceProductName.value = r.data.productName;
        }
    });
}
//后续还有banner,可提取-进销存
function isShowAdvertisement() {
    if (isInWxWork()) return;
    if (!useThirdPartInfoStoreHook().isHideBarcode || !useThirdPartInfoStoreHook().isThirdPart) {
        let systemType: BannerEnum = BannerEnum.Free;
        if (window.isAccountingAgent) {
            if (window.isProSystem) {
                systemType = BannerEnum.ProAgent;
            } else {
                systemType = BannerEnum.FreeAgent;
            }
        } else {
            if (window.isProSystem) {
                systemType = BannerEnum.Pro;
            } else {
                systemType = BannerEnum.Free;
            }
        }
        request({ url: window.apimHost + "/api/Banner/JzPc?dispType=" + systemType }).then((r: any) => {
            if (r.code == 1000 && r.data) {
                bannerSrc.value = r.data.picOne;
                if (r.data.link) {
                    linkHandle = () => {
                        const link = r.data.link;
                        globalWindowOpen(link + (link.indexOf("?") > -1 ? "&" : "?") + `appasid=${getGlobalToken()}&stay=true`);
                        request({ url: window.apimHost + "/api/Banner/JzPc?adId=" + r.data.adid, method: "post" });
                    };
                }
            }
        });
    }
}

function handleRouterTo(type: string) {
    if (type === "newVoucher") {
        if (!checkPermission(["voucher-canedit"])) {
            ElNotify({ type: "warning", message: "您没有此功能权限！" });
            return;
        }
    } else {
        if (!checkPermission([type + "-canview"])) {
            ElNotify({ type: "warning", message: "您没有此功能权限！" });
            return;
        }
    }
    let urlPath = "";
    let title = "";
    switch (type) {
        case "voucher":
            urlPath = "/Voucher/VoucherList";
            title = "查看凭证";
            break;
        case "trialbalance":
            urlPath = "/AccountBooks/TrialBalance";
            title = "科目余额表";
            break;
        case "balancesheet":
            urlPath = "/Statements/BalanceSheet";
            title = "资产负债表";
            break;
        case "incomestatement":
            urlPath = "/Statements/IncomeStatement";
            title = "利润表";
            break;
        case "businessactivitystatement":
            urlPath = "/Statements/BusinessActivityStatement";
            title = "业务活动表";
            break;
        case "surplusdistributionstatement":
            urlPath = "/Statements/SurplusDistributionStatement";
            title = "盈余及盈余分配表";
            break;
        case "newVoucher":
            urlPath = "/Voucher/NewVoucher";
            title = "新增凭证";
            break;
        case "incomeandexpenditure":
            urlPath = "/Statements/IncomeAndExpenditureSheet";
            title = "收入支出表";
            break;
        case "incomeanddistrubutionsheet":
            urlPath = "/Statements/IncomeAndDistributionSheet";
            title = "收益及收益分配表";
            break;
    }
    menuClick && menuClick();
    globalWindowOpenPage(urlPath, title);
}
function perPeroid() {
    const count = pid.value - 1;
    pid.value = count <= minPid ? minPid : count;
}
function nextPeroid() {
    const count = pid.value + 1;
    pid.value = count >= maxPid ? maxPid : count;
}
let linkHandle = () => {};

function handleSearch(pid: number) {
    if (!canChange) return;
    canChange = false;
    request({ url: "/api/FinanceIndexSheet?pid=" + pid }).then((res: IResponseModel<Array<IFinanceIndexSheetItem>>) => {
        if (res.state != 1000) return;
        changeIndexInfo(res.data);
        canChange = true;
    });
}
function changeIndexInfo(data: IFinanceIndexSheetItem[]) {
    for (let i = 0; i < data.length; i++) {
        const item = data[i];
        const financial_index = financialIndex.value.findIndex((index) => index.title === item.lineName);
        const month_index = monthIndex.value.findIndex((index) => index.title === item.lineName);
        const year_index = yearIndex.value.findIndex((index) => index.title === item.lineName);
        if (financial_index > -1) {
            financialIndex.value[financial_index].value = numberWithTenThousand(item.amount);
        } else {
            if (month_index > -1) {
                monthIndex.value[month_index].value = item.lineName.endsWith("率")
                    ? roundToTwoDecimal(item.amount)
                    : numberWithTenThousand(item.amount);
            }
            if (year_index > -1) {
                yearIndex.value[year_index].value = item.lineName.endsWith("率")
                    ? roundToTwoDecimal(item.initalAmount)
                    : numberWithTenThousand(item.initalAmount);
            }
        }
    }
}
function numberWithTenThousand(num: number): string {
    if (num === 0) return "- -";
    const count = num / 10000;
    return formatMoney(count) || "0.00";
}
function roundToTwoDecimal(num: number): string {
    if (num === 0) return "- -";
    const count = (num * 100) / 100 + "";
    return (formatMoney(count) || "0.00") + "%";
}

watch(pid, (val) => {
    isLast.value = val === maxPid;
    isFirst.value = val === minPid;
    const periodInfo = periodList.value.find((item) => item.pid === val) as IPeriod;
    const { year, sn } = periodInfo;
    const month = (sn + "").padStart(2, "0");
    indexTitle.value = year + "年" + month + "月" + "财务指标";
    indexDate.value = year + "年" + month + "月";
    handleSearch(val);
});
watchEffect(() => {
    pid.value = periodList.value[periodList.value.length - 1]?.pid ?? -1;
    const pidList = periodList.value.map((item) => item.pid);
    minPid = Math.min(...pidList);
    maxPid = Math.max(...pidList);
});
function handleInit() {
    setFinancialIndex();
    setMonthIndex();
    setYearIndex();
    changeStandard();
    IsShowSpaceBtn();
    isShowAdvertisement();
    if (isFarmer.value) {
        theme.value = "year";
    }
}

// 创建成功弹窗
const createSuccessRef = ref();
const route = useRoute();
onMounted(() => {
    const showCreateSuccessDialog = JSON.parse(localStorage.getItem("showCreateSuccessDialog") || "false");
    if (showCreateSuccessDialog) {
        createSuccessRef.value?.showDialog();
        localStorage.removeItem("showCreateSuccessDialog");
    }
    if (route.query.toPage && (route.query.toPage as string).toLowerCase() === "salesinvoice" && checkPermission(["invoice-output"])) {
        globalWindowOpenPage("/Invoice/SalesInvoice", "销售发票");
        useRouterArrayStoreHook().resetRouterQuery("/Default/Default");
    }
});
handleInit();
</script>

<style lang="less" scoped>
@import "@/style/Default/Default.less";
@import "@/style/SelfAdaption.less";
.content {
    .title-info {
        margin: 0;
        margin-left: 20px;
    }
    .top-content {
        & > div {
            width: 24%;
        }
    }
    .center-content {
        .center-content-left,
        .center-content-right {
            width: 49.5%;
        }
    }
    .main-content {
        flex: 1;
        box-sizing: border-box;
    }
}
</style>
