<template>
    <div class="content" style="height: 100%">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content" style="height: 100%">
                    <div class="title">{{ title + "明细账" }}</div>
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left" @mouseenter="closePopover">
                            <SearchInfoContainer ref="containerRef" @first-enter="firstEnter">
                                <template v-slot:title>{{ currentPeriodInfo }}</template>
                                <div class="line-item first-item input">
                                    <div class="line-item-title">会计期间：</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            ref="periodPickerRef"
                                            v-model:startPid="searchInfo.startMonth"
                                            v-model:endPid="searchInfo.endMonth"
                                            :clearable="false"
                                            :editable="false"
                                            :dateType="'month'"
                                            :value-format="'YYYYMM'"
                                            :label-format="'YYYY年MM月'"
                                            :isPeriodList="true"
                                            @getActPid="getActPid"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">起始科目：</div>
                                    <div class="line-item-field">
                                        <SubjectPicker
                                            ref="startAsubRef"
                                            asubImgRight="14px"
                                            :showDisabled="true"
                                            v-model="searchInfo.sbj_id_s"
                                            @get-asub-id="handleAsubChange($event, 'startAsub')"
                                            :immediate="false"></SubjectPicker>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">结束科目：</div>
                                    <div class="line-item-field">
                                        <SubjectPicker
                                            ref="endAsubRef"
                                            asubImgRight="14px"
                                            :showDisabled="true"
                                            v-model="searchInfo.sbj_id_e"
                                            @get-asub-id="handleAsubChange($event, 'endAsub')"
                                            :immediate="false"></SubjectPicker>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">科目级别：</div>
                                    <div class="line-item-field">
                                        <el-input-number
                                            v-model="searchInfo.sbj_leval_s"
                                            :min="1"
                                            :max="maxCodelength"
                                            controls-position="right"
                                            style="width: 132px"></el-input-number>
                                        <div class="ml-10 mr-10">至</div>
                                        <el-input-number
                                            v-model="searchInfo.sbj_leval_e"
                                            :min="1"
                                            :max="maxCodelength"
                                            controls-position="right"
                                            style="width: 132px"></el-input-number>
                                    </div>
                                </div>
                                <div class="line-item input" v-show="fcShow">
                                    <div class="line-item-title">币别：</div>
                                    <div class="line-item-field fcid">
                                        <el-select 
                                            v-model="searchInfo.fcid" 
                                            :teleported="false" 
                                            :filterable="true"
                                            :filter-method="fcFilterMethod"
                                        >
                                            <el-option v-for="item in showfcList" :label="item.name" :value="item.id" :key="item.id" />
                                        </el-select>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">摘要：</div>
                                    <div class="line-item-field">
                                        <el-input
                                            v-model="searchInfo.txtDescription"
                                            clearable
                                            style="width: 298px"
                                            maxlength="256"></el-input>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">排序方式：</div>
                                    <div class="line-item-field">
                                        <el-radio-group v-model="searchInfo.sortColumn">
                                            <el-radio label="V_NUM">凭证号排序</el-radio>
                                            <el-radio label="V_DATE">凭证日期排序</el-radio>
                                        </el-radio-group>
                                    </div>
                                </div>
                                <div class="line-item single">
                                    <div class="line-item-title">
                                        <el-checkbox v-model="searchInfo.ifas" label="显示辅助核算"></el-checkbox>
                                    </div>
                                </div>
                                <div class="line-item single">
                                    <div class="line-item-title">
                                        <el-checkbox v-model="searchInfo.ifls" label="只显示最明细科目"></el-checkbox>
                                    </div>
                                </div>
                                <div class="line-item single">
                                    <div class="line-item-title">
                                        <el-checkbox v-model="searchInfo.contrastSubject" label="显示对方科目"></el-checkbox>
                                    </div>
                                </div>
                                <div class="line-item single">
                                    <div class="line-item-title">
                                        <el-checkbox v-model="searchInfo.IsBalanceZero" label="余额为0不显示"></el-checkbox>
                                    </div>
                                </div>
                                <div class="line-item single">
                                    <div class="line-item-title">
                                        <el-checkbox
                                            v-model="searchInfo.NoAmountIncurredAndBalanceZero"
                                            label="无发生额且余额为0不显示"></el-checkbox>
                                    </div>
                                </div>
                                <div class="line-item single">
                                    <div class="line-item-title">
                                        <el-checkbox
                                            v-model="searchInfo.HiddenTotal"
                                            label="无发生额不显示本期合计、本年累计"></el-checkbox>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button solid-button" @click="handleSearch">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                            <ErpRefreshButton></ErpRefreshButton>
                            <div class="ml-10 filter-tip" v-show="searchParams.txtDescription">
                                <Tooltip
                                    :content="`已帮您按摘要(${searchParams.txtDescription})筛选对应凭证行，期初余额、本期合计、本年累计金额行按照原科目取值未按照筛选重新计算哦~`"
                                    :max-width="970"
                                    :line-clamp="1"
                                    placement="bottom"
                                    :teleported="true">
                                    <img src="@/assets/Icons/warn.png" alt="" style="width: 16px; height: 16px" />
                                    <span class="ml-10 ellipsis">
                                        已帮您按摘要({{
                                            searchParams.txtDescription
                                        }})筛选对应凭证行，期初余额、本期合计、本年累计金额行按照原科目取值未按照筛选重新计算哦~</span
                                    >
                                </Tooltip>
                            </div>
                        </div>
                        <div class="main-tool-right">
                            <div class="mr-10">
                                <el-checkbox
                                    label="显示数量金额"
                                    v-model="showNumber"
                                    :disabled="numberDisabled"
                                    @change="handleChange"></el-checkbox>
                            </div>
                            <Dropdown
                                class="mr-10 print-down"
                                btnTxt="打印当前数据"
                                :width="120"
                                :downlistWidth="120"
                                v-permission="['subsidiaryledger-canprint']">
                                <li @click="handlePrintOpen(0, 0)">直接打印</li>
                                <li @click="handlePrintOpen(0, 2)">打印设置</li>
                            </Dropdown>
                            <Dropdown
                                class="mr-10 print-down"
                                btnTxt="打印所有科目"
                                :width="120"
                                :downlistWidth="120"
                                v-permission="['subsidiaryledger-canprint']">
                                <li @click="handlePrintOpen(1, 0)">直接打印</li>
                                <li @click="handlePrintOpen(1, 2)">打印设置</li>
                            </Dropdown>
                            <a class="button mr-10" v-permission="['subsidiaryledger-canexport']" @click="handleExport(0)">导出</a>
                            <a class="button" v-permission="['subsidiaryledger-canexport']" @click="handleExport(1)">导出全部</a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div :class="['main-center', { isErpCenter: isErp }]">
                        <div v-loading="loading" element-loading-text="正在加载数据..." class="main-left">
                            <div class="main-title flex">
                                <div class="main-title-left">
                                    <Tooltip :content="asubInfo" :teleported="true" :maxWidth="1440">
                                        <div class="ellipsis">科目：{{ asubInfo ? asubInfo : "所有" }}</div>
                                    </Tooltip>
                                </div>
                                <div v-show="numberComputedShow" class="main-title-right">
                                    <Tooltip :content="unit.replace('单位：', '')" :teleported="true" :maxWidth="72">
                                        <div class="ellipsis">{{ unit }}</div>
                                    </Tooltip>
                                </div>
                            </div>
                            <AccountBooksTable
                                class="account-books-table"
                                :data="tableData"
                                :columns="columns"
                                :empty-text="emptyText"
                                :page-is-show="true"
                                :layout="paginationData.layout"
                                :page-sizes="paginationData.pageSizes"
                                :page-size="paginationData.pageSize"
                                :total="paginationData.total"
                                :currentPage="paginationData.currentPage"
                                :scrollbarShow="true"
                                :tooltip-options="{ effect: 'light', placement: 'right-start' }"
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                @refresh="handleRerefresh"
                                :tableName="setModule">
                                <template #vgname>
                                    <el-table-column
                                        label="凭证字号"
                                        min-width="80"
                                        align="left"
                                        headerAlign="left"
                                        prop="vgname"
                                        :width="getColumnWidth(setModule, 'vgname')">
                                        <template #default="scope">
                                            <span
                                                :class="checkPermission(['voucher']) ? 'link' : ''"
                                                :style="{ cursor: checkPermission(['voucher']) ? 'pointer' : 'default' }"
                                                @click="checkPermission(['voucher']) ? ShowDetail(scope.row) : ''">
                                                {{ scope.row.vg_name.replace(/<[^<>]+>/g, "") }}
                                            </span>
                                        </template>
                                    </el-table-column>
                                </template>
                            </AccountBooksTable>
                        </div>
                        <div class="main-right drag-right">
                            <div class="assit-col-line"></div>
                            <div class="drag-line" v-tree-drag="flag" v-show="flag.closeFlag"></div>
                            <div class="tree-click">
                                <span :class="['tree-img', {'disabled': !flag.closeFlag}]" @click="treeClick(0, flag)"></span>
                                <span :class="['tree-img expand', {'disabled': !flag.expandFlag}]" @click="treeClick(1, flag)"></span>
                            </div>
                            <div class="main-right-title">快速切换</div>
                            <div class="select-content">
                                <div class="search-box"></div>
                                <div class="pd-10">
                                    <Tooltip :content="searchVal" :max-width="286" :lineClamp="2" placement="left" :teleported="true">
                                        <input
                                            id="search-box-input"
                                            autocomplete="off"
                                            type="text"
                                            :value="searchVal"
                                            @input="handleInput"
                                            @keydown.enter="handleSearchEnter"
                                            @focus="handleFocus" />
                                    </Tooltip>
                                </div>
                                <div class="select-info" v-show="searchListShow">
                                    <!-- <div
                                        class="select-info-inner"
                                        v-for="item in searchShowList"
                                        :key="item.id"
                                        @click="handleSearchItemClick(item)"
                                    >
                                        <Tooltip :content="item.text" :max-width="286" :lineClamp="2" placement="left" :teleported="true">
                                            {{ item.text }}
                                        </Tooltip>
                                    </div> -->
                                    <recycle-scroller
                                        v-show="searchShowList.length"
                                        class="virtual-list"
                                        :buffer="1000"
                                        :prerender="200"
                                        style="height: 100%"
                                        :item-size="28"
                                        key-field="id"
                                        :items="searchShowList">
                                        <template v-slot="{ item }">
                                            <Tooltip
                                                :key="item.id"
                                                :content="item.text"
                                                :line-clamp="1"
                                                :font-size="14"
                                                :placement="'left'"
                                                :teleported="true"
                                                :dynamicWidth="true"
                                            >
                                                <div class="select-info-inner" @click="handleSearchItemClick(item)">
                                                    {{ item.text }}
                                                </div>
                                            </Tooltip>
                                        </template>
                                    </recycle-scroller>
                                    <div class="select-info-inner" v-show="searchShowList.length === 0" style="text-align: center">
                                        没有匹配的选项
                                    </div>
                                </div>
                            </div>
                            <div class="tree">
                                <!-- <el-scrollbar :always="true" :height="treeHeight">
                                    <el-tree
                                        ref="treeRef"
                                        node-key="id"
                                        :data="treeList"
                                        :indent="21"
                                        :default-expand-all="true"
                                        :expand-on-click-node="false"
                                        :highlight-current="true"
                                        :current-node-key="sbj_id"
                                        :empty-text="rightEmptyText"
                                        :props="{ label: 'text', children: 'children' }"
                                        :class="treeList.length === 0 ? 'no-data no-animation-tree' : 'no-animation-tree'"
                                        @node-click="handleNodeClick"
                                        :virtual-scroll="true"
                                        :remain="10"
                                        :bench="5"
                                    >
                                        <template #default="{ data }">
                                            <span class="custom-tree-node">
                                                <span
                                                    :class="
                                                        data.children.length != 0
                                                            ? 'tree-icon tree-folder tree-folder-open'
                                                            : 'tree-icon tree-file'
                                                    "
                                                ></span>
                                                <span class="tree-title">{{ data.text }}</span>
                                            </span>
                                        </template>
                                    </el-tree>
                                </el-scrollbar> -->
                                <el-scrollbar 
                                    :always="true" 
                                    :height="treeV2Height"
                                    class="tree-v2-scroll"
                                    :minSize="80" 
                                >
                                    <el-tree-v2
                                        :style="{ width: setWidth }"
                                        class="my-tree-v2"
                                        ref="treeRef"
                                        :height="treeV2Height - 10"
                                        node-key="id"
                                        :data="treeList"
                                        :indent="21"
                                        :default-expand-all="true"
                                        :expand-on-click-node="false"
                                        :highlight-current="true"
                                        :current-node-key="sbj_id"
                                        :empty-text="rightEmptyText"
                                        :props="{ label: 'text', children: 'children' }"
                                        :class="treeList.length === 0 ? 'no-data no-animation-tree' : 'no-animation-tree'"
                                        @node-click="handleNodeClick">
                                        <template #default="{ data }">
                                            <span class="custom-tree-node">
                                                <span
                                                    :class="
                                                        data.children.length != 0
                                                            ? 'tree-icon tree-folder tree-folder-open'
                                                            : 'tree-icon tree-file'
                                                    "></span>
                                                <span class="tree-title">{{ data.text }}</span>
                                            </span>
                                        </template>
                                    </el-tree-v2>
                                </el-scrollbar>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #add>
                <div class="slot-content voucher">
                    <CheckVoucher
                        ref="checkVoucherRef"
                        :changeCurrentSlot="() => (currentSlot = 'add')"
                        :sortColumn="searchInfo.sortColumn"
                        :showSwitch="true"
                        :ps="searchInfo.period_s"
                        :pe="searchInfo.period_e"
                        :sbgId="Number(sbj_id)"
                        @load-data="handleGetTableData"
                        @handle-back="handVoucherBack"
                        @hasChanged="() => (voucherHasChanged = true)" />
                </div>
            </template>
        </ContentSlider>
    </div>
    <!-- <el-dialog v-model="printDialogVisible" title="明细账打印" center width="480" draggable class="custom-confirm">
        <div class="print-content">
            <div class="print-main">
                <div class="line-item mt-20 mb-20">
                    <span class="mr-20">图像方向：</span>
                    <el-radio-group v-model="printInfo.imageDirection" :disabled="showNumber">
                        <el-radio :label="1">横向</el-radio>
                        <el-radio :label="0">纵向</el-radio>
                    </el-radio-group>
                </div>
                <div class="line-item pt-10 pb-10">
                    <el-checkbox v-model="printInfo.hidePrintDate" value="1" label="隐藏打印日期"></el-checkbox>
                    <el-checkbox v-model="printInfo.HideSubject" label="隐藏科目"></el-checkbox>
                    <el-checkbox v-model="printInfo.printBasepage" label="打印封面"></el-checkbox>
                    <el-checkbox v-model="printInfo.printCatalog" label="打印目录"></el-checkbox>
                </div>
                <div class="line-item mt-20 mb-20">
                    为了保证您的正常打印，请先下载安装
                    <a class="link" @click="globalWindowOpen('https://get.adobe.com/cn/reader/')">Adobe PDF阅读器</a>
                </div>
            </div>
            <div class="buttons a_nofloat">
                <a class="button" @click="printDialogVisible = false">取消</a>
                <a class="button solid-button ml-20" @click="handlePrint">打印</a>
            </div>
        </div>
    </el-dialog> -->
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        title="明细账打印"
        allPrint="打印所有科目"
        :printData="printInfo"
        :dir-disabled="showNumber"
        :otherOptions="otherOptions"
        :coverEdit="true"
        @continuePrint="handlePrintOpen(1, 3)"
        @currentPrint="handlePrintOpen(0, 3)" />
    <ConfirmWithoutGlobal
        v-model="confirmWithoutGlobalVisible"
        :show-close="false"
        confirmButtonText="编辑原凭证"
        cancelButtonText="进入新页面"
        :cancel-click="reLoadCurrentPage">
        <div style="text-align: left; margin: 0 -30px">
            您之前编辑的凭证还没有保存<br />点击'编辑原凭证'则可继续编辑原凭证<br />点击'进入新页面'则原凭证将不会保存并进入明细账页面
        </div>
    </ConfirmWithoutGlobal>
</template>

<script lang="ts">
export default {
    name: "SubsidiaryLedger",
};
</script>
<script setup lang="ts">
import {
    ref,
    reactive,
    provide,
    watch,
    nextTick,
    onMounted,
    computed,
    onUnmounted,
    defineAsyncComponent,
    watchEffect,
    onBeforeUnmount,
    onActivated,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElTree, ElTreeV2 } from "element-plus";
import { getGlobalLodash } from "@/util/lodash";
import { request, type IResponseModel } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { ElNotify } from "@/util/notify";
import { componentFinishKey } from "@/symbols";
import { getUrlSearchParams, globalExport,} from "@/util/url";
import { findTreeIdById, formatterSearchList, changeColumnName, CheckIfQuantity } from "./utils";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableData, ITree, IFcList, IAsubCodeLength, IResTableData } from "./types";
import Dropdown from "@/components/Dropdown/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import CheckVoucher from "@/components/CheckVoucher/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import { getCookie, setCookie } from "@/util/cookie";
import { getLocalStorage } from "@/util/localStorageOperate";
import Tooltip from "@/components/Tooltip/index.vue";
import { checkPermission } from "@/util/permission";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import RefreshButton from "@/components/RefreshButton/index.vue";
import ConfirmWithoutGlobal from "@/components/ConfirmWithoutGlobal/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import useObserver from "./observe";
import { onBeforeRouteLeave } from 'vue-router';
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { treeClick } from "@/views/AccountBooks/SubsidiaryLedger/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo, initStartOrEndMonth } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import usePrint from "@/hooks/usePrint";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "SubsidiaryLedger";
const _ = getGlobalLodash()
const width = ref("100%");
const setWidth = ref("100%");
let scrollTopTree = 0;
const callback = () => {
    // 寻找宽度
    let maxWidth = 0;
    let maxPaddingLeft = 0;
    // 获取虚拟树dom
    const treeDom = document.querySelector(`.tree-v2-scroll .el-tree .el-tree-virtual-list`);
    treeDom?.addEventListener("scroll", getScrollTopTree);
    function getScrollTopTree() {
        scrollTopTree = treeDom?.scrollTop ?? 0;
    }
    // 在组件即将被卸载前取消事件监听
    onBeforeUnmount(() => {
        treeDom?.removeEventListener("scroll", getScrollTopTree);
    });
    Array.from(treeDom?.children?.[0]?.children as unknown as HTMLElement[]).forEach((item: HTMLElement) => {
        getWidth(item);
    });
    // 寻找最小宽度
    const minWidthNode = document.querySelector(`.tree-v2-scroll .el-tree`);
    const minWidth = 0;
    const targetWidth = maxWidth + maxPaddingLeft;
    width.value = _.isNumber(targetWidth) ? (targetWidth > minWidth ? targetWidth : minWidth) + "px" : "100%";
    function getWidth(el: HTMLElement) {
        const elWidthNode = Array.from(el.children).find((item) => {
            return Array.from(item.classList || []).includes("el-tree-node__content");
        }) as HTMLElement;
        if (elWidthNode) {
            const paddingLeftValue = +getElNodeAttrValue(elWidthNode, "padding-left")?.split("px")?.[0] || 0;
            let elWidthNodeList = elWidthNode?.children || ([] as HTMLElement[]);
            let elWidth = 0;
            // 获取padding
            Array.from(elWidthNodeList).forEach((item) => {
                elWidth += item.clientWidth;
            });
            maxWidth = maxWidth > elWidth ? maxWidth : elWidth;
            maxPaddingLeft = maxPaddingLeft > paddingLeftValue ? maxPaddingLeft : paddingLeftValue;
        }
        if (el.children) {
            Array.from(el.children as unknown as HTMLElement[]).forEach((item: HTMLElement) => {
                getWidth(item);
            });
        }
    }
    if (width.value.includes("px")) {
        let num = Number(width.value.slice(0, width.value.length - 2));
        if (maxWidth + maxPaddingLeft > num) {
            setWidth.value = maxWidth + maxPaddingLeft + 8 + "px";
        } else {
            setWidth.value = width.value;
        }
    }
};
const getElNodeAttrValue = (el: HTMLElement, attrKey: string) => {
    const computedStyles = getComputedStyle(el);
    return computedStyles.getPropertyValue(attrKey) as string;
};
useObserver(`.tree-v2-scroll .el-tree`, callback);

const subjectShow = ref(false);
const firstEnter = () => {
    subjectShow.value = true;
    nextTick().then(() => {
        startAsubRef.value?.handleAsyncRender();
        endAsubRef.value?.handleAsyncRender();
    });
};
const periodStore = useAccountPeriodStore();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const route = useRoute();
const router = useRouter();
const isErp = ref(window.isErp);
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const startAsubRef = ref<InstanceType<typeof SubjectPicker>>();
const endAsubRef = ref<InstanceType<typeof SubjectPicker>>();
// const treeRef = ref<InstanceType<typeof ElTree>>();
const treeRef = ref<InstanceType<typeof ElTreeV2>>();
const checkVoucherRef = ref<InstanceType<typeof CheckVoucher>>();

const slots = ["main", "add"];
const currentSlot = ref("main");
const currentPeriodInfo = ref("");
// const periodInfo = ref("");
const columns = ref<Array<IColumnProps>>([]);
const asubInfo = ref("");
const searchVal = ref("");
const title = ref("");
const loading = ref(false);
const searchListShow = ref(false);
const tableData = ref<ITableData[]>([]);
const treeList = ref<ITree[]>([]);
const searchList = ref<ITree[]>([]);
const searchShowList = ref<ITree[]>([]);
const sbj_id = ref("");
const showNumber = ref(false);
const numberDisabled = ref(true);
const currentNode = ref<ITree>();
const fcList = ref<IFcList[]>([]);
const fcShow = ref(false);
const numberComputedShow = ref(false);
const unit = ref("");
const periodPickerRef = ref();
const searchInfo = reactive({
    period_s: Number(periodStore.getPeriodRange().start), // 会计期间开始
    period_e: Number(periodStore.getPeriodRange().end), // 会计期间结束
    sbj_id_s: "", // 起始科目
    sbj_id_e: "", // 结束科目
    sbj_leval_s: 1, // 科目级别
    sbj_leval_e: 4, // 科目级别
    sortColumn: "V_NUM", // 排序方式
    ifas: false, // 显示辅助核算
    ifls: false, // 只显示最明细科目
    contrastSubject: false, // 显示对方科目
    IsBalanceZero: false, // 余额为0不显示
    NoAmountIncurredAndBalanceZero: false, // 无发生额且余额为0不显示
    HiddenTotal: false, // 无发生额不显示本期合计、本年累计
    fcid: -1, // 币别
    txtDescription: "", // 摘要
    startMonth: "",
    endMonth: "",
});
let searchParams = _.cloneDeep(searchInfo);

const { periodData } = usePeriodData(searchInfo, searchInfo.period_s, searchInfo.period_e);  
const getActPid = (start: number, end: number) => {
    searchInfo.period_s = start;
    searchInfo.period_e = end;
}
function updateRouteFateMonth() {
    const result = initStartOrEndMonth(periodData.value, searchInfo.period_s, searchInfo.period_e);  
    searchInfo.startMonth = result.startMonth;  
    searchInfo.endMonth = result.endMonth;  
}
watch([() => searchInfo.period_s, () => searchInfo.period_e], () => {
    updateRouteFateMonth();
});

const aacode = ref(route.query.AA_CODE ?? "");
watch(
    aacode,
    () => {
        searchInfo.ifas = aacode.value ? true : false;
    },
    { immediate: true }
);
const maxCodelength = ref(4);

const getPrintOrExportParams = (ifpage:number) => ({
    period_s: searchParams.period_s,
    period_e: searchParams.period_e,
    sbj_id: sbj_id.value ?? "",
    sbj_leval_s: searchParams.sbj_leval_s,
    sbj_leval_e: searchParams.sbj_leval_e,
    sbj_id_s: searchParams.sbj_id_s.split(" ")[0] ?? "",
    sbj_id_e: searchParams.sbj_id_e.split(" ")[0] ?? "",
    contrastSubject: searchParams.contrastSubject ? 1 : -1,
    IsBalanceZero: searchParams.IsBalanceZero ? 1 : -1,
    NoAmountIncurredAndBalanceZero: searchParams.NoAmountIncurredAndBalanceZero ? 1 : -1,
    HiddenTotal: searchParams.HiddenTotal ? 1 : 0,
    ifas: searchParams.ifas ? 1 : 0,
    ifls: searchParams.ifls ? 1 : 0,
    sortColumn: searchParams.sortColumn,
    ran: Math.random(),
    fcid: searchParams.fcid,
    showNumber: showNumber.value ? 1 : 0,
    txtDescription: searchParams.txtDescription,
    ifpage,
})

const extraInfo = {
    basepage: false,
    printCatalog: true,
    isHideSubject: true, 
    isTitleSubjectShowLeaf: false,
    isBodySubjectShowLeaf: false,
    continuePrint: false,
}

function printValidator() {
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可打印！", type: "warning" });
        return false;
    }
    return true;
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "subsidiaryLedger",
    window.jAccountBooksHost + "/api/SubsidiaryLedger/Print",
    extraInfo,
    true,
    true,
    printValidator
);

if(!getLocalStorage("subsidiaryLedgerPrint")){
    const imageDirection = parseInt(getCookie("ImageDirection")) || 0;
    const hidePrintDate = getCookie("HidePrintDate") === "true";
    const printBasepage = getCookie("PrintBasepage") === "true";
    const printCatalog = getCookie("PrintCatalog") === "true";
    const HideSubject = getCookie("HideSubject") === "true";
    printInfo.value.direction = showNumber.value ? 1 : imageDirection;
    printInfo.value.isShowPrintDate = hidePrintDate;
    printInfo.value.basepage = printBasepage;
    printInfo.value.printCatalog = printCatalog;
    printInfo.value.isHideSubject = HideSubject;
}
otherOptions.value = [
    { key: "isTitleSubjectShowLeaf", label: "表头科目名称只打印末级" },
    { key: "isHideSubject", label: "表体不打印科目" },
    { key: "isBodySubjectShowLeaf", label: "表体科目名称只打印末级" },
    { key: "basepage", label: "打印封面" },
    { key: "printCatalog", label: "打印目录" },
    ...otherOptions.value,
    { key: "continuePrint", label: "连续打印" },
];
// const printDialogVisible = ref(false);
// const printInfo = reactive({
//     imageDirection: 0,
//     hidePrintDate: false,
//     printBasepage: false,
//     printCatalog: true,
//     ifpage: 0,
//     HideSubject: true, //默认隐藏科目
//     marginTop: 0,
//     marginBottom: 0,
//     marginLeft: 0,
//     marginRight: 0,
//     isTitleSubjectShowLeaf: false,
//     isBodySubjectShowLeaf: false,
//     isShowPageNumber: false,
//     continuePrint: false,
//     printDateText: "",
// });

// const otherOptions = [
//     { key: "isTitleSubjectShowLeaf", label: "表头科目名称只打印末级" },
//     { key: "HideSubject", label: "表体不打印科目" },
//     { key: "isBodySubjectShowLeaf", label: "表体科目名称只打印末级" },
//     { key: "printBasepage", label: "打印封面" },
//     { key: "printCatalog", label: "打印目录" },
//     { key: "hidePrintDate", label: "显示打印日期" },
//     { key: "isShowPageNumber", label: "显示页码" },
//     { key: "continuePrint", label: "连续打印" },
// ];

const handlePrintOpen = (ifpage: number, type: 0|2|3) => {
    if (type === 2) {
        handlePrint(type);
    } else {
        handlePrint(type,getPrintOrExportParams(ifpage));
    }
};

// const handlePrint = () => {
//     if (paginationData.total === 0) {
//         ElNotify({ message: "没有数据可打印！", type: "warning" });
//         printDialogVisible.value = false;
//         return;
//     }
//     printDialogVisible.value = false;
//     setCookie("ImageDirection", printInfo.imageDirection.toString(), "d30");
//     setCookie("HidePrintDate", printInfo.hidePrintDate.toString(), "d30");
//     setCookie("PrintBasepage", printInfo.printBasepage.toString(), "d30");
//     setCookie("PrintCatalog", printInfo.printCatalog.toString(), "d30");
//     setCookie("HideSubject", printInfo.HideSubject.toString(), "d30");
//     const printTimer = setTimeout(() => {
//         const params = {
//             ...getPrintOrExportParams(),
//             direction: printInfo.imageDirection,
//             ifpage: printInfo.ifpage,
//             basepage: printInfo.printBasepage ? 1 : 0,
//             hidePrintDate: printInfo.hidePrintDate ? 1 : 0,
//             printCatalog: printInfo.printCatalog ? 1 : 0,
//             async: !isLemonClient(),
//             fcid: searchParams.fcid,
//             HideSubject: printInfo.HideSubject ? 1 : 0,
//         };
//         globalPrint(window.jAccountBooksHost + "/api/SubsidiaryLedger/Print?" + getUrlSearchParams(params));
//         clearTimeout(printTimer);
//     }, 100);
// };

const handleExport = (ifpage: number) => {
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可导出！", type: "warning" });
        return;
    }

    const params = {
        ...getPrintOrExportParams(ifpage),
        async: !isLemonClient(),
    };
    globalExport(window.jAccountBooksHost + "/api/SubsidiaryLedger/Export?" + getUrlSearchParams(params));
};
const handleClose = () => containerRef.value?.handleClose();
const closePopover = () => {
    if (!containerRef.value?.popoverShow) {
        startAsubRef.value?.handleClose();
        endAsubRef.value?.handleClose();
    }
};

//重置按钮
const handleReset = () => {
    searchInfo.sbj_id_s = "";
    searchInfo.sbj_id_e = "";
    searchInfo.sbj_leval_s = 1;
    searchInfo.sbj_leval_e = maxCodelength.value;
    searchInfo.sortColumn = "V_NUM";
    searchInfo.ifas = false;
    searchInfo.ifls = false;
    searchInfo.contrastSubject = false;
    searchInfo.IsBalanceZero = false;
    searchInfo.NoAmountIncurredAndBalanceZero = false;
    searchInfo.HiddenTotal = false;
    searchInfo.fcid = -1;
    searchInfo.txtDescription = "";
};

// 搜索相关
const handleFocus = () => {
    searchListShow.value = true;
    if (firstSearch) {
        searchList.value = formatterSearchList(treeList.value);
        searchShowList.value = searchList.value;
        firstSearch = false;
    }
    document.addEventListener("click", () => beforeHandleBlur());
};
const handleBlur = () => {
    const blurTimer = setTimeout(() => {
        searchListShow.value = false;
        document.removeEventListener("click", () => beforeHandleBlur());
    }, 100);
};

function beforeHandleBlur() {
    document.addEventListener("click", function (event) {
        let targetElement = event.target;
        let inputElement = document.getElementById("search-box-input");
        if (targetElement !== inputElement && !inputElement?.contains(targetElement as Node)) {
            handleBlur();
        }
    });
}

let firstSearch = true;
const handleInput = (e: any) => {
    searchListShow.value = true;
    const text = e.target.value;
    searchVal.value = text;
    searchShowList.value = searchList.value.filter((item) => item.text.indexOf(text) > -1 || item.attributes.acronym.includes(text.trim()));
    firstSearch = false;
};
const handleSearchEnter = () => {
    sbj_id.value = searchShowList.value[0].id;
    searchVal.value = searchShowList.value[0].text;
    searchListShow.value = false;
    const treeId = findTreeIdById(treeList.value, searchShowList.value[0].id);
    nextTick().then(() => {
        treeRef.value?.setCurrentKey(treeId);
        handleNodeClick(treeRef.value?.getCurrentNode());
    });
};

const handleSearchItemClick = (row: ITree) => {
    sbj_id.value = row.id;
    searchVal.value = row.text;
    searchListShow.value = false;
    const treeId = findTreeIdById(treeList.value, row.id);
    nextTick().then(() => {
        treeRef.value?.setCurrentKey(treeId);
        handleNodeClick(treeRef.value?.getCurrentNode());
    });
};

// 凭证相关
const accountSubjectStore = useAccountSubjectStore();

const ShowDetail = async (row: ITableData) => {
    currentSlot.value = "add";
    checkVoucherRef.value?.loadVoucher(row.p_id, row.v_id);
    await accountSubjectStore.getAccountSubject();
    await checkVoucherRef.value?.loadAAE();
    voucherHasChanged.value = false;
};

// 获取数据
let tableLoadingCount = 0;
const setShowNumber = () => {
    const ifnum = CheckIfQuantity(currentNode.value as ITree);
    if (ifnum == "0") {
        numberDisabled.value = true;
        showNumber.value = false;
    } else {
        showNumber.value = true;
        numberDisabled.value = false;
    }
};
const rightEmptyText = ref(" "); //加载动画太多，不显示
const handleGetTreeList = () => {
    return request({
        url: "/api/SubsidiaryLedger/TreeWithObject",
        params: {
            ifas: searchParams.ifas ? 1 : 0,
            ifls: searchParams.ifls ? 1 : 0,
            period_s: searchParams.period_s,
            period_e: searchParams.period_e,
            sbj_id_s: searchParams.sbj_id_s.split(" ")[0] ?? "",
            sbj_id_e: searchParams.sbj_id_e.split(" ")[0] ?? "",
            IsBalanceZero: searchParams.IsBalanceZero ? 1 : 0,
            NoAmountIncurredAndBalanceZero: searchParams.NoAmountIncurredAndBalanceZero ? 1 : 0,
            fcid: searchParams.fcid,
            sbj_leval_s: searchParams.sbj_leval_s,
            sbj_leval_e: searchParams.sbj_leval_e,
            HiddenTotal: searchParams.HiddenTotal ? 1 : 0,
            txtDescription: searchParams.txtDescription,
        },
    });
};
const handleInit = (isRouteChange = false) => {
    request({ url: "/api/AccountSubject/GetAsubCodeLength", method: "post" }).then((res: IResponseModel<IAsubCodeLength>) => {
        if (res.state === 1000) {
            const codeLengthList: number[] = res.data.codeLength;
            const codeLength = codeLengthList.length;
            maxCodelength.value = codeLength;
            searchInfo.sbj_leval_e = codeLength;
            if (route.query.period_s) searchInfo.period_s = Number(route.query.period_s);
            if (route.query.period_e) searchInfo.period_e = Number(route.query.period_e);
            searchParams = _.cloneDeep(searchInfo);
            getTableList(isRouteChange);
        }
    });
};
const emptyText = ref(" "); //加载动画太多，不显示

//将所有科目节点树展开
function handleTreeExpand(data: any) {
    let result: string[] = [];
    function TreeExpand(data: any) {
        for (const item of data) {
            const children = item.children;
            if (children.length > 0) {
                result.push(item.id);
                TreeExpand(children);
            } else {
                result.push(item.id);
            }
        }
    }
    TreeExpand(data);
    return result;
}

const getTableList = (isRouterChange = false) => {
    loading.value = true;
    currentSlot.value = "main";
    getTableListInner(isRouterChange);
};
const getTableListInner = (isRouterChange: boolean) => {
    handleGetTreeList()
        .then((res: IResponseModel<any>) => {
            treeList.value = res.data;
            firstSearch = true;
            if (treeList.value.length === 0) {
                columns.value = changeColumnName(showNumber.value, searchParams.fcid + "", searchParams.contrastSubject, searchParams.ifas);
                tableLoadingCount++;
                searchVal.value = "";
                currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.period_s, searchInfo.period_e);
                sbj_id.value = "";
                currentNode.value = undefined;
                title.value = "";
                asubInfo.value = "无";
                showNumber.value = false;
                numberDisabled.value = true;
                tableData.value = [];
                paginationData.total = 0;
                emptyText.value = "暂无数据";
                rightEmptyText.value = "暂无数据";
                return;
            }
            nextTick().then(() => {
                tableLoadingCount++;
                const asubId = (route.query.ASUB_ID ?? "") as string;
                let treeId: string;
                let recordId: string;
                //设置全部节点都展开
                let ExpandedKeys = handleTreeExpand(treeList.value);
                treeRef.value?.setExpandedKeys(ExpandedKeys);
                let height = ExpandedKeys.length * 26;
                let div = document.querySelector(".tree-v2-scroll .el-scrollbar__view") as HTMLElement;
                if (div) {
                    div.style.height = height + "px";
                }
                // 判断是否是第一次加载  或者从其他页面跳转明细账
                if (tableLoadingCount === 1 || (asubId && isRouterChange)) {
                    treeId =
                        asubId && findTreeIdById(treeList.value, aacode.value ? asubId + "^" + aacode.value : asubId)
                            ? findTreeIdById(treeList.value, aacode.value ? asubId + "^" + aacode.value : asubId)
                            : treeList.value[0].id;
                    if (asubId && isRouterChange) recordTreeId.value = treeId;
                    treeRef.value?.setCurrentKey(treeId);
                    nextTick(()=>{
                        handleNodeClick(treeRef.value?.getCurrentNode(),false); 
                        let scrollTop = ExpandedKeys.indexOf(treeId) * 26;
                        const treeDom = treeRef.value?.$el.querySelector('.el-tree-virtual-list');
                        treeDom && (treeDom.scrollTop = scrollTop);
                    })
                } else {
                    // 显示辅助核算
                    let findId = "";
                    if (searchInfo.ifas && treeList.value[0].children.length > 0) {
                        treeId =
                            currentAsubIdStart.value && findTreeIdById(treeList.value, currentAsubIdStart.value + "")
                                ? currentAsubIdStart.value + ""
                                : findDeepId(treeList.value[0]).id;
                        // 定位深层级科目
                        // treeId = findDeepId(treeList.value[0]).id;
                    } else {
                        findId = findTreeIdById(treeList.value, currentAsubIdStart.value + "");
                        if (searchInfo.sbj_id_s && findId) {
                            treeId = findId ?? treeList.value[0].id;
                        } else {
                            treeId = treeList.value[0].id;
                        }
                    }
                    if (
                        recordTreeId.value &&
                        recordTreeId.value !== treeId &&
                        findTreeIdById(treeList.value, recordTreeId.value) &&
                        !findId
                    ) {
                        recordId = recordTreeId.value;
                    } else {
                        recordId = treeId;
                        recordTreeId.value = treeId;
                    }
                    treeRef.value?.setCurrentKey(recordId);
                    nextTick(() => {
                        handleNodeClick(treeRef.value?.getCurrentNode(), false);
                    });
                }
            });
        })
        .finally(() => {
            loading.value = false;
        });
};

// 寻找深层级id
const findDeepId = (data: any) => {
    data = data.children.length && data.children[0].children.length ? findDeepId(data.children[0]) : data.children[0];
    return data;
};
const recordTreeId = ref("");
const handleNodeClick = (treeNodeData: any, isAuto: boolean = true) => {
    currentNode.value = undefined;
    currentNode.value = treeNodeData;
    sbj_id.value = treeNodeData?.id;
    if (isAuto) {
        recordTreeId.value = treeNodeData?.id;
        if (route.query.ASUB_ID) {
            routerReplace();
        }
    }
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
        return;
    }
    setShowNumber();
    handleGetTableData();
};
const handleChange = () => {
    tableLoadingCount++;
    handleGetTableData();
};
const handleGetTableData = () => {
    columns.value = changeColumnName(showNumber.value, searchParams.fcid + "", searchParams.contrastSubject, searchParams.ifas);
    const params = {
        period_s: searchParams.period_s,
        period_e: searchParams.period_e,
        showNumber: showNumber.value ? 1 : 0,
        sbj_id: sbj_id.value ?? "",
        sbj_leval_s: searchParams.sbj_leval_s,
        sbj_leval_e: searchParams.sbj_leval_e,
        sbj_id_s: currentAsubIdStart.value ?? "",
        sbj_id_e: currentAsubIdEnd.value ?? "",
        ifas: searchParams.ifas ? 1 : 0,
        ifls: searchParams.ifls ? 1 : 0,
        sortColumn: searchParams.sortColumn,
        contrastSubject: searchParams.contrastSubject ? 1 : 0,
        IsBalanceZero: searchParams.IsBalanceZero ? 1 : 0,
        noAmountIncurredAndBalanceZero: searchParams.NoAmountIncurredAndBalanceZero ? 1 : 0,
        hiddenTotal: searchParams.HiddenTotal ? 1 : 0,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
        fcid: searchParams.fcid,
        txtDescription: searchParams.txtDescription,
    };
    loading.value = true;
    request({
        url: "/api/SubsidiaryLedger?" + getUrlSearchParams(params),
    })
        .then((res: IResponseModel<IResTableData>) => {
            loading.value = false;
            if (res.state === 1000) {
                tableData.value = res.data.rows;
                paginationData.total = res.data.total;
                unit.value = res.data.unit;
                setInfos();
            } else {
                asubInfo.value = "无";
            }
        })
        .catch(() => {
            tableData.value = [];
            paginationData.total = 0;
            loading.value = false;
        })
        .finally(() => {
            //第一次加载或者业财切换页面置顶
            if (tableLoadingCount === 1 || isErp.value) {
                nextTick().then(() => {
                    const mainCenter = document.querySelector(".main-center");
                    mainCenter?.scrollTo(0, 0);
                });
            }
            // 业财切换页面置顶
            if (isErp.value) {
                const mainCenter = document.querySelector(".main-center");
                mainCenter?.scrollTo(0, 0);
            }
            currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.period_s, searchInfo.period_e);
        });
};
const handVoucherBack = () => {
    currentSlot.value = "main";
    nextTick(() => {
        let ExpandedKeys = handleTreeExpand(treeList.value);
        treeRef.value?.setExpandedKeys(ExpandedKeys);
    });
};
const currentFcid = ref(-1);
function routerReplace() {
    const currentQuery = { ...route.query };
    delete currentQuery.ASUB_ID;
    delete currentQuery.period_s;
    delete currentQuery.period_e;
    router.replace({ query: currentQuery });
}
const handleSearch = () => {
    if (canSearch()) {
        if (route.query.ASUB_ID) {
            routerReplace();
        }
        adjustPeriod();
        periodStore.changePeriods(String(searchInfo.period_s), String(searchInfo.period_e));
        currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.period_s, searchInfo.period_e);
        searchVal.value = "";
        searchParams = _.cloneDeep(searchInfo);
        handleClose();
        if (paginationData.currentPage !== 1) {
            debugCanChangeCurrentPage();
            paginationData.currentPage = 1;
        }
        currentFcid.value = searchInfo.fcid;
        getTableList();
    }
};
//调整日期选择
const adjustPeriod = () => {
    if (searchInfo.period_s > searchInfo.period_e) {
        const temp = searchInfo.period_s;
        searchInfo.period_s = searchInfo.period_e;
        searchInfo.period_e = temp;
        periodPickerRef.value?.checkExchangePid();
    }
};
const getCurrencyList = async () => {
    const defaultList = [
        { 
            asId: 0,
            preName: "",
            id: -1,
            code: "",
            name: "综合本位币",
            rate: 1,
            isBaseCurrency: true,
            rateDecimal: "",
            rateSeparator: "",
            status: 0, 
        },
        { 
            asId: 0,
            preName: "",
            id: 0,
            code: "",
            name: "所有币别",
            rate: 1,
            isBaseCurrency: false,
            rateDecimal: "",
            rateSeparator: "",
            status: 0,
        },
    ];
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...defaultList, ...useCurrencyStore().fcList];
};
const judgeFcShow = () => {
    request({ url: "/api/AccountSubject/ExistsFc", method: "post" }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000) return;
        fcShow.value = res.data;
    });
};

const judgeNumberShow = () => {
    request({ url: "/api/AccountBooks/ExistsQutAsub", method: "post" }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000) return;
        numberComputedShow.value = res.data;
    });
};
let currentAsubIdStart = ref();
let currentAsubIdEnd = ref();
const handleAsubChange = (asubId: number, type: string) => {
    if (type === "startAsub") {
        currentAsubIdStart.value = asubId;
    } else if (type === "endAsub") {
        currentAsubIdEnd.value = asubId;
    }
};

//初始化加载
let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    if (childComponentFinishCount === 0) {
        const { AA_CODE, ASUB_ID, period_s, period_e } = route.query;
        const isRoute = ASUB_ID || AA_CODE || period_s || period_e;
        if (isRoute) return;
        childComponentFinishCount++;
        handleInit();
    }
});

let canChangeCurrentPage = true;
const debugCanChangeCurrentPage = () => {
    canChangeCurrentPage = false;
    const timer = setTimeout(() => {
        canChangeCurrentPage = true;
        clearTimeout(timer);
    }, 100);
};
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    setTimeout(() => {
        if (!canChangeCurrentPage) return;
        // window.localStorage.setItem("SubsidiaryLedgerSize", paginationData.pageSize + "");
        handleGetTableData();
    }, 10);
});

watch(showNumber, (val) => {
    // printInfo.imageDirection = val ? 1 : 0;
    printInfo.value.direction = val ? 1 : 0;
});

// 请求拦截
const canSearch = () => {
    if (searchInfo.sbj_id_s !== "" && searchInfo.sbj_id_e !== "" && searchInfo.sbj_id_s.split(" ")[0] > searchInfo.sbj_id_e.split(" ")[0]) {
        ElNotify({
            message: "亲，起始科目不能大于结束科目哦",
            type: "warning",
        });
        return false;
    }
    if (searchInfo.sbj_leval_s > searchInfo.sbj_leval_e) {
        ElNotify({
            message: "亲，起始科目级别不能大于结束科目级别哦",
            type: "warning",
        });
        return false;
    }
    return true;
};

// 信息
const setInfos = () => {
    const data: any = currentNode.value;
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.period_s, searchInfo.period_e);
    sbj_id.value = data.id;
    asubInfo.value = data.attributes.code + " " + data.attributes.name + " " + (data.attributes.aafullname || "");
    title.value = data.attributes.name ? data.attributes.name + " " : "";
};

const treeHeight = computed(() => {
    return !isErp.value ? (showNumber.value || (currentFcid.value !== -1 && currentFcid.value !== 1) ? "590px" : "560px") : "";
});

// watch(()=>route.params,()=>{
//     if(!isErp.value) return
//     const { AA_CODE, ASUB_ID, period_s, period_e } = route.query;
//     let isRoute = ASUB_ID && period_s && period_e && route.path === '/AccountBooks/SubsidiaryLedger';
//     if(!isRoute) return
//     if(route.query.AA_CODE !== aacode.value || route.query.ASUB_ID !== currentNode.value?.id.split('^')[0] || route.query.period_s != searchInfo.period_s+'' || route.query.period_e != searchInfo.period_e+'') {
//         aacode.value = route.query.AA_CODE ?? "";
//         searchParams.ifas = aacode.value ? true : false;
//         searchInfo.ifas = aacode.value ? true : false;
//         if (currentSlot.value === "add" && voucherHasChanged.value) {
//             confirmWithoutGlobalVisible.value = true;
//         } else {
//             searchInfo.period_s = Number(period_s);
//             searchInfo.period_e = Number(period_e);
//             searchParams.period_s = searchInfo.period_s;
//             searchParams.period_e = searchInfo.period_e;
//             sbj_id.value = String(ASUB_ID);
//             searchInfo.sbj_id_s = "";
//             searchInfo.sbj_id_e = "";
//             searchParams.sbj_id_s = searchInfo.sbj_id_s;
//             searchParams.sbj_id_e = searchInfo.sbj_id_e;
//             getTableList(true);
//         } 
//     }
// },{immediate:true})

onBeforeRouteLeave((to, from, next) => {
    const { ASUB_ID, period_s, period_e } = route.query;
    if (ASUB_ID && period_s && period_e && !isErp.value) {
        routerArrayStore.resetRouterQuery(currentPath.value);
    }
    next();
});
let cacheRan = ""
onActivated(() => {
    const { AA_CODE, ASUB_ID, period_s, period_e } = route.query;
    let isRoute = ASUB_ID && period_s && period_e;
    if(scrollTopTree) {
        requestAnimationFrame(() => {
            const treeDom = document.querySelector(`.tree-v2-scroll .el-tree .el-tree-virtual-list`);
            treeDom && (treeDom.scrollTop = scrollTopTree);
        });
    }
    if (!isRoute) return;
    if(route.query.AA_CODE !== aacode.value || route.query.ASUB_ID !== currentNode.value?.id.split('^')[0] || route.query.period_s != searchInfo.period_s+'' || route.query.period_e != searchInfo.period_e+'') {
        if(isErp.value) {
            if(route.query.ran && cacheRan === route.query.ran) return
        }
        aacode.value = route.query.AA_CODE ?? "";
        searchParams.ifas = aacode.value ? true : false;
        searchInfo.ifas = aacode.value ? true : false;
        if (currentSlot.value === "add" && voucherHasChanged.value) {
            confirmWithoutGlobalVisible.value = true;
        } else {
            if(period_s){
                searchInfo.period_s = Number(period_s);
                searchParams.period_s = searchInfo.period_s;
            }
            if(period_e){
                searchInfo.period_e = Number(period_e);
                searchParams.period_e = searchInfo.period_e;
            }
            sbj_id.value = String(ASUB_ID);
            searchInfo.sbj_id_s = "";
            searchInfo.sbj_id_e = "";
            searchParams.sbj_id_s = searchInfo.sbj_id_s;
            searchParams.sbj_id_e = searchInfo.sbj_id_e;
            if(childComponentFinishCount === 0){
                handleInit(true);
            }else{
                getTableList(true);
            }
        } 
        cacheRan = route.query.ran as string ?? ""
    }
});

const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
const isEditting = computed(() => {
    return currentSlot.value === "add" && checkVoucherRef.value!.getEditedState();
});
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
// 科目更新，刷新明细账表格
window.addEventListener("modifyaccountSubject", () => {
    getTableList();
});

// 切换回页面，数据刷新
const voucherHasChanged = ref(false);
// 凭证弹窗
const confirmWithoutGlobalVisible = ref(false);

const reLoadCurrentPage = () => {
    loading.value = true;
    currentSlot.value = "main";
    getTableListInner(true);
    voucherHasChanged.value = false;
};

let treeV2Height = 0;
watchEffect(() => {
    if (document.querySelector(".tree")) {
        treeV2Height = document.querySelector(".tree")!.clientHeight;
    }
});
onMounted(() => {
    searchInfo.period_s = Number(periodStore.getPeriodRange().start);
    searchInfo.period_e = Number(periodStore.getPeriodRange().end);
    judgeFcShow();
    judgeNumberShow();
    getCurrencyList();
    document.body.scrollTop = 0;
    treeV2Height = document.querySelector(".tree")!.clientHeight;
});
onUnmounted(() => {
    window.removeEventListener("modifyaccountSubject", () => {
        getTableList();
    });
});
// 展开和收起右侧树标志
const flag = reactive({
    expandFlag: true,
    closeFlag: true
});

const showfcList = ref<Array<IFcList>>([]);
watchEffect(() => { 
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));    
});
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'name');
}
</script>

<style lang="less" scoped>
@import "@/style/AccountBooks/SubsidiaryLedger.less";
@import "@/style/Functions.less";
@import "@/style//SelfAdaption.less";
@import "@/style/RightTreeExpand.less";

.line-item-field {
    &.fcid {
        .detail-el-select(132px);
    }
}
& .tree-icon {
    display: inline-block;
    vertical-align: top;
    height: 21px;
    width: 21px;
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: center;
}
& .is-expanded {
    & .tree-icon.tree-folder.tree-folder-open {
        background-image: url(@/assets/icons/folder-open.png);
    }
}
& .tree-icon.tree-folder.tree-folder-open {
    background-image: url(@/assets/icons/folder.png);
}
& .tree-icon.tree-file {
    background-image: url(@/assets/icons/file.png);
}
& .tree-title {
    color: var(--font-color);
    font-size: var(--h5);
    line-height: 21px;
    display: inline-block;
}
.no-data {
    width: 100%;
}
:deep(.custom-table tbody tr td .cell) {
    & span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.account-books-table {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    :deep(.el-scrollbar__bar.is-vertical) {
        top: 42px;
        right: 0;
        .el-scrollbar__thumb {
            margin-top: -40px;
        }
    }
}
.select-content {
    .select-info {
        .select-info-inner {
            :deep(.span_wrap) {
                overflow: hidden !important;
            }
        }
    }
}
.buttons {
    display: flex;
    justify-content: center;
}

//默认滚动条样式
.select-info {
    &::-webkit-scrollbar {
        width: 6px;
        padding-right: 2px;
        background-color: #fff;
    }
    &::-webkit-scrollbar-thumb {
        width: 6px;
        border-radius: 3px;
        background: #d0d0d0;
        &:hover {
            background-color: #b1b1b1;
        }
    }
}

.main-content {
    .main-top {
        .main-tool-left {
            .filter-tip {
                height: 28px;
                max-width: 1000px;
                border: 1px solid #ffdc6a;
                background-color: #fffbe6;
                display: flex;
                align-items: center;
                padding: 0px 15px;
                :deep(.span_wrap) {
                    display: flex !important;
                }
                .ellipsis {
                    max-width: 1000px;
                    display: block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
        .main-tool-right {
            :deep(.print-down.button.dropdown) {
                background-position: right 12px center;
            }
        }
    }
    .main-center {
        flex: 1;
        min-height: 0;
        .main-right {
            position: relative;
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            justify-content: stretch;
            .tree {
                flex: 1;
                overflow: hidden;
                box-sizing: border-box;
            }
            .select-info .select-info-inner {
                -webkit-line-clamp: 1;
                white-space: nowrap;
                display: block;
            }
        }
        .main-left {
            height: 100%;
            display: flex;
            flex-direction: column;
            min-width: 0;
            :deep(.el-table) {
                flex: 1;
                min-height: 0;
                .el-scrollbar__view {
                    // height: 100%;
                }
            }
        }
    }
}
:deep(.el-scrollbar) {
    &.tree-v2-scroll {
        padding-right: 0;
        .el-scrollbar__bar.is-vertical {
            display: none;
        }
        .el-scrollbar__wrap {
            margin-right: 10px;
        }
    }
}
:deep(.el-tree) {
    &.my-tree-v2 {
        height: 100%;
        position: static;
    }
    .el-virtual-scrollbar {
        opacity: 1;
    }
    .el-vl__wrapper {
        height: 100%;
        position: static;
        .el-tree-node__content .el-icon {
            flex-shrink: 0;
        }
    }
}
.flex {
    display: flex;
    .main-title-left {
        max-width: 94%;
    }
    .main-title-right {
        margin-left: 15px;
        max-width: 5%;
    }
    .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.virtual-list {
    &::-webkit-scrollbar-thumb {
        background-color: #c3c3c3;
        border-radius: 8px;
        &:hover {
            background-color: #b1b1b1;
        }
    }
    &::-webkit-scrollbar {
        background-color: white;
        width: 6px;
    }
    &::-webkit-scrollbar-track {
        background-color: white;
    }
    &::-webkit-scrollbar-corner {
        background-color: white;
    }
    &::-webkit-scrollbar-track-piece {
        background-color: white;
        width: 6px;
    }
}

// 业财样式兼容
body[erp] {
    .content {
        .main-content {
            .main-center {
                .main-right {
                    justify-content: stretch;
                    .tree {
                        height: 100%;
                        :deep(.el-tree) {
                            .el-tree-node__content {
                                height: 30px;
                            }
                        }
                        .tree-icon.tree-file {
                            width: 21px;
                            height: 21px;
                            background: url(@/assets/icons/file-erp.png) no-repeat 0 0;
                            background-size: contain;
                        }
                    }
                }
                :deep(.el-table) {
                    .el-table__inner-wrapper::before,
                    .el-table__inner-wrapper::after {
                        height: 0;
                    }
                    th.el-table__cell.is-leaf {
                        border-bottom: 0;
                    }
                }
            }
        }
        .slot-content.voucher {
            height: 100%;
            overflow-y: auto;
        }
    }
    .table.paging-show {
        :deep(.pagination) {
            margin-top: 0;
            border: 1px solid var(--border-color);
            border-top: none;
        }
    }
}
</style>
