<template>
    <div class="content">
        <div class="title">资产汇总</div>
        <!-- <div class="main-content"> -->
        <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleClick">
            <el-tab-pane label="按使用部门汇总" name="first">
                <div class="main-content tabs-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <span>会计期间：</span>
                            <DatePicker
                                class="faSummary-pick"
                                v-model:startPid="searchInfoFirst.startMonth"
                                v-model:endPid="searchInfoFirst.endMonth"
                                :clearable="false"
                                :editable="false"
                                :dateType="'month'"
                                :value-format="'YYYYMM'"
                                :label-format="'YYYY年MM月'"
                                :disabledDateStart="disabledDate"
                                :disabledDateEnd="disabledDate"
                            />
                            <div class="buttons" style="border: 0">
                                <a class="button solid-button ml-10" @click="handleSearch"
                                    >查询</a
                                >
                            </div>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <a class="button" @click="handleExport()" v-permission="['fasummary-canexport']">导出</a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <Table
                            :data="tableDataLeft"
                            :columns="columnsLeft"
                            :loading="loading"
                            :pageIsShow="true"
                            :page-sizes="paginationDepartment.pageSizes"
                            :page-size="paginationDepartment.pageSize"
                            :total="paginationDepartment.total"
                            :current-page="paginationDepartment.currentPage"
                            :scrollbar-show="true"
                            @size-change="handleSizeChangeDepartment"
                            @current-change="handleCurrentChangeDepartment"
                            @refresh="handleRerefreshDepartment"
                            :tableName="setModule"
                        ></Table>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="按资产类别汇总" name="second">
                <div class="main-content tabs-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <span>会计期间：</span>
                            <DatePicker
                                class="faSummary-pick"
                                v-model:startPid="searchInfoSecond.startMonth"
                                v-model:endPid="searchInfoSecond.endMonth"
                                :clearable="false"
                                :editable="false"
                                :dateType="'month'"
                                :value-format="'YYYYMM'"
                                :label-format="'YYYY年MM月'"
                                :disabledDateStart="disabledDate"
                                :disabledDateEnd="disabledDate"
                            />
                            <div class="buttons" style="border: 0">
                                <a
                                    class="button solid-button ml-10"
                                    @click="handleSearch"
                                    >查询</a
                                >
                            </div>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <a class="button" @click="handleExport()" v-permission="['fasummary-canexport']">导出</a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <Table
                            :data="tableDataRight"
                            :columns="columnsRight"
                            :loading="loading"
                            :pageIsShow="true"
                            :page-sizes="paginationType.pageSizes"
                            :page-size="paginationType.pageSize"
                            :total="paginationType.total"
                            :current-page="paginationType.currentPage"
                            :scrollbar-show="true"
                            @size-change="handleSizeChangeType"
                            @current-change="handleCurrentChangeType"
                            @refresh="handleRerefreshChangeType"
                            :tableName="setModule"
                        ></Table>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <!-- </div> -->
    </div>
</template>

<script lang="ts">
export default {
    name: "FASummary",
};
</script>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { usePagination } from "@/hooks/usePagination";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { reactive, ref, watch, onMounted } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { setColumns } from "./utils";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IPeriod } from "./types";
import { getGlobalToken } from "@/util/baseInfo";
import RefreshButton from "@/components/RefreshButton/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import { dayjs } from "element-plus";

const setModule = "FASummary";
const activeName = ref("first");
const {
    paginationData: paginationDepartment,
    handleCurrentChange: handleCurrentChangeDepartment,
    handleSizeChange: handleSizeChangeDepartment,
    handleRerefresh: handleRerefreshDepartment,
} = usePagination();
const {
    paginationData: paginationType,
    handleCurrentChange: handleCurrentChangeType,
    handleSizeChange: handleSizeChangeType,
    handleRerefresh: handleRerefreshChangeType,
} = usePagination();

const searchInfoFirst = reactive({
    startPid: "",
    endPid: "",
    startMonth: "",
    endMonth: "",
});
const searchInfoSecond = reactive({
    startPid: "",
    endPid: "",
    startMonth: "",
    endMonth: "",
});
const loading = ref(false);

function handleExport() {
    const params = {
        periodS: activeName.value == "first" ? searchInfoFirst.startPid : searchInfoSecond.startPid,
        periodE: activeName.value == "first" ? searchInfoFirst.endPid : searchInfoSecond.endPid,
        appasid: getGlobalToken(),
    };
    if (activeName.value == "first") {
        globalExport("/api/FASummary/ExportByDepartment?" + getUrlSearchParams(params));
    } else {
        globalExport("/api/FASummary/ExportByFAType?" + getUrlSearchParams(params));
    }
}

const periodList = ref<IPeriod[]>([]);
function getPeriodApi() {
    return request({
        url: `/api/FAPeriod/List?dpcFlag=false`,
        method: "get",
    });
}

function getCurrentPeriod() {
    return request({
        url: `/api/FAPeriod/Current`,
    });
}

function handleSearch() {
    let compareStart = activeName.value == "first" ? searchInfoFirst.startMonth : searchInfoSecond.startMonth;
    let compareEnd = activeName.value == "first" ? searchInfoFirst.endMonth : searchInfoSecond.endMonth;
    let startPid = periodList.value.find((item) => item.time === compareStart)?.pid || 0;
    let endPid = periodList.value.find((item) => item.time === compareEnd)?.pid || 0;
    if (startPid > endPid) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哦" });
        return;
    } else {
        if (activeName.value == "first") {
            searchInfoFirst.startPid = String(startPid);
            searchInfoFirst.endPid = String(endPid);
        } else {
            searchInfoSecond.startPid = String(startPid);
            searchInfoSecond.endPid = String(endPid);
        }
        getTableData();
    }
}
const columnsLeft = ref<Array<IColumnProps>>();
const columnsRight = ref<Array<IColumnProps>>();
const tableDataLeft = ref<any[]>([]);
const tableDataRight = ref<any[]>([]);

function getTableData() {
    loading.value = true;
    const params = {
        periodS: activeName.value == "first" ? searchInfoFirst.startPid : searchInfoSecond.startPid,
        periodE: activeName.value == "first" ? searchInfoFirst.endPid : searchInfoSecond.endPid,
        PageIndex: activeName.value == "first" ? paginationDepartment.currentPage : paginationType.currentPage,
        PageSize: activeName.value == "first" ? paginationDepartment.pageSize : paginationType.pageSize,
    };
    if (activeName.value == "first") {
        columnsLeft.value = setColumns("first", columnsLeft.value, columnsRight.value);
        request({
            url: `/api/FASummary/PagingListByDepartment?` + getUrlSearchParams(params),
        })
            .then((res: any) => {
                tableDataLeft.value = res.data.data;
                paginationDepartment.total = res.data.count;
            })
            .finally(() => (loading.value = false));
    } else {
        columnsRight.value = setColumns("second", columnsLeft.value, columnsRight.value);
        request({
            url: `/api/FASummary/PagingListByFAType?` + getUrlSearchParams(params),
        })
            .then((res: any) => {
                tableDataRight.value = res.data.data;
                paginationType.total = res.data.count;
            })
            .finally(() => (loading.value = false));
    }
}

let count = 0;
function handleClick() {
    if (count == 0) {
        getTableData();
        count++;
    }
}

watch([() => paginationDepartment.currentPage, () => paginationDepartment.pageSize, () => paginationDepartment.refreshFlag], getTableData);
watch([() => paginationType.currentPage, () => paginationType.pageSize, () => paginationType.refreshFlag], getTableData);

onMounted(() => {
    Promise.all([getPeriodApi(), getCurrentPeriod()]).then((res: any) => {
        if (res[0].state == 1000) {
            periodList.value = res[0].data.map((item: any) => {  
                const year = item.periodInfo.slice(0, 4);
                const sn = item.periodInfo.indexOf("月") === 7 ? item.periodInfo.slice(5, 7) : item.periodInfo.slice(5, 6) ;
                return {  
                    pid: item.pid,  
                    periodInfo: item.periodInfo,   
                    time: year + "" + sn.padStart(2, "0"),
                };  
            });
        }
        if (res[1].state === 1000) {
            searchInfoFirst.startPid = res[1].data.pid;
            searchInfoFirst.endPid = res[1].data.pid;
            searchInfoSecond.startPid = res[1].data.pid;
            searchInfoSecond.endPid = res[1].data.pid;
        }

        searchInfoFirst.startMonth = periodList.value.find((item) => item.pid === Number(searchInfoFirst.startPid))?.time || "";
        searchInfoFirst.endMonth = periodList.value.find((item) => item.pid === Number(searchInfoFirst.endPid))?.time || "";
        searchInfoSecond.startMonth = periodList.value.find((item) => item.pid === Number(searchInfoSecond.startPid))?.time || "";
        searchInfoSecond.endMonth = periodList.value.find((item) => item.pid === Number(searchInfoSecond.endPid))?.time || "";
        getTableData();
    });
});

function disabledDate(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
@import "@/style/FixedAssets/FixedMulti-tabs.less";

:deep(.el-tabs__header) {
    background-color: var(--white);
}
//多页签
.content {
    :deep(.el-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__content {
            flex: 1;
        }
        .el-tab-pane {
            height: 100%;
        }
    }
}
:deep(.picker) {
    &.faSummary-pick {
        .el-input {
            width: 122px;
            .el-input__wrapper {
                width: 122px;
            }
        }
        .ml-10 {
            margin-left: 5px;
        }
        .mr-10 {
            margin-right: 5px;
        }
    }
}
</style>

<style lang="less">
body[erp] .tabs-content {
    height: calc(100vh - 48px) !important;
}
</style>
