[{"functionXh": "1b9fce98b9f04eff867abf41b969f2c5", "formXh": null, "functionDm": "substr", "functionMc": "substr", "functionType": "GLOBAL", "functionBody": "function (value, startIndex, endIndex) {\r\n      return String.prototype.substr.call(value, startIndex, endIndex);\r\n    }", "description": null, "dependencies": null}, {"functionXh": "27118326006d3829667a400ad23d5d98", "formXh": null, "functionDm": "String", "functionMc": "String", "functionType": "GLOBAL", "functionBody": "function (value) {\r\n      return String(value);\r\n    }", "description": null, "dependencies": null}, {"functionXh": "27226c864bac7454a8504f8edb15d95b", "formXh": null, "functionDm": "Boolean", "functionMc": "Boolean", "functionType": "GLOBAL", "functionBody": "function (value) {\r\n      return Boolean(value);\r\n    }", "description": null, "dependencies": null}, {"functionXh": "2c7317cc06f5437e87efc2acac028506", "formXh": null, "functionDm": "validIDCardNo", "functionMc": "validIDCardNo", "functionType": "GLOBAL", "functionBody": "function validIDCardNo(val) {\n    // 是否为身份证 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X\n    var isCardNo = function (val) {\n        return /(^\\d{15}$)|(^\\d{17}(\\d|X)$)/.test(val);\n    };\n\n    // 15位转18位身份证号\n    var changeFivteenToEighteen = function (card) {\n        if (card.length == '15') {\n            var arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];\n            var arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];\n            var cardTemp = 0;\n            // FIXME: 长度15是否一律按19xx处理\n            card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6);\n            for (var i = 0; i < 17; i++) {\n                cardTemp += card.substr(i, 1) * arrInt[i];\n            }\n            card += arrCh[cardTemp % 11];\n            return card;\n        }\n        return card;\n    };\n\n    // 出生日期码校验\n    var checkDate = function (val) {\n        var pattern = /^(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)$/;\n        if (pattern.test(val)) {\n            var year = val.substring(0, 4);\n            var month = val.substring(4, 6);\n            var date = val.substring(6, 8);\n            var date2 = new Date(year + '-' + month + '-' + date);\n            if (date2 && date2.getMonth() == parseInt(month) - 1) {\n                return true;\n            }\n        }\n        return false;\n    };\n\n    // 校验码校验\n    var checkCode = function (val) {\n        var p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\n        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];\n        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];\n        var code = val.substring(17);\n        if (p.test(val)) {\n            var sum = 0;\n            for (var i = 0; i < 17; i++) {\n                sum += val[i] * factor[i];\n            }\n            if (parity[sum % 11] == code.toUpperCase()) {\n                return true;\n            }\n        }\n        return false;\n    };\n\n    // 省级地址码校验\n    var checkProv = function (val) {\n        var pattern = /^[1-9][0-9]/;\n        var provs = {\n            11: '北京',\n            12: '天津',\n            13: '河北',\n            14: '山西',\n            15: '内蒙古',\n            21: '辽宁',\n            22: '吉林',\n            23: '黑龙江 ',\n            31: '上海',\n            32: '江苏',\n            33: '浙江',\n            34: '安徽',\n            35: '福建',\n            36: '江西',\n            37: '山东',\n            41: '河南',\n            42: '湖北 ',\n            43: '湖南',\n            44: '广东',\n            45: '广西',\n            46: '海南',\n            50: '重庆',\n            51: '四川',\n            52: '贵州',\n            53: '云南',\n            54: '西藏 ',\n            61: '陕西',\n            62: '甘肃',\n            63: '青海',\n            64: '宁夏',\n            65: '新疆',\n            71: '台湾',\n            81: '香港',\n            82: '澳门',\n            83: '台湾',\n        };\n        if (pattern.test(val)) {\n            if (provs[val]) {\n                return true;\n            }\n        }\n        return false;\n    };\n    if (!isCardNo(val)) {\n        return false;\n    }\n\n    if (val.length === 15) {\n        return validIDCardNo(changeFivteenToEighteen(val));\n    }\n\n    if (checkCode(val)) {\n        var date = val.substring(6, 14);\n\n        if (checkDate(date)) {\n            if (checkProv(val.substring(0, 2))) {\n                return true;\n            }\n        }\n    }\n\n    return false;\n}", "description": null, "dependencies": null}, {"functionXh": "38fa9974d25d46d8aef7147f2499da51", "formXh": null, "functionDm": "max", "functionMc": "max", "functionType": "GLOBAL", "functionBody": "function () {\n      return Math.max.apply(this, arguments);\n    }", "description": null, "dependencies": null}, {"functionXh": "3c6b46d62e114f25acecd8ab84643c15", "formXh": null, "functionDm": "min", "functionMc": "min", "functionType": "GLOBAL", "functionBody": "function () {\n      return Math.min.apply(this, arguments);\n    }", "description": null, "dependencies": null}, {"functionXh": "3c756b7a056144ce959b4fba49fde3b4", "formXh": null, "functionDm": "sumList", "functionMc": "sumList", "functionType": "GLOBAL", "functionBody": "function (list, key, start, end, sortKey) {\r\n      if(start === undefined){\r\n        start = 0;\r\n      }\r\n      if(end === undefined){\r\n        end = (list || []).length - 1;\r\n      }\r\n      if(sortKey != undefined && typeof sortKey == \"string\"){\r\n        var ret = [];\r\n        for (var i = list.length-1; i >= 0; i--) {\r\n          ret[list[i][sortKey]] = list[i];\r\n        }\r\n        list = ret;\r\n      }\r\n      for (var i = end, sum = 0; i >= start; i--) {\r\n        sum += Number(list[i][key]);\r\n      }\r\n      return sum;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "3c756b7a056144ce959b4fba49fde3b5", "formXh": null, "functionDm": "isInteger", "functionMc": "isInteger", "functionType": "GLOBAL", "functionBody": "function isInteger(value){\r\n      if(Math && Math.isInteger){\r\n        return Math.isInteger(value)\r\n      }\r\n      return (typeof value == \"number\" || typeof value == \"string\") && value % 1 ===0\r\n    }", "description": null, "dependencies": null}, {"functionXh": "60a236bc586b4c8bb4fa13135805baaa", "formXh": null, "functionDm": "round", "functionMc": "round", "functionType": "GLOBAL", "functionBody": "function(value,decimal){var addZero=function(changeNum,origin,s){var index=changeNum.indexOf(\".\");if(index<0&&s>0){changeNum=changeNum+\".\";for(var i=0;i<s;i++){changeNum=changeNum+\"0\"}}else{index=changeNum.length-index;for(var j=0;j<(s-index)+1;j++){changeNum=changeNum+\"0\"}}if(origin<0&&Number(changeNum)!==0){if(Number(s)>0){return\"-\"+changeNum}else{return -changeNum}}else{return changeNum}};var sentificToStr=function(origin){var mtc=(origin+\"\").match(/(\\d)(?:\\.(\\d*))?e([+-]\\d+)/);if(mtc){var num=mtc[1]+(mtc[2]||\"\");if(Number(mtc[3])>0){for(var mm=0;mm<Number(mtc[3])-(mtc[2]||\"\").length;mm++){num+=\"0\"}}else{var num1=\"0.\";for(var mm=0;mm<-Number(mtc[3])-1;mm++){num1+=\"0\"}num=num1+num}origin=num}return origin.toString()};var digitLength=function(num){var eSplit=num.toString().split(/[eE]/);var len=(eSplit[0].split(\".\")[1]||\"\").length-(+(eSplit[1]||0));return len>0?len:0};var getNumberFullLen=function(origin){return origin.toString().replace(\".\",\"\").replace(/^0+/,\"\").length};var strip=function(origin,precision,targetDigit){var defaultPrecision=16;if(Number(origin.toPrecision(defaultPrecision).toString().replace(\".\",\"\").replace(/^0+/,\"\"))>Number.MAX_SAFE_INTEGER){defaultPrecision=15}if(Number(origin.toString().replace(\".\",\"\").replace(/^0+/,\"\"))>Number.MAX_SAFE_INTEGER&&Number(origin.toString().substr(origin.toString().length-1))<5){defaultPrecision=getNumberFullLen(origin)-2}precision=precision||defaultPrecision;if(getNumberFullLen(origin)<precision){return origin}var strippedNum=parseFloat(origin.toPrecision(precision));if(getNumberFullLen(strippedNum)===precision&&targetDigit+6<digitLength(strippedNum)){return strip(strippedNum,precision-1,targetDigit)}else{return strippedNum}};var _nativeToFixed=function(origin,s){var e,changeNum,index,i,j;if(origin<0){e=-origin}else{e=origin}var digitLen=digitLength(e);if(digitLen===s&&s>0){return addZero(sentificToStr(e.toString()),origin,s)}else{if(digitLen<s&&s>0){changeNum=e}else{changeNum=strip(e,undefined,s)}}changeNum=sentificToStr(changeNum);var zeros=\"\";if(((changeNum+\"\").split(\".\")[1]||\"\").length<s){zeros=new Array(s+1-((changeNum+\"\").split(\".\")[1]||\"\").length).join(\"0\")}var changeNumPowStr=(changeNum+\"\").split(\".\")[0]+(((changeNum+\"\").split(\".\")[1]||\"\").substr(0,s)+zeros)+\".\"+(zeros?\"\":(((changeNum+\"\").split(\".\")[1]||\"\").substr(s)));var changeNumPow=Number(changeNumPowStr)+0.5;changeNumPow=sentificToStr(changeNumPow);if(digitLength(changeNumPow)>1){changeNumPow=strip(Number(changeNumPow),undefined,1)}zeros=\"\";if((changeNumPow+\"\").split(\".\")[0].length-1<s){zeros=new Array(s+1-(changeNumPow+\"\").split(\".\")[0].length).join(\"0\")}changeNumPowStr=(zeros?\"0\":(changeNumPow+\"\").split(\".\")[0].substr(0,(changeNumPow+\"\").split(\".\")[0].length-s))+\".\"+zeros+(changeNumPow+\"\").split(\".\")[0].substr((zeros?0:(changeNumPow+\"\").split(\".\")[0].length-s));changeNum=(sentificToStr(Number(changeNumPowStr))).toString();return addZero(changeNum,origin,s)};return parseFloat(_nativeToFixed(Number(value),decimal))};", "description": null, "dependencies": null}, {"functionXh": "77c0aff825dbb4b8c94c4221d1c6d12a", "formXh": null, "functionDm": "doCatch", "functionMc": "doCatch", "functionType": "GLOBAL", "functionBody": "function (expression, defaultValue) {\r\n      var r = typeof defaultValue == \"undefined\" ? 0: defaultValue;\r\n      try{\r\n        r = engineJs.custom(engineJs.content,expression) || r;\r\n      }catch(e){\r\n        return r;\r\n      }\r\n      return r;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "7f3c852cba2a4ed1a8dfb6f8160f5e27", "formXh": null, "functionDm": "abs", "functionMc": "abs", "functionType": "GLOBAL", "functionBody": "function (value) {\n      return Math.abs(value);\n    }", "description": null, "dependencies": null}, {"functionXh": "81e82f42dc3f019b6ad9e22ae97f72fb", "formXh": null, "functionDm": "rangeExec", "functionMc": "rangeExec", "functionType": "GLOBAL", "functionBody": "function (start,end, expression, xhKey) {\r\n      var ret = [];\r\n      var listPath = null;\r\n      var xhMap = {};\r\n      if(typeof end == \"string\"){\r\n         xhKey = expression;\r\n        expression = end;\r\n        end = undefined;\r\n      }\r\n      if(xhKey){\r\n        listPath = expression.match(/(ywbw\\.[^\\ ]*?)\\[\\*\\]/)[1];\r\n        var list = engineJs.custom(engineJs.content,listPath);\r\n        for (var k = 0; k < list.length; k++) {\r\n          xhMap[list[k][xhKey]] = k;\r\n        }\r\n      }\r\n      var getIndex = function(xhKey,xhValue){\r\n        return xhMap[xhValue]\r\n      }\r\n      var run = function(i){\r\n        return engineJs.custom(engineJs.content,expression.replace(/\\[\\*\\]/gi,\"[\"+i+\"]\").replace(/(['\"])(\\*)\\1/g,\"$1\"+i+\"$1\"))\r\n      }\r\n      if(end){\r\n        for(var i = start;i<end;i++){\r\n          ret[i] = run(xhK<PERSON>?getIndex(xhK<PERSON>,i):i);\r\n        }\r\n      }else if(typeof start == \"object\" && start.length){\r\n        for(var i = 0;i<start.length;i++){\r\n          ret[start[i]] =run(xhKey?getIndex(xhKey,start[i]):start[i]);\r\n        }\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "904454a324354d2dbdf520d850dcf412", "formXh": null, "functionDm": "getObjFromList", "functionMc": "getObjFromList", "functionType": "GLOBAL", "functionBody": "function (list, key, value) {\r\n      for(var i=0;i<list.length;i++){\r\n        if(typeof list[i][key] == \"number\"){\r\n          if(list[i][key] === Number(value)){\r\n            return list[i];\r\n          }\r\n        }else{\r\n          if(list[i][key] === value){\r\n            return list[i];\r\n          }\r\n        }\r\n        \r\n      }\r\n      return null;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "99df2146e4ac47989faa09b27dc9b002", "formXh": null, "functionDm": "<PERSON><PERSON><PERSON><PERSON>", "functionMc": "<PERSON><PERSON><PERSON><PERSON>", "functionType": "GLOBAL", "functionBody": "function (value) {\n      return value.length;\n    }", "description": null, "dependencies": null}, {"functionXh": "a7598c5c55964b7789835109043daa8d", "formXh": null, "functionDm": "getIf", "functionMc": "getIf", "functionType": "GLOBAL", "functionBody": "function (condition, trueVal, falseVal) {\r\n      if(arguments.length<=2){\r\n        falseVal = true;\r\n      }\r\n      // falseVal = falseVal === undefined?true:falseVal\r\n      return condition ? trueVal : falseVal;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "b2ee912b91d69b435159c7c3f6df7f5f", "formXh": null, "functionDm": "Number", "functionMc": "Number", "functionType": "GLOBAL", "functionBody": "function (value) {\r\n      return Number(value) || 0;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "bd4f997ba19147f293373817244a881b", "formXh": null, "functionDm": "count<PERSON><PERSON><PERSON>", "functionMc": "count<PERSON><PERSON><PERSON>", "functionType": "GLOBAL", "functionBody": "function countChars(str,unit) {\n  var length = 0;\n  for (var i = 0; i < str.length; i++) {\n    var charCode = str.charCodeAt(i);\n    length += (charCode >= 0x4E00 && charCode <= 0x9FFF) || charCode > 0xFF00 ? unit : 1;\n  }\n  return length;\n}", "description": null, "dependencies": null}, {"functionXh": "cbe0396fee5842c98666a1eeead2392c", "formXh": null, "functionDm": "sum", "functionMc": "sum", "functionType": "GLOBAL", "functionBody": "function () {\n      for (var i = arguments.length - 1, sum = 0; i >= 0; i--) {\n        sum += arguments[i];\n      }\n      return sum;\n    }", "description": null, "dependencies": null}, {"functionXh": "e06046e1b90a47ceacaf81e482e39f49", "formXh": null, "functionDm": "customGet_checkObjExist", "functionMc": "customGet_checkObjExist", "functionType": "GLOBAL", "functionBody": "function customGet_checkObjExist(key, funcName, params) {\n  if (!this.ywbw[key] && funcName) {\n    this.ywbw[key] = this[funcName].call(this, params);\n  }\n  return true;\n}", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc001", "formXh": null, "functionDm": "execExpressionWithDep", "functionMc": "execExpressionWithDep", "functionType": "GLOBAL", "functionBody": "function execExpressionWithDep(expression, dep) {\n  return new Function('with(this){return '.concat(expression.replace(/ /gi, ''), '}')).call(this);\n}", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc819", "formXh": null, "functionDm": "trim", "functionMc": "trim", "functionType": "GLOBAL", "functionBody": "function (value) {\n      return value.replace(/^s+/, \"\");\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc820", "formXh": null, "functionDm": "checkRepeat", "functionMc": "checkRepeat", "functionType": "GLOBAL", "functionBody": "function checkRepeat(list) {\r\n      var o = {},\r\n        i;\r\n    \r\n      for (\r\n        var _len = arguments.length,\r\n          keys = new Array(_len > 1 ? _len - 1 : 0),\r\n          _key = 1;\r\n        _key < _len;\r\n        _key++\r\n      ) {\r\n        keys[_key - 1] = arguments[_key];\r\n      }\r\n    \r\n      for (i = 0; i < list.length; i++) {\r\n        var ret = \"\";\r\n    \r\n        for (var j = 0; j < keys.length; j++) {\r\n          if(list[i][keys[j]]){ret += list[i][keys[j]];}\r\n        }\r\n        if(!ret)continue;\r\n        if (o[ret]) return true;\r\n        o[ret] = true;\r\n      }\r\n    \r\n      return false;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc821", "formXh": null, "functionDm": "judgeTime", "functionMc": "judgeTime", "functionType": "GLOBAL", "functionBody": "function judgeTime(time1, time2) {\n      var expression =\n        arguments.length > 2 && arguments[2] !== undefined\n          ? arguments[2]\n          : \"==\";\n      var format =\n        arguments.length > 3 && arguments[3] !== undefined\n          ? arguments[3]\n          : \"yyyy-MM-dd\";\n\n      return eval(\n        this.parseDate(time1, format).getTime() +\n          expression +\n          this.parseDate(time2, format).getTime()\n      );\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc827", "formXh": null, "functionDm": "parseDate", "functionMc": "parseDate", "functionType": "GLOBAL", "functionBody": "function parseDate(str, fmt) {\r\n      fmt = fmt || \"yyyy-MM-dd\";\r\n      var obj = {\r\n        y: 0,\r\n        M: 1,\r\n        d: 0,\r\n        H: 0,\r\n        h: 0,\r\n        m: 0,\r\n        s: 0,\r\n        S: 0,\r\n      };\r\n      fmt.replace(\r\n        /([^yMdHmsS]*?)(([yMdHmsS])\\3*)([^yMdHmsS]*?)/g,\r\n        function (m, $1, $2, $3, $4, idx, old) {\r\n          str = str.replace(\r\n            new RegExp($1 + \"(\\\\d{\" + $2.length + \"})\" + $4),\r\n            function (_m, _$1) {\r\n              obj[$3] = parseInt(_$1);\r\n              return \"\";\r\n            }\r\n          );\r\n          return \"\";\r\n        }\r\n      );\r\n      obj.M--; // 月份是从0开始的，所以要减去1\r\n\r\n      var date = new Date(obj.y, obj.M, obj.d, obj.H, obj.m, obj.s);\r\n      if (obj.S !== 0) date.setMilliseconds(obj.S); // 如果设置了毫秒\r\n\r\n      return date;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc828", "formXh": null, "functionDm": "checkTimeByNSQXDM", "functionMc": "checkTimeByNSQXDM", "functionType": "GLOBAL", "functionBody": "function checkTimeByNSQXDM(time1, time2, nsqxdm, format) {\n      format = format || \"yyyy-MM-dd\";\n      nsqxdm = Number(nsqxdm);\n      // 06 月 08季 09 半年 10 年 11 次\n      time1 = this.parseDate(time1, format);\n      time2 = this.parseDate(time2, format);\n      var sameDay = time1.getTime() == time2.getTime();\n      var sameYear = time1.getFullYear() == time2.getFullYear();\n      if (nsqxdm == 11 && sameDay) return true;\n      if (\n        nsqxdm == 10 &&\n        sameYear &&\n        (Math.abs(time1.getTime() - time2.getTime()) / (24 * 3600 * 1000) ==\n          365 ||\n          Math.abs(time1.getTime() - time2.getTime()) / (24 * 3600 * 1000) ==\n            366)\n      )\n        return true;\n      if (\n        nsqxdm == 9 &&\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 5 && \n        [0,6].indexOf(time1.getMonth()) != -1\n      )\n        return true;\n      if (\n        nsqxdm == 8 &&\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 2 &&\n        [0,3,6,9].indexOf(time1.getMonth()) != -1\n      )\n        return true;\n      if (\n        nsqxdm == 6 &&\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 0\n      )\n        return true;\n      return false;\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc829", "formXh": null, "functionDm": "checkTime", "functionMc": "checkTime", "functionType": "GLOBAL", "functionBody": "function checkTime(time1, format) {\r\n      format = format || \"yyyy-MM-dd\";\r\n      var map = {\r\n        \"yyyy-MM-dd\":/^20(((([248][048])|([13579][26]))\\-(((0[13578]|1[02])\\-([0-2][0-9]|3[01]))|((0[469]|11)\\-([0-2][0-9]|30))|(02\\-([0-2][0-9]))))|((([248][1-35-79])|([13579][013-57-9]))\\-(((0[13578]|1[02])\\-([0-2][0-9]|3[01]))|((0[469]|11)\\-([0-2][0-9]|30))|(02\\-(((0|1)[0-9])|(2[0-8]))))))$/\r\n      }\r\n\r\n      if(!map[format]){return false};\r\n      return map[format].test(time1)\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc830", "formXh": null, "functionDm": "inTimeRange", "functionMc": "inTimeRange", "functionType": "GLOBAL", "functionBody": "function inTimeRange(time1, time2, time3, format) {\n      format = format || \"yyyy-MM-dd\";\n      time1 = this.parseDate(time1, format);\n      time2 = this.parseDate(time2, format);\n      time3 = this.parseDate(time3, format);\n      time1 = time1.getTime();\n      time2 = time2.getTime();\n      time3 = time3.getTime();\n\n      return (\n        (time3 >= time1 && time3 <= time2) || (time3 >= time2 && time3 <= time1)\n      );\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc831", "formXh": null, "functionDm": "getListFromList", "functionMc": "getListFromList", "functionType": "GLOBAL", "functionBody": "function (list, key, values) {\r\n      if(!Array.isArray(values)){\r\n        values = [values];\r\n      }\r\n      var ret = [];\r\n      var m = {};\r\n      for(var i=0;i<values.length;i++){\r\n        m[values[i]] = true;\r\n      }\r\n      for(var i=0;i<list.length;i++){\r\n        if(m[list[i][key]]){\r\n          ret.push(list[i])\r\n        }\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc832", "formXh": null, "functionDm": "inArray", "functionMc": "inArray", "functionType": "GLOBAL", "functionBody": "function (list, value) {\r\n      for(var i=0;i<list.length;i++){\r\n        if(list[i] === value){\r\n          return true\r\n        }\r\n      }\r\n      return false;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc833", "formXh": null, "functionDm": "getMonthByTime", "functionMc": "getMonthByTime", "functionType": "GLOBAL", "functionBody": "function (time, format) {\n      format = format || \"yyyy-MM-dd\";\n      time = this.parseDate(time,format);\n      return time.getMonth()+1;\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc834", "formXh": null, "functionDm": "getIndexFromList", "functionMc": "getIndexFromList", "functionType": "GLOBAL", "functionBody": "function getIndexFromList(list, key, values) {\r\n      var ret = [];\r\n      var m = {};\r\n      for(var i=0;i<list.length;i++){\r\n        m[list[i][key]] = i;\r\n      }\r\n      for(var i=0;i<values.length;i++){\r\n        ret.push(m[values[i]]);\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc835", "formXh": null, "functionDm": "getListFromListByKey", "functionMc": "getListFromListByKey", "functionType": "GLOBAL", "functionBody": "function (list, key) {\r\n      var ret = [];\r\n      for(var i=0;i<list.length;i++){\r\n        if(list[i] && list[i][key]){\r\n          ret.push(list[i][key]);\r\n        }\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc920", "formXh": null, "functionDm": "checkFbzlMustUploaded", "functionMc": "checkFbzlMustUploaded", "functionType": "GLOBAL", "functionBody": "function checkFbzlMustUploaded(xmlRoot){\r\n      var fbzl = this.doCatch(\"hd.fbzlxx.fbzl\",[]);\r\n      var fbzlVO = this.doCatch(xmlRoot+\".fbzlContent.fbzlVO\",[]);\r\n      for (var i = 0; i < fbzl.length; i++) {\r\n        if(fbzl[i].bslxDm === \"1\"){\r\n          for (var j = 0; j < fbzlVO.length; j++) {\r\n            if(fbzlVO[j].fbzlDm === fbzl[i].fbzlDm){\r\n              if(fbzlVO[j].yscbz == \"Y\" )return true;\r\n              return fbzlVO[j].files && fbzlVO[j].files.length > 0 || false\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return false\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc921", "formXh": null, "functionDm": "checkFbzlUploaded", "functionMc": "checkFbzlUploaded", "functionType": "GLOBAL", "functionBody": "function checkFbzlUploaded(fbzlDmArr, condition, validType, xmlRoot){\r\n      var hd = this.hd;\r\n      validType = validType || \"one\";\r\n      if(!condition)return true;\r\n      var sbzlxlcode = hd.sbzlxlcode;\r\n      var m = {};\r\n      var fbzlVO = this.doCatch(xmlRoot+\".fbzlContent.fbzlVO\",[]);\r\n      var runList = [];\r\n      var passCount = 0;\r\n      for (var i = 0; i < fbzlDmArr.length; i++) {\r\n        m[sbzlxlcode + fbzlDmArr[i]] = true;\r\n      }\r\n      for (var i = 0; i < fbzlVO.length; i++) {\r\n        if(m[fbzlVO[i].fbzlDm]){\r\n          runList.push(fbzlVO[i]);\r\n        }\r\n      }\r\n      for (var i = 0; i < runList.length; i++) {\r\n        if(runList[i].yscbz == \"Y\" ){\r\n          passCount ++;\r\n        }else if(runList[i].files && runList[i].files.length > 0 || false){\r\n          passCount ++;\r\n        }\r\n        if(validType == \"one\" && passCount)return true;\r\n      }\r\n      return passCount == fbzlDmArr.length;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "043d56b5af964495a26fc6027fb547bd", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_phjmse_gs", "functionMc": "customGet_phjmse_gs", "functionType": "diy", "functionBody": "function customGet_phjmse_gs(phjmse, ybzzs, zzsxejmje, bqynsfe, jme, phjzbl) {\n  var sum = ybzzs + zzsxejmje;\n  var result = (bqynsfe - jme) * phjzbl*0.01;\n  if (sum < 0 && (phjmse < result || phjmse > 0)) {\n    return false;\n  }\n  return true;\n}", "description": "1）当第一列“增值税税额”+第二列“增值税限额减免金额”<0时：\n按公式计算的值", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*]\",\"ywbw.sbFjsf.sbFjsfMx[*].phjmse\",\"ywbw.sbFjsf.sbFjsfMx[*].ybzzs\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\",\"ywbw.sbFjsf.sbFjsfMx[*].phjzbl\"]"}, {"functionXh": "0883ac72c17540a19959bbcc7bf55e9e", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sumA", "functionMc": "customGet_sumA", "functionType": "diy", "functionBody": "function customGet_sumA() {\n      var result = this.round(this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').ckmsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').msxse, 2) + this.round(this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').msxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').ckmsxse, 2);\n      return result;\n  }", "description": "免税标准判断：\n A: “本期数”的【“货物及劳务”+“服务、不动产和无形资产”】的第1栏＋第4栏＋第7栏＋第9栏＋第13栏之和", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse\"]"}, {"functionXh": "09d446df756a4d4a98cb054ff3c6f847", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sum10", "functionMc": "customGet_sum10", "functionType": "diy", "functionBody": " function customGet_sum10() {\n    var result = this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').xwqymsxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').xwqymsxse;\n    return result;\n}", "description": "第10栏1+2列", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '1').xwqymsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '2').xwqymsxse\"]"}, {"functionXh": "0a1296bac2fe40a98e3261d66373b8af", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Bqmse_col2Init", "functionMc": "customGet_Bqmse_col2Init", "functionType": "diy", "functionBody": "function customGet_Bqmse_col2Init() {\n\tvar result = 0;\n\tvar fzsbxx = this.fzxx.tzxx.fzsbxx; \n\tvar qzdbz = this.fzxx.tzxx.fzsbxx.qzdType;\n\txwqymse = this.customGet_Xwqymse_col2Init();\n\twdqzdmse = this.customGet_Wdqzdmse_col2Init();\n\tvar msehj = this.customGet_msehj();\n    if (qzdbz === 1) {\n    result = this.round(xwqymse + wdqzdmse,2);\n   }\n   else if(qzdbz === 2){\n      result = this.round(xwqymse + wdqzdmse + fzsbxx.qtfpBdc0xse * 0.05,2);\n   }\n   else{\n     result = this.max(this.round(msehj - this.round(this.round(fzsbxx.qtfpHwjlw0xsehj + fzsbxx.wpfpHwjlw0xsehj,2) * 0.03,2),2),0);\n   }\n\n\treturn result;\n}", "description": "主表19栏第2列初始化", "dependencies": "[\"customGet_Xwqymse_col2Init\",\"customGet_Wdqzdmse_col2Init\",\"customGet_msehj\",\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.qtfpBdc0xse\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj\"]"}, {"functionXh": "0b17e3591655428aae669f365c535d01", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Wdqzdxse_col1Init", "functionMc": "customGet_Wdqzdxse_col1Init", "functionType": "diy", "functionBody": "function customGet_Wdqzdxse_col1Init() {\n var result = 0;\n var fzxx = this.fzxx; \n var ytxx = this.ytxx; \n var qzdbz = fzxx.tzxx.fzsbxx.qzdType;\n var gtjy = fzxx.bqxx.gtjy;\n     if(gtjy === 'Y' && (qzdbz === 1 || qzdbz === 2)){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw3xsehj,2);\n  }\n return result;\n}", "description": "主表11栏第1列初始化赋值", "dependencies": "[\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj\",fzxx.tzxx.fzsbxx.wpfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw3xsehj\"]"}, {"functionXh": "0c61035624af462ab56a67bd1247cced", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsJsyjTsxx", "functionMc": "customGet_fjsJsyjTsxx", "functionType": "diy", "functionBody": "function customGet_fjsJsyjTsxx(zsxmDm) {\n\tresult=''\n    if (zsxmDm === '10109') {\n        result = '《附表二 附加税费情况表》城市维护建设税的增值税税额需等于主表第24行\"本期应补（退）税额 \"第1、2列之和';\n    } \n\tif (zsxmDm === '30203') {\n        result = '《附表二 附加税费情况表》教育费附加的增值税税额需等于主表第24行\"本期应补（退）税额 \"第1、2列之和，或《附表二 附加税费情况表》教育费附加的增值税税额需等于主表第24行\"本期应补（退）税额 \"第1、2列之和加代开专票增值税税额';\n    }\n\tif (zsxmDm === '30216') {\n        result = '《附表二 附加税费情况表》地方教育费附加的增值税税额需等于主表第24行\"本期应补（退）税额 \"第1、2列之和，或《附表二 附加税费情况表》教育费附加的增值税税额需等于主表第24行\"本期应补（退）税额 \"第1、2列之和加代开专票增值税税额';\n    }\n    return result;\n}", "description": "附加税增值税税额校验提示信息", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].zsxmDm\"]"}, {"functionXh": "13a5867749d04c758636b27af6b6a3a0", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Wdqzdxse_col2Init", "functionMc": "customGet_Wdqzdxse_col2Init", "functionType": "diy", "functionBody": "function customGet_Wdqzdxse_col2Init() {\n var result = 0;\n var fzxx = this.fzxx; \n var ytxx = this.ytxx; \n var qzdbz = fzxx.tzxx.fzsbxx.qzdType;\n var gtgsh = fzxx.bqxx.gtgsh;\n     if(gtgsh === 'Y' && qzdbz === 2){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdcxse,2);\n } else if(gtgsh === 'Y' && qzdbz === 1){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2);\n }\n return result;\n }", "description": "主表11栏第2列初始化赋值", "dependencies": "['']"}, {"functionXh": "1a9e6265727d4e449661b3c9ba07d2b2", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_tysbtstx", "functionMc": "customGet_tysbtstx", "functionType": "diy", "functionBody": " function customGet_tysbtstx() {\n    var inDate = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\n var sytysbxejmqy = this.fzxx.bqxx.sytysbxejmqy;\n var gtgsh = this.fzxx.bqxx.gtgsh;\n var result = '';\n var jmb = 0;\n var fb5 = 0;\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n var VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011804' || VOs[i].ssjmxzDm === '0061011804' || VOs[i].ssjmxzDm === '0099011802'){\n                fb5 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001011814'){\n                jmb = 1;\n    break;\n            }\n        }\n if (inDate && gtgsh === 'N' && sytysbxejmqy === 'Y' && (jmb === 1 || fb5 === 1)) {\n  result = 'tsxt';\n }\n return result;\n}", "description": "退役士兵提示校验", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "1ea205ea6faf49d683ca3ce32ad841da", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Xwqymsxse_col1Init", "functionMc": "customGet_Xwqymsxse_col1Init", "functionType": "diy", "functionBody": "function customGet_Xwqymsxse_col1Init() {\n var result = 0;\n var fzxx = this.fzxx; \n var ytxx = this.ytxx; \n var qzdbz = fzxx.tzxx.fzsbxx.qzdType;\n var gtjy = fzxx.bqxx.gtjy;\n     if(gtjy === 'N' && (qzdbz === 1 || qzdbz === 2)){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw1xsehj + fzxx.tzxx.fzsbxx.wpfpHwjlw3xsehj,2);\n  }\n return result;\n}", "description": "主表10栏第1列初始化赋值", "dependencies": "[\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw0xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw0xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj\",fzxx.tzxx.fzsbxx.wpfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw3xsehj\"]"}, {"functionXh": "214b70ca1dfc4363b4d7c2489390affb", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_zzsxejmje", "functionMc": "customGet_zzsxejmje", "functionType": "diy", "functionBody": "function customGet_zzsxejmje() {\n    var result = 0;\n\tvar zzsjmssbmxbjsxmGridlbVO=this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n\tfor(var i=0;i<zzsjmssbmxbjsxmGridlbVO.length;i++){\n\t\tif((this.fzxx.csxx.zzsxejmDmList || []).indexOf(zzsjmssbmxbjsxmGridlbVO[i].hmc)>=0){\n\t\t\tresult+=zzsjmssbmxbjsxmGridlbVO[i].bqsjdjse;\n\t\t}\n\t}\n    return this.round(result, 2);\n}", "description": "《增值税减免税申报明细表》中减免性质代码在核定XEJMDM时的第4列'本期实际抵减税额'的合计值。", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse\",\"fzxx.csxx.zzsxejmDmList\"]"}, {"functionXh": "29321dd19ea0490489296eed85f9eb00", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsJsyjld", "functionMc": "customGet_fjsJsyjld", "functionType": "diy", "functionBody": "function customGet_fjsJsyjld(zsxmDm) {\n    var arr = this.ywbw.sbFjsf.sbFjsfMx;\n    var val = 0;\n    for (i = 0; i < arr.length; i++) {\n        if (arr[i].zsxmDm === zsxmDm) {\n            val = arr[i].ybzzs;\n        }\n    }\n    if (this.max(this.customGet_sumA(), this.customGet_sumB()) > Number(this.fzxx.tzxx.fzsbxx.qzd) && Number(this.fzxx.tzxx.fzsbxx.dkzpyjzzsse2fjsjsyj || 0) > 0) {\n        for (i = 0; i < arr.length; i++) {\n            if (zsxmDm !== \"10109\" && arr[i].zsxmDm !== zsxmDm) {\n                arr[i].ybzzs = val;\n            }\n        }\n    } else {\n        for (i = 0; i < arr.length; i++) {\n            if (arr[i].zsxmDm !== zsxmDm) {\n                arr[i].ybzzs = val;\n            }\n        }\n    }\n}", "description": "联动性：若计税依据修改原因选择1、2，则不联动修改，根据纳税人修改的为准；选择3则当增值税销售额大于10万（季报30万），且增值税核定文书节点DKZPYJZZSSE2FJSJSYJ大于0时，仅教育附加地方教育附加的联动，否则教育附加地方教育附加城建税一起联动 （金三现状：都不联动）", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx\"]"}, {"functionXh": "2f5b03e66bd04bdaa3aab3be1af10a1b", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_hwlwBqynsejze", "functionMc": "customGet_hwlwBqynsejze", "functionType": "diy", "functionBody": "function customGet_hwlwBqynsejze() {\n        var fzsbxx = this.fzxx.tzxx.fzsbxx;\n        // 是否超起征点（不含不动产销售额）\n        var fphdxse = Math.max(this.customGet_sumA(), this.customGet_sumB());\n        var isBeyondQzd = fphdxse > Number(fzsbxx.qzd);\n        var firstArr = this.getObjFromList(this.ywbw.sbZzsXgm, \"ewblxh\", \"1\");\n        //2+3的值（开票金额）\n        var kpje = firstArr.swjgdkdzzszyfpbhsxse + firstArr.skqjkjdptfpbhsxse;\n        var hdxse = firstArr.hdxse;\n        // 1%发票销售额和税额  \n        var zyfpHwjlw1xsehj = fzsbxx.zyfpHwjlw1xsehj;\n        var zyfpHwjlw1sehj = fzsbxx.zyfpHwjlw1sehj;\n        var qtfpHwjlw1xsehj = fzsbxx.qtfpHwjlw1xsehj;\n        var qtfpHwjlw1sehj = fzsbxx.qtfpHwjlw1sehj;\n        // 货物服务混合的减免性质  \n        var zsl1Jmxz = '0001011608|SXA031901121';\n        // 服务减免性质  \n        var fwJmxz = ['0001011705|SXA031900839', '0001011707|SXA031901216', ];\n        // 减税项目  \n        var jsJmxzs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm.slice(1);\n        // 货物本期应纳税额  \n        var hwBqynse = this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1')\n                .bqynse;\n        // 本期实际抵减税额合计  \n        var bqsjdjseHj = this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'ewbhxh', '1')\n                .bqsjdjse;\n        // 服务本期实际抵减税额  \n        var fwBqsjdjse = 0;\n        for (var i = 0; i < jsJmxzs.length; i++) {\n                if (jsJmxzs[i].hmc === zsl1Jmxz) {\n                        if (isBeyondQzd) {\n                                if (kpje < hdxse) {\n                                        fwBqsjdjse = Math.max(0, this.round(jsJmxzs[i].bqsjdjse - hdxse * 0.02, 2))}\n                                else {\n                                        fwBqsjdjse = Math.max(0, this.round(jsJmxzs[i].bqsjdjse - ((zyfpHwjlw1xsehj + qtfpHwjlw1xsehj) * 0.03 - zyfpHwjlw1sehj - qtfpHwjlw1sehj), 2))\n                                        }\n                                }\n                        else {\n                                        fwBqsjdjse = Math.max(0, this.round(jsJmxzs[i].bqsjdjse - (zyfpHwjlw1xsehj * 0.03 - zyfpHwjlw1sehj), 2))\n                                }\n                        }\n                        if (fwJmxz.indexOf(jsJmxzs[i].hmc) > -1) {\n                                fwBqsjdjse += jsJmxzs[i].bqsjdjse;\n                        }\n                }\n                return this.round(Math.max(0, Math.min(hwBqynse, bqsjdjseHj - fwBqsjdjse)), 2);\n}", "description": "主表货物及劳务本期应纳税额减征额计算", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqynse\",\"getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqsjdjse\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').hdxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').skqjkjdptfpbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').swjgdkdzzszyfpbhsxse\"]"}, {"functionXh": "2f975671a117478ba7c443d3129ffa98", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_BbHwxsehj", "functionMc": "customGet_BbHwxsehj", "functionType": "diy", "functionBody": "function customGet_BbHwxsehj() {\n    var result = this.round(this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').msxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').ckmsxse,2);\n\treturn result;\n}", "description": "根据报表信息计算货物销售额合计\n货物发票销售额：主表第1+4+7+9+13栏第1列合计值", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse\"]"}, {"functionXh": "31309bf1a8af4d6e966933e934c1b680", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Xsczbdcbhsxse_col2Init", "functionMc": "customGet_Xsczbdcbhsxse_col2Init", "functionType": "diy", "functionBody": "function customGet_Xsczbdcbhsxse_col2Init() {\n\tvar result = 0;\n\tvar fzsbxx = this.fzxx.tzxx.fzsbxx; \n\tvar qzdbz = this.fzxx.tzxx.fzsbxx.qzdType;\n\tif (qzdbz === 3) {\n\tif (this.customGet_fb1_16_Init() === 0){\n\t\tresult = this.round(fzsbxx.zyfpFw5xsehj + fzsbxx.zyfpFw15xsehj + fzsbxx.qtfpFw5xsehj + fzsbxx.qtfpFw15xsehj + fzsbxx.wpfpFw5xsehj + fzsbxx.wpfpFw15xsehj,2);\n\t}\n\telse{\n\t\tresult = this.customGet_fb1_16_Init();\n\t}\n\t}\n\telse if(qzdbz === 2){\n\tresult = this.round(fzsbxx.zyfpFw5xsehj + fzsbxx.zyfpFw15xsehj + fzsbxx.qtfpBdcxse - fzsbxx.qtfpBdc0xse + fzsbxx.wpfpBdcxse,2);\n\t}\n\telse {\n\tresult = this.round(fzsbxx.zyfpFw5xsehj + fzsbxx.zyfpFw15xsehj,2);\n\t}\n\treturn result;\n}", "description": "主表第4栏第2列初始化", "dependencies": "[\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.zyfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw15xsehj\",\"customGet_fb1_16_Init\",\"fzxx.tzxx.fzsbxx.qtfpBdcxse\",\"fzxx.tzxx.fzsbxx.qtfpBdc0xse\",\"fzxx.tzxx.fzsbxx.wpfpBdcxse\"]"}, {"functionXh": "333b9ed42171415d8ea0aedc2aa7570f", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsJmse", "functionMc": "customGet_fjsJmse", "functionType": "diy", "functionBody": "function customGet_fjsJmse(indexArr) {\n    var index = indexArr[0];\n    var ywbw = this.ywbw;\n    var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm;\n    var ynse = ywbw.sbFjsf.sbFjsfMx[index].bqynsfe;\n    var xse = this.max(this.customGet_sumB(),this.customGet_sumA());\n    var ssjmxzDm = ywbw.sbFjsf.sbFjsfMx[index].ssjmxzDm || '';\n    var swsxDm = ywbw.sbFjsf.sbFjsfMx[index].swsxDm || '';\n    var jme = ywbw.sbFjsf.sbFjsfMx[index].jme || 0;\n    if (this.inArray(['30203', '30216'], zsxmDm)\n        && ((ssjmxzDm === '0061042802' && swsxDm === 'SXA031900783') || (ssjmxzDm === '0099042802' && swsxDm === 'SXA031901054'))\n        && ynse > 0\n        && xse <= (Number(this.sbsx.nsqxDm) - 5) * 100000) {\n        return Number(ynse);\n    }\n    if (!ssjmxzDm) {\n        return 0;\n    } else {\n        return jme;\n    }\n}", "description": "设置附加税减免税额", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbFjsf.sbFjsfMx[*].swsxDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\"]"}, {"functionXh": "390d91fe0ba949b79dbd1c2e6fd2775f", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fb1_16_Init", "functionMc": "customGet_fb1_16_Init", "functionType": "diy", "functionBody": "function customGet_fb1_16_Init() {\n\tvar result = 0;\n\tif(this.ywbw.sbZzsXgmFbFlzl.qcye5 + this.ywbw.sbZzsXgmFbFlzl.bqfse5 <= this.ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr5){\n\tresult = this.round(this.ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5 / 1.05,2);\n\t}\n\treturn result;\n}", "description": "附表1第16栏初始化", "dependencies": "[\"ywbw.sbZzsXgmFbFlzl.qcye5\",\"ywbw.sbZzsXgmFbFlzl.bqfse5\",\"ywbw.sbZzsXgmFbFlzl.ysfwxsqbhssr5\",\"ywbw.sbZzsXgmFbFlzl.ysfwxshsxse5\"]"}, {"functionXh": "3d33fe02e88d4f5a9ef9331c8520e8dc", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsYbtse", "functionMc": "customGet_fjsYbtse", "functionType": "diy", "functionBody": "function customGet_fjsYbtse(zsxmDm) {\n\tvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\n\tvar cjsybtsfe = 0;\n    var obj = this.getObjFromList(this.ywbw.sbFjsf.sbFjsfMx, 'zsxmDm', zsxmDm);\n    if (!obj) {\n        return 0;\n    }\n    if (zsxmDm === '10109'){\n\t\tfor (var i = 0; i < VOs.length; i++){\n\t\tif (VOs[i].zsxmDm === '10109') {\n\t\t\tcjsybtsfe = this.round(cjsybtsfe + VOs[i].bqybtse,2);\n\t\t}\n\t}\n\treturn cjsybtsfe;\n\t}else {\n\t\treturn obj.bqybtse;\n\t} \n}", "description": "从附加税附表二取对应征收项目编码的应补退税额", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx\",\"ywbw.sbFjsf.sbFjsfMx[*].bqybtse\"]"}, {"functionXh": "42f0fa2f7298476d884b5126a660ef2b", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_tysbts", "functionMc": "customGet_tysbts", "functionType": "diy", "functionBody": " function customGet_tysbts() {\n    var Date2 = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\n var Date1 = new Date(this.sbsx.skssqz) <= new Date('2023-12-31');\n var sytysbxejmqy = this.fzxx.bqxx.sytysbxejmqy;\n var gtgsh = this.fzxx.bqxx.gtgsh;\n var result = '';\n var jmb = 0;\n var fb5 = 0;\n var jmb1 = 0;\n var fb51 = 0;\n var jmb2 = 0;\n var fb52 = 0;\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n var VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011804' || VOs[i].ssjmxzDm === '0061011804' || VOs[i].ssjmxzDm === '0099011802' || VOs[i].ssjmxzDm === '0007011803' || VOs[i].ssjmxzDm === '0061011803' || VOs[i].ssjmxzDm === '0099011801'){\n                fb5 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001011804' || VOs1[i].hmc.substr(0, 10) === '0001011805' || VOs1[i].hmc.substr(0, 10) === '0001011808' || VOs1[i].hmc.substr(0, 10) === '0001011811' || VOs1[i].hmc.substr(0, 10) === '0001011812' || VOs1[i].hmc.substr(0, 10) === '0001011813' || VOs1[i].hmc.substr(0, 10) === '0001011814'){\n                jmb = 1;\n    break;\n            }\n        }\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011803' || VOs[i].ssjmxzDm === '0061011803' || VOs[i].ssjmxzDm === '0099011801'){\n                fb51 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001011813'){\n                jmb1 = 1;\n    break;\n            }\n        }\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011804' || VOs[i].ssjmxzDm === '0061011804' || VOs[i].ssjmxzDm === '0099011802'){\n                fb52 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001011814'){\n                jmb2 = 1;\n    break;\n            }\n        } \n if (Date1 && sytysbxejmqy === 'N' && (jmb === 1 || fb5 === 1)) {\n  result = '请采集《自主就业退役士兵本年度在企业工作时间表》，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\">点此采集。</a>';\n }else if (Date2 && gtgsh === 'Y' && sytysbxejmqy === 'N' && (jmb1 === 1 || fb51 === 1)) {\n  result = '没有采集创业自主就业退役士兵信息表信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\">点此采集。</a>';\n }else if (Date2 && gtgsh === 'N' && sytysbxejmqy === 'N' && (jmb2 === 1 || fb52 === 1)) {\n  result = '没有采集企业退役士兵信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\">点此采集。</a>';\n }\n return result;\n}", "description": "附表二退役士兵阻断提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "484075da482a4e72bf3f37799ff333a9", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_zdrqts", "functionMc": "customGet_zdrqts", "functionType": "diy", "functionBody": " function customGet_zdrqts() {\n    var Date2 = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\n var Date1 = new Date(this.sbsx.skssqz) <= new Date('2023-12-31');\n var sydjsyxejmqy = this.fzxx.bqxx.sydjsyxejmqy;\n var sypkrkxejmqy = this.fzxx.bqxx.sypkrkxejmqy;\n var gtgsh = this.fzxx.bqxx.gtgsh;\n var result = '';\n var jmb1 = 0;\n var jmb2 = 0;\n var jmb3 = 0;\n var jmb = 0;\n var fb5 = 0;\n var fb51 = 0;\n var fb52 = 0;\n var fb53 = 0;\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n var VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013612' || VOs[i].ssjmxzDm === '0061013610' || VOs[i].ssjmxzDm === '0099013603' || VOs[i].ssjmxzDm === '0007013613' || VOs[i].ssjmxzDm === '0061013611' || VOs[i].ssjmxzDm === '0099013604' || VOs[i].ssjmxzDm === '0007013610' || VOs[i].ssjmxzDm === '0061013608' || VOs[i].ssjmxzDm === '0099013601' || VOs[i].ssjmxzDm === '0007013611' || VOs[i].ssjmxzDm === '0061013609' || VOs[i].ssjmxzDm === '0099013602'){\n                fb5 = 1;\n    break;\n            }\n        }\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013611' || VOs[i].ssjmxzDm === '0061013609' || VOs[i].ssjmxzDm === '0099013602'){\n                fb51 = 1;\n    break;\n            }\n        }\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013610' || VOs[i].ssjmxzDm === '0061013608' || VOs[i].ssjmxzDm === '0099013601'){\n                fb52 = 1;\n    break;\n            }\n  }\n for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001013609' || VOs1[i].hmc.substr(0, 10) === '0001013612' || VOs1[i].hmc.substr(0, 10) === '0001013607' || VOs1[i].hmc.substr(0, 10) === '0001013613' || VOs1[i].hmc.substr(0, 10) === '0001013610' || VOs1[i].hmc.substr(0, 10) === '0001013611'){\n                jmb = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001013611'){\n                jmb1 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n   if (VOs1[i].hmc.substr(0, 10) === '0001013610'){\n                jmb2 = 1;\n    break;\n            }\n        }\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013612' || VOs[i].ssjmxzDm === '0007013613' || VOs[i].ssjmxzDm === '0061013610' || VOs[i].ssjmxzDm === '0061013611' || VOs[i].ssjmxzDm === '0099013603' || VOs[i].ssjmxzDm === '0099013604'){\n                fb53 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001013612' || VOs1[i].hmc.substr(0, 10) === '0001013613'){\n                jmb3 = 1;\n    break;\n            }\n        }\n if (Date1 && sydjsyxejmqy ==='N' && sypkrkxejmqy === 'N' && (jmb === 1 || fb5 === 1)) {\n  result = '请采集《重点群体人员本年度实际工作时间》，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n }else if (Date2 && sydjsyxejmqy ==='N' && gtgsh === 'Y' && (jmb1 === 1 || fb51  === 1) && ((jmb2 !== 1 && fb52 !== 1) || sypkrkxejmqy !== 'N')) {\n  result = '没有采集创业重点群体人员信息表的登记失业半年以上人员，零就业家庭、享受城市低保登记失业人员，毕业年度内高校毕业生从事个体经营人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n }else if (Date2 && sypkrkxejmqy ==='N' && gtgsh === 'Y' && (jmb2 === 1 || fb52  === 1) && ((jmb1 !== 1 && fb51 !== 1) || sydjsyxejmqy !== 'N')) {\n  result = '没有采集创业重点群体人员信息表的脱贫人口人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n }else if (Date2 && sydjsyxejmqy ==='N' && sypkrkxejmqy === 'N' && gtgsh === 'Y' && (jmb2 === 1 || fb52  === 1) && (jmb1 === 1 || fb51  === 1)) {\n  result = 'all';\n }else if (Date2 && sydjsyxejmqy ==='N' && sypkrkxejmqy === 'N' && gtgsh === 'N' && (jmb3 === 1 || fb53  === 1)) {\n  result = '没有采集企业重点群体人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n }\n return result;\n}", "description": "附表二重点重点群体采集提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "4966c8610be84353890481fe13997f91", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sumFpzxseBhbdcxse", "functionMc": "customGet_sumFpzxseBhbdcxse", "functionType": "diy", "functionBody": "function customGet_sumFpzxseBhbdcxse() {\n    var result = this.customGet_sumFpzxseHbdcxse() - Number(this.fzxx.tzxx.fpmx.zyfpBdcxse + this.fzxx.tzxx.fpmx.qtfpBdcxse);\n\treturn result;\n}", "description": "发票总销售额（不含不动产销售额）=“专用发票zy_hw_3_je+货物代开专票HWDKZPXSE+普通发票pt_hw_3_je”之和+“专用发票（zy_fw_3_je+zy_fw_5_je）+代开专用发票[FWDKZPXSE+XSCZBDCDKZPXSE]+普通发票（pt_fw_3_je+pt_fw_5_je）+PT_FW_SHFW_HSJE+PT_HW_0_JE+PT_FW_0_JE -XSBDC_5_JE ", "dependencies": "[\"customGet_sumFpzxseHbdcxse\",\"fzxx.tzxx.fpmx.zyfpBdcxse\",\"fzxx.tzxx.fpmx.qtfpBdcxse\"]"}, {"functionXh": "4e00cab290a84784829fbd865738c05a", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsXejmje", "functionMc": "customGet_fjsXejmje", "functionType": "diy", "functionBody": "function customGet_fjsXejmje() {\n    var A = Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001011814|SXA031900832').bqsjdjse\")) + Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001011814').bqsjdjse\"));\n\tvar B = Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013612|SXA031901039').bqsjdjse\")) + Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013612').bqsjdjse\"));\n\tvar C = Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013613|SXA031901022').bqsjdjse\")) + Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013613').bqsjdjse\"));\n\tvar D = Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013610|SXA031901038').bqsjdjse\")) + Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013610').bqsjdjse\"));\n\tvar E = Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013611|SXA031901021').bqsjdjse\")) + Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001013611').bqsjdjse\"));\n\tvar F = Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001011813|SXA031900831').bqsjdjse\")) + Number(this.doCatch(\"this.getObjFromList(this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm, 'hmc', '0001011813').bqsjdjse\"));\n    var G=Number(A)+Number(B)+Number(C)+Number(D)+Number(E)+Number(F);\n    return G;\n}", "description": "获取附加税费增值税限额减免金额", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse\",\"getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqsjdjse\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "4e5d4a8c08c841d889b90df53f927cb2", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_yywxsphjmBz", "functionMc": "customGet_yywxsphjmBz", "functionType": "diy", "functionBody": "function customGet_yywxsphjmBz(zsxmDm) {\n  if (zsxmDm === '10109') {\n    return this.fzxx.tzxx.fzsbxx.cswhjssYywxsphjmBz || '';\n  }\n  if (zsxmDm === '30203') {\n    return this.fzxx.tzxx.fzsbxx.jyffjYywxsphjmBz || '';\n  }\n  if (zsxmDm === '30216') {\n    return this.fzxx.tzxx.fzsbxx.dfjyfjYywxsphjmBz || '';\n  }\n  return '';\n}", "description": "根据征收项目获取yywxsphjmBz", "dependencies": "[]"}, {"functionXh": "523bfd15662142db875c7e5dc2a84e51", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Xwqymse_col2Init", "functionMc": "customGet_Xwqymse_col2Init", "functionType": "diy", "functionBody": "function customGet_Xwqymse_col2Init() {\n var result = 0;\n var fzxx = this.fzxx; \n var qzdbz = fzxx.tzxx.fzsbxx.qzdType;\n var gtjy = fzxx.bqxx.gtjy;\n     if(gtjy === 'N' && qzdbz === 2){\n  result = this.round((fzxx.tzxx.fzsbxx.qtfpFw0xsehj - fzxx.tzxx.fzsbxx.qtfpBdc0xse) * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw15xsehj * 0.05 + fzxx.tzxx.fzsbxx.qtfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw5xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw0xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw5xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw15xsehj * 0.05,2);\n } else if(gtjy === 'N' && qzdbz === 1){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw15xsehj * 0.05 + fzxx.tzxx.fzsbxx.qtfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw5xsehj * 0.05 + (fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse) * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw0xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw5xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw15xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpBdcxse * 0.05,2);\n }\n return result;\n }", "description": "主表20栏第2列初始化", "dependencies": "[\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.qtfpBdc0xse\",\"fzxx.tzxx.fzsbxx.qtfpFw0xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw0xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw3xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.wpfpBdcxse\",\"fzxx.tzxx.fzsbxx.qtfpBdcxse\"]"}, {"functionXh": "590dc5ba2b8444f6a292508d41d67a2e", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_zdrqtstx", "functionMc": "customGet_zdrqtstx", "functionType": "diy", "functionBody": " function customGet_zdrqtstx() {\n    var inDate = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\n var sydjsyxejmqy = this.fzxx.bqxx.sydjsyxejmqy;\n var sypkrkxejmqy = this.fzxx.bqxx.sypkrkxejmqy;\n var gtgsh = this.fzxx.bqxx.gtgsh;\n var result = '';\n var jmb = 0;\n var fb5 = 0;\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n var VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013612' || VOs[i].ssjmxzDm === '0007013613' || VOs[i].ssjmxzDm === '0061013610' || VOs[i].ssjmxzDm === '0061013611' || VOs[i].ssjmxzDm === '0099013603' || VOs[i].ssjmxzDm === '0099013604'){\n                fb5 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001013612' || VOs1[i].hmc.substr(0, 10) === '0001013613'){\n                jmb = 1;\n    break;\n            }\n        }\n if (inDate && gtgsh === 'N' && (sydjsyxejmqy === 'Y' || sypkrkxejmqy === 'Y') && (jmb === 1 || fb5 === 1)) {\n  result = 'tsxt';\n }\n return result;\n}", "description": "附表二重点人群提示性提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "594cd58cbcc14824befead0d8dc52942", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_FJSXEJMDM", "functionMc": "customGet_FJSXEJMDM", "functionType": "diy", "functionBody": "function customGet_FJSXEJMDM(ssjmxzDm) {\n    var result = false;\n\tif((this.hd.wsxxs.FJSXEJMDM || \"\").split(\",\").includes(ssjmxzDm)){\n\t\t\tresult=true;\n\t\t}\n    return result;\n}", "description": "选中的减免性质代码是否在hd.wsxxs.FJSXEJMDM中存在", "dependencies": "[\"hd.wsxxs.FJSXEJMDM\"]"}, {"functionXh": "5de011b4dbd94f2ba2ed8fd2b0fef20a", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Wdqzdmse_col2Init", "functionMc": "customGet_Wdqzdmse_col2Init", "functionType": "diy", "functionBody": "function customGet_Wdqzdmse_col2Init() {\n var result = 0;\n var fzxx = this.fzxx;  \n var qzdbz = fzxx.tzxx.fzsbxx.qzdType;\n var gtjy = fzxx.bqxx.gtjy;\n     if(gtjy === 'Y' && qzdbz === 2){\n  result = this.round((fzxx.tzxx.fzsbxx.qtfpFw0xsehj - fzxx.tzxx.fzsbxx.qtfpBdc0xse) * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw15xsehj * 0.05 + fzxx.tzxx.fzsbxx.qtfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw5xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw0xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw5xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw15xsehj * 0.05,2);\n } else if(gtjy === 'Y' && qzdbz === 1){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw15xsehj * 0.05 + fzxx.tzxx.fzsbxx.qtfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.qtfpFw5xsehj * 0.05 + (fzxx.tzxx.fzsbxx.qtfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdc0xse) * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw0xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw1xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw3xsehj * 0.03 + fzxx.tzxx.fzsbxx.wpfpFw5xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpFw15xsehj * 0.05 + fzxx.tzxx.fzsbxx.wpfpBdcxse * 0.05,2);\n }\n return result;\n }", "description": "主表21栏第2列初始化", "dependencies": "[\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.qtfpBdc0xse\",\"fzxx.tzxx.fzsbxx.qtfpFw0xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw0xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw3xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.wpfpBdcxse\",\"fzxx.tzxx.fzsbxx.qtfpBdcxse\"]"}, {"functionXh": "61548f6fe0f54d1c8cf798929f95d82e", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Xejsbqsjdjsehj", "functionMc": "customGet_Xejsbqsjdjsehj", "functionType": "diy", "functionBody": "function customGet_Xejsbqsjdjsehj() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].hmc.substr(0, 10) === '0001011813' || VOs[i].hmc.substr(0, 10) === '0001011814' || VOs[i].hmc.substr(0, 10) === '0001013610' || VOs[i].hmc.substr(0, 10) === '0001013611' || VOs[i].hmc.substr(0, 10) === '0001013612' || VOs[i].hmc.substr(0, 10) === '0001013613' || VOs[i].hmc.substr(0, 10) === '0001129914'){\n                A = A + VOs[i].bqsjdjse;\n            }\n        }\n    } catch (e) { }\n    return this.round(A,2);\n}", "description": "求限额减免本期实际抵减税额合计\n注意此函数初始化函数使用，所以依赖项设置为空", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\", \"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse\"]"}, {"functionXh": "65bd52e63e654d76b54058035bc852f8", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sum11", "functionMc": "customGet_sum11", "functionType": "diy", "functionBody": "function customGet_sum11() {\n    var result = this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').wdqzdxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').wdqzdxse;\n    return result;\n}", "description": "第11栏1+2列", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '1').wdqzdxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '2').wdqzdxse\"]"}, {"functionXh": "6a3fd319f5de43d48be5a77d227f29c9", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fb1_5_Init", "functionMc": "customGet_fb1_5_Init", "functionType": "diy", "functionBody": "function customGet_fb1_5_Init() {\n\tvar result = 0;\n\tbqfseValue = this.customGet_fb1_2_Init();\n\tvar fzxx= this.fzxx; \n\tif (bqfseValue !=0){\n\t  result = this.round(fzxx.tzxx.fzsbxx.zyfpFwKcq1xsehj + fzxx.tzxx.fzsbxx.zyfpFw1sehj + fzxx.tzxx.fzsbxx.qtfpFwKcq1xsehj + fzxx.tzxx.fzsbxx.qtfpFw1sehj + fzxx.tzxx.fzsbxx.zyfpFwKcq3xsehj + fzxx.tzxx.fzsbxx.zyfpFw3sehj + fzxx.tzxx.fzsbxx.qtfpFwKcq3xsehj + fzxx.tzxx.fzsbxx.qtfpFw3sehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj * 1.01 + fzxx.tzxx.fzsbxx.wpfpFw3xsehj * 1.03,2);\n\t}\n\telse{\n\t  result = 0 ;\n\t}\n\treturn result;\n}", "description": "附表1第5栏初始化", "dependencies": "[\"customGet_fb1_2_Init\",\"fzxx.tzxx.fzsbxx.zyfpFwKcq1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw1sehj\",\"fzxx.tzxx.fzsbxx.qtfpFwKcq1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1sehj\",\"fzxx.tzxx.fzsbxx.zyfpFwKcq3xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw3sehj\",\"fzxx.tzxx.fzsbxx.qtfpFwKcq3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3sehj\",\"fzxx.tzxx.fzsbxx.wpfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw3xsehj\"]"}, {"functionXh": "6b55249a543c477cb65f745f98684caf", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsBqybtse", "functionMc": "customGet_fjsBqybtse", "functionType": "diy", "functionBody": "function customGet_fjsBqybtse(arr) {\n\tvar index = arr[0];\n    var obj = this.ywbw.sbFjsf.sbFjsfMx[index]\n\tvar B = obj.bqynsfe - obj.jme - obj.phjmse - obj.bqyjse;\n    return B;\n}\n", "description": "获取附加税本期应补退税额", "dependencies": "[\"sbFjsf.sbFjsfMx*].bqynsfe\",\"sbFjsf.sbFjsfMx[*].jme\",\"sbFjsf.sbFjsfMx[*].phjmse\",\"sbFjsf.sbFjsfMx[*].bqyjse\"]"}, {"functionXh": "6b83e9ab56214a2694a122a1a3944fd0", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_msxmJmxz", "functionMc": "customGet_msxmJmxz", "functionType": "diy", "functionBody": "function customGet_msxmJmxz() {\n    // 近期发生过享受除3%征收率免税外其他免税企业（外部）\n    var jqfsgXsc3zslmswqtmsQy = this.fzxx.bqxx.jqfsgXsc3zslmswqtmsQy;\n    // 是否在3%优惠政策适用时间范围内\n    var sfz3yhzcsysjfwn = this.fzxx.bqxx.sfz3yhzcsysjfwn; \n    if (jqfsgXsc3zslmswqtmsQy !== 'Y' && sfz3yhzcsysjfwn === 'Y') {\n        var msxmList = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm.slice(3)\n        var hasMsxm = false;\n        for (var i = 0; i < msxmList.length; i++) {\n            if (msxmList[i].hmc) {\n                hasMsxm = true;\n            }\n        }\n        if (!hasMsxm) {\n            return '0001045308|SXA031901259'\n        }\n    }\n    return this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[3].hmc\n}", "description": "“0001045308|SXA031901259|小规模纳税人3%征收率销售额免征增值税”免税项目减免性质自动赋值", "dependencies": "[]"}, {"functionXh": "6f7b5d96ef5145f48d932f185a9e6d25", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jmbgthjmxm", "functionMc": "customGet_jmbgthjmxm", "functionType": "diy", "functionBody": "function customGet_jmbgthjmxm() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].hmc.substr(0, 10) === '0001011705'){\n                A = 1;\n            }\n        }\n    } catch (e) { }\n    return A;\n}", "description": "个体工商户减免项目", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "6fd714d20965486d96becf3d0b450de6", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_msehj", "functionMc": "customGet_msehj", "functionType": "diy", "functionBody": "function customGet_msehj() {\n\tvar result = 0;\n\ttry {\n    var msxm = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm; \n\tfor (var i = 0; i < msxm.length; i++){\n\tif (msxm[i].ewbhxh !== '1' && msxm[i].ewbhxh !== '2' && msxm[i].ewbhxh !== '3') {\n\t\tresult = result + msxm[i].mse;\n\t}\n\t}\n    } catch (e) { }\n\treturn result;\n}", "description": "计算减免表减税项目减免额合计", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbMsxm[*].mse\"]"}, {"functionXh": "747a1647d2064dde9456b978b871ce41", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sumFpzxseHbdcxse", "functionMc": "customGet_sumFpzxseHbdcxse", "functionType": "diy", "functionBody": "function customGet_sumFpzxseHbdcxse() {\n\tvar result = Number(this.fzxx.tzxx.fpmx.zyfpHwjlwxsehj) + Number(this.hd.wsxxs.HWDKZPXSE)\n        + Number(this.fzxx.tzxx.fpmx.qtfpHwjlwxsehj) + Number(this.fzxx.tzxx.fpmx.zyfpFw3xsehj)\n        + Number(this.fzxx.tzxx.fpmx.zyfpFw5xsehj) + Number(this.hd.wsxxs.FWDKZPXSE)\n        + Number(this.hd.wsxxs.XSCZBDCDKZPXSE) + Number(this.fzxx.tzxx.fpmx.qtfpFw3xsehj)\n        + Number(this.fzxx.tzxx.fpmx.qtfpFw5xsehj);\n\treturn result;\n}", "description": "发票总销售额（含不动产销售额）=“专用发票zy_hw_3_je+货物代开专票HWDKZPXSE+普通发票pt_hw_3_je”之和+“专用发票（zy_fw_3_je+zy_fw_5_je）+代开专用发票[FWDKZPXSE+XSCZBDCDKZPXSE]+普通发票（pt_fw_3_je+pt_fw_5_je）+PT_FW_SHFW_HSJE”+PT_HW_0_JE+PT_FW_0_JE。", "dependencies": "[\"fzxx.tzxx.fpmx.zyfpHwjlwxsehj\", \"hd.wsxxs.HWDKZPXSE\", \"fzxx.tzxx.fpmx.qtfpHwjlwxsehj\",\n    \"fzxx.tzxx.fpmx.zyfpFw3xsehj\", \"fzxx.tzxx.fpmx.zyfpFw5xsehj\", \"hd.wsxxs.FWDKZPXSE\",\n    \"hd.wsxxs.XSCZBDCDKZPXSE\", \"fzxx.tzxx.fpmx.qtfpFw3xsehj\", \"fzxx.tzxx.fpmx.qtfpFw5xsehj\"]"}, {"functionXh": "796e2de828f44f74825f8612935a10d4", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "checkTimeBchssq", "functionMc": "checkTimeBchssq", "functionType": "diy", "functionBody": "function checkTime(time1, time2, format) {\n    if (!time1 || !time2) return true;\n    format = format || \"yyyy-MM-dd\";\n    time1 = engineJs.content.parseDate(time1, format);\n    time2 = engineJs.content.parseDate(time2, format);\n    var sameDay = time1.getTime() == time2.getTime();\n    var sameYear = time1.getFullYear() == time2.getFullYear();\n    if (sameDay) return true;\n    if (\n        sameYear &&\n        (Math.abs(time1.getTime() - time2.getTime()) / (24 * 3600 * 1000) ==\n            365 ||\n            Math.abs(time1.getTime() - time2.getTime()) / (24 * 3600 * 1000) ==\n            366)\n    )\n        return true;\n    if (sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 5 &&\n        [0, 6].indexOf(time1.getMonth()) != -1\n    )\n        return true;\n    if (\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 2 &&\n        [0, 3, 6, 9].indexOf(time1.getMonth()) != -1\n    )\n        return true;\n    if (sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 0\n    )\n        return true;\n    return false;\n}", "description": "所属期必须为整年、半年、季或月！", "dependencies": "[]"}, {"functionXh": "7c0ddbd3be074ceeb9af90ecb0e57040", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Bqynsejze_col1Init", "functionMc": "customGet_Bqynsejze_col1Init", "functionType": "diy", "functionBody": "function customGet_Bqynsejze_col1Init() {\n  var result = 0;\n  var fzsbxx = this.fzxx.tzxx.fzsbxx;\n  var qzdbz = this.fzxx.tzxx.fzsbxx.qzdType;\n  var hdcfpcqzdQy = this.fzxx.bqxx.hdcfpcqzdQy;\n  var hdfwxse = this.getObjFromList(this.ytxx.sbZzsXgm,'ewblxh','1').hdxse;\n  var minValue = 0;\n  // 减免表减税项目限额减征小计本期实际抵减税额合计值\n  xejsbqsjdjsehj = this.customGet_Xejsbqsjdjsehj();\n  if (qzdbz === 3) {\n    var yzzzsbhsxse = this.round(fzsbxx.zyfpHwjlw05xsehj + fzsbxx.zyfpHwjlw1xsehj + fzsbxx.zyfpHwjlw3xsehj + fzsbxx.qtfpHwjlw05xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.qtfpHwjlw3xsehj + fzsbxx.wpfpHwjlw05xsehj + fzsbxx.wpfpHwjlw1Wjhdxsehj + fzsbxx.wpfpHwjlw3Wjhdxsehj, 2)\n    var bqynse = this.round(yzzzsbhsxse * 0.03, 2)\n    if (xejsbqsjdjsehj !== 0 && hdcfpcqzdQy === 'N') {\n      minValue = this.min(\n        xejsbqsjdjsehj,\n        bqynse - (\n          this.round(\n            (fzsbxx.zyfpHwjlw1xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.wpfpHwjlw1xsehj) * 0.03 - (fzsbxx.zyfpHwjlw1sehj + fzsbxx.qtfpHwjlw1sehj + fzsbxx.wpfpHwjlw1xsehj * 0.01),\n            2\n          ) +\n          this.round(\n            (fzsbxx.zyfpHwjlw05xsehj + fzsbxx.qtfpHwjlw05xsehj + fzsbxx.wpfpHwjlw05xsehj) * 0.03 - (fzsbxx.zyfpHwjlw05sehj + fzsbxx.qtfpHwjlw05sehj + fzsbxx.wpfpHwjlw05xsehj * 0.005),\n            2\n          )\n        )\n      );\n    }else if (xejsbqsjdjsehj !== 0 && hdcfpcqzdQy === 'Y') {\n      minValue = this.min(\n        xejsbqsjdjsehj,\n        bqynse - (\n          this.round((fzsbxx.zyfpHwjlw1xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.wpfpHwjlw1xsehj) * 0.03 - (fzsbxx.zyfpHwjlw1sehj + fzsbxx.qtfpHwjlw1sehj + fzsbxx.wpfpHwjlw1xsehj * 0.01),2)\n\t\t  )\n      );\n    }\n    if ((bqynse !== 0 || hdfwxse!==0) && hdcfpcqzdQy === 'Y') {\n      result = this.round(\n        this.round(\n          (fzsbxx.zyfpHwjlw1xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.wpfpHwjlw1xsehj) * 0.03 - (fzsbxx.zyfpHwjlw1sehj + fzsbxx.qtfpHwjlw1sehj + fzsbxx.wpfpHwjlw1xsehj * 0.01),\n          2\n        ) +\n        minValue,\n        2\n      );\n    }else if (bqynse !== 0 && hdcfpcqzdQy === 'N') {\n      result = this.round(\n        this.round(\n          (fzsbxx.zyfpHwjlw1xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.wpfpHwjlw1xsehj) * 0.03 - (fzsbxx.zyfpHwjlw1sehj + fzsbxx.qtfpHwjlw1sehj + fzsbxx.wpfpHwjlw1xsehj * 0.01),\n          2\n        ) +\n        this.round(\n          (fzsbxx.zyfpHwjlw05xsehj + fzsbxx.qtfpHwjlw05xsehj + fzsbxx.wpfpHwjlw05xsehj) * 0.03 - (fzsbxx.zyfpHwjlw05sehj + fzsbxx.qtfpHwjlw05sehj + fzsbxx.wpfpHwjlw05xsehj * 0.005),\n          2\n        ) +\n        minValue,\n        2\n      );\n    }\n  }\n  else {\n    var yzzzsbhsxse = this.round(fzsbxx.zyfpHwjlw05xsehj + fzsbxx.zyfpHwjlw1xsehj + fzsbxx.zyfpHwjlw3xsehj, 2)\n    var bqynse = this.round(yzzzsbhsxse * 0.03, 2)\n    if (xejsbqsjdjsehj !== 0) {\n      minValue = this.min(\n        xejsbqsjdjsehj,\n        bqynse - this.round(fzsbxx.zyfpHwjlw1xsehj * 0.03 - fzsbxx.zyfpHwjlw1sehj, 2) - this.round(fzsbxx.zyfpHwjlw05xsehj * 0.03 - fzsbxx.zyfpHwjlw05sehj, 2)\n      );\n    }\n    if (bqynse !== 0) {\n      result = this.round(\n        this.round(fzsbxx.zyfpHwjlw1xsehj * 0.03 - fzsbxx.zyfpHwjlw1sehj, 2) + \n        this.round(fzsbxx.zyfpHwjlw05xsehj * 0.03 - fzsbxx.zyfpHwjlw05sehj, 2) + \n        minValue,\n        2\n      );\n    }\n  }\n  return result;\n}", "description": "主表18栏第1列初始化", "dependencies": "[\"customGet_Xejsbqsjdjsehj\",\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj\",fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw1Wjhdxsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw3Wjhdxsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw1sehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw1sehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw05sehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw05sehj\"]"}, {"functionXh": "8151245472ec4070b474ac6b29cf6592", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Yzzzsbhsxse_col2Init", "functionMc": "customGet_Yzzzsbhsxse_col2Init", "functionType": "diy", "functionBody": "function customGet_Yzzzsbhsxse_col2Init() {\n\tvar result = 0;\n\tvar fzsbxx = this.fzxx.tzxx.fzsbxx; \n\tvar qzdbz = this.fzxx.tzxx.fzsbxx.qzdType;\n\tif (qzdbz === 3) {\n\tif (this.customGet_fb1_8_Init() === 0){\n\t\tresult = this.round(fzsbxx.zyfpFw1xsehj + fzsbxx.zyfpFw3xsehj + fzsbxx.qtfpFw1xsehj + fzsbxx.qtfpFw3xsehj + fzsbxx.wpfpFw1Wjhdxsehj + fzsbxx.wpfpFw3Wjhdxsehj,2);\n\t}\n\telse{\n\t\tresult = this.customGet_fb1_8_Init();\n\t}\n\t}\n\telse{\n\tresult = this.round(fzsbxx.zyfpFw1xsehj + fzsbxx.zyfpFw3xsehj,2);\n\t}\n\treturn result;\n}", "description": "主表第1栏第2列初始化", "dependencies": "[\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.zyfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw1Wjhdxsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw3Wjhdxsehj\",\"customGet_fb1_8_Init\"]"}, {"functionXh": "8712e23ab0174c00be1f825e920862d1", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_BbFwxsehj", "functionMc": "customGet_BbFwxsehj", "functionType": "diy", "functionBody": "function customGet_BbFwxsehj() {\n    var result = this.round(this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').msxse + this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').ckmsxse,2);\n\treturn result;\n}", "description": "根据报表信息计算服务销售额合计\n服务发票销售额：主表第1+4+7+9+13栏第2列合计", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse\"]"}, {"functionXh": "88d1e52b0022475e9b39cf1d43ddbafe", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Xwqymsxse_col2Init", "functionMc": "customGet_Xwqymsxse_col2Init", "functionType": "diy", "functionBody": "function customGet_Xwqymsxse_col2Init() {\n var result = 0;\n var fzxx = this.fzxx; \n var ytxx = this.ytxx; \n var qzdbz = fzxx.tzxx.fzsbxx.qzdType;\n var gtgsh = fzxx.bqxx.gtgsh;\n     if(gtgsh === 'N' && qzdbz === 2){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse - fzxx.tzxx.fzsbxx.qtfpBdcxse,2);\n }else if(gtgsh === 'N' && qzdbz === 1){\n  result = this.round(fzxx.tzxx.fzsbxx.qtfpFw0xsehj + fzxx.tzxx.fzsbxx.qtfpFw1xsehj + fzxx.tzxx.fzsbxx.qtfpFw15xsehj + fzxx.tzxx.fzsbxx.qtfpFw3xsehj + fzxx.tzxx.fzsbxx.qtfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw0xsehj + fzxx.tzxx.fzsbxx.wpfpFw1xsehj + fzxx.tzxx.fzsbxx.wpfpFw3xsehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj + fzxx.tzxx.fzsbxx.wpfpFw15xsehj + fzxx.tzxx.fzsbxx.wpfpBdcxse,2);\n }\n return result;\n }", "description": "主表10栏第2列初始化赋值", "dependencies": "['']"}, {"functionXh": "8b0182bd8a114199b6e86a904a7f2652", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jmse", "functionMc": "customGet_jmse", "functionType": "diy", "functionBody": "function customGet_jmse(indexArr) {\n    var index = indexArr[0];\n    var ywbw = this.ywbw;\n    var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm;\n    var bqynsfe = ywbw.sbFjsf.sbFjsfMx[index].bqynsfe;\n    var xse = this.max(this.customGet_sumA(), this.customGet_sumB());\n    var ssjmxzDm = ywbw.sbFjsf.sbFjsfMx[index].ssjmxzDm || '';\n    var swsxDm = ywbw.sbFjsf.sbFjsfMx[index].swsxDm || '';\n    var jme = ywbw.sbFjsf.sbFjsfMx[index].jme || 0;\n    if (this.inArray(['30203', '30216'], zsxmDm)\n        && ((ssjmxzDm === '0061042802' && swsxDm === 'SXA031900783') || (ssjmxzDm === '0099042802' && swsxDm === 'SXA031901054'))\n        && bqynsfe > 0\n        && xse < (Number(this.hd.nsqxdm) - 5) * 100000) {\n        return Number(bqynsfe);\n    }\n    if (!ssjmxzDm) {\n        return 0;\n    } else {\n        return jme;\n    }\n}", "description": "减免税费额，当第5列自动带出=0061042802时，本项自动带出=第4列，只读。", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbFjsf.sbFjsfMx[*].swsxDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\"]"}, {"functionXh": "8e37764cd88d4580845c58436d43621e", "formXh": "343dfc71ff284b9e986251d5c0692539", "functionDm": "customGet_XejmjeCheck", "functionMc": "customGet_XejmjeCheck", "functionType": "diy", "functionBody": "function customGet_XejmjeCheck(arr) {\n\tvar index = arr[0];\n\tvar result = 1;\n    var A = this.customGet_fjsXejmje();\n\tvar cjsxejmje = 0;\n\tvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\n\tfor (var i = 0; i < VOs.length; i++){\n\t\tif (VOs[i].zsxmDm === '10109') {\n\t\t\tcjsxejmje = this.round(cjsxejmje + VOs[i].zzsxejmje,2);\n\t\t}\n\t}\n\tif (VOs[index].zsxmDm === '10109' && cjsxejmje !== A) {\n\t\t\tresult = 0;\n\t\t}\n\tif (VOs[index].zsxmDm === '30203' && VOs[index].zzsxejmje !== A) {\n\t\t\tresult = 0;\n\t\t}\n\tif (VOs[index].zsxmDm === '30216' && VOs[index].zzsxejmje !== A) {\n\t\t\tresult = 0;\n\t\t}\n\treturn result;\t\n\t}", "description": "附表二“增值税限额减免金额”列校验,返回1表示通过，0表示不通过", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje\",\"customGet_fjsXejmje\"]"}, {"functionXh": "90e467bfc2a64b75b81202c7bcc38274", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sum", "functionMc": "customGet_sum", "functionType": "diy", "functionBody": "function customGet_sum() {\n    var result = this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').yzzzsbhsxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').yzzzsbhsxse + \n\tthis.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '3').yzzzsbhsxse +\n\tthis.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '4').yzzzsbhsxse +this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').xsczbdcbhsxse + \n\tthis.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '4').xsczbdcbhsxse +this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').xssygdysgdzcbhsxse + \n\tthis.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '3').xssygdysgdzcbhsxse +this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').msxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').msxse + \n\tthis.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '3').msxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '4').msxse +this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').ckmsxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').ckmsxse+\n\tthis.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '3').ckmsxse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '4').ckmsxse;\n    return result;\n}", "description": "第1栏+第4栏+第7栏+第9栏+第13栏", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '1').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '2').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '3').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '4').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '2').xsczbdcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '4').xsczbdcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '1').xssygdysgdzcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '3').xssygdysgdzcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '1').msxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '2').msxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '3').msxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '4').msxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '1').ckmsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '2').ckmsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '3').ckmsxse\",\"getObjFromList(ywbw.sbZzsXgm, 'ewblxh', '4').ckmsxse\"]"}, {"functionXh": "9a76e0cbc0224dde882e59eed4d4f507", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fb1_2_Init", "functionMc": "customGet_fb1_2_Init", "functionType": "diy", "functionBody": "function customGet_fb1_2_Init() {\n\tvar result = 0;\n\tvar fzxx= this.fzxx; \n\tresult = this.round(fzxx.tzxx.fzsbxx.zyfpFw1Ksehj + fzxx.tzxx.fzsbxx.qtfpFw1Ksehj + fzxx.tzxx.fzsbxx.zyfpFw3Ksehj + fzxx.tzxx.fzsbxx.qtfpFw3Ksehj,2);\n\treturn result;\n}", "description": "附表1第2栏初始化", "dependencies": "[\"fzxx.tzxx.fzsbxx.zyfpFw1Ksehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1Ksehj\",\"fzxx.tzxx.fzsbxx.zyfpFw3Ksehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3Ksehj\"]"}, {"functionXh": "9ab8be2dd2a9487a81ef1bff58fa9f3f", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fb1_8_Init", "functionMc": "customGet_fb1_8_Init", "functionType": "diy", "functionBody": "function customGet_fb1_8_Init() {\n\tvar result = 0;\n\tbqfseValue = this.customGet_fb1_2_Init();\n\tvar fzsbxx = this.fzxx.tzxx.fzsbxx; \n\tif(this.ytxx.sbZzsXgmFbFlzl.qcye + this.customGet_fb1_2_Init() <= this.customGet_fb1_5_Init() && bqfseValue != 0){\n\tresult = this.round((fzsbxx.zyfpFwKcq1xsehj + fzsbxx.zyfpFw1sehj - fzsbxx.zyfpFw1Ksehj + fzsbxx.qtfpFwKcq1xsehj + fzsbxx.qtfpFw1sehj - fzsbxx.qtfpFw1Ksehj) / 1.01 + (fzsbxx.zyfpFwKcq3xsehj + fzsbxx.zyfpFw3sehj - fzsbxx.zyfpFw3Ksehj + fzsbxx.qtfpFwKcq3xsehj + fzsbxx.qtfpFw3sehj - fzsbxx.qtfpFw3Ksehj) / 1.03 + (fzsbxx.wpfpFw1xsehj + fzsbxx.wpfpFw3xsehj),2);\n\t}\n\treturn result;\n}", "description": "附表1第8栏初始化", "dependencies": "[\"customGet_fb1_2_Init\",\"customGet_fb1_5_Init\",\"fzxx.tzxx.fzsbxx.zyfpFwKcq1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw1sehj\",\"fzxx.tzxx.fzsbxx.zyfpFw1Ksehj\",\"fzxx.tzxx.fzsbxx.qtfpFwKcq1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1sehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1Ksehj\",\"fzxx.tzxx.fzsbxx.zyfpFwKcq3xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw3sehj\",\"fzxx.tzxx.fzsbxx.zyfpFw3Ksehj\",\"fzxx.tzxx.fzsbxx.qtfpFwKcq3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3sehj\",\"fzxx.tzxx.fzsbxx.qtfpFw3Ksehj\",\"fzxx.tzxx.fzsbxx.wpfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw3xsehj\"]"}, {"functionXh": "9b680aef17204b1db5a208a53064c964", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_dlqyxxysjwfy", "functionMc": "customGet_dlqyxxysjwfy", "functionType": "diy", "functionBody": "function customGet_dlqyxxysjwfy() {\n    var result = 0;\n\tvar sbZzsXgmFbDlqyjxhxx=this.ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx;\n\tfor(var i=0;i<sbZzsXgmFbDlqyjxhxx.length;i++){\n\t\t\tresult+=(sbZzsXgmFbDlqyjxhxx[i].ysjwfy)*1.03;\n\t}\n    return this.round(result, 2);\n}", "description": "应税价外费用", "dependencies": "[\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx\",\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx[*].ysjwfy\"]"}, {"functionXh": "9bb0c4992d9747ddbb1af3cd92b0b301", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsJsyj", "functionMc": "customGet_fjsJsyj", "functionType": "diy", "functionBody": "function customGet_fjsJsyj(zsxmDm) {\n  var result = 0;\n  var DKZPYJZZSSE2FJSJSYJ = Number(this.fzxx.tzxx.fzsbxx.dkzpyjzzsse2fjsjsyj || 0);\n  var sum_zb_24 = this.round(this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '1').bqybtse + this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').bqybtse, 2);\n  if (zsxmDm === '10109') {\n      result = sum_zb_24;\n  } else {\n      var qzd = (Number(this.sbsx.nsqxDm) - 5) * 100000\n      var xse = this.max(this.customGet_sumA() , this.customGet_sumB());\n      if ((xse > qzd) && DKZPYJZZSSE2FJSJSYJ > 0) {\n          result = sum_zb_24 + DKZPYJZZSSE2FJSJSYJ;\n      } else {\n          result = sum_zb_24;\n      }\n  }\n  return this.round(result, 2);\n}", "description": "获取增值税小规模附加税的计税依据", "dependencies": "[\"customGet_sumA\",\"customGet_sumB\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').bqybtse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').bqybtse\",\"ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz\",\"fzxx.tzxx.fzsbxx.qzdType\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').xsczbdcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').msxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','2').ckmsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').yzzzsbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').msxse\",\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').ckmsxse\"]"}, {"functionXh": "a0a761734dba428294241e3adbac08cc", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jmbfgthjmxm", "functionMc": "customGet_jmbfgthjmxm", "functionType": "diy", "functionBody": "function customGet_jmbfgthjmxm() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].hmc.substr(0, 10) === '0001011707'){\n                A = 1;\n            }\n        }\n    } catch (e) { }\n    return A;\n}", "description": "非个体工商户减免项目", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "a4ac6274f9234791ab895087750052d5", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Bqynsejze_col2Init", "functionMc": "customGet_Bqynsejze_col2Init", "functionType": "diy", "functionBody": "function customGet_Bqynsejze_col2Init() {\n  var result = 0;\n  var fzsbxx = this.fzxx.tzxx.fzsbxx;\n  var qzdbz = this.fzxx.tzxx.fzsbxx.qzdType;\n  var hdcfpcqzdQy = this.fzxx.bqxx.hdcfpcqzdQy;\n  var fwbqynse = this.round(\n    this.customGet_Yzzzsbhsxse_col2Init() * 0.03 + this.customGet_Xsczbdcbhsxse_col2Init() * 0.05,\n    2\n  );\n  var hdfwxse = this.getObjFromList(this.ytxx.sbZzsXgm,'ewblxh','2').hdxse;\n  var xejsbqsjdjsehj = this.customGet_Xejsbqsjdjsehj();\n  var sjdjse = 0;\n  if (qzdbz === 3) {\n    var yzzzsbhsxse = this.round(\n      fzsbxx.zyfpHwjlw05xsehj +\n        fzsbxx.zyfpHwjlw1xsehj +\n        fzsbxx.zyfpHwjlw3xsehj +\n        fzsbxx.qtfpHwjlw05xsehj +\n        fzsbxx.qtfpHwjlw1xsehj +\n        fzsbxx.qtfpHwjlw3xsehj +\n        fzsbxx.wpfpHwjlw05xsehj +\n        fzsbxx.wpfpHwjlw1xsehj +\n        fzsbxx.wpfpHwjlw3xsehj,\n      2\n    );\n    var bqynse = this.round(yzzzsbhsxse * 0.03, 2);\n    if (xejsbqsjdjsehj !== 0 && hdcfpcqzdQy === 'N') {\n      sjdjse = xejsbqsjdjsehj -\n        this.min(\n          xejsbqsjdjsehj,\n          bqynse -\n            this.round(\n              (fzsbxx.zyfpHwjlw1xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.wpfpHwjlw1xsehj) * 0.03 -\n                (fzsbxx.zyfpHwjlw1sehj + fzsbxx.qtfpHwjlw1sehj + fzsbxx.wpfpHwjlw1xsehj * 0.01),\n              2\n            ) -\n            this.round(\n              (fzsbxx.zyfpHwjlw05xsehj + fzsbxx.qtfpHwjlw05xsehj + fzsbxx.wpfpHwjlw05xsehj) * 0.03 -\n                (fzsbxx.zyfpHwjlw05sehj + fzsbxx.qtfpHwjlw05sehj + fzsbxx.wpfpHwjlw05xsehj * 0.005),\n              2\n            )\n        );\n    }else if (xejsbqsjdjsehj !== 0 && hdcfpcqzdQy === 'Y') {\n      sjdjse = xejsbqsjdjsehj -\n        this.min(\n          xejsbqsjdjsehj,\n          bqynse -\n            this.round(\n              (fzsbxx.zyfpHwjlw1xsehj + fzsbxx.qtfpHwjlw1xsehj + fzsbxx.wpfpHwjlw1xsehj) * 0.03 -\n                (fzsbxx.zyfpHwjlw1sehj + fzsbxx.qtfpHwjlw1sehj + fzsbxx.wpfpHwjlw1xsehj * 0.01),\n              2\n            ) \n        );\n    }\n    if (fwbqynse !== 0 && hdcfpcqzdQy === 'N') {\n      result = this.round(\n        this.round(\n          (fzsbxx.zyfpFw1xsehj + fzsbxx.qtfpFw1xsehj + fzsbxx.wpfpFw1xsehj) * 0.03 -\n            (fzsbxx.zyfpFw1sehj + fzsbxx.qtfpFw1sehj + fzsbxx.wpfpFw1xsehj * 0.01),\n          2\n        ) +\n          this.round(\n            (fzsbxx.zyfpFw15xsehj + fzsbxx.qtfpFw15xsehj + fzsbxx.wpfpFw15xsehj) * 0.05 -\n              (fzsbxx.zyfpFw15sehj + fzsbxx.qtfpFw15sehj + fzsbxx.wpfpFw15xsehj * 0.015),\n            2\n          ) +\n          sjdjse,\n        2\n      );\n    }else if ((fwbqynse !== 0 || hdfwxse!==0) && hdcfpcqzdQy === 'Y') {\n      result = this.round(\n        this.round(\n          (fzsbxx.zyfpFw1xsehj + fzsbxx.qtfpFw1xsehj + fzsbxx.wpfpFw1xsehj) * 0.03 -\n            (fzsbxx.zyfpFw1sehj + fzsbxx.qtfpFw1sehj + fzsbxx.wpfpFw1xsehj * 0.01),\n          2\n        ) +\n          sjdjse,\n        2\n      );\n    }\n  } else {\n    var yzzzsbhsxse = this.round(fzsbxx.zyfpHwjlw05xsehj + fzsbxx.zyfpHwjlw1xsehj + fzsbxx.zyfpHwjlw3xsehj, 2);\n    var bqynse = this.round(yzzzsbhsxse * 0.03, 2);\n    if (xejsbqsjdjsehj !== 0) {\n      sjdjse = xejsbqsjdjsehj -\n        this.min(\n          xejsbqsjdjsehj,\n          bqynse -\n            this.round(fzsbxx.zyfpHwjlw1xsehj * 0.03 - fzsbxx.zyfpHwjlw1sehj, 2) -\n            this.round(fzsbxx.zyfpHwjlw05xsehj * 0.03 - fzsbxx.zyfpHwjlw05sehj, 2)\n        );\n    }\n    if (fwbqynse !== 0) {\n      result = this.round(\n        this.round(fzsbxx.zyfpFw1xsehj * 0.03 - fzsbxx.zyfpFw1sehj, 2) +\n          this.round(fzsbxx.zyfpFw15xsehj * 0.05 - fzsbxx.zyfpFw15sehj, 2) +\n          sjdjse,\n        2\n      );\n    }\n  }\n  return result;\n}", "description": "主表18栏第2列初始化", "dependencies": "[\"customGet_Yzzzsbhsxse_col2Init\",\"customGet_Xsczbdcbhsxse_col2Init\",\"customGet_Xejsbqsjdjsehj\",\"fzxx.tzxx.fzsbxx.qzdType\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw3xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw3xsehj\",fzxx.tzxx.fzsbxx.wpfpHwjlw05xsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw1Wjhdxsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw3Wjhdxsehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw1sehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw1sehj\",\"fzxx.tzxx.fzsbxx.zyfpHwjlw05sehj\",\"fzxx.tzxx.fzsbxx.qtfpHwjlw05sehj\",\"fzxx.tzxx.fzsbxx.wpfpHwjlw3xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw1xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw1sehj\",\"fzxx.tzxx.fzsbxx.qtfpFw1sehj\",\"fzxx.tzxx.fzsbxx.zyfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw15xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw15sehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15sehj\"]"}, {"functionXh": "afea82bd08e0439cab5eae1693323b78", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jsxmJmxz", "functionMc": "customGet_jsxmJmxz", "functionType": "diy", "functionBody": "function customGet_jsxmJmxz(indexArr) {\n  var index = indexArr[0];\n  var jsxmJmxzs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n  var current = jsxmJmxzs[index].hmc;\n  var gdzcBhsxse = this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse;\n  var hasGdzcJmxz = false;\n  var gdzcJmxz = '0001129902|SXA031900512';\n  for(var i = 0; i < jsxmJmxzs.length; i++) {\n    if (jsxmJmxzs[i].hmc === gdzcJmxz) {\n      hasGdzcJmxz = true;\n    }\n  }\n  // 固定资产不含税销售额不为0，且没有选择减免性质0001129902，且当前行hmc为空\n  if (gdzcBhsxse !== 0 && !hasGdzcJmxz && !current) {\n    return gdzcJmxz;\n  }\n  // 固定资产不含税销售额为0，取消勾选减免性质0001129902\n  if (gdzcBhsxse === 0 && current === gdzcJmxz) {\n    return '';\n  }\n  return current;\n}", "description": "减税项目自动赋值", "dependencies": "[\"getObjFromList(ywbw.sbZzsXgm,'ewblxh','1').xssygdysgdzcbhsxse\"]"}, {"functionXh": "b04f44ccc8a4465198cb2ae227e6118a", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jsxmQmyecheck", "functionMc": "customGet_jsxmQmyecheck", "functionType": "diy", "functionBody": "function customGet_jsxmQmyecheck(hmc,qcye,qmye) {\n    var result = '';\n\t\n    if (hmc.substr(0, 10) === '0001011608' && qcye === 0 && qmye !== 0) {\n        result = '0001011608|SXA031901121|小规模纳税人减按1%征收率征收增值税|《财政部 税务总局关于明确增值税小规模纳税人减免增值税等政策的公告》 财政部 税务总局公告2023年第1号第二条';\n    }\n\tif (hmc.substr(0, 10) === '0001011705' && qcye === 0 && qmye !== 0) {\n        result = '0001011705|SXA031900839|个人出租住房应按照5%的征收率减按1.5%计算应纳增值税|《财政部 国家税务总局关于全面推开营业税改征增值税试点的通知》 财税〔2016〕36号附件2第一条第（九）款第6项';;\n    }\n\tif (hmc.substr(0, 10) === '0001011707' && qcye === 0 && qmye !== 0) {\n        result = '0001011707|SXA031901216|对住房租赁企业适用简易计税方法的，按照5%的征收率减按1.5%征收增值税|《财政部 税务总局 住房城乡建设部关于完善住房租赁有关税收政策的公告》财政部 税务总局 住房城乡建设部公告2021年第24号';\n    }\n\tif (hmc.substr(0, 10) === '0001129935' && qcye === 0 && qmye !== 0) {\n        result = '0001129935|SXA031901124|二手车经销减征增值税（3%减按0.5%）|《财政部 税务总局关于二手车经销有关增值税政策的公告》 财政部 税务总局公告2020年第17号';\n    }\n    return result;\n}", "description": "判断特定减税项目，期初余额为0时期末余额必须为0，符合条件返回空字符串，不符合条件返回提示信息\"减税代码|税务事项代码|减税代码名称|税务事项代码名称\"", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qmye\"]"}, {"functionXh": "b7224c1f9d174812b82314ce277acb88", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_FpBdcxsehj", "functionMc": "customGet_FpBdcxsehj", "functionType": "diy", "functionBody": "function customGet_FpBdcxsehj() {\n    var result = this.round(this.fzxx.tzxx.fzsbxx.zyfpBdcxse + this.fzxx.tzxx.fzsbxx.qtfpBdcxse + this.fzxx.tzxx.fzsbxx.wpfpBdcxse,2);\n\treturn result;\n}", "description": "根据发票信息计算货物销售额合计\n不动产销售额：zyfpBdcxse+qtfpBdcxse+wpfpBdcxse", "dependencies": "[\"fzxx.tzxx.fzsbxx.zyfpBdcxse\",\"fzxx.tzxx.fzsbxx.qtfpBdcxse\",\"fzxx.tzxx.fzsbxx.wpfpBdcxse\"]"}, {"functionXh": "c0baff0b32f04514926fc9688d485c7a", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fb1_10_Init", "functionMc": "customGet_fb1_10_Init", "functionType": "diy", "functionBody": "function customGet_fb1_10_Init() {\n\tvar result = 0;\n\tvar fzxx= this.fzxx; \n\tresult = this.round(fzxx.tzxx.fzsbxx.zyfpFw15Ksehj + fzxx.tzxx.fzsbxx.qtfpFw15Ksehj + fzxx.tzxx.fzsbxx.zyfpFw5Ksehj + fzxx.tzxx.fzsbxx.qtfpFw5Ksehj,2);\n\treturn result;\n}", "description": "附表1第10栏初始化", "dependencies": "[\"fzxx.tzxx.fzsbxx.zyfpFw15Ksehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15Ksehj\",\"fzxx.tzxx.fzsbxx.zyfpFw5Ksehj\",\"fzxx.tzxx.fzsbxx.qtfpFw5Ksehj\"]"}, {"functionXh": "c8eff0700f3348ff8d2b9e03e436484f", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jmbsqqmye", "functionMc": "customGet_jmbsqqmye", "functionType": "diy", "functionBody": "function customGet_jmbsqqmye(indexArr,list) {\n    var index = indexArr[0];\n    var ywbw = this.ywbw;\n    var hmc = ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[index].hmc;\n\tvar A = 0;\n    try {\n        \n        for (var i = 0; i < list.length; i++) {\n            if (list[i].hmc.substr(0, 10) === hmc.substr(0, 10)){\n                A = list[i].qcye;\n            }\n        }\n    } catch (e) { }\n    return Number((A).toFixed(2));\n}", "description": "获取减免表减税项目期初余额", "dependencies": "[\"ytxx.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].swsxDm\",\"ytxx.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\",\"ytxx.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].qcye\"]"}, {"functionXh": "ca84bcb9cbcf4588bf7b98312b40eb23", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_phjmse_gs1", "functionMc": "customGet_phjmse_gs1", "functionType": "diy", "functionBody": "function customGet_phjmse_gs1(phjmse, ybzzs, zzsxejmje, bqynsfe, jme, phjzbl) {\n  var sum = ybzzs + zzsxejmje;\n  var result = (bqynsfe - jme) * phjzbl * 0.01;\n  if (sum >= 0 && phjmse !== result) {\n    return false;\n  }\n  return true;\n}", "description": "当第一列“增值税税额”+第二列“增值税限额减免金额”>=0时 =表样公式，只读", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*]\",\"ywbw.sbFjsf.sbFjsfMx[*].phjmse\",\"ywbw.sbFjsf.sbFjsfMx[*].ybzzs\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\",\"ywbw.sbFjsf.sbFjsfMx[*].phjzbl\"]"}, {"functionXh": "cce02b9ca21b4205b1a66306c177d214", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_swsxDminit", "functionMc": "customGet_swsxDminit", "functionType": "diy", "functionBody": "function customGet_swsxDminit(zsxmDm, bqynsfe) {\n    var result = \"\";\n    var xse = this.max(this.customGet_sumA(), this.customGet_sumB());\n\n    if (xse <= Number(this.fzxx.tzxx.fzsbxx.qzd) && bqynsfe > 0) {\n        if (zsxmDm === '30203') {\n            result = \"SXA031900783\";\n        } else if (zsxmDm === '30216') {\n            result = \"SXA031901054\";\n        }\n\n    }\n    return result;\n}", "description": "customGet_swsxDminit", "dependencies": "[\"customGet_sumA\",\"customGet_sumB\",\"fzxx.tzxx.fzsbxx.qzd\"]"}, {"functionXh": "d0eb92051c69462cadbe79be0c65a58f", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fjsJmxz", "functionMc": "customGet_fjsJmxz", "functionType": "diy", "functionBody": "function customGet_fjsJmxz(indexArr) {\n  var index = indexArr[0];\n  var ywbw = this.ywbw;\n  var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm;\n  var ynse = ywbw.sbFjsf.sbFjsfMx[index].bqynsfe;\n  var ssjmxzDm = ywbw.sbFjsf.sbFjsfMx[index].ssjmxzDm || '';\n  var qzd = (Number(this.sbsx.nsqxDm) - 5) * 100000;\n  var xse = this.max(this.customGet_sumA() ,this.customGet_sumB());\n  if (this.inArray(['30203', '30216'], zsxmDm)) {\n      if (ynse > 0 && xse <= qzd) {\n          return zsxmDm === '30203' ? '0061042802' : '0099042802'\n      } else if (this.inArray(['0061042802', '0099042802'], ssjmxzDm)) {\n          return '';\n      }\n  }\n  if (ynse === 0) {\n      return '';\n  } else {\n      return ssjmxzDm;\n  }\n}", "description": "设置附加税减免", "dependencies": "[\"customGet_sumA\",\"customGet_sumB\",\"getObjFromList(this.ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse\",\"getObjFromList(this.ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\"]"}, {"functionXh": "d2500c3f877748fe9cc6f030fbb638c9", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_sumB", "functionMc": "customGet_sumB", "functionType": "diy", "functionBody": "  function customGet_sumB() {\n    var result = Number(this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').hdxse) + Number(this.getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').hdxse);\n\treturn result;\n}", "description": " 免税标准判断：\nB: 核定销售（货物劳务（YSHWHDXSE）+服务、不动产和无形资产（YSFWHDXSE））", "dependencies": "[\"getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','2').hdxse\",\"getObjFromList(this.ywbw.sbZzsXgm,'ewblxh','1').hdxse)\"]"}, {"functionXh": "d3adc4d761a644b592839d1ad6b97a49", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jmxzdminit", "functionMc": "customGet_jmxzdminit", "functionType": "diy", "functionBody": "function customGet_jmxzdminit(indexArr) {\n    var index = indexArr[0];\n    var ywbw= this.ywbw;\n    var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm;\n    var bqynsfe = ywbw.sbFjsf.sbFjsfMx[index].bqynsfe;\n    var ssjmxzDm = ywbw.sbFjsf.sbFjsfMx[index].ssjmxzDm || '';\n    var xse = this.max(this.customGet_sumA(), this.customGet_sumB());\n\n    if (this.inArray(['30203', '30216'], zsxmDm)) {\n        if (bqynsfe > 0 && xse < (Number(this.sbsx.nsqxdm) - 5) * 100000) {\n            return zsxmDm === '30203' ? '0061042802' : '0099042802'\n        } else if (this.inArray(['0061042802', '0099042802'], ssjmxzDm)) {\n            return '';\n        }\n    }\n    if (bqynsfe === 0) {\n        return '';\n    } else {\n        return ssjmxzDm;\n    }\n}", "description": "自动取数：当销售额≤起征点（按月10万，按季30万）且本行第4列＞0时，本栏自动带出减免性质，可修改。", "dependencies": "[\"customGet_sumA\",\"customGet_sumB\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\"]"}, {"functionXh": "dc96b43367d04af7a90967a46e4fd56f", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_jmxzdm", "functionMc": "customGet_jmxzdm", "functionType": "diy", "functionBody": "function customGet_jmxzdm(zsxmDm, bqynsfe, ssjmxzDm, jme) {\n    var result = 111;\n    var xse = this.max(this.customGet_sumA(), this.customGet_sumB());\n    if (xse <= Number(this.fzxx.tzxx.fzsbxx.qzd) && bqynsfe > 0) {\n        if (zsxmDm === '30203') {\n            if (!(ssjmxzDm !== \"0061042802\" || jme !== bqynsfe)) {\n                result = 302031;\n            }\n        } else if (zsxmDm === '30216') {\n            if (!(ssjmxzDm !== \"0099042802\" || jme !== bqynsfe)) {\n                result = 302161;\n            }\n        }\n    }\n    if (xse > Number(this.fzxx.tzxx.fzsbxx.qzd)) {\n        if (zsxmDm === '30203') {\n            if (ssjmxzDm !== \"0061042802\") {\n                result = 302030;\n            }\n        } else if (zsxmDm === '30216') {\n            if (ssjmxzDm !== \"0099042802\") {\n                result = 302160;\n            }\n        }\n    }\n    return result;\n}", "description": "销售额未达起征点时，强制享受全额免征，具体逻辑如下：\n1、“教育费附加”行：当销售额≤10万(按季30万)且第4行＞0时，若【第5列“减免性质代码”≠0061042802 或 第6列≠第4列】时，给出提示“您满足减免性质代码为0061042802的减免条件，请选择该减免性质代码后再申报！”\n2、“地方教育附加”行：当销售额≤10万(按季30万)且第4行＞0时，若【第5列“减免性质代码”≠0099042802 或 第6列≠第4列】时，给出提示“您满足减免性质代码为0099042802的减免条件，请选择该减免性质代码后再申报！”\n销售额已达起征点时，不能享受起征点以下的全额免征，具体逻辑如下：\n1、“教育费附加”行：当销售额＞10万(按季30万)时，若【第5列“减免性质代码”=0061042802】时，给出提示“您不满足减免性质代码为0061042802的减免条件，请核实！”\n2、“地方教育附加”行：当销售额＞10万(按季30万)时，若【第5列“减免性质代码”=0099042802时】时，给出提示“您不满足减免性质代码为0099042802的减免条件，请核实！”", "dependencies": "[\"customGet_sumA\",\"customGet_sumB\",\"ywbw.sbFjsf.sbFjsfMx[*]\",\"ywbw.sbFjsf.sbFjsfMx[*].zsxmDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\"]"}, {"functionXh": "dde19666f56b4b95935699ff4339d12d", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "compareDateSameYear", "functionMc": "compareDateSameYear", "functionType": "diy", "functionBody": "function compareDateSameYear(date1, date2) {\n    if (!!date1 && !!date2) {\n        var oDate1 = new Date(date1).getFullYear();\n        var oDate2 = new Date(date2).getFullYear();\n        if (oDate1 === oDate2) {\n            return true;\n        } else {\n            return false;\n        }\n    }\n}", "description": "判断两时间是否同一年份", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.bchssqq\",\"ywbw.sbFjsf.sbFjsfQtxx.bchssqz\"]"}, {"functionXh": "e1d1bc0cf68c45098800329b9e78b584", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_fb1_13_Init", "functionMc": "customGet_fb1_13_Init", "functionType": "diy", "functionBody": " function customGet_fb1_13_Init() {\n\tvar result = 0;\n\tvar fzxx= this.fzxx; \n\tbqfse5Value = this.customGet_fb1_10_Init();\n\tif(bqfse5Value !=0){\n\t  result = this.round(fzxx.tzxx.fzsbxx.zyfpFwKcq5xsehj + fzxx.tzxx.fzsbxx.zyfpFw5sehj + fzxx.tzxx.fzsbxx.qtfpFwKcq5xsehj + fzxx.tzxx.fzsbxx.qtfpFw5sehj + fzxx.tzxx.fzsbxx.zyfpFwKcq15xsehj + fzxx.tzxx.fzsbxx.zyfpFw15sehj + fzxx.tzxx.fzsbxx.qtfpFwKcq15xsehj + fzxx.tzxx.fzsbxx.qtfpFw15sehj + fzxx.tzxx.fzsbxx.wpfpFw5xsehj * 1.05 + fzxx.tzxx.fzsbxx.wpfpFw15xsehj * 1.015,2);\n\t  }\n\telse{\n\tresult = 0;\n\t}\n\treturn result;\n}", "description": "附表1第13栏初始化", "dependencies": "[\"customGet_fb1_10_Init\",\"fzxx.tzxx.fzsbxx.zyfpFwKcq5xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw5sehj\",\"fzxx.tzxx.fzsbxx.qtfpFwKcq5xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw5sehj\",\"fzxx.tzxx.fzsbxx.zyfpFwKcq15xsehj\",\"fzxx.tzxx.fzsbxx.zyfpFw15sehj\",\"fzxx.tzxx.fzsbxx.qtfpFwKcq15xsehj\",\"fzxx.tzxx.fzsbxx.qtfpFw15sehj\",\"fzxx.tzxx.fzsbxx.wpfpFw5xsehj\",\"fzxx.tzxx.fzsbxx.wpfpFw15xsehj\"]"}, {"functionXh": "e20e8b90ee244bbfae8adf7ed24202fe", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_Bbqzdbz", "functionMc": "customGet_Bbqzdbz", "functionType": "diy", "functionBody": "function customGet_Bbqzdbz() {\n  var qzdbz = 1;\n  var qzd = this.fzxx.tzxx.fzsbxx.qzd;\n  var fpxsehj = this.customGet_sumA();\n  var hdxsehj = this.customGet_sumB();\n  var bdc = this.getObjFromList(this.ywbw.sbZzsXgm, 'ewblxh', '2').bdcxse;\n  if (this.max(fpxsehj , hdxsehj) <= qzd) {\n    qzdbz = 1;\n  } else if (this.max(fpxsehj , hdxsehj) - bdc <= qzd) {\n    qzdbz = 2;\n  } else {\n    qzdbz = 3;\n  }\n  return qzdbz;\n}", "description": "根据报表数据获取初始化起征点标志（1/2/3）：\n起征点1：max( （货物发票销售额合计  + 服务发票销售额合计）， （货物核定销售额合计 + 服务核定销售额合计）  )小于等于起征点(QZD)\n起征点2：max( （货物发票销售额合计  + 服务发票销售额合计）， （货物核定销售额合计 + 服务核定销售额合计）  )大于起征点，减去不动产销售额小于等于起征点(QZD)\n起征点3：max( （货物发票销售额合计  + 服务发票销售额合计）， （货物核定销售额合计 + 服务核定销售额合计）  )大于起征点，减去不动产销售额大于起征点(QZD)", "dependencies": "[\"customGet_sumA\",\"customGet_sumB\",\"fzxx.tzxx.fzsbxx.qzd\",\"getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','HWJLW_BQS').hdxse\",\"getObjFromList(ytxx.sbZzsXgm,'ewbhlbm','FWBDCHWXZC_BQS').hdxse\",\"customGet_BbFwxsehj\",\"getObjFromList(ywxx.sbZzsXgm,'ewblxh','2').bdcxse\"]"}, {"functionXh": "e83c588f7bdd4d02ac89bf11cd682846", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_phjmse", "functionMc": "customGet_phjmse", "functionType": "diy", "functionBody": "function customGet_phjmse(zsxmDm, ybzzs, zzsxejmje, bqynsfe, jme, phjzbl) {\n  var result = 0;\n  var sum = ybzzs + zzsxejmje;\n  if (sum >= 0) {\n    result = (bqynsfe - jme) * phjzbl;\n  } else {\n    var FjssqsfxsphjmVO = this.hd.FjssqsfxsphjmVO;\n    if (FjssqsfxsphjmVO) {\n      for (var i = 0; i < FjssqsfxsphjmVO.length; i++) {\n        if (FjssqsfxsphjmVO[i].zsxmDm === zsxmDm && FjssqsfxsphjmVO[i].yywxsphjm === 'Y') {\n          result = (bqynsfe - jme) * phjzbl * 0.01;\n        }\n      }\n    }\n  }\n  return this.round(result, 2);\n}", "description": "1）当第一列“增值税税额”+第二列“增值税限额减免金额”<0时：\na.若对应征收项目的yywxsphjm为Y，则第8栏减征额自动按表样公式计算;b.若对应征收项目的yywxsphjm为N，则第8栏减征额自动带出0\n2）当第一列“增值税税额”+第二列“增值税限额减免金额”>=0时：规则不变：“=表样公式”。", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*]\",\"ywbw.sbFjsf.sbFjsfMx[*].zsxmDm\",\"ywbw.sbFjsf.sbFjsfMx[*].ybzzs\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\",\"ywbw.sbFjsf.sbFjsfMx[*].phjzbl\"]"}, {"functionXh": "f824631062dc479f85b84b2b5b62bb30", "formXh": "e3aacb54eeed4abf9d95c11aeed4dd83", "functionDm": "customGet_dlqyxxyj", "functionMc": "customGet_dlqyxxyj", "functionType": "diy", "functionBody": "function customGet_dlqyxxyj() {\n    var result = 0;\n\tvar sbZzsXgmFbDlqyjxhxx=this.ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx;\n\tfor(var i=0;i<sbZzsXgmFbDlqyjxhxx.length;i++){\n\t\t\tresult+=(sbZzsXgmFbDlqyjxhxx[i].xsdl)*(sbZzsXgmFbDlqyjxhxx[i].deslhyzl);\n\t}\n    return this.round(result, 2);\n}", "description": "电力企业销项中增值税应纳税额", "dependencies": "[\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx\",\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx[*].xsdl\",\"ywbw.sbZzsXgmFbDlqyjxhxx.sbZzsXgmFbDlqyxxjxXx[*].deslhyzl\"]"}]