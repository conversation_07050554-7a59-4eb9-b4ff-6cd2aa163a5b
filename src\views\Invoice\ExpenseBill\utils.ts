import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "InvoiceExpenseBill";
export const noShowAllColumns: Array<IColumnProps> = [
    { slot: "selection", width: 36, headerAlign: "center", align: "center" },
    { slot: "date" },
    { slot: "description" },
    { slot: "expenseType" },
    { slot: "payee" },
    { slot: "paymentMethod" },
    { slot: "deductibleTax" },
    { slot: "amountIncludingTax" },
    { slot: "voucherNumber" },
    { slot: "operation" },
    { slot: "tranNo" },
];
export const showAllColumns: Array<IColumnProps> = [
    { slot: "selection", width: 36, headerAlign: "center", align: "center" },
    { slot: "date" },
    { slot: "description" },
    { slot: "expenseType" },
    { slot: "department" },
    { slot: "payee" },
    { slot: "paymentMethod" },
    {
        label: "单据金额（不含税）",
        prop: "amount",
        align: "right",
        headerAlign: "right",
        minWidth: 135,
        width: getColumnWidth(setModule, 'amount'),
        formatter(row, column, cellValue) {
            return formatMoney(cellValue);
        },
    },
    { slot: "deductibleTax" },
    { slot: "amountIncludingTax" },
    { 
        label: "单据来源", 
        prop: "billSource", 
        align: "left", 
        headerAlign: "left", 
        minWidth: 120,
        width: getColumnWidth(setModule, 'billSource'), 
    },
    { slot: "note" },
    { slot: "voucherNumber" },
    { slot: "operation" },
    { slot: "tranNo" },
];
export function objectsEqual(obj1: any, obj2: any) {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    // 检查并手动更改条件
    if (obj2.depId === null) {
        obj2.depId = 0;
    }
    if (obj2.payeeId === null) {
        obj2.payeeId = 0;
    }
    if (obj2.tax === "") {
        obj2.tax = 0;
    }
    if (keys1.length !== keys2.length) {
        return false;
    }

    for (const key of keys1) {
        if (obj1[key] !== obj2[key]) {
            return false;
        }
    }

    return true;
}
export function incrementString(inputStr: string) {
    // 使用正则表达式匹配字符串中的数字部分和字母部分
    const match = inputStr.match(/([a-zA-Z]*)(\d*)([a-zA-Z]*)/);
    if (match) {
        // 提取匹配的字母部分和数字部分
        const letters = match[1];
        const number = parseInt(match[2], 10);
        const endingLetters = match[3];
        if (number) {
            // 将数字部分加1
            const incrementedNumber = number + 1;
            // 组合新的字符串
            const result = letters + incrementedNumber + endingLetters;
            return result.slice(0, 3);
        } else {
            return "";
        }
    } else {
        return "";
    }
}
export const sourceTypeList = [
    { value: 0, label: "全部" },
    { value: 1, label: "手动新增" },
    { value: 10, label: "手动导入" },
    { value: 20, label: "企业微信导入" },
    { value: 30, label: "钉钉导入" },
];
