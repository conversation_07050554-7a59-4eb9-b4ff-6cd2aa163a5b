<template>
    <div class="container">
        <div>哎呀，您的账号在其他地方登录了....</div>
        <a :class="{ 'lemon-client': isInLemonClient }" @click="relogin"></a>
    </div>
</template>
<style scoped lang="less">
.container {
    height: 100%;
    width: 100%;
    background: url("@/assets/410/410-bg.png") no-repeat;
    background-size: 1920px 1080px;
    background-position: center;

    div {
        font-size: 48px;
        font-weight: 500;
        color: #302c48;
        line-height: 67px;
        padding-top: calc((100vh - 1080px) / 2 + 360px);
        text-align: center;
    }

    a {
        display: block;
        width: 344px;
        height: 108px;
        background: url("@/assets/410/410-btn.png") no-repeat;
        background-size: 100%;
        margin: 63px auto 0;
        cursor: pointer;
    }
}
</style>
<script lang="ts">
export default {
    name: "410",
};
</script>
<script lang="ts" setup>
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { logout } from "@/util/url";
import { ref } from "vue";

const isInLemonClient = ref(isLemonClient());

const relogin = () => {
    if (top === null || top === window || isInLemonClient.value) {
        if (!isInLemonClient.value) {
            logout();
        } else {
            getLemonClient().backToRelogin();
        }
    } else {
        logout();
    }
};
</script>
