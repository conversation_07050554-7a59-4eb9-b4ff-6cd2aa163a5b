<template>
    <el-dialog v-model="openResultDialog" title="提示" center width="574px" @close="close" class="dialogDrag">
        <div class="resultdialog" v-dialogDrag>
            <div class="resultdialog-content">
                <div class="result-title">恭喜，您的预约开户申请已提交至{{ bank }}</div>
                <div class="result-info">您可以前往【预约开户-进度查询】中查询预约开户进度或关注柠檬云微信公众号获取~</div>
                <img class="official-qrcode" src="@/assets/Invoice/wxpublic.png" />
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="openResultDialog = false">关闭</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
const props = withDefaults(
    defineProps<{
        modelValue: boolean;
        bank: string;
    }>(),
    {
        bank: "",
    }
);
const emit = defineEmits<{
    (event: "update:modelValue", args: boolean): void;
    (event: "close"): void;
}>();

const openResultDialog = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit("update:modelValue", val);
    },
});
const close = () => {
    emit("close");
};
</script>

<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
