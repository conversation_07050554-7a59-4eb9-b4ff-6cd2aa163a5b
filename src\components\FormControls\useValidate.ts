import { ref, computed } from "vue"

// 定义校验规则类型
export interface Rule {
  required?: boolean
  message?: string
  pattern?: RegExp | string
  validator?: (value: any, rule: Rule) => boolean | Promise<boolean>
  trigger?: "blur" | "change" | "select" | string[]
  min?: number
  max?: number
  type?: "string" | "number" | "boolean" | "array" | "object" | "date"
  validateType?: 1 | 2 // 1: 提示型校验, 2: 强制型校验
}

export interface ValidateProps {
  rule?: Rule // 规则对象
  validateTrigger?: string // 全局默认的校验触发时机
}

export interface ValidateEmits {
  (e: "update:modelValue", value: any): void
  (e: "change", value: any): void
  (e: "blur", value: any): void
  (e: "focus", value: any): void
}

export function useValidate(props: ValidateProps, emits: ValidateEmits, modelValue: any) {
  // 内部值
  const innerValue = computed({
    get() {
      return modelValue.value
    },
    set(newValue) {
      modelValue.value = newValue
      emits("update:modelValue", newValue)
    },
  })

  // 校验状态
  const isValid = ref(true)
  const errorMessage = ref("")
  const showError = ref(false)

  // 边框样式
  const borderClass = computed(() => {
    if (isValid.value || !showError.value) {
      return "common-border"
    } else {
      // 根据校验类型返回不同的样式
      if (props.rule && props.rule.validateType) {
        // 否则使用规则的validateType值
        return props.rule.validateType === 1 ? "prompt-border" : "force-border"
      } else {
        // 默认为强制型校验
        return "force-border"
      }
    }
  })

  // 事件处理
  function handleChange(value: any) {
    emits("change", value)

    if (props.validateTrigger === "change" || (typeof props.validateTrigger === "string" && props.validateTrigger.includes("change"))) {
      validate()
    }
  }

  function handleBlur() {
    emits("blur", innerValue.value)

    if (props.validateTrigger === "blur" || (typeof props.validateTrigger === "string" && props.validateTrigger.includes("blur"))) {
      validate()
    }
  }

  function handleFocus() {
    showError.value = false
    emits("focus", innerValue.value)
  }

  // 校验方法
  async function validate(): Promise<boolean> {
    // 如果没有规则，则认为校验通过
    if (!props.rule) {
      isValid.value = true
      errorMessage.value = ""
      return true
    }

    const rule = props.rule
    debugger
    try {
      // 必填校验
      if (rule.required && (innerValue.value === undefined || innerValue.value === null || innerValue.value === "")) {
        isValid.value = false
        errorMessage.value = rule.message || "此项为必填项"
        showError.value = true
        return false
      }

      // 自定义校验
      if (rule.validator) {
        const result = await rule.validator(innerValue.value, rule)
        if (!result) {
          isValid.value = false
          errorMessage.value = rule.message || "校验失败"
          showError.value = true
          return false
        }
      }
    } catch (error) {
      isValid.value = false
      errorMessage.value = error instanceof Error ? error.message : "校验出错"
      showError.value = true
      return false
    }

    // 规则通过
    isValid.value = true
    errorMessage.value = ""
    showError.value = false
    return true
  }

  return {
    innerValue,
    isValid,
    errorMessage,
    showError,
    borderClass,
    handleChange,
    handleBlur,
    handleFocus,
    validate,
  }
}
