import NProgress from "nprogress";
import "nprogress/nprogress.css";
import router from "@/router";
import { constantRoutes } from "@/router";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { getToken } from "@/util/cache/cookie";
import { getGlobalToken, getQueryStringByName } from "@/util/baseInfo";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "@/store/modules/user";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useAssistingAccountingStoreHook } from "@/store/modules/assistingAccouting";
import { useAccountPeriodStoreHook } from "@/store/modules/accountPeriod";
import { useVoucherGroupStoreHook } from "@/store/modules/voucherGroup";
import { useVoucherSettingsStore } from "@/store/modules/voucherSettings";
import { useAccountSubjectStoreHook } from "@/store/modules/accountSubject";
import { useRouter<PERSON><PERSON>yStoreHook } from "@/store/modules/routerArray";
import { useTrialStatusStoreHook } from "@/store/modules/trialStatus";
import { tryShowAutoDialog } from "@/util/autoDialog";
import { isLemonClient } from "@/util/lmClient";
import { appendAACompanyId, appendAppasid } from "@/util/url";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useAsubCodeLengthStoreHook } from "@/store/modules/asubCodeLength";
import { pushLoginMessage } from "@/util/pushMessage";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, _from, next) => {
    if (!isLemonClient() && !window.isErp) {
        NProgress.start();
    }
    const userStore = useUserStoreHook();
    const accountsetStore = useAccountSetStoreHook();
    const permissionStore = usePermissionStoreHook();
    const thirdPartInfoStore = useThirdPartInfoStoreHook();
    const assistingAccountingStore = useAssistingAccountingStoreHook();
    const accountPeriodStore = useAccountPeriodStoreHook();
    const voucherGroupStore = useVoucherGroupStoreHook();
    const voucherSettingsStore = useVoucherSettingsStore();
    const accountSubjectStore = useAccountSubjectStoreHook();
    const routerArrayStore = useRouterArrayStoreHook();
    const trialStatusStore = useTrialStatusStoreHook();
    const asubCodeLengthStore = useAsubCodeLengthStoreHook();
    const token = getToken();
    if (token) {
        if (to.path === "/login") {
            // 如果已经登录，并准备进入 Login 页面，则重定向到主页
            next({ path: "/" });
            NProgress.done();
        } else {
            const globalToken = getGlobalToken();
            if (globalToken !== "") {
                // 当前appasid与请求的不相同就去重新请求，不考虑当前权限是否为空，
                // 否则如果用户权限确实为空或接口返回空，会死循环
                const hasAccountSet = JSON.parse(localStorage.getItem("hasAccountSet") || "true");
                if (globalToken !== accountsetStore.appasid && !accountsetStore.accountSet && hasAccountSet) {
                    try {
                        await accountsetStore.getPermission();
                        try {
                            await Promise.all([
                                accountsetStore.getAccountSet(),
                                accountsetStore.getUserInfo(),
                                assistingAccountingStore.getAssistingAccountingType(),
                                assistingAccountingStore.getAssistingAccounting(),
                                assistingAccountingStore.getDepartment(),
                                accountPeriodStore.getPeriods(),
                                userStore.getUserName(),
                                voucherGroupStore.getVoucherGroup(),
                                thirdPartInfoStore.getThirdPartInfo(),
                                voucherSettingsStore.getVoucherSettings(),
                                accountSubjectStore.getAccountSubject(),
                                trialStatusStore.getTrialStatus(),
                                asubCodeLengthStore.getAsubCodeLength(),
                            ]);
                            pushLoginMessage();
                        } catch (e) {
                            ElMessage.error("获取账套信息失败，请刷新重试");
                        }
                        const permissions = accountsetStore.permissions;
                        permissionStore.setRoutes(permissions);
                        // 将 '有访问权限的动态路由' 添加到 Router 中
                        permissionStore.dynamicRoutes.forEach((route) => {
                            router.addRoute(route);
                        });
                        if(permissions.includes('10001')){
                            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.permissionEdit });
                        }
                        if(window.isProSystem) {
                            trialStatusStore.getProStatus();
                        }
                        // 确保添加路由已完成
                        // 设置 replace: true, 因此导航将不会留下历史记录
                        tryShowAutoDialog();
                        if (
                            to.path.toLocaleLowerCase() !== "/default/default" &&
                            !window.isErp &&
                            !useThirdPartInfoStoreHook().isThirdPart &&
                            !useThirdPartInfoStoreHook().isOEM
                        ) {
                            if (getQueryStringByName("stay") === "true") {
                                routerArrayStore.enterRouter({
                                    name: "Default",
                                    title: "首页",
                                    path: "/Default/Default",
                                    fullPath: appendAACompanyId(appendAppasid("/Default/Default")),
                                    alive: false,
                                    cache: true,
                                });
                            } else {
                                next(appendAACompanyId(appendAppasid("/Default/Default")));
                                return;
                            }
                        }
                        next({ ...to, replace: true });
                    } catch (err: any) {
                        // 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
                        userStore.logoutDialog();
                        // ElMessage.error(err.message || "路由守卫过程发生错误");
                        //next("/login");
                        NProgress.done();
                    }
                } else {
                    next();
                }
            } else {
                try {
                    to.path !== "/410" && await accountsetStore.getUserInfo();
                } catch (e) {
                    ElMessage.error("获取账套信息失败，请刷新重试");
                }
                //未携带appasid可访问页面白名单
                switch (to.path) {
                    case "/Settings/AccountSets1":
                    case "/Settings/TransferPro":
                    case "/Default/PersonalInfo":
                    case "/MasterPages/MessageSearch":
                    case "/MasterPages/AccountingEntry":
                    case "/Pro/PayDialog":
                    case "/410":
                    case "/Cashier/AlipayImportJournalRedirectRoute":
                    case "/Settings/ImportFromOther":
                    case "/":
                    case "/Default/Default": {
                        if (
                            to.path === "/Settings/AccountSets1" ||
                            to.path === "/410" ||
                            to.path === "/Cashier/AlipayImportJournalRedirectRoute" ||
                            to.path === "/" ||
                            to.path === "/Default/Default" ||
                            routerArrayStore.routerArray.length > 0
                        ) {
                            next();
                            break;
                        } else {
                            next("/Settings/AccountSets1");
                            return;
                        }
                    }
                }

                NProgress.done();
            }
            let iframeComponet = undefined;
            const route = constantRoutes.find((item) => item.path === to.path) as any;
            if (route.iframeComponet) {
                iframeComponet = route.iframeComponet;
            }
            routerArrayStore.enterRouter({
                name: to.name?.toString() || "",
                title: to.meta.title || to.fullPath,
                path: to.path,
                fullPath: to.fullPath,
                alive: false,
                cache: true,
                iframeComponet: iframeComponet,
            });
            routerArrayStore.rememberScrollTop(_from.path);
        }
    } else {
        //未登录用户可访问页面白名单
        //next("/login");
        if (to.path === "/Cashier/AlipayImportJournalRedirectRoute") {
            next();
        } else {
            userStore.logoutDialog();
        }
        NProgress.done();
    }
});

router.afterEach(() => {
    setTimeout(() => {
        const routerArrayStore = useRouterArrayStoreHook();
        routerArrayStore.restoreScrollTop();
    });
    NProgress.done();
});
