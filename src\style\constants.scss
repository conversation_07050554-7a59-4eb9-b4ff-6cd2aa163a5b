//color
:root {
  --main-color: #44b449; //主题色
  --menu-bg-color: #021102;
  --menu-font-color: #cccccc; //菜单字体颜色
  --bs-table-color: #ffffff; //报税表格单元格底色
  --bs-table-hover-color: #e5f5e5; //报税表格hover颜色
  --bs-table-title-color: #f2f8f4; //报税表格表头颜色
  --font-color: #333333; //默认字色
  --table-color: var(--bs-table-color); //表格单元格底色
  --table-hover-color: var(--bs-table-hover-color); //表格hover颜色
  --table-title-color: var(--bs-table-title-color); //表格表头颜色
  --table-selected-color: #f0f7f0;
  --table-selected-hover-color: #f0f7f0;
  --border-color: #dadada; //分割线颜色
  --table-striped-color: #f8faf7; // 表格斑马纹颜色
  --white: #ffffff; //默认白色
  --black: #000000; //默认黑色
  --orange: #ffbf00; //橘黄
  --grey: #666666; // 灰色
  --background-color: #f6f6f6; //背景色
  --dark-grey: #333333;
  --blue: #3d7fff;
  --light-blue: #5E8BE0;
  --red: #fd5055;
  --yellow: #ff991e; //黄
  --light-green: #6ed773; //亮绿，实心按钮hover颜色
  --dark-green: #26922b; //暗绿，按钮点击效果
  --button-border-color: #d8d8d8; //按钮边框颜色
  --light-main-color: var(--light-green); //亮绿，实心按钮hover颜色
  --dark-main-color: var(--dark-green); //暗绿，按钮点击效果
  --el-color-primary: var(--main-color); //element-plus主题色
  --button-text-color: var(--blue);
  //文本框变量
  --input-border-color: var(--border-color);
}

//size
:root {
  --menu-container-width: 140px;
  --router-container-margin: 12px;
  --edit-content-width: 1000px;
  //字号
  --h1: 20px;
  --h2: 18px;
  --h3: 16px;
  --h4: 14px;
  --h5: 12px;
  --font-size: var(--h4);
  --line-height: 20px;
  //表格变量
  --table-title-height: 37px;
  --table-title-line-height: 17px;
  --table-title-font-size: var(--h5);
  --table-body-height: 37px;
  --table-body-line-height: 17px;
  --table-body-font-size: var(--h5);
}

// 其他常用变量
:root {
  --transition-time: 0.2s;
}

// element-plus中变量值更新
body {
  --el-border-color-lighter: var(--border-color);

  .el-table {
    --el-table-header-text-color: var(--font-color);
    --el-table-header-bg-color: var(--table-title-color);
    --el-table-row-hover-bg-color: var(--table-hover-color);
    --el-table-current-row-bg-color: var(--table-selected-color);
    --el-fill-color-lighter: var(--table-striped-color);
    --el-fill-color-light: var(--table-title-color)
  }

  .el-button--primary {
    --el-button-hover-border-color: var(--light-main-color);
    --el-button-hover-bg-color: var(--light-main-color);
  }

  .el-dialog {
    --el-dialog-font-line-height: 52px;
    --el-dialog-title-font-size: var(--h3);
    --el-dialog-padding-primary: 0;
}
}
