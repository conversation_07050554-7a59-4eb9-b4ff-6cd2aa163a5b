<template>
    <el-table
        :class="isErp ? 'erp-table' : ''"
        :data="props.tableData"
        border
        :empty-text="props.emptyText"
        fit
        stripe
        scrollbar-always-on
        highlight-current-row
        class="custom-table"
        row-key="lineID"
        :row-class-name="setTitleRowStyle"
        ref="tableRef"
        @cell-dblclick="handleCellClick"
        @header-dragend="headerDragend"
    >
        <el-table-column 
            label="项目" 
            min-width="340" 
            align="left" 
            headerAlign="center"
            prop="proName"
            :width="getColumnWidth(setModule, 'proName')"
        >
            <template #default="scope">
                <div>
                    <div :class="[assertNameClass(scope.row)]" :title="scope.row.proName">
                        <span>{{ scope.row.proName }}</span>
                        <template v-if="assertShowEquationEdit(scope.row)">
                            <div
                                class="link"
                                v-if="useAccountSetStoreHook().permissions.includes('incomestatement-canedit')"
                                @click="openEquationDialog(scope.row.lineType, scope.row.lineID, scope.row.statementId, scope.row.proName)"
                            >
                                编辑公式
                            </div>
                        </template>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="行次" 
            min-width="60" 
            align="left" 
            header-align="left" 
            prop="lineNumber" 
            :formatter="rowNumberFormatter"
            :width="getColumnWidth(setModule, 'lineNumber')"
        >
        </el-table-column>
        <template v-if="isShowIncome">
            <el-table-column 
                label="本年累计金额" 
                min-width="280" 
                align="right" 
                header-align="right"
                prop="yearTotal"
                :width="getColumnWidth(setModule, 'yearTotal')"
            >
                <template #header>
                  <div class="table-header">
                    <el-icon><Switch @click="swapColumns"/></el-icon>
                    <span>本年累计金额</span>
                  </div>
                </template>
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.yearTotal"
                        :formula="calcFormula(scope.row, 0)"
                        :line-number="scope.row.lineNumber"
                        :queryFormulas="isQueryFormulas(scope.row)"
                        @go-to-query-formulas="goToQueryFormulas(scope.row, 0)"
                    ></TableAmountItem>
                </template>
            </el-table-column>
            <el-table-column
                :label="!searchInfo.classification ? '本期金额' : accountStandard === 1 ? '上年累计金额' : '上年同期累计金额'"
                min-width="280"
                align="right"
                header-align="right"
                :resizable="false"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.monthTotal"
                        :formula="calcFormula(scope.row, 1)"
                        :line-number="scope.row.lineNumber"
                        :queryFormulas="isQueryFormulas(scope.row)"
                        @go-to-query-formulas="goToQueryFormulas(scope.row, 1)"
                    ></TableAmountItem>
                </template>
            </el-table-column>
        </template>
        <template v-else>
            <el-table-column
                :label="!searchInfo.classification ? '本期金额' : accountStandard === 1 ? '上年累计金额' : '上年同期累计金额'"
                min-width="280"
                align="right"
                header-align="right"
                prop="monthTotal"
                :width="getColumnWidth(setModule, 'monthTotal')"
            >
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.monthTotal"
                        :formula="calcFormula(scope.row, 1)"
                        :line-number="scope.row.lineNumber"
                        :queryFormulas="isQueryFormulas(scope.row)"
                        @go-to-query-formulas="goToQueryFormulas(scope.row, 1)"
                    ></TableAmountItem>
                </template>
            </el-table-column>
            <el-table-column 
                label="本年累计金额" 
                min-width="280" 
                align="right" 
                header-align="right"
                :resizable="false"
            >
                <template #header>
                  <div class="table-header">
                    <el-icon><Switch @click="swapColumns"/></el-icon>
                    <span>本年累计金额</span>
                  </div>
                </template>
                <template #default="scope">
                    <TableAmountItem
                        :amount="scope.row.yearTotal"
                        :formula="calcFormula(scope.row, 0)"
                        :line-number="scope.row.lineNumber"
                        :queryFormulas="isQueryFormulas(scope.row)"
                        @go-to-query-formulas="goToQueryFormulas(scope.row, 0)"
                    ></TableAmountItem>
                </template>
            </el-table-column>
        </template>
    </el-table>
</template>

<script setup lang="ts">
import { computed, ref, onBeforeMount  } from "vue";
import { PeriodStatus } from "@/api/period";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import type { IIncomeSheet, IIncomeEditData, ISearchInfo } from "../types";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { globalWindowOpenPage, getUrlSearchParams } from "@/util/url";	
import type { ITableLineIDItem } from "@/views/Statements/types";
import { checkPermission } from "@/util/permission";
import { handleCellClick, getSwitchState } from "@/views/Statements/utils";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "IncomeSheet";
const props = defineProps<{
    tableData: Array<IIncomeSheet>;
    periodRef: any;
    searchInfo: ISearchInfo;
    editData: IIncomeEditData;
    accountStandard: Number;
    emptyText: string;
    lineIDList: any;
}>();
const emit = defineEmits(["changeSlot"]);

const isErp = ref(window.isErp);
const isShowIncome = ref(getSwitchState('isShowIncome'));
const colunmOrderChange=ref('0')
const tableRef=ref()
const swapColumns = () => {
    isShowIncome.value=!isShowIncome.value
    localStorage.setItem('isShowIncome',JSON.stringify(isShowIncome.value))
    colunmOrderChange.value=(isShowIncome.value?'0':'1')
    emit('colunmOrderChange', colunmOrderChange.value);
    tableRef.value.doLayout();
};
onBeforeMount(()=>{
    colunmOrderChange.value=(isShowIncome.value?'0':'1')
    emit('colunmOrderChange', colunmOrderChange.value);
})
//判断level1 2
const hasNumberTitle = (value: string,chinaNumber:string[]) => {

    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.indexOf(chinaNumber[i]) > -1) return true;
    }
    return false;
};
//判定assertnameclass
function assertNameClass(row: IIncomeSheet) {
    const chinaNumber = [
        "一、",
        "二、",
        "三、",
        "四、",
        "五、",
        "六、",
        "七、",
        "八、",
        "九、",
        "十、",
        "(一)",
        "(二)",
        "(四)",
        "(五)",
        "(六)",
        "(七)",
        "(八)",
        "(九)",
        "(十)",
        "（一）",
        "（二）",
        "（三）",
        "（四）",
    ];
    let className: string;
    if (row.expand == 1) {
        className = "level2";
    } else if (row.lineNumber == 0 || hasNumberTitle(row.proName,chinaNumber)) {
        className = "level1";
    } else {
        className = "level2";
    }
    return className;
}
function setTitleRowStyle(data:any) {
    const chinaNumber = [
        "一、",
        "二、",
        "三、",
        "四、",
        "五、",
        "六、",
        "七、",
        "八、",
        "九、",
        "十、"
    ];
    if(hasNumberTitle(data.row.proName,chinaNumber)) {
        return 'highlight-title-row';
    }
}
//
function isSixComeGo(statementid: number, lineid: number) {
    if (statementid == 1002) {
        if (lineid == 10020290) return true;
        if (lineid == 10020310) return true;
    }

    return false;
}
//是否显示编辑公式
const periodIsCheckOut = computed(() => {
    return props.periodRef?.periodStatus === PeriodStatus.CheckOut;
});
function assertShowEquationEdit(row: IIncomeSheet) {
    if (row.expand == 1 || row.fold || (row.lineType == 1 && !isSixComeGo(row.statementId, row.lineID))) {
        return !periodIsCheckOut.value;
    } else {
        return false;
    }
}

//行次
function rowNumberFormatter(_row: any, _column: any, cellValue: any) {
    if (cellValue === 0) {
        return "";
    } else {
        return cellValue;
    }
}

//跳转编辑
function openEquationDialog(lineType: number, lineId: number, statementId: number, title: string) {
    emit("changeSlot", { lineType: lineType, lineId: lineId, statementId: statementId, title: title });
}

//
function calcFormula(row: IIncomeSheet, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.note?.split("|")[0].replace(/期末/g, "年初") ?? "";
            break;
        case 1:
            formula = row.note?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}
const isQueryFormulas = (row: IIncomeSheet) => {
    if (props.lineIDList && checkPermission(['generalledger-canview'])) {
        let result = props.lineIDList[row.lineID];
        return (result?.length > 0) ? !!result[0].asubname : false;      
    } else {
        return false;
    }
}
const goToQueryFormulas = (row: IIncomeSheet, columnIndex: number) => {
    let time = props.searchInfo.pid;
    if (columnIndex === 0) { //本年累计
        time = props.searchInfo.pid;
    } else if (props.searchInfo.pid > 12 && props.searchInfo.classification && props.accountStandard===1) { //上年累计
        let y = Math.floor((props.searchInfo.pid-1) / 12);
        time = y > 1 ? 12*y : 12;
    } else if(props.searchInfo.pid > 12 && props.searchInfo.classification && props.accountStandard!==1) { //上年同期
        time = props.searchInfo.pid -12;
    }
    let result = props.lineIDList[row.lineID];
    let SubjectIDList = '&SubjectIDList=' + result.map((item:ITableLineIDItem) => item.asubid).join(','); 
    let params = {
        period_s: time,
        period_e: time,
    }
    if(result.length > 0) {
        globalWindowOpenPage("/AccountBooks/GeneralLedger?" + getUrlSearchParams(params) + SubjectIDList, '总账');
    }
}
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
</style>

