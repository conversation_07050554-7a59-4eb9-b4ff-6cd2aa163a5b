<template>
  <div class="declaration-form">
    <div class="form-title">{{ tabData.describe }}</div>
    <div class="form-info">
      <div class="info">
        <div
          v-for="(item, index) in formHeaderInfo"
          :key="index">
          <span class="label">{{ item.label }}：</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="native-table">
    <table>
      <tbody>
        <tr>
          <th colspan="3">纳税人识别号（统一社会信用代码）</th>
          <th colspan="2">
            <div style="width: 100%"></div>
          </th>
          <th colspan="3">纳税人名称</th>
          <th colspan="2">
            <span>--</span>
          </th>
        </tr>
        <tr>
          <th colspan="3">预缴方式</th>
          <td colspan="7">
            <div class="radio-group">
              <el-radio-group>
                <el-radio label="1">按照实际利润额预缴</el-radio>
                <el-radio label="2">按照上一纳税年度应纳税所得额平均额预缴</el-radio>
                <el-radio label="3">按照税务机关确定的其他方法预缴</el-radio>
              </el-radio-group>
            </div>
          </td>
        </tr>
        <tr>
          <th colspan="3">企业类型</th>
          <td colspan="7">
            <div class="radio-group">
              <el-radio-group>
                <el-radio label="1">一般企业</el-radio>
                <el-radio label="2">跨地区经营汇总纳税企业总机构</el-radio>
                <el-radio label="3">跨地区经营汇总纳税企业分支机构</el-radio>
              </el-radio-group>
            </div>
          </td>
        </tr>
        <tr>
          <th colspan="3">跨省总机构行政区划</th>
          <td colspan="2"></td>
          <td colspan="5">
            <div class="hint">提示: 总机构在外省的分支机构申报时，请先选择跨省总机构行政区划</div>
          </td>
        </tr>

        <tr>
          <th colspan="10">优惠及附报事项有关信息</th>
        </tr>
        <tr>
          <th rowspan="2">项目</th>
          <th colspan="2">一季度</th>
          <th colspan="2">二季度</th>
          <th colspan="2">三季度</th>
          <th colspan="2">四季度</th>
          <th rowspan="2">季度平均值</th>
        </tr>
        <tr>
          <th>季初</th>
          <th>季末</th>
          <th>季初</th>
          <th>季末</th>
          <th>季初</th>
          <th>季末</th>
          <th>季初</th>
          <th>季末</th>
        </tr>

        <tr>
          <td>从业人数</td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td></td>
        </tr>
        <tr>
          <td>资产总额（万元）</td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td>
            <checkable-input />
          </td>
          <td></td>
        </tr>

        <tr>
          <th>国家限制或禁止行业</th>
          <td colspan="4">
            <div class="radio-group">
              <el-radio-group
                v-model="isRestrictedIndustry"
                @change="handleRestrictedIndustryChange">
                <el-radio label="true">是</el-radio>
                <el-radio label="false">否</el-radio>
              </el-radio-group>
            </div>
          </td>
          <th>小型微利企业</th>
          <td colspan="4">
            <div class="radio-group">
              <el-radio-group v-model="isMicroEnterprise">
                <el-radio
                  label="true"
                  disabled>
                  是
                </el-radio>
                <el-radio
                  label="false"
                  disabled>
                  否
                </el-radio>
              </el-radio-group>
            </div>
          </td>
        </tr>
        <tr>
          <th>代码</th>
          <th colspan="6">附报事项说明</th>
          <th colspan="3">金额或选项</th>
        </tr>
        <tr>
          <td>K01002</td>
          <td colspan="6">
            <el-checkbox>扶贫捐赠支出全额扣除（本年累计，元）</el-checkbox>
          </td>
          <td colspan="3"><checkable-input></checkable-input></td>
        </tr>
        <tr>
          <td>Y01001</td>
          <td colspan="6">
            <el-checkbox>软件集成电路企业优惠政策适用类型</el-checkbox>
          </td>
          <td colspan="3">
            <el-checkbox label="原政策"></el-checkbox>
            <el-checkbox label="新政策"></el-checkbox>
          </td>
        </tr>

        <tr>
          <th colspan="10">预缴税款计算表</th>
        </tr>
        <tr>
          <th>行次</th>
          <th colspan="7">项目</th>
          <th colspan="2">本年累计金额</th>
        </tr>
        <tr>
          <td>1</td>
          <td colspan="7">营业收入</td>
          <td colspan="2">
            <checkable-input />
          </td>
        </tr>
        <tr>
          <td>2</td>
          <td colspan="7">营业成本</td>
          <td colspan="2">
            <checkable-input />
          </td>
        </tr>
        <tr>
          <td>3</td>
          <td colspan="7">利润总额</td>
          <td colspan="2">
            <checkable-input />
          </td>
        </tr>
        <tr>
          <td>4</td>
          <td colspan="7">加：特定企业特定业务(以前年度亏损)</td>
          <td colspan="2">
            <checkable-input />
          </td>
        </tr>
        <tr>
          <td>5</td>
          <td colspan="7">减：不征税收入</td>
          <td colspan="2">
            <checkable-input />
          </td>
        </tr>
        <tr>
          <td>6</td>
          <td colspan="7">
            减：资产加速折旧、摊销（从第2020D）
            <a
              href="#"
              @click.prevent="jumpToTable2"
              class="link">
              填写
            </a>
          </td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>7</td>
          <td colspan="7">
            减：免税收入、减计收入、加计扣除（7.1+7.2+...）
            <a
              href="#"
              class="link"
              @click.prevent="showTaxPreferenceDialog('deduction')">
              填写优惠
            </a>
            <el-icon
              v-if="taxPreferenceDetailsSelected.length > 0"
              class="expand-icon"
              @click="showTaxPreferenceDetails = !showTaxPreferenceDetails">
              <arrow-down v-if="showTaxPreferenceDetails" />
              <arrow-right v-else />
            </el-icon>
          </td>
          <td colspan="2">{{ taxPreferenceAmount }}</td>
        </tr>

        <!-- 优惠项明细，可展开收起 -->
        <template v-if="showTaxPreferenceDetails && taxPreferenceDetailsSelected.length > 0">
          <tr
            v-for="(item, index) in taxPreferenceDetailsSelected"
            :key="index">
            <td align="right">7.{{ index + 1 }}</td>
            <td colspan="7">{{ item.name }}</td>
            <td colspan="2">
              {{ item.amount }}
            </td>
          </tr>
        </template>

        <tr>
          <td>8</td>
          <td colspan="7">
            减：所得减免（8.1+8.2+...）
            <a
              href="#"
              class="link"
              @click.prevent="incomeReductionDialogVisible = true">
              填写优惠
            </a>
            <el-icon
              v-if="incomeReductionDetails.length > 0"
              class="expand-icon"
              @click="showIncomeReductionDetails = !showIncomeReductionDetails">
              <arrow-down v-if="showIncomeReductionDetails" />
              <arrow-right v-else />
            </el-icon>
          </td>
          <td colspan="2">{{ incomeReductionAmount }}</td>
        </tr>

        <!-- 所得减免明细，可展开收起 -->
        <template v-if="showIncomeReductionDetails && incomeReductionDetails.length > 0">
          <tr
            v-for="(item, index) in incomeReductionDetails"
            :key="index">
            <td align="right">8.{{ index + 1 }}</td>
            <td colspan="7">{{ item.name }}</td>
            <td colspan="2">
              {{ item.amount }}
            </td>
          </tr>
        </template>
        <tr>
          <td>9</td>
          <td colspan="7">减：弥补以前年度亏损</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>10</td>
          <td colspan="7">实际利润额（3+4-5-6-7-8-9）：按照上一纳税年度应纳税所得额平均额预缴的填写预缴税款</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>11</td>
          <td colspan="7">税率(25%)</td>
          <td colspan="2">25%</td>
        </tr>
        <tr>
          <td>12</td>
          <td colspan="7">应纳所得税额（10×11）</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>13</td>
          <td colspan="7">
            减：减免所得税额（13.1+13.2+...）
            <a
              href="#"
              class="link"
              @click.prevent="showTaxExemptionDialog">
              填写优惠
            </a>
            <el-icon
              v-if="taxExemptionDetails.length > 0"
              class="expand-icon"
              @click="showTaxExemptionDetails = !showTaxExemptionDetails">
              <arrow-down v-if="showTaxExemptionDetails" />
              <arrow-right v-else />
            </el-icon>
          </td>
          <td colspan="2">{{ taxExemptionAmount }}</td>
        </tr>

        <!-- 减免所得税明细，可展开收起 -->
        <template v-if="showTaxExemptionDetails && taxExemptionDetails.length > 0">
          <tr
            v-for="(item, index) in taxExemptionDetails"
            :key="index">
            <td align="right">13.{{ index + 1 }}</td>
            <td colspan="7">{{ item.name }}</td>
            <td colspan="2">
              {{ item.amount }}
            </td>
          </tr>
        </template>

        <tr>
          <td>14</td>
          <td colspan="7">减：本年实际已预缴所得税额</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>15</td>
          <td colspan="7">减：特定业务预缴（退）所得税额</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>16</td>
          <td colspan="7">本期应补（退）所得税额（12-13-14-15）：税务机关确定的本期应纳所得税额</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>

        <!-- 工业税收企业分支机构税款计算 -->
        <tr>
          <th colspan="10">工业企业企业分支机构税款计算</th>
        </tr>
        <tr>
          <td>17</td>
          <td rowspan="4">总机构</td>
          <td colspan="6">分支机构应纳补（退）所得税额（18+19+20）</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>18</td>
          <td colspan="6">
            其中：总机构分摊纳补（退）所得税额（16×总机构分摊比例
            <input-field value="0.000000000" />
            %）
          </td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>19</td>
          <td colspan="6">
            财政集中分配部分（退）所得税额（16×财政集中分配比例
            <input-field value="0.000000000" />
            %）
          </td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>20</td>
          <td colspan="6">
            总机构所在市县市场和税收调节分摊所得税额（16×全部分支机构分摊比例
            <input-field value="0.000000000" />
            %×总机构所在市县分享财政调控部分分摊比例
            <input-field value="0.000000000" />
            %）
          </td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>21</td>
          <td rowspan="2">分支机构</td>
          <td colspan="6">分支机构本期分摊比例</td>
          <td colspan="2"><input-field value="0.000000000" /></td>
        </tr>
        <tr>
          <td>22</td>
          <td colspan="6">分支机构本期分摊纳税（退）所得税额</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>

        <!-- 实际缴纳企业所得税计算 -->
        <tr>
          <th colspan="10">实际缴纳企业所得税计算</th>
        </tr>
        <tr>
          <td>FZ1</td>
          <td colspan="7">中央级收入部分摊入税额：16行×60%或[16行+（20行)×80%+19行22行×60%]</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>FZ2</td>
          <td colspan="7">地方级收入部分摊入税额：16行×40%或[16行+（20行)×40%或22行×40%]</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>23</td>
          <td rowspan="3">减：民族自治地区企业所得税地方分享部分：</td>
          <td
            rowspan="3"
            colspan="2">
            <div class="radio-options">
              <el-radio-group>
                <el-radio label="1">无</el-radio>
                <el-radio label="2">减征</el-radio>
                <el-radio label="3">减征</el-radio>
              </el-radio-group>
            </div>
          </td>
          <td rowspan="3">减征幅度</td>
          <td rowspan="3">0.000000%</td>
          <td colspan="2">本期应缴减免税额（FZ2×减征幅度）</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>23.1</td>
          <td colspan="2">本期地市实际截留（23行的本年累计）</td>
          <td colspan="2">
            <input-field value="0.00" />
          </td>
        </tr>
        <tr>
          <td>23.2</td>
          <td colspan="2">本年累计应减免金额（总机构及分支机构的本年累计，总机构填报）</td>
          <td colspan="2">
            <input-field value="0.00" />
          </td>
        </tr>
        <tr>
          <td>FZ3</td>
          <td colspan="7">地方级收入部分应纳税额（下期：FZ2-23）</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>
        <tr>
          <td>24</td>
          <td colspan="7">实际应补（退）所得税额（合计：FZ1+FZ3）</td>
          <td colspan="2"><input-field value="0.00" /></td>
        </tr>

        <!-- 表格底部说明和签名区域 -->
        <tr>
          <td
            colspan="10"
            class="remark">
            <div>谨声明：本纳税申报表是根据国家税收法律法规及相关规定填报的，是真实的、可靠的、完整的。</div>
            <div class="rightBotton">
              <span class="Seal">纳税人(签章):</span>
              <span></span>
              <span class="SealDate">2025 年 05 月 14 日</span>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="2">经办人:</td>
          <td colspan="3"></td>
          <td colspan="2">受理人：</td>
          <td colspan="3"></td>
        </tr>
        <tr>
          <td colspan="2">经办人身份证号码：</td>
          <td colspan="3">341227********103X</td>
          <td colspan="2">受理税务机关（章）：</td>
          <td colspan="3"></td>
        </tr>
        <tr>
          <td colspan="2">代理机构签章：</td>
          <td colspan="3"></td>
          <td colspan="2">受理日期：</td>
          <td colspan="3"></td>
        </tr>
        <tr>
          <td colspan="2">代理机构统一社会信用代码：</td>
          <td colspan="3"></td>
          <td colspan="5">填写时请忽略经办人、经办人身份证号等信息，申报成功后会根据登录税局账号自动生成对应信息</td>
        </tr>
      </tbody>
    </table>
  </div>
  <!-- 免税收入、减计收入、加计扣除弹窗 -->
  <input-select-table
    v-model="taxPreferenceDialogVisible"
    title="免税、减计收入、加计扣除类型选项表"
    :data="preferenceTableData"
    :selected-items="taxPreferenceDetailsSelected"
    :show-type-column="true"
    :show-span-method="true"
    @confirm="handleTaxPreferenceConfirm" />

  <!-- 所得减免弹窗 -->
  <input-select-table
    v-model="incomeReductionDialogVisible"
    title="所得减免类型选项表"
    :data="incomeReductionList"
    :selected-items="incomeReductionDetails"
    :show-category-column="true"
    @confirm="handleIncomeReductionConfirm" />

  <!-- 减免所得税额弹窗 -->
  <input-select-table
    v-model="taxExemptionDialogVisible"
    title="减免所得税类型选项表"
    :data="taxExemptionList"
    :selected-items="taxExemptionDetails"
    :show-category-column="true"
    @confirm="handleTaxExemptionConfirm" />
</template>

<script setup lang="ts">
  import { ElConfirm } from "@/utils/confirm"
  import { TableItem } from "@/views/TaxDeclaration/components/InputSelectTable.vue"
  import InputSelectTable from "@/views/TaxDeclaration/components/InputSelectTable.vue"
  const props = defineProps<{
    tabData: any
  }>()

  const emit = defineEmits<{
    (e: "jumpToTable2"): void
  }>()

  const jumpToTable2 = () => {
    console.log("跳转到表格2")
    emit("jumpToTable2")
  }

  const formHeaderInfo = ref([
    { label: "税款所属期", value: "2024-10-01至2024-12-31" },
    { label: "填表日期", value: "2025-01-10" },
    { label: "金额单位", value: "元，至角分" },
  ])

  // 国家限制或禁止行业
  const isRestrictedIndustry = ref("false")

  // 国家限制或禁止行业
  const handleRestrictedIndustryChange = (value: string) => {
    if (value === "true") {
      ElConfirm({
        title: "提示",
        showCancel: true,
        message: "您填报了从事国家限制或禁止行业，按照规定不能享受小型微利企业所得税优惠政策，请您再次确认填报是否准确。",
        onCancel: () => {
          isRestrictedIndustry.value = "false"
        },
      })
    }
  }

  //TODO 状态通过四个条件判断 小型微利企业
  const isMicroEnterprise = ref("true")

  // 优惠弹窗相关
  const taxPreferenceDialogVisible = ref(false)
  const currentPreferenceType = ref("")
  const taxPreferenceAmount = ref("0.00") // 优惠项汇总金额
  const taxPreferenceDetailsSelected = ref<TableItem[]>([]) // 优惠项明细
  const showTaxPreferenceDetails = ref(true) // 控制明细展开收起

  // 免税收入列表
  const exemptIncomeList = ref([
    { index: 1, code: "MSSR010", name: "国债利息收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    { index: 2, code: "MSSR021", name: "股股息红利等权益性投资收益免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    {
      index: 3,
      code: "MSSR022",
      name: "通过沪港通投资且连续持有H股满12个月取得的股息红利所得免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    {
      index: 4,
      code: "MSSR023",
      name: "通过深港通投资且连续持有H股满12个月取得的股息红利所得免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    {
      index: 5,
      code: "MSSR024",
      name: "持有创新企业CDR取得的股息红利所得免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    { index: 6, code: "MSSR025", name: "免税收入 永续债利息收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    { index: 7, code: "MSSR030", name: "符合条件的非营利组织的收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    {
      index: 8,
      code: "MSSR040",
      name: "投资者从证券投资基金分配中取得的收入免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    { index: 9, code: "MSSR050", name: "取得的地方政府债券利息收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    { index: 10, code: "MSSR100", name: "取得的基础研究收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    {
      index: 11,
      code: "MSSR999",
      name: "其他: 免税收入类系列明优惠 减免税代码:",
      amount: "0.00",
      selected: false,
      hasExtraInput: true,
      type: "免税收入",
    },
  ])

  // 减计收入列表
  const reducedIncomeList = ref([
    { index: 12, code: "JJSR010", name: "取得铁路债券利息收入减半征收企业所得税", amount: "0.00", selected: false, type: "减计收入" },
    {
      index: 13,
      code: "JJSR020",
      name: "取得的社区家庭服务收入在计算应纳所得税额时减计收入",
      amount: "0.00",
      selected: false,
      type: "减计收入",
    },
    {
      index: 14,
      code: "JJSR030",
      name: "综合利用资源生产产品取得的收入在计算应纳所得税额时减计收入",
      amount: "0.00",
      selected: false,
      type: "减计收入",
    },
    {
      index: 15,
      code: "JJSR999",
      name: "其他: 免税收入类系列明优惠 减免税代码:",
      amount: "0.00",
      selected: false,
      hasExtraInput: true,
      type: "减计收入",
    },
  ])

  // 加计扣除列表
  const additionalDeductionList = ref([
    {
      index: 16,
      code: "JJKC014",
      name: "企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除(集成电路和工业母机企业按120%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 17,
      code: "JJKC015",
      name: "企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除(按100%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 18,
      code: "JJKC024",
      name: "企业为获得创新性、创造性和突破性的产品进行创意设计活动发生的相关费用加计扣除（集成电路和工业母机企业按120%加计扣除）",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 19,
      code: "JJKC025",
      name: "企业为获得创新性、创造性和突破性的产品进行创意设计活动发生的相关费用加计扣除(按100%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 20,
      code: "JJKC031",
      name: "企业投入基础研究支出加计扣除(按100%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
  ])

  // 合并所有优惠项目到一个表格数据中
  const preferenceTableData = computed(() => {
    return [...exemptIncomeList.value, ...reducedIncomeList.value, ...additionalDeductionList.value]
  })

  // 显示优惠弹窗
  const showTaxPreferenceDialog = (type: string) => {
    currentPreferenceType.value = type
    taxPreferenceDialogVisible.value = true
  }

  // 处理免税收入、减计收入、加计扣除确认
  const handleTaxPreferenceConfirm = (selectedItems: TableItem[], totalAmount: number) => {
    // 更新表格中对应行的金额
    taxPreferenceAmount.value = totalAmount.toFixed(2)

    // 保存选中的优惠项目明细
    taxPreferenceDetailsSelected.value = selectedItems
  }

  // 所得减免弹窗相关
  const incomeReductionDialogVisible = ref(false)
  const incomeReductionAmount = ref("0.00") // 所得减免汇总金额
  const incomeReductionDetails = ref<TableItem[]>([]) // 所得减免明细
  const showIncomeReductionDetails = ref(true) // 控制明细展开收起

  // 所得减免列表
  const incomeReductionList = ref([
    {
      index: 1,
      code: "SD011",
      category: "农、林、牧、渔业项目",
      name: "从事农、林、牧、渔业项目的所得免征企业所得税（免税项目）",
      amount: "0.00",
      selected: false,
      type: "所得减免",
    },
    {
      index: 2,
      code: "SD012",
      category: "农、林、牧、渔业项目",
      name: "从事农、林、牧、渔业项目的所得免征企业所得税（减半项目）",
      amount: "0.00",
      selected: false,
      type: "所得减免",
    },
    {
      index: 3,
      code: "SD021",
      category: "国家重点扶持的公共基础设施项目",
      name: "从事国家重点扶持的公共基础设施项目（除农村饮水安全工程）投资经营的所得定期减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "所得减免",
    },
    {
      index: 4,
      code: "SD022",
      category: "国家重点扶持的公共基础设施项目",
      name: "从事农村饮水安全工程项目投资经营的所得定期减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "所得减免",
    },
    // ... 其他项目
  ])

  // 处理所得减免确认
  const handleIncomeReductionConfirm = (selectedItems: TableItem[], totalAmount: number) => {
    // 更新表格中对应行的金额
    incomeReductionAmount.value = totalAmount.toFixed(2)

    // 保存选中的所得减免项目明细
    incomeReductionDetails.value = selectedItems
  }

  // 减免所得税额弹窗相关
  const taxExemptionDialogVisible = ref(false)
  const taxExemptionAmount = ref("0.00") // 减免所得税额汇总金额
  const taxExemptionDetails = ref<TableItem[]>([]) // 减免所得税明细
  const showTaxExemptionDetails = ref(true) // 控制明细展开收起

  // 减免所得税列表
  const taxExemptionList = ref([
    {
      index: 1,
      code: "JMSE00100",
      category: "小微企业",
      name: "符合条件的小型微利企业减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "减免所得税",
    },
    {
      index: 2,
      code: "JMSE00201",
      category: "高新技术企业",
      name: "高新技术企业减按15%的税率征收企业所得税",
      amount: "0.00",
      selected: false,
      type: "减免所得税",
    },
    {
      index: 3,
      code: "JMSE00202",
      category: "高新技术企业",
      name: "经济特区和上海浦东新区新设立的高新技术企业在区内取得的所得定期减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "减免所得税",
    },
    {
      index: 5,
      code: "JMSE00302A",
      category: "原有软件集成电路优惠继续执行至到期",
      name: "线宽小于0.25微米的集成电路生产企业减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "减免所得税",
    },
    {
      index: 6,
      code: "JMSE00303A",
      category: "原有软件集成电路优惠继续执行至到期",
      name: "投资额超过80亿元的集成电路生产企业减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "减免所得税",
    },
    {
      index: 7,
      code: "JMSE00309A",
      category: "原有软件集成电路优惠继续执行至到期",
      name: "线宽小于65纳米（含）或投资额超过150亿元的集成电路生产企业减免企业所得税",
      amount: "0.00",
      selected: false,
      type: "减免所得税",
    },
  ])

  // 显示减免所得税弹窗
  const showTaxExemptionDialog = () => {
    taxExemptionDialogVisible.value = true
  }

  // 处理减免所得税确认
  const handleTaxExemptionConfirm = (selectedItems: TableItem[], totalAmount: number) => {
    // 更新表格中对应行的金额
    taxExemptionAmount.value = totalAmount.toFixed(2)

    // 保存选中的减免所得税项目明细
    taxExemptionDetails.value = selectedItems
  }
</script>

<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
  @include colspan-width(10);

  .native-table table .remark {
    padding: 20px;
    .rightBotton {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
</style>
