<template>
    <div class="content">
        <div class="title">资金报表</div>
        <div class="main-content">
            <el-tabs v-model="tabName" :class="isErp ? 'erp-tabs' : ''">
                <el-tab-pane label="按账户汇总" name="account">
                    <Account :dateInfo="dateInfo" :options="selectedList" />
                </el-tab-pane>
                <el-tab-pane label="按收支类别汇总" name="ietype">
                    <IEType :options="selectedList" ref="ietypeRef" :dateInfo="dateInfo" :fcList="fcList" @change-switch-status="changeSwitchStatus" />
                </el-tab-pane>
                <el-tab-pane label="按往来单位汇总" name="unit">
                    <Unit :options="selectedList" ref="unitRef" :dateInfo="dateInfo" :fcList="fcList" @change-switch-status="changeSwitchStatus" />
                </el-tab-pane>
                <el-tab-pane label="按项目汇总" name="project">
                    <DepartmentOrProject type="PROJECT" :options="selectedList" ref="projectRef" :dateInfo="dateInfo" :fcList="fcList" @change-switch-status="changeSwitchStatus" />
                </el-tab-pane>
                <el-tab-pane label="按部门汇总" name="department">
                    <DepartmentOrProject type="DEPARTMENT" :options="selectedList" ref="departmentRef" :dateInfo="dateInfo" :fcList="fcList" @change-switch-status="changeSwitchStatus" />
                </el-tab-pane>
                <el-tab-pane label="按组合汇总" name="combine">
                    <Combine :options="selectedList" ref="combineRef" :dateInfo="dateInfo" :fcList="fcList" @change-switch-status="changeSwitchStatus" />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "Report",
};
</script>
<script setup lang="ts">
import { ref, watch, reactive, onMounted, onUnmounted } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { judgeDate } from "./utils";
import type { IJournalDate } from "@/views/Cashier/type";
import Account from "./components/Account.vue";
import IEType from "./components/IEType.vue";
import Unit from "./components/Unit.vue";
import Combine from "./components/Combine.vue";
import DepartmentOrProject from "./components/DepartmentOrProject.vue";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import { useCDAccountStore } from "@/store/modules/cdAccountList";
import { useCurrencyStore } from "@/store/modules/currencyList";
import type { ISCurrcy } from "./types";

const cdAccountStore = useCDAccountStore();
const currencyStore = useCurrencyStore();
const ietypeRef = ref<InstanceType<typeof IEType>>();
const unitRef = ref<InstanceType<typeof Unit>>();
const projectRef = ref<InstanceType<typeof DepartmentOrProject>>();
const departmentRef = ref<InstanceType<typeof DepartmentOrProject>>();
const combineRef = ref<InstanceType<typeof Combine>>();

const isErp = ref(window.isErp);
const tabName = ref("account");
const selectedList = ref<Array<IBankAccountItem>>([]);
const dateInfo = reactive({
    start: "",
    end: "",
});

const fcList = ref<ISCurrcy[]>([]);
const getSelectList = async () => {
    await cdAccountStore.getCDAccountAllList();
    selectedList.value = [...cdAccountStore.cdAccountList];
    await currencyStore.getCurrencyList();
    reloadCurrency();
    if (selectedList.value.length == 0) {
        getDateInfo();
    } else {
        getJournalData();
    }
};
const getDateInfo = () => {
    request({ url: "/api/Period/GetCurrentPeriod", method: "post" }).then((res: any) => {
        if (res.state != 1000) return;
        const { start, end } = judgeDate(res.data.year, res.data.sn);
        const day = Number(end.split("-")[2]) <= new Date().getDate() ? end.split("-")[2] : new Date().getDate() + "";
        const endDate = end.split("-")[0] + "-" + end.split("-")[1] + "-" + day.padStart(2, "0");
        dateInfo.start = start;
        dateInfo.end = endDate;
    });
};
const getJournalData = () => {
    request({ url: "/api/CashierReport/GetSearchDate", method: "post" }).then((res: IResponseModel<IJournalDate>) => {
        if (res.state != 1000) return;
        dateInfo.start = res.data.searchStart;
        dateInfo.end = res.data.searchEnd;
    });
};

const handleInit = () => {
    getSelectList();
};

let ietypeCanLoading = true;
let unitCanLoading = true;
let projectCanLoading = true;
let departmentCanLoading = true;
let combineCanLoading = true;
let showDisabledAccount = ref(getShowDisabledAccount("cashReport"));
function changeSwitchStatus(val: boolean) {
    restLoadingValue();
    showDisabledAccount.value= val;
}
function restLoadingValue() {
    if (showDisabledAccount.value !== getShowDisabledAccount("cashReport")) {
        ietypeCanLoading = true;
        unitCanLoading = true;
        projectCanLoading = true;
        departmentCanLoading = true;
        combineCanLoading = true;
    }
}
watch(tabName, (val: string) => {
    switch (val) {
        case "ietype":
            if (ietypeCanLoading) {
                ietypeRef.value?.handleInit();
                ietypeCanLoading = false;
            }
            break;
        case "unit":
            if (unitCanLoading) {
                unitRef.value?.handleInit();
                unitCanLoading = false;
            }
            break;
        case "project":
            if (projectCanLoading) {
                projectRef.value?.handleInit();
                projectCanLoading = false;
            }
            break;
        case "department":
            if (departmentCanLoading) {
                departmentRef.value?.handleInit();
                departmentCanLoading = false;
            }
            break;
        case "combine":
            if (combineCanLoading) {
                combineRef.value?.handleInit();
                combineCanLoading = false;
            }
            break;
    }
});

async function reloadCDAccount() {
    await cdAccountStore.getCDAccountAllList();
    selectedList.value = [...cdAccountStore.cdAccountList];
}
function reloadCurrency() {
    fcList.value = [...currencyStore.fcListOptions];
}

onMounted(() => {
    handleInit();
    window.addEventListener("reloadCDAccount", reloadCDAccount);
    window.addEventListener("reloadCurrency", reloadCurrency);
});

onUnmounted(() => {
    window.removeEventListener("reloadCDAccount", reloadCDAccount);
    window.removeEventListener("reloadCurrency", reloadCurrency);
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.main-content {
    :deep(.el-tabs) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .el-tabs__content {
            flex: 1;
            min-height: 0;
        }
        .el-tab-pane {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
    }
}
</style>
