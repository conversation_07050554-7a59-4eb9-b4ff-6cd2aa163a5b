import { ref, computed } from "vue";
import { useLoadingStoreHook } from "@/store/modules/loading";
import { percentageLoading } from "@/util/percentageLoading";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { useCorsagentIframeStoreHook } from "@/store/modules/corsagent";
const corsagentIframeStore = useCorsagentIframeStoreHook();
export const useLoading = () => {
    // 用于有多个请求的场景
    const isLoading = computed(() => {
        return useLoadingStoreHook().loading <= 0 ? false : true;
    });
    const enterLoading = (title: string) => {
        percentageLoading();
        useLoadingStoreHook().title = title;
        useLoadingStoreHook().loading += 1;
        if (isLemonClient()) {
            getLemonClient().showMenuMask("show");
        }
    };

    // 退出loading，loadingIndex的值减1
    const quitLoading = () => {
        if(useLoadingStoreHook().loading >= 0) useLoadingStoreHook().loading -= 1;
        if (isLemonClient()) {
            getLemonClient().showMenuMask("close");
        }
    };

    // loadingWrapper方法，自动完成进入/退出loading的设置
    const loadingWrapper = <T>(promise: Promise<T>) => {
        return new Promise<void>((resolve, reject) => {
            // 进入loading
            enterLoading("");
            promise.finally(() => {
                // 退出loading
                quitLoading();
                resolve();
            });
        });
    };
    return {
        isLoading,
        enterLoading,
        quitLoading,
        loadingWrapper,
    };
};
