import { percentageLoading } from "@/components/PercentageLoading/utils/percentageLoading"

// 创建一个全局单例模式的loading状态管理
const loading = ref(0)
const title = ref("")

export const useLoading = () => {
  const isLoading = computed(() => {
    return loading.value > 0
  })

  const enterLoading = (titleText: string) => {
    percentageLoading()
    title.value = titleText
    loading.value = 1
  }

  const enterSyncLoading = (loadingArg?: any) => {
    // 定义默认值
    const defaultLoadingArg = {
      showConfirmBtn: true,
      width: 550,
      dialogTitle: "提示",
      alignCenter: true,
      strokeWidth: 22,
      bottomTipMsg: "正在申报中，可能需要几分钟，您可以先关闭此弹窗，申报完成后会通过任务中心通知您~",
      percentageShowText: false,
    }

    // 合并参数，确保在没有传递参数时使用默认值
    const mergedArg = loadingArg ? { ...defaultLoadingArg, ...loadingArg } : defaultLoadingArg

    percentageLoading(mergedArg)
    loading.value = 1
  }

  const quitLoading = () => {
    loading.value = 0
  }

  // loadingWrapper方法，自动完成进入/退出loading的设置
  const loadingWrapper = <T>(promise: Promise<T>) => {
    return new Promise<void>((resolve) => {
      // 进入loading
      enterLoading("")
      promise.finally(() => {
        // 退出loading
        quitLoading()
        resolve()
      })
    })
  }

  return {
    loading,
    title,
    isLoading,
    enterLoading,
    quitLoading,
    loadingWrapper,
    enterSyncLoading,
  }
}
