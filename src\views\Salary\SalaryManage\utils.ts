import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoneyWithZero, formatMoney } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const myformatter = (date: Date) => {
    const y = date.getFullYear();
    const m = date.getMonth() + 1;
    const d = date.getDate();
    return y + "-" + (m < 10 ? "0" + m : m) + "-" + (d < 10 ? "0" + d : d);
};

export const formatSalaryMoneyWithZero = (value: number | string | undefined) => {
    if (value) {
        if (typeof value === "number" && !isNaN(value)) {
            return value.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        } else if (typeof value === "string") {
            const numberValue = Number(value);
                return numberValue.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  });
            // return numberValue.toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, "$&,");
        }
    }
    return "0.00";
};

export const setColumns = (columns: Array<IColumnProps> | undefined, showInfo: boolean, salaryMultiple: boolean) => {
    const setModule = "SalaryManage";
    columns = [
        { slot: "selection", fixed: "left" },
        { label: "编号", prop: "e_code", align: "left", headerAlign: "left", fixed: "left", width: getColumnWidth(setModule, 'e_code') },
        { label: "姓名", prop: "e_name", align: "left", headerAlign: "left", fixed: "left", width: getColumnWidth(setModule, 'e_name') },
        { label: "部门", prop: "aa_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'aa_name') },
        {
            label: "工资总额",
            prop: "salary_sum",
            align: "right",
            headerAlign: "right",
            width: getColumnWidth(setModule, 'salary_sum'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        {
            label: "应扣工资",
            prop: "deduct_wage",
            align: "right",
            headerAlign: "right",
            width: getColumnWidth(setModule, 'deduct_wage'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { slot: "gross_pay" },
        {
            label: "代扣个人款项",
            prop: "ss_p",
            align: "right",
            headerAlign: "right",
            minWidth: window.isErp ? 110: 90,
            width: getColumnWidth(setModule, 'ss_p'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        {
            label: "个税免征额",
            prop: "tax_base_amount",
            align: "right",
            headerAlign: "right",
            minWidth: 90,
            width: getColumnWidth(setModule, 'tax_base_amount'),
            formatter: (row, column, value) => {
                return value;
            },
        },
        { slot: "tax" },
        {
            label: "实发工资调整",
            prop: "after_tax",
            align: "right",
            headerAlign: "right",
            minWidth: window.isErp ? 110: 90,
            width: getColumnWidth(setModule, 'after_tax'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { slot: "net_salary"},
        {
            label: "公司承担款项",
            prop: "ss_c",
            align: "right",
            headerAlign: "right",
            minWidth: window.isErp ? 110: 100,
            width: getColumnWidth(setModule, 'ss_c'),
            formatter: (row, column, value) => {
                return formatMoneyWithZero(value);
            },
        },
        { slot: "total_cost"},
    ];
    if (salaryMultiple && !showInfo) {
        columns.splice(12, 0 , {slot: "net_salary_first"}, {slot: "net_salary_second"})
    }
    if (showInfo) {
        columns = [
            { slot: "selection", fixed: "left" },
            { label: "编号", prop: "e_code", align: "left", headerAlign: "left", fixed: "left", width: getColumnWidth(setModule, 'e_code') },
            { label: "姓名", prop: "e_name", align: "left", headerAlign: "left", fixed: "left", width: getColumnWidth(setModule, 'e_name') },
            { label: "部门", prop: "aa_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'aa_name') },
            { label: "手机号", prop: "mobile_phone", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'mobile_phone') },
            { label: "身份证号码", prop: "e_id", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'e_id') },
            {
                label: "个税免征额",
                prop: "tax_base_amount",
                align: "right",
                headerAlign: "right",
                minWidth: 90,
                width: getColumnWidth(setModule, 'tax_base_amount'),
                formatter: (row, column, value) => {
                    return value;
                },
            },
            { slot: "salary_sum" }, //工资总额
            { slot: "deduct_wage" }, //应扣工资
            { slot: "gross_pay" },
            //
            { slot: "insurance_p" }, //个人代扣
            { slot: "deduction_other" },
            { slot: "salary_year" },
            { slot: "tax_base_amount_year" },
            { slot: "ss_p_year" },
            { slot: "deduction" },
            { slot: "deduction_other_year" },
            { slot: "tax_year" },
            { slot: "tax_payed_year" },
            { slot: "tax" },
            {
                label: "个税调整",
                prop: "tax_adj_diff",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                width: getColumnWidth(setModule, 'tax_adj_diff'),
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
            },
            { slot: "after_tax" }, //实发工资调整
            { slot: "net_salary" },
            { slot: "insurance_c" }, //公司承担的五险一金
            { slot: "total_cost" },
        ];
        if (salaryMultiple) {
            columns.splice(23, 0 , {slot: "net_salary_first"}, {slot: "net_salary_second"})
        }
    }
    return columns;
};
