<template>
    <div class="main-center-entry">
        <div class="main-center-entry-left">
            <Table
                ref="tableRef"
                :data="voucherEntryData"
                :columns="columns"
                :page-is-show="true"
                :layout="paginationData.layout"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :currentPage="paginationData.currentPage"
                :empty-text="emptyText"
                :tableName="setModule"
                @selection-change="selectionChange"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                @custom-sort="handleCustomSort"
                @custom-filter="handleCustomFilter"
                @scroll="handleTableScroll"
                @cell-click="cellClickHandle"
            >
                <template #billNo>
                    <el-table-column
                        :label="billNoInfo"
                        min-width="195px"
                        align="left"
                        header-align="left"
                        prop="billNo"
                        :show-overflow-tooltip="true"
                        :width="getColumnWidth(setModule, 'billNo')"
                    >
                        <template #header>
                            <div class="header-operate">
                                <span class="text">{{ billNoInfo }}</span>
                                <div class="more-operate">
                                    <div class="header-caret" @click="getSortCommon('billNo', $event)"></div>
                                    <TableHeaderFilter
                                        ref="billNoTableHeaderFilterRef"
                                        prop="billNo"
                                        filter-order="text"
                                        :use-common-select="true"
                                        :clear-last-data="false"
                                        v-model:is-filter="billNoIsFilter"
                                        @filter-search="handleCustomFilter"
                                    ></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <template v-if="scope.row.billType !== 0 && scope.row.billId !== 0">
                                <template v-if="!checkBillNoPermission(scope.row.billType)">{{ scope.row.billNo }}</template>
                                <a v-else class="link" @click="checkBill(scope.row)">
                                    {{ scope.row.billNo }}
                                </a>
                            </template>
                        </template>
                    </el-table-column>
                </template>
                <template #vtName>
                    <el-table-column
                        label="凭证模板"
                        min-width="195px"
                        align="left"
                        header-align="left"
                        prop="vtName"
                        :show-overflow-tooltip="true"
                        :width="getColumnWidth(setModule, 'vtName')"
                    >
                        <template #header>
                            <div class="header-operate">
                                <div class="left">
                                    <span class="text">凭证模板</span>
                                    <el-popover
                                        placement="right"
                                        :width="200"
                                        trigger="hover"
                                        content="支持批量更换同一类型单据的业务凭证模板"
                                    >
                                        <template #reference>
                                            <el-icon class="batch-update-template" @click="handleBatchUpdateTemplate"><Edit /></el-icon>
                                        </template>
                                    </el-popover>
                                </div>
                                <div class="more-operate">
                                    <div class="header-caret" @click="getSortCommon('vtName', $event)"></div>
                                    <TableHeaderFilter
                                        ref="vtNameTableHeaderFilterRef"
                                        prop="vtName"
                                        filter-order="multiSelect"
                                        :default-not-select-all="true"
                                        :use-common-select="true"
                                        :clear-last-data="false"
                                        :has-select-list="lastVtNames"
                                        :option="headerTempOptions"
                                        :customProp="{ id: 'label', name: 'label' }"
                                        v-model:is-filter="vtNameIsFilter"
                                        @filter-search="handleCustomFilter"
                                    ></TableHeaderFilter>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <Select
                                v-if="scope.row.showSelect"
                                ref="vtNameSelectRef"
                                class="vtname-select"
                                style="width: 98%"
                                v-model="scope.row.vtId"
                                automatic-dropdown
                                placeholder="请选择"
                                :bottom-html="checkPermission(['businessvouchertemplate-canedit']) ? selectBottomHtml:''"
                                @bottom-click="openBusinessVoucherTemplate(scope.row.billType,scope.row.ietypeName,scope.row.businessType)"
                                @blur="onVtNameBlur(scope.row)"
                                @change="vtNameChange(scope.row)"
                                @visible-change="visible => clickVtName(visible,scope.row)"
                            >
                            <OtherOption
                                v-for="item in getVtData(scope.row)"
                                :key="item.id"
                                :label="item.code + ' ' + item.name"
                                :value="item.id"
                                @mouse-enter="(style: DOMRect) => handleMouseEnter(style, item)"
                                @mouse-leave="handleMouseLeave"
                            ></OtherOption>
                            </Select>
                            <span v-else 
                                @mouseenter="(event: any) => handleMouseEnter(event.target.getBoundingClientRect(), getVtData(scope.row).find((item: any) => item.id === scope.row.vtId) as IColVoucherTemplate)"
                                @mouseleave="handleMouseLeave">
                                    {{ scope.row.vtName }}
                            </span>
                        </template>
                    </el-table-column>
                </template>
                <template #voucherInfo>
                    <el-table-column label="关联凭证" min-width="150px" align="left" header-align="left" :show-overflow-tooltip="true">
                        <template #header>
                            <div class="header-operate">
                                <span class="text">关联凭证</span>
                                <div class="more-operate">
                                    <div class="header-caret" @click="getSortCommon('voucherInfo', $event)"></div>
                                </div>
                            </div>
                        </template>
                        <template #default="scope">
                            <template v-if="scope.row.voucherInfo !== null && scope.row.voucherInfo !== ''">
                                <a :class="{ link: checkPermission(['voucher-canview']) }" @click="checkVoucher(scope.row)">
                                    {{ scope.row.voucherInfo }}
                                </a>
                            </template>
                        </template>
                    </el-table-column>
                </template>
                <template #pageOther>
                    <span class="total" v-show="selectData.length">
                        已勾选{{ selectData.length }}条， 共{{ paginationData.total }}条记录
                        <span class="ml-10">勾选行合计金额：{{ totalAmount }}</span>
                    </span>
                </template>
            </Table>
        </div>
        <div class="main-center-entry-right" ref="rightContentRef" :class="{ expend }">
            <div :class="{ function: true, expend }" @click="expend = !expend"></div>
            <div class="main-center-entry-right-buttons">
                <a :class="{ checked: !onlyShowUnRelationVoucher }" @click="showAllData">全部</a>
                <a :class="{ checked: onlyShowUnRelationVoucher }" @click="showOnlyUnRelationVoucher">未生成凭证</a>
            </div>
            <div class="select-content">
                <div class="top-search">
                    <div class="search-box"></div>
                    <div class="close" @click="handleClearSearchVal" v-show="searchVal">
                        <el-icon color="#a8abb2"><CircleClose /></el-icon>
                    </div>
                    <Tooltip :content="searchVal" :max-width="160" :lineClamp="1" placement="top" :teleported="true">
                        <input
                            ref="searchInputRef"
                            placeholder="输入关键词搜索"
                            id="quicklySwitchInput"
                            autocomplete="off"
                            type="text"
                            :value="searchVal"
                            @input="handleInput"
                            @keydown="handleSearchKeydown"
                            @focus="handleFocus"
                        />
                    </Tooltip>
                </div>
                <div class="select-info" ref="searchInfoContainerRef" @scroll="hideToolTip" v-show="searchListShow">
                    <template v-for="(item, index) in searchShowList" :key="item.id">
                        <div
                            class="select-info-inner item"
                            :class="{ active: index === currentBillTypeIndex }"
                            @mouseenter="tryShowToolTip($event, item)"
                            @mouseleave="hideToolTip"
                            @click="handleSearchItemClick(item)"
                        >
                            {{ item.text }}
                        </div>
                    </template>
                    <div class="select-info-inner" v-show="searchShowList.length === 0" style="text-align: center">没有匹配的选项</div>
                </div>
            </div>
            <div class="tool-tip" v-show="tooltipShow" ref="tooltipRef">{{ tooltipText }}</div>
            <div class="main-center-entry-right-tree">
                <el-scrollbar always ref="treeScrollBarRef" @scroll="handleScroll" height="100%">
                    <el-tree
                        :data="moduleTree"
                        node-key="id"
                        ref="treeRef"
                        highlight-current
                        default-expand-all
                        :props="{ label: 'text', children: 'children', class: treeClass }"
                        :expand-on-click-node="false"
                        class="el-tree-all"
                        @node-click="nodeClickHandle"
                    >
                        <template #default="{ data }">
                            <span class="custom-tree-node">
                                <input ref="virtualInputRef" class="virtual-input" type="text" v-if="data.id === billType" />
                                <span :class="data.children ? 'tree-icon tree-folder tree-folder-open' : 'tree-icon tree-file'"></span>
                                <span class="tree-title">{{ data.text }}</span>
                            </span>
                        </template>
                    </el-tree>
                </el-scrollbar>
            </div>
        </div>
        <el-dialog
            destroy-on-close
            v-model="batchUpdateTemplateInfo.display"
            center
            width="440px"
            title="批量修改凭证模板"
            class="custom-confirm"
            modal-class="modal-class"
        >
            <div class="batch-update-template-content" ref="batchUpdateTemplateRef">
                <div class="update-main">
                    <span class="update-title">凭证模板：</span>
                    <Select 
                        v-model="batchUpdateTemplateInfo.vtId" 
                        :style="{ width: '180px' }"  
                        :bottom-html="checkPermission(['businessvouchertemplate-canedit']) ? selectBottomHtml : ''"
                        @bottom-click="openBusinessVoucherTemplate(selectData[0].billType, 'ietypeName' in selectData[0] ? selectData[0].ietypeName : '', 'businessType' in selectData[0] ? selectData[0].businessType : 0)"
                        @visible-change="visible => clickVtName(visible,selectData[0])">
                            <OtherOption
                                v-for="item in batchTemplateList"
                                :key="item.id"
                                :label="item.code + ' ' + item.name"
                                :value="item.id"
                                @mouse-enter="(style: DOMRect) => handleMouseEnter(style, item)"
                                @mouse-leave="handleMouseLeave">
                            </OtherOption>
                    </Select>
                </div>
                <div class="buttons">
                    <span class="button mr-20" @click="batchUpdateTemplateInfo.display = false">取消</span>
                    <span class="button solid-button" @click="confirmBatchUpdateTemp">确认</span>
                </div>
              
            </div>
        </el-dialog>
        <div class="detail-tool-tip-content" ref="templateDetailRef"
            v-show="batchUpdateTemplateInfo.tipDisplay && batchUpdateTemplateInfo.hoverItem">
            <template v-if="batchUpdateTemplateInfo.hoverItem">
                <div class="detail-tool-title">类型：{{ getDisPlayVtLabel() }}</div>
                <div class="detail-tool-label">
                    <Tooltip :content="batchUpdateTemplateInfo.hoverItem.code + batchUpdateTemplateInfo.hoverItem.name"
                        :max-width="200" :font-size="20" :lineClamp="1" placement="top-start">
                        {{ batchUpdateTemplateInfo.hoverItem.code + batchUpdateTemplateInfo.hoverItem.name }}
                    </Tooltip>
                </div>
                <div class="detail-tool-info">
                    <template v-for="project in batchUpdateTemplateInfo.hoverItem.lines" :key="project.description">
                        <div class="detail-tool-info-line">
                            <Tooltip :content="project.asubInfo" :max-width="100" :lineClamp="3" placement="right">
                                <div class="detail-tool-info-line-left">
                                    {{ project.direction == 1 ? "借" : "贷" }}：{{ project.asubInfo }}
                                </div>
                            </Tooltip>
                            <div class="detail-tool-info-line-right">{{ project.amountCalcWayText }}</div>
                        </div>
                    </template>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, nextTick, reactive, computed, onActivated } from "vue";
import { usePagination } from "@/hooks/usePagination";
import { checkPermission } from "@/util/permission";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { useDebugCurrentPage } from "@/hooks/useDebugCurrentPage";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useLoading } from "@/hooks/useLoading";
import {
    Searchs,
    getModuleSearchType,
    getTemplateTypeId,
    checkBillNoPermission,
    getErpEntryColumns,
    checkCanSearch,
    SearchType,
    combineSearchs,
    ColSearchs,
    RelationVoucherState,
    getCurrentSortColumn,
    appendTreeLevel,
    getCurrentAllBillType,
    formatBillTypeText,
} from "../utils";
import { tableHeaderCustomSort, type SortType } from "@/components/Table/utils";
import { formatMoney } from "@/util/format";

import { type IResponseModel, request } from "@/util/service";
import type { IBaseErpData, IErpCashierData, IErpInvoiceData, IErpTransferData, IQueryParams, ITreeData, ErpDataType } from "../types";
import type { FilterOrder, IColumnProps } from "@/components/Table/IColumnProps";
import type { IBusinessVoucherTemplate } from "@/views/Erp/BusinessVoucherTemplate/types";

import { ElTree, ElScrollbar } from "element-plus";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import OtherOption from "./Option.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import TableHeaderFilter from "@/components/TableHeaderFilter/index.vue";

const setModule = "ErpBusinessVoucher";

const props = defineProps<{
    scmAsid: number;
    scmAsName: string;
    scmProductType: number;
    moreSearchs: Searchs;
    isProjectAccountingEnabled: boolean;
    taxEnabled: boolean;
    getSearchParams: () => IQueryParams;
    syncHasGenerateVoucher: (unRelation: boolean) => void;
}>();
const emit = defineEmits<{
    (e: "set-module-search-list", searchs: Array<SearchType>, billTypes: Array<{ id: string; name: string }>): void;
}>();

const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();
const debugCanChangeCurrentPage = useDebugCurrentPage(paginationData, () => {
    handleSearch();
});

function vtNoDataText(billType: number,businessTypeName:string){
    return `${formatBillTypeText(billType,moduleTree.value[0]?.children ?? [])}业务类型为${businessTypeName}的没有默认模板，请检查并配置凭证模板后再生成凭证`
}

const selectBottomHtml = `
    <div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">+新增凭证模版</a>
    </div>`;
function openBusinessVoucherTemplate(billType:number,ietypeName:string,businessType:number) {
    const isJournal = [3010, 3011, 3012, 3020, 3021, 3022].includes(billType);
    const isIncome = isJournal && ietypeName.startsWith("收-");
    let templateTypeId = "541"; // 日记账收入
    if (!isJournal) {
        templateTypeId = getTemplateTypeId(billType)+'';
    } else if(!isIncome){ 
        templateTypeId = "542"; // 日记账支出
    }
    if(["550","551"].includes(templateTypeId)){
        templateTypeId = templateTypeId+'_'+businessType;
    }
    globalWindowOpenPage("/Erp/BusinessVoucherTemplate?templateType="+templateTypeId, "业务凭证模板");
}
const emptyText = ref<string>(" ");
const voucherEntryData = ref<Array<any>>([]);

const columns = ref<Array<IColumnProps>>([]);

const selectData = ref<Array<ErpDataType>>([]);
const totalAmount = computed(() => {
    const total = selectData.value.reduce((pre, cur) => (pre += cur.totalAmount), 0);
    return formatMoney(total) || "0.00";
});
function selectionChange(selection: Array<any>) {
    selectData.value = selection.slice();
}
function getSelectData() {
    return selectData.value;
}
function clearSelection() {
    tableRef.value?.clearSelection();
    selectData.value.length = 0;
}

function cellClickHandle(row: any, column: any) {
    if (column.label !== "凭证模板") return;
    row.showSelect = true;
    if (!row.vtId) row.vtId = "";
    let clickIndex = voucherEntryData.value.findIndex((item: any) => item === row);
    voucherEntryData.value.forEach((item: any, index: number) => {
        if (index !== clickIndex) {
            item.showSelect = false;
        }
    });
    nextTick().then(() => {
        vtNameSelectRef.value?.focus();
    });
}
const vtNameSelectRef = ref<InstanceType<typeof Select>>();
function checkVoucher(row: any) {
    if (!checkPermission(["voucher-canview"])) return;
    const params = {
        pid: row.pid ?? row.p_id,
        vid: row.vid ?? row.v_id,
        fcode: "businessvoucher-canview",
        isTemp: row.vstatus === 1,
        from: "erp-business",
    };
    globalWindowOpenPage("/Voucher/VoucherPage?" + getUrlSearchParams(params), "查看凭证");
}
function checkBill(row: IBaseErpData) {
    if (!checkBillNoPermission(row.billType)) return;
    const isJournal = [3010, 3011, 3012, 3020, 3021, 3022].includes(row.billType);
    if (row.billType === 2010 || row.billType === 2020) {
        const bill = row as IErpInvoiceData;
        const params = { invoiceCode: bill.billNo, searchStartDate: bill.billDateText, searchEndDate: bill.billDateText };
        const path = bill.billType === 2020 ? "/Invoice/PurchaseInvoice?" : "/Invoice/SalesInvoice?";
        const name = bill.billType === 2020 ? "进项发票" : "销项发票";
        globalWindowOpenPage(path + getUrlSearchParams(params), name);
    } else if (isJournal) {
        const bill = row as IErpCashierData;
        const lineSnName = row.billNo.split("-");
        const lineSn = lineSnName.length > 0 ? lineSnName[lineSnName.length - 1] : "";
        const params = { searchStartDate: bill.billDateText, searchEndDate: bill.billDateText, cdAccount: row.billId, lineSn };
        const path = (row.billType + "").startsWith("301") ? "/Cashier/CashJournal?" : "/Cashier/DepositJournal?";
        const name = (row.billType + "").startsWith("301") ? "现金日记账" : "银行日记账";
        globalWindowOpenPage(path + getUrlSearchParams(params), name);
    } else if (row.billType === 3030) {
        const bill = row as IErpTransferData;
        const params = {
            startDate: bill.billDateText,
            endDate: bill.billDateText,
            acc_in: bill.cdaccountIn,
            acc_out: bill.cdaccount,
        };
        globalWindowOpenPage("/Cashier/Transfer?" + getUrlSearchParams(params), "内部转账");
    } else {
        let billType = row.billType;
        if (row.businessType) {
            // 自定义出入库类型 包括产品入/出库 借入/出 委外入/出库  入库1052 出库1062
            billType = billType === 1050 ? 1052 : 1062;
        }
        const params = { asid: props.scmAsid, bill_type: billType, id: row.billId };
        globalWindowOpenPage("/from_acc?" + getUrlSearchParams(params), "");
    }
}

function clickVtName(visible:boolean,row: any) {
    if(visible && !getVtData(row).length){
        ElNotify({ type: "warning", message: vtNoDataText(row.billType,row.businessTypeName) });
    }
}

function onVtNameBlur(row: any) {
    row.showSelect = false;
}
function vtNameChange(row: any) {
    const temp = voucherTemplateList.value.find((item: { id: number }) => item.id === row.vtId);
    if (temp) {
        row.vtName = temp.code + temp.name;
        updateVoucherTemplate([row], row.vtId, "single");
    }
}
class BatchUpdateTemplateInfo {
    display = false;
    vtId: number | "" = "";
    hoverItem: null | IColVoucherTemplate = null;
    timer: null | number = null;
    tipDisplay = false;
}
const batchUpdateTemplateInfo = reactive<BatchUpdateTemplateInfo>(new BatchUpdateTemplateInfo());
function getDisPlayVtLabel() {
    if (!batchUpdateTemplateInfo.hoverItem) return;
    const item = batchUpdateTemplateInfo.hoverItem;
    return item.parentVtTypeText ? `${item.parentVtTypeText}(${item.vtTypeText})` : item.vtTypeText;
}
function handleBatchUpdateTemplate() {
    if (!selectData.value.length) {
        ElNotify({ type: "warning", message: "请选择需要批量更换凭证模板的数据" });
        return;
    }
    if (!treeRef.value?.getCurrentNode() || treeRef.value?.getCurrentNode()?.children !== null) {
        ElNotify({ type: "warning", message: "右侧选择最明细的单据类型之后才能进行模板批量修改" });
        return;
    }
    batchUpdateTemplateInfo.vtId = "";
    batchTemplateList.value = getVtData(selectData.value[0]);
    batchUpdateTemplateInfo.display = true;
}
const batchUpdateTemplateRef = ref<HTMLElement>();
function calcPosition(optionStyle: DOMRect) {
    const rectTop = document.querySelector(".main-center-entry")?.getBoundingClientRect().top ?? 0;
    nextTick(() => {
        if(!templateDetailRef.value) return;
        const MainCentetHeight = document.querySelector(".main-center")?.getBoundingClientRect().height ?? 0;
        const isOverBottom = optionStyle.top - rectTop + templateDetailRef.value.offsetHeight > MainCentetHeight;
        // 20美观调整
        const position = {
            left: optionStyle.left + optionStyle.width - 20 + "px",
            top: isOverBottom ? optionStyle.top - rectTop - templateDetailRef.value.offsetHeight + 20 + "px" : optionStyle.top - rectTop + "px",
        };
        templateDetailRef.value && Object.assign(templateDetailRef.value.style, position);
    });
}
function handleMouseEnter(style: DOMRect, template: IColVoucherTemplate) {
    batchUpdateTemplateInfo.timer !== null && clearTimeout(batchUpdateTemplateInfo.timer);
    batchUpdateTemplateInfo.timer = setTimeout(() => {
        batchUpdateTemplateInfo.hoverItem = null;
        showTemplateDetail(style, template);
    }, 800);
}
const templateDetailRef = ref<HTMLElement>();
function showTemplateDetail(style: DOMRect, template: IColVoucherTemplate) {
    batchUpdateTemplateInfo.hoverItem = { ...template };
    batchUpdateTemplateInfo.tipDisplay = true;
    calcPosition(style);
}
function handleMouseLeave() {
    batchUpdateTemplateInfo.timer !== null && clearTimeout(batchUpdateTemplateInfo.timer);
    const asign = {
        timer: null,
        hoverItem: null,
        tipDisplay: false,
    };
    Object.assign(batchUpdateTemplateInfo, asign);
}
let canNext = true;
async function confirmBatchUpdateTemp() {
    if (!canNext) return;
    canNext = false;
    if (!batchUpdateTemplateInfo.vtId) {
        ElNotify({ type: "warning", message: "请选择凭证模板" });
        canNext = true;
        return;
    }
    await updateVoucherTemplate(selectData.value, ~~batchUpdateTemplateInfo.vtId, "multiple");
    batchUpdateTemplateInfo.display = false;
    canNext = true;
}
async function updateVoucherTemplate(select: Array<IBaseErpData>, vtid: number, type: "multiple" | "single") {
    let url = "";
    let data: any = {};
    const params: any = { vtid, scmAsid: props.scmAsid, scmProductType: props.scmProductType };
    const moduleType = parseInt(select[0].billType / 1000 + "", 10);
    if (moduleType === 2) {
        if (type === "single") {
            const row = select[0];
            url = "/api/VoucherForErp/UpdateInvoiceVoucherTempalteRelation";
            params.invoiceId = row.invoiceId;
        } else {
            url = "/api/VoucherForErp/BatchUpdateInvoiceVoucherTempalteRelation";
            data = select.map((item) => item.invoiceId);
        }
    } else if (moduleType === 3) {
        url = "/api/VoucherForErp/BatchUpdateJournalVoucherTempalteRelation";
        const arrayLineSn = select.map((row: any) => {
            const item = row as IErpCashierData;
            return {
                date: item.billDateText,
                created_date: item.billCreateDate,
                cdAccount: item.billId,
                ieType: billType.value === "3030" ? -1 : item.ietype,
            };
        });
        if (type === "single") {
            url = "/api/VoucherForErp/UpdateJournalVoucherTempalteRelation";
            data = arrayLineSn[0];
        } else {
            url = "/api/VoucherForErp/BatchUpdateJournalVoucherTempalteRelation";
            data = arrayLineSn;
        }
    } else {
        if (type === "single") {
            const row = select[0];
            url = "/api/BillVoucherTemplateRelation/Relate";
            params.billType = row.billType;
            params.billId = row.billId;
        } else {
            url = "/api/BillVoucherTemplateRelation/BatchRelate";
            params.billType = select[0].billType;
            data = select.map((item) => item.billId);
        }
    }
    url += "?" + getUrlSearchParams(params);
    await request({ url, method: "post", data }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({ type: "success", message: "凭证模板设置成功" });
            type === "multiple" && handleSearch();
        } else {
            ElNotify({ type: "warning", message: "凭证模板设置失败，请刷新页面重试或联系客服" });
        }
    });
}

const treeScrollBarRef = ref<InstanceType<typeof ElScrollbar>>();
let cacheScrollTop = 0;
function handleScroll(event: { scrollTop: number }) {
    cacheScrollTop = event.scrollTop;
}
onActivated(() => {
    treeScrollBarRef.value?.setScrollTop(cacheScrollTop);
});
const expend = ref(false);
const allTreeId = "1000";
const billType = ref(allTreeId);
const billNoInfo = computed(() => (billType.value === "2010" || billType.value === "2020" ? "发票号码" : "单据编号"));
function getVtData(row: IBaseErpData) {
    const isJournal = [3010, 3011, 3012, 3020, 3021, 3022].includes(row.billType);
    const isIncome = isJournal && (row as IErpCashierData).ietypeName.startsWith("收-");
    return filterTemplates(row.billType, row.businessType, isJournal, isIncome);
}
function getAllBillTypes(data: ITreeData) {
    const billTypes: Array<{ vtype?: number; businessType?: number }> = [];
    if (!data.children) return [{ vtype: data.attributes?.VType, businessType: data.attributes?.BusinessId }];
    data.children.forEach((item) => {
        if (item.children) {
            billTypes.push(...getAllBillTypes(item));
        } else {
            billTypes.push({ vtype: item.attributes?.VType, businessType: item.attributes?.BusinessId });
        }
    });
    return billTypes;
}
function filterTemplates(billType: number, businessType: number, isJournal: boolean, isIncome: boolean) {
    let templateTypeId = 541; // 日记账收入
    if (!isJournal) {
        templateTypeId = getTemplateTypeId(billType);
    } else if (!isIncome) {
        templateTypeId = 542; // 日记账支出
    }
    if (businessType !== 0 && (billType === 1050 || billType === 1060)) {
        // 其他出入库
        const vtType = billType === 1050 ? 550 : 551;
        return voucherTemplateList.value.filter((item) => item.vtType === vtType && item.customType === businessType);
    }
    return voucherTemplateList.value.filter((item) => item.vtType === templateTypeId);
}
const headerTempOptions = ref<Array<IColVoucherTemplate>>([]);
function changeVtTypeOptions() {
    const billTypes = getAllBillTypes(treeRef.value?.getCurrentNode() as ITreeData);
    let vtTypes: Array<IColVoucherTemplate> = [];
    billTypes.forEach((billTypeInfo) => {
        const isJournal = [3011, 3012, 3021, 3022].includes(billTypeInfo.vtype || 0);
        const isIncome = isJournal && (billTypeInfo.vtype === 3011 || billTypeInfo.vtype === 3022);
        vtTypes.push(...filterTemplates(billTypeInfo.vtype || 0, billTypeInfo.businessType || 0, isJournal, isIncome));
    });
    vtTypes = [...new Set(vtTypes)];
    headerTempOptions.value = vtTypes;
}
const treeRef = ref<InstanceType<typeof ElTree>>();
const onlyShowUnRelationVoucher = ref(false);
function showAllData() {
    if (onlyShowUnRelationVoucher.value) {
        onlyShowUnRelationVoucher.value = false;
    }
    props.syncHasGenerateVoucher(onlyShowUnRelationVoucher.value);
    nodeClickHandle(treeRef.value?.getCurrentNode());
}
function showOnlyUnRelationVoucher() {
    if (!onlyShowUnRelationVoucher.value) {
        onlyShowUnRelationVoucher.value = true;
    }
    props.syncHasGenerateVoucher(onlyShowUnRelationVoucher.value);
    nodeClickHandle(treeRef.value?.getCurrentNode());
}
function syncTreeStatus(unRelation: boolean) {
    onlyShowUnRelationVoucher.value = unRelation;
}
const relationVoucherState = computed(() => {
    if (onlyShowUnRelationVoucher.value) return RelationVoucherState.UnRelation;
    return RelationVoucherState.All;
});
function setColumns() {
    let currentNode = treeRef.value?.getCurrentNode() as ITreeData;
    if (!currentNode) {
        treeRef.value?.setCurrentKey(allTreeId);
        currentNode = treeRef.value?.getCurrentNode() as ITreeData;
    }
    const currentId = currentNode.attributes?.BusinessId; // 其他出入库的id是父级和子级拼成的  不能使用
    const billType = currentId ? currentId.toString() : currentNode.id;
    const _columns = getErpEntryColumns(billType, billRootType.value, props.taxEnabled, moduleTree.value[0]?.children ?? []);
    if (!props.isProjectAccountingEnabled) {
        const index = _columns.findIndex((item) => item.prop === "projectName");
        index !== -1 && _columns.splice(index, 1);
    }
    _columns.forEach((column) => {
        column.defaultNotSelectAll = true;
    });
    columns.value = _columns;
}
let currentSortColumn = "";
let isAsc = true;
function handleCustomSort(prop: string, sortType: SortType, search = true) {
    currentSortColumn = sortType === 0 ? "" : prop;
    isAsc = sortType !== -1;
    search && handleSearch();
}
function getSortCommon(prop = "", event: MouseEvent) {
    const sortType = tableHeaderCustomSort(event);
    if (sortType === false) return;
    handleCustomSort(prop, sortType);
}
const multiSelectMap: Record<string, keyof typeof colQueryParameters> = {
    vendorName: "vendorNames",
    customerName: "customerNames",
    departmentName: "departmentNames",
    projectName: "projectNames",
    employeeName: "employeeNames",
    billType: "billType",
    vtName: "vtNames",
    assemblyName: "assemblyNames",
    whName: "warehousess",
    ietypeName: "ieTypeNames",
    cdaccount: "cdAccounts",
    cdaccountIn: "cdAccountIns",
    invoiceTypeName: "invoiceTypeNames",
    expenseTypeName: "expenseTypeNames",
    offsetTypeName: "offsetTypeNames",
    businessTypeName: "businessTypeNames",
};
function handleCustomFilter(prop: string, value: any, filterOrder: FilterOrder) {
    if (!filterOrder) return;
    const updateColQueryParameters = (key: keyof typeof colQueryParameters, val: any) => {
        colQueryParameters[key] = val;
    };
    if (filterOrder === "multiSelect") {
        const key = multiSelectMap[prop];
        if (key) {
            if (prop === "vtName") {
                vtNameIsFilter.value = value.length > 0;
                updateColQueryParameters(key, vtNameIsFilter.value ? value : []);
                lastVtNames.value = value;
            } else {
                updateColQueryParameters(key, value);
            }
        }
    } else if (filterOrder === "date" && prop === "billDateText") {
        colQueryParameters.startBillDate = value.s;
        colQueryParameters.endBillDate = value.e;
    } else if (filterOrder === "number") {
        const numberMap: Record<string, keyof typeof colQueryParameters> = {
            totalAmount: "startAmount",
            quantity: "startQuantity",
        };
        const key = numberMap[prop];
        if (key) {
            colQueryParameters[key] = value.s;
            const endKey = key.replace("start", "end") as keyof typeof colQueryParameters;
            colQueryParameters[endKey] = value.e;
        }
    } else if (filterOrder === "text") {
        const textMap: Record<string, keyof typeof colQueryParameters> = {
            note: "note",
            billNo: "billNo",
            description: "description",
            name: "payeeNames",
        };
        const key = textMap[prop];
        if (key) {
            if (prop === "name") {
                updateColQueryParameters(key, [value.trim()]);
            } else {
                updateColQueryParameters(key, value.trim());
                if (prop === "billNo") {
                    billNoIsFilter.value = !!value.trim();
                }
            }
        }
    }
    handleSearch();
}
let cacheTableScrollTop = 0;
function handleTableScroll(scrollTop?: number) {
    tableRef.value?.$el?.click();
    if (useCacheTableScrollTop) return;
    cacheTableScrollTop = scrollTop || 0;
}
let useCacheTableScrollTop = false;
function setCacheTableScroll() {
    useCacheTableScrollTop = true;
}
const billNoTableHeaderFilterRef = ref<InstanceType<typeof TableHeaderFilter>>();
const billNoIsFilter = ref(false);
const vtNameTableHeaderFilterRef = ref<InstanceType<typeof TableHeaderFilter>>();
const vtNameIsFilter = ref(false);
const lastVtNames = ref<Array<string>>([]);
function resetAllHeaderFilter() {
    billNoTableHeaderFilterRef?.value?.resetFilter();
    vtNameTableHeaderFilterRef?.value?.resetFilter();
    billNoIsFilter.value = false;
}

let colQueryParameters = new ColSearchs();
function getSearchCombineParams() {
    const searchs = props.getSearchParams();
    const queryParameters = combineSearchs(props.moreSearchs, cacheModuleSearchList);
    const otherParams = {
        startDate: searchs.startDate,
        endDate: searchs.endDate,
        nodeId: billType.value,
        billTypes: [],
        relationVoucherState: relationVoucherState.value,
        searchData: searchs.txtSearch,
    };
    Object.assign(queryParameters, otherParams);
    if (!props.isProjectAccountingEnabled) queryParameters.projectIds.length = 0;
    const data = {
        queryParameters,
        colQueryParameters,
        orderField: getCurrentSortColumn(currentSortColumn),
        isAsc,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    const params = {
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    return { data, params };
}
async function handleSearch() {
    tableRef.value?.$el?.click(); // 去除异常出现的气泡
    const searchs = props.getSearchParams();
    if (!checkCanSearch(searchs)) return;
    const { data, params } = getSearchCombineParams();
    useLoading().enterLoading("努力加载中，请稍候...");
    voucherEntryData.value.length = 0;
    setColumns();
    await request({ url: "/api/VoucherForErp/GetPagingList", method: "post", data, params })
        .then((res: IResponseModel<{ data: null | Array<any>; count: number }>) => {
            if (res.state !== 1000 || !res.data) {
                emptyText.value = "暂无数据";
                return;
            }
            voucherEntryData.value = res.data.data ?? [];
            if (res.data.count === 0) {
                emptyText.value = "暂无数据";
            }
            paginationData.total = res.data.count;
            tableRef.value?.setScrollTop(0);
        })
        .catch(() => {
            emptyText.value = "暂无数据";
            ElNotify({ type: "warning", message: "获取数据失败，请刷新页面后重试" });
        })
        .finally(() => {
            useLoading().quitLoading();
            if (!useCacheTableScrollTop) return;
            useCacheTableScrollTop = false;
            tableRef.value?.setScrollTop(cacheTableScrollTop);
        });
}
const tableRef = ref<InstanceType<typeof Table>>();

const billRootType = ref("1000");
async function nodeClickHandle(data: any) {
    if (data === allTreeId) {
        treeRef.value?.setCurrentKey();
        tryFocusVirtualInput();
        await searchHandle(data);
    } else if (data.id) {
        const currentId = data.attributes?.BusinessId;
        const id = currentId ? currentId.toString() : data.id;
        const calcParentType = parseInt(~~id / 10 + "", 10) * 10 + "";
        const defaultParentType = data.attributes?.BillRootType;
        // 这里计算是有问题的 因为这样拿到的发票的父级id是1010和1020 但是发票的id是2010和2020
        // 但是实际上发票会走到自己的逻辑不会走到单据的逻辑
        // billRootType 是为了给进销存单据使用 所以虽然有问题 但在这里不受影响
        billRootType.value = defaultParentType ? defaultParentType.toString() : calcParentType;
        tryFocusVirtualInput();
        await searchHandle(data.id);
    }
}
function tryFocusVirtualInput() {
    if (needFocus) {
        needFocus = false;
        nextTick().then(() => {
            virtualInputRef.value?.focus();
            nextTick().then(() => {
                virtualInputRef.value?.blur();
            });
        });
    }
}
let isSetHeaderOptions = false;
function backFirstPage() {
    debugCanChangeCurrentPage();
    paginationData.currentPage = 1;
}
async function searchHandle(treeId: string) {
    if (billType.value !== treeId) backFirstPage();
    billType.value = treeId;
    setModuleSearchList();
    voucherEntryData.value = [];
    if (isSetHeaderOptions) return;
    await handleSearch();
}

let cacheModuleSearchList: Array<SearchType> = [];
let needResetHeaderOptions = true;
function debugResetHeaderSearch() {
    needResetHeaderOptions = false;
    const timer = setTimeout(() => {
        needResetHeaderOptions = true;
        clearTimeout(timer);
    }, 100);
}
let billTypes: Array<{ id: string; name: string }> = [];
function setModuleSearchList() {
    const currentNode = treeRef.value?.getCurrentNode() as ITreeData;
    cacheModuleSearchList = getModuleSearchType(currentNode, moduleTree.value);
    if (!props.isProjectAccountingEnabled) {
        const index = cacheModuleSearchList.findIndex((item) => item === SearchType.Project);
        index !== -1 && cacheModuleSearchList.splice(index, 1);
    }
    if (needResetHeaderOptions) {
        resetAllHeaderOptions();
    }
    billTypes = getCurrentAllBillType(treeRef.value?.getCurrentNode() as ITreeData, moduleTree.value);
    emit("set-module-search-list", cacheModuleSearchList, billTypes);
}
function resetAllHeaderOptions() {
    colQueryParameters = new ColSearchs();
    tableRef.value?.resetAllHeaderSort();
    tableRef.value?.resetAllHeaderFilter();
    changeVtTypeOptions();
    resetAllHeaderFilter();
}

const moduleTree = ref<Array<ITreeData>>([]);
const flatModuleTree = ref<Array<ITreeData>>([]);
const virtualInputRef = ref<HTMLInputElement>();
async function getBusinessModuleTree() {
    await request({
        url: `/api/VoucherForErp/GetBusinessModuleTree?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
        method: "post",
    }).then((res: IResponseModel<Array<ITreeData>>) => {
        if (res.state == 1000) {
            moduleTree.value = [{ id: allTreeId, text: "全部", children: res.data, attributes: null, level: 0 }];
            flatModuleTree.value = formatterSearchList(res.data);
            searchShowList.value = flatModuleTree.value;
            appendTreeLevel(moduleTree.value);
            nextTick().then(() => {
                treeRef.value?.setCurrentKey(billType.value);
            });
        }
    });
}
function treeClass(data: any) {
    return data.id === allTreeId ? "is-root" : "";
}

interface IColVoucherTemplate extends IBusinessVoucherTemplate {
    label: string;
}
const voucherTemplateList = ref<Array<IColVoucherTemplate>>([]);
const batchTemplateList = ref<Array<IColVoucherTemplate>>([]);
async function getVoucherTemplate() {
    await request({
        url: `/api/VoucherForErp/GetTemplates?scmProductType=${props.scmProductType}&scmAsid=${props.scmAsid}`,
        method: "post",
    }).then((res: IResponseModel<Array<IBusinessVoucherTemplate>>) => {
        if (res.state !== 1000) return;
        voucherTemplateList.value = res.data.map((item) => ({ ...item, label: item.code + item.name }));
        changeVtTypeOptions();
    });
}
async function searchDataByVirtualClick(treeId?: string) {
    if (needResetHeaderOptions) {
        currentSortColumn = "";
        isAsc = true;
    }
    if (treeId) {
        billType.value = treeId;
        treeRef.value?.setCurrentKey(treeId);
    }
    await nodeClickHandle(treeRef.value?.getCurrentNode());
}
function getBillType() {
    return billType.value;
}

let needFocus = false;
function virtualScrollTreeNode() {
    needFocus = true;
}

// 快速切换
const searchVal = ref("");
function clearSearch() {
    searchVal.value = "";
    searchShowList.value = flatModuleTree.value;
}
const searchInputRef = ref<HTMLInputElement>();
function handleClearSearchVal() {
    clearSearch();
    searchListShow.value = false;
    nextTick().then(() => {
        searchInputRef.value?.focus();
    });
}
function handleInput(event: Event) {
    currentBillTypeIndex.value = -1;
    searchListShow.value = true;
    const text = (event.target as HTMLInputElement).value;
    searchVal.value = text;
    searchShowList.value = flatModuleTree.value.filter((item) => item.text.indexOf(text) > -1);
}
const currentBillTypeIndex = ref(-1);
function handleSearchKeydown(event: any) {
    if (event.keyCode === 38) {
        if (currentBillTypeIndex.value === -1) {
            currentBillTypeIndex.value = searchShowList.value.length - 1;
        } else {
            const nextIndex = currentBillTypeIndex.value - 1;
            currentBillTypeIndex.value = nextIndex === -1 ? searchShowList.value.length - 1 : nextIndex;
        }
        autoScroll();
    }
    if (event.keyCode === 40) {
        if (currentBillTypeIndex.value === -1) {
            currentBillTypeIndex.value = 0;
        } else {
            const nextIndex = currentBillTypeIndex.value + 1;
            currentBillTypeIndex.value = nextIndex === searchShowList.value.length ? 0 : nextIndex;
        }
        autoScroll();
    }
    if (event.keyCode === 13 && currentBillTypeIndex.value !== -1) {
        handleSearchItemClick(searchShowList.value[currentBillTypeIndex.value]);
    }
}
const searchInfoContainerRef = ref<HTMLDivElement>();
function autoScroll() {
    const content = searchInfoContainerRef.value;
    if (!content) return;
    const scrollTop = content.scrollTop;
    const contentHeight = content.clientHeight;
    const items = content.querySelectorAll(".item") as NodeListOf<HTMLDivElement>;
    const offsetTop = items[currentBillTypeIndex.value]?.offsetTop || 0;
    const itemHeight = (items[currentBillTypeIndex.value]?.clientHeight || 0) + 28;
    if (scrollTop > offsetTop) {
        content.scrollTop = offsetTop;
    } else {
        if (offsetTop + itemHeight > scrollTop + contentHeight) {
            if (itemHeight > contentHeight) {
                content.scrollTop = offsetTop;
            } else {
                content.scrollTop = offsetTop - contentHeight + itemHeight;
            }
        }
    }
}
function handleFocus() {
    searchListShow.value = true;
    currentBillTypeIndex.value = -1;
    document.addEventListener("click", beforeHandleBlur);
}
function beforeHandleBlur(event: Event) {
    const targetElement = event.target;
    const inputElement = document.getElementById("quicklySwitchInput");
    if (targetElement !== inputElement && !inputElement?.contains(targetElement as Node)) {
        handleBlur();
    }
}
const handleBlur = () => {
    const blurTimer = setTimeout(() => {
        clearTimeout(blurTimer);
        searchListShow.value = false;
        document.removeEventListener("click", beforeHandleBlur);
    }, 100);
};
const searchListShow = ref(false);
const rightContentRef = ref<HTMLDivElement>();
const searchShowList = ref<Array<ITreeData>>([]);
function handleSearchItemClick(item: ITreeData) {
    needFocus = true;
    searchVal.value = item.text;
    searchDataByVirtualClick(item.id);
    searchListShow.value = false;
    currentBillTypeIndex.value = -1;
}
function formatterSearchList(searchList: Array<ITreeData>) {
    const list: ITreeData[] = [];
    searchList.forEach((item) => {
        list.push(item);
        if (item.children) {
            list.push(...formatterSearchList(item.children));
        }
    });
    return list;
}
const tooltipShow = ref(false);
const tooltipText = ref("");
const tooltipRef = ref<HTMLDivElement>();
function tryShowToolTip(event: MouseEvent, item: ITreeData) {
    const target = event.target as HTMLElement;
    if (target.scrollWidth > target.offsetWidth) {
        tooltipShow.value = true;
        const { top = 0 } = target.getBoundingClientRect();
        const { top: pTop = 0 } = rightContentRef.value?.getBoundingClientRect() || {};
        tooltipText.value = item.text;
        tooltipRef.value?.style.setProperty("top", top - pTop + "px");
        setTimeout(() => {
            const { width = 0 } = tooltipRef.value?.getBoundingClientRect() || {};
            tooltipRef.value?.style.setProperty("left", -width - 8 + "px");
        });
    }
}
function hideToolTip() {
    tooltipShow.value = false;
    tooltipText.value = "";
}
function setCustomFilter(prop: keyof typeof colQueryParameters, filterOrder: FilterOrder, value: any) {
    isSetHeaderOptions = true;
    nextTick().then(() => {
        isSetHeaderOptions = false;
        resetAllHeaderOptions();
        colQueryParameters[prop] = value;
        handleSearch();
        nextTick().then(() => {
            tableRef.value?.setCustomFilter(prop, filterOrder, value);
        });
    });
}

function getColKeyAndData(prop: keyof typeof colQueryParameters, value: any, filterOrder: FilterOrder) {
    let key = "";
    let data: any = value;
    if (!filterOrder) return { key, data };
    if (filterOrder === "multiSelect") {
        key = multiSelectMap[prop] || "";
    } else if (filterOrder === "date") {
        if (prop === "startBillDate" || prop === "endBillDate") {
            key = "billDateText";
            data = prop === "startBillDate" ? { s: value } : { e: value };
        }
    } else if (filterOrder === "number") {
        if (prop === "startAmount" || prop === "endAmount") {
            key = "totalAmount";
            data = prop === "startAmount" ? { s: value } : { e: value };
        } else if (prop === "startQuantity" || prop === "endQuantity") {
            key = "quantity";
            data = prop === "startQuantity" ? { s: value } : { e: value };
        }
    } else if (filterOrder === "text") {
        const textMap: Record<string, string> = {
            note: "note",
            billNo: "billNo",
            description: "description",
            payeeNames: "name",
        };
        key = textMap[prop] || "";
        if (prop === "payeeNames") {
            data = value[0];
        }
    }
    return { key, data };
}
function getFilterOrder(key: string) {
    if (Object.keys(multiSelectMap).includes(key)) {
        return "multiSelect";
    } else if (["billDateText"].includes(key)) {
        return "date";
    } else if (["totalAmount", "quantity"].includes(key)) {
        return "number";
    } else if (["note", "billNo", "description", "name"].includes(key)) {
        return "text";
    }
    return "";
}
function batchSetCustomFilter() {
    isSetHeaderOptions = true;
    nextTick().then(() => {
        isSetHeaderOptions = false;
        handleSearch();
        for (let key1 in colQueryParameters) {
            const key = key1 as keyof typeof colQueryParameters;
            const value = colQueryParameters[key];
            const filterOrder = getFilterOrder(key);
            const { key: k, data } = getColKeyAndData(key, value, filterOrder);
            tableRef.value?.setCustomFilter(k, filterOrder, data);
        }
    });
}
function getCurrentNode() {
    return treeRef.value?.getCurrentNode() as ITreeData;
}

defineExpose({
    getVoucherTemplate,
    getBusinessModuleTree,
    searchDataByVirtualClick,
    getSelectData,
    clearSelection,
    getBillType,
    syncTreeStatus,
    setCacheTableScroll,
    virtualScrollTreeNode,
    debugResetHeaderSearch,
    setCustomFilter,
    backFirstPage,
    getCurrentNode,
    batchSetCustomFilter,
});
</script>

<style lang="less" scoped>
.main-center-entry {
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: stretch;
    position: relative;
    min-height: 0;
    .main-center-entry-left {
        flex: 1;
        min-width: 0;
        flex-shrink: 0;
        :deep(.table.paging-show) {
            overflow: hidden;
            .el-table {
                border: none;
                &.el-table--border {
                    &::before,
                    &::after {
                        width: 1px;
                        background-color: var(--table-border-color);
                    }
                }
                .el-table__inner-wrapper {
                    &:before,
                    &:after {
                        height: 1px;
                        background-color: var(--table-border-color);
                    }
                }
                tbody tr td .cell input[type="text"] {
                    border: none;
                }
                .vtname-select {
                    input {
                        border: none;
                    }
                }
            }
            .voucher-template-header {
                display: flex;
                align-items: center;
                .batch-update-template {
                    width: 16px;
                    height: 16px;
                    margin-left: 5px;
                    margin-top: 2px;
                }
            }
        }
        :deep(.pagination-info) {
            position: relative;
            line-height: 40px;
            .other-info {
                position: absolute;
                left: 0;
                top: -5px;
                text-align: left;
                padding-left: 0;
                line-height: 16px;
                .total {
                    display: inline-block;
                    width: auto;
                    white-space: nowrap;
                }
            }
        }
    }
    .main-center-entry-right {
        width: 222px;
        border: 1px solid #dfe4eb;
        border-radius: 4px;
        margin-left: 20px;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        position: relative;
        .function {
            position: absolute;
            top: 50%;
            left: -10px;
            width: 10px;
            height: 90px;
            background-image: url(@/assets/Icons/erp-close-tree.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            cursor: pointer;
            display: block;
            transform: translateY(-50%);
            &.expend {
                background-image: url(@/assets/Icons/erp-open-tree.png);
            }
        }
        .tool-tip {
            position: absolute;
            left: 0;
            top: 0;
            width: auto;
            max-width: 300px;
            box-sizing: border-box;
            border-radius: 2px;
            padding: 12px;
            background: var(--el-bg-color-overlay);
            border: 1px solid var(--el-border-color-light);
            line-height: 1.4;
            box-shadow: var(--el-box-shadow-light);
            word-break: break-all;
            text-align: left;
            z-index: 10;
            font-size: 14px;
            &::after {
                content: "";
                position: absolute;
                top: 9px;
                width: 10px;
                height: 10px;
                right: -4px;
                background-color: var(--el-bg-color-overlay);
                transform: rotate(45deg);
            }
        }
        .main-center-entry-right-buttons {
            background: #f1f5fc;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 22px;
            height: 25px;
            padding: 13px 24px 0 24px;
            border-bottom: 1px solid #dfe4eb;
            display: flex;
            flex-direction: row;
            width: 174px;
            justify-content: space-between;
            & :hover {
                cursor: pointer;
            }
            & .checked {
                border-bottom: 2px solid #3d7fff;
                color: #3d7fff;
            }
        }
        .main-center-entry-right-tree {
            overflow: auto;
            flex: 1;
            height: auto;
            margin-top: 0;
        }
        .tree-icon {
            display: inline-block;
            vertical-align: top;
            height: 21px;
            width: 21px;
            background-repeat: no-repeat;
            background-position-y: center;
            background-position-x: center;
        }
        .is-expanded {
            .tree-icon.tree-folder.tree-folder-open {
                background-image: url(@/assets/Icons/folder-open.png);
            }
        }
        .tree-icon.tree-folder.tree-folder-open {
            background-image: url(@/assets/Icons/folder.png);
        }
        .tree-icon.tree-file {
            background-image: url(@/assets/Icons/file-erp.png);
            background-size: 16px 18px;
        }
        .tree-title {
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 21px;
            display: inline-block;
        }
        :deep(.el-tree) {
            width: auto;
            min-width: 100%;
            display: inline-block;
            padding-right: 14px;
            padding-bottom: 10px;
            box-sizing: border-box;
            .is-current {
                & > .el-tree-node__content {
                    background-color: var(--table-title-color) !important;
                }
            }
            &.el-tree-all {
                > .el-tree-node > .el-tree-node__content {
                    background-color: transparent;
                }
                > .el-tree-node > .el-tree-node__content:active {
                    background-color: transparent;
                }
                > .el-tree-node > .el-tree-node__content:hover {
                    background-color: var(--table-title-color);
                }
                > .el-tree-node.is-root > .el-tree-node__content {
                    padding-left: 24px !important;
                    .el-icon {
                        display: none;
                    }
                }
            }
            .el-tree-node > .el-tree-node__content {
                .el-icon.el-tree-node__expand-icon {
                    svg {
                        display: none;
                    }
                    background-size: 15px 14px;
                    background-repeat: no-repeat;
                    background-position: center;
                    transform: rotate(0deg);
                    background-image: url(@/assets/Erp/plus.png);
                    &.expanded {
                        background-image: url(@/assets/Erp/minus.png);
                    }
                }
            }
        }
        &.expend {
            width: 0;
            border: none;
            > div:not(.function) {
                display: none;
            }
        }
    }
}
.batch-update-template-content {
    position: relative;
    .update-main {
        width: 250px;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        margin: 50px auto;
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.detail-tool-tip-content {
        position: absolute;
        top: 50px;
        background: var(--white);
        box-shadow: 0px 6px 18px 8px rgba(21, 27, 41, 0.05), 0px 6px 10px -4px rgba(21, 27, 41, 0.08);
        border-radius: 8px;
        width: 340px;
        text-align: left;
        z-index:9999;
        .detail-tool-title {
            padding: 20px 16px 16px 20px;
            color: var(--font-color);
            font-size: 13px;
            line-height: 18px;
        }
        .detail-tool-label {
            padding: 0 20px;
            font-size: 20px;
            color: #333333;
            line-height: 28px;
            margin-bottom: 4px;
            overflow: hidden;
        }
        .detail-tool-info {
            padding: 0 24px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 24px;
            height: 72px;
            margin-bottom: 20px;
            overflow: hidden;
            .detail-tool-info-line {
                display: flex;
                flex-direction: row;
                justify-content: start;
                line-height: 24px;
                .detail-tool-info-line-left {
                    width: 140px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
                .detail-tool-info-line-right {
                    margin-left: 30px;
                }
            }
        }
    }
.more-operate {
    display: flex;
    align-items: center;
}
.header-operate {
    .text {
        flex: 1;
    }
    .left {
        display: flex;
        align-items: center;
        flex: 1;
        .text {
            flex: none;
        }
        .batch-update-template {
            width: 16px;
            height: 16px;
            margin-left: 5px;
            margin-top: 2px;
        }
    }
}
.virtual-input {
    width: 0;
    height: 0;
    padding: 0;
    border: none;
    opacity: 0;
}
.select-content {
    margin: 10px 0;
    .top-search {
        position: relative;
        &:hover {
            .close {
                display: inline-flex;
            }
        }
    }
    .search-box {
        position: absolute;
        right: 10px;
        top: 7px;
        width: 18px;
        height: 18px;
        background: url("@/assets/Icons/search-erp.png") no-repeat center;
        background-size: 100% 100%;
    }
    .close {
        position: absolute;
        right: 32px;
        top: 50%;
        transform: translateY(-50%);
        display: none;
    }
    input {
        height: 30px;
        width: 160px;
        border: 1px solid var(--main-color);
        border-radius: 4px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 30px;
        padding: 0 42px 0 10px;
        outline: none;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .select-info {
        width: 212px;
        position: absolute;
        top: 82px;
        left: 5px;
        z-index: 9;
        box-sizing: border-box;
        height: 178px;
        border: 1px solid var(--border-color);
        background-color: var(--white);
        overflow-y: auto;
        text-align: left;
        .select-info-inner {
            text-align: left;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 16px;
            padding: 6px 6px 6px 8px;
            margin: 0;
            box-sizing: border-box;
            max-height: 40px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
            &.active {
                background: var(--border-color);
            }
        }
    }
}
</style>
