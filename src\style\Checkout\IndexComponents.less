@blue: #3177f4;
.create-period-check-tips {
    height: 52px;
    padding: 0 25px 0 0;
    margin-bottom: 2px;
    border-bottom: 1px solid var(--title-split-line);
    position: sticky;
    top: 48px;
    z-index: 10;
    background-color: var(--white);
    &.erp-tips {
        div.tip-item {
            span.circle {
                &.blue {
                    background: var(--green);
                }
            }
        }
    }
    div.tip-item {
        float: left;
        margin-left: 32px;
        margin-top: 17px;
        display: flex;
        align-items: center;
        span.circle {
            height: 8px;
            width: 8px;
            display: inline-block;
            border-radius: 4px;
            vertical-align: top;
            &.green {
                background: var(--main-color);
            }
            &.blue {
                background: @blue;
            }
            &.gray {
                background: var(--border-color);
            }
        }
        span.txt {
            display: inline-block;
            margin-left: 8px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }
    }
}
.tiles {
    overflow: hidden;
    text-align: left;
    padding: 2px 0 8px 16px;
    .tile-year {
        padding-left: 4px;
        color: var(--font-color);
        font-size: 32px;
        line-height: 32px;
        margin-top: 18px;
        margin-bottom: 20px;
    }
    .base-tile {
        width: 86px;
        border: 1px solid var(--weaker-font-color);
        display: inline-block;
        margin-left: 64px;
        margin-bottom: 22px;
        border-radius: 4px;
        &.canclick {
            cursor: pointer;
            color: var(--green);
            border-color: var(--green);
            &:hover {
                box-shadow: 0px 2px 4px 0px rgba(102, 100, 100, 0.24), 0px 2px 16px 0px rgba(199, 198, 198, 0.5);
            }
            .tile-main {
                color: var(--green);
            }
            .tile-head {
                background: var(--green);
            }
        }
        &.current {
            color: @blue;
            border-color: @blue;
            &:hover {
                box-shadow: 0px 2px 4px 0px rgba(102, 100, 100, 0.24), 0px 2px 16px 0px rgba(199, 198, 198, 0.5);
            }
            .tile-main {
                color: @blue;
            }
            .tile-head {
                background: @blue;
            }
        }
        &.locked {
            border-color: var(--border-color);
            .tile-head {
                background-color: var(--border-color);
                background-repeat: no-repeat;
                background-position: center;
                background-image: url("@/assets/Checkout/locked.png");
            }
            .tile-main {
                color: var(--border-color);
            }
        }
        &.canclick.locked {
            border-color: var(--weaker-font-color);
            &:hover {
                box-shadow: 0px 2px 4px 0px rgba(102, 100, 100, 0.24), 0px 2px 16px 0px rgba(199, 198, 198, 0.5);
            }
            .tile-head {
                background: var(--weaker-font-color);
            }
            .tile-main {
                color: var(--weaker-font-color);
            }
        }

        .tile-head {
            height: 21px;
            background-color: var(--weaker-font-color);
        }
        .tile-main {
            height: 65px;
            color: var(--weaker-font-color);
            font-size: 36px;
            line-height: 65px;
            text-align: center;
        }
    }

    &.erp {
        .base-tile {
            &.canclick {
                color: @blue;
                border-color: @blue;
                .tile-main {
                    color: @blue;
                }
                .tile-head {
                    background: @blue;
                }
            }
            &.current {
                color: var(--green);
                border-color: var(--green);
                .tile-main {
                    color: var(--green);
                }
                .tile-head {
                    background: var(--green);
                }
            }
            &.canclick.locked {
                border-color: var(--weaker-font-color);
                .tile-head {
                    background: var(--weaker-font-color);
                }
                .tile-main {
                    color: var(--weaker-font-color);
                }
            }
            &.green-locked {
                color: @blue;
                border-color: @blue;
                .tile-main {
                    color: @blue;
                }
                .tile-head {
                    background: @blue;
                }
            }
        }
    }
}
