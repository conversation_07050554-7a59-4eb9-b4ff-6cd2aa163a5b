<template>
    <div class="step-box">
        <div class="content-row">
            <div class="content-left width-five qr-left high">
                <div class="width-five">&nbsp;</div>
                <div class="width-five tx-left">
                    <span class="label-bold">将公众号推送给老板</span>
                    <span class="label-color mt-10" style="display: block">推荐给朋友 > 成功关注</span>
                </div>
            </div>
            <div class="content-right width-five mt-20">
                <img src="@/assets/Settings/3-1.png" class="boss-high-img" alt="3-1.png" />
                <img src="@/assets/Settings/3-2.png" class="boss-high-img" alt="3-2.png" />
            </div>
        </div>
        <div class="content-row tx-center bottom-operate">
            <a class="button mr-20" @click="reduceStage">上一步</a>
            <a class="button solid-button long" :class="isErp?'erp-long':''" @click="upgradeStage">已推荐关注，下一步</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import {ref} from "vue";
const emit = defineEmits(["reduce-stage", "upgrade-stage"]);
const reduceStage = () => emit("reduce-stage", 1);
const upgradeStage = () => emit("upgrade-stage", 2);
const isErp = ref(window.isErp)
</script>

<style lang="less" scoped>
@import "@/style/Settings/BossPermission.less";
</style>
