<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <HomePage
                        ref="HomePageRef"
                        @goAdd="createStatement"
                        @goPivotTable="newAddPivotTable"
                        @editStatementModel="editStatementModel"
                    ></HomePage>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center">
                    <SelectAddStatement
                        ref="SelectAddStatementRef"
                        @go-main="currentSlot = 'main'"
                        @preCreateNextStep="preCreateNextStep"
                    ></SelectAddStatement>
                </div>
            </template>
            <template #template>
                <div class="slot-content align-center">
                    <div style="width: 100%; height: auto; overflow: auto">
                        <CreateTemplate @go-add="currentSlot = 'add'" @go-generate="currentSlot = 'generate'"></CreateTemplate>
                    </div>
                </div>
            </template>

            <template #generate>
                <div class="slot-content align-center">
                    <GenerateStatement
                        ref="GenerateStatementRef"
                        @submitSuccess="generateSubmitSuccess"
                        @goMain="currentSlot = 'main'"
                    ></GenerateStatement>
                </div>
            </template>
            <template #pivotTable>
                <div class="slot-content align-center">
                    <PivotTableDesigner
                        ref="PivotTableDesignerRef"
                        @submitSuccess="generateSubmitSuccess"
                        @goMain="currentSlot = 'main'"
                    ></PivotTableDesigner>
                </div>
            </template>
            <template #viewPivotTable>
                <div class="slot-content">
                    <ViewPivotTable  @goMain="currentSlot = 'main'"></ViewPivotTable>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>
<script lang="ts">
export default {
    name: "CustomStatement",
};
</script>
<script setup lang="ts">
import { defineAsyncComponent, ref } from "vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import HomePage from "./components/HomePage.vue";
import CreateTemplate from "./components/CreateTemplate.vue";
import SelectAddStatement from "./components/SelectAddStatement.vue";
import GenerateStatement from "./components/GenerateStatement.vue";
import PivotTableDesigner from "./components/PivotTableDesigner.vue";
const ViewPivotTable = defineAsyncComponent(() => import("./components/ViewPivotTable.vue"));

const HomePageRef = ref<InstanceType<typeof HomePage>>();
const slots = ["main", "add", "template", "generate", "pivotTable", "viewPivotTable"];
const currentSlot = ref("main");

const SelectAddStatementRef = ref();
const createStatement = () => {
    currentSlot.value = "add";
    SelectAddStatementRef.value?.resetAddSelect();
};
const GenerateStatementRef = ref();
const PivotTableDesignerRef = ref();
const editStatementModel = (statementId: number, statementType: number, editable: boolean) => {
    currentSlot.value = statementType === 1 ? "generate" : editable ? "pivotTable" : "viewPivotTable";
    currentSlot.value === "pivotTable" && PivotTableDesignerRef.value?.setInitialData("edit");
};
const newAddPivotTable = () => {
    currentSlot.value = "pivotTable";
    PivotTableDesignerRef.value?.setInitialData("add");
};
const preCreateNextStep = (statementType: number) => {
    let nextView: { [key: number]: string } = {
        1: "template",
        2: "copyList",
        3: "importTemplate",
        4: "generate",
    };
    currentSlot.value = nextView[statementType];
};

const generateSubmitSuccess = () => {
    currentSlot.value = "main";
    HomePageRef.value?.getTableList();
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
</style>
