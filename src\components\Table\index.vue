<template>
    <div
        v-loading="loading"
        element-loading-text="正在加载数据..."
        class="table"
        :class="[
            {
                'paging-show': pageIsShow,
                'paging-hide': !pageIsShow,
                'custom-table-normal': useNormalScroll,
                'custom-table': !useNormalScroll,
                'addSub-table': hasAddSub,
            }
        ]"
        @mouseenter="cellMouseEnter"
        @mouseleave="typeShow = false"
    >
        <div
            class="add-line"
            @mouseenter="typeShow = true"
            @mouseleave="typeShow = false"
            :style="{ top: (canCopy ? copyTop : top) + 'px' }"
            v-if="typeShow && hasAddSub"
        >
            <div :class="{ 'erp-add-line-icon': isErp, 'add-line-icon': true }" @click="handleTableAdd"></div>
            <div v-if="canCopy" :class="{ 'erp-copy-line-icon': isErp, 'copy-line-icon': true }" @click="handleCopy"></div>
        </div>
        <div
            class="subtract-line"
            @mouseenter="typeShow = true"
            @mouseleave="typeShow = false"
            :style="{ top: top + 'px' }"
            @click="handleTableSubtract"
            v-if="typeShow && hasAddSub && customSubShow && setSubShow"
        >
            <div class="subtract-line-icon" :class="isErp ? 'erp-subtract-line-icon' : ''"></div>
        </div>
        <el-table
            ref="TableComponents"
            :size="size"
            :cell-class-name="cellClassName"
            :show-header="showHeader"
            :border="border"
            :data="hasAddSub ? editData : ((!isSubjectPage  && (data && data.length > hasVirtualCount)) ? virtuallyData : data)"
            :tree-props="treeProps"
            :fit="fit"
            :stripe="stripe"
            :max-height="maxHeight"
            :height="height"
            :row-key="rowKey"
            :expand-row-keys="expandRowKeys"
            :empty-text="customEmptyText"
            :highlight-current-row="highlightCurrentRow"
            :automatic-dropdown="false"
            :row-class-name="rowClassNameAll"
            :show-overflow-tooltip="showOverflowTooltip"
            :cell-style="cellStyle"
            :scrollbar-always-on="scrollbarShow"
            :header-row-class-name="setHeaderRowClass"
            :header-cell-class-name="setHeaderCellClass"
            @select="handleSelect"
            @selection-change="handleSelectionChange"
            @select-all="handleSelectAll"
            @cell-click="handleCellClick"
            @row-click="handleRowClick"
            @sort-change="handleSortChange"
            @cell-mouse-enter="cellMouseEnter"
            @cell-mouse-leave="cellMouseLeave"
            @row-dblclick="handleRowDblclick"
            :span-method="objectSpanMethod"
            :style="{ minHeight: minHeight ? minHeight : '0px' }"
            :tooltip-options="tooltipOptions"
            :show-summary="showSummary"
            :summary-method="summaryMethod"
            :class="!showHeader ? 'no-header' : ''"
            @header-dragend="headerDragend"
        >
            <template v-for="(colItem, index) in columns" :key="colItem.prop">
                <ColumnItem :ref="(el: any) => bindColumnRef(el, index)" :column="colItem" @filter-search="handleCustomFilter" @sort="handleCustomSort" >
                    <template v-for="slotItem in slotsArr" #[slotItem]="{ slotColumn }" :key="slotItem">
                        <slot :name="slotItem" :slotColumn="slotColumn"></slot>
                    </template>
                    <template #selection="{ slotColumn }">
                        <el-table-column
                            v-if="getVirtualFlag()"
                            type="default"
                            :width="slotColumn.width ?? 40"
                            :header-align="slotColumn.headerAlign ?? 'center'"
                            :align="slotColumn.align ?? 'center'"
                            class-name="selection-col"
                            :resizable="false"
                            :fixed="slotColumn.fixed ?? false"
                            :showOverflowTooltip="false"
                        >
                            <template #header>
                                <el-checkbox class="test" v-model="selectAllStatus" @change="handleSelectAllChange" />
                            </template>
                            <template #default="scope">
                                <el-checkbox
                                    v-if="selectable(scope.row, scope.$index)"
                                    v-model="scope.row[selectKey]"
                                    @click.stop
                                    @change="(check) => handleSelectChange(scope.row, check)"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column
                            v-else
                            type="selection"
                            :width="slotColumn.width ?? 40"
                            :header-align="slotColumn.headerAlign ?? 'center'"
                            :align="slotColumn.align ?? 'center'"
                            :selectable="selectable"
                            class-name="selection-col"
                            :reserve-selection="slotColumn.reserveSelection ?? false"
                            :resizable="false"
                            :fixed="slotColumn.fixed ?? false"
                        >
                        </el-table-column>
                    </template>
                </ColumnItem>
            </template>
            <template #append>
                <slot name="append"></slot>
            </template>
            <template #empty>
                <slot name="empty"></slot>
            </template>
        </el-table>
        <TablePagination
            v-if="pageIsShow"
            class="pagination"
            :size="size"
            :page-sizes="pageSizes"
            :page-size="pageSize"
            :layout="layout"
            :total="total"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
        >
            <template #pageOther>
                <slot name="pageOther"></slot>
             </template>
        </TablePagination>
    </div>
</template>

<script setup lang="ts">
import { ref, useSlots, onMounted, watch, onActivated, inject, onDeactivated, onBeforeUnmount, nextTick, computed } from "vue";
import type { FilterOrder, IColumnProps } from "./IColumnProps";
import ColumnItem from "./ColumnItem.vue";
import TablePagination from "./TablePagination.vue";
import type { CellCls, CellStyle, ElTable } from "element-plus/es/components";
import { ElNotify } from "@/util/notify";
import { pageScrollKey } from "@/symbols";
import Sortable from "sortablejs";
import { saveColWidth } from "@/components/ColumnSet/utils";
import { getOffsetTop, getSize, getSEPos } from "@/components/Table/VirtualScroll";
import type { SortType } from "./utils";

const hasVirtualCount = 310;
const isErp = ref(window.isErp);
const props = withDefaults(
    defineProps<{
        loading?: boolean;
        data?: Array<any>;
        columns?: Array<IColumnProps>;
        pageIsShow?: boolean;
        pageSizes?: Array<number>;
        pageSize?: number;
        layout?: string;
        total?: number;
        border?: boolean;
        stripe?: boolean;
        fit?: boolean;
        size?: any;
        rowKey?: any;
        expandRowKeys?: any;
        treeProps?: Object;
        currentPage?: number;
        maxHeight?: number | string;
        minHeight?: string;
        emptyText?: string;
        height?: number | string;
        hearderRowStyleName?: string;
        showOverflowTooltip?: boolean;
        showHeader?: boolean;
        cellClassName?: CellCls<any>;
        cellStyle?: CellStyle<any>; // string | Function
        rowClassName?: any; // string | Function
        objectSpanMethod?: any; // Function
        hasAddSub?: boolean;
        // 表格有左加右减功能,根据哪个字段来判断当前行
        addSubField?: string;
        // 添加行添加的对象
        addRowData?: Object;
        // 加减按钮可否连续点击
        continuousClick?: boolean;
        // 限制多少行不可以删除（表格必须保留前notDeleteRow几行）
        notDeleteRow?: number;
        notDeleteNotifyText?: string;
        // 表格有滚动条时，最大的行数
        lastLengthRow?: number;
        // expandRowKeys?: Array<string>;
        headerSelectNone?: boolean;
        scrollbarShow?: boolean;
        tooltipOptions?: any;
        tableData?: Array<any>;
        // 是否为设置期初表格
        initialBalance?: boolean;
        highlightCurrentRow?: boolean;
        useNormalScroll?: boolean;
        // 自定义加减事件
        customAdd?: boolean;
        customSubtract?: boolean;
        selectable?: (row: any, index: number) => boolean;
        firstRowNotShow?: boolean;
        lastRowNotShow?: boolean;
        showSummary?: boolean;
        summaryMethod?: (params: { columns: any; data: any }) => any;
        customPageStartEnd?: number[];
        customSubShowIndex?: number;
        lockLine?: number;
        tableName?: string; //列宽存浏览器的表格名
        isSubjectPage?: boolean; //科目页面
        canCopy?: boolean; // 复制
        isAddInvoiceTop?: boolean; //发票新增明细滚动置顶
    }>(),
    {
        loading: false,
        data: () => [],// Lmtips:不排除有null的情况,未做兼容处理
        columns: () => [],
        pageIsShow: false,
        pageSizes: () => [1, 2, 10, 20, 30],
        pageSize: () => 20,
        layout: () => "total, sizes, prev, pager, next, jumper",
        total: () => 0,
        border: true,
        stripe: true,
        fit: true,
        rowKey: "",
        expandRowKeys:() => [],
        size: "default",
        treeProps: () => ({}),
        currentPage: () => 1,
        hearderRowStyleName: "",
        showHeader: true,
        cellClassName: "",
        rowClassName: "",
        // 数据加载中...
        emptyText: " ",
        objectSpanMethod: () => {},
        hasAddSub: false,
        addSubField: "",
        continuousClick: false,
        notDeleteRow: () => 0,
        notDeleteNotifyText: "",
        lastLengthRow: () => 9999,
        // expandRowKeys: () => [],
        // 表格头部select框不显示传值为true
        headerSelectNone: false,
        scrollbarShow: true,  //滚动条一直显示
        tooltipOptions: () => ({ effect: "light", placement: "right-start", offset: -10 }),
        // 是否为期初表格
        initialBalance: false,
        highlightCurrentRow: true,
        useNormalScroll: false,
        customSubtract: false,
        customAdd: false,
        showOverflowTooltip: true,
        selectable: (row: any, index: number): boolean => {
            return true;
        },
        firstRowNotShow: false,
        lastRowNotShow: false,
        showSummary: false,
        summaryMethod: (params: { columns: any; data: any }) => {},
        lockLine: -1,
        tableName: "",
        isSubjectPage: false,
        canCopy: false,
        isAddInvoiceTop: false,
    }
);
// 数据加载中...
const customEmptyText = ref(" ");
watch(
    () => props.emptyText,
    (val) => {
        if (val.trim()) {
            customEmptyText.value = val;
        } else {
            customEmptyText.value = " ";
        }
    },
    { immediate: true }
);
// 单元格高度
const cellTop = ref<number>();
const cellLeft = ref<number>();

const TableComponents = ref<InstanceType<typeof ElTable>>();

const emit = defineEmits<{
    (e: "select", selection: any, row: any): void;
    (e: "selection-change", value: any): void;
    (e: "select-all", value: any): void;
    (e: "row-click", row: any, column: any, event: any): void;
    (e: "row-dblclick", row: any, column: any, event: any): void;
    (e: "size-change", value: number): void;
    (e: "current-change", value: number): void;
    (e: "sort-change", column: any, prop: any, order: any): void;
    (e: "cell-click", row: any, column: any, cell: any, event: any): void;
    //(e: "get-row-info", $event: MouseEvent, row: any): void;
    (e: "table-add-or-subtract", val: any): void;
    // (e: "table-subtract", val: any): void;
    (e: "refresh"): void;
    // 单元格鼠标移入事件
    (e: "cell-mouse-enter-qichu", index: number, top: number, left: number, cellHeight: number, tableRightDistance: number): void;
    (e: "cell-mouse-enter", row: any, column: any, cell: any, event: any): void;
    (e: "cell-mouse-leave"): void;
    (e: "handleSubtract", index: number, key?: any): Boolean;
    (e: "handleAdd", index: number): Boolean;
    (e: "scroll", scrollTop?: number): void;
    //列拖拽
    (e: "cell-drop", oldIndex: number, newIndex: number): void;
    (e: "move-drop", oldIndex: number, newIndex: number, scrollTop: number, evt:any, originScrollTop: number): void;
    (e: "agin-column-drop"): void;
    (e: "save-header-dragend", width: number, prop: string): void;
    (e: "header-dragend", newWidth: number, oldWidth: number, column: any): void;
    (e: "custom-sort", prop: string, sortType: SortType): void;
    (e: "custom-filter", prop: string, value: any, filterOrder: FilterOrder): void;
    (e: "copy", index: number): void;
}>();
const stripe = ref(props.stripe);
const rowClassNameAll = ({ row, rowIndex }: any) => {
    let isSelected = selectData.value && selectData.value.indexOf(row) > -1;
    if (isSelected) {
        stripe.value = false; //斑马纹和选中样式冲突
    }
    let propsClassName = "";
    if (typeof props.rowClassName == "function") {
        propsClassName = props.rowClassName({ row, rowIndex }) ?? "";
    } else {
        propsClassName = props.rowClassName;
    }
    return isSelected ? "row-selected" + " " + propsClassName : propsClassName;
};
const selectData = ref<Array<any>>([]);
const handleSelect = (selection: any, row: any) => {
    emit("select", selection, row);
};
const handleSelectionChange = (val: any) => {
    selectData.value = val;
    emit("selection-change", val);
};
const handleSelectAll = (val: any) => {
    emit("select-all", val);
};
const handleSizeChange = (val: any) => {
    emit("size-change", val);
};
const handleCurrentChange = (val: any) => {
    emit("current-change", val);
};
const handleRerefresh = () => {
    emit("refresh");
};
const handleRowClick = (row: any, column: any, event: any) => {
    emit("row-click", row, column, event);
};
const handleRowDblclick = (row: any, column: any, event: any) => {
    emit("row-dblclick", row, column, event);
};
const handleSortChange = (column: any, prop: any, order: any) => {
    emit("sort-change", column, prop, order);
};
const handleCellClick = (row: any, column: any, cell: any, event: any) => {
    emit("cell-click", row, column, cell, event);
};
function handleCustomFilter(prop: string, value: any, filterOrder: FilterOrder) {
    emit("custom-filter", prop, value, filterOrder);
}
function handleCustomSort(prop: string, sortType: SortType) {
    emit("custom-sort", prop, sortType);
}

const savedScrollPosition = ref(0); // 保存的滚动条位置
const scrollTop = ref(0);

const saveScrollPosition = (left: number) => {
    savedScrollPosition.value = left;
};

watch([() => props.currentPage], () => {
    setScrollLeft(savedScrollPosition.value);
});
const handleTableAdd = () => {
    typeShow.value = props.continuousClick;
    if (props.customAdd) {
        emit("handleAdd", addSubRow.value[props.addSubField]);
        return;
    }
    let newRow = { ...props?.addRowData, index: editData.value.length };
    let addIndex = editData.value.findIndex((v: any) => v[props.addSubField] === addSubRow.value[props.addSubField]);
    editData.value.splice(addIndex + 1, 0, newRow);
    editData.value.forEach((item: any, index: number) => {
        editData.value[index][props.addSubField] = index;
    });
    emit("table-add-or-subtract", editData.value);
};
function handleCopy() {
    typeShow.value = false;
    let index = 0;
    if (!props.addSubField) {
        index = editData.value.findIndex((v: any) => v === addSubRow.value);
    } else {
        index = editData.value.findIndex((v: any) => v[props.addSubField] === addSubRow.value[props.addSubField]);
    }
    emit("copy", index);
}
const handleTableSubtract = () => {
    typeShow.value = props.continuousClick;
    let deleteIndex = 0;
    if(!props.addSubField){
        deleteIndex = editData.value.findIndex((v: any) => v === addSubRow.value);
    }else{
        deleteIndex = editData.value.findIndex((v: any) => v[props.addSubField] === addSubRow.value[props.addSubField]);
    }
    if (props.customSubtract) {
        emit("handleSubtract", deleteIndex, addSubRow.value[props.addSubField]);
        typeShow.value = deleteIndex !== editData.value.length;
        return;
    }
    // 只剩前notDeleteRow行禁止删除,但是点击要清空数据
    if (props.notDeleteRow > 0 && deleteIndex < props.notDeleteRow && editData.value.length === props.notDeleteRow) {
        editData.value[deleteIndex] = { ...props?.addRowData, index: deleteIndex };
        emit("table-add-or-subtract", editData.value);
        if (props.notDeleteNotifyText) {
            ElNotify({
                type: "warning",
                message: props.notDeleteNotifyText,
            });
        }
        return;
    }

    editData.value.splice(deleteIndex, 1);
    editData.value.forEach((item: any, index: number) => {
        item[props.addSubField] = index;
    });
    typeShow.value = deleteIndex !== editData.value.length;
    if (props.lastRowNotShow) {
        typeShow.value = deleteIndex !== editData.value.length - 1;
    }
    if(props.customSubShowIndex !== undefined ){
        customSubShow.value = deleteIndex !== editData.value.length - 2 && deleteIndex !== editData.value.length - 1;
    }
    emit("table-add-or-subtract", editData.value);
};
const cellMouseEnter = (row: any, column: number, cell: any, event: any) => {
    if (props.initialBalance) {
        // 当前数据在数组中的位置index,算出top是多少
        let index = props.data.findIndex((v: any) => v["asubCode"] === row["asubCode"]);
        //         const aboveRowIndex = index - 1;
        //   // 获取上方单元格的元素
        //   const tableBody = cell.closest('.el-table__body');
        //   const aboveRow = tableBody.rows[aboveRowIndex];
        //   const aboveCell = aboveRow.cells[index];

        //   // 获取上方单元格的高度
        //   const aboveCellHeight = aboveCell.offsetHeight;

        // 实际有1px边框
        // cellTop.value = ((document.querySelector(".el-table__row >.el-table__cell") as Element).clientHeight + 1) * (index + 1);
        cellTop.value = event.clientY as number;
        cellLeft.value = event.clientX;
        const cellHeight = cell.offsetHeight;
        const tableRect = TableComponents.value?.$el.getBoundingClientRect();
        const tableRightDistance = tableRect.right - event.clientX;
        emit("cell-mouse-enter-qichu", index, cellTop.value, cellLeft.value || 0, cellHeight, tableRightDistance);
    }
    if (props.hasAddSub) {
        addSubRow.value = row;
        const tableOffset = TableComponents.value?.$el.getBoundingClientRect();
        if (!event || !event.currentTarget) return;
        const rowOffset = event.currentTarget.getBoundingClientRect();
        const offsetY = rowOffset.top - tableOffset.top;
        // 当前行高度
        const cellHeight = event.currentTarget.offsetHeight;
        const translateY = isErp.value && props.canCopy ? 20 : 8;
        top.value = offsetY + cellHeight / 2 - 8;
        copyTop.value = offsetY + cellHeight / 2 - translateY;
        // 第一个是根据这个数据再数组中的位置index,算出top是多少
        let index = props.data.findIndex((v: any) => v[props.addSubField] === row[props.addSubField]);
        if (props.lastLengthRow) {
            index = index >= props.lastLengthRow - 1 ? props.lastLengthRow - 1 : index;
        }
        if ((props.firstRowNotShow && index === 0)) {
            typeShow.value = true;
            setSubShow.value = false;
        } else {
            typeShow.value = true;
            setSubShow.value = true;
        }
        if (props.lastRowNotShow ) {
            typeShow.value = index !== props.data.length - 1;
        }
        //有些没有field,判断不到index
        const rowIndex = event.target.closest("tr").rowIndex;
        if(props.customSubShowIndex !== undefined && props.customSubShowIndex === rowIndex && editData.value.length === 1){
            customSubShow.value = false
        }else{
            customSubShow.value = true
        }
    }
    emit("cell-mouse-enter", row, column, cell, event);
};

const cellMouseLeave = () => {
    emit("cell-mouse-leave");
};
// 加减标识是否显示
let typeShow = ref(false);
const customSubShow = ref(true);
// 该变量针对日记账凭证模版设置，后续看看是否可和customSubShow合并
const setSubShow = ref(true);
// 计算加减按钮位置
let top = ref();
const copyTop = ref(0);
let addSubRow = ref();
let editData = ref(props.data);

//是否开启虚拟滚动
function getVirtualFlag() {
    return !props.hasAddSub && !props.isSubjectPage && (props.data.length > hasVirtualCount);
}

watch(
    () => props.data,
    (val) => {
        editData.value = val;
        if (getVirtualFlag()) {
            updateView();
            clearSelection();
        } else if(!props.isSubjectPage) { //科目数据那里有自己的滚动，排除
            const virtualBody = (TableComponents.value?.$refs.tableBody as HTMLElement).parentNode as HTMLElement;
            virtualBody && (virtualBody.style.height = "");
            const tableBody = TableComponents.value?.$refs.tableBody as HTMLElement;
            tableBody && (tableBody.style.transform = "");
        }
        if (props.pageIsShow) { //有分页条
            if (props.tableName === "addInvoice" && !props.isAddInvoiceTop) return;
            //表格内部滚动到顶部
            setScrollTop(0);
        }
    }
);
const initEditData = (tableData: any[]) => {
    editData.value = tableData;
};

const slots = useSlots();
const slotsArr = Object.keys(slots);

function setHeaderRowClass() {
    let result = "";

    if (props.hearderRowStyleName) {
        result = props.hearderRowStyleName;
    }
    if (props.headerSelectNone) {
        result += " header-select-none";
    }
    if (props.hasAddSub) {
        result += " header-has-add-sub";
    }
    return result;
}

function setHeaderCellClass(scope: { row: any; column: any }) {
    let result = "";

    if (scope.column.showOverflowTooltip) {
        result += "show_overflow_tooltip";
    }

    return result;
}

function getTable() {
    return TableComponents.value;
}

// 修改某一行默认选中状态
const toggleRowSelect = (row: number, selected: boolean) => {
    TableComponents.value?.toggleRowSelection(props.hasAddSub ? editData.value[row] : props.data[row], selected);
};

// 滚动到指定坐标
const tableScrollTo = (x: number, y: number) => {
    TableComponents.value?.scrollTo(x, y);
};

const setScrollLeft = (scrollLeft: number) => {
    TableComponents.value?.setScrollLeft(scrollLeft);
};
const clearSelection = () => {
    if (getVirtualFlag()) {
        mapData.value.forEach((item) => (item[selectKey] = false));
        selectAllStatus.value = false;
        selectData.value = [];
        emit("selection-change", []);
        return;
    }
    TableComponents.value?.clearSelection();
};
const setScrollTop = (val: number) => {
    TableComponents.value?.setScrollTop(val);
};
const isDragStart = ref(false);
const isDragEnd = ref(false);
const isDragEvt = ref();
//双击双竖线
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    emit("header-dragend", newWidth, oldWidth, column);
    let elements = document.querySelectorAll(`.${column.id} .cell`);
    let max = elements[0].scrollWidth;
    elements.forEach((item) => {
        if (item.scrollWidth > max) {
            max = item.scrollWidth;
        }
    });
    if (newWidth === oldWidth && column.width < max) {
        column.width = max + 16; //单元格左右有8内边距
    }
    if (column.width < 50) { //最小
        column.width = 50;
    }
    if (!column.resizable) {
        column.width = oldWidth;
        return;
    }
    //列宽拖动保存浏览器
    if (props.tableName) {
        saveColWidth(props.tableName, column.width, column.property);
        //资金报表、资产部门调整
        if (props.tableName.includes("CashReport") || props.tableName === "AssetsDepartmentChange") {
            emit("save-header-dragend", column.width, column.property);
        }
    }
    //误触发拖拽开始，手动结束
    if (isDragStart.value) {
        endDragManually(isDragEvt.value);
        emit("agin-column-drop");
    }
};

function resetAllHeaderSort() {
    const header = TableComponents.value?.$refs.headerWrapper as HTMLElement;
    if (!header) return;
    const sortItems = header.querySelectorAll(".header-caret");
    if (!sortItems || sortItems.length === 0) return;
    for (let i = 0; i < sortItems.length; i++) {
        sortItems[i].classList.remove("ascending");
        sortItems[i].classList.remove("descending");
    }
}
const columnItemRefList = ref<Array<InstanceType<typeof ColumnItem> | null>>([]);
function bindColumnRef(el: any, index: number) {
    if (el) {
        columnItemRefList.value[index] = el;
    }
    return columnItemRefList.value[index];
}
function resetAllHeaderFilter() {
    columnItemRefList.value?.forEach((item) => {
        item?.resetFilter();
    });
}
function setCustomFilter(prop: string, filterOrder: FilterOrder, value: any) {
    if (!filterOrder) return;
    const index = props.columns.findIndex((item) => item.prop === prop);
    if (index === -1) return;
    columnItemRefList.value[index]?.setCustomFilter(prop, filterOrder, value);
}
defineExpose({
    getTable,
    initEditData,
    toggleRowSelect,
    tableScrollTo,
    clearSelection,
    setScrollTop,
    scrollTop,
    columnDrop,
    resetAllHeaderSort,
    resetAllHeaderFilter,
    setCustomFilter,
    rowDrop
});

watch(
    () => props.loading,
    (val) => {
        if (!val && props.data.length === 0) {
            customEmptyText.value = "暂无数据";
        } else if (val && props.data.length === 0) {
            // 数据加载中...
            customEmptyText.value = " ";
        } else {
            customEmptyText.value = "  ";
        }
    }
);

// 切换页面滚动到顶部
watch(
    [() => props.currentPage, () => props.pageSize],
    () => {
        // router-container   页面滚动到顶部
        const routerContainer = document.querySelector(".router-container");
        routerContainer?.scrollTo(0, 0);
        //表格内部滚动到顶部
        setScrollTop(0);
    }
);
const pageScroll = inject(pageScrollKey);
function checkTableIsInDialog() {
    let isInDialog = false;
    let node = TableComponents.value?.$el?.parentNode;
    while (node && node !== document.body) {
        if (node.classList && node.classList.contains("el-overlay")) {
            isInDialog = true;
            break;
        }
        node = node.parentNode;
    }
    return isInDialog;
}

function handleScroll(e: any) {
    pageScroll && !checkTableIsInDialog() && pageScroll(e.target.scrollTop > 0, () => {
        e.target.scrollTop > 0 && setScrollTop(0);
    });
    emit("scroll", e.target.scrollTop);
    if (getVirtualFlag()) {
        updateView();
    }
    saveScrollPosition(e.target.scrollLeft);
    scrollTop.value = e.target.scrollTop;
}
function findScrollDom() {
    return (TableComponents.value?.$refs.tableWrapper as HTMLElement)?.getElementsByClassName(
        "el-scrollbar__wrap"
    )[0] as HTMLElement;
}
//列拖拽
let sortableInstance: Sortable | null = null; 
function columnDrop(el: any, columnData: Array<any>, showAll:boolean = false) {
    // 增加索引以备排序(低版本浏览器fixed相同的，会不按原本顺序)  
    for (let i = 0; i < columnData.length; i++) {  
        columnData[i]["no"] = i;  
    }
    columnData.sort((a, b) => {
        const isAFixedLeft = a.fixed === 'left';  
        const isBFixedLeft = b.fixed === 'left';  
        if (isAFixedLeft && !isBFixedLeft) return -1;  
        if (!isAFixedLeft && isBFixedLeft) return 1;   
        if (isAFixedLeft === isBFixedLeft) {
            return a.no - b.no;
        }   
        return 0; 
    });
    for (let j = 0; j < columnData.length; j++) {  
        delete columnData[j].no;  
    } 
    const targetTr = el.querySelector(".el-table__header-wrapper tr") as HTMLElement; 
    const wrap = el.querySelector(".el-scrollbar__wrap") as HTMLElement; 
    let fixedLeftWidth = 0;
    let fixedRightWidth = 0;
    let leftList: any[] = [];
    let ths:HTMLElement[] = [];
    
    if (!targetTr) return;
    if (sortableInstance && sortableInstance.el) {  
        sortableInstance.destroy();  
    }   
    sortableInstance = Sortable.create(targetTr, {  
        disabled: showAll,  
        handle: ".colCellMove", 
        animation: 150, 
        delay: 0, //可以开始拖动的延迟时间
        direction: "horizontal",
        dropBubble: true,
        chosenClass: "cellMoveStyle", // 设置拖动时的样式类
        scroll: wrap, 
        scrollSensitivity: 30, //具体边缘的位置
        scrollSpeed: 10, // 设置滚动速度为 
        bubbleScroll: false, 
        onStart: function (evt) {  
            //拖动列宽时触发拖拽开始标志
            isDragStart.value = true;
            isDragEnd.value = false;
            isDragEvt.value = evt;
            
            leftList = [];
            ths = targetTr.querySelectorAll(".colCellMove") as unknown as HTMLElement[];
            ths.forEach((th, index) => { 
                if (th.style.left) {
                    leftList.push({no: index, value: th.style.left});
                } 
            }); 
        },
        onMove: function (evt, originalEvent) {
            //列冻结之间移动会丢失表头，清空 left
            if (savedScrollPosition.value === 0) {
                ths.forEach((th) => { 
                    th.style.left = '';  
                }); 
            }  
        }, 
        onEnd: function (evt) { 
            getBackStyle();
            if (isDragEnd.value) return;
            let oldIndex = evt.oldIndex as number;
            let newIndex = evt.newIndex as number;

            const isMoving = () => {
                return (columnData[oldIndex].fixed && !columnData[newIndex].fixed) 
                        || (columnData[newIndex].fixed && !columnData[oldIndex].fixed);
            }
            if (columnData[newIndex].fixed && (columnData[newIndex].slot === "selection" || columnData[newIndex].slot === "select")) {
                restOriginDom();
                ElNotify({
                    type: "warning",
                    message: "勾选框列位置不可更改",
                });
                return;
            }
            if (columnData[newIndex].fixed && columnData[newIndex].slot === "operation") {
                restOriginDom();
                ElNotify({
                    type: "warning",
                    message: "操作列位置不可更改",
                });
                return;
            }
            if (isMoving()) {
                restOriginDom();
                ElNotify({
                    type: "warning",
                    message: "列冻结需在非列冻结之前",
                });
                return;
            }
            nextTick(() => {
                emit("cell-drop", oldIndex, newIndex);
            })
            function getBackStyle() {
                nextTick(() => {
                    ths.forEach((th, index) => { 
                        leftList.forEach((item: any) =>{
                            if(item.no === index) {
                                th.style.left = item.value;  
                            }
                        })
                    }); 
                })
            }
            function restOriginDom() {
                const tagName = evt.item.tagName;
                const items = evt.from.getElementsByTagName(tagName);
                if (oldIndex > newIndex) {
                    evt.from.insertBefore(evt.item, items[oldIndex + 1]);
                } else {
                    evt.from.insertBefore(evt.item, items[oldIndex]);
                }
            }
        }
    }); 
}
// 手动结束拖拽的函数  
function endDragManually(evt: any) {  
    const manuallyEvt = {  
        from: evt.from,  
        item: evt.item,  
        oldIndex: evt.oldIndex,  
        newIndex: evt.oldIndex, // 位置保持不变  
    }; 
    if (sortableInstance) {
        isDragEnd.value = true;
        sortableInstance.options.onEnd(manuallyEvt); 
        manuallyEvt.item.classList.remove("cellMoveStyle");
        sortableInstance.destroy(); 
    }  
} 

//行拖拽（日记账）
function rowDrop(el: any) {
    const tbody = el.querySelector(".el-table__body-wrapper tbody") as HTMLElement;
    const wrap = el.querySelector(".el-scrollbar__wrap") as HTMLElement;
    let originScrollTop = 0;
    let startIndex = -1;
    let recordIndex = -1;
    let topDistance = 5;
    let bottomDistance = 2;
    Sortable.create(tbody, {
        disabled: false, //指示是否禁用了 sortable 功能
        animation: 150, //排序时的动画时长
        delay: 0, //可以开始拖动的延迟时间
        handle: ".row-move", //指定拖动手柄的选择器，只有该选择器元素被拖动时才能移动整个列表项
        direction: "vertical",
        dropBubble: false,
        scroll: wrap,   
        scrollSensitivity: 30, //具体边缘的位置
        scrollSpeed: 10, // 设置滚动速度为 
        bubbleScroll: true,
        sort: true,
        onStart: (evt) => {
            originScrollTop = scrollTop.value;
            startIndex = evt.oldIndex as number;
        },
        onMove: (evt, originalEvent) => {
            let newIndex = recordIndex;
            let endPosition = (evt as any).originalEvent.clientY;
            const items = Array.from(evt.from.children); // 获取所有子元素
            const tbodyStart = items[0]?.getBoundingClientRect().top || 0;
            if (endPosition < (tbodyStart + topDistance)) {
                newIndex = 0;
            } else {
                items.forEach((item, index) => {
                    item.classList.remove("is-moving");
                    const rect = item.getBoundingClientRect();
                    if (endPosition > (rect.top + topDistance) && endPosition < (rect.bottom - bottomDistance)) {
                        newIndex = index;
                    } else if (
                        endPosition > rect.top 
                        && endPosition < (rect.top + topDistance) 
                        && (recordIndex - 1) === (index - 1) 
                        && (recordIndex - 1) === startIndex
                    ) {
                        newIndex = startIndex;
                    } else if (
                        endPosition > (rect.bottom - bottomDistance) 
                        && endPosition < rect.bottom 
                        && (recordIndex + 1) === (index + 1) 
                        && (recordIndex + 1) === startIndex
                    ) {
                        newIndex = startIndex;
                    }
                });
            }
            if (items[newIndex]) {
                items[newIndex].classList.add("is-moving");
                recordIndex = newIndex;
            }
            return false;
        },
        onEnd: (evt) => { 
            let oldIndex = evt.oldIndex as number;
            let endPosition = (evt as any).originalEvent.clientY;
            // 计算新的下标,默认新下标为旧下标
            let newIndex = oldIndex;
            const items = Array.from(evt.from.children); // 获取所有子元素
            items.forEach((item, index) => {
                item.classList.remove("is-moving");
                const rect = item.getBoundingClientRect();
                if (endPosition > (rect.top + topDistance) && endPosition < (rect.bottom - bottomDistance)) {
                    newIndex = index;
                }
            });
            if (oldIndex === newIndex) return;
            emit("move-drop", oldIndex, newIndex, scrollTop.value, evt, originScrollTop);
        },
    });
}

// 计算固定区域宽度的函数  
function calculateFixedWidth(elements: HTMLElement[]): number {  
    return Array.from(elements).reduce((total, element) => total + element.clientWidth, 0);  
}
onMounted(() => {
    const scrollDom = findScrollDom();
    scrollDom && scrollDom.addEventListener("scroll", handleScroll);
    if (getVirtualFlag()) {
        window.addEventListener("resize", updateView);
    }
});
onBeforeUnmount(() => {
    const scrollDom = findScrollDom();
    scrollDom && scrollDom.removeEventListener("scroll", handleScroll);
    if (getVirtualFlag()) {
        window.removeEventListener("resize", updateView);
    }
});
onActivated(() => {
    const timer = setTimeout(() => {
        pageScroll && !checkTableIsInDialog() && pageScroll(scrollTop.value > 0, () => {
            scrollTop.value > 0 && setScrollTop(0);
        });
        clearTimeout(timer);
    })
    setScrollTop(scrollTop.value);
    setScrollLeft(savedScrollPosition.value);
});
onDeactivated(() => {
    pageScroll && !checkTableIsInDialog() && pageScroll(false, () => {});
})

//虚拟勾选框
const onlyKey = computed(() => props.rowKey || (props.data.length > hasVirtualCount ? "onlyKey" : ""));
const selectKey = "customSelect";
const mapData = computed(() =>
    props.data.map((item, index) => ({ 
        ...item, 
        [onlyKey.value]: props.rowKey ? item[props.rowKey] : index, 
        [selectKey]: false
    }))
);
const selectAllStatus = ref(false);
const handleSelectAllChange = (val: any) => {
    const selection: any[] = [];
    for (let i = 0; i < mapData.value.length; i++) {
        mapData.value[i][selectKey] = !!val && props.selectable(mapData.value[i], i);
        if (mapData.value[i][selectKey]) {
            selection.push(mapData.value[i]);
        }
    }
    handleVirtualSelectionChange();
    if (!val) {
        emit("select-all", []);
        return;
    }
    emit("select-all", selection);
};
const handleSelectChange = (row: any, val: any) => {
    row[selectKey] = !!val;
    const selection: any[] = [];
    let allCheck = true;
    for (let i = 0; i < mapData.value.length; i++) {
        if (mapData.value[i][selectKey]) {
            selection.push(mapData.value[i]);
        } else if (props.selectable(mapData.value[i], i)) {
            allCheck = false;
        }
    }
    selectAllStatus.value = allCheck;
    handleVirtualSelectionChange();
    emit("select", selection, row);
};
const handleVirtualSelectionChange = () => {
    const selection = mapData.value.filter((item) => item[selectKey]);
    selectData.value = selection;
    emit("selection-change", selection);
};

//虚拟滚动
const rowHeight = window.isErp ? 44 : 37;
const virtuallyData = ref<any[]>([]);

const sizes = ref<any>({});
const buffer = 500;
const start = ref(0);
const end = ref(0);
let timer: any = null;
const offsetMap = computed(() => {
    const res: any = {};
    let total = 0;
    for (let i = 0; i < props.data.length; i++) {
        const key = mapData.value[i][onlyKey.value];
        res[key] = total;
        const curSize = sizes.value[key];
        const size = typeof curSize === "number" ? curSize : rowHeight;
        total += size;
    }
    return res;
});
const renderVirtualTable = (shouldUpdate = true) => {
    updateSizes();
    calcRenderData();
    calcPosition();
    shouldUpdate && updatePosition();
};
const updateSizes = () => {
    const tableBody = TableComponents.value?.$refs.tableBody as HTMLElement;
    const rows = tableBody?.querySelectorAll(".el-table__row") as unknown as any[];
    if (rows) {
        Array.from(rows).forEach((row, index) => {
            const item = virtuallyData.value[index];
            if (!item) return;
            const key = item[onlyKey.value];
            const offsetHeight = row.offsetHeight;
            if (sizes.value[key] !== offsetHeight) {
                sizes.value[key] = offsetHeight;
            }
        });
    }
};
const calcRenderData = () => {
    const scroller = findScrollDom();
    const thisTop = scroller.scrollTop - buffer;
    const thisBottom = scroller.scrollTop + scroller.offsetHeight + buffer;

    const result = getSEPos(thisTop, thisBottom, props.data, mapData.value, offsetMap.value, onlyKey.value);

    start.value = result.thisStart;
    end.value = result.thisEnd;
    virtuallyData.value = mapData.value.slice(result.thisStart, result.thisEnd + 1);
};
const calcBodyHeight = () => {
    const last = props.data.length - 1;
    const wrapHeight = getOffsetTop(last, mapData.value, offsetMap.value, onlyKey.value) + getSize(last, virtuallyData.value, sizes.value, onlyKey.value, rowHeight);
    const el = TableComponents.value?.$refs.bodyWrapper as any;
    if (!el) return;
    const virtualBody = (TableComponents.value?.$refs.tableBody as HTMLElement).parentNode as HTMLElement;
    virtualBody && (virtualBody.style.height = wrapHeight + "px");
};
const calcPosition = () => {
    const el = TableComponents.value?.$refs.bodyWrapper as any;
    if (!el) return;
    const offsetTop = getOffsetTop(start.value, mapData.value, offsetMap.value, onlyKey.value);
    const tableBody = TableComponents.value?.$refs.tableBody as HTMLElement;
    tableBody && (tableBody.style.transform = `translateY(${offsetTop}px)`);
    calcBodyHeight();
};
const updatePosition = () => {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
        timer && clearTimeout(timer);
        renderVirtualTable(false);
    }, 100);
};
const updateView = () => {
    renderVirtualTable();
};

</script>

<style lang="less" scoped>
.addSub-table {
    .add-line {
        width: 18px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .subtract-line {
        width: 18px;
    }
}
.table {
    position: relative;
    .add-line {
        position: absolute;
        left: -18px;
        z-index: 9999;
    }
    .add-line-icon, .copy-line-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        cursor: pointer;
    }
    .add-line-icon {
        background-image: url("@/assets/Erp/3020AddVoucherLine.png");
    }
    .erp-add-line-icon {
        background-image: url("@/assets/Erp/erp-add.png");
    }
    .erp-copy-line-icon {
        margin-top: 5px;
        background-image: url("@/assets/Erp/copy.png");
    }
    .subtract-line {
        position: absolute;
        // top: 49px;
        right: -18px;
        z-index: 1999;
    }

    .subtract-line-icon {
        width: 16px;
        height: 16px;
        background: url(@/assets/Erp/3030DeleteVoucherLine.png) no-repeat center;
        background-size: 100% 100%;
        cursor: pointer;
    }
    .erp-subtract-line-icon {
        background-image: url("@/assets/Erp/erp-delete.png");
    }
    .pagination {
        // padding-top: 10px;
        text-align: right;
    }

    // 隐藏表头多选框
    :deep(.header-select-none) {
        th:first-child {
            .cell {
                cursor: default;

                .el-checkbox {
                    display: none;
                }
            }
        }
    }

    // 在checkbox状态被禁用的时隐藏
    :deep(td:first-child) {
        .cell {
            .el-checkbox.is-disabled {
                display: none;
            }
        }
    }

    :deep(.el-table-column--selection) {
        text-align: center;
    }

    :deep(.el-table) {
        overflow: visible;
        &.el-table--border::before {
            top: 0;
            // width: 1px;
        }
        &.el-table--border::after {
            top: 0;
        }
        .el-popper.is-light,
        .el-popper.is-dark {
            max-width: 300px;
            text-align: left;
        }

        & .cell.el-tooltip {
            min-width: 0px;
        }
        .el-table__header,
        .el-table__body {
            .el-table__row {
                &.el-table__row--striped {
                    background-color: var(--table-color);
                }

                &.row-selected {
                    background-color: var(--table-selected-color);
                }
            }
            .el-table-column--selection.el-table__cell {
                .cell {
                    padding: 0 4px;
                    min-width: 30px;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
        }
        &.el-table--enable-row-hover .el-table__body tr.row-selected:hover > td.el-table__cell {
            background-color: var(--table-selected-hover-color);
        }
        .el-table__header {
            .el-table__cell {
                &.show_overflow_tooltip {
                    .cell {
                        white-space: nowrap;
                        // padding: 0 4px;
                    }
                }
            }
        }
        .selection-col {
            .cell {
                display: flex;
                align-items: center;
            }
            .el-checkbox {
                height: unset;
            }
        }
    }

    &.paging-show {
        :deep(.el-table) {
            display: flex;
            flex-direction: column;
            .el-table__inner-wrapper {
                flex: 1;
                &::before {
                    height: 0;
                }
            }
            .el-table__body-wrapper {
                padding-bottom: 14px;
            }
            .el-scrollbar {
                position: static;
            }
        }
    }

    &.paging-hide {
        :deep(.el-table) {
            border-bottom: 1px solid var(--el-border-color-lighter);
        }
    }
}
:deep(.el-table__row) {
    &.is-moving {
        td.el-table__cell {
            border-bottom: 2px solid var(--main-color);
        }
    }
}
</style>
<style lang="less" scoped>
body[erp] {
    .table {
        // 隐藏表头多选框右侧线
        :deep(.header-select-none) {
            th:first-child {
                div.cell:after {
                    content: "";
                    display: none;
                }
            }
        }
        :deep(.el-table.el-table--scrollable-x) {
            .el-table__header,
            .el-table__body {
                .el-table__row {
                    &.el-table__row--striped {
                        background-color: var(--white);
                    }

                    &.row-selected {
                        background-color: var(--table-selected-color);
                    }
                    &.hover-row {
                        background-color: var(--table-selected-color);
                    }
                    &.hover {
                        background-color: var(--table-selected-color);
                    }
                }
            }
            .el-table__body tr.el-table__row--striped td.el-table__cell {
                background-color: inherit;
            }
        }
    }
}
</style>
