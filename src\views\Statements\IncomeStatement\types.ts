export interface IIncomeSheet {
    asid: number;
    expand: number;
    fold: number;
    lineID: number;
    lineNumber: number;
    lineType: number;
    monthTotal: number;
    note: string;
    proName: string;
    statementId: number;
    yearTotal: number;
    indentation?: boolean;
    children?: IIncomeSheet[];
}

export interface IIncomeEditData {
    lineType?: number;
    statementId: number;
    lineId: number;
    pid: number;
    title: string;
}
export interface ISearchInfo{
    pid: number;
    classification: boolean;
}
