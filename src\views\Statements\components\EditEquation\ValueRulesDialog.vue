<template>
    <el-dialog v-model="visible" :title="'取数规则说明'" center width="800" class="dialogDrag">
        <div class="value-rules-dialog-box" v-dialogDrag>
            <div class="value-rules-dialog-box-content">
                <p class="tips">{{ tips }}</p>
                <el-table :data="tableData" style="width: 70%; margin: 10px 0" border>
                    <el-table-column prop="code" label="科目编码" />
                    <el-table-column prop="name" label="科目名称" />
                    <el-table-column prop="debit" label="期末余额借方" />
                    <el-table-column prop="credit" label="期末余额贷方" />
                </el-table>
                <p v-for="(item, index) in tipsList" :key="index" class="tips">{{ item }}</p>
            </div>
            <div class="buttons">
                <a @click="visible = false" class="button solid-button">知道了</a>
            </div>
        </div>
    </el-dialog>
</template>
<script setup lang="ts">
import { ref, computed } from "vue";

const props = withDefaults(
    defineProps<{
        allAACalcStatement: boolean;
    }>(),
    {
        allAACalcStatement: true,
    }
);

const visible = ref(false);

const changeVisible = (show: boolean) => {
    visible.value = show;
};

const tableData = computed(() => [
    {
        code: "1122",
        name: "应收账款",
        debit: "220",
        credit: "",
    },
    {
        code: "112201",
        name: "应收账款01",
        debit: "300",
        credit: "",
    },
    {
        code: "",
        name: `客户1${props.allAACalcStatement ? "" : "_项目1"}`,
        debit: "100",
        credit: "",
    },
    {
        code: "",
        name: `${props.allAACalcStatement ? "客户2" : "客户1_项目2"}`,
        debit: "",
        credit: "200",
    },
    {
        code: "",
        name: `${props.allAACalcStatement ? "客户3" : "客户2_项目3"}`,
        debit: "400",
        credit: "",
    },
    {
        code: "112202",
        name: "应收账款02",
        debit: "",
        credit: "80",
    },
]);
const tipsList = computed(() => [
    "【余额】:取1122应收账款期末借贷方最终余额，即220",
    "【本级科目借方余额】:取1122应收账款期末余额借方，即220",
    "【本级科目贷方余额】:取1122应收账款期末余额贷方，即0",
    "【末级科目借方余额】:取112201应收账款01和112202应收账款02的期末余额借方之和，即300",
    "【末级科目货方余额】:取112201应收账款01和112202应收账款02的期末余额贷方之和，即80",
    props.allAACalcStatement
        ? "【辅助核算借方余额】:取112201应收账款01下辅助核算和112202应收账款02的期末余额借方之和，即500"
        : "【辅助核算借方余额】:取112201应收账款01下客户/供应商辅助核算和112202应收账款02的期末余额借方之和，即400",
    props.allAACalcStatement
        ? "【辅助核算贷方余额】:取112201应收账款01下辅助核算和112202应收账款02的期末余额贷方之和，即280"
        : "【辅助核算贷方余额】:取112201应收账款01下客户/供应商辅助核算和112202应收账款02的期末余额贷方之和，即180",
]);

const tips = computed(() => {
    return props.allAACalcStatement
        ? "以应收账款科目为例，112201应收账款01启用了客户辅助核算，科目余额如下:"
        : "以应收账款科目为例，112201应收账款01同时启用了客户辅助核算和项目辅助核算，科目余额如下:";
});

defineExpose({
    changeVisible,
});
</script>

<style lang="less" scoped>
.value-rules-dialog-box {
    width: 700px;
    margin: 0 auto;
    padding-top: 10px;
    .tips {
        margin: 0;
        text-align: left;
    }
    .buttons {
        margin-top: 10px;
    }
}
</style>
