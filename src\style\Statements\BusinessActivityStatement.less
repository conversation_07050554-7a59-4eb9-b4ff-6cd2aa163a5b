@import "./Statements.less";
@import "../SelfAdaption.less";
.main-content {
    .main-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 20px 16px;
    }
}
.tabs-box {
    display: flex;
    font-size: var(--font-size);
    div {
        width: 100px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 97;
        &.actived {
            z-index: 98;
            color: var(--main-color) !important;
            font-weight: bold;
        }
        &:last-child {
            margin-left: -20px;
            background-image: url("@/assets/Icons/tab.png");
            &.actived {
                background-image: url("@/assets/Icons/tab-active.png");
            }
        }
        &:first-child {
            background-image: url("@/assets/Icons/tab-head.png");
            &.actived {
                background-image: url("@/assets/Icons/tab-head-active.png");
            }
        }
    }
}
:deep(.main-center tr td .cell .level2) {
    padding-left: 30px;
}
