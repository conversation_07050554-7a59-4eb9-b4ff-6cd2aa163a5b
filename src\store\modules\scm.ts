import { defineStore } from "pinia";
import { ref } from "vue";
import store from "@/store";
import { getScmRelation, getScmRelationInfo, type ScmRelation, type ScmRelationInfo } from "@/api/scm";
import type { IResponseModel } from "@/util/service";

export const useScmInfoStore = defineStore("scm", () => {
    const isRelation = ref(false);
    const scmProductType = ref(0);
    const scmAsid = ref(0);
    const scmAsName = ref("");
    const hasGetInfo = ref(false);

    const handleGetScmRelation = async (forceUpdate = false) => {
        if (hasGetInfo.value && !forceUpdate) return;
        hasGetInfo.value = !forceUpdate;
        await getScmRelation().then((res: IResponseModel<ScmRelation>) => {
            if (res.state !== 1000) return;
            hasGetInfo.value = true;
            isRelation.value = res.data.isRelation;
            scmProductType.value = res.data.scmProductType;
            scmAsid.value = res.data.scmAsid;
        });
    };

    const handleGetScmRelationInfo = async () => {
        if (hasGetInfo.value) return;
        await getScmRelationInfo().then((res: IResponseModel<ScmRelationInfo>) => {
            hasGetInfo.value = true;
            if (res.state !== 1000) return;
            isRelation.value = res.data.isRelation;
            scmProductType.value = res.data.scmProductType;
            scmAsid.value = res.data.scmAsid;
            scmAsName.value = res.data.scmAsName;
        });
    };

    return { isRelation, scmAsid, scmProductType, scmAsName, hasGetInfo, handleGetScmRelation, handleGetScmRelationInfo };
});

/** 在setup外使用 */
export function useScmInfoStoreHook() {
    return useScmInfoStore(store);
}
