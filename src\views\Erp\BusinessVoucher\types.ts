import type { Option } from "@/components/SelectCheckbox/types";

export interface ITreeData {
    text: string;
    id: string;
    children: null | Array<ITreeData>;
    attributes: null | { VType: number; BusinessId: number; BillRootType: number };
    level: number;
}

export class ModuleBack {
    type = 0;
    typeName = "";
    sumCount = 0;
    generatedCount = 0;
    unGeneratedCount = 0;
}

export class VoucherModule extends ModuleBack {
    select = false;
}

export interface IVoucherCombineInfo {
    selectedList: Array<number>;
    selectedSubjectList: Array<number>;
    selectZero: boolean;
    date: number;
    separator: string;
    options: Array<Option>;
}

export interface IModuleVoucherCombineInfo extends IVoucherCombineInfo {
    display: boolean;
    title: string;
    type: number;
    separator: string;
}

export interface IQueryParams {
    startDate: string;
    endDate: string;
    txtSearch: string;
}

export enum ErpCheckBillType {
    All = 0,
    Generated,
    UnGenerated,
}

export interface IBaseErpData {
    billId: number;
    billDate: string;
    billCreateDate: string;
    billDateText: string;
    billNo: string;
    billRootType: number;
    billType: number;
    billTypeText: string;
    businessType: number;
    totalAmount: number;
    preparedBy: string;
    pid: number;
    vid: number;
    invoiceId: number;
    voucherInfo: string;
    note: string;
    isCheckOut: boolean;
    isApportioned: boolean;
    vtId: number;
    vtName: string;
    vstatus: number;
}
export interface IErpCashierData extends IBaseErpData {
    oppositeParty: string;
    ietype: number;
    ietypeName: string;
    iedirection: string;
    cdaccount: string;
    projectName: string;
    departmentName: string;
    description: string;
}
export interface IErpTransferData extends IBaseErpData {
    cdaccountIn: string;
    cdaccount: string;
    description: string;
}
export interface IErpInvoiceData extends IBaseErpData {
    customerName: string;
    vendorName: string;
    projectName: string;
    departmentName: string;
    employeeName: string;
    invoiceTypeName: string;
    settlementAmount: number;
    name: string;
    payeeType: number;
    expenseTypeName: string;
    offsetTypeName: string;
    transferName: string;
    transferToName: string;
    businessTypeName: string;
}
export interface IErpBillData extends IBaseErpData {
    customerName: string;
    vendorName: string;
    projectName: string;
    departmentName: string;
    employeeName: string;
    invoiceTypeName: string;
    settlementAmount: number;
    name: string;
    payeeType: number;
    expenseTypeName: string;
    offsetTypeName: string;
    transferName: string;
    transferToName: string;
    businessTypeName: string;
}

export type ErpDataType = IBaseErpData | IErpCashierData | IErpTransferData | IErpInvoiceData | IErpBillData;
