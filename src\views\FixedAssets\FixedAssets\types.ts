import type { IAccountSubjectModel } from "@/api/accountSubject.js";
export interface ISelectItem {
    value: number;
    label: string;
}

export interface ICommissioningDate {
    iaPId: number;
    iaPeriodInfo: string;
    isDefault: boolean;
    periodInfo: string;
    pid: number;
}

export interface IPeriod {
    pid: number;
    periodInfo: string;
    time: string;
}

export interface ICurrentPeriod {
    pid: number;
    isDefault: boolean;
    periodInfo: string;
}
export interface IChangeRecordSearchInfo {
    startPid: number;
    endPid: number;
    startVDate: string;
    endVDate: string;
    vgId: number;
    startVNum: string;
    endVNum: string;    
    status: string;
    changetype: string;
    fa_num: string;
    fa_type: string;
    fa_property: string;
    fa_name: string;
    fa_model: string;
    department: string;
    showInfo: boolean;
}

export interface ITableData {
    vg_name: string;
    createdDate: string;
    vp_id: number;
    period: string;
    voucher_detail: string;
    p_id: number;
    vv_id: number;
}
// 期初查看详情数据
export interface ICheckFinish {
    item1: boolean;
    item2: ICheckFinishItem2[];
}
export interface ICheckFinishItem2 {
    v1_name: string;
    v1_value: string;
    v2_name: string;
    v2_value: string;
}

export interface IFAType {
    asId: number;
    typeId: number;
    typeNum: string;
    typeName: string;
    fa_property: number;
    defaultDepreciationType: number;
    defaultServiceYear: number;
    depreciationType: number;
    serviceMonths: number;
    netsalvageRate: number;
    fixedAssetAccountSubjectID: number;
    depreciationAccountSubjectID: number;
    note: string;
}
// 部门
export interface IDepartment {
    asid: number;
    aatype: number;
    aaeid: number;
    aanum: string;
    aaname: string;
    value01: string;
    status: number;
    uscc: string;
    createdBy: number;
    createdDate: string;
    preName: string;
}

export interface ISelectStrItem {
    value: string;
    label: string;
}

export interface IAsubSelect {
    value: string | number;
    label: string;
    type: string;
}

// 科目
export interface IFAAsubDto {
    asubId: number;
    asubCode: string;
    asubName: string;
    aatypes: string;
}

export interface IFAChangePagingList {
    data: IFAChangePagingData[];
    count: number;
}

export interface IFAChangePagingData {
    as_id: number;
    change_id: number;
    period: string;
    p_id: number;
    fa_id: number;
    fa_num: string;
    fa_name: string;
    change_type_id: number;
    changetype: string;
    value1: string;
    value2: string;
    value3: string;
    note: string;
    created_date: string;
    vg_name: string;
    vp_id: number;
    vv_id: number;
    option: string;
    can_select: boolean;
}

export interface IGenerateChangeVoucher {
    voucherLines: IVoucherLines[];
    attachFileIds: string;
    attachFiles: Array<IAttachFiles>
}
export interface IAssetGenerateChangeVoucher extends IGenerateChangeVoucher {
    changeType: number;
}
interface IVoucherLines {
    veId: number;
    aacode: string;
    assistingAccounting: number;
    asubId: number;
    asubType: number;
    asubCode: string;
    asubName: string;
    asubAAName: string;
    debit: number;
    credit: number;
    description: string;
    fcId: number;
    fcCode: string;
    fcAmount: number;
    fcRate: number;
    foreigncurrency: number;
    measureUnit: string;
    quantity: number;
    price: number;
    quantityAccounting: number;
    isFaLine: true;
}
interface IAttachFiles {
    fileId: number;
    fileName: string;
    fileSize: string;
    filePath: string;
    relativePath: string;
    fileType: number;
    webPath: string;
    thumbPath: string;
}
export interface IAmortization {
    department_id: number;
    ratio: string;
    asub_id: string;
    asub_aae: string;
}

interface IAsubAAERelation {
    fa_aae: string;
    depreciation_aae: string;
    disposal_aae: string;
    impairment_provision_aae: string;
}

export interface IAddAsset {
    faNum: string;
    faProperty: number;
    createdPeriod: number;
    createdWay: number;
    faName: string;
    faType: string;
    faModel: string;
    startedDate: string;
    department?: number[];
    vendor: string;
    subjectCode: string;
    depreciationNow: number;
    faAsub: string;
    depreciationType: string;
    depreciationAsub: string;
    disposalAsub: string;
    costAsub: string;
    fa_prepareAcc: string;
    depreciationExpenseAcc: string;
    value: string;
    netSalvageRate: string;
    estimateResidualsRate: string;
    useFullLife: string;
    usedLife: string;
    remainingMonth: string;
    accumlatedDepreciation: string;
    accumlatedDepreciationTY: string;
    prevAccumulatedDepreciation: string;
    diminutionProvision: string;
    monthDepreciationValue: number;
    note: string;
    impairmentProvisionAsub: string;
    impairmentProvision: number;
    amortizations: IAmortization[];
    asubAAERelation: IAsubAAERelation;
}

// 资产详情
export interface IAssetDetail {
    as_id: number;
    fa_id: number;
    fa_num: string;
    fa_type: string;
    fa_name: string;
    fa_model: string;
    fa_property: string;
    started_date: string;
    department: number;
    vendor: string;
    vendor_id: number;
    created_way: number;
    decreased_way: string;
    depreciation_type: number;
    parent_id2: number;
    parent_id: number;
    parent_id1: number;
    fa_asub: number;
    fa_asub_name: string;
    depreciation_asub: number;
    depreciation_asub_name: string;
    cost_asub: number;
    cost_asub_name: string;
    disposal_asub: number;
    disposal_asub_name: string;
    impairment_provision_asub: number;
    impairment_provision_asub_name: string;
    value: number;
    netsalvage_rate: number;
    usefullife: number;
    usedlife: number;
    accumulated_depreciation: number;
    accumulated_depreciation_ty: number;
    impairment_provision: number;
    status: string;
    decreased_date: string;
    created_date: string;
    p_id: number;
    created_period: number;
    decreased_period: string;
    fa_type_name: string;
    department_name: string;
    preyeardeprecition: number;
    zcjz: number;
    monthdeprecition: number;
    yeardeprecition: number;
    totaldeprecition: number;
    provisionmonth: number;
    impairment: number;
    data_type: number;
    depreciation_now: number;
    note: string;
    hasDep?: number | string;
    attachFiles: string;
    attachsCount: number;
}
// 计提折旧
export interface IDepreciation {
    allChangeAssetNum: number;
    as_id: number;
    assets_detail: string;
    changeAssetNum: number;
    changeAssetVoucherNum: number;
    change_detail: string;
    depreciation_detail: string;
    depreciation_status: number;
    fa_status: number;
    ifsum: string;
    ifvoucher: string;
    newlyAssetNum: number;
    newlyAssetVoucherNum: number;
    option: string;
    p_id: number;
    period: string;
    voucher_detail: string;
    voucher_status: number;
    vp_id: number;
    vv_id: number;
}

// 编辑资产详情数据
export interface IFixedAssetsType {
    asId: number;
    defaultDepreciationType: number;
    defaultServiceYear: number;
    depreciationAccountSubjectID: number;
    depreciationType: number;
    fixedAssetAccountSubjectID: number;
    netsalvageRate: number;
    note: string;
    serviceMonths: number;
    typeId: number;
    typeName: string;
    typeNum: string;
    fa_property: number;
}

export type IAccountSubjectList = Omit<IAccountSubjectModel, 'asubId'> & { asubId: string };

export class batchData {
    visible: boolean;
    method: string;
    value?: string;
    ids: number[];
    constructor(visible: boolean, method: string, ids: number[],value?: string,) {
        this.visible = visible;
        this.method = method;
        this.value = value;
        this.ids = ids;
    }
}
export interface IFSearchItem {  
    fa_num: string;
    fa_name: string;
    fa_model: string;
    note: string;
    fa_type: number[];  
    department: number[]; 
    status: number; 
}
// export interface IFSelectItem {  
//     fa_type: number[];  
//     department: number[];  
// } 
// export interface IFSSingItem {  
//     status: number;  
// }