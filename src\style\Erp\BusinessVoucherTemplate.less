@import "../SelfAdaption.less";
.set-default-input-style() {
    height: 32px;
    box-shadow: 0 0 0 1px var(--border-color) inset;
    background-color: var(--white);
    border-radius: 2px;
    line-height: 28px;
    padding: 1px 11px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.content {
    & .main-center {
        position: relative;
    }
}
.slot-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    & .slot-top {
        display: flex;
        padding: 20px;
        border-bottom: 1px solid var(--title-split-line);
        position: relative;
        & span {
            color: var(--font-color);
            font-size: var(--h3);
            line-height: 22px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);
        }
    }
    & .add-info-wrap {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }
    & .add-info {
        padding: 0 50px 20px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: 22px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        & .add-field {
            display: flex;
            align-items: center;
            position: relative;
            & + .add-field {
                margin-left: 20px;
            }
            &.overflow {
                :deep(.el-input__inner) {
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                :deep(.el-input__wrapper) {
                    &:hover {
                        box-shadow: 0 0 0 1px var(--main-color) inset;
                    }
                }
            }
        }
        .row {
            margin-top: 20px;
            display: flex;
        }
    }
    & .add-voucher-template {
        padding-left: 40px;
        padding-right: 50px;
        flex: 1;
        min-height: 0;
        :deep(.el-table) {
            .asub-line-container {
                width: calc(100% - 2px);
                height: 32px;
                box-sizing: border-box;
                max-width: 400px;
                position: relative;
                .select-icon {
                    position: absolute;
                    right: 6px;
                    top: 9px;
                    display: flex;
                    align-items: center;
                }
                &:hover {
                    .asub-input {
                        box-shadow: 0 0 0 1px var(--main-color) inset;
                        border: none;
                    }
                }
            }
            input.asub-selector-input {
                width: 100%;
                height: 32px;
                box-sizing: border-box;
                padding: 1px 11px;
                border: 0;
                max-width: 400px;
                border: 1px solid var(--border-color);
                border-radius: 2px;
                &:focus {
                    border-color: var(--main-color);
                }
            }
        }
        .asub-input {
            width: 100%;
            max-width: 400px;
            .set-default-input-style();
            padding-right: 20px;
        }
    }
    & .add-tips {
        padding: 20px;
        padding-left: 50px;
        color: #666666;
        font-size: var(--h5);
        line-height: var(--line-height);
        display: flex;
        flex-direction: column;
        text-align: left;
        > div + div {
            margin-top: 8px;
        }
    }
}
.table {
    height: 100%;
    :deep(.el-table) {
        height: 100%;
    }
}

:deep(.el-table__cell) {
    padding: 0 !important;
}
:deep(.el-table__header) {
    height: 39.5px !important;
}
:deep(.cell input[type="text"]) {
    border: none !important;
}
.asub-selector {
    position: absolute;
    box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
    border-radius: 4px;
    border: 1px solid #e9e9e9;
    z-index: 100;
    background-color: var(--white);
    font-size: 14px;
    .asub-selector-type {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        height: 40px;
        border-bottom: 1px solid var(--border-color);
        .type-item {
            height: 100%;
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;
            .icon {
                display: inline-block;
                height: 16px;
                width: 16px;
                background: url("@/assets/Icons/question.png") no-repeat center;
                background-size: 16px 16px;
                position: absolute;
                right: -20px;
                top: 8px;
                &:hover {
                    background-image: url("@/assets/Icons/question-erp.png");
                    background-size: 15px 15px;
                }
            }
            &.active {
                color: var(--main-color);
                &::after {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background-color: var(--main-color);
                }
            }
        }
    }
    .display-list {
        position: relative;
        :deep(.el-scrollbar) {
            .el-scrollbar__view {
                .display-item {
                    height: 16px;
                    line-height: 16px;
                    padding: 6px 8px 6px 10px;
                    cursor: pointer;
                    text-align: left;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    &:hover {
                        background: #f7f8fb;
                        color: var(--main-color);
                    }
                    &.active,
                    &.select {
                        background: #f7f8fb;
                        color: var(--main-color);
                    }
                    &.current {
                        background: var(--border-color);
                    }
                }
            }
        }
        .overflow-toolTip {
            position: absolute;
            top: 52px;
            right: -310px;
            max-width: 300px;
            box-sizing: border-box;
            border-radius: 2px;
            padding: 12px;
            background: var(--el-bg-color-overlay);
            border: 1px solid var(--el-border-color-light);
            line-height: 1.4;
            text-align: justify;
            font-size: var(--el-popover-font-size);
            box-shadow: var(--el-box-shadow-light);
            word-break: break-all;
            min-width: 150px;
            text-align: left;
            .allow {
                width: 10px;
                height: 10px;
                position: absolute;
                top: 8px;
                left: -6px;
                z-index: 2;
                &::before {
                    position: absolute;
                    width: 10px;
                    height: 10px;
                    z-index: -1;
                    content: " ";
                    transform: rotate(45deg);
                    background: var(--el-text-color-primary);
                    box-sizing: border-box;
                    border: 1px solid var(--el-border-color-light);
                    background: var(--el-bg-color-overlay);
                    right: 0;
                    border-bottom-left-radius: 2px;
                    border-right-color: transparent !important;
                    border-top-color: transparent !important;
                }
            }
        }
    }
    .bottom-button {
        height: 44px;
        border-top: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        > span {
            color: var(--main-color);
            cursor: pointer;
            &.not-ps {
                color: var(--font-color);
            }
        }
    }
}

.relate-asub-detail-content {
    width: 408px;
    height: 424px;
    background: #ffffff;
    box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
    border: 1px solid #e9e9e9;
    box-sizing: border-box;
    padding: 20px 24px 28px;
    font-size: 14px;
    position: fixed;
    z-index: 100;
    text-align: left;
    display: flex;
    flex-direction: column;
    .detail-title {
        font-weight: 700;
        font-size: 14px;
        line-height: 16px;
    }
    .display-detail-asubInfo {
        display: flex;
        align-items: center;
        margin-top: 15px;
        margin-bottom: 17px;
        .right-content {
            width: 180px;
            height: 32px;
            line-height: 32px;
            border: 1px solid var(--border-color);
            padding: 0 10px;
            border-radius: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    > .row {
        line-height: 22px;
        margin-bottom: 8px;
    }
    :deep(.table.custom-table) {
        flex: 1;
        .el-table__empty-block {
            font-size: 12px;
            align-items: flex-start;
        }
        .el-table__empty-text {
            width: 100%;
            padding: 16px 20px;
            margin: 16px 18px 0;
            background: var(--el-table-header-bg-color);
        }
        .empty {
            color: var(--font-color);
            text-align: left;
            line-height: 17px;
            .row + .row {
                margin-top: 12px;
            }
            .blue {
                color: var(--main-color);
                cursor: pointer;
            }
        }
        tbody tr td {
            height: 36px;
        }
    }
    .allow {
        width: 10px;
        height: 10px;
        transform: rotate(45deg);
        position: absolute;
        left: -6px !important;
        background-color: var(--white);
        border-left: 1px solid rgba(17, 31, 65, 0.1);
        border-bottom: 1px solid rgba(17, 31, 65, 0.1);
        z-index: 101;
    }
}

.description-input-container {
    width: calc(100% - 2px);
    font-size: 14px;
    max-width: 400px;
    position: relative;
    box-sizing: border-box;
    .description-input {
        width: 100%;
        height: 32px;
    }
    .more {
        position: absolute;
        top: 4px;
        right: 3px;
        width: 24px;
        height: 24px;
        transform: rotate(90deg);
        cursor: pointer;
        background: url("@/assets/Erp/more.png") no-repeat center;
        background-size: 3px 13px;
    }
    &:hover {
        .delete-icon {
            display: block;
        }
        .description-input-display {
            box-shadow: 0 0 0 1px var(--main-color) inset;
        }
    }
    .delete-icon {
        position: absolute;
        right: 30px;
        top: 8px;
        width: 14px;
        height: 14px;
        display: none;
    }
    :deep(.el-input__wrapper) {
        padding-right: 50px;
        .el-input__inner {
            line-height: 16px;
        }
    }
    .description-input-display {
        .set-default-input-style();
        padding-right: 50px;
        &.active {
            border-color: var(--main-color);
        }
    }
}

.description-selector {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 2px;
    z-index: 100;
    max-width: 398px;
    font-size: 14px;
    .des-ls {
        height: 180px;
        overflow: auto;
    }
    .buttom-button {
        height: 32px;
        border-top: 1px solid var(--border-color);
        text-align: center;
        color: var(--main-color);
        display: flex;
        align-items: center;
        justify-content: center;
        > span {
            cursor: pointer;
        }
    }
    .des-i {
        word-break: break-all;
        color: var(--font-color);
        height: 16px;
        line-height: 16px;
        padding: 6px 8px 6px 10px;
        cursor: pointer;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &:hover {
            background: #f7f8fb;
            color: var(--main-color);
        }
        :deep(.span_wrap) {
            overflow: hidden;
        }
    }
}

.check-balance-content {
    .check-balance-main {
        padding: 20px 32px 24px;
        text-align: left;
        position: relative;
        .check-title {
            line-height: 22px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        .row {
            line-height: 22px;
        }
        :deep(.el-table) {
            tbody tr {
                &:last-child {
                    td {
                        border-bottom: none;
                    }
                }
                td {
                    height: 37px;
                }
            }
            tfoot tr td {
                &.el-table__cell {
                    background-color: rgba(250, 100, 0, 0.04);
                    .cell {
                        height: 37px;
                        line-height: 37px;
                        background-color: transparent;
                    }
                }
            }
            .el-table__footer-wrapper {
                transform: translateY(-2px);
                background-color: #fff9f5 !important;
                &::after {
                    content: "";
                    display: block;
                    height: 1px;
                    width: 100%;
                    background-color: var(--border-color);
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 99;
                }
            }
        }
        .icon {
            width: 79px;
            height: 73px;
            position: absolute;
            bottom: -10px;
            right: 10px;
            background: url("@/assets/Erp/un-balance.png") no-repeat center;
            background-size: 100% 100%;
            z-index: 100;
        }
    }
}

.reset-content {
    .reset-label {
        text-align: left;
        font-size: 14px;
        font-weight: 700;
        padding-left: 40px;
        margin: 15px 0 10px;
    }
    .vt-content {
        padding: 0 40px;
        margin-bottom: 20px;
        .vt-info {
            line-height: 28px;
        }
        .vt-main {
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 10px 20px;
            .vt-label {
                font-size: 18px;
                line-height: 28px;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-bottom: 10px;
            }
            .vt-line {
                display: flex;
                align-items: center;
                line-height: 24px;
                .left,
                .right {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .left {
                    width: 280px;
                }
                .right {
                    margin-left: 20px;
                }
            }
        }
    }
}

.note-container {
    width: 232px;
    position: relative;
    height: 32px;
    .display {
        height: 32px;
        box-sizing: border-box;
        border-radius: 2px;
        box-shadow: 0 0 0 1px #dcdfe6 inset;
        padding: 1px 11px;
        line-height: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
        &:hover {
            box-shadow: 0 0 0 1px var(--main-color) inset;
        }
    }
    :deep(.el-textarea) {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
    }
    &.editing {
        .display {
            display: none;
        }
        :deep(.el-textarea) {
            display: block;
            height: auto;
        }
    }
}

.question {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: url("@/assets/Icons/question.png") no-repeat center;
    background-size: 14px 14px;
    position: absolute;
    left: 52px;
    top: -2px;
    &:hover {
        background-image: url("@/assets/Icons/question-erp.png");
        cursor: pointer;
    }
}

.dragClass {
    width: 100%;
    border: 1px solid var(--table-border-color);
    border-bottom: none;
}
:deep(.ghostClass) {
    visibility: hidden;
    & + .el-table__row {
        border-top: 1px solid var(--table-border-color);
    }
}
