<template>
    <div>
        <TaxCalculation ref="taxCalculationRef"></TaxCalculation>
    </div>
</template>

<script lang="ts">
export default {
    name: "TaxCalculation",
};
</script>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import TaxCalculation from "@/views/Invoice/components/TaxCalculation.vue";
const taxCalculationRef = ref<InstanceType<typeof TaxCalculation>>();

onMounted(() => {
    taxCalculationRef.value?.getSearchDate();
})
</script>
