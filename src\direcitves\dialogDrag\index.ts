import { nextTick, type Directive } from "vue";
export const dialogDrag: Directive = {
    mounted(el: any) {
        nextTick(() => {
            let myEl = el;
            function getDialogDom() {
                while(myEl) {
                    if (myEl.classList.contains("dialogDrag")) {
                        return myEl;
                    }
                    myEl = myEl.parentNode;
                }
            }

            const dragDom = getDialogDom();
            const dialogHeaderEl = dragDom!.querySelector('.el-dialog__header');

            if (!dialogHeaderEl || !dragDom) return;

            dialogHeaderEl.style.cursor = 'move';

            const handleMouseDown = (e: MouseEvent) => {
                const disX = e.clientX;
                const disY = e.clientY;
                const boxTop = dragDom.offsetTop;
                const boxLeft = dragDom.offsetLeft;
                const screenHeight = document.body.clientHeight;

                const handleMouseMove = (e: MouseEvent) => {
                    let dx = e.clientX - disX;
                    let dy = e.clientY - disY;
                    let moveTop = boxTop + dy;
                    let moveLeft = boxLeft + dx;
                    if (moveTop < 0) {
                        moveTop = 0;
                    } else if (moveTop > screenHeight - dialogHeaderEl.offsetHeight) {
                        moveTop = screenHeight - dialogHeaderEl.offsetHeight;
                    }
                    // 移动当前元素 margin没为0的时候好像点击 移动会闪
                    dragDom.style.margin = '0px';
                    dragDom.style.top = `${moveTop}px`;
                    dragDom.style.left = moveLeft < 0 ? "0px" : `${moveLeft}px`;
                    // e.preventDefault ? e.preventDefault() : e.returnValue = false
                };

                const handleMouseUp = () => {
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                // e.preventDefault ? e.preventDefault() : e.returnValue = false
            };

            dialogHeaderEl.addEventListener('mousedown', handleMouseDown);
        });
    },
};

