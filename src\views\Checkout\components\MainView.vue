<template>
    <div class="checkout-content" style="min-height: 500px">
        <div class="create-period-check-tips" :class="isErp ? 'erp-tips' : ''">
            <div class="tip-item">
                <span class="circle green"></span>
                <span class="txt">未结转损益、未结账</span>
            </div>
            <div class="tip-item">
                <span class="circle blue"></span>
                <span class="txt">已结转损益、未结账</span>
                <el-tooltip effect="light" placement="top-start" :offset="6">
                    <template #content>
                        <div v-if="!isErp">
                            <div>深蓝色：对应期间已结转完损益，损益类科目无余额</div>
                            <div>浅蓝色：对应期间结转过损益，但损益类科目还有余额未结转完，需继续结转</div>
                        </div>
                        <div v-else>
                            <div>深绿色：对应期间已结转完损益，损益类科目无余额</div>
                            <div>浅绿色：对应期间结转过损益，但损益类科目还有余额未结转完，需继续结转</div>
                        </div>
                    </template>
                    <i  class="mark-question-icon"></i>
                </el-tooltip>
            </div>
            <div class="tip-item">
                <span class="circle gray"></span>
                <span class="txt">已结转损益、已结账</span>
            </div>
            <a v-permission="['checkout-cancheck']" class="button float-r" style="margin-top: 13px" @click="startBulkBilling">批量结账</a>
        </div>
        <div class="tiles" :class="{ erp: isErp }">
            <template v-for="(yearItem, index) in mainCardList" :key="index">
                <div class="tile-year">{{ Object.keys(yearItem)[0] }}</div>
                <div
                    v-for="item in yearItem[Object.keys(yearItem)[0]]"
                    :key="item.pid"
                    @click="() => goCheckOut(item)"
                    :class="
                        item.status === 3
                            ? 'base-tile locked'
                            : item.status === 2
                            ? item.greenLocked
                                ? 'base-tile current current-locked'
                                :'base-tile current canclick'
                            : item.status === 1
                            ? item.greenLocked
                                ? 'base-tile green-locked'
                                : 'base-tile canclick'
                            : item.status === 0
                            ? 'base-tile green-locked'
                            : 'base-tile'
                    "
                >
                    <div class="tile-head"></div>
                    <div class="tile-main">{{ item.sn }}</div>
                </div>
            </template>
        </div>
    </div>
    <el-dialog v-model="bulkBillingShow" title="批量结账" destroy-on-close center width="440" class="custom-confirm dialogDrag">
        <div class="dialog-container" v-dialogDrag>
            <div class="form-item date-picker">
                <div class="item-title">选择结账年月：</div>
                <el-date-picker
                    v-model="bulkBillingInfoMonth"
                    type="month"
                    :disabled-date="disabledDate"
                    :clearable="false"
                    :editable="false"
                    :teleported="false"
                    :format="'YYYY年MM月'"
                    value-format="YYYYMM"
                    class="date-picker-class"
                    style="width: 130px"
                    @change="changeMonth"  
                />
            </div>
            <div class="tip">提示：批量结账可能需要一段时间，请耐心等候~</div>
            <div class="buttons">
                <a class="button solid-button" @click="confirmCheckout">确认</a>
                <a class="button ml-10" @click="() => (bulkBillingShow = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="loadingShow" destroy-on-close center width="510" :show-close="false" class="dialogDrag">
        <div class="dialog-loading-container" v-dialogDrag>
            <div class="left">
                <div class="check-title">
                    <span>批量结账中</span><span>{{ loadingTxt }}</span>
                </div>
                <div class="txt">
                    <div>超过300万会计正在使用的工作神器</div>
                    <div>手机就能轻松记账</div>
                    <div>会计人的工作宝典</div>
                    <div style="text-align: right">——柠檬云财务</div>
                </div>
            </div>
            <div class="right">
                <img src="@/assets/Checkout/app-download-qrcode.png" alt="" />
                <div class="tip">扫码下载柠檬云财务</div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { autoCheckOutHandler } from "../utils";

import type { IPeriodData, ICheckoutItem, ICheckoutPageItem, ICheckoutPeriod } from "../tpyes";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { request } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { getUrlSearchParams, reloadPeriodInfo } from "@/util/url";
import { useLoading } from "@/hooks/useLoading";
import { processDialog } from "@/util/erpUtils";
import { checkPermission } from "@/util/permission";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { dayjs } from "element-plus";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { reloadAccountSetList } from "@/util/accountset";

const isErp = ref(window.isErp);
const props = defineProps<{
    checkoutList: ICheckoutItem[];
    bulkBillingList: IPeriodData[];
    handleAddReverseList: (item: ICheckoutPeriod) => void;
    scmAsid: number;
}>();
const tempList = computed(() => props.checkoutList);
const mainCardList = ref<ICheckoutItem[]>([]);

watch(
    tempList,
    (val) => {
        mainCardList.value = val;
    },
    { immediate: true }
);
const bulkBillingList = computed(() => {  
    return props.bulkBillingList.map((item) => ({  
        ...item,  
        time: `${item.year}${String(item.sn).padStart(2, '0')}`,   
    }));  
}); 

function disabledDate(time: Date) { 
    const start = bulkBillingList.value[0]?.time ?? new Date();
    const end = bulkBillingList.value[bulkBillingList.value.length - 1]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

function changeMonth() {
    bulkBillingInfo.value = bulkBillingList.value.find((item: any) => item.time === bulkBillingInfoMonth.value)?.pid || -1;
}

const bulkBillingShow = ref(false);
const loadingShow = ref(false);

const loadingTxt = ref("");

const bulkBillingInfo = ref(-1);
const bulkBillingInfoMonth = ref("");

const emit = defineEmits<{
    (event: "go-checkout", PID: number): void;
    (event: "bulkBillingFailed", data: ICheckoutPageItem[], pid: number): void;
    (event: "bulkBilling2preCheck", pid: number): void;
    (event: "loadData"): void;
    (event: "givePartMainMonth",partMainMonth: string): void;
}>();

const goCheckOut = (itemData: ICheckoutPeriod) => {
    if (itemData.status === 3) return;
    if ((itemData.status === 1 && itemData.greenLocked) || itemData.status === 0) {
        startBulkBilling(itemData.year,itemData.sn)
        return;
    }
    if (!checkPermission(["checkout-cancheck"])) {
        ElNotify({ type: "warning", message: "您没有财务结账权限" });
        return;
    }
    if ((itemData.status === 1 && !itemData.greenLocked) || itemData.status === 2) {
         emit("go-checkout", itemData.pid);
         if (window.localStorage.getItem("checkoutSN-" + getGlobalToken()) !== null) {
             window.localStorage.removeItem("checkoutSN-" + getGlobalToken());
         }
         window.localStorage.setItem("checkoutSN-" + getGlobalToken(), itemData.sn.toString());
         const partMainMonth = itemData.year + '' + ( String(itemData.sn).length===1? (0+''+itemData.sn) : (itemData.sn) )
         emit("givePartMainMonth", partMainMonth);
    }
};

const startBulkBilling = (year?:number, month?:number) => {
    const selectedBulkBilling = bulkBillingList.value.find(item => item.year === year && item.sn === month);
    if (selectedBulkBilling) {
        bulkBillingInfo.value = selectedBulkBilling.pid || -1;
    } else {
       bulkBillingInfo.value = bulkBillingList.value[0].pid || -1;
    }
    bulkBillingInfoMonth.value = bulkBillingList.value.find((item) => item.pid === bulkBillingInfo.value)?.time || "";
    bulkBillingShow.value = true;
};

function findItemByPid(list: ICheckoutItem[], pid: number): ICheckoutPeriod | undefined {
    for (const item of list) {
        for (const key in item) {
            if (Object.prototype.hasOwnProperty.call(item, key)) {
                const periods = item[key];
                for (const period of periods) {
                    if (period.pid === pid) {
                        return period as ICheckoutPeriod;
                    }
                }
            }
        }
    }
    return undefined;
}

const changeCardStatus = (pid: number) => {
    const item = findItemByPid(mainCardList.value, pid);
    if (item) {
        item.status = 3;
        props.handleAddReverseList(item);
        if (window.isErp) {
            finishLength.value += 1;
            const process = Math.floor((finishLength.value / length.value) * 100);
            processDialog("批量结账中...", process);
        }
    }
    thirdPartNotify(thirtPartNotifyTypeEnum.checkoutCheckout, {
        pid: pid,
    }).then(() => {});
};
const length = ref(0);
const finishLength = ref(0);

let timer: any;
const confirmCheckout = () => {
    const index = bulkBillingList.value.findIndex((item) => item.pid === bulkBillingInfo.value);
    if (index === -1) return;
    const list = bulkBillingList.value.slice(0, index + 1);
    length.value = list.length;
    bulkBillingShow.value = false;
    if (useThirdPartInfoStoreHook().isThirdPart) {
        useLoading().enterLoading("批量结账中...");
    } else if (!window.isErp) {
        loadingShow.value = true;
    }
    let dotCount = 0;
    timer = setInterval(() => {
        dotCount++;
        if (dotCount === 4) dotCount = 0;
        loadingTxt.value = Array(dotCount).fill(".").join("");
    }, 500);
    if (window.isErp) {
        processDialog("批量结账中...", 0);
    }
    autoCheckOutHandler(list, changeCardStatus)
        .then(() => {
            dispatchReloadAsubAmountEvent();
            ElNotify({ type: "success", message: "结账成功" });
            reloadAccountSetList();
            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, model: "checkout", frequency: "month", needToStore: true });
            reloadPeriodInfo();
            isErp.value && useAccountPeriodStore().getPeriods();
            emit("loadData");
        })
        .catch((err: { msg?: string; pid: number; message?: string; }) => {
            const period = bulkBillingList.value.find((item) => item.pid === err.pid) as IPeriodData;
            const dateInfo = period.year +"年"+ period.sn +"月";
            switch (true) {
                case err.msg?.includes("期初检查未通过"):
                    ElConfirm(err.msg + "，是否立即前往处理？").then((r: any) => {
                        if (r) {
                            const params: any = { pid: err.pid };
                            if (window.isErp) params.scmAsid = props.scmAsid;
                            request({ url: "/api/Checkout/LastCheck?" + getUrlSearchParams(params), method: "post" }).then((res: any) => {
                                if (res.state == 1000) emit("bulkBilling2preCheck", err.pid);
                            });
                        }
                    });
                    break;
                
                case err.msg?.includes("暂存凭证"):
                    ElConfirm(dateInfo + "结账失败，当前期间存在暂存凭证，不能结账", true);
                    break;
                
                case !!err.msg:
                    ElConfirm(err.msg + "，是否立即前往处理？").then((r: any) => {
                        if (r) {
                            const params: any = { pid: err.pid };
                            if (window.isErp) params.scmAsid = props.scmAsid;
                            request({ url: "/api/Checkout/LastCheck?" + getUrlSearchParams(params), method: "post" }).then((res: any) => {
                                if (res.state == 1000) emit("bulkBillingFailed", res.data, err.pid);
                            });
                        }
                    });
                    break;
            
                case err.message?.includes("Network Error"):
                    ElConfirm("结账超时 请稍后再试~", true);
                    break;
            
                default:
                    ElConfirm(dateInfo + "结账失败", true);
            }
        })
        .finally(() => {
            loadingClose();
        });
};

const loadingClose = () => {
    if (useThirdPartInfoStoreHook().isThirdPart) {
        useLoading().quitLoading();
    } else if (window.isErp) {
        processDialog("批量结账中...", 100);
    } else {
        loadingShow.value = false;
    }
    clearInterval(timer);
    length.value = 0;
    finishLength.value = 0;
};
defineExpose({
    startBulkBilling
})
</script>

<style lang="less" scoped>
@import "@/style/Checkout/IndexComponents.less";
@import "@/style/Functions.less";
.dialog-container {
    text-align: center;
    padding-top: 28px;
    font-size: var(--font-size);
    .form-item {
        display: flex;
        justify-content: center;
        align-items: center;
        .detail-el-select(130px, 30px);
        .item-title {
            display: inline-block;
            vertical-align: top;
            color: var(--font-color);
            line-height: 32px;
        }
    }
    .tip {
        color: var(--orange);
        line-height: var(--line-height);
        margin-top: 14px;
    }
    .buttons {
        margin-top: 28px;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
    .date-picker {
        :deep(.el-month-table) {
            td.disabled.today .cell {
                color: #a8abb2 !important;
            }
            td.current:not(.disabled) .cell {
                background-color: var(--main-color);
                color: var(--white) !important;
            }
        }
        :deep(.el-year-table) {
            td.disabled.today .cell {
                color: #a8abb2 !important;
            }
            td.current:not(.disabled) .cell {
                background-color: var(--main-color);
                color: var(--white) !important;
            }
        }
    }
}
.dialog-loading-container {
    background-color: var(--white);
    display: inline-block;
    width: 510px;
    height: 245px;
    border-radius: 2px;
    .left {
        float: left;
        margin-left: 30px;
        text-align: left;
        .check-title {
            color: var(--main-color);
            font-size: 24px;
            line-height: 33px;
            margin-top: 45px;
            text-align: left;
        }
        .txt {
            color: var(--font-color);
            font-size: var(--h3);
            line-height: 22px;
            margin-top: 20px;
            display: inline-block;
            vertical-align: top;
        }
    }
    .right {
        float: right;
        margin-right: 36px;
        > img {
            margin-top: 38px;
            width: 150px;
            height: 150px;
            vertical-align: top;
        }
        .tip {
            margin-top: 7px;
            color: #5d5d5d;
            font-size: var(--font-size);
            line-height: var(--line-height);
            text-align: center;
        }
    }
}
.tiles {
    .base-tile.green-locked {
        border: 1px solid var(--green);
        opacity: 0.4;
        &:hover {
            box-shadow: none;
            cursor: pointer;
        }
        .tile-head {
            background-color: var(--green);
        }
        .tile-main {
            color: var(--green);
        }
    }
    .base-tile.current-locked {
        border: 1px solid @blue;
        opacity: 0.4;
        &:hover {
            box-shadow: none;
            cursor: pointer;
        }
        .tile-head {
            background-color: @blue;
        }
        .tile-main {
            color: @blue;
        }
    }
}
.mark-question-icon {
    background-image: url("@/assets/Settings/question.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 16px 16px;
    font-size: 16px;
    margin-left: 3px;
    margin-top: -15px;
    height: 16px;
    width: 16px;
    cursor: pointer;
    display: block;
}
body[erp] {
    .base-tile.current-locked {
        border: 1px solid var(--green);
        opacity: 0.4;
        &:hover {
            box-shadow: none;
            cursor: pointer;
        }
        .tile-head {
            background-color: var(--green);
        }
        .tile-main {
            color: var(--green);
        }
    }
}
</style>
