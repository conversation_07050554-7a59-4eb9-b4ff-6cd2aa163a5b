<template>
    <div class="content" style="height: 100%">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="slot-title">发票风险分析</div>
                    <div class="main-center">
                        <div class="risk-analy">
                            <div class="risk-analy-img">
                                <img src="@/assets/Invoice/risk-analy-left.png" alt="">
                            </div>
                            <div class="risk-analy-info">
                                <div class="info-title">发票风险分析</div>
                                <div class="info-content">
                                    <div class="info-dot risk-green"></div>
                                    <div class="info-text">发票风险分析数据来源于企业通过一键取票从税局获取的发票数据，通过获取的发票数据，依据相关政策规定为您公司检测可能存在的发票风险及销售采购资金分析等</div>
                                </div>
                                <div class="info-content">
                                    <div class="info-dot risk-yellow"></div>
                                    <div class="info-text">进销项对比、虚开发票、采购虚假发票、销售采购资金分析、销售收入分析等多维度分析发票潜在风险</div>
                                </div>
                                <div class="info-content">
                                    <div class="info-dot risk-red"></div>
                                    <div class="info-text">通过发票风险分析， 可了解企业当前开票是否合规，客户及供应商情况，商品明细信息，规避企业潜在的发票风险</div>
                                </div>
                                <a class="button solid-button" @click="getRiskAnaly" v-permission="['invoice-analysis-canedit']" v-show="!isThrid">立即分析</a>
                                <div class="history-list" @click="getHistoryReport" v-if="isFirst">历史分析结果查询</div>
                            </div>
                        </div> 
                    </div>
                </div>
            </template>
            <template #result>
                <div class="slot-content align-center" style="height: 100%;">
                    <!-- 先把图片封面请求回来，防止数据很多时，图片预加载 -->
                    <img src="@/assets/Invoice/report-bg.png" alt="" style="display: none; width: 0; height: 0;">
                    <div class="slot-title">历史分析结果记录</div>
                    <div class="history-table">
                        <Table
                            ref="historyResultTableRef"
                            :loading="loading"
                            :data="historyResultTableData"
                            :columns="historyResultColumns"
                            :empty-text="emptyText"
                            :scrollbar-show="true"
                            :page-is-show="true"
                            :layout="paginationData.layout"
                            :page-sizes="paginationData.pageSizes"
                            :page-size="paginationData.pageSize"
                            :total="paginationData.total"
                            @current-change="handleCurrentChange"
                            @size-change="handleSizeChange"
                            @refresh="handleRerefresh"
                            :tableName="setModule"
                        >
                            <template #status>
                                <el-table-column
                                    label="生成状态"
                                    min-width="240"
                                    prop="status"
                                    align="left"
                                    header-align="left"
                                    :width="getColumnWidth(setModule, 'status')"
                                >
                                    <template #default="scope">
                                        <div class="status-block">
                                            <span :class="['status-icon', 
                                                            scope.row.status === 1 ? 'icon-padding' 
                                                            : scope.row.status === 2 ? 'icon-success' 
                                                            : 'icon-fail']"
                                            ></span>
                                            <span>{{ statusLabel[scope.row.status] }}</span>
                                            <el-tooltip :content="convertInvoiceTaskRemark(scope.row.failedMsg)" effect="light" placement="right-start">
                                                <span v-if="scope.row.status === 3" class="link" style="padding-left: 10px;">查看原因</span>
                                            </el-tooltip>
                                        </div>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #operation>
                                <el-table-column
                                    label="操作"
                                    min-width="240"
                                    prop="operation"
                                    align="left"
                                    header-align="left"
                                    :resizable="false"
                                >
                                    <template #default="scope">
                                        <span v-if="scope.row.status === 2" class="link" v-permission="['invoice-analysis-canview']" @click="checkReport(scope.row)">
                                            查看
                                        </span>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                    <div class="buttons mb-20">
                        <a class="button ml-10" @click="currentSlot='main'">返回</a>
                        <a class="button solid-button ml-10" @click="getRiskAnaly" v-permission="['invoice-analysis-canedit']" v-show="!isThrid">重新生成</a>
                    </div>
                </div>
            </template>
            <template #report>
                <div class="slot-content" style="overflow: hidden; height: 100%;">
                    <AnalyReport 
                        ref="AnalyReportRef"
                        :ReportData="ReportData"
                        @backHistoryList="backHistoryList"
                    ></AnalyReport>
                </div>
            </template>
        </ContentSlider>
    </div>
    <!-- 风险分析弹窗 -->
    <AnalyStep
        v-model:fetchInvoiceShow="AnalyStepShow"
        :hasReportTask="hasReportTask"
        :isFirst="isFirst"
        :invoiceTaskList="invoiceTaskList"
        @setCollectTaskId="setCollectTaskId"
        @getInvoiceTaskList="getInvoiceTaskList"
        @getReportTaskList="getReportTaskList"
        @getHistoryReport="getHistoryReport"
        @updateIsFirst="updateIsFirst"
        @setCollectReportId="setCollectReportId"
    ></AnalyStep>
</template>

<script lang="ts">
export default {
    name: "InvoiceRiskAnaly",
};
</script>
<script setup lang="ts">
import { ref, nextTick, computed, onMounted, onUnmounted, watch } from "vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import { request, type IResponseModel } from "@/util/service";
import AnalyStep from "./components/AnalyStep.vue";
import AnalyReport from "./components/AnalyReport.vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IInvoiceTaskResult, IInvoiceTaskValue, IHistoryPagingList, IHistoryItem } from "../types";
import { usePagination } from "@/hooks/usePagination";
import { getCookie, setCookie } from "@/util/cookie";
import { useAccountSetStore } from "@/store/modules/accountset";
import { ElNotify } from "@/util/notify";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { convertInvoiceTaskRemark } from "../utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "InvoiceRiskAnaly";
const isThrid = ref(useThirdPartInfoStoreHook().isThirdPart);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const slots = ["main", "result", "report"];
const currentSlot = ref("main");
const AnalyStepShow = ref(false);
//账套是否使用过发票分析功能
const isFirst = ref(false);
const updateIsFirst = () => {
    isFirst.value = true;
}
//判断账套是否使用过发票分析功能
function getIsFirst() {
    request({ 
        url: "/api/invoice/checkUsedAnalysis", 
        method: "get" 
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            isFirst.value = res.data;
        }
    }).catch((err) => {
        ElNotify({ type: "warning", message: "出错了，请联系侧边栏客服" });
    })
} 

const asId = useAccountSetStore().accountSet?.asId as number;
//取票任务Id
const setCollectTaskId = (val: number) => {
    setCookie("collectTaskId" + asId, val.toString(), "d14");
};
const getCollectTaskId = () => {
    const id = getCookie("collectTaskId" + asId);
    return id ? parseInt(id) : 0;
};
//风险分析报告Id
const setCollectReportId = (val: number) => {
    setCookie("collectReportId" + asId, val.toString(), "d14");
};
const getCollectReportId = () => {
    const id = getCookie("collectReportId" + asId);
    return id ? parseInt(id) : 0;
};
const setCollectReportIdStatus = (val: number) => {
    setCookie("collectReportIdStatus" + asId, val.toString(), "d14");
};
const getCollectReportIdStatus = () => {
    const id = getCookie("collectReportIdStatus" + asId);
    return id ? parseInt(id) : 0;
};
//是否有报告在生成中
let preCheck = false;
const hasReportTask = ref(false);
function hasReportTaskStatus() {
    if (!getCollectReportId()) {
        return;
    } else {
        request({ 
            url: "/api/invoice/reportStatus", 
            method: "get",
            params: {
                reportId: getCollectReportId(),
            } 
        }).then((res: IResponseModel<number>) => {
            if (res.state === 1000) {
                if (res.data === 1) {
                    hasReportTask.value = true; // 1 进行中，2成功，3，失败
                } else {
                    hasReportTask.value = false;
                }
                AnalyStepShow.value = true;
                setCollectReportIdStatus(res.data); 
            }
        }).catch(() => {
            ElNotify({ type: "warning", message: "出错了，请联系侧边栏客服" });
        }).finally(() => {
            preCheck = false;
        });
    }
}
const getRiskAnaly = () => {
    if (preCheck) return;
    if (getCollectReportId()) {
        hasReportTaskStatus();
    } else {
        preCheck = true;
        request({ 
        url: "/api/TaxBureau/PreCheck", 
        method: "get" 
        }).then((res: IResponseModel<IInvoiceTaskValue>) => {
            if (res.state === 1000) {
                AnalyStepShow.value = true;
                return;
            } else if (res.state === 2000) {
                if (res.data.result === "running" && hasReportTask.value) { 
                    AnalyStepShow.value = true;
                    return;
                }
            }
            ElNotify({ type: "warning", message: res.msg });
            return;
        }).catch((err) => {
            ElNotify({ type: "warning", message: "取票预检查发生错误，请稍后重试。" });
        }).finally(() => {
            preCheck = false;
        });
    }
};

//一键取票任务列表
let timer: number = 0;
const reInvoiceTaskList = () => {
    if (timer) return;
    timer = setInterval(() => {
        getInvoiceTaskList();
    }, window.invoiceTaskPolllingInterval*60*1000);
};
const unInvoiceTaskList = () => {
    timer && clearTimeout(timer);
    timer = 0;
};

const invoiceTaskList = ref<Array<IInvoiceTaskResult>>();
const getInvoiceTaskList = () => {
    request({
        url: `/api/TaxBureau/LastSummaryTask`,
    }).then((res: IResponseModel<Array<IInvoiceTaskResult>>) => {
        invoiceTaskList.value = res.data;
        hasInvoiceTask.value = invoiceTaskList.value && invoiceTaskList.value[0] && invoiceTaskList.value[0].status === 1;
        nextTick(() => {
            if (hasInvoiceTask.value) {
                reInvoiceTaskList();
            } else {
                unInvoiceTaskList();
            }
        });
        const taskId = getCollectTaskId();
        if (taskId && invoiceTaskList.value && invoiceTaskList.value[0] &&
                ((invoiceTaskList.value[0].invoiceTaskStatus === 2 && invoiceTaskList.value[0].fileTaskStatus > 0) ||
                (invoiceTaskList.value[0].invoiceTaskStatus > 2))
            ) {
            setCollectTaskId(0);
            window.dispatchEvent(new CustomEvent("modifyAccountSet"));
        }
    });
};

const hasInvoiceTask = ref(false);
//分析结果记录
const getHistoryReport = () => {
    currentSlot.value = "result";
    getHistoryData();
}

const historyResultTableRef = ref();
const loading = ref<boolean>(false);
const historyResultTableData = ref<Array<IHistoryItem>>([]);
function getHistoryData() {
    let params = {
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
    };
    request({
        url: `/api/invoice/reportList`,
        method: "get",
        params,
    }).then((res: IResponseModel<IHistoryPagingList>) => {
        if (res.state === 1000) {
            historyResultTableData.value = res.data.data;
            paginationData.total = res.data.count;
        }
    });
}
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    getHistoryData();
});
const historyResultColumns = ref<Array<IColumnProps>>([
    { label: "报告生成日期", prop: "reportDate", minWidth: 160, align: "left", headerAlign: "left", width:getColumnWidth(setModule, 'reportDate') },
    { label: "开票日期", prop: "invoiceDate", minWidth: 200, align: "left", headerAlign: "left", width:getColumnWidth(setModule, 'invoiceDate') },
    { label: "操作人", prop: "userName", minWidth: 120, align: "left", headerAlign: "left", width:getColumnWidth(setModule, 'userName') },
    { slot: "status" },
    { slot: "operation" },
]);
const emptyText = ref();
// 报告状态 1 进行中，2成功，3，失败
const statusLabel : { 
    [key: number]: string 
}= {
    1: "报告生成中",
    2: "已生成",
    3: "生成失败"
}

//循环是否报告已生成
let timerReport: number = 0;
const reReportTaskList = () => {
    if (timerReport) return;
    timerReport = setInterval(() => {
        getReportTaskList();
    }, 15000);
};
const unReportTaskList = () => {
    timerReport && clearTimeout(timerReport);
    timerReport = 0;
};
const getReportTaskList = () => {
    let params = {
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
    };
    request({
        url: `/api/invoice/reportList`,
        method: "get",
        params,
    }).then((res: IResponseModel<IHistoryPagingList>) => {
        if (res.state === 1000) {
            historyResultTableData.value = res.data.data;
            paginationData.total = res.data.count;
            nextTick(() => {
                if (reportTaskResult.value) {
                    reReportTaskList();
                } else {
                    unReportTaskList();
                    AnalyStepShow.value = false;
                    backHistoryList();
                }
            });
        }
    });
};
const reportTaskResult = computed(() => {
    return historyResultTableData.value && historyResultTableData.value[0] && historyResultTableData.value[0].status === 1;
});

const AnalyReportRef = ref();
const checkReport = (row: IHistoryItem) => {
    currentSlot.value ='report';   
    getReportData(row);
}
const ReportData = ref();
//获得报告数据
function getReportData(row: IHistoryItem) {
    let params = {
        reportId: row.reportId
    }
    request({
        url: `/api/invoice/report`,
        method: "get",
        params,
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            ReportData.value = res.data;
            AnalyReportRef.value?.initPage();
            return;
        } else {
            ElNotify({ type: "warning", message: "res.msg" });
        }
    }).catch(() => {
        ElNotify({ type: "warning", message: "出错了，请联系侧边栏客服" });
    });
}

const backHistoryList = () => {
    currentSlot.value = "result";
}
onMounted(() => {
    getIsFirst();
    getHistoryData();
    // hasReportTaskStatus();
    //当在生成报告中点击发票风险分析菜单，进入历史分析结果查询页面
    if (getCollectReportIdStatus() === 1 ) { 
        currentSlot.value = "result";
    } else {
        currentSlot.value = "main";
    }
});
onUnmounted(() => {
    hasReportTaskStatus();
})
</script>


<style lang="less" scoped>
@import url(@/style/SelfAdaption.less);

.risk-analy {
    margin: 30px auto 0;
    width: 1000px;
    border: 1px solid var(--slot-title-color);
    display: flex;
    padding: 50px 50px 80px;
    .risk-analy-img {
        margin-right: 90px;
        width: 341px;
        height: 337px;
        img {
            display: block;
            width: 100%;
            height: 100%;
        }
    }
    .risk-analy-info {
        padding-left: 60px;
        border-left: 1px solid var(--slot-title-color);
        width: 536px;
        .info-title {
            font-size: 24px;
            color: var(--font-color);
            text-align: center;
        }
        .info-content {
            margin-top: 20px;
            display: flex;
        }
        .info-dot {
            position: relative;
            top: 5px;
            margin-right: 10px;
            width: 10px;
            height: 10px;
            border-radius: 10px;
        }
        .info-text {
            flex: 1;
            min-width: 0;
            font-size: 16px;
            line-height: 20px;
            color: var(--font-color);
            word-break: break-all;
            text-align: left;
        }
        .solid-button {
            margin-top: 46px;
            width: 180px;
            line-height: 34px;
            height: 34px;
            border-radius: 4px;
        }
        .history-list {
            margin-top: 20px;
            font-size: 14px;
            color: #5E8BE0;
            cursor: pointer;
        }
    }
}
.risk-green {
    background: #28C940;
}
.risk-yellow {
    background: #FFBD2E;
}
.risk-red {
    background: #FF5F57;
}
.history-table {
    width: 1000px; 
    margin-top: 20px;
    margin-bottom: 40px; 
    flex: 1; 
    min-height: 0;
    :deep(.table) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table {
            flex: 1;
            min-height: 0;
        }
    }
}
.status-block {
    display: flex;
    align-items: center;
    .status-icon {
        margin-right: 5px;
        display: block;
        width: 18px;
        height: 18px;
    }
    .icon-padding {
        background: url("@/assets/Invoice/padding.png") center center no-repeat;
        background-size: 100% 100%;
    }
    .icon-fail {
        background: url("@/assets/Invoice/fail.png") center center no-repeat;
        background-size: 100% 100%;
    }
    .icon-success {
        background: url("@/assets/Invoice/success.png") center center no-repeat;
        background-size: 100% 100%;
    }
}
</style>
