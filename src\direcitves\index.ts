import type { App } from "vue";
import { permission } from "./permission";
import { decimalLimit } from "./decimalLimit";
import { dialogDrag } from "./dialogDrag";
import  {ellipsis}  from "./ellipsis";
import {treeDrag}  from "./treeDrag";
import { input } from "./input";

export function loadDirectives(app: App) {
    app.directive("permission", permission);
    app.directive("decimal-limit", decimalLimit);
    app.directive("dialogDrag", dialogDrag);
    app.directive("ellipsis", ellipsis);
    app.directive("treeDrag", treeDrag);
    app.directive("input", input);
}
