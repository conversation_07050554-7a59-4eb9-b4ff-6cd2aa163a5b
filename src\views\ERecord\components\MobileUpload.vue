<template>
    <el-dialog 
        v-model="mobileUploadFileShow" 
        title="手机拍照上传" 
        center 
        width="500px" 
        @close="qrcodeClose" 
        class="custom-confirm dialogDrag"
    >
        <div class="mobile-upload-content" v-dialogDrag>
            <div class="upload-code" v-loading="loading">
                <img :src="mobileQRCodeSrc" class="qrcodeimg" />
                <div class="expired-tip" :style="{ display: qrCodeExpired ? '' : 'none' }">
                    <img src="@/assets/ERecord/refresh.png" class="qrcode-refresh-icon" />
                    <span class="expired-tip-content">二维码过期</span>
                    <a @click="getQRCode" class="expired-tip-refresh">点击刷新</a>
                </div>
            </div>
            <p class="mobile-content-footer">微信扫一扫，用手机拍照上传</p>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount } from "vue";
import { request } from "@/util/service";
import { useAccountSetStore } from "@/store/modules/accountSet";
import type { IMobileUploadData } from "@/components/UploadFileDialog/types";

const props = defineProps({
    modelValue: { type: Boolean, required: true, default: false },
    fid: { type: Number, required: true, default: 0 },
});
const emit = defineEmits<{
    (e: "update:modelValue", value: boolean): void;
    (e: "close"): void;
    (e: "success-upload", fileData: IMobileUploadData[]): void;
}>();

const fid = computed(() => {
    return props.fid;
});
const currentAccountInfo = useAccountSetStore().accountSet;
const handleURLParams = (url: string) => {
    const flag = window.isProSystem || window.isErp || window.isAccountingAgent;
    return flag ? url + `?asid=${currentAccountInfo?.asId}` : url + `?free-asid=${currentAccountInfo?.asId}`;
};
//弹窗是否显示
const mobileUploadFileShow = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit("update:modelValue", val);
    },
});

//二维码图片地址
const loading = ref(false);
const mobileQRCodeSrc = ref("");

//二维码是否过期 切换过期样式
const qrCodeExpired = ref(false);

//请求二维码的token
const qrcodetoken = ref("");
const getGenerateToken = () => {
    return request({
        url: handleURLParams(window.jmHost + "/WeChatUpload/GenerateToken"),
        method: "get",
    });
};

//请求二维码
const getQRCode = () => {
    loading.value = true;
    getGenerateToken().then((res: any) => {
        qrcodetoken.value = res;
        request({
            url: handleURLParams(window.jmHost + "/WeChatUpload/GenerateCodeNew"),
            method: "get",
            params: {
                fid: fid.value,
                source: 0,
                authCode: res,
            },
        })
            .then((res: any) => {
                mobileQRCodeSrc.value = "data:image/jpeg;base64," + res.image;
                qrCodeExpired.value = false;
                //开启二维码过期延时器
                startQRCodeTimeout();
                //二维码是否被扫定时器
                startHasScanned(res.code);
            })
            .catch((err) => {
                console.log("二维码请求失败", err);
            })
            .finally(() => {
                loading.value = false;
            });
    });
};

//二维码是否过期

let qrcodeExpireTimer: number; //二维码到期延时器
let scannedTimer: number; //是否被扫定时器
let finishedUploadTimer: number; //是否上传成功定时器
let mostFinishedTimer: number; //最长判断是否上传
const requestFrequency = 5000; //定时器频率

const startQRCodeTimeout = () => {
    clearTimeout(qrcodeExpireTimer);
    qrcodeExpireTimer = setTimeout(() => {
        qrCodeExpired.value = true;
        clearTimer();
    }, 60000);
};

//二维码是否被扫
const startHasScanned = (code: string) => {
    clearInterval(scannedTimer);
    scannedTimer = setInterval(() => {
        hasScanned(qrcodetoken.value, code);
    }, requestFrequency);
};
const hasScanned = (qrcodeToken: string, code: string) => {
    request({
        url: handleURLParams(window.jmHost + "/WeChatUpload/HasScanned"),
        method: "post",
        data: {
            token: qrcodeToken,
        },
    }).then((res: any) => {
        if (res.Msg === "true") {
            //弹窗关闭回调会清除过期延时器和是否被扫定时器
            mobileUploadFileShow.value = false;
            //清除上一次的是否上传成功定时器
            clearFinishedTimer();
            startHasFinishedUpload(code);
        }
    });
};

//是否上传成功
const startHasFinishedUpload = (code: string) => {
    finishedUploadTimer = setInterval(() => {
        hasFinishedUpload(code);
    }, requestFrequency);
    mostFinishedTimer = setTimeout(() => {
        clearInterval(finishedUploadTimer);
    }, 8 * 60 * 1000);
};
const hasFinishedUpload = (code: string) => {
    request({
        url: handleURLParams(window.jmHost + "/WeChatUpload/HasFinishedUploadNew") + "&code=" + code,
        method: "post",
    }).then((res: any) => {
        if (res.Obj) {
            clearFinishedTimer();
        }
        if (res.Data !== null) {
            emit("success-upload", res.Data);
        }
    });
};

//窗口关闭触发函数
const qrcodeClose = () => {
    qrCodeExpired.value = false;
    clearTimer();
    emit("close");
};

//清除二维码过期延时器和二维码是否被扫的定时器
const clearTimer = () => {
    clearTimeout(qrcodeExpireTimer);
    clearInterval(scannedTimer);
};
//清除上传状态定时器，规定时间内没有上传成功自动清除判断状态的延时请求
const clearFinishedTimer = () => {
    clearTimeout(mostFinishedTimer);
    clearInterval(finishedUploadTimer);
};
defineExpose({
    getQRCode,
    clearFinishedTimer,
});

onBeforeUnmount(() => {
    clearTimer();
    clearFinishedTimer();
});
</script>

<style lang="less" scoped>
@import "@/style/ERecord/MainTopBox.less";
body[erp] {
    .mobile-upload-content {
        & .upload-code {
            :deep(.el-loading-mask) {
                .el-loading-spinner {
                    top: 50%;
                    margin-left: -95px;
                    .circular {
                        left: 85px;
                    }
                }
            }
        }
    }
}
</style>
