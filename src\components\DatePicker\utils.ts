export interface IPeriodData {
    year: number;
    sn: number;
    pid: number;
    time: string;
}

export function initStartOrEndMonth(periodData: IPeriodData[], startPid: number, endPid: number) {
    const result = {
        startMonth: "",
        endMonth: "",
    }
    result.startMonth = periodData.find((item) => item.pid === startPid)?.time || "";
    result.endMonth = periodData.find((item) => item.pid === endPid)?.time || "";
    return result;
}
const getPeriodInfo = (data: IPeriodData) => {
    return data.year + "年" + data.sn + "月";
};

function getPeriodInfoByKey(periodData: IPeriodData[], key: number) {
    const data = periodData.find((item) => item.pid === key);
    if (data) {
        return getPeriodInfo(data);
    } else {
        return "";
    }
}

export const getCurrentPeriodInfo = (periodData: IPeriodData[], startPid: number, endPid: number) => {
    if (startPid === 0 || endPid === 0) {
        return "----年--月";
    } else if (startPid === endPid) {
        return getPeriodInfoByKey(periodData, startPid);
    } else {
        return getPeriodInfoByKey(periodData, startPid) + "—" + getPeriodInfoByKey(periodData, endPid);
    }
};