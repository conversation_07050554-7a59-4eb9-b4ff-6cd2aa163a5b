@import "../Functions.less";

.content {
    height: calc(100vh - 140px);
    width: calc(100vw - 180px) !important;
    padding: 0 !important;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .main-content {
        flex: 1;
        width: 100% !important;
        .main-center {
            height: 100%;
            background-color: var(--white);
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.06);
            border-radius: 0px 0px 6px 6px;
            // flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: stretch;
            &.body-main {
                padding-top: 10px;
            }
            .body-left {
                height: auto;
                box-sizing: border-box;
                margin-right: 10px;
                // width: calc(100% - 16% - 20px);
                flex:1;
                min-width: 0;
                // height: calc(100% - 50px);
                .file-center,
                .backup-center,
                .audit-center {
                    padding: 0;
                    height: 100%;
                    :deep(.table) {
                        height: 100%;
                        .el-table {
                            border-bottom: none;
                            // .caret-wrapper {
                            //     width: 0;
                            //     .sort-caret {
                            //         left: 10px;
                            //     }
                            //     .sort-caret.ascending {
                            //         position: relative;
                            //     }
                            //     .sort-caret.descending {
                            //         position: relative;
                            //         bottom:3px;
                            //     }
                            // }
                        }
                    }
                }
                .backup-center {
                    .cell-file-name.link {
                        max-width: 80% !important;
                        display: inline-block;
                    }
                    .custom-table tbody tr td .cell .link {
                        max-width: 80% !important;
                        display: inline-block;
                    }
                }
            }
            .body-right {
                // height: auto;
                // height: calc(100% - 50px);
                // width: 200px;
                width: 16%;
                box-sizing: border-box;
                border: 1px solid var(--border-color);
                border-radius: 0px 0px 0px 6px;
                display: flex;
                flex-direction: column;

                .space-state {
                    min-height: 38px;
                    line-height: 22px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-left: 12px;
                    font-size: 11px;
                    color: var(--font-color);
                    white-space: nowrap;
                    .disk-used-space {
                        color: var(--main-color);
                    }
                    .update-btn {
                        display: inline-block;
                        min-width: 76px;
                        height: 26px;
                        background: url("@/assets/ERecord/upgrade-expand.png") no-repeat;
                        background-size: contain;
                        cursor: pointer;
                        margin: 0 4px;
                    }
                }
                .tree-view {
                    flex: 1;
                    min-height: 0;
                    // height: calc(100% - 38px);
                    // width: 198px;
                    width: 100%;
                    overflow: hidden;
                }
            }
        }
    }
}
.file-center.main-tool-bar.space-between {
    height: 30px;
    display: block;
    border-bottom: 1px solid var(--title-split-line);
}

.tree {
    width: 100%;
    height: 100%;
    .el-scrollbar {
        padding-right: 0;
    }
    // text-align: left;
    .detail-el-tree();

    :deep(.el-tree) {
        width: auto;
        min-width: 100%;
        .el-tree-node__content {
            // min-width: 198px;
            width: 100%;
            box-sizing: border-box;

            .edit-more {
                .el-icon {
                    transform: rotate(90deg);
                    color: #999;
                    &:focus-visible {
                        outline: none !important;
                    }
                }
            }
        }
        .current-highlight > .el-tree-node__content {
            background-color: var(--el-fill-color-light);
        }
        .el-tree__empty-block {
            width: 100%;
            text-align: center;
            margin-top: 40px;
        }
        .el-tree-node.no-children {
            .el-icon.el-tree-node__expand-icon {
                display: none;
            }
            .el-tree-node__label {
                margin-left: 20px;
            }
        }
    }

    .file-name {
        width: 70%;
        min-width: 128px;
    }

    .edit-more {
        width: 30%;
        text-align: right;
        padding: 5px;
        &:focus-visible {
            outline: none !important;
        }
        :deep(.el-dropdown) {
            vertical-align: middle;
            &:focus-visible {
                outline: none !important;
            }
            &:hover {
                border: none;
            }
        }
    }

    .el-dropdown-link {
        &:focus-visible {
            outline: none !important;
        }
    }
}
// 表格多选去除半选状态
:deep(.el-checkbox__input.is-indeterminate) {
    .el-checkbox__inner {
        background-color: #fff;
        border-color: #dcdfe6;
        &::before {
            display: none;
        }
    }
}
body[erp] {
    .content {
        margin-left: auto;
        margin-right: auto;
        padding-left: 0;
        padding-right: 0;
        height: calc(100vh);
        width: calc(100vw) !important;
        .edit-content {
            border-radius: 6px;
        }
        .main-content .main-center .body-left {
            .file-center,
            .backup-center,
            .audit-center {
                padding: 0;
                height: 100%;
                :deep(.table) {
                    height: 100%;
                    .el-table {
                        border-bottom: 1px solid var(--el-border-color-lighter);
                        // .caret-wrapper {
                        //     width: 0;
                        // }
                    }
                }
            }

        }
    }

}
