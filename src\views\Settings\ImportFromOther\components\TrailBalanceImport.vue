<template>
    <div v-loading="loading" class="slot-mini-content">
        <div style="width: 450px; margin: 0 auto" v-show="mainShow === 'divStep1'">
            <div class="main-center-statement">
                <div class="main-center-left">
                    <div class="main-center-left-row" style="height: 40px; margin-bottom: 3px">
                        <span class="row-title">选择财务软件：</span>
                        <div class="row-content">
                            <Select 
                                :teleported="false" 
                                filterable 
                                v-model="softWare" 
                                placeholder="--请选择--"
                                :filter-method="softWareFilterMethod"
                            >
                                <Option 
                                    v-for="item in showSoftWareList" 
                                    :key="item.key" 
                                    :label="item.value" 
                                    :value="item.key" 
                                />
                            </Select>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 22px; line-height: 20px" v-show="tipShow1">
                        <span style="color: #fd7400; font-size: 12px; margin-left: 54px">*请根据实际情况导入，如无对应数据无需导入</span>
                    </div>
                    <div class="main-center-left-row" style="line-height: 20px; height: 40px" v-show="tipShow2">
                        <span style="color: #fd7400; font-size: 12px; text-align: left; margin-left: 54px">
                            <span>*该软件的导出文件版本较低，请将文件另存为Excel 97-2003版本，</span><br />
                            <span>如无对应数据无需导入</span>
                        </span>
                    </div>
                    <div class="main-center-left-row" style="height: 40px; line-height: 40px" v-show="downloadRow">
                        <span class="row-title">下载导入模板：</span>
                        <div class="row-content">
                            <span style="color: rgba(24, 144, 255, 1); cursor: pointer" @click="DownloadLemonTemplate">
                                下载科目期初模板
                            </span>
                            <span style="padding-left: 20px"></span>
                            <span style="color: rgba(24, 144, 255, 1); cursor: pointer" @click="DownloadVoucherTemplate">
                                下载凭证模板
                            </span>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 40px; line-height: 40px" v-show="accountRow1">
                        <span class="row-title">导入科目期初：</span>
                        <div class="row-content">
                            <label class="file-button">
                                <input ref="selectedAccountFileRef1" @change="chooseFile('account', $event)" type="file" />
                                <span style="color: rgba(24, 144, 255, 1); cursor: pointer">选择文件</span>
                            </label>
                            <span class="file-Name" v-show="accountFileName !== ''">
                                <span class="icon"></span>
                                <FileNameSpan :fileName="accountFileName"></FileNameSpan>
                                <span class="clear" @click="() => clearFileInfo('account')"></span>
                            </span>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 40px; line-height: 40px" v-show="subjectRow">
                        <span class="row-title">
                            <span
                                >导入科目数据<el-popover placement="right" :width="360" trigger="hover" :teleported="false">
                                    <template #reference>
                                        <img
                                            src="@/assets/Settings/question.png"
                                            alt=""
                                            style="width: 16px; right: 85px"
                                            v-show="yiqidaizhangPopoverShow"
                                        />
                                    </template>
                                    在账套备份-审计接口中备份下载，并获取KJKM的txt文件作为科目表<br />
                                    在账套备份-Excel格式备份中备份最新的全年度的数据
                                </el-popover>
                                <el-popover placement="right" :width="360" trigger="hover" :teleported="false">
                                    <template #reference>
                                        <img
                                            src="@/assets/Settings/question.png"
                                            alt=""
                                            style="width: 16px"
                                            v-show="changjietongPopoverShow"
                                        />
                                    </template>
                                    在畅捷通的系统管理-数据导出-基础档案中导出科目表<br />
                                    在畅捷通的系统管理-数据导出-单据数据-凭证导入导出中导出凭证数据
                                </el-popover>
                                <el-popover placement="right" :width="360" trigger="hover" :teleported="false">
                                    <template #reference>
                                        <img
                                            src="@/assets/Settings/question.png"
                                            alt=""
                                            style="width: 16px"
                                            v-show="jindiezhangwuyouPopoverShow"
                                        />
                                    </template>
                                    在设置-科目中导出科目表<br />
                                    在设置-财务初始余额中导出科目期初数据(有外币核算时需切换到任一个外币再导出)<br />
                                    在查凭证中导出所有凭证数据 </el-popover
                                >：</span
                            >
                        </span>
                        <div class="row-content">
                            <label class="file-button">
                                <input ref="selectedSubjectFileRef" @change="chooseFile('subject', $event)" type="file" />
                                <span style="color: rgba(24, 144, 255, 1); cursor: pointer">选择科目数据表</span>
                            </label>
                            <span class="file-Name" v-show="subjectFileName !== ''">
                                <span class="icon"></span>
                                <FileNameSpan :fileName="subjectFileName"></FileNameSpan>
                                <span class="clear" @click="() => clearFileInfo('subject')"></span>
                            </span>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 40px; line-height: 40px" v-show="yiqidaizhangPopoverShow">
                        <span class="row-title">导入期初凭证：</span>
                        <div class="row-content">
                            <label class="file-button">
                                <input ref="selectedAccountFileRef2" @change="chooseFile('account', $event)" type="file" />
                                <span style="color: rgba(24, 144, 255, 1); cursor: pointer">选择期初和凭证</span>
                            </label>
                            <span class="file-Name" v-show="accountFileName !== ''">
                                <span class="icon"></span>
                                <FileNameSpan :fileName="accountFileName"></FileNameSpan>
                                <span class="clear" @click="() => clearFileInfo('account')"></span>
                            </span>
                        </div>
                    </div>
                    <div
                        class="main-center-left-row"
                        style="height: 40px; line-height: 40px"
                        v-show="accountRow2 && !yiqidaizhangPopoverShow"
                    >
                        <span class="row-title">导入科目期初：</span>
                        <div class="row-content">
                            <label class="file-button">
                                <input ref="selectedAccountFileRef3" @change="chooseFile('account', $event)" type="file" />
                                <span style="color: rgba(24, 144, 255, 1); cursor: pointer">选择科目余额表</span>
                            </label>
                            <span class="file-Name" v-show="accountFileName !== ''">
                                <span class="icon"></span>
                                <FileNameSpan :fileName="accountFileName"></FileNameSpan>
                                <span class="clear" @click="() => clearFileInfo('account')"></span>
                            </span>
                        </div>
                    </div>
                    <div
                        class="main-center-left-row"
                        style="height: 40px; line-height: 40px"
                        v-show="voucherRow && !yiqidaizhangPopoverShow"
                    >
                        <span class="row-title">导入凭证数据：</span>
                        <div class="row-content">
                            <label class="file-button">
                                <input ref="selectedVoucherFileRef" @change="chooseFile('voucher', $event)" type="file" />
                                <span style="color: rgba(24, 144, 255, 1); cursor: pointer">选择凭证数据表</span>
                            </label>
                            <span class="file-Name" v-show="voucherFileName !== ''">
                                <span class="icon"></span>
                                <FileNameSpan :fileName="voucherFileName"></FileNameSpan>
                                <span class="clear" @click="() => clearFileInfo('voucher')"></span>
                            </span>
                        </div>
                    </div>
                    <!-- <div v-if="isAccountingAgent" class="main-center-left-row" style="height: 40px">
                        <span class="row-title">选择导入账套：</span>
                        <div class="row-content">
                            <el-radio-group v-model="importAccount">
                                <el-radio :label="2" v-show="raidoShow">导入新账套</el-radio>
                                <el-radio :label="1">导入本账套</el-radio>
                            </el-radio-group>
                        </div>
                    </div> -->
                    <div class="main-center-left-row" style="height: 40px">
                        <span class="row-title">
                            金额为空是否导入<el-popover
                                placement="bottom-start"
                                trigger="hover"
                                :width="400"
                                :popper-style="{ padding: '6px' }"
                                content="科目余额表中金额为0或空的行是否导入科目和辅助核算资料"
                            >
                                <template #reference>
                                    <img src="@/assets/Settings/question.png" alt="" style="width: 16px; height: 16px; margin-left: 0" />
                                </template>
                            </el-popover>
                            ：
                        </span>
                        <div class="row-content">
                            <el-radio-group v-model="isZeroIsImport">
                                <el-radio label="1">导入</el-radio>
                                <el-radio label="2">不导入</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 50px">
                        <span class="row-title">账套名称：</span>
                        <div class="row-content">
                            <input type="text" v-model="searchInfo.asName" placeholder="请输入账套名称" :disabled="importAccount === 1" />
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 50px">
                        <span class="row-title">账套启用年月：</span>
                        <div class="row-content date">
                            <el-select :teleported="false" v-model="searchInfo.accountStartDateYear" :disabled="importAccount === 1">
                                <el-option v-for="item in yearList" :key="item" :label="item" :value="item" />
                            </el-select>
                            <span class="ml-10 mr-10">年</span>
                            <el-select :teleported="false" v-model="searchInfo.accountStartDateMonth" :disabled="importAccount === 1">
                                <el-option v-for="item in monthList" :key="item" :label="item" :value="item" />
                            </el-select>
                            <span class="ml-10">月</span>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 43px">
                        <span class="row-title">选择会计准则：</span>
                        <div class="row-content">
                            <el-select 
                                :teleported="false" 
                                v-model="searchInfo.accountStandard" 
                                :disabled="importAccount === 1"
                                :filterable="true"
                                :filter-method="accountStandardFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showAccountStandardList" 
                                    :label="item.value" 
                                    :value="item.key" 
                                    :key="item.key" 
                                />
                            </el-select>
                        </div>
                    </div>
                    <div class="main-center-left-row" style="height: 40px">
                        <span class="row-title">增值税种类：</span>
                        <div class="row-content">
                            <el-radio-group v-model="searchInfo.taxType" :disabled="importAccount === 1">
                                <el-radio :label="1">小规模纳税人</el-radio>
                                <el-radio :label="2">一般纳税人</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="bottom-tips" v-if="isAccountingAgent">
                        <p class="tips">
                            <img src="@/assets/Settings/tips.png" alt="" />
                            账套导入会覆盖本账套数据，请谨慎操作，导账完成后，原账套将会放进回收站
                        </p>
                    </div>
                </div>
            </div>
            <div class="bottom left">
                <div class="button solid-button" @click="SubmitStep">{{ btnSubmitStepTxt }}</div>
                <a class="button" @click.prevent="() => emits('returnBack')">上一步</a>
            </div>
        </div>
        <div id="divStep3" style="height: 510px" v-show="mainShow === 'divStep3'">
            <div id="divStep3Center" class="main-center" style="height: 400px">
                <div class="main-center-success" v-show="successShow">
                    <div class="main-center-success-img"></div>
                    导账成功!
                    <div class="main-center-success-msg">
                        账套名称：<span>{{ successASName }}</span
                        ><br />
                        会计准则：<span> {{ successAccountingStandard }}</span
                        ><br />
                        账套启用年月：<span>{{ successYearMonth }}</span>
                    </div>
                    <!-- <div class="main-center-success-link" @click="SuccessLink">立即前往柠檬云财务软件查看账套 ></div> -->
                </div>
                <div class="main-center-fail" v-show="failShow">
                    <div class="main-center-fail-img"></div>
                    您的数据导入有误，可以联系客服帮您看看哦~
                </div>
                <div class="main-center-successwitherror" v-show="successwitherrorShow">
                    <span style="margin-left: -500px">以下数据因错误原因无法导入：</span>
                    <div class="main-center-successwitherror-msg">
                        <template v-for="(item, index) in ErrorMessageGroup" :key="index">
                            <p class="main-center-successwitherror-msg-title">{{ item.errorTypeName }}</p>
                            <p class="main-center-successwitherror-msg-content" v-for="(errorItem, index) in item.messageList" :key="index">
                                {{ errorItem }}
                            </p>
                        </template>
                    </div>
                    其他数据已导账成功！<br />
                    成功导入账套：<span>{{ successwitherrorASName }}</span>
                    <!-- <div class="main-center-successwitherror-link" @click="SuccessLink">立即前往柠檬云财务软件查看账套 ></div> -->
                </div>
            </div>
            <div class="bottom" style="margin-top: 25px">
                <div class="bottom-button" id="btnSubmitStep3" @click="SuccessLink">完成</div>
            </div>
        </div>
    </div>
    <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
    <el-dialog center title="科目余额表导入" width="550px" v-model="divLemonImport" class="dialogDrag">
        <div id="divLemonImport" title="科目余额表导入" v-dialogDrag>
            <div class="dialog-info">
                <div v-if="dialogMsg.length > 0">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px">
                        <span style="line-height: 20px">请根据下面错误信息更正后重试</span>
                        <span class="link" @click="copyErrorMsg">复制</span>
                    </div>
                    <div class="dialog-info-box">
                        <el-scrollbar height="200" always>
                            <div class="dialog-info-text" v-for="(item, index) in dialogMsg" :key="index">
                                (<span>{{ index + 1 }}</span
                                >) <span>{{ item }}</span>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
                <!-- <div class="mt-10"></div>
                <span>第一步：请下载通用模板，并填写信息。</span>
                <a style="color: rgba(24, 144, 255, 1)" @click="DownloadLemonTemplate">下载模板</a>
                <br />
                第二步：导入完成的Excel文件
                <br />
                <label class="file-button">
                    <input ref="divNMYFileRef" @change="chooseFile('divNMYFile', $event)" type="file" />
                    <span style="color: rgba(24, 144, 255, 1); cursor: pointer">选取文件</span>
                </label>
                <div id="divDelNMYFile">
                    <div
                        id="spanNMYFileName"
                        style="margin-left: 70px; font-size: 14px; font-weight: 400; color: rgba(146, 146, 146, 1); float: left"
                    >
                        {{ divNMYFileName }}
                    </div>
                    <div class="dialog-info-img">
                        <div id="btnDelNMYFile" @click="() => clearFileInfo('divNMYFile')" class="dialog-info-del"></div>
                    </div>
                </div> -->
            </div>
            <div class="dialog-buttons">
                <!-- <a class="solid-button" @click="LemonTemplateImport">{{ btnSubmitLemonTxt }}</a> -->
                <a class="button ml-10" @click="CloseDivLemonImport">关闭</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { getServiceId } from "@/util/proUtils";
import { getYearArray, needSaveAs, needSubjectImport, canUploadTxt, canUploadZip } from "../utils";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import FileNameSpan from "./FileNameSpan.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import type {
    ISoftWare,
    ISoftWareItem,
    ICurrentAccountInfo,
    IImportFromOtherRequest,
    IImportFromOtherResponse,
    IErrorMessageGroupModel,
    ImportModeType,
} from "../types";
import { change2newasid } from "@/views/Settings/AccountSets/utils";
import { isInWxWork } from "@/util/wxwork";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getCookie } from "@/util/cookie";
import { commonFilterMethod } from "@/components/Select/utils";

const accountsetStore = useAccountSetStore();
const asId = accountsetStore.accountSet?.asId || 0;

const isAccountingAgent = window.isAccountingAgent;

const props = withDefaults(
    defineProps<{
        softWareName: string;
    }>(),
    {}
);

const emits = defineEmits<{
    (e: "returnBack"): void;
    (e: "gotoHistory", mode: ImportModeType, params: string): void;
}>();

const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const txtNewASId = ref(0);
const successASName = ref("");
const successAccountingStandard = ref("");
const successYearMonth = ref("");
const successwitherrorASName = ref("");
const successShow = ref(false);
const failShow = ref(false);
const successwitherrorShow = ref(false);
const ErrorMessageGroup = ref<IErrorMessageGroupModel[]>([]);
const divLemonImport = ref(false);
const dialogMsg = ref<String[]>([]);
const mainShow = ref("divStep1");
// const downloadImporterUrl = "https://www.ningmengyun.com/ningmengyun.zip";
const raidoShow = ref(true);

const yearList: number[] = getYearArray();
const monthList: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

const isWxwork = ref(isInWxWork());
const isErp = ref(window.isErp);
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const centerText = ref(false);
const activeName = ref("accountBalanceStatement");
const softWareList = ref<ISoftWare[]>([]);
const currentAccountInfo = ref<ICurrentAccountInfo>({
    asName: "",
    accountStandard: 2,
    accountStartDateMonth: 1,
    taxType: 1,
    accountStartDateYear: new Date().getFullYear(),
});
const initAccountInfo: ICurrentAccountInfo = {
    asName: "",
    accountStandard: 2,
    accountStartDateMonth: 1,
    taxType: 1,
    accountStartDateYear: new Date().getFullYear(),
};
const importAccount = ref<1 | 2>(2);
// 金额为空是否导入
const isZeroIsImport = ref("2");
const softWare = ref<string | number>("");
const softWareName = ref("--请选择--");
const searchInfo = reactive<ICurrentAccountInfo>({
    asName: "",
    accountStandard: 2,
    accountStartDateMonth: 1,
    accountStartDateYear: new Date().getFullYear(),
    taxType: 1,
});

const accountFileName = ref("");
const selectedAccountFile = ref<File | null>(null);
const selectedAccountFileRef1 = ref();
const selectedAccountFileRef2 = ref();
const selectedAccountFileRef3 = ref();
const voucherFileName = ref("");
const selectedVoucherFile = ref<File | null>(null);
const selectedVoucherFileRef = ref();
const subjectFileName = ref("");
const selectedSubjectFile = ref<File | null>(null);
const selectedSubjectFileRef = ref();
const divNMYFileName = ref("");
const divNMYFile = ref<File | null>(null);
const divNMYFileRef = ref();

// 提示展示
const tipShow1 = ref(false); // 提示1
const tipShow2 = ref(false); // 提示2
const downloadRow = ref(false); // 下载模板
const subjectRow = ref(false); // 导入科目数据
const accountRow1 = ref(false); // 导入科目期初  选择文件
const accountRow2 = ref(false); // 导入科目期初  选择科目余额表
const voucherRow = ref(false); // 导入凭证数据
const softWareType = ref(0); // 软件类型
const changjietongPopoverShow = ref(false); // 是否畅捷通显示问号提示框
const yiqidaizhangPopoverShow = ref(false); // 是否亿企代账显示问号提示框
const jindiezhangwuyouPopoverShow = ref(false); // 是否金蝶账无忧显示问号提示框

const loading = ref(false);

const getSoftware = () => {
    loading.value = true;
    request({
        url: `${window.importFromOther}/api/ImportFromOther/GetSoftware`,
        method: "post",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    })
        .then((res: IResponseModel<ISoftWare[]>) => {
            if (res.state !== 1000 || !res.data) return;
            softWareList.value = res.data;
            const targetSoftWare  = softWareList.value.find((item) => item.value === props.softWareName)
            softWare.value = targetSoftWare ? targetSoftWare.key : "";
        })
        .finally(() => {
            loading.value = false;
        });
};
const getASInfoAndInputInfo = () => {
    request({
        url: `${window.importFromOther}/api/ImportFromOther/GetAsInfo`,
        method: "post",
        headers: {
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
    }).then((res: IResponseModel<ICurrentAccountInfo>) => {
        if (res.state !== 1000 || !res.data) {
            ElNotify({ type: "warning", message: "亲，未获取到当前账套信息！" });
            return;
        }
        currentAccountInfo.value = res.data;
        changeSearchInfo(importAccount.value);
    });
};
const DownloadLemonTemplate = () => {
    globalExport(`${window.importFromOther}/api/ImportFromOther/DownloadTemplate`);
};
const DownloadVoucherTemplate = () => {
    globalExport(`${window.importFromOther}/api/ImportFromOther/DownloadVoucherTemplate`);
};
const chooseFile = (type: "account" | "voucher" | "subject" | "divNMYFile", event: Event) => {
    const input = event.target as HTMLInputElement;
    let file: File | null = (input.files as FileList)[0];
    let name = file ? file.name : "";
    if (!file) {
        file = null;
    }
    const typeName = name.substring(name.lastIndexOf("."));
    if (typeName !== "") {
        if (typeName.toLowerCase() !== ".xls" && typeName.toLowerCase() !== ".xlsx") {
            if (typeName.toLowerCase() === ".zip" && canUploadZip(softWareName.value, type)) {
                //部分软件可上传zip
            } else if (typeName.toLowerCase() == ".txt" && type == "subject" && canUploadTxt(softWareName.value)) {
                // 部分软件可上传txt
            } else {
                ElNotify({ type: "warning", message: "亲，请上传.xls或.xlsx文件！" });
                return;
            }
        }
    }
    saveFileInfo(type, name, file);
};
const clearFileInfo = (type: "account" | "voucher" | "subject" | "divNMYFile") => {
    switch (type) {
        case "account":
            accountFileName.value = "";
            selectedAccountFile.value = null;
            // 不大改只能先这样..
            if (selectedAccountFileRef1.value) selectedAccountFileRef1.value!.value = "";
            if (selectedAccountFileRef2.value) selectedAccountFileRef2.value!.value = "";
            if (selectedAccountFileRef3.value) selectedAccountFileRef3.value!.value = "";
            break;
        case "voucher":
            voucherFileName.value = "";
            selectedVoucherFile.value = null;
            if (selectedVoucherFileRef.value) selectedVoucherFileRef.value!.value = "";
            break;
        case "subject":
            subjectFileName.value = "";
            selectedSubjectFile.value = null;
            if (selectedSubjectFileRef.value) selectedSubjectFileRef.value!.value = "";
            break;
        case "divNMYFile":
            divNMYFileName.value = "";
            divNMYFile.value = null;
            if (divNMYFileRef.value) divNMYFileRef.value!.value = "";
            break;
        default:
            break;
    }
};
let canImport = true;
const btnSubmitStepTxt = ref("确定");
const handleUnLock = () => {
    canImport = true;
    btnSubmitStepTxt.value = "确定";
};
const SubmitStep = () => {
    if (!canImport) {
        ElNotify({ type: "warning", message: "导入中，请稍后。" });
        return;
    }
    if (softWare.value === -1 || softWare.value === "") {
        ElNotify({ type: "warning", message: "亲，请选择财务软件！" });
        return;
    }
    if (searchInfo.asName == "") {
        ElNotify({ type: "warning", message: "亲，请输入账套名称！" });
        return;
    }
    canImport = false;
    btnSubmitStepTxt.value = "导入中...";

    const inputRequest: IImportFromOtherRequest = {
        software: softWare.value as number,
        asNew: importAccount.value,
        asName: searchInfo.asName,
        startYear: searchInfo.accountStartDateYear.toString(),
        startMonth: searchInfo.accountStartDateMonth.toString(),
        accountingStandard: searchInfo.accountStandard,
        taxType: searchInfo.taxType,
        isZeroImport: isZeroIsImport.value,
    };

    const formData = new FormData();
    //科目附件
    if (selectedSubjectFile.value) {
        formData.append("subject", selectedSubjectFile.value);
    } else {
        if (needSubjectImport(softWareName.value)) {
            ElNotify({ type: "warning", message: "亲，请选择科目数据文件！" });
            handleUnLock();
            return false;
        }
    }
    //科目余额表附件
    if (selectedAccountFile.value) {
        formData.append("trialbalance", selectedAccountFile.value);
    } else {
        ElNotify({ type: "warning", message: "亲，请上传科目余额表附件！" });
        handleUnLock();
        return false;
    }
    //凭证附件
    if (selectedVoucherFile.value) {
        formData.append("voucher", selectedVoucherFile.value);
    }
    txtNewASId.value = 0;
    const requestParams = {
        url: window.importFromOther + getImportUrl(inputRequest),
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
        data: formData,
    };
    handleUpload(requestParams, 0);
};
let lemonCanImport = true;
const btnSubmitLemonTxt = ref("导入");
const LemonTemplateImport = () => {
    if (!lemonCanImport) {
        ElNotify({ type: "warning", message: "导入中，请稍后。" });
        return;
    }
    lemonCanImport = false;
    btnSubmitLemonTxt.value = "导入中...";
    //基本信息
    const inputRequest: IImportFromOtherRequest = {
        software: 99999,
        asNew: importAccount.value,
        asName: searchInfo.asName,
        startYear: searchInfo.accountStartDateYear.toString(),
        startMonth: searchInfo.accountStartDateMonth.toString(),
        accountingStandard: searchInfo.accountStandard,
        taxType: searchInfo.taxType,
        isZeroImport: isZeroIsImport.value,
    };
    const formData = new FormData();

    //科目余额表附件
    if (divNMYFile.value) {
        formData.append("trialbalance", divNMYFile.value);
    } else {
        ElNotify({ type: "warning", message: "亲，请上传附件。" });
        lemonCanImport = true;
        btnSubmitLemonTxt.value = "导入";
        return false;
    }
    txtNewASId.value = 0;
    const requestParams = {
        url: window.importFromOther + getImportUrl(inputRequest),
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
            withCredentials: true,
            Asid: asId,
            Authorization: `Bearer ${getCookie("ningmengcookie")}`,
        },
        data: formData,
    };
    handleUpload(requestParams, 1);
};
const formatTip = (tip: string) => {
    return `<span style="line-break: anywhere;">${tip}</span>`;
};
const handleUpload = (requestParams: any, type: 0 | 1) => {
    request(requestParams)
        .then((result: IResponseModel<IImportFromOtherResponse>) => {
            if (type === 0) {
                handleUnLock();
            } else {
                lemonCanImport = true;
                btnSubmitLemonTxt.value = "导入";
            }
            if (result.state === 1000) {
                if (result.data.code === 0) {
                    emits("gotoHistory", "historyRecord", result.data.data.importInfoId!);
                    return;
                }
                if (result.data && result.data.data) {
                    const data = result.data.data;
                    txtNewASId.value = data.asId;
                    if (data.errorMessageGroup.length > 0) {
                        //部分成功
                        successwitherrorShow.value = true;
                        successwitherrorASName.value = searchInfo.asName;
                        //失败信息
                        ErrorMessageGroup.value = data.errorMessageGroup;
                        mainShow.value = "divStep3";
                    } else {
                        divLemonImport.value = false;
                        //全部成功
                        successShow.value = true;
                        successASName.value = searchInfo.asName;
                        successAccountingStandard.value = searchInfo.accountStandard === 1 ? "小企业会计准则" : "企业会计准则";
                        successYearMonth.value = searchInfo.accountStartDateYear + "-" + searchInfo.accountStartDateMonth;
                        mainShow.value = "divStep3";
                    }
                    // if (importAccount.value === 1) {
                    //     change2newasid(data.asId);
                    // }
                } else if (result.data.isShow === true) {
                    // dialogMsg.value = result.data.message + "请使用模板导入数据哦~";
                    let newStr = result.data.message.replace(/等，请检查后重试！/g, "");
                    dialogMsg.value = newStr.split("\r\n");
                    divLemonImport.value = true;
                    return false;
                } else {
                    ElNotify({ type: "warning", message: formatTip(result.data.message) || "亲，数据获取错误，请联系管理员~" });
                    return false;
                }
            } else {
                //失败
                if (result.data) {
                    ElNotify({ type: "warning", message: formatTip(result.data.message) || "亲，导入失败，请联系管理员~" });
                    return false;
                } else {
                    ElNotify({ type: "warning", message: formatTip(result.msg) || "亲，导入失败，请联系管理员~" });
                    return false;
                }
            }
        })
        .catch((err: any) => {
            const resp = err.response;
            if (!resp) {
                console.log(err.message);
            } else if (resp?.status === 400) {
                if (window.isProSystem && resp.data.indexOf("增购") > 0) {
                    proOverFlowText.value = resp.data;
                    proOverFlowShow.value = true;
                } else {
                    const tr = JSON.parse(resp.data);
                    ElNotify({ type: "warning", message: tr.Message });
                }
                if (type === 0) {
                    canImport = true;
                    btnSubmitStepTxt.value = "确定";
                } else {
                    lemonCanImport = true;
                    btnSubmitLemonTxt.value = "导入";
                }
            } else {
                ElNotify({ type: "warning", message: "亲，导入失败，请联系管理员~" });
                if (type === 0) {
                    canImport = true;
                    btnSubmitStepTxt.value = "确定";
                } else {
                    lemonCanImport = true;
                    btnSubmitLemonTxt.value = "导入";
                }
            }
        });
};
const getImportUrl = (inputRequest: IImportFromOtherRequest) => {
    let url = "/api/ImportFromOther/ImportAS";
    const asId = useAccountSetStoreHook().accountSet?.asId;
    let subUrl = getUrlSearchParams(inputRequest) + (asId ? "&asId=" + asId : "") +(window.isProSystem ? "&serviceId=" + getServiceId() : "");
    return url + "?" + subUrl;
};

const copyErrorMsg = () => {
    let textToCopy = document.querySelector(".dialog-info-box .el-scrollbar__view");
    let range = document.createRange();
    if (textToCopy) {
        range.selectNode(textToCopy);
        window.getSelection()?.removeAllRanges();
        window.getSelection()?.addRange(range);
        document.execCommand("copy");
        window.getSelection()?.removeAllRanges();
        ElNotify({ type: "success", message: "错误信息已全部复制" });
    }
};

const openDownload = () => {
    globalExport("https://eus.ningmengyun.com/柠檬云导账工具.zip");
};

const CloseDivLemonImport = () => {
    dialogMsg.value = [];
    divLemonImport.value = false;
};
const SuccessLink = () => {
    change2newasid(txtNewASId.value);
};

const changeSearchInfo = (importAccount: 1 | 2) => {
    const replaceInfo = importAccount === 1 ? currentAccountInfo.value : initAccountInfo;
    searchInfo.asName = replaceInfo.asName;
    searchInfo.accountStandard = replaceInfo.accountStandard;
    searchInfo.accountStartDateYear = replaceInfo.accountStartDateYear;
    searchInfo.accountStartDateMonth = replaceInfo.accountStartDateMonth;
    searchInfo.taxType = replaceInfo.taxType;
};

const handleInit = () => {
    getSoftware();
    // 代账并且用户已有账套
    if (isAccountingAgent && asId) {
        importAccount.value = 1;
        getASInfoAndInputInfo();
    } else {
        importAccount.value = 2;
    }
};

handleInit();
watch(importAccount, (val) => {
    changeSearchInfo(val);
});
watch(softWare, (val) => {
    if (val === -1 || val === "") {
        softWareName.value = "--请选择--";
        tipShow1.value = false;
        tipShow2.value = false;
        downloadRow.value = false;
        subjectRow.value = false;
        accountRow1.value = false;
        accountRow2.value = false;
        voucherRow.value = false;
    } else {
        softWareName.value = softWareList.value.find((item) => item.key === val)?.value ?? "";
    }
    initFileInfo();
});
watch(softWareName, (val) => {
    if (val == "通用模板") {
        // 只展示下载模板  以及  导入科目期初的文本为选择文件
        softWareType.value = 1;
    } else {
        // 不显示下载模板  以及  导入科目期初的文本为选择科目余额表
        // needSaveAs 只判断提示 true -> tipShow2 / false -> tipShow1
        // needSubjectImport 只判断导入科目数据: subjectRow true  / false
        if (needSaveAs(val)) {
            softWareType.value = needSubjectImport(val) ? 2 : 3;
        } else {
            softWareType.value = needSubjectImport(val) ? 4 : 5;
        }
    }
    if (val === "--请选择--") {
        softWareType.value = 0;
    }
    changjietongPopoverShow.value = val.indexOf("畅捷通T+13.0") >= 0;
    yiqidaizhangPopoverShow.value = val.indexOf("亿企代账") >= 0;
    jindiezhangwuyouPopoverShow.value = val.indexOf("金蝶账无忧V5.0") >= 0;
});
watch(softWareType, (val) => {
    hiddenOrShow(val);
});
const hiddenOrShow = (type: number) => {
    switch (type) {
        case 1:
            downloadRow.value = true;
            accountRow1.value = false;
            tipShow1.value = false;
            tipShow2.value = false;
            subjectRow.value = false;
            accountRow2.value = true;
            voucherRow.value = true;
            break;
        case 2:
            downloadRow.value = false;
            accountRow1.value = false;
            tipShow1.value = false;
            tipShow2.value = true;
            subjectRow.value = true;
            accountRow2.value = true;
            voucherRow.value = true;
            break;
        case 3:
            downloadRow.value = false;
            accountRow1.value = false;
            tipShow1.value = false;
            tipShow2.value = true;
            subjectRow.value = false;
            accountRow2.value = true;
            voucherRow.value = true;
            break;
        case 4:
            downloadRow.value = false;
            accountRow1.value = false;
            tipShow1.value = true;
            tipShow2.value = false;
            subjectRow.value = true;
            accountRow2.value = true;
            voucherRow.value = true;
            break;
        case 5:
            downloadRow.value = false;
            accountRow1.value = false;
            tipShow1.value = true;
            tipShow2.value = false;
            subjectRow.value = false;
            accountRow2.value = true;
            voucherRow.value = true;
            break;
        default:
            tipShow1.value = false;
            tipShow2.value = false;
            downloadRow.value = false;
            subjectRow.value = false;
            accountRow1.value = false;
            accountRow2.value = false;
            voucherRow.value = false;
            break;
    }
};
const saveFileInfo = (type: "account" | "voucher" | "subject" | "divNMYFile", name: string, file: File | null) => {
    switch (type) {
        case "account":
            accountFileName.value = name;
            selectedAccountFile.value = file;
            break;
        case "voucher":
            voucherFileName.value = name;
            selectedVoucherFile.value = file;
            break;
        case "subject":
            subjectFileName.value = name;
            selectedSubjectFile.value = file;
            break;
        case "divNMYFile":
            divNMYFile.value = file;
            divNMYFileName.value = name;
            break;
        default:
            break;
    }
};
const initFileInfo = () => {
    accountFileName.value = "";
    selectedAccountFile.value = null;
    if (selectedAccountFileRef1.value) selectedAccountFileRef1.value!.value = "";
    if (selectedAccountFileRef2.value) selectedAccountFileRef2.value!.value = "";
    if (selectedAccountFileRef3.value) selectedAccountFileRef3.value!.value = "";
    voucherFileName.value = "";
    selectedVoucherFile.value = null;
    if (selectedVoucherFileRef.value) selectedVoucherFileRef.value!.value = "";
    subjectFileName.value = "";
    selectedSubjectFile.value = null;
    if (selectedSubjectFileRef.value) selectedSubjectFileRef.value!.value = "";
    divNMYFileName.value = "";
    divNMYFile.value = null;
    if (divNMYFileRef.value) divNMYFileRef.value!.value = "";
};
const accountStandardList = ref<Array<ISoftWare>>([
    { key: 1, value: "小企业会计准则" },
    { key: 2, value: "企业会计准则" },
]);
const showSoftWareList = ref<Array<ISoftWare>>([]);
const showAccountStandardList = ref<Array<ISoftWare>>([]);
watchEffect(() => {
    showSoftWareList.value = JSON.parse(JSON.stringify(softWareList.value));
    showAccountStandardList.value = JSON.parse(JSON.stringify(accountStandardList.value));
});
function softWareFilterMethod(value: string) {
    showSoftWareList.value = commonFilterMethod(value, softWareList.value, 'value');
}
function accountStandardFilterMethod(value: string) {
    showAccountStandardList.value = commonFilterMethod(value, accountStandardList.value, 'value');
}
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
@import "@/style/Settings/ImportFromOther.less";
.content {
    .main-content {
        position: relative;
        &::after {
            content: "";
            height: 1px;
            width: 100%;
            background-color: var(--slot-title-color);
            position: absolute;
            top: 39px;
        }
        .slot-mini-content {
            // width: 1000px;
        }
    }
}
.main-center-left-row {
    :deep(.el-popover) {
        font-size: 14px;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        text-align: left;
        background-color: var(--white);
        padding: 10px;
        box-shadow: 0px 0px 20px 0px rgba(179, 179, 179, 0.3);
        line-height: 18px;
    }
}
.bottom-tips {
    text-align: left;
    width: 520px;
    margin: 10px 0 10px 0;
    .tips {
        display: flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        color: #333;
        margin: 0;
        img {
            width: 15px;
            height: 15px;
            margin-right: 3px;
        }
        & ~ .tips {
            padding-left: 18px;
            white-space: nowrap;
        }
    }
}
.content .main-content .bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 200px;
    height: 35px;
    margin: 0 auto 20px;
}
.content .main-content .main-center {
    margin-bottom: 10px;
    margin-top: 40px;
    margin-left: 50px;
    margin-right: 50px;
    .main-center-successwitherror-link,
    .main-center-success-link {
        font-size: 16px;
        font-weight: 400;
        color: rgba(24, 144, 255, 1);
        line-height: 22px;
        cursor: pointer;
        margin-top: 15px;
        &:hover {
            text-decoration: underline;
        }
    }
    .main-center-success {
        font-size: 20px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        text-align: center;
        .main-center-success-img {
            height: 115px;
            width: 86px;
            margin: 0 auto;
            margin-bottom: 10px;
            background-image: url("@/assets/Settings/success.png");
        }
        .main-center-success-msg {
            width: 404px;
            height: 150px;
            background: rgba(246, 246, 246, 1);
            border-radius: 6px;
            margin: 0 auto;
            margin-top: 30px;
            text-align: left;
            font-size: 16px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 45px;
            padding-left: 20px;
            padding-top: 20px;
        }
    }
    .main-center-fail {
        font-size: 16px;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        text-align: center;
        padding-top: 90px;
        .main-center-fail-img {
            height: 151px;
            width: 108px;
            margin: 0 auto;
            margin-bottom: 30px;
            background-image: url("@/assets/Settings/fail.png");
        }
    }
    .main-center-successwitherror {
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        padding-top: 10px;
        text-align: center;
        .main-center-successwitherror-msg {
            width: 730px;
            height: 208px;
            background: rgba(253, 246, 246, 1);
            border-radius: 2px;
            border: 1px solid rgba(254, 131, 146, 1);
            margin: 0 auto;
            margin-bottom: 25px;
            margin-top: 10px;
            text-align: left;
            padding-left: 15px;
            overflow: auto;
            .main-center-successwitherror-msg-title {
                font-size: 14px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
                line-height: 20px;
            }
            .main-center-successwitherror-msg-content {
                font-size: 12px;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.85);
                line-height: 17px;
            }
        }
    }
}
.content .main-content .main-center-left {
    padding: 0;
}
.bottom-button {
    width: 102px;
    height: 32px;
    background: rgba(68, 180, 73, 1);
    border: 1px solid rgba(68, 180, 73, 1);
    border-radius: 4px;
    text-align: center;
    margin: 0 auto;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
    line-height: 32px;
    cursor: pointer;
}
#divLemonImport {
    height: 300px;
    a {
        text-decoration: none;
    }
    .dialog-info {
        font-size: 14px;
        line-height: 30px;
        margin: 15px 30px 10px 30px;
        height: 240px;
        .dialog-info-img {
            width: 20px;
            height: 20px;
            overflow: hidden;
            float: left;
            // margin-top: -25px;
        }
        .dialog-info-box {
            color: #fd7400;
            border: 1px solid var(--border-color);
            padding: 5px;
        }
        .dialog-info-text {
            word-break: break-all;
        }
    }
    .dialog-buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        border-top: 1px solid #dadada;
        text-align: center;
    }
    .dialog-info-del {
        width: 20px;
        height: 20px;
        background: url("@/assets/Settings/delete.png") no-repeat center;
        cursor: pointer;
        border-left: 20px solid transparent;
        -webkit-filter: drop-shadow(-20px 0 0 green);
        filter: drop-shadow(-20px 0 0 green);
    }
    #divDelNMYFile {
        display: flex;
        align-items: center;
        margin-top: -35px;
    }
    .file-button {
        vertical-align: 5px;
    }
}
.search {
    display: block;
    padding: 4px;
    font-family: 微软雅黑 !important;
    color: Gray;
    font-size: 12px !important;
    input {
        outline-style: none !important;
        padding: 4px;
        width: 100%;
        box-sizing: border-box;
        border-radius: 4px;
        color: #333;
        height: 24px;
        border: 1px solid #aaa;
    }
}
.help-icon {
    display: inline-block;
    vertical-align: middle;
    width: 14px;
    height: 14px;
    background: url("@/assets/Voucher/help.png") no-repeat 0 0;
    background-size: contain;
    &:hover {
        background: url("@/assets/Voucher/help-hover.png") no-repeat 0 0;
        background-size: contain;
    }
}

//文件选择框
.file-button {
    height: 25px;
    position: relative;
    display: inline-block;
    overflow: hidden;

    input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        filter: alpha(opacity=0);
        cursor: pointer;
    }

    .file-box {
        .set-font;
        margin-left: 20px;
        display: none;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: top;
    }

    &:hover {
        .link {
            text-decoration: underline;
        }
    }

    &:active {
        .link {
            color: var(--dark-link-color);
        }
    }
}
& :deep(.el-tabs) {
    .el-tabs__header {
        .el-tabs__nav {
            padding-left: 0;
        }
        .el-tabs__nav-wrap {
            &::after {
                display: none;
            }
            .el-tabs__item {
                padding: 20px 20px 20px 0;
                & + .el-tabs__item {
                    padding-left: 20px;
                }
            }
        }
    }
    .el-tabs__content {
        margin: 0 auto;
        margin-top: 32px;
        width: 1000px;
        border: 1px solid var(--slot-title-color);
    }
}
.main-right-bottom-link {
    right: 30px;
}
</style>
