<template>
    <el-dialog title="文件审核" center width="540px" class="custom-confirm dialogDrag">
        <div class="audit-confirm-content" v-dialogDrag>
            <p class="important-info">所选文件存在 <span style="color: red">未查验通过</span> 的电子发票</p>
            <p>您可以</p>
            <p>① 继续审核，则未查验通过的发票也会审核通过</p>
            <p>② 选择跳过，则未查验通过的发票不会审核，审核其他文件</p>

            <!-- <p>注：</p> -->
            <div class="remark-info">
                <p>1. 目前系统支持一年内的增值税发票和全电发票的查验，其他类型的发票会显示查验失败</p>
                <p>2. 您可以到列表筛选查验失败的发票重新查验或查看查验失败原因</p>
            </div>
        </div>
        <div v-if="isErp" class="buttons">
            <a class="button solid-button" @click="() => emit('skip-audit')">跳过</a>
            <a class="button" style="width: auto" @click="() => emit('force-audit')">继续审核</a>
        </div>
        <div v-else class="buttons">
            <a class="button mr-10" @click="() => emit('force-audit')">继续审核</a>
            <a class="button solid-button" @click="() => emit('skip-audit')">跳过</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
const emit = defineEmits<{
    (e: "force-audit"): void;
    (e: "skip-audit"): void;
}>();
const isErp = ref(window.isErp);
</script>

<style lang="less" scoped>
.audit-confirm-content {
    padding-left: 24px;
    padding-right: 24px;
    font-size: 14px;

    .important-info {
        font-size: 16px;
        font-weight: 500;
    }

    .remark-info {
        background-image: url("@/assets/Settings/warnning-yellow.png");
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 14px 14px;
        padding-left: 20px;
        font-size: 12px;
    }
}
.buttons {
    border-top: 1px solid var(--border-color);
}
</style>
