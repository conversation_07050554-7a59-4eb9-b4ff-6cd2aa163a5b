<template>
    <div class="content" :class="isErp ? '' : 'backup-content'">
        <div class="title">备份恢复</div>
        <div class="main-content" v-if="onlyShowAsyncFile">
            <div class="tab-right-content">
                <div class="disk-state">{{ diskState }}</div>
                <div class="update-btn" v-show="isTrial && !isThirdPart" @click="handleTrialExpired({ msg: ExpiredToBuyDialogEnum.ERecordSpaceExpand, needExpired: false })"></div>
            </div>
            <el-tabs v-model="activeName">
                <el-tab-pane label="文件下载中心" name="divAsyncFileInfo">
                    <DivAsyncFileInfo
                        :table-data="divAsyncFileInfoDAata"
                        @delete-file-item="deleteBackupItem"
                        @downloadbackupitem="downloadbackupitem"
                        @batch-delete-success="asyncFileBatchDeleteSuccess"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
        <div class="main-content" :class="onlyShowAuditFile ? 'erp' : ''" style="flex-direction:column" v-else-if="onlyShowAuditFile">
            <div class="tab-right-content">&nbsp;</div>
            <DivAuditMainInfo
                v-model:noShowAuditTable="noShowAuditTable"
                v-model:audit-soft-ware="auditSoftWare"
                v-model:start-pid="startPid"
                v-model:end-pid="endPid"
                :table-data="divAuditMainInfoData"
                :auditSoftWareList="auditSoftWareList"
                :searchPeriodList="searchPeriodList"
                :showSpaceDialog="showSpaceDialog"
                :onlyShowAuditFile="onlyShowAuditFile"
                :diskState="diskState"
                @delete-backup-item="deleteBackupItem"
                @downloadbackupitem="downloadbackupitem"
                @insert-row="insertRow"
            />
        </div>
        <div class="main-content" v-else>
            <div class="tab-right-content" v-if="!isErp">
                <div class="autoprocess-content" v-show="!isHideBarcode">
                    <el-checkbox
                        class="autoprocess-content-checkbox"
                        v-model="isOpenAutoProcess"
                        label="数据自动备份，永不丢失"
                        @change="(check) => handleSetAutoProcess(check)"
                    />
                    <a class="link" v-show="setAutoProcessBtnShow" @click="showAutoProcess">设置</a>
                </div>
                <div class="disk-state">{{ diskState }}</div>
                <div
                    class="update-btn"
                    v-show="isTrial && !isThirdPart"
                    @click="handleTrialExpired({ msg: ExpiredToBuyDialogEnum.ERecordSpaceExpand, needExpired: false })"
                ></div>
            </div>
            <div class="tips" v-show="activeName !== 'divAuditMainInfo'">
                <div class="tip-title">备份数据包、归档Excel、归档PDF有什么区别？</div>
                <div class="tip-content">
                    <div>
                        <span>备份数据包</span>
                        <img src="@/assets/Settings/jiantou.svg" alt="" style="width: 13px; height: 9px; margin: 4px 8px" />
                        <span>“备份”整个账套的数据</span>
                        <span class="highlight-gray1">（</span>
                        <img src="@/assets/Settings/dui.svg" alt="" style="width: 13px; height: 12px; margin: 3px 2px 0 4px" />
                        <span class="highlight-green">支持下载到本地和数据恢复，</span>
                        <img src="@/assets/Settings/cuo.svg" alt="" style="width: 13px; height: 12px; margin: 3px 2px 0 2px" />
                        <span class="highlight-gray2">不支持在线预览</span>
                        <span class="highlight-gray1" style="margin-left: 4px">）</span>
                    </div>
                    <div>
                        <span>归档Excel </span>
                        <span class="highlight-gray2">和 </span>
                        <span>归档PDF</span>
                        <img src="@/assets/Settings/jiantou.svg" alt="" style="width: 13px; height: 9px; margin: 4px 8px" />
                        <span>将整个账套的数据归档成Excel或PDF文件</span>
                        <span class="highlight-gray1">（</span>
                        <img src="@/assets/Settings/dui.svg" alt="" style="width: 13px; height: 12px; margin: 3px 2px 0 4px" />
                        <span class="highlight-green">支持下载到本地，</span>
                        <img src="@/assets/Settings/cuo.svg" alt="" style="width: 13px; height: 12px; margin: 3px 2px 0 2px" />
                        <span class="highlight-gray2">不支持在线预览和数据恢复</span>
                        <span class="highlight-gray1" style="margin-left: 4px">）</span>
                    </div>
                </div>
            </div>
            <el-tabs v-model="activeName">
                <el-tab-pane label="备份数据包" name="divMainInfo">
                    <DivMainInfo
                        ref="divMainInfoRef"
                        :table-data="divMainInfoData"
                        :uploadFinallyDoing="uploadFinallyDoing"
                        :showSpaceDialog="showSpaceDialog"
                        :showNotEnoughDiskDialog="showNotEnoughDiskDialog"
                        :showBackupTipDialog="showBackupTipDialog"
                        @begin-restore="beginRestore"
                        @delete-backup-item="deleteBackupItem"
                        @downloadbackupitem="downloadbackupitem"
                        @insert-row="insertRow"
                    />
                </el-tab-pane>
                <el-tab-pane label="归档Excel" name="divExcelMainInfo">
                    <DivExcelOrPdfMainInfo
                        type="excel"
                        :table-data="divExcelMainInfoData"
                        :showSpaceDialog="showSpaceDialog"
                        :showBackupTipDialog="showBackupTipDialog"
                        @delete-backup-item="deleteBackupItem"
                        @downloadbackupitem="downloadbackupitem"
                        @insert-row="insertRow"
                    />
                </el-tab-pane>
                <el-tab-pane label="归档PDF" name="divPdfMainInfo">
                    <DivExcelOrPdfMainInfo
                        type="pdf"
                        :table-data="divPdfMainInfoData"
                        :showSpaceDialog="showSpaceDialog"
                        :showBackupTipDialog="showBackupTipDialog"
                        @delete-backup-item="deleteBackupItem"
                        @downloadbackupitem="downloadbackupitem"
                        @insert-row="insertRow"
                    />
                </el-tab-pane>
                <el-tab-pane label="文件下载中心" name="divAsyncFileInfo">
                    <DivAsyncFileInfo
                        :table-data="divAsyncFileInfoDAata"
                        @delete-file-item="deleteBackupItem"
                        @downloadbackupitem="downloadbackupitem"
                        @batch-delete-success="asyncFileBatchDeleteSuccess"
                    />
                </el-tab-pane>
                <el-tab-pane label="审计对接" name="divAuditMainInfo">
                    <DivAuditMainInfo
                        v-model:noShowAuditTable="noShowAuditTable"
                        v-model:audit-soft-ware="auditSoftWare"
                        v-model:start-pid="startPid"
                        v-model:end-pid="endPid"
                        :table-data="divAuditMainInfoData"
                        :auditSoftWareList="auditSoftWareList"
                        :searchPeriodList="searchPeriodList"
                        :showSpaceDialog="showSpaceDialog"
                        :onlyShowAuditFile="onlyShowAuditFile"
                        :diskState="diskState"
                        @delete-backup-item="deleteBackupItem"
                        @downloadbackupitem="downloadbackupitem"
                        @insert-row="insertRow"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
    <el-dialog title="恢复数据" center width="478" v-model="restoreTipShow" class="dialogDrag">
        <div class="common-dialog-content restore-dialog-content" v-dialogDrag>
            <div class="restore-dialog-main" style="font-size: 14px" id="restoreTipContent">
                <div class="link-scm-desc" v-show="linkScmDescShow">本账套关联了进销存，恢复后需要您重新关联进销存哦~</div>
                <div class="restore-desc">
                    <div>1、您将把账套数据恢复到备份文件所在的状态，<span class="highlight-red">此操作不可回退，请谨慎操作</span>。</div>
                    <div>2、为保证恢复数据的完整性，<span class="highlight-red">请确保账套里的其他用户已经退出系统</span>。</div>
                </div>
                <div class="radio-btns">
                    <el-radio-group v-model="coverState">
                        <el-radio :label="true">不覆盖本账套数据，创建新账套数据</el-radio>
                        <el-radio :label="false">覆盖本账套数据，覆盖恢复后，原账套将会放进回收站</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="buttons">
                <a @click="restoreTipConfirm" class="button solid-button">确定</a>
                <a @click="() => (restoreTipShow = false)" class="button ml-10">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog title="提示" center width="440" v-model="notEnoughDiskShow" class="dialogDrag">
        <div class="common-dialog-content" v-dialogDrag>
            <div class="common-dialog-main has-title" style="padding: 30px 80px; text-align: left">
                <div class="common-dialog-main-title" style="text-align: left; font-weight: bold">
                    会计电子档案空间不足，请及时购买空间！
                </div>
                <div class="common-dialog-main-content mt-10">
                    <span>您的免费空间已使用</span>
                    <span> {{ spaceState }}</span>
                    <span>，剩余空间不足。</span>
                    <span class="highlight-red">本次文件将限时为您保留，逾期自动删除，请尽快下载到本地进行保存或者购买空间。</span>
                </div>
            </div>
            <div class="buttons">
                <a @click="() => (notEnoughDiskShow = false)" v-show="isWxwork" class="button solid-button">确定</a>
                <a @click="toDiskCenter" v-show="!isWxwork" class="button solid-button ml-10">立即购买</a>
                <a @click="() => (notEnoughDiskShow = false)" v-show="!isWxwork" class="button ml-10">取消</a>
            </div>
        </div>
    </el-dialog>
    <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
    <el-dialog center width="440" v-model="firstShow" :show-close="false" class="dialogDrag">
        <div class="firstShow-content" v-dialogDrag>
            <img class="firstShow-close" src="@/assets/Settings/X.png" @click="() => (firstShow = false)" />
            <img src="@/assets/Settings/dunpai.png" />
            <div class="firstShow-text">为了更好地保障您的账套数据安全，柠檬云特为您提供更加安全的自动备份功能</div>
            <div class="firstShow-confirm" @click="userFirstShow">抢先使用</div>
        </div>
    </el-dialog>
    <el-dialog center width="468" v-model="setAutoProcessShow" title="自动备份设置" class="dialogDrag">
        <div class="setauto-process-content" v-dialogDrag>
            <div class="setauto-process-main">
                <div class="choose-item">
                    <span class="span-label">备份日期：</span>
                    <el-select :teleported="false" v-model="backupInfo.date">
                        <el-option
                            v-for="(item, index) in new Array(31)"
                            :key="index"
                            :value="index + 1"
                            :label="'每月' + (index + 1) + '日'"
                        />
                    </el-select>
                    <span class="highlight-red float-l ml-10">每月备份将在当天24时前自动完成</span>
                </div>
                <div class="choose-item">
                    <span class="span-label">备份转发邮箱：</span>
                    <input v-show="!hasEmailShow" type="text" v-model="backupInfo.email" placeholder="请输入邮箱" />
                    <a
                        class="button solid-button ml-10"
                        v-show="!hasEmailShow"
                        @click="activateEmail"
                        style="padding-top: 1px; padding-bottom: 1px"
                    >
                        激活
                    </a>
                    <span class="weaker-font" v-show="hasEmailShow">{{ backupInfo.email }}</span>
                    <img @click="deleteEmail" v-show="hasEmailShow" src="@/assets/Settings/shanchu.png" alt="" />
                </div>
                <div class="choose-item" v-show="codeShow">
                    <span class="span-label">验证码：</span>
                    <input type="text" v-model="backupInfo.code" placeholder="请前往邮箱查看验证码" />
                </div>
            </div>
            <div class="buttons" :style="isErp ? '' : 'border-top:1px solid var(--border-color)'">
                <a class="button solid-button" @click="saveAutoProcess">确定</a>
                <a class="button ml-10" @click="setAutoProcessShow = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <DialogBackup ref="dialogBackupRef" />
</template>

<script lang="ts">
export default {
    name: "Backup",
};
</script>
<script setup lang="ts">
import { ref, watch, reactive, onUnmounted, onMounted, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { getCookie, setCookie } from "@/util/cookie";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { globalExport } from "@/util/url";
import { sizeFormatter, CheckMail } from "./utils";
import { AppConfirmDialog } from "@/util/appConfirm";
import { useRoute } from "vue-router";
import { checkPermission } from "@/util/permission";
import { change2newasid } from "../AccountSets/utils";
import { useLoading } from "@/hooks/useLoading";
import { isInWxWork } from "@/util/wxwork";

import type { ITableData, IAuditSoftWare, ISearchPeriod, IProcessRestoreResult, IProcessBack } from "./types";

import DivMainInfo from "./components/DivMainInfo.vue";
import DivAsyncFileInfo from "./components/DivAsyncFileInfo.vue";
import DivExcelOrPdfMainInfo from "./components/DivExcelOrPdfMainInfo.vue";
import DivAuditMainInfo from "./components/DivAuditMainInfo.vue";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import { tryShowPayDialog } from "@/util/proPayDialog";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { getServiceId } from "@/util/proUtils";
import DialogBackup from "./components/DialogBackup.vue";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const trialStatusStore = useTrialStatusStore();
const isTrial = computed(() => {
    return trialStatusStore.isTrial;
});
const asid = useAccountSetStore().accountSet?.asId as number;
const divMainInfoRef = ref<InstanceType<typeof DivMainInfo>>();
const dialogBackupRef = ref<InstanceType<typeof DialogBackup>>();

const route = useRoute();
const activeName = ref("divMainInfo");
const auditSoftWare = ref(-1);
const divMainInfoData = ref<ITableData[]>([]);
const divAsyncFileInfoDAata = ref<ITableData[]>([]);
const divExcelMainInfoData = ref<ITableData[]>([]);
const divPdfMainInfoData = ref<ITableData[]>([]);
const divAuditMainInfoData = ref<ITableData[]>([]);
const auditSoftWareList = ref<IAuditSoftWare[]>([]);
const searchPeriodList = ref<ISearchPeriod[]>([]);
const startPid = ref(-1);
const endPid = ref(-1);
const noShowAuditTable = ref(true);
const uploadLocalFileShow = ref(false);
const waitLoading = ref(false);
const restoreTipShow = ref(false);
const linkScmDescShow = ref(false);
const coverState = ref(true);
const filename = ref("");
const notEnoughDiskShow = ref(false);
const spaceState = ref("");
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");
const isOpenAutoProcess = ref(false);
const diskState = ref("");
const setAutoProcessBtnShow = ref(false);
const firstShow = ref(false);
const setAutoProcessShow = ref(false);
const codeShow = ref(false);
const hasEmailShow = ref(false);
const isThirdPart = computed(() => { return useThirdPartInfoStoreHook().isThirdPart });
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isErp = ref(window.isErp);
const onlyShowAuditFile = ref(false);

let isRelation = false;
let localFlag = false;
let autoProcessModel: any;
let isActivatingEmail = false;
let isSendingEmail = false;
let isDeletingEmail = false;
let emailSecret = "";
const backupInfo = reactive({
    date: 31,
    email: "",
    code: "",
});
const onlyShowAsyncFile = ref(false);
const isWxwork = ref(isInWxWork());

if (route.query.action) {
    const action = route.query.action;
    if (action == "export" || action == "print") {
        var t = route.query.t;
        if (!getCookie("backup-asyncfile-" + t)) {
            ElConfirm(
                '<div style="text-align: left;">' +
                    (action == "export" ? "导出成功！" : "打印数据生成成功！") +
                    "</div>" +
                    '<div style="text-align: left;">' +
                    (action == "export"
                        ? "您导出的数据较多，需在文件下载中心下载"
                        : "您打印的数据较多，已为您生成打印文件，需在文件下载中心打印") +
                    "</div>",
                true
            );
            activeName.value = "divAsyncFileInfo";
            setCookie("backup-asyncfile-" + t, "1", "d30");
        }
    } else if (action == "file") {
        activeName.value = "divAsyncFileInfo";
    } else if (action === "audit") {
        activeName.value = "divAuditMainInfo";
    }
}

// 自动备份相关操作
const showAutoProcess = () => {
    if (autoProcessModel.day) backupInfo.date = autoProcessModel.day;
    if (autoProcessModel.email) backupInfo.email = autoProcessModel.email;
    setAutoProcessShow.value = true;
};
const userFirstShow = () => {
    firstShow.value = false;
    showAutoProcess();
    const callBackHandle = () => {
        isOpenAutoProcess.value = true;
        setAutoProcessBtnShow.value = true;
    };
    setAutoProcess(callBackHandle);
};
const deleteEmail = () => {
    backupInfo.email = "";
    emailSecret = "";
    backupInfo.code = "";
    isActivatingEmail = false;
    hasEmailShow.value = false;
    codeShow.value = false;
};
const activateEmail = () => {
    backupInfo.code = "";
    if (isSendingEmail) {
        ElNotify({ type: "warning", message: "邮件发送中，请稍后再试！" });
        return;
    }
    const email = backupInfo.email;
    if (!email) {
        ElNotify({ type: "warning", message: "请输入邮箱！" });
        return;
    }
    if (email && !CheckMail(email)) {
        ElNotify({ type: "warning", message: "请输入正确的邮箱！" });
        return;
    }
    if (email == autoProcessModel.email && autoProcessModel.emailStatus == 1) {
        ElNotify({ type: "warning", message: "请使用一个新邮箱！" });
        return;
    }
    isSendingEmail = true;
    request({ url: "/api/Backup/SendEmailConfirmCode?email=" + email, method: "post" })
        .then((result: any) => {
            if (result.state === 1000) {
                emailSecret = result.data;
                isActivatingEmail = true;
                hasEmailShow.value = true;
                codeShow.value = true;
            } else {
                isActivatingEmail = false;
                ElNotify({ type: "warning", message: "发送邮件出现未知错误！" });
            }
        })
        .finally(() => {
            isSendingEmail = false;
        });
};
const saveAutoProcess = () => {
    const callback = () => {
        setAutoProcessShow.value = false;
        setAutoProcessBtnShow.value = true;
        isOpenAutoProcess.value = true;
        codeShow.value = false;
    };
    setAutoProcess(callback);
};
const handleSetAutoProcess = (check: any) => {
    if (!check) {
        request({ url: "/api/Backup/DeleteAutoProcess", method: "post" }).then((res: any) => {
            if (res.state === 1000 && res.data === 0) {
                ElNotify({ type: "success", message: "设置成功！" });
                setAutoProcessShow.value = false;
                setAutoProcessBtnShow.value = false;
                isOpenAutoProcess.value = false;
            } else {
                ElNotify({ type: "warning", message: "设置失败！" });
                setAutoProcessBtnShow.value = true;
                isOpenAutoProcess.value = true;
            }
        });
        return;
    }
    const successCallBack = () => {
        setAutoProcessBtnShow.value = true;
        isOpenAutoProcess.value = true;
    };
    setAutoProcess(successCallBack);
};
const setAutoProcess = (callback?: Function) => {
    const day = backupInfo.date || 31;
    const email = backupInfo.email;
    const emailStatus = autoProcessModel.emailStatus || 0;
    const confirmCode = backupInfo.code;
    if (email && !CheckMail(email)) {
        ElNotify({ type: "warning", message: "请输入正确的邮箱！" });
        return;
    }
    const params = {
        day,
        email,
        emailStatus,
        confirmCode,
        emailSecret,
        isDeletingEmail: isDeletingEmail ? 1 : 0,
        isActivatingEmail: isActivatingEmail ? 1 : 0,
    };
    request({
        url: "/api/Backup/SetAutoProcess",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: params,
    }).then((res: any) => {
        if (res.state == 1000) {
            const result = res.data;
            if (result == 0) {
                autoProcessModel.day = day;
                autoProcessModel.email = email;
                ElNotify({ type: "success", message: "设置成功！" });
                if (callback) callback();
            } else {
                if (result == -2) {
                    ElNotify({ type: "warning", message: "验证码错误！" });
                } else {
                    ElNotify({ type: "warning", message: "设置失败！" });
                }
            }
        } else {
            ElNotify({ type: "warning", message: "设置失败！" });
        }
    });
};

// 初始化执行
const getTableList = (optype: number) => {
    let urlPath = "";
    switch (optype) {
        case 0:
            urlPath = "BackupList";
            break;
        case 31:
            urlPath = "GetAsyncFileInfo";
            break;
        case 10:
            urlPath = "ExcelList";
            break;
        case 18:
            urlPath = "PdfList";
            break;
        case 24:
            urlPath = "AuditList";
            break;
    }
    request({ url: "/api/Backup/" + urlPath, method: optype === 31 ? "post" : "get" }).then((res: any) => {
        if (res.state == 1000) {
            switch (optype) {
                case 0:
                    divMainInfoData.value = res.data;
                    break;
                case 31:
                    divAsyncFileInfoDAata.value = res.data;
                    break;
                case 10:
                    divExcelMainInfoData.value = res.data;
                    break;
                case 18:
                    divPdfMainInfoData.value = res.data;
                    break;
                case 24:
                    divAuditMainInfoData.value = res.data;
                    break;
            }
        } else {
            ElNotify({ type: "warning", message: res.message });
        }
    });
};
const getAuditSoftWareList = () => {
    request({ url: "/api/Backup/AuditSoftware", method: "post" }).then((result: any) => {
        if (result.state === 1000) {
            auditSoftWareList.value = result.data;
            auditSoftWare.value = result.data[0].value || -1;
        } else {
            ElNotify({ type: "warning", message: "出现错误，请稍候刷新页面重试" });
        }
    });
};
const getSearchPeriod = () => {
    request({ url: "/api/Period/List" }).then((res: any) => {
        if (res.state == 1000) {
            searchPeriodList.value = res.data;
            startPid.value = searchPeriodList.value[searchPeriodList.value.length - 1]?.pid || -1;
            endPid.value = searchPeriodList.value[searchPeriodList.value.length - 1]?.pid || -1;
        } else {
            ElNotify({ type: "warning", message: res.message });
        }
    });
};
const initScmRelation = () => {
    request({ url: "/api/ScmRelation" })
        .then((result: any) => {
            if (result.state === 1000) {
                isRelation = result.data.isRelation;
            } else {
                ElNotify({ type: "warning", message: "出现错误，请稍候刷新页面重试" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "出现错误，请稍候刷新页面重试" });
        });
};
const getDiskState = () => {
    request({ url: "/api/Backup/GetDiskState", method: "post" }).then((res: any) => {
        if (res.state == 1000) {
            diskState.value = "已用空间(" + (sizeFormatter(res.data.usedSpace) || 0) + "/" + sizeFormatter(res.data.totalSpace) + ")";
            const restSpace = res.data.totalSpace - res.data.usedSpace;
            if (restSpace < 2 * 1024 * 1024) {
                handleTrialExpired({
                    msg:
                        restSpace <= 0
                            ? ExpiredToBuyDialogEnum.ERecordSpaceBackupOverflow
                            : ExpiredToBuyDialogEnum.ERecordSpaceBackupNotEnough,
                    model: "BackupERecordSpaceNotEnough",
                    frequency: "day",
                    needToStore: true,
                    needExpired: false,
                });
            }
        }
    });
};
const initAutoProcess = () => {
    if (autoProcessModel) {
        if (autoProcessModel.day) backupInfo.date = autoProcessModel.day;
        if (autoProcessModel.email) backupInfo.email = autoProcessModel.email;
        hasEmailShow.value = autoProcessModel.emailStatus == 1 && autoProcessModel.email;
    }
    isActivatingEmail = false;
    isSendingEmail = false;
    isDeletingEmail = false;
};
const firstShowHandle = () => {
    if (!route.query.action && !isHideBarcode.value) firstShow.value = true;
};
const loadAutoProcessStauts = () => {
    request({ url: "/api/Backup/GetAutoProcess", method: "post" }).then((result: any) => {
        if (result.state == 1000) {
            if (result.data.item1) {
                isOpenAutoProcess.value = true;
                setAutoProcessBtnShow.value = true;
            } else {
                isOpenAutoProcess.value = false;
                setAutoProcessBtnShow.value = false;
                firstShowHandle();
            }
            autoProcessModel = result.data.item2;
            initAutoProcess();
        }
    });
};
const accountSet = useAccountSetStore().accountSet;
const handleInit = () => {
    if (window.isAccountingAgent || (!checkPermission(["asyncfile-candelete"]) && !isErp.value) || accountSet?.permission !== "10001") {
        if (route.query.action && isErp.value) {
            onlyShowAuditFile.value = true;
        } else {
            onlyShowAsyncFile.value = true;
            activeName.value = "divAsyncFileInfo";
        }
    }
    getTableList(0);
    getTableList(31);
    getTableList(10);
    getTableList(18);
    getTableList(24);
    getAuditSoftWareList();
    getSearchPeriod();
    initScmRelation();
    getDiskState();
    !onlyShowAsyncFile.value && loadAutoProcessStauts();
};

handleInit();

const downloadbackupitem = (fileName: string, fileType: number) => {
    const url = "/api/Backup/DownloadFile?fileName=" + encodeURIComponent(fileName) + "&fileType=" + fileType;
    globalExport(url);
};
const deleteBackupItem = (fileName: string, backupType: string) => {
    ElConfirm("确定删除该备份？").then((r: any) => {
        if (r) {
            const path = backupType === "audit" ? "ProcessDeleteAudit" : "ProcessDelete";
            const url = `/api/Backup/${path}?fileName=` + encodeURIComponent(fileName);
            request({ url, method: "post" }).then((data: any) => {
                if (data.state == 1000) {
                    if (backupType == "zip") {
                        divMainInfoData.value = divMainInfoData.value.filter((item) => item.fileName != fileName);
                    } else if (backupType == "excel") {
                        divExcelMainInfoData.value = divExcelMainInfoData.value.filter((item) => item.fileName != fileName);
                    } else if (backupType == "pdf") {
                        divPdfMainInfoData.value = divPdfMainInfoData.value.filter((item) => item.fileName != fileName);
                    } else if (backupType == "audit") {
                        divAuditMainInfoData.value = divAuditMainInfoData.value.filter((item) => item.fileName != fileName);
                    } else if (backupType == "file") {
                        divAsyncFileInfoDAata.value = divAsyncFileInfoDAata.value.filter((item) => item.fileName != fileName);
                    }
                    ElNotify({ type: "success", message: "删除成功" });
                    getDiskState();
                } else {
                    ElNotify({ type: "warning", message: data.msg });
                }
            });
        }
    });
};
const handleReload = (optype: 0 | 31 | 10 | 18 | 24) => {
    getTableList(optype);
    getDiskState();
};
const asyncFileBatchDeleteSuccess = () => {
    handleReload(31);
};
const uploadFinallyDoing = (type: boolean) => {
    waitLoading.value = false;
    if (localFlag) return;
    localFlag = true;
    request({ url: "/api/Backup/GetBackupMsg", method: "post" })
        .then((result: any) => {
            const res = JSON.parse(result.data);
            if (res) {
                if (res.r) {
                    uploadLocalFileShow.value = false;
                    ElNotify({ type: "success", message: "导入成功" });
                    getTableList(0);
                    window.postMessage({ type: "refreshCurrent" }, window.location.origin);
                } else {
                    if (type) {
                        ElNotify({ type: "warning", message: res.msg });
                    } else {
                        if (isThirdPart.value) {
                            ElNotify({ type: "warning", message: res.msg });
                        } else {
                            if (res.message.indexOf("磁盘空间不足") != -1) {
                                tryShowPayDialog(
                                    1,
                                    "",
                                    window.isAccountingAgent
                                        ? "空间不足！购买专业版账套，立即获取10G超大会计电子档案空间。更多专业版功能如下："
                                        : "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：",
                                    "会计电子档案"
                                );
                            } else {
                                ElNotify({ type: "warning", message: res.msg });
                            }
                        }
                    }
                }
                return;
            }
            ElNotify({ type: "warning", message: "网络错误，请重新尝试" });
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "网络错误，请重新尝试" });
        })
        .finally(() => {
            divMainInfoRef.value?.changeImportState(false);
            setTimeout(() => (localFlag = false), 200);
        });
};
const beginRestore = (fileName: string) => {
    linkScmDescShow.value = isRelation;
    restoreTipShow.value = true;
    filename.value = fileName;
};
//专业版直接恢复
const restoreTipConfirm = () => {
    restoreTipShow.value = false;
    if (!window.isProSystem && !window.isErp && !isThirdPart.value && trialStatusStore.isTrial && coverState.value) {
        ElConfirm("恢复后将按照恢复的账套重新计算时间哦~").then((r: boolean) => {
            if (r) {
                execute();
            }
        });
    } else {
        execute();
    }
};
const execute = () => {
    AppConfirmDialog(1020).then((r: any) => {
        if (r) {
            useLoading().enterLoading("备份恢复进行中，请稍候...");
            const subUrl = `?fileName=${encodeURIComponent(filename.value)}&dontCover=${coverState.value}&asId=${asid}&serviceid=${getServiceId()}`;
            let url = "/api/Backup/ProcessRestoreV2" + subUrl;

            request({
                url: url,
                method: "post",
            })
                .then((res: IResponseModel<IProcessRestoreResult>) => {
                    if (res.state === 1000) {
                        var newasid = res.data.asid;
                        if (isThirdPart.value) {
                            change2newasid(newasid);
                        } else {
                            change2newasid(newasid);
                        }
                    } else if (res.state === 2000 && res.subState === 8) {
                        if(res.msg === "您的账套数量已超过已购买账套数量。"){
                           proOverFlowText.value = "您的账套数量已超过已购买账套数，建议您去增购~";
                           proOverFlowShow.value = true;
                        }else{
                            ElConfirm(res.msg, true, () => {}, "提示", { confirmButtonText: "知道了", cancelButtonText: "" });
                        }
                    } else if (res.subState === 1 && res.msg === "Overflow") {
                        ElNotify({ type: "warning", message: "亲，您的应用创建账套的数量已经超过限额，请联系管理员!" });
                    } else {
                        ElNotify({ type: "warning", message: res.msg ?? "恢复失败，请稍候重试" });
                    }
                })
                .catch((err: any) => {
                    if (err.response?.status === 400) {
                        proOverFlowText.value = err.response.data;
                        proOverFlowShow.value = true;
                    }
                })
                .finally(() => {
                    useLoading().quitLoading();
                });
        }
    });
};

watch(divMainInfoData, (val) => {
    val.forEach((item, index) => {
        if (item.progress != 100) {
            getFileRecord(item.fileName, 0, index);
        }
    });
});
watch(divAsyncFileInfoDAata, (val) => {
    val.forEach((item, index) => {
        if (item.progress != 100) {
            getFileRecord(item.fileName, 31, index);
        }
    });
});
watch(divExcelMainInfoData, (val) => {
    val.forEach((item, index) => {
        if (item.progress != 100) {
            getFileRecord(item.fileName, 10, index);
        }
    });
});
watch(divPdfMainInfoData, (val) => {
    val.forEach((item, index) => {
        if (item.progress != 100) {
            getFileRecord(item.fileName, 18, index);
        }
    });
});
watch(divAuditMainInfoData, (tableData) => {
    noShowAuditTable.value = tableData.length === 0;
    if (tableData.length > 0) {
        tableData.forEach((item, index) => {
            if (item.progress != 100) {
                getFileRecord(item.fileName, 24, index);
            }
        });
    }
});

// 归档操作
interface IGetFileRecord {
    asid: number;
    fileName: string;
    createDate: string;
    fileSize: number;
    creator: string;
    createBy: number;
    type: number;
    progress: number;
    expiredDate: string;
    fileSource?: any;
}
const showBackupTipDialog = () => {
    if (!isHideBarcode.value) {
        dialogBackupRef.value?.showBackupingDialog();
    }
};
const showSpaceDialog = (usedSpace: number, totalSpace: number) => {
    showNotEnoughDiskDialog();
    spaceState.value = sizeFormatter(usedSpace) + "/" + sizeFormatter(totalSpace);
};
const showNotEnoughDiskDialog = () => {
    notEnoughDiskShow.value = true;
};
const insertRow = (row: IProcessBack, type: "pdf" | "excel" | "audit" | "zip") => {
    let fileType: 0 | 24 | 18 | 10 | 31 = 0;
    if (type === "audit") {
        divAuditMainInfoData.value.unshift(row);
        noShowAuditTable.value = false;
        fileType = 24;
        handleReload(24);
        return;
    }
    if (type === "pdf") {
        divPdfMainInfoData.value.unshift(row);
        fileType = 18;
    } else if (type === "excel") {
        divExcelMainInfoData.value.unshift(row);
        fileType = 10;
    } else if (type === "zip") {
        divMainInfoData.value.unshift(row);
        fileType = 0;
    }
    getFileRecord(row.fileName, fileType, 0);
    if (divMainInfoData.value.length + divPdfMainInfoData.value.length + divExcelMainInfoData.value.length === 1) {
        showBackupTipDialog();
    }
};
const getFileRecord = (filename: string, type: 0 | 31 | 10 | 18 | 24, index: number) => {
    request({ url: "/api/Backup/GetFileRecord?filename=" + encodeURIComponent(filename), method: "post" })
        .then((res: IResponseModel<IGetFileRecord>) => {
            if (res.state === 1000) {
                filename = res.data.fileName;
                let tableData: ITableData[] = [];
                switch (type) {
                    case 0:
                        tableData = divMainInfoData.value;
                        break;
                    case 31:
                        tableData = divAsyncFileInfoDAata.value;
                        break;
                    case 10:
                        tableData = divExcelMainInfoData.value;
                        break;
                    case 18:
                        tableData = divPdfMainInfoData.value;
                        break;
                    case 24:
                        tableData = divAuditMainInfoData.value;
                        break;
                }
                const row: any = tableData[index];
                const progress = res.data.progress;
                if (!filename) {
                    ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
                    tableData.splice(index, 1);
                    if (useLoading().isLoading) {
                        useLoading().quitLoading();
                    }
                    return;
                }
                if (progress === 0 && !!filename) {
                    setTimeout(function () {
                        getFileRecord(filename, type, index);
                    }, 5000);
                    return;
                }

                if (type !== 31) {
                    clearTempFiles(res.data, type, index);
                }
                res.data.creator = tableData[0].creator;
                if (type === 31) {
                    res.data.fileSource = tableData[index].fileSource;
                }
                const keys = Object.keys(res.data);
                keys.forEach((key: any) => {
                    row[key] = (res.data as any)[key];
                });

                if (progress !== 100 && progress !== -1) {
                    setTimeout(function () {
                        getFileRecord(filename, type, index);
                    }, 5000);
                } else {
                    if (useLoading().isLoading) {
                        useLoading().quitLoading();
                    }
                }
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "备份失败，请稍后重试" });
            let tableData: ITableData[] = [];
            switch (type) {
                case 0:
                    tableData = divMainInfoData.value;
                    break;
                case 31:
                    tableData = divAsyncFileInfoDAata.value;
                    break;
                case 10:
                    tableData = divExcelMainInfoData.value;
                    break;
                case 18:
                    tableData = divPdfMainInfoData.value;
                    break;
                case 24:
                    tableData = divAuditMainInfoData.value;
                    break;
            }
            if (useLoading().isLoading) {
                useLoading().quitLoading();
            }
        });
};

function clearTempFiles(data: IGetFileRecord, gridId: 0 | 31 | 10 | 18 | 24, index: number) {
    if (data.progress == 100 && data.expiredDate) {
        let rows: ITableData[] = [];
        switch (gridId) {
            case 0:
                rows = divMainInfoData.value;
                break;
            case 31:
                rows = divAsyncFileInfoDAata.value;
                break;
            case 10:
                rows = divExcelMainInfoData.value;
                break;
            case 18:
                rows = divPdfMainInfoData.value;
                break;
            case 24:
                rows = divAuditMainInfoData.value;
                break;
        }
        for (let i = rows.length - 1; i >= 0; i--) {
            if (rows[i].expiredDate && i !== index) {
                rows.splice(i, 1);
            }
        }
    }
}

const toDiskCenter = () => {
    // console.log("购买磁盘空间");
};
const reloadBackupData = () => {
    getTableList(0);
};
onMounted(() => {
    window.addEventListener("reloadBackupData", reloadBackupData);
});
onUnmounted(() => {
    window.removeEventListener("reloadBackupData", reloadBackupData);
});
</script>

<style lang="less" scoped>
@import "@/style/Settings/Backup.less";
.backup-content {
    width: 100%;
    position: relative;
    &:before {
        content: "";
        position: absolute;
        z-index: 0;
        top: 47px;
        left: 0;
        right: 0;
        border-bottom: 1px solid var(--el-border-color-light);
    }
    .main-content {
        margin: 0 auto;
    }
}
body[erp] .content {
    .main-content {
        &.erp {
            :deep(.div-audit-first) {
                margin-top: 30px;
            }
            overflow: visible;
            :deep(.div-audit-last) {
                margin-top: 30px;
                overflow: visible;
                .main-center {
                    overflow: visible;
                    .table {
                        &.custom-table {
                            height: auto;
                        }
                    }
                }
            }
        }
    }
}
</style>
