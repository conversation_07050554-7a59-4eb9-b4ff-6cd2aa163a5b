<template>
    <el-dialog v-model="showAddVendor" class="custom-confirm dialogDrag" title="新增供应商" center :width="isErp?'742px':'440px'" @close="close">
        <div v-if="isErp" class="vendorAddContent" v-dialogDrag>
            <el-form ref="vendorRef" :model="vendorAddForm" :rules="rules">
                <el-row class="isRow">
                    <el-form-item label="供应商编号：" :required="true" prop="vendorCode">
                        <Tooltip :content="vendorAddForm.vendorCode" :isInput="true">
                            <el-input v-model="vendorAddForm.vendorCode" class="input" :disabled="codeDisabled"></el-input>
                        </Tooltip>
                    </el-form-item>
                    <el-form-item label="供应商名称：" :required="true" prop="vendorName">
                        <el-input v-model="vendorAddForm.vendorName"
                            v-if="vendorNameTextareaShow"
                            ref="vendorNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur"
                            class='vendorName-textarea'  
                            @input="InputMaxLength"
                        />
                        <Tooltip :content="vendorAddForm.vendorName" :isInput="true" v-else>
                            <el-input type="text" class="input" v-model="vendorAddForm.vendorName" @focus="inputTypeFocus" />
                        </Tooltip>
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="供应商类别：" prop="vendorType">
                        <el-select v-model="vendorAddForm.vendorType" class="input"></el-select>
                    </el-form-item>
                    <el-form-item label="联系人：" :required="true" prop="vendorManager">
                        <Tooltip :content="vendorAddForm.vendorManager" :isInput="true">
                            <el-input v-model="vendorAddForm.vendorManager" class="input"></el-input>
                        </Tooltip>
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="联系电话：" prop="vendorManagerPhone">
                        <Tooltip :content="vendorAddForm.vendorManagerPhone" :isInput="true">
                            <el-input v-model="vendorAddForm.vendorManagerPhone" class="input"></el-input>
                        </Tooltip>
                    </el-form-item>
                    <el-form-item label="微信/QQ：" prop="vendorCancelDate">
                        <Tooltip :content="vendorAddForm.concatWays" :isInput="true">
                            <el-input v-model="vendorAddForm.concatWays" class="input"></el-input>
                        </Tooltip>
                    </el-form-item>
                </el-row>
            </el-form>
        </div>
        <div v-else class="add-content">
            <div class="add-main">
                <div class="add-input-item">
                    <span class="label">供应商编码：</span>
                    <input
                        type="text"
                        v-model="vendorAddForm.vendorCode"
                    />
                </div>
                <div class="add-input-item">
                    <span class="label">供应商名称：</span>
                    <!-- <input
                        type="text"
                        v-model="addOpTypeForm.name"
                        @input="handleAAInput(LimitCharacterSize.Name, $event, dialogName + '名称', 'name', changeFormData)"
                    /> -->
                    <el-autocomplete
                        ref="oppositePartyRef"
                        v-model="vendorAddForm.vendorName"
                        :fetch-suggestions="querySearch"
                        :prop="[{ required: true, trigger: ['change'] }]"
                        :trigger-on-focus="false"
                        placeholder=" "
                        class="oppositePartyInput"
                        :debounce="300"
                        :teleported="true"
                        :fit-input-width="true"
                        popper-class="opposite-autocomplete-popper"
                        @select="(val:any)=>{vendorAddForm.vendorName = val.value}"
                        @input="InputMaxLength"
                        @keypress="PressMaxLength"
                    >
                        <template #default="{ item }">
                            <div class="value">{{ item.value }}</div>
                        </template>
                    </el-autocomplete>
                </div>
                <div class="add-input-item">
                    <span class="label">备注：</span>
                    <input
                        type="text"
                        v-model="vendorAddForm.note"
                        @input="handleAAInput(LimitCharacterSize.Note, $event, '备注', 'note', changeSearchInfo)"
                        @paste="handleAAPaste(LimitCharacterSize.Note, $event)"
                    />
                </div>
            </div>
        </div>
        <div class="buttons">
            <a class="button" @click="handleCancel">取消</a>
            <a class="button solid-button ml-10" @click="throttleSaveVendor">保存</a>
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, watchEffect, nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import Tooltip from "@/components/Tooltip/index.vue";
import type { ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import { isNumberOrLetter } from "@/util/validator";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";

export interface IVendorAddForm {
    vendorCode: string;
    vendorName: string;
    vendorType:string;
    vendorManager: string;
    vendorManagerPhone: string;
    concatWays:string;
    note?: string;
}

const props = withDefaults(
    defineProps<{ 
        isShow: boolean; 
        vendorCode: string;
        codeDisabled: boolean; 
        vendorNameAuto: string;
    }>(), 
    {
        isShow: false,
        vendorCode: "",
        codeDisabled: false,
        vendorNameAuto: "",
    }
);
const emits = defineEmits<{ 
    (e: "cancel"): void; 
    (e: "save", data: IVendorAddForm): void; 
    (e: "close"): void; 
}>();
const vendorNameTextareaShow = ref(false);
const vendorNameInputRef = ref();

const inputTypeBlur = () => {
    vendorNameTextareaShow.value = false;
};

const inputTypeFocus = () => {
    vendorNameTextareaShow.value = true;
    nextTick(()=>{
        vendorNameInputRef.value?.focus();
    })
};

const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};

const changeSearchInfo = (key: string, value: string) => {
    vendorAddForm.note = value;
};

const querySearch = (queryString: string, cb: any) => {
    if (!queryString.trim()) return;
    getCompanyList(1060, queryString, cb, queryParams);
};
function InputMaxLength(value: string) {
    if (value.length > LimitCharacterSize.Name) {
        let timer = setTimeout(() => {
            vendorAddForm.vendorName = value.slice(0, LimitCharacterSize.Name);
            clearTimeout(timer);
        }, 0)
        ElNotify({
            type: "warning",
            message: `亲，供应商名称不能超过${LimitCharacterSize.Name}个字符!`,
        });
    } else {
        vendorAddForm.vendorName = value;
    }
}
function PressMaxLength(e: any) {
    const { value, selectionStart,selectionEnd } = e.target as HTMLInputElement;
    if (value.length > LimitCharacterSize.Name - 1) {
        if (selectionStart === selectionEnd) {
            e.preventDefault();
            ElNotify({
                type: "warning",
                message: `亲，供应商名称不能超过${LimitCharacterSize.Name}个字符!`,
            });
        }
    }
}

const isErp = ref(window.isErp);
const vendorRef = ref();
const showAddVendor = ref(false);
const vendorAddForm = reactive<IVendorAddForm>({
    vendorCode: "",
    vendorName: "",
    vendorType:"",
    vendorManager: "",
    vendorManagerPhone: "",
    concatWays:'',
});
let timer = ref(0);
function throttleSaveVendor() {
    if (timer.value) {
        return;
    }
    SaveVendor();
    timer.value = setTimeout(() => {
        timer.value = 0; 
    }, 2000); 
}
const rules = {
      vendorName: [
        { required: true, message: '请输入供应商名称', trigger: 'blur' }
      ],
      vendorManager: [
        { required: true, message: '请填写联系人', trigger: 'blur' }
      ]
    };
const SaveVendor = () => {
    if (!vendorAddForm.vendorCode) {
        ElNotify({
            type: "warning",
            message: "请录入供应商编号！",
        });
        return;
    }
    if(!isNumberOrLetter(vendorAddForm.vendorCode)) {
        ElNotify({ type: "warning", message: "供应商编码不是数字和字母组合，请修改后重试~" });
        return false
    }
    if(vendorAddForm.vendorCode.length > 18) {
        ElNotify({ type: "warning", message: "供应商编码为不超过18位的字母或数字组合！" });
        return false
    }
    if (!vendorAddForm.vendorName) {
        ElNotify({
            type: "warning",
            message: "请录入供应商名称！",
        });
        return;
    }
    if (vendorAddForm.vendorName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return;
    }
    if (!vendorAddForm.vendorManager && isErp.value) {
        ElNotify({
            type: "warning",
            message: "请填写联系人",
        });
        return;
    }
    emits("save", vendorAddForm);
};
const handleCancel = () => {
    resetFields();
    emits("cancel");
};

const resetFields = () => {
    vendorAddForm.vendorName = "";
    vendorRef.value?.resetFields();
};
defineExpose({
    resetFields,
});
watchEffect(() => {
    showAddVendor.value = props.isShow;
    vendorAddForm.vendorCode = props.vendorCode;
    InputMaxLength(props.vendorNameAuto);
});
const close = () => {
    emits("close");
}
</script>
<style lang="less" scoped>
@import "@/style/Functions.less";
.vendorAddContent {
    padding: 30px 73px 12px;
    .isRow {
        .vendorName-textarea{
            width: 200px;
            height: 33px;
            :deep(.el-textarea__inner){
                z-index:1000;
            }
        }
        .input{
            width: 200px; 
            height: 32px;
            :deep(.el-input__inner){
                white-space: nowrap;
                overflow: hidden; 
                text-overflow: ellipsis; 
            }
        }
        min-height: 52px;
        & :deep(.el-form-item) {
            align-items: center;
            & .el-form-item__label {
                height:32px;
                width: 98px;
                padding:0px;
                // text-align: right;
            }
        }
    }
}
.add-content {
    .add-main {
        box-sizing: border-box;
        padding: 37px 70px;
        .add-input-item {
            margin-top: 20px;
            > span.label {
                text-align: right;
                width: 85px;
                display: inline-block;
                margin-right: 5px;
            }
            .detail-el-input(200px, 32px);
            &:first-child {
                margin-top: 0;
            }
        }
    }

    input {
        .detail-original-input(200px, 32px);
    }
}
</style>