<template>
    <div class="TaxDiff-content">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <el-select id="declareYear" v-model="diffPeriod" style="width: 88px">
                    <el-option v-for="item in diffPeriodList" :value="item.value" :key="item.value">{{ item.label }}</el-option>
                </el-select>
            </div>
            <div class="main-tool-right">
                <a class="button" @click="closeTaxDiff">返回</a>
            </div>
        </div>
        <div class="main-center">
            <Table
                :data="tableData"
                :columns="columns"
                :pageIsShow="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="paginationData.total"
                :current-page="paginationData.currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :scrollbarShow="true"
                :tableName="setModule"
            ></Table>
        </div>
    </div>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { usePagination } from "@/hooks/usePagination";
import { getUrlSearchParams } from "@/util/url";
import { request } from "@/util/service";
import { ref, computed, watch } from "vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "SalaryTaxDiff";
const props = defineProps<{
    periodList: any;
    period: string;
}>();
const emits = defineEmits(["closeTaxDiff"]);
const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();

const diffPeriodList = computed(() => {
    return props.periodList;
});

const diffPeriod = computed(() => {
    return props.period;
});
function closeTaxDiff() {
    emits("closeTaxDiff");
}

const columns = ref<Array<IColumnProps>>([
    { 
        label: "编号", 
        prop: "empCode", 
        align: "left", 
        headerAlign: "left", 
        width: getColumnWidth(setModule, 'empCode') 
    },
    { 
        label: "姓名", 
        prop: "empName", 
        align: "left", 
        headerAlign: "left", 
        width: getColumnWidth(setModule, 'empName') 
    },
    { 
        label: "部门", 
        prop: "empDepartment", 
        align: "left", 
        headerAlign: "left", 
        width: getColumnWidth(setModule, 'empDepartment') 
    },
    {
        label: "申报个税",
        prop: "declareTax",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'declareTax'),
        formatter: (row: any) => {
            if (row.declareTax != null) {
                return row.declareTax.toFixed(2);
            } else {
                return "";
            }
        },
    },
    {
        label: "工资表个税",
        prop: "salaryTax",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'salaryTax'),
        formatter: (row: any) => {
            if (row.salaryTax != null) {
                return row.salaryTax.toFixed(2);
            } else {
                return "";
            }
        },
    },
    {
        label: "本次个税差异",
        prop: "taxDiff",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'taxDiff'),
        formatter: (row: any) => {
            if (row.taxDiff != null) {
                return row.taxDiff.toFixed(2);
            } else {
                return "";
            }
        },
    },
    {
        label: "累计个税差异",
        prop: "taxYearDiff",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'taxYearDiff'),
        formatter: (row: any) => {
            if (row.taxYearDiff != null) {
                return row.taxYearDiff.toFixed(2);
            } else {
                return "";
            }
        },
    },
]);

const tableData = ref();
function queryTaxDiffList() {
    const params = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        period: diffPeriod.value,
    };
    request({
        url: `/api/SalaryTaxDifference/pagingList?` + getUrlSearchParams(params),
    }).then((res: any) => {
        tableData.value = res.data.data;
        paginationData.total = res.data.total;
    });
}

watch(
    [() => diffPeriod.value],
    () => {
        queryTaxDiffList();
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
.TaxDiff-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}
</style>
