<template>
  <div
    class="content"
    :class="isErp ? 'erp-content' : ''">
    <div
      class="title"
      style="height: 25px"></div>
    <div class="main-content align-center">
      <div class="main-content-buttons">
        <div class="buttons">
          <a
            class="solid-button"
            @click="handlePrint">
            打印
          </a>
          <a
            class="button ml-10"
            v-show="!isErp"
            @click="handleClose">
            关闭
          </a>
        </div>
      </div>
      <div class="slot-mini-content">
        <div class="main-content-title">
          <span>{{ txtName }}</span>
        </div>
        <div class="main-content-info">
          <span>
            账户：
            <span>{{ txtAccount + " 币别：" + fcName }}</span>
          </span>
          <span>
            <span>{{ txtCDDate }}</span>
          </span>
          <span>
            NO.
            <span>{{ txtLineSn }}</span>
          </span>

          <!-- <div class="float-clear"></div> -->
        </div>
        <div class="main-content-table">
          <table>
            <tbody>
              <tr>
                <td>摘要</td>
                <td colspan="4">
                  <ToolTip
                    :content="txtDescription"
                    :teleported="true"
                    placement="right"
                    :maxWidth="1490">
                    <span class="text-hidden">{{ txtDescription }}</span>
                  </ToolTip>
                </td>
              </tr>
              <tr>
                <td>大写金额</td>
                <td colspan="2">
                  <span>{{ txtMoneyC }}</span>
                </td>
                <td>小写金额</td>
                <td>
                  <span>{{ txtMoney }}</span>
                </td>
              </tr>
              <tr>
                <td>往来单位</td>
                <td colspan="2">
                  <ToolTip
                    :content="txtOpposite"
                    :teleported="true"
                    placement="right"
                    :maxWidth="720">
                    <span class="text-hidden">{{ txtOpposite }}</span>
                  </ToolTip>
                </td>
                <td>结算方式</td>
                <td>
                  <span>{{ txtPayment }}</span>
                </td>
              </tr>
              <tr>
                <td>收支类别</td>
                <td colspan="2">
                  <span class="text-hidden">{{ txtIEType }}</span>
                </td>
                <td>票据号</td>
                <td>
                  <span>{{ txtReceiptNo }}</span>
                </td>
              </tr>
              <tr
                id="opLine"
                v-show="opLineShow">
                <template v-if="projectShow">
                  <td class="project">项目</td>
                  <td
                    class="project"
                    :colspan="projectColspan">
                    <span>{{ txtProject }}</span>
                  </td>
                </template>
                <template v-if="departmentShow">
                  <td class="department">部门</td>
                  <td
                    class="department"
                    :colspan="departmentColspan">
                    <span>{{ txtDepartment }}</span>
                  </td>
                </template>
              </tr>
              <tr>
                <td>备注</td>
                <td colspan="4">
                  <ToolTip
                    :content="txtNote"
                    :teleported="true"
                    placement="right"
                    :maxWidth="1490">
                    <span class="text-hidden">{{ txtNote }}</span>
                  </ToolTip>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div
          class="main-content-footer"
          style="display: flex; justify-content: space-between">
          <div class="footer-list">
            <span
              style="text-align: left"
              v-for="item in bottomTextList"
              :key="item">
              {{ item }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: "JournalPage",
  };
</script>
<script setup lang="ts">
  import { ref, onActivated } from "vue";
  import { useRoute } from "vue-router";
  import { getCookie } from "@/util/cookie";
  import { getUrlSearchParams, globalPostPrint, globalWindowOpenPage } from "@/util/url";
  import { getLocalStorage } from "@/util/localStorageOperate";
  import { request, type IResponseModel } from "@/util/service";
  import { erpCloseTab } from "@/util/erpUtils";
  import { useRouterArrayStoreHook, type IRouterModel } from "@/store/modules/routerArray";
  import ToolTip from "@/components/Tooltip/index.vue";
  import usePrint from "@/hooks/usePrint";

  const routerArrayStore = useRouterArrayStoreHook();

  interface IPageData {
      ac_no: string;
      ac_name: string;
      actext: string;
      income: number;
      expenditure: number;
      journalType: string;
      money: string;
      moneyC: string;
      cd_date: string;
      line_sn: number;
      j_type: number;
      lineSn: string;
      cddate: string;
      description: string;
      oppositeParty: string;
      payment: string;
      project: string;
      department: string;
      ietype: string;
      receiptNo: string;
      note: string;
      createdBy: string;
      fc_name: string;
  }

  const route = useRoute();

  const isErp = ref(window.isErp);
  const projectShow = ref(false);
  const departmentShow = ref(false);
  const opLineShow = ref(false);
  const projectColspan = ref(2);
  const departmentColspan = ref();
  const jType = ref(1010);

  const txtName = ref("");
  const txtAccount = ref("");
  const txtLineSn = ref("");
  const txtCDDate = ref("");
  const txtDescription = ref("");
  const txtMoneyC = ref("");
  const txtMoney = ref("");
  const txtOpposite = ref("");
  const txtPayment = ref("");
  const txtIEType = ref("");
  const txtReceiptNo = ref("");
  const txtProject = ref("");
  const txtDepartment = ref("");
  const txtNote = ref("");
  const txtCashier = ref("");
  const fcName = ref("");
  const bottomTextList = ref<string[]>([]);

  const handleInit = () => {
      const journalType = route.query.JOURNAL_TYPE ?? "";
      const title = journalType === "EXPENDITURE" ? "付款凭据" : "收款凭据";
      const currentRoute = routerArrayStore.routerArray.find((item) => item.name === "JournalPage") as IRouterModel;
      currentRoute && (currentRoute.title = title);
      const acId = route.query.AC_ID ?? "";
      const cdDate = route.query.CD_DATE ?? "";
      const lineSn = route.query.LINE_SN ?? "";
      const createdDate = route.query.CREATED_DATE ?? "";
      const params = { acId, cdDate, createdDate };
      request({ url: "/api/Journal?" + getUrlSearchParams(params) }).then((res: IResponseModel<IPageData>) => {
          if (res.state !== 1000) return;
          const data = res.data;
          txtName.value = data.journalType;
          txtAccount.value = data.actext;
          txtLineSn.value = data.lineSn;
          txtCDDate.value = data.cddate;
          txtDescription.value = data.description;
          txtMoneyC.value = data.moneyC;
          txtMoney.value = data.money;
          txtOpposite.value = data.oppositeParty;
          txtPayment.value = data.payment;
          txtIEType.value = data.ietype;
          txtReceiptNo.value = data.receiptNo;
          txtProject.value = data.project;
          txtDepartment.value = data.department;
          txtNote.value = data.note;
          jType.value = data.j_type;
          txtCashier.value = data.createdBy;
          fcName.value = data.fc_name;
          if (!getLocalStorage("receiptPrint")) {
              if (getCookie("JournalIsPrintProject") == "true") {
                  projectShow.value = true;
                  if (getCookie("JournalIsPrintDepartment") != "true") {
                      projectColspan.value = 4;
                  }
              } else {
                  projectShow.value = false;
              }
              if (getCookie("JournalIsPrintDepartment") == "true") {
                  departmentShow.value = true;
                  if (getCookie("JournalIsPrintProject") != "true") {
                      departmentColspan.value = 4;
                  }
              } else {
                  departmentShow.value = false;
              }
              if (getCookie("JournalIsPrintProject") == "true" || getCookie("JournalIsPrintDepartment") == "true") {
                  opLineShow.value = true;
              } else {
                  opLineShow.value = false;
              }
              const JournalIsPrintAccountantName = !(getCookie("JournalIsPrintAccountantName") == "false");
              if(JournalIsPrintAccountantName) bottomTextList.value.push(`会计：${decodeURI(getCookie("JournalAccountantName") ?? "")}`);
              const JournalIsPrintCashierName = !(getCookie("JournalIsPrintCashierName") == "false");
              if(JournalIsPrintCashierName) bottomTextList.value.push(`出纳：${JournalIsPrintCashierName ? txtCashier.value :''}`);
              const JournalIsPrintUsedName = !(getCookie("JournalIsPrintUsedName") == "false");
              if(JournalIsPrintUsedName) bottomTextList.value.push(`经手人：${decodeURI(getCookie("JournalUsedName") ?? "")}`);
          } else {
              const receiptPrint = JSON.parse(getLocalStorage("receiptPrint")!);
              if (!receiptPrint.isPrintCashierName) {
                  txtCashier.value = "";
              }
              if (receiptPrint.isPrintProject) {
                  projectShow.value = true;
                  if (!receiptPrint.isPrintDepartment) {
                      projectColspan.value = 4;
                  }
              } else {
                  projectShow.value = false;
              }
              if (receiptPrint.isPrintDepartment) {
                  departmentShow.value = true;
                  if (!receiptPrint.isPrintProject) {
                      departmentColspan.value = 4;
                  }
              } else {
                  departmentShow.value = false;
              }
              if (receiptPrint.isPrintProject || receiptPrint.isPrintDepartment) {
                  opLineShow.value = true;
              } else {
                  opLineShow.value = false;
              }
              bottomTextList.value = receiptPrint.bottomTextList.map((item: string) => {
                  return item === "#CASHIER" ? item.replace("#CASHIER", `出纳：${txtCashier.value}`) : item;
              });
          }
      });
  };

  const handleClose = () => {
      if (window.isErp) {
          erpCloseTab("/Cashier/JournalPage", "日记账");
      } else {
          const from = route.query.from as string;
          let routerModel = routerArrayStore.routerArray.find((item) => item.path === from) as IRouterModel;
          if (routerModel) {
              globalWindowOpenPage(routerModel.fullPath, routerModel.title);
              setTimeout(() => {
                  routerArrayStore.removeRouter("/Cashier/JournalPage");
              });
          } else {
              routerArrayStore.removeRouter("/Cashier/JournalPage");
          }
      }
  };

  const handlePrint = () => {
      const { printInfo } = usePrint("receipt", "", { pageType: 2 }, false, false);
      if (!getLocalStorage("receiptPrint"))
          printInfo.value.bottomTextList = bottomTextList.value;
      const params = {
          cdAccount: route.query.AC_ID ?? "",
          lineSn: route.query.LINE_SN ?? "",
          cdDate: route.query.CD_DATE ?? "",
          createdDate: route.query.CREATED_DATE ?? "",
          seniorModelJson: printInfo.value,
      };
      const urlPath = jType.value === 1010 ? "PrintCash" : "PrintDeposit";
      globalPostPrint("/api/Journal/" + urlPath, params);
  };

  onActivated(() => {
      handleInit();
  });
</script>

<style lang="less" scoped>
  @import "@/style/SelfAdaption.less";

  .content {
    .main-content {
      color: var(--font-color);

      .slot-mini-content {
        width: 1000px;
      }

      .main-content-buttons {
        padding: 10px 20px 10px 20px;
        border-bottom: 1px solid var(--border-color);
        box-sizing: border-box;
        width: 100%;

        .buttons {
          width: 1000px;
          margin: 0 auto;
          text-align: left;
        }
      }

      .main-content-title {
        font-size: 22px;
        font-weight: bold;
        text-align: center;
        margin-top: 30px;
      }

      .main-content-info {
        margin: 22px 40px 17px 40px;
        font-size: var(--h4);
        display: flex;
        align-items: center;
        justify-content: space-between;

        & > span {
          flex: 1;

          &:nth-child(1) {
            text-align: left;
          }

          &:nth-child(2) {
            text-align: center;
          }

          &:nth-child(3) {
            text-align: right;
          }
        }
      }

      .main-content-table {
        margin-left: 20px;
        margin-right: 20px;
        border-top: 1px solid var(--border-color);
        border-left: 1px solid var(--border-color);

        table {
          width: 960px;
          font-size: var(--h4);
          table-layout: fixed;
          border-spacing: 0px;

          tr {
            height: 57px;
            line-height: 57px;

            td {
              border-right: 1px solid var(--border-color);
              border-bottom: 1px solid var(--border-color);

              &:nth-child(1),
              &:nth-child(3) {
                text-align: center;
              }

              &:nth-child(2),
              &:nth-child(4) {
                text-align: left;
                padding-left: 20px;
              }
            }
          }
        }
      }

      .main-content-footer {
        padding: 18px 40px 31px 40px;
        font-size: var(--h4);

        .footer-list {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 10px;
          width: 100%;

          span {
            &:nth-child(1) {
              text-align: left;
            }

            &:nth-child(2) {
              text-align: center;
            }

            &:nth-child(3) {
              text-align: right;
            }
          }
        }

        .mian-footer-broke {
          margin-left: 285px;
          margin-right: 96px;
          float: right;
        }
      }
    }

    &.erp-content {
      .main-content {
        overflow: auto;
      }
    }
  }

  .float-clear {
    display: block;
    clear: both;
  }

  .text-hidden {
    max-width: 100%;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }
</style>
