//字体
@mixin set-font($color: var(--font-color), $font-size: var(--font-size), $line-height: var(--line-height)) {
  color: $color;
  font-size: $font-size;
  line-height: $line-height;
}

// 绿色圆柱体
@mixin common-before {
  content: "";
  display: inline-block;
  width: 3px;
  height: 12px;
  background-color: var(--main-color);
  border-radius: 2px;
  vertical-align: middle;
}

//按钮
@mixin base-button($height, $width) {
  height: $height;
  width: $width;
  line-height: $height;
  font-size: 13px;
  display: inline-block;
  cursor: pointer;
  text-align: center;
  padding: 0;
  outline: none;
  @include set-transition;
}

//实心按钮
@mixin solid($color) {
  color: var(--white);
  border-color: $color;
  background-color: $color;
}

//动画
@mixin set-transition($time: var(--transition-time)) {
  transition: $time;
}

//hover和active效果
@mixin hover($color) {
  &:hover {
      @include solid($color);
  }
}

@mixin active($color) {
  &:active {
    @include solid($color);
  }
}