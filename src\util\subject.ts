export const sixMajorCodeList = (accountStand: number): string[] => {
    if (accountStand === 1 || accountStand === 2) {
        return ["1122", "2202", "2203", "1123", "1221", "2241"];
    }
    if (accountStand === 3) {
        return ["1122", "2202", "2203", "1121", "1141", "2209"];
    }
    if (accountStand === 4 || accountStand === 5) { //农民合作社只有应收款和应付款
        return ["113", "211"];
    }
    if (accountStand === 6){ //工会只有其他应收款和其他应付款
        return [ "135", "215"];
    }
    if (accountStand === 7) { //农村集体经济只有其他应收款和应付款
        return ["112", "211"];
    }
    return ["1122", "2202", "2203"];
};

export const checkSixMajor = (code: string, accountStandard: number, firstCodeLength: number) => {
    const sixMajorList = sixMajorCodeList(accountStandard) || "0000";
    const firstLevelCode = code?.slice(0, firstCodeLength);
    const isSixMajor = sixMajorList.find((v: string) => v === firstLevelCode);
    return Boolean(isSixMajor);
};
