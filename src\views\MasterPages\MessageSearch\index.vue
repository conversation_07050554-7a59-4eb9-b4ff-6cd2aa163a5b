<template>
    <div class="content-search">
        <div class="search-title">查询导航</div>
        <div class="search-list">
            <div class="search-block" v-for="item in list" :key="item.id" @click="globalWindowOpen(item.url)">
                <div class="search-block-img">
                   <span>{{ item.imgText }}</span>
                </div>
                <div class="search-block-bd">
                    <div class="search-block-name">{{ item.name }}</div>
                    <div class="search-block-pos">{{ item.detail }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "MessageSearch",
};
</script>

<script setup lang="ts">
import { globalWindowOpen } from "@/util/url";
const list = [
    {
        id: 1,
        imgText: '税',
        name: '办税日历',
        detail: '国家税务总局',
        url: 'https://12366.chinatax.gov.cn/bsfw/calendar/main',
    },
    {
        id: 2,
        imgText: '利',
        name: '利率查询',
        detail: '中国人民银行',
        url: 'http://www.pbc.gov.cn/zhengcehuobisi/125207/125213/125440/125838/125885/index.html',
    },
    {
        id: 3,
        imgText: '汇',
        name: '汇率查询',
        detail: '中国人民银行',
        url: 'http://www.pbc.gov.cn/zhengcehuobisi/125207/125217/125925/index.html',
    },
    {
        id: 4,
        imgText: '全',
        name: '全国增值税发票查验平台',
        detail: '国家税务总局',
        url: 'https://inv-veri.chinatax.gov.cn/',
    },
    {
        id: 5,
        imgText: '会',
        name: '会计法规',
        detail: '中华人民共和国财政部',
        url: 'https://www.mof.gov.cn/zhengwuxinxi/zhengcefabu/',
    },
    {
        id: 6,
        imgText: '工',
        name: '工商法规',
        detail: '国家市场监督管理总局',
        url: 'https://www.samr.gov.cn/',
    },
    {
        id: 7,
        imgText: '工',
        name: '工商查询',
        detail: '国家企业信用信息公示系统',
        url: 'https://www.gsxt.gov.cn/index.html',
    },
    {
        id: 8,
        imgText: '税',
        name: '税收法规',
        detail: '国家税务总局',
        url: 'https://www.chinatax.gov.cn/',
    },
    {
        id: 9,
        imgText: '证',
        name: '证券法规',
        detail: '中国证券监督管理委员会',
        url: 'http://www.csrc.gov.cn/',
    },
]
</script>

<style scoped lang="less">
    .content-search {
        width: 100%;
        height: 100%;
    }
    .search-title {
        margin-bottom: 20px;
        height: var(--title-height);
        padding-top: 40px;
        box-sizing: border-box;
        color: var(--font-color);
        font-size: var(--h2);
        line-height: 25px;
        font-weight: 700;
        display: flex;
        align-items: flex-start;
        justify-content: center;
    }
    .search-list {
        width: 1000px;
        margin: 0 auto;
        padding: 20px;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }
    .search-block {
        width: 250px;
        height: 42px;
        border: 1px solid #D0D0D0;
        border-radius: 2px;
        margin-right: 20px;
        margin-bottom: 20px;
        padding: 17px 0 17px 24px;
        cursor: pointer;
        color: #333333;
        display: flex;
        align-items: center;
        &:hover {
            -moz-box-shadow: 0 0 10px 0 #dadada;
            -webkit-box-shadow: 0 0 10px 0 #dadada;
            box-shadow: 0 0 10px 0 #dadada;
        }
    }
    .search-block-img {
        background-color: #93D495;
        width: 42px;
        height: 42px;
        font-size: 26px;
        color: white;
        line-height: 37px;
        vertical-align: top;
        text-align: center;
    }
    .search-block-bd {
        flex: 1;
        margin-left: 16px;
        text-align: left;
    }
    .search-block-name {
        font-size: 16px;
        height: 22px;
        margin-top: -2px;
        width: 192px;
    }
    .search-block-pos {
        font-size: 13px;
        height: 18px;
        margin-top: 4px;
    }
</style>