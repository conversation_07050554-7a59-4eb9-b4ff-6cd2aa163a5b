export interface ITableItem {
    userSn: number;
    userId: string;
    userName: string;
    mobile: string;
    userinfo: string;
    pid: string;
    flag: number;
    permission: string;
    unionId: string;
    wxworkUserId: string;
    option: string;
}
export interface IRoleInfo {
    preName: string;
    roleId: number;
    roleName: string;
    functionsState: number[];
}
export interface IEditRoleItem {
    roleId: number;
    roleName: string;
    functionsState: number[];
    isDisabled: boolean;
}
export interface IMenuFunctionItem {
    functionId: number;
    parentId: number;
    functionNo: number;
    functionCode: string;
    fullFunctionCode: string;
    functionName: string;
    level: number;
    order: number;
    isIndeterminate?: boolean;
}
export interface IMenuFunction extends IMenuFunctionItem {
    children: IMenuFunction[];
}

export interface IAccountList {
    asid: number;
    appAsId: string;
    asname: string;
    accountStandard: number;
    locked: boolean;
    needLockPassword: boolean;
    asStartMonth: number;
    asStartYear: number;
}
