import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { getGlobalToken } from "@/util/baseInfo";
import { setTopLocationhref } from "@/util/url";
import { getCookie } from "@/util/cookie";
import { EnterLockedAccountSetDialog } from "@/util/enterLockedAccountSet";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { setSelectedAccount, setSelectedAccountWithoutChange } from "@/util/accountset";
import type { IAccountSetSwitchResult } from "./types";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { getServiceId } from "@/util/proUtils";

export const GetAccountStandardText = (accountStandard: number | string, subAccountStandard: number | string) => {
    switch (accountStandard) {
        case 1:
        case "1":
            return "小企业会计准则";
        case 2:
        case "2":
            switch (subAccountStandard) {
                case 1:
                case "1":
                    return "企业会计准则(2019年未执行新金融准则、新收入准则和新租赁准则)";
                case 2:
                case "2":
                    return "企业会计准则(2019年已执行新金融准则、新收入准则和新租赁准则)";
                default:
                    return "企业会计准则(2007年)";
            }
        case 3:
        case "3":
            return "民间非营利组织会计制度";
        case 4:
        case "4":
            return "农民专业合作社财务会计制度";
        case 5:
        case "5":
            return "农民专业合作社财务会计制度（2023年）";
        case 6:
        case "6":
            return "工会会计制度";
        case 7:
        case "7":
            return "农村集体经济组织会计制度";
        default:
            return "";
    }
};

export const GetAccoundTaxType = (taxType: number | string) => {
    switch (taxType) {
        case 1:
        case "1":
            return "小规模纳税人";
        case 2:
        case "2":
            return "一般纳税人";
        default:
            return "";
    }
};

export const changeAS = (asId: number, isLocked?: boolean, asname?: string) => {
    if (isLemonClient()) {
        change2newasid(asId);
    } else {
        if (useAccountSetStoreHook().accountSet?.asId === asId) {
            setTopLocationhref("/Default/Default?appasid=" + getGlobalToken());
        } else {
            //改变账套选择
            if (isLocked) {
                EnterLockedAccountSet(asId, asname);
            } else {
                setSelectedAccount(asId);
            }
        }
    }
};

export const change2newasid = (asid: string | number, callback?: Function) => {
    const url = `/api/AccountSetOnlyAuth/Switch?asid=${asid}`;
    const notFirstAdminErrorCode = 423;
    request({ url, method: "post" }).then((result: IResponseModel<IAccountSetSwitchResult>) => {
        if (result.data.status === 200) {
            thirdPartNotify(thirtPartNotifyTypeEnum.globalSwitchAccountSet).then(() => {
                if (callback) {
                    callback();
                } else {
                    if (isLemonClient()) {
                        getLemonClient().setSelectedAccount(Number(asid));
                    } else if (window.isProSystem && !window.isAccountingAgent) {
                        const eHost = window.eHost + "/wb/resource-state";
                        request({
                            url: eHost,
                            method: "post",
                            headers: { "Content-Type": "application/x-www-form-urlencoded" },
                            data: {
                                productType:'acc',
                                state: {
                                    userSn: useAccountSetStoreHook().userInfo?.userSn,
                                    asId: asid,
                                },
                                serviceId:getServiceId(),
                            },
                        }).finally(() => {
                            setTopLocationhref("/Default/Default?appasid=" + result.data.appAsid);
                        });
                    } else {
                        setTopLocationhref("/Default/Default?appasid=" + result.data.appAsid);
                    }
                    if (localStorage) {
                        localStorage.removeItem("voucherdraft");
                    }
                }
            });
        } else if (result.data.status == notFirstAdminErrorCode) {
            setTopLocationhref("/Default/Default?appasid=" + getGlobalToken() + "&virtualAsId=" + asid);
        } else {
            ElNotify({ type: "warning", message: "切换账套失败！" });
        }
    });
};

export const global_EnterLockedAccountSet = function (asid: number, asname: string) {
    request({
        url: `/api/AccountSetOnlyAuth/CheckNeedLockPassword?asId=${asid}`,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data) {
                EnterLockedAccountSet(asid, asname);
            } else {
                change2newasid(asid);
            }
        }
    });
};

export const EnterLockedAccountSet = function (asid: number, asname?: string) {
    EnterLockedAccountSetDialog(asid, asname).then((r: boolean) => {
        if (r) {
            setSelectedAccount(asid);
        } else {
            setSelectedAccountWithoutChange(useAccountSetStoreHook().accountSet?.asId!);
        }
    });
};

export const taxBureauLoginList = [
    {value: 9, label: "企业新版登录"},
    {value: 101, label: "代理业务登录"},
];

export const getTaxBureauLoginList = (invoiceAgentArea: number[], taxadId: number) => {
    let result = [];
    if (invoiceAgentArea && invoiceAgentArea.includes(taxadId)) {
        result = JSON.parse(JSON.stringify(taxBureauLoginList));
    } else {
        result = taxBureauLoginList.slice(0, 1);
    }
    return result;
}

export const getUseAgentTaxApi = ()  => {
    return request({
        url: window.invoiceConfigUrl + '?r=' + Math.random(),
    });
}
