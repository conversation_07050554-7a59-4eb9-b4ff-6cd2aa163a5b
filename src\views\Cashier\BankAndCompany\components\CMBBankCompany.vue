<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:acid="autoBankForm.acid"
        v-model:enterprise_number="autoBankForm.enterprise_number"
        v-model:uid="autoBankForm.uid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :uscc-number="props.usccNumber"
        :orderAccountShow="true"
        bankName="招商银行"
        @save-success="saveSuccessHandle"
        @confirm-bank="confirmBank"
    />
    <div class="slot-content align-center edit-temp-table cmb" v-show="cmbConfirmTable">
        <div class="slot-content-mini edit-temp-table-content">
            <div class="autoBankSelect">
                <div class="autoBankItem"><span style="font-weight: 600">第一步：</span>复制右侧密钥</div>
                <div class="autoBankItem"><span style="font-weight: 600">第二步：</span>前往招行网上银行完成签约，获取用户编号</div>
                <div class="autoBankItem">
                    <p style="margin-left: 56px; margin-top: -8px; font-size: 12px; color: rgba(102, 102, 102, 1)">
                        (IE浏览器)：招商银行-系统管理-银企直联-接入设置-业务设置
                    </p>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第三步：</span>确认签约的用户编号</div>
                <div class="autoBankItem">
                    <div style="margin-left: 56px">
                        <span style="color: #fd5055">*</span>招行用户编号：
                        <input type="text" class="autoBankInput" style="width: 240px" v-model="autoBankForm.uid" />
                    </div>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第四步：</span>点击确定，完成授权</div>
            </div>
            <div class="autoBankDescription">
                <div style="margin-left: 40px; padding-top: 20px; line-height: 16px">
                    <span>对称密钥：</span>
                    <span class="copy-file copy-key" @click="handleCopy('sm2EnKey')">复制</span>
                </div>
                <p class="pub-key copy-key" @click="handleCopy('sm2EnKey')">{{ cmbKeyAesVal }}</p>
                <div style="margin-left: 40px; line-height: 16px">
                    <span>用户公钥：</span>
                    <span class="copy-file copy-key" @click="handleCopy('pubKey')">复制</span>
                </div>
                <p class="pub-key copy-key" @click="handleCopy('pubKey')">{{ cmbKeyRsaVal }}</p>
            </div>
            <div class="authButton" style="margin-bottom: 20px">
                <a class="button back-button" @click="handleBack">上一步</a>
                <a class="solid-button-large" @click="hanldeAuthorize">{{ authorizeText }}</a>
            </div>
            <CommonBottom></CommonBottom>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watchEffect, onMounted, watch, nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { BankType } from "@/constants/bankKey";
import { GetBankLink, GetBankUrl } from "@/util/bankType";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams, globalWindowOpen } from "@/util/url";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { appendStyle, copyText, handleCheck, upgradeApi } from "../utils";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, IBankHandleResult, IBankAuthorized, CMBGradeResult, CMBGradeResultData } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import CommonBottom from "./CommonBottom.vue";
import { replaceAll } from "@/util/common";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = [
    "1、请确认公司名称、统一社会信用代码",
    "2、在电脑上插入“招商银行”U盾",
    "3、登录招行网银经办用户查看企业编号、用户编号(右上角操作手册有位置截图)",
];

const props = defineProps({
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const cmbConfirmTable = ref(false);
const impowerValue = ref("立即授权");
const bankUrl = ref("");

const cmbKeyAesVal = ref("");
const cmbKeyRsaVal = ref("");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    mainaccno: "",
    enterprise_number: "",
    uid: "",
});

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "申请中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};
let canAuthorizeNext = true;
const authorizeText = ref("确定");
const hanldeAuthorizeLock = () => {
    canAuthorizeNext = false;
    authorizeText.value = "确认中...";
};
const hanldeAuthorizeUnLock = () => {
    canAuthorizeNext = true;
    authorizeText.value = "确定";
};
const handleBack = () => {
    showMain.value = true;
    cmbConfirmTable.value = false;
    hanldeAuthorizeUnLock();
};
const confirmBank = () => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    handleConfirmLock();
    props.checkAuthorization(props.bankType, replaceAll(autoBankForm.mainaccno, " ", ""), () => {
        request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
                handleConfirmUnLock();
                return;
            }
            //检查当前账户的签约状态
            handleQuery();
        });
    });
};

const handleQuery = () => {
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
            handleConfirmUnLock();
            return;
        }
        const result = res.data;
        if (result.status === 1 || result.status === 2) {
            //已签约成功
            const msg = "本公司“" + autoBankForm.accname + "”已成功开通银企互联功能，签约后的每一笔款项都会实时自动录入该银行日记账哦~";
            ElConfirm(appendStyle(msg), true, () => {}, "授权成功").then(() => {
                handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
            });
            handleConfirmUnLock();
            return;
        }
        if (result.status === 3) {
            //签约中
            ApplyCMBSuccess(result.data);
            return;
        }
        if (result.status === 4) {
            //签约失败
            if (result.data != null) {
                let { Code, Msg } = result.data;
                switch (Code) {
                    case "DCSAS00":
                    case "DCOPN01":
                    case "DCUSR03":
                    case "DCCLT00":
                    case "DCCLT04":
                    case "DCOPN08": //路由字段值为(P0030),从第(7)位开始截取长度为(2)的路由因子失败
                    case "leamonDCOPNcornbrLengthError":
                        ElConfirm("请确认输入的公司名称、信用代码、企业编号、用户编号是否正确", true, () => {}, "授权失败");
                        break;
                    case "DCOPN00":
                    case "DCOPN04":
                        ApplyCMBSuccess(result.data);
                        break;
                    case "DCSAS01":
                    case "DCCLT03":
                    case "DCCLT02":
                    case "DCOPN03":
                    case "DCLQU04":
                        ElConfirm(
                            "申请失败，请联系您的招行客户经理开通SaaS模式银企直连，再到招行网银进行签约哦~",
                            true,
                            () => {},
                            "授权失败"
                        ).then(() => {
                            globalWindowOpen(bankUrl.value);
                            cmbKeyAesVal.value = result.data?.AesEncryptKey || "";
                            cmbKeyRsaVal.value = result.data?.PubKey || "";
                            cmbConfirmTable.value = true;
                            showMain.value = false;
                        });
                        break;
                    case "DCOPN06":
                        ElConfirm(
                            "申请失败，本账套不存在您已经发起的招行网银签约密钥，请解绑或拒绝后重新签约哦~",
                            true,
                            () => {},
                            "授权失败"
                        );
                        break;
                    default:
                        ElConfirm(Msg, true, () => {}, "授权失败");
                        break;
                }
            } else {
                ElConfirm(result.message, true, () => {}, "授权失败");
            }
            handleConfirmUnLock();
            return;
        }
        if (result.status === 5 || result.status === 6) {
            //其他账套成功签约/其他账套签约中
            ElConfirm(result.message, true, () => {}, "授权失败");
            handleConfirmUnLock();
            return;
        }
    });
};
const ApplyCMBSuccess = (result: CMBGradeResultData) => {
    if (result.AesEncryptKey && result.PubKey) {
        cmbKeyAesVal.value = result?.AesEncryptKey || "";
        cmbKeyRsaVal.value = result?.PubKey || "";
        cmbConfirmTable.value = true;
        showMain.value = false;
        handleConfirmUnLock();
    } else {
        ElConfirm("签约受理成功，请前往招行网银“自助申请-经办-银企直联”签约确认及审核。", true).then(() => {
            globalWindowOpen(bankUrl.value);
            handleApplybank();
        });
    }
};
const handleApplybank = () => {
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        handleConfirmUnLock();
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
            return;
        }
        const result = res.data;
        if (result.status === 1 || result.status === 2) {
            //已签约成功
            const msg = "本公司“" + autoBankForm.accname + "”已成功开通银企互联功能，签约后的每一笔款项都会实时自动录入该银行日记账哦~";
            ElConfirm(appendStyle(msg), true, () => {}, "授权成功").then(() => {
                handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
            });
            return;
        } 
        if (result.status === 3) { //签约中
            if (result.data.AesEncryptKey && result.data.PubKey) {
                cmbKeyAesVal.value = result.data.AesEncryptKey || "";
                cmbKeyRsaVal.value = result.data.PubKey || "";
                cmbConfirmTable.value = true;
                showMain.value = false;
            } else {
                ElConfirm(result.data.Msg, true, () => {}, "授权失败");
            }
        } else {
            ElConfirm(result.message, true, () => {}, "授权失败");
        }
    });
};
const hanldeAuthorize = () => {
    if (!canAuthorizeNext) {
        ElNotify({ type: "warning", message: "请求中，请稍后!" });
        return;
    }
    hanldeAuthorizeLock();
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        hanldeAuthorizeUnLock();
        if (res.state !== 1000) {
            ElNotify({ type: "warning", message: res.msg || "签约失败，请重试" });
            return;
        }
        const result = res.data;
        if (result.status === 1 || result.status === 2) {
            //已签约成功
            const msg = "“" + autoBankForm.accname + "”已成功开通银企互联功能，签约后的每一笔款项都会实时自动录入该银行日记账哦~";
            ElConfirm(appendStyle(msg), true, () => {}, "授权成功");
            return;
        } else if(result.status === 3) {
            ElNotify({ type: "warning", message: "签约失败，请前往网银完成签约、审批银企直连业务后再进行授权" });
        } else {
            ElConfirm(result.message, true, () => {}, "授权失败");
        }
    });
};

const bankHelpLink = ref("");
onMounted(() => {
    const res = GetBankLink(props.bankType);
    const url = GetBankUrl(props.bankType);
    bankHelpLink.value = res;
    bankUrl.value = url;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});

watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);

const handleCopy = (type: "sm2EnKey" | "pubKey") => {
    if (type === "sm2EnKey") {
        copyText(cmbKeyAesVal.value);
    } else {
        copyText(cmbKeyRsaVal.value);
    }
    ElNotify({ type: "success", message: "复制成功" });
};
defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
.autoBankItem {
    & > div {
        & > input {
            .detail-original-input(258px, 30px);
            outline: none;
            &:focus {
                border-color: var(--border-color);
            }
        }
    }
}
</style>
