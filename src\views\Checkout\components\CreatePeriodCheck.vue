<template>
    <div class="check-item-boxs">
        <div class="stepTitle">
            <span class="float-l">第 1 步：期末检查</span>
            <a class="button float-r mr-20" @click="goUserDefinedTemplate">自定义结转模版</a>
            <span class="step-title-tip float-r"> *请检查是否有需要生成凭证，如无需要，点击下一步即可！</span>
        </div>
        <div class="item-boxs-context" :class="isErp ? 'no-icon' : ''">
            <div class="item-box-total" v-for="(item, index) in checkPresetData" :key="index">
                <div class="item-box" v-if="([310, 320, 321, 322].includes(item.checktype) && checkPermission(['salarymanage-cancreatevoucher'])) || ![310, 320, 321, 322].includes(item.checktype)">
                    <div :class="item.cardInfo.type === 1 ? 'item-box-title' : 'item-box-title green'">
                        <template v-if="item.checktype !== 2">
                            <ToolTip :content="item.cardInfo.title" :dynamicWidth="true" :fontSize="16">
                                <span class="text-overflow-ellipsis" style="display: block;">{{ item.cardInfo.title }}</span>
                            </ToolTip>
                        </template>
                        <span v-else>
                            <span>{{ item.cardInfo.title }} </span>
                            <a
                                v-show="item.voucherSums.IsActiveFixedModel === '1'"
                                @click="globalWindowOpenPage('/FixedAssets/DepreciationLedger', '固定资产管理')"
                            >
                                清单
                            </a>
                        </span>
                    </div>
                    <div class="money">
                        <span v-if="item.cardInfo.type === 1" class="currecy">{{ item.cardInfo.money }}</span>
                        <template v-if="item.cardInfo.type === 2">
                            <el-popover placement="right" :width="item.cardInfo.title==='计提税金'? 400 : 300" trigger="hover" :teleported="false" ref="elPopoverRefs">
                                <template #reference>
                                    <span class="currecy green">{{ item.cardInfo.money }}</span>
                                </template>
                                <div class="tip">
                                    <div
                                        style="margin-bottom: 10px; line-height: 16px"
                                        v-for="(otherInfoItem, index) in item.cardInfo.otherInfo"
                                        :key="index"
                                        class="popover-line"
                                    >
                                        <span>{{ otherInfoItem.label + "： " }}</span>
                                        <input
                                            type="text"
                                            class="popoverInput blue mr-10"
                                            :class="isErp ? 'erp' : ''"
                                            v-model="otherInfoItem.value"
                                            v-show="otherInfoItem.indexType !== undefined"
                                            @blur="handleBlur(otherInfoItem, otherInfoItem.value, item)"
                                        />
                                        <input
                                            type="text"
                                            class="popoverInput fc blue mr-10"
                                            :class="isErp ? 'erp' : ''"
                                            v-model="otherInfoItem.value"
                                            v-show="item.checktype === 20"
                                            @blur="handleFCAdjustInfoBlur(otherInfoItem, otherInfoItem.value)"
                                        />
                                        <span v-show="otherInfoItem.indexType === undefined && item.checktype !== 20" class="mr-10">
                                            {{ otherInfoItem.value }}
                                        </span>
                                        <span v-show="otherInfoItem.indexType !== undefined">%</span>
                                        <span
                                            v-show="otherInfoItem.showHelpCenter === true"
                                            class="help-icon"
                                            @click="
                                                globalWindowOpen('https://help.ningmengyun.com/#/jz/commonPro?subMenuId=100172109&answerId=820')
                                            "
                                        ></span>
                                    </div>
                                    <div class="bottom">
                                        <a class="button solid-button" @click="popoverClick(item)">确定</a>
                                    </div>
                                </div>
                            </el-popover>
                        </template>
                        <span
                            v-if="item.cardInfo.type === 3"
                            @click="() => goVoucherAbout(item.cardInfo.type, item)"
                            class="currecy"
                            :class="!item.hasLoading ? '' : 'green'"
                        >
                            {{ !item.hasLoading ? "--" : item.cardInfo.money }}
                        </span>
                    </div>
                    <div
                    :class="
                        item.cardInfo.type === 3 && !item.hasLoading || item.checktype === 0 && !item.hasLoading
                            ? 'check-btn disabled'
                            : item.cardInfo.type === 1
                            ? 'check-btn check-voucher-btn'
                            : 'check-btn'
                    "
                    @click="item.cardInfo.type === 3 && !item.hasLoading || item.checktype === 0 && !item.hasLoading ? '' : goVoucherAbout(item.cardInfo.type, item)"
                    >
                    {{ item.cardInfo.type === 3 && !item.hasLoading || item.checktype === 0 && !item.hasLoading ? "正在计算..." : item.cardInfo.type === 1 ? "查看凭证" : "生成凭证" }}
                </div>
                </div>
            </div>
        </div>
        <div class="check-box-tool-bar">
            <a class="button mr-10" @click="createPeriodCheckLast">上一步</a>
            <a class="solid-button" @click="createPeriodCheckNext">下一步</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

import {
    setCheckPresetData,
    resetSalesMoney,
    resetFCAdjustInfo,
    resetAccruedIncomeTaxMoney,
    resetAccruedTaxMoney,
    initCreateSalesVoucher,
    initAccruedTaxVoucher,
    initAccruedIncomeTaxVoucher,
    initAdjustFCVoucher,
    VoucherType
} from "../utils";
import { formatMoney } from "@/util/format";
import { getGlobalToken } from "@/util/baseInfo";
import type { ICheckResultItem, ICreateVoucher } from "../tpyes";
import { ElPopover } from "element-plus";
import { request } from "@/util/service";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { nextTick } from "vue";
import { globalWindowOpen } from "@/util/url";
import { VoucherEntryModel } from "@/components/Voucher/types";
import { checkPermission } from "@/util/permission";
import ToolTip from "@/components/Tooltip/index.vue";
const isErp = ref(window.isErp);
const props = defineProps({
  partMainMonth: {
    type: String, 
    required: true 
  }
});
const indexTypeParams: any = {
    ChangeSales: 10010,
    CalcTax: 10020,
    CalcIncomeTax: 10030,
};
const indexSubTypeParams: any = {
    // 默认数值
    Default: 1010,
    // 第二计算项
    Second: 1020,
    // 第三计算项
    Third: 1030,
};

const elPopoverRefs = ref<any>();
const emit = defineEmits(["createPeriodCheckLast", "createPeriodCheckNext", "goUserDefinedTemplate", "goLookVouchers", "newVoucher"]);
const checkPresetData = ref<ICheckResultItem[]>([]);

const createPeriodCheckLast = () => emit("createPeriodCheckLast");
const createPeriodCheckNext = () => emit("createPeriodCheckNext");
const goUserDefinedTemplate = () => emit("goUserDefinedTemplate");
const goVoucherAbout = (type: number, data: any) => {
    if (type === 1) {
        const saveInfo = {
            checktype: data.checktype,
            vtId: data.vtId,
        };
        if (window.localStorage.getItem("editCheckoutLookVoucher-" + getGlobalToken())) {
            window.localStorage.removeItem("editCheckoutLookVoucher-" + getGlobalToken());
        }
        window.localStorage.setItem("editCheckoutLookVoucher-" + getGlobalToken(), JSON.stringify(saveInfo));
        emit("goLookVouchers", data);
    } else {
        const vt_id=data.cardInfo.vtId
        //partMainMonth 格式是202407或202410这样的,接口需要这样的
        const partMainMonth=props.partMainMonth
        if(data.cardInfo.vtype === VoucherType.accruedSalaryModule || data.cardInfo.vtype === VoucherType.paySalaryModule){
            request({
                url: `/api/SalaryVoucher/GenerateVoucher?vtId=${vt_id}&mid=${partMainMonth}`,
                method: "POST",
            }).then((res: any) => {
                if (res.state !== 1000) {
                    ElNotify({
                        message: res.msg || res.Msg,
                        type: "warning",
                    });
                    return;
                }
                if(res.data.haveSalary){
                    const voucherLines = res.data.data.rows.map((item: ICreateVoucher) => {
                    const voucherModel = new VoucherEntryModel();
                    voucherModel.asubId = item.asub_id;
                    voucherModel.assistingAccounting = item.assistingaccounting;
                    voucherModel.aacode = item.assistsetting;
                    voucherModel.asubCode = item.asub_code;
                    voucherModel.asubName = item.asub_name;
                    voucherModel.debit = item.debit;
                    voucherModel.credit = item.credit;
                    voucherModel.description = item.description;
                    voucherModel.measureUnit = item.measureupnit + "";
                    voucherModel.quantity = item.quantity;
                    voucherModel.price = item.price;
                    voucherModel.quantityAccounting = item.quantityaccounting;
                    voucherModel.fcId = item.fcid;
                    voucherModel.fcRate = item.fcrate;
                    voucherModel.fcCode = item.fccode;
                    voucherModel.fcAmount = item.fcamount;
                    voucherModel.foreigncurrency = item.foreigncurrency;
                    voucherModel.isjtsub = item.isjtsub;
                    return voucherModel;
                    });
                    data.newVoucherInfo = voucherLines;
                    data.attachFiles = res.data.data.attachFiles;
                    emit("newVoucher", data);
                }else{
                    ElConfirm("暂无本月工资数据，无法生成凭证！", true);
                }
            })
        }else{
            emit("newVoucher", data);
        }
    }
};

const setCheckPresetItem = (loadingData: ICheckResultItem[], pid: number) => {
    currentPid.value = pid;
    checkPresetData.value = [];
    nextTick().then(async () => {
        checkPresetData.value = setCheckPresetData(loadingData).filter((item) => !!item);
        const carryOverItemData = checkPresetData.value.find((item) => item.checktype === 0);
        if (carryOverItemData) {
            const carryOverPercent = carryOverItemData.voucherSums.carryOverPercent || 0;
            carryOverItemData.hasLoading = false;
            //获取销售成本的结转金额以及结转销售成本生成凭证条目列表
            const [amountRes, entryListRes] = await Promise.all([
            getSalesCostCheckoutAmount(pid, carryOverPercent),
            getCheckoutSalesCostVoucherEntryList(pid, carryOverPercent)
            ]);
            if (amountRes.state === 1000) {
                const calResult = formatMoney(amountRes.data || 0) || "0.00";
                carryOverItemData.cardInfo.money = carryOverItemData.status === 1 
                    ? carryOverItemData.cardInfo.money 
                    : calResult;
                
                carryOverItemData.cardInfo.otherInfo.forEach((item: any) => {
                    if (item.label === "4. 综合计算结果") {
                        item.value = calResult;
                    }
                });
            }
            if (entryListRes.state === 1000) {
                carryOverItemData.newVoucherInfo = entryListRes.data;
            }

            carryOverItemData.hasLoading = true;
            }
    });
};
const currentPid = ref(0);
const getSalesCostCheckoutAmount = (pid: number, percent: number) => {
    return request({
        url: `/api/Checkout/GetSalesCostCheckoutAmount?pid=${pid}&value=${percent}`,
        method: "post",
    });
};
const getCheckoutSalesCostVoucherEntryList = (pid: number, percent: number) => {
    return request({
        url: `/api/Voucher/GetCheckoutSalesCostVoucherEntryList?pid=${pid}&value=${percent}`,
        method: "post",
    });
};
const setItemInfoFor99 = (newItemData: any) => {
    const item = checkPresetData.value.find((item) => item.vtId === newItemData.model.vtId) as ICheckResultItem;
    const index = checkPresetData.value.findIndex((item) => item.vtId === newItemData.model.vtId);
    const vouchers: any[] = newItemData.vouchers;
    const rows: any[] = newItemData.rows;
    if (item) {
        item.extendInfo = newItemData;
        if (vouchers.length > 0) {
            let sum = 0;
            for (let i = 0; i < vouchers.length; i++) {
                sum += vouchers[i].debitSum;
            }
            item.cardInfo.money = formatMoney(sum || 0) || "0.00";
            item.cardInfo.type = 1;
        } else {
            let sum = 0;
            for (let i = 0; i < rows.length; i++) {
                sum += Number(rows[i].debit);
            }
            item.cardInfo.money = formatMoney(sum || 0) || "0.00";
            item.cardInfo.type = 3;
        }
        item.vouchers = vouchers;
        item.hasLoading = true;
        checkPresetData.value.splice(index, 1, item);
    }
};
const getCreatePageCardInfo = () => checkPresetData.value;

defineExpose({ setCheckPresetItem, setItemInfoFor99, getCreatePageCardInfo });

const popoverClick = (item: ICheckResultItem) => {
    const type2List = checkPresetData.value.filter((item) => item.cardInfo.type === 2);
    const index = type2List.findIndex((itemData) => itemData.cardInfo.title === item.cardInfo.title) as number;
    elPopoverRefs.value[index]?.hide();
};

const handleBlur = (
    otherInfo: { label: string; value: string; indexType?: string; indexSubType?: string },
    val: string,
    item: ICheckResultItem
) => {
    const { indexType, indexSubType } = otherInfo;
    const fcIndex = item.cardInfo.otherInfo.findIndex((item: any) => item.label == otherInfo.label);
    if (Number(val) + "" !== "NaN") {
        if (val.includes(".")) {
            item.cardInfo.otherInfo[fcIndex].value = removeTrailingZeros(val);
        }
    }
    if (indexSubType && indexType) {
        const taxType = indexTypeParams[indexType];
        const subType = indexSubTypeParams[indexSubType];
        const params = { taxType, subType, value: val };
        request({ url: "/api/Checkout/UpdateCustomeTax?" + getUrlSearchParams(params), method: "post" })
            .then((res: any) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: "更新税率出错，请刷新当前页面重试" });
                    return;
                }
                if (item.checktype === 0) {
                    const cardItem = checkPresetData.value.find((itemData) => itemData.checktype === 0) as ICheckResultItem;
                    getSalesCostCheckoutAmount(currentPid.value, Number(val)).then((res: any) => {
                        if (res.state !== 1000) return;
                        const cardItemDecemal = cardItem.cardInfo.otherInfo.find((item: any) => item.label === "4. 综合计算结果");
                        cardItemDecemal.value = cardItem.cardInfo.money = formatMoney(res.data || 0) || "0.00";
                    });
                    getCheckoutSalesCostVoucherEntryList(currentPid.value, Number(val)).then((res: any) => {
                        if (res.state !== 1000) return;
                        cardItem.newVoucherInfo = res.data;
                    });
                    cardItem.voucherSums.carryOverPercent = Number(val);
                } else if (item.checktype === 4) {
                    const cardItem = checkPresetData.value.find((itemData) => itemData.checktype === 4) as ICheckResultItem;
                    if (otherInfo.indexSubType === "Default") {
                        cardItem.voucherSums.cityBuildTaxRate = Number(val);
                    } else if (otherInfo.indexSubType === "Second") {
                        cardItem.voucherSums.educationPlusTaxRate = Number(val);
                    } else if (otherInfo.indexSubType === "Third") {
                        cardItem.voucherSums.localEduPlusTaxRate = Number(val);
                    }
                    const money = resetAccruedTaxMoney(cardItem);
                    cardItem.cardInfo.money = money;
                    cardItem.newVoucherInfo = initAccruedTaxVoucher(true, cardItem);
                } else if (item.checktype === 6) {
                    const cardItem = checkPresetData.value.find((itemData) => itemData.checktype === 6) as ICheckResultItem;
                    cardItem.voucherSums.incomeTaxRate = Number(val);
                    const money = resetAccruedIncomeTaxMoney(cardItem);
                    cardItem.cardInfo.money = money;
                    cardItem.newVoucherInfo = initAccruedIncomeTaxVoucher(true, cardItem);
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "更新税率出错，请刷新当前页面重试" });
            });
    }
};
const handleFCAdjustInfoBlur = (otherInfo: { label: string; value: string; id: number | string }, val: string) => {
    const { id } = otherInfo;
    const cardItem = checkPresetData.value.find((itemData) => itemData.checktype === 20) as ICheckResultItem;
    const fcIndex = cardItem.cardInfo.otherInfo.findIndex((item: any) => item.id == id);
    if (Number(val) + "" !== "NaN") {
        if (val.includes(".")) {
            cardItem.cardInfo.otherInfo[fcIndex].value = removeTrailingZeros(val);
        }
        cardItem.cardInfo.otherInfo[fcIndex].value = removeTrailingZeros(val);
    }
    const params = { taxType: 10040, subType: id, value: val };
    request({ url: "/api/Checkout/UpdateCustomeTax?" + getUrlSearchParams(params), method: "post" })
        .then((res: any) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: "更新税率出错，请刷新当前页面重试" });
                return;
            }

            cardItem.fcitems[fcIndex].rate = Number(val);
            const money = resetFCAdjustInfo(cardItem);
            cardItem.cardInfo.money = money;
            cardItem.newVoucherInfo = initAdjustFCVoucher(true, cardItem.fcitems, cardItem.accountingSubjects);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "更新税率出错，请刷新当前页面重试" });
        });
};
function removeTrailingZeros(numberString: string) {
    return parseFloat(numberString).toString();
}
</script>

<style lang="less" scoped>
.check-item-boxs {
    box-sizing: border-box;
    width: 1000px;
    padding-left: 20px;
    margin: 0 auto;
    .stepTitle {
        padding-top: 25px;
        height: 30px;
        color: var(--font-color);
        font-size: 24px;
        line-height: 30px;
        & > a.button {
            width: auto;
            padding: 0 12px;
        }
        & > span.step-title-tip {
            margin-right: 168px;
            margin-top: 5px;
            color: var(--orange);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }
    }
    .item-boxs-context {
        margin-top: 20px;
        min-height: 348px;
        color: #404040;
        font-size: 12px;
        text-align: left;
        &.no-icon {
            .item-box {
                .item-box-title,
                .item-box-title.green {
                    background-image: none;
                    padding-left: 10px;
                }
                .check-btn {
                    border-radius: 2px;
                    &.check-voucher-btn {
                        color: var(--main-color);
                        border: 1px solid var(--main-color);
                        background-color: var(--white);
                        &:hover {
                            opacity: 0.8;
                        }
                    }
                }
            }
            .currecy {
                text-decoration: none !important;
            }
        }
        .item-box-total{
            display: inline-block;
        }
        .item-box {
            width: 234px;
            height: 142px;
            box-sizing: border-box;
            border: 1px solid var(--border-color);
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            text-align: right;
            .item-box-title {
                color: var(--font-color);
                font-size: var(--h3);
                line-height: 42px;
                // padding-left: 49px;
                border-radius: 4px 4px 0px 0px;
                background-color: #f8f8f8;
                background-position-x: 17px;
                background-position-y: 12px;
                background-repeat: no-repeat;
                background-size: 20px 18px;
                text-align: center;
                padding-left: 40px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                background-image: url("@/assets/Checkout/icon2.png");
                &.green {
                    background-image: url("@/assets/Checkout/icon1.png");
                }
            }
            .money {
                height: 30px;
                margin-top: 18px;
                margin-left: 16px;
                text-align: left;
                .tip {
                    font-size: 12px;
                    line-height: 30px;
                    color: var(--font-color);
                    display: flex;
                    flex-direction: column;
                    .popover-line {
                        display: flex;
                        align-items: center;
                        .help-icon {
                            height: 16px;
                            width: 16px;
                            background: url(@/assets/Icons/help.png) no-repeat;
                            background-size: 100% 100%;
                            cursor: pointer;
                        }
                    }
                    .bottom {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        a.button {
                            width: 48px;
                            height: 20px;
                            line-height: 20px;
                            font-size: 12px;
                        }
                    }
                    span.blue {
                        color: var(--blue);
                    }
                    input.popoverInput {
                        font-size: 12px;
                        padding: 0;
                        border: none;
                        box-sizing: border-box;
                        height: 16px;
                        line-height: 16px;
                        width: 20px;
                        text-align: center;
                        outline: none;
                        color: var(--blue);
                        &:focus {
                            color: var(--font-color);
                            border-bottom: 1px solid var(--blue);
                        }
                        &.fc {
                            width: 80px;
                            text-align: left;
                            padding-left: 10px;
                        }

                        &.erp {
                            &:focus {
                                color: var(--font-color);
                                border-bottom: 1px solid var(--blue);
                                border-top: 1px solid var(--blue);
                                box-shadow: 0 0 0 1px rgba(140, 197, 255, 0.35);
                            }
                        }
                    }
                }
                .currecy {
                    color: var(--font-color);
                    font-size: 22px;
                    line-height: 30px;
                    display: inline-block;
                    &.green {
                        color: var(--main-color);
                        text-decoration: underline;
                        cursor: pointer;
                    }
                }
            }
            .check-btn {
                font-size: 12px;
                margin-top: 8px;
                margin-right: 16px;
                width: 68px;
                line-height: 24px;
                text-align: center;
                color: var(--white);
                cursor: pointer;
                display: inline-block;
                vertical-align: top;
                background-color: var(--main-color);
                &.check-voucher-btn {
                    background-color: #3177f4;
                }
                &.disabled {
                    background-color: #bbbbbb;
                }
            }
        }
    }
    .check-box-tool-bar {
        text-align: center;
        margin: 50px 0;
    }
}
</style>
