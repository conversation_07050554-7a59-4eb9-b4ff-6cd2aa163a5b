<template>
  <el-dialog class="dialogDrag" v-model="dialogVisible" title="报表订阅推送" width="560px" :show-close="true" @close="handleClose"
    destroy-on-close>
    <div class="subscribe-push-dialog" v-dialogDrag>
      <div class="subscribe-settings">
        <div class="setting-row">
          <div class="setting-label"><span
              style="font-size: 16px;font-weight: 500;padding-right: 10px;">订阅推送</span><span style="color:#666;">支持订阅推送资金日报、周报和月报</span>
          </div>
        </div>

        <!-- 日报设置 -->
        <div class="push-item">
          <div class="push-switch mr-20">
            <span class="push-label">推送日报：</span>
            <el-switch v-model="pushConfig.dayOn" />
          </div>
          <!-- <div class="push-time  mr-20">
            <span class="time-label">推送时间</span>
            <el-time-select v-model="pushConfig.daily.time" :disabled="!pushConfig.daily.enabled" :start="'01:00'"
              :step="'01:00'" :end="'24:00'" placeholder="选择时间" />
          </div> -->
        </div>

        <!-- 周报设置 -->
        <div class="push-item">
          <div class="push-switch mr-20">
            <span class="push-label">推送周报：</span>
            <el-switch v-model="pushConfig.weekOn" @change="handleWeeklyChange" />
          </div>
          <div class="push-time  mr-20">
            <span class="time-label">推送日期</span>
            <el-select v-model="pushConfig.weekDay" :disabled="true" placeholder="选择日期">
              <el-option v-for="(day, index) in weekDays" :key="index" :label="day" :value="index + 1" />
            </el-select>
          </div>
          <!-- <div class="push-time">
            <span class="time-label">推送时间</span>
            <el-time-select v-model="pushConfig.weekly.time" :disabled="!pushConfig.weekly.enabled" :start="'01:00'"
              :step="'01:00'" :end="'24:00'" placeholder="选择时间" />
          </div> -->

        </div>

        <!-- 月报设置 -->
        <div class="push-item">
          <div class="push-switch mr-20">
            <span class="push-label">推送月报：</span>
            <el-switch v-model="pushConfig.monthOn" @change="handleMonthlyChange" />
          </div>
          <div class="push-time switch mr-20">
            <span class="time-label">推送日期</span>
            <el-select v-model="pushConfig.monthDay" :disabled="true" placeholder="选择日期">
              <el-option v-for="day in 31" :key="day" :label="`每月${day}日`" :value="day" />
            </el-select>
          </div>
          <!-- <div class="push-time">
            <span class="time-label">推送时间</span>
            <el-time-select v-model="pushConfig.monthly.time" :disabled="!pushConfig.monthly.enabled" :start="'01:00'"
              :step="'01:00'" :end="'24:00'" placeholder="选择时间" />
          </div> -->

        </div>
      </div>

      <div class="app-download">
        <div class="setting-row">
          <div class="setting-label"><span
              style="font-size: 16px;font-weight: 500;padding-right: 10px;">接收方式</span><span style="color:#666;">APP开启推送时，支持APP接收推送通知
            </span></div>
        </div>
        <div class="setting-img">
          <div class="qrcode-section">
            <div class="qrcode-wrapper">
              <div class="qrcode-box">
                <img class="qrCodeImg" src="@/assets/Icons/downloadApp.png" />
                <!-- <img src="@/assets/Icons/downloadApp.png" class="qr-logo" alt="" style="top: 44%" /> -->
              </div>
            </div>
            <p class="download-tip">扫一扫，下载APP</p>
          </div>
          <div class="qrcode-section">
            <div class="qrcode-wrapper">
              <div class="qrcode-box">
                <img class="qrCodeImg" src="@/assets/Cashier/reportSubScribe.png" />
                <!-- <img src="@/assets/Cashier/reportSubScribe.png" class="qr-logo" alt="" style="top: 44%" /> -->
              </div>
            </div>
            <p class="download-tip">开启推送及时获取公司资金情况</p>
          </div>
        </div>

      </div>
    </div>

    <template #footer>
      <div class="buttons borderTop">
        <a class="button mr-10" @click="handleClose">取消</a>
        <a class="button solid-button" @click="handleConfirm">确定</a>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref,} from 'vue'
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";

const dialogVisible = ref(false)

const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

const pushConfig = ref({
  dayOn: false,
  monthDay: 1,
  monthOn: false,
  weekDay: 1,
  weekOn: false,
})

const handleWeeklyChange = (value: boolean) => {
  if (!value) {
    pushConfig.value.monthDay = 1
  }
}

const handleMonthlyChange = (value: boolean) => {
  if (!value) {
    pushConfig.value.monthDay = 1
  }
}

const handleConfirm = () => {
  request({
    url:'/api/CashierReport/SetSubcription',
    method: 'post',
    data:pushConfig.value
  }).then((res: IResponseModel<Boolean>) => {
    if (res.state !== 1000) return
    ElNotify({
      message: '订阅推送设置成功',
      type: 'success',
    })
  })
  handleClose()
}

const handleClose = () => {
  dialogVisible.value = false
}
interface ISubscribe {
  dayOn: boolean
  monthDay: number
  monthOn: boolean
  weekDay: number
  weekOn: boolean
}
const initSubscribe = () => {
  request({
    url: '/api/CashierReport/GetSubscription'
  }).then((res: IResponseModel<ISubscribe>) => {
    if (res.state !== 1000) return
    pushConfig.value = res.data
    dialogVisible.value = true
  })
}

defineExpose({
  initSubscribe
})
</script>

<style lang="less" scoped>
.subscribe-push-dialog {
  padding: 20px 20px 0px 44px;

  .setting-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .setting-label {
      font-size: 14px;
      color: #333;
      margin-right: 8px;
    }
  }

  .push-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .push-switch {
      display: flex;
      align-items: center;
      width: 120px;


      .push-label {
        margin-left: 8px;
        font-size: 14px;
        color: #333;
      }
    }

    .el-select,
    .el-time-select {
      width: 120px;
      margin-left: 16px;
    }
  }

  .app-download {
    .setting-img {
      display: flex;
      justify-content: center;
      align-items: center;

      .qrcode-section {
        text-align: center;
        margin-top: 10px;
        margin-right: 30px;

        .qrcode-wrapper {
          display: inline-block;

          .qrcode-box {
            position: relative;
            display: inline-block;
            vertical-align: middle;

            .qrCodeImg {
              display: inline-block;
              width: 128px;
              height: 120px;
              background: #D8D8D8;
              margin: 10px 0 0px 0;
            }

            .qr-logo {
              position: absolute;
              top: 50%;
              left: 50%;
              margin-left: -11px;
            }
          }
        }

        .download-tip {
          font-size: 14px;
          color: #666;
          margin-top: 12px;
        }
      }
    }


  }
}

:deep(.subscribe-tooltip) {
  max-width: 240px;
}
</style>