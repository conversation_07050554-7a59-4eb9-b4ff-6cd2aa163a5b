<template>
    <el-select-v2
        ref="selectRef"
        v-model="value"
        :filterable="filterable"
        :remote="true"
        :class="[props.class, 'select', 'custom-select-v2']"
        :popper-class="popperClass + ' select-down'"
        :options="selectShowOptions"
        :placeholder="placeholder"
        :style="style"
        :teleported="teleported"
        :scrollbar-always-on="scrollbarAlwaysOn"
        :item-height="28"
        :props="prop"
        :remote-method="remoteMethod"
        @change="handleChange"
        @visible-change="handleVisibleChange"
        @input="handleInput"
        @focus="handleFocus"
        @mouseenter="enterInput"
        @mouseleave="leaveInput"
    >
        <template #prefix>
            <div v-show="clearable && iconFlag" class="icon_clear_v2" @click="handleClearClick" :style="{ right: iconClearRight + 'px' }">
                <el-icon color="#a8abb2"><CircleClose /></el-icon>
            </div>
            <div class="more-filled" v-show="showMoreFilled" :style="{ right: iconClearRight + 'px' }" @click="(e)=>expandAssit(e)">
                <el-icon><MoreFilled /></el-icon>
            </div>
        </template>
        <template #default="{ item }">
            <ToolTip 
                :content="item[prop.label]" 
                :line-clamp="1" 
                :teleported="true" 
                placement="right-start" 
                :offset="-8"
                :font-size="fontSize"
                :dynamicWidth="dynamicWidth"
            >
                <span @click="clickOption(item[prop.value])"> {{ item[prop.label] }}</span>
            </ToolTip>
        </template>
    </el-select-v2>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, nextTick, onUnmounted } from "vue";
// import type { SelectWithLotOfDataOptions } from "./types";
import ToolTip from "@/components/ToolTip/index.vue";
import { watchEffect } from "vue";
import { pinyin } from "pinyin-pro";

interface IProps {
    label?: string;
    value?: string;
    options?: string;
    disabled?: string;
}

const defaultProps = {
    label: "label",
    value: "value",
    options: "options",
    disabled: "disabled",
};

interface IToolTipOptions {
    fontSize?: number;
    dynamicWidth?: boolean;
}

const selectRef = ref();

const props = withDefaults(
    defineProps<{
        options: Array<any>;
        modelValue: string;
        teleported?: boolean;
        class?: string;
        filterable?: boolean;
        placeholder?: string;
        scrollbarAlwaysOn?: boolean;
        style?: any;
        popperClass?: string;
        clearable?: boolean;
        iconClearRight?: number;
        allowCreate?: boolean;
        props?: IProps;
        toolTipOptions?: IToolTipOptions;
        showMoreFilled?: boolean;
    }>(),
    {
        teleported: true,
        filterable: false,
        class: "",
        placeholder: "",
        scrollbarAlwaysOn: true,
        style: "",
        popperClass: "",
        clearable: false,
        iconClearRight: 18,
        allowCreate: false,
        props: () => ({}),
        toolTipOptions: () => ({}),
        showMoreFilled: false,
    }
);

const fontSize = computed(() => props.toolTipOptions.fontSize || 14);
const dynamicWidth = computed(() => props.toolTipOptions.dynamicWidth || false);

const prop = Object.assign({}, defaultProps, props.props);
const selectOptions = ref();
const selectShowOptions = ref();
const emit = defineEmits<{
    (event: "update:modelValue", args: string): void;
    (event: "change", args: string): void;
    (event: "visible-change", args: boolean): void;
    (event: "input", args: string): void;
    (event: "focus", args: FocusEvent): void;
    (event: "keydownEnter", args: KeyboardEvent): void;
    (event: "clickOption",value:string): void;
    (event: "expandAssit",value:string): void;
}>();

const value = computed({
    get() {
        return props.modelValue;
    },
    set(value: string) {
        emit("update:modelValue", value);
    },
});

function expandAssit(e:any) {
    e.stopPropagation();
    emit("expandAssit",selectRef.value?.states?.selectedLabel);
}

const remoteMethod = (query: string) => {
    if (query.trim() === "") {
        selectShowOptions.value = selectOptions.value;
        return;
    }
    const lowerCaseValue = query.toLowerCase();
    selectShowOptions.value = selectOptions.value.filter((item: any) => {
        return (
            item.label.includes(query) 
            || pinyin(item.label, { pattern: "first", toneType: "none", type: "array" }).join("").includes(lowerCaseValue)
            || pinyin(item.label, { toneType: "none", type: "array" }).join("").includes(lowerCaseValue)
        );
    });
};

const handleChange = (value: string) => {
    emit("change", value);
};
const handleVisibleChange = (value: boolean) => {
    selectShowOptions.value = selectOptions.value;
    emit("visible-change", value);
};
const handleInput = (event: InputEvent) => {
    emit("input", (event.target as HTMLInputElement)?.value || "");
};
const handleFocus = (event: FocusEvent) => {
    emit("focus", event);
};
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        // selectRef.value.popperRef.style.display = "none";
        emit("keydownEnter", event);
        nextTick().then(() => {
            value.value = props.modelValue;
            selectRef.value.popperRef.style.display = "none";
            iconFlag.value = value.value ? true : false;
            // selectRef.value.$refs.inputRef.value = props.options.find((item) => item.value === props.modelValue)?.label || "";
        });
    }
};
const clickOption = (value:string) => {
    emit("clickOption",value);
};
const handeleMouseDown = (e: any) => {
    if (selectRef.value && selectRef.value.popperRef) {
        selectRef.value.popperRef.style.display = "none";
    }
};

const handleClearClick = () => {
    value.value = "";
    iconFlag.value = false;
};
const enterInput = () => {
    iconFlag.value = value.value ? true : false;
};
const leaveInput = () => {
    iconFlag.value = false;
};
const iconFlag = ref(false);
onMounted(() => {
    const input = selectRef.value?.$refs?.inputRef;
    if (input) {
        input.addEventListener("keydown", handleKeyDown);
    }
    const elHeader = document.querySelector(".voucher-template-dialog .el-dialog__header");
    if (elHeader) {
        elHeader.addEventListener("mousedown", handeleMouseDown);
    }
});
onUnmounted(() => {
    const input = selectRef.value?.$refs?.inputRef;
    if (input) {
        input.removeEventListener("keydown", handleKeyDown);
    }
    const elHeader = document.querySelector(".voucher-template-dialog .el-dialog__header");
    if (elHeader) {
        elHeader.removeEventListener("mousedown", handeleMouseDown);
    }
});

watchEffect(() => {
    selectOptions.value = props.options;
    selectShowOptions.value = props.options;
});
defineExpose({
    focus: () => {
        selectRef.value.focus();
    },
    blur: () => {
        selectRef.value.blur();
    },
    clear: () => {
        value.value = "";
    },
});
</script>

<style lang="less">
.icon_clear_v2 {
    position: absolute;
    right: 0 !important;
    width: 20px;
    height: 20px;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.custom-select-v2 {
    width: 100%;
    .el-select-v2__wrapper {
        > div {
            &:not(.el-select-v2__input-wrapper) {
                width: 20px;
                height: 20px;
                position: absolute;
                right: 30px;
            }
        }
    }
    .more-filled{
        z-index: 100;
        top: -5px;
        width: 20px;
    }
}
</style>
