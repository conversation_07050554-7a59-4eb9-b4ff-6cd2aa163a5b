<template>
    <div>
        <img src="@/assets/AccountBooks/error.png" style="margin-top: 120px" />
        <div style="margin: 20px auto; font-size: 14px; line-height: 24px">
            已存在{{ sourcePageName }}文件导出任务，为了防止出错，请勿重复导出。您可以前往【备份恢复-文件下载中心】查看~
        </div>
        <div><a class="button mr-10" @click="toBackup()">前往查看</a><a class="button solid-button" @click="reload()">确定</a></div>
    </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import { globalWindowOpenPage } from "@/util/url";

const route = useRoute();
const sourcePageName = ref(route.query.sourcePageName?.toString() || "首页");
const sourcePagePath = ref(route.query.sourcePagePath?.toString() || "/Default/Default");
const isErp = ref(window.isErp);
const toBackup = () => {
    if (isErp.value) {
        window.location.href = window.erpHost + `/#/Backup?type=1020&stay=true`;
    } else {
        globalWindowOpenPage("/Settings/Backup?action=file", "备份恢复");
    }
};
const reload = () => {
    if (isErp.value) {
        window.location.href = window.erpHost + `/#${sourcePagePath.value}?stay=true`;
    } else {
        globalWindowOpenPage(sourcePagePath.value , sourcePageName.value);
    }
};
</script>
