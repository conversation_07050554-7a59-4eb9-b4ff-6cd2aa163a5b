<template>
    <el-tooltip :content="text" placement="right-start" effect="light" :offset="-8" :visible="visible" popper-class="tooltip-checkbox">
        <el-checkbox @mouseenter="checkOverflow" @mouseleave="visible = false" ref="checkboxRef" :label="label" @change="changeCheck">
            {{ text }}
        </el-checkbox>
    </el-tooltip>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect } from "vue";
import { ElCheckbox } from "element-plus";

const props = defineProps<{
    label: number | string;
    text: string;
    lineCount?: number;
    showTip?: boolean;
    setTipShow?: (show: boolean) => void;
}>();

const emits = defineEmits(["change"])
function changeCheck(value:any) {
    emits("change", value);
}
const visible = ref(false);
const checkboxRef = ref<InstanceType<typeof ElCheckbox>>();

watchEffect(() => {
    if (props.showTip !== undefined && !props.showTip) {
        visible.value = false;
    }
});

const checkOverflow = () => {
    const text = props.text;
    if (isOverflowed(text)) {
        visible.value = true;
    } else {
        visible.value = false;
    }
};

const isOverflowed = (text: string): boolean => {
    props.setTipShow && props.setTipShow(true);
    const lineCount = props.lineCount || 2;
    const checkboxElement = checkboxRef.value?.$el;
    if (!checkboxElement) return false;
    const _w = checkboxElement.getBoundingClientRect().width;
    const checkLabelEl = checkboxElement.querySelector(".el-checkbox__label");
    const style = window.getComputedStyle(checkLabelEl);
    const fontSize = style.getPropertyValue("font-size");
    const width = _w - 8 * 2 - 14 - 8;
    const element = document.createElement("div");
    element.style.display = "inline-block";
    element.style.visibility = "hidden";
    element.style.boxSizing = "border-box";
    element.style.fontSize = fontSize;
    document.body.appendChild(element);
    element.innerText = text;
    const isOverflow = element.scrollWidth > width * lineCount;
    document.body.removeChild(element);

    return isOverflow;
};

const handleScroll = () => {
    visible.value = false;
};

onMounted(() => {
    const element = checkboxRef.value?.$el;
    if (element && element.parentNode) {
        const checkBoxGroup = element.parentNode;
        if (checkBoxGroup && checkBoxGroup.parentNode) {
            const scrollViewElement = checkBoxGroup.parentNode;
            if (scrollViewElement && scrollViewElement.parentNode) {
                const scrollElement = scrollViewElement.parentNode;
                if (scrollElement) {
                    scrollElement.addEventListener("scroll", handleScroll);
                }
            }
        }
    }
});
</script>

<style lang="less">
.tooltip-checkbox {
    max-width: 300px;
    text-align: left;
}
</style>
