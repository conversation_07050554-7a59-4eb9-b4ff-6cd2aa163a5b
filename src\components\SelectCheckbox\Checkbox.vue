<template>
  <el-tooltip
    :content="text"
    placement="right-start"
    effect="light"
    :visible="visible"
    popper-class="tooltip-checkbox">
    <el-checkbox
      @mouseenter="checkOverflow"
      @mouseleave="visible = false"
      ref="checkboxRef"
      :label="label"
      @change="handleChange">
      {{ text }}
    </el-checkbox>
  </el-tooltip>
</template>

<script setup lang="ts">
  import { ref, watch } from "vue"
  import { ElCheckbox } from "element-plus"
  import type { CheckboxValueType } from "element-plus"
  import { calculateTextWidth } from "@/utils/dom"

  const props = defineProps<{
    label: number | string
    text: string
    lineCount?: number // 指定文本的行数，默认为2
    showTip?: boolean
    setTipShow?: (show: boolean) => void
  }>()

  const emits = defineEmits<{
    change: [value: CheckboxValueType]
  }>()

  const visible = ref(false)
  const checkboxRef = ref<InstanceType<typeof ElCheckbox>>()

  // 监听 showTip 属性变化
  watch(
    () => props.showTip,
    (newVal) => {
      if (newVal === false) {
        visible.value = false
      }
    },
  )

  // 处理复选框变化事件
  function handleChange(value: CheckboxValueType) {
    emits("change", value)
  }

  // 检查文本是否溢出
  const checkOverflow = () => {
    visible.value = isOverflowed()
  }

  // 判断文本是否溢出
  const isOverflowed = (): boolean => {
    // 通知父组件提示将要显示
    if (props.setTipShow) {
      props.setTipShow(true)
    }

    const lineCount = props.lineCount || 2
    const checkboxElement = checkboxRef.value?.$el
    if (!checkboxElement) return false

    const checkLabelEl = checkboxElement.querySelector(".el-checkbox__label") as HTMLElement
    if (!checkLabelEl) return false

    // 获取标签元素的样式
    const style = window.getComputedStyle(checkLabelEl)
    const fontSize = parseFloat(style.getPropertyValue("font-size"))
    const fontFamily = style.getPropertyValue("font-family")
    // 计算可用宽度
    const containerWidth = checkboxElement.getBoundingClientRect().width
    const availableWidth = containerWidth - 8 * 2 - 14 - 8

    // 使用提取的函数测量文本宽度
    const textWidth = calculateTextWidth(props.text, fontSize, fontFamily)

    // 判断文本是否溢出
    return textWidth > availableWidth * lineCount
  }
</script>

<style lang="scss">
  .tooltip-checkbox {
    max-width: 300px;
    text-align: left;
  }
</style>
