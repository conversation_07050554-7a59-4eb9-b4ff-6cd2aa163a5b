<template>
    <div class="content">
        <div class="main-content">
            <div class="main-tool-bar space-between">
                <div class="main-tool-left">
                    <div class="button solid-button mr-20" @click="handleRouteToOtherPage()">科目关联明细设置</div>
                    <div class="button" @click="showGuidance">业务凭证操作指引</div>
                    <div class="warning-tip ml-20">让业务基础资料和财务会计科目关联对应，生成业务凭证能自动带出科目</div>
                </div>
                <div class="main-tool-right" @click="toHelp"><span class="icon"></span><span>帮助视频</span></div>
            </div>
            <div class="line mb-16"></div>
            <div class="main-center">
                <div class="example">
                    当每个业务的科目相对唯一，则按照以下默认科目设置和携带；如果入账科目有明细要求，则点击
                    <span class="link" @click="handleRouteToOtherPage()">按类别设置</span>
                    去设置
                </div>
                <div class="example mb-16">
                    例如：存货科目，通常情况下，原材料类存货的入账科目是原材料科目、产成品类存货的入账科目是库存商品科目，即入账科目受存货分类影响，需要选择不同科目，则建议在按商品类别设置中去设置
                </div>
                <Table
                    :data="asubRelationInfo"
                    :columns="asubRelationColumns"
                    :show-overflow-tooltip="false"
                    :highlight-current-row="false"
                >
                    <template #asubs>
                        <el-table-column label="默认科目" min-width="300px" align="left" header-align="left">
                            <template #default="{ row }: { row: IAsubRelationInfo }">
                                <template v-for="(asub, index) in row.asubs" :key="index">
                                    <div class="subject-text">
                                        <span class="text-title">{{ asub }}</span>
                                        <div class="subject-picker-container">
                                            <SubjectPicker
                                                v-model="defaultAsubList[row.asubRelationType[index]]"
                                                diyWidth="100%"
                                                asubImgRight="1px"
                                                :is-by-id="true"
                                                :is-to-body="true"
                                                :defaultMaxWidth="155"
                                                :isExpansion="true"
                                                :isParentShow="false"
                                                :isErpAsub="true"
                                                :is-account="true"
                                                :clearable="false"
                                                :useOriginFilter="true"
                                                :disabled="!checkPermission(['asubrelationsettings-canedit'])"
                                                @change="handleSubmitModifyDefaultAsub(row.asubRelationType, index)"
                                            ></SubjectPicker>
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #explain>
                        <el-table-column label="说明" min-width="380px" align="left" header-align="left">
                            <template #default="{ row }: { row: IAsubRelationInfo }">
                                <div class="explain-text" v-for="(explain, index) in row.explain" :key="index">
                                    <span class="text-title">{{ row.asubs[index] + "：" }}</span>
                                    <span class="text">{{ explain }}</span>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                    <template #operation>
                        <el-table-column label="更多明细设置" min-width="180px" align="left" header-align="left">
                            <template #default="{ row }: { row: IAsubRelationInfo }">
                                <div class="link" @click="handleRouteToOtherPage(row.typeCode)">{{ row.operation }}</div>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
        <OperationGuidance current="AsubRelationSettings1" ref="operationGuidanceRef" />
    </div>
</template>

<script lang="ts">
export default {
    name: "AsubRelationSettings1",
};
</script>
<script setup lang="ts">
import { computed, onMounted, provide, ref, watch } from "vue";
import { ElNotify } from "@/util/notify";
import { type IResponseModel, request } from "@/util/service";
import { asubRelationInfo, asubRelationColumns, asubRelationActiveTabKey, DefaultAsubClass } from "./utils";
import { getUrlSearchParams, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { checkPermission } from "@/util/permission";
import { expansionTreeList, getAsubTree, type ISubjectTree } from "@/components/Picker/SubjectPicker/util";
import { initAsubTreeKey } from "@/components/Picker/SubjectPicker/symbols";
import { resetErpVoucherHistory } from "../utils";
import { useScmInfoStore } from "@/store/modules/scm";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";

import type { IAsubRelationInfo, AsubRelationType } from "./utils";

import Table from "@/components/Table/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import OperationGuidance from "../components/OperationGuidance.vue";

const defaultAsubList = ref<Record<AsubRelationType, string>>(new DefaultAsubClass());
const backDefaultAsubList = ref<Record<AsubRelationType, string>>(new DefaultAsubClass());
const scmAsid = ref(0);
const scmProductType = ref(0);

const asubTreeData = ref<Array<ISubjectTree>>([]);
provide(initAsubTreeKey, asubTreeData);
function requestInitAsubInfo() {
    getAsubTree(1).then((res: IResponseModel<string>) => {
        if (res.state !== 1000 || res.data === "") return;
        asubTreeData.value = expansionTreeList(JSON.parse(res.data));
    });
}

async function getDefaultAsubRelation() {
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    await request({ url: "/api/AsubRelation/GetDefaultList?" + getUrlSearchParams(params), method: "post" }).then(
        (res: IResponseModel<Record<AsubRelationType, number>>) => {
            for (const keyName in res.data) {
                const key = keyName as unknown as AsubRelationType;
                if (defaultAsubList.value[key] !== undefined) {
                    defaultAsubList.value[key] = res.data[key].toString();
                    backDefaultAsubList.value[key] = res.data[key].toString();
                }
            }
        }
    );
}
const operationGuidanceRef = ref<InstanceType<typeof OperationGuidance>>();
function showGuidance() {
    operationGuidanceRef.value?.showGuidance();
}
function toHelp() {
    globalWindowOpen("https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=130150112");
}
const scmInfoStore = useScmInfoStore();
onMounted(async () => {
    requestInitAsubInfo();
    await scmInfoStore.handleGetScmRelation().then(() => {
        scmAsid.value = scmInfoStore.scmAsid;
        scmProductType.value = scmInfoStore.scmProductType;
    });
    operationGuidanceRef.value?.checkShowGuidance();
    await resetErpVoucherHistory();
    getDefaultAsubRelation();
});

function handleSubmitModifyDefaultAsub(asubRelationType: Array<AsubRelationType>, index: number) {
    const data: any = {};
    asubRelationType.forEach((type) => (data[type] = defaultAsubList.value[type]));
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    request({ url: "/api/AsubRelation/SubmitDefaultSettings", method: "post", params, data }).then((res: IResponseModel<boolean>) => {
        if (res.state !== 1000 || !res.data) {
            ElNotify({ type: "warning", message: "保存失败" });
            Object.assign(defaultAsubList.value, backDefaultAsubList.value);
            return;
        }
        ElNotify({ type: "success", message: "保存成功" });
        Object.assign(backDefaultAsubList.value, defaultAsubList.value);
    });
}

function handleRouteToOtherPage(active?: string) {
    const params = { [asubRelationActiveTabKey]: active, r: Math.random() };
    let url = "/Erp/AsubRelationSettings";
    if (active) url += "?" + getUrlSearchParams(params);
    globalWindowOpenPage(url, "科目关联设置");
}
const accountSubjectStore = useAccountSubjectStore();
const subject = computed(() => accountSubjectStore.accountSubjectList);
watch(subject, () => {
    getDefaultAsubRelation();
    requestInitAsubInfo();
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.mb-16 {
    margin-bottom: 16px;
}
.content {
    font-size: var(--font-size);
    .main-content {
        .main-tool-right {
            cursor: pointer;
            .icon {
                display: inline-block;
                width: 16px;
                height: 16px;
                margin-right: 5px;
                background: url("@/assets/Icons/video-blue.png") no-repeat 100% 100%;
            }
        }
        .main-center {
            display: flex;
            flex-direction: column;
            :deep(.table.custom-table) {
                flex: 1;
                overflow: hidden;
            }
            :deep(.el-table) {
                .el-table__body {
                    .cell {
                        padding: 30px 8px;
                    }
                }
            }
        }
    }
}
.example {
    line-height: 24px;
    text-align: left;
}
.subject-text,
.explain-text {
    line-height: 24px;
    & + & {
        margin-top: 8px;
    }
}
.subject-text {
    :deep(input) {
        border: none !important;
    }
    .text-title {
        width: 100px;
        display: inline-block;
    }
    .subject-picker-container {
        display: inline-block;
        position: relative;
        width: calc(100% - 100px);
        min-width: 178px;
        max-width: 298px;
    }
}
.explain-text {
    .text-title {
        font-weight: 600;
    }
}
</style>
