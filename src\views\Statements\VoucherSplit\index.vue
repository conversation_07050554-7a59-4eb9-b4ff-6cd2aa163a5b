<template>
    <div class="content">
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <span>会计期间：</span>
                    <el-select v-model="pid" @change="handleSearch">
                        <el-option
                            v-for="item in periodList"
                            :key="item.pid"
                            :label="item.year + '年' + item.sn + '月'"
                            :value="item.pid"
                        ></el-option>
                    </el-select>
                </div>
                <div class="main-tool-right">
                    <a class="button solid-button large-2 ml-10" @click="handleCancelAllSplit">取消所有拆分</a>
                </div>
            </div>
            <div class="main-center">
                <Table
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :emptyText="emptyText"
                    :data="tableData"
                    :columns="columns"
                    :page-is-show="true"
                    :showOverflowTooltip="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :scrollbar-show="true"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #operate>
                        <el-table-column 
                            label="操作" 
                            min-width="100px" 
                            align="left" 
                            header-align="left"
                            :resizable="false"
                        >
                            <template #default="scope">
                                <span class="link" @click="handleAboutSplitVoucher(scope.row.pid, scope.row.vid, scope.row.splited)">
                                    {{ scope.row.splited ? "取消拆分" : "拆分" }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "VoucherSplit",
};
</script>
<script setup lang="ts">
import { toRef, onMounted, ref, watch } from "vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { request, type IResponseModel } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { formatMoney } from "@/util/format";

import type { IVoucherSplitItem } from "@/views/Statements/CashFlowAdjustment/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "VoucherSplit";
interface ISearchBack {
    data: Array<IVoucherSplitItem>;
    count: number;
}

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination({
    pageSize: 300,
    pageSizes: [300, 500, 1000],
});
const periodList = toRef(useAccountPeriodStore(), "periodList");

const pid = ref(0);

const columns: Array<IColumnProps> = [
    { 
        label: "日期", 
        prop: "vdate", 
        minWidth: 100, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "vdate")
    },
    {
        label: "凭证字号",
        prop: "vgName",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        formatter: function (row: IVoucherSplitItem, column: any, value: number) {
            return value + "-" + row.vnum;
        },
        width: getColumnWidth(setModule, "vgName")
    },
    { 
        label: "拆分序号", 
        prop: "splitNum", 
        minWidth: 100, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "splitNum")
    },
    { 
        label: "摘要", 
        prop: "description", 
        minWidth: 200, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "description")
    },
    { 
        label: "科目名称", 
        prop: "asubName", 
        minWidth: 200, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "asubName")
    },
    {
        label: "借方金额",
        prop: "debit",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        formatter: function (row: IVoucherSplitItem, column: any, value: number) {
            return formatMoney(value);
        },
        width: getColumnWidth(setModule, "debit")
    },
    {
        label: "贷方金额",
        prop: "credit",
        minWidth: 100,
        align: "left",
        headerAlign: "left",
        formatter: function (row: IVoucherSplitItem, column: any, value: number) {
            return formatMoney(value);
        },
        width: getColumnWidth(setModule, "credit")
    },
    { slot: "operate" },
];
const tableData = ref<Array<IVoucherSplitItem>>([]);
const loading = ref(false);
const emptyText = ref("暂无数据");
function handleSearch() {
    const params = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        pid: pid.value,
    };
    loading.value = true;
    request({ url: "/api/StandardCashFlowStatement/GetAllSplitVoucherPagingList?" + getUrlSearchParams(params), method: "post" })
        .then((res: IResponseModel<ISearchBack>) => {
            loading.value = false;
            if (res.state !== 1000) {
                emptyText.value = "暂无数据";
                tableData.value = [];
                ElNotify({ type: "warning", message: res.msg || "获取数据失败" });
                return;
            }
            emptyText.value = "";
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
        })
        .catch(() => {
            loading.value = false;
            emptyText.value = "暂无数据";
            tableData.value = [];
        });
}
let isOperate = false;
function handleCancelAllSplit() {
    if (isOperate) return;
    isOperate = true;
    request({ url: "/api/StandardCashFlowStatement/CancelSplitVouchers", method: "post" })
        .then((res: IResponseModel<boolean>) => {
            isOperate = false;
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "取消所有拆分失败" });
                return;
            }
            ElNotify({ type: "success", message: "取消所有拆分成功" });
            handleSearch();
        })
        .catch(() => {
            isOperate = false;
            ElNotify({ type: "warning", message: "取消所有拆分失败" });
        });
}
function handleAboutSplitVoucher(pid: number, vid: number, splited: boolean) {
    if (isOperate) return;
    isOperate = true;
    const params = { pid, vid };
    const path = splited ? "CancelSplitVoucher?" : "ReSplitVoucher?";
    const message = splited ? "取消拆分" : "拆分";
    request({ url: "/api/StandardCashFlowStatement/" + path + getUrlSearchParams(params), method: "post" })
        .then((res: IResponseModel<boolean>) => {
            isOperate = false;
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || message + "失败" });
                return;
            }
            ElNotify({ type: "success", message: message + "成功" });
            handleSearch();
        })
        .catch(() => {
            isOperate = false;
            ElNotify({ type: "warning", message: message + "失败" });
        });
}

onMounted(() => {
    pid.value = periodList.value[periodList.value.length - 1].pid;
    handleSearch();
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    handleSearch();
});
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.main-center {
    flex: 1 !important;
    :deep(.custom-table) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table__inner-wrapper {
            &::before {
                height: 1px !important;
                bottom: 14px;
            }
        }
    }
}
</style>
