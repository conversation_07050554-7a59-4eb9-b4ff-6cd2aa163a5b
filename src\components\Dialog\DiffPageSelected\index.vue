<template>
    <div v-if="display">
        <el-dialog
            v-model="display"
            :overflow="false"
            :draggable="false"
            title="已选记录"
            center
            width="1100px"
            top="5vh"
            class="custom-confirm selected-table-dialog dialogDrag"
            @closed="handleClosed"
        >
            <div class="selected-table-content" v-dialogDrag>
                <Table
                    class="selected-table"
                    ref="selectedTableRef"
                    empty-text="暂无数据"
                    :data="selectedData"
                    :columns="columns"
                    :page-is-show="false"
                    :row-key="rowKey"
                    :scrollbarShow="true"
                    :row-class-name="rowClassName"
                    :selectable="selectable"
                    @row-click="handleRowClick"
                    @select="handleSelected"
                    @select-all="handleSelectedAll"
                    @header-dragend="handleHeaderDragend"
                >
                </Table>
                <div class="buttons bt">
                    <a class="button solid-button ml-10" @click="display = false">关闭</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import Table from "@/components/Table/index.vue";

const selectedTableRef = ref<InstanceType<typeof Table>>();

const props = withDefaults(
    defineProps<{
        columns: Array<IColumnProps>;
        selectedData: Array<any>;
        selectable?: (row: any, rowIndex: number) => boolean;
        rowClassName?: (data: { row: any; rowIndex: number }) => string;
        rowKey?: string;
    }>(),
    {
        selectable: () => true,
        rowClassName: () => "",
        rowKey: "",
    }
);

const emit = defineEmits<{
    (e: "row-click", row: any): void;
    (e: "select", select: Array<any>): void;
    (e: "select-all"): void;
}>();

const display = ref(false);

function handleClosed() {
    selectedTableRef.value?.clearSelection();
}
function handleRowClick(row: any) {
    emit("row-click", row);
}
function handleSelected(select: Array<any>) {
    emit("select", select);
}
function handleSelectedAll() {
    emit("select-all");
}
function handleHeaderDragend() {
    selectedTableRef.value?.getTable()?.doLayout();
}
function handleOpen() {
    display.value = true;
    nextTick().then(() => {
        props.selectedData.forEach((row) => {
            selectedTableRef.value?.getTable()?.toggleRowSelection(row, true);
        });
    });
}
defineExpose({ handleOpen });
</script>

<style lang="less" scoped>
.selected-table-content {
    .bt {
        border-top: 1px solid var(--border-color);
    }
}
</style>
