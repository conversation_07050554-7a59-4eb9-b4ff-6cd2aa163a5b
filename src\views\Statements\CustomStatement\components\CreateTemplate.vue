<template>
    <div class="slot-content-mini create-statement">
        <div class="create-statement-content">
            <div class="create-statement-content-container">
                <div class="title">新建报表模板</div>
                <div class="create-statement-content-center">
                    <div class="buttons-line">
                        <a
                            :class="['button', 'mr-20', { disabled: !tableDataOriginal.length && !columnsRight.length }]"
                            @click="preViewDialogShow = tableDataOriginal.length || columnsRight.length"
                            >预览</a
                        >
                        <el-radio-group v-model="direction">
                            <el-radio :label="1">纵向</el-radio>
                            <el-radio :label="2">横向</el-radio>
                        </el-radio-group>
                    </div>
                    <div class="table-container">
                        <div class="table-rows-content">
                            <el-table
                                :class="['erp-table', 'custom-table', { 'none-data': !tableDataOriginal.length }]"
                                :flexible="true"
                                :fit="false"
                                :show-overflow-tooltip="true"
                                tooltip-effect="light"
                                :data="tableDataOriginal"
                                @cell-mouse-enter="tableOriginalRowMouseEnter"
                                @cell-mouse-leave="mouseEnterCurrentRow = -1"
                            >
                                <el-table-column property="index" label="序号" width="46" :resizable="false">
                                    <template #default="scope">
                                        <div
                                            v-if="tableDataOriginal.length && mouseEnterCurrentRow === scope.row.index"
                                            class="delete-btn"
                                            @click="deleteStatementModuleRow(scope.row.index)"
                                        >
                                            删除
                                        </div>
                                        <span v-else>{{ scope.row.index + 1 }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    v-for="col in columns"
                                    :key="col.label"
                                    :property="col.property"
                                    :label="col.label"
                                    :width="col.width"
                                    :resizable="false"
                                >
                                </el-table-column>
                            </el-table>
                            <div class="add-row">
                                <FilterCustomSelect
                                    v-model="subjectChecked"
                                    :teleported="false"
                                    suffix-icon=""
                                    :filterable="true"
                                    class="add-subject-input mr-10"
                                    @change="handleSubjectChange"
                                    :filter-method="handleAsubNameFilter"
                                    placeholder=" "
                                >
                                    <ElOption
                                        v-for="item in subjectData"
                                        :optionValue="item.asubCode + ' ' + item.asubAAName"
                                        :key="item.asubId"
                                        :label="item.asubAAName"
                                        :value="item.asubId"
                                    >
                                        <span>{{ item.asubCode }} {{ item.asubAAName }}</span>
                                    </ElOption>
                                </FilterCustomSelect>
                                <a
                                    class="button solid-button"
                                    style="height: 32px; line-height: 32px"
                                    @click="batchAddAsubsDialogShow = true"
                                    >添加科目</a
                                >
                            </div>
                        </div>
                        <div class="table-columns-content">
                            <div
                                :class="['table-columns-tables', { 'is-hover': currentColIndex === col.index }]"
                                v-for="col in columnsRight"
                                :key="col.index"
                                @mouseenter="columnsRightMouseEnter($event, col.index)"
                                @mouseleave="currentColIndex = -1"
                            >
                                <el-table
                                    v-show="columnsRight.length"
                                    :class="[
                                        'erp-table',
                                        'custom-table',
                                        'table-columns-right',
                                        { 'none-data': !tableDataOriginal.length },
                                    ]"
                                    :flexible="true"
                                    :fit="false"
                                    :data="col.tableData"
                                >
                                    <el-table-column
                                        :class-name="currentColIndex === col.index ? 'mouse-enter-col' : ''"
                                        property="index"
                                        :label="col.label"
                                        :width="col.width"
                                        :resizable="false"
                                    >
                                        <template v-if="currentColIndex === col.index" #header>
                                            <div class="op-btns">
                                                <div class="edit-btn" @click="editStatementModuleColumn(col)">编辑</div>
                                                <div class="delete-btn" @click="deleteStatementModuleColumn(col.index)">删除</div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <div v-show="columnsRight.length < 5" class="add-column" @click="addStatementModuleColumn()">
                                <div class="add-icon"></div>
                                <div class="add-tip">添加取数类型</div>
                            </div>
                        </div>
                    </div>
                    <div class="checkbox-line">
                        <el-checkbox v-model="addChildAsubs" label="展开下级科目" size="large" />
                    </div>
                    <div class="checkbox-line">
                        <el-checkbox v-model="addLeafAsubs" label="展开所有明细科目" size="large" />
                    </div>
                    <div class="checkbox-line">
                        <el-checkbox v-model="needTotalLine" label="需要合计" size="large" />
                    </div>

                    <div class="btns" v-if="from === 'main'">
                        <a
                            class="button dialog-hidden"
                            @click="
                                emits('goAdd');
                                resetTemplate();
                            "
                            >上一步</a
                        >
                        <a class="button solid-button ml-10 dialog-hidden" @click="statementModuleNextStep">下一步</a>
                    </div>
                </div>
                <div class="buttons mt-10" v-if="from === 'filling'">
                    <a class="button solid-button page-hidden" @click="statementModuleNextStep()">确定</a>
                    <a
                        class="button page-hidden"
                        @click="
                            emits('fillCellsCancel');
                            resetTemplate();
                        "
                        >取消</a
                    >
                </div>
            </div>
            <el-dialog
                v-model="columnTypeDialogShow"
                title="选择取数类型"
                width="388px"
                class="column-type-dialog-container custom-confirm dialogDrag"
            >
                <div class="dialog-content" v-dialogDrag>
                    <div class="dialog-content-body">
                        <div class="form-line">
                            <span class="form-title">时间类型:</span>
                            <el-select 
                                class="form-field" 
                                v-model="columnTypeDateType" 
                                :teleported="false"
                                :filterable="true"
                                :filter-method="dateTypeFilterMethod"
                            >
                                <el-option
                                    v-for="item in showDateTypeOption"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </div>
                        <div class="form-line">
                            <span class="form-title">取数类型:</span>
                            <el-select 
                                class="form-field" 
                                v-model="columnTypeDataType" 
                                :teleported="false"
                                :filterable="true"
                                :filter-method="dataTypeFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showDataTypeOption" 
                                    :key="item.value" 
                                    :label="item.label" 
                                    :value="item.value" 
                                />
                            </el-select>
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" @click="columnTypeAddSure">确定</a>
                        <a
                            class="button"
                            @click="
                                columnTypeDialogShow = false;
                                columnTypeDateType = 13;
                                columnTypeDataType = 1;
                            "
                            >取消</a
                        >
                    </div>
                </div>
            </el-dialog>
            <AddAsubjectDialog v-model="batchAddAsubsDialogShow" @addAsubSure="rerenderTableLeft"></AddAsubjectDialog>
            <PreViewStatementDialog
                v-model="preViewDialogShow"
                :direction="direction"
                :preview-table-data="tableDataOriginal"
                :previewColumns="columns.concat(columnsRight).slice()"
                :addChildAsubs="addChildAsubs"
                :addLeafAsubs="addLeafAsubs"
                :needTotalLine="needTotalLine"
            ></PreViewStatementDialog>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, toRef, type Ref, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { columnRelation, columnDateTypeEnum, columnDataTypeEnum, splitCellName, dateTypeOption, dataTypeOption } from "../utils";
import { type IAccountSubjectModel } from "@/api/accountSubject";
import FilterCustomSelect from "@/views/Statements/components/EditEquation/FilterCustomSelect.vue";
import { ElConfirm } from "@/util/confirm";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { ITableDataItem } from "@/views/Settings/AccountSubject/types";
import type { ICustomStatementListItem, IStatementCell, ICreateTemplateTable, ICreateTemplateColumnsRight } from "../types";
import ElOption from "@/components/Option/index.vue";
import AddAsubjectDialog from "./AddAsubjectDialog.vue";
import PreViewStatementDialog from "./PreViewStatementDialog.vue";
import { getGlobalLodash } from "@/util/lodash";
import { debounce } from "@/views/Statements/utils";
import $bus from "@/bus";
import { ElNotify } from "@/util/notify";
import { pinyin } from "pinyin-pro";
import { commonFilterMethod } from "@/components/Select/utils";

const _ = getGlobalLodash()
const direction = ref(1);
const props = defineProps({
    from: {
        type: String,
        default: "main",
    },
    selectedStartCell: {
        type: String,
        default: "",
    },
});
const emits = defineEmits(["goAdd", "goGenerate", "fillCellsCancel"]);
let baseRowNumber = computed(() => {
    return props.from === "filling" && props.selectedStartCell ? splitCellName(props.selectedStartCell).rowNumber : 1;
});
let baseColumnNumber = computed(() => {
    return props.from === "filling" && props.selectedStartCell ? splitCellName(props.selectedStartCell).columnNumber : 1;
});
const addChildAsubs = ref(false);
const addLeafAsubs = ref(false);
const needTotalLine = ref(false);
const columnTypeDialogShow = ref(false);
const columnTypeDataType = ref(1);
const columnTypeDateType = ref(13);
const batchAddAsubsDialogShow = ref(false);
const preViewDialogShow = ref(false);
const tableData = ref<ICustomStatementListItem[]>([]);
const columns = ref<Array<{ label: string; property: string; width: number }>>([
    {
        label: "科目编码",
        property: "asubCode",
        width: 116,
    },
    {
        label: "科目名称",
        property: "asubAAName",
        width: 178,
    },
]);
const tableDataOriginal = ref<Array<ICreateTemplateTable>>([]);
const dataTypeList = ref([1, 2, 3, 4, 5, 6, 7]);
let currentColIndex = ref(-1);

watch(
    columnTypeDateType,
    (v: number) => {
        dataTypeList.value = columnRelation[v - 1].dataType;
        if (!dataTypeList.value.includes(columnTypeDataType.value)) {
            columnTypeDataType.value = dataTypeList.value[0];
        }
    },
    { immediate: true }
);
const columnsRight = ref<Array<ICreateTemplateColumnsRight>>([]);
const isEditStatementModuleIndex = ref(-1);

const columnTypeAddSureFn = () => {
    if (isEditStatementModuleIndex.value > -1) {
        columnsRight.value[isEditStatementModuleIndex.value].label =
            columnDateTypeEnum[columnTypeDateType.value] + "." + columnDataTypeEnum[columnTypeDataType.value];
    } else {
        columnsRight.value.push({
            label: columnDateTypeEnum[columnTypeDateType.value] + "." + columnDataTypeEnum[columnTypeDataType.value] || "",
            property: "prop",
            width: 148,
            tableData: [],
            index: columnsRight.value.length,
            dateType: columnTypeDateType.value,
            dataType: columnTypeDataType.value,
        });
    }

    columnTypeDialogShow.value = false;
    currentColIndex.value = -1;
};
const columnTypeAddSure = debounce(columnTypeAddSureFn, 1000);
const addStatementModuleColumn = () => {
    isEditStatementModuleIndex.value = -1;
    columnTypeDialogShow.value = true;
    columnTypeDateType.value = 13;
    columnTypeDataType.value = 1;
};
const statementModuleNextStep = () => {
    let rowsClone = _.cloneDeep(tableDataOriginal.value);
    let rows: Array<{ asub: ICreateTemplateTable }> = [];
    rowsClone.forEach((v, i) => {
        delete v.index;
        rows.push({ asub: v });
    });
    let columns = columnsRight.value.map((v, i) => {
        return {
            dateType: v.dateType,
            dataType: v.dataType,
        };
    });
    request({
        url: "/api/CustomStatement/GetCellsByStatementModule?statementId=0",
        method: "post",
        headers: {
            "Content-Type": "application/json",
        },
        data: {
            BaseRowNumber: baseRowNumber.value,
            BaseColumnNumber: baseColumnNumber.value,
            rows,
            columns,
            AddChildAsubs: addChildAsubs.value,
            AddLeafAsubs: addLeafAsubs.value,
            NeedTotalLine: needTotalLine.value,
            TableDirection: direction.value,
        },
    })
        .then((res: IResponseModel<{ [key: string]: IStatementCell }>) => {
            if (res.state === 1000) {
                $bus.emit(props.from === "filling" ? "getFillingCellsByStatementModule" : "getCellsByStatementModule", res.data);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg || "保存失败",
                });
            }
        })
        .then(() => {
            emits("goGenerate");
            resetTemplate();
        });
};
const accountSubjectStore = useAccountSubjectStore();
const subjectDataAll: Ref<IAccountSubjectModel[]> = toRef(accountSubjectStore, "accountSubjectListWithoutDisabled");
const subjectData = ref<IAccountSubjectModel[]>(_.cloneDeep(subjectDataAll.value));
watch(
    () => subjectDataAll,
    () => {
        subjectData.value = _.cloneDeep(subjectDataAll.value);
    }
);
const subjectChecked = "";

const handleSubjectChange = (v: number) => {
    subjectData.value = subjectDataAll.value;
    let checkedAsub: IAccountSubjectModel[] = subjectData.value.filter((vl) => vl.asubId === v);
    tableDataOriginal.value.push({ index: tableDataOriginal.value.length, ...checkedAsub[0] });
};
const handleAsubNameFilter = (v: string) => {
    const lowerCaseValue = v.trim().toLowerCase();
    subjectData.value = subjectDataAll.value.filter((item: IAccountSubjectModel, index: number) =>{
        const itemProp = item.asubCode + ' ' + item.asubAAName; 
        const pinyinFirst = pinyin(itemProp, {   
            pattern: "first",   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();
        const pinyinFull = pinyin(itemProp, {   
            toneType: "none",   
            type: "array"   
        }).join("").toLowerCase();

        return (
            item.asubCode.startsWith(v.trim()) 
            || item.asubAAName.includes(v.trim()) 
            || item.acronym.includes(v.trim()) 
            || pinyinFirst.includes(lowerCaseValue) 
            || pinyinFull.includes(lowerCaseValue)
        );
    });
};
const rerenderTableLeft = (data: ITableDataItem[]) => {
    data.forEach((v) => {
        tableDataOriginal.value.push({ index: tableDataOriginal.value.length, ...v });
    });
};
let mouseEnterCurrentRow = ref(-1);

const tableOriginalRowMouseEnter = (row: any) => {
    mouseEnterCurrentRow.value = row.index;
};
const deleteStatementModuleRow = (index: number) => {
    tableDataOriginal.value.splice(index, 1);
    tableDataOriginal.value.forEach((v, i) => {
        v.index = i;
    });
};
const resetTemplate = () => {
    addChildAsubs.value = false;
    addLeafAsubs.value = false;
    needTotalLine.value = false;
    tableData.value = [];
    direction.value = 1;
    columnsRight.value = [];
    tableDataOriginal.value = [];
};
watch([() => tableDataOriginal.value.length, () => columnsRight.value.length], () => {
    if (columnsRight.value.length) {
        columnsRight.value.forEach((v, i) => {
            v.tableData = [];
            for (let row = 0; row < tableDataOriginal.value.length; row++) {
                v.tableData.push({ index: " " });
            }
        });
    }
});
const columnsRightMouseEnter = (e: any, index: number) => {
    currentColIndex.value = index;
};
const deleteStatementModuleColumn = (index: number) => {
    ElConfirm("确定删除吗？").then((r: boolean) => {
        if (r) {
            columnsRight.value.splice(index, 1);
            if (columnsRight.value.length) {
                columnsRight.value.forEach((v, i) => {
                    v.index = i;
                });
            }
        }
    });
};
const editStatementModuleColumn = (col: ICreateTemplateColumnsRight) => {
    isEditStatementModuleIndex.value = col.index;
    columnTypeDialogShow.value = true;
    columnTypeDateType.value = col.dateType;
    columnTypeDataType.value = col.dataType;
};

const dataTypeOptionAll = ref<Array<any>>([]);
const showDataTypeOption = ref<Array<any>>([]);
const showDateTypeOption = ref<Array<any>>([]);
watchEffect(() => {  
    dataTypeOptionAll.value = dataTypeOption.filter(option =>   
        dataTypeList.value.includes(option.value)  
    );  
    showDataTypeOption.value = JSON.parse(JSON.stringify(dataTypeOptionAll.value));
});
watchEffect(() => {
    showDateTypeOption.value = JSON.parse(JSON.stringify(dateTypeOption));
});
function dataTypeFilterMethod(value: string) {
    showDataTypeOption.value = commonFilterMethod(value, dataTypeOptionAll.value, 'label');
}
function dateTypeFilterMethod(value: string) {
    showDateTypeOption.value = commonFilterMethod(value, dateTypeOption, 'label');
}
</script>

<style lang="less" scoped>
.create-statement {
    overflow: auto;
    .create-statement-content {
        margin: 0 auto;
        height: auto;
        :deep(.el-table) {
            .el-table__header-wrapper {
                border-top-left-radius: 0;
                border-top-right-radius: 0;
            }
            .el-table__body-wrapper {
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
        }
        width: 1142px !important;
        box-sizing: border-box;
        .create-statement-content-center {
            padding: 0 24px;
            height: 100vh;
            display: flex;
            flex-direction: column;

            .buttons-line {
                display: flex;
                justify-content: start;
                align-items: center;
                margin-top: 16px;
                :deep(.el-radio-group .el-radio__inner) {
                    width: 16px;
                    height: 16px;
                }
            }
            .table-container {
                width: 100%;
                display: inline-block;
                box-sizing: border-box;
                min-height: 306px;
                flex: 1;
                border-radius: 2px 0px 2px 0px;
                border: 1px solid var(--table-border-color);
                font-size: 0;
                margin: 16px 0 16px;
                overflow: auto;
                display: flex;
                flex: 1;
                .table-rows-content {
                    font-size: 0;
                    display: inline-block;
                    vertical-align: top;
                    :deep(.el-table) {
                        .el-popper.is-light {
                            max-width: 300px;
                            text-align: left;
                        }
                    }
                    .custom-table {
                        width: auto;
                        border: none;
                        border-radius: 0;
                        &.none-data {
                            :deep(.el-table__body-wrapper) {
                                display: none;
                            }
                        }
                        :deep(.cell) {
                            .delete-btn {
                                text-align: center;
                                height: 20px;
                                width: 30px;
                                line-height: 18px;
                                border-radius: 2px;
                                border: 1px solid var(--button-red);
                                background-color: var(--white);
                                font-size: 10px;
                                color: var(--button-red);
                                cursor: pointer;
                                margin: 0 -4px;
                            }
                        }
                    }

                    .add-row {
                        font-size: 0;
                        display: flex;
                        justify-content: right;
                        margin-top: 4px;
                        padding-right: 5px;
                        padding-top: 4px;
                        padding-bottom: 4px;
                        .add-subject-input {
                            width: 212px;
                            height: 28px;
                            outline: none;
                            padding: 0;
                            padding-left: 10px;
                            padding-right: 10px;
                            color: var(--font-color);
                            font-size: var(--font-size);
                            line-height: 28px;
                            box-sizing: border-box;
                            border-radius: var(--input-border-radius);
                            :deep(.el-select .el-input) {
                                height: 28px;
                            }
                        }
                    }
                }
                .table-columns-content {
                    font-size: 0;
                    display: inline-block;
                    vertical-align: top;
                    display: flex;
                    .table-columns-tables {
                        display: inline-block;
                        vertical-align: top;
                        .table-columns-right {
                            border: none;
                            border-radius: 0;
                            &.none-data {
                                :deep(.el-table__body-wrapper) {
                                    display: none;
                                }
                            }
                            :deep(thead tr th div.cell:before) {
                                content: "";
                                height: 16px;
                                width: 1px;
                                background-color: #d0dae8;
                                display: block;
                                position: absolute;
                                left: 0;
                                top: 50%;
                                transform: translateY(-50%);
                            }
                            :deep(.mouse-enter-col) {
                                .cell {
                                    .op-btns {
                                        div {
                                            display: inline-block;
                                            vertical-align: top;
                                            width: 34px;
                                            height: 20px;
                                            background-color: var(--white);
                                            border-radius: 2px;
                                            text-align: center;
                                            line-height: 18px;
                                            cursor: pointer;
                                            font-size: 10px;
                                            border: 1px solid;
                                        }
                                        .edit-btn {
                                            border-color: var(--link-color);
                                            color: var(--link-color);
                                        }
                                        .delete-btn {
                                            border-color: var(--button-red);
                                            color: var(--button-red);
                                            margin-left: 4px;
                                        }
                                    }
                                }
                            }
                        }
                        &.is-hover {
                            :deep(.el-table .el-table__cell) {
                                background-color: var(--el-table-row-hover-bg-color);
                            }
                        }
                    }
                    .add-column {
                        vertical-align: top;
                        display: inline-block;
                        border: 1px dashed var(--main-color);
                        width: 120px;
                        height: 248px;
                        border-radius: 1px;
                        text-align: center;
                        cursor: pointer;
                        .add-icon {
                            height: 30px;
                            width: 30px;
                            background-size: 100%;
                            background-repeat: no-repeat;
                            background-image: url(@/assets/Statements/erp-add-icon.png);
                            margin-top: 93px;
                            vertical-align: top;
                            display: inline-block;
                        }
                        .add-tip {
                            font-size: var(--h5);
                            line-height: 17px;
                            color: var(--main-color);
                            margin-top: 16px;
                        }
                    }
                }
            }
            .checkbox-line {
                margin-top: 8px;
                text-align: left;
                .el-checkbox.el-checkbox--large {
                    height: 20px;
                }
            }
        }

        .btns {
            margin-top: 30px;
            padding-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .column-type-dialog-container {
            .dialog-content-body {
                padding: 18px 0 34px;
            }
            .form-line {
                font-size: 0;
                text-align: center;
                margin-top: 16px;
                .form-title {
                    text-align: left;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: var(--line-height);
                    line-height: 30px;
                    display: inline-block;
                    margin-right: 8px;
                    vertical-align: top;
                }
                .form-field {
                    vertical-align: top;
                    text-align: left;
                    display: inline-block;
                }
            }
        }
    }
}
</style>
