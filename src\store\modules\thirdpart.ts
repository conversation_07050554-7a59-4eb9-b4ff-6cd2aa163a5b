import { request, type IResponseModel } from "@/util/service";
import { defineStore } from "pinia";
import { ref } from "vue";
import store from "..";
import { getCookie } from "@/util/cookie";

export const useThirdPartInfoStore = defineStore("thirdPart", () => {
    const isThirdPart = ref(false);
    const isHideBarcode = ref(false);
    const isOEM = ref(false);

    const getThirdPartInfo = () => {
        return new Promise<{ isThirdPart: boolean; isHideBarcode: boolean; isOem: boolean }>((resolve, reject) => {
            request({
                url: "/api/ThirdPart",
                method: "get",
            })
                .then((res: IResponseModel<{ isThirdPart: boolean; isHideBarcode: boolean; isOem: boolean }>) => {
                    if (res.state === 1000) {
                        isThirdPart.value = res.data.isThirdPart;
                        isHideBarcode.value = res.data.isHideBarcode;
                        isOEM.value = res.data.isOem;
                        resolve(res.data);
                    } else {
                        reject(res.msg);
                    }
                })
                .catch((error) => {
                    reject(error);
                });
        });
    };
    return { isThirdPart, isHideBarcode, isOEM, getThirdPartInfo };
});

/** 在setup外使用 */
export function useThirdPartInfoStoreHook() {
    return useThirdPartInfoStore(store);
}
