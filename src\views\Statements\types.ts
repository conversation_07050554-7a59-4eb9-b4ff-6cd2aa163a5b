export interface IValueTypeOption {
    value: string;
    label: string;
}
export interface IValueTypeOption {
    value: string;
    label: string;
}
export interface IEquationItem {
    as_id?: number;
    asub_id: number;
    asub_name: string;
    amount: number;
    column_type?: number;
    equation_id?: number;
    equationSn?: number;
    equationType?: number;
    flag?: number;
    initalAmount: number;
    line_id?: number;
    multiplication?: number;
    operator: number;
    rate?: number;
    statement_id?: number;
    source?: number;
    value_type: number;
}

export interface IEquationSingleValue {
    amount: number;
    initalAmount: number;
}
export interface ITableLineIDItem {
    asid: number;
    asubid: number;
    asubname: String;
    asubtype: number;
    columnType: number;
    derection: number; 
    equationID: number;
    equationSn: number;
    equationType: number;
    flag: number; 
    lineId: number;
    multiplication: number;
    operator: number;
    rate: string;
    source: number;
    statementID: number;
    valueType: number;
}
