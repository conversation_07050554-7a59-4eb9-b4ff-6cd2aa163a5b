export interface ISearchParams {
    startP: number;
    endP: number;
    startPText: string;
    endPText: string;
    prepareBy: string;
    description: string;
    approvalStatus: number;
    noApproved: number;
    startMoney: string;
    endMoney: string;
    startVoucherNo: string;
    endVoucherNo: string;
    sortColumn: string;
    VG_ID: string | number;
    subjectCode: string;
}

export interface ITableData {
    v_date: string;
    v_num: string;
    asub_code: string;
    asub_name: string;
    asub_names: string;
    description: string;
    debit: number;
    credit: number;
    direction: string;
    total: number;
    p_id: number;
    v_id: number;
}

export interface IJournalTableData {
    columns: string[];
    rows: object[];
    total: number;
}