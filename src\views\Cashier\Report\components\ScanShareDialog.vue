<template>
  <el-dialog class="dialogDrag" v-model="dialogVisible" title="扫码分享" width="520px" :show-close="true" @close="handleClose"
    destroy-on-close>
    <div class="scan-share-dialog" v-dialogDrag>
      <!-- 1.1 功能介绍部分 -->
      <div class="feature-intro">
        <h3 class="intro-text">柠檬云财务APP支持生成资金报表长图保存或分享至微信、朋友圈</h3>
        <div class="intro-images">
          <div class="app-share">
            <img :src="appShareImg" alt="APP分享示例" />
          </div>
          <div class="report-preview">
            <img :src="reportPreviewImg" alt="APP分享示例" />
          </div>
        </div>
      </div>

      <!-- 1.2 扫码部分 -->
      <div class="scan-section">
        <div class="scan-title"></div>
        <div class="qrcode-wrapper">
          <div class="qrcode-box" v-if="qrCodeVisible">
            <img class="qrCodeImg" :src="qrCodeSrc" />
            <img src="@/assets/AppConfirm/logo_qr.png" class="qr-logo" alt="" style="top: 44%" />
          </div>
          <div class="expired-qrcode-box" v-else>
            <div>二维码过期</div>
            <a class="link" @click="() => initShareDialog()">请点击刷新</a>
          </div>
          <p class="scan-tip">柠檬云财务APP扫一扫分享</p>
          <p class="download-tip">(未安装柠檬云财务APP,请使用微信扫码下载)</p>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getServiceId } from "@/util/proUtils";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";

const accountsetStore = useAccountSetStore();
const asId = accountsetStore.accountSet?.asId || 0;

const dialogVisible = ref(false)

const appShareImg = new URL('/src/assets/Cashier/reportScan1.png', import.meta.url).href
const reportPreviewImg = new URL('/src/assets/Cashier/reportScan2.png', import.meta.url).href
const qrCodeSrc = ref('');

const handleClose = () => {
  clearTimeout(scanTimer);
  dialogVisible.value = false
}

let polling = false;
const qrCodeVisible = ref(true)
interface IScanParams {
  tab: string,
  startDate: string,
  endDate: string,
  accIds?: number[],
  fcIds?: number[],
}
let scanParams: IScanParams
const initShareDialog = (params?: IScanParams) => {
  params && (scanParams = params)
  const scanData = {
    scanType: 1,
    scanContent: JSON.stringify({
      asId,
      serviceId: getServiceId(),
      pageName: "FinancialReport",
      pageParam: scanParams,
    })
  }

  request({
    url: '/api/AppScan/GenerateGuid',
    method: 'post',
    data: scanData
  }).then((res: IResponseModel<string>) => {
    if (res.state !== 1000) return
    const downloadUrl = "https://a.app.qq.com/o/simple.jsp?pkgname=com.wta.NewCloudApp.jiuwei117478";
    qrCodeSrc.value =
      window.accountSrvHost +
      "/api/WxPay/MakeQRCode.ashx" +
      "?data=" +
      encodeURIComponent(downloadUrl + "&type=1&guid=" + res.data) +
      "&CurrentSystemType=1";
    dialogVisible.value = true
    qrCodeVisible.value = true
    polling = true;
    scanPolling(res.data);
  })
}
let scanTimer: number;
function scanPolling(guid: string) {
  request({
    url: `/api/AppScan/Scanned?guid=` + guid,
    method: "post",
  }).then((res: any) => {
    if (res.state === 1000) {
      if (polling) {
        scanTimer = setTimeout(() => scanPolling(guid), 5 * 1000);
      }
    } else if (res.state === 2000) {
      qrCodeVisible.value = false;
    } else {
      ElNotify({
        type: "error",
        message: "出现异常，请刷新页面重试或联系系统管理员",
      });
    }
  });
}

defineExpose({
  initShareDialog,
})
</script>

<style lang="less" scoped>
.scan-share-dialog {

  .feature-intro {
    margin-bottom: 20px;

    .intro-text {
      font-size: 16px;
      color: #333;
      margin-bottom: 24px;
      text-align: center;
    }

    .intro-images {
      display: flex;
      justify-content: space-around;
      align-items: center;

      img {
        width: 248px;
        height: 180px;
        object-fit: cover;
      }

      p {
        font-size: 14px;
        color: #666;
        text-align: center;
        margin-top: 12px;
      }
    }
  }

  .scan-section {
    text-align: center;

    .scan-title {
      border-top: 2px dotted #000000;
    }

    .qrcode-wrapper {
      display: inline-block;

      .qrcode-box {
        position: relative;
        display: inline-block;
        vertical-align: middle;

        .qrCodeImg {
          display: inline-block;
          width: 140px;
          height: 140px;
          background: #D8D8D8;
          margin: 10px 0 0px 0;
        }

        .qr-logo {
          position: absolute;
          top: 50%;
          left: 50%;
          margin-left: -11px;
        }
      }

      .expired-qrcode-box {
        width: 140px;
        height: 90px;
        background: #D8D8D8;
        color: black;
        margin: 10px auto 5px auto;
        padding-top: 50px;
      }

      .scan-tip {
        font-size: 14px;
        color: #333;
        margin-top: 10px;
      }

      .download-tip {
        font-size: 12px;
        color: #999;
        margin-top: 8px;
      }
    }
  }
}
</style>