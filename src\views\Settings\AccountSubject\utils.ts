import { AccountStandard, useAccountSetStoreHook } from "@/store/modules/accountSet";
import type { ITabListItem, ITableDataItem } from "./types";
import type { IAccountSubjectModel } from "@/api/accountSubject.js";
export function getAccountSubjectTabList(): ITabListItem[] {
    const accountSetStore = useAccountSetStoreHook();
    const accountSet = accountSetStore.accountSet;
    const accountStandard = accountSet?.accountingStandard;
    let tabList: ITabListItem[] = [];
    switch (accountStandard as number) {
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            tabList = [
                {
                    label: "资产",
                    name: "aasj",
                    asubTypeCode: asubTypeCode["资产类"],
                },
                {
                    label: "负债",
                    name: "dasj",
                    asubTypeCode: asubTypeCode["负债类"],
                },
                {
                    label: "权益",
                    name: "oasj",
                    asubTypeCode: asubTypeCode["权益类"],
                },
                {
                    label: "成本",
                    name: "casj",
                    asubTypeCode: asubTypeCode["成本类"],
                },
                {
                    label: "损益",
                    name: "iasj",
                    asubTypeCode: asubTypeCode["损益类"],
                },
            ];
            break;
        case AccountStandard.CompanyStandard:
            tabList = [
                {
                    label: "资产",
                    name: "aasj",
                    asubTypeCode: asubTypeCode["资产类"],
                },
                {
                    label: "负债",
                    name: "dasj",
                    asubTypeCode: asubTypeCode["负债类"],
                },
                {
                    label: "共同",
                    name: "cmasj",
                    asubTypeCode: asubTypeCode["共同类"],
                },
                {
                    label: "权益",
                    name: "oasj",
                    asubTypeCode: asubTypeCode["权益类"],
                },
                {
                    label: "成本",
                    name: "casj",
                    asubTypeCode: asubTypeCode["成本类"],
                },
                {
                    label: "损益",
                    name: "iasj",
                    asubTypeCode: asubTypeCode["损益类"],
                },
            ];
            break;
        case AccountStandard.FolkComapnyStandard:
            tabList = [
                {
                    label: "资产",
                    name: "aasj",
                    asubTypeCode: asubTypeCode["资产类"],
                },
                {
                    label: "负债",
                    name: "dasj",
                    asubTypeCode: asubTypeCode["负债类"],
                },
                {
                    label: "净资产",
                    name: "nasj",
                    asubTypeCode: asubTypeCode["净资产类"],
                },
                {
                    label: "收入",
                    name: "resj",
                    asubTypeCode: asubTypeCode["收入类"],
                },
                {
                    label: "费用",
                    name: "exsj",
                    asubTypeCode: asubTypeCode["费用类"],
                },
            ];
            break;
        case AccountStandard.UnionStandard:
            tabList = [
                {
                    label: "资产",
                    name: "aasj",
                    asubTypeCode: asubTypeCode["资产类"],
                },
                {
                    label: "负债",
                    name: "dasj",
                    asubTypeCode: asubTypeCode["负债类"],
                },
                {
                    label: "净资产",
                    name: "nasj",
                    asubTypeCode: asubTypeCode["净资产类"],
                },
                {
                    label: "收入",
                    name: "resj",
                    asubTypeCode: asubTypeCode["收入类"],
                },
                {
                    label: "支出",
                    name: "dissj",
                    asubTypeCode: asubTypeCode["支出类"],
                },
            ];
            break;
    }
    return tabList;
}
export enum asubTypeCode {
    "资产类" = 1,
    "负债类" = 2,
    "共同类" = 3,
    "权益类" = 4,
    "成本类" = 5,
    "损益类" = 6,
    "净资产类" = 7,
    "收入类" = 8,
    "费用类" = 9,
    "支出类" = 10,
}
export function setCodeClass(level: number) {
    switch (level) {
        case 2:
            return "pl-10";
        case 3:
            return "pl-20";
        case 4:
            return "pl-30";
        case 5:
            return "pl-40";
        case 6:
            return "pl-50";
        case 7:
            return "pl-60";
        case 8:
            return "pl-70";
        case 9:
            return "pl-80";
        case 10:
            return "pl-90";
        default:
            return "";
    }
}
export function getCodeExample(code: number) {
    let str = "";
    for (let i = 0; i < code - 1; i++) {
        str += 0;
    }
    return (str += 1);
}
type TableDataItem = ITableDataItem | IAccountSubjectModel;
export const getAsubCodeIsUsedBaseItem = <T extends TableDataItem>(
    tableData: T[],
    value: string,
    parentId?: number,
): T | null => {
    const findItem = tableData.find((item: T) => 
        item.parentId === parentId && item.asubName.trim().replace("(", "（").replace(")", "）") === value?.trim()
    );
    return findItem || null;
};