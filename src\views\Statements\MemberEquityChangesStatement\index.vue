<template>
    <div class="content">
        <div class="title">成员权益变动表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef" />
                </div>
                <div class="main-tool-right">
                    <Dropdown :btnTxt="'打印'" :downlistWidth="102" v-permission="['businessactivitystatement-canprint']">
                        <li @click="handlePrint(0,{pid: searchInfo.pid,})">当前报表数据</li>
                        <li @click="handlePrint(1)">批量打印</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <div class="button large-1 dropdown ml-16" v-permission="['memberequitychangesstatement-canexport']">
                        <div style="width: 100%; text-align: center">导出</div>
                        <div class="downlist" style="width: 100%">
                            <div class="downlist-buttons large">
                                <div @click="handleExport(0)">当前报表数据</div>
                                <div @click="handleExport(1)">批量导出</div>
                            </div>
                        </div>
                    </div>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <MemberTable
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :empty-text="emptyText"
                    :tableData="tableData"
                ></MemberTable>
            </div>
        </div>
    </div>
    <!-- 打印导出弹窗 -->
    <PrintOrExportDialog
        :show-dialog="showDialog"
        :type="dialogType"
        :pid="searchInfo.pid"
        :fromStatement="36"
        @close="handleDialogClose"
    />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="成员权益变动表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,{pid: searchInfo.pid})"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "MemberEquityChangesStatement",
};
</script>
<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import MemberTable from "./components/MemberTable.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import { useRoute } from "vue-router";
import { globalPrint, globalExport } from "@/util/url";
import { reactive, ref, watch } from "vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import usePrint from "@/hooks/usePrint";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";

const periodStore = useAccountPeriodStore();
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const route = useRoute();
const searchInfo = reactive({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    classification: false,
});

const tableData = ref<any[]>([]);
let loading = ref(false);
let emptyText = ref("");
function handleSearch() {
    loading.value = true;
    emptyText.value = " ";
    // 获取成员权益变动表
    request({
        url: `/api/MemberEquityChangesStatement`,
        params: { PId: searchInfo.pid },
        method: "get",
    })
        .then((res: IResponseModel<object[]>) => {
            loading.value = false;
            tableData.value = [];
            if (res.state === 1000) {
                tableData.value = res.data;
            }
            emptyText.value = !tableData.value.length ? "暂无数据" : "";
        })
        .catch((error) => {
            console.log(error);
        });
}

// 打印导出
const handleDialogClose = () => {
    showDialog.value = false;
};

const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "memberEquityChangesStatement",
    `/api/MemberEquityChangesStatement/Print`,
    {},
    false,
    false,
);

//导出
const handleExport = (exportType: number) => {
    if (exportType === 0) {
        globalExport(`/api/MemberEquityChangesStatement/Export?PId=${searchInfo.pid}`);
    } else {
        // 批量导出
        dialogType.value = "export";
        showDialog.value = true;
    }
};

watch(searchInfo, handleSearch, { immediate: true });
</script>

<style scoped lang="less">
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";

:deep(.el-table) {
    .cell {
        padding: 0 4px;
        height: 37px;
        line-height: 37px;

        tr {
            height: 100%;

            td {
                display: table-cell;
                vertical-align: middle;
            }

            .firsttd {
                padding: 0 4px;
                width: 72px;
                word-wrap: normal;
                display: inline-block;
                vertical-align: middle;
            }

            .wrap {
                line-height: 18px;
            }

            background-color: transparent;

            &:hover {
                background-color: var(--table-hover-color);
            }
        }
    }
}
</style>
