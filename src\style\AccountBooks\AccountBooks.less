@import "@/style/functions.less";

.content {
    .main-content {
        .main-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--white);
            box-sizing: border-box;
            :deep(.el-checkbox__label) {
                color: var(--font-color) !important;
                cursor: default !important;
            }
        }
    }
    .main-title {
        border: 1px solid var(--border-color);
        border-bottom: none;
        margin: 0 10px;
        padding-left: 8px;
        height: 36px;
        color: var(--font-color);
        font-size: var(--h5);
        line-height: 36px;
        display: flex;
    }
    .main-center {
        padding: 0 20px 10px;
    }
}

.print-content {
    text-align: center;
    .print-main {
        text-align: left;
        display: inline-block;

        .line-item {
            height: 20px;
            .set-font;
            display: flex;
            align-items: center;
        }
    }

    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}
body[erp] {
    .a_nofloat {
        a {
            float: none !important;
            display: inline-block !important;
        }
    }
    .main-center {
        :deep(.el-table) {
            border: none;
            border-radius: 0;
            .el-table__header-wrapper{
                border-radius: 0;
            }
            &.el-table--border {
                &::before,
                &::after {
                    width: 1px;
                }
            }
            .el-table__inner-wrapper {
                &::before,
                &::after {
                    height: 1px;
                }
            }
        }
        .custom-table{
            border-radius: 0;
        }
    }
}
