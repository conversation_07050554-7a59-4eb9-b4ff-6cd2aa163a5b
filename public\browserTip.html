<!DOCTYPE html>
<html style="overflow: auto">
    <head>
        <meta charset="utf-8" />
        <title>柠檬云财税</title>
        <style>
            html,
            body {
                height: 100%;
                width: 100%;
                margin: 0;
                padding: 0;
            }

            body {
                background-image: url("/browerTip-bg.png");
                background-repeat: no-repeat;
                background-color: white;
                background-position: center;
                background-size: 100% auto;
            }

            .buttons {
                position: absolute;
                top: 47.96%;
                left: 37.76%;
                display: flex;
                align-items: center;
                margin-top: calc(- (100vw / (1920 / 1080) - 100vh) / 2);
            }

            .button {
                display: inline-block;
                width: 280px;
                height: 75px;
                color: white;
                font-size: 28px;
                line-height: 75px;
                text-align: center;
                cursor: pointer;
                background-color: #44b449;
                font-weight: 500;
                border-radius: 38px;
                outline: none;
                text-decoration: none;
            }

            .button:hover {
                background-color: #6ed773;
            }

            .button:active {
                background-color: #26922b;
            }
        </style>
    </head>
    <body>
        <div class="buttons">
            <a target="_blank" href="http://chrome.360.cn/" class="button">360浏览器</a>
            <a target="_blank" href="https://www.google.cn/chrome/" class="button" style="margin-left: 60px">谷歌浏览器</a>
        </div>
    </body>
</html>
