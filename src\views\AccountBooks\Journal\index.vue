<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">序时账</div>
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <SearchInfoContainer ref="containerRef">
                                <template v-slot:title>{{ currentPeriodInfo }}</template>
                                <div class="line-item first-item input">
                                    <div class="line-item-title">会计期间：</div>
                                    <div class="line-item-field">
                                        <DatePicker
                                            v-model:startPid="searchInfo.startMonth"
                                            v-model:endPid="searchInfo.endMonth"
                                            :clearable="false"
                                            :editable="false"
                                            :dateType="'month'"
                                            :value-format="'YYYYMM'"
                                            :label-format="'YYYY年MM月'"
                                            :isPeriodList="true"
                                            @getActPid="getActPid"
                                        />
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">凭证字：</div>
                                    <div class="line-item-field line-item-select">
                                        <Select
                                            style="width: 132px"
                                            :teleported="false"
                                            v-model="searchInfo.voucher"
                                            :fit-input-width="true"
                                            :filterable="true"
                                            :filter-method="voucherGroupFilterMethod"
                                        >
                                            <Option v-for="item in showVoucherGroupList" :key="item.id" :value="item.id" :label="item.title" />
                                        </Select>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">制单人：</div>
                                    <div class="line-item-field line-item-select">
                                        <Select
                                            style="width: 132px"
                                            :teleported="false"
                                            v-model="searchInfo.prepareBy"
                                            :fit-input-width="true"
                                            :filterable="true"
                                            :filter-method="prepareFilterMethod"
                                        >
                                            <Option v-for="item in showPrepareOption" :key="item" :value="item" :label="item" />
                                        </Select>
                                    </div>
                                </div>
                                <div class="line-item input" v-if="accountSet?.checkNeeded">
                                    <div class="line-item-title">是否审核：</div>
                                    <div class="line-item-field line-item-select">
                                        <el-select style="width: 132px" :teleported="false" v-model="searchInfo.approvalStatus">
                                            <el-option
                                                v-for="item in approvalStatusOption"
                                                :key="item.value"
                                                :value="item.value"
                                                :label="item.label"
                                            ></el-option>
                                        </el-select>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">摘要：</div>
                                    <div class="line-item-field">
                                        <el-input style="width: 298px" v-model="searchInfo.description" clearable></el-input>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">科目：</div>
                                    <div class="line-item-field">
                                        <SubjectPicker
                                            ref="startAsubRef"
                                            v-model="searchInfo.subjectCode"
                                            :isById="true"
                                            asubImgRight="14px"
                                            :showDisabled="true"
                                        ></SubjectPicker>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">金额：</div>
                                    <div class="line-item-field">
                                        <el-input v-model="searchInfo.startMoney" style="width: 132px" clearable></el-input>
                                        <div class="ml-10 mr-10">至</div>
                                        <el-input v-model="searchInfo.endMoney" style="width: 132px" clearable></el-input>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">凭证号：</div>
                                    <div class="line-item-field">
                                        <el-input v-model="searchInfo.startVoucher" style="width: 132px" clearable></el-input>
                                        <div class="ml-10 mr-10">至</div>
                                        <el-input v-model="searchInfo.endVoucher" style="width: 132px" clearable></el-input>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">排序方式：</div>
                                    <div class="line-item-field">
                                        <el-radio-group v-model="searchInfo.sortColumn">
                                            <el-radio label="V_NUM" size="large">凭证号排序</el-radio>
                                            <el-radio label="V_DATE" size="large">凭证日期排序</el-radio>
                                        </el-radio-group>
                                    </div>
                                </div>
                                <div class="buttons">
                                    <a class="button solid-button" @click="handleSearch">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <a class="button" @click="handleExport" v-permission="['journal-canexport']">导出</a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div v-loading="loading" element-loading-text="正在加载数据..." class="main-center">
                        <AccountBooksTable
                            :data="tableData"
                            :columns="columns"
                            :loading="loading"
                            :empty-text="emptyText"
                            :pageIsShow="true"
                            :page-sizes="paginationData.pageSizes"
                            :page-size="paginationData.pageSize"
                            :total="paginationData.total"
                            :current-page="paginationData.currentPage"
                            :scrollbar-show="true"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            @refresh="handleRerefresh"
                            :tooltipOptions="{ effect: 'light', placement: 'right-start' }"
                            :tableName="setModule"
                        >
                            <template #vgname>
                                <el-table-column 
                                    label="凭证号" 
                                    align="left" 
                                    headerAlign="left"
                                    prop="vgname"
                                    :width="getColumnWidth(setModule, 'vgname')"
                                >
                                    <template #default="scope">
                                    <span 
                                        v-show="scope.row.v_num !== ''" 
                                        :class="checkPermission(['voucher-canview']) ? 'link' : 'cursor-default'"  @click="checkPermission(['voucher-canview']) ?ShowDetail(scope.row):''"
                                    >
                                        {{ scope.row.v_num.replace(/<[^<span>]+>/g, "") }}
                                    </span>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #debit_qut>
                                <el-table-column 
                                    label="借方数量" 
                                    align="right" 
                                    headerAlign="right" 
                                    min-width="120"
                                    prop="debit_qut"
                                    :width="getColumnWidth(setModule, 'debit_qut')"
                                >
                                    <template #default="scope">
                                        <el-tooltip placement="right" effect="light" :disabled="showSpanPopover(scope.row.debit_qut)">
                                            <template #content>{{ scope.row.debit_qut }}</template>
                                            <span>{{ formatClipPointNum(scope.row.debit_qut) }}</span>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </template>
                            <template #credit_qut>
                                <el-table-column 
                                    label="贷方数量" 
                                    align="right" 
                                    headerAlign="right" 
                                    min-width="120"
                                    prop="credit_qut"
                                    :width="getColumnWidth(setModule, 'credit_qut')"
                                >
                                    <template #default="scope">
                                        <el-tooltip placement="right" effect="light" :disabled="showSpanPopover(scope.row.credit_qut)">
                                            <template #content>{{ scope.row.credit_qut }}</template>
                                            <span>{{ formatClipPointNum(scope.row.credit_qut) }}</span>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </template>
                        </AccountBooksTable>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "Journal",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { componentFinishKey } from "@/symbols";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISearchParams, ITableData, IJournalTableData } from "./types";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import { ElNotify } from "@/util/notify";
import { formatMoney, formatQuantity, formatClipPointNum, showSpanPopover } from "@/util/format";
import { getUrlSearchParams, globalExport, globalWindowOpenPage } from "@/util/url";
import { strInArray } from "./utils";
import { ref, reactive, provide, watch, onMounted, watchEffect, computed } from "vue";
import { useAccountSetStore } from "@/store/modules/accountset";
import type { VoucherGroupModel } from "@/components/Voucher/types";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { isLemonClient } from "@/util/lmClient";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { checkPermission } from "@/util/permission";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useRoute } from "vue-router";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";

const setModule = "AccountBooksJournal";
const periodStore = useAccountPeriodStore();
const accountSet = useAccountSetStore().accountSet;
const slots = ["main"];
const currentSlot = ref("main");
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const currentPeriodInfo = ref("");
const periodInfo = ref("");
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const loading = ref<boolean>(false);
const route = useRoute();

const searchInfo = reactive({
    startPid: Number(periodStore.getPeriodRange().start),
    endPid: Number(periodStore.getPeriodRange().end),
    voucher: 1000,
    prepareBy: "全部",
    description: "",
    approvalStatus: 0,
    subjectCode: "",
    startMoney: "",
    endMoney: "",
    startVoucher: "",
    endVoucher: "",
    sortColumn: "V_DATE",
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, searchInfo.startPid, searchInfo.endPid); 
const getActPid = (start: number, end: number) => {
    searchInfo.startPid = start;
    searchInfo.endPid = end;
}

const voucherGroupList= computed(() => useVoucherGroupStore().voucherGroupList);

const prepareOption = ref<string[]>(["全部"]);
const approvalStatusOption = [
    { value: 0, label: "全部" },
    { value: 2, label: "已审核" },
    { value: 1, label: "未审核" },
];

function setParams(params: ISearchParams) {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始期间不能大于结束期间哦",
            type: "warning",
        });
        return false;
    }
    if (Number(searchInfo.startMoney) > Number(searchInfo.endMoney) && searchInfo.startMoney != "" && searchInfo.endMoney != "") {
        ElNotify({
            message: "亲，开始金额不能大于结束金额哦",
            type: "warning",
        });
        return false;
    }
    if (Number(searchInfo.startVoucher) > Number(searchInfo.endVoucher) && searchInfo.endVoucher != "" && searchInfo.startVoucher != "") {
        ElNotify({
            message: "亲，开始凭证号不能大于结束凭证号哦",
            type: "warning",
        });
        return false;
    }
    params.prepareBy = searchInfo.prepareBy == "全部" ? "" : searchInfo.prepareBy;
    params.VG_ID = searchInfo.voucher === 1000 ? "" : searchInfo.voucher;
    params.description = searchInfo.description;
    params.startP = searchInfo.startPid;
    params.endP = searchInfo.endPid;
    params.startMoney = searchInfo.startMoney;
    params.endMoney = searchInfo.endMoney;
    params.startVoucherNo = searchInfo.startVoucher;
    params.endVoucherNo = searchInfo.endVoucher;
    params.approvalStatus = searchInfo.approvalStatus;
    params.sortColumn = searchInfo.sortColumn;
    params.startPText = periodInfo.value.split("—")[0];
    params.endPText = periodInfo.value.split("—")[1];
    return true;
}

const isErp = ref(window.isErp);
const ShowDetail = (row: ITableData) => {
    const from = "journal";
    globalWindowOpenPage(`/Voucher/VoucherPage?pid=${row.p_id}&vid=${row.v_id}&from=${from}`, "查看凭证");
};

const searchParams: ISearchParams = {
    description: "",
    startMoney: "",
    endMoney: "",
    startP: 26,
    startPText: "2022年4月",
    endP: 26,
    endPText: "2022年8月",
    startVoucherNo: "",
    endVoucherNo: "",
    VG_ID: "",
    prepareBy: "",
    noApproved: 0,
    approvalStatus: 1,
    sortColumn: "V_DATE",
    subjectCode: "",
};

function handleSearch() {
    if (searchInfo.startPid > searchInfo.endPid) {
        ElNotify({
            message: "亲，开始日期不能大于结束日期哦",
            type: "warning",
        });
        return false;
    }

    if (setParams(searchParams)) {
        periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
        currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
        paginationData.currentPage = 1;
        getTableData();
        handleClose();
    }
}

function handleClose() {
    containerRef.value?.handleClose();
}

function handleReset() {
    searchInfo.voucher = 1000;
    searchInfo.prepareBy = "全部";
    searchInfo.description = "";
    searchInfo.approvalStatus = 0;
    searchInfo.subjectCode = "";
    searchInfo.startMoney = "";
    searchInfo.endMoney = "";
    searchInfo.startVoucher = "";
    searchInfo.endVoucher = "";
    searchInfo.sortColumn = "V_DATE";
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
}

function handleExport() {
    const params = {
        periodStart: searchInfo.startPid,
        periodEnd: searchInfo.endPid,
        vgid: searchInfo.voucher === 1000 ? "" : searchInfo.voucher,
        prepareBy: searchInfo.prepareBy == "全部" ? "" : searchInfo.prepareBy,
        description: searchInfo.description,
        subjectCode: searchInfo.subjectCode,
        moneyStart: searchInfo.startMoney,
        moneyEnd: searchInfo.endMoney,
        voucherNoStart: searchInfo.startVoucher,
        voucherNoEnd: searchInfo.endVoucher,
        sortColumn: searchInfo.sortColumn,
        approvalStatus: searchInfo.approvalStatus,
        appasid: getGlobalToken(),
        async: !isLemonClient(),
    };
    globalExport(window.jAccountBooksHost + "/api/AccountBooksJournal/Export?" + getUrlSearchParams(params));
}

const columns = ref<Array<IColumnProps>>([]);
function setColumns(data: Array<string>) {
    let columnsMid: Array<Array<IColumnProps>> = [[]];
    let columnsChecked: Array<IColumnProps> = [
        { label: "客户编码", prop: "customer_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'customer_code') },
        { label: "客户名称", prop: "customer_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'customer_name') },
        { label: "供应商编码", prop: "vender_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'vender_code') },
        { label: "供应商名称", prop: "vender_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'vender_name') },
        { label: "职员编码", prop: "employee_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'employee_code') },
        { label: "职员名称", prop: "employee_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'employee_name') },
        { label: "部门编码", prop: "depart_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'depart_code') },
        { label: "部门名称", prop: "depart_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'depart_name') },
        { label: "项目编码", prop: "project_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'project_code') },
        { label: "项目名称", prop: "project_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'project_name') },
    ];
    if (window.isErp) {
        columnsChecked.push({ label: "商品编码", prop: "stock_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'stock_code') });
        columnsChecked.push({ label: "商品名称", prop: "stock_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'stock_name') });
    } else {
        columnsChecked.push({ label: "存货编码", prop: "stock_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'stock_code') });
        columnsChecked.push({ label: "存货名称", prop: "stock_name", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'stock_name') });
    }
    columnsMid[0] = columnsMid[0].concat([
        { label: "日期", prop: "v_date", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'v_date') },
        { slot: "vgname" },
        {
            label: "摘要",
            prop: "description",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            width: getColumnWidth(setModule, 'description')
        },
        {
            label: "科目编码",
            prop: "asub_code",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            width: getColumnWidth(setModule, 'asub_code')
        },
        {
            label: "科目名称",
            prop: "asub_name",
            align: "left",
            headerAlign: "left",
            minWidth: 120,
            width: getColumnWidth(setModule, 'asub_name')
        },
    ]);
    if (strInArray("fc_code", data) != -1 && (strInArray("debit_qut", data) != -1 || strInArray("credit_qut", data) != -1)) {
        columnsMid[0] = columnsMid[0].concat([
            { label: "外币代码", prop: "fc_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'fc_code') },
            {
                slot: "debit_qut",
            },
            {
                label: "借方外币",
                prop: "debit_amount",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
                width: getColumnWidth(setModule, 'debit_amount')
            },
            {
                label: "借方本币",
                prop: "debit",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                formatter: (row, column, value) => {
                    return formatMoney(value);
                }, 
                width: getColumnWidth(setModule, 'debit')
            },
            {
                slot: "credit_qut",
            },
            {
                label: "贷方外币",
                prop: "credit_amount",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
                width: getColumnWidth(setModule, 'credit_amount')
            },
            {
                label: "贷方本币",
                prop: "credit",
                align: "right",
                headerAlign: "right",
                minWidth: 120,
                formatter: (row, column, value) => {
                    return formatMoney(value);
                },
                width: getColumnWidth(setModule, 'credit')
            },
        ]);
    } else {
        if (strInArray("fc_code", data) != -1) {
            columnsMid[0] = columnsMid[0].concat([
                { label: "外币代码", prop: "fc_code", align: "left", headerAlign: "left", minWidth: 120, width: getColumnWidth(setModule, 'fc_code') },
                {
                    label: "借方外币",
                    prop: "debit_amount",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit_amount')
                },
                {
                    label: "借方本币",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit')
                },
                {
                    label: "贷方外币",
                    prop: "credit_amount",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit_amount')
                },
                {
                    label: "贷方本币",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit')
                },
            ]);
        } else if (strInArray("debit_qut", data) != -1 || strInArray("credit_qut", data) != -1) {
            columnsMid[0] = columnsMid[0].concat([
                {
                    slot: "debit_qut",
                },
                {
                    label: "借方金额",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit')
                },
                {
                    slot: "credit_qut",
                },
                {
                    label: "贷方金额",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit')
                },
            ]);
        } else {
            columnsMid[0] = columnsMid[0].concat([
                {
                    label: "借方金额",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'debit')
                },
                {
                    label: "贷方金额",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    minWidth: 120,
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, 'credit')
                },
            ]);
        }
    }
    for (let i = 0; i < columnsChecked.length; i++) {
        if (strInArray(columnsChecked[i].prop as string, data) != -1) {
            columnsMid[0].push(columnsChecked[i]);
        }
    }
    for (let i = 0; i < data.length; i++) {
        let hasColumn = false;
        for (let j = 0; j < columnsChecked.length; j++) {
            if (columnsChecked[j].prop == data[i]) {
                hasColumn = true;
                break;
            }
        }
        if (!hasColumn) {
            let index = data[i].indexOf(".");
            if (index == -1) {
                continue;
            }
            let label = data[i].substring(index + 1);
            let prop = data[i].substring(0, index).toLocaleLowerCase();
            if (prop === "10007_name") {
                columnsMid[0] = columnsMid[0].concat([
                    {
                        label: label,
                        prop: prop,
                        align: "left",
                        headerAlign: "left",
                        minWidth: 120,
                        useHtml: true,
                        formatter: (row) => {
                            return row[prop]
                                ? `<div title='${row[prop]}' style="text-overflow: ellipsis;white-space: nowrap;overflow: hidden;">${row[prop]}</div>`
                                : "";
                        },
                        width: getColumnWidth(setModule, prop, 120)
                    },
                ]);
            } else {
                columnsMid[0] = columnsMid[0].concat([{ 
                    label: label, 
                    prop: prop, 
                    align: "left", 
                    headerAlign: "left", 
                    minWidth: 120,
                    width: getColumnWidth(setModule, prop)
                }]);
            }
        }
    }
    columns.value = columnsMid[0].concat([
        { label: "制单人", prop: "prepared_by", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "prepared_by") },
        { label: "审核人", prop: "approved_by", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, "approved_by") },
        { label: "附件数", prop: "attachments", align: "right", headerAlign: "right", minWidth: 60, width: getColumnWidth(setModule, "attachments") },
        { label: "备注", prop: "note", align: "left", headerAlign: "left", minWidth: 120, resizable: false },
    ]);
}

function getPrepareBy() {
    return request({
        url: "/api/Voucher/GetVoucherPreparedBys",
        method: "post",
    });
}
getPrepareBy();

const tableData = ref<any[]>([]);
const emptyText = ref(" ");
const getTableData = () => {
    const data = {
        periodStart: searchInfo.startPid,
        periodEnd: searchInfo.endPid,
        vgid: searchInfo.voucher === 1000 ? "" : searchInfo.voucher,
        prepareBy: searchInfo.prepareBy == "全部" ? "" : searchInfo.prepareBy,
        description: searchInfo.description,
        subjectCode: searchInfo.subjectCode,
        moneyStart: searchInfo.startMoney,
        moneyEnd: searchInfo.endMoney,
        voucherNoStart: searchInfo.startVoucher,
        voucherNoEnd: searchInfo.endVoucher,
        sortColumn: searchInfo.sortColumn,
        approvalStatus: searchInfo.approvalStatus,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    loading.value = true;
    request({
        url: `/api/AccountBooksJournal?` + getUrlSearchParams(data),
    })
        .then((res: IResponseModel<IJournalTableData>) => {
            currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPid, searchInfo.endPid);
            setColumns(res.data.columns);
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            if (res.data.rows.length === 0) {
                emptyText.value = "暂无数据";
            }
            loading.value = false;
        })
        .finally(() => {});
};

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        Promise.all([getPrepareBy()]).then((res: IResponseModel<string[]>[]) => {
            prepareOption.value = prepareOption.value.concat(res[0].data);
            getTableData();
        });
    }
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], getTableData);

onMounted(() => {
    // periodStore.changePeriods(String(searchInfo.startPid), String(searchInfo.endPid));
    searchInfo.startPid = Number(periodStore.getPeriodRange().start);
    searchInfo.endPid = Number(periodStore.getPeriodRange().end);
    document.body.scrollTop = 0;
});

const voucherGroupListAll = ref<Array<VoucherGroupModel>>([]);
const showVoucherGroupList = ref<Array<VoucherGroupModel>>([]);
const showPrepareOption = ref<Array<string>>([]);
watchEffect(() => { 
    voucherGroupListAll.value = JSON.parse(JSON.stringify(voucherGroupList.value));  
    voucherGroupListAll.value.unshift({
        id: 1000,
        name: "全部",
        title: "全部",
        isDefault: false,
    });
    showVoucherGroupList.value = JSON.parse(JSON.stringify(voucherGroupListAll.value));  
});
watchEffect(() => {  
    showPrepareOption.value = JSON.parse(JSON.stringify(prepareOption.value));  
});
function prepareFilterMethod(value: string) {
    showPrepareOption.value = commonFilterMethod(value, prepareOption.value, '');
}
function voucherGroupFilterMethod(value: string) {
    showVoucherGroupList.value = commonFilterMethod(value, voucherGroupListAll.value, 'title');
}
</script>

<style scoped lang="less">
@import "@/style/AccountBooks/AccountBooks.less";
@import "@/style/SelfAdaption.less";

.content {
    .main-center {
        .link {
            outline: none;
            cursor: pointer;
            text-decoration: none;
            color: var(--link-color);
            &:hover {
                text-decoration: underline;
            }
        }
        :deep(.el-popper) {
            max-width: 300px;
            text-align: left;
        }
    }
}
:deep(.el-table__cell) {
    .cell.el-tooltip {
        min-width: 0px !important;
    }
}
:deep(.el-table .el-table__header .cell) {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: clip;
}
:deep(.custom-table tbody tr td .cell) {
    // overflow: hidden;
    white-space: nowrap;
    // text-overflow:ellipsis;
    & span {
        display: block;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
.downlist {
    .el-select-dropdown__list {
        max-height: 200px;
        // overflow-y: auto;
    }
    .el-select-dropdown__item {
        width: 100%;
        height: auto;
        font-size: var(--el-font-size-base);
        padding: 6px 6px 6px 8px;
        line-height: 16px;
        position: relative;
        word-wrap: break-word;
        white-space: normal;
        color: var(--el-text-color-regular);
        box-sizing: border-box;
        cursor: pointer;
        text-align: left;
        & span {
            display: -webkit-box;
            -webkit-line-clamp: 2; /* 设置最多显示2行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    }
}
.line-item-field.line-item-select {
    .detail-el-select(132px);
}
body[erp] {
    .main-center {
        :deep(.el-table) {
            border-radius: 4px 4px 0 0;
            overflow: hidden;
            .el-table__header-wrapper {
                border-radius: 4px 4px 0 0;
            }
        }
    }
}
</style>
