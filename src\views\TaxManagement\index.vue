<template>
  <div class="content">
    <div class="main-content">
      <div class="main-content-header">
        <div class="left-content">
          <el-card>
            <el-icon class="waring-filled"><WarningFilled /></el-icon>
            <span>
              温馨提示:1、若税费种认定信息或财务会计制度备案信息有变更,可以点击【更新税费种认定】按钮进行更新;2、若更新以后,税费种认定信息显示不全，可以手动新增！
            </span>

            <img
              src="@/assets/Icons/question.png"
              @click="showNotification(TAX_DETERMINATION_INFO)" />
          </el-card>
        </div>
        <div class="right-content">
          <el-popover
            placement="bottom"
            width="300">
            <template #reference>
              <a
                class="button solid-button"
                style="width: 120px"
                @click="onUpdateTaxClick">
                更新税费种认定
              </a>
            </template>
            <div class="popover-content">
              <span class="time">{{ latestUpdateTime }} 更新</span>
              <span
                class="link"
                @click="showUpdateHistory">
                查看记录
              </span>
            </div>
          </el-popover>
          <!-- <el-button ></el-button>
          <el-button>删除</el-button> -->
          <a
            class="button ml-10"
            @click="openTaxModal">
            新增
          </a>
          <a
            class="button ml-10"
            @click="onDelete">
            删除
          </a>
        </div>
      </div>

      <div class="main-content-body">
        <div
          class="table-container"
          ref="tableContainerRef">
          <LMTable
            :row-key="(row: TaxTypeItem) => row.index"
            :columns="columns"
            highlight-current-row
            @cell-click="handleRowClick"
            ref="tableRef"
            :height="tableHeight"
            :data="tableData">
            <template #projectName>
              <el-table-column
                prop="projectName"
                label="征收项目"
                width="180">
                <template #default="{ row }">
                  <!-- 点击选中该行时可编辑 -->
                  <div>
                    <span :class="{ 'indented-row': isAdditionalTax(row) }">{{ row.projectName }}</span>
                    <el-popover
                      placement="right"
                      trigger="hover"
                      width="400"
                      @show="getCategoryData(row)"
                      v-if="row.categoryCount > 0">
                      <template #reference>
                        <span class="catogory">
                          征收品目
                          <el-icon class="el-icon-warning-filled">
                            <WarningFilled />
                          </el-icon>
                        </span>
                      </template>
                      <div class="category-table">
                        <LMTable
                          row-key="categoryName"
                          :columns="categoryColumns"
                          :loading="categoryTableLoading"
                          :data="categoryData"
                          style="width: 100%"></LMTable>
                      </div>
                    </el-popover>
                  </div>
                </template>
              </el-table-column>
            </template>
            <template #declarationType>
              <el-table-column
                prop="declarationType"
                label="申报种类"
                width="250">
                <template #default="{ row }">
                  <!-- 点击选中该行时可编辑 -->
                  <el-select
                    v-if="
                      row.projectCode === editingRow?.projectCode &&
                      row.taxPeriod === editingRow?.taxPeriod &&
                      (row.source !== EnumSource.税局获取 || row.projectName == '企业会计准则')
                    "
                    v-model="row.declarationType"
                    @change="onDeclarationTypeChange(row)"
                    ref="selectRef"
                    size="default"
                    @focus="handleSelectFocus('declarationType')"
                    @blur="handleSelectBlur('declarationType')"
                    placeholder="请输入申报种类">
                    <el-option
                      v-for="item in declarationTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </el-select>
                  <span v-else>{{ row.declarationTypeName }}</span>
                </template>
              </el-table-column>
            </template>

            <template #taxPeriod>
              <el-table-column
                prop="taxPeriod"
                label="申报期限">
                <template #default="{ row }">
                  <!-- 点击选中该行时可编辑 -->

                  <el-select
                    v-if="row.projectCode === editingRow?.projectCode && row.source !== EnumSource.税局获取"
                    v-model="row.taxPeriod"
                    @change="onTaxPeriodChange(row)"
                    @focus="handleSelectFocus('taxPeriod')"
                    @blur="handleSelectBlur('taxPeriod')"
                    placeholder="请输入申报期限">
                    <el-option
                      v-for="item in taxPeriodOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </el-select>
                  <span v-else>{{ row.taxPeriodName }}</span>
                </template>
              </el-table-column>
            </template>

            <template #option>
              <el-table-column
                prop="option"
                label="申报选项">
                <template #default="{ row }">
                  <!-- 点击选中该行时可编辑 -->
                  <el-select
                    v-if="row.projectCode === editingRow?.projectCode && row.source !== EnumSource.税局获取"
                    v-model="row.option"
                    @change="onOptionChange(row)"
                    size="default"
                    @focus="handleSelectFocus('option')"
                    @blur="handleSelectBlur('option')"
                    placeholder="请输入申报选项">
                    <el-option
                      v-for="item in DECLARATION_OPTIONS"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value" />
                  </el-select>
                  <span v-else>{{ row.optionName }}</span>
                </template>
              </el-table-column>
            </template>

            <template #confirmStartDate>
              <el-table-column
                prop="confirmStartDate"
                label="确认开始日期"
                width="240">
                <template #default="{ row }">
                  <!-- 点击选中该行时可编辑 -->
                  <el-date-picker
                    v-if="row.projectCode === editingRow?.projectCode && row.source !== EnumSource.税局获取"
                    v-model="row.confirmStartDate"
                    type="date"
                    size="default"
                    :format="'YYYY-MM-DD'"
                    :value-format="'YYYY-MM-DD'"
                    @focus="handleSelectFocus('confirmStartDate')"
                    @blur="handleSelectBlur('confirmStartDate')"
                    placeholder="请输入确认开始日期" />
                  <span v-else>{{ row.confirmStartDate }}</span>
                </template>
              </el-table-column>
            </template>

            <template #confirmEndDate>
              <el-table-column
                prop="confirmEndDate"
                label="确认结束日期"
                width="240">
                <template #default="{ row }">
                  <!-- 点击选中该行时可编辑 -->
                  <el-date-picker
                    v-if="row.projectCode === editingRow?.projectCode && row.source !== EnumSource.税局获取"
                    v-model="row.confirmEndDate"
                    type="date"
                    size="default"
                    :format="'YYYY-MM-DD'"
                    :value-format="'YYYY-MM-DD'"
                    @focus="handleSelectFocus('confirmEndDate')"
                    @blur="handleSelectBlur('confirmEndDate')"
                    placeholder="请输入确认结束日期" />
                  <span v-else>{{ row.confirmEndDate }}</span>
                </template>
              </el-table-column>
            </template>
          </LMTable>
        </div>

        <!-- 内容底部 -->
        <div class="footer-tips">
          <el-card v-show="isShowTips">
            <el-icon class="waring-filled"><WarningFilled /></el-icon>
            <span>
              温馨提示：无财务报表报送信息，建议前往税局进行

              <a
                @click="goToChinaTax"
                class="link">
                财务会计制度及核算软件备案报告
              </a>

              。仅符合条件的主体可不在电子税务局填报财务报表，点击查看
              <span
                class="link"
                @click="showNotification(ELIGIBLE_ENTITIES_INFO)">
                符合条件的主体
              </span>
            </span>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 新增模态框 -->
    <el-dialog
      v-model="addModal"
      title="新增税费种认定"
      center
      modal-class="modal-class"
      class="dialogDrag"
      width="800">
      <div v-dialog-drag>
        <el-form
          :model="form"
          ref="formRef"
          :rules="FORM_RULES"
          label-width="auto">
          <div class="form-row">
            <el-form-item
              label="征收项目"
              prop="projectCode">
              <el-select
                v-model="form.projectCode"
                placeholder="请选择征收项目"
                filterable
                clearable>
                <el-option
                  v-for="item in taxTypeOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item
              label="申报种类"
              prop="declarationType">
              <el-select
                v-model="form.declarationType"
                placeholder="请选择申报种类"
                :disabled="form.projectCode === null">
                <el-option
                  v-for="item in declarationTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item
              label="纳税期限"
              prop="taxPeriod">
              <el-select
                v-model="form.taxPeriod"
                placeholder="请选择纳税期限"
                :disabled="form.projectCode === null">
                <el-option
                  v-for="item in taxPeriodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item
              label="申报选项"
              prop="option">
              <el-select
                v-model="form.option"
                placeholder="请选择申报选项"
                :disabled="form.projectCode === null">
                <el-option
                  v-for="item in DECLARATION_OPTIONS"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </div>

          <div class="form-row">
            <el-form-item
              label="认定有效期起"
              prop="confirmStartDate">
              <el-date-picker
                v-model="form.confirmStartDate"
                type="date"
                :format="'YYYY-MM-DD'"
                :value-format="'YYYY-MM-DD'"
                placeholder="选择日期" />
            </el-form-item>
            <el-form-item
              label="认定有效期止"
              prop="confirmEndDate">
              <el-date-picker
                v-model="form.confirmEndDate"
                type="date"
                :format="'YYYY-MM-DD'"
                :value-format="'YYYY-MM-DD'"
                placeholder="选择日期" />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <el-button
          type="primary"
          @click="onSubmit">
          确认
        </el-button>
        <el-button @click="closeTaxModal">取消</el-button>
      </template>
    </el-dialog>

    <!-- 更新记录模态框 -->
    <UpdateHistoryDialog v-model:visible="updateHistoryVisible" />
  </div>
</template>

<script setup lang="ts">
  import {
    addTaxtype,
    deleteTaxtypeMany,
    editTaxtype,
    getCategorys,
    getCustomList,
    getLatestUpdateTime,
    getTaxList,
    updateTaxtype,
    checkUpdateStatus,
    LatestParamsType,
  } from "@/api/taxManagement"
  import { WarningFilled } from "@element-plus/icons-vue"
  import { type FormInstance } from "element-plus"
  import {
    EnumSource,
    getTaxPeriodOptions,
    TAX_DETERMINATION_INFO,
    ELIGIBLE_ENTITIES_INFO,
    FORM_RULES,
    DECLARATION_OPTIONS,
    EnumUpdateStatus,
    ACCOUNTING_STANDARD,
    taxPeriodDict,
    EnumTaxPeriod,
  } from "./constants"
  import { ElNotify } from "@/utils/notify"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { reorderTaxList } from "./utils"
  import { ElConfirm } from "@/utils/confirm"
  import UpdateHistoryDialog from "./components/UpdateHistoryDialog.vue"
  import { CategoryItem, FocusState, TaxTypeItem, ITaxTypeEditParams } from "./types"
  import { isEqual } from "lodash-es"
  import { useResizeObserver } from "@vueuse/core"
  import { TaxBureauLoginType, useTaxBureau } from "@/hooks/useTaxBureau"

  defineOptions({
    name: "TaxManagement",
  })

  const categoryTableLoading = ref(false)
  const taxInfoRefreshing = ref(false)
  const updateHistoryVisible = ref(false)
  const latestUpdateTime = ref("")
  const addModal = ref(false)
  const formRef = ref<FormInstance>()
  const selectRef = ref()
  const tableRef = ref()

  // 税种数据
  let taxTypeOptions = [] as any[]

  const form = ref({
    projectCode: null,
    projectName: "",
    declarationType: null,
    declarationTypeName: "",
    taxPeriod: null,
    taxPeriodName: "",
    option: 0,
    optionName: "",
    confirmStartDate: "",
    confirmEndDate: "",
  })
  const declarationTypeOptions = ref<Array<{ label: string; value: number }>>([]) // 申报种类选项
  const taxPeriodOptions = ref<Array<{ label: string; value: number }>>([]) // 纳税期限选项

  // 根据征收项目使得匹配对应的申报种类选项
  const matchOptions = (projectCode: string, projectName?: string) => {
    if (projectName == "企业会计准则") {
      declarationTypeOptions.value = ACCOUNTING_STANDARD
      return
    }
    const selectedTax = taxTypeOptions.find((item) => item.code === projectCode)
    if (selectedTax) {
      // 更新申报种类选项
      declarationTypeOptions.value = selectedTax.declarationType.map((item: string, index: number) => ({
        label: item,
        value: index,
      }))
    }
  }

  // 监听税种选择变化
  watch(
    () => form.value.projectCode,
    (newVal) => {
      if (newVal) {
        matchOptions(newVal)
        // 重置申报种类、纳税期限和申报选项
        form.value.declarationType = null
        form.value.declarationTypeName = ""
        form.value.taxPeriod = null
        form.value.taxPeriodName = ""
        form.value.option = 0
      } else {
        // 重置申报种类和纳税期限选项
        declarationTypeOptions.value = []
        taxPeriodOptions.value = []
        form.value.declarationType = null
        form.value.taxPeriod = null
      }
    },
  )

  // 监听申报种类变化
  watch(
    () => form.value.declarationType,
    (newVal) => {
      if (newVal !== null) {
        form.value.declarationTypeName = declarationTypeOptions.value[newVal].label

        // 根据申报种类的名称获取纳税期限选项
        taxPeriodOptions.value = getTaxPeriodOptions(form.value.declarationTypeName, taxPeriodDict)
      } else {
        form.value.declarationTypeName = ""
      }
    },
  )

  const showNotification = (info: any) => {
    ElConfirm({
      title: "提示",
      message: info,
      showCancel: false,
      buttons: {
        confirm: "知道了",
      },
    })
  }

  const tableData = ref<TaxTypeItem[]>([])

  const categoryData = ref<CategoryItem[]>([])

  const columns = [
    { type: "selection", width: 50, selectable: (row: TaxTypeItem) => row.source !== EnumSource.税局获取 },
    { slot: "projectName" },
    { slot: "declarationType" },
    {
      slot: "taxPeriod",
    },
    {
      slot: "option",
    },
    {
      slot: "confirmStartDate",
    },
    {
      slot: "confirmEndDate",
    },
    {
      label: "数据来源",
      prop: "sourceName",
      width: 200,
    },
  ]
  const categoryColumns = [
    { label: "征收品目", prop: "projectName" },
    { label: "认定有效期起", prop: "confirmStartDate" },
    { label: "认定有效期止", prop: "confirmEndDate" },
  ]

  const editingRow = ref<TaxTypeItem | null>()
  const beforeEditingRow = ref<TaxTypeItem | null>()
  const handleRowClick = async (row: TaxTypeItem, column: any, cell: any, event: any) => {
    if (focusLock) return

    if (editingRow.value?.projectCode && editingRow.value.projectCode == row.projectCode) return

    if (row.source === EnumSource.税局获取 && row.projectName !== "企业会计准则") return

    if (row.existDeclareRecord) {
      const res = await ElConfirm({
        title: "提示",
        message: "存在已填写的申报表，若修改税费种，将删除已填写的申报表，确认继续吗？",
        showCancel: true,
      })
      if (!res) return
    }

    editingRow.value = row
    beforeEditingRow.value = JSON.parse(JSON.stringify(row))
    matchOptions(row.projectCode, row.projectName)
    taxPeriodOptions.value = getTaxPeriodOptions(editingRow.value.declarationTypeName || "", taxPeriodDict)

    nextTick(() => {
      selectRef.value?.focus()
    })
  }

  // 跟踪每个 select 的焦点状态
  const selectFocusState = ref<FocusState>({
    projectCode: false,
    declarationType: false,
    taxPeriod: false,
    option: false,
    confirmStartDate: false,
    confirmEndDate: false,
  })

  // 检查是否所有 select 都失去了焦点
  const isAllSelectsBlurred = computed(() => {
    return Object.values(selectFocusState.value).every((focused) => !focused)
  })

  // 处理单个 select 的焦点获得
  const handleSelectFocus = (fieldName: keyof FocusState) => {
    selectFocusState.value[fieldName] = true
  }

  // 处理单个 select 的焦点失去
  let focusLock = false
  const handleSelectBlur = (fieldName: keyof FocusState) => {
    focusLock = true
    selectFocusState.value[fieldName] = false
    // 使用 setTimeout 确保在所有焦点事件处理完后再检查 ？？
    setTimeout(() => {
      if (isAllSelectsBlurred.value) {
        editTaxtypeRequest()
      }
      focusLock = false
    }, 250)
  }

  const openTaxModal = () => {
    addModal.value = true
  }

  const closeTaxModal = () => {
    addModal.value = false
  }

  const showUpdateHistory = () => {
    updateHistoryVisible.value = true
  }

  const onSubmit = () => {
    if (!formRef.value) return
    formRef.value.validate((valid: boolean) => {
      if (valid) {
        // 需要将code对应的name也进行传递
        form.value.projectName = taxTypeOptions.find((item) => item.code === form.value.projectCode)?.name || ""
        form.value.declarationTypeName = declarationTypeOptions.value.find((item) => item.value === form.value.declarationType)?.label || ""
        form.value.taxPeriodName = taxPeriodOptions.value.find((item) => item.value === form.value.taxPeriod)?.label || ""
        form.value.optionName = DECLARATION_OPTIONS.find((item) => item.value === form.value.option)?.label || ""
        addTaxtype(form.value).then((res: any) => {
          if (res.data) {
            ElNotify({
              type: "success",
              message: "新增成功",
            })
            getTaxListRequest()
            getLatestUpdateTimeRequest()
            addModal.value = false
          } else {
            ElNotify({
              type: "warning",
              message: res.msg,
            })
          }
        })
      } else {
        ElNotify({
          type: "warning",
          message: "请填写完整信息",
        })
      }
    })
  }

  const onDelete = () => {
    const { TableComponents } = tableRef.value
    const selectedRows = TableComponents.getSelectionRows()
    if (selectedRows.length === 0) {
      ElNotify({ type: "warning", message: "请先选择要删除的行" })
      return
    }

    // 删除选中的行
    const projectCodes = selectedRows.map((row: TaxTypeItem) => row.projectCode).join(",")
    deleteTaxtypeMany({ codes: projectCodes }).then((res) => {
      if (res.data) {
        ElNotify({
          type: "success",
          message: "删除成功",
        })
        getTaxListRequest()
        getLatestUpdateTimeRequest()
      }
    })
  }

  // 获取征收品目数据
  const getCategoryData = (row: any) => {
    // 这里应该根据实际情况返回征收品目数据
    // 如果数据已经在row中，直接返回
    categoryTableLoading.value = true
    getCategorys(row.projectCode).then((res) => {
      categoryData.value = res.data
      categoryTableLoading.value = false
    })
  }

  // 首先获取上次更新时间
  // 加载表格数据
  // 每次更新都需要做这两步

  const getTaxListRequest = () => {
    return getTaxList().then((res) => {
      if (res.data) {
        // 对数据进行重排序
        tableData.value = reorderTaxList(res.data).map((item, index) => ({
          ...item,
          index: index + 1,
        }))
      } else {
        ElNotify({
          type: "warning",
          message: res.msg,
        })
      }
    })
  }

  const getLatestUpdateTimeRequest = () => {
    // 这里应该根据实际情况返回上次更新时间
    return getLatestUpdateTime({ type: LatestParamsType.获取税费种信息 }).then((res) => {
      if (res.data || res.state == 1000) {
        latestUpdateTime.value = res.data
      } else {
        ElNotify({
          type: "warning",
          message: res.msg,
        })
      }
    })
  }

  // 更新税费种认定信息
  const { basicInfo } = storeToRefs(useBasicInfoStore())
  let taskId = ""
  const onUpdateTaxClick = () => {
    // toast显示
    if (taxInfoRefreshing.value) {
      return ElNotify({
        type: "warning",
        message: "亲，正在更新税费种认定，不用重复提交哦~",
      })
    }
    let params = {
      creditCode: basicInfo.value.taxNumberS,
      areaCode: basicInfo.value.taxAreaId,
      personAccount: basicInfo.value.taxBureauPersonAccount,
    }

    // 开启更新任务
    updateTaxtype(params).then((res) => {
      if (!res.data) {
        ElNotify({
          type: "warning",
          title: "更新失败",
          message: res.msg,
        })
        return
      }
      taskId = res.data
      taxInfoRefreshing.value = true
      ElNotify({
        type: "success",
        message: "正在更新税费种认定信息……",
      })
      // 开启轮训
      checkUpdateResult()
    })
  }

  // 轮训检查更新结果
  const checkUpdateResult = () => {
    if (taxInfoRefreshing.value) {
      checkUpdateStatus({ taskId: taskId }).then((res) => {
        switch (res.data) {
          case EnumUpdateStatus.成功:
            ElNotify({
              type: "success",
              message: "更新成功",
            })
            taxInfoRefreshing.value = false
            getTaxListRequest()
            getLatestUpdateTimeRequest()
            break
          case EnumUpdateStatus.成功有变动:
            ElNotify({
              type: "success",
              message: "更新成功，有变动",
            })
            showUpdateHistory()
            taxInfoRefreshing.value = false
            getTaxListRequest()
            getLatestUpdateTimeRequest()
            break
          case EnumUpdateStatus.成功没变动:
            ElNotify({
              type: "success",
              message: "更新成功，没有变动",
            })
            taxInfoRefreshing.value = false
            getTaxListRequest()
            getLatestUpdateTimeRequest()
            break
          case EnumUpdateStatus.正在处理中:
            setTimeout(() => {
              checkUpdateResult()
            }, 1000)
            break
          case EnumUpdateStatus.失败:
            ElNotify({
              type: "warning",
              message: "更新失败",
            })
            taxInfoRefreshing.value = false
            break
        }
      })
    }
  }

  // 编辑请求
  const editTaxtypeRequest = async () => {
    // 如果编辑前和编辑后没有变化，不发送请求
    if (editingRow.value === null || isEqual(editingRow.value, beforeEditingRow.value)) {
      editingRow.value = null
      return
    }

    // const checkResult = await checkTaxtypeCode({ code: editingRow.value?.projectCode, period: editingRow.value?.taxPeriod })

    // if (checkResult.data > 0) {
    //   const res = await ElConfirm({
    //     title: "提示",
    //     message: "存在已填写的申报表，若修改税费种，将删除已填写的申报表，确认继续吗？",
    //     showCancel: true,
    //   })
    //   if (!res) {
    //     // 恢复原始数据
    //     if (editingRow.value && beforeEditingRow.value) {
    //       Object.assign(editingRow.value, beforeEditingRow.value)
    //     }
    //     editingRow.value = null
    //     return
    //   }
    // }

    if (editingRow.value?.projectName.includes("企业会计准则")) {
      // 如果是月/季
      if (editingRow.value.taxPeriod == EnumTaxPeriod.月 || editingRow.value.taxPeriod == EnumTaxPeriod.季) {
        const subTaxAccountingStandard = useBasicInfoStore().basicInfo.subTaxAccountingStandard

        if (editingRow.value.declarationType !== subTaxAccountingStandard) {
          const res = await ElConfirm({
            title: "提示",
            message: `检测到上期财务报表申报为${subTaxAccountingStandard ? "已执行" : "未执行"}，
            若选择${!subTaxAccountingStandard ? "已执行" : "未执行"}，本期申报将无期初数据，需手动填写，确认修改吗？`,
            showCancel: true,
          })
          if (!res) {
            // 恢复原始数据
            if (editingRow.value && beforeEditingRow.value) {
              Object.assign(editingRow.value, beforeEditingRow.value)
            }
            editingRow.value = null
            return
          }
        }
      }

      // 如果是年
      if (editingRow.value.taxPeriod == EnumTaxPeriod.年) {
        // 先将企业会计准则的月报的申报种类获取到

        const monthDeclarationType = tableData.value.find(
          (item: TaxTypeItem) =>
            item.projectName === "企业会计准则" && (item.taxPeriod == EnumTaxPeriod.月 || item.taxPeriod == EnumTaxPeriod.季),
        )?.declarationType

        if (editingRow.value.declarationType !== monthDeclarationType) {
          const res = await ElConfirm({
            title: "提示",
            message: `季/月报申报种类:财务报表(企业会计准则一般企业)-${monthDeclarationType ? "已执行" : "未执行"}
                      <br/>
                      年报申报种类:财务报表(企业会计准则一般企业)-${editingRow.value.declarationType ? "已执行" : "未执行"}
                      <br/>
                      二者存在差异，同一公司的会计准则执行状态建议保持一致，以避免后续申
                      报数据冲突或税务风险，确认继续修改吗?`,
            showCancel: true,
          })
          if (!res) {
            // 恢复原始数据
            if (editingRow.value && beforeEditingRow.value) {
              Object.assign(editingRow.value, beforeEditingRow.value)
            }
            editingRow.value = null
            return
          }
        }
      }
    }

    let params = {
      code: beforeEditingRow.value?.projectCode,
      period: beforeEditingRow.value?.taxPeriod,
      isContinue: editingRow.value?.existDeclareRecord,
    } as ITaxTypeEditParams
    editTaxtype(editingRow.value, params)
      .then((res) => {
        if (res.data) {
          ElNotify({
            type: "success",
            message: "修改成功",
          })
        } else {
          ElNotify({
            type: "warning",
            message: res.msg,
          })
          // 恢复原始数据
          if (editingRow.value && beforeEditingRow.value) {
            Object.assign(editingRow.value, beforeEditingRow.value)
          }
        }
      })
      .catch(() => {
        // 请求失败时也恢复原始数据
        if (editingRow.value && beforeEditingRow.value) {
          Object.assign(editingRow.value, beforeEditingRow.value)
        }
      })
      .finally(() => {
        getTaxListRequest()
        getLatestUpdateTimeRequest()
        editingRow.value = null
      })
  }

  const onDeclarationTypeChange = (row: any) => {
    // 根据code改变其对应的name字段
    row.declarationTypeName = declarationTypeOptions.value.find((item) => item.value === row.declarationType)?.label || ""
  }

  const onOptionChange = (row: any) => {
    // 根据code改变其对应的name字段
    row.optionName = DECLARATION_OPTIONS.find((item) => item.value === row.option)?.label || ""
  }

  const onTaxPeriodChange = (row: any) => {
    // 根据code改变其对应的name字段
    row.taxPeriodName = taxPeriodOptions.value.find((item) => item.value === row.taxPeriod)?.label || ""
  }

  const isShowTips = computed(() => {
    return !basicInfo.value.hasFinancialInfo
  })

  const tableContainerRef = ref()
  const tableHeight = ref(0)
  useResizeObserver(tableContainerRef, (entries) => {
    const entry = entries[0]
    const { height } = entry.contentRect
    tableHeight.value = height
  })

  const getCustomListRequest = () => {
    getCustomList().then((res) => {
      if (res.data) {
        taxTypeOptions = res.data
      }
    })
  }

  const isAdditionalTax = (row: any) => {
    // 根据实际业务逻辑判断是否为附加税
    return row.parentCode !== "0"
  }

  onMounted(() => {
    getTaxListRequest()
    getLatestUpdateTimeRequest()
    getCustomListRequest()
  })

  const { checkLoginState } = useTaxBureau()
  const goToChinaTax = () => {
    checkLoginState(103, TaxBureauLoginType.Jump, "xxbg/view/zhxxbg/#/cwkjzdbabg/index")
  }
</script>

<style lang="scss" scoped>
  .indented-row {
    padding-left: 20px; // 缩进距离，可以根据需要调整
  }
  .popover-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--h5);
  }

  .catogory {
    margin-left: 8px;
    color: var(--blue);
    cursor: pointer;
    font-size: var(--h5);
    display: inline-flex;
    align-items: center;

    .el-icon {
      margin-left: 4px;
      color: var(--orange);
    }
  }

  .category-table {
    padding: 5px;

    :deep(.el-table) {
      font-size: var(--h5);

      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .content {
    .main-content {
      height: calc(100vh - 115px);
      display: flex;
      flex-direction: column;
      //   卡片样式后续单独组件
      :deep(.el-card__body) {
        font-size: var(--h5);
        display: flex;
        padding: 6px 7px;
        line-height: 18px;
        color: var(--grey);

        .el-icon {
          padding-top: 2px;
          margin-right: 3px;
          font-size: var(--h4);
          color: var(--orange);
        }

        img {
          cursor: pointer;
          padding-top: 2px;
          width: var(--h3);
          height: var(--h3);
          vertical-align: bottom;
        }
      }

      .main-content-header {
        padding: 11px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left-content {
          display: flex;
          align-items: center;
        }
      }

      .main-content-body {
        height: calc(100vh - 115px);
        flex: 1;
        display: flex;
        flex-direction: column;
        .table-container {
          flex: 1;
          :deep(.el-table) {
            position: absolute;
          }
        }
        .footer-tips {
          margin-top: 10px;
        }
      }
    }
  }

  .form-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 18px;

    :deep(.el-form-item) {
      flex: 1;
      margin-bottom: 0;

      .el-input,
      .el-select,
      .el-date-picker {
        width: 100%;
      }
    }
  }
</style>
