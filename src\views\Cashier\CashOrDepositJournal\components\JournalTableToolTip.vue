<template>
    <ToolTip :content="content" :lineClamp="1" :teleported="true" placement="right-start" :offset="-8" :class="props.class">
        {{ content }}
    </ToolTip>
</template>

<script setup lang="ts">
import ToolTip from "@/components/Tooltip/index.vue";
const props = withDefaults(
    defineProps<{
        content: string;
        class?: string;
    }>(),
    {
        class: "",
    }
);
</script>
