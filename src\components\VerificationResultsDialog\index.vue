<!-- 校验结果 -->
<template>
  <div
    class="box dialogDrag"
    v-if="visible">
    <div
      v-dialogDrag
      class="title el-dialog__header">
      <div>
        <span class="strong-prompt">校验问题</span>
        <span class="prompt">可拖动气泡窗</span>
      </div>
      <img
        width="24"
        height="24"
        src="@/assets/Menu/close-btn.png"
        alt=""
        @click="handleClose" />
    </div>
    <div class="body">
      <div v-if="forceValidate.length">
        <el-icon style="color: var(--red)"><WarningFilled /></el-icon>
        <span class="strong-prompt">强制校验：需处理后申报</span>
        <div class="message">
          <p
            v-for="(item, index) in forceValidate"
            :key="index">
            {{ `${index + 1}、${item.content}` }}
          </p>
        </div>

        <el-divider />
      </div>
      <div v-if="promptValidate.length">
        <el-icon style="color: var(--yellow)"><WarningFilled /></el-icon>
        <span class="strong-prompt">提示校验：仅提醒，可直接申报</span>
        <div class="message">
          <p
            v-for="(item, index) in promptValidate"
            :key="index">
            {{ `${index + 1}、${item.content}` }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      validateMessages: { [key: string]: any }[]
    }>(),
    {
      validateMessages: () => [],
    },
  )
  const visible = defineModel<boolean>({ default: true })

  const forceValidate = computed(() => {
    return props.validateMessages.filter((item) => item.type === "error")
  })
  const promptValidate = computed(() => {
    return props.validateMessages.filter((item) => item.type === "warning")
  })

  function handleClose() {
    visible.value = false
  }

  onMounted(() => {
    visible.value = !!props.validateMessages.length
  })
</script>
<style scoped lang="scss">
  .box {
    z-index: 999;
    position: absolute;
    left: 190px;
    top: 274px;
    width: 650px;
    // min-height: 400px;

    border-radius: 4px;
    padding: 15px;
    background: var(--white);
    box-shadow: 0px 15px 30px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e5e5;

    &:before {
      content: "";
      position: absolute;
      top: 310px;
      left: -5px;
      width: 10px;
      height: 10px;
      background: var(--white);
      box-shadow: 0px 15px 30px 0px rgba(0, 0, 0, 0.1);
      border-left: 1px solid #e5e5e5;
      border-bottom: 1px solid #e5e5e5;
      transform: rotate(45deg);
    }

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .prompt {
        margin-left: 20px;
        font-size: 10px;
        color: var(--grey);

        &::before {
          content: "*";
          color: var(--orange-dark);
          vertical-align: middle;
        }
      }

      img {
        cursor: pointer;
      }
    }

    .body {
      height: 370px;
      max-height: 370px;
      overflow-y: auto;
    }

    .strong-prompt {
      font-weight: 500;
      font-size: var(--h5);
      vertical-align: middle;
    }

    .el-icon {
      margin-right: 6px;
      vertical-align: middle;
    }

    .message {
      padding-left: 15px;

      p {
        margin: 6px 0;
        padding: 0 5px;
        color: var(--grey);
        font-size: var(--h5);

        &:hover {
          background: #f8faf9;
        }
      }
    }

    .el-divider--horizontal {
      margin: 16px 0;
    }
  }
</style>
