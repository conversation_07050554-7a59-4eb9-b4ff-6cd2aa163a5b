import { getKeyByValue } from "./utils"

export enum EnumSource {
  "税局获取" = 0,
  "自行添加" = 1,
}

/**
 * 申报选项枚举
 */
export enum EnumOptions {
  "无需申报" = 1,
  "申报" = 0,
}
// 获取更新任务状态
// 返回值=1，则成功；2 成功有变动；3成功没变动；-1 失败
export enum EnumUpdateStatus {
  "成功" = 1,
  "成功有变动" = 2,
  "成功没变动" = 3,
  "正在处理中" = 0,
  "失败" = -1,
}

/**
 * 申报选项
 */
export const DECLARATION_OPTIONS = [
  { label: "申报", value: EnumOptions.申报 },
  { label: "无需申报", value: EnumOptions.无需申报 },
]
// 企业会计准则
export const ACCOUNTING_STANDARD = [
  { label: "财务报表（企业会计准则一般企业）-未执行", value: 0 },
  { label: "财务报表（企业会计准则一般企业）-已执行", value: 1 },
]

export const SPECIAL_TAX_NAMES = ["地方教育附加", "教育费附加", "城市维护建设税"]
export const TARGET_TAX_NAME = "增值税"
/**
 * 纳税期限枚举
 */
export enum TaxPeriodEnum {
  Month = "月",
  Quarter = "季",
  HalfYear = "半年",
  Year = "年",
  OneTime = "次",
}

/**
 * 申报种类与纳税期限映射关系
 */
export const DECLARATION_TAX_PERIOD_MAP: { [key: string]: string[] } = {
  车船税纳税申报表: [TaxPeriodEnum.Month, TaxPeriodEnum.Quarter, TaxPeriodEnum.HalfYear, TaxPeriodEnum.Year, TaxPeriodEnum.OneTime],
  代收代缴车船税: [TaxPeriodEnum.Month, TaxPeriodEnum.Quarter, TaxPeriodEnum.HalfYear, TaxPeriodEnum.Year, TaxPeriodEnum.OneTime],

  "城镇土地使用税 房产税纳税申报表（房产税）": [TaxPeriodEnum.Quarter],
  房产税从租计征: [TaxPeriodEnum.Quarter],
  房产税从价计征: [TaxPeriodEnum.Quarter],

  "城镇土地使用税 房产税纳税申报表（城镇土地使用税）": [TaxPeriodEnum.Quarter],

  契税: [TaxPeriodEnum.Month, TaxPeriodEnum.Quarter, TaxPeriodEnum.HalfYear, TaxPeriodEnum.Year, TaxPeriodEnum.OneTime],

  "环境保护税纳税申报表（次）": [TaxPeriodEnum.OneTime],
  "环境保护税纳税申报表（季）": [TaxPeriodEnum.Quarter],

  "土地增值税申报表（从事房地产开发的纳税人预征适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.HalfYear,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "土地增值税申报表（从事房地产开发的纳税人清算适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.HalfYear,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "土地增值税申报表（非从事房地产开发的纳税人适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "土地增值税申报表（从事房地产开发的纳税人清算后尾盘销售适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.HalfYear,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "土地增值税申报表（从事房地产开发的纳税人清算方式为核定征收适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.HalfYear,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "土地增值税申报表（整体转让在建工程适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.HalfYear,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "土地增值税申报表（非从事房地产开发的纳税人核定征收适用）": [
    TaxPeriodEnum.Month,
    TaxPeriodEnum.Quarter,
    TaxPeriodEnum.HalfYear,
    TaxPeriodEnum.Year,
    TaxPeriodEnum.OneTime,
  ],
  "印花税纳税申报表（月）": [TaxPeriodEnum.OneTime],
  "印花税纳税申报表（季）": [TaxPeriodEnum.OneTime],
  "印花税纳税申报表（年）": [TaxPeriodEnum.OneTime],
  "印花税纳税申报表（半年）": [TaxPeriodEnum.OneTime],
  "印花税纳税申报表（次）": [TaxPeriodEnum.OneTime],
} as const

/**
 * 获取纳税期限选项的辅助函数
 * @param declarationTypeName 申报种类
 * @param taxPeriodDict 纳税期限字典
 * @returns 纳税期限选项
 */
export const getTaxPeriodOptions = (declarationTypeName: string, taxPeriodDict: { [key: string]: string }) => {
  const periods = DECLARATION_TAX_PERIOD_MAP[declarationTypeName] || []
  return periods.map((period: any) => ({
    label: period,
    value: Number(getKeyByValue(taxPeriodDict, period)),
  }))
}

/**
 * 税费种认定注意事项
 */
export const TAX_DETERMINATION_INFO = `<span>
  税费种认定注意事项：
  <br />
  ①只需要做税源登记，不需要做税费种认定，也可以在税务局进行申报的报表：
  车船税、房产税（从价、从租）、城镇土地使用税、契税、环保税、土地增值税纳税申报表（二）、（四）、（五）、印花税（按次）；土增（三）、（七）需要土地增值税清算核定后采集税源。
  <br />
  ②同时需要做税源和税费种认定，才可以在税务局进行申报的报表：
  印花税（按期申报）、资源税（含水资源税）、烟叶税、耕地占用税、土地增值税（一）报表。
  <br />
  ③除以上两种情况，其他税费种需要在税务局做税费种认定后，才可以在税务局进行申报。
  <br />
  温馨提示：
  <br />
  车船税、城镇土地使用税、房产税（从租、从价）：一般不需要做税费种登记，只做税源采集就可以填报了，但如果在税局做了税费种，或是税局主动给做了税费种，且税费种认定信息为按月或按半年等，则要优先取税费种中所属期信息~
</span>`

/**
 * 符合条件的主体信息
 */
export const ELIGIBLE_ENTITIES_INFO = `
  <span>可以不在税务局填报财务报装的主体有：村集体经济组织、工会组织、农民专业合作社、个体工商户（实行定期定额管理的除外）选择执行《村集体经济组织会计制度》、《工会会计制度》、《农民专业合作社财务会计制度》、《个体工商户会计制度》的纳税人。</span>
  <br/>
  <br/>
  <span>应报送财务报表的企业，未按照规定报送财务报表的，由税务机关责令限期改正，可以处二千元以下的罚款；情节严重的，处二千元以上一万元以下的罚款。</span>
`

/**
 * 表单验证规则
 */
export const FORM_RULES = {
  projectCode: [{ required: true, message: "请输入征收项目", trigger: "blur" }],
  declarationType: [{ required: true, message: "请输入申报种类", trigger: "blur" }],
  taxPeriod: [{ required: true, message: "请输入纳税期限", trigger: "blur" }],
  option: [{ required: true, message: "请输入申报选项", trigger: "blur" }],
  confirmStartDate: [{ required: true, message: "请选择认定有效期起", trigger: "blur" }],
  confirmEndDate: [{ required: true, message: "请选择认定有效期止", trigger: "blur" }],
}

/**
 * 申报种类字典
 */

export const taxPeriodDict = {
  "0": "月",
  "1": "季",
  "2": "年",
  "3": "半年",
  "4": "次",
}
