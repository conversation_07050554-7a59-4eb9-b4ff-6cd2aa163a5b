import { ElTooltip } from "element-plus"
import questionIcon from "@/assets/Icons/question.png"
export function useTooltip() {
  const renderTooltip = (content: string) => {
    return h(
      ElTooltip,
      {
        effect: "light",
        placement: "right-start",
        popperClass: "el-option-tool-tip",
        content: content,
        rawContent: true,
        teleported: true,
        appendTo: "body",
      },
      {
        default: () =>
          h(
            "div",
            {
              class: "label-with-icon",
            },
            [
              h("img", {
                class: "img-question",
                src: questionIcon,
                style: { height: "16px" },
              }),
            ],
          ),
      },
    )
  }
  return {
    renderTooltip,
  }
}
