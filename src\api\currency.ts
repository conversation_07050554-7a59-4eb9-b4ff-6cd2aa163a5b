import { request, type IResponseModel } from "@/util/service";

export function getCurrencyApi() {
    return request({
        url: "/api/Currency/List",
        method: "get",
    });
}
export interface ICurrency {
    asId: number;
    code: string;
    id: number;
    isBaseCurrency: boolean;
    name: string;
    preName: string;
    rate: number;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}
