<script lang="ts" setup>
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { useAccountSetStore } from "@/store/modules/accountSet";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoneyWithZero } from "@/util/format";
import { request, type IResponseModel } from "@/util/service";
import { ElLoading, ElMessage } from "element-plus";
import { reactive, ref, toRef, watch, onMounted, onBeforeUnmount, computed, watchEffect } from "vue";
import Table from "@/components/Table/index.vue";
import { ElNotify } from "@/util/notify";
import type { IValueTypeOption, IEquationItem } from "@/views/Statements/types";
import { isInWxWork } from "@/util/wxwork";
import ElOption from "@/components/Option/index.vue";
import type { IEquationSingleValue } from "../../types";
// import FilterCustomSelect from "./FilterCustomSelect.vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
// import SelectV2 from "./SelectV2.vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import Tooltip from "@/components/Tooltip/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { ElConfirm } from "@/util/confirm";
import ValueRulesDialog from "./ValueRulesDialog.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "StateEditEquation";

const accountsetStore = useAccountSetStore();
const accountStandard = accountsetStore.accountSet!.accountingStandard;
const accountSubjectStore = useAccountSubjectStore();
const _ = getGlobalLodash()
const props = withDefaults(
    defineProps<{
        statementId: number;
        lineId: number;
        pid: number;
        title: string;
        monthTitle: string;
        yearTitle: string;
        valueTypeOptions: IValueTypeOption[];
        classify?: boolean;
        columnType?: number | string;
        isSixComeGo?: boolean;
        isClassification?: boolean;
        isBalanceSheet?: boolean;
        allAACalcStatement?:boolean;
    }>(),
    {
        classify: false,
        allAACalcStatement: true,
    }
);

const emit = defineEmits<{
    (e: "handleSubmitSuccess"): void;
    (e: "handleCancel"): void;
    (e: "resetDiyFormula", statementId: number, lineId: number, isClassification: boolean): void;
}>();

const subjectDataAll = toRef(accountSubjectStore, "accountSubjectListWithoutDisabled");
const subjectData = ref(_.cloneDeep(subjectDataAll.value));
const asubIdList = ref<any[]>([]);

// 取数规则弹窗
const valueRulesDialogRef = ref<InstanceType<typeof ValueRulesDialog>>();
const showValueRulesDialog = () => {
    valueRulesDialogRef.value?.changeVisible(true);
};
// 需要重分类提示和重置公式按钮
const needClassification = computed(() => props.isSixComeGo && [1, 2, 3].includes(accountStandard));
// 是否重分类默认参数
const defaultClassification = computed(() => {
    if (props.isSixComeGo) {
        if ([1, 2, 3].includes(accountStandard)) {
            return props.isClassification;
        } else if ([4, 5].includes(accountStandard)) {
            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
});

watch(subjectDataAll, () => {
    subjectData.value = _.cloneDeep(subjectDataAll.value);
    if (newData.asubId && !subjectData.value.find((v) => Number(v.asubId) === Number(newData.asubId))) {
        newData.asubId = "";
    }
    asubOptions();
});
function asubOptions() {
    subjectDataAll.value.forEach((item) => {
        asubIdList.value.push({ label: item.asubCode + "-" + item.asubAAName, value: item.asubId.toString() });
    });
}
asubOptions();
// 科目名称
// const getSubjectData = () => {
// getAllAsubApi()
//     .then((data) => {
//         subjectData.value = data.data;
//         subjectDataAll = data.data;
//     })
//     .catch((error) => {
//         console.log(error);
//         ElMessage.error("出现错误，请刷新页面重试");
//     });

// };
// getSubjectData();
const columns: Array<IColumnProps> = [
    { 
        label: "科目名称", 
        prop: "asub_name", 
        minWidth: 240, 
        align: "left", 
        headerAlign: "center",
        width: getColumnWidth(setModule, "asub_name")
    },
    {
        label: "运算符号",
        prop: "operator",
        minWidth: 80,
        align: "left",
        headerAlign: "center",
        formatter: (_row, _column, cellValue) => {
            return getOperatorText(cellValue);
        },
        width: getColumnWidth(setModule, "operator")
    },
    {
        label: "取数规则",
        prop: "value_type",
        minWidth: 80,
        align: "left",
        headerAlign: "center",
        formatter: (_row, _column, cellValue) => {
            return getValueTypeText(cellValue);
        },
        width: getColumnWidth(setModule, "value_type")
    },
    {
        label: props.monthTitle,
        prop: "amount",
        minWidth: 160,
        align: "left",
        headerAlign: "center",
        formatter: (_row, _column, cellValue) => {
            return formatMoneyWithZero(cellValue);
        },
        width: getColumnWidth(setModule, "amount")
    },
    {
        label: props.yearTitle,
        prop: "initalAmount",
        minWidth: 160,
        align: "left",
        headerAlign: "center",
        formatter: (_row, _column, cellValue) => {
            return formatMoneyWithZero(cellValue);
        },
        width: getColumnWidth(setModule, "initalAmount")
    },
    { slot: "operation" },
];

const tableData = ref<IEquationItem[]>([]);
const tableDataOrigin = ref<IEquationItem[]>([]);
const emptyText = ref("");
const loading = ref(false);
function searchEquationData() {
    if (props.statementId !== 0 && props.lineId !== 0) {
        // 获取公式列表
        loading.value = true;
        request({
            url: "/api/StatementEquation",
            method: "get",
            params: {
                statementId: props.statementId,
                lineId: props.lineId,
                pId: props.pid,
                columnType: props.columnType || -1,
                isClassification: defaultClassification.value,
            },
        })
            .then((res: IResponseModel<IEquationItem[]>) => {
                loading.value = false;
                tableData.value = res.data.slice();
                tableDataOrigin.value = res.data.slice();
                emptyText.value = !tableData.value.length ? "暂无数据" : "";
                emptyText.value = !tableData.value.length ? "暂无数据" : "";
            })
            .catch(() => {
                loading.value = false;
                ElMessage.error("出现错误，请刷新页面重试");
            });
    }
}
function areArraysEqual(arr1: IEquationItem[], arr2: IEquationItem[]) {
    if (arr1.length !== arr2.length) {
        return false;
    }

    for (let i = 0; i < arr1.length; i++) {
        if (arr1[i].asub_id !== arr2[i].asub_id || arr1[i].operator !== arr2[i].operator || arr1[i].value_type !== arr2[i].value_type) {
            return false;
        }
    }

    return true;
}
function getOperatorText(value: number) {
    switch (value) {
        case 1:
            return "+";
        case 2:
            return "-";
        default:
            return "";
    }
}

function getValueTypeText(type: number) {
    switch (type) {
        case 0:
            return "期初余额";
        case 1:
            return "余额";
        case 2:
            return "借方发生额";
        case 3:
            return "贷方发生额";
        case 4:
            return "发生额";
        case 5:
            return "借方余额";
        case 6:
            return "贷方余额";
        case 8:
            return "本级科目借方余额";
        case 9:
            return "本级科目贷方余额";
        case 10:
            return "末级科目借方余额";
        case 11:
            return "末级科目货方余额";
        case 12:
            return "辅助核算借方余额";
        case 13:
            return "辅助核算贷方余额";
        default:
            return "";
    }
}

function init() {
    tableData.value = [];
    resetNewData();
    searchEquationData();
}

const newData = reactive({
    asubId: "",
    operator: "1",
    valueType: "",
});

function resetNewData() {
    newData.asubId = "";
    newData.valueType = props.valueTypeOptions[0].value;
}
const addAsub = ref<number[]>([]);
let addButtonCanClick = true;

// 重置自定义公式
const resetFormula = async () => {
    ElConfirm("确认删除自定义公式？", false, () => {}, "重置公式").then((r: boolean) => {
        if (r) {
            emit("resetDiyFormula", props.statementId, props.lineId, props.isClassification);
        }
    });
};
function handleAddData() {
    if (!addButtonCanClick) return;
    if (newData.asubId === "") {
        ElNotify({
            message: "请先选择科目后进行公式项添加",
            type: "warning",
        });
        return;
    }
    const asubid = Number(newData.asubId);
    const valueType = Number(newData.valueType);

    for (let index = 0; index < tableData.value.length; index++) {
        const element = tableData.value[index];
        if (element.asub_id == asubid && element.value_type == valueType) {
            ElNotify({
                message: "科目已经存在",
                type: "warning",
            });
            return;
        }
    }
    // 是否存在某个科目
    addButtonCanClick = false;
    request({
        url: "/api/AccountSubject/ExistsAsub",
        method: "post",
        params: { asubid: asubid, isClassification: defaultClassification.value },
    })
        .then((res: IResponseModel<boolean>) => {
            //获取金额
            request({
                url: "/api/StatementEquation/SingleValue",
                method: "get",
                params: { pId: props.pid, asubId: asubid, valueType: valueType, statementId: props.statementId },
            })
                .then((res: IResponseModel<IEquationSingleValue>) => {
                    tableData.value.push({
                        asub_id: asubid,
                        asub_name: getAsubName(asubid),
                        amount: res.data?.amount,
                        operator: Number(newData.operator),
                        value_type: valueType,
                        initalAmount: res.data?.initalAmount,
                    });
                    addAsub.value.push(asubid);
                    addButtonCanClick = true;
                })
                .catch((err) => {
                    addButtonCanClick = true;
                    console.log(err);
                    ElNotify({
                        message: "获取总账数据错误",
                        type: "error",
                    });
                });
        })
        .catch((err) => {
            console.log(err);
            addButtonCanClick = true;
            ElNotify({
                message: "请选择正确的科目",
                type: "warning",
            });
        });
}

function getAsubName(asubid: number) {
    for (let index = 0; index < subjectData.value.length; index++) {
        const element = subjectData.value[index];
        if (Number(element.asubId) === asubid) {
            return element.asubAAName;
        }
    }
    return "";
}
let deleteAsub = ref<number[]>([]);
function handleDeleteData(index: number) {
    tableData.value.splice(index, 1);
    deleteAsub.value.push(index);
}

function handleSubmit() {
    if (areArraysEqual(tableData.value, tableDataOrigin.value)) {
        emit("handleSubmitSuccess");
        return false;
    }
    const seCreateList = [];
    if (tableData.value.length > 0) {
        for (let index = 0; index < tableData.value.length; index++) {
            const element = tableData.value[index];
            seCreateList.push(createNode(element, index));
        }
    }

    const loadingInstance = ElLoading.service();
    let lineName = !props.classify ? props.title : "编辑公式  -  " + props.title;
    // 编辑公式
    request({
        url: "/api/StatementEquation",
        method: "put",
        params: {
            lineName,
            statementId: props.statementId,
            lineId: props.lineId,
            columnType: props.columnType || -1,
            isClassification: defaultClassification.value,
        },
        data: JSON.stringify(seCreateList),
        headers: {
            "Content-Type": "application/json",
        },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                loadingInstance.close();
                if (!isInWxWork() && deleteAsub.value.length && seCreateList.length) {
                    ElNotify({
                        message: `公式成功删除`,
                        type: isErp.value ? "warning" : "success",
                    });
                }
                if (addAsub.value.length && seCreateList.length) {
                    ElNotify({
                        message: `公式成功添加`,
                        type: "success",
                    });
                }
                addAsub.value = [];
                deleteAsub.value = [];
                emit("handleSubmitSuccess");
            } else {
                console.log(res.msg);
                loadingInstance.close();
            }
        })
        .catch((err) => {
            loadingInstance.close();
            console.log(err);
            ElNotify({
                message: "公式保存失败了",
                type: "warning",
            });
        });
    subjectData.value = subjectDataAll.value;
}
// const handleAsubNameFilter = (v: string) => {
//     subjectData.value = subjectDataAll.value.filter(
//         (item: IAccountSubjectModel, index: number) =>
//             item.asubCode.includes(v.trim()) || item.asubAAName.includes(v.trim()) || item.acronym.includes(v.trim())
//     );
// };
function createNode(element: IEquationItem, index: number) {
    const node = { ...element };
    node.statement_id = props.statementId;
    node.line_id = props.lineId;
    node.multiplication = 1;
    node.column_type = props.statementId === 3009 ? Number(props.columnType) : 1010; //业务活动表需要
    node.rate = 1;
    node.source = 0;
    node.flag = 1;
    node.equationType = 1000;
    node.equationSn = index;
    return node;
}
const isErp = ref(window.isErp);
defineExpose({
    init,
    searchEquationData,
});
const tooltipContent = ref("");
const visible = ref(false);
watch(
    () => newData.asubId,
    (val) => {
        let item = asubIdList.value.find((v) => Number(v.value) === Number(val));
        if (item) {
            tooltipContent.value = item.label;
        } else {
            tooltipContent.value = "";
        }
    },
    { immediate: true }
);
let flag = false;
watch(
    () => tooltipContent.value,
    (val) => {
        if (val.length > 15) {
            flag = true;
        } else {
            flag = false;
        }
    }
);
const enterInput = () => {
    if (flag) {
        visible.value = true;
    } else {
        visible.value = false;
    }
};
const leaveInput = () => {
    visible.value = false;
};
const handleVisibleChange = (visible: boolean) => {
    let div = document.querySelector(".edit-subject-select .el-select-v2");
    if (!visible) {
        div?.classList.add("noVisible");
    } else {
        showAsubIdList.value = JSON.parse(JSON.stringify(asubIdList.value));
        div?.classList.remove("noVisible");
    }
};
const handleChange = () => {
    let div = document.querySelector(".edit-subject-select .el-select-v2");
    div?.classList.add("noVisible");
};
onMounted(() => {
    let div = document.querySelector(".edit-subject-select .el-select-v2__wrapper");
    div?.addEventListener("mouseenter", enterInput);
    div?.addEventListener("mouseleave", leaveInput);
});
onBeforeUnmount(() => {
    let div = document.querySelector(".edit-subject-select .el-select-v2__wrapper");
    div?.removeEventListener("mouseenter", enterInput);
    div?.removeEventListener("mouseleave", leaveInput);
});

const showAsubIdList = ref<any[]>([]);
const showValueTypeOptions = ref<IValueTypeOption[]>([]);
watchEffect(() => { 
    showAsubIdList.value = JSON.parse(JSON.stringify(asubIdList.value));
    showValueTypeOptions.value = JSON.parse(JSON.stringify(props.valueTypeOptions));
});
function AsubIdFilterMethod(value: string) {
    showAsubIdList.value = commonFilterMethod(value, asubIdList.value, 'label');
}
function valueTypeFilterMethod(value: string) {
    showValueTypeOptions.value = commonFilterMethod(value, props.valueTypeOptions, 'label');
}
</script>

<template>
    <div>
        <div class="edit-title">编辑公式 - {{ title }}</div>
        <div class="edit-top">
            <div class="select-item edit-subject-select" style="display: flex; align-items: center">
                科目名称：
                <!-- <FilterCustomSelect
                    ref="selectRef"
                    fit-input-width
                    filterable
                    :teleported="false"
                    placeholder=" "
                    :key="Math.random()"
                    default-first-option
                    v-model="newData.asubId"
                    :filter-method="handleAsubNameFilter"
                >
                    <ElOption
                        v-for="item in subjectData"
                        :optionValue="item.asubCode + '-' + item.asubAAName"
                        :key="item.asubId"
                        :label="item.asubAAName"
                        :value="item.asubId"
                    >
                        <span>{{ item.asubCode }}-{{ item.asubAAName }}</span>
                    </ElOption>
                </FilterCustomSelect> -->
                <!-- <Tooltip :content="tooltipContent" :visible="visible" effect="light" placement="right"> -->
                    <SelectV2
                        ref="selectRef"
                        :options="showAsubIdList"
                        :filterable="true"
                        :teleported="false"
                        :fit-input-width="true"
                        placeholder=" "
                        v-model="newData.asubId"
                        @visible-change="handleVisibleChange"
                        @change="handleChange"
                        :remote="true"
                        :toolTipOptions="{ dynamicWidth: true, fontSize: 14 }"
                        :filter-method="AsubIdFilterMethod"
                        :isSuffixIcon="true"
                    ></SelectV2>
                <!-- </Tooltip> -->
            </div>
            <div class="select-item edit-sign-select">
                运算符号：
                <el-select style="width: 70px" :teleported="false" fit-input-width v-model="newData.operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                </el-select>
            </div>
            <div class="select-item edit-rules-select">
                取数规则：
                <!-- <el-select :teleported="false" fit-input-width v-model="newData.valueType">
                    <el-option v-for="item in valueTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <Select 
                    style="width: 135px" 
                    :teleported="false" 
                    v-model="newData.valueType"
                    :filterable="true"
                    :filter-method="valueTypeFilterMethod"
                >
                    <Option 
                        v-for="item in showValueTypeOptions" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value"
                    ></Option>
                </Select>
                <img
                    v-if="props.isBalanceSheet"
                    src="@/assets/Settings/question.png"
                    style="height: 18px; cursor: pointer; margin-left: 7px"
                    @click="showValueRulesDialog"
                />
            </div>
            <div class="buttons">
                <a v-if="needClassification" class="button solid-button" v-permission="['balancesheet-canedit']" @click="resetFormula">
                    重置公式
                </a>
                <a class="button solid-button" @click="handleAddData">添加</a>
            </div>
        </div>
        <div v-if="isErp" class="divider-line"></div>
        <div class="edit-center" style="width: 1100px; margin: 0 auto">
            <div v-if="needClassification" class="tips">当前为{{ props.isClassification ? "已" : "未" }}开启重分类状态</div>
            <Table
                v-loading="loading"
                :class="isErp ? 'erp-table' : 'custom-table'"
                :columns="columns"
                :data="tableData"
                :empty-text="emptyText"
                :scrollbar-show="true"
                :max-height="isErp ? 'calc(100vh - 280px)' : 'calc(100vh - 350px)'"
                :tableName="setModule"
            >
                <template #operation>
                    <el-table-column label="操作" min-width="80" align="left" header-align="center" :resizable="false">
                        <template #default="scope">
                            <a class="link" @click="handleDeleteData(scope.$index)">删除</a>
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click="handleSubmit">确定</a>
            <a class="button" @click="emit('handleCancel'), ((subjectData = subjectDataAll), (addAsub = []), (deleteAsub = []))">取消</a>
        </div>
        <ValueRulesDialog :allAACalcStatement="props.allAACalcStatement"  ref="valueRulesDialogRef" />
    </div>
</template>

<style lang="less" scoped>
@import "@/style/Functions.less";

.edit-title {
    .set-font;
    padding-top: 20px;
    font-weight: bold;
}

.edit-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 0;
    width: 1100px;
    margin: 0 auto;
    border-bottom: 0;
    .set-font;

    .edit-subject-select {
        :deep(.el-select) {
            width: 190px;
        }
        :deep(.el-select-v2) {
            width: 220px;
            // .el-select-v2__wrapper.is-focused {
            //     .el-select-v2__placeholder {
            //         color: var(--border-color);
            //     }
            // }
            // &.noVisible {
            //     .el-select-v2__wrapper.is-focused {
            //         .el-select-v2__placeholder {
            //             color: var(--font-color);
            //         }
            //     }
            // }
            .el-select-v2__placeholder {
                text-align: left;
            }
        }
    }
    .edit-sign-select {
        .detail-el-select(70px);
    }
    .edit-rules-select {
        .detail-el-select(135px);
    }
}

.edit-center {
    padding: 0 20px;
    :deep(.table .el-table) {
        border-bottom: none;
        .el-scrollbar {
            min-height: 30px;
        }
    }
    :deep(.el-table__empty-block) {
        min-height: 30px;
    }
    :deep(.erp-table) {
        .el-table {
            border-bottom: 1px solid var(--border-color);
        }
        .el-table__body {
            .el-table__row {
                &:last-child {
                    .el-table__cell {
                        border-bottom: none;
                    }
                }
            }
        }
        .el-table__empty-block {
            width: 100% !important;
        }
    }
}

.buttons {
    margin-top: 20px;
    padding-bottom: 20px;

    .button + .button {
        margin-left: 10px;
    }
}

// 页面调窄
.edit-top,
.edit-center {
    width: 1100px;
    margin: 0 auto;
}
.divider-line {
    height: 1px;
    background-color: var(--title-split-line);
    margin-bottom: 20px;
}

:deep(.scroll-opacity.el-scrollbar) {
    &.el-scrollbar__bar.is-vertical {
        opacity: 1; //改为0不显示滚动条
    }
}
body[erp] {
    .edit-top {
        margin-bottom: 20px;
    }
}
.tips {
    color: #f00;
    font-size: 14px;
    text-align: left;
}
</style>
