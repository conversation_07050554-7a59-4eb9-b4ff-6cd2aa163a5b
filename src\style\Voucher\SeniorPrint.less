@import "../SelfAdaption.less";
@import "../Functions.less";
:deep(.el-textarea__inner) {
    min-height: 100% !important;
}

:deep(.el-input__wrapper) {
    padding: 0 11px;
    height: 100%;
}
:deep(.vdr-container) {
    &.active {
        border-style: none !important;
    }

    &.resizable.active {
        border-style: solid !important;
        border-color: #44b449 !important;
        &.isErp {
            border-color: #3d7fff !important;
        }
    }
    &.unactive {
        border-style: none !important;
        .vdr-handle {
            display: none !important;
        }
    }
    & .vdr-handle {
        z-index: 9999;
    }
}
:deep(.table-drag-box) {
    &.unactive {
        border: 1px solid #999;
        border-style: solid !important;
    }
    &.active {
        border: 1px solid #44b449;
        border-style: solid !important;
        &.isErp {
            border-color: #3d7fff !important;
        }
    }
    // border:1px solid #999;
    .smooth-dnd-draggable-wrapper {
        position: relative;
        // border-right: 1px solid #999;
    }
}
.table-drag-content {
    // border: 1px solid #999;
    //造成表格高度不准确
    box-sizing: border-box;
    height: 100%;
}
:deep(.smooth-dnd-container) {
    display: flex !important;
    box-sizing: border-box;
    height: 100%;
    width: 100%;
    &:has(.card-ghost) .smooth-dnd-draggable-wrapper {
        &:last-child {
            border-right: 1px solid #999;
        }
    }
    .card-ghost {
        height: 100%;
        box-sizing: border-box;
        border: 1px solid #999;
        border-right: none;
        .table-debit-amount,
        .table-credit-amount {
            border: 1px solid #999;
            border-top: none;
            left: 0px;
        }
    }
    // position: relative;
    // left: -0.5px;
}
.noDraggableTable {
    box-sizing: border-box;
    // position: relative;
    // left: -0.5px;
    .table-body {
        box-sizing: border-box;
        border-right: 1px solid #999;
        position: relative;
    }
    // .draggable-item{
    //     position:relative;
    //     left:-0.5px;
    // }
}
.draggable-item {
    // position: relative;
    box-sizing: border-box;
    cursor: move;
    display: flex;
    .table-body {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        // -webkit-box-flex: 1;
        // -ms-flex: 1;
        flex: 1;
        // border-right: 1px solid #999;

        .table-lable {
            // white-space: nowrap;
            overflow: hidden;
            // height: 50px;
            // border-top: 1px solid #999;
        }
        .table-content {
            // white-space: nowrap;
            box-sizing: border-box;
            overflow: hidden;
            // flex: 1;
            // height: calc(100% - 50px);
            border-top: 1px solid #999;
            // border-bottom: 1px solid #999;
        }

        .table-debit-amount,
        .table-credit-amount {
            width: 100%;
            overflow: hidden;
            z-index: 99;
            box-sizing: border-box;
        }
    }
}
.table-total {
    display: flex;
    width: 100%;
    position: absolute;
    box-sizing: border-box;
    border-top: 1px solid #999;
    overflow: hidden;
}
.drag-w-line {
    position: absolute;
    top: 0;
    left: 100%;
    transform: translateX(-50%);
    // right: 0px;
    width: 8px;
    height: 100%;
    z-index: 10;
    padding: 0;
    margin: 0;
    background-color: transparent;
    display: flex;
    justify-content: center;
    cursor: col-resize;
    div {
        height: 100%;
        width: 2px;
        background-color: #3d7fff;
        display: none;
    }
    &:hover div {
        display: block;
    }
}
.drag-h-line {
    width: 100%;
    height: 8px;
    position: absolute;
    left: 0;
    background-color: transparent;
    cursor: row-resize;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    z-index: 10;
    div {
        width: 100%;
        height: 2px;
        background-color: #3d7fff;
        display: none;
    }
    &:hover div {
        display: block;
    }
}
.seniorPrint {
    padding: 10px !important;
    height: 100%;
    box-sizing: border-box;
    //拖动时取消选中效果
    user-select: none;
}
.print-top {
    display: flex;
    justify-content: space-between;
}
.zoom-btn {
    display: flex;
    .el-icon-minus,
    .el-icon-plus {
        width: 30px;
        line-height: 28px;
        font-size: 14px;
        color: #999;
        text-align: center;
        cursor: pointer;
        &:first-child {
            border-right: 1px solid #ddd;
        }
    }
}
.print-tool-left {
    display: flex;
    justify-content: flex-start;
    padding: 0 0 10px 0;
}
.print-tool-right {
    .screen-btn {
        margin-left: 18px;
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
        &::before {
            content: " ";
            width: 17px;
            height: 17px;
            background-repeat: no-repeat;
            background-size: 100%;
            margin-right: 7px;
        }

        &:hover {
            color: var(--main-color);
        }
        &.full-screen {
            color: var(--main-color);
            &::before {
                width: 20px;
                height: 20px;
                background-image: url("@/assets/Voucher/suoxiao.png");
            }
            &.erp {
                &::before {
                    background-image: url("@/assets/Voucher/erp-suoxiao.png");
                }
            }
        }
        &.exit-full-screen {
            color: var(--font-color);
            &::before {
                width: 19px;
                height: 19px;
                background-image: url("@/assets/Voucher/fangda.png");
            }
            span {
                position: relative;
                top: 1px;
            }
        }
    }
}
.video-guide {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 26px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    line-height: 26px;
    padding: 0;
    cursor: pointer;
    // margin: 0;
}
.print-content {
    position: relative;
    height: calc(100% - 38px);
    display: flex;
    text-align: left;
    .arrowLeftNav {
        background-image: url("@/assets/Voucher/arrow-left.png");
        width: 40px;
        height: 50px;
        position: absolute;
        left: 250px;
        top: 300px;
        z-index: 999;
        &:hover {
            background-image: url("@/assets/Voucher/arrow-left-active.png");
        }
    }
    .expandLeftNav {
        background-image: url("@/assets/Voucher/expand-left.png");
        width: 40px;
        height: 50px;
        position: absolute;
        left: -6px;
        top: 300px;
        z-index: 999;
    }
    .arrowRightNav {
        background-image: url("@/assets/Voucher/arrow-right.png");
        width: 40px;
        height: 50px;
        position: absolute;
        right: 248px;
        top: 300px;
        z-index: 999;
        &:hover {
            background-image: url("@/assets/Voucher/arrow-right-active.png");
        }
    }
    .expandRightNav {
        background-image: url("@/assets/Voucher/expand-right.png");
        width: 40px;
        height: 50px;
        position: absolute;
        right: -6px;
        top: 300px;
        z-index: 999;
    }
    .nav-name {
        padding-left: 20px;
    }
    .nav-title {
        padding-left: 12px;
    }
    .nav-name,
    .nav-title {
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        font-style: normal;
    }
    .print-pageOption {
        border-bottom: 1px solid #ddd;
        padding: 16px 0;
        .print-pageOption-inner {
            margin: 6px 8px 0;
        }
    }
    .page-item {
        display: flex;
        line-height: 30px;
        padding-top: 4px;
        font-size: 14px;
    }
    .nav-tool {
        height: 80px;
        border-bottom: 1px solid #ddd;
        padding: 16px 0;
        .tool-list {
            display: flex;
            flex-wrap: wrap;
            margin: 12px 8px;
            justify-content: space-between;
            .tool {
                flex-basis: calc(25% - 10px); /* 计算为三分之一减去间距 */
                margin-bottom: 10px; /* 设置元素间距 */
            }
            .tool {
                width: 42px;
                margin-bottom: 12px;
                padding-top: 4px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                background: #f8fbf8;
                &:hover {
                    color: #44b449;
                }
            }
            i {
                width: 25px;
                height: 25px;
            }
            .text-icon {
                i {
                    background: url("@/assets/Voucher/text.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/text-hover.png");
                }
            }
            .img-icon {
                i {
                    background: url("@/assets/Voucher/img.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/img-hover.png");
                }
            }
            .line-icon {
                i {
                    background: url("@/assets/Voucher/line.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/line-hover.png");
                }
            }
            .square-icon {
                i {
                    background: url("@/assets/Voucher/square.png") no-repeat center;
                    background-size: 100% 100%;
                }
                &:hover i {
                    background-image: url("@/assets/Voucher/square-hover.png");
                }
            }
            .tool-name {
                line-height: 22px;
                // font-size: 22px;
                // transform: scale(.5, .5);
                font-size: 12px;
                white-space: nowrap;
            }
        }
    }
    .nav-table {
        height: calc(100% - 242px);
        // overflow: auto;
        .nav-table-collapse {
            padding: 12px 11px 12px 10px;
            padding-bottom: 0;
        }
        // .detail-lm-default-scroll(8px);
        :deep(.el-collapse) {
            border: 0;
        }
        :deep(.el-collapse-item) {
            margin-bottom: 12px;
        }
        :deep(.el-collapse-item__header) {
            background-color: #f8fbf8;
            border: 1px solid #eaeaea;
        }
        :deep(.el-collapse-item__wrap) {
            border: 1px solid #eaeaea;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }
        :deep(.el-collapse-item__content) {
            padding: 16px 8px 8px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .is-selected {
                color: #44b449;
                border-color: #44b449;
            }
        }
        .nav-btn {
            font-size: 13px;
            line-height: 28px;
            width: 90px;
            color: #333;
            border: 1px solid #eaeaea;
            border-radius: 14px;
            text-align: center;
            margin-bottom: 12px;
            // padding: 0 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            &.is-selected {
                color: #44b449;
                border-color: #44b449;
                &.isErp {
                    color: #3d7fff;
                    border-color: #3d7fff;
                }
            }
        }
        h6 {
            margin: 0;
            padding: 0;
            width: 100%;
            font-size: 12px;
        }
        .flex-center {
            display: flex;
            align-items: center;
            .page-margin {
                display: flex;
                font-size: 11px;
                line-height: 24px;
                margin-top: 10px;
                .page-direction {
                    width: 24px;
                    height: 24px;
                    text-align: center;
                    border: 1px solid #44b449;
                    border-right: none;
                    color: #666;
                }
                :deep(.el-input-number) {
                    width: 48px;
                    .el-input-number__decrease,
                    .el-input-number__increase {
                        height: 12px !important;
                        width: 12px !important;
                    }
                    .el-input {
                        height: 24px;
                        .el-input__wrapper {
                            box-shadow: 0 0 0 0;
                            border: 1px solid #44b449;
                            padding-left: 0 !important;
                            padding-right: 0 !important;
                            .el-input__inner {
                                text-align: center;
                                padding-right: 13px !important;
                            }
                        }
                    }
                }
                .operate {
                    width: 18px;
                    height: 24px;
                    border: 1px solid #a3cfff;
                    display: flex;
                    flex-wrap: wrap;
                    span {
                        cursor: pointer;
                        width: 100%;
                        height: 12px;
                        text-align: center;
                        line-height: 12px;
                    }
                    .add-btn {
                        .el-icon-caret-top:before {
                            content: "\E60C";
                        }
                    }
                    .sub-btn {
                        .el-icon-caret-bottom:before {
                            content: "\E60B";
                        }
                    }
                }
            }
            &.isErp {
                .page-direction {
                    border: 1px solid #3d7fff;
                    border-right: none;
                }
                :deep(.el-input-number) {
                    .el-input {
                        .el-input__wrapper {
                            border: 1px solid #3d7fff;
                        }
                    }
                }
                .operate {
                    border: 1px solid #3d7fff;
                }
            }
        }
        .sub-level {
            padding-left: 20px;
            :deep(.el-input__wrapper) {
                padding: 0 6px;
            }
        }
    }
}

.print-left {
    width: 250px;
    border: 1px solid #ddd;
    margin-right: 16px;
    box-sizing: border-box;
    overflow-y: hidden;
    // height: 610px;
}
.print-center {
    flex: 1;
    border: 1px solid #ddd;
    background-color: #f6f6f6;
    color: #000;
    overflow: auto;
    position: relative;
    /* 自定义滚动条样式 */

    .detail-lm-default-scroll(8px);
    .main-drag-box {
        position: relative;
        background-color: #fff;
        margin: 20px auto;
        .drag-box {
            position: absolute;
            box-sizing: border-box;
        }
        img {
            width: 100%;
            height: 100%;
        }
        .print-time {
            box-sizing: border-box;
            position: absolute;
            bottom: 20px;
            right: 0;
            // width: 100%;
            font-size: 10px;
            color: #333;
            text-align: left;
        }
        .page-number {
            box-sizing: border-box;
            position: absolute;
            bottom: 20px;
            right: 10px;
            font-size: 10px;
            color: #333;
            // width: 100%;
            text-align: right;
        }
        .top-left-subscript,
        .top-right-subscript,
        .bottom-left-subscript,
        .bottom-right-subscript {
            position: absolute;
            width: 24px;
            height: 24px;
        }
        .top-left-subscript {
            border-bottom: 1px solid #ccc;
            border-right: 1px solid #ccc;
        }
        .top-right-subscript {
            border-bottom: 1px solid #ccc;
            border-left: 1px solid #ccc;
        }
        .bottom-left-subscript {
            border-top: 1px solid #ccc;
            border-right: 1px solid #ccc;
        }
        .bottom-right-subscript {
            border-top: 1px solid #ccc;
            border-left: 1px solid #ccc;
        }
    }
}
.print-right {
    width: 248px;
    border: 1px solid #ddd;
    overflow: scroll;
    .detail-lm-default-scroll(8px);
    margin-left: 16px;
    .rich-text {
        border-bottom: 1px solid #ddd;
        padding: 16px 0;
        .rich-text-name {
            padding-left: 22px;
        }
        .typeFace {
            margin: 12px 20px;
            .rich-text-btn {
                padding: 0 6px;
                &.is-active {
                    color: #44b449;
                }
                &:hover {
                    background: #eee;
                    // border: 1px solid #ddd;
                    // border-radius: 2px;
                }
            }
        }
        .rich-text-col {
            display: flex;
            margin: 12px 10px;
            justify-content: flex-start;
            flex-wrap: wrap;
            .cell {
                text-align: center;
                align-items: center;
                display: flex;
                flex-direction: column;
                width: 48px;
                height: 37px;
                padding-top: 4px;
                margin-right: 4px;
                margin-bottom: 12px;
                &:hover {
                    background: #eee;
                }
                img {
                    width: 17px;
                    height: 17px;
                }
                span {
                    font-size: 12px;
                    white-space: nowrap;
                }
                &.is-active {
                    color: #44b449;
                }
            }
        }
    }
}

.erpSeniorPrint {
    .print-content {
        .nav-table {
            :deep(.el-collapse-item__header) {
                background-color: #f7f9fd;
            }
        }
        .nav-tool {
            .tool-list .tool {
                background-color: #f7f9fd;
                &:hover {
                    color: #3d7fff;
                }
                &.text-icon:hover i {
                    background-image: url("@/assets/Voucher/text-blue.png");
                }
                &.img-icon:hover i {
                    background-image: url("@/assets/Voucher/img-blue.png");
                }
                &.square-icon:hover i {
                    background-image: url("@/assets/Voucher/square-blue.png");
                }
                &.line-icon:hover i {
                    background-image: url("@/assets/Voucher/line-blue.png");
                }
            }
        }
        .arrowLeftNav {
            &:hover {
                background-image: url("@/assets/Voucher/arrow-left-blue.png");
            }
        }
        .arrowRightNav {
            &:hover {
                background-image: url("@/assets/Voucher/arrow-right-blue.png");
            }
        }
        .expandLeftNav {
            background-image: url("@/assets/Voucher/expand-left-blue.png");
        }
        .expandRightNav {
            background-image: url("@/assets/Voucher/expand-right-blue.png");
        }
    }

    .print-right {
        .rich-text {
            .rich-text-btn {
                &.is-active {
                    color: #3d7fff;
                }
            }
        }
        .rich-text-col .cell {
            &.is-active {
                color: #3d7fff;
            }
        }
    }
}
.tips-content {
    padding-left: 34px;
    margin-top: 20px;
    font-size: 14px;
}
