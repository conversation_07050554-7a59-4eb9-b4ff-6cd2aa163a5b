<template>
    <div class="edit-voucher-edit-content">
        <div class="title">{{ voucherTitle }}</div>
        <!-- <div class="edit-voucher-edit-top" :class="zoomState">
            <a class="button solid-button mr-10" @click="handleSave" @mousedown="$event.stopPropagation()">保存</a>
            <a class="button" @click="handleCancel" @mousedown="$event.stopPropagation()">取消</a>
        </div>
        <div class="line"></div> -->
        <div class="voucher-content">
            <Voucher
                ref="voucherRef"
                :query-params="voucherQueryParams"
                :show-cancel="true"
                cancelTxt="取消"
                :edited="edited"
                @back="handleCancel"
                @save="handleSave"
                @voucher-changed="voucherChanged"
                @load-success="voucherLoadSuccess"
                @zoom="voucherZoomChange"
                @delete-voucher="handleDeleteVoucher"
            ></Voucher>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElNotify } from "@/util/notify";
import type { IResponseModel } from "@/util/service";
import { VoucherSaveModel, VoucherSaveParams, EditVoucherQueryParams, VoucherQueryParams } from "@/components/Voucher/types";

import { request } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import Voucher from "@/components/Voucher/index.vue";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";

const props = defineProps<{
    toVoucherAbout: () => void;
    back: () => void;
    reloadData: () => void;
}>();

const voucherRef = ref<InstanceType<typeof Voucher>>();

const voucherQueryParams = ref<VoucherQueryParams>();

const edited = ref(false);
let init = false;
const voucherChanged = () => {
    if (!init) return;
    edited.value = true;
};
function voucherLoadSuccess() {
    const vgId = voucherRef.value?.getVoucherModel()?.vgId;
    const voucherGroupList = useVoucherGroupStore().voucherGroupList;
    const voucherGroup = voucherGroupList.find((item) => item.id === vgId);
    voucherTitle.value = voucherGroup?.title || "记账凭证";
    props.toVoucherAbout();
    init = true;
}

let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    isSaving = true;
    voucherRef.value?.saveVoucher(
        new VoucherSaveParams(1010, (res: IResponseModel<VoucherSaveModel>) => {
            isSaving = false;
            if (res.state === 1000) {
                const voucherModel = voucherRef.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({ message: "亲，保存成功啦！", type: "success" });
                }
                handleCancel();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({ message: res.msg, type: "warning" });
                }
            } else if (res.state === 9999) {
                ElNotify({ message: "保存失败", type: "warning" });
            }
        })
    );
};
const handleCancel = () => {
    edited.value = false;
    voucherRef.value?.removeEventListener();
    voucherRef.value?.clearAAE();
    props.back();
};

const handleDeleteVoucher = () => {
    let voucherModel = voucherRef.value?.getVoucherModel();
    if (voucherModel) {
        ElConfirm("亲，确认要删除吗").then((r) => {
            if (r) {
                request({ url: "/api/Voucher?pId=" + voucherModel?.pid + "&vId=" + voucherModel?.vid, method: "delete" }).then(
                    (res: IResponseModel<boolean>) => {
                        if (res.data) {
                            ElNotify({
                                message: "删除成功！",
                                type: "success",
                            });
                            props.reloadData();
                        } else {
                            ElNotify({
                                message: res.msg || "亲，删除失败啦！请联系侧边栏客服！",
                                type: "warning",
                            });
                        }
                    }
                );
            }
        });
    }
};
const cachePid = ref(0);
const cacheVid = ref(0);
const checkVoucher = (pid: number, vid: number) => {
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
    cachePid.value = pid;
    cacheVid.value = vid;
    edited.value = false;
    init = false;
    voucherRef.value?.loadAAE();
};

const zoomState = ref<"out" | "in">("out");
const voucherTitle = ref("记账凭证");
const voucherZoomChange = (zoom: "out" | "in") => {
    zoomState.value = zoom;
};

function getEditedState() {
    return edited.value;
}

defineExpose({ checkVoucher, getEditedState });
</script>

<style lang="less" scoped>
.edit-voucher-edit-content {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // height: calc(var(--voucher-min-height) + var(--title-height) + 60px);
    // height: ~"max(calc(var(--voucher-min-height) + var(--title-height) + 60px), calc(100vh - var(--content-padding-bottom)))";
    .title {
        flex-shrink: 0;
    }
    .edit-voucher-edit-top {
        padding: 16px 0;
        margin: 0 auto;
        text-align: left;
        box-sizing: border-box;
        flex-shrink: 0;
        &.in {
            width: 1000px;
        }
        &.out {
            width: 100%;
            padding: 16px 54px;
        }
    }
    .line {
        background-color: var(--title-split-line);
    }
}
</style>
