<template>
    <div class="advertisement-container" v-show="advertisementDialogShow">
        <img class="ad-img" :src="adUrl" @click="adClick()" />
        <img class="close-btn" src="@/assets/AdvertisementDialog/close.png" @click="advertisementDialogShow = false" />
    </div>
</template>
<style scoped lang="less">
.advertisement-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--shadow-color);
    z-index: 99999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .ad-img {
        max-height: 600px;
        min-height: 200px;
        cursor: pointer;
    }

    .close-btn {
        cursor: pointer;
        width: 30px;
        height: 30px;
        margin-top: 10px;
    }
}
</style>
<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { globalWindowOpen } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";
import { ref } from "vue";

const props = defineProps<{ adId: number; adUrl: string; adLink: string }>();
const advertisementDialogShow = ref(true);

const adClick = () => {
    // 点击广告链接为免费版链接
    let link = props.adLink;
    if(props.adLink.includes(window.location.host)){
        link = props.adLink + '?stay=true&appasid=' + getGlobalToken();
    }
    globalWindowOpen(link);
    request({
        url: "/api/Advertisement/UpdatePCClick",
        method: "POST",
        params: { adId: props.adId, typeId: 2020 },
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            console.log("记录广告点击成功");
        }
    });
};
</script>
