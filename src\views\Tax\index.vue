<template>
  <div class="content" :class="isErp ? 'erp-content' : ''">
    <ContentSlider :slots="slots" :currentSlot="currentSlot">
      <template #main>
        <div class="main-content">
          <div class="title">{{ "税务申报（" + areaName + "税务局）" }}</div>
          <div class="main-center">
            <div
              v-for="item in baseTypeList"
              :key="item.dataTaxType"
              :data-tax-type="item.dataTaxType"
              :class="'base-type ' + item.type"
              v-show="item.isShow"
              @click="goDeclaration">
              {{ item.label }}
              <img class="hot" v-if="item.dataTaxType === 'loginTax'" src="@/assets/Tax/hot.png" alt="" />
            </div>

            <div class="main-bottom-tip">
              <div>
                <span class="red">一键登录跳转税局地区：</span>
                <span>全国</span>
              </div>
              <div v-show="!isErp">
                <span class="red" color="#FD5055">个税申报地区：</span>
                <span>全国</span>
              </div>
              <div>
                <span class="red">财务报表申报地区：</span>
                <span>全国</span>
                <!-- <span>{{ areaList.join("、") }}</span>
                                <span>。其他地区将陆续开放，敬请期待！</span> -->
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #declaration>
        <div class="slot-content align-center">
          <div class="slot-title">{{ "税务申报（" + areaName + "税务局）" }}</div>
          <div class="slot-mini-content">
            <div class="title">{{ statementTaxTitle }}</div>
            <div class="statementTax-selector">
              <div class="statementTax-selector-help" v-if="operationManualUrl !== '' && !isHideBarcode">
                <a class="link" @click="globalWindowOpen(operationManualUrl)">点此查看操作手册</a>
              </div>
              <div class="statementTax-selector-title">
                <span>请确认您的报税选项，导出文件</span>
              </div>
              <div class="statementTax-selector-left">
                <div class="statementTax-selector-item">
                  <span class="highlight-red">*</span>
                  <span>报税类型：</span>
                </div>
                <div class="statementTax-selector-item">
                  <span class="highlight-red">*</span>
                  <span>报税所属期：</span>
                </div>
                <div v-show="showExtraChoose" class="statementTax-selector-item">
                  <span class="highlight-red">*</span>
                  <span>申报方式：</span>
                </div>
                <div class="statementTax-selector-item" v-show="isSupportAllTaxType">
                  <span class="highlight-red">*</span>
                  <span>适用税局：</span>
                </div>
                <div class="statementTax-selector-item" v-show="statementTaxDeclareWay">
                  <span class="highlight-red">*</span>
                  <span>报表样式：</span>
                </div>
              </div>
              <div class="statementTax-selector-right">
                <div class="statementTax-selector-item">
                  <el-select v-model="taxType" :teleported="false" :filterable="true" :filter-method="taxTypeFilterMethod">
                    <el-option v-for="item in showTaxTypeList" :label="item.label" :value="item.value" :key="item.value" />
                  </el-select>
                </div>
                <div class="statementTax-selector-item" v-show="taxType === '1'">
                  <el-select v-model="periodPid" :teleported="false" :filterable="true" :filter-method="periodFilterMethod">
                    <el-option :label="item.label" :value="item.pid" v-for="item in showPeriodList" :key="item.pid" />
                  </el-select>
                </div>
                <div class="statementTax-selector-item" v-show="taxType === '2'">
                  <el-select v-model="quarterPid" :teleported="false" :filterable="true" :filter-method="quarterFilterMethod">
                    <el-option :label="item.label" :value="item.pid" v-for="item in showQuarterList" :key="item.pid" />
                  </el-select>
                </div>
                <div class="statementTax-selector-item" v-show="taxType === '3'">
                  <el-select v-model="yearPid" :teleported="false" :filterable="true" :filter-method="yearFilterMethod">
                    <el-option :label="item.label" :value="item.pid" v-for="item in showYearList" :key="item.pid" />
                  </el-select>
                </div>
                <div class="statementTax-selector-item" v-show="showExtraChoose">
                  <el-select v-model="applyType" :teleported="false" @change="handleApplyTypeChange">
                    <el-option label="eTax@SH客户端申报" value="1" />
                    <el-option label="电子税务局申报" value="0" />
                  </el-select>
                </div>
                <div class="statementTax-selector-item tax-type" v-show="isSupportAllTaxType">
                  <el-select v-model="applyTaxType" :teleported="false" @change="handleApplyTaxTypeChange">
                    <el-option :label="areaName + '电子税务局'" value="0" />
                    <el-option label="全国统一规范电子税务局" value="1" />
                  </el-select>
                  <img src="@/assets/Scm/question.png" alt="适用税局" @click="isTaxApplyTypeShow = true" />
                </div>
                <div class="statementTax-selector-item statementTax-statementTaxDeclareWay" v-show="statementTaxDeclareWay">
                  <el-radio-group v-model="searchInfo.fileType">
                    <el-radio label="0" v-show="isShowCompanyStandard">与系统报表保持一致</el-radio>
                    <el-radio label="1" :style="!isShowCompanyStandard ? 'margin-top:6px' : ''">
                      <span>一般企业财务报表格式（适用于未执行新金融准则、</span>
                      <br />
                      <span>新收入准则和新租赁准则的企业）</span>
                    </el-radio>
                    <el-radio label="2">
                      <span>一般企业财务报表格式（适用于已执行新金融准则、</span>
                      <br />
                      <span>新收入准则和新租赁准则的企业）</span>
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <div class="statementTax-selector-buttons">
                <a class="solid-button" @click="handleConfirm">确定</a>
                <a class="ml-10 button" @click="() => (currentSlot = 'main')">返回</a>
              </div>
              <div class="statementTax-selector-tips">
                <div>
                  <span>登录国税局网站的报税界面，将文件导入即可报税</span>
                </div>
                <div>
                  <span>如有疑问，请</span>
                  <span class="consult">
                    咨询客服
                    <img v-if="isErp" src="@/assets/Invoice/fetchInvoice_erp.png" alt="" />
                    <img v-else-if="isProSystem" src="@/assets/Invoice/fetchInvoice_pro.png" alt="" />
                    <img v-else src="@/assets/Tax/yijianbaoshui.png" alt="" />
                  </span>
                  <span>，咨询时间：周一至周五 9:30 - 18:30</span>
                </div>
              </div>
              <div class="statementTax-selector-turn-to">
                <span class="link" @click="goDifferentTaxWebsite">
                  <span>点击前往</span>
                  <span>{{ areaName }}</span>
                  <span>电子税局</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </ContentSlider>
  </div>
  <el-dialog
    title="提示"
    width="440px"
    center
    :draggable="false"
    v-model="isTaxInfoCompleteTip"
    @close="onConfirmClick"
    class="custom-confirm">
    <div class="dialog-content">
      <div class="dialog-content-body">
        <div class="dialog-content-message">请先联系管理员完善税务信息，即可实现一键报税接入！</div>
      </div>
      <div class="buttons">
        <a class="button solid-button" @click="onConfirmClick">确定</a>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    title="提示"
    width="820px"
    top="10vh"
    center
    :draggable="false"
    v-model="isTaxApplyTypeShow"
    class="custom-confirm tax-import-tip">
    <div class="dialog-content">
      <div class="dialog-content-body">
        <div class="dialog-content-message" style="text-align: left">
          2024年，在全国各省市扩围上线全国统一规范电子税务局（简称“新电局”）。如果登录税局进行财务报表报送的页面如下图所示，表明该企业是新电局试点企业，适用税局请选择“全国统一规范电子税务局”导出文件。在新电局里报送模式选择“财报导入”导入文件哦~如果不是下图所示页面，表明该企业还不是新电局试点企业，选择各省市电子税务局即可。
        </div>
        <img src="@/assets/Tax/财报导入.png" alt="财报导入" />
      </div>
      <div class="buttons">
        <a class="button solid-button" @click="isTaxApplyTypeShow = false">知道了</a>
      </div>
    </div>
  </el-dialog>
  <LoginTaxBureauDialog
    ref="loginTaxBureauDialogRef"
    v-model:loginTaxBureauDialog="loginTaxBureauDialog"
    @successLogin="loginTax"></LoginTaxBureauDialog>
</template>
<script lang="ts">
export default {
  name: "TaxDeclaration",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted, onActivated, computed, watchEffect } from "vue";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getCookie } from "@/util/cookie";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { getUrlSearchParams, globalExport, globalWindowOpen, globalWindowOpenNoReferrer, globalWindowOpenPage } from "@/util/url";
import { changePeriodList, mapToQuartersAndYear,OpenApp } from "./utils";
import { checkPermission } from "@/util/permission";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useLoading } from "@/hooks/useLoading";

import { type IAccountSetInfo } from "@/api/accountSet";
import type { IPeriodData, ISelectList, IBasetype, ICheckTaxInfoIsCompleteBack } from "./types";
import TaxBureauLoginTableJson from "@/assets/Invoice/LoginTable.json";
import type { IInvoiceTaskValue } from "@/views/Invoice/types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import LoginTaxBureauDialog from "@/components/TaxBureauDialog/index.vue";
import { isLemonClient } from "@/util/lmClient";
import { erpCreateTab } from "@/util/erpUtils";
import { nextTick } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { commonFilterMethod } from "@/components/Select/utils";

const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const trialStatusStore = useTrialStatusStore();
const accountsetStore = useAccountSetStoreHook();

const loginTaxBureauDialog = ref(false);
const loginTaxBureauDialogRef = ref();
const areaList = ref<string[]>([]);
const _ = getGlobalLodash();
const isTaxInfoCompleteTip = ref(false);
const slots = ["main", "declaration"];
const currentSlot = ref("main");
const taxType = ref("1");
const periodList = ref<ISelectList[]>([]);
const quarterList = ref<ISelectList[]>([]);
const yearList = ref<ISelectList[]>([]);
const statementTaxTitle = ref("");
const areaName = ref("");
const statementTaxDeclareWay = ref(false);
const nationalTaxBureauUrl = ref("");
const operationManualUrl = ref("");
const quarterShow = ref(false);
const yearShow = ref(false);
const showExtraChoose = computed(() => {
  return areaName.value === "上海市";
});
const hideArea = ["北京市", "厦门市", "青岛市", "河南省", "宁夏回族自治区", "深圳市"];
const dynamicHideFirstStyle = computed(() => {
  if (showExtraChoose.value) return applyType.value === "0";
  if (hideArea.includes(areaName.value)) return true;
  const newTempArea = ["安徽省", "广东省", "辽宁省"];
  if (newTempArea.includes(areaName.value)) return applyTaxType.value === "1";
  return false;
});
const hideFirstStyle = computed(() => {
  const hideArea = ["江苏省", "云南省", "新疆维吾尔自治区"];
  return hideArea.includes(areaName.value);
});
const isSupportAllTaxType = ref(false);
const applyType = ref("0");
const applyTaxType = ref("0");
const isTaxApplyTypeShow = ref(false);

let canClick = false;
const searchInfo = reactive({
  pid: 0,
  fileType: "0",
});
const isErp = ref(window.isErp);
const isProSystem = ref(window.isProSystem);
const baseTypeList = ref<IBasetype[]>([
  { label: "一键登录税局", type: "opened", dataTaxType: "loginTax", isShow: true },
  { label: "个税申报", type: "opened", dataTaxType: "salaryTaxDeclare", isShow: !window.isErp },
  { label: "财务报表申报", type: "closed", dataTaxType: "statementTax", isShow: true },
  { label: "增值税申报", type: "closed", dataTaxType: "vatTax", isShow: true },
  { label: "消费税申报", type: "closed", dataTaxType: "conTax", isShow: true },
  { label: "附加税申报", type: "closed", dataTaxType: "surTax", isShow: true },
  { label: "企业所得税预缴申报", type: "closed", dataTaxType: "corIncomeTax", isShow: true },
  { label: "印花税申报", type: "closed", dataTaxType: "stampTax", isShow: true },
  { label: "房产税申报", type: "closed", dataTaxType: "buildingTax", isShow: true },
]);

const getPeriodsByASID = () => {
  const res: IPeriodData[] = useAccountPeriodStore().periodList;
  // 判断季报和年报是否显示
  quarterShow.value = res.some((item) => item.sn % 3 == 0);
  yearShow.value = res.some((item) => item.sn % 12 == 0);
  periodList.value = changePeriodList(res);
  periodPid.value = periodList.value[0].pid;
  searchInfo.pid = periodList.value[0].pid;
  const { years, quarters } = mapToQuartersAndYear(res);
  quarterList.value = quarters;
  yearList.value = years;
};
const getSupportDeclareAreas = () => {
  request({ url: window.jtaxHost + "/api/TaxDeclare/GetSupportDeclareAreas", method: "post" }).then((res: IResponseModel<string[]>) => {
    if (res.state == 1000) areaList.value = res.data;
  });
};

const issupportCompanyStandard = ref(true); //与系统报表保持一致是否显示
const isShowCompanyStandard = computed(() => {
  return showExtraChoose.value ? applyType.value !== "0" : issupportCompanyStandard.value;
});

watch(
  () => isShowCompanyStandard.value,
  (val) => {
    searchInfo.fileType = statementTaxDeclareWay.value && !val ? "1" : searchInfo.fileType;
  }
);

let isCompleteTaxInfo = false;
const handleInit = () => {
  getSupportDeclareAreas();
  // 报表样式的显示与隐藏（仅2007会计准则会显示）
  const accountingStandard = accountsetStore.accountSet?.accountingStandard ?? 0;
  const subAccountingStandard = accountsetStore.accountSet?.subAccountingStandard ?? 0;
  statementTaxDeclareWay.value = accountingStandard === 2 && subAccountingStandard === 0;

  request({ url: window.jtaxHost + "/api/TaxDeclare/GetTaxInfo", method: "post" })
    .then((res: IResponseModel<ICheckTaxInfoIsCompleteBack>) => {
      if (res.state != 1000) {
        ElNotify({ type: "warning", message: res.msg || "请求失败" });
        return;
      }
      request({ url: "/api/PermissionsRole/GetRoleIdList", method: "post" }).then((r: IResponseModel<number[]>) => {
        const len = r.data.filter(function (p: number) {
          return p + "" == "*********";
        }).length;
        const result = res.data;
        areaName.value = result.taxAreaName ?? "";
        issupportCompanyStandard.value = result.issupportCompanyStandard;
        // if (hideArea.includes(areaName.value) && statementTaxDeclareWay.value) {
        //     searchInfo.fileType === "0" && (searchInfo.fileType = "1");
        // }
        isSupportAllTaxType.value = !!result.issupportAllTaxType && areaName.value !== "上海市";

        if (areaName.value === "上海市") {
          applyType.value = "1";
        }
        if (result.isTaxInfoComplete !== true) {
          length.value = len;
          isTaxInfoCompleteTip.value = true;
        } else {
          isCompleteTaxInfo = true;
          const taxDeclarationTypes = result.taxDeclarationTypes.map((item) => item + "") || [];
          judjeItemShow(taxDeclarationTypes);
          getPeriodsByASID();
          nationalTaxBureauUrl.value = result.nationalTaxBureauUrl;
          operationManualUrl.value = result.operationManualUrl;
        }
      });
    })
    .catch(() => {
      ElNotify({ type: "warning", message: "获取信息失败，请刷新页面后重试！" });
    })
    .finally(() => {
      canClick = true;
    });
};
handleInit();

const isAppOpened = ref(false);
function openUriWithTimeoutHack(info: IAccountSetInfo, website: string) {
  request({
    url: `/api/TaxBureau/SetUserIdentifier?taxArea=${info.taxadId}&personAccount=${info.taxBureauPersonAccount}&taxNumber=${info.taxNumberS}`,
    method: "post",
  }).then((res: any) => {
    useLoading().quitLoading();
    // let isAppOpened = false;
    if(res.data.code !== 200) {
      ElNotify({ type: "warning", message: res.data.message || "请求失败" });
      return;
    }
    const openApp = new Promise((resolve, reject) => {
      const protocol = `nmyswzs://?USCC=${info.taxNumberS}&AreaCode=${
      TaxBureauLoginTableJson[String(info.taxadId)][String(info.taxBureauLoginType)].NmyAreaCode
    }&Identifier=${res.data.message}`;
      // 替换可能影响唤起应用读取query参数的符号
      const encodedUrl = encodeURIComponent(website);

      // 拼接protocol和encodedUrl
      const newProtocol = protocol + "&url=" + encodedUrl;
      const iframe = document.createElement("iframe");
      iframe.style.display = "none";
      iframe.src = newProtocol;
      document.body.appendChild(iframe);
      const start = Date.now();

      const timeout = setTimeout(() => {
        if (Date.now() - start < 2500) {
          return;
        }
        // 如果应用没有在一定时间内被唤起，就认为唤起失败，调用 reject 函数
        reject();
      }, 2500);

      window.onblur = function () {
        clearTimeout(timeout);
        window.onblur = null;
        isAppOpened.value = true;
        // 如果窗口失去焦点，就认为应用已经被唤起，调用 resolve 函数
        resolve(true);
      };
      window.onfocus = function () {
        isAppOpened.value = false;
        window.onfocus = null;
        // reject();
      };
    });

    async function tryOpenApp() {
        try {
          await openApp;
        } catch(e) {
          if(isAppOpened.value) return
          await OpenApp().then((r) => {
            if (r) {
              window.location.href = window.downLoadHost + (window.downLoadHost.includes('test') ? 'AutoUpdateDownload/':'taxclient/installer/') + encodeURIComponent('柠檬云税务助手安装程序.exe');
            }
          });
        }
    }
    tryOpenApp();
  });
}

function loginTax() {
  console.log("loginTax")
  useLoading().enterLoading("正在处理中，请稍候...");
  if(loginTaxBureauDialog.value) loginTaxBureauDialog.value = false;
  useAccountSetStore()
    .getAccountSetInfo(true)
    .then((info) => {
      const data = {
        taxPayerName: info.taxpayerName,
        taxPayerNumber: info.taxNumberS,
        taxType: info.taxType,
        taxArea: info.taxadId,
        taxLoginType: info.taxBureauLoginType,
        taxAccount: info.taxBureauAccount,
        taxPayerPassword: info.taxpayerPassword,
        personPositionType: info.taxBureauPersonPositionType,
        personIdentityNo: info.taxBureauPersonIdentityNo,
        personAccount: info.taxBureauPersonAccount,
        personPassword: info.taxBureauPersonPassword,
        personPhone: info.taxBureauPersonPhone,
        agentTaxNumber: info.taxBureauAgentTaxNumber,
      };
      if (info.taxBureauPersonAccount === "" || info.taxBureauPersonPassword === "" || !info.taxadId) {
        useLoading().quitLoading();
        loginTaxBureauDialog.value = true;
        return;
      }
      request({
        url: `/api/TaxBureau/LoginState?isAuthorized=${info.taxBureauAuthorized}&taskSource=103`,
        data,
        method: "post",
      }).then((res: IResponseModel<IInvoiceTaskValue>) => {
        if (res.data.result === "firstLogin") {
          useLoading().quitLoading();
          loginTaxBureauDialog.value = true;
        } else if (res.data.result === "success") {
          const website = isLoginUrl.value ? nationalTaxBureauUrl.value + "loginb/" : nationalTaxBureauUrl.value + "sbzx/view/sdsfsgjssb/#/yyzx/cwbbbs";
          openUriWithTimeoutHack(info, website);
        } else if (
          ["needUpdate", "notSureAndInfo", "loginFail"].includes(res.data.result) ||
          (!info.taxBureauAuthorized && res.data.result === "error")
        ) {
          useLoading().quitLoading();
          loginTaxBureauDialog.value = true;
          loginTaxBureauDialogRef.value.changeOperationalIndex(1);
        } else if (info.taxBureauAuthorized) {
          useLoading().quitLoading();
          loginTaxBureauDialog.value = true;
          loginTaxBureauDialogRef.value.changeOperationalIndex(3);
        } else {
          useLoading().quitLoading();
          ElNotify({ type: "warning", message: res.msg || "请求失败" });
        }
      }).catch(() => {
        useLoading().quitLoading();
        ElNotify({ type: "warning", message: "请求失败" });
      });
    });
}

const goDeclaration = async (e: Event) => {
  // 初始化加载未完成不允许点击
  if (!canClick) return;
  if ((e.target as HTMLDivElement).dataset.taxType === "loginTax") {
    isLoginUrl.value = true;
    loginTax()
    return;
  }
  if ((e.target as HTMLDivElement).dataset.taxType === "salaryTaxDeclare") {
    globalWindowOpenPage("/Salary/SalaryManage?autoDeclare=1&from=tax&r=" + Math.random(), "工资管理");
    return;
  }
  const canGoDeclaration = (e.target as HTMLDivElement).classList.contains("opened");
  if (!canGoDeclaration) {
    ElNotify({ type: "warning", message: "功能尚未开通，敬请期待！" });
    return;
  }
  const title = (e.target as HTMLDivElement).innerText;
  statementTaxTitle.value = title;
  currentSlot.value = "declaration";
  // loginProcess.value = false;
  // isAppOpened.value = false;
};
const judjeItemShow = (taxDeclarationTypes: string[]) => {
  if (!window.isProSystem && !window.isErp) {
    //账套已过期隐藏个税申报
    if (trialStatusStore.isTrial && trialStatusStore.isExpired) {
      const index = baseTypeList.value.findIndex((baseItem) => baseItem.dataTaxType === "salaryTaxDeclare");
      baseTypeList.value.splice(index, 1, { ...baseTypeList.value[index], isShow: false });
    }
  }
  taxDeclarationTypes.forEach((item) => {
    // if (item === "file") {
    if (item == "0") {
      const index = baseTypeList.value.findIndex((baseItem) => baseItem.dataTaxType === "statementTax");
      baseTypeList.value.splice(index, 1, { ...baseTypeList.value[index], type: "opened" });
    }
    // else {
    //     const index = baseTypeList.value.findIndex((baseItem) => baseItem.dataTaxType === item);
    //     baseTypeList.value.splice(index, 1, { ...baseTypeList.value[index], type: "opened" });
    // }
  });
};
const goToAccountSets = () => {
  if (window.isErp) {
    erpCreateTab("/ArgsSetting?taxmanage=1", "系统参数");
  } else {
    globalWindowOpenPage("/Settings/AccountSets?isFromTax=true&r=" + Math.random(), "账套");
  }
};

const isLoginUrl = ref(true)
const goDifferentTaxWebsite = () => {
  // globalWindowOpenNoReferrer(nationalTaxBureauUrl.value);
  isLoginUrl.value = false;
  loginTax();
};
let isExport = false;
const handleConfirmFn = () => {
  if (isExport) return;
  isExport = true;
  const selectTaxType = showExtraChoose.value ? (applyType.value === "1" ? "0" : "1") : applyTaxType.value;
  new Promise<void>((resolve) => {
    if (isLemonClient()) {
      //作此修改是由于客户端先请求再跳转，会导致无法弹出页面
      resolve();
    } else {
      const requestParams = {
        selectTaxType,
        taxType: taxType.value,
        fileType: searchInfo.fileType,
      };
      request({
        url: window.jtaxHost + "/api/TaxDeclare/CheckCanExportStatement?" + getUrlSearchParams(requestParams),
        method: "post",
      }).then((res: IResponseModel<boolean>) => {
        if (res.state != 1000) {
          ElNotify({ type: "warning", message: res.msg ?? "请求失败" });
          isExport = false;
          return;
        }
        if (res.data) {
          resolve();
        } else {
          isExport = false;
          ElConfirm("该报表样式暂不支持导出，请联系在线客服~", true, () => {}, "提示", undefined, false, 999, true, true, false, true);
        }
      });
    }
  }).then(() => {
    searchInfo.pid = taxType.value === "1" ? periodPid.value : taxType.value === "2" ? quarterPid.value : yearPid.value;
    const params = {
      pId: searchInfo.pid,
      taxType: taxType.value,
      balanceIsClass: JSON.parse(window.localStorage.getItem("classificationSwitch") || "false"),
      cashFlowUseAssist: getCookie("calmethod") === "2",
      fileType: searchInfo.fileType,
      async: !isLemonClient(),
      appasid: getGlobalToken(),
      extraChoose: applyType.value,
      selectTaxType,
    };
    const timer = setTimeout(() => {
      isExport = false;
      clearTimeout(timer);
    }, 300);
    globalExport(window.jtaxHost + "/api/TaxDeclare/Export?" + getUrlSearchParams(params));
  });
};

const handleConfirm = _.debounce(handleConfirmFn);

const yearPid = ref(-1);
const quarterPid = ref(-1);
const periodPid = ref(-1);
watch(taxType, (val) => {
  if (val === "1") {
    if (periodPid.value === -1) periodPid.value = periodList.value[0].pid;
    searchInfo.pid = periodPid.value;
  } else if (val === "2") {
    if (quarterPid.value === -1) quarterPid.value = quarterList.value[0].pid;
    searchInfo.pid = quarterPid.value;
  } else if (val === "3") {
    if (yearPid.value === -1) yearPid.value = yearList.value[0].pid;
    searchInfo.pid = yearPid.value;
  }
});

const length = ref(0);
const onConfirmClick = () => {
  isTaxInfoCompleteTip.value = false;
  nextTick(() => {
    if (checkPermission(["accountset-canedit"]) || (window.isErp && length.value > 0)) {
      goToAccountSets();
    } else {
      if (window.isErp) {
        ElNotify({
          type: "warning",
          message: "请登录管理员账号，在系统设置-系统参数中设置好财税参数再进行纳税申报。",
        });
      } else {
        ElNotify({ type: "warning", message: "请联系账套管理员，在设置-账套中设置好税务信息再进行个税申报。" });
      }
    }
  });
};

const refreshByModifyAccountSet = () => {
  if (useRouterArrayStoreHook().routerArray.find((item) => item.title.includes("税务申报"))) {
    handleInit();
  }
};

onMounted(() => {
  window.addEventListener("modifyAccountSet", refreshByModifyAccountSet);
});
onUnmounted(() => {
  window.removeEventListener("modifyAccountSet", refreshByModifyAccountSet);
  canClick = false;
});

onActivated(() => {
  if (!canClick || isCompleteTaxInfo) return;
  request({ url: window.jtaxHost + "/api/TaxDeclare/GetTaxInfo", method: "post" }).then(
    (res: IResponseModel<ICheckTaxInfoIsCompleteBack>) => {
      if (res.state != 1000) return;
      isTaxInfoCompleteTip.value = res.data.isTaxInfoComplete !== true;
      issupportCompanyStandard.value = res.data.issupportCompanyStandard;
    }
  );
});
function handleApplyTypeChange(selectType: string) {
  if (selectType === "0" && dynamicHideFirstStyle.value && statementTaxDeclareWay.value) {
    searchInfo.fileType = "1";
  }
}
function handleApplyTaxTypeChange(selectTaxType: string) {
  if (selectTaxType === "1" && dynamicHideFirstStyle.value && statementTaxDeclareWay.value) {
    searchInfo.fileType = "1";
  }
}

const taxTypeList = ref<any[]>([
  {
    value: "1",
    label: "月报"
}]);
let showTaxTypeList = ref<any[]>(JSON.parse(JSON.stringify(taxTypeList.value)));
watch(
  () => quarterShow.value,
  () => {
    let index = taxTypeList.value.findIndex((item) => item.label === "季报");
    if (quarterShow.value) {
      index === -1 && taxTypeList.value.push({ value: "2", label: "季报" });
    } else {
      index !== -1 && taxTypeList.value.splice(index, 1);
    }
    showTaxTypeList.value = JSON.parse(JSON.stringify(taxTypeList.value));
  }
);
watch(
  () => yearShow.value,
  () => {
    let index = taxTypeList.value.findIndex((item) => item.label === "年报");
    if (quarterShow.value) {
      index === -1 && taxTypeList.value.push({ value: "3", label: "年报" });
    } else {
      index !== -1 && taxTypeList.value.splice(index, 1);
    }
    showTaxTypeList.value = JSON.parse(JSON.stringify(taxTypeList.value));
  }
);
const showPeriodList = ref<ISelectList[]>([]);
const showQuarterList = ref<ISelectList[]>([]);
const showYearList = ref<ISelectList[]>([]);
watchEffect(() => {
  showPeriodList.value = JSON.parse(JSON.stringify(periodList.value));
  showQuarterList.value = JSON.parse(JSON.stringify(quarterList.value));
  showYearList.value = JSON.parse(JSON.stringify(yearList.value));
});
function taxTypeFilterMethod(value: string) {
  showTaxTypeList.value = commonFilterMethod(value, taxTypeList.value, "label");
}
function periodFilterMethod(value: string) {
  showPeriodList.value = commonFilterMethod(value, periodList.value, "label");
}
function quarterFilterMethod(value: string) {
  showQuarterList.value = commonFilterMethod(value, quarterList.value, "label");
}
function yearFilterMethod(value: string) {
  showYearList.value = commonFilterMethod(value, yearList.value, "label");
}
</script>

<style lang="less" scoped>
@import "@/style/Tax/TaxDeclaration.less";
@import "@/style/SelfAdaption.less";

.content {
  .slot-content {
    .slot-mini-content {
      border: 1px solid var(--slot-title-color);
      margin-top: 32px;
    }
  }
}

div.statementTax-selector-help {
  position: absolute;
  right: 50px;
  top: 20px;

  &::before {
    width: 16px;
    height: 16px;
    content: "";
    position: absolute;
    top: 5px;
    left: -18px;
    background-image: url("@/assets/Icons/help.png");
  }
}
</style>
<style lang="less">
.el-dialog.custom-confirm.tax-import-tip {
  .el-dialog__header {
    border-bottom: none;
  }

  img {
    width: 680px;
    height: 325px;
    margin-top: 20px;
  }

  .dialog-content-body {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .buttons {
    border-top: none;
  }
}
.reverse-buttons {
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
  a {
    margin-right: 20px;
  }
}
</style>
