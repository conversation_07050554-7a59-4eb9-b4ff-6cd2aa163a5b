@import "../SelfAdaption.less";

.tabs {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
    width: 50000px;
    border-style: solid;
    border-width: 0 0 1px 0;
    background-color: rgb(242, 242, 242);
    border-bottom: 0px;
    padding-left: 0px;
    height: 39px;
    display: flex;
    & li {
        border-radius: initial;
        height: var(--tab-height);
        background-repeat: no-repeat;
        margin-top: 0px;
        margin-bottom: 0px;
        margin-left: 0px;
        margin-right: 0px !important;
        & a {
            padding: 0px 10px 0px 0px;
            text-align: center;
            font-weight: normal;
            color: var(--font-color);
            background: none !important;
            height: var(--tab-height) !important;
            text-decoration: none;
        }
    }
}
.tb-hr {
    padding: 20px 20px 15px 20px;
    color: var(--font-color);
    font-size: var(--h3);
    line-height: 22px;
    font-weight: bold;
    text-align: left;
    .edit-tb {
        #txtceatetime {
            color: var(--weaker-font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            font-weight: normal;
        }
    }

    .tb-title {
        text-align: left;
        line-height: 20px;
        font-size: 14px;
        &.required-title::before {
            content: "*";
            color: red;
        }
    }
    .tb-field {
        width: 160px;
        padding-top: 5px;
        padding-bottom: 5px;
        text-align: left;
    }
}

.txt {
    &.first-item {
        padding-top: 60px;
    }
}

.content {
    & .buttons {
        margin-top: 20px;
        margin-bottom: 40px;
        text-align: center;
    }
}
// 无形资产功能更新弹窗
.functionUpdate {
    .function-content {
        padding: 30px 80px;
        text-align: center;
        background: linear-gradient(
            to right bottom,
            rgba(0, 255, 161, 0.3),
            rgba(250, 239, 126, 0.2),
            rgba(255, 255, 255, 0.3),
            rgba(255, 245, 232, 0.3)
        );
        &.erpfunction-content{
            background: linear-gradient(
                to right bottom,
                rgba(136, 203, 234, 0.1),
                rgba(255, 255, 255,1),
            );
        }
    }
    
    .function-title {
        padding: 18px 0px 40px;
        font-size: 28px;
        line-height: 40px;
        font-weight: 600;
    }
    .function-tips {
        font-size: 21px;
        text-align: left;
    }
    .function-message{
        margin-top: 10px;
        text-align: left;
    }
}
//无形资产启用日期弹窗
.start-dialog {
    .start-content {
        padding: 50px 60px 0px;
        .start-date {
            padding-left: 60px;
            padding-bottom: 32px;
        }
        .start-tips {
            font-size: 14px;
            line-height: 24px;
            padding-bottom: 20px;
        }
        .start-confirm-tip{
            display: flex;
            padding-bottom: 20px;
            color: #666;
            font-size: 12px;
            img {
                width: 16px;
                height: 16px;
                padding-top: 2px;
                padding-right: 2px;
            }
        }
    }
    .start-buttons {
        border-top: 1px solid var(--border-color);
    }
}

:deep(.batchDialog) {
    .batchDialog-warning {
        text-align: center;
        display: flex;
        align-items: center;
        line-height: 26px;
        width: auto;
        white-space: nowrap;
        padding: 10px 0 10px 72px;
        &::before {
            content: " ";
            background-image: url("@/assets/Icons/warn.png");
            background-repeat: no-repeat;
            background-size: 100%;
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
    }
}
