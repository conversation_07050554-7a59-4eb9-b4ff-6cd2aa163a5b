import { getServiceId } from "@/util/proUtils";

export const isLemonClient = () => {
    return /LemonClient\(Acc\)\/([\d\.]*)/.test(window.navigator.userAgent);
};

export const getClientVersion = () => {
    const match = window.navigator.userAgent.match(/LemonClient\(Acc\)\/([\d\.]*)/);
    if (match) {
        return Number(match[1].replace(/\./g, ""));
    } else {
        return 0;
    }
};

const getLmClient = () => {
    return (window as any).lmClient as ILmClient;
};

interface ILmClient {
    showMenuMask: (action: "show" | "close") => void;
    loadTopAccounstInfo: () => void;
    loadTopPeriodInfo: () => void;
    loadTopUserName: () => void;
    loadMenu: () => void;
    createNewTab: (title: string, href: string) => void;
    setHref: (href: string) => void;
    openWindow: (href: string) => void;
    openPostWindow: (href: string, params: any, title: string) => void;
    closeTab: (href: string, title: string) => void;
    showBuyDialog: (msg: string, onClose: () => void, highlightmodule: string) => void;
    setSelectedAccount: (asId: number, serviceId?: string) => void;
    init: () => void;
    upgradeToPro: () => void;
    toPurchase: () => void;
    closeNormalDialog: () => void;
    showNormalDialog: (href: string, onClose: () => void) => void;
    pickOneOrCreate: () => void;
    backToRelogin: () => void;
    hideTopBar: (hide: boolean) => void;
}

class LmClient implements ILmClient {
    showMenuMask = (action: "show" | "close") => {
        getLmClient().showMenuMask(action);
    };
    loadTopAccounstInfo = () => {
        getLmClient().loadTopAccounstInfo();
    };
    loadTopPeriodInfo = () => {
        if (getClientVersion() >= 504) {
            getLmClient().loadTopPeriodInfo();
        } else {
            getLmClient().loadTopAccounstInfo();
        }
    };
    loadTopUserName = () => {
        if (getClientVersion() >= 504) {
            getLmClient().loadTopUserName();
        } else {
            getLmClient().loadTopAccounstInfo();
        }
    };
    loadMenu = () => {
        getLmClient().loadMenu();
    };
    createNewTab = (title: string, href: string) => {
        getLmClient().createNewTab(title, href);
    };
    setHref = (href: string) => {
        getLmClient().setHref(href);
    };
    openWindow = (href: string) => {
        getLmClient().openWindow(href);
    };
    openPostWindow = (href: string, params: any, title: string) => {
        getLmClient().openPostWindow(href, params, title);
    };
    closeTab = (title: string) => {
        getLmClient().closeTab(window.location.href, title);
    };
    showBuyDialog = (msg: string, onClose: () => void, highlightmodule: string) => {
        getLmClient().showBuyDialog(msg, onClose, highlightmodule);
    };
    setSelectedAccount = (asId: number, serviceId: string = "") => {
        if (getClientVersion() >= 616) {
            getLmClient().setSelectedAccount(asId, getServiceId() || serviceId);
        } else {
            getLmClient().setSelectedAccount(asId);
        }
    };
    init = () => {
        getLmClient().init();
    };
    upgradeToPro = () => {
        getLmClient().upgradeToPro();
    };
    toPurchase = () => {
        getLmClient().toPurchase();
    };
    closeNormalDialog = () => {
        getLmClient().closeNormalDialog();
    };
    showNormalDialog = (href: string, onClose?: () => void) => {
        getLmClient().showNormalDialog(href, onClose || (() => {}));
    };
    pickOneOrCreate = () => {
        getLmClient().pickOneOrCreate();
    };
    backToRelogin = () => {
        getLmClient().backToRelogin();
    };
    hideTopBar = (hide: boolean) => {
        getLmClient().hideTopBar(hide);
    };
}

export const getLemonClient = () => {
    return new LmClient();
};
