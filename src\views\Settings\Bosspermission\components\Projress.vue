<template>
    <div class="progress">
        <div v-for="index in stages.length * 2 - 1" :key="index" :class="index % 2 !== 0 ? 'small progressCombine' : 'progressCombine'">
            <div class="outerCircle" v-if="index % 2 !== 0">
                <span>{{ (index + 1) / 2 }}</span>
            </div>
            <p class="colorBack" v-if="index % 2 !== 0">{{ stages[(index + 1) / 2 - 1] }}</p>
            <div class="outLine" v-if="index % 2 === 0"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
const stages = ["授权看账", "关注公众号", "推送给老板", "成功绑定账号"];
const setSelected = (num: number) => {
    const progressCombines = document.querySelectorAll(".progressCombine");
    progressCombines.forEach((item, index) => {
        item.querySelector(".outerCircle")?.classList.contains("choose") && item.querySelector(".outerCircle")?.classList.remove("choose");
        if (index <= num) {
            item.querySelector(".outerCircle")?.classList.add("colorSelected");
            item.querySelector(".colorBack")?.classList.add("colorSelected");
            item.querySelector(".outLine")?.classList.add("colorSelected");
            if (index === num) item.querySelector(".outerCircle")?.classList.add("choose");
        } else {
            item.querySelector(".outerCircle")?.classList.contains("colorSelected") &&
                item.querySelector(".outerCircle")?.classList.remove("colorSelected");
            item.querySelector(".colorBack")?.classList.contains("colorSelected") &&
                item.querySelector(".colorBack")?.classList.remove("colorSelected");
            item.querySelector(".outLine")?.classList.contains("colorSelected") &&
                item.querySelector(".outLine")?.classList.remove("colorSelected");
        }
    });
};
defineExpose({ setSelected });
</script>

<style lang="less" scoped>
.progress {
    background-color: var(--white);
    .small {
        width: 36px !important;
    }
    .progressCombine {
        height: 80px;
        width: auto;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        .outLine {
            width: 180px;
            height: 8px;
            border-radius: 4px;
            margin-top: 35px;
            background-color: #cdcdcd;
            &.colorSelected {
                background-color: var(--main-color);
            }
        }
        p {
            height: 16px;
            margin: 12px 0;
            white-space: nowrap;
            font-size: 12px;
            &.colorSelected {
                color: var(--main-color);
            }
        }
        .outerCircle {
            display: flex;
            height: 36px;
            border-radius: 50%;
            width: 36px;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            border: 2px solid #f0f0f0;
            color: #cdcdcd;
            &.colorSelected {
                color: #f0f0f0;
                background-color: var(--main-color);
            }
            &.choose {
                background-color: transparent;
                color: var(--main-color);
                border-color: var(--main-color);
            }

            span {
                font-size: 18px;
            }
        }
        .colorBack {
            color: #cdcdcd;
        }
    }
}
</style>
