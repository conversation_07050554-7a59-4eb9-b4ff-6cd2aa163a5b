<template>
    <div class="content">
        <el-tabs class="container" v-model="tabName" @tab-click="handleTabClick">
            <el-tab-pane :name="item.name" v-for="(item, index) in tabPanes" :key="index">
                <template #label>
                    <span>{{ item.label }}</span>
                    <el-popover placement="top" :width="300" trigger="hover" :content="item.explain">
                        <template #reference>
                            <span class="explain-icon"></span>
                        </template>
                    </el-popover>
                </template>
                <CommonType
                    v-if="isLoaded[item.name]"
                    ref="commonTypeRef"
                    :requestInitAsubInfo="requestInitAsubInfo"
                    :scmAsid="scmAsid"
                    :scmProductType="scmProductType"
                    :defaultAsubList="defaultAsubList"
                    :typeCode="item.name"
                    :getDefaultAsubRelation="getDefaultAsubRelation"
                    :refreshOtherTabRequestState="refreshOtherTabRequestState"
                ></CommonType>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts">
export default {
    name: "AsubRelationSettings",
};
</script>
<script setup lang="ts">
import { ref, reactive, onMounted, provide, onActivated, computed, watch } from "vue";
import { type IResponseModel, request } from "@/util/service";
import { DefaultAsubClass, type AsubRelationType } from "../AsubRelationSettings1/utils";
import { getUrlSearchParams, tryClearCustomUrlParams } from "@/util/url";
import { useRoute } from "vue-router";
import { asubRelationInfo, asubRelationActiveTabKey } from "../AsubRelationSettings1/utils";
import { tabPanes } from "./utils";
import { expansionTreeList, getAsubTree, type ISubjectTree } from "@/components/Picker/SubjectPicker/util";
import { initAsubTreeKey } from "@/components/Picker/SubjectPicker/symbols";
import { useScmInfoStore } from "@/store/modules/scm";
import { resetErpVoucherHistory } from "../utils";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";

import type { IAsubRelationType } from "./types";

import CommonType from "./components/CommonType.vue";

const route = useRoute();
const tabName = ref("commodity");
const isLoaded = reactive<IAsubRelationType>({
    commodity: false,
    customer: false,
    vendor: false,
    employee: false,
    account: false,
    income: false,
    expense: false,
});
const isRequest = reactive<IAsubRelationType>({ ...isLoaded });
function refreshOtherTabRequestState(key: keyof typeof isRequest) {
    for (const item in isRequest) {
        const itemKey = item as keyof typeof isRequest;
        if (item !== key) isRequest[itemKey] = false;
    }
}
function handleTabClick(tab: any) {
    const name = tab.props.name as keyof typeof isLoaded;
    if (!isLoaded[name]) {
        isLoaded[name] = true;
        isRequest[name] = true;
    }
    if (isLoaded[name] && !isRequest[name]) {
        const index = tabPanes.findIndex((item) => item.name === name);
        commonTypeRef.value[index]?.getTableData();
    }
}
const scmAsid = ref(0);
const scmProductType = ref(0);
const defaultAsubList = ref<Record<AsubRelationType, string>>(new DefaultAsubClass());
async function getDefaultAsubRelation() {
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    await request({ url: "/api/AsubRelation/GetDefaultList?" + getUrlSearchParams(params), method: "post" }).then(
        (res: IResponseModel<Record<AsubRelationType, number>>) => {
            const asubList = new DefaultAsubClass();
            for (const keyName in res.data) {
                const key = keyName as unknown as AsubRelationType;
                asubList[key] !== undefined && (asubList[key] = res.data[key].toString());
            }
            defaultAsubList.value = asubList;
        }
    );
}
function trySetInfoFromRoute() {
    const active = route.query[asubRelationActiveTabKey] as unknown as keyof typeof isLoaded;
    const tabs = asubRelationInfo.map((item) => item.typeCode);
    if (!active || !tabs.includes(active)) {
        isLoaded.commodity = true;
        isRequest.commodity = true;
        return;
    }
    isLoaded[active] = true;
    isRequest[active] = true;
    tabName.value = active;
    tryClearCustomUrlParams(route);
}
const asubTreeData = ref<Array<ISubjectTree>>([]);
function requestInitAsubInfo() {
    getAsubTree(1).then((res: IResponseModel<string>) => {
        if (res.state !== 1000 || res.data === "") return;
        asubTreeData.value = expansionTreeList(JSON.parse(res.data));
    });
}
const commonTypeRef = ref();
provide(initAsubTreeKey, asubTreeData);
const scmInfoStore = useScmInfoStore();
let hasMounted = false;
onMounted(async () => {
    trySetInfoFromRoute();
    requestInitAsubInfo();
    await scmInfoStore.handleGetScmRelation().then(() => {
        scmAsid.value = scmInfoStore.scmAsid;
        scmProductType.value = scmInfoStore.scmProductType;
    });
    await resetErpVoucherHistory();
    commonTypeRef.value[0]?.getTableData();
    getDefaultAsubRelation();
    hasMounted = true;
});
onActivated(() => {
    if (!hasMounted) return;
    trySetInfoFromRoute();
});
const accountSubjectStore = useAccountSubjectStore();
const subject = computed(() => accountSubjectStore.accountSubjectList);
watch(subject, () => {
    getDefaultAsubRelation();
    requestInitAsubInfo();
    const index = tabPanes.findIndex((item) => item.name === tabName.value);
    if (index !== -1) {
        const key = tabPanes[index].name;
        commonTypeRef.value[index]?.getTableData();
        refreshOtherTabRequestState(key);
    }
});
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
:deep(.el-table__empty-block) {
    position: absolute;
    top: 0;
}
.content {
    :deep(.subject-dialog-container.erp) {
        .subject-dialog-title {
            margin: 0 -20px 8px;
            padding: 12px 20px 20px;
            font-size: var(--h3);
            border-bottom: 1px solid var(--border-color);
            text-align: left;
        }
        .buttons {
            display: flex;
            justify-content: flex-end;
            margin: 20px -12px 0;
            padding: 16px 20px 4px;
            border-top: 1px solid var(--border-color);
        }
    }
    :deep(.el-tabs.container) {
        .main-tool-left {
            .el-select-v2__suffix {
                right: 28px;
            }
        }
        & > .el-tabs__header {
            .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item {
                height: auto;
                padding: 13px 30px;
            }
            .explain-icon {
                display: none;
                height: 14px;
                width: 14px;
                background: url("@/assets/Icons/question.png") no-repeat;
                background-size: 100% 100%;
                position: absolute;
                right: 14px;
                top: 14px;
                &:hover {
                    cursor: pointer;
                    background-image: url("@/assets/Icons/question-erp.png");
                }
            }
            .el-tabs__item.is-active {
                .explain-icon {
                    display: block;
                }
            }
        }
    }
    :deep(.asub-img) {
        top: 2px;
    }
}
</style>
