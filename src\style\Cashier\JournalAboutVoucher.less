.about-voucehr-content {
    .buttons.bt {
        border-top: 1px solid var(--border-color);
    }
    .about-voucehr-main {
        padding: 10px;
        .title-cell-item,
        .table-cell-item {
            box-sizing: border-box;
            &.date,
            &.create,
            &.check,
            &.operate {
                width: 75px;
            }
            &.num {
                width: 70px;
            }
            &.debit,
            &.credit {
                width: 80px;
            }
            &.desc {
                width: 140px;
            }
            &.asub {
                width: 130px;
            }
        }
        .table-title {
            border: 1px solid var(--border-color);
            display: inline-flex;
            align-items: center;
            background-color: rgb(232, 232, 232);
            height: 40px;
            border-bottom: none;
            .title-cell-item {
                height: 100%;
                line-height: 40px;
                border-right: 1px solid var(--border-color);
                padding-left: 5px;
                &.operate {
                    border-right: none;
                }
            }
        }
        .table-item {
            border-bottom: 1px solid var(--border-color);
            border-top: none;
            display: flex;
            align-items: center;
            font-size: 12px;
            .pl-5 {
                padding-left: 5px;
            }
            .table-cell-item {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                border-right: 1px solid var(--border-color);
                overflow: hidden;
                &.operate {
                    border-right: none;
                    flex-direction: row;
                    align-items: center;
                    justify-content: flex-start;
                    span.link {
                        color: var(--main-color);
                        font-size: 12px;
                    }
                }
                & > div {
                    box-sizing: border-box;
                    height: 37px;
                    line-height: 37px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 100%;
                    white-space: nowrap;
                    border-bottom: 1px solid var(--border-color);
                }

                &.expend {
                    & > div {
                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }

                &:not(.expend) {
                    & > div {
                        &:nth-child(2) {
                            border-bottom: none;
                        }
                    }
                }

                .text-overflow {
                    overflow: hidden;
                    width: 100%;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
            &:first-child {
                border-bottom: 1px solid var(--border-color);
            }
            &.suggest {
                border-top: none;
                background-image: url("@/assets/Cashier/suggest.png");
                background-repeat: no-repeat;
                background-size: 40px 40px;
                background-position: -2px -2px;
            }
        }
        .voucehr-main-center {
            :deep(.el-scrollbar) {
                position: relative;
                border-left: 1px solid var(--border-color);
                border-right: 1px solid var(--border-color);
                &::after,
                &::before {
                    content: "";
                    width: 100%;
                    height: 1px;
                    background-color: var(--border-color);
                    position: absolute;
                    left: 0;
                }
                &::after {
                    bottom: 0;
                }
                &::before {
                    top: 0;
                }
            }
        }

        .table-tip {
            padding-left: 20px;
            line-height: 20px;
            background: url("@/assets/Icons/warn.png") no-repeat 2px 2px;
        }
    }
}
.period-container {
    display: flex;
    align-items: center;
}
.line-item-field {
    :deep(.el-select) {
        width: 132px;
    }
}
.line-item-title {
    :deep(.el-select) {
        width: 97px;
        margin-left: -12px;
        .el-input__wrapper {
            padding: 0 5px 0 10px;
        }
    }
}
