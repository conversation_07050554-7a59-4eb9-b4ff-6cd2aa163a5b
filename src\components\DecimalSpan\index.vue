<script lang="ts" setup>
import { ref, watch } from "vue";
import { useAccountSetStore } from "@/store/modules/accountset";
import { FormatNumber, truncateDecimal, truncateDecimalZero } from "@/util/format";
const props = defineProps({
    value: {
        type: String,
        required: true,
    },
});

const accountsetStore = useAccountSetStore();
const showTip = ref(false);
const formated = ref(false);
const truncatedData = ref("");
const realData = ref("");
const calcValue = (val: string) => {
    showTip.value = false;
    realData.value = "";
    let value = val;
    if (value.indexOf(",") != -1) {
        formated.value = true;
        value = value.replace(/,/g, "");
    }
    const number = Number(props.value);
    if (isNaN(number) || number === 0) {
        truncatedData.value = "";
        return;
    }

    const decimal = accountsetStore.accountSet?.decimalPlace;
    const calcTruncatedData = truncateDecimal(value, decimal);
    if (calcTruncatedData == "") {
        truncatedData.value = calcTruncatedData;
        return;
    }
    truncatedData.value = formated.value ? FormatNumber(calcTruncatedData) : calcTruncatedData;
    if (number !== Number(calcTruncatedData)) {
        showTip.value = true;
        const realValueStr = truncateDecimalZero(number.toFixed(8));
        realData.value = formated.value ? FormatNumber(realValueStr) : realValueStr;
    }
};
calcValue(props.value);
watch(props, (val) => {
    calcValue(val.value);
});
</script>
<template>
    <span v-if="showTip" :title="realData">
        <span>{{ truncatedData }}</span>
    </span>
    <span v-else>
        {{ truncatedData }}
    </span>
</template>
