<template>
    <div class="main-center-module">
        <template v-for="item in voucherModuleData" :key="item.type">
            <div class="main-center-module-template">
                <div class="main-center-module-block">
                    <div class="settings-icon" @click="handleShowModuleVoucherSettings(item)"></div>
                    <div class="main-center-module-title" v-permission="['businessvoucher-canedit']">
                        {{ item.typeName }}
                    </div>
                    <div class="main-center-module-info">
                        <div class="row">
                            <span v-html="getDisplayModuleText(item.type, item.sumCount)"></span>
                            <span v-if="item.sumCount" class="link ml-20" @click="checkDetailBill(ErpCheckBillType.All, item.type)">查看</span>
                        </div>
                        <div class="row">
                            <span class="ok-voucher">
                                已生成凭证：
                                <span :class="{ link: item.generatedCount }" @click="item.generatedCount ? checkDetailBill(ErpCheckBillType.Generated, item.type):''">
                                {{ item.generatedCount }}张单据
                                </span>
                            </span>
                        </div>
                        <div class="row">
                            <span class="no-voucher">
                                未生成凭证：
                                <span :class="{ link: item.unGeneratedCount }" @click="item.unGeneratedCount ? checkDetailBill(ErpCheckBillType.UnGenerated, item.type):''">
                                {{ item.unGeneratedCount }}张单据
                                </span>
                            </span>
                        </div>
                        <div class="row">包含单据：{{ getIncludeBills(item.type) }}</div>
                    </div>
                    <div class="main-center-module-button">
                        <template v-if="!item.unGeneratedCount">
                            <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start" :teleported="false">
                                <template #content>
                                    <div>
                                        当前{{ item.typeName }}没有需要生成凭证的单据
                                    </div>
                                </template>
                                <template #default>
                                    <el-checkbox v-model="item.select" :disabled="!item.unGeneratedCount" />
                                </template>
                            </el-tooltip>
                            <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start" :teleported="false">
                                <template #content>
                                    <div>
                                        当前{{ item.typeName }}没有需要生成凭证的单据
                                    </div>
                                </template>
                                <template #default>
                                    <a
                                        class="button solid-button"
                                        :class="item.unGeneratedCount && checkPermission(['businessvoucher-canedit']) ? '' : 'disabled'"
                                        @click="generateVoucher(item.type, item.unGeneratedCount)">
                                    生成凭证
                                    </a>
                                </template>
                            </el-tooltip>
                        </template>
                        <template v-else>
                            <el-checkbox v-model="item.select" />
                            <a
                                class="button solid-button"
                                :class="checkPermission(['businessvoucher-canedit']) ? '' : 'disabled'"
                                @click="generateVoucher(item.type, item.unGeneratedCount)">
                                生成凭证
                            </a>
                           </template>
                    </div>
                </div>
            </div>
        </template>
        <el-dialog
            destroy-on-close
            v-model="voucherModuleCombineInfo.display"
            title="生成凭证设置"
            width="440px"
            center
            class="custom-confirm"
            @closed="resetVoucherModuleCombineInfo"
        >
            <div class="voucher-settings-content">
                <div class="voucher-settings-main">
                    <div class="row">
                        <span>凭证合并：</span>
                        <SelectCheckbox
                            class="ml-10"
                            :class="{ empty: voucherModuleCombineInfo.selectedList.length === 0 }"
                            width="180px"
                            place-holder="请选择凭证合并方式"
                            :useElIcon="true"
                            :options="voucherModuleCombineInfo.options"
                            :select-all="false"
                            :show-all="false"
                            v-model:selectedList="voucherModuleCombineInfo.selectedList"
                            @update:selected-list="handleChangeSelectedList"
                        >
                        </SelectCheckbox>
                    </div>
                    <div class="row">
                        <span>科目合并：</span>
                        <SelectCheckbox
                            class="ml-10"
                            :class="{ empty: voucherModuleCombineInfo.selectedSubjectList.length === 0 }"
                            width="180px"
                            place-holder="请选择科目合并方式"
                            :useElIcon="true"
                            :options="subjectOption"
                            :select-all="false"
                            :show-all="false"
                            v-model:selectedList="voucherModuleCombineInfo.selectedSubjectList"
                        >
                        </SelectCheckbox>
                        <el-popover placement="right" :width="200" trigger="hover">
                            <template #reference>
                                <span class="question-icon"></span>
                            </template>
                            <div>
                                生成的凭证为<span class="highlight-red">暂存凭证</span>时<span class="highlight-red">不会合并科目</span>
                            </div>
                        </el-popover>
                    </div>
                    <div class="row">
                        <span>凭证日期：</span>
                        <el-select class="ml-10" style="width: 180px" v-model="voucherModuleCombineInfo.date">
                            <el-option label="单据日期" :value="3000"></el-option>
                            <el-option label="会计日期" :value="3001"></el-option>
                            <el-option label="电脑日期" :value="3002"></el-option>
                        </el-select>
                    </div>
                    <div class="row">
                        <span>摘要分隔符：</span>
                        <el-select
                            :teleported="false"
                            class="ml-10 separator"
                            style="width: 180px"
                            v-model="voucherModuleCombineInfo.separator"
                        >
                            <el-option
                                v-for="(item, index) in erpSeparatorOption"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </div>
                    <div class="tips">
                        <div>1.该设置仅用于模块展示时批量生成{{ voucherModuleCombineInfo.title}}凭证，业务单据列表展示时生成凭证不适用</div>
                        <div>2.此设置主要用来配置生成凭证的合并规则、凭证日期</div>
                        <div>3.如需修改生成凭证模板，可到 “<span :class="{ link: checkHasPermission() }" @click="linkToTemplate">业务凭证模板</span>” 中配置</div>
                    </div>
                </div>
                <div class="buttons">
                    <span class="button mr-20" @click="voucherModuleCombineInfo.display = false">取消</span>
                    <span class="button solid-button" @click="handleModuleVoucherSettingsSave">确定</span>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { type IResponseModel, request } from "@/util/service";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { checkPermission } from "@/util/permission";
import {
    changeSelectedList,
    checkCanSearch,
    getDisplayModuleText,
    getIncludeBills,
    getMergeOptions,
    subjectOption,
    erpSeparatorOption,
    ERPVoucherSummaryWay,
} from "../utils";

import { VoucherModule, ErpCheckBillType } from "../types";
import type { ModuleBack, IModuleVoucherCombineInfo, IQueryParams } from "../types";

import SelectCheckbox from "@/components/SelectCheckbox/index.vue";

const props = defineProps<{
    getSearchParams: () => IQueryParams;
    scmProductType: number;
    scmAsid: number;
    isProjectAccountingEnabled: boolean;
}>();

const emit = defineEmits<{
    (e: "generate-voucher", type: number, count: number): void;
    (e: "check-detail-bill", generateType: ErpCheckBillType, moduleType: number): void;
}>();

interface IModuleVoucherSettings {
    asid: number;
    moduleType: number;
    voucherSummaryWay: string;
    asubDirectionWay: string;
    dateWay: number;
    separator: string;
}
function getCombineOptions(combineWay: string) {
    let options: Array<number> = [];
    if (!combineWay) return options;
    options = combineWay.split(",").map((item) => ~~item);
    return options;
}
function handleShowModuleVoucherSettings(row: VoucherModule) {
    const configOptions = [...getMergeOptions(row.type)];
    if (!props.isProjectAccountingEnabled) {
        const projectIndex = configOptions.findIndex((item) => item.id === ERPVoucherSummaryWay.Project);
        if (projectIndex !== -1) {
            configOptions.splice(projectIndex, 1);
        }
    }
    const assignParams = {
        title: row.typeName,
        options: configOptions,
        type: row.type,
    };
    Object.assign(voucherModuleCombineInfo, assignParams);
    const params = {
        type: row.type,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    request({ url: "/api/VoucherForErp/VoucherSettings?" + getUrlSearchParams(params) }).then(
        (res: IResponseModel<IModuleVoucherSettings>) => {
            if (res.state !== 1000) return;
            voucherModuleCombineInfo.selectedList = getCombineOptions(res.data.voucherSummaryWay);
            voucherModuleCombineInfo.selectedSubjectList = getCombineOptions(res.data.asubDirectionWay);
            voucherModuleCombineInfo.date = res.data.dateWay;
            voucherModuleCombineInfo.separator = res.data.separator || "_";
            voucherModuleCombineInfo.display = true;
        }
    );
}
function generateVoucher(type: number, count: number) {
    emit("generate-voucher", type, count);
}
function checkHasPermission() {
    return checkPermission(["businessvouchertemplate-canview"]);
}
function linkToTemplate() {
    if (!checkHasPermission()) return;
    globalWindowOpenPage("/Erp/BusinessVoucherTemplate", "业务凭证模板");
}

const voucherModuleCombineInfo = reactive<IModuleVoucherCombineInfo>({
    type: 0,
    display: false,
    selectedList: [],
    selectedSubjectList: [],
    selectZero: false,
    date: 3000,
    separator: "_",
    title: "",
    options: [],
});
function resetVoucherModuleCombineInfo() {
    const data = {
        display: false,
        selectedList: [],
        selectedSubjectList: [],
        selectZero: false,
        date: 3000,
        title: "",
        options: [],
    };
    Object.assign(voucherModuleCombineInfo, data);
}

let isSave = false;
function handleModuleVoucherSettingsSave() {
    if (isSave) return;
    isSave = true;
    const queryParams = {
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    const data = {
        moduleType: voucherModuleCombineInfo.type,
        voucherSummaryWay: voucherModuleCombineInfo.selectedList.join(),
        asubDirectionWay: voucherModuleCombineInfo.selectedSubjectList.join(),
        dateWay: voucherModuleCombineInfo.date,
        separator: voucherModuleCombineInfo.separator,
    };
    request({ url: "/api/VoucherForErp/VoucherSettings?" + getUrlSearchParams(queryParams), method: "post", data })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: "保存失败" });
                return;
            }
            voucherModuleCombineInfo.display = false;
            ElNotify({ type: "success", message: "保存成功" });
        })
        .finally(() => {
            isSave = false;
        });
}
function handleChangeSelectedList(val: Array<number>) {
    changeSelectedList(val, voucherModuleCombineInfo);
}

const voucherModuleData = ref<Array<VoucherModule>>([]);
function getVoucherModuleData() {
    return voucherModuleData.value;
}
function clearVoucherModuleDataChecked() {
    voucherModuleData.value.forEach((item) => {
        item.select = false;
    });
}
function getBusinessVoucherCount() {
    const searchs = props.getSearchParams();
    if (!checkCanSearch(searchs)) return;
    const { startDate, endDate } = searchs;
    const data = {
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
        startDate,
        endDate,
    };
    request({
        url: `/api/VoucherForErp/GetBusinessStatistics?` + getUrlSearchParams(data),
        method: "post",
    }).then((res: IResponseModel<Array<ModuleBack>>) => {
        if (res.state !== 1000) return;
        voucherModuleData.value = res.data.map((item) => {
            const voucherModule = new VoucherModule();
            Object.assign(voucherModule, item);
            return voucherModule;
        });
    });
}
defineExpose({ getBusinessVoucherCount, getVoucherModuleData, clearVoucherModuleDataChecked });

function checkDetailBill(generateType: ErpCheckBillType, moduleType: number) {
    emit("check-detail-bill", generateType, moduleType);
}
</script>

<style lang="less" scoped>
.main-center-module {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    & .main-center-module-template {
        margin-top: 12px;
        margin-bottom: 20px;
        text-align: left;
        min-width: 360px;
        width: 33%;
        max-width: 600px;
        box-sizing: border-box;
        & .main-center-module-block {
            margin: 0 20px;
            position: relative;
            width: calc(100% - 40px);
            height: 240px;
            background: #ffffff;
            box-shadow: 0px 6px 18px 8px rgba(21, 27, 41, 0.03), 0px 6px 10px -4px rgba(21, 27, 41, 0.05);
            border-radius: 8px;
            padding: 20px 16px 16px 24px;
            box-sizing: border-box;
            & .main-center-module-title {
                color: var(--font-color);
                font-size: 22px;
                line-height: 28px;
            }
            .row {
                font-size: 14px;
                line-height: 24px;
                margin-top: 4px;
                
                .ok-voucher{
                    padding-left: 20px;
                    background: url("@/assets/Erp/ok-voucher.png") no-repeat 0 2px;
                    background-size: 15px 15px;

                }
                .no-voucher{
                    padding-left: 20px;
                    background: url("@/assets/Erp/no-voucher.png") no-repeat 0 2px;
                    background-size: 15px 15px;

                }
            }
            .settings-icon {
                width: 20px;
                height: 20px;
                position: absolute;
                top: 12px;
                right: 12px;
                background: url(@/assets/Icons/gear.png) no-repeat center;
                background-size: 20px 20px;
                cursor: pointer;
            }
        }
        .main-center-module-button {
            width: calc(100% - 40px);
            position: absolute;
            bottom: 16px;
            right: 16px;
            display: flex;
            justify-content: space-between;
        }
    }
}
.voucher-settings-content {
    .voucher-settings-main {
        padding: 20px 20px 10px;
        .row {
            display: flex;
            align-items: center;
            height: 32px;
            padding-left: 50px;
            & > span {
                width: 84px;
            }
            + .row {
                margin-top: 10px;
            }
            .question-icon {
                width: 16px;
                height: 16px;
                display: inline-block;
                background: url("@/assets/Scm/question.png") no-repeat center;
                background-size: 16px 16px;
            }
            :deep(.el-select) {
                &.separator {
                    .el-select-dropdown__item {
                        line-height: 20px;
                    }
                }
            }
        }
        .tips {
            display: inline-block;
            margin: 30px 20px 0;
            padding-left: 20px;
            background: url("@/assets/Icons/warn.png") no-repeat 2px 2px;
            background-size: 16px 16px;
        }
        :deep(.empty) {
            .el-input__inner {
                color: #929292;
            }
        }
    }
}
</style>
