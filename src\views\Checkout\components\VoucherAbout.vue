<template>
    <div class="edit-carryOver-box" :class="{ 'show-title': showTitle }">
        <div class="VoucherContainer">
            <div class="VoucherHeader" v-show="showTitle">
                <div class="stepTitle" :class="zoomState === 'out' ? 'out' : 'in'">
                    <span>{{ titleText }}</span>
                </div>
            </div>
            <div class="divBody" ref="voucherAboutRef">
                <VoucherView
                    ref="voucherViewRef"
                    v-model:query-params="voucherQueryParams"
                    :get-disabled-date="getDisabledDate"
                    @voucher-changed="voucherChangedHnadle"
                    @load-success="voucherLoadSuccess"
                    @zoom="zoomChange"
                    from="checkout"
                    :showCancel="true"
                    @back="handleCancelByUserClick"
                    :edited="voucherCanSave"
                    cancelTxt="取消"
                    :hiddenLine="true"
                    :isBottomBtn="true"
                    @save="handleSave"
                    @delete-voucher="handleDeleteVoucher"
                    :is-generate-voucher-page="true"
                ></VoucherView>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue";
import { ElNotify } from "@/util/notify";
import {
    VoucherModel,
    VoucherSaveModel,
    VoucherSaveParams,
    EditVoucherQueryParams,
    VoucherEntryModel,
    VoucherQueryParams,
    DataVoucherQueryParams,
} from "@/components/Voucher/types";
import { request, type IResponseModel } from "@/util/service";
import VoucherView from "@/components/Voucher/index.vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { VoucherType } from "../utils";

const emit = defineEmits(["back-from-voucher","delete-voucher"]);
const voucherViewRef = ref<InstanceType<typeof VoucherView>>();
const voucherAboutRef = ref<HTMLDivElement>();

const props = defineProps<{
    toVoucherAbout: Function;
    pid: number;
    partMainMonth: {
        type: String, 
        required: true 
    }
}>();

const zoomState = ref<"in" | "out">("in");
const zoomChange = (state: "in" | "out") => {
    zoomState.value = state;
};

function getDisabledDate(current: Date) {
    const periodList = useAccountPeriodStore().periodList;
    const periodInfo = periodList.find((item) => item.pid === props.pid);
    const p_s = new Date(periodInfo!.year, periodInfo!.sn - 1, 1); 
    const p_e = new Date(periodInfo!.year, periodInfo!.sn, 0);
    // const p_s = periodInfo?.year + "-" + periodInfo?.sn + "-01";
    // const p_e = periodInfo?.year + "-" + periodInfo?.sn + "-" + getDaysInMonth(periodInfo?.year as number, periodInfo?.sn as number);
    return current.getTime() < new Date(p_s).getTime() || current.getTime() > new Date(p_e).getTime();
}

const edited = ref(false);
//生成凭证或者编辑了凭证可以保存
const voucherCanSave = computed(() => {
    const vid  = voucherViewRef.value?.getVoucherModel().vid ?? 0;
    return vid === 0 || edited.value;
});
const currentvtid = ref('');
function voucherLoadSuccess() {
    props.toVoucherAbout();
    edited.value = false;
}

const voucherQueryParams = ref<VoucherQueryParams>();
const voucherChangedHnadle = () => {
    edited.value = true;
};
let backPage = "";
const showTitle = ref(false);
const titleText = ref("");
const setData = (pid: number, vid: number, backPath?: string) => {
    if (backPath) backPage = backPath;
    voucherViewRef.value?.resetScrollIndex();
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
};

const getCheckoutVoucherModel = (voucherLines: VoucherEntryModel[], vgId: number, vdate?: string, vtype = 0, attachFiles?: any[]) => {
    const voucherModel = new DataVoucherQueryParams(voucherLines);
    voucherModel.vgId = vgId;
    voucherModel.vtype = vtype;
    if (vdate !== undefined) {
        voucherModel.vdate = vdate;
    }
    if (attachFiles !== undefined) {
        voucherModel.attachments = attachFiles.length;
        voucherModel.attachFileIds = attachFiles.map((item: any) => item.fileId).join(",");
        voucherModel.attachFiles = attachFiles;
    }
    return voucherModel;
};
const newSetData = (voucherLines: VoucherEntryModel[], vgId = 1010, vdate?: string, backPath?: string, vtype = 0, vtId?: string, attachFiles?: any[]) => {
    if (vtId) {
        currentvtid.value = vtId;
    }
    voucherQueryParams.value = new VoucherQueryParams();
    nextTick().then(() => {
        if (backPath) backPage = backPath;
        const voucherModel = getCheckoutVoucherModel(voucherLines, vgId, vdate, vtype, attachFiles);
        voucherQueryParams.value = voucherModel;
    });
};

const newVoucher = (
    vdate: string,
    vgId = 1010,
    titleShow = false,
    title = "",
    backPath = "",
    voucherLines?: VoucherEntryModel[],
    vtype = 0
) => {
    showTitle.value = titleShow;
    titleText.value = title;
    backPage = backPath;
    const voucherParams = new DataVoucherQueryParams(voucherLines || []);
    voucherParams.vdate = vdate;
    voucherParams.vgId = vgId;
    voucherParams.vtype = vtype;
    voucherQueryParams.value = voucherParams;
};
const resetTotalMoney = () => {
    voucherViewRef.value?.resetAsubTotalAmount();
};

const voucherChanged = () => {
    return edited.value;
};

defineExpose({ setData, newVoucher, newSetData, resetTotalMoney, voucherChanged });

// 这里已经用了节流，防抖并不适用这里，所以不用再特意加个防抖
let canSave = true;
const handleSave = () => {
    if (!canSave) return;
    canSave = false;
    if(voucherQueryParams.value?.vtype===VoucherType.accruedSalaryModule || voucherQueryParams.value?.vtype===VoucherType.paySalaryModule){
        voucherViewRef.value?.saveVoucher(
            new VoucherSaveParams(1057, (res: IResponseModel<VoucherSaveModel>) => {
                if (res.state === 1000) {
                    request({
                        url: `/api/SalaryVoucher/BindSalaryAndVoucher?mid=${props.partMainMonth}&vtid=${currentvtid.value}&pid=${res.data.pid}&vid=${res.data.vid}`,
                        method: "post",
                    }).then((res:any) => {
                        if(res.state === 1000){
                            ElNotify({
                                message:"凭证生成成功！",
                                type: "success",
                            });
                            handleCancel();
                        }else{
                            ElNotify({
                                message:"res.msg",
                                type: "warning",
                            });
                        }
                    });
                }else if (res.state === 2000) {
                    if (res.subState !== -1) {
                        ElNotify({ message: res.msg, type: "warning" });
                    }
                } else if (res.state === 9999) {
                    ElNotify({ message: "保存失败", type: "warning" });
                }
                setTimeout(() => {
                    canSave = true;
                }, 1000);
            })
        );
    }else{
        voucherViewRef.value?.saveVoucher(
        new VoucherSaveParams(1057, (res: IResponseModel<VoucherSaveModel>) => {
            if (res.state === 1000) {
                const voucherModel = voucherViewRef.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({ message: "亲，保存成功啦！", type: "success" });
                }
                handleCancel();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({ message: res.msg, type: "warning" });
                }
            } else if (res.state === 9999) {
                ElNotify({ message: "保存失败", type: "warning" });
            }
            setTimeout(() => {
                canSave = true;
            }, 1000);
        })
        );
    }
};

const reset = () => {
    backPage = "";
    titleText.value = "";
    showTitle.value = false;
    nextTick().then(() => {
        voucherQueryParams.value = new VoucherQueryParams();
        edited.value = false;
    });
};
const handleCancelByUserClick = () => {
    voucherViewRef.value?.removeEventListener();
    emit("back-from-voucher", backPage, true);
    useFullScreenStore().changeFullScreenStage(false);
    reset();
};
const handleDeleteVoucher = () => {   
    const { vid, pid } = voucherViewRef.value!.getVoucherModel();
    emit("delete-voucher", vid, pid, backPage, handleCancelByUserClick);
};
const handleCancel = () => {
    emit("back-from-voucher", backPage, false);
    useFullScreenStore().changeFullScreenStage(false);
    reset();
};
</script>

<style lang="less" scoped>
.edit-carryOver-box {
    text-align: left;
    box-sizing: border-box;
    // height: calc(var(--voucher-min-height) + 108px);
    // height: ~"max(calc(var(--voucher-min-height) + 108px), calc(100vh - var(--content-padding-bottom) - var(--title-height) - 48px))";
    display: flex;
    flex-direction: column;
    align-items: stretch;
    .VoucherContainer {
        // height: 0;
        // flex: 1;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        .VoucherFooter {
            height: 20px;
            padding: 0 20px 20px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            flex-shrink: 0;
        }
        .VoucherHeader {
            flex-shrink: 0;
            .stepTitle {
                color: var(--font-color);
                font-size: 24px;
                line-height: 30px;
                margin-top: 20px;
                width: 100%;
                padding: 0 54px;
                box-sizing: border-box;
                > span {
                    display: block;
                    margin: 0 auto;
                }
                &.in {
                    > span {
                        width: 1002px;
                    }
                }
                &.out {
                    > span {
                        width: 100%;
                    }
                }
            }
        }
        .divBody {
            flex: 1;
        }
    }
    .check-box-tool-bar {
        text-align: center;
        margin-bottom: 40px;
        margin-top: 40px;
        flex-shrink: 0;
    }

    &.show-title {
        // height: max(
        //     calc(var(--voucher-min-height) + 108px + 50px),
        //     calc(100vh - var(--content-padding-bottom) - var(--title-height) - 48px)
        // );
    }
}
</style>
