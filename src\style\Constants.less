:root {
    //色值
    --orenge: #fba285;
    --purple: #b69ce7;
    --light-green: #6ed773; //亮绿，实心按钮hover颜色
    --green: #44b449; //主色调，默认绿色
    --dark-green: #26922b; //暗绿，按钮点击效果
    --light-blue: #699cff; //亮蓝，实心按钮hover颜色
    --blue: #3d7fff; //erp主色调
    --dark-blue: #336cd9; //暗蓝，按钮点击效果
    --menu-green: #32d373; //菜单绿
    --menu-background-color: #021102; //菜单背景色
    --menu-font-color: #cccccc; //菜单字色
    --red: #fd5055; //默认红
    --dark-red: #cf1322; //暗红色
    --yellow: #eeb400; //默认黄
    --dark-yellow: #c29300; //暗黄
    --orange: #ff821c; //橘黄
    --link-color: #3385ff; //链接蓝
    --dark-link-color: #0f4fae; //深蓝，链接点击效果
    --font-color: #333333; //默认字色
    --weaker-font-color: #929292; //不重要的文字颜色
    --button-border-color: #d8d8d8; //按钮边框颜色
    --button-red: #f5222d; //按钮红
    --button-dark-red: #cf1322; //按钮暗红
    --border-color: #dadada; //分割线颜色
    --page-background-color: #f6f6f6; //页面底色
    --jz-table-color: #f8faf8; //记账表格单元格底色
    --jz-table-hover-color: #e5f5e5; //记账表格hover颜色
    --jz-table-title-color: #f0f7f0; //记账表格表头颜色
    --jz-table-border-color: #dadada; //记账表格分割线颜色
    --erp-table-color: transparent; //记账表格单元格底色
    --erp-table-hover-color: #f5f7fa; //记账表格hover颜色--和表头一样
    --erp-table-title-color: #f1f5fc; //业财表格表头颜色
    --erp-table-border-color: #ebeef5; //记账表格分割线颜色
    --table-color: var(--jz-table-color); //表格单元格底色
    --table-hover-color: var(--jz-table-hover-color); //表格hover颜色
    --table-title-color: var(--jz-table-title-color); //表格表头颜色
    --table-border-color: var(--jz-table-border-color); //表格分割线颜色
    --table-striped-color: #f8faf8;
    --table-selected-color: #f0f7f0;
    --table-selected-hover-color: #f0f7f0;
    --white: #ffffff; //默认白色
    --black: #000000; //默认黑色
    --shadow-color: rgba(0, 0, 0, 0.2); //遮罩层颜色
    --background-color: #f6f6f6; //背景色
    --light-main-color: var(--light-green); //亮绿，实心按钮hover颜色
    --main-color: var(--green); //主色调，默认绿色
    --dark-main-color: var(--dark-green); //暗绿，按钮点击效果
    --green-header-color: #f3f7f3; //绿色表头背景色
    --slot-title-color: #E5E5E5; //多页签标题颜色
    //常用变量
    --transition-time: 0.2s;
    --title-split-line: rgba(0, 0, 0, 0.08);
    //字号
    --h1: 20px;
    --h2: 18px;
    --h3: 16px;
    --h4: 14px;
    --h5: 12px;
    --font-size: var(--h4);
    --line-height: 20px;
    //标签页变量
    --tab-height: 30px;
    --tab-head-width: 100px;
    --tab-head-long-width: 160px;
    --tab-width: 100px;
    --tab-long-width: 160px;
    //表格变量
    --table-title-height: 37px;
    --table-title-line-height: 17px;
    --table-title-font-size: var(--h5);
    --table-body-height: 37px;
    --table-body-line-height: 17px;
    --table-body-font-size: var(--h5);
    //icon
    --arrow-up-img: url("/Image/Settings/up-black.png");
    --arrow-down-img: url("/Image/Settings/down-black.png");
    //文本框变量
    --input-border-color: var(--border-color); //文本框边框颜色
    --input-border-radius: 0px; //文本框边框弧度
    --input-border-hover-color: var(--border-color); //文本框hover颜色
    --input-border-focus-color: var(--main-color); //文本框focus颜色
    --input-border-outline-color: transparent; //文本框outline颜色
    --input-placeholder-color: var(--weaker-font-color); //文本框outline颜色
    --input-disabled-color: var(--background-color); //文本框不可用背景色
    --input-disabled-font-color: var(--weaker-font-color); //文本框不可用文字颜色
    --input-disabled-border-color: var(--input-border-color); //文本框不可用边框颜色
}

:root {
    --content-width: 1000px;
    --edit-content-width: 1000px;
    --self-adaption-content-padding: 0px;
    --menu-container-width: 140px;
    --router-container-margin: 12px;
    --top-value:97px;
    --content-padding-bottom: 0px;
    --title-height: 0px;
    --voucher-min-height: 536px;}

body {
    --el-border-radius-base: 2px;
    --el-border-radius-small: 4px;
    --el-text-color-primary: var(--font-color);
    --el-text-color-regular: var(--font-color);
    --el-color-primary: var(--main-color);
    --el-datepicker-text-color: var(--main-color);
    .el-popover {
        --el-popover-border-radius: var(--el-border-radius-base, 2px);
    }
    .el-popper {
        --el-popper-border-radius: var(--el-popover-border-radius, 2px);
    }

    --el-border-color-lighter: var(--border-color);

    .el-table {
        --el-table-header-text-color: var(--font-color);
        --el-table-header-bg-color: var(--table-title-color);
        --el-table-row-hover-bg-color: var(--table-hover-color);
        --el-table-current-row-bg-color: var(--table-selected-color);
    }
    --el-fill-color-light: var(--table-title-color);
    --el-fill-color-lighter: var(--table-color);

    .el-dialog {
        --el-dialog-font-line-height: 52px;
        --el-dialog-title-font-size: var(--h3);
        --el-dialog-padding-primary: 0;
    }

    .el-button--primary {
        --el-button-hover-border-color: var(--light-main-color);
        --el-button-hover-bg-color: var(--light-main-color);
    }
}

body[erp] {
    --light-main-color: var(--light-blue);
    --main-color: var(--blue);
    --dark-main-color: var(--dark-blue);
    --table-color: var(--erp-table-color); //表格单元格底色
    --table-hover-color: var(--erp-table-hover-color); //表格hover颜色
    --table-selected-color: #f1f5fc; //表格hover颜色
    --table-selected-hover-color: #eef2f8; //表格hover颜色
    --table-title-color: var(--erp-table-title-color); //表格表头颜色
    --table-border-color: var(--erp-table-border-color); //表格分割线颜色
    --table-line-color: #dfe4eb; // 表格边线颜色
    --table-title-height: 44px;
    --table-title-line-height: 20px;
    --table-title-font-size: var(--font-size);
    --table-body-height: 44px;
    --table-body-line-height: 20px;
    --table-body-font-size: var(--font-size);
    --input-border-color: #d9d9d9;
    --input-border-radius: 2px;
    --tab-height: 32px;
    --input-border-hover-color: var(--blue); // input悬停
    --input-border-outline-color: #dae7ff;
    --input-placeholder-color: #999999;
    --input-disabled-color: #f5f5f5;
    --input-disabled-font-color: #b7b7b7;
    --input-disabled-border-color: #e9e9e9;
    --button-border-color: #d9d9d9;
    --border-color: rgba(0, 0, 0, 0.1);
    --common-item-margin-32: 32px; // 通用边距
    --common-item-margin-20: 20px; // 通用边距
    --common-item-margin-16: 16px; // 通用边距
    --select-item-hover: rgba(98, 127, 178, 0.1); // 下拉框移入颜色
    --self-adaption-content-padding: 0px;
    --input-border-focus-color: var(--main-color); //文本框focus颜色
}
