<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:acid="autoBankForm.acid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :uscc-number="props.usccNumber"
        :bank-name="props.bankName"
        @save-success="saveSuccessHandle"
        @confirm-bank="confirmBank"
    />
    <div class="slot-content align-center edit-temp-table" v-show="icbcConfirm">
        <div class="slot-content-mini edit-temp-table-content icbc-bank">
            <div class="autoBankSelect">
                <div class="autoBankItem"><span style="font-weight: 600">第一步：</span>打开IE浏览器使用签约U盾登录工行网上银行</div>
                <div class="autoBankItem"><span style="font-weight: 600">第二步：</span>首页点击进入工银聚进行协议签订</div>
                <div class="autoBankItem">
                    <p style="margin-left: 56px; margin-top: -8px; font-size: 12px; color: rgba(102, 102, 102, 1)">
                        项目名称选择柠檬云（自主记账）
                    </p>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第三步：</span>首页点击进入工银聚进行指令复核，审核协议通过</div>
                <div class="autoBankItem">
                    <p style="margin-left: 56px; margin-top: -8px; font-size: 12px; color: rgba(102, 102, 102, 1)">
                        协议签订完成退出后使用授权U盾重新登录工行网银
                    </p>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第四步：</span>点击立即授权，确定完成授权</div>
            </div>
            <div class="autoBankDescription">
                <div style="margin-left: 40px; padding-top: 20px; line-height: 16px">
                    <span style="font-weight: 500">工行网银地址：</span>
                    <span class="copy-file copy-key" data-target="icbc-apply-url" @click="handleCopy">复制</span>
                </div>
                <p class="copy-key pub-key" data-target="icbc-apply-url" @click="handleCopy">https://www.icbc.com.cn</p>
            </div>
            <div class="authButton" style="margin-bottom: 20px; margin-top: 40px">
                <a class="button back-button" attr-click="1" @click="handleBackToMain">上一步</a>
                <a class="solid-button-large" attr-click="1" @click="hanldeAuthorize">{{ authorizeBtnText }}</a>
            </div>
            <div class="autoBankToOpen" style="height: 16px">&nbsp;</div>
            <CommonBottom></CommonBottom>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watchEffect, onMounted, nextTick, watch } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { BankType } from "@/constants/bankKey";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams } from "@/util/url";
import { GetBankLink } from "@/util/bankType";
import { appendStyle, copyText, handleCheck, upgradeApi, getSuccessMsg } from "../utils";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, IBankHandleResult, CMBGradeResult } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import CommonBottom from "./CommonBottom.vue";
import { isInWxWork } from "@/util/wxwork";
import { replaceAll } from "@/util/common";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = [
    "1、请确认公司名称和统一社会信用代码",
    "2、选择对应的银行账户，并确认对应的银行账号",
    "3、在电脑上插入“中国工商银行”U盾",
    "4、设置授权记账",
];

const props = defineProps({
    bankName: { type: String, default: "" },
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const currencyList = computed(() => props.currencyList);
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const icbcConfirm = ref(false);
const impowerValue = ref("立即授权");
const authorizeBtnText = ref("立即授权");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    mainaccno: "",
});
const isWxwork = ref(isInWxWork());

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "授权中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};
let authorizeCanNext = true;
const hanldeAuthorizeLock = () => {
    authorizeCanNext = false;
    authorizeBtnText.value = "申请中...";
};
const handleAuthorizeUnLock = () => {
    authorizeCanNext = true;
    authorizeBtnText.value = "立即授权";
};

const confirmBank = () => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "授权中，请稍后！" });
        return;
    }
    handleConfirmLock();
    props.checkAuthorization(props.bankType, replaceAll(autoBankForm.mainaccno, " ", ""), () => {
        request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "授权失败，请重试！" });
                handleConfirmUnLock();
                return;
            }
            handleToIcbcConfirm();
        });
    });
};

const hanldeAuthorize = () => {
    if (!authorizeCanNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    hanldeAuthorizeLock();
    // 检查当前账户的授权状态
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Query");
    });
};

const handleICBCApply = () => {
    ElConfirm("是否已完成在中国工商银行的授权签约操作？").then((r: boolean) => {
        if (r) {
            upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
                handleResult(res, "Apply");
            });      
        }
    });
};

function handleResult(res: IResponseModel<CMBGradeResult>, type: string) {
    const { successMsg, acctNo, acname} =  getSuccessMsg(autoBankForm, bankAccountList.value);
    if (res.state !== 1000) {
        ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
        handleAuthorizeUnLock();
        return;
    }
    const result = res.data;
    if (result.status === 1 || result.status === 2) {
        //已签约成功
        ElConfirm(appendStyle(successMsg), true, () => {}, "授权成功").then(() => {
            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
        });
        handleAuthorizeUnLock();
        handleBackToMain();
        return;
    }
    if (result.status === 4) {
        let errorMsg = result.message;
        if (result.data && result.data.Msg) {
            errorMsg = result.data.Msg;
        }
        ElConfirm(errorMsg || "签约失败，请联系客服或者稍后再试~", true, () => {}, "授权失败");
        handleAuthorizeUnLock();
        return;
    }
    if (result.status === 5 || result.status === 6) {
        //其他账套成功签约/其他账套签约中
        ElConfirm(result.message, true, () => {}, "授权失败");
        handleAuthorizeUnLock();
        return;
    }
    if (result.status === 3 && type === "Query") { //签约中查询
        handleICBCApply();
        handleAuthorizeUnLock();
        return;
    }
    if (result.status === 3 && type === "Apply") { //签约中申请
        ElNotify({ type: "warning", message: "未查询到本银行账户“" + acname + "”账号“" + acctNo + "”在工商银行的授权信息。" });
        handleAuthorizeUnLock();
        return;
    } 
}

const handleBackToMain = () => {
    showMain.value = true;
    icbcConfirm.value = false;
    canNext = true;
};
const handleToIcbcConfirm = () => {
    showMain.value = false;
    icbcConfirm.value = true;
    handleConfirmUnLock();
};

const handleCopy = () => {
    copyText("https://www.icbc.com.cn");
    ElNotify({ type: "success", message: "复制成功！" });
};

const bankHelpLink = ref("");
onMounted(() => {
    let res = GetBankLink(props.bankType);
    bankHelpLink.value = res;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});

watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);

defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
</style>
