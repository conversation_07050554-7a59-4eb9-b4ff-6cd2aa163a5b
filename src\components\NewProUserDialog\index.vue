<template>
    <div class="new-pro-user-dialog" v-if="newProUserDialogShow">
        <div class="new-pro-user-container">
            <img src="@/assets/NewProUserDialog/pic.png" />
            <div class="dialog-title">您已购买专业版，立即前往专业版？</div>
            <div class="buttons">
                <a class="button opacity" @click="newProUserDialogShow = false">留在免费版</a
                ><a class="button solid-button" @click="tryGoToPro()">前往专业版</a>
            </div>
        </div>
    </div>
</template>
<style scoped lang="less">
.new-pro-user-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--shadow-color);
    z-index: 9999;

    .new-pro-user-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        background: var(--white);
        border-radius: 10px;
        width: 680px;
        height: 440px;

        img {
            width: 236px;
            height: 168px;
            margin-top: 38px;
        }

        .dialog-title {
            color: var(--font-color);
            line-height: 45px;
            font-size: 32px;
            font-weight: 600;
            margin-top: 43px;
        }

        .buttons {
            margin-top: 40px;
            padding: 0;
            border-top: none;

            .button {
                border-radius: 6px;
                width: 160px;
                height: 48px;
                box-sizing: border-box;
                line-height: 46px;
                font-size: 18px;
            }

            .opacity {
                background-color: rgba(68, 180, 73, 0.08);
                border-color: var(--main-color);
                color: var(--main-color);

                &:hover {
                    background-color: var(--main-color);
                    color: var(--white);
                }

                &:active {
                    background-color: var(--dark-main-color);
                    border-color: var(--dark-main-color);
                }
            }

            .solid-button {
                margin-left: 35px;
                line-height: 48px;
            }
        }
    }
}
</style>
<script setup lang="ts">
import { tryGoToPro } from "@/util/proUtils";
import { ref } from "vue";

const newProUserDialogShow = ref(true);
</script>
