export const getGlobalToken = () => {
    return getQueryStringByName("appasid");
};
export const getServiceID = () => {
    return getQueryStringByName("serviceID");
};
export const getAACompanyId = () => {
    return getQueryStringByName("AACompanyId");
};

export const getAACustomerId = () => {
    return getQueryStringByName("AACustomerId") || getQueryStringByNameFromHash("AACustomerId");
};

export const getAACustomerName = () => {
    return getQueryStringByName("AACustomerName") || getQueryStringByNameFromHash("AACustomerName");
};

export const getQueryStringByName = (name: string) => {
    const result = location.search.match(new RegExp(new RegExp(`[?&]${name}=([^&#]*)`, "i")));
    if (result == null || result.length < 1) {
        return "";
    }
    return result[1];
};

const getQueryStringByNameFromHash = (name: string) => {
    const result = location.hash.match(new RegExp(new RegExp(`[?&]${name}=([^&#]*)`, "i")));
    if (result == null || result.length < 1) {
        return "";
    }
    return result[1];
};
