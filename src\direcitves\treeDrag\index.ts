import type { Directive } from "vue";

export const treeDrag: Directive = {
    mounted: (el: any, binding: any) => {
        let totalWidth = 0;
        let currentX = 0;
        let actWidth = 0;
        el.addEventListener("mousedown", (e: MouseEvent) => {
            e.preventDefault();
            e.stopPropagation();
            document.addEventListener("mousemove", handleMouseMove);
            document.addEventListener("mouseup", handleMouseUp);
            totalWidth = (document.querySelector(".drag-right") as  HTMLElement)?.offsetWidth;
            currentX = e.clientX;
        });

        function handleMouseMove(e: MouseEvent) {
            e.preventDefault();
            const left = el.offsetLeft + e.clientX - el.getBoundingClientRect().left;
            const lineRef = document.querySelector(".assit-col-line") as HTMLElement;
            if (lineRef) {
                lineRef.style.left = left + "px";
                lineRef.style.display = "block";
                lineRef.style.zIndex = "999";
            }
        }

        function handleMouseUp(e: MouseEvent) {
            e.preventDefault();
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            const moveX = e.clientX - currentX;
            actWidth = totalWidth - moveX;
            actWidth = Math.max(100, Math.min(actWidth, 480));
            if (actWidth >= 480) {
                binding.value.expandFlag = false;
                binding.value.closeFlag = true;
            } else {
                binding.value.closeFlag = true;
                binding.value.expandFlag = true;
            }
            (document.querySelector(".drag-right") as  HTMLElement).style.width = actWidth + "px";
            (document.querySelector(".assit-col-line") as HTMLElement).style.display = "none";
        }
    },
};
