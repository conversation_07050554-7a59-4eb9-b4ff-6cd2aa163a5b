export const useCompIncomeTaxQuarterStore = defineStore("compIncomeTaxQuarter", () => {
  const compIncomeTaxQuarter = reactive({
    id: 0,
    name: "",
    year: 0,
    quarter: 0,
    amount: 0,
    taxRate: 0,
    taxAmount: 0,
    tableData: [
      {
        id: 1,
        name: "季度所得税申报",
        year: 2023,
        quarter: 1,
        amount: 10000,
        taxRate: 25,
        taxAmount: 2500,
      },
      {
        id: 2,
        name: "季度所得税申报",
        year: 2023,
        quarter: 2,
        amount: 15000,
        taxRate: 25,
        taxAmount: 3750,
      },
    ],
    quickFillModel: {
      // 季初从业人数
      startEmployeeNum: 0,
      // 季末从业人数
      endEmployeeNum: 0,
      // 季初资产总额（万元）
      startAssetTotal: 0,
      // 季末资产总额（万元）
      endAssetTotal: 0,
      // 营业收入本年累计额（元）
      currentYearIncome: 0,
      // 营业成本本年累计额（元）
      currentYearCost: 0,
      // 利润总额本年累计额（元）
      currentYearProfit: 0,
      // 可弥补亏损额（元）
      lossAmount: 0,
    },
  })
  const compIncomeTaxQuarterLoading = ref(false)
  const compIncomeTaxQuarterLoadingText = ref("正在加载数据，请稍等...")
  // 获取企业季度所得税申报信息
  const getCompIncomeTaxQuarter = async (params?: any) => {
    compIncomeTaxQuarterLoading.value = true
    compIncomeTaxQuarterLoadingText.value = "正在获取初始表单数据和预填报表数据"
    // 模拟获取数据
    console.log("开始填写")
    try {
      await new Promise((resolve) => setTimeout(resolve, 4000))
      Object.assign(compIncomeTaxQuarter, {
        id: "1",
        name: "季度所得税申报",
        year: 2023,
        quarter: 1,
        amount: 10000,
        taxRate: 25,
        taxAmount: 2500,
        tableData: [
          {
            id: 1,
            name: "季度所得税申报",
            year: 2023,
            quarter: 1,
            amount: 10000,
            taxRate: 25,
            taxAmount: 2500,
          },
          {
            id: 2,
            name: "季度所得税申报",
            year: 2023,
            quarter: 2,
            amount: 15000,
            taxRate: 25,
            taxAmount: 3750,
          },
        ],
        quickFillModel: {
          // 季初从业人数
          startEmployeeNum: 21,
          // 季末从业人数
          endEmployeeNum: 23,
          // 季初资产总额（万元）
          startAssetTotal: 100,
          // 季末资产总额（万元）
          endAssetTotal: 200,
          // 营业收入本年累计额（元）
          currentYearIncome: 21,
          // 营业成本本年累计额（元）
          currentYearCost: 32,
          // 利润总额本年累计额（元）
          currentYearProfit: 111,
          // 可弥补亏损额（元）
          lossAmount: 444,
        },
      })
      console.log("填写完成")
    } catch (error) {
      console.error("填写失败", error)
    } finally {
      compIncomeTaxQuarterLoading.value = false
    }
  }

  // 保存企业季度所得税申报信息
  const saveCompIncomeTaxQuarter = async () => {
    compIncomeTaxQuarterLoading.value = true
    compIncomeTaxQuarterLoadingText.value = "正在保存数据，请稍等..."
    // 模拟保存数据
    console.log("开始保存store")
    try {
      await new Promise((resolve) => setTimeout(resolve, 4000))
      console.log("保存完成")
    } catch (error) {
      console.error("保存失败", error)
    } finally {
      compIncomeTaxQuarterLoading.value = false
    }
  }

  return {
    compIncomeTaxQuarter,
    getCompIncomeTaxQuarter,
    compIncomeTaxQuarterLoading,
    compIncomeTaxQuarterLoadingText,
    saveCompIncomeTaxQuarter,
  }
})
