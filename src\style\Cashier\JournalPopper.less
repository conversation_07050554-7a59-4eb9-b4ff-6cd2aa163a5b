.el-popper.opposite-autocomplete-popper {
    .el-autocomplete-suggestion__list {
        > li {
            cursor: pointer;
            padding: 6px 6px 6px 8px;
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }
    .value {
        white-space: break-spaces;
        line-height: 16px;
        font-size: 12px;

        display: -webkit-box;
        -webkit-line-clamp: 2; /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
.pay-method-popper {
    .el-select-dropdown__item {
        .option-icon {
            width: 16px;
            height: 16px;
            position: absolute;
            top: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            &.edit {
                right: 30px;
            }
            &.delete {
                right: 10px;
            }
        }
        &.payment-option {
            span {
                -webkit-line-clamp: 1 !important;
            }
        }
        &.edit-payment-option {
            padding-right: 45px !important;
        }
    }
}
.el-popper.journal-table-popper {
    z-index: 1999 !important;
}
