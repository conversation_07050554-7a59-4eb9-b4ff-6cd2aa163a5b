workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'
      when: always
    - if: '$CI_DEPLOY_FREEZE != null'
      when: never
    - when: always

variables:
  GIT_STRATEGY: none

stages:
  - details
  - deploy

details:
  stage: details
  script:
    - echo $CI_PIPELINE_SOURCE
    - echo $CI_DEPLOY_FREEZE
    - echo $CI_COMMIT_BRANCH
    - echo $GITLAB_USER_NAME

stage:
  stage: deploy
  tags:
    - jenkins
  script:
    - echo $H5_URL
    - curl -X POST $H5_URL
  rules:
    - if: $CI_COMMIT_BRANCH == "stage"

stage2:
  stage: deploy
  tags:
    - jenkins
  script:
    - echo $H5_URL2
    - curl -X POST $H5_URL2
  rules:
    - if: $CI_COMMIT_BRANCH == "stage2"
