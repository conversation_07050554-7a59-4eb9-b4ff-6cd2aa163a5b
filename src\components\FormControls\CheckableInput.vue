<template>
  <div
    class="checkable-input"
    :class="{ 'is-invalid': !isValid && showError }">
    <el-input
      v-model="innerValue"
      v-bind="$attrs"
      :class="borderClass"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus">
      <!-- 前缀插槽：错误图标或透传前缀 -->
      <template #prefix>
        <template v-if="!isValid && showError">
          <el-tooltip
            effect="light"
            :content="errorMessage">
            <el-icon><WarningFilled /></el-icon>
          </el-tooltip>
        </template>
        <slot
          v-else-if="$slots.prefix"
          name="prefix"></slot>
      </template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
  import { onMounted } from "vue"
  import { WarningFilled } from "@element-plus/icons-vue"
  import { useValidate, type Rule } from "./useValidate"

  // 定义props
  const props = withDefaults(
    defineProps<{
      // 校验相关
      rule?: Rule
      validateTrigger?: string // 全局默认的校验触发时机
    }>(),
    {
      validateTrigger: "blur",
    },
  )

  // 定义事件
  const emits = defineEmits<{
    (e: "update:modelValue", value: any): void
    (e: "change", value: any): void
    (e: "input", value: any): void
    (e: "blur", value: any): void
    (e: "focus", value: any): void
  }>()

  // 使用v-model
  const modelValue = defineModel<any>()

  // 使用验证钩子
  const { innerValue, isValid, errorMessage, showError, borderClass, handleChange, handleBlur, handleFocus, validate } = useValidate(
    props,
    emits,
    modelValue,
  )

  // 暴露方法
  defineExpose({
    validate,
  })

  // 挂载时校验
  onMounted(() => {
    debugger
    validate()
  })
</script>

<style lang="scss" scoped>
  .checkable-input {
    width: 100%;
    position: relative;

    .common-border {
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-border-color) inset;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }

    .prompt-border {
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-color-warning) inset;

        .el-input__prefix {
          color: var(--el-color-warning);
        }
      }
    }

    .force-border {
      :deep(.el-input__wrapper) {
        box-shadow: 0 0 0 1px var(--el-color-danger) inset;

        .el-input__prefix {
          color: var(--el-color-danger);
        }
      }
    }

    :deep(.el-input) {
      width: 100%;
    }

    :deep(.el-input__prefix) {
      font-size: 16px;
      display: flex;
      align-items: center;
    }
  }
</style>
