<template>
    <div class="slot-content">
        <div class="title">{{ currentSlidName }}</div>
        <div class="voucher-handle-box" :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in' }">
            <div class="edit-content-topbox">
                <div class="edit-top">
                    <span class="txt float-l">模板名称：</span>
                    <div  class="float-l mr-10 input">
                        <Tooltip :content='submitInfo.name' placement="right" :isInput="true">
                            <el-input type="text" v-model="submitInfo.name" maxlength="30" />
                        </Tooltip>
                    </div>
                    <span class="txt float-l ml-20">模板类型：</span>
                    <div class="cd-accountList">
                        <Select
                            ref="SelectRef"
                            v-model="submitInfo.type"
                            :teleported="false"
                            :bottom-html="newTemplateTypeHtml"
                            @bottom-click="handleAddTemplateTypeClick"
                            :filterable="true"
                            :filter-method="voucherTypeFilterMethod"
                        >
                            <Option v-for="item in showVoucherTypeList" :key="item.id" :label="item.label" :value="item.id" />
                        </Select>
                    </div>
                </div>
                <div class="edit-tool-bar">
                    <a
                        v-permission="['vouchertemplate-canedit']"
                        class="button solid-button float-l"
                        @click="saveVoucher"
                        @mousedown="$event.stopPropagation()"
                        >保存</a
                    >
                    <a class="button float-l ml-10" @click="cancleEdit" @mousedown="$event.stopPropagation()">取消</a>
                </div>
            </div>
            <div class="edit-content-bottom" style="box-sizing: border-box">
                <Voucher
                    v-model:query-params="voucherQueryParams"
                    :toolbar-top-display="false"
                    @zoom="zoomCallback"
                    @load-success="voucherLoadSuccess"
                    @voucher-changed="voucherChanged"
                    ref="voucher"
                ></Voucher>
            </div>
        </div>

        <el-dialog v-model="setTemplateDialog" title="模板类型设置" center width="600" class="dialogDrag">
            <div class="template-name pt-10 pl-10 pb-10" v-dialogDrag>
                <el-input placeholder="请输入" v-model="vtTypeName" style="width: 30%; height: 28px" maxlength="30" />
                <a class="solid-button button ml-10" @click="handleAddVtType">新增</a>
            </div>
            <div class="template-table-list pl-10 pr-10 pb-10">
                <el-table
                    :data="voucherTypeList"
                    border
                    fit
                    stripe
                    highlight-current-row
                    class="custom-table"
                    :scrollbar-always-on="true"
                    height="260px"
                >
                    <el-table-column label="类型名称" min-width="50%" header-align="left" prop="label" :resizable="false">
                        <template #default="scope">
                            <span v-if="editStatusId !== scope.row.id">{{ scope.row.label }}</span>
                            <el-input class="template-table-item-input" v-else v-model="scope.row.label"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="50%" header-align="left" :resizable="false">
                        <template #default="scope">
                            <span v-if="editStatusId !== scope.row.id">
                                <a class="link" @click="handleEditVtType(scope.row.id)">编辑</a>
                                <a class="link" @click="handleDeleteVtType(scope.row.id)">删除</a>
                            </span>
                            <span v-else>
                                <a class="button solid-button" @click="handleEditVtTypeSave(scope.row)">保存</a>
                                <a
                                    class="button ml-10"
                                    @click="
                                        () => {
                                            (editStatusId = ''), getVoucherTypeList();
                                        }
                                    "
                                    >取消</a
                                >
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, watchEffect } from "vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Voucher from "@/components/Voucher/index.vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { debounce } from "@/views/Statements/utils";
import Tooltip from "@/components/Tooltip/index.vue";
import type { ISubmitInfo, ISelectOption } from "../types";
import {
    VoucherModel,
    VoucherTemplateSaveParams,
    type VoucherQueryParams,
    EditVoucherTemplateQueryParams,
    NewVoucherTemplateQueryParams,
} from "@/components/Voucher/types";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { commonFilterMethod } from "@/components/Select/utils";

const currentSlidName = ref("新增模板");
let setTemplateDialog = ref(false);
const voucherTypeList = ref<ISelectOption[]>([]);
const submitInfo = reactive<ISubmitInfo>({
    type: 1,
    vtid: -1,
    name: "",
});
let editType = ref("add");

const emit = defineEmits(["backToMain"]);

const cancleEdit = () => {
    voucher.value?.removeEventListener();
    emit("backToMain");
    submitInfo.type = 1;
    submitInfo.vtid = -1;
    submitInfo.name = "";
    resetInit()
};
const newTemplateTypeHtml = `<li class="new-cd-account">
    <a class="link">
        +点击添加
    </a>
</li>`;
let SelectRef = ref<InstanceType<typeof Select>>();
const handleAddTemplateTypeClick = () => {
    let timer = setTimeout(() => {
        setTemplateDialog.value = true;
        clearTimeout(timer);
    }, 0);
};
// 新增凭证
const voucherQueryParams = ref<VoucherQueryParams>();
const voucher = ref<InstanceType<typeof Voucher>>();
const zoomState = ref<"in" | "out">("in");
const zoomCallback = ref((_zoomState: "in" | "out"): void => {
    zoomState.value = _zoomState;
});
function voucherLoadSuccess(voucherModel: VoucherModel) {
    init = true;
    // edited.value = false;
    // if ((voucherModel.pId === 0 && voucherModel.vId === 0) || pageType.value === "voucherPage") {
    //     loadVoucherSwitchInfo(0);
    // }
}

function initSubmitInfo(type: string, data: any) {
    if (type === "add") {
        editType.value = "add";
        voucherQueryParams.value = new NewVoucherTemplateQueryParams();

        currentSlidName.value = "新建模板";
        submitInfo.name = "";
        submitInfo.type = voucherTypeList.value[0].id;
    } else {
        editType.value = "edit";
        // getVoucherItemInfo(vtId);
        voucherQueryParams.value = new EditVoucherTemplateQueryParams(data.vtId);
        submitInfo.name = data.vtName;
        submitInfo.type = data.vtType;
        submitInfo.vtid = data.vtId;
        currentSlidName.value = "编辑模板";
    }
    init = false;
}
defineExpose({
    initSubmitInfo,
});
const saveVoucher = debounce(saveVoucherFn);
let isSaving = false;
function saveVoucherFn() {
    if (isSaving) return;
    isSaving = true;
    let data = {
        vtId: editType.value === "add" ? 0 : submitInfo.vtid,
        vtType: submitInfo.type,
        vtCode: "",
        vtName: submitInfo.name,
        vtDefault: false,
        vgId: 0,
        voucherLines: voucher.value?.getVoucherModel().voucherLines,
    };
    if (!submitInfo.name) {
        ElNotify({
            type: "warning",
            message: "亲，请输入模板名称！",
        });
        isSaving = false;
        return false;
    } else if (submitInfo.name.length > 64) {
        ElNotify({
            type: "warning",
            message: "模板名称不能超过64个字！",
        });
        isSaving = false;
        return false;
    }

    let templateSaveData = new VoucherTemplateSaveParams(submitInfo.type, submitInfo.name, (res) => {
        isSaving = false;
        if (res.state === 1000) {
            ElNotify({
                type: "success",
                message: "亲，保存成功啦！",
            });
            emit("backToMain");
            resetInit()
        } else if (res.state === 2000) {
            if (res.subState !== -1) {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        } else if (res.state === 9999) {
            if (res.msg) {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        }
    });
    voucher.value?.saveVoucher(templateSaveData);
}
const getVoucherTypeList = () => {
    request({
        url: "/api/VoucherTemplate/GetVtTypeList",
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            let resArr = [];
            for (let key in res.data) {
                resArr.push({ id: Number(key), label: res.data[key] });
            }
            voucherTypeList.value = resArr;

            submitInfo.type = voucherTypeList.value[0].id;
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
getVoucherTypeList();
// 模板类型设置
let vtTypeName = ref("");
const handleAddVtType = () => {
    if (!vtTypeName.value) {
        ElNotify({
            type: "warning",
            message: "请输入模板类型名称！",
        });
        return false;
    }
    request({
        url: "/api/VoucherTemplate/VtType",
        method: "post",
        params: {
            vtTypeName: vtTypeName.value,
            vtTypeId: 0,
        },
    }).then((res: any) => {
        if (res.data) {
            ElNotify({
                type: "success",
                message: "保存成功！",
            });
            vtTypeName.value = "";
            getVoucherTypeList();
            window.dispatchEvent(new CustomEvent("reloadVoucherTemplateType"));
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存失败！",
            });
        }
    });
};
let editStatusId = ref("");
const handleEditVtType = (id: string) => {
    editStatusId.value = id;
};

const handleEditVtTypeSave = (row: any) => {
    request({
        url: "/api/VoucherTemplate/VtType",
        method: "post",
        params: {
            vtTypeName: row.label,
            vtTypeId: row.id,
        },
    }).then((res: any) => {
        if (res.data) {
            ElNotify({
                type: "success",
                message: "保存成功！",
            });
            editStatusId.value = "";
            getVoucherTypeList();
            window.dispatchEvent(new CustomEvent("reloadVoucherTemplateType"));
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存失败！",
            });
        }
    });
};

const handleDeleteVtType = (id: number | string) => {
    request({
        url: "/api/VoucherTemplate/VtType",
        method: "delete",
        params: {
            vtTypeId: id,
        },
    }).then((res: any) => {
        if (res.data) {
            getVoucherTypeList();
            window.dispatchEvent(new CustomEvent("reloadVoucherTemplateType"));
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
};

const edited = ref(false);
let init = false;
const voucherChanged = () => {
    if (!init) return;
    edited.value = true;
};
const resetInit = () => {
    init = false;
    edited.value = false;
};
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
watch([() => submitInfo.name, () => submitInfo.type], () => {
    if (!init) return;
    edited.value = true;
});
watch(edited, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});

const showVoucherTypeList = ref<Array<ISelectOption>>([]);
watchEffect(() => { 
    showVoucherTypeList.value = JSON.parse(JSON.stringify(voucherTypeList.value));  
});
function voucherTypeFilterMethod(value: string) {
    showVoucherTypeList.value = commonFilterMethod(value, voucherTypeList.value, 'label');
}

</script>

<style lang="less" scoped>
@import "@/style/Settings/VoucherTemplate.less";

body[erp] .slot-content {
    overflow-y: auto !important;
}

.cd-accountList {
    display: inline-block;
    .detail-el-select(166px, 28px);
    :deep(.el-select-dropdown__item) {
        padding: 0;
        text-align: left;
    }

    :deep(.new-cd-account) {
        line-height: 34px;
        height: 34px;
        display: flex;
        justify-content: center;
        .link {
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        .link:hover {
            color: var(--link-color);
        }
    }
}
.el-input.template-table-item-input {
    :deep(.el-input__wrapper) {
        .el-input__inner {
            border: none;
        }
    }
}
.template-name {
    display: flex;
    align-items: center;
}
.input{
    width: 150px;
    height: 30px;
}
:deep(.el-input__inner){
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
</style>
