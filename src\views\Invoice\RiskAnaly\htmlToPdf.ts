import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { ElNotify } from "@/util/notify";
import { request } from "@/util/service";

const setBox = {
    useCORS: true, //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。
    allowTaint: true, //允许跨域
    scale: 1.5,  //设置放大倍数
    backgroundColor: '#ffffff'//背景色
}
export const downloadPDF = (
    directory: Record<string, number>,
    fileName: string,
    loadingVisible: any,
) => {
    let totalPage = 1;
    const PDF = new jsPDF({
        orientation: 'p', //参数： l：横向  p：纵向
        unit: 'mm', //参数：测量单位（"pt"，"mm", "cm", "m", "in" or "px"）
        format: 'a4', //A4纸
        compress: true, //文件压缩
    });

    //分页函数
    function pageSplit(canvas: any) {
        //设置画布
        let ctx = canvas.getContext('2d');
        const a4w = 190;
        const a4h = 272; //A4大小，210mm x 297mm，左右保留10mm的边距，上20下10，显示区域190x267
        const imgHeight = Math.floor(a4h * canvas.width / a4w);
        let renderedHeight = 0;

        while (renderedHeight < canvas.height) {
            const page = document.createElement("canvas") as HTMLCanvasElement;
            page.width = canvas.width;
            page.height = Math.min(imgHeight, canvas.height - renderedHeight); //可能内容不足一页


            // 获取 2D 渲染上下文
            const ctxPage = page.getContext('2d');
            if (ctxPage) {
                //用getImageData剪裁指定区域，并画到前面创建的canvas对象中
                ctxPage.putImageData(ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)), 0, 0);

                // canvas转图片数据
                PDF.addImage(page.toDataURL('image/png', 0.2), 'PNG', 10, 17, a4w, Math.min(270, a4w * page.height / page.width), '', 'FAST');
                // 释放 ImageData 对象的资源
                URL.revokeObjectURL(page.toDataURL('image/png', 0.2));
                renderedHeight += imgHeight;

                // 绘制页眉背景
                PDF.addImage(headerImg, 'PNG', 5, 3, 200, 10, '', 'FAST');
                //设置页脚
                PDF.setTextColor("#666");
                PDF.setFontSize(10);
                PDF.text(String(totalPage), 105, 292);

                //判断是否分页，如果后面还有内容，添加一个空页
                if (renderedHeight < canvas.height) {
                    PDF.addPage();
                    totalPage++;
                }
            }
            // 清空 canvas 元素内容
            ctxPage!.clearRect(0, 0, page.width, page.height);
        }
        //释放画布内存
        ctx.width = 0;
        ctx.height = 0;
        ctx = null;
    }
    
    //页眉图片重复插入重复
    let headerImg = "";

    //新增页
    function commonSetPage() {
        PDF.addPage();
        totalPage++;
        PDF.setPage(totalPage);
    }
    //释放canvas内存
    function releaseCanvasMemory(canvas: HTMLCanvasElement): void {
        if (canvas instanceof HTMLCanvasElement) {
            // 清空canvas内容
            canvas.width = 0;
            canvas.height = 0;
        }
    }

    const generatePDF = async () => {
        try {
            //获取页眉图片
            const header = document.querySelector(".header") as HTMLElement;
            const headerCanvas = await html2canvas(header, setBox)
            headerImg = headerCanvas.toDataURL('image/png');
            releaseCanvasMemory(headerCanvas);

            //封面
            const fm = document.querySelector(".report-warp") as HTMLElement;
            const fmCanvas = await html2canvas(fm, setBox);
            const fmImgData = fmCanvas.toDataURL('image/png');
            PDF.addImage(fmImgData, 'PNG', 0, 0, 210, 297, '', 'FAST');
            commonSetPage();
            releaseCanvasMemory(fmCanvas);
            URL.revokeObjectURL(fmImgData);
            
            //目录
            const ml = document.querySelector(".directory") as HTMLElement;
            const mlCanvas = await html2canvas(ml, setBox);
            const mlImgData = mlCanvas.toDataURL('image/png');
            const imgWidth = 190;
            const imgHeight = imgWidth * (mlCanvas.height / mlCanvas.width);
            PDF.addImage(mlImgData, 'PNG', 10, 0, imgWidth, imgHeight, '', 'FAST');
            commonSetPage();
            releaseCanvasMemory(mlCanvas);
            URL.revokeObjectURL(mlImgData);

            const element = document.getElementById("pages") as HTMLElement;
            if (!element) {
                return;
            }
            const canvas = await html2canvas(element, setBox);
            pageSplit(canvas);
            releaseCanvasMemory(canvas);
            
        } catch (error) {
            ElNotify({
                message: `${error}`,
                type: "success",
            });
        }
    };

    generatePDF().then(()=>{
        setTimeout(()=>{
            PDF.save(fileName + ".pdf");
            ElNotify({
                message: "导出成功",
                type: "success",
            });
            //调用日志下载成功记录
            request({ 
                url: "/api/invoice/reportDownload", 
                method: "post" 
            });
            loadingVisible.value = false;
        }, 1000)
    }).catch(()=>{
        ElNotify({
            message: "导出失败",
            type: "warning",
        });
    }).finally(()=> {
        //清除页眉header图片资源
        if (headerImg) {
            URL.revokeObjectURL(headerImg);
        }
        // 查找所有拥有 "add" 类名的元素
        const elements = document.querySelectorAll('.add');
        // 遍历并移除元素
        elements.forEach(element => {
            element.remove();
        });
    })
};
 