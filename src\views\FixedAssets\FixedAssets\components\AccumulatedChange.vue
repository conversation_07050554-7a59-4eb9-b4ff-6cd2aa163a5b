<template>
    <div class="edit-content" style="width: 1000px; padding: 0px;">
        <div class="title">{{ props.changeTitle }}</div>
        <div style="border: 1px solid var(--slot-title-color);margin-top: 32px;">
            <table class="change-tb">
                <tr>
                    <td colspan="4" class="tb-hr">基本信息</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px; text-align: right">资产编号：</td>
                    <td id="ztxg_bh" class="tb-field" style="width: 326px">{{ props.changeData.fa_num }}</td>
                    <td class="tb-title" style="width: 84px">资产类别：</td>
                    <td id="ztxg_lb" class="tb-field" style="width: 416px">
                        <Tooltip :content="props.changeData.fa_type_name" :max-width="326">{{ props.changeData.fa_type_name }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">资产名称：</td>
                    <td id="ztxg_mc" class="tb-field">
                        <Tooltip :content="props.changeData.fa_name" :max-width="326">{{ props.changeData.fa_name }}</Tooltip>
                    </td>
                    <td class="tb-title">资产型号：</td>
                    <td id="ztxg_xh" class="tb-field">
                        <Tooltip :content="props.changeData.fa_model" :max-width="326">{{ props.changeData.fa_model }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">所属部门：</td>
                    <td id="ztxg_bm" class="tb-field">
                        <Tooltip :content="props.changeData.department_name" :max-width="326">{{
                           props.changeData.department_name
                        }}</Tooltip>
                    </td>
                </tr>
            </table>
            <div class="line"></div>
            <table class="change-tb" cellspacing="0" cellpadding="0">
                <tr>
                    <td colspan="4" class="tb-hr">变更内容</td>
                </tr>
                <td class="tb-title" style="width: 174px">{{changeData.fa_property===0?'累计折旧':'累计摊销'}}调整值：</td>
                <td class="tb-field" style="width: 326px">
                    <el-input v-model="dataValue" style="width: 180px; height: 28px"></el-input>
                </td>
                <td class="tb-title" style="width: 84px"></td>
                <td class="tb-field" style="width: 416px"></td>
            </table>
            <div class="buttons" style="margin-top: 22px; margin-bottom: 40px; text-align: center">
                <a class="button solid-button" style="margin: 0 5px" @click="savedata">保存</a>
                <a class="button" style="margin: 0 5px" @click="changeCancle">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { ref } from "vue";
import { ElConfirm } from "@/util/confirm";
import { isDecimal } from "../validator";
import { ElNotify } from "@/util/notify";
import Tooltip from "@/components/Tooltip/index.vue";
const props = defineProps({
    changeData: {
        type: Object,
        default: () => {
            return {};
        },
    },
    changeTitle: {
        type: String,
        default: "",
    },
    pid: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(["changeCancle", "goToVoucher","getAssetsList"]);

const dataValue = ref("");

function savedata() {
    // 累计折旧调整
    let curentnum = dataValue.value.replace(/,/g, "");
    const depInfo = props.changeData.fa_property === 0 ? "折旧" : "摊销";
    if (!isDecimal(curentnum)) {
        ElNotify({
            type: "warning",
            message: `请输入正确的累计${depInfo}值！`,
        });
        return false;
    }
    if(!Number(curentnum)){
        ElNotify({
            type: "warning",
            message: "亲，您的数据没有变动，不能保存哦！",
        });
        return false;
    }
    request({
        url: `/api/FAChange/AccumulatedChange?faid=${props.changeData.fa_id}&pid=${props.pid}&value=${dataValue.value}&vid=0`,
        method: "post",
    }).then((res: IResponseModel<number>) => {
        if(res.state ===1000){
            ElConfirm("您的变更已保存，需要立即生成凭证吗？").then((r: Boolean) => {
            if (r) {
                emits('getAssetsList')
                getVoucher(res.data);
            } else {
                emits("changeCancle",true);
            }
        });
        }else{
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
       
    });
}

//资产变更生成凭证
function getVoucher(changeId: number) {
    // 生成累计折旧调整凭证
    request({
        url: `/api/FAVoucher/GenerateAccumulatedChangeVoucher?faid=${props.changeData.fa_id}&changeId=${changeId}&value=${dataValue.value}`,
        method: "post",
    }).then((res: IResponseModel<object>) => {
        emits("goToVoucher", res.data, props.changeTitle, null, null, changeId);
    });
}
function changeCancle() {
    emits("changeCancle");
}
</script>

<style scoped lang="less">
.tb-hr {
    padding: 20px 20px 15px 20px;
    color: var(--font-color);
    font-size: var(--h3);
    line-height: 22px;
    font-weight: bold;
    text-align: left;
}
.change-tb {
    & .tb-title {
        text-align: right;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
    }
    & .tb-field {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 14px;
        text-align: left;
    }
}
</style>
