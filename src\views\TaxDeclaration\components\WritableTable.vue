<template>
  <LMTable
    class="main-center"
    row-key="id"
    :show-overflow-tooltip="false"
    :data="data"
    :height="height"
    :columns="columns"
    :span-method="spanMethod">
    <template #input="{ slotColumn }">
      <el-table-column v-bind="slotColumn">
        <template #default="scope">
          <checkable-input
            v-if="scope.row.lineType === 1"
            v-model="scope.row[slotColumn.prop]"
            :check-type="scope.row[slotColumn.checkTypeField]"
            :row-data="scope.row"
            :check-type-field="slotColumn.checkTypeField"></checkable-input>
          <Popover
            v-else-if="scope.row.note"
            placement="right"
            :content="scope.row.note">
            <template #trigger>
              <div class="column-data">
                <div class="equal-icon" />
                <span style="float: right; line-height: 12px">{{ scope.row[slotColumn.prop] }}</span>
              </div>
            </template>
          </Popover>
        </template>
      </el-table-column>
    </template>
  </LMTable>
</template>
<script setup lang="ts">
  import type { IWritableTableProps } from "./types"

  const props = withDefaults(defineProps<IWritableTableProps>(), {
    columns: () => [],
    data: () => [],
  })
</script>
<style lang="scss" scoped>
  .el-table__row {
    &:hover {
      .cell {
        .equal-icon {
          // z-index: 9;
          float: left;
          width: 12px;
          height: 12px;
          background: url("@/assets/Icons/equal.png") no-repeat 0 0;
          background-size: 12px 12px;
        }
      }
    }
  }
</style>
