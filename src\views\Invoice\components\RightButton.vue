<template>
    <div class="button-content">
        <a
            v-if="showToEisBtn"
            class="button solid-button ml-10"
            @click="toInvoiceSystem"
            >开具发票</a
        >
        <div class="ml-10" @mouseenter="enterCheckScreenSize" @mouseleave="leaveCheckScreenSize">
            <el-tooltip 
                effect="light" 
                placement="top-start"
                :visible="visibleTaskTime"
                :width="300"
                :offset="4"
                :teleported="false" 
            >
                <a
                    v-permission="['invoice-fetch-canedit']"
                    v-show="!isThrid"
                    class="button solid-button hot-btn"
                    @click="showFetchInvoice"
                    ><img class="mark-loading" v-show="hasInvoiceTask" src="@/assets/Invoice/loading.png" />{{
                        hasInvoiceTask ? "取票中..." : "一键取票"
                    }}
                </a>
                <template #content>
                    <div class="invoice-task-tip">
                        {{ dayjs(finishedInvoiceTaskTime).format("YYYY-MM-DD HH:mm") }}更新
                        <a @click="openFetchInvoiceTaskShow" class="link">查看记录</a>
                    </div>
                </template>
            </el-tooltip>
        </div>
        <el-tooltip effect="light" placement="bottom-start">
            <template #content
                ><div style="width: 300px">
                    该功能通过一键采集获取电子税局里税务数字账户相关所属期的{{
                        invoiceCategory === "10070" ? "销项" : "进项"
                    }}发票信息和发票类型为全电发票的PDF格式附件，该过程可能需要一定时间~
                </div></template
            >
            <i
                v-permission="['invoice-fetch-canedit']"
                v-show="!isThrid"
                class="mark-question-icon"
            ></i>
        </el-tooltip>
        <div class="item ml-10">
            <Dropdown
                btnTxt="新增发票"
                class="mr-10"
                :downlistWidth="178"
                v-permission="invoiceCategory === '10070' ? ['invoice-output-canedit'] : ['invoice-input-canedit']"
            >
                <li @click="quickAddInvoice">快速新增</li>
                <li v-for="item in addInvoiceOptions" :key="item.IN_ID" @click="addInvoiceType(item)">{{ item.IN_NAME }}</li>
            </Dropdown>
        </div>
        <Dropdown
            btnTxt="导出"
            class="mr-10"
            :downlistWidth="110"
            v-permission="[invoiceCategory === '10070' ? 'invoice-output-canexport' : 'invoice-input-canexport']"
        >
            <li @click="handleExport(0)">导出</li>
            <li @click="handleExport(1)">按导入模板导出</li>
        </Dropdown>
        <Dropdown
            btnTxt="导入"
            class="mr-10"
            :downlistWidth="122"
            v-permission="[invoiceCategory === '10070' ? 'invoice-output-canimport' : 'invoice-input-canimport']"
        >
            <li @click="handleImportClick(0)">一键导入</li>
            <li @click="handleImportClick(1)">通用模板导入</li>
            <li @click="handleRecognizeClick">发票文件识别导入</li>
        </Dropdown>
        <Dropdown
            btnTxt="打印列表"
            class="mr-10"
            :downlistWidth="102"
            v-permission="[invoiceCategory === '10070' ? 'invoice-output-canprint' : 'invoice-input-canprint']"
        >
            <li @click="handlePrint(0,getDefaultParams(),printApi)">直接打印</li>
            <li @click="handlePrint(2)">打印设置</li>
        </Dropdown>
        <Dropdown
            btnTxt="批量认证"
            class="mr-10"
            :downlistWidth="isErp ? 98: 86"
            v-if="invoiceCategory === '10080' && checkPermission(['invoice-input-canedit'])"
        >
            <li @click="batchOperation(1)">批量认证</li>
            <li @click="batchOperation(4)">取消认证</li>
        </Dropdown>
        <div
            class="item"
            v-show="isErp"
            v-permission="[invoiceCategory === '10070' ? 'invoice-output-cancreatebill' : 'invoice-input-cancreatebill']"              
        >
            <a class="button" @click="openErpBill">生成单据</a>
        </div>
        <el-tooltip effect="light" placement="bottom-start">
            <template #content>
                <div style="width: 200px">
                    <div v-if="invoiceCategory === '10070'">
                        <div>销项正数发票会自动生成销售出库单,</div>
                        <div>销项负数发票会自动生成销售退货单;</div>
                    </div>
                    <div v-else>
                        <div>进项正数发票会自动生成采购入库单,</div>
                        <div>进项负数发票会自动生成采购退货单;</div>
                    </div>
                </div>
            </template>
            <i class="mark-question-icon mr-10" v-show="isErp" v-permission="[invoiceCategory === '10070' ? 'invoice-output-cancreatebill' : 'invoice-input-cancreatebill']"></i>
        </el-tooltip>
        <div
            class="item mr-10"
            v-show="isErp && !isProjectAccountingEnabled"
            v-permission="[invoiceCategory === '10070' ? 'invoice-output-candelete' : 'invoice-input-candelete']"
        >
            <a class="button" @click="batchOperation(2)">批量删除</a>
        </div>
        <Dropdown
            btnTxt="批量操作"
            class="mr-10"
            :downlistWidth="isErp ? 98: 86"
            v-show="isErp && isProjectAccountingEnabled"
        >
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-candelete' : 'invoice-input-candelete']"
                @click="batchOperation(2)"
            >批量删除
            </li>
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-canedit' : 'invoice-input-canedit']"
                @click="batchOperationProject"
            >
                指定项目
            </li>
        </Dropdown>
        <Dropdown
            btnTxt="批量操作"
            class="mr-10"
            :downlistWidth="110"
            v-if="bulkOperationCanView && !isErp"
        >
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-candelete' : 'invoice-input-candelete']"
                @click="batchOperation(2)"
            >
                批量删除
            </li>
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-canedit' : 'invoice-input-canedit']"
                @click="batchOperationProject"
            >
                指定项目
            </li>
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-canedit' : 'invoice-input-canedit']"
                @click="() => batchOperation(3)"
            >
                指定业务类别
            </li>
            <li
                v-if="
                    ([1, 2].includes(accountStandard)) &&
                    !isWxwork &&
                    checkPermission(
                       [ invoiceCategory === '10070'
                            ? 'invoice-output-cancreatewarehousingout'
                            : 'invoice-input-cancreatewarehousing']
                    ) &&
                    !isHideBarcode
                "
                @click="openBatchUpdateDialog"
            >
                生成{{ billText }}
            </li>
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-canedit' : 'invoice-input-canedit']"
                @click="associateVoucher(1)"
            >
                关联凭证
            </li>
            <li
                v-permission="[invoiceCategory === '10070' ? 'invoice-output-canedit' : 'invoice-input-canedit']"
                @click="associateVoucher(2)"
            >
                取消关联
            </li>
        </Dropdown>
        <div
            class="item"
            style="position: relative; display: flex"
            @mouseleave="() => (invoiceTemplateDownList = false)"
            v-show="allCanView && !isErp"
            v-permission="
                invoiceCategory === '10070'
                    ? ['invoice-output-cancreatevoucher', 'invoicevouchertemplate-canview']
                    : ['invoice-input-cancreatevoucher', 'invoicevouchertemplate-canview']
            "
        >
            <a class="button solid-button voucher-button" style="float: left" @click="openVoucherFromJournal">生成凭证</a>
            <div style="display: inline-block" class="item">
                <a class="button solid-button down-click" style="float: left" @mouseenter="() => (invoiceTemplateDownList = true)"></a>
                <div
                    style="width: 105px; position: absolute; top: 28px; left: -85px; z-index: 9999"
                    class="downlist"
                    v-show="invoiceTemplateDownList && checkPermission(['invoicevouchertemplate-canview'])"
                >
                    <li @click="showJournalSettings" class="downlist-buttons">生成凭证设置</li>
                    <li @click="showJournalTemplate" class="downlist-buttons">凭证模板设置</li>
                </div>
            </div>
        </div>
        <div
            class="item"
            style="position: relative; display: flex"
            @mouseleave="() => (invoiceTemplateDownList = false)"
            v-show="!isErp && invoicecreateVoucherCanView"
            v-permission="
                invoiceCategory === '10070'
                    ? ['invoice-output-cancreatevoucher', 'invoicevouchertemplate-canview']
                    : ['invoice-input-cancreatevoucher', 'invoicevouchertemplate-canview']
            "
        >
            <a class="button solid-button voucher-button" style="float: left" @click="openVoucherFromJournal">生成凭证</a>
            <div style="display: inline-block" class="item">
                <a class="button solid-button down-click" style="float: left" @mouseenter="() => (invoiceTemplateDownList = true)"></a>
                <div
                    style="width: 105px; position: absolute; top: 28px; left: -85px; z-index: 9999"
                    class="downlist"
                    v-show="invoiceTemplateDownList"
                >
                    <li @click="showJournalSettings" class="downlist-buttons">生成凭证设置</li>
                </div>
            </div>
        </div>
        <a class="button ml-10" v-if="isErp && checkPermission(['businessvoucher-canview'])" @click="genertaeErpVoucher">生成凭证</a>
        <el-tooltip effect="light" content="本按钮只支持跳转业务生成凭证页面，单据的筛选和勾选不会带入" placement="top-start">
            <el-icon v-if="isErp && checkPermission(['businessvoucher-canview'])" class="el-icon-question" color="#3385ff"><QuestionFilled /></el-icon>
        </el-tooltip>
        <a
            v-show="!isErp && invoicevouchertemplateCanView"
            class="button solid-button voucher-button large-1 ml-10"
            @click="showJournalTemplate"
            >凭证模板设置</a
        >
        <RefreshButton></RefreshButton>
    </div>
    <el-dialog v-model="certificationShow" :title="batchTitle" center width="440" modal-class="modal-class" class="custom-confirm dialogDrag">
        <div class="certification-show-content" v-dialogDrag>
            <div class="certification-show-main">
                <el-form-item v-if="batchTitle === '批量认证'" label="请选择批量认证日期：" class="certification-form" :status-icon="false">
                    <el-date-picker class="line-item-field" v-model="IssueDate" value-format="YYYY-MM-DD" />
                </el-form-item>
                <span v-else-if="batchTitle === '提示'" class="delete-tip"> 亲，确认要删除吗？ </span>
                <el-form-item v-else-if="batchTitle === '批量指定发票业务类型'" label="选择业务类型：" class="certification-form line-item" :show-message="false" prop="businessType">
                    <keep-alive>
                        <Select
                            class="line-item-field businessTypeRef"
                            popper-class="business-type-list"
                            :suffix-icon="CaretBottom"
                            v-model="businessType"
                            placeholder=" "
                            :teleported="false"
                            :filterable="true"
                            :no-match-text="'暂无数据'"
                            :filter-method="businessTypeFilterMethod"
                        >
                            <ElOption 
                                v-for="item in showBusinessTypeOptions" 
                                :key="item.value" 
                                :label="item.label" 
                                :value="item.value"
                            >
                            </ElOption>
                        </Select>
                    </keep-alive>
                </el-form-item>
                <span v-else class="delete-tip"> 亲，确认要取消认证吗？ </span>
            </div>
            <div class="buttons">
                <a class="button solid-button ml-10" @click="certificationSure(batchOption as number)">确定</a>
                <a class="button ml-10" @click="() => (certificationShow = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="certResultShow" title="提示" center width="440" modal-class="modal-class" class="custom-confirm dialogDrag">
        <div class="certification-show-content" v-dialogDrag>
            <div class="certification-res-tip">
                <div>成功：{{ certSuccess }}，跳过：{{ certJump }}（<span>{{ batchTitle === '批量认证' ? '已有' : '无' }}</span>认证日期的发票记录，将会被跳过！）</div>
            </div>
            <div class="buttons">
                <a
                    class="button solid-button"
                    @click="
                        () => {
                            certResultShow = false;
                            certificationShow = false;
                        }
                    "
                    >确定</a
                >
            </div>
        </div>
    </el-dialog>
    <ImportSingleFileDialog
        :importTitle="invoiceCategory === '10070' ? '销项发票导入' : '进项发票导入'"
        v-model:import-show="importShow"
        :importUrl="`/api/Invoice/${invoiceCategory === '10070' ? 'Sales' : 'Purchase'}Import`"
        :uploadSuccess="uploadSuccess"
        :need-loading="true"
        allowFileType=".xls,.xlsx,.xml"
    >
        <template #top-tips v-if="ImportWay === 0">
            <a
                @click="
                    globalWindowOpen(
                        `https://help.ningmengyun.com/#/jz/project?subMenuId=12010910${invoiceCategory === '10070' ? '5' : '6'}`
                    )
                "
                class="link invoice-import-help"
                v-show="!isErp && !isHideBarcode"
                >点击查看帮助</a
            >
        </template>
        <template #download>
            <span class="invoice-import-title"
                >1.请选择以下导入格式的任意一种导入{{ invoiceCategory === "10070" ? "销项" : "进项" }}发票：</span
            >
            <!-- 一键导入(原导入各平台导出发票) -->
            <div v-if="ImportWay === 0">
                <span v-if="invoiceCategory === '10070'">
                    <span class="invoice-import-title child">(1)从增值税发票管理2.0综合服务平台导出的Excel格式发票</span>
                    <span class="invoice-import-title child">(2)从航信税控盘导出的Excel格式发票</span>
                    <span class="invoice-import-title child">(3)从百旺税控盘导出的XML格式发票</span>
                    <span class="invoice-import-title child">(4)从增值税发票开票软件（税务Ukey版）导出的XML格式发票</span>
                    <span class="invoice-import-title child">(5)从电子税务局税务数字账户导出的EXCEL格式发票</span>
                    <span class="invoice-import-title child">(6)从电子税务局税务数字账户下载的XML格式发票</span>
                </span>
                <span v-else>
                    <span class="invoice-import-title child">(1)从增值税发票管理2.0综合服务平台导出的Excel格式发票</span>
                    <span class="invoice-import-title child">(2)从增值税发票选择确认平台（增值税发票认证平台）导出的进项发票</span>
                    <span class="invoice-import-title child">(3)从电子税务局税务数字账户导出的EXCEL格式发票</span>
                    <span class="invoice-import-title child">(4)从电子税务局税务数字账户下载的XML格式发票</span>
                </span>
            </div>
            <!-- 通用模板导入 -->
            <div v-if="ImportWay === 1">
                <span class="invoice-import-title child">(1)发票导出选择按导入模板导出，确认后直接导入</span>
                <span class="invoice-import-title child"
                    >(2)点击下载模板，按照模板格式进行数据整理再导入 <a class="link ml-20" @click="downloadTemplate">下载模板</a></span
                >
            </div>
        </template>
        <template #import-content>
            <!-- <span>导入{{ invoiceCategory === "10070" ? "销项" : "进项" }}发票：</span> -->
            <span>2.选择文件导入</span>
        </template>
    </ImportSingleFileDialog>

    <el-dialog 
        v-model="generateBillShow" 
        title="提示" 
        center 
        width="440" 
        align-center
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
    >
        <div class="generate-bill-content" v-dialogDrag>
            <div class="generate-main">
                <div class="generate-result">
                    <div>您已选择{{ billTotalCount }}张发票生成{{ invoiceCategory === "10070" ? "销售出库单" : "采购入库单" }}：</div>
                    <div class="result">
                        <p>
                            选择成功： <span>{{ billSuccessCount }}张</span>
                        </p>
                        <p>
                            选择失败： <span>{{ billFailCount }}张</span>
                        </p>
                    </div>
                </div>
                <div class="generate-notice">
                    <div class="generate-container">
                        <img src="@/assets/Icons/hint.png" />
                        <p>已作废发票/无商品明细发票/无数量发票/已生成单据等不支持操作；</p>
                    </div>

                    <div class="generate-container-line2">
                        <p>最终生成结果将根据进销存匹配情况而定。</p>
                    </div>
                </div>
                <div class="generate-container-radio" v-if="invoiceCategory === '10080'">
                    <div class="radio-lt">
                        <div>普通发票不参与抵扣</div>
                        <el-tooltip effect="light" placement="top-start">
                            <template #content>
                                <div style="width: 260px">
                                    选择【是】，普票生成采购单据后，税率为0，单据金额和单据价税合计等于发票的价税合计金额
                                </div></template>
                            <i class="question-icon"></i>
                        </el-tooltip>
                    </div>
                    <el-radio-group v-model="NotParticipatingDeductions">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="confirmGenerate">确定</a>
            </div>
        </div>
    </el-dialog>

    <!-- 发票识别导入 -->
    <el-dialog
        v-model="recognizeImportDialog"
        :title="invoiceCategory === '10070' ? '导入销项发票' : '导入进项发票'"
        center
        width="440"
        class="custom-confirm dialogDrag"
    >
        <div class="recognize-content" v-dialogDrag>
            <div class="recognize-drag">
                <el-upload
                    class="upload-demo"
                    drag
                    multiple
                    :auto-upload="true"
                    :show-file-list="false"
                    action="#"
                    :on-change="onChange"
                    :http-request="uploadInvoiceFile"
                >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">点击或拖拽上传</div>
                    <template #tip>
                        <div class="el-upload__tip">注：支持上传PDF、OFD格式的发票文件，识别后导入系统</div>
                        <div style="color: var(--el-color-error); font-size: 12px; margin-top: 5px">
                            最高可支持同时上传{{ limitCount }}张发票文件
                        </div>
                    </template>
                </el-upload>
                <div v-if="fileList.length">
                    <el-scrollbar class="file-list-wrap" :always="true" :max-height="150">
                        <ul class="file-list">
                            <li class="file-list-item" v-for="item in fileList" :key="item.file.uid">
                                <span> {{ item.file.name }} </span>
                                <div class="file-list-oprate">
                                    <span @click="deleteFile(item.file.uid)">删除</span>
                                    <span @click="downloadFile(item)">下载</span>
                                </div>
                            </li>
                        </ul>
                    </el-scrollbar>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button ml-10" @click="comfirmRecognizeImport">识别并导入</a>
                <a class="button ml-10" @click="cancelRecognizeImport">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 发票导入结果 -->
    <el-dialog
        v-model="importResultDialog"
        :title="invoiceCategory === '10070' ? '销项发票导入结果' : '进项发票导入结果'"
        center
        width="600"
        class="custom-confirm dialogDrag"
    >
        <div class="importResultDialog" v-dialogDrag>
            <Table
                ref="importResultTableRef"
                :loading="loading"
                :data="importResultTableData"
                :columns="importResultColumns"
                :page-is-show="false"
                :height="258"
                :empty-text="emptyText"
                :scrollbar-show="true"
                :tableName="setModule"
            >
            </Table>
        </div>
        <div class="buttons">
            <a class="button solid-button ml-10 importResultDialog-disabled" v-if="!completeFlag">导入加载中</a>
            <a class="button solid-button ml-10" v-else @click="showSuccessImportInvoice">查看</a>
            <a class="button ml-10" @click="closeImportResultDialog">关闭</a>
        </div>
    </el-dialog>
    <!-- 指定项目弹窗 -->
    <DialogBatchProject
        ref="dialogProjectRef"
        v-model="AlterProjectShow"
        aatype="10005"
        name="项目"
        @handle-sure="handleBatchEditProject"
        @update:override="handleOverride"
    ></DialogBatchProject>

    <!-- 快速新增 -->
    <el-dialog 
        v-model="quickInvoiceDialog" 
        :title="'快速新增'" 
        center 
        width="470" 
        modal-class="modal-class"
        class="custom-confirm dialogDrag"
        custom-class="quickInvoice-dialog"
        @close="quickInvoiceDialogClose"
    >
        <template #header>
            <div class="el-dialog__title" :style="isErp ? 'line-height: 65px; text-align: left': ''">
                <span>快速新增</span>
                <el-popover
                    placement="right-start"
                    :width="isErp ? 340 : 180"
                    trigger="hover"
                    :show-arrow="true"
                    :popper-style="{ padding: '6px' }"
                    content="录入发票要素，可以快速新增发票；目前仅支持五年内的增值税发票查验"
                >
                    <template #reference>
                        <span class="help-icon"></span>
                    </template>
                </el-popover>
            </div>
        </template>
        <div class="quickInvoice-dialog-form" v-loading="addLoading" v-dialogDrag>
            <el-form ref="invoiceFormRef" inline :model="invoiceForm">
                <el-form-item
                    label="发票代码:"
                    :show-message="false"
                    prop="invoiceCode"
                    class="line-item required"
                >
                    <Tooltip :content="invoiceForm.invoiceCode" :isInput="true">
                        <el-input
                            style="width: 220px;"
                            ref="codeRef"
                            class="line-item-field"
                            maxlength="20"
                            placeholder="请输入发票代码"
                            :disabled = "isCodeDisable"
                            v-model="invoiceForm.invoiceCode"
                            @input="invoiceCodeInput"
                            @keydown.enter="numberRef.focus()"
                        />
                    </Tooltip>
                </el-form-item>
                <el-form-item
                    label="发票号码:"
                    :show-message="false"
                    prop="invoiceNum"
                    class="line-item required"
                >
                    <Tooltip :content="invoiceForm.invoiceNum" :isInput="true">
                        <el-input
                            style="width: 220px;"
                            ref="numberRef"
                            class="line-item-field"
                            maxlength="20"
                            placeholder="请输入发票号码/全电发票号码"
                            v-model="invoiceForm.invoiceNum"
                            @input="invoiceNumInput"
                            @keydown.enter="dateRef.focus()"
                        />
                    </Tooltip>
                </el-form-item>
                <el-form-item
                    label="发票日期:"
                    :show-message="false"
                    :status-icon="false"
                    class="line-item required"
                >
                    <el-date-picker
                        ref="dateRef"
                        v-model="invoiceForm.invoiceDate"
                        value-format="YYYYMMDD"
                        type="date"
                        clearable
                        placeholder="请选择开票日期"
                        :disabled-date="disabledDateFun"
                        @keydown.enter="moneyRef.focus()"
                    />
                </el-form-item>
                <el-form-item
                    :label="quickInvoiceType !== 1 ? '开具金额:' : '校验码:'"
                    :show-message="false"
                    prop="invoiceMoney"
                    class="line-item required"
                >
                    <Tooltip :content="invoiceForm.invoiceMoney" :isInput="true">
                        <el-input
                            style="width: 220px;"
                            ref="moneyRef"
                            class="line-item-field"
                            :placeholder="getPlaceholder(quickInvoiceType)"
                            @input="invoiceMoneyInput"
                            v-model="invoiceForm.invoiceMoney"
                        />
                    </Tooltip>
                </el-form-item>
            </el-form>
        </div>
        <div class="buttons">
            <a class="button ml-10" @click="quickSaveFu(0)">保存</a>
            <a class="button solid-button ml-10" @click="quickSaveFu(1)">保存并新增</a>
        </div>
    </el-dialog>
    <el-dialog 
        v-model="isJumpAccountDialog" 
        title="提示" 
        center 
        width="470" 
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
        custom-class="quickInvoice-dialog"
    >
        <div class="quickInvoice-dialog-form" v-if="isJumpAccountDialogTipShow" v-dialogDrag>
            <el-form ref="AccountSetRef" inline :model="AccountSetForm">
                <el-form-item
                    label="公司名称:"
                    :show-message="false"
                    prop="name"
                    class="line-item accountSet required"
                >
                    <Tooltip :content="AccountSetForm.name" :isInput="true">
                        <el-autocomplete
                            :teleported="false"
                            :fit-input-width="true"
                            v-model="AccountSetForm.name"
                            :fetch-suggestions="querySearch"
                            class="inline-input w-50"
                            placeholder="请输入公司名称"
                            style="width: 220px"
                            @select="selectName"
                        >
                            <template v-slot:default="{ item }">
                                <Tooltip :content="item.value" :line-clamp="2" placement="right" :maxWidth='400' :teleported="true">
                                    <div style="line-height:18px">{{ item.value }}</div>
                                </Tooltip>
                            </template>
                        </el-autocomplete>
                    </Tooltip>
                </el-form-item>
                <el-form-item
                    label="统一社会信用代码:"
                    :show-message="false"
                    prop="code"
                    class="line-item accountSet required"
                >
                    <Tooltip :content="AccountSetForm.code" :isInput="true">
                        <el-input
                            style="width: 220px;"
                            class="line-item-field"
                            placeholder="请输入统一社会信用代码"
                            v-model="AccountSetForm.code"
                        />
                    </Tooltip>
                </el-form-item>            
            </el-form>
            <div class="info-tip">
                <el-icon class="tip-icon"><WarningFilled /></el-icon>
                <span class="tip-txt">请先完善公司信息。快速新增录入发票号码等要素即可获取发票明细；同时支持自动识别进销项发票</span>
            </div>
        </div>
        <div class="jump-tip" v-else :style="isErp ? 'text-align: left': ''">{{ isJumpAccountDialogTip }}</div>
        <div class="buttons">
            <a class="button ml-10" @click="isJumpAccountDialog = false">取消</a>
            <a class="button solid-button ml-10" @click="clickAccountSet">{{ isJumpAccountDialogBtn }}</a>
        </div>
    </el-dialog>
    <InvoicePrint
        v-model:printDialogShow="printDialogVisible"
        :printData="printInfo"
        :title="invoiceCategory === '10070' ? '销项发票列表打印' : '进项发票列表打印'"
        :dir-disabled="showAll"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3, getDefaultParams(), printApi)"
    />
    <!-- 业财生成单据 -->
    <el-dialog 
        v-model="generateBillShowErp" 
        title="提示" 
        center 
        width="550" 
        class="custom-confirm dialogDrag"
    >
        <div class="generate-bill-content erpBill" v-dialogDrag>
            <div class="generate-main">
                <div class="generate-result">
                    <div>您已选择 <span class="orange">{{ billTotalCount }}</span> 张发票生成{{ invoiceCategory === "10070" ? "销售单据" : "采购单据" }}：</div>
                    <div class="result">
                        <div>
                            生成{{ invoiceCategory === "10070" ? "销售出库" : "采购入库" }}单：<span class="orange">{{ erpPositiveCount }}张</span>
                        </div>
                        <div>
                            生成{{ invoiceCategory === "10070" ? "销售" : "采购" }}退货单：<span class="orange">{{ erpNegativeCount }}张</span>
                        </div>
                        <div>
                            已跳过：<span class="orange">{{ erpDropCount }}张</span>
                        </div>
                    </div>
                </div>
                <div class="generate-notice">
                    <div class="generate-container">
                        <img src="@/assets/Icons/hint.png" />
                        <div class="generate-txt">
                            <div>1.已作废发票/无商品明细发票/无数量发票/已关联单据的发票会被跳过，不支持操作；</div>
                            <div>2.保质期、批次、序列号商品不支持匹配；</div>
                            <div>3.生成单据的数量小数位是{{ quantityFloat }}；</div>
                        </div>
                    </div>
                </div>
                <div class="generate-bot">
                    <span>发票对应生成单据：</span>
                    <div class="generate-bot-rt">
                        <el-radio-group v-model="genarateType">
                            <el-radio :label="0">一张发票生成一张单据</el-radio>
                            <el-radio :label="1">相同开票日期相同{{ invoiceCategory === "10070" ? "客户" : "供应商" }}生成一张单据</el-radio>
                            <el-radio :label="2">相同{{ invoiceCategory === "10070" ? "客户" : "供应商" }}生成一张单据</el-radio>
                        </el-radio-group>
                        <div class="mt-10" v-if="invoiceCategory === '10080'">
                            <el-checkbox v-model="NotParticipatingDeductionsErp" label="普通发票不参与抵扣"></el-checkbox>
                            <el-tooltip effect="light" placement="top-start">
                                <template #content>
                                    <div style="width: 180px">
                                        生成单据后，普票税率为0，单据金额和单据价税合计等于发票的价税合计金额
                                    </div></template>
                                <i class="question-icon" style="top: -10px"></i>
                            </el-tooltip>
                        </div>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="confirmGenerateErp">确定</a>
                <a class="button" @click="generateBillShowErp = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog
        v-model="eisRelationTipDialog"
        class="custom-confirm dialogDrag"
        title="提示" 
        center 
        width="400">
        <div class="eis-relation-dialog" v-dialogDrag>
            <div class="eis-tip">
                <a class="link" @click="globalWindowOpen('https://help.ningmengyun.com/#/yfp/commonPro?subMenuId=100112&answerId=34')">查看帮助</a>
                <div>该账套暂未关联云发票企业，请先关联！</div>
            </div>
            <div class="buttons">
                <a class="button solid-button mr-20" @click="toEisRelationPage">去关联</a>
                <a class="button" @click="eisRelationTipDialog = false">取消</a>
            </div>
        </div>
    </el-dialog>

</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getUrlSearchParams, globalWindowOpen, globalExport, globalWindowOpenPage } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { showExpiredDialog } from "@/util/showExpiredDialog";
import type { 
    IInvoiceTypeListItem, 
    IInvoicesGenerateBillModel, 
    IImportResultTableData, 
    QuickAddInvoiceForm,
    IInvoicesGenerateBillModelErp,
    IEisPermissionRes
} from "../types";
import { isInWxWork } from "@/util/wxwork";
import { checkHasScmSettingsWrapper } from "@/util/scm";
import { computed } from "vue";
import { watch } from "vue";
import ElOption from "@/components/Option/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import Select from "@/components/Select/index.vue";
import { CaretBottom, WarningFilled } from "@element-plus/icons-vue";
import { openScmTab } from "@/util/scm";
import { watchEffect } from "vue";
import { checkPermission } from "@/util/permission";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import InvoicePrint from "@/components/PrintDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { UploadRequestOptions } from "element-plus";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import DialogBatchProject from "./DialogBatchProject.vue"
import type { FormInstance} from "element-plus";
import Tooltip from "@/components/Tooltip/index.vue";
import { getCompanyList } from "@/util/getCompanyList";
import type { ICompanyInfo } from "@/api/getCompanyList";
import dayjs from "dayjs";
import { getCookie, setCookie } from "@/util/cookie";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { checkBillInportPermission, urlRoute, urlRouteName } from "../utils";
import { erpCreateTab } from "@/util/erpUtils";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";
import usePrint from "@/hooks/usePrint";
import { ElAlert } from "@/util/confirm"
import { useEisInfoStoreHook } from "@/store/modules/eis"
import { checkInvoicePermissionApi } from "@/api/eis"

const _ = getGlobalLodash();
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;
const props = defineProps({
    cate: {
        type: String,
        required: true,
    },
    checkCanSearch: {
        type: Function,
        required: true,
    },
    addInvoiceOptions: {
        type: Array<IInvoiceTypeListItem>,
        required: true,
    },
    searchInfo: {
        type: Object,
        default: () => {},
    },
    filterParams: {
        type: Object,
        default: () => {},
    },
    checkedTableData: {
        type: Array,
        default: () => [],
    },
    businessSalesOptions: {
        type: Array<IBusinessTypeItem>,
        default: () => [],
    },
    businessPurchaseOptions: {
        type: Array<IBusinessTypeItem>,
        default: () => [],
    },
    hasInvoiceTask: {
        type: Boolean,
        default: false,
    },
    exportInvoiceIds: {
        type: Boolean,
        default: false,
    },
    isProjectAccountingEnabled: {
        type: Boolean,
        default: false,
    },
    finishedInvoiceTaskTime: {
        type: String,
        default: "",
    },
    sortField: {
        type: Number,
        default: 0,
    },
    sortOrder: {
        type: Number,
        default: -1,
    },
    priceFloat: {
        type: Number,
        default: 0,
    },
    quantityFloat: {
        type: Number,
        default: 0,
    },
    scmProductTypeVal: {
        type: Number,
        default: 0,
    },
    scmAsidVal: {
        type: Number,
        default: 0,
    },
    showAll: {
        type: Boolean,
        default: false,
    },
});
interface IBusinessTypeItem {
    label: string;
    value: string;
}

const emit = defineEmits([
    "checkedInvoiceType",
    "optionsSuccess",
    "goMasterPlate",
    "goGenerateVoucher",
    "showJournalSettings",
    "showFetchInvoice",
    "showSuccessImportInvoice",
    "BatchUpdateProjectDialog",
    "quickAddInvoiceSuccess",
    "openFetchInvoiceTaskShow",
    "associateVoucher"
]);
const invoiceTemplateDownList = ref(false);
let invoiceTypeChecked = ref<boolean>(false);
let certificationShow = ref<boolean>(false);
let certResultShow = ref<boolean>(false);
let certSuccess = ref<number>();
let certJump = ref<number>();
let batchOption = ref<number>();
const batchTitle = ref<string>();
let importShow = ref<boolean>(false);
const generateBillShow = ref<boolean>(false);
const generateBillShowErp = ref<boolean>(false);
const billTotalCount = ref<number>(0);
const billSuccessCount = ref<number>(0);
const billFailCount = ref<number>(0);
let invoiceCategory = computed(() => {
    return props.cate;
});

const invoicecreateVoucherCanView = ref(false);
const invoicevouchertemplateCanView = ref(false);
const allCanView = ref(false);
const bulkOperationCanView = ref(false);
const addButttonWiden = ref(false);
watchEffect(() => {
    invoicecreateVoucherCanView.value = false;
    invoicevouchertemplateCanView.value = false;
    allCanView.value = false;
    bulkOperationCanView.value = false;
    if (invoiceCategory.value === "10070") {
        if (checkPermission(["invoice-output-cancreatevoucher"]) && checkPermission(["invoicevouchertemplate-canview"])) {
            allCanView.value = true;
        } else if (checkPermission(["invoice-output-cancreatevoucher"])) {
            invoicecreateVoucherCanView.value = true;
        } else if (checkPermission(["invoicevouchertemplate-canview"])) {
            invoicevouchertemplateCanView.value = true;
        }
        if (
            checkPermission(["invoice-output-candelete"]) ||
            checkPermission(["invoice-output-canedit"]) ||
            checkPermission(["invoice-output-cancreatewarehousingout"])
        ) {
            bulkOperationCanView.value = true;
        }
        addButttonWiden.value =
            window.isErp &&
            !checkPermission(["invoice-output-candelete"]) &&
            !checkPermission(["invoice-output-canexport"]) &&
            !checkPermission(["invoice-output-canimport"]);
    } else {
        if (checkPermission(["invoice-input-cancreatevoucher"]) && checkPermission(["invoicevouchertemplate-canview"])) {
            allCanView.value = true;
        } else if (checkPermission(["invoice-input-cancreatevoucher"])) {
            invoicecreateVoucherCanView.value = true;
        } else if (checkPermission(["invoicevouchertemplate-canview"])) {
            invoicevouchertemplateCanView.value = true;
        }
        if (
            checkPermission(["invoice-input-candelete"]) ||
            checkPermission(["invoice-input-canedit"]) ||
            checkPermission(["invoice-input-cancreatewarehousing"])
        ) {
            bulkOperationCanView.value = true;
        }
    }
});
//去云发票
const eisRelationTipDialog = ref(false);
const toEisRelationPage = () => {
  if (!checkPermission(["yfprelation"])) {
    ElNotify({
      message: "暂无权限，请联系账套管理员处理！",
      type: "warning",
    });
    return;
  }
  globalWindowOpenPage("/Settings/EisRelation", "关联云发票");
  eisRelationTipDialog.value = false;
};
const showToEisBtn = computed(() => checkPermission(["invoice-output-invoicing"]) && !isErp.value && invoiceCategory.value === "10070");
const toInvoiceSystem = () => {
  const { isRelation, eisRelationData } = useEisInfoStoreHook();
  if (!isRelation) {
    eisRelationTipDialog.value = true;
    return;
  }
  //已关联
  checkInvoicePermissionApi().then((res: IResponseModel<IEisPermissionRes>) => {
    if (res.state === 1000) {
      // 判断的是否有开票管理权限 没有权限提示
      if (res.data.hasInvoiceIssuePermission) {
        globalWindowOpen(window.fpHost + `/ImmediateInvoice?eiscid=${eisRelationData?.companyId}&stay=true`);
      } else {
        const { companyName, adminPhoneNumber } = res.data;
        ElAlert({
          message: `暂无关联发票企业：${companyName}的“开票管理”权限，请联系云发票企业管理员：${adminPhoneNumber}为您开通权限后再操作；您也可以选择去创建发票企业。`,
          options: {
            confirmButtonText: "去创建",
            cancelButtonText: "已联系",
          },
        }).then((r) => {
          if (r) {
            globalWindowOpen(window.fpHost);
          }
        });
      }
    }
  });
};


//完善公司信息弹窗
const AccountSetRef = ref<FormInstance>();
const AccountSetForm = reactive({
    name: "",
    code: "",
});
const openFetchInvoiceTaskShow = ()=> {
    emit('openFetchInvoiceTaskShow');
}
const visibleTaskTime = ref(false);
function enterCheckScreenSize() {
    const screenWidth = window.innerWidth;
    // 根据屏幕宽度和高度判断隐藏时间到按钮悬浮
    if (screenWidth < 1480 && props.finishedInvoiceTaskTime) {
        visibleTaskTime.value = true;
    } else {
        visibleTaskTime.value = false;
    }
}
function leaveCheckScreenSize() {
    visibleTaskTime.value = false;
}

//快速新增
const quickInvoiceDialog = ref(false);
const asId = useAccountSetStore().accountSet!.asId;
const isJumpAccountDialog = ref(false);
const isJumpAccountDialogBtn = ref("");
const isJumpAccountDialogTip = ref("");
const isJumpAccountDialogTipShow = ref(false);
const quickAddInvoice = () => {
    request({
        url: `/api/AccountSetOnlyAuth/Info?asId=${asId}`,
    }).then((res: any) => {
        if(res.state === 1000) {
            AccountSetForm.name = res.data.taxpayerName;
            AccountSetForm.code = res.data.taxNumberS;
            if (res.data.taxpayerName.trim() && res.data.taxNumberS.trim()) { //名称和税号都存在
                quickInvoiceDialog.value = true;
            } else { //不存在
                if (isMainRole()) { //是管理员
                    isJumpAccountDialogBtn.value = "确定";
                    isJumpAccountDialogTipShow.value = true;
                    isJumpAccountDialog.value = true;
                } else { //不是管理员
                    ElNotify({
                        message: "请联系账套管理员完善公司信息",
                        type: "warning",
                    });
                }
            } 
        }
    });
    
}
let invoiceFormRef = ref<FormInstance>();
let invoiceForm = reactive<QuickAddInvoiceForm>({
    invoiceCode: "",
    invoiceNum: "",
    invoiceDate: "",
    invoiceMoney: "",
});
const codeRef = ref();
const numberRef = ref();
const dateRef = ref();
const moneyRef = ref();
const quickInvoiceType = ref(0); //专票0，普票1, 全电票2, 二手车3, 机动车销售统一发票4
const isCodeDisable = ref(false);
const checkInvoiceType = (val: string) => {
    let eightVal = val.slice(7, 8); //截取第8位进行判断
    quickInvoiceType.value = 0;
    if(val.length === 10) { //发票代码长度10位
        if (eightVal === "6" || eightVal === "3") { //普票
            quickInvoiceType.value = 1;
        } else if(eightVal === "1" || eightVal ==="2" || eightVal === "5" || eightVal === "7") { //专票
            quickInvoiceType.value = 0;
        }
    } else { //发票代码长度12位
        let twelveVal = val.slice(10); //截取第11-12位进行判断
        if (val.charAt(0) === '0' && ["04", "05", "06", "07", "11", "12"].includes(twelveVal)) {  
            quickInvoiceType.value = 1;  
        } else if (val.charAt(0) === '0' && twelveVal === "17") {
            quickInvoiceType.value = 3;
        } else if (val.charAt(0) !== '0' && eightVal === "2") {
            quickInvoiceType.value = 4;
        }
    }
}
const invoiceCodeBlur = () => {
    if (invoiceForm.invoiceCode.length === 10 || invoiceForm.invoiceCode.length === 12) {
        checkInvoiceType(invoiceForm.invoiceCode);
    } else {
        quickInvoiceType.value = 0;
    }
}
const invoiceCodeInput = (val: string) => {
    invoiceForm.invoiceCode = val.replace(/[^\d]/g, ""); //只能输入数字
    if (val.length >=20 ) {
        quickInvoiceType.value = 2;
        invoiceForm.invoiceNum = invoiceForm.invoiceCode;
        invoiceForm.invoiceCode = "";
        isCodeDisable.value = true;
    } else {
        invoiceCodeBlur();
    }
}
const getPlaceholder = (quickInvoiceType: number) => {
    switch (quickInvoiceType) {
        case 0:
            return '请输入开具金额(不含税)';
        case 1:
            return '请输入校验码后六位';
        case 2:
            return '请输入开具金额(含税)';
        case 3:
            return '请输入车价合计';
        case 4:
            return '请输入开具金额(不含税)';
    }
}
const disabledDateFun = (date: Date)=> {
    // return date.getTime() < Date.now() - 366 * 24 * 3600 * 1000 || date.getTime() > Date.now() - 24 * 3600 * 1000;
    const now = new Date();
    const fiveYearsAgo = new Date(now);
    fiveYearsAgo.setFullYear(now.getFullYear() - 5);

    return date.getTime() < fiveYearsAgo.getTime() || date.getTime() > now.getTime();
}
const invoiceNumInput = (val: string) => {
    invoiceForm.invoiceNum = val.replace(/[^\d]/g, ""); //只能输入数字
    if (val.length >= 20) {
        quickInvoiceType.value = 2;
        invoiceForm.invoiceCode = "";
        isCodeDisable.value = true;
    } else {
        if(isCodeDisable.value) {
            quickInvoiceType.value = 0;
        } 
        isCodeDisable.value = false;
    }
}
let taxAmountHistory: string[] = [];
const invoiceMoneyInput = (val: string) => {
    if (quickInvoiceType.value === 1) { //普票校验码-只能输入数字
        invoiceForm.invoiceMoney = val.replace(/[^\d]/g, ""); 
    } else { //专票/数电票-金额只能数字小数点负号
        taxAmountHistory.push(val);
        if (val === "" || val === "-") {
            invoiceForm.invoiceMoney = val;
            return;
        }
        if ((isNaN(parseFloat(val)) || !isFinite(val)) && val !== "-") {
            taxAmountHistory.pop();
            invoiceForm.invoiceMoney = taxAmountHistory[taxAmountHistory.length - 1];
            return;
        }
        invoiceForm.invoiceMoney = String(invoiceForm.invoiceMoney).replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
    }
}
const quickSaveCheck = () => {
    if(!invoiceForm.invoiceCode.trim()) {
        if(quickInvoiceType.value!==2) { //排除数电票
            ElNotify({
                message: "请输入发票代码",
                type: "warning",
            });
            return false;
        }
    }
    if(!invoiceForm.invoiceNum.trim()) {
        ElNotify({
            message: "请输入发票号码",
            type: "warning",
        });
        return false;
    }
    if(!invoiceForm.invoiceDate) {
        ElNotify({
            message: "请选择开票日期",
            type: "warning",
        });
        return false;
    }
    if(!invoiceForm.invoiceMoney.trim()) {
        let msg = (quickInvoiceType.value !== 1) ? "请输入开具金额" : "请输入校验码后六位"
        ElNotify({
            message: msg,
            type: "warning",
        });
        return false;
    }
    return true;
}
const addLoading = ref(false);
function quickSave(val: number) {
    if(!quickSaveCheck()) {
        return;
    }
    addLoading.value = true;
    request({
        url: `/api/Invoice/FastAdded`,
        method: "post",
        data: invoiceForm,
        headers: {
            "Content-Type": "application/json",
        },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            ElNotify({
                message: "保存成功",
                type: "success",
            });
            emit('quickAddInvoiceSuccess', res.data);
            resetInvoiceForm();
            if (val === 0) {
                quickInvoiceDialog.value = false;
            }
        } else if(res.state === 2000 && res.msg.includes("发票扫描结果不匹配，请检查发票购销方名称是否正确，或重新录入正确的公司信息")) {
            if (isMainRole()) {
                isJumpAccountDialogBtn.value = "重新录入";
                isJumpAccountDialogTip.value = "发票扫描结果不匹配，请检查发票购销方名称是否正确，或重新录入正确的公司信息";
                isJumpAccountDialogTipShow.value = false;
                isJumpAccountDialog. value = true;
                quickInvoiceDialog.value = false;
            } else {
                ElNotify({
                    message: "发票扫描结果不匹配，请检查发票购销方名称是否正确，或联系账套管理员录入正确的公司信息",
                    type: "warning",
                });
                return;
            }
        } else {
            ElNotify({
                message: res.msg,
                type: "warning",
            });
            return;
        }
    }).finally(()=>{
        addLoading.value = false;
    });
}
const quickInvoiceDialogClose = () => {
    resetInvoiceForm();
}
const roleLength = ref(0);
function getAccountRole() {
    request({ 
        url: "/api/PermissionsRole/GetRoleIdList", 
        method: "post" 
    }).then((res: IResponseModel<number[]>) => {
        roleLength.value = res.data.filter((p: number) => p + "" == "*********").length;
    }).catch((e)=>{
        ElNotify({
            message: "获取信息失败，请刷新页面后重试！",
            type: "warning",
        });
    })
}
getAccountRole();
function isMainRole() { //角色判断是否是管理员
    return checkPermission(["accountset-canedit"]) || (window.isErp && roleLength.value > 0);
}
const clickAccountSet = ()=> {
    if (isMainRole() ) { //管理员直接跳转修改
        if (isJumpAccountDialogBtn.value === "重新录入") { //重新录入按钮
            isJumpAccountDialogBtn.value = "确定";
            isJumpAccountDialogTipShow.value = true;
        } else { //确定按钮
            debounceSave();
        }
    } else {
        ElNotify({
            message: "请联系账套管理员完善公司信息",
            type: "warning",
        });
    }
}
//防止连点
const debounceSave = _.debounce(saveAccountSet, 300);
//保存
function saveAccountSet() {
    if (!AccountSetForm.name.trim()) {
        ElNotify({
            message: "亲，请输入公司名称！",
            type: "warning",
        });
        return;
    }
    if (!AccountSetForm.code.trim()) {
        ElNotify({
            message: "亲，请输入统一社会信用代码！",
            type: "warning",
        });
        return;
    }
    if (AccountSetForm.name.length > 64) {
        ElNotify({
            type: "warning",
            message: "亲，纳税人名称不能超过64个字，请修改后重试哦~",
        });
        return;
    }
    if (AccountSetForm.code && AccountSetForm.code != "") {
        const taxNumberPattern = /^(([0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10})|([0-9X]{15})|([0-9X]{17})|([0-9X]{20}))$/;
        if (!taxNumberPattern.test(AccountSetForm.code)) {
            ElNotify({
                type: "warning",
                message: "亲，统一社会信用代码格式有误，请认真检查！",
            });
            return;
        }
    }
    const data = {
        TaxPayerName: AccountSetForm.name,
        TaxPayerNumber: AccountSetForm.code,
    }
    request({ 
        url: "/api/AccountSet/SaveCompanyInfo", 
        method: "post",
        data, 
    }).then((res) => {
        if (res.state === 1000) {
            ElNotify({
                type: "success",
                message: "保存成功",
            });
            isJumpAccountDialog.value = false;
            quickInvoiceDialog.value = true;
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    })
}

const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};
const resetInvoiceForm = ()=> {
    invoiceForm.invoiceCode = "";
    invoiceForm.invoiceNum = "";
    invoiceForm.invoiceDate = "";
    invoiceForm.invoiceMoney = "";
    quickInvoiceType.value = 0;
    isCodeDisable.value = false;
    taxAmountHistory = [];
}
function selectName(item: any) {
    AccountSetForm.name = item.value;
    AccountSetForm.code = item.creditCode;
}

const timer = ref(0); //防止点击多次重复提交的时间戳
const quickSaveFu = (val: number) => {
    if(!timer.value) {
        quickSave(val);
        timer.value = setTimeout(() => {
            timer.value = 0; 
        }, 500); 
    }
}
const isWxwork = ref(isInWxWork());
const isErp = ref(window.isErp);
const isThrid = ref(useThirdPartInfoStoreHook().isThirdPart);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
let businessType = ref<string>();
const businessTypeOptions = computed(() => {
    return invoiceCategory.value === "10070" ? props.businessSalesOptions : props.businessPurchaseOptions;
});
watch(
    businessTypeOptions,
    () => {
        businessType.value = businessTypeOptions.value[0]?.value;
    },
    { immediate: true }
);

let IssueDate = ref<string>();
function addInvoiceType(item: IInvoiceTypeListItem) {
    invoiceTypeChecked.value = true;
    emit("checkedInvoiceType", item);
}

const showFetchInvoice = () => {
    emit("showFetchInvoice");
};
function genertaeErpVoucher() {
    const billType = invoiceCategory.value === "10070" ? "2010" : "2020";
    const params = { billType, searchStartDate: props.searchInfo.startDate, searchEndDate: props.searchInfo.endDate };
    globalWindowOpenPage("/Erp/BusinessVoucher?" + getUrlSearchParams(params), "业务生成凭证");
}
const showJournalTemplate = () => {
    emit("goMasterPlate");
};
const openVoucherFromJournal = () => {
    emit("goGenerateVoucher");
};
const showJournalSettings = () => {
    emit("showJournalSettings");
};
// 1:认证、2:删除、3:指定业务类别、4：取消认证
const batchOperation = (optionType: number) => {
    batchOption.value = optionType;
    batchTitle.value = optionType === 1 ? "批量认证" : optionType === 2 ? "提示" : optionType === 3 ? "批量指定发票业务类型" : "取消认证";
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: `请选择发票数据后${optionType === 1 ? "认证" : optionType === 2 ? "删除" : optionType === 3 ? "指定业务类型": '取消认证'}`,
        });
        return false;
    }
    if (optionType === 3) {
        let hasVoucherIndex = props.checkedTableData.findIndex((v: any) => v.pid !== 0 && v.vid !== 0);

        if (hasVoucherIndex >= 0) {
            ElNotify({
                type: "warning",
                message: "发票数据已生成凭证，无法修改",
            });
            return false;
        }
    }
    certificationShow.value = true;
};
const certificationSure = (optionType: number) => {
    if (optionType === 1 && !IssueDate.value) {
        ElNotify({
            type: "warning",
            message: "请选择认证日期",
        });
        return false;
    }
    let noneDataNum = 0;
    let hasDataNum = 0;
    let invoiceIds: number[] = [];
    let flag = true;

    for (let i = 0; i < props.checkedTableData.length; i++) {
        const v: any = props.checkedTableData[i];
        if (optionType === 1) {
            if (v.issueDateText && v.issueDateText !== "0001-01-01") {
                hasDataNum += 1;
            } else {
                noneDataNum += 1;
                invoiceIds.push(v.invoiceId);
            }
            certificationShow.value = false;
        } else if(optionType === 4) {
            if (v.issueDateText && v.issueDateText !== "0001-01-01") {
                noneDataNum += 1;
                invoiceIds.push(v.invoiceId);
            } else {
                hasDataNum += 1;
            }
            certificationShow.value = false;
        } else {
            if (v.invoiceId !== 0) {
                if (v.pid !== 0 && v.vid !== 0) {
                    flag = false;
                    ElNotify({ type: "warning", message: `发票数据已生成凭证，无法${optionType === 2 ? "删除" : "修改"}` });
                    certificationShow.value = false;
                    return false;
                } else if (v.isOffseted) {
                    flag = false;
                    ElNotify({ type: "warning", message: `发票数据已核销，无法${optionType === 2 ? "删除" : "修改"}` });
                    certificationShow.value = false;
                    return false;
                } else if (optionType === 2 && v.billId !== 0) {
                    let billType = invoiceCategory.value === "10070" ? "出库单" : "入库单";
                    flag = false;
                    ElNotify({ type: "warning", message: `发票数据已生成${billType}，无法删除` });
                    certificationShow.value = false;
                    return false;
                } else {
                    invoiceIds.push(v.invoiceId);
                }
            }
        }
    }
    if ((optionType === 1 || optionType === 4) && !invoiceIds.length) {
        certResultShow.value = true;
        certSuccess.value = noneDataNum;
        certJump.value = hasDataNum;
        flag = false;
        return false;
    }
    if (flag) {
        handleBatchOperation(optionType, invoiceIds, noneDataNum, hasDataNum);
    }
};
const handleBatchOperation = (optionType: number, invoiceIds: number[], noneDataNum: number, hasDataNum: number) => {
    let params =
        optionType === 1
            ? {
                  issueDate: IssueDate.value,
              }
            : optionType === 2
            ? {}
            : {
                  businessTypeId: businessType.value,
              };
    let obj: any = {
        2: {
            route: "Batch",
            method: "delete",
        },
        1: {
            route: "IssueDataBatch",
            method: "put",
        },
        3: {
            "10070": {
                route: "SalesBusinessTypeBatch",
                method: "put",
            },
            "10080": {
                route: "PurchaseBusinessTypeBatch",
                method: "put",
            },
        },
        4: {
            route: "IssueDataBatchCancel",
            method: "put",
        },
    };
    request({
        url: optionType === 3 ? `/api/Invoice/${obj[optionType][invoiceCategory.value].route}` : `/api/Invoice/${obj[optionType].route}`,
        method: optionType === 3 ? `${obj[optionType][invoiceCategory.value].method}` : `${obj[optionType].method}`,
        data: invoiceIds,
        params,
        headers: {
            "Content-Type": "application/json",
        },
    })
        .then((res: any) => {
            if (res.data) {
                certificationShow.value = false;
                if (optionType === 1 || optionType === 4) {
                    certResultShow.value = true;
                    certSuccess.value = noneDataNum;
                    certJump.value = hasDataNum;
                } else {
                    ElNotify({
                        type: "success",
                        message: `${optionType === 1 ? "认证" : optionType === 2 ? "删除" : "修改"}成功`,
                    });
                }

                emit("optionsSuccess", optionType, invoiceIds.length);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
                certificationShow.value = false;
            }
        })
        .catch((e) => {
            ElNotify({
                type: "error",
                message: `${optionType === 1 ? "认证" : optionType === 2 ? "删除" : "修改"}失败，请稍候重试或联系系统管理员`,
            });
        });
};
let invoiceIds = "";
function checkCanExport(params: any) {
    if (params.startVDate && !params.endVDate) {
        ElNotify({ type: "warning",  message: "亲，凭证结束日期不能为空！" });
        return false;
    }
    if (!params.startVDate && params.endVDate) {
        ElNotify({ type: "warning",  message: "亲，凭证起始日期不能为空！" });
        return false;
    }
    return true;
}
function getDefaultParams() {
    const { invoiceStatus, ...rest } = props.filterParams;
    let params = {
    ..._.cloneDeep(props.searchInfo),
    ...rest,
    sortOrder: props.sortOrder,
    sortField: props.sortField,
    };

    if (invoiceCategory.value === "10070") {
    ["invoiceIssueDateStatus", "issueStartDate", "issueEndDate"].forEach(key => delete params[key]);
    }
    return params;

}
const handleExport = (val: number) => {
    if (!props.checkCanSearch()) return;
    if (!checkCanExport(props.searchInfo)) return;
    let params = getDefaultParams();

    //exportType  0原来的导出，1 按导入模板导出
    let exportType = "&exportType=" + val;
    //发票识别导入成功后马上导出，成功导入的发票列表id
    let successInvoiceIds = "&invoiceIds=";
    if (props.exportInvoiceIds) {
        successInvoiceIds += invoiceIds;
    }
    globalExport(
        `/api/Invoice/${invoiceCategory.value === "10070" ? "Sales" : "Purchase"}Export?` +
            getUrlSearchParams(params) +
            exportType +
            successInvoiceIds
    );
};

const printApi = computed(() => {
    return `/api/Invoice/${invoiceCategory.value === "10070" ? "Sales" : "Purchase"}Print?showAll=${props.showAll}`;
});
const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    `${invoiceCategory.value === "10070" ? "sales" : "purchase"}`,
    printApi.value,
    {},
    true
);

watch(
    () => props.showAll,
    () => {
        printInfo.value.direction = props.showAll ? 1 : 0;
    }
);

const ImportWay = ref(0);
const handleImportClick = (val: number) => {
    importShow.value = true;
    ImportWay.value = val;
};
const downloadTemplate = () => globalExport(`/api/Invoice/ExportCommonTemplate?invoiceCategory=${invoiceCategory.value}`);

const uploadSuccess = (res: IResponseModel<boolean>) => {
    if (res.state === 1000) {
        ElNotify({
            type: "success",
            message: "导入成功！",
        });
        importShow.value = false;
        emit("optionsSuccess", -1);
        handleExpiredCheckData(ExpiredCheckModuleEnum.Invoice);

    } else {
        ElNotify({
            type: "warning",
            message: res.msg,
        });
    }
};
//指定项目弹窗
const AlterProjectShow = ref(false);
const dialogProjectRef = ref();
const batchOperationProject = () => {
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: `请选择发票数据后再指定项目`,
        });
        return false;
    }
    dialogProjectRef.value?.handleInit();
    AlterProjectShow.value = true;
}
//是否覆盖原项目
const override = ref(true);
const handleOverride = (val: boolean) => {
    override.value = val;
};
const handleBatchEditProject = (aaeId: string)=> {
    let list:number[] = [];
    props.checkedTableData.forEach((item: any) => {
        if (!override.value) { //判断是否覆盖
            if (!item.vid && item.projectId === 0) {
                list.push(item.invoiceId);
            }
        } else {
            if (!item.vid) {
                list.push(item.invoiceId);
            }
        }
    });
    if (list.length === 0) {
        if (window.isErp) {
            ElNotify({
                type: "warning",
                message: `成功：0，跳过：${props.checkedTableData.length}(已生成凭证的数据已跳过)`,
            });
        } else {
            ElNotify({
                type: "success",
                message: `成功：0，跳过：${props.checkedTableData.length}(已生成凭证的数据已跳过)`,
            });
        }
        return;
    }
    let projectId = Number(aaeId);
    request({
        url: `/api/Invoice/ProjectBatch?projectId=${projectId}`,
        method: "put",
        data: list,
    }).then((res) => {
        if (res.state === 1000) {
            emit("BatchUpdateProjectDialog");
            if (window.isErp) {
                ElNotify({
                    type: "warning",
                    message: `成功：${list.length}，跳过：${props.checkedTableData.length - list.length}(已生成凭证的数据已跳过)`,
                });
            } else {
                ElNotify({
                    type: "success",
                    message: `成功：${list.length}，跳过：${props.checkedTableData.length - list.length}(已生成凭证的数据已跳过)`,
                });
            }
        } else {
            ElNotify({
                message: res.msg || "保存失败了，请稍候重试或联系系统管理员",
                type: "warning",
            });
        }
    }).catch((err)=>{
        ElNotify({
            message: "保存失败了，请稍候重试或联系系统管理员",
            type: "warning",
        });
    });
}

//发票识别导入
const fileList = ref<UploadRequestOptions[]>([]);
const recognizeImportDialog = ref(false);
const errCount = ref(0); //所有非pdf/ofd格式文件的个数
const rightCount = ref(0); //所有pdf/ofd格式文件的个数
const totalCount = ref(0); //总上传文件个数
const currentErr = ref(0); //本次上传的非pdf/ofd格式文件的个数
const currentRight = ref(0); //本次上传的pdf/ofd格式文件的个数
const delCount = ref(0); //删除的pdf/ofd格式文件的个数
const handleRecognizeClick = () => {
    recognizeImportDialog.value = true;
};

const uploadInvoiceFile = (file: UploadRequestOptions) => {
    if (!/(\.pdf|\.ofd)$/i.test(file.file.name)) {
        errCount.value++;
        currentErr.value++;
    } else {
        rightCount.value++;
        currentRight.value++;
        fileList.value.push(file);
    }
    if (errCount.value + rightCount.value === totalCount.value) {
        if (currentErr.value > 0) {
            if (currentRight.value > 0) {
                ElNotify({
                    message: "PDF或OFD格式的文件上传成功,已跳过不支持的文件类型",
                    type: "success",
                });
            } else {
                ElNotify({
                    message: "选择的文件中没有PDF或OFD格式的文件",
                    type: "warning",
                });
            }
        } else if (currentRight.value > 0) {
            ElNotify({
                message: "PDF或OFD格式的文件上传成功",
                type: "success",
            });
        }
        currentErr.value = 0;
        currentRight.value = 0;
    }
};
const onChange = (file: any, list: any) => {
    if (!delCount.value) {
        totalCount.value = list.length;
    } else {
        totalCount.value = list.length - delCount.value;
    }
};
const downloadFile = (file: UploadRequestOptions) => {
    const blob = new Blob([file.file], { type: file.file.type });
    let a = document.createElement("a");
    a.download = file.file.name;
    a.href = URL.createObjectURL(blob);
    a.style.display = "none"; // 将a元素隐藏
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(a.href); // 释放URL对象
    document.body.removeChild(a);
};

const deleteFile = (uid: number) => {
    delCount.value++;
    rightCount.value--;
    fileList.value = fileList.value.filter((item) => item.file.uid != uid);
};

const cancelRecognizeImport = () => {
    recognizeImportDialog.value = false;
    fileList.value = [];
};
//发票导入结果
const setModule = "InvoiceImportResult"
const importResultDialog = ref(false);
const importResultTableData = ref<IImportResultTableData[]>([]);
const importResultColumns = ref<Array<IColumnProps>>([
    { label: "序号", prop: "no", minWidth: 40, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'no') },
    { label: "文件名", prop: "fileName", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'fileName') },
    { label: "发票号码", prop: "number", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'number') },
    { label: "导入状态", prop: "importStatusText", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'importStatusText') },
    { label: "原因", prop: "reason", minWidth: 160, headerAlign: "left", align: "left", resizable: false },
]);
const emptyText = ref(" ");
const importResultTableRef = ref();
let successInvoiceData = {
    endDate: "",
    invoiceIds: [],
    startDate: "",
};
const loading = ref<boolean>(false);
const limitCount = ref(50); //一次性最多导入发票的数量
const completeFlag = ref(false);
const onExceed = () => {
    ElNotify({
        message: `一次最多上传${limitCount.value}个文件`,
        type: "warning",
    });
};
const comfirmRecognizeImport = () => {
    if (fileList.value.length > limitCount.value) {
        onExceed();
        return;
    }
    if (!fileList.value.length) {
        ElNotify({
            message: "未上传识别导入的文件，请传入PDF或OFD格式的发票文件后再操作",
            type: "warning",
        });
        return;
    }
    completeFlag.value = false;
    const fileData = new FormData();
    fileList.value.forEach((file) => {
        fileData.append("file", file.file);
    });
    request({
        url: `/api/Invoice/ImportFile?category=${invoiceCategory.value}`,
        method: "post",
        data: fileData,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000 && res.data) {
            importResultDialog.value = true;
            recognizeImportDialog.value = false;
            fileList.value = [];
            handResultData(res.data).then((result) => {
                completeFlag.value = true;
                successInvoiceData = result?.invoice;
                if (successInvoiceData.invoiceIds !== undefined) {
                    invoiceIds = successInvoiceData.invoiceIds.join(",");
                }
            });
        } else {
            ElNotify({
                message: res.msg,
                type: "warning",
            });
            return;
        }
    });
};

async function handResultData(guid: string) {
    try {
        let res = await request({
            url: `/api/Invoice/GetImportResult?guid=${guid}`,
            method: "get",
        });
        if (res.state === 1000) {
            importResultTableData.value = res.data.importResult;
            importResultTableData.value.forEach((item) => {
                if (!item.importStatusText) {
                    item.importStatusText = "导入加载中...";
                }
            });
            if (res.data.handComplete) {
                // 导入结果已完成，展示最终结果
                return res.data;
            } else {
                // 如果handComplete为false，继续调用接口刷新结果
                await new Promise((resolve) => setTimeout(resolve, 1000));
                return handResultData(guid);
            }
        }
    } catch (error) {
        console.error("获取导入结果出错:", error);
    }
}

const showSuccessImportInvoice = () => {
    closeImportResultDialog();
    emit("showSuccessImportInvoice", successInvoiceData);
};

const closeImportResultDialog = () => {
    importResultTableData.value = [];
    importResultDialog.value = false;
};
const billText = computed(() => {
    return invoiceCategory.value === "10070" ? "销售出库单" : "采购入库单";
});

const scmProductType = ref(0);
const scmAsid = ref(0);
const cstId = ref(0)

// 生成销售出库单/入库采购单
function openBatchUpdateDialog() {
    // if (trialStatusStore.isTrial && trialStatusStore.isExpired) {
    //     tryShowPayDialog(1, "invoice");
    //     return;
    // }
    if (window.isErp) {
        generateScmBillInner();
    } else {
        checkHasScmSettingsWrapper(`自动生成${billText.value}据`).then((res) => {
            if (res.state === 1000) {
                scmProductType.value = res.data.scmProductType;
                scmAsid.value = res.data.scmAsid;
                cstId.value = res.data.cstId;
                generateScmBillInner();
            }
        });
    }
}
// 获取进销存关系后才能去判断进销存参数设置
function generateScmBillInner() {
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: `请选择发票数据后生成${billText.value}`,
        });
        return;
    }
    let invoiceIds: number[] = [];
    props.checkedTableData.forEach((v: any) => {
        invoiceIds.push(v.invoiceId);
    });
    request({
        url: "/api/ScmBill/GenerateBillByInvoices",
        method: "post",
        data: invoiceIds,
        headers: {
            "Content-Type": "application/json",
            scmProductType: scmProductType.value,
            scmAsid: scmAsid.value,
        },
    })
        .then((result: IResponseModel<IInvoicesGenerateBillModel>) => {
            if (result.state === 1000) {
                billSuccessCount.value = result.data.successCount;
                billFailCount.value = result.data.dropCount;
                billTotalCount.value = invoiceIds.length;

                if (result.data.successCount === 0) {
                    idFromScm.value = 0;
                    NotParticipatingDeductions.value = Number(getCookie("NotParticipatingDeductions" + asId));
                    generateBillShow.value = true;
                } else if (result.data.idFromScm !== 0) {
                    idFromScm.value = result.data.idFromScm;
                    NotParticipatingDeductions.value = Number(getCookie("NotParticipatingDeductions" + asId));
                    generateBillShow.value = true;
                    if (invoiceCategory.value === "10070") {
                        if (!result.data.isRefund) {
                            listType.value = 1021;
                        } else {
                            listType.value = 1022;
                        }
                    } else {
                        if (!result.data.isRefund) {
                            listType.value = 1011;
                        } else {
                            listType.value = 1012;
                        }
                    }
                } else {
                    ElNotify({
                        type: "warning",
                        message: `生成失败，请稍后重试或联系系统管理员`,
                    });
                }
            } else if (result.state === 2000) {
                ElNotify({
                    type: "warning",
                    message: result.msg,
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "生成失败，请稍后重试或联系系统管理员",
                });
            }
        })
        .catch((e) => {
            ElNotify({
                type: "warning",
                message: "生成失败，请稍后重试或联系系统管理员",
            });
        });
}

//业财生成单据
const erpPositiveCount = ref(0);
const erpNegativeCount = ref(0);
const erpDropCount = ref(0);
const NotParticipatingDeductionsErp = ref(true);
function openErpBill() {
    if (window.isErp) {  
        scmProductType.value = props.scmProductTypeVal;
        scmAsid.value = props.scmAsidVal;
        generateScmBillInnerErp();
    }
}
function generateScmBillInnerErp() {
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: "请选择发票",
        });
        return;
    }
    let invoiceIds: number[] = [];
    props.checkedTableData.forEach((v: any) => {
        invoiceIds.push(v.invoiceId);
    });
    listType.value = 0;
    request({
        url: "/api/ScmBill/ErpGenerateBillByInvoices",
        method: "post",
        data: invoiceIds,
        headers: {
            "Content-Type": "application/json",
            scmProductType: scmProductType.value,
            scmAsid: scmAsid.value,
        },
    }).then((result: IResponseModel<IInvoicesGenerateBillModelErp>) => {
        if (result.state === 1000) {
            erpPositiveCount.value = result.data.positiveCount;
            erpNegativeCount.value = result.data.negativeCount;
            erpDropCount.value = result.data.dropCount;
            billTotalCount.value = invoiceIds.length;

            if (result.data.positiveCount === 0 && result.data.negativeCount === 0 ) {
                idFromScm.value = 0;
                if (getCookie("NotParticipatingDeductionsErp" + asId)) {
                    NotParticipatingDeductionsErp.value = getCookie("NotParticipatingDeductionsErp" + asId) === "1";
                }
                generateBillShowErp.value = true;
            } else if (result.data.idFromScm !== 0) {
                idFromScm.value = result.data.idFromScm;
                if (getCookie("NotParticipatingDeductionsErp" + asId)) {
                    NotParticipatingDeductionsErp.value = getCookie("NotParticipatingDeductionsErp" + asId) === "1";
                }
                generateBillShowErp.value = true;
                
                const permissionMap = invoiceCategory.value === "10070" ? [1021, 1022] : [1011, 1012];    
                if (result.data.positiveCount) {  
                    if (checkBillInportPermission(permissionMap[0])) {  
                        listType.value = permissionMap[0];  
                        return;  
                    }  
                }  
                if (result.data.negativeCount) {  
                    listType.value = checkBillInportPermission(permissionMap[1]) ? permissionMap[1] : (result.data.positiveCount ? -1 : 0);  
                    return;  
                }
            } else {
                ElNotify({
                    type: "warning",
                    message: `生成失败，请稍后重试或联系系统管理员`,
                });
            }
        } else if (result.state === 2000) {
            ElNotify({
                type: "warning",
                message: result.msg,
            });
        } else {
            ElNotify({
                type: "warning",
                message: "生成失败，请稍后重试或联系系统管理员",
            });
        }
    }).catch((e) => {
        ElNotify({
            type: "warning",
            message: "生成失败，请稍后重试或联系系统管理员",
         });
    });
}

const idFromScm = ref(0);
const listType = ref(0);
const NotParticipatingDeductions = ref(0); //普通发票不参与抵扣，默认否0，是1
const genarateType = ref(0); //每张发票生成一个单据 0;相同发票日期和客户的发票生成一张单据 1;相同客户生成一张单据 2;

const confirmGenerate = () => {
    if (idFromScm.value !== 0) {
        let NotPartVal = NotParticipatingDeductions.value === 1;
        openScmTab(
            `/from_acc?asid=${scmAsid.value}&list_type=${listType.value}&fromInvoice=true&id=${idFromScm.value}&NotParticipatingDeductions=${NotPartVal}`+ (cstId.value ? `&serviceId=${cstId.value}` : ""),
            scmProductType.value
        );
    }
    setCookie("NotParticipatingDeductions" + asId, NotParticipatingDeductions.value.toString(), "d30");
    generateBillShow.value = false;
};
const confirmGenerateErp = () => {
    setCookie("NotParticipatingDeductionsErp" + asId, `${NotParticipatingDeductionsErp.value ? '1' : '0' }`, "d30");
    if (idFromScm.value !== 0) {
        generateBillShowErp.value = false;
        if (listType.value === -1) {
            let msg = invoiceCategory.value === "10070" ? "销售出库单和销售退货单" : "采购入库单和采购退货单"
            ElNotify({
                type: "warning",
                message: `您无${msg}的导入权限，请联系管理员添加`,
            });
        } else if (listType.value === 0) {
            let msg = "";
            if (erpPositiveCount.value) {
                msg = invoiceCategory.value === "10070" ? "销售出库单" : "采购入库单"
            } else {
                msg = invoiceCategory.value === "10070" ? "销售退货单" : "采购退货单"
            }
            ElNotify({
                type: "warning",
                message: `您无${msg}的导入权限，请联系管理员添加`,
            });
        } else {
            let url = `${urlRoute[listType.value]}?asid=${scmAsid.value}&fromInvoice=true&id=${idFromScm.value}&NotParticipatingDeductions=${NotParticipatingDeductionsErp.value}&genarateType=${genarateType.value}&userSn=0`;
            erpCreateTab(url, urlRouteName[listType.value]);
        }
    } else {
        generateBillShowErp.value = false;
        ElNotify({
            type: "warning",
            message: `暂无数据可生成单据`,
        });
    }
};

const associateVoucher = (type: number) => {
    emit("associateVoucher", type);
}

const showBusinessTypeOptions = ref<Array<any>>([]);
watchEffect(() => {
    showBusinessTypeOptions.value = JSON.parse(JSON.stringify(businessTypeOptions.value));
});
function businessTypeFilterMethod(value: string) {
    showBusinessTypeOptions.value = commonFilterMethod(value, businessTypeOptions.value, 'label');
}
onMounted(()=>{
    if(showToEisBtn.value){
        useEisInfoStoreHook().getRelationInfo();
    }
})
</script>

<style lang="less" scoped>
@media screen and (max-width: 1240px) {
    .ml-10 {
        margin-left: 1px;
    }

    .mr-10 {
        margin-right: 1px;
    }
}
.quickInvoice-dialog {
    form {
        display: flex;
        flex-direction: column;
    }
    .quickInvoice-dialog-form {
        padding: 20px 20px 0;
    } 
    .line-item {
        margin: 0 auto 18px;
        &.required {
            :deep(.el-form-item__label::before) {
                content: "*";
                color: var(--el-color-danger);
                margin-right: 4px;
            }
        }
        :deep(.el-form-item__label) {
            min-width: 88px;
        }
        &.accountSet {
            :deep(.el-form-item__label) {
                min-width: 138px;
            }
        }
    }
    .help-icon {
        margin-left: 5px;
        display: inline-block;
        width: 14px;
        height: 14px;
        background: url("@/assets/Voucher/help.png") no-repeat 0 0;
        background-size: contain;
        cursor: pointer;
    }
}
.info-tip {
    width: 330px;
    margin-left: 65px;
    display: flex;
    .tip-icon {
        margin-right: 5px;
        color: var(--yellow);
        font-size: 16px;
        position: relative;
        top: 1px;
    }
    .tip-txt {
        flex: 1;
        min-width: 0;
        color: #999;
    }
}
.jump-tip {
    text-align: center; 
    display: block; 
    padding: 40px 70px;
    &.tip-erp {
        text-align: left; 
    }
}
ul,
li {
    list-style: none;
    padding: 0;
    margin: 0;
}

.buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    box-sizing: border-box;
    border-top: 1px solid var(--border-color);
    margin-top: 22px;
}

.question-icon {
    background-image: url("@/assets/Settings/question.png");
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 16px 16px;
    font-size: 16px;
    vertical-align: middle;
    margin-left: 3px;
    display: inline-block;
    height: 16px;
    width: 16px;
    cursor: pointer;
    position: relative;
    top: -3px;
}

.button-content {
    display: flex;
    align-items: center;

    .button.solid-button.hot-btn {
        background-image: url("@/assets/Invoice/hot.png");
        background-repeat: no-repeat;
        background-position: 84px 0px;
        background-size: 26px 13px;
        width: 110px;
    }

    .mark-loading {
        margin-right: 5px;
        vertical-align: middle;
    }
    .mark-question-icon {
        background-image: url("@/assets/Settings/question.png");
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 16px 16px;
        font-size: 16px;
        vertical-align: middle;
        margin-left: 3px;
        margin-top: -15px;
        height: 16px;
        width: 16px;
        cursor: pointer;
    }

    .item {
        position: relative;

        &:hover {
            ul {
                display: block;
                z-index: 9999;
            }
        }

        .voucher-button {
            &:hover {
                .down-click ul {
                    display: none;
                }
            }
        }
    }


    .downlist {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        left: 1px;

        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            box-shadow: none;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;

            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }

    ul {
        z-index: 10;
        color: var(--font-color);
        background-color: var(--white);
        box-shadow: 0 0 4px var(--button-border-color);
        border-radius: 2px;
        position: absolute;
        left: -1px;
        display: none;

        li {
            height: 27px;
            line-height: 27px;
            cursor: pointer;
            font-size: 13px;
            box-shadow: none;
            text-align: left;
            padding: 0 12px;
            white-space: nowrap;
            &:hover {
                background-color: var(--main-color);
                color: var(--white);
            }
        }
    }

    .down-click {
        width: 19px;
        margin-left: 1px;
        background: url("@/assets/Icons/down-white.png") no-repeat center;
        background-color: var(--main-color);

        &:hover {
            border-color: var(--light-main-color);
            background-color: var(--light-main-color);

            // & + ul {
            //     display: block;
            // }
        }
    }
}
.invoice-task-tip {
    font-size: var(--font-size);
    line-height: var(--line-height);
}
.printshow-content {
    .printshow-main {
        box-sizing: border-box;
        padding: 0 32px;

        .top {
            width: 76px;
            height: 28px;
            background-color: var(--main-color);
            color: var(--white);
            text-align: center;
            font-size: 13px;
            line-height: 26px;
            cursor: pointer;
            display: inline-block;
            border: 1px solid var(--main-color);
            margin-top: 20px;
        }
    }

    .print-item {
        position: relative;
        height: 18px;
        margin-top: 16px;
        display: flex;
        align-items: center;

        &.first {
            margin-top: 24px;
        }

        :deep(.el-input) {
            height: 28px;
            width: 152px;
            position: absolute;
            right: 80px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
.invoice-import-help {
    text-align: right;
    padding-right: 20px;
    margin-top: 10px;
    display: block;
}
.invoice-import-title {
    display: block;
    margin: 10px 0 0;
    line-height: 20px;
    &.child {
        margin-left: 10px;
    }
}

.certification-show-content {
    .certification-form {
        padding: 45px 40px 20px;

        :deep(.el-date-editor.el-input) {
            width: 160px;
        }
    }

    .delete-tip {
        padding: 40px 70px;
        display: block;
        text-align: center;
    }

    .certification-res-tip {
        padding: 40px 70px 20px;
    }
}

.importBankShow-content {
    .importBankShow-main {
        padding-right: 40px;
        box-sizing: border-box;

        div {
            font-size: var(--font-size);
            color: #404040;
            margin-left: 40px;
        }
    }
}

.importAlipayShow-content {
    ul.importAlipayShow-top {
        height: 30px;
        margin-top: 20px;
        display: flex;

        li {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            position: relative;

            span {
                color: #999999;
                font-size: 14px;
                line-height: 22px;
            }

            span.active {
                color: #147bff;
                font-size: 16px;
                font-weight: 600;
            }

            span.line {
                width: 67px;
                height: 3px;
                position: absolute;
                bottom: 0;
                left: 50%;
                background-color: transparent;
                transform: scaleY(0.5) translateX(-50%);

                &.active {
                    background-color: #1677fe;
                }
            }
        }
    }

    .importAlipayShow-main {
        display: flex;
        align-items: center;
        margin-top: 20px;

        > div {
            display: inline-block;

            &.left {
                margin-left: 33px;
            }

            &.right {
                margin-left: 22px;
                width: 233px;
                line-height: 25px;
                font-size: 14px;
                vertical-align: middle;

                .line {
                    line-height: 20px;
                    margin-top: 10px;

                    > a {
                        color: var(--link-color);
                    }
                }
            }
        }
    }

    .buttons {
        border-top: none;
    }
}

.importWechatShow-content {
    .importWechatShow-main {
        display: flex;
        align-items: center;
        margin-top: 20px;

        > div {
            display: inline-block;

            &.left {
                margin-left: 33px;
            }

            &.right {
                margin-left: 22px;
                width: 233px;
                font-size: 14px;
                vertical-align: middle;

                .line {
                    line-height: 20px;
                    height: 20px;
                    margin-top: 15px;
                    background-color: transparent;

                    > a {
                        color: #03ce81;
                    }
                }
            }
        }
    }

    .buttons {
        border-top: none;

        a {
            background-color: #03ce81;
        }
    }
}

.generate-bill-content {
    display: flex;
    flex-direction: column;
    .generate-result {
        font-size: 12px;
        color: var(--weaker-font-color);
        background-color: var(--background-color);
        margin: 20px 40px 10px 40px;
        padding: 10px 20px;

        .result {
            display: flex;
            flex-direction: row;
            font-size: 14px;
            color: var(--font-color);
            width: 80%;
            justify-content: space-between;
        }
    }
    .generate-notice {
        font-size: 12px;
        margin-left: 40px;
        color: var(--weaker-font-color);
    }
    .generate-container {
        display: flex;
        img {
            margin: 13px 2px 13px 0;
            width: 14px;
            height: 14px;
        }
    }
    .generate-container-line2 {
        margin-top: -15px;
        margin-left: 16px;
    }
    .generate-container-radio {
        margin: 20px 0 0 40px;
        font-size: 12px;
        display: flex;
        .radio-lt {
            display: flex;
            align-items: center;
            margin-right: 40px;
        }
        :deep(.el-radio) {
            margin-right: 20px;
        }
    }
    .buttons {
        margin-top: 12px;
    }
    .generate-txt {
        flex: 1;
        min-width: 0;
        color: #666;
    }
    .generate-bot {
        margin: 12px;
        padding-left: 32px;
        display: flex;
        line-height: 32px;
        .generate-bot-rt {
            margin-left: 5px;
            flex: 1;
            min-width: 0;
        }
    }
    &.erpBill {
        .generate-result {
            margin: 12px;
            padding: 8px 20px 12px;
            font-size: 14px;
            line-height: 24px;
            color: var(--font-color);
            background: #fbfbfb;
            .result {
                width: 100%;
            }
        }
        .orange {
            color: #fa6400;
        }
        .generate-notice {
            margin: 12px 12px 0 34px;
            font-size: 12px;
            line-height: 18px;
        }
        .generate-container img {
            margin: 0;
            position: relative;
            top: 2px;
            left: -6px;
        }
    }
}
.recognize-drag {
    margin: 20px;
    .el-upload__tip {
        font-weight: bold;
        font-size: var(--h4);
        color: var(--font-color);
    }
}
.importResultDialog {
    margin: 20px;
}
.importResultDialog-disabled {
    opacity: 0.4;
    cursor: auto;
}
.file-list-wrap {
    margin-top: 20px;
}
.file-list {
    .file-list-item {
        display: flex;
        justify-content: space-between;
        line-height: 30px;
        font-size: 14px;
        &:hover {
            background-color: var(--el-fill-color-light);
        }
        > span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    .file-list-oprate {
        margin-left: 10px;
        display: flex;
        > span {
            margin-right: 15px;
            cursor: pointer;
            color: var(--main-color);
        }
    }
}
:deep(.el-upload-dragger) {
    padding: 10px;
}
:deep(.el-select) {
    &.businessTypeRef {
        .el-select-dropdown {
            max-height: 200px;
            overflow: hidden;
        }
    }
}
:deep(.el-autocomplete-suggestion li){
    margin:12px 0;
}
body[erp] {
    .button.large-2.dropdown {
        > ul {
            top: 27px;
        }
    }
}
.el-icon-question {
    margin-top: -36px;
}
.eis-relation-dialog {
    .eis-tip{
        padding: 30px 50px 20px;
        display: flex;
        flex-direction: column;
    }
}
</style>
