import { useCorsagentIframeStoreHook } from "@/store/modules/corsagent";
//后续通信
class ErpType {
    constructor(typeName: string) {
        this.typeName = typeName;
    }
    typeName: string;
    toString() {
        return this.typeName;
    }
}

export const erpNotifyTypeEnum = {
    openTab: new ErpType("openTab"),
    erpLoading: new ErpType("erpLoading"),
    erpBack: new ErpType("erpBack"),
};

export const erpNotify = function (erpNotifyType: ErpType, data?: any) {
    return new Promise<void>((resolve, reject) => {
        parent.postMessage({ type: erpNotifyType.toString(), data: data || {} }, "*");
    });
}

const corsagentIframeStore = useCorsagentIframeStoreHook();
export const erpCloseTab = (href: string, title: string) => {
    corsagentIframeStore.pushNoticeUrl("fn=closeTab&href=" + encodeURIComponent(href) + "&title=" + title);
};

export const erpCreateTab = (href: string, title: string) => {
    if (href.toLocaleLowerCase() == "/default/default") {
        href = "/";
    }
    corsagentIframeStore.pushNoticeUrl("fn=openTab&href=" + encodeURIComponent(href) + "&title=" + title);
};

export const processDialog = (title: string, num: number) => {
    corsagentIframeStore.pushNoticeUrl("fn=RobotProcess&title=" + encodeURIComponent(encodeURIComponent(title)) + "&num=" + num);
};

export const toLogout = (reson: string) => {
    corsagentIframeStore.pushNoticeUrl("fn=logout&reson=" + encodeURIComponent(encodeURIComponent(reson)));
};
export const erpChangeFullScreen = (state: 'cover'|'uncover') => {
    corsagentIframeStore.pushNoticeUrl(`fn=${state}`);
};
export const erpOpenWindow = (href: string) => {
    const obj = document.createElement("a");
    obj.target = "_blank";
    obj.href = href;
    if (document.body.firstChild) {
        document.body.insertBefore(obj, document.body.firstChild);
    } else {
        document.body.appendChild(obj);
    }
    obj.focus();
    obj.click();
    document.body.removeChild(obj);
};
export const erpSetTopLocationhref = (href: string) => {
    corsagentIframeStore.pushNoticeUrl("fn=setTopLocationhref&href=" + encodeURIComponent(href));
};
