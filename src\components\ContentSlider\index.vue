<script lang="ts" setup>
import { ref, watch, nextTick } from "vue";

const props = defineProps({
    slots: { type: Array<string>, required: true },
    currentSlot: { type: String, required: true },
});

const slideDirection = ref<"slide-left" | "slide-right">("slide-right");

watch(
    () => props.currentSlot,
    (newValue, oldValue) => {
        // 先遍历到新值，则是向左滑
        // 先遍历到旧值，则是向右滑
        for (let index = 0; index < props.slots.length; index++) {
            const element = props.slots[index];
            if (element === newValue) {
                slideDirection.value = "slide-left";
                break;
            } else if (element === oldValue) {
                slideDirection.value = "slide-right";
                break;
            }
        }
        nextTick(() => {
            const el = document.getElementById(newValue + "ContentSlider");
            // tabs 组件特殊处理
            // 如果是在tabs组件内部使用，那么ContentSlider组件的父元素必须是el-tab-pane才生效
            if (el && el.parentElement && el.parentElement.classList.contains("el-tab-pane")) {
                el.parentElement.style.minHeight = el.offsetHeight + "px";
                el.parentElement.style.transition = "min-height 1s";
            }
            document.body.scrollTop = 0;
        });
    }
);
</script>

<template>
    <template v-for="item in slots" :key="item">
        <Transition :name="slideDirection" :id="item + 'ContentSlider'" v-show="currentSlot === item">
            <slot :name="item"></slot>
        </Transition>
    </template>
</template>

<style lang="less" scoped>
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
    will-change: transform;
    transition: transform 1s;
    width: calc(100vw - var(--menu-container-width) - var(--router-container-margin) * 2) !important;
    position: absolute !important;
    backface-visibility: hidden;

    &.edit-content {
        width: var(--edit-content-width) !important;
        left: calc((100vw - var(--edit-content-width)) / 2);
    }
}

.slide-right-enter-from,
.slide-left-leave-to {
    transform: translateX(calc(100vw + var(--menu-container-width) + var(--router-container-margin)));
}

.slide-right-leave-to,
.slide-left-enter-from {
    transform: translateX(calc(-100vw - var(--menu-container-width) - var(--router-container-margin)));
}

body[erp] {
    .slide-left-enter-active,
    .slide-left-leave-active,
    .slide-right-enter-active,
    .slide-right-leave-active {
        width: calc(100vw - var(--self-adaption-content-padding) * 2) !important;
    }

    .slide-right-enter-from,
    .slide-left-leave-to {
        transform: translateX(calc(100vw + var(--self-adaption-content-padding)));
    }

    .slide-right-leave-to,
    .slide-left-enter-from {
        transform: translateX(calc(-100vw - var(--self-adaption-content-padding)));
    }
}
</style>
