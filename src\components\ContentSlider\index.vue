<script lang="ts" setup>
  const props = withDefaults(
    defineProps<{
      slots: string[]
      currentSlot: string
    }>(),
    { slots: () => [], currentSlot: "" },
  )

  const slideDirection = ref<"slide-left" | "slide-right">("slide-right")

  watch(
    () => props.currentSlot,
    (newValue, oldValue) => {
      const newIndex = props.slots.findIndex((item) => item === newValue)
      const oldIndex = props.slots.findIndex((item) => item === oldValue)

      if (newIndex > oldIndex) {
        slideDirection.value = "slide-right"
      } else {
        slideDirection.value = "slide-left"
      }

      nextTick(() => {
        const el = document.getElementById(newValue + "ContentSlider")
        // tabs 组件特殊处理
        // 如果是在tabs组件内部使用，那么ContentSlider组件的父元素必须是el-tab-pane才生效
        if (el && el.parentElement && el.parentElement.classList.contains("el-tab-pane")) {
          el.parentElement.style.minHeight = el.offsetHeight + "px"
          el.parentElement.style.transition = "min-height 1s"
        }
        document.body.scrollTop = 0
      })
    },
  )
</script>

<template>
  <Transition
    :name="slideDirection"
    :id="currentSlot + 'ContentSlider'">
    <slot :name="currentSlot"></slot>
  </Transition>
</template>

<style lang="scss" scoped>
  .slide-left-enter-active,
  .slide-left-leave-active,
  .slide-right-enter-active,
  .slide-right-leave-active {
    will-change: transform;
    transition: transform 1s;
    width: calc(100vw - var(--menu-container-width) - var(--router-container-margin) * 2) !important;
    position: absolute !important;
    backface-visibility: hidden;

    &.edit-content {
      width: var(--edit-content-width) !important;
      left: calc((100vw - var(--edit-content-width)) / 2);
    }
  }

  .slide-right-enter-from,
  .slide-left-leave-to {
    transform: translateX(calc(100vw + var(--menu-container-width) + var(--router-container-margin)));
  }

  .slide-right-leave-to,
  .slide-left-enter-from {
    transform: translateX(calc(-100vw - var(--menu-container-width) - var(--router-container-margin)));
  }
</style>
