import { defineStore } from "pinia";
import { ref, computed } from "vue";
import store from "@/store";
import { getEisRelationInfoApi } from "@/api/eis";
import type { IResponseModel } from "@/util/service";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";

enum AccProductType {
  ACC = 1,
  DZ = 2,
  PRO = 5,
  DZPRO = 6,
}
export enum SystemEnum {
  Acc = 1,
  Scm = 2,
  AccAndScm = 3, // [Description("同时关联云财务和云进销存")]
  Erp = 4,
}

export interface IRelationData {
  companyName: string;
  companyId: string;
  systemType: SystemEnum | null;
  accProductType: AccProductType | null;
  accAsId: string | null;
  accAsName: string;
  scmProductType?: number | null;
  scmAsId?: string | null;
}

export const useEisInfoStore = defineStore("eis", () => {
  const isRelation = ref(false);
  const eisRelationData = ref<IRelationData>();
  const initRelationData: IRelationData = {
    companyName: "",
    companyId: "",
    systemType: null,
    accProductType: null,
    accAsId: "",
    accAsName: useAccountSetStoreHook().accountSet?.asName as string,
  };
  const hasGetInfo = ref(false);
  const currentProductType = computed(() => {
    if (window.isProSystem) {
      return window.productType === "acc" ? AccProductType.PRO : AccProductType.DZPRO;
    } else {
      return window.productType === "acc" ? AccProductType.ACC : AccProductType.DZ;
    }
  });

  const getRelationInfo = () => {
    return new Promise<IRelationData | null>((resolve) => {
      const asId = useAccountSetStoreHook().accountSet!.asId;
      getEisRelationInfoApi(asId).then((res: IResponseModel<IRelationData>) => {
        if (res.state === 1000) {
          isRelation.value = res.data !== null;
          eisRelationData.value = res.data ?? initRelationData;
          resolve(res.data);
        }
      });
    });
  };

  return { isRelation, eisRelationData, hasGetInfo, getRelationInfo, currentProductType };
});

/** 在setup外使用 */
export function useEisInfoStoreHook() {
  return useEisInfoStore(store);
}
