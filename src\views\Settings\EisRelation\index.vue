<template>
  <div class="content">
    <ContentSlider
      :slots="slots"
      :currentSlot="currentSlot">
      <template #main>
        <div class="main-content align-center">
          <div class="slot-mini-content">
            <div class="main-top space-between">
              <div class="main-tool-left"></div>
              <div class="main-tool-right"><RefreshButton></RefreshButton></div>
            </div>
            <div class="main-table">
              <Table
                :data="tableData"
                :columns="columns"
                :tableName="setModule">
                <template #operator>
                  <el-table-column
                    label="操作"
                    :min-width="228"
                    :resizable="false">
                    <template #default="scope">
                      <a
                        class="link"
                        @click="editfp(scope.row)">
                        编辑
                      </a>
                      <a
                        class="link ml-10"
                        v-if="scope.row.companyId"
                        @click="enterfpUrl(scope.row)">
                        进入云发票企业
                      </a>
                    </template>
                  </el-table-column>
                </template>
              </Table>
            </div>
          </div>
        </div>
      </template>
      <template #edit>
        <div class="slot-content align-center">
          <div class="slot-mini-content">
            <div class="item-line">
              <div class="item-title">是否关联云发票：</div>
              <div class="item-input">
                <el-radio-group
                  v-model="relationForm.isRelation"
                  @change="relationRadioChange">
                  <el-radio :label="false">不关联</el-radio>
                  <el-radio :label="true">关联</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div class="item-line">
              <div class="item-title">财务软件账套：</div>
              <div class="item-input">
                <span>{{ relationForm.asName }}</span>
              </div>
            </div>
            <div
              class="item-line item-fp"
              :style="{ visibility: relationForm.isRelation ? 'visible' : 'hidden' }">
              <div
                class="item-title"
                :style="{ lineHeight: '30px' }">
                关联云发票企业：
              </div>
              <div class="item-input edit-fp-relation">
                <Select
                  v-model="relationForm.companyId"
                  :disabled="fpAccountBool"
                  :fit-input-width="true"
                  no-data-text="请到云发票里创建企业"
                  :teleported="false"
                  style="width: 230px">
                  <Option
                    v-for="item in eisCompanyList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name" />
                </Select>
                <a
                  class="link"
                  v-show="fpAccountBool"
                  style="margin-left: 2px"
                  @click="fpAccountBool = false"
                  :style="{ lineHeight: '30px' }">
                  修改
                </a>
              </div>
            </div>
            <div
              class="item-line item-fp item-center"
              :style="{ visibility: relationForm.isRelation ? 'visible' : 'hidden' }">
              <a
                class="link"
                @click="newfpAccountSet"
                style="text-align: center">
                没有企业？请点击前往云发票创建新企业>>
              </a>
            </div>
            <div class="item-line item-center">
              <a
                class="button solid-button"
                @click="submit">
                保存
              </a>
              <a
                class="button ml-10"
                @click="currentSlot = 'main'">
                取消
              </a>
            </div>
          </div>
        </div>
      </template>
    </ContentSlider>
  </div>
  <el-dialog
    title="提示"
    v-model="taxInfoDialog"
    width="460"
    class="dialogDrag">
    <div
      class="taxInfo-container"
      v-dialogDrag>
      <div class="taxInfo-box">
        <div class="taxInfo-line">
          <div class="taxInfo-title">企业名称：</div>
          <div class="taxInfo-input">
            <el-input
              v-model="taxInfoForm.name"
              disabled />
          </div>
        </div>
        <div class="taxInfo-line">
          <div class="taxInfo-title">统一社会信用代码：</div>
          <div class="taxInfo-input">
            <el-input
              v-model="taxInfoForm.taxCode"
              disabled />
          </div>
        </div>
        <div class="taxInfo-line">
          <div class="taxInfo-title">报税地区：</div>
          <div class="taxInfo-input">
            <el-input
              v-model="taxInfoForm.taxArea"
              disabled />
          </div>
        </div>
        <div class="taxInfo-tip">
          <img
            src="@/assets/Icons/warn.png"
            class="tip-icon" />
          <div class="tip-content">
            当前账套税务信息和云发票企业信息不符，已自动匹配填充了云发票企业相对应的企业名称、税号和报税地区，点击确定，将更新当前账套信息和云发票保持一致，并关联成功哦~
          </div>
        </div>
      </div>
      <div class="buttons borderTop">
        <a
          class="button solid-button"
          @click="saveRelationData">
          确定
        </a>
        <a
          class="button ml-10"
          @click="taxInfoDialog = false">
          取消
        </a>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts">
  export default {
    name: "EisRelation",
  };
</script>
<script setup lang="ts">
  import ContentSlider from "@/components/ContentSlider/index.vue";
  import Table from "@/components/Table/index.vue";
  import Select from "@/components/Select/index.vue";
  import Option from "@/components/Option/index.vue";
  import { type IResponseModel } from "@/util/service";
  import { globalWindowOpen } from "@/util/url";
  import { ElNotify } from "@/util/notify";
  import type { IEisCompanyListItem, IRelationOperateRes } from "./types";
  import { ref, onMounted, computed } from "vue";
  import RefreshButton from "@/components/RefreshButton/index.vue";
  import { ElAlert } from "@/util/confirm";
  import { useEisInfoStoreHook, type IRelationData, SystemEnum } from "@/store/modules/eis";
  import { useAccountSetStoreHook } from "@/store/modules/accountSet";
  import { getEisCompanyApi, saveEisRelationApi, cancelEisRelationApi } from "@/api/eis";
  import { getColumnWidth } from "@/components/ColumnSet/utils";
  import { checkInvoiceIsAdminApi } from "@/api/eis";

  const setModule = "eisRelation";
  const slots = ["main", "edit"];
  const currentSlot = ref("main");
  const { getRelationInfo, currentProductType } = useEisInfoStoreHook();
  const { getAccountSetInfo } = useAccountSetStoreHook();
  const currentAccountSet = computed(() => useAccountSetStoreHook().accountSetInfo);
  const tableData = computed(() => [useEisInfoStoreHook().eisRelationData]);
  const relationForm = ref({
    isRelation: false,
    asName: "",
    companyId: "",
  });

  const columns = [
    {
      prop: "companyId",
      label: "关联状态",
      minWidth: 150,
      align: "center",
      headerAlign: "center",
      useHtml: true,
      formatter: (row: IRelationData) => {
        if (row?.companyId === "") {
          return "<div class='state-block un-relation'>未关联</div>";
        } else {
          return "<div class='state-block relation'><i></i>已关联</div>";
        }
      },
      width: getColumnWidth(setModule, "companyId"),
    },
    {
      prop: "accAsName",
      label: "财务软件账套",
      minWidth: 300,
      align: "left",
      headerAlign: "left",
      width: getColumnWidth(setModule, "accAsName"),
    },
    {
      prop: "companyName",
      label: "云发票企业",
      minWidth: 300,
      align: "left",
      headerAlign: "left",
      width: getColumnWidth(setModule, "companyName"),
    },
    { slot: "operator" },
  ];

  // 进入云发票企业
  const enterfpUrl = (row: IRelationData) => {
    const { companyId } = row;
    let url = window.fpHost + "/MainPage?eiscid=" + companyId;
    globalWindowOpen(url);
  };

  const eisCompanyList = ref<Array<IEisCompanyListItem>>([]);
  const fpAccountBool = ref(false);

  const submitFlag = ref<boolean>(true);

  const editfp = (row: IRelationData) => {
    const { companyId, accAsName } = row;
    const toEditSlot = () => {
      relationForm.value.isRelation = !!companyId;
      relationForm.value.companyId = companyId;
      relationForm.value.asName = accAsName;
      currentSlot.value = "edit";
      fpAccountBool.value = companyId !== "";
    };
    if (companyId) {
      checkInvoiceIsAdminApi().then((res: any) => {
        if (res.state === 1000) {
          const { isAdministrator, adminPhoneNumber } = res.data;
          if (isAdministrator) {
            toEditSlot();
          } else {
            ElNotify({
              type: "warning",
              message: `暂无权限！请联系云发票企业管理员：${adminPhoneNumber}操作！`,
            });
          }
        }
      });
    } else {
      toEditSlot();
    }
  };

  const newfpAccountSet = () => {
    globalWindowOpen(window.fpHost);
  };
  const relationRadioChange = () => {
    if (relationForm.value.isRelation && relationForm.value.companyId === "" && eisCompanyList.value.length > 0) {
      relationForm.value.companyId = eisCompanyList.value[0].id;
    }
  };
  let saveRelationData = () => {};
  let cancelRelationData = (successsCB?: Function) => {
    const cancelParams = {
      eiscid: tableData.value[0]!.companyId,
      companyId: tableData.value[0]!.companyId,
      systemType: SystemEnum.Acc,
      accProductType: currentProductType,
      accAsId: currentAccountSet.value!.asId,
      accAsName: currentAccountSet.value!.asName,
    };
    cancelEisRelationApi(cancelParams).then((res: any) => {
      if (res.state === 1000 && res.data.isSuccess) {
        if (successsCB) {
          successsCB();
        } else {
          ElNotify({
            type: "success",
            message: "保存成功",
          });
          getRelationInfo();
          getEisCompanyList();
          currentSlot.value = "main";
        }
      } else {
        ElNotify({
          type: "warning",
          message: res.data.errorMessage,
        });
      }
    });
  };
  const submit = () => {
    if (!submitFlag.value) return;
    submitFlag.value = false;
    setTimeout(() => {
      submitFlag.value = true;
    }, 3000);
    const { isRelation, companyId } = relationForm.value;
    if (isRelation) {
      if (!companyId) {
        ElNotify({
          type: "warning",
          message: "请选择云发票企业",
        });
        return;
      }
      const { isTaxBureauLogged, name, taxDistrict, uscic, taxDistrictName, systemRelationInfo } = eisCompanyList.value.find(
        (item: IEisCompanyListItem) => item.id === companyId
      )!;
      if (!isTaxBureauLogged) {
        ElAlert({
          message: "该云发票企业未经过税局登录验证，请先前往柠檬云发票登录税局！",
          options: {
            confirmButtonText: "前往登录",
            cancelButtonText: "取消",
          },
        }).then((r) => {
          if (r) {
            globalWindowOpen(window.fpHost + "/MainPage?loginTax=true&eiscid=" + companyId);
          }
        });
        return;
      }
      if (systemRelationInfo && systemRelationInfo.companyId !== useEisInfoStoreHook().eisRelationData?.companyId) {
        ElNotify({
          type: "warning",
          message: `该发票企业已关联${systemRelationInfo.systemType === SystemEnum.Erp ? "云业财" : "云财务"}账套：${
            systemRelationInfo.accAsName
          }，请重新选择！`,
        });
        return;
      }
      saveRelationData = () => {
        const saveData = {
          companyId: companyId,
          systemType: SystemEnum.Acc,
          accProductType: currentProductType,
          accAsId: currentAccountSet.value!.asId,
          accAsName: currentAccountSet.value!.asName,
        };
        const fn = () => {
          saveEisRelationApi(saveData, companyId).then((res: IResponseModel<IRelationOperateRes>) => {
            if (res.state === 1000 && res.data.isSuccess) {
              ElNotify({
                type: "success",
                message: "保存成功！云发票系统发票池完成数电平台数据同步，发票信息将自动同步至云财务发票模块",
              });
              getRelationInfo();
              getAccountSetInfo(true);
              getEisCompanyList();
              taxInfoDialog.value = false;
              currentSlot.value = "main";
            } else {
              ElNotify({
                type: "warning",
                message: res.data.errorMessage,
              });
            }
          });
        };
        if (!tableData.value[0]?.companyId) {
          fn();
          return;
        }
        companyId !== tableData.value[0]?.companyId ? cancelRelationData(fn) : fn();
      };

      const { accountSetInfo } = useAccountSetStoreHook();

      if (
        accountSetInfo!.taxNumberS !== uscic ||
        accountSetInfo!.taxpayerName !== name ||
        accountSetInfo!.taxadId!.toString() !== taxDistrict
      ) {
        taxInfoDialog.value = true;
        taxInfoForm.value = {
          name,
          taxCode: uscic,
          taxArea: taxDistrictName,
        };
      } else {
        saveRelationData();
      }
    } else {
      //选择未关联进行保存
      if (!tableData.value[0]?.companyId) {
        currentSlot.value = "main";
      } else {
        cancelRelationData();
      }
    }
  };
  //保存时税务信息和云发票的公司信息不一致
  const taxInfoDialog = ref(false);
  const taxInfoForm = ref({
    name: "",
    taxCode: "",
    taxArea: "",
  });

  const getEisCompanyList = () => {
    getEisCompanyApi().then((res: IResponseModel<IEisCompanyListItem[]>) => {
      if (res.state === 1000) {
        eisCompanyList.value = res.data;
      }
    });
  };

  onMounted(() => {
    getRelationInfo();
    getEisCompanyList();
    getAccountSetInfo();
  });
</script>

<style scoped lang="less">
  @import "@/style/SelfAdaption.less";
  @import "@/style/Scm/ScmRelation.less";
  .taxInfo-container {
    display: flex;
    flex-direction: column;
    .taxInfo-box {
      padding: 20px;
      .taxInfo-line {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .taxInfo-title {
          width: 160px;
          text-align: right;
          &::before {
            content: "*";
            color: red;
            margin-right: 3px;
          }
        }
      }
      .taxInfo-tip {
        display: flex;
        margin-top: 15px;
        .tip-icon {
          width: 15px;
          height: 15px;
          margin: 3px 5px 0 0;
        }
        .tip-content {
          text-align: left;
        }
      }
    }
  }
</style>
