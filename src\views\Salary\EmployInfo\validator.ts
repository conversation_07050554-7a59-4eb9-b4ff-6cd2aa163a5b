import { ElNotify } from "@/util/notify";
import { checkinvalidasubid } from "@/components/Picker/jtzjSubjectPicker/utils";

export const IsPhone = (str: string) => {
    if (str.length != 0) {
        const reg = /^1[3-9][0-9]\d{8}$/;
        const f = str.match(reg);
        if (f != null) {
            return true;
        } else {
            return false;
        }
    }
};

export const IsEmail = (str: string) => {
    if (str.length != 0) {
        const reg = /^[a-zA-Z0-9]+([._\\-]*[a-zA-Z0-9])*@([a-zA-Z0-9]+[-a-zA-Z0-9]*[a-zA-Z0-9]+\.){1,63}[a-zA-Z0-9]+$/;
        const f = str.match(reg);
        if (f != null) {
            return true;
        } else {
            ElNotify({
                type: "warning",
                message: "请输入正确的邮箱格式！",
            });
            return false;
        }
    }
};

export const IsID = (idType: string, idNum: string) => {
    switch (idType) {
        case "1":
            if (idNum.length != 18) {
                ElNotify({
                    type: "warning",
                    message: "居民身份证号码必须输入18位哦！",
                });
                return false;
            }
            break;
        case "2":
            if (idNum.length != 9) {
                ElNotify({
                    type: "warning",
                    message: "中国护照号码必须输入9位哦！",
                });
                return false;
            }
            break;
        case "3":
            if (idNum.length != 9) {
                ElNotify({
                    type: "warning",
                    message: "港澳居民来往内地通行证号码必须输入9位哦！",
                });
                return false;
            }
            break;
        case "4":
            if (idNum.length != 8) {
                ElNotify({
                    type: "warning",
                    message: "台湾居民来往内地通行证号码必须输入8位哦！",
                });
                return false;
            }
            break;
        case "5":
            if (idNum.length != 18) {
                ElNotify({
                    type: "warning",
                    message: "港澳居民居住证号码必须输入18位哦！",
                });
                return false;
            }
            break;
        case "6":
            if (idNum.length != 18) {
                ElNotify({
                    type: "warning",
                    message: "台湾居民居住证号码必须输入18位哦",
                });
                return false;
            }
            break;
        case "8":
            if (idNum.length != 15) {
                ElNotify({
                    type: "warning",
                    message: "外国人永久居留身份证号码必须输入15位哦！",
                });
                return false;
            }
            break;
        case "7":
        case "9":
        case "10":
        case "11":
            if (idNum.length > 30) {
                ElNotify({
                    type: "warning",
                    message: "请输入不超过30位的证件号码哦~",
                });
                return false;
            }
            break;
    }
};

function checkTime(start: string, end: string) {
    const s = new Date(start); //工资始发日期
    const e = new Date(end); //入职日期
    const startDay = s.getFullYear() + "-" + (s.getMonth() + 1) + "-" + s.getDate();
    const endDay = e.getFullYear() + "-" + (e.getMonth() + 1) + "-" + e.getDate();
    const pattern = /(\d{4})(\d{2})(\d{2})/;
    const beginTimeStr = startDay.replace(pattern, "$1-$2-$3");
    const endTimeStr = endDay.replace(pattern, "$1-$2-$3");
    const beginTime = new Date(beginTimeStr).getTime();
    const endTime = new Date(endTimeStr).getTime();
    return endTime > beginTime;
}
export const ValidateEmplotInfo = async(data: any) => {
    if (!data[1]) {
        ElNotify({
            type: "warning",
            message: "员工编号不能为空",
        });
        return false;
    }
    if (!data[2]) {
        ElNotify({
            type: "warning",
            message: "姓名不能为空",
        });
        return false;
    }
    if (!data[4]) {
        ElNotify({
            type: "warning",
            message: "证件号码不能为空",
        });
        return false;
    }
    switch (data[21]) {
        case "1":
            if (data[4].length != 18) {
                ElNotify({
                    type: "warning",
                    message: "居民身份证号码必须输入18位哦！",
                });
                return false;
            }
            break;
        case "2":
            if (data[4].length != 9) {
                ElNotify({
                    type: "warning",
                    message: "中国护照号码必须输入9位哦！",
                });
                return false;
            }
            break;
        case "3":
            if (data[4].length != 9) {
                ElNotify({
                    type: "warning",
                    message: "港澳居民来往内地通行证号码必须输入9位哦！",
                });
                return false;
            }
            break;
        case "4":
            if (data[4].length != 8) {
                ElNotify({
                    type: "warning",
                    message: "台湾居民来往内地通行证号码必须输入8位哦！",
                });
                return false;
            }
            break;
        case "5":
            if (data[4].length != 18) {
                ElNotify({
                    type: "warning",
                    message: "港澳居民居住证号码必须输入18位哦！",
                });
                return false;
            }
            break;
        case "6":
            if (data[4].length != 18) {
                ElNotify({
                    type: "warning",
                    message: "台湾居民居住证号码必须输入18位哦",
                });
                return false;
            }
            break;
        case "8":
            if (data[4].length != 15) {
                ElNotify({
                    type: "warning",
                    message: "外国人永久居留身份证号码必须输入15位哦！",
                });
                return false;
            }
            break;
        case "7":
        case "9":
        case "10":
        case "11":
            if (data[4].length > 30) {
                ElNotify({
                    type: "warning",
                    message: "请输入不超过30位的证件号码哦~",
                });
                return false;
            }
            break;
    }
    // if (!IsID(data[21], data[4])) {
    //     ElNotify({
    //         type: "error",
    //         message: "证件号格式有误！",
    //     });
    //     return false;
    // }
    if (!data[5]) {
        ElNotify({
            type: "warning",
            message: "手机号码不能为空",
        });
        return false;
    }
    if (!IsPhone(data[5])) {
        ElNotify({
            type: "warning",
            message: "手机号格式有误！",
        });
        return false;
    }
    if (data[6]&&!IsEmail(data[6])) {
        ElNotify({
            type: "warning",
            message: "邮箱格式有误！",
        });
        return false;
    }
    if (!data[7]) {
        ElNotify({
            type: "warning",
            message: "出生日期不能为空",
        });
        return false;
    }
    if (!data[9]) {
        ElNotify({
            type: "warning",
            message: "入职日期不能为空",
        });
        return false;
    }
    if (!data[12]) {
        ElNotify({
            type: "warning",
            message: "工资始发日期不能为空",
        });
        return false;
    }
    if (!data[22]) {
        ElNotify({
            type: "warning",
            message: "部门不能为空！",
        });
        return false;
    }
    if (!data[17]) {
        ElNotify({
            type: "warning",
            message: "计提工资科目不能为空",
        });
        return false;
    }
    if (data[18] == 1) {
        if (!data[19]) {
            ElNotify({
                type: "warning",
                message: "离职状态下离职日期不能为空！",
            });
            return false;
        }
        if (!data[20]) {
            ElNotify({
                type: "warning",
                message: "离职状态下工资停发年月不能为空！",
            });
            return false;
        }
        // if (checkTime(data[18],data[9])) {             
        //     ElNotify({
        //         type: "error",
        //         message: "离职日期不能早于入职日期",
        //     });
        //     return false;
        // }
    }

    //目前还没有校验格式
    // 手机号格式校验
    // IsPhone(data[5]);
    // 邮箱格式校验
    // IsEmail(data[6]);

    // const s = new Date(data[12]);//工资始发日期
    // const e = new Date(data[9]);//入职日期
    // const startDay = s.getFullYear() + "-" + (s.getMonth() + 1) + "-" + s.getDate();
    // const endDay = e.getFullYear() + "-" + (e.getMonth() + 1) + "-" + e.getDate();
    // const pattern = /(\d{4})(\d{2})(\d{2})/;
    // const beginTimeStr = startDay.replace(pattern, "$1-$2-$3");
    // const endTimeStr = endDay.replace(pattern, "$1-$2-$3");
    // const beginTime = new Date(beginTimeStr).getTime();
    // const endTime = new Date(endTimeStr).getTime();
    // endTime > beginTime
    if (checkTime(data[12], data[9])) {
        ElNotify({
            type: "warning",
            message: "工资始发日期不能早于入职日期",
        });
        return false;
    }

    if ((data[16] + "").length > 1024) {
        ElNotify({
            type: "warning",
            message: "备注不能超过1024个字符,请修改后重试哦",
        });
        return false;
    }
    return true;
};
