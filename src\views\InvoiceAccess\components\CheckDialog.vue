<script setup lang="ts">
  import { QuestionFilled } from "@element-plus/icons-vue"
  import { IInvoiceDataItem } from "../types"
  import TypeMapTable from "./TypeMapTable.vue"
  import { getInvoiceTableCheck } from "@/api/invoiceAccess"

  // 定义props和emits
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(["update:modelValue", "generateReport"])

  // 销项发票数据
  const salesInvoiceData = reactive<Array<IInvoiceDataItem>>([])

  // 进项发票数据
  const purchaseInvoiceData = reactive<Array<IInvoiceDataItem>>([])

  // 合并销项和进项发票数据，添加标题行和类型标识
  const tableData = computed(() => {
    const result: Array<any> = []
    // 添加销项发票数据
    salesInvoiceData.forEach((item) => {
      result.push({
        isHeader: false,
        type: "sales",
        ...item,
      })
    })
    // 添加进项发票数据
    purchaseInvoiceData.forEach((item) => {
      result.push({
        isHeader: false,
        type: "purchase",
        ...item,
      })
    })
    return result
  })

  // 销项发票的行数
  const salesRowSpan = computed(() => salesInvoiceData.length)

  // 进项发票的行数
  const purchaseRowSpan = computed(() => purchaseInvoiceData.length)

  // 获取行合并配置
  const getSpanMethod = ({ column, rowIndex }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    // 不再需要使用row参数

    // 第一列的处理
    if (column.label === "发票种类") {
      // 销项发票区域
      if (rowIndex === 0) {
        return { rowspan: salesRowSpan.value, colspan: 1 }
      } else if (rowIndex === salesRowSpan.value) {
        return { rowspan: purchaseRowSpan.value, colspan: 1 }
      }
      return { rowspan: 0, colspan: 0 }
    }
    return { rowspan: 1, colspan: 1 }
  }

  const headerCellStyle = ({ row, column, rowIndex, columnIndex }: any) => {
    if (columnIndex === 0 && rowIndex === 0) {
      column.colSpan = 2
    }
    if (columnIndex === 1 && rowIndex === 0) {
      return { display: "none" }
    }
  }

  // 对话框是否显示，与外部的modelValue同步
  const dialogVisible = ref(props.modelValue)

  // 监听props变化，更新本地状态
  watch(
    () => props.modelValue,
    (newVal) => {
      dialogVisible.value = newVal
    },
  )

  // 监听本地状态变化，通知父组件更新
  watch(dialogVisible, (newVal) => {
    emit("update:modelValue", newVal)
  })

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false
    emit("update:modelValue", false)
  }

  // 生成申报表
  const generateReport = () => {
    dialogVisible.value = false
    emit("update:modelValue", false)
    emit("generateReport")
    // 处理申报表生成逻辑
  }

  // 获取核对结果
  const getCheckResult = (row: any) => {
    switch (row.checkResult) {
      case EnumCheckResult.MATCH:
        return "√"
      case EnumCheckResult.UNMATCH:
        return "不相符,请核对数据"
      case EnumCheckResult.NOT_SUPPORT:
        return "暂不支持,请手动填写"
      default:
        return "手动核对"
    }
  }

  enum EnumCheckResult {
    MATCH = "1",
    UNMATCH = "0",
    NOT_SUPPORT = "-1",
  }

  // 获取核对结果的样式类
  const getCheckResultClass = (row: any) => {
    const result = row.checkResult
    if (result === EnumCheckResult.MATCH) {
      return "text-green-500"
    } else {
      return "text-red-500"
    }
  }

  const loading = ref(false)
  const loadData = () => {
    loading.value = true
    // 调用接口获取数据
    getInvoiceTableCheck()
      .then((res) => {
        if (res.state === 1000 && res.data) {
          // 清空现有数据
          salesInvoiceData.splice(0, salesInvoiceData.length)
          purchaseInvoiceData.splice(0, purchaseInvoiceData.length)

          // 处理返回的数据，根据categoryText区分销项和进项发票
          if (Array.isArray(res.data)) {
            res.data.forEach((item) => {
              // 根据categoryText判断发票类型
              if (item.categoryText === "进项发票") {
                purchaseInvoiceData.push(item)
              } else if (item.categoryText === "销项发票") {
                salesInvoiceData.push(item)
              }
            })
          }
        }
        loading.value = false
      })
      .catch(() => {
        loading.value = false
      })
  }

  // 处理模拟数据
  // const processMockData = () => {
  //   salesInvoiceData.splice(0, salesInvoiceData.length)
  //   purchaseInvoiceData.splice(0, purchaseInvoiceData.length)

  //   mockInvoiceData.forEach((item) => {
  //     if (item.categoryText === "进项发票") {
  //       purchaseInvoiceData.push(item)
  //     } else {
  //       salesInvoiceData.push(item)
  //     }
  //   })
  // }

  watch(dialogVisible, (newVal) => {
    if (newVal) {
      loadData()
    }
  })

  onMounted(() => {
    loadData()
  })

  const columns = [
    { slot: "invoiceType" },
    { prop: "invoiceTypeText", label: "", minWidth: 180 },
    {
      label: "填表数据",
      children: [
        { label: "份数", prop: "count_Left", align: "center" },
        {
          label: "金额",
          prop: "amount_Left",
          align: "center",
        },
        {
          label: "税额",
          prop: "tax_Left",
          align: "center",
        },
      ],
      align: "center",
    },
    {
      label: "企业开票数据/认证统计表",
      children: [
        { label: "份数", prop: "count_Right", align: "center" },
        {
          label: "金额",
          prop: "amount_Right",
          align: "center",
        },
        {
          label: "税额",
          prop: "tax_Right",
          align: "center",
        },
      ],
      align: "center",
    },
    { slot: "checkResult" },
  ]

  // 提示信息
  const salesInvoiceTooltip =
    "销项发票：提取【财务系统 - 销项发票模块】中开票日期属于所选税款所属期的发票数据 和 【税局开票数据】数据比对！"
  const purchaseInvoiceTooltip =
    "进项发票：提取【财务系统 - 进项发票模块】中认证税款所属期匹配的发票数据 和 当期【勾选认证-认证统计表】比对！"
</script>

<template>
  <!-- 内置的发票核对表格对话框 -->
  <Confirm
    v-model:visible="dialogVisible"
    title="票表核对"
    :onConfirm="generateReport"
    :onCancel="closeDialog"
    width="80%"
    :options="{
      showCancel: true,
      buttons: { confirm: '生成申报表', cancel: '返回' },
    }">
    <div>
      <ElAlert
        type="warning"
        show-icon
        :closable="false"
        class="mb-4">
        <div>
          <span class="font-bold">温馨提示：</span>
          由于数据同步机制存在延迟，可能数据无法及时获取到，请仔细核对您本月的企业开票发票/当期认证进项发票！若确认无误，可继续生成申报表。
        </div>
      </ElAlert>

      <div class="invoice-table-container">
        <!-- 销项和进项发票合并在一个表格中展示 -->
        <LMTable
          :data="tableData"
          border
          row-key="index"
          :columns="columns"
          v-loading="loading"
          :header-cell-style="headerCellStyle"
          style="width: 100%; margin-top: 16px"
          :span-method="getSpanMethod">
          <template #invoiceType>
            <ElTableColumn
              align="center"
              label="发票种类">
              <template #header>
                <div class="invoice-type-cell">
                  <div class="invoice-type-with-tooltip">
                    <span>发票种类</span>
                    <ElTooltip
                      class="tooltip-icon"
                      effect="light"
                      placement="right-end">
                      <el-icon><QuestionFilled /></el-icon>
                      <template #content>
                        <TypeMapTable></TypeMapTable>
                      </template>
                    </ElTooltip>
                  </div>
                </div>
              </template>

              <template #default="scope">
                <div class="invoice-type-cell">
                  <div
                    v-if="scope.row.categoryText === '销项发票' && scope.$index === 0"
                    class="invoice-type-with-tooltip">
                    <span>销项发票</span>
                    <ElTooltip
                      class="tooltip-icon"
                      effect="dark"
                      :content="salesInvoiceTooltip"
                      placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </ElTooltip>
                  </div>
                  <div
                    v-if="scope.row.categoryText === '进项发票' && scope.$index === salesRowSpan"
                    class="invoice-type-with-tooltip">
                    <span>进项发票</span>
                    <ElTooltip
                      class="tooltip-icon"
                      effect="dark"
                      :content="purchaseInvoiceTooltip"
                      placement="top">
                      <el-icon><QuestionFilled /></el-icon>
                    </ElTooltip>
                  </div>
                </div>
              </template>
            </ElTableColumn>
          </template>
          <template #checkResult>
            <ElTableColumn
              label="核对结果"
              width="180"
              align="center">
              <template #default="scope">
                <span :class="getCheckResultClass(scope.row)">
                  {{ getCheckResult(scope.row) }}
                </span>
              </template>
            </ElTableColumn>
          </template>
        </LMTable>
      </div>

      <div class="mt-4 text-sm text-gray-500">
        系统暂不支持农产品、海关缴款书、代扣代缴完税凭证等场景的智能取数填表，请
        <a
          href="javascript:void(0)"
          class="text-blue-500">
          联系客服记录您的需求
        </a>
      </div>
    </div>
  </Confirm>
</template>

<style lang="scss" scoped>
  .invoice-check-page {
    padding: 20px;

    .card-header {
      h2 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }
    }

    .card-content {
      padding: 10px;

      .description {
        margin-bottom: 24px;

        p {
          line-height: 1.6;
          color: #606266;
          margin-bottom: 12px;
        }
      }

      .instructions {
        margin-bottom: 32px;
        padding: 16px;
        background-color: #f5f7fa;
        border-radius: 4px;

        h3 {
          margin-top: 0;
          margin-bottom: 12px;
          font-size: 16px;
          color: #303133;
        }

        ol {
          padding-left: 20px;

          li {
            line-height: 1.8;
            color: #606266;
            margin-bottom: 8px;
          }
        }
      }

      .action-container {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-top: 32px;

        .el-button {
          padding: 12px 24px;
          font-size: 16px;
        }
      }
    }
  }

  // 表格相关样式
  .mb-4 {
    margin-bottom: 16px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .text-red-500 {
    color: #f56c6c;
    font-weight: bold;
  }

  .text-green-500 {
    color: #67c23a;
    font-weight: bold;
  }

  .text-gray-500 {
    color: #909399;
  }

  .text-blue-500 {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .text-sm {
    font-size: 14px;
  }

  .font-bold {
    font-weight: bold;
  }

  .invoice-type-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 8px 0;
  }

  .invoice-type-with-tooltip {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;

    .tooltip-icon {
      cursor: pointer;
      color: #909399;
      font-size: 16px;
      display: flex;
      align-items: center;

      &:hover {
        color: #409eff;
      }
    }
  }

  .invoice-type {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    min-width: 80px;
    height: 40px;
    border-radius: 4px;
    padding: 0 12px;
  }

  .sales-type {
    background-color: #409eff;
  }

  .purchase-type {
    background-color: #67c23a;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
</style>
