<template>
    <div class="icon-container" @mouseenter="changeContentsShow()" @mouseleave="hideContents()">
        <div class="icon-content">
            <slot name="content"></slot>
        </div>
        <div
            :class="[
                'tip-container',
                { 'tip-container-light': effect === 'light', 'show-contents': showContents, 'hide-contents': hidePopover },
            ]"
            :style="{ width: containerWidth + 'px', top: bubbleTop + 'px' }"
        >
            <div class="tip-content" :style="{ 'font-size': fontSize }">
                <slot name="tips"></slot>
                <span v-if="!$slots.tips">{{ content }}</span>
            </div>
        </div>
    </div>
</template>
<style lang="less" scoped>
@import "@/style/Functions.less";

.icon-container {
    position: relative;
    display: flex;

    .icon-content {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .tip-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%, 0);
        .set-transition;
        opacity: 0;
        visibility: hidden;
        padding-top: 6px;
        z-index: 99;

        &::before {
            content: " ";
            width: 0;
            height: 0;
            border-bottom: 7px solid rgba(0, 0, 0, 0.8);
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            align-self: center;
        }

        .tip-content {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 12px 17px;
            box-shadow: 0px 0px 20px 0px rgba(179, 179, 179, 0.3);

            :deep(div) {
                color: var(--white);
                line-height: 17px;
                text-align: left;
                word-break: break-all;
            }
        }
        &.tip-container-light {
            &::before {
                content: "";
                position: absolute;
                top: -10px;
                left: 50%;
                transform: translateX(-50%);
                border-width: 10px;
                border-style: solid;
                border-color: transparent transparent #fff transparent;
            }
            .tip-content {
                background-color: #fff;
                :deep(div) {
                    color: #333;
                }
            }
        }
    }

    .tip-container.show-contents {
        opacity: 1;
        visibility: visible;
    }
    .tip-container.hide-contents {
        opacity: 0;
        visibility: hidden;
    }
}
</style>
<script setup lang="ts">
import { watch, ref } from "vue";
const props = withDefaults(
    defineProps<{
        bubbleWidth: number;
        bubbleTop: number;
        content?: string;
        effect?: string;
        autoClose?: number;
        hidePopover?: boolean;
        fontSize?: string;
    }>(),
    {
        content: "",
        effect: "dark",
        autoClose: -1,
        hidePopover: false,
        fontSize: "12px",
    }
);
const emits = defineEmits(["hide"]);
const slots = defineSlots();
const getWidth = (content: string) => {
    // 创建一个新的 span 元素
    let clone = document.createElement("span");

    // 将原来的 span 元素的文本复制到新的 span 元素
    clone.textContent = content;

    // 设置新的 span 元素的样式
    clone.style.display = "inline";
    clone.style.width = "auto";
    clone.style.visibility = "hidden";
    clone.style.whiteSpace = "nowrap"; // 确保文本不会换行
    clone.style.fontSize = props.fontSize;

    // 将新的 span 元素添加到 DOM
    document.body.appendChild(clone);

    // 获取新的 span 元素的宽度
    let width = clone.getBoundingClientRect().width;

    // 从 DOM 中移除新的 span 元素
    document.body.removeChild(clone);
    return width + 34;
};

const containerWidth = ref(0);
if (props.content) {
    containerWidth.value = getWidth(props.content);
    if (containerWidth.value > props.bubbleWidth || slots.tips) {
        containerWidth.value = props.bubbleWidth;
    }
} else {
    containerWidth.value = props.bubbleWidth;
}
const showContents = ref(false);
function changeContentsShow() {
    showContents.value = true;
    if (props.autoClose >= 0) {
        let timer = setTimeout(() => {
            showContents.value = false;
            emits("hide");
            clearTimeout(timer);
        }, props.autoClose);
    }
}
function hideContents() {
    showContents.value = false;
    emits("hide");
}
// watch(
//     () => props.content,
//     (val) => {
//         if(val){
//             containerWidth.value = getWidth(val);
//         }
//     }
// )
</script>
