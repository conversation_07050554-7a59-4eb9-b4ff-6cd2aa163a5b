<template>
    <el-dialog
        v-model="display"
        :title="'新增' + inputTitle"
        center
        :width="width + 'px'"
        @close="close"
        @closed="resetDialogWidth"
        class="custom-confirm dialogDrag"
    >
        <div class="add-aae-dialog-container" v-dialogDrag>
            <div class="form-item">
                <div class="input-title"><span class="required">*</span> {{inputTitle}}编号:</div>
                <div class="input-field">
                    <el-input v-model="aaNum"></el-input>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title"><span class="required">*</span> {{inputTitle}}名称:</div>
                <div class="input-field">
                    <Tooltip :content="aaName" :isInput="true">
                        <el-input type="text" v-model="aaName" @input="limitInputLength(inputTitle+'名称', $event, 'aaName', 256)"></el-input>
                    </Tooltip>
                </div>
            </div>
            <div class="form-item">
                <div class="input-title">备注:</div>
                <div class="input-field">
                    <Tooltip :content="note" :isInput="true">
                        <el-input v-model="note" type="text" @input="limitInputLength('备注', $event, 'note', 1024)"></el-input>
                    </Tooltip>
                </div>
            </div>
            <div class="buttons" :class="isErp ? 'erp' : ''">
                <a class="button" @click="close">取消</a>
                <a class="button solid-button ml-10" @click="save()">确定</a>
            </div>
        </div>
    </el-dialog>
</template>
<style lang="less" scoped>
@import "@/style/Functions.less";

.add-aae-dialog-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-top: 19px;

    .form-item {
        align-self: center;
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .input-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            width: 100px;
            text-align: right;
        }

        .input-field {
            margin-left: 10px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .detail-input(256px, 30px);
            .detail-el-select(256px, 30px);
            display: flex;
            align-items: center;
            .asubName-textarea{
                width: 192px;
                height: 32px;
            }
            :deep(.el-input__inner) {
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    .required {
        color: var(--red);
    }

    .buttons {
        margin-top: 11px;
        padding: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

body[erp] .custom-confirm .el-dialog__body .buttons {
    &.erp {
        display: flex;
        justify-content: flex-end;
    }
}
</style>
<script lang="ts" setup>
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ref } from "vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import Tooltip from "@/components/Tooltip/index.vue";

const display = ref(false);
const width = ref(440);
const isErp = ref(window.isErp);

const props = defineProps({
    inputTitle:{
        type: String,
        default: "辅助核算项目",
    },
});

const emit = defineEmits<{
    (e: "save-success", data: any): void;
}>();

function resetDialogWidth() {
    width.value = 440;
}
function close() {
    display.value = false;
}
function showAADialog(aatype: number, autoAddName: string= "") {
    display.value = true;
    note.value = "";
    width.value = 440;
    aaName.value = autoAddName.slice(0, 256);
    limitInputLength(props.inputTitle +'名称', autoAddName, 'aaName', 256);
    init(aatype);
}

defineExpose({
    showAADialog,
});

const aatype = ref(0);
const aaNum = ref("");
const aaName = ref("");
const note = ref("");

function limitInputLength(label: string, value:any, attr: string, limitsize: number) {
    if (value.length > limitsize) {
        ElNotify({ type: "warning", message: `亲，${label}不能超过${limitsize}个字符!` });
        const newValue = value.slice(0, limitsize);
        switch (attr) {
            case 'aaName':
                aaName.value = newValue;
                break;
            case 'note':
                note.value = newValue;
                break;
        }
    }
}
function init(_aatype: number) {
    aatype.value = _aatype;
    aaNum.value = "";
    request({
        url: "/api/AssistingAccounting/GetNewAANum",
        method: "post",
        params: {
            aaType: aatype.value,
            categoryId: 0,
        },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            aaNum.value = res.data;
        }
    });
}

function saveVatiorCheck() {
    if (!aaNum.value) {
        ElNotify({
            message: `请输入${props.inputTitle}编码！`,
            type: "warning",
        });
        return false;
    }
    if (!/^[0-9a-zA-Z_]+$/.test(aaNum.value) || aaNum.value.length > 18) {
        ElNotify({
            message: `${props.inputTitle}编码为不超过18位的字符或数字组合！`,
            type: "warning",
        });
        return false;
    }
    if (!aaName.value) {
        ElNotify({
            message: `请输入${props.inputTitle}名称！`,
            type: "warning",
        });
        return false;
    }
    if (/[\\:'"\n]/.test(aaName.value)) {
        ElNotify({
            message: `亲,你输入的${props.inputTitle}名称！不能包含\\:等特殊字符串！`,
            type: "warning",
        });
        return false;
    }
    return true;
}
let timer:any;
function save() { 
    if (!saveVatiorCheck()) return;
    if (timer) {
        clearTimeout(timer);
        return;
    }
    saveSubmit();
    timer = setTimeout(() => {
        timer = 0;
    }, 500)
}

function saveSubmit() {
    new Promise<void>((resolve) => {
        request({
            url: "/api/AssistingAccounting/Check",
            method: "post",
            params: {
                aaType: aatype.value,
            },
            data: {
                aaeId: 0,
                aaNum: aaNum.value,
                aaName: aaName.value,
            },
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                if (res.data === "Y") {
                    ElNotify({
                        message: "编号重复，请重新编号！",
                        type: "warning",
                    });
                } else if (res.data === "Name") {
                    ElConfirm("亲，此名称已存在，是否继续保存？").then((r) => {
                        if (r) {
                            resolve();
                        } 
                    });
                } else {
                    resolve();
                }
            }
        });
    }).then(() => {
        SaveRequest();
    });
}

const entityParamsCustomerOrVendor = {
    type: "",
    adLevel0: "",
    adLevel1: "",
    adLevel2: "",
    adLevel3: "",
    address: "",
    contact: "",
    mobilePhone: "",
    taxNumber: "",
    note: "",
};
const entityParamsEmploy = {
    gender: "1",
    departmentId: "",
    departmentName: "",
    title: "",
    position: "",
    mobilePhone: "",
    birthday: "",
    startDate: "",
    endDate: "",
    note: "",
};
const entityParamsProject = {
    department: "",
    owner: "",
    mobilePhone: "",
    startDate: "",
    endDate: "",
    note: "",
};
const entityParamsStock = {
    stockModel: "",
    stockType: "",
    unit: "",
    startDate: "",
    endDate: "",
    note: "",
};
const entityParamsOther = {
    value01: "",
    value02: "",
    value03: "",
    value04: "",
    value05: "",
    value06: "",
    value07: "",
    value08: "",
    note: "",
};
const entityParamsDepart = {
    manager: "",
    mobilePhone: "",
    startDate: "",
    endDate: "",
    note: "",
};
const SaveRequest = () => {
    const params = {
        entity: {},
        aaNum: aaNum.value,
        aaName: aaName.value,
        uscc: "",
        status: 0,
        hasEntity: true,
        ifvoucher: true,
    };
    let urlPath = "";
    if (aatype.value === 10001) {
        urlPath = "Customer";
        entityParamsCustomerOrVendor.note = note.value;
        params.entity = entityParamsCustomerOrVendor;
    } else if (aatype.value === 10002) {
        urlPath = "Vendor";
        entityParamsCustomerOrVendor.note = note.value;
        params.entity = entityParamsCustomerOrVendor;
    } else if (aatype.value === 10003) {
        urlPath = "Employee";
        entityParamsEmploy.note = note.value;
        params.entity = entityParamsEmploy;
    } else if (aatype.value === 10004) {
        urlPath = "Department";
        entityParamsDepart.note = note.value;
        params.entity = entityParamsDepart;
    } else if (aatype.value === 10005) {
        urlPath = "Project";
        entityParamsProject.note = note.value;
        params.entity = entityParamsProject;
    } else if (aatype.value === 10006) {
        urlPath = "Stock";
        entityParamsStock.note = note.value;
        params.entity = entityParamsStock;
    } else if (aatype.value > 10007) {
        urlPath = "Custom?aaType=" + aatype.value;
        entityParamsOther.note = note.value;
        params.entity = entityParamsOther;
    }
    request({
        url: "/api/AssistingAccounting/" + urlPath,
        method: "post",
        headers: { "Content-Type": "application/json" },
        data: JSON.stringify(params),
    }).then((res: IResponseModel<string>) => {
        if (res.state !== 1000 || "Failed" === res.data) {
            ElNotify({ type: "warning", message: res.msg || "保存失败" });
            return;
        }
        ElNotify({ type: "success", message: "保存成功" });
        emit("save-success", { aaeId: Number(res.data), aaNum: aaNum.value, aaName: aaName.value });
        display.value = false;
        useAssistingAccountingStore().getAssistingAccounting(aatype.value);
    }) .catch(() => {
        ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
    });
};
</script>
