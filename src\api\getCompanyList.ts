import { request } from "@/util/service";
export const getCompanyListApi = (module:number,companyName:string) => {
    return request({
        url: "/api/Company/List",
        method: "get",
        params:{
            module,
            companyName
        }
    });
};
export const getCompanyDetailApi = (module:number,companyName:string) => {
    return request({
        url: "/api/Company",
        method: "get",
        params:{
            module,
            companyName
        }
    });
};

export interface ICompanyList {
    isFromDb: boolean;
    companyInfos: ICompanyInfo[];
}

export interface ICompanyInfo {
    name: string;
    creditCode:string;
}