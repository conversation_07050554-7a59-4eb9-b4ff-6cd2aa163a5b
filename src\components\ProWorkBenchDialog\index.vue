<template>
    <el-dialog center :draggable="false" v-model="proWorkBenchDialogShow" width="976px" class="pro-workBench-dialog">
        <div class="pro-workbench-container">
            <div class="workbench-toolbar">
                <div class="toolbar-left">
                    <div class="toolbar-title">企业成员</div>
                    <a v-show="wxworkSelectProSurperManager" class="button solid-button" @click="showSelectProSurperManagerDialog(true)">
                        授权管理员
                    </a>
                </div>
                <div class="toolbar-right">
                    <div class="company-name">{{ companyName }}</div>
                </div>
            </div>
            <div class="workbench-table-container" id="proWorkBenchDialog">
                <Table 
                    :data="tableData" 
                    :columns="columns" 
                    :border="false" 
                    :highlight-current-row="false"
                >
                    <template #nickname>
                        <el-table-column 
                            label="摘要" 
                            min-width="124" 
                            align="center" 
                            header-align="center" 
                            prop="description"
                            :width="getColumnWidth(column, 'description')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.openid">
                                    <ww-open-data type="userName" :openid="scope.row.openid"></ww-open-data>
                                </template>
                                <template v-else>
                                    <span>{{ scope.row.mobile.indexOf("108") === 0 ? "" : scope.row.nickname }}</span>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </el-dialog>
    <el-dialog
        center
        :draggable="false"
        v-model="wxworkSelectProSurperManagerDialogShow"
        width="450px"
        class="wxwork-select-pro-surpermanager-dialog"
        title="授权系统管理员"
        :show-close="customCanClose"
    >
        <div class="wxwork-select-surpermanager-container">
            <div class="manager-selector-container">
                选择管理员：
                <div id="superManagerContainer" class="manager-selector">
                    <div class="placeholder" v-show="placeholderShow">请选择</div>
                    <ww-open-data type="userName" :openid="openid"></ww-open-data>
                    <div class="more-btn" @click="selectProSurperManager"></div>
                </div>
            </div>
            <div class="tips">
                <div>授权说明：</div>
                <div>1.可授予应用可见范围内的用户为系统管理员，且只有1位</div>
                <div>2.只有系统管理员才可以创建账套，且成为账套管理员</div>
                <div>3.修改系统管理员后，系统权限及账套权限将自动转移为新的管理员</div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="saveProSurperManager">确定</a>
                <a class="button ml-10" v-show="customCanClose" @click="wxworkSelectProSurperManagerDialogShow = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { request, type IResponseModel } from "@/util/service";
import { isInWxWork } from "@/util/wxwork";
import { getCookie } from "@/util/cookie";
import { wxworkSuperAdminOpenId } from "@/util/wxwork";
import { ElNotify } from "@/util/notify";
import { initWxworkConfig } from "./utils";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "ProWorkBench";
const proWorkBenchDialogShow = ref(false);
const wxworkSelectProSurperManagerDialogShow = ref(false);

const tableData = ref<Array<any>>([]);
const columns = ref<Array<IColumnProps>>([
    { slot: "nickname" },
    {
        prop: "mobile",
        label: "账号",
        minWidth: 124,
        align: "left",
        headerAlign: "left",
        formatter(row, cloumn, cellValue) {
            return cellValue.indexOf("108") === 0 ? "" : cellValue;
        },
        width: getColumnWidth(setModule, "mobile")
    },
    { 
        prop: "asName", 
        label: "账套名称", 
        minWidth: 216, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "asName")
    },
    { 
        prop: "productTypeDescription", 
        label: "产品类型", 
        minWidth: 216, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "productTypeDescription")
    },
    {
        prop: "isSuperAdmin",
        label: "操作角色",
        minWidth: 216,
        align: "left",
        headerAlign: "left",
        formatter(row, column, cellValue) {
            return cellValue ? "账套管理员" : "普通用户";
        },
        resizable: false,
    },
]);

const customCanClose = ref(true);
const openid = ref("");
const placeholderShow = ref(true);
async function showSelectProSurperManagerDialog(canClose: boolean) {
    if (canClose) {
        !window.wxworkSuperAdminOpenId && (await wxworkSuperAdminOpenId());
        openid.value = window.wxworkSuperAdminOpenId || "";
        !window.wxworkSuperAdminOpenId && (placeholderShow.value = true);
        setSuperManagerContainer();
    }
    proWorkBenchDialogShow.value = false;
    customCanClose.value = canClose;
    wxworkSelectProSurperManagerDialogShow.value = true;
}
let wxworkAuthMode: any = undefined;
async function getWxWorkAuthMode() {
    if (wxworkAuthMode === undefined) {
        await request({ url: window.jmHost + "/Wxwork/GetAuthMode", method: "post" }).then((r: any) => {
            wxworkAuthMode = r;
        });
    }
    return wxworkAuthMode;
}

function selectProSurperManager() {
    initWxworkConfig(async function () {
        if ((await getWxWorkAuthMode()) == 1) {
            wx.invoke(
                "selectPrivilegedContact",
                {
                    fromDepartmentId: 0, // 必填，表示打开的通讯录从指定的部门开始展示，-1表示自己所在部门开始, 0表示从最上层开始
                    mode: "single", // 必填，选择模式，single表示单选，multi表示多选
                    selectedContextContact: 0, // 是否勾选当前环境的参与者。例如在群+号打开，默认勾选当前群成员。
                },
                (res: any) => {
                    if (res.err_msg == "selectPrivilegedContact:ok") {
                        const selectedUserList = res.result.userList; // 已选的成员列表
                        if (selectedUserList && selectedUserList.length > 0) {
                            const openUserId = selectedUserList[0].openUserId;
                            request({ url: window.jmHost + "/Wxwork/GetBindingUserInfo?openUserId=" + openUserId }).then((r: any) => {
                                openid.value = r.id;
                                hidePlaceholderShow();
                                setSuperManagerContainer();
                            });
                        } else {
                            ElNotify({ type: "warning", message: "成员未授权" });
                        }
                    }
                }
            );
        } else {
            wx.invoke(
                "selectEnterpriseContact",
                {
                    fromDepartmentId: 0, // 必填，表示打开的通讯录从指定的部门开始展示，-1表示自己所在部门开始, 0表示从最上层开始
                    mode: "single", // 必填，选择模式，single表示单选，multi表示多选
                    type: ["user"], // 必填，选择限制类型，指定department、user中的一个或者多个
                    selectedDepartmentIds: [], // 非必填，已选部门ID列表。用于多次选人时可重入，single模式下请勿填入多个id
                    selectedUserIds: [], // 非必填，已选用户ID列表。用于多次选人时可重入，single模式下请勿填入多个id
                },
                function (res: any) {
                    if (res.err_msg == "selectEnterpriseContact:ok") {
                        if (typeof res.result == "string") {
                            res.result = JSON.parse(res.result); //由于目前各个终端尚未完全兼容，需要开发者额外判断result类型以保证在各个终端的兼容性
                        }
                        if (res.result.userList.length > 0) {
                            openid.value = res.result.userList[0].id;
                            hidePlaceholderShow();
                            setSuperManagerContainer();
                        }
                    }
                }
            );
        }
    });
}

const userIdentity = ref(0);
function saveProSurperManager() {
    const wxworkUserId = openid.value;
    if (!wxworkUserId) {
        ElNotify({ type: "warning", message: "请选择柠檬云财务应用可见范围内的用户" });
        return;
    }
    request({
        url: window.jmHost + "/Wxwork/CreateUser?userId=" + wxworkUserId,
        method: "post",
    }).then((r: any) => {
        if (r.code === 0) {
            const userSn = r.userSn;
            if (userSn != window.wxworkSuperAdminUserSn) {
                request({
                    url: window.eHost + "/wb/" + (isInWxWork() ? "wecom/" : "") + "plan/administrator",
                    method: "post",
                    data: { userSn: userSn },
                }).then((r: any) => {
                    if (r.statusCode === true) {
                        userIdentity.value = useAccountSetStoreHook().userInfo?.userSn || 0;
                        if (window.wxworkSuperAdminUserSn) {
                            ElNotify({ type: "success", message: "保存成功" });
                            wxworkSelectProSurperManagerDialogShow.value = false;
                            if (userIdentity.value === window.wxworkSuperAdminUserSn || userIdentity.value === userSn) {
                                setTimeout(function () {
                                    window.location.href =
                                        window.eHost +
                                        "/wb/" +
                                        (isInWxWork() ? "wecom/" : "") +
                                        "redirect?productType=" +
                                        window.productType +
                                        "&appId=1060&virtualAppId=1062&corpid=" +
                                        getCookie("corpId") +
                                        "&suitid=" +
                                        window.wxworkSuitId +
                                        "&wxworkUserId=" +
                                        getCookie("wxworkUserId");
                                }, 300);
                            }
                            window.wxworkSuperAdminUserSn = userSn;
                        } else {
                            window.wxworkSuperAdminUserSn = userSn;
                            if (userSn != userIdentity.value) {
                                ElNotify({ type: "warning", message: "请联系系统管理员创建专业版账套" });
                            } else {
                                ElNotify({ type: "success", message: "保存成功" });
                            }
                            wxworkSelectProSurperManagerDialogShow.value = false;
                            window.location.reload();
                        }
                        window.wxworkSuperAdminOpenId = wxworkUserId;
                    }
                });
            } else {
                ElNotify({ type: "success", message: "保存成功" });
                wxworkSelectProSurperManagerDialogShow.value = false;
                window.location.reload();
            }
        } else if (r.code != -1) {
            ElNotify({ type: "warning", message: r.msg == "此用户未被授权使用应用" ? "请选择柠檬云财务应用可见范围内的用户" : r.msg });
        } else {
            ElNotify({ type: "warning", message: "保存失败" });
        }
    });
}

function showWorkBench() {
    proWorkBenchDialogShow.value = true;
    getCorpName();
    request({
        url: window.eHost + "/wb/" + (isInWxWork() ? "wecom/" : "") + "employee?productType=acc&currentPage=1&pageSize=999",
        headers: { "x-tenant-id": window.wxworkSuitId + "-" + getCookie("corpId") },
        method: "get",
    }).then((res: any) => {
        if (res.statusCode === true) {
            tableData.value = res.data.data;
            tableData.value.forEach((item, index) => {
                if (item.mobile.indexOf("108") === 0) {
                    request({
                        url: "/api/WxWorkOnlyAuth/GetUserId?userSn=" + item.userSn,
                        method: "post",
                    }).then((res: IResponseModel<string>) => {
                        if (res.state === 1000) {
                            item.openid = res.data;
                            WWOpenData.bind(document.querySelector("#proWorkBenchDialog")?.querySelectorAll("ww-open-data")[index]);
                        }
                    });
                }
            });
        }
    });
}

const companyName = ref("");
function getCorpName() {
    request({ url: "/api/WxWorkOnlyAuth/GetCorpName", method: "post" }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            companyName.value = res.data;
        }
    });
}

const wxworkSelectProSurperManager = ref(false);
function showWxworkSelectProSurperManager() {
    wxworkSelectProSurperManager.value = true;
}

function setOpenid(val: string) {
    openid.value = val;
}

function hidePlaceholderShow() {
    placeholderShow.value = false;
}

function setSuperManagerContainer() {
    WWOpenData.bind(document.querySelector("#superManagerContainer")?.querySelector("ww-open-data"));
}

defineExpose({
    showWorkBench,
    showSelectProSurperManagerDialog,
    showWxworkSelectProSurperManager,
    setOpenid,
    hidePlaceholderShow,
    setSuperManagerContainer,
});
</script>

<style lang="less">
body {
    .el-dialog {
        &.pro-workBench-dialog {
            .el-dialog__header {
                border-bottom: none;
                .el-dialog__title {
                    display: inline-block;
                    width: 181px;
                    height: 29px;
                    padding-top: 28px;
                    background: url("@/assets/Icons/title-logo.png") no-repeat center 29px;
                    background-size: 181px 29px;
                }
            }
            .pro-workbench-container {
                .workbench-toolbar {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    color: var(--font-color);
                    font-size: 16px;
                    line-height: 22px;
                    padding: 22px 40px 0;
                    font-weight: 500;
                    .toolbar-left {
                        display: flex;
                        align-items: center;
                        .toolbar-title {
                            margin-right: 28px;
                            font-weight: 600;
                            .solid-button {
                                width: 108px;
                            }
                        }
                    }
                    .toolbar-right {
                        .company-name {
                            margin-right: 5px;
                        }
                    }
                }
                .workbench-table-container {
                    padding: 20px 40px 60px;
                    height: 192px;
                    overflow: auto;
                    .el-table {
                        height: 192px;
                        border-bottom: none;
                        box-shadow: 0 0 1px gray;
                        .el-table__inner-wrapper {
                            &::before {
                                height: 0;
                            }
                        }
                    }
                }
            }
        }

        &.wxwork-select-pro-surpermanager-dialog {
            .wxwork-select-surpermanager-container {
                .manager-selector-container {
                    padding-top: 28px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 14px;
                    color: var(--font-color);
                    line-height: 22px;
                    .manager-selector {
                        border-radius: 2px;
                        border: 1px solid var(--input-border-color);
                        position: relative;
                        width: 200px;
                        height: 28px;
                        box-sizing: border-box;
                        display: flex;
                        align-items: center;
                        padding-left: 10px;
                        .placeholder {
                            color: #999999;
                        }
                        .more-btn {
                            position: absolute;
                            right: 0;
                            top: 0;
                            background: url("@/assets/Icons/more-icon.png") no-repeat;
                            background-size: 14px 4px;
                            background-position: center;
                            width: 26px;
                            height: 26px;
                            cursor: pointer;
                        }
                    }
                }
                .tips {
                    padding: 35px 45px 17px;
                    font-size: 12px;
                    line-height: 22px;
                    color: #666666;
                }
                .buttons {
                    border-top: 1px solid var(--border-color);
                }
            }
        }
    }
}
</style>
