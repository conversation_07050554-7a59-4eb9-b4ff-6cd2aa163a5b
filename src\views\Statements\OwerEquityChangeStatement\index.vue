<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <span>会计年度：</span>
                            <el-select v-model="yearId" :teleported="false" @change="handleSelectYearChange">
                                <el-option v-for="item in selectYearList" :key="item" :label="item + '年'" :value="item"></el-option>
                            </el-select>
                            <el-popover placement="bottom-start" :width="400" trigger="hover">
                                <template #reference>
                                    <span class="statement-about ml-10"> 报表说明 </span>
                                </template>
                                <div class="statement-about-popover">
                                    <div>1.系统已为部分字段设置了默认公式，对于存在分析填列的场景仍支持手工修改</div>
                                    <div>2.鼠标挪入对应文本框，点击编辑按钮后可修改金额</div>
                                    <div>3.有底色的文本框为手工录入的数据 <span class="color-lump"></span></div>
                                </div>
                            </el-popover>
                            <ErpRefreshButton></ErpRefreshButton>
                            <div v-show="!isBalance" class="uneven-prompt">
                                <Tooltip :content="reason" :max-width="500" :line-clamp="1" placement="bottom" :teleported="true">
                                    <img v-show="isErp" src="@/assets/Erp/tip-erp.png" alt="" class="uneven-prompt-icon ml-10" />
                                    <span :class="['highlight-red uneven-prompt-text', { 'pl-10': !isErp }]">{{ reason }}</span>
                                </Tooltip>
                            </div>
                        </div>
                        <div class="main-tool-right">
                            <a
                                class="button large-1"
                                v-show="resetSHow"
                                v-permission="['owerequitychangesheet-canedit']"
                                @click="handleResetAll"
                            >
                                按公式重新计算
                            </a>
                            <a class="button ml-16" v-permission="['owerequitychangesheet-canedit']" @click="handleToggleEditState">
                                {{ editText }}
                            </a>
                            <a class="button ml-16" @click="handleExport" v-permission="['owerequitychangesheet-canexport']">导出</a>
                            <a
                                class="button solid-button ml-10"
                                v-if="resetButtonShow"
                                @click="handleResetEquation"
                                v-permission="['owerequitychangesheet-canedit']"
                            >
                                重置公式
                            </a>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div :class="{ 'main-center': true, erp: isErp, showDataMask: showDataMask }">
                        <el-table
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                            border
                            fit
                            stripe
                            scrollbar-always-on
                            highlight-current-row
                            :data="tableData"
                            :empty-text="emptyText"
                            :class="{ 'custom-table': true, 'erp-table': isErp }"
                            :row-class-name="rowClassName"
                            :cell-class-name="assertCellClassName"
                            @cell-mouse-enter="resetDefaultRowClass"
                            @header-dragend="headerDragend"
                        >
                            <el-table-column 
                                label="项目" 
                                min-width="340" 
                                align="left" 
                                headerAlign="center"
                                prop="lineName"
                                :width="getColumnWidth(setModule, 'lineName')"
                            >
                                <template #default="{ row }: { row: ITableData }">
                                    <div :class="[assertNameClass(row.lineName)]">
                                        <span>{{ row.lineName }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                v-for="(firstLevel, firstLevelIndex) in firstLevelArray"
                                :key="firstLevelIndex"
                                :label="firstLevel.label"
                                headerAlign="center"
                            >
                                <el-table-column 
                                    :label="labelArray[0]" 
                                    min-width="120"
                                    headerAlign="right" 
                                    align="right"
                                    :prop="firstLevel.label + labelArray[0]"
                                    :width="getColumnWidth(setModule, firstLevel.label + labelArray[0])"
                                >
                                    <template #default="{ row, $index }: { row: ITableData, $index: number }">
                                        <TableAmountItem
                                            :hasTip="hasTip"
                                            :hideTip="hideTip"
                                            :showMask="showMask"
                                            :canEditByInput="canEditByInput"
                                            :isRequest="isRequest"
                                            :showReset="firstLevelIndex === 0 && !!row.cells[0].createdManually"
                                            :canInput="
                                                $index !== 0 &&
                                                $index !== tableData.length - 1 &&
                                                row.cells[0].lineType !== 3 &&
                                                firstLevelIndex === 0
                                            "
                                            :isEditing="isEditing"
                                            v-model:isInput="isInput"
                                            :amount="row.cells[0][firstLevel.isLastYear ? 'lastYearAmount' : 'yearAmount']"
                                            :formula="calcFormula(row.cells[0].note, firstLevel.isLastYear ? 1 : 0)"
                                            :line-number="row.lineNo"
                                            :showEditPen="checkShowEditPen(row.cells[0], firstLevelIndex)"
                                            @to-edit-equation="handleToEdit(row.cells[0].lineID, row.lineName, labelArray[0])"
                                            @save-amount="(amount: string, resetHandle: () => void ) => handleSaveAmount(amount, resetHandle, row.cells[0].lineID, row.lineNo)"
                                            @reset="(close: () => void) => handleReset(row.cells[0].lineID, close)"
                                        ></TableAmountItem>
                                    </template>
                                </el-table-column>
                                <el-table-column 
                                    label="其他权益工具" 
                                    headerAlign="center" 
                                    align="left"
                                >
                                    <el-table-column
                                        v-for="index in 3"
                                        :key="index"
                                        :label="labelArray[index]"
                                        min-width="120"
                                        headerAlign="right"
                                        align="right"
                                        :prop="labelArray[index]"
                                        :width="getColumnWidth(setModule, labelArray[index])"
                                    >
                                        <template #default="{ row, $index }: { row: ITableData, $index: number }">
                                            <TableAmountItem
                                                :hasTip="hasTip"
                                                :hideTip="hideTip"
                                                :showMask="showMask"
                                                :canEditByInput="canEditByInput"
                                                :isRequest="isRequest"
                                                :showReset="firstLevelIndex === 0 && !!row.cells[index].createdManually"
                                                :canInput="
                                                    $index !== 0 &&
                                                    $index !== tableData.length - 1 &&
                                                    row.cells[index].lineType !== 3 &&
                                                    firstLevelIndex === 0
                                                "
                                                :isEditing="isEditing"
                                                v-model:isInput="isInput"
                                                :amount="row.cells[index][firstLevel.isLastYear ? 'lastYearAmount' : 'yearAmount']"
                                                :formula="calcFormula(row.cells[index].note, firstLevel.isLastYear ? 1 : 0)"
                                                :line-number="row.lineNo"
                                                :showEditPen="checkShowEditPen(row.cells[index], firstLevelIndex)"
                                                @to-edit-equation="handleToEdit(row.cells[index].lineID, row.lineName, labelArray[index])"
                                                @save-amount="(amount: string, resetHandle: () => void ) => handleSaveAmount(amount, resetHandle, row.cells[index].lineID, row.lineNo)"
                                                @reset="(close: () => void) => handleReset(row.cells[index].lineID, close)"
                                            ></TableAmountItem>
                                        </template>
                                    </el-table-column>
                                </el-table-column>
                                <el-table-column
                                    v-for="index in cycleIndex"
                                    :key="index"
                                    :label="labelArray[index + 3]"
                                    min-width="120"
                                    headerAlign="right"
                                    align="right"
                                    :prop="labelArray[index + 3]"
                                    :width="getColumnWidth(setModule, labelArray[index + 3])"
                                    :resizable="firstLevelIndex !== firstLevelArray.length -1 || index !== cycleIndex"
                                >
                                    <template #default="{ row, $index }: { row: ITableData, $index: number }">
                                        <TableAmountItem
                                            :hasTip="hasTip"
                                            :hideTip="hideTip"
                                            :showMask="showMask"
                                            :canEditByInput="canEditByInput"
                                            :isRequest="isRequest"
                                            :showReset="firstLevelIndex === 0 && !!row.cells[index + 3].createdManually"
                                            :canInput="
                                                $index !== 0 &&
                                                $index !== tableData.length - 1 &&
                                                row.cells[index + 3].lineType !== 3 &&
                                                firstLevelIndex === 0
                                            "
                                            :isEditing="isEditing"
                                            v-model:isInput="isInput"
                                            :canEditEquation="checkCanEdit(row.cells[index + 3].lineID)"
                                            :amount="row.cells[index + 3][firstLevel.isLastYear ? 'lastYearAmount' : 'yearAmount']"
                                            :formula="calcFormula(row.cells[index + 3].note, firstLevel.isLastYear ? 1 : 0)"
                                            :line-number="row.lineNo"
                                            :showEditPen="checkShowEditPen(row.cells[index + 3], firstLevelIndex)"
                                            @to-edit-equation="
                                                handleToEdit(row.cells[index + 3].lineID, row.lineName, labelArray[index + 3])
                                            "
                                            @save-amount="(amount: string, resetHandle: () => void ) => handleSaveAmount(amount, resetHandle, row.cells[index + 3].lineID, row.lineNo)"
                                            @reset="(close: () => void) => handleReset(row.cells[index + 3].lineID, close)"
                                        ></TableAmountItem>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                        </el-table>
                        <DataMask v-if="showDataMask" ref="dataMaskRef" :showLines="5" :hasPage="false" />
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <EditEquation
                        ref="editEquationRef"
                        :class="{ 'slot-mini-content': true, erp: isErp }"
                        :statement-id="editData.statementId"
                        :line-id="editData.lineId"
                        :pid="yearId"
                        :title="editData.title"
                        month-title="本年金额"
                        year-title="上年金额"
                        :value-type-options="valueTypeOptions"
                        @handle-submit-success="handleReloadPage"
                        @handle-cancel="handleBack"
                    ></EditEquation>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "OwerEquityChangeStatement",
};
</script>
<script setup lang="ts">
import { ref, reactive, computed, nextTick, onBeforeMount } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { checkPermission } from "@/util/permission";
import { getGlobalToken } from "@/util/baseInfo";

import { PeriodStatus } from "@/api/period";

import ContentSlider from "@/components/ContentSlider/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import TableAmountItem from "./components/TableAmountItem.vue";
import DataMask from "@/components/DataMask/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "OwnerSheet";
interface ITableData {
    lineNo: number;
    lineName: string;
    showLevel: number;
    cells: Array<ICells>;
}

enum CreatedManually {
    No = 0,
    Yes = 1,
}

interface ICells {
    statementID: number;
    lineID: number;
    lineType: number;
    partentID: number;
    yearAmount: number;
    lastYearAmount: number;
    note: string;
    createdManually: CreatedManually;
    lastCreatedManually: CreatedManually;
}

interface ISearchBack {
    list: Array<ITableData>;
    balanceStatus: {
        isBalance: boolean;
        reason: string;
    };
}

const editEquationRef = ref<InstanceType<typeof EditEquation>>();
const dataMaskRef = ref<InstanceType<typeof DataMask>>();

const accountSet = useAccountSetStore().accountSet;
const trialStatusStore = useTrialStatusStore();
const isExpired = computed(() => trialStatusStore.isExpired);
const showDataMask = computed(() => isExpired.value && tableData.value.length > 5);
const isErp = ref(window.isErp);
const slots: Array<string> = ["main", "edit"];
const currentSlot = ref<"main" | "edit">("main");
const periodList = useAccountPeriodStore().periodList;
const selectYearList = computed(() => {
    const yearList: number[] = [];
    periodList.reduce((prev, curr) => {
        if (prev.findIndex((item) => item === curr.year) === -1) {
            yearList.unshift(curr.year);
        }
        return prev;
    }, yearList);
    return yearList;
});
const canEditByInput = computed(() => {
    const item = periodList.find((item) => item.year === yearId.value && item.sn === 12);
    if (item && item.status === PeriodStatus.CheckOut) return false;
    if (!checkPermission(["owerequitychangesheet-canedit"])) return false;
    return true;
});
const asStartYear = computed(() => selectYearList.value[0]);
const yearId = ref(asStartYear.value);
const tableData = ref<Array<ITableData>>([]);
const emptyText = ref("");
const loading = ref(false);
const isBalance = ref(true);
const reason = ref("");
let cacheYearid = 0;
function handleSelectYearChange() {
    if (!showMask()) {
        yearId.value = cacheYearid;
        return;
    }
    handleSearch();
}
const resetSHow = ref(false);
function showMask() {
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    return true;
}
function handleSearch() {
    if (!showMask()) return;
    loading.value = true;
    request({ url: "/api/OwerEquityChangeStatement?yearid=" + yearId.value })
        .then((res: IResponseModel<ISearchBack>) => {
            isEditing.value = false;
            loading.value = false;
            if (res.state === 1000) {
                const data: Array<ITableData> = res.data.list;
                data.forEach((item) => {
                    item.lineName = item.lineName.split("|")[1].trim();
                });

                tableData.value = data;
                emptyText.value = "";
                isBalance.value = res.data.balanceStatus.isBalance;
                reason.value = res.data.balanceStatus.reason;
                resetSHow.value = tableData.value.some((item) => item.cells.some((cell) => cell.createdManually));
            } else {
                tableData.value = [];
                emptyText.value = "暂无数据";
            }
        })
        .catch(() => {
            isEditing.value = false;
            loading.value = false;
            tableData.value = [];
            emptyText.value = "暂无数据";
        })
        .finally(() => {
            cacheYearid = yearId.value;
            nextTick().then(() => {
                if (showDataMask.value) {
                    dataMaskRef.value?.getTableHeight();
                }
            });
        });
}
function handleExport() {
    if (!showMask()) return;
    globalExport(`/api/OwerEquityChangeStatement/Export?yearid=${yearId.value}`);
}

const labelArray: Array<string> = [
    "实收资本",
    "优先股",
    "永续债",
    "其他",
    "资本公积",
    "减：库存股",
    "其他综合收益",
    "专项储备",
    "盈余公积",
    "未分配利润",
    "所有者权益合计",
];
const cycleIndex = labelArray.length - 4; // 从其他权益工具后面开始循环
const firstLevelArray = [
    { label: "本年金额", isLastYear: false },
    { label: "上年金额", isLastYear: true },
];
const reportStatementId = computed(() => {
    const accountingStandard = accountSet?.accountingStandard as number;
    const subAccountingStandard = accountSet?.subAccountingStandard as number;
    let reportType = 0;
    if (accountingStandard === 1) {
        reportType = ReportTypeEnum.OwerEquityChangeStatementDefault;
    } else if (accountingStandard === 2) {
        // 已执行
        if (subAccountingStandard === 2) {
            reportType = ReportTypeEnum.OwerEquityChangeStatementExecuted;
        } else if (subAccountingStandard === 1) {
            reportType = ReportTypeEnum.OwerEquityChangeStatementUnExecuted;
        } else {
            reportType = ReportTypeEnum.OwerEquityChangeStatementDefault;
        }
    }
    return accountingStandard * 1000 + reportType;
});
const editData = reactive({
    statementId: reportStatementId.value,
    lineId: 0,
    title: "",
});
const resetButtonShow = ref(false);
function handleResetEquation() {
    if (!showMask()) return;
    ElConfirm("确认删除此报表所有自定义公式？", false, () => {}, "重置此报表公式").then((r: boolean) => {
        if (r) {
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${reportStatementId.value}`,
                method: "post",
            })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state === 1000) {
                        ElNotify({ type: "success", message: "已经成功重置" });
                        resetButtonShow.value = false;
                        handleSearch();
                    } else {
                        ElNotify({ type: "warning", message: res.msg || "重置失败" });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "重置失败" });
                });
        }
    });
}
function checkHasCustomEqutions() {
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${reportStatementId.value}`,
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                resetButtonShow.value = res.data;
            } else {
                resetButtonShow.value = false;
            }
        })
        .catch(() => {
            resetButtonShow.value = false;
        });
}
function handleReloadPage() {
    handleInit();
    currentSlot.value = "main";
}
function handleBack() {
    currentSlot.value = "main";
}

const lineID = ref(-1);
const valueTypeOptions = [
    { value: "0", label: "期初余额" },
    { value: "2", label: "累计借方发生额" },
    { value: "3", label: "累计贷方发生额" },
    { value: "1", label: "余额" },
];
const isEditing = ref(false);
function checkShowEditPen(cellValue: ICells, index: number) {
    const { lineType, lineID } = cellValue;
    // index 0 本年金额 1 上年金额
    // 上年金额不可编辑
    const canEdit =
        index !== 1 && isEditing.value && lineType !== 3 && checkPermission(["owerequitychangesheet-canedit"]) && checkCanEdit(lineID);
    return canEdit;
}
const editText = computed(() => (isEditing.value ? "取消编辑" : "编辑公式"));
function handleToEdit(id: number, lineName: string, label: string) {
    lineID.value = id;
    const index = labelArray.findIndex((item) => item === label);
    if (index === -1 || index === labelArray.length - 1) {
        lineID.value = -1;
        editData.title = "";
    }
    if (lineID.value === -1) {
        ElNotify({ type: "warning", message: "请选择要设置公式的单元格" });
        return;
    }
    editData.lineId = lineID.value;
    let labelText = label;
    if ([1, 2, 3].includes(index)) {
        labelText = `其他权益工具-${label}`;
    }
    editData.title = resetRowName(lineName) + "-" + labelText;
    nextTick().then(() => {
        editEquationRef.value?.init();
        currentSlot.value = "edit";
    });
}

function handleInit() {
    handleSearch();
    checkHasCustomEqutions();
}
handleInit();

function assertNameClass(lineName: string) {
    const firstLevel = ["一、", "二、", "三、", "四、"];
    const secondLevel = ["（一）", "（二）", "（三）", "（四）"];
    const thirdLevel = ["1", "2", "3", "4", "5", "6"];
    let className: string;
    if (hasNumberTitle(lineName, firstLevel)) {
        className = "level1";
    } else if (hasNumberTitle(lineName, secondLevel)) {
        className = "level2";
    } else if (hasNumberTitle(lineName, thirdLevel)) {
        className = "level3";
    } else {
        className = "level2";
    }
    return className;
}
function hasNumberTitle(value: string, chinaNumber: string[]) {
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.startsWith(chinaNumber[i])) return true;
    }
    return false;
}
function calcFormula(note: string, columnIndex: 0 | 1) {
    if (!note) return "";
    let formula = note?.split("|")[columnIndex] ?? "";
    return formula;
}
function resetRowName(rowName: string) {
    let returnRowName = rowName.replace("（减少以“-”号填列）", "").trim();
    const splitSymbolArray = ["、", "．", "：", "）"];
    if (splitSymbolArray.some((item) => returnRowName.includes(item))) {
        const index = returnRowName.indexOf(splitSymbolArray.find((item) => returnRowName.includes(item)) as string);
        returnRowName = returnRowName.slice(index + 1);
    }
    return returnRowName;
}
let highLight = ref(false);
function resetDefaultRowClass() {
    highLight.value = false;
}
function handleToggleEditState() {
    if (!showMask()) return;
    isEditing.value = !isEditing.value;
    if (isEditing.value) {
        highLight.value = true;
    }
}
function rowClassName({ rowIndex }: { rowIndex: number }) {
    if (rowIndex === 0 && highLight.value) {
        return "default-edit-row";
    }
}
interface IRowInfo {
    row: ITableData;
    column: any;
    rowIndex: number;
    columnIndex: number;
}
function assertCellClassName(rowInfo: IRowInfo) {
    const { row, columnIndex } = rowInfo;
    if (columnIndex > 0) {
        if (columnIndex <= 11) {
            if (row.cells[columnIndex - 1]?.createdManually) {
                return "custom-input-cell";
            }
        } else {
            if (row.cells[columnIndex - 12]?.lastCreatedManually) {
                return "custom-input-cell";
            }
        }
    }
}
const isRequest = ref(false);
function handleSaveAmount(amount: string, resetHandle: () => void, lineID: number, lineNo: number) {
    if (isRequest.value) return;
    isRequest.value = true;
    const params = {
        yearid: yearId.value,
        lineid: lineID,
        amount,
        statementid: reportStatementId.value,
    };
    request({
        url: "/api/OwerEquityChangeStatement?" + getUrlSearchParams(params),
        method: "put",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                isRequest.value = false;
                ElNotify({ type: "warning", message: res.msg || "保存失败" });
                return;
            }
            ElNotify({ type: "success", message: "保存成功" });
            resetHandle();
            handleSearch();
            isRequest.value = false;
        })
        .catch(() => {
            isRequest.value = false;
            ElNotify({ type: "warning", message: "保存失败" });
        });
}
function handleReset(lineid: number, close?: () => void) {
    const params = { yearid: yearId.value, lineid, statementid: reportStatementId.value };
    request({
        url: "/api/OwerEquityChangeStatement?" + getUrlSearchParams(params),
        method: "delete",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                isRequest.value = false;
                ElNotify({ type: "warning", message: res.msg || "按公式重新计算失败" });
                return;
            }
            close && close();
            ElNotify({ type: "success", message: "按公式重新计算成功" });
            handleSearch();
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "按公式重新计算失败" });
        });
}
function handleResetAll() {
    if (!showMask()) return;
    ElConfirm(
        `<div style="text-align: left;">${yearId.value}年度所有手工录入的金额将会按照公式重新计算哦~<br />是否继续重置？</div>`,
        false,
        () => {},
        "提示",
        {
            confirmButtonText: "继续重置",
            cancelButtonText: "取消",
        }
    ).then((r: boolean) => {
        if (r) handleReset(-1);
    });
}
const isInput = ref(false);
const hasTip = ref(false);
onBeforeMount(() => {
    hasTip.value = window.localStorage.getItem("ownerEquityChangeStatementTip+" + getGlobalToken()) === "true";
});
function hideTip() {
    hasTip.value = true;
    window.localStorage.setItem("ownerEquityChangeStatementTip+" + getGlobalToken(), "true");
}
function checkCanEdit(lineID: number) {
    const notCanEditList = [10472130, 20471440, 20472130, 20491440, 20492130, 20481440, 20482130];
    if (notCanEditList.includes(lineID)) return false;
    return true;
}
const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.content {
    .slot-content {
        .slot-mini-content:not(.erp) {
            width: 1100px;
        }
    }

    .main-tool-right {
        a.button.large-1 {
            padding: 0 10px;
        }
    }

    .main-center {
        position: relative;
        &:not(.showDataMask) {
            flex: 1 !important;
            :deep(.el-table) {
                display: flex;
                flex-direction: column;
                .el-table__inner-wrapper {
                    flex: 1;
                }
                .el-scrollbar {
                    margin-bottom: 14px;
                    height: calc(100% - 14px) !important;
                    box-sizing: border-box;
                    border-bottom: 1px solid var(--border-color);
                    overflow: visible;
                    .el-table__row {
                        &:last-child {
                            .el-table__cell {
                                border-bottom: none;
                            }
                        }
                        .custom-input-cell {
                            background-color: #fff5e6;
                        }
                    }
                    .el-scrollbar__bar {
                        &.is-horizontal {
                            bottom: -13px;
                        }
                    }
                }
            }
        }

        &.showDataMask {
            :deep(.el-table) {
                .el-scrollbar__view {
                    min-height: 0;
                    height: 535px;
                    overflow: hidden;
                }
                // 底部滚动条样式
                .el-scrollbar__bar {
                    &.is-horizontal {
                        height: 8px;
                        bottom: 1px;
                        background-color: #fff;
                        .el-scrollbar__thumb {
                            &:hover {
                                filter: brightness(1.2);
                                opacity: 1;
                            }
                        }
                    }
                }
            }
        }
    }
}
.statement-about {
    display: inline-block;
    padding-right: 20px;
    cursor: pointer;
    background-image: url(@/assets/Settings/question.png);
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: right center;
}
</style>
<style lang="less">
.statement-about-popover {
    font-size: var(--font-size);
    div + div {
        margin-top: 5px;
        display: flex;
        align-items: center;
    }
    .color-lump {
        display: inline-block;
        width: 40px;
        height: 20px;
        background-color: #fff5e6;
        margin-left: 10px;
    }
}
</style>
