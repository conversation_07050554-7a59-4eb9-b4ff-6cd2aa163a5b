import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney, formatQuantity } from "@/util/format";
import { ref } from "vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const rankCell = (asubCode: any, val: any, codeLengthStr: string) => {
    let formatString = "";
    const code = asubCode.toString().split("_")[0];
    val === "0" ? "" : val;
    // 空格
    for (let i = 0; i < codeLengthStr.length; i++) {
        let length = 0;
        let space = "";
        for (let j = 0; j <= i; j++) {
            length += Number(codeLengthStr[j]);
            space += "&nbsp;&nbsp;";
        }
        if (length == code.length) {
            // formatString = "<span class='level" + (i + 1) + "' >" + val + "</span>";
            formatString = `
            <span>${space}${val}</span>`;
            break;
        }
    }

    return formatString;
};
export const setColumns = (
    // 按月显示
    showPeriod: Boolean,
    // 显示数量金额
    showQuantity: Boolean,
    // 显示本年累计
    showYear: Boolean,
    codeLengthStr: string,
    fcid: number
): Array<IColumnProps> => {
    const setModule = "TrialBalance";
    const columns = ref<Array<IColumnProps>>([]);
    columns.value = [
        showPeriod
            ? {
                  label: "会计年度",
                  prop: "year",
                  align: "left",
                  headerAlign: "left",
                  formatter: (row, column, value) => {
                      if (!row || !value) {
                          return "";
                      }
                      return value;
                  },
                  width: getColumnWidth(setModule, 'year'), 
              }
            : {},
        showPeriod
            ? {
                  label: "会计期间",
                  prop: "month",
                  align: "left",
                  headerAlign: "left",
                  formatter: (row, column, value) => {
                      if (!row || !value) {
                          return "";
                      }
                      return value;
                  },
                  width: getColumnWidth(setModule, 'month'), 
              }
            : {},
        { slot: "asub_code" },
        // 科目名称
        { slot: "name" },
        fcid !== -1
            ? {
                  label: "币别",
                  prop: "fc_code",
                  minWidth: showQuantity ? 50 : 60,
                  align: "left",
                  headerAlign: "left",
                  width: getColumnWidth(setModule, 'fc_code'),
              }
            : {},
        {
            label: "期初余额",
            headerAlign: "center",
            children: [
                showQuantity
                    ? {
                          slot: "customizePrompt",
                          label: "借方数量",
                          prop: "initial_debit_qut",
                          minWidth: 60,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatQuantity(value);
                          },
                          width: getColumnWidth(setModule, 'initial_debit_qut'),
                      }
                    : {},
                fcid === -1
                    ? {}
                    : {
                          label: "借方(原币)",
                          prop: "initial_debit_fc",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'initial_debit_fc'),
                      },
                fcid === -1
                    ? {
                          label: `借方${showQuantity ? "金额" : ""}`,
                          prop: "initial_debit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'initial_debit'),
                      }
                    : {
                          label: "借方(本位币)",
                          prop: "initial_debit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'initial_debit'),
                      },
                showQuantity
                    ? {
                          slot: "customizePrompt",
                          label: "贷方数量",
                          prop: "initial_credit_qut",
                          minWidth: 60,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatQuantity(value);
                          },
                          width: getColumnWidth(setModule, 'initial_credit_qut'),
                      }
                    : {},
                fcid === -1
                    ? {}
                    : {
                          label: "贷方(原币)",
                          prop: "initial_credit_fc",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'initial_credit_fc'),
                      },
                fcid === -1
                    ? {
                          label: `贷方${showQuantity ? "金额" : ""}`,
                          prop: "initial_credit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'initial_credit'),
                      }
                    : {
                          label: "贷方(本位币)",
                          prop: "initial_credit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'initial_credit'),
                      },
            ],
        },
        {
            label: "本期发生额",
            headerAlign: "center",
            children: [
                showQuantity
                    ? {
                          slot: "customizePrompt",
                          label: "借方数量",
                          prop: "debit_qut",
                          minWidth: 60,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatQuantity(value);
                          },
                          width: getColumnWidth(setModule, 'debit_qut'),
                      }
                    : {},
                fcid === -1
                    ? {}
                    : {
                          label: "借方(原币)",
                          prop: "debit_fc",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'debit_fc'),
                      },
                fcid === -1
                    ? {
                          label: `借方${showQuantity ? "金额" : ""}`,
                          prop: "debit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'debit'),
                      }
                    : {
                          label: "借方(本位币)",
                          prop: "debit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'debit'),
                      },
                showQuantity
                    ? {
                          slot: "customizePrompt",
                          label: "贷方数量",
                          prop: "credit_qut",
                          minWidth: 60,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatQuantity(value);
                          },
                          width: getColumnWidth(setModule, 'credit_qut'),
                      }
                    : {},
                fcid === -1
                    ? {}
                    : {
                          label: "贷方(原币)",
                          prop: "credit_fc",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'credit_fc'),
                      },
                fcid === -1
                    ? {
                          label: `贷方${showQuantity ? "金额" : ""}`,
                          prop: "credit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'credit'),
                      }
                    : {
                          label: "贷方(本位币)",
                          prop: "credit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'credit'),
                      },
            ],
        },
        showYear
            ? {
                  label: "本年累计发生额",
                  headerAlign: "center",
                  children: [
                      showQuantity
                          ? {
                                slot: "customizePrompt",
                                label: "借方数量",
                                prop: "year_debit_qut",
                                minWidth: 60,
                                align: "left",
                                headerAlign: "left",
                                formatter: (row, column, value) => {
                                    return formatQuantity(value);
                                },
                                width: getColumnWidth(setModule, 'year_debit_qut'),
                            }
                          : {},
                      fcid === -1
                          ? {}
                          : {
                                label: "借方(原币)",
                                prop: "year_debit_fc",
                                minWidth: 100,
                                align: "right",
                                headerAlign: "right",
                                formatter: (row, column, value) => {
                                    return formatMoney(value);
                                },
                                width: getColumnWidth(setModule, 'year_debit_fc'),
                            },
                      fcid === -1
                          ? {
                                label: `借方${showQuantity ? "金额" : ""}`,
                                prop: "year_debit",
                                minWidth: 100,
                                align: "right",
                                headerAlign: "right",
                                formatter: (row, column, value) => {
                                    return formatMoney(value);
                                },
                                width: getColumnWidth(setModule, 'year_debit'),
                            }
                          : {
                                label: "借方(本位币)",
                                prop: "year_debit",
                                minWidth: 100,
                                align: "right",
                                headerAlign: "right",
                                formatter: (row, column, value) => {
                                    return formatMoney(value);
                                },
                                width: getColumnWidth(setModule, 'year_debit'),
                            },
                      showQuantity
                          ? {
                                slot: "customizePrompt",
                                label: "贷方数量",
                                prop: "year_credit_qut",
                                minWidth: 60,
                                align: "left",
                                headerAlign: "left",
                                formatter: (row, column, value) => {
                                    return formatQuantity(value);
                                },
                                width: getColumnWidth(setModule, 'year_credit_qut'),
                            }
                          : {},
                      fcid === -1
                          ? {}
                          : {
                                label: "贷方(原币)",
                                prop: "year_credit_fc",
                                minWidth: 100,
                                align: "right",
                                headerAlign: "right",
                                formatter: (row, column, value) => {
                                    return formatMoney(value);
                                },
                                width: getColumnWidth(setModule, 'year_credit_fc'),
                            },

                      fcid === -1
                          ? {
                                label: `贷方${showQuantity ? "金额" : ""}`,
                                prop: "year_credit",
                                minWidth: 100,
                                align: "right",
                                headerAlign: "right",
                                formatter: (row, column, value) => {
                                    return formatMoney(value);
                                },
                                width: getColumnWidth(setModule, 'year_credit'),
                            }
                          : {
                                label: "贷方(本位币)",
                                prop: "year_credit",
                                minWidth: 100,
                                align: "right",
                                headerAlign: "right",
                                formatter: (row, column, value) => {
                                    return formatMoney(value);
                                },
                                width: getColumnWidth(setModule, 'year_credit'),
                            },
                  ],
              }
            : {},
        {
            label: "期末余额",
            headerAlign: "center",
            children: [
                showQuantity
                    ? {
                          slot: "customizePrompt",
                          label: "借方数量",
                          prop: "total_debit_qut",
                          minWidth: 60,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatQuantity(value);
                          },
                          width: getColumnWidth(setModule, 'total_debit_qut'),
                      }
                    : {},
                fcid === -1
                    ? {}
                    : {
                          label: "借方(原币)",
                          prop: "total_debit_fc",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'total_debit_fc'),
                      },
                fcid === -1
                    ? {
                          label: `借方${showQuantity ? "金额" : ""}`,
                          prop: "total_debit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'total_debit'),
                      }
                    : {
                          label: "借方(本位币)",
                          prop: "total_debit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'total_debit'),
                      },

                showQuantity
                    ? {
                          slot: "customizePrompt",
                          label: "贷方数量",
                          prop: "total_credit_qut",
                          minWidth: 60,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatQuantity(value);
                          },
                          width: getColumnWidth(setModule, 'total_credit_qut'),
                      }
                    : {},
                fcid === -1
                    ? {}
                    : {
                          label: "贷方(原币)",
                          prop: "total_credit_fc",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'total_credit_fc'),
                      },
                fcid === -1
                    ? {
                          label: `贷方${showQuantity ? "金额" : ""}`,
                          prop: "total_credit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'total_credit'),
                          resizable: false,
                      }
                    : {
                          label: "贷方(本位币)",
                          prop: "total_credit",
                          minWidth: 100,
                          align: "right",
                          headerAlign: "right",
                          formatter: (row, column, value) => {
                              return formatMoney(value);
                          },
                          width: getColumnWidth(setModule, 'total_credit'),
                          resizable: false,
                      },
            ],
        },
    ];

    return columns.value;
};

export function extractParams(str: string): { ASUB_ID: string; AA_CODE: string; period_s: string; period_e: string } {
    const regex = /ASUB_ID=(\d+)&AA_CODE=([^&]+)?&period_s=(\d+)&period_e=(\d+)/;
    const match = str.match(regex);
    if (match) {
        const [, ASUB_ID, , period_s, period_e] = match;
        let AA_CODE = match[2];
        if (!AA_CODE) {
            AA_CODE = "";
        }
        return { ASUB_ID, AA_CODE, period_s, period_e };
    }
    return { ASUB_ID: "", AA_CODE: "", period_s: "", period_e: "" };
}
