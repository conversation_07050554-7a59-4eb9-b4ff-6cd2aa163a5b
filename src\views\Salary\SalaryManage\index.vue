<template>
  <div class="content" :style="isErp ? 'overflow-y:visible;' : ''">
    <ContentSlider :slots="slots" :currentSlot="currentSlot">
      <template #main>
        <div
          :class="['main-content', { 'part-voucher': activeName === 'second' && partCurrentSlot === 'partVoucher' }]"
          :style="isErp ? 'overflow-y:auto;' : ''">
          <div class="title">工资管理</div>
          <el-tabs v-model="activeName" @tab-change="handleClick">
            <el-tab-pane label="工资列表" name="first">
              <div class="main-top main-tool-bar space-between" :class="{ 'split-line': !isErp }">
                <div class="main-tool-left">
                  <SearchInfoContainer ref="containerRef">
                    <template v-slot:title>{{ salaryTitle }}</template>
                    <div class="line-item input">
                      <div class="line-item-title">工资月份：</div>
                      <div class="line-item-field">
                        <div class="jqtransform float-l date-picker">
                          <el-date-picker
                            v-model="searchInfo.month"
                            type="month"
                            :disabled-date="disabledDate"
                            :clearable="false"
                            :editable="false"
                            :teleported="false"
                            :format="'YYYY年MM月'"
                            value-format="YYYYMM"
                            style="width: 123px"
                            class="date-picker-class" />
                        </div>
                      </div>
                    </div>
                    <div class="line-item input">
                      <div class="line-item-title">部门：</div>
                      <div class="line-item-field">
                        <div class="jqtransform float-l">
                          <Select
                            v-model="searchInfo.department"
                            placeholder="请选择"
                            :teleported="false"
                            :fit-input-width="true"
                            :style="{ width: '123px' }"
                            :filterable="true"
                            :filter-method="departFilterMethod">
                            <Option :label="item.label" :value="item.value" v-for="item in showDepartmentList" :key="item.value"></Option>
                          </Select>
                        </div>
                      </div>
                    </div>
                    <div class="line-item input">
                      <div class="line-item-title">姓名：</div>
                      <div class="line-item-field">
                        <Tooltip :content="searchInfo.name" :isInput="true" placement="right" :teleported="true">
                          <el-input :style="{ width: '123px' }" v-model="searchInfo.name" placeholder="" clearable></el-input>
                        </Tooltip>
                      </div>
                    </div>
                    <div class="buttons" :style="isErp ? 'flex-direction:row' : ''">
                      <a class="button solid-button" @click="handleSearch">确定</a>
                      <a class="button" @click="handleClose">取消</a>
                      <a class="button" @click="handleReset">重置</a>
                    </div>
                  </SearchInfoContainer>
                  <div class="link ml-20" @click="customWageClick" v-permission="['salarymanage-project-canview']">自定义工资项目</div>
                </div>
                <div class="main-tool-right">
                  <div class="mr-20">
                    <el-checkbox
                      v-model="searchInfo.showInfo"
                      :label="checkboxInfo || !checkPermission(['salarymanage-canedit']) ? '显示工资信息' : '编辑、显示工资明细'"
                      @change="handleLoadData"></el-checkbox>
                  </div>
                  <div class="mr-20" v-if="[1, 2, 3].includes(accountingStandard)">
                    <el-checkbox v-model="searchInfo.salaryMultiple" label="多次发放工资" @change="changeMultiStatus"></el-checkbox>
                  </div>
                  <a class="button mr-10" @click="CreateSalaryDataFromFrontMonth" v-permission="['salarymanage-canedit']">生成工资表</a>
                  <Dropdown btnTxt="导入" class="mr-10" :downlistWidth="120" v-permission="['salarymanage-canimport']">
                    <li @click="ImportTaxPage">个税申报表导入</li>
                    <li @click="ImportPage">模板导入</li>
                    <li v-if="searchInfo.salaryMultiple" @click="ImportPageActual">实发工资导入</li>
                  </Dropdown>
                  <a class="button mr-10" @click="sendWage" v-show="!isWxwork && !isHideBarcode" v-permission="['salarymanage-cansend']"
                    >发送工资条</a
                  >
                  <img class="declare-img" id="imgTaxDeclare" :src="taxDeclareImg" v-show="imgTaxDeclare" />
                  <a
                    :class="['button', 'mr-10', { declaring: btnTaxDeclareWait }]"
                    @click="ApplyTaxDeclare"
                    v-show="!isErp && btnTaxDeclare"
                    v-permission="['taxdeclaration-canreport']"
                    @mouseover="taxDeclareMouseOver"
                    @mouseout="taxDeclareMouseOut"
                    >{{ btnTaxDeclareTxt }}</a
                  >
                  <Dropdown btnTxt="打印" class="mr-10" :downlistWidth="84" v-permission="['salarymanage-canprint']">
                    <li @click="handlePrint(0, printPeriod)">直接打印</li>
                    <li @click="handlePrint(2)">打印设置</li>
                  </Dropdown>
                  <a class="button mr-10" @click="handleCheckAttachFile">{{
                    "附件" + (attachFileCount > 0 ? `(${attachFileCount})` : "")
                  }}</a>
                  <div>
                    <Dropdown
                      :btnTxt="'更多操作'"
                      :class="'downlist'"
                      :downlistWidth="isErp ? 98 : 86"
                      v-permission="[
                        'salarymanage-candelete',
                        'salarymanage-canexport',
                        'salarymanage-cansend',
                        'taxdeclaration-canreport',
                      ]">
                      <li @click="deleteHandle" v-permission="['salarymanage-candelete']">删除</li>
                      <li @click="exportHandle" v-permission="['salarymanage-canexport']">导出</li>
                      <li @click="wageRecordHandle" v-show="!isWxwork && !isHideBarcode" v-permission="['salarymanage-cansend']">
                        工资条记录
                      </li>
                      <li
                        @click="[2, 1, 10].includes(taxDeclareInfo.declareStatus) ? declareRecordHandle(1) : declareRecordHandle(0)"
                        v-show="!isErp && btnQueryTaxDeclare"
                        v-permission="['taxdeclaration-canreport']">
                        申报记录
                      </li>
                    </Dropdown>
                  </div>
                  <RefreshButton></RefreshButton>
                </div>
              </div>
              <div class="main-center" ref="tableContentRef" :class="isErp ? 'erp-container' : ''" :style="isErp ? 'overflow-y:auto;' : ''">
                <Table
                  :data="tableData"
                  :columns="columns"
                  :loading="loading"
                  :pageIsShow="true"
                  :page-sizes="paginationData.pageSizes"
                  :page-size="paginationData.pageSize"
                  :total="paginationData.total"
                  :current-page="paginationData.currentPage"
                  :row-class-name="setCancelRowStyle"
                  :selectable="setSelectable"
                  :scrollbarShow="true"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  @selection-change="handleSelectionChange"
                  @refresh="handleRerefresh"
                  @row-click="handleRowClick"
                  @row-dblclick="handleRowDblclick"
                  @scroll="handleScroll"
                  :tableName="setModule">
                  <template #salary_sum>
                    <el-table-column label="工资总额" header-align="center">
                      <el-table-column
                        show-overflow-tooltip
                        v-for="(item, index) in customColumns.salary[0]"
                        :key="item.prop"
                        :label="item.label"
                        :align="item.align"
                        :header-align="item.headerAlign"
                        :prop="item.prop"
                        :width="getColumnWidth(setModule, item.prop)">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{ formatSalaryMoneyWithZero(scope.row[item.prop]) }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row[item.prop]"
                              @focus="handleFocus(scope.row, item.prop)"
                              @blur="handleBlur(scope.row, item.prop)"
                              @input="handleInput($event, scope.row, item.prop)"
                              class="table_input"
                              ref="firstinputRef"
                              :id="'grossPay' + index" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="工资小计"
                        prop="salary_sum"
                        :formatter="formatter"
                        align="right"
                        header-align="right"
                        show-overflow-tooltip
                        :width="getColumnWidth(setModule, 'salary_sum')">
                      </el-table-column>
                    </el-table-column>
                  </template>
                  <template #deduct_wage>
                    <el-table-column label="应扣工资" header-align="center">
                      <template v-if="customColumns.salary[1].length">
                        <el-table-column
                          v-for="(item, index) in customColumns.salary[1]"
                          :key="item.prop"
                          :label="item.label"
                          :align="item.align"
                          :header-align="item.headerAlign"
                          show-overflow-tooltip
                          :prop="item.prop"
                          :width="getColumnWidth(setModule, item.prop)">
                          <template #default="scope">
                            <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row[item.prop]) }}</span>
                            <div v-if="editShow == scope.$index" class="edit-item">
                              <el-input
                                v-model="scope.row[item.prop]"
                                @focus="handleFocus(scope.row, item.prop)"
                                @blur="handleBlur(scope.row, item.prop)"
                                @input="handleInput($event, scope.row, item.prop)"
                                ref="firstinputRef"
                                class="table_input"
                                :id="'deductWage' + index" />
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="应扣工资小计"
                          v-if="customColumns.salary[1].length"
                          prop="deduct_wage"
                          :formatter="formatter"
                          align="right"
                          header-align="right"
                          min-width="115"
                          show-overflow-tooltip
                          :width="getColumnWidth(setModule, 'deduct_wage')">
                        </el-table-column>
                      </template>
                      <template v-else></template>
                    </el-table-column>
                  </template>
                  <template #gross_pay>
                    <el-table-column
                      header-align="right"
                      align="right"
                      :min-width="isErp ? 100 : 80"
                      prop="gross_pay"
                      :width="getColumnWidth(setModule, 'gross_pay')">
                      <template #header>
                        <div class="cell-header" style="padding-right: 0">
                          <span>应发工资</span>
                          <div class="icon-equal"></div>
                          <Popover title="公式：工资总额-应扣工资" placement="top">
                            <template #trigger>
                              <div class="icon-equal" @click="visible = true" @mouseleave="visible = false"></div>
                            </template>
                          </Popover>
                        </div>
                      </template>
                      <template #default="scope">
                        {{ formatMoneyWithZero(scope.row.gross_pay) }}
                      </template>
                    </el-table-column>
                  </template>
                  <template #deduction_other>
                    <el-table-column
                      label="其他扣除"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="deduction_other"
                      :width="getColumnWidth(setModule, 'deduction_other')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.deduction_other) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.deduction_other"
                            @focus="handleFocus(scope.row, 'deduction_other')"
                            @blur="handleBlur(scope.row, 'deduction_other')"
                            @input="handleInput($event, scope.row, 'deduction_other')"
                            ref="inputRef"
                            class="table_input"
                            id="deductionOther" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #salary_year>
                    <el-table-column
                      label="累计收入额"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="salary_year"
                      :width="getColumnWidth(setModule, 'salary_year')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.salary_year) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.salary_year"
                            @focus="handleFocus(scope.row, 'salary_year')"
                            @blur="handleBlur(scope.row, 'salary_year')"
                            @input="handleInput($event, scope.row, 'salary_year')"
                            ref="inputRef"
                            class="table_input"
                            id="salaryYear" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #tax_base_amount_year>
                    <el-table-column
                      label="累计减除费用"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="tax_base_amount_year"
                      :width="getColumnWidth(setModule, 'tax_base_amount_year')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{
                          formatMoneyWithZero(scope.row.tax_base_amount_year)
                        }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.tax_base_amount_year"
                            @focus="handleFocus(scope.row, 'tax_base_amount_year')"
                            @blur="handleBlur(scope.row, 'tax_base_amount_year')"
                            @input="handleInput($event, scope.row, 'tax_base_amount_year')"
                            ref="inputRef"
                            class="table_input"
                            id="taxBaseAmountYear" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #ss_p_year>
                    <el-table-column
                      label="累计代扣额"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="ss_p_year"
                      :width="getColumnWidth(setModule, 'ss_p_year')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.ss_p_year) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.ss_p_year"
                            @focus="handleFocus(scope.row, 'ss_p_year')"
                            @blur="handleBlur(scope.row, 'ss_p_year')"
                            @input="handleInput($event, scope.row, 'ss_p_year')"
                            ref="inputRef"
                            class="table_input"
                            id="ssPYear" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #deduction>
                    <el-table-column label="累计专项附加扣除" header-align="center">
                      <el-table-column
                        label="子女教育"
                        header-align="right"
                        min-width="90"
                        align="right"
                        show-overflow-tooltip
                        prop="deduction_child_year"
                        :width="getColumnWidth(setModule, 'deduction_child_year')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{
                            formatMoneyWithZero(scope.row.deduction_child_year)
                          }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row.deduction_child_year"
                              @focus="handleFocus(scope.row, 'deduction_child_year')"
                              @blur="handleBlur(scope.row, 'deduction_child_year')"
                              @input="handleInput($event, scope.row, 'deduction_child_year')"
                              ref="inputRef"
                              class="table_input"
                              id="deductionChildYear" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="赡养老人"
                        header-align="right"
                        min-width="90"
                        align="right"
                        show-overflow-tooltip
                        prop="deduction_parent_year"
                        :width="getColumnWidth(setModule, 'deduction_parent_year')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{
                            formatMoneyWithZero(scope.row.deduction_parent_year)
                          }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row.deduction_parent_year"
                              @focus="handleFocus(scope.row, 'deduction_parent_year')"
                              @blur="handleBlur(scope.row, 'deduction_parent_year')"
                              @input="handleInput($event, scope.row, 'deduction_parent_year')"
                              ref="inputRef"
                              class="table_input"
                              id="deductionParentYear" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="住房贷款利息"
                        header-align="right"
                        min-width="90px"
                        align="right"
                        show-overflow-tooltip
                        prop="deduction_house_year"
                        :width="getColumnWidth(setModule, 'deduction_house_year')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{
                            formatMoneyWithZero(scope.row.deduction_house_year)
                          }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row.deduction_house_year"
                              @focus="handleFocus(scope.row, 'deduction_house_year')"
                              @blur="handleBlur(scope.row, 'deduction_house_year')"
                              @input="handleInput($event, scope.row, 'deduction_house_year')"
                              ref="inputRef"
                              class="table_input"
                              id="deductionHouseYear" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="住房租金"
                        header-align="right"
                        min-width="90"
                        align="right"
                        show-overflow-tooltip
                        prop="deduction_rent_year"
                        :width="getColumnWidth(setModule, 'deduction_rent_year')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{
                            formatMoneyWithZero(scope.row.deduction_rent_year)
                          }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row.deduction_rent_year"
                              @focus="handleFocus(scope.row, 'deduction_rent_year')"
                              @blur="handleBlur(scope.row, 'deduction_rent_year')"
                              @input="handleInput($event, scope.row, 'deduction_rent_year')"
                              ref="inputRef"
                              class="table_input"
                              id="deductionRentYear" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="继续教育"
                        header-align="right"
                        min-width="90"
                        align="right"
                        show-overflow-tooltip
                        prop="deduction_education_year"
                        :width="getColumnWidth(setModule, 'deduction_education_year')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{
                            formatMoneyWithZero(scope.row.deduction_education_year)
                          }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row.deduction_education_year"
                              @focus="handleFocus(scope.row, 'deduction_education_year')"
                              @blur="handleBlur(scope.row, 'deduction_education_year')"
                              @input="handleInput($event, scope.row, 'deduction_education_year')"
                              ref="inputRef"
                              class="table_input"
                              id="deductionEducationYear" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="3岁以下婴幼儿照护"
                        header-align="right"
                        min-width="140"
                        align="right"
                        show-overflow-tooltip
                        prop="deduction_baby_year"
                        :width="getColumnWidth(setModule, 'deduction_baby_year')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{
                            formatMoneyWithZero(scope.row.deduction_baby_year)
                          }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row.deduction_baby_year"
                              @focus="handleFocus(scope.row, 'deduction_baby_year')"
                              @blur="handleBlur(scope.row, 'deduction_baby_year')"
                              @input="handleInput($event, scope.row, 'deduction_baby_year')"
                              ref="inputRef"
                              class="table_input"
                              id="deductionBabyYear" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="累计专项附加扣除小计"
                        prop="tax_deduction_special_year"
                        :formatter="formatter"
                        align="right"
                        header-align="right"
                        min-width="160"
                        show-overflow-tooltip
                        :width="getColumnWidth(setModule, 'tax_deduction_special_year')">
                      </el-table-column>
                    </el-table-column>
                  </template>
                  <template #deduction_other_year>
                    <el-table-column
                      label="累计其他扣除"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="deduction_other_year"
                      :width="getColumnWidth(setModule, 'deduction_other_year')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{
                          formatMoneyWithZero(scope.row.deduction_other_year)
                        }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.deduction_other_year"
                            @focus="handleFocus(scope.row, 'deduction_other_year')"
                            @blur="handleBlur(scope.row, 'deduction_other_year')"
                            @input="handleInput($event, scope.row, 'deduction_other_year')"
                            ref="inputRef"
                            class="table_input"
                            id="deductionOtherYear" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #tax_year>
                    <el-table-column
                      label="累计应缴个税"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="tax_year"
                      :width="getColumnWidth(setModule, 'tax_year')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.tax_year) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.tax_year"
                            @focus="handleFocus(scope.row, 'tax_year')"
                            @blur="handleBlur(scope.row, 'tax_year')"
                            @input="handleInput($event, scope.row, 'tax_year')"
                            ref="inputRef"
                            class="table_input"
                            id="taxYear" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #tax_payed_year>
                    <el-table-column
                      label="已缴个税"
                      header-align="right"
                      min-width="120"
                      align="right"
                      show-overflow-tooltip
                      prop="tax_payed_year"
                      :width="getColumnWidth(setModule, 'tax_payed_year')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.tax_payed_year) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.tax_payed_year"
                            @focus="handleFocus(scope.row, 'tax_payed_year')"
                            @blur="handleBlur(scope.row, 'tax_payed_year')"
                            @input="handleInput($event, scope.row, 'tax_payed_year')"
                            ref="inputRef"
                            class="table_input"
                            id="taxPayedYear" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #tax>
                    <el-table-column
                      header-align="right"
                      align="right"
                      :min-width="isErp ? 120 : 100"
                      prop="tax"
                      :width="getColumnWidth(setModule, 'tax')">
                      <template #header>
                        <div class="cell-header" style="padding-right: 0">
                          <span>本月应缴个税</span>
                          <div class="icon-equal"></div>
                          <Popover title="公式：累计应缴个税-已缴个税" placement="top">
                            <template #trigger>
                              <div class="icon-equal" @click="visible = true" @mouseleave="visible = false"></div>
                            </template>
                          </Popover>
                        </div>
                      </template>
                      <template #default="scope">
                        {{ !searchInfo.showInfo ? formatMoneyWithZero(scope.row.tax) : formatMoneyWithZero(scope.row.tax_show) }}
                      </template>
                    </el-table-column>
                  </template>
                  <template #after_tax>
                    <el-table-column
                      label="实发工资调整"
                      header-align="center"
                      min-width="120"
                      prop="after_tax"
                      :width="getColumnWidth(setModule, 'after_tax')">
                      <template v-if="customColumns.salary[2].length">
                        <el-table-column
                          v-for="(item, index) in customColumns.salary[2]"
                          :key="item.prop"
                          :label="item.label"
                          :align="item.align"
                          :header-align="item.headerAlign"
                          show-overflow-tooltip>
                          <template #default="scope">
                            <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row[item.prop]) }}</span>
                            <div v-if="editShow == scope.$index" class="edit-item">
                              <el-input
                                v-model="scope.row[item.prop]"
                                @focus="handleFocus(scope.row, item.prop)"
                                @blur="handleBlur(scope.row, item.prop)"
                                @input="handleInput($event, scope.row, item.prop)"
                                ref="firstinputRef"
                                class="table_input"
                                :id="'afterTax' + index" />
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="实发工资调整小计"
                          v-if="customColumns.salary[2].length"
                          prop="after_tax"
                          :formatter="formatter"
                          align="right"
                          header-align="right"
                          width="130"
                          show-overflow-tooltip>
                        </el-table-column>
                      </template>
                      <template v-else></template>
                    </el-table-column>
                  </template>
                  <template #net_salary>
                    <el-table-column
                      header-align="right"
                      align="right"
                      :min-width="isErp ? 120 : 100"
                      prop="net_salary"
                      :width="getColumnWidth(setModule, 'net_salary')">
                      <template #header>
                        <div class="cell-header" style="padding-right: 0">
                          <span>{{ resultSalaryMultiple ? "实发工资合计" : "实发工资" }}</span>
                          <div class="icon-equal"></div>
                          <Popover title="公式：应发工资-代扣个人款项-本月应缴个税+实发工资调整" placement="top">
                            <template #trigger>
                              <div class="icon-equal" @click="visible = true" @mouseleave="visible = false"></div>
                            </template>
                          </Popover>
                        </div>
                      </template>
                      <template #default="scope">
                        {{ formatMoneyWithZero(scope.row.net_salary) }}
                      </template>
                    </el-table-column>
                  </template>
                  <template #net_salary_first>
                    <el-table-column
                      label="实发工资(第一次)"
                      header-align="right"
                      :min-width="isErp ? 130 : 112"
                      align="right"
                      show-overflow-tooltip
                      prop="net_salary_first"
                      :width="getColumnWidth(setModule, 'net_salary_first')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.net_salary_first) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.net_salary_first"
                            @focus="handleFocus(scope.row, 'net_salary_first')"
                            @blur="handleBlur(scope.row, 'net_salary_first')"
                            @input="handleInput($event, scope.row, 'net_salary_first')"
                            ref="inputRef"
                            class="table_input"
                            id="netSalaryFirst" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #net_salary_second>
                    <el-table-column
                      label="实发工资(第二次)"
                      header-align="right"
                      :min-width="isErp ? 130 : 112"
                      align="right"
                      show-overflow-tooltip
                      prop="net_salary_second"
                      :width="getColumnWidth(setModule, 'net_salary_second')">
                      <template #default="scope">
                        <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row.net_salary_second) }}</span>
                        <div v-if="editShow == scope.$index" class="edit-item">
                          <el-input
                            v-model="scope.row.net_salary_second"
                            @focus="handleFocus(scope.row, 'net_salary_second')"
                            @blur="handleBlur(scope.row, 'net_salary_second')"
                            @input="handleInput($event, scope.row, 'net_salary_second')"
                            ref="inputRef"
                            class="table_input"
                            id="netSalarySecond" />
                        </div>
                      </template>
                    </el-table-column>
                  </template>
                  <template #insurance_p>
                    <el-table-column label="代扣个人款项" header-align="center">
                      <el-table-column
                        v-for="(item, index) in customColumns.insurance[0]"
                        :prop="item.prop"
                        :key="item.prop"
                        :label="item.label"
                        :align="item.align"
                        :header-align="item.headerAlign"
                        show-overflow-tooltip
                        :width="getColumnWidth(setModule, 'item.prop')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">
                            {{ formatMoneyWithZero(scope.row[item.prop]) }}
                          </span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row[item.prop]"
                              @focus="handleFocus(scope.row, item.prop)"
                              @blur="handleBlur(scope.row, item.prop)"
                              @input="handleInput($event, scope.row, item.prop)"
                              ref="firstinputRef"
                              class="table_input"
                              :id="'insuranceP' + index" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="代扣个人款项小计"
                        prop="ss_p"
                        :formatter="formatter"
                        align="right"
                        header-align="right"
                        min-width="130"
                        show-overflow-tooltip
                        :width="getColumnWidth(setModule, 'ss_p')">
                      </el-table-column>
                    </el-table-column>
                  </template>
                  <template #insurance_c>
                    <el-table-column label="公司承担款项" header-align="center">
                      <el-table-column
                        v-for="(item, index) in customColumns.insurance[1]"
                        :prop="item.prop"
                        :key="item.prop"
                        :label="item.label"
                        :align="item.align"
                        :header-align="item.headerAlign"
                        show-overflow-tooltip
                        :width="getColumnWidth(setModule, 'item.prop')">
                        <template #default="scope">
                          <span :class="editShow == scope.$index ? 'none' : ''">{{ formatMoneyWithZero(scope.row[item.prop]) }}</span>
                          <div v-if="editShow == scope.$index" class="edit-item">
                            <el-input
                              v-model="scope.row[item.prop]"
                              @focus="handleFocus(scope.row, item.prop)"
                              @blur="handleBlur(scope.row, item.prop)"
                              @input="handleInput($event, scope.row, item.prop)"
                              ref="firstinputRef"
                              class="table_input"
                              :id="'insuranceC' + index" />
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="公司承担款项小计"
                        prop="ss_c"
                        :formatter="formatter"
                        align="right"
                        header-align="right"
                        min-width="130"
                        show-overflow-tooltip
                        :width="getColumnWidth(setModule, 'ss_c')">
                      </el-table-column>
                    </el-table-column>
                  </template>
                  <template #total_cost>
                    <el-table-column header-align="right" align="right" :min-width="isErp ? 100 : 80" prop="total_cost" :resizable="false">
                      <template #header>
                        <div class="cell-header" style="padding-right: 0">
                          <span>员工成本</span>
                          <div class="icon-equal"></div>
                          <Popover title="公式：应发工资+公司承担款项" placement="top">
                            <template #trigger>
                              <div class="icon-equal" @click="visible = true" @mouseleave="visible = false"></div>
                            </template>
                          </Popover>
                        </div>
                      </template>
                      <template #default="scope">
                        {{ formatMoneyWithZero(scope.row.total_cost) }}
                      </template>
                    </el-table-column>
                  </template>
                </Table>
              </div>
            </el-tab-pane>
            <el-tab-pane
              :label="secondTitle"
              name="second"
              v-if="checkPermission(['salarymanage-cancreatevoucher', 'salarymanage-setting-canview'])">
              <ContentSlider :slots="partSlots" :currentSlot="partCurrentSlot">
                <template #partMain>
                  <div class="part-content">
                    <div class="main-top main-tool-bar space-between" :class="{ 'split-line': !isErp }">
                      <div class="main-tool-left date-picker">
                        <el-date-picker
                          v-model="partMainMonth"
                          type="month"
                          :disabled-date="disabledDate"
                          :clearable="false"
                          :editable="false"
                          :teleported="false"
                          :format="'YYYY年MM月'"
                          value-format="YYYYMM"
                          style="width: 130px"
                          class="date-picker-class"
                          @change="changeMonth" />
                        <div id="warntext" class="warn-content ml-20" v-if="warnMessage !== ''">
                          <span class="warn-icon"></span
                          ><a class="link yellow" style="margin-left: 4px" id="warningnote">{{ warnMessage }}</a>
                        </div>
                      </div>
                      <div class="main-tool-right">
                        <div
                          class="voucher-settings"
                          @click="jumptosetting"
                          style="display: flex; align-items: center"
                          v-if="checkPermission(['salarymanage-setting-canview'])">
                          <img class="settings-icon" src="@/assets/Voucher/setting.png" alt="" />
                          <a class="link" style="margin-left: 4px">工资凭证设置</a>
                        </div>
                      </div>
                    </div>
                    <div
                      class="main-center"
                      :class="isErp ? 'erp-container' : ''"
                      v-loading="partMainLoading"
                      element-loading-text="正在加载数据...">
                      <div id="itemBoxsContext" class="item-boxs-context"></div>
                    </div>
                  </div>
                </template>
                <template #partVoucher>
                  <div class="slot-content part-voucher-container">
                    <div class="voucher-content">
                      <Voucher
                        v-model:query-params="voucherQueryParams"
                        @load-success="toVoucherPage"
                        @voucher-changed="voucherChanged"
                        ref="voucher"
                        :edited="isEditting"
                        :showCancel="true"
                        cancelTxt="取消"
                        @delete-voucher="deleteVoucher"
                        @save="savePartVoucher()"
                        @back="divCancel"
                        :isBottomBtn="true"
                        :module-permission="'salarymanage-cancreatevoucher'"></Voucher>
                    </div>
                  </div>
                </template>
                <template #checkSalary>
                  <CheckSalaryDialog
                    :pid="show_pid"
                    :vid="show_vid"
                    :checkSalaryVtId="checkSalaryVtId"
                    :checkSalaryVtName="checkSalaryVtName"
                    :checkSalaryVoucher="checkSalaryVoucher"
                    :ShowVoucherCheckout="ShowVoucherCheckout"
                    @updateVoucherFromList="CreateVoucher"
                    @backTo="() => (partCurrentSlot = 'partMain')"></CheckSalaryDialog>
                </template>
              </ContentSlider>
            </el-tab-pane>
          </el-tabs>
        </div>
      </template>
      <template #declare>
        <div class="main-content">
          <DeclareSlot
            ref="declareSlotRef"
            :declareStatus="taxDeclareInfo"
            :declareData="declareData"
            :declareTotal="declareTotal"
            :showTaxDeclarationWait="showTaxDeclarationWait"
            :declareRecordHandle="declareRecordHandle"
            :queryTaxDeclareRecord="QueryTaxDeclareRecord"
            @CloseTaxDeclareRecord="CloseTaxDeclareRecord"
            @enterTaxDiff="enterTaxDiff"></DeclareSlot>
        </div>
      </template>
      <template #taxDiff>
        <div class="main-content">
          <TaxDiffSlot
            ref="taxDiffSlotRef"
            :periodList="taxDiffList"
            :period="taxDiffPeriod"
            @closeTaxDiff="() => (currentSlot = 'declare')"></TaxDiffSlot>
        </div>
      </template>
      <template #voucherSetting>
        <div class="slot-content align-center" style="height: 100%">
          <div class="slot-title">工资凭证设置</div>
          <VoucherSetting
            :voucherSettingList="voucherSettingList"
            @modifyRules="modifyRules"
            @newVoucherTemplate="newVoucherTemplate"
            @deleteVoucherTemplate="deleteVoucherTemplate"
            @divCancel="handleSettingClose"></VoucherSetting>
        </div>
      </template>
      <template #customWage>
        <div class="slot-content align-center" :style="isErp ? 'overflow-y:auto; height:auto;' : ''">
          <div class="slot-title">自定义工资项目</div>
          <CustomWageSlot :data="customWageData" :month="searchInfo.month" @cancleCustomWage="cancleCustomWage"></CustomWageSlot>
        </div>
      </template>
    </ContentSlider>
  </div>
  <el-dialog title="提示" v-model="divEmpError" width="381" class="dialogDrag">
    <div class="emperror-container" id="divEmpErrorCenter" v-dialogDrag>
      <div class="emperror-message" id="empErrorMsg">{{ empErrorMsg }}</div>
      <div class="emperror-name" id="empErrorName">
        <template v-for="(item, index) in checkEmployeeName" :key="index">
          <div class="name-content">
            <img src="@/assets/Salary/emp-icon.png" />
            <span>{{ item }}</span>
          </div>
        </template>
      </div>
      <div class="dialog-buttons">
        <a class="solid-button" id="btnSubmitEmpError" @click="toEmployeeInfo">确定</a>
      </div>
    </div>
  </el-dialog>
  <ImportSingleFileDialog
    :importTitle="'个税申报表导入'"
    v-model:import-show="individualTaxShow"
    :importUrl="`/api/Salary/Import?mid=${searchInfo.month}`"
    :uploadCheck="uploadCheck"
    :uploadSuccess="uploadSuccess">
    <template #top-tips>
      <div style="margin-left: 40px; margin-top: 20px" v-show="!isErp && !isHideBarcode">
        <a @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/commonPro?subMenuId=*********&answerId=648')" class="link"
          >点击查看帮助
        </a>
      </div>
    </template>
    <template #download>
      <div class="mt-10">1.请选择下面任意一种方式导入工资数据</div>
      <div class="mt-10 ml-10">(1) 从自然人税收管理系统扣缴客户端导出个税申报表；</div>
      <div class="mt-10 ml-10">(2) 点击下载工资表模板，按照模板格式进行数据整理再导入。</div>
      <div class="mt-10 ml-10"><a class="link" @click="downloadfile">下载模板</a></div>
    </template>
    <template #import-content>
      <span>2. 选择文件导入</span>
    </template>
  </ImportSingleFileDialog>
  <ImportSingleFileDialog
    :importTitle="'导入工资信息'"
    v-model:import-show="importWageShow"
    :importUrl="`/api/Salary/Import?mid=${searchInfo.month}`"
    :uploadCheck="uploadCheck"
    :uploadSuccess="uploadSuccess"
    :fileMarginBotton="'0'"
    class="other-inport-dialog">
    <template #download>
      <div>第一步：请点击下面的链接来下载模板，并填写工资信息</div>
      <div class="mt-10"><a class="link" @click="downloadfile">下载模板</a></div>
      <div class="mt-20">第二步：导入Excel模板文件</div>
    </template>
    <template #import-content>
      <div></div>
    </template>
  </ImportSingleFileDialog>
  <el-dialog title="工资数据" class="custom-confirm dialogDrag" v-model="salaryDataShow" width="440" center>
    <div class="import-method-container" v-dialogDrag>
      <div class="import-method-content">
        <span class="mr-20">选择：</span>
        <el-radio-group v-model="salaryDirection" class="ml-4">
          <el-radio label="1" size="large">空白工资表</el-radio>
          <el-radio label="2" size="large">导入完整工资表</el-radio>
        </el-radio-group>
      </div>
      <div class="buttons" :style="isErp ? '' : 'border-top:1px solid var(--border-color)'">
        <a class="button solid-button mr-20" @click="ImportSelect">确定</a>
        <a class="button" @click="CancelImportSelect">取消</a>
      </div>
    </div>
  </el-dialog>
  <!-- 个税申报 -->
  <el-dialog title="个税申报" v-model="taxDeclaration" destroy-on-close width="440" center class="dialogDrag">
    <div class="divImport" v-dialogDrag>
      <div class="template-import-download">
        <div class="declare-info-card">
          <div class="info-title">您即将进行{{ taxDeclarationMonth }}的个税申报：</div>
          <div class="info-subtitle mt-10">工资表数据：{{ salaryTitle }}</div>
        </div>
        <div class="mt-10 radioFlex">
          <div>选择类型：</div>
          <el-radio-group v-model="taxDeclarationType" class="ml-4">
            <el-radio label="1" size="large">申报及缴税</el-radio>
            <el-radio label="0" size="large">先申报，后缴税</el-radio>
          </el-radio-group>
        </div>
        <div class="mb-10">
          <el-checkbox v-model="updateFromDeclare" label="自动更新本月应缴个税和累计专项附加扣除" size="large" />
        </div>
        <div class="small">
          <span>注意：此操作不可回退，请谨慎操作。</span>
        </div>
      </div>

      <div class="buttons">
        <a class="button solid-button mr-20" @click="taxDeclarationHandle">确定</a>
        <a class="button" @click="cancelTaxDeclaration">取消</a>
      </div>
    </div>
  </el-dialog>
  <!-- 个税申报 -->
  <!-- 申报等待中 -->
  <el-dialog title="个税申报" v-model="taxDeclarationWait" destroy-on-close width="800" center class="dialogDrag">
    <div class="divImport" v-dialogDrag>
      <div class="waiting-content">
        <div class="waiting-left">
          <div class="left-title">
            <img class="left-title-loading-img" src="@/assets/Salary/tax-declare-waiting.png" />
            <span class="waitingText">正在连接税局申报中，可能需要几分钟...</span>
          </div>
          <div class="left-subtitle">您可以扫码关注柠檬云财务公众号，无需停留在当前页面，申报完成后我们会通过公众号通知您~</div>
        </div>
        <div class="waiting-line"></div>
        <div class="waiting-right">
          <img class="qrcode-img" src="@/assets/Salary/lemon-official-accounts.png" />
          <div class="qrcode-desc">微信扫码关注</div>
          <div class="qrcode-desc">获取申报进度</div>
        </div>
      </div>
      <div class="buttons">
        <a class="button solid-button" @click="closeTaxDeclareWait">知道了</a>
      </div>
    </div>
  </el-dialog>
  <!-- 个税申报校验 -->
  <el-dialog title="个税申报校验" v-model="divInfoCheck" width="460" class="dialogDrag">
    <div class="taxinfo-check" title="个税申报校验" v-dialogDrag>
      <div class="taxinfo-check-container">
        <div
          v-for="(item, index) in taxTypeList"
          :key="index"
          :class="['check-item', { 'check-item-active': checkInfoList[index].checkBg }]">
          <img src="@/assets/Salary/check-img.png" :class="checkInfoList[index].checkImgClass" />
          <img :src="getIcon(index)" class="icon-item" />
          {{ item }}
          <span :class="checkInfoList[index].checkTxtClass">
            {{ checkInfoList[index].checkTxt }}
          </span>
          <img src="@/assets/Salary/check-bg.png" :class="checkInfoList[index].checkBgClass" />
        </div>
      </div>
      <div class="dialog-buttons" v-show="divSubmitInfoCheck">
        <a class="button solid-button" @click="SubmitInfoCheck">确定</a>
      </div>
    </div>
  </el-dialog>
  <!-- 申报等待中 -->
  <!-- 已结账无法修改 -->
  <el-dialog title="提示" v-model="isSalaryCheckout" destroy-on-close width="440" center class="custom-confirm dialogDrag">
    <div class="checkoutDia" v-dialogDrag>当前月份工资已结账，无法修改！</div>
    <div class="buttons" :style="isErp ? '' : 'border-top:1px solid var(--border-color)'">
      <a class="button solid-button" @click="isSalaryCheckout = false">确定</a>
    </div>
  </el-dialog>
  <!-- 已结账无法修改 -->
  <GenerateVoucherDialog
    ref="generateVoucherDialogRef"
    v-model:voucher-dialog-is-show="voucherDialogIsShow"
    :columns="GenerateVoucherRulesColums"
    :autoAddNextLine="false"
    :pre-check="preCheck"
    :tempalteValueTypeList="tempalteValueTypeList"
    :defaultLine="voucherRulesAddDefault"
    :not-show="false"
    :customAdd="true"
    :customSubtract="true"
    :isMaxHeight="false"
    :customNotify="true"
    :width="isErp ? '740px' : '670px'"
    @voucherSave="handleVoucherSave"
    @handleDelete="handleDelete"
    :setModule="setModuleDialog">
    <form>
      <span class="txt" style="line-height: 32px"><span>模板名称：</span> </span>
      <el-input
        ref="templateNameRef"
        class="templateNameIpt"
        autofocus
        type="text"
        name="templateName"
        autocomplete="on"
        :readonly="templateName === '计提工资' || templateName === '发放工资'"
        v-model="templateName"
        @input="formatTempalteName" />
      <el-checkbox style="top: 2px" v-model="combine" label="生成凭证时合并相同的科目"></el-checkbox>
    </form>
  </GenerateVoucherDialog>
  <UploadFileDialog :readonly="checkboxInfo" ref="uploadFileDialogRef" @save="saveAttachFile" />
  <!--实发工资导入  -->
  <ImportSingleFileDialog
    :importTitle="'实发工资导入'"
    v-model:import-show="importActualShow"
    :importUrl="`/api/Salary/ImportNetSalary?mid=${searchInfo.month}`"
    :uploadCheck="uploadCheck"
    :uploadSuccess="uploadSuccess"
    :fileMarginBotton="'0'"
    class="other-inport-dialog">
    <template #download>
      <div>第一步：请根据工资表导出实发工资数据，编辑确认后直接导入</div>
      <div class="mt-10"><a class="link" @click="downloadSalaryActualTable">下载工资表</a></div>
      <div class="mt-20">第二步：选择文件导入</div>
    </template>
    <template #import-content>
      <div></div>
    </template>
  </ImportSingleFileDialog>
  <SalaryPrint
    v-model:printDialogShow="printDialogVisible"
    :title="'工资表打印'"
    :printData="printInfo"
    :dirShow="false"
    :otherOptions="otherOptions"
    @currentPrint="handlePrint(3, printPeriod)">
    <template #custom>
      <div class="print-item mt-20">
        <div class="print-item-label">工资月份：</div>
        <div class="print-item-field date-picker">
          <el-date-picker
            v-model="printPeriod.startMid"
            type="month"
            :disabled-date="disabledPrintStartMonth"
            :clearable="false"
            :editable="false"
            :teleported="false"
            :format="'YYYY年MM月'"
            value-format="YYYYMM"
            style="width: 123px"
            class="date-picker-class" />
          <span class="ml-10 mr-10">至</span>
          <el-date-picker
            v-model="printPeriod.endMid"
            type="month"
            :disabled-date="disabledPrntEndMonth"
            :clearable="false"
            :editable="false"
            :teleported="false"
            :format="'YYYY年MM月'"
            value-format="YYYYMM"
            style="width: 123px"
            class="date-picker-class" />
        </div>
      </div>
    </template>
  </SalaryPrint>
</template>

<script lang="ts">
export default {
  name: "SalaryManage",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import Voucher from "@/components/Voucher/index.vue";
import GenerateVoucherDialog from "@/components/Dialog/GenerateVoucher/index.vue";
import DeclareSlot from "./components/DeclareSlot.vue";
import TaxDiffSlot from "./components/TaxDiffSlot.vue";
import CustomWageSlot from "./components/CustomWageSlot.vue";
import VoucherSetting from "./components/voucherSetting.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import SalaryPrint from "@/components/PrintDialog/index.vue";
import CheckSalaryDialog from "./components/checkSalaryDialog.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type {
  ISelectItem,
  IItemData,
  IItemDetailData,
  IDetailColumn,
  ISalaryData,
  ICheckSalaryVoucherItem,
  ICreateVoucher,
  IIofo,
  ITaxDeclarationWaitParams,
  IPeriodItem,
} from "./types";
import type { IVoucherTemplateModel } from "@/components/dialog/GenerateVoucher/types";
import { usePagination } from "@/hooks/usePagination";
import { request, type IResponseModel } from "@/util/service";
import { setColumns, formatSalaryMoneyWithZero } from "./utils";
import { useAccountSetStoreHook, useAccountSetStore } from "@/store/modules/accountSet";
import {
    getUrlSearchParams,
    globalWindowOpen,
    globalWindowOpenPage,
    closeCurrentTab,
    closeTab,
    globalExport,
    downloadFile,
    setTopLocationhref,
    tryClearCustomUrlParams,
} from "@/util/url";
import { ElNotify } from "@/util/notify";
import { ElAlert, ElConfirm } from "@/util/confirm";
import { checkPermission } from "@/util/permission";
import { ref, reactive, nextTick, watch, onMounted, onUnmounted, onActivated, toRef, computed, watchEffect } from "vue";
import { onBeforeRouteLeave, useRoute } from "vue-router";
import router from "@/router";
import {
  DataVoucherQueryParams,
  VoucherEntryModel,
  VoucherSaveModel,
  VoucherSaveParams,
  EditVoucherQueryParams,
} from "@/components/Voucher/types";
import { formatMoneyWithZero } from "@/util/format";
import { isInWxWork } from "@/util/wxwork";
import { erpCloseTab, erpCreateTab } from "@/util/erpUtils";
import { useLoading } from "@/hooks/useLoading";
import usePrint from "@/hooks/usePrint";
import { getGlobalLodash } from "@/util/lodash";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { editConfirm } from "@/util/editConfirm";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import Tooltip from "@/components/Tooltip/index.vue";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import Popover from "@/components/Popover/index.vue";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { dayjs } from "element-plus";
import { commonFilterMethod } from "@/components/Select/utils";
import { getFormatterDate } from "@/util/date";

const setModule = "SalaryManage";
const setModuleDialog = "SalaryGenerateVoucherDialog";
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const _ = getGlobalLodash();
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");

const accountsetStore = useAccountSetStore();
const trialStatusStore = useTrialStatusStore();
const secondTitle = ref("计提工资、发放工资");
//小企业/企业/民非准则生效多次发放工资
const accountingStandard = useAccountSetStore().accountSet?.accountingStandard as number;

// 新增或修改凭证弹窗
const voucherDialogIsShow = ref(false);
// 新增或修改凭证
const createOrEditTemp = ref<string>("");
// 新增或修改凭证参数
const voucherTempParams = ref<object>({});
// 新增或修改凭证参数请求方法
const voucherTempMethod = ref<string>("");
// 凭证模板名称输入框
const templateNameRef = ref();

const isErp = ref(window.isErp);
const slots = ["main", "declare", "customWage", "voucherSetting", "taxDiff"];
const currentSlot = ref("main");
const partSlots = ["partMain", "partVoucher", "checkSalary"];
const partCurrentSlot = ref("partMain");
const activeName = ref("first");
const warnShow = ref(false);
const warnMessage = ref("");
const templateName = ref<string>("");
const voucherRulesAddDefault = ref();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const searchInfo = reactive({
  month: "202209",
  department: "",
  name: "",
  showInfo: false,
  salaryMultiple: false,
});
const salaryTitle = ref("");
const individualTaxShow = ref(false);
const divEmpError = ref(false);
const divInfoCheck = ref(false);
const divSubmitInfoCheck = ref(false);
const importWageShow = ref(false);
const salaryDataShow = ref(false);
const salaryDirection = ref("2");
const loading = ref(false);
const columns = ref<Array<IColumnProps>>();
const voucherQueryParams = ref();
const voucher = ref();
let generateVoucherDialogRef = ref();
const editRowId = ref<number>(0);

const GenerateVoucherRulesColums = ref<Array<IColumnProps>>([{ slot: "asubId" }, { slot: "direction" }, { slot: "salaryValueType" }]);
const voucherTableData = ref<Array<any>>([]);
const checkSalaryVoucher = ref<ICheckSalaryVoucherItem[]>([]);
let currentvtid = 0;

// 个税申报检测类型
const taxTypeList = [
  "纳税人名称",
  "纳税人识别号",
  "报税地区",
  "办税人员姓名",
  "办税人员手机号",
  "员工姓名",
  "员工身份证号",
  "工资数据",
  "个税系统密码",
];
// 获取图标地址
const getIcon = (index: number) => {
  return new URL(`/src/assets/Salary/check-icon${index + 1}.png`, import.meta.url).href;
};

watch(partCurrentSlot, (val) => {
  if (val === "partMain" && !window.isErp) {
    secondTitle.value = "计提工资、发放工资";
  }
});

function formatTempalteName(value: string) {
  value = value.slice(0, 9);
  templateName.value = value;
}

function formatDate(input: string) {
  const inputyear = input.substring(0, 4);
  const inputmonth = input.substring(4, 6);
  // 获取指定月份的最后一天
  const inputlastDay = new Date(~~inputyear, ~~inputmonth, 0).getDate();
  return `${inputyear}-${inputmonth}-${inputlastDay}`;
}

const checkSalaryVtId = ref("");
const checkSalaryVtName = ref("");
const createSalaryVtId = ref("");
const isWxwork = ref(isInWxWork());

// 个税申报校验确定
function SubmitInfoCheck() {
  divInfoCheck.value = false;
  setTopLocationhref(window.jHost + "/app/default.aspx#账套@" + window.location.origin + "/Settings/AccountSets?isFromTax=true");
}

const voucherStatus = ref("");
//flag未定义是为生成，true是为修改
async function CreateVoucher(vt_id: string, title: string, flag?: boolean) {
  createSalaryVtId.value = vt_id;
  voucherStatus.value = flag ? "update" : "create";
  const isc = await IsHasCheckOutForVoucher(partMainMonth.value);
  let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
  if (isTrilExpired) {
    return;
  }
  request({
    url: `/api/SalaryVoucher/GenerateVoucher?vtId=${vt_id}&mid=${partMainMonth.value}`,
    method: "post",
  }).then((res: any) => {
    if (res.state !== 1000) {
      ElNotify({
        message: res.msg,
        type: "warning",
      });
      return;
    }
    if (res.data.haveSalary) {
      currentvtid = Number(vt_id);
      const voucherLines = res.data.data.rows.map((item: ICreateVoucher) => {
        const voucherModel = new VoucherEntryModel();
        voucherModel.asubId = item.asub_id;
        voucherModel.assistingAccounting = item.assistingaccounting;
        voucherModel.aacode = item.assistsetting;
        voucherModel.asubCode = item.asub_code;
        voucherModel.asubName = item.asub_name;
        voucherModel.debit = item.debit;
        voucherModel.credit = item.credit;
        voucherModel.description = item.description;
        voucherModel.measureUnit = item.measureupnit + "";
        voucherModel.quantity = item.quantity;
        voucherModel.price = item.price;
        voucherModel.quantityAccounting = item.quantityaccounting;
        voucherModel.fcId = item.fcid;
        voucherModel.fcRate = item.fcrate;
        voucherModel.fcCode = item.fccode;
        voucherModel.fcAmount = item.fcamount;
        voucherModel.foreigncurrency = item.foreigncurrency;
        voucherModel.isjtsub = item.isjtsub;
        return voucherModel;
      });
      setTimeout(() => {
        secondTitle.value = title;
      }, 1000);
      voucherQueryParams.value = flag
        ? new EditVoucherQueryParams(Number(show_pid.value), Number(show_vid.value))
        : new DataVoucherQueryParams(voucherLines);
      if (isc) {
        voucherQueryParams.value.vdate = getFormatterDate();
      } else {
        voucherQueryParams.value.vdate = formatDate(partMainMonth.value);
      }
      if (title === "计提工资") {
        voucherQueryParams.value.attachments = res.data.data.attachFiles.length;
        voucherQueryParams.value.attachFileIds = res.data.data.attachFiles.map((item: any) => item.fileId).join(",");
        voucherQueryParams.value.attachFiles = res.data.data.attachFiles;
      }
      voucherQueryParams.value.vgId = Number(res.data.vgId);
      let debit = 0;
      let credit = 0;
      if (!flag) {
        for (let i = 0; i < voucherLines.length; i++) {
          credit += voucherLines[i].credit;
          debit += voucherLines[i].debit;
        }
        if (debit.toFixed(2) != credit.toFixed(2)) {
          ElNotify({
            message: "亲，凭证借贷金额不等，请检查工资凭证模板设置！",
            type: "warning",
          });
        }
      }
      voucherIsInit = false;
      edited.value = false;
    } else {
      ElConfirm("暂无本月工资数据，无法生成凭证！", true);
    }
  });
}
const toVoucherPage = () => {
  if (voucherQueryParams.value instanceof EditVoucherQueryParams) {
    edited.value = false;
  }
  partCurrentSlot.value = "partVoucher";
  voucherIsInit = true;
};
const show_pid = ref<string>("");
const show_vid = ref<string>("");
const ShowVoucherCheckout = ref(false);
function ShowVoucher(vid: string, pid: string, vtid: string, vtname: string) {
  show_pid.value = pid;
  show_vid.value = vid;
  checkSalaryVtId.value = vtid;
  checkSalaryVtName.value = vtname;
  request({
    url: `/api/SalaryVoucher/GetVoucher?vid=${vid}&pid=${pid}`,
    method: "POST",
  }).then(async (res: any) => {
    checkSalaryVoucher.value = res.data.data.rows;
    ShowVoucherCheckout.value = await IsHasCheckOutForVoucherForAdmission(pid);
    partCurrentSlot.value = "checkSalary";
  });
}

const savePartVoucher = _.debounce(SaveMyVouchers, 500);

let isSave = false;
async function SaveMyVouchers() {
  if (voucherStatus.value === "create") {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.generateVoucher });
    if (isTrilExpired) {
      return;
    }
  }
  if (isSave) return;
  isSave = true;
  const voucherModel = voucher.value?.getVoucherModel();
  const dateObj = new Date(voucherModel.vdate);
  const year = dateObj.getFullYear();
  const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
  const resultStr = year + month;
  if (await IsHasCheckOut(resultStr)) {
    ElConfirm("当前凭证期间已结账，无法修改！", true);
    isSave = false;
    return;
  } else {
    voucher.value.saveVoucher(
      new VoucherSaveParams(1054, (res: IResponseModel<VoucherSaveModel>) => {
        if (res.state === 1000) {
          if (voucherStatus.value === "create") {
            request({
              url: `/api/SalaryVoucher/BindSalaryAndVoucher?mid=${partMainMonth.value}&vtid=${currentvtid}&pid=${res.data.pid}&vid=${res.data.vid}`,
              method: "post",
            }).then((res: any) => {
              isSave = false;
            });
          } else {
            isSave = false;
          }
          secondTitle.value = "计提工资、发放工资";
          ElNotify({
            message: voucherStatus.value === "update" ? "凭证修改成功！" : "凭证生成成功！",
            type: "success",
          });
          partCurrentSlot.value = voucherStatus.value === "update" ? "checkSalary" : "partMain";
          voucherStatus.value === "update" &&
            ShowVoucher(res.data.vid + "", res.data.pid + "", checkSalaryVtId.value, checkSalaryVtName.value);
          useFullScreenStore().changeFullScreenStage(false);
        } else if (res.state === 2000) {
          isSave = false;
          if (res.subState !== -1) {
            ElNotify({ message: res.msg, type: "warning" });
          }
        } else if (res.state === 9999) {
          isSave = false;
          ElNotify({ message: "保存失败", type: "warning" });
        }
      })
    );
  }
}

function divCancel(targetSlot?: "partMain" | "checkSalary") {
  voucher.value?.removeEventListener();
  partCurrentSlot.value = targetSlot ? targetSlot : voucherStatus.value === "update" ? "checkSalary" : "partMain";
  secondTitle.value = "计提工资、发放工资";
  document.querySelector("#pane-second")!.scrollTop = 0;
  useFullScreenStore().changeFullScreenStage(false);
}
//从凭证里直接删除直接回到partMain
const deleteVoucher = () => {
  ElConfirm("确定要删除吗").then((r: boolean) => {
    if (r) {
      const { pid, vid } = voucher!.value.getVoucherModel();
      request({
        url: `/api/SalaryVoucher/DeleteVoucher?pid=${pid}&vid=${vid}`,
        method: "post",
      }).then((res: any) => {
        if (res.state === 1000) {
          if (res.data) {
            ElNotify({
              type: "success",
              message: "删除成功！",
            });
            divCancel("partMain");
            dispatchReloadAsubAmountEvent();
          } else {
            ElNotify({
              type: "error",
              message: "删除失败，请稍后重试！",
            });
          }
        } else {
          ElNotify({
            type: "error",
            message: "删除失败，请稍后重试！",
          });
        }
      });
    }
  });
};
const handleSettingClose = () => {
  currentSlot.value = "main";
  getAccruedsalary();
};

const tempalteValueTypeList = ref<any[]>([]);
function GetSalaryTemplateValueData(vtId: number) {
  request({
    url: `/api/SalaryTemplate/GetValueData?mid=${searchInfo.month}&vtId=${vtId}`,
    method: "post",
  }).then((res: any) => {
    tempalteValueTypeList.value = res.data?.reduce((prev: any[], item: any) => {
      prev.push({
        value: item.id,
        label: item.name,
      });
      return prev;
    }, []);
    voucherRulesAddDefault.value = {
      asubId: "",
      direction: 1,
      valueId: tempalteValueTypeList.value[0].value,
    };
  });
}

const deleteTemplate = (vt_id: number) => {
  useLoading().enterLoading("努力加载中，请稍候...");
  return request({
    url: `/api/SalaryTemplate?vtId=${vt_id}`,
    method: "delete",
  });
};
const deleteVoucherTemplate = (vt_id: number) => {
  deleteTemplate(vt_id).then((res) => {
    useLoading().quitLoading();
    if ((res as any).state === 1000) {
      loadTemplates();
      return ElNotify({
        type: "success",
        message: "删除成功！",
      });
    }
  });
};

function GetSalaryTemplateItem(vtId: number) {
  return request({
    url: `/api/SalaryTemplate/GetItems?vtId=${vtId}`,
    method: "post",
  });
}
const defaultVgId = computed(() => {
  return useVoucherGroupStore().defaultVgId;
});
async function modifyRules(val: any) {
  createOrEditTemp.value = "modify";
  GetSalaryTemplateValueData(val.vt_id);
  let templateItem = await GetSalaryTemplateItem(val.vt_id).then((res: any) => {
    return res.data;
  });
  let list = templateItem.map((item: any) => {
    item.asubId = String(item.asub_id);
    item.direction = item.direction === 2 ? 1 : 2;
    item.valueId = item.valueid;
    return item;
  });
  voucherTableData.value = [];
  templateName.value = val.vt_name;
  combine.value = val.combine ? true : false;
  const data = {
    vgId: val.vg_id > 0 ? val.vg_id : defaultVgId.value,
    vtId: val.vt_id,
    voucherTemplateLines: list,
  };
  generateVoucherDialogRef.value?.initEditForm(data);
  voucherDialogIsShow.value = true;
  // 自动聚焦
  if (templateName.value !== "计提工资" && templateName.value !== "发放工资") {
    nextTick(async () => {
      (await templateNameRef.value) && templateNameRef.value.focus();
    });
  }
}

async function newVoucherTemplate() {
  createOrEditTemp.value = "add";
  GetSalaryTemplateValueData(0);
  templateName.value = "";
  combine.value = false;
  const data: IVoucherTemplateModel = {
    vgId: 1010,
    vtId: 0,
    voucherTemplateLines: [
      {
        asubId: "",
        direction: 1,
        valueId: "1-1001",
      },
      {
        asubId: "",
        direction: 1,
        valueId: "1-1001",
      },
    ],
  };
  editRowId.value = 0;
  nextTick().then(() => generateVoucherDialogRef.value?.initEditForm(data, "new"));
  voucherDialogIsShow.value = true;
  // 自动聚焦
  if (templateName.value !== "计提工资" && templateName.value !== "发放工资") {
    nextTick(async () => {
      (await templateNameRef.value) && templateNameRef.value.focus();
    });
  }
}

const preCheck = () => {
    if (templateName.value.trim() === "") {
        ElNotify({
            type: "warning",
            message: "请输入模板名称！",
        });
        return false;
    }
    return true;
};

function handleDelete(length: number, index: number) {
  if (length <= 2) {
    ElNotify({
      type: "warning",
      message: "亲，凭证模板不能少于2行哦",
    });
    return false;
  }
  generateVoucherDialogRef.value?.handleDeleteRow(index);
}

let voucherSetting = true;
const combine = ref(false); //合并凭证
function handleVoucherSave(data: any, deletedRowIdList?: any) {
  if (!voucherSetting) {
    return;
  }
  voucherSetting = false;
  if (createOrEditTemp.value === "add") {
    // 新增
    voucherTempMethod.value = "post";
    voucherTempParams.value = {
      Add: data.voucherTemplateLines.map((item: any, index: number) => {
        let itemData: IItemData = {
          VET_ID: "",
          ASUB_ID: "",
          Direction: 0,
          ValueId: "",
        };
        itemData.ASUB_ID = String(item.asubId);
        itemData.Direction = item.direction === 1 ? 2 : 1;
        itemData.ValueId = item.valueId;
        itemData.VET_ID = `add_${index}_0`;
        return itemData;
      }),
      Update: [],
      Delete: [],
    };
  } else {
    // 修改
    voucherTempMethod.value = "put";
    voucherTempParams.value = {
      Add: data.voucherTemplateLines
        .filter((item: any) => !item.ve_id)
        .map((item: any) => {
          let itemData: IItemData = {
            VET_ID: "",
            ASUB_ID: "",
            Direction: 0,
            ValueId: "",
          };
          itemData.ASUB_ID = String(item.asubId);
          itemData.Direction = item.direction === 1 ? 2 : 1;
          itemData.ValueId = item.valueId;
          itemData.VET_ID = `add_${item.index}_${data.vtId}`;
          return itemData;
        }),
      Update: data.voucherTemplateLines
        .filter((item: any) => item.ve_id)
        .map((item: any) => {
          let itemData: IItemData = {
            VET_ID: "",
            ASUB_ID: "",
            Direction: 0,
            ValueId: "",
          };
          itemData.ASUB_ID = String(item.asubId);
          itemData.Direction = item.direction === 1 ? 2 : 1;
          itemData.ValueId = item.valueId;
          itemData.VET_ID = item.ve_id + "_" + data.vtId;
          return itemData;
        }),
      Delete: deletedRowIdList.length > 0 ? deletedRowIdList.map((item: any) => item.ve_id + "_" + item.vt_id) : [],
    };
  }
  request({
    url: `/api/SalaryTemplate?name=${templateName.value}&vgId=${data.vgId}&combine=${combine.value ? 1 : 0}`,
    method: voucherTempMethod.value,
    data: voucherTempParams.value,
  }).then((res: any) => {
    if (res.state === 1000 && res.data) {
      ElNotify({
        type: "success",
        message: voucherTempMethod.value === "put" ? "修改成功" : "新增成功",
      });
      voucherDialogIsShow.value = false;
      combine.value = false;
      setTimeout(() => {
        voucherSetting = true;
      });
      jumptosetting();
    }
  });
}

function setCancelRowStyle(data: { row: any; rowIndex: number }): string {
  data.row.index = data.rowIndex;
  return "";
}

function setSelectable(row: any): boolean {
  if (row.aa_name === "合计") {
    return false;
  }
  return true;
}

const confirmMonth = ref("");
function handleSearch() {
  salaryTitle.value = (allMonth.value as any).find((item: any) => {
    return item.value == searchInfo.month;
  }).label;
  paginationData.currentPage = 1;
  confirmMonth.value = searchInfo.month;
  isHasMultiSalary();
  IsSettingSalaryItem(searchInfo.month, searchDataWithTaxStatus);
  containerRef.value?.handleClose();
}

function handleClose() {
  containerRef.value?.handleClose();
}

function handleReset() {
  searchInfo.month = allMonth.value[0].value;
  confirmMonth.value = searchInfo.month;
  searchInfo.name = "";
  searchInfo.department = "0";
}

const customWageData = ref([]);
function customWageClick() {
  request({
    url: `/api/SalarySettings/List?mid=` + searchInfo.month,
  }).then((res: any) => {
    customWageData.value = res.data;
    currentSlot.value = "customWage";
  });
}

const eSnList = ref([]);
function handleSelectionChange(val: any) {
  eSnList.value = [];
  eSnList.value = val.map((item: any) => item.e_sn);
}

function ImportTaxPage() {
  individualTaxShow.value = true;
}

function ImportPage() {
  importWageShow.value = true;
}

//获取某一期需要发工资的员工数量
async function GetIsHaveData() {
  let salaryCount = 0;
  await request({
    url: `/api/Employee/GetSalaryEmployeeCountByPeriod?pid=` + searchInfo.month,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000) {
      salaryCount = res.data;
    }
  });
  return salaryCount;
}

function IsSettingSalaryItem(pid: string, callback: Function) {
  request({
    url: `/api/SalarySettings/Init?mid=` + pid,
    method: "post",
  }).then((res: any) => {
    if (callback) {
      callback();
    }
  });
}
function IsHaveVoucherTemplateAndEntry() {
  request({
    url: `/api/SalaryTemplate/Init`,
    method: "post",
  });
}
async function IsHaveSalaryForFrontMonth() {
  let flag = "";
  await request({
    url: `/api/Salary/ExistsSalaryBeforeMonth?mid=${searchInfo.month}`,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000) {
      flag = res.data;
    }
  });
  return flag;
}
async function IsHaveSalaryData() {
  let agent = null;
  await request({
    url: `/api/Salary/ExistsSalaryByMonth?mid=${searchInfo.month}`,
    method: "post",
  }).then((res: any) => {
    agent = res.data;
  });
  return agent;
}

function CreateEmptySalaryData() {
  request({
    url: `/api/Salary/CreateEmptySheet?mid=${searchInfo.month}`,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000 && res.data) {
      ElNotify({
        type: "success",
        message: "空白工资表生成成功！",
      });
      salaryDataShow.value = false;
      getTableData();
      handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
    } else {
      ElNotify({
        type: "warning",
        message: res.msg,
      });
    }
  });
}

//某个月份之前是否有工资
async function CreateSalaryDataFromFrontMonth() {
  if ((await GetIsHaveData()) > 0) {
    if (await IsHaveSalaryForFrontMonth()) {
      request({
        url: `/api/Salary/CopySalaryData?mid=${searchInfo.month}`,
        method: "post",
      }).then((res: any) => {
        if (res.data == 0 || res.data == 101) {
          ElNotify({
            type: "success",
            message: "本期工资表生成成功！",
          });
          getTableData();
          // var isc = 0;
          // if ($("#showall1").is(":checked") == true) {
          //     isc = 1;
          // }
          // InitSalaryTable();
          if (res.data == 101) {
            ElNotify({
              type: "warning",
              message: "本月工资表已根据最新的五险一金比例生成！",
            });
          }
          handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
          return;
        } else if (res.data == 100) {
          // ElConfirm("本期已有工资数据，无需再次生成！", true);
          againCreate();
          return;
        } else if (res.data == 1) {
          ElConfirm("当前账套没有工资数据无法一键生成，请先导入工资数据或生成空白工资表！");
          return;
        } else if (res.data == 999) {
          ElConfirm("当前账套暂无员工可以在本月发工资的数据！");
          return;
        }
        ElNotify({
          type: "warning",
          message: "本期工资表生成失败！",
        });
      });
    } else {
      if (await IsHaveSalaryData()) {
        againCreate();
        return;
      } else {
        if (!useAccountSetStoreHook().permissions.includes("salarymanage-canimport")) {
          CreateEmptySalaryData();
        } else {
          salaryDataShow.value = true;
        }
      }
    }
  } else {
    ElConfirm("当前账套暂无员工可以在本月发工资的数据！", true);
    return;
  }
}

function againCreate() {
  ElAlert({
    message: "本期已有工资数据，继续生成工资表将覆盖原有工资数据，是否继续生成？",
  }).then(async (r) => {
    if (r) {
      if (await IsHasCheckOut()) {
        ElNotify({
          type: "warning",
          message: "本月已结账，无法导入工资数据！",
        });
        return;
      }
      salaryDataShow.value = true;
    }
  });
}

//个税申报表
async function downloadfile() {
    if ((await GetIsHaveData()) > 0) {
        request({
            url: `/api/Employee/ExistsEmployee`,
            method: "post",
        }).then(async (res: any) => {
            if (res.data) {
                downloadFile(`/api/Salary/ExportTemplate?mid=` + searchInfo.month);
            }
        });
    } else {
        ElNotify({
            type: "warning",
            message: "当前账套暂无员工可以在本月发工资的数据！",
        });
        return;
    }
}

async function uploadCheck() {
  let result = true;
  if ((await IsHaveSalaryData()) != 0 && (importWageShow.value || individualTaxShow.value)) {
    importWageShow.value = false;
    individualTaxShow.value = false;
    result = await ElAlert({
      message: "本期已有工资数据，继续导入将覆盖原有工资数据，是否继续导入？",
    });
  }
  if (await IsHasCheckOut()) {
    importActualShow.value = false;
    ElNotify({
      type: "warning",
      message: "本月已结账，无法导入工资数据！",
    });
    result = false;
  }
  return result;
}

const uploadSuccess = (res: IResponseModel<any>) => {
  if (res.state === 1000 && res.data) {
    ElNotify({
      message: "导入成功",
      type: "success",
    });
    individualTaxShow.value = false;
    importWageShow.value = false;
    importActualShow.value = false;
    handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
    getTableData();
  } else if (res.state === 2000) {
    ElNotify({
      message: res.msg,
      type: "warning",
    });
  }
};

function sendWage() {
  var period = searchInfo.month;
  var asid = useAccountSetStoreHook().accountSet?.asId;
  var url = "/salary/newbuild?asid=" + asid + "&period=" + period + "&type=" + (window.isErp ? "erp" : window.isProSystem ? "pro" : "jz");
  globalWindowOpen(window.salaryHost + url);
}

async function CheckTaxPayerInfo(taxPayerName: string, taxPayerNumber: string): Promise<string> {
  let message = "";
  if (taxPayerName != "") {
    await request({
      url: "/api/Company/List?module=1010&companyName=" + taxPayerName,
    }).then(async (res: any) => {
      //taxpayerName为空，故还没做操作
      if (res.data == null || res.data.companyInfos.length == 0) {
        message = "纳税人名称不正确，请修改后申报！";
      } else if (res.data.creditCode != taxPayerNumber) {
        let companyIndex = -1;
        for (let i = 0; i < res.data.companyInfos.length; i++) {
          if (res.data.companyInfos[i].name === taxPayerName) {
            companyIndex = i;
            if (res.data.companyInfos[i].creditCode === taxPayerNumber) {
              break;
            }
          }
        }
        if (companyIndex === -1) {
          message = "纳税人名称不正确，请修改后申报！";
        } else {
          if (res.data.companyInfos[companyIndex].creditCode !== taxPayerNumber) {
            message = "纳税人识别号不正确，请修改后申报！";
          }
        }
      }
    });
  }
  return message;
}

//校验工资数据
async function CheckEmployeeData() {
  let checkMsg = {
    message: "",
    names: [],
  };
  await request({
    url: `/api/SalaryTaxDeclare/CheckEmployeeData?pid=` + searchInfo.month,
    method: "post",
  }).then((res: any) => {
    if (res.data != null) {
      if (res.data.idErrors.length > 0) {
        checkMsg.message = "存在" + res.data.idErrors.length + "名员工身份证号不正确：";
        checkMsg.names = res.data.idErrors;
      } else if (res.data.phoneErrors.length > 0) {
        checkMsg.message = "存在" + res.data.phoneErrors.length + "名员工手机号不正确：";
        checkMsg.names = res.data.phoneErrors;
      } else if (res.data.nameErrors.length > 0) {
        checkMsg.message = "存在" + res.data.nameErrors.length + "名员工的姓名需要修改：";
        checkMsg.names = res.data.nameErrors;
      } else if (res.data.idRepeatErrors.length > 0) {
        checkMsg.message = "存在" + res.data.idRepeatErrors.length + "名员工身份证号重复：";
        checkMsg.names = res.data.idRepeatErrors;
      } else if (res.data.phoneRepeatErrors.length > 0) {
        checkMsg.message = "存在" + res.data.phoneRepeatErrors.length + "名员工手机号重复：";
        checkMsg.names = res.data.phoneRepeatErrors;
      }
    }
  });
  return checkMsg;
}

const empErrorMsg = ref<string>("");
const checkEmployeeName = ref([]);
function TaxDeclare() {
  request({
    url: `/api/SalaryTaxDeclare/GetTaxInfo`,
    method: "post",
  }).then(async (res: any) => {
    if (res.state == 1000) {
      if (res.data.isEnd) {
        ElConfirm("本月申报已过期，请前往办税服务厅办理。", true);
      } else {
        if (res.data.infoComplete) {
          //启信宝校验
          let checkMsg: string = await CheckTaxPayerInfo(res.data.taxPayerName, res.data.taxPayerNumber);
          if (checkMsg == "") {
            if (!(await IsHaveSalaryData())) {
              ElConfirm("暂无本月工资数据，无法进行个税申报！");
            } else {
              let checkEmployee = await CheckEmployeeData();
              if (checkEmployee.message != "" && checkEmployee.names.length > 0) {
                empErrorMsg.value = checkEmployee.message;
                checkEmployeeName.value = checkEmployee.names;
                divEmpError.value = true;
              } else {
                let isTaxDeclareData = await isTaxDeclare(); //获取申报状态
                switch (isTaxDeclareData.declareStatus) {
                  case 1:
                  case 10:
                    taxDeclarationWait.value = true;
                    startQueryTaxDeclareResult(isTaxDeclareData.applyId);
                    setTaxWaitingStatus();
                    break;
                  case 2:
                    ElConfirm("亲，所选月份工资已申报成功过，无法重复申报。");
                    break;
                  case 0:
                    taxDeclaration.value = true; //无申报或申报失败都是0 进入个税申报弹框
                    break;
                  case 98:
                    ElConfirm("本月已成功申报个税！", false).then(() => {
                      declareRecordHandle(1);
                    });
                    break;
                  case 99:
                    ElConfirm("目前正在进行个税申报，请稍等！", false).then(() => {
                      declareRecordHandle(1);
                    });
                    break;
                  default:
                    ElConfirm("亲，操作失败，请稍后重试。");
                    break;
                }
              }
            }
          } else {
            ElConfirm(checkMsg).then((r: Boolean) => {
              if (r) {
                closeCurrentTab();
                globalWindowOpenPage("/Settings/AccountSets?isFromTax=true", "账套");
              }
            });
          }
        } else {
          ElConfirm("请先联系管理员完善税务信息，即可实现个税申报！").then((r: Boolean) => {
            if (r) {
              if (
                useAccountSetStoreHook().permissions.includes("accountset-canedit") ||
                (window.isErp && accountsetStore.accountSet?.permission === "*********")
              ) {
                if (window.isErp) {
                  erpCreateTab("/ArgsSetting?taxmanage=1", "系统参数");
                } else {
                  globalWindowOpenPage("/Settings/AccountSets?isFromTax=true&r=" + Math.random(), "账套");
                }
              } else {
                if (window.isErp) {
                  ElConfirm("请登录管理员账号，在系统设置-系统参数中设置好财税参数再进行纳税申报。");
                } else {
                  ElConfirm("请联系账套管理员，在设置-账套中设置好税务信息再进行个税申报。");
                }
              }
            }
          });
        }
      }
    }
  });
}

function toEmployeeInfo() {
  divEmpError.value = false;
  closeCurrentTab();
  globalWindowOpenPage("/Salary/EmployInfo", "员工信息");
}
// 个税申报
let gotoTaxDisabled = ref<boolean>(true);
function ApplyTaxDeclare() {
  if (!gotoTaxDisabled.value) return;
  gotoTaxDisabled.value = false;
  request({
    url: `/api/SalaryTaxDeclare/IsAuthorized`,
    method: "post",
  })
    .then((res: any) => {
      if (res.state == 1000) {
        if (res.data) {
          TaxDeclare();
        }
      }
    })
    .finally(() => {
      setTimeout(() => {
        gotoTaxDisabled.value = true;
      }, 2000);
    });
}

//获取当前申报状态
async function isTaxDeclare() {
  let isTaxDeclareInfo = {
    declareStatus: -1,
    applyId: "",
    salaryCount: 0,
    message: "",
  };
  await request({
    url: `/api/SalaryTaxDeclare/GetTaxDeclareStatus?mid=` + searchInfo.month,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000) {
      isTaxDeclareInfo = res.data;
    }
  });
  return isTaxDeclareInfo;
}

async function IsHasCheckOutForVoucher(mid: string) {
  var pmid = searchInfo.month;
  if (mid != undefined) {
    var y = mid.substring(0, 4);
    var m = mid.substring(4);
    pmid = y.toString() + m.toString();
  }
  let status = false;
  await request({
    url: `/api/Salary/IsCheckOut?mid=${pmid}`,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000 && res.data === true) {
      // if (res.data == 3) {
      status = true;
      // }
    }
  });
  return status;
}

async function IsHasCheckOut(month?: string) {
  let status = false;
  await request({
    url: `/api/Salary/IsCheckOut?mid=${month ? month : searchInfo.month}`,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000 && res.data === true) {
      status = true;
    }
  });
  return status;
}

async function IsHasCheckOutForVoucherForAdmission(pid: string | number) {
  let status = false;
  await request({
    url: `/api/Period/IsCheckOut?pid=` + pid,
    method: "post",
  }).then((res: any) => {
    if (res.state == 1000 && res.data === true) {
      status = true;
    }
  });
  return status;
}

async function deleteHandle() {
  if (await IsHasCheckOut()) {
    ElConfirm("当前月份已结账，无法删除工资数据！", true);
    return;
  }
  if (eSnList.value.length == 0) {
    ElConfirm("请选中需要删除的工资数据！", true);
    return;
  }
  ElConfirm("确定删除当前选中的工资数据吗？").then((r: Boolean) => {
    if (r) {
      request({
        url: `/api/Salary/Batch?mid=${searchInfo.month}`,
        method: "delete",
        data: eSnList.value,
        headers: {
          "Content-Type": "application/json",
        },
      }).then((res: any) => {
        if (res.state == 1000 && res.data) {
          ElNotify({
            type: "success",
            message: "删除成功",
          });
          getTableData();
        } else if (res.state == 2000) {
          ElNotify({
            type: "warning",
            message: res.msg,
          });
        }
      });
    }
  });
}

function exportHandle() {
  globalExport(`/api/Salary/Export?mid=` + searchInfo.month);
}

const printPeriod = reactive({
  startMid: "",
  endMid: "",
});

watch(
  () => searchInfo.month,
  (val) => {
    printPeriod.startMid = val;
    printPeriod.endMid = val;
  }
);

function printValidate() {
  //起始日期不得大于结束日期
  if (printPeriod.startMid > printPeriod.endMid) {
    ElNotify({
      type: "warning",
      message: "亲，打印起始月份不得大于结束月份哦！",
    });
    return false;
  }

  return true;
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint("salary", `/api/Salary/Print`, {}, false, true, printValidate);

function wageRecordHandle() {
  const period = searchInfo.month;
  const asid = useAccountSetStoreHook().accountSet?.asId;
  globalWindowOpen(
    window.salaryHost +
      "/salary/sendrecord?asid=" +
      asid +
      "&period=" +
      period +
      "&type=" +
      (window.isErp ? "erp" : window.isProSystem ? "pro" : "jz")
  );
}
const declareSlotRef = ref(); //申报记录ref
const declareData = ref([]);
const declareTotal = ref(0);
//获取申报记录

let nowYear: number;
function declareRecordHandle(status: number = 0) {
  //根据申报状态的失败和成功决定申报记录组件状态单选框不同table展示
  declareSlotRef.value.declareSearchInfo.status = status === 0 ? "0" : "1";
  let today = new Date();
  nowYear = today.getFullYear();
  let nowMonth = today.getMonth();
  if (nowMonth == 0) {
    nowYear = nowYear - 1;
  }
  declareSlotRef.value.declareSearchInfo.year = nowYear;
  setTimeout(QueryTaxDeclareRecord, 710);
}

function QueryTaxDeclareRecord() {
  currentSlot.value = "declare";
  const params = {
    year: nowYear,
    status: declareSlotRef.value.declareSearchInfo.status,
    pageIndex: paginationData.currentPage,
    pageSize: paginationData.pageSize,
  };
  request({
    url: `/api/SalaryTaxDeclare/PagingList?` + getUrlSearchParams(params),
  }).then((res: any) => {
    (declareData.value = res.data.data), (declareTotal.value = res.data.count);
  });
}
function CloseTaxDeclareRecord() {
  currentSlot.value = "main";
  getTableData();
  beginSearch();
}
const taxDiffList = ref();
const taxDiffPeriod = ref("");
function enterTaxDiff(list: any, date: string) {
  taxDiffList.value = list;
  taxDiffPeriod.value = date;
  currentSlot.value = "taxDiff";
}
// 取消新增自定义列
function cancleCustomWage() {
  currentSlot.value = "main";
  getCustomDetail();
  getTableData();
}

//工资月份
const allMonth = ref<IPeriodItem[]>([]);
function getAllMonth() {
  return request({
    url: `/api/Salary/GetSearchMonthList`,
    method: "post",
  });
}

const departmentList = ref<ISelectItem[]>([{ value: "0", label: "全部" }]);
function getDepartmentApi() {
  request({
    url: `/api/AssistingAccounting/DepartmentList?showAll=true&onlyLeaf=false`,
    method: "get",
  }).then((res: any) => {
    if (res.state == 1000) {
      let list = res.data?.reduce((prev: ISelectItem[], item: any) => {
        if (item.aaeid > 0) {
          prev.push({
            value: item.aaeid,
            label: item.aaname,
          });
        }
        return prev;
      }, []);
      departmentList.value = [...departmentList.value, ...list];
    }
  });
}

function IsHaveEmployee() {
  request({
    url: `/api/Employee/ExistsEmployee`,
    method: "post",
  }).then(async (res: any) => {
    if (res.state === 1000) {
      if (res.data) {
        if ((await GetIsHaveData()) > 0) {
          if (router.currentRoute.value.query.autoDeclare == "1") {
            ApplyTaxDeclare();
          } else {
            if (
              useAccountSetStoreHook().permissions.includes("salarymanage-canimport") &&
              useAccountSetStoreHook().permissions.includes("salarymanage-canedit")
            ) {
              if (trialStatusStore.isTrial && trialStatusStore.isExpired) return;
              IsHaveSalaryDataForAcc();
            }
          }
        } else {
          ElConfirm("当前账套暂无员工可以在本月发工资的数据！");
          return;
        }
      } else {
        // useDialogStore().dialogCount = true;
        ElAlert({ message: "暂无员工信息，请先完善员工信息", hideCancel: true, zIndex: 999 }).then(() => {
          // useDialogStore().dialogCount = false;
          if (isErp.value) {
            erpCreateTab("/Employees", "职员列表");
            erpCloseTab("/Salary/SalaryManage", "工资管理");
          } else {
            //跳转到员工信息
            closeTab("/Salary/SalaryManage");
            if (checkPermission(["employinfo-canedit"])) {
              globalWindowOpenPage("/Salary/EmployInfo", "员工信息");
            } else {
              ElNotify({
                type: "warning",
                message: "您没有新增员工的权限哦~",
              });
              globalWindowOpenPage("/Default/Default", "首页");
            }
          }
        });
        return;
      }
    }
  });
}

function IsHaveSalaryDataForAcc() {
  request({
    url: `/api/Salary/ExistsSalary`,
    method: "post",
  }).then((res: any) => {
    if (res.state === 1000) {
      if (!res.data) {
        salaryDataShow.value = true;
      }
    }
  });
}

function ImportSelect() {
  if (salaryDirection.value === "1") {
    CreateEmptySalaryData();
  } else {
    salaryDataShow.value = false;
    individualTaxShow.value = true;
  }
}

function CancelImportSelect() {
  salaryDataShow.value = false;
  salaryDirection.value = "2";
}

//表格数据
const tableData = ref<any[]>([]);
const beforeData = ref<any[]>([]);
const handleLoadData = () => {
  getTableData();
};
let hasLoading = false;

const getTableData = (column?: any, index?: number) => {
  loading.value = true;
  const data = {
    pid: +searchInfo.month,
    department: searchInfo.department,
    employeeName: searchInfo.name,
    showDetail: searchInfo.showInfo,
    pageIndex: paginationData.currentPage,
    pageSize: paginationData.pageSize,
  };
  request({
    url: `/api/Salary/PagingList?` + getUrlSearchParams(data),
  })
    .then((res: IResponseModel<ISalaryData>) => {
      columns.value = setColumns(columns.value, searchInfo.showInfo, searchInfo.salaryMultiple);
      if (searchInfo.showInfo) {
        getCustomDetail();
      }
      tableData.value = res.data.data;
      beforeData.value = JSON.parse(JSON.stringify(tableData.value));
      paginationData.total = res.data.count;
      if (column !== undefined && index !== undefined) {
        const item = tableData.value[index];
        nextTick().then(() => {
          handleRowClick(item, column);
        });
      }
      handleGetAttachFileCount();
    })
    .catch((error) => console.log(error))
    .finally(() => {
      loading.value = false;
      if (!hasLoading) {
        tryClearCustomUrlParams(route);
        hasLoading = true;
      }
    });
};
// 自定义工资项目
const customColumns = ref<any>({ insurance: [], salary: [] });
const formatter = (row: any, column: any, value: any) => {
  return formatMoneyWithZero(value);
};
// 获取自定义列表
const getCustomDetail = () => {
  request({
    url: `/api/Salary/GetTitleForDetail?mid=${searchInfo.month}`,
    method: "post",
  }).then((res: IResponseModel<IDetailColumn>) => {
    let diyColumns: IDetailColumn = { insurance: [], salary: [] };
    //应发
    let salaryCol0 = res.data.salary[0].length > 0 ? res.data.salary[0] : [];
    let grossPayDetail = salaryCol0.reduce((prev: any, item: any, index: number) => {
      prev.push({
        label: item.item_name,
        prop: `gross_pay_${index + 1}`,
        align: "right",
        headerAlign: "right",
      });
      return prev;
    }, []);
    //应扣
    let salaryCol1 = res.data.salary[1].length > 0 ? res.data.salary[1] : [];
    let deductWageDetail = salaryCol1.reduce((prev: any, item: any, index: number) => {
      prev.push({
        label: item.item_name,
        prop: `deduct_wage_${index + 1}`,
        align: "right",
        headerAlign: "right",
      });
      return prev;
    }, []);
    //税后调整
    let afterTaxDetail = res.data.salary[2].reduce((prev: any, item: any, index: number) => {
      prev.push({
        label: item.item_name,
        prop: `after_tax_${index + 1}`,
        align: "right",
        headerAlign: "right",
      });
      return prev;
    }, []);
    //五险一金个人 代扣个人项
    // 表头默认值
    let insurancePCol =
      res.data.insurance[0].length > 0
        ? res.data.insurance[0]
        : [
            {
              as_id: 0,
              item_id: 1001,
              item_name: "养老保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 1,
            },
            {
              as_id: 0,
              item_id: 1002,
              item_name: "医疗保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 2,
            },
            {
              as_id: 0,
              item_id: 1003,
              item_name: "失业保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 3,
            },
            {
              as_id: 0,
              item_id: 1006,
              item_name: "住房公积金",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 6,
            },
          ];
    let insurancePDetail = insurancePCol.reduce((prev: any, item: any) => {
      prev.push({
        label: item.item_name,
        // prop: `insurance_p_${Number(item.item_id.toString()) - 1000}`,
        prop: `insurance_p_${item.index.toString()}`,
        align: "right",
        headerAlign: "right",
      });
      return prev;
    }, []);
    //五险一金公司
    let insuranceCCol =
      res.data.insurance[1].length > 0
        ? res.data.insurance[1]
        : [
            {
              as_id: 0,
              item_id: 1001,
              item_name: "养老保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 1,
            },
            {
              as_id: 0,
              item_id: 1002,
              item_name: "医疗保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 2,
            },
            {
              as_id: 0,
              item_id: 1003,
              item_name: "失业保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 3,
            },
            {
              as_id: 0,
              item_id: 1004,
              item_name: "工伤保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 4,
            },
            {
              as_id: 0,
              item_id: 1005,
              item_name: "生育保险",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 5,
            },
            {
              as_id: 0,
              item_id: 1006,
              item_name: "住房公积金",
              companypercent: 0.0,
              personpercent: 0.0,
              index: 6,
            },
          ];
    let insuranceCDetail = insuranceCCol.reduce((prev: any, item: any) => {
      prev.push({
        label: item.item_name,
        prop: `insurance_c_${item.index.toString()}`,
        align: "right",
        headerAlign: "right",
      });
      return prev;
    }, []);
    diyColumns.salary.push(grossPayDetail);
    diyColumns.salary.push(deductWageDetail);
    diyColumns.salary.push(afterTaxDetail);
    diyColumns.insurance.push(insurancePDetail);
    diyColumns.insurance.push(insuranceCDetail);
    customColumns.value = diyColumns;
  });
};

function searchDataWithTaxStatus() {
  getTableData();
  beginSearch();
}

function disabledDate(time: Date) {
  const start = allMonth.value[allMonth.value.length - 1]?.time ?? new Date();
  const end = allMonth.value[0]?.time ?? new Date();
  const asStartDate = dayjs(start).valueOf();
  const asEndDate = dayjs(end).valueOf();
  return time.getTime() < asStartDate || time.getTime() > asEndDate;
}

function disabledPrintStartMonth(time: Date) {
  return disabledDate(time);
}
function disabledPrntEndMonth(time: Date) {
  return disabledDate(time) || time.getTime() < dayjs(printPeriod.startMid).valueOf();
}

function changeMonth() {
  if (activeName.value === "second") {
    getAccruedsalary();
  }
}

let declareStatusTimer: number;
const initSearchInfo = async () => {
  await Promise.all([getAllMonth(), getDepartmentApi()]).then((res: any) => {
    allMonth.value = res[0].data?.reduce((prev: IPeriodItem[], item: any) => {
      prev.push({
        value: item.key,
        label: item.text,
        time: item.key.slice(0, 4) + "-" + item.key.slice(4),
      });
      return prev;
    }, []);
    const selectedItem = res[0].data?.find((item: any) => item.isSeledcted);
    if ((route.query.from === "voucherList" || route.query.from === "ERecord") && route.query.dateKey) {
      const isRightMonth = res[0].data?.some((item: any) => item.key === route.query.dateKey);
      searchInfo.month = isRightMonth ? (route.query.dateKey as string) : selectedItem?.key;
      salaryTitle.value =
        res[0].data?.find((item: any) => {
          return item.key === searchInfo.month;
        })?.text ?? "";
    } else {
      searchInfo.month = selectedItem?.key;
      salaryTitle.value = selectedItem?.text;
    }
    confirmMonth.value = searchInfo.month;
    IsHaveVoucherTemplateAndEntry();
    IsHaveEmployee();
    isHasMultiSalary();
    IsSettingSalaryItem(searchInfo.month, searchDataWithTaxStatus);
    declareStatusTimer = setInterval(function () {
      beginSearch();
    }, 1000 * 60 * 5);
    getCustomDetail();
  });
};

//申报记录
const btnQueryTaxDeclare = ref(true);
const btnTaxDeclare = ref(true);
const btnTaxDeclareWait = ref(false);
const btnTaxDeclareTxt = ref("个税申报");
const imgTaxDeclare = ref(false);
const taxDeclareInfo = ref();
interface IAttachFileInfo {
  data: any[];
  parentId: number;
}
interface IGetAttachFileList extends IAttachFileInfo {
  result: boolean;
}
const attachFileInfo = reactive<IAttachFileInfo>({
  data: [],
  parentId: 10006,
});
const attachFileCount = computed(() => attachFileInfo.data.length);
async function handleGetAttachFileCount() {
  await request({ url: "/api/Salary/GetAttachFileList", method: "post", params: { mid: searchInfo.month } }).then(
    (res: IResponseModel<IGetAttachFileList>) => {
      if (res.state === 1000 && res.data.result) {
        attachFileInfo.data = res.data.data;
        attachFileInfo.parentId = res.data.parentId;
      }
    }
  );
}
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
async function saveAttachFile(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
  const needToast = await checkNeedToastWithBillAndVoucher(_params.mid, delFileids);
  if (!needToast) {
    saveAttachFileFn(_params.mid, newFileids, delFileids);
    return;
  }
  showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
    saveAttachFileFn(_params.mid, newFileids, delFileids, batchDelte);
  });
}
async function saveAttachFileFn(mid: number, newFileids: number[], delFileids: number[], isNeedSaveToVoucherForDelete = false) {
  const params = {
    mid,
    newFileids: newFileids.join(","),
    delFileids: delFileids.join(","),
    isNeedSaveToVoucherForAdd: true,
    isNeedSaveToVoucherForDelete,
  };
  await request({ url: "/api/Salary/SaveAttachFile", method: "post", params }).then((res: IResponseModel<boolean>) => {
    if (res.state === 1000 && res.data) {
      ElNotify({ type: "success", message: "保存成功" });
      handleGetAttachFileCount();
    } else {
      ElNotify({ type: "error", message: "保存失败" });
    }
  });
}
async function checkNeedToastWithBillAndVoucher(mid: number, attachFiles: number[]) {
  const params = { mid, attachFiles: attachFiles.join(",") };
  return await request({ url: "/api/Salary/GetNeedSaveToVoucher", method: "post", params })
    .then((res: IResponseModel<boolean>) => {
      if (res.state !== 1000) return false;
      return res.data;
    })
    .catch(() => {
      return false;
    });
}
function handleCheckAttachFile() {
  const params = { mid: searchInfo.month };
  const list = attachFileInfo.data.map((item: any) => {
    item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
    return item;
  });
  uploadFileDialogRef.value?.open(params, list, attachFileInfo.parentId);
}
function setTaxWaitingStatus() {
  btnTaxDeclare.value = true;
  btnTaxDeclareWait.value = true;
  btnTaxDeclareTxt.value = "申报中";
  imgTaxDeclare.value = true;
  btnQueryTaxDeclare.value = true;
}
onMounted(async () => {
  IsHasCheckOut();
  initSearchInfo();
  getTaxMonth(); //获取个税申报的月份
  const currentRouterModel = routerArray.value.find((item) => item.alive);
  if ((currentRouterModel as any)?.stop + "" !== "undefined") {
    setTimeout(() => {
      delete (currentRouterModel as any).stop;
    });
  }
});
// 获取图标地址
const getTaxDeclareImg = (url: string) => {
  return new URL(`/src/assets/Salary/${url}`, import.meta.url).href;
};
const taxDeclareImg = ref(getTaxDeclareImg("tax-declare-waiting.png"));
function taxDeclareMouseOver() {
  taxDeclareImg.value = getTaxDeclareImg("tax-declare-waiting-2.png");
}
function taxDeclareMouseOut() {
  taxDeclareImg.value = getTaxDeclareImg("tax-declare-waiting.png");
}
async function beginSearch() {
  taxDeclareInfo.value = await isTaxDeclare();
  if (taxDeclareInfo.value.declareStatus == -1) {
    btnTaxDeclare.value = false;
    btnTaxDeclareWait.value = false;
    imgTaxDeclare.value = false;
    btnQueryTaxDeclare.value = false;
  } else if (taxDeclareInfo.value.declareStatus == 1) {
    setTaxWaitingStatus();
  } else if (taxDeclareInfo.value.declareStatus == 10) {
    btnTaxDeclare.value = true;
    btnTaxDeclareWait.value = true;
    btnTaxDeclareTxt.value = "缴税中";
    imgTaxDeclare.value = true;
    btnQueryTaxDeclare.value = true;
  } else {
    btnTaxDeclareWait.value = false;
    btnTaxDeclare.value = true;
    btnTaxDeclareTxt.value = "个税申报";
    imgTaxDeclare.value = false;
    btnQueryTaxDeclare.value = true;
  }
}
const AccruedsalaryData = ref([]);
const partMainMonth = ref<string>(searchInfo.month);
let famount = 0; //凭证发放工资总数
let times = 0; //发放凭证生成的个数
function getAccruedsalary() {
  let content = "";
  warnMessage.value = "";
  warnMessage1 = "";
  warnShow.value = false;
  partMainLoading.value = true;
  famount = 0;
  times = 0;
  request({
    url: `/api/SalaryTemplate/SimpleList?mid=` + partMainMonth.value,
    method: "get",
  })
    .then(async (res: any) => {
      AccruedsalaryData.value = res.data.map((item: any) => item.split(":"));
      for (let k in res.data) {
        let strarr = res.data[k].split(":");
        if (strarr[1] == "发放工资（第一次）" || strarr[1] == "发放工资（第二次）") {
          famount += await getCalAmount(strarr[0]);
        }
      }
      for (let k in res.data) {
        let strarr = res.data[k].split(":");
        content = content + (await AppendString(strarr[0], strarr[1], strarr[2], k));
      }
      warnMessage.value = warnMessage1;
      (document.getElementById("itemBoxsContext") as HTMLElement).innerHTML = content;
    })
    .finally(() => {
      partMainLoading.value = false;
    });
}

let jitivoucheramount = "";
let warnMessage1 = "";
async function AppendString(vtid: string, vtname: string, amount: string, k: string) {
  let str = "";
  // warnShow.value = false;
  await request({
    url: `/api/SalaryVoucher/GetExistsVoucherStatus?mid=${partMainMonth.value}&vtid=${vtid}`,
    method: "post",
  }).then(async (res: any) => {
    amount = TurnToStandardFormat(amount);
    if (res.data.isCreate) {
      let voucheramount = await GetVoucherRowSum(res.data.pid, res.data.vid, true);
      if (vtname == "计提工资") {
        jitivoucheramount = voucheramount;
        if (voucheramount != amount) {
          warnShow.value = true;
          warnMessage1 = "计提工资与工资表数据不符";
          str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, true);
        } else {
          warnShow.value = false;
          warnMessage1 = "";
          str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, false);
        }
      } else if (vtname == "发放工资") {
        if (voucheramount != jitivoucheramount) {
          warnShow.value = true;
          if (warnMessage1 != "" && !warnMessage1.match(`发放工资与计提工资不符`)) {
            warnMessage1 = warnMessage1 + ",发放工资与计提工资不符";
          } else {
            warnMessage1 = "发放工资与计提工资不符!";
          }
          str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, true);
        } else {
          str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, false);
        }
      } else if (vtname == "发放工资（第一次）" || vtname == "发放工资（第二次）") {
        if (times === 2 && TurnToStandardFormat(String(famount)) != jitivoucheramount) {
          warnShow.value = true;
          if (warnMessage1 != "" && !warnMessage1.match(`发放工资与计提工资不符`)) {
            warnMessage1 = warnMessage1 + ",发放工资与计提工资不符";
          } else if (warnMessage1 != "" && warnMessage1.match(`发放工资与计提工资不符`)) {
            warnMessage1 = warnMessage1 + "";
          } else {
            warnMessage1 = "发放工资与计提工资不符!";
          }
          str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, true);
        } else {
          str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, false);
        }
      } else {
        str = getHtml(k, vtname, voucheramount, res.data.vid, res.data.pid, vtid, false);
      }
    } else {
      //生成凭证
      if (vtname == "计提工资") {
        jitivoucheramount = amount;
      }
      str =
        "<div class='item-box item-box-create' id='salesCost" +
        k +
        "'><div class='item-box-title'>" +
        vtname +
        "</div>" +
        "<div class='money'><span class='currecy has-currency hasCurrency tooltip-f' title=''>" +
        amount +
        "</span></div>" +
        "<div class='check-btn create-voucher-btn" +
        " " +
        (useAccountSetStoreHook().permissions.includes("salarymanage-cancreatevoucher") ? "" : "disabled") +
        "' onclick='CreateVoucher(\"" +
        vtid +
        '","' +
        vtname +
        "\")'>生成凭证</div></div>";
    }
  });
  return str;
}
async function getCalAmount(vtid: string) {
  let all = 0;
  await request({
    url: `/api/SalaryVoucher/GetExistsVoucherStatus?mid=${partMainMonth.value}&vtid=${vtid}`,
    method: "post",
  }).then(async (res: any) => {
    if (res.data.isCreate) {
      times++;
      all = Number(await GetVoucherRowSum(res.data.pid, res.data.vid, false));
    }
  });
  return all;
}
function getHtml(k: string, vtname: string, amount: string, vid: number, pid: number, vtid: string, warnFlag: boolean) {
  let str = `  
        <div class='item-box item-box-create' id='salesCost${k}' style=''>  
            <div class='item-box-title'>  
                ${vtname}  
            </div>  
            <div class='money'>  
                <span class='currecy has-currency hasCurrency tooltip-f' title='' style='color:black'>  
                    ${amount}  
                </span>  
            </div>  
            <div class='check-btn check-voucher-btn${
              useAccountSetStoreHook().permissions.includes("salarymanage-cancreatevoucher") ? "" : " disabled"
            }'   
                ${warnFlag ? "style='background-color:#EEB400'" : ""}   
                onclick='ShowVoucher("${vid}", "${pid}", "${vtid}", "${vtname}")'>  
                查看凭证  
            </div>  
        </div>  
    `;
  return str;
}
(window as any).CreateVoucher = CreateVoucher;
(window as any).ShowVoucher = ShowVoucher;
async function GetVoucherRowSum(pid: number, vid: number, flag: boolean) {
  let amount = "";
  await request({
    url: `/api/SalaryVoucher/GetVoucherAmount?pid=${pid}&vid=${vid}`,
    method: "post",
  }).then((res: any) => {
    amount = res.data;
  });
  amount = flag ? TurnToStandardFormat(amount) : amount;
  return amount;
}

function TurnToStandardFormat(rl: string) {
  let amount = parseFloat(rl) + "";
  if (amount == "0") {
    amount = "0.00";
  } else {
    if (amount.toString().indexOf(".") == -1) {
      amount = amount.toString().replace(/(\d{1,3})(?=(\d{3})+$)/g, "$1,");
      amount = amount + ".00";
    } else {
      var n = amount.toString().split(".")[0];
      var x = amount.toString().split(".")[1];
      amount = n.toString().replace(/(\d{1,3})(?=(\d{3})+$)/g, "$1,") + "." + x;
    }
  }
  return amount;
}

const voucherSettingList = ref([]);

// 加载所有凭证
const loadTemplates = () => {
  return request({
    url: `/api/SalaryTemplate/List?mid=${partMainMonth.value}`,
  }).then((res: any) => {
    voucherSettingList.value = res.data;
  });
};
function jumptosetting() {
  loadTemplates();
  currentSlot.value = "voucherSetting";
}

const partMainLoading = ref(false);
function handleClick() {
  if (activeName.value === "first") {
    if (!window.isErp) {
      secondTitle.value = "计提工资、发放工资";
    }
    partCurrentSlot.value = "partMain";
    // autoToEdit();
  } else {
    partMainMonth.value = confirmMonth.value;
    secondTitle.value = "计提工资、发放工资";
    getAccruedsalary();
  }
}

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
  getTableData();
});

watch(
  () => partCurrentSlot.value,
  (val) => {
    if (val === "partMain") {
      getAccruedsalary();
    }
  }
);

//根据月份是否计提工资复选框内容为显示还是显示编辑
const checkboxInfo = ref(true);
const isSalaryCheckout = ref(false);
watch(
  salaryTitle,
  async () => {
    checkboxInfo.value = await IsHasCheckOut();
    editShow.value = "-1";
  },
  { immediate: true }
);
//自动进入编辑状态
watch(
  () => searchInfo.showInfo,
  () => autoToEdit()
);
function autoToEdit() {
  if (!searchInfo.showInfo) return;
  if (!checkboxInfo.value && !(trialStatusStore.isTrial && trialStatusStore.isExpired)) {
    nextTick(() => {
      setTimeout(() => {
        const item = tableData.value[0];
        handleRowClick(item);
      }, 500);
    });
  }
}

//个税申报
const taxDeclaration = ref(false); //弹窗绑定值
const taxDeclarationType = ref("1"); //申报类型单选框绑定值
const updateFromDeclare = ref(true); //复选框绑定值
const taxDeclarationMonth = ref(""); //个税申报月份
const getTaxMonth = () => {
  let today = new Date();
  let nowYear = today.getFullYear();
  let nowMonth = today.getMonth();
  if (nowMonth == 0) {
    nowYear = nowYear - 1;
    nowMonth = 12;
  }
  taxDeclarationMonth.value = nowYear + "年" + (nowMonth < 10 ? "0" + nowMonth : nowMonth) + "月";
};

//个税申报确定按钮
const taxDeclarationHandle = async () => {
  let isTrilExpired = false;
  await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.taxDeclare }).then((r: boolean) => {
    isTrilExpired = r;
  });
  if (isTrilExpired) {
    return;
  }
  //校验信息
  let checkStatus = await applyCheckTaxPwd();
  if (checkStatus == 2) {
    //验证正确 进行申报
    request({
      url: `/api/SalaryTaxDeclare/TaxDeclare?`,
      method: "post",
      params: {
        applyType: taxDeclarationType.value,
        mid: searchInfo.month,
        isUpdate: updateFromDeclare.value,
      },
    }).then(async (res: any) => {
      if (res.state === 1000) {
        //成功就进入申报等待弹框
        let isTaxDeclareData = await isTaxDeclare(); //获取申报状态
        //个税申报员工数量
        taxDeclaration.value = false;
        taxDeclarationWait.value = true;
        if (taxDeclarationType.value == "1") {
          startQueryTaxPayResult(res.data.applyId);
        } else {
          startQueryTaxDeclareResult(res.data.applyId);
        }
      } else {
        ElNotify({
          message: "亲，操作失败，请稍后重试。",
          type: "warning",
        });
      }
    });
  } else if (checkStatus == 1) {
    //校验中 进入个税申报校验框
    taxDeclaration.value = false;
    divInfoCheck.value = true;
    initCheck();
    startQueryCheckPwdResult();
  } else {
    //提交校验失败
    ElNotify({
      message: "亲，操作失败，请稍后重试。",
      type: "warning",
    });
  }
};

// {checkImg:Boolean,checkBg:Boolean,checkTxt:String,checkTxtClass:string}
const checkInfoList = reactive<IIofo[]>([]);
const info = {
  checkImgClass: "img-item",
  checkBg: false,
  checkBgClass: "item-waite",
  checkTxt: "等待校验…",
  checkTxtClass: "check-waite",
};

for (let i = 0; i < 9; i++) {
  checkInfoList.push(_.cloneDeep(info));
}
let checkIndex = 0;
const initCheck = () => {
  checkIndex = 1;
  checkInfoList[0].checkImgClass = "img-item img-active";
  checkInfoList[0].checkBg = true;
  checkInfoList[0].checkBgClass = "item-waite item-active";
  checkInfoList[0].checkTxt = "等待校验…";
  checkInfoList[0].checkTxtClass = "check-waite";
  for (let i = 2; i <= 9; i++) {
    checkInfoList[i - 1].checkImgClass = "img-item";
    checkInfoList[i - 1].checkBg = false;
    checkInfoList[i - 1].checkBgClass = "item-waite";
    checkInfoList[i - 1].checkTxt = "等待校验…";
    checkInfoList[i - 1].checkTxtClass = "check-waite";
  }
};
const startQueryCheckPwdResult = () => {
  timer = setInterval(function () {
    queryCheckPwdResult(timer);
  }, 10000);
};
function queryCheckPwdResult(timer: number) {
  request({
    url: `/api/SalaryTaxDeclare/GetCheckTaxPwdStatus`,
    method: "post",
  }).then((res: IResponseModel<any>) => {
    if (res.state === 1000) {
      let status = res.data.status;
      if (status == 2) {
        //成功
        clearInterval(timer);
        console.log("success and stop query check pwd result");
        showCheckPwdResult(2);
      } else if (status == 3) {
        //失败
        clearInterval(timer);
        ElNotify({
          message: res.data.message || "亲，操作失败，请稍后重试。",
          type: "warning",
        });
        console.log("fail and stop query check pwd result");
        showCheckPwdResult(3);
      } else if (status == 1) {
        //校验中
        showCheckPwdResult(1);
      } else {
        clearInterval(timer);
        ElNotify({
          message: "亲，操作失败，请稍后重试。",
          type: "warning",
        });
      }
    }
  });
}

const showTaxDeclarationWait = (params: ITaxDeclarationWaitParams) => {
  taxDeclaration.value = false;
  taxDeclarationWait.value = true;
};
let checkTimer: number;
function showCheckPwdResult(status: number) {
  if (status == 1) {
    //校验中
    checkInfoList[checkIndex - 1].checkImgClass = "img-item";
    checkInfoList[checkIndex - 1].checkBg = false;
    checkInfoList[checkIndex - 1].checkBgClass = "item-waite";
    checkInfoList[checkIndex - 1].checkTxt = "校验成功！";
    checkInfoList[checkIndex - 1].checkTxtClass = "check-success";
    checkInfoList[checkIndex].checkImgClass = "img-item img-active";
    checkInfoList[checkIndex].checkBg = true;
    checkInfoList[checkIndex].checkBgClass = "item-waite item-active";
    if (checkIndex < 8) {
      checkIndex++;
    }
  } else if (status == 2) {
    //成功
    if (checkTimer != null) {
      clearInterval(checkTimer);
    }
    checkTimer = setInterval(function () {
      if (checkIndex == 9) {
        clearInterval(checkTimer);
        checkInfoList[8].checkImgClass = "img-item";
        checkInfoList[8].checkBg = false;
        checkInfoList[8].checkBgClass = "item-waite";
        checkInfoList[8].checkTxt = "校验成功！";
        checkInfoList[8].checkTxtClass = "check-success";
        if (divInfoCheck.value) {
          request({
            url: `/api/SalaryTaxDeclare/TaxDeclare`,
            method: "post",
            params: {
              applyType: taxDeclarationType.value,
              mid: searchInfo.month,
              isUpdate: updateFromDeclare.value,
            },
          }).then((res: any) => {
            if (res.state === 1000) {
              // 关闭个税校验弹窗
              divInfoCheck.value = false;
              //成功就进入申报等待弹框
              taxDeclaration.value = false;
              taxDeclarationWait.value = true;
              if (taxDeclarationType.value == "1") {
                startQueryTaxPayResult(res.data.applyId);
              } else {
                startQueryTaxDeclareResult(res.data.applyId);
              }
            } else {
              ElNotify({
                message: "亲，操作失败，请稍后重试。",
                type: "warning",
              });
            }
          });
        }
      } else {
        checkInfoList[checkIndex - 1].checkImgClass = "img-item";
        checkInfoList[checkIndex - 1].checkBg = false;
        checkInfoList[checkIndex - 1].checkBgClass = "item-waite";
        checkInfoList[checkIndex - 1].checkTxt = "校验成功！";
        checkInfoList[checkIndex - 1].checkTxtClass = "check-success";
        checkInfoList[checkIndex].checkImgClass = "img-item img-active";
        checkInfoList[checkIndex].checkBg = true;
        checkInfoList[checkIndex].checkBgClass = "item-waite item-active";
        checkIndex++;
      }
    }, 2000);
    initCheck();
  } else if (status == 3) {
    //失败
    if (checkTimer != null) {
      clearInterval(checkTimer);
    }
    checkTimer = setInterval(function () {
      if (checkIndex == 9) {
        checkInfoList[8].checkImgClass = "img-item";
        checkInfoList[8].checkBg = false;
        checkInfoList[8].checkBgClass = "item-waite";
        checkInfoList[8].checkTxt = "校验失败！";
        checkInfoList[8].checkTxtClass = "check-fail";
        // $("#divBtnSubmitInfoCheck").show();
        clearInterval(checkTimer);
      } else {
        checkInfoList[checkIndex - 1].checkImgClass = "img-item";
        checkInfoList[checkIndex - 1].checkBg = false;
        checkInfoList[checkIndex - 1].checkBgClass = "item-waite";
        checkInfoList[checkIndex - 1].checkTxt = "校验成功！";
        checkInfoList[checkIndex - 1].checkTxtClass = "check-success";
        checkInfoList[checkIndex].checkImgClass = "img-item img-active";
        checkInfoList[checkIndex].checkBg = true;
        checkInfoList[checkIndex].checkBgClass = "item-waite item-active";
        checkIndex++;
      }
    }, 2000);
  }
}
//校验个税密码
async function applyCheckTaxPwd() {
  let checkStatus = 0;
  await request({
    url: `/api/SalaryTaxDeclare/CheckTaxPwd`,
    method: "post",
  }).then((res: any) => {
    if (res.state === 1000) {
      checkStatus = res.data.status;
    } else {
      ElNotify({
        message: res.msg,
        type: "warning",
      });
    }
  });
  return checkStatus;
}

//个税申报取消按钮
const cancelTaxDeclaration = () => {
  taxDeclaration.value = false;
};

//申报等待中
const taxDeclarationWait = ref(false); //申报等待弹窗绑定值
const closeTaxDeclareWait = () => {
  taxDeclarationWait.value = false;
};

let timer: any = null; //20秒请求一次申报结果
function startQueryTaxDeclareResult(applyId: string) {
  timer = setInterval(function () {
    queryTaxDeclareResult(timer, applyId);
  }, 20000);
}

function queryTaxDeclareResult(timer: any, applyId: string) {
  request({
    url: `/api/SalaryTaxDeclare/GetTaxDeclareStatusById?applyId=${applyId}`,
    method: "post",
  }).then((res: any) => {
    if (res.state === 1000) {
      if (res.data == 0 || res.data == 1) {
        //申报中
      } else if (res.data == 2 || res.data == 4) {
        //成功进入申报成功记录
        clearInterval(timer);
        taxDeclarationWait.value = false;
        declareRecordHandle(1);
        // ShowTaxDeclareRecord(1);
      } else if (res.data == 3) {
        //失败进入申报失败记录
        clearInterval(timer);
        taxDeclarationWait.value = false;
        declareRecordHandle(0);
        // ShowTaxDeclareRecord(0);
      }
    }
    // else {
    //     clearInterval(timer);
    //     taxDeclarationWait.value = false;
    //     declareRecordHandle(0);
    // }
  });
}

//查询缴税结果
function startQueryTaxPayResult(applyId: string) {
  timer = setInterval(function () {
    queryTaxPayResult(timer, applyId);
  }, 20000);
}
function queryTaxPayResult(timer: any, applyId: string) {
  request({
    url: `/api/SalaryTaxDeclare/GetTaxPayStatusById?applyId=${applyId}`,
    method: "post",
  }).then((res: any) => {
    if (res.state === 1000) {
      if (res.data == 11) {
        clearInterval(timer);
        taxDeclarationWait.value = false;
        declareRecordHandle(1);
      } else if (res.data == 12) {
        taxDeclarationWait.value = false;
        declareRecordHandle(0);
      } else {
        //缴税中
        console.log("缴税中");
      }
    }
  });
}

onUnmounted(() => {
  clearInterval(timer);
  clearInterval(declareStatusTimer);
  cacheRouterQueryParams = null;
});

//编辑

const editShow = ref("-1");
const allowBlur = ref(true);
//双击行
const handleRowDblclick = (row: any, column: any) => {
  if (searchInfo.showInfo) {
    beforeInputRow = JSON.parse(JSON.stringify(row));
    if (["姓名", "编号", "部门", "手机号", "身份证号码", "个税免征额"].includes(column.label)) {
      editShow.value = row.e_name === "" ? "-1" : row.index + "";
      editFocus(column);
    }
    return;
  }
  searchInfo.showInfo = true;
  getTableData();
};
const tableContentRef = ref<HTMLElement>();
const handleRowClick = async (row: any, column?: any) => {
  //不是编辑状态不进行操作
  if (!searchInfo.showInfo) return;
  //没有编辑权限
  if (!checkPermission(["salarymanage-canedit"])) return;
  //结账的工资不能编辑
  if (await IsHasCheckOut()) {
    isSalaryCheckout.value = true;
    return;
  }
  //提交上一次保存
  if (row.index + "" !== editShow.value) {
    allowBlur.value = false;
    const item = tableData.value.find((item) => item.index + "" === editShow.value) as IItemDetailData;
    if (typeof item !== "undefined" && editShow.value !== "-1") {
      if (row.e_code !== "" && row.e_name !== "" && canClickNextRow(column)) {
        submitEdit(item, column, row.index);
      } else {
        submitEdit(item);
      }
      return;
    }
    if (column && ["姓名", "编号", "部门", "手机号", "身份证号码", "个税免征额"].includes(column.label)) return;
    beforeInputRow = JSON.parse(JSON.stringify(row));
    editShow.value = row.e_name === "" ? "-1" : row.index + "";
    editFocus(column);
  }
  //同一行的失焦不进行操作
  allowBlur.value = true;
};
const canClickNextRow = (column?: any) => {
  if (!column) return false;
  if (["姓名", "编号", "部门", "手机号", "身份证号码", "个税免征额"].includes(column.label)) return false;
  return true;
};
const editFocus = (column?: any) => {
  if (editShow.value === "-1") return;
  let id = "";
  if (column?.label) {
    if (column?.label === "其他扣除") {
      id = "deductionOther";
    } else if (column?.label === "累计收入额") {
      id = "salaryYear";
    } else if (column?.label === "累计减除费用") {
      id = "taxBaseAmountYear";
    } else if (column?.label === "累计代扣额") {
      id = "ssPYear";
    } else if (column?.label === "子女教育") {
      id = "deductionChildYear";
    } else if (column?.label === "赡养老人") {
      id = "deductionParentYear";
    } else if (column?.label === "住房贷款利息") {
      id = "deductionHouseYear";
    } else if (column?.label === "住房租金") {
      id = "deductionRentYear";
    } else if (column?.label === "继续教育") {
      id = "deductionEducationYear";
    } else if (column?.label === "3岁以下婴幼儿照护") {
      id = "deductionBabyYear";
    } else if (column?.label === "累计其他扣除") {
      id = "deductionOtherYear";
    } else if (column?.label === "累计应缴个税") {
      id = "taxYear";
    } else if (column?.label === "已缴个税") {
      id = "taxPayedYear";
    } else if (column?.label === "实发工资(第一次)") {
      id = "netSalaryFirst";
    } else if (column?.label === "实发工资(第二次)") {
      id = "netSalarySecond";
    } else {
      if (column.rawColumnKey) {
        let customId = "";
        const rawColumnKey = column?.rawColumnKey + "";
        if (rawColumnKey?.includes("gross_pay_")) {
          const index = customColumns.value.salary[0].findIndex((item: any) => item.prop === column.rawColumnKey);
          customId = "grossPay" + index;
        } else if (rawColumnKey?.includes("deduct_wage_")) {
          const index = customColumns.value.salary[1].findIndex((item: any) => item.prop === column.rawColumnKey);
          customId = "deductWage" + index;
        } else if (rawColumnKey?.includes("after_tax_")) {
          const index = customColumns.value.salary[2].findIndex((item: any) => item.prop === column.rawColumnKey);
          customId = "afterTax" + index;
        } else if (rawColumnKey?.includes("insurance_p_")) {
          const index = customColumns.value.insurance[0].findIndex((item: any) => item.prop === column.rawColumnKey);
          customId = "insuranceP" + index;
        } else if (rawColumnKey?.includes("insurance_c_")) {
          const index = customColumns.value.insurance[1].findIndex((item: any) => item.prop === column.rawColumnKey);
          customId = "insuranceC" + index;
        } else {
          customId = "grossPay0";
        }
        nextTick().then(() => {
          const defaultClickItem = document.getElementById(customId);
          if (defaultClickItem) {
            defaultClickItem.focus();
          }
        });
        return;
      } else {
        id = "grossPay0";
      }
    }
  } else {
    id = "grossPay0";
  }
  nextTick().then(() => {
    const defaultClickItem = document.getElementById(id);
    if (defaultClickItem) {
      defaultClickItem.focus();
    }
  });
};

//保存编辑
const isEdit = ref(false);
// let submitRow: IItemDetailData;
let errorSUbmitRecord = ref(false);
const submitEdit = (row: IItemDetailData, column?: any, index?: number) => {
  // if(isEqual(submitRow, row)) return;
  if (errorSUbmitRecord.value) return;
  const jsDecimalMax = "79228162514264337593543950335";
  const jsDecimalMin = "-79228162514264337593543950335";
  const bigIntValue = BigInt((row.gross_pay_1 + "").replace(".", "")); // 移除小数点用于比较
  const maxBigInt = BigInt(jsDecimalMax.replace(".", ""));
  const minBigInt = BigInt(jsDecimalMin.replace(".", ""));
  // 比较BigInt值，因为它们可以表示非常大的整数
  if (bigIntValue > maxBigInt || bigIntValue < minBigInt) {
    ElNotify({ type: "warning", message: "保存失败，请稍后重试" });
    return;
  }
  // submitRow = cloneDeep(row);
  request({
    url: `/api/Salary?mid=${searchInfo.month}`,
    method: "put",
    data: row,
  })
    .then((res: IResponseModel<Number>) => {
      if (res.state === 1000) {
        if (res.data === 1) {
          ElNotify({ type: "success", message: "保存成功" });
          GetNewSalaryAfterUpdate(row);
          // getTableData(column, index);
        } else {
          errorSUbmitRecord.value = true;
          const index = tableData.value.findIndex((item) => (isErp.value ? item.e_code === row.e_code : item.e_id === row.e_id));
          // autoToEdit();
          tableData.value.splice(index, 1, beforeInputRow);
          ElNotify({ type: "warning", message: "保存失败，该员工当前月份不缴纳五险一金，请清空五险一金的数据后再次保存！" });
        }
      } else {
        errorSUbmitRecord.value = true;
        const index = tableData.value.findIndex((item) => (isErp.value ? item.e_code === row.e_code : item.e_id === row.e_id));
        tableData.value.splice(index, 1, beforeInputRow);
        ElNotify({ type: "warning", message: res.msg });
      }
    })
    .catch(() => {
      ElNotify({ type: "warning", message: "保存失败，请刷新页面重试" });
      getTableData(column, index);
    })
    .finally(() => {
      errorSUbmitRecord.value = false;
      // getTableData();
      isEdit.value = false;
      editShow.value = "-1";
    });
};
function GetNewSalaryAfterUpdate(rowData: IItemDetailData) {
    let rowIndex = tableData.value.findIndex((item) => item.e_sn === rowData.e_sn);
    let rowindexforsum: number;
    let islast = false;
    for (let ro in tableData.value) {
        if (tableData.value[ro]["e_sn"] == undefined || tableData.value[ro]["e_sn"] == "") {
            rowindexforsum = Number(ro);
            islast = true;
            break;
        }
    }
    request({
        url: `/api/Salary?pid=${searchInfo.month}&eid=${rowData.e_sn}`,
    }).then((res: any) => {
        if(res.data === null){
            ElNotify({ type: "warning", message: "保存失败，请刷新页面重试" });
            return;
        }
        let newdata = res.data;
        tableData.value[rowIndex] = newdata;

        if (islast == true) {
            const beforeRow = beforeData.value[rowIndex];
            for(const key in tableData.value[rowindexforsum]){
                if(typeof tableData.value[rowindexforsum][key] === "number" && key !== "e_sn"){
                    if(beforeInputRow){
                        const calcData = _.round(tableData.value[rowindexforsum][key] + newdata[key] - Number(beforeRow[key as keyof IItemDetailData]),2);
                        tableData.value[rowindexforsum][key] = calcData;
                    }
                }
            }
        }
        beforeData.value = JSON.parse(JSON.stringify(tableData.value));
    });
}

const handleFocus = (row: IItemDetailData, prop: string) => {
  allowBlur.value = true;
  isEdit.value = true;
  let currentRowData = tableData.value.find((item) => item.e_id === row.e_id);
  if (currentRowData[prop] == 0) {
    currentRowData[prop] = "";
  }
  //添加回车事件
  let inputEls = document.querySelectorAll(".table_input input");
  inputEls.forEach((input, index) => {
    let htmlInput = input as HTMLElement;
    if (!htmlInput.dataset.hasListener) {
      //防止重复添加监听事件
      input.addEventListener("keyup", function (event: any) {
        if (event.keyCode === 13) {
          // Enter 13
          event.preventDefault();
          if (index + 1 < inputEls.length) {
            (inputEls[index + 1] as HTMLInputElement).focus();
          } else if (index + 1 == inputEls.length) {
            (inputEls[0] as HTMLInputElement).focus();
          }
        }
      });
      htmlInput.dataset.hasListener = "true";
    }
  });
};
const handleScroll = () => {
  isEdit.value = true;
};
const calTaxArr = [
  "salary_year",
  "tax_base_amount_year",
  "ss_p_year",
  "deduction_child_year",
  "deduction_parent_year",
  "deduction_house_year",
  "deduction_rent_year",
  "deduction_education_year",
  "deduction_other_year",
  "deduction_baby_year",
];
//重新计算本月应缴个税
const calCurrentMonPerTax = (row: IItemDetailData, currentRowData: IItemDetailData) => {
  const tax_year = row.tax_year; //累计应缴
  const tax_payed_year = row.tax_payed_year; //累计已缴纳税额（不含当月）
  const tax_currentMonth = tax_year - tax_payed_year; //当月应缴
  currentRowData["tax"] = tax_currentMonth > 0 ? tax_currentMonth : 0;
  currentRowData["tax_show"] = tax_currentMonth > 0 ? tax_currentMonth : 0;
};

//计算年个人所得税
const calYearPerTax = (row: IItemDetailData) => {
  const {
    salary_year,
    tax_base_amount_year,
    ss_p_year,
    deduction_child_year,
    deduction_parent_year,
    deduction_house_year,
    deduction_rent_year,
    deduction_education_year,
    deduction_other_year,
    deduction_baby_year,
  } = row;

  const need_tax_salary =
    salary_year -
    tax_base_amount_year -
    ss_p_year -
    deduction_child_year -
    deduction_parent_year -
    deduction_house_year -
    deduction_rent_year -
    deduction_education_year -
    deduction_other_year -
    deduction_baby_year;
  let rate = 0.0;
  let deduction = 0;
  if (need_tax_salary >= 0 && need_tax_salary <= 36000) {
    rate = 0.03;
    deduction = 0;
  } else if (need_tax_salary <= 144000) {
    rate = 0.1;
    deduction = 2520;
  } else if (need_tax_salary <= 300000) {
    rate = 0.2;
    deduction = 16920;
  } else if (need_tax_salary <= 420000) {
    rate = 0.25;
    deduction = 31920;
  } else if (need_tax_salary <= 660000) {
    rate = 0.3;
    deduction = 52920;
  } else if (need_tax_salary <= 960000) {
    rate = 0.35;
    deduction = 85920;
  } else {
    rate = 0.45;
    deduction = 181920;
  }
  let tax_year = need_tax_salary * rate - deduction;
  if (tax_year < 0) {
    tax_year = 0;
  }
  return tax_year;
};

//重新计算应缴个税
const calNeedPerTax = (row: IItemDetailData, currentRowData: IItemDetailData) => {
  const tax_year = calYearPerTax(row);
  currentRowData["tax_year"] = tax_year;
  const tax_payed_year = row.tax_payed_year; //累计已缴纳税额（不含当月）
  const tax_currentMonth = tax_year - tax_payed_year; //当月应缴
  currentRowData["tax"] = tax_currentMonth > 0 ? tax_currentMonth : 0;
  currentRowData["tax_show"] = tax_currentMonth > 0 ? tax_currentMonth : 0;
};

type IItemDetailDataKey = keyof IItemDetailData;
//重新计算累计收入额:工资总额 - 应扣项；累计代扣个人款项;累计其他扣除
const calAccIncome = (row: IItemDetailData, currentRowData: IItemDetailData) => {
  let grosspay = 0;
  let deductwage = 0;
  let ssp = 0;
  let salary_before = parseFloat(row.salary_year_before.toString());
  let ssp_before = parseFloat(row.ss_p_year_before.toString());
  let have_before = row.have_before;
  let deduction_other = parseFloat(row.deduction_other.toString());
  let deduction_other_before = parseFloat(row.deduction_other_year_before.toString());
  //工资总额
  customColumns.value.salary[0].forEach((item: any) => {
    if (item.prop) {
      grosspay += parseFloat(row[item.prop as IItemDetailDataKey]!.toString());
    }
  });
  //应扣工资
  customColumns.value.salary[1].forEach((item: any) => {
    if (item.prop) {
      deductwage += parseFloat(row[item.prop as IItemDetailDataKey]!.toString());
    }
  });
  //代扣个人款项
  customColumns.value.insurance[0].forEach((item: any) => {
    if (item.prop) {
      ssp += parseFloat(row[item.prop as IItemDetailDataKey]!.toString());
    }
  });
  if (have_before) {
    let calSalary_year = salary_before + grosspay - deductwage;
    currentRowData["salary_year"] = calSalary_year > 0 ? calSalary_year : 0;
    let calSs_p_year = ssp_before + ssp;
    currentRowData["ss_p_year"] = calSs_p_year > 0 ? calSs_p_year : 0;
    let calDeduction_other_year = deduction_other + deduction_other_before;
    currentRowData["deduction_other_year"] = calDeduction_other_year > 0 ? calDeduction_other_year : 0;
  }
  const tax_year = calYearPerTax(row);
  currentRowData["tax_year"] = tax_year;
  const tax_payed_year = row.tax_payed_year; //累计已缴纳税额（不含当月）
  const tax_currentMonth = tax_year - tax_payed_year; //当月应缴
  currentRowData["tax"] = tax_currentMonth > 0 ? tax_currentMonth : 0;
  currentRowData["tax_show"] = tax_currentMonth > 0 ? tax_currentMonth : 0;
};
const handleBlur = (row: IItemDetailData, prop: IItemDetailDataKey) => {
  let currentRowData = tableData.value.find((item) => item.e_sn === row.e_sn);
  if (currentRowData[prop] == "" || currentRowData[prop] == "-") {
    currentRowData[prop] = "0";
  }
  //重新计算本月应缴个税
  if (prop === "tax_year" || prop === "tax_payed_year") {
    calCurrentMonPerTax(row, currentRowData);
  }
  //重新计算应缴个税
  if (calTaxArr.indexOf(prop) >= 0) {
    calNeedPerTax(row, currentRowData);
  }
  //重新计算累计收入额
  if (
    prop.indexOf("gross_pay_") >= 0 ||
    prop.indexOf("deduct_wage_") >= 0 ||
    prop.indexOf("insurance_p_") >= 0 ||
    prop == "deduction_other"
  ) {
    calAccIncome(row, currentRowData);
  }
  // 正在编辑不可再次提交
  isEdit.value = false;
  setTimeout(() => {
    if (isEdit.value) return;
    if (!allowBlur.value) return;
    if (row.index + "" !== editShow.value) return;
    // needWaiting = false;
    submitEdit(row);
    editShow.value = "-1";
  }, 200);
};
let beforeInputRow: IItemDetailData;
const handleInput = (value: any, row: IItemDetailData, prop: string) => {
  const changeData = tableData.value.find((item) => (isErp.value ? item.e_code === row.e_code : item.e_id === row.e_id));
  const dotIndex = value.indexOf(".");
  if (dotIndex !== -1) {
    value = value.slice(0, dotIndex + 1) + value.slice(dotIndex).replace(/\./g, "");
    const decimalPart = value.slice(dotIndex + 1);
    if (decimalPart.length > 2) {
      value = value.slice(0, dotIndex + 3);
    }
  }
  if (prop === "net_salary_first" || prop === "net_salary_second") {
    //实发工资第一次/第二次输入
    let inputVal = value.replace(/[^-0-9.]/g, "");
    changeData[prop] = inputVal;
    let resNumber = isNaN(Number(changeData[prop])) ? 0 : changeData[prop];
    if (prop === "net_salary_first") {
      changeData["net_salary_second"] = (changeData["net_salary"] - resNumber).toFixed(2);
    } else if (prop === "net_salary_second") {
      changeData["net_salary_first"] = (changeData["net_salary"] - resNumber).toFixed(2);
    }
  } else {
    //其他
    changeData[prop] = value.replace(/[^-0-9.]/g, "");
  }
};
//回车键
const inputRef = ref();
const firstinputRef = ref();
let voucherIsInit = false;
const edited = ref(false);
const voucherChanged = () => {
  if (!voucherIsInit) return;
  edited.value = true;
};
const route = useRoute();
let firstInit = true;
onBeforeRouteLeave((to, from, next) => {
  firstInit = false;
  cacheRouterQueryParams = from.query;
  next();
});
let cacheRouterQueryParams: any = null;
const isFromOtherPage = (page: "tax" | "voucherList" | "ERecord"): boolean => {
  if (!cacheRouterQueryParams?.from && route.query.from === page) {
    return true;
  }
  if (
    cacheRouterQueryParams?.from === page &&
    route.query?.from === page &&
    cacheRouterQueryParams.r &&
    cacheRouterQueryParams.r !== route.query.r
  ) {
    return true;
  }

  return false;
};
const reLoadCurrentPage = () => {
  const currentRouterModel = routerArray.value.find((item) => item.alive);
  if (currentRouterModel) {
    if (currentRouterModel.stop) return;
    routerArrayStore.refreshRouter(currentRouterModel!.path);
    currentRouterModel.stop = true;
  }
};
const currentPath = ref(route.path);
const isEditting = computed(() => {
  return (
    currentSlot.value === "main" &&
    activeName.value === "second" &&
    partCurrentSlot.value === "partVoucher" &&
    (edited.value || voucherStatus.value === "create")
  );
});
watch(isEditting, (newVal) => {
  routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
onActivated(() => {
  if ((isFromOtherPage("tax") || isFromOtherPage("voucherList") || isFromOtherPage("ERecord")) && !firstInit) {
    if (isEditting.value) {
      editConfirm("voucherEdit", () => {}, reLoadCurrentPage, "工资管理");
    } else {
      reLoadCurrentPage();
    }
  }
});
const resultSalaryMultiple = ref(false);
//表头公式气泡
const visible = ref(false);
//多次发放工资
//检测当月工资是否勾选了多次发放按钮
async function isHasMultiSalary() {
  await request({
    url: `/api/Salary/SalaryIsMultiplePay?mid=${searchInfo.month}`,
    method: "get",
  }).then((res: IResponseModel<boolean>) => {
    if (res.state === 1000) {
      searchInfo.salaryMultiple = res.data;
      resultSalaryMultiple.value = searchInfo.salaryMultiple;
    }
  });
}
const changeMultiStatus = async (event: any) => {
  await saveMultipleStatus(event);
};
//当月工资能否勾选状态
async function saveMultipleStatus(flag: boolean) {
  request({
    url: `/api/Salary/SaveMultiplePayFlag?mid=${searchInfo.month}&flag=${flag ? 1 : 0}`,
    method: "post",
  })
    .then((res: IResponseModel<boolean>) => {
      if (res.state === 1000) {
        if (res.data) {
          getTableData();
          window.dispatchEvent(new CustomEvent("changeSalaryMultiStatus"));
          if (searchInfo.salaryMultiple) {
            //勾选成功
            ElAlert({
              message: "多次发放工资需根据实发工资项目拆分发放金额，可前往工资表导入实发工资拆分金额数据哦~",
              options: {
                confirmButtonText: "去导入",
                cancelButtonText: "取消",
              },
              dialogWidth: "450",
            }).then((r) => {
              if (r) {
                importActualShow.value = true;
              }
            });
          }
        } else {
          //保存状态返回false说明不能勾选/取消勾选多次发放工资
          searchInfo.salaryMultiple = !flag;
          ElNotify({
            type: "warning",
            message: res.msg,
          });
        }
      } else {
        ElNotify({
          type: "warning",
          message: res.msg,
        });
      }
    })
    .finally(() => {
      resultSalaryMultiple.value = searchInfo.salaryMultiple;
    });
}
const importActualShow = ref(false);
const ImportPageActual = () => {
  importActualShow.value = true;
};
const downloadSalaryActualTable = async () => {
  if (await IsHaveSalaryData()) {
    globalExport(`/api/Salary/ExportNetSalaryTemplate?mid=` + searchInfo.month);
  } else {
    ElNotify({
      type: "warning",
      message: "当前月份无员工工资,请创建工资后再进行操作！",
    });
  }
};

const showDepartmentList = ref<Array<ISelectItem>>([]);
watchEffect(() => {
  showDepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
});
function departFilterMethod(value: string) {
  showDepartmentList.value = commonFilterMethod(value, departmentList.value, "label");
}
</script>

<style scoped lang="less">
@import "@/style/Salary/salaryManage.less";
</style>
<style lang="less">
.other-inport-dialog {
  .import-dialog .import-content {
    margin-top: 10px;
    .file-button {
      padding-right: 10px;
    }
  }
}
.new-popover-content {
  min-width: 100px;
  .new-popover-content-inner {
    min-width: 100px;
    width: fit-content;
    padding-right: 10px;
  }
}
</style>
