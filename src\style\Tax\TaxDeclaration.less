@import "../Functions.less";
.content {
    .main-content {
        .main-center {
            padding: 30px 30px 0 50px;
            height: 560px;
            width: 1000px;
            box-sizing: border-box;
            margin: 0 auto;
            text-align: left;
            .base-type {
                width: 208px;
                height: 96px;
                border-radius: 4px;
                display: inline-block;
                margin-right: 20px;
                margin-bottom: 20px;
                border: 1px solid #d0d0d0;
                background-position-x: -1px;
                background-position-y: -1px;
                background-repeat: no-repeat;
                color: var(--font-color);
                font-size: var(--h2);
                line-height: 96px;
                text-align: center;
                &.closed {
                    cursor: default;
                    background-image: url("@/assets/Tax/closed.png");
                    background-color: var(--background-color);
                    color: var(--weaker-font-color);
                }
                &.opened {
                    cursor: pointer;
                    background-image: url("@/assets/Tax/opened.png");
                    &:hover {
                        box-shadow: rgba(0, 0, 0, 0.1) 0 0 15px;
                    }
                }
            }
            .hot{
                width:50px;
                height:25px;
                position: absolute;
                top: 14px;
            }
            .main-bottom-tip {
                width: 900px;
                line-height: 26px;
                font-size: 12px;
                color: #333333;
                display: inline-block;
                margin-top: 200px;
                margin-left: 10px;
                text-align: left;
                span.red {
                    color: #fd5055;
                }
            }
        }
    }
    .slot-content {
        .slot-mini-content {
            width: 1000px;
        }
    }
    .statementTax-selector {
        position: relative;
        .statementTax-selector-buttons {
            margin: 40px 0;
            .button,
            .solid-button {
                width: 76px;
            }
        }
        .statementTax-selector-tips {
            color: var(--weaker-font-color);
            font-size: var(--h4);
            line-height: var(--line-height);
            .consult {
                position: relative;
                color: #1890ff;
                cursor: pointer;
                img {
                    position: absolute;
                    bottom: 20px;
                    left: -10px;
                    display: none;
                    width: 182px;
                    height: 205px;
                }
                &:hover {
                    img {
                        display: block;
                    }
                }
            }
        }
        .statementTax-selector-turn-to {
            margin-top: 10px;
            padding-bottom: 60px;
        }
        .statementTax-selector-title {
            padding-top: 40px;
            color: var(--font-color);
            font-size: var(--h4);
            line-height: 25px;
            margin-bottom: 20px;
        }
        .statementTax-selector-left {
            display: inline-block;
            text-align: right;
            .statementTax-selector-item {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 32px;
                & + .statementTax-selector-item {
                    margin-top: 16px;
                }
            }
        }
        .statementTax-selector-right {
            display: inline-block;
            text-align: left;
            vertical-align: top;
            width: 226px;
            overflow: visible;
            .statementTax-selector-item {
                height: 32px;
                .detail-el-select(198px, 30px);
                & + .statementTax-selector-item {
                    margin-top: 16px;
                }
                &.statementTax-statementTaxDeclareWay {
                    height: auto;
                    :deep(.el-radio-group) {
                        .el-radio {
                            align-items: flex-start;
                            margin-top: 16px;
                            height: auto;
                            min-height: 20px;
                            max-width: 160px;
                            &:first-child {
                                margin-top: 6px;
                            }
                            .el-radio__input {
                                margin-top: 3px;
                            }
                        }
                    }
                }
                &.tax-type {
                    display: flex;
                    align-items: center;
                    img {
                        width: 16px;
                        height: 16px;
                        margin-left: 5px;
                        cursor: pointer;
                    }
                }
            }
        }
    }
    &.erp-content {
        .main-content {
            overflow-y: auto;
            .main-center {
                padding: 30px 30px 0 50px;
                overflow: visible;
            }
        }
    }
}
.dialog-content {
    .dialog-content-body {
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        .set-font;
    }
    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
}
