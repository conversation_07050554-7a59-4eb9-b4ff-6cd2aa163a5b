@import "../Functions.less";
.main-content {
    text-align: left;
    .main-top {
        width: 1100px;
        margin: 0 auto;
        height: 28px;
        padding: 10px;
    }
    .split-line {
        border-bottom: 1px solid var(--title-split-line);
        margin-bottom: 10px;
    }
    .voucher-head {
        width: 1100px !important;
        width: calc(100% - 38px);
        margin: 0 auto;
        background-color: var(--table-title-color);
        height: 37px;
        .voucher-title {
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 37px;
            font-weight: bold;
            float: left;
            padding-left: 20px;
        }
    }
    .pagination {
        width: 1100px;
        margin: 0 auto;
        padding: 6px 0;
    }
    .voucher-list {
        width: 1100px;
        margin: 0 auto;
        flex: 1;
        min-height: 0;
        .none-data-text {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #909399;
            font-size: 14px;
        }
        .voucher-list-item {
            margin: 10px auto 0;
            border: 1px solid var(--table-border-color);
            margin-left: auto;
            margin-right: auto;
            &:first-child {
                margin-top: 0;
            }
            &:hover {
                border-color: var(--main-color);
                > .voucher-list-head {
                    a.hover-display {
                        display: inline-block;
                    }
                }
            }
            .voucher-list-head {
                height: 39px;
                position: relative;
                padding: 0 20px;
                .voucher-date,
                .voucher-num {
                    display: inline-block;
                    color: #333;
                    font-weight: bold;
                    font-size: var(--h5);
                    line-height: 17px;
                    margin-top: 11px;
                }
                a.hover-display {
                    margin-top: 11px;
                    line-height: 17px;
                    font-size: var(--h5);
                    display: none;
                }
            }
            .voucher-list-lines {
                .voucher-list-line {
                    display: flex;
                    align-items: center;
                    color: var(--font-color);
                    font-size: var(--h5);
                    line-height: 17px;
                    padding: 8px 20px 7px 0;
                    cursor: pointer;
                    border-top: 1px solid #f0f0f0;
                    &:hover {
                        background-color: var(--table-hover-color);
                    }
                    &:first-child {
                        border-top-color: var(--table-border-color);
                    }
                    & > div {
                        padding-left: 20px;
                        display: inline-block;
                        word-break: break-all;
                        vertical-align: top;
                    }
                }
            }
        }
        :deep(.el-loading-spinner) {
            top: 100px;
        }
    }
}
.divImport {
    text-align: left;
    font-size: var(--font-size);
    .import-download,
    .import-import {
        margin-top: 30px;
        margin-left: 40px;
    }
    .import-file {
        margin-top: 10px;
        margin-bottom: 30px;
        margin-left: 40px;
        display: flex;
        .upload-file-name {
            display: inline-block;
            width: 240px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
    }
    .dialog-buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        border-top: 1px solid var(--border-color);
        text-align: center;
    }
}
.voucher-handle-box {
    width: 1160px;
    text-align: left;
    background-color: var(--white);
    margin: 0 auto;
    font-size: var(--font-size);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // height: calc(var(--voucher-min-height) + 53px);
    // height: ~"max(calc(var(--voucher-min-height) + 53px), calc(100vh - var(--content-padding-bottom) - var(--title-height)))";

    .edit-content-topbox {
        margin: 20px 0 3px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: center;
        padding: 0px 20px;
        position: relative;
        z-index: 9;
        flex-shrink: 0;
        .edit-top {
            height: 30px;
            .detail-el-select(150px, 30px);
            :deep(.default-show) {
                display: none !important;
            }
            > input.float-l {
                .detail-original-input(150px, 30px);
            }
            .txt {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
            }
        }
        .edit-tool-bar {
            height: 28px;
        }
    }
    .edit-content-bottom {
        // height: 0;
        // flex: 1;
        :deep(.voucher-container) {
            padding-top: 0 !important;
        }
    }
    &.zoom-in {
        .edit-content-topbox {
            width: 1050px;
        }
    }
    &.zoom-out {
        .edit-content-topbox {
            align-self: stretch;
            padding: 10px 54px;
        }
    }
}

body[erp] {
    .content .main-content {
        :deep(.voucher-list) {
            margin-top: 15px;
            .voucher-list-item {
                border-color: var(--table-border-color);

                .voucher-list-head {
                    background-color: var(--table-title-color) !important;
                    .voucher-date,
                    .voucher-num {
                        color: #7f7f7f;
                        font-weight: 500;
                    }
                }
                .voucher-list-lines {
                    .voucher-list-line {
                        &:first-child {
                            border-top-color: var(--table-border-color);
                        }
                    }
                }
            }
        }
    }
}
