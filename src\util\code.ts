export const getNextCode = (str: string, maxLength: number) => {
    const next = str.replace(/^[^\d]*(\d+)[^\d]*$/, "$1"); //获取字符串第一个数字所在位置
    const s = str.indexOf(next);
    const e = s + next.length;
    if (isNaN(Number(str.substring(s, e)))) {
        const result = processNaNNum(str);
        if (result.length > maxLength) {
            return "";
        } else {
            return result;
        }
    } else {
        let end = "";
        if (e < str.length) {
            end = str.substring(e);
        }
        const num = addOne(next);

        let pre = "";
        if (num.toString().length < next.length) {
            for (let i = 0; i < next.length - num.toString().length; i++) {
                pre += "0";
            }
        }
        const result = str.substring(0, s) + pre + num.toString() + end;
        if (result.length > maxLength) {
            return "";
        } else {
            return result;
        }
    }
};

const processNaNNum = (input: string) => {
    const regex = /(.*?)(\d+)$/;
    const match = input.match(regex);

    if (match) {
        const matchedString = match[1];
        const numberPart = match[2];
        const incrementedNumber = addOne(numberPart);
        const result = matchedString + incrementedNumber;
        return result;
    } else {
        return input + 1;
    }
};

const addOne = (numStr: string) => {
    return (Number(numStr) + 1).toString();
};
