import type { IResponseModel } from "@/util/service";
import type { VoucherTemplateSaveModel } from "@/api/voucherTemplate";

export class VoucherModel {
    constructor() {
        this.voucherLines = new Array<VoucherEntryModel>();
        this.voucherLines.push(new VoucherEntryModel());
        this.voucherLines.push(new VoucherEntryModel());
        this.voucherLines.push(new VoucherEntryModel());
        this.voucherLines.push(new VoucherEntryModel());
    }
    //期间Id
    pid: number = 0;
    //凭证Id，期间内唯一
    vid: number = 0;
    //凭证字Id
    vgId: number = 0;
    //凭证字
    vgName: string = "";
    //凭证号
    vnum: number = 0;
    //凭证日期
    vdate: string = "";
    //凭证类别
    vtype: number = 0;
    //凭证附件数量
    attachments: number | undefined = undefined;
    //制单人
    preparedBy: string = "";
    //制单日期
    preparedDate: string = "";
    //审核人
    approvedBy: string = "";
    //审核日期
    approvedDate: string = "";
    //审核状态，0：未审核，1：已审核
    approveStatus: number = 0;
    //备注
    note: string = "";
    //期间状态，1：当前期间，2：已结转损益，3：已结账
    pstatus: number = 0;
    //是否是资产凭证
    isFaVoucher: boolean = false;
    //凭证行
    voucherLines: Array<VoucherEntryModel>;
    //凭证附件Id，逗号隔开
    attachFileIds: string = "";
    //凭证附件
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
    height?: number;
    //红冲记录
    rf: Array<IRf> = [];
}
export interface IRf {
    pid: string;
    vid: string;
    vdate: string;
    vgid: string;
    vnum: string;
}
export class VoucherEntryModel {
    //凭证行Id
    veId: number = 0;
    //科目Id
    asubId: number = 0;
    //科目类别
    asubType: number = 0;
    //科目编码
    asubCode: string = "";
    //科目全称，AsubCode AsubName_AANumAAName
    asubName: string = "";
    //科目全称，不添加辅助核算项，AsubCode AsubName
    asubAAName: string = "";
    //借方金额
    debit: number = 0;
    //贷方金额
    credit: number = 0;
    //凭证摘要
    description: string = "";
    //外币Id
    fcId: number = 1;
    //外币编码
    fcCode: string = "";
    //外币数量
    fcAmount: number = 0;
    //汇率
    fcRate: number = 0;
    //是否开启外币核算
    foreigncurrency: number = 0;
    //单位
    measureUnit: string = "";
    //数量
    quantity: number = 0;
    //单价
    price: number = 0;
    //是否开启数量核算
    quantityAccounting: number = 0;
    //辅助核算Id，逗号分隔
    aacode: string = "";
    //辅助核算类型，逗号分隔
    aatype: string = "";
    //是否开启辅助核算
    assistingAccounting: number = 0;
    //是否是资产行
    isFaLine: boolean = false;
    direction?: number = 0;
    //是否在员工信息计提科目中使用
    isjtsub?:number = 0;
}

export class VoucherAttachFileModel {
    //文件Id
    fileId: number = 0;
    //文件名称
    fileName: string = "";
    //文件大小
    fileSize: string | number = "";
    //相对路径
    relativePath: string = "";
    //文件类别
    fileType: FileType = FileType.Other;
}

enum FileType {
    //Word
    Word = 1010,
    //Excel
    Excel = 1020,
    //PPT
    PPT = 1030,
    //Access
    Access = 1040,
    //NotePad
    NotePad = 2010,
    //PDF
    PDF = 2020,
    //Picture
    Picture = 3010,
    //OFD
    OFD = 4010,
    //Other
    Other = 9010,
}

export interface IVoucherDescriptionModel {
    description: string;
    acronym: string;
    pinyin: string;
}

export class VoucherSwitchInfoModel {
    hasPrev: boolean = false;
    hasNext: boolean = false;
    pid: number = 0;
    vid: number = 0;
}

export class VoucherSettingsModel {
    autoSupplementVNumStatus: number = 0;
    autoCalcVoucherStatus: number = 0;
    allowFcZeroStatus: number = 0;
    defaultAISuggestionStatus: number = 0;
    amountDirectionByAsubStatus: number = 0;
}

export interface IVoucherDraftModel {
    //期间Id
    pid: number;
    //凭证Id，期间内唯一
    vid: number;
    //凭证字Id
    vgId: number;
    //凭证字
    vgName: string;
    //凭证号
    vnum: number;
    //凭证日期
    vdate: string;
    //凭证附件数量
    attachments: number;
    //备注
    note: string;
    //凭证行
    voucherEntryModel: VoucherEntryModel;
}

export class VoucherTemplateModel {
    //模板Id
    vtId: number = 0;
    //模板类型
    vtType: number = 0;
    //模板名称
    vtName: string = "";
    //凭证字
    vgId: number = 0;
    //凭证
    voucherLines: Array<VoucherEntryModel> = new Array<VoucherEntryModel>();
}

export class VoucherQueryParams {}

export class NewVoucherQueryParams implements VoucherQueryParams {
    vgId: number = 0;
    //此参数不会生效，只在加载出新凭证号前展示使用
    vnum: number = 0;
    attachFileIds: string = "";
    lastVoucherDate: string = "";
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
}

export class InsertVoucherQueryParams implements VoucherQueryParams {
    constructor(pId: number, vId: number) {
        this.pId = pId;
        this.vId = vId;
    }
    pId: number;
    vId: number;
}

export class CopyVoucherQueryParams implements VoucherQueryParams {
    constructor(pId: number, vId: number) {
        this.pId = pId;
        this.vId = vId;
    }
    pId: number;
    vId: number;
}

export class EditVoucherQueryParams implements VoucherQueryParams {
    constructor(pId: number, vId: number) {
        this.pId = pId;
        this.vId = vId;
    }
    pId: number;
    vId: number;
}

export class RecyclebinVoucherQueryParams implements VoucherQueryParams {
    constructor(rId: number) {
        this.rId = rId;
    }
    rId: number;
}

export class OffsetVoucherQueryParams implements VoucherQueryParams {
    constructor(pId: number, vId: number) {
        this.pId = pId;
        this.vId = vId;
    }
    pId: number;
    vId: number;
    }

export class LoadVoucherTemplateQueryParams implements VoucherQueryParams {
    constructor(vtId: number, originEditType?: number) {
        this.vtId = vtId;
        this.originEditType = originEditType || 1;
    }
    vtId: number;
    attachFileIds: string = "";
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
    originEditType: number = 1;
}

export class LoadVoucherDraftQueryParams implements VoucherQueryParams {
    constructor(pId: number, vId: number, originEditType?: number) {
        this.pId = pId;
        this.vId = vId;
        this.originEditType = originEditType || 1;
    }
    pId: number;
    vId: number;
    originEditType: number = 1;
}

export class LoadVoucherRecyclebintQueryParams implements VoucherQueryParams {
    constructor(rId: number) {
        this.rId = rId;
    }
    rId: number;
}

export class NewVoucherTemplateQueryParams implements VoucherQueryParams {}

export class EditVoucherTemplateQueryParams implements VoucherQueryParams {
    constructor(vtId: number) {
        this.vtId = vtId;
    }
    vtId: number;
}

export class DataVoucherQueryParams implements VoucherQueryParams {
    constructor(voucherLines: Array<VoucherEntryModel>) {
        this.voucherLines = voucherLines;
    }
    vgId: number = 0;
    vdate: string = "";
    vnum: number = 0;
    vtype: number = 0;
    note: string = "";
    attachments: number | undefined = undefined;
    attachFileIds: string = "";
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
    voucherLines: Array<VoucherEntryModel> = new Array<VoucherEntryModel>();
    tempAsubList: Array<{ asubCode: string; asubName: string }> = [];
    tempAAEList: Array<{ aatype: number; num: string; name: string }> = [];
    isCombine: boolean = false;
    pid: number = 0;
    vids: Array<number> = [];
}

export class VoucherSaveModel {
    constructor(voucherModel: VoucherModel) {
        this.pid = voucherModel.pid;
        this.vid = voucherModel.vid;
        this.vgId = voucherModel.vgId;
        this.vnum = voucherModel.vnum;
        this.attachments = voucherModel.attachments || 0;
        this.note = voucherModel.note;
        this.vtype = voucherModel.vtype;
        this.vdate = voucherModel.vdate;
        this.fileIds = voucherModel.attachFileIds;
        this.autoVNum = voucherModel.vid === 0;
        this.voucherLines = new Array<VoucherLineSaveModel>();
    }
    //凭证所属期间，新增/插入/复制凭证时不使用
    pid: number;
    //凭证Id，期间内唯一，新增/插入/复制凭证时不使用
    vid: number;
    //凭证字Id
    vgId: number;
    //凭证号
    vnum: number;
    //凭证附件数量
    attachments: number;
    //备注
    note: string;
    //凭证类别
    vtype: number;
    //凭证日期
    vdate: string;
    //凭证附件文件Id，逗号隔开
    fileIds: string;
    //是否自动生成可用的凭证号
    autoVNum: boolean;
    //凭证行
    voucherLines: Array<VoucherLineSaveModel>;
}

export class VoucherLineSaveModel {
    constructor(voucherline: VoucherEntryModel) {
        this.description = voucherline.description;
        this.asubId = voucherline.asubId;
        this.debit = voucherline.debit;
        this.credit = voucherline.credit;
        this.quantityAccounting = voucherline.quantityAccounting;
        this.quantity = voucherline.quantity;
        this.price = voucherline.price;
        this.foreigncurrency = voucherline.foreigncurrency;
        this.fcId = voucherline.fcId;
        this.fcRate = voucherline.fcRate;
        this.fcAmount = voucherline.fcAmount;
        this.assistingAccounting = voucherline.assistingAccounting;
        this.aacode = voucherline.aacode;
        if(voucherline.direction !== undefined){
            this.direction = voucherline.direction;
        }
    }
    //摘要
    description: string;
    //科目Id
    asubId: number;
    //借方金额
    debit: number;
    //贷方金额
    credit: number;
    //是否开启数量核算
    quantityAccounting: number;
    //数量
    quantity: number;
    //单价
    price: number;
    //是否开启外币核算
    foreigncurrency: number;
    //外币Id
    fcId: number;
    //汇率
    fcRate: number;
    //原币
    fcAmount: number;
    //是否开启辅助核算
    assistingAccounting: number;
    //辅助核算Id，逗号分隔
    aacode: string;
    //方向
    direction?: number;
}

export class BaseVoucherSaveParams {}

export class VoucherSaveParams implements BaseVoucherSaveParams {
    constructor(createdWay: number, callback: (res: IResponseModel<VoucherSaveModel>, savingVoucherLines: Array<VoucherEntryModel>) => void) {
        this.createdWay = createdWay;
        this.callback = callback;
    }
    createdWay: number;
    dotSave: boolean = false;
    callback: (res: IResponseModel<VoucherSaveModel>, savingVoucherLines: Array<VoucherEntryModel>) => void;
    autoVNum: boolean = true;
}

export class VoucherTemplateSaveParams implements BaseVoucherSaveParams {
    constructor(vtType: number, vtName: string,callback: (res: IResponseModel<VoucherTemplateSaveModel>) => void,saveAmount?:boolean) {
        this.vtType = vtType;
        this.vtName = vtName;
        if(typeof saveAmount === 'boolean'){
            this.saveAmount = saveAmount;
        }
        this.callback = callback;
    }
    vtType: number = 0;
    vtCode: string = "";
    vtName: string = "";
    vtDefault: boolean = false;
    vgId: number = 0;
    saveAmount:boolean = true;
    callback: (res: IResponseModel<VoucherTemplateSaveModel>) => void;
}

export class VoucherDraftSaveParams implements BaseVoucherSaveParams {
    constructor(callback: (res: IResponseModel<VoucherSaveModel>) => void) {
        this.callback = callback;
    }
    callback: (res: IResponseModel<VoucherSaveModel>) => void;
}

export class DraftSaveParams implements BaseVoucherSaveParams {
    constructor(callback: (voucherModel: VoucherModel) => void) {
        this.callback = callback;
    }
    callback: (voucherModel: VoucherModel) => void;
}

export interface IVoucherSaveResultModel {
    pid: number;
    vid: number;
    vnum: number;
}

export interface CurrencyModel {
    //外币ID编码
    id: number;
    //外币代码
    code: string;
    //外币名称
    name: string;
    //初始汇率
    rate: number;
    //是否是本位币
    isBaseCurrency: boolean;
}

export interface VoucherGroupModel {
    id: number;
    name: string;
    title: string;
    isDefault: boolean;
}

export enum DisplayStatus {
    // 隐藏
    Hidden = 0,
    // 显示
    Visible = 1,
    // 加载中
    Loading = 2,
}