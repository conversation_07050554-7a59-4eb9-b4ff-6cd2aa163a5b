<template>
  <div>
    <LMTable
      :data="tableData"
      :columns="columns"
      @cell-click="handleCellClick"
      empty-text="暂无数据"
      :loading="loading"
      :rowKey="rowKey">
      <template #operation>
        <el-table-column>
          <template #header>
            <div class="align-center">
              <span>功能</span>
              <el-icon
                size="16"
                class="ml-10">
                <Setting />
              </el-icon>
            </div>
          </template>
          <template #default="{ row }">
            <span
              v-if="row.invoiceTypeText !== '合计'"
              class="link"
              @click="handleOperation(row)">
              操作
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </template>
    </LMTable>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, withDefaults, computed } from "vue"

  // 定义组件属性
  interface InvoiceTableProps {
    data?: any[]
    columns?: any[]
    rowKey?: string
    height?: number
    loading?: boolean
  }

  // 定义事件
  const emit = defineEmits<{
    (e: "operation", row: any): void
    (e: "cellClick", row: any, column: any): void
  }>()

  const props = withDefaults(defineProps<InvoiceTableProps>(), {
    data: () => [],
    columns: () => [],
    rowKey: "invoiceType",
    loading: false,
  })

  // 使用计算属性处理传入的数据
  const tableData = computed(() => props.data || [])

  // 处理操作按钮点击
  const handleOperation = (row: any) => {
    emit("operation", row)
  }

  const handleCellClick = (row: any, column: any) => {
    emit("cellClick", row, column)
  }
</script>

<style scoped lang="scss">
  :deep(.custom-table) {
    tbody tr td {
      .cell {
        color: inherit;
      }
    }
  }
</style>
