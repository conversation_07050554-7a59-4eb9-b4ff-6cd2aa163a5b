import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoneyWithZero } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const setColumns =(activeName:string,columnsLeft:Array<IColumnProps>|undefined,columnsRight:Array<IColumnProps>|undefined) => {
    const setModule = "FASummary";
    if (activeName == "first") {
        columnsLeft = [
            { 
                label: "部门名称", 
                prop: "department_name", 
                align: "left", 
                headerAlign: "left", 
                minWidth: 138,
                width: getColumnWidth(setModule, 'department_name'),
            },
            {
                label: "资产原值",
                headerAlign: "center",
                children: [
                    {
                        label: "期初数",
                        prop: "value_s",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_s'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期增加",
                        prop: "value_add",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_add'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期减少",
                        prop: "value_reduce",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_reduce'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "期末数",
                        prop: "value_e",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_e'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                ],
            },
            {
                label: "累计折旧",
                headerAlign: "center",
                children: [
                    {
                        label: "期初数",
                        prop: "depreciation_s",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_s'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期增加",
                        prop: "depreciation_add",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_add'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期减少",
                        prop: "depreciation_reduce",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_reduce'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "期末数",
                        prop: "depreciation_e",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_e'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                ],
            },
            {
                label: "减值准备",
                headerAlign: "center",
                children: [
                    {
                        label: "期初数",
                        prop: "provision_s",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'provision_s'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期增加",
                        prop: "provision_add",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'provision_add'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期减少",
                        prop: "provision_reduce",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'provision_reduce'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "期末数",
                        prop: "provision_e",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        resizable: false,
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                ],
            },
        ];
        return columnsLeft;
    } else {
        columnsRight = [
            { 
                label: "资产类别", 
                prop: "fa_type_name", 
                align: "left", 
                headerAlign: "left",
                minWidth: 138, 
                width: getColumnWidth(setModule, 'fa_type_name'),
            },
            {
                label: "资产原值",
                headerAlign: "center",
                children: [
                    {
                        label: "期初数",
                        prop: "value_s",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_s'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期增加",
                        prop: "value_add",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_add'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期减少",
                        prop: "value_reduce",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_reduce'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "期末数",
                        prop: "value_e",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'value_e'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                ],
            },
            {
                label: "累计折旧",
                headerAlign: "center",
                children: [
                    {
                        label: "期初数",
                        prop: "depreciation_s",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_s'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期增加",
                        prop: "depreciation_add",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_add'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期减少",
                        prop: "depreciation_reduce",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_reduce'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "期末数",
                        prop: "depreciation_e",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'depreciation_e'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                ],
            },
            {
                label: "减值准备",
                headerAlign: "center",
                children: [
                    {
                        label: "期初数",
                        prop: "provision_s",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'provision_s'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期增加",
                        prop: "provision_add",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'provision_add'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "本期减少",
                        prop: "provision_reduce",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        width: getColumnWidth(setModule, 'provision_reduce'),
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                    {
                        label: "期末数",
                        prop: "provision_e",
                        align: "right",
                        headerAlign: "right",
                        minWidth: 114,
                        resizable: false,
                        formatter: (row, column, value) => {
                            return formatMoneyWithZero(value);
                        },
                    },
                ],
            },
        ];
        return columnsRight;
    }
}