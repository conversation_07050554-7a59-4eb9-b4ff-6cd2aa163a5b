import { request, type IResponseModel } from "@/util/service";
export function getAsubCodeLengthApi() {
    return request({
        url: "/api/AccountSubject/GetAsubCodeLength",
        method: "post",
    }) as any as Promise<IResponseModel<IAsubCodeLengthRes>>;
}
export interface IAsubCodeLengthRes {
    preName: string;
    asid: number;
    codeLength: number[];
    firstCodeLength: number;
    secondCodeLength: number;
    thirdCodeLength: number;
    forthCodeLength: number;
    firstAsubLength: number;
    secondAsubLength: number;
    thirdAsubLength: number;
    forthAsubLength: number;
}